﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.InteropServices.WindowsRuntime</name>
  </assembly>
  <members>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.DefaultInterfaceAttribute">
      <summary>관리되는 Windows 런타임 클래스의 기본 인터페이스를 지정합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.DefaultInterfaceAttribute.#ctor(System.Type)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.WindowsRuntime.DefaultInterfaceAttribute" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="defaultInterface">특성이 적용되는 클래스에 대한 기본 인터페이스로 지정되는 인터페이스 형식입니다. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.DefaultInterfaceAttribute.DefaultInterface">
      <summary>기본 인터페이스의 형식을 가져옵니다. </summary>
      <returns>기본 인터페이스의 형식입니다. </returns>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken">
      <summary>이벤트 처리기가 Windows 런타임 이벤트에 추가되면 반환되는 토큰입니다.이 토큰은 나중에 이벤트에서 이벤트 처리기를 제거하는 데 사용됩니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken.Equals(System.Object)">
      <summary>현재 개체가 지정된 개체와 같은지 여부를 나타내는 값을 반환합니다. </summary>
      <returns>현재 개체가 <paramref name="obj" />와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">비교할 개체입니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken.GetHashCode">
      <summary>이 인스턴스의 해시 코드를 반환합니다. </summary>
      <returns>이 인스턴스의 해시 코드입니다. </returns>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken.op_Equality(System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken,System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken)">
      <summary>두 개의 <see cref="T:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken" /> 인스턴스가 같은지 여부를 나타냅니다. </summary>
      <returns>두 개체가 같으면 true이고, 그렇지 않으면 false입니다. </returns>
      <param name="left">비교할 첫 번째 인스턴스입니다. </param>
      <param name="right">비교할 두 번째 인스턴스입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken.op_Inequality(System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken,System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken)">
      <summary>두 개의 <see cref="T:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken" /> 인스턴스가 같지 않은지 여부를 나타냅니다.</summary>
      <returns>두 인스턴스가 서로 다르면 true이고, 그렇지 않으면 false입니다. </returns>
      <param name="left">비교할 첫 번째 인스턴스입니다. </param>
      <param name="right">비교할 두 번째 인스턴스입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1">
      <summary>관리 코드에서 Windows 런타임 이벤트의 구현을 지원하도록 대리자 및 이벤트 토큰 간의 매핑을 저장합니다.</summary>
      <typeparam name="T">특정 이벤트에 대한 이벤트 처리기의 형식입니다. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1.#ctor">
      <summary>
        <see cref="T:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="T" />가 대리자 형식이 아닌 경우 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1.AddEventHandler(`0)">
      <summary>지정된 이벤트 처리기를 테이블과 호출 목록에 추가하고 이벤트 처리기를 제거하는 데 사용할 수 있는 토큰을 반환합니다. </summary>
      <returns>테이블 및 호출 목록에서 이벤트 처리기를 제거하는 데 사용할 수 있는 토큰입니다. </returns>
      <param name="handler">추가할 이벤트 처리기입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1.GetOrCreateEventRegistrationTokenTable(System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable{`0}@)">
      <summary>지정된 이벤트 등록 토큰 테이블이 null이 아닌 경우 이 테이블을 반환하며, 그렇지 않은 경우 새 이벤트 등록 토큰 테이블을 반환합니다. </summary>
      <returns>null이 아닌 경우 이벤트 등록 토큰 테이블이 <paramref name="refEventTable" />로 지정되며, 그렇지 않은 경우 새 이벤트 등록 토큰 테이블이 지정됩니다. </returns>
      <param name="refEventTable">참조로 전달되는 이벤트 등록 토큰 테이블입니다. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1.InvocationList">
      <summary>추가되었지만 아직 제거되지 않은 모든 이벤트 처리기 대리자를 포함하는 호출 목록이 있는 <paramref name="T" /> 형식의 대리자를 가져오거나 설정합니다.이 대리자를 호출하면 모든 이벤트 처리기가 호출됩니다.</summary>
      <returns>이벤트에 대해 현재 등록된 모든 이벤트 처리기 대리자를 나타내는 <paramref name="T" /> 형식의 대리자입니다. </returns>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1.RemoveEventHandler(System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken)">
      <summary>테이블과 호출 목록에서 지정된 토큰과 관련된 이벤트 처리기를 제거합니다. </summary>
      <param name="token">이벤트 처리기에 추가되었을 때 반환된 토큰입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1.RemoveEventHandler(`0)">
      <summary>테이블과 호출 목록에서 지정된 이벤트 처리기 대리자를 제거합니다. </summary>
      <param name="handler">제거할 이벤트 처리기입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.IActivationFactory">
      <summary>Windows 런타임에 의해 활성화될 클래스를 사용하도록 설정합니다. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.IActivationFactory.ActivateInstance">
      <summary>
        <see cref="T:System.Runtime.InteropServices.WindowsRuntime.IActivationFactory" /> 인터페이스에서 만든 Windows 런타임 클래스의 새 인스턴스를 반환합니다. </summary>
      <returns>Windows 런타임 클래스의 새 인스턴스입니다. </returns>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute">
      <summary>지정된 인터페이스를 처음 구현한 대상 형식 버전을 지정합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute.#ctor(System.Type,System.Byte,System.Byte,System.Byte,System.Byte)">
      <summary>대상 형식이 구현되는 인스턴스와 해당 인스턴스가 처음 구현되었던 버전을 지정하여 <see cref="T:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="interfaceType">지정된 버전의 대상 형식에 처음 구현된 인터페이스입니다. </param>
      <param name="majorVersion">먼저 <paramref name="interfaceType" />을 구현한 대상 형식의 버전의 주요 구성 요소입니다.</param>
      <param name="minorVersion">먼저 <paramref name="interfaceType" />을 구현한 대상 형식의 버전의 부 버전 구성 요소입니다.</param>
      <param name="buildVersion">먼저 <paramref name="interfaceType" />을 구현한 대상 형식의 버전의 빌드 구성 요소입니다.</param>
      <param name="revisionVersion">먼저 <paramref name="interfaceType" />을 구현한 대상 형식의 버전의 수정 번호 구성 요소입니다.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute.BuildVersion">
      <summary>인터페이스를 가장 먼저 구현한 대상 형식 버전의 빌드 구성 요소를 가져옵니다. </summary>
      <returns>버전의 빌드 구성 요소입니다.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute.InterfaceType">
      <summary>대상 형식이 구현하는 인터페이스의 형식을 가져옵니다. </summary>
      <returns>인터페이스의 형식입니다. </returns>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute.MajorVersion">
      <summary>인터페이스를 가장 먼저 구현한 대상 형식의 주 버전 구성 요소를 가져옵니다. </summary>
      <returns>버전의 주요 구성 요소입니다.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute.MinorVersion">
      <summary>인터페이스를 가장 먼저 구현한 대상의 부 버전 구성 요소를 가져옵니다. </summary>
      <returns>버전의 부분 구성 요소입니다. </returns>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute.RevisionVersion">
      <summary>인터페이스를 가장 먼저 구현한 대상 형식의 부 버전 구성 요소를 가져옵니다. </summary>
      <returns>버전의 수정 버전 구성 요소입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.ReadOnlyArrayAttribute">
      <summary>Windows 런타임 구성 요소의 배열 매개 변수에 적용할 때 해당 매개 변수에 전달되는 배열의 콘텐츠가 입력에만 사용됨을 지정합니다.호출자는 호출로 배열이 변경되지 않도록 합니다.관리 코드를 사용 하 여 작성 된 호출자에 대 한 중요 한 정보에 대 한 설명 부분을 참조 하십시오.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.ReadOnlyArrayAttribute.#ctor">
      <summary>
        <see cref="T:System.Runtime.InteropServices.WindowsRuntime.ReadOnlyArrayAttribute" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.ReturnValueNameAttribute">
      <summary>Windows 런타임 구성 요소의 메서드 반환 값의 이름을 지정합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.ReturnValueNameAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.WindowsRuntime.ReturnValueNameAttribute" /> 클래스의 새 인스턴스를 초기화하고 반환 값의 이름을 지정합니다.</summary>
      <param name="name">반환 값의 이름입니다. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.ReturnValueNameAttribute.Name">
      <summary>Windows 런타임 구성 요소에서 메서드의 반환 값에 지정된 이름을 가져옵니다.</summary>
      <returns>메서드의 반환 값의 이름입니다. </returns>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal">
      <summary>.NET Framework 및 Windows 런타임 간 데이터를 마샬링하기 위한 도우미 메서드를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal.AddEventHandler``1(System.Func{``0,System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken},System.Action{System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken},``0)">
      <summary>지정된 이벤트 처리기를 Windows 런타임 이벤트에 추가합니다.</summary>
      <param name="addMethod">Windows 런타임 이벤트에 이벤트 처리기를 추가하는 메서드를 나타내는 대리자입니다. </param>
      <param name="removeMethod">Windows 런타임 이벤트에서 이벤트 처리기를 제거하는 메서드를 나타내는 대리자입니다. </param>
      <param name="handler">추가된 이벤트 처리기를 나타내는 대리자입니다. </param>
      <typeparam name="T">이벤트 처리기를 나타내는 대리자의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="addMethod" />가 null입니다. 또는<paramref name="removeMethod" />가 null입니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal.FreeHString(System.IntPtr)">
      <summary>지정된 Windows 런타임 HSTRING을 해제합니다. </summary>
      <param name="ptr">사용할 수 있는 HSTRING 주소입니다.</param>
      <exception cref="T:System.PlatformNotSupportedException">현재 버전의 운영 체제에서는 Windows 런타임가 지원되지 않습니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal.GetActivationFactory(System.Type)">
      <summary>지정된 Windows 런타임 형식에 대한 활성화 팩터리 인터페이스를 구현하는 개체를 반환합니다. </summary>
      <returns>활성화 팩터리 인터페이스를 구현하는 개체입니다. </returns>
      <param name="type">활성화 팩터리 인터페이스를 가져올 Windows 런타임 형식입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="type" />은 Windows 런타임 형식(즉 Windows 런타임 자체에 속하거나 Windows 런타임 구성 요소에 정의되어 있는 형식)을 나타내지 않습니다. 또는<paramref name="type" />에 지정된 개체가 공용 언어 런타임 형식 시스템에서 제공되지 않았습니다. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" />가 null입니다. </exception>
      <exception cref="T:System.TypeLoadException">지정된 Windows 런타임 클래스가 제대로 등록되지 않은 경우예를 들어 .winmd 파일을 찾았지만 Windows 런타임이 구현을 찾지 못했습니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal.PtrToStringHString(System.IntPtr)">
      <summary>지정된 Windows 런타임 HSTRING의 복사본이 포함되어 있는 관리되는 문자열을 반환합니다. </summary>
      <returns>
        <paramref name="ptr" />이 <see cref="F:System.IntPtr.Zero" />가 아니면 HSTRING의 복사본을 포함하는 관리되는 문자열이고, 그렇지 않으면 <see cref="F:System.String.Empty" />입니다. </returns>
      <param name="ptr">복사할 HSTRING에 대한 관리되지 않는 포인터입니다. </param>
      <exception cref="T:System.PlatformNotSupportedException">현재 버전의 운영 체제에서는 Windows 런타임가 지원되지 않습니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal.RemoveAllEventHandlers(System.Action{System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken})">
      <summary>지정된 메서드를 사용하여 제거할 수 있는 모든 이벤트 처리기를 제거합니다. </summary>
      <param name="removeMethod">Windows 런타임 이벤트에서 이벤트 처리기를 제거하는 메서드를 나타내는 대리자입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="removeMethod" />가 null입니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal.RemoveEventHandler``1(System.Action{System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken},``0)">
      <summary>Windows 런타임 이벤트에서 지정한 이벤트 처리기를 제거합니다. </summary>
      <param name="removeMethod">Windows 런타임 이벤트에서 이벤트 처리기를 제거하는 메서드를 나타내는 대리자입니다. </param>
      <param name="handler">제거되는 이벤트 처리기입니다. </param>
      <typeparam name="T">이벤트 처리기를 나타내는 대리자의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="removeMethod" />가 null입니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal.StringToHString(System.String)">
      <summary>Windows 런타임 HSTRING을 할당하고 지정된 관리되는 문자열을 여기에 복사합니다. </summary>
      <returns>
        <paramref name="s" />이 <see cref="F:System.String.Empty" />인 경우 새 HSTRING 또는 <see cref="F:System.IntPtr.Zero" />에 대한 관리되지 않는 포인터입니다 </returns>
      <param name="s">복사할 관리되는 문자열입니다. </param>
      <exception cref="T:System.PlatformNotSupportedException">현재 버전의 운영 체제에서는 Windows 런타임가 지원되지 않습니다. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" />가 null입니다. </exception>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.WriteOnlyArrayAttribute">
      <summary>Windows 런타임 구성 요소의 배열 매개 변수에 적용할 때 해당 매개 변수에 전달되는 배열의 콘텐츠가 출력에만 사용됨을 지정합니다.호출자는 콘텐츠가 초기화되었음을 보증하지 않으며, 호출된 메서드가 콘텐츠를 읽지 않아야 합니다.관리 코드를 사용 하 여 작성 된 호출자에 대 한 중요 한 정보에 대 한 설명 부분을 참조 하십시오.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WriteOnlyArrayAttribute.#ctor">
      <summary>
        <see cref="T:System.Runtime.InteropServices.WindowsRuntime.WriteOnlyArrayAttribute" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
  </members>
</doc>