﻿using MetroFramework.Controls;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms.Design;

namespace MetroFramework.Design
{
    [Designer(typeof(ScrollableControlDesigner), typeof(ParentControlDesigner))]
    internal class MetroScrollBarDesigner : ControlDesigner
    {
        public override SelectionRules SelectionRules
        {
            get
            {
                var propertyDescriptor = TypeDescriptor.GetProperties(Component)["Orientation"];
                if (propertyDescriptor != null)
                {
                    var metroScrollOrientation = (MetroScrollOrientation)propertyDescriptor.GetValue(Component);
                    if (metroScrollOrientation == MetroScrollOrientation.Vertical)
                        return SelectionRules.Moveable | SelectionRules.Visible | SelectionRules.TopSizeable |
                               SelectionRules.BottomSizeable;
                    return SelectionRules.Moveable | SelectionRules.Visible | SelectionRules.LeftSizeable |
                           SelectionRules.RightSizeable;
                }

                return base.SelectionRules;
            }
        }

        protected override void PreFilterProperties(IDictionary properties)
        {
            properties.Remove("Text");
            properties.Remove("BackgroundImage");
            properties.Remove("ForeColor");
            properties.Remove("ImeMode");
            properties.Remove("Padding");
            properties.Remove("BackgroundImageLayout");
            properties.Remove("BackColor");
            properties.Remove("Font");
            properties.Remove("RightToLeft");
            base.PreFilterProperties(properties);
        }
    }
}