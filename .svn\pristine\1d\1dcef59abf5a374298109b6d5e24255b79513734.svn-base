﻿using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Web;

namespace OCRTools
{
    public enum WeatherIconType
    {
        QQ = 1,
        墨迹 = 2
    }

    public class CommonWeather
    {
        private const string STR_QQ_WEATHER_URL =
            "https://wis.qq.com/weather/common?source=pc&weather_type=observe%7Calarm&province={0}&city={1}&county={2}&callback=weather";

        private const string Str360WeatherUrl =
            "http://weather.kjjs.360.cn/freshcalendar/weather?ver=1.0.0.1125&output=json";
        private const string STR_QQ_PROVINCE = "\"province\":\"";
        private const string STR_QQ_CITY = "\"city\":\"";
        private const string STR_QQ_COUNTY = "\"district\":\"";
        private const string STR_QQ_WEATHER_UPDATE_TIME = "\"update_time\":\"";
        private const string STR_QQ_WEATHER_IMAGE = "\"weather_code\":\"";
        private const string STR_QQ_WEATHER_TIPMSG = "\"detail\":\"";

        private const string Str360WeatherSection = "\"realtime\":";
        private const string Str360WeatherImage = "\"img\":\"";
        private const string Str360WeatherCity = "\"city_name\":\"";
        private const string Str360WeatherUpdateTime = "\"dataUptime\":\"";
        private const string Str360WeatherTipMsg = "\"content\":\"";

        private const string STR_QQ_WEATHER_IMG_URL =
            //"https://mat1.gtimg.com/pingjs/ext2020/weather/pc/icon/weather/day/{0}.png";//小图
            "https://mat1.gtimg.com/pingjs/ext2020/weather/pc/icon/currentweather/day/{0}.png"; //大图

        private const string STR_MO_JI_WEATHER_IMG_URL =
            "https://h5tq.moji.com/tianqi/assets/images/weather/w{0}.png";

        public static void InitWeather()
        {
            try
            {
                var timerInfo = new TimerInfo
                {
                    TimerType = "LoopMinutes",
                    DateValue = 30,
                    IsExecFirst = true,
                    TaskName = "WeatherTask"
                };
                var updateTimeTaskService = TimerTaskService.CreateTimerTaskService(timerInfo, UpdateMethod);
                updateTimeTaskService.Start();
            }
            catch
            {
            }
        }

        public static bool UpdateMethod(bool isUserUpdate, DateTime dtDate, string paramStr)
        {
            try
            {
                if (!CommonSetting.天气图标) return false;
                var weatherIcon = CommonSetting.ConvertToEnum(CommonSetting.天气图标样式, WeatherIconType.QQ);
                var strImage = GetWeather(weatherIcon);
                if (!string.IsNullOrEmpty(strImage))
                    try
                    {
                        CommonSetting.工具栏图片 = strImage;
                        FrmMain.FrmTool.RefreshImage();
                    }
                    catch
                    {
                    }
            }
            catch (Exception oe)
            {
                Log.WriteError("更新天气失败", oe);
            }
            return true;
        }

        public static string GetWeatherIcon(string data, WeatherIconType iconType, bool isAuto = true)
        {
            var url = string.Empty;
            switch (iconType)
            {
                case WeatherIconType.QQ:
                    url = string.Format(STR_QQ_WEATHER_IMG_URL, data);
                    break;
                case WeatherIconType.墨迹:
                    url = string.Format(STR_MO_JI_WEATHER_IMG_URL, data.TrimStart('0').PadRight(1, '0'));
                    break;
            }

            var result = CommonSetting.SetHeadImageByUrl(url);
            if (string.IsNullOrEmpty(result) && isAuto)
                return GetWeatherIcon(data, iconType == WeatherIconType.QQ ? WeatherIconType.墨迹 : WeatherIconType.QQ,
                    false);
            return result;
        }

        public static string GetWeather(WeatherIconType iconType, bool isShowTip = false)
        {
            var qqWeather = GetQqWeather();
            var _360Weather = Get360Weather();
            WeatherDetail weather;
            if (string.IsNullOrEmpty(qqWeather.code) || string.IsNullOrEmpty(_360Weather.code))
            {
                weather = string.IsNullOrEmpty(qqWeather.code) ? _360Weather : qqWeather;
            }
            else
            {
                weather = qqWeather.updateTime > _360Weather.updateTime ? qqWeather : _360Weather;
            }
            var result = string.Empty;
            if (!string.IsNullOrEmpty(weather.code))
                result = GetWeatherIcon(weather.code, iconType);
            if (string.IsNullOrEmpty(weather.code))
            {
                CommonMethod.ShowHelpMsg("更新天气失败！".CurrentText());
            }
            else
            {
                if (isShowTip)
                    CommonMethod.ShowHelpMsg(string.Format("更新{0}天气成功！".CurrentText() + "{1:HH:mm:ss}", weather.city, weather.updateTime, weather.type));
                if (!string.IsNullOrEmpty(weather.tipMsg))
                {
                    CommonMethod.ShowNotificationTip(weather.tipMsg, null, 5 * 1000);
                }
            }
            return result;
        }

        public static WeatherDetail Get360Weather()
        {
            var result = new WeatherDetail { type = "360" };
            try
            {
                var html = WebClientExt.GetHtml(Str360WeatherUrl).Replace(" ", "").Trim();
                if (!string.IsNullOrEmpty(html) && html.Contains(Str360WeatherSection))
                {
                    html = html.Substring(html.IndexOf(Str360WeatherSection) + Str360WeatherSection.Length);
                    result.code = CommonMethod.SubString(html, Str360WeatherImage, "\"").Trim();
                    result.city = Regex.Unescape(CommonMethod.SubString(html, Str360WeatherCity, "\"").Trim());
                    var strTime = CommonMethod.SubString(html, Str360WeatherUpdateTime, "\"").Trim();
                    if (!string.IsNullOrEmpty(strTime))
                    {
                        result.updateTime = CommonMethod.ConvertToDateTime(strTime);
                    }
                    result.tipMsg = Regex.Unescape(CommonMethod.SubString(html, Str360WeatherTipMsg, "\"").Trim());
                }
            }
            catch { }
            return result;
        }

        public static WeatherDetail GetQqWeather()
        {
            var result = new WeatherDetail { type = "QQ" };
            try
            {
                var geo = IpHelper.GetLocation();
                if (string.IsNullOrEmpty(geo.province)) return result;
                var html = WebClientExt.GetHtml(string.Format(STR_QQ_WEATHER_URL, HttpUtility.UrlEncode(geo.province),
                    HttpUtility.UrlEncode(geo.city), HttpUtility.UrlEncode(geo.district))
                ).Replace(" ", "").Trim();
                result.code = CommonMethod.SubString(html, STR_QQ_WEATHER_IMAGE, "\"").Trim();
                var strTime = CommonMethod.SubString(html, STR_QQ_WEATHER_UPDATE_TIME, "\"").Trim();
                if (!string.IsNullOrEmpty(strTime))
                {
                    result.updateTime = DateTime.ParseExact(strTime, "yyyyMMddHHmm", null);
                }
                result.tipMsg = CommonMethod.SubString(html, STR_QQ_WEATHER_TIPMSG, "\"").Trim();

                if (!string.IsNullOrEmpty(result.code))
                    result.city = string.IsNullOrEmpty(geo.district) ? geo.city : geo.district;
            }
            catch { }
            return result;
        }
    }

    public class WeatherDetail
    {
        public string type { get; set; }

        public string code { get; set; }

        public string city { get; set; }

        public DateTime updateTime { get; set; }

        public string tipMsg { get; set; }
    }
}