using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class LABEL : CellValue
	{
		public string Value;

		public LABEL(Record record)
			: base(record)
		{
		}

		public LABEL()
		{
			Type = 516;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			RowIndex = binaryReader.ReadUInt16();
			ColIndex = binaryReader.ReadUInt16();
			XFIndex = binaryReader.ReadUInt16();
			Value = ReadString(binaryReader, 16);
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(RowIndex);
			binaryWriter.Write(ColIndex);
			binaryWriter.Write(XFIndex);
			Record.WriteString(binaryWriter, Value, 16);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
