using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using OCRTools.Common;

namespace OCRTools.UserControlEx
{
    /// <summary>
    /// 文本块与文本位置映射器，负责将OCR文本块与完整文本进行位置映射。
    /// 
    /// 核心功能：
    /// 1. 将OCR识别的文本块（TextCellInfo）与RichTextBox等控件中的完整文本进行精确位置对应
    /// 2. 支持模糊匹配策略，处理OCR识别误差、标点符号差异、空白字符不一致等问题
    /// 3. 通过置信度算法评估匹配质量，自动选择最佳匹配位置
    /// 4. 防止文本块重叠映射，确保每个文本区域只被一个文本块占用
    /// 5. 支持多页文档和复杂布局的文本块排序
    /// 
    /// 适用场景：
    /// - OCR识别后的文本高亮显示
    /// - 文本块与原文的精确定位
    /// - 支持翻译、校对等文本交互功能
    /// - 处理扫描文档、PDF转换等复杂文本场景
    /// 
    /// 匹配策略：
    /// - 基于LCS（最长公共子序列）算法的相似度计算
    /// - 支持标点符号容错和空白字符忽略
    /// - 对非标点符号和非空格字符采用严格匹配策略
    /// - 确保数字、字母等内容的准确匹配
    /// </summary>
    public class TextPositionMapper
    {
        /// <summary>
        /// 匹配结果类，存储单次文本匹配的详细信息
        /// 用于评估OCR文本块与完整文本中某个位置的匹配质量
        /// </summary>
        private class MatchResult
        {
            /// <summary>是否找到有效匹配（基于匹配长度和质量的初步判断）</summary>
            public bool IsMatch { get; set; }

            /// <summary>匹配文本在完整文本中的起始索引（包含）</summary>
            public int StartIndex { get; set; }

            /// <summary>匹配文本在完整文本中的结束索引（不包含，即[StartIndex, EndIndex)区间）</summary>
            public int EndIndex { get; set; }

            /// <summary>匹配过程中非标点符号字符的不匹配数量，用于计算匹配质量</summary>
            public int MismatchedNonPunctuationChars { get; set; }

            /// <summary>匹配置信度（0.0-1.0），综合考虑LCS相似度、字符匹配率、长度相似度等因素</summary>
            public double Confidence { get; set; }

            // 延迟创建的匹配文本缓存
            private string _matchedText;

            /// <summary>
            /// 从完整文本中提取的匹配片段（延迟创建以优化性能）
            /// </summary>
            public string MatchedText
            {
                get => _matchedText;
                set => _matchedText = value;
            }

            /// <summary>
            /// 延迟创建匹配文本，使用外部提供的源文本
            /// </summary>
            /// <param name="sourceText">完整的源文本</param>
            /// <returns>匹配的文本片段</returns>
            public string GetMatchedText(string sourceText)
            {
                if (_matchedText == null && sourceText != null && EndIndex > StartIndex)
                {
                    _matchedText = sourceText.Substring(StartIndex, EndIndex - StartIndex);
                }
                return _matchedText;
            }
        }

        /// <summary>
        /// 文本块在完整文本中的位置信息
        /// 用于记录OCR文本块映射到完整文本的具体位置和范围
        /// </summary>
        public class TextPosition
        {
            /// <summary>文本块在完整文本中的起始索引</summary>
            public int StartIndex { get; set; }

            /// <summary>文本块长度</summary>
            public int Length { get; set; }
        }

        /// <summary>存储文本块到位置的映射关系，key为OCR文本块，value为在完整文本中的位置</summary>
        private readonly Dictionary<TextCellInfo, TextPosition> textPositionMap;

        /// <summary>OCR识别得到的所有文本块列表，包含位置、内容等信息</summary>
        private readonly List<TextCellInfo> cells;

        /// <summary>目标完整文本（如RichTextBox.Text），作为匹配的参考文本</summary>
        private readonly string fullText;

        /// <summary>
        /// 获取文本块实际内容的委托函数
        /// 支持多语言场景：可返回原文、翻译文本或其他处理后的文本
        /// 灵活适应不同的文本获取需求
        /// </summary>
        private readonly Func<TextCellInfo, string> getTextByContent;

        /// <summary>
        /// 文本块比较器，用于字典key的相等性比较
        /// 基于文本块的位置信息进行比较，而非文本内容
        /// 允许一定的位置误差（MAX_POSITION_DELTA像素）
        /// </summary>
        private readonly IEqualityComparer<TextCellInfo> cellComparer;

        /// <summary>
        /// 获取只读的文本块与文本位置映射表
        /// </summary>
        public IReadOnlyDictionary<TextCellInfo, TextPosition> TextPositionMap => textPositionMap;

        /// <summary>
        /// 文本块预处理数据，用于缓存处理结果避免重复计算
        /// </summary>
        private class CellTextData
        {
            /// <summary>原始文本内容</summary>
            public string OriginalText { get; set; }
            /// <summary>清理后的文本（移除空白字符）</summary>
            public string CleanedText { get; set; }
            /// <summary>用于匹配算法的清理文本</summary>
            public string CleanedForMatching { get; set; }
            /// <summary>预处理的小写文本，避免重复大小写转换</summary>
            public string LowerCaseText { get; set; }
        }

        /// <summary>
        /// 构造函数，初始化文本位置映射器
        /// </summary>
        /// <param name="fullText">完整的目标文本内容（如RichTextBox.Text、TextBox.Text等控件文本）</param>
        /// <param name="cells">OCR识别得到的文本块列表，每个文本块包含位置和内容信息</param>
        /// <param name="getTextByContent">
        /// 获取文本块内容的委托函数，参数为TextCellInfo，返回该文本块的实际文本内容
        /// 可以返回原始OCR文本、翻译后文本或经过其他处理的文本
        /// 例如：cell => cell.text 或 cell => translatedTexts[cell.id]
        /// </param>
        public TextPositionMapper(
            string fullText,
            List<TextCellInfo> cells,
            Func<TextCellInfo, string> getTextByContent)
        {
            this.fullText = fullText;
            this.cells = cells;
            this.getTextByContent = getTextByContent;
            this.cellComparer = new TextCellInfoComparer();
            this.textPositionMap = new Dictionary<TextCellInfo, TextPosition>(this.cellComparer);
        }

        /// <summary>
        /// 构建文本块与文本位置的映射关系
        ///
        /// 算法流程：
        /// 1. 清空现有映射关系
        /// 2. 对文本块按页面、行、列进行排序，确保处理顺序符合阅读习惯
        /// 3. 对每个文本块在完整文本中寻找最佳匹配位置
        /// 4. 使用多候选匹配策略，计算每个候选位置的置信度
        /// 5. 选择置信度最高且未被占用的位置作为最终匹配
        /// 6. 维护已占用区域列表，防止文本块重叠映射
        ///
        /// 性能考虑：
        /// - 当前实现对每个文本块进行全文搜索，适用于中小型文档
        /// - 对于大型文档，可考虑实现搜索窗口优化
        /// </summary>
        public void Build()
        {
            textPositionMap.Clear();
            if (cells == null || !cells.Any() || string.IsNullOrEmpty(fullText)) return;

            // 1. 预处理所有文本内容，避免重复调用委托和字符串处理
            var cellTextData = PreprocessCellTexts(cells);
            if (!cellTextData.Any()) return;

            // 2. 一次性排序和过滤，避免多次LINQ操作
            var sortedCells = SortAndFilterCells(cellTextData.Keys);
            if (!sortedCells.Any()) return;

            // 最小置信度阈值：过滤掉质量过低的匹配
            // 在严格匹配模式下，提高阈值以确保更高质量的匹配结果
            // 阈值说明：0.5表示至少50%的相似度
            // 由于我们已经对非标点字符实施了严格匹配，这个阈值主要影响标点和空格匹配的评估
            const double MinConfidenceThreshold = 0.5;

            // 已占用区域列表，防止文本块重叠映射
            // 每个元组表示一个已占用的文本区间：(起始索引, 结束索引)
            // 预分配容量以提高性能
            var occupiedRegions = new List<Tuple<int, int>>(sortedCells.Count);

            // 为每个文本块找到候选匹配结果，并根据匹配结果数量分类
            // 预分配集合容量以提高性能
            var cellCandidateMatches = new Dictionary<TextCellInfo, List<MatchResult>>(sortedCells.Count);
            var cellsWithMultipleHighConfidenceMatches = new List<TextCellInfo>(sortedCells.Count / 4); // 估算25%有多个匹配
            var cellsWithOneOrLowConfidenceMatches = new List<TextCellInfo>(sortedCells.Count);

            // 第一阶段：为每个文本块寻找所有候选匹配位置
            // 使用并行处理优化候选匹配搜索，但保持原有的处理顺序
            foreach (var cell in sortedCells)
            {
                var textData = cellTextData[cell];
                if (string.IsNullOrEmpty(textData.CleanedForMatching)) continue; // 跳过纯空白文本块

                // 并行搜索候选匹配位置
                var candidateMatches = FindCandidateMatchesParallel(fullText, textData, MinConfidenceThreshold);

                // 将候选匹配结果存储起来
                if (candidateMatches.Any())
                {
                    cellCandidateMatches[cell] = candidateMatches;

                    // 计算高置信度匹配的数量 (置信度 >= 0.9)
                    int highConfidenceMatches = candidateMatches.Count(m => Math.Round(m.Confidence, 2) >= 0.9);

                    // 根据匹配情况分类
                    if (highConfidenceMatches > 1)
                    {
                        // 有多个高置信度匹配，优先级较低
                        cellsWithMultipleHighConfidenceMatches.Add(cell);
                    }
                    else
                    {
                        // 只有一个或没有高置信度匹配，优先级较高
                        cellsWithOneOrLowConfidenceMatches.Add(cell);
                    }
                }
            }

            // 第二阶段：处理只有一个高置信度匹配或全都是低置信度匹配的文本块
            foreach (var cell in cellsWithOneOrLowConfidenceMatches)
            {
                ProcessCellMatching(cell, cellCandidateMatches[cell], occupiedRegions);
            }

            // 第三阶段：处理有多个高置信度匹配的文本块，根据文本长度分布智能分组
            if (cellsWithMultipleHighConfidenceMatches.Any())
            {
                // 计算文本长度统计信息
                var lengths = cellsWithMultipleHighConfidenceMatches.Select(p => p.words.Length).ToList();
                double avgLength = lengths.Average();
                double stdDev = Math.Sqrt(lengths.Average(l => Math.Pow(l - avgLength, 2)));
                
                // 根据长度分布创建动态分组
                var lengthGroups = new List<Tuple<double, double>>();
                
                // 如果标准差较大，使用更细致的分组
                if (stdDev > avgLength * 0.5)
                {
                    // 长文本组 (> avg+stdDev)
                    lengthGroups.Add(new Tuple<double, double>(avgLength + stdDev, double.MaxValue));
                    // 中长文本组 (avg ~ avg+stdDev)
                    lengthGroups.Add(new Tuple<double, double>(avgLength, avgLength + stdDev));
                    // 中短文本组 (avg-stdDev ~ avg)
                    lengthGroups.Add(new Tuple<double, double>(Math.Max(1, avgLength - stdDev), avgLength));
                    // 短文本组 (< avg-stdDev)
                    lengthGroups.Add(new Tuple<double, double>(0, Math.Max(1, avgLength - stdDev)));
                }
                // 如果标准差适中，使用三组
                else if (stdDev > avgLength * 0.2)
                {
                    // 长文本组 (> avg*1.2)
                    lengthGroups.Add(new Tuple<double, double>(avgLength * 1.2, double.MaxValue));
                    // 中等文本组 (avg*0.8 ~ avg*1.2)
                    lengthGroups.Add(new Tuple<double, double>(avgLength * 0.8, avgLength * 1.2));
                    // 短文本组 (< avg*0.8)
                    lengthGroups.Add(new Tuple<double, double>(0, avgLength * 0.8));
                }
                // 如果标准差小，使用二组
                else
                {
                    // 长文本组 (>= avg)
                    lengthGroups.Add(new Tuple<double, double>(avgLength, double.MaxValue));
                    // 短文本组 (< avg) 
                    lengthGroups.Add(new Tuple<double, double>(0, avgLength));
                }

                // 按分组依次处理，从长到短
                foreach (var group in lengthGroups)
                {
                    double minLength = group.Item1;
                    double maxLength = group.Item2;

                    var cellsInGroup = cellsWithMultipleHighConfidenceMatches
                        .Where(p => p.words.Length >= minLength && p.words.Length < maxLength)
                        .ToList();

                    foreach (var cell in cellsInGroup)
                    {
                        ProcessCellMatching(cell, cellCandidateMatches[cell], occupiedRegions);
                    }
                }
            }
        }



        /// <summary>
        /// 并行搜索单个文本块的候选匹配位置
        /// </summary>
        /// <param name="fullText">完整文本</param>
        /// <param name="textData">预处理的文本数据</param>
        /// <param name="minConfidenceThreshold">最小置信度阈值</param>
        /// <returns>候选匹配结果列表</returns>
        private List<MatchResult> FindCandidateMatchesParallel(string fullText, CellTextData textData, double minConfidenceThreshold)
        {
            // 预分配集合容量：根据文本长度和匹配文本长度估算可能的匹配数量
            int estimatedMatches = Math.Min(100, fullText.Length / Math.Max(1, textData.CleanedForMatching.Length));
            var candidateMatches = new List<MatchResult>(estimatedMatches);

            // 将全文搜索分段并行处理
            int segmentSize = Math.Max(100, fullText.Length / Environment.ProcessorCount);
            int segmentCount = (fullText.Length + segmentSize - 1) / segmentSize;
            var segments = new List<Tuple<int, int>>(segmentCount);

            for (int i = 0; i < fullText.Length; i += segmentSize)
            {
                int endIndex = Math.Min(i + segmentSize + textData.CleanedForMatching.Length, fullText.Length);
                segments.Add(new Tuple<int, int>(i, endIndex));
            }

            // 并行处理各个段
            var segmentResults = new ConcurrentBag<List<MatchResult>>();

            Parallel.ForEach(segments, new ParallelOptions
            {
                MaxDegreeOfParallelism = Math.Max(1, Environment.ProcessorCount - 1)
            }, segment =>
            {
                // 预分配段内匹配结果容量
                int segmentLength = segment.Item2 - segment.Item1;
                int estimatedSegmentMatches = Math.Min(50, segmentLength / Math.Max(1, textData.CleanedForMatching.Length));
                var segmentMatches = new List<MatchResult>(estimatedSegmentMatches);

                for (int i = segment.Item1; i < segment.Item2 - textData.CleanedForMatching.Length + 1; i++)
                {
                    MatchResult rawMatchAttempt = TryMatchTextAt(fullText, i, textData);

                    // 只有匹配成功时才会返回非null对象
                    if (rawMatchAttempt != null)
                    {
                        // 延迟创建匹配文本用于置信度计算
                        rawMatchAttempt.MatchedText = rawMatchAttempt.GetMatchedText(fullText);

                        rawMatchAttempt.Confidence = CalculateMatchConfidence(
                            textData.OriginalText,
                            rawMatchAttempt.MatchedText,
                            rawMatchAttempt.MismatchedNonPunctuationChars,
                            textData.CleanedForMatching.Length,
                            textData.CleanedText
                        );

                        if (rawMatchAttempt.Confidence >= minConfidenceThreshold)
                        {
                            segmentMatches.Add(rawMatchAttempt);
                        }
                    }
                }

                segmentResults.Add(segmentMatches);
            });

            // 合并所有段的结果
            foreach (var segmentResult in segmentResults)
            {
                candidateMatches.AddRange(segmentResult);
            }

            return candidateMatches;
        }

        /// <summary>
        /// 预处理所有文本块内容，避免重复调用委托和字符串处理
        /// </summary>
        /// <param name="cells">文本块列表</param>
        /// <returns>预处理后的文本数据字典</returns>
        private Dictionary<TextCellInfo, CellTextData> PreprocessCellTexts(List<TextCellInfo> cells)
        {
            // 预分配字典容量
            var result = new Dictionary<TextCellInfo, CellTextData>(cells.Count, cellComparer);

            foreach (var cell in cells)
            {
                if (cell?.location == null) continue;

                string originalText = getTextByContent(cell);
                if (string.IsNullOrEmpty(originalText)) continue;

                // 一次性处理，避免重复计算
                var cleanedText = RemoveWhitespace(originalText);
                result[cell] = new CellTextData
                {
                    OriginalText = originalText,
                    CleanedText = cleanedText,
                    CleanedForMatching = cleanedText,
                    LowerCaseText = cleanedText.ToLower()
                };
            }

            return result;
        }

        /// <summary>
        /// 扩展的标点符号判断，基于反向逻辑
        /// 根据需求：除了标点和空格外，其他字符需要完全匹配
        /// 反向思维：不是内容字符的都视为标点符号（可以宽松匹配）
        /// </summary>
        /// <param name="c">要判断的字符</param>
        /// <returns>如果应该视为标点符号返回true</returns>
        private static bool IsPunctuationExtended(char c)
        {
            // 使用反向逻辑：不是内容字符的都视为标点符号
            return !IsContentCharacter(c);
        }

        /// <summary>
        /// 判断字符是否为需要严格匹配的内容字符
        /// 只有字母、数字、汉字等"内容字符"需要严格匹配
        /// </summary>
        /// <param name="c">要判断的字符</param>
        /// <returns>如果是需要严格匹配的内容字符返回true</returns>
        private static bool IsContentCharacter(char c)
        {
            var category = char.GetUnicodeCategory(c);

            // 只有这些类别的字符需要严格匹配（字母、数字等实际内容）
            return category == UnicodeCategory.UppercaseLetter ||        // Lu: 大写字母
                   category == UnicodeCategory.LowercaseLetter ||        // Ll: 小写字母
                   category == UnicodeCategory.TitlecaseLetter ||        // Lt: 标题字母
                   category == UnicodeCategory.ModifierLetter ||         // Lm: 修饰字母
                   category == UnicodeCategory.OtherLetter ||            // Lo: 其他字母 (包括汉字、日文、韩文等)
                   category == UnicodeCategory.DecimalDigitNumber ||     // Nd: 十进制数字 (0-9)
                   category == UnicodeCategory.LetterNumber ||           // Nl: 字母数字 (如罗马数字)
                   category == UnicodeCategory.OtherNumber;              // No: 其他数字
        }

        /// <summary>
        /// 优化的字符串清理方法，使用StringBuilder避免多次字符串分配
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>移除空白字符后的字符串</returns>
        private static string RemoveWhitespace(string input)
        {
            if (string.IsNullOrEmpty(input)) return string.Empty;

            // 使用StringBuilder避免多次字符串分配
            var sb = new StringBuilder(input.Length);
            for (int i = 0; i < input.Length; i++)
            {
                if (!char.IsWhiteSpace(input[i]))
                {
                    sb.Append(input[i]);
                }
            }
            return sb.ToString();
        }

        /// <summary>
        /// 一次性排序和过滤文本块，避免多次LINQ操作
        /// </summary>
        /// <param name="cells">文本块集合</param>
        /// <returns>排序后的文本块列表</returns>
        private List<TextCellInfo> SortAndFilterCells(IEnumerable<TextCellInfo> cells)
        {
            var result = new List<TextCellInfo>(cells.Count());

            // 一次遍历完成过滤
            foreach (var cell in cells)
            {
                if (cell?.location != null)
                {
                    result.Add(cell);
                }
            }

            // 一次性排序，避免多次LINQ操作
            result.Sort((c1, c2) =>
            {
                // 按页面索引排序
                int pageCompare = c1.PageIndex.CompareTo(c2.PageIndex);
                if (pageCompare != 0) return pageCompare;

                // 按行分组（20像素容差）
                int rowCompare = (c1.location.top / 20).CompareTo(c2.location.top / 20);
                if (rowCompare != 0) return rowCompare;

                // 按列位置从左到右
                return c1.location.left.CompareTo(c2.location.left);
            });

            return result;
        }

        /// <summary>
        /// 处理单个文本块的匹配，选择最佳匹配结果并更新占用区域
        /// </summary>
        private void ProcessCellMatching(TextCellInfo cell, List<MatchResult> candidateMatches, List<Tuple<int, int>> occupiedRegions)
        {
            // 过滤掉已被占用区域的匹配结果
            var availableMatches = candidateMatches
                .Where(m => !IsRegionOccupied(m.StartIndex, m.EndIndex, occupiedRegions))
                .ToList();

            if (!availableMatches.Any()) return;

            var maxConfidence = Math.Round(availableMatches.OrderByDescending(m => m.Confidence).Take(3).Average(p => p.Confidence), 2);
            // 选择最佳匹配：优先选择置信度最高的，置信度相同时选择位置靠后的
            var bestMatchForCell = availableMatches
                                    .Where(m => Math.Round(m.Confidence, 2) >= maxConfidence)
                                    .OrderBy(m => m.StartIndex)
                                    .First();

            // 记录映射关系
            textPositionMap[cell] = new TextPosition
            {
                StartIndex = bestMatchForCell.StartIndex,
                Length = bestMatchForCell.MatchedText.Length
            };

            // 标记区域为已占用，防止后续文本块重叠映射
            occupiedRegions.Add(new Tuple<int, int>(bestMatchForCell.StartIndex, bestMatchForCell.EndIndex));
            // 保持已占用区域列表的排序状态，提高重叠检测效率
            occupiedRegions.Sort((r1, r2) => r1.Item1.CompareTo(r2.Item1));
        }

        /// <summary>
        /// TextCellInfo的位置比较器，专门用于基于位置的相等性判断
        /// 
        /// 设计原理：
        /// - 仅根据文本块的位置信息进行比较，忽略文本内容差异
        /// - 适用于字典key、集合去重、位置查找等场景
        /// - 允许一定的位置误差，适应OCR识别的位置偏差
        /// 
        /// 应用场景：
        /// - Dictionary<TextCellInfo, TextPosition>的key比较
        /// - 文本块去重（相同位置的重复文本块）
        /// - 位置相近的文本块合并判断
        /// </summary>
        public class TextCellInfoComparer : IEqualityComparer<TextCellInfo>
        {
            /// <summary>
            /// 允许的最大位置误差，单位为像素
            /// 说明：OCR识别可能存在1-3像素的位置偏差，此值用于容错处理
            /// 调整建议：根据OCR精度和文档分辨率适当调整，一般在1-5像素之间
            /// </summary>
            private const double MAX_POSITION_DELTA = 3.0;

            /// <summary>
            /// 判断两个文本块是否位置相等
            /// 
            /// 比较逻辑：
            /// 1. 引用相等性检查（性能优化）
            /// 2. 空值检查
            /// 3. 页面索引必须完全相同
            /// 4. 基于文本块中心点的位置比较，允许MAX_POSITION_DELTA像素误差
            /// 
            /// 中心点比较的优势：
            /// - 相比左上角坐标，中心点更能代表文本块的实际位置
            /// - 对文本块大小差异有更好的容错性
            /// - 适应OCR识别边界的微小偏差
            /// </summary>
            /// <param name="x">第一个文本块</param>
            /// <param name="y">第二个文本块</param>
            /// <returns>如果两个文本块位置相等（在误差范围内）则返回true</returns>
            public bool Equals(TextCellInfo x, TextCellInfo y)
            {
                // 引用相等性检查：同一对象直接返回true
                if (ReferenceEquals(x, y)) return true;

                // 空值检查：任一为空则不相等
                if (x == null || y == null) return false;

                // 页面索引检查：不同页面的文本块不相等
                if (x.PageIndex != y.PageIndex) return false;

                // 位置信息检查
                if (x.location != null && y.location != null)
                {
                    // 计算两个文本块的中心点坐标
                    double xCenter = x.location.left + x.location.width / 2;
                    double yCenter = x.location.top + x.location.height / 2;
                    double xCenter2 = y.location.left + y.location.width / 2;
                    double yCenter2 = y.location.top + y.location.height / 2;

                    // 计算中心点距离
                    double dx = Math.Abs(xCenter - xCenter2);
                    double dy = Math.Abs(yCenter - yCenter2);

                    // 在允许误差范围内则认为位置相等
                    return dx < MAX_POSITION_DELTA && dy < MAX_POSITION_DELTA;
                }

                // 位置信息缺失则不相等
                return false;
            }

            /// <summary>
            /// 计算文本块的哈希码，用于字典和哈希表的快速查找
            /// 
            /// 哈希算法设计：
            /// 1. 基于页面索引和位置网格的组合哈希
            /// 2. 使用网格化处理：将连续的位置坐标映射到离散的网格
            /// 3. 确保位置相近的文本块具有相同的哈希码
            /// 
            /// 网格化的作用：
            /// - 将MAX_POSITION_DELTA范围内的位置映射到同一网格
            /// - 保证Equals返回true的对象具有相同的哈希码
            /// - 提高哈希表的查找效率
            /// 
            /// 哈希冲突处理：
            /// - 使用质数乘法（17, 23）减少哈希冲突
            /// - 组合多个因子（页面、X坐标、Y坐标）提高分布均匀性
            /// </summary>
            /// <param name="obj">要计算哈希码的文本块</param>
            /// <returns>文本块的哈希码</returns>
            public int GetHashCode(TextCellInfo obj)
            {
                if (obj == null) return 0;

                // 使用质数进行哈希计算，减少冲突
                int hash = 17;

                // 页面索引参与哈希计算
                hash = hash * 23 + obj.PageIndex.GetHashCode();

                if (obj.location != null)
                {
                    // 计算文本块中心点
                    double centerX = obj.location.left + obj.location.width / 2;
                    double centerY = obj.location.top + obj.location.height / 2;

                    // 网格化处理：将连续坐标映射到离散网格
                    // 同一网格内的位置将产生相同的哈希码
                    int gridX = (int)(centerX / MAX_POSITION_DELTA);
                    int gridY = (int)(centerY / MAX_POSITION_DELTA);

                    // 组合网格坐标到哈希码中
                    hash = hash * 23 + gridX.GetHashCode();
                    hash = hash * 23 + gridY.GetHashCode();
                }

                return hash;
            }
        }

        /// <summary>
        /// 核心文本匹配算法：尝试从指定位置开始匹配文本
        /// 
        /// 算法特点：
        /// 1. 容错匹配：允许一定数量的字符不匹配
        /// 2. 空白字符忽略：跳过完整文本中的空白字符
        /// 3. 标点符号灵活处理：支持标点符号的增删
        /// 4. 大小写不敏感：统一转换为小写进行比较
        /// 
        /// 匹配策略：
        /// - 非标点字符：必须严格匹配（仅允许大小写差异）
        /// - 标点符号：支持OCR文本和完整文本的标点差异
        /// - 空白字符：完整文本中的空白字符被跳过
        /// - 严格机制：不允许任何非标点字符不匹配
        /// 
        /// 性能考虑：
        /// - 单次匹配的时间复杂度：O(n+m)，n为完整文本长度，m为清理文本长度
        /// - 提前终止：超过容错阈值时立即停止匹配
        /// </summary>
        /// <param name="fullText">完整的目标文本</param>
        /// <param name="startIndexInFullText">在完整文本中开始匹配的位置</param>
        /// <param name="textData">预处理的文本数据</param>
        /// <returns>匹配成功时返回MatchResult对象，失败时返回null</returns>
        private MatchResult TryMatchTextAt(string fullText, int startIndexInFullText, CellTextData textData)
        {
            string cleanedText = textData.CleanedForMatching;
            string lowerCaseText = textData.LowerCaseText;

            if (string.IsNullOrEmpty(cleanedText)) return null;

            // 双指针匹配算法
            int j = startIndexInFullText; // 完整文本的指针
            int k = 0;                    // 清理文本的指针
            int firstMatchCharIndexInFullText = -1;  // 第一个匹配字符在完整文本中的位置
            int matchedCharsCount = 0;               // 已匹配的字符数量
            int mismatchedNonPunctuationChars = 0;   // 不匹配的非标点字符数量

            // 严格匹配：除了标点和空格外，非标点字符必须完全匹配
            // 不再允许任何非标点字符的不匹配
            int maxMismatchedCharsAllowed = 0;

            // 双指针匹配主循环
            while (j < fullText.Length && k < cleanedText.Length)
            {
                char ftChar = fullText[j];    // 完整文本当前字符
                char clChar = cleanedText[k]; // 清理文本当前字符

                // 处理完整文本中的空白字符
                if (char.IsWhiteSpace(ftChar))
                {
                    // 只有在开始匹配后才跳过空白字符
                    // 避免在匹配开始前跳过前导空白，确保起始位置准确
                    if (firstMatchCharIndexInFullText != -1)
                    {
                        // 跳过匹配过程中的空白字符
                    }
                    j++;
                    continue;
                }
                // 注意：不跳过匹配开始前的前导空白字符
                // 这由外层循环的不同起始位置来处理

                // 记录第一个匹配字符的位置
                if (firstMatchCharIndexInFullText == -1)
                {
                    firstMatchCharIndexInFullText = j; // 标记实际匹配段在完整文本中的起始位置
                }

                // 字符类型判断：区分标点符号和普通字符
                // 使用扩展的标点符号判断，包含波浪号等字符
                bool ftIsPunctuation = IsPunctuationExtended(ftChar);
                bool clIsPunctuation = IsPunctuationExtended(clChar);

                // 字符匹配逻辑：根据字符类型采用不同的匹配策略
                if (ftIsPunctuation && clIsPunctuation)
                {
                    // 情况1：两个都是标点符号
                    // 标点符号类型匹配即可，不要求完全相同
                    j++;
                    k++;
                    matchedCharsCount++;
                }
                else if (!ftIsPunctuation && !clIsPunctuation)
                {
                    // 情况2：两个都是字母或数字等非标点字符
                    // 使用预处理的小写文本进行比较，避免重复转换
                    char lowerClChar = lowerCaseText[k];
                    if (char.ToLower(ftChar) == lowerClChar)
                    {
                        // 字符匹配（忽略大小写）
                        j++;
                        k++;
                        matchedCharsCount++;
                    }
                    else
                    {
                        // 非标点字符不匹配：记录不匹配数量
                        mismatchedNonPunctuationChars++;
                        // 由于maxMismatchedCharsAllowed = 0，任何非标点字符不匹配将立即终止匹配
                        break; // 严格匹配模式，立即终止
                    }
                }
                else if (clIsPunctuation && !ftIsPunctuation)
                {
                    // 情况3：清理文本有标点，完整文本是字母/数字
                    // OCR可能识别出多余的标点符号（如"word." vs "word"）
                    // 跳过清理文本中的标点符号
                    k++;
                    // j不前进，完整文本字符将与下一个清理文本字符比较
                }
                else if (ftIsPunctuation && !clIsPunctuation)
                {
                    // 情况4：完整文本是标点，清理文本不是标点
                    // 跳过完整文本中的标点符号
                    j++;
                }
                else
                {
                    // 理论上不应该到达这里，但为了安全起见
                    break;
                }
            }

            // 匹配结果评估：判断是否构成有效匹配
            if (k > 0 && firstMatchCharIndexInFullText != -1) // 确保至少处理了一个清理文本字符
            {
                // 严格匹配判断条件：
                // 检查是否处理完了所有非标点字符，允许末尾有未处理的标点
                bool allNonPunctProcessed = true;

                // 检查从当前k位置开始往后是否还有未处理的非标点字符
                for (int i = k; i < cleanedText.Length; i++)
                {
                    if (!IsPunctuationExtended(cleanedText[i]))
                    {
                        allNonPunctProcessed = false;
                        break;
                    }
                }

                // 如果所有非标点字符都已处理，则认为是有效匹配
                if (allNonPunctProcessed)
                {
                    // 只在匹配成功时才创建MatchResult对象
                    return new MatchResult
                    {
                        IsMatch = true,
                        StartIndex = firstMatchCharIndexInFullText,
                        EndIndex = j, // 结束索引（不包含）
                        MismatchedNonPunctuationChars = mismatchedNonPunctuationChars
                    };
                }
            }

            // 匹配失败，返回null避免创建不必要的对象
            return null;
        }

        /// <summary>
        /// 计算匹配置信度：综合评估文本匹配的质量
        ///
        /// 置信度算法组成：
        /// 1. LCS相似度（权重60%）：基于最长公共子序列的文本相似度
        /// 2. 字符匹配率（权重25%）：基于匹配过程中的字符不匹配率
        /// 3. 长度相似度（权重15%）：原始文本与匹配文本的长度相似性
        ///
        /// 算法优势：
        /// - 多维度评估：不仅考虑字符匹配，还考虑结构相似性
        /// - 权重平衡：LCS作为主要指标，其他指标作为辅助
        /// - 鲁棒性强：对OCR错误、格式差异有较好的容错性
        ///
        /// 置信度范围：[0.0, 1.0]
        /// - 0.8-1.0：高质量匹配，可直接使用
        /// - 0.5-0.8：中等质量匹配，可能需要人工确认
        /// - 0.3-0.5：低质量匹配，建议谨慎使用
        /// - 0.0-0.3：极低质量匹配，通常应该丢弃
        /// </summary>
        /// <param name="originalCellText">原始OCR文本块内容</param>
        /// <param name="matchedTextInFullText">在完整文本中匹配到的文本片段</param>
        /// <param name="mismatchedNonPunctuationCharsInMatchLogic">匹配过程中不匹配的非标点字符数量</param>
        /// <param name="lengthOfCleanedCellTextForMatchLogic">用于匹配的清理文本长度</param>
        /// <param name="preCleanedOriginalText">预处理的原始文本（可选，用于避免重复清理）</param>
        /// <returns>匹配置信度，范围[0.0, 1.0]</returns>
        private double CalculateMatchConfidence(string originalCellText, string matchedTextInFullText, int mismatchedNonPunctuationCharsInMatchLogic, int lengthOfCleanedCellTextForMatchLogic, string preCleanedOriginalText = null)
        {
            // 使用预处理的文本或现场清理
            var cleanedMatched = RemoveWhitespace(matchedTextInFullText);
            var cleanedOriginal = preCleanedOriginalText ?? RemoveWhitespace(originalCellText);
            // 边界条件检查
            if (string.IsNullOrEmpty(cleanedOriginal) || string.IsNullOrEmpty(cleanedMatched))
            {
                return 0.0; // 任一文本为空，置信度为0
            }

            // 清理文本为空但原始文本不为空的情况（如文本块只包含空格）
            if (lengthOfCleanedCellTextForMatchLogic == 0 && cleanedOriginal.Length > 0)
            {
                return 0.0; // 无法匹配纯空白文本块
            }

            // 特殊情况：两个文本都为空
            if (cleanedOriginal.Length == 0 && cleanedMatched.Length == 0)
                return 1.0; // 完美匹配

            // 一个为空，另一个不为空
            if (cleanedOriginal.Length == 0 || cleanedMatched.Length == 0)
                return 0.0; // 无法匹配

            // 1. LCS相似度计算（主要指标，权重60%）
            // 使用最长公共子序列算法比较原始文本和匹配文本的结构相似性
            int lcsLength = LongestCommonSubsequenceLength(cleanedOriginal, cleanedMatched);
            double lcsRatio = (double)lcsLength / Math.Max(cleanedOriginal.Length, cleanedMatched.Length);

            // 2. 字符匹配相似度（辅助指标，权重25%）
            // 基于匹配过程中的非标点字符不匹配率计算
            double mismatchSimilarity = 1.0;
            if (lengthOfCleanedCellTextForMatchLogic > 0)
            {
                // 计算匹配过程中的字符准确率
                // 在严格匹配模式下，mismatchedNonPunctuationCharsInMatchLogic应为0
                // 但为了鲁棒性，仍然保留计算逻辑
                mismatchSimilarity = 1.0 - ((double)mismatchedNonPunctuationCharsInMatchLogic / lengthOfCleanedCellTextForMatchLogic);

                // 如果有任何非标点字符不匹配，置信度直接设为0
                // 这是对严格匹配模式的保障措施
                if (mismatchedNonPunctuationCharsInMatchLogic > 0)
                {
                    return 0.0; // 严格模式下任何非标点字符不匹配都不被接受
                }
            }
            else if (mismatchedNonPunctuationCharsInMatchLogic > 0)
            {
                // 异常情况：有不匹配字符但清理文本长度为0
                mismatchSimilarity = 0.0;
                return 0.0; // 严格模式下直接返回0置信度
            }

            // 3. 长度相似度（辅助指标，权重15%）
            // 比较原始文本和匹配文本的长度差异
            double lengthDiff = Math.Abs(cleanedOriginal.Length - cleanedMatched.Length);
            double maxLength = Math.Max(cleanedOriginal.Length, cleanedMatched.Length);
            double overallLengthSimilarity = (maxLength == 0) ? 1.0 : 1.0 - (lengthDiff / maxLength);

            // 综合置信度计算：加权平均
            // 权重分配原理：
            // - LCS（60%）：反映文本的整体结构相似性，是最重要的指标
            // - 字符匹配率（25%）：反映匹配过程的准确性，重要的质量指标
            // - 长度相似度（15%）：确保匹配文本长度合理，防止过长或过短的匹配
            double confidence = (lcsRatio * 0.6) + (mismatchSimilarity * 0.25) + (overallLengthSimilarity * 0.15);

            // 确保置信度在有效范围内[0.0, 1.0]
            return Math.Max(0.0, Math.Min(1.0, confidence));
        }

        /// <summary>
        /// 检查指定区域是否与已占用区域重叠
        /// 
        /// 重叠检测算法：
        /// - 对于两个区间[a1, a2)和[b1, b2)，重叠条件为：a1 < b2 && a2 > b1
        /// - 使用排序的已占用区域列表提高检测效率
        /// 
        /// 应用场景：
        /// - 防止多个文本块映射到同一文本区域
        /// - 确保文本映射的唯一性和准确性
        /// - 避免文本块重叠导致的显示问题
        /// 
        /// 性能优化：
        /// - 已占用区域列表保持排序状态
        /// - 可进一步优化为二分查找（当区域数量很大时）
        /// </summary>
        /// <param name="start">新区域的起始位置（包含）</param>
        /// <param name="end">新区域的结束位置（不包含）</param>
        /// <param name="occupiedRegions">已占用区域列表，每个元组表示(起始位置, 结束位置)</param>
        /// <returns>如果新区域与任何已占用区域重叠则返回true</returns>
        private bool IsRegionOccupied(int start, int end, List<Tuple<int, int>> occupiedRegions)
        {
            if (start >= end) return false;

            // 遍历所有已占用区域，检查是否存在重叠
            foreach (var region in occupiedRegions)
            {
                // 重叠检测公式：new_start < existing_end && new_end > existing_start
                if (start < region.Item2 && end > region.Item1)
                {
                    // 获取重叠区域
                    int overlapStart = Math.Max(start, region.Item1);
                    int overlapEnd = Math.Min(end, region.Item2);

                    // 检查重叠区域是否仅含标点或空格
                    bool isOnlyPunctOrSpace = true;
                    for (int i = overlapStart; i < overlapEnd; i++)
                    {
                        if (i < fullText.Length && !char.IsPunctuation(fullText[i]) && !char.IsWhiteSpace(fullText[i]))
                        {
                            isOnlyPunctOrSpace = false;
                            break;
                        }
                    }

                    // 严格模式：如果重叠区域只包含标点或空格，才允许重叠
                    if (isOnlyPunctOrSpace)
                    {
                        continue; // 仅标点空格重叠，允许
                    }

                    return true; // 重叠区域包含实质内容
                }
            }

            return false; // 没有重叠
        }

        /// <summary>
        /// 计算两个字符串的最长公共子序列（LCS）长度
        /// 
        /// 算法说明：
        /// - 使用动态规划算法，时间复杂度O(m*n)，空间复杂度O(m*n)
        /// - LCS不要求字符连续，但要求保持相对顺序
        /// - 适用于评估两个文本的结构相似性
        /// 
        /// 应用优势：
        /// - 对字符插入、删除、替换有很好的容错性
        /// - 能够识别文本的核心相似部分
        /// - 不受局部差异影响，关注整体结构
        /// 
        /// 示例：
        /// - LCS("ABCDGH", "AEDFHR") = 3 ("ADH")
        /// - LCS("AGGTAB", "GXTXAYB") = 4 ("GTAB")
        /// </summary>
        /// <param name="s1">第一个字符串</param>
        /// <param name="s2">第二个字符串</param>
        /// <returns>最长公共子序列的长度</returns>
        private static int LongestCommonSubsequenceLength(string s1, string s2)
        {
            // 边界条件：任一字符串为空，LCS长度为0
            if (string.IsNullOrEmpty(s1) || string.IsNullOrEmpty(s2))
                return 0;

            // 创建动态规划表：dp[i,j]表示s1前i个字符和s2前j个字符的LCS长度
            int[,] dp = new int[s1.Length + 1, s2.Length + 1];

            // 填充动态规划表
            for (int i = 0; i <= s1.Length; i++)
            {
                for (int j = 0; j <= s2.Length; j++)
                {
                    if (i == 0 || j == 0)
                    {
                        // 边界情况：空字符串的LCS长度为0
                        dp[i, j] = 0;
                    }
                    else if (s1[i - 1] == s2[j - 1])
                    {
                        // 字符匹配：LCS长度在前一状态基础上+1
                        dp[i, j] = dp[i - 1, j - 1] + 1;
                    }
                    else
                    {
                        // 字符不匹配：取两个子问题的最大值
                        // dp[i-1,j]：忽略s1当前字符
                        // dp[i,j-1]：忽略s2当前字符
                        dp[i, j] = Math.Max(dp[i - 1, j], dp[i, j - 1]);
                    }
                }
            }

            // 返回完整字符串的LCS长度
            return dp[s1.Length, s2.Length];
        }
    }
}