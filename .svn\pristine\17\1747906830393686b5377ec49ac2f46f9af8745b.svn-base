﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Reflection;
using System.Security.Permissions;
using System.Web;
using System.Windows.Forms;

namespace OCRTools
{
    [PermissionSet(SecurityAction.Demand, Name = "FullTrust")]
    public partial class FrmGoBuy : MetroForm
    {
        private ChargeViewToUser _nowSelectedChargeType;

        private UserType _nowSelectedType;

        public UserTypeInfo NextUserType;

        public FrmGoBuy()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;

            CommonMethod.SetStyle(linkLabel1, ControlStyles.Selectable, false);
        }

        private void btnContactQQ_Click(object sender, EventArgs e)
        {
            CommonMethod.OpenKeFuQ();
        }

        private void btnOpenVip_Click(object sender, EventArgs e)
        {
            if (Program.NowUser == null || string.IsNullOrEmpty(Program.NowUser.Account))
            {
                CommonMethod.ShowHelpMsg("当前用户未登录，请登录后重试！");
                return;
            }

            if (!CommonString.IsOnLine)
            {
                CommonMethod.ShowHelpMsg("当前网络异常，请等待网络恢复后重试！");
                return;
            }

            if (_nowSelectedType == null)
            {
                CommonMethod.ShowHelpMsg("请选择要升级的账户类型后重试！");
                return;
            }

            //if (NowSelectedType.Type == Program.NowUser?.UserType)
            //{
            //    CommonMethod.ShowHelpMsg(string.Format("当前已经是{0}，祝您使用愉快！", NowSelectedType.Type.ToString()));
            //    return;
            //}
            //http://t.cn/AimZvjI8
            Top -= (785 - Height) / 2;
            Height = 785;

            var url = CommonString.HostAccount?.FullUrl + "code.aspx?op=pay&remark=" +
                      HttpUtility.UrlEncode(_nowSelectedChargeType?.Name +
                                            (_nowSelectedType.Name ?? _nowSelectedType.Type.ToString()))
                      + "&account=" + Program.NowUser.Account;

            var html = "";
            for (int i = 0; i < 5; i++)
            {
                html = WebClientExt.GetHtml(url, 30);
                if (html.Trim().StartsWith("http"))
                {
                    break;
                }
                System.Threading.Thread.Sleep(500);
            }

            pnlMain.Controls.Clear();
            if (html.Trim().StartsWith("http"))
            {
                var dicCheck = new Dictionary<string, string>();
                dicCheck.Add("minute_show", "0分");
                CommonMethod.LoadHtml(pnlMain, new Point(0, 0), Size, html.Trim(), dicCheck);
            }
            else
            {
                CommonMethod.LoadHtml(pnlMain, new Point(0, 0), Size, CommonString.StrServerHostUrl + "pay.png");
            }

            CommonMethod.ShowHelpMsg("支付完成后，如未自动开通，请发送付款截图给客服，祝您使用愉快！", 10000);
        }

        private void FrmGoBuy_Load(object sender, EventArgs e)
        {
            // LoadInfo
            CommonUser.GetUserTypes(true)?.ForEach(p =>
            {
                var radioButton = new RadioButton
                {
                    Text = p.Name ?? p.Type.ToString(),
                    Tag = p,
                    AutoSize = true,
                    Font = new Font(btnOpenVip.Font.FontFamily, 13, GraphicsUnit.Pixel),
                    TabStop = false
                };
                CommonMethod.SetStyle(radioButton, ControlStyles.Selectable, false);
                radioButton.CheckedChanged += RadioButton_CheckedChanged;
                if (Equals(p.Type, NextUserType?.Code)) radioButton.Checked = true;
                pnlUserType.Controls.Add(radioButton);
            });
            if (pnlPayType.Controls.Count <= 0 && pnlUserType.Controls.Count > 0)
            {
                (pnlUserType.Controls[0] as RadioButton).Checked = true;
            }

            if (pnlUserType.Controls.Count > 3)
            {
                Width += (pnlUserType.Controls.Count - 3) * 50;
            }

            CommonMsg.ShowToWindow(this, new Point(136, 21));
            var url = CommonString.HostAccount?.FullUrl + "/desc.aspx?t=" + ServerTime.DateTime.Ticks;
            CommonMethod.LoadHtml(pnlMain, new Point(7, 88), new Size(535, 372), url);
        }

        private void RadioButton_CheckedChanged(object sender, EventArgs e)
        {
            if (sender == null) return;
            _nowSelectedType = (sender as RadioButton)?.Tag as UserType;
            pnlPayType.Controls.Clear();
            _nowSelectedChargeType = null;
            _nowSelectedType?.UserChargeType?.ForEach(p =>
            {
                var radio = new RadioButton
                {
                    Text = p.Name,
                    AutoSize = true,
                    Tag = p,
                    Font = new Font(btnOpenVip.Font.FontFamily, 12, GraphicsUnit.Pixel),
                    TabStop = false
                };
                CommonMethod.SetStyle(radio, ControlStyles.Selectable, false);
                radio.CheckedChanged += rdoByYear_CheckedChanged;
                if (!string.IsNullOrEmpty(p.Tag))
                {
                    radio.TextImageRelation = TextImageRelation.TextBeforeImage;
                    try
                    {
                        radio.Image = Resources.ResourceManager.GetObject(p.Tag) as Bitmap;
                    }
                    catch { }
                }

                pnlPayType.Controls.Add(radio);
                if (p.IsDefault) radio.Checked = true;
            });
        }

        private void rdoByYear_CheckedChanged(object sender, EventArgs e)
        {
            var rdo = sender as RadioButton;
            if (rdo == null || !rdo.Checked)
                return;
            _nowSelectedChargeType = rdo.Tag as ChargeViewToUser;
            btnOpenVip.Text = _nowSelectedChargeType?.Desc ?? "-";
            btnOpenVip.Image = CommonUser.GetUserLevelImage(_nowSelectedType.Type);
        }
    }

    [Obfuscation]
    public class UserType
    {
        [Obfuscation] public int Type { get; set; }

        [Obfuscation] public string Name { get; set; }

        [Obfuscation] public List<ChargeViewToUser> UserChargeType { get; set; }
    }

    [Obfuscation]
    public class ChargeViewToUser
    {
        [Obfuscation] public string Name { get; set; }

        [Obfuscation] public string Desc { get; set; }

        [Obfuscation] public double Price { get; set; }

        [Obfuscation] public bool IsDefault { get; set; }

        [Obfuscation] public string Tag { get; set; }
    }

    [Obfuscation]
    public class UserTypeInfo
    {
        public string Name { get; set; }

        public int Code { get; set; }
    }
}