// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class SelectionPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = SelectionPatternIdentifiers.Pattern;

        public static readonly AutomationProperty CanSelectMultipleProperty =
            SelectionPatternIdentifiers.CanSelectMultipleProperty;

        public static readonly AutomationEvent InvalidatedEvent = SelectionPatternIdentifiers.InvalidatedEvent;

        public static readonly AutomationProperty IsSelectionRequiredProperty =
            SelectionPatternIdentifiers.IsSelectionRequiredProperty;

        public static readonly AutomationProperty SelectionProperty = SelectionPatternIdentifiers.SelectionProperty;

        private IUIAutomationSelectionPattern _pattern;


        private SelectionPattern(AutomationElement el, IUIAutomationSelectionPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new SelectionPattern(el, (IUIAutomationSelectionPattern) pattern, cached);
        }
    }

    public class SelectionItemPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = SelectionItemPatternIdentifiers.Pattern;

        public static readonly AutomationEvent ElementAddedToSelectionEvent =
            SelectionItemPatternIdentifiers.ElementAddedToSelectionEvent;

        public static readonly AutomationEvent ElementRemovedFromSelectionEvent =
            SelectionItemPatternIdentifiers.ElementRemovedFromSelectionEvent;

        public static readonly AutomationEvent ElementSelectedEvent =
            SelectionItemPatternIdentifiers.ElementSelectedEvent;

        public static readonly AutomationProperty IsSelectedProperty =
            SelectionItemPatternIdentifiers.IsSelectedProperty;

        public static readonly AutomationProperty SelectionContainerProperty =
            SelectionItemPatternIdentifiers.SelectionContainerProperty;

        private IUIAutomationSelectionItemPattern _pattern;

        private SelectionItemPattern(AutomationElement el, IUIAutomationSelectionItemPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new SelectionItemPattern(el, (IUIAutomationSelectionItemPattern) pattern, cached);
        }
    }
}