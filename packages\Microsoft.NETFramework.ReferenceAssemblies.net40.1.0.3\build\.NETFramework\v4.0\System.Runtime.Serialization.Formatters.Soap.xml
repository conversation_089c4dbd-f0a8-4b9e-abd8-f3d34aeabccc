﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Serialization.Formatters.Soap</name>
  </assembly>
  <members>
    <member name="T:System.Runtime.Serialization.Formatters.Soap.SoapFormatter">
      <summary>Serializes and deserializes an object, or an entire graph of connected objects, in SOAP format.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.Formatters.Soap.SoapFormatter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Formatters.Soap.SoapFormatter" /> class with default property values.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.Formatters.Soap.SoapFormatter.#ctor(System.Runtime.Serialization.ISurrogateSelector,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Formatters.Soap.SoapFormatter" /> class with the specified <see cref="T:System.Runtime.Serialization.ISurrogateSelector" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />.</summary>
      <param name="selector">The <see cref="T:System.Runtime.Serialization.ISurrogateSelector" /> to use with the new instance of <see cref="T:System.Runtime.Serialization.Formatters.Soap.SoapFormatter" />. Can be null. </param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that holds the source and destination of the serialization. If the <paramref name="context" /> parameter is null, then the <see cref="P:System.Runtime.Serialization.Formatters.Soap.SoapFormatter.Context" /> defaults to <see cref="F:System.Runtime.Serialization.StreamingContextStates.CrossMachine" />. </param>
    </member>
    <member name="P:System.Runtime.Serialization.Formatters.Soap.SoapFormatter.AssemblyFormat">
      <summary>Gets or sets the behavior of the deserializer with regards to finding and loading assemblies.</summary>
      <returns>One of the <see cref="T:System.Runtime.Serialization.Formatters.FormatterAssemblyStyle" /> values that specifies the deserializer behavior.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Formatters.Soap.SoapFormatter.Binder">
      <summary>Gets or sets the <see cref="T:System.Runtime.Serialization.SerializationBinder" /> that controls the binding of a serialized object to a type.</summary>
      <returns>The <see cref="T:System.Runtime.Serialization.SerializationBinder" /> used with this <see cref="T:System.Runtime.Serialization.Formatters.Soap.SoapFormatter" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Formatters.Soap.SoapFormatter.Context">
      <summary>Gets or sets the <see cref="T:System.Runtime.Serialization.StreamingContext" /> used with this <see cref="T:System.Runtime.Serialization.Formatters.Soap.SoapFormatter" />.</summary>
      <returns>The <see cref="T:System.Runtime.Serialization.StreamingContext" /> used with this <see cref="T:System.Runtime.Serialization.Formatters.Soap.SoapFormatter" />.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Formatters.Soap.SoapFormatter.Deserialize(System.IO.Stream)">
      <summary>Deserializes the data on the provided stream and reconstitutes the graph of objects.</summary>
      <returns>The top object of the deserialized graph (root).</returns>
      <param name="serializationStream">The stream that contains the data to deserialize. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serializationStream" /> is null. </exception>
    </member>
    <member name="M:System.Runtime.Serialization.Formatters.Soap.SoapFormatter.Deserialize(System.IO.Stream,System.Runtime.Remoting.Messaging.HeaderHandler)">
      <summary>Deserializes the stream into an object graph with any headers in that stream being handled by the given <see cref="T:System.Runtime.Remoting.Messaging.HeaderHandler" />.</summary>
      <returns>The top object of the deserialized graph (root).</returns>
      <param name="serializationStream">The stream that contains the data to deserialize.</param>
      <param name="handler">Delegate to handle any headers found on the stream. Can be null. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serializationStream" /> is null. </exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">
        <paramref name="serializationStream" /> supports seeking, and its length is 0. </exception>
    </member>
    <member name="P:System.Runtime.Serialization.Formatters.Soap.SoapFormatter.FilterLevel">
      <summary>Gets or sets the <see cref="T:System.Runtime.Serialization.Formatters.TypeFilterLevel" /> of automatic deserialization for .NET Framework remoting.</summary>
      <returns>The <see cref="T:System.Runtime.Serialization.Formatters.TypeFilterLevel" /> that represents the current automatic deserialization level.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Formatters.Soap.SoapFormatter.Serialize(System.IO.Stream,System.Object)">
      <summary>Serializes an object or graph of objects with the specified root to the given <see cref="T:System.IO.Stream" />.</summary>
      <param name="serializationStream">The stream onto which the formatter puts the data to serialize. </param>
      <param name="graph">The object, or root of the object graph, to serialize. All child objects of this root object are automatically serialized. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serializationStream" /> is null. </exception>
    </member>
    <member name="M:System.Runtime.Serialization.Formatters.Soap.SoapFormatter.Serialize(System.IO.Stream,System.Object,System.Runtime.Remoting.Messaging.Header[])">
      <summary>Serializes an object or graph of objects with the specified root to the given <see cref="T:System.IO.Stream" /> in the SOAP Remote Procedure Call (RPC) format.</summary>
      <param name="serializationStream">The stream onto which the formatter puts the data to serialize. </param>
      <param name="graph">The object or root of the object graph to serialize. All child objects of this root object are automatically serialized. </param>
      <param name="headers">Remoting headers to include in the serialization. Can be null. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serializationStream" /> is null. </exception>
    </member>
    <member name="P:System.Runtime.Serialization.Formatters.Soap.SoapFormatter.SurrogateSelector">
      <summary>Gets or sets the <see cref="T:System.Runtime.Serialization.SurrogateSelector" /> that controls type substitution during serialization and deserialization.</summary>
      <returns>The <see cref="T:System.Runtime.Serialization.SurrogateSelector" /> used with this <see cref="T:System.Runtime.Serialization.Formatters.Soap.SoapFormatter" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Formatters.Soap.SoapFormatter.TopObject">
      <summary>Gets or sets the <see cref="T:System.Runtime.Serialization.Formatters.ISoapMessage" /> into which the SOAP top object is deserialized.</summary>
      <returns>The <see cref="T:System.Runtime.Serialization.Formatters.ISoapMessage" /> into which the SOAP top object is deserialized.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Formatters.Soap.SoapFormatter.TypeFormat">
      <summary>Gets or sets the format in which type descriptions are laid out in the serialized stream.</summary>
      <returns>The format in which type descriptions are laid out in the serialized stream.</returns>
    </member>
  </members>
</doc>