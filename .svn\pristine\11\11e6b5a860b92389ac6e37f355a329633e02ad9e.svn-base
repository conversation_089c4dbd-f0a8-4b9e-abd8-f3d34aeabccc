﻿using System;
using System.Drawing;
using System.Windows.Forms;
using MetroFramework.Forms;

namespace OCRTools
{
    public partial class FrmImageView : MetroForm
    {
        private const int CONTROL_WIDTH = 45;
        private const int CONTROL_HEIGHT = 85;

        public FrmImageView()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            imageBox.HideButton();
            DoubleBuffered = true;
        }

        public void SetImage(Image image)
        {
            ControlBox = true;
            Padding = new Padding(20, 60, 20, 20);
            StartPosition = FormStartPosition.CenterScreen;
            DisplayHeader = true;
            var width = image.Width + CONTROL_WIDTH;
            var height = image.Height + CONTROL_HEIGHT;
            var minWidth = Screen.PrimaryScreen.WorkingArea.Width * 0.367;
            var minHeight = Screen.PrimaryScreen.WorkingArea.Height * 0.542;
            width = (int) Math.Max(width, minWidth);
            height = (int) Math.Max(height, minHeight);
            Size = new Size(width, height);
            if (width > Screen.PrimaryScreen.WorkingArea.Width
                || height > Screen.PrimaryScreen.WorkingArea.Height)
                WindowState = FormWindowState.Maximized;
            imageBox.SetPicImage(image);
            Location = new Point((Screen.PrimaryScreen.WorkingArea.Width - Width) / 2,
                (Screen.PrimaryScreen.WorkingArea.Height - Height) / 2);
        }

        private void FrmPasteScreen_FormClosed(object sender, FormClosedEventArgs e)
        {
            imageBox.Dispose();
        }

        private void FrmPasteScreen_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape) Close();
        }

        private void cmsShowTool_Click(object sender, EventArgs e)
        {
            var isShow = imageBox.ShowOrHideToolBox();
            cmsShowTool.Text = string.Format("{0}工具栏", isShow ? "隐藏" : "显示");
        }

        private void cmsTop_Click(object sender, EventArgs e)
        {
            TopMost = !TopMost;
            cmsTop.Text = TopMost ? "取消置顶" : "置顶窗体";
        }

        private void 复制图像ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ClipboardService.ClipSetImage(imageBox.OriginImage, false);
            CommonMethod.ShowHelpMsg("贴图已成功复制到剪切板！");
        }

        private void cmsClose_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void cmsOCR_Click(object sender, EventArgs e)
        {
            imageBox.Ocr();
        }

        private void 阴影ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var isCommon = ShadowType == CommonString.CommonShadowType;
            ShadowType = isCommon ? MetroFormShadowType.None : CommonString.CommonShadowType;
            阴影ToolStripMenuItem.Checked = isCommon;
            Invalidate();
        }

        private void cmsSave_Click(object sender, EventArgs e)
        {
            imageBox.SaveFile();
        }
    }
}