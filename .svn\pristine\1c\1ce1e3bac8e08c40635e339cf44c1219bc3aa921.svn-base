﻿using MetroFramework.Forms;
using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Net;
using System.Threading;
using System.Windows.Forms;

#pragma warning disable 1570

namespace OCRTools
{
    public partial class FormUpdate : MetroForm
    {
        private readonly string _tmpCacheFile = Path.GetTempFileName();
        private bool _isCancel;

        private bool _isOpenDownLoad;

        public string AppName { get; set; }

        public string AppPath { get; set; }

        public bool IsUpdateMode { get; set; } = true;

        public bool IsNeedUnZip { get; set; }

        public bool IsCanUserUpdate { get; set; } = true;

        public string StrUpdateMode => IsUpdateMode ? "更新" : "安装";

        internal UpdateEntity UpdateInfo { get; set; }

        public bool IsAutoStart { get; internal set; }

        public bool IsNeedClearFolder { get; set; }

        public bool IsHasUpdate { get; set; }

        public bool IsBeta { get; internal set; }

        public FormUpdate()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            //不执行线程检查
            CheckForIllegalCrossThreadCalls = false;

            CommonMethod.SetStyle(lnkNoUpdate, ControlStyles.Selectable, false);
        }

        public override void OnThemeChange()
        {
            var foreColor = CommonSetting.Get默认文字颜色();
            lblNew.ForeColor = foreColor;
            lblDate.ForeColor = foreColor;
            rtbCon.ForeColor = foreColor;
            lblNowVersion.ForeColor = CommonSetting.夜间模式 ? CommonTheme.ReveseColor(Color.DimGray) : Color.DimGray;
            lblNewDate.ForeColor = Color.DimGray;
        }

        private void FormUpdate_Load(object sender, EventArgs e)
        {
            Text = AppName + (IsBeta ? " Beta版" : "") + (IsHasUpdate ? StrUpdateMode : "-最近更新内容");
            if (UpdateInfo == null) return;
            btnOK.Enabled = true;
            btnOK.Text = "立即" + StrUpdateMode;
            lblNew.Text = UpdateInfo.strNewVersion;
            lblDate.Text = UpdateInfo.dtNewDate.ToString("yyyy-MM-dd");
            rtbCon.Text = UpdateInfo.strContext;
            InitFontSize(5);
            Opacity = 1;
            if (!IsHasUpdate)
            {
                btnOK.Left -= 90;
                btnOK.Width += 20;
                btnOK.Text = "好的，已了解！";
                btnUpdateLater.Visible = false;
                lnkNoUpdate.Visible = false;
            }
            else
            {
                lnkNoUpdate.Visible = IsCanUserUpdate;
                if (UpdateInfo.IsForceUpdate)
                {
                    Text = AppName + "【强制更新】";
                }
                if (IsAutoStart || UpdateInfo.IsForceUpdate)
                {
                    btnOK_Click(sender, null);
                }
            }
        }

        private void InitFontSize(int count)
        {
            if (count <= 0)
            {
                return;
            }

            count--;
            if (rtbCon.Font.Size > 6)
            {
                if (rtbCon.Size.Height < rtbCon.PreferredSize.Height)
                    rtbCon.Font = CommonString.GetSysNormalFont(rtbCon.Font.Size - 1);
            }
            else
            {
                return;
            }

            if (rtbCon.Size.Height < rtbCon.PreferredSize.Height)
                InitFontSize(count);
        }

        private void bgUpdate_DoWork(object sender, DoWorkEventArgs e)
        {
            if (UpdateInfo == null) return;
            try
            {
                _isOpenDownLoad = false;
                try
                {
                    using (new FileStream(_tmpCacheFile, FileMode.Create))
                    {
                    }
                }
                catch
                {
                    _isOpenDownLoad = true;
                    CommonMethod.ShowHelpMsg("无法创建临时文件，开始尝试以管理员方式更新！");
                }

                if (_isOpenDownLoad)
                    proProcess.Value = proProcess.Maximum;
                else
                    DownloadFile(UpdateInfo.strURL, _tmpCacheFile, proProcess, lblProcess);
                if (proProcess.Value == proProcess.Maximum)
                {
                    lblProcess.Text = "下载完成，准备开始" + StrUpdateMode + "！";
                    btnOK.Tag = "open";
                }
                else
                {
                    pnlUpdate.Visible = false;
                    btnOK.Enabled = true;
                    btnOK.Tag = "down";
                    CommonMethod.ShowHelpMsg("下载失败，请稍候重试！");
                }

                bgUpdate.CancelAsync();
            }
            catch { }
        }

        private void BeforeUpdate()
        {
            try
            {
                LocalOcrService.CloseService();
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (!IsHasUpdate)
            {
                Close();
                return;
            }
            if (_isCancel)
                return;
            var tagNow = btnOK.Tag.ToString();
            if (tagNow == "down")
            {
                bgUpdate.RunWorkerAsync();
                btnOK.Enabled = false;
                lblProcess.Visible = true;
                proProcess.Visible = true;
                pnlUpdate.Visible = true;
                pnlUpdate.BringToFront();
                lnkNoUpdate.BringToFront();
            }
            else if (tagNow == "open")
            {
                btnOK.Enabled = false;
                BeforeUpdate();

                if (IsNeedUnZip)
                {
                    if (IsNeedClearFolder && Directory.Exists(AppPath))
                    {
                        Microsoft.VisualBasic.FileIO.FileSystem.DeleteDirectory(AppPath, Microsoft.VisualBasic.FileIO.DeleteDirectoryOption.DeleteAllContents);
                    }
                    DoUnZip();
                    CommonMethod.ShowHelpMsg("恭喜，" + AppName + "已安装成功！");
                    Close();
                }
                else
                {
                    var paramStr = string.Format("\"{0}\" \"{1}\" \"{2}\""
                        , AppPath
                        , _tmpCacheFile
                        , _isOpenDownLoad ? UpdateInfo.strURL : string.Empty
                    );
                    try
                    {
                        var path = Path.GetDirectoryName(AppPath);
                        if (!Directory.Exists(path))
                        {
                            Directory.CreateDirectory(path);
                        }
                    }
                    catch (Exception exception)
                    {
                        Console.WriteLine(exception);
                    }
                    var updateFileName = CommonString.StrUpdateFile();
                    try
                    {
                        CommonString.RunAsAdmin(updateFileName, paramStr, true, true);
                    }
                    catch
                    {
                        MessageBox.Show(this, "无法以管理员方式启动更新程序，可能会更新失败，请知晓！\n正在尝试以非管理员方式启动更新程序……", "提示",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        CommonString.RunAsAdmin(updateFileName, paramStr, false, true);
                    }
                    finally
                    {
                        if (IsUpdateMode)
                            CommonMethod.Exit();
                        else
                            Close();
                    }
                }
            }
        }

        private void DoUnZip()
        {
            lblProcess.Text = "开始解压缩，请稍候…";
            GZip.GZip.Decompress(Path.GetDirectoryName(_tmpCacheFile), AppPath, Path.GetFileName(_tmpCacheFile));
            lblProcess.Text = "恭喜，安装完成！";
        }

        public void DownloadFile(string url, string filename, ProgressBar prog, Label label1)
        {
            try
            {
                var client = new WebClient { Proxy = null };
                client.DownloadProgressChanged += (sender, e) =>
                {
                    if (!IsDisposed)
                    {
                        if (prog != null)
                            prog.Value = e.ProgressPercentage;
                        if (label1 != null)
                            label1.Text = "共" + CommonMethod.FormatBytes(e.TotalBytesToReceive) + ",已下载" + e.ProgressPercentage + "%";
                        Application.DoEvents();
                    }
                };
                if (url.Contains("?"))
                {
                    url += "&t=" + ServerTime.DateTime.Millisecond;
                }
                else
                {
                    url += "?t=" + ServerTime.DateTime.Millisecond;
                }
                client.DownloadFileAsync(new Uri(url), filename);
                while (client.IsBusy) Thread.Sleep(1000);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }

        private void FormUpdate_FormClosing(object sender, FormClosingEventArgs e)
        {
            _isCancel = true;
            bgUpdate.CancelAsync();
            if (UpdateInfo.IsForceUpdate)
                CommonMethod.Exit();
        }

        private void lnkNoUpdate_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var url = string.IsNullOrEmpty(UpdateInfo.strFullURL) ? UpdateInfo.strURL : UpdateInfo.strFullURL;
            if (!string.IsNullOrEmpty(url))
            {
                url += (url.Contains("?") ? "&" : "?") + "t=" + ServerTime.DateTime.Ticks;
                try
                {
                    ClipboardService.SetText(url);
                }
                catch
                {
                }

                MessageBox.Show(this, "已复制下载地址到粘贴板！\n助手将尝试自动用默认浏览器打开网址…\n如果一直没有弹出下载框，请手动粘贴网址到浏览器重试！", "手动更新提示",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                CommonMethod.OpenUrl(url);
            }
        }

        private void bgUpdate_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            if (!btnOK.Enabled)
            {
                proProcess.Value = 100;
                Application.DoEvents();
                btnOK_Click(sender, null);
            }
        }

        private void btnUpdateLater_Click(object sender, EventArgs e)
        {
            CommonUpdate.IsAutoCheckUpdate = false;
            Close();
        }
    }
}