﻿using System.Drawing;

namespace OCRTools
{
    public struct RGBA
    {
        private int _red, _green, _blue, _alpha;

        public int Red
        {
            get => _red;
            set => _red = ColorHelper.ValidColor(value);
        }

        public int Green
        {
            get => _green;
            set => _green = ColorHelper.ValidColor(value);
        }

        public int Blue
        {
            get => _blue;
            set => _blue = ColorHelper.ValidColor(value);
        }

        public int Alpha
        {
            get => _alpha;
            set => _alpha = ColorHelper.ValidColor(value);
        }

        public RGBA(int red, int green, int blue, int alpha = 255) : this()
        {
            Red = red;
            Green = green;
            Blue = blue;
            Alpha = alpha;
        }

        public RGBA(Color color) : this(color.R, color.G, color.B, color.A)
        {
        }

        public static implicit operator RGBA(Color color)
        {
            return new RGBA(color);
        }

        public static implicit operator Color(RGBA color)
        {
            return color.ToColor();
        }

        public static implicit operator HSB(RGBA color)
        {
            return color.ToHsb();
        }

        public static implicit operator <PERSON><PERSON><PERSON>(RGBA color)
        {
            return color.ToCmyk();
        }

        public static bool operator ==(RGBA left, RGBA right)
        {
            return left.Red == right.Red && left.Green == right.Green && left.Blue == right.Blue &&
                   left.Alpha == right.Alpha;
        }

        public static bool operator !=(RGBA left, RGBA right)
        {
            return !(left == right);
        }

        public override string ToString()
        {
            return $"R:{Red},G:{Green},B:{Blue},A:{Alpha}";
        }

        public Color ToColor()
        {
            return Color.FromArgb(Alpha, Red, Green, Blue);
        }

        public HSB ToHsb()
        {
            return ColorHelper.ColorToHsb(this);
        }

        public Cmyk ToCmyk()
        {
            return ColorHelper.ColorToCmyk(this);
        }
    }
}