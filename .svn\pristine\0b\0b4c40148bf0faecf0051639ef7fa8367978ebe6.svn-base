﻿using MetroFramework.Components;
using MetroFramework.Controls;
using OCRTools.Language;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmMain
    {

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private IContainer components = null;
        private MetroTabControl tbMain;
        private NotifyIcon notifyMain;
        private MetroContextMenu cmsNotify;
        private ToolStripMenuItem tsmShowMain;
        private ToolStripMenuItem 截图合集toolStripMenuItem;
        private ToolStripMenuItem 贴图合集toolStripMenuItem;
        private ToolStripMenuItem 工具合集toolStripMenuItem;
        private ToolStripMenuItem 识别合集toolStripMenuItem;
        private ToolStripMenuItem 截图识别toolStripMenuItem;
        private ToolStripMenuItem 粘贴识别ToolStripMenuItem;
        private ToolStripMenuItem 文件识别ToolStripMenuItem;
        private ToolStripMenuItem 批量识别ToolStripMenuItem;
        private ToolStripMenuItem 图片压缩ToolStripMenuItem;
        private ToolStripMenuItem PDF转图片ToolStripMenuItem;
        private ToolStripMenuItem 批量矫正ToolStripMenuItem;
        private ToolStripMenuItem 识别历史ToolStripMenuItem;
        private ToolStripMenuItem tsmExit;
        private ToolStripMenuItem 系统设置SToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator1;
        private ToolStripSeparator tsmOcrGroupSpilt;
        private StatusStrip stateStip;
        private ToolStripDropDownButton tmsSpiltMode;
        private ToolStripMenuItem 取色器toolStripMenuItem1;
        private ToolStripMenuItem 放大镜toolStripMenuItem1;
        private ToolStripMenuItem 白板toolStripMenuItem1;
        private ToolStripMenuItem 调色板toolStripMenuItem1;
        private ToolStripMenuItem 标尺工具toolStripMenuItem1;
        private ToolStripDropDownButton tsmContentType;
        private ToolStripDropDownButton tsmFirstDirection;
        private ToolStripDropDownButton tsmSecondDirection;
        private ToolStripMenuItem ocrGroupTypeToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator3;
        private ToolStripSeparator toolStripSeparator4;
        private MetroFramework.Components.MetroToolTip tipMain;

        private UcLoading ucLoading1;
        private ToolStripMenuItem tsmShowTool;
        private ToolStripMenuItem tsmToolImage;
        private ToolStripMenuItem tsmExportExcel;
        private ToolStripMenuItem tsmExportTxt;
        private ToolStripMenuItem tsmTrace;
        private ToolStripMenuItem tsmCopyTxt;
        private ToolStripMenuItem tsmGuide;
        private ToolStripSeparator tsmSpiltExport;

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FrmMain));
            this.tbMain = new MetroFramework.Controls.MetroTabControl();
            this.notifyMain = new System.Windows.Forms.NotifyIcon(this.components);
            this.cmsNotify = new MetroFramework.Controls.MetroContextMenu(this.components);
            this.tsmGuide = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmCopyTxt = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmSearch = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmTrans = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmExportTxt = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmTrace = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmSpiltExport = new System.Windows.Forms.ToolStripSeparator();
            this.截图合集toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.贴图合集toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.截图识别toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.粘贴识别ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.文件识别ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.批量识别ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.图片压缩ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.PDF转图片ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.批量矫正ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.识别历史ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.工具合集toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.识别合集toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.取色器toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.放大镜toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.白板toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.调色板toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.标尺工具toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator5 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripSeparator6 = new System.Windows.Forms.ToolStripSeparator();
            this.ocrGroupTypeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmOcrGroupSpilt = new System.Windows.Forms.ToolStripSeparator();
            this.tsmShowMain = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmShowTool = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmToolImage = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.网络检测ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.工具配置ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.系统设置SToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmExit = new System.Windows.Forms.ToolStripMenuItem();
            this.stateStip = new System.Windows.Forms.StatusStrip();
            this.tsmContentType = new System.Windows.Forms.ToolStripDropDownButton();
            this.tsmPicViewModel = new ToolStripDropDownButton();
            this.tsmTransFrom = new System.Windows.Forms.ToolStripDropDownButton();
            this.tsmTransTo = new System.Windows.Forms.ToolStripDropDownButton();
            this.tsmSecondDirection = new System.Windows.Forms.ToolStripDropDownButton();
            this.tsmFirstDirection = new System.Windows.Forms.ToolStripDropDownButton();
            this.tmsSpiltMode = new System.Windows.Forms.ToolStripDropDownButton();
            this.tsmShowOldContent = new OCRTools.ToolStripCheckBoxControl();
            this.tsmAutoTrans = new OCRTools.ToolStripCheckBoxControl();
            this.tipMain = new MetroFramework.Components.MetroToolTip();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripSeparator4 = new System.Windows.Forms.ToolStripSeparator();
            this.ucLoading1 = new OCRTools.UcLoading();
            this.检查更新ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.问题反馈ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tbMain.SuspendLayout();
            //this.cmsNotify.SuspendLayout();
            this.stateStip.SuspendLayout();
            this.SuspendLayout();
            // 
            // tbMain
            // 
            this.tbMain.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tbMain.FontSize = MetroFramework.MetroTabControlSize.Tall;
            this.tbMain.FontWeight = MetroFramework.MetroTabControlWeight.Regular;
            this.tbMain.HotTrack = true;
            this.tbMain.Location = new System.Drawing.Point(1, 31);
            this.tbMain.Name = "tbMain";
            this.tbMain.Padding = new System.Drawing.Point(6, 8);
            this.tbMain.SelectedIndex = 0;
            this.tbMain.ShowToolTips = true;
            this.tbMain.Size = new System.Drawing.Size(452, 325);
            this.tbMain.StyleManager = CommonTheme.StyleManager;
            this.tbMain.TabIndex = 4;
            this.tbMain.TabStop = false;
            this.tbMain.UseSelectable = true;
            // 
            // notifyMain
            // 
            this.notifyMain.BalloonTipTitle = CommonString.FullName.CurrentText();
            this.notifyMain.Text = CommonString.FullName.CurrentText();
            this.notifyMain.Visible = true;
            this.notifyMain.BalloonTipClicked += NotifyMain_BalloonTipClicked;
            this.notifyMain.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.notifyMain_MouseDoubleClick);
            //
            // cms工具集合
            //
            this.工具合集toolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
            this.取色器toolStripMenuItem1,
            this.放大镜toolStripMenuItem1,
            this.白板toolStripMenuItem1,
            this.调色板toolStripMenuItem1,
            this.标尺工具toolStripMenuItem1,
            this.图片压缩ToolStripMenuItem,
            this.PDF转图片ToolStripMenuItem,
            this.批量矫正ToolStripMenuItem,
            this.网络检测ToolStripMenuItem,
            this.工具配置ToolStripMenuItem
            });
            //
            // 识别合集toolStripMenuItem
            //
            this.识别合集toolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[]
            {
            this.截图识别toolStripMenuItem,
            this.粘贴识别ToolStripMenuItem,
            this.文件识别ToolStripMenuItem,
            this.批量识别ToolStripMenuItem
            });
            // 
            // cmsNotify
            // 
            this.cmsNotify.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmGuide,
            this.tsmCopyTxt,
            this.tsmSearch,
            this.tsmTrans,
            this.tsmExportExcel,
            this.tsmExportTxt,
            this.tsmTrace,
            this.tsmSpiltExport,
            this.截图合集toolStripMenuItem,
            this.贴图合集toolStripMenuItem,
            this.识别合集toolStripMenuItem,
            this.工具合集toolStripMenuItem,
            this.toolStripSeparator5,
            this.ocrGroupTypeToolStripMenuItem,
            this.识别历史ToolStripMenuItem,
            this.tsmOcrGroupSpilt,
            this.tsmShowMain,
            this.tsmShowTool,
            this.tsmToolImage,
            this.toolStripSeparator1,
            this.系统设置SToolStripMenuItem,
            this.检查更新ToolStripMenuItem,
            this.问题反馈ToolStripMenuItem,
            this.tsmExit});
            this.cmsNotify.Name = "cmsNotify";
            this.cmsNotify.Size = new System.Drawing.Size(181, 496);
            this.cmsNotify.Style = MetroColorStyle.黑色;
            this.cmsNotify.Font = CommonString.GetSysNormalFont(14F);
            // 
            // tsmCopyTxt
            // 
            this.tsmCopyTxt.Size = new System.Drawing.Size(191, 22);
            this.tsmCopyTxt.AccessibleDefaultActionDescription = "复制";
            this.tsmCopyTxt.Text = "复制文本";
            this.tsmCopyTxt.Visible = false;
            this.tsmCopyTxt.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // tsmGuide
            // 
            this.tsmGuide.Size = new System.Drawing.Size(191, 22);
            this.tsmGuide.AccessibleDefaultActionDescription = "导航";
            this.tsmGuide.Text = "快速上手";
            this.tsmGuide.Visible = true;
            this.tsmGuide.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // tsmSearch
            // 
            this.tsmSearch.AccessibleDefaultActionDescription = "搜索";
            this.tsmSearch.Size = new System.Drawing.Size(191, 22);
            this.tsmSearch.Text = "搜索选中文字";
            this.tsmSearch.Visible = false;
            this.tsmSearch.Click += new System.EventHandler(this.tsmSearch_Click);
            // 
            // tsmTrans
            // 
            this.tsmTrans.AccessibleDefaultActionDescription = "翻译";
            this.tsmTrans.Size = new System.Drawing.Size(191, 22);
            this.tsmTrans.Text = "翻译选中文字";
            this.tsmTrans.Visible = false;
            this.tsmTrans.Click += new System.EventHandler(this.tsmTrans_Click);
            // 
            // tsmExportExcel
            // 
            this.tsmExportExcel.AccessibleDefaultActionDescription = "excel";
            this.tsmExportExcel.Size = new System.Drawing.Size(191, 22);
            this.tsmExportExcel.Text = "导出Excel";
            this.tsmExportExcel.Visible = false;
            this.tsmExportExcel.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // tsmExportTxt
            // 
            this.tsmExportTxt.AccessibleDefaultActionDescription = "导出";
            this.tsmExportTxt.Size = new System.Drawing.Size(191, 22);
            this.tsmExportTxt.Text = "保存文本";
            this.tsmExportTxt.Visible = false;
            this.tsmExportTxt.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // tsmTrace
            // 
            this.tsmTrace.AccessibleDefaultActionDescription = "网络检测";
            this.tsmTrace.Size = new System.Drawing.Size(191, 22);
            this.tsmTrace.Text = "识别链路";
            this.tsmTrace.Visible = false;
            this.tsmTrace.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // tsmSpiltExport
            // 
            this.tsmSpiltExport.Name = "tsmSpiltExport";
            this.tsmSpiltExport.Size = new System.Drawing.Size(188, 6);
            this.tsmSpiltExport.Visible = false;
            // 
            // 截图合集toolStripMenuItem
            // 
            this.截图合集toolStripMenuItem.AccessibleDefaultActionDescription = "截图";
            this.截图合集toolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.截图合集toolStripMenuItem.Text = "截图";
            this.截图合集toolStripMenuItem.Click += new System.EventHandler(this.截图ToolStripMenuItem_Click);
            // 
            // 贴图合集toolStripMenuItem
            // 
            this.贴图合集toolStripMenuItem.AccessibleDefaultActionDescription = "贴图";
            this.贴图合集toolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.贴图合集toolStripMenuItem.Text = "贴图";
            this.贴图合集toolStripMenuItem.Click += new System.EventHandler(this.贴图ToolStripMenuItem_Click);
            // 
            // 识别合集toolStripMenuItem
            // 
            this.识别合集toolStripMenuItem.AccessibleDefaultActionDescription = "文本";
            this.识别合集toolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.识别合集toolStripMenuItem.Text = "识别";
            this.识别合集toolStripMenuItem.Click += new System.EventHandler(this.截图识别ToolStripMenuItem_Click);
            // 
            // 工具合集toolStripMenuItem
            // 
            this.工具合集toolStripMenuItem.AccessibleDefaultActionDescription = "toolbox";
            this.工具合集toolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.工具合集toolStripMenuItem.Text = "工具";
            this.工具合集toolStripMenuItem.AccessibleDescription = 工具合集toolStripMenuItem.Text;
            // 
            // 截图识别toolStripMenuItem
            // 
            this.截图识别toolStripMenuItem.AccessibleDefaultActionDescription = "截图";
            this.截图识别toolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.截图识别toolStripMenuItem.Text = "截图识别";
            this.截图识别toolStripMenuItem.AccessibleDescription = 截图识别toolStripMenuItem.Text;
            this.截图识别toolStripMenuItem.Click += new System.EventHandler(this.截图识别ToolStripMenuItem_Click);
            // 
            // 粘贴识别ToolStripMenuItem
            // 
            this.粘贴识别ToolStripMenuItem.AccessibleDefaultActionDescription = "粘贴";
            this.粘贴识别ToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.粘贴识别ToolStripMenuItem.Text = "粘贴识别";
            this.粘贴识别ToolStripMenuItem.AccessibleDescription = 粘贴识别ToolStripMenuItem.Text;
            this.粘贴识别ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // 文件识别ToolStripMenuItem
            // 
            this.文件识别ToolStripMenuItem.AccessibleDefaultActionDescription = "文件";
            this.文件识别ToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.文件识别ToolStripMenuItem.Text = "文件识别";
            this.文件识别ToolStripMenuItem.AccessibleDescription = 文件识别ToolStripMenuItem.Text;
            this.文件识别ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // tsmCaptureColor
            // 
            this.取色器toolStripMenuItem1.AccessibleDefaultActionDescription = "取色器";
            this.取色器toolStripMenuItem1.Size = new System.Drawing.Size(191, 22);
            this.取色器toolStripMenuItem1.Text = "取色器";
            this.取色器toolStripMenuItem1.Click += new System.EventHandler(this.取色器ToolStripMenuItem_Click);
            // 
            // tsmMagnifier
            // 
            this.放大镜toolStripMenuItem1.AccessibleDefaultActionDescription = "搜索";
            this.放大镜toolStripMenuItem1.Size = new System.Drawing.Size(191, 22);
            this.放大镜toolStripMenuItem1.Text = "放大镜";
            this.放大镜toolStripMenuItem1.Click += new System.EventHandler(this.放大镜ToolStripMenuItem_Click);

            this.白板toolStripMenuItem1.AccessibleDefaultActionDescription = "窗体";
            this.白板toolStripMenuItem1.Size = new System.Drawing.Size(191, 22);
            this.白板toolStripMenuItem1.Text = "白板";
            this.白板toolStripMenuItem1.Click += new System.EventHandler(this.白板ToolStripMenuItem_Click);
            // 
            // tsmCaptureColor
            // 
            this.调色板toolStripMenuItem1.AccessibleDefaultActionDescription = "调色板";
            this.调色板toolStripMenuItem1.Size = new System.Drawing.Size(191, 22);
            this.调色板toolStripMenuItem1.Text = "调色板";
            this.调色板toolStripMenuItem1.Click += new System.EventHandler(this.调色板ToolStripMenuItem_Click);
            // 
            // tsmRuler
            // 
            this.标尺工具toolStripMenuItem1.AccessibleDefaultActionDescription = "标尺";
            this.标尺工具toolStripMenuItem1.Size = new System.Drawing.Size(191, 22);
            this.标尺工具toolStripMenuItem1.Text = "标尺";
            this.标尺工具toolStripMenuItem1.Click += new System.EventHandler(this.标尺工具ToolStripMenuItem_Click);
            // 
            // 识别历史ToolStripMenuItem
            // 
            this.识别历史ToolStripMenuItem.AccessibleDefaultActionDescription = "历史";
            this.识别历史ToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.识别历史ToolStripMenuItem.Text = "识别历史";
            this.识别历史ToolStripMenuItem.Click += new System.EventHandler(this.识别历史ToolStripMenuItem_Click);
            // 
            // 批量识别ToolStripMenuItem
            // 
            this.批量识别ToolStripMenuItem.AccessibleDefaultActionDescription = "批量";
            this.批量识别ToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.批量识别ToolStripMenuItem.Font = CommonString.GetSysBoldFont(13F);
            this.批量识别ToolStripMenuItem.Text = "批量识别";
            this.批量识别ToolStripMenuItem.Click += new System.EventHandler(this.批量识别ToolStripMenuItem_Click);
            // 
            // 图片压缩ToolStripMenuItem
            // 
            this.图片压缩ToolStripMenuItem.AccessibleDefaultActionDescription = "图片压缩";
            this.图片压缩ToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.图片压缩ToolStripMenuItem.Font = CommonString.GetSysBoldFont(13F);
            this.图片压缩ToolStripMenuItem.Text = "图片压缩";
            this.图片压缩ToolStripMenuItem.Click += new System.EventHandler(this.批量压缩ToolStripMenuItem_Click);
            // 
            // PDF转图片ToolStripMenuItem
            // 
            this.PDF转图片ToolStripMenuItem.AccessibleDefaultActionDescription = "ui_thumbnail_title";
            this.PDF转图片ToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.PDF转图片ToolStripMenuItem.Font = CommonString.GetSysBoldFont(13F);
            this.PDF转图片ToolStripMenuItem.Text = "PDF转图片";
            this.PDF转图片ToolStripMenuItem.Click += new System.EventHandler(this.PDF转图片ToolStripMenuItem_Click);
            // 
            // 批量矫正ToolStripMenuItem
            // 
            this.批量矫正ToolStripMenuItem.AccessibleDefaultActionDescription = "文档";
            this.批量矫正ToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.批量矫正ToolStripMenuItem.Font = CommonString.GetSysBoldFont(13F);
            this.批量矫正ToolStripMenuItem.Text = "文档矫正";
            this.批量矫正ToolStripMenuItem.Click += new System.EventHandler(this.批量矫正ToolStripMenuItem_Click);
            // 
            // toolStripSeparator5
            // 
            this.toolStripSeparator5.Size = new System.Drawing.Size(188, 6);
            // 
            // ocrGroupTypeToolStripMenuItem
            // 
            this.ocrGroupTypeToolStripMenuItem.AccessibleDefaultActionDescription = "通道";
            this.ocrGroupTypeToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.ocrGroupTypeToolStripMenuItem.Text = "识别引擎";
            // 
            // tsmOcrGroupSpilt
            // 
            this.tsmOcrGroupSpilt.Name = "tsmOcrGroupSpilt";
            this.tsmOcrGroupSpilt.Size = new System.Drawing.Size(188, 6);
            // 
            // tsmShowMain
            // 
            this.tsmShowMain.AccessibleDefaultActionDescription = "窗体";
            this.tsmShowMain.Size = new System.Drawing.Size(191, 22);
            this.tsmShowMain.Text = "显示主窗体";
            this.tsmShowMain.Click += new System.EventHandler(this.tsmShowMain_Click);
            // 
            // tsmShowTool
            // 
            this.tsmShowTool.AccessibleDefaultActionDescription = "显示隐藏";
            this.tsmShowTool.Size = new System.Drawing.Size(191, 22);
            this.tsmShowTool.Text = "显示工具栏";
            this.tsmShowTool.Click += new System.EventHandler(this.tsmShowTool_Click);
            // 
            // tsmToolImage
            // 
            this.tsmToolImage.AccessibleDefaultActionDescription = "工具栏";
            this.tsmToolImage.Size = new System.Drawing.Size(191, 22);
            this.tsmToolImage.Text = "工具栏图片";
            this.tsmToolImage.Visible = false;
            this.tsmToolImage.Click += new System.EventHandler(this.系统设置SToolStripMenuItem_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(188, 6);
            // 
            // 网络检测ToolStripMenuItem
            // 
            this.网络检测ToolStripMenuItem.AccessibleDefaultActionDescription = "网络检测";
            this.网络检测ToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.网络检测ToolStripMenuItem.Text = "网络检测";
            this.网络检测ToolStripMenuItem.Click += new System.EventHandler(this.pnlNetWork_Click);
            // 
            // 工具配置ToolStripMenuItem
            // 
            this.工具配置ToolStripMenuItem.AccessibleDefaultActionDescription = "设置";
            this.工具配置ToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.工具配置ToolStripMenuItem.Text = "工具配置";
            this.工具配置ToolStripMenuItem.Click += new System.EventHandler(this.系统设置SToolStripMenuItem_Click);
            // 
            // 系统设置SToolStripMenuItem
            // 
            this.系统设置SToolStripMenuItem.AccessibleDefaultActionDescription = "设置";
            this.系统设置SToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.系统设置SToolStripMenuItem.Text = "系统设置";
            this.系统设置SToolStripMenuItem.AccessibleDescription = 系统设置SToolStripMenuItem.Text;
            this.系统设置SToolStripMenuItem.Click += new System.EventHandler(this.系统设置SToolStripMenuItem_Click);
            // 
            // tsmExit
            // 
            this.tsmExit.AccessibleDefaultActionDescription = "退出";
            this.tsmExit.Size = new System.Drawing.Size(191, 22);
            this.tsmExit.Text = "退出程序(&X)";
            this.tsmExit.Click += new System.EventHandler(this.tsmExit_Click);
            // 
            // stateStip
            // 
            this.stateStip.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.stateStip.AutoSize = false;
            this.stateStip.BackColor = System.Drawing.Color.Transparent;
            this.stateStip.Dock = System.Windows.Forms.DockStyle.None;
            this.stateStip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmContentType,
            this.tsmPicViewModel,
            this.tmsSpiltMode,
            this.tsmTransFrom,
            this.tsmTransTo,
            this.tsmFirstDirection,
            this.tsmSecondDirection,
            this.tsmShowOldContent,
            this.tsmAutoTrans});
            this.stateStip.Location = new System.Drawing.Point(6, 357);
            this.stateStip.Name = "stripAuto";
            this.stateStip.RenderMode = System.Windows.Forms.ToolStripRenderMode.Professional;
            this.stateStip.ShowItemToolTips = true;
            this.stateStip.Size = new System.Drawing.Size(444, 40);
            this.stateStip.TabIndex = 5;
            this.stateStip.Renderer = new ComboxbtnRenderer(true);
            // 
            // tsmContentType
            // 
            this.tsmContentType.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmContentType.ForeColor = System.Drawing.Color.Black;
            this.tsmContentType.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmContentType.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmContentType.Name = "tsmContentType";
            this.tsmContentType.Size = new System.Drawing.Size(13, 38);
            this.tsmContentType.Tag = "识别方式";
            this.tsmContentType.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmDDL_DropDownItemClicked);
            // 
            // tsmPicViewModel
            // 
            this.tsmPicViewModel.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmPicViewModel.ForeColor = System.Drawing.Color.Black;
            this.tsmPicViewModel.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmPicViewModel.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmPicViewModel.Name = "tsmPicViewModel";
            this.tsmPicViewModel.Size = new System.Drawing.Size(23, 38);
            this.tsmPicViewModel.Tag = "展示模式";
            this.tsmPicViewModel.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmDDL_DropDownItemClicked);
            // 
            // tsmTransFrom
            // 
            this.tsmTransFrom.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmTransFrom.ForeColor = System.Drawing.Color.Black;
            this.tsmTransFrom.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmTransFrom.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmTransFrom.Name = "tsmTransFrom";
            this.tsmTransFrom.Size = new System.Drawing.Size(13, 38);
            this.tsmTransFrom.Tag = "翻译语言From";
            this.tsmTransFrom.Text = "自动";
            this.tsmTransFrom.Visible = false;
            this.tsmTransFrom.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmDDL_DropDownItemClicked);
            // 
            // tsmTransTo
            // 
            this.tsmTransTo.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmTransTo.ForeColor = System.Drawing.Color.Black;
            this.tsmTransTo.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmTransTo.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmTransTo.Name = "tsmTransTo";
            this.tsmTransTo.Size = new System.Drawing.Size(13, 38);
            this.tsmTransTo.Tag = "翻译语言To";
            this.tsmTransTo.Text = "英文";
            this.tsmTransTo.Visible = false;
            this.tsmTransTo.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmDDL_DropDownItemClicked);
            // 
            // tsmFirstDirection
            // 
            this.tsmFirstDirection.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmFirstDirection.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmFirstDirection.ForeColor = System.Drawing.Color.Black;
            this.tsmFirstDirection.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmFirstDirection.Name = "tsmFirstDirection";
            this.tsmFirstDirection.Size = new System.Drawing.Size(13, 38);
            this.tsmFirstDirection.Tag = "排版方向";
            this.tsmFirstDirection.Text = "竖版";
            this.tsmFirstDirection.ToolTipText = "主排版方向";
            this.tsmFirstDirection.Visible = false;
            this.tsmFirstDirection.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmDDL_DropDownItemClicked);
            // 
            // tsmVerticalDirection
            // 
            this.tsmSecondDirection.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmSecondDirection.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmSecondDirection.ForeColor = System.Drawing.Color.Black;
            this.tsmSecondDirection.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmSecondDirection.Name = "tsmVerticalDirection";
            this.tsmSecondDirection.Size = new System.Drawing.Size(13, 38);
            this.tsmSecondDirection.Tag = "竖排方向";
            this.tsmSecondDirection.Text = "上下左右";
            this.tsmSecondDirection.ToolTipText = "竖排识别方向";
            this.tsmSecondDirection.Visible = false;
            this.tsmSecondDirection.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmDDL_DropDownItemClicked);
            // 
            // tmsSpiltMode
            // 
            this.tmsSpiltMode.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tmsSpiltMode.ForeColor = System.Drawing.Color.Black;
            this.tmsSpiltMode.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tmsSpiltMode.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tmsSpiltMode.Name = "tmsSpiltMode";
            this.tmsSpiltMode.Size = new System.Drawing.Size(13, 38);
            this.tmsSpiltMode.Tag = "分段模式";
            this.tmsSpiltMode.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmDDL_DropDownItemClicked);
            // 
            // tsmShowOldContent
            // 
            this.tsmShowOldContent.BackColor = System.Drawing.Color.Transparent;
            this.tsmShowOldContent.Checked = true;
            this.tsmShowOldContent.AutoSize = true;
            this.tsmShowOldContent.ForeColor = System.Drawing.Color.Black;
            this.tsmShowOldContent.Size = new System.Drawing.Size(71, 38);
            this.tsmShowOldContent.Text = "显示原文";
            this.tsmShowOldContent.ToolTipText = "翻译模式是否显示原文";
            this.tsmShowOldContent.Visible = false;
            this.tsmShowOldContent.CheckedChanged += new System.EventHandler(this.tsmShowOldContent_CheckedChanged);
            // 
            // tsmCopyTrans
            // 
            this.tsmAutoTrans.BackColor = System.Drawing.Color.Transparent;
            this.tsmAutoTrans.Checked = false;
            this.tsmAutoTrans.ForeColor = System.Drawing.Color.Black;
            this.tsmAutoTrans.Size = new System.Drawing.Size(83, 38);
            this.tsmAutoTrans.Text = "监听粘贴板";
            this.tsmAutoTrans.Visible = false;
            // 
            // tipMain
            // 
            this.tipMain.StyleManager = null;
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Size = new System.Drawing.Size(101, 6);
            // 
            // toolStripSeparator4
            // 
            this.toolStripSeparator4.Size = new System.Drawing.Size(101, 6);
            // 
            // ucLoading1
            // 
            this.ucLoading1.Location = new System.Drawing.Point(7, 7);
            this.ucLoading1.Name = "ucLoading1";
            this.ucLoading1.Size = new System.Drawing.Size(27, 30);
            this.ucLoading1.TabIndex = 2;
            // 
            // 检查更新ToolStripMenuItem
            // 
            this.检查更新ToolStripMenuItem.AccessibleDefaultActionDescription = "检查更新";
            this.检查更新ToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.检查更新ToolStripMenuItem.Text = "检查更新";
            this.检查更新ToolStripMenuItem.Click += new System.EventHandler(this.CheckUpdate);
            // 
            // 问题反馈ToolStripMenuItem
            // 
            this.问题反馈ToolStripMenuItem.AccessibleDefaultActionDescription = "问题反馈";
            this.问题反馈ToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.问题反馈ToolStripMenuItem.Text = "问题反馈";
            this.问题反馈ToolStripMenuItem.Click += new System.EventHandler(this.问题反馈ToolStripMenuItem_Click);
            // 
            // FrmMain
            // 
            this.ClientSize = new System.Drawing.Size(454, 398);
            this.Controls.Add(this.stateStip);
            this.Controls.Add(this.tbMain);
            this.Controls.Add(this.ucLoading1);
            this.ForeColor = System.Drawing.Color.DarkGray;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "FrmMain";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Show;
            this.Load += new System.EventHandler(this.FrmMain_Load);
            this.Shown += new System.EventHandler(this.FrmMain_Shown);
            this.tbMain.ResumeLayout(false);
            //this.cmsNotify.ResumeLayout(false);
            this.stateStip.ResumeLayout(false);
            this.stateStip.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private ToolStripDropDownButton tsmTransFrom;
        private ToolStripDropDownButton tsmTransTo;
        private ToolStripCheckBoxControl tsmShowOldContent;
        private ToolStripSeparator toolStripSeparator5;
        private ToolStripSeparator toolStripSeparator6;
        private ToolStripCheckBoxControl tsmAutoTrans;
        private ToolStripMenuItem tsmTrans;
        private ToolStripMenuItem tsmSearch;
        private ToolStripDropDownButton tsmPicViewModel;
        private ToolStripMenuItem 网络检测ToolStripMenuItem;
        private ToolStripMenuItem 工具配置ToolStripMenuItem;
        private ToolStripMenuItem 检查更新ToolStripMenuItem;
        private ToolStripMenuItem 问题反馈ToolStripMenuItem;
    }
}
