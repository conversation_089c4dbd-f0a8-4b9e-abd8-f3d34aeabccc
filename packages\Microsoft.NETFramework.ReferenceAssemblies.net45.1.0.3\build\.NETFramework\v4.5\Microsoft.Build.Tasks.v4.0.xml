﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Build.Tasks.v4.0</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Build.Tasks.AL">
      <summary>Implements the AL task. Use the AL element in your project file to create and execute this task. For usage and parameter information, see AL (Assembly Linker) Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.AL.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.AL" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.AL.AddResponseFileCommands(Microsoft.Build.Tasks.CommandLineBuilderExtension)">
      <summary>Fills the specified <paramref name="commandLine" /> parameter with switches and other information that can go into a response file.</summary>
      <param name="commandLine">Command line builder to add arguments to.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.AlgorithmId">
      <summary>Gets or sets an algorithm to hash all files in a multifile assembly except the file that contains the assembly manifest.</summary>
      <returns>The algorithm to hash all files in a multifile assembly except the file that contains the assembly manifest.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.BaseAddress">
      <summary>Gets or sets the address at which a DLL will be loaded on the user’s computer at run time.</summary>
      <returns>The address at which a DLL will be loaded on the user’s computer at run time.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.CompanyName">
      <summary>Gets or sets the Company field in the assembly.</summary>
      <returns>The Company field in the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.Configuration">
      <summary>Gets or sets the Configuration field in the assembly.</summary>
      <returns>The Configuration field in the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.Copyright">
      <summary>Gets or sets the Copyright field in the assembly.</summary>
      <returns>The Copyright field in the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.Culture">
      <summary>Gets or sets the culture string to associate with the assembly.</summary>
      <returns>The culture string to associate with the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.DelaySign">
      <summary>Gets or sets a Boolean value that indicates whether to place only the public key in the assembly (true) or to fully sign the assembly (false).</summary>
      <returns>true if only the public key is placed in the assembly; false if the assembly is fully signed.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.Description">
      <summary>Gets or sets the Description field in the assembly.</summary>
      <returns>The Description field in the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.EmbedResources">
      <summary>Gets or sets the resources to embed in the image that contains the assembly manifest.</summary>
      <returns>The resources to embed in the image that contains the assembly manifest.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.EvidenceFile">
      <summary>Gets or sets the file to embed in the assembly with the resource name of Security.Evidence.</summary>
      <returns>The file to embed in the assembly with the resource name of Security.Evidence.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.AL.Execute">
      <summary>Executes the AL task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.FileVersion">
      <summary>Gets or sets the File Version field in the assembly.</summary>
      <returns>The File Version field in the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.Flags">
      <summary>Gets or sets the Flags field in the assembly.</summary>
      <returns>The Flags field in the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.GenerateFullPaths">
      <summary>Gets or sets a Boolean value that indicates whether to cause the task to use the absolute path for any files that are reported in an error message.</summary>
      <returns>true if the task will use the absolute path for any files that are reported in an error message; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.AL.GenerateFullPathToTool">
      <summary>Returns the full file path to the AL tool.</summary>
      <returns>The full file path to the AL tool.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.KeyContainer">
      <summary>Gets or sets a container that holds a key pair.</summary>
      <returns>The container that holds a key pair.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.KeyFile">
      <summary>Gets or sets a file that contains a key pair or just a public key to sign an assembly.</summary>
      <returns>The file that contains a key pair or just a public key to sign an assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.LinkResources">
      <summary>Gets or sets the resource files to link to an assembly.</summary>
      <returns>The resource files to link to an assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.MainEntryPoint">
      <summary>Gets or sets the fully qualified name (class.method) of the method to use as an entry point when converting a module to an executable file.</summary>
      <returns>The fully qualified name (class.method) of the method to use as an entry point when converting a module to an executable file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.OutputAssembly">
      <summary>Gets or sets the name of the file generated by this task.</summary>
      <returns>The name of the file generated by this task.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.Platform">
      <summary>Gets or sets a string that limits which platform this code can run on; must be one of x86, Itanium, x64, or anycpu.</summary>
      <returns>A string that limits which platform this code can run on; must be one of x86, Itanium, x64, or anycpu.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.Prefer32Bit">
      <summary>Gets or sets a flag indicating whether the task runs on a 32 bit platform.</summary>
      <returns>Returns the value of the flag.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.ProductName">
      <summary>Gets or sets the Product field in the assembly.</summary>
      <returns>The Product field in the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.ProductVersion">
      <summary>Gets or sets the ProductVersion field in the assembly.</summary>
      <returns>The ProductVersion field in the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.ResponseFiles">
      <summary>Gets or sets the names of the response files that contain commands for the AL tool.</summary>
      <returns>The names of the response files that contain commands for the AL tool.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.SdkToolsPath">
      <summary>Gets or sets the path to the SDK tools.</summary>
      <returns>Returns the path to the SDK tools.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.SourceModules">
      <summary>Gets or sets the modules to be compiled into an assembly.</summary>
      <returns>The modules to be compiled into an assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.TargetType">
      <summary>Gets or sets the file format of the output file: library (code library), exe (console application), or win (Windows-based application).</summary>
      <returns>The file format of the output file: library (code library), exe (console application), or win (Windows-based application).</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.TemplateFile">
      <summary>Gets or sets the assembly from which to inherit all assembly metadata, except the culture field.</summary>
      <returns>The assembly from which to inherit all assembly metadata, except the culture field.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.Title">
      <summary>Gets or sets the Title field in the assembly.</summary>
      <returns>The Title field in the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.ToolName">
      <summary>Gets the name of the tool (Al.exe).</summary>
      <returns>The name of the tool.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.Trademark">
      <summary>Gets or sets the Trademark field in the assembly.</summary>
      <returns>The Trademark field in the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.Version">
      <summary>Gets or sets the version information for this assembly.</summary>
      <returns>The version information for this assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.Win32Icon">
      <summary>Gets or sets the .ico file to insert in the assembly.</summary>
      <returns>The .ico file to insert in the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AL.Win32Resource">
      <summary>Gets or sets a Win32 resource (.res file) to insert in the output file.</summary>
      <returns>The Win32 resource (.res file) to insert in the output file.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.AppDomainIsolatedTaskExtension">
      <summary>Provides the same functionality as the <see cref="T:Microsoft.Build.Utilities.Task" /> class, but derives from <see cref="T:System.MarshalByRefObject" /> so that it can be instantiated in its own application domain.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.AppDomainIsolatedTaskExtension.Log">
      <summary>Gets an instance of a <see cref="T:Microsoft.Build.Tasks.TaskLoggingHelperExtension" /> class containing task logging methods.</summary>
      <returns>The logging helper object.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.AspNetCompiler">
      <summary>Implements the AspNetCompiler task. Use the AspNetCompiler element in your project file to create and execute this task. For usage and parameter information, see AspNetCompiler Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.AspNetCompiler.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.AspNetCompiler" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.AspNetCompiler.AddCommandLineCommands(Microsoft.Build.Tasks.CommandLineBuilderExtension)">
      <summary>Generates command line arguments for the AspNetCompiler tool.</summary>
      <param name="commandLine">Command line builder to add arguments to.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.AllowPartiallyTrustedCallers">
      <summary>Gets or sets a Boolean value that indicates whether the <see cref="T:System.Security.AllowPartiallyTrustedCallersAttribute" />, which allows partially trusted callers access to an assembly, should be applied to the strongly named assembly that Aspnet_compiler.exe generates.</summary>
      <returns>true if the <see cref="T:System.Security.AllowPartiallyTrustedCallersAttribute" /> should be applied to the strongly named assembly that Aspnet_compiler.exe generates; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.Clean">
      <summary>Gets or sets a Boolean value that indicates whether the precompiled application will be built clean.</summary>
      <returns>true if the precompiled application will be built clean; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.Debug">
      <summary>Gets or sets a Boolean value that indicates whether debug information (.PDB file) is emitted during compilation.</summary>
      <returns>true if the debug information (.PDB file) is emitted during compilation; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.DelaySign">
      <summary>Gets or sets a Boolean value that indicates whether the <see cref="T:System.Reflection.AssemblyDelaySignAttribute" />, which indicates that an assembly should be signed only with the public key token rather than with the public/private key pair, should be applied to the generated assembly.</summary>
      <returns>true if the <see cref="T:System.Reflection.AssemblyDelaySignAttribute" /> should be applied to the generated assembly; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.AspNetCompiler.Execute">
      <summary>Executes the AspNetCompiler task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.FixedNames">
      <summary>Gets or sets a Boolean value that indicates whether one assembly should be generated for each page in the application.</summary>
      <returns>true if one assembly should be generated for each page in the application; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.Force">
      <summary>Gets or sets a Boolean value that specifies whether existing files in the <paramref name="targetDir" /> directory and its subdirectories should be overwritten.</summary>
      <returns>true if existing files in the <paramref name="targetDir" /> directory and its subdirectories should be overwritten; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.AspNetCompiler.GenerateFullPathToTool">
      <summary>Returns the full file path of the AspNetCompiler tool.</summary>
      <returns>The full file path of the AspNetCompiler tool.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.KeyContainer">
      <summary>Gets or sets a Boolean value that indicates whether the <see cref="T:System.Reflection.AssemblyKeyNameAttribute" />, which indicates the name of the container for the public/private key pair that is used to generate a strong name, should be applied to the compiled assembly.</summary>
      <returns>true if the <see cref="T:System.Reflection.AssemblyKeyNameAttribute" /> should be applied to the compiled assembly; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.KeyFile">
      <summary>Gets or sets a Boolean value that indicates whether the <see cref="T:System.Reflection.AssemblyKeyFileAttribute" />, which indicates the name of the file containing the public/private key pair that is used to generate a strong name, should be applied to the compiled assembly.</summary>
      <returns>true if the <see cref="T:System.Reflection.AssemblyKeyFileAttribute" /> should be applied to the compiled assembly; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.MetabasePath">
      <summary>Gets or sets the full IIS metabase path of the application.</summary>
      <returns>The full IIS metabase path of the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.PhysicalPath">
      <summary>Gets or sets the physical path of the application to be compiled.</summary>
      <returns>The physical path of the application to be compiled.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.TargetFrameworkMoniker">
      <summary>Gets or sets the TargetFrameworkMoniker indicating which .NET Framework version of aspnet_compiler.exe should be used.</summary>
      <returns>Returns the TargetFrameworkMoniker.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.TargetPath">
      <summary>Gets or sets the physical path to which the application is compiled.</summary>
      <returns>The physical path to which the application is compiled.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.ToolName">
      <summary>Gets the name of the AspNetCompiler tool (aspnet_compiler.exe).</summary>
      <returns>The name of the AspNetCompiler tool.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.Updateable">
      <summary>Gets or sets a Boolean value that indicates whether the precompiled application will be updateable.</summary>
      <returns>true if the precompiled application will be updateable; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.AspNetCompiler.ValidateParameters">
      <summary>Validates input parameters and logs errors or warnings, if any. Returns a Boolean value that indicates whether task execution should proceed.</summary>
      <returns>true if task execution should proceed; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AspNetCompiler.VirtualPath">
      <summary>Gets or sets the virtual path of the application to be compiled.</summary>
      <returns>The virtual path of the application to be compiled.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.AssignCulture">
      <summary>Implements the AssignCulture task. Use the AssignCulture element in your project file to create and execute this task. For usage and parameter information, see AssignCulture Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.AssignCulture.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.AssignCulture" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignCulture.AssignedFiles">
      <summary>Gets a list of <see cref="P:Microsoft.Build.Tasks.AssignCulture.Files" /> with a Culture metadata entry added to each item.</summary>
      <returns>The list of <see cref="P:Microsoft.Build.Tasks.AssignCulture.Files" />, with a Culture metadata entry added to each item.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignCulture.AssignedFilesWithCulture">
      <summary>Gets a subset of <see cref="P:Microsoft.Build.Tasks.AssignCulture.AssignedFiles" /> that have a Culture metadata entry.</summary>
      <returns>A subset of <see cref="P:Microsoft.Build.Tasks.AssignCulture.AssignedFiles" /> that have a Culture metadata entry.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignCulture.AssignedFilesWithNoCulture">
      <summary>Gets a subset of <see cref="P:Microsoft.Build.Tasks.AssignCulture.AssignedFiles" /> that do not have a Culture metadata entry.</summary>
      <returns>A subset of <see cref="P:Microsoft.Build.Tasks.AssignCulture.AssignedFiles" /> that do not have a Culture metadata entry.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignCulture.CultureNeutralAssignedFiles">
      <summary>Gets a subset of <see cref="P:Microsoft.Build.Tasks.AssignCulture.AssignedFiles" /> that do not have a Culture metadata entry.</summary>
      <returns>A subset of <see cref="P:Microsoft.Build.Tasks.AssignCulture.AssignedFiles" /> that do not have a Culture metadata entry.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.AssignCulture.Execute">
      <summary>Executes the AssignCulture task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignCulture.Files">
      <summary>Gets or sets a list of files with embedded culture names to assign a culture to.</summary>
      <returns>The list of files with embedded culture names to assign a culture to.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.AssignProjectConfiguration">
      <summary>Assigns XML project configurations to project reference task items.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.AssignProjectConfiguration.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.AssignProjectConfiguration" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignProjectConfiguration.AddSyntheticProjectReferencesForSolutionDependencies">
      <summary>Gets or sets a flag determining whether to use the solution dependency information passed in the solution blob to add synthetic project references for the purposes of build ordering.</summary>
      <returns>Returns the flag.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignProjectConfiguration.AssignedProjects">
      <summary>Gets or sets the list of resolved project reference paths while preserving the original project reference attributes.</summary>
      <returns>The list of resolved project reference paths.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignProjectConfiguration.CurrentProject">
      <summary>Gets or sets the project’s full path.</summary>
      <returns>Returns the full path of the project.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignProjectConfiguration.CurrentProjectConfiguration">
      <summary>Gets or sets the current project's configuration.</summary>
      <returns>Returns the current project's configuration.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignProjectConfiguration.CurrentProjectPlatform">
      <summary>Gets or sets the current project's platform.</summary>
      <returns>Returns the current project's platform.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignProjectConfiguration.DefaultToVcxPlatformMapping">
      <summary>Gets or sets a semicolon-delimited list of mappings from the platform names used by most VS types to those used by .vcxprojs.</summary>
      <returns>Returns a semicolon-delimited list of mappings from the platform names used by most VS types to those used by .vcxprojs.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.AssignProjectConfiguration.Execute">
      <summary>Executes the AssignProjectConfiguration task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignProjectConfiguration.OnlyReferenceAndBuildProjectsEnabledInSolutionConfiguration">
      <summary>Gets or sets a switch that specifies whether references should be built, even if they were disabled in the project configuration.</summary>
      <returns>Returns true if references should be built; false otherwise. Defaults to false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignProjectConfiguration.OutputType">
      <summary>Gets or sets the output type for the project.</summary>
      <returns>Returns the output type for the project.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignProjectConfiguration.ResolveConfigurationPlatformUsingMappings">
      <summary>Gets or sets a switch that specifies whether default mappings should be used to resolve the configuration/platform of the passed in project references.</summary>
      <returns>Returns true if default mappings should be used; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignProjectConfiguration.ShouldUnsetParentConfigurationAndPlatform">
      <summary>Gets or sets a switch that determines whether to set the GlobalPropertiesToRemove metadata on the project reference such that on an MSBuild call, the Configuration and Platform metadata will be unset, allowing the child project to build in its default configuration / platform.</summary>
      <returns>Returns true if the Configuration and Platform metadata will be unset; false otherwise. The default is false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignProjectConfiguration.SolutionConfigurationContents">
      <summary>Gets or sets an XML string that contains the project configuration.</summary>
      <returns>An XML string that contains the project configuration.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignProjectConfiguration.UnassignedProjects">
      <summary>Gets or sets the list of project reference items that could not be resolved using the pre-resolved list of outputs.</summary>
      <returns>The list of project reference items that could not be resolved using the pre-resolved list of outputs.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignProjectConfiguration.VcxToDefaultPlatformMapping">
      <summary>Gets or sets a semicolon-delimited list of mappings from .vcxproj platform names to the platform names use by most other VS project types.</summary>
      <returns>Returns the semicolon-delimited list of mappings.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.AssignTargetPath">
      <summary>Assigns target paths to input files.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.AssignTargetPath.#ctor">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Build.Tasks.AssignTargetPath" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignTargetPath.AssignedFiles">
      <summary>Gets the output files that are assigned to target paths.</summary>
      <returns>The output files that are assigned to target paths.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.AssignTargetPath.Execute">
      <summary>Executes the AssignTargetPath task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignTargetPath.Files">
      <summary>Gets or sets the input files to assign target paths to.</summary>
      <returns>The input files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.AssignTargetPath.RootFolder">
      <summary>Gets or sets the root folder that the target file paths are relative to.</summary>
      <returns>The root folder that the target file paths are relative to.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.CallTarget">
      <summary>Implements the CallTarget task. Use the CallTarget element in your project file to create and execute this task. For usage and parameter information, see CallTarget Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CallTarget.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.CallTarget" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CallTarget.Execute">
      <summary>Instructs the MSBuild engine to build one or more targets in the current project.</summary>
      <returns>true if all targets built successfully; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CallTarget.RunEachTargetSeparately">
      <summary>Gets or sets a Boolean value that indicates whether the MSBuild engine is called once per target (true) or the MSBuild engine is called once to build all targets (false).</summary>
      <returns>true if the MSBuild engine is called once per target; false if the MSBuild engine is called once to build all targets.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CallTarget.TargetOutputs">
      <summary>Gets the outputs of all built targets.</summary>
      <returns>The outputs of all built targets.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CallTarget.Targets">
      <summary>Gets or sets the target or targets to build.</summary>
      <returns>The target or targets to build.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CallTarget.UseResultsCache">
      <summary>Gets or sets a Boolean value that indicates whether the MSBuild engine will return the built targets from the cache.</summary>
      <returns>true if the MSBuild engine will return the built targets from the cache; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.CodeTaskFactory">
      <summary>A task factory which can take a code DOM supported language and create a task from it.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CodeTaskFactory.#ctor">
      <summary>Creates the task factory.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CodeTaskFactory.CleanupTask(Microsoft.Build.Framework.ITask)">
      <summary>Cleans up any context or state used in a task.</summary>
      <param name="task">The task to clean up.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.CodeTaskFactory.CreateTask(Microsoft.Build.Framework.IBuildEngine)">
      <summary>Create a taskfactory instance which contains the data that needs to be refreshed between task invocations.</summary>
      <returns>Returns the new task.</returns>
      <param name="loggingHost">The host that logs information from the task factory.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.CodeTaskFactory.FactoryName">
      <summary>The MSBuild engine uses this for logging.</summary>
      <returns>Returns the name of the task.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.CodeTaskFactory.GetTaskParameters">
      <summary>Gets the type information for all task parameters.</summary>
      <returns>Returns an array of the type information for all task parameters.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.CodeTaskFactory.Initialize(System.String,System.Collections.Generic.IDictionary{System.String,Microsoft.Build.Framework.TaskPropertyInfo},System.String,Microsoft.Build.Framework.IBuildEngine)">
      <summary>Initialzes the task factory.</summary>
      <returns>Returns the new task factory.</returns>
      <param name="taskName">The name of the task.</param>
      <param name="taskParameters">The parameters for the task.</param>
      <param name="taskElementContents">The element contents for the task.</param>
      <param name="taskFactoryLoggingHost">The logging host that gets information about tasks.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.CodeTaskFactory.TaskType">
      <summary>Represents the type of task.</summary>
      <returns>Returns the task type.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.CombinePath">
      <summary>Combines the specified paths into a single path.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CombinePath.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.CombinePath" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.CombinePath.BasePath">
      <summary>Gets or sets the base path to combine with the other paths. Can be a relative path, absolute path, or blank.</summary>
      <returns>The base path to combine with the other paths.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CombinePath.CombinedPaths">
      <summary>Gets or sets the combined path that is created by the CombinePath task.</summary>
      <returns>The combined path that is created by the CombinePath task.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.CombinePath.Execute">
      <summary>Executes the CombinePath task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CombinePath.Paths">
      <summary>Gets or sets a list of individual paths to combine with the <see cref="P:Microsoft.Build.Tasks.CombinePath.BasePath" /> to form the combined path. Paths can be relative or absolute.</summary>
      <returns>A list of individual paths to combine with the <see cref="P:Microsoft.Build.Tasks.CombinePath.BasePath" /> to form the combined path.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.CommandLineBuilderExtension">
      <summary>Comprises extended utility methods for constructing a command line.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CommandLineBuilderExtension.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.CommandLineBuilderExtension" /> class. </summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CommandLineBuilderExtension.GetQuotedText(System.String)">
      <summary>Returns a quoted string appropriate for appending to a command line.</summary>
      <returns>A string representing the new quoted string.</returns>
      <param name="unquotedText">The string to convert.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.ConvertToAbsolutePath">
      <summary>Implements the ConvertToAbsolutePath task. Use the ConvertToAbsolutePath element in your project file to create and execute this task. For usage and parameter information, see ConvertToAbsolutePath Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ConvertToAbsolutePath.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.ConvertToAbsolutePath" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.ConvertToAbsolutePath.AbsolutePaths">
      <summary>Gets or sets a list of absolute paths.</summary>
      <returns>The list of absolute paths.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ConvertToAbsolutePath.Execute">
      <summary>Executes the ConvertToAbsolutePath task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ConvertToAbsolutePath.Paths">
      <summary>Gets or sets a list of relative paths to convert to absolute paths.</summary>
      <returns>The list of relative paths to convert to absolute paths.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Copy">
      <summary>Implements the Copy task. Use the Copy element in your project file to create and execute this task. For usage and parameter information, see Copy Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Copy.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Copy" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Copy.Cancel">
      <summary>Stop and return (in an undefined state) as soon as possible.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Copy.CopiedFiles">
      <summary>Gets the items that were successfully copied.</summary>
      <returns>The items that were successfully copied.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Copy.DestinationFiles">
      <summary>Gets or sets a list of files to copy the source files to.</summary>
      <returns>The list of files to copy the source files to.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Copy.DestinationFolder">
      <summary>Gets or sets the directory to which you want to copy the files.</summary>
      <returns>The directory to which you want to copy the files.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Copy.Execute">
      <summary>Executes the Copy task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Copy.OverwriteReadOnlyFiles">
      <summary>Gets or sets a Boolean value that indicates whether to overwrite files even if they are marked as read only files.</summary>
      <returns>true if the Copy task should overwrite files even if they are marked as read only files; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Copy.Retries">
      <summary>How many times to attempt to copy, if all previous attempts failed. Defaults to zero. Warning: using retries may mask a synchronization problem in your build process.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Copy.RetryDelayMilliseconds">
      <summary>Delay between any necessary retries. Defaults to <see cref="F:Microsoft.Build.Tasks.Copy.RetryDelayMillisecondsDefault" /></summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Copy.SkipUnchangedFiles">
      <summary>Gets or sets a Boolean value that indicates whether the Copy task should skip the copying of files that are unchanged between the source and destination.</summary>
      <returns>true if the Copy task should skip the copying of files that are unchanged between the source and destination; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Copy.SourceFiles">
      <summary>Gets or sets the files to copy.</summary>
      <returns>The files to copy.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Copy.UseHardlinksIfPossible">
      <summary>Indicates if hard links should be used.</summary>
    </member>
    <member name="T:Microsoft.Build.Tasks.CreateCSharpManifestResourceName">
      <summary>Creates a C#-style manifest name from a given resource file name.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateCSharpManifestResourceName.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.CreateCSharpManifestResourceName" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateCSharpManifestResourceName.CreateManifestName(System.String,System.String,System.String,System.String,System.IO.Stream)">
      <summary>Returns the C#-style manifest resource name that corresponds to the specified resource file.</summary>
      <returns>The manifest resource name that corresponds to the specified resource file.</returns>
      <param name="fileName">The name of the resource file from which to create the C# manifest name.</param>
      <param name="linkFileName">The name of the link file.</param>
      <param name="rootNamespace">The root namespace of the resource file, typically taken from the project file. May be null.</param>
      <param name="dependentUponFileName">The file name of the parent of the resource file (usually a .cs file). May be null.</param>
      <param name="binaryStream">File contents binary stream. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateCSharpManifestResourceName.IsSourceFile(System.String)">
      <summary>Indicates whether the specified file is a C# source file.</summary>
      <returns>true if the specified file is a C# source file; otherwise, false.</returns>
      <param name="fileName">The name of the file to examine.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.CreateItem">
      <summary>Implements the CreateItem task. Use the CreateItem element in your project file to create and execute this task. For usage and parameter information, see CreateItem Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateItem.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.CreateItem" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.CreateItem.AdditionalMetadata">
      <summary>Gets or sets additional metadata to attach to the output items.</summary>
      <returns>Additional metadata to attach to the output items.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CreateItem.Exclude">
      <summary>Gets or sets the items to exclude from the output item collection.</summary>
      <returns>The items to exclude from the output item collection.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateItem.Execute">
      <summary>Executes the CreateItem task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CreateItem.Include">
      <summary>Gets or sets the items to include in the output item collection.</summary>
      <returns>The items to include in the output item collection.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CreateItem.PreserveExistingMetadata">
      <summary>Gets or sets a Boolean value that indicates whether to apply the additional metadata only if none already exists.</summary>
      <returns>true if the CreateItem task should apply the additional metadata only if none already exists; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.CreateManifestResourceName">
      <summary>Determines the manifest resource name to assign to a resource.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateManifestResourceName.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.CreateManifestResourceName" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateManifestResourceName.CreateManifestName(System.String,System.String,System.String,System.String,System.IO.Stream)">
      <summary>When overridden in a derived class, creates the manifest resource name.</summary>
      <returns>The manifest resource name.</returns>
      <param name="fileName">The name of the dependent file.</param>
      <param name="linkFileName">The name of the file specified by the Link attribute.</param>
      <param name="rootNamespaceName">The root namespace. This parameter may be a null reference (Nothing in Visual Basic).</param>
      <param name="dependentUponFileName">The file name of the parent of this dependency. This parameter may be a null reference (Nothing).</param>
      <param name="binaryStream">The binary stream contents of the file. This parameter may be a null reference (Nothing).</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateManifestResourceName.Execute">
      <summary>Runs the task.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateManifestResourceName.IsSourceFile(System.String)">
      <summary>When overridden in a derived class, indicates whether the specified file is a valid source file.</summary>
      <returns>true if the file is a valid source file; otherwise, false.</returns>
      <param name="fileName">The file name.</param>
    </member>
    <member name="F:Microsoft.Build.Tasks.CreateManifestResourceName.itemSpecToTaskitem">
      <summary>Contains the task item in a dictionary to provide quick access from a derived class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateManifestResourceName.MakeValidEverettIdentifier(System.String)">
      <summary>Creates a valid identifier for use with Visual Studio 2003.</summary>
      <returns>The identifier to use with Visual Studio 2003.</returns>
      <param name="name">The file name.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.CreateManifestResourceName.ManifestResourceNames">
      <summary>Gets the created manifest resource names.</summary>
      <returns>The created manifest resource names.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CreateManifestResourceName.PrependCultureAsDirectory">
      <summary>Gets or sets whether the culture name should be prepended to the manifest resource name as a directory.</summary>
      <returns>true if the culture name should be prepended, false if otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CreateManifestResourceName.ResourceFiles">
      <summary>Gets or sets the dependent resource files.</summary>
      <returns>The dependent resource files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CreateManifestResourceName.ResourceFilesWithManifestResourceNames">
      <summary>Gets or sets the initial list of resource names, along with additional metadata for manifest resource names.</summary>
      <returns>An <see cref="T:Microsoft.Build.Framework.ITaskItem" /> object, representing the resource names and metadata.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CreateManifestResourceName.RootNamespace">
      <summary>Gets or sets the root namespace to use for naming.</summary>
      <returns>The root namespace to use for naming.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.CreateProperty">
      <summary>Implements the CreateProperty task. Use the CreateProperty element in your project file to create and execute this task. For usage and parameter information, see CreateProperty Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateProperty.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.CreateProperty" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateProperty.Execute">
      <summary>Executes the CreateProperty task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CreateProperty.Value">
      <summary>Gets or sets the value to copy to the new property.</summary>
      <returns>The value to copy to the new property</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.CreateProperty.ValueSetByTask">
      <summary>Contains the same value as the <see cref="P:Microsoft.Build.Tasks.CreateProperty.Value" /> property.</summary>
      <returns>The same value as the <see cref="P:Microsoft.Build.Tasks.CreateProperty.Value" /> property.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.CreateVisualBasicManifestResourceName">
      <summary>Creates a Visual Basic-style manifest name from a given resource file name.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateVisualBasicManifestResourceName.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.CreateVisualBasicManifestResourceName" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateVisualBasicManifestResourceName.CreateManifestName(System.String,System.String,System.String,System.String,System.IO.Stream)">
      <summary>Returns the Visual Basic-style manifest resource name that corresponds to the specified resource file.</summary>
      <returns>The manifest resource name that corresponds to the specified resource file.</returns>
      <param name="fileName">The name of the resource file from which to create the Visual Basic manifest name.</param>
      <param name="linkFileName">The name of the link file.</param>
      <param name="rootNamespace">The root namespace of the resource file, typically taken from the project file. May be null.</param>
      <param name="dependentUponFileName">The file name of the parent of the resource file (usually a .vb file). May be null.</param>
      <param name="binaryStream">File contents binary stream. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.CreateVisualBasicManifestResourceName.IsSourceFile(System.String)">
      <summary>Indicates whether the specified file is a Visual Basic source file.</summary>
      <returns>true if the specified file is a Visual Basic source file; otherwise, false.</returns>
      <param name="fileName">The name of the file to examine.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Csc">
      <summary>Implements the Csc task. Use the Csc element in your project file to create and execute this task. For usage and parameter information, see Csc Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Csc.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Csc" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Csc.AddResponseFileCommands(Microsoft.Build.Tasks.CommandLineBuilderExtension)">
      <summary>Fills the specified <paramref name="commandLine" /> parameter with switches and other information that can go into a response file.</summary>
      <param name="commandLine">Command line builder to add arguments to.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.AllowUnsafeBlocks">
      <summary>Gets or sets a Boolean value that indicates whether to compile code that uses the unsafe keyword.</summary>
      <returns>true if will compile code that uses the unsafe keyword; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.ApplicationConfiguration">
      <summary>Returns configuration information.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.BaseAddress">
      <summary>Gets or sets the preferred base address at which to load a DLL.</summary>
      <returns>The preferred base address at which to load a DLL.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Csc.CallHostObjectToExecute">
      <summary>Compiles the project through the host object.</summary>
      <returns>true if compilation succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.CheckForOverflowUnderflow">
      <summary>Gets or sets a Boolean value that indicates whether the Csc task should cause an exception at run time for integer arithmetic that overflows the bounds of the data type.</summary>
      <returns>Gets or sets a Boolean value that indicates whether true if the Csc task should cause an exception at run time for integer arithmetic that overflows the bounds of the data type.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.DisabledWarnings">
      <summary>Gets or sets the list of warnings to be disabled.</summary>
      <returns>The list of warnings to be disabled.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.DocumentationFile">
      <summary>Gets or sets the XML file to hold the documentation comments.</summary>
      <returns>The XML file to hold the documentation comments.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.ErrorEndLocation">
      <summary>If true, outputs the line and column of the end location of each error.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.ErrorReport">
      <summary>Gets or sets the method to report a C# internal compiler error to Microsoft.</summary>
      <returns>The method to report a C# internal compiler error to Microsoft.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.GenerateFullPaths">
      <summary>Gets or sets a Boolean value that indicates whether to generate the absolute path to the file in the compiler output (true) or to generate the name of the file in the compiler output (false).</summary>
      <returns>true if the Csc task should generate the absolute path to the file in the compiler output; false if the Csc task should generate the name of the file in the compiler output.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Csc.GenerateFullPathToTool">
      <summary>Returns the full file path of the Csc tool.</summary>
      <returns>The full file path of the Csc tool.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Csc.InitializeHostObject">
      <summary>Returns a host object initialization status value that indicates what the next action should be.</summary>
      <returns>A host object initialization status value that indicates what the next action should be.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.LangVersion">
      <summary>Gets or sets the version of the language to use.</summary>
      <returns>The version of the language to use.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.ModuleAssemblyName">
      <summary>Gets or sets an assembly whose non-public types a .netmodule can access.</summary>
      <returns>An assembly whose non-public types a .netmodule can access.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.NoStandardLib">
      <summary>Gets or sets a Boolean value that indicates whether the Csc task should prevent the import of mscorlib.dll, which defines the entire System namespace.</summary>
      <returns>true if the Csc task should prevent the import of mscorlib.dll; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.PdbFile">
      <summary>Gets or sets the path of the .pdb file.</summary>
      <returns>The path of the .pdb file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.PreferredUILang">
      <summary>The name of the language passed to the “/preferreduilang” compiler option.</summary>
      <returns>Returns the name of the language.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.ToolName">
      <summary>Returns the name of the Csc tool (csc.exe).</summary>
      <returns>The name of the Csc tool (csc.exe).</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.UseHostCompilerIfAvailable">
      <summary>Gets or sets a Boolean value that indicates whether the Csc task should use the in-process compiler object, if available.</summary>
      <returns>true if the Csc task should use the in-process compiler object, if available; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.WarningLevel">
      <summary>Gets or sets the warning level for the compiler to display.</summary>
      <returns>The warning level for the compiler to display.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.WarningsAsErrors">
      <summary>Gets or sets a list of warnings to treat as errors.</summary>
      <returns>The list of warnings to treat as errors.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Csc.WarningsNotAsErrors">
      <summary>Gets or sets a list of warnings that are not treated as errors.</summary>
      <returns>The list of warnings that are not treated as errors.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Delete">
      <summary>Implements the Delete task. Use the Delete element in your project file to create and execute this task. For usage and parameter information, see Delete Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Delete.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Delete" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Delete.Cancel">
      <summary>Stop and return (in an undefined state) as soon as possible.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Delete.DeletedFiles">
      <summary>Gets or sets the files that were successfully deleted.</summary>
      <returns>The files that were successfully deleted.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Delete.Execute">
      <summary>Executes the Delete task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Delete.Files">
      <summary>Gets or sets the files to delete.</summary>
      <returns>The files to delete.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Delete.TreatErrorsAsWarnings">
      <summary>Gets or sets a Boolean value that indicates whether errors are logged as warnings.</summary>
      <returns>true if errors are logged as warnings; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Error">
      <summary>Implements the Error task. Use the Error element in your project file to create and execute this task. For usage and parameter information, see Delete Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Error.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Error" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Error.Code">
      <summary>Gets or sets the error code to associate with the error.</summary>
      <returns>The error code to associate with the error.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Error.Execute">
      <summary>Executes the Error task - logs an error and stops the build.</summary>
      <returns>false in all cases.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Error.File">
      <summary>Relevant file if any. If none is provided, the file containing the Error task will be used.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Error.HelpKeyword">
      <summary>Gets or sets the Help keyword to associate with the error.</summary>
      <returns>The Help keyword to associate with the error.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Error.Text">
      <summary>Gets or sets the error text.</summary>
      <returns>The error text.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Exec">
      <summary>Implements the Exec task. Use the Exec element in your project file to create and execute this task. For usage and parameter information, see Exec Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Exec.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Exec" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Exec.AddCommandLineCommands(Microsoft.Build.Tasks.CommandLineBuilderExtension)">
      <summary>Generates command line arguments that the command line tool must run directly from the command line and not from a response file.</summary>
      <param name="commandLine">Command line builder to add arguments to.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.Command">
      <summary>Gets or sets the command to run.</summary>
      <returns>The command to run.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.ConsoleOutput">
      <summary>Returns the output as an Item. Whitespace is trimmed. ConsoleOutput is enabled when ConsoleToMSBuild is true. This avoids holding lines in memory if they aren't used. ConsoleOutput is a combination of stdout and stderr.</summary>
      <returns>Returns <see cref="T:Microsoft.Build.Framework.ITaskItem" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.ConsoleToMSBuild">
      <summary>Enable the standard output to pipe to an item. (StandardOutput).</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.CustomErrorRegularExpression">
      <summary>Gets or sets a regular expression to spot error lines in the tool output.</summary>
      <returns>A regular expression to spot error lines in the tool output.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.CustomWarningRegularExpression">
      <summary>Gets or sets a regular expression to spot warning lines in the tool output.</summary>
      <returns>A regular expression to spot warning lines in the tool output.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Exec.ExecuteTool(System.String,System.String,System.String)">
      <summary>Executes the cmd.exe tool, waits for its completion, and cleans up the batch file after execution completes.</summary>
      <returns>The exit code that the tool returns.</returns>
      <param name="pathToTool">The path to the tool to execute.</param>
      <param name="responseFileCommands">Command line arguments that should go into a temporary response file.</param>
      <param name="commandLineCommands">Command line arguments that should be passed to the tool directly.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Exec.GenerateFullPathToTool">
      <summary>Returns the full path to the system tool cmd.exe.</summary>
      <returns>The full path to the system tool cmd.exe.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Exec.GetWorkingDirectory">
      <summary>Returns the working directory to use for the tool process.</summary>
      <returns>The working directory to use for the tool process.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Exec.HandleTaskExecutionErrors">
      <summary>Logs error message and handles exit code.</summary>
      <returns>true if exit code was ignored; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.IgnoreExitCode">
      <summary>Gets or sets a Boolean value that indicates whether the Exec task should ignore the exit code provided by the executed command (true) or should return false if the executed command returns a non-zero exit code (false).</summary>
      <returns>true if the Exec task should ignore the exit code provided by the executed command; false if the Exec task should return false if the executed command returns a non-zero exit code.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.IgnoreStandardErrorWarningFormat">
      <summary>Gets or sets a Boolean value that indicates whether to ignore the standard error and warning format and log errors and warnings from the output as regular messages.</summary>
      <returns>true if the Exec task should ignore the standard error and warning format and should log errors and warnings as regular messages; false if the Exec task should log the errors and warnings that match the standard error and warning format.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Exec.LogEventsFromTextOutput(System.String,Microsoft.Build.Framework.MessageImportance)">
      <summary>Logs the specified single line of text as an error or warning if the single line matched custom or standard error and warning formats or as a regular message if <see cref="P:Microsoft.Build.Tasks.Exec.IgnoreStandardErrorWarningFormat" /> is set to true and no custom format matched.</summary>
      <param name="singleLine">Single line from the output to parse.</param>
      <param name="messageImportance">The importance to log the message with.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Exec.LogPathToTool(System.String,System.String)">
      <summary>Logs the tool name and path.</summary>
      <param name="toolName">The tool name.</param>
      <param name="pathToTool">The current directory where the tool is run.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Exec.LogToolCommand(System.String)">
      <summary>Logs the batch file command to be executed.</summary>
      <param name="message">Specify an empty string. This parameter is not used.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.Outputs">
      <summary>Gets or sets the output items from the task.</summary>
      <returns>The output items from the task.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.StandardErrorEncoding">
      <summary>Gets the encoding of the captured task standard error stream.</summary>
      <returns>The encoding of the captured task standard error stream.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.StandardErrorLoggingImportance">
      <summary>Gets the importance to log ordinary messages with in the standard error stream.</summary>
      <returns>The importance to log ordinary messages with in the standard error stream.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.StandardOutputEncoding">
      <summary>Gets the encoding of the captured task standard output stream.</summary>
      <returns>The encoding of the captured task standard output stream.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.StandardOutputLoggingImportance">
      <summary>Gets the importance to log ordinary messages with in the standard out stream.</summary>
      <returns>The importance to log ordinary messages with in the standard out stream.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.StdErrEncoding">
      <summary>Gets or sets the encoding of the captured task standard error stream that is visible to the project.</summary>
      <returns>The encoding of the captured task standard error stream that is visible to the project.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.StdOutEncoding">
      <summary>Gets or sets the encoding of the captured task standard output stream that is visible to the project.</summary>
      <returns>The encoding of the captured task standard output stream that is visible to the project.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.ToolName">
      <summary>Gets the name of the tool (cmd.exe).</summary>
      <returns>The name of the tool (cmd.exe).</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Exec.ValidateParameters">
      <summary>Validates input parameters and logs errors or warnings, if any. Returns a Boolean value that indicates whether task execution should proceed.</summary>
      <returns>true if task execution should proceed; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Exec.WorkingDirectory">
      <summary>Gets or sets the directory in which the command will run.</summary>
      <returns>The directory in which the command will run.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.ExtractedClassName">
      <summary>Extracts a class name from a language source file.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.ExtractedClassName.IsInsideConditionalBlock">
      <summary>Gets or sets a value indicating whether the extracted class name exists inside a block of conditionally compiled code.</summary>
      <returns>true to indicate the extracted class name exists inside a block of conditionally compiled code; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ExtractedClassName.Name">
      <summary>Gets or sets the extracted class name.</summary>
      <returns>The extracted class name.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.FindAppConfigFile">
      <summary>Finds the app.config file, if any, in the provided lists.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.FindAppConfigFile.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.FindAppConfigFile" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindAppConfigFile.AppConfigFile">
      <summary>Gets or sets the first matching item found in the list, if any.</summary>
      <returns>The first matching item found in the list, or null if no matching item is found.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.FindAppConfigFile.Execute">
      <summary>Finds the app config file.</summary>
      <returns>true in all cases.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindAppConfigFile.PrimaryList">
      <summary>Gets or sets the primary list to search through.</summary>
      <returns>The primary list to search through.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindAppConfigFile.SecondaryList">
      <summary>Gets or sets the secondary list to search through.</summary>
      <returns>The secondary list to search through.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindAppConfigFile.TargetPath">
      <summary>Gets or sets the value to add as <see cref="P:Microsoft.Build.Tasks.FindAppConfigFile.TargetPath" /> metadata.</summary>
      <returns>The <see cref="P:Microsoft.Build.Tasks.FindAppConfigFile.TargetPath" /> metadata.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.FindInList">
      <summary>In a specified list, finds an item that has the matching itemspec.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.FindInList.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.FindInList" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindInList.CaseSensitive">
      <summary>Gets or sets a Boolean value that indicates whether to match case sensitively.</summary>
      <returns>trueFindInList task should match case sensitively; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.FindInList.Execute">
      <summary>Executes the FindInList task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindInList.FindLastMatch">
      <summary>Gets or sets a Boolean value that indicates whether to return the last match (true) or to return the first match (false).</summary>
      <returns>true if the FindInList task should return the last match; false if the FindInList task should return the first match.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindInList.ItemFound">
      <summary>Gets or sets the first matching item found in the list, if any.</summary>
      <returns>The first matching item found in the list.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindInList.ItemSpecToFind">
      <summary>Gets or sets the itemspec to search for.</summary>
      <returns>The itemspec to search for.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindInList.List">
      <summary>Gets or sets the list in which to search for the itemspec.</summary>
      <returns>The list in which to search for the itemspec.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindInList.MatchFileNameOnly">
      <summary>Gets or sets a Boolean value that indicates whether to match against just the file name part of the itemspec or to match against the whole itemspec.</summary>
      <returns>true if the FindInList task should match against just the file name part of the itemspec; false if the FindInList task should match against the whole itemspec.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.FindUnderPath">
      <summary>Implements the FindUnderPath task. Use the FindUnderPath element in your project file to create and execute this task. For usage and parameter information, see FindUnderPath Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.FindUnderPath.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.FindUnderPath" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.FindUnderPath.Execute">
      <summary>Executes the FileUnderPath task.</summary>
      <returns>true if task executes successfully; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindUnderPath.Files">
      <summary>Gets or sets the files whose paths should be compared with the path specified by the <see cref="P:Microsoft.Build.Tasks.FindUnderPath.Path" /> property.</summary>
      <returns>The files whose paths should be compared with the path specified by the <see cref="P:Microsoft.Build.Tasks.FindUnderPath.Path" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindUnderPath.InPath">
      <summary>Gets or sets the items that were found under the specified path.</summary>
      <returns>The items that were found under the specified path.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindUnderPath.OutOfPath">
      <summary>Gets or sets the items that were not found under the specified path.</summary>
      <returns>The items that were not found under the specified path.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindUnderPath.Path">
      <summary>Gets or sets the folder path to use as the reference.</summary>
      <returns>The folder path to use as the reference.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FindUnderPath.UpdateToAbsolutePaths">
      <summary>Gets or sets a Boolean value that indicates whether the paths of the output items should be updated to be absolute.</summary>
      <returns>true if the paths of the output items should be updated to be absolute; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.FormatUrl">
      <summary>Converts a URL into a proper URL format.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.FormatUrl.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.FormatUrl" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.FormatUrl.Execute">
      <summary>Executes the FormatUrl task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FormatUrl.InputUrl">
      <summary>Gets or sets the URL to format.</summary>
      <returns>The URL to format.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FormatUrl.OutputUrl">
      <summary>Gets or sets the formatted URL.</summary>
      <returns>The formatted URL.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.FormatVersion">
      <summary>Appends the revision number to the version number.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.FormatVersion.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.FormatVersion" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.FormatVersion.Execute">
      <summary>Executes the FormatVersion task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FormatVersion.FormatType">
      <summary>Gets or sets the format type.</summary>
      <returns>The format type.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FormatVersion.OutputVersion">
      <summary>Gets or sets the output version that includes the revision number.</summary>
      <returns>The output version that includes the revision number.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FormatVersion.Revision">
      <summary>Gets or sets the revision to append to the version.</summary>
      <returns>The revision to append to the version.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.FormatVersion.Version">
      <summary>Gets or sets the version number string to format.</summary>
      <returns>The version number string to format.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.GenerateApplicationManifest">
      <summary>Implements the GenerateApplicationManifest task. Use the GenerateApplicationManifest element in the project file to create and execute this task. For usage and parameter information, see GenerateApplicationManifest Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateApplicationManifest.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.GenerateApplicationManifest" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.ClrVersion">
      <summary>Gets or sets the minimum version of the Common Language Runtime (CLR) required by the application.</summary>
      <returns>The minimum version of the CLR required by the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.ConfigFile">
      <summary>Gets or sets the task item that contains the application configuration file.</summary>
      <returns>The task item that contains the application configuration file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.Dependencies">
      <summary>Gets or sets an item list that defines the set of dependent assemblies for the generated manifest.</summary>
      <returns>An item list of dependent assembly names.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.ErrorReportUrl">
      <summary>Gets or sets the URL of the Web page that is displayed in dialog boxes during ClickOnce installations.</summary>
      <returns>A string value that represents the URL of a Web page.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.FileAssociations">
      <summary>Gets or sets a list of one or more file type that are associated with the ClickOnce deployment manifest. File associations only valid only when .NET Framework 3.5 or later is targeted.</summary>
      <returns>A list of file types that are associated with the generated manifest.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.Files">
      <summary>Gets or sets the full paths of files to include in the manifest.</summary>
      <returns>Paths to files that are included in the manifest.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.HostInBrowser">
      <summary>Gets or sets whether the application is hosted in a browser (as are WPF Web Browser Applications).</summary>
      <returns>A Boolean value that indicates whether the application being deployed will be hosted in a browser (true), or not (false). true only applies to WPF Web Browser Applications.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.IconFile">
      <summary>Gets or sets the application's icon file.</summary>
      <returns>The icon file to use for the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.IsolatedComReferences">
      <summary>Gets or sets COM components to isolate in the generated manifest. </summary>
      <returns>The COM components to isolate in the generated manifest.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.ManifestType">
      <summary>Gets or sets the type of manifest to generate. </summary>
      <returns>The kind of manifest to generate.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.OSVersion">
      <summary>Gets or sets the operating system (OS) version that is the minimum required by the application. </summary>
      <returns>The OS version that is the minimum required by the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.Product">
      <summary>Specifies the ProductName property in the project file.</summary>
      <returns>A string value that corresponds to the ProductName property in the project file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.Publisher">
      <summary>Specifies the PublisherName property in the project file.</summary>
      <returns>A string value that corresponds to the PublisherName property in the project file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.RequiresMinimumFramework35SP1">
      <summary>Gets or sets whether the application requires the .NET Framework 3.5 SP1.</summary>
      <returns>true if the application requires .NET Framework 3.5 SP1; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.SuiteName">
      <summary>Gets or sets the name of the folder on the Start menu where the application is located after ClickOnce deployment.</summary>
      <returns>A string that represents the name of a Start menu folder.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.SupportUrl">
      <summary>Specifies the SupportUrl property in the project file.</summary>
      <returns>A string value that corresponds to the SupportUrl property in the project file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.TargetFrameworkProfile">
      <summary>Gets or sets the target framework profile.</summary>
      <returns>Returns the target framework profile.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.TargetFrameworkSubset">
      <summary>Gets or sets the name of the .NET Framework subset to target.</summary>
      <returns>The name of the .NET Framework subset.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.TrustInfoFile">
      <summary>Gets or sets an XML document that specifies the application security.</summary>
      <returns>The XML document that specifies the application security.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateApplicationManifest.UseApplicationTrust">
      <summary>Specifies whether the <see cref="P:Microsoft.Build.Tasks.GenerateApplicationManifest.Product" />, <see cref="P:Microsoft.Build.Tasks.GenerateApplicationManifest.Publisher" />, and <see cref="P:Microsoft.Build.Tasks.GenerateApplicationManifest.SupportUrl" /> properties are written to the application manifest.</summary>
      <returns>A Boolean value that indicates whether the <see cref="P:Microsoft.Build.Tasks.GenerateApplicationManifest.Product" />, <see cref="P:Microsoft.Build.Tasks.GenerateApplicationManifest.Publisher" />, and <see cref="P:Microsoft.Build.Tasks.GenerateApplicationManifest.SupportUrl" /> properties are written to the application manifest (true). If the value is false, these properties are ignored.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.GenerateBootstrapper">
      <summary>Implements the GenerateBootstrapper task. Use the GenerateBootstrapper element in your project file to create and execute this task. For usage and parameter information, see GenerateBootstrapper Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateBootstrapper.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.GenerateBootstrapper" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.ApplicationFile">
      <summary>Gets or sets the file the bootstrapper will use to begin the installation of the application after all prerequisites have been installed.</summary>
      <returns>The file the bootstrapper will use to begin the installation of the application after all prerequisites have been installed.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.ApplicationName">
      <summary>Gets or sets the name of the application that the bootstrapper will install.</summary>
      <returns>The name of the application that the bootstrapper will install.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.ApplicationRequiresElevation">
      <summary>Gets or sets whether a component runs with elevated permissions when it is installed on a target computer.</summary>
      <returns>Returns True if the installed component runs with elevated permissions; otherwise False.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.ApplicationUrl">
      <summary>Gets or sets the Web location that is hosting the application’s installer.</summary>
      <returns>The Web location that is hosting the application’s installer.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.BootstrapperComponentFiles">
      <summary>Gets or sets the built location of bootstrapper package files.</summary>
      <returns>The built location of bootstrapper package files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.BootstrapperItems">
      <summary>Gets or sets the products to build into the bootstrapper.</summary>
      <returns>The products to build into the bootstrapper.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.BootstrapperKeyFile">
      <summary>Gets or sets the built location of setup.exe.</summary>
      <returns>The built location of setup.exe.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.ComponentsLocation">
      <summary>Gets or sets a location for the bootstrapper to look for installation prerequisites to install.</summary>
      <returns>The location for the bootstrapper to look for installation prerequisites to install.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.ComponentsUrl">
      <summary>Gets or sets the URL containing the installation prerequisites.</summary>
      <returns>The URL containing the installation prerequisites.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.CopyComponents">
      <summary>Gets or sets a Boolean value that indicates whether the bootstrapper copies all output files to the path specified in the <see cref="P:Microsoft.Build.Tasks.GenerateBootstrapper.OutputPath" /> property. </summary>
      <returns>true if the bootstrapper should copy all output files to the path specified in the <see cref="P:Microsoft.Build.Tasks.GenerateBootstrapper.OutputPath" /> property; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.Culture">
      <summary>Gets or sets the culture to use for the bootstrapper UI and installation prerequisites. </summary>
      <returns>The culture to use for the bootstrapper UI and installation prerequisites.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateBootstrapper.Execute">
      <summary>Executes the GenerateBootstrapper task.</summary>
      <returns>true if the task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.FallbackCulture">
      <summary>Gets or sets the secondary culture to use for the bootstraper UI and installation prerequisites.</summary>
      <returns>The secondary culture to use for the bootstraper UI and installation prerequisites.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.OutputPath">
      <summary>Gets or sets the location to copy setup.exe and all package files.</summary>
      <returns>The location to copy setup.exe and all package files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.Path">
      <summary>Gets or sets the location of all available prerequisite packages.</summary>
      <returns>The location of all available prerequisite packages.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.SupportUrl">
      <summary>Gets or sets the URL to provide should the bootstrapper installation fail.</summary>
      <returns>The URL to provide should the bootstrapper installation fail.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.Validate">
      <summary>Gets or sets a Boolean value that indicates whether the bootstrapper performs XSD validation on the specified input bootstrapper items.</summary>
      <returns>true if the bootstrapper performs XSD validation on the specified input bootstrapper items; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateBootstrapper.VisualStudioVersion">
      <summary>The Visual Studio version, in the form &lt;major build.minor build&gt;, for example “4.0”.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.GenerateDeploymentManifest">
      <summary>Implements the GenerateDeploymentManifest task. Use the GenerateDeploymentManifest element in the project file to create and execute this task. For usage and parameter information, see GenerateDeploymentManifest Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateDeploymentManifest.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.GenerateDeploymentManifest" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.CreateDesktopShortcut">
      <summary>Gets or sets whether an icon is created on the desktop during ClickOnce application installation.</summary>
      <returns>true if an icon is created on the desktop during installation; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.DeploymentUrl">
      <summary>Gets or sets the update location for the application.</summary>
      <returns>The update location for the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.DisallowUrlActivation">
      <summary>Gets or sets a Boolean value that indicates whether the application should be run automatically when it is opened through a URL.</summary>
      <returns>true if the application can only be started from the Start menu; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.ErrorReportUrl">
      <summary>Gets or sets the URL of the Web page that is displayed in dialog boxes during ClickOnce installations.</summary>
      <returns>A string that represents the URL of a Web page.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.Install">
      <summary>Gets or sets a Boolean that indicates whether the application is an installed application or an online-only application.</summary>
      <returns>true if the application is to be installed on the local computer and can be removed by using the Add or Remove Programs dialog box; false if the application is for online use from a Web page.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.MapFileExtensions">
      <summary>Gets or sets a Boolean value that indicates whether the .deploy file name extension mapping is used.</summary>
      <returns>true if every program file is published with a .deploy file name extension; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.MinimumRequiredVersion">
      <summary>Gets or sets a Boolean value that indicates whether the user can skip the update.</summary>
      <returns>true if the user can skip the update; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.Product">
      <summary>Gets or sets the name of the application.</summary>
      <returns>The name of the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.Publisher">
      <summary>Gets or sets the publisher of the application.</summary>
      <returns>The publisher of the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.SuiteName">
      <summary>Gets or sets the name of the folder on the Start menu where the application is located after ClickOnce deployment.</summary>
      <returns>A string that represents the name of a Start menu folder.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.SupportUrl">
      <summary>Gets or sets the link that appears in the Uninstall or change a program dialog box for the application. The specified value should be a fully qualified URL or UNC path.</summary>
      <returns>The link that appears in the Uninstall or change a program dialog box for the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.TrustUrlParameters">
      <summary>Gets or sets a Boolean value that indicates whether URL query-string parameters should be made available to the application.</summary>
      <returns>true if URL query-string parameters should be made available to the application; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.UpdateEnabled">
      <summary>Gets or sets a Boolean value that indicates whether the application is enabled for updates.</summary>
      <returns>true if the application is enabled for updates; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.UpdateInterval">
      <summary>Gets or sets the update interval for the application. </summary>
      <returns>The update interval for the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.UpdateMode">
      <summary>Gets or sets a Boolean value that indicates whether updates should be checked. Updates can be checked before the application is started (in the foreground), or when the application is running (in the background).</summary>
      <returns>true if updates should be checked; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.UpdateUnit">
      <summary>Gets or sets the time unit for the <see cref="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.UpdateInterval" /> property.</summary>
      <returns>The time unit for the <see cref="P:Microsoft.Build.Tasks.GenerateDeploymentManifest.UpdateInterval" /> property.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.GenerateManifestBase">
      <summary>Represents the base class for all manifest generation tasks.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateManifestBase.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.GenerateManifestBase" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateManifestBase.AddAssemblyFromItem(Microsoft.Build.Framework.ITaskItem)">
      <summary>Adds an assembly reference to the manifest, based on the specified item.</summary>
      <returns>The assembly reference that was added to the manifest.</returns>
      <param name="item">The item from which to create an assembly reference to add to the manifest.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateManifestBase.AddAssemblyNameFromItem(Microsoft.Build.Framework.ITaskItem,Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceType)">
      <summary>Adds an assembly reference to the manifest, based on the specified item and the assembly reference type.</summary>
      <returns>The assembly reference that was added to the manifest.</returns>
      <param name="item">The item from which to create an assembly reference to add to the manifest.</param>
      <param name="referenceType">The assembly reference type.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateManifestBase.AddEntryPointFromItem(Microsoft.Build.Framework.ITaskItem,Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceType)">
      <summary>Adds an entry point reference to the manifest, based on the specified item and the assembly reference type.</summary>
      <returns>The entry point reference that was added to the manifest.</returns>
      <param name="item">The item from which to create the entry point reference to add to the manifest.</param>
      <param name="referenceType">The entry point assembly reference type.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateManifestBase.AddFileFromItem(Microsoft.Build.Framework.ITaskItem)">
      <summary>Adds a file reference to the manifest, based on the specified item.</summary>
      <returns>The file reference that was added to the manifest.</returns>
      <param name="item">The item from which to create the file reference to add to the manifest.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateManifestBase.AssemblyName">
      <summary>Gets or sets the name of the assembly.</summary>
      <returns>The name of the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateManifestBase.AssemblyVersion">
      <summary>Gets or sets the assembly version.</summary>
      <returns>The assembly version.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateManifestBase.Description">
      <summary>Gets or sets the manifest description text.</summary>
      <returns>The manifest description text.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateManifestBase.EntryPoint">
      <summary>Gets or sets the managed assembly or ClickOnce manifest reference that is the entry point to the manifest.</summary>
      <returns>The managed assembly or ClickOnce manifest reference that is the entry point to the application.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateManifestBase.Execute">
      <summary>Executes the GenerateManifestBase task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateManifestBase.FindFileFromItem(Microsoft.Build.Framework.ITaskItem)">
      <summary>Returns the manifest file reference that matches the specified item.</summary>
      <returns>The manifest file reference that matches the specified item.</returns>
      <param name="item">The item to get a file reference for.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateManifestBase.GetObjectType">
      <summary>When implemented in a derived class, returns the type of the manifest.</summary>
      <returns>The manifest type.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateManifestBase.InputManifest">
      <summary>Gets or sets the input manifest.</summary>
      <returns>The input manifest.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateManifestBase.MaxTargetPath">
      <summary>Gets or sets the maximum manifest file name length.</summary>
      <returns>The maximum manifest file name length.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateManifestBase.OnManifestLoaded(Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest)">
      <summary>When implemented in a derived class, sets manifest properties and any dependencies.</summary>
      <returns>true if no errors occurred when the manifest properties are set; otherwise, false.</returns>
      <param name="manifest">The manifest to set properties for.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateManifestBase.OnManifestResolved(Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest)">
      <summary>When implemented in a derived class, sets resolved manifest properties.</summary>
      <returns>true if no errors occurred when the manifest properties are set; otherwise, false.</returns>
      <param name="manifest">The manifest to set properties for.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateManifestBase.OutputManifest">
      <summary>Gets or sets the generated manifest.</summary>
      <returns>The generated manifest.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateManifestBase.Platform">
      <summary>Gets or sets the manifest platform.</summary>
      <returns>The manifest platform.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateManifestBase.TargetCulture">
      <summary>Gets or sets the manifest target culture code.</summary>
      <returns>The manifest target culture code.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateManifestBase.TargetFrameworkMoniker">
      <summary>Gets or sets the target framework moniker.</summary>
      <returns>Returns the target framework moniker.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateManifestBase.TargetFrameworkVersion">
      <summary>The target .NET Framework version for the project.</summary>
      <returns>A string that represents the .NET Framework version.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateManifestBase.ValidateInputs">
      <summary>Validates the inputs of the GenerateManifestBase task.</summary>
      <returns>true if input validation succeeded; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateManifestBase.ValidateOutput">
      <summary>Validates the generated manifest.</summary>
      <returns>true if manifest validation succeeded; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.GenerateResource">
      <summary>Implements the GenerateResource task. Use the GenerateResource element in your project file to create and execute this task. For usage and parameter information, see GenerateResource Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateResource.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.GenerateResource" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.AdditionalInputs">
      <summary>Gets or sets the additional inputs for the dependency checking that the GenerateResource task performs.</summary>
      <returns>The additional inputs for the dependency checking that the GenerateResource task performs.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.EnvironmentVariables">
      <summary>Gets or sets an array of name-value pairs of environment variables that should be passed to the spawned resgen.exe, in addition to (or selectively overriding) the regular environment block.</summary>
      <returns>Returns an array of name-value pairs of environment variables.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.ExcludedInputPaths">
      <summary>Gets or sets an array of items that specify paths from which tracked inputs will be ignored during Up to date checking.</summary>
      <returns>Returns an array of items that specify paths from which tracked inputs will be ignored during Up to date checking.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateResource.Execute">
      <summary>Executes the GenerateResource task.</summary>
      <returns>true if the GenerateResource task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.ExecuteAsTool">
      <summary>Property to allow multitargeting of ResolveComReferences: If true, tlbimp.exe and aximp.exe from the appropriate target framework will be run out-of-proc to generate the necessary wrapper assemblies.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.ExtractResWFiles">
      <summary>Gets or sets a flag specifying whether this rule is generating .resources files or extracting .ResW files from assemblies.</summary>
      <returns>Returns the flag.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.FilesWritten">
      <summary>Gets or sets the names of all files written to disk including the cache file, if present.</summary>
      <returns>The names of all files written to disk.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.MinimalRebuildFromTracking">
      <summary>Gets or sets a switch that specifies whether tracked incremental build will be used. If true, incremental build is turned on; otherwise, a rebuild will be forced.</summary>
      <returns>Returns a switch that specifies whether tracked incremental build will be used. If true, incremental build is turned on; otherwise, a rebuild will be forced.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.NeverLockTypeAssemblies">
      <summary>Gets or sets a Boolean value that specifies whether to create a new <see cref="T:System.AppDomain" /> to evaluate the resources (.resx) files (true) or to create a new <see cref="T:System.AppDomain" /> only when the resources files reference a user's assembly (false).</summary>
      <returns>true if the GenerateResource task should create a new <see cref="T:System.AppDomain" /> to evaluate the resources (.resx) files; false if the GenerateResource task should create a new <see cref="T:System.AppDomain" /> only when the resources (.resx) files reference a user's assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.OutputDirectory">
      <summary>Gets or sets a value specifying where to extract .ResW files.</summary>
      <returns>Returns the value.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.OutputResources">
      <summary>Gets or sets the name of the generated files, such as .resources files. </summary>
      <returns>The name of the generated files, such as .resources files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.PublicClass">
      <summary>Gets or sets a Boolean value that indicates whether the GenerateResource task should create a strongly typed resource class as a public class.</summary>
      <returns>true if the GenerateResource task should create a strongly typed resource class as a public class; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.References">
      <summary>Gets or sets the references to load types in .resx files from.</summary>
      <returns>The references to load types in .resx files from.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.SdkToolsPath">
      <summary>Even though the generate resource task will do the processing in process, a logging message is still generated. This logging message will include the path to the windows SDK. Since the targets now will pass in the Windows SDK path we should use this for logging.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.Sources">
      <summary>Gets or sets the items to convert.</summary>
      <returns>The items to convert.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.StateFile">
      <summary>Gets or sets the path to an optional cache file that is used to speed up dependency checking of links in .resx input files.</summary>
      <returns>The path to an optional cache file that is used to speed up dependency checking of links in .resx input files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.StronglyTypedClassName">
      <summary>Gets or sets the class name for the strongly typed resource class.</summary>
      <returns>The class name for the strongly typed resource class.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.StronglyTypedFileName">
      <summary>Gets or sets the filename for the source file.</summary>
      <returns>The filename for the source file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.StronglyTypedLanguage">
      <summary>Gets or sets the language to use when generating the class source for the strongly typed resource.</summary>
      <returns>The language to use when generating the class source for the strongly typed resource.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.StronglyTypedManifestPrefix">
      <summary>Gets or sets the resource namespace or manifest prefix to use in the generated class source for the strongly typed resource.</summary>
      <returns>The resource namespace or manifest prefix to use in the generated class source for the strongly typed resource.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.StronglyTypedNamespace">
      <summary>Gets or sets the namespace to use for the generated class source for the strongly typed resource.</summary>
      <returns>The namespace to use for the generated class source for the strongly typed resource.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.TLogReadFiles">
      <summary>Gets an array of items that represent the read tracking logs.</summary>
      <returns>Returns an array of items that represent the read tracking logs.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.TLogWriteFiles">
      <summary>Gets an array of items that represent the write tracking logs.</summary>
      <returns>Returns an array of items that represent the write tracking logs.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.ToolArchitecture">
      <summary>Microsoft.Build.Utilities.ExecutableType of ResGen.exe. Used to determine whether or not Tracker.exe needs to be used to spawn ResGen.exe. If empty, uses a heuristic to determine a default architecture.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.TrackerFrameworkPath">
      <summary>Path to the appropriate .NET Framework location that contains FileTracker.dll. If set, the user takes responsibility for making sure that the bitness of the FileTracker.dll that they pass matches the bitness of the ResGen.exe that they intend to use. If not set, the task decides the appropriate location based on the current .NET Framework version.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.TrackerLogDirectory">
      <summary>Gets or sets the intermediate directory into which the tracking logs from running this task will be placed.</summary>
      <returns>Returns the intermediate directory into which the tracking logs from running this task will be placed.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.TrackerSdkPath">
      <summary>Path to the appropriate Windows SDK location that contains Tracker.exe. If set, the user takes responsibility for making sure that the bitness of the Tracker.exe that they pass matches the bitness of the ResGen.exe that they intend to use. If not set, the task decides the appropriate location based on the current Windows SDK.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.TrackFileAccess">
      <summary>Gets or sets a switch that specifies whether we should be tracking file access patterns.</summary>
      <returns>True if we should be tracking file access patterns; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateResource.UseSourcePath">
      <summary>Gets or sets a Boolean value that indicates whether the input file's directory is to be used for resolving relative file paths.</summary>
      <returns>true if the input file's directory is to be used for resolving relative file paths; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.GenerateTrustInfo">
      <summary>Generates the application trust from the base manifest, and from the <see cref="P:Microsoft.Build.Tasks.GenerateTrustInfo.TargetZone" /> and <see cref="P:Microsoft.Build.Tasks.GenerateTrustInfo.ExcludedPermissions" /> properties.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateTrustInfo.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.GenerateTrustInfo" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateTrustInfo.ApplicationDependencies">
      <summary>Gets or sets the dependent assemblies.</summary>
      <returns>The dependent assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateTrustInfo.BaseManifest">
      <summary>Gets or sets the base manifest to generate the application trust from.</summary>
      <returns>The base manifest to generate the application trust from.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateTrustInfo.ExcludedPermissions">
      <summary>Gets or sets one or more permission identity values separated by a semicolon to be excluded from the zone default permission set.</summary>
      <returns>The permission identity values to be excluded from the zone default permission set.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.GenerateTrustInfo.Execute">
      <summary>Executes the GenerateTrustInfo task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateTrustInfo.TargetFrameworkMoniker">
      <summary>Gets or sets the target framework moniker.</summary>
      <returns>Returns the target framework moniker.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateTrustInfo.TargetZone">
      <summary>Gets or sets a zone default permission set, which is obtained from machine policy.</summary>
      <returns>The zone default permission set.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GenerateTrustInfo.TrustInfoFile">
      <summary>Gets or sets the file that contains the application security trust information.</summary>
      <returns>The file that contains the application security trust information.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.GetAssemblyIdentity">
      <summary>Implements the GetAssemblyIdentity task. Use the GetAssemblyIdentity element in your project file to create and execute this task. For usage and parameter information, see GetAssemblyIdentity Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GetAssemblyIdentity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.GetAssemblyIdentity" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetAssemblyIdentity.Assemblies">
      <summary>Gets or sets the retrieved assembly identities.</summary>
      <returns>The retrieved assembly identities.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetAssemblyIdentity.AssemblyFiles">
      <summary>Gets or sets the files to retrieve identities from.</summary>
      <returns>The files to retrieve identities from.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.GetAssemblyIdentity.Execute">
      <summary>Executes the GetAssemblyIdentity task.</summary>
      <returns>true if the GetAssemblyIdentity task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.GetFrameworkPath">
      <summary>Implements the GetFrameworkPath task. Use the GetFrameworkPath element in your project file to create and execute this task. For usage and parameter information, see GetFrameworkPath Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GetFrameworkPath.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.GetFrameworkPath" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GetFrameworkPath.Execute">
      <summary>Returns true in all cases.</summary>
      <returns>true in all cases.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetFrameworkPath.FrameworkVersion11Path">
      <summary>Gets or sets the path of the folder that contains the .NET Framework 1.1 assemblies.</summary>
      <returns>The path of the folder that contains the .NET Framework 1.1 assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetFrameworkPath.FrameworkVersion20Path">
      <summary>Gets or sets the path of the folder that contains the .NET Framework 2.0 assemblies.</summary>
      <returns>The path of the folder that contains the .NET Framework 2.0 assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetFrameworkPath.FrameworkVersion30Path">
      <summary>Gets or sets the path of the folder that contains the .NET Framework 3.0 assemblies.</summary>
      <returns>The path of the folder that contains the .NET Framework 3.0 assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetFrameworkPath.FrameworkVersion35Path">
      <summary>Gets or sets the path of the folder that contains the .NET Framework 3.5 assemblies.</summary>
      <returns>The path of the folder that contains the .NET Framework 3.5 assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetFrameworkPath.FrameworkVersion40Path">
      <summary>Path to the v4.0 framework, if available</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetFrameworkPath.FrameworkVersion45Path">
      <summary>Path to the v4.5 framework, if available</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetFrameworkPath.Path">
      <summary>Gets or sets the path of the folder that contains the latest version of the .NET Framework assemblies.</summary>
      <returns>The path of the folder that contains the latest version of the .NET Framework assemblies.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.GetFrameworkSdkPath">
      <summary>Implements the GetFrameworkSdkPath task. Use the GetFrameworkSdkPath element in your project file to create and execute this task. For usage and parameter information, see GetFrameworkSdkPath Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GetFrameworkSdkPath.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.GetFrameworkSdkPath" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GetFrameworkSdkPath.Execute">
      <summary>Returns true in all cases.</summary>
      <returns>true in all cases.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetFrameworkSdkPath.FrameworkSdkVersion20Path">
      <summary>Gets or sets the path to the .NET Framework 2.0 SDK.</summary>
      <returns>The path to the .NET Framework 2.0 SDK.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetFrameworkSdkPath.FrameworkSdkVersion35Path">
      <summary>Gets or sets the path to the .NET Framework 3.5 SDK.</summary>
      <returns>The path to the .NET Framework 3.5 SDK.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetFrameworkSdkPath.FrameworkSdkVersion40Path">
      <summary>The path to the v4.0 .NET SDK if it could be found. It will be String.Empty if the SDK was not found.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetFrameworkSdkPath.FrameworkSdkVersion45Path">
      <summary>The path to the v4.5 .NET SDK if it could be found. It will be String.Empty if the SDK was not found.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetFrameworkSdkPath.Path">
      <summary>Gets or sets the path to the latest version of the .NET Framework SDK.</summary>
      <returns>The path to the latest version of the .NET Framework SDK.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.GetInstalledSDKLocations">
      <summary>Gathers the list of installed SDKS in the registry and on disk and outputs them into the project so they can be used during SDK reference resolution and RAR for single files.GetInstalledSDKLocations doesn't recognize a version folder if “v” appears in its name.  For example, an SDK that's located at C:\Windows\Program Files\Microsoft SDKs\Windows\8.0\SDKFolders\3rdPartySDK\2.5\will be located correctly. However, the same SDK located atC:\Windows\Program Files\Microsoft SDKs\Windows\8.0\SDKFolders\3rdPartySDK\v2.5\won't be located and will be ignored. If an SDK is ignored, the failure won't be recorded in the log, and no error will be thrown.Version folder names need to be formed as major.minor[.build[.revision]]. For more information, see <see cref="M:System.Version.TryParse(System.String,System.Version@)" />.To find an SDK with a particular version, the version sought must match the version requested. For example, if the SDK has a folder version name 8.0.0 and the customer requested the SDK with version 8.0, there is no match, and the SDK won't be found.For more information on version matching, see <see cref="M:System.Version.CompareTo(System.Version)" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GetInstalledSDKLocations.#ctor">
      <summary>Creates a new instance of GetInstalledSDKLocations.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GetInstalledSDKLocations.Execute">
      <summary>Get the SDKs.</summary>
      <returns>Returns true if the method succeeds; otherwise, returns false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetInstalledSDKLocations.InstalledSDKs">
      <summary>Gets or sets the set of items that represent all of the installed SDKs found in the SDKDirectory and SDKRegistry roots. The itemspec is the SDK install location. There is a piece of metadata called SDKName which contains the name of the SDK.</summary>
      <returns>Returns the set of all installed SDKs found.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetInstalledSDKLocations.SDKDirectoryRoots">
      <summary>Gets or sets the root directory on disk in which to look for SDKs.</summary>
      <returns>Returns the root directory.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetInstalledSDKLocations.SDKRegistryRoot">
      <summary>Gets or sets the root registry root in which to look for SDKs.</summary>
      <returns>Returns the registry root.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetInstalledSDKLocations.TargetPlatformIdentifier">
      <summary>Gets the platform identifier we are targeting.</summary>
      <returns>Returns the target platform identifier.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetInstalledSDKLocations.TargetPlatformVersion">
      <summary>Gets the platform version we are targeting.</summary>
      <returns>Returns the target platform version.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.GetReferenceAssemblyPaths">
      <summary>Returns the reference assembly paths to the various frameworks. </summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GetReferenceAssemblyPaths.#ctor">
      <summary>Creates a new instance of the GetReferenceAssemblyPaths class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetReferenceAssemblyPaths.BypassFrameworkInstallChecks">
      <summary>Gets or sets a switch the specifies whether GetReferenceAssemblyPaths performs simple checks to ensure that certain runtime frameworks are installed depending on the target framework.</summary>
      <returns>Returns true if GetReferenceAssemblyPaths should not perform these simple checks; false otherwise. </returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.GetReferenceAssemblyPaths.Execute">
      <summary>If the target framework moniker is set, generate the correct Paths.</summary>
      <returns>Returns true if paths were generated.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetReferenceAssemblyPaths.FullFrameworkReferenceAssemblyPaths">
      <summary>Gets the paths based on the passed in TargetFrameworkMoniker without considering the profile part of the moniker. </summary>
      <returns>Returns the paths based on the passed in TargetFrameworkMoniker without considering the profile part of the moniker. If the TargetFrameworkMoniker is null or empty no paths will be returned.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetReferenceAssemblyPaths.ReferenceAssemblyPaths">
      <summary>Returns the path based on the passed in TargetFrameworkMoniker. If the TargetFrameworkMoniker is null or empty this path will be null or empty.</summary>
      <returns>Returns the path.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetReferenceAssemblyPaths.RootPath">
      <summary>The root path to use to generate the reference assembly path</summary>
      <returns>Returns the root path.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetReferenceAssemblyPaths.TargetFrameworkMoniker">
      <summary>The target framework moniker to get the reference assembly paths for</summary>
      <returns>Returns the framework moniker.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetReferenceAssemblyPaths.TargetFrameworkMonikerDisplayName">
      <summary>Gets or sets the display name for the target tframework moniker.</summary>
      <returns>Returns the display name for the target tframework moniker. </returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.GetSDKReferenceFiles">
      <summary>Resolves an SDKReference to a full path on disk</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.GetSDKReferenceFiles.#ctor">
      <summary>Creates a new instance of GetSDKReferenceFiles.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetSDKReferenceFiles.CacheFileFolderPath">
      <summary>Gets or sets the path where the cache files are stored.</summary>
      <returns>Returns the cache file folder path.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetSDKReferenceFiles.CopyLocalFiles">
      <summary>Files that need to be copied locally. These are the reference assemblies and the xml intellisense files.</summary>
      <returns>Returns <see cref="T:Microsoft.Build.Framework.ITaskItem" />.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.GetSDKReferenceFiles.Execute">
      <summary>Execute the task</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetSDKReferenceFiles.LogCacheFileExceptions">
      <summary>Gets or sets a flag indicating whether to log cache file reading or writing exceptions.</summary>
      <returns>Returns the flag.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetSDKReferenceFiles.LogRedistConflictBetweenSDKsAsWarning">
      <summary>Gets or sets a flag specifying whether to log conflicts between redist files across different referenced SDKs as messages or warnings. Default is true, they are logged as warnings.</summary>
      <returns>Returns the flag.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetSDKReferenceFiles.LogRedistConflictWithinSDKAsWarning">
      <summary>Gets or sets a flag specifying whether to log conflicts between reference files within an SDK as messages or warnings. Default is false, they are logged as messages.</summary>
      <returns>Returns the flag.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetSDKReferenceFiles.LogRedistFilesList">
      <summary>Gets or sets a flag specifying whether to log redist files found as a part of resolving the SDK. Default is true, redist files are logged.</summary>
      <returns>Returns the flag.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetSDKReferenceFiles.LogReferenceConflictBetweenSDKsAsWarning">
      <summary>Gets or sets a flag specifying whether to log conflicts between reference files across different referenced SDKs as messages or warnings. Default is true, they are logged as warnings.</summary>
      <returns>Returns the flag.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetSDKReferenceFiles.LogReferenceConflictWithinSDKAsWarning">
      <summary>Gets or sets a flag specifying whether to log conflicts between reference files across different referenced SDKs as messages or warnings. Default is false, they are logged as messages.</summary>
      <returns>Returns the flag.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetSDKReferenceFiles.LogReferencesList">
      <summary>Gets or sets a flag specifying whether to log references found as a part of resolving the SDK. Default is true, references are logged.</summary>
      <returns>Returns the flag.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetSDKReferenceFiles.RedistFiles">
      <summary>Gets the resolved redist files.</summary>
      <returns>Returns an array of<see cref="T:Microsoft.Build.Framework.ITaskItem" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetSDKReferenceFiles.ReferenceExtensions">
      <summary>Extensions which should be considered reference files</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetSDKReferenceFiles.References">
      <summary>Resolved reference items.</summary>
      <returns>Returns <see cref="T:Microsoft.Build.Framework.ITaskItem" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.GetSDKReferenceFiles.ResolvedSDKReferences">
      <summary>Resolved SDK references which we will get the reference assemblies from.</summary>
      <returns>Returns <see cref="T:Microsoft.Build.Framework.ITaskItem" />.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.IFixedTypeInfo">
      <summary>Provides a substitute for <see cref="T:System.Runtime.InteropServices.ComTypes.ITypeInfo" /> by modifying the definitions of <see cref="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetRefTypeOfImplType(System.Int32,System.IntPtr@)" /> and <see cref="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetRefTypeInfo(System.IntPtr,Microsoft.Build.Tasks.IFixedTypeInfo@)" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)" />.</summary>
      <param name="memid">Identifier for the member.</param>
      <param name="invKind">The type of invocation to use.</param>
      <param name="ppv">Location to return the address.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.CreateInstance(System.Object,System.Guid@,System.Object@)" />.</summary>
      <param name="pUnkOuter">Pointer to IUNknown interface.</param>
      <param name="riid">The GUID of the class.</param>
      <param name="ppvObj">Location to return the pointer to the interface.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)" />.</summary>
      <param name="ppTLB">Location to return the location of the ITypeLib interface.</param>
      <param name="pIndex">Location to return the index.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)" />.</summary>
      <param name="memid">Identifier for the member.</param>
      <param name="invKind">The type of invocation to use.</param>
      <param name="pBstrDllName">The name of the DLL containing the interface.</param>
      <param name="pBstrName">The name of the entry point.</param>
      <param name="pwOrdinal">The ordinal value of the entry point.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)" />.</summary>
      <param name="index">The index of the documentation article.</param>
      <param name="strName">The name of the topic.</param>
      <param name="strDocString">The name of the documentation.</param>
      <param name="dwHelpContext">The context number of the help article.</param>
      <param name="strHelpFile">The name of the help file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="index">The index of the function</param>
      <param name="ppFuncDesc">Location to return a pointer to the description.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetIDsOfNames(System.String[],System.Int32,System.Int32[])" />.</summary>
      <param name="rgszNames">The names of the functions.</param>
      <param name="cNames">The number of names passed.</param>
      <param name="pMemId">Location to return the description.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)" />.</summary>
      <param name="index">The index of the flag.</param>
      <param name="pImplTypeFlags">Location to return the flags.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetMops(System.Int32,System.String@)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetMops(System.Int32,System.String@)" />.</summary>
      <param name="memid">Identifier for the Mops.</param>
      <param name="pBstrMops">Location to return the Mops.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)" />.</summary>
      <param name="memid">Identifier for the member.</param>
      <param name="rgBstrNames">Location to return the name.</param>
      <param name="cMaxNames">The number of bytes allocated at rgBstrNames.</param>
      <param name="pcNames">Location to return the number of names.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetRefTypeInfo(System.IntPtr,Microsoft.Build.Tasks.IFixedTypeInfo@)">
      <summary>Retrieves the referenced type descriptions if a type description references other type descriptions.</summary>
      <param name="hRef">A handle to the referenced type description to return.</param>
      <param name="ppTI">When this method returns, contains the referenced type description. This parameter is passed uninitialized.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetRefTypeOfImplType(System.Int32,System.IntPtr@)">
      <summary>Retrieves the type description of the implemented interface types if a type description describes a COM class.</summary>
      <param name="index">The index of the implemented type whose handle is returned.</param>
      <param name="href">When this method returns, contains a reference to a handle for the implemented interface. This parameter is passed uninitialized.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetTypeAttr(System.IntPtr@)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" />.</summary>
      <param name="ppTypeAttr">Location to return the type attributes.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)" />.</summary>
      <param name="ppTComp">Location to return the type comp.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="index">Index of the variable.</param>
      <param name="ppVarDesc">Location to return the description.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)" />.</summary>
      <param name="pvInstance">Pointer to an instance of the interface described by this type description.</param>
      <param name="memid">Identifies the interface member.</param>
      <param name="wFlags">Flags describing the context of the invoke call, as follows:DISPATCH_METHOD if the member is accessed as a method. If there is ambiguity, both this and the DISPATCH_PROPERTYGET flag can be set.DISPATCH_PROPERTYGET if the member is retrieved as a property or data member.DISPATCH_PROPERTYPUT if the member is changed as a property or data member.DISPATCH_PROPERTYPUTREF if the member is changed by using a reference assignment, rather than a value assignment. This value is only valid when the property accepts a reference to an object.</param>
      <param name="pDispParams">Points to a structure that contains an array of arguments, an array of DISPIDs for named arguments, and counts of the number of elements in each array.</param>
      <param name="pVarResult">Should be Null if the caller does not expect any result. Otherwise, it should be a pointer to the location at which the result is to be stored. If wFlags specifies DISPATCH_PROPERTYPUT or DISPATCH_PROPERTYPUTREF, pVarResultis ignored.</param>
      <param name="pExcepInfo">Points to an exception information structure, which is filled in only if DISP_E_EXCEPTION is returned. If pExcepInfois Null on input, only an HRESULT error will be returned.</param>
      <param name="puArgErr">If Invoke returns DISP_E_TYPEMISMATCH, puArgErr indicates the index (within rgvarg) of the argument with incorrect type. If more than one argument returns an error, puArgErr indicates only the first argument with an error. Arguments in pDispParams-&gt;rgvarg appear in reverse order, so the first argument is the one having the highest index in the array. Cannot be Null.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.ReleaseFuncDesc(System.IntPtr)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseFuncDesc(System.IntPtr)" />.</summary>
      <param name="pFuncDesc">Pointer to the FUNCDESC to be freed.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.ReleaseTypeAttr(System.IntPtr)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseTypeAttr(System.IntPtr)" />.</summary>
      <param name="pTypeAttr">Pointer to the TYPEATTR to be freed.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.IFixedTypeInfo.ReleaseVarDesc(System.IntPtr)">
      <summary>For a description of this member, see <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseVarDesc(System.IntPtr)" />.</summary>
      <param name="pVarDesc">Pointer to the VARDESC to be freed.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.LC">
      <summary>Implements the LC task. Use the LC element in your project file to create and execute this task. For usage and parameter information, see LC Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.LC.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.LC" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.LC.AddCommandLineCommands(Microsoft.Build.Tasks.CommandLineBuilderExtension)">
      <summary>Generates command line arguments that the license compiler tool (lc.exe) must run directly from the command line and not from a response file.</summary>
      <param name="commandLine">Command line builder to add arguments to.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.LC.GenerateFullPathToTool">
      <summary>Returns the full file path of the license compiler tool (lc.exe).</summary>
      <returns>The full file path of the license compiler tool, or null if the license compiler tool file is not found.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.LC.LicenseTarget">
      <summary>Gets or sets the executable for which the .licenses files are generated.</summary>
      <returns>The executable for which the .licenses files are generated.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.LC.NoLogo">
      <summary>Gets or sets a Boolean value that specifies whether to suppress the Microsoft startup banner display.</summary>
      <returns>true if the Microsoft startup banner display is suppressed; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.LC.OutputDirectory">
      <summary>Gets or sets the directory in which to place the output .licenses files.</summary>
      <returns>The directory in which to place the output .licenses files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.LC.OutputLicense">
      <summary>Gets or sets the name of the .licenses file.</summary>
      <returns>The name of the .licenses file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.LC.ReferencedAssemblies">
      <summary>Gets or sets the referenced components to load when generating the .licenses file.</summary>
      <returns>The referenced components to load when generating the .licenses file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.LC.SdkToolsPath">
      <summary>Gets or sets the path to use to search for the SDK tools.</summary>
      <returns>Returns the SDK tools path.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.LC.Sources">
      <summary>Gets or sets the items that contain licensed components to include in the .licenses file.</summary>
      <returns>The items that contain licensed components to include in the .licenses file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.LC.ToolName">
      <summary>Gets the name of the license compiler tool.</summary>
      <returns>The name of the license compiler tool.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.LC.ValidateParameters">
      <summary>Validates input parameters and logs errors or warnings, if any. Returns a Boolean value that indicates whether task execution should proceed.</summary>
      <returns>true if task execution should proceed; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.MakeDir">
      <summary>Implements the MakeDir task. Use the MakeDir element in your project file to create and execute this task. For usage and parameter information, see MakeDir Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.MakeDir.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.MakeDir" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.MakeDir.Directories">
      <summary>Gets or sets the set of directories to create.</summary>
      <returns>The set of directories to create.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.MakeDir.DirectoriesCreated">
      <summary>Gets the directories that the MakeDir task created. </summary>
      <returns>The directories that the MakeDir task created.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.MakeDir.Execute">
      <summary>Executes the MakeDir task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.ManagedCompiler">
      <summary>Defines the properties and methods common to managed compiler tasks.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ManagedCompiler.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.ManagedCompiler" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ManagedCompiler.AddCommandLineCommands(Microsoft.Build.Tasks.CommandLineBuilderExtension)">
      <summary>Generates command line arguments that the command line tool must run directly from the command line and not from a response file.</summary>
      <param name="commandLine">Command line builder to add arguments to.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.AdditionalLibPaths">
      <summary>Gets or sets the additional folders in which to look for assemblies.</summary>
      <returns>The additional folders in which to look for assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.AddModules">
      <summary>Gets or sets the modules for the compiler to make available to the project you are currently compiling.</summary>
      <returns>The modules for the compiler to make available to the project you are currently compiling.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ManagedCompiler.AddResponseFileCommands(Microsoft.Build.Tasks.CommandLineBuilderExtension)">
      <summary>Fills the specified <paramref name="commandLine" /> parameter with the switches and other information that can go into a response file.</summary>
      <param name="commandLine">Command line builder to add arguments to.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.ManagedCompiler.CheckAllReferencesExistOnDisk">
      <summary>Verifies that all specified references exist on disk.</summary>
      <returns>true if all references exist on disk; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ManagedCompiler.CheckHostObjectSupport(System.String,System.Boolean)">
      <summary>Logs a message if the specified parameter is not supported by the host compiler.</summary>
      <param name="parameterName">The parameter name to set on the host compiler.</param>
      <param name="resultFromHostObjectSetOperation">true if the host compiler supports <paramref name="parameterName" />; otherwise, false</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.CodePage">
      <summary>Gets or sets the code page to use for all source code files in the compilation.</summary>
      <returns>The code page to use for all source code files in the compilation.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.DebugType">
      <summary>Gets or sets the debug type.</summary>
      <returns>The debug type.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.DefineConstants">
      <summary>Gets or sets the conditional compiler constants.</summary>
      <returns>The conditional compiler constants.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.DelaySign">
      <summary>Gets or sets a value indicating whether the public key is placed in the assembly.</summary>
      <returns>true if the public key is placed in the assembly; false if the assembly is fully signed.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.EmitDebugInformation">
      <summary>Gets or sets a value indicating whether the compiler generates debugging information.</summary>
      <returns>true if debugging information is generated; otherwise, false;</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.FileAlignment">
      <summary>Gets or sets a value indicating where to align the sections of the output file.</summary>
      <returns>A value indicating where to align the sections of the output file.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ManagedCompiler.HandleTaskExecutionErrors">
      <summary>Handles the return code from the compiler.</summary>
      <returns>true if the return code was handled successfully; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.HighEntropyVA">
      <summary>Gets or sets the HighEntropyVA attribute.</summary>
      <returns>Returns the HighEntropyVA attribute.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.HostCompilerSupportsAllParameters">
      <summary>Gets or sets a value indicating wheter the host compilter supports all task parameters.</summary>
      <returns>true if the host compiler supports all task parameters; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.KeyContainer">
      <summary>Gets or sets the name of the cryptographic key container.</summary>
      <returns>The name of the cryptographic key container.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.KeyFile">
      <summary>Gets or sets the file name containing the cryptographic key.</summary>
      <returns>The file name containing the cryptographic key.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.LinkResources">
      <summary>Gets or sets the .NET Framework resource files to link to the output file.</summary>
      <returns>The .NET Framework resource files to link to the output file.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ManagedCompiler.ListHasNoDuplicateItems(Microsoft.Build.Framework.ITaskItem[],System.String)">
      <summary>Determines if any duplicate items exist in the specified task parameter.</summary>
      <returns>true if the item list contains no duplicates; otherwise, false.</returns>
      <param name="itemList">The list of items to examine for duplicates.</param>
      <param name="parameterName">The name of the parameter that contains the <paramref name="itemList" />.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.MainEntryPoint">
      <summary>Gets or sets the class or module that contains the main entry point.</summary>
      <returns>The class or module that contains the main entry point.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.NoConfig">
      <summary>Gets or sets a value indicating whether the compiler should use the default response file.</summary>
      <returns>true if the compiler is not using the default response file; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.NoLogo">
      <summary>Gets or sets a value indicating whether to suppress the compiler banner information.</summary>
      <returns>true to suppress the compiler banner information; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.NoWin32Manifest">
      <summary>Gets or sets a Boolean value that specifies whether an external UAC manifest is generated for the application.</summary>
      <returns>true if an external UAC manifest is generated for the application; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.Optimize">
      <summary>Gets or sets a value indicating whether to enable compiler optimizations.</summary>
      <returns>true to enable compiler optimizations; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.OutputAssembly">
      <summary>Gets or sets the name of the output file.</summary>
      <returns>The name of the output file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.Platform">
      <summary>Gets or sets the Platform attribute.</summary>
      <returns>Returns the Platform attribute.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.Prefer32Bit">
      <summary>Gets or sets the Prefer32Bit attribute.</summary>
      <returns>Returns the Prefer32Bit attribute.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.References">
      <summary>Gets or sets the items from which the compiler will import public type information.</summary>
      <returns>The items from which the compiler will import public type information.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.Resources">
      <summary>Gets or sets the .NET Framework resources to embed in the output file.</summary>
      <returns>The .NET Framework resources to embed in the output file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.ResponseFiles">
      <summary>Gets or sets the response files that contain commands for the task.</summary>
      <returns>The response files that contain commands for the task.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.Sources">
      <summary>Gets or sets the source files to compile.</summary>
      <returns>The source files to compile.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.StandardOutputEncoding">
      <summary>Gets or sets the encoding of the captured task standard output stream.</summary>
      <returns>The encoding of the captured task standard output stream.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.SubsystemVersion">
      <summary>Gets or sets the SubsystemVersion attribute value.</summary>
      <returns>Returns the subsystem version.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.TargetType">
      <summary>Gets or sets the file format of the output file.</summary>
      <returns>The file format of the output file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.TreatWarningsAsErrors">
      <summary>Gets or sets a value indicating whether warnings are treated as errors.</summary>
      <returns>true if warnings are treated as errors; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ManagedCompiler.UseAlternateCommandLineToolToExecute">
      <summary>If an alternate tool name or tool path was specified in the project file, then that tool is used rather than the host compiler for integrated development environment (IDE) builds.</summary>
      <returns>false if the host compiler should be used; otherwise, true.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.UsedCommandLineTool">
      <summary>Whether the command line compiler was invoked, instead of the host object compiler.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.Utf8Output">
      <summary>Gets or sets a value indicating whether compiler output is logged using UTF-8 encoding.</summary>
      <returns>true if compiler output is logged using UTF-8 encoding; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ManagedCompiler.ValidateParameters">
      <summary>Validates the task parameters.</summary>
      <returns>true if all parameters are valid; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.Win32Icon">
      <summary>Gets or sets the icon file name.</summary>
      <returns>The icon file name.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.Win32Manifest">
      <summary>Gets or sets the Win32 manifest.</summary>
      <returns>The Win32 manifest.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ManagedCompiler.Win32Resource">
      <summary>Gets or sets a Win32 resource (.res) file to insert in the output file.</summary>
      <returns>The Win32 resource (.res) file to insert in the output file.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Message">
      <summary>Implements the Message task. Use the Message element in your project file to create and execute this task. For usage and parameter information, see Message Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Message.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Message" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Message.Code">
      <summary>Message code</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Message.Execute">
      <summary>Executes the Message task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Message.File">
      <summary>Relevant file if any. If none is provided and this is a critical message, the file containing the Message task will be used.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Message.HelpKeyword">
      <summary>Message help keyword</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Message.Importance">
      <summary>Gets or sets the importance of the message.</summary>
      <returns>The importance of the message.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Message.IsCritical">
      <summary>Indicates if this is a critical message</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Message.Text">
      <summary>Gets or sets the error text to log.</summary>
      <returns>The error text to log.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Move">
      <summary>Task to move one or more files.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Move.#ctor">
      <summary>Creates a task to move one or more files.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Move.Cancel">
      <summary>Stop and return (in an undefined state) as soon as possible.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Move.DestinationFiles">
      <summary>Gets or sets the destination files that represent the destination of the matching source files.</summary>
      <returns>Returns an item that represents the destination files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Move.DestinationFolder">
      <summary>Gets or sets the destination folder for the source files.</summary>
      <returns>Returns an item representing the destination folder.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Move.Execute">
      <summary>Executes the move.</summary>
      <returns>Returns true if the move is successful, false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Move.MovedFiles">
      <summary>Gets an item representing the files that were successfully moved.</summary>
      <returns>Returns an item representing the files that were successfully moved.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Move.OverwriteReadOnlyFiles">
      <summary>Gets or sets a switch that selects whether files in the destination folder that have the read-only attribute set can be overwritten.</summary>
      <returns>Returns true if the files can be overwritten, false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Move.SourceFiles">
      <summary>Gets or sets an item representing the list of files to move.</summary>
      <returns>Returns an item representing the list of files to move.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.MSBuild">
      <summary>Implements the MSBuild task. Use the MSBuild element in your project file to create and execute this task. For usage and parameter information, see MSBuild Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.MSBuild.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.MSBuild" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.MSBuild.BuildInParallel">
      <summary>Gets or sets a Boolean value that specifies whether the projects specified in the <see cref="P:Microsoft.Build.Tasks.MSBuild.Projects" /> property are built in parallel, if possible. </summary>
      <returns>true if the projects specified in the <see cref="P:Microsoft.Build.Tasks.MSBuild.Projects" /> property are built in parallel if possible; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.MSBuild.Execute">
      <summary>Instructs the MSBuild engine to build one or more project files whose locations are specified by the <see cref="P:Microsoft.Build.Tasks.MSBuild.Projects" /> property.</summary>
      <returns>true if all projects build successfully; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.MSBuild.Projects">
      <summary>Gets or sets the project files to build.</summary>
      <returns>The project files to build.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.MSBuild.Properties">
      <summary>Gets or sets a semicolon-delimited list of property name/value pairs to apply as global properties to the child project. </summary>
      <returns>A semicolon-delimited list of property name/value pairs to apply as global properties to the child project.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.MSBuild.RebaseOutputs">
      <summary>Gets or sets a Boolean value that specifies whether the relative paths of target output items from the built projects have their paths adjusted to be relative to the calling project. </summary>
      <returns>true if the relative paths of target output items from the built projects have their paths adjusted to be relative to the calling project.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.MSBuild.RemoveProperties">
      <summary>Gets or sets the set of global properties to remove.</summary>
      <returns>Returns the set of global properties to remove.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.MSBuild.RunEachTargetSeparately">
      <summary>Gets or sets a Boolean value that specifies whether the MSBuild task invokes each target in the list passed to MSBuild one at a time, instead of at the same time.</summary>
      <returns>true if the MSBuild task invokes each target in the list passed to MSBuild one at a time; false if the MSBuild task invokes all targets in the list at the same time.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.MSBuild.SkipNonexistentProjects">
      <summary>Gets or sets a Boolean value that specifies whether project files that do not exist on the disk will be skipped.</summary>
      <returns>true if project files that do not exist on the disk will be skipped; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.MSBuild.StopOnFirstFailure">
      <summary>Gets or sets a Boolean value that specifies whether the task should stop building the remaining projects as soon as any one of them may not work.</summary>
      <returns>true if the task should stop building the remaining projects as soon as any one of them may not work; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.MSBuild.TargetAndPropertyListSeparators">
      <summary>Gets or sets the escape characters to be unescaped from the specified <see cref="P:Microsoft.Build.Tasks.MSBuild.Properties" /> and <see cref="P:Microsoft.Build.Tasks.MSBuild.Targets" />.</summary>
      <returns>The escape characters to be unescaped from the specified <see cref="P:Microsoft.Build.Tasks.MSBuild.Properties" /> and <see cref="P:Microsoft.Build.Tasks.MSBuild.Targets" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.MSBuild.TargetOutputs">
      <summary>Returns the outputs of the built targets from all the project files. Only the outputs from the targets that were specified are returned, not any outputs that may exist on targets that those targets depend on.</summary>
      <returns>The outputs of the built targets from all the project files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.MSBuild.Targets">
      <summary>Gets or sets the target or targets to build in the project files. Use a semicolon to separate a list of target names. </summary>
      <returns>The target or targets to build in the project files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.MSBuild.ToolsVersion">
      <summary>Gets or sets a target .NET Framework version to build the project with, which enables an MSBuild task to build a project that targets a different version of the .NET Framework than the one specified in the project. Valid values are <paramref name="2.0" />, <paramref name="3.0" /> and <paramref name="3.5" />. </summary>
      <returns>The target .NET Framework version to build the project with.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.MSBuild.UnloadProjectsOnCompletion">
      <summary>Gets or sets a Boolean value that specifies whether the project will be unloaded once the operation is completed.</summary>
      <returns>true if the project will be unloaded once the operation is completed; otherwise, false. This property is no longer used in version 4.0</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.MSBuild.UseResultsCache">
      <summary>Gets or sets a Boolean value that indicates whether the MSBuild engine will return the built targets from the cache.</summary>
      <returns>true if the MSBuild engine will return the built targets from the cache; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.ReadLinesFromFile">
      <summary>Implements the ReadLinesFromFile task. Use the ReadLinesFromFile element in your project file to create and execute this task. For usage and parameter information, see ReadLinesFromFile Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ReadLinesFromFile.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.ReadLinesFromFile" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ReadLinesFromFile.Execute">
      <summary>Executes the ReadLinesFromFile task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ReadLinesFromFile.File">
      <summary>Gets or sets the file to read.</summary>
      <returns>The file to read.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ReadLinesFromFile.Lines">
      <summary>Gets or sets the lines read from the file.</summary>
      <returns>The lines read from the file.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.RegisterAssembly">
      <summary>Implements the RegisterAssembly task. Use the RegisterAssembly element in your project file to create and execute this task. For usage and parameter information, see RegisterAssembly Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.RegisterAssembly.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.RegisterAssembly" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.RegisterAssembly.Assemblies">
      <summary>Gets or sets the assemblies to be registered with COM.</summary>
      <returns>The assemblies to be registered with COM.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RegisterAssembly.AssemblyListFile">
      <summary>Gets or sets information about the state between the RegisterAssembly task and the UnregisterAssembly task. This prevents the UnregisterAssembly task from attempting to unregister an assembly that failed to register in the RegisterAssembly task.</summary>
      <returns>The state information necessary for the UnregisterAssembly task to do proper clean-up.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RegisterAssembly.CreateCodeBase">
      <summary>Gets or sets a Boolean value that specifies whether the task creates a codebase entry in the registry, which specifies the file path for an assembly that is not installed in the global assembly cache. </summary>
      <returns>true if the task creates a codebase entry in the registry; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.RegisterAssembly.Execute">
      <summary>Executes the RegisterAssembly task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.RegisterAssembly.ReportEvent(System.Runtime.InteropServices.ExporterEventKind,System.Int32,System.String)">
      <summary>Callback method for reporting the type library export events.</summary>
      <param name="kind">Describes the callbacks that the type library exporter makes when exporting a type library.</param>
      <param name="code">The error code of the export event.</param>
      <param name="msg">The message of the export event.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.RegisterAssembly.ResolveRef(System.Reflection.Assembly)">
      <summary>Callback method for finding the type library for the specified assembly.</summary>
      <returns>null in all cases.</returns>
      <param name="assemblyToResolve">The assembly to find type libraries for.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.RegisterAssembly.TypeLibFiles">
      <summary>Gets or sets the type library to generate from the specified assembly.</summary>
      <returns>The type library to generate from the specified assembly.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.RemoveDir">
      <summary>Implements the RemoveDir task. Use the RemoveDir element in your project file to create and execute this task. For usage and parameter information, see RemoveDir Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.RemoveDir.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.RemoveDir" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.RemoveDir.Directories">
      <summary>Gets or sets the directories to delete.</summary>
      <returns>The directories to delete.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.RemoveDir.Execute">
      <summary>Executes the RemoveDir task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RemoveDir.RemovedDirectories">
      <summary>Gets or sets the directories that were successfully deleted.</summary>
      <returns>The directories that were successfully deleted.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.RemoveDuplicates">
      <summary>Implements the RemoveDuplicates task. Use the RemoveDuplicates element in your project file to create and execute this task. For usage and parameter information, see RemoveDuplicates Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.RemoveDuplicates.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.RemoveDuplicates" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.RemoveDuplicates.Execute">
      <summary>Executes the RemoveDuplicates task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RemoveDuplicates.Filtered">
      <summary>Gets or sets an item collection with all duplicate items removed.</summary>
      <returns>An item collection with all duplicate items removed.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RemoveDuplicates.Inputs">
      <summary>Gets or sets the items to remove duplicate items from.</summary>
      <returns>The items to remove duplicate items from.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly">
      <summary>Determines whether the application requires the .NET Framework 3.5 SP1.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly.Assemblies">
      <summary>Gets or sets the assemblies referenced in this application.</summary>
      <returns>The assemblies referenced in the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly.CreateDesktopShortcut">
      <summary>Gets or sets whether to create an icon on the desktop during installation.</summary>
      <returns>true if a shortcut icon is added to the desktop; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly.DeploymentManifestEntryPoint">
      <summary>Gets or sets the manifest file name for the application.</summary>
      <returns>The assembly that should be executed.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly.EntryPoint">
      <summary>Gets or sets the assembly that should be executed when the application is run.</summary>
      <returns>The assembly that should be executed.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly.ErrorReportUrl">
      <summary>Gets or sets the Web site that is displayed in dialog boxes that are encountered during ClickOnce installations.</summary>
      <returns>The URL of the web site to display.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly.Execute">
      <summary>Executes the task that determines whether the applications requires .NET Framework 3.5 SP1.</summary>
      <returns>true if the application requires .NET Framework 3.5 SP1; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly.Files">
      <summary>Gets or sets the list of all of the files that will be deployed when you publish your application.</summary>
      <returns>The files that will be deployed.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly.ReferencedAssemblies">
      <summary>Gets or sets the assemblies referenced in your project.</summary>
      <returns>The assemblies in your project.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly.RequiresMinimumFramework35SP1">
      <summary>Gets or sets whether the application requires .NET Framework 3.5 SP1.</summary>
      <returns>true if the application requires .NET Framework 3.5 SP1; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly.SigningManifests">
      <summary>Gets or sets whether the ClickOnce manifests are signed.</summary>
      <returns>true if the ClickOnce manifests are signed; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly.SuiteName">
      <summary>Gets or sets the name of the folder on the Start menu in which the application will be installed.</summary>
      <returns>The name of the installation folder.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.RequiresFramework35SP1Assembly.TargetFrameworkVersion">
      <summary>Gets or sets the version of the .NET Framework that this application targets.</summary>
      <returns>The name of .NET Framework version.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.ResolveAssemblyReference">
      <summary>Implements the ResolveAssemblyReference task. Use the ResolveAssemblyReference element in your project file to create and execute this task. For usage and parameter information, see ResolveAssemblyReference Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveAssemblyReference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.ResolveAssemblyReference" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.AllowedAssemblyExtensions">
      <summary>Gets or sets the assembly extensions that will be considered during references resolution.</summary>
      <returns>The assembly extensions that will be considered during references resolution.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.AllowedRelatedFileExtensions">
      <summary>Gets or sets the extensions that will be considered when searching for related files.</summary>
      <returns>The extensions that will be considered when searching for related files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.AppConfigFile">
      <summary>Gets or sets the App.Config file path.</summary>
      <returns>The App.Config file path.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.Assemblies">
      <summary>Gets or sets the assembly names to resolve into full paths and to find dependencies for. </summary>
      <returns>The assembly names to resolve into full paths and to find dependencies for.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.AssemblyFiles">
      <summary>Gets or sets a list of fully qualified assembly paths to find dependencies for.</summary>
      <returns>The list of fully qualified assembly paths to find dependencies for.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.AutoUnify">
      <summary>Gets or sets a Boolean value that specifies whether the resulting dependency graph is automatically treated as if there were an App.Config file passed in to the <see cref="P:Microsoft.Build.Tasks.ResolveAssemblyReference.AppConfigFile" /> property.</summary>
      <returns>true if the resulting dependency graph is automatically treated as if there were an App.Config file passed in to the <see cref="P:Microsoft.Build.Tasks.ResolveAssemblyReference.AppConfigFile" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.CandidateAssemblyFiles">
      <summary>Gets or sets a list of assemblies, which must be absolute file names or project-relative file names, to use for the search and resolution process.</summary>
      <returns>The list of assemblies to use for the search and resolution process.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.CopyLocalDependenciesWhenParentReferenceInGac">
      <summary>Gets or sets a switch that determines how dependencies are determined, and whether or not a dependency is copied locally.</summary>
      <returns>Returns true if when determining if a dependency should be copied locally one of the checks done is to see if the parent reference in the project file has the Private metadata set or not. If that metadata is set then we will use that for the dependency as well. If the metadata is not set then the dependency will go through the same checks as the parent reference. One of these checks is to see if the reference is in the GAC. If a reference is in the GAC then we will not copy it locally as it is assumed it will be in the gac on the target machine as well. However this only applies to that specific reference and not its dependencies.This means a reference in the project file may be copy local false due to it being in the GAC but the dependencies may still be copied locally because they are not in the GAC. This is the default behavior for RAR and causes the default value for this property to be true.Returns false if we will still check project file references to see if they are in the GAC and set their copy local state as appropriate. However for dependencies we will not only check to see if they are in the GAC but we will also check to see if the parent reference from the project file is in the GAC. If the parent reference from the project file is in the GAC then we will not copy the dependency locally.NoteIf there are multiple parent reference and ANY of them does not come from the GAC then we will set copy local to true.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.CopyLocalFiles">
      <summary>Returns all files in the <see cref="P:Microsoft.Build.Tasks.ResolveAssemblyReference.ResolvedFiles" />, <see cref="P:Microsoft.Build.Tasks.ResolveAssemblyReference.ResolvedDependencyFiles" />, <see cref="P:Microsoft.Build.Tasks.ResolveAssemblyReference.RelatedFiles" />, <see cref="P:Microsoft.Build.Tasks.ResolveAssemblyReference.SatelliteFiles" />, and <see cref="T:Microsoft.Build.Tasks.ResolveAssemblyReference" /> properties that have the CopyLocal item metadata with a value of true.</summary>
      <returns>All files in the <see cref="P:Microsoft.Build.Tasks.ResolveAssemblyReference.ResolvedFiles" />, <see cref="P:Microsoft.Build.Tasks.ResolveAssemblyReference.ResolvedDependencyFiles" />, <see cref="P:Microsoft.Build.Tasks.ResolveAssemblyReference.RelatedFiles" />, <see cref="P:Microsoft.Build.Tasks.ResolveAssemblyReference.SatelliteFiles" />, and <see cref="P:Microsoft.Build.Tasks.ResolveAssemblyReference.ScatterFiles" /> properties that have the CopyLocal item metadata with a value of true.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveAssemblyReference.Execute">
      <summary>Executes the ResolveAssemblyReference task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.FilesWritten">
      <summary>Gets or sets the items written to disk.</summary>
      <returns>The items written to disk.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.FindDependencies">
      <summary>Gets or sets a Boolean value that specifies whether dependencies will be found (true) or only primary references will be found (false). </summary>
      <returns>true if dependencies will be found; false if only primary references will be found. </returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.FindRelatedFiles">
      <summary>Gets or sets a Boolean value that specifies whether related files such as .pdb files and .xml files will be found.</summary>
      <returns>true if related files such as .pdb files and .xml files will be found; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.FindSatellites">
      <summary>Gets or sets a Boolean value that specifies whether satellite assemblies will be found.</summary>
      <returns>true if satellite assemblies will be found; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.FindSerializationAssemblies">
      <summary>Gets or sets a Boolean value that specifies whether the serialization assemblies will be found.</summary>
      <returns>true if the serialization assemblies will be found; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.FullFrameworkAssemblyTables">
      <summary>Gets or sets a list of items representing XML files that contain the full framework for the profile.</summary>
      <returns>Returns a list of items representing XML files that contain the full framework for the profile.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.FullFrameworkFolders">
      <summary>Gets or sets a list of folders which contain a RedistList directory which represent the full framework for a given client profile, for example:%programfiles%\reference assemblies\microsoft\framework\v4.0</summary>
      <returns>Returns a list of folders which contain a RedistList directory which represent the full framework for a given client profile</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.FullTargetFrameworkSubsetNames">
      <summary>A string that contains the names of the subsets of the specified target framework.</summary>
      <returns>A string value that contains subset names.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.IgnoreDefaultInstalledAssemblySubsetTables">
      <summary>Gets or sets a Boolean value that specifies whether the task should look for and use additional installed assembly subset tables (also known as Subset Lists), which are found in the SubsetList directory under the provided TargetFrameworkDirectories.</summary>
      <returns>true if additional assembly subset tables are used; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.IgnoreDefaultInstalledAssemblyTables">
      <summary>Gets or sets a Boolean value that specifies whether the ResolveAssemblyReference task should ignore additional installed assembly tables found in the RedistList directory underneath the provided <see cref="P:Microsoft.Build.Tasks.ResolveAssemblyReference.TargetFrameworkDirectories" /> (true) or should search for and use these installed assembly tables (false).</summary>
      <returns>true if the task should ignore the additional installed assembly tables; false if the task should search for and use the additional installed assembly tables.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.IgnoreVersionForFrameworkReferences">
      <summary>Gets or sets a flag indicating whether to ignore the version information on the framework assembly and actually resolve the framework assembly from the currently targeted framework. Flag applies if the primary reference is a framework assembly.</summary>
      <returns>Returns the flag.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.InstalledAssemblySubsetTables">
      <summary>An <see cref="T:Microsoft.Build.Framework.ITaskItem" /> that represents a list of XML files that contain assemblies that are expected to be in the target subset.</summary>
      <returns>An <see cref="T:Microsoft.Build.Framework.ITaskItem" /> list of XML files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.InstalledAssemblyTables">
      <summary>Gets or sets a list of XML files that contain assemblies that are expected to be installed on the target machine.</summary>
      <returns>A list of XML files that contain assemblies that are expected to be installed on the target machine.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.LatestTargetFrameworkDirectories">
      <summary>Gets or sets the list of directories which contain the redist lists for the most current framework which can be targeted on the machine. </summary>
      <returns>Returns the list of directories which contain the redist lists for the most current framework which can be targeted on the machine.  If this is not set Then we will looks for the highest framework installed on the machine for a given target framework identifier and use that.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.ProfileName">
      <summary>Gets or sets the name of the target framework profile we are targeting, for example  Client, Web, or Network</summary>
      <returns>Returns the name of the target framework profile we are targeting, for example  Client, Web, or Network</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.RelatedFiles">
      <summary>Gets related files, such as .xml and .pdb files with the same base name as a reference.</summary>
      <returns>The related files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.ResolvedDependencyFiles">
      <summary>Gets the nth order paths to dependencies.</summary>
      <returns>The nth order paths to dependencies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.ResolvedFiles">
      <summary>Gets a list of all primary references resolved to full paths.</summary>
      <returns>The list of all primary references resolved to full paths.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.ResolvedSDKReferences">
      <summary>A list of resolved SDK references which contain the sdk name, sdk location and the targeted configuration. These locations will only be searched if the reference has the SDKName metadata attached to it.</summary>
      <returns>Returns <see cref="T:Microsoft.Build.Framework.ITaskItem" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.SatelliteFiles">
      <summary>Gets any satellite files found.</summary>
      <returns>Any satellite files found.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.ScatterFiles">
      <summary>Gets the scatter files associated with one of the given assemblies.</summary>
      <returns>The scatter files associated with one of the given assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.SearchPaths">
      <summary>Specifies the directories or special locations that are searched to find the files on disk that represent the assemblies.</summary>
      <returns>The directories or special locations that are searched to find the files on disk that represent the assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.SerializationAssemblyFiles">
      <summary>Gets the serialization assemblies.</summary>
      <returns>The serialization assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.Silent">
      <summary>Gets or sets a Boolean value that specifies whether no messages are logged. </summary>
      <returns>true if no messages are logged; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.StateFile">
      <summary>Gets or sets a file name that indicates where to save the intermediate build state for this task.</summary>
      <returns>The file name that indicates where to save the intermediate build state for this task.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.SuggestedRedirects">
      <summary>Gets one item for every distinct conflicting assembly identity, regardless of the value of the <see cref="P:Microsoft.Build.Tasks.ResolveAssemblyReference.AutoUnify" /> property.</summary>
      <returns>The items that correspond to distinct conflicting assembly identities.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.TargetedRuntimeVersion">
      <summary>Gets or sets the name of the runtime we are targeting, for example, 2.0.57027. </summary>
      <returns>Returns the name of the runtime we are targeting, for example, 2.0.57027. A "v" may be prefixed to the name.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.TargetFrameworkDirectories">
      <summary>Gets or sets the path to the target framework directory. </summary>
      <returns>The path to the target framework directory.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.TargetFrameworkMoniker">
      <summary>Gets or sets the target framework moniker being targeted, if any.</summary>
      <returns>Returns the target framework moniker being targeted, if any. Returns an empty string by default.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.TargetFrameworkMonikerDisplayName">
      <summary>Gets or sets the display name of the target framework moniker being targeted, if any.</summary>
      <returns>Returns the display name of the target framework moniker being targeted, if any.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.TargetFrameworkSubsets">
      <summary>A list of target framework subset names that will be searched for in the target framework directories.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.TargetFrameworkVersion">
      <summary>Gets or sets the project target framework version used for reference filtering.</summary>
      <returns>The project target framework version used for reference filtering.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.TargetProcessorArchitecture">
      <summary>Gets or sets the preferred target processor architecture used for resolving Global Assembly Cache (GAC) references, which can have a value of x86, IA64, or AMD64.</summary>
      <returns>The preferred target processor architecture used for resolving Global Assembly Cache (GAC) references.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveAssemblyReference.WarnOrErrorOnTargetArchitectureMismatch">
      <summary>Gets or sets a string value specifying the behavior when a mismatch between the target processor architecture and the architecture of a primary reference. When this is “Error”, an error will be logged for a mismatch. When this is “Warning”, a warning will be logged for a mismatch. When this is “None”, no error or warning will be logged for a mismatch.</summary>
      <returns>Returns a string value of “Error”, “Warning” or “None”.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.ResolveComReference">
      <summary>Implements the ResolveComReference task. Use the ResolveComReference element in your project file to create and execute this task. For usage and parameter information, see ResolveComReference Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveComReference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.ResolveComReference" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.DelaySign">
      <summary>Gets or sets a Boolean value that specifies whether the ResolveComReference task places the public key in the assembly (true) or fully signs the assembly (false).</summary>
      <returns>true if the ResolveComReference task places the public key in the assembly; false if the ResolveComReference task fully signs the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.EnvironmentVariables">
      <summary>Gets or sets an array of name-value pairs of environment variables that should be passed to the spawned tlbimp.exe and aximp.exe in addition to (or selectively overriding) the regular environment block.</summary>
      <returns>Returns an array of name-value pairs of environment variables.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveComReference.Execute">
      <summary>Executes the ResolveComReference task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.ExecuteAsTool">
      <summary>Property to allow multitargeting of ResolveComReferences: If true, tlbimp.exe and aximp.exe from the appropriate target framework will be run out-of-proc to generate the necessary wrapper assemblies.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.IncludeVersionInInteropName">
      <summary>When set to true, the typelib version will be included in the wrapper name. Default is false.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.KeyContainer">
      <summary>Gets or sets a container that holds a key pair.</summary>
      <returns>A container that holds a key pair.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.KeyFile">
      <summary>Gets or sets an item that contains a key pair.</summary>
      <returns>An item that contains a key pair.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.NoClassMembers">
      <summary>Gets or sets a Boolean value that specifies whether to pass the <see cref="F:System.Runtime.InteropServices.TypeLibImporterFlags.PreventClassMembers" /> flag to .tlb wrapper generation.</summary>
      <returns>true if the task should pass the <see cref="F:System.Runtime.InteropServices.TypeLibImporterFlags.PreventClassMembers" /> flag to .tlb wrapper generation; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.ResolvedAssemblyReferences">
      <summary>Gets or sets the resolved assembly references.</summary>
      <returns>The resolved assembly references.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.ResolvedFiles">
      <summary>Gets or sets the fully qualified files on disk that correspond to the physical locations of the type libraries that were provided as input to this task.</summary>
      <returns>The fully qualified files on disk that correspond to the physical locations of the type libraries that were provided as input to this task.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.ResolvedModules">
      <summary>Gets or sets the paths to the modules that were found.</summary>
      <returns>The paths to the modules that were found.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.SdkToolsPath">
      <summary>If ExecuteAsTool is true, this must be set to the SDK tools path for the framework version being targeted.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.Silent">
      <summary>Gets or sets a flag specifying whether messages or warnings are logged. Default is false, messages and warnings are logged.</summary>
      <returns>Returns the flag.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.StateFile">
      <summary>Gets or sets the cache file for COM component timestamps.</summary>
      <returns>The cache file for COM component timestamps.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.TargetFrameworkVersion">
      <summary>The project target framework version. Default is empty. which means there will be no filtering for the reference based on their target framework.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.TargetProcessorArchitecture">
      <summary>Gets or sets the name of the preferred target processor architecture, which is passed to the tlbimp.exe /machine flag after translation, for example x86, ia64, amd64 or msil.</summary>
      <returns>Returns the name of the preferred target processor architecture.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.TypeLibFiles">
      <summary>Gets or sets the type library file path to COM references. </summary>
      <returns>The type library file path to COM references.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.TypeLibNames">
      <summary>Gets or sets the type library names to resolve.</summary>
      <returns>The type library names to resolve.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveComReference.WrapperOutputDirectory">
      <summary>Gets or sets the location on disk where the generated interop assembly is placed.</summary>
      <returns>The location on disk where the generated interop assembly is placed.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.ResolveKeySource">
      <summary>Implements the ResolveKeySource task. Use the ResolveKeySource element in your project file to create and execute this task. For usage and parameter information, see ResolveKeySource Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveKeySource.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.ResolveKeySource" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveKeySource.AutoClosePasswordPromptShow">
      <summary>Gets or sets the amount of time, in seconds, to display the count down message.</summary>
      <returns>The amount of time, in seconds, to display the count down message.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveKeySource.AutoClosePasswordPromptTimeout">
      <summary>Gets or sets the amount of time, in seconds, to wait before closing the password prompt dialog.</summary>
      <returns>The amount of time, in seconds, to wait before closing the password prompt dialog.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveKeySource.CertificateFile">
      <summary>Gets or sets the path of the certificate file.</summary>
      <returns>The path of the certificate file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveKeySource.CertificateThumbprint">
      <summary>Gets or sets the certificate thumbprint.</summary>
      <returns>The certificate thumbprint.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveKeySource.Execute">
      <summary>Executes the ResolveKeySource task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveKeySource.KeyFile">
      <summary>Gets or sets the path of the key file.</summary>
      <returns>The path of the key file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveKeySource.ResolvedKeyContainer">
      <summary>Gets or sets the resolved key container.</summary>
      <returns>The resolved key container.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveKeySource.ResolvedKeyFile">
      <summary>Gets or sets the resolved key file.</summary>
      <returns>The resolved key file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveKeySource.ResolvedThumbprint">
      <summary>Gets or sets the resolved certificate thumbprint.</summary>
      <returns>The resolved certificate thumbprint.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveKeySource.ShowImportDialogDespitePreviousFailures">
      <summary>Gets or sets a Boolean value that specifies whether to show the import dialog despite previous failures.</summary>
      <returns>true if the task should show the import dialog despite previous failures; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveKeySource.SuppressAutoClosePasswordPrompt">
      <summary>Gets or sets a Boolean value that specifies whether the password prompt dialog should not auto-close.</summary>
      <returns>true the password prompt dialog should not auto-close; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.ResolveManifestFiles">
      <summary>This task resolves the following items in the build process to files for manifest generation: built items, dependencies, satellites, content, debug symbols, and documentation) to files for manifest generation.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveManifestFiles.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.ResolveManifestFiles" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.DeploymentManifestEntryPoint">
      <summary>Gets or sets the name of the deployment manifest.</summary>
      <returns>The deployment manifest name as an entry point.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.EntryPoint">
      <summary>Gets or sets the managed assembly or ClickOnce manifest reference that is the entry point to the manifest.</summary>
      <returns>The entry point.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveManifestFiles.Execute">
      <summary>Executes the ResolveManifestFiles task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.ExtraFiles">
      <summary>Gets or sets the extra files.</summary>
      <returns>The extra files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.Files">
      <summary>Gets or sets the input files.</summary>
      <returns>The input files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.ManagedAssemblies">
      <summary>Gets or sets the managed assemblies.</summary>
      <returns>The managed assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.NativeAssemblies">
      <summary>Gets or sets the native assemblies.</summary>
      <returns>The native assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.OutputAssemblies">
      <summary>Gets or sets the generated assemblies.</summary>
      <returns>The generated assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.OutputDeploymentManifestEntryPoint">
      <summary>Gets or sets the output deployment manifest entry point.</summary>
      <returns>A deployment manifest entry point.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.OutputEntryPoint">
      <summary>Gets or sets the output entry point.</summary>
      <returns>An output entry point.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.OutputFiles">
      <summary>Gets or sets the output files.</summary>
      <returns>The output files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.PublishFiles">
      <summary>Gets or sets the publish files.</summary>
      <returns>The publish files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.SatelliteAssemblies">
      <summary>Gets or sets the satellite assemblies.</summary>
      <returns>The satellite assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.SigningManifests">
      <summary>Gets or sets a value that indicates whether the manifests are signed.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.TargetCulture">
      <summary>Gets or sets the target culture for satellite assemblies.</summary>
      <returns>The target culture for satellite assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveManifestFiles.TargetFrameworkVersion">
      <summary>Gets or sets the target .NET Framework version.</summary>
      <returns>The version of the target .NET Framework.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.ResolveNativeReference">
      <summary>Implements the ResolveNativeReference task. Use the ResolveNativeReference element in your project file to create and execute this task. For usage and parameter information, see ResolveNativeReference Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveNativeReference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.ResolveNativeReference" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveNativeReference.AdditionalSearchPaths">
      <summary>Gets or sets the search paths for resolving assembly identities of native references.</summary>
      <returns>The search paths for resolving assembly identities of native references.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveNativeReference.ContainedComComponents">
      <summary>Gets or sets the COM components of the native assembly.</summary>
      <returns>The COM components of the native assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveNativeReference.ContainedLooseEtcFiles">
      <summary>Gets or sets the loose Etc files listed in the native manifest.</summary>
      <returns>The loose Etc files listed in the native manifest.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveNativeReference.ContainedLooseTlbFiles">
      <summary>Gets or sets the loose .tlb files of the native assembly.</summary>
      <returns>The loose .tlb files of the native assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveNativeReference.ContainedPrerequisiteAssemblies">
      <summary>Gets or sets the assemblies that must be present before the manifest can be used.</summary>
      <returns>The assemblies that must be present before the manifest can be used.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveNativeReference.ContainedTypeLibraries">
      <summary>Gets or sets the type libraries of the native assembly.</summary>
      <returns>The type libraries of the native assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveNativeReference.ContainingReferenceFiles">
      <summary>Gets or sets the reference files.</summary>
      <returns>The reference files.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveNativeReference.Execute">
      <summary>Executes the ResolveNativeReference task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveNativeReference.NativeReferences">
      <summary>Gets or sets the Win32 native assembly references.</summary>
      <returns>The Win32 native assembly references.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.ResolveNonMSBuildProjectOutput">
      <summary>Determines the output files for non-MSBuild project references.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveNonMSBuildProjectOutput.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.ResolveNonMSBuildProjectOutput" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveNonMSBuildProjectOutput.Execute">
      <summary>Executes the ResolveNonMSBuildProjectOutput task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveNonMSBuildProjectOutput.PreresolvedProjectOutputs">
      <summary>Gets or sets an XML string containing resolved project outputs.</summary>
      <returns>An XML string containing resolved project outputs.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveNonMSBuildProjectOutput.ResolvedOutputPaths">
      <summary>Gets or sets resolved reference paths that preserve the original project reference attributes.</summary>
      <returns>The resolved reference paths that preserve the original project reference attributes.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveNonMSBuildProjectOutput.UnresolvedProjectReferences">
      <summary>Gets or sets project reference items that are in the MSBuild format.</summary>
      <returns>Project reference items that are in the MSBuild format.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.ResolveProjectBase">
      <summary>Represents the base class for <see cref="T:Microsoft.Build.Tasks.ResolveNonMSBuildProjectOutput" /> and <see cref="T:Microsoft.Build.Tasks.AssignProjectConfiguration" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveProjectBase.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.ResolveProjectBase" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveProjectBase.AddSyntheticProjectReferences(System.String)">
      <summary>Helper method for retrieving the extra “project references” passed in the solution blob. These come from dependencies expressed in the solution file itself.</summary>
      <param name="currentProjectAbsolutePath">The absolute path of the current project.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveProjectBase.GetProjectElement(Microsoft.Build.Framework.ITaskItem)">
      <summary>Retrieves the XML element representing the given project.</summary>
      <returns>Returns the project element.</returns>
      <param name="projectRef">The project reference item.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveProjectBase.GetProjectItem(Microsoft.Build.Framework.ITaskItem)">
      <summary>Retrieves the XML contents of the project element of the specified project.</summary>
      <returns>The XML contents of the project element of the specified project.</returns>
      <param name="projectRef">A project reference that contains a project GUID attribute.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveProjectBase.ProjectReferences">
      <summary>Gets or sets the project references.</summary>
      <returns>The project references.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.ResolveSDKReference">
      <summary>Resolves an SDKReference to a full path on disk</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveSDKReference.#ctor">
      <summary>Creates a new instance of ResolveSDKReference.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ResolveSDKReference.Execute">
      <summary>Execute the task.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveSDKReference.InstalledSDKs">
      <summary>The list of installed SDKs the location of the SDK, the SDKName metadata is the SDKName.</summary>
      <returns>Returns <see cref="T:Microsoft.Build.Framework.ITaskItem" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveSDKReference.LogResolutionErrorsAsWarnings">
      <summary>Specifies whether problems resolving SDKs are logged as warnings or errors. If the resolution problem is logged as an error the build will fail. If the resolution problem is logged as a warning we will warn and continue.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveSDKReference.References">
      <summary>References may be passed in so that the SDKNames can be resolved. Once resolved, the sdkroot paths can be appended to the references so RAR can find the assembly correctly in the sdk location.</summary>
      <returns>Returns <see cref="T:Microsoft.Build.Framework.ITaskItem" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveSDKReference.ResolvedSDKReferences">
      <summary>Resolved SDK References</summary>
      <returns>Returns <see cref="T:Microsoft.Build.Framework.ITaskItem" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveSDKReference.SDKReferences">
      <summary>Set of SDK References to resolve to paths on disk</summary>
      <returns>Returns <see cref="T:Microsoft.Build.Framework.ITaskItem" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveSDKReference.TargetedSDKArchitecture">
      <summary>Architecture of the SDKs we are targeting</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ResolveSDKReference.TargetedSDKConfiguration">
      <summary>Configuration for SDK's which are resolved</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.SGen">
      <summary>Implements the SGen task. Use the SGen element in your project file to create and execute this task. For usage and parameter information, see SGen Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.SGen.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.SGen" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.SGen.BuildAssemblyName">
      <summary>Gets or sets the assembly to generate serialization code for.</summary>
      <returns>The assembly to generate serialization code for.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SGen.BuildAssemblyPath">
      <summary>Gets or sets the path to the assembly to generate serialization code for.</summary>
      <returns>The path to the assembly to generate serialization code for.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SGen.DelaySign">
      <summary>Gets or sets a Boolean value that specifies whether the SGen task places the public key in the assembly (true) or fully signs the assembly (false).</summary>
      <returns>true if the SGen task places the public key in the assembly; false if the SGen task fully signs the assembly.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.SGen.GenerateCommandLineCommands">
      <summary>Gets the switches and other information that the command line tool must run directly from the command line and not from a response file.</summary>
      <returns>A string containing the switches and other information that the command line tool must run directly from the command line and not from a response file.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.SGen.GenerateFullPathToTool">
      <summary>Gets the full file path of the SGen tool.</summary>
      <returns>The full file path of the SGen tool.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SGen.KeyContainer">
      <summary>Gets or sets a container that holds a key pair. </summary>
      <returns>The container that holds a key pair.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SGen.KeyFile">
      <summary>Gets or sets a key pair or a public key to use to sign an assembly. </summary>
      <returns>A key pair or a public key to use to sign an assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SGen.Platform">
      <summary>Gets or sets the compiler platform to use to generate the output assembly.</summary>
      <returns>Returns the compiler platform to use.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SGen.References">
      <summary>Gets or sets the assemblies that are referenced by the types requiring XML serialization.</summary>
      <returns>The assemblies that are referenced by the types requiring XML serialization.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SGen.SdkToolsPath">
      <summary>Gets or sets the SDK Tools path.</summary>
      <returns>Returns the SDK Tools path.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SGen.SerializationAssembly">
      <summary>Gets or sets the generated serialization assembly.</summary>
      <returns>The generated serialization assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SGen.SerializationAssemblyName">
      <summary>Gets the name of the generated serialization assembly.</summary>
      <returns>The name of the generated serialization assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SGen.ShouldGenerateSerializer">
      <summary>Gets or sets a Boolean value that specifies whether the SGen task should generate a serialization assembly.</summary>
      <returns>true if the SGen task should generate a serialization assembly; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.SGen.SkipTaskExecution">
      <summary>Indicates whether task execution should be skipped.</summary>
      <returns>true if task execution should be skipped; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SGen.ToolName">
      <summary>Gets the name of the tool executable file (sgen.exe).</summary>
      <returns>The name of the tool executable file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SGen.Types">
      <summary>Gets or sets a list of specific types to generate serialization code for. SGen generates code only for these types.</summary>
      <returns> Returns a list of specific types.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SGen.UseProxyTypes">
      <summary>Gets or sets a Boolean value that specifies whether the SGen task generates serialization code only for the XML Web service proxy types.</summary>
      <returns>true if the SGen task generates serialization code only for the XML Web service proxy types; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.SGen.ValidateParameters">
      <summary>Validates input parameters and logs errors or warnings, if any. Returns a Boolean value that indicates whether task execution should proceed.</summary>
      <returns>true if task execution should proceed; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.SignFile">
      <summary>Implements the SignFile task. Use the SignFile element in your project file to create and execute this task. For usage and parameter information, see SignFile Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.SignFile.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.SignFile" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.SignFile.CertificateThumbprint">
      <summary>Gets or sets the certificate to use for signing.</summary>
      <returns>The certificate to use for signing.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.SignFile.Execute">
      <summary>Executes the SignFile task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SignFile.SigningTarget">
      <summary>Gets or sets the files to sign with the certificate.</summary>
      <returns>The files to sign with the certificate.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.SignFile.TimestampUrl">
      <summary>Gets or sets the URL of a time stamping server.</summary>
      <returns>The URL of a time stamping server.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.TaskExtension">
      <summary>Contains properties to help extend a task.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.TaskExtension.Log">
      <summary>Gets an instance of a <see cref="T:Microsoft.Build.Tasks.TaskLoggingHelperExtension" /> containing task logging methods.</summary>
      <returns>An instance of a <see cref="T:Microsoft.Build.Tasks.TaskLoggingHelperExtension" /> containing task logging methods.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.TaskLoggingHelperExtension">
      <summary>Enables logging of various messages. Also, enables loading and formatting of resources.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.TaskLoggingHelperExtension.#ctor(Microsoft.Build.Framework.ITask,System.Resources.ResourceManager,System.Resources.ResourceManager,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.TaskLoggingHelperExtension" /> class with the task instance, primary resources, shared resources, and a Help keyword prefix.</summary>
      <param name="taskInstance">A task containing an instance of the <see cref="T:Microsoft.Build.Tasks.TaskLoggingHelperExtension" /> class.</param>
      <param name="primaryResources">UI and string resources.</param>
      <param name="sharedResources">Shared UI and string resources.</param>
      <param name="helpKeywordPrefix">The prefix for composing Help keywords.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.TaskLoggingHelperExtension.FormatResourceString(System.String,System.Object[])">
      <summary>Loads the specified resource string and optionally formats it using the given arguments. The current thread's culture is used for formatting.</summary>
      <returns>The formatted string.</returns>
      <param name="resourceName">The name of the string resource to load.</param>
      <param name="args">Optional arguments for formatting the loaded string, or null.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.TaskLoggingHelperExtension.TaskSharedResources">
      <summary>Gets or sets the shared UI and string resources.</summary>
      <returns>The shared UI and string resources.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.ToolTaskExtension">
      <summary>Comprises extended utility methods for constructing a task that wraps a command line tool.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.ToolTaskExtension.AddCommandLineCommands(Microsoft.Build.Tasks.CommandLineBuilderExtension)">
      <summary>Fills the specified <see cref="T:Microsoft.Build.Tasks.CommandLineBuilderExtension" /> with the switches and other information that the command line tool must run from the command line and not from a response file.</summary>
      <param name="commandLine">The <see cref="T:Microsoft.Build.Tasks.CommandLineBuilderExtension" /> to fill.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.ToolTaskExtension.AddResponseFileCommands(Microsoft.Build.Tasks.CommandLineBuilderExtension)">
      <summary>Fills the specified <see cref="T:Microsoft.Build.Tasks.CommandLineBuilderExtension" /> with the switches and other information that the command line tool can run from a response file.</summary>
      <param name="commandLine">The <see cref="T:Microsoft.Build.Tasks.CommandLineBuilderExtension" /> to fill.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.ToolTaskExtension.Bag">
      <summary>Gets the collection of parameters used by the derived task class.</summary>
      <returns>The collection of parameters used by the derived task class.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ToolTaskExtension.GenerateCommandLineCommands">
      <summary>Gets the switches and other information that the command line tool must run directly from the command line and not from a response file.</summary>
      <returns>A string containing the switches and other information that the command line tool must run directly from the command line and not from a response file.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ToolTaskExtension.GenerateResponseFileCommands">
      <summary>Gets the switch used by the command line tool to specify the response file.</summary>
      <returns>The switch used by the command line tool to specify the response file.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.ToolTaskExtension.GetBoolParameterWithDefault(System.String,System.Boolean)">
      <summary>Gets the value of the specified Boolean parameter.</summary>
      <returns>The parameter value.</returns>
      <param name="parameterName">The name of the parameter to return.</param>
      <param name="defaultValue">The value to return if <paramref name="parameterName" /> does not exist in the <see cref="P:Microsoft.Build.Tasks.ToolTaskExtension.Bag" />.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.ToolTaskExtension.GetIntParameterWithDefault(System.String,System.Int32)">
      <summary>Gets the value of the specified integer parameter.</summary>
      <returns>The parameter value.</returns>
      <param name="parameterName">The name of the parameter to return.</param>
      <param name="defaultValue">The value to return if <paramref name="parameterName" /> does not exist in the <see cref="P:Microsoft.Build.Tasks.ToolTaskExtension.Bag" />.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.ToolTaskExtension.HasLoggedErrors">
      <summary>Returns whether this ToolTask has logged any errors.</summary>
      <returns>true if the ToolTask logged errors, false if otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.ToolTaskExtension.Log">
      <summary>Gets an instance of a <see cref="T:Microsoft.Build.Tasks.TaskLoggingHelperExtension" /> class containing task logging methods.</summary>
      <returns>An instance of a <see cref="T:Microsoft.Build.Tasks.TaskLoggingHelperExtension" /> class containing task logging methods.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Touch">
      <summary>Implements the Touch task. Use the Touch element in your project file to create and execute this task. For usage and parameter information, see Touch Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Touch.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Touch" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Touch.AlwaysCreate">
      <summary>Gets or sets a Boolean value that specifies whether to create any files that do not already exist.</summary>
      <returns>true if the Touch task creates any files that do not already exist; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Touch.Execute">
      <summary>Executes the Touch task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Touch.Files">
      <summary>Gets or sets the files to touch.</summary>
      <returns>The files to touch.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Touch.ForceTouch">
      <summary>Gets or sets a Boolean value that specifies whether to force a file touch even if the files are read-only.</summary>
      <returns>true if the Touch task forces a file touch even if the files are read-only; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Touch.Time">
      <summary>Gets or sets a time other than the current time. The format must be a format that is acceptable to the <see cref="M:System.DateTime.Parse(System.String)" /> method.</summary>
      <returns>A time other than the current time.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Touch.TouchedFiles">
      <summary>Gets or sets the items that were successfully touched.</summary>
      <returns>The items that were successfully touched.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.UnregisterAssembly">
      <summary>Implements the UnregisterAssembly task. Use the UnregisterAssembly element in your project file to create and execute this task. For usage and parameter information, see UnregisterAssembly Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.UnregisterAssembly.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.UnregisterAssembly" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.UnregisterAssembly.Assemblies">
      <summary>Gets or sets the assemblies to be unregistered.</summary>
      <returns>The assemblies to be unregistered.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.UnregisterAssembly.AssemblyListFile">
      <summary>Gets or sets information about the state between the RegisterAssembly task and the UnregisterAssembly task that the UnregisterAssembly task uses to avoid unregistering an assembly that failed to register in the RegisterAssembly task.</summary>
      <returns>Information about the state between the RegisterAssembly task and the UnregisterAssembly task.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.UnregisterAssembly.Execute">
      <summary>Executes the UnregisterAssembly task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.UnregisterAssembly.TypeLibFiles">
      <summary>Gets or sets a Boolean value that specifies whether to unregister the specified type library from the specified assembly.</summary>
      <returns>true if the UnregisterAssembly task should unregister the specified type library from the specified assembly; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.UpdateManifest">
      <summary>Updates selected properties in a manifest and resigns.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.UpdateManifest.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.UpdateManifest" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.UpdateManifest.ApplicationManifest">
      <summary>Gets or sets the application manifest.</summary>
      <returns>The application manifest.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.UpdateManifest.ApplicationPath">
      <summary>Gets or sets the path of the application manifest.</summary>
      <returns>The path of the application manifest.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.UpdateManifest.Execute">
      <summary>Executes the task.</summary>
      <returns>true if task execution succeeded; <see cref="T:System.InvalidOperationException" /> on error.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.UpdateManifest.InputManifest">
      <summary>Gets or sets the manifest to update.</summary>
      <returns>The manifest to update.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.UpdateManifest.OutputManifest">
      <summary>Gets or sets the manifest that contains updated properties.</summary>
      <returns>The manifest that contains updated properties.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.UpdateManifest.TargetFrameworkVersion">
      <summary>Gets or sets the target framework version.</summary>
      <returns>Returns the target framework version.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Vbc">
      <summary>Implements the Vbc task. Use the Vbc element in your project file to create and execute this task. For usage and parameter information, see Vbc Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Vbc.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Vbc" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Vbc.AddResponseFileCommands(Microsoft.Build.Tasks.CommandLineBuilderExtension)">
      <summary>Fills the specified <paramref name="commandLine" /> parameter with switches and other information that can go into a response file.</summary>
      <param name="commandLine">Command line builder to add arguments to.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.BaseAddress">
      <summary>Gets or sets the base address of the DLL.</summary>
      <returns>The base address of the DLL.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Vbc.CallHostObjectToExecute">
      <summary>Compiles the project through the host object.</summary>
      <returns>true if compilation succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.DisabledWarnings">
      <summary>Gets or sets one or more warnings to suppress that are specified by the numeric part of the warning identifier.</summary>
      <returns>The warnings to suppress that are specified by the numeric part of the warning identifier.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.DocumentationFile">
      <summary>Turns on documentation comments processing and gets or sets the output documentation XML file.</summary>
      <returns>The output documentation XML file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.ErrorReport">
      <summary>Gets or sets a string value that specifies how the task will report internal compiler errors.</summary>
      <returns>A string value that specifies how the task will report internal compiler errors.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Vbc.Execute">
      <summary>Override Execute so that we can move the PDB file, if we need to, after the compiler is done.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.GenerateDocumentation">
      <summary>Gets or sets a Boolean value that specifies whether to process documentation comments to an XML file.</summary>
      <returns>true if the Vbc task processes documentation comments to a XML file; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Vbc.GenerateFullPathToTool">
      <summary>Returns the full file path of the tool.</summary>
      <returns>The full file path of the tool.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.Imports">
      <summary>Gets or sets the item collections to import namespaces from.</summary>
      <returns>The item collections to import namespaces from.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Vbc.InitializeHostObject">
      <summary>Returns a host object initialization status value that indicates what the next action should be.</summary>
      <returns>A host object initialization status value that indicates what the next action should be.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.LangVersion">
      <summary>Gets or sets the language version.</summary>
      <returns>Returns the language version.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Vbc.LogEventsFromTextOutput(System.String,Microsoft.Build.Framework.MessageImportance)">
      <summary>This method intercepts the lines to be logged coming from STDOUT from VBC.</summary>
      <param name="singleLine">A single line captured from the STDOUT of the vbc compiler</param>
      <param name="messageImportance">The message importance: High,Low, or Normal</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.ModuleAssemblyName">
      <summary>Gets or sets the name of the assembly that this module will be a part of.</summary>
      <returns>The name of the assembly that this module will be a part of.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.NoStandardLib">
      <summary>Gets or sets a Boolean value that specifies whether the compiler should not reference the standard libraries.</summary>
      <returns>true if the compiler should not reference the standard libraries; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.NoVBRuntimeReference">
      <summary>Gets or sets a Boolean value that specifies whether the compiler should compile without a reference to the Visual Basic Runtime Library.</summary>
      <returns>true if the compiler should compile without a reference to the Visual Basic Runtime Library; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.NoWarnings">
      <summary>Gets or sets a Boolean value that specifies whether the task will suppress all warnings.</summary>
      <returns>true if the task will suppress all warnings; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.OptionCompare">
      <summary>Gets or sets a string value that specifies how string comparisons are made.</summary>
      <returns>A string value that specifies how string comparisons are made.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.OptionExplicit">
      <summary>Gets or sets a Boolean value that specifies whether explicit declaration of variables is required.</summary>
      <returns>true if explicit declaration of variables is required; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.OptionInfer">
      <summary>Gets or sets a Boolean value that specifies whether to use local type inference in variable declarations.</summary>
      <returns>true if the Vbc task will use local type inference in variable declarations; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.OptionStrict">
      <summary>Gets or sets a Boolean value that specifies whether the task enforces strict type semantics to restrict implicit type conversions.</summary>
      <returns>true if the task enforces strict type semantics; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.OptionStrictType">
      <summary>Gets or sets a value for the <see cref="P:Microsoft.Build.Tasks.Vbc.OptionStrict" /> property. Currently, the value can be set to "custom" and specifies whether the task generates warnings for implicit narrowing type conversions.</summary>
      <returns>A value for the <see cref="P:Microsoft.Build.Tasks.Vbc.OptionStrict" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.PdbFile">
      <summary>The path to the .Pdb file.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.RemoveIntegerChecks">
      <summary>Gets or sets a Boolean value that specifies whether to disable integer overflow error checks. </summary>
      <returns>true if the Vbc task will disable integer overflow error checks; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.RootNamespace">
      <summary>Gets or sets the root namespace for all type declarations.</summary>
      <returns>The root namespace for all type declarations.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.SdkPath">
      <summary>Gets or sets the location of mscorlib.dll and microsoft.visualbasic.dll.</summary>
      <returns>The location of mscorlib.dll and microsoft.visualbasic.dll.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.TargetCompactFramework">
      <summary>Gets or sets a Boolean value that specifies whether to target the .NET Compact Framework.</summary>
      <returns>true if the Vbc task should target the .NET Compact Framework; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.ToolName">
      <summary>Gets the name of the tool executable file (vbc.exe).</summary>
      <returns>The name of the tool executable file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.UseHostCompilerIfAvailable">
      <summary>Gets or sets a Boolean value that specifies whether to use the in-process compiler object, if available. Used only by Visual Studio.</summary>
      <returns>true if the Vbc task will use the in-process compiler object, if available; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Vbc.ValidateParameters">
      <summary>Validates input parameters and logs errors or warnings, if any. Returns a Boolean value that indicates whether task execution should proceed.</summary>
      <returns>true if task execution should proceed; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.VBRuntime">
      <summary>Specifies whether to compile with the default Visual Basic runtime.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.VBRuntimePath">
      <summary>The path to the Visual Basic runtime to use.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.Verbosity">
      <summary>Gets or sets the verbosity of the compiler’s output, which can be Quiet, Normal, or Verbose.</summary>
      <returns>The verbosity of the compiler’s output.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.WarningsAsErrors">
      <summary>Gets or sets a list of warnings to treat as errors.</summary>
      <returns>A list of warnings to treat as errors.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Vbc.WarningsNotAsErrors">
      <summary>Gets or sets a list of warnings that are not treated as errors.</summary>
      <returns>A list of warnings that are not treated as errors.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Warning">
      <summary>Implements the Warning task. Use the Warning element in your project file to create and execute this task. For usage and parameter information, see Warning Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Warning.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Warning" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Warning.Code">
      <summary>Gets or sets the warning code.</summary>
      <returns>The warning code.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Warning.Execute">
      <summary>Executes the Warning task.</summary>
      <returns>true in all cases.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Warning.File">
      <summary>Relevant file if any. If none is provided, the file containing the Warning task will be used.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Warning.HelpKeyword">
      <summary>Gets or sets the Help keyword to associate with the warning.</summary>
      <returns>The Help keyword to associate with the warning.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Warning.Text">
      <summary>Gets or sets the warning text that MSBuild logs.</summary>
      <returns>The warning text that MSBuild logs.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.WinMDExp">
      <summary>Exports a managed assembly to a windows runtime metadata.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.WinMDExp.#ctor">
      <summary>Creates an instance of WinMDExp.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.WinMDExp.AddCommandLineCommands(Microsoft.Build.Tasks.CommandLineBuilderExtension)">
      <summary>Fills the provided CommandLineBuilderExtension with all the command line options used when executing this tool</summary>
      <param name="commandLine">Gets filled with command line commands</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.WinMDExp.DisabledWarnings">
      <summary>Warning codes to disable</summary>
      <returns>Returns the warning codes to disable.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.WinMDExp.GenerateFullPathToTool">
      <summary>The full path of the tool to execute.</summary>
      <returns>Returns the full path of the tool to execute.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WinMDExp.InputDocumentationFile">
      <summary>Input documentation file</summary>
      <returns>Returns the input documentation file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WinMDExp.InputPDBFile">
      <summary>Input PDB file</summary>
      <returns>Returns the input PDB file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WinMDExp.OutputDocumentationFile">
      <summary>Output documentation file</summary>
      <returns>Returns the output documentation file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WinMDExp.OutputPDBFile">
      <summary>Output PDB file</summary>
      <returns>Returns the output PDB file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WinMDExp.OutputWindowsMetadataFile">
      <summary>Output windows metadata file .winmd</summary>
      <returns>Returns the output windows metadata file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WinMDExp.References">
      <summary>Set of references to pass to the winmdexp tool.</summary>
      <returns>Returnsthe set of references.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WinMDExp.SdkToolsPath">
      <summary>Path to the SDK directory which contains this tool</summary>
      <returns>Returns the SDK directory path.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.WinMDExp.SkipTaskExecution">
      <summary>Returns true if task execution is not necessary. Executed after ValidateParameters</summary>
      <returns>Returns the flag indicating whether task execution is skipped.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WinMDExp.StandardErrorEncoding">
      <summary>Gets the value which specifies the encoding of the captured task standard error stream.</summary>
      <returns>Returns the value specifying the error stream encoding.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WinMDExp.StandardOutputEncoding">
      <summary>Gets the value which specifies the encoding of the captured task standard output stream.</summary>
      <returns>Returns the value specifying the output stream encoding.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WinMDExp.ToolName">
      <summary>The name of the tool to execute.</summary>
      <returns>Returns the tool name.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WinMDExp.TreatWarningsAsErrors">
      <summary>Gets or sets the flag indicating whether to treat warnings as errors.</summary>
      <returns>Returns the flag indicating whether to treat warnings as errors.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WinMDExp.UTF8Output">
      <summary>Gets or sets the flag indicating whether to use UTF-8 output stream encoding.</summary>
      <returns>Returns the flag indicating UTF-8 output stream encoding. </returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.WinMDExp.ValidateParameters">
      <summary>Validate parameters, log errors and warnings and return true if Execute should proceed.</summary>
      <returns>Returns the flag indicating whether execute should proceed.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WinMDExp.WinMDModule">
      <summary>WinMDModule to generate the WinMDFile for.</summary>
      <returns>Returns the WinMDModule.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.WriteCodeFragment">
      <summary>Generates a temporary code file with the specified generated code fragment.  Does not delete the file.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.WriteCodeFragment.#ctor">
      <summary>Creates a temporary code file with the specified generated code fragment.  </summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.WriteCodeFragment.AssemblyAttributes">
      <summary>Gets or sets the attributes to write.</summary>
      <returns>Returns an item whose value is the full type name of he attribute, for example, "System.AssemblyVersionAttribute".</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.WriteCodeFragment.Execute">
      <summary>Executes the WriteCodeFragment method.</summary>
      <returns>Returns true if the code fragment was written.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WriteCodeFragment.Language">
      <summary>Gets or sets the language of code to generate.</summary>
      <returns>Returns the language of code to generate.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WriteCodeFragment.OutputDirectory">
      <summary>Gets or sets the destination folder for the generated code, typically the intermediate folder.</summary>
      <returns>Returns the destination folder for the generated code.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WriteCodeFragment.OutputFile">
      <summary>Gets or sets the path to the file that was generated.</summary>
      <returns>Returns the path to the file that was generated.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.WriteLinesToFile">
      <summary>Implements the WriteLinesToFile task. Use the WriteLinesToFile element in your project file to create and execute this task. For usage and parameter information, see WriteLinesToFileWriteLinesToFile Task.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.WriteLinesToFile.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.WriteLinesToFile" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.WriteLinesToFile.Encoding">
      <summary>Gets or sets the system text encoding, for example UTF-8.</summary>
      <returns>Returns the system text encoding.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.WriteLinesToFile.Execute">
      <summary>Executes the WriteLinesToFile task.</summary>
      <returns>true if task execution succeeded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WriteLinesToFile.File">
      <summary>Gets or sets the file to write the items to.</summary>
      <returns>The file to write the items to.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WriteLinesToFile.Lines">
      <summary>Gets or sets the items to write to the file.</summary>
      <returns>The items to write to the file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.WriteLinesToFile.Overwrite">
      <summary>Gets or sets a Boolean value that specifies whether the task overwrites any existing content in the file.</summary>
      <returns>true if the task overwrites any existing content in the file; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.XamlTaskFactory">
      <summary>The task factory provider for XAML tasks.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.XamlTaskFactory.#ctor">
      <summary>Creates a task factory for XAML tasks.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.XamlTaskFactory.CleanupTask(Microsoft.Build.Framework.ITask)">
      <summary>Cleans up any context or state that may have been built up for a given task. </summary>
      <param name="task">The task to clean up.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.XamlTaskFactory.CreateTask(Microsoft.Build.Framework.IBuildEngine)">
      <summary>Creates an instance of the task to be used.</summary>
      <returns>Returns the new task factory.</returns>
      <param name="taskFactoryLoggingHost">The task factory logging host that logs messages in the context of the task.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.XamlTaskFactory.FactoryName">
      <summary>Gets the name of this task factory, to be used in error messages. For example, Task "Mytask" failed to load from "FactoryName".</summary>
      <returns>Returns the name of this task factory</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.XamlTaskFactory.GetTaskParameters">
      <summary>Gets the type information for the parameters of the task.</summary>
      <returns>Returns the type information as an array of PropertyInfo.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.XamlTaskFactory.Initialize(System.String,System.Collections.Generic.IDictionary{System.String,Microsoft.Build.Framework.TaskPropertyInfo},System.String,Microsoft.Build.Framework.IBuildEngine)">
      <summary>Initializes the factory.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
      <param name="taskName">The name of the task.</param>
      <param name="taskParameters">The parameters to pass to the task.</param>
      <param name="taskElementContents">The element contents.</param>
      <param name="taskFactoryLoggingHost">The task factory logging host that logs messages in the context of the task.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.XamlTaskFactory.TaskElementContents">
      <summary>Gets or sets the contents of the UsingTask body.</summary>
      <returns>Returns the contents of the UsingTask body.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XamlTaskFactory.TaskName">
      <summary>Gets or sets the name of the task, pulled from the XAML.</summary>
      <returns>Returns the name of the task.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XamlTaskFactory.TaskNamespace">
      <summary>Gets or sets the namespace of the task, pulled from the XAML.</summary>
      <returns>Returns the namespace of the task.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XamlTaskFactory.TaskType">
      <summary>Gets or sets the type of the task, pulled from the XAML.</summary>
      <returns>Returns the task type.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.XmlPeek">
      <summary>A task that returns values as specified by XPath Query from an XML file.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.XmlPeek.#ctor">
      <summary>Creates a task that returns values as specified by XPath Query from an XML file.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.XmlPeek.Execute">
      <summary>Executes the XMLPeek task.</summary>
      <returns>Returns true if transformation succeeds; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XmlPeek.Namespaces">
      <summary>Gets or sets the namespaces for XPath query's prefixes.</summary>
      <returns>Returns the namespaces.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XmlPeek.Query">
      <summary>Gets or sets the XPath Query.</summary>
      <returns>Returns the XPath Query.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XmlPeek.Result">
      <summary>Gets the results returned by this task.</summary>
      <returns>Returns the results.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XmlPeek.XmlContent">
      <summary>Gets or sets the XML input as a string.</summary>
      <returns>Returns the XML input as a string.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XmlPeek.XmlInputPath">
      <summary>Gets or sets the XML input as a file path.</summary>
      <returns>Returns the XML input as a file path.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.XmlPoke">
      <summary>A task that sets values as specified by XPath Query into a XML file.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.XmlPoke.#ctor">
      <summary>Creates a task that sets values as specified by XPath Query into a XML file.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.XmlPoke.Execute">
      <summary>Executes the XMLPoke task.</summary>
      <returns>Returns true if transformation succeeds.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XmlPoke.Namespaces">
      <summary>Gets or sets the namespaces for XPath query's prefixes.</summary>
      <returns>Returns the namespaces for XPath query's prefixes.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XmlPoke.Query">
      <summary>Gets or sets the XPath Query.</summary>
      <returns>Returns the XPath Query.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XmlPoke.Value">
      <summary>Gets or sets the output file.</summary>
      <returns>Returns the output file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XmlPoke.XmlInputPath">
      <summary>Gets or sets the XML input as file path.</summary>
      <returns>Returns the XML input as file path.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.XslTransformation">
      <summary>Transforms an XML input by using an XSLT or Compiled XSLT and outputs to an output device or a file.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.XslTransformation.#ctor">
      <summary>Creates an XML transformation.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.XslTransformation.Execute">
      <summary>Executes the XslTransform task.</summary>
      <returns>Returns true if the transformation succeeds; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XslTransformation.OutputPaths">
      <summary>Gets or sets the output files for the XML transformation.</summary>
      <returns>Returns the output files for the XML transformation.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XslTransformation.Parameters">
      <summary>Gets or sets the parameters to the XSLT Input document.</summary>
      <returns>Returns the parameters to the XSLT Input document.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XslTransformation.XmlContent">
      <summary>Gets or sets the XML input as a string.</summary>
      <returns>Returns the XML input as a string.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XslTransformation.XmlInputPaths">
      <summary>Gets or sets the XML input files.</summary>
      <returns>Returns the XML input files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XslTransformation.XslCompiledDllPath">
      <summary>Gets or sets the compiled XSLT.</summary>
      <returns>Returns the path of the compiled XSLT assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XslTransformation.XslContent">
      <summary>Gets or sets the XSLT input as string.</summary>
      <returns>Returns the XSLT input as string.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.XslTransformation.XslInputPath">
      <summary>Gets or sets the XSLT input file.</summary>
      <returns>Returns the XSLT input file.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder">
      <summary>The top-level object for the bootstrapper system.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder.#ctor">
      <summary>Initializes a new instance of the BootstrapperBuilder class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder.#ctor(System.String)">
      <summary>Initializes a new instance of the BootstrapperBuilder class, using the specified version of Visual Studio.</summary>
      <param name="visualStudioVersion">The version of Visual Studio to use.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder.Build(Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings)">
      <summary>Generates a bootstrapper by using the specified settings.</summary>
      <returns>The results of the bootstrapper generation.</returns>
      <param name="settings">The properties used to create the bootstrapper.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder.GetOutputFolders(System.String[],System.String,System.String,Microsoft.Build.Tasks.Deployment.Bootstrapper.ComponentsLocation)">
      <summary>Returns the directories to which bootstrapper component files are copied when they are created, given the specified settings.</summary>
      <returns>The directories to which bootstrapper component files are copied when they are created, given the specified settings.</returns>
      <param name="productCodes">The <see cref="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IProduct.ProductCode" /> values of the selected components.</param>
      <param name="culture">The culture used to create the bootstrapper.</param>
      <param name="fallbackCulture">The fallback culture used to create the bootstrapper.</param>
      <param name="componentsLocation">Specifies how the bootstrapper packages the selected components.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder.Path">
      <summary>Gets or sets the location of the required bootstrapper files.</summary>
      <returns>The path for the required bootstrapper files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder.Products">
      <summary>Gets all products available at the current bootstrapper <see cref="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder.Path" />.</summary>
      <returns>All products available at the current bootstrapper <see cref="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder.Path" />.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildMessage">
      <summary>Defines messages that occur during the <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder" /> build operation.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildMessage.HelpId">
      <summary>Gets the MSBuild Help ID for the host IDE.</summary>
      <returns>An integer value that represents the Help ID.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildMessage.HelpKeyword">
      <summary>Gets the MSBuild F1 Help keyword for the host IDE, or null.</summary>
      <returns>A string that represents the F1 Help keyword.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildMessage.Message">
      <summary>Gets the details of the build message.</summary>
      <returns>A text string that describes the details of the build message.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildMessage.Severity">
      <summary>Gets the severity of the build message.</summary>
      <returns>The severity of the build message as defined by the <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildMessageSeverity" /> class.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildMessageSeverity">
      <summary>This enumeration provides three levels of importance for build messages.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildMessageSeverity.Info">
      <summary>Indicates that the message corresponds to build information.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildMessageSeverity.Warning">
      <summary>Indicates that the message corresponds to a build warning.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildMessageSeverity.Error">
      <summary>Indicates that the message corresponds to a build error.</summary>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildResults">
      <summary>Represents the results of the <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder" /> build operation.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildResults.ComponentFiles">
      <summary>Gets paths to the copied component installer files.</summary>
      <returns>A string array that contains paths for the copied component installer files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildResults.KeyFile">
      <summary>Gets the path of the generated primary bootstrapper file.</summary>
      <returns>A string representing the path of the generated primary bootstrapper file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildResults.Messages">
      <summary>Gets the build messages generated from the bootstrapper build.</summary>
      <returns>An array of <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildMessage" /> messages from the bootstrapper build.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildResults.Succeeded">
      <summary>Gets a Boolean value that indicates whether the bootstrapper build was successful.</summary>
      <returns>A Boolean value indicating whether the bootstrapper build was successful. Returns true if the bootstrapper build was successful; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings">
      <summary>Defines the settings for the bootstrapper build operation.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.ApplicationFile">
      <summary>Gets or sets the file to be installed after the bootstrapper installs the required components.</summary>
      <returns>The name of the file to be installed after the required components are installed.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.ApplicationName">
      <summary>Gets or sets the name of the application to be installed after the bootstrapper installs all required components.</summary>
      <returns>The name of the application to be installed after the required components are installed.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.ApplicationRequiresElevation">
      <summary>Gets or sets a value indicating whether a component runs with elevated permissions when it is installed on a target computer.</summary>
      <returns>true if the installed component runs with elevated permissions; otherwise false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.ApplicationUrl">
      <summary>Gets or sets the expected source location when the bootstrapper is published to a Web site.</summary>
      <returns>A URL that represents the expected source location when the bootstrapper is published to a Web site.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.ComponentsLocation">
      <summary>Gets or sets the installation location for bootstrapper components.</summary>
      <returns>The <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ComponentsLocation" /> installation location for bootstrapper components.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.ComponentsUrl">
      <summary>Gets or sets the location to which the bootstrapper will copy components if <see cref="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.ComponentsLocation" /> is set to <see cref="F:Microsoft.Build.Tasks.Deployment.Bootstrapper.ComponentsLocation.Absolute" />.</summary>
      <returns>The location to which the bootstrapper will copy components if <see cref="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.ComponentsLocation" /> is set to <see cref="F:Microsoft.Build.Tasks.Deployment.Bootstrapper.ComponentsLocation.Absolute" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.CopyComponents">
      <summary>Gets or sets a value indicating whether the bootstrapper components will be copied to the build output directory.</summary>
      <returns>true if the bootstrapper components will be copied to the build output directory; otherwise false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.FallbackLCID">
      <summary>Gets or sets the culture identifier to use if the LCID identifier is not available.</summary>
      <returns>An integer value that represents the culture identifier to use if the LCID identifier is not available.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.LCID">
      <summary>Gets or sets the culture identifier specifying the culture for which the bootstrapper is built.</summary>
      <returns>An integer value that represents the culture identifier specifying the culture for which the bootstrapper is built.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.OutputPath">
      <summary>Gets or sets the file location to which to copy output files.</summary>
      <returns>A string representing the file location to which to copy output files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.ProductBuilders">
      <summary>Gets the product builders to use for generating the bootstrapper.</summary>
      <returns>A collection of product builders (<see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilderCollection" />) to use for generating the bootstrapper.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.SupportUrl">
      <summary>Gets or sets the URL for the Web site that contains support information for the bootstrapper.</summary>
      <returns>The URL for the Web site that contains support information for the bootstrapper.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.Validate">
      <summary>Gets or sets a value indicating whether the bootstrapper performs XML validation on the component manifests.</summary>
      <returns>true if the bootstrapper performs XML validation on the component manifests; otherwise false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ComponentsLocation">
      <summary>Specifies the way that required components will be published.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.Bootstrapper.ComponentsLocation.HomeSite">
      <summary>Specifies that products be found according to the redistributable vendor's designated URL.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.Bootstrapper.ComponentsLocation.Relative">
      <summary>Specifies that products be located relative to the generated bootstrapper.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.Bootstrapper.ComponentsLocation.Absolute">
      <summary>Specifies that products be located at a specific location.</summary>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBootstrapperBuilder">
      <summary>Exposes functionality that you must have to create a bootstrapper.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBootstrapperBuilder.Build(Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings)">
      <summary>Generates a bootstrapper based on the specified settings.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildResults" /> object that represents the results of the bootstrapper generation.</returns>
      <param name="settings">The properties used to create the bootstrapper.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBootstrapperBuilder.Path">
      <summary>Gets or sets the location of the required bootstrapper files.</summary>
      <returns>A string value that represents the location of the required bootstrapper files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBootstrapperBuilder.Products">
      <summary>Gets all products available at the current bootstrapper path.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductCollection" /> object that represents all products available at the current bootstrapper <see cref="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBootstrapperBuilder.Path" />.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildMessage">
      <summary>Represents messages that occur during the <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder" /> build operation.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildMessage.HelpId">
      <summary>Gets the MSBuild Help ID for the host IDE.</summary>
      <returns>An integer representing the MSBuild Help ID for the host IDE.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildMessage.HelpKeyword">
      <summary>Gets the MSBuild F1 Help keyword for the host IDE.</summary>
      <returns>The MSBuild F1 Help keyword for the host IDE.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildMessage.Message">
      <summary>Gets a description of the details of the build message.</summary>
      <returns>A text string that describes the details of the build message.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildMessage.Severity">
      <summary>Gets the severity of the build message.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildMessageSeverity" /> object that represents the severity of the build message.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildResults">
      <summary>Represents the results of the <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder" /> build operation.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildResults.ComponentFiles">
      <summary>Gets the file paths to the copied component installer files.</summary>
      <returns>Returns an array of strings containing the file paths to the copied component installer files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildResults.KeyFile">
      <summary>Gets the file path to the generated primary bootstrapper file.</summary>
      <returns>Returns a string representing the file path to the generated primary bootstrapper file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildResults.Messages">
      <summary>Gets the build messages generated from a bootstrapper build.</summary>
      <returns>Returns an array of <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildMessage" /> objects representing the build messages generated from a bootstrapper build.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildResults.Succeeded">
      <summary>Indicates whether the bootstrapper build was successful.</summary>
      <returns>Returns a Boolean value indicating whether the bootstrapper build was successful. Returns true if the bootstrapper build was successful, false if not successful.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildSettings">
      <summary>Defines the settings for the bootstrapper build operation.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildSettings.ApplicationFile">
      <summary>Gets or sets the file to be installed after the bootstrapper installs the required components.</summary>
      <returns>The name of the file to be installed after the bootstrapper installs the required components.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildSettings.ApplicationName">
      <summary>Gets or sets the name of the application to be installed after the bootstrapper installs all required components.</summary>
      <returns>The name of the application to be installed after the bootstrapper installs all required components.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildSettings.ApplicationRequiresElevation">
      <summary>Gets or sets a value indicating whether a component runs with elevated permissions when it is installed on a target computer.</summary>
      <returns>true if the installed component runs with elevated permissions; otherwise false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildSettings.ApplicationUrl">
      <summary>Gets or sets the expected source location when the bootstrapper is published to a Web site.</summary>
      <returns>The expected source location when the bootstrapper is published to a Web site.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildSettings.ComponentsLocation">
      <summary>Gets or sets the installation location for bootstrapper components.</summary>
      <returns>The installation location (<see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ComponentsLocation" />) for bootstrapper components.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildSettings.ComponentsUrl">
      <summary>Gets or sets the location to which the bootstrapper will copy components if <see cref="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.ComponentsLocation" /> is set to <see cref="F:Microsoft.Build.Tasks.Deployment.Bootstrapper.ComponentsLocation.Absolute" />.</summary>
      <returns>The location to which the bootstrapper will copy components if <see cref="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BuildSettings.ComponentsLocation" /> is set to <see cref="F:Microsoft.Build.Tasks.Deployment.Bootstrapper.ComponentsLocation.Absolute" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildSettings.CopyComponents">
      <summary>Gets or sets a value indicating whether the bootstrapper components will be copied to the build output directory.</summary>
      <returns>true if the bootstrapper components will be copied to the build output directory; otherwise false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildSettings.FallbackLCID">
      <summary>Gets or sets the culture identifier to use if the LCID identifier is not available.</summary>
      <returns>An integer value that represents the culture identifier to use if the LCID identifier is not available.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildSettings.LCID">
      <summary>Gets or sets the culture identifier specifying the culture for which the bootstrapper is built.</summary>
      <returns>An integer value that represents the culture identifier specifying the culture for which the bootstrapper is built.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildSettings.OutputPath">
      <summary>Gets or sets the file location to which to copy output files.</summary>
      <returns>A string representing the file location to which to copy output files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildSettings.ProductBuilders">
      <summary>Gets the product builders to use for generating the bootstrapper.</summary>
      <returns>The product builders to use for generating the bootstrapper.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildSettings.SupportUrl">
      <summary>Gets or sets the URL for the Web site that contains support information for the bootstrapper.</summary>
      <returns>The URL for the Web site that contains support information for the bootstrapper.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IBuildSettings.Validate">
      <summary>Gets or sets a value indicating whether the bootstrapper performs XML validation on the component manifests.</summary>
      <returns>true if the bootstrapper performs XML validation on the component manifests; otherwise false.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.IProduct">
      <summary>Represents a product that the <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder" /> finds in the <see cref="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder.Path" /> property.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IProduct.Includes">
      <summary>Gets all products that this product installs.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductCollection" /> object that contains all products that this product installs.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IProduct.Name">
      <summary>Gets a human-readable name for this product.</summary>
      <returns>A string that contains a human-readable name for this product.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IProduct.ProductBuilder">
      <summary>Gets the <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder" /> representation of this product.</summary>
      <returns>Returns a <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder" /> object that represents this product.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IProduct.ProductCode">
      <summary>Gets the unique product identifier of this product.</summary>
      <returns>Returns a string that specifies the unique product identifier of this product.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.IProductBuilder">
      <summary>Represents a buildable version of a <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product" />.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IProductBuilder.Product">
      <summary>Gets the <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product" /> object that corresponds to this <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder" /> object.</summary>
      <returns>The <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product" /> object that corresponds to this <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder" /> object.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.IProductBuilderCollection">
      <summary>Contains a collection of <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder" /> objects.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.IProductBuilderCollection.Add(Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder)">
      <summary>Adds a <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder" /> object to the product builder collection.</summary>
      <param name="builder">The <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder" /> object to add to the collection.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.IProductCollection">
      <summary>Represents a collection of <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product" /> objects.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.IProductCollection.Count">
      <summary>Gets the number of elements that are contained in the product collection.</summary>
      <returns>The number of elements that are contained in the product collection.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.IProductCollection.Item(System.Int32)">
      <summary>Retrieves the product at the specified index.</summary>
      <returns>The <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product" /> object at the specified index.</returns>
      <param name="index">The zero-based index of the element to get.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.IProductCollection.Product(System.String)">
      <summary>Retrieves the product with the specified product code.</summary>
      <returns>The <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product" /> object that has the specified product code; null if the specified product code is not found.</returns>
      <param name="productCode">The unique identifier of the product to get.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product">
      <summary>Represents a product that the <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder" /> finds in the <see cref="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder.Path" /> property.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product.#ctor">
      <summary>Initializes a new instance of the Product class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product.Includes">
      <summary>Gets all products that this product installs.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductCollection" /> object that represents all products that this product installs.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product.Name">
      <summary>Gets a human-readable name for this product.</summary>
      <returns>A string that represents a human-readable name for this product.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product.ProductBuilder">
      <summary>Gets a buildable version of the product.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder" /> object that represents a buildable version of the product.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product.ProductCode">
      <summary>Gets a string that indicates the unique product identifier of this product.</summary>
      <returns>A string that indicates the unique product identifier of this product.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder">
      <summary>Represents a buildable version of a <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product" />.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder.Product">
      <summary>Gets the <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product" /> object that corresponds to this <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder" /> object.</summary>
      <returns>Returns the <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product" /> object that corresponds to this <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder" /> object.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilderCollection">
      <summary>Contains a collection of <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder" /> objects.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilderCollection.Add(Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder)">
      <summary>Adds a <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder" /> object to the product builder collection.</summary>
      <param name="builder">The <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilder" /> object to add to the collection.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductBuilderCollection.GetEnumerator">
      <summary>Returns an enumerator that can iterate through the product builder collection.</summary>
      <returns>An enumerator that can iterate through the product builder collection.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductCollection">
      <summary>This interface represents a collection of <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product" /> objects. The collection is a closed set generated by the <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder" /> based on the <see cref="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.BootstrapperBuilder.Path" /> property. The client cannot add or remove items from this collection.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductCollection.Count">
      <summary>Gets the number of elements contained in the product collection.</summary>
      <returns>An integer indicating the number of elements contained in the product collection.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductCollection.GetEnumerator">
      <summary>Returns an enumerator that can iterate through the product collection.</summary>
      <returns>An enumerator that can iterate through the product collection.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductCollection.Item(System.Int32)">
      <summary>Retrieves the product at the specified index.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product" /> object that represents the product at the specified index.</returns>
      <param name="index">The zero-based index of the element to get.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.Bootstrapper.ProductCollection.Product(System.String)">
      <summary>Retrieves the product with the specified product code.</summary>
      <returns>The <see cref="T:Microsoft.Build.Tasks.Deployment.Bootstrapper.Product" /> object with the specified product code. Returns null if the specified product code is not found.</returns>
      <param name="productCode">The unique identifier of the product to get.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationIdentity">
      <summary>Provides a unique identifier for a ClickOnce application.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationIdentity.#ctor(System.String,Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity,Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationIdentity" /> class.</summary>
      <param name="url">The deployment provider URL for the ClickOnce deployment manifest.</param>
      <param name="deployManifestIdentity">The assembly identity of the ClickOnce deployment manifest.</param>
      <param name="applicationManifestIdentity">The assembly identity of the ClickOnce application manifest.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationIdentity.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationIdentity" /> class.</summary>
      <param name="url">The deployment provider URL for the ClickOnce deployment manifest.</param>
      <param name="deployManifestPath">The path to ClickOnce deployment manifest. The assembly identity will be obtained from the specified file.</param>
      <param name="applicationManifestPath">The path to ClickOnce application manifest. The assembly identity will be obtained from the specified file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationIdentity.ToString">
      <summary>Returns the full ClickOnce application identity.</summary>
      <returns>A string containing the ClickOnce application identity.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest">
      <summary>Describes a ClickOnce or native Win32 application manifest.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest" /> class. </summary>
      <param name="targetFrameworkVersion">A token that represents a version of the .NET Framework.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.ConfigFile">
      <summary>Gets or sets the application configuration file.</summary>
      <returns>A string that represents the name of the application configuration file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.EntryPoint">
      <summary>Gets or sets an assembly reference that represents the entry point of the application.</summary>
      <returns>An <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference" /> object that represents the entry point of the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.ErrorReportUrl">
      <summary>Gets or sets the URL of the error report.</summary>
      <returns>A string that contains the URL of the error report.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.FileAssociations">
      <summary>Gets or sets the file associations that are defined in the manifest.</summary>
      <returns>A string that contains the file associations.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.HostInBrowser">
      <summary>Gets or sets a value that specifies whether the application is hosted in a browser (as WPF Web Browser Applications are).</summary>
      <returns>A Boolean value that indicates whether the application being deployed will be hosted in a browser (True), or not (False). True applies only to WPF Web Browser Applications.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.IconFile">
      <summary>Gets or sets the program icon file.</summary>
      <returns>A string that represents the name of the icon file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.IsClickOnceManifest">
      <summary>Gets or sets a value that specifies whether the manifest is a ClickOnce application manifest or a native Win32 application manifest.</summary>
      <returns>A Boolean value that indicates whether the manifest is a ClickOnce application manifest (True) or a native Win32 manifest (False).</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.MaxTargetPath">
      <summary>Gets or sets a value that specifies the maximum allowed length of a file path in a ClickOnce application deployment. If this value is specified, the length of each file path in the application is checked against this limit. Any items that exceed the limit cause a warning message. If this input is not specified or is zero, no checking is performed. For a native Win32 manifest, this input is ignored.</summary>
      <returns>An integer that indicates the maximum allowed length of a file path in a ClickOnce application deployment.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.OSDescription">
      <summary>Gets or sets a value that specifies a textual description for the operating system dependency.</summary>
      <returns>A string that indicates the operating system dependency.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.OSSupportUrl">
      <summary>Gets or sets a value that specifies a support URL for the operating system dependency.</summary>
      <returns>A string indicating the support URL for the operating system dependency.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.OSVersion">
      <summary>Gets or sets a value that specifies the minimum required operating system version required by the application.</summary>
      <returns>A string that indicates the minimum required operating system version required by the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.Product">
      <summary>Gets or sets a value that specifies the ProductName property in the project file.</summary>
      <returns>A string value that corresponds to the ProductName property in the project file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.Publisher">
      <summary>Gets or sets a value that specifies the PublisherName property in the project file.</summary>
      <returns>A string value that corresponds to the PublisherName property in the project file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.SuiteName">
      <summary>Gets or sets the suite name that is defined in the manifest.</summary>
      <returns>A string that contains the suite name.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.SupportUrl">
      <summary>Gets or sets a value that specifies the SupportUrl property in the project file.</summary>
      <returns>A string value that corresponds to the SupportUrl property in the project file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.TargetFrameworkVersion">
      <summary>Gets or sets a token that represents a version of the .NET Framework. </summary>
      <returns>A <see cref="T:System.String" /> that represents a version of the .NET Framework.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.TrustInfo">
      <summary>Gets or sets a value that specifies a trust object that defines the application security.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo" /> object that defines the application security.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.UseApplicationTrust">
      <summary>Gets or sets a value that specifies whether the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.Product" />, <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.Publisher" />, and <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.SupportUrl" /> properties are written to the application manifest.</summary>
      <returns>A Boolean value that indicates whether the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.Publisher" />, <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.Product" />, and <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.SupportUrl" /> properties are written to the application manifest. If the value is True, they are written to the application manifest. If the value is False, these properties are ignored.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.Validate">
      <summary>Performs various checks to verify the validity of the manifest. Any resulting errors or warnings are reported in the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessageCollection" /> object.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlConfigFile">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.ConfigFile" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.ConfigFile" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlEntryPointIdentity">
      <summary>Gets or sets a property that is used to serialize the entry point identity to an XML file.</summary>
      <returns>The entry point identity.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlEntryPointParameters">
      <summary>Gets or sets a property that is used to serialize the entry point parameters to an XML file.</summary>
      <returns>The entry point parameters.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlEntryPointPath">
      <summary>Gets or sets a property that specifies the entry point for executing the assembly and that is used to serialize the entry point to an XML file.</summary>
      <returns>The entry point for executing the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlErrorReportUrl">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.ErrorReportUrl" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.ErrorReportUrl" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlFileAssociations">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.FileAssociations" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.FileAssociations" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlHostInBrowser">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.HostInBrowser" /> property to an XML file.</summary>
      <returns>true or false. The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.HostInBrowser" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlIconFile">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.IconFile" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.IconFile" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlIsClickOnceManifest">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.IsClickOnceManifest" /> property to an XML file.</summary>
      <returns>true or false. The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.IsClickOnceManifest" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlOSBuild">
      <summary>Gets or sets a property that is used to serialize the build version of the operating system to an XML file.</summary>
      <returns>The build version of the operating system.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlOSDescription">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.OSDescription" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.OSDescription" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlOSMajor">
      <summary>Gets or sets a property that is used to serialize the major portion of the version number of the operating system to an XML file.</summary>
      <returns>The major portion of the version number of the operating system.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlOSMinor">
      <summary>Gets or sets a property that is used to serialize the minor portion of the version number of the operating system to an XML file.</summary>
      <returns>The minor portion of the version number of the operating system.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlOSRevision">
      <summary>Gets or sets a property that is used to serialize the revision portion of the version number of the operating system to an XML file.</summary>
      <returns>The revision portion of the version number of the operating system.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlOSSupportUrl">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.OSSupportUrl" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.OSSupportUrl" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlProduct">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.Product" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.Product" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlPublisher">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.Publisher" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.Publisher" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlSuiteName">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.SuiteName" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.SuiteName" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlSupportUrl">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.SupportUrl" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.SupportUrl" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.XmlUseApplicationTrust">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.UseApplicationTrust" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest.UseApplicationTrust" /> property.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity">
      <summary>Specifies the identity of an assembly.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.#ctor(Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity" /> class.</summary>
      <param name="identity">Specifies another instance to duplicate.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity" /> class.</summary>
      <param name="name">Specifies the simple name of the assembly.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity" /> class.</summary>
      <param name="name">Specifies the simple name of the assembly.</param>
      <param name="version">Specifies the version of the assembly.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.#ctor(System.String,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity" /> class.</summary>
      <param name="name">Specifies the simple name of the assembly.</param>
      <param name="version">Specifies the version of the assembly.</param>
      <param name="publicKeyToken">Specifies the public key token of the assembly, which is the last 8 bytes of the SHA-1 hash of the public key under which the assembly is signed.</param>
      <param name="culture">Specifies the culture of the assembly. A blank string indicates the invariant culture.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.#ctor(System.String,System.String,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity" /> class.</summary>
      <param name="name">Specifies the simple name of the assembly.</param>
      <param name="version">Specifies the version of the assembly.</param>
      <param name="publicKeyToken">Specifies the public key token of the assembly, which is the last 8 bytes of the SHA-1 hash of the public key under which the assembly is signed.</param>
      <param name="culture">Specifies the culture of the assembly. A blank string indicates the invariant culture.</param>
      <param name="processorArchitecture">Specifies the processor architecture of the assembly. Valid values are msil, x86, ia64, and amd64.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.#ctor(System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity" /> class.</summary>
      <param name="name">Specifies the simple name of the assembly.</param>
      <param name="version">Specifies the version of the assembly.</param>
      <param name="publicKeyToken">Specifies the public key token of the assembly, which is the last 8 bytes of the SHA-1 hash of the public key under which the assembly is signed.</param>
      <param name="culture">Specifies the culture of the assembly. A blank string indicates the invariant culture.</param>
      <param name="processorArchitecture">Specifies the processor architecture of the assembly. Valid values are msil, x86, ia64, and amd64.</param>
      <param name="type">Specifies the type attribute of the assembly. Valid values are win32 or a blank string.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.Culture">
      <summary>Gets or sets the culture of the assembly.</summary>
      <returns>A string indicating the culture of the assembly. A blank string indicates the invariant culture.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.FromAssemblyName(System.String)">
      <summary>Parses string to obtain an assembly identity. Returns null if identity could not be obtained.</summary>
      <returns>The resulting assembly identity.</returns>
      <param name="assemblyName">The full name of the assembly, also known as the display name.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.FromFile(System.String)">
      <summary>Obtains identity of the specified assembly. File can be a PE with an embedded xml manifest, a stand-alone xml manifest file, or a .NET assembly. Returns null if identity could not be obtained.</summary>
      <returns>The assembly identity of the specified file.</returns>
      <param name="path">The name of the file from which the identity is to be obtained.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.FromManagedAssembly(System.String)">
      <summary>Obtains identity of the specified .NET assembly. File must be a .NET assembly. Returns null if identity could not be obtained.</summary>
      <returns>The assembly identity of the specified file.</returns>
      <param name="path">The name of the file from which the identity is to be obtained.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.FromManifest(System.String)">
      <summary>Obtains identity of the specified manifest file. File must be a standalone XML manifest file. Returns null if identity could not be obtained.</summary>
      <returns>The assembly identity of the specified file.</returns>
      <param name="path">The name of the file from which the identity is to be obtained.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.FromNativeAssembly(System.String)">
      <summary>Obtains identity of the specified native assembly. File must be either a PE with an embedded XML manifest, or a stand-alone XML manifest file. Returns NULL if identity could not be obtained.</summary>
      <returns>The assembly identity of the specified file.</returns>
      <param name="path">The name of the file from which the identity is to be obtained.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.GetFullName(Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.FullNameFlags)">
      <summary>Returns the full name of the assembly.</summary>
      <returns>A string representation of the full name.</returns>
      <param name="flags">Specifies which attributes to be included in the full name.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.IsFrameworkAssembly">
      <summary>Returns True if this assembly is part of the .NET Framework.</summary>
      <returns>A Boolean value indicating whether this assembly is part of the .NET Framework. True if this assembly is part of the .NET Framework; otherwise False.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.IsNeutralPlatform">
      <summary>Gets a value indicating whether the assembly identity represents a neutral platform assembly.</summary>
      <returns>A Boolean value indicating whether the assembly identity represents a neutral platform assembly. True if the assembly identity is a neutral platform assembly; otherwise False.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.IsStrongName">
      <summary>Gets a value indicating whether the assembly identity is a strong name.</summary>
      <returns>A Boolean value indicating whether the assembly identity is a strong name. True if the assembly identity is a strong name; otherwise False.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.Name">
      <summary>Gets or sets the simple name of the assembly.</summary>
      <returns>A string indicating the simple name of the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.ProcessorArchitecture">
      <summary>Gets or sets the processor architecture of the assembly.</summary>
      <returns>A string representing the processor architecture of the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.PublicKeyToken">
      <summary>Gets or sets the public key token of the assembly, which is the last 8 bytes of the SHA-1 hash of the public key under which the assembly is signed.</summary>
      <returns>A string indicating the public key token of the assembly.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.ToString">
      <summary>Returns the full ClickOnce assembly identity.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.Type">
      <summary>Gets or sets the type attribute of the assembly.</summary>
      <returns>A string indicating the type attribute of the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.Version">
      <summary>Gets or sets the version of the assembly.</summary>
      <returns>A string indicating the assembly version.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.XmlCulture">
      <summary>Get or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.Culture" /> property to an XML file.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.XmlName">
      <summary>Get or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.Name" /> property to an XML file.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.XmlProcessorArchitecture">
      <summary>Get or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.ProcessorArchitecture" /> property to an XML file.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.XmlPublicKeyToken">
      <summary>Get or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.PublicKeyToken" /> property to an XML file.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.XmlType">
      <summary>Get or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.Type" /> property to an XML file.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.XmlVersion">
      <summary>Get or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.Version" /> property to an XML file.</summary>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.FullNameFlags">
      <summary>This class represents the attributes that the <see cref="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.GetFullName(Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.FullNameFlags)" /> function returns.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.FullNameFlags.Default">
      <summary>Include the Name, Version, Culture, and PublicKeyToken attributes.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.FullNameFlags.ProcessorArchitecture">
      <summary>Include the Name, Version, Culture, PublicKeyToken, and ProcessorArchitecture attributes.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.FullNameFlags.Type">
      <summary>Include the Name, Version, Culture, PublicKeyToken, and Type attributes.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity.FullNameFlags.All">
      <summary>Include all attributes.</summary>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyManifest">
      <summary>Describes a Win32 assembly manifest.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyManifest.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyManifest" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyManifest.ExternalProxyStubs">
      <summary>Gets the set of external proxy stubs referenced by the manifest for isolated applications and Reg-Free COM.</summary>
      <returns>Returns the set of external proxy stubs referenced by the manifest for isolated applications and Reg-Free COM.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyManifest.XmlExternalProxyStubs">
      <summary>Gets or sets the set of external proxy stubs referenced by the manifest for isolated applications and Reg-Free COM in XML format.</summary>
      <returns>Returns the set of external proxy stubs referenced by the manifest for isolated applications and Reg-Free COM in XML format.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference">
      <summary>Describes a manifest assembly reference.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference" /> class.</summary>
      <param name="path">The specified source path of the file.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference.AssemblyIdentity">
      <summary>Specifies the identity of the assembly reference.</summary>
      <returns>An <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity" /> object representing the identity of the assembly reference.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference.IsPrerequisite">
      <summary>Specifies whether the assembly reference is a prerequisite or not.</summary>
      <returns>A Boolean value indicating whether the assembly reference is a prerequisite (True) or not (False).</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference.ReferenceType">
      <summary>Specifies the type of the assembly reference.</summary>
      <returns>An <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceType" /> object representing the type of the assembly reference.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference.ToString">
      <summary>Returns the full ClickOnce assembly reference.</summary>
      <returns>A string containing the ClickOnce assembly reference.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference.XmlAssemblyIdentity">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference.AssemblyIdentity" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference.AssemblyIdentity" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference.XmlIsNative">
      <summary>Gets or sets a value indicating whether the reference is native.</summary>
      <returns>A value indicating whether the reference is native.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference.XmlIsPrerequisite">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference.IsPrerequisite" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference.IsPrerequisite" /> property.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceCollection">
      <summary>Provides a collection for manifest assembly references.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceCollection.Add(Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference)">
      <summary>Adds the specified assembly reference to the collection.</summary>
      <returns>Returns the added assembly reference instance.</returns>
      <param name="assembly">The assembly reference to add.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceCollection.Add(System.String)">
      <summary>Adds the specified assembly reference to the collection.</summary>
      <returns>Returns the added assembly reference instance.</returns>
      <param name="path">The path of the assembly to add.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceCollection.Clear">
      <summary>Removes all objects from the collection.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceCollection.Count">
      <summary>Gets the number of objects contained in the collection.</summary>
      <returns>An integer indicating the number of objects contained in the collection.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceCollection.Find(Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity)">
      <summary>Finds an assembly reference in the collection by the specified assembly identity.</summary>
      <returns>Returns the found assembly reference.</returns>
      <param name="identity">The specified assembly identity.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceCollection.Find(System.String)">
      <summary>Finds an assembly reference in the collection by simple name.</summary>
      <returns>Returns the found assembly reference.</returns>
      <param name="name">The specified assembly simple name.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceCollection.FindTargetPath(System.String)">
      <summary>Finds an assembly reference in the collection by the specified target path.</summary>
      <returns>Returns the found assembly reference.</returns>
      <param name="targetPath">The specified target path.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceCollection.GetEnumerator">
      <summary>Returns an enumerator that can iterate through the collection.</summary>
      <returns>Returns an enumerator (<see cref="T:System.Collections.IEnumerator" />).</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceCollection.Item(System.Int32)">
      <summary>Gets the element at the specified index.</summary>
      <returns>Returns the assembly reference instance.</returns>
      <param name="index">The zero-based index of the entry to get.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceCollection.Remove(Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference)">
      <summary>Removes the specified assembly reference from the collection.</summary>
      <param name="assemblyReference">The specified assembly reference to remove.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceType">
      <summary>This class represents the type of an assembly reference.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceType.Unspecified">
      <summary>Assembly type is unspecified and will be determined by the <see cref="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.UpdateFileInfo" /> method.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceType.ClickOnceManifest">
      <summary>Specifies a ClickOnce manifest.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceType.ManagedAssembly">
      <summary>Specifies a .NET Framework assembly.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceType.NativeAssembly">
      <summary>Specifies a Win32 native assembly.</summary>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference">
      <summary>Describes base functionality common to both file references and assembly references.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference" /> class.</summary>
      <param name="path">The path for the assembly base reference.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.Group">
      <summary>Gets or sets the group for on-demand download functionality.</summary>
      <returns>A string indicating the group for on-demand download functionality. A blank string indicates a primary file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.Hash">
      <summary>Gets or sets the SHA1 hash of the file.</summary>
      <returns>A string indicating the SHA1 hash of the file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.IsOptional">
      <summary>Gets or sets whether the file is optional for on-demand download functionality.</summary>
      <returns>A Boolean value indicating whether the file is optional for on-demand download functionality.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.ResolvedPath">
      <summary>Gets or sets the resolved path to the file. This path is determined by the Resolve method, and is used to compute the file information by the <see cref="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.UpdateFileInfo" /> method.</summary>
      <returns>A string indicating the resolved path to the file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.Size">
      <summary>Gets or sets the file size in bytes.</summary>
      <returns>The file size in bytes.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.SortName">
      <summary>Gets a value that is used to sort the referenced assemblies in the application manifest. </summary>
      <returns>The name to sort by.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.SourcePath">
      <summary>Gets or sets the source path of the file.</summary>
      <returns>A string indicating the source path of the file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.TargetPath">
      <summary>Gets or sets the target path of the file. This is the path that is used for specification in the generated manifest.</summary>
      <returns>A string indicating the target path of the file.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.ToString">
      <summary>Returns the base reference name.</summary>
      <returns>A string indicating the base reference name.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.XmlGroup">
      <summary>Gets or sets the group for on-demand download functionality.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.XmlHash">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.Hash" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.Hash" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.XmlHashAlgorithm">
      <summary>Gets or sets a property that is used to serialize the hash algorithm to an XML file.</summary>
      <returns>The hash algorithm.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.XmlIsOptional">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.IsOptional" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.IsOptional" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.XmlPath">
      <summary>Gets or sets a property that is used to serialize the path to an XML file.</summary>
      <returns>The path of the reference.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.XmlSize">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.Size" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.BaseReference.Size" /> property.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass">
      <summary>Represents a COM class that is described in a manifest.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass" /> class. </summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.ClsId">
      <summary>Gets the class identifier (ClsId) for a <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass" /> object.</summary>
      <returns>A <see cref="T:System.String" /> formatted in the pattern {xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx}, where the value of the GUID is represented as a series of lower-case hexadecimal digits in groups of 8, 4, 4, 4, and 12 digits and separated by hyphens. An example of a return value is "{382c74c3-721d-4f34-80e5-57657b6cbc27}".</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.Description">
      <summary>Gets the description of a <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass" /> object.</summary>
      <returns>A description of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass" /> object.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.ProgId">
      <summary>Gets the programmatic identifier (ProgId) of a <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass" /> object.</summary>
      <returns>The programmatic identifier of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass" /> object.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.ThreadingModel">
      <summary>Gets the threading model of a <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass" /> object.</summary>
      <returns>A string such as "Apartment".</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.TlbId">
      <summary>Gets a value that is the identifier of the type library that contains type information about the class.</summary>
      <returns>The identifier of the type library that contains type information about the class. A <see cref="T:System.String" /> formatted in the pattern {xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx}, where the value of the GUID is represented as a series of lower-case hexadecimal digits in groups of 8, 4, 4, 4, and 12 digits and separated by hyphens. An example of a return value is "{382c74c3-721d-4f34-80e5-57657b6cbc27}".</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.XmlClsId">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.ClsId" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.ClsId" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.XmlDescription">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.Description" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.Description" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.XmlProgId">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.ProgId" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.ProgId" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.XmlThreadingModel">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.ThreadingModel" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.ThreadingModel" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.XmlTlbId">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.TlbId" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass.TlbId" /> property.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFramework">
      <summary>Describes the versions of the .NET Framework that an application can run on.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFramework.#ctor">
      <summary>Initializes a new instance of the <see cref="Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFramework" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFramework.Profile">
      <summary>Represents the profile of the corresponding .NET Framework.</summary>
      <returns>A <see cref="T:System.String" /> object that represents the profile of the.NET Framework.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFramework.SupportedRuntime">
      <summary>Gets or sets the supported runtime for the framework.</summary>
      <returns>A <see cref="T:System.String" /> object that represents a runtime version of the corresponding .NET Framework.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFramework.Version">
      <summary>Gets or sets the version of the framework.</summary>
      <returns>A <see cref="T:System.String" /> object that represents the version of a .NET Framework.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFramework.XmlProfile">
      <summary>Gets or sets the XML version for the framework.</summary>
      <returns>An array of XML elements that represents the profile of the.NET Framework.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFramework.XmlSupportedRuntime">
      <summary>Gets or sets the XML supported runtime for the framework.</summary>
      <returns>An array of XML elements that represents the runtime version of the corresponding .NET Framework.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFramework.XmlVersion">
      <summary>Gets or sets the version of the framework.</summary>
      <returns>An array of XML elements that represents the version of a .NET Framework.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFrameworkCollection">
      <summary>Represents a collection of versions of the .NET Framework that an application prefers to run on.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFrameworkCollection.Add(Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFramework)">
      <summary>Adds a framework to the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFramework" /> class.</summary>
      <param name="compatibleFramework">The framework to add.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFrameworkCollection.Clear">
      <summary>Clears the collection.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFrameworkCollection.Count">
      <summary>Gets the number of .NET Framework versions listed in the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFrameworkCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Int32" /> object that represents the number of .NET Framework versions listed in the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFrameworkCollection" /> collection.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFrameworkCollection.GetEnumerator">
      <summary>Gets the enumerator for the collection.</summary>
      <returns>A <see cref="T:System.Collections.IEnumerator" /> interface that can iterate over the collection. </returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFrameworkCollection.Item(System.Int32)">
      <summary>Gets a version of the .NET Framework that an application prefers to run on.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFramework" /> object that represents the versions of the .NET Framework that an application prefers to run on.</returns>
      <param name="index">The version of .NET Framework to check for on the end-user computer.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest">
      <summary>Represents a ClickOnce deployment manifest.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.#ctor(System.String)">
      <summary>Initializes a new instance of <see cref="Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest" />.</summary>
      <param name="targetFrameworkMoniker">A token that represents a version of the .NET Framework runtime.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.CompatibleFrameworks">
      <summary>Gets the versions of the .NET Framework that an application can run on. </summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFrameworkCollection" /> class that represents the versions of the .NET Framework that an application can run on.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.CreateDesktopShortcut">
      <summary>Gets or sets a value that indicates whether to create a desktop shortcut.</summary>
      <returns>"true" or "false". A string that contains a value that indicates whether to create a desktop shortcut.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.DeploymentUrl">
      <summary>Gets or sets the update location for the application.</summary>
      <returns>A string that indicates the update location for the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.DisallowUrlActivation">
      <summary>Gets or sets a value indicating whether the application should be blocked from being started by means of a URL.</summary>
      <returns>true if the application is blocked from being started by means of a URL otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.EntryPoint">
      <summary>Gets or sets a deployment reference that is the entry point of the application.</summary>
      <returns>An <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference" /> object that represents the entry point of the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.ErrorReportUrl">
      <summary>Gets or sets the URL of the error report.</summary>
      <returns>A string that contains the URL of the error report.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.Install">
      <summary>Gets or sets a value indicating whether the application is an installed application or an online-only application.</summary>
      <returns>true if the application is an installed application; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.MapFileExtensions">
      <summary>Gets or sets a value indicating whether .deploy file extension mapping is used.</summary>
      <returns>true if .deploy file extension mapping is used; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.MinimumRequiredVersion">
      <summary>Gets or sets the minimum required version number for an application.</summary>
      <returns>A string indicating the minimum required version number for an application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.Product">
      <summary>Gets or sets the name of the application.</summary>
      <returns>A string that indicates the name of the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.Publisher">
      <summary>Gets or sets the publisher of the application.</summary>
      <returns>A string that indicates the publisher of the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.SuiteName">
      <summary>Gets or sets the suite name that is defined in the manifest.</summary>
      <returns>A string that contains the suite name.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.SupportUrl">
      <summary>Gets or sets the link that appears in Add or Remove Programs in Control Panel for the application.</summary>
      <returns>A string that indicates the support URL.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.TargetFrameworkMoniker">
      <summary>Gets or sets a token that represents a version of the .NET Framework runtime. </summary>
      <returns>A <see cref="T:System.String" /> that represents a version of the .NET Framework runtime.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.TrustUrlParameters">
      <summary>Gets or sets a value indicating whether URL query-string parameters are available to the application.</summary>
      <returns>true if URL query-string parameters are available to the application; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.UpdateEnabled">
      <summary>Gets or sets a value indicating whether the application is updatable.</summary>
      <returns>true if the application is updatable; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.UpdateInterval">
      <summary>Gets or sets the update interval for the application.</summary>
      <returns>An integer that indicates the update interval for the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.UpdateMode">
      <summary>Gets or sets an object indicating whether updates should be checked in the foreground before the application is started, or in the background while the application is running.</summary>
      <returns>An <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.UpdateMode" /> object that indicates whether updates should be checked before the application is started, or while the application is running.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.UpdateUnit">
      <summary>Gets or sets the units used for the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.UpdateInterval" /> property.</summary>
      <returns>An <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.UpdateUnit" /> object that indicates the units used for the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.UpdateInterval" /> property.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.Validate">
      <summary>Performs various checks to verify the validity of the manifest. Any resulting errors or warnings are reported in the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessageCollection" /> object.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlCompatibleFrameworks">
      <summary>Gets or sets the array of XML elements that represent the versions of the .NET Framework that an application can run on.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.CompatibleFramework" /> class that represents the versions of the .NET Framework than an application can run on.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlCreateDesktopShortcut">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.CreateDesktopShortcut" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.CreateDesktopShortcut" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlDeploymentUrl">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.DeploymentUrl" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.DeploymentUrl" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlDisallowUrlActivation">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.DisallowUrlActivation" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.DisallowUrlActivation" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlErrorReportUrl">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.ErrorReportUrl" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.ErrorReportUrl" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlInstall">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.Install" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.Install" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlMapFileExtensions">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.MapFileExtensions" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.MapFileExtensions" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlMinimumRequiredVersion">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.MinimumRequiredVersion" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.MinimumRequiredVersion" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlProduct">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.Product" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.Product" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlPublisher">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.Publisher" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.Publisher" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlSuiteName">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.SuiteName" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.SuiteName" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlSupportUrl">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.SupportUrl" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.SupportUrl" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlTrustUrlParameters">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.TrustUrlParameters" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.TrustUrlParameters" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlUpdateEnabled">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.UpdateEnabled" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.UpdateEnabled" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlUpdateInterval">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.UpdateInterval" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.UpdateInterval" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlUpdateMode">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.UpdateMode" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.UpdateMode" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.XmlUpdateUnit">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.UpdateUnit" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest.UpdateUnit" /> property.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation">
      <summary>Represents a file name extension to be associated with the application.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.DefaultIcon">
      <summary>Gets or sets the icon to use for files that use a particular <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.Extension" />.</summary>
      <returns>The name of the file that contains the icon.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.Description">
      <summary>Gets or sets the description of the file type for use by the shell.</summary>
      <returns>A description of the file type.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.Extension">
      <summary>Gets or sets the file name extension to be associated with the application.</summary>
      <returns>The file name extension to be associated with the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.ProgId">
      <summary>Gets or sets the name that uniquely identifies the file association's file type.</summary>
      <returns>The name that uniquely identifies the file type.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.XmlDefaultIcon">
      <summary>Gets or sets a property that is used to serialize the file association's <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.DefaultIcon" /> property to an XML file.</summary>
      <returns>An XML representation of this file association's DefaultIcon.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.XmlDescription">
      <summary>Gets or sets a property that is used to serialize the file association's <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.Description" /> property to an XML file.</summary>
      <returns>An XML representation of this file association's <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.Description" /> value.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.XmlExtension">
      <summary>Gets or sets a property that is used to serialize the file association's <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.Extension" /> property to an XML file.</summary>
      <returns>An XML representation of this file association's <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.Extension" /> value.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.XmlProgId">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.ProgId" /> property to an XML file.</summary>
      <returns>An XML representation of this file association's <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation.ProgId" /> value.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociationCollection">
      <summary>Represents a collection of file associations.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociationCollection.Add(Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociation)">
      <summary>Adds the specified file association to the collection.</summary>
      <param name="fileAssociation">The file association to add.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociationCollection.Clear">
      <summary>Removes all file associations from the collection.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociationCollection.Count">
      <summary>Gets the number of file associations in the collection.</summary>
      <returns>The number of file associations in the collection.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociationCollection.GetEnumerator">
      <summary>Returns an enumerator that can iterate through the collection.</summary>
      <returns>An interface that represents an enumerator that can iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileAssociationCollection.Item(System.Int32)">
      <summary>Gets the file association that appears at the specified index.</summary>
      <returns>The file association at the specified index.</returns>
      <param name="index">The zero-based index of the file association to get.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference">
      <summary>Describes a manifest file reference.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference" /> class.</summary>
      <param name="path">The specified source path of the file.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.ComClasses">
      <summary>Returns the set of COM classes referenced by the manifest for isolated applications and registration-free COM.</summary>
      <returns>An array of <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ComClass" /> objects representing the set of COM classes referenced by the manifest for isolated applications and registration-free COM.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.IsDataFile">
      <summary>Gets or sets whether the file is a data file.</summary>
      <returns>A Boolean value indicating whether the file is a data file.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.ProxyStubs">
      <summary>Returns the set of proxy stubs referenced by the manifest for isolated applications and registration-free COM.</summary>
      <returns>An array of <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub" /> objects representing the set of proxy stubs referenced by the manifest for isolated applications and registration-free COM.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.TypeLibs">
      <summary>Returns the set of type libraries referenced by the manifest.</summary>
      <returns>An array of <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib" /> objects representing the set of type libraries referenced by the manifest.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.XmlComClasses">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.ComClasses" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.ComClasses" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.XmlProxyStubs">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.ProxyStubs" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.ProxyStubs" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.XmlTypeLibs">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.TypeLibs" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.TypeLibs" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference.XmlWriteableType">
      <summary>Gets or sets a value that indicates whether the file is a data file.</summary>
      <returns>"applicationData" is the only valid setting.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReferenceCollection">
      <summary>Represents a collection for manifest file references.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReferenceCollection.Add(Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference)">
      <summary>Adds the specified file reference to the collection.</summary>
      <returns>Returns the added file reference object.</returns>
      <param name="file">The specified file reference to add.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReferenceCollection.Add(System.String)">
      <summary>Adds the specified file reference to the collection.</summary>
      <returns>Returns the added file reference object.</returns>
      <param name="path">The path of the specified file reference to add.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReferenceCollection.Clear">
      <summary>Removes all objects from the collection.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReferenceCollection.Count">
      <summary>Returns the number of objects contained in the collection.</summary>
      <returns>the number of objects contained in the collection.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReferenceCollection.FindTargetPath(System.String)">
      <summary>Finds a file reference in the collection by the specified target path.</summary>
      <returns>Returns the found file reference.</returns>
      <param name="targetPath">The specified target path.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReferenceCollection.GetEnumerator">
      <summary>Returns an enumerator that can iterate through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> interface representing an enumerator that can iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReferenceCollection.Item(System.Int32)">
      <summary>Returns the element at the specified index.</summary>
      <returns>Returns the file reference object.</returns>
      <param name="index">The zero-based index of the entry to get.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReferenceCollection.Remove(Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReference)">
      <summary>Removes the specified file reference from the collection.</summary>
      <param name="file">The specified file reference to remove.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest">
      <summary>Represents base functionality common to all supported manifest types.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.AssemblyIdentity">
      <summary>Gets or sets the identity of the manifest.</summary>
      <returns>An <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyIdentity" /> object representing the identity of the manifest.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.AssemblyReferences">
      <summary>Gets the set of assemblies that the manifest references.</summary>
      <returns>An <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReferenceCollection" /> object representing the set of assemblies that the manifest references.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.Description">
      <summary>Gets or sets a textual description for the manifest.</summary>
      <returns>A string describing the manifest.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.EntryPoint">
      <summary>Gets or sets an assembly reference that is the entry point of the application.</summary>
      <returns>An <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyReference" /> object representing an assembly reference that is the entry point of the application.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.FileReferences">
      <summary>Gets the set of files that the manifest references.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.FileReferenceCollection" /> object representing the set of files that the manifest references.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.InputStream">
      <summary>Gets or sets the input stream from which the manifest was read.</summary>
      <returns>A <see cref="T:System.IO.Stream" /> object representing the input stream from which the manifest was read.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.OutputMessages">
      <summary>Gets a collection of current error and warning messages.</summary>
      <returns>An <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessageCollection" /> object, which contains a collection of current error and warning messages.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.ReadOnly">
      <summary>Gets or sets a value indicating whether the manifest is operating in read-only or read-write mode.</summary>
      <returns>A Boolean value indicating whether the manifest is operating in read-only or read-write mode.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.ResolveFiles">
      <summary>Locates all specified assembly and file references by searching in the same directory as the loaded manifest, or in the current directory.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.ResolveFiles(System.String[])">
      <summary>Locates all specified assembly and file references by searching in the specified directories.</summary>
      <param name="searchPaths">An array of strings specifying the directories to search.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.SourcePath">
      <summary>Gets or sets the location where the manifest was loaded or saved.</summary>
      <returns>A string indicating the location where the manifest was loaded or saved.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.ToString">
      <summary>Returns the manifest name.</summary>
      <returns>A string indicating the name of the manifest.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.UpdateFileInfo">
      <summary>Updates file information for each referenced assembly and file.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.UpdateFileInfo(System.String)">
      <summary>Updates file information for each referenced assembly and file, using the specified version of the target framework.</summary>
      <param name="targetFrameworkVersion">The version of the target framework to use. Null if no target framework version. If no target framework version, uses sha256 algorithm.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.Validate">
      <summary>Performs various checks to verify the validity of the manifest.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.ValidatePlatform">
      <summary>Validates the manifest platform.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.XmlAssemblyIdentity">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.AssemblyIdentity" /> property to an XML file.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.XmlAssemblyReferences">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.AssemblyReferences" /> property to an XML file.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.XmlDescription">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.Description" /> property to an XML file.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.XmlFileReferences">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.FileReferences" /> property to an XML file.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.XmlSchema">
      <summary>Gets or sets a property that is used to serialize the schema to an XML file.</summary>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ManifestReader">
      <summary>Reads an XML manifest file into an object representation.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ManifestReader.ReadManifest(System.IO.Stream,System.Boolean)">
      <summary>Reads the specified manifest XML and returns an object representation.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest" /> object representation of the manifest. Can be cast to <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyManifest" />, <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest" />, or <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest" /> to access more specific functionality.</returns>
      <param name="input">Specifies an input stream.</param>
      <param name="preserveStream">Specifies whether to preserve the input stream in the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.InputStream" /> property of the resulting manifest object. Used by <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ManifestWriter" /> to reconstitute input which is not represented in the object representation.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ManifestReader.ReadManifest(System.String,System.Boolean)">
      <summary>Reads the specified manifest XML and returns an object representation.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest" /> object representation of the manifest. Can be cast to <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyManifest" />, <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest" />, or <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest" /> to access more specific functionality.</returns>
      <param name="path">The fully qualified path name of the input file. For example, the full path may be c:\directory\folder\filename.manifest.</param>
      <param name="preserveStream">Specifies whether to preserve the input stream in the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.InputStream" /> property of the resulting manifest object. Used by <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ManifestWriter" /> to reconstitute input which is not represented in the object representation.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ManifestReader.ReadManifest(System.String,System.IO.Stream,System.Boolean)">
      <summary>Reads the specified manifest XML and returns an object representation.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest" /> object representation of the manifest. Can be cast to <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyManifest" />, <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest" />, or <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest" /> to access more specific functionality.</returns>
      <param name="manifestType">Specifies the expected type of the manifest. Valid values are AssemblyManifest, ApplicationManifest, or DeployManifest.</param>
      <param name="input">Specifies an input stream.</param>
      <param name="preserveStream">Specifies whether to preserve the input stream in the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.InputStream" /> property of the resulting manifest object. Used by <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ManifestWriter" /> to reconstitute input which is not represented in the object representation.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ManifestReader.ReadManifest(System.String,System.String,System.Boolean)">
      <summary>Reads the specified manifest XML and returns an object representation.</summary>
      <returns>A <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest" /> object representation of the manifest. Can be cast to <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.AssemblyManifest" />, <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ApplicationManifest" />, or <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.DeployManifest" /> to access more specific functionality.</returns>
      <param name="manifestType">Specifies the expected type of the manifest. Valid values are AssemblyManifest, ApplicationManifest, or DeployManifest.</param>
      <param name="path">The path name of the input file.</param>
      <param name="preserveStream">Specifies whether to preserve the input stream in the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest.InputStream" /> property of the resulting manifest object. Used by <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ManifestWriter" /> to reconstitute input which is not represented in the object representation.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ManifestWriter">
      <summary>Writes an object representation of a manifest to XML.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ManifestWriter.WriteManifest(Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest)">
      <summary>Writes the specified manifest object to XML.</summary>
      <param name="manifest">The manifest object.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ManifestWriter.WriteManifest(Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest,System.IO.Stream)">
      <summary>Writes the specified manifest object to XML.</summary>
      <param name="manifest">The manifest object.</param>
      <param name="output">Specifies an output stream.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ManifestWriter.WriteManifest(Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest,System.String)">
      <summary>Writes the specified manifest object to XML.</summary>
      <param name="manifest">The manifest object.</param>
      <param name="path">The path name of the output file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ManifestWriter.WriteManifest(Microsoft.Build.Tasks.Deployment.ManifestUtilities.Manifest,System.String,System.String)">
      <summary>Writes the specified manifest object to XML.</summary>
      <param name="manifest">The manifest object.</param>
      <param name="path">The path name of the output file.</param>
      <param name="targetframeWorkVersion">The target framework version.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessage">
      <summary>This class represents an error, warning, or informational output message for the manifest generator.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessage.GetArguments">
      <summary>Returns a string array of arguments for the message.</summary>
      <returns>Returns a string array of arguments for the message.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessage.Name">
      <summary>Returns an identifier for the message.</summary>
      <returns>A string indicating the message name.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessage.Text">
      <summary>Returns the text of the message.</summary>
      <returns>A string indicating the message text.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessage.Type">
      <summary>Returns whether the message is an error, warning, or informational message.</summary>
      <returns>An <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessageType" /> object indicating the message type.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessageCollection">
      <summary>This class represent a collection for output messages.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessageCollection.Clear">
      <summary>Removes all objects from the collection.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessageCollection.ErrorCount">
      <summary>Gets the number of error messages in the collection.</summary>
      <returns>The number of error messages in the collection.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessageCollection.GetEnumerator">
      <summary>Returns an enumerator that can iterate through the collection.</summary>
      <returns>Returns an <see cref="T:System.Collections.IEnumerator" /> object that can iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessageCollection.Item(System.Int32)">
      <summary>Gets the element at the specified index.</summary>
      <returns>Returns the file reference instance.</returns>
      <param name="index">The zero-based index of the entry to get.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessageCollection.WarningCount">
      <summary>Gets the number of warning messages in the collection.</summary>
      <returns>The number of warning messages in the collection.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessageType">
      <summary>Specifies the type of output message as either an error, warning, or informational.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessageType.Info">
      <summary>Indicates an informational message.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessageType.Warning">
      <summary>Indicates a warning.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.OutputMessageType.Error">
      <summary>Indicates an error.</summary>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub">
      <summary>Represents a proxy in a ClickOnce application manifest. .</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub" /> class. </summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.BaseInterface">
      <summary>Gets the IID of the interface from which the interface described by the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.IID" /> property is derived.</summary>
      <returns>The IID of the base interface.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.IID">
      <summary>Gets the IID of the interface for which the proxy is being declared.</summary>
      <returns>The IID of the interface.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.Name">
      <summary>Gets the name of the component.</summary>
      <returns>The name of the component.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.NumMethods">
      <summary>Gets the number of methods in the component.</summary>
      <returns>The number of methods in the component.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.TlbId">
      <summary>Gets the GUID for the type library for the component.</summary>
      <returns>The GUID for the type library.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.XmlBaseInterface">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.BaseInterface" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.BaseInterface" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.XmlIID">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.IID" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.IID" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.XmlName">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.Name" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.Name" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.XmlNumMethods">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.NumMethods" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.NumMethods" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.XmlTlbId">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.TlbId" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.ProxyStub.TlbId" /> property.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.SecurityUtilities">
      <summary>Provides a set of utility functions for manipulating security permision sets and signing.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.SecurityUtilities.ComputeZonePermissionSet(System.String,System.Security.PermissionSet,System.String[])">
      <summary>Generates a permission set by computing the zone default permission set, removing any excluded permissions, and adding any included permissions.</summary>
      <returns>Returns the generated permission set.</returns>
      <param name="targetZone">Specifies a zone default permission set, which is obtained from machine policy. Valid values are Internet, LocalIntranet, or Custom. If Custom is specified, the generated permission set is based only on the <paramref name="includedPermissionSet" /> parameter.</param>
      <param name="includedPermissionSet">A <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.PermissionSet" /> object containing the set of permissions to be explicitly included in the generated permission set. Permissions specified in this parameter will be included verbatim in the generated permission set, regardless of <paramref name="targetZone" /> or <paramref name="excludedPermissions" /> parameters.</param>
      <param name="excludedPermissions">An array of permission identity strings to be excluded from the zone default permission set. Permission identity strings can be computed using the <see cref="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.SecurityUtilities.PermissionSetToIdentityList(System.Security.PermissionSet)" /> method. This parameter is ignored if <paramref name="targetZone" /> is not Internet or LocalIntranet.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.SecurityUtilities.IdentityListToPermissionSet(System.String[])">
      <summary>Converts an array of permission identity strings to a permission set object.</summary>
      <returns>Returns the converted permission set.</returns>
      <param name="ids">An array of permission identity strings.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.SecurityUtilities.PermissionSetToIdentityList(System.Security.PermissionSet)">
      <summary>Converts a permission set object to an array of permission identity strings.</summary>
      <returns>Returns an array of permission identity strings.</returns>
      <param name="permissionSet">The input permission set to be converted.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.SecurityUtilities.SignFile(System.Security.Cryptography.X509Certificates.X509Certificate2,System.Uri,System.String)">
      <summary>Signs a ClickOnce manifest or PE file.</summary>
      <param name="cert">The certificate to be used to sign the file.</param>
      <param name="timestampUrl">Indicates that the file is to be timestamped by the timestamp server at the specified HTTP address. This parameter can be NULL if you do not want a timestamp.</param>
      <param name="path">The name of the file to sign.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.SecurityUtilities.SignFile(System.String,System.Security.SecureString,System.Uri,System.String)">
      <summary>Signs a ClickOnce manifest.</summary>
      <param name="certPath">The certificate to be used to sign the file. The certificate must contain a private key. The KeyUsage extension is optional, but if it is present, DigitalSignature must be set to True. The EnhancedKeyUsage extension is also optional, but if it is present, the certificate must contain object identifier (OID) support.</param>
      <param name="certPassword">The certificate password.</param>
      <param name="timestampUrl">Indicates that the file is to be timestamped by the timestamp server at the specified HTTP address. This parameter can be NULL if you do not want a timestamp.</param>
      <param name="path">The name of the file to sign.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.SecurityUtilities.SignFile(System.String,System.Uri,System.String)">
      <summary>Signs a ClickOnce manifest or PE file.</summary>
      <param name="certThumbprint">Specifies the thumbprint, which is the SHA1 hash of the signing certificate, which is kept in your personal certificate store.</param>
      <param name="timestampUrl">Indicates that the file is to be timestamped by the timestamp server at the specified HTTP address. This parameter can be NULL if you do not want a timestamp.</param>
      <param name="path">The name of the file to sign.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.SecurityUtilities.XmlToPermissionSet(System.Xml.XmlElement)">
      <summary>Converts an XML element to a permission set object.</summary>
      <returns>The converted permission set object.</returns>
      <param name="element">An XML representation of the permission set.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo">
      <summary>This class represents the application security trust information.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.Clear">
      <summary>Resets the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo" /> object to its default state of full trust.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.HasUnmanagedCodePermission">
      <summary>Determines whether the application has permission to call unmanaged code.</summary>
      <returns>A Boolean value indicating whether the application has permission to call unmanaged code.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.IsFullTrust">
      <summary>Determines whether the application is full trust or partial trust.</summary>
      <returns>A Boolean value indicating whether the application is full trust or partial trust.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.PermissionSet">
      <summary>Gets or sets the permission set object for the application trust.</summary>
      <returns>A <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.PermissionSet" /> object for the application trust.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.PreserveFullTrustPermissionSet">
      <summary>Gets or sets whether to preserve partial trust permission when the full trust flag is set.</summary>
      <returns>A Boolean value indicating whether to preserve partial trust permission when the full trust flag is set.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.Read(System.IO.Stream)">
      <summary>Reads the application trust from an XML file.</summary>
      <param name="input">Specifies an input stream.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.Read(System.String)">
      <summary>Reads the application trust from an XML file.</summary>
      <param name="path">The name of the input file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.ReadManifest(System.IO.Stream)">
      <summary>Reads the application trust from a ClickOnce application manifest.</summary>
      <param name="input">Specifies an input stream.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.ReadManifest(System.String)">
      <summary>Reads the application trust from a ClickOnce application manifest.</summary>
      <param name="path">The name of the input file.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.SameSiteAccess">
      <summary>Gets or sets the level of same site access permitted, specifying whether the application has permission to communicate with the server from which it was deployed.</summary>
      <returns>A string indicating the level of same site access permitted. The values can be site or none.site specifies that the application can call Web services at the same URL from which it was deployed; if deployed from a share, the application has permission to read files from that share.none specifies that the application can neither access Web services from the same URL from which it was deployed, nor access files from the share from which it was deployed."Application" refers to the application for which the manifest is being read (using <see cref="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.ReadManifest(System.IO.Stream)" />).</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.ToString">
      <summary>Returns all the information in the application security trust object as a string.</summary>
      <returns>A string containing the information in the application security trust object.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.Write(System.IO.Stream)">
      <summary>Writes the application trust to an XML file.</summary>
      <param name="output">Specifies an output stream.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.Write(System.String)">
      <summary>Writes the application trust to an XML file.</summary>
      <param name="path">The name of the output file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.WriteManifest(System.IO.Stream)">
      <summary>Writes the application trust to a new template ClickOnce application manifest.</summary>
      <param name="output">Specifies an output stream to which to write the updated manifest file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.WriteManifest(System.IO.Stream,System.IO.Stream)">
      <summary>Updates an existing ClickOnce application manifest with the specified trust.</summary>
      <param name="input">Specifies an input stream containing the manifest to be updated.</param>
      <param name="output">Specifies an output stream to which to write the updated manifest file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TrustInfo.WriteManifest(System.String)">
      <summary>Writes the application trust to a ClickOnce application manifest.</summary>
      <param name="path">The name of the output file.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib">
      <summary>Represents a type library.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib" /> class. </summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.Flags">
      <summary>Gets the string representation of the type library flags for this type library.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.HelpDirectory">
      <summary>Gets the directory where the Help file for the types in the type library is located.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.ResourceId">
      <summary>Gets the hexadecimal string representation of the locale identifier (LCID).</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.TlbId">
      <summary>Gets the unique ID of the type library.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.Version">
      <summary>Gets the version number of the type library.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.XmlFlags">
      <summary>Get or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.Flags" /> property to an XML file.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.XmlHelpDirectory">
      <summary>Get or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.HelpDirectory" /> property to an XML file.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.XmlResourceId">
      <summary>Get or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.ResourceId" /> property to an XML file.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.XmlTlbId">
      <summary>Get or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.TlbId" /> property to an XML file.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.XmlVersion">
      <summary>Get or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.TypeLib.Version" /> property to an XML file.</summary>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.UpdateMode">
      <summary>Specifies how the application checks for updates.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.UpdateMode.Background">
      <summary>Check for updates in the background, after the application starts.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.UpdateMode.Foreground">
      <summary>Check for updates in the foreground, before the application starts.</summary>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.UpdateUnit">
      <summary>Specifies the units for the update interval.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.UpdateUnit.Hours">
      <summary>Update interval is in hours.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.UpdateUnit.Days">
      <summary>Update interval is in days.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Deployment.ManifestUtilities.UpdateUnit.Weeks">
      <summary>Update interval is in weeks.</summary>
    </member>
    <member name="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.WindowClass">
      <summary>Represents a window class in an application manifest.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.WindowClass.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.WindowClass" /> class. </summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Deployment.ManifestUtilities.WindowClass.#ctor(System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Deployment.ManifestUtilities.WindowClass" /> class, specifying information about the versioning status of the window class. </summary>
      <param name="name">The name of the class.</param>
      <param name="versioned">Controls whether the internal window class name that is used in registration contains the version of the assembly that contains the window class.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.WindowClass.Name">
      <summary>Gets the name of the window class.</summary>
      <returns>The name of the window class.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.WindowClass.Versioned">
      <summary>Gets a value that controls whether the internal window class name that is used in registration contains the version of the assembly that contains the window class.</summary>
      <returns>yes if the name that is used in registration contains the version of the assembly; otherwise, no. The default is yes.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.WindowClass.XmlName">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.WindowClass.Name" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.WindowClass.Name" /> property.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.WindowClass.XmlVersioned">
      <summary>Gets or sets a property that is used to serialize the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.WindowClass.Versioned" /> property to an XML file.</summary>
      <returns>The value of the <see cref="P:Microsoft.Build.Tasks.Deployment.ManifestUtilities.WindowClass.Versioned" /> property.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Hosting.ICscHostObject">
      <summary>Defines an interface that allows the Csc task to communicate with the host integrated development environment (IDE).</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.BeginInitialization">
      <summary>Begins the initialization of the <see cref="T:Microsoft.Build.Tasks.Hosting.ICscHostObject" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.Compile">
      <summary>Compiles the project.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.EndInitialization(System.String@,System.Int32@)">
      <summary>Ends the initialization of the <see cref="T:Microsoft.Build.Tasks.Hosting.ICscHostObject" /> with the specified error message and code.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="errorMessage">The error message.</param>
      <param name="errorCode">The error code.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.IsDesignTime">
      <summary>Returns a value indicating whether the integrated development environment (IDE) is currently in design time mode.</summary>
      <returns>true if the IDE is currently in design time mode; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.IsUpToDate">
      <summary>Returns a value indicating whether the compiled project is up-to-date.</summary>
      <returns>true if the compiled project is up-to-date; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetAdditionalLibPaths(System.String[])">
      <summary>Specifies additional directories to search for references.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="additionalLibPaths">An array of directories to search for references.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetAddModules(System.String[])">
      <summary>Specifies one or more modules to be part of the assembly.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="addModules">An array of modules to be part of the assembly.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetAllowUnsafeBlocks(System.Boolean)">
      <summary>Specifies a value indicating whether to allow code that uses the unsafe keyword to compile.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="allowUnsafeBlocks">true to allow code that uses the unsafe keyword to compile; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetBaseAddress(System.String)">
      <summary>Specifies the preferred base address at which to load a DLL.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="baseAddress">The preferred base address at which to load a DLL.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetCheckForOverflowUnderflow(System.Boolean)">
      <summary>Specifies a value indicating whether integer arithmetic that overflows the bounds of the data type causes an exception at run time.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="checkForOverflowUnderflow">true to cause an exception when integer arithmetic overflows the bounds of the data type; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetCodePage(System.Int32)">
      <summary>Specifies the code page to use for all source code files in the compilation.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="codePage">The code page to use for all source code files in the compilation.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetDebugType(System.String)">
      <summary>Specifies the debug type.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="debugType">The debug type, which can be full or pdbonly.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetDefineConstants(System.String)">
      <summary>Specifies the preprocessor symbols to define.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="defineConstants">The preprocessor symbols to define.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetDelaySign(System.Boolean,System.Boolean)">
      <summary>Specifies a value indicating whether to create a fully signed assembly.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="delaySignExplicitlySet">true if the DelaySign parameter is explicitly set; otherwise, false.</param>
      <param name="delaySign">true if you want to create a fully signed assembly; false if you only want to place the public key in the assembly.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetDisabledWarnings(System.String)">
      <summary>Specifies the list of warnings to disable.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="disabledWarnings">The list of warnings to disable. Multiple warnings are separated with a comma.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetDocumentationFile(System.String)">
      <summary>Specifies the XML file in which to save processed documentation comments.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="documentationFile">A string that contains the name of the file in which to save processed documentation comments.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetEmitDebugInformation(System.Boolean)">
      <summary>Specifies a value indicating whether to generate debugging information and place it in a program database (.pdb) file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="emitDebugInformation">true to generate debugging information and place it in a .pdb file; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetErrorReport(System.String)">
      <summary>Specifies a value that indicates how internal compiler errors are reported to Microsoft.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="errorReport">A value specifying how internal compiler errors are reported to Microsoft. This value can be prompt, send, or none.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetFileAlignment(System.Int32)">
      <summary>Specifies the size of sections in the output file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="fileAlignment">The size of sections in the output file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetGenerateFullPaths(System.Boolean)">
      <summary>Specifies a value indicating whether to specify the absolute path to the file in the compiler output.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="generateFullPaths">true specify the absolute path to the file in the compiler output; false to specify only the name of the file in the compiler output.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetKeyContainer(System.String)">
      <summary>Specifies the name of the cryptographic key container.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="keyContainer">The name of the cryptographic key container.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetKeyFile(System.String)">
      <summary>Specifies the file name containing the cryptographic key.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="keyFile">The name of the file containing the cryptographic key.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetLangVersion(System.String)">
      <summary>Specifies the version of the language to use.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="langVersion">The version of the language to use.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetLinkResources(Microsoft.Build.Framework.ITaskItem[])">
      <summary>Creates links to the specified .NET Framework resources in the output file; the resource files are not placed in the output file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="linkResources">The .NET Framework resources to link to the output file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetMainEntryPoint(System.String,System.String)">
      <summary>Specifies the location of the Main method.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="targetType">The file format of the output file. This value can be library, exe, module, or winexe.</param>
      <param name="mainEntryPoint">The location of the Main method.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetModuleAssemblyName(System.String)">
      <summary>Specifies an assembly whose non-public types a .netmodule can access.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="moduleAssemblyName">An assembly whose non-public types a .netmodule can access.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetNoConfig(System.Boolean)">
      <summary>Specifies a value indicating whether to prevent the compiler from compiling with the csc.rsp file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="noConfig">true to prevent the compiler from compiling with the csc.rsp file; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetNoStandardLib(System.Boolean)">
      <summary>Specifies a value indicating whether to prevent the import of mscorlib.dll.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="noStandardLib">true to prevent the import of mscorlib.dll; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetOptimize(System.Boolean)">
      <summary>Specifies a value indicating whether to enable optimizations.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="optimize">true to enable optimizations; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetOutputAssembly(System.String)">
      <summary>Specifies the name of the output file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="outputAssembly">The name of the output file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetPdbFile(System.String)">
      <summary>Specifies the program database (.pdb) file in which to place generated debugging information.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="pdbFile">The .pdb file in which to place generated debugging information.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetPlatform(System.String)">
      <summary>Specifies the processor platform to be targeted by the output file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="platform">The processor platform to be targeted by the output file. This value can be x86, x64, or anycpu.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetReferences(Microsoft.Build.Framework.ITaskItem[])">
      <summary>Specifies the items from which to import public type information into the current project.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="references">The items from which to import public type information into the current project.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetResources(Microsoft.Build.Framework.ITaskItem[])">
      <summary>Specifies the .NET Framework resources to embed into the output file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="resources">The .NET Framework resources to embed into the output file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetResponseFiles(Microsoft.Build.Framework.ITaskItem[])">
      <summary>Specifies the response files that contain commands for the compiler.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="responseFiles">The response files that contain commands for the compiler.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetSources(Microsoft.Build.Framework.ITaskItem[])">
      <summary>Specifies one or more Visual C# source files.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="sources">One or more Visual C# source files.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetTargetType(System.String)">
      <summary>Specifies the file format of the output file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="targetType">The file format of the output file. This value can be library, exe, module, or winexe.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetTreatWarningsAsErrors(System.Boolean)">
      <summary>Specifies a value indicating whether to treat all warnings as errors.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="treatWarningsAsErrors">true to treat all warnings as errors; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetWarningLevel(System.Int32)">
      <summary>Specifies the warning level, from 0-4.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="warningLevel">The warning level, from 0-4.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetWarningsAsErrors(System.String)">
      <summary>Specifies a list of warnings to treat as errors.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="warningsAsErrors">A list of warnings to treat as errors.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetWarningsNotAsErrors(System.String)">
      <summary>Specifies a list of warnings that are not treated as errors.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="warningsNotAsErrors">A list of warnings that are not treated as errors.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetWin32Icon(System.String)">
      <summary>Specifies an .ico file to insert into the assembly.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="win32Icon">The .ico file to insert into the assembly.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject.SetWin32Resource(System.String)">
      <summary>Specifies a Win32 resource (.res) file to insert into the output file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="win32Resource">A Win32 resource (.res) file to insert into the output file.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Hosting.ICscHostObject2">
      <summary>Defines an interface that allows the Csc task to communicate with the host integrated development environment (IDE).</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject2.SetWin32Manifest(System.String)">
      <summary>Specifies a manifest file to insert into the assembly.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="win32Manifest">The name of the manifest to insert into the assembly.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Hosting.ICscHostObject3">
      <summary>Represents the Csc host compiler.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject3.SetApplicationConfiguration(System.String)">
      <summary>Set the application configuration.</summary>
      <param name="applicationConfiguration">The application configuration.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Hosting.ICscHostObject4">
      <summary>Represents the Csc host compiler.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject4.SetHighEntropyVA(System.Boolean)">
      <summary>Sets the /highentropyva value as specified.</summary>
      <returns>Returns True if the method was successful.</returns>
      <param name="highEntropyVA">True to set the / highentropyva to indicate support of high entropy Address Space Layout Randomization (ASLR); otherwise, False.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject4.SetPlatformWith32BitPreference(System.String)">
      <summary>Sets the /platform value to AnyCPU32BitPreferred.</summary>
      <returns>true if the method was successful.</returns>
      <param name="platformWith32BitPreference">true to set the /platform value to AnyCPU32BitPreferred; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.ICscHostObject4.SetSubsystemVersion(System.String)">
      <summary>Sets the subsystem version flag as specified.</summary>
      <returns>Returns true if the method succeeds.</returns>
      <param name="subsystemVersion">The subsystem version.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Hosting.IVbcHostObject">
      <summary>Defines an interface that allows the Vbc task to communicate with the host integrated development environment (IDE).</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.BeginInitialization">
      <summary>Begins the initialization of the <see cref="T:Microsoft.Build.Tasks.Hosting.IVbcHostObject" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.Compile">
      <summary>Compiles the project.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.EndInitialization">
      <summary>Ends the initialization of the <see cref="T:Microsoft.Build.Tasks.Hosting.IVbcHostObject" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.IsDesignTime">
      <summary>Returns a value indicating whether the integrated development environment (IDE) is currently in design time mode.</summary>
      <returns>true if the IDE is currently in design time mode; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.IsUpToDate">
      <summary>Returns a value indicating whether the compiled project is up-to-date.</summary>
      <returns>true if the compiled project is up-to-date; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetAdditionalLibPaths(System.String[])">
      <summary>Specifies additional directories to search for references.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="additionalLibPaths">An array of directories to search for references.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetAddModules(System.String[])">
      <summary>Specifies one or more modules to be part of the assembly.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="addModules">An array of modules to be part of the assembly.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetBaseAddress(System.String,System.String)">
      <summary>Specifies the preferred base address at which to load a DLL.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="targetType">The file format of the output file. This value can be library, exe, module, or winexe.</param>
      <param name="baseAddress">The preferred base address at which to load a DLL.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetCodePage(System.Int32)">
      <summary>Specifies the code page to use for all source code files in the compilation.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="codePage">The code page to use for all source code files in the compilation.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetDebugType(System.Boolean,System.String)">
      <summary>Specifies the debug type.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="emitDebugInformation">true to generate debugging information and place it in a program database (.pdb) file; otherwise, false.</param>
      <param name="debugType">The debug type, which can be full or pdbonly.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetDefineConstants(System.String)">
      <summary>Specifies the preprocessor symbols to define.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="defineConstants">The preprocessor symbols to define.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetDelaySign(System.Boolean)">
      <summary>Specifies a value indicating whether to create a fully signed assembly.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="delaySign">true if you want to create a fully signed assembly; false if you only want to place the public key in the assembly.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetDisabledWarnings(System.String)">
      <summary>Specifies the list of warnings to disable.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="disabledWarnings">The list of warnings to disable. Multiple warnings are separated with a comma.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetDocumentationFile(System.String)">
      <summary>Specifies the XML file in which to save processed documentation comments.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="documentationFile">A string that contains the name of the file in which to save processed documentation comments.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetErrorReport(System.String)">
      <summary>Specifies a value that indicates how internal compiler errors are reported to Microsoft.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="errorReport">A value specifying how internal compiler errors are reported to Microsoft. This value can be prompt, send, or none.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetFileAlignment(System.Int32)">
      <summary>Specifies the size of sections in the output file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="fileAlignment">The size of sections in the output file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetGenerateDocumentation(System.Boolean)">
      <summary>Specifies a value indicating whether to generate documentation and place it in an XML file with the name of the executable file or library that the compiler is creating.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="generateDocumentation">true to generate a documentation file; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetImports(Microsoft.Build.Framework.ITaskItem[])">
      <summary>Specifies the namespaces to import.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="importsList">The namespaces to import.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetKeyContainer(System.String)">
      <summary>Specifies the name of the cryptographic key container.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="keyContainer">The name of the cryptographic key container.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetKeyFile(System.String)">
      <summary>Specifies the file name containing the cryptographic key.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="keyFile">The name of the file containing the cryptographic key.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetLinkResources(Microsoft.Build.Framework.ITaskItem[])">
      <summary>Creates links to the specified .NET Framework resources in the output file; the resource files are not placed in the output file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="linkResources">The .NET Framework resources to link to the output file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetMainEntryPoint(System.String)">
      <summary>Specifies the class or module that contains the location of the Sub Main procedure.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="mainEntryPoint">The class or module that contains the location of the Sub Main procedure.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetNoConfig(System.Boolean)">
      <summary>Specifies a value indicating whether to prevent the compiler from compiling with the vbc.rsp file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="noConfig">true to prevent the compiler from compiling with the vbc.rsp file; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetNoStandardLib(System.Boolean)">
      <summary>Specifies a value indicating whether to prevent the import of mscorlib.dll.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="noStandardLib">true to prevent the import of mscorlib.dll; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetNoWarnings(System.Boolean)">
      <summary>Specifies a value indicating whether the compiler supresses all warnings.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="noWarnings">true to suppress all warnings; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetOptimize(System.Boolean)">
      <summary>Specifies a value indicating whether to enable optimizations.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="optimize">true to enable optimizations; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetOptionCompare(System.String)">
      <summary>Specifies a value indicating how the compiler makes string comparisons.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="optionCompare">A value indicating how the compiler makes string comparisons. The value must be either binary or text.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetOptionExplicit(System.Boolean)">
      <summary>Specifies a value indicating whether the explicit declaration of variables is required.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="optionExplicit">true to require the explicit declaration of variables; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetOptionStrict(System.Boolean)">
      <summary>Specifies a value indicating whether the compiler enforces strict type semantics to restrict implicit type conversions.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="optionStrict">true to enforce strict type semantics to restrict implicit type conversions; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetOptionStrictType(System.String)">
      <summary>Specifies that the compiler should warn when strict language semantics are not respected.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="optionStrictType">The string passed to the OptionStrictType parameter.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetOutputAssembly(System.String)">
      <summary>Specifies the name of the output file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="outputAssembly">The name of the output file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetPlatform(System.String)">
      <summary>Specifies the processor platform to be targeted by the output file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="platform">The processor platform to be targeted by the output file. This value can be x86, x64, Itanium, or anycpu.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetReferences(Microsoft.Build.Framework.ITaskItem[])">
      <summary>Specifies the items from which to import public type information into the current project.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="references">The items from which to import public type information into the current project.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetRemoveIntegerChecks(System.Boolean)">
      <summary>Specifies a value indicating whether to disable integer overflow error checks.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="removeIntegerChecks">true to remove integer overflow error checks; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetResources(Microsoft.Build.Framework.ITaskItem[])">
      <summary>Specifies the .NET Framework resources to embed into the output file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="resources">The .NET Framework resources to embed into the output file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetResponseFiles(Microsoft.Build.Framework.ITaskItem[])">
      <summary>Specifies the response files that contain commands for the compiler.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="responseFiles">The response files that contain commands for the compiler.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetRootNamespace(System.String)">
      <summary>Specifies the root namespace for all type declarations.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="rootNamespace">The root namespace for all type declarations.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetSdkPath(System.String)">
      <summary>Specifies the location of mscorlib.dll and microsoft.visualbasic.dll.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="sdkPath">The location of mscorlib.dll and microsoft.visualbasic.dll.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetSources(Microsoft.Build.Framework.ITaskItem[])">
      <summary>Specifies one or more Visual Basic source files.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="sources">One or more Visual Basic source files.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetTargetCompactFramework(System.Boolean)">
      <summary>Specifies a value indicating whether to target the .NET Compact Framework.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="targetCompactFramework">true to target the .NET Compact Framework; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetTargetType(System.String)">
      <summary>Specifies the file format of the output file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="targetType">The file format of the output file. This value can be library, exe, module, or winexe.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetTreatWarningsAsErrors(System.Boolean)">
      <summary>Specifies a value indicating whether to treat all warnings as errors.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="treatWarningsAsErrors">true to treat all warnings as errors; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetWarningsAsErrors(System.String)">
      <summary>Specifies a list of warnings to treat as errors.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="warningsAsErrors">A list of warnings to treat as errors.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetWarningsNotAsErrors(System.String)">
      <summary>Specifies a list of warnings that are not treated as errors.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="warningsNotAsErrors">A list of warnings that are not treated as errors.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetWin32Icon(System.String)">
      <summary>Specifies an .ico file to insert into the assembly.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="win32Icon">The .ico file to insert into the assembly.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject.SetWin32Resource(System.String)">
      <summary>Specifies a Win32 resource (.res) file to insert into the output file.</summary>
      <returns>true if the method was successful; otherwise, false.</returns>
      <param name="win32Resource">A Win32 resource (.res) file to insert into the output file.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Hosting.IVbcHostObject2">
      <summary>Defines an interface that allows the Vbc task to communicate with the host integrated development environment (IDE).</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject2.SetModuleAssemblyName(System.String)">
      <summary>Specifies the name of the module file.</summary>
      <returns>Returns true if the method was successful; otherwise, false.</returns>
      <param name="moduleAssemblyName">A string representing the name of the module file.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject2.SetOptionInfer(System.Boolean)">
      <summary>Specifies a value indicating whether variable types must be explicitly declared.</summary>
      <returns>Returns true if the method was successful; otherwise, false.</returns>
      <param name="optionInfer">true to require the explicit declaration of variable types; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject2.SetWin32Manifest(System.String)">
      <summary>Sets a Win32 manifest to use.</summary>
      <param name="win32Manifest">The win32 manifest</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Hosting.IVbcHostObject3">
      <summary>Defines an interface that allows the Vbc task to communicate with the host integrated development environment (IDE).</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject3.SetLanguageVersion(System.String)">
      <summary>Sets the language version.</summary>
      <returns>Returns true if the language version is set; false otherwise.</returns>
      <param name="languageVersion">The language version.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Hosting.IVbcHostObject4">
      <summary>Defines an interface for the Vbc task to communicate with the IDE. In particular, the Vbc task will delegate the actual compilation to the IDE, rather than shelling out to the command-line compilers.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject4.SetVBRuntime(System.String)">
      <summary>Defines an interface for the Vbc task to communicate with the IDE. In particular, the Vbc task will delegate the actual compilation to the IDE, rather than shelling out to the command line compiler.</summary>
      <returns>Returns true if the method succeeds; otherwise, returns false.</returns>
      <param name="VBRuntime">The name of the Visual Basic Runtime Library.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Hosting.IVbcHostObject5">
      <summary>Defines an interface that proffers a free-threaded host object that allows for background threads to call directly (avoids marshalling to the UI thread.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject5.CompileAsync(System.IntPtr@,System.IntPtr@)">
      <summary>This code is called only on the UI thread and kicks off the actual build with VB. </summary>
      <returns>Returns 0 if the method was successful.</returns>
      <param name="buildSucceededEvent">[out] the build-succeeded event.</param>
      <param name="buildFailedEvent">[out] the build-failed event.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject5.EndCompile(System.Boolean)">
      <summary>Performs functions at the end of compilation.</summary>
      <returns>Returns 0 if the method was successful.</returns>
      <param name="buildSuccess">A flag indicating whether the build succeeded. Value is true for success.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject5.GetFreeThreadedHostObject">
      <summary>Gets a free-threaded host object that allows for background threads to call directly. Avoids marshaling to the UI thread.</summary>
      <returns>Returns the free-threaded host object.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject5.SetHighEntropyVA(System.Boolean)">
      <summary>Sets the /highentropyva value as specified.</summary>
      <returns>True if the method was successful.</returns>
      <param name="highEntropyVA">True to set the / highentropyva to indicate support of high entropy Address Space Layout Randomization (ASLR); otherwise, False.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject5.SetPlatformWith32BitPreference(System.String)">
      <summary>Sets the /platform value to AnyCPU32BitPreferred.</summary>
      <returns>Returns True if the method was successful. </returns>
      <param name="platformWith32BitPreference">True to set the /platform value to AnyCPU32BitPreferred; otherwise, False.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObject5.SetSubsystemVersion(System.String)">
      <summary>Sets the subsystem version as specified.</summary>
      <returns>Returns true if the method is successful.</returns>
      <param name="subsystemVersion">The subsystem version.</param>
    </member>
    <member name="T:Microsoft.Build.Tasks.Hosting.IVbcHostObjectFreeThreaded">
      <summary>Defines a free-threaded interface for the Vbc task to communicate with the IDE. In particular, the Vbc task will delegate the actual compilation to the IDE, rather than shelling out to the command-line compilers.  This particular version of Compile (unlike the IVbcHostObject::Compile) is not marshalled back to the UI thread. The implementor of the interface is responsible for any marshalling.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Hosting.IVbcHostObjectFreeThreaded.Compile">
      <summary>Compile the source file.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Xaml.CommandLineArgumentRelation">
      <summary>Encapsulations the relations between command line arguments.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.CommandLineArgumentRelation.#ctor(System.String,System.String,System.Boolean,System.String)">
      <summary>Initializes an instance of the <see cref="Microsoft.Build.Tasks.Xaml.CommandLineArgumentRelation" /> class.</summary>
      <param name="argument">The argument</param>
      <param name="value">The value.</param>
      <param name="required">true if required.</param>
      <param name="separator">The separator between arguments.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineArgumentRelation.Separator">
      <summary>Contains the separator between command line arguments.</summary>
      <returns>A string containing the separator.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Xaml.CommandLineGenerator">
      <summary>Class for generating command lines.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.CommandLineGenerator.#ctor(Microsoft.Build.Framework.XamlTypes.Rule,System.Collections.Generic.Dictionary{System.String,System.Object})">
      <summary>Initializes a new instance of the <see cref="Microsoft.Build.Tasks.Xaml.CommandLineGenerator" /> class.</summary>
      <param name="rule">The rule</param>
      <param name="parameterValues">The parameter values.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineGenerator.AdditionalOptions">
      <summary>Contains a string of additional options to include on the command line.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineGenerator.AlwaysAppend">
      <summary>Contains the string to append to a command line when there is no template.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineGenerator.CommandLineTemplate">
      <summary>Contains the template to use when creating the command line.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.CommandLineGenerator.GenerateCommandLine">
      <summary>Generates a command line.</summary>
    </member>
    <member name="T:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch">
      <summary>Represents a single command line tool switch.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.#ctor(Microsoft.Build.Tasks.Xaml.CommandLineToolSwitchType)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch" /> to hold the name, tool, attributes, dependent switches, and values..</summary>
      <param name="toolType">The type of the switch.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.AllowMultipleValues">
      <summary>Indicates whether multiple values are allowed.</summary>
      <returns>True if multiple values are allowed; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.ArgumentRequired">
      <summary>Gets or sets a value that indicates whether arguments are required in the command line tool switch.</summary>
      <returns>True if arguments are required; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.Arguments">
      <summary>Gets or sets the arguments required for the command line tool switch.</summary>
      <returns>The arguments required for the command line tool switch.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.BooleanValue">
      <summary>Gets or sets the Boolean value of the command line switch.</summary>
      <returns>The Boolean value of the command line switch.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.Description">
      <summary>Gets or sets the description of the single command line tool switch.</summary>
      <returns>The description of the single command line tool switch.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.DisplayName">
      <summary>Gets or sets the display name of the switch.</summary>
      <returns>The display name of the switch.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.FallbackArgumentParameter">
      <summary>Gets or sets the fallback parameter.</summary>
      <returns>The fallback parameter.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.FalseSuffix">
      <summary>Gets or sets the suffix to use when the switch is false or negated.</summary>
      <returns>The suffix to use when the switch is false or negated.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.IncludeInCommandLine">
      <summary>Gets or sets a value that indicates whether to include the command line tool switch in the command line.</summary>
      <returns>true if to include the command line tool switch in the command line; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.IsValid">
      <summary>Gets or sets whether the switch is valid.</summary>
      <returns>True if the switch is valid; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.Name">
      <summary>Gets or sets the name of the switch.</summary>
      <returns>The name of the switch.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.Number">
      <summary>Gets or sets the value for integer type switches.</summary>
      <returns>The value for integer type switches.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.Overrides">
      <summary>Gets the overrides for the switch.</summary>
      <returns>The overrides for the switch.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.Parents">
      <summary>Gets the parents for the switch.</summary>
      <returns>The parents for the switch.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.Required">
      <summary>Gets or sets whether the switch is required.</summary>
      <returns>True If the switch is required; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.ReverseSwitchValue">
      <summary>Gets or sets the reverse switch text.</summary>
      <returns>The reverse switch text.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.Reversible">
      <summary>Gets or sets whether the switch is reversible.</summary>
      <returns>True if the switch is reversible; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.Separator">
      <summary>Gets or sets the separator between the switch and its arguments.</summary>
      <returns>The separator between the switch and its arguments.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.StringList">
      <summary>Gets or sets the collection of strings.</summary>
      <returns>The collection of strings.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.SwitchValue">
      <summary>Gets or sets the value for the switch.</summary>
      <returns>The value for the switch.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.TaskItemArray">
      <summary>Gets or sets an array of task items.</summary>
      <returns>The array of task items.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.TrueSuffix">
      <summary>Gets or sets the suffix to use when the switch is true.</summary>
      <returns>The suffix to use when the switch is true.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.Type">
      <summary>Gets or sets the type of the switch.</summary>
      <returns>The type of the switch.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch.Value">
      <summary>Gets or sets the value of the switch.</summary>
      <returns>The value of the switch.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitchType">
      <summary>Enumerates the types for command line switches.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitchType.Boolean">
      <summary>Indicates a Boolean type.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitchType.Integer">
      <summary>Indicates an integer type.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitchType.String">
      <summary>Indicates a string type.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitchType.StringArray">
      <summary>Indicates an array of strings type.</summary>
    </member>
    <member name="F:Microsoft.Build.Tasks.Xaml.CommandLineToolSwitchType.ITaskItemArray">
      <summary>Indicates a task item array type.</summary>
    </member>
    <member name="T:Microsoft.Build.Tasks.Xaml.PropertyRelation">
      <summary>Expresses a relationship between an argument and a property.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.PropertyRelation.#ctor">
      <summary>Initializes a new instance of the <see cref="PropertyRelation" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.PropertyRelation.#ctor(System.String,System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="PropertyRelation" /> class.</summary>
      <param name="argument">The argument.</param>
      <param name="value">The value of the property.</param>
      <param name="required">true if the property is required.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PropertyRelation.Argument">
      <summary>Contains the name of the argument.</summary>
      <returns>The name.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PropertyRelation.Required">
      <summary>Indicates if the property is required.</summary>
      <returns>true if the property is required.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PropertyRelation.Value">
      <summary>Contains the value of the property.</summary>
      <returns>The value.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask">
      <summary>Represents the tasks generated by the Xaml task factory.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.#ctor(System.String[],System.Resources.ResourceManager)">
      <summary>Initializes an instance of a Microsoft<see cref=".Build.Tasks.Xaml.XamlDataDrivenToolTask" /> class. </summary>
      <param name="switchOrderList">The list of switches in the order in which they should appear.</param>
      <param name="taskResources">The task resources.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.AcceptableNonZeroExitCodes">
      <summary>Gets or sets an array of non-zero codes which don't cause an error.</summary>
      <returns>An array of non-zero codes</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.ActiveToolSwitches">
      <summary>Gets a set of active tool switches.</summary>
      <returns>The set of active tool switches.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.ActiveToolSwitchesValues">
      <summary>Gets or sets the values of the active tool switches.</summary>
      <returns>Sets the values of the active tool switches.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.AddActiveSwitchToolValue(Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch)">
      <summary>Adds the value for a switch to the list of active values.</summary>
      <param name="switchToAdd">The switch to add.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.AdditionalOptions">
      <summary>Gets or sets any additional options specified in the project file.</summary>
      <returns>A string listing the options.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.CommandLineTemplate">
      <summary>Gets or sets the command line template.</summary>
      <returns>The command line template.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.CreateSwitchValue(System.String,System.String,System.String,System.Tuple{System.String,System.Boolean}[])">
      <summary>Creates a switch value for the <see cref="T:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask" />.</summary>
      <returns>The created switch value.</returns>
      <param name="propertyName">The property name.</param>
      <param name="baseSwitch">The base switch.</param>
      <param name="separator">The separator.</param>
      <param name="arguments">The arguments.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.Execute">
      <summary>Executes the task, then closes the event handle created.</summary>
      <returns>The result of the execution.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.GenerateCommandLineCommands">
      <summary>Generates the command line if it is less than 32k.</summary>
      <returns>The generated commands.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.GenerateFullPathToTool">
      <summary>Finds the tool if ToolPath wasn't specified.</summary>
      <returns>The generated full path.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.GenerateResponseFileCommands">
      <summary>Creates the response file command.</summary>
      <returns>The generated response file command.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.HandleTaskExecutionErrors">
      <summary>Handles the return code from the tool.</summary>
      <returns>The return codes.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.IsPropertySet(System.String)">
      <summary>Indicates if the property is set.</summary>
      <returns>true if the property is set.</returns>
      <param name="propertyName">The name of the property.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.ReadSwitchMap(System.String,System.String[][],System.String)">
      <summary>Checks the value a property is set to, and finds the corresponding switch.</summary>
      <param name="propertyName">The name of the property to check.</param>
      <param name="switchMap">The switch map.</param>
      <param name="value">The value of the property.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.ReadSwitchMap2(System.String,System.Tuple{System.String,System.String,System.Tuple{System.String,System.Boolean}[]}[],System.String)">
      <summary>Checks the value of a property is set to, and finds the corresponding switch.</summary>
      <returns>The checked value.</returns>
      <param name="propertyName">The name of the property to check.</param>
      <param name="switchMap">The switch map.</param>
      <param name="value">The value of the property.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.ReplaceToolSwitch(Microsoft.Build.Tasks.Xaml.CommandLineToolSwitch)">
      <summary>Replaces an existing switch with the specified one of the same name.</summary>
      <param name="switchToAdd">The name of the switch.</param>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.ResponseFileEncoding">
      <summary>Gets the response file encoding.</summary>
      <returns>The response file encoding.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.ValidateInteger(System.String,System.Int32,System.Int32,System.Int32)">
      <summary>Checks an integer property to see if it is within a specified range.</summary>
      <returns>true if the value is within the specified range.</returns>
      <param name="switchName">The name of the switch to check.</param>
      <param name="min">The minimum value.</param>
      <param name="max">The maximum value.</param>
      <param name="value">The value of the property.</param>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.XamlDataDrivenToolTask.ValidateParameters">
      <summary>Validates the parameters.</summary>
      <returns>true if the parameters are all valid.</returns>
    </member>
  </members>
</doc>