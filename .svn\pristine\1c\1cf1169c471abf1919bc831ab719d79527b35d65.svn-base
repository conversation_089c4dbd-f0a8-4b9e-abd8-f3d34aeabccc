﻿/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using MetroFramework.Native;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Security;
using System.Windows.Forms;

namespace MetroFramework.Controls
{
    public enum MetroLabelMode
    {
        Default,
        Selectable
    }
    [ToolboxBitmap(typeof(Label))]
    [Designer(typeof(Design.MetroLabelDesigner), typeof(System.Windows.Forms.Design.ParentControlDesigner))]
    public class MetroLabel : Label, IMetroControl
    {
        private class DoubleBufferedTextBox : TextBox
        {
            public DoubleBufferedTextBox()
            {
                SetStyle(ControlStyles.SupportsTransparentBackColor | ControlStyles.OptimizedDoubleBuffer, value: true);
            }
        }

        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        private MetroStyleManager metroStyleManager;

        private bool useCustomBackColor;

        private bool useCustomForeColor;

        private bool useStyleColors;

        private DoubleBufferedTextBox baseTextBox;

        private MetroLabelSize metroLabelSize = MetroLabelSize.Medium;

        private MetroLabelWeight metroLabelWeight;

        private MetroLabelMode labelMode;

        private bool wrapToLine;

        private bool firstInitialization = true;

        [Category("Metro Appearance")]
        [DefaultValue(MetroColorStyle.Blue)]
        public MetroColorStyle Style
        {
            get
            {
                if (base.DesignMode || metroStyle != 0)
                {
                    return metroStyle;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Style;
                }
                if (StyleManager == null)
                {
                    return MetroColorStyle.Blue;
                }
                return metroStyle;
            }
            set
            {
                metroStyle = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (base.DesignMode || metroTheme != 0)
                {
                    return metroTheme;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Theme;
                }
                if (StyleManager == null)
                {
                    return MetroThemeStyle.Light;
                }
                return metroTheme;
            }
            set
            {
                metroTheme = value;
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public MetroStyleManager StyleManager
        {
            get
            {
                return metroStyleManager;
            }
            set
            {
                metroStyleManager = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor
        {
            get
            {
                return useCustomBackColor;
            }
            set
            {
                useCustomBackColor = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomForeColor
        {
            get
            {
                return useCustomForeColor;
            }
            set
            {
                useCustomForeColor = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseStyleColors
        {
            get
            {
                return useStyleColors;
            }
            set
            {
                useStyleColors = value;
            }
        }

        [DefaultValue(false)]
        [Browsable(false)]
        [Category("Metro Behaviour")]
        public bool UseSelectable
        {
            get
            {
                return GetStyle(ControlStyles.Selectable);
            }
            set
            {
                SetStyle(ControlStyles.Selectable, value);
            }
        }

        [DefaultValue(MetroLabelSize.Medium)]
        [Category("Metro Appearance")]
        public MetroLabelSize FontSize
        {
            get
            {
                return metroLabelSize;
            }
            set
            {
                metroLabelSize = value;
                Refresh();
            }
        }

        [DefaultValue(MetroLabelWeight.Light)]
        [Category("Metro Appearance")]
        public MetroLabelWeight FontWeight
        {
            get
            {
                return metroLabelWeight;
            }
            set
            {
                metroLabelWeight = value;
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroLabelMode.Default)]
        public MetroLabelMode LabelMode
        {
            get
            {
                return labelMode;
            }
            set
            {
                labelMode = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Behaviour")]
        public bool WrapToLine
        {
            get
            {
                return wrapToLine;
            }
            set
            {
                wrapToLine = value;
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintBackground != null)
            {
                this.CustomPaintBackground(this, e);
            }
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaint != null)
            {
                this.CustomPaint(this, e);
            }
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintForeground != null)
            {
                this.CustomPaintForeground(this, e);
            }
        }

        public MetroLabel()
        {
            SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor | ControlStyles.OptimizedDoubleBuffer, value: true);
            baseTextBox = new DoubleBufferedTextBox();
            baseTextBox.Visible = false;
            base.Controls.Add(baseTextBox);
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                Color color = BackColor;
                if (!useCustomBackColor)
                {
                    color = MetroPaint.BackColor.Form(Theme);
                    if (base.Parent is MetroTile)
                    {
                        color = MetroPaint.GetStyleColor(Style);
                    }
                }
                if (color.A == byte.MaxValue && BackgroundImage == null)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint))
                {
                    OnPaintBackground(e);
                }
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            Color foreColor = useCustomForeColor ? ForeColor : ((!base.Enabled) ? ((base.Parent == null) ? MetroPaint.ForeColor.Label.Disabled(Theme) : ((!(base.Parent is MetroTile)) ? MetroPaint.ForeColor.Label.Normal(Theme) : MetroPaint.ForeColor.Tile.Disabled(Theme))) : ((base.Parent != null) ? ((base.Parent is MetroTile) ? MetroPaint.ForeColor.Tile.Normal(Theme) : ((!useStyleColors) ? MetroPaint.ForeColor.Label.Normal(Theme) : MetroPaint.GetStyleColor(Style))) : ((!useStyleColors) ? MetroPaint.ForeColor.Label.Normal(Theme) : MetroPaint.GetStyleColor(Style))));
            if (LabelMode == MetroLabelMode.Selectable)
            {
                CreateBaseTextBox();
                UpdateBaseTextBox();
                if (!baseTextBox.Visible)
                {
                    TextRenderer.DrawText(e.Graphics, Text, MetroFonts.Label(metroLabelSize, metroLabelWeight), base.ClientRectangle, foreColor, MetroPaint.GetTextFormatFlags(TextAlign));
                }
            }
            else
            {
                DestroyBaseTextbox();
                TextRenderer.DrawText(e.Graphics, Text, MetroFonts.Label(metroLabelSize, metroLabelWeight), base.ClientRectangle, foreColor, MetroPaint.GetTextFormatFlags(TextAlign, wrapToLine));
                OnCustomPaintForeground(new MetroPaintEventArgs(Color.Empty, foreColor, e.Graphics));
            }
        }

        public override void Refresh()
        {
            if (LabelMode == MetroLabelMode.Selectable)
            {
                UpdateBaseTextBox();
            }
            base.Refresh();
        }

        public override Size GetPreferredSize(Size proposedSize)
        {
            base.GetPreferredSize(proposedSize);
            using (Graphics dc = CreateGraphics())
            {
                proposedSize = new Size(int.MaxValue, int.MaxValue);
                return TextRenderer.MeasureText(dc, Text, MetroFonts.Label(metroLabelSize, metroLabelWeight), proposedSize, MetroPaint.GetTextFormatFlags(TextAlign));
            }
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }

        protected override void OnResize(EventArgs e)
        {
            if (LabelMode == MetroLabelMode.Selectable)
            {
                HideBaseTextBox();
            }
            base.OnResize(e);
        }

        protected override void OnSizeChanged(EventArgs e)
        {
            base.OnSizeChanged(e);
            if (LabelMode == MetroLabelMode.Selectable)
            {
                ShowBaseTextBox();
            }
        }

        private void CreateBaseTextBox()
        {
            if ((baseTextBox.Visible && !firstInitialization) || !firstInitialization)
            {
                return;
            }
            firstInitialization = false;
            if (!base.DesignMode)
            {
                Form form = FindForm();
                if (form != null)
                {
                    form.ResizeBegin += parentForm_ResizeBegin;
                    form.ResizeEnd += parentForm_ResizeEnd;
                }
            }
            baseTextBox.BackColor = Color.Transparent;
            baseTextBox.Visible = true;
            baseTextBox.BorderStyle = BorderStyle.None;
            baseTextBox.Font = MetroFonts.Label(metroLabelSize, metroLabelWeight);
            baseTextBox.Location = new Point(1, 0);
            baseTextBox.Text = Text;
            baseTextBox.ReadOnly = true;
            baseTextBox.Size = GetPreferredSize(Size.Empty);
            baseTextBox.Multiline = true;
            baseTextBox.DoubleClick += BaseTextBoxOnDoubleClick;
            baseTextBox.Click += BaseTextBoxOnClick;
            base.Controls.Add(baseTextBox);
        }

        private void parentForm_ResizeEnd(object sender, EventArgs e)
        {
            if (LabelMode == MetroLabelMode.Selectable)
            {
                ShowBaseTextBox();
            }
        }

        private void parentForm_ResizeBegin(object sender, EventArgs e)
        {
            if (LabelMode == MetroLabelMode.Selectable)
            {
                HideBaseTextBox();
            }
        }

        private void DestroyBaseTextbox()
        {
            if (baseTextBox.Visible)
            {
                baseTextBox.DoubleClick -= BaseTextBoxOnDoubleClick;
                baseTextBox.Click -= BaseTextBoxOnClick;
                baseTextBox.Visible = false;
            }
        }

        private void UpdateBaseTextBox()
        {
            if (!baseTextBox.Visible)
            {
                return;
            }
            SuspendLayout();
            baseTextBox.SuspendLayout();
            if (useCustomBackColor)
            {
                baseTextBox.BackColor = BackColor;
            }
            else
            {
                baseTextBox.BackColor = MetroPaint.BackColor.Form(Theme);
            }
            if (!base.Enabled)
            {
                if (base.Parent != null)
                {
                    if (base.Parent is MetroTile)
                    {
                        baseTextBox.ForeColor = MetroPaint.ForeColor.Tile.Disabled(Theme);
                    }
                    else if (useStyleColors)
                    {
                        baseTextBox.ForeColor = MetroPaint.GetStyleColor(Style);
                    }
                    else
                    {
                        baseTextBox.ForeColor = MetroPaint.ForeColor.Label.Disabled(Theme);
                    }
                }
                else if (useStyleColors)
                {
                    baseTextBox.ForeColor = MetroPaint.GetStyleColor(Style);
                }
                else
                {
                    baseTextBox.ForeColor = MetroPaint.ForeColor.Label.Disabled(Theme);
                }
            }
            else if (base.Parent != null)
            {
                if (base.Parent is MetroTile)
                {
                    baseTextBox.ForeColor = MetroPaint.ForeColor.Tile.Normal(Theme);
                }
                else if (useStyleColors)
                {
                    baseTextBox.ForeColor = MetroPaint.GetStyleColor(Style);
                }
                else
                {
                    baseTextBox.ForeColor = MetroPaint.ForeColor.Label.Normal(Theme);
                }
            }
            else if (useStyleColors)
            {
                baseTextBox.ForeColor = MetroPaint.GetStyleColor(Style);
            }
            else
            {
                baseTextBox.ForeColor = MetroPaint.ForeColor.Label.Normal(Theme);
            }
            baseTextBox.Font = MetroFonts.Label(metroLabelSize, metroLabelWeight);
            baseTextBox.Text = Text;
            baseTextBox.BorderStyle = BorderStyle.None;
            base.Size = GetPreferredSize(Size.Empty);
            baseTextBox.ResumeLayout();
            ResumeLayout();
        }

        private void HideBaseTextBox()
        {
            baseTextBox.Visible = false;
        }

        private void ShowBaseTextBox()
        {
            baseTextBox.Visible = true;
        }

        [SecuritySafeCritical]
        private void BaseTextBoxOnClick(object sender, EventArgs eventArgs)
        {
            WinCaret.HideCaret(baseTextBox.Handle);
        }

        [SecuritySafeCritical]
        private void BaseTextBoxOnDoubleClick(object sender, EventArgs eventArgs)
        {
            baseTextBox.SelectAll();
            WinCaret.HideCaret(baseTextBox.Handle);
        }
    }

}
