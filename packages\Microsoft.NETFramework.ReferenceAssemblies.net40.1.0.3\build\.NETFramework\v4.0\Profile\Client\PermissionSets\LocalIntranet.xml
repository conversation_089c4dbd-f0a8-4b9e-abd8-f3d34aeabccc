﻿<PermissionSet
   version="1"
   class="System.Security.NamedPermissionSet"
   Name="LocalIntranet"
   Description="Default rights given to applications on the local intranet">
   <IPermission
      Read="USERNAME"
      version="1"
      class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
   <IPermission
      version="1"
      class="System.Security.Permissions.FileDialogPermission, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"
      Unrestricted="true" />
   <IPermission
      version="1"
      Permanent="True"
      Expiry="9223372036854775807"
      UserQuota="9223372036854775807"
      Allowed="AssemblyIsolationByUser"
      class="System.Security.Permissions.IsolatedStorageFilePermission, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
   <IPermission
      version="1"
      class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"
      Flags="ReflectionEmit, RestrictedMemberAccess" />
   <IPermission
      version="1"
      class="System.Security.Permissions.SecurityPermission, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"
      Flags="Assertion, Execution, BindingRedirects" />
   <IPermission
      version="1"
      class="System.Security.Permissions.UIPermission, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"
      Unrestricted="true" />
   <IPermission
      version="1"
      class="System.Drawing.Printing.PrintingPermission, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
      Level="DefaultPrinting" />
   <IPermission
      version="1"
      class="System.Security.Permissions.MediaPermission, WindowsBase, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
      Audio="SafeAudio"
      Image="SafeImage"
      Video="SafeVideo" />
   <IPermission
      version="1"
      class="System.Security.Permissions.WebBrowserPermission, WindowsBase, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
      Level="Safe" />
   <IPermission
      version="1"
      class="System.Net.DnsPermission, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"
      Unrestricted="true" />
</PermissionSet>