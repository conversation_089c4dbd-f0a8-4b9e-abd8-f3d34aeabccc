﻿using OCRTools.Common;
using System;
using System.Collections.Specialized;
using System.IO;
using System.Net;
using System.Reflection;
using OCRTools;

namespace ImageLib
{
    /// <summary>
    /// PnnLAB
    /// </summary>
    public class ALiYunUpload
    {
        public static bool Enable { get; set; } = true;

        private const string strFileNameSpilt = "origin_url\":\"";
        private const string strFileNameSpilt2 = "output_file\":\"";

        //http://www.m6a.cn/
        private static AliYunToken GetToken1()
        {
            var html = WebClientExt.GetHtml("http://www.m6a.cn/index/getToken?t=" + ServerTime.DateTime.Ticks);
            if (!string.IsNullOrEmpty(html) && html.Length > 2)
            {
                return CommonString.JavaScriptSerializer.Deserialize<AliYunToken>(html);
            }

            return null;
        }

        //https://www.hipdf.cn/image-compressor
        private static AliYunToken GetToken2()
        {
            var html = WebClientExt.GetHtml("https://www.hipdf.cn/middle/file/get-oss-policy?t=" + ServerTime.DateTime.Ticks);
            if (!string.IsNullOrEmpty(html) && html.Length > 2)
            {
                return CommonString.JavaScriptSerializer.Deserialize<AliYunTokenRoot>(html)?.data;
            }

            return null;
        }

        internal static string GetResult(byte[] content, bool isZip = false)
        {
            var result = string.Empty;
            var token = GetToken1();
            if (!isZip && (token == null || string.IsNullOrEmpty(token.accessid)))
            {
                token = GetToken2();
            }

            if (token == null || string.IsNullOrEmpty(token.accessid))
                return result;
            var file = new UploadFileInfo()
            {
                Name = "file",
                Filename = "1.png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            var vaules = new NameValueCollection() {
                    { "ossAccessKeyId", token.accessid } ,
                    { "policy", token.policy } ,
                    { "signature", token.signature } ,
                    { "callback", token.callback } ,
                    { "key", token.dir +ServerTime.DateTime.ToString("M-d")+ "/"+Guid.NewGuid().ToString().ToLower().Replace("-","")+".PNG" }
                };
            try
            {
                var html = UploadFileRequest.Post(token.host, new[] { file
    }, vaules);
                if (html?.Contains(strFileNameSpilt) == true)
                {
                    result = CommonMethod.SubString(html, strFileNameSpilt, "\"").Replace("\\/", "/");
                }
                else if (html?.Contains(strFileNameSpilt2) == true)
                {
                    result = CommonMethod.SubString(html, strFileNameSpilt2, "\"").Replace("\\/", "/");
                }
                if (!string.IsNullOrEmpty(result))
                {
                    if (!result.StartsWith("http"))
                    {
                        result = token.host + "/" + result;
                    }
                }
            }
            catch { }
            return result;
        }

        [Obfuscation]
        internal class AliYunTokenRoot
        {
            [Obfuscation]
            public AliYunToken data { get; set; }
        }

        [Obfuscation]
        internal class AliYunToken
        {
            [Obfuscation]
            public string accessid { get; set; }

            [Obfuscation]
            public string host { get; set; }

            [Obfuscation]
            public string policy { get; set; }

            [Obfuscation]
            public string signature { get; set; }

            [Obfuscation]
            public int expire { get; set; }

            [Obfuscation]
            public string dir { get; set; }

            [Obfuscation]
            public string callback { get; set; }
        }
    }
}
