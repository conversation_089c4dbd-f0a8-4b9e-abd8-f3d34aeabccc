﻿using OCRTools.Language;
using OCRTools.ScreenCaptureLib;
using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;
using static MetroFramework.Drawing.MetroPaint;

namespace OCRTools.Common
{
    public class GuidePresenterForm : Form
    {
        private Form _mainForm;
        private GuideEntity guide;
        private int _currentGuideIndex = -1;
        private Panel pnlMain;
        private Label lblTitle;
        private Label lblDesc;
        private Button btnPrev;
        private Button btnNext;
        private LinkLabel lnkSkip;
        private Color _guidePanelColor = Color.FromArgb(0, 112, 249);
        private double wScale = 1d;
        private double hScale = 1d;

        public GuidePresenterForm(Form mainForm, GuideEntity entity)
        {
            guide = entity;
            if (guide.ShowSummary && !guide.Items[0].Rect.IsEmpty)
            {
                var strSummary = guide.Desc;
                if (string.IsNullOrEmpty(strSummary))
                {
                    strSummary = string.Join("\n", guide.Items.Where(p => p.Summary).Select(p => p.Title.CurrentText(true))).Trim();
                    if (string.IsNullOrEmpty(strSummary))
                    {
                        strSummary = guide.Title;
                    }
                }
                guide.Items.Insert(0, new GuideItem()
                {
                    Title = guide.Title,
                    Desc = strSummary,
                    Rect = Rectangle.Empty
                });
            }
            _mainForm = mainForm;
            if (!mainForm.Visible)
            {
                mainForm.Show();
                Application.DoEvents();
            }
            if (mainForm.WindowState != FormWindowState.Normal)
            {
                mainForm.WindowState = FormWindowState.Normal;
                Application.DoEvents();
            }
            wScale = _mainForm.Width * 1d / guide.BaseSize.Width;
            hScale = _mainForm.Height * 1d / guide.BaseSize.Height;
            FormBorderStyle = FormBorderStyle.None;
            ShowInTaskbar = false;
            Margin = CommonString.PaddingZero;
            Padding = CommonString.PaddingZero;
            BackgroundImageLayout = ImageLayout.Stretch;
            InitializeControls();
            CommonMethod.EnableDoubleBuffering(this);
            Shown += GuidePresenterForm_Shown;
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            if (keyData == Keys.Escape)
            {
                CloseGuide();
                return true;
            }
            if (keyData == Keys.Left || keyData == Keys.Up)
            {
                ShowPreviousGuide();
                return true;
            }
            if (keyData == Keys.Right || keyData == Keys.Down)
            {
                ShowNextGuide(false);
                return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }

        private void GuidePresenterForm_Shown(object sender, EventArgs e)
        {
            //SetStyle(ControlStyles.SupportsTransparentBackColor,true);
            //this.BackColor = Color.Transparent;
            ////BackColor = Color.Green;
            ////TransparencyKey = BackColor;
            RefreshBackImg();
            Bounds = _mainForm.Bounds;
            Location = _mainForm.Location;
            if (guide?.Items?.Count > 0)
            {
                ShowNextGuide(false);
            }
        }

        private void RefreshBackImg()
        {
            this.Hide();
            Application.DoEvents();
            var rect = Rectangle.Empty;
            var backImg = Screenshot.CaptureWindow(_mainForm.Handle, ref rect);
            BackgroundImage = backImg;
            this.Show();
        }

        private void InitializeControls()
        {
            // 创建引导提示框及其内部控件
            pnlMain = new Panel
            {
                BackColor = _guidePanelColor,
                Size = new Size(Math.Min(_mainForm.Width, (int)(450 * wScale)), Math.Min((int)(196 * hScale), _mainForm.Width)),
                Location = new Point(100, 100),
                Dock = DockStyle.None,
            };
            Controls.Add(pnlMain);

            lblTitle = new Label
            {
                Font = CommonString.GetSysBoldFont(20),
                ForeColor = Color.White,
                Padding = new Padding(20, 20, 20, 0),
                AutoSize = true
            };
            pnlMain.Controls.Add(lblTitle);

            lblDesc = new Label
            {
                Font = CommonString.GetSysNormalFont(15),
                ForeColor = Color.White,
                Padding = new Padding(20, 0, 20, 0),
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleLeft,
                Size = new Size(pnlMain.Width, (int)(90 * hScale)),
                Top = lblTitle.Top + lblTitle.Height,
            };
            pnlMain.Controls.Add(lblDesc);

            // 创建引导提示框中的按钮
            lnkSkip = new LinkLabel
            {
                Text = "跳过".CurrentText(),
                ForeColor = Color.FromArgb(165, 205, 253),
                LinkColor = Color.FromArgb(165, 205, 253),
                BackColor = _guidePanelColor,
                Font = CommonString.GetSysBoldFont(13),
                Location = new Point(20, pnlMain.Height - (int)(42 * hScale)),
                AutoSize = true,
                LinkBehavior = LinkBehavior.NeverUnderline,
                TabStop = false
            };

            var btnWidth = (int)(100 * wScale);
            var btnFont = CommonString.GetSysBoldFont(16);
            btnPrev = new Button
            {
                Text = "上一步".CurrentText(),
                Size = new Size(btnWidth, (int)(40 * hScale)),
                ForeColor = Color.White,
                BackColor = _guidePanelColor,
                Font = btnFont,
                Visible = false,
                TabStop = false,
                Location = new Point(pnlMain.Width - 30 - btnWidth * 2, pnlMain.Height - (int)(50 * hScale))
            };
            btnNext = new Button
            {
                Text = "下一步".CurrentText(),
                Size = new Size(btnWidth, (int)(40 * hScale)),
                ForeColor = _guidePanelColor,
                BackColor = Color.White,
                Font = btnFont,
                Visible = false,
                TabStop = false,
                Location = new Point(btnPrev.Left + btnWidth + 10, pnlMain.Height - (int)(50 * hScale))
            };

            btnPrev.Click += (s, e) => ShowPreviousGuide();
            btnNext.Click += (s, e) => ShowNextGuide(true);
            lnkSkip.Click += (s, e) => CloseGuide();

            pnlMain.Controls.Add(btnPrev);
            pnlMain.Controls.Add(btnNext);
            pnlMain.Controls.Add(lnkSkip);
            CommonMethod.SetStyle(lnkSkip, ControlStyles.Selectable, false);
        }

        private void ShowNextGuide(bool isUser)
        {
            if (_currentGuideIndex < guide.Items.Count - 1)
            {
                _currentGuideIndex++;
                ShowGuide(guide.Items[_currentGuideIndex]);
                btnNext.Focus();
            }
            else if (isUser && _currentGuideIndex == guide.Items.Count - 1)
            {
                CloseGuide();
            }
        }

        private void ShowPreviousGuide()
        {
            if (_currentGuideIndex > 0)
            {
                _currentGuideIndex--;
                ShowGuide(guide.Items[_currentGuideIndex]);
                if (btnPrev.Visible)
                    btnPrev.Focus();
                else
                    btnNext.Focus();
            }
        }

        private void ShowGuide(GuideItem item)
        {
            if (!string.IsNullOrEmpty(item.Exec))
            {
                _mainForm.ExecuteScript(item.Exec);
                RefreshBackImg();
            }
            var rect = item.Rect;
            if (!string.IsNullOrEmpty(item.Ctrl))
            {
                var targetControl = FindTargetControl(item.Ctrl);
                if (targetControl != null)
                {
                    rect = targetControl.Bounds;
                    var frmPadding = targetControl.FindForm().Padding;
                    rect.Y += frmPadding.Top;
                    rect.X += frmPadding.Left;
                    rect = rect.ZoomBig(10);
                }
            }
            if (!rect.IsEmpty)
            {
                if (!guide.BaseSize.IsEmpty)
                {
                    // 等比例缩放
                    var widthScale = Width * 1d / guide.BaseSize.Width;
                    var heightScale = Height * 1d / guide.BaseSize.Height;
                    rect.X = (int)(rect.X * widthScale);
                    rect.Y = (int)(rect.Y * heightScale);
                    rect.Width = (int)(rect.Width * widthScale);
                    rect.Height = (int)(rect.Height * heightScale);
                }
                if (rect.X < 0)
                {
                    rect.X += Width;
                }
                else if (rect.Y < 0)
                {
                    rect.Y += Height;
                }
            }
            //if (!rect.IsEmpty)
            {
                lblTitle.Text = item.Title;
                lblDesc.Text = item.Desc;
                var index = guide.Items.IndexOf(item);
                if (guide.ShowSummary)
                    lnkSkip.Text = "跳过".CurrentText() + " [Esc]" + (index > 0 ? $" ({index}/{guide.Items.Count - 1})" : "");
                else
                    lnkSkip.Text = "跳过".CurrentText() + $" [Esc] ({index + 1}/{guide.Items.Count})";

                //rect = rect.Zoom(1.5);
                var arrow = SetGuidePanelPosition(rect);
                UpdateButtonVisibility();
                SetHighlight(rect, arrow);
            }
        }

        private void SetHighlight(Rectangle rectangle, ArrowDirection arrow)
        {
            Region formRegion = new Region(ClientRectangle);

            if (!rectangle.IsEmpty)
            {
                var radius = 8;
                GraphicsPath tabPath = new GraphicsPath();
                tabPath.AddArc(rectangle.X, rectangle.Y, radius * 2, radius * 2, 180, 90); // 左上角圆弧
                tabPath.AddArc(rectangle.Right - radius * 2, rectangle.Y, radius * 2, radius * 2, 270, 90); // 右上角圆弧
                tabPath.AddArc(rectangle.Right - radius * 2, rectangle.Bottom - radius * 2, radius * 2, radius * 2, 0, 90); // 右下角圆弧
                tabPath.AddArc(rectangle.X, rectangle.Bottom - radius * 2, radius * 2, radius * 2, 90, 90); // 左下角圆弧
                tabPath.CloseFigure();

                Point[] points = new Point[3];
                var arrowSize = 20;
                var arrowWidth = 20;

                switch (arrow)
                {
                    case ArrowDirection.Up:
                        var location = new Point(rectangle.Left + rectangle.Width / 2, rectangle.Top);
                        points[0] = new Point(location.X - arrowSize / 2, location.Y);
                        points[1] = new Point(location.X + arrowSize / 2, location.Y);
                        points[2] = new Point(location.X, location.Y - arrowWidth);
                        break;
                    case ArrowDirection.Down:
                        location = new Point(rectangle.Left + rectangle.Width / 2, rectangle.Bottom);
                        points[0] = new Point(location.X - arrowSize / 2, location.Y);
                        points[1] = new Point(location.X + arrowSize / 2, location.Y);
                        points[2] = new Point(location.X, location.Y + arrowWidth);
                        break;
                    case ArrowDirection.Left:
                        location = new Point(rectangle.Left, rectangle.Top + rectangle.Height / 2);
                        points[0] = new Point(location.X, location.Y - arrowSize / 2);
                        points[1] = new Point(location.X, location.Y + arrowSize / 2);
                        points[2] = new Point(location.X - arrowWidth, location.Y);
                        break;
                    case ArrowDirection.Right:
                        location = new Point(rectangle.Right, rectangle.Top + rectangle.Height / 2);
                        points[0] = new Point(location.X, location.Y - arrowSize / 2);
                        points[1] = new Point(location.X, location.Y + arrowSize / 2);
                        points[2] = new Point(location.X + arrowWidth, location.Y);
                        break;
                }
                // 创建三角形区域
                tabPath.AddPolygon(new Point[] { points[0], points[1], points[2] });

                // 从区域中排除指定的矩形区域
                formRegion.Exclude(new Region(tabPath));
            }

            using (var g = CreateGraphics())
            {
                g.SmoothingMode = SmoothingMode.AntiAlias;
                g.InterpolationMode = InterpolationMode.High;
                g.CompositingQuality = CompositingQuality.HighQuality;
                g.PixelOffsetMode = PixelOffsetMode.HighQuality;

                var diff = CommonSetting.夜间模式 ? 150 : 255;
                using (Brush brush = new SolidBrush(Color.FromArgb(80, 255 - diff, 255 - diff, 255 - diff)))
                {
                    Refresh();

                    // 使用半透明画笔填充区域
                    g.FillRegion(brush, formRegion);
                }
            }
        }

        private ArrowDirection SetGuidePanelPosition(Rectangle targetArea)
        {
            ArrowDirection arrow;
            // 根据高亮区域的位置自适应调整引导提示框的位置
            int guidePanelWidth = pnlMain.Width;
            int guidePanelHeight = pnlMain.Height;
            int targetControlX = targetArea.X;
            int targetControlY = targetArea.Y;
            int targetControlWidth = targetArea.Width;
            int targetControlHeight = targetArea.Height;
            int screenWidth = Width;
            int screenHeight = Height;

            int guidePanelX, guidePanelY;
            if (targetArea.IsEmpty)
            {
                arrow = ArrowDirection.Down;
                // 在高亮区域下方显示
                guidePanelX = 0;
                guidePanelY = (screenHeight - guidePanelHeight) / 2;
            }
            else
            {
                if (targetControlX + targetControlWidth + guidePanelWidth <= screenWidth)
                {
                    arrow = ArrowDirection.Right;
                    // 在高亮区域右侧显示
                    guidePanelX = targetControlX + targetControlWidth + 20;
                    guidePanelY = targetControlY + (targetControlHeight - guidePanelHeight) / 2;
                }
                else if (targetControlX - guidePanelWidth >= 0)
                {
                    arrow = ArrowDirection.Left;
                    // 在高亮区域左侧显示
                    guidePanelX = targetControlX - guidePanelWidth - 20;
                    guidePanelY = targetControlY + (targetControlHeight - guidePanelHeight) / 2;
                }
                else if (targetControlY + targetControlHeight + guidePanelHeight <= screenHeight)
                {
                    arrow = ArrowDirection.Down;
                    // 在高亮区域下方显示
                    guidePanelX = targetControlX + (targetControlWidth - guidePanelWidth) / 2;
                    guidePanelY = targetControlY + targetControlHeight + 20;
                }
                else
                {
                    arrow = ArrowDirection.Up;
                    // 在高亮区域上方显示
                    guidePanelX = targetControlX + (targetControlWidth - guidePanelWidth) / 2;
                    guidePanelY = targetControlY - guidePanelHeight - 20;
                }
            }

            pnlMain.Location = new Point(Math.Max(0, guidePanelX), Math.Max(guidePanelY, 0));
            if (pnlMain.Location.X + pnlMain.Width > Width)
            {
                pnlMain.Location = new Point(Width - pnlMain.Width - 5, pnlMain.Location.Y);
            }
            if (pnlMain.Location.Y + pnlMain.Height > Height)
            {
                pnlMain.Location = new Point(pnlMain.Location.X, Height - pnlMain.Height - 5);
            }
            return arrow;
        }

        private void UpdateButtonVisibility()
        {
            btnPrev.Visible = (_currentGuideIndex > 0);
            btnNext.Visible = (_currentGuideIndex <= guide.Items.Count - 1);
            lnkSkip.Visible = guide.Items.Count > 1;
            btnNext.Text = (_currentGuideIndex == guide.Items.Count - 1) ? "完成".CurrentText() : "下一步".CurrentText();
        }

        private Control FindTargetControl(string controlName)
        {
            return _mainForm.Controls.Find(controlName, true).FirstOrDefault() ?? _mainForm.Controls.OfType<Control>().FirstOrDefault(p => Equals(p.AccessibleDescription, controlName));
        }

        private void CloseGuide()
        {
            _currentGuideIndex = -1;
            Controls.Clear();
            this.Close();
        }
    }
}
