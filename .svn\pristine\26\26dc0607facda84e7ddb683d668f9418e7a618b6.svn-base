using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Emgu.CV;
using Emgu.CV.CvEnum;

namespace OcrLib
{
    public class Ocr
    {
        private DbNet dbNet;

        private AngleNet angleNet;

        private CrnnNet crnnNet;

        private string OcrType;

        public Ocr(string ocrType = "")
        {
            OcrType = ocrType;
            dbNet = new DbNet();
            angleNet = new AngleNet();
            crnnNet = new CrnnNet();
        }

        public void InitModels(string detPath, string clsPath, string recPath, string keysPath, int numThread)
        {
            try
            {
                dbNet.InitModel(detPath, numThread);
                angleNet.InitModel(clsPath, numThread);
                crnnNet.InitModel(recPath, keysPath, numThread);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message + ex.StackTrace);
                throw ex;
            }
        }

        public OcrResult Detect(byte[] img, int padding, int imgResize, float boxScoreThresh, float boxThresh, float unClipRatio, bool doAngle, bool mostAngle, bool isPaddle)
        {
            Mat mat = new Mat();
            CvInvoke.Imdecode(img, ImreadModes.Color, mat);
            return DetectByMat(padding, imgResize, boxScoreThresh, boxThresh, unClipRatio, doAngle, mostAngle, mat, isPaddle);
        }

        private OcrResult DetectByMat(int padding, int imgResize, float boxScoreThresh, float boxThresh, float unClipRatio, bool doAngle, bool mostAngle, Mat brgSrc, bool isPaddle)
        {
            Mat mat = new Mat();
            CvInvoke.CvtColor(brgSrc, mat, ColorConversion.Bgr2Rgb);
            Mat mat2 = OcrUtils.MakePadding(mat, padding);
            int dstSize = ((imgResize > 0) ? imgResize : Math.Max(mat2.Cols, mat2.Rows));
            ScaleParam scaleParam = ScaleParam.GetScaleParam(mat2, dstSize);
            return DetectOnce(mat2, scaleParam, boxScoreThresh, boxThresh, unClipRatio, doAngle, mostAngle, isPaddle);
        }

        private OcrResult DetectOnce(Mat src, ScaleParam scale, float boxScoreThresh, float boxThresh, float unClipRatio, bool doAngle, bool mostAngle, bool isPaddle)
        {
            Console.WriteLine("=====" + OcrType + "Start detect=====");
            long ticks = DateTime.Now.Ticks;
            List<TextBox> textBoxes = dbNet.GetTextBoxes(src, scale, boxScoreThresh, boxThresh, unClipRatio, isPaddle);
            float num = (float)(DateTime.Now.Ticks - ticks) / 10000f;
            Console.WriteLine(OcrType + $" dbNetTime({num}ms)");
            List<Mat> partImages = OcrUtils.GetPartImages(src, textBoxes);
            List<Angle> angles = angleNet.GetAngles(partImages, isPaddle, doAngle, mostAngle);
            Parallel.For(0, partImages.Count, new ParallelOptions
            {
                MaxDegreeOfParallelism = -1
            }, delegate (int index)
            {
                if (angles[index].Index == (isPaddle ? 1 : 0))
                {
                    partImages[index] = OcrUtils.MatRotateClockWise180(partImages[index]);
                }
            });
            float num2 = (float)(DateTime.Now.Ticks - ticks) / 10000f;
            Console.WriteLine(OcrType + $" angleTime({num2}ms)");
            List<TextLine> textLines = crnnNet.GetTextLines(partImages, isPaddle);
            float num3 = (float)(DateTime.Now.Ticks - ticks) / 10000f;
            Console.WriteLine(OcrType + $" txtLineTime=============({num3}ms)");
            TextBlock[] textBlocks = new TextBlock[textLines.Count];
            Parallel.For(0, textLines.Count, new ParallelOptions
            {
                MaxDegreeOfParallelism = -1
            }, delegate (int index)
            {
                TextBlock textBlock = new TextBlock
                {
                    BoxPoints = textBoxes[index].Points,
                    BoxScore = textBoxes[index].Score,
                    AngleIndex = angles[index].Index,
                    AngleScore = angles[index].Score,
                    AngleTime = angles[index].Time,
                    Text = textLines[index].Text,
                    CharScores = textLines[index].CharScores,
                    CrnnTime = textLines[index].Time,
                    BlockTime = angles[index].Time + textLines[index].Time
                };
                textBlocks[index] = textBlock;
            });
            float num4 = (float)(DateTime.Now.Ticks - ticks) / 10000f;
            Console.WriteLine($"{OcrType}-耗时{num4}ms");
            return new OcrResult
            {
                TextBlocks = textBlocks.ToList(),
                DbNetTime = num,
                DetectTime = num4,
                StrRes = string.Join("\n", textBlocks.Select((TextBlock p) => p.Text))
            };
        }
    }
}
