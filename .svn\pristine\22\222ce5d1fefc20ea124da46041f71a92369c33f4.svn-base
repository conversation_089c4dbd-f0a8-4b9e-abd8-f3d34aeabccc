using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    /// <summary>
    /// 双模式图像查看器 - 支持图文模式和文档模式
    /// 采用壳子+处理器架构，具体逻辑委托给对应的处理器
    /// </summary>
    public class DualModeImageViewer : MultiModeImageViewer
    {
        #region 模式常量
        
        public const string IMAGE_TEXT_MODE = "ImageText";
        public const string DOCUMENT_MODE = "Document";
        
        #endregion
        
        #region 私有字段
        
        private bool _enableDocumentMode = false;
        private ImageTextModeHandler _imageTextHandler;
        private DocumentModeHandler _documentHandler;
        
        #endregion
        
        #region 构造函数
        
        public DualModeImageViewer()
        {
            Margin = Padding.Empty;
            AutoScroll = true;
            TabStop = true; // 文档模式需要能够接收焦点
            AutoCenter = false;

            InitializeModeHandlers();

            // 默认图文模式
            SwitchMode(IMAGE_TEXT_MODE);
        }
        
        #endregion
        
        #region 模式切换
        
        /// <summary>
        /// 启用或禁用文档模式
        /// </summary>
        /// <param name="enable">true=文档模式，false=图文模式</param>
        public void EnableDocumentMode(bool enable)
        {
            if (_enableDocumentMode != enable)
            {
                _enableDocumentMode = enable;

                if (enable)
                {
                    // 文档模式：禁用提示功能
                    if (_imageTextHandler != null)
                    {
                        _imageTextHandler.IsShowTip = false;
                    }
                }
                else
                {
                    // 图文模式：恢复提示功能
                    if (_imageTextHandler != null)
                    {
                        _imageTextHandler.IsShowTip = true;
                    }
                }

                SwitchMode(enable ? DOCUMENT_MODE : IMAGE_TEXT_MODE);
            }
        }
        
        #endregion
        
        #region 向后兼容属性
        
        /// <summary>
        /// 是否显示提示 - 向后兼容属性，只对图文模式有效
        /// </summary>
        public bool IsShowTip
        {
            get
            {
                return _imageTextHandler?.IsShowTip ?? false;
            }
            set
            {
                if (_imageTextHandler != null)
                {
                    _imageTextHandler.IsShowTip = value;
                }
            }
        }
        
        #endregion

        #region 文档模式公共方法代理

        /// <summary>
        /// 绑定图片和文字区域 - 文档模式专用方法
        /// </summary>
        /// <param name="image">图片</param>
        /// <param name="regions">文字区域列表</param>
        public void BindImageAndTextRegions(Image image, List<TextCellInfo> regions)
        {
            if (_enableDocumentMode && _documentHandler != null)
            {
                _documentHandler.BindData(image, regions);
            }
            else if (_imageTextHandler != null)
            {
                // 图文模式下使用基类的绑定方法
                _imageTextHandler.BindData(image, regions);
            }
        }

        /// <summary>
        /// 清除选择 - 文档模式专用方法
        /// </summary>
        public void ClearSelection()
        {
            if (_enableDocumentMode && _documentHandler != null)
            {
                _documentHandler.ClearSelection();
            }
        }

        /// <summary>
        /// 获取选中的文字内容 - 文档模式专用方法
        /// </summary>
        /// <returns>选中的文字</returns>
        public string GetSelectedTextContent()
        {
            if (_enableDocumentMode && _documentHandler != null)
            {
                return _documentHandler.GetSelectedTextContent();
            }
            return string.Empty;
        }

        #endregion

        #region 向后兼容方法
        
        /// <summary>
        /// 绑定图文数据 - 向后兼容方法
        /// </summary>
        /// <param name="content">内容对象</param>
        /// <param name="isShowTxt">是否显示文字</param>
        public void BindPicTxt(UcContent content, bool isShowTxt = false)
        {
            var verticalTextJson = content.OcrContent?.result?.verticalText;
            var cells = verticalTextJson?.DeserializeJson<List<TextCellInfo>>() ?? new List<TextCellInfo>();

            if (_enableDocumentMode)
            {
                BindImageAndTextRegions(content.Image, cells);
            }
            else
            {
                _imageTextHandler?.BindPicTxt(content, isShowTxt);
            }
        }
        
        #endregion
        
        #region 抽象方法实现
        
        protected override void InitializeModeHandlers()
        {
            // 创建处理器实例
            _imageTextHandler = new ImageTextModeHandler();
            _documentHandler = new DocumentModeHandler();
            
            // 注册处理器
            RegisterHandler(IMAGE_TEXT_MODE, _imageTextHandler);
            RegisterHandler(DOCUMENT_MODE, _documentHandler);
        }
        
        #endregion
        
        #region 事件转发
        
        /// <summary>
        /// 图文模式的文字选择事件
        /// </summary>
        public event EventHandler<ImageTextModeHandler.TextCellEventArgs> ImageTextCellStateChanged
        {
            add { if (_imageTextHandler != null) _imageTextHandler.TextCellStateChanged += value; }
            remove { if (_imageTextHandler != null) _imageTextHandler.TextCellStateChanged -= value; }
        }
        
        /// <summary>
        /// 文档模式的文字选择事件
        /// </summary>
        public event EventHandler<DocumentModeHandler.TextSelectionEventArgs> DocumentTextSelectionChanged
        {
            add { if (_documentHandler != null) _documentHandler.TextSelectionChanged += value; }
            remove { if (_documentHandler != null) _documentHandler.TextSelectionChanged -= value; }
        }
        
        #endregion
    }
}
