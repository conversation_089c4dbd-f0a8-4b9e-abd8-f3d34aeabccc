using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools.WebBroswerEx
{
    /// <summary>
    /// 智能Web控件 - 自动选择最佳的网页加载器
    /// 专注于核心的网页加载功能，保持简单
    /// </summary>
    public class SmartWebControl : UserControl
    {
        private IWebLoader _loader;
        private LoadingIndicator _loadingIndicator;
        private bool _isInitialized = false;

        // 事件定义 - 保持向后兼容
        public event EventHandler<string> LoadingStarted;
        public event EventHandler<string> DocumentTitleChanged;
        public event EventHandler<LoadCompletedEventArgs> LoadingCompleted;
        public event EventHandler NavigationCompleted;
        public event EventHandler<WebBrowserNavigateErrorEventArgs> NavigationError;

        // 简化的属性
        public string DocumentTitle => _loader?.DocumentTitle ?? string.Empty;
        public string CurrentUrl => _loader?.CurrentUrl ?? string.Empty;
        public string LoaderName => _loader?.LoaderName ?? "未初始化";
        public bool IsWebView2 => _loader is WebView2Loader;
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 是否可以升级到更好的引擎
        /// </summary>
        public bool CanUpgradeEngine => !IsWebView2 && ApplicationWebManager.IsWebView2Available;

        /// <summary>
        /// 获取底层WebBrowser控件（用于兼容性）
        /// </summary>
        public WebBrowser GetWebBrowser()
        {
            if (_loader is WebBrowserLoader webBrowserLoader)
            {
                return webBrowserLoader.WebControl as WebBrowser;
            }
            return null;
        }

        public SmartWebControl()
        {
            Name = "SmartWebControl";
            Dock = DockStyle.Fill;

            SetupWindowStyles();

            // 初始化加载指示器
            _loadingIndicator = new LoadingIndicator();
            _loadingIndicator.SetParent(this);

            // 同步初始化加载器（基于ApplicationWebManager的预检测结果）
            InitializeLoader();
        }

        /// <summary>
        /// 设置窗口样式优化，解决WebView2控件闪烁问题
        /// </summary>
        private void SetupWindowStyles()
        {
            try
            {
                // 启用双缓冲
                CommonMethod.EnableDoubleBuffering(this);

                // 设置控件样式：启用AllPaintingInWmPaint和UserPaint，禁用Opaque
                CommonMethod.SetStyle(this, ControlStyles.AllPaintingInWmPaint, true);
                CommonMethod.SetStyle(this, ControlStyles.UserPaint, true);
                CommonMethod.SetStyle(this, ControlStyles.DoubleBuffer, true);
                CommonMethod.SetStyle(this, ControlStyles.ResizeRedraw, true);
                CommonMethod.SetStyle(this, ControlStyles.Opaque, false);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SmartWebControl窗口样式设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化加载器（基于ApplicationWebManager的预检测结果）
        /// </summary>
        private void InitializeLoader()
        {
            try
            {
                // 基于ApplicationWebManager的检测结果直接选择合适的加载器
                if (ApplicationWebManager.IsWebView2Available)
                {
                    _loader = new WebView2Loader();
                }
                else if (ApplicationWebManager.IsWebBrowserAvailable)
                {
                    _loader = new WebBrowserLoader();
                }
                else
                {
                    throw new InvalidOperationException("没有可用的Web加载器");
                }

                // 异步完成初始化
                _ = CompleteInitializationAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SmartWebControl初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 完成异步初始化（只处理UI相关的异步操作）
        /// </summary>
        private async Task CompleteInitializationAsync()
        {
            try
            {
                // 异步初始化加载器
                var initResult = await _loader.InitializeAsync();

                if (initResult && _loader.WebControl != null)
                {
                    // 绑定事件
                    _loader.LoadingStarted += OnLoadingStarted;
                    _loader.LoadingCompleted += OnLoadingCompleted;
                    _loader.TitleChanged += OnTitleChanged;

                    CommonMethod.DetermineCall(this, delegate
                    {
                        Controls.Add(_loader.WebControl);
                        _isInitialized = true;
                    });

                    // 如果当前使用的不是WebView2，注册以接收升级通知
                    if (!IsWebView2)
                    {
                        ApplicationWebManager.RegisterControlForUpgrade(this);
                    }
                }
            }
            catch (Exception ex)
            {
                // 静默处理初始化异常
            }
        }

        /// <summary>
        /// 加载开始事件处理
        /// </summary>
        private void OnLoadingStarted(object sender, string url)
        {
            _loadingIndicator?.Show();
            LoadingStarted?.Invoke(this, url);
        }

        /// <summary>
        /// 加载完成事件处理
        /// </summary>
        private void OnLoadingCompleted(object sender, LoadCompletedEventArgs e)
        {
            _loadingIndicator?.Hide();
            LoadingCompleted?.Invoke(this, e);

            // 触发兼容事件
            if (e.IsSuccess) NavigationCompleted?.Invoke(this, EventArgs.Empty);
            else NavigationError?.Invoke(this, new WebBrowserNavigateErrorEventArgs(e.Url, -1));
        }

        /// <summary>
        /// 标题变化事件处理
        /// </summary>
        private void OnTitleChanged(object sender, string title)
        {
            DocumentTitleChanged?.Invoke(this, title);

            // 可选：更新窗体标题（可通过属性控制）
            UpdateParentFormTitle(title);
        }

        /// <summary>
        /// 更新父窗体标题（提取为独立方法，便于控制）
        /// </summary>
        private void UpdateParentFormTitle(string title)
        {
            if (string.IsNullOrEmpty(title)) return;
            try
            {
                var form = FindForm();
                if (form?.IsDisposed == false && form.Visible) form.Text = title;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新窗体标题失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载网页 - 核心功能
        /// </summary>
        public async Task<bool> LoadAsync(string url, string postData = null)
        {
            if (string.IsNullOrWhiteSpace(url))
            {
                throw new ArgumentException("URL不能为空", nameof(url));
            }

            // 立即显示加载指示器，避免空白等待
            _loadingIndicator?.Show();

            try
            {
                // 检查加载器是否可用
                if (_loader == null)
                {
                    return false;
                }

                // 如果还在初始化中，等待一小段时间
                if (!_isInitialized)
                {
                    await WaitForInitializationAsync();
                }

                if (!_isInitialized)
                {
                    return false;
                }

                return await _loader.LoadAsync(url, postData);
            }
            catch
            {
                // 如果加载失败，隐藏加载指示器
                _loadingIndicator?.Hide();
                return false;
            }
        }

        /// <summary>
        /// 等待初始化完成（仅在必要时使用）
        /// </summary>
        private async Task WaitForInitializationAsync()
        {
            const int maxWaitMs = 3000, checkIntervalMs = 100;
            for (int waitedMs = 0; !_isInitialized && waitedMs < maxWaitMs; waitedMs += checkIntervalMs)
                await Task.Delay(checkIntervalMs);
        }

        /// <summary>
        /// 尝试升级到更好的引擎（如果可用）
        /// </summary>
        public async Task<bool> TryUpgradeEngineAsync()
        {
            if (!CanUpgradeEngine)
            {
                Console.WriteLine($"SmartWebControl: 无法升级引擎，当前: {LoaderName}, WebView2可用: {ApplicationWebManager.IsWebView2Available}");
                return false;
            }

            try
            {
                Console.WriteLine($"SmartWebControl: 开始升级引擎，从 {LoaderName} 到 WebView2");

                // 保存当前状态
                var currentUrl = CurrentUrl;
                var oldLoader = _loader;
                var wasInitialized = _isInitialized;

                // 准备切换
                UnbindLoaderEvents(_loader);
                RemoveLoaderControl(_loader);
                _loader = null;
                _isInitialized = false;

                // 创建新的WebView2加载器
                var newLoader = new WebView2Loader();
                var initResult = await newLoader.InitializeAsync();

                if (initResult && newLoader.WebControl != null)
                {
                    // 成功升级
                    SetupNewLoader(newLoader);
                    Console.WriteLine($"SmartWebControl: 引擎升级成功，新引擎: {LoaderName}");

                    // 重新加载之前的页面
                    await ReloadPreviousPage(currentUrl);

                    // 清理旧加载器
                    DisposeOldLoader(oldLoader);
                    return true;
                }
                else
                {
                    // 升级失败，回滚
                    Console.WriteLine("SmartWebControl: WebView2加载器初始化失败，回滚到原引擎");
                    RollbackToOldLoader(oldLoader, wasInitialized);
                    newLoader?.Dispose();
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SmartWebControl: 引擎升级异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 解绑加载器事件
        /// </summary>
        private void UnbindLoaderEvents(IWebLoader loader)
        {
            if (loader == null) return;

            loader.LoadingStarted -= OnLoadingStarted;
            loader.LoadingCompleted -= OnLoadingCompleted;
            loader.TitleChanged -= OnTitleChanged;
        }

        /// <summary>
        /// 移除加载器控件
        /// </summary>
        private void RemoveLoaderControl(IWebLoader loader)
        {
            if (loader?.WebControl != null && Controls.Contains(loader.WebControl))
            {
                Controls.Remove(loader.WebControl);
            }
        }

        /// <summary>
        /// 设置新的加载器
        /// </summary>
        private void SetupNewLoader(IWebLoader newLoader)
        {
            // 绑定事件
            newLoader.LoadingStarted += OnLoadingStarted;
            newLoader.LoadingCompleted += OnLoadingCompleted;
            newLoader.TitleChanged += OnTitleChanged;

            // 添加控件
            Controls.Add(newLoader.WebControl);

            _loader = newLoader;
            _isInitialized = true;
        }

        /// <summary>
        /// 重新加载之前的页面
        /// </summary>
        private async Task ReloadPreviousPage(string currentUrl)
        {
            if (!string.IsNullOrEmpty(currentUrl) && currentUrl != "about:blank")
            {
                Console.WriteLine($"SmartWebControl: 重新加载页面: {currentUrl}");
                _ = LoadAsync(currentUrl);
            }
        }

        /// <summary>
        /// 回滚到旧的加载器
        /// </summary>
        private void RollbackToOldLoader(IWebLoader oldLoader, bool wasInitialized)
        {
            if (oldLoader != null)
            {
                _loader = oldLoader;
                _isInitialized = wasInitialized;

                // 重新绑定事件和控件
                SetupNewLoader(oldLoader);
            }
        }

        /// <summary>
        /// 安全释放旧的加载器
        /// </summary>
        private void DisposeOldLoader(IWebLoader oldLoader)
        {
            try
            {
                oldLoader?.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SmartWebControl: 清理旧加载器时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查并自动升级引擎（如果需要且可能）
        /// </summary>
        public async Task<bool> CheckAndAutoUpgradeAsync()
        {
            if (CanUpgradeEngine)
            {
                Console.WriteLine("SmartWebControl: 检测到可以升级到WebView2，开始自动升级");
                return await TryUpgradeEngineAsync();
            }
            return false;
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                try
                {
                    // 取消注册升级通知
                    ApplicationWebManager.UnregisterControlForUpgrade(this);

                    _loadingIndicator?.Dispose();
                    _loadingIndicator = null;

                    _loader?.Dispose();
                    _loader = null;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"SmartWebControl释放资源失败: {ex.Message}");
                }
            }
            base.Dispose(disposing);
        }
    }
}