﻿using System.ComponentModel;

namespace OCRTools.Common.SNTP
{
    /// <summary>
    ///     A class that holds the information needed to connect to a remote NTP/SNTP server.
    /// </summary>
    [TypeConverter(typeof(ExpandableObjectConverter))]
    public class RemoteSntpServer
    {
        #region Methods

        /// <summary>
        ///     Returns the host name, IP address and port number of this server.
        /// </summary>
        /// <returns>The host name, IP address and port number of this server.</returns>
        public override string ToString()
        {
            return string.Format("{0}:{1}",
                HostNameOrAddress, Port);
        }

        #endregion Methods 

        #region Fields 

        private string _hostNameOrAddress;
        private int _port;

        /// <summary>
        ///     A remote NTP/SNTP server configured with the default values.
        /// </summary>
        public static readonly RemoteSntpServer Default = new RemoteSntpServer();

        /// <summary>
        ///     The default server host name.
        /// </summary>
        public const string DefaultHostName = "time.nist.gov";

        /// <summary>
        ///     The dafault port number for a NTP/SNTP server.
        /// </summary>
        public const int DefaultPort = 123;

        /// <summary>
        ///     The Microsoft (Redmond, Washington) RemoteSNTPServer (time-nw.nist.gov).
        /// </summary>
        public static readonly RemoteSntpServer Microsoft = new RemoteSntpServer("time-nw.nist.gov");

        /// <summary>
        ///     NTL UK (Virgin Media).
        /// </summary>
        public static readonly RemoteSntpServer Ntl = new RemoteSntpServer("time.cableol.net");

        /// <summary>
        ///     The Microsoft Windows RemoteSNTPServer (time.windows.com).
        /// </summary>
        public static readonly RemoteSntpServer Windows = new RemoteSntpServer("time.windows.com");

        #endregion Fields 

        #region Constructors 

        /// <summary>
        ///     Creates a new instance of a remote NTP/SNTP server.
        /// </summary>
        /// <param name="hostNameOrAddress">The host name or address of the server.</param>
        /// <param name="port">The port to use (normally 123).</param>
        public RemoteSntpServer(string hostNameOrAddress, int port)
        {
            HostNameOrAddress = hostNameOrAddress;
            Port = port;
        }

        /// <summary>
        ///     Creates a new instance of a remote NTP/SNTP server.
        /// </summary>
        /// <param name="hostNameOrAddress">The host name or address of the server.</param>
        public RemoteSntpServer(string hostNameOrAddress)
            : this(hostNameOrAddress, DefaultPort)
        {
        }

        /// <summary>
        ///     Creates a new instance of a remote NTP/SNTP server.
        /// </summary>
        public RemoteSntpServer()
            : this(DefaultHostName, DefaultPort)
        {
        }

        #endregion Constructors 

        #region Properties 

        /// <summary>
        ///     Gets or sets the host name or address of the server.
        /// </summary>
        [Description("The host name or address of the server.")]
        [DefaultValue(DefaultHostName)]
        [NotifyParentProperty(true)]
        public string HostNameOrAddress
        {
            get => _hostNameOrAddress;
            set
            {
                value = value.Trim();
                if (string.IsNullOrEmpty(value))
                    value = DefaultHostName;
                _hostNameOrAddress = value;
            }
        }

        /// <summary>
        ///     Gets or sets the port number that this server uses.
        /// </summary>
        [Description("The port number that this server uses.")]
        [DefaultValue(DefaultPort)]
        [NotifyParentProperty(true)]
        public int Port
        {
            get => _port;
            set
            {
                if (value >= 0 && value <= 65535)
                    _port = value;
                else
                    _port = DefaultPort;
            }
        }

        #endregion Properties 
    }
}