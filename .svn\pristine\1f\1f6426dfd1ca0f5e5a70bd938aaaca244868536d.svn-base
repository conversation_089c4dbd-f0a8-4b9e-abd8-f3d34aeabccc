using System.Text;
using UtfUnknown.Core.Probers.MultiByte;
using UtfUnknown.Core.Probers.MultiByte.Chinese;

namespace UtfUnknown.Core.Probers
{
    public class MbcsGroupProber : CharsetProber
    {
        private readonly bool[] _isActive = new bool[4];

        private readonly CharsetProber[] _probers = new CharsetProber[4];

        private int _activeNum;

        private int _bestGuess;

        public MbcsGroupProber()
        {
            _probers[0] = new Utf8Prober();
            _probers[1] = new Gb18030Prober();
            _probers[2] = new Big5Prober();
            _probers[3] = new EuctwProber();
            Reset();
        }

        public override string GetCharsetName()
        {
            if (_bestGuess == -1)
            {
                GetConfidence();
                if (_bestGuess == -1) _bestGuess = 0;
            }

            return _probers[_bestGuess].GetCharsetName();
        }

        public override void Reset()
        {
            _activeNum = 0;
            for (var i = 0; i < _probers.Length; i++)
                if (_probers[i] != null)
                {
                    _probers[i].Reset();
                    _isActive[i] = true;
                    _activeNum++;
                }
                else
                {
                    _isActive[i] = false;
                }

            _bestGuess = -1;
            state = ProbingState.Detecting;
        }

        public override ProbingState HandleData(byte[] buf, int offset, int len)
        {
            var array = new byte[len];
            var len2 = 0;
            var flag = true;
            var num = offset + len;
            for (var i = offset; i < num; i++)
                if ((buf[i] & 0x80) != 0)
                {
                    array[len2++] = buf[i];
                    flag = true;
                }
                else if (flag)
                {
                    array[len2++] = buf[i];
                    flag = false;
                }

            for (var j = 0; j < _probers.Length; j++)
            {
                if (!_isActive[j]) continue;
                switch (_probers[j].HandleData(array, 0, len2))
                {
                    case ProbingState.FoundIt:
                        _bestGuess = j;
                        state = ProbingState.FoundIt;
                        break;
                    case ProbingState.NotMe:
                        _isActive[j] = false;
                        _activeNum--;
                        if (_activeNum > 0) continue;
                        state = ProbingState.NotMe;
                        break;
                    default:
                        continue;
                }

                break;
            }

            return state;
        }

        public override float GetConfidence(StringBuilder status = null)
        {
            var num = 0f;
            switch (state)
            {
                case ProbingState.FoundIt:
                    return 0.99f;
                case ProbingState.NotMe:
                    return 0.01f;
                default:
                    {
                        status?.AppendLine("Get confidence:");
                        for (var i = 0; i < 4; i++)
                            if (_isActive[i])
                            {
                                var confidence = _probers[i].GetConfidence();
                                if (num < confidence)
                                {
                                    num = confidence;
                                    _bestGuess = i;
                                    status?.AppendLine(
                                        $"-- new match found: confidence {num}, index {_bestGuess}, charset {_probers[i].GetCharsetName()}.");
                                }
                            }

                        status?.AppendLine("Get confidence done.");
                        return num;
                    }
            }
        }

        public override string DumpStatus()
        {
            var stringBuilder = new StringBuilder();
            var confidence = GetConfidence(stringBuilder);
            stringBuilder.AppendLine(" MBCS Group Prober --------begin status");
            for (var i = 0; i < 4; i++)
                if (_probers[i] != null)
                {
                    if (!_isActive[i])
                    {
                        stringBuilder.AppendLine(" MBCS inactive: " + _probers[i].GetCharsetName() +
                                                 " (i.e. confidence is too low).");
                        continue;
                    }

                    var confidence2 = _probers[i].GetConfidence();
                    stringBuilder.AppendLine($" MBCS {confidence2}: [{_probers[i].GetCharsetName()}]");
                    stringBuilder.AppendLine(_probers[i].DumpStatus());
                }

            stringBuilder.AppendLine(
                $" MBCS Group found best match [{_probers[_bestGuess].GetCharsetName()}] confidence {confidence}.");
            return stringBuilder.ToString();
        }
    }
}