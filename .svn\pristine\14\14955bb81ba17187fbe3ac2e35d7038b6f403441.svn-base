﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace OCRTools.UserControlEx
{
    public enum BlendStyle
    {
        Normal,
        Glass
    }

    [ToolboxItem(false)]
    public abstract class TabStyleProvider : Component
    {
        private Point _Padding;

        private bool _HotTrack;

        private TabStyle _Style = TabStyle.Default;

        private ContentAlignment _ImageAlign;

        private int _Radius = 1;

        private int _Overlap;

        private bool _FocusTrack;

        private float _Opacity = 1f;

        private bool _ShowTabCloser;

        private bool _SelectedTabIsLarger;

        private BlendStyle _BlendStyle;

        private Color _BorderColorDisabled = Color.Empty;

        private Color _BorderColorFocused = Color.Empty;

        private Color _BorderColorHighlighted = Color.Empty;

        private Color _BorderColorSelected = Color.Empty;

        private Color _BorderColorUnselected = Color.Empty;

        private Color _CloserColorFocused = SystemColors.ControlDark;

        private Color _CloserColorFocusedActive = SystemColors.ControlDark;

        private Color _CloserColorSelected = SystemColors.ControlDark;

        private Color _CloserColorSelectedActive = SystemColors.ControlDark;

        private Color _CloserColorHighlighted = SystemColors.ControlDark;

        private Color _CloserColorHighlightedActive = SystemColors.ControlDark;

        private Color _CloserColorUnselected = Color.Empty;

        private Color _CloserButtonFillColorFocused = Color.Empty;

        private Color _CloserButtonFillColorFocusedActive = Color.Empty;

        private Color _CloserButtonFillColorSelected = Color.Empty;

        private Color _CloserButtonFillColorSelectedActive = Color.Empty;

        private Color _CloserButtonFillColorHighlighted = Color.Empty;

        private Color _CloserButtonFillColorHighlightedActive = Color.Empty;

        private Color _CloserButtonFillColorUnselected = Color.Empty;

        private Color _CloserButtonOutlineColorFocused = Color.Empty;

        private Color _CloserButtonOutlineColorFocusedActive = Color.Empty;

        private Color _CloserButtonOutlineColorSelected = Color.Empty;

        private Color _CloserButtonOutlineColorSelectedActive = Color.Empty;

        private Color _CloserButtonOutlineColorHighlighted = Color.Empty;

        private Color _CloserButtonOutlineColorHighlightedActive = Color.Empty;

        private Color _CloserButtonOutlineColorUnselected = Color.Empty;

        private Color _FocusColor = Color.Empty;

        private Color _PageBackgroundColorDisabled = Color.Empty;

        private Color _PageBackgroundColorFocused = Color.Empty;

        private Color _PageBackgroundColorHighlighted = Color.Empty;

        private Color _PageBackgroundColorSelected = Color.Empty;

        private Color _PageBackgroundColorUnselected = Color.Empty;

        private Color _TabColorDisabled1 = Color.Empty;

        private Color _TabColorDisabled2 = Color.Empty;

        private Color _TabColorFocused1 = Color.Empty;

        private Color _TabColorFocused2 = Color.Empty;

        private Color _TabColorSelected1 = Color.Empty;

        private Color _TabColorSelected2 = Color.Empty;

        private Color _TabColorUnSelected1 = Color.Empty;

        private Color _TabColorUnSelected2 = Color.Empty;

        private Color _TabColorHighLighted1 = Color.Empty;

        private Color _TabColorHighLighted2 = Color.Empty;

        private Color _TextColorDisabled = Color.Empty;

        private Color _TextColorFocused = Color.Empty;

        private Color _TextColorHighlighted = Color.Empty;

        private Color _TextColorSelected = Color.Empty;

        private Color _TextColorUnselected = Color.Empty;

        private Padding _TabPageMargin = new Padding(1);

        private int _TabPageRadius;

        protected TabControlExtra TabControl { get; private set; }

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public TabStyle DisplayStyle
        {
            get
            {
                return _Style;
            }
            set
            {
                _Style = value;
            }
        }

        public BlendStyle BlendStyle
        {
            get
            {
                return _BlendStyle;
            }
            set
            {
                _BlendStyle = value;
                TabControl.Invalidate();
            }
        }

        [Category("Appearance")]
        public ContentAlignment ImageAlign
        {
            get
            {
                return _ImageAlign;
            }
            set
            {
                _ImageAlign = value;
            }
        }

        [Category("Appearance")]
        public Point Padding
        {
            get
            {
                return _Padding;
            }
            set
            {
                _Padding = value;
                if (_ShowTabCloser)
                {
                    if (value.X + _Radius / 2 < -15)
                    {
                        ((TabControl)TabControl).Padding = new Point(0, value.Y);
                    }
                    else
                    {
                        ((TabControl)TabControl).Padding = new Point(value.X + _Radius + 12, value.Y);
                    }
                }
                else if (value.X + _Radius / 2 < 1)
                {
                    ((TabControl)TabControl).Padding = new Point(0, value.Y);
                }
                else
                {
                    ((TabControl)TabControl).Padding = new Point(value.X + _Radius, value.Y);
                }
            }
        }

        [Category("Appearance")]
        [DefaultValue(1)]
        [Browsable(true)]
        public int Radius
        {
            get
            {
                return _Radius;
            }
            set
            {
                if (value < 1)
                {
                    throw new ArgumentException("The radius cannot be less than 1", "value");
                }
                _Radius = value;
                Padding = _Padding;
            }
        }

        [Category("Appearance")]
        public int Overlap
        {
            get
            {
                return _Overlap;
            }
            set
            {
                if (value < 0)
                {
                    throw new ArgumentException("The tabs cannot have a negative overlap", "value");
                }
                _Overlap = value;
            }
        }

        [Category("Appearance")]
        public bool FocusTrack
        {
            get
            {
                return _FocusTrack;
            }
            set
            {
                _FocusTrack = value;
            }
        }

        [Category("Appearance")]
        public bool HotTrack
        {
            get
            {
                return _HotTrack;
            }
            set
            {
                _HotTrack = value;
                ((TabControl)TabControl).HotTrack = value;
            }
        }

        [Category("Appearance")]
        public bool SelectedTabIsLarger
        {
            get
            {
                return _SelectedTabIsLarger;
            }
            set
            {
                _SelectedTabIsLarger = value;
                TabControl.Invalidate();
            }
        }

        [Category("Appearance")]
        public bool ShowTabCloser
        {
            get
            {
                return _ShowTabCloser;
            }
            set
            {
                _ShowTabCloser = value;
                Padding = _Padding;
            }
        }

        [Category("Appearance")]
        public float Opacity
        {
            get
            {
                return _Opacity;
            }
            set
            {
                if (value < 0f)
                {
                    throw new ArgumentException("The opacity must be between 0 and 1", "value");
                }
                if (value > 1f)
                {
                    throw new ArgumentException("The opacity must be between 0 and 1", "value");
                }
                _Opacity = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color BorderColorDisabled
        {
            get
            {
                if (_BorderColorDisabled.IsEmpty)
                {
                    return SystemColors.ControlLight;
                }
                return _BorderColorDisabled;
            }
            set
            {
                if (value.Equals(SystemColors.ControlLight))
                {
                    _BorderColorDisabled = Color.Empty;
                }
                else
                {
                    _BorderColorDisabled = value;
                }
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color BorderColorFocused
        {
            get
            {
                if (_BorderColorFocused.IsEmpty)
                {
                    return ThemedColors.ToolBorder;
                }
                return _BorderColorFocused;
            }
            set
            {
                if (!value.Equals(BorderColorFocused))
                {
                    if (value.Equals(ThemedColors.ToolBorder))
                    {
                        _BorderColorFocused = Color.Empty;
                    }
                    else
                    {
                        _BorderColorFocused = value;
                    }
                }
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color BorderColorHighlighted
        {
            get
            {
                if (_BorderColorHighlighted.IsEmpty)
                {
                    return SystemColors.ControlDark;
                }
                return _BorderColorHighlighted;
            }
            set
            {
                if (value.Equals(SystemColors.ControlDark))
                {
                    _BorderColorHighlighted = Color.Empty;
                }
                else
                {
                    _BorderColorHighlighted = value;
                }
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color BorderColorSelected
        {
            get
            {
                if (_BorderColorSelected.IsEmpty)
                {
                    return SystemColors.ControlDark;
                }
                return _BorderColorSelected;
            }
            set
            {
                if (value.Equals(SystemColors.ControlDark))
                {
                    _BorderColorSelected = Color.Empty;
                }
                else
                {
                    _BorderColorSelected = value;
                }
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color BorderColorUnselected
        {
            get
            {
                if (_BorderColorUnselected.IsEmpty)
                {
                    return SystemColors.ControlDark;
                }
                return _BorderColorUnselected;
            }
            set
            {
                if (value.Equals(SystemColors.ControlDark))
                {
                    _BorderColorUnselected = Color.Empty;
                }
                else
                {
                    _BorderColorUnselected = value;
                }
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color PageBackgroundColorDisabled
        {
            get
            {
                if (_PageBackgroundColorDisabled.IsEmpty)
                {
                    return SystemColors.Control;
                }
                return _PageBackgroundColorDisabled;
            }
            set
            {
                _PageBackgroundColorDisabled = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color PageBackgroundColorFocused
        {
            get
            {
                if (_PageBackgroundColorFocused.IsEmpty)
                {
                    return SystemColors.ControlLight;
                }
                return _PageBackgroundColorFocused;
            }
            set
            {
                _PageBackgroundColorFocused = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color PageBackgroundColorHighlighted
        {
            get
            {
                if (_PageBackgroundColorHighlighted.IsEmpty)
                {
                    return PageBackgroundColorUnselected;
                }
                return _PageBackgroundColorHighlighted;
            }
            set
            {
                _PageBackgroundColorHighlighted = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color PageBackgroundColorSelected
        {
            get
            {
                if (_PageBackgroundColorSelected.IsEmpty)
                {
                    return SystemColors.ControlLightLight;
                }
                return _PageBackgroundColorSelected;
            }
            set
            {
                _PageBackgroundColorSelected = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color PageBackgroundColorUnselected
        {
            get
            {
                if (_PageBackgroundColorUnselected.IsEmpty)
                {
                    return SystemColors.Control;
                }
                return _PageBackgroundColorUnselected;
            }
            set
            {
                _PageBackgroundColorUnselected = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TabColorDisabled1
        {
            get
            {
                if (_TabColorDisabled1.IsEmpty)
                {
                    return PageBackgroundColorDisabled;
                }
                return _TabColorDisabled1;
            }
            set
            {
                _TabColorDisabled1 = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TabColorDisabled2
        {
            get
            {
                if (_TabColorDisabled2.IsEmpty)
                {
                    return TabColorDisabled1;
                }
                return _TabColorDisabled2;
            }
            set
            {
                _TabColorDisabled2 = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TabColorFocused1
        {
            get
            {
                if (_TabColorFocused1.IsEmpty)
                {
                    return PageBackgroundColorFocused;
                }
                return _TabColorFocused1;
            }
            set
            {
                _TabColorFocused1 = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TabColorFocused2
        {
            get
            {
                if (_TabColorFocused2.IsEmpty)
                {
                    return TabColorFocused1;
                }
                return _TabColorFocused2;
            }
            set
            {
                _TabColorFocused2 = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TabColorSelected1
        {
            get
            {
                if (_TabColorSelected1.IsEmpty)
                {
                    return PageBackgroundColorSelected;
                }
                return _TabColorSelected1;
            }
            set
            {
                _TabColorSelected1 = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TabColorSelected2
        {
            get
            {
                if (_TabColorSelected2.IsEmpty)
                {
                    return TabColorSelected1;
                }
                return _TabColorSelected2;
            }
            set
            {
                _TabColorSelected2 = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TabColorUnSelected1
        {
            get
            {
                if (_TabColorUnSelected1.IsEmpty)
                {
                    return PageBackgroundColorUnselected;
                }
                return _TabColorUnSelected1;
            }
            set
            {
                _TabColorUnSelected1 = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TabColorUnSelected2
        {
            get
            {
                if (_TabColorUnSelected2.IsEmpty)
                {
                    return TabColorUnSelected1;
                }
                return _TabColorUnSelected2;
            }
            set
            {
                _TabColorUnSelected2 = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TabColorHighLighted1
        {
            get
            {
                if (_TabColorHighLighted1.IsEmpty)
                {
                    return PageBackgroundColorHighlighted;
                }
                return _TabColorHighLighted1;
            }
            set
            {
                _TabColorHighLighted1 = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TabColorHighLighted2
        {
            get
            {
                if (_TabColorHighLighted2.IsEmpty)
                {
                    return TabColorHighLighted1;
                }
                return _TabColorHighLighted2;
            }
            set
            {
                _TabColorHighLighted2 = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TextColorDisabled
        {
            get
            {
                if (_TextColorUnselected.IsEmpty)
                {
                    return SystemColors.ControlDark;
                }
                return _TextColorDisabled;
            }
            set
            {
                if (value.Equals(SystemColors.ControlDark))
                {
                    _TextColorDisabled = Color.Empty;
                }
                else
                {
                    _TextColorDisabled = value;
                }
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TextColorFocused
        {
            get
            {
                if (_TextColorFocused.IsEmpty)
                {
                    return TextColorSelected;
                }
                return _TextColorFocused;
            }
            set
            {
                _TextColorFocused = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TextColorHighlighted
        {
            get
            {
                if (_TextColorHighlighted.IsEmpty)
                {
                    return TextColorUnselected;
                }
                return _TextColorHighlighted;
            }
            set
            {
                _TextColorHighlighted = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TextColorSelected
        {
            get
            {
                if (_TextColorSelected.IsEmpty)
                {
                    return SystemColors.ControlText;
                }
                return _TextColorSelected;
            }
            set
            {
                if (value.Equals(SystemColors.ControlText))
                {
                    _TextColorSelected = Color.Empty;
                }
                else
                {
                    _TextColorSelected = value;
                }
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "")]
        public Color TextColorUnselected
        {
            get
            {
                if (_TextColorUnselected.IsEmpty)
                {
                    return SystemColors.ControlText;
                }
                return _TextColorUnselected;
            }
            set
            {
                if (value.Equals(SystemColors.ControlText))
                {
                    _TextColorUnselected = Color.Empty;
                }
                else
                {
                    _TextColorUnselected = value;
                }
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Color), "Orange")]
        public Color FocusColor
        {
            get
            {
                return _FocusColor;
            }
            set
            {
                _FocusColor = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "ControlDark")]
        public Color CloserColorFocused
        {
            get
            {
                return _CloserColorFocused;
            }
            set
            {
                _CloserColorFocused = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "ControlDark")]
        public Color CloserColorFocusedActive
        {
            get
            {
                return _CloserColorFocusedActive;
            }
            set
            {
                _CloserColorFocusedActive = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "ControlDark")]
        public Color CloserColorSelected
        {
            get
            {
                return _CloserColorSelected;
            }
            set
            {
                _CloserColorSelected = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "ControlDark")]
        public Color CloserColorSelectedActive
        {
            get
            {
                return _CloserColorSelectedActive;
            }
            set
            {
                _CloserColorSelectedActive = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "ControlDark")]
        public Color CloserColorHighlighted
        {
            get
            {
                return _CloserColorHighlighted;
            }
            set
            {
                _CloserColorHighlighted = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "ControlDark")]
        public Color CloserColorHighlightedActive
        {
            get
            {
                return _CloserColorHighlightedActive;
            }
            set
            {
                _CloserColorHighlightedActive = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserColorUnselected
        {
            get
            {
                return _CloserColorUnselected;
            }
            set
            {
                _CloserColorUnselected = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserButtonFillColorFocused
        {
            get
            {
                return _CloserButtonFillColorFocused;
            }
            set
            {
                _CloserButtonFillColorFocused = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserButtonFillColorFocusedActive
        {
            get
            {
                return _CloserButtonFillColorFocusedActive;
            }
            set
            {
                _CloserButtonFillColorFocusedActive = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserButtonFillColorSelected
        {
            get
            {
                return _CloserButtonFillColorSelected;
            }
            set
            {
                _CloserButtonFillColorSelected = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserButtonFillColorSelectedActive
        {
            get
            {
                return _CloserButtonFillColorSelectedActive;
            }
            set
            {
                _CloserButtonFillColorSelectedActive = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserButtonFillColorHighlighted
        {
            get
            {
                return _CloserButtonFillColorHighlighted;
            }
            set
            {
                _CloserButtonFillColorHighlighted = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserButtonFillColorHighlightedActive
        {
            get
            {
                return _CloserButtonFillColorHighlightedActive;
            }
            set
            {
                _CloserButtonFillColorHighlightedActive = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserButtonFillColorUnselected
        {
            get
            {
                return _CloserButtonFillColorUnselected;
            }
            set
            {
                _CloserButtonFillColorUnselected = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserButtonOutlineColorFocused
        {
            get
            {
                return _CloserButtonOutlineColorFocused;
            }
            set
            {
                _CloserButtonOutlineColorFocused = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserButtonOutlineColorFocusedActive
        {
            get
            {
                return _CloserButtonOutlineColorFocusedActive;
            }
            set
            {
                _CloserButtonOutlineColorFocusedActive = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserButtonOutlineColorSelected
        {
            get
            {
                return _CloserButtonOutlineColorSelected;
            }
            set
            {
                _CloserButtonOutlineColorSelected = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserButtonOutlineColorSelectedActive
        {
            get
            {
                return _CloserButtonOutlineColorSelectedActive;
            }
            set
            {
                _CloserButtonOutlineColorSelectedActive = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserButtonOutlineColorHighlighted
        {
            get
            {
                return _CloserButtonOutlineColorHighlighted;
            }
            set
            {
                _CloserButtonOutlineColorHighlighted = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserButtonOutlineColorHighlightedActive
        {
            get
            {
                return _CloserButtonOutlineColorHighlightedActive;
            }
            set
            {
                _CloserButtonOutlineColorHighlightedActive = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(SystemColors), "Empty")]
        public Color CloserButtonOutlineColorUnselected
        {
            get
            {
                return _CloserButtonOutlineColorUnselected;
            }
            set
            {
                _CloserButtonOutlineColorUnselected = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(Padding), "{1,1,1,1}")]
        public Padding TabPageMargin
        {
            get
            {
                return _TabPageMargin;
            }
            set
            {
                if (value.Left < 0)
                {
                    value.Left = 0;
                }
                if (value.Right < 0)
                {
                    value.Right = 0;
                }
                if (value.Top < 0)
                {
                    value.Top = 0;
                }
                if (value.Bottom < 0)
                {
                    value.Bottom = 0;
                }
                if (value.Left > 4)
                {
                    value.Left = 4;
                }
                if (value.Right > 4)
                {
                    value.Right = 4;
                }
                if (value.Top > 4)
                {
                    value.Top = 4;
                }
                if (value.Bottom > 4)
                {
                    value.Bottom = 4;
                }
                _TabPageMargin = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(int), "0")]
        public int TabPageRadius
        {
            get
            {
                return _TabPageRadius;
            }
            set
            {
                if (value < 0)
                {
                    value = 0;
                }
                if (value > 4)
                {
                    value = 4;
                }
                _TabPageRadius = value;
            }
        }

        protected TabStyleProvider(TabControlExtra tabControl)
        {
            TabControl = tabControl;
            _FocusColor = Color.Orange;
            if (TabControl.RightToLeftLayout)
            {
                _ImageAlign = ContentAlignment.MiddleRight;
            }
            else
            {
                _ImageAlign = ContentAlignment.MiddleLeft;
            }
            HotTrack = true;
            Padding = new Point(6, 3);
        }

        public static TabStyleProvider CreateProvider(TabControlExtra tabControl)
        {
            TabStyleProvider tabStyleProvider;
            switch (tabControl.DisplayStyle)
            {
                case TabStyle.None:
                    tabStyleProvider = new TabStyleNoneProvider(tabControl);
                    break;
                ////case TabStyle.Angled:
                ////    tabStyleProvider = new TabStyleAngledProvider(tabControl);
                ////    break;
                case TabStyle.Rounded:
                    tabStyleProvider = new TabStyleRoundedProvider(tabControl);
                    break;
                case TabStyle.VisualStudio:
                    tabStyleProvider = new TabStyleVisualStudioProvider(tabControl);
                    break;
                case TabStyle.Chrome:
                    tabStyleProvider = new TabStyleChromeProvider(tabControl);
                    break;
                case TabStyle.IE8:
                    tabStyleProvider = new TabStyleIE8Provider(tabControl);
                    break;
                case TabStyle.VS2010:
                    tabStyleProvider = new TabStyleVS2010Provider(tabControl);
                    break;
                ////case TabStyle.Rectangular:
                ////    tabStyleProvider = new TabStyleRectangularProvider(tabControl);
                ////    break;
                case TabStyle.VS2012:
                    tabStyleProvider = new TabStyleVS2012Provider(tabControl);
                    break;
                default:
                    tabStyleProvider = new TabStyleDefaultProvider(tabControl);
                    break;
            }
            tabStyleProvider._Style = tabControl.DisplayStyle;
            return tabStyleProvider;
        }

        public virtual void AddTabBorder(GraphicsPath path, Rectangle tabBounds)
        {
            switch (TabControl.Alignment)
            {
                case TabAlignment.Top:
                    path.AddLine(tabBounds.X, tabBounds.Bottom, tabBounds.X, tabBounds.Y);
                    path.AddLine(tabBounds.X, tabBounds.Y, tabBounds.Right, tabBounds.Y);
                    path.AddLine(tabBounds.Right, tabBounds.Y, tabBounds.Right, tabBounds.Bottom);
                    break;
                case TabAlignment.Bottom:
                    path.AddLine(tabBounds.Right, tabBounds.Y, tabBounds.Right, tabBounds.Bottom);
                    path.AddLine(tabBounds.Right, tabBounds.Bottom, tabBounds.X, tabBounds.Bottom);
                    path.AddLine(tabBounds.X, tabBounds.Bottom, tabBounds.X, tabBounds.Y);
                    break;
                case TabAlignment.Left:
                    path.AddLine(tabBounds.Right, tabBounds.Bottom, tabBounds.X, tabBounds.Bottom);
                    path.AddLine(tabBounds.X, tabBounds.Bottom, tabBounds.X, tabBounds.Y);
                    path.AddLine(tabBounds.X, tabBounds.Y, tabBounds.Right, tabBounds.Y);
                    break;
                case TabAlignment.Right:
                    path.AddLine(tabBounds.X, tabBounds.Y, tabBounds.Right, tabBounds.Y);
                    path.AddLine(tabBounds.Right, tabBounds.Y, tabBounds.Right, tabBounds.Bottom);
                    path.AddLine(tabBounds.Right, tabBounds.Bottom, tabBounds.X, tabBounds.Bottom);
                    break;
            }
        }

        public virtual Rectangle GetTabRect(Rectangle baseTabRect, Rectangle pageBounds, bool tabIsSelected)
        {
            Rectangle tabBounds = baseTabRect;
            switch (TabControl.Alignment)
            {
                case TabAlignment.Top:
                    tabBounds.Height += pageBounds.Top - tabBounds.Bottom;
                    break;
                case TabAlignment.Bottom:
                    tabBounds.Height += tabBounds.Top - pageBounds.Bottom;
                    tabBounds.Y -= tabBounds.Top - pageBounds.Bottom;
                    break;
                case TabAlignment.Left:
                    tabBounds.Width += pageBounds.Left - tabBounds.Right;
                    break;
                case TabAlignment.Right:
                    tabBounds.Width += tabBounds.Left - pageBounds.Right;
                    tabBounds.X -= tabBounds.Left - pageBounds.Right;
                    break;
            }
            if (SelectedTabIsLarger)
            {
                tabBounds = EnlargeTab(tabBounds, tabIsSelected);
            }
            if (TabControl.Alignment <= TabAlignment.Bottom)
            {
                tabBounds.X -= _Overlap;
                tabBounds.Width += _Overlap;
            }
            else
            {
                tabBounds.Y -= _Overlap;
                tabBounds.Height += _Overlap;
            }
            return EnsureTabIsInView(tabBounds, pageBounds);
        }

        private Rectangle EnlargeTab(Rectangle tabBounds, bool tabIsSelected)
        {
            Rectangle result = tabBounds;
            int num = (tabIsSelected ? 1 : 0);
            int num2 = (tabIsSelected ? 1 : (-1));
            switch (TabControl.Alignment)
            {
                case TabAlignment.Top:
                    result.Y -= num2;
                    result.Height += num2;
                    result.X -= num;
                    result.Width += 2 * num;
                    break;
                case TabAlignment.Bottom:
                    result.Height += num2;
                    result.X -= num;
                    result.Width += 2 * num;
                    break;
                case TabAlignment.Left:
                    result.X -= num2;
                    result.Width += num2;
                    result.Y -= num;
                    result.Height += 2 * num;
                    break;
                case TabAlignment.Right:
                    result.Width += num2;
                    result.Y -= num;
                    result.Height += 2 * num;
                    break;
            }
            return result;
        }

        protected virtual Rectangle EnsureTabIsInView(Rectangle tabBounds, Rectangle pageBounds)
        {
            if (!TabControl.IsTabVisible(tabBounds, pageBounds))
            {
                return tabBounds;
            }
            Rectangle result = tabBounds;
            switch (TabControl.Alignment)
            {
                case TabAlignment.Top:
                case TabAlignment.Bottom:
                    if (result.X <= pageBounds.X + 4)
                    {
                        result.X = pageBounds.X;
                    }
                    result.Intersect(new Rectangle(pageBounds.X, tabBounds.Y, pageBounds.Width, tabBounds.Height));
                    break;
                case TabAlignment.Left:
                case TabAlignment.Right:
                    if (result.Y <= pageBounds.Y + 4)
                    {
                        result.Y = pageBounds.Y;
                    }
                    result.Intersect(new Rectangle(tabBounds.X, pageBounds.Y, tabBounds.Width, pageBounds.Height));
                    break;
            }
            return result;
        }

        protected internal virtual void DrawTabCloser(GraphicsPath closerPath, GraphicsPath closerButtonPath, Graphics graphics, TabState state, Point mousePosition)
        {
            bool flag = closerButtonPath.GetBounds().Contains(mousePosition);
            switch (state)
            {
                case TabState.Disabled:
                    DrawTabCloser(closerPath, closerButtonPath, graphics, CloserColorUnselected, CloserButtonFillColorUnselected, CloserButtonOutlineColorUnselected);
                    break;
                case TabState.Focused:
                    if (flag)
                    {
                        DrawTabCloser(closerPath, closerButtonPath, graphics, CloserColorFocusedActive, CloserButtonFillColorFocusedActive, CloserButtonOutlineColorFocusedActive);
                    }
                    else
                    {
                        DrawTabCloser(closerPath, closerButtonPath, graphics, CloserColorFocused, CloserButtonFillColorFocused, CloserButtonOutlineColorFocused);
                    }
                    break;
                case TabState.Highlighted:
                    if (flag)
                    {
                        DrawTabCloser(closerPath, closerButtonPath, graphics, CloserColorHighlightedActive, CloserButtonFillColorHighlightedActive, CloserButtonOutlineColorHighlightedActive);
                    }
                    else
                    {
                        DrawTabCloser(closerPath, closerButtonPath, graphics, CloserColorHighlighted, CloserButtonFillColorHighlighted, CloserButtonOutlineColorHighlighted);
                    }
                    break;
                case TabState.Selected:
                    if (flag)
                    {
                        DrawTabCloser(closerPath, closerButtonPath, graphics, CloserColorSelectedActive, CloserButtonFillColorSelectedActive, CloserButtonOutlineColorSelectedActive);
                    }
                    else
                    {
                        DrawTabCloser(closerPath, closerButtonPath, graphics, CloserColorSelected, CloserButtonFillColorSelected, CloserButtonOutlineColorSelected);
                    }
                    break;
                case TabState.None:
                    DrawTabCloser(closerPath, closerButtonPath, graphics, CloserColorUnselected, CloserButtonFillColorUnselected, CloserButtonOutlineColorUnselected);
                    break;
            }
        }

        private void DrawTabCloser(GraphicsPath closerPath, GraphicsPath closerButtonPath, Graphics graphics, Color closerColor, Color closerFillColor, Color closerOutlineColor)
        {
            if (closerButtonPath != null)
            {
                if (closerFillColor != Color.Empty)
                {
                    graphics.SmoothingMode = SmoothingMode.None;
                    using (Brush brush = new SolidBrush(closerFillColor))
                        graphics.FillPath(brush, closerButtonPath);
                }
                if (closerOutlineColor != Color.Empty)
                {
                    graphics.SmoothingMode = SmoothingMode.AntiAlias;
                    using (Pen pen = new Pen(closerOutlineColor))
                        graphics.DrawPath(pen, closerButtonPath);
                }
            }
            if (closerColor != Color.Empty)
            {
                using (Pen pen2 = new Pen(closerColor))
                {
                    pen2.Width = 1f;
                    graphics.SmoothingMode = SmoothingMode.AntiAlias;
                    graphics.DrawPath(pen2, closerPath);
                }
            }
        }

        protected internal virtual GraphicsPath GetTabCloserButtonPath(Rectangle closerButtonRect)
        {
            GraphicsPath graphicsPath = new GraphicsPath();
            graphicsPath.AddLine(closerButtonRect.X, closerButtonRect.Y, closerButtonRect.Right, closerButtonRect.Y);
            graphicsPath.AddLine(closerButtonRect.Right, closerButtonRect.Y, closerButtonRect.Right, closerButtonRect.Bottom);
            graphicsPath.AddLine(closerButtonRect.Right, closerButtonRect.Bottom, closerButtonRect.X, closerButtonRect.Bottom);
            graphicsPath.AddLine(closerButtonRect.X, closerButtonRect.Bottom, closerButtonRect.X, closerButtonRect.Y);
            graphicsPath.CloseFigure();
            return graphicsPath;
        }

        public void DrawTabCloser(Rectangle closerButtonRect, Graphics graphics, TabState state, Point mousePosition)
        {
            if (!_ShowTabCloser)
            {
                return;
            }
            using (GraphicsPath closerPath = GetTabCloserPath(closerButtonRect))
            using (GraphicsPath closerButtonPath = GetTabCloserButtonPath(closerButtonRect))
                DrawTabCloser(closerPath, closerButtonPath, graphics, state, mousePosition);
        }

        protected internal virtual GraphicsPath GetTabCloserPath(Rectangle closerButtonRect)
        {
            GraphicsPath graphicsPath = new GraphicsPath();
            graphicsPath.AddLine(closerButtonRect.X + 4, closerButtonRect.Y + 4, closerButtonRect.Right - 4, closerButtonRect.Bottom - 4);
            graphicsPath.CloseFigure();
            graphicsPath.AddLine(closerButtonRect.Right - 4, closerButtonRect.Y + 4, closerButtonRect.X + 4, closerButtonRect.Bottom - 4);
            graphicsPath.CloseFigure();
            return graphicsPath;
        }

        public virtual void DrawTabFocusIndicator(GraphicsPath tabpath, TabState state, Graphics graphics)
        {
            if (_FocusTrack && state == TabState.Focused)
            {
                Brush brush = null;
                RectangleF bounds = tabpath.GetBounds();
                Rectangle rect = Rectangle.Empty;
                switch (TabControl.Alignment)
                {
                    case TabAlignment.Top:
                        rect = new Rectangle((int)bounds.X, (int)bounds.Y, (int)bounds.Width, 4);
                        brush = new LinearGradientBrush(rect, FocusColor, SystemColors.Window, LinearGradientMode.Vertical);
                        break;
                    case TabAlignment.Bottom:
                        rect = new Rectangle((int)bounds.X, (int)bounds.Bottom - 4, (int)bounds.Width, 4);
                        brush = new LinearGradientBrush(rect, SystemColors.ControlLight, FocusColor, LinearGradientMode.Vertical);
                        break;
                    case TabAlignment.Left:
                        rect = new Rectangle((int)bounds.X, (int)bounds.Y, 4, (int)bounds.Height);
                        brush = new LinearGradientBrush(rect, FocusColor, SystemColors.ControlLight, LinearGradientMode.Horizontal);
                        break;
                    case TabAlignment.Right:
                        rect = new Rectangle((int)bounds.Right - 4, (int)bounds.Y, 4, (int)bounds.Height);
                        brush = new LinearGradientBrush(rect, SystemColors.ControlLight, FocusColor, LinearGradientMode.Horizontal);
                        break;
                }
                Region region = new Region(rect);
                region.Intersect(tabpath);
                graphics.FillRegion(brush, region);
                region.Dispose();
                brush.Dispose();
            }
        }

        protected internal virtual void PaintTabBackground(GraphicsPath tabBorder, TabState state, Graphics graphics)
        {
            using (Brush brush = GetTabBackgroundBrush(state, tabBorder))
                graphics.FillPath(brush, tabBorder);
        }

        public virtual Brush GetPageBackgroundBrush(TabState state)
        {
            Color color = Color.Empty;
            switch (state)
            {
                case TabState.Disabled:
                    color = PageBackgroundColorDisabled;
                    break;
                case TabState.Focused:
                    color = PageBackgroundColorFocused;
                    break;
                case TabState.Highlighted:
                    color = PageBackgroundColorHighlighted;
                    break;
                case TabState.Selected:
                    color = PageBackgroundColorSelected;
                    break;
                case TabState.None:
                    color = PageBackgroundColorUnselected;
                    break;
            }
            return new SolidBrush(color);
        }

        protected internal Brush GetTabBackgroundBrush(TabState state, GraphicsPath tabBorder)
        {
            Color tabBackgroundColor = GetTabBackgroundColor1(state, tabBorder);
            Color tabBackgroundColor2 = GetTabBackgroundColor2(state, tabBorder);
            return CreateTabBackgroundBrush(tabBackgroundColor, tabBackgroundColor2, state, tabBorder);
        }

        protected internal virtual Brush CreateTabBackgroundBrush(Color color1, Color color2, TabState state, GraphicsPath tabBorder)
        {
            LinearGradientBrush linearGradientBrush = null;
            RectangleF bounds = tabBorder.GetBounds();
            switch (TabControl.Alignment)
            {
                case TabAlignment.Top:
                    bounds.Height += 1f;
                    linearGradientBrush = new LinearGradientBrush(bounds, color2, color1, LinearGradientMode.Vertical);
                    break;
                case TabAlignment.Bottom:
                    linearGradientBrush = new LinearGradientBrush(bounds, color1, color2, LinearGradientMode.Vertical);
                    break;
                case TabAlignment.Left:
                    linearGradientBrush = new LinearGradientBrush(bounds, color2, color1, LinearGradientMode.Horizontal);
                    break;
                case TabAlignment.Right:
                    linearGradientBrush = new LinearGradientBrush(bounds, color1, color2, LinearGradientMode.Horizontal);
                    break;
            }
            linearGradientBrush.Blend = GetBackgroundBlend();
            return linearGradientBrush;
        }

        protected virtual Color GetTabBackgroundColor1(TabState state, GraphicsPath tabBorder)
        {
            Color result = Color.Empty;
            switch (state)
            {
                case TabState.Disabled:
                    result = TabColorDisabled1;
                    break;
                case TabState.Focused:
                    result = TabColorFocused1;
                    break;
                case TabState.Highlighted:
                    result = TabColorHighLighted1;
                    break;
                case TabState.Selected:
                    result = TabColorSelected1;
                    break;
                case TabState.None:
                    result = TabColorUnSelected1;
                    break;
            }
            return result;
        }

        protected virtual Color GetTabBackgroundColor2(TabState state, GraphicsPath tabBorder)
        {
            Color result = Color.Empty;
            switch (state)
            {
                case TabState.Disabled:
                    result = TabColorDisabled2;
                    break;
                case TabState.Focused:
                    result = TabColorFocused2;
                    break;
                case TabState.Highlighted:
                    result = TabColorHighLighted2;
                    break;
                case TabState.Selected:
                    result = TabColorSelected2;
                    break;
                case TabState.None:
                    result = TabColorUnSelected2;
                    break;
            }
            return result;
        }

        protected virtual Blend GetBackgroundBlend()
        {
            float[] factors = new float[3] { 0f, 0.7f, 1f };
            float[] positions = new float[3] { 0f, 0.6f, 1f };
            if (BlendStyle == BlendStyle.Glass)
            {
                factors = new float[4] { 0f, 0.5f, 1f, 1f };
                positions = new float[4] { 0f, 0.5f, 0.51f, 1f };
            }
            return new Blend
            {
                Factors = factors,
                Positions = positions
            };
        }

        public GraphicsPath GetTabBorder(Rectangle tabBounds)
        {
            GraphicsPath graphicsPath = new GraphicsPath();
            AddTabBorder(graphicsPath, tabBounds);
            graphicsPath.CloseFigure();
            return graphicsPath;
        }
    }

}
