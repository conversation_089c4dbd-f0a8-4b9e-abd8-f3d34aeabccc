﻿using OCRTools;
using OCRTools.Language;
using System.Drawing;

namespace ShareX.ScreenCaptureLib
{
    public class PixelateEffectShape : BaseEffectShape
    {
        public override ShapeType ShapeType { get; } = ShapeType.马赛克;

        public override string OverlayText => $"{"像素".CurrentText()}:{PixelSize}";

        public int PixelSize { get; set; }

        public override void OnConfigLoad()
        {
            PixelSize = AnnotationOptions.PixelateSize;
        }

        public override void OnConfigSave()
        {
            AnnotationOptions.PixelateSize = PixelSize;
        }

        public override void ApplyEffect(Bitmap bmp)
        {
            ImageHelp.Pixelate(bmp, PixelSize);
        }

        public override void OnDrawFinal(Graphics g, Bitmap bmp)
        {
            if (PixelSize > 1)
            {
                base.OnDrawFinal(g, bmp);
            }
        }
    }
}