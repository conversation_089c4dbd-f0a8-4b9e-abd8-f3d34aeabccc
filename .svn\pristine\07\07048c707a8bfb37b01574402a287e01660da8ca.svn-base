using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using QiHe.CodeLib;

namespace ExcelLibrary.BinaryFileFormat
{
	public class Record
	{
		public const ushort MaxContentLength = 8224;

		public ushort Type;

		public ushort Size;

		public byte[] Data;

		public List<Record> ContinuedRecords;

		public int FullSize
		{
			get
			{
				int num = 4 + Size;
				foreach (Record continuedRecord in ContinuedRecords)
				{
					num += 4 + continuedRecord.Size;
				}
				return num;
			}
		}

		public int TotalSize
		{
			get
			{
				int num = Size;
				foreach (Record continuedRecord in ContinuedRecords)
				{
					num += continuedRecord.Size;
				}
				return num;
			}
		}

		public byte[] AllData
		{
			get
			{
				if (ContinuedRecords.Count == 0)
				{
					return Data;
				}
				List<byte> list = new List<byte>(TotalSize);
				list.AddRange(Data);
				foreach (Record continuedRecord in ContinuedRecords)
				{
					list.AddRange(continuedRecord.AllData);
				}
				return list.ToArray();
			}
		}

		public static Record Read(Stream stream)
		{
			Record record = ReadBase(stream);
			switch (record.Type)
			{
			case 2057:
				return new BOF(record);
			case 545:
				return new ARRAY(record);
			case 64:
				return new BACKUP(record);
			case 513:
				return new BLANK(record);
			case 218:
				return new BOOKBOOL(record);
			case 41:
				return new BOTTOMMARGIN(record);
			case 133:
				return new BOUNDSHEET(record);
			case 12:
				return new CALCCOUNT(record);
			case 13:
				return new CALCMODE(record);
			case 66:
				return new CODEPAGE(record);
			case 512:
				return new DIMENSIONS(record);
			case 190:
				return new MULBLANK(record);
			case 189:
				return new MULRK(record);
			case 520:
				return new ROW(record);
			case 214:
				return new RSTRING(record);
			case 252:
				return new SST(record);
			case 60:
				return new CONTINUE(record);
			case 6:
				return new FORMULA(record);
			case 224:
				return new XF(record);
			case 233:
				return new BITMAP(record);
			case 93:
				return new OBJ(record);
			case 34:
				return new DATEMODE(record);
			case 235:
				return new MSODRAWINGGROUP(record);
			case 236:
				return new MSODRAWING(record);
			case 237:
				return new MSODRAWINGSELECTION(record);
			case 519:
				return new STRING(record);
			case 10:
				return new EOF(record);
			case 517:
				return new BOOLERR(record);
			case 253:
				return new LABELSST(record);
			case 515:
				return new NUMBER(record);
			case 638:
				return new RK(record);
			case 516:
				return new LABEL(record);
			case 215:
				return new DBCELL(record);
			case 255:
				return new EXTSST(record);
			case 61:
				return new WINDOW1(record);
			case 1054:
				return new FORMAT(record);
			case 49:
				return new FONT(record);
			case 125:
				return new COLINFO(record);
			case 146:
				return new PALETTE(record);
			case 153:
				return new STANDARDWIDTH(record);
			case 85:
				return new DEFCOLWIDTH(record);
			default:
				return record;
			}
		}

		public Record()
		{
			ContinuedRecords = new List<Record>();
		}

		public Record(Record record)
		{
			Type = record.Type;
			Size = record.Size;
			Data = record.Data;
			ContinuedRecords = record.ContinuedRecords;
		}

		public virtual void Decode()
		{
		}

		public virtual void Encode()
		{
			ContinuedRecords.Clear();
			if (Size > 0 && Data.Length > 8224)
			{
				int num;
				for (int i = 8224; i < Data.Length; i += num)
				{
					CONTINUE cONTINUE = new CONTINUE();
					num = Math.Min(8224, Data.Length - i);
					cONTINUE.Data = Algorithm.ArraySection(Data, i, num);
					cONTINUE.Size = (ushort)num;
					ContinuedRecords.Add(cONTINUE);
				}
				Size = 8224;
				Data = Algorithm.ArraySection(Data, 0, 8224);
			}
		}

		public static Record ReadBase(Stream stream)
		{
			BinaryReader binaryReader = new BinaryReader(stream);
			Record record = new Record();
			record.Type = binaryReader.ReadUInt16();
			record.Size = binaryReader.ReadUInt16();
			record.Data = binaryReader.ReadBytes(record.Size);
			return record;
		}

		public static object DecodeRK(uint value)
		{
			bool flag = (value & 1) == 1;
			if ((value & 2) == 0)
			{
				ulong data = (ulong)(uint)((int)value & -4) << 32;
				double num = TreatUInt64AsDouble(data);
				if (flag)
				{
					num /= 100.0;
				}
				return num;
			}
			int num2 = (int)(value & 0xFFFFFFFCu) >> 2;
			if (flag)
			{
				return (decimal)num2 / 100m;
			}
			return num2;
		}

		public static double TreatUInt64AsDouble(ulong data)
		{
			byte[] bytes = BitConverter.GetBytes(data);
			return BitConverter.ToDouble(bytes, 0);
		}

		public void Write(BinaryWriter writer)
		{
			writer.Write(Type);
			writer.Write(Size);
			if (Size <= 0)
			{
				return;
			}
			writer.Write(Data);
			if (ContinuedRecords.Count <= 0)
			{
				return;
			}
			foreach (Record continuedRecord in ContinuedRecords)
			{
				writer.Write(continuedRecord.Type);
				writer.Write(continuedRecord.Size);
				writer.Write(continuedRecord.Data);
			}
		}

		public string ReadString(BinaryReader reader, int lengthbits)
		{
			StringDecoder stringDecoder = new StringDecoder(this, reader);
			return stringDecoder.ReadString(lengthbits);
		}

		public static void WriteString(BinaryWriter writer, string text, int lengthbits)
		{
			switch (lengthbits)
			{
			case 8:
				writer.Write((byte)text.Length);
				break;
			case 16:
				writer.Write((ushort)text.Length);
				break;
			default:
				throw new ArgumentException("Invalid lengthbits, must be 8 or 16.");
			}
			if (TextEncoding.FitsInASCIIEncoding(text))
			{
				writer.Write((byte)0);
				writer.Write(Encoding.ASCII.GetBytes(text));
			}
			else
			{
				writer.Write((byte)1);
				writer.Write(Encoding.Unicode.GetBytes(text));
			}
		}

		public static int GetStringDataLength(string text)
		{
			if (TextEncoding.FitsInASCIIEncoding(text))
			{
				return Encoding.ASCII.GetByteCount(text) + 3;
			}
			return text.Length * 2 + 3;
		}

		public static void EncodeRecords(List<Record> records)
		{
			foreach (Record record in records)
			{
				record.Encode();
			}
		}

		public static int CountDataLength(List<Record> records)
		{
			int num = 0;
			foreach (Record record in records)
			{
				num += record.FullSize;
			}
			return num;
		}
	}
}
