﻿namespace OCRTools
{
    partial class RulerForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(RulerForm));
            this.contxtMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.conMeasure = new System.Windows.Forms.ToolStripMenuItem();
            this.comUnits = new System.Windows.Forms.ToolStripMenuItem();
            this.conRulerMode = new System.Windows.Forms.ToolStripMenuItem();
            this.conModeHorizontal = new System.Windows.Forms.ToolStripMenuItem();
            this.conModeVertical = new System.Windows.Forms.ToolStripMenuItem();
            this.conModeTwoDimensional = new System.Windows.Forms.ToolStripMenuItem();
            this.conFollowMousePointer = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator4 = new System.Windows.Forms.ToolStripSeparator();
            this.comTheme = new System.Windows.Forms.ToolStripMenuItem();
            this.conSlimMode = new System.Windows.Forms.ToolStripMenuItem();
            this.conOpacity = new System.Windows.Forms.ToolStripMenuItem();
            this.conHigh = new System.Windows.Forms.ToolStripMenuItem();
            this.conDefault = new System.Windows.Forms.ToolStripMenuItem();
            this.conMedium = new System.Windows.Forms.ToolStripMenuItem();
            this.conLow = new System.Windows.Forms.ToolStripMenuItem();
            this.conVeryLow = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.appearanceToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.contxtAppearance = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.conMarkCenter = new System.Windows.Forms.ToolStripMenuItem();
            this.conMarkThirds = new System.Windows.Forms.ToolStripMenuItem();
            this.conMarkGolden = new System.Windows.Forms.ToolStripMenuItem();
            this.conMarkMouse = new System.Windows.Forms.ToolStripMenuItem();
            this.conOffsetLength = new System.Windows.Forms.ToolStripMenuItem();
            this.conClearCustomMarker = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.conLength = new System.Windows.Forms.ToolStripMenuItem();
            this.conCalibrate = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.conTopmost = new System.Windows.Forms.ToolStripMenuItem();
            this.conExit = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator5 = new System.Windows.Forms.ToolStripSeparator();
            this.rulerToolTip = new System.Windows.Forms.ToolTip(this.components);
            this.contxtMenu.SuspendLayout();
            this.contxtAppearance.SuspendLayout();
            this.SuspendLayout();
            // 
            // contxtMenu
            // 
            this.contxtMenu.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.contxtMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.conMeasure,
            this.comUnits,
            this.conRulerMode,
            this.conFollowMousePointer,
            this.toolStripSeparator4,
            this.comTheme,
            this.conSlimMode,
            this.conOpacity,
            this.toolStripSeparator1,
            this.appearanceToolStripMenuItem,
            this.conClearCustomMarker,
            this.toolStripSeparator3,
            this.conLength,
            this.conCalibrate,
            this.toolStripSeparator2,
            this.conTopmost,
            this.conExit});
            this.contxtMenu.Name = "contxtMenu";
            this.contxtMenu.Size = new System.Drawing.Size(205, 366);
            this.contxtMenu.Opening += new System.ComponentModel.CancelEventHandler(this.contxtMenu_Opening);
            // 
            // conMeasure
            // 
            this.conMeasure.ShortcutKeyDisplayString = "Z";
            this.conMeasure.Size = new System.Drawing.Size(204, 26);
            this.conMeasure.Text = "测量窗口";
            this.conMeasure.Click += new System.EventHandler(this.conMeasure_Click);
            // 
            // comUnits
            // 
            this.comUnits.Size = new System.Drawing.Size(204, 26);
            this.comUnits.Text = "计量单位";
            this.comUnits.ToolTipText = "计量单位";
            // 
            // conRulerMode
            // 
            this.conRulerMode.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.conModeHorizontal,
            this.conModeVertical,
            this.conModeTwoDimensional});
            this.conRulerMode.Size = new System.Drawing.Size(204, 26);
            this.conRulerMode.Text = "方向";
            this.conRulerMode.DropDownOpening += new System.EventHandler(this.conRulerMode_DropDownOpening);
            // 
            // conModeHorizontal
            // 
            this.conModeHorizontal.Size = new System.Drawing.Size(163, 26);
            this.conModeHorizontal.Tag = "Horizontal";
            this.conModeHorizontal.Text = "横向";
            this.conModeHorizontal.Click += new System.EventHandler(this.ChangeRulerMode);
            // 
            // conModeVertical
            // 
            this.conModeVertical.Size = new System.Drawing.Size(163, 26);
            this.conModeVertical.Tag = "Vertical";
            this.conModeVertical.Text = "纵向";
            this.conModeVertical.Click += new System.EventHandler(this.ChangeRulerMode);
            // 
            // conModeTwoDimensional
            // 
            this.conModeTwoDimensional.Size = new System.Drawing.Size(163, 26);
            this.conModeTwoDimensional.Tag = "TwoDimensional";
            this.conModeTwoDimensional.Text = "横向+纵向";
            this.conModeTwoDimensional.Click += new System.EventHandler(this.ChangeRulerMode);
            // 
            // conFollowMousePointer
            // 
            this.conFollowMousePointer.ShortcutKeyDisplayString = "W";
            this.conFollowMousePointer.Size = new System.Drawing.Size(204, 26);
            this.conFollowMousePointer.Text = "跟随鼠标";
            this.conFollowMousePointer.Click += new System.EventHandler(this.conFollowMousePointer_Click);
            // 
            // toolStripSeparator4
            // 
            this.toolStripSeparator4.Name = "toolStripSeparator4";
            this.toolStripSeparator4.Size = new System.Drawing.Size(201, 6);
            // 
            // comTheme
            // 
            this.comTheme.Size = new System.Drawing.Size(204, 26);
            this.comTheme.Text = "颜色";
            // 
            // conSlimMode
            // 
            this.conSlimMode.ShortcutKeyDisplayString = "J";
            this.conSlimMode.Size = new System.Drawing.Size(204, 26);
            this.conSlimMode.Text = "简洁模式";
            this.conSlimMode.Click += new System.EventHandler(this.conSlimMode_Click);
            // 
            // conOpacity
            // 
            this.conOpacity.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.conHigh,
            this.conDefault,
            this.conMedium,
            this.conLow,
            this.conVeryLow});
            this.conOpacity.Size = new System.Drawing.Size(204, 26);
            this.conOpacity.Text = "透明度";
            // 
            // conHigh
            // 
            this.conHigh.Name = "conHigh";
            this.conHigh.Size = new System.Drawing.Size(167, 26);
            this.conHigh.Tag = "100";
            this.conHigh.Text = "100%";
            this.conHigh.Click += new System.EventHandler(this.ChangeOpacity);
            // 
            // conDefault
            // 
            this.conDefault.Name = "conDefault";
            this.conDefault.Size = new System.Drawing.Size(167, 26);
            this.conDefault.Tag = "80";
            this.conDefault.Text = "80%";
            this.conDefault.Click += new System.EventHandler(this.ChangeOpacity);
            // 
            // conMedium
            // 
            this.conMedium.Name = "conMedium";
            this.conMedium.Size = new System.Drawing.Size(167, 26);
            this.conMedium.Tag = "60";
            this.conMedium.Text = "60%";
            this.conMedium.Click += new System.EventHandler(this.ChangeOpacity);
            // 
            // conLow
            // 
            this.conLow.Name = "conLow";
            this.conLow.Size = new System.Drawing.Size(167, 26);
            this.conLow.Tag = "40";
            this.conLow.Text = "40%";
            this.conLow.Click += new System.EventHandler(this.ChangeOpacity);
            // 
            // conVeryLow
            // 
            this.conVeryLow.Name = "conVeryLow";
            this.conVeryLow.Size = new System.Drawing.Size(167, 26);
            this.conVeryLow.Tag = "20";
            this.conVeryLow.Text = "20%";
            this.conVeryLow.Click += new System.EventHandler(this.ChangeOpacity);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(201, 6);
            // 
            // appearanceToolStripMenuItem
            // 
            this.appearanceToolStripMenuItem.DropDown = this.contxtAppearance;
            this.appearanceToolStripMenuItem.Size = new System.Drawing.Size(204, 26);
            this.appearanceToolStripMenuItem.Text = "刻度标记";
            // 
            // contxtAppearance
            // 
            this.contxtAppearance.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.contxtAppearance.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.conMarkCenter,
            this.conMarkThirds,
            this.conMarkGolden,
            this.conMarkMouse,
            this.conOffsetLength});
            this.contxtAppearance.Name = "contxtAppearance";
            this.contxtAppearance.OwnerItem = this.appearanceToolStripMenuItem;
            this.contxtAppearance.Size = new System.Drawing.Size(204, 134);
            // 
            // conMarkCenter
            // 
            this.conMarkCenter.ShortcutKeyDisplayString = "M";
            this.conMarkCenter.Size = new System.Drawing.Size(203, 26);
            this.conMarkCenter.Text = "二等分标记";
            this.conMarkCenter.Click += new System.EventHandler(this.conMarkCenter_Click);
            // 
            // conMarkThirds
            // 
            this.conMarkThirds.ShortcutKeyDisplayString = "T";
            this.conMarkThirds.Size = new System.Drawing.Size(203, 26);
            this.conMarkThirds.Text = "三等分标记";
            this.conMarkThirds.Click += new System.EventHandler(this.conMarkThirds_Click);
            // 
            // conMarkGolden
            // 
            this.conMarkGolden.ShortcutKeyDisplayString = "G";
            this.conMarkGolden.Size = new System.Drawing.Size(203, 26);
            this.conMarkGolden.Text = "黄金分割比标记";
            this.conMarkGolden.Click += new System.EventHandler(this.conMarkGolden_Click);
            // 
            // conMarkMouse
            // 
            this.conMarkMouse.Checked = true;
            this.conMarkMouse.CheckState = System.Windows.Forms.CheckState.Checked;
            this.conMarkMouse.ShortcutKeyDisplayString = "P";
            this.conMarkMouse.Size = new System.Drawing.Size(203, 26);
            this.conMarkMouse.Text = "标记鼠标位置";
            this.conMarkMouse.Click += new System.EventHandler(this.conMarkMouse_Click);
            // 
            // conOffsetLength
            // 
            this.conOffsetLength.Size = new System.Drawing.Size(203, 26);
            this.conOffsetLength.Text = "显示偏移与长度";
            this.conOffsetLength.Click += new System.EventHandler(this.conOffsetLength_Click);
            // 
            // conClearCustomMarker
            // 
            this.conClearCustomMarker.ShortcutKeyDisplayString = "Del";
            this.conClearCustomMarker.Size = new System.Drawing.Size(204, 26);
            this.conClearCustomMarker.Text = "清除所有标记";
            this.conClearCustomMarker.Click += new System.EventHandler(this.conClearCustomMarker_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(201, 6);
            // 
            // conLength
            // 
            this.conLength.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.L)));
            this.conLength.Size = new System.Drawing.Size(204, 26);
            this.conLength.Text = "设定长度";
            this.conLength.Click += new System.EventHandler(this.conLength_Click);
            // 
            // conCalibrate
            // 
            this.conCalibrate.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.K)));
            this.conCalibrate.Size = new System.Drawing.Size(204, 26);
            this.conCalibrate.Text = "DPI设置";
            this.conCalibrate.Click += new System.EventHandler(this.conCalibrate_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(201, 6);
            // 
            // conTopmost
            // 
            this.conTopmost.Checked = true;
            this.conTopmost.CheckState = System.Windows.Forms.CheckState.Checked;
            this.conTopmost.ShortcutKeyDisplayString = "S";
            this.conTopmost.Size = new System.Drawing.Size(204, 26);
            this.conTopmost.Text = "置顶窗体";
            this.conTopmost.Click += new System.EventHandler(this.conTopmost_Click);
            // 
            // conExit
            // 
            this.conExit.ShortcutKeyDisplayString = "Esc";
            this.conExit.Size = new System.Drawing.Size(204, 26);
            this.conExit.Text = "关闭";
            this.conExit.Click += new System.EventHandler(this.conExit_Click);
            // 
            // toolStripSeparator5
            // 
            this.toolStripSeparator5.Name = "toolStripSeparator5";
            this.toolStripSeparator5.Size = new System.Drawing.Size(6, 6);
            // 
            // RulerForm
            // 
            this.AccessibleDescription = "";
            this.AutoScaleDimensions = new System.Drawing.SizeF(120F, 120F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BackColor = System.Drawing.Color.Thistle;
            this.ClientSize = new System.Drawing.Size(438, 100);
            this.ContextMenuStrip = this.contxtMenu;
            this.DoubleBuffered = true;
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.MaximizeBox = false;
            this.Text = "标尺";
            this.TransparencyKey = System.Drawing.Color.Thistle;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.RulerForm_FormClosing);
            this.Load += new System.EventHandler(this.RulerForm_Load);
            this.SizeChanged += new System.EventHandler(this.RulerForm_SizeChanged);
            this.MouseClick += new System.Windows.Forms.MouseEventHandler(this.RulerForm_MouseClick);
            this.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.RulerForm_MouseDoubleClick);
            this.Move += new System.EventHandler(this.RulerForm_Move);
            this.contxtMenu.ResumeLayout(false);
            this.contxtAppearance.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contxtMenu;
        private System.Windows.Forms.ToolStripMenuItem conExit;
        private System.Windows.Forms.ToolStripMenuItem conTopmost;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem conOpacity;
        private System.Windows.Forms.ToolStripMenuItem conLength;
        private System.Windows.Forms.ToolStripMenuItem conHigh;
        private System.Windows.Forms.ToolStripMenuItem conDefault;
        private System.Windows.Forms.ToolStripMenuItem conLow;
        private System.Windows.Forms.ToolStripMenuItem conVeryLow;
        private System.Windows.Forms.ToolStripMenuItem conClearCustomMarker;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripMenuItem comUnits;
        private System.Windows.Forms.ToolStripMenuItem conMeasure;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator4;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator5;
        private System.Windows.Forms.ToolTip rulerToolTip;
        private System.Windows.Forms.ToolStripMenuItem conCalibrate;
        private System.Windows.Forms.ToolStripMenuItem conRulerMode;
        private System.Windows.Forms.ToolStripMenuItem conModeHorizontal;
        private System.Windows.Forms.ToolStripMenuItem conModeVertical;
        private System.Windows.Forms.ToolStripMenuItem conModeTwoDimensional;
        private System.Windows.Forms.ToolStripMenuItem conSlimMode;
        private System.Windows.Forms.ToolStripMenuItem conFollowMousePointer;
        private System.Windows.Forms.ToolStripMenuItem conMedium;
        private System.Windows.Forms.ToolStripMenuItem appearanceToolStripMenuItem;
        private System.Windows.Forms.ContextMenuStrip contxtAppearance;
        private System.Windows.Forms.ToolStripMenuItem conMarkCenter;
        private System.Windows.Forms.ToolStripMenuItem conMarkThirds;
        private System.Windows.Forms.ToolStripMenuItem conMarkGolden;
        private System.Windows.Forms.ToolStripMenuItem conMarkMouse;
        private System.Windows.Forms.ToolStripMenuItem conOffsetLength;
        private System.Windows.Forms.ToolStripMenuItem comTheme;
    }
}

