﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.WindowsRuntime</name>
  </assembly>
  <members>
    <member name="T:System.WindowsRuntimeSystemExtensions">
      <summary>작업과 Windows 런타임 간 비동기 동작 및 작업을 변환하기 위한 확장 메서드를 제공합니다. </summary>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsAsyncAction(System.Threading.Tasks.Task)">
      <summary>시작된 작업을 나타내는 Windows 런타임 비동기 작업을 반환합니다. </summary>
      <returns>시작된 작업을 나타내는 Windows.Foundation.IAsyncAction 인스턴스입니다. </returns>
      <param name="source">시작된 작업입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" />는 시작되지 않은 작업입니다. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsAsyncOperation``1(System.Threading.Tasks.Task{``0})">
      <summary>결과를 반환하는 시작된 작업을 나타내는 Windows 런타임 비동기 작업을 반환합니다. </summary>
      <returns>시작된 작업을 나타내는 Windows.Foundation.IAsyncOperation&lt;TResult&gt; 인스턴스입니다. </returns>
      <param name="source">시작된 작업입니다. </param>
      <typeparam name="TResult">결과를 반환하는 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" />는 시작되지 않은 작업입니다. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask(Windows.Foundation.IAsyncAction)">
      <summary>Windows 런타임 비동기 작업을 나타내는 작업을 반환합니다. </summary>
      <returns>비동기 동작을 나타내는 작업입니다. </returns>
      <param name="source">비동기 작업입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask(Windows.Foundation.IAsyncAction,System.Threading.CancellationToken)">
      <summary>취소할 수 있는 Windows 런타임 비동기 작업을 나타내는 작업을 반환합니다. </summary>
      <returns>비동기 동작을 나타내는 작업입니다. </returns>
      <param name="source">비동기 작업입니다. </param>
      <param name="cancellationToken">비동기 작업(asynchronous action)의 취소를 요청하는 데 사용할 수 있는 토큰입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0})">
      <summary>Windows 런타임 비동기 작업을 나타내는 작업을 반환합니다. </summary>
      <returns>비동기 동작을 나타내는 작업입니다. </returns>
      <param name="source">비동기 작업입니다. </param>
      <typeparam name="TProgress">진행을 나타내는 데이터를 제공하는 개체의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0},System.IProgress{``0})">
      <summary>진행률을 보고하는 Windows 런타임 비동기 작업을 나타내는 작업을 반환합니다. </summary>
      <returns>비동기 동작을 나타내는 작업입니다. </returns>
      <param name="source">비동기 작업입니다. </param>
      <param name="progress">진행 상황 업데이트를 수신하는 개체입니다. </param>
      <typeparam name="TProgress">진행을 나타내는 데이터를 제공하는 개체의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0},System.Threading.CancellationToken)">
      <summary>취소할 수 있는 Windows 런타임 비동기 작업을 나타내는 작업을 반환합니다. </summary>
      <returns>비동기 동작을 나타내는 작업입니다. </returns>
      <param name="source">비동기 작업입니다. </param>
      <param name="cancellationToken">비동기 작업(asynchronous action)의 취소를 요청하는 데 사용할 수 있는 토큰입니다. </param>
      <typeparam name="TProgress">진행을 나타내는 데이터를 제공하는 개체의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0},System.Threading.CancellationToken,System.IProgress{``0})">
      <summary>진행률을 보고하고 취소할 수 있는 Windows 런타임 비동기 작업을 나타내는 작업을 반환합니다.</summary>
      <returns>비동기 동작을 나타내는 작업입니다. </returns>
      <param name="source">비동기 작업입니다. </param>
      <param name="cancellationToken">비동기 작업(asynchronous action)의 취소를 요청하는 데 사용할 수 있는 토큰입니다. </param>
      <param name="progress">진행 상황 업데이트를 수신하는 개체입니다. </param>
      <typeparam name="TProgress">진행을 나타내는 데이터를 제공하는 개체의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncOperation{``0})">
      <summary>결과를 반환하는 Windows 런타임 비동기 작업을 나타내는 작업을 반환합니다. </summary>
      <returns>비동기 작업(operation)을 나타내는 작업(task)입니다. </returns>
      <param name="source">비동기 작업입니다. </param>
      <typeparam name="TResult">비동기 작업의 결과를 반환하는 개체의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncOperation{``0},System.Threading.CancellationToken)">
      <summary>결과를 반환하고 취소할 수 있는 Windows 런타임 비동기 작업을 나타내는 작업을 반환합니다. </summary>
      <returns>비동기 작업(operation)을 나타내는 작업(task)입니다. </returns>
      <param name="source">비동기 작업입니다. </param>
      <param name="cancellationToken">비동기 작업(asynchronous operation)의 취소를 요청하는 데 사용할 수 있는 토큰입니다. </param>
      <typeparam name="TResult">비동기 작업의 결과를 반환하는 개체의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1})">
      <summary>결과를 반환하는 Windows 런타임 비동기 작업을 나타내는 작업을 반환합니다. </summary>
      <returns>비동기 작업(operation)을 나타내는 작업(task)입니다. </returns>
      <param name="source">비동기 작업입니다. </param>
      <typeparam name="TResult">비동기 작업의 결과를 반환하는 개체의 형식입니다. </typeparam>
      <typeparam name="TProgress">진행을 나타내는 데이터를 제공하는 개체의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1},System.IProgress{``1})">
      <summary>결과를 반환하고 진행률을 보고하는 Windows 런타임 비동기 작업을 나타내는 작업을 반환합니다. </summary>
      <returns>비동기 작업(operation)을 나타내는 작업(task)입니다. </returns>
      <param name="source">비동기 작업입니다. </param>
      <param name="progress">진행 상황 업데이트를 수신하는 개체입니다. </param>
      <typeparam name="TResult">비동기 작업의 결과를 반환하는 개체의 형식입니다. </typeparam>
      <typeparam name="TProgress">진행을 나타내는 데이터를 제공하는 개체의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1},System.Threading.CancellationToken)">
      <summary>결과를 반환하고 취소할 수 있는 Windows 런타임 비동기 작업을 나타내는 작업을 반환합니다. </summary>
      <returns>비동기 작업(operation)을 나타내는 작업(task)입니다. </returns>
      <param name="source">비동기 작업입니다. </param>
      <param name="cancellationToken">비동기 작업(asynchronous operation)의 취소를 요청하는 데 사용할 수 있는 토큰입니다. </param>
      <typeparam name="TResult">비동기 작업의 결과를 반환하는 개체의 형식입니다. </typeparam>
      <typeparam name="TProgress">진행을 나타내는 데이터를 제공하는 개체의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1},System.Threading.CancellationToken,System.IProgress{``1})">
      <summary>결과를 반환하고 진행률을 보고하고 취소할 수 있는 비동기 작업인 Windows 런타임를 나타내는 작업을 반환합니다. </summary>
      <returns>비동기 작업(operation)을 나타내는 작업(task)입니다. </returns>
      <param name="source">비동기 작업입니다. </param>
      <param name="cancellationToken">비동기 작업(asynchronous operation)의 취소를 요청하는 데 사용할 수 있는 토큰입니다. </param>
      <param name="progress">진행 상황 업데이트를 수신하는 개체입니다. </param>
      <typeparam name="TResult">비동기 작업의 결과를 반환하는 개체의 형식입니다. </typeparam>
      <typeparam name="TProgress">진행을 나타내는 데이터를 제공하는 개체의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter(Windows.Foundation.IAsyncAction)">
      <summary>비동기 작업을 기다리는 개체를 반환합니다. </summary>
      <returns>지정된 비동기 작업을 기다리는 개체입니다. </returns>
      <param name="source">대기 중인 비동기 작업입니다. </param>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter``1(Windows.Foundation.IAsyncActionWithProgress{``0})">
      <summary>진행률을 보고하는 비동기 작업을 기다리는 개체를 반환합니다. </summary>
      <returns>지정된 비동기 작업을 기다리는 개체입니다. </returns>
      <param name="source">대기 중인 비동기 작업입니다. </param>
      <typeparam name="TProgress">진행을 나타내는 데이터를 제공하는 개체의 형식입니다. </typeparam>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter``1(Windows.Foundation.IAsyncOperation{``0})">
      <summary>결과를 반환하는 비동기 작업을 기다리는 개체를 반환합니다.</summary>
      <returns>지정된 비동기 작업을 기다리는 개체입니다. </returns>
      <param name="source">대기할 비동기 작업입니다. </param>
      <typeparam name="TResult">비동기 작업의 결과를 반환하는 개체의 형식입니다. </typeparam>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1})">
      <summary>진행률을 보고하고 결과를 반환하는 비동기 작업을 기다리는 개체를 반환합니다. </summary>
      <returns>지정된 비동기 작업을 기다리는 개체입니다. </returns>
      <param name="source">대기할 비동기 작업입니다. </param>
      <typeparam name="TResult">비동기 작업의 결과를 반환하는 개체의 형식입니다.</typeparam>
      <typeparam name="TProgress">진행을 나타내는 데이터를 제공하는 개체의 형식입니다. </typeparam>
    </member>
    <member name="T:System.IO.WindowsRuntimeStorageExtensions">
      <summary>Windows Store 응용 프로그램을 개발할 때 Windows 런타임의 IStorageFile 및 IStorageFolder 인터페이스에 대한 확장 메서드를 포함합니다.</summary>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForReadAsync(Windows.Storage.IStorageFile)">
      <summary>지정된 파일에서 읽기 위한 스트림을 검색합니다.</summary>
      <returns>비동기 읽기 작업을 나타내는 작업입니다.</returns>
      <param name="windowsRuntimeFile">읽을 Windows 런타임 IStorageFile 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeFile" />가 null입니다.</exception>
      <exception cref="T:System.IO.IOException">파일을 열 수 없거나 스트림으로 검색할 수 없습니다.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForReadAsync(Windows.Storage.IStorageFolder,System.String)">
      <summary>지정한 상위 폴더의 파일에서 읽기 위한 스트림을 검색합니다.</summary>
      <returns>비동기 읽기 작업을 나타내는 작업입니다.</returns>
      <param name="rootDirectory">읽을 파일이 포함된 Windows 런타임 IStorageFolder 개체입니다.</param>
      <param name="relativePath">읽을 파일의 경로(루트 폴더에 상대적)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rootDirectory" /> 또는 <paramref name="relativePath" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="relativePath" />이 비어 있거나 공백 문자만 있습니다.</exception>
      <exception cref="T:System.IO.IOException">파일을 열 수 없거나 스트림으로 검색할 수 없습니다.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForWriteAsync(Windows.Storage.IStorageFile)">
      <summary>지정된 파일에 쓰기 위한 스트림을 검색합니다.</summary>
      <returns>비동기 쓰기 작업을 나타내는 작업입니다.</returns>
      <param name="windowsRuntimeFile">쓸 Windows 런타임 IStorageFile 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeFile" />가 null입니다.</exception>
      <exception cref="T:System.IO.IOException">파일을 열 수 없거나 스트림으로 검색할 수 없습니다.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForWriteAsync(Windows.Storage.IStorageFolder,System.String,Windows.Storage.CreationCollisionOption)">
      <summary>지정한 상위 폴더의 파일에 쓰기 위한 스트림을 검색합니다.</summary>
      <returns>비동기 쓰기 작업을 나타내는 작업입니다.</returns>
      <param name="rootDirectory">쓸 파일이 포함된 Windows 런타임 IStorageFolder 개체입니다.</param>
      <param name="relativePath">쓸 파일의 경로(루트 폴더에 상대적)입니다.</param>
      <param name="creationCollisionOption">만들 파일 이름이 기존 파일 이름과 동일할 때 사용할 동작을 지정하는 Windows 런타임 CreationCollisionOption 열거 값입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rootDirectory" /> 또는 <paramref name="relativePath" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="relativePath" />이 비어 있거나 공백 문자만 있습니다.</exception>
      <exception cref="T:System.IO.IOException">파일을 열 수 없거나 스트림으로 검색할 수 없습니다.</exception>
    </member>
    <member name="T:System.IO.WindowsRuntimeStreamExtensions">
      <summary>Windows 런타임의 스트림과 Windows 스토어 앱용 .NET의 관리되는 스트림 간에 변환하기 위한 확장 메서드를 포함합니다.</summary>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsInputStream(System.IO.Stream)">
      <summary>Windows 스토어 앱용 .NET의 관리되는 스트림을 Windows 런타임의 입력 스트림으로 변환합니다.</summary>
      <returns>변환된 스트림을 나타내는 Windows 런타임 IInputStream 개체입니다.</returns>
      <param name="stream">변환할 스트림입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" />가 null입니다.</exception>
      <exception cref="T:System.NotSupportedException">스트림이 읽기를 지원하지 않습니다.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsOutputStream(System.IO.Stream)">
      <summary>Windows 스토어 앱용 .NET의 관리되는 스트림을 Windows 런타임의 출력 스트림으로 변환합니다.</summary>
      <returns>변환된 스트림을 나타내는 Windows 런타임 IOutputStream 개체입니다.</returns>
      <param name="stream">변환할 스트림입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" />가 null입니다.</exception>
      <exception cref="T:System.NotSupportedException">스트림이 쓰기를 지원하지 않습니다.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsRandomAccessStream(System.IO.Stream)">
      <summary>지정된 스트림을 임의 액세스 스트림으로 변환합니다.</summary>
      <returns>변환된 스트림을 나타내는 Windows 런타임 RandomAccessStream입니다.</returns>
      <param name="stream">변환할 스트림입니다.</param>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStream(Windows.Storage.Streams.IRandomAccessStream)">
      <summary>Windows 런타임의 임의 액세스 스트림을 Windows 스토어 앱용 .NET에서 관리되는 스트림으로 변환합니다.</summary>
      <returns>변환된 스트림입니다.</returns>
      <param name="windowsRuntimeStream">변환할 Windows 런타임 IRandomAccessStream 대상입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" />가 null입니다.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStream(Windows.Storage.Streams.IRandomAccessStream,System.Int32)">
      <summary>지정된 버퍼 크기를 사용하여 Windows 런타임의 임의 액세스 스트림을 Windows 스토어 앱용 .NET의 관리되는 스트림으로 변환합니다.</summary>
      <returns>변환된 스트림입니다.</returns>
      <param name="windowsRuntimeStream">변환할 Windows 런타임 IRandomAccessStream 대상입니다.</param>
      <param name="bufferSize">버퍼의 크기(바이트)입니다.이 값은 음수일 수는 없지만 버퍼링을 사용하지 않도록 0일 수는 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" />가 음수인 경우</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForRead(Windows.Storage.Streams.IInputStream)">
      <summary>Windows 런타임의 입력 스트림을 Windows 스토어 앱용 .NET의 관리되는 스트림으로 변환합니다.</summary>
      <returns>변환된 스트림입니다.</returns>
      <param name="windowsRuntimeStream">변환할 Windows 런타임 IInputStream 대상입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" />가 null입니다.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForRead(Windows.Storage.Streams.IInputStream,System.Int32)">
      <summary>지정된 버퍼 크기를 사용하여 Windows 런타임의 입력 스트림을 Windows 스토어 앱용 .NET의 관리되는 스트림으로 변환합니다.</summary>
      <returns>변환된 스트림입니다.</returns>
      <param name="windowsRuntimeStream">변환할 Windows 런타임 IInputStream 대상입니다.</param>
      <param name="bufferSize">버퍼의 크기(바이트)입니다.이 값은 음수일 수는 없지만 버퍼링을 사용하지 않도록 0일 수는 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" />가 음수인 경우</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForWrite(Windows.Storage.Streams.IOutputStream)">
      <summary>Windows 런타임의 출력 스트림을 Windows 스토어 앱용 .NET의 관리되는 스트림으로 변환합니다.</summary>
      <returns>변환된 스트림입니다.</returns>
      <param name="windowsRuntimeStream">변환할 Windows 런타임 IOutputStream 대상입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" />가 null입니다.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForWrite(Windows.Storage.Streams.IOutputStream,System.Int32)">
      <summary>지정된 버퍼 크기를 사용하여 Windows 런타임의 출력 스트림을 Windows 스토어 앱용 .NET의 관리되는 스트림으로 변환합니다.</summary>
      <returns>변환된 스트림입니다.</returns>
      <param name="windowsRuntimeStream">변환할 Windows 런타임 IOutputStream 대상입니다.</param>
      <param name="bufferSize">버퍼의 크기(바이트)입니다.이 값은 음수일 수는 없지만 버퍼링을 사용하지 않도록 0일 수는 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" />가 음수인 경우</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo">
      <summary>Windows 런타임 비동기 동작 및 작업과 호환되는 관리되는 작업의 구문 표현에 대해 팩터리 메서드를 제공합니다. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run``1(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{``0}})">
      <summary>결과를 반환하는 시작된 작업을 생성하는 함수를 사용하여 Windows 런타임 비동기 작업을 만들고 시작합니다.이 작업은 취소를 지원할 수 있습니다.</summary>
      <returns>
        <paramref name="taskProvider" />에서 생성되는 작업을 나타내는 시작된 Windows.Foundation.IAsyncOperation&lt;TResult&gt; 인스턴스입니다. </returns>
      <param name="taskProvider">작업을 만들고 시작하는 함수를 나타내는 대리자입니다.시작된 작업이 반환되는 Windows 런타임 비동기 작업으로 표현됩니다.함수에서 취소 요청에 대한 알림을 받기 위해 작업을 모니터링할 수 있는 취소 토큰을 전달합니다. 작업에서 취소 상황을 지원하지 않는 경우 토큰을 무시할 수 있습니다.</param>
      <typeparam name="TResult">결과를 반환하는 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="taskProvider" />가 null입니다. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" />는 시작되지 않은 작업을 반환합니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task})">
      <summary>시작된 작업을 생성하는 함수를 사용하여 Windows 런타임 비동기 작업을 만들고 시작합니다.이 작업은 취소를 지원할 수 있습니다.</summary>
      <returns>
        <paramref name="taskProvider" />에서 생성되는 작업을 나타내는 시작된 Windows.Foundation.IAsyncAction 인스턴스입니다. </returns>
      <param name="taskProvider">작업을 만들고 시작하는 함수를 나타내는 대리자입니다.시작된 작업이 반환되는 Windows 런타임 비동기 작업으로 표현됩니다.함수에서 취소 요청에 대한 알림을 받기 위해 작업을 모니터링할 수 있는 취소 토큰을 전달합니다. 작업에서 취소 상황을 지원하지 않는 경우 토큰을 무시할 수 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="taskProvider" />가 null입니다. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" />는 시작되지 않은 작업을 반환합니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run``2(System.Func{System.Threading.CancellationToken,System.IProgress{``1},System.Threading.Tasks.Task{``0}})">
      <summary>결과를 반환하는 시작된 작업을 생성하는 함수를 사용하여 진행률 업데이트를 포함하는 Windows 런타임 비동기 작업을 만들고 시작합니다.이 작업은 취소 및 진행률 보고를 지원할 수 있습니다.</summary>
      <returns>
        <paramref name="taskProvider" />에서 생성되는 작업을 나타내는 시작된 Windows.Foundation.IAsyncOperationWithProgress&lt;TResult,TProgress&gt; 인스턴스입니다. </returns>
      <param name="taskProvider">작업을 만들고 시작하는 함수를 나타내는 대리자입니다.시작된 작업이 반환되는 Windows 런타임 비동기 작업으로 표현됩니다.함수에서 취소 요청에 대한 알림을 받기 위해 작업을 모니터링할 수 있는 취소 토큰과 진행률을 보고하는 인터페이스를 전달합니다. 작업에서 진행률을 보고하거나 취소하지 않는 경우 이러한 인수 중 하나 또는 둘 다 무시할 수 있습니다.</param>
      <typeparam name="TResult">결과를 반환하는 형식입니다. </typeparam>
      <typeparam name="TProgress">진행률 알림에 사용되는 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="taskProvider" />가 null입니다. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" />는 시작되지 않은 작업을 반환합니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run``1(System.Func{System.Threading.CancellationToken,System.IProgress{``0},System.Threading.Tasks.Task})">
      <summary>시작된 작업을 생성하는 함수를 사용하여 진행률 업데이트를 포함하는 Windows 런타임 비동기 작업을 만들고 시작합니다.이 작업은 취소 및 진행률 보고를 지원할 수 있습니다.</summary>
      <returns>
        <paramref name="taskProvider" />에서 생성되는 작업을 나타내는 시작된 Windows.Foundation.IAsyncActionWithProgress&lt;TProgress&gt; 인스턴스입니다. </returns>
      <param name="taskProvider">작업을 만들고 시작하는 함수를 나타내는 대리자입니다.시작된 작업이 반환되는 Windows 런타임 비동기 작업으로 표현됩니다.함수에서 취소 요청에 대한 알림을 받기 위해 작업을 모니터링할 수 있는 취소 토큰과 진행률을 보고하는 인터페이스를 전달합니다. 작업에서 진행률을 보고하거나 취소하지 않는 경우 이러한 인수 중 하나 또는 둘 다 무시할 수 있습니다.</param>
      <typeparam name="TProgress">진행률 알림에 사용되는 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="taskProvider" />가 null입니다. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" />는 시작되지 않은 작업을 반환합니다. </exception>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBuffer">
      <summary>Windows 런타임 IBuffer 인터페이스(Windows.Storage.Streams.IBuffer) 및 모든 추가 필수 인터페이스의 구현을 제공합니다. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBuffer.Create(System.Byte[],System.Int32,System.Int32,System.Int32)">
      <summary>바이트 배열에서 복사된 바이트의 지정된 범위를 포함하는 Windows.Storage.Streams.IBuffer 인터페이스를 반환합니다.지정한 용량이 복사된 바이트 수보다 크면 나머지 버퍼는 0으로 채워집니다.</summary>
      <returns>지정된 범위의 바이트를 포함하는 Windows.Storage.Streams.IBuffer 인터페이스입니다.<paramref name="capacity" />가 <paramref name="length" />보다 클 경우 버퍼의 나머지가 0으로 채워집니다.</returns>
      <param name="data">복사할 바이트 배열입니다. </param>
      <param name="offset">복사를 시작할 <paramref name="data" />의 오프셋입니다. </param>
      <param name="length">복사할 바이트 수입니다. </param>
      <param name="capacity">버퍼에서 보유할 수 있는 최대 바이트 수입니다. 이 수가 <paramref name="length" />보다 크면 버퍼의 나머지 바이트가 0으로 초기화됩니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" />, <paramref name="offset" /> 또는 <paramref name="length" />가 0보다 작습니다. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" />에서 시작하는 <paramref name="data" />에는 <paramref name="length" /> 요소가 들어 있지 않습니다. 또는<paramref name="offset" />부터, <paramref name="data" />에는 <paramref name="capacity" /> 요소가 포함되지 않습니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBuffer.Create(System.Int32)">
      <summary>지정된 최대 용량을 갖는 빈 Windows.Storage.Streams.IBuffer 인터페이스를 반환합니다. </summary>
      <returns>지정된 용량이 있고 Length 속성이 0으로 설정된 Windows.Storage.Streams.IBuffer 인터페이스입니다. </returns>
      <param name="capacity">버퍼에서 저장할 수 있는 최대 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" />가 0보다 작습니다. </exception>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions">
      <summary>Windows 런타임 버퍼에서 작업을 위한 확장 메서드를 제공합니다(Windows.Storage.Streams.IBuffer 인터페이스). </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsBuffer(System.Byte[])">
      <summary>지정된 바이트 배열을 나타내는 Windows.Storage.Streams.IBuffer 인터페이스를 반환합니다. </summary>
      <returns>지정된 바이트 배열을 나타내는 Windows.Storage.Streams.IBuffer 인터페이스입니다. </returns>
      <param name="source">나타낼 배열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsBuffer(System.Byte[],System.Int32,System.Int32)">
      <summary>지정된 바이트 배열의 바이트 범위를 나타내는 Windows.Storage.Streams.IBuffer 인터페이스를 반환합니다. </summary>
      <returns>
        <paramref name="source" />에서 지정된 범위의 바이트를 나타내는 IBuffer 인터페이스입니다.</returns>
      <param name="source">IBuffer로 표현되는 바이트 범위가 포함된 배열입니다. </param>
      <param name="offset">범위가 시작되는 <paramref name="source" />의 오프셋입니다. </param>
      <param name="length">IBuffer로 표현되는 범위의 길이입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 또는 <paramref name="length" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.ArgumentException">배열의 크기가 IBuffer에 대한 백업 저장소의 역할을 할 정도로 충분하지 않습니다. 즉, <paramref name="offset" />에서 시작하는 <paramref name="source" />의 바이트 수는 <paramref name="length" />  미만입니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsBuffer(System.Byte[],System.Int32,System.Int32,System.Int32)">
      <summary>지정된 바이트 배열의 바이트 범위를 나타내는 Windows.Storage.Streams.IBuffer 인터페이스를 반환합니다.IBuffer의 Length 속성을 용량보다 작은 값으로 필요에 따라 설정합니다.</summary>
      <returns>지정된 Length 속성 값이 있는 <paramref name="source" />의 지정된 바이트 범위를 나타내는 IBuffer 인터페이스입니다. </returns>
      <param name="source">IBuffer로 표현되는 바이트 범위가 포함된 배열입니다. </param>
      <param name="offset">범위가 시작되는 <paramref name="source" />의 오프셋입니다. </param>
      <param name="length">IBuffer의 Length 속성의 값입니다. </param>
      <param name="capacity">IBuffer로 표현되는 범위의 크기입니다.IBuffer의 Capacity 속성이 이 값에 설정되어 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" />, <paramref name="length" /> 또는 <paramref name="capacity" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="length" />가 <paramref name="capacity" />보다 큰 경우 또는배열의 크기가 IBuffer에 대한 백업 저장소의 역할을 할 정도로 충분하지 않습니다. 즉, <paramref name="offset" />에서 시작하는 <paramref name="source" />의 바이트 수는 <paramref name="length" /> 또는 <paramref name="capacity" /> 미만입니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsStream(Windows.Storage.Streams.IBuffer)">
      <summary>지정된 Windows.Storage.Streams.IBuffer 인터페이스가 나타내는 동일한 메모리를 나타내는 스트림을 반환합니다. </summary>
      <returns>지정된 Windows.Storage.Streams.IBuffer 인터페이스가 나타내는 동일한 메모리를 나타내는 스트림입니다. </returns>
      <param name="source">스트림으로 나타낼 IBuffer입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(System.Byte[],System.Int32,Windows.Storage.Streams.IBuffer,System.UInt32,System.Int32)">
      <summary>원본 배열의 시작 인덱스, 대상 버퍼의 시작 인덱스 및 복사할 바이트 수를 지정하여 원본 배열의 바이트를 대상 버퍼(Windows.Storage.Streams.IBuffer)에 복사합니다.메서드는 대상 버퍼의 Length 속성을 업데이트하지 않습니다.</summary>
      <param name="source">데이터를 복사할 배열입니다. </param>
      <param name="sourceIndex">데이터 복사를 시작할 <paramref name="source" />의 인덱스입니다. </param>
      <param name="destination">데이터를 복사할 버퍼입니다. </param>
      <param name="destinationIndex">데이터 복사를 시작할 <paramref name="destination" />의 인덱스입니다. </param>
      <param name="count">복사할 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 또는 <paramref name="destination" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" />, <paramref name="sourceIndex" /> 또는 <paramref name="destinationIndex" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceIndex" />가 <paramref name="source" />의 길이보다 크거나 같은 경우 또는<paramref name="sourceIndex" />부터 시작하는 <paramref name="source" />의 바이트 수가 <paramref name="count" />보다 작습니다. 또는<paramref name="destinationIndex" />에서 시작하여 <paramref name="count" />바이트를 복사하면 <paramref name="destination" />의 용량을 초과합니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(System.Byte[],Windows.Storage.Streams.IBuffer)">
      <summary>오프셋 0에서 시작하여 원본 배열의 모든 바이트를 대상 버퍼(Windows.Storage.Streams.IBuffer)에 복사합니다.메서드는 대상 버퍼의 길이를 업데이트하지 않습니다.</summary>
      <param name="source">데이터를 복사할 배열입니다. </param>
      <param name="destination">데이터를 복사할 버퍼입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 또는 <paramref name="destination" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="source" />의 크기가 <paramref name="destination" />의 용량을 초과합니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,System.Byte[])">
      <summary>오프셋 0에서 시작하여 원본 버퍼(Windows.Storage.Streams.IBuffer)의 모든 바이트를 대상 배열에 복사합니다. </summary>
      <param name="source">데이터를 복사해 올 버퍼입니다. </param>
      <param name="destination">데이터를 복사할 대상 배열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 또는 <paramref name="destination" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="source" />의 크기가 <paramref name="destination" />의 크기를 초과합니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,System.UInt32,System.Byte[],System.Int32,System.Int32)">
      <summary>원본 버퍼의 시작 인덱스, 대상 배열의 시작 인덱스 및 복사할 바이트 수를 지정하여 원본 버퍼(Windows.Storage.Streams.IBuffer)의 바이트를 대상 배열에 복사합니다. </summary>
      <param name="source">데이터를 복사해 올 버퍼입니다. </param>
      <param name="sourceIndex">데이터 복사를 시작할 <paramref name="source" />의 인덱스입니다. </param>
      <param name="destination">데이터를 복사할 대상 배열입니다. </param>
      <param name="destinationIndex">데이터 복사를 시작할 <paramref name="destination" />의 인덱스입니다. </param>
      <param name="count">복사할 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 또는 <paramref name="destination" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" />, <paramref name="sourceIndex" /> 또는 <paramref name="destinationIndex" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceIndex" />가 <paramref name="source" />의 용량보다 크거나 같은 경우 또는<paramref name="destinationIndex" />가 <paramref name="destination" />의 길이보다 크거나 같은 경우 또는<paramref name="sourceIndex" />부터 시작하는 <paramref name="source" />의 바이트 수가 <paramref name="count" />보다 작습니다. 또는<paramref name="destinationIndex" />에서 시작하여 <paramref name="count" />바이트를 복사하면 <paramref name="destination" />의 크기를 초과합니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,System.UInt32,Windows.Storage.Streams.IBuffer,System.UInt32,System.UInt32)">
      <summary>원본의 시작 인덱스, 대상의 시작 인덱스 및 복사할 바이트 수를 지정하여 원본 버퍼(Windows.Storage.Streams.IBuffer)의 바이트를 대상 버퍼에 복사합니다.</summary>
      <param name="source">데이터를 복사해 올 버퍼입니다. </param>
      <param name="sourceIndex">데이터 복사를 시작할 <paramref name="source" />의 인덱스입니다. </param>
      <param name="destination">데이터를 복사할 버퍼입니다. </param>
      <param name="destinationIndex">데이터 복사를 시작할 <paramref name="destination" />의 인덱스입니다. </param>
      <param name="count">복사할 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 또는 <paramref name="destination" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" />, <paramref name="sourceIndex" /> 또는 <paramref name="destinationIndex" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceIndex" />가 <paramref name="source" />의 용량보다 크거나 같은 경우 또는<paramref name="destinationIndex" />가 <paramref name="destination" />의 용량보다 크거나 같은 경우 또는<paramref name="sourceIndex" />부터 시작하는 <paramref name="source" />의 바이트 수가 <paramref name="count" />보다 작습니다. 또는<paramref name="destinationIndex" />에서 시작하여 <paramref name="count" />바이트를 복사하면 <paramref name="destination" />의 용량을 초과합니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,Windows.Storage.Streams.IBuffer)">
      <summary>오프셋 0에서 시작하여 원본 버퍼(Windows.Storage.Streams.IBuffer)의 모든 바이트를 대상 버퍼에 복사합니다. </summary>
      <param name="source">소스 버퍼입니다. </param>
      <param name="destination">대상 버퍼입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 또는 <paramref name="destination" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="source" />의 크기가 <paramref name="destination" />의 용량을 초과합니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.GetByte(Windows.Storage.Streams.IBuffer,System.UInt32)">
      <summary>지정된 Windows.Storage.Streams.IBuffer 인터페이스의 지정된 오프셋에서 바이트를 반환합니다.</summary>
      <returns>지정한 인덱스의 바이트입니다. </returns>
      <param name="source">바이트를 가져오는 버퍼입니다. </param>
      <param name="byteOffset">바이트의 오프셋입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteOffset" />이 0보다 작습니다. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="byteOffset" />이 <paramref name="source" />의 용량보다 크거나 같습니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.GetWindowsRuntimeBuffer(System.IO.MemoryStream)">
      <summary>같은 메모리를 지정된 메모리 스트림으로 나타내는 Windows.Storage.Streams.IBuffer 인터페이스를 반환합니다. </summary>
      <returns>지정된 메모리 스트림을 지원하는 동일한 메모리의 지원을 받는 Windows.Storage.Streams.IBuffer 인터페이스입니다.</returns>
      <param name="underlyingStream">IBuffer의 백업 메모리를 제공하는 스트림입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.GetWindowsRuntimeBuffer(System.IO.MemoryStream,System.Int32,System.Int32)">
      <summary>지정된 메모리 스트림을 나타내는 메모리 내 영역을 나타내는 Windows.Storage.Streams.IBuffer 인터페이스를 반환합니다. </summary>
      <returns>지정된 메모리 스트림을 지원하는 메모리 내 영역의 지원을 받는 Windows.Storage.Streams.IBuffer 인터페이스입니다. </returns>
      <param name="underlyingStream">IBuffer와 메모리를 공유하는 스트림입니다. </param>
      <param name="positionInStream">
        <paramref name="underlyingStream" />의 공유 메모리 영역의 위치입니다. </param>
      <param name="length">공유 메모리 영역의 최대 크기입니다.<paramref name="positionInStream" />에서 시작하는 <paramref name="underlyingStream" />의 바이트 수가 <paramref name="length" />보다 작은 경우 반환되는  IBuffer는 사용 가능한 바이트만 나타냅니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="underlyingStream" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="positionInStream" /> 또는 <paramref name="length" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="positionInStream" />은 <paramref name="source" />의 끝을 지납니다. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="underlyingStream" />은 내부 메모리 버퍼를 노출할 수 없습니다. </exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="underlyingStream" />이 닫힌 경우 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.IsSameData(Windows.Storage.Streams.IBuffer,Windows.Storage.Streams.IBuffer)">
      <summary>두 버퍼(Windows.Storage.Streams.IBuffer 개체)가 같은 기본 메모리 영역을 나타내는지 여부를 나타내는 값을 반환합니다. </summary>
      <returns>두 버퍼로 표현되는 메모리 영역에 동일한 시작점이 있으면 true이고, 그렇지 않으면 false입니다. </returns>
      <param name="buffer">첫 번째 버퍼입니다. </param>
      <param name="otherBuffer">두 번째 버퍼입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.ToArray(Windows.Storage.Streams.IBuffer)">
      <summary>지정된 버퍼(Windows.Storage.Streams.IBuffer)의 콘텐츠에서 생성되는 새 배열을 반환합니다.배열의 크기는 IBuffer의 Length 속성의 값입니다.</summary>
      <returns>오프셋 0에서 시작하고 IBuffer의 Length 속성 값과 같은 수의 바이트를 포함하는 지정된 IBuffer의 바이트가 포함된 바이트 배열입니다. </returns>
      <param name="source">해당 내용으로 새 배열을 채우는 버퍼입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.ToArray(Windows.Storage.Streams.IBuffer,System.UInt32,System.Int32)">
      <summary>지정한 버퍼(Windows.Storage.Streams.IBuffer)의 콘텐츠에서 생성되는 새 배열을 반환합니다. 이 경우 버퍼는 지정한 오프셋에서 시작하여 지정한 바이트 만큼 보유하게 됩니다. </summary>
      <returns>지정된 바이트 범위를 포함하는 바이트 배열입니다. </returns>
      <param name="source">해당 내용으로 새 배열을 채우는 버퍼입니다. </param>
      <param name="sourceIndex">데이터 복사를 시작할 <paramref name="source" />의 인덱스입니다. </param>
      <param name="count">복사할 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> 또는 <paramref name="sourceIndex" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceIndex" />가 <paramref name="source" />의 용량보다 크거나 같은 경우 또는<paramref name="sourceIndex" />부터 시작하는 <paramref name="source" />의 바이트 수가 <paramref name="count" />보다 작습니다. </exception>
    </member>
    <member name="T:Windows.Foundation.Point">
      <summary>2차원 공간에서 X 및 Y 좌표 쌍을 나타냅니다.특정 속성 사용에 대한 논리적 점을 나타낼 수도 있습니다.</summary>
    </member>
    <member name="M:Windows.Foundation.Point.#ctor(System.Double,System.Double)">
      <summary>지정된 값이 포함된 <see cref="T:Windows.Foundation.Point" /> 구조체를 초기화합니다. </summary>
      <param name="x">
        <see cref="T:Windows.Foundation.Point" /> 구조체의 X 좌표 값입니다. </param>
      <param name="y">
        <see cref="T:Windows.Foundation.Point" /> 구조체의 Y 좌표 값입니다. </param>
    </member>
    <member name="M:Windows.Foundation.Point.Equals(System.Object)">
      <summary>지정된 개체가 <see cref="T:Windows.Foundation.Point" />인지 여부와 이 <see cref="T:Windows.Foundation.Point" />와 동일한 값이 들어 있는지 여부를 확인합니다. </summary>
      <returns>
        <paramref name="obj" />가 <see cref="T:Windows.Foundation.Point" />이고 <see cref="P:Windows.Foundation.Point.X" /> 및 <see cref="P:Windows.Foundation.Point.Y" /> 값이 이 <see cref="T:Windows.Foundation.Point" />와 동일하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="o">비교할 개체입니다.</param>
    </member>
    <member name="M:Windows.Foundation.Point.Equals(Windows.Foundation.Point)">
      <summary>두 <see cref="T:Windows.Foundation.Point" /> 구조체가 같은지 여부를 비교합니다.</summary>
      <returns>두 <see cref="T:Windows.Foundation.Point" /> 구조체의 <see cref="P:Windows.Foundation.Point.X" /> 및 <see cref="P:Windows.Foundation.Point.Y" /> 값이 서로 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">이 인스턴스와 비교할 점입니다.</param>
    </member>
    <member name="M:Windows.Foundation.Point.GetHashCode">
      <summary>이 <see cref="T:Windows.Foundation.Point" />의 해시 코드를 반환합니다.</summary>
      <returns>이 <see cref="T:Windows.Foundation.Point" /> 구조체의 해시 코드입니다.</returns>
    </member>
    <member name="M:Windows.Foundation.Point.op_Equality(Windows.Foundation.Point,Windows.Foundation.Point)">
      <summary>두 <see cref="T:Windows.Foundation.Point" /> 구조체가 같은지 여부를 비교합니다.</summary>
      <returns>
        <paramref name="point1" />과 <paramref name="point2" />의 <see cref="P:Windows.Foundation.Point.X" /> 값과 <see cref="P:Windows.Foundation.Point.Y" /> 값이 모두 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="point1">비교할 첫 번째 <see cref="T:Windows.Foundation.Point" /> 구조체입니다.</param>
      <param name="point2">비교할 두 번째 <see cref="T:Windows.Foundation.Point" /> 구조체입니다.</param>
    </member>
    <member name="M:Windows.Foundation.Point.op_Inequality(Windows.Foundation.Point,Windows.Foundation.Point)">
      <summary>두 <see cref="T:Windows.Foundation.Point" /> 구조체가 다른지 비교합니다.</summary>
      <returns>
        <paramref name="point1" />과 <paramref name="point2" />의 <see cref="P:Windows.Foundation.Point.X" /> 또는 <see cref="P:Windows.Foundation.Point.Y" /> 값이 서로 다르면 true이고, <paramref name="point1" />과 <paramref name="point2" />의 <see cref="P:Windows.Foundation.Point.X" /> 및 <see cref="P:Windows.Foundation.Point.Y" /> 값이 서로 같으면 false입니다.</returns>
      <param name="point1">비교할 첫 번째 점입니다.</param>
      <param name="point2">비교할 두 번째 점입니다.</param>
    </member>
    <member name="M:Windows.Foundation.Point.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>이 멤버에 대한 설명은 <see cref="M:System.IFormattable.ToString(System.String,System.IFormatProvider)" />을 참조하십시오.</summary>
      <returns>지정된 형식의 현재 인스턴스 값이 포함된 문자열입니다.</returns>
      <param name="format">사용할 형식을 지정하는 문자열입니다. 또는 IFormattable 구현 형식에 대해 정의된 기본 형식을 사용하기 위한 null입니다. </param>
      <param name="provider">값의 형식을 지정하는 데 사용할 IFormatProvider입니다. 또는 운영 체제의 현재 로캘 설정에서 숫자 형식 정보를 가져오기 위한 null입니다. </param>
    </member>
    <member name="M:Windows.Foundation.Point.ToString">
      <summary>이 <see cref="T:Windows.Foundation.Point" />의 <see cref="T:System.String" /> 표현을 만듭니다. </summary>
      <returns>이 <see cref="T:Windows.Foundation.Point" /> 구조체의 <see cref="P:Windows.Foundation.Point.X" /> 및 <see cref="P:Windows.Foundation.Point.Y" /> 값이 포함된 <see cref="T:System.String" />입니다.</returns>
    </member>
    <member name="M:Windows.Foundation.Point.ToString(System.IFormatProvider)">
      <summary>이 <see cref="T:Windows.Foundation.Point" />의 <see cref="T:System.String" /> 표현을 만듭니다. </summary>
      <returns>이 <see cref="T:Windows.Foundation.Point" /> 구조체의 <see cref="P:Windows.Foundation.Point.X" /> 및 <see cref="P:Windows.Foundation.Point.Y" /> 값이 포함된 <see cref="T:System.String" />입니다.</returns>
      <param name="provider">문화권별 형식 지정 정보입니다.</param>
    </member>
    <member name="P:Windows.Foundation.Point.X">
      <summary>이 <see cref="T:Windows.Foundation.Point" /> 구조체의 <see cref="P:Windows.Foundation.Point.X" /> 좌표 값을 가져오거나 설정합니다. </summary>
      <returns>이 <see cref="T:Windows.Foundation.Point" /> 구조체의 <see cref="P:Windows.Foundation.Point.X" /> 좌표 값입니다.기본값은 0입니다.</returns>
    </member>
    <member name="P:Windows.Foundation.Point.Y">
      <summary>이 <see cref="T:Windows.Foundation.Point" />의 <see cref="P:Windows.Foundation.Point.Y" /> 좌표 값을 가져오거나 설정합니다. </summary>
      <returns>이 <see cref="T:Windows.Foundation.Point" /> 구조체의 <see cref="P:Windows.Foundation.Point.Y" /> 좌표 값입니다.  기본값은 0입니다.</returns>
    </member>
    <member name="T:Windows.Foundation.Rect">
      <summary>사각형의 너비, 높이 및 원점을 설명합니다. </summary>
    </member>
    <member name="M:Windows.Foundation.Rect.#ctor(System.Double,System.Double,System.Double,System.Double)">
      <summary>지정된 X 좌표, Y 좌표, 너비 및 높이를 갖는 <see cref="T:Windows.Foundation.Rect" /> 구조체를 초기화합니다. </summary>
      <param name="x">사각형 왼쪽 위 모퉁이의 x좌표입니다.</param>
      <param name="y">사각형 왼쪽 위 모퉁이의 y좌표입니다.</param>
      <param name="width">사각형의 너비입니다.</param>
      <param name="height">사각형의 높이입니다.</param>
      <exception cref="T:System.ArgumentException">width 또는 height가 0보다 작은 경우</exception>
    </member>
    <member name="M:Windows.Foundation.Rect.#ctor(Windows.Foundation.Point,Windows.Foundation.Point)">
      <summary>지정된 두 지점을 정확히 포함할 수 있는 크기의 <see cref="T:Windows.Foundation.Rect" /> 구조체를 초기화합니다. </summary>
      <param name="point1">새 사각형에 들어 있어야 하는 첫 번째 지점입니다.</param>
      <param name="point2">새 사각형에 들어 있어야 하는 두 번째 지점입니다.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.#ctor(Windows.Foundation.Point,Windows.Foundation.Size)">
      <summary>원점 및 크기를 기준으로 <see cref="T:Windows.Foundation.Rect" /> 구조체를 초기화합니다. </summary>
      <param name="location">새 <see cref="T:Windows.Foundation.Rect" />의 원점입니다.</param>
      <param name="size">새 <see cref="T:Windows.Foundation.Rect" />의 크기입니다.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Bottom">
      <summary>사각형 아래쪽의 y축 값을 가져옵니다. </summary>
      <returns>사각형 아래쪽의 y축 값입니다.사각형이 비어 있으면 값이 <see cref="F:System.Double.NegativeInfinity" />입니다.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.Contains(Windows.Foundation.Point)">
      <summary>
        <see cref="T:Windows.Foundation.Rect" />에서 설명하는 사각형에 지정된 점이 들어 있는지 여부를 나타냅니다.</summary>
      <returns>
        <see cref="T:Windows.Foundation.Rect" />에서 설명하는 사각형에 지정된 점이 들어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="point">확인할 지점입니다.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Empty">
      <summary>위치와 넓이가 없는 사각형을 나타내는 특수 값을 가져옵니다. </summary>
      <returns>
        <see cref="P:Windows.Foundation.Rect.X" /> 및 <see cref="P:Windows.Foundation.Rect.Y" /> 속성 값이 <see cref="F:System.Double.PositiveInfinity" />이고 <see cref="P:Windows.Foundation.Rect.Width" /> 및 <see cref="P:Windows.Foundation.Rect.Height" /> 속성 값이 <see cref="F:System.Double.NegativeInfinity" />인 빈 사각형입니다.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.Equals(System.Object)">
      <summary>지정된 개체가 현재 <see cref="T:Windows.Foundation.Rect" />와 같은지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="o" />가 <see cref="T:Windows.Foundation.Rect" />이고 현재 <see cref="T:Windows.Foundation.Rect" />와 x,y,width,height 값이 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="o">현재 사각형과 비교할 개체입니다.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.Equals(Windows.Foundation.Rect)">
      <summary>지정된 <see cref="T:Windows.Foundation.Rect" />가 현재 <see cref="T:Windows.Foundation.Rect" />와 같은지 여부를 나타냅니다. </summary>
      <returns>지정된 <see cref="T:Windows.Foundation.Rect" />의 x,y,width,height 속성 값이 현재 <see cref="T:Windows.Foundation.Rect" />와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">현재 사각형과 비교할 사각형입니다.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.GetHashCode">
      <summary>
        <see cref="T:Windows.Foundation.Rect" />의 해시 코드를 만듭니다. </summary>
      <returns>현재 <see cref="T:Windows.Foundation.Rect" /> 구조체의 해시 코드입니다.</returns>
    </member>
    <member name="P:Windows.Foundation.Rect.Height">
      <summary>사각형의 높이를 가져오거나 설정합니다. </summary>
      <returns>사각형의 높이를 나타내는 값입니다.기본값은 0입니다.</returns>
      <exception cref="T:System.ArgumentException">0보다 작은 값을 지정한 경우</exception>
    </member>
    <member name="M:Windows.Foundation.Rect.Intersect(Windows.Foundation.Rect)">
      <summary>현재 <see cref="T:Windows.Foundation.Rect" />가 나타내는 사각형과 지정된 <see cref="T:Windows.Foundation.Rect" />가 나타내는 사각형이 겹치는 부분을 찾아서 그 결과를 현재 <see cref="T:Windows.Foundation.Rect" />로 저장합니다. </summary>
      <param name="rect">현재 사각형과 겹치는 사각형입니다.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.IsEmpty">
      <summary>사각형이 <see cref="P:Windows.Foundation.Rect.Empty" /> 사각형인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>사각형이 <see cref="P:Windows.Foundation.Rect.Empty" /> 사각형이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:Windows.Foundation.Rect.Left">
      <summary>사각형 왼쪽의 x축 값을 가져옵니다. </summary>
      <returns>사각형 왼쪽의 x축 값입니다.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.op_Equality(Windows.Foundation.Rect,Windows.Foundation.Rect)">
      <summary>두 <see cref="T:Windows.Foundation.Rect" /> 구조체가 같은지 여부를 비교합니다.</summary>
      <returns>두 <see cref="T:Windows.Foundation.Rect" /> 구조체의 x,y,width,height 속성 값이 같으면 true이고, 그렇지 않으면 false입니다</returns>
      <param name="rect1">비교할 첫 번째 사각형입니다.</param>
      <param name="rect2">비교할 두 번째 사각형입니다.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.op_Inequality(Windows.Foundation.Rect,Windows.Foundation.Rect)">
      <summary>두 <see cref="T:Windows.Foundation.Rect" /> 구조체가 다른지 여부를 비교합니다.  </summary>
      <returns>두 <see cref="T:Windows.Foundation.Rect" /> 구조체의 x,y,width,height 속성 값이 같지 않으면 true이고, 그렇지 않으면 false입니다</returns>
      <param name="rect1">비교할 첫 번째 사각형입니다.</param>
      <param name="rect2">비교할 두 번째 사각형입니다.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Right">
      <summary>사각형 오른쪽의 x축 값을 가져옵니다.  </summary>
      <returns>사각형 오른쪽의 x축 값입니다.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>이 멤버에 대한 설명은 <see cref="M:System.IFormattable.ToString(System.String,System.IFormatProvider)" />을 참조하십시오.</summary>
      <returns>지정된 형식의 현재 인스턴스 값이 포함된 문자열입니다.</returns>
      <param name="format">사용할 형식을 지정하는 문자열입니다. 또는 IFormattable 구현 형식에 대해 정의된 기본 형식을 사용하기 위한 null입니다. </param>
      <param name="provider">값의 형식을 지정하는 데 사용할 IFormatProvider입니다. 또는 운영 체제의 현재 로캘 설정에서 숫자 형식 정보를 가져오기 위한 null입니다. </param>
    </member>
    <member name="P:Windows.Foundation.Rect.Top">
      <summary>사각형 위쪽의 y축 좌표를 가져옵니다. </summary>
      <returns>사각형 위쪽의 y축 좌표입니다.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.ToString">
      <summary>
        <see cref="T:Windows.Foundation.Rect" /> 구조체의 문자열 표현을 반환합니다. </summary>
      <returns>현재 <see cref="T:Windows.Foundation.Rect" /> 구조체의 문자열 표현입니다.문자열의 형식은 "<see cref="P:Windows.Foundation.Rect.X" />,<see cref="P:Windows.Foundation.Rect.Y" />,<see cref="P:Windows.Foundation.Rect.Width" />,<see cref="P:Windows.Foundation.Rect.Height" />"입니다.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.ToString(System.IFormatProvider)">
      <summary>지정된 형식 공급자를 사용하여 사각형의 문자열 표현을 반환합니다. </summary>
      <returns>지정된 형식 공급자에 따라 결정된 현재 사각형의 문자열 표현입니다.</returns>
      <param name="provider">문화권별 형식 지정 정보입니다.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.Union(Windows.Foundation.Point)">
      <summary>지정된 점을 정확히 포함할 수 있는 크기로 현재 <see cref="T:Windows.Foundation.Rect" />를 확장합니다. </summary>
      <param name="point">포함할 지점입니다.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.Union(Windows.Foundation.Rect)">
      <summary>지정된 사각형을 정확히 포함할 수 있는 크기로 현재 <see cref="T:Windows.Foundation.Rect" />를 확장합니다. </summary>
      <param name="rect">포함할 사각형입니다.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Width">
      <summary>사각형의 너비를 가져오거나 설정합니다.  </summary>
      <returns>사각형의 너비(픽셀)를 나타내는 값입니다.기본값은 0입니다.</returns>
      <exception cref="T:System.ArgumentException">0보다 작은 값을 지정한 경우</exception>
    </member>
    <member name="P:Windows.Foundation.Rect.X">
      <summary>사각형 왼쪽의 x축 값을 가져오거나 설정합니다. </summary>
      <returns>사각형 왼쪽의 x축 값입니다.이 값은 좌표 공간 내의 픽셀로 해석됩니다.</returns>
    </member>
    <member name="P:Windows.Foundation.Rect.Y">
      <summary>사각형 위쪽의 y축 값을 가져오거나 설정합니다. </summary>
      <returns>사각형 위쪽의 y축 값입니다.이 값은 좌표 공간 내의 픽셀로 해석됩니다.</returns>
    </member>
    <member name="T:Windows.Foundation.Size">
      <summary>개체의 너비와 높이를 설명합니다. </summary>
    </member>
    <member name="M:Windows.Foundation.Size.#ctor(System.Double,System.Double)">
      <summary>
        <see cref="T:Windows.Foundation.Size" /> 구조체의 새 인스턴스를 초기화하여 초기 <paramref name="width" /> 및 <paramref name="height" />를 할당합니다.</summary>
      <param name="width">
        <see cref="T:Windows.Foundation.Size" /> 인스턴스의 초기 너비입니다.</param>
      <param name="height">
        <see cref="T:Windows.Foundation.Size" /> 인스턴스의 초기 높이입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="width" /> 또는 <paramref name="height" />가 0보다 작은 경우</exception>
    </member>
    <member name="P:Windows.Foundation.Size.Empty">
      <summary>빈 정적 <see cref="T:Windows.Foundation.Size" />를 나타내는 값을 가져옵니다. </summary>
      <returns>
        <see cref="T:Windows.Foundation.Size" />의 빈 인스턴스입니다.</returns>
    </member>
    <member name="M:Windows.Foundation.Size.Equals(System.Object)">
      <summary>개체와 <see cref="T:Windows.Foundation.Size" /> 인스턴스가 같은지 비교합니다. </summary>
      <returns>크기가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="o">비교할 <see cref="T:System.Object" />입니다.</param>
    </member>
    <member name="M:Windows.Foundation.Size.Equals(Windows.Foundation.Size)">
      <summary>
        <see cref="T:Windows.Foundation.Size" />의 인스턴스와 값이 같은지 비교합니다. </summary>
      <returns>
        <see cref="T:Windows.Foundation.Size" />의 인스턴스가 서로 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">
        <see cref="T:Windows.Foundation.Size" />의 이 현재 인스턴스와 비교할 크기입니다.</param>
    </member>
    <member name="M:Windows.Foundation.Size.GetHashCode">
      <summary>
        <see cref="T:Windows.Foundation.Size" />의 이 인스턴스에 대한 해시 코드를 가져옵니다. </summary>
      <returns>
        <see cref="T:Windows.Foundation.Size" />의 이 인스턴스에 대한 해시 코드입니다.</returns>
    </member>
    <member name="P:Windows.Foundation.Size.Height">
      <summary>이 <see cref="T:Windows.Foundation.Size" /> 인스턴스의 높이를 가져오거나 설정합니다. </summary>
      <returns>이 <see cref="T:Windows.Foundation.Size" /> 인스턴스의 <see cref="P:Windows.Foundation.Size.Height" />(픽셀 단위)입니다.기본값은 0입니다.값은 음수일 수 없습니다.</returns>
      <exception cref="T:System.ArgumentException">0보다 작은 값을 지정한 경우</exception>
    </member>
    <member name="P:Windows.Foundation.Size.IsEmpty">
      <summary>
        <see cref="T:Windows.Foundation.Size" />의 이 인스턴스가 <see cref="P:Windows.Foundation.Size.Empty" />인지 여부를 나타내는 값을 가져옵니다. </summary>
      <returns>이 크기 인스턴스가 <see cref="P:Windows.Foundation.Size.Empty" />이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:Windows.Foundation.Size.op_Equality(Windows.Foundation.Size,Windows.Foundation.Size)">
      <summary>
        <see cref="T:Windows.Foundation.Size" />의 두 인스턴스를 비교하여 같은지 여부를 확인합니다. </summary>
      <returns>
        <see cref="T:Windows.Foundation.Size" />의 두 인스턴스가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="size1">비교할 첫 번째 <see cref="T:Windows.Foundation.Size" /> 인스턴스입니다.</param>
      <param name="size2">비교할 두 번째 <see cref="T:Windows.Foundation.Size" /> 인스턴스입니다.</param>
    </member>
    <member name="M:Windows.Foundation.Size.op_Inequality(Windows.Foundation.Size,Windows.Foundation.Size)">
      <summary>
        <see cref="T:Windows.Foundation.Size" />의 두 인스턴스를 비교하여 같지 않은지 여부를 확인합니다. </summary>
      <returns>
        <see cref="T:Windows.Foundation.Size" />의 인스턴스가 서로 다르면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="size1">비교할 첫 번째 <see cref="T:Windows.Foundation.Size" /> 인스턴스입니다.</param>
      <param name="size2">비교할 두 번째 <see cref="T:Windows.Foundation.Size" /> 인스턴스입니다.</param>
    </member>
    <member name="M:Windows.Foundation.Size.ToString">
      <summary>이 <see cref="T:Windows.Foundation.Size" />의 문자열 표현을 반환합니다.</summary>
      <returns>이 <see cref="T:Windows.Foundation.Size" />의 문자열 표현입니다.</returns>
    </member>
    <member name="P:Windows.Foundation.Size.Width">
      <summary>이 <see cref="T:Windows.Foundation.Size" /> 인스턴스의 너비를 가져오거나 설정합니다. </summary>
      <returns>이 <see cref="T:Windows.Foundation.Size" /> 인스턴스의 <see cref="P:Windows.Foundation.Size.Width" />(픽셀 단위)입니다.기본값은 0입니다.값은 음수일 수 없습니다.</returns>
      <exception cref="T:System.ArgumentException">0보다 작은 값을 지정한 경우</exception>
    </member>
    <member name="T:Windows.UI.Color">
      <summary>알파, 빨강, 녹색 및 파랑 채널로 색을 설명합니다. </summary>
    </member>
    <member name="P:Windows.UI.Color.A">
      <summary>색의 sRGB 알파 채널 값을 가져오거나 설정합니다. </summary>
      <returns>0에서 255 사이의 값으로 표시되는 색의 sRGB 알파 채널 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Color.B">
      <summary>색의 sRGB 파랑 채널 값을 가져오거나 설정합니다. </summary>
      <returns>0에서 255 사이의 값으로 표시되는 sRGB 파랑 채널 값입니다.</returns>
    </member>
    <member name="M:Windows.UI.Color.Equals(System.Object)">
      <summary>지정한 개체가 <see cref="T:Windows.UI.Color" /> 구조체이면서 현재 색과 동일한지 여부를 테스트합니다. </summary>
      <returns>지정한 개체가 <see cref="T:Windows.UI.Color" /> 구조체이고 현재 <see cref="T:Windows.UI.Color" />구조체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="o">현재 <see cref="T:Windows.UI.Color" /> 구조체와 비교할 개체입니다.</param>
    </member>
    <member name="M:Windows.UI.Color.Equals(Windows.UI.Color)">
      <summary>지정한 <see cref="T:Windows.UI.Color" /> 구조체가 현재 색과 같은지 여부를 테스트합니다.</summary>
      <returns>지정한 <see cref="T:Windows.UI.Color" /> 구조체가 현재 <see cref="T:Windows.UI.Color" /> 구조체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="color">현재 <see cref="T:Windows.UI.Color" /> 구조체와 비교할 <see cref="T:Windows.UI.Color" /> 구조체입니다.</param>
    </member>
    <member name="M:Windows.UI.Color.FromArgb(System.Byte,System.Byte,System.Byte,System.Byte)">
      <summary>지정한 sRGB 알파 채널 값과 색 채널 값을 사용하여 새 <see cref="T:Windows.UI.Color" /> 구조체를 만듭니다. </summary>
      <returns>지정한 값을 가진 <see cref="T:Windows.UI.Color" /> 구조체입니다.</returns>
      <param name="a">새 색의 알파 채널 <see cref="P:Windows.UI.Color.A" />입니다.이 값은 0에서 255 사이여야 합니다.</param>
      <param name="r">새 색의 빨강 채널 <see cref="P:Windows.UI.Color.R" />입니다.이 값은 0에서 255 사이여야 합니다.</param>
      <param name="g">새 색의 녹색 채널 <see cref="P:Windows.UI.Color.G" />입니다.이 값은 0에서 255 사이여야 합니다.</param>
      <param name="b">새 색의 파랑 채널 <see cref="P:Windows.UI.Color.B" />입니다.이 값은 0에서 255 사이여야 합니다.</param>
    </member>
    <member name="P:Windows.UI.Color.G">
      <summary>색의 sRGB 녹색 채널 값을 가져오거나 설정합니다. </summary>
      <returns>0에서 255 사이의 값으로 표시되는 sRGB 녹색 채널 값입니다.</returns>
    </member>
    <member name="M:Windows.UI.Color.GetHashCode">
      <summary>현재 <see cref="T:Windows.UI.Color" /> 구조체의 해시 코드를 가져옵니다. </summary>
      <returns>현재 <see cref="T:Windows.UI.Color" /> 구조체의 해시 코드입니다.</returns>
    </member>
    <member name="M:Windows.UI.Color.op_Equality(Windows.UI.Color,Windows.UI.Color)">
      <summary>두 <see cref="T:Windows.UI.Color" /> 구조체가 같은지 여부를 테스트합니다. </summary>
      <returns>
        <paramref name="color1" />과 <paramref name="color2" />가 똑같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="color1">비교할 첫 번째 <see cref="T:Windows.UI.Color" /> 구조체입니다.</param>
      <param name="color2">비교할 두 번째 <see cref="T:Windows.UI.Color" /> 구조체입니다.</param>
    </member>
    <member name="M:Windows.UI.Color.op_Inequality(Windows.UI.Color,Windows.UI.Color)">
      <summary>두 <see cref="T:Windows.UI.Color" /> 구조체가 같지 않은지 여부를 테스트합니다. </summary>
      <returns>
        <paramref name="color1" />과 <paramref name="color2" />가 같지 않으면 true이고, 서로 같으면 false입니다.</returns>
      <param name="color1">비교할 첫 번째 <see cref="T:Windows.UI.Color" /> 구조체입니다.</param>
      <param name="color2">비교할 두 번째 <see cref="T:Windows.UI.Color" /> 구조체입니다.</param>
    </member>
    <member name="P:Windows.UI.Color.R">
      <summary>색의 sRGB 빨강 채널 값을 가져오거나 설정합니다. </summary>
      <returns>0에서 255 사이의 값으로 표시되는 sRGB 빨강 채널 값입니다.</returns>
    </member>
    <member name="M:Windows.UI.Color.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>이 멤버에 대한 설명은 <see cref="M:System.IFormattable.ToString(System.String,System.IFormatProvider)" />을 참조하십시오.</summary>
      <returns>지정된 형식의 현재 인스턴스 값이 포함된 문자열입니다.</returns>
      <param name="format">사용할 형식을 지정하는 문자열입니다. 또는 IFormattable 구현 형식에 대해 정의된 기본 형식을 사용하기 위한 null입니다. </param>
      <param name="provider">값의 형식을 지정하는 데 사용할 IFormatProvider입니다. 또는 운영 체제의 현재 로캘 설정에서 숫자 형식 정보를 가져오기 위한 null입니다. </param>
    </member>
    <member name="M:Windows.UI.Color.ToString">
      <summary>16진수 표기법의 ARGB 채널을 사용하여 색의 문자열 표현을 만듭니다. </summary>
      <returns>색의 문자열 표현입니다.</returns>
    </member>
    <member name="M:Windows.UI.Color.ToString(System.IFormatProvider)">
      <summary>ARGB 채널과 지정한 형식 공급자를 사용하여 색의 문자열 표현을 만듭니다. </summary>
      <returns>색의 문자열 표현입니다.</returns>
      <param name="provider">문화권별 형식 지정 정보입니다.</param>
    </member>
  </members>
</doc>