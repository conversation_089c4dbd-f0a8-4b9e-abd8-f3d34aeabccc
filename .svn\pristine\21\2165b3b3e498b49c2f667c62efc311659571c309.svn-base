using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolText : ToolObject
    {
        public delegate void ValueChangeHandler(object sender, EventArgs e);

        private CommandAdd _command;

        private DrawText _drawText;

        public event ValueChangeHandler ValueChanged;

        public override void MouseDoubleClick(DrawArea drawArea, MouseEventArgs e)
        {
            var o = GetSelected(drawArea);
            if (o != null && o.Rectangle.Contains(e.Location) && o.Rectangle.IsLimt())
            {
                var bounds = new Rectangle(o.Rectangle.Left - 1, o.Rectangle.Top - 1, o.Rectangle.Width - 2,
                    o.Rectangle.Height - 2);
                var autoSizeTextBox = new AutoSizeTextBox();
                drawArea.tempTextBox = autoSizeTextBox;
                autoSizeTextBox.BorderStyle = BorderStyle.None;
                autoSizeTextBox.ImeMode = ImeMode.Hangul;
                autoSizeTextBox.Multiline = true;
                autoSizeTextBox.MinimumSize = new Size(bounds.Width, bounds.Height);
                autoSizeTextBox.MaximumSize = new Size(bounds.Width, 0);
                autoSizeTextBox.Bounds = bounds;
                autoSizeTextBox.Text = o.Text;
                autoSizeTextBox.Font = o.Font;
                autoSizeTextBox.Visible = true;
                autoSizeTextBox.LostFocus += delegate
                {
                    var selected = GetSelected(drawArea);
                    if (selected != null && StaticValue.IsShowText)
                    {
                        StaticValue.IsShowText = false;
                        selected.Text = autoSizeTextBox.Text;
                        selected.Font = autoSizeTextBox.Font;
                        drawArea.Controls.Remove(autoSizeTextBox);
                        drawArea.GraphicsList.UnselectAll();
                        drawArea.Refresh();
                    }
                };
                drawArea.Controls.Add(autoSizeTextBox);
                autoSizeTextBox.Focus();
                autoSizeTextBox.SelectAll();
                var rectangle = new Rectangle(autoSizeTextBox.Bounds.Left - 1, autoSizeTextBox.Bounds.Top - 1,
                    autoSizeTextBox.Bounds.Width + 2, autoSizeTextBox.Bounds.Height + 2);
                o.Rectangle = rectangle;
                StaticValue.IsShowText = true;
                drawArea.Refresh();
                autoSizeTextBox.Disposed += delegate
                {
                    var commandChangeState = new CommandChangeState(drawArea.GraphicsList);
                    commandChangeState.NewState(drawArea.GraphicsList);
                    drawArea.AddCommandToHistory(commandChangeState);
                };
                autoSizeTextBox.Resize += delegate
                {
                    var rectangle2 = new Rectangle(autoSizeTextBox.Bounds.Left - 1, autoSizeTextBox.Bounds.Top - 1,
                        autoSizeTextBox.Bounds.Width + 2, autoSizeTextBox.Bounds.Height + 2);
                    o.Rectangle = rectangle2;
                    drawArea.Refresh();
                };
            }
        }

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.CurrentToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                _drawText = new DrawText(e.X, e.Y, 1, 1);
                AddNewObject(drawArea, _drawText);
            }
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left && _drawText != null)
            {
                _drawText.IsSelected = true;
                var obj = _drawText;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    _drawText.MoveHandleTo(e.Location, 5, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public void Change()
        {
            if (ValueChanged != null) ValueChanged(null, EventArgs.Empty);
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (_drawText != null)
            {
                _drawText.IsCache = true;
                StaticValue.CurrentRectangle = _drawText.Rectangle;
                if (!_drawText.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                }
                else
                {
                    var obj = _drawText;
                    using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                    {
                        _drawText.Normalize();
                        _command = new CommandAdd(_drawText);
                        drawArea.AddCommandToHistory(_command);
                    }
                }

                Change();
            }
        }
    }
}