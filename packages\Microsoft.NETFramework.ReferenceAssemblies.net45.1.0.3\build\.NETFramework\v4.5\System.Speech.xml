﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Speech</name>
  </assembly>
  <members>
    <member name="T:System.Speech.AudioFormat.AudioBitsPerSample">
      <summary>Enumerates values of the AudioBitsPerSample property of the SpeechAudioFormatInfo object.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.AudioFormat.AudioBitsPerSample.Eight">
      <summary>Value that sets the bits per sample to 8.</summary>
    </member>
    <member name="F:System.Speech.AudioFormat.AudioBitsPerSample.Sixteen">
      <summary>Value that sets the bits per second to 16.</summary>
    </member>
    <member name="T:System.Speech.AudioFormat.AudioChannel">
      <summary>Enumerates values of the audio channel used by the SpeechAudioFormatInfo object.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.AudioFormat.AudioChannel.Mono">
      <summary>Value that sets the channel to Mono. </summary>
    </member>
    <member name="F:System.Speech.AudioFormat.AudioChannel.Stereo">
      <summary>Value that sets the channel to Stereo.</summary>
    </member>
    <member name="T:System.Speech.AudioFormat.EncodingFormat">
      <summary>Enumerates values of the EncodingFormat property of the SpeechAudioFormatInfo object.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.AudioFormat.EncodingFormat.Pcm">
      <summary>Value that sets the format to Pcm.</summary>
    </member>
    <member name="F:System.Speech.AudioFormat.EncodingFormat.ALaw">
      <summary>Value that sets the format to ALaw.</summary>
    </member>
    <member name="F:System.Speech.AudioFormat.EncodingFormat.ULaw">
      <summary>Value that sets the format to ULaw.</summary>
    </member>
    <member name="T:System.Speech.AudioFormat.SpeechAudioFormatInfo">
      <summary>Represents an audio format for speech recognition.</summary>
    </member>
    <member name="M:System.Speech.AudioFormat.SpeechAudioFormatInfo.#ctor(System.Int32,System.Speech.AudioFormat.AudioBitsPerSample,System.Speech.AudioFormat.AudioChannel)">
      <summary>Creates a new instance of SpeechAudioFormatInfo.</summary>
      <param name="samplesPerSecond">An int containing the samples per second value.</param>
      <param name="bitsPerSample">An int containing the bits per sample value.</param>
      <param name="channel">A member of the AudioChannel enumeration (ie., <paramref name="Mono" /> or <paramref name="Stereo" />).</param>
    </member>
    <member name="M:System.Speech.AudioFormat.SpeechAudioFormatInfo.#ctor(System.Speech.AudioFormat.EncodingFormat,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Byte[])">
      <summary>Creates a new instance of SpeechAudioFormatInfo.</summary>
      <param name="encodingFormat">An EncodingFormat object.</param>
      <param name="samplesPerSecond">An int containing the samples per second value.</param>
      <param name="bitsPerSample">An int containing the bits per sample value.</param>
      <param name="channelCount">An int containing the channel count value.</param>
      <param name="averageBytesPerSecond">An int containing the average bytes per second value.</param>
      <param name="blockAlign">An int containing the BlockAlign value.</param>
      <param name="formatSpecificData">A byte array containing the format-specific data.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Speech.AudioFormat.SpeechAudioFormatInfo.AverageBytesPerSecond">
      <summary>Gets the average bytes per second of the audio format.</summary>
      <returns>An int containing the average bytes per second value.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Speech.AudioFormat.SpeechAudioFormatInfo.BitsPerSample">
      <summary>Gets the bits per sample of the audio format.</summary>
      <returns>An int containing the bits per sample value.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Speech.AudioFormat.SpeechAudioFormatInfo.BlockAlign">
      <summary>Gets and sets the block alignment in bytes.</summary>
      <returns>An int containing the block alignment value.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Speech.AudioFormat.SpeechAudioFormatInfo.ChannelCount">
      <summary>Gets the channel count of the audio format.</summary>
      <returns>An int containing the channel count value.</returns>
    </member>
    <member name="P:System.Speech.AudioFormat.SpeechAudioFormatInfo.EncodingFormat">
      <summary>Gets the encoding format of the audio format.</summary>
      <returns>An EncodingFormat object representing the encoding format value.</returns>
    </member>
    <member name="M:System.Speech.AudioFormat.SpeechAudioFormatInfo.Equals(System.Object)">
      <summary>Returns whether a specified object is equal to the SpeechAudioFormatInfo object.</summary>
      <param name="obj">The object to be compared.</param>
    </member>
    <member name="M:System.Speech.AudioFormat.SpeechAudioFormatInfo.FormatSpecificData">
      <summary>Returns the format-specific data of the audio format.</summary>
      <returns>A byte array containing the format-specific data.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.AudioFormat.SpeechAudioFormatInfo.GetHashCode">
      <summary>Returns the hash code of the audio format.</summary>
      <returns>An int containing the hash code.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.AudioFormat.SpeechAudioFormatInfo.SamplesPerSecond">
      <summary>Gets the samples per second of the audio format.</summary>
      <returns>An int containing the samples per second value.</returns>
    </member>
    <member name="T:System.Speech.Recognition.AudioLevelUpdatedEventArgs">
      <summary>Returns data from the AudioLevelUpdated event.</summary>
    </member>
    <member name="P:System.Speech.Recognition.AudioLevelUpdatedEventArgs.AudioLevel">
      <summary>Gets the new audio level.</summary>
      <returns>An int indicating the new audio level value.</returns>
    </member>
    <member name="T:System.Speech.Recognition.AudioSignalProblem">
      <summary>Enumerates the types of audio signal problems.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Recognition.AudioSignalProblem.None">
      <summary>Audio input can be processed by the recognition engine.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioSignalProblem.TooNoisy">
      <summary>Audio input has too much background noise for the recognition engine to process.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioSignalProblem.NoSignal">
      <summary>No audio input is detected.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioSignalProblem.TooLoud">
      <summary>Audio input is too loud for the recognition engine to process.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioSignalProblem.TooSoft">
      <summary>Audio input is not loud enough for the recognition engine to process.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioSignalProblem.TooFast">
      <summary>Audio input is too fast for the recognition engine to process.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioSignalProblem.TooSlow">
      <summary>Audio input is too slow for the recognition engine to process.</summary>
    </member>
    <member name="T:System.Speech.Recognition.AudioSignalProblemOccurredEventArgs">
      <summary>Returns data from the AudioSignalProblemOccurred event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.AudioSignalProblemOccurredEventArgs.AudioLevel">
      <summary>Gets the audio level associated with the event. </summary>
      <returns>An int value indicating the audio level of a recognition engine at the end of the speech just processed.</returns>
    </member>
    <member name="P:System.Speech.Recognition.AudioSignalProblemOccurredEventArgs.AudioPosition">
      <summary>Gets the audio position associated with the event.  </summary>
      <returns>Returns an instance of System.TimeSpan indicating the location in the audio input stream of a recognition engine at the starting point in which the <see cref="T:System.Speech.Recognition.AudioSignalProblemOccurredEventArgs" /> occurred.</returns>
    </member>
    <member name="P:System.Speech.Recognition.AudioSignalProblemOccurredEventArgs.AudioSignalProblem">
      <summary>Gets the audio signal problem.</summary>
      <returns>An instance of the AudioSignalProblem enumeration containing one of several possible reasons for the problem in the audio signal.</returns>
    </member>
    <member name="P:System.Speech.Recognition.AudioSignalProblemOccurredEventArgs.RecognizerAudioPosition">
      <summary>Gets the recognizer audio position associated with the event. </summary>
      <returns>Returns an instance of System.TimeSpan indicating the location in the audio input stream of a recognition engine in which the <see cref="P:System.Speech.Recognition.AudioSignalProblemOccurredEventArgs.RecognizerAudioPosition" /> occurred.</returns>
    </member>
    <member name="T:System.Speech.Recognition.AudioState">
      <summary>Enumerates values of the recognizer's audio state.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioState.Stopped">
      <summary>Indicates the recognition engine is not currently processing audio input.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioState.Silence">
      <summary>Indicates audio input that is either silence, or non-speech background noise.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioState.Speech">
      <summary>Indicates speech in the audio input.</summary>
    </member>
    <member name="T:System.Speech.Recognition.AudioStateChangedEventArgs">
      <summary>Returns data from the AudioStateChanged event. </summary>
    </member>
    <member name="P:System.Speech.Recognition.AudioStateChangedEventArgs.AudioState">
      <summary>Gets the audio state associated with the event.</summary>
      <returns>Returns the <see cref="P:System.Speech.Recognition.AudioStateChangedEventArgs.AudioState" /> for the current recognizer containing information about the status of the audio signal.</returns>
    </member>
    <member name="T:System.Speech.Recognition.Choices">
      <summary>Represents a list of alternative items to make up an element in a grammar.</summary>
    </member>
    <member name="M:System.Speech.Recognition.Choices.#ctor">
      <summary>Default constructor for <see cref="T:System.Speech.Recognition.Choices" />.</summary>
    </member>
    <member name="M:System.Speech.Recognition.Choices.#ctor(System.Speech.Recognition.GrammarBuilder[])">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.Choices" /> from one or more <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instances.</summary>
      <param name="alternateChoices">One or more valid instances of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> used to construct a new <see cref="T:System.Speech.Recognition.Choices" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.Choices.#ctor(System.String[])">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.Choices" /> from one or more <see cref="System.String" /> instances.</summary>
      <param name="phrases">One or more phrases containing in valid instances of <see cref="System.String" /> used to construct a new <see cref="T:System.Speech.Recognition.Choices" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.Choices.Add(System.Speech.Recognition.GrammarBuilder[])">
      <summary>Adds one of more <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object to the list of options already encapsulated in the current instance of <see cref="T:System.Speech.Recognition.Choices" />.</summary>
      <param name="alternateChoices">One or more <see cref="T:System.Speech.Recognition.GrammarBuilder" /> objects, each of which represents a possible option for recognition.</param>
    </member>
    <member name="M:System.Speech.Recognition.Choices.Add(System.String[])">
      <summary>Adds a <see cref="System.String" /> to the list of options already encapsulated in the current instance of <see cref="T:System.Speech.Recognition.Choices" />.</summary>
      <param name="phrases">One or more <see cref="System.String" /> instances, each of which represents a possible option for recognition.</param>
    </member>
    <member name="M:System.Speech.Recognition.Choices.ToGrammarBuilder">
      <summary>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from the current <see cref="T:System.Speech.Recognition.Choices" /> instance.</summary>
      <returns>An instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> created from the current instance of <see cref="T:System.Speech.Recognition.Choices" />.</returns>
    </member>
    <member name="T:System.Speech.Recognition.DictationGrammar">
      <summary>Represents a grammar used for free text dictation.</summary>
    </member>
    <member name="M:System.Speech.Recognition.DictationGrammar.#ctor">
      <summary>Default constructor for <see cref="T:System.Speech.Recognition.DictationGrammar" /> returning an instance of the standard dictation grammar provided by Windows Desktop Speech technology.</summary>
    </member>
    <member name="M:System.Speech.Recognition.DictationGrammar.#ctor(System.String)">
      <summary>Constructs an instance of <see cref="T:System.Speech.Recognition.DictationGrammar" /> with explicitly specified features.</summary>
      <param name="topic">String containing an XML compliant URI (Universal Resource Identifier) specifying the feature set support by the <see cref="T:System.Speech.Recognition.DictationGrammar" /> to be constructed.</param>
    </member>
    <member name="M:System.Speech.Recognition.DictationGrammar.SetDictationContext(System.String,System.String)">
      <summary>Allows an application define a context to be used with a dictation model.</summary>
      <param name="precedingText">A string containing a text used to mark the beginning of a context used in speech recognition.</param>
      <param name="subsequentText">A string containing a text used to mark the end of a context used in speech recognition.</param>
    </member>
    <member name="T:System.Speech.Recognition.DisplayAttributes">
      <summary>Enumerates formats for the display of words in a recognized phrase.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Recognition.DisplayAttributes.None">
      <summary>Indicates no display attributes.</summary>
    </member>
    <member name="F:System.Speech.Recognition.DisplayAttributes.ZeroTrailingSpaces">
      <summary>Indicates no trailing space following the given word.</summary>
    </member>
    <member name="F:System.Speech.Recognition.DisplayAttributes.OneTrailingSpace">
      <summary>Indicates one trailing space following the given word. </summary>
    </member>
    <member name="F:System.Speech.Recognition.DisplayAttributes.TwoTrailingSpaces">
      <summary>Indicates two trailing space following the given word.</summary>
    </member>
    <member name="F:System.Speech.Recognition.DisplayAttributes.ConsumeLeadingSpaces">
      <summary>Indicates that spaces preceding the given word be removed.</summary>
    </member>
    <member name="T:System.Speech.Recognition.EmulateRecognizeCompletedEventArgs">
      <summary>Returns data from the EmulateRecognizeCompleted event.</summary>
    </member>
    <member name="P:System.Speech.Recognition.EmulateRecognizeCompletedEventArgs.Result">
      <summary>Gets the emulated recognition results.</summary>
      <returns>Returns the <see cref="T:System.Speech.Recognition.RecognitionResult" />, which is derived from <see cref="T:System.Speech.Recognition.RecognizedPhrase" /> and contains full information about the a phrase returned by an Windows Desktop Speech Recognition Technology recognition operation.</returns>
    </member>
    <member name="T:System.Speech.Recognition.Grammar">
      <summary>Provides run time support for obtaining and managing Speech grammar information.</summary>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor">
      <summary>Default constructor for <see cref="T:System.Speech.Recognition.Grammar" /> objects.</summary>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.IO.Stream)">
      <param name="stream">A <see cref="System.IO.Stream" /> connecting to an I/O object -- including files, Visual Studio Resources, and DLLs -- containingagrammar specification.</param>
      <exception cref="T:System.ArgumentException">Generated for invalid <paramref name="stream" /> values. Generated if the stream is connected to a grammar which has no a root rule defined, requires initialization parameters, or has a relative rule reference not resolvable by the default base System.Uri rule for grammars.</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.IO.Stream,System.String)">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.Grammar" /> from an instance <see cref="System.IO.Stream" /> connecting to an object containing a grammar specification; the name of a particular rule to be the grammar’s root rule may also be specified.</summary>
      <param name="stream">A System.IO.Stream connecting to an I/O object -- including files, Visual Studio Resources, and DLLs -- containingagrammar specification.</param>
      <param name="ruleName">Name of a grammar rule used as the entry point or root of the <see cref="T:System.Speech.Recognition.Grammar" /> objects to be created. This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">Generated for invalid <paramref name="stream" /> or <paramref name="ruleName" /> values. Generated if the stream is connected to a grammar that does not contain the rule <paramref name="ruleName" />, requires initialization parameters, or has a relative rule reference not resolvable by the default base System.Uri rule for grammars.</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.IO.Stream,System.String,System.Object[])">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.Grammar" /> from an instance <see cref="System.IO.Stream" /> connecting to an object containing a grammar specification; the name of a particular rule to be the grammar’s root rule, and parameters for the appropriate initialization handler may also be specified.</summary>
      <param name="stream">A <see cref="System.IO.Stream" /> connecting to an I/O object -- including files, Visual Studio Resources, and DLLs -- containingagrammar specification.</param>
      <param name="ruleName">Name of a grammar rule used as the entry point or root of the <see cref="T:System.Speech.Recognition.Grammar" /> object to be created. This parameter may be null.</param>
      <param name="parameters">Parameters to be passed to the appropriate initialization handler specified by the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnInit" /> property on the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> for the entry point or root rule of the <see cref="T:System.Speech.Recognition.Grammar" /> to be created.This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">Generated for invalid <paramref name="stream" />, <paramref name="ruleName," /> or <paramref name="parameters" /> values. Generated if the stream is connected to a grammar which does contain the rule <paramref name="ruleName" />, requires initialization parameters different from those specified, or has a relative rule reference not resolvable by the default base <see cref="System.Uri" /> rule for grammars.</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.IO.Stream,System.String,System.Uri)">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.Grammar" /> from an instance <see cref="System.IO.Stream" /> connecting to an object containing a grammar specification; a base URI for relative references, may also be specified.</summary>
      <param name="stream">A System.IO.Stream connecting to an I/O object -- including files, Visual Studio Resources, and DLLs -- containingagrammar specification.</param>
      <param name="ruleName">Name of a grammar rule used as the entry point or root of the <see cref="T:System.Speech.Recognition.Grammar" /> object to be created. This parameter may be null.</param>
      <param name="baseUri">
        <see cref="System.Uri" /> object defining base path that any relative rule references, for example to other grammars, within the grammar are resolved against.This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">Generated for invalid <paramref name="stream," /><paramref name="ruleName" />, or <paramref name="baseUri" /> values. Generated if the stream is connected to a grammar that does not contain the rule <paramref name="ruleName" />, or has a relative rule reference not resolvable even using the <paramref name="baseUri" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.IO.Stream,System.String,System.Uri,System.Object[])">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.Grammar" /> from an instance <see cref="System.IO.Stream" /> connecting to an object containing a grammar specification; the name of a particular rule to be the grammar’s root rule, a base URI for relative references, and parameters for the appropriate initialization handler may also be specified.</summary>
      <param name="stream">A <see cref="System.IO.Stream" /> connecting to an I/O object -- including files, Visual Studio Resources, and DLLs -- containingagrammar specification.</param>
      <param name="ruleName">Name of a grammar rule used as the entry point or root of the <see cref="T:System.Speech.Recognition.Grammar" /> object to be created. This parameter may be null.</param>
      <param name="baseUri">
        <see cref="System.Uri" /> object defining base path that any relative rule references, for example to other grammars, within the grammar are resolved against.This parameter may be null.</param>
      <param name="parameters">Parameters to be passed to the appropriate initialization handler specified by the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnInit" /> property on the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> for the entry point or root rule of the <see cref="T:System.Speech.Recognition.Grammar" /> to be created.This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">Generated for invalid <paramref name="stream," /><paramref name="ruleName" />, <paramref name="baseUri" />, or <paramref name="parameters" /> values. Generated if the stream is connected to a grammar that does not contain the rule <paramref name="ruleName" />, if the contents of the array <paramref name="parameters" /> do not match the arguments of any of the rule's initialization handlers, or has a relative rule reference not resolvable by the default base System.Uri rule for grammars or the Uri supplied by <paramref name="baseUri" /> .</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.Speech.Recognition.GrammarBuilder)">
      <summary>Constructs a <see cref="T:System.Speech.Recognition.Grammar" /> object from a valid <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instance</summary>
      <param name="builder">A valid <see cref="T:System.Speech.Recognition.GrammarBuilder" /> defining a grammar to be used by a recognition engine.</param>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsDocument)">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.Grammar" /> from an instance of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" />.</summary>
      <param name="srgsDocument">An instance of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> to a file containing a rules and a grammar to be used to construct the <see cref="T:System.Speech.Recognition.Grammar" /> object.</param>
      <exception cref="T:System.ArgumentException">Generated for invalid values. Generated if the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> specified by <paramref name="srgsDocument" /> does not contain a root rule <paramref name="ruleName" />.</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsDocument,System.String)">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.Grammar" /> from an instance of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> the name of a particular rule to be the grammar’s root rule may also be specified.</summary>
      <param name="srgsDocument">An instance of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> to a file containing a rules and a grammar to be used to construct the <see cref="T:System.Speech.Recognition.Grammar" /> object.</param>
      <param name="ruleName">Name of a grammar rule used as the entry point or root of the <see cref="T:System.Speech.Recognition.Grammar" /> object to be created. This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">Generated for invalid <paramref name="srgsDocuments" />, and <paramref name="ruleName" />. Generated if the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> specified by <paramref name="srgsDocument" /> does not contain the rule <paramref name="ruleName" />, or if <paramref name="ruleName" /> is null and <paramref name="srgsDocument" /> does not have a root rule..</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsDocument,System.String,System.Object[])">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.Grammar" /> from an instance of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> the name of a particular rule to be the grammar’s root rule, and parameters for the appropriate initialization handler may also be specified.</summary>
      <param name="srgsDocument">An instance of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> to a file containing a rules and a grammar to be used to construct the <see cref="T:System.Speech.Recognition.Grammar" /> object.</param>
      <param name="ruleName">Name of a grammar rule used as the entry point or root of the <see cref="T:System.Speech.Recognition.Grammar" /> object to be created. This parameter may be null.</param>
      <param name="parameters">Parameters to be passed to the appropriate initialization handler specified by the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnInit" /> property on the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> for the entry point or root rule of the <see cref="T:System.Speech.Recognition.Grammar" /> to be created.This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">Generated for invalid <paramref name="srgsDocuments" />, <paramref name="ruleName" />, or <paramref name="parameters" /> values. Generated if the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> specified by <paramref name="srgsDocument" /> does not contain the rule <paramref name="ruleName" />, if the contents of the array <paramref name="parameters" /> do not match the arguments of any of the rule's initialization handlers.</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsDocument,System.String,System.Uri)">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.Grammar" /> from an instance of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> the name of a particular rule to be the grammar’s root rule, and a base URI for relative references.</summary>
      <param name="srgsDocument">An instance of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> to a file containing a rules and a grammar to be used to construct the <see cref="T:System.Speech.Recognition.Grammar" /> object.</param>
      <param name="ruleName">Name of a grammar rule used as the entry point or root of the <see cref="T:System.Speech.Recognition.Grammar" /> object to be created. This parameter may be null.</param>
      <param name="baseUri">
        <see cref="System.Uri" /> object defining base path that any relative rule references, for example to other grammars, within the grammar are resolved against.This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">Generated for invalid <paramref name="srgsDocuments," /><paramref name="ruleName" />, or <paramref name="baseUri" /> values. Generated if the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> specified by <paramref name="srgsDocument" /> does not contain the rule <paramref name="ruleName" />, or has a relative rule reference not resolvable by the default base System.Uri rule for grammars or the Uri supplied by <paramref name="baseUri" /> .</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsDocument,System.String,System.Uri,System.Object[])">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.Grammar" /> from an instance of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> the name of a particular rule to be the grammar’s root rule, a base URI for relative references, and parameters for the appropriate initialization handler may also be specified.</summary>
      <param name="srgsDocument">An instance of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> to a file containing a rules and a grammar to be used to construct the <see cref="T:System.Speech.Recognition.Grammar" /> object.</param>
      <param name="ruleName">Name of a grammar rule used as the entry point or root of the <see cref="T:System.Speech.Recognition.Grammar" /> object to be created. This parameter may be null.</param>
      <param name="baseUri">
        <see cref="System.Uri" /> object defining base path that any relative rule references, for example to other grammars, within the grammar are resolved against.This parameter may be null.</param>
      <param name="parameters">Parameters to be passed to the appropriate initialization handler specified by the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnInit" /> property on the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> for the entry point or root rule of the <see cref="T:System.Speech.Recognition.Grammar" /> to be created.This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">Generated for invalid <paramref name="srgsDocuments" />, <paramref name="ruleName" />, <paramref name="baseUri" />, or <paramref name="parameters" /> values. Generated if the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> specified by <paramref name="srgsDocument" /> does not contain the rule <paramref name="ruleName" />, if the contents of the array <paramref name="parameters" /> do not match the arguments of any of the rule's initialization handlers, or has a relative rule reference not resolvable by the default base System.Uri rule for grammars or the Uri supplied by <paramref name="baseUri" /> .</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.String)">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.Grammar" /> from file containing a grammar specification.</summary>
      <param name="path">Path to a file, including DLLs, containing a grammar specification.</param>
      <exception cref="T:System.ArgumentException">Generated for invalid <paramref name="path" /> values. Generated if file does not contain a valid grammar, if the grammar is valid but does not specify a root rule, if the root rule initialization handler requires arguments, or if it has a relative rule reference not resolvable by the default base System.Uri rule for grammars.</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.String,System.String)">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.Grammar" /> from file containing a grammar specification; the name of a particular rule to be the grammar’s root rule may also be specified.</summary>
      <param name="path">Path to a file, including DLLs, containing a grammar specification.</param>
      <param name="ruleName">Name of a grammar rule used as the entry point or root of the <see cref="T:System.Speech.Recognition.Grammar" /> object to be created. This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">Generated for invalid <paramref name="path" /><paramref name="or" /><paramref name="ruleName" />. Generated if the file specified by <paramref name="path" /> does not contain a valid grammar or the rule <paramref name="ruleName" />, if the root rule initialization handler requires arguments, or if it has a relative rule reference not resolvable by the default base System.Uri rule for grammars.</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.String,System.String,System.Object[])">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.Grammar" /> from file containing a grammar specification; the name of a particular rule to be the grammar’s root rule and parameters for the appropriate initialization handler may also be specified.</summary>
      <param name="path">Path to a file, including DLLs, containing a grammar specification.</param>
      <param name="ruleName">Name of a grammar rule used as the entry point or root of the <see cref="T:System.Speech.Recognition.Grammar" /> object to be created. This parameter may be null.</param>
      <param name="parameters">Parameters to be passed to the appropriate initialization handler specified by the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnInit" /> property on the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> for the entry point or root rule of the <see cref="T:System.Speech.Recognition.Grammar" /> to be created.This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">Generated for invalid <paramref name="path," /><paramref name="ruleName" />, or <paramref name="parameters" /> values. Generated if the file specified by <paramref name="path" /> does not contain a valid grammar or the rule <paramref name="ruleName" />, if the contents of the array <paramref name="parameters" /> do not match the arguments of any of the rule's initialization handlers, or has a relative rule reference not resolvable by the default base System.Uri rule for grammars.</exception>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.Enabled">
      <summary>Gets or sets a value controlling whether a <see cref="T:System.Speech.Recognition.Grammar" /> can be used by a recognition engine.</summary>
      <returns>Property is a bool value which is set to true if an instance of <see cref="T:System.Speech.Recognition.Grammar" /> is enabled and ready to process input, and false it the instance is not available for input processing.</returns>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.IsStg">
      <summary>Read-only property indicating if grammar is strongly typed.</summary>
      <returns>A bool which is true if the grammar is strongly typed, and false if it is not.</returns>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.Loaded">
      <summary>Ready only attribute, indicates if a <see cref="T:System.Speech.Recognition.Grammar" /> has been loaded into a recognition engine.</summary>
      <returns>Returns a bool value, true if the instance of <see cref="T:System.Speech.Recognition.Grammar" /> has been loaded by a recognition engine (<see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />), false if it has not.</returns>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.LoadLocalizedGrammarFromType(System.Type,System.Object[])">
      <summary>Returns a localized instance of a <see cref="T:System.Speech.Recognition.Grammar" /> derived object from a Common Language Runtime <see cref="System.Type" />.</summary>
      <returns>Returns a valid <see cref="T:System.Speech.Recognition.Grammar" /> based object as a <see cref="T:System.Speech.Recognition.Grammar" /> instance, or null if there has been an error.</returns>
      <param name="type">
        <see cref="System." />
        <see cref="Type" /> of an <see cref="T:System.Speech.Recognition.Grammar" /> based object in an assembly.</param>
      <param name="onInitParameters">Parameters to be passed to the appropriate initialization method of the localized <see cref="T:System.Speech.Recognition.Grammar" /><see cref="based" /> object. This parameter may be null.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.Name">
      <summary>Gets or sets the user-friendly name of an instance of <see cref="T:System.Speech.Recognition.Grammar" />.</summary>
      <returns>Returns user friendly-name of an instance of <see cref="T:System.Speech.Recognition.Grammar" />. If it is not set, returns null.</returns>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.Priority">
      <summary>Set or gets a value used as a tie breaker when one or more grammars have returned a recognized phrase.</summary>
      <returns>Returns an integer value representing the relative priority of a specific <see cref="T:System.Speech.Recognition.Grammar" />.</returns>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.ResourceName">
      <summary>Set or gets a value with the a name of a binary resource from which was used to load the current <see cref="T:System.Speech.Recognition.Grammar" />. </summary>
      <returns>Returns a string containing the name of the binary resource from which the strongly typed grammar used by the <see cref="T:System.Speech.Recognition.Grammar" /> was loaded.</returns>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.RuleName">
      <summary>Read-only value containing the name of root or entry point rule of a valid <see cref="T:System.Speech.Recognition.Grammar" /> instance.</summary>
      <returns>Returns a string with the name of root or entry point rule of a valid <see cref="T:System.Speech.Recognition.Grammar" /> instance.</returns>
    </member>
    <member name="E:System.Speech.Recognition.Grammar.SpeechRecognized">
      <summary>The event raised when the current grammar has been used by the recognition engine to detect speech and find one or more phrases with sufficient confidence levels.</summary>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.StgInit(System.Object[])">
      <summary>Initializes a strongly typed grammar.</summary>
      <param name="parameters">Parameters to be passed to initialize strongly typed grammar.This parameter may be null.</param>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.Weight">
      <summary>Property used to control the influence of a grammar on a recognition engine's algorithm.</summary>
      <returns>Returns a floating point value indicating the relative weight a recognition engine (<see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />) instance should assign to this grammar when processing audio input.</returns>
    </member>
    <member name="T:System.Speech.Recognition.GrammarBuilder">
      <summary>Provides an easy-to-use mechanism for constructing complicated <see cref="T:System.Speech.Recognition.Grammar" /> objects from simple inputs.</summary>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor">
      <summary>Default constructor for the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</summary>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor(System.Speech.Recognition.Choices)">
      <summary>Constructs a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object from a phrase expressed as a <see cref="T:System.Speech.Recognition.Choices" /> instance.</summary>
      <param name="alternateChoices">Valid instance of <see cref="T:System.Speech.Recognition.Choices" /> used to construct a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor(System.Speech.Recognition.GrammarBuilder,System.Int32,System.Int32)">
      <summary>Constructs a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> that can generate a <see cref="T:System.Speech.Recognition.Grammar" /> requiring that audio input is to be checked for matching against repeated occurrences of a pattern defined by a <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</summary>
      <param name="builder">A <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instance containing the grammar logic to match against audio input.</param>
      <param name="minRepeat">Specifies the minimum number of times the audio input matching the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> specified by the <paramref name="builder" /> paramenter can be repeated and still be successfully recognized.</param>
      <param name="maxRepeat">Specifies the maximum number of times the audio input matching the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> specified by the <paramref name="builder" /> paramenter can be repeated and still be successfully recognized.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor(System.Speech.Recognition.SemanticResultKey)">
      <summary>Constructs a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object from a phrase expressed as a <see cref="T:System.Speech.Recognition.SemanticResultKey" /> instance.</summary>
      <param name="key">Valid instance of <see cref="T:System.Speech.Recognition.SemanticResultKey" /> used to construct a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor(System.Speech.Recognition.SemanticResultValue)">
      <summary>Constructs a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object from a phrase expressed as a <see cref="T:System.Speech.Recognition.SemanticResultValue" /> instance.</summary>
      <param name="value">Valid instance of <see cref="T:System.Speech.Recognition.SemanticResultValue" /> used to construct a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor(System.String)">
      <summary>Constructs a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object from a phrase expressed as a <see cref="System.String" /> instance.</summary>
      <param name="phrase">A valid <see cref="System.String" /> instance containing a phrase to be used to construct a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor(System.String,System.Int32,System.Int32)">
      <summary>Constructs a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> that can generate a <see cref="T:System.Speech.Recognition.Grammar" /> requiring that audio input is to be checked for matching against repeated occurrences of a given phrase.</summary>
      <param name="phrase">A <see cref="System.String" /> instance containing a phrase to match against audio input.</param>
      <param name="minRepeat">Specifies the minimum number of times the audio input matching the <see cref="System.String" /> specified by the <paramref name="phrase" /> paramenter can be repeated and still be successfully recognized.</param>
      <param name="maxRepeat">Specifies the maximum number of times the audio input matching the <see cref="System.String" /> specified by the <paramref name="phrase" /> paramenter can be repeated and still be successfully recognized.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor(System.String,System.Speech.Recognition.SubsetMatchingMode)">
      <summary>Constructs a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object which can recognize audio input that is a subset of a given match phrase.</summary>
      <param name="phrase">
        <see cref="System.String" /> containing a phrase to be used recognized.</param>
      <param name="subsetMatchingCriteria">A member of  <see cref="T:System.Speech.Recognition.SubsetMatchingMode" /> defining the method used to match audio input which may be a subset of the match string</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Add(System.Speech.Recognition.Choices,System.Speech.Recognition.GrammarBuilder)">
      <summary>Returns a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from by appending the grammar logic of a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instance to the grammar logic of a <see cref="T:System.Speech.Recognition.Choices" /> object.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> if successful, null is returned or an exception generated if the operation fails.</returns>
      <param name="choices">A valid instance of <see cref="T:System.Speech.Recognition.Choices" /> define a list of acceptable audio inputs provide the initial grammar logic of the new <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</param>
      <param name="builder">A valid instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> containing grammar logic to be appended to the grammar logic of <paramref name="choices" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Add(System.Speech.Recognition.GrammarBuilder,System.Speech.Recognition.Choices)">
      <summary>Returns a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from by appending the grammar logic of a <see cref="T:System.Speech.Recognition.Choices" /> instance to the grammar logic of a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> if successful, null is returned or an exception generated if the operation fails.</returns>
      <param name="builder">A valid instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> to contain the initial grammar logic of the new <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</param>
      <param name="choices">A valid instance of <see cref="T:System.Speech.Recognition.Choices" /> define a list of acceptable audio inputs to be appended to the grammar logic defined by <paramref name="builder." /></param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Add(System.Speech.Recognition.GrammarBuilder,System.Speech.Recognition.GrammarBuilder)">
      <summary>Returns a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from by appending the grammar logic of two <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instances.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> if successful, null is returned or an exception generated if the operation fails.</returns>
      <param name="builder1">A valid instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> to contain the initial grammar logic of the new <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</param>
      <param name="builder2">A valid instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> containing grammar logic to be appended to the grammar logic of <paramref name="builder1" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Add(System.Speech.Recognition.GrammarBuilder,System.String)">
      <summary>Returns a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from by appending a phrase to the grammar logic of a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instance</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> if successful, null is returned or an exception generated if the operation fails.Because <see cref="T:System.Speech.Recognition.GrammarBuilder" /> support implicit conversions from <see cref="T:System.Speech.Recognition.SemanticResultValue" /> and <see cref="T:System.Speech.Recognition.SemanticResultKey" />, these may be cast to use as <paramref name="builder" />.</returns>
      <param name="builder">A valid instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> to contain the initial grammar logic of the new <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</param>
      <param name="phrase">A <see cref="System.String" /> containing a phrase to be appended to the grammar logic of <paramref name="builder" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Add(System.String,System.Speech.Recognition.GrammarBuilder)">
      <summary>Returns a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from by appending the grammar logic of a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instance to a phrase.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> if successful, null is returned or an exception generated if the operation fails.</returns>
      <param name="phrase">A <see cref="System.String" /> containing a phrase to create the first element of the grammar logic of the returned <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</param>
      <param name="builder">A valid instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> containing grammar logic to be appended to the grammar logic of <paramref name="choices" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.Speech.Recognition.Choices)">
      <summary>Appends a <see cref="T:System.Speech.Recognition.Choices" /> instance as a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object on to the current <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</summary>
      <param name="alternateChoices">Valid instance of <see cref="T:System.Speech.Recognition.Choices" /> used to construct a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object to be appended to the current <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.Speech.Recognition.GrammarBuilder)">
      <summary>Appends an instance a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> to the current <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</summary>
      <param name="builder">Valid instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> to be appended to the current <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.Speech.Recognition.GrammarBuilder,System.Int32,System.Int32)">
      <summary>
        <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instance, which can generate a <see cref="T:System.Speech.Recognition.Grammar" /> elements requiring that audio input is to be checked for matching against repeated occurrences of a given pattern defined by a <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</summary>
      <param name="builder">A <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instance containing the grammar logic to match against audio input.</param>
      <param name="minRepeat">Specifies the minimum number of times the audio input matching the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> specified by the <paramref name="builder" /> paramenter can be repeated and still be successfully recognized.</param>
      <param name="maxRepeat">Specifies the maximum number of times the audio input matching the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> specified by the <paramref name="builder" /> paramenter can be repeated and still be successfully recognized.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.Speech.Recognition.SemanticResultKey)">
      <summary>Appends a <see cref="T:System.Speech.Recognition.SemanticResultKey" /> instance as a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object on to the current <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</summary>
      <param name="key">Valid instance of <see cref="T:System.Speech.Recognition.SemanticResultKey" /> used to construct a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object to be appended to the current <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.Speech.Recognition.SemanticResultValue)">
      <summary>Appends a <see cref="T:System.Speech.Recognition.SemanticResultValue" /> instance as a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object onto the current <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</summary>
      <param name="value">Valid instance of <see cref="T:System.Speech.Recognition.SemanticResultValue" /> used to construct a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object to be appended to the current <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.String)">
      <summary>Appends a <see cref="System.String" /> instance as a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object on to the current <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</summary>
      <param name="phrase">Valid instance of <see cref="System.String" /> used to construct a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object to be appended to the current <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.String,System.Int32,System.Int32)">
      <summary>Appends an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> to the current <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instance, which can generate a <see cref="T:System.Speech.Recognition.Grammar" /> elements requiring that audio input is to be checked for matching against repeated occurrences of a given phrase.</summary>
      <param name="phrase">A <see cref="System.String" /> instance containing a phrase to match against audio input.</param>
      <param name="minRepeat">Specifies the minimum number of times the audio input matching the <see cref="System.String" /> specified by the <paramref name="phrase" /> paramenter can be repeated and still be successfully recognized.</param>
      <param name="maxRepeat">Specifies the maximum number of times the audio input matching the <see cref="System.String" /> specified by the <paramref name="phrase" /> paramenter can be repeated and still be successfully recognized.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.String,System.Speech.Recognition.SubsetMatchingMode)">
      <summary>Appends a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object which can recognize audio input that is a subset of a given match phrase.</summary>
      <param name="phrase">
        <see cref="System.String" /> containing a phrase to be used recognized.</param>
      <param name="subsetMatchingCriteria">A member of  <see cref="T:System.Speech.Recognition.SubsetMatchingMode" /> defining the method used to match audio input which may be a subset of the match string</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.AppendDictation">
      <summary>Appends grammar logic to provide access the default free text dictation grammar provided by the Speech platform.</summary>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.AppendDictation(System.String)">
      <summary>Appends grammar logic to provide access special function free text dictation grammars on the basis of a category string.</summary>
      <param name="category">A <see cref="System.String" /> indicating the type of special function dictation grammar to be appended.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.AppendRuleReference(System.String)">
      <summary>Appends a valid grammar rule definition from an external source specified by a URI to the current <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instance.</summary>
      <param name="path">A <see cref="System.String" /> containing the Universal Resource Information Identifier (URI) of an object containing a valid grammar rule definition.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.AppendRuleReference(System.String,System.String)">
      <summary>Appends a particular valid grammar rule definition from an external source specified by a URI to the current <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instance.</summary>
      <param name="path">A <see cref="System.String" /> containing the Universal Resource Information Identifier (URI) of an object containing a valid grammar rule definition.</param>
      <param name="rule">Name of a grammar rule used as the entry point or root on the grammar definition referred to by <paramref name="path." />This parameter may be null.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.AppendWildcard">
      <summary>Appends recognition logic designed to match any input.</summary>
    </member>
    <member name="P:System.Speech.Recognition.GrammarBuilder.Culture">
      <summary>Gets or sets the culture of the grammar.</summary>
      <returns>A CultureInfo object that represents the culture of the grammar.</returns>
    </member>
    <member name="P:System.Speech.Recognition.GrammarBuilder.DebugShowPhrases">
      <summary>Writes a description of the grammar logic constructed by a <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</summary>
      <returns>Returns a <see cref="System.String" /> containing a string with an ordered list of phrases and options supported by the current <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</returns>
      <filterpriority>3</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Addition(System.Speech.Recognition.Choices,System.Speech.Recognition.GrammarBuilder)">
      <summary>Returns a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from by appending the grammar logic of a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instance to the grammar logic of a <see cref="T:System.Speech.Recognition.Choices" /> object.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> if successful, null is returned or an exception generated if the operation fails.</returns>
      <param name="choices">A valid instance of <see cref="T:System.Speech.Recognition.Choices" /> define a list of acceptable audio inputs provide the initial grammar logic of the new <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</param>
      <param name="builder">A valid instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> containing grammar logic to be appended to the grammar logic of <paramref name="choices" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Addition(System.Speech.Recognition.GrammarBuilder,System.Speech.Recognition.Choices)">
      <summary>Returns a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from by appending the grammar logic of a <see cref="T:System.Speech.Recognition.Choices" /> instance to the grammar logic of a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> if successful, null is returned or an exception generated if the operation fails.</returns>
      <param name="builder">A valid instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> to contain the initial grammar logic of the new <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</param>
      <param name="choices">A valid instance of <see cref="T:System.Speech.Recognition.Choices" /> defines a list of acceptable audio inputs to be appended to the grammar logic defined by <paramref name="builder." /></param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Addition(System.Speech.Recognition.GrammarBuilder,System.Speech.Recognition.GrammarBuilder)">
      <summary>Returns a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from by appending the grammar logic of two <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instances.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> if successful, null is returned or an exception generated if the operation fails.</returns>
      <param name="builder1">A valid instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> to contain the initial grammar logic of the new <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</param>
      <param name="builder2">A valid instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> containing grammar logic to be appended to the grammar logic of <paramref name="builder1" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Addition(System.Speech.Recognition.GrammarBuilder,System.String)">
      <summary>Returns a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from by appending a phrase to the grammar logic of a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instance.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> if successful, null is returned or an exception generated if the operation fails.Because <see cref="T:System.Speech.Recognition.GrammarBuilder" /> support implicit conversions from <see cref="T:System.Speech.Recognition.SemanticResultValue" /> and <see cref="T:System.Speech.Recognition.SemanticResultKey" />, these may be cast to use as <paramref name="builder" />.</returns>
      <param name="builder">A valid instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> to contain the initial grammar logic of the new <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</param>
      <param name="phrase">A <see cref="System.String" /> containing a phrase to be appended to the grammar logic of <paramref name="builder" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Addition(System.String,System.Speech.Recognition.GrammarBuilder)">
      <summary>Returns a new instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from by appending the grammar logic of a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instance to a phrase.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> if successful, null is returned or an exception generated if the operation fails.</returns>
      <param name="phrase">A <see cref="System.String" /> containing a phrase to create the first element of the grammar logic of the returned <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</param>
      <param name="builder">A valid instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> containing grammar logic to be appended to the grammar logic of <paramref name="choices" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Implicit(System.Speech.Recognition.Choices)~System.Speech.Recognition.GrammarBuilder">
      <summary>Implicitly converts a <see cref="T:System.Speech.Recognition.Choices" /> object to an instance of.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> containing the logical choice defined by the <paramref name="choices" /> argument.</returns>
      <param name="choices">Valid instance of <see cref="T:System.Speech.Recognition.Choices" /> to be implicitly converted to a containing <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Implicit(System.Speech.Recognition.SemanticResultKey)~System.Speech.Recognition.GrammarBuilder">
      <summary>Implicitly converts a <see cref="T:System.Speech.Recognition.SemanticResultKey" /> object to an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.SemanticResultKey" /> containing the logical choice defined by the <paramref name="semanticKey" /> argument.</returns>
      <param name="semanticKey">Valid instance of <see cref="T:System.Speech.Recognition.SemanticResultKey" /> to be implicitly converted to a containing <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Implicit(System.Speech.Recognition.SemanticResultValue)~System.Speech.Recognition.GrammarBuilder">
      <summary>Implicitly converts a <see cref="T:System.Speech.Recognition.SemanticResultValue" /> object to an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.SemanticResultValue" /> containing the logical choice defined by the <paramref name="semanticKey" /> argument.</returns>
      <param name="semanticValue">Valid instance of <see cref="T:System.Speech.Recognition.SemanticResultValue" /> to be implicitly converted to a containing <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Implicit(System.String)~System.Speech.Recognition.GrammarBuilder">
      <summary>Implicitly converts a <see cref="System.String" /> object to an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.SemanticResultValue" /> containing the logical choice defined by the <paramref name="phrase" /> argument.</returns>
      <param name="phrase">Valid instance of <see cref="System.String" /> to be implicitly converted to a containing <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Speech.Recognition.LoadGrammarCompletedEventArgs">
      <summary>Returns data from the LoadGrammarCompleted event.</summary>
    </member>
    <member name="P:System.Speech.Recognition.LoadGrammarCompletedEventArgs.Grammar">
      <summary>Gets the grammar object that has completed loading.</summary>
      <returns>An instance of <see cref="T:System.Speech.Recognition.Grammar" />, which contains a full description of a speech recognition grammar loaded by a recognition engine.</returns>
    </member>
    <member name="T:System.Speech.Recognition.RecognitionEventArgs">
      <summary>Base class for event arguments objects passed to handlers of the speech recognition events.</summary>
    </member>
    <member name="P:System.Speech.Recognition.RecognitionEventArgs.Result">
      <summary>Read only property returning about a speech recognition operation that generated a <see cref="E:System.Speech.Recognition.SpeechRecognizer.SpeechHypothesized" />, <see cref="E:System.Speech.Recognition.SpeechRecognizer.SpeechRecognized" /> or <see cref="E:System.Speech.Recognition.SpeechRecognizer.SpeechRecognitionRejected" /> event.</summary>
      <returns>Returns the <see cref="T:System.Speech.Recognition.RecognitionResult" />, which is derived from <see cref="T:System.Speech.Recognition.RecognizedPhrase" /> and contains full information about the a phrase returned by an Windows Desktop Speech Recognition Technology recognition operation.</returns>
    </member>
    <member name="T:System.Speech.Recognition.RecognitionResult">
      <summary>Represents the result of recognition engine on audio input as detailed information about the best candidate phrases matching phrase, and a list of all candidate matching phrases.</summary>
    </member>
    <member name="P:System.Speech.Recognition.RecognitionResult.Alternates">
      <summary>Provides a list of all the speech recognition candidate phrases found by a recognition engine.</summary>
      <returns>Returns a read-only list of <see cref="T:System.Speech.Recognition.RecognizedPhrase" /> objects, ranked by order of confidence.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognitionResult.Audio">
      <summary>A read-only property containing detailed information about the entire audio input used with the current recognition operation.</summary>
      <returns>Returns a <see cref="T:System.Speech.Recognition.RecognizedAudio" /> object containing all of a recognition candidate's the audio input.</returns>
    </member>
    <member name="M:System.Speech.Recognition.RecognitionResult.GetAudioForWordRange(System.Speech.Recognition.RecognizedWordUnit,System.Speech.Recognition.RecognizedWordUnit)">
      <summary>Returns a <see cref="T:System.Speech.Recognition.RecognizedAudio" /> containing detailed information about a subset of the audio input used with the current recognition operation.</summary>
      <returns>Returns a <see cref="T:System.Speech.Recognition.RecognizedAudio" /> object containing the selected subsection of the audio input.</returns>
      <param name="firstWord">A <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> object from the recognition candidate marking the first spoken word, as determined by the recognition engine, of the subsection of input audio to be returned by <see cref="M:System.Speech.Recognition.RecognitionResult.GetAudioForWordRange(System.Speech.Recognition.RecognizedWordUnit,System.Speech.Recognition.RecognizedWordUnit" />.</param>
      <param name="lastWord">A <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> object from the recognition candidate marking the last spoken word, as determined by the recognition engine, of the subsection of input audio to be returned by <see cref="M:System.Speech.Recognition.RecognitionResult.GetAudioForWordRange(System.Speech.Recognition.RecognizedWordUnit,System.Speech.Recognition.RecognizedWordUnit" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.RecognitionResult.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <param name="info">The SerializationInfo.</param>
      <param name="context">The StreamingContext.</param>
    </member>
    <member name="T:System.Speech.Recognition.RecognizeCompletedEventArgs">
      <summary>Returns data from the RecognizeCompleted event.</summary>
    </member>
    <member name="P:System.Speech.Recognition.RecognizeCompletedEventArgs.AudioPosition">
      <summary>Gets the audio position associated with the event.</summary>
      <returns>An instance of <see cref="System.TimeSpan" /> indicating the location in the audio input stream of a recognition engine of the start of the speech just processed.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizeCompletedEventArgs.BabbleTimeout">
      <summary>Gets whether a BabbleTimeout has occurred.</summary>
      <returns>Returns a bool which is true if the recognition engine has detected only background noise for longer than was specified by the <see cref="P:System.Speech.Recognition.SpeechRecognitionEngine.BabbleTimeout" /> property of the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> managing the recognition engine, otherwise false.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizeCompletedEventArgs.InitialSilenceTimeout">
      <summary>Gets whether an InitialSilenceTimeout has occurred.</summary>
      <returns>Returns a bool which is true if the recognition engine has detected no input for longer than was specified by the <see cref="P:System.Speech.Recognition.SpeechRecognitionEngine.InitialSilenceTimeout" /> property of the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> managing the recognition engine, otherwise false.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizeCompletedEventArgs.InputStreamEnded">
      <summary>Gets whether an input stream has ended.</summary>
      <returns>Returns a bool which is true if the recognition engine no longer has audio input, otherwise false.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizeCompletedEventArgs.Result">
      <summary>Gets the result of a successful recognition.</summary>
      <returns>Returns a valid instance of <see cref="T:System.Speech.Recognition.RecognitionResult" /> if the recognition operation was successful, or null if the recognition operation failed.</returns>
    </member>
    <member name="T:System.Speech.Recognition.RecognizedAudio">
      <summary>Represents audio input a recognition engine used to generate candidate phrases result. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedAudio.AudioPosition">
      <summary>Read-only property returning the position in a recognition engine's audio stream of the audio input it processed.</summary>
      <returns>Returns a <see cref="System.TimeSpan" /> object.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedAudio.Duration">
      <summary>Read-only property returning length of the audio input a recognition engine processed.</summary>
      <returns>Returns a <see cref="System.TimeSpan" /> indicating the duration of the audio input<see cref="." /></returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedAudio.Format">
      <summary>Read-only property returning the format in audio processed by an recognition engine.</summary>
      <returns>Returns a <see cref="T:System.Speech.AudioFormat.SpeechAudioFormatInfo" /> value indicating the format of the recognized audio.</returns>
    </member>
    <member name="M:System.Speech.Recognition.RecognizedAudio.GetRange(System.TimeSpan,System.TimeSpan)">
      <summary>Selects and returns a subsection of section of the current recognized audio as pure binary data.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.RecognizedAudio" /> containing the subset of the original input selected by the values of <paramref name="audioPosition" /> and <paramref name="duration" />.</returns>
      <param name="audioPosition">A <see cref="System.TimeSpan" /> indicating the starting point of the audio to be returned.</param>
      <param name="duration">A <see cref="System.TimeSpan" /> indicating length of the segment following <paramref name="audioPosition" /> to be returned.</param>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedAudio.StartTime">
      <summary>Read-only property providing the time audio input began.</summary>
      <returns>Returns a <see cref="System.DateTime" /> object.</returns>
    </member>
    <member name="M:System.Speech.Recognition.RecognizedAudio.WriteToAudioStream(System.IO.Stream)">
      <summary>Write entire audio to a stream as raw data.</summary>
      <param name="outputStream">Any valid <see cref="System.IO.Stream" /> object, to which an application wishes to write audio data in binary format.</param>
    </member>
    <member name="M:System.Speech.Recognition.RecognizedAudio.WriteToWaveStream(System.IO.Stream)">
      <summary>Writes audio to a stream in Wave format.</summary>
      <param name="outputStream">Any valid <see cref="System.IO.Stream" /> object, to which an application wishes to write audio data in Wave format</param>
    </member>
    <member name="T:System.Speech.Recognition.RecognizedPhrase">
      <summary>Represents detailed information about a candidate phrase found by a recognition engine as matching audio input. </summary>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.Confidence">
      <summary>Returns the measure of certainty for a <see cref="T:System.Speech.Recognition.RecognizedPhrase" /> returned by a recognition engine.</summary>
      <returns>Returns a float a relative measure of the certainty of correct recognition for the phrase information returned in the current <see cref="T:System.Speech.Recognition.RecognizedPhrase" /> instance.</returns>
    </member>
    <member name="M:System.Speech.Recognition.RecognizedPhrase.ConstructSmlFromSemantics">
      <summary>Constructs an object containing a valid Semantic Markup Language (SML) based description of the semantics of a <see cref="T:System.Speech.Recognition.Grammar" /> used to obtain the current instance of <see cref="T:System.Speech.Recognition.RecognizedPhrase" />. </summary>
      <returns>Returns an object implementing the <see cref="T:System.Xml.XPath.IXPathNavigable" /> interface and containing an SML description of the semantics of the <see cref="T:System.Speech.Recognition.Grammar" /> used to obtain the current <see cref="T:System.Speech.Recognition.RecognizedPhrase." /></returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.Grammar">
      <summary>Returns the <see cref="T:System.Speech.Recognition.Grammar" /> used by a recognition engine to return the current <see cref="T:System.Speech.Recognition.RecognizedPhrase" />.</summary>
      <returns>Returns and instance of <see cref="T:System.Speech.Recognition.Grammar" /> corresponding the the <see cref="T:System.Speech.Recognition.Grammar" /> used to obtain the current <see cref="T:System.Speech.Recognition.RecognizedPhrase" />.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.HomophoneGroupId">
      <summary>Returns the identifier for the group from which the homophones available on an instance of <see cref="T:System.Speech.Recognition.RecognizedPhrase" /> are derived.</summary>
      <returns>Returns an int value that identifies for the group homophones used to provide the values returned by the <see cref="P:System.Speech.Recognition.RecognizedPhrase.Homophones" /> property on a <see cref="T:System.Speech.Recognition.RecognizedPhrase" /> object.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.Homophones">
      <summary>Returns a collection of homophones for the  current recognized phrase.</summary>
      <returns>Returns a <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection" /> of <see cref="T:System.Speech.Recognition.RecognizedPhrase" /> instances, each of which is a potential homophone for the current <see cref="T:System.Speech.Recognition.RecognizedPhrase" />.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.ReplacementWordUnits">
      <summary>Returns information about the location and content of text originally recognized and then replaced by text normalization when a recognition engine creates the value of the <see cref="P:System.Speech.Recognition.RecognizedPhrase.Text" /> property on an instance of <see cref="T:System.Speech.Recognition.RecognizedPhrase" />.</summary>
      <returns>Returns a <see cref="T:System.Collections.ObjectModel.Collection" /> object containing <see cref="T:System.Speech.Recognition.ReplacementText" /> instances corresponding to each phrase or text replaced by the recognition engine during speech normalization.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.Semantics">
      <summary>Returns the any of semantic logic of a <see cref="T:System.Speech.Recognition.Grammar" /> that was used to return a <see cref="T:System.Speech.Recognition.RecognizedPhrase" />.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.SemanticValue" /> specifying what, if any, semantic logic was used by the recognition engine to produce the current <see cref="T:System.Speech.Recognition.RecognizedPhrase" />.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.Text">
      <summary>Returns the normalized text obtained by a recognition engine from audio input.</summary>
      <returns>A <see cref="T:System.String" /> instance containing the normalized text recognized from audio input.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.Words">
      <summary>Returns text obtained from audio input from a recognition engine as a <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection" /> of <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> instances.</summary>
      <returns>Returns recognized text as a <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection" /> of <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> instances, ordered as their text appears in the <see cref="T:System.String" /> returned by <see cref="P:System.Speech.Recognition.RecognizedPhrase.Text" />.</returns>
    </member>
    <member name="T:System.Speech.Recognition.RecognizedWordUnit">
      <summary>Provides the atomic unit of recognized speech.</summary>
    </member>
    <member name="M:System.Speech.Recognition.RecognizedWordUnit.#ctor(System.String,System.Single,System.String,System.String,System.Speech.Recognition.DisplayAttributes,System.TimeSpan,System.TimeSpan)">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Recognition.RecognizedWordUnit" />.</summary>
      <param name="text">
        <see cref="System.String" /> containing normalized text for a recognized word.This value may be null, <see cref="&quot;&quot;" /> or <see cref="System.String.Empty" />.</param>
      <param name="confidence">An float value between 1.0 and 0.0 indicating the recognition certainty of word recognition.</param>
      <param name="pronunciation">
        <see cref="System.String" /> containing phonetic information for a recognized word.This value may be null, <see cref="&quot;&quot;" /> or <see cref="System.String.Empty" />.</param>
      <param name="lexicalForm">
        <see cref="System.String" /> containing unnormalized text for a recognized word.This argument is requred and may not be null, <see cref="&quot;&quot;" /> or <see cref="System.String.Empty" />.</param>
      <param name="displayAttributes">
        <see cref="T:System.Speech.Recognition.DisplayAttributes" /> instance containing formatting</param>
      <param name="audioPosition">A <see cref="System.TimeSpan" /> instance indicating the location in the audio input stream of the recognized word.This value may be <see cref="System.TimeSpan.Zero." /></param>
      <param name="audioDuration">A <see cref="System.TimeSpan" /> instance indicating the length of the audio input corresponding to the recognized word. This value may be <see cref="System.TimeSpan.Zero." /></param>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedWordUnit.Confidence">
      <summary>Returns a measure of the recognition engine certainty as to the correctness of a word recognition.</summary>
      <returns>Returns a float, typically between 0 and 1, indicating e recognition engine certainty as to the correctness of a word recognition. </returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedWordUnit.DisplayAttributes">
      <summary>Returns formatting information used in creating the text output from the current <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> instance.</summary>
      <returns>Returns a valid instance of <see cref="T:System.Speech.Recognition.DisplayAttributes" />, containing formatting for the text output from the current <see cref="T:System.Speech.Recognition.RecognizedWordUnit" />. </returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedWordUnit.LexicalForm">
      <summary>Returns the text of a recognized word without normalization.</summary>
      <returns>Returns a <see cref="System.String" /> containing the text of a recognized word, without any normalization.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedWordUnit.Pronunciation">
      <summary>Returns the phonetic spelling of a recognized word.</summary>
      <returns>Returns an <see cref="System.String" /> containing a phonetic description of a word using the International Phonetic Alphabet (IPA).</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedWordUnit.Text">
      <summary>Returns a <see cref="System.String" /> containing the normalized text output.</summary>
      <returns>Returns an instance of <see cref="System.String" /> containing the normalized text output for a given input word.</returns>
    </member>
    <member name="T:System.Speech.Recognition.RecognizeMode">
      <summary>Enumerates values of the recognition mode.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Recognition.RecognizeMode.Single">
      <summary>Specifies that recognition terminates after completion.</summary>
    </member>
    <member name="F:System.Speech.Recognition.RecognizeMode.Multiple">
      <summary>Specifies that recognition does not terminate after completion.</summary>
    </member>
    <member name="T:System.Speech.Recognition.RecognizerInfo">
      <summary>Represents information about a SpeechRecognizer or SpeechRecognitionEngine.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerInfo.AdditionalInfo">
      <summary>Gets additional information about a SpeechRecognizer or SpeechRecognitionEngine.</summary>
      <returns>Returns an instance of <see cref="System.Collections.Generic.IDictionary" /> containing information about the configuration of a recognition engine (<see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />).</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerInfo.Culture">
      <summary>Gets the culture of a SpeechRecognizer or SpeechRecognitionEngine.</summary>
      <returns>Returns an instance of <see cref="System.Globalization.CultureInfo" />, containing information about the culture supported by a given recognition engine (<see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />), including the name of the culture, the writing system, the calendar used, and how to format dates and sort strings.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerInfo.Description">
      <summary>Gets the description of a SpeechRecognizer or SpeechRecognitionEngine.</summary>
      <returns>Returns a string containing a description of a particular recognition engine (<see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />) configuration.. </returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerInfo.Id">
      <summary>Gets the ID of a SpeechRecognizer or SpeechRecognitionEngine.</summary>
      <returns>Returns a string identifying a particular recognition engine (<see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />) configuration. </returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerInfo.Name">
      <summary>Gets the friendly name of a SpeechRecognizer or SpeechRecognitionEngine.</summary>
      <returns>Returns a string containing a friendly name of a particular recognition engine (<see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />) configuration. </returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerInfo.SupportedAudioFormats">
      <summary>Gets the audio formats supported by a SpeechRecognizer or SpeechRecognitionEngine.</summary>
      <returns>Returns an list of audio formats supported by the recognition engine (<see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />) configuration as a <see cref="System.Collections.ObjectModel.ReadOnlyCollection" /> of <see cref="T:System.Speech.AudioFormat.SpeechAudioFormatInfo" /> objects  </returns>
    </member>
    <member name="T:System.Speech.Recognition.RecognizerState">
      <summary>Enumerates values of the recognizer's state.</summary>
    </member>
    <member name="F:System.Speech.Recognition.RecognizerState.Stopped">
      <summary>The recognition engine is not receiving or analyzing audio input.</summary>
    </member>
    <member name="F:System.Speech.Recognition.RecognizerState.Listening">
      <summary>The recognition engine is available to receive and analyze audio input.</summary>
    </member>
    <member name="T:System.Speech.Recognition.RecognizerUpdateReachedEventArgs">
      <summary>Returns data from the RecognizerUpdateReached event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerUpdateReachedEventArgs.AudioPosition">
      <summary>Gets the audio position associated with the event.</summary>
      <returns>Returns an instance of <see cref="System.TimeSpan" />, which contains the location active within the speech buffer of a recognition engine, when the engine pauses and generates a RecognizerUpdateReached event (<see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.RecognizerUpdateReached" /> and<see cref="E:System.Speech.Recognition.SpeechRecognizer.RecognizerUpdateReached" />).</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerUpdateReachedEventArgs.UserToken">
      <summary>Gets the UserToken passed to the system when an application creates a recognition engine configuration modification request.</summary>
      <returns>Returns an instance of <see cref="System.Object" /> supplied by call to RequestRecognizerUpdate method (<see cref="M:System.Speech.Recognition.SpeechRecognitionEngine.RequestRecognizerUpdate" />or <see cref="M:System.Speech.Recognition.SpeechRecognizer.RequestRecognizerUpdate" />, which created a recognizer update request.</returns>
    </member>
    <member name="T:System.Speech.Recognition.ReplacementText">
      <summary>Contains originally recognized text replace using speech normalization by a recognition engine.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.ReplacementText.CountOfWords">
      <summary>Returns the number of originally recognized words replaced through text normalization by a recognition engine.</summary>
      <returns>Returns an int containing the number of originally recognized words replaced through normalization by a recognition engine</returns>
    </member>
    <member name="P:System.Speech.Recognition.ReplacementText.DisplayAttributes">
      <summary>Returns the information on how the text originally recognized but replaced through text normalization by a recognition engine should be displayed.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.DisplayAttributes" /> stipulating how text originally recognized but replaced through text normalization by a recognition engine should be displayed.</returns>
    </member>
    <member name="P:System.Speech.Recognition.ReplacementText.FirstWordIndex">
      <summary>Returns the location of the first word of originally recognized words replaced through text normalization by a recognition engine.</summary>
      <returns>Returns an int containing the location in the current text of text originally recognized but replaced by normalization.</returns>
    </member>
    <member name="P:System.Speech.Recognition.ReplacementText.Text">
      <summary>Returns the originally recognized text replaced through text normalization by a recognition engine.</summary>
      <returns>Returns a <see cref="System.String" /> containing the text originally recognized by a recognition engine and replaced by text normalization.</returns>
    </member>
    <member name="T:System.Speech.Recognition.SemanticResultKey">
      <summary>Attaches key string to <see cref="T:System.Speech.Recognition.SemanticResultValue" /> values to define the <see cref="T:System.Speech.Recognition.SemanticValue" /> objects created in <see cref="T:System.Speech.Recognition.Grammar" /> using <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instances</summary>
    </member>
    <member name="M:System.Speech.Recognition.SemanticResultKey.#ctor(System.String,System.Speech.Recognition.GrammarBuilder[])">
      <summary>Assigns a semantic key to one of more <see cref="T:System.Speech.Recognition.GrammarBuilder" /> objects used to construct a <see cref="T:System.Speech.Recognition.Grammar" />.</summary>
      <param name="semanticResultKey">
        <see cref="System.String" /> object containing the tag to be used access the <see cref="T:System.Speech.Recognition.SemanticValue" /> instance associated with the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> objects specified by the <paramref name="builders" /> argument. </param>
      <param name="builders">One or more <see cref="T:System.Speech.Recognition.GrammarBuilder" /> objects which will be associated with a <see cref="T:System.Speech.Recognition.SemanticValue" /> object accessible with the tag defined in <paramref name="semanticResultKey" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticResultKey.#ctor(System.String,System.String[])">
      <summary>Assigns a semantic key to one of more <see cref="System.String" /> instances used to construct a <see cref="T:System.Speech.Recognition.Grammar" />.</summary>
      <param name="semanticResultKey">
        <see cref="System.String" /> object containing the tag to be used access the <see cref="T:System.Speech.Recognition.SemanticValue" /> instance associated with the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> objects specified by the <paramref name="phrases" /> argument.</param>
      <param name="phrases">One or more <see cref="System.String" /> objects, whose concatenated text will be associated with a <see cref="T:System.Speech.Recognition.SemanticValue" /> object accessible with the tag defined in <paramref name="semanticResultKey" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticResultKey.ToGrammarBuilder">
      <summary>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from the current <see cref="T:System.Speech.Recognition.SemanticResultKey" /> instance.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from the current <see cref="T:System.Speech.Recognition.SemanticResultKey" /> instance.</returns>
    </member>
    <member name="T:System.Speech.Recognition.SemanticResultValue">
      <summary>Sets values to <see cref="T:System.Speech.Recognition.SemanticValue" /> objects created in <see cref="T:System.Speech.Recognition.Grammar" /> using <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instances.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SemanticResultValue.#ctor(System.Object)">
      <summary>Constructs an instance of <see cref="T:System.Speech.Recognition.SemanticResultValue" /> with a specified value.</summary>
      <param name="value">An instance of <see cref="T:System.Object" /> specifying the value the <see cref="T:System.Speech.Recognition.SemanticResultValue" /> managed. Must be of type bool, int, float, or string.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticResultValue.#ctor(System.Speech.Recognition.GrammarBuilder,System.Object)">
      <summary>Creates an instance of <see cref="T:System.Speech.Recognition.SemanticResultValue" /> with a given value and associates it with a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> instance.</summary>
      <param name="builder">An instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> specifying some grammar element to be used in recognition.</param>
      <param name="value">An instance of <see cref="T:System.Object" /> specifying the value the <see cref="T:System.Speech.Recognition.SemanticResultValue" /> managed. Must be of type bool, int, float, or string.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticResultValue.#ctor(System.String,System.Object)">
      <summary>Creates an instance of <see cref="T:System.Speech.Recognition.SemanticResultValue" /> with a given value and associates it with a phrase.</summary>
      <param name="phrase">A <see cref="T:System.String" /> containing a phrase specifying words to be used in recognition.</param>
      <param name="value">An instance of <see cref="T:System.Object" /> specifying the value the <see cref="T:System.Speech.Recognition.SemanticResultValue" /> managed. Must be of type bool, int, float, or string.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticResultValue.ToGrammarBuilder">
      <summary>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from the current <see cref="T:System.Speech.Recognition.SemanticResultValue" /> instance.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from the current <see cref="T:System.Speech.Recognition.SemanticResultValue" /> instance.</returns>
    </member>
    <member name="T:System.Speech.Recognition.SemanticValue">
      <summary>Represents the semantic organization of a recognized phrase.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.#ctor(System.Object)">
      <summary>Creates a <see cref="T:System.Speech.Recognition.SemanticValue" /> object with a particular value.</summary>
      <param name="value">An object containing information to be store in <see cref="T:System.Speech.Recognition.SemanticValue" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.#ctor(System.String,System.Object,System.Single)">
      <summary>Creates a <see cref="T:System.Speech.Recognition.SemanticValue" /> object with a particular value, key name and confidence level.</summary>
      <param name="keyName">A string containing a key that be used to reference this <see cref="T:System.Speech.Recognition.SemanticValue" /> instance.</param>
      <param name="value">An object containing information to be store in <see cref="T:System.Speech.Recognition.SemanticValue" /> object.</param>
      <param name="confidence">A float containing an estimate of the certainty of semantic analysis.</param>
    </member>
    <member name="P:System.Speech.Recognition.SemanticValue.Confidence">
      <summary>Returns a relative measure of the certainty as to the correctness of the semantic parsing that returned the current instance of <see cref="T:System.Speech.Recognition.SemanticValue" />.</summary>
      <returns>Returns a float a relative measure of the certainty of correct recognition for the phrase information returned in the current <see cref="T:System.Speech.Recognition.SemanticValue" /> instance.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.Contains(System.Collections.Generic.KeyValuePair{System.String,System.Speech.Recognition.SemanticValue})">
      <summary>Indicates whether the current <see cref="T:System.Speech.Recognition.SemanticValue" /> instance collection contains a specific key and specific instance of <see cref="T:System.Speech.Recognition.SemanticValue" /> expressed as a key value pair.</summary>
      <returns>Returns a bool which is true if the current <see cref="T:System.Speech.Recognition.SemanticValue" /> contains an instance of KeyValuePare&lt;String,SemanticValue&gt; for a specified value of the key string and the <see cref="T:System.Speech.Recognition.SemanticValue" />. Otherwise, false is returned.</returns>
      <param name="item">An instance of <see cref="System.Collections.Generic.KeyValuePair" /> instanitated for given value of a key string and a <see cref="T:System.Speech.Recognition.SemanticValue" /> instance. </param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.ContainsKey(System.String)">
      <summary>Indicates whether the current <see cref="T:System.Speech.Recognition.SemanticValue" /> instance collection contains a child <see cref="T:System.Speech.Recognition.SemanticValue" /> instance with a given key string.</summary>
      <returns>Returns a bool, true if a child instance <see cref="T:System.Speech.Recognition.SemanticValue" /> tagged with the string <paramref name="key" /> is found, false if not.</returns>
      <param name="key">
        <see cref="System.String" /> containing the key string used to identify a child instance of <see cref="T:System.Speech.Recognition.SemanticValue" /> under the current <see cref="T:System.Speech.Recognition.SemanticValue" />.</param>
    </member>
    <member name="P:System.Speech.Recognition.SemanticValue.Count">
      <summary>Returns the number of child <see cref="T:System.Speech.Recognition.SemanticValue" /> objects under the current <see cref="T:System.Speech.Recognition.SemanticValue" /> instance.</summary>
      <returns>Returns an int containing the number of child <see cref="T:System.Speech.Recognition.SemanticValue" /> objects the current <see cref="T:System.Speech.Recognition.SemanticValue" /> has.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.Equals(System.Object)">
      <param name="obj"></param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.GetHashCode">
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SemanticValue.Item(System.String)">
      <summary>Provides read-only indexing child <see cref="T:System.Speech.Recognition.SemanticValue" /> instances belonging to the current <see cref="T:System.Speech.Recognition.SemanticValue" />.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.SemanticValue" /> that is a child of the current <see cref="T:System.Speech.Recognition.SemanticValue" /> and indexable as part of a key value pair: KeyValuePair&lt;String,SemanticValue&gt;.</returns>
      <param name="key">A <see cref="System.String" /> containing a key for a KeyValuePair&lt;String,SemanticValue&gt; contained in the current instance of <see cref="T:System.Speech.Recognition.SemanticValue" /></param>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Thrown if no child member of the current instance of <see cref="T:System.Speech.Recognition.SemanticValue" /> has the key matching the <paramref name="key" /> parameter.</exception>
      <exception cref="T:System.InvalidOperationException">Thrown is code attempts to change the <see cref="T:System.Speech.Recognition.SemanticValue" /> at a given index.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{System.String,System.Speech.Recognition.SemanticValue})"></member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#ICollection{T}#Clear"></member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Speech.Recognition.SemanticValue}[],System.Int32)"></member>
    <member name="P:System.Speech.Recognition.SemanticValue.System#Collections#Generic#ICollection{T}#IsReadOnly"></member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{System.String,System.Speech.Recognition.SemanticValue})"></member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#IDictionary{TKey@TValue}#Add(System.String,System.Speech.Recognition.SemanticValue)"></member>
    <member name="P:System.Speech.Recognition.SemanticValue.System#Collections#Generic#IDictionary{TKey@TValue}#Keys"></member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#IDictionary{TKey@TValue}#Remove(System.String)"></member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#IDictionary{TKey@TValue}#TryGetValue(System.String,System.Speech.Recognition.SemanticValue@)"></member>
    <member name="P:System.Speech.Recognition.SemanticValue.System#Collections#Generic#IDictionary{TKey@TValue}#Values"></member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#IEnumerable{T}#GetEnumerator"></member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="P:System.Speech.Recognition.SemanticValue.Value">
      <summary>Read only property returning the information contained in the current <see cref="T:System.Speech.Recognition.SemanticValue" />.</summary>
      <returns>Returns a <see cref="System.Object" /> instance containing the information stored in the current <see cref="T:System.Speech.Recognition.SemanticValue" /> instance.</returns>
    </member>
    <member name="T:System.Speech.Recognition.SpeechDetectedEventArgs">
      <summary>Returns data from the SpeechDetected event.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechDetectedEventArgs.AudioPosition">
      <summary>Gets the audio position of a detected speech segment.</summary>
      <returns>Returns an instance of System.TimeSpan, which contains thelocation of a detected phrase within the speech buffer of a recognition engine.</returns>
    </member>
    <member name="T:System.Speech.Recognition.SpeechHypothesizedEventArgs">
      <summary>Returns notification from the SpeechHypothesized event.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SpeechRecognitionEngine">
      <summary>Provides access to run any properly installed speech recognition services found on a Windows Desktop system.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.#ctor">
      <summary>Constructs an instance of <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> using the current system default recognition engine.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.#ctor(System.Globalization.CultureInfo)">
      <summary>Constructs an instance of <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> using the default recognition engine for a given locale.</summary>
      <param name="culture">A valid instance of <see cref="T:System.Globalization.CultureInfo" /> to specify a locale or language.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.#ctor(System.Speech.Recognition.RecognizerInfo)"></member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.#ctor(System.String)">
      <summary>Constructs an instance of <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> based on the registry key name for a recognition engine.</summary>
      <param name="recognizerId"></param>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.AudioFormat">
      <summary>Read-only property specifying the current format of audio input being processed by the recognition engine.</summary>
      <returns>Returns an instance <see cref="T:System.Speech.AudioFormat.SpeechAudioFormatInfo" /> containing information about the current format of audio input being processed.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.AudioLevel">
      <summary>Gets the audio level of the SpeechRecognitionEngine.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.AudioLevelUpdated">
      <summary>Raised when the audio level of the SpeechRecognitionEngine is updated.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.AudioPosition">
      <summary>Gets the audio position of the SpeechRecognitionEngine.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.AudioSignalProblemOccurred">
      <summary>Raised when the SpeechRecognitionEngine encounters an audio signal problem.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.AudioState">
      <summary>Gets the audio state of the SpeechRecognitionEngine.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.AudioStateChanged"></member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.BabbleTimeout">
      <summary>Gets and set a time out period for only background noise inputs.</summary>
      <returns>Gets and sets a valid instance of <see cref="T:System.TimeSpan" /> used to define the period of time a recognition engine will accept input consisting of background noise before generating an error.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.Dispose"></member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.Dispose(System.Boolean)">
      <param name="disposing"></param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.EmulateRecognize(System.Speech.Recognition.RecognizedWordUnit[],System.Globalization.CompareOptions)">
      <summary>Synchronously simulate audio input to the Windows Desktop Speech Technology recognition engine with an array of <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> objects and specified case sensitivity.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.RecognitionResult" /> containing the results of speech recognition. The instance is null if recognition fails, or if the recognitionEngine is not enabled.</returns>
      <param name="wordUnits">Array of <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> objects used as an input phrase in place of audio input during recognition emulation.</param>
      <param name="compareOptions">A member of the <see cref="T:System.Globalization.CompareOptions" /> enumeration determining whether the emulation is case sensitive or case insensitive.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.EmulateRecognize(System.String)">
      <summary>Synchronously simulate audio input to the Windows Desktop Speech Technology recognition engine with a <see cref="T:System.String" />.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.RecognitionResult" /> containing the results of speech recognition. The instance is null if recognition fails, or if the recognitionEngine is not enabled.</returns>
      <param name="inputText">A <see cref="T:System.String" /> containing text used as an input phrase in place of audio input during recognition emulation.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.EmulateRecognize(System.String,System.Globalization.CompareOptions)">
      <summary>Synchronously simulate audio input to the Windows Desktop Speech Technology recognition engine with a <see cref="T:System.String" /> and specified case sensitivity.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.RecognitionResult" /> containing the results of speech recognition. The instance is null if recognition fails, or if the recognitionEngine is not enabled.</returns>
      <param name="inputText">A <see cref="T:System.String" /> containing text used as an input phrase in place of audio input during recognition emulation.</param>
      <param name="compareOptions">A member of the <see cref="T:System.Globalization.CompareOptions" /> enumeration determining whether the emulation is case sensitive or case insensitive.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.EmulateRecognizeAsync(System.Speech.Recognition.RecognizedWordUnit[],System.Globalization.CompareOptions)">
      <summary>Asynchronously simulate audio input to the Windows Desktop Speech Technology recognition engine with an array of <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> objects and specified case sensitivity.</summary>
      <param name="wordUnits">Array of <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> objects used as an input phrase in place of audio input during recognition emulation.</param>
      <param name="compareOptions">A member of the <see cref="T:System.Globalization.CompareOptions" /> enumeration determining whether the emulation is case sensitive or case insensitive.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.EmulateRecognizeAsync(System.String)">
      <summary>Asynchronously simulate audio input to the Windows Desktop Speech Technology recognition engine with <see cref="string" />.</summary>
      <param name="inputText">A <see cref="string" /> containing text used as an input phrase in place of audio input during recognition emulation.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.EmulateRecognizeAsync(System.String,System.Globalization.CompareOptions)">
      <summary>Asynchronously simulate audio input to the Windows Desktop Speech Technology recognition engine with a <see cref="string" /> and specified case sensitivity.</summary>
      <param name="inputText">A <see cref="string" /> containing text used as an input phrase in place of audio input during recognition emulation.</param>
      <param name="compareOptions">A member of the <see cref="T:System.Globalization.CompareOptions" /> enumeration determining whether the emulation is case sensitive or case insensitive.</param>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.EmulateRecognizeCompleted">
      <summary>Event generated when an asynchronous emulation of speech recognition completes.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.EndSilenceTimeout">
      <summary>Gets and set the amount of silence a recognition engine requires before completing an unambiguous recognition. </summary>
      <returns>Gets and sets a valid instance of <see cref="T:System.TimeSpan" /> used to define how much silence following a valid audio input is required before an unambigous recognition operation should complete.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.EndSilenceTimeoutAmbiguous">
      <summary>Gets and set the amount of silence a recognition engine requires before completing an ambiguous recognition.</summary>
      <returns>Gets and sets a valid instance of <see cref="T:System.TimeSpan" /> used to define how much silence following a valid audio input is required before an ambigous recognition operation should complete.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.Grammars">
      <summary>Read-only property returning a list of all <see cref="T:System.Speech.Recognition.Grammar" /> objects loaded into the current instance of the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</summary>
      <returns>Returns an list of the <see cref="T:System.Speech.Recognition.Grammar" /> objects loaded into the current instance of the <see cref="T:System.Speech.Recognition.SpeechRecognizer" />.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.InitialSilenceTimeout">
      <summary>Gets and set the maximum amount of silence that can precede audio input before a recognition engine generates an error.</summary>
      <returns>Gets and sets a valid instance of <see cref="T:System.TimeSpan" /> specifying the maximum amount of time a recognition engine should accept silence before generating an error.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.InstalledRecognizers">
      <summary>Returns a list of all available recognition engines on a system.</summary>
      <returns>Returns an instance of <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection" /> containing an instance of <see cref="T:System.Speech.Recognition.RecognizerInfo" /> fully specifying each recognition engine available on a system supporting the Speech platform.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.LoadGrammar(System.Speech.Recognition.Grammar)">
      <summary>Synchronously loads a specific grammar, as specified by a <see cref="T:System.Speech.Recognition.Grammar" />, for use by a <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</summary>
      <param name="grammar">An instance of <see cref="T:System.Speech.Recognition.Grammar" /> specify the grammar to add to and instance of <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.LoadGrammarAsync(System.Speech.Recognition.Grammar)">
      <summary>Asynchronously loads a specific grammar, as specified by a <see cref="T:System.Speech.Recognition.Grammar" />, for use by a <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /></summary>
      <param name="grammar">An instance of <see cref="T:System.Speech.Recognition.Grammar" /> specify the grammar to add to and instance of <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /></param>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.LoadGrammarCompleted">
      <summary>Event generated when an asynchronous load of a <see cref="T:System.Speech.Recognition.Grammar" /> object in to a <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> completes.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.MaxAlternates">
      <summary>Gets and sets the maximum number of candidate results an instance of <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> is to return.</summary>
      <returns>Gets and sets an int specifying the maximum number of candidate phrases will be returned by a recognition engine.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.QueryRecognizerSetting(System.String)">
      <param name="settingName"></param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.Recognize">
      <summary>Provides a single synchronous recognition operation on recognition engine input.</summary>
      <returns>Returns a valid instance of <see cref="T:System.Speech.Recognition.RecognitionResult" /> is the recognition engine has be able to recognize the input, null is recognition fails.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.Recognize(System.TimeSpan)">
      <summary>Provides a single synchronous recognition operation, with a predetermined time out, on recognition engine input.</summary>
      <returns>Returns a valid instance of <see cref="T:System.Speech.Recognition.RecognitionResult" /> is the recognition engine has be able to recognize the input, null is recognition fails.</returns>
      <param name="initialSilenceTimeout">A valid instance of <see cref="T:System.TimeSpan" /> defining a time out period, after which if no input is received an error is generated.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.RecognizeAsync">
      <summary>Provides a single asynchronous recognition operation on recognition engine input.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.RecognizeAsync(System.Speech.Recognition.RecognizeMode)">
      <summary>Provides either single or multiple asynchronous recognition reoperations on recognition engine input.</summary>
      <param name="mode">A member of <see cref="T:System.Speech.Recognition.RecognizeMode" /> enumeration specifying either multiple or single recognition operations.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.RecognizeAsyncCancel">
      <summary>Terminates a running instance of a recognition engine without waiting for the current recognition operation to complete.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.RecognizeAsyncStop">
      <summary>Terminates a running instance of a recognition engine after the current recognition operation to completes.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.RecognizeCompleted">
      <summary>The event raised when an asynchronous recognition operation completes.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.RecognizerAudioPosition">
      <summary>Gets the audio position of the SpeechRecognizer.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.RecognizerInfo"></member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.RecognizerUpdateReached">
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.RequestRecognizerUpdate">
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.RequestRecognizerUpdate(System.Object)">
      <param name="userToken"></param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.RequestRecognizerUpdate(System.Object,System.TimeSpan)">
      <param name="userToken"></param>
      <param name="audioPositionAheadToRaiseUpdate"></param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.SetInputToAudioStream(System.IO.Stream,System.Speech.AudioFormat.SpeechAudioFormatInfo)">
      <summary>Assigns a specific audio stream as the input for the current instance of <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</summary>
      <param name="audioSource">A valid <see cref="T:System.IO.Stream" /> instance connected to an audio source.</param>
      <param name="audioFormat">A valid member of the <see cref="T:System.Speech.AudioFormat.SpeechAudioFormatInfo" /> enumeration specifying the format of the audio input.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.SetInputToDefaultAudioDevice">
      <summary>Assigns the system default audio input to the current instance of <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.SetInputToNull">
      <summary>Assigns a null device as the input for the current instance of <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.SetInputToWaveFile(System.String)">
      <summary>Assigns a specfic Wave format audio file as input to the current instance of <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</summary>
      <param name="path">A <see cref="T:System.String" /> containing a path to the wave file to be used as input.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.SetInputToWaveStream(System.IO.Stream)">
      <summary>Assigns an audio stream in Wave format as input to the current <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance.</summary>
      <param name="audioSource">A valid <see cref="T:System.IO.Stream" /> instance connected to an audio source in Wave format.</param>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechDetected">
      <summary>Event raised by recognition engine when speech is detected.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechHypothesized">
      <summary>Event raised when when the recognition engine detects speech and part of the audio input speech has been tentatively recognized.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechRecognitionRejected">
      <summary>The event raised when the recognition engine detects speech, but can only return candidate phrases with low confidence levels.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechRecognized">
      <summary>The event raised when the recognition engine detects speech, and has found one or more phrases with sufficient confidence levels.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.UnloadAllGrammars">
      <summary>Unloads all <see cref="T:System.Speech.Recognition.Grammar" /> objects currently loaded in by a Windows Desktop Speech Technology recognition engine.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.UnloadGrammar(System.Speech.Recognition.Grammar)">
      <summary>Unloads a grammar, as specified by an instance of <see cref="T:System.Speech.Recognition.Grammar" />, from an instance of <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</summary>
      <param name="grammar">An instance of <see cref="T:System.Speech.Recognition.Grammar" /> specify the grammar to remove from an instance of <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.UpdateRecognizerSetting(System.String,System.Int32)">
      <param name="settingName"></param>
      <param name="updatedValue"></param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.UpdateRecognizerSetting(System.String,System.String)">
      <param name="settingName"></param>
      <param name="updatedValue"></param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Speech.Recognition.SpeechRecognitionRejectedEventArgs">
      <summary>Returns notification from the SpeechRecognitionRejected event.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SpeechRecognizedEventArgs">
      <summary>Returns notification from the SpeechRecognized event.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SpeechRecognizer">
      <summary>Provides access to the default shared speech recognition service available on the Windows Desktop. </summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.#ctor">
      <summary>Construct and returns a new instance of an object.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.AudioFormat">
      <summary>Read-only property specifying the current format of audio input being processed by the recognition engine.</summary>
      <returns>Returns an instance <see cref="T:System.Speech.AudioFormat.SpeechAudioFormatInfo" /> containing information about the current format of audio input being processed.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.AudioLevel">
      <summary>Read-only property specifying the current audio level of the input being processed by the recognition engine.</summary>
      <returns>Returns an integer value representing the audio level returned by the current SpeechRecognizer instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.AudioLevelUpdated">
      <summary>Event raised when by recogintion when a change in the audio level is detected. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.AudioPosition">
      <summary>Read-only property specifying the position in the audio stream of input being processed by the recognition engine.</summary>
      <returns>Returns a TimeSpan object that represents the audio position in a number of milliseconds.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.AudioSignalProblemOccurred">
      <summary>Event generated when an error is detected in a SpeechRecognitionEngine.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.AudioState">
      <summary>Enumeration used to describe the type of audio the recognizer is currently processing.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.AudioStateChanged">
      <summary>Event generated when a change in the current state of the audio input in a recognition engine is detected.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.Dispose">
      <summary>Releases all resources used by the recognition engine.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.Dispose(System.Boolean)">
      <summary>Releases any unmanaged resources used by the SpeechRecognizer object and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.EmulateRecognize(System.Speech.Recognition.RecognizedWordUnit[],System.Globalization.CompareOptions)">
      <summary>Synchronously simulate audio input to the Windows Desktop Speech Technology recognition engine with an array of <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> objects and specified case sensitivity.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.RecognitionResult" /> containing the results of speech recognition. The instance is null if recognition fails, or if the recognizer is not enabled.</returns>
      <param name="wordUnits">Array of <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> objects used as an input phrase in place of audio input during recognition emulation.</param>
      <param name="compareOptions">A member of the <see cref="T:System.Globalization.CompareOptions" /> enumeration determining whether the emulation is case sensitive or case insensitive.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.EmulateRecognize(System.String)">
      <summary>Synchronously simulate audio input to the Windows Desktop Speech Technology recognition engine with a <see cref="T:System.String" />.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.RecognitionResult" /> containing the results of speech recognition. The instance is null if recognition fails, or if the recognizer is not enabled.</returns>
      <param name="inputText">A <see cref="T:System.String" /> containing text used as an input phrase in place of audio input during recognition emulation.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.EmulateRecognize(System.String,System.Globalization.CompareOptions)">
      <summary>Synchronously simulate audio input to the Windows Desktop Speech Technology recognition engine with a <see cref="string" /> and specified case sensitivity.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.RecognitionResult" /> containing the results of speech recognition. The instance is null if recognition fails, or if the recognizer is not enabled.</returns>
      <param name="inputText">A <see cref="string" /> containing text used as an input phrase in place of audio input during recognition emulation.</param>
      <param name="compareOptions">A member of the <see cref="T:System.Globalization.CompareOptions" /> enumeration determining whether the emulation is case sensitive or case insensitive.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.EmulateRecognizeAsync(System.Speech.Recognition.RecognizedWordUnit[],System.Globalization.CompareOptions)">
      <summary>Asynchronously simulate audio input to the Windows Desktop Speech Technology recognition engine with an array of <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> objects and specified case sensitivity.</summary>
      <param name="wordUnits">Array of <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> objects used as an input phrase in place of audio input during recognition emulation.</param>
      <param name="compareOptions">A member of the <see cref="T:System.Globalization.CompareOptions" /> enumeration determining whether the emulation is case sensitive or case insensitive.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.EmulateRecognizeAsync(System.String)">
      <summary>Asynchronously simulate audio input to the Windows Desktop Speech Technology recognition engine with <see cref="string" />.</summary>
      <param name="inputText">A <see cref="string" /> containing text used as an input phrase in place of audio input during recognition emulation.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.EmulateRecognizeAsync(System.String,System.Globalization.CompareOptions)">
      <summary>Asynchronously simulate audio input to the Windows Desktop Speech Technology recognition engine with a <see cref="string" /> and specified case sensitivity.</summary>
      <param name="inputText">A <see cref="string" /> containing text used as an input phrase in place of audio input during recognition emulation.</param>
      <param name="compareOptions">A member of the <see cref="T:System.Globalization.CompareOptions" /> enumeration determining whether the emulation is case sensitive or case insensitive.</param>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.EmulateRecognizeCompleted">
      <summary>Event generated when an asynchronous emulation of speech recognition completes.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.Enabled">
      <summary>Gets or sets a value controlling whether a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> is enabled an running<see cref="." /></summary>
      <returns>Property is a bool value which is set to true if an instance of <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> is enabled and ready to process input, and false it the instance is not available for input processing.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.Grammars">
      <summary>Read-only property returning a list of all <see cref="T:System.Speech.Recognition.Grammar" /> objects loaded into the current instance of the <see cref="T:System.Speech.Recognition.SpeechRecognizer" /></summary>
      <returns>Returns an list of the <see cref="T:System.Speech.Recognition.Grammar" /> objects loaded into the current instance of the <see cref="T:System.Speech.Recognition.SpeechRecognizer" />.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.LoadGrammar(System.Speech.Recognition.Grammar)">
      <summary>Synchronously loads a specific grammar, as specified by a <see cref="T:System.Speech.Recognition.Grammar" />, for use by a <see cref="T:System.Speech.Recognition.SpeechRecognizer" />.</summary>
      <param name="grammar">An instance of <see cref="T:System.Speech.Recognition.Grammar" /> specify the grammar to add to and instance of <see cref="T:System.Speech.Recognition.SpeechRecognizer" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.LoadGrammarAsync(System.Speech.Recognition.Grammar)">
      <summary>Asynchronously loads a specific grammar, as specified by a <see cref="T:System.Speech.Recognition.Grammar" />, for use by a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /></summary>
      <param name="grammar">An instance of <see cref="T:System.Speech.Recognition.Grammar" /> specify the grammar to add to and instance of <see cref="T:System.Speech.Recognition.SpeechRecognizer" /></param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.LoadGrammarCompleted">
      <summary>Event generated when an asynchronous load of a <see cref="T:System.Speech.Recognition.Grammar" /> object in to a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> completes.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.MaxAlternates">
      <summary>Gets and sets the maximum number of candidate results an instance of <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> is to return.</summary>
      <returns>Returns an int specifying the maximum number of candidate phrases will be returned by a recognition engine.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.PauseRecognizerOnRecognition">
      <summary>Gets and sets a value indicating whether a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> based recognition engine pause while an application is handling <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechRecognized" /> events.</summary>
      <returns>Property is a bool value which is set to true if an instance of <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> is will pause while handlers subscribed to <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechRecognized" /> are executing, and false if the <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> will continue to execute while the handler is executing.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.RecognizerAudioPosition">
      <summary>Read-only property returning the position in a recognition engine's audio stream of the audio input it processed. </summary>
      <returns>Returns a System.TimeSpan object containg the current postion of the Speech Recognizer.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.RecognizerInfo">
      <summary>Read-only property providing identifical and configuration information about the Windows Desktop Speech Technology recognition engine in use.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.RecognizerInfo" /> containing configuration information about the Windows Desktop Speech Technology recognition engine currently in use.</returns>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.RecognizerUpdateReached">
      <summary>Event generated when the Windows Desktop Speech Technology recognition engine pauses to allow an atomic updated requested by <see cref="M:System.Speech.Recognition.SpeechRecognizer.RequestRecognizerUpdate" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.RequestRecognizerUpdate">
      <summary>Requests that the Windows Desktop Speech Technology recognition engine pause and update its state.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.RequestRecognizerUpdate(System.Object)">
      <param name="userToken">An object containing information to be used by <see cref="E:System.Speech.Recognition.SpeechRecognizer.RecognizerUpdateReached" /> event handlers. This value can be null.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.RequestRecognizerUpdate(System.Object,System.TimeSpan)">
      <param name="userToken">An object containing information to be used by <see cref="E:System.Speech.Recognition.SpeechRecognizer.RecognizerUpdateReached" /> event handlers. This value can be null.</param>
      <param name="audioPositionAheadToRaiseUpdate">The offset from the current audio position after which the requested operation can start.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.SpeechDetected">
      <summary>Event raised by recognition engine when speech is detected.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.SpeechHypothesized">
      <summary>Event raised when when the recognition engine detects speech and part of the audio input speech has been tentatively recognized.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.SpeechRecognitionRejected">
      <summary>The event raised when the recognition engine detects speech, but can only return candidate phrases with low confidence levels.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.SpeechRecognized">
      <summary>The event raised when the recognition engine detects speech, and has found one or more phrases with sufficient confidence levels.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.State">
      <summary>Read-only property providing configuration information about the current running state of theWindows Desktop Speech Technology recognition engine in use.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.RecognizerState" /> containing configuration information about the running state of the Windows Desktop Speech Technology recognition engine currently in use.</returns>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.StateChanged">
      <summary>Event generated when the Windows Desktop Speech Technology recognition engine running state changes.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.UnloadAllGrammars">
      <summary>Unloads all <see cref="T:System.Speech.Recognition.Grammar" /> objects currently loaded in by a Windows Desktop Speech Technology recognition engine.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.UnloadGrammar(System.Speech.Recognition.Grammar)">
      <summary>Unloads a grammar, as specified by an instance of <see cref="T:System.Speech.Recognition.Grammar" />, from an instance of <see cref="T:System.Speech.Recognition.SpeechRecognizer" />.</summary>
      <param name="grammar">An instance of <see cref="T:System.Speech.Recognition.Grammar" /> specify the grammar to remove from an instance of <see cref="T:System.Speech.Recognition.SpeechRecognizer" />.</param>
    </member>
    <member name="T:System.Speech.Recognition.SpeechUI">
      <summary>Provides text and status information to the operating system in speech user interface to display to the user.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechUI.SendTextFeedback(System.Speech.Recognition.RecognitionResult,System.String,System.Boolean)">
      <summary>Sends status and descriptive text to the Speech platform user interface about the status of a recognition operation.</summary>
      <returns>Returns a bool which is true if the information provided to the method (<paramref name="Feedback" />, and <paramref name="isSuccessfulAction" />) by was successfully made available to the Speech platform user interface, and false if the operation failed.</returns>
      <param name="result">A valid <see cref="T:System.Speech.Recognition.RecognitionResult" /> instance.</param>
      <param name="feedback">A <see cref="System.String" /> containing a comment of the recognition operation which produced the <see cref="T:System.Speech.Recognition.RecognitionResult" /><paramref name="result" />.</param>
      <param name="isSuccessfulAction">A bool indicating if the application deemed the recognition operation a success.</param>
    </member>
    <member name="T:System.Speech.Recognition.StateChangedEventArgs">
      <summary>Returns data from the StateChanged event.</summary>
    </member>
    <member name="P:System.Speech.Recognition.StateChangedEventArgs.RecognizerState">
      <summary>Gets the current state of the recognition engine.</summary>
      <returns>Returns a <see cref="T:System.Speech.Recognition.RecognizerState" /> object containing full information about a recognition engine.</returns>
    </member>
    <member name="T:System.Speech.Recognition.SubsetMatchingMode">
      <summary>Enumerates values of subset matching mode.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SubsetMatchingMode.Subsequence">
      <summary>Indicates that subset matching mode is Subsequence.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SubsetMatchingMode.OrderedSubset">
      <summary>Indicates that subset matching mode is OrderedSubset.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SubsetMatchingMode.SubsequenceContentRequired">
      <summary>Indicates that subset matching mode is SubsequenceContentRequired.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SubsetMatchingMode.OrderedSubsetContentRequired">
      <summary>Indicates that subset matching mode is OrderedSubsetContentRequired.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument">
      <summary>Defines a design-time object that is used to build strongly typed runtime SRGS grammars. </summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsDocument.#ctor">
      <summary>Initializes a new instance of the <see cref="SrgsDocument" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsDocument.#ctor(System.Speech.Recognition.GrammarBuilder)">
      <summary>Initializes a new instance of the <see cref="SrgsDocument" /> class specifying a set of rules defined by the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</summary>
      <param name="builder">The <see cref="GrammarBuilder" /> object used to create the <see cref="SrgsDocument" /> instance.</param>
      <exception cref="ArgumentNullException">
        <paramref name="builder" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsDocument.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsRule)">
      <summary>Initializes a new instance of the <see cref="SrgsDocument" /> class specifying the root rule of <see cref="SrgsDocument" />.</summary>
      <param name="grammarRootRule">The root rule in the <see cref="SrgsDocument" /> object.</param>
      <exception cref="ArgumentNullException">
        <paramref name="grammarRootRule" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsDocument.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="SrgsDocument" /> class specifying the location of the XML document that is used to fill in the <see cref="SrgsDocument" /> instance.</summary>
      <param name="path">The location of the SRGS XML file.</param>
      <exception cref="ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="ArgumentException">
        <paramref name="path" /> is an empty string.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsDocument.#ctor(System.Xml.XmlReader)">
      <summary>Initializes a new instance of the <see cref="SrgsDocument" /> class specifying the grammar to use when creating the document.</summary>
      <param name="srgsGrammar">The System.Xml.XmlReader that was created with the <see cref="SrgsDocument" /> XML instance.</param>
      <exception cref="ArgumentNullException">
        <paramref name="srgsGrammar" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.AssemblyReferences">
      <summary>Gets the assembly reference information for the <see cref="SrgsDocument" /> instance.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.CodeBehind">
      <summary>Gets the code behind information for the <see cref="SrgsDocument" /> instance.</summary>
      <returns>A string collection containing a list of the code behind documents.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Culture">
      <summary>Gets or sets the culture information for the <see cref="SrgsDocument" /> instance.</summary>
      <returns>A <see cref="System.Globalization.CultureInfo" /> object that contains the current culture information for <see cref="SrgsDocument" />.</returns>
      <exception cref="ArgumentNullException">The value being assigned to <see cref="Culture" /> is null.</exception>
      <exception cref="ArgumentException">The value being assigned to <see cref="Culture" /> is System.Globalization.CultureInfo.InvariantCulture.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Debug">
      <summary>Gets or sets whether line numbers should be added to inline scripts.</summary>
      <returns>True if line numbers should be added for debugging purposes; False, otherwise.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.ImportNamespaces">
      <summary>Gets the related namespaces for the current <see cref="SrgsDocument" /> instance.</summary>
      <returns>A string collection containing a list of the related namespaces in the <see cref="SrgsDocument" /> instance.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Language">
      <summary>Gets or sets the programming language used for inline code in the <see cref="SrgsDocument" /> class.</summary>
      <returns>The language to which <see cref="SrgsDocument" /> is currently set.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Mode">
      <summary>Gets or sets the mode for the <see cref="SrgsDocument" /> class.</summary>
      <returns>An <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsGrammarMode" /> value that describes the current mode of <see cref="SrgsDocument" />.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Namespace">
      <summary>Gets or sets the namespace of the <see cref="SrgsDocument" /> class.</summary>
      <returns>The namespace for the current <see cref="SrgsDocument" />.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.PhoneticAlphabet"></member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Root">
      <summary>Gets or sets the root rule of the <see cref="SrgsDocument" /> class.</summary>
      <returns>The <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> that is designated to be the root rule of <see cref="SrgsDocument" />.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Rules">
      <summary>Gets the collection of rules that are currently defined for the <see cref="SrgsDocument" /> class.</summary>
      <returns>An <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRulesCollection" /> object that contains the rules defined for <see cref="SrgsDocument" />.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Script">
      <summary>Gets or sets the .NET scripting language for the <see cref="SrgsDocument" /> class.</summary>
      <returns>The current .NET scripting language for <see cref="SrgsDocument" />.</returns>
      <exception cref="ArgumentNullException">An attempt is made to set <see cref="Script" /> to null.</exception>
      <exception cref="ArgumentException">An attempt is made to set <see cref="Script" /> to an empty string.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsDocument.WriteSrgs(System.Xml.XmlWriter)">
      <summary>Writes an SRGS XML document.</summary>
      <param name="srgsGrammar">The System.Xml.XmlWriter that is used to write the <see cref="SrgsDocument" /> instance.</param>
      <exception cref="ArgumentNullException">
        <paramref name="srgsGrammar" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.XmlBase">
      <summary>Gets or sets the base URI of the <see cref="SrgsDocument" /> class.</summary>
      <returns>The current base URI of <see cref="SrgsDocument" />.</returns>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsElement">
      <summary>Defines the base class for elements in a grammar.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsElement.#ctor">
      <summary>Initializes a new instance of the <see cref="SrgsElement" /> class.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsGrammarCompiler">
      <summary>Compiles an XML-format SRGS document into a binary CFG file.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsGrammarCompiler.Compile(System.Speech.Recognition.SrgsGrammar.SrgsDocument,System.IO.Stream)">
      <summary>Compiles an SRGS document to a file.</summary>
      <param name="srgsGrammar">The grammar to compile.</param>
      <param name="outputStream">The stream that receives the results of  compilation.</param>
      <exception cref="ArgumentNullException">
        <paramref name="srgsGrammar" /> is null.<paramref name="outputStream" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsGrammarCompiler.Compile(System.String,System.IO.Stream)">
      <summary>Compiles a grammar to a file.</summary>
      <param name="inputPath">The path of the file to compile.</param>
      <param name="outputStream">The stream that receives the results of compilation.</param>
      <exception cref="ArgumentNullException">
        <paramref name="inputPath" /> is null.<paramref name="outputStream" /> is null.</exception>
      <exception cref="ArgumentException">
        <paramref name="inputPath" /> is an empty string.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsGrammarCompiler.Compile(System.Xml.XmlReader,System.IO.Stream)">
      <summary>Compiles a grammar to a file.</summary>
      <param name="reader">The <see cref="XmlReader" /> that reads the grammar. The grammar can reside in a physical file or in memory.</param>
      <param name="outputStream">The stream that will receive the results of  compilation.</param>
      <exception cref="ArgumentNullException">
        <paramref name="reader" /> is null.<paramref name="outputStream" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsGrammarCompiler.CompileClassLibrary(System.Speech.Recognition.SrgsGrammar.SrgsDocument,System.String,System.String[],System.String)">
      <summary>Compiles a SRGS document into a DLL.</summary>
      <param name="srgsGrammar">The <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> that contains the grammars to compile.</param>
      <param name="outputPath">The path of the output DLL.</param>
      <param name="referencedAssemblies">A list of the assemblies referenced from the input grammars.</param>
      <param name="keyFile">The name of the file that contains a pair of keys, thereby enabling the output DLL to be signed.</param>
      <exception cref="ArgumentNullException">
        <paramref name="srgsGrammar" /> is null.<paramref name="outputPath" /> is null.</exception>
      <exception cref="ArgumentException">
        <paramref name="outputPath" /> is an empty string.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsGrammarCompiler.CompileClassLibrary(System.String[],System.String,System.String[],System.String)">
      <summary>Compiles a set of grammars to a DLL.</summary>
      <param name="inputPaths">A list of the grammars to compile.</param>
      <param name="outputPath">The path of the output DLL.</param>
      <param name="referencedAssemblies">A list of the assemblies referenced from the input grammars.</param>
      <param name="keyFile">The name of the file that contains a pair of keys, thereby enabling the output DLL to be signed.</param>
      <exception cref="ArgumentNullException">
        <paramref name="inputPaths" /> is null.<paramref name="outputPath" /> is null.</exception>
      <exception cref="ArgumentException">
        <paramref name="outputPath" /> is an empty string.Any element of the <paramref name="inputPaths" /> array is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsGrammarCompiler.CompileClassLibrary(System.Xml.XmlReader,System.String,System.String[],System.String)">
      <summary>Compiles a grammar to a file.</summary>
      <param name="reader">The <see cref="XmlReader" /> that reads the grammars. The grammars can reside in a physical file or in memory.</param>
      <param name="outputPath">The path of the output DLL.</param>
      <param name="referencedAssemblies">A list of the assemblies referenced from the input grammars.</param>
      <param name="keyFile">The name of the file that contains a pair of keys, thereby enabling the output DLL to be signed.</param>
      <exception cref="ArgumentNullException">
        <paramref name="reader" /> is null.<paramref name="outputPath" /> is null.</exception>
      <exception cref="ArgumentException">
        <paramref name="outputPath" /> is an empty string.</exception>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsGrammarMode">
      <summary>Indicates the type of input that the <see cref="Grammar" /> object can expect. </summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsGrammarMode.Voice">
      <summary>The <see cref="Grammar" /> object should expect spoken audio data.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsGrammarMode.Dtmf">
      <summary>The <see cref="Grammar" /> object should expect DTMF tones similar to those found on a telephone.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsItem">
      <summary>Represents the grammar element that contains phrases or other entities that a user can speak to produce a successful recognition.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.#ctor">
      <summary>Initializes a new instance of the <see cref="SrgsItem" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="SrgsItem" /> class, specifying the number of times that the item can be spoken.</summary>
      <param name="repeatCount">The number of times that the item can be spoken.</param>
      <exception cref="ArgumentOutOfRangeException">
        <paramref name="repeatCount" /> is negative or is larger than 255.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.#ctor(System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="SrgsItem" /> class, specifying minimum and maximum repetition counts.</summary>
      <param name="min">The minimum number of times that the text in the item can be repeated.</param>
      <param name="max">The maximum number of times that the text in the item can be repeated.</param>
      <exception cref="ArgumentOutOfRangeException">
        <paramref name="min" /> is negative or larger than 255.<paramref name="max" /> is negative or larger than 255.</exception>
      <exception cref="ArgumentException">
        <paramref name="min" /> is larger than <paramref name="max" />.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.#ctor(System.Int32,System.Int32,System.Speech.Recognition.SrgsGrammar.SrgsElement[])">
      <summary>Initializes a new instance of the <see cref="SrgsItem" /> class, specifying minimum and maximum repetition counts and specifying an array of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsElement" /> objects to be added to this instance.</summary>
      <param name="min">The minimum number of times that any of the items in the <see cref="SrgsItem" /> object can be repeated.</param>
      <param name="max">The maximum number of times that any of the items in the <see cref="SrgsItem" /> object can be repeated.</param>
      <param name="elements">The array of <see cref="SrgsElement" /> objects to be added to the <see cref="SrgsItem" /> instance.</param>
      <exception cref="ArgumentNullException">
        <paramref name="elements" /> is null.</exception>
      <exception cref="ArgumentException">Any member of the <paramref name="elements" /> array is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.#ctor(System.Int32,System.Int32,System.String)">
      <summary>Initializes a new instance of the <see cref="SrgsItem" /> class, specifying minimum and maximum repetition counts for the item and specifying the text associated with the item.</summary>
      <param name="min">The minimum number of times that the item can be repeated.</param>
      <param name="max">The maximum number of times that the item can be repeated.</param>
      <param name="text">The text associated with the item.</param>
      <exception cref="ArgumentOutOfRangeException">
        <paramref name="min" /> is negative or larger than 255.<paramref name="max" /> is negative or larger than 255.</exception>
      <exception cref="ArgumentException">
        <paramref name="min" /> is larger than <paramref name="max" />.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsElement[])">
      <summary>Initializes a new instance of the <see cref="SrgsItem" /> class, specifying an array of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsElement" /> objects to be added to this instance.</summary>
      <param name="elements">The array of <see cref="SrgsElement" /> objects to be added to the <see cref="SrgsItem" /> instance.</param>
      <exception cref="ArgumentNullException">
        <paramref name="elements" /> is null.</exception>
      <exception cref="ArgumentException">Any member of the <paramref name="elements" /> array is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="SrgsItem" /> class, specifying the text of the item.</summary>
      <param name="text">The text associated with the item.</param>
      <exception cref="ArgumentNullException">
        <paramref name="text" /> is null.</exception>
      <exception cref="ArgumentException">
        <paramref name="text" /> is an empty string.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.Add(System.Speech.Recognition.SrgsGrammar.SrgsElement)">
      <summary>Adds the item to the list of items belonging to this <see cref="SrgsItem" /> instance.</summary>
      <param name="element">The item to add to the list of items that belong to the <see cref="SrgsItem" /> instance.</param>
      <exception cref="ArgumentNullException">
        <paramref name="element" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsItem.Elements">
      <summary>Gets the collection of objects that make up the <see cref="SrgsItem" /> instance.</summary>
      <returns>The collection of objects that make up the <see cref="SrgsItem" /> instance.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsItem.MaxRepeat">
      <summary>Gets the maximum number of times that a user can speak the item.</summary>
      <returns>The maximum number of times that a user can speak the item.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsItem.MinRepeat">
      <summary>Gets the minimum number of times that a user can speak the item.</summary>
      <returns>The minimum number of times that a user can speak the item.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsItem.RepeatProbability">
      <summary>Gets or sets the probability that a user will repeat the item in this <see cref="SrgsItem" /> instance.</summary>
      <returns>The probability, as a floating point value, that this item will be repeatedly spoken.</returns>
      <exception cref="ArgumentOutOfRangeException">An attempt is made to set <see cref="RepeatProbability" /> to a value that is negative or larger than 1.0.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.SetRepeat(System.Int32)">
      <summary>Sets the number of times that an item can be spoken.</summary>
      <param name="count">The number of times that the item can be spoken.</param>
      <exception cref="ArgumentOutOfRangeException">
        <paramref name="count" /> is less than 0 or greater than 255.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.SetRepeat(System.Int32,System.Int32)">
      <summary>Sets the minimum number of times and the maximum number of times that an item can be spoken.</summary>
      <param name="minRepeat">The minimum number of times that the item can be spoken.</param>
      <param name="maxRepeat">The maximum number of times that the item can be spoken.</param>
      <exception cref="ArgumentOutOfRangeException">
        <paramref name="minRepeat" /> is less than zero or larger than 255.<paramref name="maxRepeat" /> is less than zero or larger than 255.</exception>
      <exception cref="ArgumentException">
        <paramref name="minRepeat" /> is larger than <paramref name="maxRepeat" />.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsItem.Weight">
      <summary>Gets or sets a multiplying factor that adjusts the likelihood that an item within a list will be spoken.</summary>
      <returns>The weight, as a floating point value, that adjusts the likelihood of this item being spoken.</returns>
      <exception cref="ArgumentOutOfRangeException">An attempt is made to set <see cref="Weight" /> to a negative value.</exception>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag">
      <summary>Represents the World Wide Web Consortium (W3C) Speech Recognition Grammar Specification (SRGS) <see cref="tag" /> element with Speech API (SAPI) properties.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.#ctor">
      <summary>Initializes a new instance of the <see cref="SrgsNameValueTag" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.#ctor(System.Object)">
      <summary>Initializes a new instance of the <see cref="SrgsNameValueTag" /> class, specifying a value for the instance.</summary>
      <param name="value">The value used to set the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.Value" /> property.</param>
      <exception cref="ArgumentNullException">
        <paramref name="value" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="SrgsNameValueTag" /> class, specifying a name and a value for the instance.</summary>
      <param name="name">The string used to set the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.Name" /> property on the <see cref="SrgsNameValueTag" /> object.</param>
      <param name="value">The object used to set the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.Value" /> property on the <see cref="SrgsNameValueTag" /> object.</param>
      <exception cref="ArgumentNullException">
        <paramref name="value" /> is null.<paramref name="name" /> is null.</exception>
      <exception cref="ArgumentException">
        <paramref name="name" /> is an empty string.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.Name">
      <summary>Gets or sets the name of the <see cref="SrgsNameValueTag" /> instance.</summary>
      <returns>A string that contains the name of the <see cref="SrgsNameValueTag" /> instance.</returns>
      <exception cref="ArgumentNullException">An attempt is made to set <see cref="Name" /> to null.</exception>
      <exception cref="ArgumentException">An attempt is made to set <see cref="Name" /> to an empty string.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.Value">
      <summary>Gets or sets the value contained in the <see cref="SrgsNameValueTag" /> instance.</summary>
      <returns>The value contained in the <see cref="SrgsNameValueTag" /> instance.</returns>
      <exception cref="ArgumentNullException">An attempt is made to set <see cref="Value" /> to null.</exception>
      <exception cref="ArgumentException">An attempt is made to set <see cref="Value" /> to an invalid type. The type used to set <see cref="Value" /> must be a String, a Boolean, an Int32, or a Double.</exception>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsOneOf">
      <summary>Represents a list of items from which a user can speak only one.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsOneOf.#ctor">
      <summary>Initializes a new instance of the <see cref="SrgsOneOf" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsOneOf.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsItem[])">
      <summary>Initializes a new instance of the <see cref="SrgsOneOf" /> class, specifying one or more alternative items in the passed <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> array.</summary>
      <param name="items">An array of type <see cref="SrgsItem" />. Each member of the array represents the contents of a single <see cref="SrgsItem" /> element.</param>
      <exception cref="ArgumentNullException">
        <paramref name="items" /> is null.Any element in the <paramref name="items" /> array is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsOneOf.#ctor(System.String[])">
      <summary>Initializes a new instance of the <see cref="SrgsOneOf" /> class, specifying one or more items in the passed <see cref="string" /> array.</summary>
      <param name="items">An array of type <see cref="string" />. Each member of the array represents the contents of a single <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> element.</param>
      <exception cref="ArgumentNullException">
        <paramref name="items" /> is null.Any element in the <paramref name="items" /> array is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsOneOf.Add(System.Speech.Recognition.SrgsGrammar.SrgsItem)">
      <summary>Adds a set of alternative phrases that can possibly be matched by a user. </summary>
      <param name="item">Any valid rule expansion. A valid rule expansion can consist of a word or other entity that can be spoken, an <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> element, an <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsTag" /> element, or any logical combination of these.</param>
      <exception cref="ArgumentNullException">
        <paramref name="item" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsOneOf.Items">
      <summary>Gets the list of all elements contained in the <see cref="SrgsOneOf" /> element.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsPhoneticAlphabet">
      <summary>Enumerates the supported phonetic alphabets.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsPhoneticAlphabet.Sapi">
      <summary>Speech API phoneme set.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsPhoneticAlphabet.Ipa">
      <summary>International Phonetic Alphabet phoneme set.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsPhoneticAlphabet.Ups">
      <summary>Universal Phone Set phoneme set, which is ASCII encoding of phonemes for IPA.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsRule">
      <summary>Represents a grammar rule. </summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRule.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="SrgsRule" /> class with the given identifier.</summary>
      <param name="id">A string that is the identifier of the rule. The identifier must be unique within a given grammar.</param>
      <exception cref="ArgumentNullException">
        <paramref name="id" /> is null.</exception>
      <exception cref="ArgumentOutOfRangeException">
        <paramref name="id" /> is empty.</exception>
      <exception cref="FormatException">
        <paramref name="id" /> is not a proper rule identifier.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRule.#ctor(System.String,System.Speech.Recognition.SrgsGrammar.SrgsElement[])">
      <summary>Initializes a new instance of the <see cref="SrgsRule" /> class, creating a rule in an SRGS grammar that is populated with the elements of the passed array.</summary>
      <param name="id">A <see cref="string" /> object that is the identifier of the rule. The identifier must be unique within a given grammar.</param>
      <param name="elements">An array of <see cref="SrgsElement" /> elements.</param>
      <exception cref="ArgumentNullException">
        <paramref name="id" /> is null.<paramref name="elements" /> is null.</exception>
      <exception cref="ArgumentOutOfRangeException">
        <paramref name="id" /> is empty.</exception>
      <exception cref="FormatException">
        <paramref name="id" /> is not a proper rule identifier.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRule.Add(System.Speech.Recognition.SrgsGrammar.SrgsElement)">
      <summary>Adds an SRGS element to a rule.</summary>
      <param name="element">Text or other XML elements that specify what speakers can say, and the order in which they can say it. A valid rule element must contain at least one piece of recognizable text or one rule reference.</param>
      <exception cref="ArgumentNullException">
        <paramref name="element" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.BaseClass">
      <summary>Gets or sets the base class of the rule.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.Elements">
      <summary>Gets the collection of elements in the rule.</summary>
      <returns>The collection of elements in the rule.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.Id">
      <summary>Gets or sets the identifier for the rule.</summary>
      <returns>The identifier for the rule.</returns>
      <exception cref="FormatException">An attempt is made to set <see cref="Id" /> to an invalid value.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnError"></member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnInit"></member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnParse"></member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnRecognition"></member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.Scope">
      <summary>Gets or sets whether a rule can be activated for recognition and when the rule can be referenced by other rules.</summary>
      <returns>An <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleScope" /> value.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.Script"></member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef">
      <summary>Represents the grammar element that specifies the rule to reference.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsRule)">
      <summary>Initializes a new instance of the <see cref="SrgsRuleRef" /> class, specifying the rule to reference.</summary>
      <param name="rule">The identifier of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> element to reference.</param>
      <exception cref="ArgumentNullException">
        <paramref name="rule" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsRule,System.String)">
      <param name="rule"></param>
      <param name="semanticKey"></param>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsRule,System.String,System.String)">
      <param name="rule"></param>
      <param name="semanticKey"></param>
      <param name="parameters"></param>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.#ctor(System.Uri)">
      <summary>Initializes a new instance of the <see cref="SrgsRuleRef" /> class, specifying the Uniform Resource Identifier (URI) of the grammar to reference.</summary>
      <param name="uri">The URI of the rule to reference.</param>
      <exception cref="ArgumentNullException">
        <paramref name="uri" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.#ctor(System.Uri,System.String)">
      <summary>Initializes a new instance of the <see cref="SrgsRuleRef" /> class, specifying the Uniform Resource Identifier (URI) of the grammar to reference and the identifier of the rule.</summary>
      <param name="uri">The URI of the grammar to reference.</param>
      <param name="rule">The identifier of the rule.</param>
      <exception cref="ArgumentNullException">
        <paramref name="uri" /> is null.<paramref name="rule" /> is null.</exception>
      <exception cref="ArgumentOutOfRangeException">
        <paramref name="rule" /> is empty.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.#ctor(System.Uri,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="SrgsRuleRef" /> class, specifying the URI of the rule, the identifier of the rule, and the string alias of the semantic dictionary.</summary>
      <param name="uri">The URI of the rule to reference.</param>
      <param name="rule">The identifier of the rule to reference.</param>
      <param name="semanticKey">An alias string for the semantic dictionary.</param>
      <exception cref="ArgumentNullException">
        <paramref name="uri" /> is null.<paramref name="semanticKey" /> is null.</exception>
      <exception cref="ArgumentOutOfRangeException">
        <paramref name="semanticKey" /> is empty.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.#ctor(System.Uri,System.String,System.String,System.String)">
      <param name="uri"></param>
      <param name="rule"></param>
      <param name="semanticKey"></param>
      <param name="parameters"></param>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.Dictation">
      <summary>Defines a rule that can match spoken input as defined by the dictation topic associated with this grammar. </summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.Garbage">
      <summary>Defines a rule that can match any speech up to the next rule match, the next token, or until the end of spoken input.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.MnemonicSpelling"></member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.Null">
      <summary>Defines a rule that is automatically matched; that is, a rule that is matched without the user speaking anything.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.Params">
      <summary>Gets the initialization parameters for a <see cref="SrgsRuleRef" /> element.</summary>
      <returns>The initialization parameters for a <see cref="SrgsRuleRef" /> element.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.SemanticKey">
      <summary>Gets an alias string for the semantic dictionary.</summary>
      <returns>An alias string for the semantic dictionary.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.Uri">
      <summary>Gets the URI for the rule that this <see cref="SrgsRuleRef" /> element references.</summary>
      <returns>The <see cref="System.URI" /> for the rule that this <see cref="SrgsRuleRef" /> element references.</returns>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.Void">
      <summary>Defines a rule that can never be spoken. Inserting VOID into a sequence automatically makes that sequence unspeakable.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsRulesCollection">
      <summary>Represents a collection of objects that can be child elements of a <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> instance.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRulesCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="SrgsRuleCollection" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRulesCollection.Add(System.Speech.Recognition.SrgsGrammar.SrgsRule[])">
      <summary>Adds the contents of an array of a <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> objects to the <see cref="SrgsRuleCollection" /> object.</summary>
      <param name="rules">An array of type <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" />. Each rule in the array is added to the <see cref="SrgsRuleCollection" /> object.</param>
      <exception cref="ArgumentNullException">
        <paramref name="rules" /> is null.</exception>
      <exception cref="ArgumentException">Any <see cref="SrgsRule" /> element in the <paramref name="rules" /> array is null.</exception>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleScope">
      <summary>Enumerates values for the scope of a rule in an SRGS grammar. </summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsRuleScope.Public">
      <summary>The rule can be referenced by an external grammar (in a <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> element in the grammar making the reference). A public rule can always be activated for recognition. </summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsRuleScope.Private">
      <summary>The rule cannot ordinarily be referenced by an external grammar (in an <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> element in the grammar making the reference). The only exception is that a private rule that is the root rule of its grammar can be referenced by an external grammar.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsSemanticInterpretationTag">
      <summary>Represents a tag that contains ECMAScript that is run when the rule is matched.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsSemanticInterpretationTag.#ctor">
      <summary>Creates an instance of the <see cref="SrgsSemanticInterpretationTag" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsSemanticInterpretationTag.#ctor(System.String)">
      <summary>Creates an instance of the <see cref="SrgsSemanticInterpretationTag" /> class, specifying the script contents of the tag.</summary>
      <param name="script">A string that contains the ECMAScript for the tag.</param>
      <exception cref="ArgumentNullException">
        <paramref name="script" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsSemanticInterpretationTag.Script">
      <summary>Gets or sets the ECMAScript for the tag.</summary>
      <returns>A string that contains the semantic interpretation script for the tag.</returns>
      <exception cref="ArgumentNullException">An attempt is made to set <see cref="Script" /> to null.</exception>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsSubset">
      <summary>Defines methods and properties that can be used to match a given string with a spoken phrase. </summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsSubset.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="SrgsSubset" /> class, specifying the portion of the phrase to be matched. </summary>
      <param name="text">The portion of the phrase to be matched.</param>
      <exception cref="ArgumentNullException">
        <paramref name="text" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsSubset.#ctor(System.String,System.Speech.Recognition.SubsetMatchingMode)">
      <summary>Initializes a new instance of the <see cref="SrgsSubset" /> class, specifying the portion to be matched and the mode in which the text should be matched.</summary>
      <param name="text">The portion of the phrase to be matched.</param>
      <param name="matchingMode">The mode in which <paramref name="text" /> should be matched with the spoken phrase.</param>
      <exception cref="ArgumentNullException">
        <paramref name="text" /> is null.</exception>
      <exception cref="ArgumentException">
        <paramref name="text" /> is empty.<paramref name="text" /> contains only white space characters (that is, ' ', '\t', '\n', '\r').<paramref name="matchingMode" /> is set to a value in the <see cref="T:System.Speech.Recognition.SubsetMatchingMode" /> enumeration.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsSubset.MatchingMode">
      <summary>Gets or sets the matching mode for the subset.</summary>
      <returns>A member of the <see cref="T:System.Speech.Recognition.SubsetMatchingMode" /> enumeration.</returns>
      <exception cref="ArgumentException">An attempt is made to set <see cref="MatchingMode" /> to a value that is not a member of the <see cref="T:System.Speech.Recognition.SubsetMatchingMode" /> enumeration.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsSubset.Text">
      <summary>Gets or sets as string that contains the portion of a spoken phrase to be matched.</summary>
      <returns>A string that contains the portion of a spoken phrase to be matched.</returns>
      <exception cref="ArgumentNullException">An attempt is made to set <see cref="Text" /> to null or to an empty string. </exception>
      <exception cref="ArgumentException">An attempt is made to set <see cref="Text" /> using a string that contains only white space characters (' ', '\t', '\n', '\r').</exception>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsText">
      <summary>Represents the <see cref="Text" /> content for the World Wide Web Consortium (W3C) Speech Recognition Grammar Specification (SRGS) elements.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsText.#ctor">
      <summary>Initializes a new instance of the <see cref="SrgsText" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsText.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="SrgsText" /> class, specifying the text of the instance.</summary>
      <param name="text">The value used to set the <see cref="P:System.Speech.GrammarBuilding.SrgsText.Text" /> property on the <see cref="SrgsText" /> instance.</param>
      <exception cref="ArgumentNullException">
        <paramref name="text" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsText.Text">
      <summary>Gets or sets the text contained within the <see cref="SrgsText" /> class instance.</summary>
      <returns>The text contained within the <see cref="SrgsText" /> instance.</returns>
      <exception cref="ArgumentNullException">An attempt is made to set <see cref="Text" /> to null.</exception>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsToken">
      <summary>Represents the World Wide Web Consortium (W3C) Speech Recognition Grammar Specification (SRGS) <see cref="token" /> element.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsToken.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="SrgsToken" /> class, specifying the text of the <see cref="SrgsToken" /> class instance.</summary>
      <param name="text">The text of the new <see cref="SrgsToken" /> class instance.</param>
      <exception cref="ArgumentNullException">
        <paramref name="text" /> is null.</exception>
      <exception cref="ArgumentOutOfRangeException">
        <paramref name="text" /> is empty.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsToken.Display">
      <summary>Gets or sets how recognized text is returned.</summary>
      <returns>A representation of the token as it should be displayed.</returns>
      <exception cref="ArgumentNullException">An attempt is made to set <see cref="Display" /> to null.</exception>
      <exception cref="ArgumentOutOfRangeException">An attempt is made to assign an empty string to <see cref="Display" />.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsToken.Pronunciation">
      <summary>Gets or sets the pronunciation phoneme string for the token.</summary>
      <returns>A string containing the pronunciation phoneme for the token.</returns>
      <exception cref="ArgumentNullException">An attempt is made to set <see cref="Pronunciation" /> to null.</exception>
      <exception cref="ArgumentOutOfRangeException">An attempt is made to assign an empty string to <see cref="Pronunciation" />.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsToken.Text">
      <summary>Gets or sets the text contained within the <see cref="SrgsToken" /> class instance.</summary>
      <returns>The text contained within the <see cref="SrgsToken" /> class instance.</returns>
      <exception cref="ArgumentNullException">An attempt is made to set <see cref="Text" /> to null.</exception>
      <exception cref="ArgumentOutOfRangeException">An attempt is made to assign an empty string to <see cref="Text" />.</exception>
      <exception cref="ArgumentException">An attempt is made to assign a string that contains a quotation mark (") to <see cref="Text" />. </exception>
    </member>
    <member name="T:System.Speech.Synthesis.BookmarkReachedEventArgs">
      <summary>Returns data from the <see cref="E:System.Speech.Synthesis.SpeechSynthesizer. BookmarkReached" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.BookmarkReachedEventArgs.AudioPosition">
      <summary>Gets the time offset, in milliseconds, at which the bookmark was reached. </summary>
      <returns>Returns an instance of System.TimeSpan indicating the location in the audio input stream of a synthesis engine at the current audio position.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.BookmarkReachedEventArgs.Bookmark">
      <summary>Gets the name of the bookmark that was reached.</summary>
      <returns>Returns a string value containing the name of the bookmark for the current the audio input stream of a synthesis engine.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.FilePrompt">
      <summary>Represents a prompt spoken from a file.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.FilePrompt.#ctor(System.String,System.Speech.Synthesis.SynthesisMediaType)">
      <summary>Creates a new instance of the FilePrompt class.</summary>
      <param name="path">The path of the file containing the prompt content.</param>
      <param name="media">A <see cref="T:System.Speech.Synthesis.SynthesisMediaType" /> member that specifies the media type of the file.</param>
    </member>
    <member name="M:System.Speech.Synthesis.FilePrompt.#ctor(System.Uri,System.Speech.Synthesis.SynthesisMediaType)">
      <summary>Creates a new instance of the FilePrompt class.</summary>
      <param name="promptFile">The URI of the file containing the prompt content.</param>
      <param name="media">A <see cref="T:System.Speech.Synthesis.SynthesisMediaType" /> member that specifies the media type of the file.</param>
    </member>
    <member name="T:System.Speech.Synthesis.InstalledVoice">
      <summary>Represents an installed Voice object.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.InstalledVoice.Enabled">
      <summary>Gets or sets whether the Voice is enabled.</summary>
      <returns>A bool that represents the Enabled state of the Voice.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.InstalledVoice.Equals(System.Object)">
      <param name="obj"></param>
    </member>
    <member name="M:System.Speech.Synthesis.InstalledVoice.GetHashCode">
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.InstalledVoice.VoiceInfo">
      <summary>Gets a VoiceInfo object that contains information about the Voice. </summary>
      <returns>A VoiceInfo object that represents the Voice.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.PhonemeReachedEventArgs">
      <summary>Returns data from the SpeechSynthesizer.PhonemeReached event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.PhonemeReachedEventArgs.AudioPosition">
      <summary>Gets the audio position of the phoneme.</summary>
      <returns>A TimeSpan object indicating the audio position.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.PhonemeReachedEventArgs.Duration">
      <summary>Gets the duration of the phoneme.</summary>
      <returns>A TimeSpan object indicating the duration.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.PhonemeReachedEventArgs.Emphasis">
      <summary>Gets the emphasis of the phoneme.</summary>
      <returns>A SynthesizerEmphasis member indicating the level of emphasis.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.PhonemeReachedEventArgs.NextPhoneme">
      <summary>Gets the phoneme following the phoneme associated with the PhonemeReached event.</summary>
      <returns>A string containing the next phoneme.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.PhonemeReachedEventArgs.Phoneme">
      <summary>The phoneme associated with the PhonemeReached event.</summary>
      <returns>A string containing the phoneme.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.Prompt">
      <summary>Plays a prompt from text or from a PromptBuilder.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.Prompt.#ctor(System.Speech.Synthesis.PromptBuilder)">
      <summary>Creates a new instance of the Prompt class.</summary>
      <param name="promptBuilder">The <see cref="T:System.Speech.Synthesis.PromptBuilder" /> to be spoken.</param>
    </member>
    <member name="M:System.Speech.Synthesis.Prompt.#ctor(System.String)">
      <summary>Creates a new instance of the Prompt class.</summary>
      <param name="textToSpeak">The text to be spoken.</param>
    </member>
    <member name="M:System.Speech.Synthesis.Prompt.#ctor(System.String,System.Speech.Synthesis.SynthesisTextFormat)">
      <summary>Creates a new instance of the Prompt class.</summary>
      <param name="textToSpeak">The text to be spoken.</param>
      <param name="media">A <see cref="T:System.Speech.Synthesis.SynthesisTextFormat" /> member that specifies the format of the text.</param>
    </member>
    <member name="P:System.Speech.Synthesis.Prompt.IsCompleted">
      <summary>Gets or sets whether the Prompt has finished playing.</summary>
      <returns>
        <paramref name="false" /> if the prompt has completed; otherwise <paramref name="true" />.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.PromptBreak">
      <summary>Enumerates values for levels of prosodic boundaries (pausing) between words.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.PromptBreak.None">
      <summary>Indicates no break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptBreak.ExtraSmall">
      <summary>Indicates an extra small pause.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptBreak.Small">
      <summary>Indicates a small pause.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptBreak.Medium">
      <summary>Indicates a medium pause.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptBreak.Large">
      <summary>Indicates a large pause.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptBreak.ExtraLarge">
      <summary>Indicates an extra large pause.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.PromptBuilder">
      <summary>Creates an empty Prompt object and provides methods for adding content.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.#ctor">
      <summary>Creates a new instance of the PromptBuilder class.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.#ctor(System.Globalization.CultureInfo)">
      <summary>Creates a new instance of the PromptBuilder class.</summary>
      <param name="culture">Provides information about a specific culture, such as the names of the culture, the writing system, the calendar used, and how to format dates and sort strings.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendAudio(System.String)">
      <summary>Appends the specified audio file to the PromptBuilder.</summary>
      <param name="path">A fully qualified path to the audio file.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendAudio(System.Uri)">
      <summary>Appends the audio file at the specified URI to the <see cref="PromptBuilder" />.</summary>
      <param name="audioFile">URI for the audio file.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendAudio(System.Uri,System.String)">
      <summary>Appends the specified audio file and alternate text to the PromptBuilder.</summary>
      <param name="audioFile">URI for the audio file.</param>
      <param name="alternateText">A string containing alternate text representing the audio.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendBookmark(System.String)">
      <summary>Appends a bookmark to the <see cref="PromptBuilder" /> object.</summary>
      <param name="bookmarkName">A string containing the name of the appended bookmark.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendBreak">
      <summary>Appends a break to the <see cref="PromptBuilder" /> object.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendBreak(System.Speech.Synthesis.PromptBreak)">
      <summary>Appends a break of the specified strength to the <see cref="PromptBuilder" /> object.</summary>
      <param name="strength">Indicates the length of the break, with the following increasing values:None. Indicates to suppress a break where one is normally inserted by default.ExtraSmallSmallMediumLargeExtraLarge</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendBreak(System.TimeSpan)">
      <summary>Appends a break of the specified duration to the <see cref="PromptBuilder" /> object.</summary>
      <param name="duration">The time in ticks, where one tick equals 100 nanoseconds.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendPromptBuilder(System.Speech.Synthesis.PromptBuilder)">
      <summary>Appends a <see cref="PromptBuilder" /> object to a <see cref="PromptBuilder" /> object.</summary>
      <param name="promptBuilder">Specifies a <see cref="PromptBuilder" /> object.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendSsml(System.String)">
      <summary>Appends the SSML at the specified path to the <see cref="PromptBuilder" /> object.</summary>
      <param name="path">A fully qualified path to the SSML file to append.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendSsml(System.Uri)">
      <summary>Appends the SSML file at the specified URI to the <see cref="PromptBuilder" /> object.</summary>
      <param name="ssmlFile">A fully qualified URI to the SSML file to append.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendSsml(System.Xml.XmlReader)">
      <summary>Appends the specified XML file containing SSML to the <see cref="PromptBuilder" /> object.</summary>
      <param name="ssmlFile">A fully qualified name to the XML file to append.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendSsmlMarkup(System.String)">
      <summary>Appends the specified string containing SSML markup into the <see cref="PromptBuilder" /> object.</summary>
      <param name="ssmlMarkup">A string containing SSML markup.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendText(System.String)">
      <summary>Appends text to the <see cref="PromptBuilder" /> object.</summary>
      <param name="textToSpeak">A string containing the text to be spoken.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendText(System.String,System.Speech.Synthesis.PromptEmphasis)">
      <summary>Appends text to the <see cref="PromptBuilder" /> object and specifies an emphasis value for the text.</summary>
      <param name="textToSpeak">A string containing the text to be spoken.</param>
      <param name="emphasis">The value for the emphasis or stress for the text, with the following decreasing values:NotSetStrongModerateNoneReduced</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendText(System.String,System.Speech.Synthesis.PromptRate)">
      <summary>Appends text to the <see cref="PromptBuilder" /> object and specifies a speed value for the text to be spoken.</summary>
      <param name="textToSpeak">A string containing the text to be spoken.</param>
      <param name="rate">The value for the speed for the text, with the following decreasing values:NotSetExtraFastFastMediumSlowExtraSlow</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendText(System.String,System.Speech.Synthesis.PromptVolume)">
      <summary>Appends text to the <see cref="PromptBuilder" /> object and specifies a volume for the text to be spoken.</summary>
      <param name="textToSpeak">A string containing the text to be spoken.</param>
      <param name="volume">The value for the speed for the text, with the following increasing values:NotSetSilentExtraSoftSoftMediumLoudExtraLoud</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendTextWithAlias(System.String,System.String)">
      <summary>Appends text to the <see cref="PromptBuilder" /> object and specifies the alias text to be spoken in place of the appended text.</summary>
      <param name="textToSpeak">A string containing the text representation.</param>
      <param name="substitute">A string containing the text to be spoken.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendTextWithHint(System.String,System.Speech.Synthesis.SayAs)">
      <summary>Appends text to the <see cref="PromptBuilder" /> object and specifies the construct from the <see cref="SayAs" /> enumerations to use to render the text.</summary>
      <param name="textToSpeak">A string containing the text to be spoken.</param>
      <param name="sayAs">A construct from the enumeration in <see cref="T:System.Speech.Synthesis.SayAs" />.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendTextWithHint(System.String,System.String)">
      <summary>Appends text to the <see cref="PromptBuilder" /> object and specifies the construct to use to render the text as a string.</summary>
      <param name="textToSpeak">A string containing the text to be spoken.</param>
      <param name="sayAs">A string containing the construct to use to render the text.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendTextWithPronunciation(System.String,System.String)">
      <summary>Appends text to the <see cref="PromptBuilder" /> object and specifies the pronunciation for the text in a string using phonemic representations. </summary>
      <param name="textToSpeak">A string containing the text to be spoken.</param>
      <param name="pronunciation">A string consisting of phonemic representations from the International Phonetic Association (IPA) alphabet.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.ClearContent">
      <summary>Clears the content from the <see cref="PromptBuilder" /> object.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.PromptBuilder.Culture">
      <summary>Gets the culture information of the PromptBuilder.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.EndParagraph">
      <summary>Specifies the end of a paragraph in the <see cref="PromptBuilder" /> object.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.EndSentence">
      <summary>Specifies the end of a sentence in the <see cref="PromptBuilder" /> object.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.EndStyle">
      <summary>Specifies the end of a style in the <see cref="PromptBuilder" /> object.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.EndVoice">
      <summary>Specifies the end of use of a voice in the <see cref="PromptBuilder" /> object.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.PromptBuilder.IsEmpty">
      <summary>Gets whether the PromptBuilder is empty.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartParagraph">
      <summary>Specifies the start of a paragraph in the <see cref="PromptBuilder" /> object.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartParagraph(System.Globalization.CultureInfo)">
      <summary>Specifies the start of a paragraph in the specified culture in the <see cref="PromptBuilder" /> object.</summary>
      <param name="culture">Provides information about a specific culture, such as the names of the culture, the writing system, the calendar used, and how to format dates and sort strings.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartSentence">
      <summary>Specifies the start of a sentence in the <see cref="PromptBuilder" /> object.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartSentence(System.Globalization.CultureInfo)">
      <summary>Specifies the start of a sentence in the specified culture in the <see cref="PromptBuilder" /> object.</summary>
      <param name="culture">Provides information about a specific culture, such as the names of the culture, the writing system, the calendar used, and how to format dates and sort strings.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartStyle(System.Speech.Synthesis.PromptStyle)">
      <summary>Specifies the start of a style in the <see cref="PromptBuilder" /> object.</summary>
      <param name="style">The style to start:<see cref="T:System.Speech.Synthesis.PromptEmphasis" /> emphasis.<see cref="T:System.Speech.Synthesis.PromptRate" /> rate.<see cref="T:System.Speech.Synthesis.PromptVolume" /> volume.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartVoice(System.Globalization.CultureInfo)">
      <summary>Specifies the start of use of a voice in the specified culture in the <see cref="PromptBuilder" /> object.</summary>
      <param name="culture">Provides information about a specific culture, such as the names of the culture, the writing system, the calendar used, and how to format dates and sort strings.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartVoice(System.Speech.Synthesis.VoiceGender)">
      <summary>Specifies the start of use of a voice of the specified gender in the <see cref="PromptBuilder" /> object.</summary>
      <param name="gender">One of the following gender attributes:NotSetMaleFemaleNeutral</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartVoice(System.Speech.Synthesis.VoiceGender,System.Speech.Synthesis.VoiceAge)">
      <summary>Specifies the start of use of a voice of the specified gender and the specified age in the <see cref="PromptBuilder" /> object.</summary>
      <param name="gender">One of the following gender attributes:NotSetMaleFemaleNeutral</param>
      <param name="age">One of the following age attributes:NotSetChild = 10Teen = 15Adult =30Senior = 65</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartVoice(System.Speech.Synthesis.VoiceGender,System.Speech.Synthesis.VoiceAge,System.Int32)">
      <summary>Specifies the start of use of a voice of the specified gender and the specified age, and an alternate voice in the <see cref="PromptBuilder" /> object.</summary>
      <param name="gender">One of the following gender attributes:NotSetMaleFemaleNeutral</param>
      <param name="age">One of the following age attributes:NotSetChild = 10Teen = 15Adult =30Senior = 65</param>
      <param name="voiceAlternate">The integer specifying an alternate voice, if the voice specified by the other parameters is not present.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartVoice(System.Speech.Synthesis.VoiceInfo)">
      <summary>Specifies the start of use of a voice specified by the <see cref="VoiceInfo" /> object in the <see cref="PromptBuilder" /> object.</summary>
      <param name="voice">The <see cref="VoiceInfo" /> object with voice attributes.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartVoice(System.String)">
      <summary>Specifies the start of use of a voice in the <see cref="PromptBuilder" /> object.</summary>
      <param name="name">A string specifying the name of a voice.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.ToXml">
      <summary>Returns the SSML generated from the <see cref="PromptBuilder" /> object.</summary>
      <returns>Returns the SSML generated from the <see cref="PromptBuilder" /> object as a single line.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.PromptEmphasis">
      <summary>Enumerates values for the levels of speaking emphasis in prompts.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.PromptEmphasis.NotSet">
      <summary>Indicates that no emphasis value is specified.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptEmphasis.Strong">
      <summary>Indicates a strong level of emphasis.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptEmphasis.Moderate">
      <summary>Indicates a moderate level of emphasis.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptEmphasis.None">
      <summary>Indicates no emphasis.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptEmphasis.Reduced">
      <summary>Indicates a reduced level of emphasis.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.PromptEventArgs">
      <summary>Represents the base class for SpeechSynthesizer EventArgs classes.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.PromptEventArgs.Prompt">
      <summary>Gets the prompt associated with the event.</summary>
      <returns>The Prompt object associated with the event.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.PromptRate">
      <summary>Enumerates values for levels of speaking rate in prompts.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.PromptRate.NotSet">
      <summary>Indicates no rate is specified.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptRate.ExtraFast">
      <summary>Indicates an extra-fast rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptRate.Fast">
      <summary>Indicates a fast rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptRate.Medium">
      <summary>Indicates a medium rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptRate.Slow">
      <summary>Indicates a slow rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptRate.ExtraSlow">
      <summary>Indicates an extra-slow rate.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.PromptStyle">
      <summary>Defines a style of prompting that consists of settings for emphasis, rate, and volume.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.PromptStyle.#ctor">
      <summary>Initializes a new instance of the PromptStyle class.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptStyle.#ctor(System.Speech.Synthesis.PromptEmphasis)">
      <summary>Initializes a new instance of the PromptStyle class.</summary>
      <param name="emphasis">A PromptEmphasis enumeration member indicating the emphasis of the style.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptStyle.#ctor(System.Speech.Synthesis.PromptRate)">
      <summary>Initializes a new instance of the PromptStyle class.</summary>
      <param name="rate">A PromptRate enumeration member indicating the speaking rate of the style.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptStyle.#ctor(System.Speech.Synthesis.PromptVolume)">
      <summary>Initializes a new instance of the PromptStyle class.</summary>
      <param name="volume">A PromptVolume enumeration member indicating the volume of the style.</param>
    </member>
    <member name="P:System.Speech.Synthesis.PromptStyle.Emphasis">
      <summary>Gets or sets the emphasis of the style.</summary>
      <returns>A PromptEmphasis enumeration member indicating the emphasis of the style.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.PromptStyle.Rate">
      <summary>Gets or sets the speaking rate of the style.</summary>
      <returns>A PromptRate enumeration member indicating the speaking rate of the style.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.PromptStyle.Volume">
      <summary>Gets or sets the volume (loudness) of the style.</summary>
      <returns>A PromptVolume enumeration member indicating the volume of the style.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.PromptVolume">
      <summary>Enumerates values for levels of volume (loudness) in prompts.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.NotSet">
      <summary>Indicates that the volume level is not set.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.Silent">
      <summary>Indicates a muted volume level.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.ExtraSoft">
      <summary>Indicates an extra soft volume level.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.Soft">
      <summary>Indicates a soft volume level.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.Medium">
      <summary>Indicates a medium volume level.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.Loud">
      <summary>Indicates a loud volume level.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.ExtraLoud">
      <summary>Indicates an extra loud volume level.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.Default">
      <summary>Indicates the engine-specific default volume level.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.SayAs">
      <summary>Enumerates the data formats for the speaking of elements such as times, dates, and currency.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.SpellOut">
      <summary>Spell the word or phrase.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.NumberOrdinal">
      <summary>Speak a number as an ordinal number. For example, speak "3" as "third."</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.NumberCardinal">
      <summary>Speak a number as a cardinal number. For example, speak "3" as "three."</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Date">
      <summary>Same as DayMonthYear.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.DayMonthYear">
      <summary />
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.MonthDayYear">
      <summary />
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.YearMonthDay">
      <summary />
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.YearMonth">
      <summary />
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.MonthYear">
      <summary />
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.MonthDay">
      <summary />
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.DayMonth">
      <summary />
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Year">
      <summary />
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Month">
      <summary />
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Day">
      <summary />
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Time">
      <summary>Speak the word or phrase as a time.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Time24">
      <summary />
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Time12">
      <summary />
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Telephone">
      <summary>Speak the word or phrase as a U.S. telephone number.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Text">
      <summary>Speak the word or phrase as text.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.SpeakCompletedEventArgs">
      <summary>Returns notification from the SpeechSynthesizer.SpeakCompleted event.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.SpeakProgressEventArgs">
      <summary>Returns data from the SpeechSynthesizer.SpeakProgress event.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.SpeakProgressEventArgs.AudioPosition">
      <summary>Gets the audio position of the event.</summary>
      <returns>A TimeSpan object that represents the time position of the event in the audio output stream.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.SpeakProgressEventArgs.CharacterCount"></member>
    <member name="P:System.Speech.Synthesis.SpeakProgressEventArgs.CharacterPosition">
      <summary>Gets the character position of the event.</summary>
      <returns>An int that represents the character position of the event in the text input stream.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.SpeakProgressEventArgs.Text"></member>
    <member name="T:System.Speech.Synthesis.SpeakStartedEventArgs">
      <summary>Returns notification from the SpeechSynthesizer.SpeakStarted event.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.SpeechSynthesizer">
      <summary>Supports the production of speech and DTMF output.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.#ctor">
      <summary>Creates a new instance of SpeechSynthesizer.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.AddLexicon(System.Uri,System.String)">
      <param name="uri"></param>
      <param name="mediaType"></param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.BookmarkReached">
      <summary>Raised when a bookmark is reached.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.Dispose"></member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.GetCurrentlySpokenPrompt">
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.GetInstalledVoices">
      <summary>Returns the collection of installed TTS voices.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.GetInstalledVoices(System.Globalization.CultureInfo)">
      <summary>Returns the collection of installed TTS voices.</summary>
      <param name="culture">A System.CultureInfo object that indicates the requested culture.  </param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.Pause">
      <summary>Pauses the synthesizer.</summary>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.PhonemeReached">
      <summary>Raised when a phoneme is reached.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.SpeechSynthesizer.Rate">
      <summary>Gets the speaking rate of the SpeechSynthesizer.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.RemoveLexicon(System.Uri)">
      <param name="uri"></param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.Resume"></member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SelectVoice(System.String)">
      <summary>Selects a specific voice.</summary>
      <param name="name">The requested voice name.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SelectVoiceByHints(System.Speech.Synthesis.VoiceGender)">
      <summary>Selects a voice with a specific voice gender.</summary>
      <param name="gender">A <see cref="T:System.Speech.Synthesis.VoiceGender" /> member that indicates the requested voice gender.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SelectVoiceByHints(System.Speech.Synthesis.VoiceGender,System.Speech.Synthesis.VoiceAge)">
      <summary>Selects a voice with a specific voice gender and voice age.</summary>
      <param name="gender">A <see cref="T:System.Speech.Synthesis.VoiceGender" /> member that indicates the requested voice gender.</param>
      <param name="age">A <see cref="T:System.Speech.Synthesis.VoiceAge" /> member that indicates the requested voice age.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SelectVoiceByHints(System.Speech.Synthesis.VoiceGender,System.Speech.Synthesis.VoiceAge,System.Int32)">
      <summary>Selects a voice with a specific voice gender and voice age, specifying an alternate voice.</summary>
      <param name="gender">A <see cref="T:System.Speech.Synthesis.VoiceGender" /> member that indicates the requested voice gender.</param>
      <param name="age">A <see cref="T:System.Speech.Synthesis.VoiceAge" /> member that indicates the requested voice age.</param>
      <param name="voiceAlternate">The name of an alternate voice.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SelectVoiceByHints(System.Speech.Synthesis.VoiceGender,System.Speech.Synthesis.VoiceAge,System.Int32,System.Globalization.CultureInfo)">
      <summary>Selects a voice with a specific voice gender, voice age and culture, specifying an alternate voice.</summary>
      <param name="gender">A <see cref="T:System.Speech.Synthesis.VoiceGender" /> member that indicates the requested voice gender.</param>
      <param name="age">A <see cref="T:System.Speech.Synthesis.VoiceAge" /> member that indicates the requested voice age.</param>
      <param name="voiceAlternate">The name of an alternate voice.</param>
      <param name="culture">A System.CultureInfo object that indicates the requested culture.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SetOutputToAudioStream(System.IO.Stream,System.Speech.AudioFormat.SpeechAudioFormatInfo)">
      <param name="audioDestination"></param>
      <param name="formatInfo"></param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SetOutputToDefaultAudioDevice"></member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SetOutputToNull"></member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SetOutputToWaveFile(System.String)">
      <param name="path"></param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SetOutputToWaveFile(System.String,System.Speech.AudioFormat.SpeechAudioFormatInfo)">
      <param name="path"></param>
      <param name="formatInfo"></param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SetOutputToWaveStream(System.IO.Stream)">
      <param name="audioDestination"></param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.Speak(System.Speech.Synthesis.Prompt)">
      <summary>Speaks the specified prompt.</summary>
      <param name="prompt">The prompt to be spoken.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.Speak(System.Speech.Synthesis.PromptBuilder)">
      <summary>Speaks the specified PromptBuilder.</summary>
      <param name="promptBuilder"></param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.Speak(System.String)">
      <summary>Speaks the specified text string.</summary>
      <param name="textToSpeak">The text to speak.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SpeakAsync(System.Speech.Synthesis.Prompt)">
      <summary>Speaks the specified prompt asynchronously.</summary>
      <param name="prompt"></param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SpeakAsync(System.Speech.Synthesis.PromptBuilder)">
      <summary>Speaks the specified PromptBuilder asynchronously.</summary>
      <param name="promptBuilder"></param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SpeakAsync(System.String)">
      <summary>Speaks the specified text string asynchronously.</summary>
      <param name="textToSpeak">The text to speak.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SpeakAsyncCancel(System.Speech.Synthesis.Prompt)">
      <summary>Cancels asynchronous speaking of the specified prompt.</summary>
      <param name="prompt"></param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SpeakAsyncCancelAll">
      <summary>Cancels asynchronous speaking of all queued prompts.</summary>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.SpeakCompleted">
      <summary>Raised when the SpeechSynthesizer completes the speaking of a prompt.</summary>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.SpeakProgress"></member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SpeakSsml(System.String)">
      <summary>Speaks the specified SSML string.</summary>
      <param name="textToSpeak">The SSML string to be spoken.  </param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SpeakSsmlAsync(System.String)">
      <summary>Speaks the specified text string asynchronously.</summary>
      <param name="textToSpeak">The text string to be spoken.  </param>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.SpeakStarted">
      <summary>Raised when the SpeechSynthesizer begins the speaking of a prompt.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.SpeechSynthesizer.State">
      <summary>Gets the speaking state of the SpeechSynthesizer.</summary>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.StateChanged">
      <summary>Raised when the state of the SpeechSynthesizer changes.</summary>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.VisemeReached">
      <summary>Raised when a viseme is reached.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.SpeechSynthesizer.Voice">
      <summary>Gets the voice of the SpeechSynthesizer.</summary>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.VoiceChange">
      <summary>Raised when the voice of the SpeechSynthesizer changes.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.SpeechSynthesizer.Volume">
      <summary>Gets the speaking volume of the SpeechSynthesizer.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Speech.Synthesis.StateChangedEventArgs">
      <summary>Returns data from the SpeechSynthesizer.StateChanged event.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.StateChangedEventArgs.PreviousState">
      <summary>Gets the synthesizer State before the change.</summary>
      <returns>A SynthesizerState object that represents the State before the change.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.StateChangedEventArgs.State">
      <summary>Gets the synthesizer State after the change.</summary>
      <returns>A SynthesizerState object that represents the State after the change.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.SynthesisMediaType">
      <summary>Enumerates types of synthesis media.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesisMediaType.Text">
      <summary>Indicates that the media type is <see cref="Text" />.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesisMediaType.Ssml">
      <summary>Indicates that the media type is <see cref="SSML" />.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesisMediaType.WaveAudio">
      <summary>Indicates that the media type is <see cref="WaveAudio" />.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.SynthesisTextFormat">
      <summary>Enumerates types of text input for the synthesizer.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesisTextFormat.Text">
      <summary>Indicates that text format is <see cref="Text" />. </summary>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesisTextFormat.Ssml">
      <summary>Indicates that text format is <see cref="SSML" />. </summary>
    </member>
    <member name="T:System.Speech.Synthesis.SynthesizerEmphasis">
      <summary>Enumerates levels of synthesizer emphasis.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesizerEmphasis.Stressed">
      <summary>Indicates a high level of synthesizer emphasis.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesizerEmphasis.Emphasized">
      <summary>Indicates a low level of synthesizer emphasis.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.SynthesizerState">
      <summary>Enumerates values for the State of the synthesizer.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesizerState.Ready">
      <summary>Indicates that the synthesizer is ready.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesizerState.Speaking">
      <summary>Indicates that the synthesizer is speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesizerState.Paused">
      <summary>Indicates that the synthesizer is paused.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.VisemeReachedEventArgs">
      <summary>Returns data from the VisemeReached event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.VisemeReachedEventArgs.AudioPosition">
      <summary>Gets the position of the viseme in the audio stream.</summary>
      <returns>A TimeSpan object that represents the position of the viseme.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.VisemeReachedEventArgs.Duration">
      <summary>Gets the duration of the viseme.</summary>
      <returns>A TimeSpan object that represents the duration of the viseme.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.VisemeReachedEventArgs.Emphasis">
      <summary>Gets the SynthesizerEmphasis of the viseme.</summary>
      <returns>A SynthesizerEmphasis object that represents the emphasis of the viseme.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.VisemeReachedEventArgs.NextViseme">
      <summary>Gets the value of the next viseme.</summary>
      <returns>An int that contains the value of the next viseme.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.VisemeReachedEventArgs.Viseme">
      <summary>Gets the value of the viseme.</summary>
      <returns>An int that contains the value of the viseme.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.VoiceAge">
      <summary>Defines the values for the age of synthesized voices.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceAge.NotSet">
      <summary>Indicates that no voice age is specified.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceAge.Child">
      <summary>Indicates a child voice.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceAge.Teen">
      <summary>Indicates a teenage voice.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceAge.Adult">
      <summary>Indicates an adult voice.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceAge.Senior">
      <summary>Indicates a senior voice.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.VoiceChangeEventArgs">
      <summary>Returns data from the VoiceChange event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceChangeEventArgs.Voice">
      <summary>Gets the VoiceInfo of the new voice.</summary>
      <returns>A VoiceInfo object that identifies the new voice.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.VoiceGender">
      <summary>Defines the values for the gender of synthesized voices.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceGender.NotSet">
      <summary>Indicates no voice gender specification.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceGender.Male">
      <summary>Indicates a male voice.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceGender.Female">
      <summary>Indicates a female voice.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceGender.Neutral">
      <summary>Indicates a gender-neutral voice.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.VoiceInfo">
      <summary>Represents a text-to-speech (TTS) voice.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.AdditionalInfo">
      <summary>Gets the AdditionalInfo dictionary of the Voice.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.Age">
      <summary>Gets the age of the Voice.</summary>
      <returns>Returns a <see cref="T:System.Speech.Synthesis.VoiceAge" /> object that contains the current age of the <see cref="VoiceInfo" /> object.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.Culture">
      <summary>Gets the Culture of the Voice.</summary>
      <returns>Returns a <see cref="System.Globalization.CultureInfo" /> object that provides information about a specific culture, such as the names of the culture, the writing system, the calendar used, and how to format dates and sort strings.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.Description">
      <summary>Gets the description of the Voice.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.VoiceInfo.Equals(System.Object)">
      <summary>Compares the fields of the voice with the specified <see cref="VoiceInfo" /> object to determine whether they contain the same values.</summary>
      <returns>True if the fields of the two <see cref="VoiceInfo" /> objects are equal. False if the fields of the two <see cref="VoiceInfo" /> objects are different.</returns>
      <param name="obj"></param>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.Gender">
      <summary>Gets the gender of the Voice.</summary>
      <returns>Returns a <see cref="T:System.Speech.Synthesis.VoiceGender" /> object that contains the gender of the <see cref="VoiceInfo" /> object.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.VoiceInfo.GetHashCode"></member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.Id">
      <summary>Gets the ID of the Voice.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.Name">
      <summary>Gets the Name of the Voice.</summary>
      <returns>Returns the processor specific voice that is associated with this <see cref="VoiceInfo" /> object.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.SupportedAudioFormats">
      <summary>Gets the collection of audio formats supported by the Voice.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ContourPoint">
      <summary>Represents the volume for a text fragment with which the <see cref="T:System.Speech.Synthesis.TtsEngine.ContourPoint" /> is associated. </summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ContourPoint.#ctor(System.Single,System.Single,System.Speech.Synthesis.TtsEngine.ContourPointChangeType)">
      <param name="start"></param>
      <param name="change"></param>
      <param name="changeType"></param>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ContourPoint.Change"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ContourPoint.ChangeType"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ContourPoint.Equals(System.Object)">
      <summary>Determines whether the specified ContourPoint instances are considered equal.</summary>
      <returns>true if the specified Object is equal to the current Object; otherwise, false.</returns>
      <param name="obj">The System.Object to compare with the current Object.</param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ContourPoint.Equals(System.Speech.Synthesis.TtsEngine.ContourPoint)">
      <param name="other"></param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ContourPoint.GetHashCode">
      <summary>Returns a hash code for this instance.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ContourPoint.op_Equality(System.Speech.Synthesis.TtsEngine.ContourPoint,System.Speech.Synthesis.TtsEngine.ContourPoint)">
      <param name="point1"></param>
      <param name="point2"></param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ContourPoint.op_Inequality(System.Speech.Synthesis.TtsEngine.ContourPoint,System.Speech.Synthesis.TtsEngine.ContourPoint)">
      <param name="point1"></param>
      <param name="point2"></param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ContourPoint.Start"></member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ContourPointChangeType">
      <summary>Enumerates values for the types of ContourPoint change.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ContourPointChangeType.Hz">
      <summary>Indicates a change of the pitch value.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ContourPointChangeType.Percentage">
      <summary>Indicates a change of the time value.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.EmphasisBreak">
      <summary>Enumerates values for lengths of EmphasisBreak between spoken words.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisBreak.None">
      <summary>No word break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisBreak.ExtraWeak">
      <summary>Very small word break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisBreak.Weak">
      <summary>Small word break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisBreak.Medium">
      <summary>Moderate word break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisBreak.Strong">
      <summary>Long word break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisBreak.ExtraStrong">
      <summary>Longest word break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisBreak.Default">
      <summary>Normal word break.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.EmphasisWord">
      <summary>Enumerates the values of EmphasisWord for a specific TextFragment.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisWord.Default">
      <summary>Indicates an engine-specific default level of emphasis.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisWord.Strong">
      <summary>Indicates strong emphasis.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisWord.Moderate">
      <summary>Indicates moderate emphasis.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisWord.None">
      <summary>Indicates no emphasis specified.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisWord.Reduced">
      <summary>Indicates reduced emphasis.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.EventParameterType">
      <summary>Enumerates the types of data pointers passed to speech synthesis events.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EventParameterType.Undefined">
      <summary>Indicates that the <paramref name="param2" /> argument to the <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is undefined.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EventParameterType.Token">
      <summary>Indicates that the <paramref name="param2" /> argument to the <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is a </summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EventParameterType.Object">
      <summary>Currently not supported.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EventParameterType.Pointer">
      <summary>Currently not supported.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EventParameterType.String">
      <summary>Indicates that the <paramref name="param2" /> argument to the <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is a System.IntPtr created using System.Runtime.InteropServices.Marshal.StringToCoTaskMemUni referencing a System.String object; <paramref name="param1" /> may take on any value.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.FragmentState">
      <summary>Provides detailed information about a TextFragment.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.FragmentState.#ctor(System.Speech.Synthesis.TtsEngine.TtsEngineAction,System.Int32,System.Int32,System.Int32,System.Speech.Synthesis.TtsEngine.SayAs,System.Speech.Synthesis.TtsEngine.Prosody,System.Char[])">
      <summary>Constructs a new instance of FragmentState.</summary>
      <param name="action">A member of the <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEngineAction" />  enumeration that specifies a speech synthesis action.</param>
      <param name="langId">The id of the language being used. Corresponds to the XML xml:lang attribute.</param>
      <param name="emphasis">The emphasis to be applied to speech output or pauses.</param>
      <param name="duration">The time allotted to speak the text of the TextFragment. </param>
      <param name="sayAs">A member of the <see cref="T:System.Speech.Synthesis.TtsEngine.SayAs" /> class, indicating the type of text of the TextFragment and the level of detail required for accurate rendering of the contained text. Corresponds to the &lt;say-as&gt; XML tag in the SSML specificationThe argument may be null.</param>
      <param name="prosody">A <see cref="T:System.Speech.Synthesis.TtsEngine.Prosody" /> object indicating characteristics of the speech output such as pitch, speaking rate and volume. Corresponds to the &lt;prosody&gt; XML tag in the SSML specification</param>
      <param name="phonemes">An array of char objects providing the phonetic pronunciation for text contained in the <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" />, using the International Phonetic Alphabet (IPA) specification.Corresponds to the &lt;phoneme&gt; XML tag in the SSML specification.This argument may be null.</param>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.FragmentState.Action">
      <summary>Returns the requested speech synthesizer action.</summary>
      <returns>A member of <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEngineAction" /> indicating the speech synthesis action requested by SSML input.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.FragmentState.Duration">
      <summary>Returns the desired time for rendering a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /></summary>
      <returns>Returns an int containing a value in millisecond of the desired time for rendering a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" />.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.FragmentState.Emphasis">
      <summary>Returns instructions on how to emphasize a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" />.</summary>
      <returns>Returns an int value indicating how to emphasize a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" />.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.FragmentState.Equals(System.Object)">
      <summary>Determines if a given object is an instance <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" /> equal to the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" />.</summary>
      <returns>Returns true, if the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> and that obtained from the  provided by the <paramref name="obj" /> argument describe the same <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state. Returns false if the current <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> and the <paramref name="obj" /> argument do not support the same <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state.</returns>
      <param name="obj">An object that can be cast to an instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /></param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.FragmentState.Equals(System.Speech.Synthesis.TtsEngine.FragmentState)">
      <summary>Determines if a given instance of <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" /> is equal to the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" />.</summary>
      <returns>Returns true, if both the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> and that supplied through the <paramref name="other" /> argument describe the same <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state. Returns false if the current <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> and the <paramref name="other" /> argument do not support the same <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state.</returns>
      <param name="other">An instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> that </param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.FragmentState.GetHashCode">
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.FragmentState.LangId">
      <summary>Returns the language supported by the current <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" />.</summary>
      <returns>Returns an int containing an identifier for the language used by the current <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" />.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.FragmentState.op_Equality(System.Speech.Synthesis.TtsEngine.FragmentState,System.Speech.Synthesis.TtsEngine.FragmentState)">
      <summary>Determines if two instances of <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" /> describes the same <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state.</summary>
      <returns>Returns true if both instances of <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" />, <paramref name="state1" /> and <paramref name="state2" />, describe the same <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state, otherwise false is returned.</returns>
      <param name="state1">An instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> whose described state is compared against the instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> provided by the <paramref name="state2" /> argument.</param>
      <param name="state2">An instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> whose described state is compared against the instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> provided by the <paramref name="state1" /> argument.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.FragmentState.op_Inequality(System.Speech.Synthesis.TtsEngine.FragmentState,System.Speech.Synthesis.TtsEngine.FragmentState)">
      <summary>Determines if two instances of <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" /> describes the different <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state.</summary>
      <returns>Returns true if both instances of <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" />, <paramref name="state1" /> and <paramref name="state2" />, do not describe the same <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state, otherwise false is returned.</returns>
      <param name="state1">An instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> whose described state is compared against the instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> provided by the <paramref name="state2" /> argument.</param>
      <param name="state2">An instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> whose described state is compared against the instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> provided by the <paramref name="state1" /> argument.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.FragmentState.Phoneme">
      <summary>Returns phonetic information for a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /></summary>
      <returns>Returns an array of char instance containing the phonetic pronunciation for the <see cref="P;System.Speech.Synthesis.TtsEngine.TextFragment.TextToSpeak" /> property on a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> instance.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.FragmentState.Prosody">
      <summary>Returns detailed information about the pitch, speaking rate, and volume of speech output.</summary>
      <returns>Returns a valid instance of <see cref="T:System.Speech.Synthesis.TtsEngine.Prosody" /> containing the pitch, speaking rate, and volume settings, and changes to those setting, for speech output.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.FragmentState.SayAs">
      <summary>Returns information about the context for the generation of speech from text..</summary>
      <returns>Returns a value <see cref="T:System.Speech.Synthesis.TtsEngine.SayAs" /> instance if the SSML used by a speech synthesis engine contains detailed information about the context to be used to generate speech, otherwise null.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ITtsEngineSite">
      <summary>Provides methods for writing audio data and events.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.Actions">
      <summary>Determines the action or actions the engine should perform.</summary>
      <returns>An int containing the sum of one or more members of the TtsEngineAction enumeration.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.AddEvents(System.Speech.Synthesis.TtsEngine.SpeechEventInfo[],System.Int32)">
      <summary>Adds one or more events to the EventInterest property.</summary>
      <param name="events">An array of SpeechEventInfo objects.</param>
      <param name="count">The size of the array.</param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.CompleteSkip(System.Int32)">
      <summary>Returns the number of items skipped.</summary>
      <param name="skipped">The number of items skipped.</param>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.EventInterest">
      <summary>Determines the events the engine should raise.</summary>
      <returns>An int containing the sum of one or more members of the TtsEventId enumeration.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.GetSkipInfo">
      <summary>Returns the number and type of items to be skipped.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.LoadResource(System.Uri,System.String)">
      <summary>Loads the resource at the specified URI.</summary>
      <param name="uri">The URI of the resource.</param>
      <param name="mediaType">The media type of the resource.</param>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.Rate">
      <summary>Gets the speaking rate of the engine.</summary>
      <returns>An int containing the speaking rate.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.Volume">
      <summary>Gets the speaking volume of the engine.</summary>
      <returns>An int containing the speaking volume.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.Write(System.IntPtr,System.Int32)">
      <summary>Outputs audio data.</summary>
      <param name="data">The location of the output audio data.</param>
      <param name="count">The number of items in the output audio stream.</param>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.Prosody">
      <summary>Represents a collection of settings for voice properties such as Pitch, Rate and Volume.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.Prosody.#ctor">
      <summary>Constructs a new instance of the Prosody class.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.Prosody.Duration">
      <summary>Gets or sets the duration of the TextFragment in milliseconds.</summary>
      <returns>A value in milliseconds for the desired time to speak the text.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.Prosody.GetContourPoints">
      <summary>Returns an array containing the ContourPoints of the TextFragment.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.Prosody.Pitch">
      <summary>Gets or sets the baseline pitch of the TextFragment.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.Prosody.Range">
      <summary>Gets or sets the pitch range of the TextFragment.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.Prosody.Rate">
      <summary>Gets or sets the speaking rate of the TextFragment.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.Prosody.SetContourPoints(System.Speech.Synthesis.TtsEngine.ContourPoint[])">
      <summary>Sets the ContourPoints of the TextFragment.</summary>
      <param name="points">A byte array of ContourPoint objects.</param>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.Prosody.Volume">
      <summary>Gets or sets the speaking volume (loudness) of the TextFragment.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ProsodyNumber"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ProsodyNumber.#ctor(System.Int32)">
      <param name="ssmlAttributeId"></param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ProsodyNumber.#ctor(System.Single)">
      <param name="number"></param>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyNumber.AbsoluteNumber"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ProsodyNumber.Equals(System.Object)">
      <param name="obj"></param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ProsodyNumber.Equals(System.Speech.Synthesis.TtsEngine.ProsodyNumber)">
      <param name="other"></param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ProsodyNumber.GetHashCode">
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ProsodyNumber.IsNumberPercent"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ProsodyNumber.Number"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ProsodyNumber.op_Equality(System.Speech.Synthesis.TtsEngine.ProsodyNumber,System.Speech.Synthesis.TtsEngine.ProsodyNumber)">
      <param name="prosodyNumber1"></param>
      <param name="prosodyNumber2"></param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ProsodyNumber.op_Inequality(System.Speech.Synthesis.TtsEngine.ProsodyNumber,System.Speech.Synthesis.TtsEngine.ProsodyNumber)">
      <param name="prosodyNumber1"></param>
      <param name="prosodyNumber2"></param>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ProsodyNumber.SsmlAttributeId"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ProsodyNumber.Unit"></member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ProsodyPitch">
      <summary>Enumerates values for the Pitch property of a Prosody object.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyPitch.Default">
      <summary>Indicates a normal pitch range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyPitch.ExtraLow">
      <summary>Indicates an extra-low pitch range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyPitch.Low">
      <summary>Indicates a low pitch range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyPitch.Medium">
      <summary>Indicates a medium pitch range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyPitch.High">
      <summary>Indicates a high pitch range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyPitch.ExtraHigh">
      <summary>Indicates an extra- high pitch range.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ProsodyRange">
      <summary>Enumerates values for the Range property of a Prosody object.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRange.Default">
      <summary>Indicates a normal prosody range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRange.ExtraLow">
      <summary>Indicates an extra low prosody range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRange.Low">
      <summary>Indicates a low prosody range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRange.Medium">
      <summary>Indicates a medium prosody range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRange.High">
      <summary>Indicates a high prosody range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRange.ExtraHigh">
      <summary>Indicates an extra high prosody range.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ProsodyRate">
      <summary>Enumerates values for the Rate property of a Prosody object.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRate.Default">
      <summary>Indicates the engine-specific default rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRate.ExtraSlow">
      <summary>Indicates an extra-slow rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRate.Slow">
      <summary>Indicates a slow rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRate.Medium">
      <summary>Indicates a medium rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRate.Fast">
      <summary>Indicates a fast rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRate.ExtraFast">
      <summary>Indicates an extra-fast rate.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ProsodyUnit">
      <summary>Enumerates values for the Unit property on the Prosody object.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyUnit.Default">
      <summary>Indicates the engine-specific default value.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyUnit.Hz">
      <summary>Indicates the Unit value is Hz.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyUnit.Semitone">
      <summary>Indicates the Unit value is semitone.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ProsodyVolume">
      <summary>Enumerates values for the Volume property of a Prosody object.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyVolume.Default">
      <summary>Current default volume value, same as the value returned by the <see cref="P:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.Volume" /> property on the <see cref="T:System.Speech.Synthesis.TtsEngine.ITtsEngineSite" /> site supplied to that engine.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyVolume.Silent">
      <summary>Volume off</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyVolume.ExtraSoft">
      <summary>Approximately 20% of maximum volume.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyVolume.Soft">
      <summary>Approximately 40% of maximum volume.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyVolume.Medium">
      <summary>Approximately 60% of maximum volume.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyVolume.Loud">
      <summary>Approximately 80% of maximum volume.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyVolume.ExtraLoud">
      <summary>Maximum volume.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.SayAs"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SayAs.#ctor"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SayAs.Detail"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SayAs.Format"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SayAs.InterpretAs"></member>
    <member name="T:System.Speech.Synthesis.TtsEngine.SkipInfo">
      <summary>Provides information about text stream items to be skipped.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SkipInfo.#ctor">
      <summary>Creates a new instance of the SkipInfo object.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SkipInfo.Count">
      <summary>Gets or sets the number of items to be skipped.</summary>
      <returns>An int containing the number of items.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SkipInfo.Type">
      <summary>Gets or sets the type of object to skip.</summary>
      <returns>An int representing the type of the object.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.SpeakOutputFormat">
      <summary>Enumerates the types of speech output formats.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.SpeakOutputFormat.WaveFormat">
      <summary>Indicates wave (audio) output.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.SpeakOutputFormat.Text">
      <summary>Indicates text output.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo">
      <summary>Used to specify the type of event, and its arguments (if any)  to be generated as part of the rendering of text to speech by a custom synthetic speech engine.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.#ctor(System.Int16,System.Int16,System.Int32,System.IntPtr)">
      <summary>Constucts an appropriate <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" />. </summary>
      <param name="eventId">An instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEventId" /> indicating the sort of Speech platform event the <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> object is to handle.</param>
      <param name="parameterType">An instance of <see cref="T:System.Speech.Synthesis.TtsEngine.EventParameterType" /> indicating how the System.IntPtr reference of <paramref name="param2" /> is to be interpreted, and, by implication, the use of <paramref name="param1" />.</param>
      <param name="param1">An integer value to be passed to the Speech platform when the event requested by the instance of <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> to be constructed is generated.The exact meaning of this integer is implicitly determined by the value of <paramref name="parameterType" />.</param>
      <param name="param2">A System.IntPtr instance referencing an object. to be passed to the Speech platform when the event requested by the instance of <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> to be constructed is generated.The type which must be referenced is explicitly defined by the value <paramref name="parameterType" />. The value System.IntPtr.Zero.</param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.Equals(System.Object)">
      <param name="obj"></param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.Equals(System.Speech.Synthesis.TtsEngine.SpeechEventInfo)">
      <param name="other"></param>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.EventId">
      <summary>Gets and set the Speech platform event which an instance of <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is used to request. </summary>
      <returns>Returns a member of <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEventId" /> as a short, indicating the event type the <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> object is to generate.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.GetHashCode">
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.op_Equality(System.Speech.Synthesis.TtsEngine.SpeechEventInfo,System.Speech.Synthesis.TtsEngine.SpeechEventInfo)">
      <param name="event1"></param>
      <param name="event2"></param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.op_Inequality(System.Speech.Synthesis.TtsEngine.SpeechEventInfo,System.Speech.Synthesis.TtsEngine.SpeechEventInfo)">
      <param name="event1"></param>
      <param name="event2"></param>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.Param1">
      <summary>Gets and set the integer value (<paramref name="param1" /> in the constructor) to be passed to the Speech platform to generate an event the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is used to request. </summary>
      <returns>Returns the integer to be passed to Speech platform when the event specified by the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is generated.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.Param2">
      <summary>Gets and set the System.IntPtr instance (<paramref name="param2" /> in the constructor) referencing the object to be passed to the Speech platform to generate an event the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is used to request. </summary>
      <returns>Returns the System.IntPtr referencing the object to be passed to Speech platform when the event specified by the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is generated.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.ParameterType">
      <summary>Returns the data type of the object pointed to by the IntPtr returned by the <see cref="P:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.Param2" /> parameter on the current <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> object.</summary>
      <returns>A short value corresponding to a member of the <see cref="T:System.Speech.Synthesis.TtsEngine.EventParameterType" /> enumeration and indicating the data type of the object pointed to by the IntPtr returned by the <see cref="P:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.Param2" /> parameter and used as the second argument for the constructor of the current <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> object.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.TextFragment">
      <summary>Contains text and speech attribute information for consumption by a speech synthsizer engine.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.TextFragment.#ctor">
      <summary>Constructs a new instance of TextFragment.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.TextFragment.State">
      <summary>Gets or sets speech attribute information for a TextFragment.</summary>
      <returns>A <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" /> instance is returned, or used to set speech attribute information for a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" />.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.TextFragment.TextLength">
      <summary>Gets or sets the length of the speech text in the fragment.</summary>
      <returns>An int is returned or can be used to set the length, in character, of the text string associated with this fragment to be spoken.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.TextFragment.TextOffset">
      <summary>Gets or sets the starting location of the text in the fragment.</summary>
      <returns>An int is returned or can be used to set the start location, in character, of the part of text string associated with this fragment to be spoken.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.TextFragment.TextToSpeak">
      <summary>Sets or gets the speech text of the fragment.</summary>
      <returns>A System.String is returned or can be used to set the speech text to be used by a speech synthesis engine to generate audio output.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.TtsEngineAction">
      <summary>Specifies the Speech Synthesis Markup Language (SSML) action to be taken in rendering a given TextFragment. </summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.Speak">
      <summary>Requests that the associated <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> should be processed and spoken.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.Silence">
      <summary>Indicates that a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> contains no text to be rendered as speech. </summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.Pronounce">
      <summary>Requests that input <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> text be interpreted as phonemes. </summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.Bookmark">
      <summary>Indicates that <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> is to be used as the contents of a bookmark.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.SpellOut">
      <summary>Indicates that text values provided by a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> through its <see cref="P:System.Speech.Synthesis.TtsEngine.TextFragment.TextToSpeak" /> property are to be synthesize as individual characters. </summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.StartSentence">
      <summary>Indicates start of sentence.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.StartParagraph">
      <summary>Indicates state of paragraph.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.ParseUnknownTag">
      <summary>Indicates that no action has been determined from SSML input.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.TtsEngineSsml">
      <summary>Abstract base class to be implemented by all text to speech synthesis engines.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.TtsEngineSsml.#ctor(System.String)">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEngineSsml" /> based on an appropriate Voice Token registry key.</summary>
      <param name="registryKey">Full name of the registry key for the Voice Token associated with the <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEngineSsml" /> implementation. engine. </param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.TtsEngineSsml.AddLexicon(System.Uri,System.String,System.Speech.Synthesis.TtsEngine.ITtsEngineSite)">
      <summary>Adds a lexicon to the SynthesizerVoice implemented by the current <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEngineSsml" /> instance. </summary>
      <param name="uri">A valid instance of System.Uri indicating the location of the lexicon information.</param>
      <param name="mediaType">A string containing the media type of the lexicon. Media types are case insensitive.</param>
      <param name="site">A reference to an <see cref="T:System.Speech.Synthesis.TtsEngine.ITtsEngineSite" /> interface used to interact with the platform infrastructure.</param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.TtsEngineSsml.GetOutputFormat(System.Speech.Synthesis.TtsEngine.SpeakOutputFormat,System.IntPtr)">
      <summary>Returns the best matching audio output supported by a given synthesize engine response to a request to the synthesizer engine for the support of a particular output format.</summary>
      <returns>Returns a valid IntPtr instance referring to a struct containing detailed information about the output format.</returns>
      <param name="speakOutputFormat">Valid member of the <see cref="T:System.Speech.Synthesis.TtsEngine.SpeakOutputFormat" /> enumeration indicating the type of requested audio output format.</param>
      <param name="targetWaveFormat">A pointer to a struct containing detail setting for the audio format type requested by the <paramref name="speakOutputFormat" /> argument.</param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.TtsEngineSsml.RemoveLexicon(System.Uri,System.Speech.Synthesis.TtsEngine.ITtsEngineSite)">
      <summary>Removes a lexicon currently loaded by the SynthesizerVoice implemented by the current <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEngineSsml" /> instance.</summary>
      <param name="uri">A valid instance of System.Uri indicating the location of the lexicon information.</param>
      <param name="site">A reference to an <see cref="T:System.Speech.Synthesis.TtsEngine.ITtsEngineSite" /> interface passed in by the platform infrastructure to allow access to the infrastructure resources.</param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.TtsEngineSsml.Speak(System.Speech.Synthesis.TtsEngine.TextFragment[],System.IntPtr,System.Speech.Synthesis.TtsEngine.ITtsEngineSite)">
      <summary>Renders specified <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> array in the specified output format.</summary>
      <param name="fragment">An array of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> instances containing the text to be rendered into speech.</param>
      <param name="waveHeader">An IntPtr pointing to a structure containing audio output format.</param>
      <param name="site">A reference to an <see cref="T:System.Speech.Synthesis.TtsEngine.ITtsEngineSite" /> interface passed in by the platform infrastructure to allow access to the infrastructure resources.</param>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.TtsEventId">
      <summary>Enumerates types of speech synthesis events.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.StartInputStream">
      <summary>Identifies events generated when a speech synthesize engine a begins speaking a stream.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.EndInputStream">
      <summary>Identifies events generated when a speech synthesize engine encounters the end of its input stream while speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.VoiceChange">
      <summary>Identifies events generated when a speech synthesize engine encounters a change of Voice while speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.Bookmark">
      <summary>Identifies events generated when a speech synthesize engine encounters a bookmark while speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.WordBoundary">
      <summary>Identifies events generated when a speech synthesize engine completes a word while speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.Phoneme">
      <summary>Identifies events generated when a speech synthesize engine completes a phoneme while speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.SentenceBoundary">
      <summary>Identifies events generated when a speech synthesize engine completes a sentence while speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.Viseme">
      <summary>Identifies events generated when a speech synthesize engine completes a viseme while speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.AudioLevel">
      <summary>Identifies events generated when a speech synthesize engine completes an audio level change while speaking.</summary>
    </member>
  </members>
</doc>