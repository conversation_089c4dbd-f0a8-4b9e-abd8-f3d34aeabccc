using System.Drawing;

namespace OCRTools
{
    public static class CustomColor
    {
        public static Color red = Color.FromArgb(255, 30, 16);

        public static Color yellow = Color.FromArgb(247, 187, 65);

        public static Color blue = Color.FromArgb(26, 155, 255);

        public static Color Green = Color.FromArgb(26, 173, 25);

        public static Color purple = Color.FromArgb(151, 16, 255);

        public static Color black = Color.FromArgb(77, 77, 77);

        public static Color gray = Color.FromArgb(227, 227, 227);

        public static Color CheckColor = Color.FromArgb(30, 144, 255);

        public static Color UnCheckColor = Color.FromArgb(120, 120, 120);

        public static Color Custom = Color.Transparent;
    }
}