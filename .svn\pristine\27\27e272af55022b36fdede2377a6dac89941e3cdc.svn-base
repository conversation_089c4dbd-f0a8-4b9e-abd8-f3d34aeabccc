using System;
using System.Collections;
using System.Collections.Generic;

namespace QiHe.CodeLib
{
	public class FastSearchList<T> : IList<T>
	{
		private IList<T> internalList;

		private IDictionary<T, int> internalLookup;

		public T this[int index]
		{
			get
			{
				return internalList[index];
			}
			set
			{
				internalList[index] = value;
			}
		}

		public int Count => internalList.Count;

		public bool IsReadOnly => internalList.IsReadOnly;

		public FastSearchList()
		{
			internalList = new List<T>();
			internalLookup = new Dictionary<T, int>();
		}

		public FastSearchList(int capacity)
		{
			internalList = new List<T>(capacity);
			internalLookup = new Dictionary<T, int>(capacity);
		}

		public int IndexOf(T item)
		{
			if (internalLookup.ContainsKey(item))
			{
				return internalLookup[item];
			}
			return -1;
		}

		public void Insert(int index, T item)
		{
			if (internalLookup.ContainsKey(item))
			{
				throw new ArgumentException("Duplicate item already exist in the list");
			}
			internalList.Insert(index, item);
			internalLookup.Add(item, index);
			for (int i = index; i < internalList.Count; i++)
			{
				T key = internalList[i];
				internalLookup[key] = i;
			}
		}

		public void RemoveAt(int index)
		{
			T key = internalList[index];
			internalList.RemoveAt(index);
			internalLookup.Remove(key);
			for (int i = index; i < internalList.Count; i++)
			{
				T key2 = internalList[i];
				internalLookup[key2] = i;
			}
		}

		public void Add(T item)
		{
			internalList.Add(item);
			if (!internalLookup.ContainsKey(item))
			{
				internalLookup.Add(item, internalList.Count - 1);
			}
		}

		public void Clear()
		{
			internalList.Clear();
			internalLookup.Clear();
		}

		public bool Contains(T item)
		{
			return internalLookup.ContainsKey(item);
		}

		public void CopyTo(T[] array, int arrayIndex)
		{
			internalList.CopyTo(array, arrayIndex);
		}

		public bool Remove(T item)
		{
			if (internalLookup.ContainsKey(item))
			{
				int index = internalLookup[item];
				RemoveAt(index);
				return true;
			}
			return false;
		}

		IEnumerator<T> IEnumerable<T>.GetEnumerator()
		{
			return internalList.GetEnumerator();
		}

		public IEnumerator GetEnumerator()
		{
			return internalList.GetEnumerator();
		}
	}
}
