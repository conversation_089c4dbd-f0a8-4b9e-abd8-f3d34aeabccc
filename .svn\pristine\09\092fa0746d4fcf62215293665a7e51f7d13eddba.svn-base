﻿using MetroFramework.Components;
using MetroFramework.Design;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using OCRTools;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace MetroFramework.Controls
{
    [Designer(typeof(MetroCheckBoxDesigner), typeof(ParentControlDesigner))]
    [ToolboxBitmap(typeof(CheckBox))]
    public class MetroCheckBox : CheckBox, IMetroControl
    {
        private bool _isFocused;

        private bool _isHovered;

        private bool _isPressed;

        private MetroColorStyle _metroStyle;

        private MetroThemeStyle _metroTheme;

        public MetroCheckBox()
        {
            SetStyle(ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.ResizeRedraw |
                     ControlStyles.DoubleBuffer |
                     ControlStyles.SupportsTransparentBackColor |
                     ControlStyles.UserPaint, true);
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomBackColor { get; set; }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomForeColor { get; set; }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseStyleColors { get; set; }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool DisplayFocus { get; set; }

        [DefaultValue(MetroCheckBoxSize.Small)]
        [Category("Metro Appearance")]
        public MetroCheckBoxSize FontSize { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(MetroCheckBoxWeight.Regular)]
        public MetroCheckBoxWeight FontWeight { get; set; } = MetroCheckBoxWeight.Regular;

        [Browsable(false)]
        public override Font Font
        {
            get => base.Font;
            set => base.Font = value;
        }

        [DefaultValue(MetroColorStyle.蓝色)]
        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (DesignMode || _metroStyle != 0) return _metroStyle;
                if (StyleManager != null) return StyleManager.Style;
                if (StyleManager == null) return MetroColorStyle.蓝色;
                return _metroStyle;
            }
            set => _metroStyle = value;
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (DesignMode || _metroTheme != 0) return _metroTheme;
                if (StyleManager != null) return StyleManager.Theme;
                if (StyleManager == null) return MetroThemeStyle.Light;
                return _metroTheme;
            }
            set => _metroTheme = value;
        }

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public MetroStyleManager StyleManager { get; set; }

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintBackground != null) CustomPaintBackground(this, e);
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaint != null) CustomPaint(this, e);
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintForeground != null) CustomPaintForeground(this, e);
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                var color = BackColor;
                if (!UseCustomBackColor)
                    color = MetroPaint.BackColor.Form(Theme);
                //if (Parent is MetroTile) color = MetroPaint.GetStyleColor(Style);

                if (color.A == byte.MaxValue)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            if (!Size.IsValidate())
                return;
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint)) OnPaintBackground(e);
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            Color foreColor;
            Color color;
            if (UseCustomForeColor)
            {
                foreColor = ForeColor;
                color = _isHovered && !_isPressed && Enabled ? MetroPaint.BorderColor.CheckBox.Hover(Theme) :
                    _isHovered && _isPressed && Enabled ? MetroPaint.BorderColor.CheckBox.Press(Theme) :
                    Enabled ? MetroPaint.BorderColor.CheckBox.Normal(Theme) :
                    MetroPaint.BorderColor.CheckBox.Disabled(Theme);
            }
            else if (_isHovered && !_isPressed && Enabled)
            {
                foreColor = MetroPaint.ForeColor.CheckBox.Hover(Theme);
                color = MetroPaint.BorderColor.CheckBox.Hover(Theme);
            }
            else if (_isHovered && _isPressed && Enabled)
            {
                foreColor = MetroPaint.ForeColor.CheckBox.Press(Theme);
                color = MetroPaint.BorderColor.CheckBox.Press(Theme);
            }
            else if (!Enabled)
            {
                foreColor = MetroPaint.ForeColor.CheckBox.Disabled(Theme);
                color = MetroPaint.BorderColor.CheckBox.Disabled(Theme);
            }
            else
            {
                foreColor = !UseStyleColors
                    ? MetroPaint.ForeColor.CheckBox.Normal(Theme)
                    : MetroPaint.GetStyleColor(Style);
                color = MetroPaint.BorderColor.CheckBox.Normal(Theme);
            }

            var bounds = new Rectangle(16, 0, Width - 16, Height);
            var rect = new Rectangle(0, Height / 2 - 6, 12, 12);
            using (var pen = new Pen(color))
            {
                switch (CheckAlign)
                {
                    case ContentAlignment.TopLeft:
                        rect = new Rectangle(0, 0, 12, 12);
                        break;
                    case ContentAlignment.MiddleLeft:
                        rect = new Rectangle(0, Height / 2 - 6, 12, 12);
                        break;
                    case ContentAlignment.BottomLeft:
                        rect = new Rectangle(0, Height - 13, 12, 12);
                        break;
                    case ContentAlignment.TopCenter:
                        rect = new Rectangle(Width / 2 - 6, 0, 12, 12);
                        bounds = new Rectangle(16, rect.Top + rect.Height - 5, Width - 8, Height);
                        break;
                    case ContentAlignment.BottomCenter:
                        rect = new Rectangle(Width / 2 - 6, Height - 13, 12, 12);
                        bounds = new Rectangle(16, -10, Width - 8, Height);
                        break;
                    case ContentAlignment.MiddleCenter:
                        rect = new Rectangle(Width / 2 - 6, Height / 2 - 6, 12, 12);
                        break;
                    case ContentAlignment.TopRight:
                        rect = new Rectangle(Width - 13, 0, 12, 12);
                        bounds = new Rectangle(0, 0, Width - 16, Height);
                        break;
                    case ContentAlignment.MiddleRight:
                        rect = new Rectangle(Width - 13, Height / 2 - 6, 12, 12);
                        bounds = new Rectangle(0, 0, Width - 16, Height);
                        break;
                    case ContentAlignment.BottomRight:
                        rect = new Rectangle(Width - 13, Height - 13, 12, 12);
                        bounds = new Rectangle(0, 0, Width - 16, Height);
                        break;
                }

                e.Graphics.DrawRectangle(pen, rect);
            }

            if (Checked)
            {
                var color2 = CheckState == CheckState.Indeterminate ? color : MetroPaint.GetStyleColor(Style);
                using (var brush = new SolidBrush(color2))
                {
                    var rect2 = new Rectangle(rect.Left + 2, rect.Top + 2, 9, 9);
                    e.Graphics.FillRectangle(brush, rect2);
                }
            }

            TextRenderer.DrawText(e.Graphics, Text, MetroFonts.CheckBox(FontSize, FontWeight), bounds, foreColor,
                MetroPaint.GetTextFormatFlags(TextAlign));
            OnCustomPaintForeground(new MetroPaintEventArgs(Color.Empty, foreColor, e.Graphics));
            if (DisplayFocus && _isFocused) ControlPaint.DrawFocusRectangle(e.Graphics, ClientRectangle);
        }

        protected override void OnGotFocus(EventArgs e)
        {
            _isFocused = true;
            _isHovered = true;
            Invalidate();
            base.OnGotFocus(e);
        }

        protected override void OnLostFocus(EventArgs e)
        {
            _isFocused = false;
            _isHovered = false;
            _isPressed = false;
            Invalidate();
            base.OnLostFocus(e);
        }

        protected override void OnEnter(EventArgs e)
        {
            _isFocused = true;
            _isHovered = true;
            Invalidate();
            base.OnEnter(e);
        }

        protected override void OnLeave(EventArgs e)
        {
            _isFocused = false;
            _isHovered = false;
            _isPressed = false;
            Invalidate();
            base.OnLeave(e);
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Space)
            {
                _isHovered = true;
                _isPressed = true;
                Invalidate();
            }

            base.OnKeyDown(e);
        }

        protected override void OnKeyUp(KeyEventArgs e)
        {
            Invalidate();
            base.OnKeyUp(e);
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            _isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                _isPressed = true;
                Invalidate();
            }

            base.OnMouseDown(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            _isPressed = false;
            Invalidate();
            base.OnMouseUp(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            if (!_isFocused) _isHovered = false;
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }

        protected override void OnCheckedChanged(EventArgs e)
        {
            base.OnCheckedChanged(e);
            Invalidate();
        }

        public override Size GetPreferredSize(Size proposedSize)
        {
            base.GetPreferredSize(proposedSize);
            using (var dc = CreateGraphics())
            {
                proposedSize = new Size(int.MaxValue, int.MaxValue);
                var result = TextRenderer.MeasureText(dc, Text, MetroFonts.CheckBox(FontSize, FontWeight), proposedSize,
                    MetroPaint.GetTextFormatFlags(TextAlign));
                result.Width += 16;
                if (CheckAlign != ContentAlignment.TopCenter && CheckAlign != ContentAlignment.BottomCenter)
                    return result;
                result.Height += 16;
                return result;
            }
        }
    }
}