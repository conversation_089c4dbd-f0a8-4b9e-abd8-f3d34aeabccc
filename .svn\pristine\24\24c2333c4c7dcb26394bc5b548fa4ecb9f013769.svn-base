﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace OCRTools.Common
{
    internal class CommonTask<T>
    {
        public static T GetFastestValidResult(List<TaskParam> lstPram, Func<TaskParam, T> function, int maxConcurrentTasks, int maxTimeout, Func<T, bool> isValid)
        {
            try
            {
                if (lstPram.Count == 0)
                    return default;
                if (lstPram.Count == 1)
                    return function(lstPram[0]);
                using (var cancellationTokenSource = new CancellationTokenSource(maxTimeout))
                {
                    var semaphore = new SemaphoreSlim(maxConcurrentTasks);

                    var tasks = lstPram.Select(param => Task.Run(() =>
                    {
                        return ExecuteWithSemaphore(function, param);
                    }, cancellationTokenSource.Token)).ToList();

                    T ExecuteWithSemaphore(Func<TaskParam, T> fun, TaskParam param)
                    {
                        //Console.WriteLine(ServerTime.DateTime.ToString("mm:ss fff") + " begin:" + param.Param1 + " " + param.Param2);
                        semaphore.Wait();
                        try
                        {
                            if (!cancellationTokenSource.IsCancellationRequested)
                            {
                                T result = fun(param);

                                //Console.WriteLine(ServerTime.DateTime.ToString("mm:ss fff") + " end:" + param.Param1 + " " + param.Param2 + "  " + result);
                                if (Equals(isValid, null) || isValid(result))
                                {
                                    if (!cancellationTokenSource.IsCancellationRequested)
                                        cancellationTokenSource.Cancel();
                                    return result;
                                }
                            }
                        }
                        catch { }
                        finally
                        {
                            semaphore.Release();
                        }
                        return default;
                    }

                    while (tasks.Count > 0)
                    {
                        try
                        {
                            var completedTask = Task.WhenAny(tasks).Result;
                            tasks.Remove(completedTask);

                            T result = completedTask.Result;
                            if (result != null)
                            {
                                return result;
                            }
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                    }
                }
            }
            catch { }

            return default;
        }
    }

    public class TaskParam
    {
        public string Param1 { get; set; }

        public object Param2 { get; set; }

        public object Param3 { get; set; }
    }
}
