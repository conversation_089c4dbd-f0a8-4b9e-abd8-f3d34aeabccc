<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Choose>
    <When Condition="'$(DoNotReferenceWinRT)' != 'true'">
      <!-- 
        ResolveAssemblyReference was attempting to write binding redirects
        for these assemblies (even though they are in the unification list).
      -->
      <PropertyGroup>
        <!-- ...include the reference to System.Runtime -->
        <DependsOnNETStandard>true</DependsOnNETStandard>
      </PropertyGroup>
      <ItemGroup>
        <ReferencePath Include="$(MSBuildThisFileDirectory)..\..\ref\netstandard2.0\System.Runtime.WindowsRuntime.dll">
          <!-- Private = false to make these reference only -->
          <Private>false</Private>
          <!-- given this package does not have NugetPackage metadata it will not show in solution explorer, making it explicit -->
          <Visible>false</Visible>
        </ReferencePath>
      </ItemGroup>
    </When>
  </Choose>
  <!-- when this package is referenced as a nuget reference the binding redirect is still present, add a target to remove it -->
  <Target Name="_RemoveWindowsRuntimeSuggestedRedirect" 
      BeforeTargets="GenerateBindingRedirects" 
      DependsOnTargets="ResolveAssemblyReferences"
      Condition="'$(DoNotReferenceWinRT)' != 'true'">
    <ItemGroup> 
      <SuggestedBindingRedirects Remove="System.Runtime.WindowsRuntime, Culture=neutral, PublicKeyToken=b77a5c561934e089" /> 
    </ItemGroup> 
  </Target>
</Project>
