using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace OcrLib
{
	public sealed class TextBlock
	{
		public List<Point> BoxPoints { get; set; }

		public float BoxScore { get; set; }

		public int AngleIndex { get; set; }

		public float AngleScore { get; set; }

		public float AngleTime { get; set; }

		public string Text { get; set; }

		public List<float> CharScores { get; set; }

		public float CrnnTime { get; set; }

		public float BlockTime { get; set; }

		public override string ToString()
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.AppendLine("├─TextBlock");
			string value = $"│   ├──TextBox[score({BoxScore}),[x: {BoxPoints[0].X}, y: {BoxPoints[0].Y}], [x: {BoxPoints[1].X}, y: {BoxPoints[1].Y}], [x: {BoxPoints[2].X}, y: {BoxPoints[2].Y}], [x: {BoxPoints[3].X}, y: {BoxPoints[3].Y}]]";
			stringBuilder.AppendLine(value);
			string text = ((AngleIndex >= 0) ? "Angle" : "AngleDisabled");
			string value2 = $"│   ├──{text}[Index({AngleIndex}), Score({AngleScore}), Time({AngleTime}ms)]";
			stringBuilder.AppendLine(value2);
			StringBuilder sbScores = new StringBuilder();
			CharScores.ForEach(delegate(float x)
			{
				sbScores.Append($"{x},");
			});
			string value3 = $"│   ├──TextLine[Text({Text}),CharScores({sbScores.ToString()}),Time({CrnnTime}ms)]";
			stringBuilder.AppendLine(value3);
			stringBuilder.AppendLine($"│   └──BlockTime({BlockTime}ms)");
			return stringBuilder.ToString();
		}
	}
}
