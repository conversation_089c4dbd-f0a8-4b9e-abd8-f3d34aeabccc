﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.WindowsRuntime.UI.Xaml</name>
  </assembly>
  <members>
    <member name="T:Windows.UI.Xaml.CornerRadius">
      <summary>Windows.UI.Xaml.Controls.Border 등에 적용될 수 있는 둥근 모퉁이의 특성을 설명합니다.</summary>
    </member>
    <member name="M:Windows.UI.Xaml.CornerRadius.#ctor(System.Double)">
      <summary>모든 모퉁이에 동일한 일정한 반지름을 적용하여 새 <see cref="T:Windows.UI.Xaml.CornerRadius" /> 구조체를 초기화합니다.</summary>
      <param name="uniformRadius">일정한 반지름은 네 개의 <see cref="T:Windows.UI.Xaml.CornerRadius" /> 속성(<see cref="P:Windows.UI.Xaml.CornerRadius.TopLeft" />, <see cref="P:Windows.UI.Xaml.CornerRadius.TopRight" />, <see cref="P:Windows.UI.Xaml.CornerRadius.BottomRight" />, <see cref="P:Windows.UI.Xaml.CornerRadius.BottomLeft" />) 모두에 적용됩니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.CornerRadius.#ctor(System.Double,System.Double,System.Double,System.Double)">
      <summary>모퉁이에 특정 반지름 값을 적용하여 <see cref="T:Windows.UI.Xaml.CornerRadius" /> 구조체의 새 인스턴스를 초기화합니다.</summary>
      <param name="topLeft">초기 <see cref="P:Windows.UI.Xaml.CornerRadius.TopLeft" />를 설정합니다.</param>
      <param name="topRight">초기 <see cref="P:Windows.UI.Xaml.CornerRadius.TopRight" />를 설정합니다.</param>
      <param name="bottomRight">초기 <see cref="P:Windows.UI.Xaml.CornerRadius.BottomLeft" />를 설정합니다.</param>
      <param name="bottomLeft">초기 <see cref="P:Windows.UI.Xaml.CornerRadius.BottomRight" />를 설정합니다.</param>
    </member>
    <member name="P:Windows.UI.Xaml.CornerRadius.BottomLeft">
      <summary>
        <see cref="T:Windows.UI.Xaml.CornerRadius" />가 적용된 개체의 왼쪽 아래 둥근 모퉁이의 반지름(픽셀 단위)을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.CornerRadius" />가 적용된 개체의 왼쪽 아래 둥근 모퉁이의 반지름(픽셀 단위)을 나타내는 <see cref="T:System.Double" />입니다.기본값은 0입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.CornerRadius.BottomRight">
      <summary>
        <see cref="T:Windows.UI.Xaml.CornerRadius" />가 적용된 개체의 왼쪽 아래 둥근 모퉁이의 반지름(픽셀 단위)을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.CornerRadius" />가 적용된 개체의 오른쪽 아래 둥근 모퉁이의 반지름(픽셀 단위)을 나타내는 <see cref="T:System.Double" />입니다.기본값은 0입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.CornerRadius.Equals(System.Object)">
      <summary>이 <see cref="T:Windows.UI.Xaml.CornerRadius" /> 구조체가 다른 개체와 같은지 비교합니다.</summary>
      <returns>두 개체가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">비교할 개체입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.CornerRadius.Equals(Windows.UI.Xaml.CornerRadius)">
      <summary>이 <see cref="T:Windows.UI.Xaml.CornerRadius" /> 구조체가 다른 <see cref="T:Windows.UI.Xaml.CornerRadius" /> 구조체와 같은지 비교합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.CornerRadius" />의 두 인스턴스가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="cornerRadius">같은지 비교할 <see cref="T:Windows.UI.Xaml.CornerRadius" />의 인스턴스입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.CornerRadius.GetHashCode">
      <summary>구조체의 해시 코드를 반환합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.CornerRadius" />의 해시 코드입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.CornerRadius.op_Equality(Windows.UI.Xaml.CornerRadius,Windows.UI.Xaml.CornerRadius)">
      <summary>두 <see cref="T:Windows.UI.Xaml.CornerRadius" />의 값이 같은지 비교합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.CornerRadius" />의 두 인스턴스가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="cr1">비교할 첫 번째 구조체입니다.</param>
      <param name="cr2">비교할 다른 구조체입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.CornerRadius.op_Inequality(Windows.UI.Xaml.CornerRadius,Windows.UI.Xaml.CornerRadius)">
      <summary>두 <see cref="T:Windows.UI.Xaml.CornerRadius" /> 구조체가 다른지 여부를 비교합니다. </summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.CornerRadius" /> 클래스의 두 인스턴스가 서로 다르면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="cr1">비교할 첫 번째 구조체입니다.</param>
      <param name="cr2">비교할 다른 구조체입니다.</param>
    </member>
    <member name="P:Windows.UI.Xaml.CornerRadius.TopLeft">
      <summary>
        <see cref="T:Windows.UI.Xaml.CornerRadius" />가 적용된 개체의 왼쪽 위 둥근 모퉁이의 반지름(픽셀 단위)을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.CornerRadius" />가 적용된 개체의 왼쪽 위 둥근 모퉁이의 반지름(픽셀 단위)을 나타내는 <see cref="T:System.Double" />입니다.기본값은 0입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.CornerRadius.TopRight">
      <summary>
        <see cref="T:Windows.UI.Xaml.CornerRadius" />가 적용된 개체의 오른쪽 위 둥근 모퉁이의 반지름(픽셀 단위)을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.CornerRadius" />가 적용된 개체의 오른쪽 위 둥근 모퉁이의 반지름(픽셀 단위)을 나타내는 <see cref="T:System.Double" />입니다.기본값은 0입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.CornerRadius.ToString">
      <summary>
        <see cref="T:Windows.UI.Xaml.CornerRadius" /> 구조체의 문자열 표현을 반환합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.CornerRadius" /> 값을 나타내는 <see cref="T:System.String" />입니다.</returns>
    </member>
    <member name="T:Windows.UI.Xaml.Duration">
      <summary>Windows.UI.Xaml.Media.Animation.Timeline이 활성 상태인 지속 시간을 나타냅니다.</summary>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.#ctor(System.TimeSpan)">
      <summary>제공된 <see cref="T:System.TimeSpan" /> 값을 사용하여 <see cref="T:Windows.UI.Xaml.Duration" /> 구조체의 새 인스턴스를 초기화합니다.</summary>
      <param name="timeSpan">이 기간의 초기 시간 간격을 나타냅니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="timeSpan" />이 <see cref="F:System.TimeSpan.Zero" />보다 작은 경우</exception>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.Add(Windows.UI.Xaml.Duration)">
      <summary>지정된 <see cref="T:Windows.UI.Xaml.Duration" />의 값을 이 <see cref="T:Windows.UI.Xaml.Duration" />에 추가합니다.</summary>
      <returns>관련된 각 <see cref="T:Windows.UI.Xaml.Duration" />에 값이 있을 경우 결합된 값을 나타내는 <see cref="T:Windows.UI.Xaml.Duration" />입니다.그렇지 않으면 이 메서드는 null을 반환합니다.</returns>
      <param name="duration">현재 인스턴스의 값과 <paramref name="duration" />을 더한 값을 나타내는 <see cref="T:Windows.UI.Xaml.Duration" />의 인스턴스입니다.</param>
    </member>
    <member name="P:Windows.UI.Xaml.Duration.Automatic">
      <summary>자동으로 결정되는 <see cref="T:Windows.UI.Xaml.Duration" /> 값을 가져옵니다.</summary>
      <returns>자동 값으로 초기화되는 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.Compare(Windows.UI.Xaml.Duration,Windows.UI.Xaml.Duration)">
      <summary>한 <see cref="T:Windows.UI.Xaml.Duration" /> 값을 다른 값과 비교합니다.</summary>
      <returns>
        <paramref name="t1" />이 <paramref name="t2" />보다 작은 경우 두 값 간의 차이를 나타내는 음수 값입니다.<paramref name="t1" />이 <paramref name="t2" />와 같으면 값이 0입니다.<paramref name="t1" />이 <paramref name="t2" />보다 큰 경우에는 두 값의 차를 나타내는 양수 값입니다.</returns>
      <param name="t1">비교할 첫 번째 <see cref="T:Windows.UI.Xaml.Duration" /> 인스턴스입니다.</param>
      <param name="t2">비교할 두 번째 <see cref="T:Windows.UI.Xaml.Duration" /> 인스턴스입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.Equals(System.Object)">
      <summary>지정된 개체가 <see cref="T:Windows.UI.Xaml.Duration" />과 같은지 확인합니다.</summary>
      <returns>값이 이 <see cref="T:Windows.UI.Xaml.Duration" />과 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">같은지 여부를 확인할 개체입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.Equals(Windows.UI.Xaml.Duration)">
      <summary>지정된 <see cref="T:Windows.UI.Xaml.Duration" />이 이 <see cref="T:Windows.UI.Xaml.Duration" />과 같은지 확인합니다.</summary>
      <returns>
        <paramref name="duration" />가 이 <see cref="T:Windows.UI.Xaml.Duration" />과 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="duration">같은지 확인할 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.Equals(Windows.UI.Xaml.Duration,Windows.UI.Xaml.Duration)">
      <summary>두 <see cref="T:Windows.UI.Xaml.Duration" /> 값이 서로 같은지 확인합니다.</summary>
      <returns>
        <paramref name="t1" />가 <paramref name="t2" />와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="t1">비교할 첫 번째 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
      <param name="t2">비교할 두 번째 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
    </member>
    <member name="P:Windows.UI.Xaml.Duration.Forever">
      <summary>무한 간격을 나타내는 <see cref="T:Windows.UI.Xaml.Duration" /> 값을 가져옵니다.</summary>
      <returns>무제한 값으로 초기화되는 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.GetHashCode">
      <summary>이 개체의 해시 코드를 가져옵니다.</summary>
      <returns>해시 코드 식별자입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Duration.HasTimeSpan">
      <summary>이 <see cref="T:Windows.UI.Xaml.Duration" />이 <see cref="T:System.TimeSpan" /> 값을 나타내는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.Duration" />이 <see cref="T:System.TimeSpan" /> 값이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.op_Addition(Windows.UI.Xaml.Duration,Windows.UI.Xaml.Duration)">
      <summary>두 개의 <see cref="T:Windows.UI.Xaml.Duration" /> 값을 추가합니다.</summary>
      <returns>두 <see cref="T:Windows.UI.Xaml.Duration" /> 값에 모두 <see cref="T:System.TimeSpan" /> 값이 있으면 이 메서드는 이 두 값의 합을 반환합니다.두 값 중 하나가 <see cref="P:Windows.UI.Xaml.Duration.Automatic" />으로 설정되어 있으면 메서드는 <see cref="P:Windows.UI.Xaml.Duration.Automatic" />을 반환합니다.두 값 중 하나가 <see cref="P:Windows.UI.Xaml.Duration.Forever" />으로 설정되어 있으면 메서드는 <see cref="P:Windows.UI.Xaml.Duration.Forever" />을 반환합니다.<paramref name="t1" /> 또는 <paramref name="t2" /> 중 하나에 값이 없으면 이 메서드는 null을 반환합니다.</returns>
      <param name="t1">더할 첫째 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
      <param name="t2">더할 둘째 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.op_Equality(Windows.UI.Xaml.Duration,Windows.UI.Xaml.Duration)">
      <summary>두 <see cref="T:Windows.UI.Xaml.Duration" />이 같은지 확인합니다.</summary>
      <returns>두 <see cref="T:Windows.UI.Xaml.Duration" /> 값에 동일한 속성 값이 있거나 모든 <see cref="T:Windows.UI.Xaml.Duration" /> 값이 null이면 true입니다.그렇지 않으면 이 메서드는 false를 반환합니다.</returns>
      <param name="t1">비교할 첫 번째 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
      <param name="t2">비교할 두 번째 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.op_GreaterThan(Windows.UI.Xaml.Duration,Windows.UI.Xaml.Duration)">
      <summary>
        <see cref="T:Windows.UI.Xaml.Duration" />의 한 인스턴스가 다른 인스턴스보다 큰지를 확인합니다.</summary>
      <returns>
        <paramref name="t1" /> 및 <paramref name="t2" />에 값이 있으며 <paramref name="t1" />이 <paramref name="t2" />보다 크면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="t1">비교할 <see cref="T:Windows.UI.Xaml.Duration" /> 값입니다.</param>
      <param name="t2">비교할 두 번째 <see cref="T:Windows.UI.Xaml.Duration" /> 값입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.op_GreaterThanOrEqual(Windows.UI.Xaml.Duration,Windows.UI.Xaml.Duration)">
      <summary>
        <see cref="T:Windows.UI.Xaml.Duration" />의 한 인스턴스가 다른 인스턴스보다 크거나 같은지 확인합니다.</summary>
      <returns>
        <paramref name="t1" /> 및 <paramref name="t2" />에 값이 있으며 <paramref name="t1" />이 <paramref name="t2" />보다 크거나 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="t1">비교할 첫 번째 <see cref="T:Windows.UI.Xaml.Duration" /> 인스턴스입니다.</param>
      <param name="t2">비교할 두 번째 <see cref="T:Windows.UI.Xaml.Duration" /> 인스턴스입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.op_Implicit(System.TimeSpan)~Windows.UI.Xaml.Duration">
      <summary>지정된 <see cref="T:System.TimeSpan" />에서 암시적으로 <see cref="T:Windows.UI.Xaml.Duration" />을 만듭니다.</summary>
      <returns>생성된 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</returns>
      <param name="timeSpan">암시적으로 <see cref="T:Windows.UI.Xaml.Duration" />을 만드는 데 사용할 <see cref="T:System.TimeSpan" />입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="timeSpan" />이 <see cref="F:System.TimeSpan.Zero" />보다 작은 경우</exception>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.op_Inequality(Windows.UI.Xaml.Duration,Windows.UI.Xaml.Duration)">
      <summary>두 <see cref="T:Windows.UI.Xaml.Duration" />이 같지 않은지 확인합니다.</summary>
      <returns>
        <paramref name="t1" /> 또는 <paramref name="t2" /> 중 하나만 값을 나타내거나 두 항목이 모두 다른 값을 나타내면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="t1">비교할 첫 번째 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
      <param name="t2">비교할 두 번째 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.op_LessThan(Windows.UI.Xaml.Duration,Windows.UI.Xaml.Duration)">
      <summary>
        <see cref="T:Windows.UI.Xaml.Duration" />의 한 인스턴스의 값이 다른 인스턴스의 값보다 작은지 확인합니다.</summary>
      <returns>
        <paramref name="t1" /> 및 <paramref name="t2" />에 값이 있으며 <paramref name="t1" />이 <paramref name="t2" />보다 작으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="t1">비교할 첫 번째 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
      <param name="t2">비교할 두 번째 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.op_LessThanOrEqual(Windows.UI.Xaml.Duration,Windows.UI.Xaml.Duration)">
      <summary>
        <see cref="T:Windows.UI.Xaml.Duration" />의 한 인스턴스가 다른 인스턴스보다 작거나 같은지 확인합니다.</summary>
      <returns>
        <paramref name="t1" /> 및 <paramref name="t2" />에 값이 있으며 <paramref name="t1" />이 <paramref name="t2" />보다 작거나 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="t1">비교할 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
      <param name="t2">비교할 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.op_Subtraction(Windows.UI.Xaml.Duration,Windows.UI.Xaml.Duration)">
      <summary>다른 값에서 하나의 <see cref="T:Windows.UI.Xaml.Duration" /> 값을 뺍니다.</summary>
      <returns>각 <see cref="T:Windows.UI.Xaml.Duration" />에 값이 있을 경우 <paramref name="t1" />에서 <paramref name="t2" />를 뺀 값을 나타내는 <see cref="T:Windows.UI.Xaml.Duration" />입니다.<paramref name="t1" />의 값이 <see cref="P:Windows.UI.Xaml.Duration.Forever" />이고 <paramref name="t2" />의 값이 <see cref="P:Windows.UI.Xaml.Duration.TimeSpan" />이면 이 메서드는 <see cref="P:Windows.UI.Xaml.Duration.Forever" />를 반환합니다.그렇지 않으면 이 메서드는 null을 반환합니다.</returns>
      <param name="t1">첫 번째 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
      <param name="t2">뺄 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.op_UnaryPlus(Windows.UI.Xaml.Duration)">
      <summary>지정한 <see cref="T:Windows.UI.Xaml.Duration" />을 반환합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.Duration" /> 작업 결과입니다.</returns>
      <param name="duration">가져올 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.Subtract(Windows.UI.Xaml.Duration)">
      <summary>지정된 <see cref="T:Windows.UI.Xaml.Duration" />을 이 <see cref="T:Windows.UI.Xaml.Duration" />에서 뺍니다.</summary>
      <returns>한 인스턴스의 값을 뺀 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</returns>
      <param name="duration">이 <see cref="T:Windows.UI.Xaml.Duration" />에서 뺄 <see cref="T:Windows.UI.Xaml.Duration" />입니다.</param>
    </member>
    <member name="P:Windows.UI.Xaml.Duration.TimeSpan">
      <summary>이 <see cref="T:Windows.UI.Xaml.Duration" />이 나타내는 <see cref="T:System.TimeSpan" /> 값을 가져옵니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Duration" />이 나타내는 <see cref="T:System.TimeSpan" /> 값입니다.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:Windows.UI.Xaml.Duration" />이 <see cref="T:System.TimeSpan" />을 나타내지 않는 경우</exception>
    </member>
    <member name="M:Windows.UI.Xaml.Duration.ToString">
      <summary>
        <see cref="T:Windows.UI.Xaml.Duration" />을 <see cref="T:System.String" /> 표현으로 변환합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Duration" />의 <see cref="T:System.String" /> 표현입니다.</returns>
    </member>
    <member name="T:Windows.UI.Xaml.DurationType">
      <summary>
        <see cref="T:Windows.UI.Xaml.Duration" />에 Automatic 또는 Forever의 특수 값이 있거나 해당 <see cref="T:System.TimeSpan" /> 구성 요소에 유효한 정보가 있는지 여부를 지정합니다. </summary>
    </member>
    <member name="F:Windows.UI.Xaml.DurationType.Automatic">
      <summary>"Automatic" 특수 값이 있습니다. </summary>
    </member>
    <member name="F:Windows.UI.Xaml.DurationType.Forever">
      <summary>"Forever" 특수 값이 있습니다. </summary>
    </member>
    <member name="F:Windows.UI.Xaml.DurationType.TimeSpan">
      <summary>
        <see cref="T:System.TimeSpan" /> 구성 요소에 유효한 정보가 있습니다. </summary>
    </member>
    <member name="T:Windows.UI.Xaml.GridLength">
      <summary>
        <see cref="F:Windows.UI.Xaml.GridUnitType.Star" /> 단위 형식을 명시적으로 지원하는 요소의 길이를 나타냅니다. </summary>
    </member>
    <member name="M:Windows.UI.Xaml.GridLength.#ctor(System.Double)">
      <summary>지정된 픽셀 단위 절대값을 사용하여 <see cref="T:Windows.UI.Xaml.GridLength" /> 구조체의 새 인스턴스를 초기화합니다. </summary>
      <param name="pixels">값으로 설정할 절대 픽셀 수입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.GridLength.#ctor(System.Double,Windows.UI.Xaml.GridUnitType)">
      <summary>
        <see cref="T:Windows.UI.Xaml.GridLength" /> 구조체의 새 인스턴스를 초기화하고 보유할 값의 종류를 지정합니다. </summary>
      <param name="value">
        <see cref="T:Windows.UI.Xaml.GridLength" />의 이 인스턴스의 초기 값입니다.</param>
      <param name="type">
        <see cref="T:Windows.UI.Xaml.GridLength" />의 이 인스턴스가 보유하는 <see cref="T:Windows.UI.Xaml.GridUnitType" />입니다.</param>
      <exception cref="T:System.ArgumentException">값이 0보다 작거나 숫자가 아닙니다.- 또는 -형식이 올바른 <see cref="T:Windows.UI.Xaml.GridUnitType" />이 아닙니다.</exception>
    </member>
    <member name="P:Windows.UI.Xaml.GridLength.Auto">
      <summary>콘텐츠 개체의 크기 속성에 따라 크기가 결정되는 값을 보유하는 <see cref="T:Windows.UI.Xaml.GridLength" />의 인스턴스를 가져옵니다.</summary>
      <returns>
        <see cref="P:Windows.UI.Xaml.GridLength.GridUnitType" /> 속성이 <see cref="F:Windows.UI.Xaml.GridUnitType.Auto" />로 설정된 <see cref="T:Windows.UI.Xaml.GridLength" />의 인스턴스입니다. </returns>
    </member>
    <member name="M:Windows.UI.Xaml.GridLength.Equals(System.Object)">
      <summary>지정된 개체가 현재 <see cref="T:Windows.UI.Xaml.GridLength" /> 인스턴스와 같은지 확인합니다. </summary>
      <returns>지정된 개체의 값과 <see cref="T:Windows.UI.Xaml.GridUnitType" />이 현재 인스턴스와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="oCompare">현재 인스턴스와 비교할 개체입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.GridLength.Equals(Windows.UI.Xaml.GridLength)">
      <summary>지정한 <see cref="T:Windows.UI.Xaml.GridLength" />가 현재 <see cref="T:Windows.UI.Xaml.GridLength" />와 같은지 여부를 확인합니다.</summary>
      <returns>지정된 <see cref="T:Windows.UI.Xaml.GridLength" />의 값과 <see cref="P:Windows.UI.Xaml.GridLength.GridUnitType" />이 현재 인스턴스와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="gridLength">현재 구조체와 비교할 <see cref="T:Windows.UI.Xaml.GridLength" /> 구조체입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.GridLength.GetHashCode">
      <summary>
        <see cref="T:Windows.UI.Xaml.GridLength" />의 해시 코드를 가져옵니다. </summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.GridLength" />의 해시 코드입니다. </returns>
    </member>
    <member name="P:Windows.UI.Xaml.GridLength.GridUnitType">
      <summary>
        <see cref="T:Windows.UI.Xaml.GridLength" />에 연결된 <see cref="T:Windows.UI.Xaml.GridUnitType" />을 가져옵니다. </summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.GridUnitType" /> 값 중 하나입니다.기본값은 <see cref="F:Windows.UI.Xaml.GridUnitType.Auto" />입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.GridLength.IsAbsolute">
      <summary>
        <see cref="T:Windows.UI.Xaml.GridLength" />가 픽셀 단위로 나타낸 값을 보유하는지 여부를 나타내는 값을 가져옵니다. </summary>
      <returns>
        <see cref="P:Windows.UI.Xaml.GridLength.GridUnitType" /> 속성이 <see cref="F:Windows.UI.Xaml.GridUnitType.Pixel" />이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.GridLength.IsAuto">
      <summary>
        <see cref="T:Windows.UI.Xaml.GridLength" />가 보유하는 값의 크기가 콘텐츠 개체의 크기 속성에 따라 결정되는지 여부를 나타내는 값을 가져옵니다. </summary>
      <returns>
        <see cref="P:Windows.UI.Xaml.GridLength.GridUnitType" /> 속성이 <see cref="F:Windows.UI.Xaml.GridUnitType.Auto" />이면 true이고, 그렇지 않으면 false입니다. </returns>
    </member>
    <member name="P:Windows.UI.Xaml.GridLength.IsStar">
      <summary>
        <see cref="T:Windows.UI.Xaml.GridLength" />가 가중치에 따른 여유 공간에 대한 비율로 나타낸 값을 보유하는지 여부를 나타내는 값을 가져옵니다. </summary>
      <returns>
        <see cref="P:Windows.UI.Xaml.GridLength.GridUnitType" /> 속성이 <see cref="F:Windows.UI.Xaml.GridUnitType.Star" />이면 true이고, 그렇지 않으면 false입니다. </returns>
    </member>
    <member name="M:Windows.UI.Xaml.GridLength.op_Equality(Windows.UI.Xaml.GridLength,Windows.UI.Xaml.GridLength)">
      <summary>두 <see cref="T:Windows.UI.Xaml.GridLength" /> 구조체가 같은지 여부를 비교합니다.</summary>
      <returns>두 <see cref="T:Windows.UI.Xaml.GridLength" /> 인스턴스의 값과 <see cref="T:Windows.UI.Xaml.GridUnitType" />이 서로 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="gl1">비교할 첫 번째 <see cref="T:Windows.UI.Xaml.GridLength" /> 인스턴스입니다.</param>
      <param name="gl2">비교할 두 번째 <see cref="T:Windows.UI.Xaml.GridLength" /> 인스턴스입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.GridLength.op_Inequality(Windows.UI.Xaml.GridLength,Windows.UI.Xaml.GridLength)">
      <summary>두 개의 <see cref="T:Windows.UI.Xaml.GridLength" /> 구조체를 비교하여 같지 않은지 여부를 확인합니다.</summary>
      <returns>두 <see cref="T:Windows.UI.Xaml.GridLength" /> 인스턴스의 값과 <see cref="T:Windows.UI.Xaml.GridUnitType" />이 서로 다르면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="gl1">비교할 첫 번째 <see cref="T:Windows.UI.Xaml.GridLength" /> 인스턴스입니다.</param>
      <param name="gl2">비교할 두 번째 <see cref="T:Windows.UI.Xaml.GridLength" /> 인스턴스입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.GridLength.ToString">
      <summary>
        <see cref="T:Windows.UI.Xaml.GridLength" />의 <see cref="T:System.String" /> 표현을 반환합니다.</summary>
      <returns>현재 <see cref="T:Windows.UI.Xaml.GridLength" /> 구조체의 <see cref="T:System.String" /> 표현입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.GridLength.Value">
      <summary>
        <see cref="T:Windows.UI.Xaml.GridLength" />의 값을 나타내는 <see cref="T:System.Double" />을 가져옵니다.</summary>
      <returns>현재 인스턴스의 값을 나타내는 <see cref="T:System.Double" />입니다. </returns>
    </member>
    <member name="T:Windows.UI.Xaml.GridUnitType">
      <summary>
        <see cref="T:Windows.UI.Xaml.GridLength" /> 개체가 보유하는 값의 유형을 설명합니다. </summary>
    </member>
    <member name="F:Windows.UI.Xaml.GridUnitType.Auto">
      <summary>콘텐츠 개체의 크기 속성에 따라 크기가 결정됩니다. </summary>
    </member>
    <member name="F:Windows.UI.Xaml.GridUnitType.Pixel">
      <summary>값을 픽셀 단위로 나타냅니다. </summary>
    </member>
    <member name="F:Windows.UI.Xaml.GridUnitType.Star">
      <summary>값을 사용 가능한 공간에 대한 가중치로 나타냅니다. </summary>
    </member>
    <member name="T:Windows.UI.Xaml.LayoutCycleException">
      <summary>레이아웃 주기에서 throw되는 예외입니다.</summary>
    </member>
    <member name="M:Windows.UI.Xaml.LayoutCycleException.#ctor">
      <summary>기본값으로 <see cref="T:Windows.UI.Xaml.LayoutCycleException" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:Windows.UI.Xaml.LayoutCycleException.#ctor(System.String)">
      <summary>지정된 오류 메시지를 사용하여 <see cref="T:Windows.UI.Xaml.LayoutCycleException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
    </member>
    <member name="M:Windows.UI.Xaml.LayoutCycleException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:Windows.UI.Xaml.LayoutCycleException" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="innerException">현재 예외의 원인이 되는 예외이거나, 내부 예외를 지정하지 않았으면 null입니다.</param>
    </member>
    <member name="T:Windows.UI.Xaml.Thickness">
      <summary>사각형 주위의 프레임 두께를 설명합니다.네 개의 <see cref="T:System.Double" /> 값이 사각형의 <see cref="P:Windows.UI.Xaml.Thickness.Left" />, <see cref="P:Windows.UI.Xaml.Thickness.Top" />, <see cref="P:Windows.UI.Xaml.Thickness.Right" /> 및 <see cref="P:Windows.UI.Xaml.Thickness.Bottom" /> 변을 각각 설명합니다.</summary>
    </member>
    <member name="M:Windows.UI.Xaml.Thickness.#ctor(System.Double)">
      <summary>각 변의 길이가 균일하게 지정된 <see cref="T:Windows.UI.Xaml.Thickness" /> 구조체를 초기화합니다. </summary>
      <param name="uniformLength">경계 사각형의 네 변 모두에 적용되는 균일한 길이입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Thickness.#ctor(System.Double,System.Double,System.Double,System.Double)">
      <summary>사각형의 각 변이 <see cref="T:System.Double" />로 나타낸 특정 길이로 지정된 <see cref="T:Windows.UI.Xaml.Thickness" /> 구조체를 초기화합니다. </summary>
      <param name="left">사각형 왼쪽 변의 두께입니다.</param>
      <param name="top">사각형 위쪽 변의 두께입니다.</param>
      <param name="right">사각형 오른쪽 변의 두께입니다.</param>
      <param name="bottom">사각형 아래쪽 변의 두께입니다.</param>
    </member>
    <member name="P:Windows.UI.Xaml.Thickness.Bottom">
      <summary>경계 사각형 아래쪽 변의 너비를 픽셀 단위로 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.Thickness" />의 이 인스턴스에서 경계 사각형 아래쪽 변의 너비를 픽셀 단위로 나타내는 <see cref="T:System.Double" />입니다.기본값은 0입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Thickness.Equals(System.Object)">
      <summary>이 <see cref="T:Windows.UI.Xaml.Thickness" /> 구조체가 다른 <see cref="T:System.Object" />와 같은지 비교합니다.</summary>
      <returns>두 개체가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">비교할 개체입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Thickness.Equals(Windows.UI.Xaml.Thickness)">
      <summary>이 <see cref="T:Windows.UI.Xaml.Thickness" /> 구조체가 다른 <see cref="T:Windows.UI.Xaml.Thickness" /> 구조체와 같은지 비교합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.Thickness" />의 두 인스턴스가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="thickness">같은지 비교할 <see cref="T:Windows.UI.Xaml.Thickness" />의 인스턴스입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Thickness.GetHashCode">
      <summary>구조체의 해시 코드를 반환합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.Thickness" />의 이 인스턴스에 대한 해시 코드입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Thickness.Left">
      <summary>경계 사각형 왼쪽 변의 너비를 픽셀 단위로 가져오거나 설정합니다. </summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.Thickness" />의 이 인스턴스에서 경계 사각형 왼쪽 변의 너비를 픽셀 단위로 나타내는 <see cref="T:System.Double" />입니다.기본값은 0입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Thickness.op_Equality(Windows.UI.Xaml.Thickness,Windows.UI.Xaml.Thickness)">
      <summary>두 <see cref="T:Windows.UI.Xaml.Thickness" />의 값이 같은지 비교합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.Thickness" />의 두 인스턴스가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="t1">비교할 첫 번째 구조체입니다.</param>
      <param name="t2">비교할 다른 구조체입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Thickness.op_Inequality(Windows.UI.Xaml.Thickness,Windows.UI.Xaml.Thickness)">
      <summary>두 <see cref="T:Windows.UI.Xaml.Thickness" /> 구조체가 다른지 여부를 비교합니다. </summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.Thickness" /> 클래스의 두 인스턴스가 서로 다르면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="t1">비교할 첫 번째 구조체입니다.</param>
      <param name="t2">비교할 다른 구조체입니다.</param>
    </member>
    <member name="P:Windows.UI.Xaml.Thickness.Right">
      <summary>경계 사각형 오른쪽 변의 너비를 픽셀 단위로 가져오거나 설정합니다. </summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.Thickness" />의 이 인스턴스에서 경계 사각형 오른쪽 변의 너비를 픽셀 단위로 나타내는 <see cref="T:System.Double" />입니다.기본값은 0입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Thickness.Top">
      <summary>경계 사각형 위쪽 변의 너비를 픽셀 단위로 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.Thickness" />의 이 인스턴스에서 경계 사각형 위쪽 변의 너비를 픽셀 단위로 나타내는 <see cref="T:System.Double" />입니다.기본값은 0입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Thickness.ToString">
      <summary>
        <see cref="T:Windows.UI.Xaml.Thickness" /> 구조체의 문자열 표현을 반환합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.Thickness" /> 값을 나타내는 <see cref="T:System.String" />입니다.</returns>
    </member>
    <member name="T:Windows.UI.Xaml.Automation.ElementNotAvailableException">
      <summary>사용자 인터페이스에서 더 이상 사용할 수 없는 부분에 해당하는 UI 자동화 요소에 액세스하려고 할 때 throw되는 예외입니다.</summary>
    </member>
    <member name="M:Windows.UI.Xaml.Automation.ElementNotAvailableException.#ctor">
      <summary>기본값으로 <see cref="T:Windows.UI.Xaml.Automation.ElementNotAvailableException" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:Windows.UI.Xaml.Automation.ElementNotAvailableException.#ctor(System.String)">
      <summary>지정된 오류 메시지를 사용하여 <see cref="T:Windows.UI.Xaml.Automation.ElementNotAvailableException" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="message">오류를 설명하는 메시지입니다. </param>
    </member>
    <member name="M:Windows.UI.Xaml.Automation.ElementNotAvailableException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:Windows.UI.Xaml.Automation.ElementNotAvailableException" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="message">오류를 설명하는 메시지입니다. </param>
      <param name="innerException">현재 예외의 원인이 되는 예외이거나, 내부 예외를 지정하지 않았으면 null입니다. </param>
    </member>
    <member name="T:Windows.UI.Xaml.Automation.ElementNotEnabledException">
      <summary>사용할 수 없는 컨트롤을 UI 자동화를 통해 조작하려고 할 때 throw되는 예외입니다. </summary>
    </member>
    <member name="M:Windows.UI.Xaml.Automation.ElementNotEnabledException.#ctor">
      <summary>기본값으로 <see cref="T:Windows.UI.Xaml.Automation.ElementNotEnabledException" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:Windows.UI.Xaml.Automation.ElementNotEnabledException.#ctor(System.String)">
      <summary>지정된 오류 메시지를 사용하여 <see cref="T:Windows.UI.Xaml.Automation.ElementNotEnabledException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">오류를 설명하는 메시지입니다. </param>
    </member>
    <member name="M:Windows.UI.Xaml.Automation.ElementNotEnabledException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:Windows.UI.Xaml.Automation.ElementNotEnabledException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">오류를 설명하는 메시지입니다. </param>
      <param name="innerException">현재 예외의 원인이 되는 예외이거나, 내부 예외를 지정하지 않았으면 null입니다. </param>
    </member>
    <member name="T:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition">
      <summary>
        <see cref="T:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition" />은 Windows.UI.Xaml.Controls.ItemContainerGenerator에서 관리하는 항목의 위치를 설명하는 데 사용됩니다.</summary>
    </member>
    <member name="M:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition.#ctor(System.Int32,System.Int32)">
      <summary>지정된 인덱스 및 오프셋을 사용하여 <see cref="T:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="index">생성(표시)된 항목을 기준으로 하는 <see cref="T:System.Int32" /> 인덱스입니다.-1은 항목 목록의 시작 또는 끝에 있는 가상의 항목을 참조하는 특수한 값입니다.</param>
      <param name="offset">인덱싱된 항목 근처에 있는 생성되지 않은(표시되지 않은) 항목을 기준으로 하는 <see cref="T:System.Int32" /> 오프셋입니다.오프셋이 0인 경우 인덱싱된 요소 자체를 참조하고, 1인 경우 생성되지 않은(표시되지 않은) 다음 항목을 참조하며, -1인 경우에는 이전 항목을 참조합니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition.Equals(System.Object)">
      <summary>지정된 인스턴스와 <see cref="T:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition" />의 현재 인스턴스를 비교하여 값이 같은지 확인합니다.</summary>
      <returns>
        <paramref name="o" />와 <see cref="T:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition" />의 이 인스턴스 값이 서로 같으면 true입니다.</returns>
      <param name="o">비교할 <see cref="T:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition" /> 인스턴스입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition.GetHashCode">
      <summary>이 <see cref="T:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition" />의 해시 코드를 반환합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition" />에 대한 해시 코드입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition.Index">
      <summary>생성(표시)된 항목을 기준으로 하는 <see cref="T:System.Int32" /> 인덱스를 가져오거나 설정합니다.</summary>
      <returns>생성(표시)된 항목을 기준으로 하는 <see cref="T:System.Int32" /> 인덱스입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition.Offset">
      <summary>인덱싱된 항목 근처에 있는 생성되지 않은(표시되지 않은) 항목을 기준으로 하는 <see cref="T:System.Int32" /> 오프셋을 가져오거나 설정합니다.</summary>
      <returns>인덱싱된 항목 근처에 있는 생성되지 않은(표시되지 않은) 항목을 기준으로 하는 <see cref="T:System.Int32" /> 오프셋입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition.op_Equality(Windows.UI.Xaml.Controls.Primitives.GeneratorPosition,Windows.UI.Xaml.Controls.Primitives.GeneratorPosition)">
      <summary>두 <see cref="T:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition" /> 개체를 비교하여 값이 같은지 확인합니다.</summary>
      <returns>두 개체가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="gp1">비교할 첫 번째 인스턴스입니다.</param>
      <param name="gp2">비교할 두 번째 인스턴스입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition.op_Inequality(Windows.UI.Xaml.Controls.Primitives.GeneratorPosition,Windows.UI.Xaml.Controls.Primitives.GeneratorPosition)">
      <summary>두 <see cref="T:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition" /> 개체를 비교하여 값이 다른지 확인합니다.</summary>
      <returns>값이 서로 다르면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="gp1">비교할 첫 번째 인스턴스입니다.</param>
      <param name="gp2">비교할 두 번째 인스턴스입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition.ToString">
      <summary>이 <see cref="T:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition" /> 인스턴스의 문자열 표현을 반환합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Controls.Primitives.GeneratorPosition" /> 인스턴스의 문자열 표현입니다.</returns>
    </member>
    <member name="T:Windows.UI.Xaml.Markup.XamlParseException">
      <summary>Xaml을 구문 분석하는 동안 오류가 발생한 경우 throw되는 예외입니다. </summary>
    </member>
    <member name="M:Windows.UI.Xaml.Markup.XamlParseException.#ctor">
      <summary>기본값으로 <see cref="T:Windows.UI.Xaml.Markup.XamlParseException" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:Windows.UI.Xaml.Markup.XamlParseException.#ctor(System.String)">
      <summary>지정된 오류 메시지를 사용하여 <see cref="T:Windows.UI.Xaml.Markup.XamlParseException" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
    </member>
    <member name="M:Windows.UI.Xaml.Markup.XamlParseException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:Windows.UI.Xaml.Markup.XamlParseException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="innerException">현재 예외의 원인이 되는 예외이거나, 내부 예외를 지정하지 않았으면 null입니다. </param>
    </member>
    <member name="T:Windows.UI.Xaml.Media.Matrix">
      <summary> 2차원 공간에서의 변환에 사용되는 3x3 행렬 유사 변환을 나타냅니다. </summary>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Matrix.#ctor(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double)">
      <summary>
        <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체를 초기화합니다. </summary>
      <param name="m11">
        <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 <see cref="P:Windows.UI.Xaml.Media.Matrix.M11" /> 계수입니다.</param>
      <param name="m12">
        <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 <see cref="P:Windows.UI.Xaml.Media.Matrix.M12" /> 계수입니다.</param>
      <param name="m21">
        <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 <see cref="P:Windows.UI.Xaml.Media.Matrix.M21" /> 계수입니다.</param>
      <param name="m22">
        <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 <see cref="P:Windows.UI.Xaml.Media.Matrix.M22" /> 계수입니다.</param>
      <param name="offsetX">
        <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 <see cref="P:Windows.UI.Xaml.Media.Matrix.OffsetX" /> 계수입니다.</param>
      <param name="offsetY">
        <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 <see cref="P:Windows.UI.Xaml.Media.Matrix.OffsetY" /> 계수입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Matrix.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 이 <see cref="T:Windows.UI.Xaml.Media.Matrix" />와 같은 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체인지 여부를 결정합니다. </summary>
      <returns>
        <paramref name="o" />가 이 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체와 같은 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="o">비교할 <see cref="T:System.Object" />입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Matrix.Equals(Windows.UI.Xaml.Media.Matrix)">
      <summary>지정한 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체가 이 인스턴스와 같은지 여부를 결정합니다. </summary>
      <returns>인스턴스가 서로 같으면 true이고, 그렇지 않으면 false입니다. </returns>
      <param name="value">이 인스턴스와 비교할 <see cref="T:Windows.UI.Xaml.Media.Matrix" />의 인스턴스입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Matrix.GetHashCode">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 해시 코드를 반환합니다. </summary>
      <returns>이 인스턴스의 해시 코드입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Matrix.Identity">
      <summary>항등 <see cref="T:Windows.UI.Xaml.Media.Matrix" />를 가져옵니다. </summary>
      <returns>항등 행렬입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Matrix.IsIdentity">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체가 항등 행렬인지 여부를 나타내는 값을 가져옵니다. </summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체가 항등 행렬이면 true이고, 그렇지 않으면 false입니다.기본값은 true입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Matrix.M11">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 첫 번째 행과 첫 번째 열 값을 가져오거나 설정합니다. </summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" />의 첫 번째 행과 첫 번째 열 값입니다.기본값은 1입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Matrix.M12">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 첫 번째 행과 두 번째 열 값을 가져오거나 설정합니다. </summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" />의 첫 번째 행과 두 번째 열 값입니다.기본값은 0입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Matrix.M21">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 두 번째 행과 첫 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" />의 두 번째 행과 첫 번째 열 값입니다.기본값은 0입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Matrix.M22">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 두 번째 행과 두 번째 열 값을 가져오거나 설정합니다. </summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 두 번째 행과 두 번째 열 값입니다.기본값은 1입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Matrix.OffsetX">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 세 번째 행과 첫 번째 열 값을 가져오거나 설정합니다.  </summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 세 번째 행과 첫 번째 열 값입니다.기본값은 0입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Matrix.OffsetY">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 세 번째 행과 두 번째 열 값을 가져오거나 설정합니다. </summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 세 번째 행과 두 번째 열 값입니다.기본값은 0입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Matrix.op_Equality(Windows.UI.Xaml.Media.Matrix,Windows.UI.Xaml.Media.Matrix)">
      <summary>지정한 두 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체가 같은지 여부를 결정합니다.</summary>
      <returns>
        <paramref name="matrix1" />과 <paramref name="matrix2" />가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="matrix1">비교할 첫 번째 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체입니다.</param>
      <param name="matrix2">비교할 두 번째 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Matrix.op_Inequality(Windows.UI.Xaml.Media.Matrix,Windows.UI.Xaml.Media.Matrix)">
      <summary>지정한 두 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체가 같지 않은지 여부를 결정합니다.</summary>
      <returns>
        <paramref name="matrix1" />과 <paramref name="matrix2" />가 같지 않으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="matrix1">비교할 첫 번째 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체입니다.</param>
      <param name="matrix2">비교할 두 번째 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Matrix.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>이 멤버에 대한 설명은 <see cref="M:System.IFormattable.ToString(System.String,System.IFormatProvider)" />을 참조하십시오.</summary>
      <returns>지정된 형식의 현재 인스턴스 값이 포함된 문자열입니다.</returns>
      <param name="format">사용할 형식을 지정하는 문자열입니다. 또는 IFormattable 구현 형식에 대해 정의된 기본 형식을 사용하기 위한 null입니다. </param>
      <param name="provider">값의 형식을 지정하는 데 사용할 IFormatProvider입니다. 또는 운영 체제의 현재 로캘 설정에서 숫자 형식 정보를 가져오기 위한 null입니다. </param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Matrix.ToString">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 <see cref="T:System.String" /> 표현을 만듭니다. </summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" />의 <see cref="P:Windows.UI.Xaml.Media.Matrix.M11" />, <see cref="P:Windows.UI.Xaml.Media.Matrix.M12" />, <see cref="P:Windows.UI.Xaml.Media.Matrix.M21" />, <see cref="P:Windows.UI.Xaml.Media.Matrix.M22" />, <see cref="P:Windows.UI.Xaml.Media.Matrix.OffsetX" /> 및 <see cref="P:Windows.UI.Xaml.Media.Matrix.OffsetY" /> 값이 포함된 <see cref="T:System.String" />입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Matrix.ToString(System.IFormatProvider)">
      <summary>문화권별 형식 지정 정보를 사용하여 이 <see cref="T:Windows.UI.Xaml.Media.Matrix" /> 구조체의 <see cref="T:System.String" /> 표현을 만듭니다. </summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" />의 <see cref="P:Windows.UI.Xaml.Media.Matrix.M11" />, <see cref="P:Windows.UI.Xaml.Media.Matrix.M12" />, <see cref="P:Windows.UI.Xaml.Media.Matrix.M21" />, <see cref="P:Windows.UI.Xaml.Media.Matrix.M22" />, <see cref="P:Windows.UI.Xaml.Media.Matrix.OffsetX" /> 및 <see cref="P:Windows.UI.Xaml.Media.Matrix.OffsetY" /> 값이 포함된 <see cref="T:System.String" />입니다.</returns>
      <param name="provider">문화권별 형식 지정 정보입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Matrix.Transform(Windows.Foundation.Point)">
      <summary>
        <see cref="T:Windows.UI.Xaml.Media.Matrix" />를 사용하여 지정한 점을 변환한 다음 결과를 반환합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Matrix" />를 사용하여 <paramref name="point" />를 변환한 결과입니다.</returns>
      <param name="point">변형할 점입니다.</param>
    </member>
    <member name="T:Windows.UI.Xaml.Media.Animation.KeyTime">
      <summary>애니메이션에서 특정 키 프레임이 발생할 시점을 지정합니다. </summary>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.KeyTime.Equals(System.Object)">
      <summary>
        <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />이 이 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />과 같은지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="value" />가 이 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />과 같은 시간 길이를 나타내는 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">이 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />과 비교할 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.KeyTime.Equals(Windows.UI.Xaml.Media.Animation.KeyTime)">
      <summary>지정된 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />이 이 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />과 같은지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="value" />가 이 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />과 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">이 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />과 비교할 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.KeyTime.Equals(Windows.UI.Xaml.Media.Animation.KeyTime,Windows.UI.Xaml.Media.Animation.KeyTime)">
      <summary>두 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" /> 값이 같은지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="keyTime1" />의 값과 <paramref name="keyTime2" />의 값이 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="keyTime1">비교할 첫 번째 값입니다.</param>
      <param name="keyTime2">비교할 두 번째 값입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.KeyTime.FromTimeSpan(System.TimeSpan)">
      <summary>제공된 <see cref="T:System.TimeSpan" />을 사용하여 새 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />을 만듭니다.</summary>
      <returns>
        <paramref name="timeSpan" />의 값으로 초기화된 새 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />입니다.</returns>
      <param name="timeSpan">새 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />의 값입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">지정된 <paramref name="timeSpan" />이 허용되는 범위를 벗어난 경우</exception>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.KeyTime.GetHashCode">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />을 나타내는 해시 코드를 반환합니다.</summary>
      <returns>해시 코드 식별자입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.KeyTime.op_Equality(Windows.UI.Xaml.Media.Animation.KeyTime,Windows.UI.Xaml.Media.Animation.KeyTime)">
      <summary>두 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" /> 값이 같은지 여부를 비교합니다.</summary>
      <returns>
        <paramref name="keyTime1" />과 <paramref name="keyTime2" />가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="keyTime1">비교할 첫 번째 값입니다.</param>
      <param name="keyTime2">비교할 두 번째 값입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.KeyTime.op_Implicit(System.TimeSpan)~Windows.UI.Xaml.Media.Animation.KeyTime">
      <summary>
        <see cref="P:Windows.UI.Xaml.Media.Animation.KeyTime.TimeSpan" />을 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />으로 암시적으로 변환합니다.</summary>
      <returns>만든 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />입니다.</returns>
      <param name="timeSpan">변환할 <see cref="P:Windows.UI.Xaml.Media.Animation.KeyTime.TimeSpan" /> 값입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.KeyTime.op_Inequality(Windows.UI.Xaml.Media.Animation.KeyTime,Windows.UI.Xaml.Media.Animation.KeyTime)">
      <summary>두 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" /> 값이 다른지 여부를 비교합니다.</summary>
      <returns>
        <paramref name="keyTime1" />과 <paramref name="keyTime2" />가 같지 않으면 true이고, 서로 같으면 false입니다. </returns>
      <param name="keyTime1">비교할 첫 번째 값입니다.</param>
      <param name="keyTime2">비교할 두 번째 값입니다.</param>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Animation.KeyTime.TimeSpan">
      <summary>애니메이션의 시작 부분을 기준으로 하는 시간으로 표시되는 키 프레임이 끝나는 시간을 가져옵니다.</summary>
      <returns>애니메이션의 시작 부분을 기준으로 하는 시간으로 표시되는 키 프레임이 끝나는 시간입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.KeyTime.ToString">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />의 문자열 표현을 반환합니다. </summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Animation.KeyTime" />의 문자열 표현입니다.</returns>
    </member>
    <member name="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior">
      <summary>Windows.UI.Xaml.Media.Animation.Timeline이 단순 재생 시간을 반복하는 방법을 설명합니다.</summary>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.RepeatBehavior.#ctor(System.Double)">
      <summary>지정된 반복 횟수를 사용하여 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" /> 구조체의 새 인스턴스를 초기화합니다. </summary>
      <param name="count">애니메이션의 반복 횟수를 지정하는 0보다 크거나 같은 숫자입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" />가 무한대이거나, 숫자가 아닌 값이거나, 음수인 경우</exception>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.RepeatBehavior.#ctor(System.TimeSpan)">
      <summary>지정된 반복 재생 시간을 사용하여 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" /> 구조체의 새 인스턴스를 초기화합니다. </summary>
      <param name="duration">Windows.UI.Xaml.Media.Animation.Timeline을 재생하는 총 시간(활성 재생 시간)입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="duration" />이 음수인 경우</exception>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Animation.RepeatBehavior.Count">
      <summary>Windows.UI.Xaml.Media.Animation.Timeline의 반복 횟수를 가져옵니다. </summary>
      <returns>실행할 반복 횟수입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />가 반복 횟수가 아닌 반복 재생 시간을 나타내는 경우</exception>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Animation.RepeatBehavior.Duration">
      <summary>Windows.UI.Xaml.Media.Animation.Timeline을 재생하는 총 시간을 가져옵니다. </summary>
      <returns>Timeline을 재생하는 총 시간입니다. </returns>
      <exception cref="T:System.InvalidOperationException">이 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />가 반복 재생 시간이 아닌 반복 횟수를 나타내는 경우</exception>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.RepeatBehavior.Equals(System.Object)">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />와 지정된 개체가 같은지 여부를 나타냅니다. </summary>
      <returns>
        <paramref name="value" />가 이 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />와 같은 반복 동작을 나타내는 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">해당 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />과 비교할 개체입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.RepeatBehavior.Equals(Windows.UI.Xaml.Media.Animation.RepeatBehavior)">
      <summary>지정된 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />가 이<see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />와 같은지 여부를 나타내는 값을 반환합니다. </summary>
      <returns>
        <paramref name="repeatBehavior" />의 형식 및 반복 동작이 이 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="repeatBehavior">이 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />와 비교할 개체입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.RepeatBehavior.Equals(Windows.UI.Xaml.Media.Animation.RepeatBehavior,Windows.UI.Xaml.Media.Animation.RepeatBehavior)">
      <summary>지정된 두 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" /> 값이 같은지 여부를 나타냅니다. </summary>
      <returns>
        <paramref name="repeatBehavior1" />과 <paramref name="repeatBehavior2" />의 형식 및 반복 동작이 서로 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="repeatBehavior1">비교할 첫 번째 값입니다.</param>
      <param name="repeatBehavior2">비교할 두 번째 값입니다.</param>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Animation.RepeatBehavior.Forever">
      <summary>무한 반복을 지정하는 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />를 가져옵니다.  </summary>
      <returns>무한 반복을 지정하는 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />입니다.   </returns>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.RepeatBehavior.GetHashCode">
      <summary>해당 인스턴스의 해시 코드를 반환합니다.</summary>
      <returns>해시 코드입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Animation.RepeatBehavior.HasCount">
      <summary>반복 동작에 반복 횟수가 지정되어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>인스턴스가 반복 횟수를 나타내면 true이고, 그렇지 않으면 false입니다. </returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Animation.RepeatBehavior.HasDuration">
      <summary>반복 동작에 반복 재생 시간이 지정되어 있는지 여부를 나타내는 값을 가져옵니다. </summary>
      <returns>인스턴스가 반복 재생 시간을 나타내면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.RepeatBehavior.op_Equality(Windows.UI.Xaml.Media.Animation.RepeatBehavior,Windows.UI.Xaml.Media.Animation.RepeatBehavior)">
      <summary>지정된 두 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" /> 값이 같은지 여부를 나타냅니다. </summary>
      <returns>
        <paramref name="repeatBehavior1" />과 <paramref name="repeatBehavior2" />의 형식 및 반복 동작이 서로 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="repeatBehavior1">비교할 첫 번째 값입니다.</param>
      <param name="repeatBehavior2">비교할 두 번째 값입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.RepeatBehavior.op_Inequality(Windows.UI.Xaml.Media.Animation.RepeatBehavior,Windows.UI.Xaml.Media.Animation.RepeatBehavior)">
      <summary>두 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" /> 값이 같지 않은지 여부를 나타냅니다. </summary>
      <returns>
        <paramref name="repeatBehavior1" />과 <paramref name="repeatBehavior2" />의 형식이 서로 다르거나 반복 동작 속성이 다르면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="repeatBehavior1">비교할 첫 번째 값입니다.</param>
      <param name="repeatBehavior2">비교할 두 번째 값입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.RepeatBehavior.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>이 멤버에 대한 설명은 <see cref="M:System.IFormattable.ToString(System.String,System.IFormatProvider)" />을 참조하십시오.</summary>
      <returns>지정된 형식의 현재 인스턴스 값이 포함된 문자열입니다.</returns>
      <param name="format">사용할 형식을 지정하는 문자열이거나, IFormattable 구현 형식에 대해 정의된 기본 형식을 사용하려면 null입니다. </param>
      <param name="formatProvider">값의 형식을 지정하는 데 사용할 IFormatProvider이거나, 운영 체제의 현재 로캘 설정에서 숫자 형식 정보를 가져오려면 null입니다. </param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.RepeatBehavior.ToString">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />의 문자열 표현을 반환합니다. </summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />의 문자열 표현입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Animation.RepeatBehavior.ToString(System.IFormatProvider)">
      <summary>지정된 형식을 사용하여 이 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />의 문자열 표현을 반환합니다. </summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />의 문자열 표현입니다.</returns>
      <param name="formatProvider">반환 값을 생성하는 데 사용되는 형식입니다.</param>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Animation.RepeatBehavior.Type">
      <summary>동작을 반복하는 방식을 설명하는 <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehaviorType" /> 값 중 하나를 가져오거나 설정합니다. </summary>
      <returns>반복 동작의 형식입니다. </returns>
    </member>
    <member name="T:Windows.UI.Xaml.Media.Animation.RepeatBehaviorType">
      <summary>
        <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" /> 원시 값이 나타내는 반복 모드를 지정합니다. </summary>
    </member>
    <member name="F:Windows.UI.Xaml.Media.Animation.RepeatBehaviorType.Count">
      <summary>
        <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />가 타임라인에서 전체 실행 횟수를 반복해야 하는 경우를 나타냅니다. </summary>
    </member>
    <member name="F:Windows.UI.Xaml.Media.Animation.RepeatBehaviorType.Duration">
      <summary>
        <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />가 타임라인에서 지정된 기간 동안 반복해야 하는 경우를 나타냅니다. 이 기간 동안 일부에서 애니메이션이 종료 될 수 있습니다. </summary>
    </member>
    <member name="F:Windows.UI.Xaml.Media.Animation.RepeatBehaviorType.Forever">
      <summary>
        <see cref="T:Windows.UI.Xaml.Media.Animation.RepeatBehavior" />에서 타임라인을 무기한 반복해야 하는 사례를 나타냅니다. </summary>
    </member>
    <member name="T:Windows.UI.Xaml.Media.Media3D.Matrix3D">
      <summary>3D(3차원) 공간에서의 변환에 사용되는 4x4 매트릭스를 나타냅니다.</summary>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Media3D.Matrix3D.#ctor(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double)">
      <summary>
        <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="m11">새 매트릭스의 (1,1) 필드에 대한 값입니다.</param>
      <param name="m12">새 매트릭스의 (1,2) 필드에 대한 값입니다.</param>
      <param name="m13">새 매트릭스의 (1,3) 필드에 대한 값입니다.</param>
      <param name="m14">새 매트릭스의 (1,4) 필드에 대한 값입니다.</param>
      <param name="m21">새 매트릭스의 (2,1) 필드에 대한 값입니다.</param>
      <param name="m22">새 매트릭스의 (2,2) 필드에 대한 값입니다.</param>
      <param name="m23">새 매트릭스의 (2,3) 필드에 대한 값입니다.</param>
      <param name="m24">새 매트릭스의 (2,4) 필드에 대한 값입니다.</param>
      <param name="m31">새 매트릭스의 (3,1) 필드에 대한 값입니다.</param>
      <param name="m32">새 매트릭스의 (3,2) 필드에 대한 값입니다.</param>
      <param name="m33">새 매트릭스의 (3,3) 필드에 대한 값입니다.</param>
      <param name="m34">새 매트릭스의 (3,4) 필드에 대한 값입니다.</param>
      <param name="offsetX">새 매트릭스의 X 오프셋 필드에 대한 값입니다.</param>
      <param name="offsetY">새 매트릭스의 Y 오프셋 필드에 대한 값입니다.</param>
      <param name="offsetZ">새 매트릭스의 Z 오프셋 필드에 대한 값입니다.</param>
      <param name="m44">새 매트릭스의 (4,4) 필드에 대한 값입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Media3D.Matrix3D.Equals(System.Object)">
      <summary>두 매트릭스가 일치하는지 여부를 테스트합니다.</summary>
      <returns>매트릭스가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="o">같은지 여부를 테스트할 개체입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Media3D.Matrix3D.Equals(Windows.UI.Xaml.Media.Media3D.Matrix3D)">
      <summary>두 매트릭스가 일치하는지 여부를 테스트합니다.</summary>
      <returns>매트릭스가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">비교할 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Media3D.Matrix3D.GetHashCode">
      <summary>이 매트릭스의 해시 코드를 반환합니다.</summary>
      <returns>이 매트릭스의 해시 코드를 지정하는 정수입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.HasInverse">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />를 반전할 수 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />에 역이 있으면 true이고, 그렇지 않으면 false입니다.기본값은 true입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.Identity">
      <summary>
        <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" /> 구조체를 항등 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />로 변경합니다.</summary>
      <returns>항등 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Media3D.Matrix3D.Invert">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" /> 구조체를 반전합니다.</summary>
      <exception cref="T:System.InvalidOperationException">매트릭스를 반전할 수 없는 경우</exception>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.IsIdentity">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" /> 구조체가 항등 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />가 항등 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />이면 true이고, 그렇지 않으면 false입니다.기본값은 true입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.M11">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 첫 번째 행과 첫 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" /> 구조체의 첫 번째 행과 첫 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.M12">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 첫 번째 행과 두 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 첫 번째 행과 두 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.M13">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 첫 번째 행과 세 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 첫 번째 행과 세 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.M14">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 첫 번째 행과 네 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 첫 번째 행과 네 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.M21">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 두 번째 행과 첫 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 두 번째 행과 첫 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.M22">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 두 번째 행과 두 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 두 번째 행과 두 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.M23">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 두 번째 행과 세 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 두 번째 행과 세 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.M24">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 두 번째 행과 네 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 두 번째 행과 네 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.M31">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 세 번째 행과 첫 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 세 번째 행과 첫 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.M32">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 세 번째 행과 두 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 세 번째 행과 두 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.M33">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 세 번째 행과 세 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 세 번째 행과 세 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.M34">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 세 번째 행과 네 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 세 번째 행과 네 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.M44">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 네 번째 행과 네 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 네 번째 행과 네 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.OffsetX">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 네 번째 행과 첫 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 네 번째 행과 첫 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.OffsetY">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 네 번째 행과 두 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 네 번째 행과 두 번째 열 값입니다.</returns>
    </member>
    <member name="P:Windows.UI.Xaml.Media.Media3D.Matrix3D.OffsetZ">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 네 번째 행과 세 번째 열 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 네 번째 행과 세 번째 열 값입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Media3D.Matrix3D.op_Equality(Windows.UI.Xaml.Media.Media3D.Matrix3D,Windows.UI.Xaml.Media.Media3D.Matrix3D)">
      <summary>두 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" /> 인스턴스를 비교하여 완전히 같은지 여부를 확인합니다.</summary>
      <returns>매트릭스가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="matrix1">비교할 첫 번째 매트릭스입니다.</param>
      <param name="matrix2">비교할 두 번째 매트릭스입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Media3D.Matrix3D.op_Inequality(Windows.UI.Xaml.Media.Media3D.Matrix3D,Windows.UI.Xaml.Media.Media3D.Matrix3D)">
      <summary>두 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" /> 인스턴스가 다른지 비교합니다.</summary>
      <returns>매트릭스가 다르면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="matrix1">비교할 첫 번째 매트릭스입니다.</param>
      <param name="matrix2">비교할 두 번째 매트릭스입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Media3D.Matrix3D.op_Multiply(Windows.UI.Xaml.Media.Media3D.Matrix3D,Windows.UI.Xaml.Media.Media3D.Matrix3D)">
      <summary>지정된 매트릭스를 곱합니다.</summary>
      <returns>곱셈 결과에 해당하는 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />입니다.</returns>
      <param name="matrix1">곱할 매트릭스입니다.</param>
      <param name="matrix2">첫 번째 매트릭스와 곱하는 매트릭스입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Media3D.Matrix3D.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>이 멤버에 대한 설명은 <see cref="M:System.IFormattable.ToString" />을 참조하십시오.</summary>
      <returns>지정된 형식의 현재 인스턴스 값입니다.</returns>
      <param name="format">사용할 형식입니다.</param>
      <param name="provider">사용할 공급자입니다.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Media3D.Matrix3D.ToString">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 문자열 표현을 만듭니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 문자열 표현입니다.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Media.Media3D.Matrix3D.ToString(System.IFormatProvider)">
      <summary>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 문자열 표현을 만듭니다.</summary>
      <returns>이 <see cref="T:Windows.UI.Xaml.Media.Media3D.Matrix3D" />의 문자열 표현입니다.</returns>
      <param name="provider">문화권별 형식 지정 정보입니다.</param>
    </member>
  </members>
</doc>