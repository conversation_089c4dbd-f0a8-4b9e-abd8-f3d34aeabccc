﻿#region License Information (GPL v3)

/*
    OCRTools - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2020 OCRTools Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)

using MetroFramework;
using MetroFramework.Components;
using MetroFramework.Drawing;
using OCRTools.Common;
using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class TaskThumbnailPanel : UserControl
    {
        private Rectangle dragBoxFromMouseDown;

        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        private bool selected;

        private Size thumbnailSize;

        private string title;

        private ThumbnailTitleLocation titleLocation;

        private bool titleVisible = true;

        public TaskThumbnailPanel(HistoryTask task)
        {
            Task = task;

            InitializeComponent();
            UpdateTheme();
            UpdateTitle();
        }

        public HistoryTask Task { get; }

        public bool Selected
        {
            get => selected;
            set
            {
                if (selected != value)
                {
                    selected = value;

                    pThumbnail.Selected = selected;
                }
            }
        }

        public string Title
        {
            get => title;
            set
            {
                title = value;

                if (lblTitle.Text != title) lblTitle.Text = title;
            }
        }

        public bool TitleVisible
        {
            get => titleVisible;
            set
            {
                if (titleVisible != value)
                {
                    titleVisible = value;
                    lblTitle.Visible = titleVisible;
                    UpdateLayout();
                }
            }
        }

        public ThumbnailTitleLocation TitleLocation
        {
            get => titleLocation;
            set
            {
                if (titleLocation != value)
                {
                    titleLocation = value;
                    pThumbnail.StatusLocation = value;
                    UpdateLayout();
                }
            }
        }

        public bool ThumbnailExists { get; private set; }

        public Size ThumbnailSize
        {
            get => thumbnailSize;
            set
            {
                if (thumbnailSize != value)
                {
                    thumbnailSize = value;

                    UpdateLayout();
                }
            }
        }

        public bool ThumbnailSupportsClick { get; private set; }

        [Category("Metro Appearance")]
        [DefaultValue(MetroColorStyle.Blue)]
        public MetroColorStyle Style
        {
            get
            {
                if (DesignMode || metroStyle != 0) return metroStyle;
                if (StyleManager != null) return StyleManager.Style;
                if (StyleManager == null) return MetroColorStyle.Blue;
                return metroStyle;
            }
            set => metroStyle = value;
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (DesignMode || metroTheme != 0) return metroTheme;
                if (StyleManager != null) return StyleManager.Theme;
                if (StyleManager == null) return MetroThemeStyle.Light;
                return metroTheme;
            }
            set => metroTheme = value;
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public MetroStyleManager StyleManager { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor { get; set; }

        public new event EventHandler MouseEnter
        {
            add
            {
                base.MouseEnter += value;
                lblTitle.MouseEnter += value;
                pThumbnail.MouseEnter += value;
                pbThumbnail.MouseEnter += value;
            }
            remove
            {
                base.MouseEnter -= value;
                lblTitle.MouseEnter -= value;
                pThumbnail.MouseEnter -= value;
                pbThumbnail.MouseEnter -= value;
            }
        }

        public new event MouseEventHandler MouseDown
        {
            add
            {
                base.MouseDown += value;
                lblTitle.MouseDown += value;
                pThumbnail.MouseDown += value;
                pbThumbnail.MouseDown += value;
            }
            remove
            {
                base.MouseDown -= value;
                lblTitle.MouseDown -= value;
                pThumbnail.MouseDown -= value;
                pbThumbnail.MouseDown -= value;
            }
        }

        public new event MouseEventHandler MouseUp
        {
            add
            {
                base.MouseUp += value;
                lblTitle.MouseUp += value;
                pThumbnail.MouseUp += value;
                pbThumbnail.MouseUp += value;
            }
            remove
            {
                base.MouseUp -= value;
                lblTitle.MouseUp -= value;
                pThumbnail.MouseUp -= value;
                pbThumbnail.MouseUp -= value;
            }
        }

        public void UpdateTheme()
        {
            // 设置主题背景色
            // 设置主题背景色
            if (!UseCustomBackColor)
            {
                lblTitle.ForeColor = MetroPaint.ForeColor.Button.Normal(Theme);
                lblTitle.TextShadowColor = Color.Transparent;
                pThumbnail.PanelColor = MetroPaint.BackColor.Button.Normal(Theme);
                ttMain.BackColor = MetroPaint.BackColor.Button.Hover(Theme);
                ttMain.ForeColor = MetroPaint.ForeColor.Button.Press(Theme);
            }
            else
            {
                lblTitle.ForeColor = SystemColors.ControlText;
                lblTitle.TextShadowColor = Color.Transparent;
                pThumbnail.PanelColor = SystemColors.ControlLight;
                ttMain.BackColor = SystemColors.Window;
                ttMain.ForeColor = SystemColors.ControlText;
            }
        }

        public void UpdateTitle()
        {
            Title = Task.Info?.FileName;

            if (Task.Info != null && !string.IsNullOrEmpty(Task.Info.ToString()))
            {
                lblTitle.Cursor = Cursors.Hand;
                ttMain.SetToolTip(lblTitle, Task.Info.ToString());
            }
            else
            {
                lblTitle.Cursor = Cursors.Default;
                ttMain.SetToolTip(lblTitle, null);
            }
        }

        private void UpdateLayout()
        {
            lblTitle.Width = pThumbnail.Padding.Horizontal + ThumbnailSize.Width;
            pThumbnail.Size = new Size(pThumbnail.Padding.Horizontal + ThumbnailSize.Width,
                pThumbnail.Padding.Vertical + ThumbnailSize.Height);
            var panelHeight = pThumbnail.Height;
            if (TitleVisible) panelHeight += lblTitle.Height + 2;
            Size = new Size(pThumbnail.Width, panelHeight);

            if (TitleLocation == ThumbnailTitleLocation.Top)
            {
                lblTitle.Location = new Point(0, 0);

                if (TitleVisible)
                    pThumbnail.Location = new Point(0, lblTitle.Height + 2);
                else
                    pThumbnail.Location = new Point(0, 0);

                lblError.Location = new Point((ClientSize.Width - lblError.Width) / 2, 1);
            }
            else
            {
                pThumbnail.Location = new Point(0, 0);
                lblTitle.Location = new Point(0, pThumbnail.Height + 2);
                lblError.Location = new Point((ClientSize.Width - lblError.Width) / 2,
                    pThumbnail.Height - lblError.Height - 1);
            }
        }

        public void UpdateThumbnail(Bitmap bmp = null)
        {
            ClearThumbnail();

            if (!ThumbnailSize.IsEmpty && Task.Info != null)
                try
                {
                    var filePath = Task.Info.FilePath;

                    if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                    {
                        ThumbnailSupportsClick = true;
                        pbThumbnail.Cursor = Cursors.Hand;
                    }

                    var bmpResult = CreateThumbnail(filePath, bmp);

                    if (bmpResult != null)
                    {
                        pbThumbnail.Image = bmpResult;

                        ThumbnailExists = true;
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
        }

        private Bitmap CreateThumbnail(string filePath, Bitmap bmp = null)
        {
            if (bmp != null) return ImageProcessHelper.ResizeImage(bmp, ThumbnailSize, false);

            if (string.IsNullOrEmpty(filePath)) filePath = Task.Info.FileName;
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                using (var bmpResult = ImageProcessHelper.LoadImage(filePath))
                {
                    if (bmpResult != null) return ImageProcessHelper.ResizeImage(bmpResult, ThumbnailSize, false);
                }

            return null;
        }

        public void UpdateStatus()
        {
            if (Task.Info != null)
            {
                pThumbnail.UpdateStatusColor(Task.Status);
                lblError.Visible = Task.Status == TaskStatus.Failed;
            }

            UpdateTitle();
        }

        public void ClearThumbnail()
        {
            var temp = pbThumbnail.Image;
            pbThumbnail.Image = null;

            if (temp != null && temp != pbThumbnail.ErrorImage && temp != pbThumbnail.InitialImage) temp.Dispose();

            ThumbnailSupportsClick = false;
            pbThumbnail.Cursor = Cursors.Default;

            ThumbnailExists = false;
        }

        private void LblTitle_MouseClick(object sender, MouseEventArgs e)
        {
            if (ModifierKeys == Keys.None && e.Button == MouseButtons.Left) MouseClickEvent(true);
        }

        private void lblError_MouseClick(object sender, MouseEventArgs e)
        {
            if (ModifierKeys == Keys.None && e.Button == MouseButtons.Left)
            {
                //if (!string.IsNullOrEmpty(Task?.ErrorMsg))
                //    CommonMethod.ShowHelpMsg(Task?.ErrorMsg);
            }
        }

        private void PbThumbnail_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                var dragSize = new Size(10, 10);
                dragBoxFromMouseDown = new Rectangle(new Point(e.X - dragSize.Width / 2, e.Y - dragSize.Height / 2),
                    dragSize);
            }
        }

        private void PbThumbnail_MouseUp(object sender, MouseEventArgs e)
        {
            dragBoxFromMouseDown = Rectangle.Empty;
        }

        private void PbThumbnail_MouseClick(object sender, MouseEventArgs e)
        {
            if (ThumbnailSupportsClick && ModifierKeys == Keys.None && e.Button == MouseButtons.Left)
                MouseClickEvent(false);
        }

        public Image GetImage()
        {
            Image image = null;
            if (!string.IsNullOrEmpty(Task.Info.URL))
                try
                {
                    image = Image.FromStream(new MemoryStream(Convert.FromBase64String(Task.Info.URL)));
                }
                catch
                {
                }
            else if (!string.IsNullOrEmpty(Task.Info.FilePath) && File.Exists(Task.Info.FilePath))
                try
                {
                    image = Image.FromFile(Task.Info.FilePath);
                }
                catch
                {
                }

            return image;
        }

        public void ViewImage()
        {
            try
            {
                var bitmap = GetImage();
                this.ViewImage(bitmap);
            }
            catch
            {
            }
        }

        public void OpenUrl()
        {
            if (!string.IsNullOrEmpty(Task.Info.URL))
                try
                {
                    Process.Start(Task.Info.URL);
                }
                catch
                {
                }
        }

        public void OpenFile(bool isOpenLocation)
        {
            if (!string.IsNullOrEmpty(Task.Info.FilePath) && File.Exists(Task.Info.FilePath))
            {
                if (isOpenLocation)
                    CommonMethod.OpenFolderWithFile(Task.Info.FilePath);
                else
                    CommonMethod.OpenFile(Task.Info.FilePath);
            }
        }

        private void MouseClickEvent(bool isTitle)
        {
            if (Task.Info != null)
                switch (Task.Info.DataType)
                {
                    case EDataType.Image:
                        ViewImage();
                        break;
                    case EDataType.Text:
                        break;
                    case EDataType.URL:
                        OpenUrl();
                        break;
                    default:
                        OpenFile(isTitle);
                        break;
                }
        }

        private void PbThumbnail_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && dragBoxFromMouseDown != Rectangle.Empty &&
                !dragBoxFromMouseDown.Contains(e.X, e.Y))
            {
                if (Task.Info != null && !string.IsNullOrEmpty(Task.Info.FilePath) && File.Exists(Task.Info.FilePath))
                {
                    IDataObject dataObject = new DataObject(DataFormats.FileDrop, new[] {Task.Info.FilePath});
                    dragBoxFromMouseDown = Rectangle.Empty;
                    pbThumbnail.DoDragDrop(dataObject, DragDropEffects.Copy | DragDropEffects.Move);
                }
                else
                {
                    dragBoxFromMouseDown = Rectangle.Empty;
                }
            }
        }

        private void TtMain_Draw(object sender, DrawToolTipEventArgs e)
        {
            e.DrawBackground();
            e.DrawBorder();
            e.DrawText();
        }
    }

    public class HistoryTask
    {
        public HistoryItem Info { get; set; }
        public TaskStatus Status { get; set; }

        public string ErrorMsg { get; set; }
    }

    public enum TaskStatus
    {
        InQueue,
        Preparing,
        Working,
        Stopping,
        Stopped,
        Failed,
        Completed,
        History
    }

    public class HistoryItem
    {
        private string filePath;

        public string FilePath
        {
            get => filePath;
            set
            {
                filePath = value;

                if (string.IsNullOrEmpty(filePath))
                    FileName = "";
                else
                    FileName = Path.GetFileName(filePath);
            }
        }

        public string FileName { get; set; }

        public string URL { get; set; }

        public EDataType DataType { get; set; }

        public DateTime CreateTime { get; set; }

        public override string ToString()
        {
            return string.IsNullOrEmpty(FilePath) ? string.IsNullOrEmpty(FileName) ? URL : FileName : FilePath;
        }
    }

    public enum EDataType
    {
        Default,
        File,
        Image,
        Text,
        URL
    }
}