﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class MyPictureBox : UserControl
    {
        private readonly object _imageLoadLock = new object();

        private bool _drawCheckeredBackground;

        private bool _isImageLoading;

        private string _text;

        public MyPictureBox()
        {
            InitializeComponent();
            Text = "";
            UpdateTheme();
            UpdateImageSizeLabel();
        }

        public Image Image
        {
            get => pbMain.Image;
            private set => pbMain.Image = value;
        }

        [EditorBrowsable(EditorBrowsableState.Always)]
        [Browsable(true)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        [Bindable(true)]
        public override string Text
        {
            get => _text;
            set
            {
                _text = value;

                if (string.IsNullOrEmpty(value))
                {
                    lblStatus.Visible = false;
                }
                else
                {
                    lblStatus.Text = value;
                    lblStatus.Visible = true;
                }
            }
        }

        public Color PictureBoxBackColor
        {
            get => pbMain.BackColor;
            set => pbMain.BackColor = value;
        }

        [DefaultValue(false)]
        public bool DrawCheckeredBackground
        {
            get => _drawCheckeredBackground;
            set
            {
                _drawCheckeredBackground = value;
                UpdateCheckers();
            }
        }

        [DefaultValue(false)] public bool FullscreenOnClick { get; set; }

        [DefaultValue(false)] public bool EnableRightClickMenu { get; set; }

        [DefaultValue(false)] public bool ShowImageSizeLabel { get; set; }

        public bool IsValidImage => !_isImageLoading && pbMain.IsValidImage();

        public new event MouseEventHandler MouseDown
        {
            add
            {
                pbMain.MouseDown += value;
                lblStatus.MouseDown += value;
            }
            remove
            {
                pbMain.MouseDown -= value;
                lblStatus.MouseDown -= value;
            }
        }

        public new event MouseEventHandler MouseUp
        {
            add
            {
                pbMain.MouseUp += value;
                lblStatus.MouseUp += value;
            }
            remove
            {
                pbMain.MouseUp -= value;
                lblStatus.MouseUp -= value;
            }
        }

        public new event MouseEventHandler MouseClick
        {
            add
            {
                pbMain.MouseClick += value;
                lblStatus.MouseClick += value;
            }
            remove
            {
                pbMain.MouseClick -= value;
                lblStatus.MouseClick -= value;
            }
        }

        private void UpdateImageSizeLabel()
        {
            lblImageSize.Location = new Point((ClientSize.Width - lblImageSize.Width) / 2,
                ClientSize.Height - lblImageSize.Height + 1);
        }

        public void UpdateTheme()
        {
            lblImageSize.BackColor = SystemColors.Window;
            lblImageSize.ForeColor = SystemColors.ControlText;
        }

        public void UpdateCheckers(bool forceUpdate = false)
        {
            if (pbMain.BackgroundImage != null) pbMain.BackgroundImage.Dispose();
            pbMain.BackgroundImage = null;
        }

        public void LoadImage(Image img)
        {
            lock (_imageLoadLock)
            {
                if (!_isImageLoading)
                {
                    Reset();
                    _isImageLoading = true;
                    Image = (Image)img.Clone();
                    _isImageLoading = false;
                    AutoSetSizeMode();
                }
            }
        }

        public void Reset()
        {
            if (!_isImageLoading && Image != null)
            {
                Image temp = null;

                try
                {
                    temp = Image;
                    Image = null;
                }
                finally
                {
                    // If error happened in previous image load then PictureBox set image as error image and if we dispose it then error happens
                    if (temp != null && temp != pbMain.ErrorImage && temp != pbMain.InitialImage) temp.Dispose();
                }
            }

            if (FullscreenOnClick && Cursor != Cursors.Default) Cursor = Cursors.Default;
        }

        private void AutoSetSizeMode()
        {
            if (IsValidImage)
            {
                lblImageSize.Text = $"{Image.Width} x {Image.Height}";

                if (Image.Width > pbMain.ClientSize.Width || Image.Height > pbMain.ClientSize.Height)
                    pbMain.SizeMode = PictureBoxSizeMode.Zoom;
                else
                    pbMain.SizeMode = PictureBoxSizeMode.CenterImage;

                if (FullscreenOnClick) Cursor = Cursors.Hand;
            }

            UpdateImageSizeLabel();
            PbMain_MouseMove(null, null);
        }

        private void PbMain_Resize(object sender, EventArgs e)
        {
            UpdateCheckers();
            AutoSetSizeMode();
        }

        private void PbMain_LoadCompleted(object sender, AsyncCompletedEventArgs e)
        {
            lblStatus.Visible = false;
            _isImageLoading = false;
            if (e.Error == null) AutoSetSizeMode();
        }

        private void PbMain_LoadProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            if (_isImageLoading && e.ProgressPercentage < 100) Text = string.Format("加载图像：{0}%", e.ProgressPercentage);
        }

        private void PbMain_MouseDown(object sender, MouseEventArgs e)
        {
            if (FullscreenOnClick && e.Button == MouseButtons.Left && IsValidImage)
            {
                pbMain.Enabled = false;
                //ImageViewer.ShowImage(Image);
                pbMain.Enabled = true;
            }
        }

        private void PbMain_MouseUp(object sender, MouseEventArgs e)
        {
            if (EnableRightClickMenu && e.Button == MouseButtons.Right && IsValidImage)
                cmsMenu.Show(pbMain, e.X + 1, e.Y + 1);
        }

        private void PbMain_MouseMove(object sender, MouseEventArgs e)
        {
            lblImageSize.Visible = ShowImageSizeLabel && IsValidImage &&
                                   !new Rectangle(lblImageSize.Location, lblImageSize.Size).Contains(e.Location);
        }

        private void PbMain_MouseLeave(object sender, EventArgs e)
        {
            lblImageSize.Visible = false;
        }

        private void tsmiCopyImage_Click(object sender, EventArgs e)
        {
            if (IsValidImage) ClipboardService.ClipSetImage(Image, false);
        }
    }
}