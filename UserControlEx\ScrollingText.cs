using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Reflection;
using System.Windows.Forms;
using OCRTools.Properties;

namespace OCRTools
{
    /// <summary>
    /// 走马灯文本控件
    /// </summary>
    public class ScrollingText : Control
    {
        #region 私有字段
        
        private Timer _animationTimer;
        private System.Diagnostics.Stopwatch _performanceTimer;
        
        // 动画相关
        private float _textPosition;
        private float _scrollSpeed = 60.0f; // 像素/秒
        private bool _isScrolling = true;
        private bool _isPaused = false;
        
        // 文本和缓存
        private string _text = "Enhanced Scrolling Text";
        private SizeF _textSize;
        private bool _textSizeValid = false;
        
        // 双缓冲优化
        private Bitmap _backBuffer;
        private Graphics _backGraphics;
        private bool _bufferValid = false;
        
        // 样式设置
        private bool _showBorder = false;
        
        // 交互设置
        private bool _pauseOnHover = true;
        private bool _enableTextClick = false;
        private string _linkUrl = string.Empty;

        // 原始ScrollingText的功能
        private PictureBox _picClose;
        private bool _isCanClose = false;
        private bool _close = false;
        private Control _closeOwner;
        private VerticleTextPosition _verticleTextPosition = VerticleTextPosition.Center;
        private Brush _foregroundBrush;
        private Brush _backgroundBrush;
        private int _loopTimes = 0;
        private int _maxLoopTimes = 0;
        private bool _stopScrollOnMouseOver = false;
        private RectangleF _lastKnownRect;

        // 性能优化
        private Rectangle _lastInvalidateRect = Rectangle.Empty;
        private bool _useOptimizedRendering = true;
        private float _yPos; // 文本的Y位置
        
        #endregion
        
        #region 构造函数和初始化
        
        public ScrollingText()
        {
            InitializeControl();
            InitializeAnimation();
            InitializeDoubleBuffering();
        }
        
        private void InitializeControl()
        {
            // 设置控件样式以获得最佳性能
            SetStyle(ControlStyles.AllPaintingInWmPaint |
                     ControlStyles.UserPaint |
                     ControlStyles.DoubleBuffer |
                     ControlStyles.ResizeRedraw |
                     ControlStyles.SupportsTransparentBackColor |
                     ControlStyles.OptimizedDoubleBuffer, true);

            Size = new Size(300, 30);
            Font = new Font("Microsoft YaHei", 9F, FontStyle.Regular);

            // 初始化关闭按钮
            InitializeCloseButton();
        }

        private void InitializeCloseButton()
        {
            _picClose = new PictureBox
            {
                Cursor = Cursors.Hand,
                Visible = false,
                Image = Resources.Full_close_hover,
                BackColor = Color.Transparent,
                SizeMode = PictureBoxSizeMode.StretchImage
            };

            _picClose.MouseDown += PicClose_MouseDown;
            SizeChanged += OnSizeChanged_CloseButton;
            Controls.Add(_picClose);
        }

        private void OnSizeChanged_CloseButton(object sender, EventArgs e)
        {
            if (_picClose?.Image != null)
            {
                var width = Math.Min(Height, _picClose.Image.Height);
                _picClose.Size = new Size(width, width);
                _picClose.Location = new Point(Width - _picClose.Width, 0);
            }
        }

        private void PicClose_MouseDown(object sender, MouseEventArgs e)
        {
            _closeOwner = Parent;
            _close = true;
            Visible = false;
        }
        
        private void InitializeAnimation()
        {
            _performanceTimer = System.Diagnostics.Stopwatch.StartNew();
            
            _animationTimer = new Timer
            {
                Interval = 16, // ~60 FPS
                Enabled = true
            };
            _animationTimer.Tick += OnAnimationTick;
            
            ResetTextPosition();
        }
        
        private void InitializeDoubleBuffering()
        {
            CreateBackBuffer();
        }
        
        #endregion
        
        #region 动画逻辑
        
        private void OnAnimationTick(object sender, EventArgs e)
        {
            // 处理关闭按钮显示逻辑
            if (_isCanClose && _picClose != null)
                _picClose.Visible = RectangleToScreen(ClientRectangle).Contains(MousePosition);

            if (!_isScrolling || _isPaused || string.IsNullOrEmpty(_text))
                return;

            UpdateTextPosition();

            if (_useOptimizedRendering)
                InvalidateTextRegion();
            else
                Invalidate();
        }
        
        private void UpdateTextPosition()
        {
            var deltaTime = _performanceTimer.Elapsed.TotalSeconds;
            _performanceTimer.Restart();
            
            if (!_textSizeValid)
                CalculateTextSize();
            
            // 计算移动距离
            var movement = (float)(_scrollSpeed * deltaTime);
            var oldPosition = _textPosition;
            _textPosition -= movement;
            
            // 检查是否需要重置位置
            if (_textPosition + _textSize.Width < 0)
            {
                // 调整初始位置：从1/3位置开始，而不是最右边
                _textPosition = Width / 3.0f;
                _loopTimes++;
                _bufferValid = false; // 重置时需要重绘整个缓冲区

                // 检查最大循环次数
                if (_maxLoopTimes > 0 && _loopTimes >= _maxLoopTimes)
                {
                    _isScrolling = false;
                }
            }
            
            // 标记缓冲区需要更新
            if (Math.Abs(_textPosition - oldPosition) > 0.1f)
            {
                _bufferValid = false;
            }
        }
        
        private void InvalidateTextRegion()
        {
            if (!_textSizeValid) return;
            
            // 计算需要刷新的区域
            var textRect = new Rectangle(
                (int)Math.Floor(_textPosition - 2),
                (int)Math.Floor(_yPos - 2),
                (int)Math.Ceiling(_textSize.Width + 4),
                (int)Math.Ceiling(_textSize.Height + 4)
            );
            
            // 扩展到包含上一次的位置
            if (!_lastInvalidateRect.IsEmpty)
                textRect = Rectangle.Union(textRect, _lastInvalidateRect);
            
            // 确保在控件范围内
            textRect.Intersect(ClientRectangle);
            
            if (!textRect.IsEmpty)
            {
                Invalidate(textRect);
                _lastInvalidateRect = textRect;
            }
        }
        
        #endregion
        
        #region 绘制逻辑
        
        protected override void OnPaint(PaintEventArgs e)
        {
            if (_useOptimizedRendering && _backBuffer != null)
            {
                // 使用双缓冲绘制
                DrawToBackBuffer();
                e.Graphics.DrawImage(_backBuffer, 0, 0);
            }
            else
            {
                // 直接绘制
                DrawDirect(e.Graphics);
            }
            
            base.OnPaint(e);
        }
        
        private void DrawToBackBuffer()
        {
            if (_backGraphics == null || !_bufferValid)
            {
                CreateBackBuffer();

                // 清除背景
                if (_backgroundBrush != null)
                    _backGraphics.FillRectangle(_backgroundBrush, 0, 0, Width, Height);
                else
                    _backGraphics.Clear(BackColor);

                // 绘制边框
                if (_showBorder)
                {
                    using (var pen = new Pen(BorderColor))
                    {
                        _backGraphics.DrawRectangle(pen, 0, 0, Width - 1, Height - 1);
                    }
                }

                // 绘制文本
                DrawText(_backGraphics);

                _bufferValid = true;
            }
        }
        
        private void DrawDirect(Graphics g)
        {
            // 设置渲染质量
            g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
            g.SmoothingMode = SmoothingMode.HighQuality;
            g.InterpolationMode = InterpolationMode.HighQualityBilinear;
            g.CompositingQuality = CompositingQuality.HighQuality;

            // 清除背景
            if (_backgroundBrush != null)
                g.FillRectangle(_backgroundBrush, 0, 0, Width, Height);
            else
                g.Clear(BackColor);

            // 绘制边框
            if (_showBorder)
            {
                using (var pen = new Pen(BorderColor))
                {
                    g.DrawRectangle(pen, 0, 0, Width - 1, Height - 1);
                }
            }

            // 绘制文本
            DrawText(g);
        }
        
        private void DrawText(Graphics g)
        {
            if (string.IsNullOrEmpty(_text)) return;

            if (!_textSizeValid)
                CalculateTextSize();

            // 计算垂直位置
            CalcVerticalPosition(_textSize);

            // 绘制文本
            if (_foregroundBrush != null)
            {
                g.DrawString(_text, Font, _foregroundBrush, _textPosition, _yPos);
            }
            else
            {
                using (var brush = new SolidBrush(ForeColor))
                {
                    g.DrawString(_text, Font, brush, _textPosition, _yPos);
                }
            }

            // 更新文本区域用于鼠标交互
            _lastKnownRect = new RectangleF(_textPosition, _yPos, _textSize.Width, _textSize.Height);
            EnableTextLink(_lastKnownRect);
        }

        /// <summary>
        /// 计算滚动文本的垂直位置
        /// </summary>
        /// <param name="stringSize">文本字符串的大小</param>
        private void CalcVerticalPosition(SizeF stringSize)
        {
            switch (_verticleTextPosition)
            {
                case VerticleTextPosition.Top:
                    _yPos = 2;
                    break;
                case VerticleTextPosition.Center:
                    _yPos = Height * 1.0f / 2 - stringSize.Height / 2;
                    break;
                case VerticleTextPosition.Botom:
                    _yPos = Height - stringSize.Height;
                    break;
            }
        }

        /// <summary>
        /// 处理文本链接和鼠标交互
        /// </summary>
        private void EnableTextLink(RectangleF textRect)
        {
            if (!_stopScrollOnMouseOver && !_pauseOnHover)
            {
                if (Cursor != Cursors.Default)
                    Cursor = Cursors.Default;
                return;
            }

            var curPt = PointToClient(Cursor.Position);

            if (textRect.Contains(curPt))
            {
                // 当用户鼠标悬停在文本上时停止文本滚动
                if (_stopScrollOnMouseOver && !_isPaused)
                {
                    _isPaused = true;
                    // 重要：暂停时重置计时器，避免时间累积
                    _performanceTimer.Restart();
                }

                if (_enableTextClick)
                    Cursor = Cursors.Hand;
            }
            else
            {
                if (_stopScrollOnMouseOver && _isPaused)
                {
                    _isPaused = false;
                    // 重要：恢复时重置计时器，避免跳跃
                    _performanceTimer.Restart();
                }

                // 确保当用户鼠标不在文本上时文本继续滚动
                Cursor = Cursors.Default;
            }
        }
        
        #endregion
        
        #region 辅助方法
        
        private void CalculateTextSize()
        {
            if (string.IsNullOrEmpty(_text))
            {
                _textSize = SizeF.Empty;
                _textSizeValid = true;
                return;
            }
            
            using (var g = CreateGraphics())
            {
                g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
                _textSize = g.MeasureString(_text, Font);
            }
            _textSizeValid = true;
        }
        
        private void CreateBackBuffer()
        {
            if (Width <= 0 || Height <= 0) return;
            
            _backGraphics?.Dispose();
            _backBuffer?.Dispose();
            
            _backBuffer = new Bitmap(Width, Height);
            _backGraphics = Graphics.FromImage(_backBuffer);
            _backGraphics.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
            _backGraphics.SmoothingMode = SmoothingMode.HighQuality;
            
            _bufferValid = false;
        }
        
        private void ResetTextPosition()
        {
            // 调整初始位置：从1/3位置开始
            _textPosition = Width / 3.0f;
            _loopTimes = 0;
            _bufferValid = false;
            _lastInvalidateRect = Rectangle.Empty;
        }
        
        private void InvalidateTextCache()
        {
            _textSizeValid = false;
            _bufferValid = false;
            _lastInvalidateRect = Rectangle.Empty;
        }
        
        #endregion
        
        #region 事件处理
        
        protected override void OnSizeChanged(EventArgs e)
        {
            base.OnSizeChanged(e);
            CreateBackBuffer();
            ResetTextPosition();
        }
        
        protected override void OnFontChanged(EventArgs e)
        {
            base.OnFontChanged(e);
            InvalidateTextCache();
        }
        
        protected override void OnMouseEnter(EventArgs e)
        {
            base.OnMouseEnter(e);
            if (_pauseOnHover)
            {
                _isPaused = true;
                // 重要：暂停时重置计时器，避免时间累积
                _performanceTimer.Restart();
                if (_enableTextClick)
                    Cursor = Cursors.Hand;
            }
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            base.OnMouseLeave(e);
            if (_pauseOnHover)
            {
                _isPaused = false;
                // 重要：恢复时重置计时器，避免跳跃
                _performanceTimer.Restart();
                Cursor = Cursors.Default;
            }
        }
        
        protected override void OnClick(EventArgs e)
        {
            base.OnClick(e);

            // 如果用户在鼠标悬停在文本上时点击，则触发文本点击事件
            if (Cursor == Cursors.Hand)
                OnTextClicked(this, EventArgs.Empty);
        }

        /// <summary>
        /// 文本点击事件处理
        /// </summary>
        private void OnTextClicked(object sender, EventArgs args)
        {
            // 调用委托
            if (TextClicked != null)
            {
                TextClicked(sender, args);
            }
            else
            {
                if (string.IsNullOrEmpty(_linkUrl)) return;
                try
                {
                    CommonMethod.DetermineCall(this, () =>
                    {
                        new FrmViewUrl
                        {
                            Url = StrLink,
                            WindowState = FormWindowState.Maximized,
                            StartPosition = FormStartPosition.CenterScreen,
                            Icon = FrmMain.FrmTool.Icon,
                            Text = _text
                        }.ShowDialog(this);
                    });
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
            }
        }
        
        #endregion
        
        #region 资源清理
        
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _animationTimer?.Dispose();
                _backGraphics?.Dispose();
                _backBuffer?.Dispose();
                _performanceTimer?.Stop();

                // 清理画刷资源
                if (_foregroundBrush != null)
                    _foregroundBrush.Dispose();
                if (_backgroundBrush != null)
                    _backgroundBrush.Dispose();

                // 清理关闭按钮
                _picClose?.Dispose();
            }
            base.Dispose(disposing);
        }
        
        #endregion

        #region 事件和委托

        public delegate void TextClickEventHandler(object sender, EventArgs args);
        public event TextClickEventHandler TextClicked;

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取或设置滚动的文本内容
        /// </summary>
        public string ScrollText
        {
            get => _text;
            set
            {
                if (_text != value)
                {
                    _text = value ?? string.Empty;
                    InvalidateTextCache();
                    ResetTextPosition();
                    Invalidate();
                }
            }
        }

        /// <summary>
        /// 决定控件重绘频率的计时器间隔
        /// </summary>
        public int TextScrollSpeed
        {
            set => _animationTimer.Interval = value;
            get => _animationTimer.Interval;
        }

        /// <summary>
        /// 获取或设置滚动速度（像素/秒）
        /// </summary>
        public float ScrollSpeed
        {
            get => _scrollSpeed;
            set => _scrollSpeed = Math.Max(1, value);
        }

        /// <summary>
        /// 获取或设置是否启用滚动
        /// </summary>
        public bool IsScrolling
        {
            get => _isScrolling;
            set
            {
                if (_isScrolling != value)
                {
                    _isScrolling = value;
                    if (!value) _isPaused = false;
                }
            }
        }

        /// <summary>
        /// 获取或设置鼠标悬停时是否暂停滚动
        /// </summary>
        public bool PauseOnHover
        {
            get => _pauseOnHover;
            set => _pauseOnHover = value;
        }

        /// <summary>
        /// 获取或设置是否显示边框
        /// </summary>
        public bool ShowBorder
        {
            get => _showBorder;
            set
            {
                if (_showBorder != value)
                {
                    _showBorder = value;
                    _bufferValid = false;
                    Invalidate();
                }
            }
        }

        /// <summary>
        /// 边框的颜色
        /// </summary>
        public Color BorderColor { set; get; } = Color.Black;

        /// <summary>
        /// 获取或设置是否启用文本点击功能
        /// </summary>
        public bool EnableTextClick
        {
            get => _enableTextClick;
            set => _enableTextClick = value;
        }

        /// <summary>
        /// 获取或设置点击文本时打开的链接URL
        /// </summary>
        public string LinkUrl
        {
            get => _linkUrl;
            set => _linkUrl = value ?? string.Empty;
        }

        /// <summary>
        /// 获取或设置是否使用优化渲染（推荐开启）
        /// </summary>
        public bool UseOptimizedRendering
        {
            get => _useOptimizedRendering;
            set
            {
                if (_useOptimizedRendering != value)
                {
                    _useOptimizedRendering = value;
                    _bufferValid = false;
                    Invalidate();
                }
            }
        }

        /// <summary>
        /// 文本的垂直对齐方式
        /// </summary>
        public VerticleTextPosition NowVerticleTextPosition
        {
            get => _verticleTextPosition;
            set
            {
                if (_verticleTextPosition != value)
                {
                    _verticleTextPosition = value;
                    _bufferValid = false;
                    Invalidate();
                }
            }
        }

        /// <summary>
        /// 此文本的链接URL
        /// </summary>
        public string StrLink
        {
            get => _linkUrl;
            set => _linkUrl = value ?? string.Empty;
        }

        /// <summary>
        /// 决定当用户鼠标移动到文本上时是否停止滚动
        /// </summary>
        public bool StopScrollOnMouseOver
        {
            get => _stopScrollOnMouseOver;
            set => _stopScrollOnMouseOver = value;
        }

        /// <summary>
        /// 指示控件是否启用
        /// </summary>
        public new bool Enabled
        {
            set
            {
                _animationTimer.Enabled = value;
                base.Enabled = value;
            }
            get => base.Enabled;
        }

        /// <summary>
        /// 前景画刷
        /// </summary>
        public Brush ForegroundBrush
        {
            get => _foregroundBrush;
            set
            {
                if (_foregroundBrush != value)
                {
                    _foregroundBrush = value;
                    _bufferValid = false;
                    Invalidate();
                }
            }
        }

        /// <summary>
        /// 背景画刷
        /// </summary>
        public Brush BackgroundBrush
        {
            get => _backgroundBrush;
            set
            {
                if (_backgroundBrush != value)
                {
                    _backgroundBrush = value;
                    _bufferValid = false;
                    Invalidate();
                }
            }
        }

        /// <summary>
        /// 循环次数
        /// </summary>
        public int LoopTimes
        {
            get => _loopTimes;
            internal set => _loopTimes = value;
        }

        /// <summary>
        /// 最大循环次数
        /// </summary>
        public int MaxLoopTimes
        {
            get => _maxLoopTimes;
            internal set => _maxLoopTimes = value;
        }

        /// <summary>
        /// 是否可以关闭
        /// </summary>
        public bool IsCanClose
        {
            get => _isCanClose;
            set => _isCanClose = value;
        }

        /// <summary>
        /// 关闭状态
        /// </summary>
        public bool Close
        {
            get => _close;
            set => _close = value;
        }

        /// <summary>
        /// 关闭时的拥有者控件
        /// </summary>
        public Control CloseOwner
        {
            get => _closeOwner;
            set => _closeOwner = value;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 开始滚动动画
        /// </summary>
        public void StartScrolling()
        {
            _isScrolling = true;
            _isPaused = false;
            // 重置计时器避免跳跃
            _performanceTimer?.Restart();
        }

        /// <summary>
        /// 停止滚动动画
        /// </summary>
        public void StopScrolling()
        {
            _isScrolling = false;
            _isPaused = false;
        }

        /// <summary>
        /// 暂停滚动动画
        /// </summary>
        public void PauseScrolling()
        {
            _isPaused = true;
            // 重置计时器避免时间累积
            _performanceTimer?.Restart();
        }

        /// <summary>
        /// 恢复滚动动画
        /// </summary>
        public void ResumeScrolling()
        {
            _isPaused = false;
            // 重置计时器避免跳跃
            _performanceTimer?.Restart();
        }

        /// <summary>
        /// 重置文本位置到起始位置
        /// </summary>
        public void ResetPosition()
        {
            ResetTextPosition();
            Invalidate();
        }

        #endregion
    }

    /// <summary>
    /// 文本垂直位置枚举
    /// </summary>

    public enum VerticleTextPosition
    {
        Top,
        Center,
        Botom
    }

    /// <summary>
    /// 滚动实体类
    /// </summary>

    [Obfuscation]
    public class ScrollEntity
    {
        [Obfuscation] public string LnkUrl { get; set; } = string.Empty;

        [Obfuscation] public Color ForeColor { get; set; } = Color.Black;

        [Obfuscation] public string Text { get; set; } = string.Empty;
    }
}
