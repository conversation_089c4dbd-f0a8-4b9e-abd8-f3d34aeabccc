using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolRectangleFill : ToolObject
    {
        private DrawRectangleFill drawRectangleFill;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.current_ToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                drawRectangleFill = new DrawRectangleFill(e.X, e.Y, 1, 1)
                {
                    BackgroundImageEx = drawArea.BackgroundImageEx
                };
                AddNewObject(drawArea, drawRectangleFill);
            }
        }

        protected new void AddNewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.Add(o);
            drawArea.Capture = true;
            o.Selected = true;
            drawArea.Refresh();
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (drawRectangleFill == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                drawRectangleFill.IsSelected = true;
                var obj = drawRectangleFill;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    drawRectangleFill.MoveHandleTo(e.Location, 5, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawRectangleFill != null)
            {
                StaticValue.current_Rectangle = drawRectangleFill.Rectangle;
                drawRectangleFill.IsCache = true;
                if (!drawRectangleFill.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var obj = drawRectangleFill;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    drawRectangleFill.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(drawRectangleFill));
                }
            }
        }
    }
}