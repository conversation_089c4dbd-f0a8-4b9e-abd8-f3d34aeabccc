﻿using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using OCRTools;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace MetroFramework.Components
{
    [ToolboxBitmap(typeof(ToolTip))]
    public class MetroToolTip : ToolTip, IMetroComponent
    {
        private MetroColorStyle _metroStyle = MetroCommonStyle.DefaultStyle;

        private MetroThemeStyle _metroTheme = MetroThemeStyle.Light;

        public MetroToolTip()
        {
            OwnerDraw = true;
            ShowAlways = true;
            Draw += MetroToolTip_Draw;
            Popup += MetroToolTip_Popup;
        }

        [DefaultValue(true)]
        [Browsable(false)]
        public new bool ShowAlways
        {
            get => base.ShowAlways;
            set => base.ShowAlways = true;
        }

        [Browsable(false)]
        [DefaultValue(true)]
        public new bool OwnerDraw
        {
            get => base.OwnerDraw;
            set => base.OwnerDraw = true;
        }

        [Browsable(false)]
        public new Color BackColor
        {
            get => base.BackColor;
            set => base.BackColor = value;
        }

        [Browsable(false)]
        public new Color ForeColor
        {
            get => base.ForeColor;
            set => base.ForeColor = value;
        }

        [Browsable(false)]
        public new string ToolTipTitle
        {
            get => base.ToolTipTitle;
            set => base.ToolTipTitle = "";
        }

        [Browsable(false)]
        public new ToolTipIcon ToolTipIcon
        {
            get => base.ToolTipIcon;
            set => base.ToolTipIcon = ToolTipIcon.None;
        }

        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (StyleManager != null) return StyleManager.Style;
                return _metroStyle;
            }
            set => _metroStyle = value;
        }

        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (StyleManager != null) return StyleManager.Theme;
                return _metroTheme;
            }
            set => _metroTheme = value;
        }

        [Browsable(false)] public MetroStyleManager StyleManager { get; set; }

        public new void SetToolTip(Control control, string caption)
        {
            base.SetToolTip(control, caption);
            if (control is IMetroControl)
                foreach (Control control2 in control.Controls)
                    SetToolTip(control2, caption);
        }

        private void MetroToolTip_Popup(object sender, PopupEventArgs e)
        {
            if (e.AssociatedWindow is IMetroForm form)
            {
                StyleManager = CommonTheme.StyleManager;
            }
            else if (e.AssociatedControl is IMetroControl control)
            {
                StyleManager = CommonTheme.StyleManager;
            }

            e.ToolTipSize = new Size(e.ToolTipSize.Width + 24, e.ToolTipSize.Height + 9);
        }

        private void MetroToolTip_Draw(object sender, DrawToolTipEventArgs e)
        {
            e.Graphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;
            var theme = Theme == MetroThemeStyle.Light ? MetroThemeStyle.Dark : MetroThemeStyle.Light;
            var backColor = MetroPaint.BackColor.Form(theme);
            var borderColor = MetroPaint.BorderColor.Button.Normal(theme);
            var foreColor = MetroPaint.ForeColor.Label.Normal(theme);
            using (var brush = new SolidBrush(backColor))
            {
                e.Graphics.FillRectangle(brush, e.Bounds);
            }

            using (var pen = new Pen(borderColor))
            {
                e.Graphics.DrawRectangle(pen,
                    new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width - 1, e.Bounds.Height - 1));
            }

            var font = CommonString.GetSysNormalFont(13f);
            TextRenderer.DrawText(e.Graphics, e.ToolTipText, font, e.Bounds, foreColor,
                TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
        }
    }
}