using System.Collections.Generic;
using System.Drawing;

namespace ExcelLibrary.BinaryFileFormat
{
	public class ColorPalette
	{
		public Dictionary<int, Color> Palette = new Dictionary<int, Color>();

		public Color this[int index]
		{
			get
			{
				if (Palette.ContainsKey(index))
				{
					return Palette[index];
				}
				return Color.White;
			}
			set
			{
				Palette[index] = value;
			}
		}

		public ColorPalette()
		{
			Palette.Add(0, Color.Black);
			Palette.Add(1, Color.White);
			Palette.Add(2, Color.Red);
			Palette.Add(3, Color.Green);
			Palette.Add(4, Color.Blue);
			Palette.Add(5, Color.Yellow);
			Palette.Add(6, Color.Magenta);
			Palette.Add(7, Color.Cyan);
			Palette.Add(8, Color.FromArgb(0, 0, 0));
			Palette.Add(9, Color.FromArgb(255, 255, 255));
			Palette.Add(10, Color.FromArgb(255, 0, 0));
			Palette.Add(31, Color.FromArgb(204, 204, 255));
			Palette.Add(56, Color.FromArgb(0, 51, 102));
			Palette.Add(57, Color.FromArgb(51, 153, 102));
			Palette.Add(58, Color.FromArgb(0, 51, 0));
			Palette.Add(59, Color.FromArgb(51, 51, 0));
			Palette.Add(60, Color.FromArgb(153, 51, 0));
			Palette.Add(61, Color.FromArgb(153, 51, 102));
			Palette.Add(62, Color.FromArgb(51, 51, 153));
			Palette.Add(63, Color.FromArgb(51, 51, 51));
			Palette.Add(64, SystemColors.Window);
			Palette.Add(65, SystemColors.WindowText);
			Palette.Add(67, SystemColors.WindowFrame);
			Palette.Add(77, SystemColors.ControlText);
			Palette.Add(78, SystemColors.Control);
			Palette.Add(79, Color.Black);
			Palette.Add(80, SystemColors.Info);
			Palette.Add(81, SystemColors.InfoText);
			Palette.Add(32767, SystemColors.WindowText);
		}
	}
}
