﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.DirectoryServices.Protocols</name>
  </assembly>
  <members>
    <member name="T:System.DirectoryServices.Protocols.AddRequest">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.AddRequest" /> class adds an entry to the directory.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.AddRequest.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.AddRequest.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.AddRequest" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.AddRequest.#ctor(System.String,System.DirectoryServices.Protocols.DirectoryAttribute[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.AddRequest.#ctor(System.String,System.DirectoryServices.Protocols.DirectoryAttribute[])" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.AddRequest" /> class using the specified distinguished name and attributes.</summary>
      <param name="distinguishedName">The <paramref name="distinguishedName" /> of the new object in the directory.</param>
      <param name="attributes">An array, of  <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> objects, that contains the attributes for this object. This parameter may be null.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.AddRequest.#ctor(System.String,System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.AddRequest.#ctor(System.String,System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.AddRequest" /> class using the specified <paramref name="distinguishedName" /> and the object class.</summary>
      <param name="distinguishedName">The <paramref name="distinguishedName" /> of the new object in the directory.</param>
      <param name="objectClass">The object class for this object. If this parameter is null, an exception is thrown.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="objectClass" /> parameter contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.AddRequest.Attributes">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.AddRequest.Attributes" /> property contains a <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeCollection" /> of attribute-value pairs for the object.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeCollection" /> of attribute-value pairs for the object.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.AddRequest.DistinguishedName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.AddRequest.DistinguishedName" /> property contains the distinguished name of the object.</summary>
      <returns>The distinguished name of the object.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.AddRequest.ToXmlNode(System.Xml.XmlDocument)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.AddRequest.ToXmlNode(System.Xml.XmlDocument)" /> method creates an XML node from the specified  <see cref="T:System.Xml.XmlDocument" /> object.</summary>
      <returns>The resulting <see cref="T:System.Xml.XmlElement" /> object.</returns>
      <param name="doc">A <see cref="T:System.Xml.XmlDocument" /> object.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.AddResponse">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.AddResponse" /> class is returned by <see cref="M:System.DirectoryServices.Protocols.DirectoryConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest)" /> as a response to <see cref="T:System.DirectoryServices.Protocols.AddRequest" />.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.AsqRequestControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.AsqRequestControl" /> class creates an attribute scoped query (ASQ) control. This control is used with a search request to force the query to be based on a specific DN-valued attribute.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.AsqRequestControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.AsqRequestControl.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.AsqRequestControl" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.AsqRequestControl.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.AsqRequestControl.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.AsqRequestControl" /> class using the specified attribute name.</summary>
      <param name="attributeName">The attribute name on which the query is based.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.AsqRequestControl.AttributeName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.AsqRequestControl.AttributeName" /> property gets or sets the attribute name on which the search query is based.</summary>
      <returns>The attribute name on which the search query is based.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.AsqRequestControl.GetValue">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.AsqRequestControl.GetValue" /> method returns the binary representation of the <see cref="T:System.DirectoryServices.Protocols.AsqRequestControl" />.</summary>
      <returns>The binary representation of the <see cref="T:System.DirectoryServices.Protocols.AsqRequestControl" />.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.AsqResponseControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.AsqResponseControl" /> class is returned in a <see cref="T:System.DirectoryServices.Protocols.SearchResponse" /> object as a response to a <see cref="T:System.DirectoryServices.Protocols.AsqRequestControl" /> query.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.AsqResponseControl.Result">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.AsqResponseControl.Result" /> property gets a <see cref="T:System.DirectoryServices.Protocols.ResultCode" /> object with result data.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.ResultCode" /> object with result data.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.AuthType">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.AuthType" /> enumeration is used to specify the authentication method to use on a connection.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.AuthType.Anonymous">
      <summary>Indicates that the connection should be made without passing credentials. The value is equal to 0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.AuthType.Basic">
      <summary>Indicates that basic authentication should be used on the connection. The value is equal to 1.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.AuthType.Negotiate">
      <summary>Indicates that Microsoft Negotiate authentication should be used on the connection. The value is equal to 2.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.AuthType.Ntlm">
      <summary>Indicates that Windows NT Challenge/Response (NTLM) authentication should be used on the connection. The value is equal to 3.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.AuthType.Digest">
      <summary>Indicates that the Digest Access Authentication should be used on the connection. The value is equal to 4.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.AuthType.Sicily">
      <summary>Indicates a negotiation mechanism (Sicily) will be used to choose MSN, DPA or NTLM.  This should be used for LDAPv2 servers only. The value is equal to 5.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.AuthType.Dpa">
      <summary>Indicates that Distributed Password Authentication (DPA) should be used on the connection. The value is equal to 6.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.AuthType.Msn">
      <summary>Indicates that it is authenticated by "Microsoft Network Authentication Service". The value is equal to 7.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.AuthType.External">
      <summary>Indicates an external method will be used to authenticate the connection. The value is equal to 8.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.AuthType.Kerberos">
      <summary>Indicates that Kerberos authentication should be used on the connection. The value is equal to 9.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.BerConversionException">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.BerConversionException" /> class is an exception thrown when converting data using a <see cref="T:System.DirectoryServices.Protocols.BerConverter" /> object.          </summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.BerConversionException.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.BerConversionException.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.BerConversionException" /> class.          </summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.BerConversionException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.BerConversionException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.BerConversionException" /> class using the specified serialization data and streaming contextual data.          </summary>
      <param name="info">The serialized object data about the exception thrown. </param>
      <param name="context">The contextual data about the source or destination. </param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.BerConversionException.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.BerConversionException.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.BerConversionException" /> class using the specified message.          </summary>
      <param name="message">The message displayed to the client when the exception is thrown.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.BerConversionException.#ctor(System.String,System.Exception)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.BerConversionException.#ctor(System.String,System.Exception)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.BerConversionException" /> class using the specified message and inner exception.          </summary>
      <param name="message">The message displayed when the exception is thrown.</param>
      <param name="inner">The <see cref="P:System.Exception.InnerException" />, if any, that threw the exception.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.BerConverter">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.BerConverter" /> class encodes and decodes structured data using basic encoding rules (BER).</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.BerConverter.Decode(System.String,System.Byte[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.BerConverter.Decode(System.String,System.Byte[])" /> method decodes a binary representation of the data, using BER, to retrieve structured data.</summary>
      <returns>The decoded data.</returns>
      <param name="format">The format string.</param>
      <param name="value">An array of BER data.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="format" /> parameter contains a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="format" /> parameter contains an undefined character.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.BerConversionException">The underlying decoding fails. The decoding rules include the following.CodeCorresponding Result'{' '}' '[' ']' 'n' 'x'No corresponding result'i' 'e'int'b'bool'a'string'O'byte[]'B'byte[] containing bit strings'v'string[]'V'byte[][]</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.BerConverter.Encode(System.String,System.Object[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.BerConverter.Encode(System.String,System.Object[])" /> method encodes structured data, using BER, to retrieve a binary representation of the data.</summary>
      <returns>An array of BER-encoded data.</returns>
      <param name="format">The format string.</param>
      <param name="value">An array of formatted data.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.CompareRequest">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.CompareRequest" /> class determines whether the directory object holds the specified value for the attribute.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.CompareRequest.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.CompareRequest.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.CompareRequest" /> class.          </summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.CompareRequest.#ctor(System.String,System.DirectoryServices.Protocols.DirectoryAttribute)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.CompareRequest.#ctor(System.String,System.DirectoryServices.Protocols.DirectoryAttribute)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.CompareRequest" /> class using the specified distinguished name and attributes.          </summary>
      <param name="distinguishedName">The distinguished name of the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object to compare.</param>
      <param name="assertion">A <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="assertion" /> contains a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="assertion" /> must contain exactly one value.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.CompareRequest.#ctor(System.String,System.String,System.Byte[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.CompareRequest.#ctor(System.String,System.String,System.Byte[])" /> constructor creates an instance of <see cref="T:System.DirectoryServices.Protocols.CompareRequest" /> using the specified distinguished name, attribute, and attribute value.          </summary>
      <param name="distinguishedName">The distinguished name of the object                       to compare.</param>
      <param name="attributeName">The name of the attribute to compare.</param>
      <param name="value">The value to compare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attributeName" /> or <paramref name="value" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.CompareRequest.#ctor(System.String,System.String,System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.CompareRequest.#ctor(System.String,System.String,System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.CompareRequest" /> class using the specified distinguished name, attribute, and attribute value.          </summary>
      <param name="distinguishedName">The distinguished name of the object to compare.</param>
      <param name="attributeName">The name of the attribute to compare.</param>
      <param name="value">The value to compare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attributeName" /> or <paramref name="value" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.CompareRequest.#ctor(System.String,System.String,System.Uri)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.CompareRequest.#ctor(System.String,System.String,System.Uri)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.CompareRequest" /> class using the specified distinguished name, attribute, and attribute value.          </summary>
      <param name="distinguishedName">The distinguished name of the object                       to compare.</param>
      <param name="attributeName">The name of the attribute to compare.</param>
      <param name="value">The value to compare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attributeName" /> or <paramref name="value" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.CompareRequest.Assertion">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.CompareRequest.Assertion" /> property gets the attribute/value pair used to compare.          </summary>
      <returns>The attribute/value pair used to compare.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.CompareRequest.DistinguishedName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.CompareRequest.DistinguishedName" /> property gets or sets the distinguished name of the object to compare.          </summary>
      <returns>The distinguished name of the object to compare.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.CompareRequest.ToXmlNode(System.Xml.XmlDocument)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.CompareRequest.ToXmlNode(System.Xml.XmlDocument)" /> method creates an XML node from the specified <see cref="T:System.Xml.XmlDocument" /> object.          </summary>
      <returns>The resulting <see cref="T:System.Xml.XmlElement" /> object.</returns>
      <param name="doc">A <see cref="T:System.Xml.XmlDocument" /> object.</param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object must contain exactly one value.</exception>
    </member>
    <member name="T:System.DirectoryServices.Protocols.CompareResponse">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.CompareResponse" /> class is returned by <see cref="M:System.DirectoryServices.Protocols.DirectoryConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest)" /> as a response to <see cref="T:System.DirectoryServices.Protocols.CompareRequest" />.          </summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.CrossDomainMoveControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.CrossDomainMoveControl" /> class is used with a <see cref="T:System.DirectoryServices.Protocols.ModifyDNRequest" /> object to move an LDAP object from one domain to another.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.CrossDomainMoveControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.CrossDomainMoveControl.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.CrossDomainMoveControl" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.CrossDomainMoveControl.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.CrossDomainMoveControl.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.CrossDomainMoveControl" /> class using the specified target domain.</summary>
      <param name="targetDomainController">The DNS name of the destination domain controller.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.CrossDomainMoveControl.GetValue">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.CrossDomainMoveControl.GetValue" /> method returns the binary representation of <see cref="T:System.DirectoryServices.Protocols.CrossDomainMoveControl" />.</summary>
      <returns>The binary representation of <see cref="T:System.DirectoryServices.Protocols.CrossDomainMoveControl" />.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.CrossDomainMoveControl.TargetDomainController">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.CrossDomainMoveControl.TargetDomainController" /> property gets or sets the DNS name of the destination domain controller (DC).</summary>
      <returns>The DNS name of the destination DC.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DeleteRequest">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DeleteRequest" /> class deletes an entry from the directory.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DeleteRequest.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DeleteRequest.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DeleteRequest" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DeleteRequest.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DeleteRequest.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DeleteRequest" /> class using the specified distinguished name.</summary>
      <param name="distinguishedName">The distinguished name of the object to delete.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DeleteRequest.DistinguishedName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DeleteRequest.DistinguishedName" /> property gets or sets the distinguished name of object to delete.</summary>
      <returns>The distinguished name of object to delete.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DeleteRequest.ToXmlNode(System.Xml.XmlDocument)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DeleteRequest.ToXmlNode(System.Xml.XmlDocument)" /> method creates an XML node from the specified <see cref="T:System.Xml.XmlDocument" /> object.</summary>
      <returns>The resulting <see cref="T:System.Xml.XmlElement" /> object.</returns>
      <param name="doc">An <see cref="T:System.Xml.XmlDocument" /> object.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DeleteResponse">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DeleteResponse" /> class is returned by <see cref="M:System.DirectoryServices.Protocols.DirectoryConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest)" /> as a response to <see cref="T:System.DirectoryServices.Protocols.DeleteRequest" />.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DereferenceAlias">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DereferenceAlias" /> enumeration specifies the process by which aliases are dereferenced.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DereferenceAlias.Never">
      <summary>Does not dereference aliases. The value is equal to 0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DereferenceAlias.InSearching">
      <summary>Dereferences aliases when searching subordinates of the base object, but not when locating the base itself. The value is equal to 1.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DereferenceAlias.FindingBaseObject">
      <summary>Dereferences aliases when locating the base object, but not when searching its subordinates. The value is equal to 2.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DereferenceAlias.Always">
      <summary>Dereferences aliases when both searching subordinates and locating the base object of the search. The value is equal to 3.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DereferenceConnectionCallback">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DereferenceConnectionCallback" /> delegate dereferences a connection that is no longer required. The connection was probably established through a successful call to the <see cref="T:System.DirectoryServices.Protocols.QueryForConnectionCallback" /> or <see cref="T:System.DirectoryServices.Protocols.NotifyOfNewConnectionCallback" /> delegate.</summary>
      <param name="primaryConnection">An <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object that specifies the primary connection.</param>
      <param name="connectionToDereference">An <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object that specifies the connection to dereference.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryAttribute">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> class enables access to the attribute values.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.#ctor(System.String,System.Byte[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.#ctor(System.String,System.Byte[])" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> class using the specified attribute name and value.</summary>
      <param name="name">The attribute name.</param>
      <param name="value">The attribute value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> or <paramref name="value" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.#ctor(System.String,System.Object[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.#ctor(System.String,System.Object[])" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> class using the specified attribute name and values.</summary>
      <param name="name">The attribute name.</param>
      <param name="values">An array of values for the attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> or <paramref name="value" /> contains a null reference (<paramref name="Nothing" /> in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">An element of <paramref name="values" /> is not of type string, byte[], or Uri.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.#ctor(System.String,System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.#ctor(System.String,System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> class using the specified attribute name and value.</summary>
      <param name="name">The attribute name.</param>
      <param name="value">The attribute value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> or <paramref name="value" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.#ctor(System.String,System.Uri)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.#ctor(System.String,System.Uri)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> class using the specified attribute name and value.</summary>
      <param name="name">The attribute name.</param>
      <param name="value">The attribute value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> or <paramref name="value" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.Add(System.Byte[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.Add(System.Byte[])" /> method adds the specified value to this attribute.</summary>
      <returns>The index at which the value has been added.</returns>
      <param name="value">The value to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="Value" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.Add(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.Add(System.String)" /> method adds the specified value to this attribute.          </summary>
      <returns>The index at which the value has been added.</returns>
      <param name="value">The value to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="Value" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.Add(System.Uri)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.Add(System.Uri)" /> method adds the specified value to this attribute.</summary>
      <returns>The index at which the value has been added.</returns>
      <param name="value">The value to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="Value" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.AddRange(System.Object[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.AddRange(System.Object[])" /> method adds an array of values to the attribute.</summary>
      <param name="values">The values to add.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="values" /> is not a  byte[][], string[], or Uri[].</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="Values" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.Contains(System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.Contains(System.Object)" /> method determines if the attribute contains the specified value.          </summary>
      <returns>true if this attribute contains the value or false if it does not.</returns>
      <param name="value">The value for which to search.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.CopyTo(System.Object[],System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.CopyTo(System.Object[],System.Int32)" /> method copies the entire collection to a one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="array">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object.</param>
      <param name="index">The zero-based index of <paramref name="array" /> where the copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> contains a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">This exception can occur under one of the following conditions:<paramref name="array" /> is multidimensional<paramref name="index" /> is equal to, or greater than, the length of <paramref name="array" />The number of elements in the source <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> is greater than the space available from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
      <exception cref="T:System.InvalidCastException">The type of the source <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.GetValues(System.Type)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.GetValues(System.Type)" /> method returns all values of the specified type.</summary>
      <returns>An array of <see cref="T:System.Object" /> objects that contain the values.</returns>
      <param name="valuesType">A <see cref="T:System.Type" /> object that specifies the type of values to return.</param>
      <exception cref="T:System.NotSupportedException">A value cannot be converted to <paramref name="valuesType" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="valuesType" /> must be either string or byte.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.IndexOf(System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.IndexOf(System.Object)" /> method returns the zero-based index of the first occurrence of the specified  <see cref="T:System.Object" /> in the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> collection.          </summary>
      <returns>The index value of the specified <see cref="T:System.Object" />, if found; otherwise, -1.</returns>
      <param name="value">The <see cref="T:System.Object" /> for which the value is returned.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.Insert(System.Int32,System.Byte[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.Insert(System.Int32,System.Byte[])" /> method inserts the specified <paramref name="value" /> into the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> collection at the specified <paramref name="index" />.</summary>
      <param name="index">The index in the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> collection at which to insert the <paramref name="value" />.</param>
      <param name="value">The value to insert.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> contains a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero, or index is greater than the number of elements in the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.Insert(System.Int32,System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.Insert(System.Int32,System.String)" /> method inserts the specified <paramref name="value" /> into the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> collection at the specified <paramref name="index" />.          </summary>
      <param name="index">The index in the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> collection at which to insert the <paramref name="value" />.</param>
      <param name="value">The value to insert.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> contains a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero, or <paramref name="index" /> is greater than  the number of elements in the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.Insert(System.Int32,System.Uri)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.Insert(System.Int32,System.Uri)" /> method inserts the specified <paramref name="value" /> into the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> collection at the specified <paramref name="index" />.</summary>
      <param name="index">The index in the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> collection at which to insert the <paramref name="value" />.</param>
      <param name="value">The value to insert.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> contains a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero or <paramref name="index" /> is greater than the number of elements in the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryAttribute.Item(System.Int32)">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryAttribute.Item(System.Int32)" /> property gets or sets the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object at the specified index.</summary>
      <returns>The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object at the specified index.</returns>
      <param name="index">The zero-based index value at which the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object is found.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> to set is an invalid type. The valid types are byte[], string, or URI.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryAttribute.Name">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryAttribute.Name" /> property contains the attribute name.</summary>
      <returns>The attribute name.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.OnValidate(System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.OnValidate(System.Object)" /> method verifies that <paramref name="value" /> is a <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object.</summary>
      <param name="value">The <see cref="T:System.Object" /> to evaluate.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> contains a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> must be either a string, byte[], or URI.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttribute.Remove(System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttribute.Remove(System.Object)" /> method removes the first occurrence of the specified <paramref name="value" /> from the list of attributes.</summary>
      <param name="value">The <see cref="T:System.Object" /> to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryAttributeCollection">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeCollection" /> class contains a collection of <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> objects.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeCollection" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.Add(System.DirectoryServices.Protocols.DirectoryAttribute)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.Add(System.DirectoryServices.Protocols.DirectoryAttribute)" /> method adds a <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object to the collection.</summary>
      <returns>The zero-based index in the collection where the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> was added.</returns>
      <param name="attribute">The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object to add.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="attribute" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.AddRange(System.DirectoryServices.Protocols.DirectoryAttribute[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.AddRange(System.DirectoryServices.Protocols.DirectoryAttribute[])" /> method adds an array of <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> objects to the collection.</summary>
      <param name="attributes">An array of <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attributes" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">A member of <paramref name="attributes" /> is a null reference (Nothing in Visual Basic) or a member of <paramref name="attributes" /> specifies a modification operation other than Add.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.AddRange(System.DirectoryServices.Protocols.DirectoryAttributeCollection)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.AddRange(System.DirectoryServices.Protocols.DirectoryAttributeCollection)" /> method adds a <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeCollection" /> object to the current collection.</summary>
      <param name="attributeCollection">The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeCollection" /> object to add to this collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attributeCollection" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.Contains(System.DirectoryServices.Protocols.DirectoryAttribute)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.Contains(System.DirectoryServices.Protocols.DirectoryAttribute)" /> method determines whether this <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeCollection" /> object contains a specified <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object.</summary>
      <returns>true if the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> is part of the collection; false otherwise.</returns>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object for which to search.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.CopyTo(System.DirectoryServices.Protocols.DirectoryAttribute[],System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.CopyTo(System.DirectoryServices.Protocols.DirectoryAttribute[],System.Int32)" /> method copies the entire collection to a one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="array">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeCollection" /> object.</param>
      <param name="index">The zero-based index of <paramref name="array" /> where copying begins.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.IndexOf(System.DirectoryServices.Protocols.DirectoryAttribute)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.IndexOf(System.DirectoryServices.Protocols.DirectoryAttribute)" /> method returns the zero-based index of the first occurrence of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object in the  <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeCollection" />.</summary>
      <returns>The index value of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object, if found; otherwise, -1.</returns>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object for which the index is returned.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.Insert(System.Int32,System.DirectoryServices.Protocols.DirectoryAttribute)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.Insert(System.Int32,System.DirectoryServices.Protocols.DirectoryAttribute)" /> method inserts the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object into the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeCollection" /> at the specified <paramref name="index" />.</summary>
      <param name="index">The zero-based index point, in the collection, at which to insert the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object.</param>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object to be inserted into the collection.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is a null reference (Nothing in Visual Basic) or <paramref name="value" /> specifies a modification operation other than Add.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero, or <paramref name="index" /> is greater than  the number of elements in the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeCollection" /> object.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryAttributeCollection.Item(System.Int32)">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryAttributeCollection.Item(System.Int32)" /> property contains the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object at the specified index.</summary>
      <returns>The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object at the specified index.</returns>
      <param name="index">The index value of the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.OnValidate(System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.OnValidate(System.Object)" /> method verifies that <paramref name="value" /> is a <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object or specifies an add operation.</summary>
      <param name="value">The object to evaluate.</param>
      <exception cref="T:System.ArgumentException">This exception can occur under one of the following conditions:<paramref name="value" /> is a null reference (Nothing in Visual Basic).<paramref name="value" /> is not a <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.Remove(System.DirectoryServices.Protocols.DirectoryAttribute)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeCollection.Remove(System.DirectoryServices.Protocols.DirectoryAttribute)" /> method removes the first occurrence of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object to remove.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryAttributeModification">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> class enables modification of the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> values.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeModification.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeModification.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> class.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryAttributeModification.Operation">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryAttributeModification.Operation" /> property specifies one of <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeOperation" /> modifications to perform.</summary>
      <returns>The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeOperation" /> modification to perform.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection" /> class contains a collection of <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> objects.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.Add(System.DirectoryServices.Protocols.DirectoryAttributeModification)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.Add(System.DirectoryServices.Protocols.DirectoryAttributeModification)" /> method adds a <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> object to the collection.</summary>
      <returns>The zero-based index in the collection where the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> was added.</returns>
      <param name="attribute">The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> to add.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="attribute" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.AddRange(System.DirectoryServices.Protocols.DirectoryAttributeModification[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.AddRange(System.DirectoryServices.Protocols.DirectoryAttributeModification[])" /> method adds an array of <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> objects to the collection.</summary>
      <param name="attributes">An array of <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attributes" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">One of the members of <paramref name="attributes" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.AddRange(System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.AddRange(System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection)" /> method adds a <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection" /> object to the current collection.</summary>
      <param name="attributeCollection">The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection" /> collection to add to this collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attributeCollection" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.Contains(System.DirectoryServices.Protocols.DirectoryAttributeModification)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.Contains(System.DirectoryServices.Protocols.DirectoryAttributeModification)" /> method determines whether this <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection" /> object contains a specified <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> object.</summary>
      <returns>true if the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> is part of the collection; false otherwise.</returns>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> object for which to search.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.CopyTo(System.DirectoryServices.Protocols.DirectoryAttributeModification[],System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.CopyTo(System.DirectoryServices.Protocols.DirectoryAttributeModification[],System.Int32)" /> method copies the entire collection to a one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="array">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection" /> object.</param>
      <param name="index">The zero-based index of <paramref name="array" /> where copying begins.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.IndexOf(System.DirectoryServices.Protocols.DirectoryAttributeModification)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.IndexOf(System.DirectoryServices.Protocols.DirectoryAttributeModification)" /> method returns the zero-based index of the first occurrence of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> object in the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection" />.</summary>
      <returns>The index value of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> object, if found; otherwise, -1.</returns>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection" /> object for which the index is returned.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.Insert(System.Int32,System.DirectoryServices.Protocols.DirectoryAttributeModification)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.Insert(System.Int32,System.DirectoryServices.Protocols.DirectoryAttributeModification)" /> method inserts a <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> object into the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection" /> collection at the specified index.</summary>
      <param name="index">The zero-based index in the collection at which to insert the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> object.</param>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> object to insert.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero, or <paramref name="index" /> is greater than the number of elements in the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection" /> object.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.Item(System.Int32)">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.Item(System.Int32)" /> property contains the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> object at the specified <paramref name="index" />.</summary>
      <returns>The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> object at the specified <paramref name="index" />.</returns>
      <param name="index">The index value of the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> object.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.OnValidate(System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.OnValidate(System.Object)" /> method verifies that <paramref name="value" /> is a <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> object.</summary>
      <param name="value">The object to evaluate.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.Remove(System.DirectoryServices.Protocols.DirectoryAttributeModification)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection.Remove(System.DirectoryServices.Protocols.DirectoryAttributeModification)" /> method removes the first occurrence of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> object to remove.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryAttributeOperation">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeOperation" /> enumeration specifies the operation to perform on a directory attribute.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DirectoryAttributeOperation.Add">
      <summary>Add an attribute value. The value is equal to 0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DirectoryAttributeOperation.Delete">
      <summary>Delete an attribute value. The value is equal to 1.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DirectoryAttributeOperation.Replace">
      <summary>Replace an attribute value. The value is equal to 2.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryConnection">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryConnection" /> class is an abstract class and cannot be instantiated. Use the <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> or <see cref="T:System.DirectoryServices.Protocols.DsmlSoapHttpConnection" /> classes to connect to a directory server.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryConnection.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryConnection.#ctor" /> constructor initializes a new instance of a <see cref="T:System.DirectoryServices.Protocols.DirectoryConnection" /> class.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryConnection.ClientCertificates">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryConnection.ClientCertificates" /> property specifies one or more client certificates to send for authentication.</summary>
      <returns>One or more client certificates to send for authentication.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryConnection.Credential">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryConnection.Credential" /> property specifies alternate credentials for the connection object.</summary>
      <returns>Alternate credentials for the connection object.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryConnection.Directory">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryConnection.Directory" /> property specifies the domain or the server to which the connection should be made.</summary>
      <returns>The domain or the server to which the connection should be made.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest)" /> method sends a single directory operation to the server.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object that contains the server response to the request operation. The returned response object may be different than the requested object if the server returns a different response. </returns>
      <param name="request">A <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object that contains the request.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryConnection.Timeout">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryConnection.Timeout" /> property contains the length of time, in seconds, before the connection times out.</summary>
      <returns>The length of time, in seconds, before the connection times out.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> class specifies extension data for various LDAP operations.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryControl.#ctor(System.String,System.Byte[],System.Boolean,System.Boolean)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryControl.#ctor(System.String,System.Byte[],System.Boolean,System.Boolean)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> class using the specified   values for the type, value, criticality, and ServerSide properties.</summary>
      <param name="type">The control type.</param>
      <param name="value">The value associated with the control.</param>
      <param name="isCritical">Specifies if the control is critical. The default is true.</param>
      <param name="serverSide">Specifies if this is a server-side control.  The default is true. false specifies a client-side control.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> parameter is null.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryControl.GetValue">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryControl.GetValue" /> method returns the data associated with the control.</summary>
      <returns>The data associated with the control.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryControl.IsCritical">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryControl.IsCritical" /> property specifies whether the control is critical.</summary>
      <returns>true if the control is critical; otherwise, false.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryControl.ServerSide">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryControl.ServerSide" /> property specifies whether this is a server-side control.</summary>
      <returns>true if the control is a server-side control. false specifies a client-side control.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryControl.Type">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryControl.Type" /> property contains the object identifier (OID) of the control.</summary>
      <returns>The OID of the control.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryControlCollection">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> class manages a collection of <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> objects.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryControlCollection.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryControlCollection.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryControlCollection.Add(System.DirectoryServices.Protocols.DirectoryControl)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryControlCollection.Add(System.DirectoryServices.Protocols.DirectoryControl)" /> method adds a new <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object to the collection.</summary>
      <returns>The index of the <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> at which the <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object has been added.</returns>
      <param name="control">The <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="control" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryControlCollection.AddRange(System.DirectoryServices.Protocols.DirectoryControl[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryControlCollection.AddRange(System.DirectoryServices.Protocols.DirectoryControl[])" /> method adds an array of <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> objects to the collection.</summary>
      <param name="controls">An array of <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="controls" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">A member of the array is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryControlCollection.AddRange(System.DirectoryServices.Protocols.DirectoryControlCollection)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryControlCollection.AddRange(System.DirectoryServices.Protocols.DirectoryControlCollection)" /> method adds a <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> object to the current collection.</summary>
      <param name="controlCollection">The <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> object to add to this collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="controlCollection" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryControlCollection.Contains(System.DirectoryServices.Protocols.DirectoryControl)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryControlCollection.Contains(System.DirectoryServices.Protocols.DirectoryControl)" /> method determines whether this <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> object contains a specified <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object.</summary>
      <returns>true if the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object is part of the collection; false, otherwise.</returns>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object for which to search.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryControlCollection.CopyTo(System.DirectoryServices.Protocols.DirectoryControl[],System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryControlCollection.CopyTo(System.DirectoryServices.Protocols.DirectoryControl[],System.Int32)" /> method copies the entire collection to a one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="array">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> object.</param>
      <param name="index">The zero-based index of <paramref name="array" /> where copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is a null reference (Nothing in Visual Basic). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional-or-The number of elements in the source <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
      <exception cref="T:System.InvalidCastException">The type of the source <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" />  cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryControlCollection.IndexOf(System.DirectoryServices.Protocols.DirectoryControl)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryControlCollection.IndexOf(System.DirectoryServices.Protocols.DirectoryControl)" /> method returns the zero-based index of the first occurrence of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object in the <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" />.</summary>
      <returns>The index value of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object, if found; otherwise, -1.</returns>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object for which the index is returned.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryControlCollection.Insert(System.Int32,System.DirectoryServices.Protocols.DirectoryControl)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryControlCollection.Insert(System.Int32,System.DirectoryServices.Protocols.DirectoryControl)" /> method inserts the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object into the <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> at the specified index.</summary>
      <param name="index">The index in the collection at which to insert the <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object.</param>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object to be inserted into the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero, or <paramref name="index" /> is greater than the number of elements in the <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> object.</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> is read-only, or <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> has a fixed size.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryControlCollection.Item(System.Int32)">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryControlCollection.Item(System.Int32)" /> property contains the <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object at the specified index.</summary>
      <returns>Contains the <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object at the specified index.</returns>
      <param name="index">The index value of the <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object. </param>
      <exception cref="T:System.ArgumentNullException">There was an attempt to set the property to null.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryControlCollection.OnValidate(System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryControlCollection.OnValidate(System.Object)" /> method verifies that <paramref name="value" /> is a <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object.          </summary>
      <param name="value">The object to evaluate.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is a null reference (Nothing in Visual Basic). </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is not a <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryControlCollection.Remove(System.DirectoryServices.Protocols.DirectoryControl)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryControlCollection.Remove(System.DirectoryServices.Protocols.DirectoryControl)" /> method removes the first occurrence of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object to remove.</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> is read-only, or <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> has a fixed size.</exception>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryException">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryException" /> class is an abstract class used as the base class for all <see cref="N:System.DirectoryServices.Protocols" /> exceptions.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryException.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryException.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryException" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryException" /> class using the specified serialization information and streaming contextual information.</summary>
      <param name="info">The serialized object data about the exception being thrown. </param>
      <param name="context">The contextual information about the source or destination. </param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryException.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryException.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryException" /> class using the specified parameter.</summary>
      <param name="message">The message displayed to the client when the exception is thrown.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryException.#ctor(System.String,System.Exception)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryException.#ctor(System.String,System.Exception)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryException" /> class using the specified parameters.</summary>
      <param name="message">The message displayed to the client when the exception is thrown.</param>
      <param name="inner">The <see cref="P:System.Exception.InnerException" />, if any, that threw the exception.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryIdentifier">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryIdentifier" /> class is an abstract class that identifies the target to connect to.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryIdentifier.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryIdentifier.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryIdentifier" /> class.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryNotificationControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryNotificationControl" /> class is a directory control used with an extended LDAP asynchronous search function to register the client to be notified when changes are made to an object in the Active Directory Domain Services.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryNotificationControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryNotificationControl.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryNotificationControl" /> class.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryOperation">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryOperation" /> class is an abstract class used as a base for request and response elements.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryOperation.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryOperation.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryOperation" /> object.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryOperationException">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryOperationException" /> class is an exception thrown by the <see cref="M:System.DirectoryServices.Protocols.LdapConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest)" /> method to indicate that the server returned a <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object with an error.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryOperationException.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryOperationException.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryOperationException" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryOperationException.#ctor(System.DirectoryServices.Protocols.DirectoryResponse)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryOperationException.#ctor(System.DirectoryServices.Protocols.DirectoryResponse)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryOperationException" /> class using the specified response object.</summary>
      <param name="response">The <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object returned by the server.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryOperationException.#ctor(System.DirectoryServices.Protocols.DirectoryResponse,System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryOperationException.#ctor(System.DirectoryServices.Protocols.DirectoryResponse,System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryOperationException" /> object using the specified response object and message.</summary>
      <param name="response">The <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object returned by the server.</param>
      <param name="message">The message displayed to the client when the exception is thrown.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryOperationException.#ctor(System.DirectoryServices.Protocols.DirectoryResponse,System.String,System.Exception)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryOperationException.#ctor(System.DirectoryServices.Protocols.DirectoryResponse,System.String,System.Exception)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryOperationException" /> class using the specified response object, message, and inner exception. </summary>
      <param name="response">The <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object returned by the server.</param>
      <param name="message">The message displayed to the client when the exception is thrown.</param>
      <param name="inner">The <see cref="P:System.Exception.InnerException" />, if any, that threw the exception.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryOperationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryOperationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryOperationException" /> class using the specified serialization information and streaming context.</summary>
      <param name="info">Data required to serialize the parameter.</param>
      <param name="context">The source and destination of the serialized stream associated with the parameter.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryOperationException.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryOperationException.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryOperationException" /> class using the specified message.</summary>
      <param name="message">The message displayed to the client when the exception is thrown.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryOperationException.#ctor(System.String,System.Exception)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryOperationException.#ctor(System.String,System.Exception)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirectoryOperationException" /> class using the specified message and inner exception.</summary>
      <param name="message">The message displayed to the client when the exception is thrown.</param>
      <param name="inner">The <see cref="P:System.Exception.InnerException" />, if any, that threw the exception.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryOperationException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryOperationException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> method populates the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with the data required to serialize the parameter.</summary>
      <param name="serializationInfo">Data required to serialize the parameter.</param>
      <param name="streamingContext">The source and destination of the serialized stream associated with the parameter.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryOperationException.Response">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryOperationException.Response" /> property specifies the <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object returned by the server that contains an error.</summary>
      <returns>The <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object returned by the server that contains an error.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryRequest">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> class is the base class for request related classes, like the <see cref="T:System.DirectoryServices.Protocols.SearchRequest" /> and <see cref="T:System.DirectoryServices.Protocols.AddRequest" /> classes.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryRequest.Controls">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryRequest.Controls" /> property contains a <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> object.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> object.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryRequest.RequestId">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryRequest.RequestId" /> property contains the <paramref name="requestID" /> specified in the request.</summary>
      <returns>The <paramref name="requestID" /> specified in the request.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirectoryRequest.ToXmlNode(System.Xml.XmlDocument)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirectoryRequest.ToXmlNode(System.Xml.XmlDocument)" /> method creates an XML node from the specified <see cref="T:System.Xml.XmlDocument" /> object.</summary>
      <returns>The created <see cref="T:System.Xml.XmlElement" />.</returns>
      <param name="doc">The <see cref="T:System.Xml.XmlDocument" /> to represent as an XML element.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectoryResponse">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> class is the base class for request response classes, like the <see cref="T:System.DirectoryServices.Protocols.SearchResponse" /> and <see cref="T:System.DirectoryServices.Protocols.AddResponse" /> classes.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryResponse.Controls">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryResponse.Controls" /> property contains an array of <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> objects returned by the server.</summary>
      <returns>An array of <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> objects returned by the server.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryResponse.ErrorMessage">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryResponse.ErrorMessage" /> property contains the error message, if any, for this operation.</summary>
      <returns>The error message, if any, for this operation.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryResponse.MatchedDN">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryResponse.MatchedDN" /> property contains the matched distinguished name returned by the server.</summary>
      <returns>The matched distinguished name returned by the server.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryResponse.Referral">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryResponse.Referral" /> property contains the server referrals.</summary>
      <returns>The server referrals.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryResponse.RequestId">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryResponse.RequestId" /> property contains the Request Identifier.</summary>
      <returns>The Request Identifier.</returns>
      <exception cref="T:System.NotSupportedException">User attempted to set the property value.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirectoryResponse.ResultCode">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirectoryResponse.ResultCode" /> property contains the result code of the operation.</summary>
      <returns>The result code of the operation.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirectorySynchronizationOptions">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirectorySynchronizationOptions" /> enumeration specifies the behavior of the search in a <see cref="T:System.DirectoryServices.Protocols.DirSyncRequestControl" /> object.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DirectorySynchronizationOptions.None">
      <summary>No options used. The value equals 0 or 0x0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DirectorySynchronizationOptions.ObjectSecurity">
      <summary>If this option is used, the caller requires no rights, but can only view objects and attributes that are accessible to the caller. If this option is not used, the caller must have the replicate changes right. The value equals 1 or 0x1.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DirectorySynchronizationOptions.ParentsFirst">
      <summary>Return parents before children, when parents would otherwise appear later in the replication stream. The value equals 2048 or 0x800.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DirectorySynchronizationOptions.PublicDataOnly">
      <summary>Do not return private data in search results. The value equals 8192 or 0x2000.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DirectorySynchronizationOptions.IncrementalValues">
      <summary>If this option is used, only changed values are returned. If this option is not used, all values, up to a server-specified limit, in a multi-valued attribute, are returned when a value changes. The value equals 2147483648 or 0x80000000.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirSyncRequestControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirSyncRequestControl" /> class is a directory control that enables the application to search the directory for objects that have changed since a previous state.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirSyncRequestControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirSyncRequestControl.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirSyncRequestControl" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirSyncRequestControl.#ctor(System.Byte[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirSyncRequestControl.#ctor(System.Byte[])" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirSyncRequestControl" /> class using the specified parameter.</summary>
      <param name="cookie">An opaque structure used by the server. It is updated by the directory during each <see cref="T:System.DirectoryServices.Protocols.SearchRequest" />, which enables the control to incrementally read changes from the directory.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirSyncRequestControl.#ctor(System.Byte[],System.DirectoryServices.Protocols.DirectorySynchronizationOptions)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirSyncRequestControl.#ctor(System.Byte[],System.DirectoryServices.Protocols.DirectorySynchronizationOptions)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirSyncRequestControl" /> class using the specified parameters.</summary>
      <param name="cookie">An opaque structure used by the server. It is updated by the directory during each <see cref="T:System.DirectoryServices.Protocols.SearchRequest" />, which enables the control to incrementally read changes from the directory.</param>
      <param name="option">Specifies the behavior of the search. This parameter can be zero or a combination of one or more of the values of <see cref="T:System.DirectoryServices.Protocols.DirectorySynchronizationOptions" />.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirSyncRequestControl.#ctor(System.Byte[],System.DirectoryServices.Protocols.DirectorySynchronizationOptions,System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirSyncRequestControl.#ctor(System.Byte[],System.DirectoryServices.Protocols.DirectorySynchronizationOptions,System.Int32)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DirSyncRequestControl" /> class using the specified parameters.</summary>
      <param name="cookie">An opaque structure used by the server. It is updated by the directory during each <see cref="T:System.DirectoryServices.Protocols.SearchRequest" />, which enables the control to incrementally read changes from the directory.</param>
      <param name="option">Specifies the behavior of the search. This parameter can be zero or a combination of one or more of the values of <see cref="T:System.DirectoryServices.Protocols.DirectorySynchronizationOptions" />. </param>
      <param name="attributeCount">The maximum number of attributes to return. The default is 1048576.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirSyncRequestControl.AttributeCount">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirSyncRequestControl.AttributeCount" /> property specifies the maximum number of attributes to return.</summary>
      <returns>The maximum number of attributes to return.</returns>
      <exception cref="T:System.ArgumentException">There was an attempt to set a value less than zero.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirSyncRequestControl.Cookie">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirSyncRequestControl.Cookie" /> property contains an opaque structure used by the server.</summary>
      <returns>An opaque structure used by the server.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DirSyncRequestControl.GetValue">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DirSyncRequestControl.GetValue" /> method returns the data associated with this control.</summary>
      <returns>The data associated with this control.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirSyncRequestControl.Option">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirSyncRequestControl.Option" /> property specifies the behavior of the search.</summary>
      <returns>The behavior of the search.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DirSyncResponseControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DirSyncResponseControl" /> class is a directory control used to pass DirSyncRequest information from the server to the client.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirSyncResponseControl.Cookie">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirSyncResponseControl.Cookie" /> property contains an opaque structure returned by the directory.</summary>
      <returns>An opaque structure returned by the directory.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirSyncResponseControl.MoreData">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirSyncResponseControl.MoreData" /> property specifies whether there is more data to return.</summary>
      <returns>true if there is more data; false, otherwise.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DirSyncResponseControl.ResultSize">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DirSyncResponseControl.ResultSize" /> property specifies the size of the result.</summary>
      <returns>The size of the result.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DomainScopeControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DomainScopeControl" /> class is a directory control used to instruct the LDAP server not to generate referrals when completing a request.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DomainScopeControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DomainScopeControl.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DomainScopeControl" /> class.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DsmlAuthRequest">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DsmlAuthRequest" /> class indicates that access control for the requests be interpreted as though the requests are performed by the security principal identified by the <see cref="P:System.DirectoryServices.Protocols.DsmlAuthRequest.Principal" /> property.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlAuthRequest.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlAuthRequest.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DsmlAuthRequest" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlAuthRequest.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlAuthRequest.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DsmlAuthRequest" /> class using the specified parameter.</summary>
      <param name="principal">The security principal.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlAuthRequest.Principal">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlAuthRequest.Principal" /> property contains the security principal.</summary>
      <returns>The security principal.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlAuthRequest.ToXmlNode(System.Xml.XmlDocument)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlAuthRequest.ToXmlNode(System.Xml.XmlDocument)" /> method transforms an <see cref="T:System.Xml.XmlDocument" /> object into its DSML v2 XML representation.</summary>
      <returns>The transformed <see cref="T:System.Xml.XmlElement" /> object.</returns>
      <param name="doc">An <see cref="T:System.Xml.XmlDocument" /> object.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DsmlAuthResponse">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DsmlAuthResponse" /> class is returned by <see cref="M:System.DirectoryServices.Protocols.DirectoryConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest)" /> as a response to <see cref="T:System.DirectoryServices.Protocols.DsmlAuthRequest" />.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DsmlDirectoryIdentifier">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DsmlDirectoryIdentifier" /> class identifies an HTTP server.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlDirectoryIdentifier.#ctor(System.Uri)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlDirectoryIdentifier.#ctor(System.Uri)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DsmlDirectoryIdentifier" /> class using the specified parameter.</summary>
      <param name="serverUri">A server represented as a <see cref="T:System.Uri" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serverUri" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="serverUri" /> scheme is not http or https.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlDirectoryIdentifier.ServerUri">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlDirectoryIdentifier.ServerUri" /> property contains the <see cref="T:System.Uri" /> of the server this object identifies.</summary>
      <returns>The <see cref="T:System.Uri" /> of the server this object identifies.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DsmlDocument">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DsmlDocument" /> class is an abstract class to construct or manipulate a DSML document. It is the base class for both <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> and <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" />. <see cref="T:System.DirectoryServices.Protocols.DsmlDocument" /> supports collections and indexes. Each item in its collection is an object derived from <see cref="T:System.DirectoryServices.Protocols.DirectoryOperation" />.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlDocument.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlDocument.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DsmlDocument" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlDocument.ToXml">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlDocument.ToXml" /> method transforms this <see cref="T:System.DirectoryServices.Protocols.DsmlDocument" /> object into a <see cref="T:System.Xml.XmlDocument" /> object.</summary>
      <returns>The transformed <see cref="T:System.Xml.XmlDocument" /> object.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DsmlDocumentProcessing">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DsmlDocumentProcessing" /> enumeration specifies the processing method for a DSML document.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DsmlDocumentProcessing.Sequential">
      <summary>Sequential.  Value is equal to 0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DsmlDocumentProcessing.Parallel">
      <summary>Parallel.  Value is equal to 1.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DsmlErrorProcessing">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DsmlErrorProcessing" /> enumeration specifies how to proceed when an error occurs.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DsmlErrorProcessing.Resume">
      <summary>Resume. Value is equal to 0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DsmlErrorProcessing.Exit">
      <summary>Exit. Value is equal to 1.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DsmlErrorResponse">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DsmlErrorResponse" /> class is generated by the server when a severe error occurs.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlErrorResponse.Controls">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlErrorResponse.Controls" /> property is not applicable to a DSML &lt;errorResponse&gt;.</summary>
      <returns>Not applicable to a DSML &lt;errorResponse&gt;.</returns>
      <exception cref="T:System.NotSupportedException">This property is not valid for a DSML error response.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlErrorResponse.Detail">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlErrorResponse.Detail" /> property contains the contents of the &lt;detail&gt; element of the errorResponse, if any.</summary>
      <returns>The contents of the &lt;detail&gt; element of the errorResponse, if any.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlErrorResponse.ErrorMessage">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlErrorResponse.ErrorMessage" /> property is not applicable to a DSML &lt;errorResponse&gt;.</summary>
      <returns>not applicable to a DSML &lt;errorResponse&gt;.</returns>
      <exception cref="T:System.NotSupportedException">This property is not valid for a DSML error response.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlErrorResponse.MatchedDN">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlErrorResponse.MatchedDN" /> property is not applicable to a DSML &lt;errorResponse&gt;.</summary>
      <returns>Not applicable to a DSML &lt;errorResponse&gt;.</returns>
      <exception cref="T:System.NotSupportedException">This property is not valid for a DSML error response.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlErrorResponse.Message">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlErrorResponse.Message" /> property contains the text returned in the &lt;message&gt; element of the errorResponse, if any.</summary>
      <returns>The text returned in the &lt;message&gt; element of the errorResponse, if any.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlErrorResponse.Referral">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlErrorResponse.Referral" /> property is not applicable to a DSML &lt;errorResponse&gt;.</summary>
      <returns>Not applicable to a DSML &lt;errorResponse&gt;.</returns>
      <exception cref="T:System.NotSupportedException">This property is not valid for a DSML error response.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlErrorResponse.ResultCode">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlErrorResponse.ResultCode" /> property is not applicable to a DSML &lt;errorResponse&gt;.</summary>
      <returns>Not applicable to a DSML &lt;errorResponse&gt;.</returns>
      <exception cref="T:System.NotSupportedException">This property is not valid for a DSML error response.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlErrorResponse.Type">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlErrorResponse.Type" /> property contains a value of <see cref="T:System.DirectoryServices.Protocols.ErrorResponseCategory" /> that specifies the type of the error response returned by the server.</summary>
      <returns>A value of <see cref="T:System.DirectoryServices.Protocols.ErrorResponseCategory" /> that specifies the type of the error response returned by the server.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DsmlInvalidDocumentException">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DsmlInvalidDocumentException" /> class is an exception that occurs when a DSML Request or Response document is not well-formed XML or cannot be validated with DSMLv2 schema.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlInvalidDocumentException.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlInvalidDocumentException.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DsmlInvalidDocumentException" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlInvalidDocumentException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlInvalidDocumentException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DsmlInvalidDocumentException" /> class using the specified serialization data and streaming contextual data.</summary>
      <param name="info">The serialized object data about the exception thrown.</param>
      <param name="context">The contextual data about the source or destination.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlInvalidDocumentException.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlInvalidDocumentException.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DsmlInvalidDocumentException" /> class using the specified parameter.</summary>
      <param name="message">The message displayed to the client when the exception is thrown.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlInvalidDocumentException.#ctor(System.String,System.Exception)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlInvalidDocumentException.#ctor(System.String,System.Exception)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DsmlInvalidDocumentException" /> class using the specified parameters.</summary>
      <param name="message">The message displayed to the client when the exception is thrown.</param>
      <param name="inner">The <see cref="P:System.Exception.InnerException" />, if any, that threw the exception.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DsmlRequestDocument">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> class enables you to build a DSML request payload, such as <see cref="T:System.DirectoryServices.Protocols.AddRequest" />, <see cref="T:System.DirectoryServices.Protocols.ModifyRequest" />, or <see cref="T:System.DirectoryServices.Protocols.SearchRequest" />.  It contains zero or more <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> derived objects. The user may add, delete, modify, and enumerate the <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> before sending to the server. <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> can be used with any DSML Binding classes to transport the document, such as <see cref="T:System.DirectoryServices.Protocols.DsmlSoapHttpConnection" />.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.Add(System.DirectoryServices.Protocols.DirectoryRequest)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.Add(System.DirectoryServices.Protocols.DirectoryRequest)" /> method adds a <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object to the collection.</summary>
      <returns>The index of the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> collection at which the <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object was added.</returns>
      <param name="request">The <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.Clear">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.Clear" /> method removes all items from this collection.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.Contains(System.DirectoryServices.Protocols.DirectoryRequest)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.Contains(System.DirectoryServices.Protocols.DirectoryRequest)" /> method determines whether this <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> collection contains the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object.</summary>
      <returns>true if the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object is part of the collection; false, otherwise.</returns>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object for which to search.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.CopyTo(System.DirectoryServices.Protocols.DirectoryRequest[],System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.CopyTo(System.DirectoryServices.Protocols.DirectoryRequest[],System.Int32)" /> method copies the entire <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> collection to a one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="value">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> object.</param>
      <param name="i">The zero-based index of <paramref name="value" /> where copying begins.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.Count">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.Count" /> property contains the number of <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> objects in this collection.</summary>
      <returns>The number of <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> objects in this collection.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.DocumentProcessing">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.DocumentProcessing" /> property contains one of the values of <see cref="T:System.DirectoryServices.Protocols.DsmlDocumentProcessing" /> that specifies how the elements of the collection are processed.</summary>
      <returns>One of the values of <see cref="T:System.DirectoryServices.Protocols.DsmlDocumentProcessing" /> that specifies how the elements of the collection are processed.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">
        <paramref name="value" /> is an invalid enumeration value.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.ErrorProcessing">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.ErrorProcessing" /> property contains one of the values of <see cref="T:System.DirectoryServices.Protocols.DsmlErrorProcessing" /> that specifies how errors are processed.</summary>
      <returns>One of the values of <see cref="T:System.DirectoryServices.Protocols.DsmlErrorProcessing" /> that specifies how errors are processed.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">
        <paramref name="value" /> is an invalid enumeration value.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.GetEnumerator">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.GetEnumerator" /> method returns an enumerator for the entire <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> collection.</summary>
      <returns>A <see cref="T:System.Collections.IEnumerator" /> object for the entire collection.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.IndexOf(System.DirectoryServices.Protocols.DirectoryRequest)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.IndexOf(System.DirectoryServices.Protocols.DirectoryRequest)" /> method returns the zero-based index of the first occurrence of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object in the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> collection.</summary>
      <returns>The index value of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object, if found; otherwise, -1.</returns>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object for which the index is returned.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.Insert(System.Int32,System.DirectoryServices.Protocols.DirectoryRequest)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.Insert(System.Int32,System.DirectoryServices.Protocols.DirectoryRequest)" /> method inserts the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object into the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> collection at the specified index.</summary>
      <param name="index">The index in the collection at which to insert the <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object.</param>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object to be inserted into the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.IsFixedSize">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.IsFixedSize" /> property contains a value that indicates whether the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> has a fixed size.</summary>
      <returns>true if the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> has a fixed size; otherwise, false.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.IsReadOnly">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.IsReadOnly" /> property contains a value indicating whether the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> is read only or not.</summary>
      <returns>true if the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> is read only; otherwise, false.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.IsSynchronized">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.IsSynchronized" /> property contains a value that indicates whether access to the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> is synchronized (thread-safe).</summary>
      <returns>true if access to the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> is synchronized (thread-safe); otherwise, false.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.Item(System.Int32)">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.Item(System.Int32)" /> property contains the <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object at the specified index.</summary>
      <returns>The <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object at the specified index.</returns>
      <param name="index">The zero-based index value at which the <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object is found.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> is set to a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.Remove(System.DirectoryServices.Protocols.DirectoryRequest)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.Remove(System.DirectoryServices.Protocols.DirectoryRequest)" /> method removes the first occurrence of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.RemoveAt(System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.RemoveAt(System.Int32)" /> method removes the <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object at the specified position.</summary>
      <param name="index">The zero-based index of the <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object to remove.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.RequestId">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.RequestId" /> property contains the <paramref name="RequestID" /> associated with the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" />.</summary>
      <returns>The <paramref name="RequestID" /> associated with the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" />.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.ResponseOrder">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.ResponseOrder" /> property contains one of the values of <see cref="T:System.DirectoryServices.Protocols.DsmlResponseOrder" /> that specifies the order of the response.</summary>
      <returns>One of the values of <see cref="T:System.DirectoryServices.Protocols.DsmlResponseOrder" /> that specifies the order of the response.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">
        <paramref name="value" /> is an invalid enumeration value.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.SyncRoot">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.SyncRoot" /> property contains an object that can be used to synchronize access to the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" />.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System.Collections.ICollection.CopyTo(System.Array,System.Int32)" /> method copies the entire <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> collection to a one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="value">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> object.</param>
      <param name="i">The zero-based index of <paramref name="value" /> where copying begins.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.System#Collections#ICollection#Count">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.System.Collections.ICollection.Count" /> property gets the number of <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> objects in this collection.</summary>
      <returns>The number of <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> objects.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.System#Collections#ICollection#IsSynchronized">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.System.Collections.ICollection.IsSynchronized" /> property gets a value that indicates whether access to the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> is synchronized (thread-safe).</summary>
      <returns>true if access to the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> is synchronized (thread-safe); otherwise, false.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.System#Collections#ICollection#SyncRoot">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.System.Collections.ICollection.SyncRoot" /> property gets an object that can be used to synchronize access to the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" />.</summary>
      <returns>An object that can be used to synchronize access to <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" />.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System#Collections#IList#Add(System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System.Collections.IList.Add(System.Object)" /> method adds a <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object to the collection.</summary>
      <returns>The index of the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> collection at which the <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object was added.</returns>
      <param name="request">The <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="request" /> is not a  <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System#Collections#IList#Clear">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System.Collections.IList.Clear" /> method removes all items from the collection.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System#Collections#IList#Contains(System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System.Collections.IList.Contains(System.Object)" /> method determines whether this <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> collection contains the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object.</summary>
      <returns>true if the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object is part of the collection; false, otherwise.</returns>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object for which to search.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System#Collections#IList#IndexOf(System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System.Collections.IList.IndexOf(System.Object)" /> method returns the zero-based index of the first occurrence of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object in the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> collection.</summary>
      <returns>The index value of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object, if found; otherwise, -1.</returns>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object for which the index is returned.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System.Collections.IList.Insert(System.Int32,System.Object)" /> method inserts the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object into the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> collection at the specified index.</summary>
      <param name="index">The index in the collection at which to insert the <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object.</param>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object to be inserted into the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is not a  <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.System#Collections#IList#IsFixedSize">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.System.Collections.IList.IsFixedSize" /> property gets a value that indicates whether the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> has a fixed size.</summary>
      <returns>true if the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> has a fixed size; otherwise, false.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.System#Collections#IList#IsReadOnly">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.System.Collections.IList.IsReadOnly" /> property gets a value that indicates whether the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> is read-only or not.</summary>
      <returns>true if the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> is read only; otherwise, false.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlRequestDocument.System#Collections#IList#Item(System.Int32)">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlRequestDocument.System.Collections.IList.Item(System.Int32)" /> property gets or sets the <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object at the specified index.</summary>
      <returns>The <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object at the specified <paramref name="index" />.</returns>
      <param name="index">The zero-based index value at which the <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object is found.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> is set to a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is not a  <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System#Collections#IList#Remove(System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System.Collections.IList.Remove(System.Object)" /> method removes the first occurrence of the specified <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System#Collections#IList#RemoveAt(System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.System.Collections.IList.RemoveAt(System.Int32)" /> method removes the <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object at the specified position.</summary>
      <param name="index">The zero-based index of the <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object to remove.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlRequestDocument.ToXml">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlRequestDocument.ToXml" /> method transforms each <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object in the collection into an <see cref="T:System.Xml.XmlDocument" /> object.</summary>
      <returns>The transformed <see cref="T:System.Xml.XmlDocument" /> object.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DsmlResponseDocument">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" /> class is a read-only collection generated as a response to a <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> object that contains zero, or more, objects derived from <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" />.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlResponseDocument.CopyTo(System.DirectoryServices.Protocols.DirectoryResponse[],System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlResponseDocument.CopyTo(System.DirectoryServices.Protocols.DirectoryResponse[],System.Int32)" /> method copies the entire collection to a one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="value">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> object.</param>
      <param name="i">The zero-based index of <paramref name="value" /> where copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is a null reference (Nothing in Visual Basic). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="i" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is multidimensional-or-The number of elements in the source <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> is greater than the available space from i to the end of the destination <paramref name="value" />.</exception>
      <exception cref="T:System.InvalidCastException">The type of the source <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> cannot be cast automatically to the type of the destination <paramref name="value" />.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlResponseDocument.Count">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlResponseDocument.Count" /> property contains the number of <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> objects in this collection.</summary>
      <returns>The number of <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> objects in this collection.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlResponseDocument.GetEnumerator">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlResponseDocument.GetEnumerator" /> method returns an enumerator for the entire <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" /> collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object for the entire collection.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlResponseDocument.IsErrorResponse">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlResponseDocument.IsErrorResponse" /> property specifies whether an error response has occurred.</summary>
      <returns>This property is true if an error response has occurred; false, otherwise.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlResponseDocument.IsOperationError">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlResponseDocument.IsOperationError" /> property specifies whether an operation error response has occurred.</summary>
      <returns>This property is true if an operation error has occurred; false, otherwise.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlResponseDocument.IsSynchronized">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlResponseDocument.IsSynchronized" /> property contains a value that indicates whether access to the <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" /> is synchronized (thread-safe).</summary>
      <returns>true if access to the <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" /> is synchronized (thread-safe); otherwise, false.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlResponseDocument.Item(System.Int32)">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlResponseDocument.Item(System.Int32)" /> property contains the <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object at the specified index.</summary>
      <returns>The <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object at the specified index.</returns>
      <param name="index">The zero-based index value at which the <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object is found.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlResponseDocument.RequestId">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlResponseDocument.RequestId" /> property contains the RequestID associated with the document.</summary>
      <returns>The RequestID associated with the document.</returns>
      <exception cref="T:System.NotSupportedException">You attempted to set the property value.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlResponseDocument.SyncRoot">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlResponseDocument.SyncRoot" /> property contains an object that can be used to synchronize access to the <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" />.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlResponseDocument.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlResponseDocument.System.Collections.ICollection.CopyTo(System.Array,System.Int32)" /> method copies the entire <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" /> collection to a one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="value">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" /> object.</param>
      <param name="i">The zero-based index of <paramref name="value" /> where copying begins.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlResponseDocument.System#Collections#ICollection#Count">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlResponseDocument.System.Collections.ICollection.Count" /> property gets the number of <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> objects in this collection.</summary>
      <returns>The number of <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> objects.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlResponseDocument.System#Collections#ICollection#IsSynchronized">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlResponseDocument.System.Collections.ICollection.IsSynchronized" /> property gets a value that indicates whether access to the <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" /> is synchronized (thread-safe).</summary>
      <returns>true if access to the <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" /> is synchronized (thread-safe); otherwise, false.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlResponseDocument.System#Collections#ICollection#SyncRoot">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlResponseDocument.System.Collections.ICollection.SyncRoot" /> property gets an object that can be used to synchronize access to the <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" />.</summary>
      <returns>An object that can be used to synchronize access to <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" />.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlResponseDocument.ToXml">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlResponseDocument.ToXml" /> method transforms each <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object in the collection into an <see cref="T:System.Xml.XmlDocument" /> object.</summary>
      <returns>The transformed <see cref="T:System.Xml.XmlDocument" /> object.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DsmlResponseOrder">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DsmlResponseOrder" /> enumeration specifies the order in which responses are received.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DsmlResponseOrder.Sequential">
      <summary>Sequential. The value is equal to 0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.DsmlResponseOrder.Unordered">
      <summary>Unordered. The value is equal to 1.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DsmlSoapConnection">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DsmlSoapConnection" /> class is an abstract class that cannot be instantiated. It enables stateless and stateful protocols to be handled similarly by providing session-oriented features.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlSoapConnection.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlSoapConnection.#ctor" /> constructor initializes a new instance of a <see cref="T:System.DirectoryServices.Protocols.DsmlSoapConnection" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlSoapConnection.BeginSession">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlSoapConnection.BeginSession" /> method instructs the DSML server to start a new session.</summary>
      <exception cref="T:System.InvalidOperationException">Thrown when a session is already open on the connection.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.DsmlInvalidDocumentException">Thrown when the XML returned from the Server in response to the BeginSession is not well-formed.</exception>
      <exception cref="T:System.Net.WebException">Thrown when there is a communications failure with the DSML server.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlSoapConnection.EndSession">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlSoapConnection.EndSession" /> method ends the session with the DSML server and clears the <see cref="P:System.DirectoryServices.Protocols.DsmlSoapConnection.SessionId" /> property.</summary>
      <exception cref="T:System.Net.WebException">Thrown when a communications failure occurs with the DSML server.</exception>
      <exception cref="T:System.InvalidOperationException">Thrown when there is no open session.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlSoapConnection.SessionId">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlSoapConnection.SessionId" /> property contains the active session ID.</summary>
      <returns>The active session ID.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlSoapConnection.SoapRequestHeader">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlSoapConnection.SoapRequestHeader" /> property contains the SOAP Header attached to outgoing requests.</summary>
      <returns>The SOAP Header attached to outgoing requests.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.DsmlSoapHttpConnection">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.DsmlSoapHttpConnection" /> class represents a connection to a DSML Gateway using SOAP over HTTP.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.#ctor(System.DirectoryServices.Protocols.DsmlDirectoryIdentifier)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.#ctor(System.DirectoryServices.Protocols.DsmlDirectoryIdentifier)" /> constructor initializes a new instance of a <see cref="T:System.DirectoryServices.Protocols.DsmlSoapHttpConnection" /> class.</summary>
      <param name="identifier">A <see cref="T:System.DirectoryServices.Protocols.DsmlDirectoryIdentifier" /> object that specifies server for the connection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> is null (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.#ctor(System.DirectoryServices.Protocols.DsmlDirectoryIdentifier,System.Net.NetworkCredential)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.#ctor(System.DirectoryServices.Protocols.DsmlDirectoryIdentifier,System.Net.NetworkCredential)" /> constructor initializes a new instance of a <see cref="T:System.DirectoryServices.Protocols.DsmlSoapHttpConnection" /> class.</summary>
      <param name="identifier">A <see cref="T:System.DirectoryServices.Protocols.DsmlDirectoryIdentifier" /> object that specifies server for the connection.</param>
      <param name="credential">A <see cref="T:System.Net.NetworkCredential" /> object that contains the network credentials.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> is null (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.#ctor(System.DirectoryServices.Protocols.DsmlDirectoryIdentifier,System.Net.NetworkCredential,System.DirectoryServices.Protocols.AuthType)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.#ctor(System.DirectoryServices.Protocols.DsmlDirectoryIdentifier,System.Net.NetworkCredential,System.DirectoryServices.Protocols.AuthType)" /> constructor creates and initializes a new instance of a <see cref="T:System.DirectoryServices.Protocols.DsmlSoapHttpConnection" /> class using the specified identifier, network credentials, and authentication type.</summary>
      <param name="identifier">A <see cref="T:System.DirectoryServices.Protocols.DsmlDirectoryIdentifier" /> object that specifies the server for the connection.</param>
      <param name="credential">A <see cref="T:System.Net.NetworkCredential" /> object that contains the Network Credentials.</param>
      <param name="authType">The <see cref="T:System.DirectoryServices.Protocols.AuthType" /> value that specifies the authentication type to use for this connection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="authType" /> value is not one of the DSML supported values. DSML only supports Anonymous, Ntlm, Basic, Negotiate and digest.</exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">
        <paramref name="authType" /> value is not one of the constants defined in the <see cref="T:System.DirectoryServices.Protocols.AuthType" /> enumeration.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.#ctor(System.Uri)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.#ctor(System.Uri)" /> constructor initializes a new instance of a <see cref="T:System.DirectoryServices.Protocols.DsmlSoapHttpConnection" /> class.</summary>
      <param name="uri">A <see cref="T:System.Uri" /> object that specifies server for the connection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> is null (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.Abort(System.IAsyncResult)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.Abort(System.IAsyncResult)" /> method cancels the asynchronous request.</summary>
      <param name="asyncResult">An object derived from <see cref="T:System.IAsyncResult" /> that references the asynchronous request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is null (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by the corresponding call to <see cref="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.BeginSendRequest(System.DirectoryServices.Protocols.DsmlRequestDocument,System.AsyncCallback,System.Object)" />.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.AuthType">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.AuthType" /> property contains one of the values of <see cref="T:System.DirectoryServices.Protocols.AuthType" /> enumeration that specifies the authentication type.</summary>
      <returns>One of the values of <see cref="T:System.DirectoryServices.Protocols.AuthType" /> enumeration that specifies the authentication type.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.BeginSendRequest(System.DirectoryServices.Protocols.DsmlRequestDocument,System.AsyncCallback,System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.BeginSendRequest(System.DirectoryServices.Protocols.DsmlRequestDocument,System.AsyncCallback,System.Object)" /> method begins asynchronously sending the specified <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> object to the server.</summary>
      <returns>An object derived from <see cref="T:System.IAsyncResult" /> that references the asynchronous send.</returns>
      <param name="request">The <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> object to send.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> function.</param>
      <param name="state">An <see cref="T:System.Object" /> that contains the state data for this request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is null (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.BeginSession">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.BeginSession" /> method instructs the DSML server to start a new session.</summary>
      <exception cref="T:System.InvalidOperationException">Thrown when a session is already open on the connection.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.DsmlInvalidDocumentException">Thrown when the XML returned from the Server in response to the <see cref="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.BeginSession" /> is not well-formed.</exception>
      <exception cref="T:System.Net.WebException">Thrown when there is a communications failure with the DSML server or when the server is out of available sessions.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.EndSendRequest(System.IAsyncResult)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.EndSendRequest(System.IAsyncResult)" /> method completes an asynchronous send request.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" /> object that contains the results of the request.</returns>
      <param name="asyncResult">An object derived from <see cref="T:System.IAsyncResult" /> that contains state data for this request.</param>
      <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="asyncResult" /> is null.</exception>
      <exception cref="T:System.ArgumentException">Thrown if <paramref name="asyncResult" /> was not returned by the current instance from a call to <see cref="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.BeginSendRequest(System.DirectoryServices.Protocols.DsmlRequestDocument,System.AsyncCallback,System.Object)" />.</exception>
      <exception cref="T:System.IO.IOException">The request did not complete. No stream is available.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.DsmlInvalidDocumentException">Thrown when the response returned by server is not valid.</exception>
      <exception cref="T:System.Net.WebException">Thrown when abort was previously called or when an error occurred while processing the request.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.EndSession">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.EndSession" /> method ends the session with the DSML server and clears the <see cref="P:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.SessionId" /> property.</summary>
      <exception cref="T:System.Net.WebException">Thrown when there is a communications failure with the DSML server.</exception>
      <exception cref="T:System.InvalidOperationException">Thrown when there is no open session.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest)" /> method sends a single <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object that contains the results of the request.</returns>
      <param name="request">A <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object that contains the request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is null (Nothing in Visual Basic).</exception>
      <exception cref="T:System.DirectoryServices.Protocols.ErrorResponseException">The DSML server returned an error response.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.DirectoryOperationException">The operation returned a failure code.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.SendRequest(System.DirectoryServices.Protocols.DsmlRequestDocument)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.SendRequest(System.DirectoryServices.Protocols.DsmlRequestDocument)" /> method sends a <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> object to the DSML server.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.DsmlResponseDocument" /> object that contains the results of the request.</returns>
      <param name="request">A <see cref="T:System.DirectoryServices.Protocols.DsmlRequestDocument" /> object sent to the server.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is null (Nothing in Visual Basic).</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.SessionId">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.SessionId" /> property contains the active session ID.</summary>
      <returns>The active session ID.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.SoapActionHeader">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.SoapActionHeader" /> property contains the SOAP Action Header sent with other HTTP headers.</summary>
      <returns>The SOAP Action Header sent with other HTTP headers.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.Timeout">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.DsmlSoapHttpConnection.Timeout" /> property contains the length of time, in seconds, before the <see cref="T:System.DirectoryServices.Protocols.DsmlSoapHttpConnection" /> times out.</summary>
      <returns>The length of time, in seconds, before the <see cref="T:System.DirectoryServices.Protocols.DsmlSoapHttpConnection" /> times out.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.ErrorResponseCategory">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.ErrorResponseCategory" /> enumeration contains possible DSML error responses.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ErrorResponseCategory.NotAttempted">
      <summary>The DSML provider did not attempt to execute a request element. The value is equal to 0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ErrorResponseCategory.CouldNotConnect">
      <summary>The DSML provider was unable to connect to a server. The value is equal to 1.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ErrorResponseCategory.ConnectionClosed">
      <summary>The DSML provider connected to a server, but the server closed the connection without responding to the request. The value is equal to 2.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ErrorResponseCategory.MalformedRequest">
      <summary>The client failed to specify a request ID for each request. The value is equal to 3.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ErrorResponseCategory.GatewayInternalError">
      <summary>There is an internal gateway error. The value is equal to 4.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ErrorResponseCategory.AuthenticationFailed">
      <summary>Authentication failed. The value is equal to 5.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ErrorResponseCategory.UnresolvableUri">
      <summary>The client provider is unable to resolve the URI to a value that can transferred to the server. The value is equal to 6.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ErrorResponseCategory.Other">
      <summary>An unknown error occurred. The value is equal to 7.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.ErrorResponseException">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.ErrorResponseException" /> class is an exception that occurs when the server returns an &lt;errorResponse&gt;.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ErrorResponseException.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ErrorResponseException.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ErrorResponseException" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ErrorResponseException.#ctor(System.DirectoryServices.Protocols.DsmlErrorResponse)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ErrorResponseException.#ctor(System.DirectoryServices.Protocols.DsmlErrorResponse)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ErrorResponseException" /> class using the specified response object. The default message displayed to the client is "&lt;errorResponse&gt; returned".</summary>
      <param name="response">The <see cref="T:System.DirectoryServices.Protocols.DsmlErrorResponse" /> object returned by the server that corresponds to the &lt;errorResponse&gt;. </param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ErrorResponseException.#ctor(System.DirectoryServices.Protocols.DsmlErrorResponse,System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ErrorResponseException.#ctor(System.DirectoryServices.Protocols.DsmlErrorResponse,System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ErrorResponseException" /> class using the specified response object and message.</summary>
      <param name="response">The <see cref="T:System.DirectoryServices.Protocols.DsmlErrorResponse" /> object returned by the server that corresponds to the &lt;errorResponse&gt;. </param>
      <param name="message">The message displayed to the client when the exception occurs.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ErrorResponseException.#ctor(System.DirectoryServices.Protocols.DsmlErrorResponse,System.String,System.Exception)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ErrorResponseException.#ctor(System.DirectoryServices.Protocols.DsmlErrorResponse,System.String,System.Exception)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ErrorResponseException" /> class using the specified response object, message, and inner exception.</summary>
      <param name="response">The <see cref="T:System.DirectoryServices.Protocols.DsmlErrorResponse" /> object returned by the server that corresponds to the &lt;errorResponse&gt;. </param>
      <param name="message">The message displayed to the client when the exception occurs.</param>
      <param name="inner">The <see cref="P:System.Exception.InnerException" />, if any, that threw the exception.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ErrorResponseException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ErrorResponseException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ErrorResponseException" /> class using the specified serialization data and streaming context.</summary>
      <param name="info">Data required to serialize the parameter.</param>
      <param name="context">The source and destination of the serialized stream associated with the parameter.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ErrorResponseException.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ErrorResponseException.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ErrorResponseException" /> class using the specified message.</summary>
      <param name="message">The message displayed to the client when the exception occurs.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ErrorResponseException.#ctor(System.String,System.Exception)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ErrorResponseException.#ctor(System.String,System.Exception)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ErrorResponseException" /> class using the specified message and inner exception.</summary>
      <param name="message">The message displayed to the client when the exception occurs.</param>
      <param name="inner">The <see cref="P:System.Exception.InnerException" />, if any, that threw the exception.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ErrorResponseException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ErrorResponseException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> method populates the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with the data required to serialize the parameter.</summary>
      <param name="serializationInfo">Data required to serialize the parameter.</param>
      <param name="streamingContext">The source and destination of the serialized stream associated with the parameter.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ErrorResponseException.Response">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ErrorResponseException.Response" /> property contains the <see cref="T:System.DirectoryServices.Protocols.DsmlErrorResponse" /> object that corresponds to the &lt;errorResponse&gt;.</summary>
      <returns>The <see cref="T:System.DirectoryServices.Protocols.DsmlErrorResponse" /> object that corresponds to the &lt;errorResponse&gt;.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.ExtendedDNControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.ExtendedDNControl" /> class requests an extended form of the distinguished name of an Active Directory Domain Services object.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ExtendedDNControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ExtendedDNControl.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ExtendedDNControl" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ExtendedDNControl.#ctor(System.DirectoryServices.Protocols.ExtendedDNFlag)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ExtendedDNControl.#ctor(System.DirectoryServices.Protocols.ExtendedDNFlag)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ExtendedDNControl" /> class using the specified flag.</summary>
      <param name="flag">A value of the <see cref="T:System.DirectoryServices.Protocols.ExtendedDNFlag" /> enumeration that specifies the format of the extended distinguished name.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ExtendedDNControl.Flag">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ExtendedDNControl.Flag" /> property contains one of the values of <see cref="T:System.DirectoryServices.Protocols.ExtendedDNFlag" /> that specifies the format of the extended distinguished name.</summary>
      <returns>One of the values of <see cref="T:System.DirectoryServices.Protocols.ExtendedDNFlag" /> that specifies the format of the extended distinguished name.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ExtendedDNControl.GetValue">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ExtendedDNControl.GetValue" /> method returns the format of the extended distinguished name.</summary>
      <returns>The format of the extended distinguished name.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.ExtendedDNFlag">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.ExtendedDNFlag" /> enumeration specifies the format of an extended distinguished name.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ExtendedDNFlag.HexString">
      <summary>Specifies that the GUID and SID values are returned in hexadecimal format. The value is equal to 0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ExtendedDNFlag.StandardString">
      <summary>Specifies that the GUID and SID values are returned in standard string format. The value is equal to 1.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.ExtendedRequest">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.ExtendedRequest" /> class passes extended LDAP operations to the server.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ExtendedRequest.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ExtendedRequest.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ExtendedRequest" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ExtendedRequest.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ExtendedRequest.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ExtendedRequest" /> class using the specified name.</summary>
      <param name="requestName">The name of the request.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ExtendedRequest.#ctor(System.String,System.Byte[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ExtendedRequest.#ctor(System.String,System.Byte[])" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ExtendedRequest" /> class using the specified name and value.</summary>
      <param name="requestName">The name of the request.</param>
      <param name="requestValue">The value of the request.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ExtendedRequest.RequestName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ExtendedRequest.RequestName" /> property contains the request name.</summary>
      <returns>The request name.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ExtendedRequest.RequestValue">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ExtendedRequest.RequestValue" /> property contains the value of the request.</summary>
      <returns>The value of the request.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ExtendedRequest.ToXmlNode(System.Xml.XmlDocument)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ExtendedRequest.ToXmlNode(System.Xml.XmlDocument)" /> method creates an XML node from the specified <see cref="T:System.Xml.XmlDocument" /> object.</summary>
      <returns>The resulting <see cref="T:System.Xml.XmlElement" /> object.</returns>
      <param name="doc">An <see cref="T:System.Xml.XmlDocument" /> object.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.ExtendedResponse">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.ExtendedResponse" /> class is returned by <see cref="M:System.DirectoryServices.Protocols.DirectoryConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest)" /> as a response to <see cref="T:System.DirectoryServices.Protocols.ExtendedRequest" />.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ExtendedResponse.ResponseName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ExtendedResponse.ResponseName" /> property contains the name of the response returned by the server.</summary>
      <returns>The name of the response returned by the server.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ExtendedResponse.ResponseValue">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ExtendedResponse.ResponseValue" /> property contains the optional response value returned by the server.</summary>
      <returns>The optional response value returned by the server.</returns>
      <exception cref="T:System.DirectoryServices.Protocols.DsmlInvalidDocumentException">The server returned an invalid value.</exception>
    </member>
    <member name="T:System.DirectoryServices.Protocols.LazyCommitControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.LazyCommitControl" /> class instructs the server to return the results of a DS modification command, such as add, delete, or replace, after it has been completed in memory, but before it has been committed to disk.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LazyCommitControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LazyCommitControl.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LazyCommitControl" /> class.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.LdapConnection">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> class creates a TCP/IP or UDP LDAP connection to Microsoft Active Directory Domain Services or an LDAP server. </summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.#ctor(System.DirectoryServices.Protocols.LdapDirectoryIdentifier)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.#ctor(System.DirectoryServices.Protocols.LdapDirectoryIdentifier)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> class using the specified directory identifier. The logon credentials and the Negotiate Authentication are used to connect to the LDAP server.</summary>
      <param name="identifier">An <see cref="T:System.DirectoryServices.Protocols.LdapDirectoryIdentifier" /> object that specifies the server.</param>
      <exception cref="T:System.DirectoryServices.Protocols.LdapException">Thrown if it fails to create a connection block or fails to open a connection to server.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.#ctor(System.DirectoryServices.Protocols.LdapDirectoryIdentifier,System.Net.NetworkCredential)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.#ctor(System.DirectoryServices.Protocols.LdapDirectoryIdentifier,System.Net.NetworkCredential)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> class using the specified directory identifier and network credentials. Negotiate Authentication is used.</summary>
      <param name="identifier">An <see cref="T:System.DirectoryServices.Protocols.LdapDirectoryIdentifier" /> object that specifies the server.</param>
      <param name="credential">A <see cref="T:System.Net.NetworkCredential" /> object that specifies the credentials to use.</param>
      <exception cref="T:System.DirectoryServices.Protocols.LdapException">Thrown if it fails to create a connection block or fails to open a connection to server.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.#ctor(System.DirectoryServices.Protocols.LdapDirectoryIdentifier,System.Net.NetworkCredential,System.DirectoryServices.Protocols.AuthType)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.#ctor(System.DirectoryServices.Protocols.LdapDirectoryIdentifier,System.Net.NetworkCredential,System.DirectoryServices.Protocols.AuthType)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> class using the specified directory identifier, network credentials, and authentication type.</summary>
      <param name="identifier">An <see cref="T:System.DirectoryServices.Protocols.LdapDirectoryIdentifier" /> object that specifies the server.</param>
      <param name="credential">A <see cref="T:System.Net.NetworkCredential" /> object that specifies the credentials to use.</param>
      <param name="authType">A <see cref="T:System.DirectoryServices.Protocols.AuthType" /> values that specifies the type of authentication to use.</param>
      <exception cref="T:System.DirectoryServices.Protocols.LdapException">Thrown if it fails to create a connection block or fails to open a connection to server.</exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">Thrown when <paramref name="authType" /> is out of range.</exception>
      <exception cref="T:System.ArgumentException">Thrown when <paramref name="authType" /> is specified as Anonymous but <paramref name="credential" /> specified credentials.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> class using the specified server.</summary>
      <param name="server">A string specifying the server  which can be a domain name, LDAP server name or dotted strings representing the IP address of the LDAP server. Optionally, this parameter may also include a port number, separated from the right end of the string by a colon (:).</param>
      <exception cref="T:System.DirectoryServices.Protocols.LdapException">Thrown if it fails to create a connection block or fails to open a connection to server.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.Abort(System.IAsyncResult)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.Abort(System.IAsyncResult)" /> method cancels the asynchronous request.</summary>
      <param name="asyncResult">A <see cref="T:System.IAsyncResult" /> object that references the asynchronous request.</param>
      <exception cref="T:System.ObjectDisposedException">The object handle is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is null (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">Thrown if <paramref name="asyncResult" /> was not returned by the corresponding call to <see cref="M:System.DirectoryServices.Protocols.LdapConnection.BeginSendRequest" /></exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapConnection.AuthType">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapConnection.AuthType" /> property contains the supported authentication types.</summary>
      <returns>A supported authentication type.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">Thrown when <paramref name="authType" /> is out of range.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapConnection.AutoBind">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapConnection.AutoBind" /> property specifies whether an automatic bind is allowed.</summary>
      <returns>true if the automatic bind is allowed; otherwise, false.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.BeginSendRequest(System.DirectoryServices.Protocols.DirectoryRequest,System.DirectoryServices.Protocols.PartialResultProcessing,System.AsyncCallback,System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.BeginSendRequest(System.DirectoryServices.Protocols.DirectoryRequest,System.DirectoryServices.Protocols.PartialResultProcessing,System.AsyncCallback,System.Object)" /> method sends data asynchronously to the server.</summary>
      <returns>A <see cref="T:System.IAsyncResult" /> object that references the asynchronous send.</returns>
      <param name="request">A <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object.</param>
      <param name="partialMode">A value from <see cref="T:System.DirectoryServices.Protocols.PartialResultProcessing" /> enumeration that specifies the level of partial result to return.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> function.</param>
      <param name="state">An <see cref="T:System.Object" /> that contains the state data for this request.</param>
      <exception cref="T:System.ObjectDisposedException">The object handle is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is null (Nothing in Visual Basic).</exception>
      <exception cref="T:System.NotSupportedException">Partial results are not supported. </exception>
      <exception cref="T:System.ArgumentException">A callback function must be specified if <paramref name="partialMode" /> is equal to <see cref="F:System.DirectoryServices.Protocols.PartialResultProcessing.ReturnPartialResultsAndNotifyCallback" />.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.BeginSendRequest(System.DirectoryServices.Protocols.DirectoryRequest,System.TimeSpan,System.DirectoryServices.Protocols.PartialResultProcessing,System.AsyncCallback,System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.BeginSendRequest(System.DirectoryServices.Protocols.DirectoryRequest,System.TimeSpan,System.DirectoryServices.Protocols.PartialResultProcessing,System.AsyncCallback,System.Object)" /> method sends data asynchronously to the server.</summary>
      <returns>A <see cref="T:System.IAsyncResult" /> object that references the asynchronous send.</returns>
      <param name="request">A <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object.</param>
      <param name="requestTimeout">The time, in seconds, until the request times out.</param>
      <param name="partialMode">A value from <see cref="T:System.DirectoryServices.Protocols.PartialResultProcessing" /> enumeration that specifies the level of partial result to return.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> function.</param>
      <param name="state">An <see cref="T:System.Object" /> that contains the state data for this request.</param>
      <exception cref="T:System.ObjectDisposedException">The object handle is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is null (Nothing in Visual Basic).</exception>
      <exception cref="T:System.NotSupportedException">Partial results are not supported. </exception>
      <exception cref="T:System.ArgumentException">A callback function must be specified if <paramref name="partialMode" /> is equal to <see cref="F:System.DirectoryServices.Protocols.PartialResultProcessing.ReturnPartialResultsAndNotifyCallback" />.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.Bind">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.Bind" /> method sends an LDAP bind using the current credentials.</summary>
      <exception cref="T:System.ObjectDisposedException">The object handle is not valid.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.LdapException">The error code returned by LDAP does not map to one of the <see cref="T:System.DirectoryServices.Protocols.ResultCode" /> enumeration error codes.</exception>
      <exception cref="T:System.InvalidOperationException">Either the <see cref="P:System.DirectoryServices.Protocols.DirectoryConnection.ClientCertificates" /> property specifies more than one client certificate to send for authentication, or the <see cref="P:System.DirectoryServices.Protocols.LdapConnection.AuthType" /> property is Anonymous and one or more credentials are supplied.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.Bind(System.Net.NetworkCredential)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.Bind(System.Net.NetworkCredential)" /> method sends an LDAP bind using the specified <see cref="T:System.Net.NetworkCredential" />.</summary>
      <param name="newCredential">A <see cref="T:System.Net.NetworkCredential" /> object that specifies the credentials to use.</param>
      <exception cref="T:System.ObjectDisposedException">The object handle is not valid.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.LdapException">The error code returned by LDAP does not map to a <see cref="T:System.DirectoryServices.Protocols.ResultCode" /> enumeration error code.</exception>
      <exception cref="T:System.InvalidOperationException">Either the <see cref="P:System.DirectoryServices.Protocols.DirectoryConnection.ClientCertificates" /> property specifies more than one client certificate to send for authentication, or the <see cref="P:System.DirectoryServices.Protocols.LdapConnection.AuthType" /> property is Anonymous and one or more credentials are supplied.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapConnection.Credential">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapConnection.Credential" /> property contains the network credentials for the object.</summary>
      <returns>The network credentials for the object.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.Dispose">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.Dispose" /> method closes and releases the LDAP handle.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.Dispose(System.Boolean)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.Dispose(System.Boolean)" /> method closes the connection and optionally releases the LDAP handle.</summary>
      <param name="disposing">true if the handle is released when the connection is closed or false if the connection is closed without releasing the handle.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.EndSendRequest(System.IAsyncResult)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.EndSendRequest(System.IAsyncResult)" /> method completes an asynchronous request.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object that contains the results of the request.</returns>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> object that contains state data for this request.</param>
      <exception cref="T:System.ObjectDisposedException">Thrown if the object is already disposed.  The object handle is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is null (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> does not match the <paramref name="asyncResult" /> returned in the <see cref="M:System.DirectoryServices.Protocols.LdapConnection.BeginSendRequest" /> call.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.LdapException">The error code returned by LDAP does not map to a  <see cref="T:System.DirectoryServices.Protocols.ResultCode" /> enumeration error code.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.DirectoryOperationException">Thrown if the server returned a <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object with an error.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.Finalize">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.Finalize" /> method allows an <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object to attempt to free resources and perform other cleanup operations before the <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is reclaimed by garbage collection.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.GetPartialResults(System.IAsyncResult)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.GetPartialResults(System.IAsyncResult)" /> method retrieves partial results of an asynchronous operation.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.PartialResultsCollection" /> object that contains the partial results.</returns>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> object that contains state data for this request.</param>
      <exception cref="T:System.ObjectDisposedException">The object is already disposed. The object handle is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is null (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> does not match the <paramref name="asyncResult" /> returned in the <see cref="M:System.DirectoryServices.Protocols.LdapConnection.BeginSendRequest" /> call.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.LdapException">The error code returned by LDAP does not map to a  <see cref="T:System.DirectoryServices.Protocols.ResultCode" /> enumeration error code.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.DirectoryOperationException">Thrown if the server returned a <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object with an error.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest)" /> method sends a single <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> as an LDAP v3 operation.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object that contains the response from the server.</returns>
      <param name="request">A <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object that contains the request.</param>
      <exception cref="T:System.ObjectDisposedException">The object is already disposed. The object handle is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is null (Nothing in Visual Basic).</exception>
      <exception cref="T:System.NotSupportedException">Request is of type DsmlAuthRequest.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.LdapException">The error code returned by LDAP does not map to a <see cref="T:System.DirectoryServices.Protocols.ResultCode" /> enumeration error code.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.DirectoryOperationException">Thrown if the server returned a <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object with an error.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest,System.TimeSpan)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest,System.TimeSpan)" /> method sends a single <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> as an LDAP v3 operation.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object that contains the response from the server.</returns>
      <param name="request">A <see cref="T:System.DirectoryServices.Protocols.DirectoryRequest" /> object that contains the request.</param>
      <param name="requestTimeout">The length of time, in seconds, until the request times out.</param>
      <exception cref="T:System.ObjectDisposedException">The object is already disposed. The object handle is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is null (Nothing in Visual Basic).</exception>
      <exception cref="T:System.NotSupportedException">Request is of type DsmlAuthRequest.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.LdapException">The error code returned by LDAP does not map to a  the <see cref="T:System.DirectoryServices.Protocols.ResultCode" /> enumeration error code.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.DirectoryOperationException">Thrown if the server returned a <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object with an error.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapConnection.SessionOptions">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapConnection.SessionOptions" /> property contains an <see cref="T:System.DirectoryServices.Protocols.LdapSessionOptions" /> object that specifies the session options.</summary>
      <returns>An <see cref="T:System.DirectoryServices.Protocols.LdapSessionOptions" /> object that specifies the session options.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapConnection.Timeout">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapConnection.Timeout" /> property contains the length of time, in seconds, before the <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> times out.</summary>
      <returns>The length of time, in seconds, before the <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> times out.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.LdapDirectoryIdentifier">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.LdapDirectoryIdentifier" /> class creates a directory identifier for one or more LDAP servers.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapDirectoryIdentifier" /> class using the specified server.</summary>
      <exception cref="T:System.ArgumentException">Thrown if <paramref name="server" /> has space.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.#ctor(System.String,System.Boolean,System.Boolean)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.#ctor(System.String,System.Boolean,System.Boolean)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapDirectoryIdentifier" /> class using the specified server and properties.  This constructor creates an identifier that represents a set of LDAP servers.</summary>
      <param name="fullyQualifiedDnsHostName">true if each element in servers is a fully-qualified DNS host name. If false, an element can be an IP address, a DNS domain or host name, or null. If null, it represents the identity of any domain controller in the domain associated with the computer account.</param>
      <exception cref="T:System.ArgumentException">Thrown if <paramref name="server" /> contains only white space.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.#ctor(System.String,System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.#ctor(System.String,System.Integer)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapDirectoryIdentifier" /> class using the specified server and properties.  This constructor creates an identifier that represents a set of LDAP servers.</summary>
      <param name="portNumber">The port number to use when connecting to the server.</param>
      <exception cref="T:System.ArgumentException">Thrown if <paramref name="server" /> has space.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.#ctor(System.String,System.Int32,System.Boolean,System.Boolean)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.#ctor(System.String,System.Int32,System.Boolean,System.Boolean)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapDirectoryIdentifier" /> class using the specified server and properties.  This constructor creates an identifier that represents a set of LDAP servers.</summary>
      <param name="portNumber">The port number to be used when connecting to the server.</param>
      <param name="fullyQualifiedDnsHostName">true if each element in servers is a fully-qualified DNS host name. If false, an element can be an IP address, a DNS domain or host name, or null. If null, it represents the identity of any domain controller in the domain associated with the computer account.</param>
      <exception cref="T:System.ArgumentException">Thrown if <paramref name="server" /> has space.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.#ctor(System.String[],System.Boolean,System.Boolean)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.#ctor(System.String,System.Boolean,System.Boolean)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapDirectoryIdentifier" /> class using the specified server and properties.  This constructor creates an identifier that represents a set of LDAP servers.</summary>
      <param name="fullyQualifiedDnsHostName">true if each element in servers is a fully-qualified DNS host name. If false, an element can be an IP address, a DNS domain or host name, or null. If null, it represents the identity of any domain controller in the domain associated with the computer account.</param>
      <exception cref="T:System.ArgumentException">Thrown if <paramref name="server" /> has space.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.#ctor(System.String[],System.Int32,System.Boolean,System.Boolean)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.#ctor(System.String[],System.Int32,System.Boolean,System.Boolean)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapDirectoryIdentifier" /> class using the specified server and properties.  This constructor creates an identifier that represents a set of LDAP servers.</summary>
      <param name="portNumber">the port number to be used when connecting to a server.</param>
      <param name="fullyQualifiedDnsHostName">true if each element in servers is a fully-qualified DNS host name. If false, an element can be an IP address, a DNS domain or host name, or null. If null, it represents the identity of any domain controller in the domain associated with the computer account.</param>
      <exception cref="T:System.ArgumentException">Thrown if <paramref name="server" /> has space.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.Connectionless">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.Connectionless" /> property specifies that the connection is User Datagram Protocol (UDP). </summary>
      <returns>This property is true if the connection is UDP. This property is false if the LDAP connection is over TCP/IP.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.FullyQualifiedDnsHostName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.FullyQualifiedDnsHostName" /> property specifies that a server name is a fully-qualified DNS host name. </summary>
      <returns>This property is true if a server name is a fully-qualified DNS host name.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.PortNumber">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.PortNumber" /> property contains the portnumber to be used to connect to the server.</summary>
      <returns>Returns an integer representing the port number.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.Servers">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapDirectoryIdentifier.Servers" /> property contains the set of servers this object identifies.</summary>
      <returns>The set of servers this object identifies.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.LdapException">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.LdapException" /> class is an exception that occurs when LDAP returns an error code not included in <see cref="T:System.DirectoryServices.Protocols.ResultCode" />.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapException.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapException.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapException" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapException.#ctor(System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapException.#ctor(System.Int32)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapException" /> class using the specified error code. The default message displayed to the client is "Communications error with the LDAP server".</summary>
      <param name="errorCode">The error code returned by the LDAP implementation.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapException.#ctor(System.Int32,System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapException.#ctor(System.Int32,System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapException" /> class using the specified error code and message.</summary>
      <param name="errorCode">The error code returned by the LDAP implementation.</param>
      <param name="message">The message displayed to the client when the exception occurs.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapException.#ctor(System.Int32,System.String,System.Exception)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapException.#ctor(System.Int32,System.String,System.Exception)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapException" /> class using the specified error code, message, and inner exception.</summary>
      <param name="errorCode">The error code returned by the LDAP implementation.</param>
      <param name="message">The message displayed to the client when the exception occurs.</param>
      <param name="inner">The inner exception, if any, that threw the exception.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapException.#ctor(System.Int32,System.String,System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapException.#ctor(System.Int32,System.String,System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapException" /> class using the specified error code, message, and LDAP server error message.</summary>
      <param name="errorCode">The error code returned by the LDAP implementation.</param>
      <param name="serverErrorMessage">The LDAP server error message.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapException" /> class using the specified serialization data and streaming context.</summary>
      <param name="info">Data required to serialize the parameter.</param>
      <param name="context">The source and destination of the serialized stream associated with the parameter.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapException.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapException.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapException" /> class using the specified message.</summary>
      <param name="message">The message displayed to the client when the exception occurs.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapException.#ctor(System.String,System.Exception)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapException.#ctor(System.String,System.Exception)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.LdapException" /> class using the specified message and inner exception.</summary>
      <param name="message">The message displayed to the client when the exception occurs.</param>
      <param name="inner">The inner exception, if any, that threw the exception.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapException.ErrorCode">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapException.ErrorCode" /> property contains the LDAP error code.</summary>
      <returns>The LDAP error code.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> method populates the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with the data required to serialize the parameter.</summary>
      <param name="serializationInfo">Data required to serialize the parameter.</param>
      <param name="streamingContext">The source and destination of the serialized stream associated with the parameter.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapException.PartialResults">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapException.PartialResults" /> property contains a <see cref="T:System.DirectoryServices.Protocols.PartialResultsCollection" /> object that contains partial results.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.PartialResultsCollection" /> object that contains partial results.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapException.ServerErrorMessage">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapException.ServerErrorMessage" /> property contains a <see cref="T:System.String" /> that contains the LDAP server error message (if any exists).</summary>
      <returns>A <see cref="T:System.String" /> that contains the LDAP server error message (if any).</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.LdapSessionOptions">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.LdapSessionOptions" /> class is used to get or set various LDAP session options.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.AutoReconnect">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.AutoReconnect" /> property specifies if auto-reconnect is enabled. </summary>
      <returns>This property is true if auto-reconnect is enabled or false if it is not.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.DomainName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.DomainName" /> property returns the domain to which this connection is bound.</summary>
      <returns>The domain to which this connection is bound.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapSessionOptions.FastConcurrentBind">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapSessionOptions.FastConcurrentBind" /> method enables support for fast concurrent binds.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
      <exception cref="T:System.PlatformNotSupportedException">This method does not work on the current platform.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.LdapException">An LDAP error occurred.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.HostName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.HostName" /> property returns the name of the LDAP server associated with the connection.</summary>
      <returns>The name of the LDAP server associated with the connection.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.HostReachable">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.HostReachable" /> property specifies whether the host is reachable. </summary>
      <returns>This property is true if the host is reachable or false if it is not.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.LocatorFlag">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.LocatorFlag" /> property specifies one of the values of the <see cref="T:System.DirectoryServices.Protocols.LocatorFlags" /> enumeration that are used when locating a domain controller.</summary>
      <returns>One of the values of the <see cref="T:System.DirectoryServices.Protocols.LocatorFlags" /> enumeration that are used when locating a domain controller.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.PingKeepAliveTimeout">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.PingKeepAliveTimeout" /> property contains a <see cref="T:System.TimeSpan" /> object that specifies the minimum number of seconds the client waits, after the last response from the server, before sending a keep-alive ping.</summary>
      <returns>A <see cref="T:System.TimeSpan" /> object that specifies the minimum number of seconds the client waits, after the last response from the server, before sending a keep-alive ping.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
      <exception cref="T:System.ArgumentException">The specified time span is less than <see cref="F:System.TimeSpan.Zero" />.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.PingLimit">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.PingLimit" /> property contains the number of unanswered pings that the client sends before closing a connection.</summary>
      <returns>The number of unanswered pings that the client sends before closing a connection.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
      <exception cref="T:System.ArgumentException">The specified value is less than zero.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.PingWaitTimeout">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.PingWaitTimeout" /> property contains a <see cref="T:System.TimeSpan" /> object that specifies the number of milliseconds that the client waits for the response to come back after sending a ping.</summary>
      <returns>A <see cref="T:System.TimeSpan" /> object that specifies the number of milliseconds that the client waits for the response to come back after sending a ping.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
      <exception cref="T:System.ArgumentException">The specified time span is less than <see cref="F:System.TimeSpan.Zero" />.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.ProtocolVersion">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.ProtocolVersion" /> property specifies the LDAP protocol version to use.</summary>
      <returns>The LDAP protocol version to use.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.QueryClientCertificate">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.QueryClientCertificate" /> property contains a <see cref="T:System.DirectoryServices.Protocols.QueryClientCertificateCallback" /> object that specifies the default callback function used to specify client certificates when establishing an SSL connection.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.QueryClientCertificateCallback" /> object that specifies the default callback function used to specify client certificates when establishing an SSL connection.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.ReferralCallback">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.ReferralCallback" /> property contains a <see cref="T:System.DirectoryServices.Protocols.ReferralCallback" /> object that specifies the default callback function used when chasing referrals.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.ReferralCallback" /> object that specifies the default callback function used when chasing referrals.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.ReferralChasing">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.ReferralChasing" /> property contains a <see cref="T:System.DirectoryServices.ReferralChasingOption" /> object that specifies how the LDAP library follows referrals returned by LDAP servers.</summary>
      <returns>A <see cref="T:System.DirectoryServices.ReferralChasingOption" /> object that specifies how the LDAP library follows referrals returned by LDAP servers.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The specified value is not defined in <see cref="T:System.DirectoryServices.ReferralChasingOption" />.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.ReferralHopLimit">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.ReferralHopLimit" /> property specifies the number of hops allowed when chasing referrals.</summary>
      <returns>The number of hops allowed when chasing referrals.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
      <exception cref="T:System.ArgumentException">The specified value is less than zero.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.RootDseCache">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.RootDseCache" /> property enables the internal RootDSE cache. </summary>
      <returns>This property is true if the cache is enabled or false if it is not.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.SaslMethod">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.SaslMethod" /> property specifies the preferred Simple Authentication and Security Layer (SASL) binding method.</summary>
      <returns>The preferred SASL binding method.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.Sealing">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.Sealing" /> property enables Kerberos encryption. </summary>
      <returns>This property is true if Kerberos encryption is enabled or false if it is not.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.SecureSocketLayer">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.SecureSocketLayer" /> property enables secure socket layer on connection. </summary>
      <returns>This property is true if secure socket layer is enabled or false if it is not.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.SecurityContext">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.SecurityContext" /> property specifies the security context associated with the current connection.</summary>
      <returns>The security context associated with the current connection.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.SendTimeout">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.SendTimeout" /> property contains a <see cref="T:System.TimeSpan" /> object that specifies the send time-out.</summary>
      <returns>A <see cref="T:System.TimeSpan" /> object that specifies the send time-out.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
      <exception cref="T:System.ArgumentException">The value specified is less than <see cref="F:System.TimeSpan.Zero" />.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.Signing">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.Signing" /> property enables Kerberos encryption. </summary>
      <returns>This property is true if Kerberos encryption is enabled or false if it is not.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.SslInformation">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.SslInformation" /> property contains a <see cref="T:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation" /> object that contains data about the current secure connection.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation" /> object that contains data about the current secure connection.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.SspiFlag">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.SspiFlag" /> property specifies the flags to pass to the Security Support Provider Interface (SSPI) InitializeSecurityContext function.  For more information about the InitializeSecurityContext function, see the InitializeSecurityContext function topic in the MSDN library at http://msdn.microsoft.com/library.</summary>
      <returns>The flags to pass to the SSPI InitializeSecurityContext function.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapSessionOptions.StartTransportLayerSecurity(System.DirectoryServices.Protocols.DirectoryControlCollection)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapSessionOptions.StartTransportLayerSecurity(System.DirectoryServices.Protocols.DirectoryControlCollection)" /> method starts transport layer security encryption.</summary>
      <param name="controls">A <see cref="T:System.DirectoryServices.Protocols.DirectoryControlCollection" /> object that contains the controls to use.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.TlsOperationException">The request failed.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.LdapException">An LDAP error occurred.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.LdapSessionOptions.StopTransportLayerSecurity">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.LdapSessionOptions.StopTransportLayerSecurity" /> method ends transport layer security encryption.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
      <exception cref="T:System.DirectoryServices.Protocols.TlsOperationException">The request failed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.TcpKeepAlive">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.TcpKeepAlive" /> property enables TCP keep-alive. </summary>
      <returns>This property is true if TCP keep-alive is enabled or false if it is not.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.LdapSessionOptions.VerifyServerCertificate">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.LdapSessionOptions.VerifyServerCertificate" /> property contains a <see cref="T:System.DirectoryServices.Protocols.VerifyServerCertificateCallback" /> object that specifies the default callback method to use to verify server certificates when an SSL connection is established.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.VerifyServerCertificateCallback" /> object that specifies the default callback method to use to verify server certificates when an SSL connection is established.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object is already disposed.</exception>
    </member>
    <member name="T:System.DirectoryServices.Protocols.LocatorFlags">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.LocatorFlags" /> enumeration specifies data required to locate a domain controller.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.None">
      <summary>No data required. The value is equal to 0 or 0x0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.ForceRediscovery">
      <summary>Forces the cached domain controller to be ignored. The value is equal to 1 or 0x1.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.DirectoryServicesRequired">
      <summary>Requires that the returned domain controller support Windows 2000 or later. The value is equal to 16 or 0x10.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.DirectoryServicesPreferred">
      <summary>Attempts to find a domain controller that supports directory service functions (Windows 2000 or later). The value is equal to 32 or 0x20.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.GCRequired">
      <summary>Requires that the returned domain controller be a global catalog server for the forest of domains with this domain as the root. The value is equal to 64 or 0x40.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.PdcRequired">
      <summary>Requires that the returned domain controller be the primary domain controller for the domain. The value is equal to 128 or 0x80.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.IPRequired">
      <summary>Sets the Internet protocol address of the domain controller in the DomainControllerAddress member of DomainControllerInfo. The value is equal to 512 or 0x200.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.KdcRequired">
      <summary>Requires that the returned domain controller be currently running the Kerberos Key Distribution Center service. The value is equal to 1024 or 0x400.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.TimeServerRequired">
      <summary>Requires that the returned domain controller be currently running the Windows Time Service. The value is equal to 2048 or 0x800.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.WriteableRequired">
      <summary>Requires that the returned domain controller be writable. The value is equal to 4096 or 0x1000.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.GoodTimeServerPreferred">
      <summary>Attempts to find a domain controller that is a reliable time server. The value is equal to 8192 or 0x2000.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.AvoidSelf">
      <summary>Specifies that the domain controller returned should not be the current computer. The value is equal to 16384 or 0x4000.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.OnlyLdapNeeded">
      <summary>Specifies that the server returned is an LDAP server. The value is equal to 32768 or 0x8000.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.IsFlatName">
      <summary>Specifies that the DomainName parameter is a flat name. The value is equal to 65536 or 0x10000.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.IsDnsName">
      <summary>Specifies that the DomainName parameter is a DNS name. The value is equal to 131072 or 0x20000.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.ReturnDnsName">
      <summary>Specifies that the name returned should be a DNS name. The value is equal to 1073741824 or 0x40000000.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.LocatorFlags.ReturnFlatName">
      <summary>Specifies that the name should be a flat name. The value is equal to 2147483648 or 0x80000000.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.ModifyDNRequest">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.ModifyDNRequest" /> class modifies the distinguished name of an object. This class moves an object to a new parent and/or renames the object.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ModifyDNRequest.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ModifyDNRequest.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ModifyDNRequest" /> object.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ModifyDNRequest.#ctor(System.String,System.String,System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ModifyDNRequest.#ctor(System.String,System.String,System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ModifyDNRequest" /> object using the specified object name, parent name, and new object name.</summary>
      <param name="distinguishedName">The current distinguished name of the object.</param>
      <param name="newParentDistinguishedName">The distinguished name of the new parent of the object.</param>
      <param name="newName">The new distinguished name of the object.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ModifyDNRequest.DeleteOldRdn">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ModifyDNRequest.DeleteOldRdn" /> property specifies whether to delete the old relative distinguished name (RDN) of the object. </summary>
      <returns>This property is true if the RDN should be deleted or false if it should not be deleted.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ModifyDNRequest.DistinguishedName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ModifyDNRequest.DistinguishedName" /> property contains the distinguished name of the object.</summary>
      <returns>The distinguished name of the object.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ModifyDNRequest.NewName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ModifyDNRequest.NewName" /> property contains the new object name.</summary>
      <returns>The new object name.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ModifyDNRequest.NewParentDistinguishedName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ModifyDNRequest.NewParentDistinguishedName" /> property contains the distinguished name of the new parent of the object.</summary>
      <returns>The distinguished name of the new parent of the object.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ModifyDNRequest.ToXmlNode(System.Xml.XmlDocument)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ModifyDNRequest.ToXmlNode(System.Xml.XmlDocument)" /> method creates an XML node from the specified <see cref="T:System.Xml.XmlDocument" /> object.</summary>
      <returns>The resulting <see cref="T:System.Xml.XmlElement" /> object.</returns>
      <param name="doc">A <see cref="T:System.Xml.XmlDocument" /> object.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.ModifyDNResponse">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.ModifyDNResponse" /> class is returned by <see cref="M:System.DirectoryServices.Protocols.DirectoryConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest)" /> as a response to <see cref="T:System.DirectoryServices.Protocols.ModifyDNRequest" />. </summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.ModifyRequest">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.ModifyRequest" /> class modifies the attributes of an existing directory entry.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ModifyRequest.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ModifyRequest.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ModifyRequest" /> class. This constructor creates an empty request.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ModifyRequest.#ctor(System.String,System.DirectoryServices.Protocols.DirectoryAttributeModification[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ModifyRequest.#ctor(System.String,System.DirectoryServices.Protocols.DirectoryAttributeModification[])" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ModifyRequest" /> class using the specified distinguished name and modifications.</summary>
      <param name="distinguishedName">The distinguished name of the object to modify.</param>
      <param name="modifications">An array of <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModification" /> objects that specify the modifications.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ModifyRequest.#ctor(System.String,System.DirectoryServices.Protocols.DirectoryAttributeOperation,System.String,System.Object[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ModifyRequest.#ctor(System.String,System.DirectoryServices.Protocols.DirectoryAttributeOperation,System.String,System.Object[])" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ModifyRequest" /> class using the specified distinguished name, modification, attribute name, and values.</summary>
      <param name="distinguishedName">The distinguished name of the object to be modified.</param>
      <param name="operation">A <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeOperation" /> object that specifies the modification to perform.</param>
      <param name="attributeName">The name of the attribute to modify. This parameter cannot be null.</param>
      <param name="values">The new values for the attribute.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ModifyRequest.DistinguishedName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ModifyRequest.DistinguishedName" /> property contains the distinguished name of the object to modify.</summary>
      <returns>The distinguished name of the object to modify.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ModifyRequest.Modifications">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ModifyRequest.Modifications" /> property contains a <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection" /> object that specifies a list of attribute modifications.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.DirectoryAttributeModificationCollection" /> object that specifies a list of attribute modifications.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ModifyRequest.ToXmlNode(System.Xml.XmlDocument)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ModifyRequest.ToXmlNode(System.Xml.XmlDocument)" /> method creates an XML node from the specified <see cref="T:System.Xml.XmlDocument" /> object.</summary>
      <returns>The resulting <see cref="T:System.Xml.XmlElement" /> object.</returns>
      <param name="doc">A <see cref="T:System.Xml.XmlDocument" /> object.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.ModifyResponse">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.ModifyResponse" /> class is returned by <see cref="M:System.DirectoryServices.Protocols.DirectoryConnection.SendRequest(System.DirectoryServices.Protocols.DirectoryRequest)" /> as a response to <see cref="T:System.DirectoryServices.Protocols.ModifyRequest" />.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.NotifyOfNewConnectionCallback">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.NotifyOfNewConnectionCallback" /> delegate is called if a new connection was created while chasing a referral.</summary>
      <returns>true if the connection is to be cached.  false if not required to cache the connection.</returns>
      <param name="primaryConnection">A <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object that specifies the primary connection.</param>
      <param name="referralFromConnection">A <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object that specifies the connection which provided the referral.</param>
      <param name="newDistinguishedName">A <see cref="T:System.String" /> object that specifies the distinguished name of the desired entry in the referred server.</param>
      <param name="identifier">A <see cref="T:System.DirectoryServices.Protocols.LdapDirectoryIdentifier" /> object that specifies the identifier the referred LDAP directory.</param>
      <param name="newConnection">A <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object that specifies the new connection created in the course of chasing a referral.</param>
      <param name="credential">A <see cref="T:System.Net.NetworkCredential" /> object that specifies the credentials to pass to the referred server.</param>
      <param name="currentUserToken">The LUID of the current user.</param>
      <param name="errorCodeFromBind">A <see cref="T:System.Int32" /> object that specifies the error code from the bind operation.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.PageResultRequestControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.PageResultRequestControl" /> class instructs the server to return the search result in the specified page size.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.PageResultRequestControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.PageResultRequestControl.#ctor" /> constructor creates a new instance of the <see cref="T:System.DirectoryServices.Protocols.PageResultRequestControl" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.PageResultRequestControl.#ctor(System.Byte[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.PageResultRequestControl.#ctor(System.Byte[])" /> constructor creates a new instance of the <see cref="T:System.DirectoryServices.Protocols.PageResultRequestControl" /> class using the specified page search cookie.</summary>
      <param name="cookie">Specifies a cookie used for subsequent paged results search calls.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.PageResultRequestControl.#ctor(System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.PageResultRequestControl.#ctor(System.Int32)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.PageResultRequestControl" /> class using the requested page size.</summary>
      <param name="pageSize">The requested page size.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.PageResultRequestControl.Cookie">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.PageResultRequestControl.Cookie" /> property specifies the page search cookie used for subsequent paged results search calls.</summary>
      <returns>The page search cookie used for subsequent paged results search calls.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.PageResultRequestControl.GetValue">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.PageResultRequestControl.GetValue" /> method returns the page size and cookie associated with this control.</summary>
      <returns>The page size and cookie associated with this control.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.PageResultRequestControl.PageSize">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.PageResultRequestControl.PageSize" /> property specifies the requested page size.</summary>
      <returns>The requested page size.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.PageResultResponseControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.PageResultResponseControl" /> class is a directory control used to pass page data from the server to the client.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.PageResultResponseControl.Cookie">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.PageResultResponseControl.Cookie" /> property contains the page search cookie returned by the server.  </summary>
      <returns>The page search cookie returned by the server.  If the cookie is empty, then the paged search completed.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.PageResultResponseControl.TotalCount">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.PageResultResponseControl.TotalCount" /> property contains the estimated result set size.</summary>
      <returns>The estimated value of the total count of the entries returned.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.PartialResultProcessing">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.PartialResultProcessing" /> enumeration specifies the required type of partial results processing.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.PartialResultProcessing.NoPartialResultSupport">
      <summary>The application will not retrieve partial results. The value is equal to 0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.PartialResultProcessing.ReturnPartialResults">
      <summary>The application will retrieve partial results. The value is equal to 1.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.PartialResultProcessing.ReturnPartialResultsAndNotifyCallback">
      <summary>An application will retrieve partial results and use the callback mechanism. The value is equal to 2.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.PartialResultsCollection">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.PartialResultsCollection" /> class represents any partial results returned from a not-yet-completed asynchronous operation.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.PartialResultsCollection.Contains(System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.PartialResultsCollection.Contains(System.Object)" /> method determines whether this <see cref="T:System.DirectoryServices.Protocols.PartialResultsCollection" /> object contains a specified <see cref="T:System.Object" />.</summary>
      <returns>true if the specified <see cref="T:System.Object" /> is part of the collection or false if it is not.</returns>
      <param name="value">The <see cref="T:System.Object" /> for which to search.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.PartialResultsCollection.CopyTo(System.Object[],System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.PartialResultsCollection.CopyTo(System.Object[],System.Int32)" /> method copies the entire collection to a one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="values">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.DirectoryServices.Protocols.PartialResultsCollection" /> object.</param>
      <param name="index">The zero-based index of <paramref name="values" /> where copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="values" /> is multidimensional-or-The number of elements in the source <see cref="T:System.DirectoryServices.Protocols.PartialResultsCollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="values" />.</exception>
      <exception cref="T:System.InvalidCastException">The type of the source <see cref="T:System.DirectoryServices.Protocols.PartialResultsCollection" /> cannot be cast automatically to the type of the destination <paramref name="values" />.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.PartialResultsCollection.IndexOf(System.Object)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.PartialResultsCollection.IndexOf(System.Object)" /> method returns the zero-based index of the first occurrence of the specified <see cref="T:System.Object" /> in the <see cref="T:System.DirectoryServices.Protocols.PartialResultsCollection" />.</summary>
      <returns>The index value of the specified <see cref="T:System.Object" />, if found, or -1 if the specified object is not found.</returns>
      <param name="value">The <see cref="T:System.Object" /> for which the index is returned.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.PartialResultsCollection.Item(System.Int32)">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.PartialResultsCollection.Item(System.Int32)" /> property contains the <see cref="T:System.Object" /> at the specified index.</summary>
      <returns>The <see cref="T:System.Object" /> at the specified index.</returns>
      <param name="index">The index value of the <see cref="T:System.Object" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero or <paramref name="index" /> is equal to or greater than the number of items in the collection.</exception>
    </member>
    <member name="T:System.DirectoryServices.Protocols.PermissiveModifyControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.PermissiveModifyControl" /> class is used to modify the behavior of a <see cref="T:System.DirectoryServices.Protocols.ModifyRequest" /> object.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.PermissiveModifyControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.PermissiveModifyControl.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.PermissiveModifyControl" /> class.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.QueryClientCertificateCallback">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.QueryClientCertificateCallback" /> delegate gets or sets the default callback function used to specify the client certificates while establishing an SSL connection.</summary>
      <returns>The default callback function used to specify the client certificates while establishing an SSL connection.</returns>
      <param name="connection">A <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object that specifies the connection.</param>
      <param name="trustedCAs">An array of <see cref="T:System.Byte" /> arrays that specifies the trusted certification authorities (CAs).</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.QueryForConnectionCallback">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.QueryForConnectionCallback" /> delegate determines whether there is a cached connection available for use.</summary>
      <returns>A cached <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> is returned, if there is one available. If no cached connection is available, NULL is returned.</returns>
      <param name="primaryConnection">A <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object that specifies the primary connection.</param>
      <param name="referralFromConnection">A <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object that specifies the connection that provides the referral.</param>
      <param name="newDistinguishedName">A <see cref="T:System.String" /> object that specifies the distinguished name (DN) of the desired entry in the referred server.</param>
      <param name="identifier">A <see cref="T:System.DirectoryServices.Protocols.LdapDirectoryIdentifier" /> object that specifies the identifier for the referred LDAP directory.</param>
      <param name="credential">A <see cref="T:System.Net.NetworkCredential" /> object that specifies the credentials to pass to the referred server.</param>
      <param name="currentUserToken">The LUID for the current user.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.QuotaControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.QuotaControl" /> class is used to pass the security identifier (SID) of a security principle to retrieve quota-related data.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.QuotaControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.QuotaControl.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.QuotaControl" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.QuotaControl.#ctor(System.Security.Principal.SecurityIdentifier)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.QuotaControl.#ctor(System.Security.Principal.SecurityIdentifier)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.QuotaControl" /> class using the specified SID.</summary>
      <param name="querySid">The SID of the security principle.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.QuotaControl.GetValue">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.QuotaControl.GetValue" /> method returns the SID associated with this query.</summary>
      <returns>The SID associated with this query.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.QuotaControl.QuerySid">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.QuotaControl.QuerySid" /> property contains the SID of the security principle.</summary>
      <returns>The SID of the security principle.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.ReferralCallback">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.ReferralCallback" /> class contains delegates used as referral callback methods.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ReferralCallback.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ReferralCallback.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ReferralCallback" /> class.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ReferralCallback.DereferenceConnection">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ReferralCallback.DereferenceConnection" /> property contains a <see cref="T:System.DirectoryServices.Protocols.DereferenceConnectionCallback" /> object.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.DereferenceConnectionCallback" /> object.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ReferralCallback.NotifyNewConnection">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ReferralCallback.NotifyNewConnection" /> property contains a <see cref="T:System.DirectoryServices.Protocols.NotifyOfNewConnectionCallback" /> object.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.NotifyOfNewConnectionCallback" /> object.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.ReferralCallback.QueryForConnection">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.ReferralCallback.QueryForConnection" /> property contains a <see cref="T:System.DirectoryServices.Protocols.QueryForConnectionCallback" /> object.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.QueryForConnectionCallback" /> object.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.ReferralChasingOptions">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.ReferralChasingOptions" /> enumeration specifies if and how referral chasing is pursued.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ReferralChasingOptions.None">
      <summary>Never chase the referred-to server. Setting this option prevents a client from contacting other servers in a referral process. The value is equal to 0 or 0x0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ReferralChasingOptions.Subordinate">
      <summary>Chase only subordinate referrals which are a subordinate naming context in a directory tree. The ADSI LDAP provider always turns off this flag for paged searches. The value is equal to 32 or 0x20.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ReferralChasingOptions.External">
      <summary>Chase external referrals. The value is equal to 64 or 0x40.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ReferralChasingOptions.All">
      <summary>Chase referrals of either the subordinate or external type. The value is equal to 96 or 0x60.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.ResultCode">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.ResultCode" /> enumeration specifies the operation result codes.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.Success">
      <summary>The call completed successfully. The value is equal to 0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.OperationsError">
      <summary>An operation error occurred. The value is equal to 1.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.ProtocolError">
      <summary>A protocol error occurred. The value is equal to 2.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.TimeLimitExceeded">
      <summary>The time limit set by the server-side time limit parameter was exceeded. The value is equal to 3.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.SizeLimitExceeded">
      <summary>The size limit was exceeded. The value is equal to 4.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.CompareFalse">
      <summary>The attribute and known values do not match. The value is equal to 5.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.CompareTrue">
      <summary>The attribute and known values match. The value is equal to 6.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.AuthMethodNotSupported">
      <summary>The authentication method is not supported. The value is equal to 7.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.StrongAuthRequired">
      <summary>Strong authentication is required for this operation. The value is equal to 8.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.ReferralV2">
      <summary>Only partial results and referrals were received. The value is equal to 9.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.Referral">
      <summary>A referral was returned from the server. The value is equal to 10.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.AdminLimitExceeded">
      <summary>The administration limit on the server was exceeded. The value is equal to 11.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.UnavailableCriticalExtension">
      <summary>The server does not support the control. The control is critical. The value is equal to 12.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.ConfidentialityRequired">
      <summary>Confidentiality is required for this operation. The value is equal to 13.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.SaslBindInProgress">
      <summary>Simple Authentication and Security Layer (SASL) bind is in progress. The value is equal to 14.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.NoSuchAttribute">
      <summary>The requested attribute does not exist. The value is equal to 16.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.UndefinedAttributeType">
      <summary>The type is not defined. The value is equal to 17.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.InappropriateMatching">
      <summary>The matching rule does not apply to the specified attribute type. The value is equal to 18.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.ConstraintViolation">
      <summary>A value in the request does not comply with certain constraints. The value is equal to 19.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.AttributeOrValueExists">
      <summary>The attribute exists or the value has been assigned. The value is equal to 20.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.InvalidAttributeSyntax">
      <summary>The syntax is invalid. The value is equal to 21.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.NoSuchObject">
      <summary>The object does not exist. The value is equal to 32.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.AliasProblem">
      <summary>The alias is invalid. The value is equal to 33.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.InvalidDNSyntax">
      <summary>The distinguished name contains invalid syntax. The value is equal to 34.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.AliasDereferencingProblem">
      <summary>A problem occurred when dereferencing the alias. The value is equal to 36.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.InappropriateAuthentication">
      <summary>The authentication that was provided was not appropriate. The value is equal to 48.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.InsufficientAccessRights">
      <summary>The user has insufficient access rights. The value is equal to 50.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.Busy">
      <summary>The server is busy. The value is equal to 51.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.Unavailable">
      <summary>The server is unavailable. The value is equal to 52.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.UnwillingToPerform">
      <summary>The server cannot handle directory requests. The value is equal to 53.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.LoopDetect">
      <summary>The chain of referrals has looped back to a referring server. The value is equal to 54.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.SortControlMissing">
      <summary>The search requires a SORT control. The value is equal to 60.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.OffsetRangeError">
      <summary>The search results exceed the specified offset range. The value is equal to 61.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.NamingViolation">
      <summary>A naming violation occurred. The value is equal to 64.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.ObjectClassViolation">
      <summary>An object class violation occurred. The value is equal to 65.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.NotAllowedOnNonLeaf">
      <summary>This operation is not allowed on a non-leaf object. The value is equal to 66.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.NotAllowedOnRdn">
      <summary>This operation is not allowed on a relative distinguished name (RDN). The value is equal to 67.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.EntryAlreadyExists">
      <summary>The object already exists. The value is equal to 68.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.ObjectClassModificationsProhibited">
      <summary>The request is attempting to modify an object class that cannot be modified. The value is equal to 69.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.ResultsTooLarge">
      <summary>The returned results are too large. The value is equal to 70.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.AffectsMultipleDsas">
      <summary>Multiple directory service agents are affected. The value is equal to 71.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.VirtualListViewError">
      <summary>An error occurred when attempting to perform a requested Virtual List View operation. The value is equal to 76.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.ResultCode.Other">
      <summary>An unknown error occurred. The value is equal to 80.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SearchOption">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SearchOption" /> enumeration specifies the search options which define how the search will behave.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SearchOption.DomainScope">
      <summary>Prevents referrals from being generated when the search results are returned. The value is equal to 1.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SearchOption.PhantomRoot">
      <summary>Instructs the server to search all naming contexts that are subordinate to the search base. The value is equal to 2.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SearchOptionsControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SearchOptionsControl" /> class is used to pass flags to the server to control search behavior.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchOptionsControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchOptionsControl.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.SearchOptionsControl" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchOptionsControl.#ctor(System.DirectoryServices.Protocols.SearchOption)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchOptionsControl.#ctor(System.DirectoryServices.Protocols.SearchOption)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.SearchOptionsControl" /> class using the specified search options.</summary>
      <param name="flags">The <see cref="T:System.DirectoryServices.Protocols.SearchOption" /> enumeration value that specifies the search behavior.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchOptionsControl.GetValue">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchOptionsControl.GetValue" /> method returns search option for this object.</summary>
      <returns>The search option associated with this control.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchOptionsControl.SearchOption">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchOptionsControl.SearchOption" /> property contains one of the values of <see cref="T:System.DirectoryServices.Protocols.SearchOption" /> enumeration that specifies the search behavior.</summary>
      <returns>One of the values of <see cref="T:System.DirectoryServices.Protocols.SearchOption" /> enumeration that specifies the search behavior.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value is not one of the values of <see cref="T:System.DirectoryServices.Protocols.SearchOption" />.</exception>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SearchRequest">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SearchRequest" /> class initiates a search operation.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchRequest.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchRequest.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.SearchRequest" /> class. This constructor creates an empty request.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchRequest.#ctor(System.String,System.String,System.DirectoryServices.Protocols.SearchScope,System.String[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchRequest.#ctor(System.String,System.String,System.DirectoryServices.Protocols.SearchScope,System.String[])" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.SearchRequest" /> class using the specified name, search filter, scope, and attributes.</summary>
      <param name="distinguishedName">The distinguished name of the object at which to start the search.</param>
      <param name="ldapFilter">An LDAP search filter.</param>
      <param name="searchScope">One of the values of <see cref="T:System.DirectoryServices.Protocols.SearchScope" /> that specifies the search scope.</param>
      <param name="attributeList">An array that contains the requested attributes in the result set.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchRequest.#ctor(System.String,System.Xml.XmlDocument,System.DirectoryServices.Protocols.SearchScope,System.String[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchRequest.#ctor(System.String,System.Xml.XmlDocument,System.DirectoryServices.Protocols.SearchScope,System.String[])" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.SearchRequest" /> class using the specified name, search filter, scope, and attributes.</summary>
      <param name="distinguishedName">The distinguished name of the object for which to search.</param>
      <param name="filter">A DSML v2 search filter.</param>
      <param name="searchScope">A <see cref="T:System.DirectoryServices.Protocols.SearchScope" /> value that specifies the search scope. The default value is <see cref="T:System.DirectoryServices.Protocols.SearchScope" />.</param>
      <param name="attributeList">An array that contains the requested attributes in the result set.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchRequest.Aliases">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchRequest.Aliases" /> property contains one of the values of <see cref="T:System.DirectoryServices.Protocols.DereferenceAlias" /> that specifies the dereference alias behavior.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.DereferenceAlias" /> value that specifies the dereference alias behavior.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value is not a member of <see cref="T:System.DirectoryServices.Protocols.DereferenceAlias" />.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchRequest.Attributes">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchRequest.Attributes" /> property contains the attributes of the requested object. </summary>
      <returns>The attributes of the requested object. Set this property to null to retrieve all attributes.</returns>
      <exception cref="T:System.ArgumentException">One of the strings in <see cref="T:System.Collections.Specialized.StringCollection" /> object is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchRequest.DistinguishedName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchRequest.DistinguishedName" /> property contains the distinguished name of the requested object.</summary>
      <returns>The distinguished name of the requested object.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchRequest.Filter">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchRequest.Filter" /> property contains the search filter. </summary>
      <returns>The search filter. If the value is a string, this property is an LDAP search filter. If the value is an XML document, the property is a DSML v2 search filter. Any other type throws an exception.</returns>
      <exception cref="T:System.ArgumentException">The value is not a string or an XML document.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchRequest.Scope">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchRequest.Scope" /> property contains one of the values of <see cref="T:System.DirectoryServices.SearchScope" /> that specifies the scope of the search.</summary>
      <returns>One of the values of <see cref="T:System.DirectoryServices.SearchScope" /> that specifies the scope of the search.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value is not a member of <see cref="T:System.DirectoryServices.SearchScope" />.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchRequest.SizeLimit">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchRequest.SizeLimit" /> property contains the maximum number of objects returned in the search request.</summary>
      <returns>The maximum number of objects returned in the search request.</returns>
      <exception cref="T:System.ArgumentException">The value is less than zero.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchRequest.TimeLimit">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchRequest.TimeLimit" /> property contains a <see cref="T:System.TimeSpan" /> object that specifies the time span allowed, if the server processes the search request.</summary>
      <returns>A <see cref="T:System.TimeSpan" /> object that specifies the time span allowed, if the server processes the search request.</returns>
      <exception cref="T:System.ArgumentException">The value is less than <see cref="F:System.TimeSpan.Zero" />.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchRequest.ToXmlNode(System.Xml.XmlDocument)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchRequest.ToXmlNode(System.Xml.XmlDocument)" /> method creates an XML node from the specified <see cref="T:System.Xml.XmlDocument" /> object.</summary>
      <returns>The resulting <see cref="T:System.Xml.XmlElement" /> object.</returns>
      <param name="doc">A <see cref="T:System.Xml.XmlDocument" /> object.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchRequest.TypesOnly">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchRequest.TypesOnly" /> property specifies whether the search returns only the attribute names and not the attribute values. </summary>
      <returns>If this property is true, the search returns only the attribute names. If this property is false, the search returns both the attribute names and values.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SearchResponse">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SearchResponse" /> class is sent by the server as a response to a <see cref="T:System.DirectoryServices.Protocols.SearchRequest" /> object. This response contains zero or more <see cref="T:System.DirectoryServices.Protocols.SearchResultEntry" /> objects, and zero or more <see cref="T:System.DirectoryServices.Protocols.SearchResultReference" /> objects.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResponse.Controls">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResponse.Controls" /> property contains the <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object returned by the server.</summary>
      <returns>The <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> object returned by the server.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResponse.Entries">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResponse.Entries" /> property contains a <see cref="T:System.DirectoryServices.Protocols.SearchResultEntryCollection" /> object that contains the entries returned by the search.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.SearchResultEntryCollection" /> object that contains the entries returned by the search.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResponse.ErrorMessage">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResponse.ErrorMessage" /> property contains the error message, if any, for this operation.</summary>
      <returns>The error message, if any, for this operation.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResponse.MatchedDN">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResponse.MatchedDN" /> property contains the matched distinguished name returned by the server.</summary>
      <returns>The matched distinguished name returned by the server.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResponse.References">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResponse.References" /> property contains a <see cref="T:System.DirectoryServices.Protocols.SearchResultReferenceCollection" /> object that contains search references returned by the server.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.SearchResultReferenceCollection" /> object that contains search references returned by the server.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResponse.Referral">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResponse.Referral" /> property contains the server referrals.</summary>
      <returns>The server referrals.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResponse.ResultCode">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResponse.ResultCode" /> property contains a value of <see cref="T:System.DirectoryServices.Protocols.ResultCode" /> that specifies the result code for the operation.</summary>
      <returns>The result code for the operation.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SearchResultAttributeCollection">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SearchResultAttributeCollection" /> class is a collection of <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> objects.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResultAttributeCollection.AttributeNames">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResultAttributeCollection.AttributeNames" /> property contains names of attributes in this collection.</summary>
      <returns>Names of attributes in this collection.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchResultAttributeCollection.Contains(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchResultAttributeCollection.Contains(System.String)" /> method determines whether the collection contains the specified <paramref name="attributeName" />.</summary>
      <returns>true if the collection contains attributeName or false if it does not.</returns>
      <param name="attributeName">The name of the attribute to locate.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attributeName" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchResultAttributeCollection.CopyTo(System.DirectoryServices.Protocols.DirectoryAttribute[],System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchResultAttributeCollection.CopyTo(System.DirectoryServices.Protocols.DirectoryAttribute[],System.Int32)" /> method copies the entire collection to a one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="array">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.DirectoryServices.Protocols.SearchResultAttributeCollection" /> object.</param>
      <param name="index">The zero-based index of <paramref name="array" /> where copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional-or-The number of elements in the source <see cref="T:System.DirectoryServices.Protocols.SearchResultAttributeCollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
      <exception cref="T:System.InvalidCastException">The type of the source <see cref="T:System.DirectoryServices.Protocols.SearchResultAttributeCollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResultAttributeCollection.Item(System.String)">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResultAttributeCollection.Item(System.String)" /> property contains the <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object specified by <paramref name="attributeName" />.</summary>
      <returns>The <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> object specified by <paramref name="attributeName" />.</returns>
      <param name="attributeName">The name of this attribute.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResultAttributeCollection.Values">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResultAttributeCollection.Values" /> property contains values of attributes in this collection.</summary>
      <returns>Values of attributes in this collection.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SearchResultEntry">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SearchResultEntry" /> class contains an object returned in the result set.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResultEntry.Attributes">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResultEntry.Attributes" /> property contains a collection of <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> objects.</summary>
      <returns>A collection of <see cref="T:System.DirectoryServices.Protocols.DirectoryAttribute" /> objects.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResultEntry.Controls">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResultEntry.Controls" /> property contains an array of <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> objects.</summary>
      <returns>An array of <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> objects.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResultEntry.DistinguishedName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResultEntry.DistinguishedName" /> property contains the distinguished name of the returned entry.</summary>
      <returns>The distinguished name of the returned entry.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SearchResultEntryCollection">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SearchResultEntryCollection" /> class contains a collection of <see cref="T:System.DirectoryServices.Protocols.SearchResultEntry" /> objects returned in a result set.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchResultEntryCollection.Contains(System.DirectoryServices.Protocols.SearchResultEntry)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchResultEntryCollection.Contains(System.DirectoryServices.Protocols.SearchResultEntry)" /> method determines if the collection contains the specified <see cref="T:System.DirectoryServices.Protocols.SearchResultEntry" /> object.</summary>
      <returns>true if the collection contains <paramref name="value" /> or false if it does not.</returns>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.SearchResultEntry" /> to locate.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchResultEntryCollection.CopyTo(System.DirectoryServices.Protocols.SearchResultEntry[],System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchResultEntryCollection.CopyTo(System.DirectoryServices.Protocols.SearchResultEntry[],System.Int32)" /> method copies the entire collection to a one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="values">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.DirectoryServices.Protocols.SearchResultEntryCollection" /> object.</param>
      <param name="index">The zero-based index of <paramref name="values" /> where copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="values" /> is multidimensional-or-The number of elements in the source <see cref="T:System.DirectoryServices.Protocols.SearchResultEntryCollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="values" />.</exception>
      <exception cref="T:System.InvalidCastException">The type of the source <see cref="T:System.DirectoryServices.Protocols.SearchResultEntryCollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchResultEntryCollection.IndexOf(System.DirectoryServices.Protocols.SearchResultEntry)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchResultEntryCollection.IndexOf(System.DirectoryServices.Protocols.SearchResultEntry)" /> method returns the zero-based index of the first occurrence of the specified <see cref="T:System.DirectoryServices.Protocols.SearchResultEntry" /> object in <see cref="T:System.DirectoryServices.Protocols.SearchResultEntryCollection" />.</summary>
      <returns>The index value of the specified <see cref="T:System.DirectoryServices.Protocols.SearchResultEntry" /> object, if found, or -1 if it was not found.</returns>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.SearchResultEntry" /> object for which the index is returned.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResultEntryCollection.Item(System.Int32)">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResultEntryCollection.Item(System.Int32)" /> property contains the <see cref="T:System.DirectoryServices.Protocols.SearchResultEntry" /> object at the specified <paramref name="index" />.</summary>
      <returns>The <see cref="T:System.DirectoryServices.Protocols.SearchResultEntry" /> object at the specified <paramref name="index" />.</returns>
      <param name="index">The index value of the <see cref="T:System.DirectoryServices.Protocols.SearchResultEntry" /> object.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SearchResultReference">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SearchResultReference" /> class contains a single search reference returned by the server.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResultReference.Controls">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResultReference.Controls" /> property contains an array of <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> objects returned by the server in the search reference.</summary>
      <returns>An array of <see cref="T:System.DirectoryServices.Protocols.DirectoryControl" /> objects returned by the server in the search reference.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResultReference.Reference">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResultReference.Reference" /> property contains an array of <see cref="T:System.Uri" /> objects returned by the server in the search reference.</summary>
      <returns>An array of <see cref="T:System.Uri" /> objects returned by the server in the search reference.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SearchResultReferenceCollection">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SearchResultReferenceCollection" /> class contains a collection of <see cref="T:System.DirectoryServices.Protocols.SearchResultReference" /> objects.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchResultReferenceCollection.Contains(System.DirectoryServices.Protocols.SearchResultReference)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchResultReferenceCollection.Contains(System.DirectoryServices.Protocols.SearchResultReference)" /> method determines if the collection contains specified <see cref="T:System.DirectoryServices.Protocols.SearchResultReference" /> object.</summary>
      <returns>true if the collection contains <paramref name="value" /> or false if it does not.</returns>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.SearchResultReference" /> object to locate.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchResultReferenceCollection.CopyTo(System.DirectoryServices.Protocols.SearchResultReference[],System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchResultReferenceCollection.CopyTo(System.DirectoryServices.Protocols.SearchResultReference[],System.Int32)" /> method copies the entire collection to a one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="values">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.DirectoryServices.Protocols.SearchResultReferenceCollection" /> object.</param>
      <param name="index">The zero-based index of <paramref name="values" /> where copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is a null reference (Nothing in Visual Basic). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="values" /> is multidimensional-or-The number of elements in the source <see cref="T:System.DirectoryServices.Protocols.SearchResultReferenceCollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="values" />.</exception>
      <exception cref="T:System.InvalidCastException">The type of the source <see cref="T:System.DirectoryServices.Protocols.SearchResultReferenceCollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SearchResultReferenceCollection.IndexOf(System.DirectoryServices.Protocols.SearchResultReference)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SearchResultReferenceCollection.IndexOf(System.DirectoryServices.Protocols.SearchResultReference)" /> method returns the zero-based index of the first occurrence of the specified <see cref="T:System.DirectoryServices.Protocols.SearchResultReference" /> object in <see cref="T:System.DirectoryServices.Protocols.SearchResultReferenceCollection" />.</summary>
      <returns>The index value of the specified <see cref="T:System.DirectoryServices.Protocols.SearchResultReference" /> object, if found. If the object is not found, this method returns -1.</returns>
      <param name="value">The <see cref="T:System.DirectoryServices.Protocols.SearchResultReference" /> object for which the index is returned.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SearchResultReferenceCollection.Item(System.Int32)">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SearchResultReferenceCollection.Item(System.Int32)" /> property contains the <see cref="T:System.DirectoryServices.Protocols.SearchResultReference" /> object at the specified <paramref name="index" />.</summary>
      <returns>The <see cref="T:System.DirectoryServices.Protocols.SearchResultReference" /> object at the specified <paramref name="index" />.</returns>
      <param name="index">The index value of the <see cref="T:System.DirectoryServices.Protocols.SearchResultReference" /> object.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SearchScope">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SearchScope" /> enumeration specifies the scope of a search.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SearchScope.Base">
      <summary>Search only the specified base object. The value is equal to 0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SearchScope.OneLevel">
      <summary>Search the child objects of the base object, but not the base object itself. The value is equal to 1.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SearchScope.Subtree">
      <summary>Search the base object and all child objects. The value is equal to 2.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SecurityDescriptorFlagControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SecurityDescriptorFlagControl" /> class is used to pass flags to the server to control various security descriptor behaviors.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SecurityDescriptorFlagControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SecurityDescriptorFlagControl.#ctor" /> constructor creates a new instance of the <see cref="T:System.DirectoryServices.Protocols.SecurityDescriptorFlagControl" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SecurityDescriptorFlagControl.#ctor(System.DirectoryServices.Protocols.SecurityMasks)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SecurityDescriptorFlagControl.#ctor(System.DirectoryServices.Protocols.SecurityMasks)" /> constructor creates a new instance of the <see cref="T:System.DirectoryServices.Protocols.SecurityDescriptorFlagControl" /> class using the specified security masks.</summary>
      <param name="masks">A combination of <see cref="T:System.DirectoryServices.Protocols.SecurityMasks" /> values that specify security descriptor options.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SecurityDescriptorFlagControl.GetValue">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SecurityDescriptorFlagControl.GetValue" /> method returns the security descriptor options specified for this control.</summary>
      <returns>The security descriptor options specified for this control.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SecurityDescriptorFlagControl.SecurityMasks">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SecurityDescriptorFlagControl.SecurityMasks" /> property contains a combination of <see cref="T:System.DirectoryServices.Protocols.SecurityMasks" /> values that specify security descriptor options.</summary>
      <returns>A combination of <see cref="T:System.DirectoryServices.Protocols.SecurityMasks" /> values that specify security descriptor options.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SecurityMasks">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SecurityMasks" /> enumeration specifies various security descriptor options.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SecurityMasks.None">
      <summary>No security descriptor flag is set. The value is equal to 0 or 0x0.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SecurityMasks.Owner">
      <summary>The owner security descriptor flag. The value is equal to 1 or 0x1.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SecurityMasks.Group">
      <summary>The primary group security descriptor flag. The value is equal to 2 or 0x2.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SecurityMasks.Dacl">
      <summary>The discretionary access control list (DACL) security descriptor flag. The value is equal to 4 or 0x4.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SecurityMasks.Sacl">
      <summary>The system access control list (SACL) security descriptor flag.  The value is equal to 8 or 0x8.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation" /> class contains data about a secure connection.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation.AlgorithmIdentifier">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation.AlgorithmIdentifier" /> property contains the encryption algorithm used by the connection.</summary>
      <returns>The encryption algorithm used by the connection.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation.CipherStrength">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation.CipherStrength" /> property contains the cipher strength of the encryption algorithm used on this connection.</summary>
      <returns>The cipher strength of the encryption algorithm used on this connection.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation.ExchangeStrength">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation.ExchangeStrength" /> property contains the exchange strength for this connection.</summary>
      <returns>The exchange strength for this connection.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation.Hash">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation.Hash" /> property contains the hashing algorithm used by this connection.</summary>
      <returns>The hashing algorithm used by this connection.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation.HashStrength">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation.HashStrength" /> property contains the strength of the hashing algorithm used on this connection.</summary>
      <returns>The strength of the hashing algorithm used on this connection.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation.KeyExchangeAlgorithm">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation.KeyExchangeAlgorithm" /> property contains the key exchange algorithm used by this connection.</summary>
      <returns>The key exchange algorithm used by this connection.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation.Protocol">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SecurityPackageContextConnectionInformation.Protocol" /> property contains the security protocol used by this connection.</summary>
      <returns>The security protocol used by this connection.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SecurityProtocol">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SecurityProtocol" /> enumeration is used to specify the security protocol used by a connection.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SecurityProtocol.Pct1Server">
      <summary>Indicates that the server is using the PCT1 protocol. The value is equal to 1.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SecurityProtocol.Pct1Client">
      <summary>Indicates that the client is using the PCT1 protocol. The value is equal to 2.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SecurityProtocol.Ssl2Server">
      <summary>Indicates that the server is using the SSL2 protocol. The value is equal to 4.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SecurityProtocol.Ssl2Client">
      <summary>Indicates that the client is using the SSL2 protocol. The value is equal to 8.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SecurityProtocol.Ssl3Server">
      <summary>Indicates that the server is using the SSL3 protocol. The value is equal to 16.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SecurityProtocol.Ssl3Client">
      <summary>Indicates that the client is using the SSL3 protocol. The value is equal to 32.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SecurityProtocol.Tls1Server">
      <summary>Indicates that the server is using the TLS1 protocol. The value is equal to 64.</summary>
    </member>
    <member name="F:System.DirectoryServices.Protocols.SecurityProtocol.Tls1Client">
      <summary>Indicates that the client is using the TLS1 protocol. The value is equal to 128.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.ShowDeletedControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.ShowDeletedControl" /> class is used with <see cref="T:System.DirectoryServices.Protocols.SearchRequest" /> to specify that the search results should include any deleted objects that match the search filter.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.ShowDeletedControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.ShowDeletedControl.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.ShowDeletedControl" /> class.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SortKey">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SortKey" /> class stores sort criteria for use by sort controls.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SortKey.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SortKey.#ctor" /> constructor creates and initializes a new instance of the <see cref="T:System.DirectoryServices.Protocols.SortKey" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SortKey.#ctor(System.String,System.String,System.Boolean)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SortKey.#ctor(System.String,System.String,System.Boolean)" /> constructor creates and initializes a new instance of the <see cref="T:System.DirectoryServices.Protocols.SortKey" /> class using the specified attribute name, matching rule, and sort order.</summary>
      <param name="attributeName">The name of the attribute to use as a sort key.</param>
      <param name="matchingRule">The object identifier (OID) of the matching rule for the sort.</param>
      <param name="reverseOrder">true if the sort is ordered from the lowest to highest or false if the sort order is from highest to lowest.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SortKey.AttributeName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SortKey.AttributeName" /> property specifies the name of the attribute to use as a sort key.</summary>
      <returns>The name of the attribute to use as a sort key.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SortKey.MatchingRule">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SortKey.MatchingRule" /> property specifies the object identifier (OID) of the matching rule for the sort.</summary>
      <returns>The OID of the matching rule for the sort.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SortKey.ReverseOrder">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SortKey.ReverseOrder" /> property specifies if the sort should be returned in reverse order. </summary>
      <returns>This property is true if the sort is ordered from the lowest to highest or false if the sort is ordered from highest to lowest.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SortRequestControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SortRequestControl" /> class is used with <see cref="T:System.DirectoryServices.Protocols.SearchRequest" /> to instruct the server to sort the search results before returning them to the client application.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SortRequestControl.#ctor(System.DirectoryServices.Protocols.SortKey[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SortRequestControl.#ctor(System.DirectoryServices.Protocols.SortKey[])" /> constructor creates and initializes a new instance of the <see cref="T:System.DirectoryServices.Protocols.SortRequestControl" /> class using the specified sort keys.</summary>
      <param name="sortKeys">An array of <see cref="T:System.DirectoryServices.Protocols.SortKey" /> objects used by the server to sort the search results before returning them to the client.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SortRequestControl.#ctor(System.String,System.Boolean)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SortRequestControl.#ctor(System.String,System.Boolean)" /> constructor creates a new instance of the <see cref="T:System.DirectoryServices.Protocols.SortRequestControl" /> class using the specified attribute name and sort order.</summary>
      <param name="attributeName">The name of the attribute on which the query is based.</param>
      <param name="reverseOrder">true if results should be returned in reverse order or false otherwise.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SortRequestControl.#ctor(System.String,System.String,System.Boolean)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SortRequestControl.#ctor(System.String,System.String,System.Boolean)" /> constructor creates a new instance of the <see cref="T:System.DirectoryServices.Protocols.SortRequestControl" /> class using the specified attribute name, matching rule, and sort order.</summary>
      <param name="attributeName">The name of the attribute on which the query is based.</param>
      <param name="matchingRule">Specifies the object identifier (OID) of the matching rule for the sort.</param>
      <param name="reverseOrder">true if the sort is ordered from lowest to highest or false if the sort is ordered from highest to lowest.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.SortRequestControl.GetValue">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.SortRequestControl.GetValue" /> method specifies the sort keys and matching rules for this control.</summary>
      <returns>The sort keys and matching rules for this control.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SortRequestControl.SortKeys">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SortRequestControl.SortKeys" /> property contains an array of <see cref="T:System.DirectoryServices.Protocols.SortKey" /> objects used by the server to sort the search results before returning them to the client.</summary>
      <returns>An array of <see cref="T:System.DirectoryServices.Protocols.SortKey" /> objects used by the server to sort the search results before returning them to the client.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.SortResponseControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.SortResponseControl" /> class is used to pass sort data from the server to the client.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SortResponseControl.AttributeName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SortResponseControl.AttributeName" /> property contains the attribute name.</summary>
      <returns>The attribute name.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.SortResponseControl.Result">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.SortResponseControl.Result" /> property returns a <see cref="T:System.DirectoryServices.Protocols.ResultCode" /> object that contains the result code.</summary>
      <returns>A <see cref="T:System.DirectoryServices.Protocols.ResultCode" /> object that contains the result code.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.TlsOperationException">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.TlsOperationException" /> class is an exception that occurs in the <see cref="M:System.DirectoryServices.Protocols.LdapSessionOptions.StartTransportLayerSecurity(System.DirectoryServices.Protocols.DirectoryControlCollection)" /> method if the request fails.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.TlsOperationException.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.TlsOperationException.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.TlsOperationException" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.TlsOperationException.#ctor(System.DirectoryServices.Protocols.DirectoryResponse)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.TlsOperationException.#ctor(System.DirectoryServices.Protocols.DirectoryResponse)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.TlsOperationException" /> class using the specified response.</summary>
      <param name="response">A <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object returned by the server.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.TlsOperationException.#ctor(System.DirectoryServices.Protocols.DirectoryResponse,System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.TlsOperationException.#ctor(System.DirectoryServices.Protocols.DirectoryResponse,System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.TlsOperationException" /> class using the specified response and message.</summary>
      <param name="response">A <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object returned by the server.</param>
      <param name="message">The message displayed to the client when the exception occurs.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.TlsOperationException.#ctor(System.DirectoryServices.Protocols.DirectoryResponse,System.String,System.Exception)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.TlsOperationException.#ctor(System.DirectoryServices.Protocols.DirectoryResponse,System.String,System.Exception)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.TlsOperationException" /> class using the specified response, message, and inner exception.</summary>
      <param name="response">A <see cref="T:System.DirectoryServices.Protocols.DirectoryResponse" /> object returned by the server.</param>
      <param name="message">The message displayed to the client when the exception occurs.</param>
      <param name="inner">The <see cref="P:System.Exception.InnerException" />, if any, that threw the exception.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.TlsOperationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.TlsOperationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.TlsOperationException" /> class using the specified serialization data and streaming context.</summary>
      <param name="info">The data required to serialize the parameter.</param>
      <param name="context">The source and destination of the serialized stream associated with the parameter.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.TlsOperationException.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.TlsOperationException.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.TlsOperationException" /> class using the specified message.</summary>
      <param name="message">The message displayed to the client when the exception occurs.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.TlsOperationException.#ctor(System.String,System.Exception)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.TlsOperationException.#ctor(System.String,System.Exception)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.TlsOperationException" /> class using the specified message and inner exception.</summary>
      <param name="message">The message displayed to the client when the exception occurs.</param>
      <param name="inner">The <see cref="P:System.Exception.InnerException" />, if any, that threw the exception.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.TreeDeleteControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.TreeDeleteControl" /> class is used with <see cref="T:System.DirectoryServices.Protocols.DeleteRequest" /> to delete an entire subtree in the directory.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.TreeDeleteControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.TreeDeleteControl.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.TreeDeleteControl" /> class.</summary>
    </member>
    <member name="T:System.DirectoryServices.Protocols.VerifyNameControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.VerifyNameControl" /> class is used with an LDAP search function to specify the server used to verify the existence of an object.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.VerifyNameControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.VerifyNameControl.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.VerifyNameControl" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.VerifyNameControl.#ctor(System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.VerifyNameControl.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.VerifyNameControl" /> class using the specified server name.</summary>
      <param name="serverName">The global catalog the performs the validation.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.VerifyNameControl.#ctor(System.String,System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.VerifyNameControl.#ctor(System.String,System.Int32)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.VerifyNameControl" /> class using the specified server name and flags.</summary>
      <param name="serverName">The global catalog that performs the validation.</param>
      <param name="flag">The flags used for the validation.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.VerifyNameControl.Flag">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.VerifyNameControl.Flag" /> property contains the flag used for validation.</summary>
      <returns>The flag used for validation.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.VerifyNameControl.GetValue">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.VerifyNameControl.GetValue" /> method returns the name of the validation server.</summary>
      <returns>The name of the validation server.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.VerifyNameControl.ServerName">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.VerifyNameControl.ServerName" /> property contains the name of the server that performs the validation.</summary>
      <returns>The name of the server that performs the validation.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.VerifyServerCertificateCallback">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.VerifyServerCertificateCallback" /> delegate gets or sets the default callback function used to verify server certificates when establishing an SSL connection.</summary>
      <returns>true if the client approves the server certificate; otherwise false.</returns>
      <param name="connection">A <see cref="T:System.DirectoryServices.Protocols.LdapConnection" /> object that specifies the connection.</param>
      <param name="certificate">A <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object that specifies the server certificate.</param>
    </member>
    <member name="T:System.DirectoryServices.Protocols.VlvRequestControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.VlvRequestControl" /> class is used to request virtual list view (VLV) support from the server.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.VlvRequestControl.#ctor">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.VlvRequestControl.#ctor" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.VlvRequestControl" /> class.</summary>
    </member>
    <member name="M:System.DirectoryServices.Protocols.VlvRequestControl.#ctor(System.Int32,System.Int32,System.Byte[])">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.VlvRequestControl.#ctor(System.Int32,System.Int32,System.Byte[])" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.VlvRequestControl" /> class using the specified entry counts and target entry.</summary>
      <param name="beforeCount">The number of entries before the target entry that the client requests from the server to return in the list results.</param>
      <param name="afterCount">The number of entries after the target entry that the client requests from the server to return in the results.</param>
      <param name="target">The target entry for the search.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.VlvRequestControl.#ctor(System.Int32,System.Int32,System.Int32)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.VlvRequestControl.#ctor(System.Int32,System.Int32,System.Int32)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.VlvRequestControl" /> class using the specified entry counts and offset.</summary>
      <param name="beforeCount">The number of entries before the target entry that the client requests from the server to send back in the list results.</param>
      <param name="afterCount">The number of entries after the target entry that the client requests from the server to send back in the results.</param>
      <param name="offset">The ratio between the offset value and the content count.</param>
    </member>
    <member name="M:System.DirectoryServices.Protocols.VlvRequestControl.#ctor(System.Int32,System.Int32,System.String)">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.VlvRequestControl.#ctor(System.Int32,System.Int32,System.String)" /> constructor creates an instance of the <see cref="T:System.DirectoryServices.Protocols.VlvRequestControl" /> class using the specified entry counts and target entry.</summary>
      <param name="beforeCount">The number of entries before the target entry that the client requests from the server to return in the list results.</param>
      <param name="afterCount">The number of entries after the target entry that the client requests from the server to return in the results.</param>
      <param name="target">The target entry for the search.</param>
    </member>
    <member name="P:System.DirectoryServices.Protocols.VlvRequestControl.AfterCount">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.VlvRequestControl.AfterCount" /> property specifies the number of entries after the target entry to return in the list results.</summary>
      <returns>The number of entries after the target entry to return in the list results.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.VlvRequestControl.BeforeCount">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.VlvRequestControl.BeforeCount" /> property specifies the number of entries before the target entry to return in the list results.</summary>
      <returns>The number of entries before the target entry to return in the list results.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.VlvRequestControl.ContextId">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.VlvRequestControl.ContextId" /> property contains the context ID assigned by the server to identify this search operation.</summary>
      <returns>The context ID assigned by the server to identify this search operation.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.VlvRequestControl.EstimateCount">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.VlvRequestControl.EstimateCount" /> property contains the content count of the list.</summary>
      <returns>The content count of the list.</returns>
    </member>
    <member name="M:System.DirectoryServices.Protocols.VlvRequestControl.GetValue">
      <summary>The <see cref="M:System.DirectoryServices.Protocols.VlvRequestControl.GetValue" /> method returns a BER-encoded sequence that specifies the control.</summary>
      <returns>A BER-encoded sequence that specifies this control.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.VlvRequestControl.Offset">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.VlvRequestControl.Offset" /> property contains the offset value of the target entry.</summary>
      <returns>The offset value of the target entry.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.VlvRequestControl.Target">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.VlvRequestControl.Target" /> property contains the target entry of the search.</summary>
      <returns>The target entry of the search.</returns>
    </member>
    <member name="T:System.DirectoryServices.Protocols.VlvResponseControl">
      <summary>The <see cref="T:System.DirectoryServices.Protocols.VlvResponseControl" /> class is used to pass virtual list view (VLV) data from the server to the client.</summary>
    </member>
    <member name="P:System.DirectoryServices.Protocols.VlvResponseControl.ContentCount">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.VlvResponseControl.ContentCount" /> property contains the number of items in the list.</summary>
      <returns>The number of items in the list.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.VlvResponseControl.ContextId">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.VlvResponseControl.ContextId" /> property contains the context ID assigned by the server to identify this search.</summary>
      <returns>The context ID assigned by the server to identify this search.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.VlvResponseControl.Result">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.VlvResponseControl.Result" /> property contains a result code in the form of a <see cref="T:System.DirectoryServices.Protocols.ResultCode" /> object.</summary>
      <returns>A result code in the form of a <see cref="T:System.DirectoryServices.Protocols.ResultCode" /> object.</returns>
    </member>
    <member name="P:System.DirectoryServices.Protocols.VlvResponseControl.TargetPosition">
      <summary>The <see cref="P:System.DirectoryServices.Protocols.VlvResponseControl.TargetPosition" /> property contains the list index position of the target entry.</summary>
      <returns>The list index position of the target entry.</returns>
    </member>
  </members>
</doc>