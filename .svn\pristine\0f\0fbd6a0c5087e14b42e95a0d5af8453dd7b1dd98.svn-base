﻿using System;
using System.Drawing;
using System.Windows.Forms;
using OCRTools.Properties;

namespace OCRTools
{
    partial class UcContent
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle13 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle14 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle15 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle16 = new System.Windows.Forms.DataGridViewCellStyle();
            this.txtContent = new System.Windows.Forms.RichTextBox();
            this.wbContent = new OCRTools.WebBrowser2();
            this.imageBox = new OCRTools.PanelPictureView();
            this.toolResize = new System.Windows.Forms.ToolStrip();
            this.txtPicZoomPercent = new System.Windows.Forms.ToolStripLabel();
            this.tsmPicType = new System.Windows.Forms.ToolStripDropDownButton();
            this.toolStripMenuItem20 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem21 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem22 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem24 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem25 = new System.Windows.Forms.ToolStripMenuItem();
            this.文档矫正ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.图像增强ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dgContent = new OCRTools.DataGridViewEx();
            this.tsmImageViewBackStyle = new System.Windows.Forms.ToolStripDropDownButton();
            this.tsmEdit = new System.Windows.Forms.ToolStripButton();
            this.tsmCopy = new System.Windows.Forms.ToolStripButton();
            this.tsmSearch = new System.Windows.Forms.ToolStripButton();
            this.tsmTrans = new System.Windows.Forms.ToolStripButton();
            this.tsmPicBig = new System.Windows.Forms.ToolStripButton();
            this.tsmPicSmall = new System.Windows.Forms.ToolStripButton();
            this.tsmPicOrigin = new System.Windows.Forms.ToolStripButton();
            this.tsmRotate = new System.Windows.Forms.ToolStripButton();
            this.tsbReOcr = new System.Windows.Forms.ToolStripButton();
            this.tsbSaveImg = new System.Windows.Forms.ToolStripButton();
            this.tsmModel = new System.Windows.Forms.ToolStripButton();
            this.tsmNewWindow = new System.Windows.Forms.ToolStripButton();
            this.tsmFullScreen = new System.Windows.Forms.ToolStripButton();
            this.toolResize.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgContent)).BeginInit();
            this.SuspendLayout();
            // 
            // txtContent
            // 
            this.txtContent.BackColor = System.Drawing.Color.Honeydew;
            this.txtContent.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.txtContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtContent.EnableAutoDragDrop = true;
            this.txtContent.Font = new System.Drawing.Font("微软雅黑", 16F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Pixel);
            this.txtContent.Location = new System.Drawing.Point(0, 0);
            this.txtContent.Margin = new System.Windows.Forms.Padding(0);
            this.txtContent.Name = "txtContent";
            this.txtContent.Size = new System.Drawing.Size(470, 351);
            this.txtContent.TabIndex = 1;
            this.txtContent.TabStop = false;
            this.txtContent.Text = "";
            // 
            // wbContent
            // 
            this.wbContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.wbContent.Location = new System.Drawing.Point(0, 0);
            this.wbContent.Margin = new System.Windows.Forms.Padding(0);
            this.wbContent.MinimumSize = new System.Drawing.Size(20, 20);
            this.wbContent.Name = "wbContent";
            this.wbContent.Size = new System.Drawing.Size(470, 351);
            this.wbContent.TabIndex = 3;
            this.wbContent.TabStop = false;
            // 
            // imageBox
            // 
            this.imageBox.AutoCenter = false;
            this.imageBox.AutoScroll = true;
            this.imageBox.BackColor = System.Drawing.Color.White;
            this.imageBox.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(220)))), ((int)(((byte)(220)))), ((int)(((byte)(220)))));
            this.imageBox.GridDisplayMode = OCRTools.ImageBoxGridDisplayMode.马赛克;
            this.imageBox.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
            this.imageBox.Location = new System.Drawing.Point(0, 0);
            this.imageBox.Margin = new System.Windows.Forms.Padding(0);
            this.imageBox.Name = "imageBox";
            this.imageBox.Size = new System.Drawing.Size(470, 351);
            this.imageBox.TabIndex = 5;
            this.imageBox.TabStop = false;
            // 
            // toolResize
            // 
            this.toolResize.BackColor = System.Drawing.Color.GhostWhite;
            this.toolResize.Dock = System.Windows.Forms.DockStyle.None;
            this.toolResize.GripStyle = System.Windows.Forms.ToolStripGripStyle.Hidden;
            this.toolResize.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmImageViewBackStyle,
            this.tsmEdit,
            this.tsmCopy,
            this.tsmSearch,
            this.tsmTrans,
            this.tsmPicBig,
            this.txtPicZoomPercent,
            this.tsmPicSmall,
            this.tsmPicOrigin,
            this.tsmPicType,
            this.tsmRotate,
            this.tsbReOcr,
            this.tsbSaveImg,
            this.tsmModel,
            this.tsmNewWindow,
            this.tsmFullScreen});
            this.toolResize.Location = new System.Drawing.Point(56, 314);
            this.toolResize.Name = "toolResize";
            this.toolResize.Size = new System.Drawing.Size(632, 39);
            this.toolResize.TabIndex = 3;
            this.toolResize.Text = "toolStrip1";
            this.toolResize.Visible = false;
            // 
            // txtPicZoomPercent
            // 
            this.txtPicZoomPercent.Font = new System.Drawing.Font("微软雅黑", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Pixel);
            this.txtPicZoomPercent.ForeColor = System.Drawing.Color.Black;
            this.txtPicZoomPercent.Name = "txtPicZoomPercent";
            this.txtPicZoomPercent.Size = new System.Drawing.Size(49, 36);
            this.txtPicZoomPercent.Text = "100%";
            // 
            // tsmPicType
            // 
            this.tsmPicType.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.tsmPicType.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem20,
            this.toolStripMenuItem21,
            this.toolStripMenuItem22,
            this.toolStripMenuItem24,
            this.toolStripMenuItem25,
            this.文档矫正ToolStripMenuItem,
            this.图像增强ToolStripMenuItem});
            this.tsmPicType.ForeColor = System.Drawing.Color.Black;
            this.tsmPicType.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmPicType.Name = "tsmPicType";
            this.tsmPicType.Size = new System.Drawing.Size(69, 36);
            this.tsmPicType.Tag = "图像处理";
            this.tsmPicType.Text = "原始图片";
            this.tsmPicType.Visible = false;
            this.tsmPicType.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmPicType_DropDownItemClicked);
            // 
            // toolStripMenuItem20
            // 
            this.toolStripMenuItem20.Name = "toolStripMenuItem20";
            this.toolStripMenuItem20.Size = new System.Drawing.Size(124, 22);
            this.toolStripMenuItem20.Tag = "0";
            this.toolStripMenuItem20.Text = "原图";
            // 
            // toolStripMenuItem21
            // 
            this.toolStripMenuItem21.Name = "toolStripMenuItem21";
            this.toolStripMenuItem21.Size = new System.Drawing.Size(124, 22);
            this.toolStripMenuItem21.Tag = "1";
            this.toolStripMenuItem21.Text = "灰度";
            // 
            // toolStripMenuItem22
            // 
            this.toolStripMenuItem22.Name = "toolStripMenuItem22";
            this.toolStripMenuItem22.Size = new System.Drawing.Size(124, 22);
            this.toolStripMenuItem22.Tag = "2";
            this.toolStripMenuItem22.Text = "增强";
            // 
            // toolStripMenuItem24
            // 
            this.toolStripMenuItem24.Name = "toolStripMenuItem24";
            this.toolStripMenuItem24.Size = new System.Drawing.Size(124, 22);
            this.toolStripMenuItem24.Tag = "3";
            this.toolStripMenuItem24.Text = "反色";
            // 
            // toolStripMenuItem25
            // 
            this.toolStripMenuItem25.Name = "toolStripMenuItem25";
            this.toolStripMenuItem25.Size = new System.Drawing.Size(124, 22);
            this.toolStripMenuItem25.Tag = "4";
            this.toolStripMenuItem25.Text = "彩色";
            // 
            // 文档矫正ToolStripMenuItem
            // 
            this.文档矫正ToolStripMenuItem.Name = "文档矫正ToolStripMenuItem";
            this.文档矫正ToolStripMenuItem.Size = new System.Drawing.Size(124, 22);
            this.文档矫正ToolStripMenuItem.Tag = "50";
            this.文档矫正ToolStripMenuItem.Text = "文档矫正";
            // 
            // 图像增强ToolStripMenuItem
            // 
            this.图像增强ToolStripMenuItem.Name = "图像增强ToolStripMenuItem";
            this.图像增强ToolStripMenuItem.Size = new System.Drawing.Size(124, 22);
            this.图像增强ToolStripMenuItem.Tag = "51";
            this.图像增强ToolStripMenuItem.Text = "图像增强";
            // 
            // dgContent
            // 
            this.dgContent.AllowDrop = true;
            dataGridViewCellStyle13.ForeColor = System.Drawing.Color.Black;
            this.dgContent.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle13;
            this.dgContent.BackgroundColor = System.Drawing.Color.White;
            this.dgContent.BorderStyle = System.Windows.Forms.BorderStyle.None;
            dataGridViewCellStyle14.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle14.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(244)))), ((int)(((byte)(244)))), ((int)(((byte)(244)))));
            dataGridViewCellStyle14.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Pixel);
            dataGridViewCellStyle14.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle14.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle14.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle14.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle14;
            this.dgContent.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgContent.ColumnHeadersVisible = false;
            dataGridViewCellStyle15.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle15.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle15.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Pixel);
            dataGridViewCellStyle15.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle15.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle15.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle15.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.DefaultCellStyle = dataGridViewCellStyle15;
            this.dgContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgContent.EnableHeadersVisualStyles = false;
            this.dgContent.IsShowSequence = false;
            this.dgContent.Location = new System.Drawing.Point(0, 0);
            this.dgContent.Margin = new System.Windows.Forms.Padding(0);
            this.dgContent.Name = "dgContent";
            dataGridViewCellStyle16.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle16.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle16.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Pixel);
            dataGridViewCellStyle16.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle16.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle16.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle16.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.RowHeadersDefaultCellStyle = dataGridViewCellStyle16;
            this.dgContent.RowHeadersVisible = false;
            this.dgContent.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.AutoSizeToAllHeaders;
            this.dgContent.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.Black;
            this.dgContent.RowTemplate.Height = 30;
            this.dgContent.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgContent.Size = new System.Drawing.Size(470, 351);
            this.dgContent.TabIndex = 2;
            this.dgContent.TabStop = false;
            // 
            // tsmImageViewBackStyle
            // 
            this.tsmImageViewBackStyle.AutoSize = false;
            this.tsmImageViewBackStyle.BackgroundImage = global::OCRTools.Properties.Resources.相框;
            this.tsmImageViewBackStyle.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Center;
            this.tsmImageViewBackStyle.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmImageViewBackStyle.ForeColor = System.Drawing.Color.Black;
            this.tsmImageViewBackStyle.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmImageViewBackStyle.Name = "tsmImageViewBackStyle";
            this.tsmImageViewBackStyle.ShowDropDownArrow = false;
            this.tsmImageViewBackStyle.Size = new System.Drawing.Size(32, 34);
            this.tsmImageViewBackStyle.ToolTipText = "点击更换背景";
            this.tsmImageViewBackStyle.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmImageViewBackStyle_DropDownItemClicked);
            // 
            // tsmEdit
            // 
            this.tsmEdit.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmEdit.Image = global::OCRTools.Properties.Resources.edit_small;
            this.tsmEdit.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmEdit.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmEdit.Name = "tsmEdit";
            this.tsmEdit.Size = new System.Drawing.Size(34, 36);
            this.tsmEdit.Text = "编辑图片";
            this.tsmEdit.ToolTipText = "编辑图片";
            this.tsmEdit.Visible = false;
            this.tsmEdit.Click += new System.EventHandler(this.tsmEdit_Click);
            // 
            // tsmCopy
            // 
            this.tsmCopy.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmCopy.Image = global::OCRTools.Properties.Resources.复制;
            this.tsmCopy.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmCopy.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmCopy.Name = "tsmCopy";
            this.tsmCopy.Size = new System.Drawing.Size(34, 36);
            this.tsmCopy.ToolTipText = "复制结果";
            this.tsmCopy.Click += new System.EventHandler(this.tsmCopy_Click);
            // 
            // tsmSearch
            // 
            this.tsmSearch.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmSearch.Image = global::OCRTools.Properties.Resources.搜索;
            this.tsmSearch.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmSearch.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmSearch.Name = "tsmSearch";
            this.tsmSearch.Size = new System.Drawing.Size(36, 36);
            this.tsmSearch.Text = "搜索文字";
            this.tsmSearch.Click += new System.EventHandler(this.tsmSearch_Click);
            // 
            // tsmTrans
            // 
            this.tsmTrans.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmTrans.Image = global::OCRTools.Properties.Resources.翻译_Small;
            this.tsmTrans.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmTrans.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmTrans.Name = "tsmTrans";
            this.tsmTrans.Size = new System.Drawing.Size(36, 36);
            this.tsmTrans.Text = "翻译文字";
            this.tsmTrans.Click += new System.EventHandler(this.tsmTrans_Click);
            // 
            // tsmPicBig
            // 
            this.tsmPicBig.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmPicBig.Image = global::OCRTools.Properties.Resources.放大;
            this.tsmPicBig.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmPicBig.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmPicBig.Name = "tsmPicBig";
            this.tsmPicBig.Size = new System.Drawing.Size(34, 36);
            this.tsmPicBig.Text = "toolStripButton2";
            this.tsmPicBig.ToolTipText = "放大";
            this.tsmPicBig.Click += new System.EventHandler(this.tsmPicBig_Click);
            // 
            // tsmPicSmall
            // 
            this.tsmPicSmall.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmPicSmall.Image = global::OCRTools.Properties.Resources.缩小;
            this.tsmPicSmall.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmPicSmall.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmPicSmall.Name = "tsmPicSmall";
            this.tsmPicSmall.Size = new System.Drawing.Size(34, 36);
            this.tsmPicSmall.Text = "toolStripButton3";
            this.tsmPicSmall.ToolTipText = "缩小";
            this.tsmPicSmall.Click += new System.EventHandler(this.tsmPicSmall_Click);
            // 
            // tsmPicOrigin
            // 
            this.tsmPicOrigin.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmPicOrigin.Image = global::OCRTools.Properties.Resources.原始;
            this.tsmPicOrigin.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmPicOrigin.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmPicOrigin.Name = "tsmPicOrigin";
            this.tsmPicOrigin.Size = new System.Drawing.Size(34, 36);
            this.tsmPicOrigin.Text = "toolStripButton4";
            this.tsmPicOrigin.ToolTipText = "原始尺寸";
            this.tsmPicOrigin.Click += new System.EventHandler(this.tsmPicOrigin_Click);
            // 
            // tsmRotate
            // 
            this.tsmRotate.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmRotate.Image = global::OCRTools.Properties.Resources.Rotate;
            this.tsmRotate.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmRotate.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmRotate.Name = "tsmRotate";
            this.tsmRotate.Size = new System.Drawing.Size(34, 36);
            this.tsmRotate.Text = "toolStripButton4";
            this.tsmRotate.ToolTipText = "向右旋转";
            this.tsmRotate.Visible = false;
            this.tsmRotate.Click += new System.EventHandler(this.tsmRotate_Click);
            // 
            // tsbReOcr
            // 
            this.tsbReOcr.ForeColor = System.Drawing.Color.Black;
            this.tsbReOcr.Image = global::OCRTools.Properties.Resources.文本_小;
            this.tsbReOcr.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsbReOcr.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsbReOcr.Name = "tsbReOcr";
            this.tsbReOcr.Size = new System.Drawing.Size(34, 36);
            this.tsbReOcr.ToolTipText = "重新识别";
            this.tsbReOcr.Visible = false;
            this.tsbReOcr.Click += new System.EventHandler(this.tsbReOcr_Click);
            // 
            // tsbSaveImg
            // 
            this.tsbSaveImg.ForeColor = System.Drawing.Color.Black;
            this.tsbSaveImg.Image = global::OCRTools.Properties.Resources.导出;
            this.tsbSaveImg.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsbSaveImg.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsbSaveImg.Name = "tsbSaveImg";
            this.tsbSaveImg.Size = new System.Drawing.Size(34, 36);
            this.tsbSaveImg.ToolTipText = "另存为";
            this.tsbSaveImg.Visible = false;
            this.tsbSaveImg.Click += new System.EventHandler(this.tsbSaveImg_Click);
            // 
            // tsmModel
            // 
            this.tsmModel.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmModel.Image = global::OCRTools.Properties.Resources.文字模式;
            this.tsmModel.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmModel.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmModel.Name = "tsmModel";
            this.tsmModel.Size = new System.Drawing.Size(36, 36);
            this.tsmModel.Click += new System.EventHandler(this.tsmModel_Click);
            // 
            // tsmNewWindow
            // 
            this.tsmNewWindow.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmNewWindow.Image = global::OCRTools.Properties.Resources.预览;
            this.tsmNewWindow.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmNewWindow.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmNewWindow.Name = "tsmNewWindow";
            this.tsmNewWindow.Size = new System.Drawing.Size(34, 36);
            this.tsmNewWindow.Text = "toolStripButton1";
            this.tsmNewWindow.ToolTipText = "新窗口预览";
            this.tsmNewWindow.Visible = false;
            this.tsmNewWindow.Click += new System.EventHandler(this.tsmPicView_Click);
            // 
            // tsmFullScreen
            // 
            this.tsmFullScreen.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmFullScreen.Image = global::OCRTools.Properties.Resources.全屏;
            this.tsmFullScreen.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmFullScreen.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmFullScreen.Name = "tsmFullScreen";
            this.tsmFullScreen.Size = new System.Drawing.Size(34, 36);
            this.tsmFullScreen.ToolTipText = "全屏查看";
            this.tsmFullScreen.Visible = false;
            this.tsmFullScreen.Click += new System.EventHandler(this.tsmFullScreen_Click);
            // 
            // UcContent
            // 
            this.Controls.Add(this.toolResize);
            this.Controls.Add(this.txtContent);
            this.Controls.Add(this.wbContent);
            this.Controls.Add(this.imageBox);
            this.Controls.Add(this.dgContent);
            this.Margin = new System.Windows.Forms.Padding(0);
            this.Name = "UcContent";
            this.Size = new System.Drawing.Size(470, 351);
            this.toolResize.ResumeLayout(false);
            this.toolResize.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgContent)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.RichTextBox txtContent;
        private DataGridViewEx dgContent;
        private WebBrowser2 wbContent;
        private PanelPictureView imageBox;
        private ToolStrip toolResize;
        private ToolStripButton tsmCopy;
        private ToolStripButton tsmModel;
        private ToolStripButton tsmPicBig;
        private ToolStripButton tsmPicOrigin;
        private ToolStripButton tsmPicSmall;
        private ToolStripButton tsmNewWindow;
        private ToolStripButton tsmTrans;
        private ToolStripLabel txtPicZoomPercent;
        private ToolStripButton tsmSearch;
        private ToolStripButton tsmFullScreen;
        private ToolStripButton tsmRotate;
        private ToolStripButton tsbSaveImg;
        private ToolStripButton tsbReOcr;
        private ToolStripDropDownButton tsmPicType;
        private ToolStripDropDownButton tsmImageViewBackStyle;
        private ToolStripMenuItem toolStripMenuItem20;
        private ToolStripMenuItem toolStripMenuItem21;
        private ToolStripMenuItem toolStripMenuItem22;
        private ToolStripMenuItem toolStripMenuItem24;
        private ToolStripMenuItem toolStripMenuItem25;
        private ToolStripMenuItem 文档矫正ToolStripMenuItem;
        private ToolStripMenuItem 图像增强ToolStripMenuItem;
        private ToolStripButton tsmEdit;
    }
}
