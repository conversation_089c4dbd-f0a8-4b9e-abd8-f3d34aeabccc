﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>UIAutomationTypes</name>
  </assembly>
  <members>
    <member name="T:System.Windows.Automation.AsyncContentLoadedEventArgs">
      <summary>Provides data for a <see cref="F:System.Windows.Automation.AutomationElement.AsyncContentLoadedEvent" />. </summary>
    </member>
    <member name="M:System.Windows.Automation.AsyncContentLoadedEventArgs.#ctor(System.Windows.Automation.AsyncContentLoadedState,System.Double)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.AsyncContentLoadedEventArgs" /> class.</summary>
      <param name="asyncContentState">The state of content loading.</param>
      <param name="percentComplete">Percentage of content that has been loaded.</param>
    </member>
    <member name="P:System.Windows.Automation.AsyncContentLoadedEventArgs.AsyncContentLoadedState">
      <summary>Gets the state of the content loading. </summary>
      <returns>One of the <see cref="T:System.Windows.Automation.AsyncContentLoadedState" /> values.</returns>
    </member>
    <member name="P:System.Windows.Automation.AsyncContentLoadedEventArgs.PercentComplete">
      <summary>Gets the percentage of content that has been loaded. </summary>
      <returns>The percentage of content that has been loaded.</returns>
    </member>
    <member name="T:System.Windows.Automation.AsyncContentLoadedState">
      <summary>Contains values that specify the state of the content being loaded into a content element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AsyncContentLoadedState.Beginning">
      <summary>Specifies that asynchronous loading of the content into the UI Automation element is beginning.</summary>
    </member>
    <member name="F:System.Windows.Automation.AsyncContentLoadedState.Progress">
      <summary>Specifies that asynchronous loading of the content into the UI Automation element is in progress. UI Automation providers should specify the percent complete so that client applications will know how much more content remains.</summary>
    </member>
    <member name="F:System.Windows.Automation.AsyncContentLoadedState.Completed">
      <summary>Specifies that asynchronous loading of the content into the UI Automation element is complete.</summary>
    </member>
    <member name="T:System.Windows.Automation.AutomationElementIdentifiers">
      <summary>Contains values used as identifiers by UI Automation providers.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.AcceleratorKeyProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.AcceleratorKey" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.AccessKeyProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.AccessKey" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.AsyncContentLoadedEvent">
      <summary>Identifies an event raised during asynchronous content-loading.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.AutomationFocusChangedEvent">
      <summary>Identifies an event that is raised when the focus has changed.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.AutomationIdProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.AutomationId" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.AutomationPropertyChangedEvent">
      <summary>Identifies a property-changed event.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.BoundingRectangleProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.BoundingRectangle" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.ClassNameProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.ClassName" /> property. </summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.ClickablePointProperty">
      <summary>Identifies the <see cref="F:System.Windows.Automation.AutomationElement.ClickablePointProperty" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.ControlTypeProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.ControlType" /> property. </summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.CultureProperty">
      <summary>Identifies the culture property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.FrameworkIdProperty">
      <summary>Identifies the property that contains the underlying framework's name for the element. </summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.HasKeyboardFocusProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.HasKeyboardFocus" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.HelpTextProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.HelpText" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsContentElementProperty">
      <summary>Identifies the property that indicates whether the element contains content that is valuable to the end user.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsControlElementProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.IsControlElement" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsDockPatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.DockPattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsEnabledProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.IsEnabled" /> property, which specifies whether the user interface (UI) item referenced by the <see cref="T:System.Windows.Automation.AutomationElement" /> is enabled.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsExpandCollapsePatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.ExpandCollapsePattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsGridItemPatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.GridItemPattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsGridPatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.GridPattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsInvokePatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.InvokePattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsItemContainerPatternAvailableProperty"></member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsKeyboardFocusableProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.IsKeyboardFocusable" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsMultipleViewPatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.MultipleViewPattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsOffscreenProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.IsOffscreen" /> property, which indicates whether the UI Automation element is visible. </summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsPasswordProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.IsPassword" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsRangeValuePatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.RangeValuePattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsRequiredForFormProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.IsRequiredForForm" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsScrollItemPatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.ScrollItemPattern" /> is available for this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsScrollPatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.ScrollPattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsSelectionItemPatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.SelectionItemPattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsSelectionPatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.SelectionPattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsSynchronizedInputPatternAvailableProperty"></member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsTableItemPatternAvailableProperty">
      <summary>Identifies the property that indicates whether the <see cref="T:System.Windows.Automation.TableItemPattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsTablePatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.TablePattern" /> is available on this UI Automation element. </summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsTextPatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.TextPattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsTogglePatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.TogglePattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsTransformPatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.TransformPattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsValuePatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.ValuePattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsVirtualizedItemPatternAvailableProperty"></member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.IsWindowPatternAvailableProperty">
      <summary>Identifies the property that indicates whether <see cref="T:System.Windows.Automation.WindowPattern" /> is available on this UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.ItemStatusProperty">
      <summary>Identifies the property that specifies the status of the visual representation of a complex item.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.ItemTypeProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.ItemType" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.LabeledByProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.LabeledBy" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.LayoutInvalidatedEvent">
      <summary>Identifies the event that is raised when the layout is invalidated.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.LocalizedControlTypeProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.LocalizedControlType" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.MenuClosedEvent">
      <summary>Identifies the event that is raised when a menu is closed.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.MenuOpenedEvent">
      <summary>Identifies the event that is raised when a menu is opened.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.NameProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.Name" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.NativeWindowHandleProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.NativeWindowHandle" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.NotSupported">
      <summary>Indicates that a property is not supported.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.OrientationProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.Orientation" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.ProcessIdProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.AutomationElement.AutomationElementInformation.ProcessId" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.RuntimeIdProperty">
      <summary>Identifies the property that contains the runtime identifier of the element.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.StructureChangedEvent">
      <summary>Identifies the event that is raised when the UI Automation tree structure is changed.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.ToolTipClosedEvent">
      <summary>Identifies the event that is raised when a ToolTip is closed.</summary>
    </member>
    <member name="F:System.Windows.Automation.AutomationElementIdentifiers.ToolTipOpenedEvent">
      <summary>Identifies the event that is raised when a ToolTip is opened.</summary>
    </member>
    <member name="T:System.Windows.Automation.AutomationEvent">
      <summary>Identifies a UI Automation event.</summary>
    </member>
    <member name="M:System.Windows.Automation.AutomationEvent.LookupById(System.Int32)">
      <summary>Retrieves an <see cref="T:System.Windows.Automation.AutomationEvent" /> that encapsulates the specified numerical identifier.</summary>
      <returns>A new <see cref="T:System.Windows.Automation.AutomationEvent" />.</returns>
      <param name="id">Identifier of the event.</param>
    </member>
    <member name="T:System.Windows.Automation.AutomationEventArgs">
      <summary>Provides data for UI Automation events that are passed to an <see cref="T:System.Windows.Automation.AutomationEventHandler" /> delegate.</summary>
    </member>
    <member name="M:System.Windows.Automation.AutomationEventArgs.#ctor(System.Windows.Automation.AutomationEvent)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.AutomationEventArgs" /> class.</summary>
      <param name="eventId">The event identifier.</param>
    </member>
    <member name="P:System.Windows.Automation.AutomationEventArgs.EventId">
      <summary>Gets the event identifier.</summary>
      <returns>The event identifier.</returns>
    </member>
    <member name="T:System.Windows.Automation.AutomationEventHandler">
      <summary>Represents the method implemented by the UI Automation client application to handle an event raised by a UI Automation provider. </summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">Information about the event.</param>
    </member>
    <member name="T:System.Windows.Automation.AutomationIdentifier">
      <summary>Base class for types that identify control types, events, patterns, properties, and text attributes in UI Automation.</summary>
    </member>
    <member name="M:System.Windows.Automation.AutomationIdentifier.CompareTo(System.Object)">
      <summary>Compares this <see cref="T:System.Windows.Automation.AutomationIdentifier" /> with another <see cref="T:System.Windows.Automation.AutomationIdentifier" />.</summary>
      <returns>The hash code of this object minus the hash code of <paramref name="obj" />.</returns>
      <param name="obj">The object to compare this one with.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> is null.</exception>
    </member>
    <member name="M:System.Windows.Automation.AutomationIdentifier.Equals(System.Object)">
      <summary>Returns a value indicating whether the supplied <see cref="T:System.Windows.Automation.AutomationIdentifier" /> is equivalent to this <see cref="T:System.Windows.Automation.AutomationIdentifier" />.</summary>
      <returns>true if the objects are equivalent; otherwise false.</returns>
      <param name="obj">An <see cref="T:System.Windows.Automation.AutomationIdentifier" /> object to compare with this identifier.</param>
    </member>
    <member name="M:System.Windows.Automation.AutomationIdentifier.GetHashCode">
      <summary>Returns the hash code for this UI Automation identifier.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="P:System.Windows.Automation.AutomationIdentifier.Id">
      <summary>Gets the underlying numerical identifier.</summary>
      <returns>The underlying identifier.</returns>
    </member>
    <member name="P:System.Windows.Automation.AutomationIdentifier.ProgrammaticName">
      <summary>Gets the registered programmatic name.</summary>
      <returns>The programmatic name.</returns>
    </member>
    <member name="T:System.Windows.Automation.AutomationPattern">
      <summary>Identifies a control pattern.</summary>
    </member>
    <member name="M:System.Windows.Automation.AutomationPattern.LookupById(System.Int32)">
      <summary>Retrieves an <see cref="T:System.Windows.Automation.AutomationPattern" /> that encapsulates a specified numerical identifier.</summary>
      <returns>The <see cref="T:System.Windows.Automation.AutomationPattern" /> specified by <paramref name="id" />.</returns>
      <param name="id">Identifier of the pattern.</param>
    </member>
    <member name="T:System.Windows.Automation.AutomationProperty">
      <summary>Identifies a property of an <see cref="T:System.Windows.Automation.AutomationElement" />. </summary>
    </member>
    <member name="M:System.Windows.Automation.AutomationProperty.LookupById(System.Int32)">
      <summary>Retrieves an <see cref="T:System.Windows.Automation.AutomationProperty" /> that encapsulates a specified numerical identifier.</summary>
      <param name="id">The property identifier.</param>
    </member>
    <member name="T:System.Windows.Automation.AutomationPropertyChangedEventArgs">
      <summary>Provides information about a property-changed event.</summary>
    </member>
    <member name="M:System.Windows.Automation.AutomationPropertyChangedEventArgs.#ctor(System.Windows.Automation.AutomationProperty,System.Object,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.AutomationPropertyChangedEventArgs" /> class.</summary>
      <param name="property">The identifier of the property that has changed.</param>
      <param name="oldValue">The previous value of the property.</param>
      <param name="newValue">The new value of the property.</param>
    </member>
    <member name="P:System.Windows.Automation.AutomationPropertyChangedEventArgs.NewValue">
      <summary>Gets the new value of a property that has changed.</summary>
      <returns>The new value of the property.</returns>
    </member>
    <member name="P:System.Windows.Automation.AutomationPropertyChangedEventArgs.OldValue">
      <summary>Gets the old value of a property that has changed.</summary>
      <returns>The previous value of the property, or null (Nothing in Microsoft Visual Basic .NET) if the previous value is not readily available.</returns>
    </member>
    <member name="P:System.Windows.Automation.AutomationPropertyChangedEventArgs.Property">
      <summary>Gets an <see cref="T:System.Windows.Automation.AutomationProperty" /> identifier indicating which property changed.</summary>
      <returns>The identifier of the property that has changed.</returns>
    </member>
    <member name="T:System.Windows.Automation.AutomationPropertyChangedEventHandler">
      <summary>Represents the method implemented by the UI Automation client application to handle the event raised by a UI Automation provider when a property has changed.</summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">Information about the event.</param>
    </member>
    <member name="T:System.Windows.Automation.AutomationTextAttribute">
      <summary>Identifies UI Automation text attributes.</summary>
    </member>
    <member name="M:System.Windows.Automation.AutomationTextAttribute.LookupById(System.Int32)">
      <summary>Retrieves a <see cref="T:System.Windows.Automation.AutomationTextAttribute" /> that encapsulates a specified numerical identifier.</summary>
      <param name="id">The numerical identifier.</param>
    </member>
    <member name="T:System.Windows.Automation.ControlType">
      <summary>Identifies the type of a user interface (UI) control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Button">
      <summary>Identifies a button control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Calendar">
      <summary>Identifies a calendar control, such as a date-picker.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.CheckBox">
      <summary>Identifies a check box control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.ComboBox">
      <summary>Identifies a combo box control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Custom">
      <summary>Identifies a control that is not one of the defined control types.  </summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.DataGrid">
      <summary>Identifies a data grid control. </summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.DataItem">
      <summary>Identifies a data item control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Document">
      <summary>Identifies a document control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Edit">
      <summary>Identifies an edit control, such as a text box.</summary>
    </member>
    <member name="M:System.Windows.Automation.ControlType.GetNeverSupportedPatterns">
      <summary>Retrieves the pattern identifiers that are not supported by the control type.</summary>
      <returns>An array of UI Automation pattern identifiers.</returns>
    </member>
    <member name="M:System.Windows.Automation.ControlType.GetRequiredPatternSets">
      <summary>Retrieves an array of sets of required patterns. </summary>
      <returns>An array of sets of required patterns.</returns>
    </member>
    <member name="M:System.Windows.Automation.ControlType.GetRequiredProperties">
      <summary>Retrieves an array of the required property identifiers (IDs) for this control type.</summary>
      <returns>An array of property IDs. </returns>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Group">
      <summary>Identifies a group control, which acts as a container for other controls.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Header">
      <summary>Identifies a header control, which is a container for the labels of rows and columns of information. </summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.HeaderItem">
      <summary>Identifies a header item, which is the label for a row or column of information. </summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Hyperlink">
      <summary>Identifies a hyperlink control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Image">
      <summary>Identifies an image control. </summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.List">
      <summary>Identifies a list control, such as a list box.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.ListItem">
      <summary>Identifies a list item control, which is a child item of a list control.  </summary>
    </member>
    <member name="P:System.Windows.Automation.ControlType.LocalizedControlType">
      <summary>Gets a description of the control type. </summary>
      <returns>A localized description of the control type, such as "button". </returns>
    </member>
    <member name="M:System.Windows.Automation.ControlType.LookupById(System.Int32)">
      <summary>Retrieves a <see cref="T:System.Windows.Automation.ControlType" /> that encapsulates a specified numerical identifier. </summary>
      <param name="id">Identifier of the control type.</param>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Menu">
      <summary>Identifies a menu control, such as a top-level menu in an application window.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.MenuBar">
      <summary>Identifies a menu bar control, which generally contains a set of top-level menus. </summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.MenuItem">
      <summary>Identifies a menu item control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Pane">
      <summary>Identifies a pane control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.ProgressBar">
      <summary>Identifies a progress bar control, which visually indicates the progress of a lengthy operation.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.RadioButton">
      <summary>Identifies a radio button control, which is a selection mechanism allowing exactly one selected item in a group.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.ScrollBar">
      <summary>Identifies a scroll bar control, such as a scroll bar in an application window.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Separator">
      <summary>Identifies a separator, which creates a visual division in controls like menus and toolbars. </summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Slider">
      <summary>Identifies a slider control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Spinner">
      <summary>Identifies a spinner control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.SplitButton">
      <summary>Identifies a split button, which is a button that performs a default action and can also expand to a list of other possible actions. </summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.StatusBar">
      <summary>Identifies a status bar control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Tab">
      <summary>Identifies a tab control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.TabItem">
      <summary>Identifies a tab item control, which represents a page of a tab control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Table">
      <summary>Identifies a table.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Text">
      <summary>Identifies an edit control, such as a text box or rich text box.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Thumb">
      <summary>Identifies the control in a scrollbar that can be dragged to a different position.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.TitleBar">
      <summary>Identifies the caption bar on a window. </summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.ToolBar">
      <summary>Identifies a toolbar, such as the control that contains a set of command buttons in an application window.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.ToolTip">
      <summary>Identifies a tooltip control, an informational window that appears as a result of moving the pointer over a control or sometimes when tabbing to a control using the keyboard.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Tree">
      <summary>Identifies a tree control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.TreeItem">
      <summary>Identifies a node in a <see cref="F:System.Windows.Automation.ControlType.TreeItem" /> control.</summary>
    </member>
    <member name="F:System.Windows.Automation.ControlType.Window">
      <summary>Identifies a window frame, which contains child objects. </summary>
    </member>
    <member name="T:System.Windows.Automation.DockPatternIdentifiers">
      <summary>Contains values used as identifiers for <see cref="T:System.Windows.Automation.Provider.IDockProvider" />. </summary>
    </member>
    <member name="F:System.Windows.Automation.DockPatternIdentifiers.DockPositionProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IDockProvider.DockPosition" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.DockPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.DockPattern" /> pattern.</summary>
    </member>
    <member name="T:System.Windows.Automation.DockPosition">
      <summary>Contains values that specify the dock position of an object, represented by a <see cref="T:System.Windows.Automation.DockPattern" />, within a docking container.</summary>
    </member>
    <member name="F:System.Windows.Automation.DockPosition.Top">
      <summary>Indicates that the UI Automation element is docked along the top edge of the docking container. </summary>
    </member>
    <member name="F:System.Windows.Automation.DockPosition.Left">
      <summary>Indicates that the UI Automation element is docked along the left edge of the docking container. </summary>
    </member>
    <member name="F:System.Windows.Automation.DockPosition.Bottom">
      <summary>Indicates that the UI Automation element is docked along the bottom edge of the docking container. </summary>
    </member>
    <member name="F:System.Windows.Automation.DockPosition.Right">
      <summary>Indicates that the UI Automation element is docked along the right edge of the docking container. </summary>
    </member>
    <member name="F:System.Windows.Automation.DockPosition.Fill">
      <summary>Indicates that the UI Automation element is docked along all edges of the docking container and fills all available space within the container. </summary>
    </member>
    <member name="F:System.Windows.Automation.DockPosition.None">
      <summary>Indicates that the UI Automation element is not docked to any edge of the docking container. </summary>
    </member>
    <member name="T:System.Windows.Automation.ElementNotAvailableException">
      <summary>Contains information about the exception that is raised when an attempt is made to access an UI Automation element corresponding to a part of the user interface that is no longer available.</summary>
    </member>
    <member name="M:System.Windows.Automation.ElementNotAvailableException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.ElementNotAvailableException" /> class. </summary>
    </member>
    <member name="M:System.Windows.Automation.ElementNotAvailableException.#ctor(System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.ElementNotAvailableException" /> class with a reference to the inner exception that caused this exception. </summary>
      <param name="innerException">The exception that caused this exception.</param>
    </member>
    <member name="M:System.Windows.Automation.ElementNotAvailableException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.ElementNotAvailableException" /> class with serialization information. </summary>
      <param name="info">Data needed to serialize or deserialize the object. </param>
      <param name="context">The source and destination of the serialized stream.</param>
    </member>
    <member name="M:System.Windows.Automation.ElementNotAvailableException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.ElementNotAvailableException" /> class with a specified error message. </summary>
      <param name="message">The description of the error.</param>
    </member>
    <member name="M:System.Windows.Automation.ElementNotAvailableException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.ElementNotAvailableException" /> class with a specified error message and a reference to the inner exception that caused this exception. </summary>
      <param name="message">The description of the error.</param>
      <param name="innerException">The exception that caused this exception.</param>
    </member>
    <member name="M:System.Windows.Automation.ElementNotAvailableException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Retrieves data needed to serialize the target object. </summary>
      <param name="info">Data needed to serialize or deserialize the object.</param>
      <param name="context">The destination of the serialized stream.</param>
    </member>
    <member name="T:System.Windows.Automation.ElementNotEnabledException">
      <summary>Contains information about the exception that is raised when an attempt is made to manipulate a control that is not enabled. </summary>
    </member>
    <member name="M:System.Windows.Automation.ElementNotEnabledException.#ctor">
      <summary> Initializes a new instance of the <see cref="T:System.Windows.Automation.ElementNotEnabledException" /> class. </summary>
    </member>
    <member name="M:System.Windows.Automation.ElementNotEnabledException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.ElementNotEnabledException" /> class with serialization information.</summary>
      <param name="info">Data needed to serialize or deserialize the object. </param>
      <param name="context">The source and destination of the serialized stream.</param>
    </member>
    <member name="M:System.Windows.Automation.ElementNotEnabledException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.ElementNotEnabledException" /> class with a specified error message. </summary>
      <param name="message">The description of the error.</param>
    </member>
    <member name="M:System.Windows.Automation.ElementNotEnabledException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.ElementNotEnabledException" /> class with a specified error message and a reference to the inner exception that caused this exception. </summary>
      <param name="message">The description of the error.</param>
      <param name="innerException">The exception that caused this exception.</param>
    </member>
    <member name="M:System.Windows.Automation.ElementNotEnabledException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Retrieves data needed to serialize the target object. </summary>
      <param name="info">Data needed to serialize or deserialize the object.</param>
      <param name="context">The destination of the serialized stream.</param>
    </member>
    <member name="T:System.Windows.Automation.ExpandCollapsePatternIdentifiers">
      <summary>Contains values used as identifiers by <see cref="T:System.Windows.Automation.Provider.IExpandCollapseProvider" />. </summary>
    </member>
    <member name="F:System.Windows.Automation.ExpandCollapsePatternIdentifiers.ExpandCollapseStateProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IExpandCollapseProvider.ExpandCollapseState" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.ExpandCollapsePatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.ExpandCollapsePattern" /> control pattern.</summary>
    </member>
    <member name="T:System.Windows.Automation.ExpandCollapseState">
      <summary>Contains values that specify the <see cref="T:System.Windows.Automation.ExpandCollapseState" /> of a UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.ExpandCollapseState.Collapsed">
      <summary>No child nodes, controls, or content of the UI Automation element are displayed. </summary>
    </member>
    <member name="F:System.Windows.Automation.ExpandCollapseState.Expanded">
      <summary>All child nodes, controls or content of the UI Automation element are displayed. </summary>
    </member>
    <member name="F:System.Windows.Automation.ExpandCollapseState.PartiallyExpanded">
      <summary>Some, but not all, child nodes, controls, or content of the UI Automation element are displayed. </summary>
    </member>
    <member name="F:System.Windows.Automation.ExpandCollapseState.LeafNode">
      <summary>The UI Automation element has no child nodes, controls, or content to display. </summary>
    </member>
    <member name="T:System.Windows.Automation.GridItemPatternIdentifiers">
      <summary>Contains values used as identifiers by <see cref="T:System.Windows.Automation.Provider.IGridItemProvider" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.GridItemPatternIdentifiers.ColumnProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IGridItemProvider.Column" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.GridItemPatternIdentifiers.ColumnSpanProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IGridItemProvider.ColumnSpan" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.GridItemPatternIdentifiers.ContainingGridProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IGridItemProvider.ContainingGrid" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.GridItemPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.GridItemPattern" /> pattern.</summary>
    </member>
    <member name="F:System.Windows.Automation.GridItemPatternIdentifiers.RowProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IGridItemProvider.Row" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.GridItemPatternIdentifiers.RowSpanProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IGridItemProvider.RowSpan" /> property.</summary>
    </member>
    <member name="T:System.Windows.Automation.GridPatternIdentifiers">
      <summary>Contains values used as identifiers by <see cref="T:System.Windows.Automation.Provider.IGridProvider" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.GridPatternIdentifiers.ColumnCountProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IGridProvider.ColumnCount" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.GridPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.GridPattern" /> pattern.</summary>
    </member>
    <member name="F:System.Windows.Automation.GridPatternIdentifiers.RowCountProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IGridProvider.RowCount" /> property.</summary>
    </member>
    <member name="T:System.Windows.Automation.InvokePatternIdentifiers">
      <summary>Contains values used as identifiers by <see cref="T:System.Windows.Automation.Provider.IInvokeProvider" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.InvokePatternIdentifiers.InvokedEvent">
      <summary>Identifies the event raised when a control is activated. </summary>
    </member>
    <member name="F:System.Windows.Automation.InvokePatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.InvokePattern" /> control pattern. </summary>
    </member>
    <member name="T:System.Windows.Automation.ItemContainerPatternIdentifiers">
      <summary>Contains values used as identifiers by <see cref="T:System.Windows.Automation.Provider.IItemContainerProvider" />. </summary>
    </member>
    <member name="F:System.Windows.Automation.ItemContainerPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.ItemContainerPattern" /> control pattern.</summary>
    </member>
    <member name="T:System.Windows.Automation.MultipleViewPatternIdentifiers">
      <summary>Contains values used as identifiers by <see cref="T:System.Windows.Automation.Provider.IMultipleViewProvider" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.MultipleViewPatternIdentifiers.CurrentViewProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IMultipleViewProvider.CurrentView" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.MultipleViewPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.MultipleViewPattern" /> control pattern.</summary>
    </member>
    <member name="F:System.Windows.Automation.MultipleViewPatternIdentifiers.SupportedViewsProperty">
      <summary>Identifies the property that gets the control-specific collection of views.</summary>
    </member>
    <member name="T:System.Windows.Automation.NoClickablePointException">
      <summary>Contains information about the exception that is raised when <see cref="M:System.Windows.Automation.AutomationElement.GetClickablePoint" /> is called on a UI Automation element that has no clickable point.</summary>
    </member>
    <member name="M:System.Windows.Automation.NoClickablePointException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.NoClickablePointException" /> class.</summary>
    </member>
    <member name="M:System.Windows.Automation.NoClickablePointException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.NoClickablePointException" /> class with serialization information.</summary>
      <param name="info">Data needed to serialize or deserialize the object. </param>
      <param name="context">The source and destination of the serialized stream.</param>
    </member>
    <member name="M:System.Windows.Automation.NoClickablePointException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.NoClickablePointException" /> class with a specified error message.</summary>
      <param name="message">The description of the error.</param>
    </member>
    <member name="M:System.Windows.Automation.NoClickablePointException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.NoClickablePointException" /> class with a specified error message and a reference to the inner exception that caused this exception.</summary>
      <param name="message">The description of the error.</param>
      <param name="innerException">The exception that caused this exception.</param>
    </member>
    <member name="M:System.Windows.Automation.NoClickablePointException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Retrieves data needed to serialize the target object. </summary>
      <param name="info">Data needed to serialize or deserialize the object.</param>
      <param name="context">The destination of the serialized stream.</param>
    </member>
    <member name="T:System.Windows.Automation.OrientationType">
      <summary>Contains values that specify the orientation of a control.</summary>
    </member>
    <member name="F:System.Windows.Automation.OrientationType.None">
      <summary>Specifies that the control has no orientation.</summary>
    </member>
    <member name="F:System.Windows.Automation.OrientationType.Horizontal">
      <summary>Specifies that the control has horizontal orientation.</summary>
    </member>
    <member name="F:System.Windows.Automation.OrientationType.Vertical">
      <summary>Specifies that the control has vertical orientation.</summary>
    </member>
    <member name="T:System.Windows.Automation.ProxyAssemblyNotLoadedException">
      <summary>Contains information about an exception that is raised when there is a problem loading an assembly that contains client-side providers</summary>
    </member>
    <member name="M:System.Windows.Automation.ProxyAssemblyNotLoadedException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.ProxyAssemblyNotLoadedException" /> class. Used internally.</summary>
    </member>
    <member name="M:System.Windows.Automation.ProxyAssemblyNotLoadedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.ProxyAssemblyNotLoadedException" /> class with serialization information. Used internally.</summary>
      <param name="info">Data needed to serialize or deserialize the object.</param>
      <param name="context">The source and destination of the serialized stream.</param>
    </member>
    <member name="M:System.Windows.Automation.ProxyAssemblyNotLoadedException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.ProxyAssemblyNotLoadedException" /> class with an error message. Used internally.</summary>
      <param name="message">The description of the error.</param>
    </member>
    <member name="M:System.Windows.Automation.ProxyAssemblyNotLoadedException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.ProxyAssemblyNotLoadedException" /> class with a specified error message and a reference to the inner exception that caused this exception. Used internally.</summary>
      <param name="message">The description of the error.</param>
      <param name="innerException">The exception that caused this exception.</param>
    </member>
    <member name="M:System.Windows.Automation.ProxyAssemblyNotLoadedException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Retrieves data needed to serialize the target object. Used internally.</summary>
      <param name="info">Data needed to serialize or deserialize the object.</param>
      <param name="context">The destination of the serialized stream.</param>
    </member>
    <member name="T:System.Windows.Automation.RangeValuePatternIdentifiers">
      <summary>Contains values used as identifiers for <see cref="T:System.Windows.Automation.Provider.IRangeValueProvider" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.RangeValuePatternIdentifiers.IsReadOnlyProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IRangeValueProvider.IsReadOnly" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.RangeValuePatternIdentifiers.LargeChangeProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IRangeValueProvider.LargeChange" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.RangeValuePatternIdentifiers.MaximumProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IRangeValueProvider.Maximum" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.RangeValuePatternIdentifiers.MinimumProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IRangeValueProvider.Minimum" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.RangeValuePatternIdentifiers.Pattern">
      <summary>Identifies this pattern as a <see cref="T:System.Windows.Automation.RangeValuePattern" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.RangeValuePatternIdentifiers.SmallChangeProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IRangeValueProvider.SmallChange" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.RangeValuePatternIdentifiers.ValueProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IRangeValueProvider.Value" /> property.</summary>
    </member>
    <member name="T:System.Windows.Automation.RowOrColumnMajor">
      <summary>Contains values that specify whether data in a table should be read primarily by row or by column.</summary>
    </member>
    <member name="F:System.Windows.Automation.RowOrColumnMajor.RowMajor">
      <summary>Specifies that data in the table should be read row by row. </summary>
    </member>
    <member name="F:System.Windows.Automation.RowOrColumnMajor.ColumnMajor">
      <summary>Specifies that data in the table should be read column by column </summary>
    </member>
    <member name="F:System.Windows.Automation.RowOrColumnMajor.Indeterminate">
      <summary>Specifies that the best way to present the data is indeterminate.</summary>
    </member>
    <member name="T:System.Windows.Automation.ScrollAmount">
      <summary>Contains values used by <see cref="T:System.Windows.Automation.ScrollPattern" /> to indicate the direction and distance to scroll.</summary>
    </member>
    <member name="F:System.Windows.Automation.ScrollAmount.LargeDecrement">
      <summary>Specifies that scrolling is done in large decrements, equivalent to PageUp or clicking on a blank part of a scrollbar. If PageUp is not a relevant amount for the control and/or no scrollbar exists, the value represents an amount equal to the current visible window. </summary>
    </member>
    <member name="F:System.Windows.Automation.ScrollAmount.SmallDecrement">
      <summary>Specifies that scrolling is done in small decrements, equivalent to pressing an arrow key or clicking the arrow button on a scrollbar. </summary>
    </member>
    <member name="F:System.Windows.Automation.ScrollAmount.NoAmount">
      <summary>Specifies that scrolling should not be performed.</summary>
    </member>
    <member name="F:System.Windows.Automation.ScrollAmount.LargeIncrement">
      <summary>Specifies that scrolling is done in large increments, equivalent to a PageDown or clicking on the track of a scrollbar component. If a PageDown is not a relevant amount for the control and/or no scrollbar exists, the value represents an amount equal to the current visible region. </summary>
    </member>
    <member name="F:System.Windows.Automation.ScrollAmount.SmallIncrement">
      <summary>Specifies that scrolling is done in small increments, equivalent to pressing an arrow key or clicking the arrow button on a scrollbar. </summary>
    </member>
    <member name="T:System.Windows.Automation.ScrollItemPatternIdentifiers">
      <summary>Contains values used as identifiers by <see cref="T:System.Windows.Automation.Provider.IScrollItemProvider" />. </summary>
    </member>
    <member name="F:System.Windows.Automation.ScrollItemPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.ScrollItemPattern" /> pattern.</summary>
    </member>
    <member name="T:System.Windows.Automation.ScrollPatternIdentifiers">
      <summary>Contains values used as identifiers for <see cref="T:System.Windows.Automation.Provider.IScrollProvider" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.ScrollPatternIdentifiers.HorizontallyScrollableProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IScrollProvider.HorizontallyScrollable" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.ScrollPatternIdentifiers.HorizontalScrollPercentProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IScrollProvider.HorizontalScrollPercent" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.ScrollPatternIdentifiers.HorizontalViewSizeProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IScrollProvider.HorizontalViewSize" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.ScrollPatternIdentifiers.NoScroll">
      <summary>Specifies that scrolling should not be performed.</summary>
    </member>
    <member name="F:System.Windows.Automation.ScrollPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.ScrollPattern" /> pattern.</summary>
    </member>
    <member name="F:System.Windows.Automation.ScrollPatternIdentifiers.VerticallyScrollableProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IScrollProvider.VerticallyScrollable" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.ScrollPatternIdentifiers.VerticalScrollPercentProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IScrollProvider.VerticalScrollPercent" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.ScrollPatternIdentifiers.VerticalViewSizeProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IScrollProvider.VerticalViewSize" /> property.</summary>
    </member>
    <member name="T:System.Windows.Automation.SelectionItemPatternIdentifiers">
      <summary>Contains values used as identifiers by <see cref="T:System.Windows.Automation.Provider.ISelectionItemProvider" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.SelectionItemPatternIdentifiers.ElementAddedToSelectionEvent">
      <summary>Identifies the event raised when an item is added to a collection of selected items.</summary>
    </member>
    <member name="F:System.Windows.Automation.SelectionItemPatternIdentifiers.ElementRemovedFromSelectionEvent">
      <summary>Identifies the event raised when an item is removed from a collection of selected items.</summary>
    </member>
    <member name="F:System.Windows.Automation.SelectionItemPatternIdentifiers.ElementSelectedEvent">
      <summary>Identifies the event that is raised when a single item is selected (causing all previously selected items to become deselected).</summary>
    </member>
    <member name="F:System.Windows.Automation.SelectionItemPatternIdentifiers.IsSelectedProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.ISelectionItemProvider.IsSelected" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.SelectionItemPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.SelectionItemPattern" /> pattern.</summary>
    </member>
    <member name="F:System.Windows.Automation.SelectionItemPatternIdentifiers.SelectionContainerProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.SelectionItemPattern.SelectionItemPatternInformation.SelectionContainer" /> property.</summary>
    </member>
    <member name="T:System.Windows.Automation.SelectionPatternIdentifiers">
      <summary>Contains values used as identifiers for <see cref="T:System.Windows.Automation.Provider.ISelectionProvider" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.SelectionPatternIdentifiers.CanSelectMultipleProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.ISelectionProvider.CanSelectMultiple" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.SelectionPatternIdentifiers.InvalidatedEvent">
      <summary>Identifies the event that is raised when a selection in a container has changed significantly and requires sending more addition and removal events than the <see cref="F:System.Windows.Automation.Provider.AutomationInteropProvider.InvalidateLimit" /> constant permits.</summary>
    </member>
    <member name="F:System.Windows.Automation.SelectionPatternIdentifiers.IsSelectionRequiredProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.ISelectionProvider.IsSelectionRequired" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.SelectionPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.SelectionPattern" /> pattern.</summary>
    </member>
    <member name="F:System.Windows.Automation.SelectionPatternIdentifiers.SelectionProperty">
      <summary>Identifies the property that gets the selected items in a container.</summary>
    </member>
    <member name="T:System.Windows.Automation.StructureChangedEventArgs">
      <summary>Contains information about the event that is raised when the UI Automation tree structure has changed.</summary>
    </member>
    <member name="M:System.Windows.Automation.StructureChangedEventArgs.#ctor(System.Windows.Automation.StructureChangeType,System.Int32[])">
      <summary>Initializes a new instance of the <see cref="P:System.Windows.Automation.StructureChangedEventArgs.StructureChangeType" /> class, specifying the type of change and the identifier (ID) of the element whose structure changed.</summary>
      <param name="structureChangeType">A single value that specifies the type of change.</param>
      <param name="runtimeId">The runtime identifier (ID) of the UI Automation element whose structure changed. See Remarks.</param>
    </member>
    <member name="M:System.Windows.Automation.StructureChangedEventArgs.GetRuntimeId">
      <summary>Retrieves the UI Automation runtime identifier (ID) of the UI Automation element whose structure changed.</summary>
      <returns>The runtime ID of the UI Automation element whose structure changed.</returns>
    </member>
    <member name="P:System.Windows.Automation.StructureChangedEventArgs.StructureChangeType">
      <summary>Gets a value indicating the type of change that occurred in the UI Automation tree structure.</summary>
      <returns>The type of changed that occurred.</returns>
    </member>
    <member name="T:System.Windows.Automation.StructureChangedEventHandler">
      <summary>Represents the method implemented by the client application to handle the event raised when the UI Automation tree structure has changed.</summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">Information about the event.</param>
    </member>
    <member name="T:System.Windows.Automation.StructureChangeType">
      <summary>Contains values that specify changes in the structure of the Microsoft UI Automation element tree.</summary>
    </member>
    <member name="F:System.Windows.Automation.StructureChangeType.ChildAdded">
      <summary>A child element was added to the UI Automation element tree.</summary>
    </member>
    <member name="F:System.Windows.Automation.StructureChangeType.ChildRemoved">
      <summary>A child element was removed from the UI Automation element tree.</summary>
    </member>
    <member name="F:System.Windows.Automation.StructureChangeType.ChildrenInvalidated">
      <summary>Child elements were invalidated in the UI Automation element tree. This might mean that one or more children were added or removed, depending on the UI Automation providers implementation.</summary>
    </member>
    <member name="F:System.Windows.Automation.StructureChangeType.ChildrenBulkAdded">
      <summary>Child elements were added to the UI Automation element tree.</summary>
    </member>
    <member name="F:System.Windows.Automation.StructureChangeType.ChildrenBulkRemoved">
      <summary>Child elements were removed from the UI Automation element tree.</summary>
    </member>
    <member name="F:System.Windows.Automation.StructureChangeType.ChildrenReordered">
      <summary>The order of the child elements in the UI Automation element tree changed.</summary>
    </member>
    <member name="T:System.Windows.Automation.SupportedTextSelection">
      <summary>Contains values that specify whether a text provider supports selection and, if so, whether it supports a single, continuous selection or multiple, disjoint selections.</summary>
    </member>
    <member name="F:System.Windows.Automation.SupportedTextSelection.None">
      <summary>Does not support text selections.</summary>
    </member>
    <member name="F:System.Windows.Automation.SupportedTextSelection.Single">
      <summary>Supports a single, continuous text selection.</summary>
    </member>
    <member name="F:System.Windows.Automation.SupportedTextSelection.Multiple">
      <summary>Supports multiple, disjoint text selections.</summary>
    </member>
    <member name="T:System.Windows.Automation.SynchronizedInputPatternIdentifiers">
      <summary>Contains values used as identifiers by <see cref="T:System.Windows.Automation.Provider.ISynchronizedInputProvider" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.SynchronizedInputPatternIdentifiers.InputDiscardedEvent">
      <summary>Identifies the event raised when the input was discarded by WPF. </summary>
    </member>
    <member name="F:System.Windows.Automation.SynchronizedInputPatternIdentifiers.InputReachedOtherElementEvent">
      <summary>Identifies the event raised when the input was received by an element other than the one currently listening for the input.</summary>
    </member>
    <member name="F:System.Windows.Automation.SynchronizedInputPatternIdentifiers.InputReachedTargetEvent">
      <summary>Identifies the event raised when the input was received by the element currently listening for the input.</summary>
    </member>
    <member name="F:System.Windows.Automation.SynchronizedInputPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.SynchronizedInputPattern" /> control pattern.</summary>
    </member>
    <member name="T:System.Windows.Automation.SynchronizedInputType">
      <summary>Contains values that specify the type of synchronized input. </summary>
    </member>
    <member name="F:System.Windows.Automation.SynchronizedInputType.KeyUp">
      <summary>A key has been released.</summary>
    </member>
    <member name="F:System.Windows.Automation.SynchronizedInputType.KeyDown">
      <summary>A key has been pressed.</summary>
    </member>
    <member name="F:System.Windows.Automation.SynchronizedInputType.MouseLeftButtonUp">
      <summary>The left mouse button has been released.</summary>
    </member>
    <member name="F:System.Windows.Automation.SynchronizedInputType.MouseLeftButtonDown">
      <summary>The left mouse button has been pressed.</summary>
    </member>
    <member name="F:System.Windows.Automation.SynchronizedInputType.MouseRightButtonUp">
      <summary>The right mouse button has been released.</summary>
    </member>
    <member name="F:System.Windows.Automation.SynchronizedInputType.MouseRightButtonDown">
      <summary>The right mouse button has been pressed.</summary>
    </member>
    <member name="T:System.Windows.Automation.TableItemPatternIdentifiers">
      <summary>Contains values used as identifiers by <see cref="T:System.Windows.Automation.Provider.ITableItemProvider" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.TableItemPatternIdentifiers.ColumnHeaderItemsProperty">
      <summary>Identifies the property that retrieves all the column headers associated with a table item or cell.</summary>
    </member>
    <member name="F:System.Windows.Automation.TableItemPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.TableItemPattern" /> pattern.</summary>
    </member>
    <member name="F:System.Windows.Automation.TableItemPatternIdentifiers.RowHeaderItemsProperty">
      <summary>Identifies the property that retrieves all the row headers associated with a table item or cell.</summary>
    </member>
    <member name="T:System.Windows.Automation.TablePatternIdentifiers">
      <summary>Contains values used as identifiers for <see cref="T:System.Windows.Automation.TablePattern" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.TablePatternIdentifiers.ColumnHeadersProperty">
      <summary>Identifies the property that calls the <see cref="M:System.Windows.Automation.Provider.ITableProvider.GetColumnHeaders" /> method.</summary>
    </member>
    <member name="F:System.Windows.Automation.TablePatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.TablePattern" /> pattern.</summary>
    </member>
    <member name="F:System.Windows.Automation.TablePatternIdentifiers.RowHeadersProperty">
      <summary>Identifies the property that calls the <see cref="M:System.Windows.Automation.Provider.ITableProvider.GetRowHeaders" /> method.</summary>
    </member>
    <member name="F:System.Windows.Automation.TablePatternIdentifiers.RowOrColumnMajorProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.TablePattern.TablePatternInformation.RowOrColumnMajor" /> property.</summary>
    </member>
    <member name="T:System.Windows.Automation.TextPatternIdentifiers">
      <summary>Contains values used as identifiers for <see cref="T:System.Windows.Automation.Provider.ITextProvider" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.AnimationStyleAttribute">
      <summary>Identifies the <see cref="T:System.Windows.Automation.Text.AnimationStyle" /> attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.BackgroundColorAttribute">
      <summary>Identifies the BackgroundColor attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.BulletStyleAttribute">
      <summary>Identifies the <see cref="T:System.Windows.Automation.Text.BulletStyle" /> attribute of a text range. </summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.CapStyleAttribute">
      <summary>Identifies the <see cref="T:System.Windows.Automation.Text.CapStyle" /> attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.CultureAttribute">
      <summary>Identifies the Culture (<see cref="T:System.Globalization.CultureInfo" />) attribute of a text range down to the sub-language level; for example, French–Switzerland (fr-CH) instead of French (fr). </summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.FontNameAttribute">
      <summary>Identifies the FontName attribute of a text range. </summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.FontSizeAttribute">
      <summary>Identifies the FontSize attribute of a text range. </summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.FontWeightAttribute">
      <summary>Identifies the FontWeight attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.ForegroundColorAttribute">
      <summary>Identifies the ForegroundColor (COLORREF) attribute of a text range. </summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.HorizontalTextAlignmentAttribute">
      <summary>Identifies the <see cref="T:System.Windows.Automation.Text.HorizontalTextAlignment" /> attribute of a text range. </summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.IndentationFirstLineAttribute">
      <summary>Identifies the IndentationFirstLine (<see cref="P:System.Windows.Documents.Paragraph.TextIndent" />) attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.IndentationLeadingAttribute">
      <summary>Identifies the IndentationLeading (<see cref="P:System.Windows.Documents.Paragraph.TextIndent" />) attribute of a text range. </summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.IndentationTrailingAttribute">
      <summary>Identifies the IndentationTrailing (<see cref="P:System.Windows.Documents.Paragraph.TextIndent" />) attribute of a text range. </summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.IsHiddenAttribute">
      <summary>Identifies the IsHidden attribute of a text range. </summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.IsItalicAttribute">
      <summary>Identifies the IsItalic (<see cref="T:System.Windows.FontStyle" />) attribute of a text range. </summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.IsReadOnlyAttribute">
      <summary>Identifies the IsReadOnly attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.IsSubscriptAttribute">
      <summary>Identifies the IsSubscript (<see cref="T:System.Windows.FontVariants" />) attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.IsSuperscriptAttribute">
      <summary>Identifies the IsSuperscript (<see cref="T:System.Windows.FontVariants" />) attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.MarginBottomAttribute">
      <summary>Identifies the MarginBottom (<see cref="T:System.Drawing.Printing.PageSettings" />) attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.MarginLeadingAttribute">
      <summary>Identifies the MarginLeading (<see cref="T:System.Drawing.Printing.PageSettings" />) attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.MarginTopAttribute">
      <summary>Identifies the MarginTop (<see cref="T:System.Drawing.Printing.PageSettings" />) attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.MarginTrailingAttribute">
      <summary>Identifies the MarginTrailing (<see cref="T:System.Drawing.Printing.PageSettings" />) attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.MixedAttributeValue">
      <summary>Identifies whether the value of a given attribute varies over a text range in a rich edit control.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.OutlineStylesAttribute">
      <summary>Identifies the OutlineStyles (<see cref="T:System.Windows.Automation.Text.OutlineStyles" />) attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.OverlineColorAttribute">
      <summary>Identifies the OverlineColor attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.OverlineStyleAttribute">
      <summary>Identifies the OverlineStyle (<see cref="T:System.Windows.Automation.Text.TextDecorationLineStyle" />) attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.TextPattern" /> pattern. </summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.StrikethroughColorAttribute">
      <summary>Identifies the StrikethroughColor attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.StrikethroughStyleAttribute">
      <summary>Identifies the StrikethroughStyle (<see cref="T:System.Windows.Automation.Text.TextDecorationLineStyle" />) attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.TabsAttribute">
      <summary>Identifies the Tabs attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.TextChangedEvent">
      <summary>Identifies the event raised whenever textual content is modified.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.TextFlowDirectionsAttribute">
      <summary>Identifies the TextFlowDirections (<see cref="T:System.Windows.Automation.Text.FlowDirections" />) attribute of a text range.</summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.TextSelectionChangedEvent">
      <summary>Identifies the event raised whenever the text selection is modified. </summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.UnderlineColorAttribute">
      <summary>Identifies the UnderlineColor attribute of a text range. </summary>
    </member>
    <member name="F:System.Windows.Automation.TextPatternIdentifiers.UnderlineStyleAttribute">
      <summary>Identifies the UnderlineStyle (<see cref="T:System.Windows.Automation.Text.TextDecorationLineStyle" />) attribute of a text range. </summary>
    </member>
    <member name="T:System.Windows.Automation.TogglePatternIdentifiers">
      <summary>Contains values used as identifiers by <see cref="T:System.Windows.Automation.Provider.IToggleProvider" />. </summary>
    </member>
    <member name="F:System.Windows.Automation.TogglePatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.TogglePattern" /> control pattern.</summary>
    </member>
    <member name="F:System.Windows.Automation.TogglePatternIdentifiers.ToggleStateProperty">
      <summary>Identifies the <see cref="T:System.Windows.Automation.ToggleState" /> of the UI Automation element.</summary>
    </member>
    <member name="T:System.Windows.Automation.ToggleState">
      <summary>Contains values that specify the <see cref="T:System.Windows.Automation.ToggleState" /> of a UI Automation element.</summary>
    </member>
    <member name="F:System.Windows.Automation.ToggleState.Off">
      <summary>The UI Automation element is not selected, checked, marked or otherwise activated.</summary>
    </member>
    <member name="F:System.Windows.Automation.ToggleState.On">
      <summary>The UI Automation element is selected, checked, marked or otherwise activated.</summary>
    </member>
    <member name="F:System.Windows.Automation.ToggleState.Indeterminate">
      <summary>The UI Automation element is in an indeterminate state.</summary>
    </member>
    <member name="T:System.Windows.Automation.TransformPatternIdentifiers">
      <summary>Contains values used as identifiers for <see cref="T:System.Windows.Automation.Provider.ITransformProvider" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.TransformPatternIdentifiers.CanMoveProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.ITransformProvider.CanMove" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.TransformPatternIdentifiers.CanResizeProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.ITransformProvider.CanResize" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.TransformPatternIdentifiers.CanRotateProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.ITransformProvider.CanRotate" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.TransformPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.TransformPattern" /> control pattern.</summary>
    </member>
    <member name="T:System.Windows.Automation.TreeScope">
      <summary>Contains values that specify the scope of elements within the UI Automation tree.</summary>
    </member>
    <member name="F:System.Windows.Automation.TreeScope.Element">
      <summary>Specifies that the search include the element itself. </summary>
    </member>
    <member name="F:System.Windows.Automation.TreeScope.Children">
      <summary>Specifies that the search include the element's immediate children. </summary>
    </member>
    <member name="F:System.Windows.Automation.TreeScope.Descendants">
      <summary>Specifies that the search include the element's descendants, including children.</summary>
    </member>
    <member name="F:System.Windows.Automation.TreeScope.Parent">
      <summary>Specifies that the search include the element's parent. Not supported.</summary>
    </member>
    <member name="F:System.Windows.Automation.TreeScope.Ancestors">
      <summary>Specifies that the search include the element's ancestors, including the parent. Not supported.</summary>
    </member>
    <member name="F:System.Windows.Automation.TreeScope.Subtree">
      <summary>Specifies that the search include the root of the search and all descendants.</summary>
    </member>
    <member name="T:System.Windows.Automation.ValuePatternIdentifiers">
      <summary>Contains values used as identifiers for <see cref="T:System.Windows.Automation.Provider.IValueProvider" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.ValuePatternIdentifiers.IsReadOnlyProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IValueProvider.IsReadOnly" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.ValuePatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.ValuePattern" /> control pattern.</summary>
    </member>
    <member name="F:System.Windows.Automation.ValuePatternIdentifiers.ValueProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.Provider.IValueProvider.Value" /> property.</summary>
    </member>
    <member name="T:System.Windows.Automation.VirtualizedItemPatternIdentifiers">
      <summary>Contains values used as identifiers by <see cref="T:System.Windows.Automation.Provider.IVirtualizedItemProvider" />. </summary>
    </member>
    <member name="F:System.Windows.Automation.VirtualizedItemPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.VirtualizedItemPattern" /> control pattern.</summary>
    </member>
    <member name="T:System.Windows.Automation.WindowClosedEventArgs">
      <summary>Contains information about the event that is raised when a window is closed.</summary>
    </member>
    <member name="M:System.Windows.Automation.WindowClosedEventArgs.#ctor(System.Int32[])">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Automation.WindowClosedEventArgs" /> class.</summary>
      <param name="runtimeId">The runtime identifier of the window that has closed.</param>
    </member>
    <member name="M:System.Windows.Automation.WindowClosedEventArgs.GetRuntimeId">
      <summary>Retrieves the UI Automation runtime identifier (ID) associated with this event.</summary>
      <returns>The UI Automation runtime ID of the window on which the event was raised.</returns>
    </member>
    <member name="T:System.Windows.Automation.WindowInteractionState">
      <summary>Contains values that specify the current state of the window for purposes of user or programmatic interaction.</summary>
    </member>
    <member name="F:System.Windows.Automation.WindowInteractionState.Running">
      <summary>Indicates that the window is running. This does not guarantee that the window is responding or ready for user interaction.</summary>
    </member>
    <member name="F:System.Windows.Automation.WindowInteractionState.Closing">
      <summary>Indicates that the window is closing. </summary>
    </member>
    <member name="F:System.Windows.Automation.WindowInteractionState.ReadyForUserInteraction">
      <summary>Indicates that the window is ready for user interaction.</summary>
    </member>
    <member name="F:System.Windows.Automation.WindowInteractionState.BlockedByModalWindow">
      <summary>Indicates that the window is blocked by a modal window. </summary>
    </member>
    <member name="F:System.Windows.Automation.WindowInteractionState.NotResponding">
      <summary>Indicates that the window is not responding. </summary>
    </member>
    <member name="T:System.Windows.Automation.WindowPatternIdentifiers">
      <summary>Contains values used as identifiers by <see cref="T:System.Windows.Automation.Provider.IWindowProvider" />. </summary>
    </member>
    <member name="F:System.Windows.Automation.WindowPatternIdentifiers.CanMaximizeProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.WindowPattern.WindowPatternInformation.CanMaximize" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.WindowPatternIdentifiers.CanMinimizeProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.WindowPattern.WindowPatternInformation.CanMinimize" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.WindowPatternIdentifiers.IsModalProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.WindowPattern.WindowPatternInformation.IsModal" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.WindowPatternIdentifiers.IsTopmostProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.WindowPattern.WindowPatternInformation.IsTopmost" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.WindowPatternIdentifiers.Pattern">
      <summary>Identifies the <see cref="T:System.Windows.Automation.WindowPattern" /> pattern.</summary>
    </member>
    <member name="F:System.Windows.Automation.WindowPatternIdentifiers.WindowClosedEvent">
      <summary>Identifies the event that is raised when a window is closed.</summary>
    </member>
    <member name="F:System.Windows.Automation.WindowPatternIdentifiers.WindowInteractionStateProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.WindowPattern.WindowPatternInformation.WindowInteractionState" /> property.</summary>
    </member>
    <member name="F:System.Windows.Automation.WindowPatternIdentifiers.WindowOpenedEvent">
      <summary>Identifies the event that is raised when a window is opened.</summary>
    </member>
    <member name="F:System.Windows.Automation.WindowPatternIdentifiers.WindowVisualStateProperty">
      <summary>Identifies the <see cref="P:System.Windows.Automation.WindowPattern.WindowPatternInformation.WindowVisualState" /> property.</summary>
    </member>
    <member name="T:System.Windows.Automation.WindowVisualState">
      <summary>Contains values that specify the visual state of a window.</summary>
    </member>
    <member name="F:System.Windows.Automation.WindowVisualState.Normal">
      <summary>Specifies that the window is normal (restored). </summary>
    </member>
    <member name="F:System.Windows.Automation.WindowVisualState.Maximized">
      <summary>Specifies that the window is maximized. </summary>
    </member>
    <member name="F:System.Windows.Automation.WindowVisualState.Minimized">
      <summary>Specifies that the window is minimized.</summary>
    </member>
    <member name="T:System.Windows.Automation.Text.AnimationStyle">
      <summary>Represents values for <see cref="F:System.Windows.Automation.TextPattern.AnimationStyleAttribute" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.AnimationStyle.None">
      <summary>Specifies that there is no animation style.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.AnimationStyle.LasVegasLights">
      <summary>Specifies that the bounding rectangle displays a border of alternating icons of different colors.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.AnimationStyle.BlinkingBackground">
      <summary>Specifies that the font and background alternate between assigned colors and contrasting colors.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.AnimationStyle.SparkleText">
      <summary>Specifies that the background displays flashing, multi-colored icons.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.AnimationStyle.MarchingBlackAnts">
      <summary>Specifies that the bounding rectangle displays moving black dashes.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.AnimationStyle.MarchingRedAnts">
      <summary>Specifies that the bounding rectangle displays moving red dashes.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.AnimationStyle.Shimmer">
      <summary>Specifies that the font alternates between solid and blurred.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.AnimationStyle.Other">
      <summary>Specifies that the animation style is one not explicitly itemized in the <see cref="T:System.Windows.Automation.Text.AnimationStyle" /> enumeration.</summary>
    </member>
    <member name="T:System.Windows.Automation.Text.BulletStyle">
      <summary>Values for <see cref="F:System.Windows.Automation.TextPattern.BulletStyleAttribute" />. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.BulletStyle.None">
      <summary>No bullet style. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.BulletStyle.HollowRoundBullet">
      <summary>Hollow round bullets. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.BulletStyle.FilledRoundBullet">
      <summary>Solid round bullets.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.BulletStyle.HollowSquareBullet">
      <summary>Hollow square bullets.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.BulletStyle.FilledSquareBullet">
      <summary>Solid square bullets. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.BulletStyle.DashBullet">
      <summary>Dash bullets. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.BulletStyle.Other">
      <summary>Another bullet style not explicitly specified in the <see cref="T:System.Windows.Automation.Text.BulletStyle" /> enumeration. </summary>
    </member>
    <member name="T:System.Windows.Automation.Text.CapStyle">
      <summary>Represents values for <see cref="F:System.Windows.Automation.TextPattern.CapStyleAttribute" />. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.CapStyle.None">
      <summary>Indicates that there is no capitalization style; uppercase and lowercase letters render normally.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.CapStyle.SmallCap">
      <summary>Indicates that the capitalization style is small caps; lowercase letters are replaced with uppercase letters of the same approximate height as the font's x-height.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.CapStyle.AllCap">
      <summary>Indicates that the capitalization style is all caps; all lowercase letters are replaced with uppercase letters.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.CapStyle.AllPetiteCaps">
      <summary>Indicates that the capitalization style is all petite caps; uppercase and lowercase letters are replaced with uppercase letters slightly smaller than small caps.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.CapStyle.PetiteCaps">
      <summary>Indicates that the capitalization style is petite caps; lowercase letters are replaced with uppercase letters slightly smaller than small caps.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.CapStyle.Unicase">
      <summary>Indicates that the capitalization style is unicase; uppercase and lowercase letters are rendered in a way determined by the type designer. A unicase alphabet is one that does not specify case for its letters.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.CapStyle.Titling">
      <summary>Indicates that the capitalization style is title case; uppercase and lowercase letters are rendered with letters specifically designed for titles.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.CapStyle.Other">
      <summary>Indicates that the capitalization style is a style not explicitly specified in the <see cref="T:System.Windows.Automation.Text.CapStyle" /> enumeration. </summary>
    </member>
    <member name="T:System.Windows.Automation.Text.FlowDirections">
      <summary>Values for <see cref="F:System.Windows.Automation.TextPattern.TextFlowDirectionsAttribute" />. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.FlowDirections.Default">
      <summary>Top to bottom, left to right, horizontal.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.FlowDirections.RightToLeft">
      <summary>Right to left. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.FlowDirections.BottomToTop">
      <summary>Bottom to top. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.FlowDirections.Vertical">
      <summary>Vertical. </summary>
    </member>
    <member name="T:System.Windows.Automation.Text.HorizontalTextAlignment">
      <summary>Values for <see cref="F:System.Windows.Automation.TextPattern.HorizontalTextAlignmentAttribute" /></summary>
    </member>
    <member name="F:System.Windows.Automation.Text.HorizontalTextAlignment.Left">
      <summary>Aligned left.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.HorizontalTextAlignment.Centered">
      <summary>Centered.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.HorizontalTextAlignment.Right">
      <summary>Aligned right. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.HorizontalTextAlignment.Justified">
      <summary>Justified. </summary>
    </member>
    <member name="T:System.Windows.Automation.Text.OutlineStyles">
      <summary>Values for <see cref="F:System.Windows.Automation.TextPattern.OutlineStylesAttribute" />. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.OutlineStyles.None">
      <summary>No outline style. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.OutlineStyles.Outline">
      <summary>Outlined. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.OutlineStyles.Shadow">
      <summary>Shadowed.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.OutlineStyles.Engraved">
      <summary>Engraved.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.OutlineStyles.Embossed">
      <summary>Embossed.</summary>
    </member>
    <member name="T:System.Windows.Automation.Text.TextDecorationLineStyle">
      <summary>Values for <see cref="F:System.Windows.Automation.TextPattern.UnderlineStyleAttribute" />.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.None">
      <summary>No text decoration line style.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.Single">
      <summary>Single line.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.WordsOnly">
      <summary>Words only. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.Double">
      <summary>Double line.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.Dot">
      <summary>Dotted line. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.Dash">
      <summary>Dashed line. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.DashDot">
      <summary>Alternating dash-dot line. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.DashDotDot">
      <summary>Alternating dash-dot-dot line. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.Wavy">
      <summary>Wavy line. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.ThickSingle">
      <summary>Thick single line. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.DoubleWavy">
      <summary>Double wavy line. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.ThickWavy">
      <summary>Thick wavy line.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.LongDash">
      <summary>Long-dashed line.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.ThickDash">
      <summary>Thick dashed line. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.ThickDashDot">
      <summary>Thick alternating dash-dot line. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.ThickDashDotDot">
      <summary>Thick alternating dash-dot-dot line.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.ThickDot">
      <summary>Thick dotted line. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.ThickLongDash">
      <summary>Thick long-dash line. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextDecorationLineStyle.Other">
      <summary>Another text decoration line style not explicitly specified in the <see cref="T:System.Windows.Automation.Text.TextDecorationLineStyle" /> enumeration. </summary>
    </member>
    <member name="T:System.Windows.Automation.Text.TextPatternRangeEndpoint">
      <summary>Allows the endpoints to be identified when calling methods of <see cref="T:System.Windows.Automation.Text.TextPatternRange" />. Each <see cref="T:System.Windows.Automation.Text.TextPatternRange" /> has two endpoints (<see cref="F:System.Windows.Automation.Text.TextPatternRangeEndpoint.Start" /> and <see cref="F:System.Windows.Automation.Text.TextPatternRangeEndpoint.End" />). </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextPatternRangeEndpoint.Start">
      <summary>Identifies the starting point of the range. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextPatternRangeEndpoint.End">
      <summary> Identifies the ending point of the range. </summary>
    </member>
    <member name="T:System.Windows.Automation.Text.TextUnit">
      <summary>Represents pre-defined units of text for the purposes of navigation within a document. </summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextUnit.Character">
      <summary>Specifies that the text unit is one character in length.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextUnit.Format">
      <summary>Specifies that the text unit is the length of a single, common format specification, such as bold, italic, or similar.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextUnit.Word">
      <summary>Specifies that the text unit is one word in length.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextUnit.Line">
      <summary>Specifies that the text unit is one line in length.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextUnit.Paragraph">
      <summary>Specifies that the text unit is one paragraph in length.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextUnit.Page">
      <summary>Specifies that the text unit is one document-specific page in length.</summary>
    </member>
    <member name="F:System.Windows.Automation.Text.TextUnit.Document">
      <summary>Specifies that the text unit is an entire document in length.</summary>
    </member>
  </members>
</doc>