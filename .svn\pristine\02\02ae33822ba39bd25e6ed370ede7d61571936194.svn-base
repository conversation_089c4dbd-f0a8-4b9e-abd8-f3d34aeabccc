using OCRTools.Common;
using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;

[ToolboxItem(false)]
public class ScrollControl : Control
{
    public class HScrollProperties : ScrollProperties
    {
        public HScrollProperties(ScrollControl container)
            : base(container)
        {
        }
    }

    public abstract class ScrollProperties
    {
        private readonly ScrollControl _container;

        [DefaultValue(true)]
        public bool Enabled { get; set; }

        [DefaultValue(10)]
        public int LargeChange { get; set; }

        [DefaultValue(100)]
        public int Maximum { get; set; }

        [DefaultValue(0)]
        public int Minimum { get; set; }

        public ScrollControl ParentControl => _container;

        [DefaultValue(1)]
        public int SmallChange { get; set; }

        [Bindable(true)]
        [DefaultValue(0)]
        public int Value { get; set; }

        [DefaultValue(false)]
        public bool Visible { get; set; }

        protected ScrollProperties(ScrollControl container)
        {
            _container = container;
        }
    }

    public class VScrollProperties : ScrollProperties
    {
        public VScrollProperties(ScrollControl container)
            : base(container)
        {
        }
    }

    private bool _alwaysShowHScroll;

    private bool _alwaysShowVScroll;

    private BorderStyle _borderStyle;

    private Size _pageSize;

    private Size _scrollSize;

    private Size _stepSize;

    protected override CreateParams CreateParams
    {
        get
        {
            CreateParams createParams = base.CreateParams;
            switch (_borderStyle)
            {
                case BorderStyle.FixedSingle:
                    createParams.Style |= 8388608;
                    break;
                case BorderStyle.Fixed3D:
                    createParams.ExStyle |= 512;
                    break;
            }
            return createParams;
        }
    }

    [Category("Layout")]
    [DefaultValue(false)]
    public bool AlwaysShowHScroll
    {
        get
        {
            return _alwaysShowHScroll;
        }
        set
        {
            if (_alwaysShowHScroll != value)
            {
                _alwaysShowHScroll = value;
                if (value)
                {
                    NativeMethods.SCROLLINFO sCROLLINFO = new NativeMethods.SCROLLINFO();
                    sCROLLINFO.fMask = NativeMethods.SIF.SIF_RANGE | NativeMethods.SIF.SIF_DISABLENOSCROLL;
                    sCROLLINFO.nMin = 0;
                    sCROLLINFO.nMax = 0;
                    sCROLLINFO.nPage = 1;
                    SetScrollInfo(ScrollOrientation.HorizontalScroll, sCROLLINFO, refresh: false);
                    Invalidate();
                }
                else
                {
                    UpdateHorizontalScrollbar();
                }
            }
        }
    }

    [Category("Layout")]
    [DefaultValue(false)]
    public bool AlwaysShowVScroll
    {
        get
        {
            return _alwaysShowVScroll;
        }
        set
        {
            bool vScroll = VScroll;
            _alwaysShowVScroll = value;
            if (_alwaysShowVScroll != vScroll)
            {
                if (_alwaysShowVScroll)
                {
                    NativeMethods.SCROLLINFO sCROLLINFO = new NativeMethods.SCROLLINFO();
                    sCROLLINFO.fMask = NativeMethods.SIF.SIF_RANGE | NativeMethods.SIF.SIF_DISABLENOSCROLL;
                    sCROLLINFO.nMin = 0;
                    sCROLLINFO.nMax = 0;
                    sCROLLINFO.nPage = 1;
                    SetScrollInfo(ScrollOrientation.VerticalScroll, sCROLLINFO, refresh: false);
                    Invalidate();
                }
                else
                {
                    UpdateVerticalScrollbar();
                }
            }
        }
    }

    [Category("Appearance")]
    [DefaultValue(typeof(BorderStyle), "None")]
    public virtual BorderStyle BorderStyle
    {
        get
        {
            return _borderStyle;
        }
        set
        {
            if (BorderStyle != value)
            {
                _borderStyle = value;
                OnBorderStyleChanged(EventArgs.Empty);
            }
        }
    }

    [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
    [Browsable(false)]
    public HScrollProperties HorizontalScroll { get; protected set; }

    [Browsable(false)]
    [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
    public virtual Size PageSize
    {
        get
        {
            return _pageSize;
        }
        set
        {
            if (value.Width < 0)
            {
                throw new ArgumentOutOfRangeException("value", "Width must be a positive integer.");
            }
            if (value.Height < 0)
            {
                throw new ArgumentOutOfRangeException("value", "Height must be a positive integer.");
            }
            if (PageSize != value)
            {
                _pageSize = value;
                OnPageSizeChanged(EventArgs.Empty);
            }
        }
    }

    [Browsable(false)]
    [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
    public virtual Size ScrollSize
    {
        get
        {
            return _scrollSize;
        }
        set
        {
            if (value.Width < 0)
            {
                throw new ArgumentOutOfRangeException("value", "Width must be a positive integer.");
            }
            if (value.Height < 0)
            {
                throw new ArgumentOutOfRangeException("value", "Height must be a positive integer.");
            }
            if (ScrollSize != value)
            {
                _scrollSize = value;
                OnScrollSizeChanged(EventArgs.Empty);
            }
        }
    }

    [Category("Layout")]
    [DefaultValue(typeof(Size), "10, 10")]
    public virtual Size StepSize
    {
        get
        {
            return _stepSize;
        }
        set
        {
            if (value.Width < 0)
            {
                throw new ArgumentOutOfRangeException("value", "Width must be a positive integer.");
            }
            if (value.Height < 0)
            {
                throw new ArgumentOutOfRangeException("value", "Height must be a positive integer.");
            }
            if (StepSize != value)
            {
                _stepSize = value;
                OnStepSizeChanged(EventArgs.Empty);
            }
        }
    }

    [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
    [Browsable(false)]
    public VScrollProperties VerticalScroll { get; protected set; }

    [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
    [Browsable(false)]
    protected bool HScroll
    {
        get
        {
            return (NativeMethods.GetWindowLong(base.Handle, -16) & 0x100000) == 1048576;
        }
        set
        {
            uint windowLong = NativeMethods.GetWindowLong(base.Handle, -16);
            windowLong = ((!value) ? (windowLong & 0xFFEFFFFFu) : (windowLong | 0x100000u));
            NativeMethods.SetWindowLong(base.Handle, -16, windowLong);
        }
    }

    [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
    [Browsable(false)]
    protected bool VScroll
    {
        get
        {
            return (NativeMethods.GetWindowLong(base.Handle, -16) & 0x200000) == 2097152;
        }
        set
        {
            uint windowLong = NativeMethods.GetWindowLong(base.Handle, -16);
            windowLong = ((!value) ? (windowLong & 0xFFDFFFFFu) : (windowLong | 0x200000u));
            NativeMethods.SetWindowLong(base.Handle, -16, windowLong);
        }
    }

    [Browsable(false)]
    [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
    protected bool WheelScrollsControl { get; set; }

    [Category("Property Changed")]
    public event EventHandler BorderStyleChanged;

    [Category("Property Changed")]
    public event EventHandler PageSizeChanged;

    [Category("Action")]
    public event ScrollEventHandler Scroll;

    [Category("Property Changed")]
    public event EventHandler ScrollSizeChanged;

    [Category("Property Changed")]
    public event EventHandler StepSizeChanged;

    public ScrollControl()
    {
        BorderStyle = BorderStyle.None;
        ScrollSize = Size.Empty;
        PageSize = Size.Empty;
        StepSize = new Size(10, 10);
        HorizontalScroll = new HScrollProperties(this);
        VerticalScroll = new VScrollProperties(this);
    }

    protected override void OnEnabledChanged(EventArgs e)
    {
        base.OnEnabledChanged(e);
        UpdateScrollbars();
    }

    protected override void OnMouseDown(MouseEventArgs e)
    {
        base.OnMouseDown(e);
        if (!Focused)
        {
            Focus();
        }
    }

    protected override void OnMouseWheel(MouseEventArgs e)
    {
        if (WheelScrollsControl)
        {
            int num = HorizontalScroll.Value;
            int num2 = VerticalScroll.Value;
            if (VerticalScroll.Visible && VerticalScroll.Enabled)
            {
                int num3 = ((Control.ModifierKeys != Keys.Control) ? (SystemInformation.MouseWheelScrollLines * VerticalScroll.SmallChange) : VerticalScroll.LargeChange);
                num2 += ((e.Delta > 0) ? (-num3) : num3);
            }
            else if (HorizontalScroll.Visible && HorizontalScroll.Enabled)
            {
                int num3 = ((Control.ModifierKeys != Keys.Control) ? (SystemInformation.MouseWheelScrollLines * HorizontalScroll.SmallChange) : HorizontalScroll.LargeChange);
                num += ((e.Delta > 0) ? (-num3) : num3);
            }
            ScrollTo(num, num2);
        }
        base.OnMouseWheel(e);
    }

    [DebuggerStepThrough]
    protected override void WndProc(ref Message msg)
    {
        int msg2 = msg.Msg;
        if ((uint)(msg2 - 276) <= 1u)
        {
            WmScroll(ref msg);
        }
        else
        {
            base.WndProc(ref msg);
        }
    }

    public void ScrollTo(int x, int y)
    {
        ScrollTo(ScrollOrientation.HorizontalScroll, x);
        ScrollTo(ScrollOrientation.VerticalScroll, y);
    }

    protected ScrollEventType GetEventType(IntPtr wParam)
    {
        var type = wParam.ToInt32() & 0xFFFF;
        switch (type)
        {

            case 7: return ScrollEventType.Last;
            case 8: return ScrollEventType.EndScroll;
            case 1: return ScrollEventType.SmallIncrement;
            case 0: return ScrollEventType.SmallDecrement;
            case 3: return ScrollEventType.LargeIncrement;
            case 2: return ScrollEventType.LargeDecrement;
            case 4: return ScrollEventType.ThumbPosition;
            case 5: return ScrollEventType.ThumbTrack;
            case 6: return ScrollEventType.First;
        }
        throw new ArgumentException($"{wParam} isn't a valid scroll event type.", "wparam");

    }

    protected virtual void OnBorderStyleChanged(EventArgs e)
    {
        UpdateStyles();
        this.BorderStyleChanged?.Invoke(this, e);
    }

    protected virtual void OnPageSizeChanged(EventArgs e)
    {
        UpdateScrollbars();
        this.PageSizeChanged?.Invoke(this, e);
    }

    protected virtual void OnScroll(ScrollEventArgs e)
    {
        UpdateHorizontalScroll();
        UpdateVerticalScroll();
        this.Scroll?.Invoke(this, e);
    }

    protected virtual void OnScrollSizeChanged(EventArgs e)
    {
        UpdateScrollbars();
        this.ScrollSizeChanged?.Invoke(this, e);
    }

    protected virtual void OnStepSizeChanged(EventArgs e)
    {
        this.StepSizeChanged?.Invoke(this, e);
    }

    protected virtual void ScrollTo(ScrollOrientation scrollbar, int value)
    {
        NativeMethods.SCROLLINFO scrollInfo = GetScrollInfo(scrollbar);
        if (value > scrollInfo.nMax - scrollInfo.nMin + 1 - scrollInfo.nPage)
        {
            value = scrollInfo.nMax - scrollInfo.nMin + 1 - scrollInfo.nPage;
        }
        if (value < scrollInfo.nMin)
        {
            value = scrollInfo.nMin;
        }
        if (scrollInfo.nPos != value)
        {
            NativeMethods.SCROLLINFO sCROLLINFO = new NativeMethods.SCROLLINFO();
            sCROLLINFO.fMask = NativeMethods.SIF.SIF_POS;
            sCROLLINFO.nPos = value;
            SetScrollInfo(scrollbar, sCROLLINFO, refresh: true);
            OnScroll(new ScrollEventArgs(ScrollEventType.ThumbPosition, scrollInfo.nPos, value, scrollbar));
        }
    }

    protected virtual void UpdateHorizontalScroll()
    {
        NativeMethods.SCROLLINFO scrollInfo = GetScrollInfo(ScrollOrientation.HorizontalScroll);
        HorizontalScroll.Enabled = base.Enabled;
        HorizontalScroll.LargeChange = scrollInfo.nPage;
        HorizontalScroll.Maximum = scrollInfo.nMax;
        HorizontalScroll.Minimum = scrollInfo.nMin;
        HorizontalScroll.SmallChange = StepSize.Width;
        HorizontalScroll.Value = scrollInfo.nPos;
        HorizontalScroll.Visible = true;
    }

    protected virtual void UpdateHorizontalScrollbar()
    {
        int num = ScrollSize.Width - 1;
        int nPage = PageSize.Width;
        if (num < 1)
        {
            num = 0;
            nPage = 1;
        }
        NativeMethods.SCROLLINFO sCROLLINFO = new NativeMethods.SCROLLINFO();
        sCROLLINFO.fMask = NativeMethods.SIF.SIF_RANGE | NativeMethods.SIF.SIF_PAGE;
        if (AlwaysShowHScroll || !base.Enabled)
        {
            sCROLLINFO.fMask |= NativeMethods.SIF.SIF_DISABLENOSCROLL;
        }
        sCROLLINFO.nMin = 0;
        sCROLLINFO.nMax = num;
        sCROLLINFO.nPage = nPage;
        SetScrollInfo(ScrollOrientation.HorizontalScroll, sCROLLINFO, refresh: true);
    }

    protected void UpdateScrollbars()
    {
        UpdateHorizontalScrollbar();
        UpdateVerticalScrollbar();
    }

    protected virtual void UpdateVerticalScroll()
    {
        NativeMethods.SCROLLINFO scrollInfo = GetScrollInfo(ScrollOrientation.VerticalScroll);
        VerticalScroll.Enabled = base.Enabled;
        VerticalScroll.LargeChange = scrollInfo.nPage;
        VerticalScroll.Maximum = scrollInfo.nMax;
        VerticalScroll.Minimum = scrollInfo.nMin;
        VerticalScroll.SmallChange = StepSize.Height;
        VerticalScroll.Value = scrollInfo.nPos;
        VerticalScroll.Visible = true;
    }

    protected virtual void UpdateVerticalScrollbar()
    {
        int num = ScrollSize.Height - 1;
        int nPage = PageSize.Height;
        if (num < 1)
        {
            num = 0;
            nPage = 1;
        }
        NativeMethods.SCROLLINFO sCROLLINFO = new NativeMethods.SCROLLINFO();
        sCROLLINFO.fMask = NativeMethods.SIF.SIF_RANGE | NativeMethods.SIF.SIF_PAGE;
        if (AlwaysShowVScroll)
        {
            sCROLLINFO.fMask |= NativeMethods.SIF.SIF_DISABLENOSCROLL;
        }
        sCROLLINFO.nMin = 0;
        sCROLLINFO.nMax = num;
        sCROLLINFO.nPage = nPage;
        SetScrollInfo(ScrollOrientation.VerticalScroll, sCROLLINFO, refresh: true);
    }

    protected virtual void WmScroll(ref Message msg)
    {
        ScrollEventType eventType = GetEventType(msg.WParam);
        ScrollOrientation scrollOrientation = ((msg.Msg != 276) ? ScrollOrientation.VerticalScroll : ScrollOrientation.HorizontalScroll);
        int num2;
        int newValue;
        if (eventType != ScrollEventType.EndScroll)
        {
            int num = ((scrollOrientation == ScrollOrientation.HorizontalScroll) ? StepSize.Width : StepSize.Height);
            NativeMethods.SCROLLINFO scrollInfo = GetScrollInfo(scrollOrientation);
            scrollInfo.fMask = NativeMethods.SIF.SIF_POS;
            num2 = scrollInfo.nPos;
            switch (eventType)
            {
                case ScrollEventType.ThumbPosition:
                case ScrollEventType.ThumbTrack:
                    scrollInfo.nPos = scrollInfo.nTrackPos;
                    break;
                case ScrollEventType.SmallDecrement:
                    scrollInfo.nPos = num2 - num;
                    break;
                case ScrollEventType.SmallIncrement:
                    scrollInfo.nPos = num2 + num;
                    break;
                case ScrollEventType.LargeDecrement:
                    scrollInfo.nPos = num2 - scrollInfo.nPage;
                    break;
                case ScrollEventType.LargeIncrement:
                    scrollInfo.nPos = num2 + scrollInfo.nPage;
                    break;
                case ScrollEventType.First:
                    scrollInfo.nPos = scrollInfo.nMin;
                    break;
                case ScrollEventType.Last:
                    scrollInfo.nPos = scrollInfo.nMax;
                    break;
            }
            if (scrollInfo.nPos > scrollInfo.nMax - scrollInfo.nMin + 1 - scrollInfo.nPage)
            {
                scrollInfo.nPos = scrollInfo.nMax - scrollInfo.nMin + 1 - scrollInfo.nPage;
            }
            if (scrollInfo.nPos < scrollInfo.nMin)
            {
                scrollInfo.nPos = scrollInfo.nMin;
            }
            newValue = scrollInfo.nPos;
            SetScrollInfo(scrollOrientation, scrollInfo, refresh: true);
        }
        else
        {
            num2 = 0;
            newValue = 0;
        }
        OnScroll(new ScrollEventArgs(eventType, num2, newValue, scrollOrientation));
    }

    private NativeMethods.SCROLLINFO GetScrollInfo(ScrollOrientation scrollbar)
    {
        NativeMethods.SCROLLINFO sCROLLINFO = new NativeMethods.SCROLLINFO();
        sCROLLINFO.fMask = NativeMethods.SIF.SIF_ALL;
        NativeMethods.GetScrollInfo(base.Handle, (int)scrollbar, sCROLLINFO);
        return sCROLLINFO;
    }

    private int SetScrollInfo(ScrollOrientation scrollbar, NativeMethods.SCROLLINFO scrollInfo, bool refresh)
    {
        return NativeMethods.SetScrollInfo(base.Handle, (int)scrollbar, scrollInfo, refresh);
    }
}
