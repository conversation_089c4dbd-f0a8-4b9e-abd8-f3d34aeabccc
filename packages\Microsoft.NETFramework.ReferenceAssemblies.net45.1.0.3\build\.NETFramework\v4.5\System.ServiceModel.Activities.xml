﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ServiceModel.Activities</name>
  </assembly>
  <members>
    <member name="T:System.ServiceModel.CorrelationActionMessageFilter">
      <summary>Provides a XAML-friendly implementation of a <see cref="T:System.ServiceModel.Dispatcher.MessageFilter" /> that filters on a single action.</summary>
    </member>
    <member name="M:System.ServiceModel.CorrelationActionMessageFilter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" /> class. </summary>
    </member>
    <member name="P:System.ServiceModel.CorrelationActionMessageFilter.Action">
      <summary>Gets or sets the action for the <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" />.</summary>
      <returns>The action for the <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" />.</returns>
    </member>
    <member name="M:System.ServiceModel.CorrelationActionMessageFilter.Equals(System.Object)">
      <summary>Determines whether the current <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" /> is equal to the specified <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" />.</summary>
      <returns>true if the specified <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" /> is equal to the current <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" />; otherwise, false.</returns>
      <param name="other">The <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" /> to compare with the current <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" />.</param>
    </member>
    <member name="M:System.ServiceModel.CorrelationActionMessageFilter.GetHashCode">
      <summary>Returns the hash code for the <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" /> instance.</summary>
      <returns>The hash code for the <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" /> instance.</returns>
    </member>
    <member name="M:System.ServiceModel.CorrelationActionMessageFilter.Match(System.ServiceModel.Channels.Message)">
      <summary>Determines whether the specified message’s action matches the <see cref="P:System.ServiceModel.CorrelationActionMessageFilter.Action" /> of this <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" />.</summary>
      <returns>true if the action matches; otherwise, false.</returns>
      <param name="message">The message to compare.</param>
    </member>
    <member name="M:System.ServiceModel.CorrelationActionMessageFilter.Match(System.ServiceModel.Channels.MessageBuffer)">
      <summary>Determines whether the action of the message contained by the specified message buffer matches the <see cref="P:System.ServiceModel.CorrelationActionMessageFilter.Action" /> of this <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" />.</summary>
      <returns>true if the action matches; otherwise, false.</returns>
      <param name="messageBuffer">The message buffer that contains the message to compare.</param>
    </member>
    <member name="M:System.ServiceModel.CorrelationActionMessageFilter.ToString">
      <summary>Returns a <see cref="T:System.String" /> representation of the <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" />.</summary>
      <returns>The current <see cref="T:System.ServiceModel.CorrelationActionMessageFilter" />.</returns>
    </member>
    <member name="T:System.ServiceModel.CorrelationQuery">
      <summary>Represents a <see cref="P:System.ServiceModel.CorrelationQuery.Where" /> clause and a <see cref="P:System.ServiceModel.CorrelationQuery.Select" /> clause that identify the unique characteristics of a message so that it can be routed to the correct instance.</summary>
    </member>
    <member name="M:System.ServiceModel.CorrelationQuery.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.CorrelationQuery" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.CorrelationQuery.Equals(System.Object)">
      <summary>Determines whether the current <see cref="T:System.ServiceModel.CorrelationQuery" /> is equal to the specified object.</summary>
      <returns>true if the current <see cref="T:System.ServiceModel.CorrelationQuery" /> is equal to the specified object; otherwise, false.</returns>
      <param name="other">The object to compare.</param>
    </member>
    <member name="M:System.ServiceModel.CorrelationQuery.GetHashCode">
      <summary>Returns the hash code for the current <see cref="T:System.ServiceModel.CorrelationQuery" />.</summary>
      <returns>The hash code for the current <see cref="T:System.ServiceModel.CorrelationQuery" />.</returns>
    </member>
    <member name="P:System.ServiceModel.CorrelationQuery.Select">
      <summary>Gets or sets the <see cref="T:System.ServiceModel.MessageQuerySet" /> that contains the elements in the message that compose the instance key that uniquely identifies the message.</summary>
      <returns>The <see cref="T:System.ServiceModel.MessageQuerySet" /> that contains the elements in the message that compose the instance key that uniquely identifies the message.</returns>
    </member>
    <member name="P:System.ServiceModel.CorrelationQuery.SelectAdditional">
      <summary>Gets a collection of clauses, each of which defines the set of elements in the message that make up an associated key.</summary>
      <returns>A collection of clauses, each of which defines the set of elements in the message that make up an associated key.</returns>
    </member>
    <member name="P:System.ServiceModel.CorrelationQuery.Where">
      <summary>Gets or sets the <see cref="T:System.ServiceModel.Dispatcher.MessageFilter" /> that defines the set of messages for which this query applies.</summary>
      <returns>The message filter that defines the set of messages for which this query applies.</returns>
    </member>
    <member name="T:System.ServiceModel.Endpoint">
      <summary>Represents an endpoint declared in XAML.</summary>
    </member>
    <member name="M:System.ServiceModel.Endpoint.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Endpoint" /> class. </summary>
    </member>
    <member name="P:System.ServiceModel.Endpoint.AddressUri">
      <summary>Gets or sets the endpoint’s URI.</summary>
      <returns>The <see cref="T:System.Uri" /> for the endpoint..</returns>
    </member>
    <member name="P:System.ServiceModel.Endpoint.BehaviorConfigurationName">
      <summary>Gets or sets the behavior configuration name.</summary>
      <returns>The behavior configuration name.</returns>
    </member>
    <member name="P:System.ServiceModel.Endpoint.Binding">
      <summary>Gets or sets the binding for the endpoint.</summary>
      <returns>The <see cref="T:System.ServiceModel.Binding" /> for the endpoint.</returns>
    </member>
    <member name="M:System.ServiceModel.Endpoint.GetAddress">
      <summary>Gets the address of the endpoint.</summary>
      <returns>The <see cref="T:System.ServiceModel.EndpointAddress" /> of the endpoint.</returns>
    </member>
    <member name="M:System.ServiceModel.Endpoint.GetAddress(System.ServiceModel.ServiceHostBase)">
      <summary>Gets the address of the endpoint using the specified <see cref="T:System.ServiceModel.ServiceHostBase" /> instance.</summary>
      <returns>The <see cref="T:System.ServiceModel.EndpointAddress" /> of the endpoint.</returns>
      <param name="host">The <see cref="T:System.ServiceModel.ServiceHostBase" /> instance to use.</param>
    </member>
    <member name="P:System.ServiceModel.Endpoint.Headers">
      <summary>Gets the address headers collection for the endpoint.</summary>
      <returns>A collection of <see cref="T:System.ServiceModel.Channels.AddressHeader" /> derived instances. </returns>
    </member>
    <member name="P:System.ServiceModel.Endpoint.Identity">
      <summary>Gets or sets the endpoint identity for the endpoint.</summary>
      <returns>The <see cref="T:System.ServiceModel.EndpointIdentity" /> for the endpoint.</returns>
    </member>
    <member name="P:System.ServiceModel.Endpoint.ListenUri">
      <summary>Gets or sets the listen URI for the endpoint.</summary>
      <returns>A <see cref="T:System.Uri" /> containing the listen URI for the endpoint.</returns>
    </member>
    <member name="P:System.ServiceModel.Endpoint.Name">
      <summary>Gets or sets the endpoint name.</summary>
      <returns>A <see cref="T:System.String" /> containing the endpoint’s name.</returns>
    </member>
    <member name="P:System.ServiceModel.Endpoint.ServiceContractName">
      <summary>Gets or sets the service contract name.</summary>
      <returns>The service contract name.</returns>
    </member>
    <member name="T:System.ServiceModel.EndpointIdentityExtension">
      <summary>A markup extension for the <see cref="T:System.ServiceModel.EndpointIdentity" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.EndpointIdentityExtension.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.EndpointIdentityExtension" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.EndpointIdentityExtension.#ctor(System.ServiceModel.EndpointIdentity)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.EndpointIdentityExtension" /> class with the specified <see cref="T:System.ServiceModel.EndpointIdentity" />.</summary>
      <param name="identity">The endpoint identity.</param>
    </member>
    <member name="P:System.ServiceModel.EndpointIdentityExtension.ClaimResource">
      <summary>Gets or sets the claim resource.</summary>
      <returns>An <see cref="T:System.Object" /> containing the claim resource.</returns>
    </member>
    <member name="P:System.ServiceModel.EndpointIdentityExtension.ClaimRight">
      <summary>Gets or sets the claim right.</summary>
      <returns>A <see cref="T:System.String" /> containing the claim right.</returns>
    </member>
    <member name="P:System.ServiceModel.EndpointIdentityExtension.ClaimType">
      <summary>Gets or sets the claim type.</summary>
      <returns>A <see cref="T:System.String" /> containing the claim type.</returns>
    </member>
    <member name="M:System.ServiceModel.EndpointIdentityExtension.ProvideValue(System.IServiceProvider)">
      <summary>Provides an <see cref="T:System.ServiceModel.EndpointIdentity" /> for the specified <see cref="T:System.IdentityModel.Claims.Claim" />.</summary>
      <returns>An <see cref="T:System.Object" /> containing the value.</returns>
      <param name="serviceProvider">This parameter is currently ignored.</param>
    </member>
    <member name="T:System.ServiceModel.MessageQuerySet">
      <summary>A set of <see cref="T:System.ServiceModel.MessageQuery" /> objects and an associated string parameter.  A correlation hash (<see cref="T:System.ServiceModel.InstanceKey" />) is computed from the results of the <see cref="T:System.ServiceModel.MessageQuery" /> objects as well as the associated strings.</summary>
    </member>
    <member name="M:System.ServiceModel.MessageQuerySet.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.MessageQuerySet" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.MessageQuerySet.#ctor(System.ServiceModel.Dispatcher.MessageQueryTable{System.String})">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.MessageQuerySet" /> class with the specified <see cref="T:System.ServiceModel.Dispatcher.MessageQueryTable`1" />.</summary>
      <param name="queryTable">The message query table.</param>
    </member>
    <member name="M:System.ServiceModel.MessageQuerySet.GetMessageQueryTable">
      <summary>Gets the message query table associated with the current <see cref="T:System.ServiceModel.MessageQuerySet" /> instance.</summary>
      <returns>The message query table.</returns>
    </member>
    <member name="P:System.ServiceModel.MessageQuerySet.Name">
      <summary>Gets or sets the name of the <see cref="T:System.ServiceModel.MessageQuerySet" /> instance.</summary>
      <returns>The name of the message query set.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.CallbackCorrelationInitializer">
      <summary>Initializes the associated <see cref="P:System.ServiceModel.Activities.CorrelationInitializer.CorrelationHandle" /> with a callback correlation.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.CallbackCorrelationInitializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.CallbackCorrelationInitializer" /> class.</summary>
    </member>
    <member name="T:System.ServiceModel.Activities.ChannelCacheSettings">
      <summary>Represents settings that define operational characteristics of a channel cache.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.ChannelCacheSettings.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.ChannelCacheSettings" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.ChannelCacheSettings.IdleTimeout">
      <summary>Gets the maximum interval of time for which the object can remain idle in the cache before being disposed.</summary>
      <returns>An interval of time.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.ChannelCacheSettings.LeaseTimeout">
      <summary>Gets the interval of time after which an object is removed from the cache.</summary>
      <returns>An interval of time.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.ChannelCacheSettings.MaxItemsInCache">
      <summary>Gets the maximum number of objects that can be in the cache.</summary>
      <returns>A maximum number of objects.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.ContextCorrelationInitializer">
      <summary>Initializes the associated <see cref="P:System.ServiceModel.Activities.CorrelationInitializer.CorrelationHandle" /> with a.NET Context Exchange-based correlation, subject to the <see cref="T:System.ServiceModel.Channels.ContextExchangeMechanism" /> specified.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.ContextCorrelationInitializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.ContextCorrelationInitializer" /> class.</summary>
    </member>
    <member name="T:System.ServiceModel.Activities.CorrelationHandle">
      <summary>Associates activities together in a correlation by representing a particular shared <see cref="T:System.Runtime.DurableInstancing.InstanceKey" /> or transient context in the workflow. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.CorrelationHandle.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.CorrelationHandle" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.CorrelationHandle.OnInitialize(System.Activities.HandleInitializationContext)">
      <summary>Registers the <see cref="T:System.ServiceModel.Activities.CorrelationHandle" /> with the workflow runtime.</summary>
      <param name="context">The initialization environment.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.CorrelationHandle.OnUninitialize(System.Activities.HandleInitializationContext)">
      <summary>Unregisters the <see cref="T:System.ServiceModel.Activities.CorrelationHandle" /> with the workflow runtime.</summary>
      <param name="context">The initialization environment of the <see cref="T:System.ServiceModel.Activities.CorrelationHandle" />.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.CorrelationInitializer">
      <summary>An abstract base class for correlation initializers that are used to tell the runtime what protocol is being used for the correlation.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.CorrelationInitializer.CorrelationHandle">
      <summary>Gets or sets the <see cref="T:System.ServiceModel.Activities.CorrelationHandle" /> argument for the correlation initializer.</summary>
      <returns>The correlation handle argument.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.CorrelationScope">
      <summary>Provides implicit <see cref="T:System.ServiceModel.Activities.CorrelationHandle" /> management for child messaging activities.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.CorrelationScope.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.CorrelationScope" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.CorrelationScope.Body">
      <summary>Gets or sets the activity’s execution logic.</summary>
      <returns>The activity’s execution logic.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.CorrelationScope.CacheMetadata(System.Activities.NativeActivityMetadata)">
      <summary>Builds and validates a description of the activity’s arguments, variables, child activities, and activity delegates.</summary>
      <param name="metadata">The activity’s metadata that encapsulates the activity’s arguments, variables, child activities, and activity delegates.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.CorrelationScope.CorrelatesWith">
      <summary>Gets or sets the <see cref="T:System.ServiceModel.Activities.CorrelationHandle" /> used by the child messaging activities.</summary>
      <returns>The correlation handle used by the child messaging activities.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.CorrelationScope.Execute(System.Activities.NativeActivityContext)">
      <summary>Called by the workflow runtime to execute the <see cref="T:System.ServiceModel.Activities.CorrelationScope" /> activity.</summary>
      <param name="context">The current native activity execution context.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.CorrelationScope.ShouldSerializeCorrelatesWith">
      <summary>Indicates whether the <see cref="P:System.ServiceModel.Activities.CorrelationScope.CorrelatesWith" /> property should be serialized. </summary>
      <returns>true if the <see cref="P:System.ServiceModel.Activities.CorrelationScope.CorrelatesWith" /> property value should be serialized; otherwise, false.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.DurableInstancingOptions">
      <summary>Used mainly to associate a concrete implementation of the <see cref="T:System.Runtime.Persistence.InstanceStore" /> such as <see cref="T:System.Activities.DurableInstancing.SqlWorkflowInstanceStore" /> with a workflow service host. This class is also used to add instance owner metadata.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.DurableInstancingOptions.AddInitialInstanceValues(System.Collections.Generic.IDictionary{System.Xml.Linq.XName,System.Object})">
      <summary>Adds write-only metadata for an instance to the host.</summary>
      <param name="writeOnlyValues">The write-only values. These values are optional when loading an instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.DurableInstancingOptions.AddInstanceOwnerValues(System.Collections.Generic.IDictionary{System.Xml.Linq.XName,System.Object},System.Collections.Generic.IDictionary{System.Xml.Linq.XName,System.Object})">
      <summary>Adds metadata for an instance owner to the host. This method may   be called before the workflow host is opened.</summary>
      <param name="readWriteValues">The read-write metadata.</param>
      <param name="writeOnlyValues">The write-only values. These values are optional when loading an instance.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.DurableInstancingOptions.InstanceStore">
      <summary>Refers to a concrete implementation of the <see cref="T:System.Runtime.Persistence.InstanceStore" /> class such as <see cref="T:System.Activities.DurableInstancing.SqlWorkflowInstanceStore" />.</summary>
      <returns>A concrete implementation of the <see cref="T:System.Runtime.Persistence.InstanceStore" /> class.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.HostSettings">
      <summary>Represents settings that define operational characteristics of a host.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.HostSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.HostSettings" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.HostSettings.IncludeExceptionDetailInFaults">
      <summary>Gets or sets whether exception details are included in faults.</summary>
      <returns>true if exception details are included in faults; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.HostSettings.ScopeName">
      <summary>Gets or sets the name of scope.</summary>
      <returns>The name of scope.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.HostSettings.UseNoPersistHandle">
      <summary>Gets or sets a value that indicates whether to use non-persistent handle.</summary>
      <returns>true if non-persistent handle is used; otherwise, false.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.InitializeCorrelation">
      <summary>Initializes correlation without sending or receiving a message.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.InitializeCorrelation.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.InitializeCorrelation" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.InitializeCorrelation.Correlation">
      <summary>Gets or sets the <see cref="T:System.ServiceModel.Activities.CorrelationHandle" /> that references the correlation.</summary>
      <returns>The correlation handle.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.InitializeCorrelation.CorrelationData">
      <summary>Gets or sets a dictionary of correlation data that relates messages to this workflow instance.</summary>
      <returns>A dictionary of correlation data.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.IReceiveMessageCallback">
      <summary>Implements a callback to be executed when a service message is received by the <see cref="T:System.ServiceModel.Activities.Receive" /> and <see cref="T:System.ServiceModel.Activities.ReceiveParameters" /> activities.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.IReceiveMessageCallback.OnReceiveMessage(System.ServiceModel.OperationContext,System.Activities.ExecutionProperties)">
      <summary>Executed when a service message is received.</summary>
      <param name="operationContext">The operation context under which the message received.</param>
      <param name="activityExecutionProperties">The set of execution properties available within the workflow. </param>
    </member>
    <member name="T:System.ServiceModel.Activities.ISendMessageCallback">
      <summary>An interface that implements a callback that is called just before a message is sent on the wire by the <see cref="T:System.ServiceModel.Activities.Send" /> or <see cref="T:System.ServiceModel.Activities.SendParameters" /> activities. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.ISendMessageCallback.OnSendMessage(System.ServiceModel.OperationContext)">
      <summary>Executed when a service message is sent.</summary>
      <param name="operationContext">The operation’s context under which the message is being sent.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.IWorkflowInstanceManagement">
      <summary>Represents a service contract that defines a set of operations that allow you to control workflow instances.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.Abandon(System.Guid,System.String)">
      <summary>Attempts to abandon the specified workflow instance.</summary>
      <param name="instanceId">The GUID identifier of the workflow instance to abandon.</param>
      <param name="reason">The reason for abandoning the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginAbandon(System.Guid,System.String,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation to abandon the specified workflow instance.</summary>
      <returns>The <see cref="T:System.IAsyncResult" />.</returns>
      <param name="instanceId">The GUID identifier of the workflow instance to abandon.</param>
      <param name="reason">The reason for abandoning the workflow instance.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">An object, specified by the application, that contains state information associated with the asynchronous abandon operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginCancel(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation to cancel the specified workflow instance.</summary>
      <returns>An <see cref="T:System.IasyncResult" />.</returns>
      <param name="instanceId">The GUID identifier of the workflow instance to cancel.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">An object, specified by the application, that contains state information associated with the asynchronous cancel operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginRun(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation to run the specified workflow instance.</summary>
      <returns>An <see cref="T:System.IAsyncResult" />.</returns>
      <param name="instanceId">The GUID identifier of the workflow instance to run.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">An object, specified by the application, that contains state information associated with the asynchronous run operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginSuspend(System.Guid,System.String,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation to suspend the specified workflow instance.</summary>
      <returns>The <see cref="T:System.IAsyncResult" />.</returns>
      <param name="instanceId">The GUID identifier of the workflow instance to suspend.</param>
      <param name="reason">The reason to suspend the workflow instance.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">An object, specified by the application, that contains state information associated with the asynchronous suspend operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginTerminate(System.Guid,System.String,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation to terminate the specified workflow instance.</summary>
      <returns>The <see cref="T:System.IAsyncResult" />.</returns>
      <param name="instanceId">The GUID identifier of the workflow instance to terminate.</param>
      <param name="reason">The reason to terminate the workflow instance.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">An object, specified by the application, that contains state information associated with the asynchronous terminate operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginTransactedCancel(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous cancel operation in a transaction.</summary>
      <returns>The <see cref="T:System.IAsyncResult" />.</returns>
      <param name="instanceId">The GUID identifier of the workflow instance to cancel.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">An object, specified by the application, that contains state information associated with the asynchronous cancel operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginTransactedRun(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that runs a workflow instance within a transaction.</summary>
      <returns>The <see cref="T:System.IAsyncResult" />.</returns>
      <param name="instanceId">The GUID identifier of the workflow instance to run.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">An object, specified by the application, that contains state information associated with the asynchronous run operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginTransactedSuspend(System.Guid,System.String,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that suspends the specified workflow instance.</summary>
      <returns>The <see cref="T:System.IAsyncResult" />.</returns>
      <param name="instanceId">The GUID identifier of the workflow instance to suspend.</param>
      <param name="reason">The reason to suspend the workflow instance.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">An object, specified by the application, that contains state information associated with the asynchronous suspend operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginTransactedTerminate(System.Guid,System.String,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that terminates a workflow instance within a transaction.</summary>
      <returns>The <see cref="T:System.IAsyncResult" />.</returns>
      <param name="instanceId">The GUID identifier of the workflow instance to terminate.</param>
      <param name="reason">The reason to terminate the workflow instance.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">An object, specified by the application, that contains state information associated with the asynchronous terminate operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginTransactedUnsuspend(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that resumes the specified workflow instance within a transaction.</summary>
      <returns>The <see cref="T:System.IAsyncResult" />.</returns>
      <param name="instanceId">The GUID identifier of the workflow instance to unsuspend.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">An object, specified by the application, that contains state information associated with the asynchronous unsuspend operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginUnsuspend(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that resumes the specified workflow instance.</summary>
      <returns>The <see cref="T:System.IAsyncResult" />.</returns>
      <param name="instanceId">The GUID identifier of the workflow instance to resume.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">An object, specified by the application, that contains state information associated with the asynchronous unsuspend operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.Cancel(System.Guid)">
      <summary>Cancels the specified workflow instance.</summary>
      <param name="instanceId">The GUID identifier of the workflow instance to cancel.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.EndAbandon(System.IAsyncResult)">
      <summary>Completes an asynchronous abandon operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> returned by a call to the <see cref="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginAbandon(System.Guid,System.String,System.AsyncCallback,System.Object)" /> method.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.EndCancel(System.IAsyncResult)">
      <summary>Completes an asynchronous cancel operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> returned by a call to the <see cref="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginCancel(System.Guid,System.AsyncCallback,System.Object)" /> method.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.EndRun(System.IAsyncResult)">
      <summary>Completes an asynchronous run operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> returned by a call to the <see cref="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginRun(System.Guid,System.AsyncCallback,System.Object)" /> method.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.EndSuspend(System.IAsyncResult)">
      <summary>Completes an asynchronous suspend operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> returned by a call to the <see cref="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginSuspend(System.Guid,System.String,System.AsyncCallback,System.Object)" /> operation</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.EndTerminate(System.IAsyncResult)">
      <summary>Completes an asynchronous terminate operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> returned by a call to the <see cref="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginTerminate(System.Guid,System.String,System.AsyncCallback,System.Object)" /> method.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.EndTransactedCancel(System.IAsyncResult)">
      <summary>Completes an asynchronous transacted cancel operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> returned by a call to the <see cref="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginTransactedCancel(System.Guid,System.AsyncCallback,System.Object)" /> method.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.EndTransactedRun(System.IAsyncResult)">
      <summary>Completes an asynchronous transacted run operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> returned by a call to the <see cref="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginTransactedRun(System.Guid,System.AsyncCallback,System.Object)" /> method.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.EndTransactedSuspend(System.IAsyncResult)">
      <summary>Completes an asynchronous transacted suspend operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> returned by a call to the <see cref="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginTransactedSuspend(System.Guid,System.String,System.AsyncCallback,System.Object)" /> method. </param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.EndTransactedTerminate(System.IAsyncResult)">
      <summary>Completes an asynchronous transacted terminate operation. </summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> returned by a call to the <see cref="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginTransactedTerminate(System.Guid,System.String,System.AsyncCallback,System.Object)" /> method.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.EndTransactedUnsuspend(System.IAsyncResult)">
      <summary>Completes an asynchronous transacted unsuspend operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> returned by a call to the <see cref="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginTransactedUnsuspend(System.Guid,System.AsyncCallback,System.Object)" /> method.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.EndUnsuspend(System.IAsyncResult)">
      <summary>Completes an asynchronous unsuspend operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> returned by a call to the <see cref="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.BeginUnsuspend(System.Guid,System.AsyncCallback,System.Object)" /> method. </param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.Run(System.Guid)">
      <summary>Starts executing the specified workflow instance.</summary>
      <param name="instanceId">The GUID identifier of the workflow instance to run.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.Suspend(System.Guid,System.String)">
      <summary>Suspends the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to suspend.</param>
      <param name="reason">The reason for suspending the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.Terminate(System.Guid,System.String)">
      <summary>Terminates the specified workflow instance.</summary>
      <param name="instanceId">The GUID identifier of the workflow instance to terminate.</param>
      <param name="reason">The reason to terminate the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.TransactedCancel(System.Guid)">
      <summary>Cancels the specified workflow instance within a transaction.</summary>
      <param name="instanceId">The GUID identifier of the workflow instance to cancel.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.TransactedRun(System.Guid)">
      <summary>Runs the specified workflow instance within a transaction.</summary>
      <param name="instanceId">The GUID identifier of the workflow instance to run.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.TransactedSuspend(System.Guid,System.String)">
      <summary>Suspends the specified workflow instance within a transaction.</summary>
      <param name="instanceId">The GUID identifier of the workflow instance to suspend.</param>
      <param name="reason">The reason the workflow instance is suspended.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.TransactedTerminate(System.Guid,System.String)">
      <summary>Terminates the specified workflow instance within a transaction.</summary>
      <param name="instanceId">The GUID identifier of the workflow instance to terminate.</param>
      <param name="reason">The reason to terminate the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.TransactedUnsuspend(System.Guid)">
      <summary>Resumes the specified workflow instance within a transaction.</summary>
      <param name="instanceId">The GUID identifier of the workflow instance to resume.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowInstanceManagement.Unsuspend(System.Guid)">
      <summary>Resumes the specified workflow instance.</summary>
      <param name="instanceId">The GUID identifier of the workflow instance to resume.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.IWorkflowUpdateableInstanceManagement">
      <summary>Represents a service contract that defines a set of operations that allow you to update workflow instances.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowUpdateableInstanceManagement.BeginTransactedUpdate(System.Guid,System.Activities.WorkflowIdentity,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that updates a workflow instance within a transaction.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</returns>
      <param name="instanceId">The GUID identifier of the workflow instance to terminate.</param>
      <param name="updatedDefinitionIdentity">The updated workflow identity.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">An object, specified by the application, that contains state information associated with the asynchronous update operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowUpdateableInstanceManagement.BeginUpdate(System.Guid,System.Activities.WorkflowIdentity,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation to update the specified workflow instance.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</returns>
      <param name="instanceId">The GUID identifier of the workflow instance to terminate.</param>
      <param name="updatedDefinitionIdentity">The updated workflow identity.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">An object, specified by the application, that contains state information associated with the asynchronous update operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowUpdateableInstanceManagement.EndTransactedUpdate(System.IAsyncResult)">
      <summary>Completes an asynchronous transacted update operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowUpdateableInstanceManagement.EndUpdate(System.IAsyncResult)">
      <summary>Completes an asynchronous update operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowUpdateableInstanceManagement.TransactedUpdate(System.Guid,System.Activities.WorkflowIdentity)">
      <summary>Updates the specified workflow instance within a transaction.</summary>
      <param name="instanceId">The GUID identifier of the workflow instance to terminate.</param>
      <param name="updatedDefinitionIdentity">The updated workflow identity.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.IWorkflowUpdateableInstanceManagement.Update(System.Guid,System.Activities.WorkflowIdentity)">
      <summary>Updates the specified workflow instance.</summary>
      <param name="instanceId">The GUID identifier of the workflow instance to terminate.</param>
      <param name="updatedDefinitionIdentity">The updated workflow identity.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.MessageContext">
      <summary>Represents a message context.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.MessageContext.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.MessageContext" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.MessageContext.EndToEndTracingId">
      <summary>Gets or sets the End-to-End tracing GUID of the message context.</summary>
      <returns>The End-to-End tracing GUID of the message context.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.MessageContext.Message">
      <summary>Gets or sets the message associated with the message context.</summary>
      <returns>The message associated with the message context.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.QueryCorrelationInitializer">
      <summary>Initializes the associated <see cref="P:System.ServiceModel.Activities.CorrelationInitializer.CorrelationHandle" /> based on the results of the <see cref="P:System.ServiceModel.Activities.QueryCorrelationInitializer.MessageQuerySet" /> when the message is sent or received.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.QueryCorrelationInitializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.QueryCorrelationInitializer" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.QueryCorrelationInitializer.MessageQuerySet">
      <summary>Gets or sets the <see cref="T:System.ServiceModel.MessageQuerySet" /> that is used to perform the <see cref="T:System.ServiceModel.Channels.CorrelationKey" /> calculation.</summary>
      <returns>The <see cref="T:System.ServiceModel.MessageQuerySet" /> that is used to perform the <see cref="T:System.ServiceModel.Channels.CorrelationKey" /> calculation.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Receive">
      <summary>An activity that receives a message.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Receive.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Receive" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Receive.Action">
      <summary>Gets or sets the value of the action header of the message.</summary>
      <returns>The action URI of the message.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Receive.CanCreateInstance">
      <summary>Gets or sets a value that indicates whether a new workflow instance is created to process the message if the message does not correlate to an existing workflow instance..</summary>
      <returns>true if a new workflow instance is created to process the message, otherwise false.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Receive.Content">
      <summary>Gets or sets the data to receive. The content can be either a <see cref="T:System.ServiceModel.Activities.ReceiveMessageContent" /> or a <see cref="T:System.ServiceModel.Activities.ReceiveParametersContent" />.</summary>
      <returns>The data to receive.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Receive.CorrelatesOn">
      <summary>Gets or sets the <see cref="T:System.ServiceModel.MessageQuerySet" /> used to query the message to extract correlation data.</summary>
      <returns>A set of message queries used to extract correlation data from the incoming message.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Receive.CorrelatesWith">
      <summary>Gets or sets a correlation handle that is used to route the message to the appropriate workflow instance.</summary>
      <returns>The correlation handle.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Receive.CorrelationInitializers">
      <summary>Gets a collection of correlation initializers that initialize query-based, context, callback context, or request-reply correlations with a <see cref="T:System.ServiceModel.Activities.Send" /> activity runs.</summary>
      <returns>A collection of correlation initializers.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Receive.FromOperationDescription(System.ServiceModel.Description.OperationDescription)">
      <summary>Returns the receive activity from the given contract operation description.</summary>
      <returns>A <see cref="T:System.ServiceModel.Activities.Receive" /> object from the given contract operation description.</returns>
      <param name="operation">The contract operation description.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.Receive.KnownTypes">
      <summary>Gets a collection of known types for the operation.</summary>
      <returns>A collection of known types.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Receive.OperationName">
      <summary>Gets or sets the name of the operation implemented by the <see cref="T:System.ServiceModel.Activities.Receive" />.</summary>
      <returns>The name of the operation.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Receive.ProtectionLevel">
      <summary>Gets or sets a value that indicates the protection level for the operation.</summary>
      <returns>The protection level for the operation.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Receive.SerializerOption">
      <summary>Gets or sets a value that specifies the serializer to use for this operation.</summary>
      <returns>The serializer to use.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Receive.ServiceContractName">
      <summary>Gets or sets the name of the service contract.</summary>
      <returns>The service contract name.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Receive.ShouldSerializeCorrelatesOn">
      <summary>Returns a value that indicates whether the <see cref="P:System.ServiceModel.Activities.Receive.CorrelatesOn" /> property has changed from its default value and should be serialized.</summary>
      <returns>true if the <see cref="P:System.ServiceModel.Activities.Receive.CorrelatesOn" /> property value should be serialized; otherwise, false.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.ReceiveContent">
      <summary>An abstract base class for classes that represent the data received by a workflow service.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.ReceiveContent.Create(System.Activities.OutArgument)">
      <summary>Creates a new <see cref="T:System.ServiceModel.Activities.ReceiveMessageContent" /> instance with the specified message.</summary>
      <returns>A <see cref="T:System.ServiceModel.Activities.ReceiveMessageContent" />instance.</returns>
      <param name="message">The message.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.ReceiveContent.Create(System.Activities.OutArgument,System.Type)">
      <summary>Creates a new <see cref="T:System.ServiceModel.Activities.ReceiveMessageContent" /> instance with the specified message and message type.</summary>
      <returns>A  <see cref="T:System.ServiceModel.Activities.ReceiveMessageContent" /> instance.</returns>
      <param name="message">The message.</param>
      <param name="declaredMessageType">The message type.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.ReceiveContent.Create(System.Collections.Generic.IDictionary{System.String,System.Activities.OutArgument})">
      <summary>Creates a new <see cref="T:System.ServiceModel.Activities.ReceiveMessageContent" /> instance with the specified parameters.</summary>
      <returns>A <see cref="T:System.ServiceModel.Activities.ReceiveParametersContent" /> instance.</returns>
      <param name="parameters">The parameters.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.ReceiveMessageContent">
      <summary>A class used to receive a <see cref="T:System.ServiceModel.Message" /> or a message contract type within a workflow service.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.ReceiveMessageContent.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.ReceiveMessageContent" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.ReceiveMessageContent.#ctor(System.Activities.OutArgument)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.ReceiveMessageContent" /> class with the specified message.</summary>
      <param name="message">The message.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.ReceiveMessageContent.#ctor(System.Activities.OutArgument,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.ReceiveMessageContent" /> class with the specified message and message type.</summary>
      <param name="message">The message.</param>
      <param name="declaredMessageType">The message type.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.ReceiveMessageContent.DeclaredMessageType">
      <summary>Gets or sets the declared message type.</summary>
      <returns>The declared message type.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.ReceiveMessageContent.Message">
      <summary>Gets or sets the message received.</summary>
      <returns>The received message.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.ReceiveMessageContent.ShouldSerializeDeclaredMessageType">
      <summary>Gets a boolean value that determines if the message should be serialized as the declared message type.</summary>
      <returns>true if the message should be serialized as the declared message type; otherwise false.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.ReceiveParametersContent">
      <summary>Enables a workflow service to receive data n the form of parameters.<see cref="T:System.ServiceModel.Activities.ReceiveParametersContent" /> is interoperable with non-workflow WCF clients and services. The <see cref="P:System.ServiceModel.Activities.ReceiveParametersContent.Parameters" /> collection is similar to the argument declaration on a C# method signature.The <see cref="P:System.ServiceModel.Activities.ReceiveParametersContent.Parameters" /> collection cannot contain arguments marked with <see cref="T:System.ServiceModel.MessageContractAttribute" />, or of type <see cref="System.ServiceModel.Channels.Message" />. Please use <see cref="T:System.ServiceModel.Activities.ReceiveMessageContent" /> for these types of data.This is a sealed class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.ReceiveParametersContent.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.ReceiveParametersContent" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.ReceiveParametersContent.#ctor(System.Collections.Generic.IDictionary{System.String,System.Activities.OutArgument})">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.ReceiveParametersContent" /> class with the specified parameters.</summary>
      <param name="parameters">A collection of key-value pairs with the keys containing the parameter names and the values containing the arguments.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.ReceiveParametersContent.Parameters">
      <summary>Modifies the parameters collection.</summary>
      <returns>An ordered collection of key-value pairs with the keys containing the parameter names and the values containing the arguments.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.ReceiveReply">
      <summary>An activity that receives a message as part of a request/reply message exchange pattern.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.ReceiveReply.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.ReceiveReply" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.ReceiveReply.Action">
      <summary>Gets or sets the value of the action header of the message.</summary>
      <returns>The action of the message.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.ReceiveReply.Content">
      <summary>Gets or sets the content received by the <see cref="T:System.ServiceModel.Activities.ReceiveReply" /> activity</summary>
      <returns>The content received.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.ReceiveReply.CorrelationInitializers">
      <summary>Gets a collection of correlation initializers.</summary>
      <returns>A collection of correlation initializers.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.ReceiveReply.Request">
      <summary>Gets or sets a reference to the <see cref="T:System.ServiceModel.Activities.Send" /> activity paired with this <see cref="T:System.ServiceModel.Activites.ReceiveReply" /> activity.</summary>
      <returns>The send activity.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.ReceiveSettings">
      <summary>Represents the settings for an activity that receives a message.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.ReceiveSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.ReceiveSettings" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.ReceiveSettings.Action">
      <summary>Gets or sets the value of the action header of the message.</summary>
      <returns>The action URI of the message.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.ReceiveSettings.CanCreateInstance">
      <summary>Gets or sets a value that indicates whether a new workflow instance is created to process the message if the message does not correlate to an existing workflow instance.</summary>
      <returns>true if a new workflow instance is created to process the message; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.ReceiveSettings.OwnerDisplayName">
      <summary>Gets or sets the display name of the owner.</summary>
      <returns>The display name of the owner.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.RequestReplyCorrelationInitializer">
      <summary>Initializes the associated <see cref="P:System.ServiceModel.Activities.CorrelationInitializer.CorrelationHandle" /> based on the <see cref="T:System.ServiceModel.Channels.RequestContext" /> that is created for the two-way operation.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.RequestReplyCorrelationInitializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.RequestReplyCorrelationInitializer" /> class.</summary>
    </member>
    <member name="T:System.ServiceModel.Activities.Send">
      <summary>An activity that sends a message to a service. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Send.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Send" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Send.Action">
      <summary>Gets or sets the value of the action header of the message being sent. </summary>
      <returns>The action of the message.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Send.Content">
      <summary>Gets or sets the content sent by the <see cref="T:System.ServiceModel.Activities.Send" /> activity.</summary>
      <returns>The content to send.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Send.CorrelatesWith">
      <summary>Gets or sets a correlation handle that is used to route the message to the appropriate workflow instance.</summary>
      <returns>The correlation handle.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Send.CorrelationInitializers">
      <summary>Gets a collection of correlation initializers.</summary>
      <returns>A collection of correlation initializers.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Send.Endpoint">
      <summary>Gets or sets the endpoint to send messages to.</summary>
      <returns>The endpoint.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Send.EndpointAddress">
      <summary>Gets or sets the address of the endpoint to send messages to.</summary>
      <returns>The endpoint address.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Send.EndpointConfigurationName">
      <summary>Gets or sets the name of the endpoint configuration.</summary>
      <returns>The endpoint configuration name.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Send.KnownTypes">
      <summary>Gets a collection of the known types for the service operation to be called.</summary>
      <returns>A collection of known types.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Send.OperationName">
      <summary>Gets or sets the name of the service operation to be called.</summary>
      <returns>The name of the service operation.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Send.ProtectionLevel">
      <summary>Gets or sets a value that indicates the protection level for the message.</summary>
      <returns>The protection level for the message.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Send.SerializerOption">
      <summary>Gets or sets a value that specifies the serializer to use when sending a message.</summary>
      <returns>The serializer to use for this operation.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Send.ServiceContractName">
      <summary>The name of the contract the service to be called implements.</summary>
      <returns>The name of the service contract.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Send.TokenImpersonationLevel">
      <summary>Gets or sets a value that indicates the token impersonation level allowed for the receiver of the message.</summary>
      <returns>The token impersonation level.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.SendContent">
      <summary>An abstract base class for classes that represent the data sent to a workflow service.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.SendContent.Create(System.Activities.InArgument)">
      <summary>Creates a new <see cref="T:System.ServiceModel.Activities.SendMessageContent" /> instance with the specified message.</summary>
      <returns>A <see cref="T:System.ServiceModel.Activities.SendMessageContent" /> instance.</returns>
      <param name="message">The data to send.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.SendContent.Create(System.Activities.InArgument,System.Type)">
      <summary>Creates a new <see cref="T:System.ServiceModel.Activities.SendMessageContent" /> instance with the specified message and message type.</summary>
      <returns>A <see cref="T:System.ServiceModel.Activities.SendMessageContent" /> instance.</returns>
      <param name="message">The data to send.</param>
      <param name="declaredMessageType">The data type that will appear in the service description and WSDL. It must be either the same as the argument type of the message or its base type.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.SendContent.Create(System.Collections.Generic.IDictionary{System.String,System.Activities.InArgument})">
      <summary>Creates a new <see cref="T:System.ServiceModel.Activities.SendParametersContent" /> instance with the specified parameters.</summary>
      <returns>A <see cref="T:System.ServiceModel.Activities.SendParametersContent" /> instance.</returns>
      <param name="parameters">A collection of name/value pairs with the keys containing the parameter names and the values containing the arguments.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.SendMessageChannelCache">
      <summary>Represents an extension that enables the customization of the cache sharing levels, the settings of the channel factory cache, and the settings of the channel cache for workflows that send messages to service endpoints using <see cref="T:System.ServiceModel.Activities.Send" /> messaging activities.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.SendMessageChannelCache.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.SendMessageChannelCache" /> class with default factory cache and channel cache settings.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.SendMessageChannelCache.#ctor(System.ServiceModel.Activities.ChannelCacheSettings,System.ServiceModel.Activities.ChannelCacheSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.SendMessageChannelCache" /> class with custom factory cache and channel cache settings.</summary>
      <param name="factorySettings">The cache settings for cached channel factories.</param>
      <param name="channelSettings">The cache settings for cached channels.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.SendMessageChannelCache.#ctor(System.ServiceModel.Activities.ChannelCacheSettings,System.ServiceModel.Activities.ChannelCacheSettings,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.SendMessageChannelCache" /> class with custom factory cache settings, custom channel cache settings, and a value that indicates whether to turn caching on.</summary>
      <param name="factorySettings">The cache settings for cached channel factories.</param>
      <param name="channelSettings">The cache settings for cached channels.</param>
      <param name="allowUnsafeCaching">true to turn caching on; otherwise, false.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.SendMessageChannelCache.AllowUnsafeCaching">
      <summary>Gets or sets a value that indicates whether to turn caching on.</summary>
      <returns>true to turn caching on; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.SendMessageChannelCache.ChannelSettings">
      <summary>Gets or sets the settings of the channel cache.</summary>
      <returns>The settings of the channel cache.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.SendMessageChannelCache.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.ServiceModel.Activities.SendMessageChannelCache" /> class. </summary>
    </member>
    <member name="P:System.ServiceModel.Activities.SendMessageChannelCache.FactorySettings">
      <summary>Gets or sets the settings of the channel factory cache.</summary>
      <returns>The settings of the channel factory cache.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.SendMessageContent">
      <summary>A class used to send a single item (message) to or from a workflow service. The item can be a built-in type such as primitive types, <see cref="T:System.ServiceModel.Channels.Message" />, or <see cref="T:System.Xml.Linq.XElement" />; or an application-defined data contract, message contract, or XML serializable type.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.SendMessageContent.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.SendMessageContent" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.SendMessageContent.#ctor(System.Activities.InArgument)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.SendMessageContent" /> class with the specified data to send.</summary>
      <param name="message">The data to send.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.SendMessageContent.#ctor(System.Activities.InArgument,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.SendMessageContent" /> class with the specified message and message type.</summary>
      <param name="message">The data to send.</param>
      <param name="declaredMessageType">The data type that appears on the service description. It must be the same type as <paramref name="message" /> or any base type of <paramref name="message" />.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.SendMessageContent.DeclaredMessageType">
      <summary>Gets or sets the declared message type.</summary>
      <returns>The declared message type.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.SendMessageContent.Message">
      <summary>Gets or sets the data to send.</summary>
      <returns>The data to send.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.SendMessageContent.ShouldSerializeDeclaredMessageType">
      <summary>Gets a value that indicates whether the <see cref="P:System.ServiceModel.Activities.SendMessageContent.DeclaredMessateType" /> property should be XAML-serialized.</summary>
      <returns>true if the property should be serialized; otherwise false.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.SendParametersContent">
      <summary>Enables sending data to or from a workflow service in the form of parameters.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.SendParametersContent.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.SendParametersContent" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.SendParametersContent.#ctor(System.Collections.Generic.IDictionary{System.String,System.Activities.InArgument})">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.SendParametersContent" /> class with the specified parameters.</summary>
      <param name="parameters">A collection of key-value pairs with the keys containing the parameter names and the values containing the arguments. </param>
    </member>
    <member name="P:System.ServiceModel.Activities.SendParametersContent.Parameters">
      <summary>Gets or sets the workflow service operation parameters.</summary>
      <returns>An ordered collection of key-value pairs with the keys containing the parameter names and the values containing the arguments. </returns>
    </member>
    <member name="T:System.ServiceModel.Activities.SendReceiveExtension">
      <summary>An abstract class that defines extension methods for the send/receive activities.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.SendReceiveExtension.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.SendReceiveExtension" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.SendReceiveExtension.Cancel(System.Activities.Bookmark)">
      <summary>Cancels the activity specified by the given bookmark.</summary>
      <param name="bookmark">A bookmark at which an activity instance is to be cancelled.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.SendReceiveExtension.HostSettings">
      <summary>Gets the host settings of the send/receive activities.</summary>
      <returns>The host settings of the send/receive activities.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.SendReceiveExtension.OnRegisterReceive(System.ServiceModel.Activities.ReceiveSettings,System.Runtime.DurableInstancing.InstanceKey,System.Activities.Bookmark)">
      <summary>Executes upon calling RegisterReceive method.</summary>
      <param name="settings">The settings for the receive activity.</param>
      <param name="correlatesWith">An instance key of the activity used to correlate with the message.</param>
      <param name="receiveBookmark">A bookmark at which the receive activity waits to receive a message.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.SendReceiveExtension.OnUninitializeCorrelation(System.Runtime.DurableInstancing.InstanceKey)">
      <summary>Executes when correlation handle is marked as disposed.</summary>
      <param name="correlationKey">An instance key of the activity used to correlate with the message.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.SendReceiveExtension.RegisterReceive(System.ServiceModel.Activities.ReceiveSettings,System.Runtime.DurableInstancing.InstanceKey,System.Activities.Bookmark)">
      <summary>Registers the received message activity.</summary>
      <param name="settings">The settings for the receive activity.</param>
      <param name="correlatesWith">An instance key of the activity used to correlate with the message.</param>
      <param name="receiveBookmark">A bookmark at which the receive activity waits to receive a message.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.SendReceiveExtension.Send(System.ServiceModel.Activities.MessageContext,System.ServiceModel.Activities.SendSettings,System.Runtime.DurableInstancing.InstanceKey,System.Activities.Bookmark)">
      <summary>Sends the given message.</summary>
      <param name="message">The message to send.</param>
      <param name="settings">The settings for send activity.</param>
      <param name="correlatesWith">An instance key of the activity used to correlate with the message.</param>
      <param name="sendCompleteBookmark">A bookmark at which the send activity waits for the sending completion.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.SendReply">
      <summary>An activity that sends the reply message as part of a request/response message exchange pattern on the service side.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.SendReply.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.SendReply" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.SendReply.Action">
      <summary>Gets or sets the value of the action header of the message.</summary>
      <returns>The action URI of the message.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.SendReply.Content">
      <summary>Gets or sets the content sent by the <see cref="T:System.ServiceModel.Activities.SendReply" /> activity.</summary>
      <returns>The content to send.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.SendReply.CorrelationInitializers">
      <summary>Gets a collection of correlation initializers.</summary>
      <returns>A collection of correlation initializers.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.SendReply.FromOperationDescription(System.ServiceModel.Description.OperationDescription,System.Collections.Generic.IEnumerable{System.ServiceModel.Activities.SendReply}@)">
      <summary>Returns the send reply activity from the given contract operation description.</summary>
      <returns>A <see cref="T:System.ServiceModel.Activities.SendReply" /> object from the given contract operation description.</returns>
      <param name="operation">The contract operation description.</param>
      <param name="faultReplies">When this method returns, contains the send replies with fault.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.SendReply.PersistBeforeSend">
      <summary>Gets or sets a value that specifies whether the workflow service instance should be persisted before sending the message.</summary>
      <returns>true if the workflow service should be persisted before sending the message;otherwise false.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.SendReply.Request">
      <summary>Gets or sets a reference to the <see cref="T:System.ServiceModel.Activities.Receive" /> activity paired with this <see cref="T:System.ServiceModel.Activities.SendReply" /> activity. </summary>
      <returns>A receive activity.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.SendSettings">
      <summary>Represents the settings for an activity that sends a message.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.SendSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.SendSettings" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.SendSettings.Endpoint">
      <summary>Gets or sets the endpoint to send messages to.</summary>
      <returns>The endpoint to send messages to.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.SendSettings.EndpointAddress">
      <summary>Gets or sets the address of the endpoint to send messages to.</summary>
      <returns>The endpoint address.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.SendSettings.EndpointConfigurationName">
      <summary>Gets or sets the name of the endpoint configuration.</summary>
      <returns>The endpoint configuration name.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.SendSettings.IsOneWay">
      <summary>Gets or sets a value that indicates whether communication is one-way.</summary>
      <returns>true if communication is one-way; false if communication is two-way.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.SendSettings.OwnerDisplayName">
      <summary>Gets or sets the display name of the owner.</summary>
      <returns>The display name of the owner.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.SendSettings.ProtectionLevel">
      <summary>Gets or sets a value that indicates the protection level for the message.</summary>
      <returns>One of the enumeration values of the <see cref="T:System.Net.Security.ProtectionLevel" /> enumeration.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.SendSettings.RequirePersistBeforeSend">
      <summary>Gets or sets a value that indicates whether the workflow service instance should be persisted before sending the message.</summary>
      <returns>true if the workflow service should be persisted before sending the message; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.SendSettings.TokenImpersonationLevel">
      <summary>Gets or sets a value that indicates the token impersonation level allowed for the receiver of the message.</summary>
      <returns>One of the enumeration values of the <see cref="T:System.Security.Principal.TokenImpersonationLevel" /> enumeration.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.SerializerOption">
      <summary>A class that represents the types of serializers available.</summary>
    </member>
    <member name="F:System.ServiceModel.Activities.SerializerOption.DataContractSerializer">
      <summary>Specifies that the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> should be used when serializing.</summary>
    </member>
    <member name="F:System.ServiceModel.Activities.SerializerOption.XmlSerializer">
      <summary>Specifies that the <see cref="T:System.Runtime.Serialization.XMLSerializer" /> should be used when serializing.</summary>
    </member>
    <member name="T:System.ServiceModel.Activities.TransactedReceiveScope">
      <summary>An activity which scopes the lifetime of a transaction which is initiated by a received message. The transaction may be flowed into the workflow on the initiating message, or be created by the dispatcher when the message is received.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.TransactedReceiveScope.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.TransactedReceiveScope" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.TransactedReceiveScope.Body">
      <summary>Gets or sets the <see cref="T:System.Activities.Activity" /> that makes up the body of the <see cref="T:System.ServiceModel.Activities.TransactedReceiveScope" /> activity. </summary>
      <returns>The body of the transacted receive scope.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.TransactedReceiveScope.Request">
      <summary>Gets or sets the <see cref="T:System.ServiceModel.Activities.Receive" /> activity associated with this <see cref="T:System.ServiceModel.Activities.TransactedReceiveScope" /> activity.</summary>
      <returns>The receive activity associated with the transacted receive scope.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.TransactedReceiveScope.Variables">
      <summary>Gets the collection of <see cref="T:System.Activities.Variables" /> associated with this <see cref="T:System.ServiceModel.Activities.TransactedReceiveScope" />.</summary>
      <returns>The variables associated with the transacted receive scope.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.WorkflowControlClient">
      <summary>Allows a client to send control operations to a workflow service hosted with <see cref="T:System.ServiceModel.Activities.WorkflowServiceHost" />.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowControlClient" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.#ctor(System.ServiceModel.Activities.WorkflowControlEndpoint)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowControlClient" /> class with the specified <see cref="T:System.ServiecModel.Activities.WorkflowControlEndpoint" />.</summary>
      <param name="workflowEndpoint">The workflow control endpoint.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.#ctor(System.ServiceModel.Channels.Binding,System.ServiceModel.EndpointAddress)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowControlClient" /> class with the specified binding, and <see cref="T:System.ServiecModel.Activities.WorkflowControlEndpoint" />.</summary>
      <param name="binding">The binding.</param>
      <param name="remoteAddress">The workflow control endpoint.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowControlClient" /> class with the specified endpoint configuration.</summary>
      <param name="endpointConfigurationName">The configuration to use.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.#ctor(System.String,System.ServiceModel.EndpointAddress)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowControlClient" /> class with the specified endpoint configuration and <see cref="T:System.ServiceModel.EndpointAddress" />.</summary>
      <param name="endpointConfigurationName">The endpoint configuration.</param>
      <param name="remoteAddress">The endpoint address.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowControlClient" /> class with the specified endpoint configuration and endpoint address.</summary>
      <param name="endpointConfigurationName">The endpoint configuration.</param>
      <param name="remoteAddress">The endpoint address.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.Abandon(System.Guid)">
      <summary>Abandons the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to abandon.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.Abandon(System.Guid,System.String)">
      <summary>Abandons the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to abandon.</param>
      <param name="reason">The reason to abandon the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.AbandonAsync(System.Guid)">
      <summary>Begins an asynchronous operation that abandons the specified workflow instance.</summary>
      <param name="instanceId">The workflows instance to abandon.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.AbandonAsync(System.Guid,System.Object)">
      <summary>Begins an asynchronous operation that abandons the specified workflow instance with the specified user-defined data.</summary>
      <param name="instanceId">The workflow instance to abandon.</param>
      <param name="userState">The user-defined state data.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.AbandonAsync(System.Guid,System.String)">
      <summary>Begins an asynchronous operation that abandons the specified workflow instance with the specified reason.</summary>
      <param name="instanceId">The workflow instance to abandon.</param>
      <param name="reason">The reason to abandon the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.AbandonAsync(System.Guid,System.String,System.Object)">
      <summary>Begins an asynchronous operation that abandons the specified workflow instance with the specified reason and user-defined data.</summary>
      <param name="instanceId">The workflow instance to abandon.</param>
      <param name="reason">The reason to abandon the workflow instance.</param>
      <param name="userState">The user-defined state data.</param>
    </member>
    <member name="E:System.ServiceModel.Activities.WorkflowControlClient.AbandonCompleted">
      <summary>Occurs when an asynchronous abandon operation completes.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.BeginAbandon(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that abandons the specified workflow instance.</summary>
      <returns>The asynchronous abandon operation.</returns>
      <param name="instanceId">The workflow instance to abandon.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">The user-defined state data.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.BeginAbandon(System.Guid,System.String,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that abandons the specified workflow instance.</summary>
      <returns>The asynchronous abandon operation.</returns>
      <param name="instanceId">The workflow instance to abandon.</param>
      <param name="reason">The reason to abandon the workflow instance.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">The user-defined state data.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.BeginCancel(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that cancels the specified workflow instance.</summary>
      <returns>The asynchronous cancel operation.</returns>
      <param name="instanceId">The workflow instance to cancel.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">The user-defined state data.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.BeginRun(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that runs the specified workflow instance.</summary>
      <returns>The asynchronous run operation.</returns>
      <param name="instanceId">The workflow instance to run.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">The user-defined state data.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.BeginSuspend(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that suspends the specified operation.</summary>
      <returns>The asynchronous suspend operation.</returns>
      <param name="instanceId">The workflow instance to suspend.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">The user-defined state data.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.BeginSuspend(System.Guid,System.String,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that suspends the specified operation.</summary>
      <returns>The asynchronous suspend operation.</returns>
      <param name="instanceId">The workflow instance to suspend.</param>
      <param name="reason">The reason the workflow instance is suspended.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">The user-defined state data.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.BeginTerminate(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that terminates a workflow instance.</summary>
      <returns>The asynchronous terminate operation.</returns>
      <param name="instanceId">The workflow instance to terminate.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">The user-defined state data.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.BeginTerminate(System.Guid,System.String,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that terminates a workflow instance.</summary>
      <returns>The asynchronous terminate operation.</returns>
      <param name="instanceId">The workflow instance to terminate.</param>
      <param name="reason">The reason to terminate the workflow instance.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">The user-defined state data.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.BeginUnsuspend(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that unsuspends a workflow instance.</summary>
      <returns>The asynchronous unsuspend operation.</returns>
      <param name="instanceId">The workflow instance to unsuspend.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">The user-defined state data.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.Cancel(System.Guid)">
      <summary>Cancels the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to cancel.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.CancelAsync(System.Guid)">
      <summary>Begins an asynchronous operation that cancels the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to cancel.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.CancelAsync(System.Guid,System.Object)">
      <summary>Begins an asynchronous operation that cancels the specified workflow instance with the specified user-defined data.</summary>
      <param name="instanceId">The workflow instance to cancel.</param>
      <param name="userState">The user-defined data.</param>
    </member>
    <member name="E:System.ServiceModel.Activities.WorkflowControlClient.CancelCompleted">
      <summary>Occurs when an asynchronous cancel operation completes.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.EndAbandon(System.IAsyncResult)">
      <summary>Completes an asynchronous abandon operation.</summary>
      <param name="result">The asynchronous result returned by a call to the <see cref="Overload:System.ServiceModel.Activities.WorkflowControlClient.BeginAbandon" /> method.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.EndCancel(System.IAsyncResult)">
      <summary>Completes an asynchronous cancel operation.</summary>
      <param name="result">The asynchronous result returned by a call to the <see cref="M:System.ServiceModel.Activities.WorkflowControlClient.BeginCancel(System.Guid,System.AsyncCallback,System.Object)" /> method.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.EndRun(System.IAsyncResult)">
      <summary>Completes an asynchronous run operation.</summary>
      <param name="result">The asynchronous result returned by a call to the <see cref="M:System.ServiceModel.Activities.WorkflowControlClient.BeginRun(System.Guid,System.AsyncCallback,System.Object)" /> method.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.EndSuspend(System.IAsyncResult)">
      <summary>Completes an asynchronous suspend operation.</summary>
      <param name="result">The asynchronous result returned by a call to the <see cref="Overload:System.ServiceModel.Activities.WorkflowControlClient.BeginSuspend" /> method.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.EndTerminate(System.IAsyncResult)">
      <summary>Completes an asynchronous terminate operation.</summary>
      <param name="result">The asynchronous result returned by a call to the <see cref="Overload:System.ServiceModel.Activities.WorkflowControlClient.BeginTerminate" /> method.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.EndUnsuspend(System.IAsyncResult)">
      <summary>Completes an asynchronous unsuspend operation.</summary>
      <param name="result">The asynchronous result returned by a call to the <see cref="M:System.ServiceModel.Activities.WorkflowControlClient.BeginUnsuspend(System.Guid,System.AsyncCallback,System.Object)" /> method.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.Run(System.Guid)">
      <summary>Runs the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to run.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.RunAsync(System.Guid)">
      <summary>Begins an asynchronous operation that runs the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to run.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.RunAsync(System.Guid,System.Object)">
      <summary>Begins an asynchronous operation that runs the specified workflow instance with the user-defined state data.</summary>
      <param name="instanceId">The workflow instance to run.</param>
      <param name="userState">The user-defined state data.</param>
    </member>
    <member name="E:System.ServiceModel.Activities.WorkflowControlClient.RunCompleted">
      <summary>Occurs when an asynchronous run operation completes.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.Suspend(System.Guid)">
      <summary>Suspends the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to suspend.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.Suspend(System.Guid,System.String)">
      <summary>Suspends the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to suspend.</param>
      <param name="reason">The reason to suspend the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.SuspendAsync(System.Guid)">
      <summary>Begins an asynchronous suspend operation </summary>
      <param name="instanceId">The workflow instance to suspend.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.SuspendAsync(System.Guid,System.Object)">
      <summary>Begins an asynchronous suspend operation with the specified workflow instance ID and user-defined state data.</summary>
      <param name="instanceId">The workflow instance to suspend.</param>
      <param name="userState">The user-defined state data.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.SuspendAsync(System.Guid,System.String)">
      <summary>Begins an asynchronous suspend operation with the specified workflow instance ID and reason.</summary>
      <param name="instanceId">The workflow instance to suspend.</param>
      <param name="reason">The reason to suspend the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.SuspendAsync(System.Guid,System.String,System.Object)">
      <summary>Begins an asynchronous suspend operation with the specified workflow instance ID, reason, and user-defined state data.</summary>
      <param name="instanceId">The workflow instance to suspend.</param>
      <param name="reason">The reason to suspend the workflow instance.</param>
      <param name="userState">The user-defined state data.</param>
    </member>
    <member name="E:System.ServiceModel.Activities.WorkflowControlClient.SuspendCompleted">
      <summary>Occurs when an asynchronous suspend operation completes.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.Terminate(System.Guid)">
      <summary>Terminates the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to terminate.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.Terminate(System.Guid,System.String)">
      <summary>Terminates the specified workflow instance with the specified reason.</summary>
      <param name="instanceId">The workflow instance to terminate.</param>
      <param name="reason">The reason to terminate the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.TerminateAsync(System.Guid)">
      <summary>Begins an asynchronous operation that terminates the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to terminate.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.TerminateAsync(System.Guid,System.Object)">
      <summary>Begins an asynchronous operation that terminates the specified workflow instance with the specified user-defined data.</summary>
      <param name="instanceId">The workflow instance to terminate.</param>
      <param name="userState">The user-defined state data.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.TerminateAsync(System.Guid,System.String)">
      <summary>Begins an asynchronous operation that terminates the specified workflow instance with the specified reason.</summary>
      <param name="instanceId">The workflow instance to terminate.</param>
      <param name="reason">The reason to terminate the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.TerminateAsync(System.Guid,System.String,System.Object)">
      <summary>Begins an asynchronous operation that terminates the specified workflow instance with the specified reason and user-defined data.</summary>
      <param name="instanceId">The workflow instance to terminate.</param>
      <param name="reason">The reason to terminate the workflow instance.</param>
      <param name="userState">The user-defined state data.</param>
    </member>
    <member name="E:System.ServiceModel.Activities.WorkflowControlClient.TerminateCompleted">
      <summary>Occurs when an asynchronous terminate operation completes.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.Unsuspend(System.Guid)">
      <summary>Unsuspends the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to unsuspend.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.UnsuspendAsync(System.Guid)">
      <summary>Begins an asynchronous unsuspend operation.</summary>
      <param name="instanceId">The workflow instance to unsuspend.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlClient.UnsuspendAsync(System.Guid,System.Object)">
      <summary>Begins an asynchronous unsuspend operation with the specified user-defined state data.</summary>
      <param name="instanceId">The workflow instance to unsuspend.</param>
      <param name="userState">The user-defined state data.</param>
    </member>
    <member name="E:System.ServiceModel.Activities.WorkflowControlClient.UnsuspendCompleted">
      <summary>Occurs when an asynchronous unsuspend operation completes.</summary>
    </member>
    <member name="T:System.ServiceModel.Activities.WorkflowControlEndpoint">
      <summary>A standard endpoint that enables you to call control operations on workflow instances. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlEndpoint.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowControlEndpoint" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowControlEndpoint.#ctor(System.ServiceModel.Channels.Binding,System.ServiceModel.EndpointAddress)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowControlEndpoint" /> class with the specified binding and <see cref="T:System.ServiceModel.EndpointAddress" />.</summary>
      <param name="binding">The binding.</param>
      <param name="address">The endpoint address.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.WorkflowCreationContext">
      <summary>Represents a context that is set up during workflow activation using the workflow hosting endpoint to handle arguments, and receive workflow completion notifications.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowCreationContext.#ctor">
      <summary>Initiates a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowCreationContext" /> class. </summary>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowCreationContext.CreateOnly">
      <summary>Gets or sets a value that indicates if the workflow instance should be suspended or run after creation..</summary>
      <returns>true if the workflow instance is suspended after it is created ; false if the workflow is run after it is created.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowCreationContext.IsCompletionTransactionRequired">
      <summary>Gets or sets a value that indicates whether the <see cref="M:System.ServiceModel.Activities.WorkflowCreationContext.OnBeginWorkflowCompleted(System.Activities.ActivityInstanceState,System.Collections.Generic.IDictionary{System.String,System.Object},System.Exception,System.TimeSpan,System.AsyncCallback,System.Object)" /> method is called in a transaction..</summary>
      <returns>true if a completion transaction is required; otherwise, false.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowCreationContext.OnAbort">
      <summary>Aborts the <see cref="T:System.ServiceModel.Activities.WorkflowCreationContext" />.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowCreationContext.OnBeginWorkflowCompleted(System.Activities.ActivityInstanceState,System.Collections.Generic.IDictionary{System.String,System.Object},System.Exception,System.TimeSpan,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation to perform workflow instance completion notification. This method is called when the workflow instance associated with this <see cref="T:System.ServiceModel.Activities.WorkflowCreationContext" /> completes. Derived classes can override this operation to perform custom actions on workflow instance completion.
</summary>
      <returns>The status of the asynchronous operation.</returns>
      <param name="completionState">The state of the workflow instance.</param>
      <param name="workflowOutputs">A collection of key/value pairs that contain output generated by the workflow instance.</param>
      <param name="terminationException">If present, an error that caused the workflow to terminate.</param>
      <param name="timeout">The time interval during which the asynchronous operation must complete.</param>
      <param name="callback">The location in an application to which control returns when the asynchronous operation completes.</param>
      <param name="state">User-defined state.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowCreationContext.OnEndWorkflowCompleted(System.IAsyncResult)">
      <summary>Completes an asynchronous operation that notifies workflow instance completion associated with this <see cref="T:System.ServiceModel.Activities.WorkflowCreationContext" />.</summary>
      <param name="result">The status of the asynchronous operation.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowCreationContext.WorkflowArguments">
      <summary>Gets the arguments passed to the newly created workflow instance.</summary>
      <returns>A dictionary of key/value pair arguments to the workflow instance.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.WorkflowHostingEndpoint">
      <summary>An abstract implementation of <see cref="T:System.ServiceModel.ServiceEndpoint" />. Derive from this class to expose contracts that support workflow creation and bookmark resumption. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowHostingEndpoint.#ctor(System.Type)">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowHostingEndpoint" /> class with the specified contract type.</summary>
      <param name="contractType">The type of the contract that defines the basic message exchange patterns for a workflow hosting operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowHostingEndpoint.#ctor(System.Type,System.ServiceModel.Channels.Binding,System.ServiceModel.EndpointAddress)">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowHostingEndpoint" /> class with the specified contract type, binding, and endpoint address.</summary>
      <param name="contractType">The service contract for the endpoint.</param>
      <param name="binding">The binding for the endpoint.</param>
      <param name="address">The address of the endpoint.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowHostingEndpoint.CorrelationQueries">
      <summary>Gets a collection of <see cref="T:System.ServiceModel.CorrelationQuery" /> instances .</summary>
      <returns>A collection of correlation queries.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowHostingEndpoint.OnGetCreationContext(System.Object[],System.ServiceModel.OperationContext,System.Guid,System.ServiceModel.Activities.WorkflowHostingResponseContext)">
      <summary> Override to create a new<see cref="T:System.ServiceModel.Activities.WorkflowCreationContext" /> instance.</summary>
      <returns>A workflow creation context object.</returns>
      <param name="inputs">The inputs to the service operation.</param>
      <param name="operationContext">Provides the execution context of the service operation invoked.</param>
      <param name="instanceId">The instance ID of the workflow instance being created.</param>
      <param name="responseContext">The <see cref="T:System.ServiceModel.Activities.WorkflowHostingEndpointResponseContext" /> object that can be used to send replies back to the message source for a request/reply contract.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowHostingEndpoint.OnGetInstanceId(System.Object[],System.ServiceModel.OperationContext)">
      <summary>Override to return the instance ID for the workflow instance being created.</summary>
      <returns>The instance ID for the newly created workflow instance.</returns>
      <param name="inputs">The inputs to the service operation.</param>
      <param name="operationContext">The operation context of the service operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowHostingEndpoint.OnResolveBookmark(System.Object[],System.ServiceModel.OperationContext,System.ServiceModel.Activities.WorkflowHostingResponseContext,System.Object@)">
      <summary>Override to return a bookmark to be resumed on the workflow instance. </summary>
      <returns>A bookmark.</returns>
      <param name="inputs">The inputs to the service operation.</param>
      <param name="operationContext">The execution context of the service operation being invoked.</param>
      <param name="responseContext">The <see cref="T:System.ServiceModel.Activities.WorkflowHostingEndpointResponseContext" /> object that can be used to send replies back to the message source for a request/reply contract.</param>
      <param name="value">A value to be passed back to the workflow instance when the bookmark is resumed </param>
    </member>
    <member name="T:System.ServiceModel.Activities.WorkflowHostingResponseContext">
      <summary>This class is used in conjunction with the <see cref="T:System.ServiceModel.Activities.WorkflowHostingEndpoint" /> class.  It is used for sending the response for a request/reply contract supported by a <see cref="T:System.ServiceModel.Activities.WorkflowHostingEndpoint" /> implementation.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowHostingResponseContext.SendResponse(System.Object,System.Object[])">
      <summary>Sends response to a message sent to the <see cref="T:System.ServiceModel.Activities.WorkflowHostingEndpoint" />.</summary>
      <param name="returnValue">The return value of the operation.</param>
      <param name="outputs">The out arguments of the operation.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.WorkflowService">
      <summary>Enables you to create, configure, and access the properties of a workflow service.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowService.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowService" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowService.AllowBufferedReceive">
      <summary>Gets or sets a value that indicates whether the workflow service has buffered receive processing enabled for incoming messages.</summary>
      <returns>true if the workflow service has buffered receive processing enabled for incoming messages; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowService.Body">
      <summary>Gets or sets the activity tree for this workflow service.</summary>
      <returns>The activity tree for this workflow service.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowService.ConfigurationName">
      <summary>Gets or sets the workflow service configuration name.</summary>
      <returns>The workflow service configuration name.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowService.DefinitionIdentity">
      <summary>Gets or sets the definition identity of the workflow service.</summary>
      <returns>The definition identity of the workflow service.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowService.Endpoints">
      <summary>Gets the collection of endpoints of this workflow service.</summary>
      <returns>The collection of endpoints of this workflow service.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowService.GetContractDescriptions">
      <summary>Returns a dictionary of contract descriptions keyed by the contract name.</summary>
      <returns>A dictionary of contract descriptions keyed by the contract name.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowService.GetWorkflowRoot">
      <summary>Returns the root of the activity tree.</summary>
      <returns>The root of the activity tree.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowService.ImplementedContracts">
      <summary>Gets the contracts implemented by the workflow service.</summary>
      <returns>The <see cref="T:System.Collections.ObjectModel.Collection`1" /> object that contains the contracts implemented by the workflow service.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowService.Name">
      <summary>Gets or sets the name of the workflow service as an <see cref="T:System.Xml.Linq.XName" /> object.</summary>
      <returns>The name of the workflow service.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowService.UpdateMaps">
      <summary>Gets the dynamic update maps.</summary>
      <returns>A <see cref="T:System.Collections.Generic.IDictionary`2" /> object that represents the dynamic update maps.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowService.Validate(System.Activities.Validation.ValidationSettings)">
      <summary>Verifies that a workflow service is correctly configured according to the validation logic.</summary>
      <returns>A collection of validation errors and validation warnings as a result of the validation process.</returns>
      <param name="settings">The custom settings that customize the behavior of the validation process.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.WorkflowServiceHost">
      <summary>Provides a host for workflows. Supports features like messaging activities, multi-instancing, and configuration. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowServiceHost" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.#ctor(System.Activities.Activity,System.Uri[])">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowServiceHost" /> class using the specified activity and base addresses.</summary>
      <param name="activity">The root activity of the workflow service.</param>
      <param name="baseAddresses">An array of  base addresses for the workflow service.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.#ctor(System.Object,System.Uri[])">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowServiceHost" /> class using the specified service object and base addresses.</summary>
      <param name="serviceImplementation">The root activity of the workflow service to be hosted.</param>
      <param name="baseAddresses">An array of base addresses for the workflow service.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.#ctor(System.ServiceModel.Activities.WorkflowService,System.Uri[])">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowServiceHost" /> class using the specified service definition and collection of base addresses.</summary>
      <param name="serviceDefinition">The workflow service to host.</param>
      <param name="baseAddresses">An array of base addresses for the hosted workflow service.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowServiceHost.Activity">
      <summary>Gets the root activity of the hosted workflow service. </summary>
      <returns>The root activity of the hosted workflow service.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.AddServiceEndpoint(System.ServiceModel.Description.ServiceEndpoint)">
      <summary>Adds a service endpoint to the workflow service host.</summary>
      <param name="endpoint">The service endpoint to add.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.AddServiceEndpoint(System.String,System.ServiceModel.Channels.Binding,System.String)">
      <summary>Adds a service endpoint to the workflow service host using the specified contract, binding, and address.</summary>
      <returns>A service endpoint.</returns>
      <param name="implementedContract">The service contract exposed by the endpoint.</param>
      <param name="binding">The binding for the endpoint.</param>
      <param name="address">The address of the endpoint..</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.AddServiceEndpoint(System.String,System.ServiceModel.Channels.Binding,System.String,System.Uri)">
      <summary>Adds a service endpoint to the workflow service host using the specified contract, binding, endpoint address, and listen URI.</summary>
      <returns>A service endpoint.</returns>
      <param name="implementedContract">The service contract exposed by the endpoint.</param>
      <param name="binding">The binding for the endpoint.</param>
      <param name="address">The address of the endpoint.</param>
      <param name="listenUri">The address at which the service endpoint listens for incoming messages.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.AddServiceEndpoint(System.String,System.ServiceModel.Channels.Binding,System.Uri)">
      <summary>Adds a service endpoint to the workflow service host using the specified contract, binding, and address.</summary>
      <returns>A service endpoint.</returns>
      <param name="implementedContract">The service contract exposed by the endpoint.</param>
      <param name="binding">The binding for the endpoint.</param>
      <param name="address">The address of the endpoint.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.AddServiceEndpoint(System.String,System.ServiceModel.Channels.Binding,System.Uri,System.Uri)">
      <summary>Adds a service endpoint to the workflow service host using the specified contract, binding, endpoint address, and listen URI.</summary>
      <returns>A service endpoint.</returns>
      <param name="implementedContract">The contract exposed by the endpoint.</param>
      <param name="binding">The binding for the endpoint.</param>
      <param name="address">The address of the endpoint.l</param>
      <param name="listenUri">The address at which the service endpoint listens for incoming messages.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.AddServiceEndpoint(System.Xml.Linq.XName,System.ServiceModel.Channels.Binding,System.String,System.Uri,System.String)">
      <summary>Adds a service endpoint to the workflow service host using the specified contract, binding, endpoint address, listen URI, and behavior configuration name.</summary>
      <returns>A service endpoint.</returns>
      <param name="serviceContractName">The contract exposed by the endpoint</param>
      <param name="binding">The binding for the endpoint.</param>
      <param name="address">The address of the endpoint.</param>
      <param name="listenUri">The address at which the service endpoint listens for incoming messages.</param>
      <param name="behaviorConfigurationName">The endpoint behavior configuration name. </param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.AddServiceEndpoint(System.Xml.Linq.XName,System.ServiceModel.Channels.Binding,System.Uri,System.Uri,System.String)">
      <summary>Adds a service endpoint to the workflow service host using the specified implemented contract, binding, endpoint address, listen URI, and a behavior configuration name.</summary>
      <returns>A service endpoint.</returns>
      <param name="serviceContractName">The contract exposed by the endpoint.</param>
      <param name="binding">The binding for the endpoint.</param>
      <param name="address">The address of the endpoint.</param>
      <param name="listenUri">The address at which the service endpoint listens for incoming messages.</param>
      <param name="behaviorConfigurationName">The behavior configuration name.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.CreateDescription(System.Collections.Generic.IDictionary{System.String,System.ServiceModel.Description.ContractDescription}@)">
      <summary>Creates a service description of the hosted workflow service using the specified key/value pair collection of address, binding, contract and behavior service endpoint information. </summary>
      <returns>A service for the hosted workflow service.</returns>
      <param name="implementedContracts">A collection of key/value pairs that contains the contract description for the hosted workflow service.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowServiceHost.DurableInstancingOptions">
      <summary>Gets the <see cref="T:System.ServiceModel.Activities.DurableInstancingOptions" /> associated with the workflow service host. </summary>
      <returns>The durable instancing options.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.InitializeRuntime">
      <summary>Initializes the runtime for the workflow service host.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.OnAbort">
      <summary>Called when  the workflow service host is aborted.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.OnBeginClose(System.TimeSpan,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation invoked when the workflow service host is closed.</summary>
      <returns>A reference to the asynchronous close operation.</returns>
      <param name="timeout">The period of time the asynchronous close operation has to complete before timing out.</param>
      <param name="callback">The asynchronous callback delegate that receives notification of the completion of the asynchronous close operation. </param>
      <param name="state">User-define state data that is associated with the asynchronous close operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.OnClose(System.TimeSpan)">
      <summary>Closes down the hosted service, including the channel dispatchers and associated instance contexts and listeners.</summary>
      <param name="timeout">The period of time the close operation has to complete before timing out.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.OnEndClose(System.IAsyncResult)">
      <summary>Completes an asynchronous operation invoked when the workflow service host is closed.</summary>
      <param name="result">A reference to the asynchronous operation.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowServiceHost.SupportedVersions">
      <summary>Gets a list of workflow versions that are supported by this workflow service host.</summary>
      <returns>A list of workflow versions that are supported by this workflow service host.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.WorkflowServiceHost.WorkflowExtensions">
      <summary>Gets a <see cref="T:System.Activities.Hosting.WorkflowInstanceExtensionManager" /> object to add or remove extensions to the workflow service instance.</summary>
      <returns>workflow instance extension manager.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.WorkflowUpdateableControlClient">
      <summary>Represents a service contract that defines a set of operations that allows a client to update workflow service hosted with <see cref="T:System.ServiceModel.Activities.WorkflowServiceHost" />.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowUpdateableControlClient" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.#ctor(System.ServiceModel.Activities.WorkflowControlEndpoint)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowUpdateableControlClient" /> class.</summary>
      <param name="workflowEndpoint">The workflow control endpoint.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.#ctor(System.ServiceModel.Channels.Binding,System.ServiceModel.EndpointAddress)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowUpdateableControlClient" /> class.</summary>
      <param name="binding">The binding.</param>
      <param name="remoteAddress">The endpoint address.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowUpdateableControlClient" /> class.</summary>
      <param name="endpointConfigurationName">The configuration to use.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.#ctor(System.String,System.ServiceModel.EndpointAddress)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowUpdateableControlClient" /> class.</summary>
      <param name="endpointConfigurationName">The configuration to use.</param>
      <param name="remoteAddress">The endpoint address.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowUpdateableControlClient" /> class.</summary>
      <param name="endpointConfigurationName">The configuration to use.</param>
      <param name="remoteAddress">The endpoint address.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.Abandon(System.Guid)">
      <summary>Abandons the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to abandon.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.Abandon(System.Guid,System.String)">
      <summary>Abandons the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to abandon.</param>
      <param name="reason">The reason to abandon the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.AbandonAsync(System.Guid)">
      <summary>Begins an asynchronous operation that abandons the specified workflow instance.</summary>
      <param name="instanceId">The workflows instance to abandon.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.AbandonAsync(System.Guid,System.Object)">
      <summary>Begins an asynchronous operation that abandons the specified workflow instance.</summary>
      <param name="instanceId">The workflows instance to abandon.</param>
      <param name="userState">A user-defined object that contains state information associated with the asynchronous abandon operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.AbandonAsync(System.Guid,System.String)">
      <summary>Begins an asynchronous operation that abandons the specified workflow instance.</summary>
      <param name="instanceId">The workflows instance to abandon.</param>
      <param name="reason">The reason to abandon the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.AbandonAsync(System.Guid,System.String,System.Object)">
      <summary>Begins an asynchronous operation that abandons the specified workflow instance.</summary>
      <param name="instanceId">The workflows instance to abandon.</param>
      <param name="reason">The reason to abandon the workflow instance.</param>
      <param name="userState">A user-defined object that contains state information associated with the asynchronous abandon operation.</param>
    </member>
    <member name="E:System.ServiceModel.Activities.WorkflowUpdateableControlClient.AbandonCompleted">
      <summary>Occurs when an asynchronous abandon operation completes.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.BeginAbandon(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that abandons the specified workflow instance.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</returns>
      <param name="instanceId">The workflow instance to abandon.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">A user-defined object that contains state information associated with the asynchronous abandon operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.BeginAbandon(System.Guid,System.String,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that abandons the specified workflow instance.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</returns>
      <param name="instanceId">The workflow instance to abandon.</param>
      <param name="reason">The reason to abandon the workflow instance.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">A user-defined object that contains state information associated with the asynchronous abandon operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.BeginCancel(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that cancels the specified workflow instance.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</returns>
      <param name="instanceId">The workflow instance to cancel.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">A user-defined object that contains state information associated with the asynchronous cancel operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.BeginRun(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that runs the specified workflow instance.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</returns>
      <param name="instanceId">The workflow instance to run.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">A user-defined object that contains state information associated with the asynchronous run operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.BeginSuspend(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that suspends the specified operation.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</returns>
      <param name="instanceId">The workflow instance to suspend.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">A user-defined object that contains state information associated with the asynchronous suspend operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.BeginSuspend(System.Guid,System.String,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that suspends the specified operation.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</returns>
      <param name="instanceId">The workflow instance to suspend.</param>
      <param name="reason">The reason to suspend the workflow instance.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">A user-defined object that contains state information associated with the asynchronous suspend operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.BeginTerminate(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that terminates a workflow instance.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</returns>
      <param name="instanceId">The workflow instance to terminate.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">A user-defined object that contains state information associated with the asynchronous terminate operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.BeginTerminate(System.Guid,System.String,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that terminates a workflow instance.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</returns>
      <param name="instanceId">The workflow instance to terminate.</param>
      <param name="reason">The reason to terminate the workflow instance.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">A user-defined object that contains state information associated with the asynchronous terminate operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.BeginUnsuspend(System.Guid,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that unsuspends a workflow instance.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</returns>
      <param name="instanceId">The workflow instance to unsuspend.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">A user-defined object that contains state information associated with the asynchronous unsuspend operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.BeginUpdate(System.Guid,System.Activities.WorkflowIdentity,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation that updates a workflow instance.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</returns>
      <param name="instanceId">The workflow instance to update.</param>
      <param name="updatedDefinitionIdentity">The updated workflow identity.</param>
      <param name="callback">The asynchronous callback delegate that receives the notification of the asynchronous operation completion.</param>
      <param name="state">A user-defined object that contains state information associated with the asynchronous update operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.Cancel(System.Guid)">
      <summary>Cancels the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to cancel.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.CancelAsync(System.Guid)">
      <summary>Begins an asynchronous operation that cancels the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to cancel.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.CancelAsync(System.Guid,System.Object)">
      <summary>Begins an asynchronous operation that cancels the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to cancel.</param>
      <param name="userState">A user-defined object that contains state information associated with the asynchronous cancel operation.</param>
    </member>
    <member name="E:System.ServiceModel.Activities.WorkflowUpdateableControlClient.CancelCompleted">
      <summary>Occurs when an asynchronous cancel operation completes.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.EndAbandon(System.IAsyncResult)">
      <summary>Completes an asynchronous abandon operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.EndCancel(System.IAsyncResult)">
      <summary>Completes an asynchronous cancel operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.EndRun(System.IAsyncResult)">
      <summary>Completes an asynchronous run operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.EndSuspend(System.IAsyncResult)">
      <summary>Completes an asynchronous suspend operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.EndTerminate(System.IAsyncResult)">
      <summary>Completes an asynchronous terminate operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.EndUnsuspend(System.IAsyncResult)">
      <summary>Completes an asynchronous unsuspend operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.EndUpdate(System.IAsyncResult)">
      <summary>Completes an asynchronous update operation.</summary>
      <param name="result">The <see cref="T:System.IAsyncResult" /> object that represents the status of the asynchronous operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.Run(System.Guid)">
      <summary>Runs the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to run.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.RunAsync(System.Guid)">
      <summary>Begins an asynchronous operation that runs the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to run.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.RunAsync(System.Guid,System.Object)">
      <summary>Begins an asynchronous operation that runs the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to run.</param>
      <param name="userState">A user-defined object that contains state information associated with the asynchronous run operation.</param>
    </member>
    <member name="E:System.ServiceModel.Activities.WorkflowUpdateableControlClient.RunCompleted">
      <summary>Occurs when an asynchronous run operation completes.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.Suspend(System.Guid)">
      <summary>Suspends the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to suspend.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.Suspend(System.Guid,System.String)">
      <summary>Suspends the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to suspend.</param>
      <param name="reason">The reason to suspend the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.SuspendAsync(System.Guid)">
      <summary>Begins an asynchronous suspend operation.</summary>
      <param name="instanceId">The workflow instance to suspend.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.SuspendAsync(System.Guid,System.Object)">
      <summary>Begins an asynchronous suspend operation.</summary>
      <param name="instanceId">The workflow instance to suspend.</param>
      <param name="userState">A user-defined object that contains state information associated with the asynchronous suspend operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.SuspendAsync(System.Guid,System.String)">
      <summary>Begins an asynchronous suspend operation.</summary>
      <param name="instanceId">The workflow instance to suspend.</param>
      <param name="reason">The reason to suspend the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.SuspendAsync(System.Guid,System.String,System.Object)">
      <summary>Begins an asynchronous suspend operation.</summary>
      <param name="instanceId">The workflow instance to suspend.</param>
      <param name="reason">The reason to suspend the workflow instance.</param>
      <param name="userState">A user-defined object that contains state information associated with the asynchronous run operation.</param>
    </member>
    <member name="E:System.ServiceModel.Activities.WorkflowUpdateableControlClient.SuspendCompleted">
      <summary>Occurs when an asynchronous suspend operation completes.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.Terminate(System.Guid)">
      <summary>Terminates the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to terminate.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.Terminate(System.Guid,System.String)">
      <summary>Terminates the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to terminate.</param>
      <param name="reason">The reason to terminate the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.TerminateAsync(System.Guid)">
      <summary>Begins an asynchronous terminate operation.</summary>
      <param name="instanceId">The workflow instance to terminate.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.TerminateAsync(System.Guid,System.Object)">
      <summary>Begins an asynchronous terminate operation.</summary>
      <param name="instanceId">The workflow instance to terminate.</param>
      <param name="userState">A user-defined object that contains state information associated with the asynchronous terminate operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.TerminateAsync(System.Guid,System.String)">
      <summary>Begins an asynchronous terminate operation.</summary>
      <param name="instanceId">The workflow instance to terminate.</param>
      <param name="reason">The reason to terminate the workflow instance.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.TerminateAsync(System.Guid,System.String,System.Object)">
      <summary>Begins an asynchronous terminate operation.</summary>
      <param name="instanceId">The workflow instance to terminate.</param>
      <param name="reason">The reason to terminate the workflow instance.</param>
      <param name="userState">A user-defined object that contains state information associated with the asynchronous terminate operation.</param>
    </member>
    <member name="E:System.ServiceModel.Activities.WorkflowUpdateableControlClient.TerminateCompleted">
      <summary>Occurs when an asynchronous terminate operation completes.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.Unsuspend(System.Guid)">
      <summary>Unsuspends the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to unsuspend.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.UnsuspendAsync(System.Guid)">
      <summary>Begins an asynchronous unsuspend operation.</summary>
      <param name="instanceId">The workflow instance to unsuspend.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.UnsuspendAsync(System.Guid,System.Object)">
      <summary>Begins an asynchronous unsuspend operation.</summary>
      <param name="instanceId">The workflow instance to unsuspend.</param>
      <param name="userState">A user-defined object that contains state information associated with the asynchronous unsuspend operation.</param>
    </member>
    <member name="E:System.ServiceModel.Activities.WorkflowUpdateableControlClient.UnsuspendCompleted">
      <summary>Occurs when an asynchronous unsuspend operation completes.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.Update(System.Guid,System.Activities.WorkflowIdentity)">
      <summary>Updates the specified workflow instance.</summary>
      <param name="instanceId">The workflow instance to update.</param>
      <param name="updatedDefinitionIdentity">The updated workflow identity.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.UpdateAsync(System.Guid,System.Activities.WorkflowIdentity)">
      <summary>Begins an asynchronous update operation.</summary>
      <param name="instanceId">The workflow instance to update.</param>
      <param name="updatedDefinitionIdentity">The updated workflow identity.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowUpdateableControlClient.UpdateAsync(System.Guid,System.Activities.WorkflowIdentity,System.Object)">
      <summary>Begins an asynchronous update operation.</summary>
      <param name="instanceId">The workflow instance to update.</param>
      <param name="updatedDefinitionIdentity">The updated workflow identity.</param>
      <param name="userState">A user-defined object that contains state information associated with the asynchronous update operation.</param>
    </member>
    <member name="E:System.ServiceModel.Activities.WorkflowUpdateableControlClient.UpdateCompleted">
      <summary>Occurs when an asynchronous update operation completes.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.OnBeginOpen(System.TimeSpan,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation invoked when the workflow service host is opened.</summary>
      <returns>A reference to the asynchronous operation.</returns>
      <param name="timeout">The period of time the asynchronous open operation has to complete before timing out.</param>
      <param name="callback">The asynchronous callback delegate that receives notification of the completion of the asynchronous open operation.</param>
      <param name="state">User-define state data that is associated with the asynchronous open operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.OnEndOpen(System.IAsyncResult)">
      <summary>Completes an asynchronous operation invoked when the workflow service host is opened.</summary>
      <param name="result">A reference to the asynchronous operation.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.WorkflowServiceHost.OnOpen(System.TimeSpan)">
      <summary>Opens the channel dispatchers.</summary>
      <param name="timeout">The period of time the open operation has to complete before timing out.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.Configuration.BufferedReceiveElement">
      <summary>Defines the configuration element that controls the buffering of data received from the client by the workflow service operation. This is a sealed class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.BufferedReceiveElement.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.Configuration.BufferedReceiveElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.BufferedReceiveElement.BehaviorType">
      <summary>Gets the behavior type of the service element that controls the workflow runtime buffering of messages received from a client instance of workflow.</summary>
      <returns>A type object.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.BufferedReceiveElement.MaxPendingMessagesPerChannel">
      <summary>Gets or sets the maximum number of messages which can be buffered for a communications channel during a workflow service operation.</summary>
      <returns>An integer.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Configuration.ChannelSettingsElement">
      <summary>Enables assigning values to the settings of a channel listener configuration element in a workflow service application. This is a sealed class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.ChannelSettingsElement.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.Configuration.ChannelSettingsElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.ChannelSettingsElement.IdleTimeout">
      <summary>Gets or sets the maximum interval of time for which the object can remain idle in the cache before being disposed..</summary>
      <returns>A timespan object.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.ChannelSettingsElement.LeaseTimeout">
      <summary>Gets or sets the interval of time after which an object will be removed from the cache.</summary>
      <returns>A timespan object.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.ChannelSettingsElement.MaxItemsInCache">
      <summary>Gets or sets the maximum number of objects that can be in the cache.</summary>
      <returns>An integer representing a maximum number.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Configuration.EtwTrackingBehaviorElement">
      <summary>Creates and maintains the <see cref="T:System.ServiceModel.Activities.Description.EtwTrackingBehavior" /> sub-element in the service behaviors section of a Sql workflow configuration file.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.EtwTrackingBehaviorElement.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.Configuration.EtwTrackingBehaviorElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.EtwTrackingBehaviorElement.BehaviorType">
      <summary>Gets the type of the <see cref="T:System.ServiceModel.Activities.Configuration.EtwTrackingBehaviorElement" /> instance.</summary>
      <returns>The type of an event tracking behavior element.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.EtwTrackingBehaviorElement.CreateBehavior">
      <summary>Returns a new instance of the <see cref="T:System.ServiceModel.Activities.Description.EtwTrackingBehavior" /> class.</summary>
      <returns>An instance of an event tracking behavior object.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.EtwTrackingBehaviorElement.ProfileName">
      <summary>Gets the tracking profile name parameter for the event tracking behavior element.</summary>
      <returns>A string profile name.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.EtwTrackingBehaviorElement.Properties">
      <summary>Gets the <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> of the current event tracking behavior element.</summary>
      <returns>A collection of configuration properties.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Configuration.FactorySettingsElement">
      <summary>Enables assigning values to the settings of a channel factory configuration element in a workflow service application. This is a sealed class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.FactorySettingsElement.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.Configuration.FactorySettingsElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.FactorySettingsElement.IdleTimeout">
      <summary>Gets or sets the maximum interval of time for which the object can remain idle in a workflow cache before being disposed.</summary>
      <returns>A timespan object.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.FactorySettingsElement.LeaseTimeout">
      <summary>Gets or sets the interval of time after which an object will be removed from a workflow cache.</summary>
      <returns>A timespan object.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.FactorySettingsElement.MaxItemsInCache">
      <summary>Gets or sets the maximum number of objects that can be in a workflow cache.</summary>
      <returns>An integer representing a maximum number.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Configuration.SendMessageChannelCacheElement">
      <summary>Creates and maintains the sub-element that configures use of the <see cref="T:System.ServiceModel.Activities.SendMessageChannelCache" />, in the service behaviors section of a workflow configuration file. This is a sealed class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.SendMessageChannelCacheElement.#ctor">
      <summary>Creates a  new instance of the <see cref="T:System.ServiceModel.Activities.Configuration.SendMessageChannelCacheElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.SendMessageChannelCacheElement.AllowUnsafeCaching">
      <summary>Gets a value indicating whether the cache associated with an instance of workflow is turned on or off. </summary>
      <returns>true if the workflow instance cache is turned on; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.SendMessageChannelCacheElement.BehaviorType">
      <summary>Gets or sets the type of the <see cref="T:System.ServiceModel.Activities.Configuration.SendMessageChannelCacheElement" />.</summary>
      <returns>A type object.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.SendMessageChannelCacheElement.ChannelSettings">
      <summary>Gets or sets the settings of the channel cache.</summary>
      <returns>The settings of the channel cache.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.SendMessageChannelCacheElement.FactorySettings">
      <summary>Gets or sets the settings of the channel factory cache.</summary>
      <returns>The settings of the channel factory cache.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Configuration.ServiceModelActivitiesSectionGroup">
      <summary>Represents a configuration section that contains workflow hosting options.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.ServiceModelActivitiesSectionGroup.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Configuration.ServiceModelActivitiesSectionGroup" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.ServiceModelActivitiesSectionGroup.GetSectionGroup(System.Configuration.Configuration)">
      <summary>Gets or sets the <see cref="T:System.ServiceModel.Activities.Configuration.ServiceModelActivitiesSectionGroup" /> from the given configuration.</summary>
      <returns>The <see cref="T:System.ServiceModel.Activities.Configuration.ServiceModelActivitiesSectionGroup" />.</returns>
      <param name="config">The configuration from which to get the <see cref="T:System.ServiceModel.Activities.Configuration.ServiceModelActivitiesSectionGroup" />.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.ServiceModelActivitiesSectionGroup.WorkflowHostingOptionsSection">
      <summary>Gets or sets the <see cref="T:System.ServiceModel.Activities.Configuration.WorkflowHostingOptionsSection" />.</summary>
      <returns>The <see cref="T:System.ServiceModel.Activities.Configuration.WorkflowHostingOptionsSection" />.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement">
      <summary>Creates and maintains the <see cref="T:System.Activities.DurableInstancing.SqlWorkflowInstanceStore" /> sub-element of the service behaviors section of a Sql workflow configuration file.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement.BehaviorType">
      <summary>Gets the type of a <see cref="T:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior" /> object.</summary>
      <returns>A type object.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement.ConnectionString">
      <summary>Gets or sets the connection string to the database server.</summary>
      <returns>A string that contains information about the connection to the instance store database.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement.ConnectionStringName">
      <summary>Gets or sets a named connection string to the database server.</summary>
      <returns>A string name that represents a connection to a database server.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement.CreateBehavior">
      <summary>Returns a new object of type <see cref="T:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior" /> that contains property settings for a <see cref="T:System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement" />.</summary>
      <returns>An generic object that contains workflow configuration element property settings.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement.HostLockRenewalPeriod">
      <summary>Gets or sets the time period in which the host must renew the lock on an instance.</summary>
      <returns>A timespan object that represents the lock renewal period.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement.InstanceCompletionAction">
      <summary>Gets or sets whether workflow instance data is kept in the persistence store after the workflow instance completes or if it is deleted at that point.</summary>
      <returns>An enumeration of the actions that can occur when an instance completes.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement.InstanceEncodingOption">
      <summary>Gets or sets the optional property that specifies how compression is used for instance data.</summary>
      <returns>An option that determines the manner in which data compression is performed, if at all.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement.InstanceLockedExceptionAction">
      <summary>Gets or sets the action that occurs in response to an exception that is thrown when a workflow instance is locked.</summary>
      <returns>An action response to an exception thrown.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement.MaxConnectionRetries">
      <summary>Gets or sets the maximum number of SQL connection retries.  The default value is 4.</summary>
      <returns>An integer representing the maximum number of SQL connection retries.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.SqlWorkflowInstanceStoreElement.RunnableInstancesDetectionPeriod">
      <summary>Gets or sets the <see cref="P:System.Activities.DurableInstancing.SqlWorkflowInstanceStore.RunnableInstancesDetectionPeriod" /> property. Specifies the time period after which the SQL Workflow Instance Store runs a detection task to detect any runnable or activatable workflow instances in the persistence database after the previous detection cycle. </summary>
      <returns>Returns the time interval.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointCollectionElement">
      <summary>Represents a collection of <see cref="T:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointElement" /> objects in a workflow control configuration element.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointCollectionElement.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointCollectionElement" /> class.</summary>
    </member>
    <member name="T:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointElement">
      <summary>Defines a workflow control endpoint element for a workflow configuration file. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointElement.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointElement" /> class. </summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointElement.Address">
      <summary>Gets or sets the URI address for the control endpoint.</summary>
      <returns>A string containing the URI address.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointElement.Binding">
      <summary>Gets or sets the binding for the control endpoint.</summary>
      <returns>A string containing the control endpoint binding.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointElement.BindingConfiguration">
      <summary>Gets or sets the binding configuration for the control endpoint.</summary>
      <returns>A string containing the binding configuration for the endpoint.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointElement.CreateServiceEndpoint(System.ServiceModel.Description.ContractDescription)">
      <summary>Returns a new <see cref="T:System.ServiceModel.Activities.WorkflowControlEndpoint" />.</summary>
      <returns>A workflow control service endpoint .</returns>
      <param name="contractDescription">Not used.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointElement.EndpointType">
      <summary>When called or overridden by a derived class, gets the type of the workflow control endpoint.</summary>
      <returns>The type of endpoint.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointElement.OnApplyConfiguration(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Configuration.ChannelEndpointElement)">
      <summary>When called or overridden by a derived class, converts the specified <see cref="T:System.ServiceModel.Description.ServiceEndpoint" /> to a workflow control endpoint.</summary>
      <param name="endpoint">The service endpoint to which control configuration settings are applied.</param>
      <param name="channelEndpointElement">Not implemented.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointElement.OnApplyConfiguration(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Configuration.ServiceEndpointElement)">
      <summary>When called or overridden by a derived class, converts the specified <see cref="T:System.ServiceModel.Description.ServiceEndpoint" /> to a workflow control endpoint.</summary>
      <param name="endpoint">The service endpoint to which control configuration settings are applied.</param>
      <param name="serviceEndpointElement">Not implemented.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointElement.OnInitializeAndValidate(System.ServiceModel.Configuration.ChannelEndpointElement)">
      <summary>When called or overridden by a derived class, initializes and validates the specified channel endpoint element.</summary>
      <param name="channelEndpointElement">The channel endpoint element to initialize and validate.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointElement.OnInitializeAndValidate(System.ServiceModel.Configuration.ServiceEndpointElement)">
      <summary>When called or overridden by a derived class, initializes and validates the specified service endpoint element.</summary>
      <param name="serviceEndpointElement">The service endpoint element to initialize and validate.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.WorkflowControlEndpointElement.Properties">
      <summary>Gets a collection of properties for the control endpoint element.</summary>
      <returns>A collection of configuration properties.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Configuration.WorkflowHostingOptionsSection">
      <summary>Represents the configuration section for the workflow hosting options. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.WorkflowHostingOptionsSection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Configuration.WorkflowHostingOptionsSection" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.WorkflowHostingOptionsSection.OverrideSiteName">
      <summary>Gets or sets a value that indicates whether to override site name.</summary>
      <returns>true if to override site name; otherwise, false.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Configuration.WorkflowIdleElement">
      <summary>Handles the properties of the workflow definition idle element. The idle element defines the process that is followed when the workflow instance goes into an idle state. This is a sealed class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.WorkflowIdleElement.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.Configuration.WorkflowIdleElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.WorkflowIdleElement.BehaviorType">
      <summary>Gets whether the workflow instance only unloads from memory or is persisted when the workflow instance becomes idle.</summary>
      <returns>An object that represents a type of behavior.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.WorkflowIdleElement.TimeToPersist">
      <summary>Gets or sets the period of time allotted for persisting the workflow instance.</summary>
      <returns>A timespan object.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.WorkflowIdleElement.TimeToUnload">
      <summary>Gets or sets the period of time allotted for unloading the workflow instance.</summary>
      <returns>A timespan object.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Configuration.WorkflowInstanceManagementElement">
      <summary>Adds the <see cref="T:System.ServiceModel.Activities.Description.WorkflowInstanceManagementBehavior" /> to the service host.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.WorkflowInstanceManagementElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Configuration.WorlkflowInstanceManagementElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.WorkflowInstanceManagementElement.AuthorizedWindowsGroup">
      <summary>Gets or sets the Windows group of users authorized to use the workflow instance control endpoint.</summary>
      <returns>The Windows group of users authorized to use the workflow instance control endpoint.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.WorkflowInstanceManagementElement.BehaviorType">
      <summary>Gets the type of the behavior associated with this configuration element.</summary>
      <returns>The type of behavior associated with this configuration element.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.WorkflowInstanceManagementElement.CreateBehavior">
      <summary>Creates the <see cref="T:System.ServiceModel.Activities.Description.WorkflowInstanceManagementBehavior" /> behavior.</summary>
      <returns>The workflow instance management behavior..</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.WorkflowInstanceManagementElement.Properties">
      <summary>Gets the collection of properties associated with this configuration element.</summary>
      <returns>The properties associated with this configuration element.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Configuration.WorkflowUnhandledExceptionElement">
      <summary>Handles the properties of an unhandled exception that occurs during a workflow service operation.  This is a sealed class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Configuration.WorkflowUnhandledExceptionElement.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.Configuration.WorkflowUnhandledExceptionElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.WorkflowUnhandledExceptionElement.Action">
      <summary>Gets or sets the process that is invoked when the unhandled exception occurs.</summary>
      <returns>An action.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Configuration.WorkflowUnhandledExceptionElement.BehaviorType">
      <summary>Gets the type of the unhandled exception behavior.</summary>
      <returns>A type object.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Description.BufferedReceiveServiceBehavior">
      <summary>A service behavior that enables your service to use buffered receive processing. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.BufferedReceiveServiceBehavior.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.Description.BufferedReceiveServiceBehavior" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.BufferedReceiveServiceBehavior.AddBindingParameters(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase,System.Collections.ObjectModel.Collection{System.ServiceModel.Description.ServiceEndpoint},System.ServiceModel.Channels.BindingParameterCollection)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IServiceBehavior.AddBindingParameters(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase,System.Collections.ObjectModel.Collection{System.ServiceModel.Description.ServiceEndpoint},System.ServiceModel.Channels.BindingParameterCollection)" /> method.</summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The service host.</param>
      <param name="endpoints">The endpoints exposed by the service.</param>
      <param name="bindingParameters">The binding parameters required to implement the behavior.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.BufferedReceiveServiceBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IServiceBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)" /> method.</summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The service host.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.BufferedReceiveServiceBehavior.MaxPendingMessagesPerChannel">
      <summary>Gets or sets a value that specifies the maximum number of pending messages allowed for each channel.</summary>
      <returns>The maximum number of pending messages.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.BufferedReceiveServiceBehavior.Validate(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IServiceBehavior.Validate(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)" /> method.</summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The service host.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.Description.EtwTrackingBehavior">
      <summary>A service behavior that allows a service to utilize ETW tracking using an <see cref="T:System.Activities.Tracking.EtwTrackingParticipant" />.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.EtwTrackingBehavior.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.Description.EtwTrackingBehavior" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.EtwTrackingBehavior.AddBindingParameters(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase,System.Collections.ObjectModel.Collection{System.ServiceModel.Description.ServiceEndpoint},System.ServiceModel.Channels.BindingParameterCollection)">
      <summary>An implementation of the <see cref="M:System.ServiceModel.Description.IServiceBehavior.AddBindingParameters(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase,System.Collections.ObjectModel.Collection{System.ServiceModel.Description.ServiceEndpoint},System.ServiceModel.Channels.BindingParameterCollection)" /> method.</summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The host of the service.</param>
      <param name="endpoints">The service endpoints.</param>
      <param name="bindingParameters">Custom objects required to implement the behavior.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.EtwTrackingBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)">
      <summary>An implementation of the <see cref="M:System.ServiceModel.Description.IServiceBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)" /></summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The host of the service.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.EtwTrackingBehavior.ProfileName">
      <summary>Gets or sets the name of the <see cref="T:System.Activities.Tracking.TrackingProfile" /> associated with this behavior.</summary>
      <returns>The name of the <see cref="T:System.Activities.Tracking.TrackingProfile" />.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.EtwTrackingBehavior.Validate(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)">
      <summary>An implementation of the <see cref="M:System.ServiceModel.Description.IServiceBehavior.Validate(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)" /> method.</summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The host of the service.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior">
      <summary>A service behavior that allows you to configure the <see cref="T:System.Activities.DurableInstancing.SqlWorkflowInstanceStore" /> feature, which supports persisting state information for workflow service instances into an SQL Server 2005 or SQL Server 2008 database.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior.#ctor">
      <summary>Initializes an instance of the <see cref="T:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior.#ctor(System.String)">
      <summary>Initializes an instance of the <see cref="T:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior" /> class using the connection string passed as a parameter.</summary>
      <param name="connectionString">A connection string that refers to a SQL Server persistence database.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior.AddBindingParameters(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase,System.Collections.ObjectModel.Collection{System.ServiceModel.Description.ServiceEndpoint},System.ServiceModel.Channels.BindingParameterCollection)">
      <summary>An implementation of the AddBindingParameters method of the <see cref="T:System.ServiceModel.Description.IServiceBehavior" /> interface, which allows you to pass custom data to binding elements to support contract implementation.</summary>
      <param name="serviceDescription">The service description of the service.</param>
      <param name="serviceHostBase">The host of the service.</param>
      <param name="endpoints">The service endpoints.</param>
      <param name="bindingParameters">Custom objects to which binding elements have access.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)">
      <summary>An implementation of the ApplyDispatchBehavior method of the <see cref="T:System.ServiceModel.Description.IServiceBehavior" /> interface that allows you to change run-time property values or insert custom extension objects such as error handlers, message or parameter inspectors, security extensions, and other custom extension objects.</summary>
      <param name="serviceDescription">Description of the service.</param>
      <param name="serviceHostBase">The host of the service.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior.ConnectionString">
      <summary>Gets the connection string used to connect to persistence database.</summary>
      <returns>The connection string.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior.HostLockRenewalPeriod">
      <summary>Gets or sets the <see cref="P:System.Activities.DurableInstancing.SqlWorkflowInstanceStore.HostLockRenewalPeriod" />. This property specifies the interval within which a host must renew its lock on an instance before the lock expires. If the host does not renew the lock in the specified time period, the instance is unlocked and another host can obtain the lock on the instance.</summary>
      <returns>The specified time period before the instance is unlocked.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior.InstanceCompletionAction">
      <summary>Gets or sets the <see cref="P:System.Activities.DurableInstancing.SqlWorkflowInstanceStore.InstanceCompletionAction" /> property. This property specifies whether the instance state information is kept in the persistence database after the instance is completed.</summary>
      <returns>true if the instance state information is kept in the persistence database; otherwise false.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior.InstanceEncodingOption">
      <summary>Gets or sets the <see cref="P:System.Activities.DurableInstancing.SqlWorkflowInstanceStore.InstanceEncodingOption" /> property. This property specifies whether the instance state information is compressed using the GZip algorithm before the information is saved in the persistence store.</summary>
      <returns>true if the instance state information is compressed using the GZip algorithm; otherwise false.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior.InstanceLockedExceptionAction">
      <summary>Gets or sets the <see cref="P:System.Activities.DurableInstancing.SqlWorkflowInstanceStore.InstanceLockedExceptionAction" /> property. This property specifies what action a service host should take when it receives an <see cref="T:System.Runtime.Persistence.InstanceLockedException" /> when the host tries to lock an instance because the instance is currently locked by another host. </summary>
      <returns>The action a service host should take when receiving a <see cref="T:System.Runtime.Persistence.InstanceLockedException" />.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior.MaxConnectionRetries">
      <summary>Gets or sets the maximum number of SQL connection retries.  The default value is 4.</summary>
      <returns>An integer representing the maximum number of SQL connection retries.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior.Promote(System.String,System.Collections.Generic.IEnumerable{System.Xml.Linq.XName},System.Collections.Generic.IEnumerable{System.Xml.Linq.XName})">
      <summary>Promotes properties that can be used in queries. The promoted properties are the properties that you can use in queries to retrieve instances. These property can be of simple type (int32, string, bool and so on), which is represented by a variant or binary.</summary>
      <param name="name">Name of the promotion. </param>
      <param name="promoteAsSqlVariant">The names of variables that must be promoted as variants. </param>
      <param name="promoteAsBinary">The names of variables that must be promoted as binary data. Binary data is typically used to store tracking information.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior.RunnableInstancesDetectionPeriod">
      <summary>Gets or sets the <see cref="P:System.Activities.DurableInstancing.SqlWorkflowInstanceStore.RunnableInstancesDetectionPeriod" /> property. Specifies the time period after which the SQL Workflow Instance Store runs a detection task to detect any runnable or activatable workflow instances in the persistence database after the previous detection cycle. </summary>
      <returns>Returns the value of the time interval.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.SqlWorkflowInstanceStoreBehavior.Validate(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)">
      <summary>Inspects the service host and the service description to confirm that the service can run successfully. </summary>
      <param name="serviceDescription">The description of the service.</param>
      <param name="serviceHostBase">The host for the service.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.Description.WorkflowContractBehaviorAttribute">
      <summary>An attribute that can be applied to a service contract to enable workflow service model dispatch behavior. This attribute ensures that appropriate dispatch components are created..</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowContractBehaviorAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Description.WorkflowContractBehaviorAttribute" /> class.  </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowContractBehaviorAttribute.AddBindingParameters(System.ServiceModel.Description.ContractDescription,System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Channels.BindingParameterCollection)">
      <summary>Configures binding elements to support the workflow contract behavior.</summary>
      <param name="contractDescription">The contract description.</param>
      <param name="endpoint">The endpoint.</param>
      <param name="bindingParameters">The objects that the binding elements require to support the workflow contract behavior.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowContractBehaviorAttribute.ApplyClientBehavior(System.ServiceModel.Description.ContractDescription,System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Dispatcher.ClientRuntime)">
      <summary>Implements a modification or extension of the client across a contract.</summary>
      <param name="contractDescription">The contract description for which the extension is intended.</param>
      <param name="endpoint">The endpoint.</param>
      <param name="clientRuntime">The client runtime.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowContractBehaviorAttribute.ApplyDispatchBehavior(System.ServiceModel.Description.ContractDescription,System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Dispatcher.DispatchRuntime)">
      <summary>Implements a modification or extension of the client across a contract.</summary>
      <param name="contractDescription">The contract description.</param>
      <param name="endpoint">The endpoint.</param>
      <param name="dispatchRuntime">The dispatch runtime that controls service execution.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowContractBehaviorAttribute.Validate(System.ServiceModel.Description.ContractDescription,System.ServiceModel.Description.ServiceEndpoint)">
      <summary>Validates that the contract and endpoint are compatible with the behavior.</summary>
      <param name="contractDescription">The contract description.</param>
      <param name="endpoint">The endpoint.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.Description.WorkflowIdleBehavior">
      <summary>A service behavior that controls when idle workflow instances are unloaded and persisted.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowIdleBehavior.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Description.WorkflowIdleBehavior" />.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowIdleBehavior.AddBindingParameters(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase,System.Collections.ObjectModel.Collection{System.ServiceModel.Description.ServiceEndpoint},System.ServiceModel.Channels.BindingParameterCollection)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IServiceBehavior.AddBindingParameters(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase,System.Collections.ObjectModel.Collection{System.ServiceModel.Description.ServiceEndpoint},System.ServiceModel.Channels.BindingParameterCollection)" /> method.</summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The service host.</param>
      <param name="endpoints">The endpoints exposed by the service.</param>
      <param name="bindingParameters">The binding parameters required to implement the behavior.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowIdleBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IServiceBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)" /> method.</summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The service host.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.WorkflowIdleBehavior.TimeToPersist">
      <summary>Gets or sets a value that specifies the <see cref="T:System.TimeSpan" /> that occurs between the time the workflow becomes idle and is persisted.</summary>
      <returns>The time span used to determine when a workflow is persisted.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.WorkflowIdleBehavior.TimeToUnload">
      <summary>Gets or sets a value that specifies the <see cref="T:System.TimeSpan" /> that occurs between the time workflow becomes idle and is unloaded.</summary>
      <returns>The time span used to determine when a workflow is unloaded.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowIdleBehavior.Validate(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IServiceBehavior.Validate(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)" /> method.</summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The service host.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.Description.WorkflowInstanceManagementBehavior">
      <summary>A behavior that adds a workflow control endpoint with a fixed configuration to the service host.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowInstanceManagementBehavior.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Description.WorkflowInstanceManagementBehavior" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowInstanceManagementBehavior.AddBindingParameters(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase,System.Collections.ObjectModel.Collection{System.ServiceModel.Description.ServiceEndpoint},System.ServiceModel.Channels.BindingParameterCollection)">
      <summary>An implementation of the <see cref="M:System.ServiceModel.Description.IServiceBehavior.AddBindingParameters(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase,System.Collections.ObjectModel.Collection{System.ServiceModel.Description.ServiceEndpoint},System.ServiceModel.Channels.BindingParameterCollection)" /> method.</summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The service host.</param>
      <param name="endpoints">The service endpoints.</param>
      <param name="bindingParameters">The binding parameters.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowInstanceManagementBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)">
      <summary>An implementation of the <see cref="M:System.ServiceModel.Description.IServiceBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)" /> method.</summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The service host.</param>
    </member>
    <member name="F:System.ServiceModel.Activities.Description.WorkflowInstanceManagementBehavior.ControlEndpointAddress">
      <summary>The fixed workflow control relative endpoint address. This address is appended to the base address of the service to create the control endpoint address.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.WorkflowInstanceManagementBehavior.HttpControlEndpointBinding">
      <summary>Gets the binding for the workflow instance control endpoint when the HTTP protocol is used.</summary>
      <returns>The binding for the workflow instance control endpoint.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.WorkflowInstanceManagementBehavior.NamedPipeControlEndpointBinding">
      <summary>Gets the binding for the workflow instance control endpoint when the net.pipe protocol is used.</summary>
      <returns>The binding for the workflow instance control endpoint.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowInstanceManagementBehavior.Validate(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)">
      <summary>An implementation of the <see cref="M:System.ServiceModel.Description.IServiceBehavior.Validate(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)method" />.</summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The service host.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.WorkflowInstanceManagementBehavior.WindowsGroup">
      <summary>Gets or sets the Windows group of users authorized to use the workflow instance control endpoint.</summary>
      <returns>The Windows group of users authorized to use the workflow instance control endpoint.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Description.WorkflowUnhandledExceptionAction">
      <summary>An enumeration that specifies the action to perform when an unhandled exception occurs within a workflow.</summary>
    </member>
    <member name="F:System.ServiceModel.Activities.Description.WorkflowUnhandledExceptionAction.Abandon">
      <summary>Abandons the workflow instance.</summary>
    </member>
    <member name="F:System.ServiceModel.Activities.Description.WorkflowUnhandledExceptionAction.Cancel">
      <summary>Cancels the workflow instance.</summary>
    </member>
    <member name="F:System.ServiceModel.Activities.Description.WorkflowUnhandledExceptionAction.Terminate">
      <summary>Terminates the workflow instance. The instance state is <see cref="T:System.Activities.Hosting.WorkflowInstanceState.Complete" />.</summary>
    </member>
    <member name="F:System.ServiceModel.Activities.Description.WorkflowUnhandledExceptionAction.AbandonAndSuspend">
      <summary>Abandons the workflow instance and marks the last persisted state as suspended. If there is no last persisted instance can be found, the workflow instance is aborted.</summary>
    </member>
    <member name="T:System.ServiceModel.Activities.Description.WorkflowUnhandledExceptionBehavior">
      <summary>A service behavior that enables you to specify the action to take when an unhandled exception occurs within a workflow service.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowUnhandledExceptionBehavior.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Description.WorkflowUnhandledExceptionBehavior" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Description.WorkflowUnhandledExceptionBehavior.Action">
      <summary>Gets or sets the action to take when an unhandled exception occurs.</summary>
      <returns>A <see cref="T:System.ServiceModel.Activities.Description.WorkflowUnhandledExceptionAction" /> object.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowUnhandledExceptionBehavior.AddBindingParameters(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase,System.Collections.ObjectModel.Collection{System.ServiceModel.Description.ServiceEndpoint},System.ServiceModel.Channels.BindingParameterCollection)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IServiceBehavior.AddBindingParameters(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase,System.Collections.ObjectModel.Collection{System.ServiceModel.Description.ServiceEndpoint},System.ServiceModel.Channels.BindingParameterCollection)" /> method to support the behavior.</summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The service host.</param>
      <param name="endpoints">The endpoints exposed by the service.</param>
      <param name="bindingParameters">The binding parameters required to implement the behavior.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowUnhandledExceptionBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IServiceBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)" /> method to support the behavior.</summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The service host.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Description.WorkflowUnhandledExceptionBehavior.Validate(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IServiceBehavior.Validate(System.ServiceModel.Description.ServiceDescription,System.ServiceModel.ServiceHostBase)" /> method to support the behavior.</summary>
      <param name="serviceDescription">The service description.</param>
      <param name="serviceHostBase">The service host.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.ReceiveMessageRecord">
      <summary>Contains the tracking record sent to a tracking participant by the run-time tracking infrastructure when a message is received by a workflow service instance.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.ReceiveMessageRecord.#ctor(System.ServiceModel.Activities.Tracking.ReceiveMessageRecord)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.ReceiveMessageRecord" /> class using the specified <see cref="T:System.ServiceModel.Activities.Tracking.ReceiveMessageRecord" /> instance.</summary>
      <param name="record">The tracking information.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.ReceiveMessageRecord.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.ReceiveMessageRecord" /> class using the specified name.</summary>
      <param name="name">The name of the tracking record.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.ReceiveMessageRecord.Clone">
      <summary>Creates a copy of the <see cref="T:System.ServiceModel.Activities.Tracking.ReceiveMessageRecord" />.</summary>
      <returns>A copy of <see cref="T:System.ServiceModel.Activities.Tracking.ReceiveMessageRecord" />.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.ReceiveMessageRecord.E2EActivityId">
      <summary>Gets the receiving end-to-end activity ID associated with this record.</summary>
      <returns>The activity ID.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.ReceiveMessageRecord.MessageId">
      <summary>Gets the message ID of the message associated with this record.</summary>
      <returns>The message ID.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.SendMessageRecord">
      <summary>Contains the tracking record sent to a tracking participant by the run-time tracking infrastructure when a message is sent by a workflow service instance.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.SendMessageRecord.#ctor(System.ServiceModel.Activities.Tracking.SendMessageRecord)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.SendMessageRecord" /> class using the specified <see cref="T:System.ServiceModel.Activities.Tracking.SendMessageRecord" /> instance.</summary>
      <param name="record">The tracking information.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.SendMessageRecord.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.SendMessageRecord" /> class using the specified name.</summary>
      <param name="name">The name of the tracking record.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.SendMessageRecord.Clone">
      <summary>Creates a copy of the <see cref="T:System.ServiceModel.Activities.Tracking.SendMessageRecord" />.</summary>
      <returns>A copy of the <see cref="T:System.ServiceModel.Activities.Tracking.SendMessageRecord" /> instance.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.SendMessageRecord.E2EActivityId">
      <summary>Gets the sending end-to-end activity ID associated with this record.</summary>
      <returns>The end-to-end activity ID.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.ActivityScheduledQueryElement">
      <summary>A configuration element that represents a <see cref="T:System.Activities.Tracking.ActivityScheduledQuery" />.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.ActivityScheduledQueryElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ActivityScheduledQueryElement" /> class. </summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ActivityScheduledQueryElement.ActivityName">
      <summary>Gets or sets the name of the activity to filter <see cref="T:System.Activities,Tracking.ActivityScheduledRecord" /> instances on. The default value is "*" (all).</summary>
      <returns>The name of the activity.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ActivityScheduledQueryElement.ChildActivityName">
      <summary>Gets or sets the name of the child activity to filter <see cref="T:System.Activities,Tracking.ActivityScheduledRecord" /> instances on. The default value is "*" (all). </summary>
      <returns>The name of the child activity.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.ActivityScheduledQueryElement.NewTrackingQuery">
      <summary>Creates the <see cref="T:System.Activities.Tracking.ActivityScheduledQuery" />. </summary>
      <returns>The activity-scheduled query.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ActivityScheduledQueryElement.Properties">
      <summary>Gets the properties contained in this configuration element.</summary>
      <returns>The properties contained in this configuration element.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.ActivityScheduledQueryElementCollection">
      <summary>A configuration element that contains <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ActivityScheduledQueryElement" /> elements.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.ActivityScheduledQueryElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ActivityScheduledQueryElementCollection" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ActivityScheduledQueryElementCollection.ElementName">
      <summary>Gets the configuration element name.</summary>
      <returns>The configuration element name.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElement">
      <summary>A configuration element that represents a <see cref="T:System.Activities.Tracking.ActivityStateQuery" />.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElement.ActivityName">
      <summary>Gets or sets the name of the activity to filter <see cref="T:System.Activities.Tracking.ActivityStateRecord" /> instances on. </summary>
      <returns>The name of the activity.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElement.Arguments">
      <summary>Gets an <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ArgumentElementCollection" /> that contains the variables to filter <see cref="T:System.Activities.Tracking.ActivityStateRecord" /> on.</summary>
      <returns>The collection of arguments.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElement.NewTrackingQuery">
      <summary>Creates a new <see cref="T:System.Activities.Tracking.ActivityStateQuery" />.</summary>
      <returns>The activity state query.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElement.Properties">
      <summary>Gets a <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> that contains the properties of the configuration element.</summary>
      <returns>The configuration element properties.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElement.States">
      <summary>Gets a <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.StateElementCollection" /> that contains the states to filter <see cref="T:System.Activities.Tracking.ActivityStateRecord" /> on.</summary>
      <returns>The collection of states. </returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElement.Variables">
      <summary>Gets a <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.VariableElementCollection" /> that contains the variables to filter <see cref="T:System.Activities.Tracking.ActivityStateRecord" /> on.</summary>
      <returns>The collection of variables.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElementCollection">
      <summary>A collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationElement" /> elements that contains <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElement" /> elements.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElementCollection" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElementCollection.ElementName">
      <summary>Gets the configuration element name.</summary>
      <returns>The configuration element name.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.AnnotationElement">
      <summary>A configuration element that represents an annotation that is added to an emitted tracking record.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.AnnotationElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.AnnotationElement" /> class. </summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.AnnotationElement.ElementKey">
      <summary>Gets a key that uniquely identifies the annotation.</summary>
      <returns>The element key.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.AnnotationElement.Name">
      <summary>Gets or sets the name of the annotation.</summary>
      <returns>The name of the annotation.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.AnnotationElement.Properties">
      <summary>Gets a <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> that contains the properties for the configuration element.</summary>
      <returns>The collection of properties.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.AnnotationElement.Value">
      <summary>Gets or sets the value of the annotation.</summary>
      <returns>The value of the annotation.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.AnnotationElementCollection">
      <summary>A collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationElement" /> elements that contains <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.AnnotationElement" /> elements.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.AnnotationElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.AnnotationElementCollection" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.AnnotationElementCollection.ElementName">
      <summary>Gets the configuration element name.</summary>
      <returns>The configuration element name.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.ArgumentElement">
      <summary>A configuration element that represents an argument .</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.ArgumentElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ArgumentElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ArgumentElement.ElementKey">
      <summary>Gets a key that uniquely identifies the argument.</summary>
      <returns>The element key.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ArgumentElement.Name">
      <summary>Gets or sets the name of the argument.</summary>
      <returns>The argument name.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ArgumentElement.Properties">
      <summary>Gets the collection of properties</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> of properties for the element.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.ArgumentElementCollection">
      <summary>A configuration element that contains a collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ArgumentElement" /> elements.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.ArgumentElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ArgumentElementCollection" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ArgumentElementCollection.ElementName">
      <summary>Gets the configuration element name.</summary>
      <returns>The configuration element name.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.BookmarkResumptionQueryElement">
      <summary>A configuration element that represents a <see cref="T:System.Activities.Tracking.BookmarkResumptionQuery" />.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.BookmarkResumptionQueryElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.BookmarkResumptionQueryElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.BookmarkResumptionQueryElement.Name">
      <summary>Gets or sets the name of the bookmark.</summary>
      <returns>The bookmark name.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.BookmarkResumptionQueryElement.NewTrackingQuery">
      <summary>Creates the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.BookmarkResumptionQuery" />. </summary>
      <returns>The bookmark resumption query.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.BookmarkResumptionQueryElement.Properties">
      <summary>Gets a <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> that contains the properties associated with this element.</summary>
      <returns>A collection of properties.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.BookmarkResumptionQueryElementCollection">
      <summary>A configuration element that contains a collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.BookmarkResumptionQueryElement" /> elements.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.BookmarkResumptionQueryElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.BookmarkResumptionQueryElementCollection" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.BookmarkResumptionQueryElementCollection.ElementName">
      <summary>Gets the configuration element name.</summary>
      <returns>The configuration element name.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.CancelRequestedQueryElement">
      <summary>A configuration element that represents a <see cref="T:System.Activities.TrackingCancelRequestedQuery" />.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.CancelRequestedQueryElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.CancelRequestedQueryElement" />.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.CancelRequestedQueryElement.ActivityName">
      <summary>Gets or sets the name of the activity to subscribe for the <see cref="T:System.Activities.Tracking.CancelRequestedRecord" /> from the specified activity that issued the cancel request.</summary>
      <returns>The activity name to subscribe for <see cref="T:System.Activities.Tracking.CancelRequestedRecord" /> on.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.CancelRequestedQueryElement.ChildActivityName">
      <summary>Gets or sets the name of the child activity to subscribe for the <see cref="T:System.Activities.Tracking.CancelRequestedRecord" /> activity name to be canceled.</summary>
      <returns>The name of the child activity to subscribe for <see cref="T:System.Activities.Tracking.CancelRequestedRecord" />.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.CancelRequestedQueryElement.NewTrackingQuery">
      <summary>Creates a new <see cref="T:System.Activities.Tracking.CancelRequestedQuery" />.</summary>
      <returns>A <see cref="T:System.Activities.Tracking.CancelRequestedQuery" /> instance.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.CancelRequestedQueryElement.Properties">
      <summary>Gets a <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> that contains the properties associated with this element.</summary>
      <returns>A collection of properties.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.CancelRequestedQueryElementCollection">
      <summary>A configuration element that contains <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.CancelRequestedQueryElement" /> elements.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.CancelRequestedQueryElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.CancelRequestedQueryElementCollection" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.CancelRequestedQueryElementCollection.ElementName">
      <summary>Gets the configuration element name.</summary>
      <returns>The configuration element name.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.CustomTrackingQueryElement">
      <summary>A configuration element that represents a <see cref="T:System.Activities.Tracking.CustomTrackingQuery" />.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.CustomTrackingQueryElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.CustomTrackingQueryElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.CustomTrackingQueryElement.ActivityName">
      <summary>Gets or sets the name of the activity to filter <see cref="T:System.Activities.Tracking.CustomTrackingRecord" /> instances on. The default value is “*” (all).</summary>
      <returns>The name of the activity to filter on.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.CustomTrackingQueryElement.Name">
      <summary>Gets or sets the name of the activity for which to query for tracking records.</summary>
      <returns>The activity name.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.CustomTrackingQueryElement.NewTrackingQuery">
      <summary>Creates a new <see cref="T:System.Activities.Tracking.CustomTrackingQuery" />.</summary>
      <returns>A new <see cref="T:System.Activities.Tracking.CustomTrackingQuery" /> instance.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.CustomTrackingQueryElement.Properties">
      <summary>Gets a <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> that contains the properties associated with this element.</summary>
      <returns>The property collection.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.CustomTrackingQueryElementCollection">
      <summary>A configuration element that contains <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.CustomTrackingQueryElement" /> elements.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.CustomTrackingQueryElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.CustomTrackingQueryElementCollection" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.CustomTrackingQueryElementCollection.ElementName">
      <summary>Gets the configuration element name.</summary>
      <returns>The configuration element name.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.FaultPropagationQueryElement">
      <summary>A configuration element that represents a <see cref="T:System.Activities.Tracking.FaultPropagationQuery" />.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.FaultPropagationQueryElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.FaultPropagationQueryElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.FaultPropagationQueryElement.FaultHandlerActivityName">
      <summary>Gets or sets the name of the activity that handles the fault that is used to subscribe for <see cref="T:System.Activities.Tracking.FaultPropagationRecord" /> instances. </summary>
      <returns>The activity name to filter on.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.FaultPropagationQueryElement.FaultSourceActivityName">
      <summary>Gets or sets the name of the activity that generates the fault that is used to subscribe for <see cref="T:System.Activities.Tracking.FaultPropagationRecord" /> instances. </summary>
      <returns>The activity name to filter on.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.FaultPropagationQueryElement.NewTrackingQuery">
      <summary>Creates a new <see cref="T:System.Activities.Tracking.FaultPropagationQuery" /> instance.</summary>
      <returns>The fault propagation query.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.FaultPropagationQueryElement.Properties">
      <summary>Gets the <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> that contains the properties associated with this element.</summary>
      <returns>The property collection.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.FaultPropagationQueryElementCollection">
      <summary>A collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationElement" /> elements that contains <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.FaultPropagationQueryElement" /> elements.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.FaultPropagationQueryElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.FaultPropagationQueryElementCollection" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.FaultPropagationQueryElementCollection.ElementName">
      <summary>Gets the configuration element name.</summary>
      <returns>The configuration element name.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.ProfileElement">
      <summary>A configuration element that represents a <see cref="T:System.Activities.Tracking.TrackingProfile" /> and is used for adding a profile through the behavior element.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.ProfileElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ProfileElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileElement.ElementKey">
      <summary>Gets a key that uniquely identifies the profile.</summary>
      <returns>The element key.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileElement.ImplementationVisibility">
      <summary>Gets or sets the visibility mode of the tracking profile.</summary>
      <returns>The visibility mode of the tracking profile</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileElement.Name">
      <summary>Gets or sets the name of the tracking profile.</summary>
      <returns>The name of the tracking profile. </returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileElement.Properties">
      <summary>Gets a <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> that contains the properties associated with this element.</summary>
      <returns>The properties collection.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileElement.Workflows">
      <summary>Gets the collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement" /> elements associated with this <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ProfileElement" />.</summary>
      <returns>A collection of profile workflow query elements.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.ProfileElementCollection">
      <summary>A configuration element that contains a collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ProfileElement" /> elements.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileElementCollection.CollectionType">
      <summary>Gets a value that specifies the type of collection contained within this element.</summary>
      <returns>One of the <see cref="T:System.Configuration.ConfigurationElementCollectionType" /> enumeration values.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement">
      <summary>A configuration element that contains all queries for a specific workflow identified by the <see cref="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement.ActivityDefinitionId" /> property.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement.ActivityDefinitionId">
      <summary>Gets or sets the activity definition ID of the workflow being tracked.</summary>
      <returns>The activity definition ID</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement.ActivityScheduledQueries">
      <summary>Gets a collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ActivityScheduledQueryElement" /> elements.</summary>
      <returns>A collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ActivityScheduledQueryElement" /> elements.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement.ActivityStateQueries">
      <summary>Gets a collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElement" /> elements.</summary>
      <returns>A <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ActivityStateQueryElementCollection" /> object.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement.BookmarkResumptionQueries">
      <summary>Gets a collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.BookmarkResumptionQueryElement" /> elements.</summary>
      <returns>A collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.BookmarkResumptionQueryElement" /> elements.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement.CancelRequestedQueries">
      <summary>Gets a collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.CancelRequestedQueryElement" /> elements.</summary>
      <returns>A collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.CancelRequestedQueryElement" /> elements.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement.CustomTrackingQueries">
      <summary>Gets a collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.CustomTrackingQueryElement" /> elements.</summary>
      <returns>A <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.CustomTrackingQueryElementCollection" /> object.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement.ElementKey">
      <summary>Gets a key that uniquely identifies the workflow being tracked.</summary>
      <returns>The element key.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement.FaultPropagationQueries">
      <summary>Gets a collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.FaultPropagationQueryElement" /> elements.</summary>
      <returns>A collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.FaultPropagationQueryElement" /> elements. </returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement.Properties">
      <summary>Gets a collection of <see cref="T:System.Configuration.ConfigurationProperty" /> properties.</summary>
      <returns>A <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> object.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement.StateMachineStateQueries">
      <summary>Gets a collection of StateMachineStateQueryElement configuration in DotNetConfig.xsd file.</summary>
      <returns>A collection of StateMachineStateQueryElement configuration in DotNetConfig.xsd file.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement.WorkflowInstanceQueries">
      <summary>Gets a collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.WorkflowInstanceQueryElement" /> elements.</summary>
      <returns>A <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.WorkflowInstanceQueryElementCollection" /> object.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElementCollection">
      <summary>A collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationElement" /> elements that contains a collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElement" /> elements.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElementCollection" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.ProfileWorkflowElementCollection.ElementName">
      <summary>Gets the configuration element name.</summary>
      <returns>The configuration element name.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.StateElement">
      <summary>A configuration element that specifies workflow instance state or activity instance state the user subscribes tracking information for. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.StateElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.StateElement" /> class. </summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.StateElement.ElementKey">
      <summary>Gets a key that uniquely identifies the element query.</summary>
      <returns>The element key.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.StateElement.Name">
      <summary>Gets or sets the state name to filter on.</summary>
      <returns>The state name to filter on. The default value is “*” (all).</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.StateElement.Properties">
      <summary>Gets a collection of <see cref="T:System.Configuration.ConfigurationProperty" /> properties.</summary>
      <returns>A collection of configuration properties.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.StateElementCollection">
      <summary>A configuration element that contains a collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.StateElement" /> elements.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.StateElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.StateElementCollection" /> class.</summary>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.StateMachineStateQueryElement">
      <summary>Represents a configuration StateMachineStateQuery element in DotNetConfig.xsd.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.StateMachineStateQueryElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.StateMachineStateQueryElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.StateMachineStateQueryElement.ActivityName">
      <summary> Gets or sets the activity name filter attribute in StateMachineTrackingQuery element. </summary>
      <returns>The activity name filter attribute in StateMachineTrackingQuery element.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.StateMachineStateQueryElement.NewTrackingQuery">
      <summary> Creates a StateMachine-specific CustomTrackingQuery, if the user specifies StateMachineTrackingQuery element in app.config. </summary>
      <returns> A CustomTrackingQuery instance that tracks StateMachine specific TrackingRecord. </returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.StateMachineStateQueryElement.Properties">
      <summary> Gets the attributes in StateMachineTrackingQuery element. </summary>
      <returns>The attributes in StateMachineTrackingQuery element.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.StateMachineStateQueryElementCollection">
      <summary>Represents a collection of StateMachineStateQueryElement configuration in DotNetConfig.xsd file. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.StateMachineStateQueryElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.StateMachineStateQueryElementCollection" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.StateMachineStateQueryElementCollection.ElementName">
      <summary> Gets the name of StateMachineTrackingQuery element in the DotNetConfig.xsd file.</summary>
      <returns>The name of StateMachineTrackingQuery element in the DotNetConfig.xsd file.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationCollection`1">
      <summary>The base class for all tracking configuration collections.</summary>
      <typeparam name="TConfigurationElement">The type of configuration element.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationCollection`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationCollection`1" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationCollection`1.Add(`0)">
      <summary>Adds an element into the collection.</summary>
      <param name="element">The element to add.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationCollection`1.Clear">
      <summary>Clears the contents of the collection.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationCollection`1.CollectionType">
      <summary>Gets or sets the collection type.</summary>
      <returns>One of the <see cref="T:System.Configuration.ConfigurationElementCollectionType" /> enumeration values.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationCollection`1.CreateNewElement">
      <summary>Creates a new element.</summary>
      <returns>A new configuration element.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationCollection`1.GetElementKey(System.Configuration.ConfigurationElement)">
      <summary>Gets the element key for the specified configuration element.</summary>
      <returns>The element key.</returns>
      <param name="element">The configuration element.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationCollection`1.IndexOf(`0)">
      <summary>Gets the index of the specified configuration element.</summary>
      <returns>The zero-based index of the configuration element.</returns>
      <param name="element">The configuration element.</param>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationCollection`1.Item(System.Int32)">
      <summary>Gets the element as the specified index.</summary>
      <returns>The element at the specified index.</returns>
      <param name="index">The index of the element to retrieve.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationCollection`1.Remove(`0)">
      <summary>Removes the specified configuration element.</summary>
      <param name="element">The configuration element to remove.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationCollection`1.RemoveAt(System.Int32)">
      <summary>Removes the configuration element at the specified index.</summary>
      <param name="index">The index of the configuration element to remove.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationElement">
      <summary>The base class for tracking configuration elements.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationElement" /> class. </summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationElement.ElementKey">
      <summary>Gets a key that uniquely identifies the configuration element.</summary>
      <returns>The element key.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationElement.GetStringPairKey(System.String,System.String)">
      <summary>Merges the two specified strings.</summary>
      <returns>A string that contains both strings.</returns>
      <param name="value1">The first string.</param>
      <param name="value2">The second string.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.TrackingQueryElement">
      <summary>A configuration element that represents a <see cref="T:System.Activities.Tracking.TrackingQuery" /> instance when specifying a tracking profile.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.TrackingQueryElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.TrackingQueryElement" /> class. </summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.TrackingQueryElement.Annotations">
      <summary>Gets a collection of annotations associated with the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.TrackingQueryElement" />.</summary>
      <returns>A collection of annotations.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.TrackingQueryElement.ElementKey">
      <summary>A key that uniquely identifies the tracking query.</summary>
      <returns>The element key.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.TrackingQueryElement.NewTrackingQuery">
      <summary>Override this method to create a query instance and set properties not inherited by derived classes.</summary>
      <returns>A <see cref="T:System.Activities.Tracking.TrackingQuery" /> instance.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.TrackingQueryElement.Properties">
      <summary>Gets the collection of properties.</summary>
      <returns>The collection of properties for the element.</returns>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.TrackingQueryElement.UpdateTrackingQuery(System.Activities.Tracking.TrackingQuery)">
      <summary>Override this method to set the properties that a derived class may inherit. </summary>
      <param name="trackingQuery">The tracking query.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.TrackingSection">
      <summary>A configuration section for specifying tracking settings.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.TrackingSection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.TrackingSection" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.TrackingSection.Profiles">
      <summary>Gets a collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.ProfileElement" /> elements.</summary>
      <returns>A collection of elements.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.TrackingSection.Properties">
      <summary>Gets the collection of properties.</summary>
      <returns>The collection of properties for the element.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.TrackingSection.TrackingProfiles">
      <summary>Gets a collection of <see cref="T:System.Activities.Tracking.TrackingProfile" /> objects.</summary>
      <returns>A collection of <see cref="T:System.Activities.Tracking.TrackingProfile" /> objects.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.VariableElement">
      <summary>A configuration element that specifies the variables to be extracted from an activity. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.VariableElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.VariableElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.VariableElement.ElementKey">
      <summary>Gets a key that uniquely identifies the variable query.</summary>
      <returns>The element key.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.VariableElement.Name">
      <summary>Gets or sets the name of the variable to be extracted.</summary>
      <returns>The variable name.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.VariableElement.Properties">
      <summary>Gets the collection of properties.</summary>
      <returns>The collection of properties for the element.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.VariableElementCollection">
      <summary>A configuration element that contains a collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.VariableElement" /> elements.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.VariableElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.VariableElementCollection" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.VariableElementCollection.ElementName">
      <summary>Gets the configuration element name.</summary>
      <returns>The configuration element name.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.WorkflowInstanceQueryElement">
      <summary>A configuration element that represents a <see cref="T:System.Activities.Tracking.WorkflowInstanceQuery" />. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.WorkflowInstanceQueryElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.WorkflowInstanceQueryElement" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.WorkflowInstanceQueryElement.NewTrackingQuery">
      <summary>Creates a new <see cref="T:System.Activities.Tracking.WorkflowInstanceQuery" /> instance.</summary>
      <returns>A new workflow instance query.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.WorkflowInstanceQueryElement.Properties">
      <summary>Gets the collection of properties.</summary>
      <returns>The collection of properties for the element.</returns>
    </member>
    <member name="P:System.ServiceModel.Activities.Tracking.Configuration.WorkflowInstanceQueryElement.States">
      <summary>The workflow states that the query filters on.</summary>
      <returns>The states to filter on.</returns>
    </member>
    <member name="T:System.ServiceModel.Activities.Tracking.Configuration.WorkflowInstanceQueryElementCollection">
      <summary>A collection of <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.TrackingConfigurationElement" /> elements that contains <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.WorkflowInstanceQueryElement" /> elements.</summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Tracking.Configuration.WorkflowInstanceQueryElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Tracking.Configuration.WorkflowInstanceQueryElementCollection" /> class.</summary>
    </member>
    <member name="T:System.ServiceModel.XamlIntegration.EndpointIdentityConverter">
      <summary>Converts an <see cref="T:System.ServiceModel.EndpointIdentity" /> instance to the specified type. </summary>
    </member>
    <member name="M:System.ServiceModel.XamlIntegration.EndpointIdentityConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.XamlIntegration.EndpointIdentityConverter" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.XamlIntegration.EndpointIdentityConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Gets a value that determines whether the converter can convert an <see cref="T:System.ServiceModel.EndpointIdentity" /> instance to the specified type.</summary>
      <returns>true if the converter can perform the conversion requested; otherwise false.</returns>
      <param name="context">A type descriptor context that provides a format context.</param>
      <param name="destinationType">The type you want to convert to.</param>
    </member>
    <member name="M:System.ServiceModel.XamlIntegration.EndpointIdentityConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts the given object to the specified type, using the specified context and culture information.</summary>
      <returns>The converted value.</returns>
      <param name="context">A <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
      <param name="culture">The culture information. If null is passed, the current culture is assumed.</param>
      <param name="value">The object to convert.</param>
      <param name="destinationType">The type to convert the <paramref name="value" /> parameter to.</param>
    </member>
    <member name="T:System.ServiceModel.XamlIntegration.ServiceXNameTypeConverter">
      <summary>A WCF-specific implementation of a <see cref="T:System.Xml.Linq.XName" /> type converter that does not require <see cref="T:System.Xml.Linq.XName" /> instances to specify a namespace.</summary>
    </member>
    <member name="M:System.ServiceModel.XamlIntegration.ServiceXNameTypeConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.XamlIntegration.ServiceXNameTypeConverter" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.XamlIntegration.ServiceXNameTypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Gets a value that indicates whether this converter can convert an object of one type to an <see cref="T:System.Xml.Linq.XName" />.  </summary>
      <returns>true if the converter can perform the conversion; otherwise, false.</returns>
      <param name="context">The type descriptor context</param>
      <param name="sourceType">The type to convert from.</param>
    </member>
    <member name="M:System.ServiceModel.XamlIntegration.ServiceXNameTypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Gets a value that indicates whether this converter can convert an <see cref="T:System.Xml.Linq.XName" /> instance to an object of the specified type.</summary>
      <returns>true if the converter can perform the conversion; otherwise, false.</returns>
      <param name="context">The type descriptor context.</param>
      <param name="destinationType">The type to convert to.</param>
    </member>
    <member name="M:System.ServiceModel.XamlIntegration.ServiceXNameTypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts the specified value to a <see cref="T:System.Xml.Linq.XName" /> instance.</summary>
      <returns>The converted value.</returns>
      <param name="context">The type descriptor context.</param>
      <param name="culture">The current culture.</param>
      <param name="value">The object to convert.</param>
    </member>
    <member name="M:System.ServiceModel.XamlIntegration.ServiceXNameTypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts a <see cref="T:System.Xml.Linq.XName" /> instance to an object of the specified type.</summary>
      <returns>The converted value.</returns>
      <param name="context">The type descriptor context.</param>
      <param name="culture">The current culture.</param>
      <param name="value">The value to convert.</param>
      <param name="destinationType">The type to convert the value to.</param>
    </member>
    <member name="T:System.ServiceModel.XamlIntegration.SpnEndpointIdentityExtension">
      <summary>A XAML markup extension for the <see cref="T:System.ServiceModel.SpnEndpointIdentity" /> class.  </summary>
    </member>
    <member name="M:System.ServiceModel.XamlIntegration.SpnEndpointIdentityExtension.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.SpnEndpointIdentityExtension" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.XamlIntegration.SpnEndpointIdentityExtension.#ctor(System.ServiceModel.SpnEndpointIdentity)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.SpnEndpointIdentityExtension" /> class. </summary>
      <param name="identity">The service principal name (SPN) endpoint identity.</param>
    </member>
    <member name="M:System.ServiceModel.XamlIntegration.SpnEndpointIdentityExtension.ProvideValue(System.IServiceProvider)">
      <summary>Provides the object that the markup extension represents. </summary>
      <returns>The object that the markup extension represents.</returns>
      <param name="serviceProvider">The service provider.</param>
    </member>
    <member name="P:System.ServiceModel.XamlIntegration.SpnEndpointIdentityExtension.SpnName">
      <summary>Gets or sets the service principle name.</summary>
      <returns>The service principle name.</returns>
    </member>
    <member name="T:System.ServiceModel.XamlIntegration.UpnEndpointIdentityExtension">
      <summary>A XAML markup extension for the <see cref="T:System.ServiceModel.UpnEndpointIdentity" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.XamlIntegration.UpnEndpointIdentityExtension.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.XamlIntegration.UpnEndpointIdentityExtension" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.XamlIntegration.UpnEndpointIdentityExtension.#ctor(System.ServiceModel.UpnEndpointIdentity)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.XamlIntegration.UpnEndpointIdentityExtension" /> class with the specified <see cref="T:System.ServiceModel.UpnEndpointIdentity" />.</summary>
      <param name="identity">The User Principal Name (UPN) endpoint identity.</param>
    </member>
    <member name="M:System.ServiceModel.XamlIntegration.UpnEndpointIdentityExtension.ProvideValue(System.IServiceProvider)">
      <summary>Provides the object that the markup extension represents.</summary>
      <returns>The object that the markup extension represents.</returns>
      <param name="serviceProvider">The service provider.</param>
    </member>
    <member name="P:System.ServiceModel.XamlIntegration.UpnEndpointIdentityExtension.UpnName">
      <summary>Gets or sets the user principle name.</summary>
      <returns>The user principle name.</returns>
    </member>
  </members>
</doc>