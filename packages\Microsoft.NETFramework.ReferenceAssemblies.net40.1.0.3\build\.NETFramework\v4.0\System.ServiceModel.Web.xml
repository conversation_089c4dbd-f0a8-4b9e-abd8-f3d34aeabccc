﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ServiceModel.Web</name>
  </assembly>
  <members>
    <member name="T:System.ServiceModel.WebHttpBinding">
      <summary>A binding used to configure endpoints for  Web services that are exposed through HTTP requests instead of SOAP messages.</summary>
    </member>
    <member name="M:System.ServiceModel.WebHttpBinding.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.WebHttpBinding" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.WebHttpBinding.#ctor(System.ServiceModel.WebHttpSecurityMode)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.WebHttpBinding" /> class with the type of security used by the binding explicitly specified.</summary>
      <param name="securityMode">The value of <see cref="T:System.ServiceModel.WebHttpSecurityMode" /> that specifies the type of security that is used to configure a service endpoint to receive HTTP requests.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="securityMode" /> specified is not a valid <see cref="T:System.ServiceModel.WebHttpSecurityMode" />.</exception>
    </member>
    <member name="M:System.ServiceModel.WebHttpBinding.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.WebHttpBinding" /> class with a binding specified by its configuration name.</summary>
      <param name="configurationName">The binding configuration name for the <see cref="T:System.ServiceModel.Configuration.WebHttpBindingElement" />.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The binding element with the name <paramref name="configurationName" /> was not found.</exception>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.AllowCookies">
      <summary>Gets or sets a value that indicates whether the client accepts cookies and propagates them on future requests.</summary>
      <returns>true if cookies are allowed; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.BypassProxyOnLocal">
      <summary>Gets or sets a value that indicates whether to bypass the proxy server for local addresses.</summary>
      <returns>true to bypass the proxy server for local addresses; otherwise, false. The default value is false.</returns>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.ContentTypeMapper">
      <summary>Gets or sets the content type mapper.</summary>
      <returns>The content type mapper.</returns>
    </member>
    <member name="M:System.ServiceModel.WebHttpBinding.CreateBindingElements">
      <summary>Returns an ordered collection of binding elements contained in the current binding.</summary>
      <returns>A <see cref="T:System.ServiceModel.Channels.BindingElementCollection" /> that contains the <see cref="T:System.ServiceModel.Channels.BindingElement" /> objects for the binding.</returns>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.CrossDomainScriptAccessEnabled">
      <summary>Gets or sets a value that determines if cross domain script access is enabled.</summary>
      <returns>true if cross domain scripting is enabled; otherwise false.</returns>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.EnvelopeVersion">
      <summary>Gets the envelope version that is used by endpoints that are configured by this binding to receive HTTP requests.</summary>
      <returns>The <see cref="T:System.ServiceModel.EnvelopeVersion" /> with the <see cref="P:System.ServiceModel.EnvelopeVersion.None" /> property that is used with endpoints configured with this binding to receive HTTP requests. </returns>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.HostNameComparisonMode">
      <summary>Gets or sets a value that indicates whether the hostname is used to reach the service when matching the URI.</summary>
      <returns>The <see cref="T:System.ServiceModel.HostnameComparisonMode" /> value that indicates whether the hostname is used to reach the service when matching on the URI. The default value is <see cref="F:System.ServiceModel.HostnameComparisonMode.StrongWildcard" />, which ignores the hostname in the match.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value set is not a valid <see cref="T:System.ServiceModel.HostnameComparisonMode" />      value.</exception>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.MaxBufferPoolSize">
      <summary>Gets or sets the maximum amount of memory allocated for the buffer manager that manages the buffers required by endpoints that use this binding.</summary>
      <returns>The maximum size, in bytes, for the pool of buffers used by an endpoint configured with this binding. The default value is 65,536 bytes.</returns>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.MaxBufferSize">
      <summary>Gets or sets the maximum amount of memory that is allocated for use by the manager of the message buffers that receive messages from the channel.</summary>
      <returns>The maximum amount of memory, in bytes, available for use by the message buffer manager. The default value is 524,288 (0x80000) bytes.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value set is less than or equal to zero.</exception>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.MaxReceivedMessageSize">
      <summary>Gets or sets the maximum size for a message that can be processed by the binding.</summary>
      <returns>The maximum size, in bytes, for a message that is processed by the binding. The default value is 65,536 bytes.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value is less than zero.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">A message exceeded the maximum size allocated.</exception>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.ProxyAddress">
      <summary>Gets or sets the URI address of the HTTP proxy.</summary>
      <returns>A <see cref="T:System.Uri" /> that serves as the address of the HTTP proxy. The default value is null.</returns>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.ReaderQuotas">
      <summary>Gets or sets constraints on the complexity of SOAP messages that can be processed by endpoints configured with this binding.</summary>
      <returns>The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> that specifies the complexity constraints.</returns>
      <exception cref="T:System.ArgumentNullException">The value set is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The quota values of <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> are read only.</exception>
      <exception cref="T:System.ArgumentException">The quotas set must be positive.</exception>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.Scheme">
      <summary>Gets the URI transport scheme for the channels and listeners that are configured with this binding.</summary>
      <returns>"https" if the <see cref="P:System.ServiceModel.WebHttpBinding.Security" /> is set to <see cref="F:System.ServiceModel.WebHttpSecurityMode.Transport" />; "http" if it is set to <see cref="F:System.ServiceModel.WebHttpSecurityMode.None" />.</returns>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.Security">
      <summary>Gets the security settings used with this binding.  </summary>
      <returns>The <see cref="T:System.ServiceModel.WebHttpSecurity" /> that is used with this binding. The default value is <see cref="F:System.ServiceModel.WebHttpSecurityMode.None" />. </returns>
    </member>
    <member name="M:System.ServiceModel.WebHttpBinding.ShouldSerializeReaderQuotas">
      <summary>Determines if reader quotas should be serialized.</summary>
      <returns>true if reader quotas should be serialized; otherwise false.</returns>
    </member>
    <member name="M:System.ServiceModel.WebHttpBinding.ShouldSerializeSecurity">
      <summary>Determines if security settings should be serialized.</summary>
      <returns>true if security settings should be serialized; otherwise false.</returns>
    </member>
    <member name="M:System.ServiceModel.WebHttpBinding.ShouldSerializeWriteEncoding">
      <summary>Determines if the encoding used for serialization should be serialized.</summary>
      <returns>true if the encoding should be serialized; otherwise false.</returns>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.System#ServiceModel#Channels#IBindingRuntimePreferences#ReceiveSynchronously">
      <summary>Gets a value that indicates whether incoming requests are handled synchronously or asynchronously.</summary>
      <returns>true if incoming requests are handled synchronously; false if incoming requests are handled asynchronously. The default value is false.</returns>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.TransferMode">
      <summary>Gets or sets a value that indicates whether the service configured with the binding uses streamed or buffered (or both) modes of message transfer.</summary>
      <returns>The <see cref="T:System.ServiceModel.TransferMode" /> value that indicates whether the service configured with the binding uses streamed or buffered (or both) modes of message transfer. The default value is <see cref="F:System.ServiceModel.TransferMode.Buffered" />.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value set is not a valid <see cref="T:System.ServiceModel.TransferMode" /> value.</exception>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.UseDefaultWebProxy">
      <summary>Gets or sets a value that indicates whether the auto-configured HTTP proxy of the system should be used, if available.</summary>
      <returns>true if the auto-configured HTTP proxy of the system should be used, if available; otherwise, false. The default value is true.  </returns>
    </member>
    <member name="P:System.ServiceModel.WebHttpBinding.WriteEncoding">
      <summary>Gets or sets the character encoding that is used for the message text.</summary>
      <returns>The <see cref="T:System.Text.Encoding" /> that indicates the character encoding that is used. The default is <see cref="T:System.Text.UTF8Encoding" />.</returns>
      <exception cref="T:System.ArgumentNullException">The value set is null.</exception>
    </member>
    <member name="T:System.ServiceModel.WebHttpSecurity">
      <summary>Specifies the types of security available to a service endpoint configured to receive HTTP requests.</summary>
    </member>
    <member name="M:System.ServiceModel.WebHttpSecurity.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.WebHttpSecurity" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.WebHttpSecurity.Mode">
      <summary>Gets or sets the mode of security that is used by an endpoint configured to receive HTTP requests with a <see cref="T:System.ServiceModel.WebHttpBinding" />.</summary>
      <returns>A value of the <see cref="T:System.ServiceModel.WebHttpSecurityMode" /> that indicates whether transport-level security or no security is used by an endpoint. The default value is <see cref="F:System.ServiceModel.WebHttpSecurityMode.None" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value is not a valid <see cref="T:System.ServiceModel.WebHttpSecurityMode" />.</exception>
    </member>
    <member name="M:System.ServiceModel.WebHttpSecurity.ShouldSerializeMode">
      <summary>Specifies whether the <see cref="P:System.ServiceModel.WebHttpSecurity.Mode" /> property has changed from its default and should be serialized. This is used for XAML integration.</summary>
      <returns>true if the <see cref="P:System.ServiceModel.WebHttpSecurity.Mode" /> property value should be serialized; otherwise, false.</returns>
    </member>
    <member name="M:System.ServiceModel.WebHttpSecurity.ShouldSerializeTransport">
      <summary>Returns a value that indicates whether the Transport property has changed from its default value and should be serialized. This is used by WCF for XAML integration.</summary>
      <returns>true if the <see cref="P:System.ServiceModel.WebHttpSecurity.Transport" /> property value should be serialized; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceModel.WebHttpSecurity.Transport">
      <summary>Gets an object that contains the transport-level security settings for this binding.</summary>
      <returns>The <see cref="T:System.ServiceModel.HttpTransportSecurity" /> for this binding. The default values set are a <see cref="P:System.ServiceModel.HttpTransportSecurity.ClientCredentialType" /> of <see cref="F:System.ServiceModel.HttpClientCredentialType.None" />, a <see cref="P:System.ServiceModel.HttpTransportSecurity.ProxyCredentialType" /> of <see cref="F:System.ServiceModel.HttpProxyCredentialType.None" />, and <see cref="P:System.ServiceModel.HttpTransportSecurity.Realm" /> = "".</returns>
    </member>
    <member name="T:System.ServiceModel.WebHttpSecurityMode">
      <summary>Defines the modes of security that can be used to configure a service endpoint to receive HTTP requests.</summary>
    </member>
    <member name="F:System.ServiceModel.WebHttpSecurityMode.None">
      <summary>Indicates no security is used with HTTP requests.</summary>
    </member>
    <member name="F:System.ServiceModel.WebHttpSecurityMode.Transport">
      <summary>Indicates that transport-level security is used with HTTP requests.</summary>
    </member>
    <member name="F:System.ServiceModel.WebHttpSecurityMode.TransportCredentialOnly">
      <summary>Indicates that only HTTP-based client authentication is provided.</summary>
    </member>
    <member name="T:System.ServiceModel.Activation.WebScriptServiceHostFactory">
      <summary>Automatically adds an ASP.NET AJAX endpoint to a service, without requiring configuration, in a managed hosting environment that dynamically activates host instances for the service in response to incoming messages. </summary>
    </member>
    <member name="M:System.ServiceModel.Activation.WebScriptServiceHostFactory.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activation.WebScriptServiceHostFactory" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Activation.WebScriptServiceHostFactory.CreateServiceHost(System.Type,System.Uri[])">
      <summary>Creates a derived class of <see cref="T:System.ServiceModel.ServiceHost" /> for a specified type of service with a specific base address that can be used to automatically enable ASP.NET AJAX endpoints in certain scenarios.</summary>
      <returns>A <see cref="T:System.ServiceModel.ServiceHost" /> for the type of service specified with the specified base address.</returns>
      <param name="serviceType">The type of service to host. </param>
      <param name="baseAddresses">The <see cref="T:System.Array" /> of type <see cref="T:System.Uri" /> that contains the base addresses for the service hosted.</param>
      <exception cref="T:System.InvalidOperationException">Another service uses the same base address, or another endpoint is using the same address as the ASP.NET AJAX endpoint that this factory is trying to create.</exception>
    </member>
    <member name="T:System.ServiceModel.Activation.WebServiceHostFactory">
      <summary>A factory that provides instances of <see cref="T:System.ServiceModel.Web.WebServiceHost" /> in managed hosting environments where the host instance is created dynamically in response to incoming messages.</summary>
    </member>
    <member name="M:System.ServiceModel.Activation.WebServiceHostFactory.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activation.WebServiceHostFactory" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Activation.WebServiceHostFactory.CreateServiceHost(System.Type,System.Uri[])">
      <summary>Creates an instance of the specified <see cref="T:System.ServiceModel.Web.WebServiceHost" /> derived class with the specified base addresses.</summary>
      <returns>An instance of a <see cref="T:System.ServiceModel.ServiceHost" /> derived class.</returns>
      <param name="serviceType">The type of service host to create.</param>
      <param name="baseAddresses">An array of base addresses for the service.</param>
    </member>
    <member name="T:System.ServiceModel.Channels.JavascriptCallbackResponseMessageProperty">
      <summary>Enables the use of a JavaScript callback in a service operation response using JSON Padding (JSONP).</summary>
    </member>
    <member name="M:System.ServiceModel.Channels.JavascriptCallbackResponseMessageProperty.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.JavascriptCallbackResponseMessageProperty" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Channels.JavascriptCallbackResponseMessageProperty.CallbackFunctionName">
      <summary>Gets or sets the name of the callback function used with JSONP.</summary>
      <returns>The name of the callback function.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.JavascriptCallbackResponseMessageProperty.Name">
      <summary>Gets the message property name used to add a JavaScript callback message property to a service operation response using JSONP.</summary>
      <returns>The message property name.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.JavascriptCallbackResponseMessageProperty.StatusCode">
      <summary>Gets or sets the HTTP status code.</summary>
      <returns>The HTTP status code.</returns>
    </member>
    <member name="T:System.ServiceModel.Channels.StreamBodyWriter">
      <summary>An abstract base class used to create custom <see cref="T:System.ServiceModel.Channels.BodyWriter" /> classes that can be used to a message body as a stream.</summary>
    </member>
    <member name="M:System.ServiceModel.Channels.StreamBodyWriter.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.StreamBodyWriter" /> class.</summary>
      <param name="isBuffered">true if the stream is buffered; otherwise false.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.StreamBodyWriter.OnCreateBufferedCopy(System.Int32)">
      <summary>Override this method to create a buffered copy of the stream.</summary>
      <returns>A body writer.</returns>
      <param name="maxBufferSize">The maximum buffer size.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.StreamBodyWriter.OnWriteBodyContents(System.IO.Stream)">
      <summary>Override this method to handle writing the message body contents.</summary>
      <param name="stream">The stream to write to.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.StreamBodyWriter.OnWriteBodyContents(System.Xml.XmlDictionaryWriter)">
      <summary>Override this method to handle writing the message body contents.</summary>
      <param name="writer">The writer to write to.</param>
    </member>
    <member name="T:System.ServiceModel.Channels.WebBodyFormatMessageProperty">
      <summary>Stores and retrieves the message encoding format of incoming and outgoing messages for the composite Web message encoder.</summary>
    </member>
    <member name="M:System.ServiceModel.Channels.WebBodyFormatMessageProperty.#ctor(System.ServiceModel.Channels.WebContentFormat)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.WebBodyFormatMessageProperty" /> class with a specified format.</summary>
      <param name="format">The <see cref="T:System.ServiceModel.Channels.WebContentFormat" /> of the message body.</param>
      <exception cref="T:System.ArgumentException">The format cannot be set to the <see cref="F:System.ServiceModel.Channels.WebContentFormat.Default" /> value in the constructor.</exception>
    </member>
    <member name="M:System.ServiceModel.Channels.WebBodyFormatMessageProperty.CreateCopy">
      <summary>Returns the current instance of the current property.</summary>
      <returns>An instance of the <see cref="T:System.ServiceModel.Channels.IMessageProperty" /> interface that is a copy of the current <see cref="T:System.ServiceModel.Channels.WebBodyFormatMessageProperty" />.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.WebBodyFormatMessageProperty.Format">
      <summary>Gets the format used for the message body.</summary>
      <returns>The <see cref="T:System.ServiceModel.Channels.WebContentFormat" /> that specifies the format used for the message body.</returns>
    </member>
    <member name="F:System.ServiceModel.Channels.WebBodyFormatMessageProperty.Name">
      <summary>Returns the name of the property.</summary>
      <returns>Returns: "WebBodyFormatMessageProperty".</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.WebBodyFormatMessageProperty.ToString">
      <summary>Returns the name of the property and the encoding format used when constructed.</summary>
      <returns>Returns "WebBodyFormatMessageProperty: EncodingFormat={0}", where {0} is WebContentFormat.ToString(), which specifies the encoding format used.</returns>
    </member>
    <member name="T:System.ServiceModel.Channels.WebContentFormat">
      <summary>Specifies the message formats to which content types of incoming messages can be mapped.</summary>
    </member>
    <member name="F:System.ServiceModel.Channels.WebContentFormat.Default">
      <summary>The format to map to cannot be determined.</summary>
    </member>
    <member name="F:System.ServiceModel.Channels.WebContentFormat.Xml">
      <summary>Map to the XML format.</summary>
    </member>
    <member name="F:System.ServiceModel.Channels.WebContentFormat.Json">
      <summary>Map to the JSON format.</summary>
    </member>
    <member name="F:System.ServiceModel.Channels.WebContentFormat.Raw">
      <summary>Map to the "Raw" binary format.</summary>
    </member>
    <member name="T:System.ServiceModel.Channels.WebContentTypeMapper">
      <summary>Specifies the format to which the content type of an incoming message is mapped.</summary>
    </member>
    <member name="M:System.ServiceModel.Channels.WebContentTypeMapper.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.WebContentTypeMapper" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Channels.WebContentTypeMapper.GetMessageFormatForContentType(System.String)">
      <summary>When overridden in a derived class, returns the message format used for a specified content type.</summary>
      <returns>The <see cref="T:System.ServiceModel.Channels.WebContentFormat" /> that specifies the format to which the message content type is mapped. </returns>
      <param name="contentType">The content type that indicates the MIME type of data to be interpreted.</param>
    </member>
    <member name="T:System.ServiceModel.Channels.WebMessageEncodingBindingElement">
      <summary>Enables plain-text XML, JavaScript Object Notation (JSON) message encodings and "raw" binary content to be read and written when used in a  binding.</summary>
    </member>
    <member name="M:System.ServiceModel.Channels.WebMessageEncodingBindingElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.WebMessageEncodingBindingElement" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Channels.WebMessageEncodingBindingElement.#ctor(System.Text.Encoding)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.WebMessageEncodingBindingElement" /> class with a specified write character encoding. </summary>
      <param name="writeEncoding">The <see cref="T:System.Text.Encoding" /> to be used to write characters in a message.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writeEncoding" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="writeEncoding" /> is not a supported message text encoding.</exception>
    </member>
    <member name="M:System.ServiceModel.Channels.WebMessageEncodingBindingElement.BuildChannelFactory``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Builds the channel factory stack on the client that creates a specified type of channel for a specified context.</summary>
      <returns>An <see cref="T:System.ServiceModel.Channels.IChannelFactory`1" /> of type <paramref name="TChannel" /> for the specified context.</returns>
      <param name="context">The <see cref="T:System.ServiceModel.Channels.BindingContext" /> for the channel.</param>
      <typeparam name="TChannel">The type of channel the channel factory produces.</typeparam>
      <exception cref="T:System.ArgumentNullException">The value set is null.</exception>
    </member>
    <member name="M:System.ServiceModel.Channels.WebMessageEncodingBindingElement.BuildChannelListener``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Builds the channel listener stack on the client that accepts a specified type of channel for a specified context.</summary>
      <returns>An <see cref="T:System.ServiceModel.Channels.IChannelListener`1" /> of type <paramref name="TChannel" /> for the specified context.</returns>
      <param name="context">The <see cref="T:System.ServiceModel.Channels.BindingContext" /> for the listener.</param>
      <typeparam name="TChannel">The type of channel the channel listener accepts.</typeparam>
      <exception cref="T:System.ArgumentNullException">The value set is null.</exception>
    </member>
    <member name="M:System.ServiceModel.Channels.WebMessageEncodingBindingElement.CanBuildChannelListener``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Returns a value that indicates whether the current binding can build a listener for a specified type of channel and context.</summary>
      <returns>true if the specified channel listener stack can be built on the service; otherwise, false.</returns>
      <param name="context">The <see cref="T:System.ServiceModel.Channels.BindingContext" /> for the listener.</param>
      <typeparam name="TChannel">The type of channel the channel listener accepts.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Channels.WebMessageEncodingBindingElement.Clone">
      <summary>Creates a new <see cref="T:System.ServiceModel.Channels.WebMessageEncodingBindingElement" /> object initialized from the current one.</summary>
      <returns>A <see cref="T:System.ServiceModel.Channels.WebMessageEncodingBindingElement" /> object with property values equal to those of the current element.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.WebMessageEncodingBindingElement.ContentTypeMapper">
      <summary>Gets or sets how the content type of an incoming message is mapped to a format.</summary>
      <returns>The <see cref="T:System.ServiceModel.Channels.WebContentTypeMapper" /> that indicates the format for the content type of the incoming message.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.WebMessageEncodingBindingElement.CreateMessageEncoderFactory">
      <summary>Creates a message encoder factory that produces message encoders that can write either JavaScript Object Notation (JSON) or XML messages.</summary>
      <returns>The <see cref="T:System.ServiceModel.Channels.MessageEncoderFactory" /> that encodes JSON, XML or "raw" binary messages.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.WebMessageEncodingBindingElement.CrossDomainScriptAccessEnabled">
      <summary>Gets or sets a value that determines if cross domain script access is enabled.</summary>
      <returns>true if cross domain script access is enabled; otherwise, false.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.WebMessageEncodingBindingElement.GetProperty``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Returns the object of the type requested, if present, from the appropriate layer in the channel stack, or null if it is not present.</summary>
      <returns>The typed object <paramref name="T" /> requested if it is present or null if it is not.</returns>
      <param name="context">The <see cref="T:System.ServiceModel.Channels.BindingContext" /> for the current binding element.</param>
      <typeparam name="T">The typed object for which the method is querying.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="context" /> set is null.</exception>
    </member>
    <member name="P:System.ServiceModel.Channels.WebMessageEncodingBindingElement.MaxReadPoolSize">
      <summary>Gets or sets a value that specifies the maximum number of readers that is allocated to a pool and that is available to process incoming messages without allocating new readers.</summary>
      <returns>The maximum number of readers available to process incoming messages. The default value is 64 readers of each type.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value is less than or equal to zero.</exception>
    </member>
    <member name="P:System.ServiceModel.Channels.WebMessageEncodingBindingElement.MaxWritePoolSize">
      <summary>Gets or sets a value that specifies the maximum number of writers that is allocated to a pool and that is available to process outgoing messages without allocating new writers.</summary>
      <returns>The maximum number of writers available to process outgoing messages. The default is 16 writers of each type.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value is less than or equal to zero.</exception>
    </member>
    <member name="P:System.ServiceModel.Channels.WebMessageEncodingBindingElement.MessageVersion">
      <summary>Gets or sets the message version that indicates that the binding element does not use SOAP or WS-Addressing.</summary>
      <returns>
        <see cref="P:System.ServiceModel.Channels.MessageVersion.None" />
      </returns>
      <exception cref="T:System.ArgumentNullException">The value set is null.</exception>
      <exception cref="T:System.ArgumentException">The value set is neither null nor <see cref="P:System.ServiceModel.Channels.MessageVersion.None" />.</exception>
    </member>
    <member name="P:System.ServiceModel.Channels.WebMessageEncodingBindingElement.ReaderQuotas">
      <summary>Gets constraints on the complexity of SOAP messages that can be processed by endpoints configured with this binding.</summary>
      <returns>The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> that specifies the complexity constraints on SOAP messages that are exchanged. The default values for these constraints are provided in the following Remarks section.</returns>
      <exception cref="T:System.ArgumentNullException">The value set is null.</exception>
    </member>
    <member name="M:System.ServiceModel.Channels.WebMessageEncodingBindingElement.System#ServiceModel#Description#IWsdlExportExtension#ExportContract(System.ServiceModel.Description.WsdlExporter,System.ServiceModel.Description.WsdlContractConversionContext)">
      <summary>Generates WSDL contract information from encoding policies contained in the binding element.</summary>
      <param name="exporter">The <see cref="T:System.ServiceModel.Description.WsdlExporter" /> that exports the contract information.</param>
      <param name="context">A <see cref="T:System.ServiceModel.Description.WsdlContractConversionContext" /> object that provides mappings from exported WSDL elements to the contract description.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.WebMessageEncodingBindingElement.System#ServiceModel#Description#IWsdlExportExtension#ExportEndpoint(System.ServiceModel.Description.WsdlExporter,System.ServiceModel.Description.WsdlEndpointConversionContext)">
      <summary>Generates WSDL contract information from encoding policies contained in the binding element.</summary>
      <param name="exporter">The <see cref="T:System.ServiceModel.Description.WsdlExporter" /> that exports the contract information.</param>
      <param name="context">A <see cref="T:System.ServiceModel.Description.WsdlContractConversionContext" /> object that provides mappings from exported WSDL elements to the endpoint description.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="context" /> is null.</exception>
    </member>
    <member name="P:System.ServiceModel.Channels.WebMessageEncodingBindingElement.WriteEncoding">
      <summary>Gets or sets the character encoding that is used to write the message text.</summary>
      <returns>The <see cref="T:System.Text.Encoding" /> that indicates the character encoding that is used to write the message text. The default is <see cref="T:System.Text.UTF8Encoding" />.</returns>
      <exception cref="T:System.ArgumentNullException">The value set is null.</exception>
    </member>
    <member name="T:System.ServiceModel.Configuration.WebHttpBindingCollectionElement">
      <summary>Represents a configuration element that contains sub-elements that specify settings for using the <see cref="T:System.ServiceModel.WebHttpBinding" /> binding.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpBindingCollectionElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Configuration.WebHttpBindingCollectionElement" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpBindingCollectionElement.GetDefault">
      <summary>Gets the default binding used.</summary>
      <returns>A <see cref="T:System.ServiceModel.Channels.Binding" /> object that represents the default binding.</returns>
    </member>
    <member name="T:System.ServiceModel.Configuration.WebHttpBindingElement">
      <summary>A binding element used to configure endpoints for  Web services that respond to HTTP requests instead of SOAP messages.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpBindingElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Configuration.WebHttpBindingElement" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpBindingElement.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Configuration.WebHttpBindingElement" /> class and specifies the name of the element. </summary>
      <param name="name">The name that is used for this binding configuration element.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.AllowCookies">
      <summary>Gets or sets a value that indicates whether the client accepts cookies and propagates them on future requests.</summary>
      <returns>true if cookies are allowed; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.BindingElementType">
      <summary>Gets the <see cref="T:System.Type" /> of binding that this configuration element represents.</summary>
      <returns>The binding type.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.BypassProxyOnLocal">
      <summary>Gets or sets a value that indicates whether to bypass the proxy server for local addresses.</summary>
      <returns>true to bypass the proxy server for local addresses; otherwise, false. The default value is false.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.ContentTypeMapper">
      <summary>Gets or sets how the content type of an incoming message is mapped to a format.</summary>
      <returns>A string that points to the mapper which indicates the format for the content type of the incoming message.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.CrossDomainScriptAccessEnabled">
      <summary>Gets or sets a value that indicates whether cross domain scripting is permitted. </summary>
      <returns>true if cross domain scripting is permitted; otherwise, false. </returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.HostNameComparisonMode">
      <summary>Gets or sets a value that indicates whether the hostname is used to reach the service when matching the URI.</summary>
      <returns>The <see cref="T:System.ServiceModel.HostnameComparisonMode" /> value that indicates whether the hostname is used to reach the service when matching on the URI. The default value is <see cref="F:System.ServiceModel.HostnameComparisonMode.StrongWildcard" />, which ignores the hostname in the match.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value set is not a valid <see cref="T:System.ServiceModel.HostnameComparisonMode" />      value.</exception>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpBindingElement.InitializeFrom(System.ServiceModel.Channels.Binding)">
      <summary>Initializes the contents of this binding configuration element from the property values of a specified binding.</summary>
      <param name="binding">The <see cref="T:System.ServiceModel.Channels.Binding" /> used to initialize this configuration element.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.MaxBufferPoolSize">
      <summary>Gets or sets the maximum amount of memory allocated for the buffer manager that manages the buffers required by endpoints that use this binding.</summary>
      <returns>The maximum size, in bytes, for the pool of buffers used by an endpoint configured with this binding. The default value is 65,536 bytes.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.MaxBufferSize">
      <summary>Gets or sets the maximum amount of memory that is allocated for use by the manager of the message buffers that receive messages from the channel.</summary>
      <returns>The maximum amount of memory, in bytes, available for use by the message buffer manager. The default value is 524,288 (0x80000) bytes.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value set is less than or equal to zero.</exception>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.MaxReceivedMessageSize">
      <summary>Gets or sets the maximum size for a message that can be processed by the binding.</summary>
      <returns>The maximum size, in bytes, for a message that is processed by the binding. The default value is 65,536 bytes.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value is less than zero.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">A message exceeded the maximum size allocated.</exception>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpBindingElement.OnApplyConfiguration(System.ServiceModel.Channels.Binding)">
      <summary>Initializes the property values of a specified binding from the contents of this binding configuration element.</summary>
      <param name="binding">The <see cref="T:System.ServiceModel.Channels.Binding" /> that is initialized from the contents of this binding configuration element.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="binding" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The type of this binding element is different from the type specified by <paramref name="binding" />.</exception>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.Properties">
      <summary>Gets a <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> instance that contains a collection of <see cref="T:System.Configuration.ConfigurationProperty" /> objects that can be attributes or <see cref="T:System.Configuration.ConfigurationElement" /> objects of this configuration element.</summary>
      <returns>A <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> instance that contains a collection of <see cref="T:System.Configuration.ConfigurationProperty" /> objects that can be attributes or <see cref="T:System.Configuration.ConfigurationElement" /> objects of this configuration element.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.ProxyAddress">
      <summary>Gets or sets the URI address of the HTTP proxy.</summary>
      <returns>A <see cref="T:System.Uri" /> that serves as the address of the HTTP proxy. The default value is null.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.ReaderQuotas">
      <summary>Gets or sets the configuration element that contains the constraints on the complexity of SOAP messages that can be processed by endpoints configured with this binding.</summary>
      <returns>The <see cref="T:System.ServiceModel.Configuration.XmlDictionaryReaderQuotasElement" /> that specifies the complexity constraints.</returns>
      <exception cref="T:System.ArgumentNullException">The value set is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The quota values of <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> are read only.</exception>
      <exception cref="T:System.ArgumentException">The quotas set must be positive.</exception>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.Security">
      <summary>Gets the configuration element that contains the security settings used with this binding.</summary>
      <returns>The <see cref="T:System.ServiceModel.Configuration.WebHttpSecurityElement" /> that is used with this binding. The default value is <see cref="F:System.ServiceModel.WebHttpSecurityMode.None" />.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.TransferMode">
      <summary>Gets or sets a value that indicates whether the service configured with the binding uses streamed or buffered (or both) modes of message transfer.</summary>
      <returns>The <see cref="T:System.ServiceModel.TransferMode" /> value that indicates whether the service configured with the binding uses streamed or buffered (or both) modes of message transfer. The default value is <see cref="F:System.ServiceModel.TransferMode.Buffered" />.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value set is not a valid <see cref="T:System.ServiceModel.TransferMode" /> value.</exception>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.UseDefaultWebProxy">
      <summary>Gets or sets a value that indicates whether the auto-configured HTTP proxy of the system should be used, if available.</summary>
      <returns>true if the auto-configured HTTP proxy of the system should be used, if available; otherwise, false. The default value is true.  </returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpBindingElement.WriteEncoding">
      <summary>Gets or sets the character encoding that is used for the message text.</summary>
      <returns>The <see cref="T:System.Text.Encoding" /> that indicates the character encoding that is used. The default is <see cref="T:System.Text.UTF8Encoding" />.</returns>
      <exception cref="T:System.ArgumentNullException">The value set is null.</exception>
    </member>
    <member name="T:System.ServiceModel.Configuration.WebHttpElement">
      <summary>Enables the <see cref="T:System.ServiceModel.Description.WebHttpBehavior" /> for an endpoint through configuration.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Configuration.WebHttpElement" /> class. </summary>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpElement.AutomaticFormatSelectionEnabled">
      <summary>Gets or sets a value that indicates whether the message format can be automatically selected.</summary>
      <returns>true if the message format can be automatically selected; otherwise, false. </returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpElement.BehaviorType">
      <summary>Gets the type of the behavior enabled by this configuration element.</summary>
      <returns>The <see cref="T:System.Type" /> for the behavior enabled with the configuration element: <see cref="T:System.ServiceModel.Description.WebHttpBehavior" />.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpElement.DefaultBodyStyle">
      <summary>Gets and sets the default message body style.</summary>
      <returns>One of the values defined in the <see cref="T:System.ServiceModel.Web.WebMessageBodyStyle" /> enumeration.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpElement.DefaultOutgoingResponseFormat">
      <summary>Gets and sets the default outgoing response format.</summary>
      <returns>One of the values defined in the <see cref="T:System.ServiceModel.Web.WebMessageFormat" /> enumeration.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpElement.FaultExceptionEnabled">
      <summary>Gets or sets the flag that specifies whether a FaultException is generated when an internal server error (HTTP status code: 500) occurs.</summary>
      <returns>Returns true if the flag is enabled; otherwise returns false.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpElement.HelpEnabled">
      <summary>Gets or sets a value that indicates whether help is enabled.</summary>
      <returns>true if help is enabled; otherwise, false. </returns>
    </member>
    <member name="T:System.ServiceModel.Configuration.WebHttpEndpointCollectionElement">
      <summary>Represents a collection of <see cref="T:System.ServiceModel.Description.WebHttpEndpoint" /> objects.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpEndpointCollectionElement.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Configuration.WebHttpEndpointCollectionElement" /> class.</summary>
    </member>
    <member name="T:System.ServiceModel.Configuration.WebHttpEndpointElement">
      <summary>Represents a WCF configuration element for a web service application.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpEndpointElement.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Configuration.WebHttpEndpointElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.AutomaticFormatSelectionEnabled">
      <summary>Gets or sets whether automatic formatting selection is turned on.</summary>
      <returns>true if automatic formatting is enabled; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.ContentTypeMapper">
      <summary>Gets or sets the type of MIME content of the data sent by a web service operations.</summary>
      <returns>A string that contains the name of a MIME content.</returns>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpEndpointElement.CreateServiceEndpoint(System.ServiceModel.Description.ContractDescription)">
      <summary>Creates a new endpoint of type <see cref="T:System.ServiceModel.Description.WebHttpEndpoint" /> with the specified contract description.</summary>
      <returns>A service endpoint.</returns>
      <param name="contractDescription">An object of type <see cref="T:System.ServiceModel.Description.ContractDescription" /> that specifies what an endpoint communicates to the outside world, including the signature of the operations in terms of messages exchanged, the data types of these messages, the location of the operations, and the specific protocols and serialization formats that are used to support successful communication with the service.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.CrossDomainScriptAccessEnabled">
      <summary>Gets or sets whether a condition is enabled that allows code injection into web pages viewed by other users.</summary>
      <returns>true if access is enabled; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.DefaultOutgoingResponseFormat">
      <summary>Gets or sets the default format for responses sent out from a web service operations.</summary>
      <returns>A format object.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.EndpointType">
      <summary>Gets the type of the <see cref="T:System.ServiceModel.Description.WebHttpEndpoint" />.</summary>
      <returns>A type object.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.FaultExceptionEnabled">
      <summary>Gets or sets the flag that specifies whether a FaultException is generated when an internal server error (HTTP status code: 500) occurs.</summary>
      <returns>Returns true if the flag is enabled; otherwise returns false.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.HelpEnabled">
      <summary>Gets or sets whether help is enabled.</summary>
      <returns>true if help is enabled; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.HostNameComparisonMode">
      <summary>Gets or sets the web service host name and URI matching rule that is configured in the binding element and used by transports such as HTTP, TCP, and named pipes when dispatching incoming messages.</summary>
      <returns>An enumerated comparison mode.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.MaxBufferPoolSize">
      <summary>Gets or sets the maximum amount of memory allocated for the buffer manager that manages the buffers required by endpoints using this binding.</summary>
      <returns>A 64-bit representation of a number.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.MaxBufferSize">
      <summary>Gets or sets the maximum buffer size to use.</summary>
      <returns>A number representing buffer size.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.MaxReceivedMessageSize">
      <summary>Gets or sets the maximum number of messages that can be received by the web service.</summary>
      <returns>A 64-bit number representing a maximum quantity.</returns>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpEndpointElement.OnApplyConfiguration(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Configuration.ChannelEndpointElement)">
      <summary>Converts the specified <see cref="T:System.ServiceModel.Description.ServiceEndpoint" /> to a <see cref="T:System.ServiceModel.Description.WebHttpEndpoint" />.</summary>
      <param name="endpoint">The <see cref="T:System.ServiceModel.Description.ServiceEndpoint" /> to which web HTTP configuration settings are applied.</param>
      <param name="serviceEndpointElement">Not implemented.</param>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpEndpointElement.OnApplyConfiguration(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Configuration.ServiceEndpointElement)">
      <summary>Converts the specified <see cref="T:System.ServiceModel.Description.ServiceEndpoint" /> to a <see cref="T:System.ServiceModel.Description.WebHttpEndpoint" />.</summary>
      <param name="endpoint">The <see cref="T:System.ServiceModel.Description.ServiceEndpoint" /> to which web HTTP configuration settings are applied.</param>
      <param name="serviceEndpointElement">Not implemented.</param>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpEndpointElement.OnInitializeAndValidate(System.ServiceModel.Configuration.ChannelEndpointElement)">
      <summary>Initializes and verifies the format of a specified channel endpoint element and configures it to include web HTTP content.</summary>
      <param name="channelEndpointElement">The channel endpoint element to initialize and validate. </param>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpEndpointElement.OnInitializeAndValidate(System.ServiceModel.Configuration.ServiceEndpointElement)">
      <summary>Initializes and verifies the format of a specified service endpoint element and configures it to include web HTTP content.</summary>
      <param name="serviceEndpointElement">The service endpoint element to initialize and validate.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.Properties">
      <summary>Gets the collection of <see cref="T:System.ConfigurationProperty" /> objects associated with the current <see cref="T:System.ServiceModel.Configuration.WebHttpEndpointElement" />.</summary>
      <returns>A collection of configuration properties.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.ReaderQuotas">
      <summary>Gets the <see cref="T:System.ServiceModel.Configuration.XmlDictionaryReaderQuotasElement" /> object that contains contraints on length and complexity of XML strings that are read by the XML dictionary reader.</summary>
      <returns>An object that contains XML dictionary reader quotas.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.Security">
      <summary>Gets an element that configures the security for a web service with endpoints that receive HTTP requests.</summary>
      <returns>A web service security element.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.TransferMode">
      <summary>Gets or sets the WCF method of transporting data across a network between endpoints.</summary>
      <returns>An enumeration of transfer modes.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpEndpointElement.WriteEncoding">
      <summary>Gets or sets the string to be encoded.</summary>
      <returns>Encoded text.</returns>
    </member>
    <member name="T:System.ServiceModel.Configuration.WebHttpSecurityElement">
      <summary>An XML element that configures the security for a service with endpoints that receive HTTP requests. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebHttpSecurityElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Configuration.WebHttpSecurityElement" /> class. </summary>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpSecurityElement.Mode">
      <summary>Gets an XML element that specifies the security mode for a basic HTTP service.</summary>
      <returns>A value of <see cref="T:System.ServiceModel.WebHttpSecurityMode" /> that indicates whether transport-level security or no security is used by an endpoint. The default value is <see cref="F:System.ServiceModel.WebHttpSecurityMode.None" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value is not a valid <see cref="T:System.ServiceModel.WebHttpSecurityMode" />.</exception>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebHttpSecurityElement.Transport">
      <summary>Gets an XML element that indicates the transport-level security settings for a service endpoint configured to receive HTTP requests.</summary>
      <returns>A <see cref="T:System.ServiceModel.Configuration.WebHttpSecurityElement" /> that specifies the transport-level security settings. The default values set are a <see cref="P:System.ServiceModel.HttpTransportSecurity.ClientCredentialType" /> of <see cref="F:System.ServiceModel.HttpClientCredentialType.None" />, a <see cref="P:System.ServiceModel.HttpTransportSecurity.ProxyCredentialType" /> of <see cref="F:System.ServiceModel.HttpProxyCredentialType.None" />, and <see cref="P:System.ServiceModel.HttpTransportSecurity.Realm" /> = "".</returns>
    </member>
    <member name="T:System.ServiceModel.Configuration.WebMessageEncodingElement">
      <summary>Represents the configuration element that specifies the character encoding used for non-SOAP messages. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebMessageEncodingElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Configuration.WebMessageEncodingElement" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebMessageEncodingElement.ApplyConfiguration(System.ServiceModel.Channels.BindingElement)">
      <summary>Applies the content of a specified binding element to this binding configuration section.</summary>
      <param name="bindingElement">The <see cref="T:System.ServiceModel.Channels.BindingElement" /> to be applied.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebMessageEncodingElement.BindingElementType">
      <summary>Gets the type of the binding element enabled by this configuration element.</summary>
      <returns>The type of binding element enabled by this configuration element: <see cref="T:System.ServiceModel.Channels.WebMessageEncodingBindingElement" />.</returns>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebMessageEncodingElement.CopyFrom(System.ServiceModel.Configuration.ServiceModelExtensionElement)">
      <summary>Copies the content of the specified configuration section to this element.</summary>
      <param name="from">The <see cref="T:System.ServiceModel.Configuration.ServiceModelExtensionElement" /> to be copied.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebMessageEncodingElement.MaxReadPoolSize">
      <summary>Gets or sets a value that specifies the maximum number of messages that can be read simultaneously without allocating new readers.</summary>
      <returns>The maximum number of messages that can be read simultaneously without allocating new readers. The default is 64.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebMessageEncodingElement.MaxWritePoolSize">
      <summary>Gets or sets a value that specifies the maximum number of messages that can be sent simultaneously without allocating new writers.</summary>
      <returns>The maximum number of messages that can be sent simultaneously without allocating new writers. The default is 16.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebMessageEncodingElement.ReaderQuotas">
      <summary>Gets or sets constraints on the complexity of SOAP messages that can be processed by endpoints configured with this binding.</summary>
      <returns>The <see cref="T:System.ServiceModel.Configuration.XmlDictionaryReaderQuotasElement" /> that specifies the complexity constraints. </returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebMessageEncodingElement.WebContentTypeMapperType">
      <summary>Gets or sets the type name of a <see cref="T:System.ServiceModel.Channels.WebContentTypeMapper" /> that specifies the format to which the content type of an incoming message is mapped.</summary>
      <returns>The type name of a <see cref="T:System.ServiceModel.Channels.WebContentTypeMapper" />.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebMessageEncodingElement.WriteEncoding">
      <summary>Gets or sets the character set encoding to be used for emitting messages on the binding.</summary>
      <returns>A valid <see cref="T:System.Text.Encoding" /> value that specifies the character set encoding to be used for emitting messages on the binding. The default is <see cref="T:System.Text.UTF8Encoding" />.</returns>
      <exception cref="T:System.ArgumentNullException">The value set is null.</exception>
    </member>
    <member name="T:System.ServiceModel.Configuration.WebScriptEnablingElement">
      <summary>Enables the <see cref="T:System.ServiceModel.Description.WebScriptEnablingBehavior" /> for an endpoint through configuration.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebScriptEnablingElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Configuration.WebScriptEnablingElement" /> class. </summary>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebScriptEnablingElement.BehaviorType">
      <summary>Gets the type of the behavior enabled by this configuration element.</summary>
      <returns>The <see cref="T:System.Type" /> for the behavior enabled with the configuration element: <see cref="T:System.ServiceModel.Description.WebScriptEnablingBehavior" />.</returns>
    </member>
    <member name="T:System.ServiceModel.Configuration.WebScriptEndpointCollectionElement">
      <summary>Represents a collection of <see cref="T:System.ServiceModel.Description.WebScriptEndpoint" /> objects.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebScriptEndpointCollectionElement.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Configuration.WebScriptEndpointCollectionElement" /> class.</summary>
    </member>
    <member name="T:System.ServiceModel.Configuration.WebScriptEndpointElement">
      <summary>Represents a custom configuration element that defines a <see cref="T:System.ServiceModel.Description.WebScriptEndpoint" /> in a configuration file.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebScriptEndpointElement.#ctor">
      <summary>Initiates a new instance of the <see cref="T:System.ServiceModel.Configuration.WebScriptEndpointElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebScriptEndpointElement.ContentTypeMapper">
      <summary>Gets or sets the name of the group of behaviors and metadata that produce the web document configured by the current <see cref="T:System.ServiceModel.Configuration.WebScriptEndpointElement" />.</summary>
      <returns>The name of a content type.</returns>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebScriptEndpointElement.CreateServiceEndpoint(System.ServiceModel.Description.ContractDescription)">
      <summary>Initiates a new <see cref="T:System.ServiceModel.Description.WebScriptEndpoint" /> with the specified <see cref="T:System.ServiceModel.Description.ContractDescription" />.</summary>
      <returns>A web script endpoint.</returns>
      <param name="contractDescription">An object whose properties define an endpoint’s characteristics, includingthe signature of the operations in terms of messages exchangedthe data types of these messages, the location of the operationsthe specific protocols and serialization formats that are used to support successful communication with the service</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebScriptEndpointElement.CrossDomainScriptAccessEnabled">
      <summary>Gets or sets whether different host computers can access each others’ scripts.</summary>
      <returns>true if cross domain script access is enabled; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebScriptEndpointElement.EndpointType">
      <summary>Gets the type of the <see cref="T:System.ServiceModel.Description.WebScriptEndpoint" />.</summary>
      <returns>An web script endpoint object.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebScriptEndpointElement.HostNameComparisonMode">
      <summary>Specifies how the host name should be used in URI comparisons when dispatching an incoming message to a <see cref="T:System.ServiceModel.Description.WebScriptEndpoint" />.</summary>
      <returns>An enumerated description of a comparison mode.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebScriptEndpointElement.MaxBufferPoolSize">
      <summary>Gets or sets the maximum size of any buffer pools used by the message transport.</summary>
      <returns>The maximum buffer pool size.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebScriptEndpointElement.MaxBufferSize">
      <summary>Gets or sets the maximum size of the buffer used to receive messages.</summary>
      <returns>The maximum buffer size.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebScriptEndpointElement.MaxReceivedMessageSize">
      <summary>Gets or sets the maximum byte size of messages received by a <see cref="T:System.ServiceModel.Description.WebScriptEndpoint" />. </summary>
      <returns>The maximum byte size.</returns>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebScriptEndpointElement.OnApplyConfiguration(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Configuration.ChannelEndpointElement)">
      <summary>Converts the specified <see cref="T:System.ServiceModel.Description.ServiceEndpoint" /> to a <see cref="T:System.ServiceModel.Description.WebScriptEndpoint" />.</summary>
      <param name="endpoint">The <see cref="T:System.ServiceModel.Description.ServiceEndpoint" /> to convert.</param>
      <param name="serviceEndpointElement">Not used.</param>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebScriptEndpointElement.OnApplyConfiguration(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Configuration.ServiceEndpointElement)">
      <summary>Converts the specified <see cref="T:System.ServiceModel.Description.ServiceEndpoint" /> to a <see cref="T:System.ServiceModel.Description.WebScriptEndpoint" />.</summary>
      <param name="endpoint">The <see cref="T:System.ServiceModel.Description.ServiceEndpoint" /> to convert.</param>
      <param name="serviceEndpointElement">Not used.</param>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebScriptEndpointElement.OnInitializeAndValidate(System.ServiceModel.Configuration.ChannelEndpointElement)">
      <summary>Initializes and verifies the format of the specified <see cref="T:System.ServiceModel.Configuration.ChannelEndpointElement" /> and configures it to include web HTTP binding content.</summary>
      <param name="channelEndpointElement">The <see cref="T:System.ServiceModel.Configuration.ChannelEndpointElement" /> to be initialized.</param>
    </member>
    <member name="M:System.ServiceModel.Configuration.WebScriptEndpointElement.OnInitializeAndValidate(System.ServiceModel.Configuration.ServiceEndpointElement)">
      <summary>Initializes and verifies the format of the specified <see cref="T:System.ServiceModel.Configuration.ServiceEndpointElement" /> and configures it to include web HTTP binding content.</summary>
      <param name="serviceEndpointElement">The <see cref="T:System.ServiceModel.Configuration.ServiceEndpointElement" /> to be initialized.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebScriptEndpointElement.Properties">
      <summary>Gets all the properties attached to the current <see cref="T:System.ServiceModel.Description.WebScriptEndpoint" />.</summary>
      <returns>A collection of endpoint element properties.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebScriptEndpointElement.ReaderQuotas">
      <summary>Gets a configuration element that defines the constraints on the complexity of messages that can be processed by <see cref="T:System.ServiceModel.Description.WebScriptEndpoint" /> objects.</summary>
      <returns>A configuration element that defines Xml dictionary reader quotas. </returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebScriptEndpointElement.Security">
      <summary>Gets an HTTP web security binding element.</summary>
      <returns>A web script endpoint configuration element.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebScriptEndpointElement.TransferMode">
      <summary>Gets or sets the manner in which WCF messages are transported.</summary>
      <returns>An object that contains a string that designates a WCF message transfer mode.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.WebScriptEndpointElement.WriteEncoding">
      <summary>Gets or sets the encoding that is used to format the characters in a text message.</summary>
      <returns>The character encoding.</returns>
    </member>
    <member name="T:System.ServiceModel.Description.JsonFaultDetail">
      <summary>Represents a SOAP fault that is specified in the service contract for use with JSON.</summary>
    </member>
    <member name="M:System.ServiceModel.Description.JsonFaultDetail.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Description.JsonFaultDetail" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Description.JsonFaultDetail.ExceptionDetail">
      <summary>Gets or sets the <see cref="T:System.ServiceModel.ExceptionDetail" /> object that represents a SOAP fault that is specified in the service contract.</summary>
      <returns>The SOAP fault that is specified in the service contract.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.JsonFaultDetail.ExceptionType">
      <summary>Gets or sets the type of the exception.</summary>
      <returns>The type of the exception.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.JsonFaultDetail.Message">
      <summary>Gets or sets the exception message.</summary>
      <returns>The exception message.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.JsonFaultDetail.StackTrace">
      <summary>Gets or sets the stack trace information for this exception.</summary>
      <returns>The stack trace information for this exception.</returns>
    </member>
    <member name="T:System.ServiceModel.Description.WebHttpBehavior">
      <summary>Enables the Web programming model for a  service.</summary>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpBehavior.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Description.WebHttpBehavior" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpBehavior.AddBindingParameters(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Channels.BindingParameterCollection)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IEndpointBehavior.AddBindingParameters(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Channels.BindingParameterCollection)" /> method to pass data at runtime to bindings to support custom behavior.</summary>
      <param name="endpoint">The endpoint.</param>
      <param name="bindingParameters">The binding parameters that support modifying the bindings.</param>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpBehavior.AddClientErrorInspector(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Dispatcher.ClientRuntime)">
      <summary>Adds a client error inspector to the specified service endpoint.</summary>
      <param name="endpoint">The service endpoint.</param>
      <param name="clientRuntime">The client runtime.</param>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpBehavior.AddServerErrorHandlers(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Dispatcher.EndpointDispatcher)">
      <summary>Override this method to change the way errors that occur on the service are handled.</summary>
      <param name="endpoint">The service endpoint.</param>
      <param name="endpointDispatcher">The endpoint dispatcher.</param>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpBehavior.ApplyClientBehavior(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Dispatcher.ClientRuntime)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IEndpointBehavior.ApplyClientBehavior(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Dispatcher.ClientRuntime)" /> method to support modification or extension of the client across an endpoint.</summary>
      <param name="endpoint">The endpoint that exposes the contract.</param>
      <param name="clientRuntime">The client to which the custom behavior is applied.</param>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Dispatcher.EndpointDispatcher)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IEndpointBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Dispatcher.EndpointDispatcher)" /> method to support modification or extension of the client across an endpoint.</summary>
      <param name="endpoint">The endpoint that exposes the contract.</param>
      <param name="endpointDispatcher">The endpoint dispatcher to which the behavior is applied.</param>
    </member>
    <member name="P:System.ServiceModel.Description.WebHttpBehavior.AutomaticFormatSelectionEnabled">
      <summary>Gets or sets a value that determines if automatic format selection is enabled.</summary>
      <returns>true if automatic format selection is enabled; otherwise false.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebHttpBehavior.DefaultBodyStyle">
      <summary>Gets and sets the default message body style.</summary>
      <returns>One of the values defined in the <see cref="T:System.ServiceModel.Web.WebMessageBodyStyle" /> enumeration.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebHttpBehavior.DefaultOutgoingRequestFormat">
      <summary>Gets and sets the default outgoing request format.</summary>
      <returns>One of the values defined in the <see cref="T:System.ServiceModel.Web.WebMessageFormat" /> enumeration.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebHttpBehavior.DefaultOutgoingResponseFormat">
      <summary>Gets and sets the default outgoing response format.</summary>
      <returns>One of the values defined in the <see cref="T:System.ServiceModel.Web.WebMessageFormat" /> enumeration.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebHttpBehavior.FaultExceptionEnabled">
      <summary>Gets or sets the flag that specifies whether a FaultException is generated when an internal server error (HTTP status code: 500) occurs.</summary>
      <returns>Returns true if the flag is enabled; otherwise returns false.</returns>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpBehavior.GetOperationSelector(System.ServiceModel.Description.ServiceEndpoint)">
      <summary>Creates a new <see cref="T:System.ServiceModel.Dispatcher.WebHttpDispatchOperationSelector" /> object.</summary>
      <returns>An instance of <see cref="T:System.ServiceModel.Dispatcher.WebHttpDispatchOperationSelector" /> that contains the operation selector for the specified endpoint.</returns>
      <param name="endpoint">The endpoint that exposes the contract.</param>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpBehavior.GetQueryStringConverter(System.ServiceModel.Description.OperationDescription)">
      <summary>Gets the query string converter.</summary>
      <returns>A <see cref="T:System.ServiceModel.Dispatcher.QueryStringConverter" /> instance.</returns>
      <param name="operationDescription">The service operation.</param>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpBehavior.GetReplyClientFormatter(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Description.ServiceEndpoint)">
      <summary>Gets the reply formatter on the client for the specified endpoint and service operation.</summary>
      <returns>An <see cref="T:System.ServiceModel.Dispatcher.IClientMessageFormatter" /> reference to the reply formatter on the client for the specified operation and endpoint.</returns>
      <param name="operationDescription">The service operation.</param>
      <param name="endpoint">The service endpoint.</param>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpBehavior.GetReplyDispatchFormatter(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Description.ServiceEndpoint)">
      <summary>Gets the reply formatter on the service for the specified endpoint and service operation.</summary>
      <returns>An <see cref="T:System.ServiceModel.Dispatcher.IDispatchMessageFormatter" /> reference to the reply formatter on the service for the specified operation and endpoint.</returns>
      <param name="operationDescription">The service operation.</param>
      <param name="endpoint">The service endpoint.</param>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpBehavior.GetRequestClientFormatter(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Description.ServiceEndpoint)">
      <summary>Gets the request formatter on the client for the specified service operation and endpoint.</summary>
      <returns>An <see cref="T:System.ServiceModel.Dispatcher.IClientMessageFormatter" /> reference to the request formatter on the client for the specified operation and endpoint.</returns>
      <param name="operationDescription">The service operation.</param>
      <param name="endpoint">The service endpoint.</param>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpBehavior.GetRequestDispatchFormatter(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Description.ServiceEndpoint)">
      <summary>Gets the request formatter on the service for the given service operation and service endpoint.</summary>
      <returns>An <see cref="T:System.ServiceModel.Dispatcher.IDispatchMessageFormatter" /> reference to the request formatter on the service for the specified operation and endpoint.</returns>
      <param name="operationDescription">The service operation.</param>
      <param name="endpoint">The service endpoint.</param>
    </member>
    <member name="P:System.ServiceModel.Description.WebHttpBehavior.HelpEnabled">
      <summary>Gets or sets a value that determines if the  Help page is enabled.</summary>
      <returns>true if the Help page is enabled; otherwise false.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebHttpBehavior.JavascriptCallbackParameterName">
      <summary>Gets or sets the JavaScript callback parameter name.</summary>
      <returns>The JavaScript callback parameter name.</returns>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpBehavior.Validate(System.ServiceModel.Description.ServiceEndpoint)">
      <summary>Confirms that the endpoint meets the requirements for the Web programming model.</summary>
      <param name="endpoint">The service endpoint.</param>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpBehavior.ValidateBinding(System.ServiceModel.Description.ServiceEndpoint)">
      <summary>Ensures the binding is valid for use with the WCF Web Programming Model.</summary>
      <param name="endpoint">The service endpoint.</param>
    </member>
    <member name="T:System.ServiceModel.Description.WebHttpEndpoint">
      <summary>A standard endpoint with a fixed <see cref="T:System.ServiceModel.WebHttpBinding" /> binding that automatically adds the <see cref="T:System.ServiceModel.Description.WebHttpBehavior" /> behavior.</summary>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpEndpoint.#ctor(System.ServiceModel.Description.ContractDescription)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Description.WebHttpEndpoint" /> class with the specified <see cref="T:System.ServiceModel.Description.ContractDescription" />.</summary>
      <param name="contract">The contract description.</param>
    </member>
    <member name="M:System.ServiceModel.Description.WebHttpEndpoint.#ctor(System.ServiceModel.Description.ContractDescription,System.ServiceModel.EndpointAddress)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Description.WebHttpEndpoint" /> class with the specified <see cref="T:System.ServiceModel.Description.ContractDescription" /> and <see cref="T:System.ServiceModel.EndpointAddress" />.</summary>
      <param name="contract">The contract description.</param>
      <param name="address">The endpoint address.</param>
    </member>
    <member name="P:System.ServiceModel.Description.WebHttpEndpoint.AutomaticFormatSelectionEnabled">
      <summary>Gets or sets a value that indicates whether automatic format selection is enabled.</summary>
      <returns>true if automatic format selection is enabled, otherwise false.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebHttpEndpoint.DefaultOutgoingResponseFormat">
      <summary>Gets or sets the default outgoing response format.</summary>
      <returns>The default response format for the endpoint.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebHttpEndpoint.FaultExceptionEnabled">
      <summary>Gets or sets the flag that specifies whether a FaultException is generated when an internal server error (HTTP status code: 500) occurs.</summary>
      <returns>Returns true if the flag is enabled; otherwise returns false.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebHttpEndpoint.HelpEnabled">
      <summary>Gets or sets a value that indicates whether the HTTP help page is enabled for the endpoint.</summary>
      <returns>true if the HTTP help page is enabled; otherwise false.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebHttpEndpoint.WebEndpointType">
      <summary>Gets the <see cref="T:System.Type" /> of the endpoint.</summary>
      <returns>The type of the endpoint.</returns>
    </member>
    <member name="T:System.ServiceModel.Description.WebScriptEnablingBehavior">
      <summary>Provides support for the behavior that enables  endpoints to receive HTTP requests from a browser-based ASP.NET AJAX client. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.ServiceModel.Description.WebScriptEnablingBehavior.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Description.WebScriptEnablingBehavior" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Description.WebScriptEnablingBehavior.ApplyClientBehavior(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Dispatcher.ClientRuntime)">
      <summary>Applies the behavior to the client across an endpoint.</summary>
      <param name="endpoint">The <see cref="T:System.ServiceModel.Description.ServiceEndpoint" /> that exposes the contract.</param>
      <param name="clientRuntime">The <see cref="T:System.ServiceModel.Dispatcher.ClientRuntime" /> to which the custom behavior is applied.</param>
    </member>
    <member name="M:System.ServiceModel.Description.WebScriptEnablingBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Dispatcher.EndpointDispatcher)">
      <summary>Applies the behavior to the service endpoint.</summary>
      <param name="endpoint">The <see cref="T:System.ServiceModel.Description.ServiceEndpoint" /> that exposes the contract.</param>
      <param name="endpointDispatcher">The <see cref="T:System.ServiceModel.Dispatcher.EndpointDispatcher" /> to which the custom behavior is applied.</param>
    </member>
    <member name="P:System.ServiceModel.Description.WebScriptEnablingBehavior.AutomaticFormatSelectionEnabled">
      <summary>Gets or sets a value that determines if automatic format selection is enabled.</summary>
      <returns>true if automatic format selection is enabled; otherwise false.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebScriptEnablingBehavior.DefaultBodyStyle">
      <summary>Gets or sets the default message body style.</summary>
      <returns>The <see cref="F:System.ServiceModel.Web.WebMessageBodyStyle.WrappedRequest" /> value. This default value is the only valid value.</returns>
      <exception cref="T:System.NotSupportedException">The body style set is not supported.</exception>
    </member>
    <member name="P:System.ServiceModel.Description.WebScriptEnablingBehavior.DefaultOutgoingRequestFormat">
      <summary>Gets or sets the default outgoing request message format.</summary>
      <returns>The <see cref="T:System.ServiceModel.Web.WebMessageFormat" /> used by outgoing request messages. The default value is <see cref="F:System.ServiceModel.Web.WebMessageFormat.Json" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The message format set is not a valid value of <see cref="T:System.ServiceModel.Web.WebMessageFormat" />.</exception>
    </member>
    <member name="P:System.ServiceModel.Description.WebScriptEnablingBehavior.DefaultOutgoingResponseFormat">
      <summary>Gets and sets the default outgoing response message format.</summary>
      <returns>The <see cref="T:System.ServiceModel.Web.WebMessageFormat" /> used by outgoing response messages. The default value is <see cref="F:System.ServiceModel.Web.WebMessageFormat.Json" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The message format set is not a valid value of <see cref="T:System.ServiceModel.Web.WebMessageFormat" />.</exception>
    </member>
    <member name="P:System.ServiceModel.Description.WebScriptEnablingBehavior.FaultExceptionEnabled">
      <summary>Gets or sets the flag that specifies whether a FaultException is generated when an internal server error (HTTP status code: 500) occurs.</summary>
      <returns>Returns true if the flag is enabled; otherwise returns false.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebScriptEnablingBehavior.HelpEnabled">
      <summary>Gets or sets a value that determines if the  REST Help page is enabled.</summary>
      <returns>true if the  REST Help page is enabled; otherwise false.</returns>
    </member>
    <member name="M:System.ServiceModel.Description.WebScriptEnablingBehavior.Validate(System.ServiceModel.Description.ServiceEndpoint)">
      <summary>Confirms that the endpoint meets the requirements that allow it to function as an ASP.NET AJAX endpoint.</summary>
      <param name="endpoint">The <see cref="T:System.ServiceModel.Description.ServiceEndpoint" /> that exposes the contract.</param>
      <exception cref="T:System.InvalidOperationException">The endpoint does not meet one of the requirements for being an ASP.NET AJAX endpoint.</exception>
    </member>
    <member name="T:System.ServiceModel.Description.WebScriptEndpoint">
      <summary>A standard endpoint with a fixed <see cref="T:System.ServiceModel.WebHttpBinding" /> binding that automatically adds the <see cref="T:System.ServiceModel.Description.WebScriptEnablingBehavior" /> behavior. </summary>
    </member>
    <member name="M:System.ServiceModel.Description.WebScriptEndpoint.#ctor(System.ServiceModel.Description.ContractDescription)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Description.WebScriptEndpoint" /> class with the specified <see cref="T:System.ServiceModel.Description.ContractDescription" />.</summary>
      <param name="contract">The contract description.</param>
    </member>
    <member name="M:System.ServiceModel.Description.WebScriptEndpoint.#ctor(System.ServiceModel.Description.ContractDescription,System.ServiceModel.EndpointAddress)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Description.WebScriptEndpoint" /> class with the specified <see cref="T:System.ServiceModel.Description.ContractDescription" /> and <see cref="T:System.ServiceModel.EndpointAddress" />.</summary>
      <param name="contract">The contract description.</param>
      <param name="address">The endpoint address.</param>
    </member>
    <member name="P:System.ServiceModel.Description.WebScriptEndpoint.WebEndpointType">
      <summary>Gets the <see cref="T:System.Type" /> of the endpoint.</summary>
      <returns>The type of the endpoint.</returns>
    </member>
    <member name="T:System.ServiceModel.Description.WebServiceEndpoint">
      <summary>A standard endpoint with a fixed <see cref="T:System.ServiceModel.WebHttpBinding" /> binding.</summary>
    </member>
    <member name="P:System.ServiceModel.Description.WebServiceEndpoint.ContentTypeMapper">
      <summary>Gets or sets the <see cref="T:System.ServiceModel.Channels.WebContentTypeMapper" /> associated with the <see cref="T:System.ServiceModel.Description.WebServiceEndpoint" />.</summary>
      <returns>The Web content type mapper.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebServiceEndpoint.CrossDomainScriptAccessEnabled">
      <summary>Gets or sets a value that indicates whether cross domain script access is enabled for the <see cref="T:System.ServiceModel.Description.WebServiceEndpoint" />.</summary>
      <returns>true if cross domain script access is enabled, otherwise false.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebServiceEndpoint.HostNameComparisonMode">
      <summary>Gets or sets a value that indicates whether the hostname is used to reach the service when matching the URI.</summary>
      <returns>The value that indicates whether the hostname is used to reach the service when matching on the URI. The default value is <see cref="F:System.ServiceModel.HostnameComparisonMode.StrongWildcard" />, which ignores the hostname in the match.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value set is not a valid <see cref="T:System.ServiceModel.HostnameComparisonMode" /> value.</exception>
    </member>
    <member name="P:System.ServiceModel.Description.WebServiceEndpoint.MaxBufferPoolSize">
      <summary>Gets or sets the maximum amount of memory allocated for the buffer manager that manages the buffers required by endpoints that use this binding.</summary>
      <returns>The maximum size, in bytes, for the pool of buffers used by an endpoint configured with this binding. The default value is 65,536 bytes.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebServiceEndpoint.MaxBufferSize">
      <summary>Gets or sets the maximum amount of memory that is allocated for use by the manager of the message buffers that receive messages from the channel.</summary>
      <returns>The maximum amount of memory, in bytes, available for use by the message buffer manager. The default value is 524,288 (0x80000) bytes.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value set is less than or equal to zero.</exception>
    </member>
    <member name="P:System.ServiceModel.Description.WebServiceEndpoint.MaxReceivedMessageSize">
      <summary>Gets or sets the maximum size for a message that can be processed by the binding.</summary>
      <returns>The maximum size, in bytes, for a message that is processed by the binding. The default value is 65,536 bytes.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value is less than zero.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">A message exceeded the maximum size allocated.</exception>
    </member>
    <member name="P:System.ServiceModel.Description.WebServiceEndpoint.ReaderQuotas">
      <summary>Gets or sets constraints on the complexity of SOAP messages that can be processed by endpoints configured with this binding.</summary>
      <returns>The complexity constraints.</returns>
      <exception cref="T:System.ArgumentNullException">The value set is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The quota values of <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> are read only.</exception>
      <exception cref="T:System.ArgumentException">The quotas set must be positive.</exception>
    </member>
    <member name="P:System.ServiceModel.Description.WebServiceEndpoint.Security">
      <summary>Gets the security settings used with this binding.</summary>
      <returns>The <see cref="T:System.ServiceModel.WebHttpSecurity" /> that is used with this binding. The default value is <see cref="F:System.ServiceModel.WebHttpSecurityMode.None" />.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebServiceEndpoint.TransferMode">
      <summary>Gets or sets a value that indicates whether the service configured with the binding uses streamed or buffered (or both) modes of message transfer.</summary>
      <returns>The <see cref="T:System.ServiceModel.TransferMode" /> value that indicates whether the service configured with the binding uses streamed or buffered (or both) modes of message transfer. The default value is <see cref="F:System.ServiceModel.TransferMode.Buffered" />.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value set is not a valid <see cref="T:System.ServiceModel.TransferMode" /> value.</exception>
    </member>
    <member name="P:System.ServiceModel.Description.WebServiceEndpoint.WebEndpointType">
      <summary>Gets the of the Web service endpoint.</summary>
      <returns>The type of Web service endpoint.</returns>
    </member>
    <member name="P:System.ServiceModel.Description.WebServiceEndpoint.WriteEncoding">
      <summary>Gets or sets the character encoding that is used for the message text.</summary>
      <returns>The character encoding that is used. The default is <see cref="T:System.Text.UTF8Encoding" />.</returns>
      <exception cref="T:System.ArgumentNullException">The value set is null.</exception>
    </member>
    <member name="T:System.ServiceModel.Dispatcher.JsonQueryStringConverter">
      <summary>This class converts a parameter value to and from a JavaScript Object Notation (JSON). </summary>
    </member>
    <member name="M:System.ServiceModel.Dispatcher.JsonQueryStringConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Dispatcher.JsonQueryStringConverter" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Dispatcher.JsonQueryStringConverter.CanConvert(System.Type)">
      <summary>Gets a value that indicates whether a Common Language Runtime (CLR) type specified is a known type that can be serialized and deserialized.</summary>
      <returns>true if the type can be serialized; otherwise false.</returns>
      <param name="type">The <see cref="T:System.Type" /> to verify.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="type" /> is null.</exception>
    </member>
    <member name="M:System.ServiceModel.Dispatcher.JsonQueryStringConverter.ConvertStringToValue(System.String,System.Type)">
      <summary>Deserializes a JavaScript Object Notation (JSON) query string parameter to a specified Common Language Runtime (CLR) type.</summary>
      <returns>An instance of the CLR type to which the parameter value was converted.</returns>
      <param name="parameter">The JSON form of the parameter value.</param>
      <param name="parameterType">The <see cref="T:System.Type" /> to deserialize the parameter to.</param>
    </member>
    <member name="M:System.ServiceModel.Dispatcher.JsonQueryStringConverter.ConvertValueToString(System.Object,System.Type)">
      <summary>Serializes a Common Language Runtime (CLR) parameter type to a JavaScript Object Notation (JSON) representation.</summary>
      <returns>The JSON query string parameter serialization of the CLR type. null is returned if the parameter is null.</returns>
      <param name="parameter">The parameter value to convert.</param>
      <param name="parameterType">The <see cref="T:System.Type" /> of the parameter to serialize.</param>
    </member>
    <member name="T:System.ServiceModel.Dispatcher.QueryStringConverter">
      <summary>This class converts a parameter in a query string to an object of the appropriate type. It can also convert a parameter from an object to its query string representation. </summary>
    </member>
    <member name="M:System.ServiceModel.Dispatcher.QueryStringConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Dispatcher.QueryStringConverter" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Dispatcher.QueryStringConverter.CanConvert(System.Type)">
      <summary>Determines whether the specified type can be converted to and from a string representation.</summary>
      <returns>A value that specifies whether the type can be converted.</returns>
      <param name="type">The <see cref="T:System.Type" /> to convert.</param>
    </member>
    <member name="M:System.ServiceModel.Dispatcher.QueryStringConverter.ConvertStringToValue(System.String,System.Type)">
      <summary>Converts a query string parameter to the specified type.</summary>
      <returns>The converted parameter.</returns>
      <param name="parameter">The string form of the parameter and value.</param>
      <param name="parameterType">The <see cref="T:System.Type" /> to convert the parameter to.</param>
      <exception cref="T:System.FormatException">The provided string does not have the correct format.</exception>
    </member>
    <member name="M:System.ServiceModel.Dispatcher.QueryStringConverter.ConvertValueToString(System.Object,System.Type)">
      <summary>Converts a parameter to a query string representation.</summary>
      <returns>The parameter name and value.</returns>
      <param name="parameter">The parameter to convert.</param>
      <param name="parameterType">The <see cref="T:System.Type" /> of the parameter to convert.</param>
    </member>
    <member name="T:System.ServiceModel.Dispatcher.WebHttpDispatchOperationSelector">
      <summary>The operation selector that supports the Web programming model.</summary>
    </member>
    <member name="M:System.ServiceModel.Dispatcher.WebHttpDispatchOperationSelector.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Dispatcher.WebHttpDispatchOperationSelector" />.</summary>
    </member>
    <member name="M:System.ServiceModel.Dispatcher.WebHttpDispatchOperationSelector.#ctor(System.ServiceModel.Description.ServiceEndpoint)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Dispatcher.WebHttpDispatchOperationSelector" /> with the specified endpoint.</summary>
      <param name="endpoint">The service endpoint.</param>
    </member>
    <member name="M:System.ServiceModel.Dispatcher.WebHttpDispatchOperationSelector.GetUriTemplate(System.String)">
      <summary>Gets the <see cref="T:System.UriTemplate" /> associated with the specified operation name.</summary>
      <returns>The <see cref="T:System.UriTemplate" /> for the specified operation.</returns>
      <param name="operationName">The operation.</param>
    </member>
    <member name="F:System.ServiceModel.Dispatcher.WebHttpDispatchOperationSelector.HttpOperationNamePropertyName">
      <summary>The name of the message property on the request message that provides the name of the selected operation for the request.</summary>
    </member>
    <member name="F:System.ServiceModel.Dispatcher.WebHttpDispatchOperationSelector.HttpOperationSelectorUriMatchedPropertyName">
      <summary>A string used as a key for storing the value that indicates whether a call to a service operation was matched by the URI but not by the HTTP method.</summary>
    </member>
    <member name="M:System.ServiceModel.Dispatcher.WebHttpDispatchOperationSelector.SelectOperation(System.ServiceModel.Channels.Message@)">
      <summary>Selects the service operation to call.</summary>
      <returns>The name of the service operation to call.</returns>
      <param name="message">The <see cref="T:System.ServiceModel.Channels.Message" /> object sent to invoke a service operation.</param>
    </member>
    <member name="M:System.ServiceModel.Dispatcher.WebHttpDispatchOperationSelector.SelectOperation(System.ServiceModel.Channels.Message@,System.Boolean@)">
      <summary>Selects the service operation to call.</summary>
      <returns>The name of the service operation to call.</returns>
      <param name="message">The <see cref="T:System.ServiceModel.Channels.Message" /> object sent to invoke a service operation.</param>
      <param name="uriMatched">A value that specifies whether the URI matched a specific service operation.</param>
    </member>
    <member name="T:System.ServiceModel.Web.AspNetCacheProfileAttribute">
      <summary>When applied to a service operation, indicates the ASP.NET output cache profile in the configuration file that should be used by  to cache responses from the operation in the ASP .NET Output Cache. </summary>
    </member>
    <member name="M:System.ServiceModel.Web.AspNetCacheProfileAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.AspNetCacheProfileAttribute" /> class with the specified cache profile name.</summary>
      <param name="cacheProfileName">The cache profile name.</param>
    </member>
    <member name="M:System.ServiceModel.Web.AspNetCacheProfileAttribute.AddBindingParameters(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Channels.BindingParameterCollection)">
      <summary> An implementation of the <see cref="M:System.ServiceModel.Description.IOperationBehavior.AddBindingParmeters(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Channels.BindingParameterCollection)" />. Used by the  infrastructure and is not intended to be used by developers.</summary>
      <param name="operationDescription">The operation description.</param>
      <param name="bindingParameters">The binding parameters.</param>
    </member>
    <member name="M:System.ServiceModel.Web.AspNetCacheProfileAttribute.ApplyClientBehavior(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Dispatcher.ClientOperation)">
      <summary>An implementation of the <see cref="M:System.ServiceModel.Description.IOperationBehavior.ApplyClientBehavior(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Dispatcher.ClientOperation)" /> method. Used by the  infrastructure and is not intended to be used by developers.</summary>
      <param name="operationDescription">The operation description.</param>
      <param name="clientOperation">The client operation..</param>
    </member>
    <member name="M:System.ServiceModel.Web.AspNetCacheProfileAttribute.ApplyDispatchBehavior(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Dispatcher.DispatchOperation)">
      <summary>An implementation of the <see cref="M:System.ServiceModel.Description.IOperationBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Dispatcher.DispatchOperation)" /> method. Used by the  infrastructure and is not intended to be used by developers. </summary>
      <param name="operationDescription">The operation being examined.</param>
      <param name="dispatchOperation">The run-time object that exposes customization properties for the operation described by <paramref name="operationDescription" />.</param>
    </member>
    <member name="P:System.ServiceModel.Web.AspNetCacheProfileAttribute.CacheProfileName">
      <summary>Gets the name of the cache profile.</summary>
      <returns>The name of the cache profile.</returns>
    </member>
    <member name="M:System.ServiceModel.Web.AspNetCacheProfileAttribute.Validate(System.ServiceModel.Description.OperationDescription)">
      <summary>An implementation of the <see cref="M:System.ServiceModel.Description.IOperationBehavior.Validate(System.ServiceModel.Description.OperationDescription)" /> method. Used by the  infrastructure and is not intended to be used by developers.</summary>
      <param name="operationDescription">Not implemented.</param>
    </member>
    <member name="T:System.ServiceModel.Web.IncomingWebRequestContext">
      <summary>Provides programmatic access to the context of the incoming Web request.</summary>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebRequestContext.Accept">
      <summary>Gets the Accept header value from the incoming Web request.</summary>
      <returns>The Accept header from the incoming Web request.</returns>
    </member>
    <member name="M:System.ServiceModel.Web.IncomingWebRequestContext.CheckConditionalRetrieve(System.DateTime)">
      <summary>Called when a conditional receive request is made for a resource.</summary>
      <param name="lastModified">The time at which the resource was last modified.</param>
    </member>
    <member name="M:System.ServiceModel.Web.IncomingWebRequestContext.CheckConditionalRetrieve(System.Guid)">
      <summary>Called when a conditional receive request is made for a resource.</summary>
      <param name="entityTag">An entity tag.</param>
    </member>
    <member name="M:System.ServiceModel.Web.IncomingWebRequestContext.CheckConditionalRetrieve(System.Int32)">
      <summary>Called when a conditional receive request is made for a resource.</summary>
      <param name="entityTag">An entity tag.</param>
    </member>
    <member name="M:System.ServiceModel.Web.IncomingWebRequestContext.CheckConditionalRetrieve(System.Int64)">
      <summary>Called when a conditional receive request is made for a resource.</summary>
      <param name="entityTag">The entity tag.</param>
    </member>
    <member name="M:System.ServiceModel.Web.IncomingWebRequestContext.CheckConditionalRetrieve(System.String)">
      <summary>Called when a conditional receive request is made for a resource.</summary>
      <param name="entityTag">The entity tag.</param>
    </member>
    <member name="M:System.ServiceModel.Web.IncomingWebRequestContext.CheckConditionalUpdate(System.Guid)">
      <summary>Called when a conditional receive request is made for a resource.</summary>
      <param name="entityTag">The entity tag.</param>
    </member>
    <member name="M:System.ServiceModel.Web.IncomingWebRequestContext.CheckConditionalUpdate(System.Int32)">
      <summary>Called when a conditional update request is made for a resource.</summary>
      <param name="entityTag">The entity tag.</param>
    </member>
    <member name="M:System.ServiceModel.Web.IncomingWebRequestContext.CheckConditionalUpdate(System.Int64)">
      <summary>Called when a conditional update request is made for a resource.</summary>
      <param name="entityTag">The entity tag.</param>
    </member>
    <member name="M:System.ServiceModel.Web.IncomingWebRequestContext.CheckConditionalUpdate(System.String)">
      <summary>Called when a conditional update request is made for a resource.</summary>
      <param name="entityTag">The entity tag.</param>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebRequestContext.ContentLength">
      <summary>Gets the ContentLength header value of the incoming Web request.</summary>
      <returns>The  ContentLength header of the incoming Web request.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebRequestContext.ContentType">
      <summary>Gets the ContentType header value from the incoming Web request.</summary>
      <returns>The ContentType header from the incoming Web request.</returns>
    </member>
    <member name="M:System.ServiceModel.Web.IncomingWebRequestContext.GetAcceptHeaderElements">
      <summary>Gets a collection of the Accept header elements.</summary>
      <returns>A collection of the Accept header elements.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebRequestContext.Headers">
      <summary>Gets the headers for the incoming Web request.</summary>
      <returns>A <see cref="T:System.Net.WebHeaderCollection" /> instance that contains the headers of the incoming Web request.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebRequestContext.IfMatch">
      <summary>Gets a collection of the items contained in the requests If-Match header.</summary>
      <returns>A collection of items contained in the requests If-Match header.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebRequestContext.IfModifiedSince">
      <summary>Gets the value of the request’s If-Modified-Since header.</summary>
      <returns>The request’s If-Modified-Since header value.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebRequestContext.IfNoneMatch">
      <summary>Gets the values contained in the request’s If-None-Match header.</summary>
      <returns>The values contained in the request’s If-None-Match header.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebRequestContext.IfUnmodifiedSince">
      <summary>Gets the value of the request’s If-Unmatched-Since header.</summary>
      <returns>The request’s If-Unmatched-Since header..</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebRequestContext.Method">
      <summary>Gets the HTTP method of the incoming Web request.</summary>
      <returns>The HTTP method of the incoming Web request.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebRequestContext.UriTemplateMatch">
      <summary>Gets and sets the <see cref="T:System.UriTemplateMatch" /> instance created during the dispatch of the incoming Web request.</summary>
      <returns>A <see cref="T:System.UriTemplateMatch" /> instance.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebRequestContext.UserAgent">
      <summary>Gets the UserAgent header value from the incoming Web request.</summary>
      <returns>The UserAgent header from the incoming Web request.</returns>
    </member>
    <member name="T:System.ServiceModel.Web.IncomingWebResponseContext">
      <summary>Provides programmatic access to the context of the incoming Web response.</summary>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebResponseContext.ContentLength">
      <summary>Gets the content length header from the incoming Web response.</summary>
      <returns>The content length of the incoming Web response.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebResponseContext.ContentType">
      <summary>Gets the content type header from the incoming Web response.</summary>
      <returns>The content type header of the incoming Web response.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebResponseContext.ETag">
      <summary>Gets the etag header from the incoming Web response.</summary>
      <returns>The etag header of the incoming Web response.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebResponseContext.Headers">
      <summary>Gets the headers from the incoming Web response.</summary>
      <returns>A <see cref="T:System.Net.WebHeaderCollection" /> instance that contains the headers from the incoming Web response.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebResponseContext.Location">
      <summary>Gets the location header from the incoming Web response.</summary>
      <returns>The location header from the incoming Web response.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebResponseContext.StatusCode">
      <summary>Gets the status code of the incoming Web response.</summary>
      <returns>A <see cref="T:System.Net.HttpStatusCode" /> instance that contains the status code of the incoming Web response.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.IncomingWebResponseContext.StatusDescription">
      <summary>Gets the status description of the incoming Web response.</summary>
      <returns>The status description of the incoming Web response.</returns>
    </member>
    <member name="T:System.ServiceModel.Web.JavascriptCallbackBehaviorAttribute">
      <summary>A contract behavior that allows you to set the URL query string parameter name to something other than the default “callback”.</summary>
    </member>
    <member name="M:System.ServiceModel.Web.JavascriptCallbackBehaviorAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.JavascriptCallbackBehaviorAttribute" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Web.JavascriptCallbackBehaviorAttribute.AddBindingParameters(System.ServiceModel.Description.ContractDescription,System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Channels.BindingParameterCollection)">
      <summary>An implementation of the <see cref="M:System.ServiceModel.Description.IContractBehavior.AddBindingParameters(System.ServiceModel.Description.ContractDescription,System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Channels.BindingParameterCollection)" /> method.</summary>
      <param name="contractDescription">The contract description.</param>
      <param name="endpoint">The service endpoint.</param>
      <param name="bindingParameters">The binding parameters required to implement the behavior.</param>
    </member>
    <member name="M:System.ServiceModel.Web.JavascriptCallbackBehaviorAttribute.ApplyClientBehavior(System.ServiceModel.Description.ContractDescription,System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Dispatcher.ClientRuntime)">
      <summary>An implementation of the <see cref="M:System.ServiceModel.Description.IContractBehavior.ApplyClientBehavior(System.ServiceModel.Description.ContractDescription,System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Dispatcher.ClientRuntime)" /> method.</summary>
      <param name="contractDescription">The contract description.</param>
      <param name="endpoint">The service endpoint.</param>
      <param name="clientRuntime">The client runtime.</param>
    </member>
    <member name="M:System.ServiceModel.Web.JavascriptCallbackBehaviorAttribute.ApplyDispatchBehavior(System.ServiceModel.Description.ContractDescription,System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Dispatcher.DispatchRuntime)">
      <summary>An implementation of the <see cref="M:System.ServiceModel.Description.IContractBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.ContractDescription,System.ServiceModel.Description.ServiceEndpoint,System.ServiceModel.Dispatcher.DispatchRuntime)" /> method.</summary>
      <param name="contractDescription">The contract description.</param>
      <param name="endpoint">The service endpoint.</param>
      <param name="dispatchRuntime">The dispatch runtime.</param>
    </member>
    <member name="P:System.ServiceModel.Web.JavascriptCallbackBehaviorAttribute.UrlParameterName">
      <summary>Gets or sets the URL query string parameter name to use for cross-domain script access.</summary>
      <returns>The URL query string parameter name.</returns>
    </member>
    <member name="M:System.ServiceModel.Web.JavascriptCallbackBehaviorAttribute.Validate(System.ServiceModel.Description.ContractDescription,System.ServiceModel.Description.ServiceEndpoint)">
      <summary>An implementation of the <see cref="M:System.ServiceModel.Description.IContractBehavior.Validate(System.ServiceModel.Description.ContractDescription,System.ServiceModel.Description.ServiceEndpoint)" /> method.</summary>
      <param name="contractDescription">The contract description.</param>
      <param name="endpoint">The service endpoint.</param>
    </member>
    <member name="T:System.ServiceModel.Web.OutgoingWebRequestContext">
      <summary>Provides programmatic access to the context of the outgoing Web request.</summary>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebRequestContext.Accept">
      <summary>Gets and sets the Accept header value from the outgoing Web request.</summary>
      <returns>The Accept header from the outgoing Web request.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebRequestContext.ContentLength">
      <summary>Gets and sets the content length header value of the outgoing Web request.</summary>
      <returns>The content length header of the outgoing Web request.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebRequestContext.ContentType">
      <summary>Gets and sets the content type header value from the outgoing Web request.</summary>
      <returns>The content type header from the outgoing Web request.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebRequestContext.Headers">
      <summary>Gets the headers for the outgoing Web request.</summary>
      <returns>A <see cref="T:System.Net.WebHeaderCollection" /> instance that contains the headers of the outgoing Web request.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebRequestContext.IfMatch">
      <summary>Gets and sets the IfMatch header value from the outgoing Web request.</summary>
      <returns>The IfMatch header from the outgoing Web request.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebRequestContext.IfModifiedSince">
      <summary>Gets and sets the IfModifiedSince header value from the outgoing Web request.</summary>
      <returns>The IfModifiedSince header from the outgoing Web request.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebRequestContext.IfNoneMatch">
      <summary>Gets and sets the IfNoneMatch header value from the outgoing Web request.</summary>
      <returns>The IfNoneMatch header from the outgoing Web request.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebRequestContext.IfUnmodifiedSince">
      <summary>Gets and sets the IfUnmodifiedSince header value from the outgoing Web request.</summary>
      <returns>The IfUnmodifiedSince header from the outgoing Web request.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebRequestContext.Method">
      <summary>Gets the HTTP method of the outgoing Web request.</summary>
      <returns>The HTTP method of the outgoing Web request.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebRequestContext.SuppressEntityBody">
      <summary>Gets a value that indicates whether  omits data that is normally written to the entity body of the response and forces an empty response to be returned.</summary>
      <returns>If true,  omits any data that is normally written to the entity body of the response and forces an empty response to be returned.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebRequestContext.UserAgent">
      <summary>Gets the user agent header value from the outgoing Web request.</summary>
      <returns>The user agent header from the outgoing Web request.</returns>
    </member>
    <member name="T:System.ServiceModel.Web.OutgoingWebResponseContext">
      <summary>Provides programmatic access to the context of the outgoing Web response.</summary>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebResponseContext.BindingWriteEncoding">
      <summary>Gets the encoding set on the binding.</summary>
      <returns>The encoding.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebResponseContext.ContentLength">
      <summary>Gets and sets the content length header from the outgoing Web response.</summary>
      <returns>The content length header of the outgoing Web response.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebResponseContext.ContentType">
      <summary>Gets and sets the content type header from the outgoing Web response.</summary>
      <returns>The content type header of the outgoing Web response.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebResponseContext.ETag">
      <summary>Gets and sets the etag header from the outgoing Web response.</summary>
      <returns>The etag header of the outgoing Web response.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebResponseContext.Format">
      <summary>Gets or sets the web message format</summary>
      <returns>The web message format.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebResponseContext.Headers">
      <summary>Gets the headers from the outgoing Web response.</summary>
      <returns>A <see cref="T:System.Net.WebHeaderCollection" /> instance that contains the headers from the outgoing Web response.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebResponseContext.LastModified">
      <summary>Gets and sets the last modified header of the outgoing Web response.</summary>
      <returns>A <see cref="T:System.DateTime" /> instance that contains the time the requested resource was last modified.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebResponseContext.Location">
      <summary>Gets and sets the location header from the outgoing Web response.</summary>
      <returns>The location header from the outgoing Web response.</returns>
    </member>
    <member name="M:System.ServiceModel.Web.OutgoingWebResponseContext.SetETag(System.Guid)">
      <summary>Sets the specified ETag.</summary>
      <param name="entityTag">The ETag to set.</param>
    </member>
    <member name="M:System.ServiceModel.Web.OutgoingWebResponseContext.SetETag(System.Int32)">
      <summary>Sets the specified ETag.</summary>
      <param name="entityTag">The ETag to set.</param>
    </member>
    <member name="M:System.ServiceModel.Web.OutgoingWebResponseContext.SetETag(System.Int64)">
      <summary>Sets the specified ETag.</summary>
      <param name="entityTag">The ETag to set.</param>
    </member>
    <member name="M:System.ServiceModel.Web.OutgoingWebResponseContext.SetETag(System.String)">
      <summary>Sets the specified ETag.</summary>
      <param name="entityTag">The ETag to set.</param>
    </member>
    <member name="M:System.ServiceModel.Web.OutgoingWebResponseContext.SetStatusAsCreated(System.Uri)">
      <summary>Sets the HTTP status code of the outgoing Web response to <see cref="F:System.Net.HttpStatusCode.Created" /> and sets the Location header to the provided URI.</summary>
      <param name="locationUri">The <see cref="T:System.Uri" /> instance to the requested resource.</param>
    </member>
    <member name="M:System.ServiceModel.Web.OutgoingWebResponseContext.SetStatusAsNotFound">
      <summary>Sets the HTTP status code of the outgoing Web response to <see cref="F:System.Net.HttpStatusCode.NotFound" />.</summary>
    </member>
    <member name="M:System.ServiceModel.Web.OutgoingWebResponseContext.SetStatusAsNotFound(System.String)">
      <summary>Sets the HTTP status code of the outgoing Web response to <see cref="F:System.Net.HttpStatusCode.NotFound" /> with the specified description.</summary>
      <param name="description">The description of status.</param>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebResponseContext.StatusCode">
      <summary>Gets and sets the status code of the outgoing Web response.</summary>
      <returns>An <see cref="T:System.Net.HttpStatusCode" /> instance that contains the status code of the outgoing Web response.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebResponseContext.StatusDescription">
      <summary>Gets and sets the status description of the outgoing Web response.</summary>
      <returns>The status description of the outgoing Web response.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.OutgoingWebResponseContext.SuppressEntityBody">
      <summary>Gets and sets a value that indicates whether  omits data that is normally written to the entity body of the response and forces an empty response to be returned.</summary>
      <returns>If true,  omits any data that is normally written to the entity body of the response and forces an empty response to be returned. The default value is false.</returns>
    </member>
    <member name="T:System.ServiceModel.Web.WebChannelFactory`1">
      <summary>A class for accessing  Web services on a client.</summary>
      <typeparam name="TChannel">The type of channel to create.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Web.WebChannelFactory`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebChannelFactory`1" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Web.WebChannelFactory`1.#ctor(System.ServiceModel.Channels.Binding)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebChannelFactory`1" /> class.</summary>
      <param name="binding">The binding to use when creating the channel.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebChannelFactory`1.#ctor(System.ServiceModel.Channels.Binding,System.Uri)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebChannelFactory`1" /> class with the specified binding and <see cref="T:System.Uri" />.</summary>
      <param name="binding">The binding to use.</param>
      <param name="remoteAddress">The URI of the Web service that is called.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebChannelFactory`1.#ctor(System.ServiceModel.Description.ServiceEndpoint)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebChannelFactory`1" /> class.</summary>
      <param name="endpoint">The endpoint to use when creating the channel.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebChannelFactory`1.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebChannelFactory`1" /> class.</summary>
      <param name="endpointConfigurationName">The name within the application configuration file where the channel is configured.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebChannelFactory`1.#ctor(System.String,System.Uri)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebChannelFactory`1" /> class with the specified endpoint configuration and <see cref="T:System.Uri" />.</summary>
      <param name="endpointConfigurationName">The name within the application configuration file where the channel is configured.</param>
      <param name="remoteAddress">The URI of the Web service that is called.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebChannelFactory`1.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebChannelFactory`1" /> class.</summary>
      <param name="channelType">The channel type to use.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebChannelFactory`1.#ctor(System.Uri)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebChannelFactory`1" /> class with the specified <see cref="T:System.Uri" />.</summary>
      <param name="remoteAddress">The URI of the Web service that is called.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebChannelFactory`1.OnOpening">
      <summary>This method is called when the <see cref="T:System.ServiceModel.Web.WebChannelFactory`1" /> is opened.</summary>
    </member>
    <member name="T:System.ServiceModel.Web.WebFaultException">
      <summary>Represents a fault that can have an associated HTTP status code. </summary>
    </member>
    <member name="M:System.ServiceModel.Web.WebFaultException.#ctor(System.Net.HttpStatusCode)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebFaultException" /> class with the specified <see cref="T:System.Net.HttpStatusCode" />.</summary>
      <param name="statusCode">The HTTP status code to return to the caller.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebFaultException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="M:System.ServiceModel.Web.WebFaultException" /> class with the specified <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />.</summary>
      <param name="info">The serialization information.</param>
      <param name="context">The streaming context.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebFaultException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>An implementation of the <see cref="M:System.Runtime.Serialization.ISerializable.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> method that is called when an object is serialized to a stream. </summary>
      <param name="info">The serialization information to which the object data is added when serialized.</param>
      <param name="context">The destination for the serialized object.</param>
    </member>
    <member name="P:System.ServiceModel.Web.WebFaultException.StatusCode">
      <summary>Gets the <see cref="T:System.Net.HttpStatusCode" /> associated with the fault.</summary>
      <returns>The HTTP status code associated with the fault.</returns>
    </member>
    <member name="T:System.ServiceModel.Web.WebFaultException`1">
      <summary>Represents a fault that can have an associated HTTP status code.</summary>
      <typeparam name="T">The serializable error detail type.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Web.WebFaultException`1.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebFaultException`1" /> class with the specified <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" /></summary>
      <param name="info">The serialization info.</param>
      <param name="context">The streaming context.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebFaultException`1.#ctor(`0,System.Net.HttpStatusCode)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebFaultException`1" /> class with the specified exception detail and the <see cref="M:System.ServiceModel.Web.WebFaultException.#ctor(System.Net.HttpStatusCode)" /> to return to the caller.</summary>
      <param name="detail">The fault.</param>
      <param name="statusCode">The HTTP status code to set on the response message.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebFaultException`1.#ctor(`0,System.Net.HttpStatusCode,System.Collections.Generic.IEnumerable{System.Type})">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebFaultException`1" /> class with the specified exception detail and the <see cref="P:System.ServiceModel.Web.WebFaultException`1.StatusCode" /> to return to the caller.</summary>
      <param name="detail">The fault.</param>
      <param name="statusCode">The HTTP status code to set on the response message.</param>
      <param name="knownTypes">The set of known types that are used for serialization and deserialization.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebFaultException`1.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>An implementation of the <see cref="M:System.Runtime.Serialization.ISerializable.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> method that is called when an object is serialized to a stream.</summary>
      <param name="info">The serialization information to which the object data is added when serialized.</param>
      <param name="context">The destination for the serialized object.</param>
    </member>
    <member name="P:System.ServiceModel.Web.WebFaultException`1.StatusCode">
      <summary>Gets the <see cref="T:System.Net.HttpStatusCode" /> associated with the fault.</summary>
      <returns>The HTTP status code associated with the fault.</returns>
    </member>
    <member name="T:System.ServiceModel.Web.WebGetAttribute">
      <summary>Indicates that a service operation is logically a retrieval operation and that it can be called by the  REST programming model.</summary>
    </member>
    <member name="M:System.ServiceModel.Web.WebGetAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebGetAttribute" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Web.WebGetAttribute.BodyStyle">
      <summary>Gets and sets the body style of the messages that are sent to and from the service operation.</summary>
      <returns>One of the <see cref="T:System.ServiceModel.Web.WebMessageBodyStyle" /> enumeration values.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.WebGetAttribute.IsBodyStyleSetExplicitly">
      <summary>Gets the <see cref="P:System.ServiceModel.Web.WebGetAttribute.IsBodyStyleSetExplicitly" /> property.</summary>
      <returns>A value that specifies whether the <see cref="P:System.ServiceModel.Web.WebGetAttribute.BodyStyle" /> property is set.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.WebGetAttribute.IsRequestFormatSetExplicitly">
      <summary>Gets the <see cref="P:System.ServiceModel.Web.WebGetAttribute.IsRequestFormatSetExplicitly" /> property.</summary>
      <returns>A value that specifies whether the <see cref="P:System.ServiceModel.Web.WebGetAttribute.RequestFormat" /> property was set.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.WebGetAttribute.IsResponseFormatSetExplicitly">
      <summary>Gets the <see cref="P:System.ServiceModel.Web.WebGetAttribute.IsResponseFormatSetExplicitly" /> property.</summary>
      <returns>A value that specifies whether the <see cref="P:System.ServiceModel.Web.WebGetAttribute.ResponseFormat" /> property was set.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.WebGetAttribute.RequestFormat">
      <summary>Gets and sets the <see cref="P:System.ServiceModel.Web.WebGetAttribute.RequestFormat" /> property.</summary>
      <returns>One of the <see cref="T:System.ServiceModel.Web.WebMessageFormat" /> enumeration values.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.WebGetAttribute.ResponseFormat">
      <summary>Gets and sets the <see cref="P:System.ServiceModel.Web.WebGetAttribute.ResponseFormat" /> property.</summary>
      <returns>One of the <see cref="T:System.ServiceModel.Web.WebMessageFormat" /> enumeration values.</returns>
    </member>
    <member name="M:System.ServiceModel.Web.WebGetAttribute.System#ServiceModel#Description#IOperationBehavior#AddBindingParameters(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Channels.BindingParameterCollection)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IOperationBehavior.AddBindingParameters(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Channels.BindingParameterCollection)" /> method.</summary>
      <param name="operationDescription">The operation description.</param>
      <param name="bindingParameters">A collection of binding parameters.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebGetAttribute.System#ServiceModel#Description#IOperationBehavior#ApplyClientBehavior(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Dispatcher.ClientOperation)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IOperationBehavior.ApplyClientBehavior(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Dispatcher.ClientOperation)" /> method.</summary>
      <param name="operationDescription">The operation description.</param>
      <param name="clientOperation">The client operation.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebGetAttribute.System#ServiceModel#Description#IOperationBehavior#ApplyDispatchBehavior(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Dispatcher.DispatchOperation)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IOperationBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Dispatcher.DispatchOperation)" /> method.</summary>
      <param name="operationDescription">The operation description.</param>
      <param name="dispatchOperation">The operation to dispatch to.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebGetAttribute.System#ServiceModel#Description#IOperationBehavior#Validate(System.ServiceModel.Description.OperationDescription)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IOperationBehavior.Validate(System.ServiceModel.Description.OperationDescription)" /> method.</summary>
      <param name="operationDescription">The operation description.</param>
    </member>
    <member name="P:System.ServiceModel.Web.WebGetAttribute.UriTemplate">
      <summary>Gets and sets the Uniform Resource Identifier (URI) template for the service operation.</summary>
      <returns>The URI template for the service operation.</returns>
    </member>
    <member name="T:System.ServiceModel.Web.WebInvokeAttribute">
      <summary>Indicates a service operation is logically an invoke operation and that it can be called by the  REST programming model.</summary>
    </member>
    <member name="M:System.ServiceModel.Web.WebInvokeAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebGetAttribute" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Web.WebInvokeAttribute.BodyStyle">
      <summary>Gets and sets the body style of the messages that are sent to and from the service operation.</summary>
      <returns>One of the <see cref="T:System.ServiceModel.Web.WebMessageBodyStyle" /> enumeration values.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.WebInvokeAttribute.IsBodyStyleSetExplicitly">
      <summary>Gets the <see cref="P:System.ServiceModel.Web.WebInvokeAttribute.IsBodyStyleSetExplicitly" /> property.</summary>
      <returns>A value that specifies whether the <see cref="P:System.ServiceModel.Web.WebGetAttribute.BodyStyle" /> property was set explicitly.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.WebInvokeAttribute.IsRequestFormatSetExplicitly">
      <summary>Gets the <see cref="P:System.ServiceModel.Web.WebInvokeAttribute.IsRequestFormatSetExplicitly" /> property.</summary>
      <returns>A value that specifies whether the <see cref="P:System.ServiceModel.Web.WebGetAttribute.RequestFormat" /> property was set.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.WebInvokeAttribute.IsResponseFormatSetExplicitly">
      <summary>Gets the <see cref="P:System.ServiceModel.Web.WebInvokeAttribute.IsResponseFormatSetExplicitly" /> property.</summary>
      <returns>A value that specifies whether the <see cref="P:System.ServiceModel.Web.WebGetAttribute.ResponseFormat" /> property was set.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.WebInvokeAttribute.Method">
      <summary>Gets and sets the protocol (for example HTTP) method the service operation responds to.</summary>
      <returns>The protocol method associated with the operation.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.WebInvokeAttribute.RequestFormat">
      <summary>Gets and sets the <see cref="P:System.ServiceModel.Web.WebInvokeAttribute.RequestFormat" /> property.</summary>
      <returns>One of the <see cref="T:System.ServiceModel.Web.WebMessageFormat" /> enumeration values.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.WebInvokeAttribute.ResponseFormat">
      <summary>Gets and sets the <see cref="P:System.ServiceModel.Web.WebInvokeAttribute.ResponseFormat" /> property.</summary>
      <returns>One of the <see cref="T:System.ServiceModel.Web.WebMessageFormat" /> enumeration values.</returns>
    </member>
    <member name="M:System.ServiceModel.Web.WebInvokeAttribute.System#ServiceModel#Description#IOperationBehavior#AddBindingParameters(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Channels.BindingParameterCollection)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IOperationBehavior.AddBindingParameters(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Channels.BindingParameterCollection)" /> method.</summary>
      <param name="operationDescription">The operation description.</param>
      <param name="bindingParameters">A collection of binding parameters.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebInvokeAttribute.System#ServiceModel#Description#IOperationBehavior#ApplyClientBehavior(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Dispatcher.ClientOperation)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IOperationBehavior.ApplyClientBehavior(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Dispatcher.ClientOperation)" /> method.</summary>
      <param name="operationDescription">The operation description.</param>
      <param name="clientOperation">The client operation.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebInvokeAttribute.System#ServiceModel#Description#IOperationBehavior#ApplyDispatchBehavior(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Dispatcher.DispatchOperation)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IOperationBehavior.ApplyDispatchBehavior(System.ServiceModel.Description.OperationDescription,System.ServiceModel.Dispatcher.DispatchOperation)" /> method.</summary>
      <param name="operationDescription">The operation description.</param>
      <param name="dispatchOperation">The operation to dispatch to.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebInvokeAttribute.System#ServiceModel#Description#IOperationBehavior#Validate(System.ServiceModel.Description.OperationDescription)">
      <summary>Implements the <see cref="M:System.ServiceModel.Description.IOperationBehavior.Validate(System.ServiceModel.Description.OperationDescription)" /> method.</summary>
      <param name="operationDescription">The operation description.</param>
    </member>
    <member name="P:System.ServiceModel.Web.WebInvokeAttribute.UriTemplate">
      <summary>The Uniform Resource Identifier (URI) template for the service operation.</summary>
      <returns>The URI template for the service operation.</returns>
    </member>
    <member name="T:System.ServiceModel.Web.WebMessageBodyStyle">
      <summary>An enumeration that specifies whether to wrap parameter and return values.</summary>
    </member>
    <member name="F:System.ServiceModel.Web.WebMessageBodyStyle.Bare">
      <summary>Both requests and responses are not wrapped.</summary>
    </member>
    <member name="F:System.ServiceModel.Web.WebMessageBodyStyle.Wrapped">
      <summary>Both requests and responses are wrapped.</summary>
    </member>
    <member name="F:System.ServiceModel.Web.WebMessageBodyStyle.WrappedRequest">
      <summary>Requests are wrapped, responses are not wrapped.</summary>
    </member>
    <member name="F:System.ServiceModel.Web.WebMessageBodyStyle.WrappedResponse">
      <summary>Responses are wrapped, requests are not wrapped.</summary>
    </member>
    <member name="T:System.ServiceModel.Web.WebMessageFormat">
      <summary>An enumeration that specifies the format of Web messages.</summary>
    </member>
    <member name="F:System.ServiceModel.Web.WebMessageFormat.Xml">
      <summary>The XML format.</summary>
    </member>
    <member name="F:System.ServiceModel.Web.WebMessageFormat.Json">
      <summary>The JavaScript Object Notation (JSON) format.</summary>
    </member>
    <member name="T:System.ServiceModel.Web.WebOperationContext">
      <summary>A helper class that provides easy access to contextual properties of Web requests and responses.</summary>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.#ctor(System.ServiceModel.OperationContext)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebOperationContext" /> class with the specified <see cref="T:System.ServiceModel.OperationContext" /> instance.</summary>
      <param name="operationContext">The operation context.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.Attach(System.ServiceModel.OperationContext)">
      <summary>Attaches the current <see cref="T:System.ServiceModel.Web.WebOperationContext" /> instance to the specified <see cref="T:System.ServiceModel.OperationContext" /> instance.</summary>
      <param name="owner">The <see cref="T:System.ServiceModel.OperationContext" /> to attach to.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateAtom10Response(System.ServiceModel.Syndication.ServiceDocument)">
      <summary>Creates a message formatted according to the Atom 1.0 specification with the specified content.</summary>
      <returns>A message in Atom 1.0 format.</returns>
      <param name="document">The content to write to the message.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateAtom10Response(System.ServiceModel.Syndication.SyndicationFeed)">
      <summary>Creates a message formatted according to the Atom 1.0 specification with the specified content.</summary>
      <returns>A message in Atom 1.0 format.</returns>
      <param name="feed">The content to write to the message.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateAtom10Response(System.ServiceModel.Syndication.SyndicationItem)">
      <summary>Creates a message formatted according to the Atom 1.0 specification with the specified content.</summary>
      <returns>A message in Atom 1.0 format.</returns>
      <param name="item">The content to write to the message.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateJsonResponse``1(``0)">
      <summary>Creates a JSON formatted message.</summary>
      <returns>A JSON formatted message.</returns>
      <param name="instance">The object to write to the message.</param>
      <typeparam name="T">The type of object to write to the message.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateJsonResponse``1(``0,System.Runtime.Serialization.Json.DataContractJsonSerializer)">
      <summary>Creates a JSON formatted message.</summary>
      <returns>A JSON formatted message.</returns>
      <param name="instance">The object to write to the message.</param>
      <param name="serializer">The serializer to use.</param>
      <typeparam name="T">The type of object to write to the message.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateStreamResponse(System.Action{System.IO.Stream},System.String)">
      <summary>Creates a stream formatted message.</summary>
      <returns>A stream formatted message.</returns>
      <param name="streamWriter">The stream writer containg the data to write to the stream.</param>
      <param name="contentType">The content type for the message.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateStreamResponse(System.IO.Stream,System.String)">
      <summary>Creates a stream formatted message.</summary>
      <returns>A stream formatted message.</returns>
      <param name="stream">The stream containing the data to write to the stream.</param>
      <param name="contentType">The content type of the message.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateStreamResponse(System.ServiceModel.Channels.StreamBodyWriter,System.String)">
      <summary>Creates a stream formatted message.</summary>
      <returns>A stream formatted message.</returns>
      <param name="bodyWriter">The stream body writer containing the data to write to the message..</param>
      <param name="contentType">The content type of the message</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateTextResponse(System.Action{System.IO.TextWriter},System.String)">
      <summary>Creates a text formatted message</summary>
      <returns>A text formatted message.</returns>
      <param name="textWriter">A delegate that writes the text data.</param>
      <param name="contentType">The content type for the message.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateTextResponse(System.Action{System.IO.TextWriter},System.String,System.Text.Encoding)">
      <summary>Creates a text formatted message</summary>
      <returns>A text formatted message.</returns>
      <param name="textWriter">A delegate that writes the text data.</param>
      <param name="contentType">The content type of the message.</param>
      <param name="encoding">The encoding to use.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateTextResponse(System.String)">
      <summary>Creates a text formatted response message.</summary>
      <returns>A text formatted message.</returns>
      <param name="text">The text to write to the message.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateTextResponse(System.String,System.String)">
      <summary>Creates a text formatted message.</summary>
      <returns>A text formatted message.</returns>
      <param name="text">The text to write to the message.</param>
      <param name="contentType">The content type of the message.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateTextResponse(System.String,System.String,System.Text.Encoding)">
      <summary>Creates a text formatted message.</summary>
      <returns>A text formatted message.</returns>
      <param name="text">The text to write to the message.</param>
      <param name="contentType">The content type of the message.</param>
      <param name="encoding">The encoding to use.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateXmlResponse(System.Xml.Linq.XDocument)">
      <summary>Creates an XML formatted message.</summary>
      <returns>An XML formatted message.</returns>
      <param name="document">The data to write to the message.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateXmlResponse(System.Xml.Linq.XElement)">
      <summary>Creates an XML formatted message.</summary>
      <returns>An XML formatted message.</returns>
      <param name="element">The data to write to the message.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateXmlResponse``1(``0)">
      <summary>Creates an XML formatted message.</summary>
      <returns>An XML formatted message.</returns>
      <param name="instance">The object to write to write to the message.</param>
      <typeparam name="T">The type of object to write to the message.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateXmlResponse``1(``0,System.Runtime.Serialization.XmlObjectSerializer)">
      <summary>Creates an XML formatted message.</summary>
      <returns>An XML formatted message.</returns>
      <param name="instance">The object to write to the message.</param>
      <param name="serializer">The serializer to use.</param>
      <typeparam name="T">The type of object to write to the message.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.CreateXmlResponse``1(``0,System.Xml.Serialization.XmlSerializer)">
      <summary>Creates an XML formatted message.</summary>
      <returns>An XML formatted message.</returns>
      <param name="instance">The object to write to the message.</param>
      <param name="serializer">The serializer to use.</param>
      <typeparam name="T">The type of object to write to the message.</typeparam>
    </member>
    <member name="P:System.ServiceModel.Web.WebOperationContext.Current">
      <summary>Gets the current Web operation context.</summary>
      <returns>A <see cref="T:System.ServiceModel.Web.WebOperationContext" /> instance.</returns>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.Detach(System.ServiceModel.OperationContext)">
      <summary>Detaches the current <see cref="T:System.ServiceModel.Web.WebOperationContext" /> instance from the specified <see cref="T:System.ServiceModel.OperationContext" /> instance.</summary>
      <param name="owner">The <see cref="T:System.ServiceModel.OperationContext" /> to detach from.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebOperationContext.GetUriTemplate(System.String)">
      <summary>Gets the URI template associated with the specified operation.</summary>
      <returns>A URI template.</returns>
      <param name="operationName">The operation.</param>
    </member>
    <member name="P:System.ServiceModel.Web.WebOperationContext.IncomingRequest">
      <summary>Gets the Web request context for the request being received.</summary>
      <returns>An <see cref="T:System.ServiceModel.Web.IncomingWebRequestContext" /> instance.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.WebOperationContext.IncomingResponse">
      <summary>Gets the Web response context for the request being received.</summary>
      <returns>An <see cref="T:System.ServiceModel.Web.IncomingWebResponseContext" /> instance.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.WebOperationContext.OutgoingRequest">
      <summary>Gets the Web request context for the request being sent.</summary>
      <returns>An <see cref="T:System.ServiceModel.Web.OutgoingWebRequestContext" /> instance.</returns>
    </member>
    <member name="P:System.ServiceModel.Web.WebOperationContext.OutgoingResponse">
      <summary>Gets the Web response context for the response being sent.</summary>
      <returns>An <see cref="T:System.ServiceModel.Web.OutgoingWebResponseContext" /> instance.</returns>
    </member>
    <member name="T:System.ServiceModel.Web.WebServiceHost">
      <summary>A <see cref="T:System.ServiceModel.ServiceHost" /> derived class that compliments the  REST programming model.</summary>
    </member>
    <member name="M:System.ServiceModel.Web.WebServiceHost.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebServiceHost" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Web.WebServiceHost.#ctor(System.Object,System.Uri[])">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebServiceHost" /> class with the specified singleton server instance and base address.</summary>
      <param name="singletonInstance">A service instance to be used as the singleton instance.</param>
      <param name="baseAddresses">The base address of the service.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebServiceHost.#ctor(System.Type,System.Uri[])">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Web.WebServiceHost" /> class with the specified service type and base address.</summary>
      <param name="serviceType">The service type.</param>
      <param name="baseAddresses">The base address of the service.</param>
    </member>
    <member name="M:System.ServiceModel.Web.WebServiceHost.OnOpening">
      <summary>Called when the <see cref="T:System.ServiceModel.Web.WebServiceHost" /> instance opens.</summary>
    </member>
  </members>
</doc>