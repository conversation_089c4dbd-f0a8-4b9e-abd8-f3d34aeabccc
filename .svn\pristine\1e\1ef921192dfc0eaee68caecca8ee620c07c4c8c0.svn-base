﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;

namespace OCRTools.Common
{
    internal class CommonGuide
    {
        static CommonGuide()
        {
            GetAllGuide();
        }

        static List<GuideEntity> lstGuide = new List<GuideEntity>();

        public static List<GuideEntity> GetByForm(Form ctrl)
        {
            return lstGuide.Where(p => Equals(p.Target, ctrl.GetType().Name)).ToList();
        }

        internal static void ShowGuide(Form ctrl, GuideEntity guide)
        {
            if (guide == null || guide.Items.Count <= 0)
                return;
            using (var _guidePresenter = new GuidePresenterForm(ctrl, guide))
            {
                var isSetTop = !ctrl.TopMost;
                if (isSetTop)
                    ctrl.TopMost = true;
                _guidePresenter.ShowDialog(ctrl);
                if (isSetTop)
                    ctrl.TopMost = false;
            }
        }

        internal static void GetAllGuide()
        {
            var result = string.Empty;
            try
            {
                result =
                   WebClientExt.GetHtml(CommonString.HostUpdate?.FullUrl + "update/uGuide.txt?t=" + ServerTime.DateTime.Ticks, 5);
                if (!string.IsNullOrEmpty(result) && result.Length > 2)
                    lstGuide = result.DeserializeJson<List<GuideEntity>>();
            }
            catch (Exception oe)
            {
                Log.WriteError("CommonGuide.GetAll:" + result, oe);
            }
            CommonMethod.ShowHelpMsg(lstGuide.Count + "=" + result);
        }
    }

    [Obfuscation]
    public class GuideEntity
    {
        [Obfuscation]
        public List<GuideItem> Items { get; set; }

        [Obfuscation]
        public Size BaseSize { get; set; }

        [Obfuscation]
        public string Title { get; set; }

        [Obfuscation]
        public string Desc { get; set; }

        [Obfuscation]
        public string Code { get; set; }

        [Obfuscation]
        public string Target { get; set; }

        [Obfuscation]
        public bool ShowSummary { get; set; }
    }

    [Obfuscation]
    public class GuideItem
    {
        /// <summary>
        /// 根据Control的Name或者AccessibleDescription查找
        /// </summary>
        [Obfuscation]
        public string Ctrl { get; set; }

        /// <summary>
        /// 指定区域
        /// </summary>
        [Obfuscation]
        public Rectangle Rect { get; set; }

        /// <summary>
        /// 详细描述
        /// </summary>
        [Obfuscation]
        public string Desc { get; set; }

        /// <summary>
        /// 标题
        /// </summary>
        [Obfuscation]
        public string Title { get; set; }

        /// <summary>
        /// 是否主要内容（首页概要展示用）
        /// </summary>
        [Obfuscation]
        public bool Summary { get; set; }
    }
}
