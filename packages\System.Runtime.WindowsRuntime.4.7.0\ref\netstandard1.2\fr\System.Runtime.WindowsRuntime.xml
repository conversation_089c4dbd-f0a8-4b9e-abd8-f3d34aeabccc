﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.WindowsRuntime</name>
  </assembly>
  <members>
    <member name="T:System.WindowsRuntimeSystemExtensions">
      <summary>Fournit les méthodes d'extension pour convertir les tâches en actions et opérations asynchrones Windows Runtime. </summary>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsAsyncAction(System.Threading.Tasks.Task)">
      <summary>Retourne une action asynchrone Windows Runtime qui représente une tâche démarrée. </summary>
      <returns>Instance Windows.Foundation.IAsyncAction qui représente la tâche démarrée. </returns>
      <param name="source">Tâche démarrée. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> est une tâche non démarrée. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsAsyncOperation``1(System.Threading.Tasks.Task{``0})">
      <summary>Retourne une opération asynchrone Windows Runtime qui représente une tâche démarrée qui retourne un résultat. </summary>
      <returns>Instance Windows.Foundation.IAsyncOperation&lt;TResult&gt; qui représente la tâche démarrée. </returns>
      <param name="source">Tâche démarrée. </param>
      <typeparam name="TResult">Type qui retourne le résultat. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> est une tâche non démarrée. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask(Windows.Foundation.IAsyncAction)">
      <summary>Retourne une tâche qui représente une action asynchrone Windows Runtime. </summary>
      <returns>Tâche qui représente l'action asynchrone. </returns>
      <param name="source">Action asynchrone. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask(Windows.Foundation.IAsyncAction,System.Threading.CancellationToken)">
      <summary>Retourne une tâche qui représente une action asynchrone Windows Runtime qui peut être annulée. </summary>
      <returns>Tâche qui représente l'action asynchrone. </returns>
      <param name="source">Action asynchrone. </param>
      <param name="cancellationToken">Jeton qui peut être utilisé pour demander l'annulation de l'action asynchrone. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0})">
      <summary>Retourne une tâche qui représente une action asynchrone Windows Runtime. </summary>
      <returns>Tâche qui représente l'action asynchrone. </returns>
      <param name="source">Action asynchrone. </param>
      <typeparam name="TProgress">Type d'objet qui fournit des données indiquant la progression. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0},System.IProgress{``0})">
      <summary>Retourne une tâche qui représente une action asynchrone Windows Runtime qui signale une progression. </summary>
      <returns>Tâche qui représente l'action asynchrone. </returns>
      <param name="source">Action asynchrone. </param>
      <param name="progress">Objet qui reçoit des mises à jour de progression. </param>
      <typeparam name="TProgress">Type d'objet qui fournit des données indiquant la progression. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0},System.Threading.CancellationToken)">
      <summary>Retourne une tâche qui représente une action asynchrone Windows Runtime qui peut être annulée. </summary>
      <returns>Tâche qui représente l'action asynchrone. </returns>
      <param name="source">Action asynchrone. </param>
      <param name="cancellationToken">Jeton qui peut être utilisé pour demander l'annulation de l'action asynchrone. </param>
      <typeparam name="TProgress">Type d'objet qui fournit des données indiquant la progression. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0},System.Threading.CancellationToken,System.IProgress{``0})">
      <summary>Retourne une tâche qui représente une action asynchrone Windows Runtime qui signale une progression et peut être annulée.</summary>
      <returns>Tâche qui représente l'action asynchrone. </returns>
      <param name="source">Action asynchrone. </param>
      <param name="cancellationToken">Jeton qui peut être utilisé pour demander l'annulation de l'action asynchrone. </param>
      <param name="progress">Objet qui reçoit des mises à jour de progression. </param>
      <typeparam name="TProgress">Type d'objet qui fournit des données indiquant la progression. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncOperation{``0})">
      <summary>Retourne une tâche qui représente une opération asynchrone Windows Runtime qui retourne un résultat. </summary>
      <returns>Tâche qui représente l'opération asynchrone. </returns>
      <param name="source">Opération asynchrone. </param>
      <typeparam name="TResult">Type d'objet qui retourne le résultat de l'opération asynchrone. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncOperation{``0},System.Threading.CancellationToken)">
      <summary>Retourne une tâche qui représente une opération asynchrone Windows Runtime qui retourne un résultat et peut être annulée. </summary>
      <returns>Tâche qui représente l'opération asynchrone. </returns>
      <param name="source">Opération asynchrone. </param>
      <param name="cancellationToken">Jeton qui peut être utilisé pour demander l'annulation de l'opération asynchrone. </param>
      <typeparam name="TResult">Type d'objet qui retourne le résultat de l'opération asynchrone. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1})">
      <summary>Retourne une tâche qui représente une opération asynchrone Windows Runtime qui retourne un résultat. </summary>
      <returns>Tâche qui représente l'opération asynchrone. </returns>
      <param name="source">Opération asynchrone. </param>
      <typeparam name="TResult">Type d'objet qui retourne le résultat de l'opération asynchrone. </typeparam>
      <typeparam name="TProgress">Type d'objet qui fournit des données indiquant la progression. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1},System.IProgress{``1})">
      <summary>Retourne une tâche qui représente une opération asynchrone Windows Runtime qui retourne un résultat et signale une progression. </summary>
      <returns>Tâche qui représente l'opération asynchrone. </returns>
      <param name="source">Opération asynchrone. </param>
      <param name="progress">Objet qui reçoit des mises à jour de progression. </param>
      <typeparam name="TResult">Type d'objet qui retourne le résultat de l'opération asynchrone. </typeparam>
      <typeparam name="TProgress">Type d'objet qui fournit des données indiquant la progression. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1},System.Threading.CancellationToken)">
      <summary>Retourne une tâche qui représente une opération asynchrone Windows Runtime qui retourne un résultat et peut être annulée. </summary>
      <returns>Tâche qui représente l'opération asynchrone. </returns>
      <param name="source">Opération asynchrone. </param>
      <param name="cancellationToken">Jeton qui peut être utilisé pour demander l'annulation de l'opération asynchrone. </param>
      <typeparam name="TResult">Type d'objet qui retourne le résultat de l'opération asynchrone. </typeparam>
      <typeparam name="TProgress">Type d'objet qui fournit des données indiquant la progression. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1},System.Threading.CancellationToken,System.IProgress{``1})">
      <summary>Retourne une tâche qui représente une opération asynchrone Windows Runtime qui retourne un résultat, signale une progression et peut être annulée. </summary>
      <returns>Tâche qui représente l'opération asynchrone. </returns>
      <param name="source">Opération asynchrone. </param>
      <param name="cancellationToken">Jeton qui peut être utilisé pour demander l'annulation de l'opération asynchrone. </param>
      <param name="progress">Objet qui reçoit des mises à jour de progression. </param>
      <typeparam name="TResult">Type d'objet qui retourne le résultat de l'opération asynchrone. </typeparam>
      <typeparam name="TProgress">Type d'objet qui fournit des données indiquant la progression. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter(Windows.Foundation.IAsyncAction)">
      <summary>Retourne un objet qui attend une action asynchrone. </summary>
      <returns>Objet qui attend l'action asynchrone spécifiée. </returns>
      <param name="source">Action asynchrone à attendre. </param>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter``1(Windows.Foundation.IAsyncActionWithProgress{``0})">
      <summary>Retourne un objet qui attend une action asynchrone qui signale une progression. </summary>
      <returns>Objet qui attend l'action asynchrone spécifiée. </returns>
      <param name="source">Action asynchrone à attendre. </param>
      <typeparam name="TProgress">Type d'objet qui fournit des données indiquant la progression. </typeparam>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter``1(Windows.Foundation.IAsyncOperation{``0})">
      <summary>Retourne un objet qui attend une opération asynchrone qui retourne un résultat.</summary>
      <returns>Objet qui attend l'opération asynchrone spécifiée. </returns>
      <param name="source">Opération asynchrone à attendre. </param>
      <typeparam name="TResult">Type d'objet qui retourne le résultat de l'opération asynchrone. </typeparam>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1})">
      <summary>Retourne un objet qui attend une opération asynchrone qui signale une progression et retourne un résultat. </summary>
      <returns>Objet qui attend l'opération asynchrone spécifiée. </returns>
      <param name="source">Opération asynchrone à attendre. </param>
      <typeparam name="TResult">Type d'objet qui retourne le résultat de l'opération asynchrone.</typeparam>
      <typeparam name="TProgress">Type d'objet qui fournit des données indiquant la progression. </typeparam>
    </member>
    <member name="T:System.IO.WindowsRuntimeStorageExtensions">
      <summary>Contient des méthodes d'extension pour les interfaces IStorageFile et IStorageFolder dans Windows Runtime lors du développement d'applications Windows Store.</summary>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForReadAsync(Windows.Storage.IStorageFile)">
      <summary>Récupère un flux de données pour lire dans un fichier spécifié.</summary>
      <returns>Tâche qui représente l'opération de lecture asynchrone.</returns>
      <param name="windowsRuntimeFile">Objet Windows Runtime IStorageFile à lire.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeFile" /> a la valeur null.</exception>
      <exception cref="T:System.IO.IOException">Le fichier n'a pas pu être ouvert ou extrait en tant que flux.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForReadAsync(Windows.Storage.IStorageFolder,System.String)">
      <summary>Récupère un flux de données pour lire un fichier dans un fichier du dossier parent spécifié.</summary>
      <returns>Tâche qui représente l'opération de lecture asynchrone.</returns>
      <param name="rootDirectory">Objet Windows Runtime IStorageFolder qui contient le fichier à lire.</param>
      <param name="relativePath">Chemin d'accès, relatif au dossier racine, au fichier dans lequel lire.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rootDirectory" /> ou <paramref name="relativePath" /> est null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="relativePath" /> est vide ou ne contient que des espaces blancs.</exception>
      <exception cref="T:System.IO.IOException">Le fichier n'a pas pu être ouvert ou extrait en tant que flux.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForWriteAsync(Windows.Storage.IStorageFile)">
      <summary>Récupère un flux de données pour écrire dans un fichier spécifié.</summary>
      <returns>Tâche qui représente l'opération d'écriture asynchrone.</returns>
      <param name="windowsRuntimeFile">Objet Windows Runtime IStorageFile dans lequel écrire.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeFile" /> a la valeur null.</exception>
      <exception cref="T:System.IO.IOException">Le fichier n'a pas pu être ouvert ou extrait en tant que flux.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForWriteAsync(Windows.Storage.IStorageFolder,System.String,Windows.Storage.CreationCollisionOption)">
      <summary>Récupère un flux de données pour écrire dans un fichier du dossier parent spécifié.</summary>
      <returns>Tâche qui représente l'opération d'écriture asynchrone.</returns>
      <param name="rootDirectory">Objet Windows Runtime IStorageFolder qui contient le fichier dans lequel écrire.</param>
      <param name="relativePath">Chemin d'accès, relatif au dossier racine, au fichier dans lequel écrire.</param>
      <param name="creationCollisionOption">Valeur d'énumération Windows Runtime CreationCollisionOption qui spécifie le comportement à utiliser lorsque le nom du fichier à créer est identique au nom d'un fichier existant.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rootDirectory" /> ou <paramref name="relativePath" /> est null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="relativePath" /> est vide ou ne contient que des espaces blancs.</exception>
      <exception cref="T:System.IO.IOException">Le fichier n'a pas pu être ouvert ou extrait en tant que flux.</exception>
    </member>
    <member name="T:System.IO.WindowsRuntimeStreamExtensions">
      <summary>Contient des méthodes d'extension pour la conversion entre des flux de données dans Windows Runtime et des flux de données managés dans .NET pour les applications du Windows Store.</summary>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsInputStream(System.IO.Stream)">
      <summary>Convertit un flux managé dans .NET pour les applications du Windows Store en flux d'entrée dans Windows Runtime.</summary>
      <returns>Objet de Windows Runtime IInputStream qui représente le flux converti.</returns>
      <param name="stream">Flux à convertir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> a la valeur null.</exception>
      <exception cref="T:System.NotSupportedException">Le flux ne prend pas en charge la lecture.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsOutputStream(System.IO.Stream)">
      <summary>Convertit un flux managé dans .NET pour les applications du Windows Store en flux de sortie dans Windows Runtime.</summary>
      <returns>Objet de Windows Runtime IOutputStream qui représente le flux converti.</returns>
      <param name="stream">Flux à convertir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> a la valeur null.</exception>
      <exception cref="T:System.NotSupportedException">Le flux ne prend pas en charge l'écriture.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsRandomAccessStream(System.IO.Stream)">
      <summary>Convertit le flux spécifié en flux d'accès aléatoire.</summary>
      <returns>Un RandomAccessStream, qui représente le flux de données convertie.</returns>
      <param name="stream">Flux à convertir.</param>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStream(Windows.Storage.Streams.IRandomAccessStream)">
      <summary>Convertit un flux d'accès aléatoire dans Windows Runtime en flux managé dans .NET pour les applications du Windows Store.</summary>
      <returns>Flux converti.</returns>
      <param name="windowsRuntimeStream">Objet Windows Runtime IRandomAccessStream à convertir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" /> a la valeur null.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStream(Windows.Storage.Streams.IRandomAccessStream,System.Int32)">
      <summary>Convertit un flux d'accès aléatoire dans Windows Runtime en un flux managé dans le .NET pour les applications du Windows Store à l'aide de la taille de mémoire tampon spécifiée.</summary>
      <returns>Flux converti.</returns>
      <param name="windowsRuntimeStream">Objet Windows Runtime IRandomAccessStream à convertir.</param>
      <param name="bufferSize">Taille, en octets, de la mémoire tampon.Cette valeur ne peut pas être négative, mais elle peut avoir la valeur 0 (zéro) pour désactiver la mise en mémoire tampon.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> est négatif.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForRead(Windows.Storage.Streams.IInputStream)">
      <summary>Convertit un flux d'entrée dans Windows Runtime en flux managé dans .NET pour les applications du Windows Store.</summary>
      <returns>Flux converti.</returns>
      <param name="windowsRuntimeStream">Objet Windows Runtime IInputStream à convertir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" /> a la valeur null.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForRead(Windows.Storage.Streams.IInputStream,System.Int32)">
      <summary>Convertit un flux d'entrée aléatoire dans Windows Runtime en un flux managé dans le .NET pour les applications du Windows Store à l'aide de la taille de mémoire tampon spécifiée.</summary>
      <returns>Flux converti.</returns>
      <param name="windowsRuntimeStream">Objet Windows Runtime IInputStream à convertir.</param>
      <param name="bufferSize">Taille, en octets, de la mémoire tampon.Cette valeur ne peut pas être négative, mais elle peut avoir la valeur 0 (zéro) pour désactiver la mise en mémoire tampon.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> est négatif.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForWrite(Windows.Storage.Streams.IOutputStream)">
      <summary>Convertit un flux de sortie dans Windows Runtime en flux managé dans .NET pour les applications du Windows Store.</summary>
      <returns>Flux converti.</returns>
      <param name="windowsRuntimeStream">Objet Windows Runtime IOutputStream à convertir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" /> a la valeur null.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForWrite(Windows.Storage.Streams.IOutputStream,System.Int32)">
      <summary>Convertit un flux de sortie aléatoire dans Windows Runtime en un flux managé dans le .NET pour les applications du Windows Store à l'aide de la taille de mémoire tampon spécifiée.</summary>
      <returns>Flux converti.</returns>
      <param name="windowsRuntimeStream">Objet Windows Runtime IOutputStream à convertir.</param>
      <param name="bufferSize">Taille, en octets, de la mémoire tampon.Cette valeur ne peut pas être négative, mais elle peut avoir la valeur 0 (zéro) pour désactiver la mise en mémoire tampon.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> est négatif.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo">
      <summary>Fournit les méthodes de fabrique pour construire des représentations de tâches managées qui sont compatibles avec les actions et opérations asynchrones Windows Runtime. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run``1(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{``0}})">
      <summary>Crée et démarre une opération asynchrone Windows Runtime à l'aide d'une fonction qui génère une tâche démarrée qui retourne des résultats.Une tâche peut prendre en charge l'annulation.</summary>
      <returns>Instance Windows.Foundation.IAsyncOperation&lt;TResult&gt; démarrée qui représente la tâche générée par <paramref name="taskProvider" />. </returns>
      <param name="taskProvider">Délégué qui représente la fonction qui crée et démarre la tâche.La tâche démarrée est représentée par l'opération asynchrone Windows Runtime qui est retournée.Un jeton d'annulation est passé à la fonction, lequel est surveillé par la tâche pour être avertie des demandes d'annulation ; vous pouvez ignorer le jeton si votre tâche ne prend pas en charge l'annulation.</param>
      <typeparam name="TResult">Type qui retourne le résultat. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="taskProvider" /> a la valeur null. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" /> retourne une tâche non démarrée. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task})">
      <summary>Crée et démarre une action asynchrone Windows Runtime à l'aide d'une fonction qui génère une tâche démarrée.Une tâche peut prendre en charge l'annulation.</summary>
      <returns>Instance Windows.Foundation.IAsyncAction démarrée qui représente la tâche générée par <paramref name="taskProvider" />. </returns>
      <param name="taskProvider">Délégué qui représente la fonction qui crée et démarre la tâche.La tâche démarrée est représentée par l'action asynchrone Windows Runtime qui est retournée.Un jeton d'annulation est passé à la fonction, lequel est surveillé par la tâche pour être avertie des demandes d'annulation ; vous pouvez ignorer le jeton si votre tâche ne prend pas en charge l'annulation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="taskProvider" /> a la valeur null. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" /> retourne une tâche non démarrée. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run``2(System.Func{System.Threading.CancellationToken,System.IProgress{``1},System.Threading.Tasks.Task{``0}})">
      <summary>Crée et démarre une opération asynchrone Windows Runtime qui inclut des mises à jour de progression, à l'aide d'une fonction qui génère une tâche démarrée qui retourne des résultats.Une tâche peut prendre en charge l'annulation et les rapports de progression.</summary>
      <returns>Instance Windows.Foundation.IAsyncOperationWithProgress&lt;TResult,TProgress&gt; démarrée qui représente la tâche générée par <paramref name="taskProvider" />. </returns>
      <param name="taskProvider">Délégué qui représente la fonction qui crée et démarre la tâche.La tâche démarrée est représentée par l'action asynchrone Windows Runtime qui est retournée.Un jeton d'annulation est passé à la fonction, lequel est surveillé par la tâche afin d'être avertie des demandes d'annulation, avec une interface pour signaler la progression ; vous pouvez ignorer l'un ou l'autre de ces arguments ou les deux si votre tâche ne prend pas en charge le rapport de progression ou l'annulation.</param>
      <typeparam name="TResult">Type qui retourne le résultat. </typeparam>
      <typeparam name="TProgress">Type utilisé pour les notifications de progression. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="taskProvider" /> a la valeur null. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" /> retourne une tâche non démarrée. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run``1(System.Func{System.Threading.CancellationToken,System.IProgress{``0},System.Threading.Tasks.Task})">
      <summary>Crée et démarre une action asynchrone Windows Runtime qui inclut des mises à jour de progression, à l'aide d'une fonction qui génère une tâche démarrée.Une tâche peut prendre en charge l'annulation et les rapports de progression.</summary>
      <returns>Instance Windows.Foundation.IAsyncActionWithProgress&lt;TProgress&gt; démarrée qui représente la tâche générée par <paramref name="taskProvider" />. </returns>
      <param name="taskProvider">Délégué qui représente la fonction qui crée et démarre la tâche.La tâche démarrée est représentée par l'action asynchrone Windows Runtime qui est retournée.Un jeton d'annulation est passé à la fonction, lequel est surveillé par la tâche afin d'être avertie des demandes d'annulation, avec une interface pour signaler la progression ; vous pouvez ignorer l'un ou l'autre de ces arguments ou les deux si votre tâche ne prend pas en charge le rapport de progression ou l'annulation.</param>
      <typeparam name="TProgress">Type utilisé pour les notifications de progression. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="taskProvider" /> a la valeur null. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" /> retourne une tâche non démarrée. </exception>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBuffer">
      <summary>Fournit une implémentation de l'interface Windows Runtime IBuffer (Windows.Storage.Streams.IBuffer) et toutes les interfaces requises supplémentaires. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBuffer.Create(System.Byte[],System.Int32,System.Int32,System.Int32)">
      <summary>Retourne une interface Windows.Storage.Streams.IBuffer qui contient une plage d'octets spécifiée copiée à partir d'un tableau d'octets.Si la capacité spécifiée est supérieure au nombre d'octets copiés, le reste de la mémoire tampon est rempli de zéros.</summary>
      <returns>Interface de Windows.Storage.Streams.IBuffer qui contient la plage d'octets spécifiée.Si la <paramref name="capacity" /> est supérieure à la <paramref name="length" />, le reste de la mémoire tampon est rempli de zéros.</returns>
      <param name="data">Tableau d'octets à partir duquel effectuer la copie. </param>
      <param name="offset">Décalage dans <paramref name="data" /> à partir duquel la copie commence. </param>
      <param name="length">Nombre d'octets à copier. </param>
      <param name="capacity">Nombre maximal d'octets que la mémoire tampon peut contenir ; s'il est supérieur à <paramref name="length" />, les octets en mémoire tampon restants sont réinitialisés (ils prennent la valeur zéro).</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" />, <paramref name="offset" /> ou <paramref name="length" /> est inférieur à 0 (zéro). </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentException">À partir de <paramref name="offset" />, <paramref name="data" /> ne contient pas d'éléments <paramref name="length" />. ouÀ partir de <paramref name="offset" />, <paramref name="data" /> ne contient pas d'éléments <paramref name="capacity" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBuffer.Create(System.Int32)">
      <summary>Retourne une interface Windows.Storage.Streams.IBuffer vide qui a la capacité maximale spécifiée. </summary>
      <returns>Interface de Windows.Storage.Streams.IBuffer qui a la capacité spécifiée et une propriété de Length égale à 0 (zéro). </returns>
      <param name="capacity">Nombre maximal d'octets que la mémoire tampon peut contenir. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> est inférieur à 0 (zéro). </exception>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions">
      <summary>Fournit les méthodes d'extension pour utiliser les mémoires tampons Windows Runtime (interface Windows.Storage.Streams.IBuffer). </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsBuffer(System.Byte[])">
      <summary>Retourne une interface Windows.Storage.Streams.IBuffer qui représente le tableau d'octets spécifié. </summary>
      <returns>Interface de Windows.Storage.Streams.IBuffer qui représente le tableau d'octets spécifié. </returns>
      <param name="source">Tableau à représenter. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsBuffer(System.Byte[],System.Int32,System.Int32)">
      <summary>Retourne une interface Windows.Storage.Streams.IBuffer qui représente une plage d'octets dans le tableau d'octets spécifié. </summary>
      <returns>Interface de IBuffer qui représente la plage d'octets spécifiée dans <paramref name="source" />.</returns>
      <param name="source">Tableau qui contient la plage d'octets représentée par IBuffer. </param>
      <param name="offset">Décalage dans <paramref name="source" /> au niveau duquel la plage commence. </param>
      <param name="length">Longueur de la plage qui est représentée par le IBuffer. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ou <paramref name="length" /> inférieur à 0. </exception>
      <exception cref="T:System.ArgumentException">Le tableau n'est pas assez grand pour servir de magasin de stockage à IBuffer ; cela signifie que le nombre d'octets dans la <paramref name="source" />, en commençant par <paramref name="offset" />, est inférieur à <paramref name="length" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsBuffer(System.Byte[],System.Int32,System.Int32,System.Int32)">
      <summary>Retourne une interface Windows.Storage.Streams.IBuffer qui représente une plage d'octets dans le tableau d'octets spécifié.Définit éventuellement la propriété Length de l'interface IBuffer sur une valeur inférieure à la capacité.</summary>
      <returns>Interface de IBuffer qui représente la plage d'octets spécifiée dans <paramref name="source" /> et qui a la valeur de propriété spécifiée de Length. </returns>
      <param name="source">Tableau qui contient la plage d'octets représentée par IBuffer. </param>
      <param name="offset">Décalage dans <paramref name="source" /> au niveau duquel la plage commence. </param>
      <param name="length">Valeur de la propriété Length de la IBuffer. </param>
      <param name="capacity">Taille de la plage qui est représentée par le IBuffer.La propriété Capacity de IBuffer est définie sur cette valeur.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" />, <paramref name="length" /> ou <paramref name="capacity" /> est inférieur à 0 (zéro). </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="length" /> est supérieur à <paramref name="capacity" />. ouLe tableau n'est pas assez grand pour servir de magasin de stockage à IBuffer ; cela signifie que le nombre d'octets dans la <paramref name="source" />, en commençant par <paramref name="offset" />, est inférieur à <paramref name="length" /> ou <paramref name="capacity" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsStream(Windows.Storage.Streams.IBuffer)">
      <summary>Retourne un flux de données qui représente la même mémoire que celle représentée par l'interface Windows.Storage.Streams.IBuffer. </summary>
      <returns>Flux de données qui représente la mémoire identique à celle que l'interface spécifiée de Windows.Storage.Streams.IBuffer représente. </returns>
      <param name="source">IBuffer à représenter en tant que flux. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(System.Byte[],System.Int32,Windows.Storage.Streams.IBuffer,System.UInt32,System.Int32)">
      <summary>Copie les octets du tableau source vers la mémoire tampon de destination (Windows.Storage.Streams.IBuffer), en spécifiant l'index de départ dans le tableau source, l'index de départ dans la mémoire tampon de destination et le nombre d'octets à copier.La méthode ne met pas à jour la propriété Length de la mémoire tampon de destination.</summary>
      <param name="source">Tableau à partir duquel copier des données. </param>
      <param name="sourceIndex">Index dans <paramref name="source" /> dans lequel commencer la copie des données. </param>
      <param name="destination">Mémoire tampon dans laquelle copier les données. </param>
      <param name="destinationIndex">Index dans <paramref name="destination" /> dans lequel commencer la copie des données. </param>
      <param name="count">Nombre d'octets à copier. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> ou <paramref name="destination" /> est null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" />, <paramref name="sourceIndex" /> ou <paramref name="destinationIndex" /> est inférieur à 0 (zéro). </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceIndex" /> est supérieur ou égal à la longueur de <paramref name="source" />. ouNombre d'octets <paramref name="source" />, commençant à <paramref name="sourceIndex" />, inférieur à <paramref name="count" />. ouLa copie de <paramref name="count" /> octets, à partir de <paramref name="destinationIndex" />, dépasserait la capacité de <paramref name="destination" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(System.Byte[],Windows.Storage.Streams.IBuffer)">
      <summary>Copie tous les octets du tableau source vers la mémoire tampon de destination (Windows.Storage.Streams.IBuffer), en commençant à l'offset 0 (zéro) dans les deux.La méthode ne met pas à jour la longueur de la mémoire tampon de destination.</summary>
      <param name="source">Tableau à partir duquel copier des données. </param>
      <param name="destination">Mémoire tampon dans laquelle copier les données. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> ou <paramref name="destination" /> est null. </exception>
      <exception cref="T:System.ArgumentException">La taille de la <paramref name="source" /> dépasse la capacité de la <paramref name="destination" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,System.Byte[])">
      <summary>Copie tous les octets de la mémoire tampon source (Windows.Storage.Streams.IBuffer) vers le tableau de destination, en commençant à l'offset 0 (zéro) dans les deux. </summary>
      <param name="source">Mémoire tampon dans laquelle copier les données. </param>
      <param name="destination">Tableau dans lequel effectuer copier les données. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> ou <paramref name="destination" /> est null. </exception>
      <exception cref="T:System.ArgumentException">La taille de la <paramref name="source" /> dépasse celle de la <paramref name="destination" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,System.UInt32,System.Byte[],System.Int32,System.Int32)">
      <summary>Copie les octets de la mémoire tampon source (Windows.Storage.Streams.IBuffer) vers le tableau de destination, en spécifiant l'index de départ dans la mémoire tampon source, l'index de départ dans le tableau de destination et le nombre d'octets à copier. </summary>
      <param name="source">Mémoire tampon dans laquelle copier les données. </param>
      <param name="sourceIndex">Index dans <paramref name="source" /> dans lequel commencer la copie des données. </param>
      <param name="destination">Tableau dans lequel effectuer copier les données. </param>
      <param name="destinationIndex">Index dans <paramref name="destination" /> dans lequel commencer la copie des données. </param>
      <param name="count">Nombre d'octets à copier. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> ou <paramref name="destination" /> est null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" />, <paramref name="sourceIndex" /> ou <paramref name="destinationIndex" /> est inférieur à 0 (zéro). </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceIndex" /> est supérieur ou égal à la capacité de <paramref name="source" />. ou<paramref name="destinationIndex" /> est supérieur ou égal à la longueur de <paramref name="destination" />. ouNombre d'octets <paramref name="source" />, commençant à <paramref name="sourceIndex" />, inférieur à <paramref name="count" />. ouLa copie de <paramref name="count" /> octets, à partir de <paramref name="destinationIndex" />, dépasserait la taille de <paramref name="destination" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,System.UInt32,Windows.Storage.Streams.IBuffer,System.UInt32,System.UInt32)">
      <summary>Copie les octets de la mémoire tampon source (Windows.Storage.Streams.IBuffer) vers la mémoire tampon de destination, en spécifiant l'index de départ dans la source, l'index de départ dans la destination et le nombre d'octets à copier.</summary>
      <param name="source">Mémoire tampon dans laquelle copier les données. </param>
      <param name="sourceIndex">Index dans <paramref name="source" /> dans lequel commencer la copie des données. </param>
      <param name="destination">Mémoire tampon dans laquelle copier les données. </param>
      <param name="destinationIndex">Index dans <paramref name="destination" /> dans lequel commencer la copie des données. </param>
      <param name="count">Nombre d'octets à copier. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> ou <paramref name="destination" /> est null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" />, <paramref name="sourceIndex" /> ou <paramref name="destinationIndex" /> est inférieur à 0 (zéro). </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceIndex" /> est supérieur ou égal à la capacité de <paramref name="source" />. ou<paramref name="destinationIndex" /> est supérieur ou égal à la capacité de <paramref name="destination" />. ouNombre d'octets <paramref name="source" />, commençant à <paramref name="sourceIndex" />, inférieur à <paramref name="count" />. ouLa copie de <paramref name="count" /> octets, à partir de <paramref name="destinationIndex" />, dépasserait la capacité de <paramref name="destination" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,Windows.Storage.Streams.IBuffer)">
      <summary>Copie tous les octets de la mémoire tampon source (Windows.Storage.Streams.IBuffer) vers la mémoire tampon de destination, en commençant à l'offset 0 (zéro) dans les deux. </summary>
      <param name="source">Mémoire tampon source. </param>
      <param name="destination">Mémoire tampon de destination. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> ou <paramref name="destination" /> est null. </exception>
      <exception cref="T:System.ArgumentException">La taille de la <paramref name="source" /> dépasse la capacité de la <paramref name="destination" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.GetByte(Windows.Storage.Streams.IBuffer,System.UInt32)">
      <summary>Retourne l'octet au décalage spécifié dans l'interface Windows.Storage.Streams.IBuffer spécifiée.</summary>
      <returns>Octet au décalage spécifié. </returns>
      <param name="source">Mémoire tampon dans laquelle l'octet est obtenu. </param>
      <param name="byteOffset">Décalage de l'octet. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteOffset" /> est inférieur à 0 (zéro). </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="byteOffset" /> est supérieur ou égal à la capacité de <paramref name="source" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.GetWindowsRuntimeBuffer(System.IO.MemoryStream)">
      <summary>Retourne une interface Windows.Storage.Streams.IBuffer qui représente la même mémoire que le flux de données de mémoire spécifié. </summary>
      <returns>Interface de Windows.Storage.Streams.IBuffer stockée par la mémoire qui sauvegarde aussi le flux de données de mémoire spécifié.</returns>
      <param name="underlyingStream">Flux de données qui fournit la mémoire de sauvegarde pour IBuffer. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.GetWindowsRuntimeBuffer(System.IO.MemoryStream,System.Int32,System.Int32)">
      <summary>Retourne une interface Windows.Storage.Streams.IBuffer qui représente une zone dans la mémoire que le flux de données de mémoire spécifié représente. </summary>
      <returns>Interface de Windows.Storage.Streams.IBuffer stockée par une zone dans la mémoire qui sauvegarde le flux de données de mémoire spécifié. </returns>
      <param name="underlyingStream">Flux de données qui partage la mémoire avec IBuffer. </param>
      <param name="positionInStream">Position de la zone de mémoire partagée dans <paramref name="underlyingStream" />. </param>
      <param name="length">Taille maximale de la zone de mémoire partagée.Si le nombre d'octets dans le <paramref name="underlyingStream" /> démarrant au <paramref name="positionInStream" /> est inférieur à la <paramref name="length" />, l'IBuffer qui est retourné représente uniquement les octets disponibles.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="underlyingStream" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="positionInStream" /> ou <paramref name="length" /> inférieur à 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="positionInStream" /> est au-delà de la fin de <paramref name="source" />. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="underlyingStream" /> ne peut pas exposer sa mémoire tampon sous-jacente. </exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="underlyingStream" /> a été fermé. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.IsSameData(Windows.Storage.Streams.IBuffer,Windows.Storage.Streams.IBuffer)">
      <summary>Retourne une valeur qui indique si deux mémoires tampons (objets Windows.Storage.Streams.IBuffer) représentent la même zone de mémoire sous-jacente. </summary>
      <returns>true si les régions de mémoire qui sont représentées par les deux mémoires tampons ont le même point de départ ; sinon, false. </returns>
      <param name="buffer">Première mémoire tampon. </param>
      <param name="otherBuffer">Deuxième mémoire tampon. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.ToArray(Windows.Storage.Streams.IBuffer)">
      <summary>Retourne un nouveau tableau qui est créé à partir du contenu de la mémoire tampon (Windows.Storage.Streams.IBuffer) spécifiée.La taille du tableau est la valeur de la propriété Length du IBuffer.</summary>
      <returns>Tableau d'octets qui contient les octets dans IBufferspécifiée, en commençant à l'offset 0 (zéro) et incluant un nombre d'octets égal à la valeur de la Length de la propriété de IBuffer. </returns>
      <param name="source">Mémoire tampon dont le contenu remplit nouveau tableau. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.ToArray(Windows.Storage.Streams.IBuffer,System.UInt32,System.Int32)">
      <summary>Retourne un nouveau tableau qui est créé à partir du contenu de la mémoire tampon spécifiée (Windows.Storage.Streams.IBuffer), en commençant à un décalage spécifié et comportant un nombre d'octets spécifié. </summary>
      <returns>Tableau d'octets qui contient la plage d'octets spécifiée. </returns>
      <param name="source">Mémoire tampon dont le contenu remplit nouveau tableau. </param>
      <param name="sourceIndex">Index dans <paramref name="source" /> dans lequel commencer la copie des données. </param>
      <param name="count">Nombre d'octets à copier. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> ou <paramref name="sourceIndex" /> inférieur à 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceIndex" /> est supérieur ou égal à la capacité de <paramref name="source" />. ouNombre d'octets <paramref name="source" />, commençant à <paramref name="sourceIndex" />, inférieur à <paramref name="count" />. </exception>
    </member>
    <member name="T:Windows.Foundation.Point">
      <summary>Représente une paire de coordonnées X/Y dans espace à deux dimensions.Peut également représenter un point logique pour certaines utilisations de propriété.</summary>
    </member>
    <member name="M:Windows.Foundation.Point.#ctor(System.Double,System.Double)">
      <summary>Initialise une structure <see cref="T:Windows.Foundation.Point" /> qui contient les valeurs spécifiées. </summary>
      <param name="x">Valeur de la coordonnée x de la structure <see cref="T:Windows.Foundation.Point" />. </param>
      <param name="y">Valeur de la coordonnée y de la structure <see cref="T:Windows.Foundation.Point" />. </param>
    </member>
    <member name="M:Windows.Foundation.Point.Equals(System.Object)">
      <summary>Détermine si l'objet spécifié est un <see cref="T:Windows.Foundation.Point" /> et s'il contient les mêmes valeurs que <see cref="T:Windows.Foundation.Point" />. </summary>
      <returns>true si <paramref name="obj" /> est un <see cref="T:Windows.Foundation.Point" /> et qu'il reprend les mêmes valeurs <see cref="P:Windows.Foundation.Point.X" /> et <see cref="P:Windows.Foundation.Point.Y" /> que <see cref="T:Windows.Foundation.Point" /> ; sinon, false.</returns>
      <param name="o">Objet à comparer.</param>
    </member>
    <member name="M:Windows.Foundation.Point.Equals(Windows.Foundation.Point)">
      <summary>Compare l'égalité de deux structures <see cref="T:Windows.Foundation.Point" />.</summary>
      <returns>true si les deux structures <see cref="T:Windows.Foundation.Point" /> contiennent les mêmes valeurs de <see cref="P:Windows.Foundation.Point.X" /> et <see cref="P:Windows.Foundation.Point.Y" /> ; sinon, false.</returns>
      <param name="value">Point à comparer à cette instance.</param>
    </member>
    <member name="M:Windows.Foundation.Point.GetHashCode">
      <summary>Retourne le code de hachage pour ce <see cref="T:Windows.Foundation.Point" />.</summary>
      <returns>Code de hachage pour cette structure <see cref="T:Windows.Foundation.Point" />.</returns>
    </member>
    <member name="M:Windows.Foundation.Point.op_Equality(Windows.Foundation.Point,Windows.Foundation.Point)">
      <summary>Compare l'égalité de deux structures <see cref="T:Windows.Foundation.Point" />.</summary>
      <returns>true si les valeurs <see cref="P:Windows.Foundation.Point.X" /> et <see cref="P:Windows.Foundation.Point.Y" /> de <paramref name="point1" /> et <paramref name="point2" /> sont égales ; sinon, false.</returns>
      <param name="point1">Première structure <see cref="T:Windows.Foundation.Point" /> à comparer.</param>
      <param name="point2">Deuxième structure <see cref="T:Windows.Foundation.Point" /> à comparer.</param>
    </member>
    <member name="M:Windows.Foundation.Point.op_Inequality(Windows.Foundation.Point,Windows.Foundation.Point)">
      <summary>Compare l'inégalité de deux structures <see cref="T:Windows.Foundation.Point" />.</summary>
      <returns>true si <paramref name="point1" /> et <paramref name="point2" /> présentent des valeurs <see cref="P:Windows.Foundation.Point.X" /> ou <see cref="P:Windows.Foundation.Point.Y" /> différentes ; false si <paramref name="point1" /> et <paramref name="point2" /> ont les mêmes valeurs <see cref="P:Windows.Foundation.Point.X" /> et <see cref="P:Windows.Foundation.Point.Y" />.</returns>
      <param name="point1">Premier point à comparer.</param>
      <param name="point2">Deuxième point à comparer.</param>
    </member>
    <member name="M:Windows.Foundation.Point.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>Pour obtenir une description de ce membre, consultez <see cref="M:System.IFormattable.ToString(System.String,System.IFormatProvider)" />.</summary>
      <returns>Chaîne contenant la valeur de l'instance actuelle au format spécifié.</returns>
      <param name="format">Chaîne spécifiant le format à utiliser. ou null pour utiliser le format par défaut défini pour le type de l'implémentation IFormattable. </param>
      <param name="provider">IFormatProvider à utiliser pour mettre en forme la valeur. ou null pour obtenir les informations de mise en forme des nombres à partir des paramètres régionaux définis dans le système d'exploitation. </param>
    </member>
    <member name="M:Windows.Foundation.Point.ToString">
      <summary>Crée une représentation <see cref="T:System.String" /> de <see cref="T:Windows.Foundation.Point" />. </summary>
      <returns>
        <see cref="T:System.String" /> contenant les valeurs <see cref="P:Windows.Foundation.Point.X" /> et <see cref="P:Windows.Foundation.Point.Y" /> de cette structure de <see cref="T:Windows.Foundation.Point" />.</returns>
    </member>
    <member name="M:Windows.Foundation.Point.ToString(System.IFormatProvider)">
      <summary>Crée une représentation <see cref="T:System.String" /> de <see cref="T:Windows.Foundation.Point" />. </summary>
      <returns>
        <see cref="T:System.String" /> contenant les valeurs <see cref="P:Windows.Foundation.Point.X" /> et <see cref="P:Windows.Foundation.Point.Y" /> de cette structure de <see cref="T:Windows.Foundation.Point" />.</returns>
      <param name="provider">Informations de mise en forme spécifiques à la culture.</param>
    </member>
    <member name="P:Windows.Foundation.Point.X">
      <summary>Obtient ou définit la coordonnée <see cref="P:Windows.Foundation.Point.X" />-de cette structure <see cref="T:Windows.Foundation.Point" />. </summary>
      <returns>Valeur de la coordonnée <see cref="P:Windows.Foundation.Point.X" />-de cette structure <see cref="T:Windows.Foundation.Point" />.La valeur par défaut est 0.</returns>
    </member>
    <member name="P:Windows.Foundation.Point.Y">
      <summary>Obtient ou définit la coordonnée <see cref="P:Windows.Foundation.Point.Y" /> de ce <see cref="T:Windows.Foundation.Point" />. </summary>
      <returns>Valeur de la coordonnée <see cref="P:Windows.Foundation.Point.Y" />-de cette structure <see cref="T:Windows.Foundation.Point" />.  La valeur par défaut est 0.</returns>
    </member>
    <member name="T:Windows.Foundation.Rect">
      <summary>Décrit la largeur, la hauteur et le point d'origine d'un rectangle. </summary>
    </member>
    <member name="M:Windows.Foundation.Rect.#ctor(System.Double,System.Double,System.Double,System.Double)">
      <summary>Initialise une structure <see cref="T:Windows.Foundation.Rect" /> qui a les coordonnées x, y, la largeur et la hauteur spécifiées. </summary>
      <param name="x">La coordonnée x du coin supérieur gauche du rectangle.</param>
      <param name="y">La coordonnée y du coin supérieur gauche du rectangle.</param>
      <param name="width">Largeur du rectangle.</param>
      <param name="height">Hauteur du rectangle.</param>
      <exception cref="T:System.ArgumentException">width ou height sont inférieurs à 0.</exception>
    </member>
    <member name="M:Windows.Foundation.Rect.#ctor(Windows.Foundation.Point,Windows.Foundation.Point)">
      <summary>Initialise une structure <see cref="T:Windows.Foundation.Rect" /> qui est exactement assez grande pour contenir les deux points spécifiés. </summary>
      <param name="point1">Le premier point que doit contenir le nouveau rectangle.</param>
      <param name="point2">Le deuxième point que doit contenir le nouveau rectangle.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.#ctor(Windows.Foundation.Point,Windows.Foundation.Size)">
      <summary>Initialise une structure <see cref="T:Windows.Foundation.Rect" /> selon une origine et une taille. </summary>
      <param name="location">Origine du nouveau <see cref="T:Windows.Foundation.Rect" />.</param>
      <param name="size">Taille du nouveau <see cref="T:Windows.Foundation.Rect" />.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Bottom">
      <summary>Obtient la valeur d'axe y du bas du rectangle. </summary>
      <returns>La valeur d'axe y du bas du rectangle.Si le rectangle est vide, la valeur est <see cref="F:System.Double.NegativeInfinity" />.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.Contains(Windows.Foundation.Point)">
      <summary>Indique si le rectangle décrit par le <see cref="T:Windows.Foundation.Rect" /> contient le point spécifié.</summary>
      <returns>true si le rectangle décrit par le <see cref="T:Windows.Foundation.Rect" /> contient le point spécifié ; sinon, false.</returns>
      <param name="point">Point à vérifier.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Empty">
      <summary>Obtient une valeur spéciale qui représente un rectangle sans position ou zone. </summary>
      <returns>Le rectangle vide qui a <see cref="P:Windows.Foundation.Rect.X" /> et des valeurs de propriété <see cref="P:Windows.Foundation.Rect.Y" /> de <see cref="F:System.Double.PositiveInfinity" />et a <see cref="P:Windows.Foundation.Rect.Width" /> et des valeurs de propriété <see cref="P:Windows.Foundation.Rect.Height" /> de <see cref="F:System.Double.NegativeInfinity" />.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.Equals(System.Object)">
      <summary>Indique si l'objet spécifié est égal au <see cref="T:Windows.Foundation.Rect" /> actuel.</summary>
      <returns>true si <paramref name="o" /> est un <see cref="T:Windows.Foundation.Rect" /> et a les mêmes valeurs x,y,largeur,hauteur que le <see cref="T:Windows.Foundation.Rect" /> actuel ; sinon, false.</returns>
      <param name="o">Objet à comparer avec le rectangle actuel.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.Equals(Windows.Foundation.Rect)">
      <summary>Indique si le <see cref="T:Windows.Foundation.Rect" /> spécifié est égal au <see cref="T:Windows.Foundation.Rect" /> actuel. </summary>
      <returns>true si le <see cref="T:Windows.Foundation.Rect" /> spécifié a les mêmes valeurs de propriété x,y,largeur,hauteur que le <see cref="T:Windows.Foundation.Rect" /> actuel ; sinon, false.</returns>
      <param name="value">Le rectangle à comparer avec le rectangle actuel.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.GetHashCode">
      <summary>Crée un code de hachage pour <see cref="T:Windows.Foundation.Rect" />. </summary>
      <returns>Code de hachage pour la structure <see cref="T:Windows.Foundation.Rect" /> actuelle.</returns>
    </member>
    <member name="P:Windows.Foundation.Rect.Height">
      <summary>Obtient ou définit la hauteur du rectangle. </summary>
      <returns>Valeur qui représente la hauteur du rectangle.La valeur par défaut est 0.</returns>
      <exception cref="T:System.ArgumentException">Avoir spécifié une valeur inférieure à 0.</exception>
    </member>
    <member name="M:Windows.Foundation.Rect.Intersect(Windows.Foundation.Rect)">
      <summary>Recherche l'intersection du rectangle représenté par le <see cref="T:Windows.Foundation.Rect" /> courant et le rectangle représenté par le <see cref="T:Windows.Foundation.Rect" /> spécifié, et stocke le résultat comme le <see cref="T:Windows.Foundation.Rect" /> courant. </summary>
      <param name="rect">Le rectangle devant trouver l'intersection avec le rectangle actuel.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.IsEmpty">
      <summary>Obtient une valeur qui indique si le rectangle est le rectangle <see cref="P:Windows.Foundation.Rect.Empty" />.</summary>
      <returns>true si le rectangle est le rectangle<see cref="P:Windows.Foundation.Rect.Empty" /> ; sinon, false.</returns>
    </member>
    <member name="P:Windows.Foundation.Rect.Left">
      <summary>Obtient la valeur d'axe x du côté gauche du rectangle. </summary>
      <returns>La  valeur d'axe x du côté gauche du rectangle.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.op_Equality(Windows.Foundation.Rect,Windows.Foundation.Rect)">
      <summary>Compare l'égalité de deux structures <see cref="T:Windows.Foundation.Rect" />.</summary>
      <returns>true si les structures <see cref="T:Windows.Foundation.Rect" /> ont les mêmes valeurs de propriété x,y,largeur,hauteur ; sinon, false.</returns>
      <param name="rect1">Premier rectangle à comparer.</param>
      <param name="rect2">Deuxième rectangle à comparer.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.op_Inequality(Windows.Foundation.Rect,Windows.Foundation.Rect)">
      <summary>Compare l'inégalité de deux structures <see cref="T:Windows.Foundation.Rect" />.  </summary>
      <returns>true si les structures <see cref="T:Windows.Foundation.Rect" /> n'ont pas les mêmes valeurs de propriété x,y,largeur,hauteur ; sinon, false.</returns>
      <param name="rect1">Premier rectangle à comparer.</param>
      <param name="rect2">Deuxième rectangle à comparer.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Right">
      <summary>Obtient la valeur d'axe x du côté droit du rectangle.  </summary>
      <returns>La valeur d'axe x du côté droit du rectangle.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>Pour obtenir une description de ce membre, consultez <see cref="M:System.IFormattable.ToString(System.String,System.IFormatProvider)" />.</summary>
      <returns>Chaîne contenant la valeur de l'instance actuelle au format spécifié.</returns>
      <param name="format">Chaîne spécifiant le format à utiliser. ou null pour utiliser le format par défaut défini pour le type de l'implémentation IFormattable. </param>
      <param name="provider">IFormatProvider à utiliser pour mettre en forme la valeur. ou null pour obtenir les informations de mise en forme des nombres à partir des paramètres régionaux définis dans le système d'exploitation. </param>
    </member>
    <member name="P:Windows.Foundation.Rect.Top">
      <summary>Obtient la position de l'axe y du haut du rectangle. </summary>
      <returns>La  position de l'axe y du haut du rectangle.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.ToString">
      <summary>Retourne la représentation sous forme de chaîne de la structure <see cref="T:Windows.Foundation.Rect" />. </summary>
      <returns>Représentation sous forme de chaîne de la structure <see cref="T:Windows.Foundation.Rect" /> actuelle.La chaîne a la forme suivante : « <see cref="P:Windows.Foundation.Rect.X" />,<see cref="P:Windows.Foundation.Rect.Y" />,<see cref="P:Windows.Foundation.Rect.Width" />,<see cref="P:Windows.Foundation.Rect.Height" /> ».</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.ToString(System.IFormatProvider)">
      <summary>Renvoie une représentation sous forme de chaîne du rectangle en utilisant le fournisseur de format spécifié. </summary>
      <returns>Une représentation sous forme de chaîne du rectangle actuel qui est déterminée par le fournisseur de format spécifié.</returns>
      <param name="provider">Informations de mise en forme spécifiques à la culture.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.Union(Windows.Foundation.Point)">
      <summary>Développe le rectangle représenté par le <see cref="T:Windows.Foundation.Rect" /> actuel de façon à contenir exactement le point spécifié. </summary>
      <param name="point">Le point à inclure.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.Union(Windows.Foundation.Rect)">
      <summary>Développe le rectangle représenté par le <see cref="T:Windows.Foundation.Rect" /> actuel de façon à contenir le rectangle spécifié. </summary>
      <param name="rect">Le rectangle à inclure.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Width">
      <summary>Obtient ou définit la largeur du rectangle.  </summary>
      <returns>Valeur qui représente la largeur du rectangle exprimée en pixels.La valeur par défaut est 0.</returns>
      <exception cref="T:System.ArgumentException">Avoir spécifié une valeur inférieure à 0.</exception>
    </member>
    <member name="P:Windows.Foundation.Rect.X">
      <summary>Obtient ou définit la valeur d'axe x du côté gauche du rectangle. </summary>
      <returns>La  valeur d'axe x du côté gauche du rectangle.Cette valeur est interprétée comme pixels dans l'espace de coordonnées.</returns>
    </member>
    <member name="P:Windows.Foundation.Rect.Y">
      <summary>Obtient ou définit la valeur d'axe y du côté supérieur du rectangle. </summary>
      <returns>La valeur d'axe y du côté supérieur du rectangle.Cette valeur est interprétée comme pixels dans l'espace de coordonnées.</returns>
    </member>
    <member name="T:Windows.Foundation.Size">
      <summary>Décrit la largeur et la hauteur d'un objet. </summary>
    </member>
    <member name="M:Windows.Foundation.Size.#ctor(System.Double,System.Double)">
      <summary>Initialise une nouvelle instance de la structure <see cref="T:Windows.Foundation.Size" /> et lui attribue une <paramref name="width" /> et une <paramref name="height" /> initiales.</summary>
      <param name="width">La largeur initiale de l'instance de <see cref="T:Windows.Foundation.Size" />.</param>
      <param name="height">La hauteur initiale de l'instance de <see cref="T:Windows.Foundation.Size" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="width" /> ou <paramref name="height" /> sont inférieurs à 0.</exception>
    </member>
    <member name="P:Windows.Foundation.Size.Empty">
      <summary>Obtient une valeur qui représente un <see cref="T:Windows.Foundation.Size" /> vide statique. </summary>
      <returns>Une instance vide de <see cref="T:Windows.Foundation.Size" />.</returns>
    </member>
    <member name="M:Windows.Foundation.Size.Equals(System.Object)">
      <summary>Compare si un objet et une instance de <see cref="T:Windows.Foundation.Size" /> sont égaux. </summary>
      <returns>true si les tailles sont égales ; sinon false.</returns>
      <param name="o">
        <see cref="T:System.Object" /> à comparer.</param>
    </member>
    <member name="M:Windows.Foundation.Size.Equals(Windows.Foundation.Size)">
      <summary>Compare une valeur à une instance de <see cref="T:Windows.Foundation.Size" /> pour l'égalité. </summary>
      <returns>true si les instances de <see cref="T:Windows.Foundation.Size" /> sont égales ; sinon, false.</returns>
      <param name="value">La taille à comparer avec l'instance actuelle de <see cref="T:Windows.Foundation.Size" />.</param>
    </member>
    <member name="M:Windows.Foundation.Size.GetHashCode">
      <summary>Récupère le code de hachage pour cette instance de <see cref="T:Windows.Foundation.Size" />. </summary>
      <returns>Code de hachage de cette instance de la <see cref="T:Windows.Foundation.Size" />.</returns>
    </member>
    <member name="P:Windows.Foundation.Size.Height">
      <summary>Obtient ou définit la hauteur de cette instance de <see cref="T:Windows.Foundation.Size" />. </summary>
      <returns>
        <see cref="P:Windows.Foundation.Size.Height" /> de cette instance de <see cref="T:Windows.Foundation.Size" />, en pixels.La valeur par défaut est 0.La valeur ne peut pas être négative.</returns>
      <exception cref="T:System.ArgumentException">Avoir spécifié une valeur inférieure à 0.</exception>
    </member>
    <member name="P:Windows.Foundation.Size.IsEmpty">
      <summary>Obtient une valeur qui indique si cette instance de <see cref="T:Windows.Foundation.Size" /> est <see cref="P:Windows.Foundation.Size.Empty" />. </summary>
      <returns>true si cette instance de taille est <see cref="P:Windows.Foundation.Size.Empty" /> ; sinon, false.</returns>
    </member>
    <member name="M:Windows.Foundation.Size.op_Equality(Windows.Foundation.Size,Windows.Foundation.Size)">
      <summary>Compare l'égalité de deux instances de <see cref="T:Windows.Foundation.Size" />. </summary>
      <returns>true si les deux instances de <see cref="T:Windows.Foundation.Size" /> sont égales ; sinon false.</returns>
      <param name="size1">Première instance du <see cref="T:Windows.Foundation.Size" /> à comparer.</param>
      <param name="size2">Deuxième instance de <see cref="T:Windows.Foundation.Size" /> à comparer.</param>
    </member>
    <member name="M:Windows.Foundation.Size.op_Inequality(Windows.Foundation.Size,Windows.Foundation.Size)">
      <summary>Compare l'inégalité de deux instances de <see cref="T:Windows.Foundation.Size" />. </summary>
      <returns>true si les instances de <see cref="T:Windows.Foundation.Size" /> ne sont pas égales ; sinon, false.</returns>
      <param name="size1">Première instance du <see cref="T:Windows.Foundation.Size" /> à comparer.</param>
      <param name="size2">Deuxième instance de <see cref="T:Windows.Foundation.Size" /> à comparer.</param>
    </member>
    <member name="M:Windows.Foundation.Size.ToString">
      <summary>Retourne une représentation sous forme de chaîne de <see cref="T:Windows.Foundation.Size" />.</summary>
      <returns>Représentation sous forme de chaîne de <see cref="T:Windows.Foundation.Size" />.</returns>
    </member>
    <member name="P:Windows.Foundation.Size.Width">
      <summary>Obtient ou définit la largeur de cette instance de <see cref="T:Windows.Foundation.Size" />. </summary>
      <returns>
        <see cref="P:Windows.Foundation.Size.Width" /> de cette instance de <see cref="T:Windows.Foundation.Size" />, en pixels.La valeur par défaut est 0.La valeur ne peut pas être négative.</returns>
      <exception cref="T:System.ArgumentException">Avoir spécifié une valeur inférieure à 0.</exception>
    </member>
    <member name="T:Windows.UI.Color">
      <summary>Décrit une couleur en termes de canaux alpha, rouges, verts et bleus. </summary>
    </member>
    <member name="P:Windows.UI.Color.A">
      <summary>Obtient ou définit la valeur du canal alpha sRGB de la couleur. </summary>
      <returns>Valeur du canal alpha sRGB de la couleur, sous la forme d'une valeur comprise entre 0 et 255.</returns>
    </member>
    <member name="P:Windows.UI.Color.B">
      <summary>Obtient ou définit la valeur du canal bleu sRGB de la couleur. </summary>
      <returns>Valeur du canal bleu sRGB, sous la forme d'une valeur comprise entre 0 et 255.</returns>
    </member>
    <member name="M:Windows.UI.Color.Equals(System.Object)">
      <summary>Vérifie si l'objet spécifié est une structure <see cref="T:Windows.UI.Color" /> et s'il équivaut à la couleur actuelle. </summary>
      <returns>true si l'objet spécifié est une structure <see cref="T:Windows.UI.Color" /> et est identique à la structure <see cref="T:Windows.UI.Color" /> actuelle ; sinon, false.</returns>
      <param name="o">Objet à comparer à la structure <see cref="T:Windows.UI.Color" /> en cours.</param>
    </member>
    <member name="M:Windows.UI.Color.Equals(Windows.UI.Color)">
      <summary>Teste si la structure <see cref="T:Windows.UI.Color" /> spécifiée est identique à la couleur actuelle.</summary>
      <returns>true si la structure <see cref="T:Windows.UI.Color" /> spécifiée est identique à la structure <see cref="T:Windows.UI.Color" /> actuelle ; sinon, false.</returns>
      <param name="color">Structure <see cref="T:Windows.UI.Color" /> à comparer à la structure <see cref="T:Windows.UI.Color" /> en cours.</param>
    </member>
    <member name="M:Windows.UI.Color.FromArgb(System.Byte,System.Byte,System.Byte,System.Byte)">
      <summary>Crée une nouvelle structure <see cref="T:Windows.UI.Color" /> en utilisant le canal alpha sRVB et les valeurs de canaux de couleurs spécifiés. </summary>
      <returns>Structure <see cref="T:Windows.UI.Color" /> avec les valeurs spécifiées.</returns>
      <param name="a">Canal alpha, <see cref="P:Windows.UI.Color.A" />, de la nouvelle couleur.La valeur doit être comprise entre 0 et 255.</param>
      <param name="r">Canal rouge, <see cref="P:Windows.UI.Color.R" />, de la nouvelle couleur.La valeur doit être comprise entre 0 et 255.</param>
      <param name="g">Canal vert, <see cref="P:Windows.UI.Color.G" />, de la nouvelle couleur.La valeur doit être comprise entre 0 et 255.</param>
      <param name="b">Canal bleu, <see cref="P:Windows.UI.Color.B" />, de la nouvelle couleur.La valeur doit être comprise entre 0 et 255.</param>
    </member>
    <member name="P:Windows.UI.Color.G">
      <summary>Obtient ou définit la valeur du canal vert sRGB de la couleur. </summary>
      <returns>Valeur du canal vert sRGB, sous la forme d'une valeur comprise entre 0 et 255.</returns>
    </member>
    <member name="M:Windows.UI.Color.GetHashCode">
      <summary>Obtient un code de hachage pour la structure <see cref="T:Windows.UI.Color" /> actuelle. </summary>
      <returns>Code de hachage pour la structure <see cref="T:Windows.UI.Color" /> actuelle.</returns>
    </member>
    <member name="M:Windows.UI.Color.op_Equality(Windows.UI.Color,Windows.UI.Color)">
      <summary>Teste si deux structures <see cref="T:Windows.UI.Color" /> sont identiques. </summary>
      <returns>true si <paramref name="color1" /> et <paramref name="color2" /> sont strictement identiques ; sinon, false.</returns>
      <param name="color1">Première structure <see cref="T:Windows.UI.Color" /> à comparer.</param>
      <param name="color2">Deuxième structure <see cref="T:Windows.UI.Color" /> à comparer.</param>
    </member>
    <member name="M:Windows.UI.Color.op_Inequality(Windows.UI.Color,Windows.UI.Color)">
      <summary>Teste si deux structures <see cref="T:Windows.UI.Color" /> ne sont pas identiques. </summary>
      <returns>true si <paramref name="color1" /> et <paramref name="color2" /> ne sont pas égales ; sinon, false.</returns>
      <param name="color1">Première structure <see cref="T:Windows.UI.Color" /> à comparer.</param>
      <param name="color2">Deuxième structure <see cref="T:Windows.UI.Color" /> à comparer.</param>
    </member>
    <member name="P:Windows.UI.Color.R">
      <summary>Obtient ou définit la valeur du canal rouge sRGB de la couleur. </summary>
      <returns>Valeur du canal rouge sRGB, sous la forme d'une valeur comprise entre 0 et 255.</returns>
    </member>
    <member name="M:Windows.UI.Color.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>Pour obtenir une description de ce membre, consultez <see cref="M:System.IFormattable.ToString(System.String,System.IFormatProvider)" />.</summary>
      <returns>Chaîne contenant la valeur de l'instance actuelle au format spécifié.</returns>
      <param name="format">Chaîne spécifiant le format à utiliser. ou null pour utiliser le format par défaut défini pour le type de l'implémentation IFormattable. </param>
      <param name="provider">IFormatProvider à utiliser pour mettre en forme la valeur. ou null pour obtenir les informations de mise en forme des nombres à partir des paramètres régionaux définis dans le système d'exploitation. </param>
    </member>
    <member name="M:Windows.UI.Color.ToString">
      <summary>Crée une représentation sous forme de chaîne de la couleur à l'aide des canaux ARGB en notation hexadécimale. </summary>
      <returns>Représentation sous forme de chaîne de la couleur.</returns>
    </member>
    <member name="M:Windows.UI.Color.ToString(System.IFormatProvider)">
      <summary>Crée une représentation sous forme de chaîne de la couleur en utilisant les canaux ARGB et le fournisseur de format spécifié. </summary>
      <returns>Représentation sous forme de chaîne de la couleur.</returns>
      <param name="provider">Informations de mise en forme spécifiques à la culture.</param>
    </member>
  </members>
</doc>