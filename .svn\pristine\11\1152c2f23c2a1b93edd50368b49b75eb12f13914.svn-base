using OCRTools.Properties;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Windows.Forms;

namespace OCRTools
{
    public class FmPastColor : DskinPaste
    {
        private ToolStripMenuItem closeItem;

        private IContainer components;

        private bool leftFlag;

        private Point mouseOff;

        private ContextMenuStrip RightMenu;

        private readonly SaveFileDialog saveFileDialog1;

        private ToolStripMenuItem saveItem;

        private ToolStripSeparator toolStripSeparator1;

        private ToolStripSeparator toolStripSeparator2;

        private ToolStripMenuItem 复制RGBToolStripMenuItem;

        private ToolStripMenuItem 复制HEXToolStripMenuItem;

        private ToolStripMenuItem 隐藏阴影ToolStripMenuItem;

        private PictureBox duiPictureBox1;

        private ToolStripMenuItem topItem;

        private const int WM_LBUTTONDBLCLK = 515;

        public event Mydoubleclick MyMousedoubleclick;

        public FmPastColor(Image img)
        {
            InitializeComponent();
            duiPictureBox1.BackgroundImage = img;
            Size size = new Size(img.Size.Width + 8, img.Size.Height + 8);
            MaximumSize = (MinimumSize = size);
            base.Size = size;
            base.Left = (Screen.PrimaryScreen.Bounds.Width - base.Width) / 2 - 4;
            base.Top = (Screen.PrimaryScreen.Bounds.Height - base.Height) / 2 - 4;
            MyMousedoubleclick += mouseDoubleClick;
            saveFileDialog1 = new SaveFileDialog();
            RightMenu.Renderer = new ComboxbtnRenderer(fontCenter: true);
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            Keys keyData = e.KeyData;
            if (keyData == Keys.Escape)
            {
                Close();
            }
        }

        private void RightCMS_Opening(object sender, CancelEventArgs e)
        {
            if (base.TopMost)
            {
                topItem.Text = "取消置顶";
            }
            else
            {
                topItem.Text = "置顶窗体";
            }
        }

        private void TopItemClick(object sender, EventArgs e)
        {
            base.TopMost = !base.TopMost;
        }

        private void Shadow_Click(object sender, EventArgs e)
        {
            if (隐藏阴影ToolStripMenuItem.Text == "显示阴影")
            {
                隐藏阴影ToolStripMenuItem.Text = "隐藏阴影";
                BackgroundImage = Resources.morepush_bg;
            }
            else
            {
                隐藏阴影ToolStripMenuItem.Text = "显示阴影";
                BackgroundImage = null;
            }
        }

        private void CloseItemClick(object sender, EventArgs e)
        {
            GC.Collect();
            Close();
        }

        private void SaveFile(Image bmp)
        {
            string text = DateTime.Now.ToString("yyyyMMddhhmmss");
            string text2 = null;
            string text3 = text;
            for (int i = 0; i < text3.Length; i++)
            {
                char c = text3[i];
                if (c != '/' && c != ':' && c != ' ')
                {
                    text2 += c.ToString();
                }
            }
            saveFileDialog1.Filter = "png文件|*.png|jpg文件|*.jpg|bmp文件|*.bmp";
            saveFileDialog1.Title = "保存位置";
            saveFileDialog1.FileName = StaticValue.CatchName + "_" + text2;
            saveFileDialog1.FilterIndex = 0;
            this.CenterChild();
            if (saveFileDialog1.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            string extension = Path.GetExtension(saveFileDialog1.FileName);
            ImageFormat png = ImageFormat.Png;
            if (extension != "")
            {
                ImageFormat format;
                switch (extension)
                {
                    case ".jpg":
                        format = ImageFormat.Jpeg;
                        break;
                    case ".bmp":
                        format = ImageFormat.Bmp;
                        break;
                    case ".png":
                        format = ImageFormat.Png;
                        break;
                    default:
                        format = ImageFormat.Png;
                        break;
                }
                bmp.SafeSave(saveFileDialog1.FileName, format);
            }
        }

        private void saveItem_Click(object sender, EventArgs e)
        {
            SaveFile(duiPictureBox1.BackgroundImage);
        }

        protected override void WndProc(ref Message m)
        {
            if (m.Msg == 515)
            {
                MouseEventArgs e = new MouseEventArgs(MouseButtons.Left, 2, Control.MousePosition.X, Control.MousePosition.Y, 0);
                if (this.MyMousedoubleclick != null)
                {
                    this.MyMousedoubleclick(this, e);
                }
            }
            else
            {
                base.WndProc(ref m);
            }
        }

        private void mouseDoubleClick(object sender, MouseEventArgs e)
        {
            GC.Collect();
            Close();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            RightMenu = new System.Windows.Forms.ContextMenuStrip(components);
            closeItem = new System.Windows.Forms.ToolStripMenuItem();
            复制RGBToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            复制HEXToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            saveItem = new System.Windows.Forms.ToolStripMenuItem();
            topItem = new System.Windows.Forms.ToolStripMenuItem();
            隐藏阴影ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            duiPictureBox1 = new PictureBox();
            RightMenu.SuspendLayout();
            SuspendLayout();
            RightMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[6]
            {
                closeItem,
                复制RGBToolStripMenuItem,
                复制HEXToolStripMenuItem,
                saveItem,
                topItem,
                隐藏阴影ToolStripMenuItem
            });
            RightMenu.Name = "RightMenu";
            RightMenu.ShowImageMargin = false;
            RightMenu.Size = new System.Drawing.Size(101, 136);
            RightMenu.Opening += new System.ComponentModel.CancelEventHandler(RightCMS_Opening);
            closeItem.Name = "closeItem";
            closeItem.Size = new System.Drawing.Size(100, 22);
            closeItem.Text = "销毁贴图";
            closeItem.Click += new System.EventHandler(CloseItemClick);
            复制RGBToolStripMenuItem.Name = "复制RGBToolStripMenuItem";
            复制RGBToolStripMenuItem.Size = new System.Drawing.Size(100, 22);
            复制RGBToolStripMenuItem.Text = "复制RGB";
            复制RGBToolStripMenuItem.Click += new System.EventHandler(复制RGBToolStripMenuItem_Click);
            复制HEXToolStripMenuItem.Name = "复制HEXToolStripMenuItem";
            复制HEXToolStripMenuItem.Size = new System.Drawing.Size(100, 22);
            复制HEXToolStripMenuItem.Text = "复制HEX";
            复制HEXToolStripMenuItem.Click += new System.EventHandler(复制HEXToolStripMenuItem_Click);
            saveItem.Name = "saveItem";
            saveItem.Size = new System.Drawing.Size(100, 22);
            saveItem.Text = "保存图片";
            saveItem.Click += new System.EventHandler(saveItem_Click);
            topItem.Name = "topItem";
            topItem.Size = new System.Drawing.Size(100, 22);
            topItem.Text = "取消置顶";
            topItem.Click += new System.EventHandler(TopItemClick);
            隐藏阴影ToolStripMenuItem.Name = "隐藏阴影ToolStripMenuItem";
            隐藏阴影ToolStripMenuItem.Size = new System.Drawing.Size(100, 22);
            隐藏阴影ToolStripMenuItem.Text = "隐藏阴影";
            隐藏阴影ToolStripMenuItem.Click += new System.EventHandler(Shadow_Click);
            duiPictureBox1.AutoSize = false;
            duiPictureBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            duiPictureBox1.Font = new System.Drawing.Font("宋体", 9f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            duiPictureBox1.Image = null;
            duiPictureBox1.Location = new System.Drawing.Point(0, 0);
            duiPictureBox1.Margin = new System.Windows.Forms.Padding(4);
            duiPictureBox1.Name = "duiPictureBox1";
            duiPictureBox1.Size = new System.Drawing.Size(185, 162);
            duiPictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
            base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
            BackColor = System.Drawing.Color.Transparent;
            BackgroundImage = OCRTools.Properties.Resources.morepush_bg;
            BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            base.ClientSize = new System.Drawing.Size(185, 162);
            ContextMenuStrip = RightMenu;
            base.ControlBox = false;
            Controls.Add(duiPictureBox1);
            base.Name = "FmPastColor";
            base.TopMost = true;
            base.FormClosed += new System.Windows.Forms.FormClosedEventHandler(FmPastColor_FormClosed);
            base.Load += new System.EventHandler(FmPastColor_Load);
            base.Shown += new System.EventHandler(FmPastColor_Shown);
            RightMenu.ResumeLayout(false);
            ResumeLayout(false);
        }

        private void FmPastColor_Load(object sender, EventArgs e)
        {
        }

        private void 复制RGBToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Clipboard.SetText(ColorValue.colorRgb);
        }

        private void 复制HEXToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Clipboard.SetText(ColorValue.colorHex);
        }

        private void FmPastColor_Shown(object sender, EventArgs e)
        {
            StaticValue.handles.Add(base.Handle);
        }

        private void FmPastColor_FormClosed(object sender, FormClosedEventArgs e)
        {
            StaticValue.handles.Remove(base.Handle);
        }
    }
}
