﻿using OCRTools.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class UcLoading : UserControl
    {
        private LoadingTypeConfig _config;

        private bool _isStop = true;
        private Thread _thread;

        private int iC;

        public UcLoading()
        {
            InitializeComponent();
        }

        public void InitLoading(Size size, Point locaion)
        {
            Visible = false;
            Size = size;
            Location = locaion;
            BackColor = Color.Transparent;
            BackgroundImageLayout = ImageLayout.Center;
            picImage.MouseDoubleClick += UcLoading_MouseDoubleClick;
            lblText.MouseDoubleClick += UcLoading_MouseDoubleClick;
            MouseDoubleClick += UcLoading_MouseDoubleClick;
        }

        private void UcLoading_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            var form = FindForm();
            if (form == null) return;
            form.WindowState = form.WindowState == FormWindowState.Maximized
                ? FormWindowState.Normal
                : FormWindowState.Maximized;
        }

        public void ShowLoading(string strText = "", bool isShowLoading = true)
        {
            ShowText(strText);
            Visible = isShowLoading;
            if (isShowLoading)
            {
                CommonString.IsOnRec = true;
                BringToFront();
                if (_isStop)
                {
                    _isStop = false;
                    _thread = new Thread(Start);
                    _thread.Start();
                }
            }
        }

        private void Start()
        {
            while (!_isStop)
            {
                TmrTick();
                Thread.Sleep(_config.Interval);
            }

            _thread = null;
        }

        public void ShowText(string strText)
        {
            lblText.Text = strText;
            lblText.ForeColor = CommonSetting.Get默认文字颜色();
        }

        public void CloseLoading(int seconds = 0)
        {
            if (seconds > 0)
                for (var i = 0; i < seconds * 2; i++)
                {
                    Thread.Sleep(500);
                    Application.DoEvents();
                }

            Visible = false;
            _isStop = true;
            CommonString.IsOnRec = false;
        }

        ConcurrentDictionary<string, Bitmap> dicTickImage = new ConcurrentDictionary<string, Bitmap>();

        private void TmrTick()
        {
            try
            {
                if (iC >= _config.ImgCount) iC = 0;
                var imgKey = iC + (CommonSetting.夜间模式 ? "_dark" : "") + (_config.IsRound ? "_" + iC * 30 : "");
                Bitmap _bgImg = dicTickImage.ContainsKey(imgKey) ? dicTickImage[imgKey] : null;
                if (_bgImg == null)
                {
                    _bgImg = LoadingTypeHelper.GetImageByConfig(_config, _config.IsRound ? 0 : iC, _config.IsIngoreTheme);
                    if (_config.IsRound)
                    {
                        _bgImg = RotateImage(_bgImg, iC * 30);
                    }
                }
                try
                {
                    if (!dicTickImage.ContainsKey(imgKey))
                    {
                        dicTickImage.TryAdd(imgKey, _bgImg);
                    }
                }
                catch { }
                picImage.Image = _bgImg;
                iC++;
                Application.DoEvents();
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        Bitmap RotateImage(Image image, float angle)
        {
            if (image == null)
                throw new ArgumentNullException("image");
            float dx = image.Width / 2.0f;
            float dy = image.Height / 2.0f;

            Bitmap rotatedBmp = new Bitmap(image.Width, image.Height);
            rotatedBmp.SetResolution(image.HorizontalResolution, image.VerticalResolution);
            using (Graphics g = Graphics.FromImage(rotatedBmp))
            {
                g.TranslateTransform(dx, dy);
                g.RotateTransform(angle);
                g.TranslateTransform(-dx, -dy);
                g.DrawImage(image, new PointF(0, 0));
            }
            return rotatedBmp;
        }

        internal void SetLoadingType(LoadingType loadingType)
        {
            _config = LoadingTypeHelper.GetTypeConfig(loadingType);
            iC = 0;
            dicTickImage.Clear();
        }
    }
}