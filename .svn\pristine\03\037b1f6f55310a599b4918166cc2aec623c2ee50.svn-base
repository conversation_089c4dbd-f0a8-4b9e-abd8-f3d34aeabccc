// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class DockPattern : BasePattern
    {
        public static readonly AutomationProperty DockPositionProperty = DockPatternIdentifiers.DockPositionProperty;
        public static readonly AutomationPattern Pattern = DockPatternIdentifiers.Pattern;


        private DockPattern(AutomationElement el, IUIAutomationDockPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new DockPattern(el, (IUIAutomationDockPattern)pattern, cached);
        }
    }
}