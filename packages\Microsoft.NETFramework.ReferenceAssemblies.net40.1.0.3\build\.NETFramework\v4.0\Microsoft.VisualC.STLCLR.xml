﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.VisualC.STLCLR</name>
  </assembly>
  <members>
    <member name="T:Microsoft.VisualC.StlClr.BinaryDelegate`3">
      <summary>Describes a two-argument delegate. You use it to specify the arguments and return type of the delegate.</summary>
      <returns>The return type of the delegate.</returns>
      <param name="param0">The first argument.</param>
      <param name="param1">The second argument.</param>
      <typeparam name="TArg1">The first delegate argument.</typeparam>
      <typeparam name="TArg2">The second delegate argument.</typeparam>
      <typeparam name="TResult">The return type of the delegate.</typeparam>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.DequeEnumerator`1">
      <summary>Supports simple iteration over any STL/CLR object that implements the <see cref="T:Microsoft.VisualC.StlClr.IDeque`1" /> interface.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.
   </typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.DequeEnumerator`1.#ctor(Microsoft.VisualC.StlClr.IDeque{`0},System.Int32)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.DequeEnumerator`1" /> object.</summary>
      <param name="_Cont">The container to iterate over.</param>
      <param name="_First">The offset of the current first element in the container.</param>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.DequeEnumerator`1.Current">
      <summary>Gets or sets the current element in the collection.</summary>
      <returns>The current element in the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.DequeEnumerator`1.Dispose">
      <summary>Frees, releases, or resets unmanaged resources that are used by the <see cref="T:Microsoft.VisualC.StlClr.DequeEnumerator`1" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.DequeEnumerator`1.Dispose(System.Boolean)">
      <summary>Frees, releases, or resets unmanaged resources that are used by the <see cref="T:Microsoft.VisualC.StlClr.DequeEnumerator`1" /> object.</summary>
      <param name="__unnamed0">true to dispose of managed objects; false to dispose of unmanaged objects.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.DequeEnumerator`1.MoveNext">
      <summary>Advances the enumerator to the next element in the collection.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.DequeEnumerator`1.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.DequeEnumeratorBase`1">
      <summary>Supports simple iteration over any STL/CLR object that implements the <see cref="T:Microsoft.VisualC.StlClr.IDeque`1" /> interface.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.DequeEnumeratorBase`1.#ctor(Microsoft.VisualC.StlClr.IDeque{`0},System.Int32)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.DequeEnumeratorBase`1" /> object.</summary>
      <param name="_Cont">The container over which to iterate.</param>
      <param name="_First">The offset of the current first element in the container.</param>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.DequeEnumeratorBase`1.Current">
      <summary>Gets or sets the current element in the collection.</summary>
      <returns>The current element in the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.DequeEnumeratorBase`1.MoveNext">
      <summary>Advances the enumerator to the next element in the collection.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.DequeEnumeratorBase`1.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.GenericPair`2">
      <summary>Describes an object that wraps a pair of values.</summary>
      <typeparam name="TValue1">Type of the first value in the pair.</typeparam>
      <typeparam name="TValue2">Type of the second value in the pair.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.GenericPair`2.#ctor">
      <summary>Constructs a new <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object with default values for <see cref="F:Microsoft.VisualC.StlClr.GenericPair`2.first" /> and <see cref="F:Microsoft.VisualC.StlClr.GenericPair`2.second" />.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.GenericPair`2.#ctor(Microsoft.VisualC.StlClr.GenericPair{`0,`1}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Constructs a new <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object.</summary>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object to be copied into the new <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.GenericPair`2.#ctor(System.Collections.Generic.KeyValuePair{`0,`1}@)">
      <summary>Constructs a new <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object from an existing <see cref="T:System.Collections.Generic.KeyValuePair`2" /> object.</summary>
      <param name="_Right">The <see cref="T:System.Collections.Generic.KeyValuePair`2" /> object to be copied into the new <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.GenericPair`2.#ctor(`0)">
      <summary>Constructs a new <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object and assigns its first value to the specified value.</summary>
      <param name="_Val1">The value to store in <see cref="F:Microsoft.VisualC.StlClr.GenericPair`2.first" />. The <see cref="F:Microsoft.VisualC.StlClr.GenericPair`2.second" /> value is assigned the default value for its type.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.GenericPair`2.#ctor(`0,`1)">
      <summary>Constructs a new <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object with the two values specified.</summary>
      <param name="_Val1">The value to store in <see cref="F:Microsoft.VisualC.StlClr.GenericPair`2.first" />.</param>
      <param name="_Val2">The value to store in <see cref="F:Microsoft.VisualC.StlClr.GenericPair`2.second" />.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.GenericPair`2.Equals(System.Object)">
      <summary>Determines if two <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> objects are equal.</summary>
      <returns>true if the two <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> objects are equal; otherwise, false.</returns>
      <param name="_Right_arg">The <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object.</param>
    </member>
    <member name="F:Microsoft.VisualC.StlClr.GenericPair`2.first">
      <summary>The first wrapped value of the <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.GenericPair`2.op_Assign(Microsoft.VisualC.StlClr.GenericPair{`0,`1}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Replaces the stored pair of values in the <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object.</summary>
      <returns>This method returns *this.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object to copy.</param>
    </member>
    <member name="F:Microsoft.VisualC.StlClr.GenericPair`2.second">
      <summary>The second wrapped value of the <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.GenericPair`2.swap(Microsoft.VisualC.StlClr.GenericPair{`0,`1}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Swaps the contents of two <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> objects.</summary>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.GenericPair`2" /> object to swap contents with.</param>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.HashEnumerator`2">
      <summary>Supports simple iteration over any STL/CLR object that implements the <see cref="T:Microsoft.VisualC.StlClr.IHash`2" /> interface.</summary>
      <typeparam name="TKey">The type of the hash iterator key. </typeparam>
      <typeparam name="TValue">The type of the hash iterator value.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.HashEnumerator`2.#ctor(Microsoft.VisualC.StlClr.Generic.INode{`1})">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.HashEnumerator`2" /> object.</summary>
      <param name="_First">The first node in the container over which to iterate.</param>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.HashEnumerator`2.Current">
      <summary>Gets or sets the current element in the collection.</summary>
      <returns>The current element in the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.HashEnumerator`2.Dispose">
      <summary>Frees, releases, or resets unmanaged resources that are used by the <see cref="T:Microsoft.VisualC.StlClr.HashEnumerator`2" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.HashEnumerator`2.Dispose(System.Boolean)">
      <summary>Frees, releases, or resets unmanaged resources that are used by the <see cref="T:Microsoft.VisualC.StlClr.HashEnumerator`2" /> object.</summary>
      <param name="__unnamed0">true to dispose of managed objects; false to dispose of unmanaged objects.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.HashEnumerator`2.MoveNext">
      <summary>Advances the enumerator to the next element in the collection.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.HashEnumerator`2.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.HashEnumeratorBase`2">
      <summary>Supports simple iteration over any STL/CLR object that implements the <see cref="T:Microsoft.VisualC.StlClr.IHash`2" /> interface.</summary>
      <typeparam name="TKey">The type of the hash iterator key.</typeparam>
      <typeparam name="TValue">The type of the hash iterator value.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.HashEnumeratorBase`2.#ctor(Microsoft.VisualC.StlClr.Generic.INode{`1})">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.HashEnumeratorBase`2" /> object.</summary>
      <param name="_First">The first node in the container over which to iterate.</param>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.HashEnumeratorBase`2.Current">
      <summary>Gets or sets the current element in the collection.</summary>
      <returns>The current element in the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.HashEnumeratorBase`2.MoveNext">
      <summary>Advances the enumerator to the next element in the collection.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.HashEnumeratorBase`2.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.IDeque`1">
      <summary>Defines the interface of an STL/CLR deque object.</summary>
      <typeparam name="TValue">The type that is contained.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.assign(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0},Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Replaces all elements of the container with the elements specified by the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> objects.</summary>
      <param name="_First">The starting position of the range to insert into the container.</param>
      <param name="_Last">The first position beyond the end of the range to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.assign(System.Collections.IEnumerable)">
      <summary>Replaces all elements of the container with the elements in the given <see cref="T:System.Collections.IEnumerable" /> object.</summary>
      <param name="_Right">The enumeration to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.assign(System.Int32,`0)">
      <summary>Replaces all elements of the container with the given number of specified elements.</summary>
      <param name="_Count">The number of elements to insert into the container.</param>
      <param name="_Val">The value of the element to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.at(System.Int32)">
      <summary>Accesses an element at a specified position in the container.</summary>
      <returns>The element of the controlled sequence at position <paramref name="pos" />.</returns>
      <param name="_Pos">Position of element to access.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.back">
      <summary>Accesses the last element of the container.</summary>
      <returns>The last element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.IDeque`1.back_item">
      <summary>Accesses the last element of a non-empty deque collection.</summary>
      <returns>The last element of the controlled sequence.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.begin(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@)">
      <summary>Designates the beginning of the controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A random-access iterator that designates the first element of the controlled sequence, or just beyond the end of an empty sequence. You use it to obtain an iterator that designates the <paramref name="current" /> beginning of the controlled sequence, but its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.begin_bias">
      <summary>Gets the bias of the beginning of the current sequence. The bias is the offset of the current element zero.</summary>
      <returns>The bias of the beginning of the current sequence.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.clear">
      <summary>Removes all elements in the container.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.empty">
      <summary>Tests whether the container holds no elements.</summary>
      <returns>true if the container is empty; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.end(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@)">
      <summary>Designates the end of the controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A random-access iterator that points to the location immediately beyond the end of the controlled sequence. You use it to obtain an iterator that designates the <paramref name="current" /> end of the controlled sequence, but its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.end_bias">
      <summary>Gets the bias of the end of the current sequence. The bias is the offset of the current element zero.</summary>
      <returns>The bias of the end of the current sequence.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.erase(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@,Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue)">
      <summary>Removes the element at the specified position.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the first element remaining beyond any elements removed, or <see cref="M:Microsoft.VisualC.StlClr.IDeque`1.end(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@)" /> if no such element exists.</param>
      <param name="_Where">The element to erase.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.erase(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@,Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue,Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue)">
      <summary>Removes the elements between the specified iterators.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the first element remaining beyond any elements removed, or <see cref="M:Microsoft.VisualC.StlClr.IDeque`1.end(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@)" /> if no such element exists.</param>
      <param name="_First_iter">The beginning position of the range to erase.</param>
      <param name="_Last_iter">The position one beyond the last element of the range to erase.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.front">
      <summary>Accesses the first element of the container.</summary>
      <returns>The first element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.IDeque`1.front_item">
      <summary>Accesses the first element of a non-empty deque collection.</summary>
      <returns>The first element of the controlled sequence.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.get_generation">
      <summary>Gets the current change generation of the underlying container.</summary>
      <returns>The current change generation of the underlying container.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.insert(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@,Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue,`0)">
      <summary>Adds an element at a specified position in the container.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the newly inserted element.</param>
      <param name="_Where">The location in the container in front of which to insert.</param>
      <param name="_Val">The element to be inserted into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.insert(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue,Microsoft.VisualC.StlClr.Generic.IInputIterator{`0},Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Adds elements at a specified position in the container.</summary>
      <param name="_Where">The location in the container in front of which to insert.</param>
      <param name="_First">The beginning of the range to insert into the container.</param>
      <param name="_Last">The first position beyond the end of the range to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.insert(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue,System.Collections.IEnumerable)">
      <summary>Adds elements at a specified position in the container.</summary>
      <param name="_Where_iter">The location in the container in front of which to insert.</param>
      <param name="_Right">The enumeration of elements to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.insert(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue,System.Int32,`0)">
      <summary>Adds the given number of elements at a specified position in the container.</summary>
      <param name="_Where">The location in the container in front of which to insert.</param>
      <param name="_Count">The number of elements to insert into the container.</param>
      <param name="_Val">The value of the elements to be inserted into the container.</param>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.IDeque`1.Item(System.Int32)">
      <summary>Gets or sets the element at the indicated position in the container.</summary>
      <returns>The element at the indicated position.</returns>
      <param name="_Pos">The position of the element to get or set.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.pop_back">
      <summary>Removes the last element of a non-empty container.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.pop_front">
      <summary>Removes the first element of a non-empty container.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.push_back(`0)">
      <summary>Adds an element to the end of a container.</summary>
      <param name="_Val">The element to append to the end of the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.push_front(`0)">
      <summary>Adds an element to the beginning of a container.</summary>
      <param name="_Val">The element to add to the beginning of the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.rbegin(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}@)">
      <summary>Designates the beginning of the reversed controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reverse iterator that designates the last element of the controlled sequence, or just beyond the beginning of an empty sequence. Hence, it designates the <paramref name="beginning" /> of the reverse sequence. You use it to obtain an iterator that designates the <paramref name="current" /> beginning of the controlled sequence seen in reverse order, but its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.rend(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}@)">
      <summary>Designates the end of the reversed controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reverse iterator that points just beyond the beginning of the controlled sequence. Hence, it designates the <paramref name="end" /> of the reverse sequence. You use it to obtain an iterator that designates the <paramref name="current" /> end of the controlled sequence seen in reverse order, but its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.resize(System.Int32)">
      <summary>Changes the number of elements.</summary>
      <param name="_Newsize">The new size of the controlled sequence.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.resize(System.Int32,`0)">
      <summary>Changes the number of elements.</summary>
      <param name="_Newsize">The new size of the controlled sequence.</param>
      <param name="_Val">The value of the padding element.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.size">
      <summary>Counts the number of elements.</summary>
      <returns>The length of the controlled sequence.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IDeque`1.swap(Microsoft.VisualC.StlClr.IDeque{`0})">
      <summary>Swaps the contents of two containers.</summary>
      <param name="param0">Container to swap contents with.</param>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.IHash`2">
      <summary>Defines the interface of the STL/CLR hash_map, hash_multimap, hash_set, and hash_multiset objects.</summary>
      <typeparam name="TKey">The type of the key component of an element in the controlled sequence.</typeparam>
      <typeparam name="TValue">The type of the value component of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.begin(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@)">
      <summary>Designates the beginning of the controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A bidirectional iterator that designates the first element of the controlled sequence, or just beyond the end of an empty sequence. You use it to obtain an iterator that designates the <paramref name="current" /> beginning of the controlled sequence, but its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.bucket_count">
      <summary>Counts the number of buckets in the hash table.</summary>
      <returns>The current number of buckets in the hash table.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.clear">
      <summary>Removes all elements from the container.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.count(`0)">
      <summary>Finds the number of elements that match a specified key.</summary>
      <returns>The number of elements in the controlled sequence that have the same key as <paramref name="_Keyval" />. You use it to determine the number of elements currently in the controlled sequence that match a specified key.</returns>
      <param name="_Keyval">The key value for which to search.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.empty">
      <summary>Tests whether no elements are present in the container.</summary>
      <returns>true if the container has no elements; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@)">
      <summary>Designates the end of the controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A bidirectional iterator that points just beyond the end of the controlled sequence. You use it to obtain an iterator that designates the end of the controlled sequence. Its status does not change when the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.equal_range(Microsoft.VisualC.StlClr.GenericPair{Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1},Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}}@,`0)">
      <summary>Finds the range that matches a specified key.</summary>
      <returns>A pair of iterators.</returns>
      <param name="__unnamed0">A pair of iterators that determine the range of elements currently in the controlled sequence that match a specified key.</param>
      <param name="_Keyval">The key value for which to search.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.erase(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@,Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}|System.Runtime.CompilerServices.IsByValue)">
      <summary>Removes from the container the element that is specified by the given iterator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the first element beyond the removed element, or <see cref="M:Microsoft.VisualC.StlClr.IHash`2.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@)" /> if no such element exists.</param>
      <param name="_Where">An iterator that points to the element to erase.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.erase(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@,Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}|System.Runtime.CompilerServices.IsByValue,Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}|System.Runtime.CompilerServices.IsByValue)">
      <summary>Removes from the container the elements between the specified iterators.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the first element beyond the elements removed, or <see cref="M:Microsoft.VisualC.StlClr.IHash`2.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@)" /> if no such element exists.</param>
      <param name="_First_iter">An iterator that points to the beginning of the range to erase.</param>
      <param name="_Last_iter">An iterator that points to the position that immediately follows the range to erase.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.erase(`0)">
      <summary>Removes elements from the container that match the specified key.</summary>
      <returns>The number of elements removed.</returns>
      <param name="_Keyval">The key value to erase.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.find(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@,`0)">
      <summary>Finds an element that matches a specified key.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates one of the found elements; or <see cref="M:Microsoft.VisualC.StlClr.IHash`2.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@)" /> if no element is found.</param>
      <param name="_Keyval">The key value for which to search.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.hash_delegate">
      <summary>Returns the delegate that is used to convert a key value to an integer.</summary>
      <returns>The delegate that is used to convert a key value to an integer.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.insert(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@,Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}|System.Runtime.CompilerServices.IsByValue,`1)">
      <summary>Adds the given element to the container.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the newly inserted element.</param>
      <param name="_Where">An iterator that specifies where in the container to insert the element. This is a hint only and is used to improve performance. The element might not be inserted at this location.</param>
      <param name="_Val">The key value to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.insert(Microsoft.VisualC.StlClr.Generic.IInputIterator{`1},Microsoft.VisualC.StlClr.Generic.IInputIterator{`1})">
      <summary>Adds to the container the elements specified by the given iterators.</summary>
      <param name="_First">An iterator that points to the beginning of the range of elements to insert.</param>
      <param name="_Last">An iterator that points to the element that immediately follows the range of elements to insert.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.insert(Microsoft.VisualC.StlClr.GenericPair{Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1},System.Boolean}@,`1)">
      <summary>Adds the given value to the container.</summary>
      <returns>A pair of iterators.</returns>
      <param name="__unnamed0">A pair of values X. If X.second is true, X.first designates the newly inserted element; otherwise X.first designates an element with equivalent ordering that already exists, and no new element is inserted.</param>
      <param name="_Val">The key value to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.insert(System.Collections.IEnumerable)">
      <summary>Adds the given enumeration to the container.</summary>
      <param name="_Right">The enumeration to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.key_comp">
      <summary>Returns the ordering delegate that is used to order the controlled sequence. You use it to compare two keys.</summary>
      <returns>The ordering delegate that is used to order the controlled sequence.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.load_factor">
      <summary>Counts the average number of elements per bucket in the hash table.</summary>
      <returns>The average number of elements per bucket in the hash table.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.lower_bound(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@,`0)">
      <summary>Finds the beginning of the range of elements that match a specified key.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the first element in the controlled sequence that hashes to the same bucket as <paramref name="_Keyval" /> and has equivalent ordering to <paramref name="_Keyval" />. If no such element exists, it returns <see cref="M:Microsoft.VisualC.StlClr.IHash`2.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@)" />.</param>
      <param name="_Keyval">The key value for which to search.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.max_load_factor">
      <summary>Gets the maximum number of elements per bucket in the hash table.</summary>
      <returns>The maximum number of elements per bucket in the hash table.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.max_load_factor(System.Single)">
      <summary>Sets the maximum number of elements per bucket in the hash table.</summary>
      <param name="_Newmax">The maximum number of elements per bucket in the hash table.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.rbegin(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`1}@)">
      <summary>Designates the beginning of the reversed controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reverse iterator that designates the last element of the controlled sequence, or just beyond the beginning of an empty sequence. Hence, it designates the <paramref name="beginning" /> of the reverse sequence. You use it to obtain an iterator that designates the <paramref name="current" /> beginning of the controlled sequence seen in reverse order. Its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.rehash(System.Int32)">
      <summary>Rebuilds the hash table.</summary>
      <param name="_Buckets">The number of buckets for the hash table.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.rend(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`1}@)">
      <summary>Designates the end of the reversed controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reverse iterator that points just beyond the beginning of the controlled sequence. Hence, it designates the <paramref name="end" /> of the reverse sequence. You use it to obtain an iterator that designates the <paramref name="current" /> end of the controlled sequence seen in reverse order. Its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.size">
      <summary>Counts the number of elements in the container.</summary>
      <returns>The length of the controlled sequence.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.swap(Microsoft.VisualC.StlClr.IHash{`0,`1})">
      <summary>Swaps the contents of two containers.</summary>
      <param name="_Right">The container with which to swap contents.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.upper_bound(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@,`0)">
      <summary>Finds the end of the range of elements that match a specified key.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the first element beyond the last element in the controlled sequence that hashes to the same bucket as <paramref name="_Keyval" /> and has equivalent ordering to <paramref name="_Keyval" />. If no such element exists, it returns <see cref="M:Microsoft.VisualC.StlClr.IHash`2.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@)" />.</param>
      <param name="_Keyval">The key value to search for.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IHash`2.value_comp">
      <summary>Returns the ordering delegate that is used to order the controlled sequence.</summary>
      <returns>The ordering delegate that is used to order the controlled sequence.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.IList`1">
      <summary>Defines the interface for an STL/CLR list object.</summary>
      <typeparam name="TValue">The type of a list element.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.assign(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0},Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Replaces the controlled sequence with the sequence [<paramref name="_First" />, <paramref name="_Last" />).</summary>
      <param name="_First">The beginning position of the range of elements to insert into the container.</param>
      <param name="_Last">The first position beyond the range of elements to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.assign(System.Collections.IEnumerable)">
      <summary>Replaces the controlled sequence with the sequence that is designated by the given enumerator.</summary>
      <param name="_Right">The enumeration whose elements are to be inserted into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.assign(System.Int32,`0)">
      <summary>Replaces the controlled sequence with the specified number of the given element.</summary>
      <param name="_Count">The number of elements to insert into the container.</param>
      <param name="_Val">The value of the elements to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.back">
      <summary>Accesses the last element of the container.</summary>
      <returns>The last element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.IList`1.back_item">
      <summary>Accesses the last element of the container.</summary>
      <returns>The last element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.begin(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}@)">
      <summary>Designates the beginning of the controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A random-access iterator that designates the first element of the controlled sequence, or just beyond the end of an empty sequence. You use it to obtain an iterator that designates the <paramref name="current" /> beginning of the controlled sequence, but its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.clear">
      <summary>Removes all elements from the container.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.empty">
      <summary>Tests whether the container has no elements.</summary>
      <returns>true if the container has no elements; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}@)">
      <summary>Designates the end of the controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A random-access iterator that points just beyond the end of the controlled sequence. You use it to obtain an iterator that designates the end of the controlled sequence. Its status does not change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.erase(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}@,Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsByValue)">
      <summary>Removes the single element of the controlled sequence pointed to by the given iterator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the first element remaining beyond any elements removed, or <see cref="M:Microsoft.VisualC.StlClr.IList`1.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}@)" /> if no such element exists.</param>
      <param name="_Where">The position of the element to erase.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.erase(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}@,Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsByValue,Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsByValue)">
      <summary>Removes the elements of the controlled sequence in the range [<paramref name="_First_iter" />, <paramref name="_Last_iter" />).</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the first element remaining beyond any elements removed, or <see cref="M:Microsoft.VisualC.StlClr.IList`1.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}@)" /> if no such element exists.</param>
      <param name="_First_iter">The beginning position of the range of elements to erase.</param>
      <param name="_Last_iter">The first position beyond the range of elements to erase.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.front">
      <summary>Accesses the first element of the container.</summary>
      <returns>The first element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.IList`1.front_item">
      <summary>Accesses the first element of the container.</summary>
      <returns>The first element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.insert(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}@,Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsByValue,`0)">
      <summary>Inserts an element of the given value into the container.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the newly inserted element.</param>
      <param name="_Where">The position in the container immediately following the newly inserted elements.</param>
      <param name="_Val">The value of the element to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.insert(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsByValue,Microsoft.VisualC.StlClr.Generic.IInputIterator{`0},Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Inserts the sequence [<paramref name="_First" />, <paramref name="_Last" />) into the container.</summary>
      <param name="_Where">The position in the container immediately following the newly inserted elements.</param>
      <param name="_First">The beginning position of the range of elements to insert into the container.</param>
      <param name="_Last">The first position beyond the range of elements to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.insert(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsByValue,System.Collections.IEnumerable)">
      <summary>Inserts the elements of the specified enumerator into the container.</summary>
      <param name="_Where_iter">The position in the container to insert before.</param>
      <param name="_Right">The enumeration whose elements are to be inserted into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.insert(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsByValue,System.Int32,`0)">
      <summary>Inserts the specified number of elements of the given value into the container.</summary>
      <param name="_Where">The position in the container immediately following the newly inserted elements.</param>
      <param name="_Count">The number of elements to insert into the container.</param>
      <param name="_Val">The value of the elements to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.merge(Microsoft.VisualC.StlClr.IList{`0},Microsoft.VisualC.StlClr.BinaryDelegate{`0,`0,System.Boolean})">
      <summary>Merges two ordered controlled sequences.</summary>
      <param name="_Right">The container to merge into this container.</param>
      <param name="_Pred">The Boolean functor for comparing pairs of elements.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.pop_back">
      <summary>Removes the last element from the container.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.pop_front">
      <summary>Removes the first element from the container.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.push_back(`0)">
      <summary>Adds a new element to the end of the container.</summary>
      <param name="_Val">The value of the element to append to the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.push_front(`0)">
      <summary>Adds a new element to the beginning of the container.</summary>
      <param name="_Val">The value of the element to insert at the beginning of the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.rbegin(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}@)">
      <summary>Designates the beginning of the reversed controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reverse iterator that designates the last element of the controlled sequence, or the position just beyond the beginning of an empty sequence. Hence, it designates the <paramref name="beginning" /> of the reverse sequence. You use it to obtain an iterator that designates the <paramref name="current" /> beginning of the controlled sequence that is seen in reverse order, but its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.remove(`0)">
      <summary>Removes an element that has a specified value from the container.</summary>
      <param name="_Val">The value of the element to remove from the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.remove_if(Microsoft.VisualC.StlClr.UnaryDelegate{`0,System.Boolean})">
      <summary>Removes elements from the container that pass a specified test.</summary>
      <param name="_Pred">A Boolean test that determines which elements will be removed.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.rend(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}@)">
      <summary>Designates the end of the reversed controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reverse iterator that points just beyond the beginning of the controlled sequence. Hence, it designates the <paramref name="end" /> of the reverse sequence. You use it to obtain an iterator that designates the <paramref name="current" /> end of the controlled sequence seen in reverse order, but its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.resize(System.Int32)">
      <summary>Changes the number of elements in the container to the specified size.</summary>
      <param name="_Newsize">The new size of the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.resize(System.Int32,`0)">
      <summary>Changes the number of elements in the container to the specified size. If the new size is larger than the old size, the given values will be appended to the container.</summary>
      <param name="_Newsize">The new size of the container.</param>
      <param name="_Val">The value of the padding elements.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.reverse">
      <summary>Reverses the elements of the controlled sequence.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.size">
      <summary>Counts the number of elements in the container.</summary>
      <returns>The length of the controlled sequence.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.sort(Microsoft.VisualC.StlClr.BinaryDelegate{`0,`0,System.Boolean})">
      <summary>Orders the controlled sequence.</summary>
      <param name="_Pred">The operator that will be used to compare elements for ordering.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.splice(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsByValue,Microsoft.VisualC.StlClr.IList{`0})">
      <summary>Inserts the given sequence into the container before the specified position.</summary>
      <param name="_Where">The position in the container before which to splice.</param>
      <param name="_Right">The container to splice from. All elements will be removed from this container after the splice operation.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.splice(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsByValue,Microsoft.VisualC.StlClr.IList{`0},Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsByValue)">
      <summary>Removes the element from the given container pointed to by <paramref name="_First" /> and inserts it before the element in the controlled sequence pointed to by <paramref name="_Where" />.</summary>
      <param name="_Where">The position in the container before which to splice.</param>
      <param name="_Right">The container to splice from.</param>
      <param name="_First">The position of the element to splice.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.splice(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsByValue,Microsoft.VisualC.StlClr.IList{`0},Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsByValue,Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsByValue)">
      <summary>Removes the range of elements [<paramref name="_First" />, <paramref name="_Last" />) from the given container and inserts it before the element in the controlled sequence pointed to by <paramref name="_Where" />.</summary>
      <param name="_Where">The position in the container before which to splice.</param>
      <param name="_Right">The container to splice from.</param>
      <param name="_First">The beginning position of the range of elements to splice.</param>
      <param name="_Last">The first position beyond the range of elements to splice.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.swap(Microsoft.VisualC.StlClr.IList{`0})">
      <summary>Swaps the contents of two containers.</summary>
      <param name="_Right">The container to swap contents with.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IList`1.unique(Microsoft.VisualC.StlClr.BinaryDelegate{`0,`0,System.Boolean})">
      <summary>Removes adjacent elements that pass a specified test.</summary>
      <param name="_Pred">The comparer that determines whether element pairs are unique. An element that is not unique will be removed from the container.</param>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.IPriorityQueue`2">
      <summary>Defines the interface for an STL/CLR priority_queue object.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
      <typeparam name="TCont">The type of the underlying container.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IPriorityQueue`2.assign(Microsoft.VisualC.StlClr.IPriorityQueue{`0,`1})">
      <summary>Replaces all elements of the container.</summary>
      <param name="_Right">The container adapter to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IPriorityQueue`2.empty">
      <summary>Determines whether the container contains no elements.</summary>
      <returns>true if the container contains no elements; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IPriorityQueue`2.get_container">
      <summary>Accesses the underlying container.</summary>
      <returns>The underlying container. You can use it to bypass the restrictions that are imposed by the container wrapper.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IPriorityQueue`2.pop">
      <summary>Removes the highest-priority element of the container.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IPriorityQueue`2.push(`0)">
      <summary>Adds a new element to the container.</summary>
      <param name="_Val">An element to insert into the controlled sequence. The controlled sequence is then reordered to maintain the heap discipline.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IPriorityQueue`2.size">
      <summary>Counts the number of elements in the container.</summary>
      <returns>The length of the controlled sequence.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IPriorityQueue`2.top">
      <summary>Accesses the highest-priority element of the container.</summary>
      <returns>The top (highest-priority) element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.IPriorityQueue`2.top_item">
      <summary>Accesses the highest-priority element of the container.</summary>
      <returns>The top (highest-priority) element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IPriorityQueue`2.value_comp">
      <summary>Copies the ordering delegate for two elements.</summary>
      <returns>The ordering delegate used to order the controlled sequence. You use it to compare two values.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.IQueue`2">
      <summary>Defines the interface for an STL/CLR queue object.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
      <typeparam name="TCont">The type of the underlying container.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IQueue`2.assign(Microsoft.VisualC.StlClr.IQueue{`0,`1})">
      <summary>Replaces all elements of the container with the contents of the provided container.</summary>
      <param name="_Right">The container adapter whose elements will be inserted into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IQueue`2.back">
      <summary>Accesses the last element of the container.</summary>
      <returns>The last element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IQueue`2.empty">
      <summary>Determines whether the container contains no elements.</summary>
      <returns>true if the container contains no elements; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IQueue`2.front">
      <summary>Accesses the first element of the container.</summary>
      <returns>The first element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IQueue`2.get_container">
      <summary>Accesses the underlying container.</summary>
      <returns>The underlying container. You use it to bypass the restrictions that are imposed by the container wrapper.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IQueue`2.pop">
      <summary>Removes the last element of the container.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IQueue`2.push(`0)">
      <summary>Adds an element to the beginning of the container.</summary>
      <param name="_Val">The value to add to the beginning of the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IQueue`2.size">
      <summary>Counts the number of elements in the container.</summary>
      <returns>The length of the controlled sequence.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.IStack`2">
      <summary>Defines the interface of an STL/CLR stack object.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
      <typeparam name="TCont">The type of the underlying container.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IStack`2.assign(Microsoft.VisualC.StlClr.IStack{`0,`1})">
      <summary>Replaces all elements in the container with the elements in the provided container.</summary>
      <param name="_Right">The container adapter whose elements are to be inserted into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IStack`2.empty">
      <summary>Determines whether the container contains no elements.</summary>
      <returns>true if the container contains no elements; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IStack`2.get_container">
      <summary>Accesses the underlying container.</summary>
      <returns>The underlying container. You use it to bypass the restrictions that are imposed by the container wrapper.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IStack`2.pop">
      <summary>Removes the last element of the container.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IStack`2.push(`0)">
      <summary>Appends an element to the container.</summary>
      <param name="_Val">The value to be appended to the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IStack`2.size">
      <summary>Counts the number of elements in the container.</summary>
      <returns>The length of the controlled sequence.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IStack`2.top">
      <summary>Accesses the last element of the container.</summary>
      <returns>The last element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.IStack`2.top_item">
      <summary>Accesses the last element of the container.</summary>
      <returns>The last element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.ITree`2">
      <summary>Defines the interface of the STL/CLR hash_map, hash_multimap, hash_set, and hash_multiset objects.</summary>
      <typeparam name="TKey">The type of the key component of an element in the controlled sequence.</typeparam>
      <typeparam name="TValue">The type of the value component of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.begin(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@)">
      <summary>Designates the beginning of the controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A bidirectional iterator that designates the first element of the controlled sequence, or just beyond the end of an empty sequence. You use it to obtain an iterator that designates the <paramref name="current" /> beginning of the controlled sequence, but its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.clear">
      <summary>Removes all elements from the container.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.count(`0)">
      <summary>Finds the number of elements that match a specified key.</summary>
      <returns>The number of elements in the controlled sequence that have the same key as <paramref name="_Keyval" />. You use it to determine the number of elements currently in the controlled sequence that match a specified key.</returns>
      <param name="_Keyval">The key value for which to search.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.empty">
      <summary>Tests whether no elements are present in the container.</summary>
      <returns>true if the container has no elements; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@)">
      <summary>Designates the end of the controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A bidirectional iterator that points just beyond the end of the controlled sequence. You use it to obtain an iterator that designates the end of the controlled sequence. Its status does not change when the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.equal_range(Microsoft.VisualC.StlClr.GenericPair{Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1},Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}}@,`0)">
      <summary>Finds the range that matches a specified key.</summary>
      <returns>A pair of iterators.</returns>
      <param name="__unnamed0">A pair of iterators that determine the range of elements currently in the controlled sequence that match a specified key.</param>
      <param name="_Keyval">The key value for which to search.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.erase(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@,Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}|System.Runtime.CompilerServices.IsByValue)">
      <summary>Removes from the container the element that is specified by the given iterator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the first element beyond the removed element, or <see cref="M:Microsoft.VisualC.StlClr.ITree`2.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@)" /> if no such element exists.</param>
      <param name="_Where">An iterator that points to the element to erase.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.erase(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@,Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}|System.Runtime.CompilerServices.IsByValue,Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}|System.Runtime.CompilerServices.IsByValue)">
      <summary>Removes from the container the elements between the specified iterators.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the first element beyond the elements removed, or <see cref="M:Microsoft.VisualC.StlClr.ITree`2.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@)" /> if no such element exists.</param>
      <param name="_First_iter">An iterator that points to the beginning of the range to erase.</param>
      <param name="_Last_iter">An iterator that points to the position that immediately follows the range to erase.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.erase(`0)">
      <summary>Removes elements from the container that match the specified key.</summary>
      <returns>The number of elements removed.</returns>
      <param name="_Keyval">The key value to erase.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.find(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@,`0)">
      <summary>Finds an element that matches a specified key.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates one of the found elements; or <see cref="M:Microsoft.VisualC.StlClr.ITree`2.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@)" /> if no element is found.</param>
      <param name="_Keyval">The key value for which to search.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.insert(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@,Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}|System.Runtime.CompilerServices.IsByValue,`1)">
      <summary>Adds the given element to the container.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the newly inserted element.</param>
      <param name="_Where">An iterator that specifies where in the container to insert the element. This is a hint only and is used to improve performance. The element might not be inserted at this location.</param>
      <param name="_Val">The key value to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.insert(Microsoft.VisualC.StlClr.Generic.IInputIterator{`1},Microsoft.VisualC.StlClr.Generic.IInputIterator{`1})">
      <summary>Adds to the container the elements specified by the given iterators.</summary>
      <param name="_First">An iterator that points to the beginning of the range of elements to insert.</param>
      <param name="_Last">An iterator that points to the element that immediately follows the range of elements to insert.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.insert(Microsoft.VisualC.StlClr.GenericPair{Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1},System.Boolean}@,`1)">
      <summary>Adds the given value to the container.</summary>
      <returns>A pair of iterators.</returns>
      <param name="__unnamed0">A pair of values X. If X.second is true, X.first designates the newly inserted element; otherwise X.first designates an element with equivalent ordering that already exists, and no new element is inserted.</param>
      <param name="_Val">The key value to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.insert(System.Collections.Generic.IEnumerable{`1})">
      <summary>Adds the given enumeration to the container.</summary>
      <param name="_Right">The enumeration to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.key_comp">
      <summary>Returns the ordering delegate that is used to order the controlled sequence. You use it to compare two keys.</summary>
      <returns>The ordering delegate that is used to order the controlled sequence.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.lower_bound(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@,`0)">
      <summary>Finds the beginning of the range of elements that match a specified key.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the first element in the controlled sequence that hashes to the same bucket as <paramref name="_Keyval" /> and has equivalent ordering to <paramref name="_Keyval" />. If no such element exists, it returns <see cref="M:Microsoft.VisualC.StlClr.ITree`2.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@)" />.</param>
      <param name="_Keyval">The key value for which to search.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.rbegin(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`1}@)">
      <summary>Designates the beginning of the reversed controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reverse iterator that designates the last element of the controlled sequence, or just beyond the beginning of an empty sequence. Hence, it designates the <paramref name="beginning" /> of the reverse sequence. You use it to obtain an iterator that designates the <paramref name="current" /> beginning of the controlled sequence seen in reverse order. Its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.rend(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`1}@)">
      <summary>Designates the end of the reversed controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reverse iterator that points just beyond the beginning of the controlled sequence. Hence, it designates the <paramref name="end" /> of the reverse sequence. You use it to obtain an iterator that designates the <paramref name="current" /> end of the controlled sequence seen in reverse order. Its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.size">
      <summary>Counts the number of elements in the container.</summary>
      <returns>The length of the controlled sequence.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.swap(Microsoft.VisualC.StlClr.ITree{`0,`1})">
      <summary>Swaps the contents of two containers.</summary>
      <param name="_Right">The container with which to swap contents.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.upper_bound(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@,`0)">
      <summary>Finds the end of the range of elements that match a specified key.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the first element beyond the last element in the controlled sequence that hashes to the same bucket as <paramref name="_Keyval" /> and has equivalent ordering to <paramref name="_Keyval" />. If no such element exists, it returns <see cref="M:Microsoft.VisualC.StlClr.ITree`2.end(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`1}@)" />.</param>
      <param name="_Keyval">The key value to search for.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ITree`2.value_comp">
      <summary>Returns the ordering delegate that is used to order the controlled sequence.</summary>
      <returns>The ordering delegate that is used to order the controlled sequence.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.IVector`1">
      <summary>Defines the interface for an STL/CLR vector object.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.assign(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0},Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Replaces all elements in the container with the elements between the given iterators.</summary>
      <param name="_First">The beginning position of the range of elements to insert.</param>
      <param name="_Last">The first position beyond the end of the range of elements to insert.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.assign(System.Collections.IEnumerable)">
      <summary>Replaces all elements in the container with the elements in the specified enumeration.</summary>
      <param name="_Right">The enumeration whose elements are to be inserted into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.assign(System.Int32,`0)">
      <summary>Replaces all elements in the container with the specified number of elements that have given values.</summary>
      <param name="_Count">The number of elements to insert into the container.</param>
      <param name="_Val">The value of the element to insert.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.at(System.Int32)">
      <summary>Accesses an element at a specified position in the container.</summary>
      <returns>The element of the controlled sequence at position <paramref name="_Pos" />.</returns>
      <param name="_Pos">The position of the element to access.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.back">
      <summary>Accesses the last element of the container.</summary>
      <returns>The last element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.IVector`1.back_item">
      <summary>Accesses the last element of the container.</summary>
      <returns>The last element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.begin(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@)">
      <summary>Designates the beginning of the controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A random-access iterator that designates the first element of the controlled sequence, or just beyond the end of an empty sequence. You use it to obtain an iterator that designates the <paramref name="current" /> beginning of the controlled sequence, but its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.capacity">
      <summary>Reports the size of allocated storage for the container.</summary>
      <returns>The storage that is currently allocated to hold the controlled sequence, a value at least as large as <see cref="M:Microsoft.VisualC.StlClr.IVector`1.size" />.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.clear">
      <summary>Removes all elements from the container.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.empty">
      <summary>Tests whether the container has no elements.</summary>
      <returns>true if the container has no elements; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.end(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@)">
      <summary>Designates the end of the controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A random-access iterator that points just beyond the end of the controlled sequence. You use it to obtain an iterator that designates the <paramref name="current" /> end of the controlled sequence, but its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.erase(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@,Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue)">
      <summary>Removes the single element of the controlled sequence that is pointed to by <paramref name="_Where" />.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the first element that remains beyond any removed elements, or <see cref="M:Microsoft.VisualC.StlClr.IVector`1.end(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@)" /> if no such element exists.</param>
      <param name="_Where">The position of the element to remove.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.erase(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@,Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue,Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue)">
      <summary>Removes the elements of the controlled sequence in the range [<paramref name="_First_iter" />, <paramref name="_Last_iter" />).</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the first element that remains beyond any removed elements, or <see cref="M:Microsoft.VisualC.StlClr.IVector`1.end(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@)" /> if no such element exists.</param>
      <param name="_First_iter">The beginning of the range of elements to erase.</param>
      <param name="_Last_iter">The first position beyond the range of elements to erase.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.front">
      <summary>Accesses the first element of the container.</summary>
      <returns>The first element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.IVector`1.front_item">
      <summary>Accesses the first element of the container.</summary>
      <returns>The first element of the controlled sequence, which must be non-empty.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.get_generation">
      <summary>Gets the current change generation of the underlying container.</summary>
      <returns>The current change generation of the underlying container.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.insert(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@,Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue,`0)">
      <summary>Inserts the specified value into the container.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">An iterator that designates the newly inserted element.</param>
      <param name="_Where">The position in the container before which to insert.</param>
      <param name="_Val">The value of the element to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.insert(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue,Microsoft.VisualC.StlClr.Generic.IInputIterator{`0},Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Inserts the sequence beginning with <paramref name="_First" /> and ending with the element preceding <paramref name="_Last" /> into the container.</summary>
      <param name="_Where">The position in the container before which to insert.</param>
      <param name="_First">The beginning position of the range to insert into the container.</param>
      <param name="_Last">The first position beyond the range to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.insert(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue,System.Collections.IEnumerable)">
      <summary>Inserts the sequence that is designated by an enumerator into the container.</summary>
      <param name="_Where_iter">The position in the container before which to insert.</param>
      <param name="_Right">The enumeration to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.insert(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue,System.Int32,`0)">
      <summary>Inserts the specified number of the given elements into the container.</summary>
      <param name="_Where">The position in the container before which to insert.</param>
      <param name="_Count">The number of elements to insert into the container.</param>
      <param name="_Val">The value of the element to insert into the container.</param>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.IVector`1.Item(System.Int32)">
      <summary>Accesses an element at a specified position in the container.</summary>
      <returns>The element at position <paramref name="_Pos" />.</returns>
      <param name="_Pos">The position of the element to access.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.pop_back">
      <summary>Removes the last element from the container.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.push_back(`0)">
      <summary>Adds a new element to the end of the container.</summary>
      <param name="_Val">The value of the element to insert into the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.rbegin(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}@)">
      <summary>Designates the beginning of the reversed controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reverse iterator that designates the last element of the controlled sequence, or just beyond the beginning of an empty sequence. Hence, it designates the <paramref name="beginning" /> of the reverse sequence. You use it to obtain an iterator that designates the <paramref name="current" /> beginning of the controlled sequence seen in reverse order, but its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.rend(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}@)">
      <summary>Designates the end of the reversed controlled sequence.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reverse iterator that points just beyond the beginning of the controlled sequence. Hence, it designates the <paramref name="end" /> of the reverse sequence. You use it to obtain an iterator that designates the <paramref name="current" /> end of the controlled sequence seen in reverse order, but its status can change if the length of the controlled sequence changes.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.reserve(System.Int32)">
      <summary>Reserves storage to ensure the minimum growth capacity for the container.</summary>
      <param name="_Capacity">The new minimum capacity of the container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.resize(System.Int32)">
      <summary>Changes the number of elements in the container to the specified size.</summary>
      <param name="_Newsize">The new size of the controlled sequence.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.resize(System.Int32,`0)">
      <summary>Changes the number of elements in the container to the specified size. If the new size is larger than the old size, the method appends elements that have value <paramref name="_Val" />.</summary>
      <param name="_Newsize">The new size of the controlled sequence.</param>
      <param name="_Val">The value of the elements to insert.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.size">
      <summary>Counts the number of elements in the container.</summary>
      <returns>The length of the controlled sequence.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.IVector`1.swap(Microsoft.VisualC.StlClr.IVector{`0})">
      <summary>Swaps the contents of two containers.</summary>
      <param name="param0">The container with which to swap contents.</param>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.ListEnumerator`1">
      <summary>Supports simple iteration over any STL/CLR object that implements the <see cref="T:Microsoft.VisualC.StlClr.IList`1" /> interface.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ListEnumerator`1.#ctor(Microsoft.VisualC.StlClr.Generic.INode{`0})">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.ListEnumerator`1" /> object.</summary>
      <param name="_First">The first node in the container over which to iterate.</param>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.ListEnumerator`1.Current">
      <summary>Gets or sets the current element in the collection.</summary>
      <returns>The current element in the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ListEnumerator`1.Dispose">
      <summary>Frees, releases, or resets unmanaged resources that are used by the <see cref="T:Microsoft.VisualC.StlClr.ListEnumerator`1" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ListEnumerator`1.Dispose(System.Boolean)">
      <summary>Frees, releases, or resets unmanaged resources that are used by the <see cref="T:Microsoft.VisualC.StlClr.ListEnumerator`1" /> object.</summary>
      <param name="__unnamed0">true to dispose of managed objects; false to dispose of unmanaged objects.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ListEnumerator`1.MoveNext">
      <summary>Advances the enumerator to the next element in the collection.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ListEnumerator`1.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.ListEnumeratorBase`1">
      <summary>Supports simple iteration over any STL/CLR object that implements the <see cref="T:Microsoft.VisualC.StlClr.IList`1" /> interface.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ListEnumeratorBase`1.#ctor(Microsoft.VisualC.StlClr.Generic.INode{`0})">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.ListEnumeratorBase`1" /> object.</summary>
      <param name="_First">The first node in the container over which to iterate.</param>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.ListEnumeratorBase`1.Current">
      <summary>Gets or sets the current element in the collection.</summary>
      <returns>The current element in the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ListEnumeratorBase`1.MoveNext">
      <summary>Advances the enumerator to the next element in the collection.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.ListEnumeratorBase`1.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.TreeEnumerator`2">
      <summary>Supports simple iteration over any STL/CLR object that implements the <see cref="T:Microsoft.VisualC.StlClr.ITree`2" /> interface.</summary>
      <typeparam name="TKey">The type of the key component of an element in the controlled sequence.</typeparam>
      <typeparam name="TValue">The type of the value component of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.TreeEnumerator`2.#ctor(Microsoft.VisualC.StlClr.Generic.INode{`1})">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.TreeEnumerator`2" /> object.</summary>
      <param name="_First">The first node in the container over which to iterate.</param>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.TreeEnumerator`2.Current">
      <summary>Gets or sets the current element in the collection.</summary>
      <returns>The current element in the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.TreeEnumerator`2.Dispose">
      <summary>Frees, releases, or resets unmanaged resources that are used by the <see cref="T:Microsoft.VisualC.StlClr.TreeEnumerator`2" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.TreeEnumerator`2.Dispose(System.Boolean)">
      <summary>Frees, releases, or resets unmanaged resources that are used by the <see cref="T:Microsoft.VisualC.StlClr.TreeEnumerator`2" /> object.</summary>
      <param name="__unnamed0">true to dispose of managed objects; false to dispose of unmanaged objects.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.TreeEnumerator`2.MoveNext">
      <summary>Advances the enumerator to the next element in the collection.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.TreeEnumerator`2.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.TreeEnumeratorBase`2">
      <summary>Supports simple iteration over any STL/CLR object that implements the <see cref="T:Microsoft.VisualC.StlClr.ITree`2" /> interface.</summary>
      <typeparam name="TKey">The type of the key component of an element in the controlled sequence.</typeparam>
      <typeparam name="TValue">The type of the value component of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.TreeEnumeratorBase`2.#ctor(Microsoft.VisualC.StlClr.Generic.INode{`1})">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.TreeEnumeratorBase`2" /> object.</summary>
      <param name="_First">The first node in the container over which to iterate.</param>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.TreeEnumeratorBase`2.Current">
      <summary>Gets or sets the current element in the collection.</summary>
      <returns>The current element in the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.TreeEnumeratorBase`2.MoveNext">
      <summary>Advances the enumerator to the next element in the collection.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.TreeEnumeratorBase`2.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.UnaryDelegate`2">
      <summary>Describes a one-argument delegate. You use it to specify the argument and return type of the delegate.</summary>
      <returns>The return type of the delegate.</returns>
      <param name="param0">The delegate argument.</param>
      <typeparam name="TArg">The type of the delegate argument.</typeparam>
      <typeparam name="TResult">The return type of the delegate.</typeparam>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.VectorEnumerator`1">
      <summary>Supports simple iteration over any STL/CLR object that implements the <see cref="T:Microsoft.VisualC.StlClr.IVector`1" /> interface.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.VectorEnumerator`1.#ctor(Microsoft.VisualC.StlClr.IVector{`0},System.Int32)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.VectorEnumerator`1" /> object.</summary>
      <param name="_Cont">The container over which to iterate.</param>
      <param name="_First">The offset of the current first element in the container.</param>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.VectorEnumerator`1.Current">
      <summary>Gets or sets the current element in the collection.</summary>
      <returns>The current element in the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.VectorEnumerator`1.Dispose">
      <summary>Frees, releases, or resets unmanaged resources that are used by the <see cref="T:Microsoft.VisualC.StlClr.VectorEnumerator`1" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.VectorEnumerator`1.Dispose(System.Boolean)">
      <summary>Frees, releases, or resets unmanaged resources that are used by the <see cref="T:Microsoft.VisualC.StlClr.VectorEnumerator`1" /> object.</summary>
      <param name="__unnamed0">true to dispose of managed objects; false to dispose of unmanaged objects.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.VectorEnumerator`1.MoveNext">
      <summary>Advances the enumerator to the next element in the collection.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.VectorEnumerator`1.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.VectorEnumeratorBase`1">
      <summary>Supports simple iteration over any STL/CLR object that implements the <see cref="T:Microsoft.VisualC.StlClr.IVector`1" /> interface.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.VectorEnumeratorBase`1.#ctor(Microsoft.VisualC.StlClr.IVector{`0},System.Int32)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.VectorEnumeratorBase`1" /> object.</summary>
      <param name="_Cont">The container over which to iterate.</param>
      <param name="_First">The offset of the current first element in the container.</param>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.VectorEnumeratorBase`1.Current">
      <summary>Gets or sets the current element in the collection.</summary>
      <returns>The current element in the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.VectorEnumeratorBase`1.MoveNext">
      <summary>Advances the enumerator to the next element in the collection.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.VectorEnumeratorBase`1.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1">
      <summary>Defines an iterator that can access elements in a container in the forward direction by using the increment operator and in the backward direction by using the decrement operator. The element that the iterator points to cannot be modified. Bidirectional iterators can be used anywhere that a constant input or constant output iterator is required.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.
   </typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.#ctor">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.INode{`0})">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object that points to the given node.</summary>
      <param name="_Node">The node that the iterator will point to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</summary>
      <param name="_Right">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object to copy.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</summary>
      <param name="_Right">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object to copy.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.Clone">
      <summary>Returns a copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</summary>
      <returns>A copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.container">
      <summary>Gets the container that the iterator is traversing.</summary>
      <returns>The container that the iterator is traversing.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> objects are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.get_bias">
      <summary>Gets the bias of the iterator. The bias is the offset of the iterator from element zero.</summary>
      <returns>The bias of the iterator.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.get_cref">
      <summary>Returns a constant reference to the element that the iterator is currently pointing to.</summary>
      <returns>A constant reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.get_node">
      <summary>Gets the node, or element, that the iterator is pointing to.</summary>
      <returns>The node that the iterator is pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.get_ref">
      <summary>Returns a reference to the element that the iterator is currently pointing to.</summary>
      <returns>A reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.next">
      <summary>Increments the iterator to the next position in the underlying container, or to the first position beyond the end of container if the container has been completely traversed.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.op_Assign(Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Assigns the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object to the current object.</summary>
      <returns>The current object (*this).</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object to assign to the current object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.op_Assign(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Assigns the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object to the current object.</summary>
      <returns>The current object (*this).</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object to assign to the current object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator{`0}@)">
      <summary>Decrements the iterator by one element. This is the prefix version of the decrement operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator{`0}@,System.Int32)">
      <summary>Decrements the iterator by one element. This is the postfix version of the decrement operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">An unused parameter that indicates this is the postfix version of the decrement operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.op_Equality(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.op_Equality(Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.op_Implicit~Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator{`0}">
      <summary>Converts a <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> to an <see cref="T:Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator`1" />.</summary>
      <returns>The current iterator as an <see cref="T:Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator`1" />.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator{`0}@)">
      <summary>Increments the iterator by one element. This is the prefix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator{`0}@,System.Int32)">
      <summary>Increments the iterator by one element. This is the postfix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">An unused parameter that indicates this is the postfix version of the increment operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.op_Inequality(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object differs from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are different objects; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.op_Inequality(Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object differs from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are different objects; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.op_MemberSelection(Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.op_PointerDereference(Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.prev">
      <summary>Positions the iterator to the element immediately before the current element.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerBidirectionalIterator`1.valid">
      <summary>Determines whether the iterator is valid and can be safely used to traverse the underlying container.</summary>
      <returns>true if the iterator is valid and safe to use; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1">
      <summary>Provides an iterator that supports the following operations: moving forward one position by calling operator++, moving backward one position by calling operator--, accessing an element by using operator[], and accessing an element by using pointer arithmetic. The element pointed to by the iterator cannot be modified.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.#ctor">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.IRandomAccessContainer{`0},System.Int32)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessContainer`1" /> object.</summary>
      <param name="_Cont">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessContainer`1" /> object to be copied to the current container.</param>
      <param name="_Offset">The offset of the element that the iterator will point to after creation.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</summary>
      <param name="_Right">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object to be copied to the current container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</summary>
      <param name="_Right">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object to be copied to the current container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.Clone">
      <summary>Creates a copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</summary>
      <returns>A copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.container">
      <summary>Gets the container that the iterator is traversing.</summary>
      <returns>The container that the iterator is traversing.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.distance(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines the distance between the element that the current iterator is pointing to and the element that the given iterator is pointing to.</summary>
      <returns>The distance, in number of elements, between the element that the current iterator is pointing to and the element that <paramref name="_Right" /> is pointing to. This number is negative if <paramref name="_Right" /> points to an element that is before the element that the current iterator points to.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.distance(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines the distance between the element that the current iterator is pointing to and the element that the given iterator is pointing to.</summary>
      <returns>The distance, in number of elements, between the element that the current iterator is pointing to and the element that <paramref name="_Right" /> is pointing to. This number is negative if <paramref name="_Right" /> points to an element that is before the element that the current iterator points to.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> objects are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.get_bias">
      <summary>Gets the bias of the iterator. The bias is the offset of the iterator from element zero.</summary>
      <returns>The bias of the iterator.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.get_cref">
      <summary>Returns a constant reference to the element that the iterator is currently pointing to.</summary>
      <returns>A constant reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.get_node">
      <summary>Gets the node, or element, that the iterator is pointing to.</summary>
      <returns>The node that the iterator is pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.get_ref">
      <summary>Returns a reference to the element that the iterator is currently pointing to.</summary>
      <returns>A reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.Item(System.Int32)">
      <summary>Accesses the element at the given position in the container.</summary>
      <returns>The element at the given position in the container.</returns>
      <param name="_Pos">The position of the element to access.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.less_than(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines whether the current iterator is pointing to an element in the container that is before the element that the given iterator points to.</summary>
      <returns>true if the current iterator is pointing to an element that comes before the element that <paramref name="_Right" /> is pointing to; otherwise, false.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.less_than(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the current iterator is pointing to an element in the container that is before the element that the given iterator points to.</summary>
      <returns>true if the current iterator is pointing to an element that comes before the element that <paramref name="_Right" /> is pointing to; otherwise, false.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.move(System.Int32)">
      <summary>Moves the iterator by the given number of elements.</summary>
      <returns>The actual number of elements traversed. This number will differ from <paramref name="_Offset" /> if the beginning or end of the container is reached before iterating the desired number of elements.</returns>
      <param name="_Offset">The number of elements to traverse. To move the iterator forward, specify a positive number. To move the iterator backward, use a negative number.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.next">
      <summary>Increments the iterator to the next position in the underlying container, or to the first position beyond the end of container if the container has been completely traversed.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Addition(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}@,System.Int32)">
      <summary>Increments the iterator by the given number of elements. The integer parameter is specified on the right side of the addition operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="_Right">The number of elements to increment.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Addition(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}@,System.Int32,Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsByValue)">
      <summary>Increments the iterator by the given number of elements. The integer parameter is specified on the left side of the addition operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="_Left">The number of elements to increment.</param>
      <param name="_Right">The iterator to increment.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Assign(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Assigns the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object to the current object.</summary>
      <returns>The current object (*this).</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object to assign to the current object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Assign(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Assigns the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object to the current object.</summary>
      <returns>The current object (*this).</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object to assign to the current object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}@)">
      <summary>Decrements the iterator by one element. This is the prefix version of the decrement operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}@,System.Int32)">
      <summary>Decrements the iterator by one element. This is the postfix version of the decrement operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">An unused parameter that indicates this is the postfix version of the operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Equality(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Equality(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_GreaterThan(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0},Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the given left-side iterator is greater than the position of the given right-side iterator.</summary>
      <returns>true if the given left-side iterator is greater than the given right-side iterator; otherwise, false.</returns>
      <param name="_Left">The iterator to compare with the right-side iterator.</param>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_GreaterThan(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is greater than the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is greater than the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_GreaterThanOrEqual(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines whether the position of the left-side iterator is greater than or equal to the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is greater than or equal to the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_GreaterThanOrEqual(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is greater than or equal to the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is greater than or equal to the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Implicit~Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0}">
      <summary>Converts a <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> to an <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" />.</summary>
      <returns>The current iterator as an <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" />.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}@)">
      <summary>Increments the iterator by one element. This is the prefix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}@,System.Int32)">
      <summary>Increments the iterator by one element. This is the postfix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">An unused parameter that indicates this is the postfix version of the operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Inequality(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object differs from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two iterators are different objects; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Inequality(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object differs from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two iterators are different objects; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_LessThan(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines whether the position of the left-side iterator is less than the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is less than the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_LessThan(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is less than the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is less than the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_LessThanOrEqual(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0},Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the given left-side iterator is less than the position of the given right-side iterator.</summary>
      <returns>true if the given left-side iterator is less than or equal to the given right-side iterator; otherwise, false.</returns>
      <param name="_Left">The iterator to compare with the right-side iterator.</param>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_LessThanOrEqual(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is less than or equal to the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is less than or equal to the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_MemberSelection(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_PointerDereference(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Subtraction(Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator{`0}@,System.Int32)">
      <summary>Decrements the iterator by the given number of elements. The integer parameter is specified on the right side of the addition operator.</summary>
      <returns>A decremented iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="_Right">The number of elements to decrement.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.op_Subtraction(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines the difference between two iterators.</summary>
      <returns>The difference, in number of elements, between the two iterators.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" /> to be subtracted from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.prev">
      <summary>Positions the iterator to the element immediately before the current element.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstContainerRandomAccessIterator`1.valid">
      <summary>Determines whether the iterator is valid and can be safely used to traverse the underlying container.</summary>
      <returns>true if the iterator is valid and safe to use; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1">
      <summary>Defines an iterator that accesses elements in a container in the reverse direction. Elements can be accessed in the forward direction by using the decrement operator and in the backward direction by using the increment operator. The element that the iterator points to cannot be modified. Constant reverse bidirectional iterators can be used anywhere that a constant reverse input or constant reverse output iterator is required.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.#ctor">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator{`0})">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator`1" /> object.</summary>
      <param name="_Iter">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator`1" /> object to copy.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</summary>
      <param name="_Right">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object to copy.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</summary>
      <param name="_Iter">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object to copy.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.base">
      <summary>Gets the underlying base iterator for the <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</summary>
      <returns>The underlying base iterator for the <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.Clone">
      <summary>Returns a copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</summary>
      <returns>A copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.container">
      <summary>Gets the container that the iterator is traversing.</summary>
      <returns>The container that the iterator is traversing.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> objects are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.get_bias">
      <summary>Gets the bias of the iterator. The bias is the offset of the iterator from element zero.</summary>
      <returns>The bias of the iterator.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.get_cref">
      <summary>Returns a constant reference to the element that the iterator is currently pointing to.</summary>
      <returns>A constant reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.get_node">
      <summary>Gets the node, or element, that the iterator is pointing to.</summary>
      <returns>The node that the iterator is pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.get_ref">
      <summary>Returns a reference to the element that the iterator is currently pointing to.</summary>
      <returns>A reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.next">
      <summary>Decrements the iterator to the previous position in the underlying container, or to the first position in front of the beginning of container if the container has been completely traversed.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.op_Assign(Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Assigns the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object to the current object.</summary>
      <returns>The current object (*this).</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object to assign to the current object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.op_Assign(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Assigns the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object to the current object.</summary>
      <returns>The current object (*this).</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object to assign to the current object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator{`0}@)">
      <summary>Decrements the iterator by one element. Decrementing a reverse iterator is the same as incrementing a regular iterator. This is the prefix version of the decrement operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator{`0}@,System.Int32)">
      <summary>Decrements the iterator by one element. Decrementing a reverse iterator is the same as incrementing a regular iterator. This is the postfix version of the decrement operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">An unused parameter that indicates this is the postfix version of the operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.op_Equality(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.op_Equality(Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.op_Implicit~Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator{`0}">
      <summary>Converts a <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> to an <see cref="T:Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator`1" />.</summary>
      <returns>The current iterator as an <see cref="T:Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator`1" />.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator{`0}@)">
      <summary>Increments the iterator by one element. Incrementing a reverse iterator is the same as decrementing a regular iterator. This is the prefix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator{`0}@,System.Int32)">
      <summary>Increments the iterator by one element. Incrementing a reverse iterator is the same as decrementing a regular iterator. This is the postfix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">Indicates that this is the postfix version of the increment operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.op_Inequality(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object differs from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are different objects; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.op_Inequality(Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object differs from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are different objects; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.op_MemberSelection(Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.op_PointerDereference(Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.prev">
      <summary>Increments the iterator to the next position in the underlying container, or to the first position beyond the end of container if the container has been completely traversed.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseBidirectionalIterator`1.valid">
      <summary>Determines whether the iterator is valid and can be safely used to traverse the underlying container.</summary>
      <returns>true if the iterator is valid and safe to use; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1">
      <summary>Provides an iterator that supports the following operations: moving forward one position by calling operator--, moving backward one position by calling operator++, accessing an element by using operator[], and accessing an element by using pointer arithmetic. The element accessed by the iterator cannot be modified.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.#ctor">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" /> object.</summary>
      <param name="_Iter">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" /> object to be copied to the current container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object.</summary>
      <param name="_Right">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object to be copied to the current container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</summary>
      <param name="_Iter">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object to be copied to the current container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.base">
      <summary>Gets the underlying base iterator for the <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object.</summary>
      <returns>The underlying base iterator for the <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.Clone">
      <summary>Creates a copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object.</summary>
      <returns>A copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.container">
      <summary>Gets the container that the iterator is traversing.</summary>
      <returns>The container that the iterator is traversing.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.distance(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines the distance between the element that the current iterator is pointing to and the element that the given iterator is pointing to.</summary>
      <returns>The distance, in number of elements, between the element that the current iterator is pointing to and the element that <paramref name="_Right" /> is pointing to. This number is negative if <paramref name="_Right" /> points to an element that is before the element that the current iterator points to.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.distance(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines the distance between the element that the current iterator is pointing to and the element that the given iterator is pointing to.</summary>
      <returns>The distance, in number of elements, between the element that the current iterator is pointing to and the element that <paramref name="_Right" /> is pointing to. This number is negative if <paramref name="_Right" /> points to an element that is before the element that the current iterator points to.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> objects are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.get_bias">
      <summary>Gets the bias of the iterator. The bias is the offset of the iterator from element zero.</summary>
      <returns>The bias of the iterator.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.get_cref">
      <summary>Returns a constant reference to the element that the iterator is currently pointing to.</summary>
      <returns>A constant reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.get_node">
      <summary>Gets the node, or element, that the iterator is pointing to.</summary>
      <returns>The node that the iterator is pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.get_ref">
      <summary>Returns a reference to the element that the iterator is currently pointing to.</summary>
      <returns>A reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.Item(System.Int32)">
      <summary>Accesses the element at the given position in the container.</summary>
      <returns>The element at the given position in the container.</returns>
      <param name="_Pos">The position of the element to access.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.less_than(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines whether the current iterator is pointing to an element in the container that is before the element that the given iterator points to.</summary>
      <returns>true if the current iterator is pointing to an element that comes before the element that <paramref name="_Right" /> is pointing to; otherwise, false.</returns>
      <param name="_Right">A valid iterator pointing to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.less_than(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the current iterator is pointing to an element in the container that is before the element that the given iterator points to.</summary>
      <returns>true if the current iterator is pointing to an element that comes before the element that <paramref name="_Right" /> is pointing to; otherwise, false.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.move(System.Int32)">
      <summary>Moves the iterator by the given number of elements.</summary>
      <returns>The actual number of elements traversed. This number will differ from <paramref name="_Offset" /> if the beginning or end of the container is reached before iterating the desired number of elements.</returns>
      <param name="_Offset">The number of elements to traverse. To move the iterator forward, specify a negative number. To move the iterator backward, use a positive number.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.next">
      <summary>Increments the reverse iterator to the next position in the underlying container, or to the first position in front of the beginning of container if the container has been completely traversed.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_Addition(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}@,System.Int32)">
      <summary>Increments the reverse iterator by the given number of elements. The integer parameter is specified on the right side of the addition operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="_Right">The number of elements to increment.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_Addition(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}@,System.Int32,Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Increments the reverse iterator by the given number of elements. The integer parameter is specified on the left side of the addition operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="_Left">The number of elements to increment.</param>
      <param name="_Right">The iterator to increment.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_Assign(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Assigns the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object to the current object.</summary>
      <returns>The current object (*this).</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object to assign to the current object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_Assign(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Assigns the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object to the current object.</summary>
      <returns>The current object (*this).</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object to assign to the current object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}@)">
      <summary>Decrements the reverse iterator by one element. This is the prefix version of the decrement operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}@,System.Int32)">
      <summary>Decrements the reverse iterator by one element. This is the postfix version of the decrement operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">An unused parameter that indicates this is the postfix version of the operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_Equality(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether two iterators are the same object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_GreaterThan(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is greater than the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is greater than the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_GreaterThanOrEqual(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is greater than or equal to the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is greater than or equal to the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_Implicit~Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0}">
      <summary>Converts a <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> to an <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" />.</summary>
      <returns>The current iterator as an <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" />.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}@)">
      <summary>Increments the reverse iterator by one element. This is the prefix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}@,System.Int32)">
      <summary>Increments the reverse iterator by one element. This is the postfix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">Indicates that this is the postfix version of the increment operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_Inequality(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object differs from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two iterators are different objects; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_LessThan(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is less than the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is less than the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_LessThanOrEqual(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is less than or equal to the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is less than or equal to the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_MemberSelection(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_PointerDereference(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_Subtraction(Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator{`0}@,System.Int32)">
      <summary>Decrements the iterator by the given number of elements. The integer parameter is specified on the right side of the subtraction operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="_Right">The number of elements to decrement.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.op_Subtraction(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines the difference between two iterators.</summary>
      <returns>The difference (number of elements) between the two iterators.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" /> to be subtracted from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.prev">
      <summary>Positions the iterator to the element immediately after the current element.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ConstReverseRandomAccessIterator`1.valid">
      <summary>Determines whether the iterator is valid and can be safely used to traverse the underlying container.</summary>
      <returns>true if the iterator is valid and safe to use; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1">
      <summary>Defines an iterator that can access elements in a container in the forward direction by using the increment operator and in the backward direction by using the decrement operator. The element that the iterator points to can be both written to and read from any number of times. Bidirectional iterators can be used anywhere that an input or output iterator is required.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.#ctor">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.INode{`0})">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object that points to the given node.</summary>
      <param name="_Node">The node that the iterator will point to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</summary>
      <param name="_Right">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object to copy.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.Clone">
      <summary>Returns a copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</summary>
      <returns>A copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.container">
      <summary>Gets the container that the iterator is traversing.</summary>
      <returns>The container that the iterator is traversing.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines if the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> objects are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.get_bias">
      <summary>Gets the bias of the iterator. The bias is the offset of the iterator from element zero.</summary>
      <returns>The bias of the iterator.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.get_cref">
      <summary>Returns a constant reference to the element that the iterator is currently pointing to.</summary>
      <returns>A constant reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.get_node">
      <summary>Gets the node, or element, that the iterator is pointing to.</summary>
      <returns>The node that the iterator is pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.get_ref">
      <summary>Returns a reference to the element that the iterator is currently pointing to.</summary>
      <returns>A reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.next">
      <summary>Increments the iterator to the next position in the underlying container, or to the first position beyond the end of container if the container has been completely traversed.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.op_Assign(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Assigns the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object to the current object.</summary>
      <returns>The current object (*this).</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object to assign to the current object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}@)">
      <summary>Decrements the iterator by one element. This is the prefix version of the decrement operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}@,System.Int32)">
      <summary>Decrements the iterator by one element. This is the postfix version of the decrement operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">An unused parameter that indicates this is the postfix version of the operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.op_Equality(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.op_Equality(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.op_Implicit~Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator{`0}">
      <summary>Converts a <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> to an <see cref="T:Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator`1" />.</summary>
      <returns>The current iterator as an <see cref="T:Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator`1" />.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}@)">
      <summary>Increments the iterator by one element. This is the prefix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}@,System.Int32)">
      <summary>Increments the iterator by one element. This is the postfix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">An unused parameter that indicates this is the postfix version of the operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.op_Inequality(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object differs from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are different objects; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.op_Inequality(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object differs from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are different objects; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.op_MemberSelection(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.op_PointerDereference(Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.prev">
      <summary>Positions the iterator to the element immediately before the current element.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerBidirectionalIterator`1.valid">
      <summary>Determines whether the iterator is valid and can be safely used to traverse the underlying container.</summary>
      <returns>true if the iterator is valid and safe to use; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1">
      <summary>Provides an iterator that supports the following operations: moving forward one position by calling operator++, moving backward one position by calling operator--, accessing an element by using operator[], and accessing an element by using pointer arithmetic.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.#ctor">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.IRandomAccessContainer{`0},System.Int32)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessContainer`1" /> object.</summary>
      <param name="_Cont">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessContainer`1" /> object to be copied to the current container.</param>
      <param name="_Offset">The offset of the element that the iterator will point to after creation.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</summary>
      <param name="_Right">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object to be copied to the current container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.Clone">
      <summary>Creates a copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</summary>
      <returns>A copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.container">
      <summary>Gets the container that the iterator is traversing.</summary>
      <returns>The container that the iterator is traversing.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.distance(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines the distance between the element that the current iterator is pointing to and the element that the given iterator is pointing to.</summary>
      <returns>The distance, in number of elements, between the element that the current iterator is pointing to and the element that <paramref name="_Right" /> is pointing to. This number will be negative if <paramref name="_Right" /> points to an element that is before the element that the current iterator points to.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.distance(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines the distance between the element that the current iterator is pointing to and the element that the given iterator is pointing to.</summary>
      <returns>The distance, in number of elements, between the element that the current iterator is pointing to and the element that <paramref name="_Right" /> is pointing to. This number will be negative if <paramref name="_Right" /> points to an element that is before the element that the current iterator points to.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> objects are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.get_bias">
      <summary>Gets the bias of the iterator. The bias is the offset of the iterator from element zero.</summary>
      <returns>The bias of the iterator.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.get_cref">
      <summary>Returns a constant reference to the element that the iterator is currently pointing to.</summary>
      <returns>A constant reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.get_node">
      <summary>Gets the node, or element, that the iterator is pointing to.</summary>
      <returns>The node that the iterator is pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.get_ref">
      <summary>Returns a reference to the element that the iterator is currently pointing to.</summary>
      <returns>A reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.Item(System.Int32)">
      <summary>Accesses the element at the given position in the container.</summary>
      <returns>The element at the given position in the container.</returns>
      <param name="_Pos">The position of the element to access.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.less_than(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines whether the current iterator is pointing to an element in the container that is before the element that the given iterator points to.</summary>
      <returns>true if the current iterator is pointing to an element that comes before the element that <paramref name="_Right" /> is pointing to; otherwise, false.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.less_than(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the current iterator is pointing to an element in the container that is before the element that the given iterator points to.</summary>
      <returns>true if the current iterator is pointing to an element that comes before the element that <paramref name="_Right" /> is pointing to; otherwise, false.</returns>
      <param name="_Right">A valid iterator pointing to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.move(System.Int32)">
      <summary>Moves the iterator by the given number of elements.</summary>
      <returns>The actual number of elements traversed. This number will differ from <paramref name="_Offset" /> if the beginning or end of the container is reached before iterating the desired number of elements.</returns>
      <param name="_Offset">The number of elements to traverse. To move the iterator forward, specify a positive number. To move the iterator backward, use a negative number.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.next">
      <summary>Increments the iterator to the next position in the underlying container, or to the first position beyond the end of container if the container has been completely traversed.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_Addition(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@,System.Int32)">
      <summary>Increments the iterator by the given number of elements. The integer parameter is specified on the right side of the addition operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="_Right">The number of elements to increment.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_Addition(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@,System.Int32,Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Increments the iterator by the given number of elements. The integer parameter is specified on the left side of the addition operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="_Left">The number of elements to increment.</param>
      <param name="_Right">The iterator to increment.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_Assign(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Assigns the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object to the current object.</summary>
      <returns>The current object (*this).</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object to assign to the current object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@)">
      <summary>Decrements the iterator by one element. This is the prefix version of the decrement operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@,System.Int32)">
      <summary>Decrements the iterator by one element. This is the postfix version of the decrement operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">An unused parameter that indicates this is the postfix version of the operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_Equality(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_Equality(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_GreaterThan(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0},Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the given left-side iterator is greater than the position of the given right-side iterator.</summary>
      <returns>true if the given left-side iterator is greater than the given right-side iterator; otherwise, false.</returns>
      <param name="_Left">The iterator to compare with the right-side iterator.</param>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_GreaterThan(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is greater than the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is greater than the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_GreaterThanOrEqual(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines whether the position of the left-side iterator is greater than or equal to the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is greater than or equal to the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_GreaterThanOrEqual(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is greater than or equal to the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is greater than or equal to the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_Implicit~Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0}">
      <summary>Converts a <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> to an <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" />.</summary>
      <returns>The current iterator as an <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" />.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@)">
      <summary>Increments the iterator by one element. This is the prefix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@,System.Int32)">
      <summary>Increments the iterator by one element. This is the postfix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">An unused parameter that indicates this is the postfix version of the operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_Inequality(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object differs from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two iterators are different objects; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_Inequality(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object differs from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two iterators are different objects; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_LessThan(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines whether the position of the left-side iterator is less than the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is less than the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_LessThan(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is less than the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is less than the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_LessThanOrEqual(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0},Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the given left-side iterator is less than the position of the given right-side iterator.</summary>
      <returns>true if the given left-side iterator is less than or equal to the given right-side iterator; otherwise, false.</returns>
      <param name="_Left">The iterator to compare with the right-side iterator.</param>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_LessThanOrEqual(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is less than or equal to the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is less than or equal to the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_MemberSelection(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_PointerDereference(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_Subtraction(Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator{`0}@,System.Int32)">
      <summary>Decrements the iterator by the given number of elements. The integer parameter is specified on the right side of the subtraction operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="_Right">The number of elements to decrement.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.op_Subtraction(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines the difference between two iterators.</summary>
      <returns>The difference (number of elements) between the two iterators.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" /> to be subtracted from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.prev">
      <summary>Positions the iterator to the element immediately before the current element.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ContainerRandomAccessIterator`1.valid">
      <summary>Determines whether the iterator is valid and can be safely used to traverse the underlying container.</summary>
      <returns>true if the iterator is valid and safe to use; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.IBaseIterator`1">
      <summary>Defines the base interface for all STL/CLR iterators.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IBaseIterator`1.container">
      <summary>Gets the container that the iterator is traversing.</summary>
      <returns>The container that the iterator is traversing.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IBaseIterator`1.get_bias">
      <summary>Gets the bias of the iterator. The bias is the offset of the iterator from element zero.</summary>
      <returns>The bias of the iterator.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IBaseIterator`1.get_node">
      <summary>Gets the node, or element, that the iterator is pointing to.</summary>
      <returns>The node that the iterator is currently pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IBaseIterator`1.next">
      <summary>Increments the iterator to the next position in the underlying container, or to the first position beyond the end of container if the container has been completely traversed.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IBaseIterator`1.valid">
      <summary>Determines whether the iterator is valid and can be safely used to traverse the underlying container.</summary>
      <returns>true if the iterator is valid and safe to use; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.IBidirectionalContainer`1">
      <summary>Interface for a container that supports both forward and backward iteration. Containers that implement bidirectional iteration support the following operations: moving forward one position by calling operator++, and moving backward one position by calling operator--.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IBidirectionalContainer`1.get_generation">
      <summary>Gets the current change generation of the underlying container.</summary>
      <returns>The current change generation of the underlying container.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator`1">
      <summary>Interface for an iterator that can access elements in a container in the forward direction by using the increment operator and in the backward direction by using the decrement operator. The element that the iterator points to can be both written to and read from any number of times. Bidirectional iterators can be used anywhere an input or output iterator is required.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator`1.prev">
      <summary>Positions the iterator to the element immediately before the current element.</summary>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.IForwardIterator`1">
      <summary>Interface for an iterator that can access elements in a container only in the forward direction by using the increment operator. The element that the iterator points to can be both written to and read from any number of times. Forward iterators can be used anywhere an input or output iterator is required.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1">
      <summary>Interface for an iterator that is used to read a sequence from a container only in the forward direction. The element that the iterator points to can be read only one time, and it cannot be modified.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IInputIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether two <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> objects are equal.</summary>
      <returns>true if the current iterator and the specified iterator are equal; otherwise, false.</returns>
      <param name="param0">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IInputIterator`1.get_cref">
      <summary>Returns a constant reference to the element that the iterator is currently pointing to.</summary>
      <returns>A constant reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.INode`1">
      <summary>Interface for the node data structure. Containers that support bidirectional iteration contain nodes. A node consists of a value for the element at that position in the container and pointers to the next and previous elements in the container.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.Generic.INode`1._Value">
      <summary>Gets or sets the value of the element for this node in the container.</summary>
      <returns>The value of the element for this node in the container.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.INode`1.container">
      <summary>Gets the container that the current node is in.</summary>
      <returns>The container that the current node is in.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.INode`1.is_head">
      <summary>Determines whether the current node is the first node of the container.</summary>
      <returns>true if the current node is the first node in the container; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.INode`1.next_node">
      <summary>Gets the next node in the container past the current node.</summary>
      <returns>The next node in the container past the current node.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.INode`1.prev_node">
      <summary>Gets the node in the container immediately before the current node.</summary>
      <returns>The node in the container immediately before the current node.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.IOutputIterator`1">
      <summary>Interface for an iterator that is used to write a sequence to a container only in the forward direction. The element that the iterator points to can be written to only one time.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IOutputIterator`1.get_ref">
      <summary>Returns a reference to the element that the iterator is currently pointing to.</summary>
      <returns>A reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessContainer`1">
      <summary>Interface for a container that supports random access iteration. Containers that implement random access iteration support the following operations: moving forward one position by calling operator++, moving backward one position by calling operator--, accessing an element by using operator[], and accessing an element by using pointer arithmetic.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IRandomAccessContainer`1.at_bias(System.Int32)">
      <summary>Gets the element at the current bias of the container. The bias is the offset from the current element zero.</summary>
      <returns>The element at the given bias.</returns>
      <param name="param0">The current bias.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IRandomAccessContainer`1.valid_bias(System.Int32)">
      <summary>Determines if a given bias is valid for the container. The bias is the offset from the current element zero.</summary>
      <returns>true if the given bias is valid for the container; otherwise, false.</returns>
      <param name="param0">The current bias.</param>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1">
      <summary>Defines the interface for a random access iterator. Random access iterators support the following operations: moving forward one position by calling operator++, moving backward one position by calling operator--, accessing an element by using operator[], and accessing an element by using pointer arithmetic.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1.distance(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines the distance between the element that the current iterator is pointing to and the element that the given iterator is pointing to.</summary>
      <returns>The distance, in number of elements, between the element that the current iterator is pointing to and the element that <paramref name="_Right" /> is pointing to. This number is negative if <paramref name="_Right" /> points to an element that is before the element that the current iterator points to.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1.less_than(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines whether the current iterator is pointing to an element in the container that precedes the element that the given iterator points to.</summary>
      <returns>true if the current iterator is pointing to an element that comes before the element that <paramref name="_Right" /> is pointing to; otherwise, false.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1.move(System.Int32)">
      <summary>Moves the iterator by the given number of elements.</summary>
      <returns>The actual number of elements traversed. This number will differ from <paramref name="_Offset" /> if the beginning or end of the container is reached before iterating the desired number of elements.</returns>
      <param name="_Offset">The number of elements to traverse. To move the iterator forward, specify a positive number. To move the iterator backward, use a negative number.</param>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1">
      <summary>Defines an iterator that accesses elements in a container in the reverse direction. Elements can be accessed in the forward direction by using the decrement operator and in the backward direction by using the increment operator. The element that the iterator points to can be both written to and read from any number of times. Reverse bidirectional iterators can be used anywhere that a reverse input or reverse output iterator is required.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.#ctor">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator{`0})">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator`1" /> object.</summary>
      <param name="_Iter">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator`1" /> object to copy.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</summary>
      <param name="_Right">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object to copy.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.base">
      <summary>Gets the underlying base iterator for the <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</summary>
      <returns>The underlying base iterator for the <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.Clone">
      <summary>Returns a copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</summary>
      <returns>A copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.container">
      <summary>Gets the container that the iterator is traversing.</summary>
      <returns>The container that the iterator is traversing.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> objects are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.get_bias">
      <summary>Gets the bias of the iterator. The bias is the offset of the iterator from element zero.</summary>
      <returns>The bias of the iterator.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.get_cref">
      <summary>Returns a constant reference to the element that the iterator is currently pointing to.</summary>
      <returns>A constant reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.get_node">
      <summary>Gets the node, or element, that the iterator is pointing to.</summary>
      <returns>The node that the iterator is pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.get_ref">
      <summary>Returns a reference to the element that the iterator is currently pointing to.</summary>
      <returns>A reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.next">
      <summary>Decrements the iterator to the previous position in the underlying container, or to the first position in front of the beginning of container if the container has been completely traversed.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.op_Assign(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Assigns the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object to the current object.</summary>
      <returns>The current object (*this).</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object to assign to the current object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}@)">
      <summary>Decrements the iterator by one element. Decrementing a reverse iterator is the same as incrementing a regular iterator. This is the prefix version of the decrement operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}@,System.Int32)">
      <summary>Decrements the iterator by one element. Decrementing a reverse iterator is the same as incrementing a regular iterator. This is the postfix version of the decrement operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">An unused parameter that indicates this is the postfix version of the operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.op_Equality(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.op_Equality(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.op_Implicit~Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator{`0}">
      <summary>Converts a <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> to an <see cref="T:Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator`1" />.</summary>
      <returns>The current iterator as an <see cref="T:Microsoft.VisualC.StlClr.Generic.IBidirectionalIterator`1" />.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}@)">
      <summary>Increments the iterator by one element. Incrementing a reverse iterator is the same as decrementing a regular iterator. This is the prefix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}@,System.Int32)">
      <summary>Increments the iterator by one element. Incrementing a reverse iterator is the same as decrementing a regular iterator. This is the postfix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">An unused parameter that indicates this is the postfix version of the operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.op_Inequality(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object differs from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are different objects; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.op_Inequality(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object differs from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</summary>
      <returns>true if the two iterators are different objects; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.op_MemberSelection(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.op_PointerDereference(Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.prev">
      <summary>Increments the iterator to the next position in the underlying container, or to the first position beyond the end of container if the container has been completely traversed.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseBidirectionalIterator`1.valid">
      <summary>Determines whether the iterator is valid and can be safely used to traverse the underlying container.</summary>
      <returns>true if the iterator is valid and safe to use; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1">
      <summary>Provides an iterator that supports the following operations: moving forward one position by calling operator--, moving backward one position by calling operator++, accessing an element by using operator[], and accessing an element by using pointer arithmetic.</summary>
      <typeparam name="TValue">The type of an element in the controlled sequence.</typeparam>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.#ctor">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" /> object.</summary>
      <param name="_Iter">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" /> object to be copied to the current container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.#ctor(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Allocates and initializes a new <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object from an existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</summary>
      <param name="_Right">An existing <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object to be copied to the current container.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.base">
      <summary>Gets the underlying base iterator for the <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</summary>
      <returns>The underlying base iterator for the <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.Clone">
      <summary>Creates a copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</summary>
      <returns>A copy of the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.container">
      <summary>Gets the container that the iterator is traversing.</summary>
      <returns>The container that the iterator is traversing.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.distance(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines the distance between the element that the current iterator is pointing to and the element that the given iterator is pointing to.</summary>
      <returns>The distance, in number of elements, between the element that the current iterator is pointing to and the element that <paramref name="_Right" /> is pointing to. This number is negative if <paramref name="_Right" /> points to an element that is before the element that the current iterator points to.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.distance(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines the distance between the element that the current iterator is pointing to and the element that the given iterator is pointing to.</summary>
      <returns>The distance, in number of elements, between the element that the current iterator is pointing to and the element that <paramref name="_Right" /> is pointing to. This number is negative if <paramref name="_Right" /> points to an element that is before the element that the current iterator points to.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.IInputIterator{`0})">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IInputIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.equal_to(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object is the same as the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> objects are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.get_bias">
      <summary>Gets the bias of the iterator. The bias is the offset of the iterator from element zero.</summary>
      <returns>The bias of the iterator.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.get_cref">
      <summary>Returns a constant reference to the element that the iterator is currently pointing to.</summary>
      <returns>A constant reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.get_node">
      <summary>Gets the node, or element, that the iterator is pointing to.</summary>
      <returns>The node that the iterator is pointing to.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.get_ref">
      <summary>Returns a reference to the element that the iterator is currently pointing to.</summary>
      <returns>A reference to the element that the iterator is currently pointing to.</returns>
    </member>
    <member name="P:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.Item(System.Int32)">
      <summary>Accesses the element at the given position in the container.</summary>
      <returns>The element at the given position in the container.</returns>
      <param name="_Pos">The position of the element to access.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.less_than(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines whether the current iterator is pointing to an element in the container that is before the element that the given iterator points to.</summary>
      <returns>true if the current iterator is pointing to an element that comes before the element that <paramref name="_Right" /> is pointing to; otherwise, false.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.less_than(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the current iterator is pointing to an element in the container that is before the element that the given iterator points to.</summary>
      <returns>true if the current iterator is pointing to an element that comes before the element that <paramref name="_Right" /> is pointing to; otherwise, false.</returns>
      <param name="_Right">A valid iterator that points to the same container as the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.move(System.Int32)">
      <summary>Moves the iterator by the given number of elements.</summary>
      <returns>The actual number of elements traversed. This number will differ from <paramref name="_Offset" /> if the beginning or end of the container is reached before iterating the desired number of elements.</returns>
      <param name="_Offset">The number of elements to traverse. To move the iterator forward, specify a negative number. To move the iterator backward, use a positive number.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.next">
      <summary>Increments the reverse iterator to the next position in the underlying container, or to the first position in front of the beginning of container if the container has been completely traversed.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_Addition(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}@,System.Int32)">
      <summary>Increments the reverse iterator by the given number of elements. The integer parameter is specified on the right side of the addition operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="_Right">The number of elements to increment.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_Addition(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}@,System.Int32,Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Increments the reverse iterator by the given number of elements. The integer parameter is specified on the left side of the addition operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="_Left">The number of elements to increment.</param>
      <param name="_Right">The iterator to increment.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_Assign(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Assigns the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object to the current object.</summary>
      <returns>The current object (*this).</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object to assign to the current object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}@)">
      <summary>Decrements the reverse iterator by one element. This is the prefix version of the decrement operator.</summary>
      <returns>An iterator. </returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_Decrement(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}@,System.Int32)">
      <summary>Decrements the reverse iterator by one element. This is the postfix version of the decrement operator.</summary>
      <returns>An iterator. </returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">An unused parameter that indicates this is the postfix version of the operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_Equality(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether two iterators are the same object.</summary>
      <returns>true if the two iterators are the same object; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_GreaterThan(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is greater than the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is greater than the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_GreaterThanOrEqual(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is greater than or equal to the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is greater than or equal to the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_Implicit~Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0}">
      <summary>Converts a <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> to an <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" />.</summary>
      <returns>The current iterator as an <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" />.</returns>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}@)">
      <summary>Increments the reverse iterator by one element. This is the prefix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_Increment(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}@,System.Int32)">
      <summary>Increments the reverse iterator by one element. This is the postfix version of the increment operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="param1">An unused parameter that indicates this is the postfix version of the operator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_Inequality(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the given <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object differs from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</summary>
      <returns>true if the two iterators are different objects; otherwise, false.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object to compare with the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_LessThan(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is less than the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is less than the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_LessThanOrEqual(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Determines whether the position of the left-side iterator is less than or equal to the position of the given right-side iterator.</summary>
      <returns>true if the left-side iterator is less than or equal to the given right-side iterator; otherwise, false.</returns>
      <param name="_Right">The iterator to compare with the left-side iterator.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_MemberSelection(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_PointerDereference(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}|System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
      <summary>Returns the element that the iterator is currently pointing to.</summary>
      <returns>The element that the iterator is currently pointing to.</returns>
      <param name="_Left">The iterator that the operator is applied to.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_Subtraction(Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator{`0})">
      <summary>Determines the difference between two iterators.</summary>
      <returns>The difference, in number of elements, between the two iterators.</returns>
      <param name="_Right">The <see cref="T:Microsoft.VisualC.StlClr.Generic.IRandomAccessIterator`1" /> to be subtracted from the current <see cref="T:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1" /> object.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.op_Subtraction(Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator{`0}@,System.Int32)">
      <summary>Decrements the iterator by the given number of elements. The integer parameter is specified on the right side of the subtraction operator.</summary>
      <returns>An iterator.</returns>
      <param name="__unnamed0">A reference to the current iterator.</param>
      <param name="_Right">The number of elements to decrement.</param>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.prev">
      <summary>Positions the iterator to the element immediately after the current element.</summary>
    </member>
    <member name="M:Microsoft.VisualC.StlClr.Generic.ReverseRandomAccessIterator`1.valid">
      <summary>Determines whether the iterator is valid and can be safely used to traverse the underlying container.</summary>
      <returns>true if the iterator is valid and safe to use; otherwise, false.</returns>
    </member>
  </members>
</doc>