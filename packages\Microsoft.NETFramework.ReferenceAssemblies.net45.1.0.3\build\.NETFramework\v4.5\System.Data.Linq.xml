﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Data.Linq</name>
  </assembly>
  <members>
    <member name="T:System.Data.Linq.Binary">
      <summary>Represents an immutable block of binary data.</summary>
    </member>
    <member name="M:System.Data.Linq.Binary.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Binary" /> class.</summary>
      <param name="value">The bytes representing the binary data.</param>
    </member>
    <member name="M:System.Data.Linq.Binary.Equals(System.Data.Linq.Binary)">
      <summary>Determines whether two binary objects are equal.</summary>
      <returns>true if the two binary objects are equal; otherwise, false.</returns>
      <param name="other">The <see cref="T:System.Object" /> to which the current object is being compared.</param>
    </member>
    <member name="M:System.Data.Linq.Binary.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Object" />.</summary>
      <returns>true if the two binary objects are equal; otherwise, false.</returns>
      <param name="obj">The <see cref="T:System.Object" /> to compare with the current <see cref="T:System.Object" />.</param>
    </member>
    <member name="M:System.Data.Linq.Binary.GetHashCode">
      <summary>Serves as a hash function for a particular type.</summary>
      <returns>A hash code for the current binary object.</returns>
    </member>
    <member name="P:System.Data.Linq.Binary.Length">
      <summary>Gets the length of the binary object.</summary>
      <returns>An integer representing the length.</returns>
    </member>
    <member name="M:System.Data.Linq.Binary.op_Equality(System.Data.Linq.Binary,System.Data.Linq.Binary)">
      <summary>Describes the equality relationship between two binary objects.</summary>
      <returns>true if the binary objects are equal; otherwise false.</returns>
      <param name="binary1">First binary object.</param>
      <param name="binary2">Second binary object.</param>
    </member>
    <member name="M:System.Data.Linq.Binary.op_Implicit(System.Byte[])~System.Data.Linq.Binary">
      <summary>Enables arrays of bytes to be implicitly coerced to the <see cref="T:System.Data.Linq.Binary" /> type in a programming language.</summary>
      <returns>A <see cref="T:System.Data.Linq.Binary" /> class containing the coerced value.</returns>
      <param name="value">The array of bytes to convert into an instance of the <see cref="T:System.Data.Linq.Binary" /> type.</param>
    </member>
    <member name="M:System.Data.Linq.Binary.op_Inequality(System.Data.Linq.Binary,System.Data.Linq.Binary)">
      <summary>Describes the inequality relationship between two binary objects.</summary>
      <returns>true if the binary objects are not equal; otherwise false.</returns>
      <param name="binary1">The first binary object.</param>
      <param name="binary2">The second binary object.</param>
    </member>
    <member name="M:System.Data.Linq.Binary.ToArray">
      <summary>Returns an array of bytes that represents the current binary object.</summary>
      <returns>A byte array that contains the value of the current binary object.</returns>
    </member>
    <member name="M:System.Data.Linq.Binary.ToString">
      <summary>Returns a <see cref="T:System.String" /> that represents the current binary object.</summary>
      <returns>A <see cref="T:System.String" /> that represents the current binary object.</returns>
    </member>
    <member name="T:System.Data.Linq.ChangeAction">
      <summary>Describes the type of change the entity will undergo when changes are submitted to the database.</summary>
    </member>
    <member name="F:System.Data.Linq.ChangeAction.None">
      <summary>The entity will not be submitted.</summary>
    </member>
    <member name="F:System.Data.Linq.ChangeAction.Delete">
      <summary>The entity will be deleted.</summary>
    </member>
    <member name="F:System.Data.Linq.ChangeAction.Insert">
      <summary>The entity will be inserted.</summary>
    </member>
    <member name="F:System.Data.Linq.ChangeAction.Update">
      <summary>The entity will be updated.</summary>
    </member>
    <member name="T:System.Data.Linq.ChangeConflictCollection">
      <summary>Returns a collection of objects involved in concurrency conflicts.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.ChangeConflictCollection.Clear">
      <summary>Removes all conflicts from the collection.</summary>
    </member>
    <member name="M:System.Data.Linq.ChangeConflictCollection.Contains(System.Data.Linq.ObjectChangeConflict)">
      <summary>Specifies whether a given conflict is a member of the collection.</summary>
      <returns>Returns true if the specified conflict is a member of the collection.</returns>
      <param name="item">The specified conflict.</param>
    </member>
    <member name="M:System.Data.Linq.ChangeConflictCollection.CopyTo(System.Data.Linq.ObjectChangeConflict[],System.Int32)">
      <summary>For a description of this member, see <see cref="M:System.Collections.ICollection.CopyTo(System.Array,System.Int32)" />.</summary>
      <param name="array">The array to copy to.</param>
      <param name="arrayIndex">The array index where the copy is to start.</param>
    </member>
    <member name="P:System.Data.Linq.ChangeConflictCollection.Count">
      <summary>Returns the number of conflicts in the collection</summary>
      <returns>integer</returns>
    </member>
    <member name="M:System.Data.Linq.ChangeConflictCollection.GetEnumerator">
      <summary>Returns the enumerator for the collection.</summary>
      <returns>An enumerator for the collection.</returns>
    </member>
    <member name="P:System.Data.Linq.ChangeConflictCollection.Item(System.Int32)">
      <summary>Returns an item in conflict.</summary>
      <returns>An <see cref="T:System.Data.Linq.ObjectChangeConflict" /> representing the item in conflict.</returns>
      <param name="index">Index in the collection of the item in conflict.</param>
    </member>
    <member name="M:System.Data.Linq.ChangeConflictCollection.Remove(System.Data.Linq.ObjectChangeConflict)">
      <summary>Specifies whether the specified conflict is removed from the collection.</summary>
      <returns>Returns true if the <see cref="T:System.Data.Linq.ObjectChangeConflict" /> is removed from the collection.</returns>
      <param name="item">The conflict to remove.</param>
    </member>
    <member name="M:System.Data.Linq.ChangeConflictCollection.ResolveAll(System.Data.Linq.RefreshMode)">
      <summary>Resolves all conflicts in the collection by using the specified strategy.</summary>
      <param name="mode">One of the options available in <see cref="T:System.Data.Linq.RefreshMode" />.</param>
    </member>
    <member name="M:System.Data.Linq.ChangeConflictCollection.ResolveAll(System.Data.Linq.RefreshMode,System.Boolean)">
      <summary>Resolves all conflicts in the collection by using the specified strategy.</summary>
      <param name="mode">The strategy to use to resolve the conflict.</param>
      <param name="autoResolveDeletes">If true, automatically resolves conflicts that result from a modified object that is no longer in the database.</param>
    </member>
    <member name="M:System.Data.Linq.ChangeConflictCollection.System#Collections#Generic#ICollection{T}#Add(System.Data.Linq.ObjectChangeConflict)">
      <summary>For a description of this member, see <see cref="M:System.Collections.Generic.ICollection`1.Add(`0)" />.</summary>
      <param name="item">The item to add.</param>
    </member>
    <member name="P:System.Data.Linq.ChangeConflictCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>For a description of this member, see <see cref="P:System.Collections.Generic.ICollection`1.IsReadOnly" />.</summary>
      <returns>Returns true if the collection is read-only.</returns>
    </member>
    <member name="M:System.Data.Linq.ChangeConflictCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the collection to an array.</summary>
      <param name="array">Name of the array.</param>
      <param name="index">Index of the array.</param>
    </member>
    <member name="P:System.Data.Linq.ChangeConflictCollection.System#Collections#ICollection#IsSynchronized">
      <summary>For a description of this member, see <see cref="P:System.Collections.ICollection.IsSynchronized" />.</summary>
      <returns>Returns true if access to the collection is synchronized (thread safe).</returns>
    </member>
    <member name="P:System.Data.Linq.ChangeConflictCollection.System#Collections#ICollection#SyncRoot">
      <summary>For a description of this member, see <see cref="P:System.Collections.ICollection.IsSynchronized" />.</summary>
      <returns>Returns an object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:System.Data.Linq.ChangeConflictCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>For a description of this member, see <see cref="M:System.Collections.IEnumerable.GetEnumerator" />.</summary>
      <returns>An enumerator that iterates through a collection.</returns>
    </member>
    <member name="T:System.Data.Linq.ChangeConflictException">
      <summary>Thrown when an update fails because database values have been updated since the client last read them.</summary>
    </member>
    <member name="M:System.Data.Linq.ChangeConflictException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.ChangeConflictException" /> class.</summary>
    </member>
    <member name="M:System.Data.Linq.ChangeConflictException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.ChangeConflictException" /> class and specifies a message to explain the exception.</summary>
      <param name="message">The message to be exposed when the exception is thrown.</param>
    </member>
    <member name="M:System.Data.Linq.ChangeConflictException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.ChangeConflictException" /> class, specifies a message to explain the exception, and specifies the exception that caused this exception.</summary>
      <param name="message">The message to be exposed when the exception is thrown.</param>
      <param name="innerException">Specifies the exception of which <see cref="T:System.Data.Linq.ChangeConflictException" /> is a result.</param>
    </member>
    <member name="T:System.Data.Linq.ChangeSet">
      <summary>Provides a container to hold changes.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Linq.ChangeSet.Deletes">
      <summary>Gets a list of entities that have been deleted from the <see cref="T:System.Data.Linq.ChangeSet" />.</summary>
      <returns>An <see cref="T:System.Collections.IList" /> of deleted entities.</returns>
    </member>
    <member name="P:System.Data.Linq.ChangeSet.Inserts">
      <summary>Gets a list of entities that have been inserted into the <see cref="T:System.Data.Linq.ChangeSet" />.</summary>
      <returns>An <see cref="T:System.Collections.IList" /> of inserted entities.</returns>
    </member>
    <member name="M:System.Data.Linq.ChangeSet.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Data.Linq.ChangeSet" />.</summary>
      <returns>A string that represents the current <see cref="T:System.Data.Linq.ChangeSet" />.</returns>
    </member>
    <member name="P:System.Data.Linq.ChangeSet.Updates">
      <summary>Gets a list of entities that have been updated in the <see cref="T:System.Data.Linq.ChangeSet" />.</summary>
      <returns>An <see cref="T:System.Collections.IList" /> of updated entities.</returns>
    </member>
    <member name="T:System.Data.Linq.CompiledQuery">
      <summary>Provides for compilation and caching of queries for reuse.</summary>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``10(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg2">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg3">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg4">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg5">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg6">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg7">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg8">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``11(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg2">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg3">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg4">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg5">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg6">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg7">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg8">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg9">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``12(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg2">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg3">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg4">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg5">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg6">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg7">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg8">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg9">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg10">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``13(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg2">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg3">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg4">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg5">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg6">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg7">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg8">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg9">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg10">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg11">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``14(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg2">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg3">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg4">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg5">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg6">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg7">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg8">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg9">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg10">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg11">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg12">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``15(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg2">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg3">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg4">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg5">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg6">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg7">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg8">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg9">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg10">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg11">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg12">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg13">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``16(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg2">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg3">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg4">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg5">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg6">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg7">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg8">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg9">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg10">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg11">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg12">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg13">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg14">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``17(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15,``16}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query. </returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg2">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg3">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg4">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg5">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg6">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg7">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg8">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg9">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg10">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg11">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg12">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg13">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg14">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg15">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``3(System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``4(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg2">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``5(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg2">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg3">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``6(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg2">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg3">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg4">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``7(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg2">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg3">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg4">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg5">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``8(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg2">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg3">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg4">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg5">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg6">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="M:System.Data.Linq.CompiledQuery.Compile``9(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8}})">
      <summary>Compiles the query.</summary>
      <returns>A generic delegate that represents the compiled query.</returns>
      <param name="query">The query expression to be compiled.</param>
      <typeparam name="TArg0">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg1">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg2">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg3">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg4">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg5">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg6">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TArg7">Represents the type of the parameter that has to be passed in when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
      <typeparam name="TResult">The type of T in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> returned when executing the delegate returned by the <see cref="M:System.Data.Linq.CompiledQuery.Compile``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})" /> method.</typeparam>
    </member>
    <member name="P:System.Data.Linq.CompiledQuery.Expression">
      <summary>Returns the query as a lambda expression.</summary>
      <returns>The lambda expression that represents the query.</returns>
    </member>
    <member name="T:System.Data.Linq.ConflictMode">
      <summary>Specifies when concurrency conflicts should be reported.</summary>
    </member>
    <member name="F:System.Data.Linq.ConflictMode.FailOnFirstConflict">
      <summary>Specifies that attempts to update the database should stop immediately when the first concurrency conflict error is detected.</summary>
    </member>
    <member name="F:System.Data.Linq.ConflictMode.ContinueOnConflict">
      <summary>Specifies that all updates to the database should be tried, and that concurrency conflicts should be accumulated and returned at the end of the process.</summary>
    </member>
    <member name="T:System.Data.Linq.DataContext">
      <summary>Represents the main entry point for the LINQ to SQL framework.</summary>
    </member>
    <member name="M:System.Data.Linq.DataContext.#ctor(System.Data.IDbConnection)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.DataContext" /> class by referencing the connection used by the .NET Framework.</summary>
      <param name="connection">The connection used by the .NET Framework.</param>
    </member>
    <member name="M:System.Data.Linq.DataContext.#ctor(System.Data.IDbConnection,System.Data.Linq.Mapping.MappingSource)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.DataContext" /> class by referencing a connection and a mapping source.</summary>
      <param name="connection">The connection used by the .NET Framework.</param>
      <param name="mapping">A source for mapping.</param>
    </member>
    <member name="M:System.Data.Linq.DataContext.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.DataContext" /> class by referencing a file source.</summary>
      <param name="fileOrServerOrConnection">This argument can be any one of the following:The name of a file where a SQL Server Express database resides.The name of a server where a database is present. In this case the provider uses the default database for a user.A complete connection string. LINQ to SQL just passes the string to the provider without modification.</param>
    </member>
    <member name="M:System.Data.Linq.DataContext.#ctor(System.String,System.Data.Linq.Mapping.MappingSource)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.DataContext" /> class by referencing a file source and a mapping source.</summary>
      <param name="fileOrServerOrConnection">This argument can be any one of the following:The name of a file where a SQL Server Express database resides.The name of a server where a database is present. In this case the provider uses the default database for a user.A complete connection string. LINQ to SQL just passes the string to the provider without modification.</param>
      <param name="mapping">A source for mapping.</param>
    </member>
    <member name="P:System.Data.Linq.DataContext.ChangeConflicts">
      <summary>Gets a collection of objects that caused concurrency conflicts when <see cref="M:System.Data.Linq.DataContext.SubmitChanges" /> was called.</summary>
      <returns>A collection of objects that caused concurrency conflicts.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Linq.DataContext.CommandTimeout">
      <summary>Gets or sets a value that increases the time-out period for queries that would otherwise time out during the default time-out period.</summary>
      <returns>An integer value that increases the time-out period for queries that would otherwise time out during the default time-out period.</returns>
    </member>
    <member name="P:System.Data.Linq.DataContext.Connection">
      <summary>Gets the connection used by the framework.</summary>
      <returns>The connection used by the framework.</returns>
    </member>
    <member name="M:System.Data.Linq.DataContext.CreateDatabase">
      <summary>Creates a database on the server.</summary>
    </member>
    <member name="M:System.Data.Linq.DataContext.CreateMethodCallQuery``1(System.Object,System.Reflection.MethodInfo,System.Object[])">
      <summary>Executes the table-valued database function associated with the specified CLR method.</summary>
      <returns>A collection of resultant values returned by the database query.</returns>
      <param name="instance"> The instance of the method invocation (the current object).</param>
      <param name="methodInfo">The <see cref="T:System.Reflection.MethodInfo" /> that identifies the CLR method that corresponds to a database method.</param>
      <param name="parameters">The array of parameters to be passed to the command.</param>
      <typeparam name="TResult">The type of the elements in the returned collection.</typeparam>
    </member>
    <member name="M:System.Data.Linq.DataContext.DatabaseExists">
      <summary>Determines whether the associated database can be opened.</summary>
      <returns>true if the specified database can be opened; otherwise, false.</returns>
    </member>
    <member name="P:System.Data.Linq.DataContext.DeferredLoadingEnabled">
      <summary>Gets or sets a value that indicates whether to delay-load one-to-many or one-to-one relationships.</summary>
      <returns>true if deferred loading is enabled; otherwise, false.</returns>
    </member>
    <member name="M:System.Data.Linq.DataContext.DeleteDatabase">
      <summary>Deletes the associated database.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DataContext.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.Data.Linq.DataContext" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DataContext.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Data.Linq.DataContext" /> class and optionally releases the managed resource.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Data.Linq.DataContext.ExecuteCommand(System.String,System.Object[])">
      <summary>Executes SQL commands directly on the database.</summary>
      <returns>The number of rows modified by the executed command.</returns>
      <param name="command">The SQL command to be executed.</param>
      <param name="parameters">The array of parameters to be passed to the command. Note the following behavior:If the number of objects in the array is less than the highest number identified in the command string, an exception is thrown.If the array contains objects that are not referenced in the command string, no exception is thrown.If any one of the parameters is null, it is converted to DBNull.Value.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DataContext.ExecuteDynamicDelete(System.Object)">
      <summary>Executes, inside delete override methods, to redelegate to LINQ to SQL the task of generating and executing dynamic SQL for delete operations.</summary>
      <param name="entity">The entity to be deleted.</param>
    </member>
    <member name="M:System.Data.Linq.DataContext.ExecuteDynamicInsert(System.Object)">
      <summary>Executes, inside insert override methods, to redelegate to LINQ to SQL the task of generating and executing dynamic SQL for insert operations.</summary>
      <param name="entity">The entity to be inserted.</param>
    </member>
    <member name="M:System.Data.Linq.DataContext.ExecuteDynamicUpdate(System.Object)">
      <summary>Executes, inside update override methods, to redelegate to LINQ to SQL the task of generating and executing dynamic SQL for update operations.</summary>
      <param name="entity">The entity to be updated.</param>
    </member>
    <member name="M:System.Data.Linq.DataContext.ExecuteMethodCall(System.Object,System.Reflection.MethodInfo,System.Object[])">
      <summary>Executes the stored database procedure or scalar function associated with the specified CLR method.</summary>
      <returns>The result (the return value and output parameters) of executing the specified method.</returns>
      <param name="instance"> The instance of the method invocation (the current object).</param>
      <param name="methodInfo">Identifies the CLR method that corresponds to a database method.</param>
      <param name="parameters">The array of parameters to be passed to the command.</param>
    </member>
    <member name="M:System.Data.Linq.DataContext.ExecuteQuery``1(System.String,System.Object[])">
      <summary>Executes SQL queries directly on the database and returns objects.</summary>
      <returns>A collection of objects returned by the query.</returns>
      <param name="query">The SQL query to be executed.</param>
      <param name="parameters">The array of parameters to be passed to the command. Note the following behavior:If the number of objects in the array is less than the highest number identified in the command string, an exception is thrown.If the array contains objects that are not referenced in the command string, no exception is thrown.If a parameter is null, it is converted to DBNull.Value.</param>
      <typeparam name="TResult">The type of the elements in the returned collection.</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DataContext.ExecuteQuery(System.Type,System.String,System.Object[])">
      <summary>Executes SQL queries directly on the database.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> collection of objects returned by the query.</returns>
      <param name="elementType">The type of the <see cref="T:System.Collections.Generic.IEnumerable`1" /> to be returned.The algorithm for matching columns in the result of the query to fields or properties in the object works as follows:If a field or property is mapped to a particular column name, that column name is expected in the resultset.If a field or property is not mapped, a column with the same name as the field or property is expected in the resultset.The comparison is performed by looking for a case-sensitive match first. If this match is not found, a subsequent search occurs for a case-insensitive match.The query must return all the tracked fields and properties of the object (except those that are loaded on a deferred basis) when all the following conditions are true:<paramref name="T" /> is an entity explicitly tracked by the <see cref="T:System.Data.Linq.DataContext" />.<see cref="P:System.Data.Linq.DataContext.ObjectTrackingEnabled" /> is true.The entity has a primary key.Otherwise an exception is thrown.</param>
      <param name="query">The SQL query to be executed.</param>
      <param name="parameters">The array of parameters to be passed to the command. Note the following behavior:If the number of objects in the array is less than the highest number identified in the command string, an exception is thrown.If the array contains objects that are not referenced in the command string, no exception is thrown.If a parameter is null, it is converted to DBNull.Value.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DataContext.GetChangeSet">
      <summary>Gets the modified objects tracked by <see cref="T:System.Data.Linq.DataContext" />.</summary>
      <returns>The set of objects is returned as three read-only collections.</returns>
    </member>
    <member name="M:System.Data.Linq.DataContext.GetCommand(System.Linq.IQueryable)">
      <summary>Gets the information about SQL commands generated by LINQ to SQL.</summary>
      <returns>The requested command information object.</returns>
      <param name="query">The query whose SQL command information is to be retrieved.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DataContext.GetTable``1">
      <summary>Returns a collection of objects of a particular type, where the type is defined by the <paramref name="TEntity" /> parameter.</summary>
      <returns>A collection of objects defined by the <paramref name="TEntity" /> parameter. </returns>
      <typeparam name="TEntity">The type of the objects to be returned.</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DataContext.GetTable(System.Type)">
      <summary>Returns a collection of objects of a particular type, where the type is defined by the <paramref name="type" /> parameter.</summary>
      <returns>A collection of objects defined by the <paramref name="type" /> parameter.</returns>
      <param name="type">The type of the objects to be returned.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Linq.DataContext.LoadOptions">
      <summary>Gets or sets the <see cref="T:System.Data.Linq.DataLoadOptions" /> associated with this <see cref="T:System.Data.Linq.DataContext" />.</summary>
      <returns>The prefetch load options for related data.</returns>
    </member>
    <member name="P:System.Data.Linq.DataContext.Log">
      <summary>Gets or sets the destination to write the SQL query or command.</summary>
      <returns>The <see cref="T:System.IO.TextReader" /> to use for writing the command.</returns>
    </member>
    <member name="P:System.Data.Linq.DataContext.Mapping">
      <summary>Gets the <see cref="T:System.Data.Linq.Mapping.MetaModel" /> on which the mapping is based.</summary>
      <returns>The mapping between a database and domain objects.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Linq.DataContext.ObjectTrackingEnabled">
      <summary>Gets or sets a value that indicates whether object tracking is enabled.</summary>
      <returns>true if the object tracking is enabled; otherwise, false. The default is true.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DataContext.Refresh(System.Data.Linq.RefreshMode,System.Collections.IEnumerable)">
      <summary>Refreshes a collection of entity objects according to the specified mode.</summary>
      <param name="mode">A value that specifies how optimistic concurrency conflicts are handled.</param>
      <param name="entities">The collection of entities to be refreshed.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DataContext.Refresh(System.Data.Linq.RefreshMode,System.Object)">
      <summary>Refreshes an entity object according to the specified mode.</summary>
      <param name="mode">A value that specifies how optimistic concurrency conflicts are handled.</param>
      <param name="entity">The object to be refreshed.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DataContext.Refresh(System.Data.Linq.RefreshMode,System.Object[])">
      <summary>Refreshes an array of entity objects according to the specified mode.</summary>
      <param name="mode">A value that specifies how optimistic concurrency conflicts are handled.</param>
      <param name="entities">The array of entity objects to be refreshed.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DataContext.SubmitChanges">
      <summary>Computes the set of modified objects to be inserted, updated, or deleted, and executes the appropriate commands to implement the changes to the database.</summary>
    </member>
    <member name="M:System.Data.Linq.DataContext.SubmitChanges(System.Data.Linq.ConflictMode)">
      <summary>Sends changes that were made to retrieved objects to the underlying database, and specifies the action to be taken if the submission fails.</summary>
      <param name="failureMode">The action to be taken if the submission fails. Valid arguments are as follows:<see cref="F:System.Data.Linq.ConflictMode.FailOnFirstConflict" /><see cref="F:System.Data.Linq.ConflictMode.ContinueOnConflict" /></param>
    </member>
    <member name="P:System.Data.Linq.DataContext.Transaction">
      <summary>Gets or sets a local transaction for the .NET Framework to use to access the database.</summary>
      <returns>The transaction object used by the <see cref="T:System.Data.Linq.DataContext" /> when executing queries and commands.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DataContext.Translate``1(System.Data.Common.DbDataReader)">
      <summary>Converts an existing <see cref="T:System.Data.Common.DbDataReader" /> to objects.</summary>
      <returns>A collection of objects returned by the conversion.</returns>
      <param name="reader">The <see cref="T:System.Data.IDataReader" /> to be converted.</param>
      <typeparam name="TResult">The type of the <see cref="T:System.Collections.Generic.IEnumerable`1" /> to be returned.</typeparam>
    </member>
    <member name="M:System.Data.Linq.DataContext.Translate(System.Data.Common.DbDataReader)">
      <summary>Converts an existing <see cref="T:System.Data.Common.DbDataReader" /> to objects.</summary>
      <returns>A list of objects returned by the conversion.</returns>
      <param name="reader">The <see cref="T:System.Data.IDataReader" /> to be converted.</param>
    </member>
    <member name="M:System.Data.Linq.DataContext.Translate(System.Type,System.Data.Common.DbDataReader)">
      <summary>Converts an existing <see cref="T:System.Data.Common.DbDataReader" /> to objects.</summary>
      <returns>A list of objects returned by the conversion.</returns>
      <param name="elementType">The type of the <see cref="T:System.Collections.Generic.IEnumerable`1" /> to be returned.The algorithm for matching columns in the result to fields and properties in the object works as follows:If a field or property is mapped to a particular column name, that column name is expected in the resultset.If a field or property is not mapped, a column with the same name as the field or property is expected in the resultset.The comparison is performed by looking for a case-sensitive match first. If this match is not found, a subsequent search is occurs for a case-insensitive match.The query must return all the tracked fields and properties of the object (except those that are loaded on a deferred basis) when all the following conditions are true:<paramref name="T" /> is an entity explicitly tracked by the <see cref="T:System.Data.Linq.DataContext" />.<see cref="P:System.Data.Linq.DataContext.ObjectTrackingEnabled" /> is true.The entity has a primary key.Otherwise an exception is thrown.</param>
      <param name="reader">The <see cref="T:System.Data.IDataReader" /> to be converted.</param>
    </member>
    <member name="T:System.Data.Linq.DataLoadOptions">
      <summary>Provides for immediate loading and filtering of related data.</summary>
    </member>
    <member name="M:System.Data.Linq.DataLoadOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.DataLoadOptions" /> class.</summary>
    </member>
    <member name="M:System.Data.Linq.DataLoadOptions.AssociateWith``1(System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
      <summary>Filters objects retrieved for a particular relationship.</summary>
      <param name="expression">Identifies the query to be used on a particular one-to-many field or property. Note the following:If the expression does not start with a field or property that represents a one-to-many relationship, an exception is thrown.If an operator other than a valid operator appears in the expression, an exception is thrown. Valid operators are as follows:WhereOrderByThenByOrderByDescendingThenByDescendingTake</param>
      <typeparam name="T">The type that is queried against.If the type is unmapped, an exception is thrown.</typeparam>
    </member>
    <member name="M:System.Data.Linq.DataLoadOptions.AssociateWith(System.Linq.Expressions.LambdaExpression)">
      <summary>Filters the objects retrieved for a particular relationship.</summary>
      <param name="expression">Identifies the query to be used on a particular one-to-many field or property. Note the following:If the expression does not start with a field or property that represents a one-to-many relationship, an exception is thrown.If an operator other than a valid operator appears in the expression, an exception is thrown. Valid operators are as follows:WhereOrderByThenByOrderByDescendingThenByDescendingTake</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DataLoadOptions.LoadWith``1(System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
      <summary>Specifies which sub-objects to retrieve when a query is submitted for an object of type T.</summary>
      <param name="expression">Identifies the field or property to be retrieved.If the expression does not identify a field or property that represents a one-to-one or one-to-many relationship, an exception is thrown.</param>
      <typeparam name="T">Type that is queried against.If this type is unmapped, an exception is thrown.</typeparam>
    </member>
    <member name="M:System.Data.Linq.DataLoadOptions.LoadWith(System.Linq.Expressions.LambdaExpression)">
      <summary>Retrieves specified data related to the main target by using a lambda expression.</summary>
      <param name="expression">A lambda expression that identifies the related material.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Data.Linq.DBConvert">
      <summary>Used internally to convert one type to another.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DBConvert.ChangeType``1(System.Object)">
      <summary>Changes the specified value to the current type.</summary>
      <returns>An object of the specified type that contains the converted value.</returns>
      <param name="value">The object to be converted.</param>
      <typeparam name="T">The type to change to.</typeparam>
    </member>
    <member name="M:System.Data.Linq.DBConvert.ChangeType(System.Object,System.Type)">
      <summary>Changes the specified value to the specified type.</summary>
      <returns>An object that contains the converted value of the specified type.</returns>
      <param name="value">The object to be converted.</param>
      <param name="type">The type to convert the object to.</param>
    </member>
    <member name="T:System.Data.Linq.DuplicateKeyException">
      <summary>Thrown when an attempt is made to add an object to the identity cache by using a key that is already being used.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.DuplicateKeyException.#ctor(System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.DuplicateKeyException" /> class.</summary>
      <param name="duplicate">The duplicate key that caused the exception to be thrown.</param>
    </member>
    <member name="M:System.Data.Linq.DuplicateKeyException.#ctor(System.Object,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.DuplicateKeyException" /> class by referencing the duplicate key and providing an error message.</summary>
      <param name="duplicate">The duplicate key that caused the exception to be thrown.</param>
      <param name="message">The message to appear when the exception is thrown.</param>
    </member>
    <member name="M:System.Data.Linq.DuplicateKeyException.#ctor(System.Object,System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.DuplicateKeyException" /> class by referencing the duplicate key, providing an error message, and specifying the exception that caused this exception to be thrown.</summary>
      <param name="duplicate">The duplicate key that caused the exception to be thrown.</param>
      <param name="message">The message to appear when the exception is thrown.</param>
      <param name="innerException">The previous exception that caused the <see cref="T:System.Data.Linq.DuplicateKeyException" /> exception to be thrown.</param>
    </member>
    <member name="P:System.Data.Linq.DuplicateKeyException.Object">
      <summary>Gets the object that caused the exception.</summary>
      <returns>The object that caused the exception.</returns>
    </member>
    <member name="T:System.Data.Linq.EntityRef`1">
      <summary>Provides for deferred loading and relationship maintenance for the singleton side of a one-to-many relationship in a LINQ to SQL application. </summary>
      <typeparam name="TEntity">The type of the target entity.</typeparam>
    </member>
    <member name="M:System.Data.Linq.EntityRef`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.EntityRef`1" /> class by specifying the source.</summary>
      <param name="source">The reference source.</param>
    </member>
    <member name="M:System.Data.Linq.EntityRef`1.#ctor(System.Data.Linq.EntityRef{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.EntityRef`1" /> class by referencing the target entity.</summary>
      <param name="entityRef">The target entity.</param>
    </member>
    <member name="M:System.Data.Linq.EntityRef`1.#ctor(`0)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.EntityRef`1" /> class by specifying the target entity.</summary>
      <param name="entity">The target entity.</param>
    </member>
    <member name="P:System.Data.Linq.EntityRef`1.Entity">
      <summary>Gets or sets the target entity.</summary>
      <returns>The target entity.</returns>
    </member>
    <member name="P:System.Data.Linq.EntityRef`1.HasLoadedOrAssignedValue">
      <summary>Gets a value that indicates whether the target has been loaded or assigned.</summary>
      <returns>True if the target has been loaded or assigned.</returns>
    </member>
    <member name="T:System.Data.Linq.EntitySet`1">
      <summary>Provides for deferred loading and relationship maintenance for the collection side of one-to-many and one-to-one relationships in a LINQ to SQL applications. </summary>
      <typeparam name="TEntity">The data type of the target entity.</typeparam>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.EntitySet`1" /> class.</summary>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.#ctor(System.Action{`0},System.Action{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.EntitySet`1" /> class while supplying handlers for add and remove operations.</summary>
      <param name="onAdd">Delegate for <see cref="M:System.Data.Linq.EntitySet`1.Add(`0)" />.</param>
      <param name="onRemove">Delegate for <see cref="M:System.Data.Linq.EntitySet`1.Remove(`0)" />.</param>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.Add(`0)">
      <summary>Adds an entity.</summary>
      <param name="entity">The entity to add.</param>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Adds a collection of entities.</summary>
      <param name="collection">The collection to be added.</param>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.Assign(System.Collections.Generic.IEnumerable{`0})">
      <summary>Assigns an <see cref="T:System.Data.Linq.EntitySet`1" /> collection to another <see cref="T:System.Data.Linq.EntitySet`1" /> collection.</summary>
      <param name="entitySource">The collection to assign.</param>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.Clear">
      <summary>Removes all items.</summary>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.Contains(`0)">
      <summary>Specifies whether the <see cref="T:System.Data.Linq.EntitySet`1" /> contains a specific entity.</summary>
      <returns>true if the <see cref="T:System.Data.Linq.EntitySet`1" /> contains the entity; otherwise, false.</returns>
      <param name="entity">The entity.</param>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.CopyTo(`0[],System.Int32)">
      <summary>Copies the <see cref="T:System.Data.Linq.EntitySet`1" /> to an array.</summary>
      <param name="array">The array to copy to.</param>
      <param name="arrayIndex">The starting index in the array.</param>
    </member>
    <member name="P:System.Data.Linq.EntitySet`1.Count">
      <summary>Gets the number of entities in the <see cref="T:System.Data.Linq.EntitySet`1" /> collection.</summary>
      <returns>An integer representing the number of entities.</returns>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" />.</returns>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.GetNewBindingList">
      <summary>Creates a new list for binding to a data source.</summary>
      <returns>A new <see cref="T:System.ComponentModel.IBindingList" /> for binding to a data source.</returns>
    </member>
    <member name="P:System.Data.Linq.EntitySet`1.HasLoadedOrAssignedValues">
      <summary>Specifies whether the <see cref="T:System.Data.Linq.EntitySet`1" /> has loaded or assigned a value.</summary>
      <returns>Returns true if the <see cref="T:System.Data.Linq.EntitySet`1" /> has either loaded or assigned a value.</returns>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.IndexOf(`0)">
      <summary>Returns the index of the entity.</summary>
      <returns>An integer representing the index.</returns>
      <param name="entity">The entity whose index is to be returned.</param>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.Insert(System.Int32,`0)">
      <summary>Inserts an entity at an index position.</summary>
      <param name="index">The index representing the position at which to insert the entity.</param>
      <param name="entity">The entity to be inserted.</param>
    </member>
    <member name="P:System.Data.Linq.EntitySet`1.IsDeferred">
      <summary>Specifies whether this <see cref="T:System.Data.Linq.EntitySet`1" /> has a deferred query that has not yet executed.</summary>
      <returns>true if a deferred query has not yet been executed; otherwise false.</returns>
    </member>
    <member name="P:System.Data.Linq.EntitySet`1.Item(System.Int32)">
      <summary>Gets or sets the element at the specified index.</summary>
      <returns>An <see cref="T:System.Data.Linq.EntitySet`1" /> representing the item.</returns>
      <param name="index">The index of the element.</param>
    </member>
    <member name="E:System.Data.Linq.EntitySet`1.ListChanged">
      <summary>Occurs when the contents of a list are changed.</summary>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.Load">
      <summary>Loads the <see cref="T:System.Data.Linq.EntitySet`1" />.</summary>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.Remove(`0)">
      <summary>Removes an entity.</summary>
      <returns>true if the entity is successfully removed; otherwise, false.</returns>
      <param name="entity">The entity to be removed.</param>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.RemoveAt(System.Int32)">
      <summary>Removes an entity at a specified index.</summary>
      <param name="index">The index of the entity to be removed.</param>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.SetSource(System.Collections.Generic.IEnumerable{`0})">
      <summary>Sets the source of the <see cref="T:System.Data.Linq.EntitySet`1" />.</summary>
      <param name="entitySource">The source of the <see cref="T:System.Data.Linq.EntitySet`1" />.</param>
    </member>
    <member name="P:System.Data.Linq.EntitySet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>For a description of this member, see <see cref="P:System.Collections.Generic.ICollection`1.IsReadOnly" />.</summary>
      <returns>true if the collection is read-only; otherwise, false.</returns>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>For a description of this member, see <see cref="M:System.Collections.ICollection.CopyTo(System.Array,System.Int32)" />.</summary>
      <param name="array">The array to which elements of the collection are copied.</param>
      <param name="index">The array index at which copying begins.</param>
    </member>
    <member name="P:System.Data.Linq.EntitySet`1.System#Collections#ICollection#IsSynchronized">
      <summary>For a description of this member, see <see cref="P:System.Collections.ICollection.IsSynchronized" />.</summary>
      <returns>true if access to the collection is synchronized (thread-safe); otherwise, false. </returns>
    </member>
    <member name="P:System.Data.Linq.EntitySet`1.System#Collections#ICollection#SyncRoot">
      <summary>For a description of this member, see <see cref="P:System.Collections.ICollection.SyncRoot" />.</summary>
      <returns>An object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>For a description of this member, see <see cref="M:System.Collections.IEnumerable.GetEnumerator" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through a collection.</returns>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.System#Collections#IList#Add(System.Object)">
      <summary>For a description of this member, see <see cref="M:System.Collections.IList.Add(System.Object)" />.</summary>
      <returns>The position into which the new element was inserted.</returns>
      <param name="value">The object to add to the list.</param>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.System#Collections#IList#Contains(System.Object)">
      <summary>For a description of this member, see <see cref="M:System.Collections.IList.Contains(System.Object)" />.</summary>
      <returns>true if the <see cref="T:System.Object" /> is found in the <see cref="T:System.Collections.IList" />; otherwise, false.</returns>
      <param name="value">The object to locate in the list.</param>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.System#Collections#IList#IndexOf(System.Object)">
      <summary>For a description of this member, see <see cref="M:System.Collections.IList.IndexOf(System.Object)" />.</summary>
      <returns>The index of <paramref name="value" /> if found in the list; otherwise, -1.</returns>
      <param name="value">The object to locate in the list.</param>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>For a description of this member, see <see cref="M:System.Collections.IList.Insert(System.Int32,System.Object)" />.</summary>
      <param name="index">The index of the object in the list.</param>
      <param name="value">The object to insert into the list.</param>
    </member>
    <member name="P:System.Data.Linq.EntitySet`1.System#Collections#IList#IsFixedSize">
      <summary>For a description of this member, see <see cref="P:System.Collections.IList.IsFixedSize" />.</summary>
      <returns>true if the collection has a fixed size; otherwise false.</returns>
    </member>
    <member name="P:System.Data.Linq.EntitySet`1.System#Collections#IList#IsReadOnly">
      <summary>For a description of this member, see <see cref="P:System.Collections.IList.IsReadOnly" />.</summary>
      <returns>true if the collection is read-only; otherwise, false.</returns>
    </member>
    <member name="P:System.Data.Linq.EntitySet`1.System#Collections#IList#Item(System.Int32)">
      <summary>For a description of this member, see <see cref="P:System.Collections.IList.Item(System.Int32)" />.</summary>
      <returns>The element at the specified index.</returns>
      <param name="index">The index at which to set or get the element.</param>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.System#Collections#IList#Remove(System.Object)">
      <summary>For a description of this member, see <see cref="M:System.Collections.IList.Remove(System.Object)" />.</summary>
      <param name="value">The object to remove from the list.</param>
    </member>
    <member name="P:System.Data.Linq.EntitySet`1.System#ComponentModel#IListSource#ContainsListCollection">
      <summary>For a description of this member, see <see cref="P:System.ComponentModel.IListSource.ContainsListCollection" />.</summary>
      <returns>true if the collection is a collection of <see cref="T:System.Collections.IList" /> objects; otherwise false;</returns>
    </member>
    <member name="M:System.Data.Linq.EntitySet`1.System#ComponentModel#IListSource#GetList">
      <summary>For a description of this member, see <see cref="M:System.ComponentModel.IListSource.GetList" />.</summary>
      <returns>An <see cref="T:System.Collections.IList" /> that can be bound to a data source from the object.</returns>
    </member>
    <member name="T:System.Data.Linq.ForeignKeyReferenceAlreadyHasValueException">
      <summary>Represents errors that occur when an attempt is made to change a foreign key when the entity is already loaded.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.ForeignKeyReferenceAlreadyHasValueException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.ForeignKeyReferenceAlreadyHasValueException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.Data.Linq.ForeignKeyReferenceAlreadyHasValueException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.ForeignKeyReferenceAlreadyHasValueException" /> class with a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
    </member>
    <member name="M:System.Data.Linq.ForeignKeyReferenceAlreadyHasValueException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.ForeignKeyReferenceAlreadyHasValueException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture. </param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception. </param>
    </member>
    <member name="T:System.Data.Linq.IExecuteResult">
      <summary>Provides access to the return value or results of executing a query.</summary>
    </member>
    <member name="M:System.Data.Linq.IExecuteResult.GetParameterValue(System.Int32)">
      <summary>Provides access to the nth output parameter.</summary>
      <returns>An object that contains the value of the specified parameter. </returns>
      <param name="parameterIndex">The index of the parameter to be retrieved.</param>
    </member>
    <member name="P:System.Data.Linq.IExecuteResult.ReturnValue">
      <summary>Gets the return value or result of the executed query.</summary>
      <returns>The value or result of the executed query.</returns>
    </member>
    <member name="T:System.Data.Linq.IFunctionResult">
      <summary>Provides access to the return value of a function.</summary>
    </member>
    <member name="P:System.Data.Linq.IFunctionResult.ReturnValue">
      <summary>Gets the return value of a function.</summary>
      <returns>The value returned by the function.</returns>
    </member>
    <member name="T:System.Data.Linq.IMultipleResults">
      <summary>Represents the results of mapped functions or queries with variable return sequences.</summary>
    </member>
    <member name="M:System.Data.Linq.IMultipleResults.GetResult``1">
      <summary>Retrieves the next result as a sequence of a specified type.</summary>
      <returns>An enumeration for iterating over the results.</returns>
      <typeparam name="TElement">The type of the sequence to be returned.</typeparam>
    </member>
    <member name="T:System.Data.Linq.ISingleResult`1">
      <summary>Represents the result of a mapped function that has a single return sequence.</summary>
      <typeparam name="T">The type of the elements in the return sequence.</typeparam>
    </member>
    <member name="T:System.Data.Linq.ITable">
      <summary>Used for weakly typed query scenarios.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.ITable.Attach(System.Object)">
      <summary>Attaches an entity to the <see cref="T:System.Data.Linq.DataContext" /> in an unmodified state.</summary>
      <param name="entity">The entity to be attached.</param>
    </member>
    <member name="M:System.Data.Linq.ITable.Attach(System.Object,System.Boolean)">
      <summary>Attaches all entities of a collection to the <see cref="T:System.Data.Linq.DataContext" /> in either a modified or unmodified state.</summary>
      <param name="entity">The collection of entities.</param>
      <param name="asModified">true to attach the entities as modified.</param>
    </member>
    <member name="M:System.Data.Linq.ITable.Attach(System.Object,System.Object)">
      <summary>Attaches an entity to the <see cref="T:System.Data.Linq.DataContext" /> in either a modified or unmodified state by specifying both the entity and its original state.</summary>
      <param name="entity">The entity to be attached.</param>
      <param name="original">An instance of the same entity type with data members that contain the original values.</param>
    </member>
    <member name="M:System.Data.Linq.ITable.AttachAll(System.Collections.IEnumerable)">
      <summary>Attaches all entities of a collection to the <see cref="T:System.Data.Linq.DataContext" /> in either a modified or unmodified state.</summary>
      <param name="entities">The collection of entities.</param>
    </member>
    <member name="M:System.Data.Linq.ITable.AttachAll(System.Collections.IEnumerable,System.Boolean)">
      <summary>Attaches all entities of a collection to the <see cref="T:System.Data.Linq.DataContext" /> in either a modified or unmodified state.</summary>
      <param name="entities">The collection of entities.</param>
      <param name="asModified">true to attach the entities as modified.</param>
    </member>
    <member name="P:System.Data.Linq.ITable.Context">
      <summary>Gets the <see cref="T:System.Data.Linq.DataContext" /> that has been used to retrieve this <see cref="T:System.Data.Linq.ITable" />.</summary>
      <returns>The <see cref="T:System.Data.Linq.DataContext" /> used to retrieve the <see cref="T:System.Data.Linq.ITable" />.</returns>
    </member>
    <member name="M:System.Data.Linq.ITable.DeleteAllOnSubmit(System.Collections.IEnumerable)">
      <summary>Puts all entities from the collection into a pending delete state.</summary>
      <param name="entities">The collection from which all items are removed.</param>
    </member>
    <member name="M:System.Data.Linq.ITable.DeleteOnSubmit(System.Object)">
      <summary>Puts an entity from this table into a pending delete state.</summary>
      <param name="entity">The entity to be removed.</param>
    </member>
    <member name="M:System.Data.Linq.ITable.GetModifiedMembers(System.Object)">
      <summary>Returns an array of modified members that contain their current and original values.</summary>
      <param name="entity">The entity from which to get the array.</param>
    </member>
    <member name="M:System.Data.Linq.ITable.GetOriginalEntityState(System.Object)">
      <summary>Retrieves original values.</summary>
      <returns>A copy of the original entity. The value is null if the entity passed in is not tracked. Disconnected entities sent back by a client must be attached before the <see cref="T:System.Data.Linq.DataContext" /> can begin to track their state. The "original state" of a newly attached entity is established based on values supplied by the client. The data context does not track the state of disconnected entities.</returns>
      <param name="entity">The entity whose original value is to be retrieved.</param>
    </member>
    <member name="M:System.Data.Linq.ITable.InsertAllOnSubmit(System.Collections.IEnumerable)">
      <summary>Adds all entities of a collection to the <see cref="T:System.Data.Linq.DataContext" /> in a pending insert state.</summary>
      <param name="entities">The entities to add.</param>
    </member>
    <member name="M:System.Data.Linq.ITable.InsertOnSubmit(System.Object)">
      <summary>Adds an entity in a pending insert state to this table.</summary>
      <param name="entity">The entity to be added.</param>
    </member>
    <member name="P:System.Data.Linq.ITable.IsReadOnly">
      <summary>Indicates if the type of the entities contained in this <see cref="T:System.Data.Linq.ITable" /> instance has a primary key.</summary>
      <returns>Returns true if the entity type does not have a primary key; otherwise, false.</returns>
    </member>
    <member name="T:System.Data.Linq.ITable`1">
      <summary>Represents a table for a particular type in the underlying database. </summary>
      <typeparam name="TEntity"></typeparam>
    </member>
    <member name="M:System.Data.Linq.ITable`1.Attach(`0)">
      <summary>When overridden, attaches a disconnected or "detached" entity to a new <see cref="T:System.Data.Linq.DataContext" /> when original values are required for optimistic concurrency checks.</summary>
      <param name="entity">The object to be added.</param>
    </member>
    <member name="M:System.Data.Linq.ITable`1.DeleteOnSubmit(`0)">
      <summary>When overridden, puts an entity from this table into a pending delete state.</summary>
      <param name="entity">The object to delete.</param>
    </member>
    <member name="M:System.Data.Linq.ITable`1.InsertOnSubmit(`0)">
      <summary>When overridden, adds an entity in a pending insert state to this <see cref="T:System.Data.Linq.ITable`1" />.</summary>
      <param name="entity">The object to insert.</param>
    </member>
    <member name="T:System.Data.Linq.Link`1">
      <summary>Used to enable deferred loading of individual properties (similar to <see cref="T:System.Data.Linq.EntityRef`1" />).</summary>
      <typeparam name="T">The type of the elements in the deferred source.</typeparam>
    </member>
    <member name="M:System.Data.Linq.Link`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Link`1" /> structure by referencing the source.</summary>
      <param name="source">The source collection.</param>
    </member>
    <member name="M:System.Data.Linq.Link`1.#ctor(System.Data.Linq.Link{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Link`1" /> structure by copying the internal state from another <see cref="T:System.Data.Linq.Link`1" /> instance.</summary>
      <param name="link">The <see cref="T:System.Data.Linq.Link`1" /> instance from which to copy.</param>
    </member>
    <member name="M:System.Data.Linq.Link`1.#ctor(`0)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Link`1" /> structure by referencing the value of the property.</summary>
      <param name="value">The value for the property.</param>
    </member>
    <member name="P:System.Data.Linq.Link`1.HasLoadedOrAssignedValue">
      <summary>Specifies whether the <see cref="T:System.Data.Linq.Link`1" /> has loaded or assigned a value.</summary>
      <returns>true if the <see cref="T:System.Data.Linq.Link`1" /> has either loaded or assigned a value; otherwise false.</returns>
    </member>
    <member name="P:System.Data.Linq.Link`1.HasValue">
      <summary>Gets a value that specifies whether the source has a value.</summary>
      <returns>Returns true if the source has an assigned or loaded value (including null).</returns>
    </member>
    <member name="P:System.Data.Linq.Link`1.Value">
      <summary>Gets or sets the value assigned to or loaded by the <see cref="T:System.Data.Linq.Link`1" />.</summary>
      <returns>The value of this deferred property.</returns>
    </member>
    <member name="T:System.Data.Linq.MemberChangeConflict">
      <summary>Represents a situation in which an attempted update fails because member values have been updated since the client last read them.</summary>
    </member>
    <member name="P:System.Data.Linq.MemberChangeConflict.CurrentValue">
      <summary>Gets the current value of the member in conflict.</summary>
      <returns>The object in conflict.</returns>
    </member>
    <member name="P:System.Data.Linq.MemberChangeConflict.DatabaseValue">
      <summary>Gets the database value of the member in conflict.</summary>
      <returns>The value of the object in conflict.</returns>
    </member>
    <member name="P:System.Data.Linq.MemberChangeConflict.IsModified">
      <summary>Gets a value that indicates whether the member data has been changed since the last database read or refresh.</summary>
      <returns>True if the member data has been changed.</returns>
    </member>
    <member name="P:System.Data.Linq.MemberChangeConflict.IsResolved">
      <summary>Gets a value that indicates whether the conflict has been resolved.</summary>
      <returns>True if the conflict has been resolved.</returns>
    </member>
    <member name="P:System.Data.Linq.MemberChangeConflict.Member">
      <summary>Gets metadata information about the member in conflict.</summary>
      <returns>Information about the member in conflict.</returns>
    </member>
    <member name="P:System.Data.Linq.MemberChangeConflict.OriginalValue">
      <summary>Gets the original value of the member in conflict.</summary>
      <returns>The original value of the member in conflict.</returns>
    </member>
    <member name="M:System.Data.Linq.MemberChangeConflict.Resolve(System.Data.Linq.RefreshMode)">
      <summary>Uses a <see cref="T:System.Data.Linq.RefreshMode" /> parameter to automatically specify the value to set as the current value for the member in conflict.</summary>
      <param name="refreshMode">See <see cref="T:System.Data.Linq.RefreshMode" />.</param>
    </member>
    <member name="M:System.Data.Linq.MemberChangeConflict.Resolve(System.Object)">
      <summary>Specifies the value to set as the current value for the member in conflict.</summary>
      <param name="value">The value to set as the current value.</param>
    </member>
    <member name="T:System.Data.Linq.ModifiedMemberInfo">
      <summary>Holds values of members that have been modified in LINQ to SQL applications.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Linq.ModifiedMemberInfo.CurrentValue">
      <summary>Gets the current value of the modified member.</summary>
      <returns>The value of the member.</returns>
    </member>
    <member name="P:System.Data.Linq.ModifiedMemberInfo.Member">
      <summary>Gets member information for the modified member.</summary>
      <returns>Information about the member in conflict.</returns>
    </member>
    <member name="P:System.Data.Linq.ModifiedMemberInfo.OriginalValue">
      <summary>Gets the original value of the modified member.</summary>
      <returns>The original value for the modified member.</returns>
    </member>
    <member name="T:System.Data.Linq.ObjectChangeConflict">
      <summary>Represents an update attempt with one or more optimistic concurrency conflicts.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Linq.ObjectChangeConflict.IsDeleted">
      <summary>Gets a value that indicates whether the object in conflict has been deleted from the database.</summary>
      <returns>True if the object has been deleted.</returns>
    </member>
    <member name="P:System.Data.Linq.ObjectChangeConflict.IsResolved">
      <summary>Gets a value that indicates whether the conflicts for this object have already been resolved.</summary>
      <returns>True if the conflicts have been resolved.</returns>
    </member>
    <member name="P:System.Data.Linq.ObjectChangeConflict.MemberConflicts">
      <summary>Gets a collection of all member conflicts that caused the update to fail.</summary>
      <returns>A collection of member conflicts.</returns>
    </member>
    <member name="P:System.Data.Linq.ObjectChangeConflict.Object">
      <summary>Gets the object in conflict.</summary>
      <returns>The object in conflict.</returns>
    </member>
    <member name="M:System.Data.Linq.ObjectChangeConflict.Resolve">
      <summary>Resolves member conflicts by keeping current values and resetting the baseline original values to match the more recent database values.</summary>
    </member>
    <member name="M:System.Data.Linq.ObjectChangeConflict.Resolve(System.Data.Linq.RefreshMode)">
      <summary>Resolves member conflicts by using the specified <see cref="T:System.Data.Linq.RefreshMode" />.</summary>
      <param name="refreshMode">The appropriate option from <see cref="T:System.Data.Linq.RefreshMode" />.</param>
    </member>
    <member name="M:System.Data.Linq.ObjectChangeConflict.Resolve(System.Data.Linq.RefreshMode,System.Boolean)">
      <summary>Resolve member conflicts keeping current values and resetting the baseline original values.</summary>
      <param name="refreshMode">The appropriate option from <see cref="T:System.Data.Linq.RefreshMode" />.</param>
      <param name="autoResolveDeletes">When true, automatically resolves conflicts resulting from a modified object that is no longer in the database. </param>
    </member>
    <member name="T:System.Data.Linq.RefreshMode">
      <summary>Defines how the <see cref="Overload:System.Data.Linq.DataContext.Refresh" /> method handles optimistic concurrency conflicts.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.Linq.RefreshMode.KeepCurrentValues">
      <summary>Forces the <see cref="Overload:System.Data.Linq.DataContext.Refresh" /> method to swap the original value with the values retrieved from the database. No current value is modified.</summary>
    </member>
    <member name="F:System.Data.Linq.RefreshMode.KeepChanges">
      <summary>Forces the <see cref="Overload:System.Data.Linq.DataContext.Refresh" /> method to keep the current value that has been changed, but updates the other values with the database values.</summary>
    </member>
    <member name="F:System.Data.Linq.RefreshMode.OverwriteCurrentValues">
      <summary>Forces the <see cref="Overload:System.Data.Linq.DataContext.Refresh" /> method to override all the current values with the values from the database.</summary>
    </member>
    <member name="T:System.Data.Linq.Table`1">
      <summary>Represents a table for a particular type in the underlying database.</summary>
      <typeparam name="TEntity">The type of the data in the table.</typeparam>
    </member>
    <member name="M:System.Data.Linq.Table`1.Attach(`0)">
      <summary>Attaches a disconnected or "detached" entity to a new <see cref="T:System.Data.Linq.DataContext" /> when original values are required for optimistic concurrency checks.</summary>
      <param name="entity">The original values of the entity to be attached.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.Attach(`0,System.Boolean)">
      <summary>Attaches an entity to the <see cref="T:System.Data.Linq.DataContext" /> in either a modified or unmodified state.</summary>
      <param name="entity">The entity to be attached.</param>
      <param name="asModified">true to attach the entity as modified; false to attach the entity as unmodified.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.Attach(`0,`0)">
      <summary>Attaches an entity to the <see cref="T:System.Data.Linq.DataContext" /> in either a modified or unmodified state by specifying both the entity and its original state.</summary>
      <param name="entity">The entity to be attached.</param>
      <param name="original">An instance of the same entity type with data members that contain the original values.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.AttachAll``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Attaches all entities of a collection to the <see cref="T:System.Data.Linq.DataContext" /> in either a modified or unmodified state.</summary>
      <param name="entities">The collection of entities.</param>
      <typeparam name="TSubEntity">The type of entities to attach.</typeparam>
    </member>
    <member name="M:System.Data.Linq.Table`1.AttachAll``1(System.Collections.Generic.IEnumerable{``0},System.Boolean)">
      <summary>Attaches all entities of a collection to the <see cref="T:System.Data.Linq.DataContext" /> in either a modified or unmodified state.</summary>
      <param name="entities">The collection of entities.</param>
      <param name="asModified">true if the object has a timestamp or RowVersion member; false if original values are being used for the optimistic concurrency check.</param>
      <typeparam name="TSubEntity">The type of entities to attach.</typeparam>
    </member>
    <member name="P:System.Data.Linq.Table`1.Context">
      <summary>Gets the <see cref="T:System.Data.Linq.DataContext" /> that has been used to retrieve this <see cref="T:System.Data.Linq.Table`1" />.</summary>
      <returns>The data context that has been used to retrieve this table.</returns>
    </member>
    <member name="M:System.Data.Linq.Table`1.DeleteAllOnSubmit``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Puts all entities from the collection into a pending delete state.</summary>
      <param name="entities">The entities to delete.</param>
      <typeparam name="TSubEntity">The type of the elements to delete.</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.Table`1.DeleteOnSubmit(`0)">
      <summary>Puts an entity from this table into a pending delete state.</summary>
      <param name="entity">The entity to be deleted.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.GetEnumerator">
      <summary>Gets an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.Table`1.GetModifiedMembers(`0)">
      <summary>Returns an array of modified members that contain their current and original values.</summary>
      <returns>An array of modified members that contain their current and original values.</returns>
      <param name="entity">The entity from which to get the array.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.Table`1.GetNewBindingList">
      <summary>Creates a new list for binding to a data source.</summary>
      <returns>A new <see cref="T:System.ComponentModel.IBindingList" /> for binding to a data source.</returns>
    </member>
    <member name="M:System.Data.Linq.Table`1.GetOriginalEntityState(`0)">
      <summary>Returns a <see cref="T:System.Data.Linq.Table`1" /> instance that contains the original state of the entity.</summary>
      <returns>A <see cref="T:System.Data.Linq.Table`1" /> instance that contains the original state of the entity.</returns>
      <param name="entity">The entity whose original state is to be returned.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Linq.Table`1.InsertAllOnSubmit``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Adds all entities of a collection to the <see cref="T:System.Data.Linq.DataContext" /> in a pending insert state.</summary>
      <param name="entities">The entities to add.</param>
      <typeparam name="TSubEntity">The type of the elements to insert.</typeparam>
    </member>
    <member name="M:System.Data.Linq.Table`1.InsertOnSubmit(`0)">
      <summary>Adds an entity in a pending insert state to this <see cref="T:System.Data.Linq.Table`1" />.</summary>
      <param name="entity">The entity to be added.</param>
    </member>
    <member name="P:System.Data.Linq.Table`1.IsReadOnly">
      <summary>Gets a value that indicates whether the type of the entities contained in this <see cref="T:System.Data.Linq.Table`1" /> instance has a primary key.</summary>
      <returns>true if the entity type does not have a primary key; otherwise, false.</returns>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:System.Data.Linq.Table`1.System#ComponentModel#IListSource#ContainsListCollection">
      <summary>Gets a value that indicates whether the collection is a collection of <see cref="T:System.Collections.IList" /> objects.</summary>
      <returns>true if the collection is a collection of <see cref="T:System.Collections.IList" /> objects; otherwise, false.</returns>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#ComponentModel#IListSource#GetList">
      <summary>Returns an <see cref="T:System.Collections.IList" /> that can be bound to a data source from an object that does not implement an <see cref="T:System.Collections.IList" /> itself.</summary>
      <returns>An <see cref="T:System.Collections.IList" /> that can be bound to a data source.</returns>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Data#Linq#ITable#Attach(System.Object)">
      <summary>Attaches an entity to the <see cref="T:System.Data.Linq.DataContext" /> in an unmodified state.</summary>
      <param name="entity">The entity to be attached.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Data#Linq#ITable#Attach(System.Object,System.Boolean)">
      <summary>Attaches all entities of a collection to the <see cref="T:System.Data.Linq.DataContext" /> in either a modified or unmodified state.</summary>
      <param name="entity">The entity to be attached.</param>
      <param name="asModified">true to attach the entity as modified; false to attached the entity as unmodified.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Data#Linq#ITable#Attach(System.Object,System.Object)">
      <summary>Attaches an entity to the <see cref="T:System.Data.Linq.DataContext" /> in either a modified or unmodified state by specifying both the entity and its original state.</summary>
      <param name="entity">The entity to be attached.</param>
      <param name="original">An instance of the same entity type with data members that contain the original values.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Data#Linq#ITable#AttachAll(System.Collections.IEnumerable)">
      <summary>Attaches all entities of a collection to the <see cref="T:System.Data.Linq.DataContext" /> in either a modified or unmodified state.</summary>
      <param name="entities">The collection of entities to be attached.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Data#Linq#ITable#AttachAll(System.Collections.IEnumerable,System.Boolean)">
      <summary>Attaches all entities of a collection to the <see cref="T:System.Data.Linq.DataContext" /> in either a modified or unmodified state.</summary>
      <param name="entities">The collection of entities to be attched.</param>
      <param name="asModified">true to attach the entities as modified; false to attach the entities as unmodified.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Data#Linq#ITable#DeleteAllOnSubmit(System.Collections.IEnumerable)">
      <summary>Puts all entities from the collection into a pending delete state.</summary>
      <param name="entities">The entities being removed.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Data#Linq#ITable#DeleteOnSubmit(System.Object)">
      <summary>Puts an entity from this table into a pending delete state.</summary>
      <param name="entity">The entity to be removed.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Data#Linq#ITable#GetModifiedMembers(System.Object)">
      <summary>Returns an array of modified members that contain their current and original values.</summary>
      <returns>An array of modified members.</returns>
      <param name="entity">The entity from which to obtain the modified members.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Data#Linq#ITable#GetOriginalEntityState(System.Object)">
      <summary>Retrieves original values of a given entity.</summary>
      <returns>A copy of the original entity.</returns>
      <param name="entity">The entity whose original state is to be accessed.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Data#Linq#ITable#InsertAllOnSubmit(System.Collections.IEnumerable)">
      <summary>Adds all entities of a collection to the <see cref="T:System.Data.Linq.DataContext" /> in an pending insert state.</summary>
      <param name="entities">The collection of entities to be added.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Data#Linq#ITable#InsertOnSubmit(System.Object)">
      <summary>Adds an entity in a pending insert state to this table.</summary>
      <param name="entity">The entity to add.</param>
    </member>
    <member name="P:System.Data.Linq.Table`1.System#Linq#IQueryable#ElementType">
      <summary>Gets the type of the element(s) that are returned when the expression tree associated with this object is executed.</summary>
      <returns>The type of the element(s) that are returned when the expression tree associated with this object is executed.</returns>
    </member>
    <member name="P:System.Data.Linq.Table`1.System#Linq#IQueryable#Expression">
      <summary>Gets the expression tree that is associated with this instance of <see cref="T:System.Linq.IQueryable" />.</summary>
      <returns>The <see cref="T:System.Linq.Expressions.Expression" /> that is associated with this instance of <see cref="T:System.Linq.IQueryable" />.</returns>
    </member>
    <member name="P:System.Data.Linq.Table`1.System#Linq#IQueryable#Provider">
      <summary>Gets the query provider that is associated with this data source.</summary>
      <returns>The <see cref="T:System.Linq.IQueryProvider" /> that is associated with this data source.</returns>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Linq#IQueryProvider#CreateQuery``1(System.Linq.Expressions.Expression)">
      <summary>Constructs an <see cref="T:System.Linq.IQueryable`1" /> object that can evaluate the query represented by a specified expression tree.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that can evaluate the query represented by the specified expression tree.</returns>
      <param name="expression">The query expression from which the evaluation is performed.</param>
      <typeparam name="TResult">The type of the data in the table.</typeparam>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Linq#IQueryProvider#CreateQuery(System.Linq.Expressions.Expression)">
      <summary>Constructs an <see cref="T:System.Linq.IQueryable" /> object that can evaluate the query represented by a specified expression tree.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable" /> that can evaluate the query represented by the specified expression tree.</returns>
      <param name="expression">The query expression from which the evaluation is performed.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Linq#IQueryProvider#Execute(System.Linq.Expressions.Expression)">
      <summary>Executes the query represented by a specified expression tree.</summary>
      <returns>The value that results from executing the specified query.</returns>
      <param name="expression">The query expression to execute.</param>
    </member>
    <member name="M:System.Data.Linq.Table`1.System#Linq#IQueryProvider#Execute``1(System.Linq.Expressions.Expression)">
      <summary>Executes the strongly-typed query represented by a specified expression tree.</summary>
      <returns>The value that results from executing the specified query.</returns>
      <param name="expression">The query expression to execute.</param>
      <typeparam name="TResult">The type of the data in the table.</typeparam>
    </member>
    <member name="M:System.Data.Linq.Table`1.ToString">
      <summary>Returns a string that represents the table.</summary>
      <returns>A string representation of the table.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Data.Linq.Mapping.AssociationAttribute">
      <summary>Designates a property to represent a database association, such as a foreign key relationship.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.AssociationAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.AssociationAttribute" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.AssociationAttribute.DeleteOnNull">
      <summary>When placed on a 1:1 association whose foreign key members are all non-nullable, deletes the object when the association is set to null.</summary>
      <returns>Setting to True deletes the object. The default value is False.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.AssociationAttribute.DeleteRule">
      <summary>Gets or sets delete behavior for an association.</summary>
      <returns>A string representing the rule.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.AssociationAttribute.IsForeignKey">
      <summary>Gets or sets the member as the foreign key in an association representing a database relationship.</summary>
      <returns>Default = false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.AssociationAttribute.IsUnique">
      <summary>Gets or sets the indication of a uniqueness constraint on the foreign key.</summary>
      <returns>Default = false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.AssociationAttribute.OtherKey">
      <summary>Gets or sets one or more members of the target entity class as key values on the other side of the association.</summary>
      <returns>Default = Id of the related class.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.AssociationAttribute.ThisKey">
      <summary>Gets or sets members of this entity class to represent the key values on this side of the association.</summary>
      <returns>Default = Id of the containing class.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.AttributeMappingSource">
      <summary>A mapping source that uses attributes on the context to create the mapping model.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.AttributeMappingSource.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.AttributeMappingSource" /> class.</summary>
    </member>
    <member name="T:System.Data.Linq.Mapping.AutoSync">
      <summary>Instructs the runtime how to retrieve the value after an insert or update operation.</summary>
    </member>
    <member name="F:System.Data.Linq.Mapping.AutoSync.Default">
      <summary>Automatically selects the value.</summary>
    </member>
    <member name="F:System.Data.Linq.Mapping.AutoSync.Always">
      <summary>Always returns the value.</summary>
    </member>
    <member name="F:System.Data.Linq.Mapping.AutoSync.Never">
      <summary>Never returns the value.</summary>
    </member>
    <member name="F:System.Data.Linq.Mapping.AutoSync.OnInsert">
      <summary>Returns the value only after an insert operation.</summary>
    </member>
    <member name="F:System.Data.Linq.Mapping.AutoSync.OnUpdate">
      <summary>Returns the value only after an update operation.</summary>
    </member>
    <member name="T:System.Data.Linq.Mapping.ColumnAttribute">
      <summary>Associates a class with a column in a database table.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.ColumnAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.ColumnAttribute" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.ColumnAttribute.AutoSync">
      <summary>Gets or sets the <see cref="T:System.Data.Linq.Mapping.AutoSync" /> enumeration.</summary>
      <returns>The <see cref="T:System.Data.Linq.Mapping.AutoSync" /> value.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.ColumnAttribute.CanBeNull">
      <summary>Gets or sets whether a column can contain null values.</summary>
      <returns>Default = true.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.ColumnAttribute.DbType">
      <summary>Gets or sets the type of the database column.</summary>
      <returns>See Remarks.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.ColumnAttribute.Expression">
      <summary>Gets or sets whether a column is a computed column in a database.</summary>
      <returns>Default = empty.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.ColumnAttribute.IsDbGenerated">
      <summary>Gets or sets whether a column contains values that the database auto-generates.</summary>
      <returns>Default = false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.ColumnAttribute.IsDiscriminator">
      <summary>Gets or sets whether a column contains a discriminator value for a LINQ to SQL inheritance hierarchy.</summary>
      <returns>Default = false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.ColumnAttribute.IsPrimaryKey">
      <summary>Gets or sets whether this class member represents a column that is part or all of the primary key of the table.</summary>
      <returns>Default = false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.ColumnAttribute.IsVersion">
      <summary>Gets or sets whether the column type of the member is a database timestamp or version number.</summary>
      <returns>Default value = false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.ColumnAttribute.UpdateCheck">
      <summary>Gets or sets how LINQ to SQL approaches the detection of optimistic concurrency conflicts.</summary>
      <returns>Default = <see cref="F:System.Data.Linq.Mapping.UpdateCheck.Always" />, unless <see cref="P:System.Data.Linq.Mapping.ColumnAttribute.IsVersion" /> is true for a member.Other values are <see cref="F:System.Data.Linq.Mapping.UpdateCheck.Never" /> and <see cref="F:System.Data.Linq.Mapping.UpdateCheck.WhenChanged" />.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.DataAttribute">
      <summary>Provides members to describe attributes of data in columns.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.DataAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.DataAttribute" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.DataAttribute.Name">
      <summary>Gets or sets the name of a column.</summary>
      <returns>The name.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.DataAttribute.Storage">
      <summary>Gets or sets a private storage field to hold the value from a column.</summary>
      <returns>The name of the storage field. </returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.DatabaseAttribute">
      <summary>Specifies certain attributes of a class that represents a database.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.DatabaseAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.DatabaseAttribute" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.DatabaseAttribute.Name">
      <summary>Gets or sets the name of the database.</summary>
      <returns>The name.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.FunctionAttribute">
      <summary>Associates a method with a stored procedure or user-defined function in the database.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.FunctionAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.FunctionAttribute" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.FunctionAttribute.IsComposable">
      <summary>Gets or sets whether a method is mapped to a function or to a stored procedure.</summary>
      <returns>true if a function; false if a stored procedure.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.FunctionAttribute.Name">
      <summary>Gets or sets the name of the function.</summary>
      <returns>The name of the function or stored procedure.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.InheritanceMappingAttribute">
      <summary>Maps an inheritance hierarchy in a LINQ to SQL application. </summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.InheritanceMappingAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.InheritanceMappingAttribute" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.InheritanceMappingAttribute.Code">
      <summary>Gets or sets the discriminator code value in a mapped inheritance hierarchy.</summary>
      <returns>Must be user-specified. There is no default value.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.InheritanceMappingAttribute.IsDefault">
      <summary>Gets or sets whether an object of this type in instantiated when the discriminator value does not match a specified value.</summary>
      <returns>Default = false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.InheritanceMappingAttribute.Type">
      <summary>Gets or sets the type of the class in the hierarchy.</summary>
      <returns>Must be user-specified. There is no default value.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.MappingSource">
      <summary>Represents a source for mapping information.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.MappingSource.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.MappingSource" /> class.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.MappingSource.CreateModel(System.Type)">
      <summary>Creates a new mapping model.</summary>
      <returns>The meta-model created to match the current mapping scheme.</returns>
      <param name="dataContextType">The type of <see cref="T:System.Data.Linq.DataContext" /> on which to base the mapping.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.MappingSource.GetModel(System.Type)">
      <summary>Returns the mapping model.</summary>
      <returns>The mapping model associated with this mapping source.</returns>
      <param name="dataContextType">The type of <see cref="T:System.Data.Linq.DataContext" /> of the model to be returned.</param>
    </member>
    <member name="T:System.Data.Linq.Mapping.MetaAccessor">
      <summary>Represents an accessor to a member.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaAccessor.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.MetaAccessor" /> class.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaAccessor.GetBoxedValue(System.Object)">
      <summary>Specifies an object on which to set a value or from which to get a value.</summary>
      <returns>The boxed value of this instance.</returns>
      <param name="instance">The instance from which to get the value or on which to set the value.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaAccessor.HasAssignedValue(System.Object)">
      <summary>Specifies whether the instance has an assigned value.</summary>
      <returns>true if the instance currently has an assigned value; otherwise, false.</returns>
      <param name="instance">The instance being looked at.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaAccessor.HasLoadedValue(System.Object)">
      <summary>Specifies whether the instance has a value loaded from a deferred source.</summary>
      <returns>true if the instance currently has a value loaded from a deferred source; otherwise, false. </returns>
      <param name="instance">The instance being looked at.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaAccessor.HasValue(System.Object)">
      <summary>Specifies whether the instance has a loaded or assigned value.</summary>
      <returns>true if the instance currently has a loaded or assigned value; otherwise, false. </returns>
      <param name="instance">The instance being looked at.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaAccessor.SetBoxedValue(System.Object@,System.Object)">
      <summary>Sets the value as an object.</summary>
      <param name="instance">The instance into which to set the value.</param>
      <param name="value">The value to set.</param>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAccessor.Type">
      <summary>Gets the type of the member accessed by this accessor.</summary>
      <returns>The type of the member.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.MetaAccessor`2">
      <summary>A strongly typed version of the <see cref="T:System.Data.Linq.Mapping.MetaAccessor" /> class.</summary>
      <typeparam name="TEntity">The type of the source.</typeparam>
      <typeparam name="TMember">The type of the member of that source.</typeparam>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaAccessor`2.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.MetaAccessor`2" /> class.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaAccessor`2.GetBoxedValue(System.Object)">
      <summary>Specifies an object on which to set a value or from which to get a value.</summary>
      <returns>The boxed value of this instance.</returns>
      <param name="instance">The instance from which to get the value or on which to set the value.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaAccessor`2.GetValue(`0)">
      <summary>Specifies the strongly typed value.</summary>
      <returns>The value of this instance.</returns>
      <param name="instance">The instance from which to get the value.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaAccessor`2.SetBoxedValue(System.Object@,System.Object)">
      <summary>Specifies an instance on which to set the boxed value.</summary>
      <param name="instance">The instance into which to set the boxed value.</param>
      <param name="value">The value to set.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaAccessor`2.SetValue(`0@,`1)">
      <summary>Specifies an instance on which to set the strongly typed value.</summary>
      <param name="instance">The instance into which to set the value.</param>
      <param name="value">The strongly typed value to set.</param>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAccessor`2.Type">
      <summary>Gets the type of the member accessed by this accessor. </summary>
      <returns>The member type.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.MetaAssociation">
      <summary>Represents an association relationship between two entity types.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaAssociation.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.MetaAssociation" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAssociation.DeleteOnNull">
      <summary>Gets whether the object should be deleted when the association is set to null.</summary>
      <returns>If true, the object is deleted when the association is set to null.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAssociation.DeleteRule">
      <summary>Gets the behavior when the child is deleted.</summary>
      <returns>The string representing the rule, or null if no action is specified on delete.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAssociation.IsForeignKey">
      <summary>Gets whether the other type is the parent of this type.</summary>
      <returns>Returns true is the other type is the parent of this type.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAssociation.IsMany">
      <summary>Gets whether the association represents a one-to-many relationship.</summary>
      <returns>Returns true if the association represents a one-to-many relationship.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAssociation.IsNullable">
      <summary>Gets whether the association can be null.</summary>
      <returns>Returns true if the association can be null.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAssociation.IsUnique">
      <summary>Gets whether the association is unique.</summary>
      <returns>Returns true if the association is unique.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAssociation.OtherKey">
      <summary>Gets a list of members that represents the values on the other side of the association.</summary>
      <returns>Returns a collection representing values on the other side of the association.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAssociation.OtherKeyIsPrimaryKey">
      <summary>Gets whether the <see cref="P:System.Data.Linq.Mapping.MetaAssociation.OtherKey" /> forms the identity of the other type.</summary>
      <returns>true if the <see cref="P:System.Data.Linq.Mapping.MetaAssociation.OtherKey" /> forms the identity (primary key) of the other type.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAssociation.OtherMember">
      <summary>Gets the member on the other side of this association that represents the reverse association.</summary>
      <returns>The member on the other side.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAssociation.OtherType">
      <summary>Gets the type on the other side of the association.</summary>
      <returns>The type.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAssociation.ThisKey">
      <summary>Gets a list of members representing the values on this side of the association.</summary>
      <returns>A collection.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAssociation.ThisKeyIsPrimaryKey">
      <summary>Gets whether <see cref="P:System.Data.Linq.Mapping.MetaAssociation.ThisKey" /> forms the identity of this type.</summary>
      <returns>true if <see cref="P:System.Data.Linq.Mapping.MetaAssociation.ThisKey" /> forms the identity (primary key) of the association.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaAssociation.ThisMember">
      <summary>Gets the member on this side that represents the association.</summary>
      <returns>The member.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.MetaDataMember">
      <summary>Represents the mapping between a field or a property of a domain object into a column of a database table.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaDataMember.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.MetaDataMember" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.Association">
      <summary>Gets the <see cref="T:System.Data.Linq.Mapping.MetaAssociation" /> that corresponds to this member.</summary>
      <returns>The corresponding <see cref="T:System.Data.Linq.Mapping.MetaAssociation" /> if one exists; otherwise, null.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.AutoSync">
      <summary>Gets the read-back behavior for this member for insert and update operations. </summary>
      <returns>An <see cref="T:System.Data.Linq.Mapping.AutoSync" /> enumeration.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.CanBeNull">
      <summary>Gets whether the value of this member can be assigned the null value.</summary>
      <returns>true if this member can be assigned the null value; otherwise, false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.DbType">
      <summary>Gets the type of the corresponding database column.</summary>
      <returns>The type of the database column as a string.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.DeclaringType">
      <summary>Gets the <see cref="T:System.Data.Linq.Mapping.MetaType" /> that contains this data member.</summary>
      <returns>The meta-type that contains the current member.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.DeferredSourceAccessor">
      <summary>Gets the accessor that is used to get and set the deferred source of this member.</summary>
      <returns>The accessor used to access the deferred source for this member.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.DeferredValueAccessor">
      <summary>Gets the accessor that is used to get and set the deferred value of this member (without causing fetch).</summary>
      <returns>The accessor used to access the deferred value for this member.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.Expression">
      <summary>Gets the expression that defines a computed column.</summary>
      <returns>The expression for the computed column as a string.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.IsAssociation">
      <summary>Gets whether this member defines an association relationship.</summary>
      <returns>true if this member defines an association relationship; otherwise, false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.IsDbGenerated">
      <summary>Gets whether this member is automatically generated by the database.</summary>
      <returns>true if this member is automatically generated by the database; otherwise false.</returns>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaDataMember.IsDeclaredBy(System.Data.Linq.Mapping.MetaType)">
      <summary>Specifies whether this member is declared by the specified type.</summary>
      <returns>true if this member is declared by the specified type; otherwise, false.</returns>
      <param name="type">The type to check.</param>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.IsDeferred">
      <summary>Gets whether the default behavior is to load this member on a deferred basis.</summary>
      <returns>true if this member loaded on a deferred basis by default; otherwise, false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.IsDiscriminator">
      <summary>Gets whether this member represents the inheritance discriminator.</summary>
      <returns>true if this member represents the inheritance discriminator; otherwise, false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.IsPersistent">
      <summary>Gets whether this member is mapped to a column (or constraint).</summary>
      <returns>true if this member is mapped to a column (or constraint); otherwise, false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.IsPrimaryKey">
      <summary>Gets whether this member is part of the type's identity.</summary>
      <returns>true if this member is part of the type's identity; otherwise, false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.IsVersion">
      <summary>Gets whether this member represents the row version or timestamp.</summary>
      <returns>true if this member represents the row version or timestamp; otherwise, false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.LoadMethod">
      <summary>Gets the <see cref="T:System.Data.Linq.DataContext" /> method that is used to perform load operations.</summary>
      <returns>The load method as <see cref="T:System.Reflection.MethodInfo" />.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.MappedName">
      <summary>Gets the name of the column (or constraint) in the database.</summary>
      <returns>The name of the column (or constraint) as a string.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.Member">
      <summary>Gets the underlying <see cref="T:System.Reflection.MemberInfo" />.</summary>
      <returns>Member attribute and metadata information.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.MemberAccessor">
      <summary>Gets the accessor that is used to get or set the value of this member.</summary>
      <returns>The accessor.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.Name">
      <summary>Gets the name of the member, same as the <see cref="T:System.Reflection.MemberInfo" /> name.</summary>
      <returns>The name, same as the <see cref="T:System.Reflection.MemberInfo" /> name.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.Ordinal">
      <summary>Gets the ordinal position of this member in the default layout of query results.</summary>
      <returns>The ordinal position.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.StorageAccessor">
      <summary>Gets the accessor that is used to get or set the storage value of this member.</summary>
      <returns>The accessor for the storage value of this member.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.StorageMember">
      <summary>Gets the member that stores the data for this member.</summary>
      <returns>The storage member.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.Type">
      <summary>Gets the type of this member.</summary>
      <returns>The type of the current member.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaDataMember.UpdateCheck">
      <summary>Gets the optimistic concurrency check policy for this member.</summary>
      <returns>An <see cref="T:System.Data.Linq.Mapping.UpdateCheck" /> enum.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.MetaFunction">
      <summary>Represents the mapping between a context method and a database function.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaFunction.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.MetaFunction" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaFunction.HasMultipleResults">
      <summary>Gets whether or not the stored procedure has multiple result types.</summary>
      <returns>true if the stored procedure has multiple result types.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaFunction.IsComposable">
      <summary>Gets whether the function can be composed within a query.</summary>
      <returns>true if the function can be composed within a query.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaFunction.MappedName">
      <summary>Gets the name of the database function or procedure.</summary>
      <returns>A string representing the name of the database function or procedure.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaFunction.Method">
      <summary>Gets the underlying context method.</summary>
      <returns>A <see cref="T:System.Reflection.MethodInfo" /> object that corresponds with the underlying context method.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaFunction.Model">
      <summary>Gets the <see cref="T:System.Data.Linq.Mapping.MetaModel" /> that contains this function.</summary>
      <returns>The <see cref="T:System.Data.Linq.Mapping.MetaModel" /> object that contains this function.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaFunction.Name">
      <summary>Gets the name of the method.</summary>
      <returns>A string that represents the name of the method.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaFunction.Parameters">
      <summary>Gets an enumeration of the function parameters.</summary>
      <returns>A collection of the parameters.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaFunction.ResultRowTypes">
      <summary>Gets the enumeration of possible result row types.</summary>
      <returns>A collection of possible types.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaFunction.ReturnParameter">
      <summary>Gets the return parameter.</summary>
      <returns>The <see cref="T:System.Data.Linq.Mapping.MetaParameter" /> that corresponds to the return parameter.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.MetaModel">
      <summary>An abstraction that represents the mapping between a database and domain objects.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaModel.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.MetaModel" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaModel.ContextType">
      <summary>Gets the type of <see cref="T:System.Data.Linq.DataContext" /> type that this model describes.</summary>
      <returns>The data context type.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaModel.DatabaseName">
      <summary>Gets the name of the database.</summary>
      <returns>The database name as a string.</returns>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaModel.GetFunction(System.Reflection.MethodInfo)">
      <summary>Gets the <see cref="T:System.Data.Linq.Mapping.MetaFunction" /> that corresponds to a database function.</summary>
      <returns>The meta-function that corresponds to a database function.</returns>
      <param name="method">The method defined on the <see cref="T:System.Data.Linq.DataContext" /> or subordinate class that represents the database function.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaModel.GetFunctions">
      <summary>Gets an enumeration of all functions.</summary>
      <returns>An enumeration that can be used to iterate through all functions.</returns>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaModel.GetMetaType(System.Type)">
      <summary>Discovers the <see cref="T:System.Data.Linq.Mapping.MetaType" /> for the specified <see cref="T:System.Type" />.</summary>
      <returns>A meta-type that corresponds to the specified type.</returns>
      <param name="type">The type for which the <see cref="T:System.Data.Linq.Mapping.MetaType" /> is sought.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaModel.GetTable(System.Type)">
      <summary>Gets the <see cref="T:System.Data.Linq.Mapping.MetaTable" /> associated with a specified <see cref="T:System.Type" />.</summary>
      <returns>A meta-table associated with the specified row type.</returns>
      <param name="rowType">The common language runtime (CLR) row type.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaModel.GetTables">
      <summary>Get an enumeration of all tables.</summary>
      <returns>An enumerator that can be used to iterate over the tables. </returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaModel.MappingSource">
      <summary>Gets the mapping source that originated this model.</summary>
      <returns>The originating mapping source.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaModel.ProviderType">
      <summary>Gets or sets the provider type.</summary>
      <returns>The provider type.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.MetaParameter">
      <summary>Represents the mapping between a method parameter and a database function parameter.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaParameter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.MetaParameter" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaParameter.DbType">
      <summary>Gets the database type of the parameter.</summary>
      <returns>The database type of the parameter as a string.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaParameter.MappedName">
      <summary>Gets the name of the parameter in the database function.</summary>
      <returns>The name as a string.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaParameter.Name">
      <summary>Gets the name of the parameter.</summary>
      <returns>The name of the parameter as a string.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaParameter.Parameter">
      <summary>Gets the underlying method parameter.</summary>
      <returns>The underlying method parameter.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaParameter.ParameterType">
      <summary>Gets the common language runtime (CLR) type of the parameter.</summary>
      <returns>The type.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.MetaTable">
      <summary>Represents an abstraction of a database table or view.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaTable.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.MetaTable" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaTable.DeleteMethod">
      <summary>Gets the <see cref="T:System.Data.Linq.DataContext" /> method that is used to perform delete operations.</summary>
      <returns>The <see cref="T:System.Reflection.MethodInfo" /> that corresponds to the method used for delete operations.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaTable.InsertMethod">
      <summary>Gets the <see cref="T:System.Data.Linq.DataContext" /> method that is used to perform insert operations.</summary>
      <returns>The <see cref="T:System.Reflection.MethodInfo" /> that corresponds to the method used for insert operations.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaTable.Model">
      <summary>Gets the <see cref="T:System.Data.Linq.Mapping.MetaModel" /> that contains this <see cref="T:System.Data.Linq.Mapping.MetaTable" />.</summary>
      <returns>The <see cref="T:System.Data.Linq.Mapping.MetaModel" /> that includes this MetaTable.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaTable.RowType">
      <summary>Gets the <see cref="T:System.Data.Linq.Mapping.MetaType" /> that describes the type of the rows of the table.</summary>
      <returns>The type of rows in the table.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaTable.TableName">
      <summary>Gets the name of the table as defined by the database.</summary>
      <returns>A string representing the name of the table.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaTable.UpdateMethod">
      <summary>Gets the <see cref="T:System.Data.Linq.DataContext" /> method that is used to perform update operations.</summary>
      <returns>The <see cref="T:System.Reflection.MethodInfo" /> that corresponds to the method used for update operations.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.MetaType">
      <summary>Represents the mapping of a domain object type to the columns of a database table.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaType.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.MetaType" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.Associations">
      <summary>Gets an enumeration of all the associations.</summary>
      <returns>A collection of associations.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.CanInstantiate">
      <summary>Gets whether the underlying type can be instantiated as the result of a query.</summary>
      <returns>true if the underlying type can be instantiated as the result of a query; otherwise, false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.DataMembers">
      <summary>Gets an enumeration of all the data members (fields and properties).</summary>
      <returns>A collection of the data members.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.DBGeneratedIdentityMember">
      <summary>Gets the member that represents the auto-generated identity column. </summary>
      <returns>The member that represents the auto-generated identity column, or null if there is no auto-generated identity column.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.DerivedTypes">
      <summary>Gets an enumeration of the immediate derived types in an inheritance hierarchy.</summary>
      <returns>An enumeration of meta-types.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.Discriminator">
      <summary>Gets the member that represents the inheritance discriminator column.</summary>
      <returns>The member that represents the inheritance discriminator column, or null if there is none.</returns>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaType.GetDataMember(System.Reflection.MemberInfo)">
      <summary>Gets the <see cref="T:System.Data.Linq.Mapping.MetaDataMember" /> associated with the specified member.</summary>
      <returns>The <see cref="T:System.Data.Linq.Mapping.MetaDataMember" /> if one is associated with the specified member; otherwise, null. </returns>
      <param name="member">The member for which the associated <see cref="T:System.Data.Linq.Mapping.MetaDataMember" /> is sought.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaType.GetInheritanceType(System.Type)">
      <summary>Gets the <see cref="T:System.Data.Linq.Mapping.MetaType" /> for an inheritance subtype.</summary>
      <returns>The <see cref="T:System.Data.Linq.Mapping.MetaType" /> for an inheritance subtype.</returns>
      <param name="type">The subtype.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.MetaType.GetTypeForInheritanceCode(System.Object)">
      <summary>Gets the meta-type associated with the specified inheritance code.</summary>
      <returns>The meta-type associated with the specified inheritance code.</returns>
      <param name="code">The inheritance code.</param>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.HasAnyLoadMethod">
      <summary>Gets a value that indicates whether the current <see cref="T:System.Data.Linq.Mapping.MetaType" /> or any of its bases types has an OnLoaded method.</summary>
      <returns>true if the meta-type or any base meta-type has an OnLoaded method; otherwise, false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.HasAnyValidateMethod">
      <summary>Gets a value that indicates whether the <see cref="T:System.Data.Linq.Mapping.MetaType" /> or any of its bases types has an OnValidate method.</summary>
      <returns>true if the meta-type or any base meta-type has an OnValidate method; otherwise, false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.HasInheritance">
      <summary>Gets a value indicating whether the type is part of a mapped inheritance hierarchy.</summary>
      <returns>true if the type is part of a mapped inheritance hierarchy; otherwise false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.HasInheritanceCode">
      <summary>Gets a value indicating whether this type defines an inheritance code.</summary>
      <returns>true if this type defines an inheritance code; otherwise false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.HasUpdateCheck">
      <summary>Gets a value indicating whether the type has any persistent member that may require a test for optimistic concurrency conflicts. </summary>
      <returns>true if the type has any persistent member with an <see cref="T:System.Data.Linq.Mapping.UpdateCheck" /> policy other than <see cref="F:System.Data.Linq.Mapping.UpdateCheck.Never" />; otherwise false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.IdentityMembers">
      <summary>Gets an enumeration of all the data members that define the unique identity of the type.</summary>
      <returns>An enumeration of members that define the unique identity of the type.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.InheritanceBase">
      <summary>Gets the base meta-type in the inheritance hierarchy.</summary>
      <returns>The base meta-type for the current inheritance hierarchy.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.InheritanceCode">
      <summary>Gets a value indicating whether this type defines an inheritance code.</summary>
      <returns>true if this type defines an inheritance code; otherwise false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.InheritanceDefault">
      <summary>Gets a value indicating whether this type is used as the default of an inheritance hierarchy.</summary>
      <returns>The metadata for the default type in the inheritance mapping.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.InheritanceRoot">
      <summary>Gets the root type of the inheritance hierarchy.</summary>
      <returns>The root type.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.InheritanceTypes">
      <summary>Gets a collection of all types that are defined by an inheritance hierarchy.</summary>
      <returns>A collection of meta-types in the current inheritance hierarchy.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.IsEntity">
      <summary>Gets a value indicating whether the <see cref="T:System.Data.Linq.Mapping.MetaType" /> is an entity type.</summary>
      <returns>true if the <see cref="T:System.Data.Linq.Mapping.MetaType" /> is an entity type; otherwise false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.IsInheritanceDefault">
      <summary>Gets a value indicating whether this type is used as the default of an inheritance hierarchy.</summary>
      <returns>true if this type is used as the default of an inheritance hierarchy; otherwise false.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.Model">
      <summary>Gets the <see cref="T:System.Data.Linq.Mapping.MetaModel" /> that contains this <see cref="T:System.Data.Linq.Mapping.MetaType" />.</summary>
      <returns>The containing meta-model.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.Name">
      <summary>Gets the name of the <see cref="T:System.Data.Linq.Mapping.MetaType" />.</summary>
      <returns>The name of the current meta-type.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.OnLoadedMethod">
      <summary>Gets information about the OnLoaded method contained by this meta-type.</summary>
      <returns>A description of the OnLoaded method for this meta-type.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.OnValidateMethod">
      <summary>Gets information about the OnValidate method contained by this meta-type.</summary>
      <returns>A description of the OnValidate method for this meta-type.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.PersistentDataMembers">
      <summary>Gets a collection of all the persistent data members.</summary>
      <returns>A collection of all the meta-data members in the current type.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.Table">
      <summary>Gets the <see cref="T:System.Data.Linq.Mapping.MetaTable" /> that uses this <see cref="T:System.Data.Linq.Mapping.MetaType" /> for row definition.</summary>
      <returns>A meta-table that uses the current meta-type for its row definition. </returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.Type">
      <summary>Gets the underlying common language runtime (CLR) type.</summary>
      <returns>The associated CLR type.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.MetaType.VersionMember">
      <summary>Gets a row-version or timestamp column for this <see cref="T:System.Data.Linq.Mapping.MetaType" />.</summary>
      <returns>The meta-data member that represents the row-version or timestamp column for this meta-type, or null if there is none.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.ParameterAttribute">
      <summary>Enables specification of mapping details for a stored procedure method parameter.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.ParameterAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.ParameterAttribute" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.ParameterAttribute.DbType">
      <summary>Gets or sets the type of the parameter for a provider-specific database.</summary>
      <returns>The type as a string.</returns>
    </member>
    <member name="P:System.Data.Linq.Mapping.ParameterAttribute.Name">
      <summary>Gets or sets the name of the parameter.</summary>
      <returns>The name as a string.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.ProviderAttribute">
      <summary>Specifies which database provider to use.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.ProviderAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.ProviderAttribute" /> class.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.ProviderAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.ProviderAttribute" /> class.</summary>
      <param name="type">The provider type to use to construct the <see cref="T:System.Data.Linq.Mapping.ProviderAttribute" />.</param>
    </member>
    <member name="P:System.Data.Linq.Mapping.ProviderAttribute.Type">
      <summary>Gets the type of the provider that is used to construct the <see cref="T:System.Data.Linq.Mapping.ProviderAttribute" />.</summary>
      <returns>The type of the provider.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.ResultTypeAttribute">
      <summary>Used to specify each type of result; for functions having various result types.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.ResultTypeAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.ResultTypeAttribute" /> class.</summary>
      <param name="type">The type of the result returned by a function having various result types.</param>
    </member>
    <member name="P:System.Data.Linq.Mapping.ResultTypeAttribute.Type">
      <summary>Gets the valid or expected type mapping for a function having various result types.</summary>
      <returns>The type of result (<see cref="T:System.Type" />).</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.TableAttribute">
      <summary>Designates a class as an entity class that is associated with a database table.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.TableAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.Mapping.TableAttribute" /> class.</summary>
    </member>
    <member name="P:System.Data.Linq.Mapping.TableAttribute.Name">
      <summary>Gets or sets the name of the table or view.</summary>
      <returns>By default, the value is the same as the name of the class.</returns>
    </member>
    <member name="T:System.Data.Linq.Mapping.UpdateCheck">
      <summary>Specifies when objects are to be tested for concurrency conflicts.</summary>
    </member>
    <member name="F:System.Data.Linq.Mapping.UpdateCheck.Always">
      <summary>Always check. This is the default unless <see cref="P:System.Data.Linq.Mapping.ColumnAttribute.IsVersion" /> is true for a member.</summary>
    </member>
    <member name="F:System.Data.Linq.Mapping.UpdateCheck.Never">
      <summary>Never check.</summary>
    </member>
    <member name="F:System.Data.Linq.Mapping.UpdateCheck.WhenChanged">
      <summary>Check only when the object has been changed.</summary>
    </member>
    <member name="T:System.Data.Linq.Mapping.XmlMappingSource">
      <summary>Represents a mapping source that uses an external XML mapping file to create the model.</summary>
    </member>
    <member name="M:System.Data.Linq.Mapping.XmlMappingSource.FromReader(System.Xml.XmlReader)">
      <summary>Creates a mapping source from an XML reader.</summary>
      <returns>The new XML mapping source, as type <see cref="T:System.Data.Linq.Mapping.XmlMappingSource" />.</returns>
      <param name="reader">An XML reader.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.XmlMappingSource.FromStream(System.IO.Stream)">
      <summary>Creates a mapping source from XML in a stream.</summary>
      <returns>The new XML mapping source, as type <see cref="T:System.Data.Linq.Mapping.XmlMappingSource" />.</returns>
      <param name="stream">A stream of XML.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.XmlMappingSource.FromUrl(System.String)">
      <summary>Creates a mapping source from XML that is loaded from a URL.</summary>
      <returns>The new XML mapping source, as type <see cref="T:System.Data.Linq.Mapping.XmlMappingSource" />.</returns>
      <param name="url">The URL pointing to the XML.</param>
    </member>
    <member name="M:System.Data.Linq.Mapping.XmlMappingSource.FromXml(System.String)">
      <summary>Creates a mapping source from an XML string. </summary>
      <returns>The new XML mapping source, as type <see cref="T:System.Data.Linq.Mapping.XmlMappingSource" />.</returns>
      <param name="xml">A string that contains XML.</param>
    </member>
    <member name="T:System.Data.Linq.SqlClient.Sql2000Provider">
      <summary>Contains functionality to connect to and communicate with a SQL Server 2000.</summary>
    </member>
    <member name="M:System.Data.Linq.SqlClient.Sql2000Provider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.SqlClient.Sql2000Provider" /> class. </summary>
    </member>
    <member name="T:System.Data.Linq.SqlClient.Sql2005Provider">
      <summary>Contains functionality to connect to and communicate with a SQL Server 2005.</summary>
    </member>
    <member name="M:System.Data.Linq.SqlClient.Sql2005Provider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.SqlClient.Sql2005Provider" /> class. </summary>
    </member>
    <member name="T:System.Data.Linq.SqlClient.Sql2008Provider">
      <summary>Contains functionality to connect to and communicate with a SQL Server 2008.  </summary>
    </member>
    <member name="M:System.Data.Linq.SqlClient.Sql2008Provider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.SqlClient.Sql2008Provider" /> class.</summary>
    </member>
    <member name="T:System.Data.Linq.SqlClient.SqlHelpers">
      <summary>Provides helper methods for operations that match string patterns.</summary>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlHelpers.GetStringContainsPattern(System.String,System.Char)">
      <summary>Creates a search pattern string where the specified text can have other text before and following it.</summary>
      <returns>A search pattern string that contains the specified string and the '%' character before and after it.</returns>
      <param name="text">The string to insert into the search pattern string.</param>
      <param name="escape">The character to use to escape wildcard characters.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlHelpers.GetStringEndsWithPattern(System.String,System.Char)">
      <summary>Creates a search pattern string where the specified text can have other text before it but not following it.</summary>
      <returns>A search pattern string that contains the '%' character followed by the specified string.</returns>
      <param name="text">The string to insert into the search pattern string.</param>
      <param name="escape">The character to use to escape wildcard characters.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlHelpers.GetStringStartsWithPattern(System.String,System.Char)">
      <summary>Creates a search pattern string where the specified text can have other text after it but not before it.</summary>
      <returns>A search pattern string that contains the specified string followed by the '%' character.</returns>
      <param name="text">The string to insert into the search pattern string.</param>
      <param name="escape">The character to use to escape wildcard characters.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlHelpers.TranslateVBLikePattern(System.String,System.Char)">
      <summary>Translates a search pattern for the Visual Basic Like operator to a search pattern for the SQL Server LIKE operator.</summary>
      <returns>A search pattern for the SQL Server LIKE operator that corresponds to the specified Visual Basic Like search pattern.</returns>
      <param name="pattern">The Visual Basic Like search pattern to translate to a SQL Server LIKE search pattern.</param>
      <param name="escape">The character to use to escape special SQL characters or the escape character itself.</param>
    </member>
    <member name="T:System.Data.Linq.SqlClient.SqlMethods">
      <summary>Provides methods that correspond to SQL Server functions. Methods in the <see cref="T:System.Data.Linq.SqlClient.SqlMethods" /> class are only supported in LINQ to SQL queries.</summary>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffDay(System.DateTime,System.DateTime)">
      <summary>Counts the number of day boundaries between two non-nullable dates.</summary>
      <returns>The number of day boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffDay(System.DateTimeOffset,System.DateTimeOffset)">
      <summary>Counts the number of day boundaries between two non-nullable dates.</summary>
      <returns>The number of day boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffDay(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
      <summary>Counts the number of day boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of day boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffDay(System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
      <summary>Counts the number of day boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of day boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffHour(System.DateTime,System.DateTime)">
      <summary>Counts the number of hour boundaries between two non-nullable dates.</summary>
      <returns>The number of hour boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffHour(System.DateTimeOffset,System.DateTimeOffset)">
      <summary>Counts the number of hour boundaries between two non-nullable dates.</summary>
      <returns>The number of hour boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffHour(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
      <summary>Counts the number of hour boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of hour boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffHour(System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
      <summary>Counts the number of hour boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of hour boundaries between the two specified dates. When one or both parameters are null, returns a null value is returned.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMicrosecond(System.DateTime,System.DateTime)">
      <summary>Counts the number of microsecond boundaries between two non-nullable dates.</summary>
      <returns>The number of microsecond boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMicrosecond(System.DateTimeOffset,System.DateTimeOffset)">
      <summary>Counts the number of microsecond boundaries between two non-nullable dates.</summary>
      <returns>The number of microsecond boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMicrosecond(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
      <summary>Counts the number of microsecond boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of microsecond boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMicrosecond(System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
      <summary>Counts the number of microsecond boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of microsecond boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMillisecond(System.DateTime,System.DateTime)">
      <summary>Counts the number of millisecond boundaries between two non-nullable dates.</summary>
      <returns>The number of millisecond boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMillisecond(System.DateTimeOffset,System.DateTimeOffset)">
      <summary>Counts the number of millisecond boundaries between two non-nullable dates.</summary>
      <returns>The number of millisecond boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMillisecond(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
      <summary>Counts the number of millisecond boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of millisecond boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMillisecond(System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
      <summary>Counts the number of millisecond boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of millisecond boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMinute(System.DateTime,System.DateTime)">
      <summary>Counts the number of minute boundaries between two non-nullable dates.</summary>
      <returns>The number of minute boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMinute(System.DateTimeOffset,System.DateTimeOffset)">
      <summary>Counts the number of minute boundaries between two non-nullable dates.</summary>
      <returns>The number of minute boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMinute(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
      <summary>Counts the number of minute boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of minute boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMinute(System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
      <summary>Counts the number of minute boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of minute boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMonth(System.DateTime,System.DateTime)">
      <summary>Counts the number of month boundaries between two non-nullable dates.</summary>
      <returns>The number of month boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMonth(System.DateTimeOffset,System.DateTimeOffset)">
      <summary>Counts the number of month boundaries between two non-nullable dates.</summary>
      <returns>The number of month boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMonth(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
      <summary>Counts the number of month boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of month boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffMonth(System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
      <summary>Counts the number of month boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of month boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffNanosecond(System.DateTime,System.DateTime)">
      <summary>Counts the number of nanosecond boundaries between two non-nullable dates.</summary>
      <returns>The number of nanosecond boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffNanosecond(System.DateTimeOffset,System.DateTimeOffset)">
      <summary>Counts the number of nanosecond boundaries between two non-nullable dates.</summary>
      <returns>The number of nanosecond boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffNanosecond(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
      <summary>Counts the number of nanosecond boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of nanosecond boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffNanosecond(System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
      <summary>Counts the number of nanosecond boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of nanosecond boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffSecond(System.DateTime,System.DateTime)">
      <summary>Counts the number of second boundaries between two non-nullable dates.</summary>
      <returns>The number of second boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffSecond(System.DateTimeOffset,System.DateTimeOffset)">
      <summary>Counts the number of second boundaries between two non-nullable dates.</summary>
      <returns>The number of second boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffSecond(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
      <summary>Counts the number of second boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of second boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffSecond(System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
      <summary>Counts the number of second boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of second boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffYear(System.DateTime,System.DateTime)">
      <summary>Counts the number of year boundaries between two non-nullable dates.</summary>
      <returns>The number of year boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffYear(System.DateTimeOffset,System.DateTimeOffset)">
      <summary>Counts the number of year boundaries between two non-nullable dates.</summary>
      <returns>The number of year boundaries between the two specified dates.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffYear(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
      <summary>Counts the number of year boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of year boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.DateDiffYear(System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
      <summary>Counts the number of year boundaries between two nullable dates.</summary>
      <returns>When both parameters are not null, returns the number of year boundaries between the two specified dates. When one or both parameters are null, returns a null value.</returns>
      <param name="startDate">The start date for the time period.</param>
      <param name="endDate">The end date for the time period.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.Like(System.String,System.String)">
      <summary>Determines whether a specific character string matches a specified pattern. This method is currently only supported in LINQ to SQL queries.</summary>
      <returns>true if <paramref name="matchExpression" /> matches the pattern; otherwise, false.</returns>
      <param name="matchExpression">The string to be searched for a match.</param>
      <param name="pattern">The pattern, which may include wildcard characters, to match in <paramref name="matchExpression" />.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlMethods.Like(System.String,System.String,System.Char)">
      <summary>Determines whether a specific character string matches a specified pattern. This method is currently only supported in LINQ to SQL queries.</summary>
      <returns>true if <paramref name="matchExpression" /> matches the pattern; otherwise, false.</returns>
      <param name="matchExpression">The string to be searched for a match.</param>
      <param name="pattern">The pattern, which may include wildcard characters, to match in <paramref name="matchExpression" />.</param>
      <param name="escapeCharacter">The character to put in front of a wildcard character to indicate that it should be interpreted as a regular character and not as a wildcard character.</param>
    </member>
    <member name="T:System.Data.Linq.SqlClient.SqlProvider">
      <summary>Contains functionality to connect to and communicate with a SQL Server.</summary>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.SqlClient.SqlProvider" /> class.</summary>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlProvider.Dispose">
      <summary>Releases managed references and closes connections opened by the <see cref="T:System.Data.Linq.SqlClient.SqlProvider" />.</summary>
    </member>
    <member name="M:System.Data.Linq.SqlClient.SqlProvider.Dispose(System.Boolean)">
      <summary>Optionally releases managed references and closes connections opened by the <see cref="T:System.Data.Linq.SqlClient.SqlProvider" />.</summary>
      <param name="disposing">true to release managed references and close connections; otherwise, false.</param>
    </member>
    <member name="T:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1">
      <summary>Defines methods for dynamically materializing objects.</summary>
      <typeparam name="TDataReader">The type of the data reader.</typeparam>
    </member>
    <member name="M:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1" /> class. </summary>
    </member>
    <member name="F:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.Arguments">
      <summary>Captures internal state for the fast materializer.</summary>
    </member>
    <member name="F:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.BufferReader">
      <summary>Represents a reader that reads data rows in a forward-only manner. </summary>
    </member>
    <member name="P:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.CanDeferLoad">
      <summary>When overridden in a derived class, gets a value that indicates whether deferred loading is enabled.</summary>
      <returns>true if deferred loading is enabled; otherwise, false.</returns>
    </member>
    <member name="M:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.Convert``1(System.Collections.IEnumerable)">
      <summary>Changes the type of each element in a specified sequence.</summary>
      <returns>A sequence that contains the type-converted elements.</returns>
      <param name="source">A sequence that contains elements to convert.</param>
      <typeparam name="TOutput">The type to convert the elements to.</typeparam>
    </member>
    <member name="M:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.CreateGroup``2(``0,System.Collections.Generic.IEnumerable{``1})">
      <summary>Creates a group from a specified key and collection of values.</summary>
      <returns>A group that has the specified key and the specified collection of values.</returns>
      <param name="key">The key for the group.</param>
      <param name="items">The values for the group.</param>
      <typeparam name="TKey">The type of the key of the group.</typeparam>
      <typeparam name="TElement">The type of the values in the group.</typeparam>
    </member>
    <member name="M:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.CreateOrderedEnumerable``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Creates an ordered sequence from a specified collection of values.</summary>
      <returns>An ordered sequence that contains the specified values.</returns>
      <param name="items">The values to put in the ordered sequence.</param>
      <typeparam name="TElement">The type of the values in the ordered sequence.</typeparam>
    </member>
    <member name="F:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.DataReader">
      <summary>Represents a data reader.</summary>
    </member>
    <member name="M:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.ErrorAssignmentToNull(System.Type)">
      <summary>Returns an exception that indicates that a null value was tried to be assigned to a non-nullable value type.</summary>
      <returns>An exception that indicates that a null value was attempted to be assigned to a non-nullable value type.</returns>
      <param name="type">The type to which a null value was attempted to be assigned.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.ExecuteSubQuery(System.Int32,System.Object[])">
      <summary>When overridden in a derived class, executes a query.</summary>
      <returns>The results from executing the query.</returns>
      <param name="iSubQuery">The index of the query.</param>
      <param name="args">The arguments to the query.</param>
    </member>
    <member name="M:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.GetLinkSource``1(System.Int32,System.Int32,System.Object[])">
      <summary>When overridden in a derived class, creates a new deferred source.</summary>
      <returns>An enumerable deferred source.</returns>
      <param name="globalLink">The index of the link.</param>
      <param name="localFactory">The index of the factory.</param>
      <param name="keyValues">The key values for the deferred source.</param>
      <typeparam name="T">The type of the result elements.</typeparam>
    </member>
    <member name="M:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.GetNestedLinkSource``1(System.Int32,System.Int32,System.Object)">
      <summary>When overridden in a derived class, creates a new deferred source.</summary>
      <returns>An enumerable deferred source.</returns>
      <param name="globalLink">The index of the link.</param>
      <param name="localFactory">The index of the factory.</param>
      <param name="instance">The instance for the deferred source.</param>
      <typeparam name="T">The type of the result elements.</typeparam>
    </member>
    <member name="F:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.Globals">
      <summary>Captures internal state for the fast materializer.</summary>
    </member>
    <member name="M:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.InsertLookup(System.Int32,System.Object)">
      <summary>When overridden in a derived class, inserts a value into a data structure.</summary>
      <returns>The value that was inserted into the data structure.</returns>
      <param name="globalMetaType">The index of the <see cref="T:System.Data.Linq.Mapping.MetaType" />.</param>
      <param name="instance">The object to insert into the data structure.</param>
    </member>
    <member name="F:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.Locals">
      <summary>Captures internal state for the fast materializer.</summary>
    </member>
    <member name="F:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.Ordinals">
      <summary>Represents column ordinals of a data reader.</summary>
    </member>
    <member name="M:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.Read">
      <summary>When overridden in a derived class, advances the reader to the next record.</summary>
      <returns>true if there are more rows; otherwise, false.</returns>
    </member>
    <member name="M:System.Data.Linq.SqlClient.Implementation.ObjectMaterializer`1.SendEntityMaterialized(System.Int32,System.Object)">
      <summary>When overridden in a derived class, invokes the method represented by <see cref="P:System.Data.Linq.Mapping.MetaType.OnLoadedMethod" />.</summary>
      <param name="globalMetaType">The index of the <see cref="T:System.Data.Linq.Mapping.MetaType" />.</param>
      <param name="instance">The parameter to pass to the invoked method.</param>
    </member>
  </members>
</doc>