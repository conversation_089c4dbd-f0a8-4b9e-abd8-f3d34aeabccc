﻿using System.Reflection;

namespace OcrMain
{
    [Obfuscation]
    public class OnnxParam
    {
        [Obfuscation]
        public int padding { get; set; }
        [Obfuscation]
        public int maxSideLen { get; set; }
        [Obfuscation]
        public float boxScoreThresh { get; set; }
        [Obfuscation]
        public float boxThresh { get; set; }
        [Obfuscation]
        public float unClipRatio { get; set; }
        [Obfuscation]
        public bool doAngle { get; set; }
        [Obfuscation]
        public bool mostAngle { get; set; }
        [Obfuscation]
        public bool isPaddle { get; set; }
    }
}
