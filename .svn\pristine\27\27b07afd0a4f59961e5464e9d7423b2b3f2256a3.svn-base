﻿using System;
using System.Diagnostics;
using System.Drawing;

namespace OCRTools
{
    internal class RectangleAnimation : BaseAnimation
    {
        public Rectangle FromRectangle { get; set; }
        public Rectangle ToRectangle { get; set; }
        public TimeSpan Duration { get; set; }

        public Rectangle CurrentRectangle { get; private set; }

        public override bool Update()
        {
            if (IsActive)
            {
                base.Update();

                var amount = (float) Timer.Elapsed.Ticks / Duration.Ticks;
                amount = Math.Min(amount, 1);

                var x = (int) MathHelpers.Lerp(FromRectangle.X, ToRectangle.X, amount);
                var y = (int) MathHelpers.Lerp(FromRectangle.Y, ToRectangle.Y, amount);
                var width = (int) MathHelpers.Lerp(FromRectangle.Width, ToRectangle.Width, amount);
                var height = (int) MathHelpers.Lerp(FromRectangle.Height, ToRectangle.Height, amount);

                CurrentRectangle = new Rectangle(x, y, width, height);

                if (amount >= 1)
                    //CurrentRectangle = ToRectangle;
                    Stop();
            }

            return IsActive;
        }
    }

    internal class BaseAnimation
    {
        protected TimeSpan previousElapsed;

        public BaseAnimation()
        {
            Timer = new Stopwatch();
        }

        public virtual bool IsActive { get; protected set; }

        protected Stopwatch Timer { get; }
        protected TimeSpan TotalElapsed { get; private set; }
        protected TimeSpan Elapsed { get; private set; }

        public virtual void Start()
        {
            IsActive = true;
            Timer.Restart();
        }

        public virtual void Stop()
        {
            Timer.Stop();
            IsActive = false;
        }

        public virtual bool Update()
        {
            if (IsActive)
            {
                TotalElapsed = Timer.Elapsed;
                Elapsed = TotalElapsed - previousElapsed;
                previousElapsed = TotalElapsed;
            }

            return IsActive;
        }
    }
}