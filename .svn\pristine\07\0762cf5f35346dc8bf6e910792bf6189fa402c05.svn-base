// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class RangeValuePattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = RangeValuePatternIdentifiers.Pattern;

        private IUIAutomationRangeValuePattern _pattern;


        private RangeValuePattern(AutomationElement el, IUIAutomationRangeValuePattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new RangeValuePattern(el, (IUIAutomationRangeValuePattern) pattern, cached);
        }
    }

    public class ValuePattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = ValuePatternIdentifiers.Pattern;
        public static readonly AutomationProperty IsReadOnlyProperty = ValuePatternIdentifiers.IsReadOnlyProperty;
        public static readonly AutomationProperty ValueProperty = ValuePatternIdentifiers.ValueProperty;

        private IUIAutomationValuePattern _pattern;


        private ValuePattern(AutomationElement el, IUIAutomationValuePattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new ValuePattern(el, (IUIAutomationValuePattern) pattern, cached);
        }
    }
}