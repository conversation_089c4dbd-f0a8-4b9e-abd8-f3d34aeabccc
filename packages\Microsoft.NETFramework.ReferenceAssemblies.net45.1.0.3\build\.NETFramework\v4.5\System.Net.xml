﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net</name>
  </assembly>
  <members>
    <member name="T:System.Net.INetworkProgress"></member>
    <member name="E:System.Net.INetworkProgress.ProgressChanged"></member>
    <member name="E:System.Net.INetworkProgress.ProgressCompleted"></member>
    <member name="E:System.Net.INetworkProgress.ProgressFailed"></member>
    <member name="T:System.Net.IPEndPointCollection">
      <summary>Represents a collection used to store network endpoints as <see cref="T:System.Net.IPEndPoint" /> objects. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.IPEndPointCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.IPEndPointCollection" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.IPEndPointCollection.InsertItem(System.Int32,System.Net.IPEndPoint)">
      <summary>Inserts an <see cref="T:System.Net.IPEndPoint" /> element into the <see cref="T:System.Net.IPEndPointCollection" /> at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="item" /> should be inserted.</param>
      <param name="item">The <see cref="T:System.Net.IPEndPoint" /> object to insert. The value can be null for reference types.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than zero -or- the <paramref name="index" /> parameter is greater than the current count of items in the <see cref="T:System.Net.IPEndPointCollection" />. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="item" /> parameter is null.</exception>
    </member>
    <member name="M:System.Net.IPEndPointCollection.SetItem(System.Int32,System.Net.IPEndPoint)">
      <summary>Replaces the <see cref="T:System.Net.IPEndPoint" /> element at the specified index.</summary>
      <param name="index">The zero-based index of the element to replace.</param>
      <param name="item">The new <see cref="T:System.Net.IPEndPoint" /> value for the element at the specified index. The value can be null for reference types.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than zero -or- the <paramref name="index" /> parameter is greater than the current count of items in the <see cref="T:System.Net.IPEndPointCollection" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="item" /> parameter is null.</exception>
    </member>
    <member name="T:System.Net.IUnsafeWebRequestCreate"></member>
    <member name="M:System.Net.IUnsafeWebRequestCreate.Create(System.Uri)"></member>
    <member name="T:System.Net.NetworkProgressChangedEventArgs"></member>
    <member name="M:System.Net.NetworkProgressChangedEventArgs.#ctor(System.Int32,System.Int32,System.Int32,System.Object)"></member>
    <member name="P:System.Net.NetworkProgressChangedEventArgs.ProcessedBytes"></member>
    <member name="P:System.Net.NetworkProgressChangedEventArgs.TotalBytes"></member>
    <member name="T:System.Net.UiSynchronizationContext"></member>
    <member name="P:System.Net.UiSynchronizationContext.Current"></member>
    <member name="P:System.Net.UiSynchronizationContext.ManagedUiThreadId"></member>
    <member name="M:System.Net.CookieCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:System.Net.CookieCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Net.CookieCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="P:System.Net.WebHeaderCollection.Item(System.String)"></member>
    <member name="M:System.Net.WebHeaderCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="T:System.Net.PeerToPeer.Cloud">
      <summary>Specifies the values that define a Peer <see cref="T:System.Net.PeerToPeer.Cloud" /> object.</summary>
    </member>
    <member name="M:System.Net.PeerToPeer.Cloud.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the PNRP <see cref="T:System.Net.PeerToPeer.Cloud" /> type. This populates a serialization information object with the data needed to serialize the Cloud object.</summary>
      <param name="info">Reference to the object that holds the data needed to deserialize this instance.</param>
      <param name="context">Context that provides the means for deserializing the data. Also referred to as the source of the serialized data.</param>
    </member>
    <member name="F:System.Net.PeerToPeer.Cloud.AllLinkLocal">
      <summary>Returns a reference to a <see cref="T:System.Net.PeerToPeer.Cloud" /> which represents all the link-local clouds in which the client or peer is currently participating.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Cloud.Available">
      <summary>Returns a static reference to a <see cref="T:System.Net.PeerToPeer.Cloud" /> which represents all the available clouds in which the client is currently participating.</summary>
    </member>
    <member name="M:System.Net.PeerToPeer.Cloud.Equals(System.Net.PeerToPeer.Cloud)">
      <summary>Performs a case-sensitive comparison between two cloud objects.  </summary>
      <returns>True if the <see cref="T:System.Net.PeerToPeer.Cloud" /> specified identifies the same resource as the current one, otherwise false. </returns>
      <param name="other">The cloud to compare with this <see cref="T:System.Net.PeerToPeer.Cloud" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Cloud.Equals(System.Object)">
      <summary>Determines whether the content of this peer <see cref="T:System.Net.PeerToPeer.Cloud" /> is equivalent to the content of a <see cref="N:System" /> object.  </summary>
      <returns>True if the <see cref="T:System.Net.PeerToPeer.PeerName" /> and the comparison object contain the same information; otherwise false. </returns>
      <param name="obj">The <see cref="T:System.Object" /> to compare with this <see cref="T:System.Net.PeerToPeer.Cloud" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Cloud.GetAvailableClouds">
      <summary>Obtains a collection of peer clouds known to the calling peer.  </summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.CloudCollection" /> object that specifies a collection of peer clouds known to the calling peer.  If no clouds are available, null is returned.</returns>
    </member>
    <member name="M:System.Net.PeerToPeer.Cloud.GetCloudByName(System.String)">
      <summary>Returns the <see cref="T:System.Net.PeerToPeer.Cloud" /> object with the specified cloud name.  </summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Cloud" /> object with the specified cloud name.</returns>
      <param name="cloudName">Contains the name of the PNRP <see cref="T:System.Net.PeerToPeer.Cloud" />.</param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Net.PeerToPeer.Cloud" /> name is not known.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Cloud.GetHashCode">
      <summary>Overrides <see cref="M:System.Object.GetHashCode" />.</summary>
      <returns>A hashcode for the current <see cref="T:System.Object" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Cloud.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="N:System.Runtime.Serialization" /> information object with the data needed to serialize the <see cref="T:System.Net.PeerToPeer.Cloud" />.</summary>
      <param name="info">Holds the serialized data associated with the <see cref="T:System.Net.PeerToPeer.Cloud" /> object.</param>
      <param name="context">Contains the destination for the serialized stream associated with the <see cref="T:System.Net.PeerToPeer.Cloud" /> object.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.Cloud.Global">
      <summary>Gets a <see cref="T:System.Net.PeerToPeer.Cloud" /> instance that contains globally (internet) scoped peers.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Cloud" /> which contains the peers that will communicate via the global network scope.</returns>
    </member>
    <member name="P:System.Net.PeerToPeer.Cloud.Name">
      <summary>Gets the name of the peer <see cref="T:System.Net.PeerToPeer.Cloud" />.  </summary>
      <returns>The name of the peer <see cref="T:System.Net.PeerToPeer.Cloud" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Cloud.Scope">
      <summary>Gets the network scope of the peer <see cref="T:System.Net.PeerToPeer.Cloud" />.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.PnrpScope" /> enumeration value that specifies the PNRP scope of the current peer cloud instance. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Cloud.ScopeId">
      <summary>Gets the identifier of a specific IP address for this peer <see cref="T:System.Net.PeerToPeer.Cloud" />.</summary>
      <returns>An integer value that specifies the scope-specific ID for this peer cloud.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Cloud.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Implements the <see cref="T:System.Runtime.Serialization.ISerializable" /> interface and returns the data needed to serialize the <see cref="T:System.Net.PeerToPeer.Cloud" /> instance. </summary>
      <param name="info">Holds the serialized data associated with the <see cref="T:System.Net.PeerToPeer.Cloud" /> object.</param>
      <param name="context">Contains the destination for the serialized stream associated with the <see cref="T:System.Net.PeerToPeer.Cloud" /> object.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Cloud.ToString">
      <summary>Returns a string representation of the current <see cref="T:System.Net.PeerToPeer.Cloud" />.</summary>
      <returns>A string that represents the current <see cref="T:System.Net.PeerToPeer.Cloud" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.CloudCollection">
      <summary>Represents a container for <see cref="T:System.Net.PeerToPeer.CloudCollection" /> elements. This class cannot be inherited. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.CloudCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.CloudCollection" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.CloudCollection.InsertItem(System.Int32,System.Net.PeerToPeer.Cloud)">
      <summary>Inserts a <see cref="T:System.Net.PeerToPeer.Cloud" /> into the <see cref="T:System.Net.PeerToPeer.CloudCollection" /> at the specified index.</summary>
      <param name="index">The zero-based index of the element to replace.</param>
      <param name="item">The value for the new element at the specified index.</param>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Net.PeerToPeer.Cloud" /> provided cannot be null.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.CloudCollection.SetItem(System.Int32,System.Net.PeerToPeer.Cloud)">
      <summary>Replaces the <see cref="T:System.Net.PeerToPeer.Cloud" /> at the specified index.</summary>
      <param name="index">The zero-based index of the element to replace.</param>
      <param name="item">The new value for the element to be replaced.</param>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Net.PeerToPeer.Cloud" /> provided cannot be null.</exception>
    </member>
    <member name="T:System.Net.PeerToPeer.PeerName">
      <summary>Specifies the values that define a peer-to-peer <see cref="T:System.Net.PeerToPeer.PeerName" /> object. A peer name is typically a string used to identify a peer resource. </summary>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerName.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with the data needed to serialize the <see cref="T:System.Net.PeerToPeer.PeerName" />.</summary>
      <param name="info">Reference to the object that holds the data needed to deserialize this instance.</param>
      <param name="context">Context that provides the means for deserializing the data. Also referred to as the source of the serialized data.</param>
      <exception cref="T:System.ArgumentNullException">One or more parameters are null.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerName.#ctor(System.String)">
      <summary>Initializes a new object of type <see cref="T:System.Net.PeerToPeer.PeerName" /> with the supplied fully qualified peer name <see cref="T:System.String" /> value.</summary>
      <param name="remotePeerName">Contains the peer name to encode as a <see cref="T:System.Net.PeerToPeer.PeerName" /> instance.</param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.String" /> provided is not a valid <see cref="T:System.Net.PeerToPeer.PeerName" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="remotePeerName" /> is null.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerName.#ctor(System.String,System.Net.PeerToPeer.PeerNameType)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.PeerName" /> class. </summary>
      <param name="classifier">
        <see cref="T:System.String" /> that contains the Peer Name to encode as a <see cref="T:System.Net.PeerToPeer.PeerName" />.</param>
      <param name="peerNameType">
        <see cref="T:System.Net.PeerToPeer.PeerNameType" /> enumeration value that specifies the type of peer name to create.</param>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Net.PeerToPeer.PeerName.Classifier" /> includes one or more illegal characters.</exception>
      <exception cref="T:System.ArgumentNullException">One or more parameters are null.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The Default Identity used as the basis of the <see cref="T:System.Net.PeerToPeer.PeerName" /> could not be retrieved.The <see cref="T:System.Net.PeerToPeer.PeerName" /> could not be created.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerName.Authority">
      <summary>Returns a string that specifies the <see cref="P:System.Net.PeerToPeer.PeerName.Authority" /> used by this <see cref="T:System.Net.PeerToPeer.PeerName" /> object.  </summary>
      <returns>The string which contains the authentication portion of the specified <see cref="T:System.Net.PeerToPeer.PeerName" />. For secured peer names, this property contains the public key as a forty-character hexadecimal string. For unsecured peer names, this property is set to zero (0).</returns>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerName.Classifier">
      <summary>Returns a string that contains the classifier for a peer-to-peer <see cref="T:System.Net.PeerToPeer.PeerName" />. </summary>
      <returns>The string which contains the classifier portion used to identify a peer name for <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> or resolution within a PNRP <see cref="T:System.Net.PeerToPeer.Cloud" />.Unless explicitly specified, the default value for all properties is null for reference types and zero (0) for properties of type int.</returns>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerName.CreateFromPeerHostName(System.String)">
      <summary>Creates a new instance of the specified <see cref="P:System.Net.PeerToPeer.PeerName.PeerHostName" /> object with the specified peer host name.</summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.PeerName" /> object that represents the specified peer host name.</returns>
      <param name="peerHostName">A string that contains the DNS-qualified host name.</param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.String" /> provided is not a valid peer host name string.</exception>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Net.PeerToPeer.PeerName.PeerHostName" />   is null.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">A <see cref="T:System.Net.PeerToPeer.PeerName" /> could not be created from the supplied <see cref="P:System.Net.PeerToPeer.PeerName.PeerHostName" /></exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerName.CreateRelativePeerName(System.Net.PeerToPeer.PeerName,System.String)">
      <summary>Creates a new <see cref="T:System.Net.PeerToPeer.PeerName" /> by replacing the <see cref="P:System.Net.PeerToPeer.PeerName.Classifier" /> field defined on the supplied <see cref="T:System.Net.PeerToPeer.PeerName" /> object with the specified classifier string value.</summary>
      <returns>The new <see cref="T:System.Net.PeerToPeer.PeerName" /> object that contains the updated classifier.</returns>
      <param name="peerName">The <see cref="T:System.Net.PeerToPeer.PeerName" /> object on which to set the new classifier string value.</param>
      <param name="classifier">The <see cref="P:System.Net.PeerToPeer.PeerName.Classifier" /> to set on the returned <see cref="T:System.Net.PeerToPeer.PeerName" />.</param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.String" /> provided is not a valid peer name classifier.</exception>
      <exception cref="T:System.ArgumentNullException">One or more parameters are null.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">A <see cref="T:System.Net.PeerToPeer.PeerName" /> could not be created from the supplied <see cref="P:System.Net.PeerToPeer.PeerName.PeerHostName" /></exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerName.Equals(System.Net.PeerToPeer.PeerName)">
      <summary>Performs a case-sensitive comparison of the current <see cref="T:System.Net.PeerToPeer.PeerName" /> and the specified peer name.</summary>
      <returns>Returns True if the specified <see cref="T:System.Net.PeerToPeer.PeerName" /> identifies the same resource as the current peer name object; otherwise this method returns False.This method also returns False if <paramref name="other" /> is set to null.</returns>
      <param name="other">The peer name to compare with this <see cref="T:System.Net.PeerToPeer.PeerName" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerName.Equals(System.Object)">
      <summary>Determines whether the content of this <see cref="T:System.Net.PeerToPeer.PeerName" /> is equal to the content of another object. </summary>
      <returns>True if the <see cref="T:System.Net.PeerToPeer.PeerName" /> and the comparison object contain the same information; otherwise false.</returns>
      <param name="obj">The <see cref="T:System.Object" /> to compare with the current <see cref="T:System.Net.PeerToPeer.PeerName" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerName.GetHashCode">
      <summary>Overrides the <see cref="M:System.Object.GetHashCode" /> method.</summary>
      <returns>A hashcode for the current <see cref="T:System.Object" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerName.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a serialization information object with the data needed to serialize the <see cref="T:System.Net.PeerToPeer.PeerName" />.</summary>
      <param name="info">Holds the serialized data associated with the <see cref="T:System.Net.PeerToPeer.PeerName" /> object.</param>
      <param name="context">Contains the destination for the serialized stream associated with the <see cref="T:System.Net.PeerToPeer.PeerName" /> object.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerName.IsSecured">
      <summary>Gets a Boolean value that specifies whether this is a secured peer name.</summary>
      <returns>If true, this peer name is secured with a private key/ public key pair. Its name contains the Secure Hash Algorithm (SHA) hash of the public key of the user certificate of that peer machine. Otherwise, if false, the peer name has no associated identity.Unless explicitly specified, the default value for all properties is null for reference types and zero (0) for properties of type int.</returns>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerName.PeerHostName">
      <summary>Gets the name of the peer-to-peer host. This is a DNS-encoded version of the <see cref="T:System.Net.PeerToPeer.PeerName" /> which is equivalent to a <see cref="P:System.Net.PeerToPeer.PeerName.PeerHostName" /> in that they are both identifiers.  The difference between the two is visual representation.</summary>
      <returns>A <see cref="T:System.String" /> value that is the name of the peer-to-peer host. Unless explicitly specified, the default value for all properties is null for reference types. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerName.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Implements the <see cref="T:System.Runtime.Serialization.ISerializable" /> interface and returns the data needed to serialize the <see cref="T:System.Net.PeerToPeer.PeerName" /> instance. </summary>
      <param name="info">Holds the serialized data associated with the <see cref="T:System.Net.PeerToPeer.PeerName" /> object.</param>
      <param name="context">Contains the destination for the serialized stream associated with the <see cref="T:System.Net.PeerToPeer.PeerName" /> object.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerName.ToString">
      <summary>Returns a string representation of the current <see cref="T:System.Net.PeerToPeer.PeerName" /> object.</summary>
      <returns>A string that represents the current <see cref="T:System.Net.PeerToPeer.PeerName" />, and specified in the following format: Authority.Classifier. For example, "0.MyInternetPeer".</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.PeerNameRecord">
      <summary>Defines the set of values that form a peer name record object. This record includes items such as the peer name and the collection of endpoints with which it communicates. Peer name records are used to define the individual peer nodes within a <see cref="T:System.Net.PeerToPeer.Cloud" />.</summary>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRecord.#ctor">
      <summary>Initializes a new default instance of the <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRecord.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new default instance of the <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> class.</summary>
      <param name="info">Reference to the object that holds the data needed to deserialize this instance.</param>
      <param name="context">Context that provides the means for deserializing the data. Also referred to as the source of the serialized data.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerNameRecord.Comment">
      <summary>Gets or sets additional information about the <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> object.</summary>
      <returns>The comment associated with the <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> object. The default value for this property is null. </returns>
      <exception cref="T:System.ArgumentException">The comment to set is either larger than 39 Unicode characters or less than one character.</exception>
      <exception cref="T:System.ArgumentNullException">The comment to set is null.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerNameRecord.Data">
      <summary>Gets or sets application-defined binary data for the <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> object.</summary>
      <returns>An array of bytes that holds the binary data associated with the entry. The default value for this property is an empty (zero-length) array instance.</returns>
      <exception cref="T:System.ArgumentException">The length of the binary data array to set is either greater than 4096 or less than 1.</exception>
      <exception cref="T:System.ArgumentNullException">The data to set is null.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerNameRecord.EndPointCollection">
      <summary>Gets an <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection" /> object that contains all the endpoints available to the peer associated with this <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> object. </summary>
      <returns>A <see cref="T:System.Net.IPEndPointCollection" /> object that contains a collection of <see cref="T:System.Net.IPEndPoint" /> objects. These objects contain the endpoints of other peers participating within the associated peer cloud. The default value for this property is null.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRecord.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a serialization information object with the data needed to serialize the <see cref="T:System.Net.PeerToPeer.PeerNameRecord" />.</summary>
      <param name="info">Holds the serialized data associated with the <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> object.</param>
      <param name="context">Contains the destination for the serialized stream associated with the <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> object.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerNameRecord.PeerName">
      <summary>Gets or sets the <see cref="T:System.Net.PeerToPeer.PeerName" /> within this <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> object. A peer name is a string used to identify a peer resource.</summary>
      <returns>The <see cref="P:System.Net.PeerToPeer.PeerNameRecord.PeerName" /> within this <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> object. The default value for this property is null.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRecord.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Implements the <see cref="T:System.Runtime.Serialization.ISerializable" /> interface and returns the data needed to serialize the <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> instance. </summary>
      <param name="info">Holds the serialized data associated with the <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> instance.</param>
      <param name="context">Contains the destination for the serialized stream associated with the <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> instance.</param>
    </member>
    <member name="T:System.Net.PeerToPeer.PeerNameRecordCollection">
      <summary>Represents a container for <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> elements. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRecordCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.PeerNameRecordCollection" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRecordCollection.InsertItem(System.Int32,System.Net.PeerToPeer.PeerNameRecord)">
      <summary>Inserts a <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> element into the <see cref="T:System.Net.PeerToPeer.PeerNameRecordCollection" /> at the specified index.  </summary>
      <param name="index">The zero-based index of the element to replace.</param>
      <param name="item">The value for the new element at the specified index.</param>
      <exception cref="T:System.ArgumentNullException">The item provided cannot be null.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRecordCollection.SetItem(System.Int32,System.Net.PeerToPeer.PeerNameRecord)">
      <summary>Replaces the <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> at the specified index.</summary>
      <param name="index">The zero-based index of the element to replace.</param>
      <param name="item">The new value for the element to be replaced.</param>
      <exception cref="T:System.ArgumentNullException">The item provided cannot be null.</exception>
    </member>
    <member name="T:System.Net.PeerToPeer.PeerNameRegistration">
      <summary>Registers a <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.PeerName" /> in a <see cref="T:System.Net.PeerToPeer.Cloud" /> or set of clouds.</summary>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRegistration.#ctor">
      <summary>Initializes a new default instance of the <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRegistration.#ctor(System.Net.PeerToPeer.PeerName,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> class with the specified name and port number.</summary>
      <param name="name">The <see cref="T:System.Net.PeerToPeer.PeerName" /> object to register.</param>
      <param name="port">Integer value that specifies the port number to register. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="name" /> parameter cannot be null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The specified port number is less than zero.  Port numbers must be greater than or equal to zero and less than 65,535 (0xFFFF).</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="T:System.Net.PeerToPeer.PeerName" /> specified has already been registered from this host.The remote peer does not own the <see cref="P:System.Net.PeerToPeer.PeerName.Authority" /> for the supplied <see cref="T:System.Net.PeerToPeer.PeerName" /> object.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRegistration.#ctor(System.Net.PeerToPeer.PeerName,System.Int32,System.Net.PeerToPeer.Cloud)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> class with the specified peer name, port number, and <see cref="T:System.Net.PeerToPeer.Cloud" />.</summary>
      <param name="name">The <see cref="T:System.Net.PeerToPeer.PeerName" /> object to register.</param>
      <param name="port">Integer value that specifies the port number to register.</param>
      <param name="cloud">
        <see cref="T:System.Net.PeerToPeer.Cloud" /> in which to register the peer name.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="name" /> parameter cannot be null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The specified port number is less than zero.  Port numbers must be greater than or equal to zero and less than 65,535 (0xFFFF).</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="T:System.Net.PeerToPeer.PeerName" /> specified has already been registered from this host.The remote peer does not own the <see cref="P:System.Net.PeerToPeer.PeerName.Authority" /> for the supplied <see cref="T:System.Net.PeerToPeer.PeerName" /> object.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRegistration.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new serializable <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> object.</summary>
      <param name="info">Reference to the object that holds the data needed to deserialize this instance.</param>
      <param name="context">Context that provides the means for deserializing the data. Also referred to as the source of the serialized data.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerNameRegistration.Cloud">
      <summary>Gets or sets information in a <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.Cloud" /> into which this <see cref="P:System.Net.PeerToPeer.PeerNameRecord.PeerName" /> object will be registered.</summary>
      <returns>An object of type <see cref="T:System.Net.PeerToPeer.Cloud" /> that specifies the peer cloud for which this registration is defined. This property is set to null by default.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerNameRegistration.Comment">
      <summary>Gets or sets additional information about the <see cref="T:System.Net.PeerToPeer.PeerName" /> object that will be registered with the <see cref="T:System.Net.PeerToPeer.Cloud" />.</summary>
      <returns>The comment that contains additional information about the <see cref="T:System.Net.PeerToPeer.PeerName" /> to associate with the <see cref="T:System.Net.PeerToPeer.Cloud" />. This property is set to null by default.</returns>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The specified string value is greater than 39 Unicode characters.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerNameRegistration.Data">
      <summary>Gets or sets application-defined binary data for the <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> object.</summary>
      <returns>An array of bytes that holds the binary data associated with the entry. This property is set to null by default.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The specified <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.Data" /> is greater than 4096 bytes.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRegistration.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> object.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRegistration.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources and optionally releases the managed resources used by the <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> object.</summary>
      <param name="disposing">True to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerNameRegistration.EndPointCollection">
      <summary>Gets the collection of network endpoints for which the associated peer name is registered.</summary>
      <returns>An <see cref="P:System.Net.PeerToPeer.PeerNameRecord.EndPointCollection" /> object that contains the network endpoints for which the associated peer name is registered. Unless explicitly specified, the default value for all properties is null for reference types.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRegistration.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a serialization information object with the data needed to serialize the <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> instance.</summary>
      <param name="info">Holds the serialized data associated with the <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> object.</param>
      <param name="context">Contains destination for the serialized stream associated with the <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> object.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRegistration.IsRegistered">
      <summary>Gets or sets whether the peer name specified in the <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.PeerName" /> property is registered with a specific <see cref="T:System.Net.PeerToPeer.Cloud" /> on a host.</summary>
      <returns>If true, the peer name is registered with a <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.Cloud" /> for the peer host. If false, then the registration process can be started with <see cref="M:System.Net.PeerToPeer.PeerNameRegistration.Start" />.</returns>
      <exception cref="T:System.ObjectDisposedException">An object that has been disposed already cannot be registered.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerNameRegistration.PeerName">
      <summary>Gets or sets the peer name to register with a peer cloud.</summary>
      <returns>An object of type <see cref="T:System.Net.PeerToPeer.PeerName" /> that contains values associated with this <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> object. Unless explicitly specified, the default value for all properties is null for reference types.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerNameRegistration.Port">
      <summary>Gets or sets the TCP/IP port number used by the peer being registered into the <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> object.</summary>
      <returns>An integer value indicating the TCP port number of the <see cref="T:System.Net.IPEndPoint" />. Unless explicitly specified, the default value for this property is zero (0).</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The specified port value is less than zero.  Port numbers must be greater than or equal to zero and less than 65,535 (0xFFFF).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRegistration.Start">
      <summary>Registers the <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.PeerName" /> into the <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.Cloud" />. If no <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.EndPointCollection" /> is specified, automatic address selection is used with the port value specified by the <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.Port" /> property.</summary>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.PeerName" /> property is set to null.</exception>
      <exception cref="T:System.ObjectDisposedException">This object had Dispose() called on it previously.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="T:System.Net.PeerToPeer.PeerName" /> specified has already been registered. The <see cref="M:System.Net.PeerToPeer.PeerNameRegistration.Update" /> method must be used to update a registration. The remote peer does not own the <see cref="P:System.Net.PeerToPeer.PeerName.Authority" /> for the <see cref="T:System.Net.PeerToPeer.PeerName" /> specified.Either the <see cref="T:System.Net.PeerToPeer.PeerName" /> or the <see cref="P:System.Net.PeerToPeer.PeerNameRecord.Data" /> is not specified; at least one needs to be provided.The message or data elements are invalid. Or, <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.UseAutoEndPointSelection" /> is not set and no data blob or <see cref="T:System.Net.IPEndPoint" /> is specified.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRegistration.Stop">
      <summary>Unregisters the peer name specified in the <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.PeerName" /> property from all the clouds in which it was registered.</summary>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.PeerName" /> property has not yet been registered. This occurs when a <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> object is constructed using the empty constructor. The <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.PeerName" /> property must be populated in this instance prior to calling <see cref="M:System.Net.PeerToPeer.PeerNameRegistration.Start" />.</exception>
      <exception cref="T:System.ObjectDisposedException">This object had Dispose() called on it previously.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRegistration.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Implements the <see cref="T:System.Runtime.Serialization.ISerializable" /> interface and returns the data needed to serialize the <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> instance.</summary>
      <param name="info">Holds the serialized data associated with the <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> instance.</param>
      <param name="context">Contains the destination for the serialized stream associated with the <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> instance.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameRegistration.Update">
      <summary>Updates the <see cref="T:System.Net.PeerToPeer.PeerNameRegistration" /> for a node registered with a specific <see cref="T:System.Net.PeerToPeer.Cloud" />. Update is performed using the information specified in the properties.</summary>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.PeerName" /> property is set to null.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.PeerName" /> property has not yet been registered. The <see cref="M:System.Net.PeerToPeer.PeerNameRegistration.Update" /> method cannot be called until the peer name specified in the <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.PeerName" /> property is registered in one or more clouds.The peer name specified in the <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.PeerName" /> property has changed since the corresponding <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> was registered.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">An object that has been disposed cannot be registered.</exception>
      <exception cref="T:System.ArgumentNullException">A <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> property is set to null.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.PeerNameRegistration.UseAutoEndPointSelection">
      <summary>Gets or sets a value that specifies whether to use automatic endpoint selection when traversing a peer mesh or <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.Cloud" />. </summary>
      <returns>True if automatic endpoint selection is to be used; false if some other method will be used to determine an endpoint. The default value is true. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.PeerNameResolver">
      <summary>Specifies the values that resolve a <see cref="T:System.Net.PeerToPeer.PeerName" /> to a <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> using the PNRP Namespace Provider API protocol.</summary>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameResolver.#ctor">
      <summary>Initializes a new default instance of the <see cref="T:System.Net.PeerToPeer.PeerNameResolver" /> class.</summary>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameResolver.OnResolveCompleted(System.Net.PeerToPeer.ResolveCompletedEventArgs)">
      <summary>Returns peer resolution data after the <see cref="E:System.Net.PeerToPeer.PeerNameResolver.ResolveCompleted" /> event is raised.</summary>
      <param name="e">The <see cref="T:System.Net.PeerToPeer.ResolveCompletedEventArgs" /> object that contains the data returned by the <see cref="E:System.Net.PeerToPeer.PeerNameResolver.ResolveCompleted" /> event.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameResolver.OnResolveProgressChanged(System.Net.PeerToPeer.ResolveProgressChangedEventArgs)">
      <summary>Returns peer resolution progress data when the <see cref="E:System.Net.PeerToPeer.PeerNameResolver.ResolveProgressChanged" /> event is raised.</summary>
      <param name="e">
        <see cref="T:System.Net.PeerToPeer.ResolveProgressChangedEventArgs" /> object that contains peer name resolution progress information returned by the <see cref="E:System.Net.PeerToPeer.PeerNameResolver.ResolveProgressChanged" /> event.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameResolver.Resolve(System.Net.PeerToPeer.PeerName)">
      <summary>Resolves the specified <see cref="T:System.Net.PeerToPeer.PeerName" /> in all clouds known to the calling peer.</summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.PeerNameRecordCollection" /> that contains all peer name records (represented as <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> objects) associated with the specified peer name. For unsecured peer names, the same <see cref="T:System.Net.PeerToPeer.PeerName" /> can be registered by different users in the same <see cref="T:System.Net.PeerToPeer.Cloud" />, and associated with different endpoints.</returns>
      <param name="peerName">The <see cref="T:System.Net.PeerToPeer.PeerName" /> to resolve.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="peerName" /> parameter is set to null.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The peer name specified cannot be resolved. </exception>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameResolver.Resolve(System.Net.PeerToPeer.PeerName,System.Int32)">
      <summary>Resolves the specified peer name in all clouds known to the calling peer, returning no more than the specified number of <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> objects.</summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.PeerNameRecordCollection" /> whose size is less than or equal to <paramref name="maxRecords" /> entries. This collection contains peer name records associated with the <see cref="T:System.Net.PeerToPeer.PeerName" /> that was resolved.</returns>
      <param name="peerName">The <see cref="T:System.Net.PeerToPeer.PeerName" /> to resolve.</param>
      <param name="maxRecords">The maximum number of <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> objects to obtain from all <see cref="T:System.Net.PeerToPeer.Cloud" /> objects for the supplied <paramref name="peerName" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="peerName" /> parameter is set to null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="maxRecords" /> parameter is less than or equal to zero.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The supplied peer name cannot be resolved.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameResolver.Resolve(System.Net.PeerToPeer.PeerName,System.Net.PeerToPeer.Cloud)">
      <summary>Resolves the specified <see cref="P:System.Net.PeerToPeer.PeerNameRecord.PeerName" /> in the specified <see cref="T:System.Net.PeerToPeer.Cloud" />.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.PeerNameRecordCollection" /> that contains all peer name records (represented as <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> objects) associated with the specified peer name. For unsecured peer names, the same <see cref="T:System.Net.PeerToPeer.PeerName" /> can be registered by different users in the same <see cref="T:System.Net.PeerToPeer.Cloud" />, and associated with different endpoints.</returns>
      <param name="peerName">The <see cref="T:System.Net.PeerToPeer.PeerName" /> to resolve.</param>
      <param name="cloud">The <see cref="T:System.Net.PeerToPeer.Cloud" /> in which to resolve the peer name.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="peerName" /> parameter is set to null.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The supplied peer name cannot be resolved. </exception>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameResolver.Resolve(System.Net.PeerToPeer.PeerName,System.Net.PeerToPeer.Cloud,System.Int32)">
      <summary>Resolves the specified <see cref="P:System.Net.PeerToPeer.PeerNameRecord.PeerName" /> in the specified <see cref="T:System.Net.PeerToPeer.Cloud" />, returning no more than the specified number of <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> objects. </summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.PeerNameRecordCollection" /> whose size is less than or equal to <paramref name="maxRecords" /> entries. This collection contains peer name records associated with the <see cref="T:System.Net.PeerToPeer.PeerName" /> that was resolved.</returns>
      <param name="peerName">The <see cref="T:System.Net.PeerToPeer.PeerName" /> to resolve.</param>
      <param name="cloud">The <see cref="T:System.Net.PeerToPeer.Cloud" /> in which to resolve the <paramref name="peerName" />.</param>
      <param name="maxRecords">The maximum number of peer name record objects to obtain from the specified cloud for the specified <paramref name="peerName" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="peerName" /> parameter is set to null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="maxRecords" /> parameter is less than or equal to zero.  </exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The supplied peer name cannot be resolved.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameResolver.ResolveAsync(System.Net.PeerToPeer.PeerName,System.Int32,System.Object)">
      <summary>Begins an asynchronous peer name resolution operation for the specified <see cref="T:System.Net.PeerToPeer.PeerName" /> in all clouds known to the calling peer, returning no more than <paramref name="maxRecords" /> entries for the peer name.</summary>
      <param name="peerName">The <see cref="T:System.Net.PeerToPeer.PeerName" /> to resolve.</param>
      <param name="maxRecords">The maximum number of records to obtain for the Peer Name.</param>
      <param name="userState">A user-defined object that contains information about the resolve operation.</param>
      <exception cref="T:System.ArgumentNullException">One or both of the <paramref name="peerName" /> and <paramref name="userState" /> parameters are set to null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="maxRecords" /> parameter is less than or equal to zero.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameResolver.ResolveAsync(System.Net.PeerToPeer.PeerName,System.Net.PeerToPeer.Cloud,System.Int32,System.Object)">
      <summary>Begins an asynchronous peer name resolution operation for the specified <see cref="T:System.Net.PeerToPeer.PeerName" /> in the specified <see cref="T:System.Net.PeerToPeer.Cloud" />.  The resolution operation will resolve no more than <paramref name="maxRecords" /> entries for the specified peer name.</summary>
      <param name="peerName">The <paramref name="peerName" /> to resolve.</param>
      <param name="cloud">The <paramref name="cloud" /> in which to resolve <paramref name="peerName" />.</param>
      <param name="maxRecords">The maximum number of records to obtain from <paramref name="cloud" /> for <paramref name="peerName" />.</param>
      <param name="userState">A user-defined object that contains information about the peer name resolution operation. </param>
      <exception cref="T:System.ArgumentNullException">One or both of the <paramref name="peerName" /> and <paramref name="userState" /> parameters are set to null.</exception>
      <exception cref="T:System.ArgumentException">One or more supplied parameters are invalid.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Successful completion of this operation requires at least one event handler.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="maxRecords" /> parameter is less than or equal to zero.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameResolver.ResolveAsync(System.Net.PeerToPeer.PeerName,System.Net.PeerToPeer.Cloud,System.Object)">
      <summary>Begins an asynchronous peer name resolution operation for the specified <see cref="T:System.Net.PeerToPeer.PeerName" /> in the specified <see cref="T:System.Net.PeerToPeer.Cloud" />.</summary>
      <param name="peerName">The <see cref="T:System.Net.PeerToPeer.PeerName" /> to resolve.</param>
      <param name="cloud">The <see cref="T:System.Net.PeerToPeer.Cloud" /> in which to resolve the <paramref name="peerName" />.</param>
      <param name="userState">A user-defined <see cref="T:System.Object" /> that contains information about the peer name resolution operation.</param>
      <exception cref="T:System.ArgumentNullException">One or both of the <paramref name="peerName" /> and <paramref name="userState" /> parameters are set to null.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameResolver.ResolveAsync(System.Net.PeerToPeer.PeerName,System.Object)">
      <summary>Begins an asynchronous peer name resolution operation for the specified <see cref="P:System.Net.PeerToPeer.PeerNameRecord.PeerName" /> in all clouds known to the calling peer.</summary>
      <param name="peerName">The <see cref="T:System.Net.PeerToPeer.PeerName" /> to resolve.</param>
      <param name="userState">A user-defined object that contains state information about the peer name resolution operation.</param>
      <exception cref="T:System.ArgumentNullException">One or both of the <paramref name="peerName" /> and <paramref name="userState" /> parameters are set to null.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerNameResolver.ResolveAsyncCancel(System.Object)">
      <summary>Cancels the specified asynchronous peer name resolution request. </summary>
      <param name="userState">The object provided to the <see cref="Overload:System.Net.PeerToPeer.PeerNameResolver.ResolveAsync" /> method instance which started the resolve operation. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="userState" /> parameters cannot be null.</exception>
    </member>
    <member name="E:System.Net.PeerToPeer.PeerNameResolver.ResolveCompleted">
      <summary>The <see cref="E:System.Net.PeerToPeer.PeerNameResolver.ResolveCompleted" /> event is signaled when a peer name resolution request for a specific <see cref="P:System.Net.PeerToPeer.PeerNameRegistration.PeerName" /> has completed.  </summary>
    </member>
    <member name="E:System.Net.PeerToPeer.PeerNameResolver.ResolveProgressChanged">
      <summary>This event is signaled whenever a <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> object is found in response to a <see cref="Overload:System.Net.PeerToPeer.PeerNameResolver.ResolveAsync" /> operation for a specific <see cref="T:System.Net.PeerToPeer.PeerName" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.PeerNameType">
      <summary>Defines the type of <see cref="T:System.Net.PeerToPeer.PeerName" /> to create. A peer name is either secured or unsecured. A secured peer name provides a proof of ownership of the name. An unsecured peer name has no identity associated.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Net.PeerToPeer.PeerNameType.Secured">
      <summary>Create a secured <see cref="T:System.Net.PeerToPeer.PeerName" />  using the identity of current user.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.PeerNameType.Unsecured">
      <summary>Create an unsecured <see cref="T:System.Net.PeerToPeer.PeerName" />  using the identity of current user.</summary>
    </member>
    <member name="T:System.Net.PeerToPeer.PeerToPeerException">
      <summary>Represents the exceptions that are thrown when an error is raised by the Peer-to-Peer Infrastructure.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerToPeerException.#ctor">
      <summary>Initializes a new default instance of the <see cref="T:System.Net.PeerToPeer.PeerToPeerException" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerToPeerException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.PeerToPeerException" /> class with serialized data.</summary>
      <param name="info">Reference to the object that holds the data needed to deserialize the object.</param>
      <param name="context">Context that provides the means for deserializing the <see cref="T:System.Net.PeerToPeer.PeerToPeerException" /> data. Also referred to as the source of the serialized data.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerToPeerException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.PeerToPeerException" /> class with the supplied message string.</summary>
      <param name="message">The error message that provides the reason for the exception.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerToPeerException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.PeerToPeerException" /> class with the supplied message string and exception. </summary>
      <param name="message">The error message that explains the reason for the <see cref="T:System.Net.PeerToPeer.PeerToPeerException" />.</param>
      <param name="innerException">The exception instance that caused the current <see cref="T:System.Exception" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerToPeerException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Implements the <see cref="T:System.Runtime.Serialization.ISerializable" /> interface and returns the data needed to serialize the peer object.</summary>
      <param name="info">Contains the information required to serialize the <see cref="T:System.Net.PeerToPeer.PeerToPeerException" /> object.</param>
      <param name="context">Contains the destination for the serialized stream associated with the <see cref="T:System.Net.PeerToPeer.PeerToPeerException" /> object.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PeerToPeerException.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Implements the <see cref="T:System.Runtime.Serialization.ISerializable" /> interface and returns the data needed to serialize the <see cref="T:System.Net.PeerToPeer.PeerToPeerException" /> instance. </summary>
      <param name="info">Contains the information required to serialize the <see cref="T:System.Net.PeerToPeer.PeerToPeerException" /> instance.</param>
      <param name="context">Contains the destination for the serialized stream associated with the <see cref="T:System.Net.PeerToPeer.PeerToPeerException" /> instance.</param>
    </member>
    <member name="T:System.Net.PeerToPeer.PnrpPermission">
      <summary>Specifies the values that are used in <see cref="N:System.Net.PeerToPeer" /> object permissions. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PnrpPermission.#ctor(System.Security.Permissions.PermissionState)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.PnrpPermission" /> class with the supplied initial permission state.</summary>
      <param name="state">One of the values in the <see cref="T:System.Security.Permissions.PermissionState" /> enumeration.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PnrpPermission.Copy">
      <summary>Creates and returns an identical copy of the current <see cref="T:System.Net.PeerToPeer.PnrpPermission" />.</summary>
      <returns>An object with an IPermission interface, whose instance contains a copy of the current instance of <see cref="T:System.Net.PeerToPeer.PnrpPermission" />. </returns>
      <exception cref="T:System.ArgumentException">The parameter is not a valid <see cref="T:System.Net.PeerToPeer.PnrpPermission" /> element.The parameter's version number is not supported. </exception>
      <exception cref="T:System.ArgumentNullException">The parameter is a null reference (Nothing in Visual Basic). </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PnrpPermission.FromXml(System.Security.SecurityElement)">
      <summary>Reconstructs a security object with a specified state from an XML encoding.</summary>
      <param name="e">The XML encoding to use to reconstruct the permission. </param>
      <exception cref="T:System.ArgumentException">The parameter is not a valid <see cref="T:System.Net.PeerToPeer.PnrpPermission" /> element.The parameter's version number is not supported. </exception>
      <exception cref="T:System.ArgumentNullException">The parameter is a null reference (Nothing in Visual Basic). </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PnrpPermission.Intersect(System.Security.IPermission)">
      <summary>Creates and returns a permission that is the intersection of the current <see cref="T:System.Net.PeerToPeer.PnrpPermission" /> and the specified permission.</summary>
      <returns>A new permission that represents the intersection of the current <see cref="T:System.Net.PeerToPeer.PnrpPermission" /> and the specified permission. This new permission is a null reference (Nothing in Visual Basic) if the intersection is empty. </returns>
      <param name="target">A permission to <see cref="M:System.Net.PeerToPeer.PnrpPermission.Intersect(System.Security.IPermission)" /> with the current permission. It must be of the same type as the current permission.</param>
      <exception cref="T:System.ArgumentException">The target parameter is not a null reference (Nothing in Visual Basic) and is not an instance of the same class as the current permission. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PnrpPermission.IsSubsetOf(System.Security.IPermission)">
      <summary>Determines whether the current <see cref="T:System.Net.PeerToPeer.PnrpPermission" /> is a subset of the specified permission.</summary>
      <returns>True if the current <see cref="T:System.Net.PeerToPeer.PnrpPermission" /> is a subset of the specified permission; otherwise, false. </returns>
      <param name="target">A permission that is to be tested for the subset relationship. This permission must be of the same type as the current permission. </param>
      <exception cref="T:System.ArgumentException">The target parameter is not a null reference (Nothing in Visual Basic) and is not an instance of the same class as the current permission. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PnrpPermission.IsUnrestricted">
      <summary>Returns a value specifying whether the current <see cref="T:System.Net.PeerToPeer.PnrpPermission" /> is unrestricted.</summary>
      <returns>True if the current permission is unrestricted; otherwise, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PnrpPermission.ToXml">
      <summary>Creates an XML encoding of the <see cref="T:System.Net.PeerToPeer.PnrpPermission" /> and its current state.</summary>
      <returns>A <see cref="T:System.Security.SecurityElement" /> object that contains an XML encoding of the permission, including any state information. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PnrpPermission.Union(System.Security.IPermission)">
      <summary>Creates a permission that is the union of the current <see cref="T:System.Net.PeerToPeer.PnrpPermission" /> and the specified permission.</summary>
      <returns>A new permission that represents the <see cref="M:System.Net.PeerToPeer.PnrpPermission.Union(System.Security.IPermission)" /> of the current <see cref="T:System.Net.PeerToPeer.PnrpPermission" /> and the specified permission.</returns>
      <param name="target">A permission to combine with the current permission. It must be of the same type as the current permission.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="target" /> parameter is invalid.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.PnrpPermissionAttribute">
      <summary>Allows security actions for <see cref="T:System.Net.PeerToPeer.PnrpPermission" /> to be applied to code using declarative security.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PnrpPermissionAttribute.#ctor(System.Security.Permissions.SecurityAction)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.PnrpPermissionAttribute" /> class with the specified <see cref="T:System.Security.Permissions.SecurityAction" />.</summary>
      <param name="action">One of the <see cref="T:System.Security.Permissions.SecurityAction" /> values.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.PnrpPermissionAttribute.CreatePermission">
      <summary>Creates and returns a new <see cref="T:System.Security.IPermission" />.</summary>
      <returns>A new <see cref="T:System.Security.IPermission" /> object.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.PnrpScope">
      <summary>Specifies the different scopes of a PNRP cloud. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Net.PeerToPeer.PnrpScope.All">
      <summary>All IP addresses are allowed to register with the PNRP <see cref="T:System.Net.PeerToPeer.Cloud" />. There is no difference between any scope and all scopes.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.PnrpScope.Global">
      <summary>The scope is global; all valid IP addresses are allowed to register with the PNRP <see cref="T:System.Net.PeerToPeer.Cloud" />.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.PnrpScope.SiteLocal">
      <summary>The scope is site-local; only IP addresses defined for the site are allowed to register with the PNRP.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.PnrpScope.LinkLocal">
      <summary>The scope is link-local; only IP addresses defined for the local area subnet are allowed to register with the PNRP <see cref="T:System.Net.PeerToPeer.Cloud" />.</summary>
    </member>
    <member name="T:System.Net.PeerToPeer.ResolveCompletedEventArgs">
      <summary>Used in conjunction with the <see cref="E:System.Net.PeerToPeer.PeerNameResolver.ResolveCompleted" /> event, which is signaled when a resolve request for a specific <see cref="T:System.Net.PeerToPeer.PeerName" /> has completed.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.ResolveCompletedEventArgs.#ctor(System.Net.PeerToPeer.PeerNameRecordCollection,System.Exception,System.Boolean,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.ResolveCompletedEventArgs" /> class.</summary>
      <param name="peerNameRecordCollection">The collection associated with the peer name that was resolved.</param>
      <param name="error">Returns an exception if an error occurred. </param>
      <param name="canceled">True if the <see cref="Overload:System.Net.PeerToPeer.PeerNameResolver" /> operation was cancelled, otherwise False.</param>
      <param name="userToken">The user token specified when a <see cref="Overload:System.Net.PeerToPeer.PeerNameResolver.ResolveAsync" /> operation was started. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.ResolveCompletedEventArgs.PeerNameRecordCollection">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.PeerNameRecordCollection" /> object to resolve.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.PeerNameRecordCollection" /> object to resolve is the one found in response to a <see cref="Overload:System.Net.PeerToPeer.PeerNameResolver.ResolveAsync" /> operation on a specific <see cref="T:System.Net.PeerToPeer.PeerName" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.ResolveProgressChangedEventArgs">
      <summary>Used in conjunction with signaling the <see cref="E:System.Net.PeerToPeer.PeerNameResolver.ResolveProgressChanged" /> event.  It is signaled whenever a <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> object is found in response to a <see cref="Overload:System.Net.PeerToPeer.PeerNameResolver.ResolveAsync" /> operation on a specific <see cref="T:System.Net.PeerToPeer.PeerName" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.ResolveProgressChangedEventArgs.#ctor(System.Net.PeerToPeer.PeerNameRecord,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.ResolveProgressChangedEventArgs" /> class.</summary>
      <param name="peerNameRecord">The <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> object to be found.</param>
      <param name="userToken">The unique user state object supplied when a <see cref="Overload:System.Net.PeerToPeer.PeerNameResolver.ResolveAsync" /> operation was started. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.ResolveProgressChangedEventArgs.PeerNameRecord">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.PeerNameRecord" /> object to resolve.</summary>
      <returns>The peer name record object found in response to a <see cref="Overload:System.Net.PeerToPeer.PeerNameResolver.ResolveAsync" /> operation on a specific <see cref="P:System.Net.PeerToPeer.PeerNameRecord.PeerName" />.Unless explicitly specified, the default value for all properties is null for reference types and zero (0) for properties of type int.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.ApplicationChangedEventArgs">
      <summary>Provides qualifying information to a callback method when a <see cref="E:System.Net.PeerToPeer.Collaboration.PeerApplication.ApplicationChanged" /> event occurs.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.ApplicationChangedEventArgs.PeerApplication">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> for which the event was raised.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> instance that was updated.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.ApplicationChangedEventArgs.PeerChangeType">
      <summary>Gets the type of change to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> that occurred.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerChangeType" /> enumeration value that specifies the type of change that was performed on the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.ApplicationChangedEventArgs.PeerContact">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.ApplicationChangedEventArgs.PeerEndPoint">
      <summary>Gets the endpoint for which <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> information has changed.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> that contains the endpoint for which application information has changed. Unless explicitly specified, the default value for all properties is null for reference types and zero (0) for properties of type int.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.ContactManager">
      <summary>Represents a collection of <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> objects which persist in a Windows Address Book.</summary>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.ContactManager.AddContact(System.Net.PeerToPeer.Collaboration.PeerContact)">
      <summary>Adds the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> to the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" /> of the local peer.</summary>
      <param name="peerContact">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance to add to <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" />. </param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> specified by <paramref name="peerContact" /> already exists.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peerContact" /> is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The specified <paramref name="peerContact" /> has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> specified by <paramref name="peerContact" /> could not be reconstituted from its serialized XML format.Unable to add the local peer to the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" /> instance being constructed.</exception>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.ContactManager.ApplicationChanged">
      <summary>Raised whenever a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> associated with a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> in the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" /> has changed.</summary>
      <exception cref="T:System.ObjectDisposedException">The calling object has been disposed.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.ContactManager.CreateContact(System.Net.PeerToPeer.Collaboration.PeerNearMe)">
      <summary>Creates a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance for the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object.</summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance for the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" />.</returns>
      <param name="peerNearMe">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> to associate with the new <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peerNearMe" /> is null.</exception>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The contact manager of the remote peer passed in <paramref name="peerNearMe" /> could not be reached, or the contact could not be read from it.The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance passed in <paramref name="peerNearMe" /> has no endpoints set on it.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.ContactManager.CreateContactAsync(System.Net.PeerToPeer.Collaboration.PeerNearMe,System.Object)">
      <summary>Creates a contact instance for the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object.</summary>
      <param name="peerNearMe">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> to associate with the new <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance.</param>
      <param name="userToken">A user-defined <see cref="T:System.Object" /> that contains information about the <see cref="M:System.Net.PeerToPeer.Collaboration.ContactManager.CreateContactAsync(System.Net.PeerToPeer.Collaboration.PeerNearMe,System.Object)" /> operation. It will be passed to the callback of the asynchronous operation for identification.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userToken" /> already exists and is in use.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peerNearMe" /> is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance has no endpoints set on it.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.ContactManager.CreateContactCompleted">
      <summary>Raised whenever a <see cref="M:System.Net.PeerToPeer.Collaboration.ContactManager.CreateContact(System.Net.PeerToPeer.Collaboration.PeerNearMe)" /> method has completed.</summary>
      <exception cref="T:System.ObjectDisposedException">The calling object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.ContactManager.DeleteContact(System.Net.PeerToPeer.Collaboration.PeerContact)">
      <summary>Removes the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> from the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" /> of the local peer.</summary>
      <param name="peerContact">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> to remove from the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peerContact" /> is null.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.ContactManager.DeleteContact(System.Net.PeerToPeer.PeerName)">
      <summary>Removes the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> associated with the specified <see cref="P:System.Net.PeerToPeer.Collaboration.PeerContact.PeerName" /> from the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" /> of the local peer.</summary>
      <param name="peerName">The <see cref="P:System.Net.PeerToPeer.Collaboration.PeerContact.PeerName" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> to remove from the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" />.</param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> associated with <see cref="P:System.Net.PeerToPeer.Collaboration.PeerContact.PeerName" /> could not be found in the contact manager.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peerName" /> is null.</exception>
      <exception cref="T:System.ObjectDisposedException">This object has had <see cref="M:System.Net.PeerToPeer.Collaboration.ContactManager.Dispose" /> previously called on it and cannot be used for future operations.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Unable to complete <see cref="Overload:System.Net.PeerToPeer.Collaboration.ContactManager.DeleteContact" /> operation.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.ContactManager.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" /> object.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.ContactManager.GetContact(System.Net.PeerToPeer.PeerName)">
      <summary>Returns the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object for the specified <see cref="P:System.Net.PeerToPeer.Collaboration.PeerContact.PeerName" />.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> associated with the supplied <see cref="P:System.Net.PeerToPeer.Collaboration.PeerContact.PeerName" />.</returns>
      <param name="peerName">The <see cref="P:System.Net.PeerToPeer.Collaboration.PeerContact.PeerName" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> to be retrieved.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peerName" /> is null.</exception>
      <exception cref="T:System.ObjectDisposedException">This <see cref="P:System.Net.PeerToPeer.Collaboration.PeerContact.PeerName" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Contact not found in <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" />.Unable to obtain the contact for the supplied <see cref="P:System.Net.PeerToPeer.Collaboration.PeerContact.PeerName" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.ContactManager.GetContacts">
      <summary>Returns a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContactCollection" /> that contains all contacts within the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" /> of the remote peer.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContactCollection" /> that contains all contacts within the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" /> of the remote peer. If the contact manager is empty, then a collection of size zero (0) is returned.</returns>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">An invalid value was returned when this method attempted to enumerate a <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> in this peer contact collection.</exception>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.ContactManager.LocalContact">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> representing the local peer.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance representing the local peer. </returns>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.ContactManager.NameChanged">
      <summary>Raised whenever the <see cref="P:System.Net.PeerToPeer.Collaboration.PeerContact.PeerName" /> associated with a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> in the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" /> has changed.</summary>
      <exception cref="T:System.ObjectDisposedException">The calling object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.ContactManager.ObjectChanged">
      <summary>Raised whenever an object within a contact’s registered <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> objects has changed.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> object has been disposed.</exception>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.ContactManager.PresenceChanged">
      <summary>Raised whenever the presence status of a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> in the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" /> has changed.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.ContactManager.SubscriptionListChanged">
      <summary>Raised when the list of subscribed contacts changes.</summary>
      <exception cref="T:System.ObjectDisposedException">The object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.ContactManager.SynchronizingObject">
      <summary>When this property value is set, all events not fired as the result of an asynchronous operation will have the associated event handlers called back on the thread that created the specific <see cref="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.SynchronizingObject" />.</summary>
      <returns>Object that implements the <see cref="T:System.ComponentModel.ISynchronizeInvoke" /> interface and is used by instances of this type for event handler synchronization on the thread that created it.</returns>
      <exception cref="T:System.ObjectDisposedException">The calling object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.ContactManager.UpdateContact(System.Net.PeerToPeer.Collaboration.PeerContact)">
      <summary>Updates the data associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</summary>
      <param name="peerContact">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> to be updated.</param>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> not found in <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peerContact" /> is null.</exception>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Unable to complete <see cref="M:System.Net.PeerToPeer.Collaboration.ContactManager.UpdateContact(System.Net.PeerToPeer.Collaboration.PeerContact)" /> operation.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.CreateContactCompletedEventArgs">
      <summary>Provides qualifying information to a callback method when a <see cref="E:System.Net.PeerToPeer.Collaboration.ContactManager.CreateContactCompleted" /> event occurs.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.CreateContactCompletedEventArgs.PeerContact">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> for which the event was raised.</summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object associated with the event.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.InviteCompletedEventArgs">
      <summary>Provides qualifying information to a callback method when an <see cref="E:System.Net.PeerToPeer.Collaboration.Peer.InviteCompleted" /> event occurs.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.InviteCompletedEventArgs.InviteResponse">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponse" /> to an invitation operation.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponse" /> to the invitation. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.NameChangedEventArgs">
      <summary>Provides qualifying information to a callback method when a <see cref="E:System.Net.PeerToPeer.Collaboration.PeerEndPoint.NameChanged" /> event occurs.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.NameChangedEventArgs.Name">
      <summary>Gets the new <see cref="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.Name" /> for the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</summary>
      <returns>Gets the new <see cref="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.Name" /> for the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.NameChangedEventArgs.PeerContact">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.NameChangedEventArgs.PeerEndPoint">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> associated with the <see cref="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.Name" />.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> associated with the <see cref="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.Name" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.ObjectChangedEventArgs">
      <summary>Provides qualifying information to a callback method when a <see cref="E:System.Net.PeerToPeer.Collaboration.PeerObject.ObjectChanged" /> event occurs.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.ObjectChangedEventArgs.PeerChangeType">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerChangeType" /> that specifies the type of change that has occurred to a <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> or <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" />.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerChangeType" /> that specifies the type of change that has occurred. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.ObjectChangedEventArgs.PeerContact">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> for which object information has changed.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> on which <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> information has changed.  If the endpoint is not associated with a contact, null is returned. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.ObjectChangedEventArgs.PeerEndPoint">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> for which object information has changed.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> on which <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> information has changed.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.ObjectChangedEventArgs.PeerObject">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> that has been added, deleted or updated.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> that has been added, deleted, or updated. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.Peer">
      <summary>This class represents a remote peer. </summary>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> type. </summary>
      <param name="serializationInfo">Reference to the object that holds the data needed to deserialize this instance.</param>
      <param name="streamingContext">Context that provides the means for deserializing the data. Also referred to as the source of the serialized data.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> object.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> object and optionally releases the managed resources.</summary>
      <param name="disposing">Set to true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.Equals(System.Net.PeerToPeer.Collaboration.Peer)">
      <summary>Performs a case-sensitive comparison of the current <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> and the specified peer.</summary>
      <returns>Returns true if the supplied <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> instance has the same <see cref="P:System.Net.PeerToPeer.Collaboration.Peer.PeerEndPoints" /> as this peer instance, otherwise false. This method also returns false if the peer parameter is null.</returns>
      <param name="other">A <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> instance to compare to this instance.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with the data needed to serialize the target <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" />. </summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination for this serialization.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.GetObjects">
      <summary>Gets the collection of data objects from a local cache.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> collection for the peer or endpoint specified by this instance.</returns>
      <exception cref="T:System.InvalidOperationException">The caller is not subscribed to the endpoint or has not yet called <see cref="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshData" />.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The method is unable to complete due to an error in the underlying Peer Collaboration infrastructure.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.GetObjects(System.Guid)">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> object associated with the supplied <see cref="T:System.Guid" /> from the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObjectCollection" />.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> object associated with the supplied <see cref="T:System.Guid" />. If an object is not found, a collection of size zero (0) is returned.</returns>
      <param name="objectId">The <see cref="T:System.Guid" /> of the <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> object to be retrieved from the collection</param>
      <exception cref="T:System.ArgumentNullException">The object ID is null.</exception>
      <exception cref="T:System.InvalidOperationException">The caller is not subscribed to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> or has not yet called <see cref="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshData" /> against it.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">An error occurred when getting object information from the peer.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.GetPresenceInfo(System.Net.PeerToPeer.Collaboration.PeerEndPoint)">
      <summary>Gets the available presence information for a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</summary>
      <returns>Returns a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerPresenceInfo" /> object which contains presence information for an available endpoint if it is available; otherwise it is null.</returns>
      <param name="peerEndPoint">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> for which to retrieve presence information.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="peerEndPoint" /> does not contain a valid endpoint.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peerEndPoint" /> is null.</exception>
      <exception cref="T:System.ObjectDisposedException">This object has been disposed and cannot be used in current peer operations.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">An error occurred while retrieving presence information from the peer.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.Invite">
      <summary>Sends an invitation to a <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> to start a specific <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />.</summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponse" /> from the peer that received the invitation. </returns>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">An error occurred during the invitation process.The currently executing application is not registered with the Peer Collaboration infrastructure.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.Invite(System.Net.PeerToPeer.Collaboration.PeerApplication,System.String,System.Byte[])">
      <summary>Sends an invitation to a <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> to start a specific <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />.</summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponse" /> from the peer that received the invitation.</returns>
      <param name="applicationToInvite">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> for which the invitation is sent.</param>
      <param name="message">A message to send to the remote peer along with the application invitation.  The message can be no more than 255 Unicode characters.</param>
      <param name="invitationData">A user defined data blob to associate with the invitation.  Its size can be no more than 16,384 bytes.</param>
      <exception cref="T:System.ArgumentException">The application is not registered for collaboration.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="invitationData" /> is larger than 16,384 bytes.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">An error occurred during the invitation process.The currently executing application is not registered with the peer collaboration infrastructure.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.InviteAsync(System.Net.PeerToPeer.Collaboration.PeerApplication,System.String,System.Byte[],System.Object)">
      <summary>Begins an asynchronous invitation operation which sends an invitation to a <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> to start a specific <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />.</summary>
      <param name="applicationToInvite">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> for which the invitation is sent.</param>
      <param name="message">A message to send to the remote peer along with the application invitation. The maximum size of this message is 255 Unicode characters.</param>
      <param name="invitationData">A user defined data blob to associate with the invitation.  Its size can be no more than 16,384 bytes.</param>
      <param name="userToken">User-defined object to pass to the callback of the asynchronous operation for identification. This required parameter must be unique across all asynchronous invitation operations in progress.</param>
      <exception cref="T:System.ArgumentException">The application is not registered with the collaboration infrastructure.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="userToken" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="invitationData" /> is larger than 16,384 bytes.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">An error occurred during the invitation process.The currently executing application is not registered with the collaboration infrastructure.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.InviteAsync(System.Object)">
      <summary>Begins an asynchronous invitation operation which sends an invitation to a <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> to start a specific <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />.</summary>
      <param name="userToken">User-defined object to pass to the callback of the asynchronous operation for identification. This required parameter must be unique across all asynchronous invitation operations in progress.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="userToken" /> is null.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">An error occurred during the invitation process.The currently executing application is not registered with the collaboration infrastructure. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.InviteAsyncCancel(System.Object)">
      <summary>Cancels the invitation that was sent with the <see cref="M:System.Net.PeerToPeer.Collaboration.Peer.InviteAsync(System.Net.PeerToPeer.Collaboration.PeerApplication,System.String,System.Byte[],System.Object)" /> method. </summary>
      <param name="userToken">User defined object to pass to the callback of the <see cref="M:System.Net.PeerToPeer.Collaboration.Peer.InviteAsync(System.Net.PeerToPeer.Collaboration.PeerApplication,System.String,System.Byte[],System.Object)" /> operation for identification. This required parameter must be unique across all asynchronous invitation operations in progress.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="userToken" /> parameter cannot be null.</exception>
      <exception cref="T:System.InvalidOperationException">An asynchronous invitation is not outstanding.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.Peer.InviteCompleted">
      <summary>Raised when the invitation process for a remote peer has completed.</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.Peer.IsOnline">
      <summary>Gets a value specifying if the <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> is currently 'online'.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> is online at any of the endpoints associated with it; otherwise false. Unless specified, the default value for this property is null.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.OnInviteCompleted(System.Net.PeerToPeer.Collaboration.InviteCompletedEventArgs)">
      <summary>Raises the <see cref="E:System.Net.PeerToPeer.Collaboration.Peer.InviteCompleted" /> event.</summary>
      <param name="e">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponse" /> object containing the event data to be passed to delegates associated with the <see cref="E:System.Net.PeerToPeer.Collaboration.Peer.InviteCompleted" /> event.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.Peer.PeerEndPoints">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" />.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.Peer.SynchronizingObject">
      <summary>When this property value is set, all events not fired as the result of an asynchronous operation will have the associated event handlers called back on the thread that created the specific <see cref="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.SynchronizingObject" />.</summary>
      <returns>Object that implements the <see cref="T:System.ComponentModel.ISynchronizeInvoke" /> interface and is used by instances of this type for event handler synchronization on the thread that created it. </returns>
      <exception cref="T:System.ObjectDisposedException">The calling object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> class instance with the data required to serialize the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance. A derived type must call the base type <see cref="M:System.Runtime.Serialization.ISerializable.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> method. </summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination for the serialization.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.Peer.ToString">
      <summary>Returns a <see cref="T:System.String" /> representing the <see cref="P:System.Net.PeerToPeer.Collaboration.PeerContact.PeerName" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" />.</summary>
      <returns>
        <see cref="T:System.String" /> representing the <see cref="P:System.Net.PeerToPeer.Collaboration.PeerContact.PeerName" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerApplication">
      <summary>Represents an application that is available for use with the Peer Collaboration infrastructure.</summary>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplication.#ctor">
      <summary>Initializes a new default instance of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> type.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplication.#ctor(System.Guid,System.String,System.Byte[],System.String,System.String,System.Net.PeerToPeer.Collaboration.PeerScope)">
      <summary>Initializes a new <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> instance with the supplied application ID, description, scope, and data elements.</summary>
      <param name="id">A user-specified <see cref="T:System.Guid" /> used to identify the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />.</param>
      <param name="description">A <see cref="T:System.String" /> description of the peer application.</param>
      <param name="data">A binary data object to associate with the peer application, such as a small image.</param>
      <param name="path">The local file system path to the peer application.</param>
      <param name="commandLineArgs">Command-line arguments for starting the peer application.</param>
      <param name="peerScope">The scope in which the application will be registered for peer collaboration.</param>
      <exception cref="T:System.ArgumentException">Length of the binary data object cannot be less than one or greater than 16k.<paramref name="id" /> is set to null.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplication.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> type with the data required for serialization.</summary>
      <param name="serializationInfo">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</param>
      <param name="streamingContext">The serialization destination associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</param>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.PeerApplication.ApplicationChanged">
      <summary>This event is raised whenever a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> is added, updated or deleted by a remote peer on a subscribed endpoint.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerApplication.CommandLineArgs">
      <summary>Gets or sets command-line parameters to use when initiating a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />.</summary>
      <returns>
        <see cref="T:System.String" /> that represents application-specific command-line parameters to use when initiating the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />. </returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Data">
      <summary>Gets or sets data associated with the current <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> instance.</summary>
      <returns>Array of <see cref="T:System.Byte" /> data that contain a binary object to associate with the peer application. This is commonly a small image or XML blob. </returns>
      <exception cref="T:System.ArgumentException">The size of this binary data object is either less than 0 or greater than 4,096 bytes.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Description">
      <summary>Gets or sets a Unicode <see cref="T:System.String" /> that describes the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />.</summary>
      <returns>
        <see cref="T:System.String" /> value that describes the application. Unless specified, the default value for this property is null.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplication.Dispose">
      <summary>Releases resources used by the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplication.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object and optionally releases the managed resources.</summary>
      <param name="disposing">Set to true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplication.Equals(System.Net.PeerToPeer.Collaboration.PeerApplication)">
      <summary>Compares the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> to the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> instance.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> instance provided has matching data, else false. This method also returns false if <paramref name="other" /> is null. </returns>
      <param name="other">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> instance to test for equality.</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplication.Equals(System.Object)">
      <summary>Compares the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> to the specified <see cref="T:System.Object" />.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> is equal to the specified object instance, else false.</returns>
      <param name="obj">The <see cref="T:System.Object" /> to test for equality.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Object" /> has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplication.Equals(System.Object,System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> objects are considered equal.</summary>
      <returns>Returns true if the specified objects have the same globally unique application <see cref="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Id" /> and <see cref="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Description" />, else false.</returns>
      <param name="objA">The first <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />    to compare.</param>
      <param name="objB">The second <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> to compare.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplication.GetHashCode">
      <summary>Returns the hash code for a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> instance.</summary>
      <returns>A 32-bit signed integer hash code used to compare instances of this type.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplication.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with the data needed to serialize the target <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination for this serialization.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Id">
      <summary>Gets or sets the user-defined <see cref="T:System.Guid" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />.</summary>
      <returns>
        <see cref="T:System.Guid" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> instance. Unless specified, the default value for this property is null.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplication.OnApplicationChanged(System.Net.PeerToPeer.Collaboration.ApplicationChangedEventArgs)">
      <summary>Raises the <see cref="E:System.Net.PeerToPeer.Collaboration.PeerApplication.ApplicationChanged" /> event.</summary>
      <param name="appChangedArgs">The <see cref="T:System.Net.PeerToPeer.Collaboration.ApplicationChangedEventArgs" /> object that contains data to be passed to delegates for the <see cref="E:System.Net.PeerToPeer.Collaboration.PeerApplication.ApplicationChanged" /> event.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Path">
      <summary>Gets or sets the path that designates where the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> executable file resides on the local file system.</summary>
      <returns>
        <see cref="T:System.String" /> that represents the file path. Unless explicitly specified, the default value for this property is null.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerApplication.PeerScope">
      <summary>Gets or sets the scope in which the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> is registered for collaboration.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerScope" /> object that specifies the scope in which the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> will collaborate. </returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerApplication.SynchronizingObject">
      <summary>When this property value is set, all events not fired as the result of an asynchronous operation will have the associated event handlers called back on the thread that created the specific <see cref="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.SynchronizingObject" />.</summary>
      <returns>Object that implements the <see cref="T:System.ComponentModel.ISynchronizeInvoke" /> interface and is used by instances of this type for event handler synchronization on the thread that created it.</returns>
      <exception cref="T:System.ObjectDisposedException">The calling object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplication.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Implements the <see cref="T:System.Runtime.Serialization.ISerializable" /> interface and returns the data required to serialize the target <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object to populate with data.</param>
      <param name="context">The destination for this serialization.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplication.ToString">
      <summary>Returns a string that contains the <see cref="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Id" /> and <see cref="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Description" /> of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> instance.</summary>
      <returns>A string that contains the <see cref="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Id" /> and <see cref="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Description" /> of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> instance, separated by a space.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection">
      <summary>Represents a container for <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> elements. An instance of this type is returned by the <see cref="M:System.Net.PeerToPeer.Collaboration.ContactManager.GetContacts" /> static method.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection.InsertItem(System.Int32,System.Net.PeerToPeer.Collaboration.PeerApplication)">
      <summary>Inserts a new <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> element into the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection" /> at the specified index or key.</summary>
      <param name="index">The zero-based index of the element to replace</param>
      <param name="item">The new value for the element at the specified index</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection.SetItem(System.Int32,System.Net.PeerToPeer.Collaboration.PeerApplication)">
      <summary>Replaces the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> at the specified index.</summary>
      <param name="index">The zero-based index of the element to replace.</param>
      <param name="item">The new value for the element at the specified index.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection.ToString">
      <summary>Returns a <see cref="T:System.String" /> representing the current value of each <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object, separated by commas.</summary>
      <returns>A <see cref="T:System.String" /> representing the current value of each <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> in the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection" />, separated by commas.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerApplicationLaunchInfo">
      <summary>Represents the launch information required by a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> that has initiated in response to a peer collaboration invitation.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerApplicationLaunchInfo.Data">
      <summary>Gets or sets application-defined binary data associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> invitation.</summary>
      <returns>An array of bytes that containing the data associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> invitation. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerApplicationLaunchInfo.Message">
      <summary>Get or set a message associated with the response to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> invitation. </summary>
      <returns>A text message associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponse" /> to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> invitation. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerApplicationLaunchInfo.PeerApplication">
      <summary>Gets or sets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> for which the invitation was sent. </summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> for which the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> invitation was sent. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerApplicationLaunchInfo.PeerContact">
      <summary>Gets or sets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> that sent the invitation.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> that sent the invitation. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerApplicationLaunchInfo.PeerEndPoint">
      <summary>Gets or sets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> from which the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> invitation was sent.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> from which the invitation was sent. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerApplicationRegistrationType">
      <summary>Specifies the type of registration to perform for a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> or <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> registration.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerApplicationRegistrationType.CurrentUser">
      <summary>The application or object is being registered only for the user associated with the calling peer</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerApplicationRegistrationType.AllUsers">
      <summary>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" />  or <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> is being registered for all peers of the application host</summary>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerChangeType">
      <summary>Specifies the type of change that occurred for a peer.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerChangeType.Added">
      <summary>A peer object, endpoint or application has been added.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerChangeType.Deleted">
      <summary>A peer object, endpoint or application has been deleted.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerChangeType.Updated">
      <summary>A peer object, endpoint or application has been updated.</summary>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration">
      <summary>Interacts with the Peer Collaboration infrastructure. Many of the core collaboration scenarios begin with this class.</summary>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerCollaboration.ApplicationLaunchInfo">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationLaunchInfo" /> object that contains information needed when an application is started due to a collaboration request from a remote peer.</summary>
      <returns>If the application was started due to an invitation, a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationLaunchInfo" /> object that contains both information about the peer that sent the application invitation and the invitation itself is returned; otherwise, null is returned.  The default value for this property is null.</returns>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerCollaboration.ContactManager">
      <summary>Gets the persistent store that contains all <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> objects for remote peers.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" /> object that contains all peer contact objects for remote peers. The default value for this property is null.</returns>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.DeleteObject(System.Net.PeerToPeer.Collaboration.PeerObject)">
      <summary>Deregisters a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> associated with the calling peer.</summary>
      <param name="peerObject">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> to deregister. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="peerObject" /> parameter cannot be null.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Unable to complete <see cref="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.DeleteObject(System.Net.PeerToPeer.Collaboration.PeerObject)" /> operation.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.GetLocalRegisteredApplications">
      <summary>Gets all <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> objects that are registered on the local machine.</summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection" /> that contains all applications that are registered on the local machine. If an application is not found, a collection of size zero (0) is returned.</returns>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">An invalid value was returned when this method attempted to enumerate the application registered to this peer. Please make sure that all applications have valid registry values.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.GetLocalRegisteredApplications(System.Net.PeerToPeer.Collaboration.PeerApplicationRegistrationType)">
      <summary>Gets a collection of all <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> objects that are registered on the local machine for the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationRegistrationType" />.</summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection" /> that contains all applications of the specified <paramref name="type" /> that are registered on the local machine. If an application is not found, a collection of size zero (0) is returned.</returns>
      <param name="type">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationRegistrationType" /> to return for the specified application.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="type" /> parameter is not set to a known value in the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationRegistrationType" /> enumeration.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">An invalid value was returned when this method attempted to enumerate the application registered to this peer. Please make sure that all applications have valid registry values.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.GetLocalSetObjects">
      <summary>Obtains all <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instances registered by the calling peer with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> infrastructure on this machine.</summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObjectCollection" /> which contains all the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instances registered by the calling peer with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> infrastructure on the local machine.If registered <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instances are not discovered on the local machine, a collection of size zero (0) is returned.</returns>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">An invalid value was returned when this method attempted to enumerate all available <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instances.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.GetPeersNearMe">
      <summary>Returns a collection of all the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> endpoints currently signed in on the network subnet of the calling peer.</summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMeCollection" /> that contains all the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> endpoints currently signed into the 'Near Me' scope. If peers are not discovered on the subnet, a collection of size zero (0) is returned.</returns>
      <exception cref="T:System.InvalidOperationException">The peer is not signed in to the 'Near Me' scope.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">An invalid value was returned when this method attempted to enumerate all known People Near Me endpoints.</exception>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.PeerCollaboration.LocalApplicationChanged">
      <summary>Raised when a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> in the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection" /> for the local peer on the local host has changed.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerCollaboration.LocalEndPointName">
      <summary>Gets or sets the name of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> associated with the calling peer.</summary>
      <returns>
        <see cref="T:System.String" /> that contains the name associated with the calling peer's endpoint (provided as the <see cref="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.Name" /> property). The default value for this property is null.</returns>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Unable to set <see cref="P:System.Net.PeerToPeer.Collaboration.PeerCollaboration.LocalEndPointName" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.PeerCollaboration.LocalNameChanged">
      <summary>Raised when the name of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> on the local host for the local peer has changed.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.PeerCollaboration.LocalObjectChanged">
      <summary>A <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> in the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObjectCollection" /> for the local peer on the local host has changed.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.PeerCollaboration.LocalPresenceChanged">
      <summary>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerPresenceInfo" /> of the local peer on the local host has changed.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerCollaboration.LocalPresenceInfo">
      <summary>Gets or sets the presence for the calling peer within the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> infrastructure.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerPresenceInfo" /> that contains presence information for the calling peer that has registered for a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> session. </returns>
      <exception cref="T:System.ArgumentNullException">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerPresenceInfo" /> cannot specify null</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerPresenceStatus" /> is offlineUnable to set  <see cref="T:System.Net.PeerToPeer.Collaboration.PeerPresenceInfo" /></exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.RegisterApplication(System.Net.PeerToPeer.Collaboration.PeerApplication,System.Net.PeerToPeer.Collaboration.PeerApplicationRegistrationType)">
      <summary>Registers the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> for a collaboration session with the calling peer.</summary>
      <param name="application">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> for which to register the calling peer within the associated scope (global, local, and link-local).</param>
      <param name="type">The type of registration to perform.  The application may be registered for just the calling peer or for all peers using the machine.</param>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Path" /> property on the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object passed to <paramref name="application" /> is null.The peer application instance provided has the same globally unique <see cref="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Id" /> as an application which is already registered.  The existing registration must be unregistered before a new application can be registered with the provided identifier.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="application" /> and <paramref name="type" /> parameters cannot be null.  Both parameters must be specified.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The type parameter is not set to a known value in the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationRegistrationType" /> enumeration.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.RegisterApplication(System.Net.PeerToPeer.Collaboration.PeerApplication,System.Net.PeerToPeer.Collaboration.PeerApplicationRegistrationType)" /> operation cannot be completed until the caller has signed-in to the infrastructure.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.SetObject(System.Net.PeerToPeer.Collaboration.PeerObject)">
      <summary>Registers a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> associated with the calling peer.</summary>
      <param name="peerObject">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> to register. </param>
      <exception cref="T:System.ArgumentException">Object already registered</exception>
      <exception cref="T:System.ArgumentNullException">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> cannot be null.<see cref="P:System.Net.PeerToPeer.Collaboration.PeerObject.Id" /> cannot be null.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.SetObject(System.Net.PeerToPeer.Collaboration.PeerObject)" /> operation could not be completed.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.SignIn(System.Net.PeerToPeer.Collaboration.PeerScope)">
      <summary>Signs the peer into the collaboration infrastructure with the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerScope" />.</summary>
      <param name="peerScope">The scope the peer is using to join the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> activity.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="peerScope" /> is set to <see cref="F:System.Net.PeerToPeer.Collaboration.PeerScope.None" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="peerScope" /> parameter contains an invalid enumeration value.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.SignIn(System.Net.PeerToPeer.Collaboration.PeerScope)" /> operation could not be completed.</exception>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerCollaboration.SignInScope">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerScope" /> to which the calling peer can publish presence, capability and object information.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerScope" /> object that specifies the scope in which the calling peer will participate. The default value for this property is null.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.SignOut(System.Net.PeerToPeer.Collaboration.PeerScope)">
      <summary>Signs the peer out of the specified scope. </summary>
      <param name="peerScope">Scope enumeration specified by <see cref="T:System.Net.PeerToPeer.Collaboration.PeerScope" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="peerScope" /> parameter contains an invalid enumeration value.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.SignOut(System.Net.PeerToPeer.Collaboration.PeerScope)" /> operation could not be completed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerCollaboration.SynchronizingObject">
      <summary>Gets or sets the event handler callback object for all event handlers.</summary>
      <returns>An object with <see cref="T:System.ComponentModel.ISynchronizeInvoke" /> implemented on it, to be used for application thread synchronization. The default value for this property is null. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.UnregisterApplication(System.Net.PeerToPeer.Collaboration.PeerApplication,System.Net.PeerToPeer.Collaboration.PeerApplicationRegistrationType)">
      <summary>Deregisters the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> from the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> infrastructure.</summary>
      <param name="application">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> to deregister for the calling peer.</param>
      <param name="type">The type of deregistration to perform for either the calling peer or for all peers that exist on the machine.</param>
      <exception cref="T:System.ArgumentException">The globally unique <see cref="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Id" /> for the specified application does not exist or is empty.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="application" /> or <paramref name="type" /> parameter is set to null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="type" /> parameter is not set to a known value in <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationRegistrationType" />.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The  <see cref="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.UnregisterApplication(System.Net.PeerToPeer.Collaboration.PeerApplication,System.Net.PeerToPeer.Collaboration.PeerApplicationRegistrationType)" /> operation could not be completed.</exception>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission">
      <summary>Specifies the values that define or are used in <see cref="N:System.Net.PeerToPeer.Collaboration" /> object permissions. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission.#ctor(System.Security.Permissions.PermissionState)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission" />. The initial <see cref="T:System.Security.Permissions.PermissionState" /> for this instance is passed when the constructor is called.</summary>
      <param name="state">One of the values in the <see cref="T:System.Security.Permissions.PermissionState" /> enumeration.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission.Copy">
      <summary>Creates and returns a copy of the current <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission" />.</summary>
      <returns>A <see cref="T:System.Object" /> that contains a copy of the current instance of <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission.FromXml(System.Security.SecurityElement)">
      <summary>Represents the XML object model for encoding security objects. </summary>
      <param name="e">The XML encoding to use to reconstruct the permission.</param>
      <exception cref="T:System.ArgumentException">The parameter is not a valid permission element.The parameter does not contain a valid type or class.The parameter's version number is not supported. </exception>
      <exception cref="T:System.ArgumentNullException">The parameter is a null reference (Nothing in Visual Basic). </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission.Intersect(System.Security.IPermission)">
      <summary>Creates and returns a permission that is the intersection of the current <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission" /> and the specified <paramref name="target" /> permission.</summary>
      <returns>A new permission that represents the intersection of the current <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission" /> and the specified <paramref name="target" /> permission. This new permission is a null reference (Nothing in Visual Basic) if the intersection is empty. </returns>
      <param name="target">Permission to <see cref="M:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission.Intersect(System.Security.IPermission)" /> with the current permission. It must be of the same type as the current permission.</param>
      <exception cref="T:System.ArgumentException">The target parameter is not a null reference (Nothing in Visual Basic) and is not an instance of the same class as the current permission. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission.IsSubsetOf(System.Security.IPermission)">
      <summary>Determines whether the current <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission" /> is a subset of the specified <paramref name="target" /> permission.</summary>
      <returns>True if the current <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission" /> is a subset of the specified permission; otherwise, false. </returns>
      <param name="target">A permission that is to be tested for the subset relationship. This permission must be of the same type as the current permission.</param>
      <exception cref="T:System.ArgumentException">The parameter is a null reference (Nothing in Visual Basic). </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission.IsUnrestricted">
      <summary>Returns a value specifying whether the current <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission" /> is unrestricted.</summary>
      <returns>True if the current permission is unrestricted; otherwise, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission.ToXml">
      <summary>Creates an XML encoding of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission" /> and its current state.</summary>
      <returns>An XML encoding of the permission, including any state information. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission.Union(System.Security.IPermission)">
      <summary>Creates a permission that is the union of the current <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission" /> and the specified <paramref name="target" /> permission.</summary>
      <returns>A new permission that represents the <see cref="M:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission.Union(System.Security.IPermission)" /> of the current <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission" /> and the specified permission.</returns>
      <param name="target">A permission to combine with the current permission. It must be of the same type as the current permission.</param>
      <exception cref="T:System.ArgumentException">The parameter is a null reference (Nothing in Visual Basic). </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermissionAttribute">
      <summary>Allows security actions for <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission" /> to be applied to code using declarative security. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermissionAttribute.#ctor(System.Security.Permissions.SecurityAction)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermissionAttribute" /> class with the specified <see cref="T:System.Security.Permissions.SecurityAction" />.</summary>
      <param name="action">Specifies a <see cref="T:System.Security.Permissions.SecurityAction" /> value.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermissionAttribute.CreatePermission">
      <summary>Creates and returns a new <see cref="T:System.Security.IPermission" />.</summary>
      <returns>A new <see cref="T:System.Security.IPermission" /> object.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerContact">
      <summary>Represents a peer for which a user has retrieved extended information.</summary>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>No public constructor is defined for this class.</summary>
      <param name="serializationInfo">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</param>
      <param name="streamingContext">The serialization destination associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</param>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.PeerContact.ApplicationChanged">
      <summary>This event is raised whenever an application associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> has changed.</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerContact.Credentials">
      <summary>Gets or sets the X509Certificate (public key) for the peer identified by this <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance.</summary>
      <returns>The X509Certificate (public key) for the peer identified by this <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance. The default value for this property is null.</returns>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerContact.DisplayName">
      <summary>Gets or sets a string which represents the display name of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</summary>
      <returns>
        <see cref="T:System.String" /> which represents the display name of this <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />. Unless explicitly specified, the default value for all properties is null for reference types.</returns>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources and optionally releases the managed resources used by the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object.</summary>
      <param name="disposing">True to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerContact.EmailAddress">
      <summary>Gets or sets the email address associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</summary>
      <returns>
        <see cref="T:System.Net.Mail.MailAddress" /> object that contains the email address associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />. </returns>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.Equals(System.Net.PeerToPeer.Collaboration.PeerContact)">
      <summary>Compares the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> to the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance provided has matching data, else false.</returns>
      <param name="other">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance to test for equality.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.Equals(System.Object)">
      <summary>Compares the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance to the specified object.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance provided has matching data, else false. This method also returns false if the passed parameter is null.</returns>
      <param name="obj">Compares the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance to the specified object.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.Equals(System.Object,System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instances are considered equal. </summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instances provided have matching data, else false.</returns>
      <param name="objA">The first <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> to compare.</param>
      <param name="objB">The second <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> to compare.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.FromXml(System.String)">
      <summary>Creates a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance based on provided XML data.</summary>
      <returns>A string that represents the XML object model for encoding the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance.</returns>
      <param name="peerContactXml">The XML encoding used to reconstruct the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />. </param>
      <exception cref="T:System.ArgumentNullException">Parameter cannot be null.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Unable to complete <see cref="M:System.Net.PeerToPeer.Collaboration.PeerContact.FromXml(System.String)" /> operationParameter is not a valid <see cref="M:System.Net.PeerToPeer.Collaboration.PeerCollaborationPermission.FromXml(System.Security.SecurityElement)" /> object.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.GetApplications">
      <summary>Retrieves the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> objects that were registered by the remote peer into the local cache.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection" /> from the local cache. If associated applications are not found for the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />, a collection of size zero (0) is returned.</returns>
      <exception cref="T:System.InvalidOperationException">The calling peer is not subscribed to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.The calling peer has not yet called the <see cref="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshData" /> method.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Unable to complete <see cref="M:System.Net.PeerToPeer.Collaboration.PeerContact.GetApplications" /> operation.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.GetApplications(System.Guid)">
      <summary>Gets the collection of <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> objects with the specified <see cref="T:System.Guid" /> from the local cache. </summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection" /> from the local cache. If no applications are found with the specified <paramref name="applicationId" />, a collection of size zero (0) is returned.</returns>
      <param name="applicationId">The <see cref="T:System.Guid" /> of the peer application to be retrieved.</param>
      <exception cref="T:System.InvalidOperationException">The calling peer is not subscribed to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.The calling peer has not yet called the <see cref="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshData" /> method.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Unable to complete <see cref="M:System.Net.PeerToPeer.Collaboration.PeerContact.GetApplications" /> operation.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.GetApplications(System.Net.PeerToPeer.Collaboration.PeerEndPoint)">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection" /> associated with the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection" /> associated with the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />. If applications are not associated with the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />, a collection of size zero (0) is returned.</returns>
      <param name="peerEndPoint">Contains endpoint information associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> cannot be null.</exception>
      <exception cref="T:System.InvalidOperationException">The calling peer is not subscribed to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.The calling peer has not yet called the <see cref="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshData" /> method.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Unable to complete <see cref="M:System.Net.PeerToPeer.Collaboration.PeerContact.GetApplications" /> operation.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.GetApplications(System.Net.PeerToPeer.Collaboration.PeerEndPoint,System.Guid)">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection" /> associated with the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</summary>
      <returns>The collection of <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> objects associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />. If applications identified by the ID are not found for the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />, or if the ID for the endpoint is null or invalid, a collection of size zero (0) is returned.</returns>
      <param name="peerEndPoint">The endpoint associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplicationCollection" />.</param>
      <param name="applicationId">Contains application information associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> cannot be null.</exception>
      <exception cref="T:System.InvalidOperationException">The calling peer is not subscribed to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.The calling peer has not yet called the <see cref="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshData" /> method.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Unable to complete <see cref="M:System.Net.PeerToPeer.Collaboration.PeerContact.GetApplications" /> operation.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.GetHashCode">
      <summary>Returns the hash code for a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with the data needed to serialize the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</summary>
      <param name="info">Holds the serialized data associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object.</param>
      <param name="context">Contains the destination for the serialized stream associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.GetObjects(System.Net.PeerToPeer.Collaboration.PeerEndPoint)">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObjectCollection" /> registered by the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> and stored in the local cache.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObjectCollection" /> associated with the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />. If no applications are associated with the endpoint, a collection of size zero (0) is returned.</returns>
      <param name="peerEndPoint">The endpoint from which to retrieve objects.</param>
      <exception cref="T:System.ArgumentNullException">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> cannot be null.</exception>
      <exception cref="T:System.InvalidOperationException">The calling peer is not subscribed to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.The calling peer has not yet called the <see cref="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshData" /> method.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Unable to complete <see cref="Overload:System.Net.PeerToPeer.Collaboration.PeerContact.GetObjects" /> operation.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.GetObjects(System.Net.PeerToPeer.Collaboration.PeerEndPoint,System.Guid)">
      <summary>Gets the collection of peer objects registered by the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> and registered in the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> identified by the specified <see cref="T:System.Guid" />.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObjectCollection" /> associated with the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />. If no applications are associated with the endpoint, a collection of size zero (0) is returned.</returns>
      <param name="peerEndPoint">The endpoint from which to retrieve objects.</param>
      <param name="objectId">The <see cref="T:System.Guid" /> of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> to be retrieved.</param>
      <exception cref="T:System.ArgumentNullException">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> cannot be null.</exception>
      <exception cref="T:System.InvalidOperationException">The calling peer is not subscribed to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.The calling peer has not yet called the <see cref="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshData" /> method.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Unable to complete <see cref="Overload:System.Net.PeerToPeer.Collaboration.PeerContact.GetObjects" /> operation.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.Invite">
      <summary>Sends an invitation to join into peer collaboration sponsored by the sender.</summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponse" /> from the peer who received the invitation. </returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Id" /> for the current application does not exist.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.Invite(System.Net.PeerToPeer.Collaboration.PeerApplication,System.String,System.Byte[])">
      <summary>Sends the specified invitation to join into the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> sponsored by the sender.  </summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponse" /> from the peer who received the invitation.</returns>
      <param name="applicationToInvite">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> for which the invitation is sent.</param>
      <param name="message">A message to send to the remote peer along with the application invitation.</param>
      <param name="invitationData">A user-defined data blob to associate with the invitation.  Its size can be no more than 16,384 bytes.</param>
      <exception cref="T:System.ArgumentNullException">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> cannot be null.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Id" /> for the current application does not exist or is empty.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.Invite(System.Net.PeerToPeer.Collaboration.PeerEndPoint)">
      <summary>Sends an invitation to a specific endpoint to join into peer collaboration with the sender of the invitation. </summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponse" /> from the peer endpoint who received the invitation. </returns>
      <param name="peerEndPoint">The endpoint to receive the invitation.</param>
      <exception cref="T:System.ArgumentNullException">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> cannot be null.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Unable to complete <see cref="M:System.Net.PeerToPeer.Collaboration.PeerContact.Invite" /> operation.The currently executing application is not registered with the peer collaboration infrastructure.<see cref="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.EndPoint" /> specified by <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object is not valid.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.Invite(System.Net.PeerToPeer.Collaboration.PeerEndPoint,System.Net.PeerToPeer.Collaboration.PeerApplication,System.String,System.Byte[])">
      <summary>Sends the specified invitation to the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> to join into the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> application sponsored by the sender.</summary>
      <returns>A <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponse" /> from the peer who received the invitation. </returns>
      <param name="peerEndPoint">The endpoint or remote peer to which to send the invitation.</param>
      <param name="applicationToInvite">The application for which the invitation is sent.</param>
      <param name="message">A message to send to the remote peer along with the application invitation.  The message can be no more than 255 Unicode characters.</param>
      <param name="invitationData">A user-defined data blob to associate with the invitation.  Its size can be no more than 16,384 bytes.</param>
      <exception cref="T:System.ArgumentException">Endpoint in <paramref name="PeerEndPoint" /> cannot be null.</exception>
      <exception cref="T:System.ArgumentNullException">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object cannot be empty.<see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> and <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> objects cannot be null.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Id" /> for the current application does not exist or is empty.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.InviteAsync(System.Net.PeerToPeer.Collaboration.PeerApplication,System.String,System.Byte[],System.Object)">
      <summary>Begins an asynchronous invitation operation for the specified peer endpoints to join the specified collaboration <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> sponsored by the sender of the invitation.</summary>
      <param name="applicationToInvite">The application for which the invitation is sent.</param>
      <param name="message">A message to send to the remote peer along with the application invitation.  The message can be no more than 255 Unicode characters.</param>
      <param name="invitationData">A user-defined data blob to associate with the invitation.  Its size can be no more than 16,384 bytes.</param>
      <param name="userToken">User-defined object to pass to the callback of the asynchronous operation. Also used to identify the asynchronous operation for cancellation. This parameter must be specified and is unique across all asynchronous invitation operations in progress.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="UserToken" /> cannot be null.</exception>
      <exception cref="T:System.ArgumentNullException">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object cannot be null.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="T:System.Guid" /> for the current application does not exist or is empty.Endpoint collection is empty or null.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.InviteAsync(System.Net.PeerToPeer.Collaboration.PeerEndPoint,System.Object)">
      <summary>Begins an asynchronous invitation operation for the specified peer endpoint to join a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> application sponsored by the sender of the invitation. The invitation is identified using the specified <see cref="T:System.Object" />.</summary>
      <param name="peerEndPoint">The endpoint or remote peer to which to send the invitation.</param>
      <param name="userToken">User-defined object to pass to the callback of the asynchronous operation. Also used to identify the asynchronous operation for cancellation. This parameter must be specified and is unique across all asynchronous invitation operations in progress.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="UserToken" /> cannot be null.Endpoint in <paramref name="peerEndPoint" /> cannot be null.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peerEndPoint" /> object cannot be null.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="T:System.Guid" /> for the current application does not exist or is empty.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.InviteAsync(System.Net.PeerToPeer.Collaboration.PeerEndPoint,System.String,System.Byte[],System.Net.PeerToPeer.Collaboration.PeerApplication,System.Object)">
      <summary>Begins an asynchronous invitation operation for the specified <paramref name="peerEndPoint" /> to join the specified collaboration <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> sponsored by the sender of the invitation.</summary>
      <param name="peerEndPoint">The endpoint or remote peer to which to send the invitation.</param>
      <param name="message">A message to send to the remote peer along with the application invitation.  The message can be no more than 255 Unicode characters.</param>
      <param name="invitationData">A user defined data blob to associate with the invitation.  Its size can be no more than 16,384 bytes.</param>
      <param name="applicationToInvite">The application for which the invitation is sent.</param>
      <param name="userToken">User-defined object to pass to the callback of the asynchronous operation. Also used to identify the asynchronous operation for cancellation. This parameter must be specified and is unique across all asynchronous invitation operations in progress.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="UserToken" /> cannot be null.Endpoint specified by <paramref name="peerEndPoint" /> cannot be null.</exception>
      <exception cref="T:System.ArgumentNullException">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> and <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> objects cannot be null.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Id" /> for the current application does not exist or is empty.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.InviteAsync(System.Object)">
      <summary>Begins an asynchronous invitation operation for an endpoint to join a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> application sponsored by the sender of the invitation. The invitation is identified using the specified <see cref="T:System.Object" />.</summary>
      <param name="userToken">User-defined object to pass to the callback of the asynchronous operation. Also used to identify the asynchronous operation for cancellation. This parameter must be specified and is unique across all asynchronous invitation operations in progress.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="UserToken" /> cannot be null.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="P:System.Net.PeerToPeer.Collaboration.PeerApplication.Id" /> for the current application does not exist or is empty.<see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection" /> cannot be null.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerContact.IsSubscribed">
      <summary>Gets or sets a value specifying whether the current <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> is subscribed or unsubscribed to an endpoint. Alternatively, this property gets or sets a value specifying whether the current peer host or hosting application has subscribed or unsubscribed to this <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</summary>
      <returns>True if this <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> is subscribed to this endpoint, false if the contact has not subscribed or has unsubscribed.</returns>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerContact.Nickname">
      <summary>Gets or sets a string which represents the <paramref name="Nickname" /> of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</summary>
      <returns>
        <see cref="T:System.String" /> which represents the nickname of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />. The default value for this property is null.</returns>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.PeerContact.ObjectChanged">
      <summary>This event is signaled whenever a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> has changed.</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.OnApplicationChanged(System.Net.PeerToPeer.Collaboration.ApplicationChangedEventArgs)">
      <summary>Raises the <see cref="E:System.Net.PeerToPeer.Collaboration.PeerContact.ApplicationChanged" /> event.</summary>
      <param name="appChangedArgs">The <see cref="T:System.Net.PeerToPeer.Collaboration.ApplicationChangedEventArgs" /> object to be passed to delegates associated with the <see cref="E:System.Net.PeerToPeer.Collaboration.PeerContact.ApplicationChanged" /> event.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.OnObjectChanged(System.Net.PeerToPeer.Collaboration.ObjectChangedEventArgs)">
      <summary>Raises the <see cref="E:System.Net.PeerToPeer.Collaboration.PeerContact.ObjectChanged" /> event when a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> is added, updated or removed by a remote peer.</summary>
      <param name="objChangedArgs">Type of object change specified by <see cref="T:System.Net.PeerToPeer.Collaboration.ObjectChangedEventArgs" />.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.OnPresenceChanged(System.Net.PeerToPeer.Collaboration.PresenceChangedEventArgs)">
      <summary>Raises the <see cref="E:System.Net.PeerToPeer.Collaboration.PeerContact.PresenceChanged" /> event when the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerPresenceInfo" /> of a peer has changed.</summary>
      <param name="presenceChangedArgs">The <see cref="T:System.Net.PeerToPeer.Collaboration.PresenceChangedEventArgs" /> object to be passed to delegates associated with the <see cref="E:System.Net.PeerToPeer.Collaboration.PeerContact.PresenceChanged" /> event.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.OnSubscribeCompleted(System.Net.PeerToPeer.Collaboration.SubscribeCompletedEventArgs)">
      <summary>Raises the <see cref="E:System.Net.PeerToPeer.Collaboration.PeerContact.SubscribeCompleted" /> event. </summary>
      <param name="e">The <see cref="T:System.Net.PeerToPeer.Collaboration.SubscribeCompletedEventArgs" /> or <see cref="T:System.Net.PeerToPeer.Collaboration.SubscriptionListChangedEventArgs" /> object associated with the <see cref="E:System.Net.PeerToPeer.Collaboration.PeerContact.SubscribeCompleted" /> event.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerContact.PeerEndPoints">
      <summary>Gets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection" /> associated with this <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> collection associated with this <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />. The default value for this property is null.</returns>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerContact.PeerName">
      <summary>Gets or sets the <see cref="T:System.Net.PeerToPeer.PeerName" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</summary>
      <returns>
        <see cref="T:System.String" /> which represents the <see cref="T:System.Net.PeerToPeer.PeerName" /> of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />. Unless explicitly specified, the default value for all properties is null for reference types.</returns>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.PeerContact.PresenceChanged">
      <summary>This event is raised whenever the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> has changed its presence status.</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.Subscribe">
      <summary>Subscribes the calling peer to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />, and as a result, the peer will receive any future <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> events associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</summary>
      <exception cref="T:System.InvalidOperationException">The calling peer is not signed in to People Near Me.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Unable to complete <see cref="M:System.Net.PeerToPeer.Collaboration.PeerContact.Subscribe" /> operation. Failure may be due to an inability to establish a TCP connection to the peer.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerContact.SubscribeAllowed">
      <summary>Gets or sets a value that specifies whether the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> is exposed to the collaboration events associated with the peer or application that has ownership of the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" /> in which this <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object resides.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.SubscriptionType" /> enumeration value that specifies whether this <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> is exposed to collaboration events belonging to the peer host or hosting application. Unless explicitly specified, the default value for all properties is null for reference types.</returns>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.SubscribeAsync(System.Object)">
      <summary>Asynchronously subscribes the calling peer to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />, and as a result, the peer will receive any future <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> events associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</summary>
      <param name="userToken">A user-defined <see cref="T:System.Object" /> that contains information about the <see cref="M:System.Net.PeerToPeer.Collaboration.PeerContact.SubscribeAsync(System.Object)" /> operation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="UserToken" /> cannot be null.</exception>
      <exception cref="T:System.InvalidOperationException">The calling peer is not signed in to People Near Me.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Duplicate <see cref="M:System.Net.PeerToPeer.Collaboration.PeerContact.SubscribeAsync(System.Object)" /> identifier.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.PeerContact.SubscribeCompleted">
      <summary>Raised when a subscription operation has completed.</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Implements the <see cref="T:System.Runtime.Serialization.ISerializable" /> interface and returns the data needed to serialize the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance. </summary>
      <param name="info">Holds the serialized data associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object.</param>
      <param name="context">Contains the destination for the serialized stream associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.ToString">
      <summary>Returns a <see cref="T:System.String" /> representation of the <see cref="P:System.Net.PeerToPeer.Collaboration.PeerContact.DisplayName" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</summary>
      <returns>
        <see cref="T:System.String" /> representation of the <see cref="P:System.Net.PeerToPeer.Collaboration.PeerContact.DisplayName" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.ToXml">
      <summary>Serializes the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> to an XML string for persistent storage or network transfer.</summary>
      <returns>An XML encoding of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</returns>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">Unable to complete <see cref="M:System.Net.PeerToPeer.Collaboration.PeerContact.ToXml" /> operation.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContact.Unsubscribe">
      <summary>Removes a subscription to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> and as a result the calling peer no longer receives <see cref="T:System.Net.PeerToPeer.Collaboration.PeerCollaboration" /> events associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />.</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerContactCollection">
      <summary>Represents a container for <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> elements. An instance of this type is returned by the <see cref="M:System.Net.PeerToPeer.Collaboration.ContactManager.GetContacts" /> static method.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContactCollection.InsertItem(System.Int32,System.Net.PeerToPeer.Collaboration.PeerContact)">
      <summary>Inserts a new <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> element into the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContactCollection" /> at the specified index or key.</summary>
      <param name="index">The zero-based index of the element to replace</param>
      <param name="item">The new value for the element at the specified index</param>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> provided is null</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContactCollection.SetItem(System.Int32,System.Net.PeerToPeer.Collaboration.PeerContact)">
      <summary>Replaces the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> at the specified index.</summary>
      <param name="index">The zero-based index of the element to replace.</param>
      <param name="item">The new value for the element at the specified index.</param>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> provided is null</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerContactCollection.ToString">
      <summary>Returns a Unicode <see cref="T:System.String" /> representing the current value of each <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />, separated by commas.</summary>
      <returns>Unicode <see cref="T:System.String" /> representing the current value of each <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" />, separated by commas.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint">
      <summary>Represents the location of a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" />, or <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> instance specified with a unique network address configuration by describing the current instance of a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> or <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> within the Peer-to-Peer Collaboration Infrastructure.</summary>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPoint.#ctor">
      <summary>Generates a new instance of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPoint.#ctor(System.Net.IPEndPoint)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> class with the peer-associated endpoint contained in <see cref="T:System.Net.IPEndPoint" />.</summary>
      <param name="endPoint">The endpoint associated with the peer.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPoint.#ctor(System.Net.IPEndPoint,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> class with the peer-associated endpoint contained in <see cref="T:System.Net.IPEndPoint" /> and a string specifying <paramref name="endPointName" />.</summary>
      <param name="endPoint">The endpoint associated with the peer.</param>
      <param name="endPointName">Specifies the name associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="endPoint" /> argument specifies null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="endPoint" /> specified is not a valid IPv6 endpoint.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPoint.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> instance with the associated parameters required for serialization.</summary>
      <param name="serializationInfo">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</param>
      <param name="streamingContext">The serialization destination associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPoint.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPoint.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object and optionally releases the managed resources.</summary>
      <param name="disposing">Set to true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.EndPoint">
      <summary>Gets or sets the <see cref="T:System.Net.IPEndPoint" /> that contains the IP address associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> instance.</summary>
      <returns>IP address associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> instance.</returns>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The specified <see cref="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.EndPoint" /> endpoint is not a valid IPv6 endpoint.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPoint.Equals(System.Net.PeerToPeer.Collaboration.PeerEndPoint)">
      <summary>Compares the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> to the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> instance.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> instance provided has matching data, else false.</returns>
      <param name="other">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> instance to test for equality.</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPoint.Equals(System.Object)">
      <summary>Compares the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> to the specified <see cref="T:System.Object" />.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> is equal to the specified object instance, else false.</returns>
      <param name="obj">The <see cref="T:System.Object" /> to test for equality.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPoint.Equals(System.Object,System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> objects are considered equal.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> instances provided have matching data, else false.</returns>
      <param name="objA">The first <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />    to compare.</param>
      <param name="objB">The second <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> to compare.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPoint.GetHashCode">
      <summary>Returns the hash code for a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> instance.</summary>
      <returns>A 32-bit signed integer hash code used to compare instances of this type.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPoint.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> class instance with the data required to serialize the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance. A derived type must call the base type <see cref="M:System.Runtime.Serialization.ISerializable.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> method.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination for the serialization.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.Name">
      <summary>Gets or sets a <see cref="T:System.String" /> that represents a displayed name for the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> instance.</summary>
      <returns>
        <see cref="T:System.String" /> representing the display name of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.PeerEndPoint.NameChanged">
      <summary>Signaled when the name associated with a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object changes.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPoint.OnNameChanged(System.Net.PeerToPeer.Collaboration.NameChangedEventArgs)">
      <summary>Called when a change occurs to the <paramref name="PeerName" /> specified by the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> instance.</summary>
      <param name="nameChangedArgs">Specifies the type of change that has occurred to the <paramref name="PeerName" />.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.SynchronizingObject">
      <summary>When this property value is set, all events not fired as the result of an asynchronous operation will have the associated event handlers called back on the thread that created the specific <see cref="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.SynchronizingObject" />.</summary>
      <returns>Object that implements the <see cref="T:System.ComponentModel.ISynchronizeInvoke" /> interface and is used by instances of this type for event handler synchronization on the thread that created it.</returns>
      <exception cref="T:System.ObjectDisposedException">The calling object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPoint.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates the current <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> instance with the data obtained from the serialized source.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination for the serialization.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPoint.ToString">
      <summary>Returns a <see cref="T:System.String" /> that represents a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> instance.</summary>
      <returns>
        <see cref="T:System.String" /> representing a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection">
      <summary>Represents a container for elements of a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object. An instance of this type is returned by the <see cref="T:System.Net.PeerToPeer.Collaboration.Peer" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection.Equals(System.Net.PeerToPeer.Collaboration.PeerEndPointCollection)">
      <summary>Compares the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection" /> to the specified object.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection" /> is equal to the object specified.</returns>
      <param name="other"> The object to compare against.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection.InsertItem(System.Int32,System.Net.PeerToPeer.Collaboration.PeerEndPoint)">
      <summary>Inserts a new <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> element into the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection" /> at the specified index or key.</summary>
      <param name="index">The zero-based index of the element to replace</param>
      <param name="item">The new value for the element at the specified index</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="item" /> argument is null.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection.SetItem(System.Int32,System.Net.PeerToPeer.Collaboration.PeerEndPoint)">
      <summary>Replaces the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> element at the specified index.</summary>
      <param name="index"> The zero-based index of the element to replace</param>
      <param name="item">The new value for the element at the specified index</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="item" /> argument is null.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection.ToString">
      <summary>Returns a <see cref="T:System.String" /> representing the current value for each instance of <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />, separated by commas.</summary>
      <returns>Unicode <see cref="T:System.String" /> representing the current value of each instance of <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponse">
      <summary>Represents a response received from a remote peer to an invitation sent via the <see cref="M:System.Net.PeerToPeer.Collaboration.Peer.Invite" /> or <see cref="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.Invite" /> method.</summary>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerInvitationResponse.PeerInvitationResponseType">
      <summary>Gets or sets the response to the invitation from the remote peer specified by <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponseType" /> class.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponseType" /> object containing the response from an invitation to a remote peer.</returns>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponseType">
      <summary>Specifies the responses a local peer can receive from an application driven collaboration invitation requests.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerInvitationResponseType.Declined">
      <summary>The peer declined the invitation request.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerInvitationResponseType.Accepted">
      <summary>The peer accepted the invitation request.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerInvitationResponseType.Expired">
      <summary>The invitation request has expired.</summary>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerNearMe">
      <summary>Represents a peer located by the "People Near Me" infrastructure.  </summary>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.#ctor">
      <summary>Initializes a new default instance of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance with the associated parameters required for serialization.</summary>
      <param name="serializationInfo">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" />.</param>
      <param name="streamingContext">The serialization destination associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" />.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.AddToContactManager">
      <summary>Generates a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> from the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object and associates it with the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" />.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</returns>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The Collaboration infrastructure has failed to create and associate a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> with the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.AddToContactManager(System.String,System.String,System.Net.Mail.MailAddress)">
      <summary>Generates a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> with the associated descriptive parameters from the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object and associates it with the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" />.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object associated with a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> containing descriptive parameters.</returns>
      <param name="displayName">The display name assigned to the newly created contact.</param>
      <param name="nickname">The <paramref name="nickname" /> to assign to the newly created contact.</param>
      <param name="emailAddress">
        <see cref="T:System.Net.Mail.MailAddress" /> object that specifies the email address to assign to the newly created contact.</param>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The Collaboration infrastructure has failed to create or update a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> with the <see cref="T:System.Net.PeerToPeer.Collaboration.ContactManager" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.CreateFromPeerEndPoint(System.Net.PeerToPeer.Collaboration.PeerEndPoint)">
      <summary>Generates a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object from the provided <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />. </returns>
      <param name="peerEndPoint">
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object that specifies the endpoint associated with the peer.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="peerEndPoint" /> is null.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The referenced <see cref="P:System.Net.PeerToPeer.Collaboration.PeerEndPoint.EndPoint" /> specifies null.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object and optionally releases the managed resources.</summary>
      <param name="disposing">Set to true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.Equals(System.Net.PeerToPeer.Collaboration.PeerNearMe)">
      <summary>Compares the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> to the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance provided has matching data, else false.</returns>
      <param name="other">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance to test for equality.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="other" /> argument is null.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.Equals(System.Object)">
      <summary>Compares the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance to the specified object.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance provided has matching data, else false.</returns>
      <param name="obj">The object to test for equality.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.Equals(System.Object,System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instances are considered equal.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instances provided have matching data, else false.</returns>
      <param name="objA">The first <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> to compare.</param>
      <param name="objB">The second <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> to compare.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.GetHashCode">
      <summary>Returns the hash code for the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance.</summary>
      <returns>A 32-bit signed integer hash code used to compare instances of this type.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with the data required to serialize the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" />. A derived type must call the base type <see cref="M:System.Runtime.Serialization.ISerializable.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> method.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination for the serialization.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.InternalRefreshData(System.Object)">
      <summary>Initiates a network operation to retrieve the application, object and presence data specific to a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance.</summary>
      <param name="state">Specifies the application, object, and presence data relevant to a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance. </param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.Invite">
      <summary>Sends an invitation to join a peer collaboration application to a remote peer.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponse" /> object containing the relevant <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponseType" /> value specified by the remote peer.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object has been disposed.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> specified by the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection" /> is null or has a value count of '0' associated endpoints.A <see cref="T:System.Guid" /> has not been associated with the current peer-to-peer application.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.Invite(System.Net.PeerToPeer.Collaboration.PeerApplication,System.String,System.Byte[])">
      <summary>Sends an invitation to join a peer collaboration application to a remote peer that includes data that describes or specifies the application invite.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponse" /> object containing the relevant <see cref="T:System.Net.PeerToPeer.Collaboration.PeerInvitationResponseType" /> value specified by the remote peer.</returns>
      <param name="applicationToInvite">Specifies the relevant Peer Collaboration application represented by a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> instance.</param>
      <param name="message">A message to send to the remote peer along with the application invitation.  The message can be no more than 255 Unicode characters.</param>
      <param name="invitationData">A user defined data blob to associate with the invitation.  Size is limited to 16K (16,384 bytes).</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="applicationToInvite" /> argument specifies null.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The <paramref name="applicationToInvite" /> argument specifies an empty <see cref="T:System.Guid" />.The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> specified by the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPointCollection" /> is null or has a value count of '0' associated endpoints.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.InviteAsync(System.Net.PeerToPeer.Collaboration.PeerApplication,System.String,System.Byte[],System.Object)">
      <summary>Receives an invitation response from a peer regarding an invitation to a peer collaboration application.</summary>
      <param name="applicationToInvite">Specifies the relevant Peer Collaboration application information represented by a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object.</param>
      <param name="message">A message to send to the remote peer along with the application invitation. The message can be no more than 255 Unicode characters.</param>
      <param name="invitationData">A user defined data blob to associate with the invitation.  Size is limited to 16K.</param>
      <param name="userToken">User-defined object passed to the callback of the asynchronous operation for identification. This required parameter must be unique across all asynchronous invitation operations still in-progress.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="applicationToInvite" /> argument specifies null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="userToken" /> argument specifies null.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The endpoint specified by <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> was not found. The <paramref name="applicationToInvite" /> argument specifies an empty <see cref="T:System.Guid" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.InviteAsync(System.Object)">
      <summary>Receives an invitation response from a peer regarding joining a peer collaboration application.</summary>
      <param name="userToken">User-defined object passed to the callback of the asynchronous operation for identification. This required parameter must be unique across all asynchronous invitation operations still in-progress.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object has been disposed.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="userToken" /> argument specifies null.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">This <see cref="T:System.Net.PeerToPeer.Collaboration.PeerApplication" /> object does not specify a valid <see cref="T:System.Guid" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerNearMe.Nickname">
      <summary>Gets or sets a <see cref="T:System.String" /> representing the Nickname of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object.</summary>
      <returns>
        <see cref="T:System.String" /> that represents the Nickname of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.OnRefreshDataCompleted(System.Net.PeerToPeer.Collaboration.RefreshDataCompletedEventArgs)">
      <summary>Signals the <see cref="E:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshDataCompleted" /> event.</summary>
      <param name="e">Event data contained in a <see cref="T:System.Net.PeerToPeer.Collaboration.RefreshDataCompletedEventArgs" /> instance.</param>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.PeerNearMe.PeerNearMeChanged">
      <summary>Signaled when a new <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance is found, no longer available, or the associated <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object information has changed.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshData">
      <summary>Initiates a network operation to retrieve the application, object and presence data specific to a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshDataAsync(System.Object)">
      <summary>Initiates a network operation to retrieve the application, object and presence data specific to a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance.</summary>
      <param name="userToken">User-specified state object that is passed to the delegate when this method completes the operation.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="userToken" /> argument specifies null.</exception>
      <exception cref="T:System.Net.PeerToPeer.PeerToPeerException">The prior <see cref="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshDataAsync(System.Object)" /> call has not yet completed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshDataCompleted">
      <summary>Signaled when the <see cref="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshData" /> or <see cref="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshDataAsync(System.Object)" /> operation for the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance is completed.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates the current <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance with the data obtained from the serialized source.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination for the serialization.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.ToString">
      <summary>Returns <see cref="T:System.String" /> that represents a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance.</summary>
      <returns>
        <see cref="T:System.String" /> representing the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerNearMeChangedEventArgs">
      <summary>Provides qualifying information to a callback method when a <see cref="E:System.Net.PeerToPeer.Collaboration.PeerNearMe.PeerNearMeChanged" /> event occurs.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerNearMeChangedEventArgs.PeerChangeType">
      <summary>Gets the type of change to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object that has occurred.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerChangeType" /> object that specifies the type of change to the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> instance that occurred.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerNearMeChangedEventArgs.PeerNearMe">
      <summary>Gets the instance of <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> that has changed.</summary>
      <returns>The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object that has changed.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerNearMeCollection">
      <summary>Represents a container for elements of a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object. An instance of this type is returned by the <see cref="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.GetPeersNearMe" /> static method.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMeCollection.InsertItem(System.Int32,System.Net.PeerToPeer.Collaboration.PeerNearMe)">
      <summary>Inserts a new <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> element into the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMeCollection" /> at the specified index or key.</summary>
      <param name="index">The zero-based index in which the element resides.</param>
      <param name="item">The new value for the element in the specified index.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="item" /> argument is null.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMeCollection.SetItem(System.Int32,System.Net.PeerToPeer.Collaboration.PeerNearMe)">
      <summary>Replaces the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> element at the specified index.</summary>
      <param name="index">The zero-based index in which the element resides.</param>
      <param name="item">The new value for the element in the specified index.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="item" /> argument is null.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerNearMeCollection.ToString">
      <summary>Returns a string representation of the current value for each instance of <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" />, separated by commas.</summary>
      <returns>Unicode string representing the current value for each instance of <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerObject">
      <summary>Represents a new instance of the PeerObject class with an auto-generated <see cref="T:System.Guid" />.  </summary>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObject.#ctor">
      <summary>Initializes a new default instance of the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObject.#ctor(System.Guid,System.Byte[],System.Net.PeerToPeer.Collaboration.PeerScope)">
      <summary>Initializes a new <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance with the associated scope and data. </summary>
      <param name="Id">The user-defined identifier for the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" />.</param>
      <param name="data">A data blob that contains information about the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" />. This parameter is limited a size that is less than or equal to 16K.</param>
      <param name="peerScope">Specifies the scope in which the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> is to be registered. </param>
      <exception cref="T:System.ArgumentException">One of the arguments provided to this method is not valid.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObject.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance with the associated parameters required for serialization.</summary>
      <param name="serializationInfo">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" />.</param>
      <param name="streamingContext">The serialization destination associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" />.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerObject.Data">
      <summary>Gets or sets descriptive data associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance for a Peer Collaboration application.</summary>
      <returns>Descriptive data (such as text or a small image) associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentException">One of the arguments provided to this method is not valid.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObject.Dispose">
      <summary>Releases all resources utilized by the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObject.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> and optionally releases the managed resources.</summary>
      <param name="disposing">Set to true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObject.Equals(System.Net.PeerToPeer.Collaboration.PeerObject)">
      <summary>Compares a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> to the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance provided has matching data, else false.</returns>
      <param name="other">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance to test for equality.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance specified by <paramref name="other" /> has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObject.Equals(System.Object)">
      <summary>Compares the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> to the specified object.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" />instance provided has matching data, else false.</returns>
      <param name="obj"> The object to test for equality.</param>
      <exception cref="T:System.ObjectDisposedException">The object specified by <paramref name="obj" /> has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObject.Equals(System.Object,System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instances are considered equal.</summary>
      <returns>Returns true if the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instances provided have matching data, else false.</returns>
      <param name="objA">The first <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> to compare.</param>
      <param name="objB">The second <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> to compare.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObject.GetHashCode">
      <summary>Returns the hash code for the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance.</summary>
      <returns>A 32-bit signed integer hash code used to compare instances of this type.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObject.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with the data required to serialize the specified <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" />.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> instance to populate with data.</param>
      <param name="context">The destination for the serialization.</param>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerObject.Id">
      <summary>Gets or sets a <see cref="T:System.Guid" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance.</summary>
      <returns>
        <see cref="T:System.Guid" /> associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Net.PeerToPeer.Collaboration.PeerObject.ObjectChanged">
      <summary>Signaled when a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance changes.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObject.OnObjectChanged(System.Net.PeerToPeer.Collaboration.ObjectChangedEventArgs)">
      <summary>Signaled when a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance with the specified ID is added, updated or removed by a remote peer. </summary>
      <param name="objChangedArgs">Type of object change specified by <see cref="T:System.Net.PeerToPeer.Collaboration.ObjectChangedEventArgs" />.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance associated with this change has been disposed.</exception>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerObject.PeerScope">
      <summary>Gets or sets the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerScope" /> in which the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance is registered.</summary>
      <returns>PeerScope that specifies the scope in which the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> is registered.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerObject.SynchronizingObject">
      <summary>Gets or sets the object used to marshal the event handler calls that are issued as a result of a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance change.</summary>
      <returns>Object with an implementation of the <see cref="T:System.ComponentModel.ISynchronizeInvoke" /> interface. This object is used for event handler synchronization.</returns>
      <exception cref="T:System.ObjectDisposedException">The calling object has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObject.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates the current <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance with the data obtained from the serialized source.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination for the serialization.</param>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObject.ToString">
      <summary>Returns a <see cref="T:System.String" /> that represents a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance.</summary>
      <returns>A <see cref="T:System.String" /> representing the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance has been disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerObjectCollection">
      <summary>Represents a container for elements of a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> instance.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObjectCollection.InsertItem(System.Int32,System.Net.PeerToPeer.Collaboration.PeerObject)">
      <summary>Inserts a new <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> element into the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObjectCollection" /> at the specified index or key.</summary>
      <param name="index">The zero-based index in which the element resides.</param>
      <param name="item">The new value for the element in the specified index.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="item" /> argument is null.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObjectCollection.SetItem(System.Int32,System.Net.PeerToPeer.Collaboration.PeerObject)">
      <summary>Replaces the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" /> element at the specified index.</summary>
      <param name="index">The zero-based index in which the element resides.</param>
      <param name="item">The new value for the element in the specified index.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="item" /> argument is null.</exception>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerObjectCollection.ToString">
      <summary>Returns a string representation of the current value for each instance of <see cref="T:System.Net.PeerToPeer.Collaboration.PeerObject" />, separated by commas.</summary>
      <returns>String representing the current value for each instance of PeerObject, separated by commas.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerPresenceInfo">
      <summary>Represents the presence information of a peer.</summary>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerPresenceInfo.#ctor">
      <summary>Initializes a new default instance of a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerPresenceInfo" /> object.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Net.PeerToPeer.Collaboration.PeerPresenceInfo.#ctor(System.Net.PeerToPeer.Collaboration.PeerPresenceStatus,System.String)">
      <summary>Initializes an instance of a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerPresenceInfo" /> object that includes a Unicode string describing the presence status the local peer.</summary>
      <param name="presenceStatus"> Status of the Peer.</param>
      <param name="description"> Description of the presence state.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerPresenceInfo.DescriptiveText">
      <summary>Gets or sets a Unicode string further describing the presence status for the local peer.</summary>
      <returns>Unicode string describing the presence status of a peer.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PeerPresenceInfo.PresenceStatus">
      <summary>Gets or sets the presence status of the local peer. </summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerPresenceStatus" /> enumeration that specifies the presence status of a peer.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerPresenceStatus">
      <summary>Specifies the presence status of a peer.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerPresenceStatus.Offline">
      <summary>Specifies that the peer is Offline.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerPresenceStatus.OutToLunch">
      <summary>Specifies that the peer is currently "Out to Lunch" and unable to respond.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerPresenceStatus.Away">
      <summary>Specifies that the peer is "Away" and unable to respond.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerPresenceStatus.BeRightBack">
      <summary>Specifies that the peer has stepped away from the application and will participate soon.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerPresenceStatus.Idle">
      <summary>Specifies that the peer is idling.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerPresenceStatus.Busy">
      <summary>Specifies that the peer is "Busy" and does not wish to be disturbed.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerPresenceStatus.OnThePhone">
      <summary>Specifies that the peer is currently on the phone and does not wish to be disturbed.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerPresenceStatus.Online">
      <summary>Specifies that the peer is actively participating in the Peer Collaboration network.</summary>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PeerScope">
      <summary>Specifies the current network scope of a peer.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerScope.None">
      <summary>Specifies that a peer not sign-in to a Peer Collaboration scope. Passing this value to the <see cref="M:System.Net.PeerToPeer.Collaboration.PeerCollaboration.SignIn(System.Net.PeerToPeer.Collaboration.PeerScope)" /> method generates no result.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerScope.NearMe">
      <summary>Specifies sign-in to the 'NearMe' scope. This scope facilitates connections to all peers on the same subnet via Peer Collaboration Methods.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerScope.Internet">
      <summary>Specifies sign-in to the 'Internet' scope. This scope facilitates connections with all contacts in the Contact Manager.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.PeerScope.All">
      <summary>Specifies sign-in to the 'NearMe' scope and 'Internet' scope.</summary>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.PresenceChangedEventArgs">
      <summary>Provides qualifying information to a callback method when a <see cref="E:System.Net.PeerToPeer.Collaboration.ContactManager.PresenceChanged" /> or <see cref="E:System.Net.PeerToPeer.Collaboration.PeerCollaboration.LocalPresenceChanged" /> event occurs.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PresenceChangedEventArgs.PeerChangeType">
      <summary>Specifies the type of change that has occurred to the presence status of a peer.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerChangeType" /> object containing data that specifies the type of change that has occurred to the presence status of a peer.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PresenceChangedEventArgs.PeerContact">
      <summary>Specifies the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> on which the presence information has changed. </summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object associated with the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> on which the presence information has changed.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PresenceChangedEventArgs.PeerEndPoint">
      <summary>Specifies the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> on which the presence information has changed.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> on which the presence information has changed.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.PresenceChangedEventArgs.PeerPresenceInfo">
      <summary>Specifies the changed presence information of a <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> as well as a string provided by <see cref="T:System.Net.PeerToPeer.Collaboration.PeerPresenceInfo" /> describing the updated presence status.</summary>
      <returns>Changed presence information for <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> and a string describing the updated presence status.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.RefreshDataCompletedEventArgs">
      <summary>Provides qualifying information to the <see cref="M:System.Net.PeerToPeer.Collaboration.PeerNearMe.OnRefreshDataCompleted(System.Net.PeerToPeer.Collaboration.RefreshDataCompletedEventArgs)" /> method when <see cref="E:System.Net.PeerToPeer.Collaboration.PeerNearMe.RefreshDataCompleted" /> events occur.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.RefreshDataCompletedEventArgs.PeerEndPoint">
      <summary>Specifies the updated <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" />.</summary>
      <returns>The updated <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object containing the endpoint data of a peer.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.SubscribeCompletedEventArgs">
      <summary>Provides qualifying information to a callback method when a <see cref="E:System.Net.PeerToPeer.Collaboration.PeerContact.SubscribeCompleted" /> event is signaled.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.SubscribeCompletedEventArgs.PeerContact">
      <summary>Specifies the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> for which the subscription was requested. </summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object for which a subscription was requested.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.SubscribeCompletedEventArgs.PeerNearMe">
      <summary>Specifies the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> for which the subscription is requested. </summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerNearMe" /> object for which a subscription was requested.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.SubscriptionListChangedEventArgs">
      <summary>Provides qualifying information to a callback method when a <see cref="E:System.Net.PeerToPeer.Collaboration.ContactManager.SubscriptionListChanged" /> event is signaled.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.SubscriptionListChangedEventArgs.PeerChangeType">
      <summary>Specifies the type of change that has occurred.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerChangeType" /> enumeration that specifies the type of change performed on the Subscription List.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.SubscriptionListChangedEventArgs.PeerContact">
      <summary>Specifies the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> for which the Subscription List was changed.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerContact" /> object for which the Subscription List was changed.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Net.PeerToPeer.Collaboration.SubscriptionListChangedEventArgs.PeerEndPoint">
      <summary>Specifies the <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> for which the subscription notification was received.</summary>
      <returns>
        <see cref="T:System.Net.PeerToPeer.Collaboration.PeerEndPoint" /> object for which the Subscription List was changed.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Net.PeerToPeer.Collaboration.SubscriptionType">
      <summary>Specifies if a remote peer subscribed to the local peer can receive event notifications.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.SubscriptionType.Blocked">
      <summary>Specifies that a remote peer can subscribe to the local peer, but will not receive event notifications.</summary>
    </member>
    <member name="F:System.Net.PeerToPeer.Collaboration.SubscriptionType.Allowed">
      <summary>Specifies that a remote peer can subscribe to the local peer as well as receive event notifications.</summary>
    </member>
    <member name="T:System.Net.Sockets.HttpPolicyDownloaderProtocol"></member>
    <member name="M:System.Net.Sockets.HttpPolicyDownloaderProtocol.#ctor(System.Uri,System.Net.IPAddress)"></member>
    <member name="M:System.Net.Sockets.HttpPolicyDownloaderProtocol.Abort"></member>
    <member name="M:System.Net.Sockets.HttpPolicyDownloaderProtocol.BeginDownload(System.Net.Sockets.SecurityCriticalAction)"></member>
    <member name="M:System.Net.Sockets.HttpPolicyDownloaderProtocol.DownloadCallback(System.IAsyncResult)"></member>
    <member name="M:System.Net.Sockets.HttpPolicyDownloaderProtocol.ReadCallback(System.IAsyncResult)"></member>
    <member name="M:System.Net.Sockets.HttpPolicyDownloaderProtocol.RegisterUnsafeWebRequestCreator(System.Net.IUnsafeWebRequestCreate)"></member>
    <member name="P:System.Net.Sockets.HttpPolicyDownloaderProtocol.Result"></member>
    <member name="T:System.Net.Sockets.SecurityCriticalAction"></member>
    <member name="T:System.Net.Sockets.SocketPolicy"></member>
    <member name="M:System.Net.Sockets.SocketPolicy.#ctor"></member>
    <member name="T:System.Net.Sockets.UdpAnySourceMulticastClient">
      <summary>A client receiver for multicast traffic from any source, also known as Any Source Multicast (ASM) or Internet Standard Multicast (ISM).</summary>
    </member>
    <member name="M:System.Net.Sockets.UdpAnySourceMulticastClient.#ctor(System.Net.IPAddress,System.Int32)">
      <summary>Creates a new <see cref="T:System.Net.Sockets.UdpAnySourceMulticastClient" /> UDP client that can subscribe to a group address and receive datagrams from any source.</summary>
      <param name="groupAddress">The multicast group address for this receiver to subscribe to.</param>
      <param name="localPort">The local port for this receiver to bind to.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="groupAddress" /> is null. reference</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="localPort" /> is less than 0
-or-
<paramref name="localPort" /> is greater than 65,535.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpAnySourceMulticastClient.BeginJoinGroup(System.AsyncCallback,System.Object)">
      <summary>Binds the socket and begins a join operation to the multicast group to allow datagrams to be received from any group participant.</summary>
      <returns>Returns <see cref="T:System.IAsyncResult" />.An <see cref="T:System.IAsyncResult" /> that references this operation.</returns>
      <param name="callback">A callback method to invoke when the operation completes.</param>
      <param name="state">Optional state information to pass to the <paramref name="callback" />  method for this operation.</param>
      <exception cref="T:System.InvalidOperationException">The multicast group has already been joined or a join operation is currently in progress.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.UdpAnySourceMulticastClient" />  has been disposed. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket. See the Remarks section for more information.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpAnySourceMulticastClient.BeginReceiveFromGroup(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
      <returns>Returns <see cref="T:System.IAsyncResult" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpAnySourceMulticastClient.BeginSendTo(System.Byte[],System.Int32,System.Int32,System.Net.IPEndPoint,System.AsyncCallback,System.Object)">
      <returns>Returns <see cref="T:System.IAsyncResult" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpAnySourceMulticastClient.BeginSendToGroup(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
      <returns>Returns <see cref="T:System.IAsyncResult" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpAnySourceMulticastClient.BlockSource(System.Net.IPAddress)"></member>
    <member name="M:System.Net.Sockets.UdpAnySourceMulticastClient.Dispose"></member>
    <member name="M:System.Net.Sockets.UdpAnySourceMulticastClient.EndJoinGroup(System.IAsyncResult)"></member>
    <member name="M:System.Net.Sockets.UdpAnySourceMulticastClient.EndReceiveFromGroup(System.IAsyncResult,System.Net.IPEndPoint@)">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpAnySourceMulticastClient.EndSendTo(System.IAsyncResult)"></member>
    <member name="M:System.Net.Sockets.UdpAnySourceMulticastClient.EndSendToGroup(System.IAsyncResult)"></member>
    <member name="P:System.Net.Sockets.UdpAnySourceMulticastClient.MulticastLoopback">
      <summary>Gets or sets a value that specifies whether outgoing multicast packets are delivered to the sending application.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.A value that indicates if outgoing packets to a multicast group are delivered to the sending application.</returns>
      <exception cref="T:System.InvalidOperationException">The multicast group has not yet been joined.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.UdpAnySourceMulticastClient" />  has been disposed. </exception>
    </member>
    <member name="P:System.Net.Sockets.UdpAnySourceMulticastClient.ReceiveBufferSize">
      <summary>Gets or sets the size, in bytes, of the receive buffer of the <see cref="T:System.Net.Sockets.Socket" /> used for multicast receive operations on this <see cref="T:System.Net.Sockets.UdpAnySourceMulticastClient" />  instance.</summary>
      <returns>Returns <see cref="T:System.Int32" />.The size, in bytes, of the receive buffer.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The buffer size specified is less than 0.</exception>
      <exception cref="T:System.InvalidOperationException">The multicast group has not yet been joined.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.UdpAnySourceMulticastClient" />  has been disposed. </exception>
    </member>
    <member name="P:System.Net.Sockets.UdpAnySourceMulticastClient.SendBufferSize">
      <summary>Gets or sets the size, in bytes, of the send buffer of the <see cref="T:System.Net.Sockets.Socket" /> used for multicast send operations on this <see cref="T:System.Net.Sockets.UdpAnySourceMulticastClient" />  instance.</summary>
      <returns>Returns <see cref="T:System.Int32" />.The size, in bytes, of the send buffer.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The buffer size specified is less than 0.</exception>
      <exception cref="T:System.InvalidOperationException">The multicast group has not yet been joined.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.UdpAnySourceMulticastClient" />  has been disposed. </exception>
    </member>
    <member name="M:System.Net.Sockets.UdpAnySourceMulticastClient.UnblockSource(System.Net.IPAddress)"></member>
    <member name="T:System.Net.Sockets.UdpSingleSourceMulticastClient"></member>
    <member name="M:System.Net.Sockets.UdpSingleSourceMulticastClient.#ctor(System.Net.IPAddress,System.Net.IPAddress,System.Int32)"></member>
    <member name="M:System.Net.Sockets.UdpSingleSourceMulticastClient.BeginJoinGroup(System.AsyncCallback,System.Object)"></member>
    <member name="M:System.Net.Sockets.UdpSingleSourceMulticastClient.BeginReceiveFromSource(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)"></member>
    <member name="M:System.Net.Sockets.UdpSingleSourceMulticastClient.BeginSendToSource(System.Byte[],System.Int32,System.Int32,System.Int32,System.AsyncCallback,System.Object)"></member>
    <member name="M:System.Net.Sockets.UdpSingleSourceMulticastClient.Dispose"></member>
    <member name="M:System.Net.Sockets.UdpSingleSourceMulticastClient.EndJoinGroup(System.IAsyncResult)"></member>
    <member name="M:System.Net.Sockets.UdpSingleSourceMulticastClient.EndReceiveFromSource(System.IAsyncResult,System.Int32@)"></member>
    <member name="M:System.Net.Sockets.UdpSingleSourceMulticastClient.EndSendToSource(System.IAsyncResult)"></member>
    <member name="P:System.Net.Sockets.UdpSingleSourceMulticastClient.ReceiveBufferSize"></member>
    <member name="P:System.Net.Sockets.UdpSingleSourceMulticastClient.SendBufferSize"></member>
  </members>
</doc>