﻿using System;
using System.Windows.Forms;

namespace OCRTools
{
    partial class UcContent
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            this.txtContent = new System.Windows.Forms.RichTextBox();
            this.wbContent = new System.Windows.Forms.WebBrowser();
            this.pnlPicText = new OCRTools.PanelPictureView();
            this.toolResize = new System.Windows.Forms.ToolStrip();
            this.tsmCopy = new System.Windows.Forms.ToolStripButton();
            this.tsmSearch = new System.Windows.Forms.ToolStripButton();
            this.tsmTrans = new System.Windows.Forms.ToolStripButton();
            this.tsmPicBig = new System.Windows.Forms.ToolStripButton();
            this.txtPicZoomPercent = new System.Windows.Forms.ToolStripLabel();
            this.tsmPicSmall = new System.Windows.Forms.ToolStripButton();
            this.tsmPicOrigin = new System.Windows.Forms.ToolStripButton();
            this.tsmModel = new System.Windows.Forms.ToolStripButton();
            this.tsmPicView = new System.Windows.Forms.ToolStripButton();
            this.tsmFullScreen = new System.Windows.Forms.ToolStripButton();
            this.dgContent = new OCRTools.DataGridViewEx();
            this.toolResize.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgContent)).BeginInit();
            this.SuspendLayout();
            // 
            // txtContent
            // 
            this.txtContent.BackColor = System.Drawing.Color.Honeydew;
            this.txtContent.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.txtContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtContent.EnableAutoDragDrop = true;
            this.txtContent.Font = new System.Drawing.Font("新宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtContent.Location = new System.Drawing.Point(0, 0);
            this.txtContent.Margin = new System.Windows.Forms.Padding(0);
            this.txtContent.Name = "txtContent";
            this.txtContent.Size = new System.Drawing.Size(470, 351);
            this.txtContent.TabIndex = 1;
            this.txtContent.TabStop = false;
            this.txtContent.Text = "";
            // 
            // wbContent
            // 
            this.wbContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.wbContent.Location = new System.Drawing.Point(0, 0);
            this.wbContent.Margin = new System.Windows.Forms.Padding(0);
            this.wbContent.MinimumSize = new System.Drawing.Size(20, 20);
            this.wbContent.Name = "wbContent";
            this.wbContent.Size = new System.Drawing.Size(470, 351);
            this.wbContent.TabIndex = 3;
            this.wbContent.TabStop = false;
            // 
            // pnlPicText
            // 
            this.pnlPicText.AutoCenter = false;
            this.pnlPicText.AutoScroll = true;
            this.pnlPicText.BackColor = System.Drawing.Color.White;
            this.pnlPicText.GridColorAlternate = System.Drawing.Color.White;
            this.pnlPicText.Location = new System.Drawing.Point(0, 0);
            this.pnlPicText.Margin = new System.Windows.Forms.Padding(0);
            this.pnlPicText.Name = "pnlPicText";
            this.pnlPicText.Size = new System.Drawing.Size(470, 351);
            this.pnlPicText.TabIndex = 5;
            this.pnlPicText.TabStop = false;
            this.pnlPicText.ZoomIncrement = 20;
            // 
            // toolResize
            // 
            this.toolResize.BackColor = System.Drawing.Color.GhostWhite;
            this.toolResize.Dock = System.Windows.Forms.DockStyle.None;
            this.toolResize.GripStyle = System.Windows.Forms.ToolStripGripStyle.Hidden;
            this.toolResize.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmCopy,
            this.tsmSearch,
            this.tsmTrans,
            this.tsmPicBig,
            this.txtPicZoomPercent,
            this.tsmPicSmall,
            this.tsmPicOrigin,
            this.tsmModel,
            this.tsmPicView,
            this.tsmFullScreen});
            this.toolResize.Location = new System.Drawing.Point(56, 314);
            this.toolResize.Name = "toolResize";
            this.toolResize.Size = new System.Drawing.Size(286, 37);
            this.toolResize.TabIndex = 3;
            this.toolResize.Text = "toolStrip1";
            this.toolResize.Visible = false;
            // 
            // tsmCopy
            // 
            this.tsmCopy.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmCopy.Image = global::OCRTools.Properties.Resources.复制;
            this.tsmCopy.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmCopy.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmCopy.Name = "tsmCopy";
            this.tsmCopy.Size = new System.Drawing.Size(34, 34);
            this.tsmCopy.ToolTipText = "复制结果";
            this.tsmCopy.Click += new System.EventHandler(this.tsmCopy_Click);
            // 
            // tsmSearch
            // 
            this.tsmSearch.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmSearch.Image = global::OCRTools.Properties.Resources.搜索;
            this.tsmSearch.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmSearch.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmSearch.Name = "tsmSearch";
            this.tsmSearch.Size = new System.Drawing.Size(30, 34);
            this.tsmSearch.Text = "搜索文字";
            this.tsmSearch.Click += new System.EventHandler(this.tsmSearch_Click);
            // 
            // tsmTrans
            // 
            this.tsmTrans.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmTrans.Image = global::OCRTools.Properties.Resources.翻译_Small;
            this.tsmTrans.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmTrans.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmTrans.Name = "tsmTrans";
            this.tsmTrans.Size = new System.Drawing.Size(34, 34);
            this.tsmTrans.Text = "翻译文字";
            this.tsmTrans.Click += new System.EventHandler(this.tsmTrans_Click);
            // 
            // tsmPicBig
            // 
            this.tsmPicBig.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmPicBig.Image = global::OCRTools.Properties.Resources.放大;
            this.tsmPicBig.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmPicBig.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmPicBig.Name = "tsmPicBig";
            this.tsmPicBig.Size = new System.Drawing.Size(34, 34);
            this.tsmPicBig.Text = "toolStripButton2";
            this.tsmPicBig.ToolTipText = "放大";
            this.tsmPicBig.Click += new System.EventHandler(this.tsmPicBig_Click);
            // 
            // txtPicZoomPercent
            // 
            this.txtPicZoomPercent.Font = new System.Drawing.Font("Microsoft YaHei UI", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtPicZoomPercent.ForeColor = System.Drawing.Color.Black;
            this.txtPicZoomPercent.Name = "txtPicZoomPercent";
            this.txtPicZoomPercent.Size = new System.Drawing.Size(49, 34);
            this.txtPicZoomPercent.Text = "100%";
            // 
            // tsmPicSmall
            // 
            this.tsmPicSmall.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmPicSmall.Image = global::OCRTools.Properties.Resources.缩小;
            this.tsmPicSmall.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmPicSmall.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmPicSmall.Name = "tsmPicSmall";
            this.tsmPicSmall.Size = new System.Drawing.Size(34, 34);
            this.tsmPicSmall.Text = "toolStripButton3";
            this.tsmPicSmall.ToolTipText = "缩小";
            this.tsmPicSmall.Click += new System.EventHandler(this.tsmPicSmall_Click);
            // 
            // tsmPicOrigin
            // 
            this.tsmPicOrigin.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmPicOrigin.Image = global::OCRTools.Properties.Resources.原始;
            this.tsmPicOrigin.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmPicOrigin.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmPicOrigin.Name = "tsmPicOrigin";
            this.tsmPicOrigin.Size = new System.Drawing.Size(34, 34);
            this.tsmPicOrigin.Text = "toolStripButton4";
            this.tsmPicOrigin.ToolTipText = "原始尺寸";
            this.tsmPicOrigin.Click += new System.EventHandler(this.tsmPicOrigin_Click);
            // 
            // tsmModel
            // 
            this.tsmModel.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmModel.Image = global::OCRTools.Properties.Resources.文字模式;
            this.tsmModel.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmModel.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmModel.Name = "tsmModel";
            this.tsmModel.Size = new System.Drawing.Size(34, 34);
            this.tsmModel.Click += new System.EventHandler(this.tsmModel_Click);
            // 
            // tsmPicView
            // 
            this.tsmPicView.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmPicView.Image = global::OCRTools.Properties.Resources.预览;
            this.tsmPicView.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmPicView.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmPicView.Name = "tsmPicView";
            this.tsmPicView.Size = new System.Drawing.Size(34, 34);
            this.tsmPicView.Text = "toolStripButton1";
            this.tsmPicView.ToolTipText = "新窗口预览";
            this.tsmPicView.Visible = false;
            this.tsmPicView.Click += new System.EventHandler(this.tsmPicView_Click);
            // 
            // tsmFullScreen
            // 
            this.tsmFullScreen.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmFullScreen.Image = global::OCRTools.Properties.Resources.全屏;
            this.tsmFullScreen.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmFullScreen.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmFullScreen.Name = "tsmFullScreen";
            this.tsmFullScreen.Size = new System.Drawing.Size(34, 34);
            this.tsmFullScreen.Text = "toolStripButton1";
            this.tsmFullScreen.ToolTipText = "全屏预览";
            this.tsmFullScreen.Visible = false;
            this.tsmFullScreen.Click += new System.EventHandler(this.tsmFullScreen_Click);
            // 
            // dgContent
            // 
            this.dgContent.AllowDrop = true;
            this.dgContent.AllowUserToAddRows = false;
            this.dgContent.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.Black;
            this.dgContent.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            this.dgContent.BackgroundColor = System.Drawing.Color.White;
            this.dgContent.BorderStyle = System.Windows.Forms.BorderStyle.None;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(244)))), ((int)(((byte)(244)))), ((int)(((byte)(244)))));
            dataGridViewCellStyle2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            this.dgContent.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgContent.ColumnHeadersVisible = false;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.DefaultCellStyle = dataGridViewCellStyle3;
            this.dgContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgContent.EnableHeadersVisualStyles = false;
            this.dgContent.Location = new System.Drawing.Point(0, 0);
            this.dgContent.Margin = new System.Windows.Forms.Padding(0);
            this.dgContent.Name = "dgContent";
            this.dgContent.NotShowSequence = false;
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle4.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.RowHeadersDefaultCellStyle = dataGridViewCellStyle4;
            this.dgContent.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.AutoSizeToAllHeaders;
            this.dgContent.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.Black;
            this.dgContent.RowTemplate.Height = 30;
            this.dgContent.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgContent.Size = new System.Drawing.Size(470, 351);
            this.dgContent.TabIndex = 2;
            this.dgContent.TabStop = false;
            // 
            // UcContent
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.toolResize);
            this.Controls.Add(this.txtContent);
            this.Controls.Add(this.wbContent);
            this.Controls.Add(this.pnlPicText);
            this.Controls.Add(this.dgContent);
            this.Margin = new System.Windows.Forms.Padding(0);
            this.Name = "UcContent";
            this.Size = new System.Drawing.Size(470, 351);
            this.toolResize.ResumeLayout(false);
            this.toolResize.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgContent)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.RichTextBox txtContent;
        private DataGridViewEx dgContent;
        private WebBrowser wbContent;
        private PanelPictureView pnlPicText;
        private ToolStrip toolResize;
        private ToolStripButton tsmCopy;
        private ToolStripButton tsmModel;
        private ToolStripButton tsmPicBig;
        private ToolStripButton tsmPicOrigin;
        private ToolStripButton tsmPicSmall;
        private ToolStripButton tsmPicView;
        private ToolStripButton tsmTrans;
        private ToolStripLabel txtPicZoomPercent;
        private ToolStripButton tsmSearch;
        private ToolStripButton tsmFullScreen;
    }
}
