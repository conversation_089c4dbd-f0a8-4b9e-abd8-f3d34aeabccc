﻿#region License Information (GPL v3)

/*
    ShareX - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2020 ShareX Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)

using System.Drawing;

namespace OCRTools
{
    public struct HSB
    {
        private double _hue;
        private double _saturation;
        private double _brightness;
        private int _alpha;

        public double Hue
        {
            get => _hue;
            set => _hue = ColorHelper.ValidColor(value);
        }

        public double Hue360
        {
            get => _hue * 360;
            set => _hue = ColorHelper.ValidColor(value / 360);
        }

        public double Saturation
        {
            get => _saturation;
            set => _saturation = ColorHelper.ValidColor(value);
        }

        public double Saturation100
        {
            get => _saturation * 100;
            set => _saturation = ColorHelper.ValidColor(value / 100);
        }

        public double Brightness
        {
            get => _brightness;
            set => _brightness = ColorHelper.ValidColor(value);
        }

        public double Brightness100
        {
            get => _brightness * 100;
            set => _brightness = ColorHelper.ValidColor(value / 100);
        }

        public int Alpha
        {
            get => _alpha;
            set => _alpha = ColorHelper.ValidColor(value);
        }

        public HSB(double hue, double saturation, double brightness, int alpha = 255) : this()
        {
            Hue = hue;
            Saturation = saturation;
            Brightness = brightness;
            Alpha = alpha;
        }

        public HSB(int hue, int saturation, int brightness, int alpha = 255) : this()
        {
            Hue360 = hue;
            Saturation100 = saturation;
            Brightness100 = brightness;
            Alpha = alpha;
        }

        public HSB(Color color)
        {
            this = ColorHelper.ColorToHsb(color);
        }

        public static implicit operator HSB(Color color)
        {
            return ColorHelper.ColorToHsb(color);
        }

        public static implicit operator Color(HSB color)
        {
            return color.ToColor();
        }

        public static implicit operator RGBA(HSB color)
        {
            return color.ToColor();
        }

        public static implicit operator Cmyk(HSB color)
        {
            return color.ToColor();
        }

        public static bool operator ==(HSB left, HSB right)
        {
            return left.Hue == right.Hue && left.Saturation == right.Saturation && left.Brightness == right.Brightness;
        }

        public static bool operator !=(HSB left, HSB right)
        {
            return !(left == right);
        }

        public override string ToString()
        {
            return string.Format("色调: {0:0.0}度，饱和度: {1:0.0}%，亮度: {2:0.0}%", Hue360, Saturation100, Brightness100);
        }

        public Color ToColor()
        {
            return ColorHelper.HSBToColor(this);
        }
    }
}