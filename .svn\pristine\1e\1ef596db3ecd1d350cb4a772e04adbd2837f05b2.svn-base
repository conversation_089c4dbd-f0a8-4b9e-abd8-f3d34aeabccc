using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolRectangle : ToolObject
    {
        private DrawRectangle drawRectangle;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.current_ToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                drawRectangle = new DrawRectangle(e.X, e.Y, 1, 1);
                AddNewObject(drawArea, drawRectangle);
            }
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (drawRectangle == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                drawRectangle.IsSelected = true;
                var obj = drawRectangle;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    drawRectangle.MoveHandleTo(e.Location, 5, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawRectangle != null)
            {
                StaticValue.current_Rectangle = drawRectangle.Rectangle;
                if (!drawRectangle.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var obj = drawRectangle;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    drawRectangle.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(drawRectangle));
                }
            }
        }
    }
}