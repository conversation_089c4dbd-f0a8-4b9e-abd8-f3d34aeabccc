﻿using System;
using System.Collections;
using System.Globalization;

namespace OCRTools.Common
{
    internal static class ChinaDate
    {
        private static readonly ChineseLunisolarCalendar china = new ChineseLunisolarCalendar();
        private static readonly Hashtable gHoliday = new Hashtable();
        private static readonly Hashtable nHoliday = new Hashtable();

        static ChinaDate()
        {
            //公历节日
            gHoliday.Add("0101", "元旦");
            gHoliday.Add("0214", "情人节");
            gHoliday.Add("0308", "妇女节");
            gHoliday.Add("0312", "植树节");
            gHoliday.Add("0401", "愚人节");
            gHoliday.Add("0501", "劳动节");
            gHoliday.Add("0504", "青年节");
            gHoliday.Add("0520", "情人节");
            gHoliday.Add("0601", "儿童节");
            gHoliday.Add("0701", "建党节");
            gHoliday.Add("0801", "建军节");
            gHoliday.Add("0910", "教师节");
            gHoliday.Add("1001", "国庆节");
            gHoliday.Add("1224", "平安夜");
            gHoliday.Add("1225", "圣诞节");

            //农历节日
            nHoliday.Add("0101", "春节");
            nHoliday.Add("0115", "元宵节");
            nHoliday.Add("0505", "端午节");
            nHoliday.Add("0707", "七夕");
            nHoliday.Add("0815", "中秋节");
            nHoliday.Add("0909", "重阳节");
            nHoliday.Add("1208", "腊八节");
            nHoliday.Add("1224", "小年");
        }

        /// <summary>
        ///     获取农历
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        internal static string GetChinaDate(DateTime dt)
        {
            if (dt > china.MaxSupportedDateTime || dt < china.MinSupportedDateTime)
            {
                //日期范围：1901 年 2 月 19 日 - 2101 年 1 月 28 日
                throw new Exception(string.Format("日期超出范围！必须在{0}到{1}之间！",
                    china.MinSupportedDateTime.ToString("yyyy-MM-dd"), china.MaxSupportedDateTime.ToString("yyyy-MM-dd")));
            }

            var strHoliday = GetHolidayStr(dt);
            if (string.IsNullOrEmpty(strHoliday))
            {
                strHoliday = GetHolidayStr(dt.AddDays(1));
                if (!string.IsNullOrEmpty(strHoliday))
                {
                    strHoliday = string.Format("，明天是[{0}]", strHoliday);
                }
            }
            else
            {
                strHoliday = string.Format("-[{0}]", strHoliday);
            }
            return strHoliday;
        }

        private static string GetHolidayStr(DateTime dt)
        {
            var strHoliday = GetHoliday(dt);
            if (string.IsNullOrEmpty(strHoliday))
            {
                strHoliday = GetChinaHoliday(dt);
            }
            return strHoliday;
        }

        /// <summary>
        /// 获取公历节日
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        private static string GetHoliday(DateTime dt)
        {
            var strReturn = "";
            var g = gHoliday[dt.Month.ToString("00") + dt.Day.ToString("00")];
            if (g != null)
            {
                strReturn = g.ToString();
            }

            return strReturn;
        }

        /// <summary>
        /// 获取农历节日
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        private static string GetChinaHoliday(DateTime dt)
        {
            var strReturn = "";
            var year = china.GetYear(dt);
            var iMonth = china.GetMonth(dt);
            var leapMonth = china.GetLeapMonth(year);
            var iDay = china.GetDayOfMonth(dt);
            if (china.GetDayOfYear(dt) == china.GetDaysInYear(year))
            {
                strReturn = "除夕";
            }
            else if (leapMonth != iMonth)
            {
                if (leapMonth != 0 && iMonth >= leapMonth)
                {
                    iMonth--;
                }
                var n = nHoliday[iMonth.ToString("00") + iDay.ToString("00")];
                if (n != null)
                {
                    if (strReturn == "")
                    {
                        strReturn = n.ToString();
                    }
                    else
                    {
                        strReturn += " " + n;
                    }
                }
            }

            return strReturn;
        }
    }
}
