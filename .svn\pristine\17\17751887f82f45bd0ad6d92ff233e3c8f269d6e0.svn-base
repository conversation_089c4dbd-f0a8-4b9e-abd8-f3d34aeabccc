﻿using System;
using System.Collections;
using System.IO;
using System.IO.Compression;
using System.Text;

namespace OCRTools.GZip
{
    /// <summary>
    /// 压缩文件类
    /// </summary>
    public class GZip
    {
        /// <summary>
        /// Compress
        /// </summary>
        /// <param name="lpSourceFolder">The location of the files to include in the zip file, all files including files in subfolders will be included.</param>
        /// <param name="lpDestFolder">Folder to write the zip file into</param>
        /// <param name="zipFileName">Name of the zip file to write</param>
        public static GZipResult Compress(string lpSourceFolder, string lpDestFolder, string zipFileName)
        {
            return Compress(lpSourceFolder, "*.*", SearchOption.AllDirectories, lpDestFolder, zipFileName, true);
        }

        /// <summary>
        /// Compress
        /// </summary>
        /// <param name="lpSourceFolder">The location of the files to include in the zip file</param>
        /// <param name="searchPattern">Search pattern (ie "*.*" or "*.txt" or "*.gif") to idendify what files in lpSourceFolder to include in the zip file</param>
        /// <param name="searchOption">Only files in lpSourceFolder or include files in subfolders also</param>
        /// <param name="lpDestFolder">Folder to write the zip file into</param>
        /// <param name="zipFileName">Name of the zip file to write</param>
        /// <param name="deleteTempFile">Boolean, true deleted the intermediate temp file, false leaves the temp file in lpDestFolder (for debugging)</param>
        public static GZipResult Compress(string lpSourceFolder, string searchPattern, SearchOption searchOption, string lpDestFolder, string zipFileName, bool deleteTempFile)
        {
            DirectoryInfo di = new DirectoryInfo(lpSourceFolder);
            FileInfo[] files = di.GetFiles("*.*", searchOption);
            return Compress(files, lpSourceFolder, lpDestFolder, zipFileName, deleteTempFile);
        }

        /// <summary>
        /// Compress
        /// </summary>
        /// <param name="files">Array of FileInfo objects to be included in the zip file</param>
        /// <param name="lpBaseFolder">Base folder to use when creating relative paths for the files 
        /// stored in the zip file. For example, if lpBaseFolder is 'C:\zipTest\Files\', and there is a file 
        /// 'C:\zipTest\Files\folder1\sample.txt' in the 'files' array, the relative path for sample.txt 
        /// will be 'folder1/sample.txt'</param>
        /// <param name="lpDestFolder">Folder to write the zip file into</param>
        /// <param name="zipFileName">Name of the zip file to write</param>
        /// <param name="deleteTempFile">Boolean, true deleted the intermediate temp file, false leaves the temp file in lpDestFolder (for debugging)</param>
        public static GZipResult Compress(FileInfo[] files, string lpBaseFolder, string lpDestFolder, string zipFileName, bool deleteTempFile)
        {
            GZipResult result = new GZipResult();

            try
            {
                if (!lpDestFolder.EndsWith("\\"))
                {
                    lpDestFolder += "\\";
                }

                string lpTempFile = lpDestFolder + zipFileName + ".tmp";
                string lpZipFile = lpDestFolder + zipFileName;

                result.TempFile = lpTempFile;
                result.ZipFile = lpZipFile;

                if (files != null && files.Length > 0)
                {
                    CreateTempFile(files, lpBaseFolder, lpTempFile, result);

                    if (result.FileCount > 0)
                    {
                        CreateZipFile(lpTempFile, lpZipFile, result);
                    }

                    // delete the temp file
                    if (deleteTempFile)
                    {
                        File.Delete(lpTempFile);
                        result.TempFileDeleted = true;
                    }
                }
            }
            catch //(Exception ex4)
            {
                result.Errors = true;
            }
            return result;
        }

        private static void CreateZipFile(string lpSourceFile, string lpZipFile, GZipResult result)
        {
            // compress the file into the zip file
            try
            {
                using (var fsOut = new FileStream(lpZipFile, FileMode.Create, FileAccess.Write, FileShare.None))
                {
                    using (var gzip = new GZipStream(fsOut, CompressionMode.Compress, true))
                    {
                        byte[] buffer;
                        using (var fsIn = new FileStream(lpSourceFile, FileMode.Open, FileAccess.Read, FileShare.Read))
                        {
                            buffer = new byte[fsIn.Length];
                            fsIn.Read(buffer, 0, buffer.Length);
                            fsIn.Close();
                        }

                        // compress to the zip file
                        gzip.Write(buffer, 0, buffer.Length);
                    }

                    result.ZipFileSize = fsOut.Length;
                }

                result.CompressionPercent = GetCompressionPercent(result.TempFileSize, result.ZipFileSize);
            }
            catch //(Exception ex1)
            {
                result.Errors = true;
            }
        }

        private static void CreateTempFile(FileInfo[] files, string lpBaseFolder, string lpTempFile, GZipResult result)
        {
            int fileIndex = 0;

            if (files != null && files.Length > 0)
            {
                try
                {
                    result.Files = new GZipFileInfo[files.Length];

                    // open the temp file for writing
                    using (var fsOut = new FileStream(lpTempFile, FileMode.Create, FileAccess.Write, FileShare.None))
                    {
                        foreach (FileInfo fi in files)
                        {
                            try
                            {
                                var gzf = new GZipFileInfo { Index = fileIndex };

                                // read the source file, get its virtual path within the source folder
                                var lpSourceFile = fi.FullName;
                                gzf.LocalPath = lpSourceFile;
                                var vpSourceFile = lpSourceFile.Replace(lpBaseFolder, string.Empty);
                                vpSourceFile = vpSourceFile.Replace("\\", "/");
                                gzf.RelativePath = vpSourceFile;

                                byte[] buffer;
                                using (var fsIn = new FileStream(lpSourceFile, FileMode.Open, FileAccess.Read, FileShare.Read))
                                {
                                    buffer = new byte[fsIn.Length];
                                    fsIn.Read(buffer, 0, buffer.Length);
                                    fsIn.Close();
                                }

                                var fileModDate = fi.LastWriteTimeUtc.ToString();
                                gzf.ModifiedDate = fi.LastWriteTimeUtc;
                                gzf.Length = buffer.Length;

                                var fileHeader = fileIndex.ToString() + "," + vpSourceFile + "," + fileModDate + "," + buffer.Length.ToString() + "\n";
                                var header = Encoding.Default.GetBytes(fileHeader);

                                fsOut.Write(header, 0, header.Length);
                                fsOut.Write(buffer, 0, buffer.Length);
                                fsOut.WriteByte(10); // linefeed

                                gzf.AddedToTempFile = true;

                                // update the result object
                                result.Files[fileIndex] = gzf;

                                // increment the fileIndex
                                fileIndex++;
                            }
                            catch //(Exception ex1)
                            {
                                result.Errors = true;
                            }
                            result.TempFileSize = fsOut.Length;
                        }
                    }
                }
                catch (Exception)
                {
                    result.Errors = true;
                }
            }

            result.FileCount = fileIndex;
        }

        public static GZipResult Decompress(string lpSourceFolder, string lpDestFolder, string zipFileName)
        {
            return Decompress(lpSourceFolder, lpDestFolder, zipFileName, true, true, null, null, 4096);
        }

        public static GZipResult Decompress(string lpSourceFolder, string lpDestFolder, string zipFileName, bool writeFiles, string addExtension)
        {
            return Decompress(lpSourceFolder, lpDestFolder, zipFileName, true, writeFiles, addExtension, null, 4096);
        }

        public static GZipResult Decompress(string lpSrcFolder, string lpDestFolder, string zipFileName,
            bool deleteTempFile, bool writeFiles, string addExtension, Hashtable htFiles, int bufferSize)
        {
            GZipResult result = new GZipResult();

            if (!lpSrcFolder.EndsWith("\\"))
            {
                lpSrcFolder += "\\";
            }
            if (!lpDestFolder.EndsWith("\\"))
            {
                lpDestFolder += "\\";
            }

            string lpTempFile = lpSrcFolder + zipFileName + ".tmp";
            string lpZipFile = lpSrcFolder + zipFileName;

            result.TempFile = lpTempFile;
            result.ZipFile = lpZipFile;

            ArrayList gzfs = new ArrayList();
            var write = false;

            if (string.IsNullOrEmpty(addExtension))
            {
                addExtension = string.Empty;
            }
            else if (!addExtension.StartsWith("."))
            {
                addExtension = "." + addExtension;
            }

            // extract the files from the temp file
            try
            {
                using (var fsTemp = UnzipToTempFile(lpZipFile, lpTempFile, result))
                {
                    if (fsTemp != null)
                    {
                        while (fsTemp.Position != fsTemp.Length)
                        {
                            string line = null;
                            while (string.IsNullOrEmpty(line) && fsTemp.Position != fsTemp.Length)
                            {
                                line = ReadLine(fsTemp);
                            }

                            if (!string.IsNullOrEmpty(line))
                            {
                                var gzf = new GZipFileInfo();
                                if (gzf.ParseFileInfo(line) && gzf.Length > 0)
                                {
                                    gzfs.Add(gzf);
                                    var lpFilePath = lpDestFolder + gzf.RelativePath;
                                    var lpFolder = GetFolder(lpFilePath);
                                    gzf.LocalPath = lpFilePath;

                                    write = false;
                                    if (htFiles == null || htFiles.ContainsKey(gzf.RelativePath))
                                    {
                                        gzf.RestoreRequested = true;
                                        write = writeFiles;
                                    }

                                    if (write)
                                    {
                                        // make sure the folder exists
                                        if (!Directory.Exists(lpFolder))
                                        {
                                            Directory.CreateDirectory(lpFolder);
                                        }

                                        // read from fsTemp and write out the file
                                        gzf.Restored = WriteFile(fsTemp, gzf.Length, lpFilePath + addExtension, bufferSize);
                                    }
                                    else
                                    {
                                        // need to advance fsTemp
                                        fsTemp.Position += gzf.Length;
                                    }
                                }
                            }
                        }
                        fsTemp.Close();
                    }
                }
            }
            catch //(Exception ex3)
            {
                result.Errors = true;
            }

            // delete the temp file
            try
            {
                if (deleteTempFile)
                {
                    File.Delete(lpTempFile);
                    result.TempFileDeleted = true;
                }
            }
            catch //(Exception ex4)
            {
                result.Errors = true;
            }

            result.FileCount = gzfs.Count;
            result.Files = new GZipFileInfo[gzfs.Count];
            gzfs.CopyTo(result.Files);
            return result;
        }

        private static string ReadLine(FileStream fs)
        {
            string line = string.Empty;

            const int bufferSize = 4096;
            byte[] buffer = new byte[bufferSize];
            byte b = 0;
            byte lf = 10;
            int i = 0;

            while (b != lf)
            {
                b = (byte)fs.ReadByte();
                buffer[i] = b;
                i++;
            }

            line = Encoding.Default.GetString(buffer, 0, i - 1);

            return line;
        }

        private static bool WriteFile(FileStream fs, int fileLength, string lpFile, int bufferSize)
        {
            var success = false;

            if (bufferSize == 0 || fileLength < bufferSize)
            {
                bufferSize = fileLength;
            }

            int count;
            int remaining = fileLength;
            int readSize;

            try
            {
                byte[] buffer = new byte[bufferSize];
                using (var fsFile = new FileStream(lpFile, FileMode.Create, FileAccess.Write, FileShare.None))
                {
                    while (remaining > 0)
                    {
                        if (remaining > bufferSize)
                        {
                            readSize = bufferSize;
                        }
                        else
                        {
                            readSize = remaining;
                        }

                        count = fs.Read(buffer, 0, readSize);
                        remaining -= count;

                        if (count == 0)
                        {
                            break;
                        }

                        fsFile.Write(buffer, 0, count);
                        fsFile.Flush();
                    }
                    fsFile.Flush();
                    fsFile.Close();
                }
                File.SetAttributes(lpFile, FileAttributes.Normal);

                success = true;
            }
            catch { }
            return success;
        }

        private static string GetFolder(string lpFilePath)
        {
            string lpFolder = lpFilePath;
            int index = lpFolder.LastIndexOf("\\");
            if (index != -1)
            {
                lpFolder = lpFolder.Substring(0, index + 1);
            }
            return lpFolder;
        }

        private static FileStream UnzipToTempFile(string lpZipFile, string lpTempFile, GZipResult result)
        {
            using (var fsIn = new FileStream(lpZipFile, FileMode.Open, FileAccess.Read, FileShare.Read))
            {
                using (var fsOut = new FileStream(lpTempFile, FileMode.Create, FileAccess.Write, FileShare.None))
                {
                    using (var gzip = new GZipStream(fsIn, CompressionMode.Decompress, true))
                    {
                        const int bufferSize = 4096;
                        var buffer = new byte[bufferSize];
                        int count;

                        try
                        {
                            result.ZipFileSize = fsIn.Length;

                            while (true)
                            {
                                count = gzip.Read(buffer, 0, bufferSize);
                                if (count != 0)
                                {
                                    fsOut.Write(buffer, 0, count);
                                }
                                if (count != bufferSize)
                                {
                                    break;
                                }
                            }
                        }
                        catch //(Exception ex1)
                        {
                            result.Errors = true;
                        }
                        finally
                        {
                            gzip.Close();
                            fsOut.Close();
                            fsIn.Close();
                        }
                    }
                }
            }

            var fsTemp = new FileStream(lpTempFile, FileMode.Open, FileAccess.Read, FileShare.None);
            result.TempFileSize = fsTemp.Length;
            return fsTemp;
        }

        private static int GetCompressionPercent(long tempLen, long zipLen)
        {
            double tmp = tempLen;
            double zip = zipLen;
            double hundred = 100;

            double ratio = (tmp - zip) / tmp;
            double pcnt = ratio * hundred;

            return (int)pcnt;
        }
    }
}
