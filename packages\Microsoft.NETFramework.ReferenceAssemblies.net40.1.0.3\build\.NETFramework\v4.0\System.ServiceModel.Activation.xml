﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ServiceModel.Activation</name>
  </assembly>
  <members>
    <member name="T:System.ServiceModel.ServiceHostingEnvironment">
      <summary>Provides information about the current hosting environment in which  services are running. </summary>
    </member>
    <member name="P:System.ServiceModel.ServiceHostingEnvironment.AspNetCompatibilityEnabled">
      <summary>Gets a value that indicates whether this service is running in the context of the ASP.NET HTTP application pipeline.</summary>
      <returns>true if ASP.NET compatibility is enabled for the current AppDomain; otherwise, false. The default is false.</returns>
    </member>
    <member name="M:System.ServiceModel.ServiceHostingEnvironment.EnsureServiceAvailable(System.String)">
      <summary>Ensures that a service is activated at the provided virtual path.</summary>
      <param name="virtualPath">The virtual path to the service.</param>
      <exception cref="T:System.ServiceModel.EndpointNotFoundException">A service at given virtual path cannot be found.</exception>
      <exception cref="T:System.ServiceModel.ServiceActivationException">The service fails to activate successfully.</exception>
      <exception cref="T:System.InvalidOperationException">The service is not running in the hosted environment.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="virtualPath" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="virtualPath" /> is an absolute URI or not valid.</exception>
    </member>
    <member name="P:System.ServiceModel.ServiceHostingEnvironment.MultipleSiteBindingsEnabled">
      <summary>Gets or sets a value that specifies whether multiple site bindings are allows for the service host.</summary>
      <returns>true if multiple site bindings are enables, otherwise false.</returns>
    </member>
    <member name="T:System.ServiceModel.Activation.HostedTransportConfiguration">
      <summary>Provides transport-specific configuration for transport implementations hosted in Windows Process Activation Service (WAS).</summary>
    </member>
    <member name="M:System.ServiceModel.Activation.HostedTransportConfiguration.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activation.HostedTransportConfiguration" /> class.  </summary>
    </member>
    <member name="M:System.ServiceModel.Activation.HostedTransportConfiguration.GetBaseAddresses(System.String)">
      <summary>When overridden in a derived class, retrieves the base address of the host.</summary>
      <returns>The <see cref="T:System.Uri" /> that contains the base address of the host.</returns>
      <param name="virtualPath">The virtual path to the service.</param>
    </member>
    <member name="T:System.ServiceModel.Activation.ServiceActivationBuildProviderAttribute">
      <summary>Specifies which build provider is used for server XAML (.xamlx) documents.</summary>
    </member>
    <member name="M:System.ServiceModel.Activation.ServiceActivationBuildProviderAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activation.ServiceActivationBuildProviderAttribute" /> class.</summary>
    </member>
    <member name="T:System.ServiceModel.Activation.ServiceBuildProvider">
      <summary>Defines a set of properties and methods for generating source code within the ASP.NET build environment. This class is the  implementation of <see cref="T:System.Web.Compilation.BuildProvider" />.</summary>
    </member>
    <member name="M:System.ServiceModel.Activation.ServiceBuildProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activation.ServiceBuildProvider" /> class. </summary>
    </member>
    <member name="P:System.ServiceModel.Activation.ServiceBuildProvider.CodeCompilerType">
      <summary>Represents the compiler type used by a build provider to generate source code for a custom file type.</summary>
      <returns>A read-only compiler type that represents the code generator, code compiler, and compiler settings used to build source code for the virtual path.</returns>
    </member>
    <member name="M:System.ServiceModel.Activation.ServiceBuildProvider.GenerateCode(System.Web.Compilation.AssemblyBuilder)">
      <summary>Generates source code for the virtual path of the build provider, and adds the source code to a specified assembly builder.</summary>
      <param name="assemblyBuilder">The assembly builder that references the source code generated by the build provider.</param>
    </member>
    <member name="M:System.ServiceModel.Activation.ServiceBuildProvider.GetCustomString(System.CodeDom.Compiler.CompilerResults)">
      <summary>Generates a string to be persisted in the compiled assembly.</summary>
      <returns>A string to be persisted in the build environment.</returns>
      <param name="results">The compilation results for the build provider's virtual path.</param>
    </member>
    <member name="M:System.ServiceModel.Activation.ServiceBuildProvider.GetResultFlags(System.CodeDom.Compiler.CompilerResults)">
      <summary>Returns a value that indicates actions required when a virtual path is built.</summary>
      <returns>The actions required after the virtual path is built within the ASP.NET build environment.</returns>
      <param name="results">The compilation results for the build provider's virtual path.</param>
    </member>
    <member name="P:System.ServiceModel.Activation.ServiceBuildProvider.VirtualPathDependencies">
      <summary>Represents a collection of virtual paths that must be built before the build provider generates code.</summary>
      <returns>The virtual paths that this instance of the build provider is dependent on.</returns>
    </member>
    <member name="T:System.ServiceModel.Activation.ServiceHostFactory">
      <summary>Factory that provides instances of <see cref="T:System.ServiceModel.ServiceHost" /> in managed hosting environments where the host instance is created dynamically in response to incoming messages. A new <see cref="T:System.ServiceModel.Activation.ServiceHostFactory" /> is created for each service hosted.</summary>
    </member>
    <member name="M:System.ServiceModel.Activation.ServiceHostFactory.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activation.ServiceHostFactory" /> class.  </summary>
    </member>
    <member name="M:System.ServiceModel.Activation.ServiceHostFactory.CreateServiceHost(System.String,System.Uri[])">
      <summary>Creates a <see cref="T:System.ServiceModel.ServiceHost" /> with specific base addresses and initializes it with specified data.</summary>
      <returns>A <see cref="T:System.ServiceModel.ServiceHost" /> with specific base addresses.</returns>
      <param name="constructorString">The initialization data passed to the <see cref="T:System.ServiceModel.ServiceHostBase" /> instance being constructed by the factory. </param>
      <param name="baseAddresses">The <see cref="T:System.Array" /> of type <see cref="T:System.Uri" /> that contains the base addresses for the service hosted.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="baseAddress" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">There is no hosting context provided or <paramref name="constructorString" /> is null or empty.</exception>
    </member>
    <member name="M:System.ServiceModel.Activation.ServiceHostFactory.CreateServiceHost(System.Type,System.Uri[])">
      <summary>Creates a <see cref="T:System.ServiceModel.ServiceHost" /> for a specified type of service with a specific base address. </summary>
      <returns>A <see cref="T:System.ServiceModel.ServiceHost" /> for the type of service specified with a specific base address.</returns>
      <param name="serviceType">Specifies the type of service to host. </param>
      <param name="baseAddresses">The <see cref="T:System.Array" /> of type <see cref="T:System.Uri" /> that contains the base addresses for the service hosted.</param>
    </member>
    <member name="T:System.ServiceModel.Activation.ServiceRoute">
      <summary>Enables the creation of service routes over HTTP for WCF Services with support for extension-less base addresses. </summary>
    </member>
    <member name="M:System.ServiceModel.Activation.ServiceRoute.#ctor(System.String,System.ServiceModel.Activation.ServiceHostFactoryBase,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activation.ServiceRoute" /> class with the specified route prefix, service host factory, and service type.</summary>
      <param name="routePrefix">The route prefix.</param>
      <param name="serviceHostFactory">An instance of the <see cref="T:System.ServiceModel.Activation.ServiceHostFactory" /> class. </param>
      <param name="serviceType">The service type.</param>
    </member>
    <member name="T:System.ServiceModel.Activities.Activation.WorkflowServiceHostFactory">
      <summary>A factory that provides instances of <see cref="T:System.ServiceModel.Activities.WorkflowServiceHost" /> in managed hosting environments where the host instance is created dynamically in response to incoming messages. </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Activation.WorkflowServiceHostFactory.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Activities.Activation.WorkflowServiceHostFactory" /> class.  </summary>
    </member>
    <member name="M:System.ServiceModel.Activities.Activation.WorkflowServiceHostFactory.CreateServiceHost(System.String,System.Uri[])">
      <summary>Creates a new service host. </summary>
      <returns>A service host base instance.</returns>
      <param name="constructorString">The name of the XAML file that defines the workflow service or the service type.</param>
      <param name="baseAddresses">A list of addresses that serves as a reference point for other addresses.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Activation.WorkflowServiceHostFactory.CreateWorkflowServiceHost(System.Activities.Activity,System.Uri[])">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowServiceHost" /> class using the specified <see cref="T:System.Activities.Activity" /> and base addresses.</summary>
      <returns>A workflow service host instance.</returns>
      <param name="activity">The workflow definition.</param>
      <param name="baseAddresses">A list of addresses that serves as a reference point for other addresses.</param>
    </member>
    <member name="M:System.ServiceModel.Activities.Activation.WorkflowServiceHostFactory.CreateWorkflowServiceHost(System.ServiceModel.Activities.WorkflowService,System.Uri[])">
      <summary>Creates a new instance of the <see cref="T:System.ServiceModel.Activities.WorkflowServiceHost" /> class with the specified <see cref="T:System.ServiceModel.Activities.WorkflowService" /> and base addresses.</summary>
      <returns>A workflow service host instance.</returns>
      <param name="service">The workflow service that becomes the host.</param>
      <param name="baseAddresses">A list of addresses that serves as a reference point for other addresses.</param>
    </member>
  </members>
</doc>