using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    internal class TextboxEx : TextBox
    {

        private Color borderColor = Color.Gray;
        private UpDownButtonEx upDownButtonExT;

        public TextboxEx()
        {
            Text = 1.ToString();
        }

        public void init(UpDownButtonEx upDownButtonEx)
        {
            upDownButtonExT = upDownButtonEx;
            Text = 1.ToString();
        }

        [DllImport("user32.dll")]
        private static extern IntPtr GetDCEx(IntPtr hwnd, IntPtr hrgnclip, uint fdwOptions);

        [DllImport("user32.dll")]
        private static extern int ReleaseDC(IntPtr hwnd, IntPtr hDC);

        [DllImport("user32.dll")]
        private static extern IntPtr GetWindowDC(IntPtr hWnd);

        protected override void OnTextChanged(EventArgs e)
        {
            var num = 100;
            if (Text != null && Text != "")
            {
                if (int.Parse(Text) > num) Text = (num - 1).ToString();
                if (int.Parse(Text) < 1) Text = 1.ToString();
                if (upDownButtonExT != null)
                {
                    upDownButtonExT.Text = Text;
                    upDownButtonExT.Change();
                }
            }
        }

        protected override void OnKeyPress(KeyPressEventArgs e)
        {
            if (e.KeyChar != '\b' && !char.IsDigit(e.KeyChar)) e.Handled = true;
        }

        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);
            if (m.Msg != 15 && m.Msg != 307) return;
            var windowDC = GetWindowDC(m.HWnd);
            if (windowDC.ToInt32() != 0)
            {
                if (BorderStyle == BorderStyle.FixedSingle)
                {
                    var pen = new Pen(Color.Silver, 1f);
                    var graphics = Graphics.FromHdc(windowDC);
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.DrawRectangle(pen, 0, 0, Width - 1, Height - 1);
                    pen.Dispose();
                }

                m.Result = IntPtr.Zero;
                ReleaseDC(m.HWnd, windowDC);
            }
        }
    }
}