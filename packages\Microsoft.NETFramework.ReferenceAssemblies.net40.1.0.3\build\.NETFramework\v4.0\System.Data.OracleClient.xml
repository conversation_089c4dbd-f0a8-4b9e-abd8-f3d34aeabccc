﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Data.OracleClient</name>
  </assembly>
  <members>
    <member name="T:System.Data.OracleClient.OracleBFile">
      <summary>Represents a managed <see cref="T:System.Data.OracleClient.OracleBFile" /> object designed to work with the Oracle BFILE data type. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleBFile.CanRead">
      <summary>Gets a value indicating whether the BFILE stream can be read.</summary>
      <returns>false if a BFILE is closed or disposed; otherwise true. Always true for <see cref="F:System.Data.OracleClient.OracleBFile.Null" />.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleBFile.CanSeek">
      <summary>Gets a value indicating whether forward-seek and backward-seek operations can be performed.</summary>
      <returns>false if a BFILE is closed or disposed; otherwise true. Always true for <see cref="F:System.Data.OracleClient.OracleBFile.Null" />.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleBFile.CanWrite">
      <summary>Gets a value indicating whether the object supports writing.</summary>
      <returns>Always returns false because the Oracle BFILE data type is read-only.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleBFile.Clone">
      <summary>Creates a copy of this <see cref="T:System.Data.OracleClient.OracleBFile" /> object associated with the same physical file as the original.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleBFile" /> object associated with the same physical file as the original OracleBFile object.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleBFile.Connection">
      <summary>Gets the <see cref="T:System.Data.OracleClient.OracleConnection" /> used by this instance of the <see cref="T:System.Data.OracleClient.OracleBFile" />.</summary>
      <returns>The connection to a data source. The default is a null value.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleBFile.CopyTo(System.Data.OracleClient.OracleLob)">
      <summary>Copies the entire contents of this <see cref="T:System.Data.OracleClient.OracleBFile" /> to the beginning of a destination <see cref="T:System.Data.OracleClient.OracleLob" />.</summary>
      <returns>The number of bytes copied.</returns>
      <param name="destination">The destination <see cref="T:System.Data.OracleClient.OracleLob" /></param>
      <exception cref="T:System.ArgumentNullException">The destination OracleLob is a null object reference. </exception>
      <exception cref="T:System.InvalidOperationException">The destination is a null OracleLob.-or- The connection with which this OracleBFile is associated is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The OracleBFile object is closed or disposed. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleBFile.CopyTo(System.Data.OracleClient.OracleLob,System.Int64)">
      <summary>Copies the entire contents of this <see cref="T:System.Data.OracleClient.OracleBFile" /> to a destination <see cref="T:System.Data.OracleClient.OracleLob" /> at the specified offset.</summary>
      <returns>The number of bytes copied.</returns>
      <param name="destination">The destination <see cref="T:System.Data.OracleClient.OracleLob" />. </param>
      <param name="destinationOffset">The offset to which to copy. </param>
      <exception cref="T:System.ArgumentNullException">The destination OracleLob is a null object reference. </exception>
      <exception cref="T:System.InvalidOperationException">The destination is a null OracleLob.-or- The connection with which this OracleBFile is associated is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The OracleBFile object is closed or disposed. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleBFile.CopyTo(System.Int64,System.Data.OracleClient.OracleLob,System.Int64,System.Int64)">
      <summary>Copies from this <see cref="T:System.Data.OracleClient.OracleBFile" /> to a destination <see cref="T:System.Data.OracleClient.OracleLob" /> with the specified amount of data, the source offset, and the destination offset.</summary>
      <returns>The number of bytes copied.</returns>
      <param name="sourceOffset">The offset from which to copy. </param>
      <param name="destination">The destination <see cref="T:System.Data.OracleClient.OracleLob" />. </param>
      <param name="destinationOffset">The offset to which to copy. </param>
      <param name="amount">The quantity of data, in bytes, to copy. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of the amount parameter is less than zero or greater than 4 gigabytes. </exception>
      <exception cref="T:System.ArgumentNullException">The destination OracleLob is a null object reference. </exception>
      <exception cref="T:System.InvalidOperationException">The destination is a null OracleLob.-or- The connection with which this OracleBFile is associated is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The OracleBFile object is closed or disposed. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleBFile.DirectoryName">
      <summary>Gets the name of the DIRECTORY object, with which an <see cref="T:System.Data.OracleClient.OracleBFile" /> object is associated.</summary>
      <returns>The name of the DIRECTORY object.</returns>
      <exception cref="T:System.ObjectDisposedException">Attempted to call DirectoryName on a closed or disposed OracleBFile object. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleBFile.FileExists">
      <summary>Gets a value indicating whether a physical file containing BFILE data exists in the operating system.</summary>
      <returns>true if a physical file containing BFILE data exists; otherwise false.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Data.OracleClient.OracleBFile" /> object is closed or disposed. </exception>
      <exception cref="T:System.InvalidOperationException">The connection with which a BFILE is associated is closed. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleBFile.FileName">
      <summary>Gets the name of the BFILE without the path.</summary>
      <returns>The name of the BFILE.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Data.OracleClient.OracleBFile" /> object is closed or disposed. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleBFile.Flush">
      <summary>Not currently supported.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleBFile.IsNull">
      <summary>Gets a value that indicates whether the <see cref="T:System.Data.OracleClient.OracleBFile" /> is a <see cref="F:System.Data.OracleClient.OracleBFile.Null" /> stream.</summary>
      <returns>true if the <see cref="T:System.Data.OracleClient.OracleBFile" /> is a <see cref="F:System.Data.OracleClient.OracleBFile.Null" /> stream; otherwise false.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleBFile.Length">
      <summary>Gets a value that returns the length in bytes of the physical file with which the <see cref="T:System.Data.OracleClient.OracleBFile" /> object is associated.</summary>
      <returns>A long value representing the length of the physical file in bytes.</returns>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed or disposed. </exception>
    </member>
    <member name="F:System.Data.OracleClient.OracleBFile.Null">
      <summary>Represents a null <see cref="T:System.Data.OracleClient.OracleBFile" /> object that is not bound to a physical file.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleBFile.Position">
      <summary>Gets the current read position in the <see cref="T:System.Data.OracleClient.OracleBFile" /> stream.</summary>
      <returns>The current position within the <see cref="T:System.Data.OracleClient.OracleBFile" /> stream.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Attempted to set a position with a negative value or greater than the length of the stream. </exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed or disposed. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleBFile.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Reads a sequence of bytes from the current <see cref="T:System.Data.OracleClient.OracleBFile" /> stream and advances the position within the stream by the number of bytes read.</summary>
      <returns>The total number of bytes read into the buffer. This may be less than the number of bytes requested if that many bytes are not currently available, or zero if the end of the file has been reached.</returns>
      <param name="buffer">An array of bytes. When this method returns, the buffer contains the specified byte array with the values between <paramref name="offset" /> and (<paramref name="offset" /> + <paramref name="count" />) replaced by the bytes read from the current source. </param>
      <param name="offset">The zero-based byte offset in <paramref name="buffer" /> at which to begin storing the data read from the current stream. </param>
      <param name="count">The maximum number of bytes to be read from the current stream. </param>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is larger than the buffer length. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is a null reference (Nothing in Visual Basic). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.InvalidOperationException">The connection with which a BFILE is associated is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred. </exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed or disposed. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleBFile.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Sets the position on the current <see cref="T:System.Data.OracleClient.OracleBFile" /> stream.</summary>
      <returns>The new position within the current stream.</returns>
      <param name="offset">A byte offset relative to origin. If <paramref name="offset" /> is negative, the new position will precede the position specified by <paramref name="origin" /> by the number of bytes specified by <paramref name="offset" />. If <paramref name="offset" /> is zero, the new position will be the position specified by <paramref name="origin" />. If <paramref name="offset" /> is positive, the new position will follow the position specified by <paramref name="origin" /> by the number of bytes specified by <paramref name="offset" />. </param>
      <param name="origin">A value of type System.IO.SeekOrigin indicating the reference point used to obtain the new position. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Attempted to set a position with a negative value or greater than the length of the stream. </exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed or disposed. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleBFile.SetFileName(System.String,System.String)">
      <summary>Binds the <see cref="T:System.Data.OracleClient.OracleBFile" /> object to a different file in the operating system.</summary>
      <param name="directory">The alias of the directory object that contains a physical file. </param>
      <param name="file">The name of the file in the operating system. </param>
      <exception cref="T:System.InvalidOperationException">The operation must be within a transaction. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleBFile.SetLength(System.Int64)">
      <summary>Not currently supported.</summary>
      <param name="value">Not currently supported.</param>
      <exception cref="T:System.NotSupportedException">The exception that is thrown when an invoked method is not supported, or when there is an attempt to read, seek, or write to a stream that does not support the invoked functionality.</exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleBFile.Value">
      <summary>Gets an <see cref="T:System.Array" /> of type <see cref="T:System.Byte" /> that contains the <see cref="T:System.Data.OracleClient.OracleBFile" /> data.</summary>
      <returns>An <see cref="T:System.Array" /> of type <see cref="T:System.Byte" /> that contains the <see cref="T:System.Data.OracleClient.OracleBFile" /> data.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleBFile.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Not currently supported.</summary>
      <param name="buffer">Not currently supported.</param>
      <param name="offset">Not currently supported.</param>
      <param name="count">Not currently supported.</param>
      <exception cref="T:System.NotSupportedException">The exception that is thrown when an invoked method is not supported, or when there is an attempt to read, seek, or write to a stream that does not support the invoked functionality.</exception>
    </member>
    <member name="T:System.Data.OracleClient.OracleBinary">
      <summary>Represents a variable-length stream of binary data to be stored in or retrieved from a database.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the OracleBinary structure, setting the <see cref="P:System.Data.OracleClient.OracleBinary.Value" /> property to the contents of the supplied byte array.</summary>
      <param name="b">The byte array to be stored in the <see cref="P:System.Data.OracleClient.OracleBinary.Value" /> property. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.CompareTo(System.Object)">
      <summary>Compares this <see cref="T:System.Data.OracleClient.OracleBinary" /> object to the supplied object and returns an indication of their relative values.</summary>
      <returns>A signed number indicating the relative values of this OracleBinary structure and the object.Return Value Condition Less than zero The value of this OracleBinary object is less than the object. Zero This OracleBinary object is the same as the object. Greater than zero This OracleBinary object is greater than the object.-or- The object is a null reference. </returns>
      <param name="obj">The object to be compared to this OracleBinary structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.Concat(System.Data.OracleClient.OracleBinary,System.Data.OracleClient.OracleBinary)">
      <summary>Concatenates two <see cref="T:System.Data.OracleClient.OracleBinary" /> structures to create a new OracleBinary structure.</summary>
      <returns>An OracleBinary structure with the concatenated values of the <paramref name="x" /> and <paramref name="y" /> parameters.</returns>
      <param name="x">An OracleBinary structure. </param>
      <param name="y">An OracleBinary structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.Equals(System.Data.OracleClient.OracleBinary,System.Data.OracleClient.OracleBinary)">
      <summary>Compares two <see cref="T:System.Data.OracleClient.OracleBinary" /> structures to determine if they are equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are not equal. If either instance of OracleBinary is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the OracleBoolean will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An OracleBinary structure. </param>
      <param name="y">An OracleBinary structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.Equals(System.Object)">
      <summary>Compares the supplied object parameter to the <see cref="P:System.Data.OracleClient.OracleBinary.Value" /> property of the <see cref="T:System.Data.OracleClient.OracleBinary" /> object.</summary>
      <returns>true if object is an instance of <see cref="T:System.Data.OracleClient.OracleBinary" /> and the two are equal; otherwise false.</returns>
      <param name="value">The object to be compared. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.GetHashCode">
      <summary>Returns the hash code for this <see cref="T:System.Data.OracleClient.OracleBinary" /> structure.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.GreaterThan(System.Data.OracleClient.OracleBinary,System.Data.OracleClient.OracleBinary)">
      <summary>Compares two <see cref="T:System.Data.OracleClient.OracleBinary" /> structures to determine if the first is greater than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of OracleBinary is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the OracleBoolean will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An OracleBinary structure. </param>
      <param name="y">An OracleBinary structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.GreaterThanOrEqual(System.Data.OracleClient.OracleBinary,System.Data.OracleClient.OracleBinary)">
      <summary>Compares two <see cref="T:System.Data.OracleClient.OracleBinary" /> structures to determine if the first is greater than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of OracleBinary is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the OracleBoolean will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An OracleBinary structure. </param>
      <param name="y">An OracleBinary structure. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleBinary.IsNull">
      <summary>Gets a value indicating whether the <see cref="P:System.Data.OracleClient.OracleBinary.Value" /> property of the <see cref="T:System.Data.OracleClient.OracleBinary" /> structure is null.</summary>
      <returns>true if Value is null, otherwise false.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleBinary.Item(System.Int32)">
      <summary>Gets the single byte from the Value property located at the position indicated by the integer parameter, <paramref name="index" />. If <paramref name="index" /> indicates a position beyond the end of the byte array, an exception is raised.</summary>
      <returns>The byte located at the position indicated by the integer parameter.</returns>
      <param name="index">The position of the byte to be retrieved. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleBinary.Length">
      <summary>Gets the length in bytes of the <see cref="P:System.Data.OracleClient.OracleBinary.Value" /> property. This property is read-only.</summary>
      <returns>The length of the binary data in the Value property.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.LessThan(System.Data.OracleClient.OracleBinary,System.Data.OracleClient.OracleBinary)">
      <summary>Compares two <see cref="T:System.Data.OracleClient.OracleBinary" /> structures to determine if the first is less than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of OracleBinary is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the OracleBoolean will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An OracleBinary structure. </param>
      <param name="y">An OracleBinary structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.LessThanOrEqual(System.Data.OracleClient.OracleBinary,System.Data.OracleClient.OracleBinary)">
      <summary>Compares two <see cref="T:System.Data.OracleClient.OracleBinary" /> structures to determine if the first is less than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of OracleBinary is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the OracleBoolean will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An OracleBinary structure. </param>
      <param name="y">An OracleBinary structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.NotEquals(System.Data.OracleClient.OracleBinary,System.Data.OracleClient.OracleBinary)">
      <summary>Compares two <see cref="T:System.Data.OracleClient.OracleBinary" /> structures to determine if they are not equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are not equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are equal. If either instance of OracleBinary is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the OracleBoolean will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An OracleBinary structure. </param>
      <param name="y">An OracleBinary structure. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleBinary.Null">
      <summary>Represents a null value that can be assigned to the <see cref="P:System.Data.OracleClient.OracleBinary.Value" /> property of an <see cref="T:System.Data.OracleClient.OracleBinary" /> structure.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.op_Addition(System.Data.OracleClient.OracleBinary,System.Data.OracleClient.OracleBinary)">
      <summary>Concatenates the two <see cref="T:System.Data.OracleClient.OracleBinary" /> parameters to create a new OracleBinary structure.</summary>
      <returns>The concatenated values of the <paramref name="x" /> and <paramref name="y" /> parameters.</returns>
      <param name="x">An OracleBinary object. </param>
      <param name="y">An OracleBinary object. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.op_Equality(System.Data.OracleClient.OracleBinary,System.Data.OracleClient.OracleBinary)">
      <summary>Compares two <see cref="T:System.Data.OracleClient.OracleBinary" /> structures to determine if they are equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are not equal. If either instance of OracleBinary is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the OracleBoolean will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An OracleBinary object. </param>
      <param name="y">An OracleBinary object. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.op_Explicit(System.Data.OracleClient.OracleBinary)~System.Byte[]">
      <summary>Gets the contents of the <see cref="P:System.Data.OracleClient.OracleBinary.Value" /> property of the <see cref="T:System.Data.OracleClient.OracleBinary" /> parameter as an array of bytes.</summary>
      <returns>An array of bytes.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleBinary" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.op_GreaterThan(System.Data.OracleClient.OracleBinary,System.Data.OracleClient.OracleBinary)">
      <summary>Compares two <see cref="T:System.Data.OracleClient.OracleBinary" /> structures to determine if the first is greater than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of OracleBinary is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the OracleBoolean will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An OracleBinary object. </param>
      <param name="y">An OracleBinary object. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.op_GreaterThanOrEqual(System.Data.OracleClient.OracleBinary,System.Data.OracleClient.OracleBinary)">
      <summary>Compares two <see cref="T:System.Data.OracleClient.OracleBinary" /> structures to determine if the first is greater than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of OracleBinary is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the OracleBoolean will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An OracleBinary object. </param>
      <param name="y">An OracleBinary object. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.op_Implicit(System.Byte[])~System.Data.OracleClient.OracleBinary">
      <summary>Converts an array of bytes to an <see cref="T:System.Data.OracleClient.OracleBinary" /> structure.</summary>
      <returns>An OracleBinary structure that represents the converted array of bytes.</returns>
      <param name="b">The array of bytes to be converted. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.op_Inequality(System.Data.OracleClient.OracleBinary,System.Data.OracleClient.OracleBinary)">
      <summary>Compares two <see cref="T:System.Data.OracleClient.OracleBinary" /> structures to determine if they are equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are not equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are equal. If either instance of OracleBinary is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the OracleBoolean will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An OracleBinary object. </param>
      <param name="y">An OracleBinary object. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.op_LessThan(System.Data.OracleClient.OracleBinary,System.Data.OracleClient.OracleBinary)">
      <summary>Compares two <see cref="T:System.Data.OracleClient.OracleBinary" /> structures to determine if the first is less than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of OracleBinary is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the OracleBoolean will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An OracleBinary object. </param>
      <param name="y">An OracleBinary object. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBinary.op_LessThanOrEqual(System.Data.OracleClient.OracleBinary,System.Data.OracleClient.OracleBinary)">
      <summary>Compares two <see cref="T:System.Data.OracleClient.OracleBinary" /> structures to determine if the first is less than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of OracleBinary is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the OracleBoolean will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An OracleBinary object. </param>
      <param name="y">An OracleBinary object. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleBinary.Value">
      <summary>Gets the value of the <see cref="T:System.Data.OracleClient.OracleBinary" /> structure. This property is read-only.</summary>
      <returns>The value of the OracleBinary structure.</returns>
    </member>
    <member name="T:System.Data.OracleClient.OracleBoolean">
      <summary>Represents the value returned from a database comparison operation between Oracle data types, and exposes methods used to perform data type conversions.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure using the specified Boolean.</summary>
      <param name="value">The Boolean to be used as the initial value of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure using the specified integer.</summary>
      <param name="value">The integer to be used as the initial value of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.And(System.Data.OracleClient.OracleBoolean,System.Data.OracleClient.OracleBoolean)">
      <summary>Computes the bitwise AND of two specified <see cref="T:System.Data.OracleClient.OracleBoolean" /> structures.</summary>
      <returns>The result of the logical AND operation as shown in the following table.Value of <paramref name="x" />Value of <paramref name="y" />Result truetruetruetruefalsefalsefalsefalsefalsetrueunknownunknownfalseunknownfalseunknownunknownunknown</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.CompareTo(System.Object)">
      <summary>Compares this <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure to a specified object and returns an indication of their relative values.</summary>
      <returns>A signed number indicating the relative values of the instance and value.Value Description A negative integer This instance is less than <paramref name="value" />. Zero This instance is equal to <paramref name="value" />. A positive integer This instance is greater than <paramref name="value" />.-or- <paramref name="value" /> is a null reference (Nothing in Visual Basic). </returns>
      <param name="obj">An object to compare, or a null reference (Nothing in Visual Basic). </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.Equals(System.Data.OracleClient.OracleBoolean,System.Data.OracleClient.OracleBoolean)">
      <summary>Compares two <see cref="T:System.Data.OracleClient.OracleBoolean" /> structures to determine if they are equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are not equal. If either instance of <see cref="T:System.Data.OracleClient.OracleBoolean" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.Equals(System.Object)">
      <summary>Compares the supplied object parameter to the <see cref="T:System.Data.OracleClient.OracleBoolean" />.</summary>
      <returns>true if object is an instance of <see cref="T:System.Data.OracleClient.OracleBoolean" /> and the two are equal; otherwise false.</returns>
      <param name="value">The object to be compared. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleBoolean.False">
      <summary>Represents a false value that can be assigned to the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> property of an instance of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleBoolean.IsFalse">
      <summary>Indicates whether the current <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.False" />.</summary>
      <returns>true if Value is False, otherwise false.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleBoolean.IsNull">
      <summary>Indicates whether or not the value of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure is null.</summary>
      <returns>true if the <see cref="T:System.Data.OracleClient.OracleBoolean" /> value of the structure is Null, otherwise false.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleBoolean.IsTrue">
      <summary>Indicates whether the current <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.True" />.</summary>
      <returns>true if Value is True, otherwise false.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.NotEquals(System.Data.OracleClient.OracleBoolean,System.Data.OracleClient.OracleBoolean)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleBoolean" /> to determine if they are not equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are not equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are equal. If either instance of <see cref="T:System.Data.OracleClient.OracleBoolean" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleBoolean.Null">
      <summary>Represents a null value that can be assigned to the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> property of an instance of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleBoolean.One">
      <summary>Represents a value of one that can be assigned to the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> property of an instance of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.OnesComplement(System.Data.OracleClient.OracleBoolean)">
      <summary>Performs a ones complement operation on the supplied <see cref="T:System.Data.OracleClient.OracleBoolean" /> structures.</summary>
      <returns>The one's complement of the supplied <see cref="T:System.Data.OracleClient.OracleBoolean" />. If the Boolean contains a null value the result also is a null value.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.op_BitwiseAnd(System.Data.OracleClient.OracleBoolean,System.Data.OracleClient.OracleBoolean)">
      <summary>Computes the bitwise AND of two specified <see cref="T:System.Data.OracleClient.OracleBoolean" /> structures.</summary>
      <returns>The result of the logical AND operation as shown in the following table.Value of <paramref name="x" />Value of <paramref name="y" />Result truetruetruetruefalsefalsefalsefalsefalsetrueunknownunknownfalseunknownunknownunknownunknownunknown</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.op_BitwiseOr(System.Data.OracleClient.OracleBoolean,System.Data.OracleClient.OracleBoolean)">
      <summary>Computes the bitwise OR of its two <see cref="T:System.Data.OracleClient.OracleBoolean" /> operands.</summary>
      <returns>The result of the bitwise OR operation as shown in the following table.Value of <paramref name="x" />Value of <paramref name="y" />Result truetruetruetruefalsetruefalsefalsefalsetrueunknowntruefalseunknownunknownunknownunknownunknown</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.op_Equality(System.Data.OracleClient.OracleBoolean,System.Data.OracleClient.OracleBoolean)">
      <summary>Compares two instances of an <see cref="T:System.Data.OracleClient.OracleBoolean" /> for equality.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are not equal. If either instance of <see cref="T:System.Data.OracleClient.OracleBoolean" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleBoolean" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleBoolean" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.op_ExclusiveOr(System.Data.OracleClient.OracleBoolean,System.Data.OracleClient.OracleBoolean)">
      <summary>Performs a bitwise exclusive-OR operation on the supplied <see cref="T:System.Data.OracleClient.OracleBoolean" /> parameters.</summary>
      <returns>The results of the bitwise XOR operation as shown in the following table.Value of <paramref name="x" />Value of <paramref name="y" />Result truetruefalsetruefalsetruefalsefalsefalsetrueunknownunknownfalseunknownunknownunknownunknownunknown</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.op_Explicit(System.Data.OracleClient.OracleBoolean)~System.Boolean">
      <summary>Converts an <see cref="T:System.Data.OracleClient.OracleBoolean" /> to a Boolean.</summary>
      <returns>A Boolean set to the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> to convert. </param>
      <exception cref="T:System.NullReferenceException">The <see cref="T:System.Data.OracleClient.OracleBoolean" /> contains a null value. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.op_Explicit(System.Data.OracleClient.OracleNumber)~System.Data.OracleClient.OracleBoolean">
      <summary>Converts the <see cref="T:System.Data.OracleClient.OracleNumber" /> parameter to an <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure whose value equals the <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property of the <see cref="T:System.Data.OracleClient.OracleNumber" /> parameter.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> to be converted to an <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.op_Explicit(System.String)~System.Data.OracleClient.OracleBoolean">
      <summary>Converts a string to an <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure whose value equals the <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> of the <see cref="T:System.Data.OracleClient.OracleNumber" /> parameter.</returns>
      <param name="x">A string to be converted to an <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.op_False(System.Data.OracleClient.OracleBoolean)">
      <summary>Used to test the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> to determine whether it is false.</summary>
      <returns>true if the supplied parameter <see cref="T:System.Data.OracleClient.OracleBoolean" /> is false; otherwise false.</returns>
      <param name="x">The <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure to be tested. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.op_Implicit(System.Boolean)~System.Data.OracleClient.OracleBoolean">
      <summary>Converts a Boolean value to an <see cref="T:System.Data.OracleClient.OracleBoolean" />.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> value containing 0 or 1.</returns>
      <param name="x">A Boolean value to be converted to <see cref="T:System.Data.OracleClient.OracleBoolean" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.op_Inequality(System.Data.OracleClient.OracleBoolean,System.Data.OracleClient.OracleBoolean)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleBoolean" /> for inequality.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are not equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are equal. If either instance of <see cref="T:System.Data.OracleClient.OracleBoolean" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleBoolean" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleBoolean" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.op_LogicalNot(System.Data.OracleClient.OracleBoolean)">
      <summary>Performs a NOT operation on an <see cref="T:System.Data.OracleClient.OracleBoolean" />.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> with the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /><see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if argument was true, <see cref="F:System.Data.OracleClient.OracleBoolean.Null" /> if argument was null, and <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> otherwise.</returns>
      <param name="x">The <see cref="T:System.Data.OracleClient.OracleBoolean" /> on which the NOT operation is performed. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.op_OnesComplement(System.Data.OracleClient.OracleBoolean)">
      <summary>Performs a one's complement operation on the specified <see cref="T:System.Data.OracleClient.OracleBoolean" />.</summary>
      <returns>The one's complement of the specified <see cref="T:System.Data.OracleClient.OracleBoolean" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.op_True(System.Data.OracleClient.OracleBoolean)">
      <summary>Used to test the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> to determine whether it is true.</summary>
      <returns>true if the supplied parameter <see cref="T:System.Data.OracleClient.OracleBoolean" /> is true; otherwise false.</returns>
      <param name="x">The <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure to be tested. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.Or(System.Data.OracleClient.OracleBoolean,System.Data.OracleClient.OracleBoolean)">
      <summary>Performs a bitwise OR operation on the two specified <see cref="T:System.Data.OracleClient.OracleBoolean" /> structures.</summary>
      <returns>The result of the bitwise OR operation as shown in the following table.Value of <paramref name="x" />Value of <paramref name="y" />Result truetruetruetruefalsetruefalsefalsefalsetrueunknowntruefalseunknownunknownunknownunknownunknown</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.Parse(System.String)">
      <summary>Converts the specified <see cref="T:System.String" /> representation of a logical value to its <see cref="T:System.Data.OracleClient.OracleBoolean" /> equivalent.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure containing the parsed value.</returns>
      <param name="s">The <see cref="T:System.String" /> to be converted. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.ToString">
      <summary>Converts the current <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> to a string.</summary>
      <returns>A string containing the value of the <see cref="T:System.Data.OracleClient.OracleBoolean" />. If the value is null, the string contains a null value.</returns>
    </member>
    <member name="F:System.Data.OracleClient.OracleBoolean.True">
      <summary>Represents a true value that can be assigned to the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> property of an instance of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleBoolean.Value">
      <summary>Gets the <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure's value. This property is read-only.</summary>
      <returns>true if the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.True" />; otherwise false.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleBoolean.Xor(System.Data.OracleClient.OracleBoolean,System.Data.OracleClient.OracleBoolean)">
      <summary>Performs a bitwise exclusive-OR operation on the supplied parameters.</summary>
      <returns>The results of the logical XOR operation as shown in the following table.Value of <paramref name="x" />Value of <paramref name="y" />Result truetruefalsetruefalsetruefalsefalsefalsetrueunknownunknownfalseunknownunknownunknownunknownunknown</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleBoolean.Zero">
      <summary>Represents a value of zero that can be assigned to the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> property of an instance of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> structure.</summary>
    </member>
    <member name="T:System.Data.OracleClient.OracleClientFactory">
      <summary>Represents a set of methods for creating instances of the Oracle provider's implementation of the data source classes. </summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleClientFactory.CreateCommand">
      <summary>Returns a strongly typed <see cref="T:System.Data.Common.DbCommand" /> instance.</summary>
      <returns>A new strongly typed instance of <see cref="T:System.Data.Common.DbCommand" />.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleClientFactory.CreateCommandBuilder">
      <summary>Returns a strongly typed <see cref="T:System.Data.Common.DbCommandBuilder" /> instance.</summary>
      <returns>A new strongly typed instance of <see cref="T:System.Data.Common.DbCommandBuilder" />.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleClientFactory.CreateConnection">
      <summary>Returns a strongly typed <see cref="T:System.Data.Common.DbConnection" /> instance.</summary>
      <returns>A new strongly typed instance of <see cref="T:System.Data.Common.DbConnection" />. </returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleClientFactory.CreateConnectionStringBuilder">
      <summary>Returns a strongly typed <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> instance.</summary>
      <returns>A new strongly typed instance of <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleClientFactory.CreateDataAdapter">
      <summary>Returns a strongly typed <see cref="T:System.Data.Common.DbDataAdapter" /> instance.</summary>
      <returns>A new strongly typed instance of <see cref="T:System.Data.Common.DbDataAdapter" />.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleClientFactory.CreateParameter">
      <summary>Returns a strongly typed <see cref="T:System.Data.Common.DbParameter" /> instance.</summary>
      <returns>A new strongly typed instance of <see cref="T:System.Data.Common.DbParameter" />.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleClientFactory.CreatePermission(System.Security.Permissions.PermissionState)">
      <summary>Returns a strongly typed <see cref="T:System.Security.CodeAccessPermission" /> instance.</summary>
      <returns>A strongly typed instance of <see cref="T:System.Security.CodeAccessPermission" />.</returns>
      <param name="state">A member of the <see cref="T:System.Security.Permissions.PermissionState" /> enumeration.</param>
    </member>
    <member name="F:System.Data.OracleClient.OracleClientFactory.Instance">
      <summary>Gets an instance of the <see cref="T:System.Data.OracleClient.OracleClientFactory" />, which can be used to retrieve strongly typed data objects.</summary>
    </member>
    <member name="T:System.Data.OracleClient.OracleCommand">
      <summary>Represents an SQL statement or stored procedure to execute against a database. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleCommand" />.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleCommand" /> class with the text of the query.</summary>
      <param name="commandText">The text of the query. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.#ctor(System.String,System.Data.OracleClient.OracleConnection)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleCommand" /> class with the text of the query and an <see cref="T:System.Data.OracleClient.OracleConnection" /> object.</summary>
      <param name="commandText">The text of the query. </param>
      <param name="connection">An <see cref="T:System.Data.OracleClient.OracleConnection" /> object that represents the connection to a database. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.#ctor(System.String,System.Data.OracleClient.OracleConnection,System.Data.OracleClient.OracleTransaction)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleCommand" /> class with the text of the query, an <see cref="T:System.Data.OracleClient.OracleConnection" /> object, and an <see cref="T:System.Data.OracleClient.OracleTransaction" />.</summary>
      <param name="commandText">The text of the query. </param>
      <param name="connection">An <see cref="T:System.Data.OracleClient.OracleConnection" /> object that represents the connection to a database. </param>
      <param name="tx">The <see cref="T:System.Data.OracleClient.OracleTransaction" /> in which the <see cref="T:System.Data.OracleClient.OracleCommand" /> executes. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.Cancel">
      <summary>Attempts to cancel the execution of an <see cref="T:System.Data.OracleClient.OracleCommand" />.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.Clone">
      <summary>Creates a copy of this <see cref="T:System.Data.OracleClient.OracleCommand" /> object.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleCommand" /> object in which all property values are the same as the original.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleCommand.CommandText">
      <summary>Gets or sets the SQL statement or stored procedure to execute against the database.</summary>
      <returns>The SQL statement or stored procedure to execute. The default value is an empty string ("").</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleCommand.CommandTimeout">
      <summary>Gets or sets the wait time before terminating the attempt to execute a command and generating an error.</summary>
      <returns>The time (in seconds) to wait for the command to execute. The default value is 30 seconds.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleCommand.CommandType">
      <summary>Gets or sets a value indicating how the <see cref="P:System.Data.OracleClient.OracleCommand.CommandText" /> property is interpreted.</summary>
      <returns>One of the <see cref="T:System.Data.CommandType" /> values. The default is Text.</returns>
      <exception cref="T:System.ArgumentException">The value was not a valid <see cref="T:System.Data.CommandType" />. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleCommand.Connection">
      <summary>Gets or sets the <see cref="T:System.Data.OracleClient.OracleConnection" /> used by this instance of the <see cref="T:System.Data.OracleClient.OracleCommand" />.</summary>
      <returns>The connection to a data source. The default is a null value.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Data.OracleClient.OracleCommand.Connection" /> property was changed while a transaction was in progress. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.CreateParameter">
      <summary>Creates a new instance of an <see cref="T:System.Data.OracleClient.OracleParameter" /> object.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleParameter" /> object.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleCommand.DesignTimeVisible">
      <summary>Gets or sets a value indicating whether the command object should be visible in a customized interface control.</summary>
      <returns>true, if the command object should be visible in a control; otherwise false. The default is true.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.ExecuteNonQuery">
      <summary>Executes an SQL statement against the <see cref="P:System.Data.OracleClient.OracleCommand.Connection" /> and returns the number of rows affected.</summary>
      <returns>For UPDATE, INSERT, and DELETE statements, the return value is the number of rows affected by the command. For CREATE TABLE and DROP TABLE statements, the return value is 0. For all other types of statements, the return value is -1.</returns>
      <exception cref="T:System.InvalidOperationException">The connection does not exist.-or- The connection is not open. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.ExecuteOracleNonQuery(System.Data.OracleClient.OracleString@)">
      <summary>Executes an SQL statement against the <see cref="P:System.Data.OracleClient.OracleCommand.Connection" /> and returns the number of rows affected.</summary>
      <returns>For UPDATE, INSERT, and DELETE statements, the return value is the number of rows affected by the command. For CREATE TABLE and DROP TABLE statements, the return value is 0. For all other types of statements, the return value is -1.</returns>
      <param name="rowid">A base64 string representation of the actual row ID in the server. </param>
      <exception cref="T:System.InvalidOperationException">The connection does not exist.-or- The connection is not open. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.ExecuteOracleScalar">
      <summary>Executes the query, and returns the first column of the first row in the result set returned by the query as an Oracle-specific data type. Extra columns or rows are ignored.</summary>
      <returns>The first column of the first row in the result set as an Oracle-specific data type, or a null reference if the result is a REF CURSOR.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.ExecuteReader">
      <summary>Sends the <see cref="P:System.Data.OracleClient.OracleCommand.CommandText" /> to the <see cref="P:System.Data.OracleClient.OracleCommand.Connection" /> and builds an <see cref="T:System.Data.OracleClient.OracleDataReader" />.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleDataReader" /> object.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.ExecuteReader(System.Data.CommandBehavior)">
      <summary>Sends the <see cref="P:System.Data.OracleClient.OracleCommand.CommandText" /> to the <see cref="P:System.Data.OracleClient.OracleCommand.Connection" />, and builds an <see cref="T:System.Data.OracleClient.OracleDataReader" /> using one of the <see cref="T:System.Data.CommandBehavior" /> values.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleDataReader" /> object.</returns>
      <param name="behavior">One of the <see cref="T:System.Data.CommandBehavior" /> values. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.ExecuteScalar">
      <summary>Executes the query, and returns the first column of the first row in the result set returned by the query as a .NET Framework data type. Extra columns or rows are ignored.</summary>
      <returns>The first column of the first row in the result set as a .NET Framework data type, or a null reference if the result set is empty or the result is a REF CURSOR.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleCommand.Parameters">
      <summary>Gets the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</summary>
      <returns>The parameters of the SQL statement or stored procedure. The default is an empty collection.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.Prepare">
      <summary>Creates a prepared (or compiled) version of the command at the data source.</summary>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Data.OracleClient.OracleCommand.Connection" /> is not set.-or- The <see cref="P:System.Data.OracleClient.OracleCommand.Connection" /> is not <see cref="M:System.Data.OracleClient.OracleConnection.Open" />. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommand.ResetCommandTimeout">
      <summary>Resets the <see cref="P:System.Data.OracleClient.OracleCommand.CommandTimeout" /> property to the default value.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleCommand.Transaction">
      <summary>Gets or sets the <see cref="T:System.Data.OracleClient.OracleTransaction" /> within which the <see cref="T:System.Data.OracleClient.OracleCommand" /> executes.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleTransaction" />. The default is a null value.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleCommand.UpdatedRowSource">
      <summary>Gets or sets a value that specifies how the Update method should apply command results to the <see cref="T:System.Data.DataRow" />.</summary>
      <returns>One of the <see cref="T:System.Data.UpdateRowSource" /> values.</returns>
      <exception cref="T:System.ArgumentException">The value entered was not one of the <see cref="T:System.Data.UpdateRowSource" /> values.</exception>
    </member>
    <member name="T:System.Data.OracleClient.OracleCommandBuilder">
      <summary>Automatically generates single-table commands used to reconcile changes made to a <see cref="T:System.Data.DataSet" /> with the associated database. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommandBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleCommandBuilder" />.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommandBuilder.#ctor(System.Data.OracleClient.OracleDataAdapter)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleCommandBuilder" /> class with the associated <see cref="T:System.Data.OracleClient.OracleDataAdapter" /> object.</summary>
      <param name="adapter">An <see cref="T:System.Data.OracleClient.OracleDataAdapter" /> object to associate with this <see cref="T:System.Data.OracleClient.OracleCommandBuilder" />. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleCommandBuilder.CatalogLocation">
      <summary>Sets or gets the <see cref="T:System.Data.Common.CatalogLocation" /> for an instance of the <see cref="T:System.Data.Common.DbCommandBuilder" /> class.</summary>
      <returns>A <see cref="T:System.Data.Common.CatalogLocation" /> object.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleCommandBuilder.CatalogSeparator">
      <summary>Sets or gets a string used as the catalog separator for an instance of the <see cref="T:System.Data.Common.DbCommandBuilder" /> class.</summary>
      <returns>A string indicating the catalog separator for use with an instance of the <see cref="T:System.Data.Common.DbCommandBuilder" /> class.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleCommandBuilder.DataAdapter">
      <summary>Gets or sets an <see cref="T:System.Data.OracleClient.OracleDataAdapter" /> object for which this <see cref="T:System.Data.OracleClient.OracleCommandBuilder" /> object will generate SQL statements.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleDataAdapter" /> object that is associated with this <see cref="T:System.Data.OracleClient.OracleCommandBuilder" />.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommandBuilder.DeriveParameters(System.Data.OracleClient.OracleCommand)">
      <summary>Retrieves parameter information from the stored procedure specified in the <see cref="T:System.Data.OracleClient.OracleCommand" /> and populates the <see cref="P:System.Data.OracleClient.OracleCommand.Parameters" /> collection of the specified <see cref="T:System.Data.OracleClient.OracleCommand" /> object.</summary>
      <param name="command">The <see cref="T:System.Data.OracleClient.OracleCommand" /> referencing the stored procedure from which the parameter information is to be derived. The derived parameters are added to the <see cref="P:System.Data.OracleClient.OracleCommand.Parameters" /> collection of the <see cref="T:System.Data.OracleClient.OracleCommand" />. </param>
      <exception cref="T:System.InvalidOperationException">The command text is not a valid stored procedure name, or the <see cref="T:System.Data.CommandType" /> specified was not <see cref="F:System.Data.CommandType.StoredProcedure" />. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommandBuilder.GetDeleteCommand">
      <summary>Gets the automatically generated <see cref="T:System.Data.OracleClient.OracleCommand" /> object required to perform deletions on the database.</summary>
      <returns>The automatically generated <see cref="T:System.Data.OracleClient.OracleCommand" /> object required to perform deletions.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommandBuilder.GetDeleteCommand(System.Boolean)">
      <summary>Gets the automatically generated <see cref="T:System.Data.OracleClient.OracleCommand" /> object required to perform deletions on the database.</summary>
      <returns>The automatically generated <see cref="T:System.Data.OracleClient.OracleCommand" /> object required to perform deletions.</returns>
      <param name="useColumnsForParameterNames">If true, generate parameter names matching column names, if possible. If false, generate @p1, @p2, and so on.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommandBuilder.GetInsertCommand">
      <summary>Gets the automatically generated <see cref="T:System.Data.OracleClient.OracleCommand" /> object required to perform insertions on the database.</summary>
      <returns>The automatically generated <see cref="T:System.Data.OracleClient.OracleCommand" /> object required to perform insertions.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommandBuilder.GetInsertCommand(System.Boolean)">
      <summary>Gets the automatically generated <see cref="T:System.Data.OracleClient.OracleCommand" /> object required to perform insertions on the database.</summary>
      <returns>The automatically generated <see cref="T:System.Data.OracleClient.OracleCommand" /> object required to perform insertions.</returns>
      <param name="useColumnsForParameterNames">If true, generate parameter names matching column names, if possible. If false, generate @p1, @p2, and so on.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommandBuilder.GetUpdateCommand">
      <summary>Gets the automatically generated <see cref="T:System.Data.OracleClient.OracleCommand" /> object required to perform updates on the database.</summary>
      <returns>The automatically generated <see cref="T:System.Data.OracleClient.OracleCommand" /> object required to perform updates.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommandBuilder.GetUpdateCommand(System.Boolean)">
      <summary>Gets the automatically generated <see cref="T:System.Data.OracleClient.OracleCommand" /> object required to perform updates on the database.</summary>
      <returns>The automatically generated <see cref="T:System.Data.OracleClient.OracleCommand" /> object required to perform updates.</returns>
      <param name="useColumnsForParameterNames">If true, generate parameter names matching column names, if possible. If false, generate @p1, @p2, and so on.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommandBuilder.QuoteIdentifier(System.String)">
      <summary>Given an unquoted identifier in the correct catalog case, returns the correct quoted form of that identifier, including properly escaping any embedded quotes in the identifier.</summary>
      <returns>The quoted version of the identifier. Embedded quotes within the identifier are properly escaped.</returns>
      <param name="unquotedIdentifier">The original unquoted identifier.</param>
    </member>
    <member name="P:System.Data.OracleClient.OracleCommandBuilder.SchemaSeparator">
      <summary>Gets or sets the character to be used for the separator between the schema identifier and any other identifiers.</summary>
      <returns>The character to be used as the schema separator.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleCommandBuilder.UnquoteIdentifier(System.String)">
      <summary>Given a quoted identifier, returns the correct unquoted form of that identifier, including properly un-escaping any embedded quotes in the identifier.</summary>
      <returns>The unquoted identifier, with embedded quotes properly un-escaped.</returns>
      <param name="quotedIdentifier">The identifier that will have its embedded quotes removed.</param>
    </member>
    <member name="T:System.Data.OracleClient.OracleConnection">
      <summary>Represents an open connection to a database. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleConnection" />.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleConnection" /> class with the specified connection string.</summary>
      <param name="connectionString">The connection used to open the database. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.BeginTransaction">
      <summary>Begins a transaction at the database.</summary>
      <returns>An object representing the new transaction.</returns>
      <exception cref="T:System.InvalidOperationException">Parallel transactions are not supported. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.BeginTransaction(System.Data.IsolationLevel)">
      <summary>Begins a transaction at the database with the specified <see cref="T:System.Data.IsolationLevel" /> value.</summary>
      <returns>An object representing the new transaction.</returns>
      <param name="il">The transaction isolation level for this connection. </param>
      <exception cref="T:System.InvalidOperationException">Parallel transactions are not supported. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.ChangeDatabase(System.String)">
      <summary>Changes the current database for an open <see cref="T:System.Data.OracleClient.OracleConnection" />.</summary>
      <param name="value">The name of the database to use instead of the current database. </param>
      <exception cref="T:System.ArgumentException">The database name is not valid. </exception>
      <exception cref="T:System.InvalidOperationException">The connection is not open. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">Cannot change the database. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.ClearAllPools">
      <summary>Empties the connection pool.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.ClearPool(System.Data.OracleClient.OracleConnection)">
      <summary>Empties the connection pool associated with the specified connection.</summary>
      <param name="connection">The <see cref="T:System.Data.OracleClient.OracleConnection" /> to be cleared from the pool.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.Close">
      <summary>Closes the connection to the database. </summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnection.ConnectionString">
      <summary>Gets or sets the string used to open an Oracle database.</summary>
      <returns>The Oracle connection string that includes settings, such as the server name, needed to establish the initial connection. The default value is an empty string ("").</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnection.ConnectionTimeout">
      <summary>Gets the time to wait to establish a connection before terminating the attempt and generating an error.</summary>
      <returns>The time (in seconds) to wait for a connection to open. The default value is 15 seconds.</returns>
      <exception cref="T:System.ArgumentException">The value specified is less than 0. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.CreateCommand">
      <summary>Creates and returns an <see cref="T:System.Data.OracleClient.OracleCommand" /> object associated with the <see cref="T:System.Data.OracleClient.OracleConnection" />.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleCommand" /> object.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnection.Database">
      <summary>Gets the name of the current database or the database to be used after a connection is opened.</summary>
      <returns>The name of the current database or the name of the database to be used after a connection is opened. The default value is an empty string.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnection.DataSource">
      <summary>Gets the name of the Oracle server to which to connect.</summary>
      <returns>The name of the Oracle server to which to connect. The default value is an empty string ("").</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.EnlistDistributedTransaction(System.EnterpriseServices.ITransaction)">
      <summary>Enlists in the specified transaction as a distributed transaction.</summary>
      <param name="distributedTransaction">A reference to an existing <see cref="T:System.EnterpriseServices.ITransaction" /> in which to enlist.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.EnlistTransaction(System.Transactions.Transaction)">
      <summary>Enlists in the specified transaction as a distributed transaction.</summary>
      <param name="transaction">A reference to an existing <see cref="T:System.Transactions.Transaction" /> in which to enlist.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.GetSchema">
      <summary>Returns schema information for the data source of this <see cref="T:System.Data.OracleClient.OracleConnection" />.</summary>
      <returns>A <see cref="T:System.Data.DataTable" /> that contains schema information.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.GetSchema(System.String)">
      <summary>Returns schema information for the data source of this <see cref="T:System.Data.OracleClient.OracleConnection" /> using the specified string for the schema name.</summary>
      <returns>A <see cref="T:System.Data.DataTable" /> that contains schema information.</returns>
      <param name="collectionName">Specifies the name of the schema to return.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="collectionName" /> is specified as null.</exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.GetSchema(System.String,System.String[])">
      <summary>Returns schema information for the data source of this <see cref="T:System.Data.OracleClient.OracleConnection" /> using the specified string for the schema name and the specified string array for the restriction values.</summary>
      <returns>A <see cref="T:System.Data.DataTable" /> that contains schema information.</returns>
      <param name="collectionName">Specifies the name of the schema to return.</param>
      <param name="restrictionValues">A set of restriction values for the requested schema.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="collectionName" /> is specified as null.</exception>
    </member>
    <member name="E:System.Data.OracleClient.OracleConnection.InfoMessage">
      <summary>Occurs when Oracle sends a warning or an informational message.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.Open">
      <summary>Opens a connection to a database with the property settings specified by the <see cref="P:System.Data.OracleClient.OracleConnection.ConnectionString" />.</summary>
      <exception cref="T:System.InvalidOperationException">The connection is not open. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">Cannot change the database. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnection.ServerVersion">
      <summary>Gets a string containing the version of the server to which the client is connected.</summary>
      <returns>The version of the connected server.</returns>
      <exception cref="T:System.InvalidOperationException">The connection is closed. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnection.State">
      <summary>Gets the current state of the connection.</summary>
      <returns>A bitwise combination of the <see cref="T:System.Data.ConnectionState" /> values. The default is Closed.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnection.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current instance.</summary>
      <returns>A new object that is a copy of this instance..</returns>
    </member>
    <member name="T:System.Data.OracleClient.OracleConnectionStringBuilder">
      <summary>Provides a simple way to create and manage the contents of connection strings used by the <see cref="T:System.Data.OracleClient.OracleConnection" /> class. </summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnectionStringBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" /> class.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnectionStringBuilder.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" /> class. The provided connection string provides the data for the instance's internal connection information.</summary>
      <param name="connectionString">The basis for the object's internal connection information. Parsed into name/value pairs. Invalid key names raise a <see cref="T:System.Collections.Generic.KeyNotFoundException" />.</param>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Invalid key name within the connection string.</exception>
      <exception cref="T:System.FormatException">Invalid value within the connection string (specifically, when a Boolean or numeric value was expected but not supplied).</exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnectionStringBuilder.Clear">
      <summary>Clears the contents of the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" /> instance.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnectionStringBuilder.ContainsKey(System.String)">
      <summary>Determines whether the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" /> contains a specific key.</summary>
      <returns>true if the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" /> contains an element that has the specified key; otherwise, false.</returns>
      <param name="keyword">The key to locate in the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> is null (Nothing in Visual Basic)</exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.DataSource">
      <summary>Gets or sets the name of the Oracle data source to connect to.</summary>
      <returns>The value of the <see cref="P:System.Data.OracleClient.OracleConnectionStringBuilder.DataSource" /> property, or <see cref="F:System.String.Empty" /> if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.Enlist">
      <summary>Gets or sets a value that indicates whether the pooler automatically enlists the connection in the creation thread's current transaction context.</summary>
      <returns>The value of the <see cref="P:System.Data.OracleClient.OracleConnectionStringBuilder.Enlist" /> property, or true if the property has not been previously set.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.IntegratedSecurity">
      <summary>Gets or sets a value that indicates whether "User ID" and "Password" are specified in the connection (when false) or whether the current Windows account credentials are used for authentication (when true).</summary>
      <returns>The value of the <see cref="P:System.Data.OracleClient.OracleConnectionStringBuilder.IntegratedSecurity" /> property, or a false if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.IsFixedSize">
      <summary>Gets a value that indicates whether the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" /> has a fixed size.</summary>
      <returns>true in every case, because the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" /> supplies a fixed-size collection of key/value pairs.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.Item(System.String)">
      <summary>Gets or sets the value associated with the specified key. In C#, this property is the indexer.</summary>
      <returns>The value associated with the specified key. </returns>
      <param name="keyword">The key of the item to get or set.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Tried to add a key which does not exist within the available keys.</exception>
      <exception cref="T:System.FormatException">Invalid value within the connection string (specifically, when a Boolean or numeric value was expected but not supplied).</exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.Keys">
      <summary>Gets an <see cref="T:System.Collections.ICollection" /> that contains the keys in the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" />.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> that contains the keys in the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" />.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.LoadBalanceTimeout">
      <summary>Gets or sets the minimum time, in seconds, for the connection to live in the connection pool before it is removed.</summary>
      <returns>The value of the <see cref="P:System.Data.OracleClient.OracleConnectionStringBuilder.LoadBalanceTimeout" /> property, or 0 if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.MaxPoolSize">
      <summary>Gets or sets the maximum number of connections allowed in the connection pool for this specific connection string.</summary>
      <returns>The value of the <see cref="P:System.Data.OracleClient.OracleConnectionStringBuilder.MaxPoolSize" /> property, or 100 if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.MinPoolSize">
      <summary>Gets or sets the minimum number of connections allowed in the connection pool for this specific connection string.</summary>
      <returns>The value of the <see cref="P:System.Data.OracleClient.OracleConnectionStringBuilder.MinPoolSize" /> property, or 0 if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.OmitOracleConnectionName">
      <summary>Gets or sets the flag that enables transaction rollbacks on earlier versions of Oracle (prior to 8.1.7.4.1). </summary>
      <returns>true if transaction rollbacks are enabled; otherwise false. </returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.Password">
      <summary>Gets or sets the password for the Oracle account.</summary>
      <returns>The value of the <see cref="P:System.Data.OracleClient.OracleConnectionStringBuilder.Password" /> property, or <see cref="F:System.String.Empty" /> if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.PersistSecurityInfo">
      <summary>Gets or sets a Boolean value that indicates if security-sensitive information, such as the password, is not returned as part of the connection if the connection is open or has ever been in an open state.</summary>
      <returns>The value of the <see cref="P:System.Data.OracleClient.OracleConnectionStringBuilder.PersistSecurityInfo" /> property, or false if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.Pooling">
      <summary>Gets or sets a Boolean value that indicates whether the connection will be pooled, or whether each connection will be explicitly opened every time that the connection is requested.</summary>
      <returns>The value of the <see cref="P:System.Data.OracleClient.OracleConnectionStringBuilder.Pooling" /> property, or true if none has been supplied.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnectionStringBuilder.Remove(System.String)">
      <summary>Removes the entry with the specified key from the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" /> instance.</summary>
      <returns>true if the key existed within the connection string and was removed, false if the key did not exist.</returns>
      <param name="keyword">The key of the key/value pair to be removed from the connection string in this <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> is null (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnectionStringBuilder.ShouldSerialize(System.String)">
      <summary>Indicates whether the specified key exists in this <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" /> instance.</summary>
      <returns>true if the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" /> contains an entry with the specified key; otherwise, false.</returns>
      <param name="keyword">The key to locate in the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" />.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleConnectionStringBuilder.TryGetValue(System.String,System.Object@)">
      <summary>Retrieves a value corresponding to the supplied key from this <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" />.</summary>
      <returns>true if <paramref name="keyword" /> was found within the connection string; otherwise, false.</returns>
      <param name="keyword">The key of the item to retrieve.</param>
      <param name="value">The value corresponding to <paramref name="keyword." /></param>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.Unicode">
      <summary>Gets or sets a Boolean value that indicates if the client supports the Unicode functionality available in later Oracle clients, or if it is non-Unicode aware.</summary>
      <returns>The value of the <see cref="P:System.Data.OracleClient.OracleConnectionStringBuilder.Unicode" /> property, or false if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.UserID">
      <summary>Gets or sets the user ID to be used when connecting to Oracle.</summary>
      <returns>The value of the <see cref="P:System.Data.OracleClient.OracleConnectionStringBuilder.UserID" /> property, or <see cref="F:System.String.Empty" /> if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleConnectionStringBuilder.Values">
      <summary>Gets an <see cref="T:System.Collections.ICollection" /> that contains the values in the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" />.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> that contains the values in the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder" />.</returns>
    </member>
    <member name="T:System.Data.OracleClient.OracleDataAdapter">
      <summary>Represents a set of data commands and a connection to a database that are used to fill the <see cref="T:System.Data.DataSet" /> and update the database. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataAdapter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleDataAdapter" /> class.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataAdapter.#ctor(System.Data.OracleClient.OracleCommand)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleDataAdapter" /> class with the specified SQL SELECT statement.</summary>
      <param name="selectCommand">An <see cref="T:System.Data.OracleClient.OracleCommand" /> that is an SQL SELECT statement or stored procedure, and is set as the <see cref="P:System.Data.OracleClient.OracleDataAdapter.SelectCommand" /> property of the <see cref="T:System.Data.OracleClient.OracleDataAdapter" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataAdapter.#ctor(System.String,System.Data.OracleClient.OracleConnection)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleDataAdapter" /> class with an SQL SELECT statement and an <see cref="T:System.Data.OracleClient.OracleConnection" />.</summary>
      <param name="selectCommandText">A string that is an SQL SELECT statement or stored procedure to be used by the <see cref="P:System.Data.OracleClient.OracleDataAdapter.SelectCommand" /> property of the <see cref="T:System.Data.OracleClient.OracleDataAdapter" />. </param>
      <param name="selectConnection">An <see cref="T:System.Data.OracleClient.OracleConnection" /> that represents the connection. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataAdapter.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleDataAdapter" /> class with an SQL SELECT statement and a connection string.</summary>
      <param name="selectCommandText">A string that is an SQL SELECT statement or stored procedure to be used by the <see cref="P:System.Data.OracleClient.OracleDataAdapter.SelectCommand" /> property of the <see cref="T:System.Data.OracleClient.OracleDataAdapter" />. </param>
      <param name="selectConnectionString">The connection string. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataAdapter.DeleteCommand">
      <summary>Gets or sets an SQL statement or stored procedure used to delete records in the database.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleCommand" /> used during an update operation to delete records in the database that correspond to deleted rows in the DataSet.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataAdapter.InsertCommand">
      <summary>Gets or sets an SQL statement or stored procedure used to insert new records into the database.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleCommand" /> used during an update operation to insert records in the database that correspond to new rows in the <see cref="T:System.Data.DataSet" />.</returns>
    </member>
    <member name="E:System.Data.OracleClient.OracleDataAdapter.RowUpdated">
      <summary>Occurs during an update operation after a command is executed against the database.</summary>
    </member>
    <member name="E:System.Data.OracleClient.OracleDataAdapter.RowUpdating">
      <summary>Occurs during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> before a command is executed against the data source.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataAdapter.SelectCommand">
      <summary>Gets or sets an SQL statement or stored procedure used to select records in the database.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleCommand" /> that is used during a fill operation to select records from database for placement in the <see cref="T:System.Data.DataSet" />.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataAdapter.System#Data#IDbDataAdapter#DeleteCommand">
      <summary>For a description of this member, see <see cref="P:System.Data.IDbDataAdapter.DeleteCommand" />.</summary>
      <returns>A string representing the command.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataAdapter.System#Data#IDbDataAdapter#InsertCommand">
      <summary>For a description of this member, see <see cref="P:System.Data.IDbDataAdapter.InsertCommand" />.</summary>
      <returns>A string representing the command.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataAdapter.System#Data#IDbDataAdapter#SelectCommand">
      <summary>For a description of this member, see <see cref="P:System.Data.IDbDataAdapter.SelectCommand" />.</summary>
      <returns>A string representing the command.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataAdapter.System#Data#IDbDataAdapter#UpdateCommand">
      <summary>For a description of this member, see <see cref="P:System.Data.IDbDataAdapter.UpdateCommand" />.</summary>
      <returns>A string representing the command.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataAdapter.System#ICloneable#Clone">
      <summary>For a description of this member, see <see cref="M:System.ICloneable.Clone" />.</summary>
      <returns>A new object that is a copy of this instance. </returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataAdapter.UpdateBatchSize">
      <summary>Gets or sets a value that enables or disables batch processing support, and specifies the number of commands that can be executed in a batch.</summary>
      <returns>The number of rows to process per batch. Value:Effect:0There is no limit on the batch size.1Disables batch updating.&gt; 1Changes are sent using batches of <see cref="P:System.Data.OracleClient.OracleDataAdapter.UpdateBatchSize" /> operations at a time.When setting this to a value other than 1 all the commands associated with the <see cref="T:System.Data.OracleClient.OracleDataAdapter" /> have to have their <see cref="P:System.Data.IDbCommand.UpdatedRowSource" /> property set to None or OutputParameters. An exception is thrown otherwise. </returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataAdapter.UpdateCommand">
      <summary>Gets or sets an SQL statement or stored procedure used to update records in the database.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleCommand" /> used during an update operation to update records in the database that correspond to modified rows in the <see cref="T:System.Data.DataSet" />.</returns>
    </member>
    <member name="T:System.Data.OracleClient.OracleDataReader">
      <summary>Provides a way of reading a forward-only stream of data rows from a data source. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.Close">
      <summary>Closes the <see cref="T:System.Data.OracleClient.OracleDataReader" /> object.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataReader.Depth">
      <summary>Gets a value indicating the depth of nesting for the current row.</summary>
      <returns>The depth of nesting for the current row.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataReader.FieldCount">
      <summary>Gets the number of columns in the current row.</summary>
      <returns>When not positioned in a valid record set, 0; otherwise the number of columns in the current record. The default is -1.</returns>
      <exception cref="T:System.NotSupportedException">There is no current connection to a data source. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetBoolean(System.Int32)">
      <summary>Gets the value of the specified column as a Boolean.</summary>
      <returns>A Boolean that is the value of the column.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetByte(System.Int32)">
      <summary>Gets the value of the specified column as a byte.</summary>
      <returns>The value of the specified column as a byte.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
      <summary>Reads a stream of bytes from the specified column offset into the buffer as an array, starting at the given buffer offset.</summary>
      <returns>The actual number of bytes read.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <param name="fieldOffset">The index within the field where the read operation is to begin. </param>
      <param name="buffer2">The buffer into which to read the stream of bytes. </param>
      <param name="bufferoffset">The index where <paramref name="buffer" /> is to begin the write operation. </param>
      <param name="length">The number of bytes to read. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetChar(System.Int32)">
      <summary>Gets the value of the specified column as a character.</summary>
      <returns>The value of the specified column as a character.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
      <summary>Reads a stream of characters from the specified column offset into the buffer as an array, starting at the given buffer offset.</summary>
      <returns>The actual number of characters read.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <param name="fieldOffset">The index within the row where the read operation is to begin. </param>
      <param name="buffer2">The buffer into which to copy data. </param>
      <param name="bufferoffset">The index where <paramref name="buffer" /> is to begin the write operation. </param>
      <param name="length">The number of characters to read. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetDataTypeName(System.Int32)">
      <summary>Gets the name of the source data type.</summary>
      <returns>The name of the source data type.</returns>
      <param name="i">The zero-based column ordinal. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetDateTime(System.Int32)">
      <summary>Gets the value of the specified column as a DateTime object.</summary>
      <returns>The value of the specified column as a DateTime object.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetDecimal(System.Int32)">
      <summary>Gets the value of the specified column as a Decimal object.</summary>
      <returns>The value of the specified column as a Decimal object.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetDouble(System.Int32)">
      <summary>Gets the value of the specified column as a double-precision floating point number.</summary>
      <returns>The value of the specified column as a double-precision floating point number.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the rows in the data reader.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the rows in the data reader.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetFieldType(System.Int32)">
      <summary>Gets the <see cref="T:System.Type" /> that is the data type of the object.</summary>
      <returns>The <see cref="T:System.Type" /> that is the data type of the object.</returns>
      <param name="i">The zero-based column ordinal. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetFloat(System.Int32)">
      <summary>Gets the value of the specified column as a single-precision floating-point number.</summary>
      <returns>The value of the specified column as a single-precision floating-point number.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetGuid(System.Int32)">
      <summary>Gets the value of the specified column as a globally-unique identifier (GUID).</summary>
      <returns>The value of the specified column as a GUID.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetInt16(System.Int32)">
      <summary>Gets the value of the specified column as a 16-bit signed integer.</summary>
      <returns>The value of the specified column as a 16-bit signed integer.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetInt32(System.Int32)">
      <summary>Gets the value of the specified column as a 32-bit signed integer.</summary>
      <returns>The value of the specified column as a 32-bit signed integer.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetInt64(System.Int32)">
      <summary>Gets the value of the specified column as a 64-bit signed integer.</summary>
      <returns>The value of the specified column as a 64-bit signed integer.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetName(System.Int32)">
      <summary>Gets the name of the specified column.</summary>
      <returns>A string that is the name of the specified column.</returns>
      <param name="i">The zero-based column ordinal. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetOracleBFile(System.Int32)">
      <summary>Gets the value of the specified column as an <see cref="T:System.Data.OracleClient.OracleBFile" /> object.</summary>
      <returns>The value of the specified column as an <see cref="T:System.Data.OracleClient.OracleBFile" /> object.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetOracleBinary(System.Int32)">
      <summary>Gets the value of the specified column as an <see cref="T:System.Data.OracleClient.OracleBinary" /> object.</summary>
      <returns>The value of the specified column as an <see cref="T:System.Data.OracleClient.OracleBinary" /> object.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetOracleDateTime(System.Int32)">
      <summary>Gets the value of the specified column as an <see cref="T:System.Data.OracleClient.OracleDateTime" /> object.</summary>
      <returns>The value of the specified column as an <see cref="T:System.Data.OracleClient.OracleDateTime" /> object.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetOracleLob(System.Int32)">
      <summary>Gets the value of the specified column as an <see cref="T:System.Data.OracleClient.OracleLob" /> object.</summary>
      <returns>The value of the specified column as an <see cref="T:System.Data.OracleClient.OracleLob" /> object.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetOracleMonthSpan(System.Int32)">
      <summary>Gets the value of the specified column as an <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> object.</summary>
      <returns>The value of the specified column as an <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> object.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetOracleNumber(System.Int32)">
      <summary>Gets the value of the specified column as an <see cref="T:System.Data.OracleClient.OracleNumber" /> object.</summary>
      <returns>The value of the specified column as an <see cref="T:System.Data.OracleClient.OracleNumber" /> object.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetOracleString(System.Int32)">
      <summary>Gets the value of the specified column as an <see cref="T:System.Data.OracleClient.OracleString" /> object.</summary>
      <returns>The value of the specified column as an <see cref="T:System.Data.OracleClient.OracleString" /> object.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetOracleTimeSpan(System.Int32)">
      <summary>Gets the value of the specified column as an <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> object.</summary>
      <returns>The value of the specified column as an <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> object.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetOracleValue(System.Int32)">
      <summary>Gets the value of the column at the specified ordinal in its Oracle format.</summary>
      <returns>The Oracle value to return.</returns>
      <param name="i">The zero-based column ordinal. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetOracleValues(System.Object[])">
      <summary>Gets all the attribute columns in the current row in Oracle format.</summary>
      <returns>The number of instances of <see cref="T:System.Object" /> in the array.</returns>
      <param name="values">An array of type <see cref="T:System.Object" /> into which to copy the attribute columns. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetOrdinal(System.String)">
      <summary>Gets the column ordinal, given the name of the column.</summary>
      <returns>The zero-based column ordinal.</returns>
      <param name="name">The name of the column.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetProviderSpecificFieldType(System.Int32)">
      <summary>Gets an Object that is a representation of the underlying provider specific field type.</summary>
      <returns>Gets an <see cref="T:System.Object" /> that is a representation of the underlying provider specific field type.</returns>
      <param name="i">An <see cref="T:System.Int32" />.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetProviderSpecificValue(System.Int32)">
      <summary>Gets an Object that is a representation of the underlying provider specific field type.</summary>
      <returns>Gets an <see cref="T:System.Object" /> that is a representation of the underlying provider specific field type.</returns>
      <param name="i">An <see cref="T:System.Int32" />.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetProviderSpecificValues(System.Object[])">
      <summary>Gets an array of objects that are a representation of the underlying provider specific values.</summary>
      <returns>The number of instances of <see cref="T:System.Object" /> in the array.</returns>
      <param name="values">An array of <see cref="T:System.Object" />.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetSchemaTable">
      <summary>Returns a <see cref="T:System.Data.DataTable" /> that describes the column metadata of the OracleDataReader.</summary>
      <returns>A <see cref="T:System.Data.DataTable" /> that describes the column metadata.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetString(System.Int32)">
      <summary>Gets the value of the specified column as a string.</summary>
      <returns>The value of the specified column as a string.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetTimeSpan(System.Int32)">
      <summary>Gets the value of the specified column as a System.TimeSpan.</summary>
      <returns>The value of the specified column as a <see cref="T:System.TimeSpan" />.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetValue(System.Int32)">
      <summary>Gets the value of the column at the specified ordinal in its native format.</summary>
      <returns>The value to return.</returns>
      <param name="i">The zero-based column ordinal. </param>
      <exception cref="T:System.Data.OracleClient.OracleException">The value is too large to be stored in the .NET Decimal.</exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.GetValues(System.Object[])">
      <summary>Populates an array of objects with the column values of the current row.</summary>
      <returns>The number of instances of <see cref="T:System.Object" /> in the array.</returns>
      <param name="values">An array of type <see cref="T:System.Object" /> into which to copy the attribute columns. </param>
      <exception cref="T:System.Data.OracleClient.OracleException">The value is too large to be stored in the .NET Decimal.</exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataReader.HasRows">
      <summary>Gets a value indicating whether the <see cref="T:System.Data.OracleClient.OracleDataReader" /> contains one or more rows.</summary>
      <returns>true if the <see cref="T:System.Data.OracleClient.OracleDataReader" /> contains one or more rows; otherwise false.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataReader.IsClosed">
      <summary>Indicates whether the <see cref="T:System.Data.OracleClient.OracleDataReader" /> is closed.</summary>
      <returns>true if the <see cref="T:System.Data.OracleClient.OracleDataReader" /> is closed; otherwise, false.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.IsDBNull(System.Int32)">
      <summary>Gets a value indicating whether the column contains non-existent or missing values.</summary>
      <returns>true if the specified column value is equivalent to <see cref="T:System.DBNull" />; otherwise, false.</returns>
      <param name="i">The zero-based column ordinal. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataReader.Item(System.Int32)">
      <summary>Gets the value of the specified column in its native format given the column ordinal.</summary>
      <returns>The value of the specified column in its native format.</returns>
      <param name="i">The column ordinal. </param>
      <exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount" />. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataReader.Item(System.String)">
      <summary>Gets the value of the specified column in its native format given the column name.</summary>
      <returns>The value of the specified column in its native format.</returns>
      <param name="name">The column name.</param>
      <exception cref="T:System.IndexOutOfRangeException">No column with the specified name was found. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.NextResult">
      <summary>Advances the <see cref="T:System.Data.OracleClient.OracleDataReader" /> to the next result </summary>
      <returns>true if there are more result sets; otherwise, false.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleDataReader.Read">
      <summary>Advances the <see cref="T:System.Data.OracleClient.OracleDataReader" /> to the next record.</summary>
      <returns>true if there are more rows; otherwise, false.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleDataReader.RecordsAffected">
      <summary>Gets the number of rows changed, inserted, or deleted by execution of the SQL statement.</summary>
      <returns>The number of rows changed, inserted, or deleted. -1 for SELECT statements; 0 if no rows were affected, or the statement failed.</returns>
    </member>
    <member name="T:System.Data.OracleClient.OracleDateTime">
      <summary>Represents date and time data ranging in value from January 1, 4712 BC to December 31, 4712 AD.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.#ctor(System.Data.OracleClient.OracleDateTime)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure from an existing <see cref="T:System.Data.OracleClient.OracleDateTime" /> object.</summary>
      <param name="from">An existing <see cref="T:System.Data.OracleClient.OracleDateTime" /> object from which to copy. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.#ctor(System.DateTime)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure with the specified <see cref="T:System.DateTime" />.</summary>
      <param name="dt">The specified <see cref="T:System.DateTime" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.#ctor(System.Int32,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure using the supplied parameters to initialize the year, month, and day of the new structure.</summary>
      <param name="year">An integer value representing the year of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="month">An integer value representing the month of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="day">An integer value representing the day of the month of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.#ctor(System.Int32,System.Int32,System.Int32,System.Globalization.Calendar)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure using the supplied parameters to initialize the year, month, day, and calendar of the new structure.</summary>
      <param name="year">An integer value representing the year of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="month">An integer value representing the month of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="day">An integer value representing the day of the month of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="calendar">The <see cref="T:System.Globalization.Calendar" /> for this instance of <see cref="T:System.Data.OracleClient.OracleDateTime" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure using the supplied parameters to initialize the year, month, day, hour, minute, and second of the new structure.</summary>
      <param name="year">An integer value representing the year of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="month">An integer value representing the month of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="day">An integer value representing the day of the month of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="hour">An integer value representing the hour of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="minute">An integer value representing the minute of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="second">An integer value representing the second of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Globalization.Calendar)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure using the supplied parameters to initialize the year, month, day, hour, minute, and second for the specified calendar of the new structure.</summary>
      <param name="year">An integer value representing the year of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="month">An integer value representing the month of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="day">An integer value representing the day of the month of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="hour">An integer value representing the hour of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="minute">An integer value representing the minute of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="second">An integer value representing the second of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="calendar">The <see cref="T:System.Globalization.Calendar" /> for this instance of <see cref="T:System.Data.OracleClient.OracleDateTime" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure using the supplied parameters to initialize the year, month, day, hour, minute, second, and millisecond of the new structure.</summary>
      <param name="year">An integer value representing the year of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="month">An integer value representing the month of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="day">An integer value representing the day of the month of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="hour">An integer value representing the hour of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="minute">An integer value representing the minute of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="second">An integer value representing the second of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="millisecond">An integer value representing the millisecond of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Globalization.Calendar)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure using the supplied parameters to initialize the year, month, day, hour, minute, second, and millisecond for the specified calendar of the new structure.</summary>
      <param name="year">An integer value representing the year of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="month">An integer value representing the month of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="day">An integer value representing the day of the month of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="hour">An integer value representing the hour of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="minute">An integer value representing the minute of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="second">An integer value representing the second of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="millisecond">An integer value representing the millisecond of the new <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="calendar">The <see cref="T:System.Globalization.Calendar" /> for this instance of <see cref="T:System.Data.OracleClient.OracleDateTime" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.#ctor(System.Int64)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure using the supplied number of ticks.</summary>
      <param name="ticks">A time period expressed in 100-nanosecond units. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.CompareTo(System.Object)">
      <summary>Compares this <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure to the supplied object and returns an indication of their relative values.</summary>
      <returns>A signed number indicating the relative values of the instance and the object.Return value Condition Less than zero This structure is less than the object. Zero This structure is the same as the object. Greater than zero This structure is greater than the object, object is a null reference (Nothing in Visual Basic) </returns>
      <param name="obj">The object to be compared. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleDateTime.Day">
      <summary>Gets the value of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure as a day.</summary>
      <returns>A day value between 1 and 31.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.Equals(System.Data.OracleClient.OracleDateTime,System.Data.OracleClient.OracleDateTime)">
      <summary>Performs a logical comparison of two <see cref="T:System.Data.OracleClient.OracleDateTime" /> structures to determine whether they are equal.</summary>
      <returns>true if the two values are equal, otherwise false.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.Equals(System.Object)">
      <summary>Compares the supplied object parameter to the <see cref="P:System.Data.OracleClient.OracleDateTime.Value" /> property of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> object.</summary>
      <returns>true if object is an instance of <see cref="T:System.Data.OracleClient.OracleDateTime" /> and the two are equal, otherwise false.</returns>
      <param name="value">The object to be compared. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.GetHashCode">
      <summary>Gets the hash code for this instance.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.GreaterThan(System.Data.OracleClient.OracleDateTime,System.Data.OracleClient.OracleDateTime)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleDateTime" /> to determine whether the first is greater than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleDateTime" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.GreaterThanOrEqual(System.Data.OracleClient.OracleDateTime,System.Data.OracleClient.OracleDateTime)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleDateTime" /> to determine whether the first is greater than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleDateTime" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleDateTime.Hour">
      <summary>Gets the value of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure as an hour.</summary>
      <returns>An hour between 0 and 23.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleDateTime.IsNull">
      <summary>Gets a value indicating whether the <see cref="P:System.Data.OracleClient.OracleDateTime.Value" /> property of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure is null.</summary>
      <returns>true if <see cref="P:System.Data.OracleClient.OracleDateTime.Value" /> is null, otherwise false.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.LessThan(System.Data.OracleClient.OracleDateTime,System.Data.OracleClient.OracleDateTime)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleDateTime" /> to determine whether the first is less than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleDateTime" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.LessThanOrEqual(System.Data.OracleClient.OracleDateTime,System.Data.OracleClient.OracleDateTime)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleDateTime" /> to determine whether the first is less than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleDateTime" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleDateTime.MaxValue">
      <summary>Represents the maximum valid date value for an <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleDateTime.Millisecond">
      <summary>Gets the milliseconds component of the date represented by this instance.</summary>
      <returns>The millisecond, between 0 and 999.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleDateTime.Minute">
      <summary>Gets the value of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure as a minute.</summary>
      <returns>The minute, between 0 and 59.</returns>
    </member>
    <member name="F:System.Data.OracleClient.OracleDateTime.MinValue">
      <summary>Represents the minimum valid date value for an <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleDateTime.Month">
      <summary>Gets the value of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure as a month.</summary>
      <returns>The month, between 1 and 12.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.NotEquals(System.Data.OracleClient.OracleDateTime,System.Data.OracleClient.OracleDateTime)">
      <summary>Performs a logical comparison of two instances of <see cref="T:System.Data.OracleClient.OracleDateTime" /> to determine if they are not equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are not equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are equal. If either instance of <see cref="T:System.Data.OracleClient.OracleDateTime" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleDateTime.Null">
      <summary>Represents a null value that can be assigned to the <see cref="P:System.Data.OracleClient.OracleDateTime.Value" /> property of an instance of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.op_Equality(System.Data.OracleClient.OracleDateTime,System.Data.OracleClient.OracleDateTime)">
      <summary>Performs a logical comparison of two <see cref="T:System.Data.OracleClient.OracleDateTime" /> structures to determine if they are equal.</summary>
      <returns>true if the two values are equal, otherwise false.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.op_Explicit(System.Data.OracleClient.OracleDateTime)~System.DateTime">
      <summary>Converts an <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure to a <see cref="T:System.DateTime" /> structure.</summary>
      <returns>A <see cref="T:System.DateTime" /> structure whose <see cref="P:System.DateTime.Date" /> and <see cref="P:System.DateTime.TimeOfDay" /> properties contain the same date and time values as the <see cref="P:System.Data.OracleClient.OracleDateTime.Value" /> property of the supplied <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.op_Explicit(System.String)~System.Data.OracleClient.OracleDateTime">
      <summary>Converts a String to an <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure whose <see cref="P:System.Data.OracleClient.OracleDateTime.Value" /> is equal to the values contained in the String.</returns>
      <param name="x">A String to be converted to an <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.op_GreaterThan(System.Data.OracleClient.OracleDateTime,System.Data.OracleClient.OracleDateTime)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleDateTime" /> to determine if the first is greater than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleDateTime" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.op_GreaterThanOrEqual(System.Data.OracleClient.OracleDateTime,System.Data.OracleClient.OracleDateTime)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleDateTime" /> to determine if the first is greater than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleDateTime" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.op_Inequality(System.Data.OracleClient.OracleDateTime,System.Data.OracleClient.OracleDateTime)">
      <summary>Performs a logical comparison of two instances of <see cref="T:System.Data.OracleClient.OracleDateTime" /> to determine if they are equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are not equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are equal. If either instance of <see cref="T:System.Data.OracleClient.OracleDateTime" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.op_LessThan(System.Data.OracleClient.OracleDateTime,System.Data.OracleClient.OracleDateTime)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleDateTime" /> to determine if the first is less than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleDateTime" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.op_LessThanOrEqual(System.Data.OracleClient.OracleDateTime,System.Data.OracleClient.OracleDateTime)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleDateTime" /> to determine if the first is less than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleDateTime" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.Parse(System.String)">
      <summary>Converts the specified <see cref="T:System.String" /> representation of a date and time to its <see cref="T:System.Data.OracleClient.OracleDateTime" /> equivalent.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure equal to the date and time represented by the specified String.</returns>
      <param name="s">The String to be parsed. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleDateTime.Second">
      <summary>Gets the value of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure as a second.</summary>
      <returns>A second between 0 and 59.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleDateTime.ToString">
      <summary>Converts this <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure to a <see cref="T:System.String" />.</summary>
      <returns>A String representing the <see cref="P:System.Data.OracleClient.OracleDateTime.Value" /> property of this <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleDateTime.Value">
      <summary>Gets the value of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure.</summary>
      <returns>The value of this <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleDateTime.Year">
      <summary>Gets the value of the <see cref="T:System.Data.OracleClient.OracleDateTime" /> structure as a year.</summary>
      <returns>A year between 1 and 4712.</returns>
    </member>
    <member name="T:System.Data.OracleClient.OracleException">
      <summary>The exception that is generated when a warning or error is returned by an Oracle database or the .NET Framework Data Provider for Oracle. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleException.Code">
      <summary>Gets the code portion of the error as an integer.</summary>
      <returns>The code portion of the error as an integer.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with information about the exception.</summary>
      <param name="si">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown. </param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
    </member>
    <member name="T:System.Data.OracleClient.OracleInfoMessageEventArgs">
      <summary>Provides data for the <see cref="E:System.Data.OracleClient.OracleConnection.InfoMessage" /> event. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleInfoMessageEventArgs.Code">
      <summary>Gets the code portion of the message as an int.</summary>
      <returns>The code portion of the message as an int.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleInfoMessageEventArgs.Message">
      <summary>Gets the full text of the message sent from the database.</summary>
      <returns>The text describing the message.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleInfoMessageEventArgs.Source">
      <summary>Gets the name of the object that generated the error.</summary>
      <returns>The name of the object that generated the error.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleInfoMessageEventArgs.ToString">
      <summary>Retrieves a string representation of the <see cref="E:System.Data.OracleClient.OracleConnection.InfoMessage" /> event.</summary>
      <returns>A string representing the <see cref="E:System.Data.OracleClient.OracleConnection.InfoMessage" /> event.</returns>
    </member>
    <member name="T:System.Data.OracleClient.OracleInfoMessageEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.Data.OracleClient.OracleConnection.InfoMessage" /> event of an <see cref="T:System.Data.OracleClient.OracleConnection" />.</summary>
      <param name="sender">The source of the event. </param>
      <param name="e">An <see cref="T:System.Data.OracleClient.OracleInfoMessageEventArgs" /> object that contains the event data. </param>
    </member>
    <member name="T:System.Data.OracleClient.OracleLob">
      <summary>Represents a large object binary (LOB) data type stored on an Oracle server. This class cannot be inherited. </summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.Append(System.Data.OracleClient.OracleLob)">
      <summary>Appends data from the specified LOB to the current LOB.</summary>
      <param name="source">The LOB from which to append data. </param>
      <exception cref="T:System.ArgumentNullException">The source <see cref="T:System.Data.OracleClient.OracleLob" /> is null. </exception>
      <exception cref="T:System.InvalidOperationException">The source <see cref="T:System.Data.OracleClient.OracleLob" /> is null, or the connection is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The source <see cref="T:System.Data.OracleClient.OracleLob" /> object was closed or disposed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.BeginBatch">
      <summary>Prevents server-side triggers from firing while performing multiple read operations.</summary>
      <exception cref="T:System.InvalidOperationException">The connection is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.BeginBatch(System.Data.OracleClient.OracleLobOpenMode)">
      <summary>Prevents server-side triggers from firing while performing multiple read and write operations in the specified access mode.</summary>
      <param name="mode">Mode (one of the <see cref="T:System.Data.OracleClient.OracleLobOpenMode" /> values) in which the LOB can be accessed between this <see cref="M:System.Data.OracleClient.OracleLob.BeginBatch(System.Data.OracleClient.OracleLobOpenMode)" /> call and the corresponding <see cref="M:System.Data.OracleClient.OracleLob.EndBatch" /> call. </param>
      <exception cref="T:System.InvalidOperationException">The connection is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleLob.CanRead">
      <summary>Gets a value indicating whether the LOB stream can be read.</summary>
      <returns>true if the LOB stream supports reading, otherwise false if a LOB is closed or disposed.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleLob.CanSeek">
      <summary>Gets a value indicating whether forward and backward seek operations can be performed.</summary>
      <returns>false if a LOB is closed or disposed, otherwise true. Always true for <see cref="F:System.Data.OracleClient.OracleLob.Null" />.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleLob.CanWrite">
      <summary>Always returns true, regardless of whether the LOB supports writing or not.</summary>
      <returns>Always returns true, regardless of whether an opened or undisposed LOB supports writing or not, false if a LOB is closed or disposed.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleLob.ChunkSize">
      <summary>Gets a value indicating the minimum number of bytes to retrieve from or send to the server during a read/write operation.</summary>
      <returns>The minimum number of bytes to retrieve or send.</returns>
      <exception cref="T:System.InvalidOperationException">The connection is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.Clone">
      <summary>Creates a new <see cref="T:System.Data.OracleClient.OracleLob" /> object that references the same Oracle LOB as the original <see cref="T:System.Data.OracleClient.OracleLob" /> object.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleLob" /> object that references the same Oracle LOB as the original <see cref="T:System.Data.OracleClient.OracleLob" /> object.</returns>
      <exception cref="T:System.InvalidOperationException">The connection is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleLob.Connection">
      <summary>Gets the <see cref="T:System.Data.OracleClient.OracleConnection" /> used by this instance of the <see cref="T:System.Data.OracleClient.OracleLob" />.</summary>
      <returns>The connection to a data source.</returns>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.CopyTo(System.Data.OracleClient.OracleLob)">
      <summary>Copies from this <see cref="T:System.Data.OracleClient.OracleLob" /> to a destination <see cref="T:System.Data.OracleClient.OracleLob" />.</summary>
      <returns>The number of bytes copied. This excludes any padded bytes.</returns>
      <param name="destination">The destination <see cref="T:System.Data.OracleClient.OracleLob" />. </param>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Data.OracleClient.OracleLob" /> specified in the <paramref name="destination" /> parameter is null. </exception>
      <exception cref="T:System.InvalidOperationException">The operation is not within a transaction, the <see cref="T:System.Data.OracleClient.OracleLob" /> object is null, or the connection is closed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.CopyTo(System.Data.OracleClient.OracleLob,System.Int64)">
      <summary>Copies from this <see cref="T:System.Data.OracleClient.OracleLob" /> to a destination <see cref="T:System.Data.OracleClient.OracleLob" /> with the specified amount of data.</summary>
      <returns>The number of bytes copied. This excludes any padded bytes.</returns>
      <param name="destination">The destination <see cref="T:System.Data.OracleClient.OracleLob" /></param>
      <param name="destinationOffset">The offset to which to copy. For CLOB and NCLOB data types, this must be an even number of bytes. </param>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Data.OracleClient.OracleLob" /> specified in the <paramref name="destination" /> parameter is full. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">A value specified in the <paramref name="destinationOffset" /> parameter is less than zero or greater than 4 gigabytes.-or- A value specified in the <paramref name="destinationOffset" /> parameter for a CLOB or NCLOB data type is not even. -or- You must specify CLOB and NCLOB data types as an even number of bytes. </exception>
      <exception cref="T:System.InvalidOperationException">The operation is not within a transaction, the <see cref="T:System.Data.OracleClient.OracleLob" /> object is null, or the connection is closed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.CopyTo(System.Int64,System.Data.OracleClient.OracleLob,System.Int64,System.Int64)">
      <summary>Copies from this <see cref="T:System.Data.OracleClient.OracleLob" /> to a destination <see cref="T:System.Data.OracleClient.OracleLob" /> with the specified amount of data, and the source offset.</summary>
      <returns>The number of bytes copied. This excludes any padded bytes.</returns>
      <param name="sourceOffset">The offset from which to copy. For CLOB and NCLOB data types, this must be an even number. </param>
      <param name="destination">The destination OracleLob<see cref="N:System.Data.OracleClient" />. </param>
      <param name="destinationOffset">The destination offset to which to copy. For CLOB and NCLOB data types, this must be an even number. </param>
      <param name="amount">The quantity of data, in bytes, to copy. For CLOB and NCLOB data types, this must be an even number. </param>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Data.OracleClient.OracleLob" /> specified in the <paramref name="destination" /> parameter is full. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">A value specified in the <paramref name="amount" />, <paramref name="sourceOffset" />, or <paramref name="destinationOffset" /> parameter is less than zero or greater than 4 gigabytes.-or- A value specified in the <paramref name="amount" />, <paramref name="sourceOffset" />, or <paramref name="destinationOffset" /> parameter for a CLOB or NCLOB data type is not even. </exception>
      <exception cref="T:System.InvalidOperationException">The operation is not within a transaction, the <see cref="T:System.Data.OracleClient.OracleLob" /> object is null, or the connection is closed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.EndBatch">
      <summary>Allows server-side triggers to resume firing after performing multiple write operations.</summary>
      <exception cref="T:System.InvalidOperationException">The connection is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.Erase">
      <summary>Erases all data from this <see cref="T:System.Data.OracleClient.OracleLob" />.</summary>
      <returns>The number of bytes erased.</returns>
      <exception cref="T:System.InvalidOperationException">The operation is not within a transaction, the <see cref="T:System.Data.OracleClient.OracleLob" /> object is null, or the connection is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.Erase(System.Int64,System.Int64)">
      <summary>Erases the specified amount of data from this <see cref="T:System.Data.OracleClient.OracleLob" />.</summary>
      <returns>The number of bytes erased.</returns>
      <param name="offset">The offset from which to erase. For CLOB and NCLOB data types, this must be an even number. </param>
      <param name="amount">The quantity of data, in bytes, to erase. For CLOB and NCLOB data types, this must be an even number. </param>
      <exception cref="T:System.InvalidOperationException">The operation is not within a transaction, the <see cref="T:System.Data.OracleClient.OracleLob" /> object is null, or the connection is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.Flush">
      <summary>Not currently supported.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleLob.IsBatched">
      <summary>Gets a value indicating whether an application called the <see cref="M:System.Data.OracleClient.OracleLob.BeginBatch" /> method.</summary>
      <returns>true if application called the <see cref="M:System.Data.OracleClient.OracleLob.BeginBatch" /> method, otherwise false.</returns>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleLob.IsNull">
      <summary>Gets a value that indicates whether the <see cref="T:System.Data.OracleClient.OracleLob" /> is a <see cref="F:System.Data.OracleClient.OracleBFile.Null" /> stream.</summary>
      <returns>true if the <see cref="T:System.Data.OracleClient.OracleLob" /> is a <see cref="F:System.Data.OracleClient.OracleBFile.Null" /> stream, otherwise false.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleLob.IsTemporary">
      <summary>Gets a value indicating whether the <see cref="T:System.Data.OracleClient.OracleLob" /> is a temporary LOB.</summary>
      <returns>true if the <see cref="T:System.Data.OracleClient.OracleLob" /> is a temporary LOB, otherwise false.</returns>
      <exception cref="T:System.InvalidOperationException">The connection is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleLob.Length">
      <summary>Gets a value that returns the size of the <see cref="T:System.Data.OracleClient.OracleLob" />.</summary>
      <returns>The size of the <see cref="T:System.Data.OracleClient.OracleLob" /> in bytes.</returns>
      <exception cref="T:System.InvalidOperationException">The connection is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleLob.LobType">
      <summary>Gets a value that returns the LOB data type.</summary>
      <returns>One of the <see cref="T:System.Data.OracleClient.OracleType" />LOB data types.</returns>
    </member>
    <member name="F:System.Data.OracleClient.OracleLob.Null">
      <summary>Represents a null <see cref="T:System.Data.OracleClient.OracleLob" /> object.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleLob.Position">
      <summary>Gets the current read position in the <see cref="T:System.Data.OracleClient.OracleLob" /> stream.</summary>
      <returns>The current position within the <see cref="T:System.Data.OracleClient.OracleLob" /> stream.</returns>
      <exception cref="T:System.InvalidOperationException">The connection is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Reads a sequence of bytes from the current <see cref="T:System.Data.OracleClient.OracleLob" /> stream and advances the position within the stream by the number of bytes read.</summary>
      <returns>The total number of bytes read into the buffer. This may be less than the number of bytes requested if that many bytes are not currently available, or zero (0) if the end of the stream has been reached.</returns>
      <param name="buffer">An array of bytes. When this method returns, the buffer contains the specified byte array with the values between <paramref name="offset" /> and (<paramref name="offset" /> + <paramref name="count" />) replaced by the bytes read from the current source. </param>
      <param name="offset">The zero-based byte offset in <paramref name="buffer" /> at which to begin storing the data read from the current stream. For CLOB and NCLOB data types, this must be an even number. </param>
      <param name="count">The maximum number of bytes to be read from the current stream. For CLOB and NCLOB data types, this must be an even number. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> is a null reference (Nothing in Visual Basic). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">A value in the <paramref name="offset" /> or <paramref name="count" /> parameter is not positive.-or- The sum of the offset and count parameters is larger than the buffer length.-or- A value specified in the <paramref name="amount" /> or <paramref name="offset" /> parameter is less than zero or greater than 4 gigabytes. </exception>
      <exception cref="T:System.InvalidOperationException">The operation is not within a transaction, the <see cref="T:System.Data.OracleClient.OracleLob" /> object is null, or the connection is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Sets the position on the current <see cref="T:System.Data.OracleClient.OracleLob" /> stream.</summary>
      <returns>The new position within the current stream.</returns>
      <param name="offset">A byte offset relative to origin. If <paramref name="offset" /> is negative, the new position precedes the position specified by <paramref name="origin" /> by the number of bytes specified by <paramref name="offset" />. If <paramref name="offset" /> is zero, the new position is the position specified by <paramref name="origin" />. If <paramref name="offset" /> is positive, the new position follows the position specified by <paramref name="origin" /> by the number of bytes specified by <paramref name="offset" />. </param>
      <param name="origin">A value of type <see cref="T:System.IO.SeekOrigin" /> indicating the reference point used to obtain the new position. </param>
      <exception cref="T:System.ArgumentException">The <paramref name="origin" /> parameter does not contain a valid value. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The resulting position is beyond the length of the value. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Data.OracleClient.OracleLob" /> object was closed or disposed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.SetLength(System.Int64)">
      <summary>Sets the length of the <see cref="T:System.Data.OracleClient.OracleLob" /> stream to a value less than the current length.</summary>
      <param name="value">The desired length of the current <see cref="T:System.Data.OracleClient.OracleLob" /> stream in bytes. For CLOB and NCLOB data types, this must be an even number. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">A value specified in the <paramref name="value" /> parameter for a CLOB or NCLOB data type is not even.-or- A value specified in the <paramref name="value" /> parameter is less than zero or greater than 4 gigabytes. </exception>
      <exception cref="T:System.InvalidOperationException">The operation is not within a transaction, the <see cref="T:System.Data.OracleClient.OracleLob" /> object is null, or the connection is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleLob.Value">
      <summary>Gets the common language runtime stream value equivalent of the underlying value.</summary>
      <returns>For <see cref="F:System.Data.OracleClient.OracleType.Blob" />, an array of type Byte[]. For <see cref="F:System.Data.OracleClient.OracleType.Clob" /> and <see cref="F:System.Data.OracleClient.OracleType.NClob" />, a String. For null data, <see cref="T:System.DBNull" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Writes a sequence of bytes to the current <see cref="T:System.Data.OracleClient.OracleLob" /> stream, and advances the current position within this stream by the number of bytes written.</summary>
      <param name="buffer">An array of bytes. This method copies the number of bytes specified in <paramref name="count" /> from <paramref name="buffer" /> to the current stream. </param>
      <param name="offset">The zero-based byte offset in <paramref name="buffer" /> at which to begin copying bytes to the current stream. For CLOB and NCLOB data types, this must be an even number. </param>
      <param name="count">The number of bytes to be written to the current stream. For CLOB and NCLOB data types, this must be an even number. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> parameter is a null reference (Nothing in Visual Basic). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">A value in the <paramref name="offset" /> or <paramref name="count" /> parameter is not positive.-or- The sum of the <paramref name="offset" /> and <paramref name="count" /> parameters is larger than the <paramref name="buffer" /> length.-or- A value specified in the <paramref name="count" /> or <paramref name="offset" /> parameter is less than zero or greater than 4 gigabytes.-or- You must specify CLOB and NCLOB data types as an even number of bytes. </exception>
      <exception cref="T:System.InvalidOperationException">The operation is not within a transaction, the <see cref="T:System.Data.OracleClient.OracleLob" /> object is null, or the connection is closed. </exception>
      <exception cref="T:System.ObjectDisposedException">The object was closed or disposed. </exception>
      <exception cref="T:System.Data.OracleClient.OracleException">An Oracle error has occurred. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleLob.WriteByte(System.Byte)">
      <summary>Writes a byte to the current position in the <see cref="T:System.Data.OracleClient.OracleLob" /> stream, and advances the position within the stream by one byte.</summary>
      <param name="value">The byte to write to the stream. </param>
    </member>
    <member name="T:System.Data.OracleClient.OracleLobOpenMode">
      <summary>Specifies whether an <see cref="T:System.Data.OracleClient.OracleLob" /> should be opened in read-only or read/write mode.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleLobOpenMode.ReadOnly">
      <summary>The <see cref="T:System.Data.OracleClient.OracleLob" /> is opened in read-only mode.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleLobOpenMode.ReadWrite">
      <summary>The <see cref="T:System.Data.OracleClient.OracleLob" /> is opened in read/write mode.</summary>
    </member>
    <member name="T:System.Data.OracleClient.OracleMonthSpan">
      <summary>Represents a time interval in months and corresponds to the Oracle 9i INTERVAL YEAR TO MONTH data type.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.#ctor(System.Data.OracleClient.OracleMonthSpan)">
      <summary>Initializes a new <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure from an existing one.</summary>
      <param name="from">An existing <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure from which to create the new structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.#ctor(System.Int32)">
      <summary>Initializes a new <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure to the specified number of months.</summary>
      <param name="months">Number of months. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.#ctor(System.Int32,System.Int32)">
      <summary>Initializes a new <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure to a specified number years and months.</summary>
      <param name="years">Number of years. </param>
      <param name="months">Number of months. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The parameters specify an <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> value less than <see cref="F:System.Data.OracleClient.OracleMonthSpan.MinValue" /> or greater than <see cref="F:System.Data.OracleClient.OracleMonthSpan.MaxValue" />. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.CompareTo(System.Object)">
      <summary>Compares this <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure to the supplied object and returns an indication of their relative values.</summary>
      <returns>A signed number indicating the relative values of the instance and the object.Return Value Condition Less than zero This instance is less than the object. Zero This instance is the same as the object. Greater than zero This instance is greater than the object -or- The object is a null reference (Nothing). </returns>
      <param name="obj">The object to be compared. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.Equals(System.Data.OracleClient.OracleMonthSpan,System.Data.OracleClient.OracleMonthSpan)">
      <summary>Performs a logical comparison of two <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structures to determine if they are equal.</summary>
      <returns>true if the two values are equal, otherwise false.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.Equals(System.Object)">
      <summary>Compares the supplied object parameter to the <see cref="P:System.Data.OracleClient.OracleMonthSpan.Value" /> property of the <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> object.</summary>
      <returns>true if object is an instance of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> and the two are equal; otherwise false.</returns>
      <param name="value">The object to be compared. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.GetHashCode">
      <summary>Gets the hash code for this instance.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.GreaterThan(System.Data.OracleClient.OracleMonthSpan,System.Data.OracleClient.OracleMonthSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> to determine if the first is greater than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.GreaterThanOrEqual(System.Data.OracleClient.OracleMonthSpan,System.Data.OracleClient.OracleMonthSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> to determine if the first is greater than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleMonthSpan.IsNull">
      <summary>Gets a value indicating whether the <see cref="P:System.Data.OracleClient.OracleMonthSpan.Value" /> property of the <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure is null.</summary>
      <returns>true if <see cref="P:System.Data.OracleClient.OracleMonthSpan.Value" /> is null, otherwise false.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.LessThan(System.Data.OracleClient.OracleMonthSpan,System.Data.OracleClient.OracleMonthSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> to determine if the first is less than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.LessThanOrEqual(System.Data.OracleClient.OracleMonthSpan,System.Data.OracleClient.OracleMonthSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> to determine if the first is less than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleMonthSpan.MaxValue">
      <summary>Represents the maximum valid date value for an <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleMonthSpan.MinValue">
      <summary>Represents the minimum valid date value for an <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.NotEquals(System.Data.OracleClient.OracleMonthSpan,System.Data.OracleClient.OracleMonthSpan)">
      <summary>Performs a logical comparison of two instances of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> to determine if they are not equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are not equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are equal. If either instance of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleMonthSpan.Null">
      <summary>Represents a null value that can be assigned to the <see cref="P:System.Data.OracleClient.OracleMonthSpan.Value" /> property of an instance of the <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.op_Equality(System.Data.OracleClient.OracleMonthSpan,System.Data.OracleClient.OracleMonthSpan)">
      <summary>Performs a logical comparison of two <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structures to determine if they are equal.</summary>
      <returns>true if the two values are equal, otherwise false.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.op_Explicit(System.Data.OracleClient.OracleMonthSpan)~System.Int32">
      <summary>Converts an <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure to an Int32.</summary>
      <returns>An Int32.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure to convert to an Int32. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.op_Explicit(System.String)~System.Data.OracleClient.OracleMonthSpan">
      <summary>Converts a string to an <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure.</returns>
      <param name="x">A string to convert to an <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.op_GreaterThan(System.Data.OracleClient.OracleMonthSpan,System.Data.OracleClient.OracleMonthSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> to determine if the first is greater than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.op_GreaterThanOrEqual(System.Data.OracleClient.OracleMonthSpan,System.Data.OracleClient.OracleMonthSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> to determine if the first is greater than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.op_Inequality(System.Data.OracleClient.OracleMonthSpan,System.Data.OracleClient.OracleMonthSpan)">
      <summary>Performs a logical comparison of two instances of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> to determine if they are equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are not equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are equal. If either instance of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.op_LessThan(System.Data.OracleClient.OracleMonthSpan,System.Data.OracleClient.OracleMonthSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> to determine if the first is less than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.op_LessThanOrEqual(System.Data.OracleClient.OracleMonthSpan,System.Data.OracleClient.OracleMonthSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> to determine if the first is less than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.Parse(System.String)">
      <summary>Converts the specified <see cref="T:System.String" /> representation of a date and time to its <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> equivalent.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure equal to the date and time represented by the specified String.</returns>
      <param name="s">The String to be converted. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleMonthSpan.ToString">
      <summary>Converts this <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure to a <see cref="T:System.String" />.</summary>
      <returns>A String representing the <see cref="P:System.Data.OracleClient.OracleMonthSpan.Value" /> property of this <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleMonthSpan.Value">
      <summary>Gets the value of the <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure.</summary>
      <returns>The value of this <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> structure.</returns>
    </member>
    <member name="T:System.Data.OracleClient.OracleNumber">
      <summary>Represents a fixed precision and scale numeric value between -10 27 -1 and 10 27 -1 to be stored in or retrieved from a database. </summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.#ctor(System.Data.OracleClient.OracleNumber)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure using the supplied <see cref="T:System.Data.OracleClient.OracleNumber" />.</summary>
      <param name="from">The supplied <see cref="T:System.Data.OracleClient.OracleNumber" /> that will be used as the value of the new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.#ctor(System.Decimal)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure using the supplied <see cref="T:System.Decimal" /> value.</summary>
      <param name="decValue">The <see cref="T:System.Decimal" /> value to be stored as an <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.#ctor(System.Double)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleNumber" /> using the supplied double precision value.</summary>
      <param name="dblValue">The supplied double precision value that will the used as the value of the new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure using the supplied integer value.</summary>
      <param name="intValue">The supplied integer value that will be used as the value of the new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.#ctor(System.Int64)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure using the supplied long integer value.</summary>
      <param name="longValue">The supplied long integer value that will be used as the value of the new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Abs(System.Data.OracleClient.OracleNumber)">
      <summary>Gets the absolute value of the <see cref="T:System.Data.OracleClient.OracleNumber" /> parameter.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property contains the unsigned number representing the absolute value of the <see cref="T:System.Data.OracleClient.OracleNumber" /> parameter.</returns>
      <param name="n">An OracleNumber structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Acos(System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the inverse hyperbolic cosine of an <see cref="T:System.Data.OracleClient.OracleNumber" />.</summary>
      <returns>An angle, measured in radians.</returns>
      <param name="n">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Add(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the sum of the two <see cref="T:System.Data.OracleClient.OracleNumber" /> structures.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property contains the sum.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Asin(System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the inverse hyperbolic sine of an <see cref="T:System.Data.OracleClient.OracleNumber" />.</summary>
      <returns>An angle, measured in radians.</returns>
      <param name="n">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Atan(System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the inverse hyperbolic tangent of an <see cref="T:System.Data.OracleClient.OracleNumber" />.</summary>
      <returns>An angle, measured in radians.</returns>
      <param name="n">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Atan2(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the inverse hyperbolic tangent of two <see cref="T:System.Data.OracleClient.OracleNumber" /> structures.</summary>
      <returns>An angle, measured in radians.</returns>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Ceiling(System.Data.OracleClient.OracleNumber)">
      <summary>Returns the smallest whole number greater than or equal to the specified <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleNumber" /> representing the smallest whole number greater than or equal to the specified <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</returns>
      <param name="n">The <see cref="T:System.Data.OracleClient.OracleNumber" /> structure for which the ceiling value is to be calculated. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.CompareTo(System.Object)">
      <summary>Compares this instance of the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to the supplied object and returns an indication of their relative values.</summary>
      <returns>A signed number indicating the relative values of the instance and the object.Return Value Condition Less than zero This instance is less than the object. Zero This instance is the same as the object. Greater than zero This instance is greater than the object, or the object is a null reference (Nothing in Visual Basic) </returns>
      <param name="obj">The object to be compared. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Cos(System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the cosine of an <see cref="T:System.Data.OracleClient.OracleNumber" />.</summary>
      <returns>The cosine of <paramref name="n" />.</returns>
      <param name="n">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Cosh(System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the hyperbolic cosine of an <see cref="T:System.Data.OracleClient.OracleNumber" />.</summary>
      <returns>The hyperbolic cosine of <paramref name="n" />.</returns>
      <param name="n">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Divide(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the results of dividing the first <see cref="T:System.Data.OracleClient.OracleNumber" /> structure by the second.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property contains the results of the division.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleNumber.E">
      <summary>Returns the value e-2.718.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Equals(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleNumber" /> structures to determine if they are equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are not equal. If either instance of <see cref="T:System.Data.OracleClient.OracleNumber" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Equals(System.Object)">
      <summary>Compares the supplied object parameter to the <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property of the <see cref="T:System.Data.OracleClient.OracleNumber" /> object.</summary>
      <returns>true if the object is an instance of <see cref="T:System.Data.OracleClient.OracleNumber" /> and the two are equal, otherwise false.</returns>
      <param name="value">The object to be compared. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Exp(System.Data.OracleClient.OracleNumber)">
      <summary>Calculates e raised to the power of an <see cref="T:System.Data.OracleClient.OracleNumber" />. The constant e equals 2.71828182845904, the base of the natural logarithm.</summary>
      <returns>A number raised to the power of <paramref name="p" />.</returns>
      <param name="p">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Floor(System.Data.OracleClient.OracleNumber)">
      <summary>Rounds a specified <see cref="T:System.Data.OracleClient.OracleNumber" /> number to the next lower whole number.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure containing the whole number portion of this <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</returns>
      <param name="n">The <see cref="T:System.Data.OracleClient.OracleNumber" /> structure for which the floor value is to be calculated. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.GetHashCode">
      <summary>Returns the hash code for this instance of the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.GreaterThan(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Performs a logical comparison of two <see cref="T:System.Data.OracleClient.OracleNumber" /> structures to determine whether the first is greater than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleNumber" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.GreaterThanOrEqual(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleNumber" /> parameters to determine whether the first is greater than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleNumber" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleNumber.IsNull">
      <summary>Indicates whether or not the <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> of this <see cref="T:System.Data.OracleClient.OracleNumber" /> structure is null.</summary>
      <returns>true if <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> is null, otherwise false.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.LessThan(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Performs a logical comparison of two <see cref="T:System.Data.OracleClient.OracleNumber" /> structures to determine whether the first is less than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleNumber" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.LessThanOrEqual(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleNumber" /> parameters to determine whether the first is less than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleNumber" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Log(System.Data.OracleClient.OracleNumber)">
      <summary>Calculates natural logarithm of an <see cref="T:System.Data.OracleClient.OracleNumber" />. Natural logarithms are based on the constant e (2.71828182845904).</summary>
      <returns>The natural logarithm of <paramref name="n" />.</returns>
      <param name="n">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Log(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Calculates an <see cref="T:System.Data.OracleClient.OracleNumber" /> to the base you specify.</summary>
      <returns>The logarithm of a specified number in a specified base.</returns>
      <param name="n">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="newBase">A user-specified <see cref="T:System.Data.OracleClient.OracleNumber" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Log(System.Data.OracleClient.OracleNumber,System.Int32)">
      <summary>Calculates an <see cref="T:System.Data.OracleClient.OracleNumber" /> to the base you specify.</summary>
      <returns>The logarithm of a specified number in a specified base.</returns>
      <param name="n">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="newBase">User-specified integer. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Log10(System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the base 10 logarithm of a number.</summary>
      <returns>The base 10 logarithm of the specified number.</returns>
      <param name="n">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Max(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Returns the larger of the two <see cref="T:System.Data.OracleClient.OracleNumber" /> parameter values.</summary>
      <returns>The larger of <paramref name="x" /> or <paramref name="y" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleNumber.MaxPrecision">
      <summary>A constant representing the largest possible value for precision comparison.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleNumber.MaxScale">
      <summary>A constant representing the maximum value for scale comparison.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleNumber.MaxValue">
      <summary>A constant representing the maximum value of an <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Min(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Returns the smaller of the two <see cref="T:System.Data.OracleClient.OracleNumber" /> parameter values.</summary>
      <returns>The smaller of <paramref name="x" /> or <paramref name="y" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleNumber.MinScale">
      <summary>A constant representing the minimum value for scale comparison.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleNumber.MinusOne">
      <summary>Returns the value -1.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleNumber.MinValue">
      <summary>A constant representing the minimum value for an <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Modulo(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the modulus from dividing the first <see cref="T:System.Data.OracleClient.OracleNumber" /> structure by the second.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property contains the modulus from the results of the division operation.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Multiply(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the product of the two <see cref="T:System.Data.OracleClient.OracleNumber" /> parameters.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property contains the product of the multiplication.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Negate(System.Data.OracleClient.OracleNumber)">
      <summary>Negates the value of the <see cref="T:System.Data.OracleClient.OracleNumber" /> parameter.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleNumber" /> with the negative value of x , or zero, if <paramref name="x" /> is zero.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.NotEquals(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleNumber" /> parameters to determine whether they are not equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are not equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are equal. If either instance of <see cref="T:System.Data.OracleClient.OracleNumber" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleNumber.Null">
      <summary>Represents a null value that can be assigned to the <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property of an instance of the <see cref="T:System.Data.OracleClient.OracleNumber" /> class.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleNumber.One">
      <summary>Returns the value 1.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Addition(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the sum of the two <see cref="T:System.Data.OracleClient.OracleNumber" /> structures.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property contains the sum.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Division(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the results of dividing the first <see cref="T:System.Data.OracleClient.OracleNumber" /> structure by the second.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property contains the results of the division.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Equality(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleNumber" /> structures to determine whether they are equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are not equal. If either instance of <see cref="T:System.Data.OracleClient.OracleNumber" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Explicit(System.Data.OracleClient.OracleNumber)~System.Int32">
      <summary>Converts the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to <see cref="T:System.Int32" />.</summary>
      <returns>A new Int32 structure whose value equals the <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> of the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</returns>
      <param name="x">The <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to be converted. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Explicit(System.Data.OracleClient.OracleNumber)~System.Int64">
      <summary>Converts the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to <see cref="T:System.Int64" />.</summary>
      <returns>A new Int64 structure whose value equals the <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> of the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</returns>
      <param name="x">The <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to be converted. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Explicit(System.Data.OracleClient.OracleNumber)~System.Decimal">
      <summary>Converts the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to <see cref="T:System.Decimal" />.</summary>
      <returns>A new Decimal structure whose value equals the <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> of the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</returns>
      <param name="x">The <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to be converted. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Explicit(System.Data.OracleClient.OracleNumber)~System.Double">
      <summary>Converts the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to <see cref="T:System.Double" />.</summary>
      <returns>A new Double structure whose value equals the <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> of the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</returns>
      <param name="x">The <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to be converted. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Explicit(System.Decimal)~System.Data.OracleClient.OracleNumber">
      <summary>Converts the supplied Decimal structure to an <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> is equal to the value of the Decimal structure.</returns>
      <param name="x">The Decimal structure to be converted. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Explicit(System.Double)~System.Data.OracleClient.OracleNumber">
      <summary>Converts the supplied Double structure to an <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property equals the value of the Double structure.</returns>
      <param name="x">The Double structure to be converted. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Explicit(System.Int32)~System.Data.OracleClient.OracleNumber">
      <summary>Converts the supplied Int32 structure to an <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property equals the value of the Int32 structure.</returns>
      <param name="x">The integer structure to be converted. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Explicit(System.Int64)~System.Data.OracleClient.OracleNumber">
      <summary>Converts the supplied Int64 structure to an <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property is equal to the value of the Int64 structure.</returns>
      <param name="x">The Int64 structure to be converted. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Explicit(System.String)~System.Data.OracleClient.OracleNumber">
      <summary>Converts the supplied String to an <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> equals the value of the String structure.</returns>
      <param name="x">The String to be converted. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_GreaterThan(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Performs a logical comparison of two <see cref="T:System.Data.OracleClient.OracleNumber" /> structures to determine whether the first is greater than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleNumber" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_GreaterThanOrEqual(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleNumber" /> parameters to determine whether the first is greater than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleNumber" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Inequality(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleNumber" /> parameters to determine whether they are equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are not equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are equal. If either instance of <see cref="T:System.Data.OracleClient.OracleNumber" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_LessThan(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Performs a logical comparison of two <see cref="T:System.Data.OracleClient.OracleNumber" /> structures to determine whether the first is less than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleNumber" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_LessThanOrEqual(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleNumber" /> parameters to determine whether the first is less than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleNumber" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Modulus(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the remainder left over from dividing an <see cref="T:System.Data.OracleClient.OracleNumber" /> structure by a second <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property contains the modulus of the division.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Multiply(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the product of two <see cref="T:System.Data.OracleClient.OracleNumber" /> structures.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property contains the product of the multiplication.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_Subtraction(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the result of subtracting the second <see cref="T:System.Data.OracleClient.OracleNumber" /> structure from the first.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property contains the results of the subtraction.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.op_UnaryNegation(System.Data.OracleClient.OracleNumber)">
      <summary>Negates the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose value contains the results of the negation.</returns>
      <param name="x">The <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to be negated. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Parse(System.String)">
      <summary>Converts the <see cref="T:System.String" /> representation of a number to its <see cref="T:System.Data.OracleClient.OracleNumber" /> equivalent.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleNumber" /> equivalent to the value contained in the specified <see cref="T:System.String" />.</returns>
      <param name="s">The String to be parsed. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleNumber.PI">
      <summary>Returns the value of pi-3.1415926535897932384626433832795028842.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Pow(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the result of raising a specified <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to the power specified by a second <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</summary>
      <returns>The number <paramref name="x" /> raised to the power <paramref name="y" />.</returns>
      <param name="x">The <see cref="T:System.Data.OracleClient.OracleNumber" /> to be raised to a power. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> that specifies a power. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Pow(System.Data.OracleClient.OracleNumber,System.Int32)">
      <summary>Calculates the result of raising a specified <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to the power specified by an Int32 structure.</summary>
      <returns>The number <paramref name="x" /> raised to the power <paramref name="y" />.</returns>
      <param name="x">The <see cref="T:System.Data.OracleClient.OracleNumber" /> to be raised to a power. </param>
      <param name="y">An Int32 structure that specifies a power. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Round(System.Data.OracleClient.OracleNumber,System.Int32)">
      <summary>Gets the number nearest the specified <see cref="T:System.Data.OracleClient.OracleNumber" /> structure's value with the specified precision.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure containing the results of the rounding operation.</returns>
      <param name="n">The <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to be rounded. </param>
      <param name="position">The number of significant fractional digits (precision) in the return value. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Shift(System.Data.OracleClient.OracleNumber,System.Int32)">
      <summary>Shifts the specified number of digits to the left or right.</summary>
      <returns>The result of the operation.</returns>
      <param name="n">Number to operate on. </param>
      <param name="digits">The number of decimal places to shift. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Sign(System.Data.OracleClient.OracleNumber)">
      <summary>Gets a value indicating the sign of an <see cref="T:System.Data.OracleClient.OracleNumber" /> structure's <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property.</summary>
      <returns>A number indicating the sign of the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure.</returns>
      <param name="n">The <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose sign is to be evaluated. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Sin(System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the sine of an <see cref="T:System.Data.OracleClient.OracleNumber" />.</summary>
      <returns>The sine of <paramref name="n" />.</returns>
      <param name="n">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Sinh(System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the hyperbolic sine of an <see cref="T:System.Data.OracleClient.OracleNumber" />.</summary>
      <returns>The hyperbolic sine of <paramref name="n" />.</returns>
      <param name="n">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Sqrt(System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the square root of the <see cref="T:System.Data.OracleClient.OracleNumber" /> parameter.</summary>
      <returns>The square root of <paramref name="n" />.</returns>
      <param name="n">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Subtract(System.Data.OracleClient.OracleNumber,System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the result of subtracting the second <see cref="T:System.Data.OracleClient.OracleNumber" /> structure from the first.</summary>
      <returns>A new <see cref="T:System.Data.OracleClient.OracleNumber" /> structure whose Value property contains the results of the subtraction.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Tan(System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the tangent of an <see cref="T:System.Data.OracleClient.OracleNumber" />.</summary>
      <returns>The tangent of <paramref name="n" />.</returns>
      <param name="n">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Tanh(System.Data.OracleClient.OracleNumber)">
      <summary>Calculates the hyperbolic tangent of an <see cref="T:System.Data.OracleClient.OracleNumber" />.</summary>
      <returns>The hyperbolic tangent of <paramref name="n" />.</returns>
      <param name="n">An <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.ToString">
      <summary>Converts this <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to <see cref="T:System.String" />.</summary>
      <returns>A new <see cref="T:System.String" /> object containing the string representation of the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure's <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> property.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleNumber.Truncate(System.Data.OracleClient.OracleNumber,System.Int32)">
      <summary>Truncates the specified <see cref="T:System.Data.OracleClient.OracleNumber" /> structure's value to the desired position.</summary>
      <returns>A <see cref="T:System.Data.OracleClient.OracleNumber" /> structure with its <see cref="P:System.Data.OracleClient.OracleNumber.Value" /> truncated to the specified <paramref name="position" />.</returns>
      <param name="n">The <see cref="T:System.Data.OracleClient.OracleNumber" /> structure to be truncated. </param>
      <param name="position">The decimal position to which the number will be truncated. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleNumber.Value">
      <summary>Gets the value of the <see cref="T:System.Data.OracleClient.OracleNumber" /> structure. This property is read-only.</summary>
      <returns>A number in the range -79,228,162,514,264,337,593,543,950,335 through 79,228,162,514, 264,337,593,543,950,335.</returns>
    </member>
    <member name="F:System.Data.OracleClient.OracleNumber.Zero">
      <summary>Returns the value 0.</summary>
    </member>
    <member name="T:System.Data.OracleClient.OracleParameter">
      <summary>Represents a parameter to an <see cref="T:System.Data.OracleClient.OracleCommand" /> and optionally its mapping to a <see cref="T:System.Data.DataColumn" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleParameter" /> class.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameter.#ctor(System.String,System.Data.OracleClient.OracleType)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleParameter" /> class that uses the parameter name and data type.</summary>
      <param name="name">The name of the parameter. </param>
      <param name="oracleType">One of the <see cref="T:System.Data.OracleClient.OracleType" /> values. </param>
      <exception cref="T:System.ArgumentException">The value supplied in the <paramref name="oracleType" /> parameter is an invalid back-end data type. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameter.#ctor(System.String,System.Data.OracleClient.OracleType,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleParameter" /> class that uses the parameter name, data type, and length.</summary>
      <param name="name">The name of the parameter. </param>
      <param name="oracleType">One of the <see cref="T:System.Data.OracleClient.OracleType" /> values. </param>
      <param name="size">The length of the parameter. </param>
      <exception cref="T:System.ArgumentException">The value supplied in the <paramref name="oracleType" /> parameter is an invalid back-end data type. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameter.#ctor(System.String,System.Data.OracleClient.OracleType,System.Int32,System.Data.ParameterDirection,System.Boolean,System.Byte,System.Byte,System.String,System.Data.DataRowVersion,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleParameter" /> class that uses the parameter name, data type, length, source column name, parameter direction, numeric precision, and other properties.</summary>
      <param name="name">The name of the parameter. </param>
      <param name="oracleType">One of the <see cref="T:System.Data.OracleClient.OracleType" /> values. </param>
      <param name="size">The length of the parameter. </param>
      <param name="direction">One of the <see cref="T:System.Data.ParameterDirection" /> values. </param>
      <param name="isNullable">true if the value of the field can be null, otherwise, false. </param>
      <param name="precision">The total number of digits to the left and right of the decimal point to which <see cref="P:System.Data.OracleClient.OracleParameter.Value" /> is resolved. </param>
      <param name="scale">The total number of decimal places to which <see cref="P:System.Data.OracleClient.OracleParameter.Value" /> is resolved. </param>
      <param name="srcColumn">The name of the source column. </param>
      <param name="srcVersion">One of the <see cref="T:System.Data.DataRowVersion" /> values. </param>
      <param name="value">An <see cref="T:System.Object" /> that is the value of the <see cref="T:System.Data.OracleClient.OracleParameter" />. </param>
      <exception cref="T:System.ArgumentException">The value supplied in the <paramref name="oracleType" /> parameter is an invalid back-end data type. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameter.#ctor(System.String,System.Data.OracleClient.OracleType,System.Int32,System.Data.ParameterDirection,System.String,System.Data.DataRowVersion,System.Boolean,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleParameter" /> class that uses the parameter name, data type, size, direction, source column, source version, and other properties.</summary>
      <param name="name">The name of the parameter to map.</param>
      <param name="oracleType">One of the <see cref="T:System.Data.OracleClient.OracleType" /> values.</param>
      <param name="size">The length of the parameter.</param>
      <param name="direction">One of the <see cref="T:System.Data.ParameterDirection" /> values.</param>
      <param name="sourceColumn">The name of the source column. </param>
      <param name="sourceVersion">One of the <see cref="T:System.Data.DataRowVersion" /> values. </param>
      <param name="sourceColumnNullMapping">true if the source column is nullable, otherwise false.</param>
      <param name="value">An <see cref="T:System.Object" /> that is the value of the <see cref="T:System.Data.OracleClient.OracleParameter" />.</param>
      <exception cref="T:System.ArgumentException">The value supplied in the <paramref name="oracleType" /> parameter is an invalid back-end data type.</exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameter.#ctor(System.String,System.Data.OracleClient.OracleType,System.Int32,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleParameter" /> class that uses the parameter name, data type, length, and source column name.</summary>
      <param name="name">The name of the parameter. </param>
      <param name="oracleType">One of the <see cref="T:System.Data.OracleClient.OracleType" /> values. </param>
      <param name="size">The length of the parameter. </param>
      <param name="srcColumn">The name of the source column. </param>
      <exception cref="T:System.ArgumentException">The value supplied in the <paramref name="oracleType" /> parameter is an invalid back-end data type. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameter.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleParameter" /> class that uses the parameter name and an <see cref="T:System.Data.OracleClient.OracleParameter" /> object.</summary>
      <param name="name">The name of the parameter. </param>
      <param name="value">An <see cref="T:System.Data.OracleClient.OracleParameter" /> object. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameter.DbType">
      <summary>Gets or sets the <see cref="T:System.Data.DbType" /> of the parameter.</summary>
      <returns>One of the <see cref="T:System.Data.DbType" /> values. The default is <see cref="F:System.Data.DbType.AnsiString" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The property was not set to a valid <see cref="T:System.Data.DbType" />. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameter.Direction">
      <summary>Gets or sets a value that indicates whether the parameter is input-only, output-only, bidirectional, or a stored procedure return value parameter.</summary>
      <returns>One of the <see cref="T:System.Data.ParameterDirection" /> values. The default is Input.</returns>
      <exception cref="T:System.ArgumentException">The property was not set to one of the valid <see cref="T:System.Data.ParameterDirection" /> values.</exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameter.IsNullable">
      <summary>Gets or sets a value that indicates whether the parameter accepts null values.</summary>
      <returns>true if null values are accepted, otherwise false. The default is false.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameter.Offset">
      <summary>Gets or sets the offset to the <see cref="P:System.Data.OracleClient.OracleParameter.Value" /> property.</summary>
      <returns>The offset to the <see cref="P:System.Data.OracleClient.OracleParameter.Value" />. The default is 0.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameter.OracleType">
      <summary>Gets or sets the <see cref="T:System.Data.OracleClient.OracleType" /> of the parameter.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleType" /> value that is the <see cref="T:System.Data.OracleClient.OracleType" /> of the parameter. The default is <see cref="F:System.Data.OracleClient.OracleType.VarChar" />.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameter.ParameterName">
      <summary>Gets or sets the name of the <see cref="T:System.Data.OracleClient.OracleParameter" />.</summary>
      <returns>The name of the <see cref="T:System.Data.OracleClient.OracleParameter" />. The default is an empty string.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameter.Precision">
      <summary>Gets or sets the maximum number of digits used to represent the <see cref="P:System.Data.OracleClient.OracleParameter.Value" /> property.</summary>
      <returns>The maximum number of digits used to represent the <see cref="P:System.Data.OracleClient.OracleParameter.Value" /> property. The default value is 0, which indicates that the data provider sets the precision for <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameter.ResetDbType">
      <summary>Resets the type associated with this <see cref="T:System.Data.OracleClient.OracleParameter" />.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameter.ResetOracleType">
      <summary>Resets the type associated with this <see cref="T:System.Data.OracleClient.OracleParameter" />.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameter.Scale">
      <summary>Gets or sets the number of decimal places to which <see cref="P:System.Data.OracleClient.OracleParameter.Value" /> is resolved.</summary>
      <returns>The number of decimal places to which <see cref="P:System.Data.OracleClient.OracleParameter.Value" /> is resolved. The default is 0.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameter.Size">
      <summary>Gets or sets the maximum size, in bytes, of the data within the column.</summary>
      <returns>The maximum size, in bytes, of the data within the column. The default value is 0 (to be used when you do not want to specify the maximum size of the value).</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameter.SourceColumn">
      <summary>Gets or sets the name of the source column mapped to the <see cref="T:System.Data.DataSet" /> and used for loading or returning the <see cref="P:System.Data.OracleClient.OracleParameter.Value" /></summary>
      <returns>The name of the source column mapped to the <see cref="T:System.Data.DataSet" />. The default is an empty string ("").</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameter.SourceColumnNullMapping">
      <summary>Sets or gets a value which indicates whether the source column is nullable. This allows <see cref="T:System.Data.OracleClient.OracleCommandBuilder" /> to correctly generate Update statements for nullable columns.</summary>
      <returns>True if the source column is nullable, otherwise false.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameter.SourceVersion">
      <summary>Gets or sets the <see cref="T:System.Data.DataRowVersion" /> to use when you load <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
      <returns>One of the <see cref="T:System.Data.DataRowVersion" /> values. The default is Current.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameter.System#ICloneable#Clone">
      <summary>For a description of this member, see <see cref="M:System.ICloneable.Clone" />.</summary>
      <returns>A new object that is a copy of this instance.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameter.ToString">
      <summary>Gets a string that contains the <see cref="P:System.Data.OracleClient.OracleParameter.ParameterName" />.</summary>
      <returns>A string that contains the <see cref="P:System.Data.OracleClient.OracleParameter.ParameterName" />.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameter.Value">
      <summary>Gets or sets the value of the parameter.</summary>
      <returns>An object that is the value of the parameter. The default value is null.</returns>
    </member>
    <member name="T:System.Data.OracleClient.OracleParameterCollection">
      <summary>Represents a collection of parameters relevant to an <see cref="T:System.Data.OracleClient.OracleCommand" /> as well as their respective mappings to columns in a <see cref="T:System.Data.DataSet" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> class.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.Add(System.Data.OracleClient.OracleParameter)">
      <summary>Adds the specified <see cref="T:System.Data.OracleClient.OracleParameter" /> to the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</summary>
      <returns>A reference to the new <see cref="T:System.Data.OracleClient.OracleParameter" /> object.</returns>
      <param name="value">The <see cref="T:System.Data.OracleClient.OracleParameter" /> to add to the collection. </param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Data.OracleClient.OracleParameter" /> specified in the <paramref name="value" /> parameter is already added to this or another <see cref="T:System.Data.OracleClient.OracleParameterCollection" />. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is null. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.Add(System.Object)">
      <summary>Adds the specified <see cref="T:System.Data.OracleClient.OracleParameter" /> object to the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</summary>
      <returns>The index of the new <see cref="T:System.Data.OracleClient.OracleParameter" /> object in the collection.</returns>
      <param name="value">The <see cref="T:System.Data.OracleClient.OracleParameter" /> object to add to the collection.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.Add(System.String,System.Data.OracleClient.OracleType)">
      <summary>Adds an <see cref="T:System.Data.OracleClient.OracleParameter" /> to the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> given the parameter name and data type.</summary>
      <returns>A reference to the new <see cref="T:System.Data.OracleClient.OracleParameter" /> object.</returns>
      <param name="parameterName">The name of the parameter. </param>
      <param name="dataType">One of the <see cref="T:System.Data.OracleClient.OracleType" /> values. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.Add(System.String,System.Data.OracleClient.OracleType,System.Int32)">
      <summary>Adds an <see cref="T:System.Data.OracleClient.OracleParameter" /> to the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> given the parameter name, data type, and column length.</summary>
      <returns>A reference to the new <see cref="T:System.Data.OracleClient.OracleParameter" /> object.</returns>
      <param name="parameterName">The name of the parameter. </param>
      <param name="dataType">One of the <see cref="T:System.Data.OracleClient.OracleType" /> values. </param>
      <param name="size">The length of the column. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.Add(System.String,System.Data.OracleClient.OracleType,System.Int32,System.String)">
      <summary>Adds an <see cref="T:System.Data.OracleClient.OracleParameter" /> to the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> given the parameter name, data type, column length, and source column name.</summary>
      <returns>A reference to the new <see cref="T:System.Data.OracleClient.OracleParameter" /> object.</returns>
      <param name="parameterName">The name of the parameter. </param>
      <param name="dataType">One of the <see cref="T:System.Data.OracleClient.OracleType" /> values. </param>
      <param name="size">The length of the column. </param>
      <param name="srcColumn">The name of the source column. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.Add(System.String,System.Object)">
      <summary>Adds an <see cref="T:System.Data.OracleClient.OracleParameter" /> to the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> given the parameter name and value.</summary>
      <returns>A reference to the new <see cref="T:System.Data.OracleClient.OracleParameter" /> object.</returns>
      <param name="parameterName">The name of the parameter. </param>
      <param name="value">The <see cref="P:System.Data.OracleClient.OracleParameter.Value" /> of the <see cref="T:System.Data.OracleClient.OracleParameter" /> to add to the collection. </param>
      <exception cref="T:System.InvalidCastException">The <paramref name="value" /> parameter is not an <see cref="T:System.Data.OracleClient.OracleParameter" />. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.AddRange(System.Array)">
      <summary>Adds an array of values to the end of the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</summary>
      <param name="values">The <see cref="T:System.Array" /> values to add.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.AddRange(System.Data.OracleClient.OracleParameter[])">
      <summary>Adds an array of <see cref="T:System.Data.OracleClient.OracleParameter" /> values to the end of the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</summary>
      <param name="values">The <see cref="T:System.Data.OracleClient.OracleParameter" /> values to add.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.AddWithValue(System.String,System.Object)">
      <summary>Adds a value to the end of the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</summary>
      <returns>A <see cref="T:System.Data.OracleClient.OracleParameter" /> object.</returns>
      <param name="parameterName">The name of the parameter.</param>
      <param name="value">The value to be added.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.Clear">
      <summary>Removes all of the <see cref="T:System.Data.OracleClient.OracleParameter" /> objects from the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.Contains(System.Data.OracleClient.OracleParameter)">
      <summary>Determines whether the specified <see cref="T:System.Data.OracleClient.OracleParameter" /> is in the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</summary>
      <returns>true if the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> contains the value, false otherwise.</returns>
      <param name="value">The <see cref="T:System.Data.OracleClient.OracleParameter" /> value.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.Contains(System.Object)">
      <summary>Determines whether the specified object is in the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</summary>
      <returns>true if the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> contains the value, false otherwise.</returns>
      <param name="value">The <see cref="T:System.Object" /> value.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.Contains(System.String)">
      <summary>Indicates whether an <see cref="T:System.Data.OracleClient.OracleParameter" /> with the specified name is contained in the collection.</summary>
      <returns>true if the <see cref="T:System.Data.OracleClient.OracleParameter" /> is in the collection; otherwise, false.</returns>
      <param name="parameterName">The name of the <see cref="T:System.Data.OracleClient.OracleParameter" /> to look for in the collection.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies all the elements of the current <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> to the specified one-dimensional <see cref="T:System.Array" /> starting at the specified destination <see cref="T:System.Array" /> index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from the current <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</param>
      <param name="index">A 32-bit integer that represents the index in the <see cref="T:System.Array" /> at which copying begins.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.CopyTo(System.Data.OracleClient.OracleParameter[],System.Int32)">
      <summary>Copies all the elements of the current <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> to the specified <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> starting at the specified destination index.</summary>
      <param name="array">The <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> that is the destination of the elements copied from the current <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</param>
      <param name="index">A 32-bit integer that represents the index in the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> at which copying begins.</param>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameterCollection.Count">
      <summary>Returns an Integer containing the number of elements in the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />. Read-only. </summary>
      <returns>The number of elements in the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> as an Integer.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.IndexOf(System.Data.OracleClient.OracleParameter)">
      <summary>Gets the location of the specified <see cref="T:System.Data.OracleClient.OracleParameter" /> within the collection.</summary>
      <returns>The zero-based location of the specified <see cref="T:System.Data.OracleClient.OracleParameter" /> that is a <see cref="T:System.Data.OracleClient.OracleParameter" /> within the collection. </returns>
      <param name="value">The <see cref="T:System.Data.OracleClient.OracleParameter" /> to find.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.IndexOf(System.Object)">
      <summary>Gets the location of the specified <see cref="T:System.Object" /> within the collection.</summary>
      <returns>The zero-based location of the specified <see cref="T:System.Object" /> that is a <see cref="T:System.Data.OracleClient.OracleParameter" /> within the collection. </returns>
      <param name="value">The <see cref="T:System.Object" /> to find.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.IndexOf(System.String)">
      <summary>Gets the location of the specified <see cref="T:System.Data.OracleClient.OracleParameter" /> with the specified name.</summary>
      <returns>The zero-based location of the specified <see cref="T:System.Data.OracleClient.OracleParameter" /> that is a <see cref="T:System.Data.OracleClient.OracleParameter" /> within the collection. </returns>
      <param name="parameterName">The case-sensitive name of the <see cref="T:System.Data.OracleClient.OracleParameter" /> to find.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.Insert(System.Int32,System.Data.OracleClient.OracleParameter)">
      <summary>Inserts a <see cref="T:System.Data.OracleClient.OracleParameter" /> object into the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> at the specified index.</summary>
      <param name="index">The zero-based index at which value should be inserted.</param>
      <param name="value">A <see cref="T:System.Data.OracleClient.OracleParameter" /> object to be inserted in the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.Insert(System.Int32,System.Object)">
      <summary>Inserts a <see cref="T:System.Object" /> into the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> at the specified index.</summary>
      <param name="index">The zero-based index at which value should be inserted.</param>
      <param name="value">A <see cref="T:System.Object" /> to be inserted in the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</param>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameterCollection.IsFixedSize">
      <summary>Gets a value that indicates whether the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> has a fixed size. </summary>
      <returns>Returns true if the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> has a fixed size, false otherwise.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameterCollection.IsReadOnly">
      <summary>Gets a value that indicates whether the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> is read-only. </summary>
      <returns>Returns true if the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> is read only, false otherwise.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameterCollection.IsSynchronized">
      <summary>Gets a value that indicates whether the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> is synchronized.</summary>
      <returns>Returns true if the <see cref="T:System.Data.OracleClient.OracleParameterCollection" /> is synchronized, false otherwise.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameterCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.Data.OracleClient.OracleParameter" /> at the specified index.</summary>
      <returns>The <see cref="T:System.Data.OracleClient.OracleParameter" /> at the specified index.</returns>
      <param name="index">The zero-based index of the parameter to retrieve. </param>
      <exception cref="T:System.IndexOutOfRangeException">The index specified does not exist. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameterCollection.Item(System.String)">
      <summary>Gets or sets the <see cref="T:System.Data.OracleClient.OracleParameter" /> with the specified name.</summary>
      <returns>The <see cref="T:System.Data.OracleClient.OracleParameter" /> with the specified name.</returns>
      <param name="parameterName">The name of the parameter to retrieve. </param>
      <exception cref="T:System.IndexOutOfRangeException">The name specified does not exist. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.Remove(System.Data.OracleClient.OracleParameter)">
      <summary>Removes the specified <see cref="T:System.Data.OracleClient.OracleParameter" /> object from the collection.</summary>
      <param name="value">A <see cref="T:System.Data.OracleClient.OracleParameter" /> object to remove from the collection.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.Remove(System.Object)">
      <summary>Removes the specified <see cref="T:System.Data.OracleClient.OracleParameter" /> object from the collection.</summary>
      <param name="value">A <see cref="T:System.Object" /> object to remove from the collection.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.RemoveAt(System.Int32)">
      <summary>Removes the <see cref="T:System.Data.OracleClient.OracleParameter" /> object at the specified index from the collection.</summary>
      <param name="index">The zero-based index of the <see cref="T:System.Data.OracleClient.OracleParameter" /> object to remove.</param>
    </member>
    <member name="M:System.Data.OracleClient.OracleParameterCollection.RemoveAt(System.String)">
      <summary>Removes the <see cref="T:System.Data.OracleClient.OracleParameter" /> object with the specified name from the collection.</summary>
      <param name="parameterName">The name of the <see cref="T:System.Data.OracleClient.OracleParameter" /> object to remove.</param>
    </member>
    <member name="P:System.Data.OracleClient.OracleParameterCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Data.OracleClient.OracleParameterCollection" />.</returns>
    </member>
    <member name="T:System.Data.OracleClient.OraclePermission">
      <summary>Enables the .NET Framework Data Provider for Oracle to help ensure that a user has a security level adequate to access an Oracle database. </summary>
    </member>
    <member name="M:System.Data.OracleClient.OraclePermission.#ctor(System.Security.Permissions.PermissionState)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OraclePermission" /> class with one of the <see cref="T:System.Security.Permissions.PermissionState" /> values.</summary>
      <param name="state">One of the <see cref="T:System.Security.Permissions.PermissionState" /> values. </param>
    </member>
    <member name="M:System.Data.OracleClient.OraclePermission.Add(System.String,System.String,System.Data.KeyRestrictionBehavior)">
      <summary>Creates a new set of permissions.</summary>
      <param name="connectionString">The connection string.</param>
      <param name="restrictions">The key restrictions.</param>
      <param name="behavior">One of the <see cref="T:System.Data.KeyRestrictionBehavior" /> enumerations.</param>
    </member>
    <member name="P:System.Data.OracleClient.OraclePermission.AllowBlankPassword">
      <summary>Gets a value indicating whether a blank password is allowed.</summary>
      <returns>true if a blank password is allowed, otherwise false.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OraclePermission.Copy">
      <returns>A copy of the current permission object.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OraclePermission.FromXml(System.Security.SecurityElement)">
      <param name="securityElement">A <see cref="System.Security.SecurityElement" /> expression.</param>
    </member>
    <member name="M:System.Data.OracleClient.OraclePermission.Intersect(System.Security.IPermission)">
      <returns>A new permission that represents the intersection of the current permission and the specified permission. This new permission is null if the intersection is empty.</returns>
      <param name="target">A permission to intersect with the current permission. It must be of the same type as the current permission. </param>
    </member>
    <member name="M:System.Data.OracleClient.OraclePermission.IsSubsetOf(System.Security.IPermission)">
      <returns>true if the current permission is a subset of the specified permission; otherwise, false.</returns>
      <param name="target">A permission that is to be tested for the subset relationship. This permission must be of the same type as the current permission. </param>
    </member>
    <member name="M:System.Data.OracleClient.OraclePermission.IsUnrestricted">
      <summary>Returns a value indicating whether the permission can be represented as unrestricted without any knowledge of the permission semantics.</summary>
      <returns>true if the permission can be represented as unrestricted.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OraclePermission.ToXml">
      <summary>Creates an XML encoding of the security object and its current state.</summary>
      <returns>An XML encoding of the security object, including any state information.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OraclePermission.Union(System.Security.IPermission)">
      <returns>A new permission that represents the union of the current permission and the specified permission.</returns>
      <param name="target">A <see cref="System.Security.IPermission" /> expression.</param>
    </member>
    <member name="T:System.Data.OracleClient.OraclePermissionAttribute">
      <summary>Associates a security action with a custom security attribute.  </summary>
    </member>
    <member name="M:System.Data.OracleClient.OraclePermissionAttribute.#ctor(System.Security.Permissions.SecurityAction)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OraclePermissionAttribute" /> class.</summary>
      <param name="action">One of the <see cref="T:System.Security.Permissions.SecurityAction" /> values representing an action that can be performed using declarative security. </param>
    </member>
    <member name="P:System.Data.OracleClient.OraclePermissionAttribute.AllowBlankPassword">
      <summary>Gets or sets a value indicating whether a blank password is allowed.</summary>
      <returns>true if a blank password is allowed, otherwise false.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OraclePermissionAttribute.ConnectionString">
      <summary>Gets or sets a permitted connection string.</summary>
      <returns>A permitted connection string.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OraclePermissionAttribute.CreatePermission">
      <summary>Returns an <see cref="T:System.Data.OracleClient.OraclePermission" /> object that is configured according to the attribute properties.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OraclePermission" /> object.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OraclePermissionAttribute.KeyRestrictionBehavior">
      <summary>Identifies whether the list of connection string parameters identified by the <see cref="P:System.Data.OracleClient.OraclePermissionAttribute.KeyRestrictions" /> property are the only connection string parameters allowed.</summary>
      <returns>One of the <see cref="P:System.Data.OracleClient.OraclePermissionAttribute.KeyRestrictionBehavior" /> values.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OraclePermissionAttribute.KeyRestrictions">
      <summary>Gets or sets connection string parameters that are allowed or disallowed.</summary>
      <returns>One or more connection string parameters that are allowed or disallowed.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OraclePermissionAttribute.ShouldSerializeConnectionString">
      <summary>Identifies whether the attribute should serialize the connection string.</summary>
      <returns>true if the attribute should serialize the connection string; otherwise false.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OraclePermissionAttribute.ShouldSerializeKeyRestrictions">
      <summary>Identifies whether the attribute should serialize the set of key restrictions.</summary>
      <returns>true if the attribute should serialize the set of key restrictions, otherwise false.</returns>
    </member>
    <member name="T:System.Data.OracleClient.OracleRowUpdatedEventArgs">
      <summary>Provides data for the <see cref="E:System.Data.OracleClient.OracleDataAdapter.RowUpdated" /> event. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleRowUpdatedEventArgs.#ctor(System.Data.DataRow,System.Data.IDbCommand,System.Data.StatementType,System.Data.Common.DataTableMapping)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleRowUpdatedEventArgs" /> class.</summary>
      <param name="row">The <see cref="T:System.Data.DataRow" /> sent through an <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" />. </param>
      <param name="command">The <see cref="T:System.Data.IDbCommand" /> executed when <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> is called. </param>
      <param name="statementType">One of the <see cref="T:System.Data.StatementType" /> values that specifies the type of query executed. </param>
      <param name="tableMapping">The <see cref="T:System.Data.Common.DataTableMapping" /> sent through an <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" />. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleRowUpdatedEventArgs.Command">
      <summary>Gets or sets the <see cref="T:System.Data.OracleClient.OracleCommand" /> executed when <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> is called.</summary>
      <returns>The <see cref="T:System.Data.OracleClient.OracleCommand" /> executed when <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> is called.</returns>
    </member>
    <member name="T:System.Data.OracleClient.OracleRowUpdatedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.Data.OracleClient.OracleDataAdapter.RowUpdated" /> event of an <see cref="T:System.Data.OracleClient.OracleDataAdapter" />.</summary>
      <param name="sender">The source of the event. </param>
      <param name="e">The <see cref="T:System.Data.OracleClient.OracleRowUpdatedEventArgs" /> that contains the event data. </param>
    </member>
    <member name="T:System.Data.OracleClient.OracleRowUpdatingEventArgs">
      <summary>Provides data for the <see cref="E:System.Data.OracleClient.OracleDataAdapter.RowUpdating" /> event.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleRowUpdatingEventArgs.#ctor(System.Data.DataRow,System.Data.IDbCommand,System.Data.StatementType,System.Data.Common.DataTableMapping)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleRowUpdatingEventArgs" /> class.</summary>
      <param name="row">The <see cref="T:System.Data.DataRow" /> to update. </param>
      <param name="command">The <see cref="T:System.Data.IDbCommand" /> to execute during update. </param>
      <param name="statementType">One of the <see cref="T:System.Data.StatementType" /> values that specifies the type of query executed. </param>
      <param name="tableMapping">The <see cref="T:System.Data.Common.DataTableMapping" /> sent through an update. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleRowUpdatingEventArgs.Command">
      <summary>Gets or sets the <see cref="T:System.Data.OracleClient.OracleCommand" /> to execute when performing the <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" />.</summary>
      <returns>The <see cref="T:System.Data.OracleClient.OracleCommand" /> to execute when performing the <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" />.</returns>
    </member>
    <member name="T:System.Data.OracleClient.OracleRowUpdatingEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.Data.OracleClient.OracleDataAdapter.RowUpdating" /> event of an <see cref="T:System.Data.OracleClient.OracleDataAdapter" />.</summary>
      <param name="sender">The source of the event. </param>
      <param name="e">The <see cref="T:System.Data.OracleClient.OracleRowUpdatingEventArgs" /> that contains the event data. </param>
    </member>
    <member name="T:System.Data.OracleClient.OracleString">
      <summary>Represents a variable-length stream of characters to be stored in or retrieved from the database.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleString" /> class and specifies the string to store.</summary>
      <param name="s">The string to store. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.CompareTo(System.Object)">
      <summary>Compares this instance of <see cref="T:System.Data.OracleClient.OracleString" /> to the supplied object and returns an indication of their relative values.</summary>
      <returns>A signed number indicating the relative values of the instance and the object.Return Value Condition Less than zero This instance is less than object. Zero This instance is the same as object. Greater than zero This instance is greater than object -or- object is a null reference (Nothing) </returns>
      <param name="obj">The object to be compared. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.Concat(System.Data.OracleClient.OracleString,System.Data.OracleClient.OracleString)">
      <summary>Concatenates the two specified <see cref="T:System.Data.OracleClient.OracleString" /> structures.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleString" /> containing the newly concatenated value representing the contents of the two <see cref="T:System.Data.OracleClient.OracleString" /> parameters.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleString.Empty">
      <summary>Represents an empty string that can be assigned to the <see cref="P:System.Data.OracleClient.OracleString.Value" /> property of an instance of the <see cref="T:System.Data.OracleClient.OracleString" /> structure.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.Equals(System.Data.OracleClient.OracleString,System.Data.OracleClient.OracleString)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleString" /> operands to determine if they are equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are not equal. If either instance of <see cref="T:System.Data.OracleClient.OracleString" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.Equals(System.Object)">
      <summary>Compares the supplied object parameter to the <see cref="P:System.Data.OracleClient.OracleString.Value" /> property of the <see cref="T:System.Data.OracleClient.OracleString" /> object.</summary>
      <returns>Equals will return true if the object is an instance of <see cref="T:System.Data.OracleClient.OracleString" /> and the two are equal; otherwise false.</returns>
      <param name="value">The object to be compared. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.GetHashCode">
      <summary>Gets the hash code for this instance.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.GreaterThan(System.Data.OracleClient.OracleString,System.Data.OracleClient.OracleString)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleString" /> operands to determine if the first is greater than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleString" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.GreaterThanOrEqual(System.Data.OracleClient.OracleString,System.Data.OracleClient.OracleString)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleString" /> operands to determine if the first is greater than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleString" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleString.IsNull">
      <summary>Indicates whether the <see cref="P:System.Data.OracleClient.OracleString.Value" /> of the <see cref="T:System.Data.OracleClient.OracleString" /> is <see cref="F:System.Data.OracleClient.OracleString.Null" />.</summary>
      <returns>true if <see cref="P:System.Data.OracleClient.OracleString.Value" /> is <see cref="F:System.Data.OracleClient.OracleString.Null" />, otherwise false.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleString.Item(System.Int32)">
      <summary>Gets the single byte from the Value property located at the position indicated by the integer parameter, <paramref name="index" />.</summary>
      <returns>The byte located at the position indicated by the integer parameter.</returns>
      <param name="index">The position of the byte to be retrieved. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleString.Length">
      <summary>Gets the length of the string that is stored in this <see cref="T:System.Data.OracleClient.OracleString" /> structure.</summary>
      <returns>The length of the string that is stored.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.LessThan(System.Data.OracleClient.OracleString,System.Data.OracleClient.OracleString)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleString" /> operands to determine if the first is less than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleString" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.LessThanOrEqual(System.Data.OracleClient.OracleString,System.Data.OracleClient.OracleString)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleString" /> operands to determine if the first is less than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleString" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.NotEquals(System.Data.OracleClient.OracleString,System.Data.OracleClient.OracleString)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleString" /> operands to determine if they are not equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are not equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are equal. If either instance of <see cref="T:System.Data.OracleClient.OracleString" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleString.Null">
      <summary>Represents a null value that can be assigned to the <see cref="P:System.Data.OracleClient.OracleString.Value" /> property of an instance of the <see cref="T:System.Data.OracleClient.OracleString" /> structure.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.op_Addition(System.Data.OracleClient.OracleString,System.Data.OracleClient.OracleString)">
      <summary>Concatenates the two specified <see cref="T:System.Data.OracleClient.OracleString" /> structures.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleString" /> containing the newly concatenated value representing the contents of the two <see cref="T:System.Data.OracleClient.OracleString" /> parameters.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.op_Equality(System.Data.OracleClient.OracleString,System.Data.OracleClient.OracleString)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleString" /> operands to determine if they are equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are not equal. If either instance of <see cref="T:System.Data.OracleClient.OracleString" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.op_Explicit(System.Data.OracleClient.OracleString)~System.String">
      <summary>Converts an <see cref="T:System.Data.OracleClient.OracleString" /> to a <see cref="T:System.String" />.</summary>
      <returns>A String, whose contents are the same as the <see cref="P:System.Data.OracleClient.OracleString.Value" /> property of the OracleString parameter.</returns>
      <param name="x">The OracleString to be converted. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.op_GreaterThan(System.Data.OracleClient.OracleString,System.Data.OracleClient.OracleString)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleString" /> operands to determine if the first is greater than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleString" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.op_GreaterThanOrEqual(System.Data.OracleClient.OracleString,System.Data.OracleClient.OracleString)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleString" /> operands to determine if the first is greater than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleString" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.op_Implicit(System.String)~System.Data.OracleClient.OracleString">
      <summary>Converts the <see cref="T:System.String" /> parameter to an <see cref="T:System.Data.OracleClient.OracleString" />.</summary>
      <returns>An OracleString containing the value of the specified String.</returns>
      <param name="s">The <see cref="T:System.String" /> to be converted. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.op_Inequality(System.Data.OracleClient.OracleString,System.Data.OracleClient.OracleString)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleString" /> operands to determine if they are equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are not equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are equal. If either instance of <see cref="T:System.Data.OracleClient.OracleString" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.op_LessThan(System.Data.OracleClient.OracleString,System.Data.OracleClient.OracleString)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleString" /> operands to determine if the first is less than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleString" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.op_LessThanOrEqual(System.Data.OracleClient.OracleString,System.Data.OracleClient.OracleString)">
      <summary>Performs a logical comparison of the two <see cref="T:System.Data.OracleClient.OracleString" /> operands to determine if the first is less than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleString" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleString" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleString.ToString">
      <summary>Converts an OracleString object to a String.</summary>
      <returns>A String with the same value as this OracleString structure.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleString.Value">
      <summary>Gets the string that is stored in the <see cref="T:System.Data.OracleClient.OracleString" /> structure.</summary>
      <returns>The string that is stored.This property is read-only.</returns>
    </member>
    <member name="T:System.Data.OracleClient.OracleTimeSpan">
      <summary>Represents a time interval and corresponds to the Oracle 9i INTERVAL DAY TO SECOND data type.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.#ctor(System.Data.OracleClient.OracleTimeSpan)">
      <summary>Initializes a new <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure from an existing one.</summary>
      <param name="from">An existing <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure from which to create the new structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.#ctor(System.Int32,System.Int32,System.Int32)">
      <summary>Initializes a new <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure to a specified number of hours, minutes, and seconds.</summary>
      <param name="hours">Number of hours. </param>
      <param name="minutes">Number of minutes. </param>
      <param name="seconds">Number of seconds. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The parameters specify an <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> value less than <see cref="F:System.Data.OracleClient.OracleTimeSpan.MinValue" /> or greater than <see cref="F:System.Data.OracleClient.OracleTimeSpan.MaxValue" />. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Initializes a new <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure to a specified number of days, hours, minutes, and seconds.</summary>
      <param name="days">Number of days. </param>
      <param name="hours">Number of hours. </param>
      <param name="minutes">Number of minutes. </param>
      <param name="seconds">Number of seconds. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The parameters specify an <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> value less than <see cref="F:System.Data.OracleClient.OracleTimeSpan.MinValue" /> or greater than <see cref="F:System.Data.OracleClient.OracleTimeSpan.MaxValue" />. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Initializes a new <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure to a specified number of days, hours, minutes, seconds, and milliseconds.</summary>
      <param name="days">Number of days. </param>
      <param name="hours">Number of hours. </param>
      <param name="minutes">Number of minutes. </param>
      <param name="seconds">Number of seconds. </param>
      <param name="milliseconds">Number of milliseconds. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The parameters specify an <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> value less than <see cref="F:System.Data.OracleClient.OracleTimeSpan.MinValue" /> or greater than <see cref="F:System.Data.OracleClient.OracleTimeSpan.MaxValue" />. </exception>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.#ctor(System.Int64)">
      <summary>Initializes a new <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure to the specified number of ticks.</summary>
      <param name="ticks">A time period expressed in 100-nanosecond units. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.#ctor(System.TimeSpan)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure with the specified <see cref="T:System.TimeSpan" />.</summary>
      <param name="ts">The specified <see cref="T:System.TimeSpan" />. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.CompareTo(System.Object)">
      <summary>Compares this <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure to the supplied object and returns an indication of their relative values.</summary>
      <returns>A signed number indicating the relative values of the instance of the <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure and the object.Return Value Condition Less than zero This instance is less than the object. Zero This instance is the same as the object. Greater than zero This instance is greater than the object, or the object is a null reference (Nothing in Visual Basic). </returns>
      <param name="obj">The object to be compared. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleTimeSpan.Days">
      <summary>Gets the number of whole days represented by this instance.</summary>
      <returns>The day component of this instance.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.Equals(System.Data.OracleClient.OracleTimeSpan,System.Data.OracleClient.OracleTimeSpan)">
      <summary>Performs a logical comparison of two <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structures to determine whether they are equal.</summary>
      <returns>true if the two values are equal, otherwise false.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.Equals(System.Object)">
      <summary>Compares the supplied object parameter to the <see cref="P:System.Data.OracleClient.OracleTimeSpan.Value" /> property of the <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> object.</summary>
      <returns>true if the object is an instance of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> and the two are equal, otherwise false.</returns>
      <param name="value">The object to be compared. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.GetHashCode">
      <summary>Gets the hash code for this instance.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.GreaterThan(System.Data.OracleClient.OracleTimeSpan,System.Data.OracleClient.OracleTimeSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> to determine whether the first is greater than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.GreaterThanOrEqual(System.Data.OracleClient.OracleTimeSpan,System.Data.OracleClient.OracleTimeSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> to determine whether the first is greater than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> will be <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleTimeSpan.Hours">
      <summary>Gets the number of whole hours represented by this instance.</summary>
      <returns>The hour component of this instance between 0 and 23.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleTimeSpan.IsNull">
      <summary>Gets a value indicating whether the <see cref="P:System.Data.OracleClient.OracleTimeSpan.Value" /> property of the <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure is null.</summary>
      <returns>true if <see cref="P:System.Data.OracleClient.OracleTimeSpan.Value" /> is null, otherwise false.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.LessThan(System.Data.OracleClient.OracleTimeSpan,System.Data.OracleClient.OracleTimeSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> to determine whether the first is less than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.LessThanOrEqual(System.Data.OracleClient.OracleTimeSpan,System.Data.OracleClient.OracleTimeSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> to determine whether the first is less than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleTimeSpan.MaxValue">
      <summary>Represents the maximum valid date value for an <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure.</summary>
    </member>
    <member name="P:System.Data.OracleClient.OracleTimeSpan.Milliseconds">
      <summary>Gets the number of whole milliseconds represented by this instance.</summary>
      <returns>The millisecond component of this instance between 0 and 999.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleTimeSpan.Minutes">
      <summary>Gets the number of whole minutes represented by this instance.</summary>
      <returns>The minute component of this instance between 0 and 59.</returns>
    </member>
    <member name="F:System.Data.OracleClient.OracleTimeSpan.MinValue">
      <summary>Represents the minimum valid date value for an <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.NotEquals(System.Data.OracleClient.OracleTimeSpan,System.Data.OracleClient.OracleTimeSpan)">
      <summary>Performs a logical comparison of two instances of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> to determine whether they are not equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are not equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are equal. If either instance of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
    </member>
    <member name="F:System.Data.OracleClient.OracleTimeSpan.Null">
      <summary>Represents a null value that can be assigned to the <see cref="P:System.Data.OracleClient.OracleTimeSpan.Value" /> property of an instance of the <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.op_Equality(System.Data.OracleClient.OracleTimeSpan,System.Data.OracleClient.OracleTimeSpan)">
      <summary>Performs a logical comparison of two <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structures to determine whether they are equal.</summary>
      <returns>true if the two values are equal, otherwise false.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.op_Explicit(System.Data.OracleClient.OracleTimeSpan)~System.TimeSpan">
      <summary>Converts an <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure to a <see cref="T:System.TimeSpan" /> structure.</summary>
      <returns>A <see cref="T:System.TimeSpan" /> structure.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure to convert to a <see cref="T:System.TimeSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.op_Explicit(System.String)~System.Data.OracleClient.OracleTimeSpan">
      <summary>Converts a string to an <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure.</returns>
      <param name="x">A string to convert to an <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.op_GreaterThan(System.Data.OracleClient.OracleTimeSpan,System.Data.OracleClient.OracleTimeSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> to determine whether the first is greater than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.op_GreaterThanOrEqual(System.Data.OracleClient.OracleTimeSpan,System.Data.OracleClient.OracleTimeSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> to determine whether the first is greater than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is greater than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.op_Inequality(System.Data.OracleClient.OracleTimeSpan,System.Data.OracleClient.OracleTimeSpan)">
      <summary>Performs a logical comparison of two instances of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> to determine whether they are equal.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the two instances are not equal or <see cref="F:System.Data.OracleClient.OracleBoolean.False" /> if the two instances are equal. If either instance of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.op_LessThan(System.Data.OracleClient.OracleTimeSpan,System.Data.OracleClient.OracleTimeSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> to determine whether the first is less than the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.op_LessThanOrEqual(System.Data.OracleClient.OracleTimeSpan,System.Data.OracleClient.OracleTimeSpan)">
      <summary>Compares two instances of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> to determine whether the first is less than or equal to the second.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleBoolean" /> that is <see cref="F:System.Data.OracleClient.OracleBoolean.True" /> if the first instance is less than or equal to the second instance, otherwise <see cref="F:System.Data.OracleClient.OracleBoolean.False" />. If either instance of <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> is null, the <see cref="P:System.Data.OracleClient.OracleBoolean.Value" /> of the <see cref="T:System.Data.OracleClient.OracleBoolean" /> is <see cref="F:System.Data.OracleClient.OracleBoolean.Null" />.</returns>
      <param name="x">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
      <param name="y">An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure. </param>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.Parse(System.String)">
      <summary>Converts the specified <see cref="T:System.String" /> representation of a date and time to its <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> equivalent.</summary>
      <returns>An <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure equal to the date and time represented by the specified String.</returns>
      <param name="s">The String to be parsed. </param>
    </member>
    <member name="P:System.Data.OracleClient.OracleTimeSpan.Seconds">
      <summary>Gets the number of whole seconds represented by this instance.</summary>
      <returns>The second component of this instance between 0 and 59.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleTimeSpan.ToString">
      <summary>Converts this <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure to a <see cref="T:System.String" />.</summary>
      <returns>A String representing the <see cref="P:System.Data.OracleClient.OracleTimeSpan.Value" /> property of this <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleTimeSpan.Value">
      <summary>Gets the value of the <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure.</summary>
      <returns>The value of this <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> structure.</returns>
    </member>
    <member name="T:System.Data.OracleClient.OracleTransaction">
      <summary>Represents a transaction to be made in the database.</summary>
    </member>
    <member name="M:System.Data.OracleClient.OracleTransaction.Commit">
      <summary>Commits the SQL database transaction.</summary>
      <exception cref="T:System.Exception">An error occurred while trying to commit the transaction. </exception>
      <exception cref="T:System.InvalidOperationException">The transaction has already been committed or rolled back.-or- The connection is broken. </exception>
    </member>
    <member name="P:System.Data.OracleClient.OracleTransaction.Connection">
      <summary>Specifies the <see cref="T:System.Data.OracleClient.OracleConnection" /> object associated with the transaction.</summary>
      <returns>The <see cref="T:System.Data.OracleClient.OracleConnection" /> object associated with the transaction.</returns>
    </member>
    <member name="P:System.Data.OracleClient.OracleTransaction.IsolationLevel">
      <summary>Specifies the <see cref="T:System.Data.IsolationLevel" /> for this transaction.</summary>
      <returns>The <see cref="T:System.Data.IsolationLevel" /> for this transaction. The default is ReadCommitted.</returns>
    </member>
    <member name="M:System.Data.OracleClient.OracleTransaction.Rollback">
      <summary>Rolls back a transaction from a pending state.</summary>
      <exception cref="T:System.Exception">An error occurred while trying to commit the transaction. </exception>
      <exception cref="T:System.InvalidOperationException">The transaction has already been committed or rolled back.-or- The connection is broken. </exception>
    </member>
    <member name="T:System.Data.OracleClient.OracleType">
      <summary>Specifies the data type of a field or property for use in an <see cref="T:System.Data.OracleClient.OracleParameter" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.BFile">
      <summary>An Oracle BFILE data type that contains a reference to binary data with a maximum size of 4 gigabytes that is stored in an external file. Use the OracleClient <see cref="T:System.Data.OracleClient.OracleBFile" /> data type with the <see cref="P:System.Data.OracleClient.OracleParameter.Value" /> property.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.Blob">
      <summary>An Oracle BLOB data type that contains binary data with a maximum size of 4 gigabytes. Use the OracleClient <see cref="T:System.Data.OracleClient.OracleLob" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.Char">
      <summary>An Oracle CHAR data type that contains a fixed-length character string with a maximum size of 2,000 bytes. Use the .NET Framework <see cref="T:System.String" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleString" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.Clob">
      <summary>An Oracle CLOB data type that contains character data, based on the default character set on the server, with a maximum size of 4 gigabytes. Use the OracleClient <see cref="T:System.Data.OracleClient.OracleLob" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.Cursor">
      <summary>An Oracle REF CURSOR. The <see cref="T:System.Data.OracleClient.OracleDataReader" /> object is not available.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.DateTime">
      <summary>An Oracle DATE data type that contains a fixed-length representation of a date and time, ranging from January 1, 4712 B.C. to December 31, A.D. 4712, with the default format dd-mmm-yy. For A.D. dates, DATE maps to <see cref="T:System.DateTime" />. To bind B.C. dates, use a String parameter and the Oracle TO_DATE or TO_CHAR conversion functions for input and output parameters respectively. Use the .NET Framework <see cref="T:System.DateTime" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleDateTime" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.IntervalDayToSecond">
      <summary>An Oracle INTERVAL DAY TO SECOND data type (Oracle 9i or later) that contains an interval of time in days, hours, minutes, and seconds, and has a fixed size of 11 bytes. Use the .NET Framework <see cref="T:System.TimeSpan" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleTimeSpan" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.IntervalYearToMonth">
      <summary>An Oracle INTERVAL YEAR TO MONTH data type (Oracle 9i or later) that contains an interval of time in years and months, and has a fixed size of 5 bytes. Use the .NET Framework <see cref="T:System.Int32" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleMonthSpan" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.LongRaw">
      <summary>An Oracle LONGRAW data type that contains variable-length binary data with a maximum size of 2 gigabytes. Use the .NET Framework Byte[] or OracleClient <see cref="T:System.Data.OracleClient.OracleBinary" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.LongVarChar">
      <summary>An Oracle LONG data type that contains a variable-length character string with a maximum size of 2 gigabytes. Use the .NET Framework <see cref="T:System.String" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleString" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.NChar">
      <summary>An Oracle NCHAR data type that contains fixed-length character string to be stored in the national character set of the database, with a maximum size of 2,000 bytes (not characters) when stored in the database. The size of the value depends on the national character set of the database. See your Oracle documentation for more information. Use the .NET Framework <see cref="T:System.String" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleString" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.NClob">
      <summary>An Oracle NCLOB data type that contains character data to be stored in the national character set of the database, with a maximum size of 4 gigabytes (not characters) when stored in the database. The size of the value depends on the national character set of the database. See your Oracle documentation for more information. Use the .NET Framework <see cref="T:System.String" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleString" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.Number">
      <summary>An Oracle NUMBER data type that contains variable-length numeric data with a maximum precision and scale of 38. This maps to <see cref="T:System.Decimal" />. To bind an Oracle NUMBER that exceeds what <see cref="F:System.Decimal.MaxValue" /> can contain, either use an <see cref="T:System.Data.OracleClient.OracleNumber" /> data type, or use a String parameter and the Oracle TO_NUMBER or TO_CHAR conversion functions for input and output parameters respectively. Use the .NET Framework <see cref="T:System.Decimal" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleNumber" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.NVarChar">
      <summary>An Oracle NVARCHAR2 data type that contains a variable-length character string stored in the national character set of the database, with a maximum size of 4,000 bytes (not characters) when stored in the database. The size of the value depends on the national character set of the database. See your Oracle documentation for more information. Use the .NET Framework <see cref="T:System.String" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleString" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.Raw">
      <summary>An Oracle RAW data type that contains variable-length binary data with a maximum size of 2,000 bytes. Use the .NET Framework Byte[] or OracleClient <see cref="T:System.Data.OracleClient.OracleBinary" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.RowId">
      <summary>The base64 string representation of an Oracle ROWID data type. Use the .NET Framework <see cref="T:System.String" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleString" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.Timestamp">
      <summary>An Oracle TIMESTAMP (Oracle 9i or later) that contains date and time (including seconds), and ranges in size from 7 to 11 bytes. Use the .NET Framework <see cref="T:System.DateTime" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleDateTime" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.TimestampLocal">
      <summary>An Oracle TIMESTAMP WITH LOCAL TIMEZONE (Oracle 9i or later) that contains date, time, and a reference to the original time zone, and ranges in size from 7 to 11 bytes. Use the .NET Framework <see cref="T:System.DateTime" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleDateTime" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.TimestampWithTZ">
      <summary>An Oracle TIMESTAMP WITH TIMEZONE (Oracle 9i or later) that contains date, time, and a specified time zone, and has a fixed size of 13 bytes. Use the .NET Framework <see cref="T:System.DateTime" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleDateTime" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.VarChar">
      <summary>An Oracle VARCHAR2 data type that contains a variable-length character string with a maximum size of 4,000 bytes. Use the .NET Framework <see cref="T:System.String" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleString" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.Byte">
      <summary>An integral type representing unsigned 8-bit integers with values between 0 and 255. This is not a native Oracle data type, but is provided to improve performance when binding input parameters. Use the .NET Framework <see cref="T:System.Byte" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.UInt16">
      <summary>An integral type representing unsigned 16-bit integers with values between 0 and 65535. This is not a native Oracle data type, but is provided to improve performance when binding input parameters. For information about conversion of Oracle numeric values to common language runtime (CLR) data types, see <see cref="T:System.Data.OracleClient.OracleNumber" />. Use the .NET Framework <see cref="T:System.UInt16" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleNumber" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.UInt32">
      <summary>An integral type representing unsigned 32-bit integers with values between 0 and 4294967295. This is not a native Oracle data type, but is provided to improve performance when binding input parameters. For information about conversion of Oracle numeric values to common language runtime (CLR) data types, see <see cref="T:System.Data.OracleClient.OracleNumber" />. Use the .NET Framework <see cref="T:System.UInt32" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleNumber" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.SByte">
      <summary>An integral type representing signed 8 bit integers with values between -128 and 127. This is not a native Oracle data type, but is provided to improve performance when binding input parameters. Use the .NET Framework <see cref="T:System.SByte" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.Int16">
      <summary>An integral type representing signed 16-bit integers with values between -32768 and 32767. This is not a native Oracle data type, but is provided to improve performance when binding input parameters. For information about conversion of Oracle numeric values to common language runtime (CLR) data types, see <see cref="T:System.Data.OracleClient.OracleNumber" />. Use the .NET Framework <see cref="T:System.Int16" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleNumber" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.Int32">
      <summary>An integral type representing signed 32-bit integers with values between -2147483648 and 2147483647. This is not a native Oracle data type, but is provided for performance when binding input parameters. For information about conversion of Oracle numeric values to common language runtime data types, see <see cref="T:System.Data.OracleClient.OracleNumber" />. Use the .NET Framework <see cref="T:System.Int32" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleNumber" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.Float">
      <summary>A single-precision floating-point value. This is not a native Oracle data type, but is provided to improve performance when binding input parameters. For information about conversion of Oracle numeric values to common language runtime data types, see <see cref="T:System.Data.OracleClient.OracleNumber" />. Use the .NET Framework <see cref="T:System.Single" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleNumber" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
    <member name="F:System.Data.OracleClient.OracleType.Double">
      <summary>A double-precision floating-point value. This is not a native Oracle data type, but is provided to improve performance when binding input parameters. For information about conversion of Oracle numeric values to common language runtime (CLR) data types, see <see cref="T:System.Data.OracleClient.OracleNumber" />. Use the .NET Framework <see cref="T:System.Double" /> or OracleClient <see cref="T:System.Data.OracleClient.OracleNumber" /> data type in <see cref="P:System.Data.OracleClient.OracleParameter.Value" />.</summary>
    </member>
  </members>
</doc>