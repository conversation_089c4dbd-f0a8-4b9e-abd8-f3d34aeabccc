﻿using Microsoft.Win32;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace RegSysMenu
{
    class Program
    {
        static void Main(string[] args)
        {
            if (args.Length > 0)
            {
                bool isReg = true;
                if (args.Length > 1)
                {
                    isReg = args[1].ToLower().Trim().Equals("true");
                }

                try
                {
                    RegFileType(args[0], isReg);
                }
                catch { }
            }
            //退出
            System.Windows.Forms.Application.Exit();
        }

        private static readonly string ShellExtOcrMMenuFiles = @"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\CommandStore\shell\{0}";

        public static void RegFileType(string exePath, bool isReg)
        {
            if (string.IsNullOrEmpty(exePath))
            {
                return;
            }

            var shellExtPath = $"\"{exePath}\" \"%1 ";
            var sbCommand = new StringBuilder();
            var dicOcrType = new Dictionary<int, string>
            {
                {0,"文本"},
                {1,"竖排"},
                {2,"表格"},
                {3,"公式"},
                {4,"翻译"}
            };
            foreach (var ocr in dicOcrType)
            {
                var strKeyName = "识别为" + ocr.Value;
                var subKeyLoc = string.Format(ShellExtOcrMMenuFiles, strKeyName);
                if (isReg)
                {
                    var key = Registry.LocalMachine.OpenSubKey(subKeyLoc, true) ?? Registry.LocalMachine.CreateSubKey(subKeyLoc);
                    using (key)
                    {
                        key.SetValue("Icon", exePath);
                        key.SetValue("MUIVerb", strKeyName);
                        using (var commandKey = key.CreateSubKey("Command"))
                        {
                            commandKey.SetValue("", shellExtPath + ocr.Key + "\"");
                            commandKey.Close();
                        }
                        key.Close();
                    }

                    sbCommand.Append(strKeyName + ";");
                }
                else
                {
                    try
                    {
                        Registry.LocalMachine.DeleteSubKeyTree(subKeyLoc, false);
                    }
                    catch { }
                }
            }

            try
            {
                Registry.LocalMachine.DeleteSubKeyTree(string.Format(ShellExtOcrMMenuFiles, "识别为图片"), false);
            }
            catch { }

            if (isReg && sbCommand.Length <= 0)
            {
                return;
            }

            var lstImgExt = new List<string> { "png", "jpg", "jpeg", "bmp", "gif" };
            var productName = Path.GetFileNameWithoutExtension(exePath);
            var strViewKeyName = "预览图片";
            var strCompressKeyName = "压缩图片";
            var strPasteKeyName = "钉在桌面";
            foreach (var ext in lstImgExt)
            {
                var subKeyLoc = $"SystemFileAssociations\\.{ext}\\shell";
                if (isReg)
                {
                    var key = Registry.ClassesRoot.OpenSubKey(subKeyLoc, true) ??
                              Registry.ClassesRoot.CreateSubKey(subKeyLoc);
                    using (key)
                    {
                        //图片预览
                        using (var mainKey = key.CreateSubKey(strViewKeyName))
                        {
                            mainKey.SetValue("Icon", exePath);
                            mainKey.SetValue("MUIVerb", strViewKeyName);
                            mainKey.SetValue("Position", "1");
                            using (var commandKey = mainKey.CreateSubKey("Command"))
                            {
                                commandKey.SetValue("", shellExtPath + "10\"");
                                commandKey.Close();
                            }

                            mainKey.Close();
                        }

                        //钉在桌面
                        using (var mainKey = key.CreateSubKey(strPasteKeyName))
                        {
                            mainKey.SetValue("Icon", exePath);
                            mainKey.SetValue("MUIVerb", strPasteKeyName);
                            mainKey.SetValue("Position", "1");
                            using (var commandKey = mainKey.CreateSubKey("Command"))
                            {
                                commandKey.SetValue("", shellExtPath + "11\"");
                                commandKey.Close();
                            }

                            mainKey.Close();
                        }

                        //压缩图片
                        using (var mainKey = key.CreateSubKey(strCompressKeyName))
                        {
                            mainKey.SetValue("Icon", exePath);
                            mainKey.SetValue("MUIVerb", strCompressKeyName);
                            mainKey.SetValue("Position", "1");
                            using (var commandKey = mainKey.CreateSubKey("Command"))
                            {
                                commandKey.SetValue("", shellExtPath + "12\"");
                                commandKey.Close();
                            }

                            mainKey.Close();
                        }

                        //图片识别
                        using (var mainKey = key.CreateSubKey(productName))
                        {
                            mainKey.SetValue("Icon", exePath);
                            mainKey.SetValue("MUIVerb", productName + "(&O)");
                            mainKey.SetValue("Position", "1");
                            mainKey.SetValue("SubCommands", sbCommand.ToString());

                            mainKey.Close();
                        }

                        key.Close();
                    }
                }
                else
                {
                    try
                    {
                        Registry.ClassesRoot.DeleteSubKeyTree(subKeyLoc, true);
                    }
                    catch { }
                }

                try
                {
                    Registry.ClassesRoot.DeleteSubKeyTree(subKeyLoc + "\\Program", false);
                }
                catch { }
            }

        }
    }
}
