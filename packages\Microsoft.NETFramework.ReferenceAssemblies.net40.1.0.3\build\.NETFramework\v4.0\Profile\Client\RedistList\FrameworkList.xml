<?xml version="1.0" encoding="utf-8"?>
<FileList  Redist="Microsoft-Windows-CLRCoreComp.4.0" Name=".NET Framework 4 Client Profile" RuntimeVersion="4.0" ToolsVersion="4.0">
  <File AssemblyName="Accessibility" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="CustomMarshalers" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="Microsoft.CSharp" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="Microsoft.JScript" Version="10.0.0.0" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="Microsoft.VisualBasic" Version="10.0.0.0" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="Microsoft.VisualBasic.Compatibility" Version="10.0.0.0" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" /> 
  <File AssemblyName="Microsoft.VisualBasic.Compatibility.Data" Version="10.0.0.0" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" /> 
  <File AssemblyName="Microsoft.VisualC" Version="10.0.0.0" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="mscorlib" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="PresentationCore" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="PresentationFramework.Aero" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="PresentationFramework.Classic" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="PresentationFramework" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="PresentationFramework.Luna" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="PresentationFramework.Royale" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="ReachFramework" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="sysglobl" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Activities" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" /> 
  <File AssemblyName="System.Activities.Core.Presentation" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Activities.DurableInstancing" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" /> 
  <File AssemblyName="System.Activities.Presentation" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.AddIn.Contract" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.AddIn" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.ComponentModel.Composition" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.ComponentModel.DataAnnotations" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Configuration" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Configuration.Install" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Core" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Data.DataSetExtensions" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Data" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Data.Entity" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Data.Linq" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" /> 
  <File AssemblyName="System.Data.Services.Client" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Data.SqlXml" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Deployment" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Device" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.DirectoryServices" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.DirectoryServices.Protocols" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.DirectoryServices.AccountManagement" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Drawing" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Dynamic" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.EnterpriseServices" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.IdentityModel" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" /> 
  <File AssemblyName="System.IdentityModel.Selectors" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" /> 
  <File AssemblyName="System.IO.Log" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Management" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Management.Instrumentation" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" /> 
  <File AssemblyName="System.Messaging" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Net" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Numerics" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Printing" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Runtime.DurableInstancing" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Runtime.Remoting" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Runtime.Serialization" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Runtime.Serialization.Formatters.Soap" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Security" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.ServiceModel" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" /> 
  <File AssemblyName="System.ServiceModel.Activities" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" /> 
  <File AssemblyName="System.ServiceModel.Channels" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.ServiceModel.Discovery" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" /> 
  <File AssemblyName="System.ServiceModel.Routing" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />  
  <File AssemblyName="System.ServiceProcess" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Speech" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" /> 
  <File AssemblyName="System.Transactions" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Web.ApplicationServices" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Web.Services" Version="*******" PublicKeyToken="b03f5f7f11d50a3a" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Windows.Forms.DataVisualization" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Windows.Forms" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Windows.Input.Manipulations" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Windows.Presentation" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Xaml" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Xml" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="System.Xml.Linq" Version="*******" PublicKeyToken="b77a5c561934e089" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="UIAutomationClient" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="UIAutomationClientsideProviders" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="UIAutomationProvider" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="UIAutomationTypes" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="WindowsBase" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
  <File AssemblyName="WindowsFormsIntegration" Version="*******" PublicKeyToken="31bf3856ad364e35" Culture="neutral" ProcessorArchitecture="MSIL" InGac="true" />
</FileList>
