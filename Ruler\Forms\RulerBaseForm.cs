﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    [DesignerCategory("")]
    public class RulerBaseForm : BaseForm
    {
        private const int MINIMAL_LENGTH = 350;

        public RulerBaseForm()
        {
            MouseDown += Form_MouseDown;
            MouseMove += Form_MouseMove;
            MouseUp += Form_MouseUp;
        }

        #region Sizing Restriction

        private int _restrictSize;
        private RulerFormResizeMode _resizeMode;
        private int _cachedWidth, _cachedHeight;

        public int RestrictSize
        {
            get => _restrictSize;
            set
            {
                _restrictSize = value;
                MinimumSize = new Size(value, value);
            }
        }

        public RulerFormResizeMode ResizeMode
        {
            get => _resizeMode;
            set => ApplyResizeMode(value);
        }

        private void ApplyResizeMode(RulerFormResizeMode resizeMode)
        {
            _resizeMode = resizeMode;
            var wRest = MaximumSize.Width < int.MaxValue && !MaximumSize.IsEmpty;
            var hRest = MaximumSize.Height < int.MaxValue && !MaximumSize.IsEmpty;
            if (!wRest) _cachedWidth = Math.Max(MINIMAL_LENGTH, Width);
            if (!hRest) _cachedHeight = Math.Max(MINIMAL_LENGTH, Height);
            switch (resizeMode)
            {
                case RulerFormResizeMode.Horizontal:
                    MaximumSize = new Size(int.MaxValue, RestrictSize);
                    if (wRest) Width = _cachedWidth;
                    break;
                case RulerFormResizeMode.Vertical:
                    MaximumSize = new Size(RestrictSize, int.MaxValue);
                    if (hRest) Height = _cachedHeight;
                    break;
                case RulerFormResizeMode.TwoDimensional:
                    MaximumSize = Size.Empty;
                    if (wRest) Width = _cachedWidth;
                    if (hRest) Height = _cachedHeight;
                    break;
            }

            CheckOutOfBounds();
            Invalidate();
        }

        protected void CheckOutOfBounds()
        {
            var screenRect = Screen.FromRectangle(Bounds).WorkingArea;
            // If the ruler got out of the visible area, move it back in
            if (!screenRect.IntersectsWith(Bounds))
            {
                var newLocation = Location;
                if (Location.X < screenRect.X)
                    newLocation.X = screenRect.X;
                else if (Location.X > screenRect.Right)
                    newLocation.X = screenRect.Right - Width;
                if (Location.Y < screenRect.Y)
                    newLocation.Y = screenRect.Y;
                else if (Location.Y >= screenRect.Bottom)
                    newLocation.Y = screenRect.Bottom - Height;
                Location = newLocation;
            }
        }

        #endregion

        // Handles dragging of the form

        #region Form Dragging

        private bool _mouseDown;
        private Point _mouseLoc;

        private void Form_MouseDown(object sender, MouseEventArgs e)
        {
            _mouseDown = true;
            _mouseLoc = e.Location;
        }

        private void Form_MouseMove(object sender, MouseEventArgs e)
        {
            if (_mouseDown) Location = new Point(Location.X - _mouseLoc.X + e.X, Location.Y - _mouseLoc.Y + e.Y);
        }

        private void Form_MouseUp(object sender, MouseEventArgs e)
        {
            _mouseDown = false;
        }

        #endregion
    }
}