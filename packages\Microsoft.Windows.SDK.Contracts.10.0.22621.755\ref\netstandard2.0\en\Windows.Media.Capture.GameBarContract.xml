﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Windows.Media.Capture.GameBarContract</name>
  </assembly>
  <members>
    <member name="T:Windows.Media.Capture.GameBarCommand">
      <summary>Specifies the set of Game Bar commands.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommand.OpenGameBar">
      <summary>Open the Game Bar.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommand.PauseBroadcast">
      <summary>Pause broadcasting.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommand.RecordHistoricalBuffer">
      <summary>Record the historical buffer.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommand.ResumeBroadcast">
      <summary>Resume broadcasting.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommand.StartBroadcast">
      <summary>Start broadcasting.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommand.StartRecord">
      <summary>Start recording.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommand.StopBroadcast">
      <summary>Stop broadcasting.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommand.StopRecord">
      <summary>Stop recording.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommand.TakeScreenshot">
      <summary>Take a screenshot.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommand.ToggleCameraCapture">
      <summary>Toggle camera capture.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommand.ToggleMicrophoneCapture">
      <summary>Toggle microphone capture.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommand.ToggleRecordingIndicator">
      <summary>Toggle the recording indicator.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommand.ToggleStartStopBroadcast">
      <summary>Toggle start/stop broadcast.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommand.ToggleStartStopRecord">
      <summary>Toggle start/stop record.</summary>
    </member>
    <member name="T:Windows.Media.Capture.GameBarCommandOrigin">
      <summary>Specifies the origin of a Game Bar command.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommandOrigin.AppCommand">
      <summary>The Game Bar command originated from an app-initiated command.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommandOrigin.Cortana">
      <summary>The Game Bar command originated from Cortana.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarCommandOrigin.ShortcutKey">
      <summary>The Game Bar command originated from a shortcut key.</summary>
    </member>
    <member name="T:Windows.Media.Capture.GameBarContract">
      <summary>
      </summary>
    </member>
    <member name="T:Windows.Media.Capture.GameBarServices">
      <summary>Manages the state of the Game Bar.</summary>
    </member>
    <member name="P:Windows.Media.Capture.GameBarServices.AppBroadcastServices">
      <summary>Gets an object that manages app broadcasts.</summary>
      <returns>An object that manages app broadcasts.</returns>
    </member>
    <member name="P:Windows.Media.Capture.GameBarServices.AppCaptureServices">
      <summary>Gets an object that manages app capture.</summary>
      <returns>An object that manages app capture.</returns>
    </member>
    <member name="P:Windows.Media.Capture.GameBarServices.SessionId">
      <summary>Gets a unique identifier for the Game Bar session.</summary>
      <returns>A unique identifier for the Game Bar session.</returns>
    </member>
    <member name="P:Windows.Media.Capture.GameBarServices.TargetCapturePolicy">
      <summary>Gets the policy for app capture of a target.</summary>
      <returns>The policy for app capture of a target.</returns>
    </member>
    <member name="P:Windows.Media.Capture.GameBarServices.TargetInfo">
      <summary>Gets an object that provides metadata for an app capture target.</summary>
      <returns>An object that provides metadata for an app capture target.</returns>
    </member>
    <member name="E:Windows.Media.Capture.GameBarServices.CommandReceived">
      <summary>Occurs when a Game Bar command is received.</summary>
    </member>
    <member name="M:Windows.Media.Capture.GameBarServices.DisableCapture">
      <summary>Disables app capture.</summary>
    </member>
    <member name="M:Windows.Media.Capture.GameBarServices.EnableCapture">
      <summary>Enables app capture.</summary>
    </member>
    <member name="T:Windows.Media.Capture.GameBarServicesCommandEventArgs">
      <summary>Provides data for the GameBarServices.CommandReceived event.</summary>
    </member>
    <member name="P:Windows.Media.Capture.GameBarServicesCommandEventArgs.Command">
      <summary>Gets a value specifying the Game Bar command associated with the event.</summary>
      <returns>A value specifying the Game Bar command associated with the event.</returns>
    </member>
    <member name="P:Windows.Media.Capture.GameBarServicesCommandEventArgs.Origin">
      <summary>Gets a value specifying the origin of the Game Bar command.</summary>
      <returns>A value specifying the origin of the Game Bar command.</returns>
    </member>
    <member name="T:Windows.Media.Capture.GameBarServicesManager">
      <summary>Manages the Game Bar services.</summary>
    </member>
    <member name="E:Windows.Media.Capture.GameBarServicesManager.GameBarServicesCreated">
      <summary>Occurs when the  GameBarServices object is created.</summary>
    </member>
    <member name="M:Windows.Media.Capture.GameBarServicesManager.GetDefault">
      <summary>Gets the default instance of the GameBarServiceManager class.</summary>
      <returns>The default instance of the GameBarServiceManager class.</returns>
    </member>
    <member name="T:Windows.Media.Capture.GameBarServicesManagerGameBarServicesCreatedEventArgs">
      <summary>Provides data for the GameBarServicesManager.GameBarServicesCreated event.</summary>
    </member>
    <member name="P:Windows.Media.Capture.GameBarServicesManagerGameBarServicesCreatedEventArgs.GameBarServices">
      <summary>Gets the GameBarServices associated with the event.</summary>
      <returns>The GameBarServices associated with the event.</returns>
    </member>
    <member name="T:Windows.Media.Capture.GameBarServicesTargetInfo">
      <summary>Provides metadata about a Game Bar services target.</summary>
    </member>
    <member name="P:Windows.Media.Capture.GameBarServicesTargetInfo.AppId">
      <summary>Gets the unique identifier of the Game Bar services target app.</summary>
      <returns>The unique identifier of the Game Bar services target app.</returns>
    </member>
    <member name="P:Windows.Media.Capture.GameBarServicesTargetInfo.DisplayMode">
      <summary>Gets a value indicating the display mode of the Game Bar services target app.</summary>
      <returns>A value indicating the display mode of the Game Bar services target app.</returns>
    </member>
    <member name="P:Windows.Media.Capture.GameBarServicesTargetInfo.DisplayName">
      <summary>Gets the display name of the Game Bar services target app.</summary>
      <returns>The display name of the Game Bar services target app.</returns>
    </member>
    <member name="P:Windows.Media.Capture.GameBarServicesTargetInfo.TitleId">
      <summary>Gets the unique identifier of the Game Bar services target title.</summary>
      <returns>The unique identifier of the Game Bar services target title.</returns>
    </member>
    <member name="T:Windows.Media.Capture.GameBarTargetCapturePolicy">
      <summary>Provides information around the allowable actions for this application.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarTargetCapturePolicy.EnabledBySystem">
      <summary>The OS determined that the target allows capture</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarTargetCapturePolicy.EnabledByUser">
      <summary>The OS does not know the policy for the app, but the user did identify the target as capturable.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarTargetCapturePolicy.NotEnabled">
      <summary>The OS doesn’t know the policy for the app and hasn’t been identified by the user as capturable.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarTargetCapturePolicy.ProhibitedByPublisher">
      <summary>The publisher of the target has indicated that capture is not allowed.</summary>
    </member>
    <member name="F:Windows.Media.Capture.GameBarTargetCapturePolicy.ProhibitedBySystem">
      <summary>The OS determined that the target is prohibited from being captured.</summary>
    </member>
  </members>
</doc>