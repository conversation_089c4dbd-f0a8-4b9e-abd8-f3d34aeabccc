﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Configuration</name>
  </assembly>
  <members>
    <member name="T:System.Configuration.AppSettingsSection">
      <summary>Provides configuration system support for the appSettings configuration section. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.AppSettingsSection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.AppSettingsSection" /> class.</summary>
    </member>
    <member name="P:System.Configuration.AppSettingsSection.File">
      <summary>Gets or sets a configuration file that provides additional settings or overrides the settings specified in the appSettings element.</summary>
      <returns>A configuration file that provides additional settings or overrides the settings specified in the appSettings element.</returns>
    </member>
    <member name="P:System.Configuration.AppSettingsSection.Settings">
      <summary>Gets a collection of key/value pairs that contains application settings.</summary>
      <returns>A collection of key/value pairs that contains the application settings from the configuration file.</returns>
    </member>
    <member name="T:System.Configuration.CallbackValidator">
      <summary>Provides dynamic validation of an object.</summary>
    </member>
    <member name="M:System.Configuration.CallbackValidator.#ctor(System.Type,System.Configuration.ValidatorCallback)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.CallbackValidator" /> class.</summary>
      <param name="type">The type of object that will be validated.</param>
      <param name="callback">The <see cref="T:System.Configuration.ValidatorCallback" /> used as the delegate.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type " />is null.</exception>
    </member>
    <member name="M:System.Configuration.CallbackValidator.CanValidate(System.Type)">
      <summary>Determines whether the type of the object can be validated.</summary>
      <returns>true if the type parameter matches the type used as the first parameter when creating an instance of <see cref="T:System.Configuration.CallbackValidator" />; otherwise, false. </returns>
      <param name="type">The type of object.</param>
    </member>
    <member name="M:System.Configuration.CallbackValidator.Validate(System.Object)">
      <summary>Determines whether the value of an object is valid.</summary>
      <param name="value">The value of an object.</param>
    </member>
    <member name="T:System.Configuration.CallbackValidatorAttribute">
      <summary>Specifies a <see cref="T:System.Configuration.CallbackValidator" /> object to use for code validation. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.CallbackValidatorAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.CallbackValidatorAttribute" /> class. </summary>
    </member>
    <member name="P:System.Configuration.CallbackValidatorAttribute.CallbackMethodName">
      <summary>Gets or sets the name of the callback method.</summary>
      <returns>The name of the method to call.</returns>
    </member>
    <member name="P:System.Configuration.CallbackValidatorAttribute.Type">
      <summary>Gets or sets the type of the validator.</summary>
      <returns>The <see cref="T:System.Type" /> of the current validator attribute instance.</returns>
    </member>
    <member name="P:System.Configuration.CallbackValidatorAttribute.ValidatorInstance">
      <summary>Gets the validator instance.</summary>
      <returns>The current <see cref="T:System.Configuration.ConfigurationValidatorBase" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">The value of the <see cref="P:System.Configuration.CallbackValidatorAttribute.Type" /> property is null.</exception>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Configuration.CallbackValidatorAttribute.CallbackMethodName" /> property is not set to a public static void method with one object parameter.</exception>
    </member>
    <member name="T:System.Configuration.CommaDelimitedStringCollection">
      <summary>Represents a collection of string elements separated by commas. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.CommaDelimitedStringCollection.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Configuration.CommaDelimitedStringCollection" /> class.</summary>
    </member>
    <member name="M:System.Configuration.CommaDelimitedStringCollection.Add(System.String)">
      <summary>Adds a string to the comma-delimited collection.</summary>
      <param name="value">A string value.</param>
    </member>
    <member name="M:System.Configuration.CommaDelimitedStringCollection.AddRange(System.String[])">
      <summary>Adds all the strings in a string array to the collection.</summary>
      <param name="range">An array of strings to add to the collection.</param>
    </member>
    <member name="M:System.Configuration.CommaDelimitedStringCollection.Clear">
      <summary>Clears the collection.</summary>
    </member>
    <member name="M:System.Configuration.CommaDelimitedStringCollection.Clone">
      <summary>Creates a copy of the collection.</summary>
      <returns>A <see cref="T:System.Configuration.CommaDelimitedStringCollection" />.</returns>
    </member>
    <member name="M:System.Configuration.CommaDelimitedStringCollection.Insert(System.Int32,System.String)">
      <summary>Adds a string element to the collection at the specified index.</summary>
      <param name="index">The index in the collection at which the new element will be added.</param>
      <param name="value">The value of the new element to add to the collection.</param>
    </member>
    <member name="P:System.Configuration.CommaDelimitedStringCollection.IsModified">
      <summary>Gets a value that specifies whether the collection has been modified. </summary>
      <returns>true if the <see cref="T:System.Configuration.CommaDelimitedStringCollection" /> has been modified; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.CommaDelimitedStringCollection.IsReadOnly">
      <summary>Gets a value indicating whether the collection object is read-only.</summary>
      <returns>true if the specified string element in the <see cref="T:System.Configuration.CommaDelimitedStringCollection" /> is read-only; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.CommaDelimitedStringCollection.Item(System.Int32)">
      <summary>Gets or sets a string element in the collection based on the index.</summary>
      <returns>A string element in the collection.</returns>
      <param name="index">The index of the string element in the collection.</param>
    </member>
    <member name="M:System.Configuration.CommaDelimitedStringCollection.Remove(System.String)">
      <summary>Removes a string element from the collection.</summary>
      <param name="value">The string to remove.</param>
    </member>
    <member name="M:System.Configuration.CommaDelimitedStringCollection.SetReadOnly">
      <summary>Sets the collection object to read-only.</summary>
    </member>
    <member name="M:System.Configuration.CommaDelimitedStringCollection.ToString">
      <summary>Returns a string representation of the object.</summary>
      <returns>A string representation of the object.</returns>
    </member>
    <member name="T:System.Configuration.CommaDelimitedStringCollectionConverter">
      <summary>Converts a comma-delimited string value to and from a <see cref="T:System.Configuration.CommaDelimitedStringCollection" /> object. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.CommaDelimitedStringCollectionConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.CommaDelimitedStringCollectionConverter" /> class. </summary>
    </member>
    <member name="M:System.Configuration.CommaDelimitedStringCollectionConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts a <see cref="T:System.String" /> object to a <see cref="T:System.Configuration.CommaDelimitedStringCollection" /> object.</summary>
      <returns>A <see cref="T:System.Configuration.CommaDelimitedStringCollection" /> containing the converted value.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> used during conversion.</param>
      <param name="data">The comma-separated <see cref="T:System.String" /> to convert.</param>
    </member>
    <member name="M:System.Configuration.CommaDelimitedStringCollectionConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts a <see cref="T:System.Configuration.CommaDelimitedStringCollection" /> object to a <see cref="T:System.String" /> object.</summary>
      <returns>The <see cref="T:System.String" /> representing the converted <paramref name="value" /> parameter, which is a <see cref="T:System.Configuration.CommaDelimitedStringCollection" />.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> used during conversion.</param>
      <param name="value">The value to convert.</param>
      <param name="type">The conversion type.</param>
    </member>
    <member name="T:System.Configuration.Configuration">
      <summary>Represents a configuration file that is applicable to a particular computer, application, or resource. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Configuration.Configuration.AppSettings">
      <summary>Gets the <see cref="T:System.Configuration.AppSettingsSection" /> object configuration section that applies to this <see cref="T:System.Configuration.Configuration" /> object.</summary>
      <returns>An <see cref="T:System.Configuration.AppSettingsSection" /> object representing the appSettings configuration section that applies to this <see cref="T:System.Configuration.Configuration" /> object.</returns>
    </member>
    <member name="P:System.Configuration.Configuration.AssemblyStringTransformer">
      <summary>Specifies a function delegate that is used to transform assembly strings in configuration files.</summary>
      <returns>A delegate that transforms type strings. The default value is null.</returns>
    </member>
    <member name="P:System.Configuration.Configuration.ConnectionStrings">
      <summary>Gets a <see cref="T:System.Configuration.ConnectionStringsSection" /> configuration-section object that applies to this <see cref="T:System.Configuration.Configuration" /> object.</summary>
      <returns>A <see cref="T:System.Configuration.ConnectionStringsSection" /> configuration-section object representing the connectionStrings configuration section that applies to this <see cref="T:System.Configuration.Configuration" /> object.</returns>
    </member>
    <member name="P:System.Configuration.Configuration.EvaluationContext">
      <summary>Gets the <see cref="T:System.Configuration.ContextInformation" /> object for the <see cref="T:System.Configuration.Configuration" /> object.</summary>
      <returns>The <see cref="T:System.Configuration.ContextInformation" /> object for the <see cref="T:System.Configuration.Configuration" /> object.</returns>
    </member>
    <member name="P:System.Configuration.Configuration.FilePath">
      <summary>Gets the physical path to the configuration file represented by this <see cref="T:System.Configuration.Configuration" /> object.</summary>
      <returns>The physical path to the configuration file represented by this <see cref="T:System.Configuration.Configuration" /> object.</returns>
    </member>
    <member name="M:System.Configuration.Configuration.GetSection(System.String)">
      <summary>Returns the specified <see cref="T:System.Configuration.ConfigurationSection" /> object.</summary>
      <returns>The specified <see cref="T:System.Configuration.ConfigurationSection" /> object.</returns>
      <param name="sectionName">The path to the section to be returned.</param>
    </member>
    <member name="M:System.Configuration.Configuration.GetSectionGroup(System.String)">
      <summary>Gets the specified <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationSectionGroup" /> specified.</returns>
      <param name="sectionGroupName">The path name of the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> to return.</param>
    </member>
    <member name="P:System.Configuration.Configuration.HasFile">
      <summary>Gets a value that indicates whether a file exists for the resource represented by this <see cref="T:System.Configuration.Configuration" /> object.</summary>
      <returns>true if there is a configuration file; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.Configuration.Locations">
      <summary>Gets the locations defined within this <see cref="T:System.Configuration.Configuration" /> object.</summary>
      <returns>A <see cref="T:System.Configuration.ConfigurationLocationCollection" /> containing the locations defined within this <see cref="T:System.Configuration.Configuration" /> object.</returns>
    </member>
    <member name="P:System.Configuration.Configuration.NamespaceDeclared">
      <summary>Gets or sets a value indicating whether the configuration file has an XML namespace.</summary>
      <returns>true if the configuration file has an XML namespace; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.Configuration.RootSectionGroup">
      <summary>Gets the root <see cref="T:System.Configuration.ConfigurationSectionGroup" /> for this <see cref="T:System.Configuration.Configuration" /> object.</summary>
      <returns>The root section group for this <see cref="T:System.Configuration.Configuration" /> object.</returns>
    </member>
    <member name="M:System.Configuration.Configuration.Save">
      <summary>Writes the configuration settings contained within this <see cref="T:System.Configuration.Configuration" /> object to the current XML configuration file.</summary>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The configuration file could not be written to.- or -The configuration file has changed. </exception>
    </member>
    <member name="M:System.Configuration.Configuration.Save(System.Configuration.ConfigurationSaveMode)">
      <summary>Writes the configuration settings contained within this <see cref="T:System.Configuration.Configuration" /> object to the current XML configuration file.</summary>
      <param name="saveMode">A <see cref="T:System.Configuration.ConfigurationSaveMode" /> value that determines which property values to save.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The configuration file could not be written to.- or -The configuration file has changed. </exception>
    </member>
    <member name="M:System.Configuration.Configuration.Save(System.Configuration.ConfigurationSaveMode,System.Boolean)">
      <summary>Writes the configuration settings contained within this <see cref="T:System.Configuration.Configuration" /> object to the current XML configuration file.</summary>
      <param name="saveMode">A <see cref="T:System.Configuration.ConfigurationSaveMode" /> value that determines which property values to save.</param>
      <param name="forceSaveAll">true to save even if the configuration was not modified; otherwise, false.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The configuration file could not be written to.- or -The configuration file has changed. </exception>
    </member>
    <member name="M:System.Configuration.Configuration.SaveAs(System.String)">
      <summary>Writes the configuration settings contained within this <see cref="T:System.Configuration.Configuration" /> object to the specified XML configuration file.</summary>
      <param name="filename">The path and file name to save the configuration file to.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The configuration file could not be written to.- or -The configuration file has changed. </exception>
    </member>
    <member name="M:System.Configuration.Configuration.SaveAs(System.String,System.Configuration.ConfigurationSaveMode)">
      <summary>Writes the configuration settings contained within this <see cref="T:System.Configuration.Configuration" /> object to the specified XML configuration file.</summary>
      <param name="filename">The path and file name to save the configuration file to.</param>
      <param name="saveMode">A <see cref="T:System.Configuration.ConfigurationSaveMode" /> value that determines which property values to save.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The configuration file could not be written to.- or -The configuration file has changed. </exception>
    </member>
    <member name="M:System.Configuration.Configuration.SaveAs(System.String,System.Configuration.ConfigurationSaveMode,System.Boolean)">
      <summary>Writes the configuration settings contained within this <see cref="T:System.Configuration.Configuration" /> object to the specified XML configuration file.</summary>
      <param name="filename">The path and file name to save the configuration file to.</param>
      <param name="saveMode">A <see cref="T:System.Configuration.ConfigurationSaveMode" /> value that determines which property values to save.</param>
      <param name="forceSaveAll">true to save even if the configuration was not modified; otherwise, false.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="filename" /> is null or an empty string ("").</exception>
    </member>
    <member name="P:System.Configuration.Configuration.SectionGroups">
      <summary>Gets a collection of the section groups defined by this configuration.</summary>
      <returns>A <see cref="T:System.Configuration.ConfigurationSectionGroupCollection" /> collection representing the collection of section groups for this <see cref="T:System.Configuration.Configuration" /> object.</returns>
    </member>
    <member name="P:System.Configuration.Configuration.Sections">
      <summary>Gets a collection of the sections defined by this <see cref="T:System.Configuration.Configuration" /> object.</summary>
      <returns>A collection of the sections defined by this <see cref="T:System.Configuration.Configuration" /> object.</returns>
    </member>
    <member name="P:System.Configuration.Configuration.TargetFramework">
      <summary>Specifies the targeted version of the .NET Framework when a version earlier than the current one is targeted.</summary>
      <returns>The name of the targeted version of the .NET Framework. The default value is null, which indicates that the current version is targeted.</returns>
    </member>
    <member name="P:System.Configuration.Configuration.TypeStringTransformer">
      <summary>Specifies a function delegate that is used to transform type strings in configuration files. </summary>
      <returns>A delegate that transforms type strings. The default value is null.</returns>
    </member>
    <member name="T:System.Configuration.ConfigurationAllowDefinition">
      <summary>Specifies the locations within the configuration-file hierarchy that can set or override the properties contained within a <see cref="T:System.Configuration.ConfigurationSection" /> object.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationAllowDefinition.MachineOnly">
      <summary>The <see cref="T:System.Configuration.ConfigurationSection" /> can be defined only in the Machine.config file.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationAllowDefinition.MachineToWebRoot">
      <summary>The <see cref="T:System.Configuration.ConfigurationSection" /> can be defined in either the Machine.config file or the machine-level Web.config file found in the same directory as Machine.config, but not in application Web.config files.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationAllowDefinition.MachineToApplication">
      <summary>The <see cref="T:System.Configuration.ConfigurationSection" /> can be defined in either the Machine.config file, the machine-level Web.config file found in the same directory as Machine.config, or the top-level application Web.config file found in the virtual-directory root, but not in subdirectories of a virtual root.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationAllowDefinition.Everywhere">
      <summary>The <see cref="T:System.Configuration.ConfigurationSection" /> can be defined anywhere.</summary>
    </member>
    <member name="T:System.Configuration.ConfigurationAllowExeDefinition">
      <summary>Specifies the locations within the configuration-file hierarchy that can set or override the properties contained within a <see cref="T:System.Configuration.ConfigurationSection" /> object.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationAllowExeDefinition.MachineOnly">
      <summary>The <see cref="T:System.Configuration.ConfigurationSection" /> can be defined only in the Machine.config file. </summary>
    </member>
    <member name="F:System.Configuration.ConfigurationAllowExeDefinition.MachineToApplication">
      <summary>The <see cref="T:System.Configuration.ConfigurationSection" /> can be defined either in the Machine.config file or in the Exe.config file in the client application directory. This is the default value.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationAllowExeDefinition.MachineToRoamingUser">
      <summary>The <see cref="T:System.Configuration.ConfigurationSection" /> can be defined in the Machine.config file, in the Exe.config file in the client application directory, or in the User.config file in the roaming user directory. </summary>
    </member>
    <member name="F:System.Configuration.ConfigurationAllowExeDefinition.MachineToLocalUser">
      <summary>The <see cref="T:System.Configuration.ConfigurationSection" /> can be defined in the Machine.config file, in the Exe.config file in the client application directory, in the User.config file in the roaming user directory, or in the User.config file in the local user directory.</summary>
    </member>
    <member name="T:System.Configuration.ConfigurationCollectionAttribute">
      <summary>Declaratively instructs the .NET Framework to create an instance of a configuration element collection. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationCollectionAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationCollectionAttribute" /> class.</summary>
      <param name="itemType">The type of the property collection to create.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="itemType" /> is null.</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationCollectionAttribute.AddItemName">
      <summary>Gets or sets the name of the &lt;add&gt; configuration element.</summary>
      <returns>The name that substitutes the standard name "add" for the configuration item.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationCollectionAttribute.ClearItemsName">
      <summary>Gets or sets the name for the &lt;clear&gt; configuration element.</summary>
      <returns>The name that replaces the standard name "clear" for the configuration item.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationCollectionAttribute.CollectionType">
      <summary>Gets or sets the type of the <see cref="T:System.Configuration.ConfigurationCollectionAttribute" /> attribute.</summary>
      <returns>The type of the <see cref="T:System.Configuration.ConfigurationCollectionAttribute" />.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationCollectionAttribute.ItemType">
      <summary>Gets the type of the collection element.</summary>
      <returns>The type of the collection element.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationCollectionAttribute.RemoveItemName">
      <summary>Gets or sets the name for the &lt;remove&gt; configuration element.</summary>
      <returns>The name that replaces the standard name "remove" for the configuration element.</returns>
    </member>
    <member name="T:System.Configuration.ConfigurationConverterBase">
      <summary>The base class for the configuration converter types.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationConverterBase.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationConverterBase" /> class.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationConverterBase.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Determines whether the conversion is allowed.</summary>
      <returns>true if the conversion is allowed; otherwise, false.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="type">The <see cref="T:System.Type" /> to convert from.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationConverterBase.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Determines whether the conversion is allowed.</summary>
      <returns>true if the conversion is allowed; otherwise, false. </returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversion.</param>
      <param name="type">The type to convert to.</param>
    </member>
    <member name="T:System.Configuration.ConfigurationElement">
      <summary>Represents a configuration element within a configuration file.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationElement" /> class. </summary>
    </member>
    <member name="P:System.Configuration.ConfigurationElement.CurrentConfiguration">
      <summary>Gets a reference to the top-level <see cref="T:System.Configuration.Configuration" /> instance that represents the configuration hierarchy that the current <see cref="T:System.Configuration.ConfigurationElement" /> instance belongs to.</summary>
      <returns>The top-level <see cref="T:System.Configuration.Configuration" /> instance that the current <see cref="T:System.Configuration.ConfigurationElement" /> instance belongs to.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.DeserializeElement(System.Xml.XmlReader,System.Boolean)">
      <summary>Reads XML from the configuration file.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> that reads from the configuration file.</param>
      <param name="serializeCollectionKey">true to serialize only the collection key properties; otherwise, false.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The element to read is locked.- or -An attribute of the current node is not recognized.- or -The lock status of the current node cannot be determined.  </exception>
    </member>
    <member name="P:System.Configuration.ConfigurationElement.ElementInformation">
      <summary>Gets an <see cref="T:System.Configuration.ElementInformation" /> object that contains the non-customizable information and functionality of the <see cref="T:System.Configuration.ConfigurationElement" /> object. </summary>
      <returns>An <see cref="T:System.Configuration.ElementInformation" /> that contains the non-customizable information and functionality of the <see cref="T:System.Configuration.ConfigurationElement" />.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationElement.ElementProperty">
      <summary>Gets the <see cref="T:System.Configuration.ConfigurationElementProperty" /> object that represents the <see cref="T:System.Configuration.ConfigurationElement" /> object itself.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationElementProperty" /> that represents the <see cref="T:System.Configuration.ConfigurationElement" /> itself.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.Equals(System.Object)">
      <summary>Compares the current <see cref="T:System.Configuration.ConfigurationElement" /> instance to the specified object.</summary>
      <returns>true if the object to compare with is equal to the current <see cref="T:System.Configuration.ConfigurationElement" /> instance; otherwise, false. The default is false. </returns>
      <param name="compareTo">The object to compare with.</param>
    </member>
    <member name="P:System.Configuration.ConfigurationElement.EvaluationContext">
      <summary>Gets the <see cref="T:System.Configuration.ContextInformation" /> object for the <see cref="T:System.Configuration.ConfigurationElement" /> object.</summary>
      <returns>The <see cref="T:System.Configuration.ContextInformation" /> for the <see cref="T:System.Configuration.ConfigurationElement" />.</returns>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The current element is not associated with a context.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.GetHashCode">
      <summary>Gets a unique value representing the current <see cref="T:System.Configuration.ConfigurationElement" /> instance.</summary>
      <returns>A unique value representing the current <see cref="T:System.Configuration.ConfigurationElement" /> instance.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.GetTransformedAssemblyString(System.String)">
      <summary>Returns the transformed version of the specified assembly name. </summary>
      <returns>The transformed version of the assembly name. If no transformer is available, the <paramref name="assemblyName" /> parameter value is returned unchanged. The <see cref="P:System.Configuration.Configuration.TypeStringTransformer" /> property is null if no transformer is available.</returns>
      <param name="assemblyName">The name of the assembly.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.GetTransformedTypeString(System.String)">
      <summary>Returns the transformed version of the specified type name.</summary>
      <returns>The transformed version of the specified type name. If no transformer is available, the <paramref name="typeName" /> parameter value is returned unchanged. The <see cref="P:System.Configuration.Configuration.TypeStringTransformer" /> property is null if no transformer is available.</returns>
      <param name="typeName">The name of the type.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.Init">
      <summary>Sets the <see cref="T:System.Configuration.ConfigurationElement" /> object to its initial state.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.InitializeDefault">
      <summary>Used to initialize a default set of values for the <see cref="T:System.Configuration.ConfigurationElement" /> object.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.IsModified">
      <summary>Indicates whether this configuration element has been modified since it was last saved or loaded, when implemented in a derived class.</summary>
      <returns>true if the element has been modified; otherwise, false. </returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Configuration.ConfigurationElement" /> object is read-only.</summary>
      <returns>true if the <see cref="T:System.Configuration.ConfigurationElement" /> object is read-only; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationElement.Item(System.Configuration.ConfigurationProperty)">
      <summary>Gets or sets a property or attribute of this configuration element.</summary>
      <returns>The specified property, attribute, or child element.</returns>
      <param name="prop">The property to access. </param>
      <exception cref="T:System.Configuration.ConfigurationException">
        <paramref name="prop" /> is null or does not exist within the element.</exception>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">
        <paramref name="prop" /> is read only or locked.</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationElement.Item(System.String)">
      <summary>Gets or sets a property, attribute, or child element of this configuration element.</summary>
      <returns>The specified property, attribute, or child element</returns>
      <param name="propertyName">The name of the <see cref="T:System.Configuration.ConfigurationProperty" /> to access.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">
        <paramref name="prop" /> is read-only or locked.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.ListErrors(System.Collections.IList)">
      <summary>Adds the invalid-property errors in this <see cref="T:System.Configuration.ConfigurationElement" /> object, and in all subelements, to the passed list.</summary>
      <param name="errorList">An object that implements the <see cref="T:System.Collections.IList" /> interface.</param>
    </member>
    <member name="P:System.Configuration.ConfigurationElement.LockAllAttributesExcept">
      <summary>Gets the collection of locked attributes.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationLockCollection" /> of locked attributes (properties) for the element.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationElement.LockAllElementsExcept">
      <summary>Gets the collection of locked elements.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationLockCollection" /> of locked elements.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationElement.LockAttributes">
      <summary>Gets the collection of locked attributes </summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationLockCollection" /> of locked attributes (properties) for the element.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationElement.LockElements">
      <summary>Gets the collection of locked elements.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationLockCollection" /> of locked elements.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationElement.LockItem">
      <summary>Gets or sets a value indicating whether the element is locked.</summary>
      <returns>true if the element is locked; otherwise, false. The default is false.</returns>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The element has already been locked at a higher configuration level.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.OnDeserializeUnrecognizedAttribute(System.String,System.String)">
      <summary>Gets a value indicating whether an unknown attribute is encountered during deserialization.</summary>
      <returns>true when an unknown attribute is encountered while deserializing; otherwise, false.</returns>
      <param name="name">The name of the unrecognized attribute.</param>
      <param name="value">The value of the unrecognized attribute.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.OnDeserializeUnrecognizedElement(System.String,System.Xml.XmlReader)">
      <summary>Gets a value indicating whether an unknown element is encountered during deserialization.</summary>
      <returns>true when an unknown element is encountered while deserializing; otherwise, false.</returns>
      <param name="elementName">The name of the unknown subelement.</param>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> being used for deserialization.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The element identified by <paramref name="elementName" /> is locked.- or -One or more of the element's attributes is locked.- or -<paramref name="elementName" /> is unrecognized, or the element has an unrecognized attribute.- or -The element has a Boolean attribute with an invalid value.- or -An attempt was made to deserialize a property more than once.- or -An attempt was made to deserialize a property that is not a valid member of the element.- or -The element cannot contain a CDATA or text element.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.OnRequiredPropertyNotFound(System.String)">
      <summary>Throws an exception when a required property is not found.</summary>
      <returns>None.</returns>
      <param name="name">The name of the required attribute that was not found.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">In all cases.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.PostDeserialize">
      <summary>Called after deserialization.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.PreSerialize(System.Xml.XmlWriter)">
      <summary>Called before serialization.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> that will be used to serialize the <see cref="T:System.Configuration.ConfigurationElement" />.</param>
    </member>
    <member name="P:System.Configuration.ConfigurationElement.Properties">
      <summary>Gets the collection of properties.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> of properties for the element.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.Reset(System.Configuration.ConfigurationElement)">
      <summary>Resets the internal state of the <see cref="T:System.Configuration.ConfigurationElement" /> object, including the locks and the properties collections.</summary>
      <param name="parentElement">The parent node of the configuration element.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.ResetModified">
      <summary>Resets the value of the <see cref="M:System.Configuration.ConfigurationElement.IsModified" /> method to false when implemented in a derived class.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.SerializeElement(System.Xml.XmlWriter,System.Boolean)">
      <summary>Writes the contents of this configuration element to the configuration file when implemented in a derived class.</summary>
      <returns>true if any data was actually serialized; otherwise, false.</returns>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> that writes to the configuration file. </param>
      <param name="serializeCollectionKey">true to serialize only the collection key properties; otherwise, false. </param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The current attribute is locked at a higher configuration level.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.SerializeToXmlElement(System.Xml.XmlWriter,System.String)">
      <summary>Writes the outer tags of this configuration element to the configuration file when implemented in a derived class.</summary>
      <returns>true if writing was successful; otherwise, false.</returns>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> that writes to the configuration file. </param>
      <param name="elementName">The name of the <see cref="T:System.Configuration.ConfigurationElement" /> to be written. </param>
      <exception cref="T:System.Exception">The element has multiple child elements. </exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.SetPropertyValue(System.Configuration.ConfigurationProperty,System.Object,System.Boolean)">
      <summary>Sets a property to the specified value.</summary>
      <param name="prop">The element property to set. </param>
      <param name="value">The value to assign to the property.</param>
      <param name="ignoreLocks">true if the locks on the property should be ignored; otherwise, false.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">Occurs if the element is read-only or <paramref name="ignoreLocks" /> is true but the locks cannot be ignored.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.SetReadOnly">
      <summary>Sets the <see cref="M:System.Configuration.ConfigurationElement.IsReadOnly" /> property for the <see cref="T:System.Configuration.ConfigurationElement" /> object and all subelements.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationElement.Unmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
      <summary>Modifies the <see cref="T:System.Configuration.ConfigurationElement" /> object to remove all values that should not be saved. </summary>
      <param name="sourceElement">A <see cref="T:System.Configuration.ConfigurationElement" /> at the current level containing a merged view of the properties.</param>
      <param name="parentElement">The parent <see cref="T:System.Configuration.ConfigurationElement" />, or null if this is the top level.</param>
      <param name="saveMode">A <see cref="T:System.Configuration.ConfigurationSaveMode" /> that determines which property values to include.</param>
    </member>
    <member name="T:System.Configuration.ConfigurationElementCollection">
      <summary>Represents a configuration element containing a collection of child elements.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationElementCollection" /> class. </summary>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.#ctor(System.Collections.IComparer)">
      <summary>Creates a new instance of the <see cref="T:System.Configuration.ConfigurationElementCollection" /> class.</summary>
      <param name="comparer">The <see cref="T:System.Collections.IComparer" /> comparer to use.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" /> is null.</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationElementCollection.AddElementName">
      <summary>Gets or sets the name of the <see cref="T:System.Configuration.ConfigurationElement" /> to associate with the add operation in the <see cref="T:System.Configuration.ConfigurationElementCollection" /> when overridden in a derived class. </summary>
      <returns>The name of the element.</returns>
      <exception cref="T:System.ArgumentException">The selected value starts with the reserved prefix "config" or "lock".</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.BaseAdd(System.Configuration.ConfigurationElement)">
      <summary>Adds a configuration element to the <see cref="T:System.Configuration.ConfigurationElementCollection" />.</summary>
      <param name="element">The <see cref="T:System.Configuration.ConfigurationElement" /> to add. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.BaseAdd(System.Configuration.ConfigurationElement,System.Boolean)">
      <summary>Adds a configuration element to the configuration element collection.</summary>
      <param name="element">The <see cref="T:System.Configuration.ConfigurationElement" /> to add. </param>
      <param name="throwIfExists">true to throw an exception if the <see cref="T:System.Configuration.ConfigurationElement" /> specified is already contained in the <see cref="T:System.Configuration.ConfigurationElementCollection" />; otherwise, false. </param>
      <exception cref="T:System.Exception">The <see cref="T:System.Configuration.ConfigurationElement" /> to add already exists in the <see cref="T:System.Configuration.ConfigurationElementCollection" /> and the <paramref name="throwIfExists" /> parameter is true. </exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.BaseAdd(System.Int32,System.Configuration.ConfigurationElement)">
      <summary>Adds a configuration element to the configuration element collection.</summary>
      <param name="index">The index location at which to add the specified <see cref="T:System.Configuration.ConfigurationElement" />. </param>
      <param name="element">The <see cref="T:System.Configuration.ConfigurationElement" /> to add. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.BaseClear">
      <summary>Removes all configuration element objects from the collection.</summary>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The configuration is read-only.- or -A collection item has been locked in a higher-level configuration.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.BaseGet(System.Int32)">
      <summary>Gets the configuration element at the specified index location.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationElement" /> at the specified index.</returns>
      <param name="index">The index location of the <see cref="T:System.Configuration.ConfigurationElement" /> to return. </param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">
        <paramref name="index" /> is less than 0.- or -There is no <see cref="T:System.Configuration.ConfigurationElement" /> at the specified <paramref name="index" />.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.BaseGet(System.Object)">
      <summary>Returns the configuration element with the specified key.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationElement" /> with the specified key; otherwise, null.</returns>
      <param name="key">The key of the element to return. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.BaseGetAllKeys">
      <summary>Returns an array of the keys for all of the configuration elements contained in the <see cref="T:System.Configuration.ConfigurationElementCollection" />.</summary>
      <returns>An array that contains the keys for all of the <see cref="T:System.Configuration.ConfigurationElement" /> objects contained in the <see cref="T:System.Configuration.ConfigurationElementCollection" />.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.BaseGetKey(System.Int32)">
      <summary>Gets the key for the <see cref="T:System.Configuration.ConfigurationElement" /> at the specified index location.</summary>
      <returns>The key for the specified <see cref="T:System.Configuration.ConfigurationElement" />.</returns>
      <param name="index">The index location for the <see cref="T:System.Configuration.ConfigurationElement" />.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">
        <paramref name="index" /> is less than 0.- or -There is no <see cref="T:System.Configuration.ConfigurationElement" /> at the specified <paramref name="index" />.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.BaseIndexOf(System.Configuration.ConfigurationElement)">
      <summary>The index of the specified <see cref="T:System.Configuration.ConfigurationElement" />.</summary>
      <returns>The index of the specified <see cref="T:System.Configuration.ConfigurationElement" />; otherwise, -1.</returns>
      <param name="element">The <see cref="T:System.Configuration.ConfigurationElement" /> for the specified index location. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.BaseIsRemoved(System.Object)">
      <summary>Gets a value indicating whether the <see cref="T:System.Configuration.ConfigurationElement" /> with the specified key has been removed from the <see cref="T:System.Configuration.ConfigurationElementCollection" />.</summary>
      <returns>true if the <see cref="T:System.Configuration.ConfigurationElement" /> with the specified key has been removed; otherwise, false. The default is false.</returns>
      <param name="key">The key of the element to check.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.BaseRemove(System.Object)">
      <summary>Removes a <see cref="T:System.Configuration.ConfigurationElement" /> from the collection.</summary>
      <param name="key">The key of the <see cref="T:System.Configuration.ConfigurationElement" /> to remove. </param>
      <exception cref="T:System.Exception">No <see cref="T:System.Configuration.ConfigurationElement" /> with the specified key exists in the collection, the element has already been removed, or the element cannot be removed because the value of its <see cref="P:System.Configuration.ConfigurationProperty.Type" /> is not <see cref="F:System.Configuration.ConfigurationElementCollectionType.AddRemoveClearMap" />. </exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.BaseRemoveAt(System.Int32)">
      <summary>Removes the <see cref="T:System.Configuration.ConfigurationElement" /> at the specified index location.</summary>
      <param name="index">The index location of the <see cref="T:System.Configuration.ConfigurationElement" /> to remove. </param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The configuration is read-only.- or -<paramref name="index" /> is less than 0 or greater than the number of <see cref="T:System.Configuration.ConfigurationElement" /> objects in the collection.- or -The <see cref="T:System.Configuration.ConfigurationElement" /> object has already been removed.- or -The value of the <see cref="T:System.Configuration.ConfigurationElement" /> object has been locked at a higher level.- or -The <see cref="T:System.Configuration.ConfigurationElement" /> object was inherited.- or -The value of the <see cref="T:System.Configuration.ConfigurationElement" /> object's <see cref="P:System.Configuration.ConfigurationProperty.Type" /> is not <see cref="F:System.Configuration.ConfigurationElementCollectionType.AddRemoveClearMap" /> or <see cref="F:System.Configuration.ConfigurationElementCollectionType.AddRemoveClearMapAlternate" />.</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationElementCollection.ClearElementName">
      <summary>Gets or sets the name for the <see cref="T:System.Configuration.ConfigurationElement" /> to associate with the clear operation in the <see cref="T:System.Configuration.ConfigurationElementCollection" /> when overridden in a derived class. </summary>
      <returns>The name of the element.</returns>
      <exception cref="T:System.ArgumentException">The selected value starts with the reserved prefix "config" or "lock".</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationElementCollection.CollectionType">
      <summary>Gets the type of the <see cref="T:System.Configuration.ConfigurationElementCollection" />.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationElementCollectionType" /> of this collection.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.CopyTo(System.Configuration.ConfigurationElement[],System.Int32)">
      <summary>Copies the contents of the <see cref="T:System.Configuration.ConfigurationElementCollection" /> to an array.</summary>
      <param name="array">Array to which to copy the contents of the <see cref="T:System.Configuration.ConfigurationElementCollection" />.</param>
      <param name="index">Index location at which to begin copying.</param>
    </member>
    <member name="P:System.Configuration.ConfigurationElementCollection.Count">
      <summary>Gets the number of elements in the collection.</summary>
      <returns>The number of elements in the collection.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.CreateNewElement">
      <summary>When overridden in a derived class, creates a new <see cref="T:System.Configuration.ConfigurationElement" />.</summary>
      <returns>A new <see cref="T:System.Configuration.ConfigurationElement" />.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.CreateNewElement(System.String)">
      <summary>Creates a new <see cref="T:System.Configuration.ConfigurationElement" /> when overridden in a derived class.</summary>
      <returns>A new <see cref="T:System.Configuration.ConfigurationElement" />.</returns>
      <param name="elementName">The name of the <see cref="T:System.Configuration.ConfigurationElement" /> to create. </param>
    </member>
    <member name="P:System.Configuration.ConfigurationElementCollection.ElementName">
      <summary>Gets the name used to identify this collection of elements in the configuration file when overridden in a derived class.</summary>
      <returns>The name of the collection; otherwise, an empty string. The default is an empty string.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationElementCollection.EmitClear">
      <summary>Gets or sets a value that specifies whether the collection has been cleared.</summary>
      <returns>true if the collection has been cleared; otherwise, false. The default is false.</returns>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The configuration is read-only.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.Equals(System.Object)">
      <summary>Compares the <see cref="T:System.Configuration.ConfigurationElementCollection" /> to the specified object.</summary>
      <returns>true if the object to compare with is equal to the current <see cref="T:System.Configuration.ConfigurationElementCollection" /> instance; otherwise, false. The default is false.</returns>
      <param name="compareTo">The object to compare. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.GetElementKey(System.Configuration.ConfigurationElement)">
      <summary>Gets the element key for a specified configuration element when overridden in a derived class.</summary>
      <returns>An <see cref="T:System.Object" /> that acts as the key for the specified <see cref="T:System.Configuration.ConfigurationElement" />.</returns>
      <param name="element">The <see cref="T:System.Configuration.ConfigurationElement" /> to return the key for. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.GetEnumerator">
      <summary>Gets an <see cref="T:System.Collections.IEnumerator" /> which is used to iterate through the <see cref="T:System.Configuration.ConfigurationElementCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> which is used to iterate through the <see cref="T:System.Configuration.ConfigurationElementCollection" /></returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.GetHashCode">
      <summary>Gets a unique value representing the <see cref="T:System.Configuration.ConfigurationElementCollection" /> instance.</summary>
      <returns>A unique value representing the <see cref="T:System.Configuration.ConfigurationElementCollection" /> current instance.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.IsElementName(System.String)">
      <summary>Indicates whether the specified <see cref="T:System.Configuration.ConfigurationElement" /> exists in the <see cref="T:System.Configuration.ConfigurationElementCollection" />.</summary>
      <returns>true if the element exists in the collection; otherwise, false. The default is false.</returns>
      <param name="elementName">The name of the element to verify. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.IsElementRemovable(System.Configuration.ConfigurationElement)">
      <summary>Gets a value indicating whether the specified <see cref="T:System.Configuration.ConfigurationElement" /> can be removed from the <see cref="T:System.Configuration.ConfigurationElementCollection" />.</summary>
      <returns>true if the specified <see cref="T:System.Configuration.ConfigurationElement" /> can be removed from this <see cref="T:System.Configuration.ConfigurationElementCollection" />; otherwise, false. The default is true.</returns>
      <param name="element">The element to check.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.IsModified">
      <summary>Indicates whether this <see cref="T:System.Configuration.ConfigurationElementCollection" /> has been modified since it was last saved or loaded when overridden in a derived class.</summary>
      <returns>true if any contained element has been modified; otherwise, false</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Configuration.ConfigurationElementCollection" /> object is read only.</summary>
      <returns>true if the <see cref="T:System.Configuration.ConfigurationElementCollection" /> object is read only; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationElementCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Configuration.ConfigurationElementCollection" /> is synchronized; otherwise, false.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.OnDeserializeUnrecognizedElement(System.String,System.Xml.XmlReader)">
      <summary>Causes the configuration system to throw an exception.</summary>
      <returns>true if the unrecognized element was deserialized successfully; otherwise, false. The default is false.</returns>
      <param name="elementName">The name of the unrecognized element. </param>
      <param name="reader">An input stream that reads XML from the configuration file. </param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The element specified in <paramref name="elementName" /> is the &lt;clear&gt; element.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="elementName" /> starts with the reserved prefix "config" or "lock".</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationElementCollection.RemoveElementName">
      <summary>Gets or sets the name of the <see cref="T:System.Configuration.ConfigurationElement" /> to associate with the remove operation in the <see cref="T:System.Configuration.ConfigurationElementCollection" /> when overridden in a derived class. </summary>
      <returns>The name of the element.</returns>
      <exception cref="T:System.ArgumentException">The selected value starts with the reserved prefix "config" or "lock".</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.Reset(System.Configuration.ConfigurationElement)">
      <summary>Resets the <see cref="T:System.Configuration.ConfigurationElementCollection" /> to its unmodified state when overridden in a derived class.</summary>
      <param name="parentElement">The <see cref="T:System.Configuration.ConfigurationElement" /> representing the collection parent element, if any; otherwise, null. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.ResetModified">
      <summary>Resets the value of the <see cref="M:System.Configuration.ConfigurationElementCollection.IsModified" /> property to false when overridden in a derived class.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.SerializeElement(System.Xml.XmlWriter,System.Boolean)">
      <summary>Writes the configuration data to an XML element in the configuration file when overridden in a derived class.</summary>
      <returns>true if the <see cref="T:System.Configuration.ConfigurationElementCollection" /> was written to the configuration file successfully.</returns>
      <param name="writer">Output stream that writes XML to the configuration file. </param>
      <param name="serializeCollectionKey">true to serialize the collection key; otherwise, false. </param>
      <exception cref="T:System.ArgumentException">One of the elements in the collection was added or replaced and starts with the reserved prefix "config" or "lock".</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.SetReadOnly">
      <summary>Sets the <see cref="M:System.Configuration.ConfigurationElementCollection.IsReadOnly" /> property for the <see cref="T:System.Configuration.ConfigurationElementCollection" /> object and for all sub-elements.</summary>
    </member>
    <member name="P:System.Configuration.ConfigurationElementCollection.SyncRoot">
      <summary>Gets an object used to synchronize access to the <see cref="T:System.Configuration.ConfigurationElementCollection" />.</summary>
      <returns>An object used to synchronize access to the <see cref="T:System.Configuration.ConfigurationElementCollection" />.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the <see cref="T:System.Configuration.ConfigurationElementCollection" /> to an array.</summary>
      <param name="arr">Array to which to copy this <see cref="T:System.Configuration.ConfigurationElementCollection" />.</param>
      <param name="index">Index location at which to begin copying.</param>
    </member>
    <member name="P:System.Configuration.ConfigurationElementCollection.ThrowOnDuplicate">
      <summary>Gets a value indicating whether an attempt to add a duplicate <see cref="T:System.Configuration.ConfigurationElement" /> to the <see cref="T:System.Configuration.ConfigurationElementCollection" /> will cause an exception to be thrown.</summary>
      <returns>true if an attempt to add a duplicate <see cref="T:System.Configuration.ConfigurationElement" /> to this <see cref="T:System.Configuration.ConfigurationElementCollection" /> will cause an exception to be thrown; otherwise, false. </returns>
    </member>
    <member name="M:System.Configuration.ConfigurationElementCollection.Unmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
      <summary>Reverses the effect of merging configuration information from different levels of the configuration hierarchy </summary>
      <param name="sourceElement">A <see cref="T:System.Configuration.ConfigurationElement" /> object at the current level containing a merged view of the properties.</param>
      <param name="parentElement">The parent <see cref="T:System.Configuration.ConfigurationElement" /> object of the current element, or null if this is the top level.</param>
      <param name="saveMode">A <see cref="T:System.Configuration.ConfigurationSaveMode" /> enumerated value that determines which property values to include.</param>
    </member>
    <member name="T:System.Configuration.ConfigurationElementCollectionType">
      <summary>Specifies the type of a <see cref="T:System.Configuration.ConfigurationElementCollectionType" /> object.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationElementCollectionType.BasicMap">
      <summary>Collections of this type contain elements that apply to the level at which they are specified, and to all child levels. A child level cannot modify the properties specified by a parent element of this type.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationElementCollectionType.AddRemoveClearMap">
      <summary>The default type of <see cref="T:System.Configuration.ConfigurationElementCollection" />. Collections of this type contain elements that can be merged across a hierarchy of configuration files. At any particular level within such a hierarchy, add, remove, and clear directives are used to modify any inherited properties and specify new ones.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationElementCollectionType.BasicMapAlternate">
      <summary>Same as <see cref="F:System.Configuration.ConfigurationElementCollectionType.BasicMap" />, except that this type causes the <see cref="T:System.Configuration.ConfigurationElementCollection" /> object to sort its contents such that inherited elements are listed last.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationElementCollectionType.AddRemoveClearMapAlternate">
      <summary>Same as <see cref="F:System.Configuration.ConfigurationElementCollectionType.AddRemoveClearMap" />, except that this type causes the <see cref="T:System.Configuration.ConfigurationElementCollection" /> object to sort its contents such that inherited elements are listed last.</summary>
    </member>
    <member name="T:System.Configuration.ConfigurationElementProperty">
      <summary>Specifies the property of a configuration element. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationElementProperty.#ctor(System.Configuration.ConfigurationValidatorBase)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationElementProperty" /> class, based on a supplied parameter.</summary>
      <param name="validator">A <see cref="T:System.Configuration.ConfigurationValidatorBase" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="validator" /> is null.</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationElementProperty.Validator">
      <summary>Gets a <see cref="T:System.Configuration.ConfigurationValidatorBase" /> object used to validate the <see cref="T:System.Configuration.ConfigurationElementProperty" /> object.</summary>
      <returns>A <see cref="T:System.Configuration.ConfigurationValidatorBase" /> object.</returns>
    </member>
    <member name="T:System.Configuration.ConfigurationErrorsException">
      <summary>The current value is not one of the <see cref="P:System.Web.Configuration.PagesSection.EnableSessionState" /> values.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationErrorsException" /> class.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationErrorsException" /> class.</summary>
      <param name="info">The object that holds the information to deserialize.</param>
      <param name="context">Contextual information about the source or destination.</param>
      <exception cref="T:System.InvalidOperationException">The current type is not a <see cref="T:System.Configuration.ConfigurationException" /> or a <see cref="T:System.Configuration.ConfigurationErrorsException" />.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationErrorsException" /> class.</summary>
      <param name="message">A message that describes why this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception was thrown.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationErrorsException" /> class.</summary>
      <param name="message">A message that describes why this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception was thrown.</param>
      <param name="inner">The exception that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception to be thrown.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.#ctor(System.String,System.Exception,System.String,System.Int32)">
      <summary>Initializes a new instance of a <see cref="T:System.Configuration.ConfigurationErrorsException" /> class.</summary>
      <param name="message">A message that describes why this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception was thrown.</param>
      <param name="inner">The inner exception that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception to be thrown.</param>
      <param name="filename">The path to the configuration file that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception to be thrown.</param>
      <param name="line">The line number within the configuration file at which this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception was thrown.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.#ctor(System.String,System.Exception,System.Xml.XmlNode)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationErrorsException" /> class.</summary>
      <param name="message">A message that describes why this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception was thrown.</param>
      <param name="inner">The inner exception that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception to be thrown.</param>
      <param name="node">The <see cref="T:System.Xml.XmlNode" /> object that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception to be thrown.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.#ctor(System.String,System.Exception,System.Xml.XmlReader)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationErrorsException" /> class.</summary>
      <param name="message">A message that describes why this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception was thrown.</param>
      <param name="inner">The inner exception that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception to be thrown.</param>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> object that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception to be thrown.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.#ctor(System.String,System.String,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationErrorsException" /> class.</summary>
      <param name="message">A message that describes why this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception was thrown.</param>
      <param name="filename">The path to the configuration file that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception to be thrown.</param>
      <param name="line">The line number within the configuration file at which this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception was thrown.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.#ctor(System.String,System.Xml.XmlNode)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationErrorsException" /> class.</summary>
      <param name="message">A message that describes why this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception was thrown.</param>
      <param name="node">The <see cref="T:System.Xml.XmlNode" /> object that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception to be thrown.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.#ctor(System.String,System.Xml.XmlReader)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationErrorsException" /> class.</summary>
      <param name="message">A message that describes why this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception was thrown.</param>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> object that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception to be thrown.</param>
    </member>
    <member name="P:System.Configuration.ConfigurationErrorsException.BareMessage">
      <summary>Gets a description of why this configuration exception was thrown.</summary>
      <returns>A description of why this <see cref="T:System.Configuration.ConfigurationErrorsException" /> was thrown.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationErrorsException.Errors">
      <summary>Gets a collection of errors that detail the reasons this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception was thrown.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> object that contains errors that identify the reasons this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception was thrown.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationErrorsException.Filename">
      <summary>Gets the path to the configuration file that caused this configuration exception to be thrown.</summary>
      <returns>The path to the configuration file that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> to be thrown.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.GetFilename(System.Xml.XmlNode)">
      <summary>Gets the path to the configuration file from which the internal <see cref="T:System.Xml.XmlNode" /> object was loaded when this configuration exception was thrown.</summary>
      <returns>The path to the configuration file from which the internal <see cref="T:System.Xml.XmlNode" /> object was loaded when this configuration exception was thrown. </returns>
      <param name="node">The <see cref="T:System.Xml.XmlNode" /> object that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception to be thrown.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.GetFilename(System.Xml.XmlReader)">
      <summary>Gets the path to the configuration file that the internal <see cref="T:System.Xml.XmlReader" /> was reading when this configuration exception was thrown.</summary>
      <returns>The path of the configuration file the internal <see cref="T:System.Xml.XmlReader" /> object was accessing when the exception occurred.</returns>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> object that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception to be thrown.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.GetLineNumber(System.Xml.XmlNode)">
      <summary>Gets the line number within the configuration file that the internal <see cref="T:System.Xml.XmlNode" /> object represented when this configuration exception was thrown.</summary>
      <returns>The line number within the configuration file that contains the <see cref="T:System.Xml.XmlNode" /> object being parsed when this configuration exception was thrown.</returns>
      <param name="node">The <see cref="T:System.Xml.XmlNode" /> object that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception to be thrown.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.GetLineNumber(System.Xml.XmlReader)">
      <summary>Gets the line number within the configuration file that the internal <see cref="T:System.Xml.XmlReader" /> object was processing when this configuration exception was thrown.</summary>
      <returns>The line number within the configuration file that the <see cref="T:System.Xml.XmlReader" /> object was accessing when the exception occurred.</returns>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> object that caused this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception to be thrown.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationErrorsException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with the file name and line number at which this configuration exception occurred.</summary>
      <param name="info">The object that holds the information to be serialized.</param>
      <param name="context">The contextual information about the source or destination.</param>
    </member>
    <member name="P:System.Configuration.ConfigurationErrorsException.Line">
      <summary>Gets the line number within the configuration file at which this configuration exception was thrown.</summary>
      <returns>The line number within the configuration file at which this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception was thrown.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationErrorsException.Message">
      <summary>Gets an extended description of why this configuration exception was thrown.</summary>
      <returns>An extended description of why this <see cref="T:System.Configuration.ConfigurationErrorsException" /> exception was thrown.</returns>
    </member>
    <member name="T:System.Configuration.ConfigurationFileMap">
      <summary>Defines the configuration file mapping for the machine configuration file. </summary>
    </member>
    <member name="M:System.Configuration.ConfigurationFileMap.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationFileMap" /> class. </summary>
    </member>
    <member name="M:System.Configuration.ConfigurationFileMap.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationFileMap" /> class based on the supplied parameter.</summary>
      <param name="machineConfigFilename">The name of the machine configuration file.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationFileMap.Clone">
      <summary>Creates a copy of the existing <see cref="T:System.Configuration.ConfigurationFileMap" /> object.</summary>
      <returns>A <see cref="T:System.Configuration.ConfigurationFileMap" /> object.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationFileMap.MachineConfigFilename">
      <summary>Gets or sets the name of the machine configuration file name.</summary>
      <returns>The machine configuration file name.</returns>
    </member>
    <member name="T:System.Configuration.ConfigurationLocation">
      <summary>Represents a location element within a configuration file.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationLocation.OpenConfiguration">
      <summary>Creates an instance of a Configuration object.</summary>
      <returns>A Configuration object.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationLocation.Path">
      <summary>Gets the relative path to the resource whose configuration settings are represented by this <see cref="T:System.Configuration.ConfigurationLocation" /> object.</summary>
      <returns>The relative path to the resource whose configuration settings are represented by this <see cref="T:System.Configuration.ConfigurationLocation" />.</returns>
    </member>
    <member name="T:System.Configuration.ConfigurationLocationCollection">
      <summary>Contains a collection of <see cref="T:System.Configuration.ConfigurationLocationCollection" /> objects.</summary>
    </member>
    <member name="P:System.Configuration.ConfigurationLocationCollection.Item(System.Int32)">
      <summary>Gets the <see cref="T:System.Configuration.ConfigurationLocationCollection" /> object at the specified index.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationLocationCollection" /> at the specified index.</returns>
      <param name="index">The index location of the <see cref="T:System.Configuration.ConfigurationLocationCollection" /> to return.</param>
    </member>
    <member name="T:System.Configuration.ConfigurationLockCollection">
      <summary>Contains a collection of locked configuration objects. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationLockCollection.Add(System.String)">
      <summary>Locks a configuration object by adding it to the collection.</summary>
      <param name="name">The name of the configuration object.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">Occurs when the <paramref name="name" /> does not match an existing configuration object within the collection.</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationLockCollection.AttributeList">
      <summary>Gets a list of configuration objects contained in the collection.</summary>
      <returns>A comma-delimited string that lists the lock configuration objects in the collection.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationLockCollection.Clear">
      <summary>Clears all configuration objects from the collection.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationLockCollection.Contains(System.String)">
      <summary>Verifies whether a specific configuration object is locked.</summary>
      <returns>true if the <see cref="T:System.Configuration.ConfigurationLockCollection" /> contains the specified configuration object; otherwise, false.</returns>
      <param name="name">The name of the configuration object to verify.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationLockCollection.CopyTo(System.String[],System.Int32)">
      <summary>Copies the entire <see cref="T:System.Configuration.ConfigurationLockCollection" /> collection to a compatible one-dimensional <see cref="T:System.Array" />, starting at the specified index of the target array.</summary>
      <param name="array">A one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from the <see cref="T:System.Configuration.ConfigurationLockCollection" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Configuration.ConfigurationLockCollection.Count">
      <summary>Gets the number of locked configuration objects contained in the collection.</summary>
      <returns>The number of locked configuration objects contained in the collection.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationLockCollection.GetEnumerator">
      <summary>Gets an <see cref="T:System.Collections.IEnumerator" /> object, which is used to iterate through this <see cref="T:System.Configuration.ConfigurationLockCollection" /> collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationLockCollection.HasParentElements">
      <summary>Gets a value specifying whether the collection of locked objects has parent elements.</summary>
      <returns>true if the <see cref="T:System.Configuration.ConfigurationLockCollection" /> collection has parent elements; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationLockCollection.IsModified">
      <summary>Gets a value specifying whether the collection has been modified.</summary>
      <returns>true if the <see cref="T:System.Configuration.ConfigurationLockCollection" /> collection has been modified; otherwise, false.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationLockCollection.IsReadOnly(System.String)">
      <summary>Verifies whether a specific configuration object is read-only.</summary>
      <returns>true if the specified configuration object in the <see cref="T:System.Configuration.ConfigurationLockCollection" /> collection is read-only; otherwise, false.</returns>
      <param name="name">The name of the configuration object to verify.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The specified configuration object is not in the collection.</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationLockCollection.IsSynchronized">
      <summary>Gets a value specifying whether the collection is synchronized.</summary>
      <returns>true if the <see cref="T:System.Configuration.ConfigurationLockCollection" /> collection is synchronized; otherwise, false.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationLockCollection.Remove(System.String)">
      <summary>Removes a configuration object from the collection.</summary>
      <param name="name">The name of the configuration object.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">Occurs when the <paramref name="name" /> does not match an existing configuration object within the collection.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationLockCollection.SetFromList(System.String)">
      <summary>Locks a set of configuration objects based on the supplied list.</summary>
      <param name="attributeList">A comma-delimited string.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">Occurs when an item in the <paramref name="attributeList" /> parameter is not a valid lockable configuration attribute.</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationLockCollection.SyncRoot">
      <summary>Gets an object used to synchronize access to this <see cref="T:System.Configuration.ConfigurationLockCollection" /> collection.</summary>
      <returns>An object used to synchronize access to this <see cref="T:System.Configuration.ConfigurationLockCollection" /> collection.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationLockCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the entire <see cref="T:System.Configuration.ConfigurationLockCollection" /> collection to a compatible one-dimensional <see cref="T:System.Array" />, starting at the specified index of the target array.</summary>
      <param name="array">A one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from the <see cref="T:System.Configuration.ConfigurationLockCollection" /> collection. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="T:System.Configuration.ConfigurationManager">
      <summary>Provides access to configuration files for client applications. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Configuration.ConfigurationManager.AppSettings">
      <summary>Gets the <see cref="T:System.Configuration.AppSettingsSection" /> data for the current application's default configuration.</summary>
      <returns>Returns a <see cref="T:System.Collections.Specialized.NameValueCollection" /> object that contains the contents of the <see cref="T:System.Configuration.AppSettingsSection" /> object for the current application's default configuration. </returns>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">Could not retrieve a <see cref="T:System.Collections.Specialized.NameValueCollection" /> object with the application settings data.</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationManager.ConnectionStrings">
      <summary>Gets the <see cref="T:System.Configuration.ConnectionStringsSection" /> data for the current application's default configuration.</summary>
      <returns>Returns a <see cref="T:System.Configuration.ConnectionStringSettingsCollection" /> object that contains the contents of the <see cref="T:System.Configuration.ConnectionStringsSection" /> object for the current application's default configuration. </returns>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">Could not retrieve a <see cref="T:System.Configuration.ConnectionStringSettingsCollection" /> object.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationManager.GetSection(System.String)">
      <summary>Retrieves a specified configuration section for the current application's default configuration.</summary>
      <returns>The specified <see cref="T:System.Configuration.ConfigurationSection" /> object, or null if the section does not exist.</returns>
      <param name="sectionName">The configuration section path and name.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">A configuration file could not be loaded.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationManager.OpenExeConfiguration(System.Configuration.ConfigurationUserLevel)">
      <summary>Opens the configuration file for the current application as a <see cref="T:System.Configuration.Configuration" /> object.</summary>
      <returns>A <see cref="T:System.Configuration.Configuration" /> object.</returns>
      <param name="userLevel">The <see cref="T:System.Configuration.ConfigurationUserLevel" /> for which you are opening the configuration.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">A configuration file could not be loaded.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationManager.OpenExeConfiguration(System.String)">
      <summary>Opens the specified client configuration file as a <see cref="T:System.Configuration.Configuration" /> object.</summary>
      <returns>A <see cref="T:System.Configuration.Configuration" /> object.</returns>
      <param name="exePath">The path of the executable (exe) file.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">A configuration file could not be loaded.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationManager.OpenMachineConfiguration">
      <summary>Opens the machine configuration file on the current computer as a <see cref="T:System.Configuration.Configuration" /> object.</summary>
      <returns>A <see cref="T:System.Configuration.Configuration" /> object.</returns>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">A configuration file could not be loaded.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationManager.OpenMappedExeConfiguration(System.Configuration.ExeConfigurationFileMap,System.Configuration.ConfigurationUserLevel)">
      <summary>Opens the specified client configuration file as a <see cref="T:System.Configuration.Configuration" /> object that uses the specified file mapping and user level.</summary>
      <returns>A <see cref="T:System.Configuration.Configuration" /> object.</returns>
      <param name="fileMap">An <see cref="T:System.Configuration.ExeConfigurationFileMap" /> object that references configuration file to use instead of the application default configuration file.</param>
      <param name="userLevel">The <see cref="T:System.Configuration.ConfigurationUserLevel" /> object for which you are opening the configuration.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">A configuration file could not be loaded.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationManager.OpenMappedMachineConfiguration(System.Configuration.ConfigurationFileMap)">
      <summary>Opens the machine configuration file as a <see cref="T:System.Configuration.Configuration" /> object that uses the specified file mapping.</summary>
      <returns>A <see cref="T:System.Configuration.Configuration" /> object.</returns>
      <param name="fileMap">An <see cref="T:System.Configuration.ExeConfigurationFileMap" /> object that references configuration file to use instead of the application default configuration file.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">A configuration file could not be loaded.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationManager.RefreshSection(System.String)">
      <summary>Refreshes the named section so the next time that it is retrieved it will be re-read from disk.</summary>
      <param name="sectionName">The configuration section name or the configuration path and section name of the section to refresh.</param>
    </member>
    <member name="T:System.Configuration.ConfigurationPermission">
      <summary>Provides a permission structure that allows methods or classes to access configuration files. </summary>
    </member>
    <member name="M:System.Configuration.ConfigurationPermission.#ctor(System.Security.Permissions.PermissionState)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationPermission" /> class. </summary>
      <param name="state">The permission level to grant.</param>
      <exception cref="T:System.ArgumentException">The value of <paramref name="state" /> is neither <see cref="F:System.Security.Permissions.PermissionState.Unrestricted" /> nor <see cref="F:System.Security.Permissions.PermissionState.None" />.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationPermission.Copy">
      <summary>Returns a new <see cref="T:System.Configuration.ConfigurationPermission" /> object with the same permission level.</summary>
      <returns>A new <see cref="T:System.Configuration.ConfigurationPermission" /> with the same permission level.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationPermission.FromXml(System.Security.SecurityElement)">
      <summary>Reads the value of the permission state from XML.</summary>
      <param name="securityElement">The configuration element from which the permission state is read.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="securityElement" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Security.SecurityElement.Tag" /> for the given <paramref name="securityElement" /> does not equal "IPermission".</exception>
      <exception cref="T:System.ArgumentException">The class attribute of the given <paramref name="securityElement " />equals null.</exception>
      <exception cref="T:System.ArgumentException">The class attribute of the given <paramref name="securityElement" /> is not the type name for <see cref="T:System.Configuration.ConfigurationPermission" />.</exception>
      <exception cref="T:System.ArgumentException">The version attribute for the given <paramref name="securityElement" /> does not equal 1.</exception>
      <exception cref="T:System.ArgumentException">The unrestricted attribute for the given <paramref name="securityElement" /> is neither true nor false.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationPermission.Intersect(System.Security.IPermission)">
      <summary>Returns the logical intersection between the <see cref="T:System.Configuration.ConfigurationPermission" /> object and a given object that implements the <see cref="T:System.Security.IPermission" /> interface.</summary>
      <returns>The logical intersection between the <see cref="T:System.Configuration.ConfigurationPermission" /> and a given object that implements <see cref="T:System.Security.IPermission" />.</returns>
      <param name="target">The object containing the permissions to perform the intersection with.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="target" /> is not typed as <see cref="T:System.Configuration.ConfigurationPermission" />.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationPermission.IsSubsetOf(System.Security.IPermission)">
      <summary>Compares the <see cref="T:System.Configuration.ConfigurationPermission" /> object with an object implementing the <see cref="T:System.Security.IPermission" /> interface.</summary>
      <returns>true if the permission state is equal; otherwise, false.</returns>
      <param name="target">The object to compare to.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="target" /> is not typed as <see cref="T:System.Configuration.ConfigurationPermission" />.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationPermission.IsUnrestricted">
      <summary>Indicates whether the permission state for the <see cref="T:System.Configuration.ConfigurationPermission" /> object is the <see cref="F:System.Security.Permissions.PermissionState.Unrestricted" /> value of the <see cref="T:System.Security.Permissions.PermissionState" /> enumeration.</summary>
      <returns>true if the permission state for the <see cref="T:System.Configuration.ConfigurationPermission" /> is the <see cref="F:System.Security.Permissions.PermissionState.Unrestricted" /> value of <see cref="T:System.Security.Permissions.PermissionState" />; otherwise, false.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationPermission.ToXml">
      <summary>Returns a <see cref="T:System.Security.SecurityElement" /> object with attribute values based on the current <see cref="T:System.Configuration.ConfigurationPermission" /> object.</summary>
      <returns>A <see cref="T:System.Security.SecurityElement" /> with attribute values based on the current <see cref="T:System.Configuration.ConfigurationPermission" />.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationPermission.Union(System.Security.IPermission)">
      <summary>Returns the logical union of the <see cref="T:System.Configuration.ConfigurationPermission" /> object and an object that implements the <see cref="T:System.Security.IPermission" /> interface.</summary>
      <returns>The logical union of the <see cref="T:System.Configuration.ConfigurationPermission" /> and an object that implements <see cref="T:System.Security.IPermission" />.</returns>
      <param name="target">The object to perform the union with.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="target" /> is not typed as <see cref="T:System.Configuration.ConfigurationPermission" />.</exception>
    </member>
    <member name="T:System.Configuration.ConfigurationPermissionAttribute">
      <summary>Creates a <see cref="T:System.Configuration.ConfigurationPermission" /> object that either grants or denies the marked target permission to access sections of configuration files.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationPermissionAttribute.#ctor(System.Security.Permissions.SecurityAction)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationPermissionAttribute" /> class.</summary>
      <param name="action">The security action represented by an enumeration member of <see cref="T:System.Security.Permissions.SecurityAction" />. Determines the permission state of the attribute.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationPermissionAttribute.CreatePermission">
      <summary>Creates and returns an object that implements the <see cref="T:System.Security.IPermission" /> interface.</summary>
      <returns>Returns an object that implements <see cref="T:System.Security.IPermission" />.</returns>
    </member>
    <member name="T:System.Configuration.ConfigurationProperty">
      <summary>Represents an attribute or a child of a configuration element. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationProperty.#ctor(System.String,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationProperty" /> class. </summary>
      <param name="name">The name of the configuration entity. </param>
      <param name="type">The type of the configuration entity. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationProperty.#ctor(System.String,System.Type,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationProperty" /> class. </summary>
      <param name="name">The name of the configuration entity. </param>
      <param name="type">The type of the configuration entity. </param>
      <param name="defaultValue">The default value of the configuration entity. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationProperty.#ctor(System.String,System.Type,System.Object,System.ComponentModel.TypeConverter,System.Configuration.ConfigurationValidatorBase,System.Configuration.ConfigurationPropertyOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationProperty" /> class. </summary>
      <param name="name">The name of the configuration entity. </param>
      <param name="type">The type of the configuration entity.</param>
      <param name="defaultValue">The default value of the configuration entity. </param>
      <param name="typeConverter">The type of the converter to apply.</param>
      <param name="validator">The validator to use. </param>
      <param name="options">One of the <see cref="T:System.Configuration.ConfigurationPropertyOptions" /> enumeration values. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationProperty.#ctor(System.String,System.Type,System.Object,System.ComponentModel.TypeConverter,System.Configuration.ConfigurationValidatorBase,System.Configuration.ConfigurationPropertyOptions,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationProperty" /> class. </summary>
      <param name="name">The name of the configuration entity. </param>
      <param name="type">The type of the configuration entity. </param>
      <param name="defaultValue">The default value of the configuration entity. </param>
      <param name="typeConverter">The type of the converter to apply.</param>
      <param name="validator">The validator to use. </param>
      <param name="options">One of the <see cref="T:System.Configuration.ConfigurationPropertyOptions" /> enumeration values. </param>
      <param name="description">The description of the configuration entity. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationProperty.#ctor(System.String,System.Type,System.Object,System.Configuration.ConfigurationPropertyOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationProperty" /> class. </summary>
      <param name="name">The name of the configuration entity. </param>
      <param name="type">The type of the configuration entity. </param>
      <param name="defaultValue">The default value of the configuration entity. </param>
      <param name="options">One of the <see cref="T:System.Configuration.ConfigurationPropertyOptions" /> enumeration values.</param>
    </member>
    <member name="P:System.Configuration.ConfigurationProperty.Converter">
      <summary>Gets the <see cref="T:System.ComponentModel.TypeConverter" /> used to convert this <see cref="T:System.Configuration.ConfigurationProperty" /> into an XML representation for writing to the configuration file.</summary>
      <returns>A <see cref="T:System.ComponentModel.TypeConverter" /> used to convert this <see cref="T:System.Configuration.ConfigurationProperty" /> into an XML representation for writing to the configuration file.</returns>
      <exception cref="T:System.Exception">This <see cref="T:System.Configuration.ConfigurationProperty" /> cannot be converted. </exception>
    </member>
    <member name="P:System.Configuration.ConfigurationProperty.DefaultValue">
      <summary>Gets the default value for this <see cref="T:System.Configuration.ConfigurationProperty" /> property.</summary>
      <returns>An <see cref="T:System.Object" /> that can be cast to the type specified by the <see cref="P:System.Configuration.ConfigurationProperty.Type" /> property.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationProperty.Description">
      <summary>Gets the description associated with the <see cref="T:System.Configuration.ConfigurationProperty" />.</summary>
      <returns>A string value that describes the property.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationProperty.IsAssemblyStringTransformationRequired">
      <summary>Indicates whether the assembly name for the configuration property requires transformation when it is serialized for an earlier version of the .NET Framework.</summary>
      <returns>true if the property requires assembly name transformation; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationProperty.IsDefaultCollection">
      <summary>Gets a value that indicates whether the property is the default collection of an element. </summary>
      <returns>true if the property is the default collection of an element; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationProperty.IsKey">
      <summary>Gets a value indicating whether this <see cref="T:System.Configuration.ConfigurationProperty" /> is the key for the containing <see cref="T:System.Configuration.ConfigurationElement" /> object.</summary>
      <returns>true if this <see cref="T:System.Configuration.ConfigurationProperty" /> object is the key for the containing element; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationProperty.IsRequired">
      <summary>Gets a value indicating whether this <see cref="T:System.Configuration.ConfigurationProperty" /> is required.</summary>
      <returns>true if the <see cref="T:System.Configuration.ConfigurationProperty" /> is required; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationProperty.IsTypeStringTransformationRequired">
      <summary>Indicates whether the type name for the configuration property requires transformation when it is serialized for an earlier version of the .NET Framework.</summary>
      <returns>true if the property requires type-name transformation; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationProperty.IsVersionCheckRequired">
      <summary>Indicates whether the configuration property's parent configuration section is queried at serialization time to determine whether the configuration property should be serialized into XML.</summary>
      <returns>true if the parent configuration section should be queried; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationProperty.Name">
      <summary>Gets the name of this <see cref="T:System.Configuration.ConfigurationProperty" />.</summary>
      <returns>The name of the <see cref="T:System.Configuration.ConfigurationProperty" />.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationProperty.Type">
      <summary>Gets the type of this <see cref="T:System.Configuration.ConfigurationProperty" /> object.</summary>
      <returns>A <see cref="T:System.Type" /> representing the type of this <see cref="T:System.Configuration.ConfigurationProperty" /> object.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationProperty.Validator">
      <summary>Gets the <see cref="T:System.Configuration.ConfigurationValidatorAttribute" />, which is used to validate this <see cref="T:System.Configuration.ConfigurationProperty" /> object.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationValidatorBase" /> validator, which is used to validate this <see cref="T:System.Configuration.ConfigurationProperty" />.</returns>
    </member>
    <member name="T:System.Configuration.ConfigurationPropertyAttribute">
      <summary>Declaratively instructs the .NET Framework to instantiate a configuration property. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationPropertyAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of <see cref="T:System.Configuration.ConfigurationPropertyAttribute" /> class.</summary>
      <param name="name">Name of the <see cref="T:System.Configuration.ConfigurationProperty" /> object defined.</param>
    </member>
    <member name="P:System.Configuration.ConfigurationPropertyAttribute.DefaultValue">
      <summary>Gets or sets the default value for the decorated property.</summary>
      <returns>The object representing the default value of the decorated configuration-element property.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationPropertyAttribute.IsDefaultCollection">
      <summary>Gets or sets a value indicating whether this is the default property collection for the decorated configuration property. </summary>
      <returns>true if the property represents the default collection of an element; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationPropertyAttribute.IsKey">
      <summary>Gets or sets a value indicating whether this is a key property for the decorated element property.</summary>
      <returns>true if the property is a key property for an element of the collection; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationPropertyAttribute.IsRequired">
      <summary>Gets or sets a value indicating whether the decorated element property is required.</summary>
      <returns>true if the property is required; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationPropertyAttribute.Name">
      <summary>Gets the name of the decorated configuration-element property.</summary>
      <returns>The name of the decorated configuration-element property.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationPropertyAttribute.Options">
      <summary>Gets or sets the <see cref="T:System.Configuration.ConfigurationPropertyOptions" /> for the decorated configuration-element property.</summary>
      <returns>One of the <see cref="T:System.Configuration.ConfigurationPropertyOptions" /> enumeration values associated with the property.</returns>
    </member>
    <member name="T:System.Configuration.ConfigurationPropertyCollection">
      <summary>Represents a collection of configuration-element properties.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationPropertyCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> class. </summary>
    </member>
    <member name="M:System.Configuration.ConfigurationPropertyCollection.Add(System.Configuration.ConfigurationProperty)">
      <summary>Adds a configuration property to the collection.</summary>
      <param name="property">The <see cref="T:System.Configuration.ConfigurationProperty" />  to add. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationPropertyCollection.Clear">
      <summary>Removes all configuration property objects from the collection.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationPropertyCollection.Contains(System.String)">
      <summary>Specifies whether the configuration property is contained in this collection.</summary>
      <returns>true if the specified <see cref="T:System.Configuration.ConfigurationProperty" /> is contained in the collection; otherwise, false.</returns>
      <param name="name">An identifier for the <see cref="T:System.Configuration.ConfigurationProperty" /> to verify. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationPropertyCollection.CopyTo(System.Configuration.ConfigurationProperty[],System.Int32)">
      <summary>Copies this ConfigurationPropertyCollection to an array.</summary>
      <param name="array">Array to which to copy.</param>
      <param name="index">Index at which to begin copying.</param>
    </member>
    <member name="P:System.Configuration.ConfigurationPropertyCollection.Count">
      <summary>Gets the number of properties in the collection.</summary>
      <returns>The number of properties in the collection.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationPropertyCollection.GetEnumerator">
      <summary>Gets the <see cref="T:System.Collections.IEnumerator" /> object as it applies to the collection.</summary>
      <returns>The <see cref="T:System.Collections.IEnumerator" /> object as it applies to the collection</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationPropertyCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> is synchronized; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationPropertyCollection.Item(System.String)">
      <summary>Gets the collection item with the specified name.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationProperty" /> with the specified <paramref name="name" />.</returns>
      <param name="name">The <see cref="T:System.Configuration.ConfigurationProperty" /> to return. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationPropertyCollection.Remove(System.String)">
      <summary>Removes a configuration property from the collection.</summary>
      <returns>true if the specified <see cref="T:System.Configuration.ConfigurationProperty" /> was removed; otherwise, false.</returns>
      <param name="name">The <see cref="T:System.Configuration.ConfigurationProperty" /> to remove. </param>
    </member>
    <member name="P:System.Configuration.ConfigurationPropertyCollection.SyncRoot">
      <summary>Gets the object to synchronize access to the collection.</summary>
      <returns>The object to synchronize access to the collection.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationPropertyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies this collection to an array.</summary>
      <param name="array">The array to which to copy.</param>
      <param name="index">The index location at which to begin copying.</param>
    </member>
    <member name="T:System.Configuration.ConfigurationPropertyOptions">
      <summary>Specifies the options to apply to a property.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationPropertyOptions.None">
      <summary>Indicates that no option applies to the property.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationPropertyOptions.IsDefaultCollection">
      <summary>Indicates that the property is a default collection. </summary>
    </member>
    <member name="F:System.Configuration.ConfigurationPropertyOptions.IsRequired">
      <summary>Indicates that the property is required. </summary>
    </member>
    <member name="F:System.Configuration.ConfigurationPropertyOptions.IsKey">
      <summary>Indicates that the property is a collection key.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationPropertyOptions.IsTypeStringTransformationRequired">
      <summary>Indicates whether the type name for the configuration property requires transformation when it is serialized for an earlier version of the .NET Framework.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationPropertyOptions.IsAssemblyStringTransformationRequired">
      <summary>Indicates whether the assembly name for the configuration property requires transformation when it is serialized for an earlier version of the .NET Framework.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationPropertyOptions.IsVersionCheckRequired">
      <summary>Indicates whether the configuration property's parent configuration section should be queried at serialization time to determine whether the configuration property should be serialized into XML.</summary>
    </member>
    <member name="T:System.Configuration.ConfigurationSaveMode">
      <summary>Determines which properties are written out to a configuration file.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationSaveMode.Modified">
      <summary>Causes only modified properties to be written to the configuration file, even when the value is the same as the inherited value.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationSaveMode.Minimal">
      <summary>Causes only properties that differ from inherited values to be written to the configuration file.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationSaveMode.Full">
      <summary>Causes all properties to be written to the configuration file. This is useful mostly for creating information configuration files or moving configuration values from one machine to another.</summary>
    </member>
    <member name="T:System.Configuration.ConfigurationSection">
      <summary>Represents a section within a configuration file.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationSection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationSection" /> class. </summary>
    </member>
    <member name="M:System.Configuration.ConfigurationSection.DeserializeSection(System.Xml.XmlReader)">
      <summary>Reads XML from the configuration file.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> object, which reads from the configuration file. </param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">
        <paramref name="reader" /> found no elements in the configuration file.</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationSection.GetRuntimeObject">
      <summary>Returns a custom object when overridden in a derived class.</summary>
      <returns>The object representing the section.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationSection.IsModified">
      <summary>Indicates whether this configuration element has been modified since it was last saved or loaded when implemented in a derived class.</summary>
      <returns>true if the element has been modified; otherwise, false. </returns>
    </member>
    <member name="M:System.Configuration.ConfigurationSection.ResetModified">
      <summary>Resets the value of the <see cref="M:System.Configuration.ConfigurationElement.IsModified" /> method to false when implemented in a derived class.</summary>
    </member>
    <member name="P:System.Configuration.ConfigurationSection.SectionInformation">
      <summary>Gets a <see cref="T:System.Configuration.SectionInformation" /> object that contains the non-customizable information and functionality of the <see cref="T:System.Configuration.ConfigurationSection" /> object. </summary>
      <returns>A <see cref="T:System.Configuration.SectionInformation" /> that contains the non-customizable information and functionality of the <see cref="T:System.Configuration.ConfigurationSection" />.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationSection.SerializeSection(System.Configuration.ConfigurationElement,System.String,System.Configuration.ConfigurationSaveMode)">
      <summary>Creates an XML string containing an unmerged view of the <see cref="T:System.Configuration.ConfigurationSection" /> object as a single section to write to a file.</summary>
      <returns>An XML string containing an unmerged view of the <see cref="T:System.Configuration.ConfigurationSection" /> object.</returns>
      <param name="parentElement">The <see cref="T:System.Configuration.ConfigurationElement" /> instance to use as the parent when performing the un-merge.</param>
      <param name="name">The name of the section to create.</param>
      <param name="saveMode">The <see cref="T:System.Configuration.ConfigurationSaveMode" /> instance to use when writing to a string.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationSection.ShouldSerializeElementInTargetVersion(System.Configuration.ConfigurationElement,System.String,System.Runtime.Versioning.FrameworkName)">
      <summary>Indicates whether the specified element should be serialized when the configuration object hierarchy is serialized for the specified target version of the .NET Framework.</summary>
      <returns>true if the <paramref name="element" /> should be serialized; otherwise, false.</returns>
      <param name="element">The <see cref="T:System.Configuration.ConfigurationElement" /> object that is a candidate for serialization.</param>
      <param name="elementName">The name of the <see cref="T:System.Configuration.ConfigurationElement" /> object as it occurs in XML.</param>
      <param name="targetFramework">The target version of the .NET Framework.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationSection.ShouldSerializePropertyInTargetVersion(System.Configuration.ConfigurationProperty,System.String,System.Runtime.Versioning.FrameworkName,System.Configuration.ConfigurationElement)">
      <summary>Indicates whether the specified property should be serialized when the configuration object hierarchy is serialized for the specified target version of the .NET Framework.</summary>
      <returns>true if the <paramref name="property" /> should be serialized; otherwise, false.</returns>
      <param name="property">The <see cref="T:System.Configuration.ConfigurationProperty" /> object that is a candidate for serialization.</param>
      <param name="propertyName">The name of the <see cref="T:System.Configuration.ConfigurationProperty" /> object as it occurs in XML.</param>
      <param name="targetFramework">The target version of the .NET Framework.</param>
      <param name="parentConfigurationElement">The parent element of the property.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationSection.ShouldSerializeSectionInTargetVersion(System.Runtime.Versioning.FrameworkName)">
      <summary>Indicates whether the current <see cref="T:System.Configuration.ConfigurationSection" /> instance should be serialized when the configuration object hierarchy is serialized for the specified target version of the .NET Framework.</summary>
      <returns>true if the current section should be serialized; otherwise, false.</returns>
      <param name="targetFramework">The target version of the .NET Framework.</param>
    </member>
    <member name="T:System.Configuration.ConfigurationSectionCollection">
      <summary>Represents a collection of related sections within a configuration file.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionCollection.Add(System.String,System.Configuration.ConfigurationSection)">
      <summary>Adds a <see cref="T:System.Configuration.ConfigurationSection" /> object to the <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object.</summary>
      <param name="name">The name of the section to be added.</param>
      <param name="section">The section to be added.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionCollection.Clear">
      <summary>Clears this <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionCollection.CopyTo(System.Configuration.ConfigurationSection[],System.Int32)">
      <summary>Copies this <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object to an array.</summary>
      <param name="array">The array to copy the <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object to.</param>
      <param name="index">The index location at which to begin copying.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The length of <paramref name="array" /> is less than the value of <see cref="P:System.Configuration.ConfigurationSectionCollection.Count" /> plus <paramref name="index" />.</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionCollection.Count">
      <summary>Gets the number of sections in this <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object.</summary>
      <returns>An integer that represents the number of sections in the collection.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionCollection.Get(System.Int32)">
      <summary>Gets the specified <see cref="T:System.Configuration.ConfigurationSection" /> object contained in this <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationSection" /> object at the specified index.</returns>
      <param name="index">The index of the <see cref="T:System.Configuration.ConfigurationSection" /> object to be returned.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionCollection.Get(System.String)">
      <summary>Gets the specified <see cref="T:System.Configuration.ConfigurationSection" /> object contained in this <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationSection" /> object with the specified name.</returns>
      <param name="name">The name of the <see cref="T:System.Configuration.ConfigurationSection" /> object to be returned.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is null or an empty string ("").</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionCollection.GetEnumerator">
      <summary>Gets an enumerator that can iterate through this <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through this <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionCollection.GetKey(System.Int32)">
      <summary>Gets the key of the specified <see cref="T:System.Configuration.ConfigurationSection" /> object contained in this <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object.</summary>
      <returns>The key of the <see cref="T:System.Configuration.ConfigurationSection" /> object at the specified index.</returns>
      <param name="index">The index of the <see cref="T:System.Configuration.ConfigurationSection" /> object whose key is to be returned. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionCollection.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Used by the system during serialization.</summary>
      <param name="info">The applicable <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object.</param>
      <param name="context">The applicable <see cref="T:System.Runtime.Serialization.StreamingContext" /> object.</param>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:System.Configuration.ConfigurationSection" /> object.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationSection" /> object at the specified index.</returns>
      <param name="index">The index of the <see cref="T:System.Configuration.ConfigurationSection" /> object to be returned. </param>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:System.Configuration.ConfigurationSection" /> object.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationSection" /> object with the specified name.</returns>
      <param name="name">The name of the <see cref="T:System.Configuration.ConfigurationSection" /> object to be returned. </param>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionCollection.Keys">
      <summary>Gets the keys to all <see cref="T:System.Configuration.ConfigurationSection" /> objects contained in this <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object.</summary>
      <returns>A <see cref="T:System.Collections.Specialized.NameObjectCollectionBase.KeysCollection" /> object that contains the keys of all sections in this collection.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionCollection.Remove(System.String)">
      <summary>Removes the specified <see cref="T:System.Configuration.ConfigurationSection" /> object from this <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object.</summary>
      <param name="name">The name of the section to be removed. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionCollection.RemoveAt(System.Int32)">
      <summary>Removes the specified <see cref="T:System.Configuration.ConfigurationSection" /> object from this <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object.</summary>
      <param name="index">The index of the section to be removed. </param>
    </member>
    <member name="T:System.Configuration.ConfigurationSectionGroup">
      <summary>Represents a group of related sections within a configuration file.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionGroup.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> class. </summary>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionGroup.ForceDeclaration">
      <summary>Forces the declaration for this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionGroup.ForceDeclaration(System.Boolean)">
      <summary>Forces the declaration for this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object.</summary>
      <param name="force">true if the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object must be written to the file; otherwise, false.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object is the root section group.- or -The <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object has a location.</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionGroup.IsDeclarationRequired">
      <summary>Gets a value that indicates whether this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object declaration is required. </summary>
      <returns>true if this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> declaration is required; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionGroup.IsDeclared">
      <summary>Gets a value that indicates whether this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object is declared.</summary>
      <returns>true if this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> is declared; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionGroup.Name">
      <summary>Gets the name property of this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object.</summary>
      <returns>The name property of this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionGroup.SectionGroupName">
      <summary>Gets the section group name associated with this <see cref="T:System.Configuration.ConfigurationSectionGroup" />.</summary>
      <returns>The section group name of this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionGroup.SectionGroups">
      <summary>Gets a <see cref="T:System.Configuration.ConfigurationSectionGroupCollection" /> object that contains all the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> objects that are children of this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object.</summary>
      <returns>A <see cref="T:System.Configuration.ConfigurationSectionGroupCollection" /> object that contains all the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> objects that are children of this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionGroup.Sections">
      <summary>Gets a <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object that contains all of <see cref="T:System.Configuration.ConfigurationSection" /> objects within this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object.</summary>
      <returns>A <see cref="T:System.Configuration.ConfigurationSectionCollection" /> object that contains all the <see cref="T:System.Configuration.ConfigurationSection" /> objects within this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionGroup.ShouldSerializeSectionGroupInTargetVersion(System.Runtime.Versioning.FrameworkName)">
      <summary>Indicates whether the current <see cref="T:System.Configuration.ConfigurationSectionGroup" /> instance should be serialized when the configuration object hierarchy is serialized for the specified target version of the .NET Framework.</summary>
      <returns>true if the current section group should be serialized; otherwise, false.</returns>
      <param name="targetFramework">The target version of the .NET Framework.</param>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionGroup.Type">
      <summary>Gets or sets the type for this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object.</summary>
      <returns>The type of this <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object is the root section group.- or -The <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object has a location.</exception>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The section or group is already defined at another level.</exception>
    </member>
    <member name="T:System.Configuration.ConfigurationSectionGroupCollection">
      <summary>Represents a collection of <see cref="T:System.Configuration.ConfigurationSectionGroup" /> objects.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionGroupCollection.Add(System.String,System.Configuration.ConfigurationSectionGroup)">
      <summary>Adds a <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object to this <see cref="T:System.Configuration.ConfigurationSectionGroupCollection" /> object.</summary>
      <param name="name">The name of the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object to be added.</param>
      <param name="sectionGroup">The <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object to be added.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionGroupCollection.Clear">
      <summary>Clears the collection.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionGroupCollection.CopyTo(System.Configuration.ConfigurationSectionGroup[],System.Int32)">
      <summary>Copies this <see cref="T:System.Configuration.ConfigurationSectionGroupCollection" /> object to an array.</summary>
      <param name="array">The array to copy the object to.</param>
      <param name="index">The index location at which to begin copying.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The length of <paramref name="array" /> is less than the value of <see cref="P:System.Configuration.ConfigurationSectionGroupCollection.Count" /> plus <paramref name="index" />.</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionGroupCollection.Count">
      <summary>Gets the number of section groups in the collection.</summary>
      <returns>An integer that represents the number of section groups in the collection.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionGroupCollection.Get(System.Int32)">
      <summary>Gets the specified <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object contained in the collection.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object at the specified index.</returns>
      <param name="index">The index of the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object to be returned. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionGroupCollection.Get(System.String)">
      <summary>Gets the specified <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object from the collection.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object with the specified name.</returns>
      <param name="name">The name of the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object to be returned. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is null or an empty string ("").</exception>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionGroupCollection.GetEnumerator">
      <summary>Gets an enumerator that can iterate through the <see cref="T:System.Configuration.ConfigurationSectionGroupCollection" /> object.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the <see cref="T:System.Configuration.ConfigurationSectionGroupCollection" /> object.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionGroupCollection.GetKey(System.Int32)">
      <summary>Gets the key of the specified <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object contained in this <see cref="T:System.Configuration.ConfigurationSectionGroupCollection" /> object.</summary>
      <returns>The key of the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object at the specified index.</returns>
      <param name="index">The index of the section group whose key is to be returned. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionGroupCollection.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Used by the system during serialization.</summary>
      <param name="info">The applicable <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object.</param>
      <param name="context">The applicable <see cref="T:System.Runtime.Serialization.StreamingContext" /> object.</param>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionGroupCollection.Item(System.Int32)">
      <summary>Gets the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object whose index is specified from the collection.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object at the specified index.In C#, this property is the indexer for the <see cref="T:System.Configuration.ConfigurationSectionCollection" /> class. </returns>
      <param name="index">The index of the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object to be returned. </param>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionGroupCollection.Item(System.String)">
      <summary>Gets the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object whose name is specified from the collection.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object with the specified name.In C#, this property is the indexer for the <see cref="T:System.Configuration.ConfigurationSectionCollection" /> class. </returns>
      <param name="name">The name of the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object to be returned. </param>
    </member>
    <member name="P:System.Configuration.ConfigurationSectionGroupCollection.Keys">
      <summary>Gets the keys to all <see cref="T:System.Configuration.ConfigurationSectionGroup" /> objects contained in this <see cref="T:System.Configuration.ConfigurationSectionGroupCollection" /> object.</summary>
      <returns>A <see cref="T:System.Collections.Specialized.NameObjectCollectionBase.KeysCollection" /> object that contains the names of all section groups in this collection.</returns>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionGroupCollection.Remove(System.String)">
      <summary>Removes the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object whose name is specified from this <see cref="T:System.Configuration.ConfigurationSectionGroupCollection" /> object.</summary>
      <param name="name">The name of the section group to be removed. </param>
    </member>
    <member name="M:System.Configuration.ConfigurationSectionGroupCollection.RemoveAt(System.Int32)">
      <summary>Removes the <see cref="T:System.Configuration.ConfigurationSectionGroup" /> object whose index is specified from this <see cref="T:System.Configuration.ConfigurationSectionGroupCollection" /> object.</summary>
      <param name="index">The index of the section group to be removed. </param>
    </member>
    <member name="T:System.Configuration.ConfigurationUserLevel">
      <summary>Used to specify which configuration file is to be represented by the Configuration object.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationUserLevel.None">
      <summary>Get the <see cref="T:System.Configuration.Configuration" /> that applies to all users.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationUserLevel.PerUserRoaming">
      <summary>Get the roaming <see cref="T:System.Configuration.Configuration" /> that applies to the current user.</summary>
    </member>
    <member name="F:System.Configuration.ConfigurationUserLevel.PerUserRoamingAndLocal">
      <summary>Get the local <see cref="T:System.Configuration.Configuration" /> that applies to the current user.</summary>
    </member>
    <member name="T:System.Configuration.ConfigurationValidatorAttribute">
      <summary>Serves as the base class for the <see cref="N:System.Configuration" /> validator attribute types.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationValidatorAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationValidatorAttribute" /> class.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationValidatorAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConfigurationValidatorAttribute" /> class using the specified validator type.</summary>
      <param name="validator">The validator type to use when creating an instance of <see cref="T:System.Configuration.ConfigurationValidatorAttribute" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="validator" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="validator" /> is not derived from <see cref="T:System.Configuration.ConfigurationValidatorBase" />.</exception>
    </member>
    <member name="P:System.Configuration.ConfigurationValidatorAttribute.ValidatorInstance">
      <summary>Gets the validator attribute instance.</summary>
      <returns>The current <see cref="T:System.Configuration.ConfigurationValidatorBase" />.</returns>
    </member>
    <member name="P:System.Configuration.ConfigurationValidatorAttribute.ValidatorType">
      <summary>Gets the type of the validator attribute.</summary>
      <returns>The <see cref="T:System.Type" /> of the current validator attribute instance.</returns>
    </member>
    <member name="T:System.Configuration.ConfigurationValidatorBase">
      <summary>Acts as a base class for deriving a validation class so that a value of an object can be verified.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationValidatorBase.#ctor">
      <summary>Initializes an instance of the <see cref="T:System.Configuration.ConfigurationValidatorBase" /> class.</summary>
    </member>
    <member name="M:System.Configuration.ConfigurationValidatorBase.CanValidate(System.Type)">
      <summary>Determines whether an object can be validated based on type.</summary>
      <returns>true if the <paramref name="type" /> parameter value matches the expected type; otherwise, false. </returns>
      <param name="type">The object type.</param>
    </member>
    <member name="M:System.Configuration.ConfigurationValidatorBase.Validate(System.Object)">
      <summary>Determines whether the value of an object is valid. </summary>
      <param name="value">The object value.</param>
    </member>
    <member name="T:System.Configuration.ConnectionStringSettings">
      <summary>Represents a single, named connection string in the connection strings configuration file section.</summary>
    </member>
    <member name="M:System.Configuration.ConnectionStringSettings.#ctor">
      <summary>Initializes a new instance of a <see cref="T:System.Configuration.ConnectionStringSettings" /> class.</summary>
    </member>
    <member name="M:System.Configuration.ConnectionStringSettings.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of a <see cref="T:System.Configuration.ConnectionStringSettings" /> class.</summary>
      <param name="name">The name of the connection string.</param>
      <param name="connectionString">The connection string.</param>
    </member>
    <member name="M:System.Configuration.ConnectionStringSettings.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of a <see cref="T:System.Configuration.ConnectionStringSettings" /> object.</summary>
      <param name="name">The name of the connection string.</param>
      <param name="connectionString">The connection string.</param>
      <param name="providerName">The name of the provider to use with the connection string.</param>
    </member>
    <member name="P:System.Configuration.ConnectionStringSettings.ConnectionString">
      <summary>Gets or sets the connection string.</summary>
      <returns>The string value assigned to the <see cref="P:System.Configuration.ConnectionStringSettings.ConnectionString" /> property.</returns>
    </member>
    <member name="P:System.Configuration.ConnectionStringSettings.Name">
      <summary>Gets or sets the <see cref="T:System.Configuration.ConnectionStringSettings" /> name.</summary>
      <returns>The string value assigned to the <see cref="P:System.Configuration.ConnectionStringSettings.Name" /> property.</returns>
    </member>
    <member name="P:System.Configuration.ConnectionStringSettings.ProviderName">
      <summary>Gets or sets the provider name property.</summary>
      <returns>Gets or sets the <see cref="P:System.Configuration.ConnectionStringSettings.ProviderName" /> property.</returns>
    </member>
    <member name="M:System.Configuration.ConnectionStringSettings.ToString">
      <summary>Returns a string representation of the object.</summary>
      <returns>A string representation of the object.</returns>
    </member>
    <member name="T:System.Configuration.ConnectionStringSettingsCollection">
      <summary>Contains a collection of <see cref="T:System.Configuration.ConnectionStringSettings" /> objects.</summary>
    </member>
    <member name="M:System.Configuration.ConnectionStringSettingsCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConnectionStringSettingsCollection" /> class. </summary>
    </member>
    <member name="M:System.Configuration.ConnectionStringSettingsCollection.Add(System.Configuration.ConnectionStringSettings)">
      <summary>Adds a <see cref="T:System.Configuration.ConnectionStringSettings" /> object to the collection.</summary>
      <param name="settings">A <see cref="T:System.Configuration.ConnectionStringSettings" /> object to add to the collection.</param>
    </member>
    <member name="M:System.Configuration.ConnectionStringSettingsCollection.Clear">
      <summary>Removes all the <see cref="T:System.Configuration.ConnectionStringSettings" /> objects from the collection.</summary>
    </member>
    <member name="M:System.Configuration.ConnectionStringSettingsCollection.IndexOf(System.Configuration.ConnectionStringSettings)">
      <summary>Returns the collection index of the passed <see cref="T:System.Configuration.ConnectionStringSettings" /> object.</summary>
      <returns>The collection index of the specified <see cref="T:System.Configuration.ConnectionStringSettingsCollection" /> object.</returns>
      <param name="settings">A <see cref="T:System.Configuration.ConnectionStringSettings" /> object in the collection.</param>
    </member>
    <member name="P:System.Configuration.ConnectionStringSettingsCollection.Item(System.Int32)">
      <summary>Gets or sets the connection string at the specified index in the collection.</summary>
      <returns>The <see cref="T:System.Configuration.ConnectionStringSettings" /> object at the specified index.</returns>
      <param name="index">The index of a <see cref="T:System.Configuration.ConnectionStringSettings" /> object in the collection.</param>
    </member>
    <member name="P:System.Configuration.ConnectionStringSettingsCollection.Item(System.String)">
      <summary>Gets or sets the <see cref="T:System.Configuration.ConnectionStringSettings" /> object with the specified name in the collection.</summary>
      <returns>The <see cref="T:System.Configuration.ConnectionStringSettings" /> object with the specified name; otherwise, null.</returns>
      <param name="name">The name of a <see cref="T:System.Configuration.ConnectionStringSettings" /> object in the collection.</param>
    </member>
    <member name="M:System.Configuration.ConnectionStringSettingsCollection.Remove(System.Configuration.ConnectionStringSettings)">
      <summary>Removes the specified <see cref="T:System.Configuration.ConnectionStringSettings" /> object from the collection.</summary>
      <param name="settings">A <see cref="T:System.Configuration.ConnectionStringSettings" /> object in the collection.</param>
    </member>
    <member name="M:System.Configuration.ConnectionStringSettingsCollection.Remove(System.String)">
      <summary>Removes the specified <see cref="T:System.Configuration.ConnectionStringSettings" /> object from the collection.</summary>
      <param name="name">The name of a <see cref="T:System.Configuration.ConnectionStringSettings" /> object in the collection.</param>
    </member>
    <member name="M:System.Configuration.ConnectionStringSettingsCollection.RemoveAt(System.Int32)">
      <summary>Removes the <see cref="T:System.Configuration.ConnectionStringSettings" /> object at the specified index in the collection.</summary>
      <param name="index">The index of a <see cref="T:System.Configuration.ConnectionStringSettings" /> object in the collection.</param>
    </member>
    <member name="T:System.Configuration.ConnectionStringsSection">
      <summary>Provides programmatic access to the connection strings configuration-file section. </summary>
    </member>
    <member name="M:System.Configuration.ConnectionStringsSection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ConnectionStringsSection" /> class.</summary>
    </member>
    <member name="P:System.Configuration.ConnectionStringsSection.ConnectionStrings">
      <summary>Gets a <see cref="T:System.Configuration.ConnectionStringSettingsCollection" /> collection of <see cref="T:System.Configuration.ConnectionStringSettings" /> objects.</summary>
      <returns>A <see cref="T:System.Configuration.ConnectionStringSettingsCollection" /> collection of <see cref="T:System.Configuration.ConnectionStringSettings" /> objects.</returns>
    </member>
    <member name="T:System.Configuration.ContextInformation">
      <summary>Encapsulates the context information that is associated with a <see cref="T:System.Configuration.ConfigurationElement" /> object. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.ContextInformation.GetSection(System.String)">
      <summary>Provides an object containing configuration-section information based on the specified section name.</summary>
      <returns>An object containing the specified section within the configuration.</returns>
      <param name="sectionName">The name of the configuration section.</param>
    </member>
    <member name="P:System.Configuration.ContextInformation.HostingContext">
      <summary>Gets the context of the environment where the configuration property is being evaluated.</summary>
      <returns>An object specifying the environment where the configuration property is being evaluated.</returns>
    </member>
    <member name="P:System.Configuration.ContextInformation.IsMachineLevel">
      <summary>Gets a value specifying whether the configuration property is being evaluated at the machine configuration level.</summary>
      <returns>true if the configuration property is being evaluated at the machine configuration level; otherwise, false.</returns>
    </member>
    <member name="T:System.Configuration.DefaultSection">
      <summary>Represents a basic configuration-section handler that exposes the configuration section's XML for both read and write access.</summary>
    </member>
    <member name="M:System.Configuration.DefaultSection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.DefaultSection" /> class. </summary>
    </member>
    <member name="T:System.Configuration.DefaultValidator">
      <summary>Provides validation of an object. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.DefaultValidator.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.DefaultValidator" /> class. </summary>
    </member>
    <member name="M:System.Configuration.DefaultValidator.CanValidate(System.Type)">
      <summary>Determines whether an object can be validated, based on type.</summary>
      <returns>true for all types being validated. </returns>
      <param name="type">The object type.</param>
    </member>
    <member name="M:System.Configuration.DefaultValidator.Validate(System.Object)">
      <summary>Determines whether the value of an object is valid. </summary>
      <param name="value">The object value.</param>
    </member>
    <member name="T:System.Configuration.DpapiProtectedConfigurationProvider">
      <summary>Provides a <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> object that uses the Windows data protection API (DPAPI) to encrypt and decrypt configuration data.</summary>
    </member>
    <member name="M:System.Configuration.DpapiProtectedConfigurationProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.DpapiProtectedConfigurationProvider" /> class using default settings.</summary>
    </member>
    <member name="M:System.Configuration.DpapiProtectedConfigurationProvider.Decrypt(System.Xml.XmlNode)">
      <summary>Decrypts the passed <see cref="T:System.Xml.XmlNode" /> object.</summary>
      <returns>A decrypted <see cref="T:System.Xml.XmlNode" /> object.</returns>
      <param name="encryptedNode">The <see cref="T:System.Xml.XmlNode" /> object to decrypt. </param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">
        <paramref name="encryptedNode" /> does not have <see cref="P:System.Xml.XmlNode.Name" /> set to "EncryptedData" and <see cref="T:System.Xml.XmlNodeType" /> set to <see cref="F:System.Xml.XmlNodeType.Element" />.- or -<paramref name="encryptedNode" /> does not have a child node named "CipherData" with a child node named "CipherValue".- or -The child node named "CipherData" is an empty node.</exception>
    </member>
    <member name="M:System.Configuration.DpapiProtectedConfigurationProvider.Encrypt(System.Xml.XmlNode)">
      <summary>Encrypts the passed <see cref="T:System.Xml.XmlNode" /> object.</summary>
      <returns>An encrypted <see cref="T:System.Xml.XmlNode" /> object.</returns>
      <param name="node">The <see cref="T:System.Xml.XmlNode" /> object to encrypt. </param>
    </member>
    <member name="M:System.Configuration.DpapiProtectedConfigurationProvider.Initialize(System.String,System.Collections.Specialized.NameValueCollection)">
      <summary>Initializes the provider with default settings.</summary>
      <param name="name">The provider name to use for the object.</param>
      <param name="configurationValues">A <see cref="T:System.Collections.Specialized.NameValueCollection" /> collection of values to use when initializing the object.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">
        <paramref name="configurationValues" /> contains an unrecognized configuration setting.</exception>
    </member>
    <member name="P:System.Configuration.DpapiProtectedConfigurationProvider.UseMachineProtection">
      <summary>Gets a value that indicates whether the <see cref="T:System.Configuration.DpapiProtectedConfigurationProvider" /> object is using machine-specific or user-account-specific protection.</summary>
      <returns>true if the <see cref="T:System.Configuration.DpapiProtectedConfigurationProvider" /> is using machine-specific protection; false if it is using user-account-specific protection.</returns>
    </member>
    <member name="T:System.Configuration.ElementInformation">
      <summary>Contains meta-information about an individual element within the configuration. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Configuration.ElementInformation.Errors">
      <summary>Gets the errors for the associated element and subelements</summary>
      <returns>The collection containing the errors for the associated element and subelements</returns>
    </member>
    <member name="P:System.Configuration.ElementInformation.IsCollection">
      <summary>Gets a value indicating whether the associated <see cref="T:System.Configuration.ConfigurationElement" /> object is a <see cref="T:System.Configuration.ConfigurationElementCollection" /> collection.</summary>
      <returns>true if the associated <see cref="T:System.Configuration.ConfigurationElement" /> object is a <see cref="T:System.Configuration.ConfigurationElementCollection" /> collection; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.ElementInformation.IsLocked">
      <summary>Gets a value that indicates whether the associated <see cref="T:System.Configuration.ConfigurationElement" /> object cannot be modified.</summary>
      <returns>true if the associated <see cref="T:System.Configuration.ConfigurationElement" /> object cannot be modified; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.ElementInformation.IsPresent">
      <summary>Gets a value indicating whether the associated <see cref="T:System.Configuration.ConfigurationElement" /> object is in the configuration file.</summary>
      <returns>true if the associated <see cref="T:System.Configuration.ConfigurationElement" /> object is in the configuration file; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.ElementInformation.LineNumber">
      <summary>Gets the line number in the configuration file where the associated <see cref="T:System.Configuration.ConfigurationElement" /> object is defined.</summary>
      <returns>The line number in the configuration file where the associated <see cref="T:System.Configuration.ConfigurationElement" /> object is defined.</returns>
    </member>
    <member name="P:System.Configuration.ElementInformation.Properties">
      <summary>Gets a <see cref="T:System.Configuration.PropertyInformationCollection" /> collection of the properties in the associated <see cref="T:System.Configuration.ConfigurationElement" /> object.</summary>
      <returns>A <see cref="T:System.Configuration.PropertyInformationCollection" /> collection of the properties in the associated <see cref="T:System.Configuration.ConfigurationElement" /> object.</returns>
    </member>
    <member name="P:System.Configuration.ElementInformation.Source">
      <summary>Gets the source file where the associated <see cref="T:System.Configuration.ConfigurationElement" /> object originated.</summary>
      <returns>The source file where the associated <see cref="T:System.Configuration.ConfigurationElement" /> object originated.</returns>
    </member>
    <member name="P:System.Configuration.ElementInformation.Type">
      <summary>Gets the type of the associated <see cref="T:System.Configuration.ConfigurationElement" /> object.</summary>
      <returns>The type of the associated <see cref="T:System.Configuration.ConfigurationElement" /> object.</returns>
    </member>
    <member name="P:System.Configuration.ElementInformation.Validator">
      <summary>Gets the object used to validate the associated <see cref="T:System.Configuration.ConfigurationElement" /> object.</summary>
      <returns>The object used to validate the associated <see cref="T:System.Configuration.ConfigurationElement" /> object.</returns>
    </member>
    <member name="T:System.Configuration.ExeConfigurationFileMap">
      <summary>Defines the configuration file mapping for an .exe application. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.ExeConfigurationFileMap.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ExeConfigurationFileMap" /> class.</summary>
    </member>
    <member name="M:System.Configuration.ExeConfigurationFileMap.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ExeConfigurationFileMap" /> class by using the specified machine configuration file name.</summary>
      <param name="machineConfigFileName">The name of the machine configuration file that includes the complete physical path (for example, c:\Windows\Microsoft.NET\Framework\v2.0.50727\CONFIG\machine.config).</param>
    </member>
    <member name="M:System.Configuration.ExeConfigurationFileMap.Clone">
      <summary>Creates a copy of the existing <see cref="T:System.Configuration.ExeConfigurationFileMap" /> object.</summary>
      <returns>An <see cref="T:System.Configuration.ExeConfigurationFileMap" /> object.</returns>
    </member>
    <member name="P:System.Configuration.ExeConfigurationFileMap.ExeConfigFilename">
      <summary>Gets or sets the name of the configuration file.</summary>
      <returns>The configuration file name.</returns>
    </member>
    <member name="P:System.Configuration.ExeConfigurationFileMap.LocalUserConfigFilename">
      <summary>Gets or sets the name of the configuration file for the local user.</summary>
      <returns>The configuration file name.</returns>
    </member>
    <member name="P:System.Configuration.ExeConfigurationFileMap.RoamingUserConfigFilename">
      <summary>Gets or sets the name of the configuration file for the roaming user.</summary>
      <returns>The configuration file name.</returns>
    </member>
    <member name="T:System.Configuration.ExeContext">
      <summary>Manages the path context for the current application. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Configuration.ExeContext.ExePath">
      <summary>Gets the current path for the application.</summary>
      <returns>A string value containing the current path.</returns>
    </member>
    <member name="P:System.Configuration.ExeContext.UserLevel">
      <summary>Gets an object representing the path level of the current application.</summary>
      <returns>A <see cref="T:System.Configuration.ConfigurationUserLevel" /> object representing the path level of the current application.</returns>
    </member>
    <member name="T:System.Configuration.GenericEnumConverter">
      <summary>Converts between a string and an enumeration type. </summary>
    </member>
    <member name="M:System.Configuration.GenericEnumConverter.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.GenericEnumConverter" /> class.</summary>
      <param name="typeEnum">The enumeration type to convert.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeEnum" /> is null.</exception>
    </member>
    <member name="M:System.Configuration.GenericEnumConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts a <see cref="T:System.String" /> to an <see cref="T:System.Enum" /> type.</summary>
      <returns>The <see cref="T:System.Enum" /> type that represents the <paramref name="data" /> parameter.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="data">The <see cref="T:System.String" /> object to convert.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="data" /> is null or an empty string ("").- or -<paramref name="data" /> starts with a numeric character.- or -<paramref name="data" /> includes white space.</exception>
    </member>
    <member name="M:System.Configuration.GenericEnumConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts an <see cref="T:System.Enum" /> type to a <see cref="T:System.String" /> value.</summary>
      <returns>The <see cref="T:System.String" /> that represents the <paramref name="value" /> parameter.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="value">The value to convert to.</param>
      <param name="type">The type to convert to.</param>
    </member>
    <member name="T:System.Configuration.IgnoreSection">
      <summary>Provides a wrapper type definition for configuration sections that are not handled by the <see cref="N:System.Configuration" /> types.</summary>
    </member>
    <member name="M:System.Configuration.IgnoreSection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.IgnoreSection" /> class.</summary>
    </member>
    <member name="T:System.Configuration.InfiniteIntConverter">
      <summary>Converts between a string and the standard infinite or integer value.</summary>
    </member>
    <member name="M:System.Configuration.InfiniteIntConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.InfiniteIntConverter" /> class.</summary>
    </member>
    <member name="M:System.Configuration.InfiniteIntConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts a <see cref="T:System.String" /> to an <see cref="T:System.Int32" />.</summary>
      <returns>The <see cref="F:System.Int32.MaxValue" />, if the <paramref name="data" /> parameter is the <see cref="T:System.String" /> "infinite"; otherwise, the <see cref="T:System.Int32" /> representing the <paramref name="data" /> parameter integer value.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="data">The <see cref="T:System.String" /> object to convert.</param>
    </member>
    <member name="M:System.Configuration.InfiniteIntConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts an <see cref="T:System.Int32" />.to a <see cref="T:System.String" />.</summary>
      <returns>The <see cref="T:System.String" /> "infinite" if the <paramref name="value" /> is <see cref="F:System.Int32.MaxValue" />; otherwise, the <see cref="T:System.String" /> representing the <paramref name="value" /> parameter.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="value">The value to convert to.</param>
      <param name="type">The type to convert to.</param>
    </member>
    <member name="T:System.Configuration.InfiniteTimeSpanConverter">
      <summary>Converts between a string and the standard infinite <see cref="T:System.TimeSpan" /> value.</summary>
    </member>
    <member name="M:System.Configuration.InfiniteTimeSpanConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.InfiniteTimeSpanConverter" /> class.</summary>
    </member>
    <member name="M:System.Configuration.InfiniteTimeSpanConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts a <see cref="T:System.String" /> to a <see cref="T:System.TimeSpan" />.</summary>
      <returns>The <see cref="F:System.TimeSpan.MaxValue" />, if the <paramref name="data" /> parameter is the <see cref="T:System.String" /> infinite; otherwise, the <see cref="T:System.TimeSpan" /> representing the <paramref name="data" /> parameter in minutes.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="data">The <see cref="T:System.String" /> object to convert.</param>
    </member>
    <member name="M:System.Configuration.InfiniteTimeSpanConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts a <see cref="T:System.TimeSpan" /> to a <see cref="T:System.String" />.</summary>
      <returns>The <see cref="T:System.String" /> "infinite", if the <paramref name="value" /> parameter is <see cref="F:System.TimeSpan.MaxValue" />; otherwise, the <see cref="T:System.String" /> representing the <paramref name="value" /> parameter in minutes.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> used during object conversion.</param>
      <param name="value">The value to convert.</param>
      <param name="type">The conversion type.</param>
    </member>
    <member name="T:System.Configuration.IntegerValidator">
      <summary>Provides validation of an <see cref="T:System.Int32" /> value.</summary>
    </member>
    <member name="M:System.Configuration.IntegerValidator.#ctor(System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.IntegerValidator" /> class. </summary>
      <param name="minValue">An <see cref="T:System.Int32" /> object that specifies the minimum value.</param>
      <param name="maxValue">An <see cref="T:System.Int32" /> object that specifies the maximum value.</param>
    </member>
    <member name="M:System.Configuration.IntegerValidator.#ctor(System.Int32,System.Int32,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.IntegerValidator" /> class. </summary>
      <param name="minValue">An <see cref="T:System.Int32" /> object that specifies the minimum value.</param>
      <param name="maxValue">An <see cref="T:System.Int32" /> object that specifies the maximum value.</param>
      <param name="rangeIsExclusive">true to specify that the validation range is exclusive. Inclusive means the value to be validated must be within the specified range; exclusive means that it must be below the minimum or above the maximum.</param>
    </member>
    <member name="M:System.Configuration.IntegerValidator.#ctor(System.Int32,System.Int32,System.Boolean,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.IntegerValidator" /> class. </summary>
      <param name="minValue">An <see cref="T:System.Int32" /> object that specifies the minimum length of the integer value.</param>
      <param name="maxValue">An <see cref="T:System.Int32" /> object that specifies the maximum length of the integer value.</param>
      <param name="rangeIsExclusive">A <see cref="T:System.Boolean" /> value that specifies whether the validation range is exclusive.</param>
      <param name="resolution">An <see cref="T:System.Int32" /> object that specifies a value that must be matched.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="resolution" /> is less than 0.- or -<paramref name="minValue" /> is greater than <paramref name="maxValue" />.</exception>
    </member>
    <member name="M:System.Configuration.IntegerValidator.CanValidate(System.Type)">
      <summary>Determines whether the type of the object can be validated.</summary>
      <returns>true if the <paramref name="type" /> parameter matches an <see cref="T:System.Int32" /> value; otherwise, false. </returns>
      <param name="type">The type of the object.</param>
    </member>
    <member name="M:System.Configuration.IntegerValidator.Validate(System.Object)">
      <summary>Determines whether the value of an object is valid.</summary>
      <param name="value">The value to be validated.</param>
    </member>
    <member name="T:System.Configuration.IntegerValidatorAttribute">
      <summary>Declaratively instructs the .NET Framework to perform integer validation on a configuration property. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.IntegerValidatorAttribute.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Configuration.IntegerValidatorAttribute" /> class.</summary>
    </member>
    <member name="P:System.Configuration.IntegerValidatorAttribute.ExcludeRange">
      <summary>Gets or sets a value that indicates whether to include or exclude the integers in the range defined by the <see cref="P:System.Configuration.IntegerValidatorAttribute.MinValue" /> and <see cref="P:System.Configuration.IntegerValidatorAttribute.MaxValue" /> property values.</summary>
      <returns>true if the value must be excluded; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Configuration.IntegerValidatorAttribute.MaxValue">
      <summary>Gets or sets the maximum value allowed for the property.</summary>
      <returns>An integer that indicates the allowed maximum value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The selected value is less than <see cref="P:System.Configuration.IntegerValidatorAttribute.MinValue" />.</exception>
    </member>
    <member name="P:System.Configuration.IntegerValidatorAttribute.MinValue">
      <summary>Gets or sets the minimum value allowed for the property.</summary>
      <returns>An integer that indicates the allowed minimum value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The selected value is greater than <see cref="P:System.Configuration.IntegerValidatorAttribute.MaxValue" />.</exception>
    </member>
    <member name="P:System.Configuration.IntegerValidatorAttribute.ValidatorInstance">
      <summary>Gets an instance of the <see cref="T:System.Configuration.IntegerValidator" /> class.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationValidatorBase" /> validator instance.</returns>
    </member>
    <member name="T:System.Configuration.KeyValueConfigurationCollection">
      <summary>Contains a collection of <see cref="T:System.Configuration.KeyValueConfigurationElement" /> objects. </summary>
    </member>
    <member name="M:System.Configuration.KeyValueConfigurationCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.KeyValueConfigurationCollection" /> class.</summary>
    </member>
    <member name="M:System.Configuration.KeyValueConfigurationCollection.Add(System.Configuration.KeyValueConfigurationElement)">
      <summary>Adds a <see cref="T:System.Configuration.KeyValueConfigurationElement" /> object to the collection based on the supplied parameters.</summary>
      <param name="keyValue">A <see cref="T:System.Configuration.KeyValueConfigurationElement" />.</param>
    </member>
    <member name="M:System.Configuration.KeyValueConfigurationCollection.Add(System.String,System.String)">
      <summary>Adds a <see cref="T:System.Configuration.KeyValueConfigurationElement" /> object to the collection based on the supplied parameters.</summary>
      <param name="key">A string specifying the key.</param>
      <param name="value">A string specifying the value.</param>
    </member>
    <member name="P:System.Configuration.KeyValueConfigurationCollection.AllKeys">
      <summary>Gets the keys to all items contained in the <see cref="T:System.Configuration.KeyValueConfigurationCollection" /> collection.</summary>
      <returns>A string array.</returns>
    </member>
    <member name="M:System.Configuration.KeyValueConfigurationCollection.Clear">
      <summary>Clears the <see cref="T:System.Configuration.KeyValueConfigurationCollection" /> collection.</summary>
    </member>
    <member name="M:System.Configuration.KeyValueConfigurationCollection.CreateNewElement">
      <summary>When overridden in a derived class, the <see cref="M:System.Configuration.KeyValueConfigurationCollection.CreateNewElement" /> method creates a new <see cref="T:System.Configuration.KeyValueConfigurationElement" /> object.</summary>
      <returns>A new <see cref="T:System.Configuration.KeyValueConfigurationElement" />.</returns>
    </member>
    <member name="M:System.Configuration.KeyValueConfigurationCollection.GetElementKey(System.Configuration.ConfigurationElement)">
      <summary>Gets the element key for a specified configuration element when overridden in a derived class.</summary>
      <returns>An object that acts as the key for the specified <see cref="T:System.Configuration.KeyValueConfigurationElement" />.</returns>
      <param name="element">The <see cref="T:System.Configuration.KeyValueConfigurationElement" /> to which the key should be returned.</param>
    </member>
    <member name="P:System.Configuration.KeyValueConfigurationCollection.Item(System.String)">
      <summary>Gets the <see cref="T:System.Configuration.KeyValueConfigurationElement" /> object based on the supplied parameter.</summary>
      <returns>A configuration element, or null if the key does not exist in the collection.</returns>
      <param name="key">The key of the <see cref="T:System.Configuration.KeyValueConfigurationElement" /> contained in the collection.</param>
    </member>
    <member name="P:System.Configuration.KeyValueConfigurationCollection.Properties">
      <summary>Gets a collection of configuration properties.</summary>
      <returns>A collection of configuration properties.</returns>
    </member>
    <member name="M:System.Configuration.KeyValueConfigurationCollection.Remove(System.String)">
      <summary>Removes a <see cref="T:System.Configuration.KeyValueConfigurationElement" /> object from the collection.</summary>
      <param name="key">A string specifying the <paramref name="key" />.</param>
    </member>
    <member name="P:System.Configuration.KeyValueConfigurationCollection.ThrowOnDuplicate">
      <summary>Gets a value indicating whether an attempt to add a duplicate <see cref="T:System.Configuration.KeyValueConfigurationElement" /> object to the <see cref="T:System.Configuration.KeyValueConfigurationCollection" /> collection will cause an exception to be thrown.</summary>
      <returns>true if an attempt to add a duplicate <see cref="T:System.Configuration.KeyValueConfigurationElement" /> to the <see cref="T:System.Configuration.KeyValueConfigurationCollection" /> will cause an exception to be thrown; otherwise, false. </returns>
    </member>
    <member name="T:System.Configuration.KeyValueConfigurationElement">
      <summary>Represents a configuration element that contains a key/value pair. </summary>
    </member>
    <member name="M:System.Configuration.KeyValueConfigurationElement.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.KeyValueConfigurationElement" /> class based on the supplied parameters.</summary>
      <param name="key">The key of the <see cref="T:System.Configuration.KeyValueConfigurationElement" />.</param>
      <param name="value">The value of the <see cref="T:System.Configuration.KeyValueConfigurationElement" />.</param>
    </member>
    <member name="M:System.Configuration.KeyValueConfigurationElement.Init">
      <summary>Sets the <see cref="T:System.Configuration.KeyValueConfigurationElement" /> object to its initial state.</summary>
    </member>
    <member name="P:System.Configuration.KeyValueConfigurationElement.Key">
      <summary>Gets the key of the <see cref="T:System.Configuration.KeyValueConfigurationElement" /> object.</summary>
      <returns>The key of the <see cref="T:System.Configuration.KeyValueConfigurationElement" />.</returns>
    </member>
    <member name="P:System.Configuration.KeyValueConfigurationElement.Properties">
      <summary>Gets the collection of properties. </summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> of properties for the element.</returns>
    </member>
    <member name="P:System.Configuration.KeyValueConfigurationElement.Value">
      <summary>Gets or sets the value of the <see cref="T:System.Configuration.KeyValueConfigurationElement" /> object.</summary>
      <returns>The value of the <see cref="T:System.Configuration.KeyValueConfigurationElement" />.</returns>
    </member>
    <member name="T:System.Configuration.LongValidator">
      <summary>Provides validation of an <see cref="T:System.Int64" /> value.</summary>
    </member>
    <member name="M:System.Configuration.LongValidator.#ctor(System.Int64,System.Int64)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.LongValidator" /> class. </summary>
      <param name="minValue">An <see cref="T:System.Int64" /> value that specifies the minimum length of the long value.</param>
      <param name="maxValue">An <see cref="T:System.Int64" /> value that specifies the maximum length of the long value.</param>
    </member>
    <member name="M:System.Configuration.LongValidator.#ctor(System.Int64,System.Int64,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.LongValidator" /> class. </summary>
      <param name="minValue">An <see cref="T:System.Int64" /> value that specifies the minimum length of the long value.</param>
      <param name="maxValue">An <see cref="T:System.Int64" /> value that specifies the maximum length of the long value.</param>
      <param name="rangeIsExclusive">A <see cref="T:System.Boolean" /> value that specifies whether the validation range is exclusive.</param>
    </member>
    <member name="M:System.Configuration.LongValidator.#ctor(System.Int64,System.Int64,System.Boolean,System.Int64)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.LongValidator" /> class. </summary>
      <param name="minValue">An <see cref="T:System.Int64" /> value that specifies the minimum length of the long value.</param>
      <param name="maxValue">An <see cref="T:System.Int64" /> value that specifies the maximum length of the long value.</param>
      <param name="rangeIsExclusive">A <see cref="T:System.Boolean" /> value that specifies whether the validation range is exclusive.</param>
      <param name="resolution">An <see cref="T:System.Int64" /> value that specifies a specific value that must be matched.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="resolution" /> is equal to or less than 0.- or -<paramref name="maxValue" /> is less than <paramref name="minValue" />.</exception>
    </member>
    <member name="M:System.Configuration.LongValidator.CanValidate(System.Type)">
      <summary>Determines whether the type of the object can be validated.</summary>
      <returns>true if the <paramref name="type" /> parameter matches an <see cref="T:System.Int64" /> value; otherwise, false. </returns>
      <param name="type">The type of object.</param>
    </member>
    <member name="M:System.Configuration.LongValidator.Validate(System.Object)">
      <summary>Determines whether the value of an object is valid.</summary>
      <param name="value">The value of an object.</param>
    </member>
    <member name="T:System.Configuration.LongValidatorAttribute">
      <summary>Declaratively instructs the .NET Framework to perform long-integer validation on a configuration property. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.LongValidatorAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.LongValidatorAttribute" /> class.</summary>
    </member>
    <member name="P:System.Configuration.LongValidatorAttribute.ExcludeRange">
      <summary>Gets or sets a value that indicates whether to include or exclude the integers in the range defined by the <see cref="P:System.Configuration.LongValidatorAttribute.MinValue" /> and <see cref="P:System.Configuration.LongValidatorAttribute.MaxValue" /> property values.</summary>
      <returns>true if the value must be excluded; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Configuration.LongValidatorAttribute.MaxValue">
      <summary>Gets or sets the maximum value allowed for the property.</summary>
      <returns>A long integer that indicates the allowed maximum value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The selected value is less than <see cref="P:System.Configuration.LongValidatorAttribute.MinValue" />.</exception>
    </member>
    <member name="P:System.Configuration.LongValidatorAttribute.MinValue">
      <summary>Gets or sets the minimum value allowed for the property.</summary>
      <returns>An integer that indicates the allowed minimum value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The selected value is greater than <see cref="P:System.Configuration.LongValidatorAttribute.MaxValue" />.</exception>
    </member>
    <member name="P:System.Configuration.LongValidatorAttribute.ValidatorInstance">
      <summary>Gets an instance of the <see cref="T:System.Configuration.LongValidator" /> class.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationValidatorBase" /> validator instance.</returns>
    </member>
    <member name="T:System.Configuration.NameValueConfigurationCollection">
      <summary>Contains a collection of <see cref="T:System.Configuration.NameValueConfigurationElement" /> objects. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.NameValueConfigurationCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.NameValueConfigurationCollection" /> class.</summary>
    </member>
    <member name="M:System.Configuration.NameValueConfigurationCollection.Add(System.Configuration.NameValueConfigurationElement)">
      <summary>Adds a <see cref="T:System.Configuration.NameValueConfigurationElement" /> object to the collection.</summary>
      <param name="nameValue">A  <see cref="T:System.Configuration.NameValueConfigurationElement" /> object.</param>
    </member>
    <member name="P:System.Configuration.NameValueConfigurationCollection.AllKeys">
      <summary>Gets the keys to all items contained in the <see cref="T:System.Configuration.NameValueConfigurationCollection" />.</summary>
      <returns>A string array.</returns>
    </member>
    <member name="M:System.Configuration.NameValueConfigurationCollection.Clear">
      <summary>Clears the <see cref="T:System.Configuration.NameValueConfigurationCollection" />.</summary>
    </member>
    <member name="P:System.Configuration.NameValueConfigurationCollection.Item(System.String)">
      <summary>Gets or sets the <see cref="T:System.Configuration.NameValueConfigurationElement" /> object based on the supplied parameter.</summary>
      <returns>A <see cref="T:System.Configuration.NameValueConfigurationElement" /> object.</returns>
      <param name="name">The name of the <see cref="T:System.Configuration.NameValueConfigurationElement" /> contained in the collection.</param>
    </member>
    <member name="M:System.Configuration.NameValueConfigurationCollection.Remove(System.Configuration.NameValueConfigurationElement)">
      <summary>Removes a <see cref="T:System.Configuration.NameValueConfigurationElement" /> object from the collection based on the provided parameter.</summary>
      <param name="nameValue">A <see cref="T:System.Configuration.NameValueConfigurationElement" /> object.</param>
    </member>
    <member name="M:System.Configuration.NameValueConfigurationCollection.Remove(System.String)">
      <summary>Removes a <see cref="T:System.Configuration.NameValueConfigurationElement" /> object from the collection based on the provided parameter.</summary>
      <param name="name">The name of the <see cref="T:System.Configuration.NameValueConfigurationElement" /> object.</param>
    </member>
    <member name="T:System.Configuration.NameValueConfigurationElement">
      <summary>A configuration element that contains a <see cref="T:System.String" /> name and <see cref="T:System.String" /> value. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.NameValueConfigurationElement.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.NameValueConfigurationElement" /> class based on supplied parameters.</summary>
      <param name="name">The name of the <see cref="T:System.Configuration.NameValueConfigurationElement" /> object.</param>
      <param name="value">The value of the <see cref="T:System.Configuration.NameValueConfigurationElement" /> object.</param>
    </member>
    <member name="P:System.Configuration.NameValueConfigurationElement.Name">
      <summary>Gets the name of the <see cref="T:System.Configuration.NameValueConfigurationElement" /> object.</summary>
      <returns>The name of the <see cref="T:System.Configuration.NameValueConfigurationElement" /> object.</returns>
    </member>
    <member name="P:System.Configuration.NameValueConfigurationElement.Value">
      <summary>Gets or sets the value of the <see cref="T:System.Configuration.NameValueConfigurationElement" /> object.</summary>
      <returns>The value of the <see cref="T:System.Configuration.NameValueConfigurationElement" /> object.</returns>
    </member>
    <member name="T:System.Configuration.OverrideMode">
      <summary>Specifies the override behavior of a configuration element for configuration elements in child directories.</summary>
    </member>
    <member name="F:System.Configuration.OverrideMode.Inherit">
      <summary>The configuration setting of the element or group will be overridden by configuration settings that are in child directories if explicitly allowed by a parent element of the current configuration element or group. Permission to override is specified by using the OverrideMode attribute.</summary>
    </member>
    <member name="F:System.Configuration.OverrideMode.Allow">
      <summary>The configuration setting of the element or group can be overridden by configuration settings that are in child directories.</summary>
    </member>
    <member name="F:System.Configuration.OverrideMode.Deny">
      <summary>The configuration setting of the element or group cannot be overridden by configuration settings that are in child directories.</summary>
    </member>
    <member name="T:System.Configuration.PositiveTimeSpanValidator">
      <summary>Provides validation of a <see cref="T:System.TimeSpan" /> object. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.PositiveTimeSpanValidator.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.PositiveTimeSpanValidator" /> class. </summary>
    </member>
    <member name="M:System.Configuration.PositiveTimeSpanValidator.CanValidate(System.Type)">
      <summary>Determines whether the object type can be validated.</summary>
      <returns>true if the <paramref name="type" /> parameter matches a <see cref="T:System.TimeSpan" /> object; otherwise, false. </returns>
      <param name="type">The object type.</param>
    </member>
    <member name="M:System.Configuration.PositiveTimeSpanValidator.Validate(System.Object)">
      <summary>Determines whether the value of an object is valid.</summary>
      <param name="value">The value of an object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> cannot be resolved as a positive <see cref="T:System.TimeSpan" /> value.</exception>
    </member>
    <member name="T:System.Configuration.PositiveTimeSpanValidatorAttribute">
      <summary>Declaratively instructs the .NET Framework to perform time validation on a configuration property. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.PositiveTimeSpanValidatorAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.PositiveTimeSpanValidatorAttribute" /> class. </summary>
    </member>
    <member name="P:System.Configuration.PositiveTimeSpanValidatorAttribute.ValidatorInstance">
      <summary>Gets an instance of the <see cref="T:System.Configuration.PositiveTimeSpanValidator" /> class.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationValidatorBase" /> validator instance. </returns>
    </member>
    <member name="T:System.Configuration.PropertyInformation">
      <summary>Contains meta-information on an individual property within the configuration. This type cannot be inherited.</summary>
    </member>
    <member name="P:System.Configuration.PropertyInformation.Converter">
      <summary>Gets the <see cref="T:System.ComponentModel.TypeConverter" /> object related to the configuration attribute.</summary>
      <returns>A <see cref="T:System.ComponentModel.TypeConverter" /> object.</returns>
    </member>
    <member name="P:System.Configuration.PropertyInformation.DefaultValue">
      <summary>Gets an object containing the default value related to a configuration attribute.</summary>
      <returns>An object containing the default value of the configuration attribute.</returns>
    </member>
    <member name="P:System.Configuration.PropertyInformation.Description">
      <summary>Gets the description of the object that corresponds to a configuration attribute.</summary>
      <returns>The description of the configuration attribute.</returns>
    </member>
    <member name="P:System.Configuration.PropertyInformation.IsKey">
      <summary>Gets a value specifying whether the configuration attribute is a key.</summary>
      <returns>true if the configuration attribute is a key; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.PropertyInformation.IsLocked">
      <summary>Gets a value specifying whether the configuration attribute is locked.</summary>
      <returns>true if the <see cref="T:System.Configuration.PropertyInformation" /> object is locked; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.PropertyInformation.IsModified">
      <summary>Gets a value specifying whether the configuration attribute has been modified.</summary>
      <returns>true if the <see cref="T:System.Configuration.PropertyInformation" /> object has been modified; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.PropertyInformation.IsRequired">
      <summary>Gets a value specifying whether the configuration attribute is required.</summary>
      <returns>true if the <see cref="T:System.Configuration.PropertyInformation" /> object is required; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.PropertyInformation.LineNumber">
      <summary>Gets the line number in the configuration file related to the configuration attribute.</summary>
      <returns>A line number of the configuration file.</returns>
    </member>
    <member name="P:System.Configuration.PropertyInformation.Name">
      <summary>Gets the name of the object that corresponds to a configuration attribute.</summary>
      <returns>The name of the <see cref="T:System.Configuration.PropertyInformation" /> object.</returns>
    </member>
    <member name="P:System.Configuration.PropertyInformation.Source">
      <summary>Gets the source file that corresponds to a configuration attribute.</summary>
      <returns>The source file of the <see cref="T:System.Configuration.PropertyInformation" /> object.</returns>
    </member>
    <member name="P:System.Configuration.PropertyInformation.Type">
      <summary>Gets the <see cref="T:System.Type" /> of the object that corresponds to a configuration attribute.</summary>
      <returns>The <see cref="T:System.Type" /> of the <see cref="T:System.Configuration.PropertyInformation" /> object.</returns>
    </member>
    <member name="P:System.Configuration.PropertyInformation.Validator">
      <summary>Gets a <see cref="T:System.Configuration.ConfigurationValidatorBase" /> object related to the configuration attribute.</summary>
      <returns>A <see cref="T:System.Configuration.ConfigurationValidatorBase" /> object.</returns>
    </member>
    <member name="P:System.Configuration.PropertyInformation.Value">
      <summary>Gets or sets an object containing the value related to a configuration attribute.</summary>
      <returns>An object containing the value for the <see cref="T:System.Configuration.PropertyInformation" /> object.</returns>
    </member>
    <member name="P:System.Configuration.PropertyInformation.ValueOrigin">
      <summary>Gets a <see cref="T:System.Configuration.PropertyValueOrigin" /> object related to the configuration attribute. </summary>
      <returns>A <see cref="T:System.Configuration.PropertyValueOrigin" /> object.</returns>
    </member>
    <member name="T:System.Configuration.PropertyInformationCollection">
      <summary>Contains a collection of <see cref="T:System.Configuration.PropertyInformation" /> objects. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.PropertyInformationCollection.CopyTo(System.Configuration.PropertyInformation[],System.Int32)">
      <summary>Copies the entire <see cref="T:System.Configuration.PropertyInformationCollection" /> collection to a compatible one-dimensional <see cref="T:System.Array" />, starting at the specified index of the target array.</summary>
      <param name="array">A one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from the <see cref="T:System.Configuration.PropertyInformationCollection" /> collection. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <see cref="P:System.Array.Length" /> property of <paramref name="array" /> is less than <see cref="P:System.Collections.Specialized.NameObjectCollectionBase.Count" /> + <paramref name="index" />.</exception>
    </member>
    <member name="M:System.Configuration.PropertyInformationCollection.GetEnumerator">
      <summary>Gets an <see cref="T:System.Collections.IEnumerator" /> object, which is used to iterate through this <see cref="T:System.Configuration.PropertyInformationCollection" /> collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object, which is used to iterate through this <see cref="T:System.Configuration.PropertyInformationCollection" />.</returns>
    </member>
    <member name="M:System.Configuration.PropertyInformationCollection.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with the data needed to serialize the <see cref="T:System.Configuration.PropertyInformationCollection" /> instance.</summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object that contains the information required to serialize the <see cref="T:System.Configuration.PropertyInformationCollection" /> instance.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> object that contains the source and destination of the serialized stream associated with the <see cref="T:System.Configuration.PropertyInformationCollection" /> instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is null.</exception>
    </member>
    <member name="P:System.Configuration.PropertyInformationCollection.Item(System.String)">
      <summary>Gets the <see cref="T:System.Configuration.PropertyInformation" /> object in the collection, based on the specified property name.</summary>
      <returns>A <see cref="T:System.Configuration.PropertyInformation" /> object.</returns>
      <param name="propertyName">The name of the configuration attribute contained in the <see cref="T:System.Configuration.PropertyInformationCollection" />object.</param>
    </member>
    <member name="T:System.Configuration.PropertyValueOrigin">
      <summary>Specifies the level in the configuration hierarchy where a configuration property value originated.</summary>
    </member>
    <member name="F:System.Configuration.PropertyValueOrigin.Default">
      <summary>The configuration property value originates from the <see cref="P:System.Configuration.ConfigurationProperty.DefaultValue" /> property.</summary>
    </member>
    <member name="F:System.Configuration.PropertyValueOrigin.Inherited">
      <summary>The configuration property value is inherited from a parent level in the configuration.</summary>
    </member>
    <member name="F:System.Configuration.PropertyValueOrigin.SetHere">
      <summary>The configuration property value is defined at the current level of the hierarchy.</summary>
    </member>
    <member name="T:System.Configuration.ProtectedConfiguration">
      <summary>Provides access to the protected-configuration providers for the current application's configuration file. </summary>
    </member>
    <member name="F:System.Configuration.ProtectedConfiguration.DataProtectionProviderName">
      <summary>The name of the data protection provider.</summary>
    </member>
    <member name="P:System.Configuration.ProtectedConfiguration.DefaultProvider">
      <summary>Gets the name of the default protected-configuration provider.</summary>
      <returns>The name of the default protected-configuration provider.</returns>
    </member>
    <member name="F:System.Configuration.ProtectedConfiguration.ProtectedDataSectionName">
      <summary>The name of the protected data section.</summary>
    </member>
    <member name="P:System.Configuration.ProtectedConfiguration.Providers">
      <summary>Gets a collection of the installed protected-configuration providers.</summary>
      <returns>A <see cref="T:System.Configuration.ProtectedConfigurationProviderCollection" /> collection of installed <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> objects.</returns>
    </member>
    <member name="F:System.Configuration.ProtectedConfiguration.RsaProviderName">
      <summary>The name of the RSA provider.</summary>
    </member>
    <member name="T:System.Configuration.ProtectedConfigurationProvider">
      <summary>Is the base class to create providers for encrypting and decrypting protected-configuration data.</summary>
    </member>
    <member name="M:System.Configuration.ProtectedConfigurationProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> class using default settings.</summary>
    </member>
    <member name="M:System.Configuration.ProtectedConfigurationProvider.Decrypt(System.Xml.XmlNode)">
      <summary>Decrypts the passed <see cref="T:System.Xml.XmlNode" /> object from a configuration file.</summary>
      <returns>The <see cref="T:System.Xml.XmlNode" /> object containing decrypted data.</returns>
      <param name="encryptedNode">The <see cref="T:System.Xml.XmlNode" /> object to decrypt.</param>
    </member>
    <member name="M:System.Configuration.ProtectedConfigurationProvider.Encrypt(System.Xml.XmlNode)">
      <summary>Encrypts the passed <see cref="T:System.Xml.XmlNode" /> object from a configuration file.</summary>
      <returns>The <see cref="T:System.Xml.XmlNode" /> object containing encrypted data.</returns>
      <param name="node">The <see cref="T:System.Xml.XmlNode" /> object to encrypt.</param>
    </member>
    <member name="T:System.Configuration.ProtectedConfigurationProviderCollection">
      <summary>Provides a collection of <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> objects.</summary>
    </member>
    <member name="M:System.Configuration.ProtectedConfigurationProviderCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ProtectedConfigurationProviderCollection" /> class using default settings.</summary>
    </member>
    <member name="M:System.Configuration.ProtectedConfigurationProviderCollection.Add(System.Configuration.Provider.ProviderBase)">
      <summary>Adds a <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> object to the collection.</summary>
      <param name="provider">A <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> object to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="provider" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="provider" /> is not a <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> object.</exception>
      <exception cref="T:System.Configuration.ConfigurationException">The <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> object to add already exists in the collection.- or -The collection is read-only.</exception>
    </member>
    <member name="P:System.Configuration.ProtectedConfigurationProviderCollection.Item(System.String)">
      <summary>Gets a <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> object in the collection with the specified name.</summary>
      <returns>The <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> object with the specified name, or null if there is no object with that name.</returns>
      <param name="name">The name of a <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> object in the collection.</param>
    </member>
    <member name="T:System.Configuration.ProtectedConfigurationSection">
      <summary>Provides programmatic access to the configProtectedData configuration section. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.ProtectedConfigurationSection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ProtectedConfigurationSection" /> class using default settings.</summary>
    </member>
    <member name="P:System.Configuration.ProtectedConfigurationSection.DefaultProvider">
      <summary>Gets or sets the name of the default <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> object in the <see cref="P:System.Configuration.ProtectedConfigurationSection.Providers" /> collection property.</summary>
      <returns>The name of the default <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> object in the <see cref="P:System.Configuration.ProtectedConfigurationSection.Providers" /> collection property. </returns>
    </member>
    <member name="P:System.Configuration.ProtectedConfigurationSection.Providers">
      <summary>Gets a <see cref="T:System.Configuration.ProviderSettingsCollection" /> collection of all the <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> objects in all participating configuration files.</summary>
      <returns>A <see cref="T:System.Configuration.ProviderSettingsCollection" /> collection of all the <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> objects in all participating configuration files. </returns>
    </member>
    <member name="T:System.Configuration.ProtectedProviderSettings">
      <summary>Represents a group of configuration elements that configure the providers for the &lt;configProtectedData&gt; configuration section.</summary>
    </member>
    <member name="M:System.Configuration.ProtectedProviderSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ProtectedProviderSettings" /> class. </summary>
    </member>
    <member name="P:System.Configuration.ProtectedProviderSettings.Properties">
      <summary>Gets a <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> collection that represents the properties of the providers for the protected configuration data.</summary>
      <returns>A <see cref="T:System.Configuration.ConfigurationPropertyCollection" /> that represents the properties of the providers for the protected configuration data.</returns>
    </member>
    <member name="P:System.Configuration.ProtectedProviderSettings.Providers">
      <summary>Gets a collection of <see cref="T:System.Configuration.ProviderSettings" /> objects that represent the properties of the providers for the protected configuration data.</summary>
      <returns>A collection of <see cref="T:System.Configuration.ProviderSettings" /> objects that represent the properties of the providers for the protected configuration data.</returns>
    </member>
    <member name="T:System.Configuration.ProviderSettings">
      <summary>Represents the configuration elements associated with a provider.</summary>
    </member>
    <member name="M:System.Configuration.ProviderSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ProviderSettings" /> class. </summary>
    </member>
    <member name="M:System.Configuration.ProviderSettings.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ProviderSettings" /> class. </summary>
      <param name="name">The name of the provider to configure settings for.</param>
      <param name="type">The type of the provider to configure settings for.</param>
    </member>
    <member name="P:System.Configuration.ProviderSettings.Name">
      <summary>Gets or sets the name of the provider configured by this class.</summary>
      <returns>The name of the provider.</returns>
    </member>
    <member name="P:System.Configuration.ProviderSettings.Parameters">
      <summary>Gets a collection of user-defined parameters for the provider.</summary>
      <returns>A <see cref="T:System.Collections.Specialized.NameValueCollection" /> of parameters for the provider.</returns>
    </member>
    <member name="P:System.Configuration.ProviderSettings.Type">
      <summary>Gets or sets the type of the provider configured by this class.</summary>
      <returns>The fully qualified namespace and class name for the type of provider configured by this <see cref="T:System.Configuration.ProviderSettings" /> instance.</returns>
    </member>
    <member name="T:System.Configuration.ProviderSettingsCollection">
      <summary>Represents a collection of <see cref="T:System.Configuration.ProviderSettings" /> objects.</summary>
    </member>
    <member name="M:System.Configuration.ProviderSettingsCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.ProviderSettingsCollection" /> class. </summary>
    </member>
    <member name="M:System.Configuration.ProviderSettingsCollection.Add(System.Configuration.ProviderSettings)">
      <summary>Adds a <see cref="T:System.Configuration.ProviderSettings" /> object to the collection.</summary>
      <param name="provider">The <see cref="T:System.Configuration.ProviderSettings" /> object to add.</param>
    </member>
    <member name="M:System.Configuration.ProviderSettingsCollection.Clear">
      <summary>Clears the collection.</summary>
    </member>
    <member name="P:System.Configuration.ProviderSettingsCollection.Item(System.Int32)">
      <summary>Gets or sets a value at the specified index in the <see cref="T:System.Configuration.ProviderSettingsCollection" /> collection.</summary>
      <returns>The specified <see cref="T:System.Configuration.ProviderSettings" />.</returns>
      <param name="index">The index of the <see cref="T:System.Configuration.ProviderSettings" /> to return.</param>
    </member>
    <member name="P:System.Configuration.ProviderSettingsCollection.Item(System.String)">
      <summary>Gets an item from the collection. </summary>
      <returns>A <see cref="T:System.Configuration.ProviderSettings" /> object contained in the collection.</returns>
      <param name="key">A string reference to the <see cref="T:System.Configuration.ProviderSettings" /> object within the collection.</param>
    </member>
    <member name="M:System.Configuration.ProviderSettingsCollection.Remove(System.String)">
      <summary>Removes an element from the collection.</summary>
      <param name="name">The name of the <see cref="T:System.Configuration.ProviderSettings" /> object to remove.</param>
    </member>
    <member name="T:System.Configuration.RegexStringValidator">
      <summary>Provides validation of a string based on the rules provided by a regular expression.</summary>
    </member>
    <member name="M:System.Configuration.RegexStringValidator.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.RegexStringValidator" /> class. </summary>
      <param name="regex">A string that specifies a regular expression.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="regex" /> is null or an empty string ("").</exception>
    </member>
    <member name="M:System.Configuration.RegexStringValidator.CanValidate(System.Type)">
      <summary>Determines whether the type of the object can be validated.</summary>
      <returns>true if the <paramref name="type" /> parameter matches a string; otherwise, false. </returns>
      <param name="type">The type of object.</param>
    </member>
    <member name="M:System.Configuration.RegexStringValidator.Validate(System.Object)">
      <summary>Determines whether the value of an object is valid.</summary>
      <param name="value">The value of an object.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> does not conform to the parameters of the <see cref="T:System.Text.RegularExpressions.Regex" /> class.</exception>
    </member>
    <member name="T:System.Configuration.RegexStringValidatorAttribute">
      <summary>Declaratively instructs the .NET Framework to perform string validation on a configuration property using a regular expression. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.RegexStringValidatorAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.RegexStringValidatorAttribute" /> object.</summary>
      <param name="regex">The string to use for regular expression validation.</param>
    </member>
    <member name="P:System.Configuration.RegexStringValidatorAttribute.Regex">
      <summary>Gets the string used to perform regular-expression validation.</summary>
      <returns>The string containing the regular expression used to filter the string assigned to the decorated configuration-element property.</returns>
    </member>
    <member name="P:System.Configuration.RegexStringValidatorAttribute.ValidatorInstance">
      <summary>Gets an instance of the <see cref="T:System.Configuration.RegexStringValidator" /> class.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationValidatorBase" /> validator instance.</returns>
    </member>
    <member name="T:System.Configuration.RsaProtectedConfigurationProvider">
      <summary>Provides a <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> instance that uses RSA encryption to encrypt and decrypt configuration data.</summary>
    </member>
    <member name="M:System.Configuration.RsaProtectedConfigurationProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.RsaProtectedConfigurationProvider" /> class. </summary>
    </member>
    <member name="M:System.Configuration.RsaProtectedConfigurationProvider.AddKey(System.Int32,System.Boolean)">
      <summary>Adds a key to the RSA key container.</summary>
      <param name="keySize">The size of the key to add.</param>
      <param name="exportable">true to indicate that the key is exportable; otherwise, false.</param>
    </member>
    <member name="P:System.Configuration.RsaProtectedConfigurationProvider.CspProviderName">
      <summary>Gets the name of the Windows cryptography API (crypto API) cryptographic service provider (CSP).</summary>
      <returns>The name of the CryptoAPI cryptographic service provider.</returns>
    </member>
    <member name="M:System.Configuration.RsaProtectedConfigurationProvider.Decrypt(System.Xml.XmlNode)">
      <summary>Decrypts the XML node passed to it.</summary>
      <returns>The <see cref="T:System.Xml.XmlNode" /> object containing decrypted data.</returns>
      <param name="encryptedNode">The <see cref="T:System.Xml.XmlNode" /> to decrypt.</param>
    </member>
    <member name="M:System.Configuration.RsaProtectedConfigurationProvider.DeleteKey">
      <summary>Removes a key from the RSA key container.</summary>
    </member>
    <member name="M:System.Configuration.RsaProtectedConfigurationProvider.Encrypt(System.Xml.XmlNode)">
      <summary>Encrypts the XML node passed to it.</summary>
      <returns>An encrypted <see cref="T:System.Xml.XmlNode" /> object.</returns>
      <param name="node">The <see cref="T:System.Xml.XmlNode" /> to encrypt.</param>
    </member>
    <member name="M:System.Configuration.RsaProtectedConfigurationProvider.ExportKey(System.String,System.Boolean)">
      <summary>Exports an RSA key from the key container.</summary>
      <param name="xmlFileName">The file name and path to export the key to.</param>
      <param name="includePrivateParameters">true to indicate that private parameters are exported; otherwise, false.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">An error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.-or- This operation is not supported on the current platform.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
    </member>
    <member name="M:System.Configuration.RsaProtectedConfigurationProvider.ImportKey(System.String,System.Boolean)">
      <summary>Imports an RSA key into the key container.</summary>
      <param name="xmlFileName">The file name and path to import the key from.</param>
      <param name="exportable">true to indicate that the key is exportable; otherwise, false.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">An error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is write-only.-or- This operation is not supported on the current platform.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
    </member>
    <member name="M:System.Configuration.RsaProtectedConfigurationProvider.Initialize(System.String,System.Collections.Specialized.NameValueCollection)">
      <summary>Initializes the provider with default settings.</summary>
      <param name="name">The provider name to use for the object.</param>
      <param name="configurationValues">A <see cref="T:System.Collections.Specialized.NameValueCollection" /> collection of values to use when initializing the object.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">
        <paramref name="configurationValues" /> includes one or more unrecognized values.</exception>
    </member>
    <member name="P:System.Configuration.RsaProtectedConfigurationProvider.KeyContainerName">
      <summary>Gets the name of the key container.</summary>
      <returns>The name of the key container.</returns>
    </member>
    <member name="P:System.Configuration.RsaProtectedConfigurationProvider.RsaPublicKey">
      <summary>Gets the public key used by the provider.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.RSAParameters" /> object that contains the public key used by the provider.</returns>
    </member>
    <member name="P:System.Configuration.RsaProtectedConfigurationProvider.UseMachineContainer">
      <summary>Gets a value that indicates whether the <see cref="T:System.Configuration.RsaProtectedConfigurationProvider" /> object is using the machine key container.</summary>
      <returns>true if the <see cref="T:System.Configuration.RsaProtectedConfigurationProvider" /> object is using the machine key container; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.RsaProtectedConfigurationProvider.UseOAEP">
      <summary>Gets a value that indicates whether the provider is using Optimal Asymmetric Encryption Padding (OAEP) key exchange data.</summary>
      <returns>true if the <see cref="T:System.Configuration.RsaProtectedConfigurationProvider" /> object is using Optimal Asymmetric Encryption Padding (OAEP) key exchange data; otherwise, false.</returns>
    </member>
    <member name="T:System.Configuration.SectionInformation">
      <summary>Contains metadata about an individual section within the configuration hierarchy. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Configuration.SectionInformation.AllowDefinition">
      <summary>Gets or sets a value that indicates where in the configuration file hierarchy the associated configuration section can be defined. </summary>
      <returns>A value that indicates where in the configuration file hierarchy the associated <see cref="T:System.Configuration.ConfigurationSection" /> object can be declared.</returns>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The selected value conflicts with a value that is already defined.</exception>
    </member>
    <member name="P:System.Configuration.SectionInformation.AllowExeDefinition">
      <summary>Gets or sets a value that indicates where in the configuration file hierarchy the associated configuration section can be declared.</summary>
      <returns>A value that indicates where in the configuration file hierarchy the associated <see cref="T:System.Configuration.ConfigurationSection" /> object can be declared for .exe files.</returns>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The selected value conflicts with a value that is already defined.</exception>
    </member>
    <member name="P:System.Configuration.SectionInformation.AllowLocation">
      <summary>Gets or sets a value that indicates whether the configuration section allows the location attribute.</summary>
      <returns>true if the location attribute is allowed; otherwise, false. The default is true.</returns>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The selected value conflicts with a value that is already defined.</exception>
    </member>
    <member name="P:System.Configuration.SectionInformation.AllowOverride">
      <summary>Gets or sets a value that indicates whether the associated configuration section can be overridden by lower-level configuration files.</summary>
      <returns>true if the section can be overridden; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Configuration.SectionInformation.ConfigSource">
      <summary>Gets or sets the name of the include file in which the associated configuration section is defined, if such a file exists.</summary>
      <returns>The name of the include file in which the associated <see cref="T:System.Configuration.ConfigurationSection" /> is defined, if such a file exists; otherwise, an empty string ("").</returns>
    </member>
    <member name="M:System.Configuration.SectionInformation.ForceDeclaration">
      <summary>Forces the associated configuration section to appear in the configuration file.</summary>
    </member>
    <member name="M:System.Configuration.SectionInformation.ForceDeclaration(System.Boolean)">
      <summary>Forces the associated configuration section to appear in the configuration file, or removes an existing section from the configuration file.</summary>
      <param name="force">true if the associated section should be written in the configuration file; otherwise, false.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">
        <paramref name="force" /> is true and the associated section cannot be exported to the child configuration file, or it is undeclared.</exception>
    </member>
    <member name="P:System.Configuration.SectionInformation.ForceSave">
      <summary>Gets or sets a value that indicates whether the associated configuration section will be saved even if it has not been modified.</summary>
      <returns>true if the associated <see cref="T:System.Configuration.ConfigurationSection" /> object will be saved even if it has not been modified; otherwise, false. The default is false.NoteIf the configuration file is saved (even if there are no modifications), ASP.NET restarts the application.</returns>
    </member>
    <member name="M:System.Configuration.SectionInformation.GetParentSection">
      <summary>Gets the configuration section that contains the configuration section associated with this object.</summary>
      <returns>The configuration section that contains the <see cref="T:System.Configuration.ConfigurationSection" /> that is associated with this <see cref="T:System.Configuration.SectionInformation" /> object.</returns>
      <exception cref="T:System.InvalidOperationException">The method is invoked from a parent section.</exception>
    </member>
    <member name="M:System.Configuration.SectionInformation.GetRawXml">
      <summary>Returns an XML node object that represents the associated configuration-section object.</summary>
      <returns>The XML representation for this configuration section.</returns>
      <exception cref="T:System.InvalidOperationException">This configuration object is locked and cannot be edited.</exception>
    </member>
    <member name="P:System.Configuration.SectionInformation.InheritInChildApplications">
      <summary>Gets or sets a value that indicates whether the settings that are specified in the associated configuration section are inherited by applications that reside in a subdirectory of the relevant application.</summary>
      <returns>true if the settings specified in this <see cref="T:System.Configuration.ConfigurationSection" /> object are inherited by child applications; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.Configuration.SectionInformation.IsDeclarationRequired">
      <summary>Gets a value that indicates whether the configuration section must be declared in the configuration file.</summary>
      <returns>true if the associated <see cref="T:System.Configuration.ConfigurationSection" /> object must be declared in the configuration file; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.SectionInformation.IsDeclared">
      <summary>Gets a value that indicates whether the associated configuration section is declared in the configuration file.</summary>
      <returns>true if this <see cref="T:System.Configuration.ConfigurationSection" /> is declared in the configuration file; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.Configuration.SectionInformation.IsLocked">
      <summary>Gets a value that indicates whether the associated configuration section is locked.</summary>
      <returns>true if the section is locked; otherwise, false. </returns>
    </member>
    <member name="P:System.Configuration.SectionInformation.IsProtected">
      <summary>Gets a value that indicates whether the associated configuration section is protected.</summary>
      <returns>true if this <see cref="T:System.Configuration.ConfigurationSection" /> is protected; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Configuration.SectionInformation.Name">
      <summary>Gets the name of the associated configuration section.</summary>
      <returns>The complete name of the configuration section.</returns>
    </member>
    <member name="P:System.Configuration.SectionInformation.OverrideMode">
      <summary>Gets or sets the <see cref="T:System.Configuration.OverrideMode" /> enumeration value that specifies whether the associated configuration section can be overridden by child configuration files.</summary>
      <returns>One of the <see cref="T:System.Configuration.OverrideMode" /> enumeration values.</returns>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">An attempt was made to change both the <see cref="P:System.Configuration.SectionInformation.AllowOverride" /> and <see cref="P:System.Configuration.SectionInformation.OverrideMode" /> properties, which is not supported for compatibility reasons. </exception>
    </member>
    <member name="P:System.Configuration.SectionInformation.OverrideModeDefault">
      <summary>Gets or sets a value that specifies the default override behavior of a configuration section by child configuration files.</summary>
      <returns>One of the <see cref="T:System.Configuration.OverrideMode" /> enumeration values.</returns>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The override behavior is specified in a parent configuration section.</exception>
    </member>
    <member name="P:System.Configuration.SectionInformation.OverrideModeEffective">
      <summary>Gets the override behavior of a configuration section that is in turn based on whether child configuration files can lock the configuration section. </summary>
      <returns>One of the <see cref="T:System.Configuration.OverrideMode" /> enumeration values.</returns>
    </member>
    <member name="P:System.Configuration.SectionInformation.ProtectionProvider">
      <summary>Gets the protected configuration provider for the associated configuration section.</summary>
      <returns>The protected configuration provider for this <see cref="T:System.Configuration.ConfigurationSection" /> object.</returns>
    </member>
    <member name="M:System.Configuration.SectionInformation.ProtectSection(System.String)">
      <summary>Marks a configuration section for protection. </summary>
      <param name="protectionProvider">The name of the protection provider to use.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Configuration.SectionInformation.AllowLocation" /> property is set to false.- or -The target section is already a protected data section.</exception>
    </member>
    <member name="P:System.Configuration.SectionInformation.RequirePermission">
      <summary>Gets a value that indicates whether the associated configuration section requires access permissions.</summary>
      <returns>true if the requirePermission attribute is set to true; otherwise, false. The default is true.</returns>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The selected value conflicts with a value that is already defined.</exception>
    </member>
    <member name="P:System.Configuration.SectionInformation.RestartOnExternalChanges">
      <summary>Gets or sets a value that specifies whether a change in an external configuration include file requires an application restart.</summary>
      <returns>true if a change in an external configuration include file requires an application restart; otherwise, false. The default is true.</returns>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The selected value conflicts with a value that is already defined.</exception>
    </member>
    <member name="M:System.Configuration.SectionInformation.RevertToParent">
      <summary>Causes the associated configuration section to inherit all its values from the parent section.</summary>
      <exception cref="T:System.InvalidOperationException">This method cannot be called outside editing mode.</exception>
    </member>
    <member name="P:System.Configuration.SectionInformation.SectionName">
      <summary>Gets the name of the associated configuration section.</summary>
      <returns>The name of the associated <see cref="T:System.Configuration.ConfigurationSection" /> object.</returns>
    </member>
    <member name="M:System.Configuration.SectionInformation.SetRawXml(System.String)">
      <summary>Sets the object to an XML representation of the associated configuration section within the configuration file.</summary>
      <param name="rawXml">The XML to use.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawXml" /> is null.</exception>
    </member>
    <member name="P:System.Configuration.SectionInformation.Type">
      <summary>Gets or sets the section class name.</summary>
      <returns>The name of the class that is associated with this <see cref="T:System.Configuration.ConfigurationSection" /> section.</returns>
      <exception cref="T:System.ArgumentException">The selected value is null or an empty string ("").</exception>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The selected value conflicts with a value that is already defined.</exception>
    </member>
    <member name="M:System.Configuration.SectionInformation.UnprotectSection">
      <summary>Removes the protected configuration encryption from the associated configuration section.</summary>
    </member>
    <member name="T:System.Configuration.StringValidator">
      <summary>Provides validation of a string.</summary>
    </member>
    <member name="M:System.Configuration.StringValidator.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.StringValidator" /> class, based on a supplied parameter.</summary>
      <param name="minLength">An integer that specifies the minimum length of the string value.</param>
    </member>
    <member name="M:System.Configuration.StringValidator.#ctor(System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.StringValidator" /> class, based on supplied parameters.</summary>
      <param name="minLength">An integer that specifies the minimum length of the string value.</param>
      <param name="maxLength">An integer that specifies the maximum length of the string value.</param>
    </member>
    <member name="M:System.Configuration.StringValidator.#ctor(System.Int32,System.Int32,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.StringValidator" /> class, based on supplied parameters.</summary>
      <param name="minLength">An integer that specifies the minimum length of the string value.</param>
      <param name="maxLength">An integer that specifies the maximum length of the string value.</param>
      <param name="invalidCharacters">A string that represents invalid characters. </param>
    </member>
    <member name="M:System.Configuration.StringValidator.CanValidate(System.Type)">
      <summary>Determines whether an object can be validated based on type.</summary>
      <returns>true if the <paramref name="type" /> parameter matches a string; otherwise, false. </returns>
      <param name="type">The object type.</param>
    </member>
    <member name="M:System.Configuration.StringValidator.Validate(System.Object)">
      <summary>Determines whether the value of an object is valid. </summary>
      <param name="value">The object value.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is less than <paramref name="minValue" /> or greater than <paramref name="maxValue" /> as defined in the constructor.- or -<paramref name="value" /> contains invalid characters.</exception>
    </member>
    <member name="T:System.Configuration.StringValidatorAttribute">
      <summary>Declaratively instructs the .NET Framework to perform string validation on a configuration property. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.StringValidatorAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.StringValidatorAttribute" /> class.</summary>
    </member>
    <member name="P:System.Configuration.StringValidatorAttribute.InvalidCharacters">
      <summary>Gets or sets the invalid characters for the property.</summary>
      <returns>The string that contains the set of characters that are not allowed for the property.</returns>
    </member>
    <member name="P:System.Configuration.StringValidatorAttribute.MaxLength">
      <summary>Gets or sets the maximum length allowed for the string to assign to the property.</summary>
      <returns>An integer that indicates the maximum allowed length for the string to assign to the property.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The selected value is less than <see cref="P:System.Configuration.StringValidatorAttribute.MinLength" />.</exception>
    </member>
    <member name="P:System.Configuration.StringValidatorAttribute.MinLength">
      <summary>Gets or sets the minimum allowed value for the string to assign to the property.</summary>
      <returns>An integer that indicates the allowed minimum length for the string to assign to the property.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The selected value is greater than <see cref="P:System.Configuration.StringValidatorAttribute.MaxLength" />.</exception>
    </member>
    <member name="P:System.Configuration.StringValidatorAttribute.ValidatorInstance">
      <summary>Gets an instance of the <see cref="T:System.Configuration.StringValidator" /> class.</summary>
      <returns>A current <see cref="T:System.Configuration.StringValidator" /> settings in a <see cref="T:System.Configuration.ConfigurationValidatorBase" /> validator instance.</returns>
    </member>
    <member name="T:System.Configuration.SubclassTypeValidator">
      <summary>Validates that an object is a derived class of a specified type.</summary>
    </member>
    <member name="M:System.Configuration.SubclassTypeValidator.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.SubclassTypeValidator" /> class. </summary>
      <param name="baseClass">The base class to validate against.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="baseClass" /> is null.</exception>
    </member>
    <member name="M:System.Configuration.SubclassTypeValidator.CanValidate(System.Type)">
      <summary>Determines whether an object can be validated based on type.</summary>
      <returns>true if the <paramref name="type" /> parameter matches a type that can be validated; otherwise, false. </returns>
      <param name="type">The object type.</param>
    </member>
    <member name="M:System.Configuration.SubclassTypeValidator.Validate(System.Object)">
      <summary>Determines whether the value of an object is valid. </summary>
      <param name="value">The object value.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is not of a <see cref="T:System.Type" /> that can be derived from <paramref name="baseClass" /> as defined in the constructor.</exception>
    </member>
    <member name="T:System.Configuration.SubclassTypeValidatorAttribute">
      <summary>Declaratively instructs the .NET Framework to perform validation on a configuration property. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.SubclassTypeValidatorAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.SubclassTypeValidatorAttribute" /> class. </summary>
      <param name="baseClass">The base class type.</param>
    </member>
    <member name="P:System.Configuration.SubclassTypeValidatorAttribute.BaseClass">
      <summary>Gets the base type of the object being validated.</summary>
      <returns>The base type of the object being validated.</returns>
    </member>
    <member name="P:System.Configuration.SubclassTypeValidatorAttribute.ValidatorInstance">
      <summary>Gets the validator attribute instance.</summary>
      <returns>The current <see cref="T:System.Configuration.ConfigurationValidatorBase" /> instance.</returns>
    </member>
    <member name="T:System.Configuration.TimeSpanMinutesConverter">
      <summary>Converts a time span expressed in minutes. </summary>
    </member>
    <member name="M:System.Configuration.TimeSpanMinutesConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.TimeSpanMinutesConverter" /> class.</summary>
    </member>
    <member name="M:System.Configuration.TimeSpanMinutesConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts a <see cref="T:System.String" /> to a <see cref="T:System.TimeSpan" />.</summary>
      <returns>The <see cref="T:System.TimeSpan" /> representing the <paramref name="data" /> parameter in minutes.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="data">The <see cref="T:System.String" /> object to convert.</param>
    </member>
    <member name="M:System.Configuration.TimeSpanMinutesConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts a <see cref="T:System.TimeSpan" /> to a <see cref="T:System.String" />. </summary>
      <returns>The <see cref="T:System.String" /> representing the <paramref name="value" /> parameter in minutes.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="value">The value to convert to.</param>
      <param name="type">The type to convert to.</param>
    </member>
    <member name="T:System.Configuration.TimeSpanMinutesOrInfiniteConverter">
      <summary>Converts a <see cref="T:System.TimeSpan" /> expressed in minutes or as a standard infinite time span.</summary>
    </member>
    <member name="M:System.Configuration.TimeSpanMinutesOrInfiniteConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.TimeSpanMinutesOrInfiniteConverter" /> class.</summary>
    </member>
    <member name="M:System.Configuration.TimeSpanMinutesOrInfiniteConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts a <see cref="T:System.String" /> to a <see cref="T:System.TimeSpan" />.</summary>
      <returns>The <see cref="F:System.TimeSpan.MaxValue" />, if the <paramref name="data" /> parameter is the <see cref="T:System.String" /> "infinite"; otherwise, the <see cref="T:System.TimeSpan" /> representing the <paramref name="data" /> parameter in minutes.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="data">The <see cref="T:System.String" /> object to convert.</param>
    </member>
    <member name="M:System.Configuration.TimeSpanMinutesOrInfiniteConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts a <see cref="T:System.TimeSpan" /> to a <see cref="T:System.String" />.</summary>
      <returns>The <see cref="T:System.String" /> "infinite", if the <paramref name="value" /> parameter is <see cref="F:System.TimeSpan.MaxValue" />; otherwise, the <see cref="T:System.String" /> representing the <paramref name="value" /> parameter in minutes.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="value">The value to convert.</param>
      <param name="type">The conversion type.</param>
    </member>
    <member name="T:System.Configuration.TimeSpanSecondsConverter">
      <summary>Converts a time span expressed in seconds. </summary>
    </member>
    <member name="M:System.Configuration.TimeSpanSecondsConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.TimeSpanSecondsConverter" /> class.</summary>
    </member>
    <member name="M:System.Configuration.TimeSpanSecondsConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts a <see cref="T:System.String" /> to a <see cref="T:System.TimeSpan" />.</summary>
      <returns>The <see cref="T:System.TimeSpan" /> representing the <paramref name="data" /> parameter in seconds.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="data">The <see cref="T:System.String" /> object to convert.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="data" /> cannot be parsed as an integer value.</exception>
    </member>
    <member name="M:System.Configuration.TimeSpanSecondsConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts a <see cref="T:System.TimeSpan" /> to a <see cref="T:System.String" />.</summary>
      <returns>The <see cref="T:System.String" /> that represents the <paramref name="value" /> parameter in minutes.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="value">The value to convert to.</param>
      <param name="type">The type to convert to.</param>
    </member>
    <member name="T:System.Configuration.TimeSpanSecondsOrInfiniteConverter">
      <summary>Converts a <see cref="T:System.TimeSpan" /> expressed in seconds or as a standard infinite time span.</summary>
    </member>
    <member name="M:System.Configuration.TimeSpanSecondsOrInfiniteConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.TimeSpanSecondsOrInfiniteConverter" /> class.</summary>
    </member>
    <member name="M:System.Configuration.TimeSpanSecondsOrInfiniteConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts a <see cref="T:System.String" /> to a <see cref="T:System.TimeSpan" />.</summary>
      <returns>The <see cref="F:System.TimeSpan.MaxValue" />, if the <paramref name="data" /> parameter is the <see cref="T:System.String" /> "infinite"; otherwise, the <see cref="T:System.TimeSpan" /> representing the <paramref name="data" /> parameter in seconds.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="data">The <see cref="T:System.String" /> object to convert.</param>
    </member>
    <member name="M:System.Configuration.TimeSpanSecondsOrInfiniteConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts a <see cref="T:System.TimeSpan" /> to a. <see cref="T:System.String" />.</summary>
      <returns>The <see cref="T:System.String" /> "infinite", if the <paramref name="value" /> parameter is <see cref="F:System.TimeSpan.MaxValue" />; otherwise, the <see cref="T:System.String" /> representing the <paramref name="value" /> parameter in seconds.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="value">The value to convert.</param>
      <param name="type">The conversion type.</param>
    </member>
    <member name="T:System.Configuration.TimeSpanValidator">
      <summary>Provides validation of a <see cref="T:System.TimeSpan" /> object.</summary>
    </member>
    <member name="M:System.Configuration.TimeSpanValidator.#ctor(System.TimeSpan,System.TimeSpan)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.TimeSpanValidator" /> class, based on supplied parameters.</summary>
      <param name="minValue">A <see cref="T:System.TimeSpan" /> object that specifies the minimum time allowed to pass validation.</param>
      <param name="maxValue">A <see cref="T:System.TimeSpan" /> object that specifies the maximum time allowed to pass validation.</param>
    </member>
    <member name="M:System.Configuration.TimeSpanValidator.#ctor(System.TimeSpan,System.TimeSpan,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.TimeSpanValidator" /> class, based on supplied parameters.</summary>
      <param name="minValue">A <see cref="T:System.TimeSpan" /> object that specifies the minimum time allowed to pass validation.</param>
      <param name="maxValue">A <see cref="T:System.TimeSpan" /> object that specifies the maximum time allowed to pass validation.</param>
      <param name="rangeIsExclusive">A <see cref="T:System.Boolean" /> value that specifies whether the validation range is exclusive.</param>
    </member>
    <member name="M:System.Configuration.TimeSpanValidator.#ctor(System.TimeSpan,System.TimeSpan,System.Boolean,System.Int64)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.TimeSpanValidator" /> class, based on supplied parameters.</summary>
      <param name="minValue">A <see cref="T:System.TimeSpan" /> object that specifies the minimum time allowed to pass validation.</param>
      <param name="maxValue">A <see cref="T:System.TimeSpan" /> object that specifies the maximum time allowed to pass validation.</param>
      <param name="rangeIsExclusive">A <see cref="T:System.Boolean" /> value that specifies whether the validation range is exclusive.</param>
      <param name="resolutionInSeconds">An <see cref="T:System.Int64" /> value that specifies a number of seconds. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="resolutionInSeconds" /> is less than 0.- or -<paramref name="minValue" /> is greater than <paramref name="maxValue" />.</exception>
    </member>
    <member name="M:System.Configuration.TimeSpanValidator.CanValidate(System.Type)">
      <summary>Determines whether the type of the object can be validated.</summary>
      <returns>true if the <paramref name="type" /> parameter matches a <see cref="T:System.TimeSpan" /> value; otherwise, false. </returns>
      <param name="type">The type of the object.</param>
    </member>
    <member name="M:System.Configuration.TimeSpanValidator.Validate(System.Object)">
      <summary>Determines whether the value of an object is valid.</summary>
      <param name="value">The value of an object.</param>
    </member>
    <member name="T:System.Configuration.TimeSpanValidatorAttribute">
      <summary>Declaratively instructs the .NET Framework to perform time validation on a configuration property. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.TimeSpanValidatorAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.TimeSpanValidatorAttribute" /> class.</summary>
    </member>
    <member name="P:System.Configuration.TimeSpanValidatorAttribute.ExcludeRange">
      <summary>Gets or sets a value that indicates whether to include or exclude the integers in the range as defined by <see cref="P:System.Configuration.TimeSpanValidatorAttribute.MinValueString" /> and <see cref="P:System.Configuration.TimeSpanValidatorAttribute.MaxValueString" />.</summary>
      <returns>true if the value must be excluded; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Configuration.TimeSpanValidatorAttribute.MaxValue">
      <summary>Gets the absolute maximum <see cref="T:System.TimeSpan" /> value.</summary>
      <returns>The allowed maximum <see cref="T:System.TimeSpan" /> value. </returns>
    </member>
    <member name="P:System.Configuration.TimeSpanValidatorAttribute.MaxValueString">
      <summary>Gets or sets the relative maximum <see cref="T:System.TimeSpan" /> value.</summary>
      <returns>The allowed maximum <see cref="T:System.TimeSpan" /> value. </returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The selected value represents less than <see cref="P:System.Configuration.TimeSpanValidatorAttribute.MinValue" />.</exception>
    </member>
    <member name="P:System.Configuration.TimeSpanValidatorAttribute.MinValue">
      <summary>Gets the absolute minimum <see cref="T:System.TimeSpan" /> value.</summary>
      <returns>The allowed minimum <see cref="T:System.TimeSpan" /> value. </returns>
    </member>
    <member name="P:System.Configuration.TimeSpanValidatorAttribute.MinValueString">
      <summary>Gets or sets the relative minimum <see cref="T:System.TimeSpan" /> value.</summary>
      <returns>The minimum allowed <see cref="T:System.TimeSpan" /> value. </returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The selected value represents more than <see cref="P:System.Configuration.TimeSpanValidatorAttribute.MaxValue" />.</exception>
    </member>
    <member name="F:System.Configuration.TimeSpanValidatorAttribute.TimeSpanMaxValue">
      <summary>Gets the absolute maximum value allowed.</summary>
    </member>
    <member name="F:System.Configuration.TimeSpanValidatorAttribute.TimeSpanMinValue">
      <summary>Gets the absolute minimum value allowed.</summary>
    </member>
    <member name="P:System.Configuration.TimeSpanValidatorAttribute.ValidatorInstance">
      <summary>Gets an instance of the <see cref="T:System.Configuration.TimeSpanValidator" /> class.</summary>
      <returns>The <see cref="T:System.Configuration.ConfigurationValidatorBase" /> validator instance. </returns>
    </member>
    <member name="T:System.Configuration.TypeNameConverter">
      <summary>Converts between type and string values. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Configuration.TypeNameConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.TypeNameConverter" /> class.</summary>
    </member>
    <member name="M:System.Configuration.TypeNameConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts a <see cref="T:System.String" /> object to a <see cref="T:System.Type" /> object.</summary>
      <returns>The <see cref="T:System.Type" /> that represents the <paramref name="data" /> parameter.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="data">The <see cref="T:System.String" /> object to convert.</param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Type" /> value cannot be resolved.</exception>
    </member>
    <member name="M:System.Configuration.TypeNameConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts a <see cref="T:System.Type" /> object to a <see cref="T:System.String" /> object.</summary>
      <returns>The <see cref="T:System.String" /> that represents the <paramref name="value" /> parameter. </returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="value">The value to convert to.</param>
      <param name="type">The type to convert to.</param>
    </member>
    <member name="T:System.Configuration.ValidatorCallback">
      <summary>Represents a method to be called after the validation of an object.</summary>
      <param name="value">The callback method.</param>
    </member>
    <member name="T:System.Configuration.WhiteSpaceTrimStringConverter">
      <summary>Converts a string to its canonical format. </summary>
    </member>
    <member name="M:System.Configuration.WhiteSpaceTrimStringConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.WhiteSpaceTrimStringConverter" /> class.</summary>
    </member>
    <member name="M:System.Configuration.WhiteSpaceTrimStringConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts a <see cref="T:System.String" /> to canonical form.</summary>
      <returns>An object representing the converted value.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="data">The <see cref="T:System.String" /> object to convert.</param>
    </member>
    <member name="M:System.Configuration.WhiteSpaceTrimStringConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts a <see cref="T:System.String" /> to canonical form.</summary>
      <returns>An object representing the converted value.</returns>
      <param name="ctx">The <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> object used for type conversions.</param>
      <param name="ci">The <see cref="T:System.Globalization.CultureInfo" /> object used during conversion.</param>
      <param name="value">The value to convert to.</param>
      <param name="type">The type to convert to.</param>
    </member>
    <member name="T:System.Configuration.Internal.DelegatingConfigHost">
      <summary>Delegates all members of the <see cref="T:System.Configuration.Internal.IInternalConfigHost" /> interface to another instance of a host.</summary>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.Internal.DelegatingConfigHost" /> class.</summary>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.CreateConfigurationContext(System.String,System.String)">
      <summary>Creates a new configuration context.</summary>
      <returns>A <see cref="T:System.Object" /> representing a new configuration context.</returns>
      <param name="configPath">A string representing the path to a configuration file.</param>
      <param name="locationSubPath">A string representing a location subpath.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.CreateDeprecatedConfigContext(System.String)">
      <summary>Creates a deprecated configuration context.</summary>
      <returns>A <see cref="T:System.Object" /> representing a deprecated configuration context.</returns>
      <param name="configPath">A string representing the path to a configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.DecryptSection(System.String,System.Configuration.ProtectedConfigurationProvider,System.Configuration.ProtectedConfigurationSection)">
      <summary>Decrypts an encrypted configuration section.</summary>
      <returns>A string representing a decrypted configuration section.</returns>
      <param name="encryptedXml">An encrypted section of a configuration file.</param>
      <param name="protectionProvider">A <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> object.</param>
      <param name="protectedConfigSection">A <see cref="T:System.Configuration.ProtectedConfigurationSection" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.DeleteStream(System.String)">
      <summary>Deletes the <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</summary>
      <param name="streamName">The name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.EncryptSection(System.String,System.Configuration.ProtectedConfigurationProvider,System.Configuration.ProtectedConfigurationSection)">
      <summary>Encrypts a section of a configuration object.</summary>
      <returns>A string representing an encrypted section of the configuration object.</returns>
      <param name="clearTextXml">A section of the configuration that is not encrypted.</param>
      <param name="protectionProvider">A <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> object.</param>
      <param name="protectedConfigSection">A <see cref="T:System.Configuration.ProtectedConfigurationSection" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.GetConfigPathFromLocationSubPath(System.String,System.String)">
      <summary>Returns a configuration path based on a location subpath.</summary>
      <returns>A string representing a configuration path.</returns>
      <param name="configPath">A string representing the path to a configuration file.</param>
      <param name="locationSubPath">A string representing a location subpath.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.GetConfigType(System.String,System.Boolean)">
      <summary>Returns a <see cref="T:System.Type" /> representing the type of the configuration.</summary>
      <returns>A <see cref="T:System.Type" /> representing the type of the configuration.</returns>
      <param name="typeName">A string representing the configuration type.</param>
      <param name="throwOnError">true if an exception should be thrown if an error is encountered; false if an exception should not be thrown if an error is encountered.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.GetConfigTypeName(System.Type)">
      <summary>Returns a string representing the type name of the configuration object.</summary>
      <returns>A string representing the type name of the configuration object.</returns>
      <param name="t">A <see cref="T:System.Type" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.GetRestrictedPermissions(System.Configuration.Internal.IInternalConfigRecord,System.Security.PermissionSet@,System.Boolean@)">
      <summary>Sets the specified permission set if available within the host object.</summary>
      <param name="configRecord">An <see cref="T:System.Configuration.Internal.IInternalConfigRecord" /> object.</param>
      <param name="permissionSet">A <see cref="T:System.Security.PermissionSet" /> object.</param>
      <param name="isHostReady">true if the host has finished initialization; otherwise, false.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.GetStreamName(System.String)">
      <summary>Returns the name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</summary>
      <returns>A string representing the name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</returns>
      <param name="configPath">A string representing the path to a configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.GetStreamNameForConfigSource(System.String,System.String)">
      <summary>Returns the name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration source.</summary>
      <returns>A string representing the name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration source.</returns>
      <param name="streamName">The name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</param>
      <param name="configSource">A string representing the configuration source.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.GetStreamVersion(System.String)">
      <summary>Returns a <see cref="P:System.Diagnostics.FileVersionInfo.FileVersion" /> object representing the version of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</summary>
      <returns>A <see cref="P:System.Diagnostics.FileVersionInfo.FileVersion" /> object representing the version of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</returns>
      <param name="streamName">The name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</param>
    </member>
    <member name="P:System.Configuration.Internal.DelegatingConfigHost.Host">
      <summary>Gets or sets the <see cref="T:System.Configuration.Internal.IInternalConfigHost" /> object.</summary>
      <returns>A <see cref="T:System.Configuration.Internal.IInternalConfigHost" /> object.</returns>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.Impersonate">
      <summary>Instructs the host to impersonate and returns an <see cref="T:System.IDisposable" /> object required internally by the .NET Framework.</summary>
      <returns>An <see cref="T:System.IDisposable" /> value.</returns>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.Init(System.Configuration.Internal.IInternalConfigRoot,System.Object[])">
      <summary>Initializes the configuration host.</summary>
      <param name="configRoot">An <see cref="T:System.Configuration.Internal.IInternalConfigRoot" /> object.</param>
      <param name="hostInitParams">A parameter object containing the values used for initializing the configuration host.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.InitForConfiguration(System.String@,System.String@,System.String@,System.Configuration.Internal.IInternalConfigRoot,System.Object[])">
      <summary>Initializes the host for configuration.</summary>
      <param name="locationSubPath">A string representing a location subpath (passed by reference).</param>
      <param name="configPath">A string representing the path to a configuration file.</param>
      <param name="locationConfigPath">The location configuration path.</param>
      <param name="configRoot">The configuration root element.</param>
      <param name="hostInitConfigurationParams">A parameter object representing the parameters used to initialize the host.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.IsAboveApplication(System.String)">
      <summary>Returns a value indicating whether the configuration is above the application configuration in the configuration hierarchy.</summary>
      <returns>true if the configuration is above the application configuration in the configuration hierarchy; otherwise, false.</returns>
      <param name="configPath">A string representing the path to a configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.IsConfigRecordRequired(System.String)">
      <summary>Returns a value indicating whether a configuration record is required for the host configuration initialization.</summary>
      <returns>true if a configuration record is required for the host configuration initialization; otherwise, false.</returns>
      <param name="configPath">A string representing the path to a configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.IsDefinitionAllowed(System.String,System.Configuration.ConfigurationAllowDefinition,System.Configuration.ConfigurationAllowExeDefinition)">
      <summary>Restricts or allows definitions in the host configuration. </summary>
      <returns>true if the grant or restriction of definitions in the host configuration was successful; otherwise, false.</returns>
      <param name="configPath">A string representing the path to a configuration file.</param>
      <param name="allowDefinition">The <see cref="T:System.Configuration.ConfigurationAllowDefinition" /> object.</param>
      <param name="allowExeDefinition">The <see cref="T:System.Configuration.ConfigurationAllowExeDefinition" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.IsFile(System.String)">
      <summary>Returns a value indicating whether the file path used by a <see cref="T:System.IO.Stream" /> object to read a configuration file is a valid path.</summary>
      <returns>true if the path used by a <see cref="T:System.IO.Stream" /> object to read a configuration file is a valid path; otherwise, false.</returns>
      <param name="streamName">The name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.IsFullTrustSectionWithoutAptcaAllowed(System.Configuration.Internal.IInternalConfigRecord)">
      <summary>Returns a value indicating whether a configuration section requires a fully trusted code access security level and does not allow the <see cref="T:System.Security.AllowPartiallyTrustedCallersAttribute" /> attribute to disable implicit link demands.</summary>
      <returns>true if the configuration section requires a fully trusted code access security level and does not allow the <see cref="T:System.Security.AllowPartiallyTrustedCallersAttribute" /> attribute to disable implicit link demands; otherwise, false.</returns>
      <param name="configRecord">The <see cref="T:System.Configuration.Internal.IInternalConfigRecord" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.IsInitDelayed(System.Configuration.Internal.IInternalConfigRecord)">
      <summary>Returns a value indicating whether the initialization of a configuration object is considered delayed.</summary>
      <returns>true if the initialization of a configuration object is considered delayed; otherwise, false.</returns>
      <param name="configRecord">The <see cref="T:System.Configuration.Internal.IInternalConfigRecord" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.IsLocationApplicable(System.String)">
      <summary>Returns a value indicating whether the configuration object supports a location tag.</summary>
      <returns>true if the configuration object supports a location tag; otherwise, false.</returns>
      <param name="configPath">A string representing the path to a configuration file.</param>
    </member>
    <member name="P:System.Configuration.Internal.DelegatingConfigHost.IsRemote">
      <summary>Gets a value indicating whether the configuration is remote.</summary>
      <returns>true if the configuration is remote; otherwise, false.</returns>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.IsSecondaryRoot(System.String)">
      <summary>Returns a value indicating whether a configuration path is to a configuration node whose contents should be treated as a root.</summary>
      <returns>true if the configuration path is to a configuration node whose contents should be treated as a root; otherwise, false.</returns>
      <param name="configPath">A string representing the path to a configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.IsTrustedConfigPath(System.String)">
      <summary>Returns a value indicating whether the configuration path is trusted.</summary>
      <returns>true if the configuration path is trusted; otherwise, false.</returns>
      <param name="configPath">A string representing the path to a configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.OpenStreamForRead(System.String)">
      <summary>Opens a <see cref="T:System.IO.Stream" /> object to read a configuration file.</summary>
      <returns>Returns the <see cref="T:System.IO.Stream" /> object specified by <paramref name="streamName" />.</returns>
      <param name="streamName">The name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.OpenStreamForRead(System.String,System.Boolean)">
      <summary>Opens a <see cref="T:System.IO.Stream" /> object to read a configuration file.</summary>
      <returns>Returns the <see cref="T:System.IO.Stream" /> object specified by <paramref name="streamName" />.</returns>
      <param name="streamName">The name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</param>
      <param name="assertPermissions">true to assert permissions; otherwise, false.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.OpenStreamForWrite(System.String,System.String,System.Object@)">
      <summary>Opens a <see cref="T:System.IO.Stream" /> object for writing to a configuration file or for writing to a temporary file used to build a configuration file. Allows a <see cref="T:System.IO.Stream" /> object to be designated as a template for copying file attributes.</summary>
      <returns>A <see cref="T:System.IO.Stream" /> object.</returns>
      <param name="streamName">The name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</param>
      <param name="templateStreamName">The name of a <see cref="T:System.IO.Stream" /> object from which file attributes are to be copied as a template.</param>
      <param name="writeContext">The write context of the <see cref="T:System.IO.Stream" /> object (passed by reference).</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.OpenStreamForWrite(System.String,System.String,System.Object@,System.Boolean)">
      <summary>Opens a <see cref="T:System.IO.Stream" /> object for writing to a configuration file. Allows a <see cref="T:System.IO.Stream" /> object to be designated as a template for copying file attributes.</summary>
      <returns>Returns the <see cref="T:System.IO.Stream" /> object specified by the <paramref name="streamName" /> parameter.</returns>
      <param name="streamName">The name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</param>
      <param name="templateStreamName">The name of a <see cref="T:System.IO.Stream" /> object from which file attributes are to be copied as a template.</param>
      <param name="writeContext">The write context of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file (passed by reference).</param>
      <param name="assertPermissions">true to assert permissions; otherwise, false.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.PrefetchAll(System.String,System.String)">
      <summary>Returns a value indicating whether the entire configuration file could be read by a designated <see cref="T:System.IO.Stream" /> object.</summary>
      <returns>true if the entire configuration file could be read by the <see cref="T:System.IO.Stream" /> object designated by <paramref name="streamName" />; otherwise, false.</returns>
      <param name="configPath">A string representing the path to a configuration file.</param>
      <param name="streamName">The name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.PrefetchSection(System.String,System.String)">
      <summary>Instructs the <see cref="T:System.Configuration.Internal.IInternalConfigHost" /> object to read a designated section of its associated configuration file.</summary>
      <returns>true if a section of the configuration file designated by the <paramref name="sectionGroupName" /> and <paramref name="sectionName" /> parameters can be read by a <see cref="T:System.IO.Stream" /> object; otherwise, false.</returns>
      <param name="sectionGroupName">A string representing the name of a section group in the configuration file.</param>
      <param name="sectionName">A string representing the name of a section in the configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.RequireCompleteInit(System.Configuration.Internal.IInternalConfigRecord)">
      <summary>Indicates that a new configuration record requires a complete initialization.</summary>
      <param name="configRecord">An <see cref="T:System.Configuration.Internal.IInternalConfigRecord" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.StartMonitoringStreamForChanges(System.String,System.Configuration.Internal.StreamChangeCallback)">
      <summary>Instructs the host to monitor an associated <see cref="T:System.IO.Stream" /> object for changes in a configuration file.</summary>
      <returns>An <see cref="T:System.Object" /> instance containing changed configuration settings.</returns>
      <param name="streamName">The name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</param>
      <param name="callback">A <see cref="T:System.Configuration.Internal.StreamChangeCallback" /> object to receive the returned data representing the changes in the configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.StopMonitoringStreamForChanges(System.String,System.Configuration.Internal.StreamChangeCallback)">
      <summary>Instructs the host object to stop monitoring an associated <see cref="T:System.IO.Stream" /> object for changes in a configuration file.</summary>
      <param name="streamName">The name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</param>
      <param name="callback">A <see cref="T:System.Configuration.Internal.StreamChangeCallback" /> object.</param>
    </member>
    <member name="P:System.Configuration.Internal.DelegatingConfigHost.SupportsChangeNotifications">
      <summary>Gets a value indicating whether the host configuration supports change notifications.</summary>
      <returns>true if the host supports change notifications; otherwise, false. </returns>
    </member>
    <member name="P:System.Configuration.Internal.DelegatingConfigHost.SupportsLocation">
      <summary>Gets a value indicating whether the host configuration supports location tags.</summary>
      <returns>true if the host supports location tags; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.Internal.DelegatingConfigHost.SupportsPath">
      <summary>Gets a value indicating whether the host configuration has path support.</summary>
      <returns>true if the host configuration has path support; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.Internal.DelegatingConfigHost.SupportsRefresh">
      <summary>Gets a value indicating whether the host configuration supports refresh.</summary>
      <returns>true if the host configuration supports refresh; otherwise, false.</returns>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.VerifyDefinitionAllowed(System.String,System.Configuration.ConfigurationAllowDefinition,System.Configuration.ConfigurationAllowExeDefinition,System.Configuration.Internal.IConfigErrorInfo)">
      <summary>Verifies that a configuration definition is allowed for a configuration record.</summary>
      <param name="configPath">A string representing the path to a configuration file.</param>
      <param name="allowDefinition">An <see cref="P:System.Configuration.SectionInformation.AllowDefinition" /> object.</param>
      <param name="allowExeDefinition">A <see cref="T:System.Configuration.ConfigurationAllowExeDefinition" /> object</param>
      <param name="errorInfo">An <see cref="T:System.Configuration.Internal.IConfigErrorInfo" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.WriteCompleted(System.String,System.Boolean,System.Object)">
      <summary>Indicates that all writing to the configuration file has completed.</summary>
      <param name="streamName">The name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</param>
      <param name="success">true if writing to the configuration file completed successfully; otherwise, false.</param>
      <param name="writeContext">The write context of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.DelegatingConfigHost.WriteCompleted(System.String,System.Boolean,System.Object,System.Boolean)">
      <summary>Indicates that all writing to the configuration file has completed and specifies whether permissions should be asserted.</summary>
      <param name="streamName">The name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on a configuration file.</param>
      <param name="success">true to indicate that writing was completed successfully; otherwise, false.</param>
      <param name="writeContext">The write context of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
      <param name="assertPermissions">true to assert permissions; otherwise, false.</param>
    </member>
    <member name="T:System.Configuration.Internal.IConfigErrorInfo">
      <summary>Defines an interface used by the .NET Framework to support creating error configuration records.</summary>
    </member>
    <member name="P:System.Configuration.Internal.IConfigErrorInfo.Filename">
      <summary>Gets a string specifying the file name related to the configuration details.</summary>
      <returns>A string specifying a filename.</returns>
    </member>
    <member name="P:System.Configuration.Internal.IConfigErrorInfo.LineNumber">
      <summary>Gets an integer specifying the line number related to the configuration details.</summary>
      <returns>An integer specifying a line number.</returns>
    </member>
    <member name="T:System.Configuration.Internal.IConfigSystem">
      <summary>Defines an interface used by the .NET Framework to support the initialization of configuration properties.</summary>
    </member>
    <member name="P:System.Configuration.Internal.IConfigSystem.Host">
      <summary>Gets the configuration host.</summary>
      <returns>An <see cref="T:System.Configuration.Internal.IInternalConfigHost" /> object that is used by the .NET Framework to initialize application configuration properties.</returns>
    </member>
    <member name="M:System.Configuration.Internal.IConfigSystem.Init(System.Type,System.Object[])">
      <summary>Initializes a configuration object.</summary>
      <param name="typeConfigHost">The type of configuration host.</param>
      <param name="hostInitParams">An array of configuration host parameters.</param>
    </member>
    <member name="P:System.Configuration.Internal.IConfigSystem.Root">
      <summary>Gets the root of the configuration hierarchy.</summary>
      <returns>An <see cref="T:System.Configuration.Internal.IInternalConfigRoot" /> object.</returns>
    </member>
    <member name="T:System.Configuration.Internal.IConfigurationManagerHelper">
      <summary>Defines an interface used by the .NET Framework to support configuration management.</summary>
    </member>
    <member name="M:System.Configuration.Internal.IConfigurationManagerHelper.EnsureNetConfigLoaded">
      <summary>Ensures that the networking configuration is loaded.</summary>
    </member>
    <member name="T:System.Configuration.Internal.IConfigurationManagerInternal">
      <summary>Defines an interface used by the .NET Framework to initialize configuration properties.</summary>
    </member>
    <member name="P:System.Configuration.Internal.IConfigurationManagerInternal.ApplicationConfigUri">
      <summary>Gets the configuration file name related to the application path.</summary>
      <returns>A string value representing a configuration file name.</returns>
    </member>
    <member name="P:System.Configuration.Internal.IConfigurationManagerInternal.ExeLocalConfigDirectory">
      <summary>Gets the local configuration directory of the application based on the entry assembly.</summary>
      <returns>A string representing the local configuration directory.</returns>
    </member>
    <member name="P:System.Configuration.Internal.IConfigurationManagerInternal.ExeLocalConfigPath">
      <summary>Gets the local configuration path of the application based on the entry assembly.</summary>
      <returns>A string value representing the local configuration path of the application.</returns>
    </member>
    <member name="P:System.Configuration.Internal.IConfigurationManagerInternal.ExeProductName">
      <summary>Gets the product name of the application based on the entry assembly.</summary>
      <returns>A string value representing the product name of the application.</returns>
    </member>
    <member name="P:System.Configuration.Internal.IConfigurationManagerInternal.ExeProductVersion">
      <summary>Gets the product version of the application based on the entry assembly.</summary>
      <returns>A string value representing the product version of the application.</returns>
    </member>
    <member name="P:System.Configuration.Internal.IConfigurationManagerInternal.ExeRoamingConfigDirectory">
      <summary>Gets the roaming configuration directory of the application based on the entry assembly.</summary>
      <returns>A string value representing the roaming configuration directory of the application.</returns>
    </member>
    <member name="P:System.Configuration.Internal.IConfigurationManagerInternal.ExeRoamingConfigPath">
      <summary>Gets the roaming user's configuration path based on the application's entry assembly.</summary>
      <returns>A string value representing the roaming user's configuration path.</returns>
    </member>
    <member name="P:System.Configuration.Internal.IConfigurationManagerInternal.MachineConfigPath">
      <summary>Gets the configuration path for the Machine.config file.</summary>
      <returns>A string value representing the path of the Machine.config file.</returns>
    </member>
    <member name="P:System.Configuration.Internal.IConfigurationManagerInternal.SetConfigurationSystemInProgress">
      <summary>Gets a value representing the configuration system's status.</summary>
      <returns>true if the configuration system is in the process of being initialized; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.Internal.IConfigurationManagerInternal.SupportsUserConfig">
      <summary>Gets a value that specifies whether user configuration settings are supported.</summary>
      <returns>true if the configuration system supports user configuration settings; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.Internal.IConfigurationManagerInternal.UserConfigFilename">
      <summary>Gets the name of the file used to store user configuration settings.</summary>
      <returns>A string specifying the name of the file used to store user configuration.</returns>
    </member>
    <member name="T:System.Configuration.Internal.IInternalConfigClientHost">
      <summary>Defines interfaces that allow the internal .NET Framework infrastructure to customize configuration.</summary>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigClientHost.GetExeConfigPath">
      <summary>Returns the path to the application configuration file. </summary>
      <returns>A string representing the path to the application configuration file.</returns>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigClientHost.GetLocalUserConfigPath">
      <summary>Returns a string representing the path to the known local user configuration file.</summary>
      <returns>A string representing the path to the known local user configuration file.</returns>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigClientHost.GetRoamingUserConfigPath">
      <summary>Returns a string representing the path to the known roaming user configuration file.</summary>
      <returns>A string representing the path to the known roaming user configuration file.</returns>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigClientHost.IsExeConfig(System.String)">
      <summary>Returns a value indicating whether a configuration file path is the same as a currently known application configuration file path. </summary>
      <returns>true if a string representing a configuration path is the same as a path to the application configuration file; false if a string representing a configuration path is not the same as a path to the application configuration file. </returns>
      <param name="configPath">A string representing the path to the application configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigClientHost.IsLocalUserConfig(System.String)">
      <summary>Returns a value indicating whether a configuration file path is the same as the configuration file path for the currently known local user. </summary>
      <returns>true if a string representing a configuration path is the same as a path to a known local user configuration file; otherwise, false.</returns>
      <param name="configPath">A string representing the path to the application configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigClientHost.IsRoamingUserConfig(System.String)">
      <summary>Returns a value indicating whether a configuration file path is the same as the configuration file path for the currently known roaming user.</summary>
      <returns>true if a string representing a configuration path is the same as a path to a known roaming user configuration file; otherwise, false.</returns>
      <param name="configPath">A string representing the path to an application configuration file.</param>
    </member>
    <member name="T:System.Configuration.Internal.IInternalConfigConfigurationFactory">
      <summary>Defines the interfaces used by the internal design time API to create a <see cref="T:System.Configuration.Configuration" /> object.</summary>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigConfigurationFactory.Create(System.Type,System.Object[])">
      <summary>Creates and initializes a <see cref="T:System.Configuration.Configuration" /> object.</summary>
      <returns>A <see cref="T:System.Configuration.Configuration" /> object.</returns>
      <param name="typeConfigHost">The <see cref="T:System.Type" /> of the <see cref="T:System.Configuration.Configuration" /> object to be created.</param>
      <param name="hostInitConfigurationParams">A parameter array of <see cref="T:System.Object" /> that contains the parameters to be applied to the created <see cref="T:System.Configuration.Configuration" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigConfigurationFactory.NormalizeLocationSubPath(System.String,System.Configuration.Internal.IConfigErrorInfo)">
      <summary>Normalizes a location subpath of a path to a configuration file.</summary>
      <returns>A normalized subpath string.</returns>
      <param name="subPath">A string representing the path to the configuration file.</param>
      <param name="errorInfo">An instance of <see cref="T:System.Configuration.Internal.IConfigErrorInfo" /> or null.</param>
    </member>
    <member name="T:System.Configuration.Internal.IInternalConfigHost">
      <summary>Defines interfaces used by internal .NET structures to initialize application configuration properties.</summary>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.CreateConfigurationContext(System.String,System.String)">
      <summary>Creates and returns a context object for a <see cref="T:System.Configuration.ConfigurationElement" /> of an application configuration.</summary>
      <returns>A context object for a <see cref="T:System.Configuration.ConfigurationElement" /> object of an application configuration.</returns>
      <param name="configPath">A string representing the path of the application configuration file.</param>
      <param name="locationSubPath">A string representing a subpath location of the configuration element.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.CreateDeprecatedConfigContext(System.String)">
      <summary>Creates and returns a deprecated context object of the application configuration.</summary>
      <returns>A deprecated context object of the application configuration.</returns>
      <param name="configPath">A string representing a path to an application configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.DecryptSection(System.String,System.Configuration.ProtectedConfigurationProvider,System.Configuration.ProtectedConfigurationSection)">
      <summary>Decrypts an encrypted configuration section and returns it as a string.</summary>
      <returns>A decrypted configuration section as a string.</returns>
      <param name="encryptedXml">An encrypted XML string representing a configuration section.</param>
      <param name="protectionProvider">The <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> object.</param>
      <param name="protectedConfigSection">The <see cref="T:System.Configuration.ProtectedConfigurationSection" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.DeleteStream(System.String)">
      <summary>Deletes the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the application configuration file.</summary>
      <param name="streamName">A string representing the name of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.EncryptSection(System.String,System.Configuration.ProtectedConfigurationProvider,System.Configuration.ProtectedConfigurationSection)">
      <summary>Encrypts a configuration section and returns it as a string.</summary>
      <returns>An encrypted configuration section represented as a string.</returns>
      <param name="clearTextXml">An XML string representing a configuration section to encrypt.</param>
      <param name="protectionProvider">The <see cref="T:System.Configuration.ProtectedConfigurationProvider" /> object.</param>
      <param name="protectedConfigSection">The <see cref="T:System.Configuration.ProtectedConfigurationSection" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.GetConfigPathFromLocationSubPath(System.String,System.String)">
      <summary>Returns the complete path to an application configuration file based on the location subpath.</summary>
      <returns>A string representing the complete path to an application configuration file.</returns>
      <param name="configPath">A string representing the path of the application configuration file.</param>
      <param name="locationSubPath">The subpath location of the configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.GetConfigType(System.String,System.Boolean)">
      <summary>Returns a <see cref="T:System.Type" /> object representing the type of the configuration object.</summary>
      <returns>A <see cref="T:System.Type" /> object representing the type of the configuration object.</returns>
      <param name="typeName">The type name</param>
      <param name="throwOnError">true to throw an exception if an error occurs; otherwise, false</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.GetConfigTypeName(System.Type)">
      <summary>Returns a string representing a type name from the <see cref="T:System.Type" /> object representing the type of the configuration.</summary>
      <returns>A string representing the type name from a <see cref="T:System.Type" /> object representing the type of the configuration.</returns>
      <param name="t">A <see cref="T:System.Type" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.GetRestrictedPermissions(System.Configuration.Internal.IInternalConfigRecord,System.Security.PermissionSet@,System.Boolean@)">
      <summary>Associates the configuration with a <see cref="T:System.Security.PermissionSet" /> object.</summary>
      <param name="configRecord">An <see cref="T:System.Configuration.Internal.IInternalConfigRecord" /> object.</param>
      <param name="permissionSet">The <see cref="T:System.Security.PermissionSet" /> object to associate with the configuration.</param>
      <param name="isHostReady">true to indicate the configuration host is has completed building associated permissions; otherwise, false.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.GetStreamName(System.String)">
      <summary>Returns a string representing the configuration file name associated with the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</summary>
      <returns>A string representing the configuration file name associated with the <see cref="T:System.IO.Stream" /> I/O tasks on the configuration file.</returns>
      <param name="configPath">A string representing the path of the application configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.GetStreamNameForConfigSource(System.String,System.String)">
      <summary>Returns a string representing the configuration file name associated with the <see cref="T:System.IO.Stream" /> object performing I/O tasks on a remote configuration file.</summary>
      <returns>A string representing the configuration file name associated with the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</returns>
      <param name="streamName">A string representing the configuration file name associated with the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
      <param name="configSource">A string representing a path to a remote configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.GetStreamVersion(System.String)">
      <summary>Returns the version of the <see cref="T:System.IO.Stream" /> object associated with configuration file.</summary>
      <returns>The version of the <see cref="T:System.IO.Stream" /> object associated with configuration file.</returns>
      <param name="streamName">A string representing the name of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.Impersonate">
      <summary>Instructs the host to impersonate and returns an <see cref="T:System.IDisposable" /> object required by the internal .NET structure.</summary>
      <returns>An <see cref="T:System.IDisposable" /> value.</returns>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.Init(System.Configuration.Internal.IInternalConfigRoot,System.Object[])">
      <summary>Initializes a configuration host.</summary>
      <param name="configRoot">The configuration root object.</param>
      <param name="hostInitParams">The parameter object containing the values used for initializing the configuration host.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.InitForConfiguration(System.String@,System.String@,System.String@,System.Configuration.Internal.IInternalConfigRoot,System.Object[])">
      <summary>Initializes a configuration object.</summary>
      <param name="locationSubPath">The subpath location of the configuration file.</param>
      <param name="configPath">A string representing the path of the application configuration file.</param>
      <param name="locationConfigPath">A string representing the location of a configuration path.</param>
      <param name="configRoot">The <see cref="T:System.Configuration.Internal.IInternalConfigRoot" /> object.</param>
      <param name="hostInitConfigurationParams">The parameter object containing the values used for initializing the configuration host.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.IsAboveApplication(System.String)">
      <summary>Returns a value indicating whether the configuration file is located at a higher level in the configuration hierarchy than the application configuration.</summary>
      <returns>true the configuration file is located at a higher level in the configuration hierarchy than the application configuration; otherwise, false.</returns>
      <param name="configPath">A string representing the path of the application configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.IsConfigRecordRequired(System.String)">
      <summary>Returns a value indicating whether a child record is required for a child configuration path.</summary>
      <returns>true if child record is required for a child configuration path; otherwise, false.</returns>
      <param name="configPath">A string representing the path of the application configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.IsDefinitionAllowed(System.String,System.Configuration.ConfigurationAllowDefinition,System.Configuration.ConfigurationAllowExeDefinition)">
      <summary>Determines if a different <see cref="T:System.Type" /> definition is allowable for an application configuration object.</summary>
      <returns>true if a different <see cref="T:System.Type" /> definition is allowable for an application configuration object; otherwise, false.</returns>
      <param name="configPath">A string representing the path of the application configuration file.</param>
      <param name="allowDefinition">A <see cref="T:System.Configuration.ConfigurationAllowDefinition" /> object.</param>
      <param name="allowExeDefinition">A <see cref="T:System.Configuration.ConfigurationAllowExeDefinition" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.IsFile(System.String)">
      <summary>Returns a value indicating whether the file path used by a <see cref="T:System.IO.Stream" /> object to read a configuration file is a valid path.</summary>
      <returns>true if the path used by a <see cref="T:System.IO.Stream" /> object to read a configuration file is a valid path; otherwise, false.</returns>
      <param name="streamName">A string representing the name of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.IsFullTrustSectionWithoutAptcaAllowed(System.Configuration.Internal.IInternalConfigRecord)">
      <summary>Returns a value indicating whether a configuration section requires a fully trusted code access security level and does not allow the <see cref="T:System.Security.AllowPartiallyTrustedCallersAttribute" /> attribute to disable implicit link demands.</summary>
      <returns>true if the configuration section requires a fully trusted code access security level and does not allow the <see cref="T:System.Security.AllowPartiallyTrustedCallersAttribute" /> attribute to disable implicit link demands; otherwise, false.</returns>
      <param name="configRecord">The <see cref="T:System.Configuration.Internal.IInternalConfigRecord" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.IsInitDelayed(System.Configuration.Internal.IInternalConfigRecord)">
      <summary>Returns a value indicating whether the initialization of a configuration object is considered delayed.</summary>
      <returns>true if the initialization of a configuration object is considered delayed; otherwise, false.</returns>
      <param name="configRecord">The <see cref="T:System.Configuration.Internal.IInternalConfigRecord" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.IsLocationApplicable(System.String)">
      <summary>Returns a value indicating whether the configuration object supports a location tag.</summary>
      <returns>true if the configuration object supports a location tag; otherwise, false.</returns>
      <param name="configPath">A string representing the path of the application configuration file.</param>
    </member>
    <member name="P:System.Configuration.Internal.IInternalConfigHost.IsRemote">
      <summary>Returns a value indicating whether the configuration is remote.</summary>
      <returns>true if the configuration is remote; otherwise, false.</returns>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.IsSecondaryRoot(System.String)">
      <summary>Returns a value indicating whether a configuration path is to a configuration node whose contents should be treated as a root.</summary>
      <returns>true if the configuration path is to a configuration node whose contents should be treated as a root; otherwise, false.</returns>
      <param name="configPath">A string representing the path of the application configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.IsTrustedConfigPath(System.String)">
      <summary>Returns a value indicating whether the configuration path is trusted.</summary>
      <returns>true if the configuration path is trusted; otherwise, false.</returns>
      <param name="configPath">A string representing the path of the application configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.OpenStreamForRead(System.String)">
      <summary>Opens a <see cref="T:System.IO.Stream" /> to read a configuration file.</summary>
      <returns>A <see cref="T:System.IO.Stream" /> object.</returns>
      <param name="streamName">A string representing the name of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.OpenStreamForRead(System.String,System.Boolean)">
      <summary>Opens a <see cref="T:System.IO.Stream" /> object to read a configuration file.</summary>
      <returns>Returns the <see cref="T:System.IO.Stream" /> object specified by <paramref name="streamName" />.</returns>
      <param name="streamName">A string representing the name of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
      <param name="assertPermissions">true to assert permissions; otherwise, false.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.OpenStreamForWrite(System.String,System.String,System.Object@)">
      <summary>Opens a <see cref="T:System.IO.Stream" /> object for writing to a configuration file or for writing to a temporary file used to build a configuration file. Allows a <see cref="T:System.IO.Stream" /> object to be designated as a template for copying file attributes.</summary>
      <returns>A <see cref="T:System.IO.Stream" /> object.</returns>
      <param name="streamName">A string representing the name of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
      <param name="templateStreamName">The name of a <see cref="T:System.IO.Stream" /> object from which file attributes are to be copied as a template.</param>
      <param name="writeContext">The write context of the <see cref="T:System.IO.Stream" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.OpenStreamForWrite(System.String,System.String,System.Object@,System.Boolean)">
      <summary>Opens a <see cref="T:System.IO.Stream" /> object for writing to a configuration file. Allows a <see cref="T:System.IO.Stream" /> object to be designated as a template for copying file attributes.</summary>
      <returns>Returns the <see cref="T:System.IO.Stream" /> object specified by <paramref name="streamName" />.</returns>
      <param name="streamName">A string representing the name of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
      <param name="templateStreamName">The name of a <see cref="T:System.IO.Stream" /> from which file attributes are to be copied as a template.</param>
      <param name="writeContext">The write context of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
      <param name="assertPermissions">true to assert permissions; otherwise, false.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.PrefetchAll(System.String,System.String)">
      <summary>Returns a value indicating whether the entire configuration file could be read by a designated <see cref="T:System.IO.Stream" /> object.</summary>
      <returns>true if the entire configuration file could be read by the <see cref="T:System.IO.Stream" /> object designated by <paramref name="streamName" />; otherwise, false.</returns>
      <param name="configPath">A string representing the path of the application configuration file.</param>
      <param name="streamName">A string representing the name of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.PrefetchSection(System.String,System.String)">
      <summary>Instructs the <see cref="T:System.Configuration.Internal.IInternalConfigHost" /> object to read a designated section of its associated configuration file.</summary>
      <returns>true if a section of the configuration file designated by <paramref name="sectionGroupName" /> and <paramref name="sectionName" /> could be read by a <see cref="T:System.IO.Stream" /> object; otherwise, false.</returns>
      <param name="sectionGroupName">A string representing the identifying name of a configuration file section group.</param>
      <param name="sectionName">A string representing the identifying name of a configuration file section.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.RequireCompleteInit(System.Configuration.Internal.IInternalConfigRecord)">
      <summary>Indicates a new configuration record requires a complete initialization.</summary>
      <param name="configRecord">An <see cref="T:System.Configuration.Internal.IInternalConfigRecord" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.StartMonitoringStreamForChanges(System.String,System.Configuration.Internal.StreamChangeCallback)">
      <summary>Instructs the <see cref="T:System.Configuration.Internal.IInternalConfigHost" /> object to monitor an associated <see cref="T:System.IO.Stream" /> object for changes in a configuration file.</summary>
      <returns>An <see cref="T:System.Object" /> containing changed configuration settings.</returns>
      <param name="streamName">A string representing the name of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
      <param name="callback">A <see cref="T:System.Configuration.Internal.StreamChangeCallback" /> object to receive the returned data representing the changes in the configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.StopMonitoringStreamForChanges(System.String,System.Configuration.Internal.StreamChangeCallback)">
      <summary>Instructs the  <see cref="T:System.Configuration.Internal.IInternalConfigHost" /> object to stop monitoring an associated <see cref="T:System.IO.Stream" /> object for changes in a configuration file.</summary>
      <param name="streamName">A string representing the name of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
      <param name="callback">A <see cref="T:System.Configuration.Internal.StreamChangeCallback" /> object.</param>
    </member>
    <member name="P:System.Configuration.Internal.IInternalConfigHost.SupportsChangeNotifications">
      <summary>Returns a value indicating whether the host configuration supports change notification.</summary>
      <returns>true if the configuration supports change notification; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.Internal.IInternalConfigHost.SupportsLocation">
      <summary>Returns a value indicating whether the host configuration supports location tags.</summary>
      <returns>true if the configuration supports location tags; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.Internal.IInternalConfigHost.SupportsPath">
      <summary>Returns a value indicating whether the host configuration supports path tags.</summary>
      <returns>true if the configuration supports path tags; otherwise, false.</returns>
    </member>
    <member name="P:System.Configuration.Internal.IInternalConfigHost.SupportsRefresh">
      <summary>Returns a value indicating whether the host configuration supports configuration refresh.</summary>
      <returns>true if the configuration supports configuration refresh; otherwise, false.</returns>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.VerifyDefinitionAllowed(System.String,System.Configuration.ConfigurationAllowDefinition,System.Configuration.ConfigurationAllowExeDefinition,System.Configuration.Internal.IConfigErrorInfo)">
      <summary>Verifies that a configuration definition is allowed for a configuration record.</summary>
      <param name="configPath">A string representing the path of the application configuration file.</param>
      <param name="allowDefinition">A <see cref="P:System.Configuration.SectionInformation.AllowDefinition" /> object.</param>
      <param name="allowExeDefinition">A <see cref="T:System.Configuration.ConfigurationAllowExeDefinition" /> object</param>
      <param name="errorInfo">An <see cref="T:System.Configuration.Internal.IConfigErrorInfo" /> object.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.WriteCompleted(System.String,System.Boolean,System.Object)">
      <summary>Indicates that all writing to the configuration file has completed.</summary>
      <param name="streamName">A string representing the name of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
      <param name="success">true if the write to the configuration file was completed successfully; otherwise, false.</param>
      <param name="writeContext">The write context of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigHost.WriteCompleted(System.String,System.Boolean,System.Object,System.Boolean)">
      <summary>Indicates that all writing to the configuration file has completed and specifies whether permissions should be asserted.</summary>
      <param name="streamName">A string representing the name of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
      <param name="success">true to indicate the write was completed successfully; otherwise, false.</param>
      <param name="writeContext">The write context of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
      <param name="assertPermissions">true to assert permissions; otherwise, false.</param>
    </member>
    <member name="T:System.Configuration.Internal.IInternalConfigRecord">
      <summary>Defines interfaces used by internal .NET structures to support creation of new configuration records.</summary>
    </member>
    <member name="P:System.Configuration.Internal.IInternalConfigRecord.ConfigPath">
      <summary>Gets a string representing a configuration file path.</summary>
      <returns>A string representing a configuration file path.</returns>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigRecord.GetLkgSection(System.String)">
      <summary>Returns an object representing a section of a configuration from the last-known-good (LKG) configuration.</summary>
      <returns>An <see cref="T:System.Object" /> instance representing the section of the last-known-good configuration specified by <paramref name="configKey" />.</returns>
      <param name="configKey">A string representing a key to a configuration section.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigRecord.GetSection(System.String)">
      <summary>Returns an <see cref="T:System.Object" /> instance representing a section of a configuration file.</summary>
      <returns>An <see cref="T:System.Object" /> instance representing a section of a configuration file.</returns>
      <param name="configKey">A string representing a key to a configuration section.</param>
    </member>
    <member name="P:System.Configuration.Internal.IInternalConfigRecord.HasInitErrors">
      <summary>Returns a value indicating whether an error occurred during initialization of a configuration object.</summary>
      <returns>true if an error occurred during initialization of a configuration object; otherwise, false.</returns>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigRecord.RefreshSection(System.String)">
      <summary>Causes a specified section of the configuration object to be reinitialized.</summary>
      <param name="configKey">A string representing a key to a configuration section that is to be refreshed.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigRecord.Remove">
      <summary>Removes a configuration record.</summary>
    </member>
    <member name="P:System.Configuration.Internal.IInternalConfigRecord.StreamName">
      <summary>Returns the name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</summary>
      <returns>A string representing the name of a <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</returns>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigRecord.ThrowIfInitErrors">
      <summary>Grants the configuration object the permission to throw an exception if an error occurs during initialization.</summary>
    </member>
    <member name="T:System.Configuration.Internal.IInternalConfigRoot">
      <summary>Defines interfaces used by internal .NET structures to support a configuration root object.</summary>
    </member>
    <member name="E:System.Configuration.Internal.IInternalConfigRoot.ConfigChanged">
      <summary>Represents the method that handles the <see cref="E:System.Configuration.Internal.IInternalConfigRoot.ConfigChanged" /> event of an <see cref="T:System.Configuration.Internal.IInternalConfigRoot" /> object.</summary>
    </member>
    <member name="E:System.Configuration.Internal.IInternalConfigRoot.ConfigRemoved">
      <summary>Represents the method that handles the <see cref="E:System.Configuration.Internal.IInternalConfigRoot.ConfigRemoved" /> event of a <see cref="T:System.Configuration.Internal.IInternalConfigRoot" /> object.</summary>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigRoot.GetConfigRecord(System.String)">
      <summary>Returns an <see cref="T:System.Configuration.Internal.IInternalConfigRecord" /> object representing a configuration specified by a configuration path.</summary>
      <returns>An <see cref="T:System.Configuration.Internal.IInternalConfigRecord" /> object representing a configuration specified by <paramref name="configPath" />.</returns>
      <param name="configPath">A string representing the path to a configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigRoot.GetSection(System.String,System.String)">
      <summary>Returns an <see cref="T:System.Object" /> representing the data in a section of a configuration file.</summary>
      <returns>An <see cref="T:System.Object" /> representing the data in a section of a configuration file.</returns>
      <param name="section">A string representing a section of a configuration file.</param>
      <param name="configPath">A string representing the path to a configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigRoot.GetUniqueConfigPath(System.String)">
      <summary>Returns a value representing the file path of the nearest configuration ancestor that has configuration data.</summary>
      <returns>Returns a string representing the file path of the nearest configuration ancestor that has configuration data.</returns>
      <param name="configPath">The path of configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigRoot.GetUniqueConfigRecord(System.String)">
      <summary>Returns an <see cref="T:System.Configuration.Internal.IInternalConfigRecord" /> object representing a unique configuration record for given configuration path.</summary>
      <returns>An <see cref="T:System.Configuration.Internal.IInternalConfigRecord" /> object representing a unique configuration record for a given configuration path.</returns>
      <param name="configPath">The path of the configuration file.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigRoot.Init(System.Configuration.Internal.IInternalConfigHost,System.Boolean)">
      <summary>Initializes a configuration object.</summary>
      <param name="host">An <see cref="T:System.Configuration.Internal.IInternalConfigHost" /> object.</param>
      <param name="isDesignTime">true if design time; false if run time.</param>
    </member>
    <member name="P:System.Configuration.Internal.IInternalConfigRoot.IsDesignTime">
      <summary>Returns a value indicating whether the configuration is a design-time configuration.</summary>
      <returns>true if the configuration is a design-time configuration; false if the configuration is not a design-time configuration.</returns>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigRoot.RemoveConfig(System.String)">
      <summary>Finds and removes a configuration record and all its children for a given configuration path.</summary>
      <param name="configPath">The path of the configuration file.</param>
    </member>
    <member name="T:System.Configuration.Internal.IInternalConfigSettingsFactory">
      <summary>Defines an interface used by the configuration system to set the <see cref="T:System.Configuration.ConfigurationSettings" /> class.</summary>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigSettingsFactory.CompleteInit">
      <summary>Indicates that initialization of the configuration system has completed. </summary>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigSettingsFactory.SetConfigurationSystem(System.Configuration.Internal.IInternalConfigSystem,System.Boolean)">
      <summary>Provides hierarchical configuration settings and extensions specific to ASP.NET to the configuration system. </summary>
      <param name="internalConfigSystem">An <see cref="T:System.Configuration.Internal.IInternalConfigSystem" /> object used by the <see cref="T:System.Configuration.ConfigurationSettings" /> class.</param>
      <param name="initComplete">true if the initialization process of the configuration system is complete; otherwise, false.</param>
    </member>
    <member name="T:System.Configuration.Internal.IInternalConfigSystem">
      <summary>Defines an interface used by the .NET Framework to initialize application configuration properties.</summary>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigSystem.GetSection(System.String)">
      <summary>Returns the configuration object based on the specified key. </summary>
      <returns>A configuration object.</returns>
      <param name="configKey">The configuration key value.</param>
    </member>
    <member name="M:System.Configuration.Internal.IInternalConfigSystem.RefreshConfig(System.String)">
      <summary>Refreshes the configuration system based on the specified section name. </summary>
      <param name="sectionName">The name of the configuration section.</param>
    </member>
    <member name="P:System.Configuration.Internal.IInternalConfigSystem.SupportsUserConfig">
      <summary>Gets a value indicating whether the user configuration is supported. </summary>
      <returns>true if the user configuration is supported; otherwise, false.</returns>
    </member>
    <member name="T:System.Configuration.Internal.InternalConfigEventArgs">
      <summary>Defines a class that allows the .NET Framework infrastructure to specify event arguments for configuration events.</summary>
    </member>
    <member name="M:System.Configuration.Internal.InternalConfigEventArgs.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.Internal.InternalConfigEventArgs" /> class.</summary>
      <param name="configPath">A configuration path.</param>
    </member>
    <member name="P:System.Configuration.Internal.InternalConfigEventArgs.ConfigPath">
      <summary>Gets or sets the configuration path related to the <see cref="T:System.Configuration.Internal.InternalConfigEventArgs" /> object.</summary>
      <returns>A string value specifying the configuration path.</returns>
    </member>
    <member name="T:System.Configuration.Internal.InternalConfigEventHandler">
      <summary>Defines a class used by the .NET Framework infrastructure to support configuration events.</summary>
      <param name="sender">The source object of the event.</param>
      <param name="e">A configuration event argument.</param>
    </member>
    <member name="T:System.Configuration.Internal.StreamChangeCallback">
      <summary>Represents a method for hosts to call when a monitored stream has changed.</summary>
      <param name="streamName">The name of the <see cref="T:System.IO.Stream" /> object performing I/O tasks on the configuration file.</param>
    </member>
    <member name="T:System.Configuration.Provider.ProviderBase">
      <summary>Provides a base implementation for the extensible provider model.</summary>
    </member>
    <member name="M:System.Configuration.Provider.ProviderBase.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.Provider.ProviderBase" /> class. </summary>
    </member>
    <member name="P:System.Configuration.Provider.ProviderBase.Description">
      <summary>Gets a brief, friendly description suitable for display in administrative tools or other user interfaces (UIs).</summary>
      <returns>A brief, friendly description suitable for display in administrative tools or other UIs.</returns>
    </member>
    <member name="M:System.Configuration.Provider.ProviderBase.Initialize(System.String,System.Collections.Specialized.NameValueCollection)">
      <summary>Initializes the provider.</summary>
      <param name="name">The friendly name of the provider.</param>
      <param name="config">A collection of the name/value pairs representing the provider-specific attributes specified in the configuration for this provider.</param>
      <exception cref="T:System.ArgumentNullException">The name of the provider is null.</exception>
      <exception cref="T:System.ArgumentException">The name of the provider has a length of zero.</exception>
      <exception cref="T:System.InvalidOperationException">An attempt is made to call <see cref="M:System.Configuration.Provider.ProviderBase.Initialize(System.String,System.Collections.Specialized.NameValueCollection)" /> on a provider after the provider has already been initialized.</exception>
    </member>
    <member name="P:System.Configuration.Provider.ProviderBase.Name">
      <summary>Gets the friendly name used to refer to the provider during configuration.</summary>
      <returns>The friendly name used to refer to the provider during configuration.</returns>
    </member>
    <member name="T:System.Configuration.Provider.ProviderCollection">
      <summary>Represents a collection of provider objects that inherit from <see cref="T:System.Configuration.Provider.ProviderBase" />.</summary>
    </member>
    <member name="M:System.Configuration.Provider.ProviderCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Configuration.Provider.ProviderCollection" /> class. </summary>
    </member>
    <member name="M:System.Configuration.Provider.ProviderCollection.Add(System.Configuration.Provider.ProviderBase)">
      <summary>Adds a provider to the collection.</summary>
      <param name="provider">The provider to be added.</param>
      <exception cref="T:System.NotSupportedException">The collection is read-only.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="provider" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Configuration.Provider.ProviderBase.Name" /> of <paramref name="provider" /> is null.- or -The length of the <see cref="P:System.Configuration.Provider.ProviderBase.Name" /> of <paramref name="provider" /> is less than 1.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Configuration.Provider.ProviderCollection.Clear">
      <summary>Removes all items from the collection.</summary>
      <exception cref="T:System.NotSupportedException">The collection is set to read-only.</exception>
    </member>
    <member name="M:System.Configuration.Provider.ProviderCollection.CopyTo(System.Configuration.Provider.ProviderBase[],System.Int32)">
      <summary>Copies the contents of the collection to the given array starting at the specified index.</summary>
      <param name="array">The array to copy the elements of the collection to.</param>
      <param name="index">The index of the collection item at which to start the copying process.</param>
    </member>
    <member name="P:System.Configuration.Provider.ProviderCollection.Count">
      <summary>Gets the number of providers in the collection.</summary>
      <returns>The number of providers in the collection.</returns>
    </member>
    <member name="M:System.Configuration.Provider.ProviderCollection.GetEnumerator">
      <summary>Returns an object that implements the <see cref="T:System.Collections.IEnumerator" /> interface to iterate through the collection.</summary>
      <returns>An object that implements <see cref="T:System.Collections.IEnumerator" /> to iterate through the collection.</returns>
    </member>
    <member name="P:System.Configuration.Provider.ProviderCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>false in all cases.</returns>
    </member>
    <member name="P:System.Configuration.Provider.ProviderCollection.Item(System.String)">
      <summary>Gets the provider with the specified name.</summary>
      <returns>The provider with the specified name.</returns>
      <param name="name">The key by which the provider is identified.</param>
    </member>
    <member name="M:System.Configuration.Provider.ProviderCollection.Remove(System.String)">
      <summary>Removes a provider from the collection.</summary>
      <param name="name">The name of the provider to be removed.</param>
      <exception cref="T:System.NotSupportedException">The collection has been set to read-only.</exception>
    </member>
    <member name="M:System.Configuration.Provider.ProviderCollection.SetReadOnly">
      <summary>Sets the collection to be read-only.</summary>
    </member>
    <member name="P:System.Configuration.Provider.ProviderCollection.SyncRoot">
      <summary>Gets the current object.</summary>
      <returns>The current object.</returns>
    </member>
    <member name="M:System.Configuration.Provider.ProviderCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Configuration.Provider.ProviderCollection" /> to an array, starting at a particular array index.</summary>
      <param name="array">The array to copy the elements of the collection to.</param>
      <param name="index">The index of the array at which to start copying provider instances from the collection.</param>
    </member>
    <member name="T:System.Configuration.Provider.ProviderException">
      <summary>The exception that is thrown when a configuration provider error has occurred. This exception class is also used by providers to throw exceptions when internal errors occur within the provider that do not map to other pre-existing exception classes.</summary>
    </member>
    <member name="M:System.Configuration.Provider.ProviderException.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Configuration.Provider.ProviderException" /> class.</summary>
    </member>
    <member name="M:System.Configuration.Provider.ProviderException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Creates a new instance of the <see cref="T:System.Configuration.Provider.ProviderException" /> class.</summary>
      <param name="info">The object that holds the information to deserialize.</param>
      <param name="context">Contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Configuration.Provider.ProviderException.#ctor(System.String)">
      <summary>Creates a new instance of the <see cref="T:System.Configuration.Provider.ProviderException" /> class.</summary>
      <param name="message">A message describing why this <see cref="T:System.Configuration.Provider.ProviderException" /> was thrown.</param>
    </member>
    <member name="M:System.Configuration.Provider.ProviderException.#ctor(System.String,System.Exception)">
      <summary>Creates a new instance of the <see cref="T:System.Configuration.Provider.ProviderException" /> class.</summary>
      <param name="message">A message describing why this <see cref="T:System.Configuration.Provider.ProviderException" /> was thrown.</param>
      <param name="innerException">The exception that caused this <see cref="T:System.Configuration.Provider.ProviderException" /> to be thrown.</param>
    </member>
  </members>
</doc>