<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Web.WebView2.Core</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2">
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
            <summary>
            WebView2 enables you to host web content using the latest Microsoft Edge browser and web technology.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.PrintToPdfAsync(System.String,Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings)">
             <summary>
             Print the current page to PDF asynchronously with the provided settings.
             </summary>
             <remarks>
             See <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings"/> for description of settings. Passing null for <c>printSettings</c> results in default print settings used.
            
             Use <c>resultFilePath</c> to specify the path to the PDF file. The host should provide an absolute path, including file name. If the path points to an existing file, the file will be overwritten. If the path is not valid, the method fails.
            
             The async PrintToPdf operation completes when the data has been written to the PDF file. If the application exits before printing is complete, the file is not saved. Only one PrintToPdf operation can be in progress at a time.
             If PrintToPdf is called while a print to PDF operation is in progress, the operation completes and returns false.
             </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.Settings">
            <summary>
            Gets the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Settings"/> object contains various modifiable settings for the running WebView.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.Source">
            <summary>
            Gets the URI of the current top level document.
            </summary>
            <remarks>
            This value potentially changes as a part of the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.SourceChanged"/> event raised for some cases such as navigating to a different site or fragment navigations. It remains the same for other types of navigations such as page refreshes or <c>history.pushState</c> with the same URL as the current page.
            </remarks>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.SourceChanged"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.BrowserProcessId">
            <summary>
            Gets the process ID of the browser process that hosts the WebView.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoBack">
            <summary>
            <c>true</c> if the WebView is able to navigate to a previous page in the navigation history.
            </summary>
            <remarks>
            If CanGoBack changes value, the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.HistoryChanged"/> event is raised.
            </remarks>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.HistoryChanged"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoForward">
            <summary>
            <c>true</c> if the WebView is able to navigate to a next page in the navigation history.
            </summary>
            <remarks>
            If CanGoForward changes value, the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.HistoryChanged"/> event is raised.
            </remarks>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.HistoryChanged"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.DocumentTitle">
            <summary>
            Gets the title for the current top-level document.
            </summary>
            <remarks>
            If the document has no explicit title or is otherwise empty, a default that may or may not match the URI of the document is used.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="DocumentTitle":::
            </example>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.ContainsFullScreenElement">
            <summary>
            Indicates if the WebView contains a fullscreen HTML element.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting">
            <summary>
            NavigationStarting is raised when the WebView main frame is requesting permission to navigate to a different URI.
            </summary>
            <remarks>
            Redirects raise this event as well, and the navigation id is the same as the original one. You may block corresponding navigations until the event handler returns.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="NavigationStarting":::
            </example>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading">
            <summary>
            ContentLoading is raised before any content is loaded, including scripts added with <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync(System.String)"/>. ContentLoading is not raised if a same page navigation occurs (such as through fragment navigations or <c>history.pushState</c> navigations).
            </summary>
            <remarks>
            This operation follows the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> and <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.SourceChanged"/> events and precedes the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.HistoryChanged"/> and <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationCompleted"/> events.
            </remarks>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.SourceChanged"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.HistoryChanged"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationCompleted"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.SourceChanged">
            <summary>
            SourceChanged is raised when the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.Source"/> property changes.
            </summary>
            <remarks>
            SourceChanged is raised when navigating to a different site or fragment navigations. It is not raised for other types of navigations such as page refreshes or <c>history.pushState</c> with the same URL as the current page. This event is raised before <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/> for navigation to a new document.
            </remarks>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.Source"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.HistoryChanged">
            <summary>
            HistoryChanged is raised for changes to joint session history, which consists of top-level and manual frame navigations.
            </summary>
            <remarks>
            Use HistoryChanged to verify that the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoBack"/> or <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoForward"/> value has changed. HistoryChanged is also raised for using <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.GoBack"/> or <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.GoForward"/>. HistoryChanged is raised after <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.SourceChanged"/> and <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/>. CanGoBack is false for navigations initiated through CoreWebView2Frame APIs if there has not yet been a user gesture.
            </remarks>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoBack"/>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoForward"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.SourceChanged"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationCompleted">
            <summary>
            NavigationCompleted is raised when the WebView has completely loaded (<c>body.onload</c> has been raised) or loading stopped with error.
            </summary>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="NavigationCompleted":::
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="DOMContentLoaded":::
            </example>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.FrameNavigationStarting">
            <summary>
            FrameNavigationStarting is raised when a child frame in the WebView requests permission to navigate to a different URI.
            </summary>
            <remarks>
            Redirects raise this operation as well, and the navigation id is the same as the original one. You may block corresponding navigations until the event handler returns.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.FrameNavigationCompleted">
            <summary>
            FrameNavigationCompleted is raised when a child frame has completely loaded (<c>body.onload</c> has been raised) or loading stopped with error.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.ScriptDialogOpening">
            <summary>
            ScriptDialogOpening is raised when a JavaScript dialog (<c>alert</c>, <c>confirm</c>, <c>prompt</c>, or <c>beforeunload</c>) displays for the WebView.
            </summary>
            <remarks>
            This event only is raised if the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.AreDefaultScriptDialogsEnabled"/> property is set to <c>false</c>. This event suppresses dialogs or replaces default dialogs with custom dialogs.
            
            If a deferral is not taken on the event args, the subsequent scripts are blocked until the event handler returns. If a deferral is taken, the scripts are blocked until the deferral is completed.
            </remarks>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.AreDefaultScriptDialogsEnabled"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.PermissionRequested">
            <summary>
            PermissionRequested is raised when content in a WebView requests permission to access some privileged resources.
            </summary>
            <remarks>
            If a deferral is not taken on the event args, the subsequent scripts are blocked until the event handler returns. If a deferral is taken, the scripts are blocked until the deferral is completed.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.ProcessFailed">
            <summary>
            ProcessFailed is raised when a WebView process ends unexpectedly or becomes unresponsive.
            </summary>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="ProcessFailed":::
            </example>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebMessageReceived">
            <summary>
            WebMessageReceived is raised when the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsWebMessageEnabled"/> setting is set and the top-level document of the WebView runs <c>window.chrome.webview.postMessage</c>.
            </summary>
            <remarks>
            The <c>postMessage</c> function is <c>void postMessage(object)</c> where object is any object supported by JSON conversion.
            When <c>postMessage</c> is called, the handler's Invoke method will be called with the <c>object</c> parameter <c>postMessage</c> converted to a JSON string.
            If the same page calls <c>postMessage</c> multiple times, the corresponding <c>WebMessageReceived</c> events are guaranteed to be fired in the same order. However, if multiple frames call <c>postMessage</c>, there is no guaranteed order. In addition, <c>WebMessageReceived</c> events caused by calls to <c>postMessage</c> are not guaranteed to be sequenced with events caused by DOM APIs. For example, if the page runs
            <code>
            chrome.webview.postMessage("message");
            window.open();
            </code>
            then the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NewWindowRequested"/> event might be fired before the <c>WebMessageReceived</c> event. If you need the <c>WebMessageReceived</c> event to happen before anything else, then in the <c>WebMessageReceived</c> handler you can post a message back to the page and have the page wait until it receives that message before continuing.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.NewWindowRequested">
            <summary>
            NewWindowRequested is raised when content inside the WebView requests to open a new window, such as through <c>window.open()</c>.
            </summary>
            <remarks>
            The app passes a target WebView that is considered the opened window.
            If a deferral is not taken on the event args, scripts that resulted in the new window that are requested are blocked until the event handler returns. If a deferral is taken, then scripts are blocked until the deferral is completed.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.DocumentTitleChanged">
            <summary>
            DocumentTitleChanged is raised when the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.DocumentTitle"/> property changes and may be raised before or after the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationCompleted"/> event.
            </summary>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="DocumentTitleChanged":::
            </example>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.DocumentTitle"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContainsFullScreenElementChanged">
            <summary>
            ContainsFullScreenElementChanged is raised when the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.ContainsFullScreenElement"/> property changes.
            </summary>
            <remarks>
            An HTML element inside the WebView may enter fullscreen to the size of the WebView or leave fullscreen. This event is useful when, for example, a video element requests to go fullscreen. The listener of this event may resize the WebView in response.
            </remarks>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.ContainsFullScreenElement"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested">
            <summary>
            WebResourceRequested is raised when the WebView is performing a URL request to a matching URL and resource context filter that was added with <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.AddWebResourceRequestedFilter(System.String,Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext)"/>.
            </summary>
            <remarks>
            At least one filter must be added for the event to be raised.
            The web resource requested may be blocked until the event handler returns if a deferral is not taken on the event args. If a deferral is taken, then the web resource requested is blocked until the deferral is completed.
            
            If this event is subscribed in the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NewWindowRequested"/> handler it should be called after the new window is set. For more details see <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2NewWindowRequestedEventArgs.NewWindow"/>.
            
            Currently this only supports file, http, and https URI schemes.
            </remarks>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.AddWebResourceRequestedFilter(System.String,Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext)"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.WindowCloseRequested">
            <summary>
            WindowCloseRequested is raised when content inside the WebView requested to close the window, such as after <c>window.close()</c> is run.
            </summary>
            <remarks>
            The app should close the WebView and related app window if that makes sense to the app.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.Navigate(System.String)">
            <summary>
            Causes a navigation of the top level document to the specified URI.
            </summary>
            <param name="uri">The URI to navigate to.</param>
            <remarks>
            For more information, navigate to [Navigation event](/microsoft-edge/webview2/concepts/navigation-events). Note that this operation starts a navigation and the corresponding <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event is raised sometime after Navigate runs.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="Navigate":::
            </example>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationCompleted"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.NavigateToString(System.String)">
            <summary>
            Initiates a navigation to <c>htmlContent</c> as source HTML of a new document.
            </summary>
            <param name="htmlContent">A source HTML of a new document.</param>
            <remarks>
            The <c>htmlContent</c> parameter may not be larger than 2 MB (2 * 1024 * 1024 bytes) in total size. The origin of the new page is <c>about:blank</c>.
            </remarks>
            <example>
            <code>
            webView.CoreWebView2.SetVirtualHostNameToFolderMapping(
                "appassets.example", "assets", CoreWebView2HostResourceAccessKind.DenyCors);
            string htmlContent =
            @"
                <head><link rel='stylesheet' href ='http://appassets.example/wv2.css' /></head>
                <body>
                    <img src='http://appassets.example/wv2.png' />
                    <p><a href='http://appassets.example/winrt_test.txt'> Click me</a></p>
                </body>
            ";
            webview.NavigateToString(htmlContent);
            </code>
            </example>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.SetVirtualHostNameToFolderMapping(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2HostResourceAccessKind)"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationCompleted"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync(System.String)">
            <summary>
            Adds the provided JavaScript to a list of scripts that should be run after the global object has been created, but before the HTML document has been parsed and before any other script included by the HTML document is run.
            </summary>
            <param name="javaScript">The JavaScript code to be run.</param>
            <returns>A script ID that may be passed when calling <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.RemoveScriptToExecuteOnDocumentCreated(System.String)"/>.</returns>
            <remarks>
            The injected script will apply to all future top level document and child frame navigations until removed with <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.RemoveScriptToExecuteOnDocumentCreated(System.String)"/>.
            This is applied asynchronously and you must wait for the returned <see cref="T:System.Threading.Tasks.Task`1"/> to complete before you can be sure that the script is ready to execute on future navigations.
            
            Note that if an HTML document has sandboxing of some kind via [sandbox](https://developer.mozilla.org/docs/Web/HTML/Element/iframe#attr-sandbox) properties or the [Content-Security-Policy HTTP header](https://developer.mozilla.org/docs/Web/HTTP/Headers/Content-Security-Policy) this will affect the script run here. So, for example, if the <c>allow-modals</c> keyword is not set then calls to the <c>alert</c> function will be ignored.
            </remarks>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.RemoveScriptToExecuteOnDocumentCreated(System.String)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.RemoveScriptToExecuteOnDocumentCreated(System.String)">
            <summary>
            Removes the corresponding JavaScript added via <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync(System.String)"/> with the specified script ID.
            </summary>
            <param name="id">The ID corresponds to the JavaScript code to be removed from the list of scripts.</param>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.ExecuteScriptAsync(System.String)">
            <summary>
            Runs JavaScript code from the <c>javaScript</c> parameter in the current top-level document rendered in the WebView.
            </summary>
            <param name="javaScript">The JavaScript code to be run in the current top-level document rendered in the WebView.</param>
            <returns>A JSON encoded string that represents the result of running the provided JavaScript.</returns>
            <remarks>
            If the result is <c>undefined</c>, contains a reference cycle, or otherwise is not able to be encoded into JSON, the JSON <c>null</c> value is returned as the <c>"null"</c> string.
            
            A function that has no explicit return value returns <c>undefined</c>. If the script that was run throws an unhandled exception, then the result is also <c>null</c>. This method is applied asynchronously. If the method is run after the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event during a navigation, the script runs in the new document when loading it, around the time <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/> is run. This operation works even if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsScriptEnabled"/> is set to <c>false</c>.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="ExecuteScript":::
            </example>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsScriptEnabled"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.CapturePreviewAsync(Microsoft.Web.WebView2.Core.CoreWebView2CapturePreviewImageFormat,System.IO.Stream)">
            <summary>
            Captures an image of what WebView is displaying.
            </summary>
            <param name="imageFormat">The format of the image to be captured.</param>
            <param name="imageStream">The stream to which the resulting image binary data is written.</param>
            <remarks>
            When CapturePreviewAsync finishes writing to the stream, the Invoke method on the provided handler parameter is called. This method fails if called before the first <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/> event. For example if this is called in the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event for the first navigation it will fail. For subsequent navigations, the method may not fail, but will not capture an image of a given webpage until the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/> event has been fired for it. Any call to this method prior to that will result in a capture of the page being navigated away from.
            </remarks>
            <seealso cref="T:Microsoft.Web.WebView2.Core.CoreWebView2CapturePreviewImageFormat"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.Reload">
            <summary>
            Reloads the current page.
            </summary>
            <remarks>
            This is similar to navigating to the URI of current top level document including all navigation events firing and respecting any entries in the HTTP cache. But, the back or forward history will not be modified.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.PostWebMessageAsJson(System.String)">
            <summary>
            Posts the specified <c>webMessageAsJson</c> to the top level document in this WebView.
            </summary>
            <param name="webMessageAsJson">The web message to be posted to the top level document in this WebView.</param>
            <remarks>
            The event args is an instance of <c>MessageEvent</c>. The <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsWebMessageEnabled"/> setting must be <c>true</c> or this method will fail with E_INVALIDARG. The event arg's <c>data</c> property of the event arg is the <c>webMessageAsJson</c> string parameter parsed as a JSON string into a JavaScript object. The event arg's <c>source</c> property of the event arg is a reference to the <c>window.chrome.webview</c> object. For information about sending messages from the HTML document in the WebView to the host, navigate to <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebMessageReceived"/>. The message is sent asynchronously. If a navigation occurs before the message is posted to the page, the message is not be sent.
            </remarks>
            <example>
            Runs the message event of the <c>window.chrome.webview</c> of the top-level document. JavaScript in that document may subscribe and unsubscribe to the event using the following code:
            <code>
            window.chrome.webview.addEventListener('message', handler)
            window.chrome.webview.removeEventListener('message', handler)
            </code>
            </example>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsWebMessageEnabled"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebMessageReceived"/>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.PostWebMessageAsString(System.String)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.PostWebMessageAsString(System.String)">
            <summary>
            Posts a message that is a simple string rather than a JSON string representation of a JavaScript object.
            </summary>
            <param name="webMessageAsString">The web message to be posted to the top level document in this WebView.</param>
            <remarks>
            This behaves in exactly the same manner as <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.PostWebMessageAsJson(System.String)"/>, but the <c>data</c> property of the event arg of the <c>window.chrome.webview</c> message is a string with the same value as <c>webMessageAsString</c>. Use this instead of <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.PostWebMessageAsJson(System.String)"/> if you want to communicate using simple strings rather than JSON objects.
            </remarks>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebMessageReceived"/>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.PostWebMessageAsJson(System.String)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.CallDevToolsProtocolMethodAsync(System.String,System.String)">
            <summary>
            Runs an asynchronous DevToolsProtocol method.
            </summary>
            <param name="methodName">The full name of the method in the format <c>{domain}.{method}</c>.</param>
            <param name="parametersAsJson">A JSON formatted string containing the parameters for the corresponding method.</param>
            <returns>A JSON string that represents the method's return object.</returns>
            <remarks>
            For more information about available methods, navigate to [DevTools Protocol Viewer](https://aka.ms/DevToolsProtocolDocs). The handler's Invoke method will be called when the method asynchronously completes. Invoke will be called with the method's return object as a JSON string.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.GoBack">
            <summary>
            Navigates the WebView to the previous page in the navigation history.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.GoForward">
            <summary>
            Navigates the WebView to the next page in the navigation history.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.GetDevToolsProtocolEventReceiver(System.String)">
            <summary>
            Gets a DevTools Protocol event receiver that allows you to subscribe to a DevToolsProtocol event.
            </summary>
            <param name="eventName">The full name of the event in the format <c>{domain}.{event}</c>.</param>
            <returns>A Devtools Protocol event receiver.</returns>
            <remarks>
            For more information about DevToolsProtocol events description and event args, navigate to [DevTools Protocol Viewer](https://aka.ms/DevToolsProtocolDocs).
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.Stop">
            <summary>
            Stops all navigations and pending resource fetches.
            </summary>
            <remarks>
            Does not stop scripts.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.AddHostObjectToScript(System.String,System.Object)">
            <summary>
            Adds the provided host object to script running in the WebView with the specified name.
            </summary>
            <param name="name">The name of the host object.</param>
            <param name="rawObject">The host object to be added to script.</param>
            <remarks>
            Host objects are exposed as host object proxies via <c>window.chrome.webview.hostObjects.{name}</c>. Host object proxies are promises and will resolve to an object representing the host object. Only the COM visible objects/properties/methods can be accessed from script.
            The app can control which part of .NET objects are exposed using <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute"/>.
            
            JavaScript code in the WebView will be able to access appObject as following and then access attributes and methods of appObject.
            
            Note that while simple types, <c>IDispatch</c> and array are supported, and <c>IUnknown</c> objects that also implement <c>IDispatch</c> are treated as <c>IDispatch</c>, generic <c>IUnknown</c>, <c>VT_DECIMAL</c>, or <c>VT_RECORD</c> variant is not supported. Remote JavaScript objects like callback functions are represented as an <c>VT_DISPATCH</c> VARIANT with the object implementing <c>IDispatch</c>. The JavaScript callback method may be invoked using DISPID_VALUE for the DISPID. Nested arrays are supported up to a depth of 3. Arrays of by reference types are not supported. <c>VT_EMPTY</c> and <c>VT_NULL</c> are mapped into JavaScript as <c>null</c>. In JavaScript <c>null</c> and <c>undefined</c> are mapped to <c>VT_EMPTY</c>.
            
            Additionally, all host objects are exposed as <c>window.chrome.webview.hostObjects.sync.{name}</c>. Here the host objects are exposed as synchronous host object proxies. These are not promises and calls to functions or property access synchronously block running script waiting to communicate cross process for the host code to run. Accordingly this can result in reliability issues and it is recommended that you use the promise based asynchronous <c>window.chrome.webview.hostObjects.{name}</c> API described above.
            
            Synchronous host object proxies and asynchronous host object proxies can both proxy the same host object. Remote changes made by one proxy will be reflected in any other proxy of that same host object whether the other proxies and synchronous or asynchronous.
            
            While JavaScript is blocked on a synchronous call to native code, that native code is unable to call back to JavaScript. Attempts to do so will fail with <c>HRESULT_FROM_WIN32(ERROR_POSSIBLE_DEADLOCK)</c>.
            
            Host object proxies are JavaScript Proxy objects that intercept all property get, property set, and method invocations. Properties or methods that are a part of the Function or Object prototype are run locally. Additionally any property or method in the array <c>chrome.webview.hostObjects.options.forceLocalProperties</c> will also be run locally. This defaults to including optional methods that have meaning in JavaScript like <c>toJSON</c> and <c>Symbol.toPrimitive</c>. You can add more to this array as required.
            
            There's a method <c>chrome.webview.hostObjects.cleanupSome</c> that will best effort garbage collect host object proxies.
            
            The <c>chrome.webview.hostObjects.options</c> object provides the ability to change some functionality of host objects.
            
            <list type="table">
            <listheader>
            <term>Options property</term>
            <description>Details</description>
            </listheader>
            <item>
            <term><c>forceLocalProperties</c></term>
            <description>
            This is an array of host object property names that will be run locally, instead of being called on the native host object. This defaults to <c>then</c>, <c>toJSON</c>, <c>Symbol.toString</c>, and <c>Symbol.toPrimitive</c>. You can add other properties to specify that they should be run locally on the JavaScript host object proxy.
            </description>
            </item>
            <item>
            <term><c>log</c></term>
            <description>
            This is a callback that will be called with debug information. For example, you can set this to <c>console.log.bind(console)</c> to have it print debug information to the console to help when troubleshooting host object usage. By default this is null.
            </description>
            </item>
            <item>
            <term><c>shouldSerializeDates</c></term>
            <description>
            By default this is false, and JavaScript Date objects will be sent to host objects as a string using <c>JSON.stringify</c>. You can set this property to true to have Date objects properly serialize as a <c>System.DateTime</c> when sending to the .NET host object, and have <c>System.DateTime</c> properties and return values create a JavaScript <c>Date</c> object.
            </description>
            </item>
            <item>
            <term><c>defaultSyncProxy</c></term>
            <description>
            When calling a method on a synchronous proxy, the result should also be a synchronous proxy. But in some cases, the sync/async context is lost (for example, when providing to native code a reference to a function, and then calling that function in native code). In these cases, the proxy will be asynchronous, unless this property is set.
            </description>
            </item>
            <item>
            <term><c>forceAsyncMethodMatches</c></term>
            <description>
            This is an array of regular expressions. When calling a method on a synchronous proxy, the method call will be performed asynchronously if the method name matches a string or regular expression in this array. Setting this value to <c>Async</c> will make any method that ends with Async be an asynchronous method call. If an async method doesn't match here and isn't forced to be asynchronous, the method will be invoked synchronously, blocking execution of the calling JavaScript and then returning the resolution of the promise, rather than returning a promise.
            </description>
            </item>
            <item>
            <term><c>ignoreMemberNotFoundError</c></term>
            <description>
            By default, an exception is thrown when attempting to get the value of a proxy property that doesn't exist on the corresponding native class. Setting this property to <c>true</c> switches the behavior to match Chakra WinRT projection (and general JavaScript) behavior of returning <c>undefined</c> with no error.
            </description>
            </item>
            </list>
            
            Host object proxies additionally have the following methods which run locally:
            
            <list type="table">
            <listheader>
            <term>Method name</term>
            <description>Details</description>
            </listheader>
            <item>
            <term><c>applyHostFunction</c>, <c>getHostProperty</c>, <c>setHostProperty</c></term>
            <description>
            Perform a method invocation, property get, or property set on the host object. You can use these to explicitly force a method or property to run remotely if there is a conflicting local method or property. For instance, <c>proxy.toString()</c> will run the local <c>toString</c> method on the proxy object. But <c>proxy.applyHostFunction('toString')</c> runs <c>toString</c> on the host proxied object instead.
            </description>
            </item>
            <item>
            <term><c>getLocalProperty</c>, <c>setLocalProperty</c></term>
            <description>
            Perform property get, or property set locally. You can use these methods to force getting or setting a property on the host object proxy itself rather than on the host object it represents. For instance, <c>proxy.unknownProperty</c> will get the property named <c>unknownProperty</c> from the host proxied object. But <c>proxy.getLocalProperty('unknownProperty')</c> will get the value of the property <c>unknownProperty</c> on the proxy object itself.
            </description>
            </item>
            <item>
            <term><c>sync</c></term>
            <description>
            Asynchronous host object proxies expose a sync method which returns a promise for a synchronous host object proxy for the same host object. For example, <c>chrome.webview.hostObjects.sample.methodCall()</c> returns an asynchronous host object proxy. You can use the <c>sync</c> method to obtain a synchronous host object proxy instead:
            <c>const syncProxy = await chrome.webview.hostObjects.sample.methodCall().sync()</c>
            </description>
            </item>
            <item>
            <term><c>async</c></term>
            <description>
            Synchronous host object proxies expose an async method which blocks and returns an asynchronous host object proxy for the same host object. For example, <c>chrome.webview.hostObjects.sync.sample.methodCall()</c> returns a synchronous host object proxy. Calling the <c>async</c> method on this blocks and then returns an asynchronous host object proxy for the same host object: <c>const asyncProxy = chrome.webview.hostObjects.sync.sample.methodCall().async()</c>
            </description>
            </item>
            <item>
            <term><c>then</c></term>
            <description>
            Asynchronous host object proxies have a then method. This allows them to be awaitable. <c>then</c> will return a promise that resolves with a representation of the host object. If the proxy represents a JavaScript literal then a copy of that is returned locally. If the proxy represents a function then a non-awaitable proxy is returned. If the proxy represents a JavaScript object with a mix of literal properties and function properties, then the a copy of the object is returned with some properties as host object proxies.
            </description>
            </item>
            </list>
            
            All other property and method invocations (other than the above Remote object proxy methods, <c>forceLocalProperties</c> list, and properties on Function and Object prototypes) are run remotely. Asynchronous host object proxies return a promise representing asynchronous completion of remotely invoking the method, or getting the property. The promise resolves after the remote operations complete and the promises resolve to the resulting value of the operation. Synchronous host object proxies work similarly but block JavaScript execution and wait for the remote operation to complete.
            
            Setting a property on an asynchronous host object proxy works slightly differently. The set returns immediately and the return value is the value that will be set. This is a requirement of the JavaScript Proxy object. If you need to asynchronously wait for the property set to complete, use the <c>setHostProperty</c> method which returns a promise as described above. Synchronous object property set property synchronously blocks until the property is set.
            
            Exposing host objects to script has security risk. Please follow [best practices](/microsoft-edge/webview2/concepts/security).
            </remarks>
            <example>
            To create a [IDispatch](/windows/win32/api/oaidl/nn-oaidl-idispatch) implementing class in C# use the following attributes on each class you intend to expose.
            <code>
            // Bridge and BridgeAnotherClass are C# classes that implement IDispatch and works with AddHostObjectToScript.
            [ClassInterface(ClassInterfaceType.AutoDual)]
            [ComVisible(true)]
            public class BridgeAnotherClass
            {
                // Sample property.
                public string Prop { get; set; } = "Example";
            }
            
            [ClassInterface(ClassInterfaceType.AutoDual)]
            [ComVisible(true)]
            public class Bridge
            {
                public string Func(string param)
                {
                    return "Example: " + param;
                }
            
                public BridgeAnotherClass AnotherObject { get; set; } = new BridgeAnotherClass();
            
                // Sample indexed property.
                [System.Runtime.CompilerServices.IndexerName("Items")]
                public string this[int index]
                {
                    get { return m_dictionary[index]; }
                    set { m_dictionary[index] = value; }
                }
                private Dictionary&lt;int, string&gt; m_dictionary = new Dictionary&lt;int, string&gt;();
            }
            </code>
            Then add instances of those classes via <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.AddHostObjectToScript(System.String,System.Object)"/>:
            <code>
            webView.CoreWebView2.AddHostObjectToScript("bridge", new Bridge());
            </code>
            And then in script you can call the methods, and access those properties of the objects added via <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.AddHostObjectToScript(System.String,System.Object)"/>:
            <code>
            // Find added objects on the hostObjects property
            const bridge = chrome.webview.hostObjects.bridge;
            
            // Call a method and pass in a parameter.
            // The result is another proxy promise so you must await to get the result.
            console.log(await bridge.Func("testing..."));
            
            // A property may be another object as long as its class also implements
            // IDispatch.
            // Getting a property also gets a proxy promise you must await.
            const propValue = await bridge.AnotherObject.Prop;
            console.log(propValue);
            
            // Indexed properties
            let index = 123;
            bridge[index] = "test";
            let result = await bridge[index];
            console.log(result);
            </code>
            </example>
            <seealso cref="T:System.Runtime.InteropServices.ComVisibleAttribute"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.RemoveHostObjectFromScript(System.String)">
            <summary>
            Removes the host object specified by the name so that it is no longer accessible from JavaScript code in the WebView.
            </summary>
            <param name="name">The name of the host object to be removed.</param>
            <remarks>
            While new access attempts are denied, if the object is already obtained by JavaScript code in the WebView, the JavaScript code continues to have access to that object. Running this method for a name that is already removed or never added fails.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.OpenDevToolsWindow">
            <summary>
            Opens the DevTools window for the current document in the WebView.
            </summary>
            <remarks>
            Does nothing if run when the DevTools window is already open.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.AddWebResourceRequestedFilter(System.String,Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext)">
            <summary>
            Adds a URI and resource context filter for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event.
            </summary>
            <param name="uri">An URI to be added to the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event.</param>
            <param name="ResourceContext">A resource context filter to be added to the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event.</param>
            <remarks>
            A web resource request with a resource context that matches this filter's resource context and a URI that matches this filter's URI wildcard string will be raised via the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event.
            
            The <c>uri</c> parameter value is a wildcard string matched against the URI of the web resource request. This is a glob style wildcard string in which a <c>*</c> matches zero or more characters and a <c>?</c> matches exactly one character.
            These wildcard characters can be escaped using a backslash just before the wildcard character in order to represent the literal <c>*</c> or <c>?</c>.
            
            The matching occurs over the URI as a whole string and not limiting wildcard matches to particular parts of the URI.
            The wildcard filter is compared to the URI after the URI has been normalized, any URI fragment has been removed, and non-ASCII hostnames have been converted to punycode.
            
            Specifying a <c>nullptr</c> for the uri is equivalent to an empty string which matches no URIs.
            
            For more information about resource context filters, navigate to <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext"/>.
            
            <list type="table">
            <listheader>
            <description>URI Filter String</description>
            <description>Request URI</description>
            <description>Match</description>
            <description>Notes</description>
            </listheader>
            <item>
            <description><c>*</c></description>
            <description><c>https://contoso.com/a/b/c</c></description>
            <description>Yes</description>
            <description>A single * will match all URIs</description>
            </item>
            <item>
            <description><c>*://contoso.com/*</c></description>
            <description><c>https://contoso.com/a/b/c</c></description>
            <description>Yes</description>
            <description>Matches everything in contoso.com across all schemes</description>
            </item>
            <item>
            <description><c>*://contoso.com/*</c></description>
            <description><c>https://example.com/?https://contoso.com/</c></description>
            <description>Yes</description>
            <description>But also matches a URI with just the same text anywhere in the URI</description>
            </item>
            <item>
            <description><c>example</c></description>
            <description><c>https://contoso.com/example</c></description>
            <description>No</description>
            <description>The filter does not perform partial matches</description>
            </item>
            <item>
            <description><c>*example</c></description>
            <description><c>https://contoso.com/example</c></description>
            <description>Yes</description>
            <description>The filter matches across URI parts </description>
            </item>
            <item>
            <description><c>*example</c></description>
            <description><c>https://contoso.com/path/?example</c></description>
            <description>Yes</description>
            <description>The filter matches across URI parts</description>
            </item>
            <item>
            <description><c>*example</c></description>
            <description><c>https://contoso.com/path/?query#example</c></description>
            <description>No</description>
            <description>The filter is matched against the URI with no fragment</description>
            </item>
            <item>
            <description><c>*example</c></description>
            <description><c>https://example</c></description>
            <description>No</description>
            <description>The URI is normalized before filter matching so the actual URI used for comparison is <c>https://example.com/</c></description>
            </item>
            <item>
            <description><c>*example/</c></description>
            <description><c>https://example</c></description>
            <description>Yes</description>
            <description>Just like above, but this time the filter ends with a / just like the normalized URI</description>
            </item>
            <item>
            <description><c>https://xn--qei.example/</c></description>
            <description><c>https://&#x2764;.example/</c></description>
            <description>Yes</description>
            <description>Non-ASCII hostnames are normalized to punycode before wildcard comparison</description>
            </item>
            <item>
            <description><c>https://&#x2764;.example/</c></description>
            <description><c>https://xn--qei.example/</c></description>
            <description>No</description>
            <description>Non-ASCII hostnames are normalized to punycode before wildcard comparison</description>
            </item>
            </list>
            </remarks>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.RemoveWebResourceRequestedFilter(System.String,Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext)"/>
            <seealso cref="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.RemoveWebResourceRequestedFilter(System.String,Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext)">
            <summary>
            Removes a matching WebResource filter that was previously added for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event.
            </summary>
            <param name="uri">An URI to at which a web resource filter was added.</param>
            <param name="ResourceContext">A previously added resource context filter to be removed.</param>
            <exception cref="T:System.ArgumentException">A filter that was never added.</exception>
            <remarks>
            If the same filter was added multiple times, then it must need to be removed as many times as it was added for the removal to be effective.
            </remarks>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.AddWebResourceRequestedFilter(System.String,Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext)"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.CookieManager">
            <summary>
            Gets the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2CookieManager"/> object associated with this <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/>.
            </summary>
            <seealso cref="T:Microsoft.Web.WebView2.Core.CoreWebView2CookieManager"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.Environment">
            <summary>
            Exposes the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> used to create this <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/>.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceResponseReceived">
            <summary>
            WebResourceResponseReceived is raised when the WebView receives the response for a request for a web resource (any URI resolution performed by the WebView; such as HTTP/HTTPS, file and data requests from redirects, navigations, declarations in HTML, implicit Favicon lookups, and fetch API usage in the document).
            </summary>
            <remarks>
            The host app can use this event to view the actual request and response for a web resource. There is no guarantee about the order in which the WebView processes the response and the host app's handler runs. The app's handler will not block the WebView from processing the response.
            The event args include the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequest"/> as sent by the wire and <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponse"/> received, including any additional headers added by the network stack that were not be included as part of the associated <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event, such as Authentication headers.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="WebResourceResponseReceived":::
            </example>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.DOMContentLoaded">
            <summary>
            DOMContentLoaded is raised when the initial HTML document has been parsed.
            </summary>
            <remarks>
            This aligns with the the document's DOMContentLoaded event in HTML.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="DOMContentLoaded":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.NavigateWithWebResourceRequest(Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequest)">
            <summary>
            Navigates using a constructed <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequest"/> object.
            </summary>
            <param name="Request">The constructed web resource object to provide post data or additional request headers during navigation.</param>
            <remarks>
            The headers in the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequest"/> override headers added by WebView2 runtime except for Cookie headers. Method can only be either <c>GET</c> or <c>POST</c>. Provided post data will only be sent only if the method is <c>POST</c> and the uri scheme is <c>HTTP(S)</c>.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="NavigateWithWebResourceRequest":::
            </example>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.IsSuspended">
            <summary>
            Whether WebView is suspended.
            </summary>
            <remarks>
            True when WebView is suspended, from the time when <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.TrySuspendAsync"/> has completed successfully until WebView is resumed.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.TrySuspendAsync">
            <summary>
            An app may call this API to have the WebView2 consume less memory.
            </summary>
            <remarks>
            This is useful when a Win32 app becomes invisible, or when a Universal Windows Platform app is being suspended, during the suspended event handler before completing the suspended event.
            
            The <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.IsVisible"/> property must be false when the API is called. Otherwise, the API throws COMException with error code of <c>HRESULT_FROM_WIN32(ERROR_INVALID_STATE)</c>.
            
            Suspending is similar to putting a tab to sleep in the Edge browser. Suspending pauses WebView script timers and animations, minimizes CPU usage for the associated browser renderer process and allows the operating system to reuse the memory that was used by the renderer process for other processes.
            
            Note that Suspend is best effort and considered completed successfully once the request is sent to browser renderer process. If there is a running script, the script will continue to run and the renderer process will be suspended after that script is done.
            
            See [Sleeping Tabs FAQ](https://techcommunity.microsoft.com/t5/articles/sleeping-tabs-faq/m-p/1705434) for conditions that might prevent WebView from being suspended. In those situations, the result of the async task is false.
            
            The WebView will be automatically resumed when it becomes visible. Therefore, the app normally does not have to call <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Resume"/> explicitly.
            
            The app can call <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Resume"/> and then <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.TrySuspendAsync"/> periodically for an invisible WebView so that the invisible WebView can sync up with latest data and the page ready to show fresh content when it becomes visible.
            
            All WebView APIs can still be accessed when a WebView is suspended. Some APIs like Navigate will auto resume the WebView. To avoid unexpected auto resume, check <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.IsSuspended"/> property before calling APIs that might change WebView state.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="TrySuspend":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.Resume">
            <summary>
            Resumes the WebView so that it resumes activities on the web page.
            </summary>
            <remarks>
            This API can be called while the WebView2 controller is invisible.
            
            The app can interact with the WebView immediately after <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Resume"/>.
            
            WebView will be automatically resumed when it becomes visible.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="Resume":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.SetVirtualHostNameToFolderMapping(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2HostResourceAccessKind)">
            <summary>
            Sets a mapping between a virtual host name and a folder path to make available to web sites via that host name.
            </summary>
            <param name="hostName">A virtual host name.</param>
            <param name="folderPath">A folder path name to be mapped to the virtual host name. The length must not exceed the Windows MAX_PATH limit.</param>
            <param name="accessKind">The level of access to resources under the virtual host from other sites.</param>
            <remarks>
            <para>
            After setting the mapping, documents loaded in the WebView can use HTTP or HTTPS URLs at the specified host name specified by <c>hostName</c> to access files in the local folder specified by <c>folderPath</c>.
            This mapping applies to both top-level document and iframe navigations as well as subresource references from a document. This also applies to dedicated and shared worker scripts but does not apply to service worker scripts.
            
            Due to a current implementation limitation, media files accessed using virtual host name can be very slow to load.
            
            As the resource loaders for the current page might have already been created and running, changes to the mapping might not be applied to the current page and a reload of the page is needed to apply the new mapping.
            
            Both absolute and relative paths are supported for <c>folderPath</c>. Relative paths are interpreted as relative to the folder where the exe of the app is in.
            </para>
            <para>
            For example, after calling <c>SetVirtualHostNameToFolderMapping("appassets.example", "assets", CoreWebView2HostResourceAccessKind.Deny);</c>, navigating to <c>https://appassets.example/my-local-file.html</c> will show content from my-local-file.html in the assets subfolder located on disk under the same path as the app's executable file.
            
            DOM elements that want to reference local files will have their host reference virtual host in the source. If there are multiple folders being used, define one unique virtual host per folder.
            </para>
            <para>
            You should typically choose virtual host names that are never used by real sites.
            If you own a domain such as <c>example.com</c>, another option is to use a subdomain reserved for the app (like <c>my-app.example.com</c>).
            </para>
            <para>
            [RFC 6761](https://tools.ietf.org/html/rfc6761) has reserved several special-use domain names that are guaranteed to not be used by real sites (for example, <c>.example</c>, <c>.test</c>, and <c>.invalid</c>).
            </para>
            <para>
            Note that using <c>.local</c> as the top-level domain name will work but can cause a delay during navigations. You should avoid using <c>.local</c> if you can.
            </para>
            <para>
            Apps should use distinct domain names when mapping folder from different sources that should be isolated from each other. For instance, the app might use app-file.example for files that ship as part of the app, and book1.example might be used for files containing books from a less trusted source that were previously downloaded and saved to the disk by the app.
            </para>
            <para>
            The host name used in the APIs is canonicalized using Chromium's host name parsing logic before being used internally.
            For more information see [HTML5 2.6 URLs](https://dev.w3.org/html5/spec-LC/urls.html).
            </para>
            <para>
            All host names that are canonicalized to the same string are considered identical.
            For example, <c>EXAMPLE.COM</c> and <c>example.com</c> are treated as the same host name.
            An international host name and its Punycode-encoded host name are considered the same host name. There is no DNS resolution for host name and the trailing '.' is not normalized as part of canonicalization.
            </para>
            <para>
            Therefore <c>example.com</c> and <c>example.com.</c> are treated as different host names. Similarly, <c>virtual-host-name</c> and <c>virtual-host-name.example.com</c> are treated as different host names even if the machine has a DNS suffix of <c>example.com</c>.
            </para>
            <para>
            Specify the minimal cross-origin access necessary to run the app. If there is not a need to access local resources from other origins, use <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2HostResourceAccessKind.Deny"/>.
            </para>
            </remarks>
            <example>
            <code>
            webView.CoreWebView2.SetVirtualHostNameToFolderMapping(
                "appassets.example", "assets", CoreWebView2HostResourceAccessKind.DenyCors);
            webView.Source = new Uri("https://appassets.example/index.html");
            </code>
            
            This in an example on how to embed a local image. For more information see <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.NavigateToString(System.String)"/>.
            <code>
            webView.CoreWebView2.SetVirtualHostNameToFolderMapping(
                "appassets.example", "assets", CoreWebView2HostResourceAccessKind.DenyCors);
            string c_navString = "<img src='http://appassets.example/wv2.png'/>";
            webview.NavigateToString(c_navString);
            </code>
            </example>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.ClearVirtualHostNameToFolderMapping(System.String)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.ClearVirtualHostNameToFolderMapping(System.String)">
            <summary>
            Clears a host name mapping for local folder that was added by <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.SetVirtualHostNameToFolderMapping(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2HostResourceAccessKind)"/>.
            </summary>
            <param name="hostName">The host name to be removed from the mapping.</param>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.SetVirtualHostNameToFolderMapping(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2HostResourceAccessKind)"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.FrameCreated">
            <summary>
            FrameCreated is raised when a new iframe is created. Handle this event to get access to <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Frame"/> objects.
            </summary>
            <seealso cref="T:Microsoft.Web.WebView2.Core.CoreWebView2FrameCreatedEventArgs"/>
            <remarks>
            Use the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.Destroyed"/> to listen for when this iframe goes away.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.DownloadStarting">
            <summary>
            DownloadStarting is raised when a download has begun, blocking the default download dialog, but not blocking the progress of the download.
            </summary>
            <remarks>
            The host can choose to cancel a download, change the result file path, and hide the default download dialog. If download is not handled or canceled, the download is saved to the default path after the event completes with default download dialog shown.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="DownloadStarting":::
            </example>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.ClientCertificateRequested">
            <summary>
            ClientCertificateRequested is raised when WebView2 is making a request to an HTTP server that needs a client certificate for HTTP authentication. Read more about HTTP client certificates at [RFC 8446 The Transport Layer Security (TLS) Protocol Version 1.3](https://tools.ietf.org/html/rfc8446).
            </summary>
            <remarks>
            The host have several options for responding to client certificate requests:
            
            <list type="table">
            <listheader>
            <description>Scenario</description>
            <description>Handled</description>
            <description>Cancel</description>
            <description>SelectedCertificate</description>
            </listheader>
            <item>
            <description>Respond to server with a certificate</description>
            <description>True</description>
            <description>False</description>
            <description>MutuallyTrustedCertificate value</description>
            </item>
            <item>
            <description>Respond to server without certificate</description>
            <description>True</description>
            <description>False</description>
            <description>null</description>
            </item>
            <item>
            <description>Display default client certificate selection dialog prompt</description>
            <description>False</description>
            <description>False</description>
            <description>n/a</description>
            </item>
            <item>
            <description>Cancel the request</description>
            <description>n/a</description>
            <description>True</description>
            <description>n/a</description>
            </item>
            </list>
            
            If the host don't handle the event, WebView2 will show the default client certificate selection dialog prompt to the user.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="ClientCertificateRequested1":::
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="ClientCertificateRequested2":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.OpenTaskManagerWindow">
            <summary>
            Opens the Browser Task Manager view as a new window in the foreground.
            </summary>
            <remarks>
            If the Browser Task Manager is already open, this will bring it into the foreground. WebView2 currently blocks the Shift+Esc shortcut for opening the task manager. An end user can open the browser task manager manually via the <c>Browser task manager</c> entry of the DevTools window's title bar's context menu.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.IsMuted">
            <summary>
            Indicates whether all audio output from this CoreWebView2 is muted or not. Set to true will mute this CoreWebView2, and set to false will unmute this CoreWebView2. <c>true</c> if audio is muted.
            </summary>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="ToggleIsMuted":::
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="UpdateTitleWithMuteState":::
            </example>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.IsDocumentPlayingAudio">
            <summary>
            Indicates whether any audio output from this CoreWebView2 is playing. <c>true</c> if audio is playing even if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.IsMuted"/> is true.
            </summary>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="UpdateTitleWithMuteState":::
            </example>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.IsMutedChanged">
            <summary>
            IsMutedChanged is raised when the mute state changes.
            </summary>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="IsMutedChanged":::
            </example>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.IsDocumentPlayingAudioChanged">
            <summary>
            IsDocumentPlayingAudioChanged is raised when document starts or stops playing audio.
            </summary>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="IsDocumentPlayingAudioChanged":::
            </example>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.IsDefaultDownloadDialogOpen">
            <summary>
            True if the default download dialog is currently open.
            </summary>
            <remarks>
            The value of this property changes only when the default download dialog is explicitly opened or closed. Hiding the WebView implicitly hides the dialog, but does not change the value of this property.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="ToggleDefaultDownloadDialog":::
            </example>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.DefaultDownloadDialogCornerAlignment">
            <summary>
            The default download dialog corner alignment.
            </summary>
            <remarks>
            The dialog can be aligned to any of the WebView corners (see <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2DefaultDownloadDialogCornerAlignment"/>). When the WebView or dialog changes size, the dialog keeps it position relative to the corner. The dialog may become partially or completely outside of the WebView bounds if the WebView is small enough. Set the margin from the corner with the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.DefaultDownloadDialogMargin"/> property.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="SetDefaultDownloadDialogPosition":::
            </example>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.DefaultDownloadDialogMargin">
            <summary>
            The default download dialog margin relative to the WebView corner specified by <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.DefaultDownloadDialogCornerAlignment"/>.
            </summary>
            <remarks>
            The margin is a point that describes the vertical and horizontal distances between the chosen WebView corner and the default download dialog corner nearest to it. Positive values move the dialog towards the center of the WebView from the chosen WebView corner, and negative values move the dialog away from it. Use (0, 0) to align the dialog to the WebView corner with no margin.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.IsDefaultDownloadDialogOpenChanged">
            <summary>
            Raised when the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.IsDefaultDownloadDialogOpen"/> property changes.
            </summary>
            <remarks>
            This event comes after the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.DownloadStarting"/> event. Setting the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadStartingEventArgs.Handled"/> property disables the default download dialog and ensures that this event is never raised.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.OpenDefaultDownloadDialog">
            <summary>
            Open the default download dialog.
            </summary>
            <remarks>
            If the dialog is opened before there are recent downloads, the dialog shows all past downloads for the current profile. Otherwise, the dialog shows only the recent downloads with a "See more" button for past downloads. Calling this method raises the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.IsDefaultDownloadDialogOpenChanged"/> event if the dialog was closed. No effect if the dialog is already open.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.CloseDefaultDownloadDialog">
            <summary>
            Close the default download dialog.
            </summary>
            <remarks>
            Calling this method raises the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.IsDefaultDownloadDialogOpenChanged"/> event if the dialog was open. No effect if the dialog is already closed.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.BasicAuthenticationRequested">
            <summary>
            BasicAuthenticationRequested event is raised when WebView encounters a Basic HTTP Authentication request as described in https://developer.mozilla.org/docs/Web/HTTP/Authentication, an NTLM authentication or a Proxy Authentication request.
            </summary>
            <remarks>
            The host can provide a response with credentials for the authentication or cancel the request. If the host doesn't set the Cancel property to true or set either UserName or Password properties on the Response property, then WebView2 will show the default authentication challenge dialog prompt to the user.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="BasicAuthenticationRequested-Short":::
            </example>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContextMenuRequested">
            <summary>
            ContextMenuRequested is raised when a context menu is requested by the user and the content inside WebView hasn't disabled context menus.
            </summary>
            <remarks>
            The host has the option to create their own context menu with the information provided in the event or can add items to or remove items from WebView context menu. If the host doesn't handle the event, WebView will display the default context menu.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="CustomContextMenu":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.CallDevToolsProtocolMethodForSessionAsync(System.String,System.String,System.String)">
            <summary>
            Runs an asynchronous <c>DevToolsProtocol</c> method for a specific session of an attached target.
            </summary>
            <param name="sessionId">The sessionId for an attached target. null or empty string is treated as the session for the default target for the top page.</param>
            <param name="methodName">The full name of the method in the format <c>{domain}.{method}</c>.</param>
            <param name="parametersAsJson">A JSON formatted string containing the parameters for the corresponding method.</param>
            <returns>A JSON string that represents the method's return object.</returns>
            <remarks>
            There could be multiple <c>DevToolsProtocol</c> targets in a WebView.
            Besides the top level page, iframes from different origin and web workers are also separate targets.
            Attaching to these targets allows interaction with them.
            When the DevToolsProtocol is attached to a target, the connection is identified by a sessionId.
            
            To use this API, you must set the <c>flatten</c> parameter to true when calling <c>Target.attachToTarget</c> or <c>Target.setAutoAttach</c> <c>DevToolsProtocol</c> method.
            Using <c>Target.setAutoAttach</c> is recommended as that would allow you to attach to dedicated worker targets, which are not discoverable via other APIs like <c>Target.getTargets</c>.
            For more information about targets and sessions, navigate to [Chrome DevTools Protocol - Target domain]( https://chromedevtools.github.io/devtools-protocol/tot/Target).
            
            For more information about available methods, navigate to [DevTools Protocol Viewer](https://aka.ms/DevToolsProtocolDocs). The handler's Invoke method will be called when the method asynchronously completes. Invoke will be called with the method's return object as a JSON string.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.StatusBarText">
            <summary>
            The current text of the statusbar as defined by [Window.statusbar](https://developer.mozilla.org/docs/Web/API/Window/statusbar).
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.StatusBarTextChanged">
            <summary>
            StatusBarTextChanged event is raised when the text in the [Window.statusbar](https://developer.mozilla.org/docs/Web/API/Window/statusbar) changes. When the event is fired use the property <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.StatusBarText"/> to get the current statusbar text.
            </summary>
            <remarks>
            Events which cause causes can be anything from hover, url events, and others. There is not a finite list on how to cause the statusbar to change.
            The developer must create the status bar and set the text.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.Profile">
            <summary>
            The associated <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Profile"/> object of <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/>.
            </summary>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="Profile":::
            </example>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.ServerCertificateErrorDetected">
            <summary>
            The ServerCertificateErrorDetected event is raised when the WebView2 cannot verify server's digital certificate while loading a web page.
            </summary>
            <remarks>
            This event will raise for all web resources and follows the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event.
            
            If you don't handle the event, WebView2 will show the default TLS interstitial error page to the user for navigations, and for non-navigations the web request is cancelled.
            
            WebView2 caches the response when action is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorAction.AlwaysAllow"/> for the RequestUri's host and the server certificate in the session and the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ServerCertificateErrorDetected"/> event won't be raised again.
            
            To raise the event again you must clear the cache using <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.ClearServerCertificateErrorActionsAsync"/>.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="ServerCertificateErrorDetected":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.ClearServerCertificateErrorActionsAsync">
            <summary>
            Clears all cached decisions to proceed with TLS certificate errors from the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ServerCertificateErrorDetected"/> event for all WebView2's sharing the same session.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2.FaviconUri">
            <summary>
            Get the Uri as a string of the current Favicon. This will be an empty string if the page does not have a Favicon.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2.FaviconChanged">
            <summary>
            Raised when the Favicon has changed. This can include when a new page is loaded and thus by default no icon is set or the icon is set for the page by DOM or JavaScript.
            </summary>
            <remarks>
            The first argument is the Webview2 which saw the changed Favicon and the second is null.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2.GetFaviconAsync(Microsoft.Web.WebView2.Core.CoreWebView2FaviconImageFormat)">
            <summary>
            Get the downloaded Favicon image for the current page and copy it to the image stream.
            </summary>
            <param name="format">The format to retrieve the Favicon in.</param>
            <returns>
            An <c>IStream</c> populated with the downloaded Favicon.
            </returns>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2Certificate">
            <summary>
            Represents a certificate. Gives access to a certificate's metadata.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Certificate.ToX509Certificate2">
            <summary>
            Converts this to a X509Certificate2.
            </summary>
            <returns>
            An object created using PEM encoded data from
            this <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Certificate"/> object.
            </returns>
            <seealso cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Certificate.ValidFrom">
            <summary>
            The valid date and time for the certificate since the UNIX epoc.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Certificate.ValidTo">
            <summary>
            The valid date and time for the certificate since the UNIX epoc.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Certificate.Subject">
            <summary>
            Subject of the certificate.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Certificate.Issuer">
            <summary>
            Name of the certificate authority that issued the certificate.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Certificate.DerEncodedSerialNumber">
            <summary>
            DER encoded serial number of the certificate. Read more about DER at [RFC 7468 DER](https://tools.ietf.org/html/rfc7468#appendix-B).
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Certificate.DisplayName">
            <summary>
            Display name for a certificate.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Certificate.PemEncodedIssuerCertificateChain">
            <summary>
            Returns list of PEM encoded certificate issuer chain. In this list first element is the current certificate followed by intermediate1, intermediate2...intermediateN-1. Root certificate is the last element in the list.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Certificate.ToPemEncoding">
            <summary>
            PEM encoded data for the certificate. Returns Base64 encoding of DER encoded certificate. Read more about PEM at [RFC 1421 Privacy Enhanced Mail](https://tools.ietf.org/html/rfc1421).
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate">
            <summary>
            Represents a client certificate. Gives access to a certificate's metadata.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate.ToX509Certificate2">
            <summary>
            Converts this to a X509Certificate2.
            </summary>
            <returns>
            An object created using PEM encoded data from
            this <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate"/> object.
            </returns>
            <seealso cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate.ValidFrom">
            <summary>
            The valid date and time for the certificate since the UNIX epoc.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate.ValidTo">
            <summary>
            The valid date and time for the certificate since the UNIX epoc.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate.Subject">
            <summary>
            Subject of the certificate.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate.Issuer">
            <summary>
            Name of the certificate authority that issued the certificate.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate.DerEncodedSerialNumber">
            <summary>
            DER encoded serial number of the certificate. Read more about DER at [RFC 7468 DER](https://tools.ietf.org/html/rfc7468#appendix-B).
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate.DisplayName">
            <summary>
            Display name for a certificate.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate.PemEncodedIssuerCertificateChain">
            <summary>
            Returns list of PEM encoded client certificate issuer chain. In this list first element is the current certificate followed by intermediate1, intermediate2...intermediateN-1. Root certificate is the last element in the list.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate.Kind">
            <summary>
            Kind of a certificate. See <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateKind"/> for descriptions.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate.ToPemEncoding">
            <summary>
            PEM encoded data for the certificate. Returns Base64 encoding of DER encoded certificate. Read more about PEM at [RFC 1421 Privacy Enhanced Mail](https://tools.ietf.org/html/rfc1421).
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller">
            <summary>
            This class is the owner of the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.CoreWebView2"/> object, and
            provides support for resizing, showing and hiding, focusing, and other
            functionality related to windowing and composition.
            </summary>
            <remarks>
            The <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> owns the <see
            cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.CoreWebView2"/>, and if all references to the <see
            cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> go away, the WebView will be closed.
            </remarks>
            <summary>
            The owner of the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.CoreWebView2"/> object that provides support for resizing, showing and hiding, focusing, and other functionality related to windowing and composition.
            </summary>
            <remarks>
            The CoreWebView2Controller owns the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.CoreWebView2"/>, and if all references to the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> go away, the WebView is closed.
            </remarks>
            <summary>
            The owner of the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.CoreWebView2"/> object that provides support for resizing, showing and hiding, focusing, and other functionality related to windowing and composition.
            </summary>
            <remarks>
            The CoreWebView2Controller owns the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.CoreWebView2"/>, and if all references to the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> go away, the WebView is closed.
            </remarks>
            <summary>
            The owner of the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.CoreWebView2"/> object that provides support for resizing, showing and hiding, focusing, and other functionality related to windowing and composition.
            </summary>
            <remarks>
            The CoreWebView2Controller owns the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.CoreWebView2"/>, and if all references to the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> go away, the WebView is closed.
            </remarks>
            <summary>
            The owner of the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.CoreWebView2"/> object that provides support for resizing, showing and hiding, focusing, and other functionality related to windowing and composition.
            </summary>
            <remarks>
            The CoreWebView2Controller owns the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.CoreWebView2"/>, and if all references to the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> go away, the WebView is closed.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.CoreWebView2">
            <summary>
            Gets the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.CoreWebView2"/> associated with this <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/>.
            </summary>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.CoreWebView2"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Controller.Initialize">
            <summary>
            Add .Net specific host object helper for the CoreWebView2 so that it can
            figure out whether a member is a method of an object.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.IsVisible">
            <summary>
            Determines whether to show or hide the WebView.
            </summary>
            <remarks>
            If <c>IsVisible</c> is set to <c>false</c>, the WebView is transparent and is not rendered. However, this does not affect the window containing the WebView (the <c>ParentWindow</c> parameter that was passed to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2ControllerAsync(System.IntPtr)"/> or <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2ControllerAsync(System.IntPtr,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/>).
            If you want that window to disappear too, run the corresponding <c>Hide</c> method from the UI framework on it directly in addition to modifying this.
            WebView as a child window does not get window messages when the top window is minimized or restored. For performance reasons, developers should set the <c>IsVisible</c> property of the WebView to <c>false</c> when the app window is minimized and back to <c>true</c> when the app window is restored. The app window does this by handling <c>SIZE_MINIMIZED</c> and <c>SIZE_RESTORED</c> command upon receiving <c>WM_SIZE</c> message. There are CPU and memory benefits when the page is hidden. For instance Chromium has code that throttles activities on the page like animations and some tasks are run less frequently. Similarly, WebView2 will purge some caches to reduce memory usage.
            </remarks>
            <seealso cref="M:System.Windows.Forms.Control.Hide"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.Bounds">
            <summary>
            Gets or sets the WebView bounds.
            </summary>
            <remarks>
            Bounds are relative to the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ParentWindow"/>. The app has two ways to position a WebView:
            <list type="bullet">
            <item><description>
            Create a child HWND that is the WebView parent HWND. Position the window where the WebView should be. Use (0, 0) for the top-left corner (the offset) of the Bounds of the WebView.
            </description></item>
            <item><description>
            Use the top-most window of the app as the WebView parent HWND. For example, to position ebView correctly in the app, set the top-left corner of the Bounds of the WebView.
            </description></item>
            </list>
            
            The values of Bounds are limited by the coordinate space of the host.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor">
            <summary>
            Gets or sets the zoom factor for the WebView.
            </summary>
            <remarks>
            Note that changing zoom factor may cause <c>window.innerWidth</c> or <c>window.innerHeight</c> and page layout to change. A zoom factor that is applied by the host by setting this ZoomFactor property becomes the new default zoom for the WebView. This zoom factor applies across navigations and is the zoom factor WebView is returned to when the user presses Ctrl+0. When the zoom factor is changed by the user (resulting in the app receiving <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactorChanged"/>), that zoom applies only for the current page. Any user applied zoom is only for the current page and is reset on a navigation. Specifying a ZoomFactor less than or equal to 0 is not allowed. WebView also has an internal supported zoom factor range. When a specified zoom factor is out of that range, it is normalized to be within the range, and a <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactorChanged"/> event is raised for the real applied zoom factor. When this range normalization happens, this reports the zoom factor specified during the previous modification of the ZoomFactor property until the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactorChanged"/> event is received after WebView applies the normalized zoom factor.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ParentWindow">
            <summary>
            Gets the parent window provided by the app or sets the parent window that this WebView is using to render content.
            </summary>
            <remarks>
            It initially returns the <c>ParentWindow</c> passed into <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2ControllerAsync(System.IntPtr)"/> or <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2ControllerAsync(System.IntPtr,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/>. Setting the property causes the WebView to re-parent the main WebView window to the newly provided window.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactorChanged">
            <summary>
            ZoomFactorChanged is raised when the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor"/> property changes.
            </summary>
            <remarks>
            The event may be raised because the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor"/> property was modified, or due to the user manually modifying the zoom. When it is modified using the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor"/> property, the internal zoom factor is updated immediately and no ZoomFactorChanged event is raised. WebView associates the last used zoom factor for each site. It is possible for the zoom factor to change when navigating to a different page. When the zoom factor changes due to a navigation change, the ZoomFactorChanged event is raised right after the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/> event.
            </remarks>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.MoveFocusRequested">
            <summary>
            MoveFocusRequested is raised when user tries to tab out of the WebView.
            </summary>
            <remarks>
            The focus of the WebView has not changed when this event is raised.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.GotFocus">
            <summary>
            GotFocus is raised when WebView gets focus.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.LostFocus">
            <summary>
            LostFocus is raised when WebView loses focus.
            </summary>
            <remarks>
            In the case where <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.MoveFocusRequested"/> event is raised, the focus is still on WebView when <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.MoveFocusRequested"/> event is raised. LostFocus is only raised afterwards when code of the app or default action of <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.MoveFocusRequested"/> event sets focus away from WebView.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.AcceleratorKeyPressed">
            <summary>
            AcceleratorKeyPressed is raised when an accelerator key or key combo is pressed or released while the WebView is focused.
            </summary>
            <remarks>
            A key is considered an accelerator if either of the following conditions are true:
            
            <list type="bullet">
            <item><description>
            Ctrl or Alt is currently being held.
            </description></item>
            <item><description>
            The pressed key does not map to a character.
            </description></item>
            </list>
            
            A few specific keys are never considered accelerators, such as Shift. The Escape key is always considered an accelerator.
            
            Autorepeated key events caused by holding the key down will also raise this event. Filter out the auto-repeated key events by verifying <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2AcceleratorKeyPressedEventArgs.KeyEventLParam"/> or <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2AcceleratorKeyPressedEventArgs.PhysicalKeyStatus"/>.
            
            In windowed mode, this event is synchronous. Until you set <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2AcceleratorKeyPressedEventArgs.Handled"/> to <c>true</c> or the event handler returns, the browser process is blocked and outgoing cross-process COM calls will fail with RPC_E_CANTCALLOUT_ININPUTSYNCCALL. All <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.CoreWebView2"/> methods work, however.
            
            In windowless mode, the event is asynchronous. Further input do not reach the browser until the event handler returns or <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2AcceleratorKeyPressedEventArgs.Handled"/> is set to <c>true</c>, but the browser process is not blocked, and outgoing COM calls work normally.
            
            It is recommended to set <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2AcceleratorKeyPressedEventArgs.Handled"/> to <c>true</c> as early as you are able to know that you want to handle the accelerator key.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Controller.SetBoundsAndZoomFactor(System.Drawing.Rectangle,System.Double)">
            <summary>
            Updates <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.Bounds"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor"/> properties at the same time.
            </summary>
            <param name="Bounds">The bounds to be updated.</param>
            <param name="ZoomFactor">The zoom factor to be updated.</param>
            <remarks>
            This operation is atomic from the perspective of the host. After returning from this function, the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.Bounds"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor"/> properties are both updated if the function is successful, or neither is updated if the function fails. If <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.Bounds"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor"/> are both updated by the same scale (for example, <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.Bounds"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor"/> are both doubled), then the page does not display a change in <c>window.innerWidth</c> or <c>window.innerHeight</c> and the WebView renders the content at the new size and zoom without intermediate renderings. This function also updates just one of <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor"/> or <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.Bounds"/> by passing in the new value for one and the current value for the other.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Controller.MoveFocus(Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusReason)">
            <summary>
            Moves focus into WebView.
            </summary>
            <param name="reason">The reason for moving focus.</param>
            <remarks>
            WebView will get focus and focus will be set to correspondent element in the page hosted in the WebView. For <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusReason.Programmatic"/> reason, focus is set to previously focused element or the default element if no previously focused element exists. For <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusReason.Next"/> reason, focus is set to the first element. For <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusReason.Previous"/> reason, focus is set to the last element. WebView changes focus through user interaction including selecting into a WebView or Tab into it. For tabbing, the app runs MoveFocus with <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusReason.Next"/> or <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusReason.Previous"/> to align with Tab and Shift+Tab respectively when it decides the WebView is the next tabbable element.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Controller.NotifyParentWindowPositionChanged">
            <summary>
            Tells WebView that the main WebView parent (or any ancestor) HWND moved.
            </summary>
            <remarks>
            This is a notification separate from <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.Bounds"/>. This is needed for accessibility and certain dialogs in WebView to work correctly.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Controller.Close">
            <summary>
            Closes the WebView and cleans up the underlying browser instance.
            </summary>
            <remarks>
            Cleaning up the browser instance releases the resources powering the WebView. The browser instance is shut down if no other WebViews are using it.
            
            After running Close, all methods fail and event handlers stop running. Specifically, the WebView releases the associated references to any associated event handlers when Close is run.
            
            Close is implicitly run when the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> loses the final reference and is destructed. But it is best practice to explicitly run Close to avoid any accidental cycle of references between the WebView and the app code. Specifically, if you capture a reference to the WebView in an event handler you create a reference cycle between the WebView and the event handler. Run Close to break the cycle by releasing all event handlers. But to avoid the situation, it is best to both explicitly run Close on the WebView and to not capture a reference to the WebView to ensure the WebView is cleaned up correctly. Close is synchronous and won't trigger the <c>beforeunload</c> event.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.DefaultBackgroundColor">
            <summary>
            Gets or sets the WebView default background color.
            </summary>
            <remarks>
            The DefaultBackgroundColor is the color that renders underneath all web content. This means WebView renders this color when there is no web content loaded such as before the initial navigation or between navigations. This also means web pages with undefined css background properties or background properties containing transparent pixels will render their contents over this color. Web pages with defined and opaque background properties that span the page will obscure the DefaultBackgroundColor and display normally. The default value for this property is white to resemble the native browser experience. Currently this API only supports opaque colors and transparency. It will fail for colors with alpha values that don't equal 0 or 255 ie. translucent colors are not supported. It also does not support transparency on Windows 7. On Windows 7, setting DefaultBackgroundColor to a Color with an Alpha value other than 255 will result in failure. On any OS above Win7, choosing a transparent color will result in showing hosting app content. This means webpages without explicit background properties defined will render web content over hosting app content.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="DefaultBackgroundColor":::
            </example>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.RasterizationScale">
            <summary>
            Gets or sets the WebView rasterization scale.
            </summary>
            <remarks>
            The rasterization scale is the combination of the monitor DPI scale and text scaling set by the user. This value should be updated when the DPI scale of the app's top level window changes (i.e. monitor DPI scale changes or the window changes monitor) or when the text scale factor of the system changes.
            Rasterization scale applies to the WebView content, as well as popups, context menus, scroll bars, and so on. Normal app scaling scenarios should use the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor"/> property or <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Controller.SetBoundsAndZoomFactor(System.Drawing.Rectangle,System.Double)"/> method.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ShouldDetectMonitorScaleChanges">
            <summary>
            Determines whether the WebView will detect monitor scale changes.
            </summary>
            <remarks>
            ShouldDetectMonitorScaleChanges property determines whether the WebView attempts to track monitor DPI scale changes. When true, the WebView will track monitor DPI scale changes, update the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.RasterizationScale"/> property, and fire <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.RasterizationScaleChanged"/> event. When <c>false</c>, the WebView will not track monitor DPI scale changes, and the app must update the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.RasterizationScale"/> property itself. <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.RasterizationScaleChanged"/> event will never raise when ShouldDetectMonitorScaleChanges is false. Apps that want to set their own rasterization scale should set this property to false to avoid the WebView2 updating the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.RasterizationScale"/> property to match the monitor DPI scale.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.BoundsMode">
            <summary>
            Gets or sets the WebView bounds mode.
            </summary>
            <remarks>
            BoundsMode affects how setting the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.Bounds"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.RasterizationScale"/> properties work. Bounds mode can either be in <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2BoundsMode.UseRawPixels"/> mode or <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2BoundsMode.UseRasterizationScale"/> mode.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.RasterizationScaleChanged">
            <summary>
            RasterizationScaleChanged is raised when the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.RasterizationScale"/> property changes.
            </summary>
            <remarks>
            The event is raised when the Webview detects that the monitor DPI scale has changed, <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ShouldDetectMonitorScaleChanges"/> is true, and the Webview has changed the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.RasterizationScale"/> property.
            </remarks>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.RasterizationScale"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.AllowExternalDrop">
            <summary>
            Gets or sets the WebView allow external drop property.
            </summary>
            <remarks>
            The AllowExternalDrop is to configure the capability that dropping files into webview2 is allowed or permitted. The default value is true.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="ToggleAllowExternalDrop":::
            </example>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2Cookie">
            <summary>
            Provides a set of properties that are used to manage a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Cookie"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.ToSystemNetCookie">
            <summary>
            Converts this to a System.Net.Cookie.
            </summary>
            <returns>
            An object whose <see cref="P:System.Net.Cookie.Name"/>, <see
            cref="P:System.Net.Cookie.Value"/>, <see cref="P:System.Net.Cookie.Path"/>, <see
            cref="P:System.Net.Cookie.Domain"/>, <see cref="P:System.Net.Cookie.HttpOnly"/>, <see
            cref="P:System.Net.Cookie.Secure"/>, and <see cref="P:System.Net.Cookie.Expires"/>, matches
            those <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Name"/>, <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Value"/>, <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Path"/>,
            <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Domain"/>, <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.IsHttpOnly"/>, <see
            cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.IsSecure"/>, and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Expires"/> of this <see
            cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Cookie"/> object.
            </returns>
            <remarks>
            The values of the <see cref="T:System.Net.Cookie"/> properties other than those
            specified above remain their default values.
            </remarks>
            <seealso cref="T:System.Net.Cookie"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Expires">
            <summary>
            The expiration date and time for the cookie since the UNIX epoch.
            </summary>
            <remarks>
            Setting the Expires property to <see cref="F:System.DateTime.MinValue"/>
            makes this a session cookie, which is its default value.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Name">
            <summary>
            Get or sets the cookie name.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Value">
            <summary>
            Gets or sets the cookie value.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Domain">
            <summary>
            Gets the domain for which the cookie is valid.
            </summary>
            <remarks>
            The default value is the host that this cookie has been received from. Note that, for instance, <c>.bing.com</c>, <c>bing.com</c>, and <c>www.bing.com</c> are considered different domains.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Path">
            <summary>
            Gets the path for which the cookie is valid.
            </summary>
            <remarks>
            The default value is "/", which means this cookie will be sent to all pages on the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Domain"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.IsHttpOnly">
            <summary>
            Determines whether this cookie is http-only.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.SameSite">
            <summary>
            Determines the SameSite status of the cookie which represents the enforcement mode of the cookie.
            </summary>
            <remarks>
            The default value is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2CookieSameSiteKind.Lax"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.IsSecure">
            <summary>
            Gets or sets the security level of this cookie.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.IsSession">
            <summary>
            Determines whether this is a session cookie. The default value is <c>false</c>.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2CookieManager">
            <summary>
            Creates, adds or updates, gets, or or view the cookies.
            </summary>
            <remarks>
            The changes would apply to the context of the user profile. That is, other WebViews under the same user profile could be affected.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2CookieManager.CreateCookieWithSystemNetCookie(System.Net.Cookie)">
            <summary>
            Creates a CoreWebView2Cookie object whose params matches those of the given System.Net.Cookie.
            </summary>
            <param name="systemNetCookie">
            A System.Net.Cookie whose params to be used to create a CoreWebView2Cookie.
            </param>
            <returns>
            An object whose <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Name"/>, <see
            cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Value"/>, <see
            cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Path"/>, <see
            cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Domain"/>, <see
            cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.IsHttpOnly"/>, <see
            cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.IsSecure"/>, and <see
            cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.Expires"/>, matches those <see
            cref="P:System.Net.Cookie.Name"/>, <see cref="P:System.Net.Cookie.Value"/>, <see
            cref="P:System.Net.Cookie.Path"/>, <see cref="P:System.Net.Cookie.Domain"/>, <see
            cref="P:System.Net.Cookie.HttpOnly"/>, <see cref="P:System.Net.Cookie.Secure"/>, and <see
            cref="P:System.Net.Cookie.Expires"/> of the given <see cref="T:System.Net.Cookie"/> object.
            </returns>
            <remarks>
            The default value for the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Cookie.SameSite"/>
            property of the returned <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Cookie"/> object is
            <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2CookieSameSiteKind.Lax"/>.
            </remarks>
            <seealso cref="T:System.Net.Cookie"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2CookieManager.CreateCookie(System.String,System.String,System.String,System.String)">
            <summary>
            Creates a cookie object with a specified name, value, domain, and path.
            </summary>
            <remarks>
            One can set other optional properties after cookie creation. This only creates a cookie object and it is not added to the cookie manager until you call <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2CookieManager.AddOrUpdateCookie(Microsoft.Web.WebView2.Core.CoreWebView2Cookie)"/>. name that starts with whitespace(s) is not allowed.
            </remarks>
            <param name="name">The name for the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Cookie"/> to be created. It cannot start with whitespace(s).</param>
            <param name="value"></param>
            <param name="Domain"></param>
            <param name="Path"></param>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2CookieManager.CopyCookie(Microsoft.Web.WebView2.Core.CoreWebView2Cookie)">
            <summary>
            Creates a cookie whose params matches those of the specified cookie.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2CookieManager.GetCookiesAsync(System.String)">
            <summary>
            Gets a list of cookies matching the specific URI.
            </summary>
            <remarks>
            You can modify the cookie objects by calling <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2CookieManager.AddOrUpdateCookie(Microsoft.Web.WebView2.Core.CoreWebView2Cookie)"/>, and the changes will be applied to the webview.
            </remarks>
            <param name="uri">If uri is empty string or null, all cookies under the same profile are returned.</param>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="GetCookies":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2CookieManager.AddOrUpdateCookie(Microsoft.Web.WebView2.Core.CoreWebView2Cookie)">
            <summary>
            Adds or updates a cookie with the given cookie data; may overwrite cookies with matching name, domain, and path if they exist.
            </summary>
            <param name="cookie">The <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Cookie"/> to be added or updated.</param>
            <remarks>
            This method will fail if the domain of the given cookie is not specified.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="AddOrUpdateCookie":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2CookieManager.DeleteCookie(Microsoft.Web.WebView2.Core.CoreWebView2Cookie)">
            <summary>
            Deletes a cookie whose name and domain/path pair match those of the specified cookie.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2CookieManager.DeleteCookies(System.String,System.String)">
            <summary>
            Deletes cookies with matching name and uri.
            </summary>
            <param name="name">The name for the cookies to be deleted is required.</param>
            <param name="uri">If uri is specified, deletes all cookies with the given name where domain and path match provided URI.</param>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2CookieManager.DeleteCookiesWithDomainAndPath(System.String,System.String,System.String)">
            <summary>
            Deletes cookies with matching name and domain/path pair.
            </summary>
            <param name="name">The name for the cookies to be deleted is required.</param>
            <param name="Domain">If domain is specified, deletes only cookies with the exact domain.</param>
            <param name="Path">If path is specified, deletes only cookies with the exact path.</param>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2CookieManager.DeleteAllCookies">
            <summary>
            Deletes all cookies under the same profile.
            </summary>
            <remarks>
            This could affect other WebViews under the same user profile.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation">
            <summary>
            Represents a download operation. Gives access to a download's metadata and supports a user canceling, pausing, or resuming a download.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.EstimatedEndTime">
            <summary>
            The estimated end time of the download.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.TotalBytesToReceive">
            <summary>
            The total bytes to receive count.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.Uri">
            <summary>
            The URI of the download.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.ContentDisposition">
            <summary>
            The Content-Disposition header value from the download's HTTP response. If none, the value is an empty string.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.MimeType">
            <summary>
            MIME type of the downloaded content.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.BytesReceived">
            <summary>
            The number of bytes that have been written to the download file.
            </summary>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="BytesReceivedChanged":::
            </example>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.ResultFilePath">
            <summary>
            The absolute path to the download file, including file name.
            </summary>
            <remarks>
            Host can change this from <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadStartingEventArgs.ResultFilePath"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.State">
            <summary>
            The state of the download. A download can be in progress, interrupted, or completed.
            </summary>
            <remarks>
            See <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2DownloadState"/> for descriptions of states.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.InterruptReason">
            <summary>
            The reason why connection with file host was broken.
            </summary>
            <remarks>
            See <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason"/> for descriptions of reasons.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.CanResume">
            <summary>
            Returns true if an interrupted download can be resumed.
            </summary>
            <remarks>
            Downloads with the following interrupt reasons may automatically resume without you calling any methods: <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.ServerNoRange"/>, <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.FileHashMismatch"/>, <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.FileTooShort"/>. In these cases progress may be restarted with <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.BytesReceived"/> set to 0.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.BytesReceivedChanged">
            <summary>
            Event raised when the bytes received count is updated.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.EstimatedEndTimeChanged">
            <summary>
            Event raised when the estimated end time changes.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.StateChanged">
            <summary>
            Event raised when the state of the download changes.
            </summary>
            <remarks>
            Use <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.State"/> to get the current state, and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.InterruptReason"/> to get the reason if the download is interrupted.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="StateChanged":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.Cancel">
            <summary>
            Cancels the download.
            </summary>
            <remarks>
            If canceled, the default download dialog shows that the download was canceled. Host should use <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadStartingEventArgs.Cancel"/> if download should be canceled without displaying the default download dialog.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.Pause">
            <summary>
            Pauses the download.
            </summary>
            <remarks>
            If paused, the default download dialog shows that the download is paused. No effect if download is already paused. Pausing a download changes the state from in progress to interrupted, with interrupt reason set to <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.UserCanceled"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.Resume">
            <summary>
            Resumes a paused download. May also resume a download that was interrupted for another reason if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.CanResume"/> returns true.
            </summary>
            <remarks>
            Resuming a download changes the state from interrupted to in progress.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment">
            <summary>
            This represents the WebView2 Environment.
            </summary>
            <remarks>
            WebViews created from an environment run on the Browser process specified with environment parameters and objects created from an environment should be used in the same environment. Using it in different environments are not guaranteed to be compatible and may fail.
            </remarks>
            
            
            
            
            
            
            
            
            
            
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateAsync(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions)">
             <summary>
             Creates a WebView2 Environment using the installed or a custom WebView2 Runtime version.
             </summary>
             <param name="browserExecutableFolder">
             The relative path to the folder that contains a custom version of WebView2 Runtime.
             <para>
             To use a fixed version of the WebView2 Runtime, pass the
             folder path that contains the fixed version of the WebView2 Runtime
             to <c>browserExecutableFolder</c>. BrowserExecutableFolder supports both relative 
             (to the application's executable) and absolute file paths. To create WebView2 controls 
             that use the installed version of the WebView2 Runtime that exists on
             user machines, pass a <c>null</c> or empty string to
             <c>browserExecutableFolder</c>. In this scenario, the API tries to 
             find a compatible version of the WebView2 Runtime that is installed
             on the user machine (first at the machine level, and then per user)
             using the selected channel preference. The path of fixed version of
             the WebView2 Runtime should not contain <em>\Edge\Application\</em>. When
             such a path is used, the API fails with <c>ERROR_NOT_SUPPORTED</c>.
             </para>
             </param>
             <param name="userDataFolder">
             The user data folder location for WebView2.
             <para>
             The path is either an absolute file path or a relative file path
             that is interpreted as relative to the compiled code for the
             current process. The default user data folder <em>{Executable File
             Name}.WebView2</em> is created in the same directory next to the
             compiled code for the app. WebView2 creation fails if the compiled
             code is running in a directory in which the process does not have
             permission to create a new directory. The app is responsible to
             clean up the associated user data folder when it is done.
             </para>
             </param>
             <param name="options">
             Options used to create WebView2 Environment.
             <para>
             As a browser process may be shared among WebViews, WebView creation
             fails if the specified <c>options</c> does not match the options of
             the WebViews that are currently running in the shared browser
             process.
             </para>
             </param>
             <remarks>
             <para>
             The default channel search order is the WebView2 Runtime, Beta, Dev, and
             Canary. When an override <c>WEBVIEW2_RELEASE_CHANNEL_PREFERENCE</c> environment
             variable or applicable <c>releaseChannelPreference</c> registry value is set to
             <c>1</c>, the channel search order is reversed.
             </para>
             <para>
             To use a fixed version of the WebView2 Runtime, pass the relative
             folder path that contains the fixed version of the WebView2 Runtime
             to <c>browserExecutableFolder</c>. To create WebView2 controls that
             use the installed version of the WebView2 Runtime that exists on
             user machines, pass a <c>null</c> or empty string to
             <c>browserExecutableFolder</c>. In this scenario, the API tries to
             find a compatible version of the WebView2 Runtime that is installed
             on the user machine (first at the machine level, and then per user)
             using the selected channel preference. The path of fixed version of
             the WebView2 Runtime should not contain <em>\Edge\Application\</em>. When
             such a path is used, the API fails with the following error.
             </para>
             <para>
             The <paramref name="browserExecutableFolder"/>, <paramref
             name="userDataFolder"/>, and <paramref name="options"/> may be
             overridden by values either specified in environment variables or in
             the registry.
             </para>
             <para>
             When creating a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> the following environment variables are verified.
             </para>
             <list type="bullet">
             <item>
             <term><c>WEBVIEW2_BROWSER_EXECUTABLE_FOLDER</c></term>
             </item>
             <item>
             <term><c>WEBVIEW2_USER_DATA_FOLDER</c></term>
             </item>
             <item>
             <term><c>WEBVIEW2_ADDITIONAL_BROWSER_ARGUMENTS</c></term>
             </item>
             <item>
             <term><c>WEBVIEW2_RELEASE_CHANNEL_PREFERENCE</c></term>
             </item>
             </list>
             <para>
             If browser executable folder or user data folder is specified in an
             environment variable or in the registry, the specified <paramref
             name="browserExecutableFolder"/> or <paramref
             name="userDataFolder"/> values are overridden. If additional browser
             arguments are specified in an environment variable or in the
             registry, it is appended to the corresponding value in the specified
             <paramref name="options"/>.
             </para>
             <para>
             While not strictly overrides, additional environment variables may be set.
             </para>
             <list type="table">
             <listheader>
             <term>Value</term>
             <description>Description</description>
             </listheader>
             <item>
             <term><c>WEBVIEW2_WAIT_FOR_SCRIPT_DEBUGGER</c></term>
             <description>
             When found with a non-empty value, this indicates that the WebView
             is being launched under a script debugger. In this case, the WebView
             issues a <c>Page.waitForDebugger</c> CDP command that runs the
             script inside the WebView to pause on launch, until a debugger
             issues a corresponding <c>Runtime.runIfWaitingForDebugger</c> CDP
             command to resume the runtime.
             Note that this environment variable does not have a registry key equivalent.
             </description>
             </item>
             <item>
             <term><c>WEBVIEW2_PIPE_FOR_SCRIPT_DEBUGGER</c></term>
             <description>
             When found with a non-empty value, it indicates that the WebView is
             being launched under a script debugger that also supports host apps
             that use multiple WebViews. The value is used as the identifier for
             a named pipe that is opened and written to when a new WebView is
             created by the host app. The payload should match the payload of the
             <c>remote-debugging-port</c> JSON target and an external debugger
             may use it to attach to a specific WebView instance. The format of
             the pipe created by the debugger should be
             <c>\\.\pipe\WebView2\Debugger\{app_name}\{pipe_name}</c>, where the
             following are true.
            
             <list type="bullet">
             <item><description><c>{app_name}</c> is the host app exe file name, for example, <c>WebView2Example.exe</c></description></item>
             <item><description><c>{pipe_name}</c> is the value set for <c>WEBVIEW2_PIPE_FOR_SCRIPT_DEBUGGER</c></description></item>
             </list>
            
             To enable debugging of the targets identified by the JSON, you must
             set the <c>WEBVIEW2_ADDITIONAL_BROWSER_ARGUMENTS</c> environment
             variable to send <c>--remote-debugging-port={port_num}</c>, where
             the following is true.
            
             <list type="bullet">
             <item><description><c>{port_num}</c> is the port on which the CDP server binds.</description></item>
             </list>
            
             If both <c>WEBVIEW2_PIPE_FOR_SCRIPT_DEBUGGER</c> and
             <c>WEBVIEW2_ADDITIONAL_BROWSER_ARGUMENTS</c> environment variables,
             the WebViews hosted in your app and associated contents may exposed
             to 3rd party apps such as debuggers. Note that this environment
             variable does not have a registry key equivalent.
             </description>
             </item>
             </list>
             <para>
             If none of those environment variables exist, then the registry is examined
             next.
             </para>
             <list type="bullet">
             <item>
             <term><c>[{Root}]\Software\Policies\Microsoft\Edge\WebView2\BrowserExecutableFolder "{AppId}"=""</c></term>
             </item>
             <item>
             <term><c>[{Root}]\Software\Policies\Microsoft\Edge\WebView2\ReleaseChannelPreference "{AppId}"=""</c></term>
             </item>
             <item>
             <term><c>[{Root}]\Software\Policies\Microsoft\Edge\WebView2\AdditionalBrowserArguments "{AppId}"=""</c></term>
             </item>
             <item>
             <term><c>[{Root}]\Software\Policies\Microsoft\Edge\WebView2\UserDataFolder "{AppId}"=""</c></term>
             </item>
             </list>
             <para>
             Use a group policy under <strong>Administrative Templates</strong> &gt;
             <strong>Microsoft Edge WebView2</strong> to configure browser executable folder
             and release channel preference.
             </para>
             <list type="table">
             <listheader>
             <term>Value</term>
             <description>Description</description>
             </listheader>
             <item>
             <term><c>ERROR_DISK_FULL</c></term>
             <description>
             In the unlikely scenario where some instances of WebView are open during a
             browser update, the deletion of the previous WebView2 Runtime may be
             blocked. To avoid running out of disk space, a new WebView creation fails
             with this error if it detects that too many previous WebView2
             Runtime versions exist.
             </description>
             </item>
             <item>
             <term><c>WEBVIEW2_MAX_INSTANCES</c></term>
             <description>
             The default maximum number of WebView2 Runtime versions allowed is <c>20</c>.
             To override the maximum number of the previous WebView2 Runtime versions
             allowed, set the value of the following environment variable.
             </description>
             </item>
             <item>
             <term><c>ERROR_PRODUCT_UNINSTALLED</c></term>
             <description>
             If the Webview depends upon an installed WebView2 Runtime version and it is
             uninstalled, any subsequent creation fails with this error.
             </description>
             </item>
             </list>
             <para>
             First verify with Root as <c>HKLM</c> and then <c>HKCU</c>. <c>AppId</c> is first set to
             the Application User Model ID of the process, then if no corresponding
             registry key, the <c>AppId</c> is set to the compiled code name of the process,
             or if that is not a registry key then <c>*</c>. If an override registry key is
             found, use the <c>browserExecutableFolder</c> and <c>userDataFolder</c> registry
             values as replacements and append <c>additionalBrowserArguments</c> registry
             values for the corresponding values in the provided <paramref name="options"/>.
             </para>
             </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.GetAvailableBrowserVersionString(System.String)">
            <summary>
            Gets the browser version info including channel name if it is not the stable channel or WebView2 Runtime.
            </summary>
            <param name="browserExecutableFolder">
            The relative path to the folder that contains the WebView2 Runtime.
            </param>
            <exception cref="T:Microsoft.Web.WebView2.Core.WebView2RuntimeNotFoundException">
            WebView2 Runtime installation is missing.
            </exception>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CompareBrowserVersions(System.String,System.String)">
            <summary>
            Compares two instances of browser versions correctly and returns an integer that indicates whether the first instance is older, the same as, or newer than the second instance.
            </summary>
            <param name="version1">
            One of the version strings to compare.
            </param>
            <param name="version2">
            The other version string to compare.
            </param>
            <returns>
            An integer that indicates whether the first instance is older, the same as, or newer than the second instance.
            <list type="table">
            <listheader>
            <description>Value Type</description>
            <description>Condition</description>
            </listheader>
            <item>
            <description>Less than zero</description>
            <description><c>version1</c> is older than <c>version2</c>.</description>
            </item>
            <item>
            <description>Zero</description>
            <description><c>version1</c> is the same as <c>version2</c>.</description>
            </item>
            <item>
            <description>Greater than zero</description>
            <description><c>version1</c> is newer than <c>version2</c>.</description>
            </item>
            </list>
            </returns>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateWebResourceRequest(System.String,System.String,System.IO.Stream,System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequest"/> object.
            </summary>
            <param name="uri">The request URI.</param>
            <param name="Method">The HTTP request method.</param>
            <param name="postData"></param>
            <param name="Headers">The raw request header string delimited by CRLF (optional in last header).</param>
            <remarks>
            <c>uri</c> parameter must be absolute URI. It's also possible to create this object with <c>null</c> headers string and then use the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders"/> to construct the headers line by line.
            </remarks>
            <seealso cref="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequest"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2ControllerOptions">
            <summary>
            Creates a new <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions"/> object,
            which can be passed as a parameter in <see
            cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2ControllerAsync(System.IntPtr,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/> and <see
            cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2CompositionControllerAsync(System.IntPtr,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/> function for multiple profiles
            support.
            </summary>
            <returns>
            A <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions"/> that can be
            passed when calling <see
            cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2ControllerAsync(System.IntPtr,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/> and <see
            cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2CompositionControllerAsync(System.IntPtr,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/>.
            </returns>
            <remarks>
            The options is a settable property while the default for profile
            name is an empty string and the default value for <see
            cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions.IsInPrivateModeEnabled"/> is
            false. The profile will be created on disk or opened when calling
            CreateCoreWebView2ControllerWithOptions no matter InPrivate mode is
            enabled or not, and it will be released in memory when the
            correspoding <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> is closed but
            still remain on disk. As WebView2 is built on top of Edge browser,
            it follows Edge's behavior pattern. To create an InPrivate WebView,
            we get an off-the-record profile (an InPrivate profile) from a
            regular profile, then create the WebView with the off-the-record
            profile. Also the profile name can be reused.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2ControllerAsync(System.IntPtr,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)">
            <summary>
            Asynchronously creates a new <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> object.
            </summary>
            <param name="ParentWindow">The HWND in which the WebView should be displayed and from which receive input.</param>
            <param name="options">
            The options contains profileName and inPrivate parameters that could be used to create CoreWebView2Profile, and it can be used to create multiple WebViews with multiple profiles under a single user data directory.
            </param>
            <remarks>
            Multiple profiles under single user data directory can share some system resources including memory, CPU footprint, disk space (such as compiled shaders and safebrowsing data) etc.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2CompositionControllerAsync(System.IntPtr,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)">
            <summary>
            Asynchronously creates a new <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController"/> object.
            </summary>
            <param name="ParentWindow">The HWND in which the WebView should be displayed and from which receive input.</param>
            <param name="options">
            The options contains profileName and inPrivate parameters that could be used to create CoreWebView2Profile, and it can be used to create multiple WebViews with multiple profiles under a single user data directory.
            </param>
            <remarks>
            Multiple profiles under single user data directory can share some system resources including memory, CPU footprint, disk space (such as compiled shaders and safebrowsing data) etc.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.IsDirectorySeparator(System.Char)">
            <summary>
            True if the given character is a directory separator.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.IsValidDriveChar(System.Char)">
            <summary>
            Returns true if the given character is a valid drive letter
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.IsPathPartiallyQualified(System.String)">
            This code is taken from the .NET source code, to be able to support this functionality
            down to Framework 4.5.
            <summary>
            Returns true if the path specified is null, empty, or relative to the current drive or working directory.
            Returns false if the path is fixed to a specific drive or UNC path.  This method does no
            validation of the path (URIs will be returned as relative as a result).
            </summary>
            <remarks>
            Handles paths that use the alternate directory separator.  It is a frequent mistake to
            assume that rooted paths (Path.IsPathRooted) are not relative.  This isn't the case.
            "C:a" is drive relative- meaning that it will be resolved against the current directory
            for C: (rooted, but relative). "C:\a" is rooted and not relative (the current directory
            will not be used to modify the path).
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.SetLoaderDllFolderPath(System.String)">
            <summary>
            Set the path of the folder containing the `WebView2Loader.dll`.
            </summary>
            <param name="folderPath">The path of the folder containing the `WebView2Loader.dll`.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when `WebView2Loader.dll` has been successfully loaded.
            </exception>
            <remarks>
            This function allows you to set the path of the folder containing the `WebView2Loader.dll`. This should be the path of a folder containing `WebView2Loader.dll` and not a path to the `WebView2Loader.dll` file itself.
            Note that the WebView2 SDK contains multiple `WebView2Loader.dll` files for different CPU architectures. When specifying folder path, you must specify one containing a `WebView2Loader.dll` module with a CPU architecture matching the current process CPU architecture.
            This function is used to load the `WebView2Loader.dll` module during calls to any other static methods on `CoreWebView2Environment`. So, the path should be specified before any other API is called in `CoreWebView2Environment` class. Once `WebView2Loader.dll` is successfully loaded this function will throw an InvalidOperationException exception.
            The path can be relative or absolute. Relative paths are relative to the path of the `Microsoft.Web.WebView2.Core.dll` module.
            If the `WebView2Loader.dll` file does not exist in that path or LoadLibrary cannot load the file, or LoadLibrary fails for any other reason, an exception corresponding to the LoadLibrary failure is thrown when any other API is called in `CoreWebView2Environment` class. For instance, if the file cannot be found a `DllNotFoundException` exception will be thrown.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Environment.BrowserVersionString">
            <summary>
            Gets the browser version info of the current <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/>, including channel name if it is not the stable channel.
            </summary>
            <remarks>
            It matches the format of the <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.GetAvailableBrowserVersionString(System.String)"/> method. Channel names are <c>beta</c>, <c>dev</c>, and <c>canary</c>.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Environment.NewBrowserVersionAvailable">
            <summary>
            NewBrowserVersionAvailable is raised when a newer version of the WebView2 Runtime is installed and available using WebView2.
            </summary>
            <remarks>
            To use the newer version of the browser you must create a new environment and WebView. The event is only raised for new version from the same WebView2 Runtime from which the code is running. When not running with installed WebView2 Runtime, no event is raised.
            
            Because a user data folder is only able to be used by one browser process at a time, if you want to use the same user data folder in the WebViews using the new version of the browser, you must close the environment and instance of WebView that are using the older version of the browser first. Or simply prompt the user to restart the app.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="SubscribeToNewBrowserVersionAvailable":::
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="NewBrowserVersionAvailable":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2ControllerAsync(System.IntPtr)">
            <summary>
            Asynchronously creates a new WebView.
            </summary>
            <param name="ParentWindow">The HWND in which the WebView should be displayed and from which receive input.</param>
            <remarks>
            The WebView adds a child window to the provided window during WebView creation. Z-order and other things impacted by sibling window order are affected accordingly.
            
            <para>
            HWND_MESSAGE is a valid parameter for <c>ParentWindow</c> for an invisible WebView for Windows 8 and above. In this case the window will never become visible. You are not able to reparent the window after you have created the WebView. This is not supported in Windows 7 or below. Passing this parameter in Windows 7 or below will return ERROR_INVALID_WINDOW_HANDLE in the controller callback.
            </para>
            
            <para>
            It can also accept a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions"/> which is created by <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2ControllerOptions"/> as the second parameter for multiple profiles support. As WebView2 is built on top of Edge browser, it follows Edge's behavior pattern. To create an InPrivate WebView, we gets an off-the-record profile (an InPrivate profile) from a regular profile, then create the WebView with the off-the-record profile. Multiple profiles under single user data directory can share some system resources including memory, CPU footprint, disk space (such as compiled shaders and safebrowsing data) etc.
            </para>
            
            <para>
            It is recommended that the application set Application User Model ID for the process or the application window. If none is set, during WebView creation a generated Application User Model ID is set to root window of <c>ParentWindow</c>.
            </para>
            
            <para>
            It is recommended that the app handles restart manager messages, to gracefully restart it in the case when the app is using the WebView2 Runtime from a certain installation and that installation is being uninstalled. For example, if a user installs a version of the WebView2 Runtime and opts to use another version of the WebView2 Runtime for testing the app, and then uninstalls the 1st version of the WebView2 Runtime without closing the app, the app restarts to allow un-installation to succeed.
            </para>
            
            <para>
            When the app retries CreateCoreWebView2ControllerAsync upon failure, it is recommended that the app restarts from creating a new WebView2 Environment. If a WebView2 Runtime update happens, the version associated with a WebView2 Environment may have been removed and causing the object to no longer work. Creating a new WebView2 Environment works since it uses the latest version.
            </para>
            
            <para>
            WebView creation fails if a running instance using the same user data folder exists, and the Environment objects have different <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions"/>. For example, if a WebView was created with one <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions.Language"/>, an attempt to create a WebView with a different <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions.Language"/> using the same user data folder fails.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateWebResourceResponse(System.IO.Stream,System.Int32,System.String,System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponse"/> object.
            </summary>
            <param name="Content">HTTP response content as stream.</param>
            <param name="StatusCode">The HTTP response status code.</param>
            <param name="ReasonPhrase">The HTTP response reason phrase.</param>
            <param name="Headers">The raw response header string delimited by newline.</param>
            <remarks>
            It is also possible to create this object with empty headers string and then use the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders"/> to construct the headers line by line.
            </remarks>
            <seealso cref="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponse"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2CompositionControllerAsync(System.IntPtr)">
            <summary>
            Asynchronously creates a new WebView for use with visual hosting.
            </summary>
            <param name="ParentWindow">The HWND in which the app will connect the visual tree of the WebView.</param>
            <remarks>
            <c>ParentWindow</c> will be the HWND that the app will receive pointer/mouse input meant for the WebView (and will need to use <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.SendMouseInput(Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind,Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys,System.UInt32,System.Drawing.Point)"/> or <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.SendPointerInput(Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind,Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo)"/> to forward). If the app moves the WebView visual tree to underneath a different window, then it needs to set <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ParentWindow"/> to update the new parent HWND of the visual tree.
            
            Set <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.RootVisualTarget"/> property on the created <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController"/> to provide a visual to host the browser's visual tree.
            
            It is recommended that the application set Application User Model ID for the process or the application window. If none is set, during WebView creation a generated Application User Model ID is set to root window of <c>ParentWindow</c>.
            
            It can also accept a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions"/> which is created by <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2ControllerOptions"/> as the second parameter for multiple profiles support.
            
            CreateCoreWebView2CompositionController is supported in the following versions of Windows:
            <list type="bullet">
            <item><description>
            Windows 11
            </description></item>
            <item><description>
            Windows 10
            </description></item>
            <item><description>
            Windows Server 2019
            </description></item>
            <item><description>
            Windows Server 2016
            </description></item>
            </list>
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2PointerInfo">
            <summary>
            Creates an empty <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo"/>.
            </summary>
            <remarks>
            The returned <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo"/> needs to be populated with all of the relevant info before calling <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.SendPointerInput(Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind,Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo)"/>.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Environment.BrowserProcessExited">
            <summary>
            BrowserProcessExited is raised when the collection of WebView2 Runtime processes for the browser process of this <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> terminate due to browser process failure or normal shutdown (for example, when all associated WebViews are closed), after all resources have been released (including the user data folder).
            </summary>
            <remarks>
            Multiple app processes can share a browser process by creating their webviews from a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> with the same user data folder. When the entire collection of WebView2Runtime processes for the browser process exit, all associated <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> objects receive the BrowserProcessExited event. Multiple processes sharing the same browser process need to coordinate their use of the shared user data folder to avoid race conditions and unnecessary waits. For example, one process should not clear the user data folder at the same time that another process recovers from a crash by recreating its WebView controls; one process should not block waiting for the event if other app processes are using the same browser process (the browser process will not exit until those other processes have closed their webviews too).
            
            Note this is an event from <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/>, not <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/>. The difference between BrowserProcessExited and <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ProcessFailed"/> is that BrowserProcessExited is raised for any <strong>browser process</strong> exit (expected or unexpected, after all associated processes have exited too), while <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ProcessFailed"/> is raised for <strong>unexpected</strong> process exits of any kind (browser, render, GPU, and all other types), or for main frame <strong>render process</strong> unresponsiveness. To learn more about the WebView2 Process Model, go to [Process model](/microsoft-edge/webview2/concepts/process-model).
            
            In the case the browser process crashes, both BrowserProcessExited and <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ProcessFailed"/> events are raised, but the order is not guaranteed. These events are intended for different scenarios. It is up to the app to coordinate the handlers so they do not try to perform reliability recovery while also trying to move to a new WebView2 Runtime version or remove the user data folder.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="SubscribeToBrowserProcessExited":::
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="BrowserProcessExited":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreatePrintSettings">
            <summary>
            Creates the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings"/> used by the <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.PrintToPdfAsync(System.String,Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings)"/> method.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Environment.UserDataFolder">
            <summary>
            Gets the user data folder that all CoreWebView2s created from this environment are using.
            </summary>
            <remarks>
            This could be either the value passed in by the developer when creating the environment object or the calculated one for default handling. And will always be an absolute path.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Environment.ProcessInfosChanged">
            <summary>
            ProcessInfosChanged is raised when a collection of WebView2 Runtime processes changed due to new process being detected or when a existing process gone away.
            </summary>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="ProcessInfosChanged":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.GetProcessInfos">
            <summary>
            Returns the list of <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ProcessInfo"/>.
            </summary>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="GetProcessInfos":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateContextMenuItem(System.String,System.IO.Stream,Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItemKind)">
            <summary>
            Create a custom <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/> object to insert into the WebView context menu.
            </summary>
            <remarks>
            CoreWebView2 will rewind the <c>icon</c> stream before decoding.
            There is a limit of 1000 active custom context menu items at a given time per <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/>. Attempting to create more before deleting existing ones will fail with <c>ERROR_NOT_ENOUGH_QUOTA</c>. It is recommended to reuse custom ContextMenuItems across CoreWebView2ContextMenuRequested events for performance. The created object's <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.IsEnabled"/> property will default to <c>true</c> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.IsChecked"/> property will default to <c>false</c>. A <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.CommandId"/> will be assigned that's unique across active custom context menu items, but command ID values of deleted custom ContextMenuItems can be reassigned.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions">
            <summary>
            Options used to create WebView2 Environment.
            </summary>
            <remarks>
            Default values will use your defaulted Edge WebView2 Runtime binaries and user data folder.
            </remarks>
            
            
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions.#ctor(System.String,System.String,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the CoreWebView2EnvironmentOptions class.
            </summary>
            <param name="additionalBrowserArguments">
            AdditionalBrowserArguments can be specified to change the behavior of the WebView.
            </param>
            <param name="language">
            The default language that WebView will run with.
            </param>
            <param name="targetCompatibleBrowserVersion">
            The version of the Edge WebView2 Runtime binaries required to be compatible with the calling application.
            </param>
            <param name="allowSingleSignOnUsingOSPrimaryAccount">
            Set to true if single sign on be enabled using the end user's OS primary account. Defaults to false.
            </param>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions.AdditionalBrowserArguments">
            <summary>
            Gets or sets the additional browser arguments to change the behavior of the WebView.
            </summary>
            <remarks>
            <para>
            The arguments are passed to the browser process as part of the command. For more information about using command-line switches with Chromium browser processes, navigate to [Run Chromium with Flags](https://aka.ms/RunChromiumWithFlags). The value appended to a switch is appended to the browser process, for example, in <c>--edge-webview-switches=xxx</c> the value is <c>xxx</c>. If you specify a switch that is important to WebView functionality, it is ignore, for example, <c>--user-data-dir</c>. Specific features are disabled internally and blocked from being enabled. If a switch is specified multiple times, only the last instance is used.
            </para>
            
            <para>
            A merge of the different values of the same switch is not attempted, except for disabled and enabled features. The features specified by <c>--enable-features</c> and <c>--disable-features</c> will be merged with simple logic:
            
            <list type="bullet">
            <item><description>
            The features are the union of the specified features and built-in features. If a feature is disabled, it is removed from the enabled features list.
            </description></item>
            </list>
            </para>
            <para>
            If you specify command-line switches and sets this property, the <c>--edge-webview-switches</c> value takes precedence and is processed last. If a switch fails to parse, the switch is ignored. The default state for the operation is to run the browser process with no extra flags.
            </para>
            <para>
            Please note that calling this API twice will replace the previous value rather than appending to it. If there are multiple switches, there should be a space in between them. The one exception is if multiple features are being enabled/disabled for a single switch, in which case the features should be comma-separated. Ex. "--disable-features=feature1,feature2 --some-other-switch --do-something"
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions.Language">
            <summary>
            Gets or sets the default display language for WebView.
            </summary>
            <remarks>
            It applies to browser UIs such as context menu and dialogs. It also applies to the <c>accept-languages</c> HTTP header that WebView sends to websites. It is in the format of <c>language[-country]</c> where <c>language</c> is the 2-letter code from [ISO 639](https://www.iso.org/iso-639-language-codes.html) and <c>country</c> is the 2-letter code from [ISO 3166](https://www.iso.org/standard/72482.html).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions.TargetCompatibleBrowserVersion">
            <summary>
            Gets or sets the version of the WebView2 Runtime binaries required to be compatible with your app.
            </summary>
            <remarks>
            This defaults to the WebView2 Runtime version that corresponds with the version of the SDK the app is using. The format of this value is the same as the format of the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Environment.BrowserVersionString"/> property and other BrowserVersion values. Only the version part of the BrowserVersion value is respected. The channel suffix, if it exists, is ignored. The version of the WebView2 Runtime binaries actually used may be different from the specified TargetCompatibleBrowserVersion. They binaries are only guaranteed to be compatible. Verify the actual version on the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Environment.BrowserVersionString"/> property.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions.AllowSingleSignOnUsingOSPrimaryAccount">
            <summary>
            Determines whether to enable single sign on with Azure Active Directory (AAD) resources inside WebView using the logged in Windows account and single sign on (SSO) with web sites using Microsoft account associated with the login in Windows account.
            </summary>
            <remarks>
            The default value is <c>false</c>. Universal Windows Platform apps must also declare <c>enterpriseCloudSSO</c> [restricted capability](/windows/uwp/packaging/app-capability-declarations#restricted-capabilities) for the single sign on (SSO) to work.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions.ExclusiveUserDataFolderAccess">
            <summary>
            Determines whether other processes can create <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> from <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> created with the same user data folder and therefore sharing the same WebView browser process instance.
            </summary>
            <remarks>
            The default value is <c>false</c>.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2Frame">
            <summary>
            CoreWebView2Frame provides direct access to the iframes information and handling.
            </summary>
            <summary>
            CoreWebView2Frame provides direct access to the iframes information and handling. You can get a CoreWebView2Frame by handling the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.FrameCreated"/> event.
            </summary>
            <summary>
            CoreWebView2Frame provides direct access to the iframes information and handling. You can get a CoreWebView2Frame by handling the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.FrameCreated"/> event.
            </summary>
            <summary>
            CoreWebView2Frame provides direct access to the iframes information and handling. You can get a CoreWebView2Frame by handling the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.FrameCreated"/> event.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Frame.AddHostObjectToScript(System.String,System.Object,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds the provided host object to script running in the WebViewFrame with the specified name for the list of the specified origins.
            </summary>
            <param name="name">
            The name of the host object.
            </param>
            <param name="rawObject">
            The host object to be added to script.
            </param>
            <param name="origins">
            The list of the iframe origins for which host object will be accessible.
            </param>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.AddHostObjectToScript(System.String,System.Object)"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Frame.Name">
            <summary>
            The name of the iframe from the iframe html tag declaring it.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.NameChanged">
            <summary>
            NameChanged is raised when the iframe changes its <c>window.name</c> property.
            </summary>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.RemoveHostObjectFromScript(System.String)"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.Destroyed">
            <summary>
            Destroyed event is raised when the iframe corresponding to this <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Frame"/> object is removed or the document containing that iframe is destroyed.
            </summary>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.RemoveHostObjectFromScript(System.String)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Frame.RemoveHostObjectFromScript(System.String)">
            <summary>
            Remove the host object specified by the name so that it is no longer accessible from JavaScript code in the iframe.
            </summary>
            <remarks>
            While new access attempts are denied, if the object is already obtained by JavaScript code in the iframe, the JavaScript code continues to have access to that object. Calling this method for a name that is already removed or was never added fails. If the iframe is destroyed this method will return fail also.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Frame.IsDestroyed">
            <summary>
            Check whether a frame is destroyed. Returns true during the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.Destroyed"/> event.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.NavigationStarting">
            <summary>
            NavigationStarting is raised when the current frame is requesting permission to navigate to a different URI.
            </summary>
            <remarks>
            A frame navigation will raise a <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.NavigationStarting"/> event and a <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.FrameNavigationStarting"/> event. All of the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.FrameNavigationStarting"/> event handlers will be run before the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.NavigationStarting"/> event handlers. All of the event handlers share a common <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2NavigationStartingEventArgs"/> object. Whichever event handler is last to change the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationStartingEventArgs.Cancel"/> property will decide if the frame navigation will be cancelled.
            Redirects raise this event as well, and the navigation id is the same as the original one. You may block corresponding navigations until the event handler returns.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.ContentLoading">
            <summary>
            ContentLoading is raised before any content is loaded, including scripts added with <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync(System.String)"/>. ContentLoading is not raised if a same page navigation occurs.
            </summary>
            <remarks>
            This operation follows the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.NavigationStarting"/> event and precedes the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.DOMContentLoaded"/> and <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.NavigationCompleted"/> events.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.NavigationCompleted">
            <summary>
            NavigationCompleted is raised when the current frame has completely loaded (<c>body.onload</c> has been raised) or loading stopped with error.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.DOMContentLoaded">
            <summary>
            DOMContentLoaded is raised when the initial HTML document has been parsed.
            </summary>
            <remarks>
            This aligns with the the document's <c>DOMContentLoaded</c> event in HTML.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.WebMessageReceived">
            <summary>
            WebMessageReceived is raised when the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsWebMessageEnabled"/> setting is set and the iframe runs <c>window.chrome.webview.postMessage</c>.
            </summary>
            <remarks>
            The <c>postMessage</c> function is <c>void postMessage(object)</c> where object is any object supported by JSON conversion.
            When <c>postMessage</c> is called, the handler's Invoke method will be called with the <c>object</c> parameter <c>postMessage</c> converted to a JSON string.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="WebMessageReceivedIFrame":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Frame.ExecuteScriptAsync(System.String)">
            <summary>
            Runs JavaScript code from the <c>javaScript</c> parameter in the current frame.
            </summary>
            <param name="javaScript">The JavaScript code to be run in the current frame.</param>
            <returns>A JSON encoded string that represents the result of running the provided JavaScript.</returns>
            <remarks>
            A function that has no explicit return value returns <c>undefined</c>. If the script that was run throws an unhandled exception, then the result is also <c>null</c>. This method is applied asynchronously.
            If the method is run before <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.ContentLoading"/>, the script will not be executed and the JSON <c>null</c> will be returned.
            This operation works even if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsScriptEnabled"/> is set to <c>false</c>.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="ExecuteScriptFrame":::
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Frame.PostWebMessageAsJson(System.String)">
            <summary>
            Posts the specified <c>webMessageAsJson</c> to the current frame.
            </summary>
            <param name="webMessageAsJson">The web message to be posted to the iframe.</param>
            <remarks>
            The event args is an instance of <c>MessageEvent</c>. The <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsWebMessageEnabled"/> setting must be <c>true</c> or this method will fail with E_INVALIDARG. The event arg's <c>data</c> property of the event arg is the <c>webMessageAsJson</c> string parameter parsed as a JSON string into a JavaScript object. The event arg's <c>source</c> property of the event arg is a reference to the <c>window.chrome.webview</c> object. For information about sending messages from the iframe to the host, navigate to <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.WebMessageReceived"/>. The message is sent asynchronously. If a navigation occurs before the message is posted to the iframe, the message is not be sent.
            </remarks>
            <example>
            Runs the message event of the <c>window.chrome.webview</c> of the iframe. JavaScript in that document may subscribe and unsubscribe to the event using the following code:
            <code>
            window.chrome.webview.addEventListener('message', handler)
            window.chrome.webview.removeEventListener('message', handler)
            </code>
            </example>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsWebMessageEnabled"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.WebMessageReceived"/>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Frame.PostWebMessageAsString(System.String)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Frame.PostWebMessageAsString(System.String)">
            <summary>
            Posts a message that is a simple string rather than a JSON string representation of a JavaScript object.
            </summary>
            <param name="webMessageAsString">The web message to be posted to the iframe.</param>
            <remarks>
            This behaves in exactly the same manner as <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Frame.PostWebMessageAsJson(System.String)"/>, but the <c>data</c> property of the event arg of the <c>window.chrome.webview</c> message is a string with the same value as <c>webMessageAsString</c>. Use this instead of <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Frame.PostWebMessageAsJson(System.String)"/> if you want to communicate using simple strings rather than JSON objects.
            </remarks>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.WebMessageReceived"/>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Frame.PostWebMessageAsJson(System.String)"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2Frame.PermissionRequested">
            <summary>
            PermissionRequested is raised when content in an iframe or any of its descendant iframes requests permission to access some privileged resources.
            </summary>
            <remarks>
            This relates to the <c>PermissionRequested</c> event on the <c>CoreWebView2</c>.
            Both these events will be raised in the case of an iframe requesting permission. The <c>CoreWebView2Frame</c>'s event handlers will be invoked before the event handlers on the <c>CoreWebView2</c>. If the <c>Handled</c> property of the <c>PermissionRequestedEventArgs</c> is set to TRUE within the <c>CoreWebView2Frame</c> event handler, then the event will not be raised on the <c>CoreWebView2</c>, and it's event handlers will not be invoked.
            In the case of nested iframes, the <c>PermissionRequested</c> event will be raised from the top level iframe.
            If a deferral is not taken on the event args, the subsequent scripts are blocked until the event handler returns. If a deferral is taken, the scripts are blocked until the deferral is completed.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PrivateHostObjectHelper">
            <summary>
            This represents a helper object for Host Objects.
            </summary>
            <remarks>
            This object helps CoreWebView2 interact with managed Host Objects correctly.
            </remarks>
            
            
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateHostObjectHelper.#ctor">
            <summary>
            Create a new instance of CoreWebView2PrivateHostObjectHelper.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateHostObjectHelper.RawHelper.GetMethodInfo(System.Type,System.String,System.Nullable{System.Int32})">
            <summary>
            If a named method exists on a specified type then retrieve its MethodInfo.
            </summary>
            <param name="type">The type to find a method on.</param>
            <param name="methodName">The name of the method to find.</param>
            <param name="parameterCount">The parameter count of the method overload to find, or null to find any overload.</param>
            <returns>
            The MethodInfo of the found method; if parameterCount is null then it might be any of the method's overloads.
            If the type has a non-method member (e.g. a property) with the specified name, then null is returned.
            </returns>
            <exception cref="T:System.Runtime.InteropServices.COMException">If the type isn't a class or the named member doesn't exist on that class.</exception>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateHostObjectHelper.RawHelper.IsMethodMember(System.Object@,System.String)">
            <summary>
            Check whether a member is a method of an object.
            </summary>
            <param name="rawObject">
            The host object to check.
            </param>
            <param name="memberName">
            The name of the member to check.
            </param>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateHostObjectHelper.RawHelper.IsAsyncMethod(System.Object@,System.String,System.Int32)">
            <summary>
            Checks whether a method of a host object is async or not.
            </summary>
            <param name="rawObject">The host object to check.</param>
            <param name="methodName">The name of the method to check.</param>
            <param name="parameterCount">The parameter count of the method to check (to help differentiate overloads).</param>
            <returns>True if the specified method is async or false if it isn't.</returns>
            <exception cref="T:System.Runtime.InteropServices.COMException">If the host object is unrecognized or doesn't have a method with that name.</exception>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateHostObjectHelper.RawHelper.SetAsyncMethodContinuation(System.Object@,System.String,System.Int32,System.Object@,Microsoft.Web.WebView2.Core.Raw.ICoreWebView2PrivateHostObjectAsyncMethodContinuation)">
            <summary>
            Wires an awaitable object from an async method (e.g. a Task) to call a continuation when it finishes.
            </summary>
            <param name="rawObject">The host object on which an async method was invoked.</param>
            <param name="methodName">The async method which was invoked on the host object.</param>
            <param name="parameterCount">Number of parameters passed to the async method (to help differentiate method overloads).</param>
            <param name="methodResult">The awaitable object (usually a Task) which was returned from the async method.</param>
            <param name="continuation">An object with an Invoke method that we need to call when the methodResult Task completes.</param>
            <exception cref="T:System.Runtime.InteropServices.COMException">If the host object is unrecognized, doesn't have a method with that name, or methodResult is not awaitable.</exception>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PrivateHostObjectHelper.AwaitableReflection">
            <summary>
            A helper class that wraps up all of the reflection information related to an awaitable type
            and provides methods to invoke functionality on awaitable objects of that type and their awaiter objects.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateHostObjectHelper.AwaitableReflection.FromAwaitableType(System.Type)">
            <summary>
            Creates a new AwaitableReflection for a given awaitable type.
            </summary>
            <param name="type">The type to create an AwaitableReflection for.</param>
            <returns>An AwaitableReflection instance for the given type, or null if the given type isn't awaitable.</returns>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateHostObjectHelper.IsMethodMember(System.Object,System.String)">
            
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateHostObjectHelper.IsAsyncMethod(System.Object,System.String,System.Int32)">
            
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateHostObjectHelper.SetAsyncMethodContinuation(System.Object,System.String,System.Int32,System.Object,Microsoft.Web.WebView2.Core.CoreWebView2PrivateHostObjectAsyncMethodContinuation)">
            
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpHeadersCollectionIterator">
            <inheritdoc/>
            <summary>
            Iterator for a collection of HTTP headers.
            </summary>
            <seealso cref="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders"/>
            <seealso cref="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders"/>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2HttpHeadersCollectionIterator.isInitialized">
            <summary>
            Indicate the iterator is initialized or not, default to false.
            The isInitialized is used to indicate if the iterator starts for the first time.
            It will be used in MoveNext function.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpHeadersCollectionIterator.MoveNext">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpHeadersCollectionIterator.Reset">
            <summary>
            No COM support; throws <see cref="T:System.NotSupportedException"/> instead.
            </summary>
            <exception cref="T:System.NotSupportedException">
            No COM support.
            </exception>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpHeadersCollectionIterator.Dispose">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2HttpHeadersCollectionIterator.Current">
            <summary>
            Gets the header in the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders"/>
            or <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders"/> collection at the
            current position of the enumerator.
            </summary>
            <exception cref="T:System.InvalidOperationException"></exception>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2HttpHeadersCollectionIterator.HasCurrentHeader">
            <summary>
            <c>true</c> when the iterator has not run out of headers.
            </summary>
            <remarks>
            If the collection over which the iterator is iterating is empty or if the iterator has gone past the end of the collection then this is <c>false</c>.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpHeadersCollectionIterator.GetCurrentHeader(System.String@,System.String@)">
            
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders">
            <summary>
            HTTP request headers.
            </summary>
            <remarks>
            Used to inspect the HTTP request on <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event and <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event. It is possible to modify the HTTP request headers from a <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event, but not from a <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event.
            </remarks>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{System#String,System#String}}#GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the <see
            cref="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders"/> or <see
            cref="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders"/> collection.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders.GetHeader(System.String)">
            <summary>
            Gets the header value matching the name.
            </summary>
            <returns>The header value matching the name.</returns>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders.GetHeaders(System.String)">
            <summary>
            Gets the header value matching the name using a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpHeadersCollectionIterator"/>.
            </summary>
            <returns>The header value matching the name.</returns>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders.Contains(System.String)">
            <summary>
            Checks whether the headers contain an entry that matches the header name.
            </summary>
            <returns>Whether the headers contain an entry that matches the header name.</returns>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders.SetHeader(System.String,System.String)">
            <summary>
            Adds or updates header that matches the name.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders.RemoveHeader(System.String)">
            <summary>
            Removes header that matches the name.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders.GetIterator">
            <summary>
            Gets a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpHeadersCollectionIterator"/> over the collection of request headers.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders">
            <summary>
            HTTP response headers.
            </summary>
            <remarks>
            Used to construct a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponse"/> for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{System#String,System#String}}#GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the <see
            cref="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders"/> or <see
            cref="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders"/> collection.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders.AppendHeader(System.String,System.String)">
            <summary>
            Appends header line with name and value.
            </summary>
            <param name="name">The header name to be appended.</param>
            <param name="value">The header value to be appended.</param>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders.Contains(System.String)">
            <summary>
            Checks whether this CoreWebView2HttpResponseHeaders contain entries matching the header name.
            </summary>
            <param name="name">The name of the header to seek.</param>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders.GetHeader(System.String)">
            <summary>
            Gets the first header value in the collection matching the name.
            </summary>
            <param name="name">The header name.</param>
            <returns>The first header value in the collection matching the name.</returns>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders.GetHeaders(System.String)">
            <summary>
            Gets the header values matching the name.
            </summary>
            <param name="name">The header name.</param>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders.GetIterator">
            <summary>
            Gets a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpHeadersCollectionIterator"/> over the collection of entire <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2InitializationCompletedEventArgs">
            <summary>
            Event args for the CoreWebView2InitializationCompleted event.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2InitializationCompletedEventArgs.#ctor(System.Exception)">
            <summary>
            Initializes a new instance of the CoreWebView2InitializationCompletedEventArgs class.
            </summary>
            <param name="ex">
            Exception that occurred during initialization, or null if initialization was successful.
            </param> 
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2InitializationCompletedEventArgs.IsSuccess">
            <summary>
            True if the init task completed successfully.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2InitializationCompletedEventArgs.InitializationException">
            <summary>
            The exception thrown from the init task. If the task completed successfully, this property is null.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2Profile">
            <summary>
            Multiple profiles can be created under a single user data directory but with separated cookies, user preference settings, and various data storage etc.. If the CoreWebView2 was created with a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions"/>, the CoreWebView2Profile will match those specified options. Otherwise if this CoreWebView2 was created without a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions"/>, then this will be the default CoreWebView2Profile for the corresponding CoreWebView2Environment.
            </summary>
            <summary>
            Multiple profiles can be created under a single user data directory but with separated cookies, user preference settings, and various data storage etc.. If the CoreWebView2 was created with a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions"/>, the CoreWebView2Profile will match those specified options. Otherwise if this CoreWebView2 was created without a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions"/>, then this will be the default CoreWebView2Profile for the corresponding CoreWebView2Environment.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Profile.ClearBrowsingDataAsync(Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds,System.DateTime,System.DateTime)">
            <summary>
            Clear the browsing data for the specified dataKinds between the
            startTime and endTime. Overload the ClearBrowsingDataAsync method to
            allow for additional time parameters.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Profile.ClearBrowsingDataAsync">
            <summary>
            Clear the entirety of the browsing data associated with the profile
            it is called on. It clears the data regardless of timestamp.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Profile.ProfileName">
            <summary>
            The name of the profile.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Profile.IsInPrivateModeEnabled">
            <summary>
            InPrivate mode is enabled or not.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Profile.ProfilePath">
            <summary>
            Full path of the profile directory.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Profile.DefaultDownloadFolderPath">
            <summary>
            The default download folder path.
            </summary>
            <remarks>
            The default value is the system default download folder path for the user. The default download folder path is persisted in the user data folder across sessions. The value should be an absolute path to a folder that the user and application can write to. Throws an exception if the value is invalid, and the default download path is not changed. Otherwise the path is changed immediately. If the directory does not yet exist, it is created at the time of the next download. If the host application does not have permission to create the directory, then the user is prompted to provide a new path through the Save As dialog. The user can override the default download folder path for a given download by choosing a different path in the Save As dialog.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Profile.PreferredColorScheme">
            <summary>
            The PreferredColorScheme property sets the overall color scheme of the WebView2s associated with this profile.
            </summary>
            <remarks>
            This sets the color scheme for WebView2 UI like dialogs, prompts, and menus by setting the media feature <c>prefers-color-scheme</c>.
            The default value for this is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2PreferredColorScheme.Auto"/>, which will follow whatever color scheme the OS is currently set to.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Profile.ClearBrowsingDataAsync(Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds)">
            <summary>
            Clear the browsing data of the associated profile.
            </summary>
            <remarks>
            Clears browsing data on the profile the method is called on. Additional optional parameters include the start time and end time to clear the browsing data between as well as the data specific data kinds to clear on the profile. The method may be overloaded to take:
            <list type="bullet">
            <item><description>
            No parameters - in which the entirety of the data on the profile will be cleared.
            </description></item>
            <item><description>
            The data kind(s) - in which the data kind(s) will be cleared for their entirety.
            </description></item>
            <item><description>
            The data kind(s), start time, and end time - in which the data kind(s) will be cleared between the start and end time. The start time will be offset by -1.0 and the end time will be offset by +1.0 to include the last fractional second on each respective end. The start time is inclusive in the time period while the end time is exclusive.
            </description></item>
            </list>
            
            The exposed methods are as follows:
            <code>
            ClearBrowsingDataAsync(CoreWebView2BrowsingDataKinds dataKinds);
            ClearBrowsingDataAsync(CoreWebView2BrowsingDataKinds dataKinds, DateTime startTime, DateTime endTime);
            ClearBrowsingDataAsync();
            </code>
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="ClearBrowsingData":::
            </example>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2Deferral">
            <summary>
            This is used to complete deferrals on event args that support getting deferrals using the <c>GetDeferral</c> method. This class implements <see cref="T:System.IDisposable"/>.
            </summary>
            
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2Deferral.handle">
            Wraps in SafeHandle so resources can be released if consumer forgets to call Dispose. Recommended
            pattern for any type that is not sealed.
            https://docs.microsoft.com/dotnet/api/system.idisposable#idisposable-and-the-inheritance-hierarchy
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Deferral.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Deferral.Dispose(System.Boolean)">
            <summary>
            Protected implementation of Dispose pattern.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2Deferral.Complete">
            <summary>
            Completes the associated deferred event.
            </summary>
            <remarks>
            Complete should only be run once for each deferral taken.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.HostObjectHelper">
            <summary>
            This class is deprecated; use CoreWebView2PrivateHostObjectHelper instead.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.ManagedIStream.#ctor(System.IO.Stream)">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.ManagedIStream.System#Runtime#InteropServices#ComTypes#IStream#Read(System.Byte[],System.Int32,System.IntPtr)">
             <summary>
             Read at most bufferSize bytes into buffer and return the effective
             number of bytes read in bytesReadPtr (unless null).
             </summary>
             <remarks>
             mscorlib disassembly shows the following MarshalAs parameters
             void Read([Out, MarshalAs(UnmanagedType.LPArray, SizeParamIndex=1)] byte[] pv, int cb, IntPtr pcbRead);
             This means marshaling code will have found the size of the array buffer in the parameter bufferSize.
             </remarks>
            <SecurityNote>
                 Critical: calls Marshal.WriteInt32 which LinkDemands, takes pointers as input
            </SecurityNote>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.ManagedIStream.System#Runtime#InteropServices#ComTypes#IStream#Seek(System.Int64,System.Int32,System.IntPtr)">
             <summary>
             Move the stream pointer to the specified position.
             </summary>
             <remarks>
             System.IO.stream supports searching past the end of the stream, like
             OLE streams.
             newPositionPtr is not an out parameter because the method is required
             to accept NULL pointers.
             </remarks>
            <SecurityNote>
                 Critical: calls Marshal.WriteInt64 which LinkDemands, takes pointers as input
            </SecurityNote>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.ManagedIStream.System#Runtime#InteropServices#ComTypes#IStream#SetSize(System.Int64)">
            <summary>
            Sets stream's size.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.ManagedIStream.System#Runtime#InteropServices#ComTypes#IStream#Stat(System.Runtime.InteropServices.ComTypes.STATSTG@,System.Int32)">
            <summary>
            Obtain stream stats.
            </summary>
            <remarks>
            STATSG has to be qualified because it is defined both in System.Runtime.InteropServices and
            System.Runtime.InteropServices.ComTypes.
            The STATSTG structure is shared by streams, storages and byte arrays. Members irrelevant to streams
            or not available from System.IO.Stream are not returned, which leaves only cbSize and grfMode as 
            meaningful and available pieces of information.
            grfStatFlag is used to indicate whether the stream name should be returned and is ignored because
            this information is unavailable.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.ManagedIStream.System#Runtime#InteropServices#ComTypes#IStream#Write(System.Byte[],System.Int32,System.IntPtr)">
             <summary>
             Write at most bufferSize bytes from buffer.
             </summary>
            <SecurityNote>
                 Critical: calls Marshal.WriteInt32 which LinkDemands, takes pointers as input
            </SecurityNote>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.ManagedIStream.System#Runtime#InteropServices#ComTypes#IStream#Clone(System.Runtime.InteropServices.ComTypes.IStream@)">
            <summary>
            Create a clone.
            </summary>
            <remarks>
            Not implemented.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.ManagedIStream.System#Runtime#InteropServices#ComTypes#IStream#CopyTo(System.Runtime.InteropServices.ComTypes.IStream,System.Int64,System.IntPtr,System.IntPtr)">
            <summary>
            Read at most bufferSize bytes from the receiver and write them to targetStream.
            </summary>
            <remarks>
            Not implemented.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.ManagedIStream.System#Runtime#InteropServices#ComTypes#IStream#Commit(System.Int32)">
            <summary>
            Commit changes.
            </summary>
            <remarks>
            Only relevant to transacted streams.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.ManagedIStream.System#Runtime#InteropServices#ComTypes#IStream#LockRegion(System.Int64,System.Int64,System.Int32)">
            <summary>
            Lock at most byteCount bytes starting at offset.
            </summary>
            <remarks>
            Not supported by System.IO.Stream.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.ManagedIStream.System#Runtime#InteropServices#ComTypes#IStream#Revert">
            <summary>
            Undo writes performed since last Commit.
            </summary>
            <remarks>
            Relevant only to transacted streams.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.ManagedIStream.System#Runtime#InteropServices#ComTypes#IStream#UnlockRegion(System.Int64,System.Int64,System.Int32)">
            <summary>
            Unlock the specified region.
            </summary>
            <remarks>
            Not supported by System.IO.Stream.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.WebView2RuntimeNotFoundException">
            <summary>
            The exception that is thrown when an WebView2 Runtime installation is missing.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.WebView2RuntimeNotFoundException.#ctor">
            <summary>
            Initializes a new instance of the WebView2RuntimeNotFoundException class.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.WebView2RuntimeNotFoundException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the WebView2RuntimeNotFoundException class with a specified error message.
            </summary>
            <param name="message">
            The error message that explains the reason for the exception.
            </param>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.WebView2RuntimeNotFoundException.#ctor(System.Exception)">
            <summary>
            Initializes a new instance of the WebView2RuntimeNotFoundException class with a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="inner">
            The exception that is the cause of the current exception.
            </param>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.WebView2RuntimeNotFoundException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the WebView2RuntimeNotFoundException class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">
            The error message that explains the reason for the exception.
            </param>
            <param name="inner">
            The exception that is the cause of the current exception.
            </param>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.Dispatchcontainerscriptbehavior">
            
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.Dispatchcontainerscriptbehavior.One">
            
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.Dispatchcontainerscriptbehavior.Ector">
            
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.Dispatchcontainerscriptbehavior.Ap">
            
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext">
            <summary>
            Specifies the web resource request contexts.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.All">
            <summary>
            Specifies all resources.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.Document">
            <summary>
            Specifies a document resources.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.Stylesheet">
            <summary>
            Specifies a CSS resources.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.Image">
            <summary>
            Specifies an image resources.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.Media">
            <summary>
            Specifies another media resource such as a video.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.Font">
            <summary>
            Specifies a font resource.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.Script">
            <summary>
            Specifies a script resource.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.XmlHttpRequest">
            <summary>
            Specifies an XML HTTP request.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.Fetch">
            <summary>
            Specifies a Fetch API communication.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.TextTrack">
            <summary>
            Specifies a TextTrack resource.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.EventSource">
            <summary>
            Specifies an EventSource API communication.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.Websocket">
            <summary>
            Specifies a WebSocket API communication.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.Manifest">
            <summary>
            Specifies a Web App Manifest.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.SignedExchange">
            <summary>
            Specifies a Signed HTTP Exchange.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.Ping">
            <summary>
            Specifies a Ping request.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.CspViolationReport">
            <summary>
            Specifies a CSP Violation Report.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.Other">
            <summary>
            Specifies an other resource.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus">
            <summary>
            Indicates the error status values for web navigations.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.Unknown">
            <summary>
            Indicates that an unknown error occurred.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.CertificateCommonNameIsIncorrect">
            <summary>
            Indicates that the SSL certificate common name does not match the web address.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.CertificateExpired">
            <summary>
            Indicates that the SSL certificate has expired.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.ClientCertificateContainsErrors">
            <summary>
            Indicates that the SSL client certificate contains errors.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.CertificateRevoked">
            <summary>
            Indicates that the SSL certificate has been revoked.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.CertificateIsInvalid">
            <summary>
            Indicates that the SSL certificate is not valid. The certificate may not match the public key pins for the host name, the certificate is signed by an untrusted authority or using a weak sign algorithm, the certificate claimed DNS names violate name constraints, the certificate contains a weak key, the validity period of the certificate is too long, lack of revocation information or revocation mechanism, non-unique host name, lack of certificate transparency information, or the certificate is chained to a [legacy Symantec root](https://security.googleblog.com/2018/03/distrust-of-symantec-pki-immediate.html).
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.ServerUnreachable">
            <summary>
            Indicates that the host is unreachable.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.Timeout">
            <summary>
            Indicates that the connection has timed out.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.ErrorHttpInvalidServerResponse">
            <summary>
            Indicates that the server returned an invalid or unrecognized response.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.ConnectionAborted">
            <summary>
            Indicates that the connection was stopped.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.ConnectionReset">
            <summary>
            Indicates that the connection was reset.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.Disconnected">
            <summary>
            Indicates that the Internet connection has been lost.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.CannotConnect">
            <summary>
            Indicates that a connection to the destination was not established.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.HostNameNotResolved">
            <summary>
            Indicates that the provided host name was not able to be resolved.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.OperationCanceled">
            <summary>
            Indicates that the operation was canceled.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.RedirectFailed">
            <summary>
            Indicates that the request redirect failed.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.UnexpectedError">
            <summary>
            An unexpected error occurred.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.ValidAuthenticationCredentialsRequired">
            <summary>
            Indicates that user is prompted with a login, waiting on user action. Initial navigation to a login site will always return this even if app provides credential using <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.BasicAuthenticationRequested"/>. HTTP response status code in this case is 401. See status code reference here: https://developer.mozilla.org/docs/Web/HTTP/Status.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.ValidProxyAuthenticationRequired">
            <summary>
            Indicates that user lacks proper authentication credentials for a proxy server. HTTP response status code in this case is 407. See status code reference here: https://developer.mozilla.org/docs/Web/HTTP/Status.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorAction">
            <summary>
            Specifies the action type when server certificate error is detected to be used in the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorDetectedEventArgs"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorAction.AlwaysAllow">
            <summary>
            Indicates to ignore the warning and continue the request with the TLS certificate. This decision is cached for the RequestUri's host and the server certificate in the session.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorAction.Cancel">
            <summary>
            Indicates to reject the certificate and cancel the request.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorAction.Default">
            <summary>
            Indicates to display the default TLS interstitial error page to user for page navigations. For others TLS certificate is rejected and the request is cancelled.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogKind">
            <summary>
            Specifies the JavaScript dialog kind used in <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogKind.Alert">
            <summary>
            Indicates that the dialog uses <c>window.alert</c> JavaScript function.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogKind.Confirm">
            <summary>
            Indicates that the dialog uses <c>window.confirm</c> JavaScript function.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogKind.Prompt">
            <summary>
            Indicates that the dialog uses <c>window.prompt</c> JavaScript function.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogKind.Beforeunload">
            <summary>
            Indicates that the dialog uses <c>window.beforeunload</c> JavaScript event.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ProcessKind">
            <summary>
            Specifies the process kind used in <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ProcessInfo"/>.
            </summary>
            <remarks>
            The values in this enum make reference to the process kinds in the Chromium architecture. For more information about what these processes are and what they do, see [Browser Architecture - Inside look at modern web browser](https://developers.google.com/web/updates/2018/09/inside-browser-part1).
            </remarks>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessKind.Browser">
            <summary>
            Indicates that the process is browser process.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessKind.Renderer">
            <summary>
            Indicates that the process is render process.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessKind.Utility">
            <summary>
            Indicates that the process is utility process.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessKind.SandboxHelper">
            <summary>
            Indicates that the process is sandbox helper process.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessKind.Gpu">
            <summary>
            Indicates that the process is Gpu process.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessKind.PpapiPlugin">
            <summary>
            Indicates that the process is ppapi plugin process.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessKind.PpapiBroker">
            <summary>
            Indicates that the process is ppapi broker process.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedReason">
            <summary>
            Specifies the process failure reason used in <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedReason.Unexpected">
            <summary>
            Indicates that an unexpected process failure occurred.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedReason.Unresponsive">
            <summary>
            Indicates that the process became unresponsive. This only applies to the main frame's render process.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedReason.Terminated">
            <summary>
            Indicates that the process was terminated. For example, from Task Manager.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedReason.Crashed">
            <summary>
            Indicates that the process crashed.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedReason.LaunchFailed">
            <summary>
            Indicates that the process failed to launch.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedReason.OutOfMemory">
            <summary>
            Indicates that the process died due to running out of memory.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind">
            <summary>
            Specifies the process failure kind used in <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs"/>.
            </summary>
            <remarks>
            The values in this enum make reference to the process kinds in the Chromium architecture. For more information about what these processes are and what they do, see [Browser Architecture - Inside look at modern web browser](https://developers.google.com/web/updates/2018/09/inside-browser-part1).
            </remarks>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.BrowserProcessExited">
            <summary>
            Indicates that the browser process ended unexpectedly. The WebView automatically moves to the Closed state. The app has to recreate a new WebView to recover from this failure.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.RenderProcessExited">
            <summary>
            Indicates that the main frame's render process ended unexpectedly. A new render process is created automatically and navigated to an error page. You can use the <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Reload"/> method to try reload the page that failed.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.RenderProcessUnresponsive">
            <summary>
            Indicates that the main frame's render process is unresponsive.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.FrameRenderProcessExited">
            <summary>
            Indicates that a frame-only render process ended unexpectedly. The process exit does not affect the top-level document, only a subset of the subframes within it. The content in these frames is replaced with an error page in the frame.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.UtilityProcessExited">
            <summary>
            Indicates that a utility process ended unexpectedly.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.SandboxHelperProcessExited">
            <summary>
            Indicates that a sandbox helper process ended unexpectedly.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.GpuProcessExited">
            <summary>
            Indicates that the GPU process ended unexpectedly.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.PpapiPluginProcessExited">
            <summary>
            Indicates that a PPAPI plugin process ended unexpectedly.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.PpapiBrokerProcessExited">
            <summary>
            Indicates that a PPAPI plugin broker process ended unexpectedly.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.UnknownProcessExited">
            <summary>
            Indicates that a process of unspecified kind ended unexpectedly.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PrintOrientation">
            <summary>
            The orientation for printing, used by the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.Orientation"/> property. Currently only printing to PDF is supported.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PrintOrientation.Portrait">
            <summary>
            Print the page(s) in portrait orientation.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PrintOrientation.Landscape">
            <summary>
            Print the page(s) in landscape orientation.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PreferredColorScheme">
            <summary>
            Preferred color scheme for WebView2's associated with a profile.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PreferredColorScheme.Auto">
            <summary>
            Auto color scheme.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PreferredColorScheme.Light">
            <summary>
            Light color scheme.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PreferredColorScheme.Dark">
            <summary>
            Dark color scheme.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind">
            <summary>
            Pointer event kind used by <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.SendPointerInput(Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind,Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo)"/> to convey the kind of pointer event being sent to WebView.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind.Activate">
            <summary>
            Corresponds to <c>WM_POINTERACTIVATE</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind.Down">
            <summary>
            Corresponds to <c>WM_POINTERDOWN</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind.Enter">
            <summary>
            Corresponds to <c>WM_POINTERENTER</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind.Leave">
            <summary>
            Corresponds to <c>WM_POINTERLEAVE</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind.Up">
            <summary>
            Corresponds to <c>WM_POINTERUP</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind.Update">
            <summary>
            Corresponds to <c>WM_POINTERUPDATE</c>.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PermissionState">
            <summary>
            Specifies the response to a permission request.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PermissionState.Default">
            <summary>
            Specifies that the default browser behavior is used, which normally prompts users for decision.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PermissionState.Allow">
            <summary>
            Specifies that the permission request is granted.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PermissionState.Deny">
            <summary>
            Specifies that the permission request is denied.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PermissionKind">
            <summary>
            Indicates the kind of a permission request.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PermissionKind.UnknownPermission">
            <summary>
            Indicates an unknown permission.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PermissionKind.Microphone">
            <summary>
            Indicates permission to capture audio.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PermissionKind.Camera">
            <summary>
            Indicates permission to capture video.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PermissionKind.Geolocation">
            <summary>
            Indicates permission to access geolocation.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PermissionKind.Notifications">
            <summary>
            Indicates permission to send web notifications. This permission request is currently auto-rejected and no event is raised for it.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PermissionKind.OtherSensors">
            <summary>
            Indicates permission to access generic sensor. Generic Sensor covers ambient-light-sensor, accelerometer, gyroscope, and magnetometer.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PermissionKind.ClipboardRead">
            <summary>
            Indicates permission to read the system clipboard without a user gesture.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems">
            <summary>
            Specifies the PDF toolbar item types used for the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.HiddenPdfToolbarItems"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems.None">
            <summary>
            No item. By default the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.HiddenPdfToolbarItems"/> equal to this value.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems.Save">
            <summary>
            The save button on PDF toolbar.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems.Print">
            <summary>
            The print button on PDF toolbar.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems.SaveAs">
            <summary>
            The save as button on PDF toolbar.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems.ZoomIn">
            <summary>
            The zoom in button on PDF toolbar.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems.ZoomOut">
            <summary>
            The zoom out button on PDF toolbar.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems.Rotate">
            <summary>
            The rotate button on PDF toolbar.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems.FitPage">
            <summary>
            The fit to width button on PDF toolbar.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems.PageLayout">
            <summary>
            The page view button on PDF toolbar.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems.Bookmarks">
            <summary>
            The contents button on PDF toolbar.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems.PageSelector">
            <summary>
            The page number button on PDF toolbar.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems.Search">
            <summary>
            The search button on PDF toolbar.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusReason">
            <summary>
            Specifies the reason for moving focus.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusReason.Programmatic">
            <summary>
            Specifies that the code is setting focus into WebView.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusReason.Next">
            <summary>
            Specifies that the focus is moved due to Tab traversal forward.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusReason.Previous">
            <summary>
            Specifies that the focus is moved due to Tab traversal backward.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys">
            <summary>
            Mouse event virtual keys associated with a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind"/> for <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.SendMouseInput(Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind,Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys,System.UInt32,System.Drawing.Point)"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys.None">
            <summary>
            No additional keys pressed.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys.LeftButton">
            <summary>
            Left mouse button is down, <c>MK_LBUTTON</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys.RightButton">
            <summary>
            Right mouse button is down, <c>MK_RBUTTON</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys.Shift">
            <summary>
            Shift key is down, <c>MK_SHIFT</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys.Control">
            <summary>
            Ctrl key is down, <c>MK_CONTROL</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys.MiddleButton">
            <summary>
            Middle mouse button is down, <c>MK_MBUTTON</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys.XButton1">
            <summary>
            First X button is down, <c>MK_XBUTTON1</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys.XButton2">
            <summary>
            Second X button is down, <c>MK_XBUTTON2</c>.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind">
            <summary>
            Mouse event kind used by <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.SendMouseInput(Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind,Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys,System.UInt32,System.Drawing.Point)"/> to convey the kind of mouse event being sent to WebView.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.HorizontalWheel">
            <summary>
            Mouse horizontal wheel scroll event, <c>WM_MOUSEHWHEEL</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.LeftButtonDoubleClick">
            <summary>
            Left button double click mouse event, <c>WM_LBUTTONDBLCLK</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.LeftButtonDown">
            <summary>
            Left button down mouse event, <c>WM_LBUTTONDOWN</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.LeftButtonUp">
            <summary>
            Left button up mouse event, <c>WM_LBUTTONUP</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.Leave">
            <summary>
            Mouse leave event, <c>WM_MOUSELEAVE</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.MiddleButtonDoubleClick">
            <summary>
            Middle button double click mouse event, <c>WM_MBUTTONDBLCLK</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.MiddleButtonDown">
            <summary>
            Middle button down mouse event, <c>WM_MBUTTONDOWN</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.MiddleButtonUp">
            <summary>
            Middle button up mouse event, <c>WM_MBUTTONUP</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.Move">
            <summary>
            Mouse move event, <c>WM_MOUSEMOVE</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.RightButtonDoubleClick">
            <summary>
            Right button double click mouse event, <c>WM_RBUTTONDBLCLK</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.RightButtonDown">
            <summary>
            Right button down mouse event, <c>WM_RBUTTONDOWN</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.RightButtonUp">
            <summary>
            Right button up mouse event, <c>WM_RBUTTONUP</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.Wheel">
            <summary>
            Mouse wheel scroll event, <c>WM_MOUSEWHEEL</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.XButtonDoubleClick">
            <summary>
            First or second X button double click mouse event, <c>WM_XBUTTONDBLCLK</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.XButtonDown">
            <summary>
            First or second X button down mouse event, <c>WM_XBUTTONDOWN</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.XButtonUp">
            <summary>
            First or second X button up mouse event, <c>WM_XBUTTONUP</c>.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2KeyEventKind">
            <summary>
            Specifies the key event kind that raises an <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.AcceleratorKeyPressed"/> event.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2KeyEventKind.KeyDown">
            <summary>
            Specifies that the key event kind corresponds to window message <c>WM_KEYDOWN</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2KeyEventKind.KeyUp">
            <summary>
            Specifies that the key event kind corresponds to window message <c>WM_KEYUP</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2KeyEventKind.SystemKeyDown">
            <summary>
            Specifies that the key event kind corresponds to window message <c>WM_SYSKEYDOWN</c>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2KeyEventKind.SystemKeyUp">
            <summary>
            Specifies that the key event kind corresponds to window message <c>WM_SYSKEYUP</c>.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2HostResourceAccessKind">
            <summary>
            Kind of cross origin resource access allowed for host resources during download.
            </summary>
            <remarks>
            Note that other normal access checks like same origin DOM access check and [Content Security Policy](https://developer.mozilla.org/docs/Web/HTTP/CSP) still apply.
            The following table illustrates the host resource cross origin access according to access context and <c>CoreWebView2HostResourceAccessKind</c>.
            
            <list type="table">
            <listheader>
            <description>Cross Origin Access Context</description>
            <description>Deny</description>
            <description>Allow</description>
            <description>DenyCors</description>
            </listheader>
            <item>
            <description>From DOM like src of img, script or iframe element</description>
            <description>Deny</description>
            <description>Allow</description>
            <description>Allow</description>
            </item>
            <item>
            <description>From Script like Fetch or XMLHttpRequest</description>
            <description>Deny</description>
            <description>Allow</description>
            <description>Deny</description>
            </item>
            </list>
            </remarks>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2HostResourceAccessKind.Deny">
            <summary>
            All cross origin resource access is denied, including normal sub resource access as src of a script or image element.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2HostResourceAccessKind.Allow">
            <summary>
            All cross origin resource access is allowed, including accesses that are subject to Cross-Origin Resource Sharing(CORS) check. The behavior is similar to a web site sends back http header Access-Control-Allow-Origin: *.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2HostResourceAccessKind.DenyCors">
            <summary>
            Cross origin resource access is allowed for normal sub resource access like as src of a script or image element, while any access that subjects to CORS check will be denied. See [Cross-Origin Resource Sharing](https://developer.mozilla.org/docs/Web/HTTP/CORS) for more information.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2FaviconImageFormat">
            <summary>
            The requested format to get the Favicon from <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.GetFaviconAsync(Microsoft.Web.WebView2.Core.CoreWebView2FaviconImageFormat)"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2FaviconImageFormat.Png">
            <summary>
            Request the Favicon to be retrieved a Png Format.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2FaviconImageFormat.Jpeg">
            <summary>
            Request the Favicon to be retrieved a Jpeg Format.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2DownloadState">
            <summary>
            The state of the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadState.InProgress">
            <summary>
            The download is in progress.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadState.Interrupted">
            <summary>
            The connection with the file host was broken. The reason why a download was interrupted can accessed from <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.InterruptReason"/>. See <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason"/> for descriptions of the different kinds of interrupt reasons. Host can check whether an interrupted download can be resumed with <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation.CanResume"/>. Once resumed, the download state is in progress.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadState.Completed">
            <summary>
            The download completed successfully.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason">
            <summary>
            The reason why the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation"/> was interrupted.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.None">
            <summary>
            No interrupt reason.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.FileFailed">
            <summary>
            Generic file error.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.FileAccessDenied">
            <summary>
            Access denied due to security restrictions.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.FileNoSpace">
            <summary>
            Disk full. User should free some space or choose a different location to store the file.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.FileNameTooLong">
            <summary>
            Result file path with file name is too long.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.FileTooLarge">
            <summary>
            File is too large for file system.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.FileMalicious">
            <summary>
            Microsoft Defender Smartscreen detected a virus in the file.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.FileTransientError">
            <summary>
            File was in use, too many files opened, or out of memory.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.FileBlockedByPolicy">
            <summary>
            File blocked by local policy.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.FileSecurityCheckFailed">
            <summary>
            Security check failed unexpectedly. Microsoft Defender SmartScreen could not scan this file.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.FileTooShort">
            <summary>
            Seeking past the end of a file in opening a file, as part of resuming an interrupted download. The file did not exist or was not as large as expected. Partially downloaded file was truncated or deleted, and download will be restarted automatically.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.FileHashMismatch">
            <summary>
            Partial file did not match the expected hash and was deleted. Download will be restarted automatically.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.NetworkFailed">
            <summary>
            Generic network error. User can retry the download manually.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.NetworkTimeout">
            <summary>
            Network operation timed out.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.NetworkDisconnected">
            <summary>
            Network connection lost. User can retry the download manually.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.NetworkServerDown">
            <summary>
            Server has gone down. User can retry the download manually.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.NetworkInvalidRequest">
            <summary>
            Network request invalid because original or redirected URI is invalid, has an unsupported scheme, or is disallowed by network policy.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.ServerFailed">
            <summary>
            Generic server error. User can retry the download manually.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.ServerNoRange">
            <summary>
            Server does not support range requests.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.ServerBadContent">
            <summary>
            Server does not have the requested data.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.ServerUnauthorized">
            <summary>
            Server did not authorize access to resource.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.ServerCertificateProblem">
            <summary>
            Server certificate problem.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.ServerForbidden">
            <summary>
            Server access forbidden.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.ServerUnexpectedResponse">
            <summary>
            Unexpected server response. Responding server may not be intended server. User can retry the download manually.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.ServerContentLengthMismatch">
            <summary>
            Server sent fewer bytes than the Content-Length header. Content-Length header may be invalid or connection may have closed. Download is treated as complete unless there are [strong validators](https://tools.ietf.org/html/rfc7232#section-2) present to interrupt the download.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.ServerCrossOriginRedirect">
            <summary>
            Unexpected cross-origin redirect.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.UserCanceled">
            <summary>
            User canceled the download.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.UserShutdown">
            <summary>
            User shut down the WebView. Resuming downloads that were interrupted during shutdown is not yet supported.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.UserPaused">
            <summary>
            User paused the download.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.DownloadProcessCrashed">
            <summary>
            WebView crashed.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2DefaultDownloadDialogCornerAlignment">
            <summary>
            The default download dialog can be aligned to any of the WebView corners by setting the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.DefaultDownloadDialogCornerAlignment"/> property. The default position is top-right corner.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DefaultDownloadDialogCornerAlignment.TopLeft">
            <summary>
            The top-left corner of the WebView.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DefaultDownloadDialogCornerAlignment.TopRight">
            <summary>
            The top-right corner of the WebView.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DefaultDownloadDialogCornerAlignment.BottomLeft">
            <summary>
            The bottom-left corner of the WebView.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2DefaultDownloadDialogCornerAlignment.BottomRight">
            <summary>
            The bottom-right corner of the WebView.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2CookieSameSiteKind">
            <summary>
            Kind of cookie SameSite status used in the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Cookie"/> class.
            </summary>
            <remarks>
            These fields match those as specified in https://developer.mozilla.org/docs/Web/HTTP/Cookies#.
            Learn more about SameSite cookies here: https://tools.ietf.org/html/draft-west-first-party-cookies-07
            </remarks>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2CookieSameSiteKind.None">
            <summary>
            None SameSite type. No restrictions on cross-site requests.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2CookieSameSiteKind.Lax">
            <summary>
            Lax SameSite type. The cookie will be sent with "same-site" requests, and with "cross-site" top level navigation.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2CookieSameSiteKind.Strict">
            <summary>
            Strict SameSite type. The cookie will only be sent along with "same-site" requests.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTargetKind">
            <summary>
            Indicates the kind of context for which the context menu was created for the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.Kind"/> property. This enum will always represent the active element that caused the context menu request. If there is a selection with multiple images, audio and text, for example, the element that the end user right clicks on within this selection will be the option represented by this enum.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTargetKind.Page">
            <summary>
            Indicates that the context menu was created for the page without any additional content.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTargetKind.Image">
            <summary>
            Indicates that the context menu was created for an image element.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTargetKind.SelectedText">
            <summary>
            Indicates that the context menu was created for selected text.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTargetKind.Audio">
            <summary>
            Indicates that the context menu was created for an audio element.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTargetKind.Video">
            <summary>
            Indicates that the context menu was created for a video element.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItemKind">
            <summary>
            Specifies the menu item kind for the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.Kind"/> property.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItemKind.Command">
            <summary>
            Specifies a command menu item kind.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItemKind.CheckBox">
            <summary>
            Specifies a check box menu item kind. <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/> objects of this kind will need the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.IsChecked"/> property to determine current state of the check box.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItemKind.Radio">
            <summary>
            Specifies a radio button menu item kind. <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/> objects of this kind will need the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.IsChecked"/> property to determine current state of the radio button.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItemKind.Separator">
            <summary>
            Specifies a separator menu item kind. <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/> objects of this kind are used to signal a visual separator with no functionality.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItemKind.Submenu">
            <summary>
            Specifies a submenu menu item kind. <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/> objects of this kind will contain a collection of its children <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/> objects.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateKind">
            <summary>
            The kind of the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateKind.SmartCard">
            <summary>
            Specifies smart card certificate.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateKind.Pin">
            <summary>
            Specifies PIN certificate.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateKind.Other">
            <summary>
            Specifies other certificate.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2CapturePreviewImageFormat">
            <summary>
            Specifies the image format for the <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.CapturePreviewAsync(Microsoft.Web.WebView2.Core.CoreWebView2CapturePreviewImageFormat,System.IO.Stream)"/> method.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2CapturePreviewImageFormat.Png">
            <summary>
            Indicates that the PNG image format is used.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2CapturePreviewImageFormat.Jpeg">
            <summary>
            Indicates that the JPEG image format is used.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds">
            <summary>
            Indicates the kind of browsing data to clear. Or operations can be applied to create a mask representing multiple CoreWebView2BrowsingDataKinds. The resulting mask may be passed to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Profile.ClearBrowsingDataAsync(Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds)"/> or <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Profile.ClearBrowsingDataAsync(Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds,System.DateTime,System.DateTime)"/> to clear the corresponding data.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.FileSystems">
            <summary>
            Specifies file systems data.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.IndexedDb">
            <summary>
            Specifies data stored by the IndexedDB DOM feature.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.LocalStorage">
            <summary>
            Specifies data stored by the localStorage DOM API.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.WebSql">
            <summary>
            Specifies data stored by the Web SQL database DOM API.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.CacheStorage">
            <summary>
            Specifies data stored by the CacheStorage DOM API.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.AllDomStorage">
            <summary>
            Specifies DOM storage data, now and future. This browsing data kind is inclusive of CoreWebView2BrowsingDataKinds.FileSystems, CoreWebView2BrowsingDataKinds.IndexedDb, CoreWebView2BrowsingDataKinds.WebSql, CoreWebView2BrowsingDataKinds.CacheStorage. New DOM storage data types may be added to this data kind in the future.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.Cookies">
            <summary>
            Specifies HTTP cookies data.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.AllSite">
            <summary>
            Specifies all site data, now and future. This browsing data kind is inclusive of CoreWebView2BrowsingDataKinds.AllDomStorage and CoreWebView2BrowsingDataKinds.Cookies. New site data types may be added to this data kind in the future.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.DiskCache">
            <summary>
            Specifies disk cache.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.DownloadHistory">
            <summary>
            Specifies download history data.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.GeneralAutofill">
            <summary>
            Specifies general autofill form data. This excludes password information and includes information like: names, street and email addresses, phone numbers, and arbitrary input. This also includes payment data.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.PasswordAutosave">
            <summary>
            Specifies password autosave data.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.BrowsingHistory">
            <summary>
            Specifies browsing history data.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.Settings">
            <summary>
            Specifies settings data.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.AllProfile">
            <summary>
            Specifies profile data that should be wiped to make it look like a new profile. This does not delete account-scoped data like passwords but will remove access to account-scoped data by signing the user out. Specifies all profile data, now and future. New profile data types may be added to this data kind in the future. This browsing data kind is inclusive of <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.AllSite"/>, <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.DiskCache"/>, <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.DownloadHistory"/>, <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.GeneralAutofill"/>, <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.PasswordAutosave"/>, <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.BrowsingHistory"/>, <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds.Settings"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2BrowserProcessExitKind">
            <summary>
            Specifies the browser process exit kind used in <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2BrowserProcessExitedEventArgs"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowserProcessExitKind.Normal">
            <summary>
            Indicates that the browser process ended normally.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BrowserProcessExitKind.Failed">
            <summary>
            Indicates that the browser process ended unexpectedly. A <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ProcessFailed"/> event will also be raised to listening WebViews from the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> associated to the failed process.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2BoundsMode">
            <summary>
            Mode for how the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.Bounds"/> property is interpreted in relation to the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.RasterizationScale"/> property.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BoundsMode.UseRawPixels">
            <summary>
            <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.Bounds"/> property represents raw pixels. Physical size of WebView is not impacted by <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.RasterizationScale"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2BoundsMode.UseRasterizationScale">
            <summary>
            <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.Bounds"/> property represents logical pixels and the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.RasterizationScale"/> property is used to get the physical size of the WebView.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PhysicalKeyStatus">
            <summary>
            Contains the information packed into the LPARAM sent to a Win32 key event.
            </summary>
            <remarks>
            For more information about <c>WM_KEYDOWN</c>, navigate to [WM_KEYDOWN message](/windows/win32/inputdev/wm-keydown).
            </remarks>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PhysicalKeyStatus.RepeatCount">
            <summary>
            Specifies the repeat count for the current message.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PhysicalKeyStatus.ScanCode">
            <summary>
            Specifies the scan code.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PhysicalKeyStatus.IsExtendedKey">
            <summary>
            Indicates that the key is an extended key.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PhysicalKeyStatus.IsMenuKeyDown">
            <summary>
            Indicates that a menu key is held down (context code).
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PhysicalKeyStatus.WasKeyDown">
            <summary>
            Indicates that the key was held down.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2PhysicalKeyStatus.IsKeyReleased">
            <summary>
            Indicates that the key was released.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2Color">
            <summary>
            A value representing RGBA color (Red, Green, Blue, Alpha) for WebView2.
            </summary>
            <remarks>
            Each component takes a value from 0 to 255, with 0 being no intensity and 255 being the highest intensity.
            </remarks>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2Color.A">
            <summary>
            Specifies the intensity of the Alpha ie. opacity value. 0 is transparent, 255 is opaque.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2Color.R">
            <summary>
            Specifies the intensity of the Red color.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2Color.G">
            <summary>
            Specifies the intensity of the Green color.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.CoreWebView2Color.B">
            <summary>
            Specifies the intensity of the Blue color.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures">
            <summary>
            The window features for a WebView popup window.
            </summary>
            <remarks>
            The fields match the <c>windowFeatures</c> passed to <c>window.open()</c> as specified in [Window features](https://developer.mozilla.org/docs/Web/API/Window/open#Window_features) on MDN. There is no requirement for you to respect the values. If your app does not have corresponding UI features (for example, no toolbar) or if all instance of WebView are opened in tabs and do not have distinct size or positions, then your app does not respect the values. You may want to respect values, but perhaps only some apply to the UI of you app. Accordingly, you may respect all, some, or none of the properties as appropriate for your app. For all numeric properties, if the value that is passed to <c>window.open()</c> is outside the range of an uint, the resulting value is uint.MaxValue. If you are not able to parse the value an integer, it is considered 0. If the value is a floating point value, it is rounded down to an integer.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures.HasPosition">
            <summary>
            Indicates whether the left and top values are specified.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures.HasSize">
            <summary>
            Indicates whether the height and width values are specified.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures.Left">
            <summary>
            Gets the left position of the window. Ignored if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures.HasPosition"/> is <c>false</c>.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures.Top">
            <summary>
            Gets the top position of the window. Ignored if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures.HasPosition"/> is <c>false</c>.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures.Height">
            <summary>
            Gets the height of the window. Ignored if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures.HasSize"/> is <c>false</c>.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures.Width">
            <summary>
            Gets the width of the window. Ignored if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures.HasSize"/> is <c>false</c>.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures.ShouldDisplayMenuBar">
            <summary>
            Indicates that the menu bar is displayed.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures.ShouldDisplayStatus">
            <summary>
            Indicates that the status bar is displayed.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures.ShouldDisplayToolbar">
            <summary>
            Indicates that the browser toolbar is displayed.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures.ShouldDisplayScrollBars">
            <summary>
            Indicates that the scroll bars are displayed.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponseView">
            <summary>
            View of the HTTP representation for a web resource response.
            </summary>
            <remarks>
            The properties of this object are not mutable. This response view is used with the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceResponseReceived"/> event.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponseView.Headers">
            <summary>
            Gets the HTTP response headers as received.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponseView.StatusCode">
            <summary>
            Gets the HTTP response status code.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponseView.ReasonPhrase">
            <summary>
            Gets the HTTP response reason phrase.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponseView.GetContentAsync">
            <summary>
            Gets the response content stream asynchronously.
            </summary>
            <exception cref="T:System.Runtime.InteropServices.COMException">The content failed to load.</exception>
            <remarks>
            A <c>null</c> stream means no content was found. Note content (if any) for redirect responses is ignored.
            This method returns <c>null</c> if content size is more tha 123MB or for navigations that become downloads or if response is downloadable content type (e.g., application/octet-stream). See <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.DownloadStarting"/> event to handle the response.
            If this method is being called again before a first call has completed, it will complete at the same time all prior calls do.
            If this method is being called after a first call has completed, it will return immediately (asynchronously).
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="WebResourceResponseReceived":::
            </example>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponseReceivedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceResponseReceived"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponseReceivedEventArgs.Request">
            <summary>
            Gets the request object for the web resource, as committed.
            </summary>
            <remarks>
            This includes headers added by the network stack that were not be included during the associated <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event, such as Authentication headers. Modifications to this object have no effect on how the request is processed as it has already been sent.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponseReceivedEventArgs.Response">
            <summary>
            Gets view of the response object received for the web resource.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponse">
            <summary>
            An HTTP response used with the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponse.Content">
            <summary>
            Gets HTTP response content as stream.
            </summary>
            <remarks>
            Stream must have all the content data available by the time the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event deferral of this response is completed. Stream should be agile or be created from a background thread to prevent performance impact to the UI thread. <c>null</c> means no content data.
            </remarks>
            <seealso cref="T:System.IO.Stream"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponse.Headers">
            <summary>
            Gets the overridden HTTP response headers.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponse.StatusCode">
            <summary>
            Gets or sets the HTTP response status code.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponse.ReasonPhrase">
            <summary>
            Gets or sets the HTTP response reason phrase.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequestedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequestedEventArgs.Request">
            <summary>
            Gets the web resource request.
            </summary>
            <remarks>
            The request object may be missing some headers that are added by network stack at a later time.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequestedEventArgs.Response">
            <summary>
            Gets or sets the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponse"/> object.
            </summary>
            <remarks>
            If this object is set, the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event will be completed with this Response.
            An empty <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponse"/> object can be created with <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateWebResourceResponse(System.IO.Stream,System.Int32,System.String,System.String)"/> and then modified to construct the Response.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequestedEventArgs.ResourceContext">
            <summary>
            Gets the web resource request context.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequestedEventArgs.GetDeferral">
            <summary>
            Gets a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Deferral"/> object and put the event into a deferred state.
            </summary>
            <remarks>
            Use this to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Deferral.Complete"/> the event at a later time.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequest">
            <summary>
            An HTTP request used with the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequest.Uri">
            <summary>
            Gets or sets the request URI.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequest.Method">
            <summary>
            Gets or sets the HTTP request method.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequest.Content">
            <summary>
            Gets or sets the HTTP request message body as stream.
            </summary>
            <remarks>
            POST data should be here. If a stream is set, which overrides the message body, the stream must have all the content data available by the time the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebResourceRequested"/> event deferral of this request is completed. Stream should be agile or be created from a background STA to prevent performance impact to the UI thread. <c>null</c> means no content data.
            </remarks>
            <seealso cref="T:System.IO.Stream"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequest.Headers">
            <summary>
            Gets the mutable HTTP request headers.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2WebMessageReceivedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebMessageReceived"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebMessageReceivedEventArgs.Source">
            <summary>
            Gets the URI of the document that sent this web message.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2WebMessageReceivedEventArgs.WebMessageAsJson">
            <summary>
            Gets the message posted from the WebView content to the host converted to a JSON string.
            </summary>
            <remarks>
            Run this operation to communicate using JavaScript objects.
            </remarks>
            <example>
            For example, the following <c>postMessage</c> runs result in the following WebMessageAsJson values:
            
            <code>
            postMessage({'a': 'b'})      "{\"a\": \"b\"}"
            postMessage(1.2)             "1.2"
            postMessage('example')       "\"example\""
            </code>
            </example>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2WebMessageReceivedEventArgs.TryGetWebMessageAsString">
            <summary>
            Gets the message posted from the WebView content to the host as a string.
            </summary>
            <returns>The message posted from the WebView content to the host.</returns>
            <exception cref="T:System.ArgumentException">The message posted is some other kind of JavaScript type.</exception>
            <remarks>
            Run this operation to communicate using simple strings.
            </remarks>
            <example>
            For example the following <c>postMessage</c> runs result in the following values returned by TryWebMessageAsString:
            
            <code>
            postMessage({'a': 'b'})      ArgumentException
            postMessage(1.2)             ArgumentException
            postMessage('example')       "example"
            </code>
            </example>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2SourceChangedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.SourceChanged"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2SourceChangedEventArgs.IsNewDocument">
            <summary>
            <c>true</c> if the page being navigated to is a new document.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2Settings">
            <summary>
            Defines properties that enable, disable, or modify WebView features.
            </summary>
            <remarks>
            Changes to <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsGeneralAutofillEnabled"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsPasswordAutosaveEnabled"/> apply immediately, while other setting changes made after <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event do not apply until the next top-level navigation.
            </remarks>
            <summary>
            Defines properties that enable, disable, or modify WebView features.
            </summary>
            <remarks>
            Changes to <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsGeneralAutofillEnabled"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsPasswordAutosaveEnabled"/> apply immediately, while other setting changes made after <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event do not apply until the next top-level navigation.
            </remarks>
            <summary>
            Defines properties that enable, disable, or modify WebView features.
            </summary>
            <remarks>
            Changes to <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsGeneralAutofillEnabled"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsPasswordAutosaveEnabled"/> apply immediately, while other setting changes made after <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event do not apply until the next top-level navigation.
            </remarks>
            <summary>
            Defines properties that enable, disable, or modify WebView features.
            </summary>
            <remarks>
            Changes to <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsGeneralAutofillEnabled"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsPasswordAutosaveEnabled"/> apply immediately, while other setting changes made after <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event do not apply until the next top-level navigation.
            </remarks>
            <summary>
            Defines properties that enable, disable, or modify WebView features.
            </summary>
            <remarks>
            Changes to <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsGeneralAutofillEnabled"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsPasswordAutosaveEnabled"/> apply immediately, while other setting changes made after <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event do not apply until the next top-level navigation.
            </remarks>
            <summary>
            Defines properties that enable, disable, or modify WebView features.
            </summary>
            <remarks>
            Changes to <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsGeneralAutofillEnabled"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsPasswordAutosaveEnabled"/> apply immediately, while other setting changes made after <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event do not apply until the next top-level navigation.
            </remarks>
            <summary>
            Defines properties that enable, disable, or modify WebView features.
            </summary>
            <remarks>
            Changes to <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsGeneralAutofillEnabled"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsPasswordAutosaveEnabled"/> apply immediately, while other setting changes made after <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event do not apply until the next top-level navigation.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsScriptEnabled">
            <summary>
            Determines whether running JavaScript is enabled in all future navigations in the WebView.
            </summary>
            <remarks>
            This only affects scripts in the document. Scripts injected with <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.ExecuteScriptAsync(System.String)"/> runs even if script is disabled. The default value is <c>true</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsWebMessageEnabled">
            <summary>
            Determines whether communication from the host to the top-level HTML document of the WebView is allowed.
            </summary>
            <remarks>
            This is used when loading a new HTML document. If set to <c>true</c>, communication from the host to the top-level HTML document of the WebView is allowed using <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.PostWebMessageAsJson(System.String)"/>, <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.PostWebMessageAsString(System.String)"/>, and message event of <c>window.chrome.webview</c>. Communication from the top-level HTML document of the WebView to the host is allowed using <c>window.chrome.webview.postMessage</c> function and the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebMessageReceived"/> event. If set to <c>false</c>, then communication is disallowed. <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.PostWebMessageAsJson(System.String)"/> and <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.PostWebMessageAsString(System.String)"/> fail and <c>window.chrome.webview.postMessage</c> fails by throwing an instance of an Error object. The default value is <c>true</c>.
            </remarks>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.PostWebMessageAsJson(System.String)"/>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.PostWebMessageAsString(System.String)"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebMessageReceived"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.AreDefaultScriptDialogsEnabled">
            <summary>
            Determines whether WebView renders the default JavaScript dialog box.
            </summary>
            <remarks>
            This is used when loading a new HTML document. If set to <c>false</c>, WebView does not render the default JavaScript dialog box (specifically those displayed by the JavaScript alert, confirm, prompt functions and <c>beforeunload</c> event). Instead, WebView raises <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ScriptDialogOpening"/> event that contains all of the information for the dialog and allow the host app to show a custom UI. The default value is <c>true</c>.
            </remarks>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ScriptDialogOpening"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsStatusBarEnabled">
            <summary>
            Determines whether the status bar is displayed.
            </summary>
            <remarks>
            The status bar is usually displayed in the lower left of the WebView and shows things such as the URI of a link when the user hovers over it and other information. The default value is <c>true</c>. The status bar UI can be altered by web content and should not be considered secure.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.AreDevToolsEnabled">
            <summary>
            Determines whether the user is able to use the context menu or keyboard shortcuts to open the DevTools window.
            </summary>
            <remarks>
            The default value is <c>true</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.AreDefaultContextMenusEnabled">
            <summary>
            Determines whether the default context menus are shown to the user in WebView.
            </summary>
            <remarks>
            The default value is <c>true</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.AreHostObjectsAllowed">
            <summary>
            Determines whether host objects are accessible from the page in WebView.
            </summary>
            <remarks>
            The default value is <c>true</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsZoomControlEnabled">
            <summary>
            Determines whether the user is able to impact the zoom of the WebView.
            </summary>
            <remarks>
            When disabled, the user is not able to zoom using Ctrl++, Ctr+-, or Ctrl+mouse wheel, but the zoom is set using <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor"/> property. The default value is <c>true</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsBuiltInErrorPageEnabled">
            <summary>
            Determines whether to disable built in error page for navigation failure and render process failure.
            </summary>
            <remarks>
            When disabled, blank page is displayed when related error happens. The default value is <c>true</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.UserAgent">
            <summary>
            Determines WebView2's User Agent.
            </summary>
            <remarks>
            The default value is the default User Agent of the Edge browser. This property may be overridden if the User-Agent header is set in a request. If the parameter is empty the User Agent will not be updated and the current User Agent will remain.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="SetUserAgent":::
            </example>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.AreBrowserAcceleratorKeysEnabled">
            <summary>
            Determines whether browser-specific accelerator keys are enabled.
            </summary>
            <remarks>
            When this setting is set to false, it disables all accelerator keys that access
            features specific to a web browser, including but not limited to:
            
            <list type="bullet">
            <item>
            <description>Ctrl+F and F3 for Find on Page</description>
            </item>
            <item>
            <description>Ctrl+P for Print</description>
            </item>
            <item>
            <description>Ctrl+R and F5 for Reload</description>
            </item>
            <item>
            <description>Ctrl+Plus and Ctrl+Minus for zooming</description>
            </item>
            <item>
            <description>Ctrl+Shift-C and F12 for DevTools</description>
            </item>
            <item>
            <description>Special keys for browser functions, such as Back, Forward, and Search</description>
            </item>
            </list>
            
            It does not disable accelerator keys related to movement and text editing, such
            as:
            
            <list type="bullet">
            <item>
            <description>Home, End, Page Up, and Page Down</description>
            </item>
            <item>
            <description>Ctrl+X, Ctrl+C, Ctrl+V</description>
            </item>
            <item>
            <description>Ctrl+A for Select All</description>
            </item>
            <item>
            <description>Ctrl+Z for Undo</description>
            </item>
            </list>
            
            Those accelerator keys will always be enabled unless they are handled in the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.AcceleratorKeyPressed"/> event.
            
            This setting has no effect on the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.AcceleratorKeyPressed"/> event. The event
            will be fired for all accelerator keys, whether they are enabled or not.
            
            The default value of <c>AreBrowserAcceleratorKeysEnabled</c> is true.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="AllowWebViewShortcutKeys":::
            </example>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsPasswordAutosaveEnabled">
            <summary>
            Determines whether password information will be autosaved.
            </summary>
            <remarks>
            When disabled, no new password data is saved and no Save/Update Password prompts are displayed. However, if there was password data already saved before disabling this setting, then that password information is auto-populated, suggestions are shown and clicking on one will populate the fields.
            When enabled, password information is auto-populated, suggestions are shown and clicking on one will populate the fields, new data is saved, and a Save/Update Password prompt is displayed. The default value is <c>false</c>. It will apply immediately after setting.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="PasswordAutosaveEnabled":::
            </example>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsGeneralAutofillEnabled">
            <summary>
            Determines whether general form information will be saved and autofilled.
            </summary>
            <remarks>
            General autofill information includes information like names, street and email addresses, phone numbers, and arbitrary input. This excludes password information. When disabled, no suggestions appear, and no new information is saved.
            When enabled, information is saved, suggestions appear, and clicking on one will populate the form fields. The default value is <c>true</c>. It will apply immediately after setting.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="GeneralAutofillEnabled":::
            </example>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.AcceleratorKeyPressed"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsPinchZoomEnabled">
            <summary>
            Determines the ability of the end users to use pinching motions on touch input enabled devices to scale the web content in the WebView2.
            </summary>
            <remarks>
            When disabled, the end users cannot use pinching motions on touch input enabled devices to scale the web content in the WebView2. The default value is <c>true</c>.
            Pinch-zoom, referred to as "Page Scale" zoom, is performed as a post-rendering step, it changes the page scale factor property and scales the surface the web page is rendered onto when user performs a pinch zooming action. It does not change the layout but rather changes the viewport and clips the web content, the content outside of the viewport isn't visible onscreen and users can't reach this content using mouse. This API only affects the Page Scale zoom and has no effect on the existing browser zoom properties (<see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsZoomControlEnabled"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor"/>) or other end user mechanisms for zooming.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="TogglePinchZoomEnabled":::
            </example>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.IsSwipeNavigationEnabled">
            <summary>
            Determines whether the end user to use swiping gesture on touch input enabled devices to navigate in WebView2.
            </summary>
            <remarks>
            Swiping gesture navigation on touch screen includes:
            <list type="bullet">
            <item><description>
            Swipe left/right (swipe horizontally) to navigate to previous/next page in navigation history.
            </description></item>
            <item><description>
            Pull to refresh (swipe vertically) the current page. (This feature is currently disabled by default in the browser, to enable in WebView2, set <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions.AdditionalBrowserArguments"/> property with <c>--pull-to-refresh</c> switch).
            </description></item>
            </list>
            It defaults to <c>true</c>. When set to <c>false</c>, the end user cannot swipe to navigate or pull to refresh. This API only affects the overscrolling navigation functionality and has no effect on the scrolling interaction used to explore the web content shown in WebView2.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="ToggleSwipeNavigationEnabled":::
            </example>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2Settings.HiddenPdfToolbarItems">
            <summary>
            Used to customize the PDF toolbar items.
            </summary>
            <remarks>
            By default, it is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems.None"/> and so it displays all of the items.
            Changes to this property apply to all CoreWebView2s in the same environment and using the same profile.
            Changes to this setting apply only after the next navigation.
            </remarks>
            <example>
            :::code language="csharp" source="../code/sample/SampleApps/WebView2WpfBrowser/MainWindow.xaml.cs" id="ToggleHiddenPdfToolbarItems":::
            </example>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorDetectedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ServerCertificateErrorDetected"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorDetectedEventArgs.ErrorStatus">
            <summary>
            The TLS error code for the invalid certificate.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorDetectedEventArgs.RequestUri">
            <summary>
            URI associated with the request for the invalid certificate.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorDetectedEventArgs.ServerCertificate">
            <summary>
            Returns the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Certificate"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorDetectedEventArgs.Action">
            <summary>
            The action of the server certificate error detection.
            </summary>
            <remarks>
            The default value is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorAction.Default"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorDetectedEventArgs.GetDeferral">
            <summary>
            Gets a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Deferral"/> object.
            </summary>
            <remarks>
            Use this to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Deferral.Complete"/> the event at a later time.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ScriptDialogOpening"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs.Uri">
            <summary>
            Gets the URI of the page that requested the dialog box.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs.Kind">
            <summary>
            Gets the kind of JavaScript dialog box.
            </summary>
            <seealso cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogKind"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs.Message">
            <summary>
            Gets the message of the dialog box.
            </summary>
            <remarks>
            From JavaScript this is the first parameter passed to <c>alert</c>, <c>confirm</c>, and <c>prompt</c> and is empty for <c>beforeunload</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs.DefaultText">
            <summary>
            Gets the default value to use for the result of the <c>prompt</c> JavaScript function.
            </summary>
            <remarks>
            This is the second parameter passed to the JavaScript prompt dialog.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs.ResultText">
            <summary>
            Gets or sets the return value from the JavaScript <c>prompt</c> function if <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs.Accept"/> is run.
            </summary>
            <remarks>
            This value is ignored for <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs.Kind"/>s other than <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogKind.Prompt"/>. If <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs.Accept"/> is not run, this value is ignored and <c>false</c> is returned from <c>prompt</c>.
            </remarks>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs.Accept"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs.Accept">
            <summary>
            Responds with **OK** to <c>confirm</c>, <c>prompt</c>, and <c>beforeunload</c> dialogs. Not run this method to indicate cancel.
            </summary>
            <remarks>
            From JavaScript, this means that the <c>confirm</c> function and <c>beforeunload</c> event returns <c>true</c> if Accept is run. And for the <c>prompt</c> function it returns the value of <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs.ResultText"/> if Accept is run and otherwise returns <c>false</c>.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs.GetDeferral">
            <summary>
            Gets a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Deferral"/> object.
            </summary>
            <remarks>
            Use this to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Deferral.Complete"/> the event at a later time.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ProcessInfo">
            <summary>
            Provides a set of properties for a process list in the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ProcessInfo.ProcessId">
            <summary>
            Get the process id of the process.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ProcessInfo.Kind">
            <summary>
            Get the kind of the process.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ProcessFailed"/> event.
            </summary>
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ProcessFailed"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs.ProcessFailedKind">
            <summary>
            Gets the kind of process failure that has occurred.
            </summary>
            <remarks>
            <c>ProcessFailedKind</c> is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.RenderProcessExited"/> if the failed process is the main frame's renderer, even if there were subframes rendered by such process; all frames are gone when this happens.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs.Reason">
            <summary>
            Gets the reason for the process failure.
            </summary>
            <remarks>
            The reason is always <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedReason.Unexpected"/> when <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs.ProcessFailedKind"/> is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.BrowserProcessExited"/>, and <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedReason.Unresponsive"/> when <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs.ProcessFailedKind"/> is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.RenderProcessUnresponsive"/>. For other process failure kinds, the reason may be any of the reason values.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs.ExitCode">
            <summary>
            Gets the exit code of the failing process, for telemetry purposes.
            </summary>
            <remarks>
            The exit code is always <c>1</c> when <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs.ProcessFailedKind"/> is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.BrowserProcessExited"/>, and <c>STILL_ACTIVE</c>(<c>259</c>) when <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs.ProcessFailedKind"/> is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.RenderProcessUnresponsive"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs.ProcessDescription">
            <summary>
            Gets a description of the failing process, assigned by the WebView2 Runtime.
            </summary>
            <remarks>
            This is a technical English term appropriate for logging or development purposes, and not localized for the end user. It applies to utility processes (for example, "Audio Service", "Video Capture") and plugin processes (for example, "Flash"). The returned string is empty if the WebView2 Runtime did not assign a description to the process.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs.FrameInfosForFailedProcess">
            <summary>
            Gets the collection of <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2FrameInfo"/>s for frames in the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> that were being rendered by the failed process.
            </summary>
            <remarks>
            The content in these frames is replaced with an error page.
            This is only available when <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs.ProcessFailedKind"/> is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.FrameRenderProcessExited"/>; the returned collection is empty for all other process failure kinds, including the case in which the failed process was the renderer for the main frame and subframes within it, for which the failure kind is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind.RenderProcessExited"/>.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PrivateRemoteObjectProxy">
            
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateRemoteObjectProxy.GetId">
            
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PrivateHostObjectAsyncMethodContinuation">
            
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateHostObjectAsyncMethodContinuation.Invoke(System.Int32,System.Object)">
            
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PrivateEnvironmentTesting">
            
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateEnvironmentTesting.SetShouldCheckUninitializeForTesting(System.Int32)">
            
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PrivateContextMenuItem">
            
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateContextMenuItem.ReportSelected">
            
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateContextMenuItem.IsCustom">
            
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateContextMenuItem.SetHasReadIconStream(System.Int32)">
            
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PrivateContextMenuItem.HasReadIconStream">
            
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings">
            <summary>
            Settings used by the <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.PrintToPdfAsync(System.String,Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings)"/> method. Other programmatic printing is not currently supported.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.Orientation">
            <summary>
            The orientation can be portrait or landscape.
            </summary>
            <remarks>
            The default orientation is portrait. See <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2PrintOrientation"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.ScaleFactor">
            <summary>
            The scale factor is a value between 0.1 and 2.0.
            </summary>
            <remarks>
            The default is 1.0. If an invalid value is provided, the current value is not changed and an ArgumentException is thrown.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.PageWidth">
            <summary>
            The page width in inches.
            </summary>
            <remarks>
            The default width is 8.5 inches. If the provided page width is less than or equal to zero, the current value is not changed and an ArgumentException is thrown.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.PageHeight">
            <summary>
            The page height in inches.
            </summary>
            <remarks>
            The default height is 11 inches. If the provided page height is less than or equal to zero, the current value is not changed and an ArgumentException is thrown.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.MarginTop">
            <summary>
            The top margin in inches.
            </summary>
            <remarks>
            The default is 1 cm, or ~0.4 inches. A margin cannot be less than zero. If an invalid value is provided, the current value is not changed and an ArgumentException is thrown.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.MarginBottom">
            <summary>
            The bottom margin in inches.
            </summary>
            <remarks>
            The default is 1 cm, or ~0.4 inches. A margin cannot be less than zero. If an invalid value is provided, the current value is not changed and an ArgumentException is thrown.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.MarginLeft">
            <summary>
            The left margin in inches.
            </summary>
            <remarks>
            The default is 1 cm, or ~0.4 inches. A margin cannot be less than zero. If an invalid value is provided, the current value is not changed and an ArgumentException is thrown.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.MarginRight">
            <summary>
            The right margin in inches.
            </summary>
            <remarks>
            The default is 1 cm, or ~0.4 inches. A margin cannot be less than zero. If an invalid value is provided, the current value is not changed and an ArgumentException is thrown.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.ShouldPrintBackgrounds">
            <summary>
            <c>true</c> if background colors and images should be printed.
            </summary>
            <remarks>
            The default value is <c>false</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.ShouldPrintSelectionOnly">
            <summary>
            <c>true</c> if only the current end user's selection of HTML in the document should be printed.
            </summary>
            <remarks>
            The default value is <c>false</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.ShouldPrintHeaderAndFooter">
            <summary>
            <c>true</c> if header and footer should be printed.
            </summary>
            <remarks>
            The default value is <c>false</c>. The header consists of the date and time of printing, and the title of the page. The footer consists of the URI and page number. The height of the header and footer is 0.5 cm, or ~0.2 inches.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.HeaderTitle">
            <summary>
            The title in the header if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.ShouldPrintHeaderAndFooter"/> is <c>true</c>.
            </summary>
            <remarks>
            The default value is the title of the current document. If an empty string or null value is provided, no title is shown in the header.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.FooterUri">
            <summary>
            The URI in the footer if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings.ShouldPrintHeaderAndFooter"/> is <c>true</c>.
            </summary>
            <remarks>
            The default value is the current URI. If an empty string or null value is provided, no URI is shown in the footer.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo">
            <summary>
            This mostly represents a combined win32 <c>POINTER_INFO</c>, <c>POINTER_TOUCH_INFO</c>, and <c>POINTER_PEN_INFO</c> object.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.PointerKind">
            <summary>
            Gets or sets the PointerKind of the pointer event.
            </summary>
            <remarks>
            This corresponds to the pointerKind property of the <c>POINTER_INFO</c> struct. The values are defined by the <c>POINTER_INPUT_KIND</c> enum in the Windows SDK (_winuser.h_). Supports PT_PEN and PT_TOUCH.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.PointerId">
            <summary>
            Gets or sets the PointerId of the pointer event.
            </summary>
            <remarks>
            This corresponds to the pointerId property of the <c>POINTER_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.FrameId">
            <summary>
            Gets or sets the FrameID of the pointer event.
            </summary>
            <remarks>
            This corresponds to the frameId property of the <c>POINTER_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.PointerFlags">
            <summary>
            Gets or sets the PointerFlags of the pointer event.
            </summary>
            <remarks>
            This corresponds to the pointerFlags property of the <c>POINTER_INFO</c> struct. The values are defined by the <c>POINTER_FLAGS</c> constants in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.PointerDeviceRect">
            <summary>
            Gets or sets the PointerDeviceRect of the sourceDevice property of the <c>POINTER_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.DisplayRect">
            <summary>
            Gets or sets the DisplayRect of the sourceDevice property of the <c>POINTER_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.PixelLocation">
            <summary>
            Gets or sets the PixelLocation of the pointer event.
            </summary>
            <remarks>
            This corresponds to the ptPixelLocation property of the <c>POINTER_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.HimetricLocation">
            <summary>
            Gets or sets the HimetricLocation of the pointer event.
            </summary>
            <remarks>
            This corresponds to the ptHimetricLocation property of the <c>POINTER_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.PixelLocationRaw">
            <summary>
            Gets or sets the PixelLocationRaw of the pointer event.
            </summary>
            <remarks>
            This corresponds to the ptPixelLocationRaw property of the <c>POINTER_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.HimetricLocationRaw">
            <summary>
            Gets or sets the HimetricLocationRaw of the pointer event.
            </summary>
            <remarks>
            This corresponds to the ptHimetricLocationRaw property of the <c>POINTER_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.Time">
            <summary>
            Gets or sets the Time of the pointer event.
            </summary>
            <remarks>
            This corresponds to the dwTime property of the <c>POINTER_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.HistoryCount">
            <summary>
            Gets or sets the HistoryCount of the pointer event.
            </summary>
            <remarks>
            This corresponds to the historyCount property of the <c>POINTER_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.InputData">
            <summary>
            Gets or sets the InputData of the pointer event.
            </summary>
            <remarks>
            This corresponds to the InputData property of the <c>POINTER_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.KeyStates">
            <summary>
            Gets or sets the KeyStates of the pointer event.
            </summary>
            <remarks>
            This corresponds to the dwKeyStates property of the <c>POINTER_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.PerformanceCount">
            <summary>
            Gets or sets the PerformanceCount of the pointer event.
            </summary>
            <remarks>
            This corresponds to the PerformanceCount property of the <c>POINTER_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.ButtonChangeKind">
            <summary>
            Gets or sets the ButtonChangeKind of the pointer event.
            </summary>
            <remarks>
            This corresponds to the ButtonChangeKind property of the <c>POINTER_INFO</c> struct. The values are defined by the <c>POINTER_BUTTON_CHANGE_KIND</c> enum in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.PenFlags">
            <summary>
            Gets or sets the PenFlags of the pointer event.
            </summary>
            <remarks>
            This corresponds to the penFlags property of the <c>POINTER_PEN_INFO</c> struct. The values are defined by the PEN_FLAGS constants in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.PenMask">
            <summary>
            Gets or sets the PenMask of the pointer event.
            </summary>
            <remarks>
            This corresponds to the penMask property of the <c>POINTER_PEN_INFO</c> struct. The values are defined by the PEN_MASK constants in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.PenPressure">
            <summary>
            Gets or sets the PenPressure of the pointer event.
            </summary>
            <remarks>
            This corresponds to the pressure property of the <c>POINTER_PEN_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.PenRotation">
            <summary>
            Gets or sets the PenRotation of the pointer event.
            </summary>
            <remarks>
            This corresponds to the rotation property of the <c>POINTER_PEN_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.PenTiltX">
            <summary>
            Gets or sets the PenTiltX of the pointer event.
            </summary>
            <remarks>
            This corresponds to the tiltX property of the <c>POINTER_PEN_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.PenTiltY">
            <summary>
            Gets or sets the PenTiltY of the pointer event.
            </summary>
            <remarks>
            This corresponds to the tiltY property of the <c>POINTER_PEN_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.TouchFlags">
            <summary>
            Gets or sets the TouchFlags of the pointer event.
            </summary>
            <remarks>
            This corresponds to the touchFlags property of the <c>POINTER_TOUCH_INFO</c> struct. The values are defined by the TOUCH_FLAGS constants in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.TouchMask">
            <summary>
            Gets or sets the TouchMask of the pointer event.
            </summary>
            <remarks>
            This corresponds to the touchMask property of the <c>POINTER_TOUCH_INFO</c> struct. The values are defined by the TOUCH_MASK constants in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.TouchContact">
            <summary>
            Gets or sets the TouchContact of the pointer event.
            </summary>
            <remarks>
            This corresponds to the rcContact property of the <c>POINTER_TOUCH_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.TouchContactRaw">
            <summary>
            Gets or sets the TouchContactRaw of the pointer event.
            </summary>
            <remarks>
            This corresponds to the rcContactRaw property of the <c>POINTER_TOUCH_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.TouchOrientation">
            <summary>
            Gets or sets the TouchOrientation of the pointer event.
            </summary>
            <remarks>
            This corresponds to the orientation property of the <c>POINTER_TOUCH_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo.TouchPressure">
            <summary>
            Gets or sets the TouchPressure of the pointer event.
            </summary>
            <remarks>
            This corresponds to the pressure property of the <c>POINTER_TOUCH_INFO</c> struct as defined in the Windows SDK (_winuser.h_).
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2PermissionRequestedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.PermissionRequested"/> event.
            </summary>
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.PermissionRequested"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PermissionRequestedEventArgs.Uri">
            <summary>
            Gets the origin of the web content that requests the permission.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PermissionRequestedEventArgs.PermissionKind">
            <summary>
            Gets the kind of the permission that is requested.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PermissionRequestedEventArgs.IsUserInitiated">
            <summary>
            <c>true</c> when the permission request was initiated through a user gesture such as clicking an anchor tag with target.
            </summary>
            <remarks>
            Being initiated through a user gesture does not mean that user intended to access the associated resource.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PermissionRequestedEventArgs.State">
            <summary>
            Gets or sets the status of a permission request. For example, whether the request is granted.
            </summary>
            <remarks>
            The default value is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2PermissionState.Default"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2PermissionRequestedEventArgs.GetDeferral">
            <summary>
            Gets a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Deferral"/> object.
            </summary>
            <remarks>
            Use the deferral object to make the permission decision at a later time.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2PermissionRequestedEventArgs.Handled">
            <summary>
            The host may set this flag to <c>TRUE</c> to prevent the <c>PermissionRequested</c> event from firing on the <c>CoreWebView2</c> as well.
            </summary>
            By default, both the <c>PermissionRequested</c> on the <c>CoreWebView2Frame</c> and <c>CoreWebView2</c> will be fired.
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2NewWindowRequestedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NewWindowRequested"/> event.
            </summary>
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NewWindowRequested"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NewWindowRequestedEventArgs.Uri">
            <summary>
            Gets the target uri of the new window request.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NewWindowRequestedEventArgs.NewWindow">
            <summary>
            Gets the new window or sets a WebView as a result of the new window requested.
            </summary>
            <remarks>
            Provides a WebView as the target for a window.open() from inside the requesting WebView. If this is set, the top-level window returns as the opened WindowProxy. If this is not set, then <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2NewWindowRequestedEventArgs.Handled"/> is checked to determine behavior for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NewWindowRequested"/>. WebView provided in the NewWindow property must be on the same <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> as the opener WebView and cannot be navigated. Changes to settings should be made before setting NewWindow to ensure that those settings take effect for the newly setup WebView. The new WebView must have the same profile as the opener WebView.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NewWindowRequestedEventArgs.Handled">
            <summary>
            Indicates whether the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NewWindowRequested"/> event is handled by host.
            </summary>
            <remarks>
            If this is <c>false</c> and no <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2NewWindowRequestedEventArgs.NewWindow"/> is set, the WebView opens a popup window and it returns as opened WindowProxy. If set to <c>true</c> and no <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2NewWindowRequestedEventArgs.NewWindow"/> is set for <c>window.open()</c>, the opened WindowProxy is for a dummy window object and no window loads. The default value is <c>false</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NewWindowRequestedEventArgs.IsUserInitiated">
            <summary>
            <c>true</c> when the new window request was initiated through a user gesture such as selecting an anchor tag with target.
            </summary>
            <remarks>
            The Microsoft Edge popup blocker is disabled for WebView so the app is able to use this flag to block non-user initiated popups.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NewWindowRequestedEventArgs.WindowFeatures">
            <summary>
            Gets the window features specified by the <c>window.open()</c> call. These features should be considered for positioning and sizing of new WebView windows.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2NewWindowRequestedEventArgs.GetDeferral">
            <summary>
            Gets a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Deferral"/> object and put the event into a deferred state.
            </summary>
            <remarks>
            Use this to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Deferral.Complete"/> the window open request at a later time. While this event is deferred the opener window returns a WindowProxy to an un-navigated window, which navigates when the deferral is complete.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NewWindowRequestedEventArgs.Name">
            <summary>
            Gets the name of the new window.
            </summary>
            <remarks>
            This window can be created via <c>window.open(url, windowName)</c>, where the windowName parameter corresponds to <c>Name</c> property.
            If no windowName is passed to <c>window.open</c>, then the <c>Name</c> property will be set to an empty string. Additionally, if window is opened through other means, such as <c>&lt;a target="windowName"&gt;</c> or <c>&lt;iframe name="windowName"&gt;</c>, then the <c>Name</c> property will be set accordingly. In the case of target=_blank, the <c>Name</c> property will be an empty string.
            Opening a window via Ctrl+clicking a link would result in the <c>Name</c> property being set to an empty string.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2NavigationStartingEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event.
            </summary>
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationStartingEventArgs.Uri">
            <summary>
            Gets the uri of the requested navigation.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationStartingEventArgs.IsUserInitiated">
            <summary>
            <c>true</c> when the new window request was initiated through a user gesture.
            </summary>
            <remarks>
            Examples of user initiated requests are:
            - Selecting an anchor tag with target
            - Programmatic window open from a script that directly run as a result of user interaction such as via onclick handlers.
            Non-user initiated requests are programmatic window opens from a script that are not directly triggered by user interaction, such as those that run while loading a new page or via timers.
            The Microsoft Edge popup blocker is disabled for WebView so the app is able to use this flag to block non-user initiated popups.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationStartingEventArgs.IsRedirected">
            <summary>
            <c>true</c> when the navigation is redirected.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationStartingEventArgs.RequestHeaders">
            <summary>
            Gets the HTTP request headers for the navigation.
            </summary>
            <remarks>
            Note, you are not able to modify the HTTP request headers in a <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event.
            </remarks>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationStartingEventArgs.Cancel">
            <summary>
            Determines whether to cancel the navigation.
            </summary>
            <remarks>
            If set to <c>true</c>, the navigation is no longer present and the content of the current page is intact. For performance reasons, <c>GET</c> HTTP requests may happen, while the host is responding. You may set cookies and use part of a request for the navigation. Cancellation for navigation to <c>about:blank</c> or frame navigation to srcdoc is not supported and ignored.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationStartingEventArgs.NavigationId">
            <summary>
            Gets the ID of the navigation.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationStartingEventArgs.AdditionalAllowedFrameAncestors">
            <summary>
            Additional allowed frame ancestors set by the host app.
            </summary>
            <remarks>
            The app may set this property to allow a frame to be embedded by additional ancestors besides what is allowed by http header [X-Frame-Options](https://developer.mozilla.org/docs/Web/HTTP/Headers/X-Frame-Options) and [Content-Security-Policy frame-ancestors directive](https://developer.mozilla.org/docs/Web/HTTP/Headers/Content-Security-Policy/frame-ancestors).
            If set, a frame ancestor is allowed if it is allowed by the additional allowed frame ancestors or original http header from the site.
            Whether an ancestor is allowed by the additional allowed frame ancestors is done the same way as if the site provided it as the source list of the Content-Security-Policy frame-ancestors directive.
            For example, if <c>https://example.com</c> and <c>https://www.example.com</c> are the origins of the top page and intermediate iframes that embed a nested site-embedding iframe, and you fully trust those origins, you should set this property to <c>https://example.com https://www.example.com</c>.
            
            This property gives the app the ability to use iframe to embed sites that otherwise could not be embedded in an iframe in trusted app pages.
            This could potentially subject the embedded sites to [Clickjacking](https://wikipedia.org/wiki/Clickjacking) attack from the code running in the embedding web page. Therefore, you should only set this property with origins of fully trusted embedding page and any intermediate iframes.
            Whenever possible, you should use the list of specific origins of the top and intermediate frames instead of wildcard characters for this property.
            This API is to provide limited support for app scenarios that used to be supported by <c>&lt;webview&gt;</c> element in other solutions like JavaScript UWP apps and Electron.
            You should limit the usage of this property to trusted pages, and specific navigation target url, by checking the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.Source"/>, and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationStartingEventArgs.Uri"/>.
            
            This property is ignored for top level document navigation.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2NavigationCompletedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationCompleted"/> event.
            </summary>
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationCompleted"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationCompletedEventArgs.IsSuccess">
            <summary>
            <c>true</c> when the navigation is successful; <c>false</c> for a navigation that ended up in an error page (failures due to no network, DNS lookup failure, HTTP server responds with 4xx).
            </summary>
            <remarks>
            This may also be <c>false</c> for additional scenarios such as <c>window.stop()</c> run on navigated page.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationCompletedEventArgs.WebErrorStatus">
            <summary>
            Gets the error code if the navigation failed.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationCompletedEventArgs.NavigationId">
            <summary>
            Gets the ID of the navigation.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationCompletedEventArgs.HttpStatusCode">
            <summary>
            The HTTP status code of the navigation if it involved an HTTP request. For instance, this will usually be 200 if the request was successful, 404 if a page was not found, etc. See https://developer.mozilla.org/docs/Web/HTTP/Status for a list of common status codes.
            </summary>
            <remarks>
            The <c>HttpStatusCode</c> property will be 0 in the following cases:
            <list type="bullet">
            <item><description>
            The navigation did not involve an HTTP request. For instance, if it was a navigation to a <c>file://</c> URL, or if it was a same-document navigation.
            </description></item>
            <item><description>
            The navigation failed before a response was received. For instance, if the hostname was not found, or if there was a network error.
            </description></item>
            </list>
            In those cases, you can get more information from the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationCompletedEventArgs.IsSuccess"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationCompletedEventArgs.WebErrorStatus"/> properties.
            
            If the navigation receives a successful HTTP response, but the navigated page calls <c>window.stop()</c> before it finishes loading, then HttpStatusCode may contain a success code like 200, but <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationCompletedEventArgs.IsSuccess"/> will be <c>false</c> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2NavigationCompletedEventArgs.WebErrorStatus"/> will be <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus.ConnectionAborted"/>.
            
            Since WebView2 handles HTTP continuations and redirects automatically, it is unlikely for HttpStatusCode to ever be in the 1xx or 3xx ranges.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusRequestedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.MoveFocusRequested"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusRequestedEventArgs.Reason">
            <summary>
            Gets the reason for WebView to raise the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.MoveFocusRequested"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusRequestedEventArgs.Handled">
            <summary>
            Indicates whether the event has been handled by the app.
            </summary>
            <remarks>
            If the app has moved the focus to another desired location, it should set Handled property to <c>true</c>. When <c>Handled</c> property is <c>false</c> after the event handler returns, default action is taken. The default action is to try to find the next tab stop child window in the app and try to move focus to that window. If no other window exists to move focus, focus is cycled within the web content of the WebView.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2FrameInfo">
            <summary>
            Provides a set of properties for a frame in the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2FrameInfo.Name">
            <summary>
            Gets the name attribute of the frame, as in <c>&lt;iframe name="frame-name" ...&gt;</c>.
            </summary>
            <remarks>
            The returned string is empty when the frame has no name attribute.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2FrameInfo.Source">
            <summary>
            The URI of the document in the frame.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2FrameCreatedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.FrameCreated"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2FrameCreatedEventArgs.Frame">
            <summary>
            Gets the created frame.
            </summary>
            <seealso cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Frame"/>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2DownloadStartingEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.DownloadStarting"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadStartingEventArgs.DownloadOperation">
            <summary>
            Returns the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation"/> for the download that has started.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadStartingEventArgs.Cancel">
            <summary>
            Indicates whether to cancel the download.
            </summary>
            <remarks>
            If canceled, the download save dialog is not displayed regardless of the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadStartingEventArgs.Handled"/> value and the state is changed to <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadState.Interrupted"/> with interrupt reason <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason.UserCanceled"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadStartingEventArgs.ResultFilePath">
            <summary>
            The path to the file.
            </summary>
            <remarks>
            If setting the path, the host should ensure that it is an absolute path, including the file name, and that the path does not point to an existing file. If the path points to an existing file, the file will be overwritten. If the directory does not exist, it is created.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DownloadStartingEventArgs.Handled">
            <summary>
            Indicates whether to hide the default download dialog.
            </summary>
            <remarks>
            If set to true, the default download dialog is hidden for this download. The download progresses normally if it is not canceled, there will just be no default UI shown. By default the value is false and the default download dialog is shown.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2DownloadStartingEventArgs.GetDeferral">
            <summary>
            Gets a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Deferral"/> object.
            </summary>
            <remarks>
            Use this to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Deferral.Complete"/> the event at a later time.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2DOMContentLoadedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.DOMContentLoaded"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DOMContentLoadedEventArgs.NavigationId">
            <summary>
            Gets the ID of the navigation.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2DevToolsProtocolEventReceiver">
            <summary>
            A Receiver is created for a particular DevTools Protocol event and allows you to subscribe and unsubscribe from that event.
            </summary>
            <remarks>
            Obtained from the WebView object using <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.GetDevToolsProtocolEventReceiver(System.String)"/>.
            </remarks>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.GetDevToolsProtocolEventReceiver(System.String)"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2DevToolsProtocolEventReceiver.DevToolsProtocolEventReceived">
            <summary>
            DevToolsProtocolEventReceived is raised when the corresponding DevToolsProtocol event is raised.
            </summary>
            <seealso cref="T:Microsoft.Web.WebView2.Core.CoreWebView2DevToolsProtocolEventReceivedEventArgs"/>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2DevToolsProtocolEventReceivedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2DevToolsProtocolEventReceiver.DevToolsProtocolEventReceived"/> event.
            </summary>
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2DevToolsProtocolEventReceiver.DevToolsProtocolEventReceived"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DevToolsProtocolEventReceivedEventArgs.ParameterObjectAsJson">
            <summary>
            Gets the parameter object of the corresponding DevToolsProtocol event represented as a JSON string.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2DevToolsProtocolEventReceivedEventArgs.SessionId">
            <summary>
            Gets the sessionId of the target where the event originates from. Empty string is returned as sessionId if the event comes from the default session for the top page.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions">
            <summary>
            Used to manage profile options that created by <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2ControllerOptions"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions.ProfileName">
            <summary>
            Manage the name of the controller's profile.
            </summary>
            <remarks>
            The <c>ProfileName</c> property is to specify a profile name, which is only allowed to contain the following ASCII characters. It has a maximum length of 64 characters excluding the null-terminator. It is ASCII case insensitive.
            
            * alphabet characters: a-z and A-Z
            * digit characters: 0-9
            * and '#', '@', '$', '(', ')', '+', '-', '_', '~', '.', ' ' (space).
            
            Note: the text must not end with a period '.' or ' ' (space). And, although upper-case letters are allowed, they're treated just as lower-case counterparts because the profile name will be mapped to the real profile directory path on disk and Windows file system handles path names in a case-insensitive way.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions.IsInPrivateModeEnabled">
            <summary>
            Manage the controller's InPrivate mode.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget">
            <summary>
            Represents the information regarding the context menu target. Includes the context selected and the appropriate data used for the actions of a context menu.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.Kind">
            <summary>
            Gets the kind of context that the user selected as <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTargetKind"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.IsEditable">
            <summary>
            Returns <c>true</c> if the context menu is requested on an editable component.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.IsRequestedForMainFrame">
            <summary>
            Returns <c>true</c> if the context menu was requested on the main frame and <c>false</c> if invoked on another frame.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.PageUri">
            <summary>
            Gets the uri of the page.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.FrameUri">
            <summary>
            Gets the uri of the frame. Will match the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.PageUri"/> if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.IsRequestedForMainFrame"/> is <c>true</c>.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.HasLinkUri">
            <summary>
            Returns <c>true</c> if the context menu is requested on HTML containing an anchor tag.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.LinkUri">
            <summary>
            Gets the uri of the link (if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.HasLinkUri"/> is <c>true</c>, <c>null</c> otherwise).
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.HasLinkText">
            <summary>
            Returns <c>true</c> if the context menu is requested on text element that contains an anchor tag.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.LinkText">
            <summary>
            Gets the text of the link (if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.HasLinkText"/> is <c>true</c>, <c>null</c> otherwise).
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.HasSourceUri">
            <summary>
            Returns <c>true</c> if the context menu is requested on HTML containing a source uri.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.SourceUri">
            <summary>
            Gets the active source uri of element (if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.HasSourceUri"/> is <c>true</c>, <c>null</c> otherwise).
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.HasSelection">
            <summary>
            Returns <c>true</c> if the context menu is requested on a selection.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.SelectionText">
            <summary>
            Gets the selected text (if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget.HasSelection"/> is <c>true</c>, <c>null</c> otherwise).
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuRequestedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContextMenuRequested"/> event.
            </summary>
            <remarks>
            Will contain the selection information and a collection of all of the default context menu items that the WebView would show. Allows the app to draw its own context menu or add/remove from the default context menu.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuRequestedEventArgs.MenuItems">
            <summary>
            Gets the collection of <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/> objects.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuRequestedEventArgs.ContextMenuTarget">
            <summary>
            Gets the target information associated with the requested context menu.
            </summary>
            <seealso cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuRequestedEventArgs.Location">
            <summary>
            Gets the coordinates where the context menu request occured in relation to the upper left corner of the WebView bounds.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuRequestedEventArgs.SelectedCommandId">
            <summary>
            Gets or sets the selected <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/>'s <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.CommandId"/>.
            </summary>
            <remarks>
            When the app handles the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContextMenuRequested"/> event, it can set this to report the selected command from the context menu. The default value is -1 which means that no selection occurred. The app can also set the command ID for a custom context menu item, which will cause the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.CustomItemSelected"/> event to be fired, however while command IDs for each custom context menu item is unique during a ContextMenuRequested event, WebView may reassign command ID values of deleted custom ContextMenuItems to new objects and the command ID assigned to the same custom item can be different between each app runtime. The command ID should always be obtained via the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.CommandId"/> property.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuRequestedEventArgs.Handled">
            <summary>
            Gets or sets whether the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContextMenuRequested"/> event is handled by host after the event handler completes or after the deferral is completed if there is a taken <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Deferral"/>.
            </summary>
            <remarks>
            If Handled is set to <c>true</c> then WebView2 will not display a context menu and will instead use the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuRequestedEventArgs.SelectedCommandId"/> property to indicate which, if any, context menu item to invoke. If after the event handler or deferral completes, Handled is set to <c>false</c> then WebView will display a context menu based on the contents of the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuRequestedEventArgs.MenuItems"/> property. The default value is <c>false</c>.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuRequestedEventArgs.GetDeferral">
            <summary>
            Returns a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Deferral"/> object.
            </summary>
            <remarks>
            Use this operation to complete the event when the custom context menu is closed.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem">
            <summary>
            Represents a context menu item of a context menu displayed by WebView.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.Name">
            <summary>
            Gets the unlocalized name for the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/>.
            </summary>
            <remarks>
            Use this to distinguish between context menu item types. This will be the English label of the menu item in lower camel case. For example, the "Save as" menu item will be "saveAs". Extension menu items will be "extension", custom menu items will be "custom" and spellcheck items will be "spellCheck".
            Some example context menu item names are:
            
            <list type="bullet">
            <item><description>
            "saveAs"
            </description></item>
            <item><description>
            "copyImage"
            </description></item>
            <item><description>
            "openLinkInNewWindow"
            </description></item>
            </list>
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.Label">
            <summary>
            Gets the localized label for the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/>. Will contain an ampersand for characters to be used as keyboard accelerator.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.CommandId">
            <summary>
            Gets the Command ID for the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/>.
            </summary>
            <remarks>
            Use this to report the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuRequestedEventArgs.SelectedCommandId"/> in <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContextMenuRequested"/> event.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.ShortcutKeyDescription">
            <summary>
            Gets the localized keyboard shortcut for this <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/>.
            </summary>
            <remarks>
            It will be the empty string if there is no keyboard shortcut. This is text intended to be displayed to the end user to show the keyboard shortcut. For example this property is Ctrl+Shift+I for the "Inspect" <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.Icon">
            <summary>
            Gets the Icon for the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/> in PNG, Bitmap or SVG formats in the form of an IStream.
            </summary>
            <remarks>
            Stream will be rewound to the start of the image data before being read.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.Kind">
            <summary>
            Gets the kind of <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/> as <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItemKind"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.IsEnabled">
            <summary>
            Gets or sets the enabled property of the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/>. Must only be used in the case of a custom context menu item.
            </summary>
            <remarks>
            The default value for this is <c>true</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.IsChecked">
            <summary>
            Gets or sets the checked property of the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/>.
            </summary>
            <remarks>
            Must only be used for custom context menu items that are of kind <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItemKind.CheckBox"/> or <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItemKind.Radio"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.Children">
            <summary>
            Gets the list of children menu items if the kind is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItemKind.Submenu"/>.
            </summary>
            <remarks>
            If the kind is not <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItemKind.Submenu"/>, will return <c>null</c>.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem.CustomItemSelected">
            <summary>
            CustomItemSelected event is raised when the user selects this <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem"/>.
            </summary>
            <remarks>
            Will only be raised for end developer created context menu items.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ContentLoadingEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContentLoadingEventArgs.IsErrorPage">
            <summary>
            <c>true</c> if the loaded content is an error page.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ContentLoadingEventArgs.NavigationId">
            <summary>
            Gets the ID of the navigation.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController">
            <summary>
            This class is an extension of the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> class to support visual hosting.
            </summary>
            <summary>
            This class is an extension of the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> class to support visual hosting.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.RootVisualTarget">
            <summary>
            Gets or sets the root visual in the hosting app's visual tree.
            </summary>
            <remarks>
            This visual is where the WebView will connect its visual tree. The app uses this visual to position the WebView within the app. The app still needs to use the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.Bounds"/> property to size the WebView. The RootVisualTarget property can be an IDCompositionVisual or a Windows::UI::Composition::ContainerVisual. WebView will connect its visual tree to the provided visual before returning from the property setter. The app needs to commit on its device setting the RootVisualTarget property. The RootVisualTarget property supports being set to <c>null</c> to disconnect the WebView from the app's visual tree.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.Cursor">
            <summary>
            Gets the current cursor that WebView thinks it should be.
            </summary>
            <remarks>
            The cursor should be set in WM_SETCURSOR through Mouse.SetCursor or set on the corresponding parent/ancestor HWND of the WebView through ::SetClassLongPtr. The HCURSOR can be freed so CopyCursor/DestroyCursor is recommended to keep your own copy if you are doing more than immediately setting the cursor.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.SystemCursorId">
            <summary>
            Gets the current system cursor ID reported by the underlying rendering engine for WebView.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.CursorChanged">
            <summary>
            The event is raised when WebView thinks the cursor should be changed.
            </summary>
            <remarks>
            For example, when the mouse cursor is currently the default cursor but is then moved over text, it may try to change to the IBeam cursor.
            It is expected for the developer to send <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.Leave"/> messages (in addition to <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.Move"/> messages) through <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.SendMouseInput(Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind,Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys,System.UInt32,System.Drawing.Point)"/>. This is to ensure that the mouse is actually within the WebView that sends out CursorChanged events.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.SendMouseInput(Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind,Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys,System.UInt32,System.Drawing.Point)">
            <summary>
            Sends mouse input to the WebView.
            </summary>
            <param name="eventKind">The mouse event kind.</param>
            <param name="virtualKeys">The virtual keys associated with the <c>eventKind</c>.</param>
            <param name="mouseData">The amount of wheel movement.</param>
            <param name="point">The absolute position of the mouse, or the amount of motion since the last mouse event was generated, depending on the <c>eventKind</c>.</param>
            <remarks>
            If <c>eventKind</c> is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.HorizontalWheel"/> or <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.Wheel"/>, then <c>mouseData</c> specifies the amount of wheel movement.
            A positive value indicates that the wheel was rotated forward, away from the user; a negative value indicates that the wheel was rotated backward, toward the user. One wheel click is defined as WHEEL_DELTA, which is 120. If <c>eventKind</c> is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.XButtonDoubleClick"/>, <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.XButtonDown"/>, or <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.XButtonUp"/>, then <c>mouseData</c> specifies which X buttons were pressed or released. This value should be 1 if the first X button is pressed/released and 2 if the second X button is pressed/released. If <c>eventKind</c> is <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.Leave"/>, then <c>virtualKeys</c>, <c>mouseData</c>, and point should all be zero. If <c>eventKind</c> is any other value, then <c>mouseData</c> should be zero. <c>point</c> is expected to be in the client coordinate space of the WebView. To track mouse events that start in the WebView and can potentially move outside of the WebView and host application, calling SetCapture and ReleaseCapture is recommended. To dismiss hover popups, it is also recommended to send <see cref="F:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind.Leave"/> messages.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.SendPointerInput(Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind,Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo)">
            <summary>
            Sends pen or pointer input to the WebView.
            </summary>
            <param name="eventKind">The pointer event kind.</param>
            <param name="pointerInfo">The pointer information.</param>
            <remarks>
            Accepts touch or pen pointer input of kinds defined in <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind"/>.
            Any pointer input from the system must be converted into a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo"/> first.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ClientCertificateRequested"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.Host">
            <summary>
            Returns host name of the server that requested client certificate authentication.
            </summary>
            <remarks>
            Normalization rules applied to the hostname are:
            
            <list type="bullet">
            <item>
            <description>Convert to lowercase characters for ascii characters.</description>
            </item>
            <item>
            <description>Punycode is used for representing non ascii characters.</description>
            </item>
            <item>
            <description>Strip square brackets for IPV6 address.</description>
            </item>
            </list>
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.Port">
            <summary>
            Returns port of the server that requested client certificate authentication.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.IsProxy">
            <summary>
            Returns true if the server that issued this request is an http proxy. Returns false if the server is the origin server.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.AllowedCertificateAuthorities">
            <summary>
            The list contains distinguished names of certificate authorities allowed by the server.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.MutuallyTrustedCertificates">
            <summary>
            Returns the list of <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate"/> when client certificate authentication is requested. The list contains mutually trusted CA certificate.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.SelectedCertificate">
            <summary>
            Selected certificate to respond to the server.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.Cancel">
            <summary>
            Indicates whether to cancel the certificate selection.
            </summary>
            <remarks>
            If canceled, the request is aborted regardless of the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.Handled"/> property. By default the value is false.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.Handled">
            <summary>
            Indicates whether the event has been handled by host.
            </summary>
            <remarks>
            Set to true to respond to the server with or without a certificate. If this flag is true with a <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.SelectedCertificate"/> it responds to the server with the selected certificate otherwise respond to the server without a certificate. By default the value of <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.Handled"/> and <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.Cancel"/> are false and display default client certificate selection dialog prompt to allow the user to choose a certificate. The <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.SelectedCertificate"/> is ignored unless <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.Handled"/> is set to true.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs.GetDeferral">
            <summary>
            Gets a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Deferral"/> object.
            </summary>
            <remarks>
            Use this to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Deferral.Complete"/> the event at a later time.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2BrowserProcessExitedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Environment.BrowserProcessExited"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2BrowserProcessExitedEventArgs.BrowserProcessExitKind">
            <summary>
            The kind of browser process exit that has occurred.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2BrowserProcessExitedEventArgs.BrowserProcessId">
            <summary>
            The process ID of the browser process that has exited.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2BasicAuthenticationResponse">
            <summary>
            Represents a Basic HTTP authentication response that contains a user name and a password as according to RFC7617 (https://tools.ietf.org/html/rfc7617)
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2BasicAuthenticationResponse.UserName">
            <summary>
            User name provided for authentication.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2BasicAuthenticationResponse.Password">
            <summary>
            Password provided for authentication.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2BasicAuthenticationRequestedEventArgs">
            <summary>
            Event args for the BasicAuthenticationRequested event. Will contain the request that led to the HTTP authorization challenge, the challenge and allows the host to provide authentication response or cancel the request.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2BasicAuthenticationRequestedEventArgs.Uri">
            <summary>
            The URI that led to the authentication challenge.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2BasicAuthenticationRequestedEventArgs.Challenge">
            <summary>
            The authentication challenge string.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2BasicAuthenticationRequestedEventArgs.Response">
            <summary>
            Response to the authentication request with credentials.
            </summary>
            <remarks>
            This object will be populated by the app if the host would like to provide authentication credentials.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2BasicAuthenticationRequestedEventArgs.Cancel">
            <summary>
            Indicates whether to cancel the authentication request.
            </summary>
            <remarks>
            <c>false</c> by default. If set to <c>true</c>, Response will be ignored.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Core.CoreWebView2BasicAuthenticationRequestedEventArgs.GetDeferral">
            <summary>
            Gets a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Deferral"/> object.
            </summary>
            <remarks>
            Use this Deferral to defer the decision to show the Basic Authentication dialog.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.CoreWebView2AcceleratorKeyPressedEventArgs">
            <summary>
            Event args for the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.AcceleratorKeyPressed"/> event.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2AcceleratorKeyPressedEventArgs.KeyEventKind">
            <summary>
            Gets the key event kind that caused the event to run.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2AcceleratorKeyPressedEventArgs.VirtualKey">
            <summary>
            Gets the Win32 virtual key code of the key that was pressed or released.
            </summary>
            <remarks>
            It is one of the Win32 virtual key constants such as VK_RETURN or an (uppercase) ASCII value such as 'A'. Verify whether Ctrl or Alt are pressed by running GetKeyState(VK_CONTROL) or GetKeyState(VK_MENU).
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2AcceleratorKeyPressedEventArgs.KeyEventLParam">
            <summary>
            Gets the LPARAM value that accompanied the window message.
            </summary>
            <remarks>
            See the documentation for the <c>WM_KEYDOWN</c> and <c>WM_KEYUP</c> messages.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2AcceleratorKeyPressedEventArgs.PhysicalKeyStatus">
            <summary>
            Gets a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2PhysicalKeyStatus"/> representing the information passed in the LPARAM of the window message.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Core.CoreWebView2AcceleratorKeyPressedEventArgs.Handled">
            <summary>
            Indicates whether the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.AcceleratorKeyPressed"/> event is handled by host.
            </summary>
            <remarks>
            If set to <c>true</c> then this prevents the WebView from performing the default action for this accelerator key. Otherwise the WebView will perform the default action for the accelerator key.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Core.Raw.BrowserInfo">
            <summary>
            Browser version values from /chrome/VERSION
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Core.Raw.BrowserInfo.PRODUCT_VERSION">
            <summary> Product Version </summary>
        </member>
    </members>
</doc>
