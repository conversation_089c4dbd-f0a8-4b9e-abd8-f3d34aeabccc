﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class ThumbnailSizeForm : BaseForm
    {
        public ThumbnailSizeForm()
        {
            InitializeComponent();
        }

        public ThumbnailSizeForm(Size thumbnailSize) : this()
        {
            ThumbnailSize = thumbnailSize;
            nudWidth.SetValue(ThumbnailSize.Width);
            nudHeight.SetValue(ThumbnailSize.Height);
        }

        public Size ThumbnailSize { get; set; }

        private void btnReset_Click(object sender, EventArgs e)
        {
            nudWidth.SetValue(200);
            nudHeight.SetValue(150);
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            ThumbnailSize = new Size((int)nudWidth.Value, (int)nudHeight.Value);
            DialogResult = DialogResult.OK;
            Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
}