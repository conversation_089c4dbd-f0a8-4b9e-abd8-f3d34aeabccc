using System;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace OCRTools.WebBroswerEx
{
    /// <summary>
    /// WebView2动态加载管理器
    /// 负责动态加载WebView2相关DLL并提供统一的访问接口
    /// </summary>
    public static class WebView2DynamicManager
    {
        private static bool _isInitialized = false;
        private static bool _isWebView2Available = false;
        private static Type _webView2Type;
        private static Type _coreEnvironmentType;

        /// <summary>
        /// 尝试初始化WebView2组件
        /// </summary>
        /// <returns>是否成功初始化</returns>
        public static bool TryInitializeWebView2()
        {
            if (_isInitialized) return _isWebView2Available;
            _isInitialized = true;

            try
            {
                if (!IsWebView2RuntimeInstalled() || !LoadWebView2Loader()) return false;

                var coreAssembly = CommonString.LoadDllByName("Microsoft.Web.WebView2.Core,", true, true, true);
                var winFormsAssembly = CommonString.LoadDllByName("Microsoft.Web.WebView2.WinForms,", true, true, true);

                coreAssembly = CommonString.LoadDllByName("Microsoft.Web.WebView2.Core,", true, false, true);
                winFormsAssembly = CommonString.LoadDllByName("Microsoft.Web.WebView2.WinForms,", true, false, true);

                if (coreAssembly == null || winFormsAssembly == null) return false;

                _coreEnvironmentType = coreAssembly.GetType("Microsoft.Web.WebView2.Core.CoreWebView2Environment");
                _webView2Type = winFormsAssembly.GetType("Microsoft.Web.WebView2.WinForms.WebView2");

                return _isWebView2Available = (_coreEnvironmentType != null && _webView2Type != null);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查WebView2运行时是否安装
        /// </summary>
        internal static bool IsWebView2RuntimeInstalled()
        {
            try
            {
                // 检查注册表
                var registryPaths = new[]
                {
                    @"SOFTWARE\WOW6432Node\Microsoft\EdgeUpdate\Clients\{********-FE2A-4295-8BDF-00C3A9A7E4C5}",
                    @"SOFTWARE\Microsoft\EdgeUpdate\Clients\{********-FE2A-4295-8BDF-00C3A9A7E4C5}"
                };

                var registryCheck = registryPaths.Any(path =>
                {
                    try { using (var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(path)) return key != null; }
                    catch { return false; }
                });

                // 检查Edge浏览器
                var edgePaths = new[]
                {
                    @"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
                    @"C:\Program Files\Microsoft\Edge\Application\msedge.exe"
                };
                var edgeCheck = edgePaths.Any(File.Exists);

                // 检查WebView2运行时文件
                var runtimePaths = new[]
                {
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), @"Microsoft\EdgeWebView\Application"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), @"Microsoft\EdgeWebView\Application"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), @"Microsoft\EdgeWebView\Application")
                };
                var runtimeFilesCheck = runtimePaths.Any(path => Directory.Exists(path) && Directory.GetDirectories(path).Length > 0);

                return registryCheck || edgeCheck || runtimeFilesCheck;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 动态加载WebView2Loader.dll (native DLL)
        /// </summary>
        internal static bool LoadWebView2Loader()
        {
            try
            {
                CommonString.LoadDllByName("WebView2Loader,", true, true);
                return File.Exists(Path.Combine(CommonString.DefaultDLLPath, "WebView2Loader.dll"));
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 创建WebView2控件实例
        /// 调用方已确保WebView2可用，直接创建控件
        /// </summary>
        public static Control CreateWebView2Control()
        {
            if (_webView2Type == null) return null;
            try
            {
                return Activator.CreateInstance(_webView2Type) as Control;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// WebView2控件类型
        /// </summary>
        public static Type WebView2Type => _webView2Type;

        /// <summary>
        /// CoreWebView2Environment类型
        /// </summary>
        public static Type CoreEnvironmentType => _coreEnvironmentType;
    }
}
