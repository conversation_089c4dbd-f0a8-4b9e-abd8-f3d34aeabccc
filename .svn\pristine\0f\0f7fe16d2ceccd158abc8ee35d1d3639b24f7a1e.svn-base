﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace ImageLib
{
    public class ImageHelper
    {
        static Dictionary<ImageTypeEnum, BaseImageUpload> dicOcrs = new Dictionary<ImageTypeEnum, BaseImageUpload>();

        static ImageHelper()
        {
            var allOCRS = Assembly.GetExecutingAssembly().GetTypes()
                .Where(t => !t.IsAbstract && t.BaseType.Equals(typeof(BaseImageUpload))).ToList();

            if (allOCRS != null && allOCRS.Any())
            {
                allOCRS.ForEach(p =>
                {
                    Type objtype = Type.GetType(p.FullName, true);
                    var obj = Activator.CreateInstance(objtype) as BaseImageUpload;
                    if (obj != null)
                    {
                        dicOcrs.Add(obj.ImageType, obj);
                    }
                });
            }
        }

        public static string GetResult(ImageTypeEnum imageType, byte[] content, string ext)
        {
            return GetInstance(imageType).GetResult(content, ext);
        }

        static BaseImageUpload GetInstance(ImageTypeEnum ocrType)
        {
            return dicOcrs[ocrType];
        }
    }
}
