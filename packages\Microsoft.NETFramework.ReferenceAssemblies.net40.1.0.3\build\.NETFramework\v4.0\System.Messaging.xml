﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Messaging</name>
  </assembly>
  <members>
    <member name="T:System.Messaging.AccessControlEntry">
      <summary>Specifies access rights for a trustee (user, group, or computer) to perform application-specific implementations of common tasks.</summary>
    </member>
    <member name="M:System.Messaging.AccessControlEntry.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.AccessControlEntry" /> class that specifies neither a trustee nor set of rights to apply.</summary>
    </member>
    <member name="M:System.Messaging.AccessControlEntry.#ctor(System.Messaging.Trustee)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.AccessControlEntry" /> class that specifies a trustee to which rights are granted or denied.</summary>
      <param name="trustee">A <see cref="T:System.Messaging.Trustee" /> that specifies a user, group, computer, domain, or alias. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="trustee" /> parameter is null. </exception>
    </member>
    <member name="M:System.Messaging.AccessControlEntry.#ctor(System.Messaging.Trustee,System.Messaging.GenericAccessRights,System.Messaging.StandardAccessRights,System.Messaging.AccessControlEntryType)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.AccessControlEntry" /> class that specifies a trustee, rights to assign, and whether to grant or deny these rights.</summary>
      <param name="trustee">A <see cref="T:System.Messaging.Trustee" /> that specifies a user, group, computer, domain, or alias. </param>
      <param name="genericAccessRights">A bitwise combination of the <see cref="T:System.Messaging.GenericAccessRights" /> values. </param>
      <param name="standardAccessRights">A bitwise combination of the <see cref="T:System.Messaging.StandardAccessRights" /> values. </param>
      <param name="entryType">One of the <see cref="T:System.Messaging.AccessControlEntryType" /> values, which specifies whether to allow, deny, set, or revoke the specified rights. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="trustee" /> parameter is null. </exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="genericAccessRights" />, <paramref name="standardAccessRights" />, or <paramref name="entryType" /> parameters are not valid enumeration values. </exception>
    </member>
    <member name="P:System.Messaging.AccessControlEntry.CustomAccessRights">
      <summary>Gets or sets custom access rights.</summary>
      <returns>Application-specific access rights, usually defined as a bitflag.</returns>
    </member>
    <member name="P:System.Messaging.AccessControlEntry.EntryType">
      <summary>Gets or sets a value that indicates how the access rights apply to the trustee.</summary>
      <returns>One of the <see cref="T:System.Messaging.AccessControlEntryType" /> values, which specifies whether to allow, deny, set, or revoke the specified rights. The default is Allow.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The entry type is not a valid <see cref="T:System.Messaging.AccessControlEntryType" /> enumeration value. </exception>
    </member>
    <member name="P:System.Messaging.AccessControlEntry.GenericAccessRights">
      <summary>Gets or sets a set of common access rights that map to both standard and object-specific access rights for reading, writing, and executing.</summary>
      <returns>A bitwise combination of the <see cref="T:System.Messaging.GenericAccessRights" /> values.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value you set is not a valid combination of <see cref="T:System.Messaging.GenericAccessRights" /> bitflag members. </exception>
    </member>
    <member name="P:System.Messaging.AccessControlEntry.StandardAccessRights">
      <summary>Gets or sets a set of standard access rights that correspond to operations common to most types of securable objects.</summary>
      <returns>A bitwise combination of the <see cref="T:System.Messaging.StandardAccessRights" /> values.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value you set is not a valid combination of <see cref="T:System.Messaging.StandardAccessRights" /> bitflag members. </exception>
    </member>
    <member name="P:System.Messaging.AccessControlEntry.Trustee">
      <summary>Gets or sets the user, group, domain, or alias to which you are assigning access rights.</summary>
      <returns>A <see cref="T:System.Messaging.Trustee" /> that specifies a user account, group account, or logon session to which a <see cref="T:System.Messaging.AccessControlEntry" /> applies.</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Messaging.AccessControlEntry.Trustee" /> property is null. </exception>
    </member>
    <member name="T:System.Messaging.AccessControlEntryType">
      <summary>Specifies whether to allow, deny, or revoke access rights for a trustee.</summary>
    </member>
    <member name="F:System.Messaging.AccessControlEntryType.Allow">
      <summary>An access-allowed entry that causes the new rights to be added to any existing rights the trustee has.</summary>
    </member>
    <member name="F:System.Messaging.AccessControlEntryType.Set">
      <summary>An access-allowed entry that is similar to Allow, except that the new entry allows only the specified rights. Using it discards any existing rights, including all existing access-denied entries for the trustee.</summary>
    </member>
    <member name="F:System.Messaging.AccessControlEntryType.Deny">
      <summary>An access-denied entry that denies the specified rights in addition to any currently denied rights of the trustee.</summary>
    </member>
    <member name="F:System.Messaging.AccessControlEntryType.Revoke">
      <summary>An entry that removes all existing allowed or denied rights for the specified trustee.</summary>
    </member>
    <member name="T:System.Messaging.AccessControlList">
      <summary>Contains a list of access control entries, specifying access rights for one or more trustees.</summary>
    </member>
    <member name="M:System.Messaging.AccessControlList.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.AccessControlList" /> class.</summary>
    </member>
    <member name="M:System.Messaging.AccessControlList.Add(System.Messaging.AccessControlEntry)">
      <summary>Appends an access control entry to the access control list.</summary>
      <returns>The position into which the new access control entry was inserted.</returns>
      <param name="entry">A <see cref="T:System.Messaging.AccessControlEntry" /> to append to the end of the access control list. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="entry" /> parameter is null. </exception>
    </member>
    <member name="M:System.Messaging.AccessControlList.Contains(System.Messaging.AccessControlEntry)">
      <summary>Determines whether the access control list contains a specific access control entry.</summary>
      <returns>true if the access control entry is found in the access control list; otherwise, false.</returns>
      <param name="entry">The <see cref="T:System.Messaging.AccessControlEntry" /> to locate in the access control list. </param>
    </member>
    <member name="M:System.Messaging.AccessControlList.CopyTo(System.Messaging.AccessControlEntry[],System.Int32)">
      <summary>Copies the entire access control list to a compatible one-dimensional array of access control entries, starting at the specified index of the target array.</summary>
      <param name="array">An array of type <see cref="T:System.Messaging.AccessControlEntry" /> to which the access control list entries will be copied. The array must have zero-based indexing. </param>
      <param name="index">The index in the array at which to begin copying the access control list entries. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> parameter is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than zero. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="index" /> parameter is greater than or equal to the length of the <paramref name="array" /> parameter.-or-The number of elements in the source access control list is greater than the available space from <paramref name="index" /> to the end of the destination array of access control entries. </exception>
    </member>
    <member name="M:System.Messaging.AccessControlList.IndexOf(System.Messaging.AccessControlEntry)">
      <summary>Determines the specific index of an access control entry in the access control list.</summary>
      <returns>The index of the entry if it was found in the list; otherwise, -1 </returns>
      <param name="entry">The <see cref="T:System.Messaging.AccessControlEntry" /> to locate in the access control list. </param>
    </member>
    <member name="M:System.Messaging.AccessControlList.Insert(System.Int32,System.Messaging.AccessControlEntry)">
      <summary>Inserts an access control entry into the access control list at the specified position.</summary>
      <param name="index">The zero-based index at which the access control entry should be inserted. </param>
      <param name="entry">A <see cref="T:System.Messaging.AccessControlEntry" /> to insert into the access control list. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is not a valid index in this access control list. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="entry" /> parameter is null. </exception>
    </member>
    <member name="M:System.Messaging.AccessControlList.Remove(System.Messaging.AccessControlEntry)">
      <summary>Removes the first occurrence of a specific access control entry from the access control list.</summary>
      <param name="entry">The <see cref="T:System.Messaging.AccessControlEntry" /> to remove from the access control list. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="entry" /> parameter is null. </exception>
    </member>
    <member name="T:System.Messaging.AcknowledgeTypes">
      <summary>Specifies the types of acknowledgment message that Message Queuing returns to the sending application.</summary>
    </member>
    <member name="F:System.Messaging.AcknowledgeTypes.PositiveArrival">
      <summary>A mask used to request a positive acknowledgment when the original message reaches the queue.</summary>
    </member>
    <member name="F:System.Messaging.AcknowledgeTypes.PositiveReceive">
      <summary>A mask used to request a positive acknowledgment when the original message is successfully retrieved from the queue.</summary>
    </member>
    <member name="F:System.Messaging.AcknowledgeTypes.NegativeReceive">
      <summary>A mask used to request a negative acknowledgment when the original message fails to be received from the queue.</summary>
    </member>
    <member name="F:System.Messaging.AcknowledgeTypes.None">
      <summary>A mask used to request that no acknowledgment messages (positive or negative) be posted.</summary>
    </member>
    <member name="F:System.Messaging.AcknowledgeTypes.NotAcknowledgeReachQueue">
      <summary>A mask used to request a negative acknowledgment when the original message cannot reach the queue. This can happen when the time-to-reach-queue timer expires or if a message cannot be authenticated.</summary>
    </member>
    <member name="F:System.Messaging.AcknowledgeTypes.NotAcknowledgeReceive">
      <summary>A mask used to request a negative acknowledgment when an error occurs that prevents the original message from being received from the queue before its time-to-be-received timer expires.</summary>
    </member>
    <member name="F:System.Messaging.AcknowledgeTypes.FullReachQueue">
      <summary>A mask used to request positive acknowledgment if the original message reaches the queue or negative acknowledgment if the time-to-reach-queue timer expires or if the original message cannot be authenticated.</summary>
    </member>
    <member name="F:System.Messaging.AcknowledgeTypes.FullReceive">
      <summary>A mask used to request positive acknowledgment if the original message is received from the queue before its time-to-be-received timer expires or negative acknowledgment otherwise.</summary>
    </member>
    <member name="T:System.Messaging.Acknowledgment">
      <summary>Specifies the result of an attempted message delivery.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.None">
      <summary>The message is not an acknowledgment message.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.AccessDenied">
      <summary>A negative arrival acknowledgment indicating that the sending application does not have the necessary rights to send a message to the destination queue.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.BadDestinationQueue">
      <summary>A negative arrival acknowledgment indicating that the destination queue is not available to the sending application.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.BadEncryption">
      <summary>A negative arrival acknowledgment indicating that the destination queue manager could not decrypt a private message.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.BadSignature">
      <summary>A negative arrival acknowledgment indicating that the original message's digital signature is not valid and could not be authenticated by Message Queuing.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.CouldNotEncrypt">
      <summary>A negative arrival acknowledgment indicating that the source queue manager could not encrypt a private message.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.HopCountExceeded">
      <summary>A negative arrival acknowledgment indicating that the original message's hop count (which indicates the number of intermediate servers) was exceeded.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.NotTransactionalQueue">
      <summary>A negative arrival acknowledgment indicating that a transactional message was sent to a non-transactional queue.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.NotTransactionalMessage">
      <summary>A negative arrival acknowledgment indicating that a non-transactional message was sent to a transactional queue.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.Purged">
      <summary>A negative arrival acknowledgment indicating that the message was purged before reaching its destination queue.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.QueueDeleted">
      <summary>A negative read acknowledgment indicating that the queue was deleted before the message could be read.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.QueueExceedMaximumSize">
      <summary>A negative arrival acknowledgment indicating that the original message was not delivered because its destination queue is full.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.QueuePurged">
      <summary>A negative read acknowledgment indicating that the queue was purged before the message could be read.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.ReachQueue">
      <summary>A positive arrival acknowledgment indicating that the original message reached its destination queue.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.ReachQueueTimeout">
      <summary>A negative arrival acknowledgment indicating that the time-to-reach-queue or time-to-be-received timer expired before the original message could reach the destination queue.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.ReceiveTimeout">
      <summary>A negative read acknowledgment indicating that the original message was not received from the queue before its time-to-be-received timer expired.</summary>
    </member>
    <member name="F:System.Messaging.Acknowledgment.Receive">
      <summary>A positive read acknowledgment indicating that the original message was received by the receiving application.</summary>
    </member>
    <member name="T:System.Messaging.ActiveXMessageFormatter">
      <summary>Serializes or deserializes primitive data types and other objects to or from the body of a Message Queuing message, using a format that is compatible with the MSMQ ActiveX Component.</summary>
    </member>
    <member name="M:System.Messaging.ActiveXMessageFormatter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.ActiveXMessageFormatter" /> class.</summary>
    </member>
    <member name="M:System.Messaging.ActiveXMessageFormatter.CanRead(System.Messaging.Message)">
      <summary>Determines whether the formatter can deserialize the contents of the message.</summary>
      <returns>true if the <see cref="T:System.Messaging.ActiveXMessageFormatter" /> can deserialize the message; otherwise, false.</returns>
      <param name="message">The <see cref="T:System.Messaging.Message" /> to inspect. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="message" /> parameter is null. </exception>
    </member>
    <member name="M:System.Messaging.ActiveXMessageFormatter.Clone">
      <summary>Creates an instance of the <see cref="T:System.Messaging.ActiveXMessageFormatter" /> class that is identical to the current <see cref="T:System.Messaging.ActiveXMessageFormatter" />.</summary>
      <returns>An object whose properties are identical to those of this <see cref="T:System.Messaging.ActiveXMessageFormatter" />.</returns>
    </member>
    <member name="M:System.Messaging.ActiveXMessageFormatter.InitStreamedObject(System.Object)">
      <summary>Provides a utility to help serialize COM objects that implement IPersistStream and require IPersistStreamInit to be called.</summary>
      <param name="streamedObject">An OLE object that implements IPersistStreamInit. </param>
    </member>
    <member name="M:System.Messaging.ActiveXMessageFormatter.Read(System.Messaging.Message)">
      <summary>Reads the contents from the given message and creates an object that contains the deserialized message.</summary>
      <returns>The deserialized message.</returns>
      <param name="message">The <see cref="T:System.Messaging.Message" />, in MSMQ ActiveX control format, to deserialize. </param>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Messaging.Message.BodyType" /> property of the <paramref name="message" /> passed as a parameter cannot be mapped to a primitive type, nor does it represent a streamed object. </exception>
      <exception cref="T:System.NotSupportedException">The body represents a stored object. The <see cref="T:System.Messaging.ActiveXMessageFormatter" /> does not support deserialization of stored objects. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="message" /> parameter is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.ActiveXMessageFormatter.Write(System.Messaging.Message,System.Object)">
      <summary>Serializes an object into the body of the message.</summary>
      <param name="message">The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.Body" /> property will contain the serialized object. </param>
      <param name="obj">The object to be serialized into the message body. </param>
      <exception cref="T:System.InvalidOperationException">The object to serialize is neither a primitive nor a streamed object that implements the OLE IPersistStream interface. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="message" /> parameter is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Messaging.BinaryMessageFormatter">
      <summary>Serializes or deserializes an object, or an entire graph of connected objects, to or from the body of a Message Queuing message, using a binary format.</summary>
    </member>
    <member name="M:System.Messaging.BinaryMessageFormatter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.BinaryMessageFormatter" /> class without specifying a type style or top object assembly style.</summary>
    </member>
    <member name="M:System.Messaging.BinaryMessageFormatter.#ctor(System.Runtime.Serialization.Formatters.FormatterAssemblyStyle,System.Runtime.Serialization.Formatters.FormatterTypeStyle)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.BinaryMessageFormatter" /> class, specifying the formats of the root object and the type descriptions.</summary>
      <param name="topObjectFormat">Determines how the top (root) object of a graph is laid out in the serialized stream. </param>
      <param name="typeFormat">Determines how type descriptions are laid out in the serialized stream. </param>
    </member>
    <member name="M:System.Messaging.BinaryMessageFormatter.CanRead(System.Messaging.Message)">
      <summary>Determines whether the formatter can deserialize the contents of the message.</summary>
      <returns>true if the binary message formatter can deserialize the message; otherwise, false.</returns>
      <param name="message">The <see cref="T:System.Messaging.Message" /> to inspect. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="message" /> parameter is null. </exception>
    </member>
    <member name="M:System.Messaging.BinaryMessageFormatter.Clone">
      <summary>Creates an instance of the <see cref="T:System.Messaging.BinaryMessageFormatter" /> class whose read/write properties (the root object and type description formats) are the same as the current <see cref="T:System.Messaging.BinaryMessageFormatter" />.</summary>
      <returns>An object whose properties are identical to those of this <see cref="T:System.Messaging.BinaryMessageFormatter" />, but whose metadata does not specify it to be a formatter class instance.</returns>
    </member>
    <member name="M:System.Messaging.BinaryMessageFormatter.Read(System.Messaging.Message)">
      <summary>Reads the contents from the given message and creates an object that contains the deserialized message.</summary>
      <returns>The deserialized message.</returns>
      <param name="message">The <see cref="T:System.Messaging.Message" />, in binary format, to deserialize. </param>
      <exception cref="T:System.InvalidOperationException">The message's <see cref="P:System.Messaging.Message.BodyType" /> property does not indicate a binary object. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="message" /> parameter is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.BinaryMessageFormatter.TopObjectFormat">
      <summary>Gets or sets a value that defines how the top (root) object of a graph is deserialized with regards to finding and loading its assembly.</summary>
      <returns>One of the <see cref="T:System.Runtime.Serialization.Formatters.FormatterAssemblyStyle" /> values that defines the deserialization behavior.</returns>
    </member>
    <member name="P:System.Messaging.BinaryMessageFormatter.TypeFormat">
      <summary>Gets or sets a value that defines how type descriptions are laid out in the serialized stream.</summary>
      <returns>A <see cref="T:System.Runtime.Serialization.Formatters.FormatterTypeStyle" /> that defines the type description format.</returns>
    </member>
    <member name="M:System.Messaging.BinaryMessageFormatter.Write(System.Messaging.Message,System.Object)">
      <summary>Serializes an object into the body of the message.</summary>
      <param name="message">The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.Body" /> property will contain the serialized object. </param>
      <param name="obj">The object to be serialized into the message body. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="message" /> parameter is null. </exception>
    </member>
    <member name="T:System.Messaging.CryptographicProviderType">
      <summary>Specifies the cryptographic service providers available for validating digital signatures.</summary>
    </member>
    <member name="F:System.Messaging.CryptographicProviderType.None">
      <summary>No cryptographic provider type specified.</summary>
    </member>
    <member name="F:System.Messaging.CryptographicProviderType.RsaFull">
      <summary>The full RSA provider type, which supports both digital signatures and data encryption. Considered a general purpose cryptographic services provider. The RSA public-key algorithm is used for all public-key operations.</summary>
    </member>
    <member name="F:System.Messaging.CryptographicProviderType.RsqSig">
      <summary>A subset of the RsaFull provider type, which supports only those functions and algorithms required for hashes and digital signatures.</summary>
    </member>
    <member name="F:System.Messaging.CryptographicProviderType.Dss">
      <summary>A provider type that, like RsqSig, only supports hashes and digital signatures. Dss specifies the Digital Signature Algorithm (DSA) signature algorithm.</summary>
    </member>
    <member name="F:System.Messaging.CryptographicProviderType.Fortezza">
      <summary>A provider type that contains a set of cryptographic protocols and algorithms owned by the National Institute of Standards and Technology.</summary>
    </member>
    <member name="F:System.Messaging.CryptographicProviderType.MicrosoftExchange">
      <summary>A provider type designed for the cryptographic needs of the Microsoft Exchange mail application and other applications compatible with Microsoft Mail.</summary>
    </member>
    <member name="F:System.Messaging.CryptographicProviderType.Ssl">
      <summary>A provider type that supports the Secure Sockets Layer (SSL) protocol.</summary>
    </member>
    <member name="F:System.Messaging.CryptographicProviderType.SttMer">
      <summary>Secure transaction technology provider enterprise.</summary>
    </member>
    <member name="F:System.Messaging.CryptographicProviderType.SttAcq">
      <summary>Secure transaction technology provider acquirer.</summary>
    </member>
    <member name="F:System.Messaging.CryptographicProviderType.SttBrnd">
      <summary>Secure transaction technology provider brand.</summary>
    </member>
    <member name="F:System.Messaging.CryptographicProviderType.SttRoot">
      <summary>Secure transaction technology provider root.</summary>
    </member>
    <member name="F:System.Messaging.CryptographicProviderType.SttIss">
      <summary>Secure transaction technology provider issuer.</summary>
    </member>
    <member name="T:System.Messaging.Cursor">
      <summary>A <see cref="T:System.Messaging.Cursor" /> is used to maintain a specific location in a <see cref="T:System.Messaging.MessageQueue" /> when reading the queue's messages.</summary>
    </member>
    <member name="M:System.Messaging.Cursor.Close">
      <summary>Closes the cursor, allowing Message Queuing to release the associated resources.</summary>
    </member>
    <member name="M:System.Messaging.Cursor.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Messaging.Cursor" />.</summary>
    </member>
    <member name="T:System.Messaging.DefaultPropertiesToSend">
      <summary>Specifies the default property values that will be used when sending objects other than <see cref="T:System.Messaging.Message" /> instances to a message queue.</summary>
    </member>
    <member name="M:System.Messaging.DefaultPropertiesToSend.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.DefaultPropertiesToSend" /> class.</summary>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.AcknowledgeType">
      <summary>Gets or sets the type of acknowledgement message to be returned to the sending application.</summary>
      <returns>One of the <see cref="T:System.Messaging.AcknowledgeTypes" /> enumeration values. This value is used to determine the type of acknowledgment messages the system posts in the administration queue and when acknowledgments are returned to the sending application. The default is AcknowledgeTypes.None.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.AdministrationQueue">
      <summary>Gets or sets the queue that receives acknowledgement messages generated by Message Queuing.</summary>
      <returns>The <see cref="T:System.Messaging.MessageQueue" /> that specifies the administration queue used for system-generated acknowledgment messages. The default is null.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.AppSpecific">
      <summary>Gets or sets additional, application-specific information.</summary>
      <returns>Information specific to the application. The default is 0.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.AttachSenderId">
      <summary>Gets or sets a value that indicates whether the sender ID should be attached to the message.</summary>
      <returns>true if sender ID should be attached to the message; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.EncryptionAlgorithm">
      <summary>Gets or sets the encryption algorithm used to encrypt the body of a private message.</summary>
      <returns>One of the <see cref="T:System.Messaging.EncryptionAlgorithm" /> enumeration values. The default is RC2.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.Extension">
      <summary>Gets or sets additional information associated with the message.</summary>
      <returns>An array of bytes that provides additional, application-defined information associated with the message. The default is a zero-length array.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.HashAlgorithm">
      <summary>Gets or sets the hashing algorithm used when authenticating messages or creating a digital signature for a message.</summary>
      <returns>One of the <see cref="T:System.Messaging.HashAlgorithm" /> enumeration values. The default is MD5.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.Label">
      <summary>Gets or sets an application-defined string that describes the message.</summary>
      <returns>The label of the message. The default is an empty string ("").</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.Priority">
      <summary>Gets or sets the message priority, which is used to determine where the message is placed in the queue.</summary>
      <returns>One of the <see cref="T:System.Messaging.MessagePriority" /> enumeration values that represents the priority level of a non-transactional message. The default is Normal.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.Recoverable">
      <summary>Gets or sets a value that indicates whether the message is guaranteed to be delivered in the event of a computer failure or network problem.</summary>
      <returns>true if the message is guaranteed delivery by saving it to disk while en route; false if delivery is not assured. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.ResponseQueue">
      <summary>Gets or sets the queue that receives application-generated response messages.</summary>
      <returns>The <see cref="T:System.Messaging.MessageQueue" /> to which application-generated response messages are returned. The default is null.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.TimeToBeReceived">
      <summary>Gets or sets the time limit for the message to be retrieved from the destination queue.</summary>
      <returns>The total time, in seconds, for a sent message to be received from the destination queue. The default is <see cref="F:System.Messaging.Message.InfiniteTimeout" />.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.TimeToReachQueue">
      <summary>Gets or sets the time limit for the message to reach the queue.</summary>
      <returns>The time limit in seconds for a message to reach the destination queue, from the time the message is sent. The default is <see cref="F:System.Messaging.Message.InfiniteTimeout" />.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.TransactionStatusQueue">
      <summary>Gets the transaction status queue on the source computer.</summary>
      <returns>The transaction status queue on the source computer, which is used for sending acknowledgement messages back to the sending application. The default is null.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.UseAuthentication">
      <summary>Gets or sets a value that indicates whether the message must be authenticated before being sent.</summary>
      <returns>true if the sending application requested authentication for the message; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.UseDeadLetterQueue">
      <summary>Gets or sets a value that indicates whether a copy of the message that could not be delivered should be sent to a dead-letter queue.</summary>
      <returns>true if message delivery failure should result in a copy of the message being sent to a dead-letter queue; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.UseEncryption">
      <summary>Gets or sets a value that indicates whether to make the message private.</summary>
      <returns>true to require Message Queuing to encrypt the message; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.UseJournalQueue">
      <summary>Gets or sets a value that indicates whether a copy of the message should be kept in a machine journal on the originating computer.</summary>
      <returns>true to require that a copy of a message be kept in the originating computer's machine journal after it has been successfully transmitted from the originating computer to the next step; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.DefaultPropertiesToSend.UseTracing">
      <summary>Gets or sets a value that indicates whether to trace a message as it moves toward its destination queue.</summary>
      <returns>true if each intermediate step made by the original message en route to the destination queue will generate a report to be sent to the system's report queue; otherwise, false. The default is false.</returns>
    </member>
    <member name="T:System.Messaging.EncryptionAlgorithm">
      <summary>Specifies the encryption algorithm used to encrypt the message body of a private message.</summary>
    </member>
    <member name="F:System.Messaging.EncryptionAlgorithm.None">
      <summary>No encryption.</summary>
    </member>
    <member name="F:System.Messaging.EncryptionAlgorithm.Rc2">
      <summary>The value MQMSG_CALG_RC2. This is the default value for the encryption property of the Message Queuing application's message object.</summary>
    </member>
    <member name="F:System.Messaging.EncryptionAlgorithm.Rc4">
      <summary>The value MQMSG_CALG_RC4. This corresponds to the less secure option for the encryption property of the Message Queuing application's message object.</summary>
    </member>
    <member name="T:System.Messaging.EncryptionRequired">
      <summary>Specifies the privacy level of messages received by the queue.</summary>
    </member>
    <member name="F:System.Messaging.EncryptionRequired.None">
      <summary>Accepts only non-private (non-encrypted) messages.</summary>
    </member>
    <member name="F:System.Messaging.EncryptionRequired.Optional">
      <summary>Does not force privacy. Accepts private (encrypted) messages and non-private (non-encrypted) messages.</summary>
    </member>
    <member name="F:System.Messaging.EncryptionRequired.Body">
      <summary>Accepts only private (encrypted) messages.</summary>
    </member>
    <member name="T:System.Messaging.GenericAccessRights">
      <summary>Uses the Windows 2000 and Windows NT access format to specify a set of common access rights that Message Queuing maps to both standard and object-specific access rights for reading, writing, and executing.</summary>
    </member>
    <member name="F:System.Messaging.GenericAccessRights.All">
      <summary>Read, write, and execute access.</summary>
    </member>
    <member name="F:System.Messaging.GenericAccessRights.Execute">
      <summary>Execute access.</summary>
    </member>
    <member name="F:System.Messaging.GenericAccessRights.Write">
      <summary>Write access.</summary>
    </member>
    <member name="F:System.Messaging.GenericAccessRights.Read">
      <summary>Read access.</summary>
    </member>
    <member name="F:System.Messaging.GenericAccessRights.None">
      <summary>No access.</summary>
    </member>
    <member name="T:System.Messaging.HashAlgorithm">
      <summary>Specifies the hash algorithm used by Message Queuing when authenticating messages.</summary>
    </member>
    <member name="F:System.Messaging.HashAlgorithm.None">
      <summary>No hashing algorithm.</summary>
    </member>
    <member name="F:System.Messaging.HashAlgorithm.Md2">
      <summary>MD2 hashing algorithm.</summary>
    </member>
    <member name="F:System.Messaging.HashAlgorithm.Md4">
      <summary>MD4 hashing algorithm.</summary>
    </member>
    <member name="F:System.Messaging.HashAlgorithm.Md5">
      <summary>MD5 hashing algorithm.</summary>
    </member>
    <member name="F:System.Messaging.HashAlgorithm.Sha">
      <summary>SHA hashing algorithm.</summary>
    </member>
    <member name="F:System.Messaging.HashAlgorithm.Mac">
      <summary>MAC keyed-hash algorithm.</summary>
    </member>
    <member name="T:System.Messaging.IMessageFormatter">
      <summary>Serializes or deserializes objects from the body of a Message Queuing message.</summary>
    </member>
    <member name="M:System.Messaging.IMessageFormatter.CanRead(System.Messaging.Message)">
      <summary>When implemented in a class, determines whether the formatter can deserialize the contents of the message.</summary>
      <returns>true if the formatter can deserialize the message; otherwise, false.</returns>
      <param name="message">The <see cref="T:System.Messaging.Message" /> to inspect. </param>
    </member>
    <member name="M:System.Messaging.IMessageFormatter.Read(System.Messaging.Message)">
      <summary>When implemented in a class, reads the contents from the given message and creates an object that contains data from the message.</summary>
      <returns>The deserialized message.</returns>
      <param name="message">The <see cref="T:System.Messaging.Message" /> to deserialize. </param>
    </member>
    <member name="M:System.Messaging.IMessageFormatter.Write(System.Messaging.Message,System.Object)">
      <summary>When implemented in a class, serializes an object into the body of the message.</summary>
      <param name="message">The <see cref="T:System.Messaging.Message" /> that will contain the serialized object. </param>
      <param name="obj">The object to be serialized into the message. </param>
    </member>
    <member name="T:System.Messaging.Message">
      <summary>Provides access to the properties needed to define a Message Queuing message.</summary>
    </member>
    <member name="M:System.Messaging.Message.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.Message" /> class with an empty body.</summary>
    </member>
    <member name="M:System.Messaging.Message.#ctor(System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.Message" /> class, using the <see cref="T:System.Messaging.XmlMessageFormatter" /> to serialize the specified object into the body of the message.</summary>
      <param name="body">The object to be serialized into the body of the message. </param>
    </member>
    <member name="M:System.Messaging.Message.#ctor(System.Object,System.Messaging.IMessageFormatter)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.Message" /> class using the specified formatter to serialize the specified object into the body of the message.</summary>
      <param name="body">The object to be serialized into the body of the message. </param>
      <param name="formatter">A <see cref="T:System.Messaging.IMessageFormatter" /> that specifies the formatter with which to serialize the message body. </param>
    </member>
    <member name="P:System.Messaging.Message.AcknowledgeType">
      <summary>Gets or sets the type of acknowledgment message to be returned to the sending application.</summary>
      <returns>One of the <see cref="T:System.Messaging.AcknowledgeTypes" /> values, which represent both the types of acknowledgment messages the system posts in the administration queue and the conditions under which acknowledgments are returned to the sending application. The default is None.</returns>
      <exception cref="T:System.InvalidOperationException">The message is filtered to ignore the <see cref="P:System.Messaging.Message.AcknowledgeType" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.Acknowledgment">
      <summary>Gets the classification of acknowledgment that this message represents.</summary>
      <returns>One of the <see cref="T:System.Messaging.Acknowledgment" /> enumeration values.</returns>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or-The message queue is filtered to ignore the <see cref="P:System.Messaging.MessagePropertyFilter.Acknowledgment" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.AdministrationQueue">
      <summary>Gets or sets the queue that receives the acknowledgement messages that Message Queuing generates.</summary>
      <returns>The <see cref="T:System.Messaging.MessageQueue" /> that specifies the administration queue used for system-generated acknowledgment messages. The default is null.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.AdministrationQueue" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.AppSpecific">
      <summary>Gets or sets additional, application-specific information.</summary>
      <returns>Information that is specific to the application. The default is zero.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.AppSpecific" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.ArrivedTime">
      <summary>Gets the time that the message arrived in the destination queue.</summary>
      <returns>A <see cref="T:System.DateTime" /> that represents the message's arrival time in the destination queue. The time is adjusted from GMT to the local time of the computer on which the destination queue resides.</returns>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or-The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.ArrivedTime" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.AttachSenderId">
      <summary>Gets or sets a value that indicates whether the sender ID should be attached to the message.</summary>
      <returns>true if the <see cref="P:System.Messaging.Message.SenderId" /> should be attached to the message; otherwise, false. The default is true.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.AttachSenderId" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.Authenticated">
      <summary>Gets a value that indicates whether the message was authenticated.</summary>
      <returns>true if authentication was requested for the message when it entered the queue; otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.Authenticated" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.AuthenticationProviderName">
      <summary>Gets or sets the name of the cryptographic provider used to generate the digital signature of the message.</summary>
      <returns>The name of the cryptographic provider used to generate the digital signature of the message. The default is Microsoft Base Cryptographic Provider version 1.0.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Messaging.Message.AuthenticationProviderName" /> property could not be set.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.AuthenticationProviderName" /> property. </exception>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Messaging.Message.AuthenticationProviderName" /> was set to null. </exception>
    </member>
    <member name="P:System.Messaging.Message.AuthenticationProviderType">
      <summary>Gets or sets the type of cryptographic provider used to generate the digital signature of the message.</summary>
      <returns>One of the <see cref="T:System.Messaging.CryptographicProviderType" /> values. The default is RSA_FULL.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Messaging.Message.AuthenticationProviderType" /> property could not be set.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.AuthenticationProviderType" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.Body">
      <summary>Gets or sets the content of the message.</summary>
      <returns>An object that specifies the message contents. The object can be a string, a date, a currency, a number, an array of bytes, or any managed object.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Messaging.Message.Formatter" /> property is null.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.Body" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.BodyStream">
      <summary>Gets or sets the information in the body of the message.</summary>
      <returns>A <see cref="T:System.IO.Stream" /> that contains the serialized information included in the <see cref="P:System.Messaging.Message.Body" /> of the message.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.Body" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.BodyType">
      <summary>Gets or sets the type of data that the message body contains.</summary>
      <returns>The message body's true type, such as a string, a date, a currency, or a number.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.Body" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.ConnectorType">
      <summary>Gets or sets a value that indicates that some message properties typically set by Message Queuing were set by the sending application.</summary>
      <returns>A <see cref="T:System.Guid" /> defined by the application and used in conjunction with connector applications or message encryption. This <see cref="T:System.Guid" /> allows a receiving application to interpret message properties that were set by the sending application but that are usually set by Message Queuing.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.ConnectorType" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.CorrelationId">
      <summary>Gets or sets the message identifier used by acknowledgment, report, and response messages to reference the original message.</summary>
      <returns>The message identifier specified by the <see cref="P:System.Messaging.Message.Id" /> property of the original message. The correlation identifier is used by Message Queuing when it generates an acknowledgment or report message, and by an application when it generates a response message.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.CorrelationId" /> property. </exception>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Messaging.Message.CorrelationId" /> is null. </exception>
    </member>
    <member name="P:System.Messaging.Message.DestinationQueue">
      <summary>Gets the intended destination queue for a message.</summary>
      <returns>A <see cref="T:System.Messaging.MessageQueue" /> that specifies the intended destination queue for the message.</returns>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.DestinationQueue" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.DestinationSymmetricKey">
      <summary>Gets or sets the symmetric key used to encrypt application-encrypted messages or messages sent to foreign queues.</summary>
      <returns>An array of byte values that specifies the destination symmetric key used to encrypt the message. The default is a zero-length array.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.DestinationSymmetricKey" /> property. </exception>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Messaging.Message.DestinationSymmetricKey" /> is null. </exception>
    </member>
    <member name="P:System.Messaging.Message.DigitalSignature">
      <summary>Gets or sets the digital signature that Message Queuing uses to authenticate the message.</summary>
      <returns>An array of byte values that specifies the Message Queuing 1.0 digital signature used to authenticate the message. The default is a zero-length array.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.DigitalSignature" /> property. </exception>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Messaging.Message.DigitalSignature" /> property is null. </exception>
    </member>
    <member name="P:System.Messaging.Message.EncryptionAlgorithm">
      <summary>Gets or sets the encryption algorithm used to encrypt the body of a private message.</summary>
      <returns>One of the <see cref="T:System.Messaging.EncryptionAlgorithm" /> enumeration values. The default is RC2.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.EncryptionAlgorithm" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.Extension">
      <summary>Gets or sets additional, application-defined information associated with the message.</summary>
      <returns>An array of byte values that provides application-defined information associated with the message. The default is a zero-length array.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.Extension" /> property. </exception>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Messaging.Message.Extension" /> property is null. </exception>
    </member>
    <member name="P:System.Messaging.Message.Formatter">
      <summary>Gets or sets the formatter used to serialize an object into or deserialize an object from the message body.</summary>
      <returns>The <see cref="T:System.Messaging.IMessageFormatter" /> that produces a stream to be written to or read from the message body. The default is <see cref="T:System.Messaging.XmlMessageFormatter" />.</returns>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Messaging.Message.Formatter" /> property is null. </exception>
    </member>
    <member name="P:System.Messaging.Message.HashAlgorithm">
      <summary>Gets or sets the hashing algorithm that Message Queuing uses when authenticating a message or creating a digital signature for a message.</summary>
      <returns>One of the <see cref="T:System.Messaging.HashAlgorithm" /> enumeration values. For Windows XP, the default is SHA. Otherwise, the default is MD5.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.HashAlgorithm" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.Id">
      <summary>Gets the message's identifier.</summary>
      <returns>The message's unique identifier, which is generated by Message Queuing.</returns>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.Id" /> property. </exception>
    </member>
    <member name="F:System.Messaging.Message.InfiniteTimeout">
      <summary>Specifies that no time-out exists.</summary>
    </member>
    <member name="P:System.Messaging.Message.IsFirstInTransaction">
      <summary>Gets a value that indicates whether the message was the first message sent in a transaction.</summary>
      <returns>true if the message was the first message sent in a transaction; otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.IsFirstInTransaction" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.IsLastInTransaction">
      <summary>Gets a value that indicates whether the message was the last message sent in a transaction.</summary>
      <returns>true if the message was the last message sent in a single transaction; otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.IsLastInTransaction" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.Label">
      <summary>Gets or sets an application-defined Unicode string that describes the message.</summary>
      <returns>The label of the message. The default is an empty string ("").</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.Label" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.LookupId">
      <summary>Introduced in MSMQ 3.0. Gets the message's lookup identifier.</summary>
      <returns>The message's lookup identifier, which is generated by Message Queuing and is unique to the queue where the message resides.</returns>
      <exception cref="T:System.PlatformNotSupportedException">MSMQ 3.0 is not installed.</exception>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.LookupId" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.MessageType">
      <summary>Gets the message type: Normal, Acknowledgment, or Report.</summary>
      <returns>One of the <see cref="P:System.Messaging.Message.MessageType" /> values.</returns>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.MessageType" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.Priority">
      <summary>Gets or sets the message priority, which determines where in the queue the message is placed.</summary>
      <returns>One of the <see cref="T:System.Messaging.MessagePriority" /> values, which represent the priority levels of non-transactional messages. The default is Normal.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.Priority" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.Recoverable">
      <summary>Gets or sets a value that indicates whether the message is guaranteed to be delivered in the event of a computer failure or network problem.</summary>
      <returns>true if delivery of the message is guaranteed (through saving the message to disk while en route); false if delivery is not assured. The default is false.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.Recoverable" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.ResponseQueue">
      <summary>Gets or sets the queue that receives application-generated response messages.</summary>
      <returns>The <see cref="T:System.Messaging.MessageQueue" /> to which application-generated response messages are returned. The default is null.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.ResponseQueue" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.SecurityContext">
      <summary>Gets or sets the security context for a message.</summary>
      <returns>A <see cref="T:System.Messaging.SecurityContext" /> object that contains the security context for a message, if the property has previously been set; otherwise NULL.</returns>
    </member>
    <member name="P:System.Messaging.Message.SenderCertificate">
      <summary>Gets or sets the security certificate used to authenticate messages.</summary>
      <returns>An array of byte values that represents a security certificate, which Message Queuing uses to verify the sender of the message. The default is a zero-length array.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.SenderCertificate" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.SenderId">
      <summary>Gets the identifier of the sending user.</summary>
      <returns>An array of byte values that identifies the sender. The receiving Queue Manager uses the identifier when it authenticates the message to verify the sender of the message and the sender's access rights to the queue.</returns>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.SenderId" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.SenderVersion">
      <summary>Gets the version of Message Queuing used to send the message.</summary>
      <returns>The version of Message Queuing used to send the message.</returns>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.SenderVersion" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.SentTime">
      <summary>Gets the date and time on the sending computer that the message was sent by the source queue manager.</summary>
      <returns>A <see cref="T:System.DateTime" /> that represents the time the message was sent.</returns>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.SentTime" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.SourceMachine">
      <summary>Gets the computer from which the message originated.</summary>
      <returns>The name of the computer from which the message was sent.</returns>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.SourceMachine" /> property. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The computer information or directory service could not be accessed. </exception>
    </member>
    <member name="P:System.Messaging.Message.TimeToBeReceived">
      <summary>Gets or sets the maximum amount of time for the message to be received from the destination queue.</summary>
      <returns>The total time for a sent message to be received from the destination queue. The default is <see cref="F:System.Messaging.Message.InfiniteTimeout" />.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.TimeToBeReceived" /> property. </exception>
      <exception cref="T:System.ArgumentException">The value specified for <see cref="P:System.Messaging.Message.TimeToBeReceived" /> is invalid. </exception>
    </member>
    <member name="P:System.Messaging.Message.TimeToReachQueue">
      <summary>Gets or sets the maximum amount of time for the message to reach the queue.</summary>
      <returns>The time limit for the message to reach the destination queue, beginning from the time the message is sent. The default is <see cref="F:System.Messaging.Message.InfiniteTimeout" />.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.TimeToReachQueue" /> property. </exception>
      <exception cref="T:System.ArgumentException">The value specified for <see cref="P:System.Messaging.Message.TimeToReachQueue" /> is invalid. It might represent a negative number. </exception>
    </member>
    <member name="P:System.Messaging.Message.TransactionId">
      <summary>Gets the identifier for the transaction of which the message was a part.</summary>
      <returns>The identifier for the transaction associated with the message.</returns>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.TransactionId" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.TransactionStatusQueue">
      <summary>Gets the transaction status queue on the source computer.</summary>
      <returns>The transaction status queue on the source computer, which is used for sending acknowledgement messages back to the sending application. The default is null.</returns>
      <exception cref="T:System.InvalidOperationException">The message has not been sent. This property can only be read on messages retrieved from a queue.-or- The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.TransactionStatusQueue" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.UseAuthentication">
      <summary>Gets or sets a value that indicates whether the message was (or must be) authenticated before being sent.</summary>
      <returns>true if the sending application requested authentication for the message; otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.UseAuthentication" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.UseDeadLetterQueue">
      <summary>Gets or sets a value that indicates whether a copy of the message that could not be delivered should be sent to a dead-letter queue.</summary>
      <returns>true if message-delivery failure should result in a copy of the message being sent to a dead-letter queue; otherwise, false. The default is false.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.UseDeadLetterQueue" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.UseEncryption">
      <summary>Gets or sets a value that indicates whether to make the message private.</summary>
      <returns>true to require Message Queuing to encrypt the message; otherwise, false. The default is false.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.UseEncryption" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.UseJournalQueue">
      <summary>Gets or sets a value that indicates whether a copy of the message should be kept in a machine journal on the originating computer.</summary>
      <returns>true to require that a copy of a message be kept in the originating computer's machine journal after the message has been successfully transmitted (from the originating computer to the next server); otherwise, false. The default is false.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.UseJournalQueue" /> property. </exception>
    </member>
    <member name="P:System.Messaging.Message.UseTracing">
      <summary>Gets or sets a value that indicates whether to trace a message as it moves toward its destination queue.</summary>
      <returns>true if each intermediate step made by the original message en route to the destination queue generates a report to be sent to the system's report queue; otherwise, false. The default is false.</returns>
      <exception cref="T:System.InvalidOperationException">The message queue is filtered to ignore the <see cref="P:System.Messaging.Message.UseTracing" /> property. </exception>
    </member>
    <member name="T:System.Messaging.MessageEnumerator">
      <summary>Provides a forward-only cursor to enumerate through messages in a message queue.</summary>
    </member>
    <member name="M:System.Messaging.MessageEnumerator.Close">
      <summary>Frees the resources associated with the enumerator.</summary>
    </member>
    <member name="P:System.Messaging.MessageEnumerator.Current">
      <summary>Gets the current <see cref="T:System.Messaging.Message" /> that this enumerator points to.</summary>
      <returns>The current message.</returns>
      <exception cref="T:System.InvalidOperationException">You called <see cref="P:System.Messaging.MessageEnumerator.Current" /> before the first call to <see cref="M:System.Messaging.MessageEnumerator.MoveNext" />. The cursor is located before the first element of the message enumeration.-or- You called <see cref="P:System.Messaging.MessageEnumerator.Current" /> after a call to <see cref="M:System.Messaging.MessageEnumerator.MoveNext" /> had returned false (indicating the cursor is located after the last element of the message enumeration.) </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The message the enumerator is currently pointing to no longer exists. It might have been deleted. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageEnumerator.CursorHandle">
      <summary>Gets the native Message Queuing cursor handle used to browse messages in the queue.</summary>
      <returns>The native cursor handle.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">The handle does not exist. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageEnumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Messaging.MessageEnumerator" />.</summary>
    </member>
    <member name="M:System.Messaging.MessageEnumerator.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Messaging.MessageEnumerator" /> and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.Messaging.MessageEnumerator.Finalize">
      <summary>Releases the resources held by the enumerator.</summary>
    </member>
    <member name="M:System.Messaging.MessageEnumerator.MoveNext">
      <summary>Advances the enumerator to the next message in the queue, if one is currently available.</summary>
      <returns>true if the enumerator was succesfully advanced to the next message; false if the enumerator has reached the end of the queue.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An exception specific to Message Queuing was thrown. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageEnumerator.MoveNext(System.TimeSpan)">
      <summary>Advances the enumerator to the next message in the queue. If the enumerator is positioned at the end of the queue, <see cref="M:System.Messaging.MessageEnumerator.MoveNext" /> waits until a message is available or the given timeout expires.</summary>
      <returns>true if the enumerator successfully advanced to the next message; false if the enumerator has reached the end of the queue and a message does not become available within the time specified by the <paramref name="timeout" /> parameter.</returns>
      <param name="timeout">The <see cref="T:System.TimeSpan" /> to wait for a message to be available if the enumerator is positioned at the end of the queue. </param>
      <exception cref="T:System.ArgumentException">The value specified for the timeout parameter is invalid. It might represent a negative number. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An exception specific to Message Queuing was thrown.-or- The timeout has expired. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageEnumerator.RemoveCurrent">
      <summary>Removes the current message from a transactional or non-transactional queue and returns the message to the calling application. There is no timeout specified for a message to arrive in the queue.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references the first message available in the queue.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageEnumerator.RemoveCurrent(System.Messaging.MessageQueueTransaction)">
      <summary>Removes the current message from a transactional queue and returns the message to the calling application. There is no timeout specified for a message to arrive in the queue.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references the first message available in the queue.</returns>
      <param name="transaction">The <see cref="T:System.Messaging.MessageQueueTransaction" /> object that specifies the transaction in which the message will be removed. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="transaction" /> parameter is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageEnumerator.RemoveCurrent(System.Messaging.MessageQueueTransactionType)">
      <summary>Removes the current message from a queue and returns the message to the calling application. There is no timeout specified for a message to arrive in the queue.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references the first message available in the queue.</returns>
      <param name="transactionType">One of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> values, describing the type of transaction context to associate with the message. </param>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="transactionType" /> parameter is not one of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> members. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageEnumerator.RemoveCurrent(System.TimeSpan)">
      <summary>Removes the current message from the queue and returns the message to the calling application. If there is a message to remove, the method returns it immediately. Otherwise, the method waits the specified timeout for a new message to arrive.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references the first message available in the queue.</returns>
      <param name="timeout">The interval of time to wait for a message to arrive in the queue. </param>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is invalid. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The timeout has expired. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageEnumerator.RemoveCurrent(System.TimeSpan,System.Messaging.MessageQueueTransaction)">
      <summary>Removes the current message from a transactional queue and returns the message to the calling application. If there is a message to remove, the method returns it immediately. Otherwise, the method waits the specified timeout for a new message to arrive.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references the first message available in the queue.</returns>
      <param name="timeout">The interval of time to wait for the message to be removed. </param>
      <param name="transaction">The <see cref="T:System.Messaging.MessageQueueTransaction" /> object that specifies the transaction context for the message. </param>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is invalid. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="transaction" /> parameter is null. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The timeout has expired. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageEnumerator.RemoveCurrent(System.TimeSpan,System.Messaging.MessageQueueTransactionType)">
      <summary>Removes the current message from a queue and returns the message to the calling application. If there is a message to remove, the method returns it immediately. Otherwise, the method waits the specified timeout for a new message to arrive.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references the first message available in the queue.</returns>
      <param name="timeout">The interval of time to wait for the message to be removed. </param>
      <param name="transactionType">One of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> values, describing the type of transaction context to associate with the message. </param>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is invalid. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The timeout has expired. </exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="transactionType" /> parameter is not one of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> members. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageEnumerator.Reset">
      <summary>Resets the current enumerator so it points to the head of the queue.</summary>
    </member>
    <member name="P:System.Messaging.MessageEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the current <see cref="T:System.Messaging.Message" /> that this enumerator points to.</summary>
      <returns>The current message.</returns>
      <exception cref="T:System.InvalidOperationException">You accessed this property before the first call to <see cref="M:System.Messaging.MessageEnumerator.MoveNext" />. The cursor is located before the first element of the message enumeration.-or- You accessed this property after a call to <see cref="M:System.Messaging.MessageEnumerator.MoveNext" /> had returned false (indicating the cursor is located after the last element of the message enumeration.) </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The message the enumerator is currently pointing to no longer exists. It might have been deleted. </exception>
    </member>
    <member name="T:System.Messaging.MessageLookupAction">
      <summary>Specifies a message to peek at or receive from a message queue.</summary>
    </member>
    <member name="F:System.Messaging.MessageLookupAction.Current">
      <summary>Read the current message.</summary>
    </member>
    <member name="F:System.Messaging.MessageLookupAction.Next">
      <summary>Read the following message.</summary>
    </member>
    <member name="F:System.Messaging.MessageLookupAction.Previous">
      <summary>Read the preceding message.</summary>
    </member>
    <member name="F:System.Messaging.MessageLookupAction.First">
      <summary>Read the first message in the queue.</summary>
    </member>
    <member name="F:System.Messaging.MessageLookupAction.Last">
      <summary>Read the last message in the queue.</summary>
    </member>
    <member name="T:System.Messaging.MessagePriority">
      <summary>Specifies the priority Message Queuing applies to a message while it is en route to a queue, and when inserting the message into the destination queue.</summary>
    </member>
    <member name="F:System.Messaging.MessagePriority.Lowest">
      <summary>Lowest message priority.</summary>
    </member>
    <member name="F:System.Messaging.MessagePriority.VeryLow">
      <summary>Between Low and Lowest message priority.</summary>
    </member>
    <member name="F:System.Messaging.MessagePriority.Low">
      <summary>Low message priority.</summary>
    </member>
    <member name="F:System.Messaging.MessagePriority.Normal">
      <summary>Normal message priority.</summary>
    </member>
    <member name="F:System.Messaging.MessagePriority.AboveNormal">
      <summary>Between <see cref="F:System.Messaging.MessagePriority.High" /> and <see cref="F:System.Messaging.MessagePriority.Normal" /> message priority.</summary>
    </member>
    <member name="F:System.Messaging.MessagePriority.High">
      <summary>High message priority.</summary>
    </member>
    <member name="F:System.Messaging.MessagePriority.VeryHigh">
      <summary>Between Highest and High message priority.</summary>
    </member>
    <member name="F:System.Messaging.MessagePriority.Highest">
      <summary>Highest message priority.</summary>
    </member>
    <member name="T:System.Messaging.MessagePropertyFilter">
      <summary>Controls and selects the properties that are retrieved when peeking or receiving messages from a message queue.</summary>
    </member>
    <member name="M:System.Messaging.MessagePropertyFilter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessagePropertyFilter" /> class and sets default values for all properties.</summary>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.AcknowledgeType">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.AcknowledgeType" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.AcknowledgeType" /> information; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.Acknowledgment">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.Acknowledgment" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.Acknowledgment" /> information; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.AdministrationQueue">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.AdministrationQueue" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.AdministrationQueue" /> information; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.AppSpecific">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.AppSpecific" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.AppSpecific" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.ArrivedTime">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.ArrivedTime" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.ArrivedTime" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.AttachSenderId">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.AttachSenderId" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.AttachSenderId" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.Authenticated">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.Authenticated" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.Authenticated" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.AuthenticationProviderName">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.AuthenticationProviderName" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.AuthenticationProviderName" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.AuthenticationProviderType">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.AuthenticationProviderType" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.AuthenticationProviderType" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.Body">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.Body" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.Body" /> information; otherwise, false. The default is true.</returns>
    </member>
    <member name="M:System.Messaging.MessagePropertyFilter.ClearAll">
      <summary>Sets all Boolean filter values to false, so that no message properties are retrieved when receiving a message.</summary>
    </member>
    <member name="M:System.Messaging.MessagePropertyFilter.Clone">
      <summary>Creates a shallow copy of the object.</summary>
      <returns>A <see cref="T:System.Object" /> that represents an instance of the <see cref="T:System.Messaging.MessagePropertyFilter" /> class.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.ConnectorType">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.ConnectorType" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.ConnectorType" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.CorrelationId">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.CorrelationId" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.CorrelationId" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.DefaultBodySize">
      <summary>Gets or sets the size, in bytes, of the default body buffer.</summary>
      <returns>The default body buffer size to create when the message is received. The default is 1024 bytes.</returns>
      <exception cref="T:System.ArgumentException">The assigned value is negative. </exception>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.DefaultExtensionSize">
      <summary>Gets or sets the size, in bytes, of the default extension buffer.</summary>
      <returns>The default extension buffer size to create when the message is received. The default is 255 bytes.</returns>
      <exception cref="T:System.ArgumentException">The assigned value is negative. </exception>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.DefaultLabelSize">
      <summary>Gets or sets the size, in bytes, of the default label buffer.</summary>
      <returns>The default label buffer size to create when the message is received. The default is 255 bytes.</returns>
      <exception cref="T:System.ArgumentException">The assigned value is negative. </exception>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.DestinationQueue">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.DestinationQueue" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.DestinationQueue" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.DestinationSymmetricKey">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.DestinationSymmetricKey" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.DestinationSymmetricKey" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.DigitalSignature">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.DigitalSignature" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.DigitalSignature" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.EncryptionAlgorithm">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.EncryptionAlgorithm" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.EncryptionAlgorithm" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.Extension">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.Extension" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.Extension" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.HashAlgorithm">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.HashAlgorithm" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.HashAlgorithm" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.Id">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.Id" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.Id" /> information; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.IsFirstInTransaction">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.IsFirstInTransaction" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.IsFirstInTransaction" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.IsLastInTransaction">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.IsLastInTransaction" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.IsLastInTransaction" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.Label">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.Label" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.Label" /> information; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.LookupId">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.LookupId" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.LookupId" /> property information; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.MessageType">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.MessageType" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.MessageType" /> information; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.Priority">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.Priority" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.Priority" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.Recoverable">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.Recoverable" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.Recoverable" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.ResponseQueue">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.ResponseQueue" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.ResponseQueue" /> information; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.SenderCertificate">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.SenderCertificate" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.SenderCertificate" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.SenderId">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.SenderId" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.SenderId" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.SenderVersion">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.SenderVersion" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.SenderVersion" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.SentTime">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.SentTime" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.SentTime" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="M:System.Messaging.MessagePropertyFilter.SetAll">
      <summary>Specifies to retrieve all message properties when receiving a message.</summary>
    </member>
    <member name="M:System.Messaging.MessagePropertyFilter.SetDefaults">
      <summary>Sets the filter values of common Message Queuing properties to true and the integer-valued properties to their default values.</summary>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.SourceMachine">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.SourceMachine" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.SourceMachine" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.TimeToBeReceived">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.TimeToBeReceived" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.TimeToBeReceived" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.TimeToReachQueue">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.TimeToReachQueue" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.TimeToReachQueue" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.TransactionId">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.TransactionId" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.TransactionId" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.TransactionStatusQueue">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.TransactionStatusQueue" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.TransactionStatusQueue" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.UseAuthentication">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.UseAuthentication" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.UseAuthentication" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.UseDeadLetterQueue">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.UseDeadLetterQueue" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.UseDeadLetterQueue" /> information; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.UseEncryption">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.UseEncryption" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.UseEncryption" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.UseJournalQueue">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.UseJournalQueue" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.UseJournalQueue" /> information; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.Messaging.MessagePropertyFilter.UseTracing">
      <summary>Gets or sets a value that indicates whether to retrieve <see cref="P:System.Messaging.Message.UseTracing" /> property information when receiving or peeking a message.</summary>
      <returns>true to receive <see cref="P:System.Messaging.Message.UseTracing" /> information; otherwise, false. The default is false.</returns>
    </member>
    <member name="T:System.Messaging.MessageQueue">
      <summary>Provides access to a queue on a Message Queuing server.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueue.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueue" /> class. After the default constructor initializes the new instance, you must set the instance's <see cref="P:System.Messaging.MessageQueue.Path" /> property before you can use the instance.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueue.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueue" /> class that references the Message Queuing queue at the specified path.</summary>
      <param name="path">The location of the queue referenced by this <see cref="T:System.Messaging.MessageQueue" />. </param>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Messaging.MessageQueue.Path" /> property is not valid, possibly because it has not been set. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.#ctor(System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueue" /> class that references the Message Queuing queue at the specified path and with the specified read-access restriction.</summary>
      <param name="path">The location of the queue referenced by this <see cref="T:System.Messaging.MessageQueue" />, which can be "." for the local computer. For information about the proper syntax for this parameter, see the Remarks section. </param>
      <param name="sharedModeDenyReceive">true to grant exclusive read access to the first application that accesses the queue; otherwise, false. </param>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Messaging.MessageQueue.Path" /> property is not valid, possibly because it has not been set. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.#ctor(System.String,System.Boolean,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueue" /> class.</summary>
      <param name="path">The location of the queue referenced by this <see cref="T:System.Messaging.MessageQueue" />, which can be "." for the local computer.</param>
      <param name="sharedModeDenyReceive">true to grant exclusive read access to the first application that accesses the queue; otherwise, false. </param>
      <param name="enableCache">true to create and use a connection cache; otherwise, false.</param>
    </member>
    <member name="M:System.Messaging.MessageQueue.#ctor(System.String,System.Boolean,System.Boolean,System.Messaging.QueueAccessMode)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueue" /> class.</summary>
      <param name="path">The location of the queue referenced by this <see cref="T:System.Messaging.MessageQueue" />, which can be "." for the local computer.</param>
      <param name="sharedModeDenyReceive">true to grant exclusive read access to the first application that accesses the queue; otherwise, false. </param>
      <param name="enableCache">true to create and use a connection cache; otherwise, false.</param>
      <param name="accessMode">One of the <see cref="T:System.Messaging.QueueAccessMode" /> values.</param>
    </member>
    <member name="M:System.Messaging.MessageQueue.#ctor(System.String,System.Messaging.QueueAccessMode)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueue" /> class.</summary>
      <param name="path">The location of the queue referenced by this <see cref="T:System.Messaging.MessageQueue" />, which can be "." for the local computer.</param>
      <param name="accessMode">One of the <see cref="T:System.Messaging.QueueAccessMode" /> values.</param>
    </member>
    <member name="P:System.Messaging.MessageQueue.AccessMode">
      <summary>Gets a value that indicates the access mode for the queue.</summary>
      <returns>One of the <see cref="T:System.Messaging.QueueAccessMode" /> values.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueue.Authenticate">
      <summary>Gets or sets a value that indicates whether the queue accepts only authenticated messages.</summary>
      <returns>true if the queue accepts only authenticated messages; otherwise, false. The default is false.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.BasePriority">
      <summary>Gets or sets the base priority Message Queuing uses to route a public queue's messages over the network.</summary>
      <returns>The single base priority for all messages sent to the (public) queue. The default is zero (0).</returns>
      <exception cref="T:System.ArgumentException">The base priority was set to an invalid value. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.BeginPeek">
      <summary>Initiates an asynchronous peek operation that has no time-out. The operation is not complete until a message becomes available in the queue.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> that identifies the posted asynchronous request.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.BeginPeek(System.TimeSpan)">
      <summary>Initiates an asynchronous peek operation that has a specified time-out. The operation is not complete until either a message becomes available in the queue or the time-out occurs.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> that identifies the posted asynchronous request.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the interval of time to wait for a message to become available. </param>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.BeginPeek(System.TimeSpan,System.Messaging.Cursor,System.Messaging.PeekAction,System.Object,System.AsyncCallback)">
      <summary>Initiates an asynchronous peek operation that has a specified time-out and that uses a specified cursor, a specified peek action, and a specified state object. The state object provides associated information throughout the lifetime of the operation. This overload receives notification, through a callback, of the identity of the event handler for the operation. The operation is not complete until either a message becomes available in the queue or the time-out occurs.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> that identifies the posted asynchronous request.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the interval of time to wait for a message to become available. </param>
      <param name="cursor">A <see cref="T:System.Messaging.Cursor" /> that maintains a specific position in the message queue.</param>
      <param name="action">One of the <see cref="T:System.Messaging.PeekAction" /> values. Indicates whether to peek at the current message in the queue, or the next message.</param>
      <param name="state">A state object, specified by the application, that contains information associated with the asynchronous operation. </param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> that receives the notification of the asynchronous operation completion. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">A value other than PeekAction.Current or PeekAction.Next was specified for the <paramref name="action" /> parameter.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="cursor" /> parameter is null.</exception>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.BeginPeek(System.TimeSpan,System.Object)">
      <summary>Initiates an asynchronous peek operation that has a specified time-out and a specified state object, which provides associated information throughout the operation's lifetime. The operation is not complete until either a message becomes available in the queue or the time-out occurs.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> that identifies the posted asynchronous request.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the interval of time to wait for a message to become available. </param>
      <param name="stateObject">A state object, specified by the application, that contains information associated with the asynchronous operation. </param>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.BeginPeek(System.TimeSpan,System.Object,System.AsyncCallback)">
      <summary>Initiates an asynchronous peek operation that has a specified time-out and a specified state object, which provides associated information throughout the operation's lifetime. This overload receives notification, through a callback, of the identity of the event handler for the operation. The operation is not complete until either a message becomes available in the queue or the time-out occurs.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> that identifies the posted asynchronous request.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the interval of time to wait for a message to become available. </param>
      <param name="stateObject">A state object, specified by the application, that contains information associated with the asynchronous operation. </param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> that will receive the notification of the asynchronous operation completion. </param>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.BeginReceive">
      <summary>Initiates an asynchronous receive operation that has no time-out. The operation is not complete until a message becomes available in the queue.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> that identifies the posted asynchronous request.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.BeginReceive(System.TimeSpan)">
      <summary>Initiates an asynchronous receive operation that has a specified time-out. The operation is not complete until either a message becomes available in the queue or the time-out occurs.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> that identifies the posted asynchronous request.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the interval of time to wait for a message to become available. </param>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid, possibly because it represents a negative number. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.BeginReceive(System.TimeSpan,System.Messaging.Cursor,System.Object,System.AsyncCallback)">
      <summary>Initiates an asynchronous receive operation that has a specified time-out and uses a specified cursor and a specified state object. The state object provides associated information throughout the lifetime of the operation. This overload receives notification, through a callback, of the identity of the event handler for the operation. The operation is not complete until either a message becomes available in the queue or the time-out occurs.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> that identifies the posted asynchronous request.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the interval of time to wait for a message to become available. </param>
      <param name="cursor">A <see cref="T:System.Messaging.Cursor" /> that maintains a specific position in the message queue.</param>
      <param name="state">A state object, specified by the application, that contains information associated with the asynchronous operation. </param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> that receives the notification of the asynchronous operation completion. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="cursor" /> parameter is null.</exception>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.BeginReceive(System.TimeSpan,System.Object)">
      <summary>Initiates an asynchronous receive operation that has a specified time-out and a specified state object, which provides associated information throughout the operation's lifetime. The operation is not complete until either a message becomes available in the queue or the time-out occurs.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> that identifies the posted asynchronous request.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the interval of time to wait for a message to become available. </param>
      <param name="stateObject">A state object, specified by the application, that contains information associated with the asynchronous operation. </param>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.BeginReceive(System.TimeSpan,System.Object,System.AsyncCallback)">
      <summary>Initiates an asynchronous receive operation that has a specified time-out and a specified state object, which provides associated information throughout the operation's lifetime. This overload receives notification, through a callback, of the identity of the event handler for the operation. The operation is not complete until either a message becomes available in the queue or the time-out occurs.</summary>
      <returns>The <see cref="T:System.IAsyncResult" /> that identifies the posted asynchronous request.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the interval of time to wait for a message to become available. </param>
      <param name="stateObject">A state object, specified by the application, that contains information associated with the asynchronous operation. </param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> that will receive the notification of the asynchronous operation completion. </param>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.CanRead">
      <summary>Gets a value that indicates whether the <see cref="T:System.Messaging.MessageQueue" /> can be read.</summary>
      <returns>true if the <see cref="T:System.Messaging.MessageQueue" /> exists and the application can read from it; otherwise, false.</returns>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.CanWrite">
      <summary>Gets a value that indicates whether the <see cref="T:System.Messaging.MessageQueue" /> can be written to.</summary>
      <returns>true if the <see cref="T:System.Messaging.MessageQueue" /> exists and the application can write to it; otherwise, false.</returns>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.Category">
      <summary>Gets or sets the queue category.</summary>
      <returns>A <see cref="T:System.Guid" /> that represents the queue category (Message Queuing type identifier), which allows an application to categorize its queues. The default is Guid.empty.</returns>
      <exception cref="T:System.ArgumentException">The queue category was set to an invalid value. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.ClearConnectionCache">
      <summary>Clears the connection cache.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueue.Close">
      <summary>Frees all resources allocated by the <see cref="T:System.Messaging.MessageQueue" />.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueue.Create(System.String)">
      <summary>Creates a non-transactional Message Queuing queue at the specified path.</summary>
      <returns>A <see cref="T:System.Messaging.MessageQueue" /> that represents the new queue.</returns>
      <param name="path">The path of the queue to create. </param>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> parameter is null or is an empty string (""). </exception>
      <exception cref="T:System.Messaging.MessageQueueException">A queue already exists at the specified path.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1">
          <Path value="*" access="Administer" />
        </IPermission>
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Create(System.String,System.Boolean)">
      <summary>Creates a transactional or non-transactional Message Queuing queue at the specified path.</summary>
      <returns>A <see cref="T:System.Messaging.MessageQueue" /> that represents the new queue.</returns>
      <param name="path">The path of the queue to create. </param>
      <param name="transactional">true to create a transactional queue; false to create a non-transactional queue. </param>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> parameter is null or is an empty string (""). </exception>
      <exception cref="T:System.Messaging.MessageQueueException">A queue already exists at the specified path.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1">
          <Path value="*" access="Administer" />
        </IPermission>
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.CreateCursor">
      <summary>Creates a new <see cref="T:System.Messaging.Cursor" /> for the current message queue.</summary>
      <returns>A new <see cref="T:System.Messaging.Cursor" /> for the current message queue. This cursor is used to maintain a specific location in the queue when reading the queue's messages.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueue.CreateTime">
      <summary>Gets the time and date that the queue was created in Message Queuing.</summary>
      <returns>A <see cref="T:System.DateTime" /> that represents the date and time at which the queue was created.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.DefaultPropertiesToSend">
      <summary>Gets or sets the message property values to be used by default when the application sends messages to the queue.</summary>
      <returns>A <see cref="T:System.Messaging.DefaultPropertiesToSend" /> that contains the default Message Queuing message property values used when the application sends objects other than <see cref="T:System.Messaging.Message" /> instances to the queue.</returns>
      <exception cref="T:System.ArgumentException">The default properties could not be set for the queue, possibly because one of the properties is not valid. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.Delete(System.String)">
      <summary>Deletes a queue on a Message Queuing server.</summary>
      <param name="path">The location of the queue to be deleted. </param>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> parameter is null or is an empty string (""). </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The syntax for the <paramref name="path" /> parameter is not valid.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.DenySharedReceive">
      <summary>Gets or sets a value that indicates whether this <see cref="T:System.Messaging.MessageQueue" /> has exclusive access to receive messages from the Message Queuing queue.</summary>
      <returns>true if this <see cref="T:System.Messaging.MessageQueue" /> has exclusive rights to receive messages from the queue; otherwise, false. The default is false.</returns>
    </member>
    <member name="M:System.Messaging.MessageQueue.Dispose(System.Boolean)">
      <summary>Disposes of the resources (other than memory) used by the <see cref="T:System.Messaging.MessageQueue" />.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="P:System.Messaging.MessageQueue.EnableConnectionCache">
      <summary>Gets or sets a value that indicates whether a cache of connections will be maintained by the application.</summary>
      <returns>true to create and use a connection cache; otherwise, false.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueue.EncryptionRequired">
      <summary>Gets or sets a value that indicates whether the queue accepts only non-private (non-encrypted) messages.</summary>
      <returns>One of the <see cref="T:System.Messaging.EncryptionRequired" /> values. The default is None.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.EndPeek(System.IAsyncResult)">
      <summary>Completes the specified asynchronous peek operation.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> associated with the completed asynchronous operation.</returns>
      <param name="asyncResult">The <see cref="T:System.IAsyncResult" /> that identifies the asynchronous peek operation to finish and from which to retrieve an end result. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="asyncResult" /> parameter is null. </exception>
      <exception cref="T:System.ArgumentException">The syntax of the <paramref name="asyncResult" /> parameter is not valid. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.EndReceive(System.IAsyncResult)">
      <summary>Completes the specified asynchronous receive operation.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> associated with the completed asynchronous operation.</returns>
      <param name="asyncResult">The <see cref="T:System.IAsyncResult" /> that identifies the asynchronous receive operation to finish and from which to retrieve an end result. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="asyncResult" /> parameter is null. </exception>
      <exception cref="T:System.ArgumentException">The syntax of the <paramref name="asyncResult" /> parameter is not valid. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.Exists(System.String)">
      <summary>Determines whether a Message Queuing queue exists at the specified path.</summary>
      <returns>true if a queue with the specified path exists; otherwise, false.</returns>
      <param name="path">The location of the queue to find. </param>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> syntax is not valid. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method.-or- The <see cref="M:System.Messaging.MessageQueue.Exists(System.String)" /> method is being called on a remote private queue </exception>
      <exception cref="T:System.InvalidOperationException">The application used format name syntax when verifying queue existence. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1">
          <Path value="*" access="Browse" />
        </IPermission>
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.FormatName">
      <summary>Gets the unique queue name that Message Queuing generated at the time of the queue's creation.</summary>
      <returns>The name for the queue, which is unique on the network.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">The <see cref="P:System.Messaging.MessageQueue.Path" /> is not set.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.Formatter">
      <summary>Gets or sets the formatter used to serialize an object into or deserialize an object from the body of a message read from or written to the queue.</summary>
      <returns>The <see cref="T:System.Messaging.IMessageFormatter" /> that produces a stream to be written to or read from the message body. The default is <see cref="T:System.Messaging.XmlMessageFormatter" />.</returns>
    </member>
    <member name="M:System.Messaging.MessageQueue.GetAllMessages">
      <summary>Returns all the messages that are in the queue.</summary>
      <returns>An array of type <see cref="T:System.Messaging.Message" /> that represents all the messages in the queue, in the same order as they appear in the Message Queuing queue.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.GetEnumerator">
      <summary>Enumerates the messages in a queue. <see cref="M:System.Messaging.MessageQueue.GetEnumerator" /> is deprecated. <see cref="M:System.Messaging.MessageQueue.GetMessageEnumerator2" /> should be used instead.</summary>
      <returns>A <see cref="T:System.Collections.IEnumerator" /> that provides a dynamic connection to the messages in the queue.</returns>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.GetMachineId(System.String)">
      <summary>Gets the identifier of the computer on which the queue referenced by this <see cref="T:System.Messaging.MessageQueue" /> is located.</summary>
      <returns>A <see cref="T:System.Guid" /> that represents a unique identifier for the computer on which the queue is located.</returns>
      <param name="machineName">The name of the computer that contains the queue, without the two preceding backslashes (\\). </param>
      <exception cref="T:System.Messaging.MessageQueueException">The computer identifier could not be retrieved, possibly because the directory service is not available; for example, if you are working offline.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1">
          <Path value="*" access="Browse" />
        </IPermission>
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.GetMessageEnumerator">
      <summary>Creates an enumerator object for all the messages in the queue. <see cref="M:System.Messaging.MessageQueue.GetMessageEnumerator" /> is deprecated. <see cref="M:System.Messaging.MessageQueue.GetMessageEnumerator2" /> should be used instead.</summary>
      <returns>The <see cref="T:System.Messaging.MessageEnumerator" /> holding the messages that are contained in the queue.</returns>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.GetMessageEnumerator2">
      <summary>Creates an enumerator object for all the messages in the queue.</summary>
      <returns>The <see cref="T:System.Messaging.MessageEnumerator" /> holding the messages that are contained in the queue.</returns>
    </member>
    <member name="M:System.Messaging.MessageQueue.GetMessageQueueEnumerator">
      <summary>Provides forward-only cursor semantics to enumerate through all public queues on the network.</summary>
      <returns>A <see cref="T:System.Messaging.MessageQueueEnumerator" /> that provides a dynamic listing of all the public message queues on the network.</returns>
    </member>
    <member name="M:System.Messaging.MessageQueue.GetMessageQueueEnumerator(System.Messaging.MessageQueueCriteria)">
      <summary>Provides forward-only cursor semantics to enumerate through all public queues on the network that meet the specified criteria.</summary>
      <returns>A <see cref="T:System.Messaging.MessageQueueEnumerator" /> that provides a dynamic listing of the public message queues on the network that satisfy the restrictions specified by the <paramref name="criteria" /> parameter.</returns>
      <param name="criteria">A <see cref="T:System.Messaging.MessageQueueCriteria" /> that contains the criteria used to filter the available message queues. </param>
    </member>
    <member name="M:System.Messaging.MessageQueue.GetPrivateQueuesByMachine(System.String)">
      <summary>Retrieves all the private queues on the specified computer.</summary>
      <returns>An array of <see cref="T:System.Messaging.MessageQueue" /> objects that reference the retrieved private queues.</returns>
      <param name="machineName">The computer from which to retrieve the private queues. </param>
      <exception cref="T:System.ArgumentException">The <paramref name="machineName" /> parameter is null or an empty string (""). </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1">
          <Path value="*" access="Browse" />
        </IPermission>
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.GetPublicQueues">
      <summary>Retrieves all the public queues on the network.</summary>
      <returns>An array of <see cref="T:System.Messaging.MessageQueue" /> objects that reference the retrieved public queues.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1">
          <Path value="*" access="Browse" />
        </IPermission>
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.GetPublicQueues(System.Messaging.MessageQueueCriteria)">
      <summary>Retrieves all the public queues on the network that meet the specified criteria.</summary>
      <returns>An array of <see cref="T:System.Messaging.MessageQueue" /> objects that reference the retrieved public queues.</returns>
      <param name="criteria">A <see cref="T:System.Messaging.MessageQueueCriteria" /> that contains the criteria used to filter the queues. </param>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1">
          <Path value="*" access="Browse" />
        </IPermission>
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.GetPublicQueuesByCategory(System.Guid)">
      <summary>Retrieves all the public queues on the network that belong to the specified category.</summary>
      <returns>An array of <see cref="T:System.Messaging.MessageQueue" /> objects that reference the retrieved public queues.</returns>
      <param name="category">A <see cref="T:System.Guid" /> that groups the set of queues to be retrieved. </param>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1">
          <Path value="*" access="Browse" />
        </IPermission>
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.GetPublicQueuesByLabel(System.String)">
      <summary>Retrieves all the public queues on the network that carry the specified label.</summary>
      <returns>An array of <see cref="T:System.Messaging.MessageQueue" /> objects that reference the retrieved public queues.</returns>
      <param name="label">A label that groups the set of queues to be retrieved. </param>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="label" /> parameter is null. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1">
          <Path value="*" access="Browse" />
        </IPermission>
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.GetPublicQueuesByMachine(System.String)">
      <summary>Retrieves all the public queues that reside on the specified computer.</summary>
      <returns>An array of <see cref="T:System.Messaging.MessageQueue" /> objects that reference the public queues on the computer.</returns>
      <param name="machineName">The name of the computer that contains the set of public queues to be retrieved. </param>
      <exception cref="T:System.ArgumentException">The <paramref name="machineName" /> parameter has incorrect syntax. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1">
          <Path value="*" access="Browse" />
        </IPermission>
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.GetSecurityContext">
      <summary>Retrieves the security context that MSMQ associates with the current user (thread identity) at the time of this call.</summary>
      <returns>A <see cref="T:System.Messaging.SecurityContext" /> object that contains the security context.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueue.Id">
      <summary>Gets the unique Message Queuing identifier of the queue.</summary>
      <returns>A <see cref="P:System.Messaging.MessageQueue.Id" /> that represents the message identifier generated by the Message Queuing application.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="F:System.Messaging.MessageQueue.InfiniteQueueSize">
      <summary>Specifies that no size restriction exists for a queue.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueue.InfiniteTimeout">
      <summary>Specifies that no time-out exists for methods that peek or receive messages.</summary>
    </member>
    <member name="P:System.Messaging.MessageQueue.Label">
      <summary>Gets or sets the queue description.</summary>
      <returns>The label for the message queue. The default is an empty string ("").</returns>
      <exception cref="T:System.ArgumentException">The label was set to an invalid value. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.LastModifyTime">
      <summary>Gets the last time the properties of a queue were modified.</summary>
      <returns>A <see cref="T:System.DateTime" /> that indicates when the queue properties were last modified.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.MachineName">
      <summary>Gets or sets the name of the computer where the Message Queuing queue is located.</summary>
      <returns>The name of the computer where the queue is located. The Message Queuing default is ".", the local computer.</returns>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Messaging.MessageQueue.MachineName" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The name of the computer is not valid, possibly because the syntax is incorrect. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.MaximumJournalSize">
      <summary>Gets or sets the maximum size of the journal queue.</summary>
      <returns>The maximum size, in kilobytes, of the journal queue. The Message Queuing default specifies that no limit exists.</returns>
      <exception cref="T:System.ArgumentException">The maximum journal queue size was set to an invalid value. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.MaximumQueueSize">
      <summary>Gets or sets the maximum size of the queue.</summary>
      <returns>The maximum size, in kilobytes, of the queue. The Message Queuing default specifies that no limit exists.</returns>
      <exception cref="T:System.ArgumentException">The maximum queue size contains a negative value. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.MessageReadPropertyFilter">
      <summary>Gets or sets the property filter for receiving or peeking messages.</summary>
      <returns>The <see cref="T:System.Messaging.MessagePropertyFilter" /> used by the queue to filter the set of properties it receives or peeks for each message.</returns>
      <exception cref="T:System.ArgumentException">The filter is null. </exception>
    </member>
    <member name="P:System.Messaging.MessageQueue.MulticastAddress">
      <summary>Introduced in MSMQ 3.0. Gets or sets the multicast address associated with the queue.</summary>
      <returns>A <see cref="T:System.String" /> that contains a valid multicast address (in the form shown below) or null, which indicates that the queue is not associated with a multicast address. Copy Code&lt;address&gt;:&lt;port&gt;</returns>
      <exception cref="T:System.PlatformNotSupportedException">MSMQ 3.0 is not installed.</exception>
    </member>
    <member name="P:System.Messaging.MessageQueue.Path">
      <summary>Gets or sets the queue's path. Setting the <see cref="P:System.Messaging.MessageQueue.Path" /> causes the <see cref="T:System.Messaging.MessageQueue" /> to point to a new queue.</summary>
      <returns>The queue that is referenced by the <see cref="T:System.Messaging.MessageQueue" />. The default depends on which <see cref="M:System.Messaging.MessageQueue.#ctor" /> constructor you use; it is either null or is specified by the constructor's <paramref name="path" /> parameter.</returns>
      <exception cref="T:System.ArgumentException">The path is not valid, possibly because the syntax is not valid. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.Peek">
      <summary>Returns without removing (peeks) the first message in the queue referenced by this <see cref="T:System.Messaging.MessageQueue" />. The <see cref="M:System.Messaging.MessageQueue.Peek" /> method is synchronous, so it blocks the current thread until a message becomes available.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> that represents the first message in the queue.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Peek(System.TimeSpan)">
      <summary>Returns without removing (peeks) the first message in the queue referenced by this <see cref="T:System.Messaging.MessageQueue" />. The <see cref="M:System.Messaging.MessageQueue.Peek" /> method is synchronous, so it blocks the current thread until a message becomes available or the specified time-out occurs.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> that represents the first message in the queue.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the maximum time to wait for the queue to contain a message. </param>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid, possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Peek(System.TimeSpan,System.Messaging.Cursor,System.Messaging.PeekAction)">
      <summary>Returns without removing (peeks) the current or next message in the queue, using the specified cursor. The <see cref="M:System.Messaging.MessageQueue.Peek" /> method is synchronous, so it blocks the current thread until a message becomes available or the specified time-out occurs.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that represents a message in the queue.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the maximum time to wait for the queue to contain a message. </param>
      <param name="cursor">A <see cref="T:System.Messaging.Cursor" /> that maintains a specific position in the message queue.</param>
      <param name="action">One of the <see cref="T:System.Messaging.PeekAction" /> values. Indicates whether to peek at the current message in the queue, or the next message.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">A value other than PeekAction.Current or PeekAction.Next was specified for the <paramref name="action" /> parameter.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="cursor" /> parameter is null.</exception>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid. Possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.PeekByCorrelationId(System.String)">
      <summary>Peeks the message that matches the given correlation identifier and immediately raises an exception if no message with the specified correlation identifier currently exists in the queue.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.CorrelationId" /> matches the <paramref name="correlationId" /> parameter passed in.</returns>
      <param name="correlationId">The <see cref="P:System.Messaging.Message.CorrelationId" /> of the message to peek. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="correlationId" /> parameter is null. </exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="correlationId" /> could not be found. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.PeekByCorrelationId(System.String,System.TimeSpan)">
      <summary>Peeks the message that matches the given correlation identifier and waits until either a message with the specified correlation identifier is available in the queue, or the time-out expires.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.CorrelationId" /> matches the <paramref name="correlationId" /> parameter passed in.</returns>
      <param name="correlationId">The <see cref="P:System.Messaging.Message.CorrelationId" /> of the message to peek. </param>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the time to wait until a new message is available for inspection. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="correlationId" /> parameter is null. </exception>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid, possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="correlationId" /> does not exist in the queue and did not arrive before the time-out expired. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">A message did not arrive before the time-out expired.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.PeekById(System.String)">
      <summary>Peeks the message whose message identifier matches the <paramref name="id" /> parameter.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.Id" /> property matches the <paramref name="id" /> parameter.</returns>
      <param name="id">The <see cref="P:System.Messaging.Message.Id" /> of the message to peek. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="id" /> parameter is null. </exception>
      <exception cref="T:System.InvalidOperationException">No message with the specified <paramref name="id" /> exists. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.PeekById(System.String,System.TimeSpan)">
      <summary>Peeks the message whose message identifier matches the <paramref name="id" /> parameter. Waits until the message appears in the queue or a time-out occurs.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.Id" /> property matches the <paramref name="id" /> parameter.</returns>
      <param name="id">The <see cref="P:System.Messaging.Message.Id" /> of the message to peek. </param>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the time to wait until a new message is available for inspection. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="id" /> parameter is null. </exception>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid, possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="id" /> does not exist in the queue and did not arrive before the period specified by the <paramref name="timeout" /> parameter expired. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.PeekByLookupId(System.Int64)">
      <summary>Introduced in MSMQ 3.0. Peeks at the message that matches the given lookup identifier from a non-transactional queue.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.LookupId" /> property matches the <paramref name="lookupId" /> parameter passed in.</returns>
      <param name="lookupId">The <see cref="P:System.Messaging.Message.LookupId" /> of the message to peek at. </param>
      <exception cref="T:System.PlatformNotSupportedException">MSMQ 3.0 is not installed.</exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="lookupId" /> could not be found. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.PeekByLookupId(System.Messaging.MessageLookupAction,System.Int64)">
      <summary>Introduced in MSMQ 3.0. Peeks at a specific message from the queue. The message can be specified by a lookup identifier or by its position at the front or end of the queue.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> specified by the <paramref name="action" /> and <paramref name="lookupId" /> parameters passed in.</returns>
      <param name="action">One of the <see cref="T:System.Messaging.MessageLookupAction" /> values, specifying how the message is read in the queue. Specify one of the following:MessageLookupAction.Current: Peeks at the message specified by <paramref name="lookupId" />.MessageLookupAction.Next: Peeks at the message following the message specified by <paramref name="lookupId" />.MessageLookupAction.Previous: Peeks at the message preceding the message specified by <paramref name="lookupId" />.MessageLookupAction.First: Peeks at the first message in the queue. The <paramref name="lookupId" /> parameter must be set to 0.MessageLookupAction.Last: Peeks at the last message in the queue. The <paramref name="lookupId" /> parameter must be set to 0.</param>
      <param name="lookupId">The <see cref="P:System.Messaging.Message.LookupId" /> of the message to peek at, or 0. 0 is used when accessing the first or last message in the queue. </param>
      <exception cref="T:System.PlatformNotSupportedException">MSMQ 3.0 is not installed.</exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="lookupId" /> could not be found. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="action" /> parameter is not one of the <see cref="T:System.Messaging.MessageLookupAction" /> members.</exception>
    </member>
    <member name="E:System.Messaging.MessageQueue.PeekCompleted">
      <summary>Occurs when a message is read without being removed from the queue. This is a result of the asynchronous operation, <see cref="M:System.Messaging.MessageQueue.BeginPeek" />.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueue.Purge">
      <summary>Deletes all the messages contained in the queue.</summary>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.QueueName">
      <summary>Gets or sets the friendly name that identifies the queue.</summary>
      <returns>The name that identifies the queue referenced by this <see cref="T:System.Messaging.MessageQueue" />. The value cannot be null.</returns>
      <exception cref="T:System.ArgumentException">The queue name is null. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.ReadHandle">
      <summary>Gets the native handle used to read messages from the message queue.</summary>
      <returns>A handle to the native queue object that you use for peeking and receiving messages from the queue.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Receive">
      <summary>Receives the first message available in the queue referenced by the <see cref="T:System.Messaging.MessageQueue" />. This call is synchronous, and blocks the current thread of execution until a message is available.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references the first message available in the queue.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Receive(System.Messaging.MessageQueueTransaction)">
      <summary>Receives the first message available in the transactional queue referenced by the <see cref="T:System.Messaging.MessageQueue" />. This call is synchronous, and blocks the current thread of execution until a message is available.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references the first message available in the queue.</returns>
      <param name="transaction">The <see cref="T:System.Messaging.MessageQueueTransaction" /> object. </param>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method.-or- The queue is non-transactional. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Receive(System.Messaging.MessageQueueTransactionType)">
      <summary>Receives the first message available in the queue referenced by the <see cref="T:System.Messaging.MessageQueue" />. This call is synchronous, and blocks the current thread of execution until a message is available.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references the first message available in the queue.</returns>
      <param name="transactionType">One of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> values, describing the type of transaction context to associate with the message. </param>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="transactionType" /> parameter is not one of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> members. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Receive(System.TimeSpan)">
      <summary>Receives the first message available in the queue referenced by the <see cref="T:System.Messaging.MessageQueue" /> and waits until either a message is available in the queue, or the time-out expires.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references the first message available in the queue.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the time to wait until a new message is available for inspection. </param>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid, possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">A message did not arrive in the queue before the time-out expired.-or- An error occurred when accessing a Message Queuing method </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Receive(System.TimeSpan,System.Messaging.Cursor)">
      <summary>Receives the current message in the queue, using a specified cursor. If no message is available, this method waits until either a message is available, or the time-out expires.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references the first message available in the queue.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the time to wait until a new message is available for inspection. </param>
      <param name="cursor">A <see cref="T:System.Messaging.Cursor" /> that maintains a specific position in the message queue.</param>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid, possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">A message did not arrive in the queue before the time-out expired.-or- An error occurred when accessing a Message Queuing method Use this overload to receive a message and return in a specified period of time if there are no messages in the queue.</exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.Receive(System.TimeSpan,System.Messaging.Cursor,System.Messaging.MessageQueueTransaction)">
      <summary>Receives the current message in the queue, using a specified cursor. If no message is available, this method waits until either a message is available, or the time-out expires.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references a message in the queue.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the time to wait until a new message is available for inspection. </param>
      <param name="cursor">A <see cref="T:System.Messaging.Cursor" /> that maintains a specific position in the message queue.</param>
      <param name="transaction">The <see cref="T:System.Messaging.MessageQueueTransaction" /> object. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="cursor" /> parameter is null.-or-The <paramref name="transaction" /> parameter is null.</exception>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid. Possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">A message did not arrive in the queue before the time-out expired.-or- The queue is non-transactional.-or- An error occurred when accessing a Message Queuing method. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.Receive(System.TimeSpan,System.Messaging.Cursor,System.Messaging.MessageQueueTransactionType)">
      <summary>Receives the current message in the queue, using a specified cursor. If no message is available, this method waits until either a message is available, or the time-out expires.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references a message in the queue.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the time to wait until a new message is available for inspection. </param>
      <param name="cursor">A <see cref="T:System.Messaging.Cursor" /> that maintains a specific position in the message queue.</param>
      <param name="transactionType">One of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> values that describes the type of transaction context to associate with the message. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="cursor" /> parameter is null.</exception>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid. Possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="transactionType" /> parameter is not one of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> members. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">A message did not arrive in the queue before the time-out expired.-or- An error occurred when accessing a Message Queuing method. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.Receive(System.TimeSpan,System.Messaging.MessageQueueTransaction)">
      <summary>Receives the first message available in the transactional queue referenced by the <see cref="T:System.Messaging.MessageQueue" /> and waits until either a message is available in the queue, or the time-out expires.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references the first message available in the queue.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the time to wait until a new message is available for inspection. </param>
      <param name="transaction">The <see cref="T:System.Messaging.MessageQueueTransaction" /> object. </param>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid, possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">A message did not arrive in the queue before the time-out expired.-or- The queue is non-transactional.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Receive(System.TimeSpan,System.Messaging.MessageQueueTransactionType)">
      <summary>Receives the first message available in the queue referenced by the <see cref="T:System.Messaging.MessageQueue" />. This call is synchronous, and waits until either a message is available in the queue, or the time-out expires.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that references the first message available in the queue.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the time to wait until a new message is available for inspection. </param>
      <param name="transactionType">One of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> values, describing the type of transaction context to associate with the message. </param>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid, possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="transactionType" /> parameter is not one of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> members. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">A message did not arrive in the queue before the time-out expired.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveByCorrelationId(System.String)">
      <summary>Receives the message that matches the given correlation identifier (from a non-transactional queue) and immediately raises an exception if no message with the specified correlation identifier currently exists in the queue.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.CorrelationId" /> matches the <paramref name="correlationId" /> parameter passed in.</returns>
      <param name="correlationId">The <see cref="P:System.Messaging.Message.CorrelationId" /> of the message to receive. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="correlationId" /> parameter is null. </exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="correlationId" /> could not be found. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveByCorrelationId(System.String,System.Messaging.MessageQueueTransaction)">
      <summary>Receives the message that matches the given correlation identifier (from a transactional queue) and immediately raises an exception if no message with the specified correlation identifier currently exists in the queue.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.CorrelationId" /> matches the <paramref name="correlationId" /> parameter passed in.</returns>
      <param name="correlationId">The <see cref="P:System.Messaging.Message.CorrelationId" /> of the message to receive. </param>
      <param name="transaction">The <see cref="T:System.Messaging.MessageQueueTransaction" /> object. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="correlationId" /> parameter is null.-or- The <paramref name="transaction" /> parameter is null. </exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="correlationId" /> could not be found. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The queue is non-transactional.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveByCorrelationId(System.String,System.Messaging.MessageQueueTransactionType)">
      <summary>Receives the message that matches the given correlation identifier and immediately raises an exception if no message with the specified correlation identifier currently exists in the queue.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.CorrelationId" /> matches the <paramref name="correlationId" /> parameter passed in.</returns>
      <param name="correlationId">The <see cref="P:System.Messaging.Message.CorrelationId" /> of the message to receive. </param>
      <param name="transactionType">One of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> values, describing the type of transaction context to associate with the message. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="correlationId" /> parameter is null. </exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="correlationId" /> could not be found. </exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="transactionType" /> parameter is not one of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> members. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveByCorrelationId(System.String,System.TimeSpan)">
      <summary>Receives the message that matches the given correlation identifier (from a non-transactional queue) and waits until either a message with the specified correlation identifier is available in the queue, or the time-out expires.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.CorrelationId" /> matches the <paramref name="correlationId" /> parameter passed in.</returns>
      <param name="correlationId">The <see cref="P:System.Messaging.Message.CorrelationId" /> of the message to receive. </param>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the time to wait until a new message is available for inspection. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="correlationId" /> parameter is null. </exception>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid, possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The message with the specified <paramref name="correlationId" /> does not exist in the queue and did not arrive before the time-out expired.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveByCorrelationId(System.String,System.TimeSpan,System.Messaging.MessageQueueTransaction)">
      <summary>Receives the message that matches the given correlation identifier (from a transactional queue) and waits until either a message with the specified correlation identifier is available in the queue, or the time-out expires.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.CorrelationId" /> matches the <paramref name="correlationId" /> parameter passed in.</returns>
      <param name="correlationId">The <see cref="P:System.Messaging.Message.CorrelationId" /> of the message to receive. </param>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the time to wait until a new message is available for inspection. </param>
      <param name="transaction">The <see cref="T:System.Messaging.MessageQueueTransaction" /> object. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="correlationId" /> parameter is null.-or- The <paramref name="transaction" /> parameter is null. </exception>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid, possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The message with the specified <paramref name="correlationId" /> does not exist in the queue and did not arrive before the time-out expired.-or- The queue is non-transactional.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveByCorrelationId(System.String,System.TimeSpan,System.Messaging.MessageQueueTransactionType)">
      <summary>Receives the message that matches the given correlation identifier and waits until either a message with the specified correlation identifier is available in the queue, or the time-out expires.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.CorrelationId" /> matches the <paramref name="correlationId" /> parameter passed in.</returns>
      <param name="correlationId">The <see cref="P:System.Messaging.Message.CorrelationId" /> of the message to receive. </param>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the time to wait until a new message is available for inspection. </param>
      <param name="transactionType">One of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> values, describing the type of transaction context to associate with the message. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="correlationId" /> parameter is null. </exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="correlationId" /> could not be found. </exception>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid, possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="transactionType" /> parameter is not one of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> members. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The message with the specified <paramref name="correlationId" /> does not exist in the queue and did not arrive before the time-out expired.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveById(System.String)">
      <summary>Receives the message that matches the given identifier from a non-transactional queue and immediately raises an exception if no message with the specified identifier currently exists in the queue.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.Id" /> property matches the <paramref name="id" /> parameter passed in.</returns>
      <param name="id">The <see cref="P:System.Messaging.Message.Id" /> of the message to receive. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="id" /> parameter is null. </exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="id" /> could not be found. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveById(System.String,System.Messaging.MessageQueueTransaction)">
      <summary>Receives the message that matches the given identifier (from a transactional queue) and immediately raises an exception if no message with the specified identifier currently exists in the queue.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.Id" /> property matches the <paramref name="id" /> parameter passed in.</returns>
      <param name="id">The <see cref="P:System.Messaging.Message.Id" /> of the message to receive. </param>
      <param name="transaction">The <see cref="T:System.Messaging.MessageQueueTransaction" /> object. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="id" /> parameter is null.-or- The <paramref name="transaction" /> parameter is null. </exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="id" /> could not be found. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The queue is non-transactional.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveById(System.String,System.Messaging.MessageQueueTransactionType)">
      <summary>Receives the message that matches the given identifier and immediately raises an exception if no message with the specified identifier currently exists in the queue.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.Id" /> property matches the <paramref name="id" /> parameter passed in.</returns>
      <param name="id">The <see cref="P:System.Messaging.Message.Id" /> of the message to receive. </param>
      <param name="transactionType">One of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> values, describing the type of transaction context to associate with the message. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="id" /> parameter is null. </exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="id" /> could not be found. </exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="transactionType" /> parameter is not one of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> members. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveById(System.String,System.TimeSpan)">
      <summary>Receives the message that matches the given identifier (from a non-transactional queue) and waits until either a message with the specified identifier is available in the queue or the time-out expires.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.Id" /> property matches the <paramref name="id" /> parameter passed in.</returns>
      <param name="id">The <see cref="P:System.Messaging.Message.Id" /> of the message to receive. </param>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the time to wait until a new message is available for inspection. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="id" /> parameter is null. </exception>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid, possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">A message with the specified <paramref name="id" /> did not arrive in the queue before the time-out expired.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveById(System.String,System.TimeSpan,System.Messaging.MessageQueueTransaction)">
      <summary>Receives the message that matches the given identifier (from a transactional queue) and waits until either a message with the specified identifier is available in the queue or the time-out expires.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.Id" /> property matches the <paramref name="id" /> parameter passed in.</returns>
      <param name="id">The <see cref="P:System.Messaging.Message.Id" /> of the message to receive. </param>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the time to wait until a new message is available for inspection. </param>
      <param name="transaction">The <see cref="T:System.Messaging.MessageQueueTransaction" /> object. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="id" /> parameter is null.-or- The <paramref name="transaction" /> parameter is null. </exception>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid, possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">A message with the specified <paramref name="id" /> did not arrive in the queue before the time-out expired.-or- The queue is non-transactional.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveById(System.String,System.TimeSpan,System.Messaging.MessageQueueTransactionType)">
      <summary>Receives the message that matches the given identifier and waits until either a message with the specified identifier is available in the queue or the time-out expires.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.Id" /> property matches the <paramref name="id" /> parameter passed in.</returns>
      <param name="id">The <see cref="P:System.Messaging.Message.Id" /> of the message to receive. </param>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that indicates the time to wait until a new message is available for inspection. </param>
      <param name="transactionType">One of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> values, describing the type of transaction context to associate with the message. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="id" /> parameter is null. </exception>
      <exception cref="T:System.ArgumentException">The value specified for the <paramref name="timeout" /> parameter is not valid, possibly <paramref name="timeout" /> is less than <see cref="F:System.TimeSpan.Zero" /> or greater than <see cref="F:System.Messaging.MessageQueue.InfiniteTimeout" />. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">A message with the specified <paramref name="id" /> did not arrive in the queue before the time-out expired.-or- An error occurred when accessing a Message Queuing method. </exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="transactionType" /> parameter is not one of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> members. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveByLookupId(System.Int64)">
      <summary>Introduced in MSMQ 3.0. Receives the message that matches the given lookup identifier from a non-transactional queue.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.LookupId" /> property matches the <paramref name="lookupId" /> parameter passed in.</returns>
      <param name="lookupId">The <see cref="P:System.Messaging.Message.LookupId" /> of the message to receive. </param>
      <exception cref="T:System.PlatformNotSupportedException">MSMQ 3.0 is not installed.</exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="lookupId" /> could not be found. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveByLookupId(System.Messaging.MessageLookupAction,System.Int64,System.Messaging.MessageQueueTransaction)">
      <summary>Introduced in MSMQ 3.0. Receives a specific message from a transactional queue. The message can be specified by a lookup identifier or by its position at the front or end of the queue.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> specified by the <paramref name="lookupId" /> and <paramref name="action" /> parameters passed in.</returns>
      <param name="action">One of the <see cref="T:System.Messaging.MessageLookupAction" /> values, specifying how the message is read in the queue. Specify one of the following:MessageLookupAction.Current: Receives the message specified by <paramref name="lookupId" /> and removes it from the queue.MessageLookupAction.Next: Receives the message following the message specified by <paramref name="lookupId" /> and removes it from the queue.MessageLookupAction.Previous: Receives the message preceding the message specified by <paramref name="lookupId" /> and removes it from the queue.MessageLookupAction.First: Receives the first message in the queue and removes it from the queue. The <paramref name="lookupId" /> parameter must be set to 0.MessageLookupAction.Last: Receives the last message in the queue and removes it from the queue. The <paramref name="lookupId" /> parameter must be set to 0.</param>
      <param name="lookupId">The <see cref="P:System.Messaging.Message.LookupId" /> of the message to receive, or 0. 0 is used when accessing the first or last message in the queue. </param>
      <param name="transaction">The <see cref="T:System.Messaging.MessageQueueTransaction" /> object.</param>
      <exception cref="T:System.PlatformNotSupportedException">MSMQ 3.0 is not installed.</exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="lookupId" /> could not be found. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method.-or- The queue is non-transactional.</exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="action" /> parameter is not one of the <see cref="T:System.Messaging.MessageLookupAction" /> members.</exception>
    </member>
    <member name="M:System.Messaging.MessageQueue.ReceiveByLookupId(System.Messaging.MessageLookupAction,System.Int64,System.Messaging.MessageQueueTransactionType)">
      <summary>Introduced in MSMQ 3.0. Receives a specific message from the queue, using the specified transaction context. The message can be specified by a lookup identifier or by its position at the front or end of the queue.</summary>
      <returns>The <see cref="T:System.Messaging.Message" /> specified by the <paramref name="action" /> and <paramref name="lookupId" /> parameters passed in.</returns>
      <param name="action">One of the <see cref="T:System.Messaging.MessageLookupAction" /> values, specifying how the message is read in the queue. Specify one of the following:MessageLookupAction.Current: Receives the message specified by <paramref name="lookupId" /> and removes it from the queue.MessageLookupAction.Next: Receives the message following the message specified by <paramref name="lookupId" /> and removes it from the queue.MessageLookupAction.Previous: Receives the message preceding the message specified by <paramref name="lookupId" /> and removes it from the queue.MessageLookupAction.First: Receives the first message in the queue and removes it from the queue. The <paramref name="lookupId" /> parameter must be set to 0.MessageLookupAction.Last: Receives the last message in the queue and removes it from the queue. The <paramref name="lookupId" /> parameter must be set to 0.</param>
      <param name="lookupId">The <see cref="P:System.Messaging.Message.LookupId" /> of the message to receive, or 0. 0 is used when accessing the first or last message in the queue. </param>
      <param name="transactionType">One of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> values, describing the type of transaction context to associate with the message.</param>
      <exception cref="T:System.PlatformNotSupportedException">MSMQ 3.0 is not installed.</exception>
      <exception cref="T:System.InvalidOperationException">The message with the specified <paramref name="lookupId" /> could not be found. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="action" /> parameter is not one of the <see cref="T:System.Messaging.MessageLookupAction" /> members.-or- The <paramref name="transactionType" /> parameter is not one of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> members.</exception>
    </member>
    <member name="E:System.Messaging.MessageQueue.ReceiveCompleted">
      <summary>Occurs when a message has been removed from the queue. This event is raised by the asynchronous operation, <see cref="M:System.Messaging.MessageQueue.BeginReceive" />.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueue.Refresh">
      <summary>Refreshes the properties presented by the <see cref="T:System.Messaging.MessageQueue" /> to reflect the current state of the resource.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueue.ResetPermissions">
      <summary>Resets the permission list to the operating system's default values. Removes any queue permissions you have appended to the default list.</summary>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Send(System.Object)">
      <summary>Sends an object to non-transactional queue referenced by this <see cref="T:System.Messaging.MessageQueue" />.</summary>
      <param name="obj">The object to send to the queue. </param>
      <exception cref="T:System.Messaging.MessageQueueException">The <see cref="P:System.Messaging.MessageQueue.Path" /> property has not been set.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Send(System.Object,System.Messaging.MessageQueueTransaction)">
      <summary>Sends an object to the transactional queue referenced by this <see cref="T:System.Messaging.MessageQueue" />.</summary>
      <param name="obj">The object to send to the queue. </param>
      <param name="transaction">The <see cref="T:System.Messaging.MessageQueueTransaction" /> object. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="transaction" /> parameter is null. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The <see cref="P:System.Messaging.MessageQueue.Path" /> property has not been set.-or- The Message Queuing application indicated an incorrect transaction use.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Send(System.Object,System.Messaging.MessageQueueTransactionType)">
      <summary>Sends an object to the queue referenced by this <see cref="T:System.Messaging.MessageQueue" />.</summary>
      <param name="obj">The object to send to the queue. </param>
      <param name="transactionType">One of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> values, describing the type of transaction context to associate with the message. </param>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="transactionType" /> parameter is not one of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> members. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The <see cref="P:System.Messaging.MessageQueue.Path" /> property has not been set.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Send(System.Object,System.String)">
      <summary>Sends an object to the non-transactional queue referenced by this <see cref="T:System.Messaging.MessageQueue" /> and specifies a label for the message.</summary>
      <param name="obj">The object to send to the queue. </param>
      <param name="label">The label of the message. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="label" /> parameter is null. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The <see cref="P:System.Messaging.MessageQueue.Path" /> property has not been set.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Send(System.Object,System.String,System.Messaging.MessageQueueTransaction)">
      <summary>Sends an object to the transactional queue referenced by this <see cref="T:System.Messaging.MessageQueue" /> and specifies a label for the message.</summary>
      <param name="obj">The object to send to the queue. </param>
      <param name="label">The label of the message. </param>
      <param name="transaction">The <see cref="T:System.Messaging.MessageQueueTransaction" /> object. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="label" /> parameter is null.-or- The <paramref name="transaction" /> parameter is null. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The <see cref="P:System.Messaging.MessageQueue.Path" /> property has not been set.-or- The Message Queuing application indicated an incorrect transaction usage.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.Send(System.Object,System.String,System.Messaging.MessageQueueTransactionType)">
      <summary>Sends an object to the queue referenced by this <see cref="T:System.Messaging.MessageQueue" /> and specifies a label for the message.</summary>
      <param name="obj">The object to send to the queue. </param>
      <param name="label">The label of the message. </param>
      <param name="transactionType">One of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> values, describing the type of transaction context to associate with the message. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="label" /> parameter is null. </exception>
      <exception cref="T:System.Messaging.MessageQueueTransaction">The Message Queuing application indicated an incorrect transaction usage. </exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="transactionType" /> parameter is not one of the <see cref="T:System.Messaging.MessageQueueTransactionType" /> members. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">The <see cref="P:System.Messaging.MessageQueue.Path" /> property has not been set.-or- An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.SetPermissions(System.Messaging.AccessControlList)">
      <summary>Assigns access rights to the queue based on the contents of an access control list.</summary>
      <param name="dacl">A <see cref="T:System.Messaging.AccessControlList" /> that contains one or more access control entries that specify the trustees and the permissions to grant. </param>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.SetPermissions(System.Messaging.MessageQueueAccessControlEntry)">
      <summary>Assigns access rights to the queue based on the contents of an access control entry.</summary>
      <param name="ace">A <see cref="T:System.Messaging.MessageQueueAccessControlEntry" /> that specifies a user, an access type, and a permission type. </param>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.SetPermissions(System.String,System.Messaging.MessageQueueAccessRights)">
      <summary>Gives a computer, group, or user the specified access rights.</summary>
      <param name="user">The individual, group, or computer that gets additional rights to the queue. </param>
      <param name="rights">A <see cref="T:System.Messaging.MessageQueueAccessRights" /> that indicates the set of rights to the queue that Message Queuing assigns to the <paramref name="user" /> passed in. </param>
      <exception cref="T:System.ArgumentException">The <paramref name="user" /> is null. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueue.SetPermissions(System.String,System.Messaging.MessageQueueAccessRights,System.Messaging.AccessControlEntryType)">
      <summary>Gives a computer, group, or user the specified access rights, with the specified access control type (allow, deny, revoke, or set).</summary>
      <param name="user">The individual, group, or computer that gets additional rights to the queue. </param>
      <param name="rights">A <see cref="T:System.Messaging.MessageQueueAccessRights" /> that indicates the set of rights to the queue that Message Queuing assigns to the <paramref name="user" /> passed in. </param>
      <param name="entryType">A <see cref="T:System.Messaging.AccessControlEntryType" /> that specifies whether to grant, deny, or revoke the permissions specified by the <paramref name="rights" /> parameter. </param>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.SynchronizingObject">
      <summary>Gets or sets the object that marshals the event-handler call resulting from a <see cref="E:System.Messaging.MessageQueue.ReceiveCompleted" /> or <see cref="E:System.Messaging.MessageQueue.PeekCompleted" /> event.</summary>
      <returns>A <see cref="T:System.ComponentModel.ISynchronizeInvoke" />, which represents the object that marshals the event-handler call resulting from a <see cref="E:System.Messaging.MessageQueue.ReceiveCompleted" /> or <see cref="E:System.Messaging.MessageQueue.PeekCompleted" /> event. The default is null.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueue.Transactional">
      <summary>Gets a value that indicates whether the queue accepts only transactions.</summary>
      <returns>true if the queue accepts only messages sent as part of a transaction; otherwise, false.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.UseJournalQueue">
      <summary>Gets or sets a value that indicates whether received messages are copied to the journal queue.</summary>
      <returns>true if messages received from the queue are copied to its journal queue; otherwise, false.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">An error occurred when accessing a Message Queuing method. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueue.WriteHandle">
      <summary>Gets the native handle used to send messages to the message queue.</summary>
      <returns>A handle to the native queue object that you use for sending messages to the queue.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">The message queue is not available for writing. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.Messaging.MessageQueueAccessControlEntry">
      <summary>Specifies access rights for a trustee (user, group, or computer) to perform Message Queuing tasks.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueueAccessControlEntry.#ctor(System.Messaging.Trustee,System.Messaging.MessageQueueAccessRights)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueueAccessControlEntry" /> class, granting the specified Message Queuing access rights to the specified trustee.</summary>
      <param name="trustee">A <see cref="T:System.Messaging.Trustee" /> that specifies a user, group, computer, domain, or alias. </param>
      <param name="rights">A bitwise combination of the <see cref="T:System.Messaging.MessageQueueAccessRights" /> values which defines the combination of rights to grant to the trustee. </param>
    </member>
    <member name="M:System.Messaging.MessageQueueAccessControlEntry.#ctor(System.Messaging.Trustee,System.Messaging.MessageQueueAccessRights,System.Messaging.AccessControlEntryType)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueueAccessControlEntry" /> class, with the specified trustee and Message Queuing access rights. The type of access (such as Allow or Deny) is defined by the entry type you pass in.</summary>
      <param name="trustee">A <see cref="T:System.Messaging.Trustee" /> that specifies a user, group, computer, domain, or alias. </param>
      <param name="rights">A bitwise combination of the <see cref="T:System.Messaging.MessageQueueAccessRights" /> values which defines the combination of rights to grant to the trustee. </param>
      <param name="entryType">One of the <see cref="T:System.Messaging.AccessControlEntryType" /> values, which specifies whether to allow, deny, set or revoke the specified rights. </param>
    </member>
    <member name="P:System.Messaging.MessageQueueAccessControlEntry.MessageQueueAccessRights">
      <summary>Gets or sets the set of Message Queuing-specific rights to apply to the trustee.</summary>
      <returns>A bitwise combination of the <see cref="T:System.Messaging.MessageQueueAccessRights" /> members. The default is defined by the <paramref name="rights" /> parameter passed into the constructor.</returns>
    </member>
    <member name="T:System.Messaging.MessageQueueAccessRights">
      <summary>Specifies a set of object-specific access rights for operations specific to Message Queuing.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.DeleteMessage">
      <summary>The right to delete messages from the queue.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.PeekMessage">
      <summary>The right to peek messages from the queue.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.WriteMessage">
      <summary>The right to send messages to the queue.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.DeleteJournalMessage">
      <summary>The right to delete messages from the journal queue.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.SetQueueProperties">
      <summary>The right to modify properties of the queue.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.GetQueueProperties">
      <summary>The right to read properties of the queue.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.DeleteQueue">
      <summary>The right to delete the queue.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.GetQueuePermissions">
      <summary>The right to read queue permissions.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.ChangeQueuePermissions">
      <summary>The right to modify queue permissions.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.TakeQueueOwnership">
      <summary>The right to take ownership of the queue.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.ReceiveMessage">
      <summary>The right to receive messages from the queue. This includes the rights to delete and peek messages.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.ReceiveJournalMessage">
      <summary>The right to receive messages from the journal queue. This includes the rights to delete and peek messages from the journal queue.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.GenericRead">
      <summary>A combination of GetQueueProperties, GetQueuePermissions, ReceiveMessage, and ReceiveJournalMessage.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.GenericWrite">
      <summary>A combination of GetQueueProperties, GetQueuePermissions, and WriteMessage.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueAccessRights.FullControl">
      <summary>Full rights to the queue. A union of all other rights in the enumeration.</summary>
    </member>
    <member name="T:System.Messaging.MessageQueueCriteria">
      <summary>Filters message queues when performing a query using the <see cref="T:System.Messaging.MessageQueue" /> class's <see cref="M:System.Messaging.MessageQueue.GetPublicQueues" /> method.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueueCriteria.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueueCriteria" /> class.</summary>
    </member>
    <member name="P:System.Messaging.MessageQueueCriteria.Category">
      <summary>Gets or sets the category by which to filter queues in the network.</summary>
      <returns>The queues' category.</returns>
      <exception cref="T:System.InvalidOperationException">The application did not set the <see cref="P:System.Messaging.MessageQueueCriteria.Category" /> property before reading it. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueueCriteria.ClearAll">
      <summary>Clears all properties from being built into a filter and puts all property values into a "not set" state.</summary>
    </member>
    <member name="P:System.Messaging.MessageQueueCriteria.CreatedAfter">
      <summary>Gets or sets the lower boundary of the queue creation date and time by which to filter queues on the network.</summary>
      <returns>A <see cref="T:System.DateTime" /> that specifies the lower boundary for a queue's creation date and time.</returns>
      <exception cref="T:System.InvalidOperationException">The application did not set the <see cref="P:System.Messaging.MessageQueueCriteria.CreatedAfter" /> property before reading it. </exception>
    </member>
    <member name="P:System.Messaging.MessageQueueCriteria.CreatedBefore">
      <summary>Gets or sets the upper boundary of the queue creation date and time by which to filter queues on the network.</summary>
      <returns>A <see cref="T:System.DateTime" /> that specifies the upper boundary for a queue's creation date and time.</returns>
      <exception cref="T:System.InvalidOperationException">The application did not set the <see cref="P:System.Messaging.MessageQueueCriteria.CreatedBefore" /> property before reading it. </exception>
    </member>
    <member name="P:System.Messaging.MessageQueueCriteria.Label">
      <summary>Gets or sets the label by which to filter queues in the network.</summary>
      <returns>The queues' label.</returns>
      <exception cref="T:System.InvalidOperationException">The application did not set the <see cref="P:System.Messaging.MessageQueueCriteria.Label" /> property before reading it. </exception>
    </member>
    <member name="P:System.Messaging.MessageQueueCriteria.MachineName">
      <summary>Gets or sets the computer name by which to filter queues in the network.</summary>
      <returns>The server name of the computer on which the queues reside.</returns>
      <exception cref="T:System.InvalidOperationException">The application did not set the <see cref="P:System.Messaging.MessageQueueCriteria.MachineName" /> property before reading it.-or- The computer name syntax is invalid. </exception>
    </member>
    <member name="P:System.Messaging.MessageQueueCriteria.ModifiedAfter">
      <summary>Gets or sets the lower boundary of the queue modification date and time by which to filter queues on the network.</summary>
      <returns>A <see cref="T:System.DateTime" /> that specifies the lower boundary for a queue's last modification date and time.</returns>
      <exception cref="T:System.InvalidOperationException">The application did not set the <see cref="P:System.Messaging.MessageQueueCriteria.ModifiedAfter" /> property before reading it. </exception>
    </member>
    <member name="P:System.Messaging.MessageQueueCriteria.ModifiedBefore">
      <summary>Gets or sets the upper boundary of the queue modification date and time by which to filter queues on the network.</summary>
      <returns>A <see cref="T:System.DateTime" /> that specifies the upper boundary for a queue's last modification date and time.</returns>
      <exception cref="T:System.InvalidOperationException">The application did not set the <see cref="P:System.Messaging.MessageQueueCriteria.ModifiedBefore" /> property before reading it. </exception>
    </member>
    <member name="T:System.Messaging.MessageQueueEnumerator">
      <summary>Provides a forward-only cursor to enumerate through messages in a message queue.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueueEnumerator.Close">
      <summary>Frees the resources associated with the enumerator.</summary>
    </member>
    <member name="P:System.Messaging.MessageQueueEnumerator.Current">
      <summary>Gets the current <see cref="T:System.Messaging.MessageQueue" /> of the enumeration.</summary>
      <returns>The queue at which the cursor is currently positioned.</returns>
      <exception cref="T:System.InvalidOperationException">You called <see cref="P:System.Messaging.MessageQueueEnumerator.Current" /> before the first call to <see cref="M:System.Messaging.MessageQueueEnumerator.MoveNext" />. The cursor is located before the first queue in the enumeration.-or- You called <see cref="P:System.Messaging.MessageQueueEnumerator.Current" /> after a call to <see cref="M:System.Messaging.MessageQueueEnumerator.MoveNext" /> had returned false (indicating the cursor is located after the last queue in the enumeration). </exception>
    </member>
    <member name="M:System.Messaging.MessageQueueEnumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Messaging.MessageQueueEnumerator" />.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueueEnumerator.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Messaging.MessageQueueEnumerator" /> and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.Messaging.MessageQueueEnumerator.Finalize">
      <summary>Releases the resources held by the queue.</summary>
    </member>
    <member name="P:System.Messaging.MessageQueueEnumerator.LocatorHandle">
      <summary>Gets the native Message Queuing handle used to locate queues in a network.</summary>
      <returns>The native handle to the current queue.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">The handle does not exist. </exception>
      <exception cref="T:System.Security.SecurityException">The calling code does not have browse permissions. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1">
          <Path value="*" access="Browse" />
        </IPermission>
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueueEnumerator.MoveNext">
      <summary>Advances the enumerator to the next queue of the enumeration, if one is currently available.</summary>
      <returns>true if the enumerator was successfully advanced to the next queue; false if the enumerator has reached the end of the enumeration.</returns>
      <exception cref="T:System.Security.SecurityException">The calling code does not have browse permissions. </exception>
      <PermissionSet>
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1">
          <Path value="*" access="Browse" />
        </IPermission>
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueueEnumerator.Reset">
      <summary>Resets the cursor, so it points to the beginning of the enumeration.</summary>
    </member>
    <member name="P:System.Messaging.MessageQueueEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the current <see cref="T:System.Messaging.MessageQueue" /> of the enumeration.</summary>
      <returns>The queue at which the cursor is currently positioned.</returns>
    </member>
    <member name="T:System.Messaging.MessageQueueErrorCode">
      <summary>Identifies the source of an error that occurred within the Message Queuing application and generated a <see cref="T:System.Messaging.MessageQueueException" /> exception.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.Base">
      <summary>Message Queuing does not return this error code.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.Generic">
      <summary>Message text: Generic Error.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.Property">
      <summary>Message text: One or more of the passed properties are invalid.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.QueueNotFound">
      <summary>Message text: The queue is not registered in the directory service.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.QueueExists">
      <summary>Message text: A queue with the same pathname is already registered.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.InvalidParameter">
      <summary>Message text: An invalid parameter passed to a function.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.InvalidHandle">
      <summary>Message text: An invalid handle passed to a function.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.OperationCanceled">
      <summary>Message text: The operation was canceled before it could be completed.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.SharingViolation">
      <summary>Message text: Sharing violation. The queue is already opened for exclusive receive.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.ServiceNotAvailable">
      <summary>Message text: The Message Queues service is not available.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.MachineNotFound">
      <summary>Message text: The specified computer could not be found.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalSort">
      <summary>Message text: Illegal sort specified (for example, duplicate columns).</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalUser">
      <summary>Message text: The user has an invalid user name.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.NoDs">
      <summary>Message text: No connection with this site's controller(s).</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalQueuePathName">
      <summary>Message text: Invalid queue path name.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalPropertyValue">
      <summary>Message text: Invalid property value.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalPropertyVt">
      <summary>Message text: Invalid VARTYPE value.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.BufferOverflow">
      <summary>Message text: The buffer supplied to MQReceiveMessage for message body retrieval was too small. The message is not removed from the queue and part of the message body that fits in the buffer was copied.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IOTimeout">
      <summary>Message text: The receive or peek message time-out has expired.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalCursorAction">
      <summary>Message text: MQ_ACTION_PEEK_NEXT specified to MQReceiveMessage can not be used with the current cursor position.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.MessageAlreadyReceived">
      <summary>Message text: A message that is currently pointed at by the cursor has been removed from the queue by another process or by another call to receive the message without the use of this cursor.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalFormatName">
      <summary>Message text: The given format name is invalid.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.FormatNameBufferTooSmall">
      <summary>Message text: The format name buffer supplied to the API was too small to fit the format name.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.UnsupportedFormatNameOperation">
      <summary>Message text: The requested operation for the specified format name is not supported.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalSecurityDescriptor">
      <summary>Message text: The specified security descriptor is not a valid security descriptor.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.SenderIdBufferTooSmall">
      <summary>Message text: The passed buffer for the user identifier property is too small.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.SecurityDescriptorBufferTooSmall">
      <summary>Message text: The size of the buffer passed to MQGetQueueSecurity is too small.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CannotImpersonateClient">
      <summary>Message text: The RPC server cannot impersonate the client application, hence security credentials could not be verified.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.AccessDenied">
      <summary>Message text: Access is denied.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.PrivilegeNotHeld">
      <summary>Message text: Client does not have the required privileges to perform the operation.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.InsufficientResources">
      <summary>Message text: Insufficient resources to perform operation.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.UserBufferTooSmall">
      <summary>Message text: Request failed because user buffer is too small to hold the returned information.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.MessageStorageFailed">
      <summary>Message text: Could not store a recoverable or journal message. Message was not sent.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.SenderCertificateBufferTooSmall">
      <summary>Message text: The passed buffer for the user certificate property is too small.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.InvalidCertificate">
      <summary>Message text: The user certificate is not valid.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CorruptedInternalCertificate">
      <summary>Message text: The internal Message Queuing certificate is corrupted.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.NoInternalUserCertificate">
      <summary>Message text: The internal Message Queuing certificate for the user does not exist.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CorruptedSecurityData">
      <summary>Message text: A cryptographic function has failed.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CorruptedPersonalCertStore">
      <summary>Message text: The personal certificate store is corrupted.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.ComputerDoesNotSupportEncryption">
      <summary>Message text: The computer does not support encryption operations.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.BadSecurityContext">
      <summary>Message text: Bad security context.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CouldNotGetUserSid">
      <summary>Message text: Could not get the SID information out of the thread token.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CouldNotGetAccountInfo">
      <summary>Message text: Could not get the account information for the user.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalCriteriaColumns">
      <summary>Message text: Invalid MQCOLUMNS parameter.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalPropertyId">
      <summary>Message text: Invalid property identifier value.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalRelation">
      <summary>Message text: Invalid relation value in restriction.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalPropertySize">
      <summary>Message text: Illegal property buffer size.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalRestrictionPropertyId">
      <summary>Message text: Invalid propid value in MQRESTRICTION parameter.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalQueueProperties">
      <summary>Message text: Illegal MQQUEUEPROPS parameter, either null or with zero properties.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.PropertyNotAllowed">
      <summary>Message text: Invalid property for the requested operation </summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.InsufficientProperties">
      <summary>Message text: Not all the required properties for the operation were specified in the input parameters.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.MachineExists">
      <summary>Message text: Computer with the same name already exists in the site.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalMessageProperties">
      <summary>Message text: Illegal MQQMPROPS parameter, either null or with zero properties.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.DsIsFull">
      <summary>Message text: Directory service is full.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.DsError">
      <summary>Message text: Internal directory service error.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.InvalidOwner">
      <summary>Message text: Invalid object owner. For example CreateQueue failed because the Queue Manager object is invalid.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.UnsupportedAccessMode">
      <summary>Message text: The specified access mode is not supported.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.ResultBufferTooSmall">
      <summary>Message text: The supplied result buffer is too small.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.DeleteConnectedNetworkInUse">
      <summary>Message text: The connected network cannot be deleted; it is in use.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.NoResponseFromObjectServer">
      <summary>Message text: No response from object owner.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.ObjectServerNotAvailable">
      <summary>Message text: Object owner is not reachable.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.QueueNotAvailable">
      <summary>Message text: Error while reading from a queue residing on a remote computer.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.DtcConnect">
      <summary>Message text: Cannot connect to MS DTC.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.TransactionImport">
      <summary>Message text: Cannot import the transaction.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.TransactionUsage">
      <summary>Message text: Wrong transaction usage.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.TransactionSequence">
      <summary>Message text: Wrong transaction operations sequence.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.MissingConnectorType">
      <summary>Message text: Connector Type is mandatory when sending an Acknowledgment or secure message.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.StaleHandle">
      <summary>Message text: The Queue Manager service has been restarted. The queue handle is stale and should be closed.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.TransactionEnlist">
      <summary>Message text: Cannot enlist the transaction.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.QueueDeleted">
      <summary>Message text: The queue was deleted. Messages cannot be received anymore using this queue instance. The queue should be closed.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalContext">
      <summary>Message text: Invalid context parameter.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalSortPropertyId">
      <summary>Message text: Invalid propid value in MQSORTSET.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.LabelBufferTooSmall">
      <summary>Message text: The label buffer supplied to the API was too small.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.MqisServerEmpty">
      <summary>Message text: The list of MQIS servers (in registry) is empty.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.MqisReadOnlyMode">
      <summary>Message text: MQIS database is in read-only mode.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.SymmetricKeyBufferTooSmall">
      <summary>Message text: The passed buffer for the Symmetric key property is too small.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.SignatureBufferTooSmall">
      <summary>Message text: The passed buffer for the Signature property is too small.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.ProviderNameBufferTooSmall">
      <summary>Message text: The passed buffer for the Provider name property is too small.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalOperation">
      <summary>Message text: The operation is invalid on foreign message queuing systems.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.WriteNotAllowed">
      <summary>Message text: Another MQIS server is being installed; write operations to the database are not allowed at this time.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.WksCantServeClient">
      <summary>Message text: Message Queuing-independent clients cannot serve Message Queuing-dependent clients.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.DependentClientLicenseOverflow">
      <summary>Message text: The number of dependent clients served by this Message Queuing server reached its upper limit.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CorruptedQueueWasDeleted">
      <summary>Message text: The.ini file for the queue in LQS was deleted because it was corrupted.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.RemoteMachineNotAvailable">
      <summary>Message text: The remote machine is not available.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.UnsupportedOperation">
      <summary>Message text: The operation is not supported for a WORKGROUP installation computer.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.EncryptionProviderNotSupported">
      <summary>Message text: The Cryptographic Service Provider is not supported by Message Queuing.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CannotSetCryptographicSecurityDescriptor">
      <summary>Message text: Unable to set the security descriptor for the cryptographic keys.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CertificateNotProvided">
      <summary>Message text: A user attempted to send an authenticated message without a certificate.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.QDnsPropertyNotSupported">
      <summary>DNS property is not supported as criteria to locate queues.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CannotCreateCertificateStore">
      <summary>Message text: Unable to create a certificate store for the internal certificate.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CannotOpenCertificateStore">
      <summary>Message text: Unable to open the certificates store for the internal certificate.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalEnterpriseOperation">
      <summary>Message text: The operation is invalid for a Message Queuing services object.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CannotGrantAddGuid">
      <summary>Message text: Failed to grant the "Add Guid" permission to current user.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CannotLoadMsmqOcm">
      <summary>Message text: Cannot load the MSMQOCM.DLL library.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.NoEntryPointMsmqOcm">
      <summary>Message text: Cannot locate an entry point in the MSMQOCM.DLL library.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.NoMsmqServersOnDc">
      <summary>Message text: Failed to find Message Queuing servers on domain controllers.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CannotJoinDomain">
      <summary>Message text: Failed to join Message Queuing enterprise on Windows 2000 domain.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CannotCreateOnGlobalCatalog">
      <summary>Message text: Failed to create an object on a specified global catalog server.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.GuidNotMatching">
      <summary>Message text: Failed to create Message Queuing configuration object with a GUID that matches the computer installation. You must uninstall Message Queuing and then reinstall it.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.PublicKeyNotFound">
      <summary>Message text: Unable to find the public key for computer.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.PublicKeyDoesNotExist">
      <summary>Message text: The public key for the computer does not exist.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.IllegalPrivateProperties">
      <summary>The private properties parameter value is invalid. This may be because it has a null value or has zero properties specified.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.NoGlobalCatalogInDomain">
      <summary>Message text: Unable to find Global Catalog servers in the specified domain.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.NoMsmqServersOnGlobalCatalog">
      <summary>Message text: Failed to find Message Queuing servers on Global Catalog domain controllers.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CannotGetDistinguishedName">
      <summary>Message text: Failed to retrieve the distinguished name of local computer.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CannotHashDataEx">
      <summary>Message text: Unable to hash data for an authenticated message.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CannotSignDataEx">
      <summary>Message text: Unable to sign data before sending an authenticated message.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.CannotCreateHashEx">
      <summary>Message text: Unable to create a hash object for an authenticated message.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.FailVerifySignatureEx">
      <summary>Message text: Signature of received message is not valid.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueErrorCode.MessageNotFound">
      <summary>Message text: The specified message could not be found.</summary>
    </member>
    <member name="T:System.Messaging.MessageQueueException">
      <summary>The exception that is thrown if a Microsoft Message Queuing internal error occurs.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueueException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueueException" /> class with serialized data.</summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized data associated with the <see cref="T:System.Messaging.MessageQueueException" />. </param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains the source and destination of the serialized stream associated with the <see cref="T:System.Messaging.MessageQueueException" />. </param>
    </member>
    <member name="M:System.Messaging.MessageQueueException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a serialization information object with the data needed to serialize the <see cref="T:System.Messaging.MessageQueueException" />.</summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized data associated with the <see cref="T:System.Messaging.MessageQueueException" />. </param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains the source and destination of the serialized stream associated with the <see cref="T:System.Messaging.MessageQueueException" />. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="info" /> parameter is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueueException.Message">
      <summary>Gets a value that describes the Message Queuing error.</summary>
      <returns>The description of the Message Queuing internal error that generated this <see cref="T:System.Messaging.MessageQueueException" />.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueueException.MessageQueueErrorCode">
      <summary>Gets a value that indicates the error code associated with this exception.</summary>
      <returns>A <see cref="T:System.Messaging.MessageQueueErrorCode" /> that identifies the type of error Message Queuing generated.</returns>
    </member>
    <member name="T:System.Messaging.MessageQueueInstaller">
      <summary>Allows you to install and configure a queue that your application needs in order to run. This class is called by the installation utility, for example, InstallUtil.exe, when installing a <see cref="T:System.Messaging.MessageQueue" />.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueueInstaller.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueueInstaller" /> class. Does not set any instance properties.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueueInstaller.#ctor(System.Messaging.MessageQueue)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueueInstaller" /> class, initializing the installation settings to those of an existing <see cref="T:System.Messaging.MessageQueue" /> instance.</summary>
      <param name="componentToCopy">The <see cref="T:System.Messaging.MessageQueue" /> component whose settings determine the property settings of the new queue installed. </param>
    </member>
    <member name="P:System.Messaging.MessageQueueInstaller.Authenticate">
      <summary>Gets or sets a value that indicates whether the queue to be installed accepts only authenticated messages.</summary>
      <returns>true if the queue accepts only authenticated messages; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueueInstaller.BasePriority">
      <summary>Gets or sets the base priority that is used to route a public queue's messages over the network.</summary>
      <returns>The single base priority for all messages sent to the public queue. The default is zero (0).</returns>
    </member>
    <member name="P:System.Messaging.MessageQueueInstaller.Category">
      <summary>Gets or sets an implementation-specific queue type.</summary>
      <returns>A <see cref="T:System.Guid" /> that represents the queue category (or Message Queuing type identifier), which allows applications to categorize their queues according to how they are used. The default is Guid.empty.</returns>
    </member>
    <member name="M:System.Messaging.MessageQueueInstaller.Commit(System.Collections.IDictionary)">
      <summary>Completes the installation process by committing the <see cref="T:System.Messaging.MessageQueue" /> installation information that the <see cref="M:System.Messaging.MessageQueueInstaller.Install(System.Collections.IDictionary)" /> method wrote to the registry. This method is meant to be used by installation tools, which automatically call the appropriate methods.</summary>
      <param name="savedState">A <see cref="T:System.Collections.IDictionary" /> that contains the post-installation state of the computer. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueueInstaller.CopyFromComponent(System.ComponentModel.IComponent)">
      <summary>Copies the property values of a <see cref="T:System.Messaging.MessageQueue" /> component that are required at install time for a message queue.</summary>
      <param name="component">A <see cref="T:System.ComponentModel.IComponent" /> to use as a template for the <see cref="T:System.Messaging.MessageQueueInstaller" />. </param>
      <exception cref="T:System.ArgumentException">The component associated with this <see cref="T:System.Messaging.MessageQueueInstaller" /> is not a <see cref="T:System.Messaging.MessageQueue" />. </exception>
    </member>
    <member name="P:System.Messaging.MessageQueueInstaller.EncryptionRequired">
      <summary>Gets or sets a value that indicates whether the queue accepts only private, or encrypted, messages.</summary>
      <returns>One of the <see cref="T:System.Messaging.EncryptionRequired" /> values that indicates the encryption level required on messages sent to the queue. The default is Optional.</returns>
    </member>
    <member name="M:System.Messaging.MessageQueueInstaller.Install(System.Collections.IDictionary)">
      <summary>Performs the installation and writes message queue information to the registry. This method is meant to be used by installation tools, which automatically call the appropriate methods.</summary>
      <param name="stateSaver">A <see cref="T:System.Collections.IDictionary" /> used to save information needed to perform a commit, rollback, or uninstall operation. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.MessageQueueInstaller.IsEquivalentInstaller(System.Configuration.Install.ComponentInstaller)">
      <summary>Determines whether the specified installer can handle the same kind of installation as this installer.</summary>
      <returns>true if this installer and the installer specified by the <paramref name="otherInstaller" /> parameter can handle the same kind of installation; otherwise, false.</returns>
      <param name="otherInstaller">The installer to compare. </param>
    </member>
    <member name="P:System.Messaging.MessageQueueInstaller.Label">
      <summary>Gets or sets a description of the queue.</summary>
      <returns>The label that describes the message queue. The default is an empty string ("").</returns>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Messaging.MessageQueueInstaller.Label" /> is null. </exception>
    </member>
    <member name="P:System.Messaging.MessageQueueInstaller.MaximumJournalSize">
      <summary>Gets or sets the maximum size of the journal that is associated with the queue.</summary>
      <returns>The maximum size, in kilobytes, of the journal queue, which records messages that are removed from the queue. The Message Queuing default is no limit.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueueInstaller.MaximumQueueSize">
      <summary>Gets or sets the maximum size of the queue.</summary>
      <returns>The maximum size, in kilobytes, of the queue. The Message Queuing default is no limit.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueueInstaller.MulticastAddress">
      <summary>Introduced in MSMQ 3.0. Gets or sets the multicast address associated with the queue.</summary>
      <returns>A <see cref="T:System.String" /> that contains a valid multicast address (in the form shown below) or null, which indicates that the queue is not associated with a multicast address. Copy Code&lt;address&gt;:&lt;port&gt;</returns>
      <exception cref="T:System.PlatformNotSupportedException">MSMQ 3.0 is not installed.</exception>
    </member>
    <member name="P:System.Messaging.MessageQueueInstaller.Path">
      <summary>Gets or sets the location of the queue that is referenced by this object.</summary>
      <returns>The path that represents the location of the queue in the network.</returns>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Messaging.MessageQueueInstaller.Path" /> was set to an invalid value, possibly because the syntax is not valid.-or- The <see cref="P:System.Messaging.MessageQueueInstaller.Path" /> is null. </exception>
    </member>
    <member name="P:System.Messaging.MessageQueueInstaller.Permissions">
      <summary>Gets or sets permissions associated with the queue.</summary>
      <returns>A <see cref="T:System.Messaging.AccessControlList" /> that contains one or more access control entries that specify the trustees and permissions to grant for the queue.</returns>
    </member>
    <member name="M:System.Messaging.MessageQueueInstaller.Rollback(System.Collections.IDictionary)">
      <summary>Restores the computer to the state it was in before the installation, by rolling back the queue information that the installation procedure wrote to the registry. This method is meant to be used by installation tools, which automatically call the appropriate methods.</summary>
      <param name="savedState">A <see cref="T:System.Collections.IDictionary" /> that contains the pre-installation state of the computer. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueueInstaller.Transactional">
      <summary>Gets or sets a value that indicates whether the queue accepts only messages sent as part of a transaction.</summary>
      <returns>true if the queue can only accept messages sent as part of a transaction; otherwise, false. The default is false.</returns>
    </member>
    <member name="M:System.Messaging.MessageQueueInstaller.Uninstall(System.Collections.IDictionary)">
      <summary>Removes an installation by removing queue information from the registry. This method is meant to be used by uninstallation tools, which automatically call the appropriate methods.</summary>
      <param name="savedState">A <see cref="T:System.Collections.IDictionary" /> that contains the post-installation state of the computer. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.MessageQueueInstaller.UninstallAction">
      <summary>Gets or sets a value that indicates what the installer does with the queue at uninstall time: remove it, restore it to its pre-installation state, or leave it in its current installed state.</summary>
      <returns>One of the <see cref="T:System.Configuration.Install.UninstallAction" /> values that indicates what state to leave the queue in when the <see cref="T:System.Messaging.MessageQueue" /> is uninstalled. The default is Remove.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueueInstaller.UseJournalQueue">
      <summary>Gets or sets a value that indicates whether messages that are retrieved from the queue are also copied to the associated journal queue.</summary>
      <returns>true to copy messages that are retrieved from the queue to the journal queue; otherwise, false. The default is false.</returns>
    </member>
    <member name="T:System.Messaging.MessageQueuePermission">
      <summary>Allows control of code access permissions for messaging.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueuePermission.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueuePermission" /> class.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueuePermission.#ctor(System.Messaging.MessageQueuePermissionAccess,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueuePermission" /> class with the specified access levels and the path of the queue.</summary>
      <param name="permissionAccess">One of the <see cref="T:System.Messaging.MessageQueuePermissionAccess" /> values. </param>
      <param name="path">The path of the queue that is referenced by the <see cref="T:System.Messaging.MessageQueue" />. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermission.#ctor(System.Messaging.MessageQueuePermissionAccess,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueuePermission" /> class with the specified access levels, computer to use, queue description, and queue category.</summary>
      <param name="permissionAccess">One of the <see cref="T:System.Messaging.MessageQueuePermissionAccess" /> values. </param>
      <param name="machineName">The name of the computer where the Message Queuing queue is located. </param>
      <param name="label">The queue description. </param>
      <param name="category">The queue category (Message Queuing type identifier). </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermission.#ctor(System.Messaging.MessageQueuePermissionEntry[])">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueuePermission" /> class with the specified permission access level entries.</summary>
      <param name="permissionAccessEntries">An array of <see cref="T:System.Messaging.MessageQueuePermissionEntry" /> objects. The <see cref="P:System.Messaging.MessageQueuePermission.PermissionEntries" /> property is set to this value. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermission.#ctor(System.Security.Permissions.PermissionState)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueuePermission" /> class with the specified permission state.</summary>
      <param name="state">One of the <see cref="T:System.Security.Permissions.PermissionState" /> values. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermission.Copy">
      <summary>Creates and returns an identical copy of the current permission object.</summary>
      <returns>A copy of the current permission object.</returns>
    </member>
    <member name="M:System.Messaging.MessageQueuePermission.FromXml(System.Security.SecurityElement)">
      <summary>Reconstructs a security object with a specified state from an XML encoding.</summary>
      <param name="securityElement">The XML encoding to use to reconstruct the security object. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermission.Intersect(System.Security.IPermission)">
      <summary>Returns a new permission object that represents the intersection of the current permission object and the specified permission object.</summary>
      <returns>A new permission object that represents the intersection of the current permission object and the specified permission object. This new permission object is a null reference (Nothing in Visual Basic) if the intersection is empty.</returns>
      <param name="target">A permission object to intersect with the current permission object. It must be of the same type as the current permission object. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermission.IsSubsetOf(System.Security.IPermission)">
      <summary>Returns a value that indicates whether the current permission object is a subset of the specified permission object.</summary>
      <returns>true if the current permission object is a subset of the specified permission object; otherwise, false.</returns>
      <param name="target">A permission object that is to be tested for the subset relationship. This object must be of the same type as the current permission object. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermission.IsUnrestricted">
      <summary>Returns a value that indicates whether the permission can be represented as unrestricted without any knowledge of the permission semantics.</summary>
      <returns>true if the permission can be represented as unrestricted; otherwise, false.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueuePermission.PermissionEntries">
      <summary>Gets the collection of permission entries for this permissions request.</summary>
      <returns>A <see cref="T:System.Messaging.MessageQueuePermissionEntryCollection" /> that contains the permission entries for this permissions request.</returns>
    </member>
    <member name="M:System.Messaging.MessageQueuePermission.ToXml">
      <summary>Creates an XML encoding of the security object and its current state.</summary>
      <returns>An XML encoding of the security object, including any state information.</returns>
    </member>
    <member name="M:System.Messaging.MessageQueuePermission.Union(System.Security.IPermission)">
      <summary>Returns a new permission object that is the union of the current and specified permission objects.</summary>
      <returns>A new permission object that represents the union of the current permission object and the specified permission object.</returns>
      <param name="target">A permission object to combine with the current permission object. It must be of the same type as the current permission object. </param>
    </member>
    <member name="T:System.Messaging.MessageQueuePermissionAccess">
      <summary>Defines access levels used by <see cref="N:System.Messaging" /> permission classes.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueuePermissionAccess.None">
      <summary>The <see cref="T:System.Messaging.MessageQueue" /> has no permissions.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueuePermissionAccess.Browse">
      <summary>The <see cref="T:System.Messaging.MessageQueue" /> can look at the queues that are available.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueuePermissionAccess.Send">
      <summary>The <see cref="T:System.Messaging.MessageQueue" /> can look at the queues that are available and send messages.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueuePermissionAccess.Peek">
      <summary>The <see cref="T:System.Messaging.MessageQueue" /> can look at the queues that are available and read the messages in the queue.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueuePermissionAccess.Receive">
      <summary>The <see cref="T:System.Messaging.MessageQueue" /> can look at the queues that are available, read the messages in the queue, and receive messages.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueuePermissionAccess.Administer">
      <summary>The <see cref="T:System.Messaging.MessageQueue" /> can look at the queues that are available, read the messages in the queue, and send and receive messages.</summary>
    </member>
    <member name="T:System.Messaging.MessageQueuePermissionAttribute">
      <summary>Allows declarative <see cref="T:System.Messaging.MessageQueue" /> permission checks.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionAttribute.#ctor(System.Security.Permissions.SecurityAction)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueuePermissionAttribute" /> class.</summary>
      <param name="action">One of the <see cref="T:System.Security.Permissions.SecurityAction" /> values. </param>
    </member>
    <member name="P:System.Messaging.MessageQueuePermissionAttribute.Category">
      <summary>Gets or sets the queue category.</summary>
      <returns>The queue category (Message Queuing type identifier), which allows an application to categorize its queues.</returns>
      <exception cref="T:System.InvalidOperationException">The value is null. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionAttribute.CreatePermission">
      <summary>Creates the permission based on the requested access levels, category, label, computer name, and path that are set through the <see cref="P:System.Messaging.MessageQueuePermissionAttribute.PermissionAccess" />, <see cref="P:System.Messaging.MessageQueuePermissionAttribute.Category" />, <see cref="P:System.Messaging.MessageQueuePermissionAttribute.Label" />, <see cref="P:System.Messaging.MessageQueuePermissionAttribute.MachineName" />, and <see cref="P:System.Messaging.MessageQueuePermissionAttribute.Path" /> properties on the attribute.</summary>
      <returns>A <see cref="T:System.Security.IPermission" /> that represents the created permission.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueuePermissionAttribute.Label">
      <summary>Gets or sets the queue description.</summary>
      <returns>The label for the message queue.</returns>
      <exception cref="T:System.InvalidOperationException">The value is null. </exception>
    </member>
    <member name="P:System.Messaging.MessageQueuePermissionAttribute.MachineName">
      <summary>Gets or sets the name of the computer where the Message Queuing queue is located.</summary>
      <returns>The name of the computer where the queue is located.</returns>
      <exception cref="T:System.InvalidOperationException">The value is null. </exception>
    </member>
    <member name="P:System.Messaging.MessageQueuePermissionAttribute.Path">
      <summary>Gets or sets the queue's path.</summary>
      <returns>The queue that is referenced by the <see cref="T:System.Messaging.MessageQueue" />.</returns>
      <exception cref="T:System.InvalidOperationException">The value is null. </exception>
    </member>
    <member name="P:System.Messaging.MessageQueuePermissionAttribute.PermissionAccess">
      <summary>Gets or sets the permission access levels used in the permissions request.</summary>
      <returns>A bitwise combination of the <see cref="T:System.Messaging.MessageQueuePermissionAccess" /> values.</returns>
    </member>
    <member name="T:System.Messaging.MessageQueuePermissionEntry">
      <summary>Defines the smallest unit of a code access security permission set for messaging.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionEntry.#ctor(System.Messaging.MessageQueuePermissionAccess,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueuePermissionEntry" /> class with the specified permission access levels and the path of the queue.</summary>
      <param name="permissionAccess">A bitwise combination of the <see cref="T:System.Messaging.MessageQueuePermissionAccess" /> values. The <see cref="P:System.Messaging.MessageQueuePermissionEntry.PermissionAccess" /> property is set to this value. </param>
      <param name="path">The path of the queue that is referenced by the <see cref="T:System.Messaging.MessageQueue" /> object. The <see cref="P:System.Messaging.MessageQueuePermissionEntry.Path" /> property is set to this value. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionEntry.#ctor(System.Messaging.MessageQueuePermissionAccess,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueuePermissionEntry" /> class with the specified permission access levels, the name of the computer where the queue is located, the queue description, and the queue category.</summary>
      <param name="permissionAccess">A bitwise combination of the <see cref="T:System.Messaging.MessageQueuePermissionAccess" /> values. The <see cref="P:System.Messaging.MessageQueuePermissionEntry.PermissionAccess" /> property is set to this value. </param>
      <param name="machineName">The name of the computer where the Message Queuing queue is located. The <see cref="P:System.Messaging.MessageQueuePermissionEntry.MachineName" /> property is set to this value. </param>
      <param name="label">The queue description. The <see cref="P:System.Messaging.MessageQueuePermissionEntry.Label" /> property is set to this value. </param>
      <param name="category">The queue category (Message Queuing type identifier). The <see cref="P:System.Messaging.MessageQueuePermissionEntry.Category" /> property is set to this value. </param>
    </member>
    <member name="P:System.Messaging.MessageQueuePermissionEntry.Category">
      <summary>Gets the queue category.</summary>
      <returns>The queue category (Message Queuing type identifier), which allows an application to categorize its queues.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueuePermissionEntry.Label">
      <summary>Gets the queue description.</summary>
      <returns>The label for the message queue.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueuePermissionEntry.MachineName">
      <summary>Gets the name of the computer where the Message Queuing queue is located.</summary>
      <returns>The name of the computer where the queue is located.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueuePermissionEntry.Path">
      <summary>Gets the queue's path.</summary>
      <returns>The queue that is referenced by the <see cref="T:System.Messaging.MessageQueue" />.</returns>
    </member>
    <member name="P:System.Messaging.MessageQueuePermissionEntry.PermissionAccess">
      <summary>Gets the permission access levels used in the permissions request.</summary>
      <returns>A bitwise combination of the <see cref="T:System.Messaging.MessageQueuePermissionAccess" /> values.</returns>
    </member>
    <member name="T:System.Messaging.MessageQueuePermissionEntryCollection">
      <summary>Contains a strongly typed collection of <see cref="T:System.Messaging.MessageQueuePermissionEntry" /> objects.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionEntryCollection.Add(System.Messaging.MessageQueuePermissionEntry)">
      <summary>Adds a specified <see cref="T:System.Messaging.MessageQueuePermissionEntry" /> to this collection.</summary>
      <returns>The zero-based index of the added <see cref="T:System.Messaging.MessageQueuePermissionEntry" />.</returns>
      <param name="value">The <see cref="T:System.Messaging.MessageQueuePermissionEntry" /> to add. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionEntryCollection.AddRange(System.Messaging.MessageQueuePermissionEntry[])">
      <summary>Appends a set of specified permission entries to this collection.</summary>
      <param name="value">An array of type <see cref="T:System.Messaging.MessageQueuePermissionEntry" /> objects that contains the permission entries to add. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionEntryCollection.AddRange(System.Messaging.MessageQueuePermissionEntryCollection)">
      <summary>Appends a set of specified permission entries to this collection.</summary>
      <param name="value">A <see cref="T:System.Messaging.MessageQueuePermissionEntryCollection" /> that contains the permission entries to add. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionEntryCollection.Contains(System.Messaging.MessageQueuePermissionEntry)">
      <summary>Determines whether this collection contains a specified <see cref="T:System.Messaging.MessageQueuePermissionEntry" />.</summary>
      <returns>true if the specified <see cref="T:System.Messaging.MessageQueuePermissionEntry" /> belongs to this collection; otherwise, false.</returns>
      <param name="value">The <see cref="T:System.Messaging.MessageQueuePermissionEntry" /> to find. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionEntryCollection.CopyTo(System.Messaging.MessageQueuePermissionEntry[],System.Int32)">
      <summary>Copies the permission entries from this collection to an array, starting at a particular index of the array.</summary>
      <param name="array">An array of type <see cref="T:System.Messaging.MessageQueuePermissionEntry" /> that receives this collection's permission entries. </param>
      <param name="index">The zero-based index at which to begin copying the permission entries. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionEntryCollection.IndexOf(System.Messaging.MessageQueuePermissionEntry)">
      <summary>Determines the index of a specified permission entry in this collection.</summary>
      <returns>The zero-based index of the specified permission entry, or -1 if the permission entry was not found in the collection.</returns>
      <param name="value">The permission entry to search for. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionEntryCollection.Insert(System.Int32,System.Messaging.MessageQueuePermissionEntry)">
      <summary>Inserts a permission entry into this collection at a specified index.</summary>
      <param name="index">The zero-based index into the collection at which to insert the permission entry. </param>
      <param name="value">The permission entry to insert into this collection. </param>
    </member>
    <member name="P:System.Messaging.MessageQueuePermissionEntryCollection.Item(System.Int32)">
      <summary>Gets or sets the object at a specified index.</summary>
      <returns>The <see cref="T:System.Messaging.MessageQueuePermissionEntry" /> that exists at the specified index.</returns>
      <param name="index">The zero-based index into the collection. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionEntryCollection.OnClear">
      <summary>Performs additional custom processes after clearing the contents of the collection.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionEntryCollection.OnInsert(System.Int32,System.Object)">
      <summary>Performs additional custom processes before a new permission entry is inserted into the collection.</summary>
      <param name="index">The zero-based index at which to insert <paramref name="value" />. </param>
      <param name="value">The new value of the permission entry at <paramref name="index" />. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionEntryCollection.OnRemove(System.Int32,System.Object)">
      <summary>Performs additional custom processes when removing a new permission entry from the collection.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> can be found. </param>
      <param name="value">The permission entry to remove from <paramref name="index" />. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionEntryCollection.OnSet(System.Int32,System.Object,System.Object)">
      <summary>Performs additional custom processes before setting a value in the collection.</summary>
      <param name="index">The zero-based index at which <paramref name="oldValue" /> can be found.</param>
      <param name="oldValue">The value to replace with <paramref name="newValue" />. </param>
      <param name="newValue">The new value of the permission entry at <paramref name="index" />. </param>
    </member>
    <member name="M:System.Messaging.MessageQueuePermissionEntryCollection.Remove(System.Messaging.MessageQueuePermissionEntry)">
      <summary>Removes a specified permission entry from this collection.</summary>
      <param name="value">The permission entry to remove. </param>
    </member>
    <member name="T:System.Messaging.MessageQueueTransaction">
      <summary>Provides a Message Queuing internal transaction.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueueTransaction.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessageQueueTransaction" /> class.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueueTransaction.Abort">
      <summary>Rolls back the pending internal transaction.</summary>
      <exception cref="T:System.InvalidOperationException">The internal transaction you are attempting to roll back has not started. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An internal Message Queuing error occurs. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueueTransaction.Begin">
      <summary>Begins a new Message Queuing internal transaction.</summary>
      <exception cref="T:System.InvalidOperationException">The transaction has already been started. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An internal Message Queuing error occurs. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueueTransaction.Commit">
      <summary>Commits a pending internal transaction.</summary>
      <exception cref="T:System.InvalidOperationException">The transaction you are trying to commit has not started. </exception>
      <exception cref="T:System.Messaging.MessageQueueException">An internal Message Queuing error occurs. </exception>
    </member>
    <member name="M:System.Messaging.MessageQueueTransaction.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Messaging.MessageQueueTransaction" />.</summary>
    </member>
    <member name="M:System.Messaging.MessageQueueTransaction.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Messaging.MessageQueueTransaction" /> and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.Messaging.MessageQueueTransaction.Finalize">
      <summary>Releases the resources held by the current instance.</summary>
    </member>
    <member name="P:System.Messaging.MessageQueueTransaction.Status">
      <summary>Gets the status of the transaction.</summary>
      <returns>One of the <see cref="T:System.Messaging.MessageQueueTransactionStatus" /> values that indicates whether the transaction has been committed, aborted, initialized, or is pending.</returns>
    </member>
    <member name="T:System.Messaging.MessageQueueTransactionStatus">
      <summary>Specifies the state of an internal Message Queuing transaction.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueTransactionStatus.Aborted">
      <summary>The transaction has been aborted and all participants have been notified.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueTransactionStatus.Committed">
      <summary>The transaction has been committed and all participants have been notified.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueTransactionStatus.Initialized">
      <summary>The transaction has been initialized. It has not yet been started.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueTransactionStatus.Pending">
      <summary>The transaction has been started. It has not yet been either committed or rolled back.</summary>
    </member>
    <member name="T:System.Messaging.MessageQueueTransactionType">
      <summary>Specifies the type of a Message Queuing transaction.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueTransactionType.None">
      <summary>Operation will not be transactional.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueTransactionType.Automatic">
      <summary>A transaction type used for Microsoft Transaction Server (MTS) or COM+ 1.0 Services. If there is already an MTS transaction context, it will be used when sending or receiving the message.</summary>
    </member>
    <member name="F:System.Messaging.MessageQueueTransactionType.Single">
      <summary>A transaction type used for single internal transactions.</summary>
    </member>
    <member name="T:System.Messaging.MessageType">
      <summary>Identifies the type of a message. A message can be a typical Message Queuing message, a positive (arrival and read) or negative (arrival and read) acknowledgment message, or a report message.</summary>
    </member>
    <member name="F:System.Messaging.MessageType.Acknowledgment">
      <summary>An acknowledgment message.</summary>
    </member>
    <member name="F:System.Messaging.MessageType.Normal">
      <summary>A normal Message Queuing message.</summary>
    </member>
    <member name="F:System.Messaging.MessageType.Report">
      <summary>A report message.</summary>
    </member>
    <member name="T:System.Messaging.MessagingDescriptionAttribute">
      <summary>Specifies a description for a property or event.</summary>
    </member>
    <member name="M:System.Messaging.MessagingDescriptionAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.MessagingDescriptionAttribute" /> class, using the specified description.</summary>
      <param name="description">The application-defined description text. </param>
    </member>
    <member name="P:System.Messaging.MessagingDescriptionAttribute.Description">
      <summary>Gets description text associated with the item monitored.</summary>
      <returns>An application-defined description.</returns>
    </member>
    <member name="T:System.Messaging.PeekAction">
      <summary>Indicates whether to peek the current message in a queue, or the next message.</summary>
    </member>
    <member name="F:System.Messaging.PeekAction.Current">
      <summary>Peek at the current message in the queue.</summary>
    </member>
    <member name="F:System.Messaging.PeekAction.Next">
      <summary>Peek at the next message in the queue.</summary>
    </member>
    <member name="T:System.Messaging.PeekCompletedEventArgs">
      <summary>Provides data for the <see cref="E:System.Messaging.MessageQueue.PeekCompleted" /> event. When your asynchronous peek operation calls an event handler, an instance of this class is passed to the handler.</summary>
    </member>
    <member name="P:System.Messaging.PeekCompletedEventArgs.AsyncResult">
      <summary>Gets or sets the result of the asynchronous operation requested.</summary>
      <returns>A <see cref="T:System.IAsyncResult" /> that contains the data associated with the peek operation.</returns>
    </member>
    <member name="P:System.Messaging.PeekCompletedEventArgs.Message">
      <summary>Gets the message associated with the asynchronous peek operation.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that represents the end result of the asynchronous peek operation.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">The <see cref="P:System.Messaging.PeekCompletedEventArgs.Message" /> could not be retrieved. The time-out on the asynchronous operation might have expired. </exception>
    </member>
    <member name="T:System.Messaging.PeekCompletedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.Messaging.MessageQueue.PeekCompleted" /> event of a <see cref="T:System.Messaging.MessageQueue" />.</summary>
      <param name="sender">The source of the event, the <see cref="T:System.Messaging.MessageQueue" />. </param>
      <param name="e">A <see cref="T:System.Messaging.PeekCompletedEventArgs" /> that contains the event data. </param>
    </member>
    <member name="T:System.Messaging.QueueAccessMode">
      <summary>Specifies the access mode for a <see cref="T:System.Messaging.MessageQueue" /> at creation time.</summary>
    </member>
    <member name="F:System.Messaging.QueueAccessMode.Send">
      <summary>The queue can only send messages.</summary>
    </member>
    <member name="F:System.Messaging.QueueAccessMode.Peek">
      <summary>The queue can only peek at messages.</summary>
    </member>
    <member name="F:System.Messaging.QueueAccessMode.Receive">
      <summary>The queue can peek at or receive messages.</summary>
    </member>
    <member name="F:System.Messaging.QueueAccessMode.PeekAndAdmin">
      <summary>The queue can peek at messages. It can also be purged.</summary>
    </member>
    <member name="F:System.Messaging.QueueAccessMode.ReceiveAndAdmin">
      <summary>The queue can receive messages. It can also be purged.</summary>
    </member>
    <member name="F:System.Messaging.QueueAccessMode.SendAndReceive">
      <summary>The queue can peek at, receive, or send messages.</summary>
    </member>
    <member name="T:System.Messaging.ReceiveCompletedEventArgs">
      <summary>Provides data for the <see cref="E:System.Messaging.MessageQueue.ReceiveCompleted" /> event. When your asynchronous receive operation calls an event handler, an instance of this class is passed to the handler.</summary>
    </member>
    <member name="P:System.Messaging.ReceiveCompletedEventArgs.AsyncResult">
      <summary>Gets or sets the result of the asynchronous operation requested.</summary>
      <returns>A <see cref="T:System.IAsyncResult" /> that contains the data associated with the receive operation.</returns>
    </member>
    <member name="P:System.Messaging.ReceiveCompletedEventArgs.Message">
      <summary>Gets the message associated with the asynchronous receive operation.</summary>
      <returns>A <see cref="T:System.Messaging.Message" /> that represents the end result of the asynchronous receive operation.</returns>
      <exception cref="T:System.Messaging.MessageQueueException">The <see cref="P:System.Messaging.ReceiveCompletedEventArgs.Message" /> could not be retrieved. The time-out on the asynchronous operation might have expired. </exception>
    </member>
    <member name="T:System.Messaging.ReceiveCompletedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.Messaging.MessageQueue.ReceiveCompleted" /> event of a <see cref="T:System.Messaging.MessageQueue" />.</summary>
      <param name="sender">The source of the event, the <see cref="T:System.Messaging.MessageQueue" />. </param>
      <param name="e">A <see cref="T:System.Messaging.ReceiveCompletedEventArgs" /> that contains the event data. </param>
    </member>
    <member name="T:System.Messaging.SecurityContext">
      <summary>Represents the security context for a message in a queue.</summary>
    </member>
    <member name="M:System.Messaging.SecurityContext.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Messaging.SecurityContext" />.</summary>
    </member>
    <member name="T:System.Messaging.StandardAccessRights">
      <summary>Specifies a set of standard access rights that correspond to operations common to most types of securable objects.</summary>
    </member>
    <member name="F:System.Messaging.StandardAccessRights.Delete">
      <summary>The right to delete the object.</summary>
    </member>
    <member name="F:System.Messaging.StandardAccessRights.ReadSecurity">
      <summary>The right to read the information in the object's security descriptor.</summary>
    </member>
    <member name="F:System.Messaging.StandardAccessRights.WriteSecurity">
      <summary>The right to modify the discretionary access control list (DACL) in the security descriptor.</summary>
    </member>
    <member name="F:System.Messaging.StandardAccessRights.Synchronize">
      <summary>The right to use the object for synchronization. This enables a thread to wait until the object is in a specific state.</summary>
    </member>
    <member name="F:System.Messaging.StandardAccessRights.ModifyOwner">
      <summary>The right to change the owner in the object's security descriptor.</summary>
    </member>
    <member name="F:System.Messaging.StandardAccessRights.Read">
      <summary>The right to read the information in the object's security descriptor. Read is currently defined to equal ReadSecurity.</summary>
    </member>
    <member name="F:System.Messaging.StandardAccessRights.Write">
      <summary>The right to read the information in the object's security descriptor. Write is currently defined to equal ReadSecurity.</summary>
    </member>
    <member name="F:System.Messaging.StandardAccessRights.Execute">
      <summary>The right to read the information in the object's security descriptor. On Windows 2000 and Windows NT, the security descriptor contains the security information for a securable object. It identifies the object's owner and primary group. Execute is currently defined to equal ReadSecurity.</summary>
    </member>
    <member name="F:System.Messaging.StandardAccessRights.Required">
      <summary>Combines Delete, ReadSecurity, WriteSecurity, and ModifyOwner access.</summary>
    </member>
    <member name="F:System.Messaging.StandardAccessRights.All">
      <summary>Combines Delete, ReadSecurity, WriteSecurity, ModifyOwner, and Synchronize access.</summary>
    </member>
    <member name="F:System.Messaging.StandardAccessRights.None">
      <summary>No access.</summary>
    </member>
    <member name="T:System.Messaging.Trustee">
      <summary>Specifies a user account, group account, or logon session to which an access control entry applies.</summary>
    </member>
    <member name="M:System.Messaging.Trustee.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.Trustee" /> class without setting any of its read/write properties.</summary>
    </member>
    <member name="M:System.Messaging.Trustee.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.Trustee" /> class of type Unknown, setting the <see cref="P:System.Messaging.Trustee.Name" /> property to the value specified, and the <see cref="P:System.Messaging.Trustee.SystemName" /> to null.</summary>
      <param name="name">The value to assign to the <see cref="P:System.Messaging.Trustee.Name" /> property. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="name" /> parameter is null. </exception>
    </member>
    <member name="M:System.Messaging.Trustee.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.Trustee" /> class of type Unknown, setting the <see cref="P:System.Messaging.Trustee.Name" /> and the SystemName properties to the values specified.</summary>
      <param name="name">The value to assign to the <see cref="P:System.Messaging.Trustee.Name" /> property. </param>
      <param name="systemName">The value to assign to the <see cref="P:System.Messaging.Trustee.SystemName" /> property. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="name" /> parameter is null. </exception>
    </member>
    <member name="M:System.Messaging.Trustee.#ctor(System.String,System.String,System.Messaging.TrusteeType)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.Trustee" /> class of the specified type, setting the <see cref="P:System.Messaging.Trustee.Name" /> and the <see cref="P:System.Messaging.Trustee.SystemName" /> properties to the values specified.</summary>
      <param name="name">The value to assign to the <see cref="P:System.Messaging.Trustee.Name" /> property. </param>
      <param name="systemName">The value to assign to the <see cref="P:System.Messaging.Trustee.SystemName" /> property. </param>
      <param name="trusteeType">A <see cref="T:System.Messaging.TrusteeType" /> that indicates the account type of the trustee. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="name" /> parameter is null. </exception>
    </member>
    <member name="P:System.Messaging.Trustee.Name">
      <summary>Gets or sets the name of the trustee.</summary>
      <returns>The name of the account to which the new rights will be assigned. The default is null.</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Messaging.Trustee.Name" /> property is null. </exception>
    </member>
    <member name="P:System.Messaging.Trustee.SystemName">
      <summary>Gets or sets the computer on which to look up the trustee's account.</summary>
      <returns>The local or remote computer on which the account exists. The default is null, which indicates that the name will be looked up on the local computer.</returns>
    </member>
    <member name="P:System.Messaging.Trustee.TrusteeType">
      <summary>Gets or sets the type of the trustee, which identifies whether the trustee is a user, group, computer, domain, or alias.</summary>
      <returns>A <see cref="T:System.Messaging.TrusteeType" /> that indicates what type of account the trustee has on the system. The default is Unknown.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The trustee type specified is not one of the <see cref="T:System.Messaging.TrusteeType" /> enumeration members. </exception>
    </member>
    <member name="T:System.Messaging.TrusteeType">
      <summary>Specifies the type of a trustee.</summary>
    </member>
    <member name="F:System.Messaging.TrusteeType.Unknown">
      <summary>The trustee type is unknown, but not necessarily invalid.</summary>
    </member>
    <member name="F:System.Messaging.TrusteeType.User">
      <summary>The trustee is a user.</summary>
    </member>
    <member name="F:System.Messaging.TrusteeType.Group">
      <summary>The trustee is a group.</summary>
    </member>
    <member name="F:System.Messaging.TrusteeType.Domain">
      <summary>The trustee is a domain.</summary>
    </member>
    <member name="F:System.Messaging.TrusteeType.Alias">
      <summary>The trustee is an alias.</summary>
    </member>
    <member name="F:System.Messaging.TrusteeType.Computer">
      <summary>The trustee is a computer.</summary>
    </member>
    <member name="T:System.Messaging.XmlMessageFormatter">
      <summary>Serializes and deserializes objects to or from the body of a message, using the XML format based on the XSD schema definition.</summary>
    </member>
    <member name="M:System.Messaging.XmlMessageFormatter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.XmlMessageFormatter" /> class, without target types set.</summary>
    </member>
    <member name="M:System.Messaging.XmlMessageFormatter.#ctor(System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.XmlMessageFormatter" /> class, setting target types passed in as an array of (fully qualified) string values.</summary>
      <param name="targetTypeNames">An array of type <see cref="T:System.String" /> that specifies the set of possible types that will be deserialized by the formatter from the message provided. These values must be fully qualified, for example, "MyNamespace.MyOrders, MyOrdersAssemblyName". </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="targetTypeNames" /> parameter is null. </exception>
    </member>
    <member name="M:System.Messaging.XmlMessageFormatter.#ctor(System.Type[])">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.XmlMessageFormatter" /> class, setting target types passed in as an array of object types.</summary>
      <param name="targetTypes">An array of type <see cref="T:System.Type" /> that specifies the set of possible types that will be deserialized by the formatter from the message provided. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="targetTypes" /> parameter is null. </exception>
    </member>
    <member name="M:System.Messaging.XmlMessageFormatter.CanRead(System.Messaging.Message)">
      <summary>Determines whether the formatter can deserialize the message.</summary>
      <returns>true if the XML formatter can deserialize the message; otherwise, false.</returns>
      <param name="message">The <see cref="T:System.Messaging.Message" /> to inspect. </param>
      <exception cref="T:System.InvalidOperationException">Neither the <see cref="P:System.Messaging.XmlMessageFormatter.TargetTypeNames" /> nor <see cref="P:System.Messaging.XmlMessageFormatter.TargetTypes" /> property has been set. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="message" /> parameter is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.XmlMessageFormatter.Clone">
      <summary>Creates an instance of the <see cref="T:System.Messaging.XmlMessageFormatter" /> class whose read/write properties (the sets of target types) are the same as the current <see cref="T:System.Messaging.XmlMessageFormatter" /> instance.</summary>
      <returns>An object whose properties are identical to those of this <see cref="T:System.Messaging.XmlMessageFormatter" /> instance, but whose metadata does not specify it to be a formatter class instance.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.XmlMessageFormatter.Read(System.Messaging.Message)">
      <summary>Reads the contents from the given message and creates an object that contains the deserialized message.</summary>
      <returns>The deserialized message.</returns>
      <param name="message">The <see cref="T:System.Messaging.Message" />, in XML format, to deserialize. </param>
      <exception cref="T:System.InvalidOperationException">Neither the <see cref="P:System.Messaging.XmlMessageFormatter.TargetTypeNames" /> nor <see cref="P:System.Messaging.XmlMessageFormatter.TargetTypes" /> property has been set.-or- The instance serialized in the message body does not comply with any of the schemas represented by the types in the <see cref="P:System.Messaging.XmlMessageFormatter.TargetTypeNames" /> and <see cref="P:System.Messaging.XmlMessageFormatter.TargetTypes" /> properties. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="message" /> parameter is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Messaging.XmlMessageFormatter.TargetTypeNames">
      <summary>Specifies the set of possible types that will be deserialized by the formatter from the message provided.</summary>
      <returns>An array of type <see cref="T:System.String" /> that specifies the types of objects to deserialize from the message body when reading the message.</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Messaging.XmlMessageFormatter.TargetTypeNames" /> property is null. </exception>
    </member>
    <member name="P:System.Messaging.XmlMessageFormatter.TargetTypes">
      <summary>Specifies the set of possible types that will be deserialized by the formatter from the message provided.</summary>
      <returns>An array of type <see cref="T:System.Type" /> that specifies the types of objects to deserialize from the message body when reading the message.</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Messaging.XmlMessageFormatter.TargetTypes" /> property is null. </exception>
    </member>
    <member name="M:System.Messaging.XmlMessageFormatter.Write(System.Messaging.Message,System.Object)">
      <summary>Serializes an object into the body of the message.</summary>
      <param name="message">The <see cref="T:System.Messaging.Message" /> whose <see cref="P:System.Messaging.Message.Body" /> property will contain the serialized object. </param>
      <param name="obj">The <see cref="T:System.Object" /> to be serialized into the message body. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="message" /> parameter is null.-or- The <paramref name="obj" /> parameter is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.Messaging.Design.QueuePathDialog">
      <summary>Represents a path editor control.</summary>
    </member>
    <member name="M:System.Messaging.Design.QueuePathDialog.#ctor(System.IServiceProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.Design.QueuePathDialog" /> class, using the specified service provider.</summary>
      <param name="provider">A <see cref="T:System.IServiceProvider" /> that can be used to obtain services.</param>
    </member>
    <member name="M:System.Messaging.Design.QueuePathDialog.#ctor(System.Windows.Forms.Design.IUIService)">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.Design.QueuePathDialog" /> class, using the specified UI service.</summary>
      <param name="uiService">A <see cref="T:System.Windows.Forms.Design.IUIService" /> that enables interaction with the user interface of the hosting development environment object.</param>
    </member>
    <member name="M:System.Messaging.Design.QueuePathDialog.ChoosePath">
      <summary>Updates the path editor control when a queue path is selected.</summary>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.Design.QueuePathDialog.DoubleClicked(System.Object,System.EventArgs)">
      <summary>Occurs when the control is double-clicked.</summary>
      <param name="source">The source of the event.</param>
      <param name="e">A <see cref="T:System.EventArgs" /> that contains the event data.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.Design.QueuePathDialog.OnFormClosing(System.Windows.Forms.FormClosingEventArgs)">
      <summary>Event handler for the form closing event of the queue path editor control.</summary>
      <param name="e">A <see cref="T:System.Windows.Forms.FormClosingEventArgs" /> object that contains the arguments for the form closing event handler.</param>
    </member>
    <member name="M:System.Messaging.Design.QueuePathDialog.OnHandleCreated(System.EventArgs)">
      <summary>Event handler for the form creation event of the queue path editor control.</summary>
      <param name="e">A <see cref="T:System.Windows.Forms.FormClosingEventArgs" /> object that contains the arguments for the form creation event handler.</param>
    </member>
    <member name="P:System.Messaging.Design.QueuePathDialog.Path">
      <summary>Gets the queue path.</summary>
      <returns>The path for the queue.</returns>
    </member>
    <member name="M:System.Messaging.Design.QueuePathDialog.SelectQueue(System.Messaging.MessageQueue)">
      <summary>Selects the specified queue.</summary>
      <param name="queue">The <see cref="T:System.Messaging.MessageQueue" /> to be selected.</param>
    </member>
    <member name="T:System.Messaging.Design.QueuePathEditor">
      <summary>Provides a user interface for selecting a message queue path.</summary>
    </member>
    <member name="M:System.Messaging.Design.QueuePathEditor.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Messaging.Design.QueuePathEditor" /> class.</summary>
    </member>
    <member name="M:System.Messaging.Design.QueuePathEditor.EditValue(System.ComponentModel.ITypeDescriptorContext,System.IServiceProvider,System.Object)">
      <summary>Edits the value of the specified message queue using the specified type descriptor context and service provider.</summary>
      <returns>The new value of the message queue path. </returns>
      <param name="context">A <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides additional context information. </param>
      <param name="provider">A <see cref="T:System.IServiceProvider" /> that the editor can use to obtain services. </param>
      <param name="value">The message queue to edit. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Security.Permissions.UIPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Messaging.MessageQueuePermission, System.Messaging, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Messaging.Design.QueuePathEditor.GetEditStyle(System.ComponentModel.ITypeDescriptorContext)">
      <summary>Returns the editing style used by the <see cref="Overload:System.Messaging.Design.QueuePathEditor.EditValue" /> method. </summary>
      <returns>
        <see cref="T:System.Drawing.Design.UITypeEditorEditStyle" />.</returns>
      <param name="context">A <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides additional context information. </param>
    </member>
  </members>
</doc>