﻿using OCRTools.Common;
using System;
using System.Collections.Specialized;
using System.IO;
using System.Net;

namespace ImageLib
{
    /// <summary>
    /// </summary>
    public class Net126Upload
    {
        public static bool Enable { get; set; } = true;

        public static string GetResult(byte[] content)
        {
            var result = GetFromAiWen(content);
            return result;
        }

        private const string strFileNameSpilt = "\"data\":\"";

        private static string GetFromAiWen(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://dun.163.com/node/api/upload-image.json";
                var file = new UploadFileInfo()
                {
                    Name = "image",
                    Filename = "1.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var vaules = new NameValueCollection() {
                    { "name", "1.png" }
                };
                var html = string.Empty;
                try
                {
                    html = UploadFileRequest.Post(url, new[] { file }, vaules);
                }
                catch (BadApiException exception)
                {
                    switch (exception.Code)
                    {
                        case HttpStatusCode.MethodNotAllowed: //405
                        case HttpStatusCode.Unauthorized://401
                        case HttpStatusCode.NotFound: //404
                            Enable = false;
                            break;
                    }
                }
                catch { }
                if (html.Contains(strFileNameSpilt))
                {
                    result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
                }
            }
            catch (Exception oe)
            {

            }
            return result;
        }
    }
}
