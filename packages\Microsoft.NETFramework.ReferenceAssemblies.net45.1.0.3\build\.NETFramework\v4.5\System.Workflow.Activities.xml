﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Workflow.Activities</name>
  </assembly>
  <members>
    <member name="T:System.Workflow.Activities.ActiveDirectoryRole">
      <summary>Represents an Active Directory role in a Windows Workflow Foundation hosting environment. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.ActiveDirectoryRole.GetAllReports">
      <summary>Returns an <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> that contains all the roles that report to the current role either directly or indirectly.</summary>
      <returns>A <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> that contains all the roles that either directly or indirectly report to the current role.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.ActiveDirectoryRole.GetDirectReports">
      <summary>Returns an <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> that contains all the roles that report directly to the current role.</summary>
      <returns>An <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> that contains all the roles that directly report to the current role.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.ActiveDirectoryRole.GetEntries">
      <summary>Returns the generic collection that contains all <see cref="T:System.DirectoryServices.DirectoryEntry" /> objects associated with the current <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />. </summary>
      <returns>The generic <see cref="T:System.DirectoryServices.DirectoryEntry" /> collection that is associated with the current <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />.</returns>
    </member>
    <member name="M:System.Workflow.Activities.ActiveDirectoryRole.GetIdentities">
      <summary>Gets a generic list of the Windows NT accounts associated with the <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />. </summary>
      <returns>A generic list of values that represent the Windows NT accounts associated with the <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />. </returns>
    </member>
    <member name="M:System.Workflow.Activities.ActiveDirectoryRole.GetManager">
      <summary>Returns an Active Directory role that contains information about the manager of the current <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> that contains information about the manager of the current role.</returns>
    </member>
    <member name="M:System.Workflow.Activities.ActiveDirectoryRole.GetManagerialChain">
      <summary>Returns the Active Directory role that contains information about the managerial chain associated with the current <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />. </summary>
      <returns>The <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> that contains information about the managerial chain associated with the current role.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.ActiveDirectoryRole.GetPeers">
      <summary>Returns an Active Directory role that contains information about all the directory operations that are supervised by the manager of the current <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />.</summary>
      <returns>A <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> that contains information about all directory operations that are supervised by the manager of the current <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />.</returns>
    </member>
    <member name="M:System.Workflow.Activities.ActiveDirectoryRole.GetSecurityIdentifiers">
      <summary>Returns a generic list of <see cref="T:System.Security.Principal.SecurityIdentifier" /> objects associated with the current <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />. </summary>
      <returns>A generic list of <see cref="T:System.Security.Principal.SecurityIdentifier" /> objects associated with the current <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.ActiveDirectoryRole.IncludesIdentity(System.String)">
      <summary>Returns a value that indicates whether the <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> contains the specified identity. </summary>
      <returns>true to indicate that <paramref name="identity" /> is included in the <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />; otherwise, false.</returns>
      <param name="identity">The identity for which to search.</param>
    </member>
    <member name="P:System.Workflow.Activities.ActiveDirectoryRole.Name">
      <summary>Gets or sets the Active Directory role name.</summary>
      <returns>The Active Directory role name.</returns>
    </member>
    <member name="P:System.Workflow.Activities.ActiveDirectoryRole.RootEntry">
      <summary>Gets the root Active Directory node associated with the <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />.</summary>
      <returns>A <see cref="T:System.DirectoryServices.DirectoryEntry" /> that represents the root Active Directory node associated with the <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.ActiveDirectoryRole.System#IDisposable#Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />. </summary>
    </member>
    <member name="M:System.Workflow.Activities.ActiveDirectoryRole.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with the data required to serialize the target object.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> destination for this serialization.</param>
    </member>
    <member name="T:System.Workflow.Activities.ActiveDirectoryRoleFactory">
      <summary>A factory class for creating <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> objects from Windows NT aliases, e-mail addresses, and security identifiers (SIDs).</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.ActiveDirectoryRoleFactory.Configuration">
      <summary>Gets the <see cref="T:System.Workflow.Activities.Configuration.ActiveDirectoryRoleFactoryConfiguration" /> object that is associated with the <see cref="T:System.Workflow.Activities.ActiveDirectoryRoleFactory" />.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Configuration.ActiveDirectoryRoleFactoryConfiguration" /> object associated with the <see cref="T:System.Workflow.Activities.ActiveDirectoryRoleFactory" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.ActiveDirectoryRoleFactory.CreateFromAlias(System.String)">
      <summary>Creates an instance of <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> using the specified Windows NT alias.</summary>
      <returns>An instance of <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> that describes the specified Windows NT alias.</returns>
      <param name="alias">The alias string to create the <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />.</param>
    </member>
    <member name="M:System.Workflow.Activities.ActiveDirectoryRoleFactory.CreateFromEmailAddress(System.String)">
      <summary>Creates an instance of <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> using an e-mail address.</summary>
      <returns>An instance of <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> that describes the specified e-mail address.</returns>
      <param name="emailAddress">The e-mail address string from which to create the <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />.</param>
    </member>
    <member name="M:System.Workflow.Activities.ActiveDirectoryRoleFactory.CreateFromSecurityIdentifier(System.Security.Principal.SecurityIdentifier)">
      <summary>Creates an instance of <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> using a Windows NT <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>An instance of <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" /> that describes the specified <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
      <param name="sid">The <see cref="T:System.Security.Principal.SecurityIdentifier" /> to use to create the <see cref="T:System.Workflow.Activities.ActiveDirectoryRole" />.</param>
    </member>
    <member name="T:System.Workflow.Activities.CallExternalMethodActivity">
      <summary>Defines a workflow communication activity that is used to call a method on a local service. This activity is used to send data from the workflow to the host through the local service.</summary>
    </member>
    <member name="M:System.Workflow.Activities.CallExternalMethodActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.CallExternalMethodActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.CallExternalMethodActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.CallExternalMethodActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity, used to initialize the activity name property.</param>
    </member>
    <member name="P:System.Workflow.Activities.CallExternalMethodActivity.CorrelationToken">
      <summary>Gets or sets the <see cref="T:System.Workflow.Runtime.CorrelationToken" /> for the external method.</summary>
      <returns>The correlation token for the external method.</returns>
    </member>
    <member name="F:System.Workflow.Activities.CallExternalMethodActivity.CorrelationTokenProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.CallExternalMethodActivity.CorrelationToken" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.CallExternalMethodActivity.Execute(System.Workflow.ComponentModel.ActivityExecutionContext)">
      <summary>Tries to run the <see cref="T:System.Workflow.Activities.CallExternalMethodActivity" /> with the specified <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" />.</summary>
      <returns>One of the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionStatus" /> values.</returns>
      <param name="executionContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> that contains the execution environment in which to run the <see cref="T:System.Workflow.Activities.CallExternalMethodActivity" />.</param>
      <exception cref="T:System.ArgumentException">This <see cref="P:System.Workflow.Activities.CallExternalMethodActivity.InterfaceType" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="executionContext" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">The service object for the <paramref name="executionContext" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Activities.CallExternalMethodActivity.InitializeProperties">
      <summary>Performs initialization on dependency properties.</summary>
    </member>
    <member name="P:System.Workflow.Activities.CallExternalMethodActivity.InterfaceType">
      <summary>Gets or sets an external method's declaring interface that has the <see cref="T:System.Workflow.Activities.ExternalDataExchangeAttribute" />.</summary>
      <returns>The external method's declaring interface that has the <see cref="T:System.Workflow.Activities.ExternalDataExchangeAttribute" />.</returns>
    </member>
    <member name="F:System.Workflow.Activities.CallExternalMethodActivity.InterfaceTypeProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.CallExternalMethodActivity.InterfaceType" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Workflow.Activities.CallExternalMethodActivity.MethodInvoking">
      <summary>Occurs before invoking the method.</summary>
    </member>
    <member name="F:System.Workflow.Activities.CallExternalMethodActivity.MethodInvokingEvent">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="E:System.Workflow.Activities.CallExternalMethodActivity.MethodInvoking" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.CallExternalMethodActivity.MethodName">
      <summary>Gets or sets the name of the method to be called on the local service registered with the <see cref="T:System.Workflow.Activities.ExternalDataExchangeService" />.</summary>
      <returns>The method name of the <see cref="T:System.Workflow.Activities.ExternalDataExchangeService" /> interface.</returns>
    </member>
    <member name="F:System.Workflow.Activities.CallExternalMethodActivity.MethodNameProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.CallExternalMethodActivity.MethodName" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.CallExternalMethodActivity.OnMethodInvoked(System.EventArgs)">
      <summary>Provides a hook for derived classes to extract out and return values from the <see cref="P:System.Workflow.Activities.CallExternalMethodActivity.ParameterBindings" />. This method is called just after the external method is run. </summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the data for the <see cref="E:System.Workflow.Activities.CallExternalMethodActivity.MethodInvoking" /> event.</param>
    </member>
    <member name="M:System.Workflow.Activities.CallExternalMethodActivity.OnMethodInvoking(System.EventArgs)">
      <summary>Provides a hook for derived classes to set <see cref="P:System.Workflow.Activities.CallExternalMethodActivity.ParameterBindings" />. This method is called just before the external method is run.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the data for the <see cref="E:System.Workflow.Activities.CallExternalMethodActivity.MethodInvoking" /> event.</param>
    </member>
    <member name="P:System.Workflow.Activities.CallExternalMethodActivity.ParameterBindings">
      <summary>Gets the collection of bindable parameters as found in the external method's formal parameter list.</summary>
      <returns>The <see cref="T:System.Workflow.ComponentModel.WorkflowParameterBindingCollection" /> of parameters to bind to.</returns>
    </member>
    <member name="F:System.Workflow.Activities.CallExternalMethodActivity.ParameterBindingsProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.CallExternalMethodActivity.ParameterBindings" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.CallExternalMethodActivity.System#Workflow#ComponentModel#IDynamicPropertyTypeProvider#GetAccessType(System.IServiceProvider,System.String)">
      <summary>Returns the access type for the specified property.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Compiler.AccessTypes" /> enumeration value that denotes the access level of the property.</returns>
      <param name="serviceProvider">An object that implements <see cref="T:System.IServiceProvider" /> to provide access to design time services if they are required.</param>
      <param name="propertyName">The name of the property.</param>
    </member>
    <member name="M:System.Workflow.Activities.CallExternalMethodActivity.System#Workflow#ComponentModel#IDynamicPropertyTypeProvider#GetPropertyType(System.IServiceProvider,System.String)">
      <summary>Returns the <see cref="T:System.Type" /> of the specified property.</summary>
      <returns>The <see cref="T:System.Type" /> for the property whose name is passed as the <paramref name="propertyName " />parameter. </returns>
      <param name="serviceProvider">An object that implements <see cref="T:System.IServiceProvider" /> to provide access to design time services if they are required.</param>
      <param name="propertyName">The name of the property.</param>
    </member>
    <member name="T:System.Workflow.Activities.CallExternalMethodActivityValidator">
      <summary>Verifies that the <see cref="T:System.Workflow.Activities.CallExternalMethodActivity" /> class is configured correctly.</summary>
    </member>
    <member name="M:System.Workflow.Activities.CallExternalMethodActivityValidator.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.CallExternalMethodActivityValidator" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.CallExternalMethodActivityValidator.Validate(System.Workflow.ComponentModel.Compiler.ValidationManager,System.Object)">
      <summary>Validates the <see cref="T:System.Workflow.Activities.CallExternalMethodActivity" /> class during workflow compilation.</summary>
      <returns>A <see cref="T:System.Workflow.ComponentModel.Compiler.ValidationErrorCollection" /> that contains the errors from this operation.</returns>
      <param name="manager">The <see cref="T:System.Workflow.ComponentModel.Compiler.ValidationManager" /> to use for this validation.</param>
      <param name="obj">The <see cref="T:System.Object" /> to validate.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Workflow.Activities.CodeActivity">
      <summary>Runs the code-beside method associated with an activity. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.CodeActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.CodeActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.CodeActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.CodeActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="E:System.Workflow.Activities.CodeActivity.ExecuteCode">
      <summary>Occurs when the <see cref="T:System.Workflow.Activities.CodeActivity" /> starts.</summary>
    </member>
    <member name="F:System.Workflow.Activities.CodeActivity.ExecuteCodeEvent">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="E:System.Workflow.Activities.CodeActivity.ExecuteCode" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.CodeCondition">
      <summary>Defines a condition that guards/drives the execution of a certain activity within a workflow definition. It has an event of type <see cref="T:System.Workflow.Activities.ConditionalEventArgs" /> to return the result of the condition.</summary>
    </member>
    <member name="M:System.Workflow.Activities.CodeCondition.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.CodeCondition" /> class. </summary>
    </member>
    <member name="E:System.Workflow.Activities.CodeCondition.Condition">
      <summary>Occurs when the condition is evaluated.</summary>
    </member>
    <member name="F:System.Workflow.Activities.CodeCondition.ConditionEvent">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="E:System.Workflow.Activities.CodeCondition.Condition" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.CodeCondition.Evaluate(System.Workflow.ComponentModel.Activity,System.IServiceProvider)">
      <summary>Evaluates the underlying condition. </summary>
      <returns>true if the condition evaluates to true; otherwise, false. The default is false.</returns>
      <param name="ownerActivity">The <see cref="T:System.Workflow.ComponentModel.Activity" /> associated with this condition.</param>
      <param name="provider">The <see cref="T:System.IServiceProvider" /> for this condition evaluation.</param>
    </member>
    <member name="M:System.Workflow.Activities.CodeCondition.GetBoundValue(System.Workflow.ComponentModel.ActivityBind,System.Type)">
      <summary>Retrieves the <see cref="T:System.Object" /> that is the subject of a <see cref="T:System.Workflow.ComponentModel.ActivityBind" />.</summary>
      <returns>The <see cref="T:System.Object" /> bound to the activity by the <see cref="T:System.Workflow.ComponentModel.ActivityBind" />.</returns>
      <param name="bind">The <see cref="T:System.Workflow.ComponentModel.ActivityBind" /> of interest.</param>
      <param name="targetType">The <see cref="T:System.Type" /> of the target of the <see cref="T:System.Workflow.ComponentModel.ActivityBind" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bind" /> or <paramref name="targetType" /> is null.</exception>
    </member>
    <member name="T:System.Workflow.Activities.CompensatableSequenceActivity">
      <summary>Defines a compensatable version of the <see cref="T:System.Workflow.Activities.SequenceActivity" /> activity. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.CompensatableSequenceActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.CompensatableSequenceActivity" /> class.  </summary>
    </member>
    <member name="M:System.Workflow.Activities.CompensatableSequenceActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.CompensatableSequenceActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="M:System.Workflow.Activities.CompensatableSequenceActivity.System#Workflow#ComponentModel#ICompensatableActivity#Compensate(System.Workflow.ComponentModel.ActivityExecutionContext)">
      <summary>Executes the activity using the <see cref="M:System.Workflow.ComponentModel.ICompensatableActivity.Compensate(System.Workflow.ComponentModel.ActivityExecutionContext)" /> interface.</summary>
      <returns>The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionStatus" /> after the operation has been tried.</returns>
      <param name="executionContext">The execution context of the activity.</param>
    </member>
    <member name="T:System.Workflow.Activities.ConditionalEventArgs">
      <summary>Returns result information for the <see cref="T:System.Workflow.Activities.CodeCondition" /> class.  This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.ConditionalEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ConditionalEventArgs" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.ConditionalEventArgs.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ConditionalEventArgs" /> class using the result of the condition.</summary>
      <param name="result">The result of the condition.</param>
    </member>
    <member name="P:System.Workflow.Activities.ConditionalEventArgs.Result">
      <summary>Gets or sets the result of a <see cref="T:System.Workflow.Activities.CodeCondition" /> evaluation.</summary>
      <returns>true if the result of the condition is true; otherwise, false.</returns>
    </member>
    <member name="T:System.Workflow.Activities.ConditionedActivityGroup">
      <summary>Provides the definition of a constraint-based execution context for a set of child activities.</summary>
    </member>
    <member name="M:System.Workflow.Activities.ConditionedActivityGroup.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ConditionedActivityGroup" /> class. </summary>
      <exception cref="T:System.InvalidOperationException">No children are executing and the <see cref="P:System.Workflow.Activities.ConditionedActivityGroup.UntilCondition" /> evaluates to false.</exception>
    </member>
    <member name="M:System.Workflow.Activities.ConditionedActivityGroup.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ConditionedActivityGroup" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="M:System.Workflow.Activities.ConditionedActivityGroup.GetChildActivityExecutedCount(System.Workflow.ComponentModel.Activity)">
      <summary>Gets the number of times that the specified child activity has been executed.</summary>
      <returns>The number of times that the specified child activity has been executed.</returns>
      <param name="child">The child activity.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="child" /> parameter is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">The conditioned activity group state is null.</exception>
      <exception cref="T:System.ArgumentException">The number of times that the child activity has been run cannot be determined.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.ConditionedActivityGroup.GetDynamicActivity(System.String)">
      <summary>Gets the currently executing instance of the child activity.</summary>
      <returns>The currently executing instance of the <see cref="T:System.Workflow.Activities.EventDrivenActivity" />.</returns>
      <param name="childActivityName">The name of the child <see cref="T:System.Workflow.Activities.EventDrivenActivity" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="childactivity" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">The executable activities do not contain the <paramref name="childactivity" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.ConditionedActivityGroup.GetWhenCondition(System.Object)">
      <summary>Gets the condition associated with the <see cref="F:System.Workflow.Activities.ConditionedActivityGroup.WhenConditionProperty" /> for the specified dependency object.</summary>
      <returns>The when condition.</returns>
      <param name="dependencyObject">The underlying data storage object for the activity.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dependencyObject" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">The executable activities do not contain the <paramref name="dependencyObject" />.</exception>
    </member>
    <member name="M:System.Workflow.Activities.ConditionedActivityGroup.SetWhenCondition(System.Object,System.Object)">
      <summary>Sets the condition associated with the <see cref="F:System.Workflow.Activities.ConditionedActivityGroup.WhenConditionProperty" /> for the specified dependency object.</summary>
      <param name="dependencyObject">The underlying data storage object for the activity.</param>
      <param name="value">The value of the when condition.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dependencyObject" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">The executable activities do not contain the <paramref name="dependencyObject" />.</exception>
    </member>
    <member name="M:System.Workflow.Activities.ConditionedActivityGroup.System#Workflow#ComponentModel#IActivityEventListener{T}#OnEvent(System.Object,System.Workflow.ComponentModel.ActivityExecutionStatusChangedEventArgs)">
      <summary>Defines the processing procedure when the subscribed-to event occurs.</summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">The previously-typed event arguments.</param>
    </member>
    <member name="P:System.Workflow.Activities.ConditionedActivityGroup.UntilCondition">
      <summary>Gets or sets a value that indicates when the <see cref="T:System.Workflow.Activities.ConditionedActivityGroup" /> should complete.</summary>
      <returns>A condition that determines whether the <see cref="T:System.Workflow.Activities.ConditionedActivityGroup" /> should complete.</returns>
    </member>
    <member name="F:System.Workflow.Activities.ConditionedActivityGroup.UntilConditionProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.ConditionedActivityGroup.UntilCondition" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Workflow.Activities.ConditionedActivityGroup.WhenConditionProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="F:System.Workflow.Activities.ConditionedActivityGroup.WhenConditionProperty" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.CorrelationAliasAttribute">
      <summary>Overrides the correlation parameter value when the correlation value must be obtained from a parameter other than that indicated by the <see cref="T:System.Workflow.Activities.CorrelationParameterAttribute" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.CorrelationAliasAttribute.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.CorrelationAliasAttribute" /> with the name of the correlation that is being aliased and the path to where the value can be found.</summary>
      <param name="name">The name of the correlation parameter that is being aliased.</param>
      <param name="path">Indicates which argument in the method or event holds the value for the correlation parameter specified by <paramref name="name" />. This parameter can use dot notation, for example, Parameter.Location.</param>
    </member>
    <member name="P:System.Workflow.Activities.CorrelationAliasAttribute.Name">
      <summary>Gets the name of the correlation parameter that is being aliased.</summary>
      <returns>The name of the correlation parameter that is being aliased.</returns>
    </member>
    <member name="P:System.Workflow.Activities.CorrelationAliasAttribute.Path">
      <summary>Gets the path within the parameter that specifies the location of the correlation value.</summary>
      <returns>The path within the parameter that specifies the location of the correlation value.</returns>
    </member>
    <member name="T:System.Workflow.Activities.CorrelationInitializerAttribute">
      <summary>Indicates the method or event on an ExternalDataExchange interface that initializes the correlation value. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.CorrelationInitializerAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.CorrelationInitializerAttribute" /> attribute.</summary>
    </member>
    <member name="T:System.Workflow.Activities.CorrelationParameterAttribute">
      <summary>Indicates the name of the parameter used for correlation in the methods and events defined on an ExternalDataExchange interface. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.CorrelationParameterAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.CorrelationParameterAttribute" /> where the name of the parameter is used for correlation.</summary>
      <param name="name">The name of the parameter used for correlation.</param>
    </member>
    <member name="P:System.Workflow.Activities.CorrelationParameterAttribute.Name">
      <summary>Gets the name of the parameter used for correlation.</summary>
      <returns>Name of the parameter used for correlation. The default is an empty string.</returns>
    </member>
    <member name="T:System.Workflow.Activities.DelayActivity">
      <summary>Provides the logic to establish a timer and to wait, asynchronously, for timer's expiration.  This class cannot be inherited. </summary>
    </member>
    <member name="M:System.Workflow.Activities.DelayActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.DelayActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.DelayActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.DelayActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="E:System.Workflow.Activities.DelayActivity.InitializeTimeoutDuration">
      <summary>Occurs before the time-out duration starts. </summary>
    </member>
    <member name="F:System.Workflow.Activities.DelayActivity.InitializeTimeoutDurationEvent">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="E:System.Workflow.Activities.DelayActivity.InitializeTimeoutDuration" /> event. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.DelayActivity.System#Workflow#Activities#IEventActivity#QueueName">
      <summary>Gets the name of the workflow queue that will be delivered when the timer expires.</summary>
      <returns>The name of the workflow queue.</returns>
    </member>
    <member name="M:System.Workflow.Activities.DelayActivity.System#Workflow#Activities#IEventActivity#Subscribe(System.Workflow.ComponentModel.ActivityExecutionContext,System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.ComponentModel.QueueEventArgs})">
      <summary>Creates the subscription of the <see cref="T:System.Workflow.Activities.DelayActivity" /> to an event.</summary>
      <param name="parentContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> that represents the execution environment of the <see cref="T:System.Workflow.ComponentModel.Activity" />.</param>
      <param name="parentEventHandler">The <see cref="T:System.EventHandler" /> for the parent event.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="parentContext" /> or <paramref name="parentEventHandler" /> is null.</exception>
    </member>
    <member name="M:System.Workflow.Activities.DelayActivity.System#Workflow#Activities#IEventActivity#Unsubscribe(System.Workflow.ComponentModel.ActivityExecutionContext,System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.ComponentModel.QueueEventArgs})">
      <summary>Cancels the subscription of the <see cref="T:System.Workflow.Activities.DelayActivity" /> to an event.</summary>
      <param name="parentContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> that represents the execution environment of the <see cref="T:System.Workflow.ComponentModel.Activity" />.</param>
      <param name="parentEventHandler">The <see cref="T:System.EventHandler" /> for the parent event.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="parentContext" /> or <paramref name="parentEventHandler" /> is null.</exception>
    </member>
    <member name="M:System.Workflow.Activities.DelayActivity.System#Workflow#ComponentModel#IActivityEventListener{T}#OnEvent(System.Object,System.Workflow.ComponentModel.QueueEventArgs)">
      <summary>Defines the processing procedure when the subscribed-to event occurs.</summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">The previously typed event arguments.</param>
    </member>
    <member name="P:System.Workflow.Activities.DelayActivity.TimeoutDuration">
      <summary>Gets and sets the duration of the delay in the workflow.</summary>
      <returns>A <see cref="T:System.TimeSpan" /> that indicates the length of the delay in the workflow.</returns>
    </member>
    <member name="F:System.Workflow.Activities.DelayActivity.TimeoutDurationProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.DelayActivity.TimeoutDuration" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.EventDeliveryFailedException">
      <summary>The exception that is thrown when an event that is raised from the host cannot be delivered to the workflow instance. Typically the event is raised from an <see cref="T:System.Workflow.Activities.ExternalDataExchangeService" /> on a workflow instance. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.EventDeliveryFailedException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.EventDeliveryFailedException" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.EventDeliveryFailedException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.EventDeliveryFailedException" /> class by using the error message that explains the reason for the exception.</summary>
      <param name="message">An error message that explains the reason for the exception.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.EventDeliveryFailedException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.EventDeliveryFailedException" /> class by using the error message that explains the reason for the exception and the <see cref="T:System.Exception" /> that caused this exception.</summary>
      <param name="message">An error message that explains the reason for the exception.</param>
      <param name="innerException">The <see cref="T:System.Exception" /> that caused this exception.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.EventDrivenActivity">
      <summary>Wraps an <see cref="T:System.Workflow.ComponentModel.Activity" /> whose execution is initialized by an event. This class cannot be inherited. </summary>
    </member>
    <member name="M:System.Workflow.Activities.EventDrivenActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.EventDrivenActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.EventDrivenActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.EventDrivenActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-identified name of the activity.</param>
    </member>
    <member name="P:System.Workflow.Activities.EventDrivenActivity.EventActivity">
      <summary>Gets the event activity that drives the change.</summary>
      <returns>The event activity that drives the change.</returns>
    </member>
    <member name="T:System.Workflow.Activities.EventHandlersActivity">
      <summary>A composite activity that contains a collection of event handlers. It is typically used where a collection of events must be handled. Each event is handled by one <see cref="T:System.Workflow.Activities.EventDrivenActivity" />, which is part of <see cref="T:System.Workflow.Activities.EventHandlersActivity" />.</summary>
    </member>
    <member name="M:System.Workflow.Activities.EventHandlersActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.EventHandlersActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.EventHandlersActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.EventHandlersActivity" /> class by using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="M:System.Workflow.Activities.EventHandlersActivity.GetDynamicActivity(System.String)">
      <summary>Gets the currently executing instance of the <see cref="T:System.Workflow.Activities.EventDrivenActivity" />.</summary>
      <returns>The currently executing instance of the <see cref="T:System.Workflow.Activities.EventDrivenActivity" />.</returns>
      <param name="childActivityName">The name of the child <see cref="T:System.Workflow.Activities.EventDrivenActivity" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="childactivity" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">The executable activities do not contain the <paramref name="childactivity" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.EventHandlersActivity.System#Workflow#ComponentModel#IActivityEventListener{T}#OnEvent(System.Object,System.Workflow.ComponentModel.ActivityExecutionStatusChangedEventArgs)">
      <summary>Defines the processing procedure when the subscribed-to event occurs.</summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">The previously typed event arguments.</param>
    </member>
    <member name="T:System.Workflow.Activities.EventHandlingScopeActivity">
      <summary>Enables event handling with the execution of the child activities. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.EventHandlingScopeActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.EventHandlingScopeActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.EventHandlingScopeActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.EventHandlingScopeActivity" /> class using the name of the name of the activity.</summary>
      <param name="name">The user defined name of the activity.</param>
    </member>
    <member name="M:System.Workflow.Activities.EventHandlingScopeActivity.System#Workflow#ComponentModel#IActivityEventListener{T}#OnEvent(System.Object,System.Workflow.ComponentModel.ActivityExecutionStatusChangedEventArgs)">
      <summary>Defines the processing procedure when the subscribed-to event occurs.</summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">The previously typed event arguments.</param>
    </member>
    <member name="T:System.Workflow.Activities.EventQueueName">
      <summary>Represents the name of a queue associated with an event on a <see cref="T:System.Workflow.Runtime.WorkflowInstance" />. This class cannot be inherited. </summary>
    </member>
    <member name="M:System.Workflow.Activities.EventQueueName.#ctor(System.Type,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.EventQueueName" /> class by using the specified <see cref="T:System.Type" /> and operation name. </summary>
      <param name="interfaceType">The <see cref="T:System.Type" /> of the interface to associate with the <see cref="T:System.Workflow.Activities.EventQueueName" />.</param>
      <param name="operation">A string that contains the name of the operation to associate with the <see cref="T:System.Workflow.Activities.EventQueueName" />.</param>
    </member>
    <member name="M:System.Workflow.Activities.EventQueueName.#ctor(System.Type,System.String,System.Collections.Generic.ICollection{System.Workflow.Runtime.CorrelationProperty})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.EventQueueName" /> class.  </summary>
      <param name="interfaceType">The <see cref="T:System.Type" /> of the interface</param>
      <param name="operation">A string that contains the name of the operation to associate with the <see cref="T:System.Workflow.Activities.EventQueueName" />.</param>
      <param name="propertyValues">A <see cref="T:System.Workflow.Runtime.CorrelationProperty" /> collection that defines the property values to associate with <see cref="T:System.Workflow.Activities.EventQueueName" />.</param>
    </member>
    <member name="M:System.Workflow.Activities.EventQueueName.CompareTo(System.Object)">
      <summary>Compares the current <see cref="T:System.Workflow.Activities.EventQueueName" /> to the specified <see cref="T:System.Workflow.Activities.EventQueueName" />. </summary>
      <returns>A 32-bit signed integer that indicates the whether the comprarands match. -1 indicates that the current <see cref="T:System.Workflow.Activities.EventQueueName" /> and the specified object do not match; 0 indicates that the <see cref="T:System.Workflow.Activities.EventQueueName" /> and the specified object match.</returns>
      <param name="toCompare">The <see cref="T:System.Object" /> to compare with the current <see cref="T:System.Workflow.Activities.EventQueueName" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.EventQueueName.CompareTo(System.Workflow.Activities.EventQueueName)">
      <summary>Compares the current <see cref="T:System.Workflow.Activities.EventQueueName" /> to the specified <see cref="T:System.Workflow.Activities.EventQueueName" />.</summary>
      <returns>A 32-bit signed integer that indicates the whether the comprarands match. -1 indicates that the two <see cref="T:System.Workflow.Activities.EventQueueName" /> objects do not match; 0 indicates that the two <see cref="T:System.Workflow.Activities.EventQueueName" /> objects match.</returns>
      <param name="eventQueueName">The <see cref="T:System.Workflow.Activities.EventQueueName" /> to compare with the current <see cref="T:System.Workflow.Activities.EventQueueName" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.EventQueueName.Equals(System.Object)">
      <summary>Determines whether the specified object is equal to the current <see cref="T:System.Workflow.Activities.EventQueueName" />.</summary>
      <returns>true if the specified The <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Workflow.Activities.EventQueueName" />; otherwise, false. </returns>
      <param name="obj">The <see cref="T:System.Object" /> to compare with the current <see cref="T:System.Workflow.Activities.EventQueueName" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.EventQueueName.GetCorrelationValues">
      <summary>Returns a <see cref="T:System.Workflow.Runtime.CorrelationProperty" /> array that contains all correlation properties associated with the <see cref="T:System.Workflow.Activities.EventQueueName" />.</summary>
      <returns>The <see cref="T:System.Workflow.Runtime.CorrelationProperty" /> array that contains all correlation properties associated with the <see cref="T:System.Workflow.Activities.EventQueueName" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.EventQueueName.GetHashCode">
      <summary>Returns a hash code for the current <see cref="T:System.Workflow.Activities.EventQueueName" />.</summary>
      <returns>An integer that represents the hash code generated for the current <see cref="T:System.Workflow.Activities.EventQueueName" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.EventQueueName.InterfaceType">
      <summary>Gets the <see cref="T:System.Type" /> associated with the <see cref="T:System.Workflow.Activities.EventQueueName" />.</summary>
      <returns>The <see cref="T:System.Type" /> associated with the <see cref="T:System.Workflow.Activities.EventQueueName" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.EventQueueName.MethodName">
      <summary>Gets the method name associated with the <see cref="T:System.Workflow.Activities.EventQueueName" />.</summary>
      <returns>A string that contains the method name associated with the <see cref="T:System.Workflow.Activities.EventQueueName" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.EventQueueName.op_Equality(System.Workflow.Activities.EventQueueName,System.Workflow.Activities.EventQueueName)">
      <summary>Returns a value indicating whether the two specified <see cref="T:System.Workflow.Activities.EventQueueName" /> objects are the equal. </summary>
      <returns>true if <paramref name="queueKey1" /> and <paramref name="queueKey2" /> are equal; otherwise, false. </returns>
      <param name="queueKey1">The first <see cref="T:System.Workflow.Activities.EventQueueName" /> to compare.</param>
      <param name="queueKey2">The second <see cref="T:System.Workflow.Activities.EventQueueName" /> to compare.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.EventQueueName.op_GreaterThan(System.Workflow.Activities.EventQueueName,System.Workflow.Activities.EventQueueName)">
      <summary>Returns a value indicating whether the first of two specified <see cref="T:System.Workflow.Activities.EventQueueName" /> objects is greater than the second.</summary>
      <returns>true if <paramref name="queueKey1" /> is greater than <paramref name="queueKey2" />; otherwise, false. </returns>
      <param name="queueKey1">The first <see cref="T:System.Workflow.Activities.EventQueueName" /> to compare.</param>
      <param name="queueKey2">The second <see cref="T:System.Workflow.Activities.EventQueueName" /> to compare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="queueKey1" /> or <paramref name="queueKey2" /> contains a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.EventQueueName.op_Inequality(System.Workflow.Activities.EventQueueName,System.Workflow.Activities.EventQueueName)">
      <summary>Returns a value indicating whether the two specified <see cref="T:System.Workflow.Activities.EventQueueName" /> objects are the not equal. </summary>
      <returns>true if <paramref name="queueKey1" /> and <paramref name="queueKey2" /> are not equal; otherwise, false. </returns>
      <param name="queueKey1">The first <see cref="T:System.Workflow.Activities.EventQueueName" /> to compare.</param>
      <param name="queueKey2">The second <see cref="T:System.Workflow.Activities.EventQueueName" /> to compare.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.EventQueueName.op_LessThan(System.Workflow.Activities.EventQueueName,System.Workflow.Activities.EventQueueName)">
      <summary>Returns a value indicating whether the first of two specified <see cref="T:System.Workflow.Activities.EventQueueName" /> objects is less than the second.</summary>
      <returns>true if <paramref name="queueKey1" /> is greater than <paramref name="queueKey2" />; otherwise, false. </returns>
      <param name="queueKey1">The first <see cref="T:System.Workflow.Activities.EventQueueName" /> to compare.</param>
      <param name="queueKey2">The second <see cref="T:System.Workflow.Activities.EventQueueName" /> to compare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="queueKey1" /> or <paramref name="queueKey2" /> contains a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.EventQueueName.ToString">
      <summary>Returns a <see cref="T:System.String" /> representation of the current <see cref="T:System.Workflow.Activities.EventQueueName" />.</summary>
      <returns>A <see cref="T:System.String" /> representation of the current <see cref="T:System.Workflow.Activities.EventQueueName" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.ExecutionType">
      <summary>Specifies the execution mode for activities.</summary>
    </member>
    <member name="F:System.Workflow.Activities.ExecutionType.Sequence">
      <summary>Executes activities in sequential order. Each activity is executed in turn, after the previous activity has finished running. </summary>
    </member>
    <member name="F:System.Workflow.Activities.ExecutionType.Parallel">
      <summary>Executes activities in parallel.</summary>
    </member>
    <member name="T:System.Workflow.Activities.ExternalDataEventArgs">
      <summary>Represents the data sent when an event is raised using the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> activity. </summary>
    </member>
    <member name="M:System.Workflow.Activities.ExternalDataEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ExternalDataEventArgs" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.ExternalDataEventArgs.#ctor(System.Guid)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ExternalDataEventArgs" /> class using the instance identifier of the workflow.</summary>
      <param name="instanceId">The workflow instance identifier for the workflow instance that contains the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> that is expected to handle the event.</param>
    </member>
    <member name="M:System.Workflow.Activities.ExternalDataEventArgs.#ctor(System.Guid,System.Workflow.Runtime.IPendingWork,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ExternalDataEventArgs" /> class. </summary>
      <param name="instanceId">The workflow instance identifier for the workflow instance that contains the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> that is expected to handle the event.</param>
      <param name="workHandler">The <see cref="T:System.Workflow.Runtime.IPendingWork" /> to allow the external code, raising the event, to participate in the batch.</param>
      <param name="workItem">The object that contains the external code that raises the event.</param>
    </member>
    <member name="M:System.Workflow.Activities.ExternalDataEventArgs.#ctor(System.Guid,System.Workflow.Runtime.IPendingWork,System.Object,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ExternalDataEventArgs" /> class. </summary>
      <param name="instanceId">The workflow instance identifier for the workflow instance that contains the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> that is expected to handle the event.</param>
      <param name="workHandler">The <see cref="T:System.Workflow.Runtime.IPendingWork" /> to allow the external code that raises the event to participate in the batch.</param>
      <param name="workItem">The object that contains the external code that raises the event.</param>
      <param name="waitForIdle">A value that indicates whether the workflow should go idle before raising the event; otherwise, false.</param>
    </member>
    <member name="P:System.Workflow.Activities.ExternalDataEventArgs.Identity">
      <summary>Gets or sets the identity of the user that raised the event. </summary>
      <returns>The identity of the user that is raising the event.</returns>
    </member>
    <member name="P:System.Workflow.Activities.ExternalDataEventArgs.InstanceId">
      <summary>Gets or sets the workflow instance identifier for the workflow instance that contains the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> that is expected to handle the event.</summary>
      <returns>The workflow instance identifier for the workflow instance that contains the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> that is expected to handle the event.</returns>
    </member>
    <member name="P:System.Workflow.Activities.ExternalDataEventArgs.WaitForIdle">
      <summary>Gets or sets a value that indicates whether the event should be raised immediately, or if the workflow should go idle before raising the event.</summary>
      <returns>true if the workflow should go idle before raising the event; otherwise, false.</returns>
    </member>
    <member name="P:System.Workflow.Activities.ExternalDataEventArgs.WorkHandler">
      <summary>Gets or sets the <see cref="T:System.Workflow.Runtime.IPendingWork" /> to allow the external code, raising the event, to participate in the batch.</summary>
      <returns>The <see cref="T:System.Workflow.Runtime.IPendingWork" /> to allow the external code, raising the event, to participate in the batch.</returns>
    </member>
    <member name="P:System.Workflow.Activities.ExternalDataEventArgs.WorkItem">
      <summary>Gets or sets the object that contains the external code that raises the event.</summary>
      <returns>The object that contains the external code that raises the event.</returns>
    </member>
    <member name="T:System.Workflow.Activities.ExternalDataExchangeAttribute">
      <summary>Marks an interface as a local service interface. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.ExternalDataExchangeAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ExternalDataExchangeAttribute" />.</summary>
    </member>
    <member name="T:System.Workflow.Activities.ExternalDataExchangeService">
      <summary>Represents a service that must be added to the workflow run-time engine for local services communications to be enabled. Local service implementations are required to be added to the <see cref="T:System.Workflow.Activities.ExternalDataExchangeService" /> for these services to be properly initialized and registered.</summary>
    </member>
    <member name="M:System.Workflow.Activities.ExternalDataExchangeService.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ExternalDataExchangeService" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.ExternalDataExchangeService.#ctor(System.Collections.Specialized.NameValueCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ExternalDataExchangeService" /> class.  </summary>
      <param name="parameters">A collection of parameters used to configure the service. The allowed values are an empty collection or a collection that contains only the ConfigurationSection key.</param>
    </member>
    <member name="M:System.Workflow.Activities.ExternalDataExchangeService.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ExternalDataExchangeService" /> class. </summary>
      <param name="configSectionName">The name of the configuration section that contains this service's desired configuration.</param>
    </member>
    <member name="M:System.Workflow.Activities.ExternalDataExchangeService.#ctor(System.Workflow.Activities.ExternalDataExchangeServiceSection)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ExternalDataExchangeService" /> class.  </summary>
      <param name="settings">The configuration section that contains settings for this service.</param>
    </member>
    <member name="M:System.Workflow.Activities.ExternalDataExchangeService.AddService(System.Object)">
      <summary>Adds the specified service to the <see cref="T:System.Workflow.Activities.ExternalDataExchangeService" />.</summary>
      <param name="service">An object that represents the service to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="service" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">The external run-time container was not found.</exception>
    </member>
    <member name="M:System.Workflow.Activities.ExternalDataExchangeService.GetService(System.Type)">
      <summary>Gets the service implementation of the specified interface type, if the service is available.</summary>
      <returns>An object that implements the requested service, or null (Nothing in Visual Basic) if the service cannot be resolved.</returns>
      <param name="serviceType">The <see cref="T:System.Type" /> of the interface implemented by the service to retrieve.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="service" /> is a null reference (Nothing).</exception>
      <exception cref="T:System.InvalidOperationException">The service is not registered with the runtime.</exception>
    </member>
    <member name="M:System.Workflow.Activities.ExternalDataExchangeService.RemoveService(System.Object)">
      <summary>Removes the specified service from the <see cref="T:System.Workflow.Activities.ExternalDataExchangeService" />.</summary>
      <param name="service">An object that represents the service to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="service" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">The service is not registered with the runtime.</exception>
    </member>
    <member name="M:System.Workflow.Activities.ExternalDataExchangeService.Start">
      <summary>Adds local services if a configuration section was specified during construction of the object.</summary>
    </member>
    <member name="T:System.Workflow.Activities.ExternalDataExchangeServiceSection">
      <summary>Represents a configuration section that allows you to specify, in a configuration file, a set of services to be added to an instance of <see cref="T:System.Workflow.Activities.ExternalDataExchangeService" />.</summary>
    </member>
    <member name="M:System.Workflow.Activities.ExternalDataExchangeServiceSection.#ctor">
      <summary>Initializes an instance of the <see cref="T:System.Workflow.Activities.ExternalDataExchangeServiceSection" /> class. </summary>
    </member>
    <member name="P:System.Workflow.Activities.ExternalDataExchangeServiceSection.Services">
      <summary>Gets the collection of services to be added to an instance of <see cref="T:System.Workflow.Activities.ExternalDataExchangeService" />.</summary>
      <returns>A collection of service elements.</returns>
    </member>
    <member name="T:System.Workflow.Activities.HandleExternalEventActivity">
      <summary>Defines a workflow communication activity that is used to handle an event that is raised by a local service. </summary>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> class.</summary>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivity.Cancel(System.Workflow.ComponentModel.ActivityExecutionContext)">
      <summary>Cancels the handling of the event.</summary>
      <returns>The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> of the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> at the end of the requested operation.</returns>
      <param name="executionContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> that represents the execution environment of the <see cref="T:System.Workflow.ComponentModel.Activity" />.</param>
    </member>
    <member name="P:System.Workflow.Activities.HandleExternalEventActivity.CorrelationToken">
      <summary>Gets or sets the binding to a <see cref="T:System.Workflow.Runtime.CorrelationToken" />. </summary>
      <returns>This property is used to correlate an event to the correct <see cref="T:System.Workflow.Activities.IEventActivity" />-inherited activity based on the payload of the event. For example, when a single workflow instance is listening for multiple instances of the same event at the same time, the CorrelationToken property is used to route the event to the proper activity in that workflow instance.This correlation should not be confused with correlating an event to the correct workflow instance. The correlation to the correct workflow instance is done by sending the event to an explicit workflow instance and using the <see cref="P:System.Workflow.Activities.ExternalDataEventArgs.InstanceId" /> property to properly identify the correct workflow instance. correlation, see the Correlated Local Service Sample.</returns>
    </member>
    <member name="F:System.Workflow.Activities.HandleExternalEventActivity.CorrelationTokenProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.HandleExternalEventActivity.CorrelationToken" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.HandleExternalEventActivity.EventName">
      <summary>Gets the name of the raised event. This property must be set before local communication can occur.</summary>
      <returns>The name of the raised event.</returns>
    </member>
    <member name="F:System.Workflow.Activities.HandleExternalEventActivity.EventNameProperty">
      <summary>Corresponds to the name of an event that is defined in an interface that was marked with the <see cref="T:System.Workflow.Activities.ExternalDataExchangeAttribute" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivity.Execute(System.Workflow.ComponentModel.ActivityExecutionContext)">
      <summary>Tries to run the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> associated with the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" />.</summary>
      <returns>The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> of the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> at the end of the requested operation.</returns>
      <param name="executionContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> associated with the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" />.</param>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivity.HandleFault(System.Workflow.ComponentModel.ActivityExecutionContext,System.Exception)">
      <summary>Called when an exception is raised within the context of the execution of this instance.</summary>
      <returns>The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionStatus" /> that results from an attempt to cancel this instance.</returns>
      <param name="executionContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> for this instance.</param>
      <param name="exception">The <see cref="T:System.Exception" /> that caused this fault.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="executionContext" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivity.Initialize(System.IServiceProvider)">
      <summary>Initializes the event with the service provider.</summary>
      <param name="provider">The <see cref="T:System.IServiceProvider" /> that provides custom support to the class. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="provider" /> is a null reference (Nothing in Visual Basic), the type of the service provider is a null reference (Nothing), or the proxy for the service provider is a null reference (Nothing). </exception>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivity.InitializeProperties">
      <summary>Performs initialization on dependency properties.</summary>
    </member>
    <member name="P:System.Workflow.Activities.HandleExternalEventActivity.InterfaceType">
      <summary>Gets or sets the <see cref="T:System.Workflow.Activities.ExternalDataExchangeAttribute" /> attributed interface type of the local service whose event is handled. This property must be set before local communication can occur.</summary>
      <returns>The interface type of the local service whose event is handled.</returns>
    </member>
    <member name="F:System.Workflow.Activities.HandleExternalEventActivity.InterfaceTypeProperty">
      <summary>Corresponds to the name of the interface that was marked with the <see cref="T:System.Workflow.Activities.ExternalDataExchangeAttribute" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Workflow.Activities.HandleExternalEventActivity.Invoked">
      <summary>Occurs when the external event is received.</summary>
    </member>
    <member name="F:System.Workflow.Activities.HandleExternalEventActivity.InvokedEvent">
      <summary>Defines an event delegate that is executed after the activity receives the expected event from the local service.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivity.OnClosed(System.IServiceProvider)">
      <summary>Called by the workflow runtime as part of the activity's transition to the Closed state.</summary>
      <param name="provider">The <see cref="T:System.IServiceProvider" /> that provides the service.</param>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivity.OnInvoked(System.EventArgs)">
      <summary>Called just after the external event is received to allow derived classes to process the inbound <see cref="T:System.EventArgs" /> before the activity closes.</summary>
      <param name="e">The <see cref="T:System.EventArgs" /> that are received from the external event that was just received.</param>
    </member>
    <member name="P:System.Workflow.Activities.HandleExternalEventActivity.ParameterBindings">
      <summary>Gets the collection of parameter bindings.</summary>
      <returns>The collection of parameter bindings.</returns>
    </member>
    <member name="F:System.Workflow.Activities.HandleExternalEventActivity.ParameterBindingsProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.HandleExternalEventActivity.ParameterBindings" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.HandleExternalEventActivity.Roles">
      <summary>Gets or sets a reference to a workflow role collection.</summary>
      <returns>A collection of a workflow role.</returns>
    </member>
    <member name="F:System.Workflow.Activities.HandleExternalEventActivity.RolesProperty">
      <summary>Identifies the valid user roles that are allowed to send messages from the host to this activity. The two types of supported roles are Active Directory and ASP.NET.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.HandleExternalEventActivity.System#Workflow#Activities#IEventActivity#QueueName">
      <summary>Gets the name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> on which the activity is waiting for data to arrive.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> on which the activity is waiting for data to arrive. </returns>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivity.System#Workflow#Activities#IEventActivity#Subscribe(System.Workflow.ComponentModel.ActivityExecutionContext,System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.ComponentModel.QueueEventArgs})">
      <summary>Creates the subscription of the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> activity to an event.</summary>
      <param name="parentContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> that represents the execution environment of the <see cref="T:System.Workflow.ComponentModel.Activity" />.</param>
      <param name="parentEventHandler">The <see cref="T:System.EventHandler" /> for the parent event.</param>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivity.System#Workflow#Activities#IEventActivity#Unsubscribe(System.Workflow.ComponentModel.ActivityExecutionContext,System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.ComponentModel.QueueEventArgs})">
      <summary>Cancels the subscription of the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> activity to an event.</summary>
      <param name="parentContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> that represents the execution environment of the activity.</param>
      <param name="parentEventHandler"> The <see cref="T:System.EventHandler" /> for the parent event.</param>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivity.System#Workflow#ComponentModel#IActivityEventListener{T}#OnEvent(System.Object,System.Workflow.ComponentModel.QueueEventArgs)">
      <summary>Defines the processing procedure when the subscribed-to event occurs. </summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">The previously typed event arguments.</param>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivity.System#Workflow#ComponentModel#IDynamicPropertyTypeProvider#GetAccessType(System.IServiceProvider,System.String)">
      <summary>Returns the access type for the specified property.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Compiler.AccessTypes" /> enumeration value that denote the access level of the property.</returns>
      <param name="serviceProvider">An object that implements <see cref="T:System.IServiceProvider" /> to provide access to design time services if they are required.</param>
      <param name="propertyName">The name of the property. </param>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivity.System#Workflow#ComponentModel#IDynamicPropertyTypeProvider#GetPropertyType(System.IServiceProvider,System.String)">
      <summary>Returns the <see cref="T:System.Type" /> of the specified property.</summary>
      <returns>The <see cref="T:System.Type" /> for the property whose name is passed as the <paramref name="propertyName " />parameter. </returns>
      <param name="serviceProvider">An object that implements <see cref="T:System.IServiceProvider" /> to provide access to design time services if they are required.</param>
      <param name="propertyName">The name of the property. </param>
    </member>
    <member name="T:System.Workflow.Activities.HandleExternalEventActivityValidator">
      <summary>Verifies that the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> class is configured correctly.</summary>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivityValidator.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.HandleExternalEventActivityValidator" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.HandleExternalEventActivityValidator.Validate(System.Workflow.ComponentModel.Compiler.ValidationManager,System.Object)">
      <summary>Validates the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" /> class during workflow compilation.</summary>
      <returns>A <see cref="T:System.Workflow.ComponentModel.Compiler.ValidationErrorCollection" /> that contains the errors from this operation.</returns>
      <param name="manager">The <see cref="T:System.Workflow.ComponentModel.Compiler.ValidationManager" /> to use for this validation.</param>
      <param name="obj">The <see cref="T:System.Object" /> to validate.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Workflow.Activities.IEventActivity">
      <summary>Provides methods and properties that event-driven activities must derive from, to subscribe to events.</summary>
    </member>
    <member name="P:System.Workflow.Activities.IEventActivity.QueueName">
      <summary>Gets the name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> on which the activity is waiting for data to arrive.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> on which the activity is waiting for data to arrive.</returns>
    </member>
    <member name="M:System.Workflow.Activities.IEventActivity.Subscribe(System.Workflow.ComponentModel.ActivityExecutionContext,System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.ComponentModel.QueueEventArgs})">
      <summary>Creates the subscription of an <see cref="T:System.Workflow.ComponentModel.Activity" /> to an event.</summary>
      <param name="parentContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> that represents the execution environment of the <see cref="T:System.Workflow.ComponentModel.Activity" />.</param>
      <param name="parentEventHandler">The <see cref="T:System.EventHandler" /> for the parent event.</param>
    </member>
    <member name="M:System.Workflow.Activities.IEventActivity.Unsubscribe(System.Workflow.ComponentModel.ActivityExecutionContext,System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.ComponentModel.QueueEventArgs})">
      <summary>Cancels the subscription of an <see cref="T:System.Workflow.ComponentModel.Activity" /> to an event.</summary>
      <param name="parentContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> that represents the execution environment of the <see cref="T:System.Workflow.ComponentModel.Activity" />.</param>
      <param name="parentEventHandler">The <see cref="T:System.EventHandler" /> for the parent event.</param>
    </member>
    <member name="T:System.Workflow.Activities.IfElseActivity">
      <summary>Conditionally runs one of two or more activities of type <see cref="T:System.Workflow.Activities.IfElseBranchActivity" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.IfElseActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.IfElseActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.IfElseActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.IfElseActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="M:System.Workflow.Activities.IfElseActivity.AddBranch(System.Collections.Generic.ICollection{System.Workflow.ComponentModel.Activity})">
      <summary>Adds a new branch to the <see cref="T:System.Workflow.Activities.IfElseActivity" /> using a collection of new activities to add to the new branch.</summary>
      <returns>The updated <see cref="T:System.Workflow.Activities.IfElseBranchActivity" /> with the new branches.</returns>
      <param name="activities">A collection of activities to add to the new branch.</param>
    </member>
    <member name="M:System.Workflow.Activities.IfElseActivity.AddBranch(System.Collections.Generic.ICollection{System.Workflow.ComponentModel.Activity},System.Workflow.ComponentModel.ActivityCondition)">
      <summary>Adds a new branch to the <see cref="T:System.Workflow.Activities.IfElseActivity" /> using a collection of new activities to add to the new branch.</summary>
      <returns>The updated <see cref="T:System.Workflow.Activities.IfElseBranchActivity" /> with the new branches.</returns>
      <param name="activities">A collection of activities to add to the new branch.</param>
      <param name="branchCondition">An <see cref="T:System.Workflow.ComponentModel.ActivityCondition" /> that determines whether the branch should be run.</param>
    </member>
    <member name="M:System.Workflow.Activities.IfElseActivity.System#Workflow#ComponentModel#IActivityEventListener{T}#OnEvent(System.Object,System.Workflow.ComponentModel.ActivityExecutionStatusChangedEventArgs)">
      <summary>Defines the processing procedure when a change the activity execution status occurs.</summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">The previously typed event arguments.</param>
    </member>
    <member name="T:System.Workflow.Activities.IfElseBranchActivity">
      <summary>Represents a branch of an <see cref="T:System.Workflow.Activities.IfElseActivity" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.IfElseBranchActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.IfElseBranchActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.IfElseBranchActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.IfElseBranchActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="P:System.Workflow.Activities.IfElseBranchActivity.Condition">
      <summary>Gets or sets an <see cref="T:System.Workflow.ComponentModel.ActivityCondition" /> object reference for the current branch activity. The evaluation of this condition returns a value that indicates whether this branch should be run.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.ActivityCondition" /> that returns a value that indicates whether this branch should be run.</returns>
    </member>
    <member name="F:System.Workflow.Activities.IfElseBranchActivity.ConditionProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.IfElseBranchActivity.Condition" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.InvokeWebServiceActivity">
      <summary>Invokes a Web service through a proxy class, which passes and receives parameters as specified. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.InvokeWebServiceActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.InvokeWebServiceActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.InvokeWebServiceActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.InvokeWebServiceActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="E:System.Workflow.Activities.InvokeWebServiceActivity.Invoked">
      <summary>Occurs when the Web service is invoked.</summary>
    </member>
    <member name="F:System.Workflow.Activities.InvokeWebServiceActivity.InvokedEvent">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="E:System.Workflow.Activities.InvokeWebServiceActivity.Invoked" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Workflow.Activities.InvokeWebServiceActivity.Invoking">
      <summary>Occurs when the Web service is being invoked.</summary>
    </member>
    <member name="F:System.Workflow.Activities.InvokeWebServiceActivity.InvokingEvent">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="E:System.Workflow.Activities.InvokeWebServiceActivity.Invoking" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.InvokeWebServiceActivity.MethodName">
      <summary>Gets or sets the method name of the proxy class that is called on the Web service.</summary>
      <returns>The method name of the proxy class.  The default is an empty <see cref="T:System.String" />.</returns>
    </member>
    <member name="F:System.Workflow.Activities.InvokeWebServiceActivity.MethodNameProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.InvokeWebServiceActivity.MethodName" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.InvokeWebServiceActivity.ParameterBindings">
      <summary>Gets the collection of parameter bindings for the method specified by the <see cref="P:System.Workflow.Activities.InvokeWebServiceActivity.MethodName" /> property.</summary>
      <returns>A <see cref="T:System.Workflow.ComponentModel.WorkflowParameterBindingCollection" /> for the method, specified by <see cref="P:System.Workflow.Activities.InvokeWebServiceActivity.MethodName" /> property. The default is an empty collection of type <see cref="T:System.Workflow.ComponentModel.WorkflowParameterBindingCollection" />.</returns>
    </member>
    <member name="F:System.Workflow.Activities.InvokeWebServiceActivity.ParameterBindingsProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.InvokeWebServiceActivity.ParameterBindings" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.InvokeWebServiceActivity.ProxyClass">
      <summary>Gets or sets the type of the proxy class used to communicate with the Web service.</summary>
      <returns>
        <see cref="T:System.Type" /> of the proxy used to start the Web service. The default is an empty <see cref="T:System.String" />.</returns>
    </member>
    <member name="F:System.Workflow.Activities.InvokeWebServiceActivity.ProxyClassProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.InvokeWebServiceActivity.ProxyClass" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.InvokeWebServiceActivity.SessionId">
      <summary>Gets or sets a session identifier that indicates whether the session is shared across an activity, or if a new session is used.</summary>
      <returns>An identifier that either contains the <see cref="P:System.Workflow.Activities.InvokeWebServiceActivity.SessionID" /> of a previous session or an empty <see cref="T:System.String" /> that indicates that a new session will be used. The default is an empty <see cref="T:System.String" />.</returns>
    </member>
    <member name="F:System.Workflow.Activities.InvokeWebServiceActivity.SessionIdProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.InvokeWebServiceActivity.SessionId" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.InvokeWebServiceActivity.System#Workflow#ComponentModel#IDynamicPropertyTypeProvider#GetAccessType(System.IServiceProvider,System.String)">
      <summary>Returns the access type for the specified property.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Compiler.AccessTypes" /> enumeration value that denotes the access level of the property.</returns>
      <param name="serviceProvider">An object that implements <see cref="T:System.IServiceProvider" /> to provide access to design time services if they are required.</param>
      <param name="propertyName">The name of the property. </param>
    </member>
    <member name="M:System.Workflow.Activities.InvokeWebServiceActivity.System#Workflow#ComponentModel#IDynamicPropertyTypeProvider#GetPropertyType(System.IServiceProvider,System.String)">
      <summary>Returns the <see cref="T:System.Type" /> of the specified property.</summary>
      <returns>The <see cref="T:System.Type" /> for the property whose name is passed as the <paramref name="propertyName " />parameter. </returns>
      <param name="serviceProvider">An object that implements <see cref="T:System.IServiceProvider" /> to provide access to design time services if they are required.</param>
      <param name="propertyName">The name of the property. </param>
    </member>
    <member name="T:System.Workflow.Activities.InvokeWebServiceEventArgs">
      <summary>Contains event data used to invoke a Web service. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.InvokeWebServiceEventArgs.#ctor(System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.InvokeWebServiceEventArgs" /> class. </summary>
      <param name="proxyInstance">The Web service proxy <see cref="T:System.Object" /> to use for outgoing Web service calls.</param>
    </member>
    <member name="P:System.Workflow.Activities.InvokeWebServiceEventArgs.WebServiceProxy">
      <summary>Gets the proxy class that communicates with the Web service.</summary>
      <returns>The proxy <see cref="T:System.Object" /> that communicates with the Web service.</returns>
    </member>
    <member name="T:System.Workflow.Activities.InvokeWorkflowActivity">
      <summary>Asynchronously runs one workflow from another. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.InvokeWorkflowActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.InvokeWorkflowActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.InvokeWorkflowActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.InvokeWorkflowActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="P:System.Workflow.Activities.InvokeWorkflowActivity.InstanceId">
      <summary>Gets the <see cref="T:System.Guid" /> that indicates the newly created workflow instance.</summary>
      <returns>The <see cref="T:System.Guid" /> that indicates the newly created workflow instance.</returns>
    </member>
    <member name="F:System.Workflow.Activities.InvokeWorkflowActivity.InstanceIdProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.InvokeWorkflowActivity.InstanceId" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Workflow.Activities.InvokeWorkflowActivity.Invoking">
      <summary>Occurs before the workflow is invoked. </summary>
    </member>
    <member name="F:System.Workflow.Activities.InvokeWorkflowActivity.InvokingEvent">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="E:System.Workflow.Activities.InvokeWorkflowActivity.Invoking" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.InvokeWorkflowActivity.ParameterBindings">
      <summary>Gets the collection of parameters to bind to. </summary>
      <returns>A <see cref="T:System.Workflow.ComponentModel.WorkflowParameterBindingCollection" /> that contains the parameter bindings for the workflow being called. The default is an empty <see cref="T:System.Workflow.ComponentModel.WorkflowParameterBindingCollection" />.</returns>
    </member>
    <member name="F:System.Workflow.Activities.InvokeWorkflowActivity.ParameterBindingsProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="F:System.Workflow.Activities.InvokeWorkflowActivity.ParameterBindingsProperty" />property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.InvokeWorkflowActivity.System#Workflow#ComponentModel#Design#ITypeFilterProvider#CanFilterType(System.Type,System.Boolean)">
      <summary>Determines whether the <see cref="T:System.Type" /> can be filtered by the workflow.</summary>
      <returns>true if the specified <see cref="T:System.Type" /> can be filtered; otherwise, false.</returns>
      <param name="type">The type that the workflow must filter.</param>
      <param name="throwOnError">true to throw an error if the specified Type cannot be filtered; otherwise, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="throwonError" /> is true. This indicates that <paramref name="type" /> is a design time type.</exception>
      <exception cref="T:System.Exception">
        <paramref name="type" /> is not the root activity.</exception>
    </member>
    <member name="P:System.Workflow.Activities.InvokeWorkflowActivity.System#Workflow#ComponentModel#Design#ITypeFilterProvider#FilterDescription">
      <summary>Gets a description of the filter being used by the workflow.</summary>
      <returns>The description of the filter being used by the workflow.</returns>
    </member>
    <member name="P:System.Workflow.Activities.InvokeWorkflowActivity.TargetWorkflow">
      <summary>Gets or sets the <see cref="T:System.Type" /> of the workflow to be invoked.</summary>
      <returns>The fully-qualified name of the workflow to be invoked. The default is null.</returns>
    </member>
    <member name="F:System.Workflow.Activities.InvokeWorkflowActivity.TargetWorkflowProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.InvokeWorkflowActivity.TargetWorkflow" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.ListenActivity">
      <summary>Makes the workflow wait for any one of several possible events before the activity proceeds. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.ListenActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ListenActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.ListenActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ListenActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="M:System.Workflow.Activities.ListenActivity.System#Workflow#ComponentModel#IActivityEventListener{T}#OnEvent(System.Object,System.Workflow.ComponentModel.ActivityExecutionStatusChangedEventArgs)">
      <summary>Defines the processing procedure when the subscribed-to event occurs.</summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">The previously typed event arguments.</param>
    </member>
    <member name="T:System.Workflow.Activities.MessageEventSubscription">
      <summary>Creates a message event subscription to route messages to the appropriate workflow instance.</summary>
    </member>
    <member name="M:System.Workflow.Activities.MessageEventSubscription.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.MessageEventSubscription" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.MessageEventSubscription.#ctor(System.IComparable,System.Guid)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.MessageEventSubscription" /> class using a specified queue name and instance ID.</summary>
      <param name="queueName">The friendly name that identifies the queue.</param>
      <param name="instanceId">The <see cref="T:System.Guid" /> that indicates the workflow instance.</param>
    </member>
    <member name="M:System.Workflow.Activities.MessageEventSubscription.#ctor(System.IComparable,System.Guid,System.Guid)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.MessageEventSubscription" /> class using a specified queue name, instance ID, and subscription ID.</summary>
      <param name="queueName">The friendly name that identifies the queue.</param>
      <param name="instanceId">The <see cref="T:System.Guid" /> that indicates the workflow instance.</param>
      <param name="subscriptionId">The <see cref="T:System.Guid" /> that indicates the subscription identifier.</param>
    </member>
    <member name="M:System.Workflow.Activities.MessageEventSubscription.#ctor(System.IComparable,System.Guid,System.Type,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.MessageEventSubscription" /> class using a specified queue name, subscription ID, interface type, and operation name.</summary>
      <param name="queueName">The friendly name that identifies the queue.</param>
      <param name="subscriptionId">The <see cref="T:System.Guid" /> that indicates the subscription identifier.</param>
      <param name="interfaceType">The <see cref="T:System.Type" /> of the interface.</param>
      <param name="operation">The event name on the interface that represents the messaging operation for which you are creating a subscription.</param>
    </member>
    <member name="M:System.Workflow.Activities.MessageEventSubscription.#ctor(System.IComparable,System.Guid,System.Type,System.String,System.Guid)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.MessageEventSubscription" /> class using a specified queue name, instance ID, interface type, operation, and a subscription ID.</summary>
      <param name="queueName">The friendly name that identifies the queue.</param>
      <param name="instanceId">The <see cref="T:System.Guid" /> that indicates the workflow instance.</param>
      <param name="interfaceType">The <see cref="T:System.Type" /> of the interface.</param>
      <param name="operation">The event name on the interface that represents the messaging operation for which you are creating a subscription.</param>
      <param name="subscriptionId">The <see cref="T:System.Guid" /> that indicates the subscription identifier.</param>
    </member>
    <member name="P:System.Workflow.Activities.MessageEventSubscription.CorrelationProperties">
      <summary>Gets the collection of correlation properties for the message that will map the incoming message together with the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" />.</summary>
      <returns>A list of correlation properties for the message that will map the incoming message together with the <see cref="T:System.Workflow.Activities.HandleExternalEventActivity" />.</returns>
    </member>
    <member name="P:System.Workflow.Activities.MessageEventSubscription.InterfaceType">
      <summary>Gets or sets the interface type.</summary>
      <returns>The <see cref="T:System.Type" /> of the interface.</returns>
    </member>
    <member name="P:System.Workflow.Activities.MessageEventSubscription.MethodName">
      <summary>Gets or sets the name of the method.</summary>
      <returns>The name of the method.</returns>
    </member>
    <member name="P:System.Workflow.Activities.MessageEventSubscription.QueueName">
      <summary>Gets or sets the name of the queue to which this subscription belongs. </summary>
      <returns>The name that identifies the queue to which this subscription belongs.</returns>
    </member>
    <member name="P:System.Workflow.Activities.MessageEventSubscription.SubscriptionId">
      <summary>Gets or sets the unique identifier for this subscription.</summary>
      <returns>The <see cref="T:System.Guid" /> that indicates the subscription identifier.</returns>
    </member>
    <member name="P:System.Workflow.Activities.MessageEventSubscription.WorkflowInstanceId">
      <summary>Gets or sets the InstanceId of the workflow for which this subscription was created.</summary>
      <returns>The <see cref="T:System.Guid" /> that indicates the workflow instance.</returns>
    </member>
    <member name="T:System.Workflow.Activities.ParallelActivity">
      <summary>Runs a set of child activities at the same time. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.ParallelActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ParallelActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.ParallelActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ParallelActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="M:System.Workflow.Activities.ParallelActivity.System#Workflow#ComponentModel#IActivityEventListener{T}#OnEvent(System.Object,System.Workflow.ComponentModel.ActivityExecutionStatusChangedEventArgs)">
      <summary>Defines the processing procedure when the subscribed-to event occurs.</summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">The previously-typed event arguments.</param>
    </member>
    <member name="T:System.Workflow.Activities.PolicyActivity">
      <summary>Represents a collection of <see cref="T:System.Workflow.Activities.Rules.Rule" /> class instances to be run as part of a workflow's execution as a single step or activity.</summary>
    </member>
    <member name="M:System.Workflow.Activities.PolicyActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.PolicyActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.PolicyActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.PolicyActivity" /> using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="P:System.Workflow.Activities.PolicyActivity.RuleSetReference">
      <summary>Gets or sets a reference to a <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> in the .rules file.</summary>
      <returns>A <see cref="T:System.Workflow.Activities.Rules.RuleSetReference" /> to a <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> in the .rules file.</returns>
    </member>
    <member name="F:System.Workflow.Activities.PolicyActivity.RuleSetReferenceProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.PolicyActivity.RuleSetReference" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.ReplicatorActivity">
      <summary>Runs multiple instances of a child activity.</summary>
    </member>
    <member name="M:System.Workflow.Activities.ReplicatorActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.ReplicatorActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="P:System.Workflow.Activities.ReplicatorActivity.AllChildrenComplete">
      <summary>Gets a value that indicates whether all child instances are complete.</summary>
      <returns>true if all child instances are complete; otherwise, false.</returns>
    </member>
    <member name="E:System.Workflow.Activities.ReplicatorActivity.ChildCompleted">
      <summary>Occurs immediately after the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> finishes running a child activity instance.</summary>
    </member>
    <member name="F:System.Workflow.Activities.ReplicatorActivity.ChildCompletedEvent">
      <summary>Occurs when the child activity instance of the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> has completed.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Workflow.Activities.ReplicatorActivity.ChildInitialized">
      <summary>Occurs immediately after a child activity is initialized.</summary>
    </member>
    <member name="F:System.Workflow.Activities.ReplicatorActivity.ChildInitializedEvent">
      <summary>Occurs when the child activity instance of the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> has initialized.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Workflow.Activities.ReplicatorActivity.Completed">
      <summary>Occurs when the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> completes.</summary>
    </member>
    <member name="F:System.Workflow.Activities.ReplicatorActivity.CompletedEvent">
      <summary>Occurs when the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> has completed.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.ReplicatorActivity.CurrentChildData">
      <summary>Returns a list of all child activities.</summary>
      <returns>A list of all child activities.</returns>
    </member>
    <member name="P:System.Workflow.Activities.ReplicatorActivity.CurrentIndex">
      <summary>Gets the index position of the current child activity.</summary>
      <returns>The index position of the current child activity.</returns>
    </member>
    <member name="P:System.Workflow.Activities.ReplicatorActivity.DynamicActivities">
      <summary>Gets an array of running child activity instances. </summary>
      <returns>An array of currently running child activity instances.</returns>
    </member>
    <member name="P:System.Workflow.Activities.ReplicatorActivity.ExecutionType">
      <summary>Gets or sets the <see cref="T:System.Workflow.Activities.ExecutionType" /> for the <see cref="T:System.Workflow.Activities.ReplicatorActivity" />.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.ExecutionType" /> for the <see cref="T:System.Workflow.Activities.ReplicatorActivity" />.</returns>
    </member>
    <member name="F:System.Workflow.Activities.ReplicatorActivity.ExecutionTypeProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.ReplicatorActivity.ExecutionType" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.ReplicatorActivity.InitialChildData">
      <summary>Gets or sets a list of child activity data.</summary>
      <returns>A list of child activity data.</returns>
    </member>
    <member name="F:System.Workflow.Activities.ReplicatorActivity.InitialChildDataProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.ReplicatorActivity.InitialChildData" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Workflow.Activities.ReplicatorActivity.Initialized">
      <summary>Occurs when the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> begins to execute.</summary>
    </member>
    <member name="F:System.Workflow.Activities.ReplicatorActivity.InitializedEvent">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="E:System.Workflow.Activities.ReplicatorActivity.Initialized" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.ReplicatorActivity.IsExecuting(System.Int32)">
      <summary>Indicates whether the specified child activity is currently executing. </summary>
      <returns>true if the child activity is executing; otherwise, false.</returns>
      <param name="index">The index position of the child activity.</param>
    </member>
    <member name="P:System.Workflow.Activities.ReplicatorActivity.UntilCondition">
      <summary>Gets or sets a completion condition that indicates when the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> should finish.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.ActivityCondition" /> that determines whether the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> is finished.</returns>
    </member>
    <member name="F:System.Workflow.Activities.ReplicatorActivity.UntilConditionProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.ReplicatorActivity.UntilCondition" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.ReplicatorChildEventArgs">
      <summary>Contains event arguments for the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> activity handlers called at initialization of child <see cref="T:System.Workflow.ComponentModel.Activity" /> instances in addition to when the child <see cref="T:System.Workflow.ComponentModel.Activity" /> instances are finished.</summary>
    </member>
    <member name="M:System.Workflow.Activities.ReplicatorChildEventArgs.#ctor(System.Object,System.Workflow.ComponentModel.Activity)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.ReplicatorChildEventArgs" /> class. </summary>
      <param name="instanceData">Object instance that represents the data/execution context associated with the current child <see cref="T:System.Workflow.ComponentModel.Activity" /> instance executed through the <see cref="T:System.Workflow.Activities.ReplicatorActivity" />.</param>
      <param name="activity">Child <see cref="T:System.Workflow.ComponentModel.Activity" /> of the current <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> instance that raised the event.</param>
    </member>
    <member name="P:System.Workflow.Activities.ReplicatorChildEventArgs.Activity">
      <summary>Gets the child/template <see cref="T:System.Workflow.ComponentModel.Activity" /> of the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> instance that raised the event.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Activity" /> class instance that represents the child activity of the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> that raised the event.</returns>
    </member>
    <member name="P:System.Workflow.Activities.ReplicatorChildEventArgs.InstanceData">
      <summary>Gets the current item in the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> enumerable collection.</summary>
      <returns>The current item in the <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> enumerable collection.</returns>
    </member>
    <member name="T:System.Workflow.Activities.SequenceActivity">
      <summary>Runs a set of child activities according to a single defined ordering.</summary>
    </member>
    <member name="M:System.Workflow.Activities.SequenceActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.SequenceActivity" /> class.</summary>
    </member>
    <member name="M:System.Workflow.Activities.SequenceActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.SequenceActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="M:System.Workflow.Activities.SequenceActivity.Cancel(System.Workflow.ComponentModel.ActivityExecutionContext)">
      <summary>Cancels the execution of the activity.</summary>
      <returns>The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionStatus" /> of the activity after canceling the execution.</returns>
      <param name="executionContext">The execution context of the activity.</param>
    </member>
    <member name="M:System.Workflow.Activities.SequenceActivity.Execute(System.Workflow.ComponentModel.ActivityExecutionContext)">
      <summary>Executes the activity.</summary>
      <returns>The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionStatus" /> of the activity after executing the activity.</returns>
      <param name="executionContext">The execution context of the activity.</param>
    </member>
    <member name="M:System.Workflow.Activities.SequenceActivity.HandleFault(System.Workflow.ComponentModel.ActivityExecutionContext,System.Exception)">
      <summary>Called when an exception is raised within the context of the execution of this instance.</summary>
      <returns>The execution status of the activity.</returns>
      <param name="executionContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> for this instance.</param>
      <param name="exception">The <see cref="T:System.Exception" /> which caused this fault.</param>
    </member>
    <member name="M:System.Workflow.Activities.SequenceActivity.OnActivityChangeRemove(System.Workflow.ComponentModel.ActivityExecutionContext,System.Workflow.ComponentModel.Activity)">
      <summary>Called when an activity is removed.</summary>
      <param name="executionContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> for this instance.</param>
      <param name="removedActivity">The specified <see cref="T:System.Workflow.ComponentModel.Activity" />.</param>
    </member>
    <member name="M:System.Workflow.Activities.SequenceActivity.OnSequenceComplete(System.Workflow.ComponentModel.ActivityExecutionContext)">
      <summary>When overridden in a derived class, determines the action taken by the <see cref="T:System.Workflow.Activities.SequenceActivity" /> when the activity has completed execution.</summary>
      <param name="executionContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> for this instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="executionContext" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Activities.SequenceActivity.OnWorkflowChangesCompleted(System.Workflow.ComponentModel.ActivityExecutionContext)">
      <summary>Called after changes have been made to the collection <see cref="P:System.Workflow.ComponentModel.CompositeActivity.Activities" /> of this instance.</summary>
      <param name="executionContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> for this instance.</param>
    </member>
    <member name="M:System.Workflow.Activities.SequenceActivity.System#Workflow#ComponentModel#IActivityEventListener{T}#OnEvent(System.Object,System.Workflow.ComponentModel.ActivityExecutionStatusChangedEventArgs)">
      <summary>Defines the processing procedure when the subscribed-to event occurs.</summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">The previously typed event arguments.</param>
    </member>
    <member name="T:System.Workflow.Activities.SequentialWorkflowActivity">
      <summary>Represents a workflow that executes activities sequentially. </summary>
    </member>
    <member name="M:System.Workflow.Activities.SequentialWorkflowActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.SequentialWorkflowActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.SequentialWorkflowActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.SequentialWorkflowActivity" /> class by using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="E:System.Workflow.Activities.SequentialWorkflowActivity.Completed">
      <summary>Occurs when the workflow has finished.</summary>
    </member>
    <member name="F:System.Workflow.Activities.SequentialWorkflowActivity.CompletedEvent">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="E:System.Workflow.Activities.SequentialWorkflowActivity.Completed" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.SequentialWorkflowActivity.DynamicUpdateCondition">
      <summary>Gets or sets an <see cref="T:System.Workflow.ComponentModel.ActivityCondition" /> in which dynamic updates can be made in the workflow, when overridden in a derived class. </summary>
      <returns>A <see cref="T:System.Workflow.ComponentModel.ActivityCondition" /> that identifies when dynamic updates can be made.</returns>
    </member>
    <member name="M:System.Workflow.Activities.SequentialWorkflowActivity.Execute(System.Workflow.ComponentModel.ActivityExecutionContext)">
      <summary>Runs the workflow.</summary>
      <returns>The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionStatus" /> at the <see cref="T:System.Workflow.Activities.SequentialWorkflowActivity" /> at the end of the requested operation.</returns>
      <param name="executionContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> associated with the <see cref="T:System.Workflow.ComponentModel.Activity" />.</param>
    </member>
    <member name="E:System.Workflow.Activities.SequentialWorkflowActivity.Initialized">
      <summary>Occurs when the workflow is initialized.</summary>
    </member>
    <member name="F:System.Workflow.Activities.SequentialWorkflowActivity.InitializedEvent">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="E:System.Workflow.Activities.SequentialWorkflowActivity.Initialized" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.SequentialWorkflowActivity.OnSequenceComplete(System.Workflow.ComponentModel.ActivityExecutionContext)">
      <summary>Raises the <see cref="F:System.Workflow.Activities.SequentialWorkflowActivity.CompletedEvent" /> for the <see cref="T:System.Workflow.Activities.SequentialWorkflowActivity" />.</summary>
      <param name="executionContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> associated with the <see cref="T:System.Workflow.ComponentModel.Activity" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="executionContext" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Workflow.Activities.SetStateActivity">
      <summary>Provides the transition to a <see cref="T:System.Workflow.Activities.StateActivity" /> in a state machine workflow. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.SetStateActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.SetStateActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.SetStateActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.SetStateActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="P:System.Workflow.Activities.SetStateActivity.TargetStateName">
      <summary>Gets or sets the name of the target state.</summary>
      <returns>The name of the target stream.</returns>
    </member>
    <member name="F:System.Workflow.Activities.SetStateActivity.TargetStateNameProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.SetStateActivity.TargetStateName" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.SetStateEventArgs">
      <summary>Represents a class that is used as an argument to set the state of a <see cref="T:System.Workflow.Activities.StateMachineWorkflowActivity" />.</summary>
    </member>
    <member name="M:System.Workflow.Activities.SetStateEventArgs.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.SetStateEventArgs" /> class. </summary>
      <param name="targetStateName">The state to set the <see cref="T:System.Workflow.Activities.StateActivity" /> to.</param>
    </member>
    <member name="P:System.Workflow.Activities.SetStateEventArgs.TargetStateName">
      <summary>Gets the state to set the <see cref="T:System.Workflow.Activities.StateActivity" /> to.</summary>
      <returns>The state to set the <see cref="T:System.Workflow.Activities.StateActivity" /> to.</returns>
    </member>
    <member name="T:System.Workflow.Activities.StateActivity">
      <summary>Represents a state in a <see cref="T:System.Workflow.Activities.StateMachineWorkflowActivity" />.</summary>
    </member>
    <member name="M:System.Workflow.Activities.StateActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.StateActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.StateActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.StateActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="M:System.Workflow.Activities.StateActivity.Cancel(System.Workflow.ComponentModel.ActivityExecutionContext)">
      <summary>Cancels the execution of the <see cref="T:System.Workflow.Activities.StateActivity" />.</summary>
      <returns>The current status of the <see cref="T:System.Workflow.ComponentModel.Activity" /> in the running workflow instance.</returns>
      <param name="executionContext">The execution context for the activity.</param>
    </member>
    <member name="M:System.Workflow.Activities.StateActivity.Execute(System.Workflow.ComponentModel.ActivityExecutionContext)">
      <summary>Executes the <see cref="T:System.Workflow.Activities.StateActivity" />.</summary>
      <returns>The current status of the <see cref="T:System.Workflow.ComponentModel.Activity" /> in the running workflow instance.</returns>
      <param name="executionContext">The execution context for the activity.</param>
    </member>
    <member name="M:System.Workflow.Activities.StateActivity.GetDynamicActivity(System.String)">
      <summary>Gets the executing instance of the <see cref="T:System.Workflow.ComponentModel.Activity" /> that corresponds to the specified child activity name.</summary>
      <returns>The dynamic child activities. </returns>
      <param name="childActivityName">The child activity.</param>
      <exception cref="T:System.ArgumentException">The executable activities do not contain the <paramref name="childActivityName" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.StateActivity.Initialize(System.IServiceProvider)">
      <summary>Initializes the <see cref="T:System.Workflow.Activities.StateActivity" /> using the service provider.</summary>
      <param name="provider">The service provider from which to obtain services.</param>
    </member>
    <member name="M:System.Workflow.Activities.StateActivity.OnActivityChangeAdd(System.Workflow.ComponentModel.ActivityExecutionContext,System.Workflow.ComponentModel.Activity)">
      <summary>Determines if the activity being added is an <see cref="T:System.Workflow.Activities.EventDrivenActivity" /> and if so, verifies whether the newly added event driven needs to be subscribed.</summary>
      <param name="executionContext">The execution context for the activity.</param>
      <param name="addedActivity">The activity added to the <see cref="T:System.Workflow.Activities.StateActivity" />.</param>
    </member>
    <member name="M:System.Workflow.Activities.StateActivity.OnClosed(System.IServiceProvider)">
      <summary>Called by the workflow runtime engine as part of the activity's transition to the <see cref="F:System.Workflow.ComponentModel.ActivityExecutionStatus.Closed" /> state.</summary>
      <param name="provider">The service provider from which to obtain services.</param>
    </member>
    <member name="F:System.Workflow.Activities.StateActivity.StateChangeTrackingDataKey">
      <summary>Holds the string value of the change tracking data key; "StateActivity.StateChange". This field is a constant.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.StateActivityValidator">
      <summary>Represents a class that verifies whether a <see cref="T:System.Workflow.Activities.StateActivity" /> class is configured correctly.</summary>
    </member>
    <member name="M:System.Workflow.Activities.StateActivityValidator.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.StateActivityValidator" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.StateActivityValidator.Validate(System.Workflow.ComponentModel.Compiler.ValidationManager,System.Object)">
      <summary>Validates the <see cref="T:System.Workflow.Activities.StateActivity" /> class during workflow compilation.</summary>
      <returns>A <see cref="T:System.Workflow.ComponentModel.Compiler.ValidationErrorCollection" /> that contains the errors from this operation.</returns>
      <param name="manager">The <see cref="T:System.Workflow.ComponentModel.Compiler.ValidationManager" /> to use for this validation.</param>
      <param name="obj">The <see cref="T:System.Object" /> to validate.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Activities.StateActivityValidator.ValidateActivityChange(System.Workflow.ComponentModel.Activity,System.Workflow.ComponentModel.ActivityChangeAction)">
      <summary>Validates the changes made to <see cref="T:System.Workflow.Activities.StateActivity" />.</summary>
      <returns>A <see cref="T:System.Workflow.ComponentModel.Compiler.ValidationErrorCollection" /> that contains the errors from this operation.</returns>
      <param name="activity">The name of the <see cref="T:System.Workflow.Activities.StateActivity" /> to validate.</param>
      <param name="action">The action taken on the <see cref="T:System.Workflow.Activities.StateActivity" />.</param>
    </member>
    <member name="T:System.Workflow.Activities.StateFinalizationActivity">
      <summary>Represents an activity that executes contained activities before transitioning to another state in a state machine workflow. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.StateFinalizationActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.StateFinalizationActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.StateFinalizationActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.StateFinalizationActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="T:System.Workflow.Activities.StateInitializationActivity">
      <summary>Acts as a container to a group of activities that is executed when the <see cref="T:System.Workflow.Activities.StateActivity" /> starts running. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.StateInitializationActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.StateInitializationActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.StateInitializationActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.StateInitializationActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="T:System.Workflow.Activities.StateMachineWorkflowActivity">
      <summary>Serves as the root container for state machine workflows. Contains event-driven activities and states.</summary>
    </member>
    <member name="M:System.Workflow.Activities.StateMachineWorkflowActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.StateMachineWorkflowActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.StateMachineWorkflowActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.StateMachineWorkflowActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="P:System.Workflow.Activities.StateMachineWorkflowActivity.CompletedStateName">
      <summary>Gets or sets the end <see cref="T:System.Workflow.Activities.StateActivity" /> of the workflow.</summary>
      <returns>The end <see cref="T:System.Workflow.Activities.StateActivity" /> of the workflow.</returns>
    </member>
    <member name="F:System.Workflow.Activities.StateMachineWorkflowActivity.CompletedStateNameProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyObject" /> that targets the <see cref="P:System.Workflow.Activities.StateMachineWorkflowActivity.CompletedStateName" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.StateMachineWorkflowActivity.CurrentStateName">
      <summary>Gets the name of the currently executing <see cref="T:System.Workflow.Activities.StateActivity" />.</summary>
      <returns>The name of the current <see cref="T:System.Workflow.Activities.StateActivity" />.</returns>
    </member>
    <member name="P:System.Workflow.Activities.StateMachineWorkflowActivity.DynamicUpdateCondition">
      <summary>Gets or sets an <see cref="T:System.Workflow.ComponentModel.ActivityCondition" /> that determines whether dynamic updates can be made in the workflow, when overridden in a derived class. </summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.ActivityCondition" /> that identifies when dynamic updates can be made.</returns>
    </member>
    <member name="P:System.Workflow.Activities.StateMachineWorkflowActivity.InitialStateName">
      <summary>Gets or sets the <see cref="T:System.Workflow.Activities.StateActivity" /> in which the <see cref="T:System.Workflow.Activities.StateMachineWorkflowActivity" /> is when an instance of the state machine is created.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.StateActivity" /> in which the <see cref="T:System.Workflow.Activities.StateMachineWorkflowActivity" /> is when an instance of the state machine is created.</returns>
    </member>
    <member name="F:System.Workflow.Activities.StateMachineWorkflowActivity.InitialStateNameProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyObject" /> that targets the <see cref="P:System.Workflow.Activities.StateMachineWorkflowActivity.InitialStateName" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.StateMachineWorkflowActivity.PreviousStateName">
      <summary>Gets the name of the previously executed <see cref="T:System.Workflow.Activities.StateActivity" />.</summary>
      <returns>The name of the previous <see cref="T:System.Workflow.Activities.StateActivity" />.</returns>
    </member>
    <member name="F:System.Workflow.Activities.StateMachineWorkflowActivity.SetStateQueueName">
      <summary>The name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> that is used to change the state of a <see cref="T:System.Workflow.Activities.StateMachineWorkflowActivity" />. This field is constant.</summary>
    </member>
    <member name="T:System.Workflow.Activities.StateMachineWorkflowInstance">
      <summary>Represents a class that manages the current instance of a <see cref="T:System.Workflow.Activities.StateMachineWorkflowActivity" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.StateMachineWorkflowInstance.#ctor(System.Workflow.Runtime.WorkflowRuntime,System.Guid)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.StateMachineWorkflowInstance" /> class. </summary>
      <param name="runtime">The current workflow runtime.</param>
      <param name="instanceId">The <see cref="T:System.Guid" /> that indicates the instance of the <see cref="T:System.Workflow.Activities.StateMachineWorkflowActivity" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="runtime" /> is a null reference (Nothing in Visual Basic). or<paramref name="instanceId" /> is an empty <see cref="T:System.Guid" />.</exception>
    </member>
    <member name="P:System.Workflow.Activities.StateMachineWorkflowInstance.CurrentState">
      <summary>Gets the currently executing <see cref="T:System.Workflow.Activities.StateActivity" />.</summary>
      <returns>The current <see cref="T:System.Workflow.Activities.StateActivity" />.</returns>
    </member>
    <member name="P:System.Workflow.Activities.StateMachineWorkflowInstance.CurrentStateName">
      <summary>Gets the name of the current <see cref="T:System.Workflow.Activities.StateActivity" />.</summary>
      <returns>The name of the current <see cref="T:System.Workflow.Activities.StateActivity" />.</returns>
    </member>
    <member name="M:System.Workflow.Activities.StateMachineWorkflowInstance.EnqueueItem(System.IComparable,System.Object)">
      <summary>Posts a message to the <see cref="T:System.Workflow.Activities.StateMachineWorkflowInstance" />.</summary>
      <param name="queueName">The name of the workflow queue.</param>
      <param name="item">The object to queue.</param>
    </member>
    <member name="M:System.Workflow.Activities.StateMachineWorkflowInstance.EnqueueItem(System.IComparable,System.Object,System.Workflow.Runtime.IPendingWork,System.Object)">
      <summary>Posts a message to the <see cref="T:System.Workflow.Activities.StateMachineWorkflowInstance" />.</summary>
      <param name="queueName">The name of the workflow queue.</param>
      <param name="item">The object to queue.</param>
      <param name="pendingWork">An <see cref="T:System.Workflow.Runtime.IPendingWork" /> that allows the sender to be notified when the item is delivered.</param>
      <param name="workItem">An object to be passed to the <see cref="T:System.Workflow.Runtime.IPendingWork" /> methods.</param>
    </member>
    <member name="P:System.Workflow.Activities.StateMachineWorkflowInstance.InstanceId">
      <summary>Gets the <see cref="T:System.Guid" /> that indicates the current instance of the <see cref="T:System.Workflow.Activities.StateMachineWorkflowActivity" />.</summary>
      <returns>The <see cref="T:System.Guid" /> that indicates the current instance of the <see cref="T:System.Workflow.Activities.StateMachineWorkflowActivity" />.</returns>
    </member>
    <member name="P:System.Workflow.Activities.StateMachineWorkflowInstance.PossibleStateTransitions">
      <summary>Gets a collection of state transitions that the current <see cref="T:System.Workflow.Activities.StateActivity" /> can make.</summary>
      <returns>A collection of state transitions that the current <see cref="T:System.Workflow.Activities.StateActivity" /> can make.</returns>
    </member>
    <member name="M:System.Workflow.Activities.StateMachineWorkflowInstance.SetState(System.String)">
      <summary>Provides a transition to a specified <see cref="T:System.Workflow.Activities.StateActivity" /> using the name of the <see cref="T:System.Workflow.Activities.StateActivity" />.</summary>
      <param name="targetStateName">The name of the <see cref="T:System.Workflow.Activities.StateActivity" /> to transition to.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="targetStateName" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <see cref="T:System.Workflow.Activities.StateActivity" /> specified by <paramref name="targetStateName" /> is a null reference (Nothing).</exception>
    </member>
    <member name="M:System.Workflow.Activities.StateMachineWorkflowInstance.SetState(System.Workflow.Activities.StateActivity)">
      <summary>Provides a transition to a specified <see cref="T:System.Workflow.Activities.StateActivity" />.</summary>
      <param name="targetState">The <see cref="T:System.Workflow.Activities.StateActivity" /> to transition to.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="targetState" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="P:System.Workflow.Activities.StateMachineWorkflowInstance.StateHistory">
      <summary>Gets a collection of state activities that the state machine workflow instance has transitioned through.</summary>
      <returns>A collection of transitions that the <see cref="T:System.Workflow.Activities.StateActivity" /> has made.</returns>
    </member>
    <member name="P:System.Workflow.Activities.StateMachineWorkflowInstance.StateMachineWorkflow">
      <summary>Gets the latest definition of the <see cref="T:System.Workflow.Activities.StateMachineWorkflowActivity" />.</summary>
      <returns>The latest definition of the <see cref="T:System.Workflow.Activities.StateMachineWorkflowActivity" />.</returns>
      <exception cref="T:System.InvalidOperationException">If the workflow has already completed, the current <see cref="T:System.Workflow.Activities.StateMachineWorkflowActivity" /> cannot be retrieved, so the previous definition is retrieved.</exception>
    </member>
    <member name="P:System.Workflow.Activities.StateMachineWorkflowInstance.States">
      <summary>Gets a list of <see cref="T:System.Workflow.Activities.StateActivity" /> classes in the <see cref="P:System.Workflow.Activities.StateMachineWorkflowInstance.StateMachineWorkflow" />.</summary>
      <returns>A list of <see cref="T:System.Workflow.Activities.StateActivity" /> classes in the <see cref="P:System.Workflow.Activities.StateMachineWorkflowInstance.StateMachineWorkflow" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Workflow.Activities.StateMachineWorkflowInstance.StateMachineWorkflow" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="P:System.Workflow.Activities.StateMachineWorkflowInstance.WorkflowInstance">
      <summary>Gets the current <see cref="T:System.Workflow.Runtime.WorkflowInstance" />.</summary>
      <returns>The current <see cref="T:System.Workflow.Runtime.WorkflowInstance" />.</returns>
    </member>
    <member name="T:System.Workflow.Activities.WebServiceFaultActivity">
      <summary>Enables sending a fault to the Web service client from the workflow. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.WebServiceFaultActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WebServiceFaultActivity" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.WebServiceFaultActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WebServiceFaultActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.WebServiceFaultActivity.Fault">
      <summary>Gets or sets the exception that caused the Web service to stop.</summary>
      <returns>The exception that caused the Web service to stop.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Workflow.Activities.WebServiceFaultActivity.FaultProperty">
      <summary>Contains the exception value that is thrown when there is an error in the execution of the <see cref="T:System.Workflow.Activities.WebServiceInputActivity" /> activity.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.WebServiceFaultActivity.InputActivityName">
      <summary>Gets or sets the name of the previous <see cref="T:System.Workflow.Activities.WebServiceInputActivity" />.</summary>
      <returns>The name of the previous <see cref="T:System.Workflow.Activities.WebServiceInputActivity" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Workflow.Activities.WebServiceFaultActivity.InputActivityNameProperty">
      <summary>Represents the name of the <see cref="T:System.Workflow.Activities.WebServiceInputActivity" /> that is associated with this fault. The fault is thrown on the method that was associated with the <see cref="T:System.Workflow.Activities.WebServiceInputActivity" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Workflow.Activities.WebServiceFaultActivity.SendingFault">
      <summary>Occurs before the fault is sent to the client, which is useful for appending information to the fault.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Workflow.Activities.WebServiceFaultActivity.SendingFaultEvent">
      <summary>Defines an event delegate that is executed before the fault is delivered to the method caller. This handler provides a place to set the fault property value.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.WebServiceInputActivity">
      <summary>Enables receiving data from a Web service in a workflow. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.WebServiceInputActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WebServiceInputActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.WebServiceInputActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WebServiceInputActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="F:System.Workflow.Activities.WebServiceInputActivity.ActivitySubscribedProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="F:System.Workflow.Activities.WebServiceInputActivity.ActivitySubscribedProperty" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Workflow.Activities.WebServiceInputActivity.InputReceived">
      <summary>Occurs when the <see cref="T:System.Workflow.Activities.WebServiceInputActivity" /> has received input.</summary>
    </member>
    <member name="F:System.Workflow.Activities.WebServiceInputActivity.InputReceivedEvent">
      <summary>Defines an event delegate that is executed after the activity receives the expected call from the Web service invocation.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.WebServiceInputActivity.InterfaceType">
      <summary>Gets or sets the interface type that defines the method that this activity listens on.</summary>
      <returns>The interface type.</returns>
    </member>
    <member name="F:System.Workflow.Activities.WebServiceInputActivity.InterfaceTypeProperty">
      <summary>Corresponds to the name of the interface that is used as the Web service contract.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.WebServiceInputActivity.IsActivating">
      <summary>Gets or sets a value that indicates whether receiving data should start the workflow.</summary>
      <returns>true if receiving data should start the workflow; otherwise, false.</returns>
    </member>
    <member name="F:System.Workflow.Activities.WebServiceInputActivity.IsActivatingProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.WebServiceInputActivity.IsActivating" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.WebServiceInputActivity.MethodName">
      <summary>Gets or sets the name of the method on the interface specified by the <see cref="P:System.Workflow.Activities.WebServiceInputActivity.InterfaceType" /> property that this <see cref="T:System.Workflow.Activities.WebServiceInputActivity" /> represents.</summary>
      <returns>The name of the method on the interface specified by the <see cref="P:System.Workflow.Activities.WebServiceInputActivity.InterfaceType" /> property that this <see cref="T:System.Workflow.Activities.WebServiceInputActivity" /> represents. </returns>
    </member>
    <member name="F:System.Workflow.Activities.WebServiceInputActivity.MethodNameProperty">
      <summary>Corresponds to a name of one of the methods contained in the interface that is used as the Web service contract.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.WebServiceInputActivity.ParameterBindings">
      <summary>Gets the collection of bindings associated with the input parameters to the method specified in the <see cref="P:System.Workflow.Activities.WebServiceInputActivity.MethodName" /> property.</summary>
      <returns>The collection of properties used as inbound parameters on the method specified by the <see cref="P:System.Workflow.Activities.WebServiceInputActivity.MethodName" /> property.</returns>
    </member>
    <member name="F:System.Workflow.Activities.WebServiceInputActivity.ParameterBindingsProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.WebServiceInputActivity.ParameterBindings" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.WebServiceInputActivity.Roles">
      <summary>Gets or sets a collection of roles that are valid to send data to the <see cref="T:System.Workflow.Activities.WebServiceInputActivity" />.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.WorkflowRoleCollection" /> that contains the Web service roles that are valid to send data to the <see cref="T:System.Workflow.Activities.WebServiceInputActivity" />.</returns>
    </member>
    <member name="F:System.Workflow.Activities.WebServiceInputActivity.RolesProperty">
      <summary>Identifies the valid user roles that are allowed to send messages from the Web service client to the <see cref="T:System.Workflow.Activities.WebServiceInputActivity" />. The two types of supported roles are Active Directory and ASP.NET.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.WebServiceInputActivity.System#Workflow#Activities#IEventActivity#QueueName">
      <summary>Gets the name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> on which the activity is waiting for data to arrive.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> on which the activity is waiting for data to arrive. </returns>
    </member>
    <member name="M:System.Workflow.Activities.WebServiceInputActivity.System#Workflow#Activities#IEventActivity#Subscribe(System.Workflow.ComponentModel.ActivityExecutionContext,System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.ComponentModel.QueueEventArgs})">
      <summary>Creates the subscription of the <see cref="T:System.Workflow.Activities.WebServiceInputActivity" /> activity to an event.</summary>
      <param name="parentContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> that represents the execution environment of the <see cref="T:System.Workflow.Activities.WebServiceInputActivity" /> activity.</param>
      <param name="parentEventHandler">The <see cref="T:System.EventHandler" /> for the parent event.</param>
    </member>
    <member name="M:System.Workflow.Activities.WebServiceInputActivity.System#Workflow#Activities#IEventActivity#Unsubscribe(System.Workflow.ComponentModel.ActivityExecutionContext,System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.ComponentModel.QueueEventArgs})">
      <summary>Cancels the subscription of a <see cref="T:System.Workflow.ComponentModel.Activity" /> to an event.</summary>
      <param name="parentContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> that represents the execution environment of the <see cref="T:System.Workflow.Activities.WebServiceInputActivity" /> activity.</param>
      <param name="parentEventHandler">The <see cref="T:System.EventHandler" /> for the parent event.</param>
    </member>
    <member name="M:System.Workflow.Activities.WebServiceInputActivity.System#Workflow#ComponentModel#IActivityEventListener{T}#OnEvent(System.Object,System.Workflow.ComponentModel.QueueEventArgs)">
      <summary>Defines the processing procedure when the subscribed-to event occurs.</summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">The previously typed event arguments.</param>
    </member>
    <member name="M:System.Workflow.Activities.WebServiceInputActivity.System#Workflow#ComponentModel#IDynamicPropertyTypeProvider#GetAccessType(System.IServiceProvider,System.String)">
      <summary>Returns the access type for the specified property.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Compiler.AccessTypes" /> enumeration value that denotes the access level of the property.</returns>
      <param name="serviceProvider">An object that implements <see cref="T:System.IServiceProvider" /> to provide access to design time services if they are required.</param>
      <param name="propertyName">The name of the property. </param>
    </member>
    <member name="M:System.Workflow.Activities.WebServiceInputActivity.System#Workflow#ComponentModel#IDynamicPropertyTypeProvider#GetPropertyType(System.IServiceProvider,System.String)">
      <summary>Returns the <see cref="T:System.Type" /> of the specified property.</summary>
      <returns>The <see cref="T:System.Type" /> for the property whose name is passed as the <paramref name="propertyName " />parameter. </returns>
      <param name="serviceProvider">An object that implements <see cref="T:System.IServiceProvider" /> to provide access to design time services if they are required.</param>
      <param name="propertyName">The name of the property. </param>
    </member>
    <member name="T:System.Workflow.Activities.WebServiceOutputActivity">
      <summary>Enables sending data to a Web service from within a workflow. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.WebServiceOutputActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WebServiceOutputActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.WebServiceOutputActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WebServiceOutputActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="P:System.Workflow.Activities.WebServiceOutputActivity.InputActivityName">
      <summary>Gets or sets the name of the preceding <see cref="T:System.Workflow.Activities.WebServiceInputActivity" /> class. </summary>
      <returns>The name of the preceding <see cref="T:System.Workflow.Activities.WebServiceInputActivity" /> class. </returns>
    </member>
    <member name="F:System.Workflow.Activities.WebServiceOutputActivity.InputActivityNameProperty">
      <summary>Defines the name of the <see cref="T:System.Workflow.Activities.WebServiceInputActivity" /> that this <see cref="T:System.Workflow.Activities.WebServiceOutputActivity" /> is matched with.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.WebServiceOutputActivity.ParameterBindings">
      <summary>Gets the collection of bindings associated with the out/ref and return value of the corresponding <see cref="T:System.Workflow.Activities.WebServiceInputActivity" /> method.</summary>
      <returns>This collection is used to bind the outbound parameters to locations in the workflow. When the <see cref="T:System.Workflow.Activities.WebServiceOutputActivity" /> executes, it extracts the values from the bound locations and delivers these back to the blocked Web service method.</returns>
    </member>
    <member name="F:System.Workflow.Activities.WebServiceOutputActivity.ParameterBindingsProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyObject" /> that targets the <see cref="P:System.Workflow.Activities.WebServiceOutputActivity.ParameterBindings" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Workflow.Activities.WebServiceOutputActivity.SendingOutput">
      <summary>Occurs before the <see cref="T:System.Workflow.Activities.WebServiceOutputActivity" /> sends output to the client.</summary>
    </member>
    <member name="F:System.Workflow.Activities.WebServiceOutputActivity.SendingOutputEvent">
      <summary>Defines an event delegate that is executed before the activity sends the output of the Web service invocation.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.WebServiceOutputActivity.System#Workflow#ComponentModel#IDynamicPropertyTypeProvider#GetAccessType(System.IServiceProvider,System.String)">
      <summary>Returns the access type for the specified property.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Compiler.AccessTypes" /> enumeration value denoting the access level of the property.</returns>
      <param name="serviceProvider">An object that implements <see cref="T:System.IServiceProvider" /> to provide access to design time services if they are required.</param>
      <param name="propertyName">The name of the property. </param>
    </member>
    <member name="M:System.Workflow.Activities.WebServiceOutputActivity.System#Workflow#ComponentModel#IDynamicPropertyTypeProvider#GetPropertyType(System.IServiceProvider,System.String)">
      <summary>Returns the <see cref="T:System.Type" /> of the specified property.</summary>
      <returns>The <see cref="T:System.Type" /> for the property whose name is passed as the <paramref name="propertyName " />parameter. </returns>
      <param name="serviceProvider">An object that implements <see cref="T:System.IServiceProvider" /> to provide access to design time services if they are required.</param>
      <param name="propertyName">The name of the property. </param>
    </member>
    <member name="T:System.Workflow.Activities.WebWorkflowRole">
      <summary>Represents a Workflow role that is backed by a <see cref="T:System.Web.Security.RoleProvider" />.</summary>
    </member>
    <member name="M:System.Workflow.Activities.WebWorkflowRole.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WebWorkflowRole" /> class by using the specified role name. </summary>
      <param name="roleName">A string that defines the name of the role.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="roleName" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Activities.WebWorkflowRole.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WebWorkflowRole" /> class by using the specified role name and role provider. </summary>
      <param name="roleName">A string that defines the name of the role.</param>
      <param name="provider">A string that defines the role provider.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="roleName" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Activities.WebWorkflowRole.GetIdentities">
      <summary>Returns a generic list of ASP.NET accounts associated with the <see cref="T:System.Workflow.Activities.WebWorkflowRole" />.</summary>
      <returns>A generic list of string values that represent the ASP.NET accounts associated with the <see cref="T:System.Workflow.Activities.WebWorkflowRole" />.</returns>
    </member>
    <member name="M:System.Workflow.Activities.WebWorkflowRole.IncludesIdentity(System.String)">
      <summary>Returns a value indicating whether the <see cref="T:System.Workflow.Activities.WebWorkflowRole" /> contains the specified identity</summary>
      <returns>true if the <see cref="T:System.Workflow.Activities.WebWorkflowRole" /> contains the specified identity; otherwise, false.</returns>
      <param name="identity">The name of the identity to test for.</param>
    </member>
    <member name="P:System.Workflow.Activities.WebWorkflowRole.Name">
      <summary>Gets or sets the name of the <see cref="T:System.Workflow.Activities.WebWorkflowRole" />.</summary>
      <returns>A string that contains the name of the <see cref="T:System.Workflow.Activities.WebWorkflowRole" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Workflow.Activities.WebWorkflowRole.Name" /> was set to a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="P:System.Workflow.Activities.WebWorkflowRole.RoleProvider">
      <summary>Gets or sets the role provider for the <see cref="T:System.Workflow.Activities.WebWorkflowRole" />.</summary>
      <returns>A string that contains the role provider for the <see cref="T:System.Workflow.Activities.WebWorkflowRole" />.</returns>
    </member>
    <member name="T:System.Workflow.Activities.WhileActivity">
      <summary>Runs a child activity iteratively as long as a certain condition is true.</summary>
    </member>
    <member name="M:System.Workflow.Activities.WhileActivity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WhileActivity" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.WhileActivity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WhileActivity" /> class using the name of the activity.</summary>
      <param name="name">The user-defined name of the activity.</param>
    </member>
    <member name="P:System.Workflow.Activities.WhileActivity.Condition">
      <summary>Gets or sets a condition that determines whether the <see cref="T:System.Workflow.Activities.WhileActivity" /> should run or finish.</summary>
      <returns>When the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> property evaluates to true, the <see cref="T:System.Workflow.Activities.WhileActivity" /> continues to run. When the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> evaluates to false, the <see cref="T:System.Workflow.Activities.WhileActivity" /> finishes.</returns>
    </member>
    <member name="F:System.Workflow.Activities.WhileActivity.ConditionProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="P:System.Workflow.Activities.WhileActivity.Condition" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.WhileActivity.DynamicActivity">
      <summary>Gets the dynamic child activities in the <see cref="T:System.Workflow.Activities.WhileActivity" />.</summary>
      <returns>The dynamic child activities in the <see cref="T:System.Workflow.Activities.WhileActivity" />.</returns>
    </member>
    <member name="M:System.Workflow.Activities.WhileActivity.System#Workflow#ComponentModel#IActivityEventListener{T}#OnEvent(System.Object,System.Workflow.ComponentModel.ActivityExecutionStatusChangedEventArgs)">
      <summary>Defines the processing procedure when the subscribed-to event occurs.</summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">The previously typed event arguments.</param>
    </member>
    <member name="T:System.Workflow.Activities.WorkflowAuthorizationException">
      <summary>The exception that is thrown when role validation fails due to a specified identity that is not contained in the <see cref="T:System.Workflow.Activities.WorkflowRoleCollection" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowAuthorizationException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WorkflowAuthorizationException" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowAuthorizationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WorkflowAuthorizationException" /> class by using serialized data.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" />  that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" />  that contains contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowAuthorizationException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WorkflowAuthorizationException" /> class using the specified message.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowAuthorizationException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WorkflowAuthorizationException" /> class by using the specified message and <see cref="T:System.Exception" />.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <param name="innerException">The <see cref="T:System.Exception" /> that caused the <see cref="T:System.Workflow.Activities.WorkflowAuthorizationException" />.  </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowAuthorizationException.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WorkflowAuthorizationException" /> class using the name of the activity and the principal.</summary>
      <param name="activityName">The name of the <see cref="T:System.Workflow.ComponentModel.Activity" /> in which the exception occurred.</param>
      <param name="principalName">The name of the user or entity on whose behalf the workflow authorization exception was thrown.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.WorkflowRole">
      <summary>The abstract base class from which workflow roles are derived.</summary>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowRole.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Activities.WorkflowRole" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowRole.GetIdentities">
      <summary>When implemented in a derived class gets the identities contained by this <see cref="T:System.Workflow.Activities.WorkflowRole" />.</summary>
      <returns>A list of the names of the identities contained by this <see cref="T:System.Workflow.Activities.WorkflowRole" />.</returns>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowRole.IncludesIdentity(System.String)">
      <summary>When implemented in a derived class, returns a value that indicates whether this role contains the specified identity.</summary>
      <returns>true if <paramref name="identity" /> is included in this <see cref="T:System.Workflow.Activities.WorkflowRole" />; otherwise, false.</returns>
      <param name="identity">The name of the identity for which to test.</param>
    </member>
    <member name="P:System.Workflow.Activities.WorkflowRole.Name">
      <summary>When implemented in a derived class, gets or sets the name of this <see cref="T:System.Workflow.Activities.WorkflowRole" />.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Activities.WorkflowRole" />.</returns>
    </member>
    <member name="T:System.Workflow.Activities.WorkflowRoleCollection">
      <summary>Represents a collection of <see cref="T:System.Workflow.Activities.WorkflowRole" /> objects. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowRoleCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.WorkflowRoleCollection" /> class.  </summary>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowRoleCollection.IncludesIdentity(System.String)">
      <summary>Returns a value indicating whether the <see cref="T:System.Workflow.Activities.WorkflowRoleCollection" /> contains the specified identity.</summary>
      <returns>true if the <see cref="T:System.Workflow.Activities.WorkflowRoleCollection" /> contains the identity; otherwise false. </returns>
      <param name="identity">A string that contains the identity for which to search.</param>
    </member>
    <member name="T:System.Workflow.Activities.WorkflowSubscriptionService">
      <summary>Provides methods for a subscription service to manage subscriptions for a workflow runtime.</summary>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowSubscriptionService.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Activities.WorkflowSubscriptionService" /> class.  </summary>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowSubscriptionService.CreateSubscription(System.Workflow.Activities.MessageEventSubscription)">
      <summary>When overridden in a derived class, notifies the <see cref="T:System.Workflow.Activities.WorkflowSubscriptionService" /> that the workflow runtime created the specified <see cref="T:System.Workflow.Activities.MessageEventSubscription" />. </summary>
      <param name="subscription">The <see cref="T:System.Workflow.Activities.MessageEventSubscription" /> that the workflow runtime created.</param>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowSubscriptionService.DeleteSubscription(System.Guid)">
      <summary>When overridden in a derived class, notifies the <see cref="T:System.Workflow.Activities.WorkflowSubscriptionService" /> that the workflow runtime deleted the subscription associated with the specified GUID. </summary>
      <param name="subscriptionId">The GUID of the subscription that the workflow runtime deleted.</param>
    </member>
    <member name="T:System.Workflow.Activities.WorkflowWebService">
      <summary>Represents the base class for all workflow Web services.</summary>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowWebService.#ctor(System.Type)">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Activities.WorkflowWebService" /> class. </summary>
      <param name="workflowType">The <see cref="T:System.Type" /> of the workflow. </param>
    </member>
    <member name="M:System.Workflow.Activities.WorkflowWebService.Invoke(System.Type,System.String,System.Boolean,System.Object[])">
      <summary>Invokes the associated workflow and retrieves the response.</summary>
      <returns>The object returned by the Web service.</returns>
      <param name="interfaceType">The type of the interface.</param>
      <param name="methodName">The name of the method.</param>
      <param name="isActivation">Indicates whether the Web service activates the workflow.</param>
      <param name="parameters">The parameters of the method specified by <paramref name="methodName" />.</param>
    </member>
    <member name="P:System.Workflow.Activities.WorkflowWebService.WorkflowRuntime">
      <summary>Provides access to the current <see cref="T:System.Workflow.Runtime.WorkflowRuntime" />, which executes the workflow.</summary>
      <returns>The current <see cref="T:System.Workflow.Runtime.WorkflowRuntime" />.</returns>
    </member>
    <member name="T:System.Workflow.Activities.Configuration.ActiveDirectoryRoleFactoryConfiguration">
      <summary>Represents the section in the configuration file that addresses the role class. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Configuration.ActiveDirectoryRoleFactoryConfiguration.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Configuration.ActiveDirectoryRoleFactoryConfiguration" /> class.</summary>
    </member>
    <member name="P:System.Workflow.Activities.Configuration.ActiveDirectoryRoleFactoryConfiguration.DirectReports">
      <summary>Gets or sets the names of the direct reports.</summary>
      <returns>The names of the direct reports.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Configuration.ActiveDirectoryRoleFactoryConfiguration.DistinguishedName">
      <summary>Gets or sets the distinguished name for this instance.</summary>
      <returns>The distinguished name for this instance.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Configuration.ActiveDirectoryRoleFactoryConfiguration.Group">
      <summary>Gets or sets the name of the group for this instance.</summary>
      <returns>The name of the group for this instance.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Configuration.ActiveDirectoryRoleFactoryConfiguration.Manager">
      <summary>Gets or sets the name of the manager associated with this instance.</summary>
      <returns>The name of the manager associated with this instance.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Configuration.ActiveDirectoryRoleFactoryConfiguration.Member">
      <summary>Gets or sets the member for this instance.</summary>
      <returns>The member for this instance.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Configuration.ActiveDirectoryRoleFactoryConfiguration.RootPath">
      <summary>Gets or sets the root path for this instance.</summary>
      <returns>The root path for this instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.AddedConditionAction">
      <summary>Represents the addition of a <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> to a workflow during dynamic update. This class cannot be inherited. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.AddedConditionAction.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.AddedConditionAction" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.AddedConditionAction.#ctor(System.Workflow.Activities.Rules.RuleCondition)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.AddedConditionAction" /> class using the definition of the added condition.</summary>
      <param name="addedConditionDefinition">The condition added to the workflow.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="addedConditionDefinition" /> is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.AddedConditionAction.ConditionDefinition">
      <summary>Gets or sets the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> to add to the workflow.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> to add to the workflow.</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Workflow.Activities.Rules.AddedConditionAction.ConditionDefinition" /> is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.AddedConditionAction.ConditionName">
      <summary>Gets a string that contains the name of the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> that was added to the workflow.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> that was added to the workflow.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.AddedRuleSetAction">
      <summary>Represents the addition of a <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> during dynamic update. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.AddedRuleSetAction.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.AddedRuleSetAction" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.AddedRuleSetAction.#ctor(System.Workflow.Activities.Rules.RuleSet)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.AddedRuleSetAction" /> class by using the new <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <param name="addedRuleSetDefinition">The new <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.AddedRuleSetAction.RuleSetDefinition">
      <summary>Gets or sets the new <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <returns>The new <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.AddedRuleSetAction.RuleSetName">
      <summary>Gets the name of the new <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <returns>The name of the new <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.IRuleExpression">
      <summary>Represents the base class from which custom expression writers must derive to write custom expressions.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.IRuleExpression.AnalyzeUsage(System.Workflow.Activities.Rules.RuleAnalysis,System.Boolean,System.Boolean,System.Workflow.Activities.Rules.RulePathQualifier)">
      <summary>When overridden in a derived class, reports on how the object uses fields and properties in the context type.</summary>
      <param name="analysis">The <see cref="T:System.Workflow.Activities.Rules.RuleAnalysis" /> method to store fields and properties read by a condition or read by an action.</param>
      <param name="isRead">true if the expression is being read from; false if the expression is not being read from.</param>
      <param name="isWritten">true if the expression is being written to; false if the expression is not being written to.</param>
      <param name="qualifier">The path of a field or property.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.IRuleExpression.Clone">
      <summary>When overridden in a derived class, creates a deep copy of the current <see cref="T:System.CodeDom.CodeExpression" />.</summary>
      <returns>A deep copy of the current <see cref="T:System.CodeDom.CodeExpression" />.</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.IRuleExpression.Decompile(System.Text.StringBuilder,System.CodeDom.CodeExpression)">
      <summary>When overridden in a derived class, decompiles the custom expression into string form.</summary>
      <param name="stringBuilder">A mutable string for the decompiled expression. This method should append the decompiled syntax for this subexpression.</param>
      <param name="parentExpression">The parent code expression. This can be used to determine operator precedence, and whether this subexpression needs to be parenthesized.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.IRuleExpression.Evaluate(System.Workflow.Activities.Rules.RuleExecution)">
      <summary>When overridden in a derived class, evaluates the custom expression.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.RuleExpressionResult" /> that indicates the result of the rule execution.</returns>
      <param name="execution">The <see cref="T:System.Workflow.Activities.Rules.RuleExecution" /> to execute the expression.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.IRuleExpression.Match(System.CodeDom.CodeExpression)">
      <summary>Compares the current expression to another expression to determine whether they are equal.</summary>
      <returns>true to show that the expressions are equal; otherwise, false.</returns>
      <param name="expression">The expression to compare the current expression to.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.IRuleExpression.Validate(System.Workflow.Activities.Rules.RuleValidation,System.Boolean)">
      <summary>When overridden in a derived class, verifies that the expression is configured correctly and has no errors.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.RuleExpressionInfo" /> for the expression.</returns>
      <param name="validation">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> to manage the validation process.</param>
      <param name="isWritten">true if the expression is being written to; false if the expression is not being written to.</param>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RemovedConditionAction">
      <summary>Represents the removal of a <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> from a workflow during dynamic update. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RemovedConditionAction.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RemovedConditionAction" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RemovedConditionAction.#ctor(System.Workflow.Activities.Rules.RuleCondition)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RemovedConditionAction" /> class by using the definition of the removed condition.</summary>
      <param name="removedConditionDefinition">The condition removed from the workflow.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="addedConditionDefinition" /> is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RemovedConditionAction.ConditionDefinition">
      <summary>Gets or sets the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> to remove from the workflow.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> to remove from the workflow.</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Workflow.Activities.Rules.RemovedConditionAction.ConditionDefinition" /> is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RemovedConditionAction.ConditionName">
      <summary>Gets a string that contains the name of the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> to remove from the workflow.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> to remove from the workflow.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RemovedRuleSetAction">
      <summary>Represents the removal of a <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> during dynamic update. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RemovedRuleSetAction.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RemovedRuleSetAction" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RemovedRuleSetAction.#ctor(System.Workflow.Activities.Rules.RuleSet)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RemovedRuleSetAction" /> class by using the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> removed from the workflow.</summary>
      <param name="removedRuleSetDefinition">The <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> removed from the workflow.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="removedRuleSetDefinition" /> is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RemovedRuleSetAction.RuleSetDefinition">
      <summary>Gets or Sets the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> removed from the workflow.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> removed from the workflow.</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> to remove from the workflow is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RemovedRuleSetAction.RuleSetName">
      <summary>Gets the name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> removed from the workflow.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> removed from the workflow.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.Rule">
      <summary>Defines a condition with an associated set of actions to perform.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.Rule.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.Rule" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.Rule.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.Rule" /> class using the name of the <see cref="T:System.Workflow.Activities.Rules.Rule" />.</summary>
      <param name="name">The name of the <see cref="T:System.Workflow.Activities.Rules.Rule" />.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.Rule.#ctor(System.String,System.Workflow.Activities.Rules.RuleCondition,System.Collections.Generic.IList{System.Workflow.Activities.Rules.RuleAction})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.Rule" /> class using the name of the Rule, the rule condition, and a list of THEN actions.</summary>
      <param name="name">The name of the <see cref="T:System.Workflow.Activities.Rules.Rule" />.</param>
      <param name="condition">The <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> for the <see cref="T:System.Workflow.Activities.Rules.Rule" />.</param>
      <param name="thenActions">A collection of <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> objects to evaluate whether the condition is true.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.Rule.#ctor(System.String,System.Workflow.Activities.Rules.RuleCondition,System.Collections.Generic.IList{System.Workflow.Activities.Rules.RuleAction},System.Collections.Generic.IList{System.Workflow.Activities.Rules.RuleAction})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.Rule" /> class using the name of the Rule, the rule condition, a list of THEN actions, and a list of ELSE actions.</summary>
      <param name="name">The name of the <see cref="T:System.Workflow.Activities.Rules.Rule" />.</param>
      <param name="condition">The <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> for the <see cref="T:System.Workflow.Activities.Rules.Rule" />.</param>
      <param name="thenActions">A collection of <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> objects to evaluate if the condition is true.</param>
      <param name="elseActions">A collection of <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> objects to evaluate whether the condition is false.</param>
    </member>
    <member name="P:System.Workflow.Activities.Rules.Rule.Active">
      <summary>Gets or sets a value that indicates whether the <see cref="T:System.Workflow.Activities.Rules.Rule" /> should be evaluated.</summary>
      <returns>true if the Rule should be evaluated; otherwise, false.</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.Rule.Clone">
      <summary>Creates a deep copy of the current <see cref="T:System.Workflow.Activities.Rules.Rule" />.</summary>
      <returns>A <see cref="T:System.Workflow.Activities.Rules.Rule" /> that is identical to this instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.Rule.Condition">
      <summary>Gets or sets a <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> for the <see cref="T:System.Workflow.Activities.Rules.Rule" /> to evaluate.</summary>
      <returns>A <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> for the <see cref="T:System.Workflow.Activities.Rules.Rule" /> to evaluate.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Rules.Rule.Description">
      <summary>Gets or sets a description of the <see cref="T:System.Workflow.Activities.Rules.Rule" />.</summary>
      <returns>A description of the <see cref="T:System.Workflow.Activities.Rules.Rule" />.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Rules.Rule.ElseActions">
      <summary>Gets a collection of <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> classes to perform in the ELSE case.</summary>
      <returns>A collection of <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> to perform in the ELSE case.</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.Rule.Equals(System.Object)">
      <summary>Determines whether this instance of <see cref="T:System.Workflow.Activities.Rules.Rule" /> and the object passed as the parameter have the same value.</summary>
      <returns>true if the current object and <paramref name="obj" /> are the same; otherwise, false.</returns>
      <param name="obj">The object to compare the current object to.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.Rule.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>The hash code for this instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.Rule.Name">
      <summary>Gets or sets the name of the <see cref="T:System.Workflow.Activities.Rules.Rule" />.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Activities.Rules.Rule" />.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Rules.Rule.Priority">
      <summary>Gets or sets a value that indicates the order in which a <see cref="T:System.Workflow.Activities.Rules.Rule" /> should be run.</summary>
      <returns>A value that indicates the order in which a <see cref="T:System.Workflow.Activities.Rules.Rule" /> should be run.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Rules.Rule.ReevaluationBehavior">
      <summary>Gets or sets a value indicating whether a <see cref="T:System.Workflow.Activities.Rules.Rule" /> can be reevaluated.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.RuleReevaluationBehavior" /> indicating whether the <see cref="T:System.Workflow.Activities.Rules.Rule" /> can be reevaluated.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Rules.Rule.ThenActions">
      <summary>Gets a collection of <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> classes to perform in the THEN case.</summary>
      <returns>A collection of <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> classes to perform in the THEN case.</returns>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleAction">
      <summary>Represents an abstract class that defines an action to be executed if the associated <see cref="P:System.Workflow.Activities.Rules.Rule.Condition" /> evaluates to true, for <see cref="P:System.Workflow.Activities.Rules.Rule.ThenActions" />, or false, for <see cref="P:System.Workflow.Activities.Rules.Rule.ElseActions" />. This class must be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleAction.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> class.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleAction.Clone">
      <summary>Creates a deep copy of the current <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.</summary>
      <returns>A <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> that is identical to this instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleAction.Execute(System.Workflow.Activities.Rules.RuleExecution)">
      <summary>Executes the <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> using the specified <see cref="T:System.Workflow.Activities.Rules.RuleExecution" /> instance.</summary>
      <param name="context">The runtime state that the <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> is executing in.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleAction.GetSideEffects(System.Workflow.Activities.Rules.RuleValidation)">
      <summary>Returns the fields and properties updated by a <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.</summary>
      <returns>A collection of strings that represent the names of fields and properties that are changed by a <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.</returns>
      <param name="validation">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> that was used previously in a call to <see cref="M:System.Workflow.Activities.Rules.RuleAction.Validate(System.Workflow.Activities.Rules.RuleValidation)" />.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleAction.Validate(System.Workflow.Activities.Rules.RuleValidation)">
      <summary>Verifies that the <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> is configured correctly and has no errors.</summary>
      <returns>true to show that the <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> validated correctly and has no errors; otherwise, false.</returns>
      <param name="validator">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> to manage the validation process.</param>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleActionTrackingEvent">
      <summary>Contains the name, instance ID, and condition result of a rule that has been evaluated.</summary>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleActionTrackingEvent.ConditionResult">
      <summary>Gets the result of the condition evaluation.</summary>
      <returns>The result of the rule condition: true or false.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleActionTrackingEvent.RuleName">
      <summary>Gets the name of the <see cref="T:System.Workflow.Activities.Rules.Rule" /> that caused the <see cref="T:System.Workflow.Activities.Rules.RuleActionTrackingEvent" /> to be raised.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.Rule" /> that caused one or more actions to execute.</returns>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleAnalysis">
      <summary>Stores the fields and properties read by a condition or written to by an action. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleAnalysis.#ctor(System.Workflow.Activities.Rules.RuleValidation,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleAnalysis" /> class. </summary>
      <param name="validation">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> to manage the validation process.</param>
      <param name="forWrites">A Boolean value that indicates whether analysis is being done for writes (in the case of <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> objects) or reads (in the case of <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> objects).</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleAnalysis.AddSymbol(System.String)">
      <summary>Adds the fields and properties used by an expression as symbols to the <see cref="T:System.Workflow.Activities.Rules.RuleAnalysis" /> instance.</summary>
      <param name="symbol">A string that represents the path to a field or property.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleAnalysis.ForWrites">
      <summary>Gets a value that indicates whether analysis is being done for writes or reads. </summary>
      <returns>true if analysis is being done for writes (in the case of <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> objects); otherwise, false if the analysis is being done for reads (in the case of <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> objects).</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleAnalysis.GetSymbols">
      <summary>Returns the list of symbols from the <see cref="T:System.Workflow.Activities.Rules.RuleAnalysis" /> instance. </summary>
      <returns>The list of symbols for the <see cref="T:System.Workflow.Activities.Rules.RuleAnalysis" /> instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleAttribute">
      <summary>Represents the base class for custom <see cref="T:System.Workflow.Activities.Rules.Rule" /> attributes. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleAttribute.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleAttribute" /> class.</summary>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleAttributeTarget">
      <summary>Specifies whether the path in a <see cref="T:System.Workflow.Activities.Rules.RuleReadAttribute" /> or <see cref="T:System.Workflow.Activities.Rules.RuleWriteAttribute" /> is for a method parameter, or this.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Workflow.Activities.Rules.RuleAttributeTarget.Parameter">
      <summary>Specifies the path is an absolute path beginning with a parameter name, which optionally can be followed by a path to an inner member.</summary>
    </member>
    <member name="F:System.Workflow.Activities.Rules.RuleAttributeTarget.This">
      <summary>Specifies the path is a relative or absolute path, beginning with the token this.</summary>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleChainingBehavior">
      <summary>Specifies the forward chaining behavior of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
    </member>
    <member name="F:System.Workflow.Activities.Rules.RuleChainingBehavior.None">
      <summary>Indicates that no chaining is performed. Each rule is executed exactly once.</summary>
    </member>
    <member name="F:System.Workflow.Activities.Rules.RuleChainingBehavior.UpdateOnly">
      <summary>Indicates that chaining is performed if executed actions explicitly specify it using a <see cref="T:System.Workflow.Activities.Rules.RuleUpdateAction" />.</summary>
    </member>
    <member name="F:System.Workflow.Activities.Rules.RuleChainingBehavior.Full">
      <summary>Indicates that chaining is performed when fields or properties are modified by actions, <see cref="T:System.Workflow.Activities.Rules.RuleWriteAttribute" /> are specified for methods called by actions, or when a <see cref="T:System.Workflow.Activities.Rules.RuleUpdateAction" /> is performed.</summary>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleCondition">
      <summary>Base type for <see cref="T:System.Workflow.Activities.Rules.RuleExpressionCondition" /> and any custom conditions created by users.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleCondition.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> class.  </summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleCondition.Clone">
      <summary>When overridden in a derived class, creates a deep copy of the current <see cref="T:System.Workflow.Activities.Rules.RuleCondition" />.</summary>
      <returns>A <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> that is identical to this instance</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleCondition.Evaluate(System.Workflow.Activities.Rules.RuleExecution)">
      <summary>When overridden in a derived class, evaluates the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" />.</summary>
      <returns>true to show that the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> evaluated to true; otherwise, false.</returns>
      <param name="execution">The runtime context the rule is running in. </param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleCondition.GetDependencies(System.Workflow.Activities.Rules.RuleValidation)">
      <summary>When overridden in a derived class, gets the dependencies for the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" />.</summary>
      <returns>A list of symbols that represent entities read by the condition.</returns>
      <param name="validation">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> that was used previously in a call to <see cref="M:System.Workflow.Activities.Rules.RuleAction.Validate(System.Workflow.Activities.Rules.RuleValidation)" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleCondition.Name">
      <summary>Gets or sets the name of the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" />.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" />.</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleCondition.OnRuntimeInitialized">
      <summary>Called when all properties have been given values. After this call, no properties can be modified.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleCondition.Validate(System.Workflow.Activities.Rules.RuleValidation)">
      <summary>When overridden in a derived class, verifies that the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> is configured correctly and has no errors.</summary>
      <returns>true to show that the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> has no errors; otherwise, false.</returns>
      <param name="validation">The instance of <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> that is used to manage the validation of rules and rule conditions.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleConditionChangeAction">
      <summary>Represents an abstract base class from which all dynamic update notifications of changes to <see cref="T:System.Workflow.Activities.Rules.RuleCondition" />s must be derived.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleConditionChangeAction.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleConditionChangeAction" /> class.</summary>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleConditionChangeAction.ConditionName">
      <summary>When overridden in a derived class, gets the name of the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> to be changed.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> to be changed.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleConditionChangeAction.ValidateChanges(System.Workflow.ComponentModel.Activity)">
      <summary>When overridden in a derived class, verifies that the <see cref="T:System.Workflow.Activities.Rules.RuleConditionChangeAction" /> is configured correctly and has no errors.</summary>
      <returns>The collection of validation errors.</returns>
      <param name="activity">The <see cref="T:System.Workflow.ComponentModel.Activity" /> that the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> changes are occurring in.</param>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleConditionCollection">
      <summary>Contains a collection of <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> classes defined on the workflow. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleConditionCollection.#ctor">
      <summary>Initializes a new instance of a <see cref="T:System.Workflow.Activities.Rules.RuleConditionCollection" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleConditionCollection.Add(System.Workflow.Activities.Rules.RuleCondition)">
      <summary>Adds a <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> to the <see cref="T:System.Workflow.Activities.Rules.RuleConditionCollection" />.</summary>
      <param name="item">The <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> to add to the <see cref="T:System.Workflow.Activities.Rules.RuleConditionCollection" />.</param>
      <exception cref="T:System.Data.ReadOnlyException">The <see cref="T:System.Workflow.Activities.Rules.Rule" /> is runtime initialized.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="item" /> is null.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleConditionCollection.Diff(System.Object,System.Object)">
      <summary>Determines the difference between two <see cref="T:System.Workflow.Activities.Rules.RuleConditionCollection" /> classes.</summary>
      <returns>A list of <see cref="T:System.Workflow.ComponentModel.WorkflowChangeAction" /> classes that account for the differences between the <paramref name="originalDefinition" /> and the <paramref name="changedDefinition" />.</returns>
      <param name="originalDefinition">The original <see cref="T:System.Workflow.Activities.Rules.RuleConditionCollection" />.</param>
      <param name="changedDefinition">The <see cref="T:System.Workflow.Activities.Rules.RuleConditionCollection" /> with proposed changes.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleConditionReference">
      <summary>Represents a <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> in the conditions collection and enables you to programmatically evaluate the condition.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleConditionReference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleConditionReference" /> class. </summary>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleConditionReference.ConditionName">
      <summary>Gets or sets the name of the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> to evaluate.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> to evaluate.</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleConditionReference.Evaluate(System.Workflow.ComponentModel.Activity,System.IServiceProvider)">
      <summary>Evaluates the underlying <see cref="T:System.Workflow.Activities.Rules.RuleCondition" />.</summary>
      <returns>true if the condition evaluates to true; otherwise, false. </returns>
      <param name="activity">The <see cref="T:System.Workflow.ComponentModel.Activity" /> associated with this condition.</param>
      <param name="provider">The <see cref="T:System.IServiceProvider" /> for this condition evaluation.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleConditionReference.InitializeProperties">
      <summary>Sets member properties based on the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> that <see cref="T:System.Workflow.Activities.Rules.RuleConditionReference" /> points to.</summary>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleDefinitions">
      <summary>Represents the root of the .rules files and contains all the <see cref="P:System.Workflow.Activities.Rules.RuleDefinitions.RuleSets" /> and <see cref="T:System.Workflow.Activities.Rules.RuleCondition" />s associated with a workflow. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleDefinitions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleDefinitions" /> class. </summary>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleDefinitions.Conditions">
      <summary>Gets the collection of rule conditions in the workflow.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.RuleConditionCollection" /> that contains all the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" />s in the workflow.</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleDefinitions.Diff(System.Object,System.Object)">
      <summary>Determines the difference between <see cref="P:System.Workflow.Activities.Rules.RuleDefinitions.Conditions" /> and the <see cref="P:System.Workflow.Activities.Rules.RuleDefinitions.RuleSets" /> in the original definition and the changed definition.</summary>
      <returns>A list of <see cref="T:System.Workflow.ComponentModel.WorkflowChangeAction" /> classes that differentiate the <paramref name="originalDefinition" /> from the <paramref name="changedDefinition" />.</returns>
      <param name="originalDefinition">The original rule definition.</param>
      <param name="changedDefinition">The new rule definition.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Workflow.Activities.Rules.RuleDefinitions.RuleDefinitionsProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="T:System.Workflow.Activities.Rules.RuleDefinitions" /> property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleDefinitions.RuleSets">
      <summary>Gets the collection of <see cref="P:System.Workflow.Activities.Rules.RuleDefinitions.RuleSets" /> in the workflow.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.RuleSetCollection" /> that contains all the <see cref="P:System.Workflow.Activities.Rules.RuleDefinitions.RuleSets" /> in the workflow.</returns>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleEngine">
      <summary>Used to perform <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> evaluation.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEngine.#ctor(System.Workflow.Activities.Rules.RuleSet,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleEngine" /> class with a <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> to evaluate and a rule type to create a new instance of <see cref="M:System.Workflow.Activities.Rules.RuleValidation.#ctor(System.Type,System.Workflow.ComponentModel.Compiler.ITypeProvider)" /> to use for rule expression validation.</summary>
      <param name="ruleSet">Specifies the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> to evaluate.</param>
      <param name="objectType">Specifies the <see cref="T:System.Type" /> of the <see cref="T:System.Workflow.Activities.Rules.Rule" /> used to create a new instance of <see cref="M:System.Workflow.Activities.Rules.RuleValidation.#ctor(System.Type,System.Workflow.ComponentModel.Compiler.ITypeProvider)" />.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEngine.#ctor(System.Workflow.Activities.Rules.RuleSet,System.Workflow.Activities.Rules.RuleValidation)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleEngine" /> class with a <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> to evaluate using <see cref="T:System.Workflow.Activities.Rules.RuleValidation" />.</summary>
      <param name="ruleSet">Specifies the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> to evaluate.</param>
      <param name="validation">Specifies the <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> used to validate rule expressions.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEngine.Execute(System.Object)">
      <summary>Executes the current object using <see cref="M:System.Workflow.Activities.Rules.RuleExecution.#ctor(System.Workflow.Activities.Rules.RuleValidation,System.Object)" />.</summary>
      <param name="thisObject">The current object to be executed.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEngine.Execute(System.Object,System.Workflow.ComponentModel.ActivityExecutionContext)">
      <summary>Executes the current <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> against the object instance (which, in this case, is likely an <see cref="T:System.Workflow.ComponentModel.Activity" />).</summary>
      <param name="thisObject">The current object to be executed.</param>
      <param name="executionContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> for the current activity. </param>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleEvaluationException">
      <summary>Represents the base class for all exceptions caused by rule evaluation issues.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEvaluationException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleEvaluationException" /> class.  </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEvaluationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleEvaluationException" /> class with serialized data.</summary>
      <param name="serializeInfo">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEvaluationException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleEvaluationException" /> class with a specified error message.</summary>
      <param name="message">The message that describes the error.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEvaluationException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleEvaluationException" /> class, with a specified error message and a reference to the inner <see cref="T:System.Exception" /> that is the cause of this <see cref="T:System.Exception" />.</summary>
      <param name="message">The message that describes the error.</param>
      <param name="ex">The <see cref="T:System.Exception" /> that is the cause of the current <see cref="T:System.Exception" /> . If the <paramref name="innerException" /> parameter is not a null reference (Nothing in Visual Basic), the current <see cref="T:System.Exception" /> is raised in a catch block that handles the inner <see cref="T:System.Exception" /> . </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException">
      <summary>The exception that is thrown when attempting to apply an operator to incompatible operands when you evaluate a rule.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException" /> class.  </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException" /> class with serialized data.</summary>
      <param name="serializeInfo">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown. </param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serializeInfo" /> is a null reference (Visual Basic Nothing).</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">The class name is a null reference (Visual Basic Nothing) or the <see cref="P:System.Exception.HResult" /> is zero.</exception>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException" /> with a specified error message.</summary>
      <param name="message">A string that contains the error message to associate with this instance.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException" /> class with a specified error message and a reference to the inner <see cref="T:System.Exception" /> that is the cause of this <see cref="T:System.Exception" />.</summary>
      <param name="message">A message that describes the current exception.</param>
      <param name="ex">The <see cref="T:System.Exception" /> instance that caused the current exception.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException.#ctor(System.String,System.Type,System.CodeDom.CodeBinaryOperatorType,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException" /> class with the details of the compatibility violation.</summary>
      <param name="message">A message that describes the current <see cref="T:System.Exception" /> .</param>
      <param name="left">The <see cref="T:System.Type" /> appearing on the left side of the operator.</param>
      <param name="op">The <see cref="T:System.CodeDom.CodeBinaryOperatorType" /> representing the operator whose compatibility is violated.</param>
      <param name="right">The <see cref="T:System.Type" /> appearing on the right side of the operator.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException.#ctor(System.String,System.Type,System.CodeDom.CodeBinaryOperatorType,System.Type,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException" /> class with the details of the compatibility violation. This includes the <see cref="P:System.Exception.InnerException" />.</summary>
      <param name="message">A message that describes the current <see cref="T:System.Exception" /> .</param>
      <param name="left">The <see cref="T:System.Type" /> appearing on the left side of the operator.</param>
      <param name="op">The <see cref="T:System.CodeDom.CodeBinaryOperatorType" /> representing the operator whose compatibility is violated.</param>
      <param name="right">The <see cref="T:System.Type" /> appearing on the right side of the operator.</param>
      <param name="ex">The <see cref="T:System.Exception" /> instance that caused the current exception.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with information about the exception.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown. </param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException.Left">
      <summary>Gets or sets the <see cref="T:System.Type" /> of the left argument of the expression causing the exception.</summary>
      <returns>The <see cref="T:System.Type" /> of the left argument of the expression causing the exception.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException.Operator">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeBinaryOperatorType" /> representing the operator which caused the exception. </summary>
      <returns>The <see cref="T:System.CodeDom.CodeBinaryOperatorType" /> representing the operator which caused the exception. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleEvaluationIncompatibleTypesException.Right">
      <summary>Gets or sets the <see cref="T:System.Type" /> of the right argument of the expression causing the exception.</summary>
      <returns>The <see cref="T:System.Type" /> of the right argument of the expression causing the exception.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleException">
      <summary>Represents the base class for all exceptions caused by evaluation or validation of rules.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleException" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleException" /> class with serialized data.</summary>
      <param name="serializeInfo">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleException" /> class with a specified error message.</summary>
      <param name="message">The message that describes the error.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleException" /> class, with a specified error message and a reference to the inner <see cref="T:System.Exception" /> that is the cause of this <see cref="T:System.Exception" />.</summary>
      <param name="message">The message that describes the error.</param>
      <param name="ex">The <see cref="T:System.Exception" /> that is the cause of the current <see cref="T:System.Exception" /> . If the <paramref name="innerException" /> parameter is not a null reference (Nothing in Visual Basic), the current <see cref="T:System.Exception" /> is raised in a catch block that handles the inner <see cref="T:System.Exception" /> . </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleExecution">
      <summary>Stores state information while executing <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> or <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> classes.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExecution.#ctor(System.Workflow.Activities.Rules.RuleValidation,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleExecution" /> class by using the <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> and the object to be executed.</summary>
      <param name="validation">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> that guarantees that the expression is configured correctly.</param>
      <param name="thisObject">The current object to be executed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="validation" /> or <paramref name="thisObject" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExecution.#ctor(System.Workflow.Activities.Rules.RuleValidation,System.Object,System.Workflow.ComponentModel.ActivityExecutionContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleExecution" /> class by using the <see cref="T:System.Workflow.Activities.Rules.RuleValidation" />, the object to be executed, and the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> for the current activity.</summary>
      <param name="validation">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> that guarantees that the expression is configured correctly.</param>
      <param name="thisObject">The current object to be executed.</param>
      <param name="activityExecutionContext">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> for the current Activity.</param>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleExecution.Activity">
      <summary>Gets the <see cref="T:System.Workflow.ComponentModel.Activity" /> in which the rule is being executed.</summary>
      <returns>The <see cref="T:System.Workflow.ComponentModel.Activity" /> in which the rule is being executed.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleExecution.ActivityExecutionContext">
      <summary>Gets the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> for the current Activity.</summary>
      <returns>The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> for the current Activity.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleExecution.Halted">
      <summary>Gets or sets a value that indicates whether the rule execution was stopped.</summary>
      <returns>true if the rule execution was stopped; otherwise, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleExecution.ThisObject">
      <summary>Gets the current object to be executed.</summary>
      <returns>The object to be executed.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleExecution.Validation">
      <summary>Gets or sets the validation instance for the expression to execute.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> for the expression to execute.</returns>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleExpressionCondition">
      <summary>Defines the condition definition behind a <see cref="T:System.Workflow.Activities.Rules.RuleConditionReference" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionCondition.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleExpressionCondition" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionCondition.#ctor(System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleExpressionCondition" /> class by using a specified code expression.</summary>
      <param name="expression">The <see cref="T:System.CodeDom.CodeExpression" /> to initialize the <see cref="T:System.Workflow.Activities.Rules.RuleExpressionCondition" /> with.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionCondition.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleExpressionCondition" /> class by using a specified name.</summary>
      <param name="conditionName">The name of the condition to evaluate.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conditionName" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionCondition.#ctor(System.String,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleExpressionCondition" /> class by using a specified name and code expression.</summary>
      <param name="conditionName">The name of the condition to evaluate.</param>
      <param name="expression">The <see cref="T:System.CodeDom.CodeExpression" /> to initialize the <see cref="T:System.Workflow.Activities.Rules.RuleExpressionCondition" /> with.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionCondition.Clone">
      <summary>Creates a deep copy of the current <see cref="T:System.Workflow.Activities.Rules.RuleExpressionCondition" />.</summary>
      <returns>A <see cref="T:System.Workflow.Activities.Rules.RuleExpressionCondition" /> that is identical to this instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionCondition.Equals(System.Object)">
      <summary>Determines whether two object instances are equal.</summary>
      <returns>true if the object instances are equal; otherwise, false.</returns>
      <param name="obj">The <see cref="T:System.Object" /> to compare with the current object.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionCondition.Evaluate(System.Workflow.Activities.Rules.RuleExecution)">
      <summary>Evaluates the expression.</summary>
      <returns>true if the expression evaluates to true; otherwise, false.</returns>
      <param name="execution">Instance of the runtime context that <see cref="T:System.Workflow.Activities.Rules.RuleExpressionCondition" /> is running in.</param>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleExpressionCondition.Expression">
      <summary>Gets or sets the expression condition to evaluate. </summary>
      <returns>The expression condition to evaluate.</returns>
      <exception cref="T:System.Data.ReadOnlyException">The activity running the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> is runtime initialized.</exception>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionCondition.GetDependencies(System.Workflow.Activities.Rules.RuleValidation)">
      <summary>Gets the dependencies for the <see cref="T:System.Workflow.Activities.Rules.RuleExpressionCondition" />.</summary>
      <returns>A list of dependencies.</returns>
      <param name="validation">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> to manage the validation process.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionCondition.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>The hash code for this instance. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleExpressionCondition.Name">
      <summary>Gets or sets the name of the condition to evaluate.</summary>
      <returns>The name of the condition to evaluate.</returns>
      <exception cref="T:System.Data.ReadOnlyException">The activity running the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> is runtime initialized.</exception>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionCondition.OnRuntimeInitialized">
      <summary>Called when all properties have been given values. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionCondition.ToString">
      <summary>Returns the string value of the expression condition. </summary>
      <returns>The string value of the expression condition. An empty string if the expression condition is null.</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionCondition.Validate(System.Workflow.Activities.Rules.RuleValidation)">
      <summary>Verifies that the expression is configured correctly and has no errors.</summary>
      <returns>true if the expression has no errors; otherwise, false.</returns>
      <param name="validation">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> to manage the validation process.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="validator" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleExpressionInfo">
      <summary>An instance of this class is returned by the <see cref="M:System.Workflow.Activities.Rules.IRuleExpression.Validate(System.Workflow.Activities.Rules.RuleValidation,System.Boolean)" /> method of an <see cref="T:System.Workflow.Activities.Rules.IRuleExpression" /> expression.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionInfo.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleExpressionInfo" /> class. </summary>
      <param name="expressionType">The <see cref="T:System.Type" /> of the expression.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleExpressionInfo.ExpressionType">
      <summary>Gets the type of the expression.</summary>
      <returns>The <see cref="T:System.Type" /> of the expression.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleExpressionResult">
      <summary>Abstract class that is the base for classes representing the result of custom expressions based on the <see cref="T:System.Workflow.Activities.Rules.IRuleExpression" /> interface.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionResult.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleExpressionResult" /> class.</summary>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleExpressionResult.Value">
      <summary>When overridden in a derived class, gets or sets the result of the expression.</summary>
      <returns>The result of the expression.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleExpressionWalker">
      <summary>Propagates the current behavior in the expression to child nodes. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionWalker.AnalyzeUsage(System.Workflow.Activities.Rules.RuleAnalysis,System.CodeDom.CodeExpression,System.Boolean,System.Boolean,System.Workflow.Activities.Rules.RulePathQualifier)">
      <summary>Identifies the fields and properties used by an expression and adds these as symbols to the <see cref="T:System.Workflow.Activities.Rules.RuleAnalysis" /> instance.</summary>
      <param name="analysis">The <see cref="T:System.Workflow.Activities.Rules.RuleAnalysis" /> instance that stores symbols representing the properties and fields used by rule conditions and actions.</param>
      <param name="expression">The expression being executed.</param>
      <param name="isRead">true if fields and properties read by a <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> are to be added to <see cref="T:System.Workflow.Activities.Rules.RuleAnalysis" />; otherwise, false.</param>
      <param name="isWritten">true if fields and properties written by a <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> are to be added to <see cref="T:System.Workflow.Activities.Rules.RuleAnalysis" />; otherwise, false.</param>
      <param name="qualifier">The path of a field or property determined up to this point.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="analysis" /> is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionWalker.Clone(System.CodeDom.CodeExpression)">
      <summary>Creates a deep copy of the child nodes of the current <see cref="T:System.CodeDom.CodeExpression" />.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that is identical to this instance.</returns>
      <param name="originalExpression">The expression that the clone is based on.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionWalker.Decompile(System.Text.StringBuilder,System.CodeDom.CodeExpression,System.CodeDom.CodeExpression)">
      <summary>Decompiles the child nodes of the expression into string form.</summary>
      <param name="stringBuilder">The <see cref="T:System.Text.StringBuilder" /> that contains the mutable string of characters.</param>
      <param name="expression">The current <see cref="T:System.CodeDom.CodeExpression" />.</param>
      <param name="parentExpression">The parent <see cref="T:System.CodeDom.CodeExpression" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionWalker.Evaluate(System.Workflow.Activities.Rules.RuleExecution,System.CodeDom.CodeExpression)">
      <summary>Evaluates the expression and all child expressions.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.RuleExpressionResult" /> that indicates the result of the expression evaluation.</returns>
      <param name="execution">The <see cref="T:System.Workflow.Activities.Rules.RuleExecution" /> to execute the expression.</param>
      <param name="expression">The <see cref="T:System.CodeDom.CodeExpression" /> to evaluate.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="execution" /> is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionWalker.Match(System.CodeDom.CodeExpression,System.CodeDom.CodeExpression)">
      <summary>Compares the current expression and child nodes to another expression to determine whether they are equal.</summary>
      <returns>true if the expressions are equal; otherwise, false.</returns>
      <param name="firstExpression">The first <see cref="T:System.CodeDom.CodeExpression" /> in the comparison.</param>
      <param name="secondExpression">The second <see cref="T:System.CodeDom.CodeExpression" /> in the comparison.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleExpressionWalker.Validate(System.Workflow.Activities.Rules.RuleValidation,System.CodeDom.CodeExpression,System.Boolean)">
      <summary>Verifies that the expression and child nodes are configured correctly and has no errors.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.RuleExpressionInfo" /> for the <see cref="T:System.CodeDom.CodeExpression" />.</returns>
      <param name="validation">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> to manage the validation process.</param>
      <param name="expression">The <see cref="T:System.CodeDom.CodeExpression" /> to evaluate.</param>
      <param name="isWritten">true if fields and properties written by a <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> are stored in by <see cref="T:System.Workflow.Activities.Rules.RuleAnalysis" />; otherwise, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="validation" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">The <paramref name="expression" /> is null and the errors collection on the <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> object is a null reference (Nothing).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleHaltAction">
      <summary>Causes the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> to stop executing and returns control back to the calling method.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleHaltAction.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleHaltAction" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleHaltAction.Clone">
      <summary>Creates a deep copy of the current <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.</summary>
      <returns>A <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> that is identical to this instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleHaltAction.Equals(System.Object)">
      <summary>Determines whether two object instances are equal.</summary>
      <returns>true if the objects are equal; otherwise, false.</returns>
      <param name="obj">The <see cref="T:System.Object" /> to compare with the current <see cref="T:System.Object" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleHaltAction.Execute(System.Workflow.Activities.Rules.RuleExecution)">
      <summary>Executes the <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> using the specified <see cref="T:System.Workflow.Activities.Rules.RuleExecution" />.</summary>
      <param name="context">The <see cref="T:System.Workflow.Activities.Rules.RuleExecution" /> context used to execute the <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="context" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleHaltAction.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>The hash code for this instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleHaltAction.GetSideEffects(System.Workflow.Activities.Rules.RuleValidation)">
      <summary>Returns the fields and properties updated by the <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.</summary>
      <returns>A list of the fields and properties changed by the <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.NoteSince <see cref="T:System.Workflow.Activities.Rules.RuleHaltAction" /> does not have parameters, the return value will always be an empty list.</returns>
      <param name="validation">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> to manage the validation process.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleHaltAction.ToString">
      <summary>Returns the string "Halt".</summary>
      <returns>The string "Halt".</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleHaltAction.Validate(System.Workflow.Activities.Rules.RuleValidation)">
      <summary>Returns true, no validation is performed.</summary>
      <returns>Always returns true.</returns>
      <param name="validator">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> to manage the validation process.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleInvokeAttribute">
      <summary>Indicates that a method is invoked by a condition. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleInvokeAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleInvokeAttribute" /> class. </summary>
      <param name="methodInvoked">The method invoked by the rule.</param>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleInvokeAttribute.MethodInvoked">
      <summary>Gets the method invoked by the condition.</summary>
      <returns>The method invoked by the condition.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleLiteralResult">
      <summary>Represents the literal result of a custom expression written using <see cref="T:System.Workflow.Activities.Rules.IRuleExpression" />. </summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleLiteralResult.#ctor(System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleLiteralResult" /> class. </summary>
      <param name="literal">The literal result of the expression.</param>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleLiteralResult.Value">
      <summary>Gets the result of the expression.  </summary>
      <returns>The result of the expression.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Workflow.Activities.Rules.RuleLiteralResult.Value" /> is set.</exception>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RulePathQualifier">
      <summary>Builds the path of a field or property.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RulePathQualifier.#ctor(System.String,System.Workflow.Activities.Rules.RulePathQualifier)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RulePathQualifier" /> class. </summary>
      <param name="name">The name of the current entity in the path.</param>
      <param name="next">The next entity, or the field or property, for the path. Null if <paramref name="name" /> is the name of the property or field at the root of the path.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RulePathQualifier.Name">
      <summary>Gets the name of the current field or property.</summary>
      <returns>The name of the current entity in the path.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RulePathQualifier.Next">
      <summary>Gets the next field or property for the path.</summary>
      <returns>The next entity, or the field or property, for the path.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleReadAttribute">
      <summary>Indicates that a property is read by a <see cref="T:System.Workflow.Activities.Rules.Rule" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleReadAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleReadAttribute" /> class. </summary>
      <param name="path">The logical path of the member read by the condition.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleReadAttribute.#ctor(System.String,System.Workflow.Activities.Rules.RuleAttributeTarget)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleReadAttribute" /> class. </summary>
      <param name="path">The logical path of the member read by the condition.</param>
      <param name="target">
        <see cref="T:System.Workflow.Activities.Rules.RuleAttributeTarget" /> enumeration; specifies whether the path is relative to this or to a method parameter.</param>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleReadWriteAttribute">
      <summary>Represents the base class for the <see cref="T:System.Workflow.Activities.Rules.RuleReadAttribute" /> and <see cref="T:System.Workflow.Activities.Rules.RuleWriteAttribute" /> classes.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleReadWriteAttribute.#ctor(System.String,System.Workflow.Activities.Rules.RuleAttributeTarget)">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleReadWriteAttribute" /> class. </summary>
      <param name="path">The path to target of the attribute.</param>
      <param name="target">
        <see cref="T:System.Workflow.Activities.Rules.RuleAttributeTarget" /> enumeration value that determines how to use path to find target.</param>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleReadWriteAttribute.Path">
      <summary>Gets the path to the target of the attribute.</summary>
      <returns>A <see cref="T:System.String" /> containing the path.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleReadWriteAttribute.Target">
      <summary>Gets the <see cref="T:System.Workflow.Activities.Rules.RuleAttributeTarget" /> enumeration value that determines how to use path to find target.</summary>
      <returns>A <see cref="T:System.Workflow.Activities.Rules.RuleAttributeTarget" /> enumeration value that determines how to use the specified path to find target.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleReevaluationBehavior">
      <summary>Specifies whether a <see cref="T:System.Workflow.Activities.Rules.Rule" /> can be reevaluated.</summary>
    </member>
    <member name="F:System.Workflow.Activities.Rules.RuleReevaluationBehavior.Never">
      <summary>Indicates that the <see cref="T:System.Workflow.Activities.Rules.Rule" /> is executed once.  A condition may be evaluated several times until the rule executes actions, but the rule will never be evaluated again.</summary>
    </member>
    <member name="F:System.Workflow.Activities.Rules.RuleReevaluationBehavior.Always">
      <summary>Indicates that the <see cref="T:System.Workflow.Activities.Rules.Rule" /> can be reevaluated multiple times. <see cref="F:System.Workflow.Activities.Rules.RuleReevaluationBehavior.Always" /> is the default value.</summary>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleSet">
      <summary>Contains a collection of <see cref="T:System.Workflow.Activities.Rules.Rule" /> classes along with the semantics for forward-chaining execution of those rules. A <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> can be executed directly in code or using the <see cref="T:System.Workflow.Activities.PolicyActivity" /> activity.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSet.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSet.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> class by using the name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <param name="name">The name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSet.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> class by using the name and description of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <param name="name">The name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</param>
      <param name="description">A description of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</param>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleSet.ChainingBehavior">
      <summary>Gets or sets the forward chaining behavior for the <see cref="T:System.Workflow.Activities.Rules.Rule" /> classes in the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.RuleChainingBehavior" /> for the <see cref="T:System.Workflow.Activities.Rules.Rule" /> classes in the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSet.Clone">
      <summary>Creates a deep copy of the current <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <returns>A <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> that is identical to this instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleSet.Description">
      <summary>Gets or sets a description of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <returns>A description of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSet.Equals(System.Object)">
      <summary>Determines whether two object instances are equal.</summary>
      <returns>true if the objects are equal; otherwise, false.</returns>
      <param name="obj">The <see cref="T:System.Object" /> to compare with the current <see cref="T:System.Object" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSet.Execute(System.Workflow.Activities.Rules.RuleExecution)">
      <summary>Evaluates the <see cref="T:System.Workflow.Activities.Rules.Rule" /> instances in the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <param name="ruleExecution">The <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> associated with the <see cref="T:System.Workflow.ComponentModel.Activity" /> that is invoking the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ruleExecution" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">The cached <see cref="T:System.Workflow.Activities.Rules.RuleExecution" /> cannot be validated.</exception>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSet.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>The hash code for this instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleSet.Name">
      <summary>Gets and sets the name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</returns>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleSet.Rules">
      <summary>Gets a list of <see cref="T:System.Workflow.Activities.Rules.Rule" /> classes in the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <returns>A list of <see cref="T:System.Workflow.Activities.Rules.Rule" /> classes in the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSet.Validate(System.Workflow.Activities.Rules.RuleValidation)">
      <summary>Validates the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> attributes and the <see cref="T:System.Workflow.Activities.Rules.Rule" /> classes in the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <returns>A <see cref="T:System.Workflow.ComponentModel.Compiler.ValidationErrorCollection" /> of errors found during validation.</returns>
      <param name="validation">The <see cref="T:System.Workflow.ComponentModel.Compiler.ValidationManager" /> to manage the validation process.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="validation" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleSetChangeAction">
      <summary>Represents an abstract base class from which all dynamic update notifications of changes to a <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> must be derived. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSetChangeAction.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleSetChangeAction" /> class.</summary>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleSetChangeAction.RuleSetName">
      <summary>When overridden in a derived class, gets the name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> to be changed.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> to be changed.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSetChangeAction.ValidateChanges(System.Workflow.ComponentModel.Activity)">
      <summary>When overridden in a derived class, verifies that the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> is configured correctly and has no errors.</summary>
      <returns>The collection of the validation errors.</returns>
      <param name="activity">The <see cref="T:System.Workflow.ComponentModel.Activity" /> that the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> changes are occurring in.</param>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleSetCollection">
      <summary>Contains a collection of <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> classes defined in a workflow.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSetCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleSetCollection" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSetCollection.Add(System.Workflow.Activities.Rules.RuleSet)">
      <summary>Overloaded. Adds a <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> to the collection</summary>
      <param name="item">The <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> to add to the <see cref="T:System.Workflow.Activities.Rules.RuleSetCollection" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSetCollection.Diff(System.Object,System.Object)">
      <summary>Determines the difference between two <see cref="T:System.Workflow.Activities.Rules.RuleSetCollection" />.</summary>
      <returns>A list of <see cref="T:System.Workflow.ComponentModel.WorkflowChangeAction" /> classes that differentiate the <paramref name="originalDefinition" /> from the <paramref name="changedDefinition" />.</returns>
      <param name="originalDefinition">The original definition of the <see cref="T:System.Workflow.Activities.Rules.RuleSetCollection" />.</param>
      <param name="changedDefinition">The changed definition of the <see cref="T:System.Workflow.Activities.Rules.RuleSetCollection" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleSetReference">
      <summary>Holds the name of a <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> in the <see cref="T:System.Workflow.Activities.Rules.RuleSetCollection" /> on the workflow. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSetReference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleSetReference" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSetReference.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleSetReference" /> class using the name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <param name="ruleSetName">The name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> that the activity evaluates.</param>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleSetReference.RuleSetName">
      <summary>Gets or sets the name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> that the activity evaluates.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> that the activity evaluates.</returns>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleSetValidationException">
      <summary>Represents the exception thrown when processing cannot continue because a rule set cannot be validated.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSetValidationException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleSetValidationException" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSetValidationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleSetValidationException" /> class from serialized data.</summary>
      <param name="serializeInfo">Reference to the object that holds the data needed to deserialize the exception.</param>
      <param name="context">Provides the means for deserializing the exception data.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSetValidationException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleSetValidationException" /> class. </summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSetValidationException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleSetValidationException" /> class. </summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <param name="ex">The inner exception.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSetValidationException.#ctor(System.String,System.Workflow.ComponentModel.Compiler.ValidationErrorCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleSetValidationException" /> class. </summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <param name="errors">Collection of validation errors that occurred while validating a rule set.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleSetValidationException.Errors">
      <summary>Gets a collection of validation errors that occurred while validating a rule set.</summary>
      <returns>A <see cref="T:System.Workflow.ComponentModel.Compiler.ValidationErrorCollection" /> that contains the validation errors.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleSetValidationException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Implements the <see cref="T:System.Runtime.Serialization.ISerializable" /> interface.</summary>
      <param name="info">Reference to the object that holds the data needed to serialize and deserialize the exception.</param>
      <param name="context">Provides the means for serializing the exception data.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleStatementAction">
      <summary>Specifies a property or field to update or a method to run using the CodeDom types.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleStatementAction.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleStatementAction" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleStatementAction.#ctor(System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleStatementAction" /> class using a <see cref="T:System.CodeDom.CodeExpression" />.</summary>
      <param name="codeDomExpression">A <see cref="T:System.CodeDom.CodeExpression" /> specifying the action to perform.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleStatementAction.#ctor(System.CodeDom.CodeStatement)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleStatementAction" /> class using a <see cref="T:System.CodeDom.CodeStatement" />.</summary>
      <param name="codeDomStatement">A <see cref="T:System.CodeDom.CodeStatement" /> specifying the action to perform.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleStatementAction.Clone">
      <summary>Creates a deep copy of the current <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.</summary>
      <returns>A <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> that is identical to this instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleStatementAction.CodeDomStatement">
      <summary>Gets or sets the code statement specifying the action to perform.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeStatement" /> specifying the action to perform.</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleStatementAction.Equals(System.Object)">
      <summary>Determines whether two object instances are equal.</summary>
      <returns>true if the objects are equal; otherwise, false.</returns>
      <param name="obj">The <see cref="T:System.Object" /> to compare with the current <see cref="T:System.Object" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleStatementAction.Execute(System.Workflow.Activities.Rules.RuleExecution)">
      <summary>Executes the <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> using the specified <see cref="T:System.Workflow.Activities.Rules.RuleExecution" />.</summary>
      <param name="context">The <see cref="T:System.Workflow.Activities.Rules.RuleExecution" /> context used to execute the <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleStatementAction.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>The hash code for this instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleStatementAction.GetSideEffects(System.Workflow.Activities.Rules.RuleValidation)">
      <summary>Returns the fields and properties updated by a <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.</summary>
      <returns>A list of the fields and properties changed by a <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.</returns>
      <param name="validation">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> to manage the validation process.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleStatementAction.ToString">
      <summary>Returns the string value of the action. </summary>
      <returns>The string value of the action.</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleStatementAction.Validate(System.Workflow.Activities.Rules.RuleValidation)">
      <summary>Verifies that the <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> is configured correctly and has no errors.</summary>
      <returns>true if the <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> had no errors; otherwise, false.</returns>
      <param name="validator">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> to manage the validation process.</param>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleUpdateAction">
      <summary>Indicates that a rule is updating a variable, in order to cause reevaluation of affected rules.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleUpdateAction.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleUpdateAction" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleUpdateAction.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleUpdateAction" /> class. </summary>
      <param name="path">A string that represents the name of the field or property changed. The character "/" is used as a separator. </param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleUpdateAction.Clone">
      <summary>Creates a deep copy of the current <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.</summary>
      <returns>A <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> that is identical to this instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleUpdateAction.Equals(System.Object)">
      <summary>Determines whether two object instances are equal.</summary>
      <returns>true if the objects are equal; otherwise, false.</returns>
      <param name="obj">The <see cref="T:System.Object" /> to compare with the current <see cref="T:System.Object" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleUpdateAction.Execute(System.Workflow.Activities.Rules.RuleExecution)">
      <summary>Executes the <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> using the specified <see cref="T:System.Workflow.Activities.Rules.RuleExecution" />.</summary>
      <param name="context">The <see cref="T:System.Workflow.Activities.Rules.RuleExecution" /> context used to execute the <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleUpdateAction.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>The hash code for this instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleUpdateAction.GetSideEffects(System.Workflow.Activities.Rules.RuleValidation)">
      <summary>Returns the fields and properties updated by a <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.</summary>
      <returns>A list of the fields and properties changed by a <see cref="T:System.Workflow.Activities.Rules.RuleAction" />.</returns>
      <param name="validation">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> to manage the validation process.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleUpdateAction.Path">
      <summary>Gets or sets the path of the <see cref="T:System.Workflow.Activities.Rules.RuleUpdateAction" />.</summary>
      <returns>The path of the <see cref="T:System.Workflow.Activities.Rules.RuleUpdateAction" />.</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleUpdateAction.ToString">
      <summary>Returns a string that indicates the field or property that the <see cref="T:System.Workflow.Activities.Rules.Rule" /> is updating.</summary>
      <returns>The string "Update &lt;<see cref="P:System.Workflow.Activities.Rules.RuleUpdateAction.Path" />&gt;".</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleUpdateAction.Validate(System.Workflow.Activities.Rules.RuleValidation)">
      <summary>Verifies that the <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> is configured correctly and has no errors.</summary>
      <returns>true if the <see cref="T:System.Workflow.Activities.Rules.RuleAction" /> has no errors; otherwise, false.</returns>
      <param name="validator">The <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> to manage the validation process.</param>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleValidation">
      <summary>Validates expression conditions. </summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleValidation.#ctor(System.Type,System.Workflow.ComponentModel.Compiler.ITypeProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> class using the <see cref="T:System.Type" /> of the <see cref="T:System.Workflow.Activities.Rules.Rule" />. </summary>
      <param name="thisType">The type of the object that this <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> or <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> will be executed against. Normally it is the workflow, but it can be any valid type.</param>
      <param name="typeProvider">An optional <see cref="T:System.Workflow.ComponentModel.Compiler.ITypeProvider" /> implementation that is used to determine what <see cref="T:System.Type" />s are available in the condition. If one is not specified, only types in the assembly specified by the activity and any assemblies it references are available.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="thisType" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleValidation.#ctor(System.Workflow.ComponentModel.Activity,System.Workflow.ComponentModel.Compiler.ITypeProvider,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleValidation" /> class using the specified <see cref="T:System.Workflow.ComponentModel.Activity" /> and the type provider. </summary>
      <param name="activity">The <see cref="T:System.Workflow.ComponentModel.Activity" /> in which context the <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> or <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> is run. In most cases, this will be the workflow object.</param>
      <param name="typeProvider">An optional <see cref="T:System.Workflow.ComponentModel.Compiler.ITypeProvider" /> implementation that is used to determine what <see cref="T:System.Type" />s are available in the condition. If one is not specified, only types in the assembly specified by the activity and any assemblies it references are available.</param>
      <param name="checkStaticType">If true, the types used by any <see cref="T:System.Workflow.Activities.Rules.RuleCondition" /> or <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> that are validated with this object must be in the restricted list of types allowed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="activity" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleValidation.Errors">
      <summary>Gets the errors associated with the <see cref="T:System.Workflow.Activities.Rules.RuleValidation" />.</summary>
      <returns>The <see cref="T:System.Workflow.ComponentModel.Compiler.ValidationErrorCollection" /> that contains the <see cref="T:System.Workflow.ComponentModel.Compiler.ValidationError" /> objects created during rule validation.</returns>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleValidation.ExpressionInfo(System.CodeDom.CodeExpression)">
      <summary>Determines the type of the code expression.</summary>
      <returns>The <see cref="T:System.Workflow.Activities.Rules.RuleExpressionInfo" /> for the object. This identifies the type of the object.</returns>
      <param name="expression">The <see cref="T:System.CodeDom.CodeExpression" /> to evaluate.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleValidation.PopParentExpression">
      <summary>Removes and returns the parent expressions at the top of the <see cref="T:System.Collections.Stack" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleValidation.PushParentExpression(System.CodeDom.CodeExpression)">
      <summary>Inserts an object at the top of the <see cref="T:System.Collections.Stack" />. </summary>
      <returns>true if the <paramref name="newParent" /> was added to the stack; otherwise, false.</returns>
      <param name="newParent">The <see cref="T:System.CodeDom.CodeExpression" /> to add to the <see cref="T:System.Collections.Stack" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newParent" /> is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.RuleValidation.ThisType">
      <summary>Gets the type of object.</summary>
      <returns>The <see cref="T:System.Type" /> of the object.</returns>
    </member>
    <member name="T:System.Workflow.Activities.Rules.RuleWriteAttribute">
      <summary>Indicates that a property or field is written by the method or property that this attribute is applied to. This information is used by the rules engine to determine dependencies between rules. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleWriteAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleWriteAttribute" /> class.  </summary>
      <param name="path">The field or property that is on the same class as the method or property that this attribute is applied to.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.RuleWriteAttribute.#ctor(System.String,System.Workflow.Activities.Rules.RuleAttributeTarget)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.RuleWriteAttribute" /> class.  </summary>
      <param name="path">The field or property that is on the same class as the method or property that this attribute is applied to.</param>
      <param name="target">
        <see cref="T:System.Workflow.Activities.Rules.RuleAttributeTarget" /> enumeration value that determines how to use path to find target.</param>
    </member>
    <member name="T:System.Workflow.Activities.Rules.UpdatedConditionAction">
      <summary>Represents the change to a <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> for dynamic updates. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.UpdatedConditionAction.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.UpdatedConditionAction" /> class.  </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.UpdatedConditionAction.#ctor(System.Workflow.Activities.Rules.RuleCondition,System.Workflow.Activities.Rules.RuleCondition)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.UpdatedConditionAction" /> class, with the existing and new conditions of the change.</summary>
      <param name="conditionDefinition">The condition being replaced.</param>
      <param name="newConditionDefinition">The replacing condition.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conditionDefinition" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newConditionDefinition" /> is a null reference (Nothing).</exception>
      <exception cref="T:System.ArgumentException">The name of the <paramref name="conditionDefinition" /> is not equal to the name of <paramref name="newConditionDefinition." /></exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.UpdatedConditionAction.ConditionDefinition">
      <summary>Gets or sets the condition being replaced by this action.</summary>
      <returns>The condition being replaced by this action.</returns>
      <exception cref="T:System.ArgumentNullException">Trying to set this property to a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.UpdatedConditionAction.ConditionName">
      <summary>Gets the name of the condition be replaced.</summary>
      <returns>The name of the condition to be replaced.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.UpdatedConditionAction.NewConditionDefinition">
      <summary>Gets or sets the condition which will replace the existing condition.</summary>
      <returns>The condition which will replace the existing condition.</returns>
      <exception cref="T:System.ArgumentNullException">Trying to set this property to a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.UpdatedRuleSetAction">
      <summary>Represents the change to a <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> for dynamic updates. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.UpdatedRuleSetAction.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.UpdatedRuleSetAction" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Activities.Rules.UpdatedRuleSetAction.#ctor(System.Workflow.Activities.Rules.RuleSet,System.Workflow.Activities.Rules.RuleSet)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.UpdatedRuleSetAction" /> class by using the original and the updated <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> classes.</summary>
      <param name="originalRuleSetDefinition">The original <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</param>
      <param name="updatedRuleSetDefinition">The updated <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</param>
      <exception cref="T:System.ArgumentNullException">The RuleSetDefinition or updatedRuleSetDefinition is null</exception>
      <exception cref="T:System.ArgumentException">The names of the two RuleSets are not the same</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.UpdatedRuleSetAction.OriginalRuleSetDefinition">
      <summary>Gets or sets the original <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <returns>The original <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</returns>
      <exception cref="T:System.ArgumentNullException">The original <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.UpdatedRuleSetAction.RuleSetName">
      <summary>Gets the name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Activities.Rules.UpdatedRuleSetAction.UpdatedRuleSetDefinition">
      <summary>Gets or sets the updated <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</summary>
      <returns>The updated <see cref="T:System.Workflow.Activities.Rules.RuleSet" />.</returns>
      <exception cref="T:System.ArgumentNullException">The updated <see cref="T:System.Workflow.Activities.Rules.RuleSet" /> is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Activities.Rules.Design.RuleConditionDialog">
      <summary>This dialog is normally used by the activities <see cref="T:System.Workflow.Activities.IfElseBranchActivity" />, <see cref="T:System.Workflow.Activities.WhileActivity" />, <see cref="T:System.Workflow.Activities.ConditionedActivityGroup" />, and <see cref="T:System.Workflow.Activities.ReplicatorActivity" /> to edit declarative conditions. However, if you want to use conditions outside of these activities (for example, edit a condition for use outside of a workflow completely), you can use this dialog to perform the editing of the condition.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.Design.RuleConditionDialog.#ctor(System.Type,System.Workflow.ComponentModel.Compiler.ITypeProvider,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.Design.RuleConditionDialog" /> class with the specified activity type, the type provider, and the expression.</summary>
      <param name="activityType">The type of the object that this condition is executed against. Normally it is the workflow, but can be any valid type.</param>
      <param name="typeProvider">An optional <see cref="T:System.Workflow.ComponentModel.Compiler.ITypeProvider" /> implementation that is used to determine what types are available in the condition. If one is not specified, only types in the assembly specified by <paramref name="activityType" /> and any assemblies it references is available.</param>
      <param name="expression">The initial <see cref="T:System.CodeDom.CodeExpression" /> to be displayed.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.Design.RuleConditionDialog.#ctor(System.Workflow.ComponentModel.Activity,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.Design.RuleConditionDialog" /> class. </summary>
      <param name="activity">The <see cref="T:System.Workflow.ComponentModel.Activity" /> that this condition is executed against. Typically it is the workflow, but it can be any valid activity.</param>
      <param name="expression">The initial <see cref="T:System.CodeDom.CodeExpression" /> to be displayed.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.Design.RuleConditionDialog.Dispose(System.Boolean)">
      <summary>Releases all resources used by the <see cref="T:System.Workflow.Activities.Rules.Design.RuleConditionDialog" />.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="P:System.Workflow.Activities.Rules.Design.RuleConditionDialog.Expression">
      <summary>Gets the <see cref="T:System.CodeDom.CodeExpression" /> as modified by the dialog box user.</summary>
      <returns>The <see cref="T:System.CodeDom.CodeExpression" /> as modified by the dialog box user.</returns>
    </member>
    <member name="T:System.Workflow.Activities.Rules.Design.RuleSetDialog">
      <summary>This dialog is normally used by <see cref="T:System.Workflow.Activities.PolicyActivity" /> to edit rule sets. However, if you want to use a rule set outside of this activity (for example, edit a rule set for use outside of a workflow), you can use this dialog to perform the editing of the rule set.</summary>
    </member>
    <member name="M:System.Workflow.Activities.Rules.Design.RuleSetDialog.#ctor(System.Type,System.Workflow.ComponentModel.Compiler.ITypeProvider,System.Workflow.Activities.Rules.RuleSet)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.Design.RuleSetDialog" /> class. </summary>
      <param name="activityType">The type of the object that this condition is executed against. Normally it is the workflow, but can be any valid type.</param>
      <param name="typeProvider">An optional <see cref="T:System.Workflow.ComponentModel.Compiler.ITypeProvider" /> implementation that is used to determine what Types are available in the condition. If one is not specified, only types in the assembly specified by <paramref name="activityType" /> and any assemblies it references are available.</param>
      <param name="ruleSet">The initial <see cref="T:System.Workflow.Activies.Rules.RuleSet" /> to be displayed.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.Design.RuleSetDialog.#ctor(System.Workflow.ComponentModel.Activity,System.Workflow.Activities.Rules.RuleSet)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Activities.Rules.Design.RuleSetDialog" /> class. </summary>
      <param name="activity">The <see cref="T:System.Workflow.ComponentModel.Activity" /> that this <see cref="T:System.Workflow.Activies.Rules.RuleSet" /> is executed against. Typically it is the workflow (as used by <see cref="T:System.Workflow.Activities.PolicyActivity" />), but it can be any valid activity.</param>
      <param name="ruleSet">The initial <see cref="T:System.Workflow.Activies.Rules.RuleSet" /> to be displayed.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.Design.RuleSetDialog.Dispose(System.Boolean)">
      <summary>Releases all resources used by the <see cref="T:System.Workflow.Activities.Rules.Design.RuleSetDialog" />.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Workflow.Activities.Rules.Design.RuleSetDialog.ProcessCmdKey(System.Windows.Forms.Message@,System.Windows.Forms.Keys)">
      <summary>Processes a command key.</summary>
      <returns>true if the keystroke was processed and accessed by the control; false to allow further processing.</returns>
      <param name="msg">A <see cref="T:System.Windows.Forms.Message" />, passed by reference that represents the Win32 message to process.</param>
      <param name="keyData">One of the <see cref="T:System.Windows.Forms.Keys" /> values that represents the key to process.</param>
    </member>
    <member name="P:System.Workflow.Activities.Rules.Design.RuleSetDialog.RuleSet">
      <summary>Gets the <see cref="T:System.Workflow.Activies.Rules.RuleSet" /> as confirmed by the dialog box user.</summary>
      <returns>The <see cref="T:System.Workflow.Activies.Rules.RuleSet" /> as confirmed by the dialog box user.</returns>
    </member>
  </members>
</doc>