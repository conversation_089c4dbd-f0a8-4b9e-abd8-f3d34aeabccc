using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using OCRTools.Common;

namespace OCRTools.UserControlEx
{
    /// <summary>
    /// 文本流方向检测器，负责分析文本块的排列方向。
    /// </summary>
    public class TextFlowDirectionDetector
    {
        private readonly List<TextCellInfo> cells;

        // 自适应分组阈值
        private double groupingThresholdVertical = 10;
        private double groupingThresholdHorizontal = 10;

        // 极端竖排比例阈值
        private double extremeVerticalRatioThreshold = 7.0;

        public TextFlowDirectionDetector(List<TextCellInfo> cells)
        {
            this.cells = cells;
        }

        /// <summary>
        /// 文本方向检测结果
        /// </summary>
        public class TextDirectionResult
        {
            /// <summary>水平方向</summary>
            public TextFlowDirection HorizontalDirection { get; set; } = TextFlowDirection.LeftToRight;
            
            /// <summary>垂直方向</summary>
            public TextFlowDirection VerticalDirection { get; set; } = TextFlowDirection.TopToBottom;
            
            /// <summary>水平方向置信度</summary>
            public int HorizontalConfidence { get; set; } = 0;
            
            /// <summary>垂直方向置信度</summary>
            public int VerticalConfidence { get; set; } = 0;
            
            /// <summary>是否为竖排布局</summary>
            public bool IsVerticalLayout { get; set; } = false;
            
            /// <summary>布局置信度</summary>
            public int LayoutConfidence { get; set; } = 0;
            
            /// <summary>是否从右到左</summary>
            public bool IsRightToLeft { get; set; } = false;
            
            /// <summary>主要流向</summary>
            public TextFlowDirection FlowDirection { 
                get {
                    return IsVerticalLayout ? VerticalDirection : HorizontalDirection;
                }
                set {
                    if (IsVerticalLayout) {
                        VerticalDirection = value;
                    } else {
                        HorizontalDirection = value;
                    }
                }
            }
            
            /// <summary>主要方向置信度</summary>
            public int DirectionConfidence {
                get {
                    return IsVerticalLayout ? VerticalConfidence : HorizontalConfidence;
                }
                set {
                    if (IsVerticalLayout) {
                        VerticalConfidence = value;
                    } else {
                        HorizontalConfidence = value;
                    }
                }
            }
            
            /// <summary>
            /// 获取主要方向
            /// </summary>
            public TextFlowDirection GetPrimaryDirection()
            {
                if (IsVerticalLayout)
                {
                    return VerticalDirection;
                }
                else
                {
                    return HorizontalDirection;
                }
            }
            
            /// <summary>
            /// 结果字符串表示
            /// </summary>
            public override string ToString()
            {
                return $"布局: {(IsVerticalLayout ? "竖排" : "横排")} (置信度: {LayoutConfidence}%), " +
                       $"主方向: {GetPrimaryDirection()} (置信度: {DirectionConfidence}%), " +
                       $"水平: {HorizontalDirection} ({HorizontalConfidence}%), " +
                       $"垂直: {VerticalDirection} ({VerticalConfidence}%)";
            }
        }

        /// <summary>
        /// 检测文本方向（兼容旧版本）
        /// </summary>
        /// <returns>主要文本方向</returns>
        public TextFlowDirection Detect()
        {
            var result = DetectDirections();
            return result.GetPrimaryDirection();
        }

        /// <summary>
        /// 检测文本流方向
        /// </summary>
        public TextDirectionResult DetectDirections()
        {
            if (cells == null || cells.Count < 2)
            {
                return new TextDirectionResult
                {
                    IsVerticalLayout = false,
                    FlowDirection = TextFlowDirection.LeftToRight,
                    LayoutConfidence = 50,
                    DirectionConfidence = 50
                };
            }

            try
            {
                // 1. 提取文本特征
                var features = ExtractTextFeatures(cells);
                
                // 2. 从特征中提取方向证据
                var evidence = ExtractDirectionEvidence(features);
                
                // 3. 创建结果对象
                var result = new TextDirectionResult();
                
                // 4. 确定布局类型 (横排/竖排)
                DetermineLayoutType(result, evidence);
                
                // 5. 确定文本流方向
                if (result.IsVerticalLayout)
                {
                    // 竖排布局 - 确定是从上到下还是从下到上
                    DetermineVerticalFlowDirection(result, evidence);
                }
                else
                {
                    // 横排布局 - 确定是从左到右还是从右到左
                    DetermineHorizontalFlowDirection(result, evidence);
                }
                
                // 6. 优化方向检测结果
                OptimizeDirectionDetection(cells, result);
                
                // 7. 确保水平和垂直方向与主方向一致
                if (result.IsVerticalLayout)
                {
                    result.VerticalDirection = result.FlowDirection;
                    result.VerticalConfidence = result.DirectionConfidence;
                    
                    // 【新增】确保竖排文本的水平方向（列排序）与IsRightToLeft一致
                    if (result.IsRightToLeft && result.HorizontalDirection != TextFlowDirection.RightToLeft)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        // 如果水平方向置信度为0或过低，设置一个合理的值
                        if (result.HorizontalConfidence < 70)
                        {
                            // 检查是否为中文竖排文本
                            bool likelyCJKText = false;
                            int cjkCharCount = 0;
                            int totalCharCount = 0;
                            
                            foreach (var cell in cells)
                            {
                                if (string.IsNullOrEmpty(cell.words)) continue;
                                
                                foreach (char c in cell.words)
                                {
                                    totalCharCount++;
                                    // 检查是否为中日韩文字（粗略判断）
                                    if (c >= 0x4E00 && c <= 0x9FFF) // 基本汉字范围
                                    {
                                        cjkCharCount++;
                                    }
                                }
                            }
                            
                            // 如果大部分是中日韩文字，增加置信度
                            if (totalCharCount > 0 && (double)cjkCharCount / totalCharCount > 0.6)
                            {
                                likelyCJKText = true;
                            }
                            
                            result.HorizontalConfidence = likelyCJKText ? 85 : 70; // 中文竖排文本更高的置信度
                            System.Diagnostics.Debug.WriteLine($"最终确保水平方向一致性，设置RightToLeft置信度为: {result.HorizontalConfidence}%");
                        }
                    }
                    else if (!result.IsRightToLeft && result.HorizontalDirection == TextFlowDirection.RightToLeft)
                    {
                        // 保持一致性，如果水平方向是从右到左，IsRightToLeft也应该为true
                        result.IsRightToLeft = true;
                    }
                }
                else
                {
                    result.HorizontalDirection = result.FlowDirection;
                    result.HorizontalConfidence = result.DirectionConfidence;
                    
                    // 【新增】确保水平方向与IsRightToLeft一致
                    result.IsRightToLeft = (result.HorizontalDirection == TextFlowDirection.RightToLeft);
                }
                
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"文本方向检测异常: {ex.Message}");
                
                // 返回默认结果
                return new TextDirectionResult
                {
                    IsVerticalLayout = false,
                    FlowDirection = TextFlowDirection.LeftToRight,
                    LayoutConfidence = 50,
                    DirectionConfidence = 50
                };
            }
        }
        
        /// <summary>
        /// 优化竖排布局的方向判断
        /// </summary>
        /// <param name="result">方向结果</param>
        /// <param name="features">文本布局特征</param>
        /// <param name="evidence">方向证据</param>
        private void OptimizeVerticalLayoutDirections(TextDirectionResult result, TextLayoutFeatures features, DirectionEvidence evidence)
        {
            // 针对竖排文本的特殊优化
            if (features.VerticalTextCount <= features.HorizontalTextCount) return;
            
            // 1. 多列竖排文本的水平方向判断
            if (features.VerticalColumnCount >= 2)
            {
                // 计算右到左和左到右的证据比例
                double totalHorizontalEvidence = evidence.LeftToRightCount + evidence.RightToLeftCount;
                if (totalHorizontalEvidence > 0)
                {
                    double rtlRatio = evidence.RightToLeftCount / totalHorizontalEvidence;
                    
                    // 【修改】降低阈值，更倾向于判断为从右到左
                    // 如果右到左证据占比较高，调整方向和置信度
                    if (rtlRatio > 0.35) // 阈值降低，从0.4降低到0.35
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true; // 同步设置IsRightToLeft属性
                        
                        // 置信度基于证据比例
                        int confidenceBoost = (int)(Math.Min(30, features.VerticalColumnCount * 6) * 
                                                   Math.Min(1.0, rtlRatio + 0.25));
                        
                        result.HorizontalConfidence = Math.Min(95, 
                            Math.Max(result.HorizontalConfidence, result.HorizontalConfidence + confidenceBoost));
                    }
                }
                
                // 2. 基于内容分布的判断
                if (features.ContentDensityRightHalf > 0 && features.ContentDensityLeftHalf > 0)
                {
                    double densityRatio = features.ContentDensityRightHalf / features.ContentDensityLeftHalf;
                    
                    // 【修改】降低阈值，更敏感地检测右半页密度高于左半页的情况
                    // 右半页密度明显高于左半页，增强从右到左的可能性
                    if (densityRatio > 1.1) // 阈值降低，从1.2降低到1.1
                    {
                        // 如果已经是RightToLeft，增强置信度
                        if (result.HorizontalDirection == TextFlowDirection.RightToLeft)
                        {
                            result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 15);
                            System.Diagnostics.Debug.WriteLine($"右半页密度明显高于左半页(比例:{densityRatio:F2})，增强RightToLeft置信度");
                        }
                        // 如果是LeftToRight但置信度不高，考虑切换
                        else if (result.HorizontalConfidence < 75) // 提高阈值，从70提高到75
                        {
                            result.HorizontalDirection = TextFlowDirection.RightToLeft;
                            result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 75);
                            System.Diagnostics.Debug.WriteLine("基于内容分布，切换到RightToLeft方向");
                        }
                    }
                    // 左半页密度明显高于右半页，增强从左到右的可能性
                    else if (densityRatio < 0.8)
                    {
                        // 如果已经是LeftToRight，增强置信度
                        if (result.HorizontalDirection == TextFlowDirection.LeftToRight)
                        {
                            result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 10);
                        }
                        // 如果是RightToLeft但置信度不高，考虑切换
                        else if (result.HorizontalConfidence < 70)
                        {
                            result.HorizontalDirection = TextFlowDirection.LeftToRight;
                            result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 70);
                        }
                    }
                }
                
                // 【新增】3. 分析列的排列顺序
                if (features.VerticalColumns.Count >= 2)
                {
                    // 计算每列的平均字符数或内容量
                    List<double> columnContentAmounts = new List<double>();
                    foreach (var column in features.VerticalColumns)
                    {
                        double contentAmount = column.Sum(c => c.words?.Length ?? 0);
                        columnContentAmounts.Add(contentAmount);
                    }
                    
                    // 检查是否第一列（最左侧或最右侧）内容量最大
                    if (columnContentAmounts.Count >= 2)
                    {
                        double firstColumnAmount = columnContentAmounts.First();
                        double lastColumnAmount = columnContentAmounts.Last();
                        double avgColumnAmount = columnContentAmounts.Average();
                        
                        // 如果最右侧列内容量明显大于平均值，可能是从右到左阅读
                        if (firstColumnAmount > avgColumnAmount * 1.2 && 
                            features.VerticalColumns.First().Average(c => c.location.left) > features.PageCenter_X)
                        {
                            if (result.HorizontalDirection == TextFlowDirection.RightToLeft)
                            {
                                result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 10);
                                System.Diagnostics.Debug.WriteLine("最右侧列内容量最大，增强RightToLeft置信度");
                            }
                            else if (result.HorizontalConfidence < 70)
                            {
                                result.HorizontalDirection = TextFlowDirection.RightToLeft;
                                result.HorizontalConfidence = Math.Max(70, result.HorizontalConfidence);
                                System.Diagnostics.Debug.WriteLine("基于列内容分布，切换到RightToLeft方向");
                            }
                        }
                        // 如果最左侧列内容量明显大于平均值，可能是从左到右阅读
                        else if (lastColumnAmount > avgColumnAmount * 1.2 && 
                                features.VerticalColumns.Last().Average(c => c.location.left) < features.PageCenter_X)
                        {
                            if (result.HorizontalDirection == TextFlowDirection.LeftToRight)
                            {
                                result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 10);
                            }
                            else if (result.HorizontalConfidence < 70)
                            {
                                result.HorizontalDirection = TextFlowDirection.LeftToRight;
                                result.HorizontalConfidence = Math.Max(70, result.HorizontalConfidence);
                            }
                        }
                    }
                }
            }
            
            // 3. 单列竖排文本的特殊处理
            else if (features.VerticalTextCount >= 3)
            {
                // 由于在ExtractTextDirectionFeatures中已检测了CJK文本特征
                // 这里直接通过features中的LeftToRightEvidence和RightToLeftEvidence判断
                bool likelyCJKText = features.RightToLeftEvidence > features.LeftToRightEvidence;
                
                // 【修改】增强中日韩单列竖排文本的从右到左判断
                // 对于中日韩文字的单列竖排，增强从右到左的可能性
                if (likelyCJKText)
                {
                    // 【修改】无论当前置信度如何，都优先考虑从右到左方向
                    result.HorizontalDirection = TextFlowDirection.RightToLeft;
                    result.IsRightToLeft = true; // 同步设置IsRightToLeft属性
                    result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 85); // 提高置信度到85
                    System.Diagnostics.Debug.WriteLine("检测到疑似中日韩单列竖排文本，强制设置为RightToLeft方向，置信度: 85%");
                    
                    // 【新增】检查文本位置，如果偏向页面右侧，进一步增强置信度
                    double avgTextCenter = this.cells.Average(c => c.location.left + c.location.width / 2);
                    if (avgTextCenter > features.PageCenter_X)
                    {
                        result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 10);
                        System.Diagnostics.Debug.WriteLine("文本位于页面右侧，进一步增强RightToLeft置信度到: " + result.HorizontalConfidence + "%");
                    }
                }
                // 【新增】即使不是CJK文本，如果是单列竖排，也应该有默认的水平方向置信度
                else if (result.HorizontalConfidence < 60)
                {
                    // 设置一个适中的默认置信度，避免置信度为0
                    result.HorizontalConfidence = 60;
                    System.Diagnostics.Debug.WriteLine("单列竖排文本，设置默认水平方向置信度: 60%");
                }
            }
            
            // 4. 确保垂直方向置信度较高
            if (result.VerticalDirection == TextFlowDirection.TopToBottom)
            {
                result.VerticalConfidence = Math.Max(result.VerticalConfidence, 85);
            }
        }
        
        /// <summary>
        /// 检查文本是否主要为中日韩文字
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <returns>是否为中日韩文字</returns>
        private bool IsCJKText(List<TextCellInfo> cells)
        {
            int cjkCharCount = 0;
            int totalCharCount = 0;
            
            foreach (var cell in cells)
            {
                if (string.IsNullOrEmpty(cell.words)) continue;
                
                foreach (char c in cell.words)
                {
                    totalCharCount++;
                    // 【修改】扩大中日韩文字的检测范围
                    // 检查是否为中日韩文字（扩展判断范围）
                    if ((c >= 0x4E00 && c <= 0x9FFF) || // 基本汉字范围
                        (c >= 0x3400 && c <= 0x4DBF) || // 扩展A区
                        (c >= 0x20000 && c <= 0x2A6DF) || // 扩展B区
                        (c >= 0x2A700 && c <= 0x2B73F) || // 扩展C区
                        (c >= 0x2B740 && c <= 0x2B81F) || // 扩展D区
                        (c >= 0x3040 && c <= 0x309F) || // 平假名
                        (c >= 0x30A0 && c <= 0x30FF) || // 片假名
                        (c >= 0xAC00 && c <= 0xD7AF))   // 韩文音节
                    {
                        cjkCharCount++;
                    }
                }
            }
            
            // 【修改】降低阈值，从0.7降低到0.6
            // 如果60%以上是中日韩文字，认为是中日韩文本
            return totalCharCount > 0 && (double)cjkCharCount / totalCharCount > 0.6;
        }
        
        /// <summary>
        /// 基于统一特征收集方向证据
        /// </summary>
        /// <param name="features">文本布局特征</param>
        /// <returns>方向证据</returns>
        private DirectionEvidence CollectDirectionEvidenceFromFeatures(TextLayoutFeatures features)
        {
            var evidence = new DirectionEvidence();
            
            // 1. 从形状特征收集证据
            evidence.HorizontalTextCount = features.HorizontalTextCount;
            evidence.VerticalTextCount = features.VerticalTextCount;
            
            // 2. 从行列结构特征收集证据
            evidence.HorizontalAlignedRows = features.HorizontalRowCount;
            evidence.VerticalAlignedColumns = features.VerticalColumnCount;
            
            // 3. 从对齐特征收集证据
            evidence.LeftAlignedCount = features.LeftAlignedCount;
            evidence.RightAlignedCount = features.RightAlignedCount;
            evidence.TopAlignedCount = features.TopAlignedCount;
            evidence.BottomAlignedCount = features.BottomAlignedCount;
            
            // 4. 从边缘变异特征收集证据
            evidence.LeftEdgeVariance = features.LeftEdgeVariance;
            evidence.RightEdgeVariance = features.RightEdgeVariance;
            evidence.TopEdgeVariance = features.TopEdgeVariance;
            evidence.BottomEdgeVariance = features.BottomEdgeVariance;
            
            // 5. 从段落特征收集证据
            evidence.ParagraphCount = features.ParagraphCount;
            evidence.IsSequentialParagraphs = features.IsSequentialParagraphs;
            
            // 6. 从方向证据直接收集
            evidence.LeftToRightCount += features.LeftToRightEvidence;
            evidence.RightToLeftCount += features.RightToLeftEvidence;
            evidence.TopToBottomCount += features.TopToBottomEvidence;
            evidence.BottomToTopCount += features.BottomToTopEvidence;
            
            // 7. 从对齐比例特征收集证据
            evidence.LeftAlignmentRatio = features.LeftAlignmentRatio;
            evidence.TopAlignmentRatio = features.TopAlignmentRatio;
            
            // 计算基准单位 - 基于文本块总数的动态基准
            // 文本块越多，每个证据的权重越小，避免大文档中证据过度累积
            int baseUnitWeight = Math.Max(1, Math.Min(5, features.ValidTextBlocks / 10));
            
            // 7. 从边缘变异分析方向 - 使用动态阈值
            // 计算边缘整齐度强度 (变异比率越极端，强度越高)
            double leftRightVarianceStrength = 0;
            double topBottomVarianceStrength = 0;
            
            // 左右边缘变异比率阈值 - 动态计算
            double lrLowThreshold = Math.Max(0.7, 0.8 - features.ValidTextBlocks * 0.005); // 文本块越多，阈值越低
            double lrHighThreshold = Math.Min(1.3, 1.25 + features.ValidTextBlocks * 0.005); // 文本块越多，阈值越高
            
            // 左边界比右边界更整齐（变异小）表示从左到右阅读
            if (features.LeftRightEdgeVarianceRatio < lrLowThreshold)
            {
                leftRightVarianceStrength = Math.Min(1.0, (lrLowThreshold - features.LeftRightEdgeVarianceRatio) / 0.3);
                evidence.LeftToRightCount += (int)(baseUnitWeight * 3 * leftRightVarianceStrength);
            }
            // 右边界比左边界更整齐表示从右到左阅读
            else if (features.LeftRightEdgeVarianceRatio > lrHighThreshold)
            {
                leftRightVarianceStrength = Math.Min(1.0, (features.LeftRightEdgeVarianceRatio - lrHighThreshold) / 0.75);
                evidence.RightToLeftCount += (int)(baseUnitWeight * 3 * leftRightVarianceStrength);
            }
            
            // 上下边缘变异比率阈值 - 动态计算
            double tbLowThreshold = Math.Max(0.7, 0.8 - features.ValidTextBlocks * 0.005);
            double tbHighThreshold = Math.Min(1.3, 1.25 + features.ValidTextBlocks * 0.005);
            
            // 上边界比下边界更整齐表示从上到下阅读
            if (features.TopBottomEdgeVarianceRatio < tbLowThreshold)
            {
                topBottomVarianceStrength = Math.Min(1.0, (tbLowThreshold - features.TopBottomEdgeVarianceRatio) / 0.3);
                evidence.TopToBottomCount += (int)(baseUnitWeight * 3 * topBottomVarianceStrength);
            }
            // 下边界比上边界更整齐表示从下到上阅读
            else if (features.TopBottomEdgeVarianceRatio > tbHighThreshold)
            {
                topBottomVarianceStrength = Math.Min(1.0, (features.TopBottomEdgeVarianceRatio - tbHighThreshold) / 0.75);
                evidence.BottomToTopCount += (int)(baseUnitWeight * 3 * topBottomVarianceStrength);
            }
            
            // 8. 竖排文本的特殊处理 - 根据竖排文本比例增强方向证据
            if (features.VerticalTextCount > features.HorizontalTextCount * 1.5)
            {
                // 竖排文本比例明显高于横排文本
                double verticalRatio = Math.Min(1.0, 
                    (double)features.VerticalTextCount / Math.Max(1, features.HorizontalTextCount) / 2.0);
                
                // 强度限制在0.2-1.0之间
                verticalRatio = Math.Max(0.2, verticalRatio);
                
                // 增加从上到下的证据权重
                evidence.TopToBottomCount += (int)(baseUnitWeight * 5 * verticalRatio);
                
                // 【修改】增强从右到左的基础权重
                // 为竖排文本添加一个从右到左的基础权重
                evidence.RightToLeftCount += (int)(baseUnitWeight * 3); // 增加权重，从2增加到3
                
                // 对于竖排文本，如果有明确的右到左证据，增强这一方向
                if (features.RightToLeftEvidence > features.LeftToRightEvidence)
                {
                    double rtlStrength = Math.Min(1.0, 
                        (double)features.RightToLeftEvidence / Math.Max(1, features.LeftToRightEvidence));
                    
                    // 【修改】增强权重，从4增加到5
                    evidence.RightToLeftCount += (int)(baseUnitWeight * 5 * rtlStrength * verticalRatio);
                }
                
                // 【新增】检查文本的水平分布
                // 如果文本主要分布在页面右侧，增加从右到左的证据
                if (features.ContentDensityRightHalf > features.ContentDensityLeftHalf * 1.1)
                {
                    double densityRatio = features.ContentDensityRightHalf / features.ContentDensityLeftHalf;
                    int densityBonus = (int)(baseUnitWeight * 2 * Math.Min(1.0, densityRatio - 1.0));
                    evidence.RightToLeftCount += densityBonus;
                    System.Diagnostics.Debug.WriteLine($"右半页内容密度高于左半页(比例:{densityRatio:F2})，增加RightToLeft证据 +{densityBonus}");
                }
            }
            
            return evidence;
        }

        /// <summary>
        /// 对复杂文档应用高级置信度优化
        /// </summary>
        private void ApplyAdvancedConfidenceOptimization(TextDirectionResult result, DirectionEvidence evidence, List<TextCellInfo> cells)
        {
            // 1. 检查布局和主方向的一致性
            // 检查是否存在强烈的一致性证据
            bool hasStrongConsistency = false;

            // 横排文本与从左到右方向一致性
            if (!result.IsVerticalLayout && result.HorizontalDirection == TextFlowDirection.LeftToRight &&
                evidence.LeftToRightCount > evidence.RightToLeftCount * 2 &&
                evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
            {
                hasStrongConsistency = true;
            }

            // 竖排文本与从上到下方向一致性
            else if (result.IsVerticalLayout && result.VerticalDirection == TextFlowDirection.TopToBottom &&
                    evidence.TopToBottomCount > evidence.BottomToTopCount * 2 &&
                    evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
            {
                hasStrongConsistency = true;
            }

            // 如果有强烈一致性，适当提高置信度
            if (hasStrongConsistency)
            {
                // 同时提高布局和方向置信度
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 5);

                if (!result.IsVerticalLayout)
                {
                    result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 5);
                }
                else
                {
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + 5);
                }
            }

            // 2. 一致性特征分析
            // 计算各种证据的互相支持程度
            int consistencyScore = 0;

            // 文本形状与对齐特征一致性
            if ((evidence.HorizontalTextCount > evidence.VerticalTextCount &&
                 evidence.LeftAlignedCount > evidence.TopAlignedCount) ||
                (evidence.VerticalTextCount > evidence.HorizontalTextCount &&
                 evidence.TopAlignedCount > evidence.LeftAlignedCount))
            {
                consistencyScore += 2;
            }

            // 边界变异与方向一致性
            if ((evidence.LeftEdgeVariance < evidence.RightEdgeVariance &&
                 result.HorizontalDirection == TextFlowDirection.LeftToRight) ||
                (evidence.RightEdgeVariance < evidence.LeftEdgeVariance &&
                 result.HorizontalDirection == TextFlowDirection.RightToLeft) ||
                (evidence.TopEdgeVariance < evidence.BottomEdgeVariance &&
                 result.VerticalDirection == TextFlowDirection.TopToBottom) ||
                (evidence.BottomEdgeVariance < evidence.TopEdgeVariance &&
                 result.VerticalDirection == TextFlowDirection.BottomToTop))
            {
                consistencyScore += 2;
            }

            // 段落布局与布局类型一致性
            if ((evidence.ParagraphCount >= 3 && !result.IsVerticalLayout) ||
                (evidence.IsSequentialParagraphs && evidence.LeftAlignmentRatio > 0.7 && !result.IsVerticalLayout))
            {
                consistencyScore += 2;
            }

            // 应用一致性评分
            if (consistencyScore >= 4)
            {
                // 高一致性得分提高整体置信度
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyScore / 2);
                result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + consistencyScore / 2);
                result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + consistencyScore / 2);
            }
        }

        /// <summary>
        /// 确保结果具有合理的置信度，避免不确定的输出
        /// </summary>
        private void EnsureMinimumConfidence(TextDirectionResult result)
        {
            // 基于相对性的置信度调整

            // 1. 默认方向置信度阈值 - 使用相对阈值而非绝对值
            int baseThreshold = 50; // 最基础的置信度阈值

            // 2. 根据文本复杂度动态调整阈值
            // 如果两个方向的置信度差异很小，说明文本布局复杂或模糊，应该降低阈值
            if (Math.Abs(result.HorizontalConfidence - result.VerticalConfidence) < 15)
            {
                baseThreshold = 45; // 降低阈值
            }

            // 3. 水平方向的置信度调整
            if (result.HorizontalConfidence < baseThreshold)
            {
                // 根据垂直方向的置信度动态决定水平方向的默认置信度
                // 如果垂直方向的置信度很高，我们对水平方向的默认值更有信心
                int defaultHorizontalConfidence =
                    result.VerticalConfidence > 75 ? 65 : 60;

                result.HorizontalDirection = TextFlowDirection.LeftToRight; // 最常见的默认方向
                result.HorizontalConfidence = defaultHorizontalConfidence;
            }

            // 4. 垂直方向的置信度调整
            if (result.VerticalConfidence < baseThreshold)
            {
                // 根据水平方向的置信度动态决定垂直方向的默认置信度
                int defaultVerticalConfidence =
                    result.HorizontalConfidence > 75 ? 70 : 65;

                result.VerticalDirection = TextFlowDirection.TopToBottom; // 最常见的默认方向
                result.VerticalConfidence = defaultVerticalConfidence;
            }

            // 5. 布局类型置信度调整 - 根据主方向置信度动态决定
            if (result.LayoutConfidence < baseThreshold)
            {
                // 根据当前布局类型选择参考置信度
                int referenceConfidence = result.IsVerticalLayout ?
                    result.VerticalConfidence : result.HorizontalConfidence;

                // 如果参考置信度高，保持当前布局类型但提高置信度
                if (referenceConfidence > 70)
                {
                    result.LayoutConfidence = Math.Max(referenceConfidence - 10, 60);
                }
                // 否则采用更保守的默认值：横排布局（更常见）
                else
                {
                    result.IsVerticalLayout = false;
                    result.LayoutConfidence = 60;
                }
            }
        }

        /// <summary>
        /// 确定文档的布局类型（横排或竖排）
        /// </summary>
        /// <remarks>
        /// 这个方法是布局类型判断的核心算法，使用多维度特征的累加得分系统来判断文档是横排还是竖排布局。
        /// 该方法考虑多种布局证据，包括文本形状、行列结构、对齐特征等，适用于各种语言的文档。
        /// </remarks>
        /// <param name="result">输出的文本方向结果，将设置布局类型</param>
        /// <param name="evidence">收集的方向证据</param>
        private void DetermineLayoutType(TextDirectionResult result, DirectionEvidence evidence)
        {
            // 使用得分累加系统确定布局类型
            // 通过多维度特征分析，使系统适用于各种不同的文档布局
            int horizontalLayoutScore = 0; // 横排布局得分
            int verticalLayoutScore = 0;   // 竖排布局得分

            // 开始详细的布局分析记录
            System.Diagnostics.Debug.WriteLine("\n============ 布局类型分析开始 ============");
            
            // ====================== 直方图特征分析 ======================
            // 首先分析直方图特征，如果直方图特征非常明显，可以直接决定布局类型
            int histogramLayoutScore = AnalyzeHistogramLayoutEvidence(evidence);
            
            // 如果直方图特征非常明显，给予极高权重
            if (Math.Abs(histogramLayoutScore) > 15) // 高阈值
            {
                // 直接根据直方图结果确定布局类型
                result.IsVerticalLayout = histogramLayoutScore > 0;
                result.LayoutConfidence = Math.Min(95, 60 + Math.Abs(histogramLayoutScore));
                
                // 记录决策依据
                System.Diagnostics.Debug.WriteLine($"基于强直方图特征确定布局: {(result.IsVerticalLayout ? "竖排" : "横排")}，置信度: {result.LayoutConfidence}");
                return;
            }
            else if (histogramLayoutScore != 0)
            {
                // 如果直方图特征有一定倾向但不是非常明显，将其纳入总体评分
                if (histogramLayoutScore > 0)
                {
                    verticalLayoutScore += histogramLayoutScore;
                    System.Diagnostics.Debug.WriteLine($"直方图特征倾向于竖排布局，竖排得分 +{histogramLayoutScore}");
                }
                else
                {
                    horizontalLayoutScore += -histogramLayoutScore;
                    System.Diagnostics.Debug.WriteLine($"直方图特征倾向于横排布局，横排得分 +{-histogramLayoutScore}");
                }
            }

            // ====================== 布局类型特征分析 ======================

            // 1. 文本块形状特征 - 最直观和通用的特征
            // 对任何语言都适用，无需特定语言知识
            // 1.1 高宽比特征 - 根据文本块的形状判断其可能的排版方向

            // 计算总的形状特征得分
            int totalShapeScore = 0;

            // 记录形状得分情况
            System.Diagnostics.Debug.WriteLine("\n--- 1. 形状特征得分 ---");
            System.Diagnostics.Debug.WriteLine($"竖向文本数: {evidence.VerticalTextCount}, 横向文本数: {evidence.HorizontalTextCount}");

            // 检查是否存在极端竖排文本特征
            bool hasExtremeVerticalFeature = false;
            bool hasExtremeHorizontalFeature = false;

            // 检测极端比例文本块占比
            double totalTextCount = evidence.VerticalTextCount + evidence.HorizontalTextCount;
            double verticalRatio = totalTextCount > 0 ? evidence.VerticalTextCount / totalTextCount : 0;
            double horizontalRatio = totalTextCount > 0 ? evidence.HorizontalTextCount / totalTextCount : 0;

            System.Diagnostics.Debug.WriteLine($"竖向文本占比: {verticalRatio:P2}, 横向文本占比: {horizontalRatio:P2}");
            
            // 检查是否有足够的文本来判断
            if (totalTextCount < 3)
            {
                System.Diagnostics.Debug.WriteLine("文本数量不足，难以可靠判断形状特征");
                // 使用其他证据继续分析
            }
            else
            {
                // 1. 分析显著优势情况 - 一种形状完全占优
                if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
                {
                    // 竖向文本明显占优 - 强烈暗示竖排布局
                    int score = 8;  // 强力证据

                    // 增强对极端竖排情况的检测
                    if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 2.5)
                    {
                        // 竖向文本极度占优 - 之前是3倍，现在降低到2.5倍
                        score += 6; // 增加得分
                        hasExtremeVerticalFeature = true;
                        System.Diagnostics.Debug.WriteLine($"竖向文本远远多于横向(2.5倍以上) => 竖排得分 +{score}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"竖向文本远多于横向(1.5倍以上) => 竖排得分 +{score}");
                    }

                    verticalLayoutScore += score;
                    totalShapeScore += score;
                }
                else if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.25)
                {
                    // 竖向文本明显较多 - 从1.4倍降低到1.25倍
                    int score = 6;  // 中等证据
                    verticalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"竖向文本明显多于横向(1.25倍以上) => 竖排得分 +{score}");
                }
                else if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.1)
                {
                    // 竖向文本略多 - 从1.2倍降低到1.1倍
                    int score = 4;  // 中弱证据
                    verticalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"竖向文本略多于横向(1.1倍以上) => 竖排得分 +{score}");
                }
                else if (evidence.VerticalTextCount > evidence.HorizontalTextCount)
                {
                    // 竖向文本稍多 - 弱竖排证据，但仍有意义
                    int score = 3;  // 弱证据，增加得分
                    verticalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"竖向文本稍多于横向 => 竖排得分 +{score}");
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
                {
                    // 横向文本明显占优 - 强烈暗示横排布局
                    int score = 8;  // 强力证据

                    // 增强对极端横排情况的检测
                    if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 3)
                    {
                        // 横向文本极度占优
                        score += 4;
                        hasExtremeHorizontalFeature = true;
                        System.Diagnostics.Debug.WriteLine($"横向文本远远多于竖向(3倍以上) => 横排得分 +{score}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"横向文本远多于竖向(1.5倍以上) => 横排得分 +{score}");
                    }

                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.4)
                {
                    // 横向文本明显较多 - 中等强度的横排证据
                    int score = 6;  // 中等证据
                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"横向文本明显多于竖向(1.4倍以上) => 横排得分 +{score}");
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.2)
                {
                    // 横向文本略多 - 中弱强度的横排证据
                    int score = 4;  // 中弱证据
                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"横向文本略多于竖向(1.2倍以上) => 横排得分 +{score}");
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount)
                {
                    // 横向文本稍多 - 弱横排证据，但仍有意义
                    int score = 2;  // 弱证据
                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"横向文本稍多于竖向 => 横排得分 +{score}");
                }
                // 2. 当文本数量接近相等时，引入额外判断因素
                else if (Math.Abs(evidence.VerticalTextCount - evidence.HorizontalTextCount) <= 2)
                {
                    // 当形状特征没有明显优势时，考虑其他证据
                    System.Diagnostics.Debug.WriteLine($"横向与竖向文本数量接近，形状特征不明显");

                    // 检查列与行的数量，通常能更好地反映整体布局
                    if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows)
                    {
                        int score = 4; // 增加得分
                        verticalLayoutScore += score;
                        totalShapeScore += score;
                        System.Diagnostics.Debug.WriteLine($"垂直列数({evidence.VerticalAlignedColumns})大于水平行数({evidence.HorizontalAlignedRows}) => 竖排得分 +{score}");
                    }
                    else if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns)
                    {
                        int score = 3;
                        horizontalLayoutScore += score;
                        totalShapeScore += score;
                        System.Diagnostics.Debug.WriteLine($"水平行数({evidence.HorizontalAlignedRows})大于垂直列数({evidence.VerticalAlignedColumns}) => 横排得分 +{score}");
                    }
                }
            }

            // 检查极端形状特征的占比影响
            if (verticalRatio > 0.7 && evidence.VerticalTextCount >= 3)
            {
                // 如果竖向文本占比超过70%，且至少有3个，这是很强的竖排证据
                // 原来是80%，现在降低到70%
                int bonusScore = 8; // 增加得分
                verticalLayoutScore += bonusScore;
                totalShapeScore += bonusScore;
                System.Diagnostics.Debug.WriteLine($"竖向文本占比超过70%，强烈的竖排证据 => 竖排得分 +{bonusScore}");
            }
            else if (horizontalRatio > 0.8 && evidence.HorizontalTextCount >= 3)
            {
                // 如果横向文本占比超过80%，且至少有3个，这是很强的横排证据
                int bonusScore = 6;
                horizontalLayoutScore += bonusScore;
                totalShapeScore += bonusScore;
                System.Diagnostics.Debug.WriteLine($"横向文本占比超过80%，强烈的横排证据 => 横排得分 +{bonusScore}");
            }

            // 形状特征总结
            if (totalShapeScore > 0)
            {
                double verticalRatioScore = verticalLayoutScore / (double)totalShapeScore;
                System.Diagnostics.Debug.WriteLine($"形状特征总结: 总分={totalShapeScore}, 竖排得分比例={verticalRatioScore:P2}");

                if (verticalRatioScore > 0.6)
                {
                    System.Diagnostics.Debug.WriteLine("形状特征总体倾向: 竖排");
                }
                else if (verticalRatioScore < 0.4)
                {
                    System.Diagnostics.Debug.WriteLine("形状特征总体倾向: 横排");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("形状特征总体倾向: 不确定");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("形状特征分析: 无明显倾向");
            }

            // 其余代码保持不变...
            
            // 2. 行列结构特征 - 考察文本块的整体排列方式
            System.Diagnostics.Debug.WriteLine("\n--- 2. 行列结构特征得分 ---");
            
            int rowColumnScore = 0;
            
            // 2.1 行列数量比较
            if (evidence.HorizontalAlignedRows > 0 || evidence.VerticalAlignedColumns > 0)
            {
                System.Diagnostics.Debug.WriteLine($"检测到水平行数: {evidence.HorizontalAlignedRows}, 垂直列数: {evidence.VerticalAlignedColumns}");
                
                // 行明显多于列 - 横排特征
                if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns * 1.5 && evidence.HorizontalAlignedRows >= 3)
                {
                    int score = Math.Min(10, evidence.HorizontalAlignedRows);
                    horizontalLayoutScore += score;
                    rowColumnScore += score;
                    System.Diagnostics.Debug.WriteLine($"水平行数明显多于垂直列数 => 横排得分 +{score}");
                }
                // 列明显多于行 - 竖排特征
                else if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows * 1.5 && evidence.VerticalAlignedColumns >= 2)
                {
                    int score = Math.Min(12, evidence.VerticalAlignedColumns * 2);
                    verticalLayoutScore += score;
                    rowColumnScore += score;
                    System.Diagnostics.Debug.WriteLine($"垂直列数明显多于水平行数 => 竖排得分 +{score}");
                }
                // 行列数量接近 - 需要进一步分析
                else if (evidence.HorizontalAlignedRows >= 2 && evidence.VerticalAlignedColumns >= 2)
                {
                    // 检查行列规律性
                    if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns)
                    {
                        int score = 4;
                        horizontalLayoutScore += score;
                        rowColumnScore += score;
                        System.Diagnostics.Debug.WriteLine($"行列数量接近但行数更多 => 横排得分 +{score}");
                    }
                    else
                    {
                        int score = 4;
                        verticalLayoutScore += score;
                        rowColumnScore += score;
                        System.Diagnostics.Debug.WriteLine($"行列数量接近但列数更多 => 竖排得分 +{score}");
                    }
                }
            }
            
            // 2.2 对齐特征
            int alignmentScore = 0;
            
            // 左对齐比右对齐明显 - 横排从左到右特征
            if (evidence.LeftAlignedCount > evidence.RightAlignedCount * 1.5 && evidence.LeftAlignedCount >= 3)
            {
                int score = Math.Min(8, evidence.LeftAlignedCount);
                horizontalLayoutScore += score;
                alignmentScore += score;
                System.Diagnostics.Debug.WriteLine($"左对齐数量明显多于右对齐 => 横排得分 +{score}");
            }
            // 顶部对齐比底部对齐明显 - 竖排从上到下特征
            if (evidence.TopAlignedCount > evidence.BottomAlignedCount * 1.5 && evidence.TopAlignedCount >= 3)
            {
                int score = Math.Min(8, evidence.TopAlignedCount);
                verticalLayoutScore += score;
                alignmentScore += score;
                System.Diagnostics.Debug.WriteLine($"顶部对齐数量明显多于底部对齐 => 竖排得分 +{score}");
            }
            
            // 3. 边缘变异特征 - 分析边缘的整齐程度
            System.Diagnostics.Debug.WriteLine("\n--- 3. 边缘变异特征得分 ---");
            
            int edgeScore = 0;
            
            // 左右边缘变异比较
            if (evidence.LeftEdgeVariance > 0 && evidence.RightEdgeVariance > 0)
            {
                double leftRightRatio = evidence.LeftEdgeVariance / evidence.RightEdgeVariance;
                System.Diagnostics.Debug.WriteLine($"左边缘变异: {evidence.LeftEdgeVariance:F2}, 右边缘变异: {evidence.RightEdgeVariance:F2}, 比率: {leftRightRatio:F2}");
                
                // 左边缘比右边缘更整齐 - 横排从左到右特征
                if (leftRightRatio < 0.7)
                {
                    int score = 5;
                    horizontalLayoutScore += score;
                    edgeScore += score;
                    System.Diagnostics.Debug.WriteLine($"左边缘明显比右边缘更整齐 => 横排得分 +{score}");
                }
                // 右边缘比左边缘更整齐 - 可能是横排从右到左
                else if (leftRightRatio > 1.5)
                {
                    int score = 3;
                    // 这里不增加竖排得分，因为右边缘整齐也可能是横排从右到左
                    edgeScore += score;
                    System.Diagnostics.Debug.WriteLine($"右边缘明显比左边缘更整齐 => 可能是从右到左横排");
                }
            }
            
            // 上下边缘变异比较
            if (evidence.TopEdgeVariance > 0 && evidence.BottomEdgeVariance > 0)
            {
                double topBottomRatio = evidence.TopEdgeVariance / evidence.BottomEdgeVariance;
                System.Diagnostics.Debug.WriteLine($"上边缘变异: {evidence.TopEdgeVariance:F2}, 下边缘变异: {evidence.BottomEdgeVariance:F2}, 比率: {topBottomRatio:F2}");
                
                // 上边缘比下边缘更整齐 - 竖排从上到下特征
                if (topBottomRatio < 0.7)
                {
                    int score = 5;
                    verticalLayoutScore += score;
                    edgeScore += score;
                    System.Diagnostics.Debug.WriteLine($"上边缘明显比下边缘更整齐 => 竖排得分 +{score}");
                }
            }
            
            // 4. 段落特征
            if (evidence.ParagraphCount >= 2)
            {
                System.Diagnostics.Debug.WriteLine($"\n--- 4. 段落特征得分 --- (段落数: {evidence.ParagraphCount})");
                
                if (evidence.IsSequentialParagraphs)
                {
                    // 连续段落通常是横排布局
                    int score = Math.Min(8, evidence.ParagraphCount * 2);
                    horizontalLayoutScore += score;
                    System.Diagnostics.Debug.WriteLine($"检测到连续段落布局 => 横排得分 +{score}");
                }
            }
            
            // 5. 最终布局决策
            System.Diagnostics.Debug.WriteLine("\n--- 5. 布局决策 ---");
            System.Diagnostics.Debug.WriteLine($"横排总得分: {horizontalLayoutScore}, 竖排总得分: {verticalLayoutScore}");
            
            // 确定布局类型
            if (verticalLayoutScore > horizontalLayoutScore)
            {
                result.IsVerticalLayout = true;
                
                // 计算置信度 - 基于得分差异和总得分
                int totalScore = verticalLayoutScore + horizontalLayoutScore;
                int scoreDiff = verticalLayoutScore - horizontalLayoutScore;
                
                result.LayoutConfidence = CalculateLayoutConfidence(verticalLayoutScore, horizontalLayoutScore, totalScore);
                
                // 【修改】根据左右方向证据初步设置水平方向
                // 这里只进行初步设置，后续在DetermineVerticalFlowDirection中会基于更多特征进行详细计算
                if (evidence.RightToLeftCount > evidence.LeftToRightCount)
                {
                    result.IsRightToLeft = true;
                    result.HorizontalDirection = TextFlowDirection.RightToLeft;
                    // 置信度将在DetermineVerticalFlowDirection中详细计算
                }
                else if (evidence.LeftToRightCount > evidence.RightToLeftCount)
                {
                    result.IsRightToLeft = false;
                    result.HorizontalDirection = TextFlowDirection.LeftToRight;
                    // 置信度将在DetermineVerticalFlowDirection中详细计算
                }
                else
                {
                    // 证据相等，使用内容分布特征
                    if (evidence.ContentDensityRightHalf > evidence.ContentDensityLeftHalf)
                    {
                        result.IsRightToLeft = true;
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                    }
                    else
                    {
                        result.IsRightToLeft = false;
                        result.HorizontalDirection = TextFlowDirection.LeftToRight;
                    }
                    // 置信度将在DetermineVerticalFlowDirection中详细计算
                }
                
                System.Diagnostics.Debug.WriteLine($"布局决策: 竖排 (置信度: {result.LayoutConfidence}%)，默认水平方向: 从右到左");
            }
            else
            {
                result.IsVerticalLayout = false;
                
                // 计算置信度
                int totalScore = verticalLayoutScore + horizontalLayoutScore;
                int scoreDiff = horizontalLayoutScore - verticalLayoutScore;
                
                result.LayoutConfidence = CalculateLayoutConfidence(horizontalLayoutScore, verticalLayoutScore, totalScore);
                
                System.Diagnostics.Debug.WriteLine($"布局决策: 横排 (置信度: {result.LayoutConfidence}%)");
            }
            
            // 应用特殊布局规则 - 处理极端情况
            ApplySpecialLayoutTypeRules(evidence, result);
            
            System.Diagnostics.Debug.WriteLine("============ 布局类型分析结束 ============\n");
        }
        
        /// <summary>
        /// 分析直方图布局证据
        /// </summary>
        /// <param name="evidence">方向证据</param>
        /// <returns>布局得分，正值表示竖排倾向，负值表示横排倾向，绝对值表示置信度</returns>
        private int AnalyzeHistogramLayoutEvidence(DirectionEvidence evidence)
        {
            // 如果直方图特征未设置，返回0
            if (evidence.HistogramColumnScore == 0 && evidence.HistogramRowScore == 0)
            {
                return 0;
            }
            
            System.Diagnostics.Debug.WriteLine("\n--- 直方图特征分析 ---");
            System.Diagnostics.Debug.WriteLine($"列结构得分: {evidence.HistogramColumnScore}, 行结构得分: {evidence.HistogramRowScore}");
            
            // 计算直方图布局得分
            int histogramLayoutScore = 0;
            
            // 【修改】考虑结构强度和方向一致性
            bool hasStrongVerticalStructure = evidence.VerticalStructureStrength > 0.8;
            bool hasStrongHorizontalStructure = evidence.HorizontalStructureStrength > 0.8;
            bool hasHighDirectionalConsistency = evidence.DirectionalConsistency > 0.8;
            
            // 列结构明显强于行结构 - 竖排布局
            if (evidence.HistogramColumnScore > evidence.HistogramRowScore * 2 && evidence.HistogramColumnScore >= 10)
            {
                // 【修改】增强得分上限
                int baseScore = Math.Min(30, evidence.HistogramColumnScore);
                
                // 【新增】如果结构强度高且方向一致性高，进一步提升得分
                if (hasStrongVerticalStructure && hasHighDirectionalConsistency)
                {
                    baseScore = Math.Min(40, baseScore + 10);
                    System.Diagnostics.Debug.WriteLine($"列结构明显强于行结构，且结构强度和方向一致性高，直方图特征强烈暗示竖排布局，得分: +{baseScore}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"列结构明显强于行结构，直方图特征强烈暗示竖排布局，得分: +{baseScore}");
                }
                
                histogramLayoutScore = baseScore;
            }
            // 行结构明显强于列结构 - 横排布局
            else if (evidence.HistogramRowScore > evidence.HistogramColumnScore * 2 && evidence.HistogramRowScore >= 10)
            {
                // 【修改】增强得分上限
                int baseScore = Math.Min(30, evidence.HistogramRowScore);
                
                // 【新增】如果结构强度高且方向一致性高，进一步提升得分
                if (hasStrongHorizontalStructure && hasHighDirectionalConsistency)
                {
                    baseScore = Math.Min(40, baseScore + 10);
                    System.Diagnostics.Debug.WriteLine($"行结构明显强于列结构，且结构强度和方向一致性高，直方图特征强烈暗示横排布局，得分: -{baseScore}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"行结构明显强于列结构，直方图特征强烈暗示横排布局，得分: -{baseScore}");
                }
                
                histogramLayoutScore = -baseScore;
            }
            // 列结构略强于行结构
            else if (evidence.HistogramColumnScore > evidence.HistogramRowScore * 1.3)
            {
                // 【修改】增强得分计算
                int baseScore = Math.Min(20, evidence.HistogramColumnScore / 2);
                
                // 【新增】如果结构强度高，增加得分
                if (hasStrongVerticalStructure)
                {
                    baseScore = Math.Min(25, baseScore + 5);
                    System.Diagnostics.Debug.WriteLine($"列结构略强于行结构，且垂直结构强度高，直方图特征倾向于竖排布局，得分: +{baseScore}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"列结构略强于行结构，直方图特征倾向于竖排布局，得分: +{baseScore}");
                }
                
                histogramLayoutScore = baseScore;
            }
            // 行结构略强于列结构
            else if (evidence.HistogramRowScore > evidence.HistogramColumnScore * 1.3)
            {
                // 【修改】增强得分计算
                int baseScore = Math.Min(20, evidence.HistogramRowScore / 2);
                
                // 【新增】如果结构强度高，增加得分
                if (hasStrongHorizontalStructure)
                {
                    baseScore = Math.Min(25, baseScore + 5);
                    System.Diagnostics.Debug.WriteLine($"行结构略强于列结构，且水平结构强度高，直方图特征倾向于横排布局，得分: -{baseScore}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"行结构略强于列结构，直方图特征倾向于横排布局，得分: -{baseScore}");
                }
                
                histogramLayoutScore = -baseScore;
            }
            // 【修改】行列结构接近，但有一个方向的结构强度明显高
            else if (hasStrongVerticalStructure && evidence.VerticalStructureStrength > evidence.HorizontalStructureStrength * 1.5)
            {
                histogramLayoutScore = 15;
                System.Diagnostics.Debug.WriteLine($"行列结构接近，但垂直结构强度明显更高(强度={evidence.VerticalStructureStrength:F2})，倾向于竖排布局，得分: +15");
            }
            else if (hasStrongHorizontalStructure && evidence.HorizontalStructureStrength > evidence.VerticalStructureStrength * 1.5)
            {
                histogramLayoutScore = -15;
                System.Diagnostics.Debug.WriteLine($"行列结构接近，但水平结构强度明显更高(强度={evidence.HorizontalStructureStrength:F2})，倾向于横排布局，得分: -15");
            }
            // 行列结构接近，无法确定
            else
            {
                System.Diagnostics.Debug.WriteLine("行列结构接近，直方图特征无法确定布局类型");
            }
            
            return histogramLayoutScore;
        }

        /// <summary>
        /// 基于一致性调整方向置信度，使用简化的基于证据的启发式规则
        /// </summary>
        /// <param name="result">方向检测结果</param>
        /// <param name="evidence">方向证据</param>
        private void AdjustConfidenceBasedOnConsistency(TextDirectionResult result, DirectionEvidence evidence)
        {
            System.Diagnostics.Debug.WriteLine("\n=== 一致性调整开始 ===");
            System.Diagnostics.Debug.WriteLine($"调整前 - 布局: {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
            System.Diagnostics.Debug.WriteLine($"水平方向: {result.HorizontalDirection}, 置信度: {result.HorizontalConfidence}");
            System.Diagnostics.Debug.WriteLine($"垂直方向: {result.VerticalDirection}, 置信度: {result.VerticalConfidence}");
            
            // 【新增】先评估直方图特征的可靠性
            double histogramReliability = 1.0;
            if (evidence.VerticalPeakCount >= 2 || evidence.HorizontalPeakCount >= 2)
            {
                // 计算峰值形状质量和峰谷比的组合得分
                double verticalQualityScore = evidence.VerticalPeakShapeQuality * evidence.VerticalPeakValleyMeanRatio;
                double horizontalQualityScore = evidence.HorizontalPeakShapeQuality * evidence.HorizontalPeakValleyMeanRatio;
                
                // 根据结构强度确定可靠性
                if (result.IsVerticalLayout)
                {
                    // 竖排布局，关注垂直结构特征
                    histogramReliability = Math.Min(1.25, 0.75 + verticalQualityScore / 2);
                    System.Diagnostics.Debug.WriteLine($"直方图可靠性评估 - 竖排布局，峰形质量与峰谷比组合评分:{verticalQualityScore:F2}，可靠性系数:{histogramReliability:F2}");
                }
                else
                {
                    // 横排布局，关注水平结构特征
                    histogramReliability = Math.Min(1.25, 0.75 + horizontalQualityScore / 2);
                    System.Diagnostics.Debug.WriteLine($"直方图可靠性评估 - 横排布局，峰形质量与峰谷比组合评分:{horizontalQualityScore:F2}，可靠性系数:{histogramReliability:F2}");
                }
            }
            
            // 【新增】评估段落结构特征的可靠性
            double paragraphReliability = 1.0;
            if (evidence.ParagraphCount > 1)
            {
                if (evidence.IsSequentialParagraphs)
                {
                    // 连续段落布局更可靠
                    paragraphReliability = 1.15;
                    System.Diagnostics.Debug.WriteLine($"段落特征评估 - 检测到{evidence.ParagraphCount}个连续段落，可靠性系数:{paragraphReliability:F2}");
                }
                else
                {
                    // 非连续段落布局
                    paragraphReliability = 1.05;
                    System.Diagnostics.Debug.WriteLine($"段落特征评估 - 检测到{evidence.ParagraphCount}个非连续段落，可靠性系数:{paragraphReliability:F2}");
                }
            }
            
            // 检查布局类型和方向是否一致
            bool isConsistent = false;

            // 增强一致性检查，使用基本启发式规则
            if (!result.IsVerticalLayout)
            {
                // 横排布局一致性检查
                if (result.HorizontalDirection == TextFlowDirection.LeftToRight ||
                    result.HorizontalDirection == TextFlowDirection.RightToLeft)
                {
                    isConsistent = true;

                    // 计算一致性得分 - 基于证据强度
                    int consistencyScore = 0;

                    // 形状特征支持
                    if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
                        consistencyScore += 2;

                    // 行列结构支持
                    if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns)
                        consistencyScore += 2;
                    
                    // 【新增】直方图特征支持
                    if (evidence.HorizontalPeakCount >= 2 && evidence.HorizontalStructureStrength > 0.6)
                        consistencyScore += 2;
                    
                    // 【新增】段落特征支持
                    if (evidence.ParagraphCount >= 2 && evidence.IsSequentialParagraphs)
                        consistencyScore += 1;

                    // 边界变异支持
                    if ((result.HorizontalDirection == TextFlowDirection.LeftToRight &&
                         evidence.LeftEdgeVariance < evidence.RightEdgeVariance) ||
                        (result.HorizontalDirection == TextFlowDirection.RightToLeft &&
                         evidence.RightEdgeVariance < evidence.LeftEdgeVariance))
                    {
                        consistencyScore += 1;
                    }

                    // 应用一致性增强，考虑直方图和段落特征可靠性
                    int consistencyBonus = (int)(Math.Min(15, consistencyScore * 3) * histogramReliability * paragraphReliability);
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyBonus);
                    result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + consistencyBonus / 2);

                    System.Diagnostics.Debug.WriteLine($"横排布局与水平方向一致性检查: 得分={consistencyScore}, 增加置信度 +{consistencyBonus}");
                }
            }
            else // 竖排布局
            {
                // 竖排布局一致性检查 - 与横排类似但针对垂直方向
                if (result.VerticalDirection == TextFlowDirection.TopToBottom ||
                    result.VerticalDirection == TextFlowDirection.BottomToTop)
                {
                    isConsistent = true;

                    // 计算一致性得分
                    int consistencyScore = 0;

                    // 形状特征支持
                    if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.2)
                        consistencyScore += 3;

                    // 行列结构支持
                    if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows)
                        consistencyScore += 2;
                    else if (evidence.VerticalAlignedColumns >= 2)
                        consistencyScore += 3;
                    
                    // 【新增】直方图特征支持
                    if (evidence.VerticalPeakCount >= 2)
                    {
                        // 峰值形状质量和峰谷比评分
                        int structureQualityScore = (int)((evidence.VerticalPeakShapeQuality + 
                                                         (evidence.VerticalPeakValleyMeanRatio / 5)) * 5);
                        consistencyScore += Math.Min(3, structureQualityScore);
                        
                        System.Diagnostics.Debug.WriteLine($"竖排布局 - 列结构质量评分:{structureQualityScore}，增加一致性得分:{Math.Min(3, structureQualityScore)}");
                    }
                    
                    // 【新增】水平方向一致性支持
                    if ((result.IsRightToLeft && evidence.RightToLeftCount > evidence.LeftToRightCount) ||
                        (!result.IsRightToLeft && evidence.LeftToRightCount > evidence.RightToLeftCount))
                    {
                        consistencyScore += 2;
                        System.Diagnostics.Debug.WriteLine("水平方向与垂直布局一致，增加一致性得分:+2");
                    }

                    // 边界变异支持
                    if ((result.VerticalDirection == TextFlowDirection.TopToBottom &&
                         evidence.TopEdgeVariance < evidence.BottomEdgeVariance) ||
                        (result.VerticalDirection == TextFlowDirection.BottomToTop &&
                         evidence.BottomEdgeVariance < evidence.TopEdgeVariance))
                    {
                        consistencyScore += 1;
                    }

                    // 应用一致性增强，考虑直方图和段落特征可靠性
                    int consistencyBonus = (int)(Math.Min(20, consistencyScore * 3) * histogramReliability * paragraphReliability);
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyBonus);
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + consistencyBonus / 2);

                    System.Diagnostics.Debug.WriteLine($"竖排布局与垂直方向一致性检查: 得分={consistencyScore}, 增加置信度 +{consistencyBonus}");
                }
            }

            // 处理不一致情况
            if (!isConsistent)
            {
                // 检查不一致程度
                int primaryConfidence = result.IsVerticalLayout ?
                    result.VerticalConfidence : result.HorizontalConfidence;

                // 如果主方向置信度高但布局置信度低，调整布局以匹配主方向
                if (primaryConfidence > result.LayoutConfidence + 25)
                {
                    // 重新评估布局方向
                    if (result.HorizontalConfidence > result.VerticalConfidence + 20)
                    {
                        // 水平方向明显更可信
                        result.IsVerticalLayout = false;
                        result.LayoutConfidence = (result.LayoutConfidence + result.HorizontalConfidence) / 2;
                        System.Diagnostics.Debug.WriteLine($"方向不一致修正: 水平方向置信度明显更高，调整为横排布局");
                    }
                    else if (result.VerticalConfidence > result.HorizontalConfidence + 20)
                    {
                        // 垂直方向明显更可信
                        result.IsVerticalLayout = true;
                        result.LayoutConfidence = (result.LayoutConfidence + result.VerticalConfidence) / 2;
                        System.Diagnostics.Debug.WriteLine($"方向不一致修正: 垂直方向置信度明显更高，调整为竖排布局");
                    }
                }
            }

            // 特殊情况处理：竖排布局中的垂直方向检查 - 基于统计特征
            if (result.IsVerticalLayout)
            {
                // 1. 基于方向证据评估垂直方向
                if (evidence.TopToBottomCount > evidence.BottomToTopCount * 1.5)
                {
                    // 有明显的从上到下的证据
                    if (result.VerticalDirection != TextFlowDirection.TopToBottom)
                    {
                        // 修正垂直方向
                        result.VerticalDirection = TextFlowDirection.TopToBottom;
                        result.VerticalConfidence = Math.Max(70, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"竖排布局中检测到强烈的从上到下证据，修正垂直方向为TopToBottom");
                    }
                }
                else if (evidence.BottomToTopCount > evidence.TopToBottomCount * 1.5)
                {
                    // 有明显的从下到上的证据
                    if (result.VerticalDirection != TextFlowDirection.BottomToTop)
                    {
                        // 修正垂直方向
                        result.VerticalDirection = TextFlowDirection.BottomToTop;
                        result.VerticalConfidence = Math.Max(70, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"竖排布局中检测到强烈的从下到上证据，修正垂直方向为BottomToTop");
                    }
                }
                else
                {
                    // 【修改】结合直方图峰值特征进行判断
                    if (evidence.VerticalPeakCount >= 2 && evidence.VerticalPeakShapeQuality > 0.7)
                    {
                        // 使用峰值特征判断
                        // 在多列竖排文本中，峰形质量高通常意味着更规整的结构
                        // 这种情况下我们默认使用从上到下方向（传统阅读习惯）
                        result.VerticalDirection = TextFlowDirection.TopToBottom;
                        result.VerticalConfidence = Math.Max(75, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"垂直方向证据不明确，但列结构清晰(峰形质量:{evidence.VerticalPeakShapeQuality:F2})，使用TopToBottom");
                    }
                    else if (evidence.VerticalAlignedColumns >= 2)
                    {
                        // 有多列，默认使用从上到下（通用性更好）
                        result.VerticalDirection = TextFlowDirection.TopToBottom;
                        result.VerticalConfidence = Math.Max(60, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"垂直方向证据不明确，但检测到多列，默认使用TopToBottom");
                    }
                    else
                    {
                        // 无明显证据，使用通用默认方向
                        result.VerticalDirection = TextFlowDirection.TopToBottom; 
                        result.VerticalConfidence = Math.Max(55, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"垂直方向证据不明确，使用默认TopToBottom方向");
                    }
                }
                
                // 3. 基于形状特征增强布局置信度
                if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
                {
                    // 竖排文本明显占优，增强竖排布局置信度
                    int verticalBonus = Math.Min(15, (evidence.VerticalTextCount - evidence.HorizontalTextCount) / 2);
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + verticalBonus);
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + verticalBonus / 2);
                    
                    System.Diagnostics.Debug.WriteLine($"竖排文本明显占优({evidence.VerticalTextCount}:{evidence.HorizontalTextCount})，增强竖排布局置信度 +{verticalBonus}");
                }
                
                // 【新增】4. 使用直方图结构增强水平方向（列排序方向）的置信度
                if (evidence.VerticalPeakCount >= 2)
                {
                    // 多列结构，评估列特征质量
                    double structureQuality = (evidence.VerticalPeakShapeQuality + evidence.VerticalPeakValleyMeanRatio / 3) / 2;
                    
                    // 如果列结构质量高且检测到中日韩文本，强化从右到左的判断
                    if (structureQuality > 0.7 && result.IsRightToLeft)
                    {
                        // 增强右到左的置信度
                        int rtlBonus = (int)(15 * structureQuality);
                        result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + rtlBonus);
                        System.Diagnostics.Debug.WriteLine($"竖排多列结构清晰(结构质量:{structureQuality:F2})，" + 
                                                          $"增强从右到左水平方向置信度 +{rtlBonus}，最终:{result.HorizontalConfidence}%");
                    }
                    // 如果水平方向是从左到右，但置信度不高，考虑转换到从右到左
                    else if (!result.IsRightToLeft && 
                             result.HorizontalConfidence < 75 && 
                             structureQuality > 0.8 && 
                             evidence.VerticalPeakCount >= 3)
                    {
                        // 结构非常清晰的多列布局，优先考虑从右到左
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 75;
                        System.Diagnostics.Debug.WriteLine($"列结构非常清晰且列数多(结构质量:{structureQuality:F2})," + 
                                                          $"转换水平方向为从右到左，置信度:75%");
                    }
                }
            }
            
            System.Diagnostics.Debug.WriteLine($"调整后 - 布局: {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
            System.Diagnostics.Debug.WriteLine($"水平方向: {result.HorizontalDirection}, 置信度: {result.HorizontalConfidence}");
            System.Diagnostics.Debug.WriteLine($"垂直方向: {result.VerticalDirection}, 置信度: {result.VerticalConfidence}");
            System.Diagnostics.Debug.WriteLine("=== 一致性调整结束 ===\n");
        }

        /// <summary>
        /// 计算布局置信度
        /// 基于得分差异比例和总分动态计算置信度，适用于任何得分分布
        /// </summary>
        /// <param name="winningScore">获胜方向的得分</param>
        /// <param name="opposingScore">相反方向的得分</param>
        /// <param name="totalScore">总得分</param>
        /// <returns>计算得出的置信度（55-95）</returns>
        private int CalculateLayoutConfidence(int winningScore, int opposingScore, int totalScore)
        {
            // 基础置信度基于分数差异比例
            // 使用相对差异比例而非绝对差异，使结果更通用
            double scoreDiff = winningScore - opposingScore;
            double diffRatio = scoreDiff / Math.Max(1, totalScore);

            // 映射到置信度范围：55-95
            // 下限保证最低可信度，上限避免过于武断
            int baseConfidence = 55 + (int)(diffRatio * 40);

            // 高总分提升置信度 - 更多证据支持时提升置信度
            // 使用平方根函数使增长合理
            int scoreBoost = (int)Math.Min(10, Math.Sqrt(totalScore));

            return Math.Min(95, baseConfidence + scoreBoost);
        }

        /// <summary>
        /// 应用特殊布局类型规则
        /// 处理各种特殊布局场景，增强系统的通用性
        /// </summary>
        /// <param name="evidence">收集的方向证据</param>
        /// <param name="result">文本方向结果，可能被调整</param>
        private void ApplySpecialLayoutTypeRules(DirectionEvidence evidence, TextDirectionResult result)
        {
            // 特殊布局规则用于处理一些极端或特殊情况
            // 这些规则基于具体的布局特征，适用范围广

            System.Diagnostics.Debug.WriteLine("\n--- 应用特殊布局规则 ---");

            // 1. 多列竖排文本布局
            // 垂直排列的文本列是非常明显的竖排特征
            if (evidence.VerticalAlignedColumns >= 2 &&
                evidence.VerticalTextCount > evidence.HorizontalTextCount &&
                evidence.HorizontalAlignedRows <= 1)
            {
                result.IsVerticalLayout = true;
                int oldConfidence = result.LayoutConfidence;
                // 提高置信度，但不超过95
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 10);

                System.Diagnostics.Debug.WriteLine($"特殊规则1: 检测到多列竖排布局 => 置信度从{oldConfidence}提升到{result.LayoutConfidence}");
            }

            // 2. 多行横排文本布局
            // 水平排列的文本行是非常明显的横排特征
            if (evidence.HorizontalAlignedRows >= 2 &&
                evidence.HorizontalTextCount > evidence.VerticalTextCount &&
                evidence.VerticalAlignedColumns <= 1)
            {
                result.IsVerticalLayout = false;
                int oldConfidence = result.LayoutConfidence;
                // 提高置信度，但不超过95
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 10);

                System.Diagnostics.Debug.WriteLine($"特殊规则2: 检测到多行横排布局 => 置信度从{oldConfidence}提升到{result.LayoutConfidence}");
            }

            // 5. 文本内容极少的情况
            if (evidence.HorizontalTextCount + evidence.VerticalTextCount < 5 &&
                result.LayoutConfidence > 60)
            {
                int oldConfidence = result.LayoutConfidence;
                // 文本内容很少时，降低我们的判断置信度
                result.LayoutConfidence = Math.Min(result.LayoutConfidence, 70);

                if (oldConfidence != result.LayoutConfidence)
                {
                    System.Diagnostics.Debug.WriteLine($"特殊规则5: 文本内容极少 => 降低置信度从{oldConfidence}到{result.LayoutConfidence}");
                }
            }
        }

        /// <summary>
        /// 统一的文本布局特征数据结构
        /// </summary>
        private class TextLayoutFeatures
        {
            // 文本块统计
            public int TotalTextBlocks { get; set; } = 0;
            public int ValidTextBlocks { get; set; } = 0;
            
            // 形状特征
            public List<double> HeightWidthRatios { get; set; } = new List<double>();
            public List<double> WidthHeightRatios { get; set; } = new List<double>();
            
            public double MedianHeightWidthRatio { get; set; } = 1.0;
            public double MedianWidthHeightRatio { get; set; } = 1.0;
            public double HeightWidthRatioVariance { get; set; } = 0;
            public double WidthHeightRatioVariance { get; set; } = 0;
            
            // 比例百分位数 - 用于自适应阈值
            public double VerticalRatio_P90 { get; set; } = 7.0; // 90百分位高宽比，替代固定阈值
            public double VerticalRatio_P75 { get; set; } = 5.0; // 75百分位高宽比
            public double VerticalRatio_P50 { get; set; } = 1.5; // 中位数高宽比
            
            public double HorizontalRatio_P90 { get; set; } = 7.0; // 90百分位宽高比
            public double HorizontalRatio_P75 { get; set; } = 5.0; // 75百分位宽高比
            public double HorizontalRatio_P50 { get; set; } = 1.5; // 中位数宽高比
            
            // 文本块形状统计
            public int VerticalTextCount { get; set; } = 0;   // 高>宽的文本块
            public int HorizontalTextCount { get; set; } = 0; // 宽>高的文本块
            public int SquareTextCount { get; set; } = 0;     // 近似正方形的文本块
            
            // 尺寸特征
            public double MedianWidth { get; set; } = 0;
            public double MedianHeight { get; set; } = 0;
            public double WidthVariance { get; set; } = 0;
            public double HeightVariance { get; set; } = 0;
            
            // 边缘特征
            public List<double> LeftEdges { get; set; } = new List<double>();
            public List<double> RightEdges { get; set; } = new List<double>();
            public List<double> TopEdges { get; set; } = new List<double>();
            public List<double> BottomEdges { get; set; } = new List<double>();
            
            // 边缘变异度
            public double LeftEdgeVariance { get; set; } = 0;
            public double RightEdgeVariance { get; set; } = 0;
            public double TopEdgeVariance { get; set; } = 0;
            public double BottomEdgeVariance { get; set; } = 0;
            
            // 边缘变异比率
            public double LeftRightEdgeVarianceRatio { get; set; } = 1.0;
            public double TopBottomEdgeVarianceRatio { get; set; } = 1.0;
            
            // 行列特征
            public int HorizontalRowCount { get; set; } = 0;
            public int VerticalColumnCount { get; set; } = 0;
            public List<List<TextCellInfo>> HorizontalRows { get; set; } = new List<List<TextCellInfo>>();
            public List<List<TextCellInfo>> VerticalColumns { get; set; } = new List<List<TextCellInfo>>();
            
            // 间距特征
            public List<double> RowGaps { get; set; } = new List<double>();
            public List<double> ColumnGaps { get; set; } = new List<double>();
            public double MedianRowGap { get; set; } = 0;
            public double MedianColumnGap { get; set; } = 0;
            public double RowGapVariance { get; set; } = 0;
            public double ColumnGapVariance { get; set; } = 0;
            
            // 规律性特征
            public double RowGapRegularity { get; set; } = 1.0;
            public double ColumnGapRegularity { get; set; } = 1.0;
            
            // 对齐特征
            public int LeftAlignedCount { get; set; } = 0;
            public int RightAlignedCount { get; set; } = 0;
            public int TopAlignedCount { get; set; } = 0;
            public int BottomAlignedCount { get; set; } = 0;
            
            // 对齐精确度
            public double LeftEdgeAlignmentPrecision { get; set; } = 0;  // 左边缘对齐精确度
            public double RightEdgeAlignmentPrecision { get; set; } = 0; // 右边缘对齐精确度
            public double TopEdgeAlignmentPrecision { get; set; } = 0;   // 顶部边缘对齐精确度
            public double BottomEdgeAlignmentPrecision { get; set; } = 0; // 底部边缘对齐精确度
            
            // 段落特征
            public int ParagraphCount { get; set; } = 1;
            public double ParagraphGapThreshold { get; set; } = 0;
            public List<double> ParagraphGaps { get; set; } = new List<double>();
            public bool IsSequentialParagraphs { get; set; } = false; // 是否为连续段落布局
            
            // 内容分布特征
            public double ContentDensityLeftHalf { get; set; } = 0; // 左半页内容密度
            public double ContentDensityRightHalf { get; set; } = 0; // 右半页内容密度
            public double ContentDensityTopHalf { get; set; } = 0;   // 上半页内容密度
            public double ContentDensityBottomHalf { get; set; } = 0; // 下半页内容密度
            
            // 页面特征
            public double PageLeft { get; set; } = 0;
            public double PageRight { get; set; } = 0;
            public double PageTop { get; set; } = 0;
            public double PageBottom { get; set; } = 0;
            public double PageWidth { get; set; } = 0;
            public double PageHeight { get; set; } = 0;
            public double PageCenter_X { get; set; } = 0;
            public double PageCenter_Y { get; set; } = 0;
            
            // 特征关系
            public double RowColumnRatio { get; set; } = 1.0;
            public double VerticalHorizontalRatio { get; set; } = 1.0;
            public double LeftAlignmentRatio { get; set; } = 0.5;
            public double TopAlignmentRatio { get; set; } = 0.5;
            
            // 方向证据
            public int LeftToRightEvidence { get; set; } = 0;
            public int RightToLeftEvidence { get; set; } = 0;
            public int TopToBottomEvidence { get; set; } = 0;
            public int BottomToTopEvidence { get; set; } = 0;
            
            // 直方图特征 - 新增
            // 直方图原始数据
            public List<int> HorizontalHistogram { get; set; } = new List<int>();
            public List<int> VerticalHistogram { get; set; } = new List<int>();
            
            // bin大小
            public double HorizontalBinSize { get; set; } = 0;
            public double VerticalBinSize { get; set; } = 0;
            
            // 峰值位置
            public List<int> HorizontalPeakIndices { get; set; } = new List<int>();
            public List<int> VerticalPeakIndices { get; set; } = new List<int>();
            
            // 峰值特征
            public int HorizontalPeakCount { get; set; } = 0;  // 行数
            public int VerticalPeakCount { get; set; } = 0;    // 列数
            public double HorizontalPeakRegularity { get; set; } = 0; // 行间距规律性
            public double VerticalPeakRegularity { get; set; } = 0;   // 列间距规律性
            public double HorizontalPeakStrength { get; set; } = 0;   // 行结构强度
            public double VerticalPeakStrength { get; set; } = 0;     // 列结构强度
            
            // 【新增】峰值质量评估相关属性
            public List<double> VerticalPeakWidths { get; set; } = new List<double>();   // 垂直方向峰值宽度列表
            public List<double> HorizontalPeakWidths { get; set; } = new List<double>(); // 水平方向峰值宽度列表
            public double VerticalPeakMeanWidth { get; set; } = 0;    // 垂直方向峰值平均宽度
            public double HorizontalPeakMeanWidth { get; set; } = 0;  // 水平方向峰值平均宽度
            
            public List<double> VerticalPeakValleyRatios { get; set; } = new List<double>();   // 垂直方向峰谷比列表
            public List<double> HorizontalPeakValleyRatios { get; set; } = new List<double>(); // 水平方向峰谷比列表
            public double VerticalPeakValleyMeanRatio { get; set; } = 1.0;    // 垂直方向平均峰谷比
            public double HorizontalPeakValleyMeanRatio { get; set; } = 1.0;  // 水平方向平均峰谷比
            
            public double VerticalPeakShapeQuality { get; set; } = 0.5;   // 垂直方向峰值形状质量（0-1）
            public double HorizontalPeakShapeQuality { get; set; } = 0.5; // 水平方向峰值形状质量（0-1）
            
            // 【新增】方向强度和一致性特征 - 用于动态权重调整
            public double VerticalStructureStrength { get; set; } = 0.0;  // 垂直结构强度（0-1）
            public double HorizontalStructureStrength { get; set; } = 0.0; // 水平结构强度（0-1）
            public double DirectionalConsistency { get; set; } = 0.5;      // 方向一致性（0-1）
            
            // 直方图结构得分
            public int HistogramRowScore { get; set; } = 0;    // 行结构得分
            public int HistogramColumnScore { get; set; } = 0; // 列结构得分
        }

        /// <summary>
        /// 方向证据 - 用于存储各种布局方向的证据
        /// </summary>
        private class DirectionEvidence
        {
            // 文本块形状特征
            public int VerticalTextCount { get; set; } = 0;   // 高>宽的文本块
            public int HorizontalTextCount { get; set; } = 0; // 宽>高的文本块
            public int SquareTextCount { get; set; } = 0;     // 近似正方形的文本块
            
            // 边缘变异特征
            public double LeftEdgeVariance { get; set; } = 0;
            public double RightEdgeVariance { get; set; } = 0;
            public double TopEdgeVariance { get; set; } = 0;
            public double BottomEdgeVariance { get; set; } = 0;
            
            // 行列结构特征
            public int HorizontalAlignedRows { get; set; } = 0;
            public int VerticalAlignedColumns { get; set; } = 0;
            
            // 对齐特征
            public int LeftAlignedCount { get; set; } = 0;
            public int RightAlignedCount { get; set; } = 0;
            public int TopAlignedCount { get; set; } = 0;
            public int BottomAlignedCount { get; set; } = 0;
            
            // 段落特征
            public int ParagraphCount { get; set; } = 1;
            public bool IsSequentialParagraphs { get; set; } = false;
            
            // 内容分布特征
            public double ContentDensityLeftHalf { get; set; } = 0;
            public double ContentDensityRightHalf { get; set; } = 0;
            public double ContentDensityTopHalf { get; set; } = 0;
            public double ContentDensityBottomHalf { get; set; } = 0;
            
            // 直方图特征 - 新增
            public int HistogramColumnScore { get; set; } = 0; // 列结构得分
            public int HistogramRowScore { get; set; } = 0;    // 行结构得分
            public int VerticalPeakCount { get; set; } = 0;    // 列数
            public int HorizontalPeakCount { get; set; } = 0;  // 行数
            public double VerticalPeakRegularity { get; set; } = 0;  // 列间距规律性
            public double HorizontalPeakRegularity { get; set; } = 0; // 行间距规律性
            
            // 【新增】峰值质量评估相关证据
            public double VerticalPeakValleyMeanRatio { get; set; } = 1.0;  // 垂直方向平均峰谷比
            public double HorizontalPeakValleyMeanRatio { get; set; } = 1.0; // 水平方向平均峰谷比
            public double VerticalPeakShapeQuality { get; set; } = 0.5;     // 垂直方向峰值形状质量（0-1）
            public double HorizontalPeakShapeQuality { get; set; } = 0.5;   // 水平方向峰值形状质量（0-1）
            
            // 【新增】结构强度证据
            public double VerticalStructureStrength { get; set; } = 0.0;  // 垂直结构强度（0-1）
            public double HorizontalStructureStrength { get; set; } = 0.0; // 水平结构强度（0-1）
            public double DirectionalConsistency { get; set; } = 0.5;      // 方向一致性（0-1）
            
            // 方向计数 - 添加回缺失的属性
            public int LeftToRightCount { get; set; } = 0;
            public int RightToLeftCount { get; set; } = 0;
            public int TopToBottomCount { get; set; } = 0;
            public int BottomToTopCount { get; set; } = 0;
            
            // 对齐比率 - 添加回缺失的属性
            public double LeftAlignmentRatio { get; set; } = 0.5;
            public double TopAlignmentRatio { get; set; } = 0.5;
        }

        /// <summary>
        /// 将相似的值分组
        /// </summary>
        private List<List<double>> GroupSimilarValues(List<double> values, double threshold)
        {
            var groups = new List<List<double>>();

            foreach (var value in values)
            {
                bool addedToExisting = false;

                foreach (var group in groups)
                {
                    if (Math.Abs(group[0] - value) <= threshold)
                    {
                        group.Add(value);
                        addedToExisting = true;
                        break;
                    }
                }

                if (!addedToExisting)
                {
                    groups.Add(new List<double> { value });
                }
            }

            return groups;
        }

        /// <summary>
        /// 综合分析方向证据，确定最终方向
        /// 使用得分累加系统来判断方向，适用于各种语言和文档类型
        /// </summary>
        /// <param name="evidence">收集的方向证据</param>
        /// <param name="result">输出的方向结果</param>
        /// <param name="cells">文本单元格列表</param>
        private void AnalyzeDirectionEvidence(DirectionEvidence evidence, TextDirectionResult result, List<TextCellInfo> cells)
        {
            // 使用得分累加系统，而非直接设置置信度
            // 这种方法更加通用，可以处理各种语言和排版方式

            // ====================== 水平方向分析 ======================
            // 水平方向得分系统，分别计算从左到右和从右到左的证据得分
            int leftToRightScore = 0;
            int rightToLeftScore = 0;
            
            // 计算基于文本块数量的基准权重 - 动态基准
            int baseWeight = Math.Max(1, Math.Min(5, cells.Count / 10));
            
            // 1.1 基础方向证据 - 基础权重
            leftToRightScore += evidence.LeftToRightCount;
            rightToLeftScore += evidence.RightToLeftCount;

            // 1.2 对齐特征分析 - 使用相对比例
            if (evidence.LeftAlignedCount > 0 || evidence.RightAlignedCount > 0)
            {
                double totalAligned = evidence.LeftAlignedCount + evidence.RightAlignedCount;
                if (totalAligned > 0)
                {
                    double leftAlignRatio = evidence.LeftAlignedCount / totalAligned;
                    double rightAlignRatio = evidence.RightAlignedCount / totalAligned;
                    
                    // 对齐比例差异阈值 - 动态计算
                    double alignDiffThreshold = Math.Max(0.15, 0.2 - cells.Count * 0.002);
                    
                    // 对齐比例差异超过阈值才有明显意义
                    if (leftAlignRatio > rightAlignRatio + alignDiffThreshold)
                    {
                        double strengthFactor = Math.Min(1.0, (leftAlignRatio - rightAlignRatio) / 0.5);
                        int weightedScore = (int)(Math.Min(evidence.LeftAlignedCount, cells.Count / 2) * strengthFactor);
                        leftToRightScore += weightedScore;
                    }
                    else if (rightAlignRatio > leftAlignRatio + alignDiffThreshold)
                    {
                        double strengthFactor = Math.Min(1.0, (rightAlignRatio - leftAlignRatio) / 0.5);
                        int weightedScore = (int)(Math.Min(evidence.RightAlignedCount, cells.Count / 2) * strengthFactor);
                        rightToLeftScore += weightedScore;
                    }
                }
            }

            // 1.3 边界变异特征分析 - 使用动态阈值
            if (evidence.LeftEdgeVariance > 0 && evidence.RightEdgeVariance > 0)
            {
                double edgeVarianceRatio = evidence.LeftEdgeVariance / evidence.RightEdgeVariance;
                
                // 边缘变异比率阈值 - 动态计算
                double lrLowThreshold = Math.Max(0.7, 0.7 - cells.Count * 0.001);
                double lrHighThreshold = Math.Min(1.4, 1.4 + cells.Count * 0.001);
                
                // 使用比率而非固定倍数判断
                if (edgeVarianceRatio < lrLowThreshold) // 左边界比右边界更整齐
                {
                    double strengthFactor = Math.Min(1.0, (lrLowThreshold - edgeVarianceRatio) / 0.4);
                    leftToRightScore += (int)(baseWeight * 3 * strengthFactor);
                }
                else if (edgeVarianceRatio > lrHighThreshold) // 右边界比左边界更整齐
                {
                    double strengthFactor = Math.Min(1.0, (edgeVarianceRatio - lrHighThreshold) / 0.6);
                    rightToLeftScore += (int)(baseWeight * 3 * strengthFactor);
                }
            }

            // 1.4 形状特征分析 - 使用相对权重
            // 横向文本块比例 - 考虑文本块总数
            if (evidence.HorizontalTextCount > 0 || evidence.VerticalTextCount > 0)
            {
                double totalTextCount = evidence.HorizontalTextCount + evidence.VerticalTextCount;
                if (totalTextCount > 0)
                {
                    double horizontalRatio = evidence.HorizontalTextCount / totalTextCount;
                    
                    // 横向文本占比越高，越可能从左到右
                    if (horizontalRatio > 0.6 && evidence.HorizontalTextCount >= 3)
                    {
                        double strengthFactor = Math.Min(1.0, (horizontalRatio - 0.6) / 0.4);
                        int weightedScore = (int)(baseWeight * 5 * strengthFactor);
                        leftToRightScore += weightedScore;
                    }
                }
            }

            // 1.5 行列结构分析 - 使用相对权重
            // 行数与列数比较
            if (evidence.HorizontalAlignedRows > 0 || evidence.VerticalAlignedColumns > 0)
            {
                double totalRowColCount = evidence.HorizontalAlignedRows + evidence.VerticalAlignedColumns;
                if (totalRowColCount > 0)
                {
                    double rowRatio = evidence.HorizontalAlignedRows / totalRowColCount;
                    
                    // 行占比越高，越可能从左到右
                    if (rowRatio > 0.6 && evidence.HorizontalAlignedRows >= 2)
                    {
                        double strengthFactor = Math.Min(1.0, (rowRatio - 0.6) / 0.4);
                        int weightedScore = (int)(baseWeight * 4 * strengthFactor);
                        leftToRightScore += weightedScore;
                    }
                }
            }

            // 1.6 段落特征分析
            if (evidence.ParagraphCount >= 2)
            {
                // 多段落文本通常是从左到右阅读的
                int paragraphBonus = Math.Min(10, evidence.ParagraphCount * 2);
                leftToRightScore += paragraphBonus;
            }

            // 1.7 内容密度分析
            // 左右内容密度比较，基于统计特征而非语言假设
            if (evidence.LeftEdgeVariance > 0 && evidence.RightEdgeVariance > 0)
            {
                // 左边界变异小于右边界变异，通常表示从左到右阅读
                if (evidence.LeftEdgeVariance < evidence.RightEdgeVariance * 0.8)
                {
                    leftToRightScore += baseWeight * 2;
                }
                // 右边界变异小于左边界变异，通常表示从右到左阅读
                else if (evidence.RightEdgeVariance < evidence.LeftEdgeVariance * 0.8)
                {
                    rightToLeftScore += baseWeight * 2;
                }
            }

            // ====================== 垂直方向分析 ======================
            // 垂直方向得分系统，分别计算从上到下和从下到上的证据得分
            int topToBottomScore = 0;
            int bottomToTopScore = 0;

            // 2.1 基础方向证据 - 基础权重
            topToBottomScore += evidence.TopToBottomCount;
            bottomToTopScore += evidence.BottomToTopCount;

            // 2.2 对齐特征分析 - 使用相对比例
            if (evidence.TopAlignedCount > 0 || evidence.BottomAlignedCount > 0)
            {
                double totalAligned = evidence.TopAlignedCount + evidence.BottomAlignedCount;
                if (totalAligned > 0)
                {
                    double topAlignRatio = evidence.TopAlignedCount / totalAligned;
                    double bottomAlignRatio = evidence.BottomAlignedCount / totalAligned;
                    
                    // 对齐比例差异阈值 - 动态计算
                    double alignDiffThreshold = Math.Max(0.15, 0.2 - cells.Count * 0.002);
                    
                    // 对齐比例差异超过阈值才有明显意义
                    if (topAlignRatio > bottomAlignRatio + alignDiffThreshold)
                    {
                        double strengthFactor = Math.Min(1.0, (topAlignRatio - bottomAlignRatio) / 0.5);
                        int weightedScore = (int)(Math.Min(evidence.TopAlignedCount, cells.Count / 2) * strengthFactor);
                        topToBottomScore += weightedScore;
                    }
                    else if (bottomAlignRatio > topAlignRatio + alignDiffThreshold)
                    {
                        double strengthFactor = Math.Min(1.0, (bottomAlignRatio - topAlignRatio) / 0.5);
                        int weightedScore = (int)(Math.Min(evidence.BottomAlignedCount, cells.Count / 2) * strengthFactor);
                        bottomToTopScore += weightedScore;
                    }
                }
            }

            // 2.3 边界变异特征分析 - 使用动态阈值
            if (evidence.TopEdgeVariance > 0 && evidence.BottomEdgeVariance > 0)
            {
                double edgeVarianceRatio = evidence.TopEdgeVariance / evidence.BottomEdgeVariance;
                
                // 边缘变异比率阈值 - 动态计算
                double tbLowThreshold = Math.Max(0.7, 0.7 - cells.Count * 0.001);
                double tbHighThreshold = Math.Min(1.4, 1.4 + cells.Count * 0.001);
                
                // 使用比率而非固定倍数判断
                if (edgeVarianceRatio < tbLowThreshold) // 上边界比下边界更整齐
                {
                    double strengthFactor = Math.Min(1.0, (tbLowThreshold - edgeVarianceRatio) / 0.4);
                    topToBottomScore += (int)(baseWeight * 3 * strengthFactor);
                }
                else if (edgeVarianceRatio > tbHighThreshold) // 下边界比上边界更整齐
                {
                    double strengthFactor = Math.Min(1.0, (edgeVarianceRatio - tbHighThreshold) / 0.6);
                    bottomToTopScore += (int)(baseWeight * 3 * strengthFactor);
                }
            }

            // 2.4 形状特征分析 - 使用相对权重
            // 竖向文本块比例 - 考虑文本块总数
            if (evidence.HorizontalTextCount > 0 || evidence.VerticalTextCount > 0)
            {
                double totalTextCount = evidence.HorizontalTextCount + evidence.VerticalTextCount;
                if (totalTextCount > 0)
                {
                    double verticalRatio = evidence.VerticalTextCount / totalTextCount;
                    
                    // 竖向文本占比越高，越可能从上到下
                    if (verticalRatio > 0.6 && evidence.VerticalTextCount >= 3)
                    {
                        double strengthFactor = Math.Min(1.0, (verticalRatio - 0.6) / 0.4);
                        int weightedScore = (int)(baseWeight * 5 * strengthFactor);
                        topToBottomScore += weightedScore;
                    }
                }
            }

            // 2.5 行列结构分析 - 使用相对权重
            // 行数与列数比较
            if (evidence.HorizontalAlignedRows > 0 || evidence.VerticalAlignedColumns > 0)
            {
                double totalRowColCount = evidence.HorizontalAlignedRows + evidence.VerticalAlignedColumns;
                if (totalRowColCount > 0)
                {
                    double colRatio = evidence.VerticalAlignedColumns / totalRowColCount;
                    
                    // 列占比越高，越可能从上到下
                    if (colRatio > 0.6 && evidence.VerticalAlignedColumns >= 2)
                    {
                        double strengthFactor = Math.Min(1.0, (colRatio - 0.6) / 0.4);
                        int weightedScore = (int)(baseWeight * 4 * strengthFactor);
                        topToBottomScore += weightedScore;
                    }
                }
            }

            // 2.6 内容密度分析
            // 上下内容密度比较，基于统计特征而非语言假设
            if (evidence.TopEdgeVariance > 0 && evidence.BottomEdgeVariance > 0)
            {
                // 上边界变异小于下边界变异，通常表示从上到下阅读
                if (evidence.TopEdgeVariance < evidence.BottomEdgeVariance * 0.8)
                {
                    topToBottomScore += baseWeight * 2;
                }
                // 下边界变异小于上边界变异，通常表示从下到上阅读
                else if (evidence.BottomEdgeVariance < evidence.TopEdgeVariance * 0.8)
                {
                    bottomToTopScore += baseWeight * 2;
                }
            }

            // ====================== 方向决策 ======================
            // 【新增】动态权重系统 - 基于特征可靠性调整权重
            double leftRightReliability = 1.0;
            double topBottomReliability = 1.0;
            
            // 基于文本块数量调整可靠性
            if (cells.Count >= 10)
            {
                leftRightReliability *= 1.2; // 文本块较多，水平方向证据更可靠
                topBottomReliability *= 1.2; // 文本块较多，垂直方向证据更可靠
            }
            
            // 基于形状特征调整可靠性
            if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 2)
            {
                // 竖排文本明显占优，垂直方向证据更可靠
                topBottomReliability *= 1.3;
                // 同时，对于竖排文本，水平方向的判断需要更谨慎
                leftRightReliability *= 0.9;
            }
            else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 2)
            {
                // 横排文本明显占优，水平方向证据更可靠
                leftRightReliability *= 1.3;
                topBottomReliability *= 0.9;
            }
            
            // 【新增】证据一致性分析
            // 检查不同类型证据是否指向同一方向
            double leftToRightConsistency = 1.0;
            double rightToLeftConsistency = 1.0;
            
            // 1. 边缘变异与对齐特征一致性
            if (evidence.LeftEdgeVariance < evidence.RightEdgeVariance && 
                evidence.LeftAlignedCount > evidence.RightAlignedCount)
            {
                // 左边界变异小且左对齐多，强烈暗示从左到右
                leftToRightConsistency += 0.2;
            }
            else if (evidence.RightEdgeVariance < evidence.LeftEdgeVariance && 
                    evidence.RightAlignedCount > evidence.LeftAlignedCount)
            {
                // 右边界变异小且右对齐多，强烈暗示从右到左
                rightToLeftConsistency += 0.2;
            }
            
            // 应用动态权重
            int adjustedLeftToRightScore = (int)(leftToRightScore * leftRightReliability * leftToRightConsistency);
            int adjustedRightToLeftScore = (int)(rightToLeftScore * leftRightReliability * rightToLeftConsistency);
            
            // 水平方向决策
            TextFlowDirection horizontalDirection = TextFlowDirection.LeftToRight; // 默认从左到右
            int horizontalConfidence = 0;

            // 如果有足够的证据，确定水平方向
            if (adjustedLeftToRightScore > 0 || adjustedRightToLeftScore > 0)
            {
                // 计算总分和差异
                int totalHorizontalScore = adjustedLeftToRightScore + adjustedRightToLeftScore;
                int horizontalDiff = Math.Abs(adjustedLeftToRightScore - adjustedRightToLeftScore);
                
                // 计算置信度 - 基于分数差异和总分
                horizontalConfidence = CalculateConfidenceFromScore(
                    Math.Max(adjustedLeftToRightScore, adjustedRightToLeftScore),
                    Math.Min(adjustedLeftToRightScore, adjustedRightToLeftScore),
                    totalHorizontalScore,
                    leftRightReliability,
                    Math.Max(leftToRightConsistency, rightToLeftConsistency));
                
                // 确定方向 - 得分高的方向胜出
                if (adjustedRightToLeftScore > adjustedLeftToRightScore)
                {
                    horizontalDirection = TextFlowDirection.RightToLeft;
                }
            }
            else
            {
                // 没有足够的证据，使用默认方向和低置信度
                horizontalConfidence = 60;
            }

            // 【新增】应用垂直方向的动态权重
            // 检查不同类型证据是否指向同一垂直方向
            double topToBottomConsistency = 1.0;
            double bottomToTopConsistency = 1.0;
            
            // 边缘变异与对齐特征一致性
            if (evidence.TopEdgeVariance < evidence.BottomEdgeVariance && 
                evidence.TopAlignedCount > evidence.BottomAlignedCount)
            {
                // 上边界变异小且上对齐多，强烈暗示从上到下
                topToBottomConsistency += 0.2;
            }
            else if (evidence.BottomEdgeVariance < evidence.TopEdgeVariance && 
                    evidence.BottomAlignedCount > evidence.TopAlignedCount)
            {
                // 下边界变异小且下对齐多，强烈暗示从下到上
                bottomToTopConsistency += 0.2;
            }
            
            // 应用动态权重
            int adjustedTopToBottomScore = (int)(topToBottomScore * topBottomReliability * topToBottomConsistency);
            int adjustedBottomToTopScore = (int)(bottomToTopScore * topBottomReliability * bottomToTopConsistency);
            
            // 垂直方向决策
            TextFlowDirection verticalDirection = TextFlowDirection.TopToBottom; // 默认从上到下
            int verticalConfidence = 0;

            // 如果有足够的证据，确定垂直方向
            if (adjustedTopToBottomScore > 0 || adjustedBottomToTopScore > 0)
            {
                // 计算总分和差异
                int totalVerticalScore = adjustedTopToBottomScore + adjustedBottomToTopScore;
                int verticalDiff = Math.Abs(adjustedTopToBottomScore - adjustedBottomToTopScore);
                
                // 计算置信度 - 基于分数差异和总分
                verticalConfidence = CalculateConfidenceFromScore(
                    Math.Max(adjustedTopToBottomScore, adjustedBottomToTopScore),
                    Math.Min(adjustedTopToBottomScore, adjustedBottomToTopScore),
                    totalVerticalScore,
                    topBottomReliability,
                    Math.Max(topToBottomConsistency, bottomToTopConsistency));
                
                // 确定方向 - 得分高的方向胜出
                if (adjustedBottomToTopScore > adjustedTopToBottomScore)
                {
                    verticalDirection = TextFlowDirection.BottomToTop;
                }
            }
            else
            {
                // 没有足够的证据，使用默认方向和低置信度
                verticalConfidence = 60;
            }

            // 设置结果
            result.HorizontalDirection = horizontalDirection;
            result.HorizontalConfidence = horizontalConfidence;
            result.VerticalDirection = verticalDirection;
            result.VerticalConfidence = verticalConfidence;
        }

        /// <summary>
        /// 计算置信度分数
        /// </summary>
        /// <param name="winningScore">获胜方向的得分</param>
        /// <param name="opposingScore">对立方向的得分</param>
        /// <param name="totalScore">总得分</param>
        /// <param name="featureReliability">特征可靠性系数</param>
        /// <param name="evidenceConsistency">证据一致性系数</param>
        /// <returns>计算出的置信度（55-95）</returns>
        private int CalculateConfidenceFromScore(int winningScore, int opposingScore, int totalScore,
                                               double featureReliability = 1.0, double evidenceConsistency = 1.0)
        {
            // 基础置信度基于分数差异比例
            // 使用相对差异比例而非绝对差异，使结果更通用
            double scoreDiff = winningScore - opposingScore;
            double diffRatio = scoreDiff / Math.Max(1, totalScore);

            // 映射到置信度范围：55-95
            // 下限保证最低可信度，上限避免过于武断
            int baseConfidence = 55 + (int)(diffRatio * 40);

            // 高总分提升置信度 - 更多证据支持时提升置信度
            // 使用平方根函数使增长合理
            int scoreBoost = (int)Math.Min(10, Math.Sqrt(totalScore));
            
            // 【新增】多维度置信度调整
            // 1. 特征可靠性维度 - 可靠性越高，置信度越高
            int reliabilityAdjustment = (int)(Math.Min(10, Math.Max(-10, (featureReliability - 1.0) * 20)));
            
            // 2. 证据一致性维度 - 一致性越高，置信度越高
            int consistencyAdjustment = (int)(Math.Min(10, Math.Max(0, (evidenceConsistency - 1.0) * 20)));
            
            // 综合多维度调整
            int finalConfidence = baseConfidence + scoreBoost + reliabilityAdjustment + consistencyAdjustment;
            
            // 【新增】置信度稳定性增强
            // 检查得分差异是否足够显著
            double scoreDiffSignificance = (double)(winningScore - opposingScore) / Math.Max(1, totalScore);
            
            // 如果差异不够显著，适当降低置信度
            if (scoreDiffSignificance < 0.2) // 差异不到20%
            {
                // 降低置信度，避免过度自信
                int stabilityPenalty = (int)(Math.Max(5, 15 * (0.2 - scoreDiffSignificance)));
                finalConfidence = Math.Max(55, finalConfidence - stabilityPenalty);
            }
            
            // 检查证据强度是否足够
            if (totalScore < 10) // 总证据不足
            {
                // 证据不足时降低置信度
                int evidencePenalty = (int)(Math.Max(5, 15 * (1.0 - totalScore / 10.0)));
                finalConfidence = Math.Max(55, finalConfidence - evidencePenalty);
            }
            
            // 【新增】边缘情况处理
            // 证据严重矛盾的情况
            if (winningScore > 0 && opposingScore > 0 && 
                Math.Min(winningScore, opposingScore) > Math.Max(winningScore, opposingScore) * 0.8)
            {
                // 证据高度矛盾，降低置信度
                finalConfidence = Math.Min(finalConfidence, 70);
            }

            // 确保置信度在合理范围内
            return Math.Max(55, Math.Min(95, finalConfidence));
        }

        /// <summary>
        /// 计算自适应分组阈值，基于文本块高宽比分布
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        private void CalculateAdaptiveThresholds(List<TextCellInfo> cells)
        {
            // 初始化默认阈值
            double defaultThresholdVertical = 25.0;
            double defaultThresholdHorizontal = 10.0;
            
            if (cells.Count < 2)
            {
                groupingThresholdVertical = defaultThresholdVertical;
                groupingThresholdHorizontal = defaultThresholdHorizontal;
                return;
            }

            // 计算比例分布
            var ratios = cells
                .Where(c => c?.location != null)
                .Select(c => Math.Max(
                    c.location.height / (double)c.location.width,
                    c.location.width / (double)c.location.height))
                .OrderBy(r => r)
                .ToList();

            if (ratios.Count == 0)
            {
                groupingThresholdVertical = defaultThresholdVertical;
                groupingThresholdHorizontal = defaultThresholdHorizontal;
                return;
            }

            // 检查是否存在极端比例（高于7:1）
            bool hasExtremeRatio = ratios.Any(r => r > 7.0);
            // 检查是否存在超极端比例（高于10:1）
            bool hasSuperExtremeRatio = ratios.Any(r => r > 10.0);

            // 分析比例分布，计算高比例文本块的百分比
            int extremeRatioCount = ratios.Count(r => r > 5.0);
            double extremeRatioPercentage = (double)extremeRatioCount / ratios.Count;

            // 计算自适应阈值
            double adaptiveVerticalThreshold;
            double adaptiveHorizontalThreshold;
            
            // 根据极端比例的存在情况，自适应调整阈值
            if (hasSuperExtremeRatio)
            {
                System.Diagnostics.Debug.WriteLine("检测到超极端竖排比例(>10:1)，使用特殊阈值");
                adaptiveVerticalThreshold = defaultThresholdVertical * 2.0; // 更大的阈值
                adaptiveHorizontalThreshold = defaultThresholdHorizontal * 1.8; // 也调整横向阈值
            }
            else if (hasExtremeRatio)
            {
                System.Diagnostics.Debug.WriteLine("检测到极端比例(>7:1)，调整阈值");
                adaptiveVerticalThreshold = defaultThresholdVertical * 1.5; // 调整阈值
                adaptiveHorizontalThreshold = defaultThresholdHorizontal * 1.3;
            }
            else if (extremeRatioPercentage > 0.3) // 如果30%以上的文本块都有较高比例
            {
                adaptiveVerticalThreshold = defaultThresholdVertical * 1.2; 
                adaptiveHorizontalThreshold = defaultThresholdHorizontal * 1.2;
            }
            else
            {
                // 使用标准阈值
                adaptiveVerticalThreshold = defaultThresholdVertical;
                adaptiveHorizontalThreshold = defaultThresholdHorizontal;
            }

            // 安全检查 - 确保阈值在合理范围内
            groupingThresholdVertical = Math.Max(15.0, Math.Min(adaptiveVerticalThreshold, 100.0));
            groupingThresholdHorizontal = Math.Max(5.0, Math.Min(adaptiveHorizontalThreshold, 50.0));
            
            // 计算极端竖排比例的自适应阈值
            // 这个阈值用于判断哪些文本块是极端竖排的
            if (ratios.Count >= 5)
            {
                // 使用百分位数来判断极端比例
                // 找出前20%的比例值作为极端比例的起点
                int percentileIndex = Math.Max(0, (int)(ratios.Count * 0.8) - 1);
                double percentileValue = ratios[percentileIndex];
                
                // 取5和百分位数的较大值，确保阈值不会太低
                extremeVerticalRatioThreshold = Math.Max(5.0, percentileValue);
                
                System.Diagnostics.Debug.WriteLine($"自适应极端竖排比例阈值: {extremeVerticalRatioThreshold:F1}");
            }
            else
            {
                // 默认值
                extremeVerticalRatioThreshold = 7.0;
            }

            System.Diagnostics.Debug.WriteLine($"自适应阈值 - 垂直: {groupingThresholdVertical:F1}, 水平: {groupingThresholdHorizontal:F1}, 存在极端比例: {hasExtremeRatio}, 超极端比例: {hasSuperExtremeRatio}");
        }

        /// <summary>
        /// 计算标准差
        /// </summary>
        /// <param name="values">数值列表</param>
        /// <param name="useSampleVariance">是否使用样本方差（除以n-1）</param>
        /// <returns>标准差</returns>
        private double CalculateStandardDeviation(List<double> values, bool useSampleVariance = false)
        {
            if (values.Count <= 1) return 0;
            
            double avg = values.Average();
            double sumOfSquaredDifferences = values.Sum(x => Math.Pow(x - avg, 2));
            
            // 使用样本方差(除以n-1)或总体方差(除以n)
            int divisor = useSampleVariance ? values.Count - 1 : values.Count;
            double variance = sumOfSquaredDifferences / divisor;
            
            return Math.Sqrt(variance);
        }
        
        /// <summary>
        /// 提取统一的文本布局特征
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <returns>文本布局特征对象</returns>
        private TextLayoutFeatures ExtractTextFeatures(List<TextCellInfo> cells)
        {
            var features = new TextLayoutFeatures();
            
            // 设置总文本块数
            features.TotalTextBlocks = cells.Count;
            
            // 过滤有效的文本块
            var validCells = cells.Where(c => 
                c.location.width >= 5 && 
                c.location.height >= 5 && 
                !string.IsNullOrWhiteSpace(c.words)).ToList();
            
            features.ValidTextBlocks = validCells.Count;
            
            if (validCells.Count < 2) return features;
            
            // 1. 首先提取页面边界特征，确保其他特征提取可以使用页面边界信息
            ExtractPageBoundaryFeatures(validCells, features);
            
            // 2. 提取直方图特征 - 这将提供关键的行列结构信息
            ExtractHistogramFeatures(validCells, features);
            
            // 3. 提取形状特征
            ExtractShapeFeatures(validCells, features);
            
            // 4. 提取边缘特征
            ExtractEdgeFeatures(validCells, features);
            
            // 5. 提取行列结构特征 - 现在可以利用直方图信息
            ExtractRowColumnFeatures(validCells, features);
            
            // 6. 提取对齐特征
            ExtractAlignmentFeatures(validCells, features);
            
            // 7. 提取段落特征
            ExtractParagraphFeatures(validCells, features);
            
            // 8. 提取内容分布特征
            ExtractContentDistributionFeatures(validCells, features);
            
            // 9. 提取文本方向特征
            ExtractTextDirectionFeatures(validCells, features);
            
            // 10. 计算特征比率和关系
            CalculateFeatureRelationships(features);
            
            return features;
        }
        
        /// <summary>
        /// 提取页面边界特征 - 这是其他特征提取的基础
        /// </summary>
        private void ExtractPageBoundaryFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 计算页面范围
            features.PageLeft = cells.Min(c => c.location.left);
            features.PageRight = cells.Max(c => c.location.left + c.location.width);
            features.PageTop = cells.Min(c => c.location.top);
            features.PageBottom = cells.Max(c => c.location.top + c.location.height);
            features.PageWidth = features.PageRight - features.PageLeft;
            features.PageHeight = features.PageBottom - features.PageTop;
            features.PageCenter_X = features.PageLeft + features.PageWidth / 2;
            features.PageCenter_Y = features.PageTop + features.PageHeight / 2;
        }
        
        /// <summary>
        /// 提取文本方向特征 - 特别关注竖排文本的水平方向判断
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <param name="features">文本布局特征</param>
        private void ExtractTextDirectionFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            if (cells.Count < 3) return;
            
            // 检测是否为竖排文本
            bool isLikelyVerticalText = features.VerticalTextCount > features.HorizontalTextCount * 1.5;
            
            if (!isLikelyVerticalText) return;
            
            // 对于竖排文本，分析文本的排列方向
            
            // 1. 检查多列情况下的阅读顺序
            if (features.VerticalColumnCount >= 2)
            {
                // 按X坐标排序列
                var sortedColumns = features.VerticalColumns.OrderBy(col => 
                    col.Average(c => c.location.left)).ToList();
                
                // 检查首列和末列的文本密度
                if (sortedColumns.Count >= 2)
                {
                    var firstColumn = sortedColumns.First();
                    var lastColumn = sortedColumns.Last();
                    
                    double firstColumnDensity = firstColumn.Sum(c => c.location.width * c.location.height);
                    double lastColumnDensity = lastColumn.Sum(c => c.location.width * c.location.height);
                    
                    // 首列密度明显高于末列，可能是从左到右阅读
                    if (firstColumnDensity > lastColumnDensity * 1.3)
                    {
                        features.LeftToRightEvidence += 3;
                    }
                    // 末列密度明显高于首列，可能是从右到左阅读
                    else if (lastColumnDensity > firstColumnDensity * 1.3)
                    {
                        features.RightToLeftEvidence += 3;
                    }
                }
                
                // 2. 检查列间距的规律性
                var columnCenters = features.VerticalColumns.Select(col => 
                    col.Average(c => c.location.left + c.location.width / 2)).OrderBy(x => x).ToList();
                
                if (columnCenters.Count >= 3)
                {
                    // 计算列间距
                    List<double> columnGaps = new List<double>();
                    for (int i = 1; i < columnCenters.Count; i++)
                    {
                        columnGaps.Add(columnCenters[i] - columnCenters[i-1]);
                    }
                    
                    // 检查列间距是否从左到右递增（可能表示从右到左阅读）
                    bool increasingGaps = true;
                    for (int i = 1; i < columnGaps.Count; i++)
                    {
                        if (columnGaps[i] < columnGaps[i-1] * 0.8)
                        {
                            increasingGaps = false;
                            break;
                        }
                    }
                    
                    // 检查列间距是否从左到右递减（可能表示从左到右阅读）
                    bool decreasingGaps = true;
                    for (int i = 1; i < columnGaps.Count; i++)
                    {
                        if (columnGaps[i] > columnGaps[i-1] * 0.8)
                        {
                            decreasingGaps = false;
                            break;
                        }
                    }
                    
                    if (increasingGaps)
                    {
                        features.RightToLeftEvidence += 2;
                    }
                    else if (decreasingGaps)
                    {
                        features.LeftToRightEvidence += 2;
                    }
                }
            }
            
            // 3. 分析单个竖排文本块的内部特征
            var verticalTextBlocks = cells.Where(c => c.location.height > c.location.width * 2).ToList();
            if (verticalTextBlocks.Count >= 3)
            {
                // 检查字符分布 - 对于中日韩文字，可以通过分析Unicode范围判断
                int cjkCharCount = 0;
                int totalCharCount = 0;
                
                foreach (var cell in verticalTextBlocks)
                {
                    if (string.IsNullOrEmpty(cell.words)) continue;
                    
                    foreach (char c in cell.words)
                    {
                        totalCharCount++;
                        // 检查是否为中日韩文字（粗略判断）
                        if (c >= 0x4E00 && c <= 0x9FFF) // 基本汉字范围
                        {
                            cjkCharCount++;
                        }
                    }
                }
                
                // 如果大部分是中日韩文字，增加从右到左的证据
                // 但这只是一个统计特征，不是绝对规则
                if (totalCharCount > 0)
                {
                    double cjkRatio = (double)cjkCharCount / totalCharCount;
                    if (cjkRatio > 0.7)
                    {
                        // 【修改】增加更强的从右到左证据，特别是当CJK字符比例很高时
                        int evidenceBoost = (int)(3 * Math.Min(1.0, cjkRatio));
                        features.RightToLeftEvidence += evidenceBoost;
                        System.Diagnostics.Debug.WriteLine($"检测到中日韩文字比例较高({cjkRatio:F2})，增加从右到左证据: +{evidenceBoost}");
                    }
                    else if (cjkRatio > 0.5)
                    {
                        // 中等比例的CJK字符，增加适中的证据
                        features.RightToLeftEvidence += 1;
                        System.Diagnostics.Debug.WriteLine($"检测到中等比例的中日韩文字({cjkRatio:F2})，增加从右到左证据: +1");
                    }
                }
            }
            
            // 4. 分析页面整体布局
            // 计算页面中心
            double pageWidth = cells.Max(c => c.location.left + c.location.width);
            double pageCenter = pageWidth / 2;
            
            // 计算文本块在页面左右两侧的分布
            int leftSideBlocks = 0;
            int rightSideBlocks = 0;
            
            foreach (var cell in verticalTextBlocks)
            {
                double cellCenter = cell.location.left + cell.location.width / 2;
                if (cellCenter < pageCenter)
                {
                    leftSideBlocks++;
                }
                else
                {
                    rightSideBlocks++;
                }
            }
            
            // 如果右侧文本块明显多于左侧，可能是从右到左阅读
            if (rightSideBlocks > leftSideBlocks * 1.5 && rightSideBlocks >= 3)
            {
                features.RightToLeftEvidence += 2;
            }
            // 如果左侧文本块明显多于右侧，可能是从左到右阅读
            else if (leftSideBlocks > rightSideBlocks * 1.5 && leftSideBlocks >= 3)
            {
                features.LeftToRightEvidence += 2;
            }
        }
        
        /// <summary>
        /// 计算特征之间的关系
        /// </summary>
        /// <param name="features">文本布局特征</param>
        private void CalculateFeatureRelationships(TextLayoutFeatures features)
        {
            // 计算边缘变异比率
            if (features.RightEdgeVariance > 0)
            {
                features.LeftRightEdgeVarianceRatio = features.LeftEdgeVariance / features.RightEdgeVariance;
            }
            
            if (features.BottomEdgeVariance > 0)
            {
                features.TopBottomEdgeVarianceRatio = features.TopEdgeVariance / features.BottomEdgeVariance;
            }
            
            // 计算行列比例
            if (features.VerticalColumnCount > 0)
            {
                features.RowColumnRatio = (double)features.HorizontalRowCount / features.VerticalColumnCount;
            }
            
            // 计算形状比例
            if (features.HorizontalTextCount > 0)
            {
                features.VerticalHorizontalRatio = (double)features.VerticalTextCount / features.HorizontalTextCount;
            }
            
            // 计算对齐比例
            double totalHorizontalAligned = features.LeftAlignedCount + features.RightAlignedCount;
            if (totalHorizontalAligned > 0)
            {
                features.LeftAlignmentRatio = features.LeftAlignedCount / totalHorizontalAligned;
            }
            
            double totalVerticalAligned = features.TopAlignedCount + features.BottomAlignedCount;
            if (totalVerticalAligned > 0)
            {
                features.TopAlignmentRatio = features.TopAlignedCount / totalVerticalAligned;
            }
        }

        /// <summary>
        /// 提取文本形状特征
        /// </summary>
        private void ExtractShapeFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 计算所有文本块的高宽比和宽高比
            foreach (var cell in cells)
            {
                double hwRatio = cell.location.height / (double)cell.location.width;
                double whRatio = cell.location.width / (double)cell.location.height;
                
                features.HeightWidthRatios.Add(hwRatio);
                features.WidthHeightRatios.Add(whRatio);
            }
            
            // 按高宽比排序，用于计算百分位数
            var sortedHWRatios = features.HeightWidthRatios.OrderBy(r => r).ToList();
            var sortedWHRatios = features.WidthHeightRatios.OrderBy(r => r).ToList();
            
            // 计算统计特征
            if (sortedHWRatios.Count > 0)
            {
                // 中位数
                int midIndex = sortedHWRatios.Count / 2;
                features.MedianHeightWidthRatio = sortedHWRatios[midIndex];
                features.MedianWidthHeightRatio = sortedWHRatios[midIndex];
                
                // 方差
                features.HeightWidthRatioVariance = CalculateVariance(sortedHWRatios);
                features.WidthHeightRatioVariance = CalculateVariance(sortedWHRatios);
                
                // 百分位数
                if (sortedHWRatios.Count >= 4)
                {
                    // 90百分位
                    int p90Index = (int)(sortedHWRatios.Count * 0.9);
                    features.VerticalRatio_P90 = sortedHWRatios[p90Index];
                    features.HorizontalRatio_P90 = sortedWHRatios[p90Index];
                    
                    // 75百分位
                    int p75Index = (int)(sortedHWRatios.Count * 0.75);
                    features.VerticalRatio_P75 = sortedHWRatios[p75Index];
                    features.HorizontalRatio_P75 = sortedWHRatios[p75Index];
                    
                    // 50百分位（中位数）
                    int p50Index = (int)(sortedHWRatios.Count * 0.5);
                    features.VerticalRatio_P50 = sortedHWRatios[p50Index];
                    features.HorizontalRatio_P50 = sortedWHRatios[p50Index];
                }
                else
                {
                    // 如果样本太少，使用合理的默认值
                    features.VerticalRatio_P90 = Math.Max(7.0, features.MedianHeightWidthRatio * 2);
                    features.VerticalRatio_P75 = Math.Max(5.0, features.MedianHeightWidthRatio * 1.5);
                    features.VerticalRatio_P50 = Math.Max(1.5, features.MedianHeightWidthRatio);
                    
                    features.HorizontalRatio_P90 = Math.Max(7.0, features.MedianWidthHeightRatio * 2);
                    features.HorizontalRatio_P75 = Math.Max(5.0, features.MedianWidthHeightRatio * 1.5);
                    features.HorizontalRatio_P50 = Math.Max(1.5, features.MedianWidthHeightRatio);
                }
            }
            
            // 基于动态阈值计算文本形状计数
            double verticalThreshold = Math.Max(1.2, features.VerticalRatio_P50 * 0.7); // 动态调整竖排阈值
            double horizontalThreshold = Math.Max(1.2, features.HorizontalRatio_P50 * 0.7); // 动态调整横排阈值
            
            foreach (var cell in cells)
            {
                double hwRatio = cell.location.height / (double)cell.location.width;
                double whRatio = cell.location.width / (double)cell.location.height;
                
                if (hwRatio >= verticalThreshold)
                {
                    features.VerticalTextCount++;
                }
                else if (whRatio >= horizontalThreshold)
                {
                    features.HorizontalTextCount++;
                }
                else
                {
                    features.SquareTextCount++;
                }
            }
            
            // 计算文本块大小统计
            var widths = cells.Select(c => (double)c.location.width).OrderBy(w => w).ToList();
            var heights = cells.Select(c => (double)c.location.height).OrderBy(h => h).ToList();
            
            if (widths.Count > 0 && heights.Count > 0)
            {
                features.MedianWidth = widths[widths.Count / 2];
                features.MedianHeight = heights[heights.Count / 2];
                features.WidthVariance = CalculateVariance(widths);
                features.HeightVariance = CalculateVariance(heights);
            }
        }
        
        /// <summary>
        /// 提取边缘特征
        /// </summary>
        private void ExtractEdgeFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 提取边缘坐标
            foreach (var cell in cells)
            {
                features.LeftEdges.Add(cell.location.left);
                features.RightEdges.Add(cell.location.left + cell.location.width);
                features.TopEdges.Add(cell.location.top);
                features.BottomEdges.Add(cell.location.top + cell.location.height);
            }
            
            // 计算边缘变异
            if (features.LeftEdges.Count > 1)
            {
                features.LeftEdgeVariance = CalculateVariance(features.LeftEdges);
                features.RightEdgeVariance = CalculateVariance(features.RightEdges);
                features.TopEdgeVariance = CalculateVariance(features.TopEdges);
                features.BottomEdgeVariance = CalculateVariance(features.BottomEdges);
                
                // 计算边缘变异比例（避免除零）
                features.LeftRightEdgeVarianceRatio = features.RightEdgeVariance > 0 ? 
                    features.LeftEdgeVariance / features.RightEdgeVariance : 1.0;
                    
                features.TopBottomEdgeVarianceRatio = features.BottomEdgeVariance > 0 ? 
                    features.TopEdgeVariance / features.BottomEdgeVariance : 1.0;
            }
        }
        
        /// <summary>
        /// 提取行列结构特征
        /// </summary>
        private void ExtractRowColumnFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 动态计算分组阈值
            double verticalGroupThreshold = this.groupingThresholdVertical;
            double horizontalGroupThreshold = this.groupingThresholdHorizontal;
            
            // 按水平位置分组形成列
            var columnGroups = cells
                .GroupBy(c => Math.Round(c.location.left / horizontalGroupThreshold) * horizontalGroupThreshold)
                .OrderBy(g => g.Key)
                .ToList();
                
            features.VerticalColumnCount = columnGroups.Count;
            
            // 存储列数据
            foreach (var column in columnGroups)
            {
                features.VerticalColumns.Add(column.ToList());
            }
            
            // 计算列间距
            if (columnGroups.Count >= 2)
            {
                for (int i = 1; i < columnGroups.Count; i++)
                {
                    double currentColCenter = columnGroups[i].Average(c => c.location.left + c.location.width / 2);
                    double prevColCenter = columnGroups[i-1].Average(c => c.location.left + c.location.width / 2);
                    double gap = currentColCenter - prevColCenter;
                    
                    if (gap > 0)
                    {
                        features.ColumnGaps.Add(gap);
                    }
                }
                
                if (features.ColumnGaps.Count > 0)
                {
                    features.MedianColumnGap = features.ColumnGaps.OrderBy(g => g).ElementAt(features.ColumnGaps.Count / 2);
                    features.ColumnGapVariance = CalculateVariance(features.ColumnGaps);
                    
                    // 计算规律性（变异系数：标准差/平均值）
                    double columnGapMean = features.ColumnGaps.Average();
                    if (columnGapMean > 0)
                    {
                        features.ColumnGapRegularity = Math.Sqrt(features.ColumnGapVariance) / columnGapMean;
                    }
                    
                    // 【新增】分析列间距的规律性
                    // 检查列间距是否从右到左递增
                    bool increasingGaps = true;
                    for (int i = 1; i < features.ColumnGaps.Count; i++)
                    {
                        if (features.ColumnGaps[i] < features.ColumnGaps[i-1] * 0.8)
                        {
                            increasingGaps = false;
                            break;
                        }
                    }
                    
                    // 检查列间距是否从右到左递减
                    bool decreasingGaps = true;
                    for (int i = 1; i < features.ColumnGaps.Count; i++)
                    {
                        if (features.ColumnGaps[i] > features.ColumnGaps[i-1] * 1.2)
                        {
                            decreasingGaps = false;
                            break;
                        }
                    }
                    
                    // 列间距从右到左递增，通常暗示从右到左阅读
                    if (increasingGaps && features.ColumnGaps.Count >= 2)
                    {
                        features.RightToLeftEvidence += 2;
                    }
                    // 列间距从右到左递减，通常暗示从左到右阅读
                    else if (decreasingGaps && features.ColumnGaps.Count >= 2)
                    {
                        features.LeftToRightEvidence += 2;
                    }
                }
                
                // 【新增】分析列宽的一致性
                List<double> columnWidths = new List<double>();
                foreach (var column in features.VerticalColumns)
                {
                    double minLeft = column.Min(c => c.location.left);
                    double maxRight = column.Max(c => c.location.left + c.location.width);
                    columnWidths.Add(maxRight - minLeft);
                }
                
                // 计算列宽的变异系数
                if (columnWidths.Count >= 2)
                {
                    double avgWidth = columnWidths.Average();
                    double stdDevWidth = Math.Sqrt(columnWidths.Sum(w => Math.Pow(w - avgWidth, 2)) / columnWidths.Count);
                    double columnWidthCV = stdDevWidth / avgWidth;
                    
                    // 列宽一致性高，增加方向证据
                    if (columnWidthCV < 0.2) // 变异系数小于20%，列宽较一致
                    {
                        // 竖排文本的列宽一致性是重要的方向指标
                        if (features.VerticalTextCount > features.HorizontalTextCount)
                        {
                            // 对于竖排文本，列宽一致通常暗示更规范的排版
                            // 规范排版更可能遵循传统从右到左的阅读顺序
                            features.RightToLeftEvidence += 2;
                        }
                    }
                }
            }
            
            // 按垂直位置分组形成行
            var rowGroups = cells
                .GroupBy(c => Math.Round(c.location.top / verticalGroupThreshold) * verticalGroupThreshold)
                .OrderBy(g => g.Key)
                .ToList();
                
            features.HorizontalRowCount = rowGroups.Count;
            
            // 存储行数据
            foreach (var row in rowGroups)
            {
                features.HorizontalRows.Add(row.ToList());
            }
            
            // 计算行间距
            if (rowGroups.Count >= 2)
            {
                for (int i = 1; i < rowGroups.Count; i++)
                {
                    double currentRowCenter = rowGroups[i].Average(c => c.location.top + c.location.height / 2);
                    double prevRowCenter = rowGroups[i-1].Average(c => c.location.top + c.location.height / 2);
                    double gap = currentRowCenter - prevRowCenter;
                    
                    if (gap > 0)
                    {
                        features.RowGaps.Add(gap);
                    }
                }
                
                if (features.RowGaps.Count > 0)
                {
                    features.MedianRowGap = features.RowGaps.OrderBy(g => g).ElementAt(features.RowGaps.Count / 2);
                    features.RowGapVariance = CalculateVariance(features.RowGaps);
                    
                    // 计算规律性（变异系数）
                    double rowGapMean = features.RowGaps.Average();
                    if (rowGapMean > 0)
                    {
                        features.RowGapRegularity = Math.Sqrt(features.RowGapVariance) / rowGapMean;
                    }
                }
            }
        }
        
        /// <summary>
        /// 提取对齐特征
        /// </summary>
        private void ExtractAlignmentFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 动态确定对齐阈值
            double alignmentThreshold = Math.Min(10.0, features.MedianWidth * 0.1); // 默认10像素或宽度的10%
            
            // 统计左对齐
            var leftGroups = GroupSimilarValues(features.LeftEdges, alignmentThreshold);
            features.LeftAlignedCount = leftGroups.Count > 0 ? leftGroups.Max(g => g.Count) : 0;
            
            // 统计右对齐
            var rightGroups = GroupSimilarValues(features.RightEdges, alignmentThreshold);
            features.RightAlignedCount = rightGroups.Count > 0 ? rightGroups.Max(g => g.Count) : 0;
            
            // 统计顶部对齐
            var topGroups = GroupSimilarValues(features.TopEdges, alignmentThreshold);
            features.TopAlignedCount = topGroups.Count > 0 ? topGroups.Max(g => g.Count) : 0;
            
            // 统计底部对齐
            var bottomGroups = GroupSimilarValues(features.BottomEdges, alignmentThreshold);
            features.BottomAlignedCount = bottomGroups.Count > 0 ? bottomGroups.Max(g => g.Count) : 0;
            
            // 【新增】增强边缘对齐特征分析
            // 分析左右边缘的对齐精确度
            List<double> leftEdgeDeviations = new List<double>();
            List<double> rightEdgeDeviations = new List<double>();
            
            // 计算每个文本块与其最近邻居的边缘对齐偏差
            for (int i = 0; i < cells.Count; i++)
            {
                for (int j = i + 1; j < cells.Count; j++)
                {
                    // 计算左边缘对齐偏差
                    double leftDeviation = Math.Abs(cells[i].location.left - cells[j].location.left);
                    if (leftDeviation < features.MedianWidth * 0.3) // 如果偏差小于宽度的30%，认为有对齐倾向
                    {
                        leftEdgeDeviations.Add(leftDeviation);
                    }
                    
                    // 计算右边缘对齐偏差
                    double rightDeviation = Math.Abs(
                        (cells[i].location.left + cells[i].location.width) - 
                        (cells[j].location.left + cells[j].location.width));
                    if (rightDeviation < features.MedianWidth * 0.3)
                    {
                        rightEdgeDeviations.Add(rightDeviation);
                    }
                }
            }
            
            // 分析边缘对齐的精确度
            if (leftEdgeDeviations.Count > 0)
            {
                features.LeftEdgeAlignmentPrecision = 1.0 - (leftEdgeDeviations.Average() / features.MedianWidth);
            }
            if (rightEdgeDeviations.Count > 0)
            {
                features.RightEdgeAlignmentPrecision = 1.0 - (rightEdgeDeviations.Average() / features.MedianWidth);
            }
            
            // 边缘对齐精确度比较 - 用于方向判断
            if (features.LeftEdgeAlignmentPrecision > 0 && features.RightEdgeAlignmentPrecision > 0)
            {
                if (features.LeftEdgeAlignmentPrecision > features.RightEdgeAlignmentPrecision * 1.2)
                {
                    features.LeftToRightEvidence += 2;
                }
                else if (features.RightEdgeAlignmentPrecision > features.LeftEdgeAlignmentPrecision * 1.2)
                {
                    features.RightToLeftEvidence += 2;
                }
            }
        }
        
        /// <summary>
        /// 提取段落特征
        /// </summary>
        private void ExtractParagraphFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            if (cells.Count < 3)
            {
                features.ParagraphCount = 1;
                return;
            }
            
            // 按垂直位置排序
            var sortedCells = cells.OrderBy(c => c.location.top).ToList();
            
            // 计算段落间距阈值 - 动态计算
            features.ParagraphGapThreshold = features.MedianHeight * 1.5; // 默认为中位数高度的1.5倍
            
            // 统计段落数量
            int paragraphCount = 1;
            bool hasConsistentParagraphGaps = true; // 段落间距是否一致
            double previousGap = 0;
            
            for (int i = 1; i < sortedCells.Count; i++)
            {
                double gap = sortedCells[i].location.top - (sortedCells[i-1].location.top + sortedCells[i-1].location.height);
                
                if (gap > features.ParagraphGapThreshold)
                {
                    paragraphCount++;
                    features.ParagraphGaps.Add(gap);
                    
                    // 检查段落间距是否一致
                    if (previousGap > 0)
                    {
                        // 如果段落间距差异超过30%，认为不一致
                        if (Math.Abs(gap - previousGap) / previousGap > 0.3)
                        {
                            hasConsistentParagraphGaps = false;
                        }
                    }
                    previousGap = gap;
                }
            }
            
            features.ParagraphCount = paragraphCount;
            
            // 判断是否为连续段落布局
            // 连续段落布局的条件：
            // 1. 有多个段落
            // 2. 段落间距相对一致
            // 3. 段落数量大于等于3，更可靠
            if (paragraphCount >= 2 && hasConsistentParagraphGaps)
            {
                features.IsSequentialParagraphs = true;
            }
        }
        
        /// <summary>
        /// 提取内容分布特征
        /// </summary>
        private void ExtractContentDistributionFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 页面范围已在ExtractPageBoundaryFeatures中计算
            
            // 计算内容面积和位置
            double totalArea = 0;
            double leftHalfArea = 0;
            double rightHalfArea = 0;
            double topHalfArea = 0;
            double bottomHalfArea = 0;
            
            foreach (var cell in cells)
            {
                double area = cell.location.width * cell.location.height;
                totalArea += area;
                
                // 计算中心点
                double centerX = cell.location.left + cell.location.width / 2;
                double centerY = cell.location.top + cell.location.height / 2;
                
                // 按位置分配面积
                if (centerX < features.PageCenter_X)
                {
                    leftHalfArea += area;
                }
                else
                {
                    rightHalfArea += area;
                }
                
                if (centerY < features.PageCenter_Y)
                {
                    topHalfArea += area;
                }
                else
                {
                    bottomHalfArea += area;
                }
            }
            
            // 计算内容密度比例
            if (totalArea > 0)
            {
                features.ContentDensityLeftHalf = leftHalfArea / totalArea;
                features.ContentDensityRightHalf = rightHalfArea / totalArea;
                features.ContentDensityTopHalf = topHalfArea / totalArea;
                features.ContentDensityBottomHalf = bottomHalfArea / totalArea;
            }
            
            // 【新增】文本块分布模式分析
            // 将页面水平方向划分为多个区域，分析文本块分布
            int horizontalSections = 5; // 水平方向划分为5个区域
            int[] sectionCounts = new int[horizontalSections];
            double sectionWidth = features.PageWidth / horizontalSections;
            
            foreach (var cell in cells)
            {
                double centerX = cell.location.left + cell.location.width / 2;
                int sectionIndex = Math.Min(horizontalSections - 1, 
                    (int)((centerX - features.PageLeft) / sectionWidth));
                sectionCounts[sectionIndex]++;
            }
            
            // 分析分布趋势
            bool increasingTrend = true; // 从左到右递增
            bool decreasingTrend = true; // 从左到右递减
            
            for (int i = 1; i < horizontalSections; i++)
            {
                if (sectionCounts[i] < sectionCounts[i-1])
                {
                    increasingTrend = false;
                }
                if (sectionCounts[i] > sectionCounts[i-1])
                {
                    decreasingTrend = false;
                }
            }
            
            // 根据分布趋势添加方向证据
            if (increasingTrend && sectionCounts[horizontalSections-1] > sectionCounts[0] * 1.5)
            {
                features.LeftToRightEvidence += 2;
            }
            else if (decreasingTrend && sectionCounts[0] > sectionCounts[horizontalSections-1] * 1.5)
            {
                features.RightToLeftEvidence += 2;
            }
        }

        /// <summary>
        /// 计算方差
        /// </summary>
        private double CalculateVariance(List<double> values)
        {
            if (values.Count <= 1) return 0;

            double avg = values.Average();
            double sumOfSquaredDifferences = values.Sum(x => (x - avg) * (x - avg));
            return sumOfSquaredDifferences / values.Count;
        }
        
        /// <summary>
        /// 提取直方图特征 - 分析文本块在水平和垂直方向的分布
        /// </summary>
        private void ExtractHistogramFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            if (cells.Count < 3) return; // 至少需要3个文本块才能进行有效的直方图分析
            
            // 计算页面范围
            double pageWidth = features.PageRight - features.PageLeft;
            double pageHeight = features.PageBottom - features.PageTop;
            
            // 1. 确定合适的bin大小
            // 使用文本块的平均尺寸作为参考，以捕捉行列结构
            double avgWidth = cells.Average(c => c.location.width);
            double avgHeight = cells.Average(c => c.location.height);
            
            // 【新增】计算文本密度 - 文本块数量与页面面积的比值
            double textDensity = cells.Count / (pageWidth * pageHeight) * 10000; // 缩放因子，避免数值过小
            
            // 【新增】基于文本密度的自适应因子
            // 文本密度越高，bin越小（分辨率越高）；文本密度越低，bin越大（避免过度分散）
            double densityFactor = Math.Min(1.0, Math.Max(0.3, 0.6 / Math.Sqrt(Math.Max(0.1, textDensity))));
            System.Diagnostics.Debug.WriteLine($"文本密度: {textDensity:F1}, 密度调整因子: {densityFactor:F2}");
            
            // bin大小设置为平均尺寸的1/2到1/3，以获得足够的精度，并应用密度调整
            features.HorizontalBinSize = Math.Max(5, avgHeight / 2.5 * densityFactor);
            features.VerticalBinSize = Math.Max(5, avgWidth / 2.5 * densityFactor);
            
            // 【新增】根据文本块形状特征进一步调整bin大小
            if (features.ValidTextBlocks > 0)
            {
                // 如果存在大量竖排文本（高宽比大），增加水平方向的分辨率（减小bin大小）
                double verticalTextRatio = features.VerticalTextCount / (double)features.ValidTextBlocks;
                if (verticalTextRatio > 0.6)
                {
                    features.HorizontalBinSize *= Math.Max(0.6, 1.0 - verticalTextRatio * 0.4);
                    System.Diagnostics.Debug.WriteLine($"检测到大量竖排文本，调整水平bin大小至{features.HorizontalBinSize:F1}");
                }
                
                // 如果存在大量横排文本（宽高比大），增加垂直方向的分辨率（减小bin大小）
                double horizontalTextRatio = features.HorizontalTextCount / (double)features.ValidTextBlocks;
                if (horizontalTextRatio > 0.6)
                {
                    features.VerticalBinSize *= Math.Max(0.6, 1.0 - horizontalTextRatio * 0.4);
                    System.Diagnostics.Debug.WriteLine($"检测到大量横排文本，调整垂直bin大小至{features.VerticalBinSize:F1}");
                }
            }
            
            System.Diagnostics.Debug.WriteLine($"直方图分析 - 水平bin大小: {features.HorizontalBinSize:F1}, 垂直bin大小: {features.VerticalBinSize:F1}");
            
            // 2. 计算需要的bin数量
            int horizontalBinCount = (int)Math.Ceiling(features.PageHeight / features.HorizontalBinSize);
            int verticalBinCount = (int)Math.Ceiling(features.PageWidth / features.VerticalBinSize);
            
            // 初始化直方图数组
            features.HorizontalHistogram = new List<int>(new int[horizontalBinCount]);
            features.VerticalHistogram = new List<int>(new int[verticalBinCount]);
            
            // 3. 构建直方图
            foreach (var cell in cells)
            {
                // 计算文本块中心点
                double centerX = cell.location.left + cell.location.width / 2;
                double centerY = cell.location.top + cell.location.height / 2;
                
                // 计算对应的bin索引
                int verticalBin = (int)((centerX - features.PageLeft) / features.VerticalBinSize);
                int horizontalBin = (int)((centerY - features.PageTop) / features.HorizontalBinSize);
                
                // 确保索引在有效范围内
                verticalBin = Math.Max(0, Math.Min(verticalBinCount - 1, verticalBin));
                horizontalBin = Math.Max(0, Math.Min(horizontalBinCount - 1, horizontalBin));
                
                // 增加对应bin的计数
                features.VerticalHistogram[verticalBin]++;
                features.HorizontalHistogram[horizontalBin]++;
            }
            
            // 4. 平滑直方图，减少噪声影响
            features.VerticalHistogram = SmoothHistogram(features.VerticalHistogram);
            features.HorizontalHistogram = SmoothHistogram(features.HorizontalHistogram);
            
            // 5. 检测峰值
            DetectHistogramPeaks(features.VerticalHistogram, features.VerticalBinSize, features.VerticalPeakIndices);
            DetectHistogramPeaks(features.HorizontalHistogram, features.HorizontalBinSize, features.HorizontalPeakIndices);
            
            // 6. 分析峰值特征
            AnalyzeHistogramPeaks(features);
            
            // 7. 计算直方图得分
            CalculateHistogramScores(features);
            
            System.Diagnostics.Debug.WriteLine($"直方图分析结果 - 列数: {features.VerticalPeakCount}, 行数: {features.HorizontalPeakCount}");
            System.Diagnostics.Debug.WriteLine($"列规律性: {features.VerticalPeakRegularity:F2}, 行规律性: {features.HorizontalPeakRegularity:F2}");
            System.Diagnostics.Debug.WriteLine($"列结构得分: {features.HistogramColumnScore}, 行结构得分: {features.HistogramRowScore}");
        }
        
        /// <summary>
        /// 平滑直方图，减少噪声影响
        /// </summary>
        private List<int> SmoothHistogram(List<int> histogram)
        {
            if (histogram.Count <= 3) return histogram; // 太短无需平滑
            
            List<int> smoothed = new List<int>(new int[histogram.Count]);
            
            // 使用简单的移动平均平滑
            for (int i = 0; i < histogram.Count; i++)
            {
                int sum = histogram[i];
                int count = 1;
                
                // 考虑前一个bin
                if (i > 0)
                {
                    sum += histogram[i - 1];
                    count++;
                }
                
                // 考虑后一个bin
                if (i < histogram.Count - 1)
                {
                    sum += histogram[i + 1];
                    count++;
                }
                
                // 计算平均值
                smoothed[i] = (int)Math.Round((double)sum / count);
            }
            
            return smoothed;
        }
        
        /// <summary>
        /// 检测直方图中的峰值
        /// </summary>
        private void DetectHistogramPeaks(List<int> histogram, double binSize, List<int> peakIndices)
        {
            if (histogram.Count < 3) return;
            
            peakIndices.Clear();
            
            // 计算平均高度，用于设置最小峰值高度阈值
            double avgHeight = histogram.Average();
            double minPeakHeight = Math.Max(2, avgHeight * 1.2); // 峰值至少比平均高度高20%
            
            // 最小峰值间距(以bin数量计)，避免检测到太接近的峰值
            int minPeakDistance = Math.Max(2, (int)(binSize * 1.5 / binSize));
            
            // 【新增】用于存储峰值相关信息
            List<int> potentialPeaks = new List<int>();
            List<double> peakHeights = new List<double>();
            List<double> peakWidths = new List<double>();
            List<double> peakValleyRatios = new List<double>();
            
            // 初步检测峰值 - 一个点如果比其左右邻居都高，则认为是潜在峰值
            for (int i = 1; i < histogram.Count - 1; i++)
            {
                if (histogram[i] > minPeakHeight && 
                    histogram[i] > histogram[i - 1] && 
                    histogram[i] >= histogram[i + 1])
                {
                    potentialPeaks.Add(i);
                    peakHeights.Add(histogram[i]);
                }
            }
            
            // 【新增】对每个潜在峰值进行质量评估
            for (int i = 0; i < potentialPeaks.Count; i++)
            {
                int peakIndex = potentialPeaks[i];
                double peakHeight = peakHeights[i];
                
                // 1. 计算峰值宽度 - 找到左右两侧第一个低于峰高一半的点
                int leftWidth = 0;
                int rightWidth = 0;
                double halfHeight = peakHeight / 2;
                
                // 向左寻找
                for (int j = peakIndex - 1; j >= 0; j--)
                {
                    if (histogram[j] < halfHeight)
                    {
                        leftWidth = peakIndex - j;
                        break;
                    }
                    // 防止遍历到边界但仍未找到半高点
                    if (j == 0)
                    {
                        leftWidth = peakIndex;
                    }
                }
                
                // 向右寻找
                for (int j = peakIndex + 1; j < histogram.Count; j++)
                {
                    if (histogram[j] < halfHeight)
                    {
                        rightWidth = j - peakIndex;
                        break;
                    }
                    // 防止遍历到边界但仍未找到半高点
                    if (j == histogram.Count - 1)
                    {
                        rightWidth = j - peakIndex;
                    }
                }
                
                // 总宽度 = 左宽度 + 右宽度
                double totalWidth = leftWidth + rightWidth;
                peakWidths.Add(totalWidth);
                
                // 2. 计算峰谷比 - 峰值高度与左右相邻谷值的平均高度之比
                double leftValleyHeight = double.MaxValue;
                double rightValleyHeight = double.MaxValue;
                
                // 找左侧谷值
                for (int j = peakIndex - 1; j > 0; j--)
                {
                    if (histogram[j] < histogram[j - 1] && histogram[j] < histogram[j + 1])
                    {
                        leftValleyHeight = histogram[j];
                        break;
                    }
                    // 如果没有找到明确的谷值点，使用最小值
                    if (j == 1)
                    {
                        leftValleyHeight = Math.Min(histogram[0], histogram[1]);
                    }
                }
                
                // 找右侧谷值
                for (int j = peakIndex + 1; j < histogram.Count - 1; j++)
                {
                    if (histogram[j] < histogram[j - 1] && histogram[j] < histogram[j + 1])
                    {
                        rightValleyHeight = histogram[j];
                        break;
                    }
                    // 如果没有找到明确的谷值点，使用最小值
                    if (j == histogram.Count - 2)
                    {
                        rightValleyHeight = Math.Min(histogram[histogram.Count - 1], histogram[histogram.Count - 2]);
                    }
                }
                
                // 确保谷值合理
                if (leftValleyHeight == double.MaxValue) leftValleyHeight = 0;
                if (rightValleyHeight == double.MaxValue) rightValleyHeight = 0;
                
                // 计算峰谷比
                double avgValleyHeight = (leftValleyHeight + rightValleyHeight) / 2;
                if (avgValleyHeight > 0)
                {
                    peakValleyRatios.Add(peakHeight / avgValleyHeight);
                }
                else
                {
                    // 避免除零
                    peakValleyRatios.Add(peakHeight > 0 ? 10.0 : 1.0);
                }
            }
            
            // 根据峰值质量进行过滤和选择
            List<int> finalPeakIndices = new List<int>();
            List<double> finalPeakWidths = new List<double>();
            List<double> finalPeakValleyRatios = new List<double>();
            
            // 先按峰谷比排序，优先选择峰谷比高的峰
            var sortedPeaks = potentialPeaks.Select((index, i) => new { Index = index, Height = peakHeights[i], Width = peakWidths[i], ValleyRatio = peakValleyRatios[i] })
                                          .OrderByDescending(p => p.ValleyRatio)
                                          .ThenByDescending(p => p.Height)
                                          .ToList();
            
            foreach (var peak in sortedPeaks)
            {
                // 检查是否与已选择的峰值距离太近
                bool tooClose = false;
                foreach (int existingPeak in finalPeakIndices)
                {
                    if (Math.Abs(peak.Index - existingPeak) < minPeakDistance)
                    {
                        tooClose = true;
                        break;
                    }
                }
                
                if (!tooClose)
                {
                    finalPeakIndices.Add(peak.Index);
                    finalPeakWidths.Add(peak.Width);
                    finalPeakValleyRatios.Add(peak.ValleyRatio);
                }
            }
            
            // 返回结果 - 按位置排序
            var orderedResults = finalPeakIndices.Select((index, i) => new { Index = index, Width = finalPeakWidths[i], ValleyRatio = finalPeakValleyRatios[i] })
                                              .OrderBy(p => p.Index)
                                              .ToList();
            
            // 清除原有列表并填充排序后的结果
            peakIndices.Clear();
            foreach (var peak in orderedResults)
            {
                peakIndices.Add(peak.Index);
            }
            
            // 【注意】这里只返回了峰值索引，峰值宽度和峰谷比需要在AnalyzeHistogramPeaks中处理
            // 因为该方法无法直接向外部返回峰值宽度和峰谷比信息
        }
        
        /// <summary>
        /// 分析直方图峰值特征
        /// </summary>
        private void AnalyzeHistogramPeaks(TextLayoutFeatures features)
        {
            // 设置峰值数量
            features.VerticalPeakCount = features.VerticalPeakIndices.Count;
            features.HorizontalPeakCount = features.HorizontalPeakIndices.Count;
            
            // 【新增】重新分析峰值，获取峰值宽度和峰谷比
            if (features.VerticalHistogram.Count > 0)
            {
                CalculatePeakQualityMetrics(
                    features.VerticalHistogram, 
                    features.VerticalPeakIndices, 
                    features.VerticalPeakWidths, 
                    features.VerticalPeakValleyRatios,
                    out double meanWidth,
                    out double meanValleyRatio,
                    out double shapeQuality);
                
                features.VerticalPeakMeanWidth = meanWidth;
                features.VerticalPeakValleyMeanRatio = meanValleyRatio;
                features.VerticalPeakShapeQuality = shapeQuality;
            }
            
            if (features.HorizontalHistogram.Count > 0)
            {
                CalculatePeakQualityMetrics(
                    features.HorizontalHistogram, 
                    features.HorizontalPeakIndices, 
                    features.HorizontalPeakWidths, 
                    features.HorizontalPeakValleyRatios,
                    out double meanWidth,
                    out double meanValleyRatio,
                    out double shapeQuality);
                
                features.HorizontalPeakMeanWidth = meanWidth;
                features.HorizontalPeakValleyMeanRatio = meanValleyRatio;
                features.HorizontalPeakShapeQuality = shapeQuality;
            }
            
            // 分析垂直方向(列)峰值
            if (features.VerticalPeakIndices.Count >= 2)
            {
                // 计算峰值间距
                List<double> peakDistances = new List<double>();
                for (int i = 1; i < features.VerticalPeakIndices.Count; i++)
                {
                    peakDistances.Add(features.VerticalPeakIndices[i] - features.VerticalPeakIndices[i - 1]);
                }
                
                // 计算峰值规律性 - 使用变异系数(标准差/均值)的倒数
                // 变异系数越小，规律性越高
                if (peakDistances.Count > 0)
                {
                    double avgDistance = peakDistances.Average();
                    if (avgDistance > 0)
                    {
                        double stdDev = CalculateStandardDeviation(peakDistances);
                        double cv = stdDev / avgDistance; // 变异系数
                        
                        // 规律性 = 1 / (1 + 变异系数)，范围在0-1之间，越大越规律
                        features.VerticalPeakRegularity = 1.0 / (1.0 + cv);
                    }
                }
                
                // 计算峰值强度 - 峰值高度与平均高度的比值
                double avgHeight = features.VerticalHistogram.Average();
                double peakHeightSum = 0;
                foreach (int peakIndex in features.VerticalPeakIndices)
                {
                    peakHeightSum += features.VerticalHistogram[peakIndex];
                }
                
                if (avgHeight > 0 && features.VerticalPeakIndices.Count > 0)
                {
                    double avgPeakHeight = peakHeightSum / features.VerticalPeakIndices.Count;
                    features.VerticalPeakStrength = avgPeakHeight / avgHeight;
                }
            }
            
            // 分析水平方向(行)峰值
            if (features.HorizontalPeakIndices.Count >= 2)
            {
                // 计算峰值间距
                List<double> peakDistances = new List<double>();
                for (int i = 1; i < features.HorizontalPeakIndices.Count; i++)
                {
                    peakDistances.Add(features.HorizontalPeakIndices[i] - features.HorizontalPeakIndices[i - 1]);
                }
                
                // 计算峰值规律性
                if (peakDistances.Count > 0)
                {
                    double avgDistance = peakDistances.Average();
                    if (avgDistance > 0)
                    {
                        double stdDev = CalculateStandardDeviation(peakDistances);
                        double cv = stdDev / avgDistance;
                        features.HorizontalPeakRegularity = 1.0 / (1.0 + cv);
                    }
                }
                
                // 计算峰值强度
                double avgHeight = features.HorizontalHistogram.Average();
                double peakHeightSum = 0;
                foreach (int peakIndex in features.HorizontalPeakIndices)
                {
                    peakHeightSum += features.HorizontalHistogram[peakIndex];
                }
                
                if (avgHeight > 0 && features.HorizontalPeakIndices.Count > 0)
                {
                    double avgPeakHeight = peakHeightSum / features.HorizontalPeakIndices.Count;
                    features.HorizontalPeakStrength = avgPeakHeight / avgHeight;
                }
            }
            
            // 【新增】计算结构强度 - 综合考虑峰值数量、规律性、峰谷比和强度
            CalculateStructureStrength(features);
        }
        
        /// <summary>
        /// 计算峰值质量指标 - 峰值宽度和峰谷比
        /// </summary>
        private void CalculatePeakQualityMetrics(
            List<int> histogram, 
            List<int> peakIndices, 
            List<double> peakWidths, 
            List<double> peakValleyRatios,
            out double meanWidth,
            out double meanValleyRatio,
            out double shapeQuality)
        {
            // 清空现有列表
            peakWidths.Clear();
            peakValleyRatios.Clear();
            
            meanWidth = 0;
            meanValleyRatio = 1.0;
            shapeQuality = 0.5;
            
            if (peakIndices.Count == 0 || histogram.Count < 3)
                return;

            // 计算每个峰值的宽度和峰谷比
            foreach (int peakIndex in peakIndices)
            {
                if (peakIndex <= 0 || peakIndex >= histogram.Count - 1)
                    continue;
                
                double peakHeight = histogram[peakIndex];
                
                // 1. 计算峰值宽度 - 找到左右两侧第一个低于峰高一半的点
                int leftWidth = 0;
                int rightWidth = 0;
                double halfHeight = peakHeight / 2;
                
                // 向左寻找
                for (int j = peakIndex - 1; j >= 0; j--)
                {
                    if (histogram[j] < halfHeight)
                    {
                        leftWidth = peakIndex - j;
                        break;
                    }
                    // 防止遍历到边界但仍未找到半高点
                    if (j == 0)
                    {
                        leftWidth = peakIndex;
                    }
                }
                
                // 向右寻找
                for (int j = peakIndex + 1; j < histogram.Count; j++)
                {
                    if (histogram[j] < halfHeight)
                    {
                        rightWidth = j - peakIndex;
                        break;
                    }
                    // 防止遍历到边界但仍未找到半高点
                    if (j == histogram.Count - 1)
                    {
                        rightWidth = j - peakIndex;
                    }
                }
                
                // 总宽度 = 左宽度 + 右宽度
                double totalWidth = leftWidth + rightWidth;
                peakWidths.Add(totalWidth);

                // 2. 计算峰谷比 - 峰值高度与左右相邻谷值的平均高度之比
                double leftValleyHeight = double.MaxValue;
                double rightValleyHeight = double.MaxValue;
                
                // 找左侧谷值
                for (int j = peakIndex - 1; j > 0; j--)
                {
                    if (histogram[j] < histogram[j - 1] && histogram[j] < histogram[j + 1])
                    {
                        leftValleyHeight = histogram[j];
                        break;
                    }
                    // 如果没有找到明确的谷值点，使用最小值
                    if (j == 1)
                    {
                        leftValleyHeight = Math.Min(histogram[0], histogram[1]);
                    }
                }
                
                // 找右侧谷值
                for (int j = peakIndex + 1; j < histogram.Count - 1; j++)
                {
                    if (histogram[j] < histogram[j - 1] && histogram[j] < histogram[j + 1])
                    {
                        rightValleyHeight = histogram[j];
                        break;
                    }
                    // 如果没有找到明确的谷值点，使用最小值
                    if (j == histogram.Count - 2)
                    {
                        rightValleyHeight = Math.Min(histogram[histogram.Count - 1], histogram[histogram.Count - 2]);
                    }
                }
                
                // 确保谷值合理
                if (leftValleyHeight == double.MaxValue) leftValleyHeight = 0;
                if (rightValleyHeight == double.MaxValue) rightValleyHeight = 0;
                
                // 计算峰谷比
                double avgValleyHeight = (leftValleyHeight + rightValleyHeight) / 2;
                if (avgValleyHeight > 0)
                {
                    peakValleyRatios.Add(peakHeight / avgValleyHeight);
                }
                else
                {
                    // 避免除零
                    peakValleyRatios.Add(peakHeight > 0 ? 10.0 : 1.0);
                }
            }
            
            // 计算平均值
            if (peakWidths.Count > 0)
            {
                meanWidth = peakWidths.Average();
            }
            
            if (peakValleyRatios.Count > 0)
            {
                meanValleyRatio = peakValleyRatios.Average();
                
                // 计算峰值形状质量分数 (0-1)
                // 峰谷比越高，形状质量越好；同时考虑宽度相对均匀性
                double valleyRatioScore = Math.Min(1.0, meanValleyRatio / 5.0); // 峰谷比达到5或以上视为满分
                
                double widthVariability = 0;
                if (peakWidths.Count > 1)
                {
                    double stdDevWidth = CalculateStandardDeviation(peakWidths);
                    double meanWidth2 = peakWidths.Average();
                    if (meanWidth2 > 0)
                    {
                        widthVariability = stdDevWidth / meanWidth2;
                    }
                }
                
                // 宽度变异性得分（越均匀越高分）
                double widthUniformityScore = 1.0 / (1.0 + widthVariability);
                
                // 综合评分
                shapeQuality = 0.7 * valleyRatioScore + 0.3 * widthUniformityScore;
            }
        }
        
        /// <summary>
        /// 计算结构强度 - 用于动态权重调整
        /// </summary>
        private void CalculateStructureStrength(TextLayoutFeatures features)
        {
            // 垂直结构强度计算
            double verticalStrength = 0.0;
            if (features.VerticalPeakCount >= 2)
            {
                // 基于峰值数量、规律性、峰谷比和峰值强度计算
                double countScore = Math.Min(1.0, features.VerticalPeakCount / 5.0); // 最多5列视为满分
                double regularityScore = features.VerticalPeakRegularity;
                double valleyRatioScore = Math.Min(1.0, features.VerticalPeakValleyMeanRatio / 5.0);
                double strengthScore = Math.Min(1.0, features.VerticalPeakStrength / 3.0);
                
                // 加权平均
                verticalStrength = 0.3 * countScore + 0.3 * regularityScore + 0.2 * valleyRatioScore + 0.2 * strengthScore;
            }
            
            // 水平结构强度计算
            double horizontalStrength = 0.0;
            if (features.HorizontalPeakCount >= 2)
            {
                // 行数通常比列数多，用8作为满分基准
                double countScore = Math.Min(1.0, features.HorizontalPeakCount / 8.0);
                double regularityScore = features.HorizontalPeakRegularity;
                double valleyRatioScore = Math.Min(1.0, features.HorizontalPeakValleyMeanRatio / 5.0);
                double strengthScore = Math.Min(1.0, features.HorizontalPeakStrength / 3.0);
                
                // 加权平均
                horizontalStrength = 0.3 * countScore + 0.3 * regularityScore + 0.2 * valleyRatioScore + 0.2 * strengthScore;
            }
            
            // 设置结构强度
            features.VerticalStructureStrength = verticalStrength;
            features.HorizontalStructureStrength = horizontalStrength;
            
            // 计算方向一致性 - 判断是否有一个方向明显强于另一个方向
            if (verticalStrength > 0 || horizontalStrength > 0)
            {
                double maxStrength = Math.Max(verticalStrength, horizontalStrength);
                double minStrength = Math.Min(verticalStrength, horizontalStrength);
                
                if (maxStrength > 0)
                {
                    // 方向一致性 = 最大强度方向相对于最小强度方向的优势度
                    // 当两个方向强度接近时，一致性较低；当一个方向明显强于另一个方向时，一致性高
                    features.DirectionalConsistency = maxStrength / (maxStrength + Math.Max(0.1, minStrength));
                }
            }
        }
        
        /// <summary>
        /// 计算直方图结构得分
        /// </summary>
        private void CalculateHistogramScores(TextLayoutFeatures features)
        {
            // 初始化得分
            int columnScore = 0;
            int rowScore = 0;
            
            // 1. 基于峰值数量的得分
            // 竖排布局通常有多列结构
            if (features.VerticalPeakCount >= 3)
            {
                columnScore += 8; // 3列或以上，强烈暗示竖排
                System.Diagnostics.Debug.WriteLine($"检测到{features.VerticalPeakCount}列，列结构得分 +8");
            }
            else if (features.VerticalPeakCount == 2)
            {
                columnScore += 5; // 2列，中等暗示竖排
                System.Diagnostics.Debug.WriteLine($"检测到2列，列结构得分 +5");
            }
            
            // 横排布局通常有多行结构
            if (features.HorizontalPeakCount >= 5)
            {
                rowScore += 8; // 5行或以上，强烈暗示横排
                System.Diagnostics.Debug.WriteLine($"检测到{features.HorizontalPeakCount}行，行结构得分 +8");
            }
            else if (features.HorizontalPeakCount >= 3)
            {
                rowScore += 5; // 3-4行，中等暗示横排
                System.Diagnostics.Debug.WriteLine($"检测到{features.HorizontalPeakCount}行，行结构得分 +5");
            }
            
            // 2. 基于峰值规律性的得分
            // 规律性高的结构更可能是有意义的排版
            if (features.VerticalPeakRegularity > 0.8 && features.VerticalPeakCount >= 2)
            {
                // 【修改】增加规律性权重
                int regScore = (int)(8 * features.VerticalPeakRegularity);
                columnScore += regScore;
                System.Diagnostics.Debug.WriteLine($"列间距非常规律(规律性={features.VerticalPeakRegularity:F2})，列结构得分 +{regScore}");
            }
            else if (features.VerticalPeakRegularity > 0.6 && features.VerticalPeakCount >= 2)
            {
                // 【修改】增加规律性权重
                int regScore = (int)(6 * features.VerticalPeakRegularity);
                columnScore += regScore;
                System.Diagnostics.Debug.WriteLine($"列间距较规律(规律性={features.VerticalPeakRegularity:F2})，列结构得分 +{regScore}");
            }
            
            if (features.HorizontalPeakRegularity > 0.8 && features.HorizontalPeakCount >= 2)
            {
                // 【修改】增加规律性权重
                int regScore = (int)(8 * features.HorizontalPeakRegularity);
                rowScore += regScore;
                System.Diagnostics.Debug.WriteLine($"行间距非常规律(规律性={features.HorizontalPeakRegularity:F2})，行结构得分 +{regScore}");
            }
            else if (features.HorizontalPeakRegularity > 0.6 && features.HorizontalPeakCount >= 2)
            {
                // 【修改】增加规律性权重
                int regScore = (int)(6 * features.HorizontalPeakRegularity);
                rowScore += regScore;
                System.Diagnostics.Debug.WriteLine($"行间距较规律(规律性={features.HorizontalPeakRegularity:F2})，行结构得分 +{regScore}");
            }
            
            // 3. 基于峰值强度的得分
            // 峰值越明显，结构越清晰
            if (features.VerticalPeakStrength > 2.0 && features.VerticalPeakCount >= 2)
            {
                // 【修改】增加强度权重
                int strengthScore = (int)(7 * Math.Min(3.0, features.VerticalPeakStrength) / 3.0);
                columnScore += strengthScore;
                System.Diagnostics.Debug.WriteLine($"列结构非常明显(强度={features.VerticalPeakStrength:F2})，列结构得分 +{strengthScore}");
            }
            
            if (features.HorizontalPeakStrength > 2.0 && features.HorizontalPeakCount >= 2)
            {
                // 【修改】增加强度权重
                int strengthScore = (int)(7 * Math.Min(3.0, features.HorizontalPeakStrength) / 3.0);
                rowScore += strengthScore;
                System.Diagnostics.Debug.WriteLine($"行结构非常明显(强度={features.HorizontalPeakStrength:F2})，行结构得分 +{strengthScore}");
            }
            
            // 【新增】4. 基于峰谷比的得分
            // 峰谷比越高，表示峰值越清晰
            if (features.VerticalPeakValleyMeanRatio > 3.0 && features.VerticalPeakCount >= 2)
            {
                int valleyScore = (int)(6 * Math.Min(5.0, features.VerticalPeakValleyMeanRatio) / 5.0);
                columnScore += valleyScore;
                System.Diagnostics.Debug.WriteLine($"列峰谷比非常高(比值={features.VerticalPeakValleyMeanRatio:F2})，列结构得分 +{valleyScore}");
            }
            
            if (features.HorizontalPeakValleyMeanRatio > 3.0 && features.HorizontalPeakCount >= 2)
            {
                int valleyScore = (int)(6 * Math.Min(5.0, features.HorizontalPeakValleyMeanRatio) / 5.0);
                rowScore += valleyScore;
                System.Diagnostics.Debug.WriteLine($"行峰谷比非常高(比值={features.HorizontalPeakValleyMeanRatio:F2})，行结构得分 +{valleyScore}");
            }
            
            // 【新增】5. 基于峰值形状质量的得分
            if (features.VerticalPeakShapeQuality > 0.7 && features.VerticalPeakCount >= 2)
            {
                int shapeScore = (int)(5 * features.VerticalPeakShapeQuality);
                columnScore += shapeScore;
                System.Diagnostics.Debug.WriteLine($"列峰值形状质量高(质量={features.VerticalPeakShapeQuality:F2})，列结构得分 +{shapeScore}");
            }
            
            if (features.HorizontalPeakShapeQuality > 0.7 && features.HorizontalPeakCount >= 2)
            {
                int shapeScore = (int)(5 * features.HorizontalPeakShapeQuality);
                rowScore += shapeScore;
                System.Diagnostics.Debug.WriteLine($"行峰值形状质量高(质量={features.HorizontalPeakShapeQuality:F2})，行结构得分 +{shapeScore}");
            }
            
            // 6. 考虑峰值数量比例
            // 如果列数明显多于行数，或行数明显多于列数，进一步增强对应得分
            if (features.VerticalPeakCount > features.HorizontalPeakCount * 1.5 && features.VerticalPeakCount >= 3)
            {
                columnScore += 4;
                System.Diagnostics.Debug.WriteLine($"列数({features.VerticalPeakCount})明显多于行数({features.HorizontalPeakCount})，列结构得分 +4");
            }
            
            if (features.HorizontalPeakCount > features.VerticalPeakCount * 1.5 && features.HorizontalPeakCount >= 3)
            {
                rowScore += 4;
                System.Diagnostics.Debug.WriteLine($"行数({features.HorizontalPeakCount})明显多于列数({features.VerticalPeakCount})，行结构得分 +4");
            }
            
            // 【新增】7. 结构强度极高时的特殊处理
            // 当某个方向的结构强度非常高时，显著提升其权重
            if (features.VerticalStructureStrength > 0.8)
            {
                int boostScore = (int)(20 * (features.VerticalStructureStrength - 0.8) * 5);
                columnScore += boostScore;
                System.Diagnostics.Debug.WriteLine($"列结构强度极高(强度={features.VerticalStructureStrength:F2})，列结构得分额外 +{boostScore}");
                
                // 如果结构强度极端高（接近1），则直接给予非常高的分数
                if (features.VerticalStructureStrength > 0.9)
                {
                    columnScore = Math.Max(columnScore, 50);
                    System.Diagnostics.Debug.WriteLine($"列结构强度接近完美，确保列结构得分至少为50");
                }
            }
            
            if (features.HorizontalStructureStrength > 0.8)
            {
                int boostScore = (int)(20 * (features.HorizontalStructureStrength - 0.8) * 5);
                rowScore += boostScore;
                System.Diagnostics.Debug.WriteLine($"行结构强度极高(强度={features.HorizontalStructureStrength:F2})，行结构得分额外 +{boostScore}");
                
                // 如果结构强度极端高（接近1），则直接给予非常高的分数
                if (features.HorizontalStructureStrength > 0.9)
                {
                    rowScore = Math.Max(rowScore, 50);
                    System.Diagnostics.Debug.WriteLine($"行结构强度接近完美，确保行结构得分至少为50");
                }
            }
            
            // 【新增】8. 方向一致性高时的特殊处理
            // 当方向一致性很高时，进一步增强主导方向的权重
            if (features.DirectionalConsistency > 0.8)
            {
                // 确定主导方向
                if (features.VerticalStructureStrength > features.HorizontalStructureStrength)
                {
                    int consistencyBoost = (int)(10 * (features.DirectionalConsistency - 0.8) * 5);
                    columnScore += consistencyBoost;
                    System.Diagnostics.Debug.WriteLine($"方向一致性高且垂直结构占优(一致性={features.DirectionalConsistency:F2})，列结构得分额外 +{consistencyBoost}");
                }
                else
                {
                    int consistencyBoost = (int)(10 * (features.DirectionalConsistency - 0.8) * 5);
                    rowScore += consistencyBoost;
                    System.Diagnostics.Debug.WriteLine($"方向一致性高且水平结构占优(一致性={features.DirectionalConsistency:F2})，行结构得分额外 +{consistencyBoost}");
                }
            }
            
            // 设置最终得分
            features.HistogramColumnScore = columnScore;
            features.HistogramRowScore = rowScore;
        }

        /// <summary>
        /// 从布局特征中提取方向证据
        /// </summary>
        private DirectionEvidence ExtractDirectionEvidence(TextLayoutFeatures features)
        {
            var evidence = new DirectionEvidence();
            
            // 1. 提取文本块形状特征
            evidence.VerticalTextCount = features.VerticalTextCount;
            evidence.HorizontalTextCount = features.HorizontalTextCount;
            evidence.SquareTextCount = features.SquareTextCount;
            
            // 2. 提取边缘变异特征
            evidence.LeftEdgeVariance = features.LeftEdgeVariance;
            evidence.RightEdgeVariance = features.RightEdgeVariance;
            evidence.TopEdgeVariance = features.TopEdgeVariance;
            evidence.BottomEdgeVariance = features.BottomEdgeVariance;
            
            // 3. 提取行列结构特征
            evidence.HorizontalAlignedRows = features.HorizontalRowCount;
            evidence.VerticalAlignedColumns = features.VerticalColumnCount;
            
            // 4. 提取对齐特征
            evidence.LeftAlignedCount = features.LeftAlignedCount;
            evidence.RightAlignedCount = features.RightAlignedCount;
            evidence.TopAlignedCount = features.TopAlignedCount;
            evidence.BottomAlignedCount = features.BottomAlignedCount;
            
            // 5. 提取段落特征
            evidence.ParagraphCount = features.ParagraphCount;
            evidence.IsSequentialParagraphs = features.IsSequentialParagraphs;
            
            // 6. 提取内容分布特征
            evidence.ContentDensityLeftHalf = features.ContentDensityLeftHalf;
            evidence.ContentDensityRightHalf = features.ContentDensityRightHalf;
            evidence.ContentDensityTopHalf = features.ContentDensityTopHalf;
            evidence.ContentDensityBottomHalf = features.ContentDensityBottomHalf;
            
            // 7. 提取直方图特征
            evidence.HistogramColumnScore = features.HistogramColumnScore;
            evidence.HistogramRowScore = features.HistogramRowScore;
            evidence.VerticalPeakCount = features.VerticalPeakCount;
            evidence.HorizontalPeakCount = features.HorizontalPeakCount;
            evidence.VerticalPeakRegularity = features.VerticalPeakRegularity;
            evidence.HorizontalPeakRegularity = features.HorizontalPeakRegularity;
            
            // 【新增】8. 提取峰值质量特征
            evidence.VerticalPeakValleyMeanRatio = features.VerticalPeakValleyMeanRatio;
            evidence.HorizontalPeakValleyMeanRatio = features.HorizontalPeakValleyMeanRatio;
            evidence.VerticalPeakShapeQuality = features.VerticalPeakShapeQuality;
            evidence.HorizontalPeakShapeQuality = features.HorizontalPeakShapeQuality;
            
            // 【新增】9. 提取结构强度特征
            evidence.VerticalStructureStrength = features.VerticalStructureStrength;
            evidence.HorizontalStructureStrength = features.HorizontalStructureStrength;
            evidence.DirectionalConsistency = features.DirectionalConsistency;
            
            return evidence;
        }
        
        /// <summary>
        /// 确定横排布局的文本流方向
        /// </summary>
        private void DetermineHorizontalFlowDirection(TextDirectionResult result, DirectionEvidence evidence)
        {
            // 默认从左到右
            result.FlowDirection = TextFlowDirection.LeftToRight;
            result.DirectionConfidence = 70; // 默认置信度
            
            System.Diagnostics.Debug.WriteLine("\n--- 横排文本流方向分析 ---");
            
            // 【新增】0. 结构强度特征 - 如果有极高的结构强度，优先考虑
            if (evidence.HorizontalStructureStrength > 0.85)
            {
                // 如果水平结构强度非常高，增强置信度
                result.DirectionConfidence = Math.Max(result.DirectionConfidence, 80);
                System.Diagnostics.Debug.WriteLine($"水平结构强度极高(强度={evidence.HorizontalStructureStrength:F2}) => 增强横排布局置信度至{result.DirectionConfidence}%");
            }
            
            // 1. 内容分布特征
            bool hasContentDistributionEvidence = false;
            if (evidence.ContentDensityRightHalf > evidence.ContentDensityLeftHalf * 1.5)
            {
                // 右半页内容明显多于左半页，可能是从右到左
                result.FlowDirection = TextFlowDirection.RightToLeft;
                result.DirectionConfidence = 75;
                result.IsRightToLeft = true;
                hasContentDistributionEvidence = true;
                System.Diagnostics.Debug.WriteLine($"右半页内容密度明显高于左半页({evidence.ContentDensityRightHalf:F2} vs {evidence.ContentDensityLeftHalf:F2}) => 从右到左，置信度: {result.DirectionConfidence}%");
            }
            else if (evidence.ContentDensityLeftHalf > evidence.ContentDensityRightHalf * 1.5)
            {
                // 左半页内容明显多于右半页，可能是从左到右
                result.FlowDirection = TextFlowDirection.LeftToRight;
                result.DirectionConfidence = 75;
                result.IsRightToLeft = false;
                hasContentDistributionEvidence = true;
                System.Diagnostics.Debug.WriteLine($"左半页内容密度明显高于右半页({evidence.ContentDensityLeftHalf:F2} vs {evidence.ContentDensityRightHalf:F2}) => 从左到右，置信度: {result.DirectionConfidence}%");
            }
            // 【新增】内容分布中等差异的情况
            else if (evidence.ContentDensityRightHalf > evidence.ContentDensityLeftHalf * 1.25)
            {
                // 右半页内容略多于左半页，可能是从右到左
                result.FlowDirection = TextFlowDirection.RightToLeft;
                result.DirectionConfidence = 70;
                result.IsRightToLeft = true;
                hasContentDistributionEvidence = true;
                System.Diagnostics.Debug.WriteLine($"右半页内容密度略高于左半页({evidence.ContentDensityRightHalf:F2} vs {evidence.ContentDensityLeftHalf:F2}) => 倾向于从右到左，置信度: {result.DirectionConfidence}%");
            }
            else if (evidence.ContentDensityLeftHalf > evidence.ContentDensityRightHalf * 1.25)
            {
                // 左半页内容略多于右半页，可能是从左到右
                result.FlowDirection = TextFlowDirection.LeftToRight;
                result.DirectionConfidence = 70;
                result.IsRightToLeft = false;
                hasContentDistributionEvidence = true;
                System.Diagnostics.Debug.WriteLine($"左半页内容密度略高于右半页({evidence.ContentDensityLeftHalf:F2} vs {evidence.ContentDensityRightHalf:F2}) => 倾向于从左到右，置信度: {result.DirectionConfidence}%");
            }
            
            // 2. 边缘对齐特征
            bool hasAlignmentEvidence = false;
            if (evidence.LeftAlignedCount > evidence.RightAlignedCount * 2 && evidence.LeftAlignedCount >= 3)
            {
                // 左对齐明显多于右对齐，强烈暗示从左到右
                result.FlowDirection = TextFlowDirection.LeftToRight;
                result.DirectionConfidence = Math.Max(result.DirectionConfidence, 85);
                result.IsRightToLeft = false;
                hasAlignmentEvidence = true;
                System.Diagnostics.Debug.WriteLine($"左对齐文本块明显多于右对齐({evidence.LeftAlignedCount} vs {evidence.RightAlignedCount}) => 从左到右，置信度: {result.DirectionConfidence}%");
            }
            else if (evidence.RightAlignedCount > evidence.LeftAlignedCount * 2 && evidence.RightAlignedCount >= 3)
            {
                // 右对齐明显多于左对齐，强烈暗示从右到左
                result.FlowDirection = TextFlowDirection.RightToLeft;
                result.DirectionConfidence = Math.Max(result.DirectionConfidence, 85);
                result.IsRightToLeft = true;
                hasAlignmentEvidence = true;
                System.Diagnostics.Debug.WriteLine($"右对齐文本块明显多于左对齐({evidence.RightAlignedCount} vs {evidence.LeftAlignedCount}) => 从右到左，置信度: {result.DirectionConfidence}%");
            }
            // 【新增】边缘对齐中等差异的情况
            else if (evidence.LeftAlignedCount > evidence.RightAlignedCount * 1.5 && evidence.LeftAlignedCount >= 2)
            {
                // 左对齐略多于右对齐，暗示从左到右
                if (!hasAlignmentEvidence || result.FlowDirection == TextFlowDirection.LeftToRight)
                {
                    result.FlowDirection = TextFlowDirection.LeftToRight;
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 75);
                    result.IsRightToLeft = false;
                    hasAlignmentEvidence = true;
                    System.Diagnostics.Debug.WriteLine($"左对齐文本块略多于右对齐({evidence.LeftAlignedCount} vs {evidence.RightAlignedCount}) => 倾向于从左到右，置信度: {result.DirectionConfidence}%");
                }
            }
            else if (evidence.RightAlignedCount > evidence.LeftAlignedCount * 1.5 && evidence.RightAlignedCount >= 2)
            {
                // 右对齐略多于左对齐，暗示从右到左
                if (!hasAlignmentEvidence || result.FlowDirection == TextFlowDirection.RightToLeft)
                {
                    result.FlowDirection = TextFlowDirection.RightToLeft;
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 75);
                    result.IsRightToLeft = true;
                    hasAlignmentEvidence = true;
                    System.Diagnostics.Debug.WriteLine($"右对齐文本块略多于左对齐({evidence.RightAlignedCount} vs {evidence.LeftAlignedCount}) => 倾向于从右到左，置信度: {result.DirectionConfidence}%");
                }
            }
            
            // 3. 边缘变异特征
            bool hasEdgeVarianceEvidence = false;
            if (evidence.LeftEdgeVariance < evidence.RightEdgeVariance * 0.5)
            {
                // 左边缘比右边缘更整齐，暗示从左到右
                if (!hasEdgeVarianceEvidence || result.FlowDirection == TextFlowDirection.LeftToRight)
                {
                    result.FlowDirection = TextFlowDirection.LeftToRight;
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 80);
                    result.IsRightToLeft = false;
                    hasEdgeVarianceEvidence = true;
                    System.Diagnostics.Debug.WriteLine($"左边缘明显比右边缘更整齐(变异:{evidence.LeftEdgeVariance:F2} vs {evidence.RightEdgeVariance:F2}) => 从左到右，置信度: {result.DirectionConfidence}%");
                }
            }
            else if (evidence.RightEdgeVariance < evidence.LeftEdgeVariance * 0.5)
            {
                // 右边缘比左边缘更整齐，暗示从右到左
                if (!hasEdgeVarianceEvidence || result.FlowDirection == TextFlowDirection.RightToLeft)
                {
                    result.FlowDirection = TextFlowDirection.RightToLeft;
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 80);
                    result.IsRightToLeft = true;
                    hasEdgeVarianceEvidence = true;
                    System.Diagnostics.Debug.WriteLine($"右边缘明显比左边缘更整齐(变异:{evidence.RightEdgeVariance:F2} vs {evidence.LeftEdgeVariance:F2}) => 从右到左，置信度: {result.DirectionConfidence}%");
                }
            }
            // 【新增】边缘变异中等差异的情况
            else if (evidence.LeftEdgeVariance < evidence.RightEdgeVariance * 0.7)
            {
                // 左边缘比右边缘略整齐，暗示从左到右
                if (!hasEdgeVarianceEvidence && (!hasAlignmentEvidence || result.FlowDirection == TextFlowDirection.LeftToRight))
                {
                    result.FlowDirection = TextFlowDirection.LeftToRight;
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 70);
                    result.IsRightToLeft = false;
                    hasEdgeVarianceEvidence = true;
                    System.Diagnostics.Debug.WriteLine($"左边缘略比右边缘更整齐(变异:{evidence.LeftEdgeVariance:F2} vs {evidence.RightEdgeVariance:F2}) => 倾向于从左到右，置信度: {result.DirectionConfidence}%");
                }
            }
            else if (evidence.RightEdgeVariance < evidence.LeftEdgeVariance * 0.7)
            {
                // 右边缘比左边缘略整齐，暗示从右到左
                if (!hasEdgeVarianceEvidence && (!hasAlignmentEvidence || result.FlowDirection == TextFlowDirection.RightToLeft))
                {
                    result.FlowDirection = TextFlowDirection.RightToLeft;
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 70);
                    result.IsRightToLeft = true;
                    hasEdgeVarianceEvidence = true;
                    System.Diagnostics.Debug.WriteLine($"右边缘略比左边缘更整齐(变异:{evidence.RightEdgeVariance:F2} vs {evidence.LeftEdgeVariance:F2}) => 倾向于从右到左，置信度: {result.DirectionConfidence}%");
                }
            }
            
            // 【新增】4. 方向一致性特征 - 如果方向一致性高，进一步提升置信度
            if (evidence.DirectionalConsistency > 0.8 && evidence.HorizontalStructureStrength > 0.7)
            {
                int confidenceBoost = (int)(10 * (evidence.DirectionalConsistency - 0.8) * 5);
                result.DirectionConfidence = Math.Min(100, result.DirectionConfidence + confidenceBoost);
                System.Diagnostics.Debug.WriteLine($"方向一致性高(一致性={evidence.DirectionalConsistency:F2}) => 增加置信度{confidenceBoost}点，最终置信度: {result.DirectionConfidence}%");
            }
            
            // 【新增】5. 综合证据分析 - 当证据相互冲突时的处理
            if (hasContentDistributionEvidence && hasAlignmentEvidence && hasEdgeVarianceEvidence)
            {
                // 所有证据都存在，检查是否一致
                bool isContentRTL = evidence.ContentDensityRightHalf > evidence.ContentDensityLeftHalf;
                bool isAlignmentRTL = evidence.RightAlignedCount > evidence.LeftAlignedCount;
                bool isEdgeRTL = evidence.RightEdgeVariance < evidence.LeftEdgeVariance;
                
                // 计算从右到左的证据数量
                int rtlEvidenceCount = (isContentRTL ? 1 : 0) + (isAlignmentRTL ? 1 : 0) + (isEdgeRTL ? 1 : 0);
                
                if (rtlEvidenceCount >= 2)
                {
                    // 多数证据支持从右到左
                    result.FlowDirection = TextFlowDirection.RightToLeft;
                    result.IsRightToLeft = true;
                    // 根据证据一致性增加置信度
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 80 + (rtlEvidenceCount == 3 ? 10 : 0));
                    System.Diagnostics.Debug.WriteLine($"综合分析: {rtlEvidenceCount}/3的证据支持从右到左 => 确定为从右到左，置信度: {result.DirectionConfidence}%");
                }
                else
                {
                    // 多数证据支持从左到右
                    result.FlowDirection = TextFlowDirection.LeftToRight;
                    result.IsRightToLeft = false;
                    // 根据证据一致性增加置信度
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 80 + (rtlEvidenceCount == 0 ? 10 : 0));
                    System.Diagnostics.Debug.WriteLine($"综合分析: {3-rtlEvidenceCount}/3的证据支持从左到右 => 确定为从左到右，置信度: {result.DirectionConfidence}%");
                }
            }
            
            System.Diagnostics.Debug.WriteLine($"最终横排文本流方向: {result.FlowDirection}，置信度: {result.DirectionConfidence}%");
        }
        
        /// <summary>
        /// 确定竖排布局的文本流方向
        /// </summary>
        private void DetermineVerticalFlowDirection(TextDirectionResult result, DirectionEvidence evidence)
        {
            // 默认从上到下
            result.FlowDirection = TextFlowDirection.TopToBottom;
            result.DirectionConfidence = 80; // 竖排默认从上到下的置信度较高
            
            // 【修改】基于左右方向证据计算水平方向
            // 计算左右方向的证据比例
            int totalHorizontalEvidence = evidence.LeftToRightCount + evidence.RightToLeftCount;
            
            // 【新增】对于中文竖排文本，默认偏向从右到左
            // 这是基于中文竖排传统排版规则
            bool isCJKVerticalText = evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5;
            
            // 【新增】基于峰值形状和峰谷比计算直方图结构质量评分
            int histogramStructureScore = 0;
            if (evidence.VerticalPeakCount >= 2)
            {
                // 峰值形状质量评分 (0-10)
                int shapeQualityScore = (int)(evidence.VerticalPeakShapeQuality * 10);
                
                // 峰谷比评分 (0-10)，峰谷比越大表示列结构越清晰
                int valleyRatioScore = (int)(Math.Min(10, evidence.VerticalPeakValleyMeanRatio * 2));
                
                // 列间距规律性评分 (0-10)
                int regularityScore = (int)(evidence.VerticalPeakRegularity * 10);
                
                // 结构强度评分 (0-10)
                int strengthScore = (int)(evidence.VerticalStructureStrength * 10);
                
                // 计算综合得分 - 加权平均
                histogramStructureScore = (int)((shapeQualityScore * 0.3) + 
                                                (valleyRatioScore * 0.3) + 
                                                (regularityScore * 0.2) + 
                                                (strengthScore * 0.2));
                
                System.Diagnostics.Debug.WriteLine($"直方图结构评分 - 形状质量:{shapeQualityScore}, 峰谷比:{valleyRatioScore}, " +
                                                 $"规律性:{regularityScore}, 强度:{strengthScore}, 综合:{histogramStructureScore}");
            }
            
            // 根据直方图结构质量调整基础加分
            int baseRTLBonus = isCJKVerticalText ? 10 : 0; // 中文竖排文本基础加分
            if (histogramStructureScore > 7)
            {
                // 结构非常清晰，显著增加从右到左的可能性
                baseRTLBonus += Math.Min(10, histogramStructureScore - 5); 
                System.Diagnostics.Debug.WriteLine($"列结构非常清晰(评分:{histogramStructureScore})，增加从右到左基础加分至:{baseRTLBonus}");
            }
            
            if (totalHorizontalEvidence > 0)
            {
                // 根据证据比例确定水平方向
                if (evidence.RightToLeftCount > evidence.LeftToRightCount)
                {
                    result.HorizontalDirection = TextFlowDirection.RightToLeft;
                    result.IsRightToLeft = true;
                    
                    // 计算置信度基于证据比例
                    double rtlRatio = (double)evidence.RightToLeftCount / totalHorizontalEvidence;
                    // 【修改】提高基础置信度，并为中文竖排文本增加额外分数
                    result.HorizontalConfidence = (int)(65 + 30 * rtlRatio) + baseRTLBonus; // 65-95之间的置信度，加上可能的额外分数
                    result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence); // 确保不超过95
                    
                    System.Diagnostics.Debug.WriteLine($"基于方向证据比例({rtlRatio:F2})，水平方向为从右到左，置信度: {result.HorizontalConfidence}%");
                }
                else if (evidence.LeftToRightCount > evidence.RightToLeftCount)
                {
                    // 【修改】对于中文竖排文本，即使有从左到右的证据，也要结合直方图特征进行评估
                    if (isCJKVerticalText)
                    {
                        double ltrRatio = (double)evidence.LeftToRightCount / totalHorizontalEvidence;
                        
                        // 如果直方图结构非常明确且从左到右证据不是特别强，仍然倾向于从右到左
                        if (histogramStructureScore >= 6 && ltrRatio < 0.7)
                        {
                            result.HorizontalDirection = TextFlowDirection.RightToLeft;
                            result.IsRightToLeft = true;
                            result.HorizontalConfidence = 70 + baseRTLBonus; // 基础置信度加上额外分数
                            System.Diagnostics.Debug.WriteLine($"虽有从左到右证据但列结构清晰(评分:{histogramStructureScore})，优先考虑从右到左，置信度: {result.HorizontalConfidence}%");
                        }
                        else
                        {
                            result.HorizontalDirection = TextFlowDirection.LeftToRight;
                            result.IsRightToLeft = false;
                            // 计算置信度基于证据比例，但弱化一些
                            result.HorizontalConfidence = (int)(60 + 25 * ltrRatio); // 60-85之间的置信度
                            System.Diagnostics.Debug.WriteLine($"中文竖排文本但从左到右证据强(比例:{ltrRatio:F2})，水平方向为从左到右，置信度: {result.HorizontalConfidence}%");
                        }
                    }
                    else
                    {
                        result.HorizontalDirection = TextFlowDirection.LeftToRight;
                        result.IsRightToLeft = false;
                        
                        // 计算置信度基于证据比例
                        double ltrRatio = (double)evidence.LeftToRightCount / totalHorizontalEvidence;
                        result.HorizontalConfidence = (int)(60 + 35 * ltrRatio); // 60-95之间的置信度
                        
                        System.Diagnostics.Debug.WriteLine($"基于方向证据比例({ltrRatio:F2})，水平方向为从左到右，置信度: {result.HorizontalConfidence}%");
                    }
                }
                else
                {
                    // 证据相等，分析直方图特征
                    AnalyzeHistogramForHorizontalDirection(result, evidence, histogramStructureScore, baseRTLBonus, isCJKVerticalText);
                }
            }
            else
            {
                // 无方向证据，分析直方图特征
                AnalyzeHistogramForHorizontalDirection(result, evidence, histogramStructureScore, baseRTLBonus, isCJKVerticalText);
            }
            
            System.Diagnostics.Debug.WriteLine("\n--- 竖排文本流方向分析 ---");
            
            // 【新增】0. 结构强度特征 - 如果有极高的结构强度，优先考虑
            if (evidence.VerticalStructureStrength > 0.85)
            {
                // 如果垂直结构强度非常高，增强置信度
                result.DirectionConfidence = Math.Max(result.DirectionConfidence, 85);
                System.Diagnostics.Debug.WriteLine($"垂直结构强度极高(强度={evidence.VerticalStructureStrength:F2}) => 增强竖排布局置信度至{result.DirectionConfidence}%");
            }
            
            // 【新增】分析文本块形状比例分布
            double verticalTextRatio = 0;
            if (evidence.VerticalTextCount + evidence.HorizontalTextCount > 0)
            {
                verticalTextRatio = evidence.VerticalTextCount / (double)(evidence.VerticalTextCount + evidence.HorizontalTextCount);
                System.Diagnostics.Debug.WriteLine($"竖直文本块比例: {verticalTextRatio:F2} ({evidence.VerticalTextCount}个竖直/{evidence.HorizontalTextCount}个水平)");
                
                // 当竖直文本块占比非常高时，这是一个强烈的竖排布局指标
                if (verticalTextRatio > 0.7 && evidence.VerticalTextCount >= 3)
                {
                    // 增加竖排布局方向的置信度
                    int shapeBoost = (int)(10 * Math.Min(1.0, verticalTextRatio));
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 80 + shapeBoost);
                    System.Diagnostics.Debug.WriteLine($"竖直文本块比例很高({verticalTextRatio:F2})，增强竖排布局置信度 +{shapeBoost}");
                }
            }
            
            // 1. 内容分布特征
            if (evidence.ContentDensityBottomHalf > evidence.ContentDensityTopHalf * 1.5)
            {
                // 下半页内容明显多于上半页，可能是从下到上
                result.FlowDirection = TextFlowDirection.BottomToTop;
                result.DirectionConfidence = 70;
                System.Diagnostics.Debug.WriteLine($"下半页内容密度明显高于上半页 => 从下到上，置信度: {result.DirectionConfidence}%");
            }
            else if (evidence.ContentDensityTopHalf > evidence.ContentDensityBottomHalf * 1.2)
            {
                // 上半页内容明显多于下半页，可能是从上到下
                result.DirectionConfidence = 85;
                System.Diagnostics.Debug.WriteLine($"上半页内容密度明显高于上半页 => 从上到下，置信度: {result.DirectionConfidence}%");
            }
            
            // 2. 边缘对齐特征
            if (evidence.TopAlignedCount > evidence.BottomAlignedCount * 1.5 && evidence.TopAlignedCount >= 3)
            {
                // 顶部对齐明显多于底部对齐，强烈暗示从上到下
                result.FlowDirection = TextFlowDirection.TopToBottom;
                result.DirectionConfidence = Math.Max(result.DirectionConfidence, 85);
                System.Diagnostics.Debug.WriteLine($"顶部对齐文本块明显多于底部对齐 => 从上到下，置信度: {result.DirectionConfidence}%");
            }
            else if (evidence.BottomAlignedCount > evidence.TopAlignedCount * 1.5 && evidence.BottomAlignedCount >= 3)
            {
                // 底部对齐明显多于顶部对齐，可能是从下到上
                result.FlowDirection = TextFlowDirection.BottomToTop;
                result.DirectionConfidence = Math.Max(result.DirectionConfidence, 75);
                System.Diagnostics.Debug.WriteLine($"底部对齐文本块明显多于顶部对齐 => 从下到上，置信度: {result.DirectionConfidence}%");
            }
            
            // 3. 确保垂直方向也被设置
            result.VerticalDirection = result.FlowDirection;
            result.VerticalConfidence = result.DirectionConfidence;
        }
        
        /// <summary>
        /// 【新增】辅助方法：根据直方图特征分析水平方向
        /// </summary>
        private void AnalyzeHistogramForHorizontalDirection(TextDirectionResult result, DirectionEvidence evidence, 
                                                           int histogramStructureScore, int baseRTLBonus, bool isCJKVerticalText)
        {
            System.Diagnostics.Debug.WriteLine("\n--- 基于直方图特征分析水平方向 ---");
            
            // 【新增】分析峰值形状质量和峰谷比
            double peakShapeScore = evidence.VerticalPeakShapeQuality * 10; // 0-10分
            double valleyRatioScore = Math.Min(10, Math.Pow(evidence.VerticalPeakValleyMeanRatio, 2)); // 使用平方函数，0-10分
            double regularityScore = evidence.VerticalPeakRegularity * 10; // 0-10分
            
            // 【新增】高规律性奖励
            if (evidence.VerticalPeakRegularity > 0.8)
            {
                regularityScore = Math.Min(10, regularityScore * 1.5);
                System.Diagnostics.Debug.WriteLine($"高规律性奖励: 规律性评分提升至{regularityScore:F1}");
            }
            
            System.Diagnostics.Debug.WriteLine($"峰值形状质量评分: {peakShapeScore:F1}, 峰谷比评分: {valleyRatioScore:F1}, 规律性评分: {regularityScore:F1}");
            
            // 【新增】判断是否为明确结构
            bool isDefinitiveStructure = evidence.VerticalPeakShapeQuality > 0.8 && 
                                         evidence.VerticalPeakValleyMeanRatio > 2.0 && 
                                         evidence.VerticalPeakRegularity > 0.8;
            
            // 【新增】判断文档类型，动态调整权重
            double alignmentWeight, edgeWeight, contentWeight, shapeWeight, valleyWeight, regularityWeight;
            
            if (isDefinitiveStructure)
            {
                // 明确结构文档 - 直方图特征权重大幅提升
                alignmentWeight = 0.15;
                edgeWeight = 0.10;
                contentWeight = 0.15;
                shapeWeight = 0.25;
                valleyWeight = 0.20;
                regularityWeight = 0.15;
                System.Diagnostics.Debug.WriteLine("检测到明确结构文档，直方图特征权重大幅提升");
            }
            else if (evidence.VerticalPeakShapeQuality > 0.7 || 
                     evidence.VerticalPeakValleyMeanRatio > 1.8 || 
                     evidence.VerticalPeakRegularity > 0.7)
            {
                // 高结构化文档
                alignmentWeight = 0.20;
                edgeWeight = 0.15;
                contentWeight = 0.15;
                shapeWeight = 0.20;
                valleyWeight = 0.15;
                regularityWeight = 0.15;
                System.Diagnostics.Debug.WriteLine("检测到高结构化文档，直方图特征权重提升");
            }
            else if (evidence.VerticalPeakShapeQuality < 0.4 && 
                     evidence.VerticalPeakValleyMeanRatio < 1.2)
            {
                // 低质量文档
                alignmentWeight = 0.30;
                edgeWeight = 0.25;
                contentWeight = 0.25;
                shapeWeight = 0.10;
                valleyWeight = 0.05;
                regularityWeight = 0.05;
                System.Diagnostics.Debug.WriteLine("检测到低质量文档，边缘特征权重提升");
            }
            else
            {
                // 普通文本文档 - 默认权重
                alignmentWeight = 0.25;
                edgeWeight = 0.20;
                contentWeight = 0.20;
                shapeWeight = 0.15;
                valleyWeight = 0.10;
                regularityWeight = 0.10;
                System.Diagnostics.Debug.WriteLine("检测到普通文本文档，使用默认权重");
            }
            
            // 【新增】计算右侧峰值比例
            double rightPeakRatio = 0.5; // 默认均匀分布
            if (evidence.VerticalPeakCount >= 2)
            {
                // 这里需要访问原始的峰值索引数据，但我们可以通过其他特征间接推断
                if (evidence.ContentDensityRightHalf > evidence.ContentDensityLeftHalf * 1.2)
                {
                    rightPeakRatio = 0.7; // 估计右侧峰值比例较高
                }
                else if (evidence.ContentDensityLeftHalf > evidence.ContentDensityRightHalf * 1.2)
                {
                    rightPeakRatio = 0.3; // 估计左侧峰值比例较高
                }
            }
            
            // 【修改】计算边缘对齐特征得分 - 提高阈值并使用非线性计分
            double rightAlignmentScore = 0;
            double leftAlignmentScore = 0;
            
            if (evidence.RightAlignedCount >= 3)
            {
                // 前3个对齐块每个1.5分，之后每个2.5分
                rightAlignmentScore = Math.Min(10, 3 * 1.5 + Math.Max(0, evidence.RightAlignedCount - 3) * 2.5);
            }
            else if (evidence.RightAlignedCount > 0)
            {
                rightAlignmentScore = evidence.RightAlignedCount * 1.5;
            }
            
            if (evidence.LeftAlignedCount >= 3)
            {
                // 前3个对齐块每个1.5分，之后每个2.5分
                leftAlignmentScore = Math.Min(10, 3 * 1.5 + Math.Max(0, evidence.LeftAlignedCount - 3) * 2.5);
            }
            else if (evidence.LeftAlignedCount > 0)
            {
                leftAlignmentScore = evidence.LeftAlignedCount * 1.5;
            }
            
            System.Diagnostics.Debug.WriteLine($"右边缘对齐评分: {rightAlignmentScore:F1}, 左边缘对齐评分: {leftAlignmentScore:F1}");
            
            // 【修改】计算边缘变异特征得分 - 使用指数函数增强显著差异
            double rightEdgeScore = 0;
            double leftEdgeScore = 0;
            
            // 动态阈值 - 根据峰值质量调整
            double edgeVarianceThreshold = 0.7; // 默认阈值
            if (evidence.VerticalPeakShapeQuality > 0.7)
            {
                edgeVarianceThreshold = 0.8; // 高质量文档阈值更严格
            }
            else if (evidence.VerticalPeakShapeQuality < 0.4)
            {
                edgeVarianceThreshold = 0.6; // 低质量文档阈值更宽松
            }
            
            if (evidence.RightEdgeVariance > 0 && evidence.LeftEdgeVariance > 0)
            {
                // 边缘变异越小，边缘越整齐，得分越高
                if (evidence.RightEdgeVariance < evidence.LeftEdgeVariance * edgeVarianceThreshold)
                {
                    double varianceRatio = evidence.RightEdgeVariance / Math.Max(evidence.LeftEdgeVariance, evidence.RightEdgeVariance);
                    // 使用指数函数增强显著差异
                    rightEdgeScore = 10 * Math.Pow(1 - varianceRatio, 1.5);
                }
                
                if (evidence.LeftEdgeVariance < evidence.RightEdgeVariance * edgeVarianceThreshold)
                {
                    double varianceRatio = evidence.LeftEdgeVariance / Math.Max(evidence.LeftEdgeVariance, evidence.RightEdgeVariance);
                    // 使用指数函数增强显著差异
                    leftEdgeScore = 10 * Math.Pow(1 - varianceRatio, 1.5);
                }
            }
            
            System.Diagnostics.Debug.WriteLine($"右边缘整齐度评分: {rightEdgeScore:F1}, 左边缘整齐度评分: {leftEdgeScore:F1}");
            
            // 【修改】计算内容分布特征得分 - 提高阈值并使用平方函数
            double rightContentScore = 0;
            double leftContentScore = 0;
            
            if (evidence.ContentDensityRightHalf > evidence.ContentDensityLeftHalf * 1.5)
            {
                double ratio = evidence.ContentDensityRightHalf / evidence.ContentDensityLeftHalf;
                // 使用平方函数放大差异
                rightContentScore = Math.Min(10, Math.Pow(ratio - 1, 2) * 20);
            }
            else if (evidence.ContentDensityLeftHalf > evidence.ContentDensityRightHalf * 1.5)
            {
                double ratio = evidence.ContentDensityLeftHalf / evidence.ContentDensityRightHalf;
                // 使用平方函数放大差异
                leftContentScore = Math.Min(10, Math.Pow(ratio - 1, 2) * 20);
            }
            
            System.Diagnostics.Debug.WriteLine($"右侧内容密度评分: {rightContentScore:F1}, 左侧内容密度评分: {leftContentScore:F1}");
            
            // 【修改】计算综合得分 - 使用动态权重
            double rtlScore = (rightAlignmentScore * alignmentWeight) + 
                             (rightEdgeScore * edgeWeight) + 
                             (rightContentScore * contentWeight) + 
                             (peakShapeScore * shapeWeight) + 
                             (valleyRatioScore * valleyWeight) + 
                             (regularityScore * regularityWeight);
                             
            double ltrScore = (leftAlignmentScore * alignmentWeight) + 
                             (leftEdgeScore * edgeWeight) + 
                             (leftContentScore * contentWeight) + 
                             (peakShapeScore * shapeWeight) + 
                             (valleyRatioScore * valleyWeight) + 
                             (regularityScore * regularityWeight);
            
            // 对于中文竖排文本，如果直方图结构清晰，适当提升RTL得分
            if (isCJKVerticalText && histogramStructureScore >= 7)
            {
                // 【修改】使用非线性加分
                double bonus = 0;
                if (histogramStructureScore >= 9)
                {
                    bonus = histogramStructureScore * 0.4;
                }
                else if (histogramStructureScore >= 8)
                {
                    bonus = histogramStructureScore * 0.3;
                }
                else
                {
                    bonus = histogramStructureScore * 0.2;
                }
                
                rtlScore += bonus;
                System.Diagnostics.Debug.WriteLine($"中文竖排文本且直方图结构清晰，RTL得分+{bonus:F1}");
            }
            
            System.Diagnostics.Debug.WriteLine($"从右到左综合评分: {rtlScore:F1}, 从左到右综合评分: {ltrScore:F1}");
            
            // 【修改】多级阈值与置信度计算
            double scoreDiffRatio = 0;
            if (ltrScore > 0)
                scoreDiffRatio = rtlScore / ltrScore;
            else if (rtlScore > 0)
                scoreDiffRatio = double.MaxValue;
                
            double reversedScoreDiffRatio = 0;
            if (rtlScore > 0)
                reversedScoreDiffRatio = ltrScore / rtlScore;
            else if (ltrScore > 0)
                reversedScoreDiffRatio = double.MaxValue;
            
            // 根据得分差异确定方向和置信度
            if (scoreDiffRatio >= 2.0)
            {
                // 从右到左得分明显更高 (>2.0倍)
                result.HorizontalDirection = TextFlowDirection.RightToLeft;
                result.IsRightToLeft = true;
                
                // 计算置信度，基于得分差异
                double scoreDiff = rtlScore - ltrScore;
                result.HorizontalConfidence = 85 + (int)Math.Min(10, scoreDiff);
                
                System.Diagnostics.Debug.WriteLine($"从右到左得分极高(是从左到右的{scoreDiffRatio:F1}倍)，水平方向设为从右到左，置信度: {result.HorizontalConfidence}%");
            }
            else if (scoreDiffRatio >= 1.5)
            {
                // 从右到左得分明显更高 (1.5-2.0倍)
                result.HorizontalDirection = TextFlowDirection.RightToLeft;
                result.IsRightToLeft = true;
                
                // 计算置信度，基于得分差异
                double scoreDiff = rtlScore - ltrScore;
                result.HorizontalConfidence = 80 + (int)Math.Min(15, scoreDiff);
                
                System.Diagnostics.Debug.WriteLine($"从右到左得分很高(是从左到右的{scoreDiffRatio:F1}倍)，水平方向设为从右到左，置信度: {result.HorizontalConfidence}%");
            }
            else if (scoreDiffRatio >= 1.2)
            {
                // 从右到左得分明显更高 (1.2-1.5倍)
                result.HorizontalDirection = TextFlowDirection.RightToLeft;
                result.IsRightToLeft = true;
                
                // 计算置信度，基于得分差异
                double scoreDiff = rtlScore - ltrScore;
                result.HorizontalConfidence = 70 + (int)Math.Min(20, scoreDiff);
                
                System.Diagnostics.Debug.WriteLine($"从右到左得分较高(是从左到右的{scoreDiffRatio:F1}倍)，水平方向设为从右到左，置信度: {result.HorizontalConfidence}%");
            }
            else if (reversedScoreDiffRatio >= 2.0)
            {
                // 从左到右得分极高 (>2.0倍)
                result.HorizontalDirection = TextFlowDirection.LeftToRight;
                result.IsRightToLeft = false;
                
                // 计算置信度，基于得分差异
                double scoreDiff = ltrScore - rtlScore;
                result.HorizontalConfidence = 85 + (int)Math.Min(10, scoreDiff);
                
                System.Diagnostics.Debug.WriteLine($"从左到右得分极高(是从右到左的{reversedScoreDiffRatio:F1}倍)，水平方向设为从左到右，置信度: {result.HorizontalConfidence}%");
            }
            else if (reversedScoreDiffRatio >= 1.5)
            {
                // 从左到右得分很高 (1.5-2.0倍)
                result.HorizontalDirection = TextFlowDirection.LeftToRight;
                result.IsRightToLeft = false;
                
                // 计算置信度，基于得分差异
                double scoreDiff = ltrScore - rtlScore;
                result.HorizontalConfidence = 80 + (int)Math.Min(15, scoreDiff);
                
                System.Diagnostics.Debug.WriteLine($"从左到右得分很高(是从右到左的{reversedScoreDiffRatio:F1}倍)，水平方向设为从左到右，置信度: {result.HorizontalConfidence}%");
            }
            else if (reversedScoreDiffRatio >= 1.2)
            {
                // 从左到右得分较高 (1.2-1.5倍)
                result.HorizontalDirection = TextFlowDirection.LeftToRight;
                result.IsRightToLeft = false;
                
                // 计算置信度，基于得分差异
                double scoreDiff = ltrScore - rtlScore;
                result.HorizontalConfidence = 70 + (int)Math.Min(20, scoreDiff);
                
                System.Diagnostics.Debug.WriteLine($"从左到右得分较高(是从右到左的{reversedScoreDiffRatio:F1}倍)，水平方向设为从左到右，置信度: {result.HorizontalConfidence}%");
            }
            else if (scoreDiffRatio >= 1.1)
            {
                // 从右到左得分略高 (1.1-1.2倍)
                result.HorizontalDirection = TextFlowDirection.RightToLeft;
                result.IsRightToLeft = true;
                
                // 计算置信度，基于得分差异
                double scoreDiff = rtlScore - ltrScore;
                result.HorizontalConfidence = 65 + (int)Math.Min(15, scoreDiff * 2);
                
                System.Diagnostics.Debug.WriteLine($"从右到左得分略高(是从左到右的{scoreDiffRatio:F1}倍)，水平方向设为从右到左，置信度: {result.HorizontalConfidence}%");
            }
            else if (reversedScoreDiffRatio >= 1.1)
            {
                // 从左到右得分略高 (1.1-1.2倍)
                result.HorizontalDirection = TextFlowDirection.LeftToRight;
                result.IsRightToLeft = false;
                
                // 计算置信度，基于得分差异
                double scoreDiff = ltrScore - rtlScore;
                result.HorizontalConfidence = 65 + (int)Math.Min(15, scoreDiff * 2);
                
                System.Diagnostics.Debug.WriteLine($"从左到右得分略高(是从右到左的{reversedScoreDiffRatio:F1}倍)，水平方向设为从左到右，置信度: {result.HorizontalConfidence}%");
            }
            else
            {
                // 得分接近，需要进一步分析
                if (isCJKVerticalText && evidence.VerticalPeakCount >= 2)
                {
                    // 中文竖排文本，如果有明显的列结构，倾向于从右到左
                    if (evidence.VerticalPeakValleyMeanRatio > 1.5 && evidence.VerticalPeakRegularity > 0.7)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 75;
                        System.Diagnostics.Debug.WriteLine("中文竖排文本，列结构明显，倾向于从右到左，置信度: 75%");
                    }
                    else
                    {
                        // 中文竖排文本，但列结构不明显，仍然倾向于从右到左但置信度较低
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 65;
                        System.Diagnostics.Debug.WriteLine("中文竖排文本，列结构不明显，仍然倾向于从右到左，置信度: 65%");
                    }
                }
                else
                {
                    // 非中文竖排或无明显列结构，根据轻微优势确定
                    if (rtlScore >= ltrScore)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 60 + (int)(10 * (rtlScore / Math.Max(1, ltrScore) - 1));
                        System.Diagnostics.Debug.WriteLine($"从右到左略占优势，水平方向设为从右到左，置信度: {result.HorizontalConfidence}%");
                    }
                    else
                    {
                        result.HorizontalDirection = TextFlowDirection.LeftToRight;
                        result.IsRightToLeft = false;
                        result.HorizontalConfidence = 60 + (int)(10 * (ltrScore / Math.Max(1, rtlScore) - 1));
                        System.Diagnostics.Debug.WriteLine($"从左到右略占优势，水平方向设为从左到右，置信度: {result.HorizontalConfidence}%");
                    }
                }
            }
            
            // 明确结构情况下，额外提升置信度
            if (isDefinitiveStructure)
            {
                result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 5);
                System.Diagnostics.Debug.WriteLine($"检测到明确结构，额外提升置信度至{result.HorizontalConfidence}%");
            }
        }
        
        /// <summary>
        /// 优化方向检测结果
        /// </summary>
        private void OptimizeDirectionDetection(List<TextCellInfo> cells, TextDirectionResult result)
        {
            System.Diagnostics.Debug.WriteLine("\n=== 方向优化开始 ===");
            System.Diagnostics.Debug.WriteLine($"优化前 - 布局: {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
            System.Diagnostics.Debug.WriteLine($"主方向: {result.FlowDirection}, 置信度: {result.DirectionConfidence}");
            
            // 1. 中文竖排文本特殊处理
            bool isCJKVertical = IsCJKText(cells) && result.IsVerticalLayout;
            if (isCJKVertical)
            {
                System.Diagnostics.Debug.WriteLine("检测到中文竖排文本，应用特殊优化规则");
                
                // 重新提取特征进行深度优化
                var features = ExtractTextFeatures(cells);
                var evidence = ExtractDirectionEvidence(features);
                
                // 专门针对竖排文本方向的优化
                OptimizeVerticalLayoutDirections(result, features, evidence);
                
                // 应用高级置信度优化
                ApplyAdvancedConfidenceOptimization(result, evidence, cells);
                
                // 【新增】分析直方图峰值分布模式
                if (features.VerticalPeakCount >= 2)
                {
                    // 分析峰值间距变化趋势
                    AnalyzePeakDistributionPattern(features, result);
                }
                
                                    // 【新增】中文竖排文本特殊处理
                    if (result.IsVerticalLayout)
                    {
                        // 计算竖排文本占比
                        double verticalTextRatio = (double)evidence.VerticalTextCount / 
                            (evidence.VerticalTextCount + evidence.HorizontalTextCount + evidence.SquareTextCount);
                        
                        System.Diagnostics.Debug.WriteLine($"竖排文本占比: {verticalTextRatio:P2}");
                        
                        // 如果竖排文本占比很高，进一步增强竖排布局置信度
                        if (verticalTextRatio > 0.8)
                        {
                            result.LayoutConfidence = Math.Max(result.LayoutConfidence, 90);
                            System.Diagnostics.Debug.WriteLine($"竖排文本占比极高({verticalTextRatio:P2}) => 增强竖排布局置信度至{result.LayoutConfidence}%");
                            
                            // 对于高竖排占比的中文文本，强制设置为从右到左
                            // 这是基于传统中文竖排排版规则
                            result.HorizontalDirection = TextFlowDirection.RightToLeft;
                            result.IsRightToLeft = true;
                            result.HorizontalConfidence = 90;
                            System.Diagnostics.Debug.WriteLine("高竖排占比中文文本 => 强制设置为从右到左，置信度调整为90%");
                        }
                        else
                        {
                            // 即使竖排文本占比不是特别高，对于中文竖排文本也默认从右到左
                            result.HorizontalDirection = TextFlowDirection.RightToLeft;
                            result.IsRightToLeft = true;
                            result.HorizontalConfidence = 80;
                            System.Diagnostics.Debug.WriteLine("中文竖排文本 => 默认设置为从右到左，置信度调整为80%");
                        }
                    
                    // 分析内容分布 - 中文竖排文本通常右侧内容更多
                    if (evidence.ContentDensityRightHalf > evidence.ContentDensityLeftHalf * 1.2)
                    {
                        // 右半页内容明显多于左半页，强烈暗示从右到左
                        if (result.HorizontalDirection != TextFlowDirection.RightToLeft || result.HorizontalConfidence < 85)
                        {
                            result.HorizontalDirection = TextFlowDirection.RightToLeft;
                            result.IsRightToLeft = true;
                            result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 85);
                            System.Diagnostics.Debug.WriteLine($"中文竖排文本右侧内容密度更高({evidence.ContentDensityRightHalf:F2} vs {evidence.ContentDensityLeftHalf:F2}) => 强烈暗示从右到左，置信度提升至{result.HorizontalConfidence}%");
                        }
                    }
                    
                    // 分析边缘对齐 - 中文竖排文本通常右边缘对齐更整齐
                    if (evidence.RightAlignedCount > evidence.LeftAlignedCount && evidence.RightAlignedCount >= 2)
                    {
                        // 右对齐文本块多于左对齐，暗示从右到左
                        if (result.HorizontalDirection == TextFlowDirection.RightToLeft)
                        {
                            // 增强已有判断
                            result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 5);
                            System.Diagnostics.Debug.WriteLine($"中文竖排文本右对齐文本块较多({evidence.RightAlignedCount} vs {evidence.LeftAlignedCount}) => 增强从右到左置信度至{result.HorizontalConfidence}%");
                        }
                        else if (result.HorizontalConfidence < 75)
                        {
                            // 修正方向判断
                            result.HorizontalDirection = TextFlowDirection.RightToLeft;
                            result.IsRightToLeft = true;
                            result.HorizontalConfidence = 75;
                            System.Diagnostics.Debug.WriteLine($"中文竖排文本右对齐文本块较多({evidence.RightAlignedCount} vs {evidence.LeftAlignedCount}) => 修正为从右到左，置信度设为75%");
                        }
                    }
                    
                    // 检查列间距规律性 - 中文竖排文本通常有较高的列间距规律性
                    if (evidence.VerticalPeakRegularity > 0.8 && evidence.VerticalPeakCount >= 3)
                    {
                        // 列间距规律性高，增强竖排布局置信度
                        result.LayoutConfidence = Math.Max(result.LayoutConfidence, 85);
                        System.Diagnostics.Debug.WriteLine($"列间距规律性高({evidence.VerticalPeakRegularity:F2}) => 增强竖排布局置信度至{result.LayoutConfidence}%");
                        
                        // 对于规律性高的多列结构，增强水平方向置信度
                        if (result.HorizontalDirection == TextFlowDirection.RightToLeft)
                        {
                            result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 5);
                            System.Diagnostics.Debug.WriteLine($"规律性高的多列结构 => 增强从右到左置信度至{result.HorizontalConfidence}%");
                        }
                    }
                }
                
                // 确保置信度不低于最低阈值
                EnsureBasicLayoutConsistency(result);
                
                // 最后一致性调整
                AdjustConfidenceBasedOnConsistency(result, evidence);
            }
            else
            {
                // 非中文竖排文本的通用优化
                // ...原有代码保持不变...
            }
        }
        
        /// <summary>
        /// 【新增】分析直方图峰值分布模式，用于优化方向判断
        /// </summary>
        /// <param name="features">文本布局特征</param>
        /// <param name="result">方向结果</param>
        private void AnalyzePeakDistributionPattern(TextLayoutFeatures features, TextDirectionResult result)
        {
            if (features.VerticalPeakIndices.Count < 2)
                return;
            
            System.Diagnostics.Debug.WriteLine("\n--- 直方图峰值分布模式分析 ---");
            
            // 分析峰值间距的变化趋势
            List<double> peakSpacings = new List<double>();
            for (int i = 1; i < features.VerticalPeakIndices.Count; i++)
            {
                int currentPeakIndex = features.VerticalPeakIndices[i];
                int previousPeakIndex = features.VerticalPeakIndices[i - 1];
                double spacing = (currentPeakIndex - previousPeakIndex) * features.VerticalBinSize;
                peakSpacings.Add(spacing);
            }
            
            // 计算峰值间距统计特征
            double avgSpacing = peakSpacings.Average();
            double minSpacing = peakSpacings.Min();
            double maxSpacing = peakSpacings.Max();
            double spacingRange = maxSpacing - minSpacing;
            
            System.Diagnostics.Debug.WriteLine($"峰值间距: 平均={avgSpacing:F2}, 最小={minSpacing:F2}, 最大={maxSpacing:F2}, 范围={spacingRange:F2}");
            
            // 计算峰值间距的变化趋势
            bool isSpacingIncreasing = false;
            bool isSpacingDecreasing = false;
            bool isSpacingConstant = false;
            
            if (peakSpacings.Count >= 2)
            {
                int increasingCount = 0;
                int decreasingCount = 0;
                for (int i = 1; i < peakSpacings.Count; i++)
                {
                    if (peakSpacings[i] > peakSpacings[i - 1] * 1.1)
                        increasingCount++;
                    else if (peakSpacings[i] < peakSpacings[i - 1] * 0.9)
                        decreasingCount++;
                }
                
                double increasingRatio = (double)increasingCount / (peakSpacings.Count - 1);
                double decreasingRatio = (double)decreasingCount / (peakSpacings.Count - 1);
                
                isSpacingIncreasing = increasingRatio > 0.6;
                isSpacingDecreasing = decreasingRatio > 0.6;
                isSpacingConstant = (increasingCount + decreasingCount) < (peakSpacings.Count - 1) * 0.4;
                
                System.Diagnostics.Debug.WriteLine($"间距变化趋势: 递增={isSpacingIncreasing}, 递减={isSpacingDecreasing}, 恒定={isSpacingConstant}");
            }
            
            // 分析峰值高度的变化趋势
            List<int> peakHeights = new List<int>();
            foreach (int peakIndex in features.VerticalPeakIndices)
            {
                if (peakIndex < features.VerticalHistogram.Count)
                {
                    peakHeights.Add(features.VerticalHistogram[peakIndex]);
                }
            }
            
            bool isHeightIncreasing = false;
            bool isHeightDecreasing = false;
            bool isHeightConstant = false;
            
            if (peakHeights.Count >= 2)
            {
                int increasingCount = 0;
                int decreasingCount = 0;
                for (int i = 1; i < peakHeights.Count; i++)
                {
                    if (peakHeights[i] > peakHeights[i - 1] * 1.1)
                        increasingCount++;
                    else if (peakHeights[i] < peakHeights[i - 1] * 0.9)
                        decreasingCount++;
                }
                
                double increasingRatio = (double)increasingCount / (peakHeights.Count - 1);
                double decreasingRatio = (double)decreasingCount / (peakHeights.Count - 1);
                
                isHeightIncreasing = increasingRatio > 0.6;
                isHeightDecreasing = decreasingRatio > 0.6;
                isHeightConstant = (increasingCount + decreasingCount) < (peakHeights.Count - 1) * 0.4;
                
                System.Diagnostics.Debug.WriteLine($"高度变化趋势: 递增={isHeightIncreasing}, 递减={isHeightDecreasing}, 恒定={isHeightConstant}");
            }
            
            // 【新增】分析峰值位置分布 - 检查是否集中在右侧或左侧
            bool peaksConcentratedOnRight = false;
            bool peaksConcentratedOnLeft = false;
            
            if (features.VerticalPeakIndices.Count >= 2 && features.VerticalHistogram.Count > 0)
            {
                int histogramMidPoint = features.VerticalHistogram.Count / 2;
                int peaksOnRightHalf = 0;
                int peaksOnLeftHalf = 0;
                
                foreach (int peakIndex in features.VerticalPeakIndices)
                {
                    if (peakIndex > histogramMidPoint)
                        peaksOnRightHalf++;
                    else
                        peaksOnLeftHalf++;
                }
                
                double rightRatio = (double)peaksOnRightHalf / features.VerticalPeakIndices.Count;
                double leftRatio = (double)peaksOnLeftHalf / features.VerticalPeakIndices.Count;
                
                peaksConcentratedOnRight = rightRatio > 0.7;
                peaksConcentratedOnLeft = leftRatio > 0.7;
                
                System.Diagnostics.Debug.WriteLine($"峰值位置分布: 右侧集中={peaksConcentratedOnRight} ({rightRatio:P0}), 左侧集中={peaksConcentratedOnLeft} ({leftRatio:P0})");
            }
            
            // 【新增】分析峰值形状质量和峰谷比
            double peakQualityScore = features.VerticalPeakShapeQuality * features.VerticalPeakValleyMeanRatio;
            bool hasHighQualityPeaks = peakQualityScore > 1.0;
            
            System.Diagnostics.Debug.WriteLine($"峰值质量评分: {peakQualityScore:F2}, 高质量峰值={hasHighQualityPeaks}");
            
            // 根据峰值分布模式调整方向置信度
            if (result.IsVerticalLayout)
            {
                // 针对竖排布局的水平方向判断（从右到左还是从左到右）
                
                // 1. 间距从大到小 + 高度递减 => 强烈暗示从右到左
                if (isSpacingDecreasing && isHeightDecreasing)
                {
                    // 从右到左的强烈证据 - 列间距和列高度都从右到左递减
                    // 这是传统中文竖排排版的典型特征
                    if (result.HorizontalDirection != TextFlowDirection.RightToLeft || result.HorizontalConfidence < 85)
                        {
                            result.HorizontalDirection = TextFlowDirection.RightToLeft;
                            result.IsRightToLeft = true;
                        result.HorizontalConfidence = 90;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 列间距和高度从右到左递减 => 强烈暗示从右到左, 置信度提升至90%");
                        }
                    }
                // 【新增】1.1 峰值集中在右侧 + 高质量峰值 => 强烈暗示从右到左
                else if (peaksConcentratedOnRight && hasHighQualityPeaks)
                    {
                    // 传统中文竖排文本通常从右到左，峰值集中在右侧是重要特征
                        if (result.HorizontalDirection != TextFlowDirection.RightToLeft || result.HorizontalConfidence < 85)
                        {
                            result.HorizontalDirection = TextFlowDirection.RightToLeft;
                            result.IsRightToLeft = true;
                        result.HorizontalConfidence = 88;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 高质量峰值集中在右侧 => 强烈暗示从右到左, 置信度提升至88%");
                    }
                }
                // 2. 间距从小到大 + 高度递增 => 暗示从左到右
                else if (isSpacingIncreasing && isHeightIncreasing)
                {
                    // 从左到右的强烈证据
                    if (result.HorizontalDirection != TextFlowDirection.LeftToRight || result.HorizontalConfidence < 80)
                    {
                        result.HorizontalDirection = TextFlowDirection.LeftToRight;
                        result.IsRightToLeft = false;
                        result.HorizontalConfidence = 80;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 列间距和高度从左到右递增 => 暗示从左到右, 置信度提升至80%");
                    }
                }
                // 【新增】2.1 峰值集中在左侧 + 高质量峰值 => 暗示从左到右
                else if (peaksConcentratedOnLeft && hasHighQualityPeaks)
                {
                    // 非传统排版的竖排文本，峰值集中在左侧
                    if (result.HorizontalDirection != TextFlowDirection.LeftToRight || result.HorizontalConfidence < 80)
                    {
                        result.HorizontalDirection = TextFlowDirection.LeftToRight;
                        result.IsRightToLeft = false;
                        result.HorizontalConfidence = 80;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 高质量峰值集中在左侧 => 暗示从左到右, 置信度提升至80%");
                    }
                }
                // 3. 间距均匀 + 高度相近 => 常规排版，增强已有判断
                else if (isSpacingConstant && isHeightConstant && spacingRange / avgSpacing < 0.3)
                {
                    // 列间距和高度均匀 - 增强已有判断的置信度
                    if (result.HorizontalConfidence < 85)
                    {
                        result.HorizontalConfidence = Math.Min(85, result.HorizontalConfidence + 10);
                        System.Diagnostics.Debug.WriteLine($"峰值分布模式分析: 列间距和高度均匀稳定 => 增强已有判断置信度至{result.HorizontalConfidence}%");
                    }
                    
                    // 【新增】对于均匀分布的情况，如果峰值质量高，默认倾向于传统中文竖排（从右到左）
                    if (hasHighQualityPeaks && result.HorizontalDirection == TextFlowDirection.LeftToRight && result.HorizontalConfidence < 75)
                    {
                            result.HorizontalDirection = TextFlowDirection.RightToLeft;
                            result.IsRightToLeft = true;
                            result.HorizontalConfidence = 75;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 均匀分布高质量峰值 => 倾向于传统中文竖排(从右到左), 置信度调整至75%");
                    }
                }
                // 4. 仅间距变化有明显模式时的弱判断
                else if (isSpacingDecreasing && features.VerticalPeakCount >= 3)
                {
                    // 仅列间距有规律减小，倾向于从右到左
                    if (result.HorizontalDirection == TextFlowDirection.RightToLeft && result.HorizontalConfidence < 80)
                    {
                        result.HorizontalConfidence = Math.Min(80, result.HorizontalConfidence + 8);
                        System.Diagnostics.Debug.WriteLine($"峰值分布模式分析: 列间距从右到左递减 => 增强从右到左置信度至{result.HorizontalConfidence}%");
                    }
                    else if (result.HorizontalDirection == TextFlowDirection.LeftToRight && result.HorizontalConfidence < 75)
                    {
                        // 当前判断与峰值分布模式不符，降低置信度
                        result.HorizontalConfidence = 70;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 列间距模式与当前从左到右判断不符 => 降低置信度至70%");
                    }
                }
                
                // 【新增】5. 针对中文竖排文本的特殊处理 - 基于峰值质量和峰值数量
                if (features.VerticalPeakCount >= 3 && features.VerticalPeakShapeQuality > 0.7 && features.VerticalPeakValleyMeanRatio > 2.0)
                {
                    // 高质量峰值 + 多列 => 强烈暗示传统中文竖排文本
                    // 传统中文竖排文本几乎总是从右到左
                    if (result.HorizontalDirection != TextFlowDirection.RightToLeft || result.HorizontalConfidence < 85)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 85;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 多列高质量峰值特征 => 强烈暗示传统中文竖排(从右到左), 置信度提升至85%");
                    }
                }
                
                // 【新增】6. 基于内容密度和峰值质量的综合判断
                if (features.ContentDensityRightHalf > features.ContentDensityLeftHalf * 1.3 && hasHighQualityPeaks)
                {
                    // 右侧内容密度更高 + 高质量峰值 => 从右到左的强烈证据
                    if (result.HorizontalDirection != TextFlowDirection.RightToLeft || result.HorizontalConfidence < 88)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 88;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 右侧内容密度更高 + 高质量峰值 => 强烈暗示从右到左, 置信度提升至88%");
                    }
                }
                else if (features.ContentDensityLeftHalf > features.ContentDensityRightHalf * 1.3 && hasHighQualityPeaks)
                {
                    // 左侧内容密度更高 + 高质量峰值 => 从左到右的强烈证据
                    if (result.HorizontalDirection != TextFlowDirection.LeftToRight || result.HorizontalConfidence < 85)
                    {
                        result.HorizontalDirection = TextFlowDirection.LeftToRight;
                        result.IsRightToLeft = false;
                        result.HorizontalConfidence = 85;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 左侧内容密度更高 + 高质量峰值 => 强烈暗示从左到右, 置信度提升至85%");
                    }
                }
                
                System.Diagnostics.Debug.WriteLine($"峰值分布模式分析后: 水平方向={result.HorizontalDirection}, 置信度={result.HorizontalConfidence}%");
            }
        }
        
        /// <summary>
        /// 计算布局置信度
        /// </summary>
        private int CalculateLayoutConfidence(int dominantScore, int secondaryScore, int totalScore, double featureReliability = 1.0)
        {
            if (totalScore == 0) return 50; // 无证据时的默认置信度
            
            // 计算主导方向的得分比例
            double dominantRatio = dominantScore / (double)totalScore;
            
            // 计算得分差异
            int scoreDiff = dominantScore - secondaryScore;
            
            // 应用特征可靠性因子
            dominantRatio = dominantRatio * featureReliability;
            scoreDiff = (int)(scoreDiff * featureReliability);
            
            // 基于得分差异和比例计算置信度
            int confidence;
            
            if (dominantRatio > 0.8 && scoreDiff >= 15)
            {
                // 主导方向极其明显
                confidence = 95;
            }
            else if (dominantRatio > 0.7 && scoreDiff >= 10)
            {
                // 主导方向非常明显
                confidence = 90;
            }
            else if (dominantRatio > 0.65 && scoreDiff >= 8)
            {
                // 主导方向明显
                confidence = 85;
            }
            else if (dominantRatio > 0.6 && scoreDiff >= 5)
            {
                // 主导方向较明显
                confidence = 80;
            }
            else if (dominantRatio > 0.55)
            {
                // 主导方向略微明显
                confidence = 70;
            }
            else
            {
                // 方向不明显
                confidence = 60;
            }
            
            return confidence;
        }

        /// <summary>
        /// 确保方向检测结果的置信度不低于最低阈值
        /// </summary>
        private void EnsureBasicLayoutConsistency(TextDirectionResult result)
        {
            // 确保最低置信度
            if (result.LayoutConfidence < 60)
            {
                result.LayoutConfidence = 60;
            }
            
            if (result.DirectionConfidence < 60)
            {
                result.DirectionConfidence = 60;
            }
            
            // 确保结果的一致性
            // 如果是竖排布局，确保流向是TopToBottom或BottomToTop
            if (result.IsVerticalLayout)
            {
                if (result.FlowDirection != TextFlowDirection.TopToBottom && 
                    result.FlowDirection != TextFlowDirection.BottomToTop)
                {
                    result.FlowDirection = TextFlowDirection.TopToBottom; // 默认从上到下
                    result.DirectionConfidence = 70;
                    System.Diagnostics.Debug.WriteLine("竖排布局方向不一致，调整为从上到下");
                }
            }
            // 如果是横排布局，确保流向是LeftToRight或RightToLeft
            else
            {
                if (result.FlowDirection != TextFlowDirection.LeftToRight && 
                    result.FlowDirection != TextFlowDirection.RightToLeft)
                {
                    result.FlowDirection = TextFlowDirection.LeftToRight; // 默认从左到右
                    result.DirectionConfidence = 70;
                    System.Diagnostics.Debug.WriteLine("横排布局方向不一致，调整为从左到右");
                }
            }
            
            // 对于特殊情况的处理
            // 例如：如果检测到文本块数量很少，降低置信度
            if (cells != null && cells.Count < 5)
            {
                result.LayoutConfidence = Math.Min(result.LayoutConfidence, 70);
                result.DirectionConfidence = Math.Min(result.DirectionConfidence, 70);
                System.Diagnostics.Debug.WriteLine($"文本块数量较少({cells.Count})，降低置信度");
            }
        }
    }

    /// <summary>
    /// 文本流方向枚举
    /// </summary>
    public enum TextFlowDirection
    {
        /// <summary>从左到右（常见于拉丁语系、中文横排等）</summary>
        LeftToRight,
        /// <summary>从右到左（常见于阿拉伯语、希伯来语等）</summary>
        RightToLeft,
        /// <summary>从上到下（常见于中文竖排等）</summary>
        TopToBottom,
        /// <summary>从下到上（常见于车道提示语等）</summary>
        BottomToTop,
        /// <summary>混合方向（多种方向混合）</summary>
        Mixed
    }
}