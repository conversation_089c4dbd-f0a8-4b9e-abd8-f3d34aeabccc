﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="TencentOCR.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" />
    </startup>
    <userSettings>
        <TencentOCR.Properties.Settings>
            <setting name="StrWxOcr" serializeAs="String">
                <value />
            </setting>
            <setting name="StrWxApp" serializeAs="String">
                <value />
            </setting>
            <setting name="StrQQNTOcr" serializeAs="String">
                <value />
            </setting>
            <setting name="StrQQNTApp" serializeAs="String">
                <value />
            </setting>
        </TencentOCR.Properties.Settings>
    </userSettings>
</configuration>