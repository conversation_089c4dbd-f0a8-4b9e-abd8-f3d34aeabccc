﻿using MetroFramework.Controls;
using MetroFramework.Design;
using MetroFramework.Interfaces;
using System;
using System.ComponentModel;
using System.Reflection;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace MetroFramework.Components
{
    [Designer(typeof(MetroStyleManagerDesigner), typeof(ParentControlDesigner))]
    public sealed class MetroStyleManager : Component, ICloneable, ISupportInitialize
    {
        private readonly IContainer parentContainer;

        private bool _isInitializing;

        private MetroColorStyle _metroStyle = MetroColorStyle.Blue;

        private MetroThemeStyle _metroTheme = MetroThemeStyle.Light;

        private ContainerControl _owner;

        public MetroStyleManager()
        {
        }

        public MetroStyleManager(IContainer parentContainer)
            : this()
        {
            if (parentContainer != null)
            {
                this.parentContainer = parentContainer;
                this.parentContainer.Add(this);
            }
        }

        [DefaultValue(MetroColorStyle.Blue)]
        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get => _metroStyle;
            set
            {
                if (_metroStyle != value)
                {
                    _metroStyle = value;
                    StyleChange?.Invoke(null, null);
                    if (!_isInitializing) Update();
                }
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get => _metroTheme;
            set
            {
                if (_metroTheme != value)
                {
                    _metroTheme = value;
                    ThemeChange?.Invoke(null, null);
                    if (!_isInitializing) Update();
                }
            }
        }

        public ContainerControl Owner
        {
            get => _owner;
            set
            {
                if (_owner != null) _owner.ControlAdded -= ControlAdded;
                _owner = value;
                if (value != null)
                {
                    _owner.ControlAdded += ControlAdded;
                    if (!_isInitializing) UpdateControl(value);
                }
            }
        }

        public object Clone()
        {
            var metroStyleManager = new MetroStyleManager
            {
                _metroTheme = Theme,
                _metroStyle = Style
            };
            return metroStyleManager;
        }

        void ISupportInitialize.BeginInit()
        {
            _isInitializing = true;
        }

        void ISupportInitialize.EndInit()
        {
            _isInitializing = false;
            Update();
        }

        public event EventHandler<EventArgs> ThemeChange;

        public event EventHandler<EventArgs> StyleChange;

        public object Clone(ContainerControl owner)
        {
            var metroStyleManager = Clone() as MetroStyleManager;
            if (owner is IMetroForm)
            {
                metroStyleManager.Owner = owner;
                ((IMetroForm) owner).StyleManager = metroStyleManager;
                var type = owner.GetType();
                var field = type.GetField("components", BindingFlags.Instance | BindingFlags.NonPublic);
                if (field == null) return metroStyleManager;
                var container = (IContainer) field.GetValue(owner);
                if (container == null) return metroStyleManager;
                {
                    foreach (Component component in container.Components)
                    {
                        if (component is IMetroComponent metroComponent) ApplyTheme(metroComponent);
                        if (component.GetType() == typeof(MetroContextMenu)) ApplyTheme((MetroContextMenu) component);
                    }

                    return metroStyleManager;
                }
            }

            return metroStyleManager;
        }

        private void ControlAdded(object sender, ControlEventArgs e)
        {
            if (!_isInitializing) UpdateControl(e.Control);
        }

        public void Update()
        {
            if (_owner != null) UpdateControl(_owner);
            if (parentContainer != null && parentContainer.Components != null)
                foreach (var component in parentContainer.Components)
                {
                    if (component is IMetroComponent metroComponent) ApplyTheme(metroComponent);
                    if (component.GetType() == typeof(MetroContextMenu)) ApplyTheme((MetroContextMenu) component);
                }
        }

        private void UpdateControl(Control ctrl)
        {
            if (ctrl != null)
            {
                if (ctrl is IMetroControl metroControl) ApplyTheme(metroControl);
                if (ctrl is IMetroComponent metroComponent) ApplyTheme(metroComponent);
                if (ctrl is TabControl tabControl)
                    foreach (TabPage tabPage in tabControl.TabPages)
                        UpdateControl(tabPage);
                foreach (Control control in ctrl.Controls)
                    UpdateControl(control);
                if (ctrl.ContextMenuStrip != null) UpdateControl(ctrl.ContextMenuStrip);
                ctrl.Refresh();
            }
        }

        private void ApplyTheme(IMetroControl control)
        {
            control.StyleManager = this;
        }

        private void ApplyTheme(IMetroComponent component)
        {
            component.StyleManager = this;
        }
    }
}