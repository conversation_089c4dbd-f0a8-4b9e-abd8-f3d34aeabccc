﻿/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
using MetroFramework.Animation;
using MetroFramework.Components;
using MetroFramework.Controls;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using MetroFramework.Native;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace MetroFramework.Forms
{
    public sealed class MetroTaskWindow : MetroForm
    {
        private static MetroTaskWindow singletonWindow;

        private bool cancelTimer;

        private readonly int closeTime;

        private int elapsedTime;

        private int progressWidth;

        private DelayedCall timer;

        private readonly MetroPanel controlContainer;

        private bool isInitialized;

        public bool CancelTimer
        {
            get
            {
                return cancelTimer;
            }
            set
            {
                cancelTimer = value;
            }
        }

        public static void ShowTaskWindow(IWin32Window parent, string title, Control userControl, int secToClose)
        {
            if (singletonWindow != null)
            {
                singletonWindow.Close();
                singletonWindow.Dispose();
                singletonWindow = null;
            }
            singletonWindow = new MetroTaskWindow(secToClose, userControl);
            singletonWindow.Text = title;
            singletonWindow.Resizable = false;
            singletonWindow.Movable = true;
            singletonWindow.StartPosition = FormStartPosition.Manual;
            if (parent != null && parent is IMetroForm)
            {
                singletonWindow.Theme = ((IMetroForm)parent).Theme;
                singletonWindow.Style = ((IMetroForm)parent).Style;
                singletonWindow.StyleManager = (((IMetroForm)parent).StyleManager.Clone(singletonWindow) as MetroStyleManager);
            }
            singletonWindow.Show();
        }

        public static bool IsVisible()
        {
            if (singletonWindow != null)
            {
                return singletonWindow.Visible;
            }
            return false;
        }

        public static void ShowTaskWindow(IWin32Window parent, string text, Control userControl)
        {
            ShowTaskWindow(parent, text, userControl, 0);
        }

        public static void ShowTaskWindow(string text, Control userControl, int secToClose)
        {
            ShowTaskWindow(null, text, userControl, secToClose);
        }

        public static void ShowTaskWindow(string text, Control userControl)
        {
            ShowTaskWindow(null, text, userControl);
        }

        public static void CancelAutoClose()
        {
            if (singletonWindow != null)
            {
                singletonWindow.CancelTimer = true;
            }
        }

        public static void ForceClose()
        {
            if (singletonWindow != null)
            {
                CancelAutoClose();
                singletonWindow.Close();
                singletonWindow.Dispose();
                singletonWindow = null;
            }
        }

        public MetroTaskWindow()
        {
            controlContainer = new MetroPanel();
            base.Controls.Add(controlContainer);
        }

        public MetroTaskWindow(int duration, Control userControl)
            : this()
        {
            controlContainer.Controls.Add(userControl);
            userControl.Dock = DockStyle.Fill;
            closeTime = duration * 500;
            if (closeTime > 0)
            {
                timer = DelayedCall.Start(UpdateProgress, 5);
            }
        }

        protected override void OnActivated(EventArgs e)
        {
            if (!isInitialized)
            {
                controlContainer.Theme = base.Theme;
                controlContainer.Style = base.Style;
                controlContainer.StyleManager = base.StyleManager;
                base.MaximizeBox = false;
                base.MinimizeBox = false;
                base.Movable = true;
                base.TopMost = true;
                base.Size = new Size(400, 200);
                Taskbar taskbar = new Taskbar();
                switch (taskbar.Position)
                {
                    case TaskbarPosition.Left:
                        base.Location = new Point(taskbar.Bounds.Width + 5, taskbar.Bounds.Height - base.Height - 5);
                        break;
                    case TaskbarPosition.Top:
                        base.Location = new Point(taskbar.Bounds.Width - base.Width - 5, taskbar.Bounds.Height + 5);
                        break;
                    case TaskbarPosition.Right:
                        base.Location = new Point(taskbar.Bounds.X - base.Width - 5, taskbar.Bounds.Height - base.Height - 5);
                        break;
                    case TaskbarPosition.Bottom:
                        base.Location = new Point(taskbar.Bounds.Width - base.Width - 5, taskbar.Bounds.Y - base.Height - 5);
                        break;
                    default:
                        base.Location = new Point(Screen.PrimaryScreen.Bounds.Width - base.Width - 5, Screen.PrimaryScreen.Bounds.Height - base.Height - 5);
                        break;
                }
                controlContainer.Location = new Point(0, 60);
                controlContainer.Size = new Size(base.Width - 40, base.Height - 80);
                controlContainer.Anchor = (AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right);
                controlContainer.AutoScroll = false;
                controlContainer.HorizontalScrollbar = false;
                controlContainer.VerticalScrollbar = false;
                controlContainer.Refresh();
                if (base.StyleManager != null)
                {
                    base.StyleManager.Update();
                }
                isInitialized = true;
                MoveAnimation moveAnimation = new MoveAnimation();
                moveAnimation.Start(controlContainer, new Point(20, 60), TransitionType.EaseInOutCubic, 15);
            }
            base.OnActivated(e);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            using (SolidBrush brush = new SolidBrush(MetroPaint.BackColor.Form(base.Theme)))
            {
                e.Graphics.FillRectangle(brush, new Rectangle(base.Width - progressWidth, 0, progressWidth, 5));
            }
        }

        private void UpdateProgress()
        {
            if (elapsedTime == closeTime)
            {
                timer.Dispose();
                timer = null;
                Close();
                return;
            }
            elapsedTime += 5;
            if (cancelTimer)
            {
                elapsedTime = 0;
            }
            double num = elapsedTime / (closeTime / 100.0);
            progressWidth = (int)(Width * (num / 100.0));
            Invalidate(new Rectangle(0, 0, base.Width, 5));
            if (!cancelTimer)
            {
                timer.Reset();
            }
        }
    }
}
