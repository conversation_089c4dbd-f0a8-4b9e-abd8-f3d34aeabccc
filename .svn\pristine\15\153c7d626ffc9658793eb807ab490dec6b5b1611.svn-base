﻿using System;
using System.Runtime.InteropServices;
using System.Runtime.InteropServices.ComTypes;

namespace OCRTools.WebBroswerEx
{
    public class SecurityManagerCOM
    {
        public const int S_OK = 0x00000000;
        public const int S_FALSE = 0x00000001;
        public const int E_NOINTERFACE = unchecked((int)0x80004002);
        public const int INET_E_DEFAULT_ACTION = unchecked((int)0x800C0011);

        public static Guid IID_IProfferService = new Guid("cb728b20-f786-11ce-92ad-00aa00a74cd0");
        public static Guid SID_SProfferService = new Guid("cb728b20-f786-11ce-92ad-00aa00a74cd0");
        public static Guid IID_IInternetSecurityManager = new Guid("79eac9ee-baf9-11ce-8c82-00aa004ba90b");

        [ComImport,
        Guid("6d5140c1-7436-11ce-8034-00aa006009fa"),
        InterfaceType(ComInterfaceType.InterfaceIsIUnknown),
        ComVisible(false)]
        public interface IServiceProvider
        {
            [return: MarshalAs(UnmanagedType.I4)]
            [PreserveSig]
            int QueryService(ref Guid guidService, ref Guid riid, [MarshalAs(UnmanagedType.Interface)] out object ppvObject);
        }

        [ComImport,
        Guid("cb728b20-f786-11ce-92ad-00aa00a74cd0"),
        InterfaceType(ComInterfaceType.InterfaceIsIUnknown),
        ComVisible(false)]
        public interface IProfferService
        {
            [return: MarshalAs(UnmanagedType.I4)]
            [PreserveSig]
            int ProfferService(ref Guid guidService, IServiceProviderForIInternetSecurityManager psp, ref int cookie);

            [return: MarshalAs(UnmanagedType.I4)]
            [PreserveSig]
            int RevokeService(int cookie);
        }


        [ComImport,
        Guid("6d5140c1-7436-11ce-8034-00aa006009fa"),
        InterfaceType(ComInterfaceType.InterfaceIsIUnknown),
        ComVisible(false)]
        public interface IServiceProviderForIInternetSecurityManager
        {
            [return: MarshalAs(UnmanagedType.I4)]
            [PreserveSig]
            int QueryService(ref Guid guidService, ref Guid riid, [MarshalAs(UnmanagedType.Interface)] out IInternetSecurityManager ppvObject);
        }

        [ComImport,
        Guid("79eac9ed-baf9-11ce-8c82-00aa004ba90b"),
        InterfaceType(ComInterfaceType.InterfaceIsIUnknown),
        ComVisible(false)]
        public interface IInternetSecurityMgrSite
        {
            [return: MarshalAs(UnmanagedType.I4)]
            [PreserveSig]
            int GetWindow(out IntPtr hwnd);

            [return: MarshalAs(UnmanagedType.I4)]
            [PreserveSig]
            int EnableModeless([In, MarshalAs(UnmanagedType.Bool)] Boolean fEnable);
        }

        public enum URLZONE
        {
            URLZONE_LOCAL_MACHINE = 0,
            URLZONE_INTRANET = URLZONE_LOCAL_MACHINE + 1,
            URLZONE_TRUSTED = URLZONE_INTRANET + 1,
            URLZONE_INTERNET = URLZONE_TRUSTED + 1,
            URLZONE_UNTRUSTED = URLZONE_INTERNET + 1,
        }

        public const int URLACTION_MIN = 0x00001000;

        public const int URLACTION_DOWNLOAD_MIN = 0x00001000;
        public const int URLACTION_DOWNLOAD_SIGNED_ACTIVEX = 0x00001001;
        public const int URLACTION_DOWNLOAD_UNSIGNED_ACTIVEX = 0x00001004;
        public const int URLACTION_DOWNLOAD_CURR_MAX = 0x00001004;
        public const int URLACTION_DOWNLOAD_MAX = 0x000011FF;

        public const int URLACTION_ACTIVEX_MIN = 0x00001200;
        public const int URLACTION_ACTIVEX_RUN = 0x00001200;
        public const int URLPOLICY_ACTIVEX_CHECK_LIST = 0x00010000;
        public const int URLACTION_ACTIVEX_OVERRIDE_OBJECT_SAFETY = 0x00001201;
        public const int URLACTION_ACTIVEX_OVERRIDE_DATA_SAFETY = 0x00001202;
        public const int URLACTION_ACTIVEX_OVERRIDE_SCRIPT_SAFETY = 0x00001203;
        public const int URLACTION_SCRIPT_OVERRIDE_SAFETY = 0x00001401;
        public const int URLACTION_ACTIVEX_CONFIRM_NOOBJECTSAFETY = 0x00001204;
        public const int URLACTION_ACTIVEX_TREATASUNTRUSTED = 0x00001205;
        public const int URLACTION_ACTIVEX_NO_WEBOC_SCRIPT = 0x00001206;
        public const int URLACTION_ACTIVEX_CURR_MAX = 0x00001206;
        public const int URLACTION_ACTIVEX_MAX = 0x000013ff;

        public const int URLACTION_SCRIPT_MIN = 0x00001400;
        public const int URLACTION_SCRIPT_RUN = 0x00001400;
        public const int URLACTION_SCRIPT_JAVA_USE = 0x00001402;
        public const int URLACTION_SCRIPT_SAFE_ACTIVEX = 0x00001405;
        public const int URLACTION_CROSS_DOMAIN_DATA = 0x00001406;
        public const int URLACTION_SCRIPT_PASTE = 0x00001407;
        public const int URLACTION_SCRIPT_CURR_MAX = 0x00001407;
        public const int URLACTION_SCRIPT_MAX = 0x000015ff;

        public const int URLACTION_HTML_MIN = 0x00001600;
        public const int URLACTION_HTML_SUBMIT_FORMS = 0x00001601; // aggregate next two
        public const int URLACTION_HTML_SUBMIT_FORMS_FROM = 0x00001602; //
        public const int URLACTION_HTML_SUBMIT_FORMS_TO = 0x00001603; //
        public const int URLACTION_HTML_FONT_DOWNLOAD = 0x00001604;
        public const int URLACTION_HTML_JAVA_RUN = 0x00001605; // derive from Java custom policy
        public const int URLACTION_HTML_USERDATA_SAVE = 0x00001606;
        public const int URLACTION_HTML_SUBFRAME_NAVIGATE = 0x00001607;
        public const int URLACTION_HTML_META_REFRESH = 0x00001608;
        public const int URLACTION_HTML_MIXED_CONTENT = 0x00001609;
        public const int URLACTION_HTML_MAX = 0x000017ff;

        public const int URLACTION_SHELL_MIN = 0x00001800;
        public const int URLACTION_SHELL_INSTALL_DTITEMS = 0x00001800;
        public const int URLACTION_SHELL_MOVE_OR_COPY = 0x00001802;
        public const int URLACTION_SHELL_FILE_DOWNLOAD = 0x00001803;
        public const int URLACTION_SHELL_VERB = 0x00001804;
        public const int URLACTION_SHELL_WEBVIEW_VERB = 0x00001805;
        public const int URLACTION_SHELL_SHELLEXECUTE = 0x00001806;
        public const int URLACTION_SHELL_CURR_MAX = 0x00001806;
        public const int URLACTION_SHELL_MAX = 0x000019ff;

        public const int URLACTION_NETWORK_MIN = 0x00001A00;

        public const int URLACTION_CREDENTIALS_USE = 0x00001A00;
        public const int URLPOLICY_CREDENTIALS_SILENT_LOGON_OK = 0x00000000;
        public const int URLPOLICY_CREDENTIALS_MUST_PROMPT_USER = 0x00010000;
        public const int URLPOLICY_CREDENTIALS_CONDITIONAL_PROMPT = 0x00020000;
        public const int URLPOLICY_CREDENTIALS_ANONYMOUS_ONLY = 0x00030000;

        public const int URLACTION_AUTHENTICATE_CLIENT = 0x00001A01;
        public const int URLPOLICY_AUTHENTICATE_CLEARTEXT_OK = 0x00000000;
        public const int URLPOLICY_AUTHENTICATE_CHALLENGE_RESPONSE = 0x00010000;
        public const int URLPOLICY_AUTHENTICATE_MUTUAL_ONLY = 0x00030000;


        public const int URLACTION_COOKIES = 0x00001A02;
        public const int URLACTION_COOKIES_SESSION = 0x00001A03;

        public const int URLACTION_CLIENT_CERT_PROMPT = 0x00001A04;

        public const int URLACTION_COOKIES_THIRD_PARTY = 0x00001A05;
        public const int URLACTION_COOKIES_SESSION_THIRD_PARTY = 0x00001A06;

        public const int URLACTION_COOKIES_ENABLED = 0x00001A10;

        public const int URLACTION_NETWORK_CURR_MAX = 0x00001A10;
        public const int URLACTION_NETWORK_MAX = 0x00001Bff;


        public const int URLACTION_JAVA_MIN = 0x00001C00;
        public const int URLACTION_JAVA_PERMISSIONS = 0x00001C00;
        public const int URLPOLICY_JAVA_PROHIBIT = 0x00000000;
        public const int URLPOLICY_JAVA_HIGH = 0x00010000;
        public const int URLPOLICY_JAVA_MEDIUM = 0x00020000;
        public const int URLPOLICY_JAVA_LOW = 0x00030000;
        public const int URLPOLICY_JAVA_CUSTOM = 0x00800000;
        public const int URLACTION_JAVA_CURR_MAX = 0x00001C00;
        public const int URLACTION_JAVA_MAX = 0x00001Cff;


        // The following Infodelivery actions should have no default policies
        // in the registry.  They assume that no default policy means fall
        // back to the global restriction.  If an admin sets a policy per
        // zone, then it overrides the global restriction.

        public const int URLACTION_INFODELIVERY_MIN = 0x00001D00;
        public const int URLACTION_INFODELIVERY_NO_ADDING_CHANNELS = 0x00001D00;
        public const int URLACTION_INFODELIVERY_NO_EDITING_CHANNELS = 0x00001D01;
        public const int URLACTION_INFODELIVERY_NO_REMOVING_CHANNELS = 0x00001D02;
        public const int URLACTION_INFODELIVERY_NO_ADDING_SUBSCRIPTIONS = 0x00001D03;
        public const int URLACTION_INFODELIVERY_NO_EDITING_SUBSCRIPTIONS = 0x00001D04;
        public const int URLACTION_INFODELIVERY_NO_REMOVING_SUBSCRIPTIONS = 0x00001D05;
        public const int URLACTION_INFODELIVERY_NO_CHANNEL_LOGGING = 0x00001D06;
        public const int URLACTION_INFODELIVERY_CURR_MAX = 0x00001D06;
        public const int URLACTION_INFODELIVERY_MAX = 0x00001Dff;
        public const int URLACTION_CHANNEL_SOFTDIST_MIN = 0x00001E00;
        public const int URLACTION_CHANNEL_SOFTDIST_PERMISSIONS = 0x00001E05;
        public const int URLPOLICY_CHANNEL_SOFTDIST_PROHIBIT = 0x00010000;
        public const int URLPOLICY_CHANNEL_SOFTDIST_PRECACHE = 0x00020000;
        public const int URLPOLICY_CHANNEL_SOFTDIST_AUTOINSTALL = 0x00030000;
        public const int URLACTION_CHANNEL_SOFTDIST_MAX = 0x00001Eff;

        // For each action specified above the system maintains
        // a set of policies for the action. 
        // The only policies supported currently are permissions (i.e. is something allowed)
        // and logging status. 
        // IMPORTANT: If you are defining your own policies don't overload the meaning of the
        // loword of the policy. You can use the hiword to store any policy bits which are only
        // meaningful to your action.
        // For an example of how to do this look at the URLPOLICY_JAVA above

        // Permissions 
        public const int URLPOLICY_ALLOW = 0x00;
        public const int URLPOLICY_QUERY = 0x01;
        public const int URLPOLICY_DISALLOW = 0x03;

        // Notifications are not done when user already queried.
        public const int URLPOLICY_NOTIFY_ON_ALLOW = 0x10;
        public const int URLPOLICY_NOTIFY_ON_DISALLOW = 0x20;

        // Logging is done regardless of whether user was queried.
        public const int URLPOLICY_LOG_ON_ALLOW = 0x40;
        public const int URLPOLICY_LOG_ON_DISALLOW = 0x80;

        public const int URLPOLICY_MASK_PERMISSIONS = 0x0f;


        public const int URLPOLICY_DONTCHECKDLGBOX = 0x100;

        [ComImport, Guid("79eac9ee-baf9-11ce-8c82-00aa004ba90b"),
        InterfaceType(ComInterfaceType.InterfaceIsIUnknown),
        ComVisible(false)]
        public interface IInternetSecurityManager
        {
            [return: MarshalAs(UnmanagedType.I4)]
            [PreserveSig]
            int SetSecuritySite([In] IInternetSecurityMgrSite pSite);

            [return: MarshalAs(UnmanagedType.I4)]
            [PreserveSig]
            int GetSecuritySite([Out] IInternetSecurityMgrSite pSite);

            [return: MarshalAs(UnmanagedType.I4)]
            [PreserveSig]
            int MapUrlToZone([In, MarshalAs(UnmanagedType.LPWStr)] String pwszUrl, out int pdwZone, int dwFlags);

            [return: MarshalAs(UnmanagedType.I4)]
            [PreserveSig]
            int GetSecurityId([MarshalAs(UnmanagedType.LPWStr)] string pwszUrl, IntPtr pbSecurityId, ref uint pcbSecurityId, uint dwReserved);

            [return: MarshalAs(UnmanagedType.I4)]
            [PreserveSig]
            int ProcessUrlAction([In, MarshalAs(UnmanagedType.LPWStr)] String pwszUrl, int dwAction, out byte pPolicy, int cbPolicy, byte pContext, int cbContext, int dwFlags, int dwReserved);

            [return: MarshalAs(UnmanagedType.I4)]
            [PreserveSig]
            int QueryCustomPolicy([In, MarshalAs(UnmanagedType.LPWStr)] String pwszUrl, ref Guid guidKey, byte ppPolicy, int pcbPolicy, byte pContext, int cbContext, int dwReserved);

            [return: MarshalAs(UnmanagedType.I4)]
            [PreserveSig]
            int SetZoneMapping(int dwZone, [In, MarshalAs(UnmanagedType.LPWStr)] String lpszPattern, int dwFlags);

            [return: MarshalAs(UnmanagedType.I4)]
            [PreserveSig]
            int GetZoneMappings(int dwZone, out IEnumString ppenumString, int dwFlags);
        }
    }
}
