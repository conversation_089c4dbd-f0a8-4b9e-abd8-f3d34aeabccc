﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.InteropServices.WindowsRuntime</name>
  </assembly>
  <members>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.DefaultInterfaceAttribute">
      <summary>Specifica l'interfaccia predefinita di una classe Windows Runtime gestita.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.DefaultInterfaceAttribute.#ctor(System.Type)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.WindowsRuntime.DefaultInterfaceAttribute" />. </summary>
      <param name="defaultInterface">Il tipo di interfaccia specificato come interfaccia predefinita della classe a cui viene applicato l'attributo. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.DefaultInterfaceAttribute.DefaultInterface">
      <summary>Ottiene il tipo dell'interfaccia predefinita. </summary>
      <returns>Tipo dell'interfaccia predefinita. </returns>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken">
      <summary>Token restituito quando un gestore eventi viene aggiunto a un evento Windows Runtime .Il token viene utilizzato per rimuovere il gestore eventi dall'evento in un secondo momento.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken.Equals(System.Object)">
      <summary>Restituisce un valore che indica se l'oggetto corrente è uguale all'oggetto specificato. </summary>
      <returns>true se l'oggetto corrente è uguale a <paramref name="obj" />; in caso contrario, false.</returns>
      <param name="obj">Oggetto da confrontare.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken.GetHashCode">
      <summary>Restituisce il codice hash per l'istanza. </summary>
      <returns>Codice hash per l'istanza. </returns>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken.op_Equality(System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken,System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken)">
      <summary>Indica se due istanze <see cref="T:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken" /> sono uguali. </summary>
      <returns>true se i due oggetti sono uguali; in caso contrario, false. </returns>
      <param name="left">Prima istanza da confrontare. </param>
      <param name="right">Seconda istanza da confrontare. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken.op_Inequality(System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken,System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken)">
      <summary>Indica se due istanze di <see cref="T:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken" /> non sono uguali.</summary>
      <returns>true se le due istanze non sono uguali; in caso contrario, false. </returns>
      <param name="left">Prima istanza da confrontare. </param>
      <param name="right">Seconda istanza da confrontare. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1">
      <summary>Archivia i mapping tra i delegati e i token di evento, per supportare l'implementazione di un evento Windows Runtime nel codice gestito.</summary>
      <typeparam name="T">Tipo delegato per i gestori eventi di un determinato evento. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1" />. </summary>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="T" /> non è un tipo delegato. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1.AddEventHandler(`0)">
      <summary>Aggiunge il gestore eventi specificato alla tabella e all'elenco delle chiamate e restituisce un token che può essere utilizzato per rimuovere il gestore eventi. </summary>
      <returns>Token che può essere utilizzato per rimuovere il gestore eventi della tabella e dall'elenco di chiamate. </returns>
      <param name="handler">Il gestore eventi da aggiungere. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1.GetOrCreateEventRegistrationTokenTable(System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable{`0}@)">
      <summary>Restituisce la tabella dei token di registrazione dell'evento specificata, se non è null. In caso contrario, restituisce una nuova tabella dei token di registrazione dell'evento. </summary>
      <returns>La tabella dei token di registrazione dell'evento specificata da <paramref name="refEventTable" />, se non è null; in caso contrario, una nuova tabella dei token di registrazione dell'evento. </returns>
      <param name="refEventTable">Tabella dei token di registrazione eventi, passata per riferimento. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1.InvocationList">
      <summary>Ottiene o imposta un delegato di tipo <paramref name="T" /> il cui elenco chiamate include tutti i delegati del gestore eventi che sono stati aggiunti e che non sono ancora stati rimossi.Richiamando il delegato vengono richiamati tutti i gestori eventi.</summary>
      <returns>Delegato del tipo <paramref name="T" /> che rappresenta tutti i delegati del gestore eventi attualmente registrati per un evento. </returns>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1.RemoveEventHandler(System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken)">
      <summary>Rimuove il gestore eventi associato al token specificato dalla tabella e dall'elenco di chiamate. </summary>
      <param name="token">Token che è stato restituito quando il gestore eventi è stato aggiunto. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.EventRegistrationTokenTable`1.RemoveEventHandler(`0)">
      <summary>Rimuove il delegato del gestore eventi specificato dalla tabella e dall'elenco di chiamate. </summary>
      <param name="handler">Il gestore eventi da rimuovere. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.IActivationFactory">
      <summary>Consente l'attivazione delle classi da parte di Windows Runtime. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.IActivationFactory.ActivateInstance">
      <summary>Restituisce una nuova istanza della classe Windows Runtime creata dall'interfaccia <see cref="T:System.Runtime.InteropServices.WindowsRuntime.IActivationFactory" />. </summary>
      <returns>Nuova istanza della classe Windows Runtime. </returns>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute">
      <summary>Specifica la versione del tipo di destinazione che ha implementato per primo l'interfaccia specificata.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute.#ctor(System.Type,System.Byte,System.Byte,System.Byte,System.Byte)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute" />, specificando l'interfaccia che il tipo di destinazione implementa e la prima versione in cui l'interfaccia è stata implementata. </summary>
      <param name="interfaceType">L'interfaccia implementata per prima nella versione specificata del tipo di destinazione. </param>
      <param name="majorVersion">Componente principale della versione del tipo di destinazione che ha implementato per primo l'oggetto <paramref name="interfaceType" />.</param>
      <param name="minorVersion">Componente secondario della versione del tipo di destinazione che ha implementato per primo l'oggetto <paramref name="interfaceType" />.</param>
      <param name="buildVersion">Componente di compilazione della versione del tipo di destinazione che ha implementato per primo l'oggetto <paramref name="interfaceType" />.</param>
      <param name="revisionVersion">Componente di revisione della versione del tipo di destinazione che ha implementato per primo l'oggetto <paramref name="interfaceType" />.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute.BuildVersion">
      <summary>Ottiene il componente di compilazione della versione del tipo di destinazione che ha implementato per primo l'interfaccia. </summary>
      <returns>Il componente di compilazione della versione.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute.InterfaceType">
      <summary>Ottiene il tipo dell'interfaccia che il tipo di destinazione implementa. </summary>
      <returns>Tipo di interfaccia. </returns>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute.MajorVersion">
      <summary>Ottiene il componente principale della versione del tipo di destinazione che ha implementato per primo l'interfaccia. </summary>
      <returns>Componente principale della versione.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute.MinorVersion">
      <summary>Ottiene il componente secondario della versione del tipo di destinazione che ha implementato per primo l'interfaccia. </summary>
      <returns>Componente secondario della versione. </returns>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.InterfaceImplementedInVersionAttribute.RevisionVersion">
      <summary>Ottiene il componente revisione della versione del tipo di destinazione che ha implementato per primo l'interfaccia. </summary>
      <returns>Componente revisione della versione.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.ReadOnlyArrayAttribute">
      <summary>Quando applicato a un parametro di matrice in un componente di Windows Runtime, specifica che il contenuto della matrice passata al parametro viene usato solo per l'input.Il chiamante prevede che la matrice resti invariata dopo la chiamata.Per informazioni importanti sui chiamanti scritti usando codice gestito, vedere la sezione Osservazioni.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.ReadOnlyArrayAttribute.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.WindowsRuntime.ReadOnlyArrayAttribute" />. </summary>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.ReturnValueNameAttribute">
      <summary>Specifica il nome del valore restituito di un metodo in un componente di Windows Runtime.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.ReturnValueNameAttribute.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.WindowsRuntime.ReturnValueNameAttribute" /> e specifica il nome del valore restituito.</summary>
      <param name="name">Nome del valore restituito. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.WindowsRuntime.ReturnValueNameAttribute.Name">
      <summary>Ottiene il nome specificato per il valore restituito di un metodo in un componente di Windows Runtime.</summary>
      <returns>Nome del valore restituito del metodo. </returns>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal">
      <summary>Fornisce metodi di supporto per il marshalling dei dati tra .NET Framework e Windows Runtime.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal.AddEventHandler``1(System.Func{``0,System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken},System.Action{System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken},``0)">
      <summary>Aggiunge il gestore eventi specificato a un evento di Windows Runtime .</summary>
      <param name="addMethod">Delegato che rappresenta il metodo che aggiunge gestori eventi all'evento Windows Runtime . </param>
      <param name="removeMethod">Delegato che rappresenta il metodo che rimuove gestori eventi dall'evento Windows Runtime . </param>
      <param name="handler">Delegato che rappresenta il gestore eventi aggiunto. </param>
      <typeparam name="T">Tipo del delegato che rappresenta il gestore di evento. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="addMethod" /> è null. - oppure -<paramref name="removeMethod" /> è null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal.FreeHString(System.IntPtr)">
      <summary>Libera l'elemento Windows Runtime HSTRING specificato. </summary>
      <param name="ptr">L'indirizzo di HSTRING da liberare.</param>
      <exception cref="T:System.PlatformNotSupportedException">Windows Runtime non è supportato dalla versione attuale del sistema operativo. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal.GetActivationFactory(System.Type)">
      <summary>Restituisce un oggetto che implementa l'interfaccia factory di attivazione per il tipo Windows Runtime specificato. </summary>
      <returns>Oggetto che implementa l'interfaccia fattore di archiviazione. </returns>
      <param name="type">Tipo di Windows Runtime per cui ottenere l'interfaccia della factory di attivazione. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="type" /> non rappresenta un tipo Windows Runtime, ovvero appartenente a Windows Runtime stesso o definito in un componente Windows Runtime. - oppure -L'oggetto specificato per <paramref name="type" /> non è stato fornito dal sistema di tipi Common Language Runtime. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> è null. </exception>
      <exception cref="T:System.TypeLoadException">La classe Windows Runtime non è registrata in modo corretto.Ad esempio, il file .winmd è stato individuato, ma Windows Runtime non è stato in grado di individuare l'implementazione.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal.PtrToStringHString(System.IntPtr)">
      <summary>Restituisce una stringa gestita che contiene una copia dell'elemento Windows Runtime HSTRING specificato. </summary>
      <returns>Stringa gestita che contiene una copia di HSTRING se <paramref name="ptr" /> non è <see cref="F:System.IntPtr.Zero" />; in caso contrario, <see cref="F:System.String.Empty" />. </returns>
      <param name="ptr">Puntatore non gestito al HSTRING da copiare. </param>
      <exception cref="T:System.PlatformNotSupportedException">Windows Runtime non è supportato dalla versione attuale del sistema operativo. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal.RemoveAllEventHandlers(System.Action{System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken})">
      <summary>Rimuove tutti i gestori eventi che possono essere rimossi mediante il metodo specificato. </summary>
      <param name="removeMethod">Delegato che rappresenta il metodo che rimuove gestori eventi dall'evento Windows Runtime . </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="removeMethod" /> è null. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal.RemoveEventHandler``1(System.Action{System.Runtime.InteropServices.WindowsRuntime.EventRegistrationToken},``0)">
      <summary>Rimuove il gestore eventi specificato da un evento Windows Runtime. </summary>
      <param name="removeMethod">Delegato che rappresenta il metodo che rimuove gestori eventi dall'evento Windows Runtime . </param>
      <param name="handler">Gestore eventi rimosso. </param>
      <typeparam name="T">Tipo del delegato che rappresenta il gestore di evento. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="removeMethod" /> è null. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeMarshal.StringToHString(System.String)">
      <summary>Alloca un Windows Runtime HSTRING e vi copia la stringa gestita specificata. </summary>
      <returns>Puntatore non gestito al nuovo HSTRING o <see cref="F:System.IntPtr.Zero" /> se <paramref name="s" /> è <see cref="F:System.String.Empty" />.. </returns>
      <param name="s">Stringa gestita da copiare. </param>
      <exception cref="T:System.PlatformNotSupportedException">Windows Runtime non è supportato dalla versione attuale del sistema operativo. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> è null. </exception>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.WriteOnlyArrayAttribute">
      <summary>Quando applicato a un parametro di matrice in un componente di Windows Runtime, specifica che il contenuto di una matrice passata al parametro viene usato solo per l'output.Il chiamante non garantisce la corretta inizializzazione del contenuto e il metodo chiamato non deve leggere il contenuto.Per informazioni importanti sui chiamanti scritti usando codice gestito, vedere la sezione Osservazioni.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WriteOnlyArrayAttribute.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.WindowsRuntime.WriteOnlyArrayAttribute" />. </summary>
    </member>
  </members>
</doc>