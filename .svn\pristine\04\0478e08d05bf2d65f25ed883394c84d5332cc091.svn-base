using System.Drawing;

namespace OCRTools
{
    public class Status
    {
        public Color LastUsedColor { get; set; }

        public bool LastIsDot { get; set; }

        public bool LastIsOutline { get; set; }

        public float LastFontSize { get; set; }

        public string LastFontstyle { get; set; }

        public int LastUsedPenWidth { get; set; }

        public bool LastIsArrowBoth { get; set; }

        public DrawToolType ActiveTools { get; set; }
    }
}