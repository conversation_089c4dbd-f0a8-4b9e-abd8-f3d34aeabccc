﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Workflow.Runtime</name>
  </assembly>
  <members>
    <member name="T:System.Activities.Statements.Interop">
      <summary>An activity that manages the execution of an <see cref="T:System.Workflow.ComponentModel.Activity" /> within a workflow.</summary>
    </member>
    <member name="M:System.Activities.Statements.Interop.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Activities.Statements.Interop" /> class. </summary>
    </member>
    <member name="P:System.Activities.Statements.Interop.ActivityMetaProperties">
      <summary>The collection of name-value pairs that corresponds to the metadata of the <see cref="T:System.Workflow.ComponentModel.Activity" />, such as an activity’s <see cref="P:System.Workflow.ComponentModel.Activity.Name" /> property, or a <see cref="T:System.Workflow.Activities.WhileActivity" /> activity’s <see cref="P:System.Workflow.Activities.While.Condition" /> property.</summary>
      <returns>The collection of metadata.</returns>
    </member>
    <member name="P:System.Activities.Statements.Interop.ActivityProperties">
      <summary>Gets the collection of name-value pairs that corresponds to the input and output properties of the <see cref="T:System.Workflow.ComponentModel.Activity" />.</summary>
      <returns>The collection of properties.</returns>
    </member>
    <member name="P:System.Activities.Statements.Interop.ActivityType">
      <summary>Gets or sets the type of the activity contained by the <see cref="T:System.Activities.Statements.Interop" /> activity.</summary>
      <returns>The type of the activity contained by the <see cref="T:System.Activities.Statements.Interop" /> activity.</returns>
    </member>
    <member name="M:System.Activities.Statements.Interop.System#ComponentModel#ICustomTypeDescriptor#GetAttributes">
      <summary>Returns the collection of attributes for the contained <see cref="T:System.Workflow.ComponentModel.Activity" />.</summary>
      <returns>The attribute collection.</returns>
    </member>
    <member name="M:System.Activities.Statements.Interop.System#ComponentModel#ICustomTypeDescriptor#GetClassName">
      <summary>Returns the name of the class of the contained <see cref="T:System.Workflow.ComponentModel.Activity" />.</summary>
      <returns>The class name.</returns>
    </member>
    <member name="M:System.Activities.Statements.Interop.System#ComponentModel#ICustomTypeDescriptor#GetComponentName">
      <summary>Returns the name of the contained <see cref="T:System.Workflow.ComponentModel.Activity" />.</summary>
      <returns>The activity name.</returns>
    </member>
    <member name="M:System.Activities.Statements.Interop.System#ComponentModel#ICustomTypeDescriptor#GetConverter">
      <summary>Returns the associated type converter for the contained <see cref="T:System.Workflow.ComponentModel.Activity" />.</summary>
      <returns>The type converter.</returns>
    </member>
    <member name="M:System.Activities.Statements.Interop.System#ComponentModel#ICustomTypeDescriptor#GetDefaultEvent">
      <summary>Returns the default event for the contained <see cref="T:System.Workflow.ComponentModel.Activity" />.</summary>
      <returns>The default event.</returns>
    </member>
    <member name="M:System.Activities.Statements.Interop.System#ComponentModel#ICustomTypeDescriptor#GetDefaultProperty">
      <summary>Returns the default property for the contained <see cref="T:System.Workflow.ComponentModel.Activity" />.</summary>
      <returns>The default property.</returns>
    </member>
    <member name="M:System.Activities.Statements.Interop.System#ComponentModel#ICustomTypeDescriptor#GetEditor(System.Type)">
      <summary>Returns the editor for the contained <see cref="T:System.Workflow.ComponentModel.Activity" />.</summary>
      <returns>The editor.</returns>
      <param name="editorBaseType">The type of the requested editor.</param>
    </member>
    <member name="M:System.Activities.Statements.Interop.System#ComponentModel#ICustomTypeDescriptor#GetEvents">
      <summary>Returns the collection of events for the contained <see cref="T:System.Workflow.ComponentModel.Activity" />.</summary>
      <returns>The collection of events.</returns>
    </member>
    <member name="M:System.Activities.Statements.Interop.System#ComponentModel#ICustomTypeDescriptor#GetEvents(System.Attribute[])">
      <summary>Returns the collection of events for the contained <see cref="T:System.Workflow.ComponentModel.Activity" /> using the specified array of attributes as a filter.</summary>
      <returns>The collection of events.</returns>
      <param name="attributes">The attributes used to filter the returned events.</param>
    </member>
    <member name="M:System.Activities.Statements.Interop.System#ComponentModel#ICustomTypeDescriptor#GetProperties">
      <summary>Returns the collection of properties for the contained <see cref="T:System.Workflow.ComponentModel.Activity" />.</summary>
      <returns>A <see cref="T:System.ComponentModel.PropertyDescriptorCollection" />.</returns>
    </member>
    <member name="M:System.Activities.Statements.Interop.System#ComponentModel#ICustomTypeDescriptor#GetProperties(System.Attribute[])">
      <summary>Returns the collection of properties for the contained <see cref="T:System.Workflow.ComponentModel.Activity" /> using a specified array of attributes as a filter.</summary>
      <returns>A collection of properties.</returns>
      <param name="attributes">The array of attributes used to filter the properties.</param>
    </member>
    <member name="M:System.Activities.Statements.Interop.System#ComponentModel#ICustomTypeDescriptor#GetPropertyOwner(System.ComponentModel.PropertyDescriptor)">
      <summary>Returns the owner of the specified property descriptor or the <see cref="T:System.Activities.Statements.Interop" /> activity itself if the property descriptor has no owner.</summary>
      <returns>The property owner.</returns>
      <param name="pd">The property whose owner is to be retrieved.</param>
    </member>
    <member name="T:System.Activities.Tracking.InteropTrackingRecord">
      <summary>Represents the data sent to tracking participants when tracked records occur in an <see cref="T:System.Activities.Statements.Interop" /> activity within a workflow.</summary>
    </member>
    <member name="M:System.Activities.Tracking.InteropTrackingRecord.#ctor(System.Activities.Tracking.InteropTrackingRecord)">
      <summary>Creates a new instance of the <see cref="T:System.Activities.Tracking.InteropTrackingRecord" /> class using the specified <see cref="T:System.Activities.Tracking.InteropTrackingRecord" />.</summary>
      <param name="record">The tracking record.</param>
    </member>
    <member name="M:System.Activities.Tracking.InteropTrackingRecord.#ctor(System.String,System.Workflow.Runtime.Tracking.TrackingRecord)">
      <summary>Creates a new instance of the <see cref="T:System.Activities.Tracking.InteropTrackingRecord" /> class.</summary>
      <param name="activityDisplayName">The activity name</param>
      <param name="v1TrackingRecord">The tracking record of the <see cref="T:System.Workflow.ComponentModel.Activity" />.</param>
    </member>
    <member name="M:System.Activities.Tracking.InteropTrackingRecord.Clone">
      <summary>Creates a copy of the <see cref="T:System.Activities.Tracking.InteropTrackingRecord" />.</summary>
      <returns>A copy of the <see cref="T:System.Activities.Tracking.InteropTrackingRecord" />.</returns>
    </member>
    <member name="P:System.Activities.Tracking.InteropTrackingRecord.TrackingRecord">
      <summary>Returns a tracking record that represents the data sent to tracking participants when tracked events occur.</summary>
      <returns>The data sent to tracking participants when tracked events occur.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.CorrelationProperty">
      <summary>Represents a name and value pair used to correlate messages to specific receiving activities.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.CorrelationProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.CorrelationProperty" /> class. </summary>
      <param name="name">The name of the property used in the correlation set.</param>
      <param name="value">The value of the correlation set property.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.CorrelationProperty.Name">
      <summary>Gets the name of the property used in the correlation set.</summary>
      <returns>The name of the property used in the correlation set.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.CorrelationProperty.Value">
      <summary>Gets the value of the correlation set property.</summary>
      <returns>The value of the correlation set property.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.CorrelationToken">
      <summary>Manages the subscriptions of the <see cref="T:System.Workflow.Runtime.CorrelationProperty" /> to the owner activities. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.CorrelationToken.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.CorrelationToken" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.CorrelationToken.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.CorrelationToken" /> class using the name of the <see cref="T:System.Workflow.Runtime.CorrelationToken" />.</summary>
      <param name="name">The name of the <see cref="T:System.Workflow.Runtime.CorrelationToken" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.CorrelationToken.Initialize(System.Workflow.ComponentModel.Activity,System.Collections.Generic.ICollection{System.Workflow.Runtime.CorrelationProperty})">
      <summary>Fires correlation initialized events.</summary>
      <param name="activity">The name of the <see cref="T:System.Workflow.ComponentModel.Activity" /> that owns the correlation token.</param>
      <param name="propertyValues">A collection of property values in the <see cref="T:System.Workflow.Runtime.CorrelationToken" />.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Workflow.Runtime.CorrelationToken" /> was already initialized.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.CorrelationToken.Initialized">
      <summary>Gets a value indicating whether the <see cref="T:System.Workflow.Runtime.CorrelationToken" /> is initialized.</summary>
      <returns>true if the <see cref="T:System.Workflow.Runtime.CorrelationToken" /> is initialized; otherwise, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.CorrelationToken.Name">
      <summary>Gets or sets the name of the <see cref="T:System.Workflow.Runtime.CorrelationToken" />.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Runtime.CorrelationToken" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.CorrelationToken.OwnerActivityName">
      <summary>Gets or sets the name of the <see cref="T:System.Workflow.ComponentModel.Activity" /> that owns the correlation token.</summary>
      <returns>The name of the <see cref="T:System.Workflow.ComponentModel.Activity" /> that owns the correlation token.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.CorrelationToken.Properties">
      <summary>Gets the collection of <see cref="T:System.Workflow.Runtime.CorrelationProperty" /> objects in the <see cref="T:System.Workflow.Runtime.CorrelationToken" />.</summary>
      <returns>A collection of <see cref="T:System.Workflow.Runtime.CorrelationProperty" /> objects.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.CorrelationToken.SubscribeForCorrelationTokenInitializedEvent(System.Workflow.ComponentModel.Activity,System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.Runtime.CorrelationTokenEventArgs})">
      <summary>Subscribes the <see cref="T:System.Workflow.Runtime.CorrelationToken" /> to the initialized event.</summary>
      <param name="activity">The name of the <see cref="T:System.Workflow.ComponentModel.Activity" /> that owns the correlation token.</param>
      <param name="dataChangeListener">The <see cref="T:System.Workflow.Runtime.CorrelationTokenEventArgs" /> that is listening for a change in event data.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="activity" /> or <paramref name="dataChangeListener" /> are null references (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.CorrelationToken.UnsubscribeFromCorrelationTokenInitializedEvent(System.Workflow.ComponentModel.Activity,System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.Runtime.CorrelationTokenEventArgs})">
      <summary>Unsubscribes the <see cref="T:System.Workflow.Runtime.CorrelationToken" /> from the initialized event.</summary>
      <param name="activity">The name of the <see cref="T:System.Workflow.ComponentModel.Activity" /> that owns the correlation token.</param>
      <param name="dataChangeListener">The <see cref="T:System.Workflow.Runtime.CorrelationTokenEventArgs" /> that is listening for a change in event data.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="activity" /> or <paramref name="dataChangeListener" /> are null references (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.CorrelationTokenCollection">
      <summary>Represents a collection of <see cref="T:System.Workflow.Runtime.CorrelationToken" /> classes. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.CorrelationTokenCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.CorrelationTokenCollection" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Workflow.Runtime.CorrelationTokenCollection.CorrelationTokenCollectionProperty">
      <summary>Represents the <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> that targets the <see cref="T:System.Workflow.Runtime.CorrelationTokenCollection" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.CorrelationTokenCollection.GetCorrelationToken(System.Workflow.ComponentModel.Activity,System.String,System.String)">
      <summary>Gets the specified <see cref="T:System.Workflow.Runtime.CorrelationToken" />.</summary>
      <returns>The <see cref="T:System.Workflow.Runtime.CorrelationToken" /> with the specified <paramref name="correlationTokenName" />.</returns>
      <param name="activity">The Activity associated with the <see cref="T:System.Workflow.Runtime.CorrelationTokenCollection" />.</param>
      <param name="correlationTokenName">The name of the <see cref="T:System.Workflow.Runtime.CorrelationToken" />.</param>
      <param name="ownerActivityName">The name of the activity associated with the <see cref="T:System.Workflow.Runtime.CorrelationTokenCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="activity" />, <paramref name="correlationTokenName" />, or <paramref name="ownerActivityName" /> are null references (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.CorrelationTokenCollection.GetItem(System.String)">
      <summary>Gets the <see cref="T:System.Workflow.Runtime.CorrelationToken" /> with the specified key.</summary>
      <returns>The <see cref="T:System.Workflow.Runtime.CorrelationToken" /> with the specified key.</returns>
      <param name="key">The key for the <see cref="T:System.Workflow.Runtime.CorrelationToken" /> to retrieve. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.CorrelationTokenEventArgs">
      <summary>Contains the event data associated with the <see cref="T:System.Workflow.Runtime.CorrelationToken" />. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.CorrelationTokenEventArgs.CorrelationToken">
      <summary>Gets the current <see cref="T:System.Workflow.Runtime.CorrelationToken" />.</summary>
      <returns>The current <see cref="T:System.Workflow.Runtime.CorrelationToken" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.CorrelationTokenEventArgs.IsInitializing">
      <summary>Gets a value that indicates whether the <see cref="M:System.Workflow.Runtime.CorrelationToken.Initialize(System.Workflow.ComponentModel.Activity,System.Collections.Generic.ICollection{System.Workflow.Runtime.CorrelationProperty})" /> method has been executed.</summary>
      <returns>true if the <see cref="M:System.Workflow.Runtime.CorrelationToken.Initialize(System.Workflow.ComponentModel.Activity,System.Collections.Generic.ICollection{System.Workflow.Runtime.CorrelationProperty})" /> method has been executed; otherwise, false.  </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.IPendingWork">
      <summary>Provides methods to participate in a work batch.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.IPendingWork.Commit(System.Transactions.Transaction,System.Collections.ICollection)">
      <summary>Commits the list of work items by using the specified <see cref="T:System.Transactions.Transaction" /> object.</summary>
      <param name="transaction">The <see cref="T:System.Transactions.Transaction" /> associated with the pending work.</param>
      <param name="items">The work items to be committed.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.IPendingWork.Complete(System.Boolean,System.Collections.ICollection)">
      <summary>Called when the transaction has completed.</summary>
      <param name="succeeded">true if the transaction succeeded; otherwise, false.</param>
      <param name="items">An <see cref="T:System.Collections.ICollection" /> of work items.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.IPendingWork.MustCommit(System.Collections.ICollection)">
      <summary>Allows the items in the work batch to assert whether they must commit immediately.</summary>
      <returns>true if any item in the collection must be committed immediately; otherwise, false.</returns>
      <param name="items">An <see cref="T:System.Collections.ICollection" /> of work items.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.IWorkBatch">
      <summary>Provides methods to add work to a work batch. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.IWorkBatch.Add(System.Workflow.Runtime.IPendingWork,System.Object)">
      <summary>Adds a pending work item to a work batch.</summary>
      <param name="work">An <see cref="T:System.Workflow.Runtime.IPendingWork" /> object associated with the <paramref name="workItem" />.</param>
      <param name="workItem">An object on which work is to be performed.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.ServicesExceptionNotHandledEventArgs">
      <summary>Provides data for the <see cref="E:System.Workflow.Runtime.WorkflowRuntime.ServicesExceptionNotHandled" /> event.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.ServicesExceptionNotHandledEventArgs.Exception">
      <summary>Gets the exception that could not be handled by the service.</summary>
      <returns>A <see cref="T:System.Exception" /> that represents the exception that could not be handled by the service.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.ServicesExceptionNotHandledEventArgs.WorkflowInstanceId">
      <summary>Gets the <see cref="T:System.Guid" /> of the workflow instance associated with the unhandled exception. </summary>
      <returns>The <see cref="T:System.Guid" /> of the workflow instance associated with the unhandled exception.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.TimerEventSubscription">
      <summary>Represents a subscription to a timer event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.TimerEventSubscription.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.TimerEventSubscription.#ctor(System.Guid,System.DateTime)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> class by using the specified <see cref="T:System.Guid" /> of a workflow instance and the specified expiration <see cref="T:System.DateTime" />. </summary>
      <param name="workflowInstanceId">The <see cref="T:System.Guid" /> of the workflow instance associated with this subscription.</param>
      <param name="expiresAt">A <see cref="T:System.DateTime" /> that represents the time in Universal Coordinated Time (UTC) at which the timer associated with this subscription is expected to expire.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.TimerEventSubscription.#ctor(System.Guid,System.Guid,System.DateTime)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> class by using the specified subscription Id, the specified <see cref="T:System.Guid" /> of a workflow instance, and the specified expiration <see cref="T:System.DateTime" />.</summary>
      <param name="timerId">The <see cref="T:System.Guid" /> for this subscription.</param>
      <param name="workflowInstanceId">The <see cref="T:System.Guid" /> of the workflow instance for which this subscription was created.</param>
      <param name="expiresAt">A <see cref="T:System.DateTime" /> that represents the time in Universal Coordinated Time (UTC) at which the timer for this subscription is expected to expire.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.TimerEventSubscription.ExpiresAt">
      <summary>Gets the expected expiration time of the timer associated with this subscription.</summary>
      <returns>A <see cref="T:System.DateTime" /> that represents the time in Universal Coordinated Time (UTC) at which the timer associated with this subscription is expected to expire.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.TimerEventSubscription.QueueName">
      <summary>Represents the name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> that the <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> sends a message to when the timer subscription expires.</summary>
      <returns>The <see cref="T:System.IComparable" /> interface for a <see cref="T:System.Guid" /> object.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.TimerEventSubscription.SubscriptionId">
      <summary>Unique identifier that represents a timer event</summary>
      <returns>
        <see cref="T:System.Guid" /> representing the unique identifier for a timer event</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.TimerEventSubscription.WorkflowInstanceId">
      <summary>Identifier of the workflow associated with the timer subscription</summary>
      <returns>
        <see cref="T:System.Guid" /> representing the unique identifier for the workflow associated with the timer subscription.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.TimerEventSubscriptionCollection">
      <summary>Represents an ordered list of <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> objects.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.TimerEventSubscriptionCollection.Add(System.Workflow.Runtime.TimerEventSubscription)">
      <summary>Adds a <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> to the timer queue.</summary>
      <param name="item">The <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> to add to the timer queue.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="item" /> is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.TimerEventSubscriptionCollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the <see cref="T:System.Workflow.Runtime.TimerEventSubscriptionCollection" /> elements to an existing one-dimensional <see cref="T:System.Array" />, starting at the specified array index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from the <see cref="T:System.Workflow.Runtime.TimerEventSubscriptionCollection" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.TimerEventSubscriptionCollection.Count">
      <summary>Gets the number of <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> objects in the timer queue.</summary>
      <returns>The number of <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> objects in the queue.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.TimerEventSubscriptionCollection.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Workflow.Runtime.TimerEventSubscriptionCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Workflow.Runtime.TimerEventSubscriptionCollection" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.TimerEventSubscriptionCollection.IsSynchronized">
      <summary>Gets a value that indicates whether the access to the <see cref="T:System.Workflow.Runtime.TimerEventSubscriptionCollection" /> is synchronized (thread safe).</summary>
      <returns>Always returns true indicating that access to the <see cref="T:System.Workflow.Runtime.TimerEventSubscriptionCollection" /> is synchronized (thread safe).</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.TimerEventSubscriptionCollection.Peek">
      <summary>Returns the <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> at the beginning of the timer queue without removing it.</summary>
      <returns>The <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> at the beginning of the timer queue or a null reference (Nothing in Visual Basic) if the timer queue is empty.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.TimerEventSubscriptionCollection.Remove(System.Guid)">
      <summary>Removes the <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> that has the specified subscription id from the timer queue.</summary>
      <param name="timerSubscriptionId">The <see cref="T:System.Guid" /> of the <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> to remove.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.TimerEventSubscriptionCollection.Remove(System.Workflow.Runtime.TimerEventSubscription)">
      <summary>Removes the <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> from the timer queue.</summary>
      <param name="item">The <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> to remove from the timer queue.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="item" /> is a null reference (Nothing in Visual Basic).</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.TimerEventSubscriptionCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Workflow.Runtime.TimerEventSubscriptionCollection" />.</summary>
      <returns>An object used to synchronize access to the <see cref="T:System.Workflow.Runtime.TimerEventSubscriptionCollection" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Workflow.Runtime.TimerEventSubscriptionCollection.TimerCollectionProperty">
      <summary>Timer queue associated with a workflow instance. This queue contains time ordered <see cref="T:System.Workflow.Runtime.TimerEventSubscription" /> objects for a workflow.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.WorkflowCompletedEventArgs">
      <summary>Provides data for the <see cref="E:System.Workflow.Runtime.WorkflowRuntime.WorkflowCompleted" /> event.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowCompletedEventArgs.OutputParameters">
      <summary>Gets the output from the workflow.</summary>
      <returns>A <see cref="T:System.Collections.Generic.Dictionary`2" /> of values keyed by parameter name that contains the output parameters of the workflow.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowCompletedEventArgs.WorkflowDefinition">
      <summary>Gets an <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the workflow definition on completion of the workflow instance.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the workflow definition on completion of the workflow instance.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.WorkflowEnvironment">
      <summary>Represents the transactional environment of the workflow instance that is running on the current thread.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowEnvironment.WorkBatch">
      <summary>Gets the current work batch.</summary>
      <returns>The <see cref="T:System.Workflow.Runtime.IWorkBatch" /> that represents the current work batch. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowEnvironment.WorkflowInstanceId">
      <summary>Gets the <see cref="T:System.Guid" /> of the workflow instance associated with the current thread.</summary>
      <returns>The <see cref="T:System.Guid" /> that identifies the current workflow instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.WorkflowEventArgs">
      <summary>Provides data for workflow events.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowEventArgs.WorkflowInstance">
      <summary>Gets the workflow instance associated with the workflow event.</summary>
      <returns>The <see cref="T:System.Workflow.Runtime.WorkflowInstance" /> associated with the workflow event.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.WorkflowInstance">
      <summary>Represents a workflow instance.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.Abort">
      <summary>Aborts the workflow instance.</summary>
      <exception cref="T:System.InvalidOperationException">The workflow runtime engine is not running.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.ApplyWorkflowChanges(System.Workflow.ComponentModel.WorkflowChanges)">
      <summary>Applies changes to the workflow instance specified by the <see cref="T:System.Workflow.ComponentModel.WorkflowChanges" /> object.</summary>
      <param name="workflowChanges">A <see cref="T:System.Workflow.ComponentModel.WorkflowChanges" /> specifying dynamic updates for the workflow instance.</param>
      <exception cref="T:System.InvalidOperationException">The workflow runtime engine is not running.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.EnqueueItem(System.IComparable,System.Object,System.Workflow.Runtime.IPendingWork,System.Object)">
      <summary>Posts a message to the specified workflow queue synchronously.</summary>
      <param name="queueName">The name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</param>
      <param name="item">The object to enqueue.</param>
      <param name="pendingWork">An <see cref="T:System.Workflow.Runtime.IPendingWork" /> that allows the sender to be notified when <paramref name="item" /> is delivered.</param>
      <param name="workItem">An object to be passed to the <see cref="T:System.Workflow.Runtime.IPendingWork" /> methods.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="queueName" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">The workflow runtime engine is not running.-or-The <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> specified by <paramref name="queueName" /> does not exist.-or-The <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> specified by <paramref name="queueName" /> is not enabled.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.EnqueueItemOnIdle(System.IComparable,System.Object,System.Workflow.Runtime.IPendingWork,System.Object)">
      <summary>Posts a message to the specified workflow queue when the workflow is idle. <see cref="M:System.Workflow.Runtime.WorkflowInstance.EnqueueItemOnIdle(System.IComparable,System.Object,System.Workflow.Runtime.IPendingWork,System.Object)" /> waits until the workflow reaches an idle point and enqueues after verifying that the workflow scheduler is idle (that is, no active operation is being executed).</summary>
      <param name="queueName">The name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</param>
      <param name="item">The object to enqueue.</param>
      <param name="pendingWork">An <see cref="T:System.Workflow.Runtime.IPendingWork" /> that allows the sender to be notified when <paramref name="item" /> is delivered.</param>
      <param name="workItem">An object to be passed to the <see cref="T:System.Workflow.Runtime.IPendingWork" /> methods.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="queueName" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">The workflow runtime engine is not running.-or-The workflow instance is suspended.-or-The <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> specified by <paramref name="queueName" /> does not exist.-or-The <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> specified by <paramref name="queueName" /> is not enabled.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.Equals(System.Object)">
      <summary>Returns a value that indicates whether the specified object is equal to the <see cref="T:System.Workflow.Runtime.WorkflowInstance" />.</summary>
      <returns>true if the specified object is equal to this <see cref="T:System.Workflow.Runtime.WorkflowInstance" />; otherwise, false.</returns>
      <param name="obj">The object to compare.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.GetHashCode">
      <summary>Returns the hash code for this workflow instance.</summary>
      <returns>The hash code for this <see cref="T:System.Workflow.Runtime.WorkflowInstance" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.GetWorkflowDefinition">
      <summary>Retrieves the root activity for this workflow instance.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Activity" /> object.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.GetWorkflowNextTimerExpiration">
      <summary>Returns the next point in time that this <see cref="T:System.Workflow.Runtime.WorkflowInstance" /> is scheduled to be delivered a timer message.</summary>
      <returns>A DateTime value that represents the next <see cref="P:System.Workflow.Runtime.TimerEventSubscription.ExpiresAt" /> time this <see cref="T:System.Workflow.Runtime.WorkflowInstance" /> will be delivered a timer message. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.GetWorkflowQueueData">
      <summary>Gets a collection of <see cref="T:System.Workflow.Runtime.WorkflowQueueInfo" /> objects that contains the pending items and subscribed activities for the workflow queues associated with this workflow instance.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Workflow.Runtime.WorkflowQueueInfo" /> objects.</returns>
      <exception cref="T:System.InvalidOperationException">The workflow runtime engine is not running.</exception>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowInstance.InstanceId">
      <summary>Gets the unique identifier for the workflow instance.</summary>
      <returns>The <see cref="T:System.Guid" /> of the workflow instance.</returns>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.Load">
      <summary>Loads a previously unloaded workflow instance.</summary>
      <exception cref="T:System.InvalidOperationException">The workflow runtime engine is not running.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.ReloadTrackingProfiles">
      <summary>Reload the tracking profiles for this workflow instance.</summary>
      <exception cref="T:System.InvalidOperationException">The workflow runtime engine is not running.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.Resume">
      <summary>Resumes execution of a previously suspended workflow instance.</summary>
      <exception cref="T:System.InvalidOperationException">The workflow runtime engine is not running.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.Start">
      <summary>Starts the execution of the workflow instance.</summary>
      <exception cref="T:System.InvalidOperationException">The workflow runtime engine is not running.-or-The workflow instance has already been started.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.Suspend(System.String)">
      <summary>Suspends the workflow instance.</summary>
      <param name="error">A description of the reason for suspending the workflow instance.</param>
      <exception cref="T:System.InvalidOperationException">The workflow runtime engine is not running. </exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.Terminate(System.String)">
      <summary>Terminates the workflow instance in a synchronous manner.</summary>
      <param name="error">A description of the reason for terminating the workflow instance.</param>
      <exception cref="T:System.InvalidOperationException">The workflow runtime engine is not running.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.TryUnload">
      <summary>Unloads the workflow instance from memory to the persistence store when the instance is suspended or idle.</summary>
      <returns>true if the workflow instance was unloaded; otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">There is no persistence service registered with the workflow runtime engine.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowInstance.Unload">
      <summary>Unloads the workflow instance from memory to the persistence store. This call blocks until after the currently scheduled work is finished, or the end of a transaction scope.</summary>
      <exception cref="T:System.InvalidOperationException">There is no persistence service registered with the workflow runtime engine.</exception>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowInstance.WorkflowRuntime">
      <summary>Gets the <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> for this workflow instance.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> that represents the execution environment in which this workflow instance is running.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.WorkflowOwnershipException">
      <summary>The exception that is thrown when the workflow runtime engine attempts to load a workflow instance that is currently loaded by another workflow runtime engine instance. Additionally, this exception is thrown when the workflow runtime engine attempts to save a workflow after the ownership timeout that was specified while loading the workflow has expired. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowOwnershipException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.WorkflowOwnershipException" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowOwnershipException.#ctor(System.Guid)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.WorkflowOwnershipException" /> class by using a specified workflow instance <see cref="T:System.Guid" />.</summary>
      <param name="instanceId">The <see cref="T:System.Guid" /> of the workflow instance for which this exception occurred.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowOwnershipException.#ctor(System.Guid,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.WorkflowOwnershipException" /> class by using a specified workflow instance <see cref="T:System.Guid" /> and a specified error message.</summary>
      <param name="instanceId">The <see cref="T:System.Guid" /> of the workflow instance for which this exception occurred.</param>
      <param name="message">The message that describes the error.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowOwnershipException.#ctor(System.Guid,System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.WorkflowOwnershipException" /> class by using a specified workflow instance <see cref="T:System.Guid" />, a specified error message, and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="instanceId">The <see cref="T:System.Guid" /> of the workflow instance for which this exception occurred.</param>
      <param name="message">The message that describes the error.</param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not a null reference (Nothing in Visual Basic), the current exception is raised in a catch block that handles the inner exception.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowOwnershipException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the WorkflowOwnershipException class with serialized data.</summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowOwnershipException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.WorkflowOwnershipException" /> class by using a specified error message.</summary>
      <param name="message">The message that describes the error.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowOwnershipException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.WorkflowOwnershipException" /> class by using a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the error.</param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not a null reference (Nothing in Visual Basic), the current exception is raised in a catch block that handles the inner exception.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowOwnershipException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with the <see cref="T:System.Guid" /> of the workflow instance associated with this exception, and additional exception information.</summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is a null reference (Nothing in Visual Basic)</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowOwnershipException.InstanceId">
      <summary>Gets the <see cref="T:System.Guid" /> of the workflow instance for which this exception was thrown.</summary>
      <returns>
        <see cref="P:System.Workflow.Runtime.WorkflowOwnershipException.InstanceId" /> is equivalent to the <see cref="P:System.Workflow.Runtime.WorkflowInstance.InstanceId" /> property of the workflow instance.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.WorkflowQueue">
      <summary>Represents a workflow queue.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowQueue.Count">
      <summary>Gets the number of items contained in the <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</summary>
      <returns>The number of items in the workflow queue.</returns>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowQueue.Dequeue">
      <summary>Removes and returns the object at the beginning of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</summary>
      <returns>The object that is removed from the beginning of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> is empty.</exception>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowQueue.Enabled">
      <summary>Gets or sets a value that specifies whether the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> is enabled.</summary>
      <returns>true if the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> is enabled; otherwise false. </returns>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowQueue.Enqueue(System.Object)">
      <summary>Adds an object to the end of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</summary>
      <param name="item">The object to add to the <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> is not enabled.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowQueue.Peek">
      <summary>Returns the object at the beginning of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> without removing it.</summary>
      <returns>The object at the beginning of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> is empty.</exception>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowQueue.QueueItemArrived">
      <summary>Occurs when an item is delivered on this <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</summary>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowQueue.QueueItemAvailable">
      <summary>Occurs when an item is available on this <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowQueue.QueueName">
      <summary>Gets the name of the workflow queue.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowQueue.QueuingService">
      <summary>Gets the queuing service associated with this <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.WorkflowQueuingService" /> that represents the queuing service associated with this <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</returns>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowQueue.RegisterForQueueItemArrived(System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.ComponentModel.QueueEventArgs})">
      <summary>Registers a subscriber to the <see cref="E:System.Workflow.Runtime.WorkflowQueue.QueueItemArrived" /> event.</summary>
      <param name="eventListener">A subscriber for <see cref="T:System.Workflow.ComponentModel.QueueEventArgs" /> that implements the <see cref="T:System.Workflow.ComponentModel.IActivityEventListener`1" /> interface.</param>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowQueue.RegisterForQueueItemAvailable(System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.ComponentModel.QueueEventArgs})">
      <summary>Registers a subscriber to the <see cref="E:System.Workflow.Runtime.WorkflowQueue.QueueItemAvailable" /> event.</summary>
      <param name="eventListener">A subscriber for <see cref="T:System.Workflow.ComponentModel.QueueEventArgs" /> that implements the <see cref="T:System.Workflow.ComponentModel.IActivityEventListener`1" /> interface.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventListener" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowQueue.RegisterForQueueItemAvailable(System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.ComponentModel.QueueEventArgs},System.String)">
      <summary>Registers a subscriber to the <see cref="E:System.Workflow.Runtime.WorkflowQueue.QueueItemAvailable" /> event.</summary>
      <param name="eventListener">A subscriber for <see cref="T:System.Workflow.ComponentModel.QueueEventArgs" /> that implements the <see cref="T:System.Workflow.ComponentModel.IActivityEventListener`1" /> interface.</param>
      <param name="subscriberQualifiedName">The <see cref="P:System.Workflow.ComponentModel.Activity.QualifiedName" /> of the activity that is subscribing to the <see cref="E:System.Workflow.Runtime.WorkflowQueue.QueueItemAvailable" /> event or a null reference (Nothing in Visual Basic).</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventListener" /> is a null reference (Nothing).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowQueue.UnregisterForQueueItemArrived(System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.ComponentModel.QueueEventArgs})">
      <summary>Unregisters a subscriber to the <see cref="E:System.Workflow.Runtime.WorkflowQueue.QueueItemArrived" /> event.</summary>
      <param name="eventListener">A subscriber for <see cref="T:System.Workflow.ComponentModel.QueueEventArgs" /> that implements the <see cref="T:System.Workflow.ComponentModel.IActivityEventListener`1" /> interface.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventListener" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowQueue.UnregisterForQueueItemAvailable(System.Workflow.ComponentModel.IActivityEventListener{System.Workflow.ComponentModel.QueueEventArgs})">
      <summary>Unregisters a subscriber to the <see cref="E:System.Workflow.Runtime.WorkflowQueue.QueueItemAvailable" /> event.</summary>
      <param name="eventListener">A subscriber for <see cref="T:System.Workflow.ComponentModel.QueueEventArgs" /> that implements the <see cref="T:System.Workflow.ComponentModel.IActivityEventListener`1" /> interface.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventListener" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Workflow.Runtime.WorkflowQueueInfo">
      <summary>Contains information about a <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowQueueInfo.Items">
      <summary>Gets a collection of the unconsumed items in the workflow queue.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> that contains the unconsumed items in the <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowQueueInfo.QueueName">
      <summary>Gets the name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> associated with this <see cref="T:System.Workflow.Runtime.WorkflowQueueInfo" />.</summary>
      <returns>The name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowQueueInfo.SubscribedActivityNames">
      <summary>Gets a collection that contains the qualified name each activity subscribed to the <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> that contains the qualified name of the each activity subscribed to the <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.WorkflowQueuingService">
      <summary>Provides the services for management of <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> objects.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowQueuingService.CreateWorkflowQueue(System.IComparable,System.Boolean)">
      <summary>Creates a <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> by using the specified name and transactional scope.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</returns>
      <param name="queueName">The name of the queue.</param>
      <param name="transactional">A value that specifies whether the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> is visible outside the scope of the current transaction.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="queueName" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">A <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> with the name specified by <paramref name="queueName" /> already exists.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowQueuingService.DeleteWorkflowQueue(System.IComparable)">
      <summary>Deletes the specified <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</summary>
      <param name="queueName">The name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> to delete.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="queueName" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowQueuingService.Exists(System.IComparable)">
      <summary>Tests for the existence of the specified <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</summary>
      <returns>true if the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> exists; otherwise, false.</returns>
      <param name="queueName">The name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="queueName" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowQueuingService.GetWorkflowQueue(System.IComparable)">
      <summary>Retrieves the specified <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.WorkflowQueue" />.</returns>
      <param name="queueName">The name of the <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> to retrieve.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="queueName" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">The specified <see cref="T:System.Workflow.Runtime.WorkflowQueue" /> was not found.</exception>
    </member>
    <member name="F:System.Workflow.Runtime.WorkflowQueuingService.PendingMessagesProperty">
      <summary>Contains the unconsumed items in the workflow queues associated with this <see cref="T:System.Workflow.Runtime.WorkflowQueuingService" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.WorkflowRuntime">
      <summary>Represents the configurable execution environment provided by the workflow run-time engine for workflows.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> class by using the specified section of the application configuration file.</summary>
      <param name="configSectionName">The name of a valid workflowSettings section in the application configuration file.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="configSectionName" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">A valid workflowSettings section could not be found in the application configuration file.</exception>
      <exception cref="T:System.InvalidOperationException">A <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> already exists for this application domain.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.#ctor(System.Workflow.Runtime.Configuration.WorkflowRuntimeSection)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> class by using the settings in the specified <see cref="T:System.Workflow.Runtime.Configuration.WorkflowRuntimeSection" />.</summary>
      <param name="settings">A <see cref="T:System.Workflow.Runtime.Configuration.WorkflowRuntimeSection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="settings" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">A <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> already exists for this application domain.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.AddService(System.Object)">
      <summary>Adds the specified service to the workflow run-time engine.</summary>
      <param name="service">An object that represents the service to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="service" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="service" /> is already registered with the workflow run-time engine.-or-<paramref name="service" /> is a core service and the workflow run-time engine is already running (<see cref="P:System.Workflow.Runtime.WorkflowRuntime.IsStarted" /> is true).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.CreateWorkflow(System.Type)">
      <summary>Creates a new workflow instance by using the specified workflow <see cref="T:System.Type" />.</summary>
      <returns>The created workflow instance.</returns>
      <param name="workflowType">The <see cref="T:System.Type" /> of the workflow to create.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="workflowType" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.CreateWorkflow(System.Type,System.Collections.Generic.Dictionary{System.String,System.Object})">
      <summary>Creates a workflow instance by using the specified workflow <see cref="T:System.Type" /> and the arguments to the workflow contained in the specified <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>The created workflow instance.</returns>
      <param name="workflowType">The <see cref="T:System.Type" /> of the workflow to create.</param>
      <param name="namedArgumentValues">A <see cref="T:System.Collections.Generic.Dictionary`2" /> of objects keyed by string that represents the arguments to the workflow.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="workflowType" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.CreateWorkflow(System.Type,System.Collections.Generic.Dictionary{System.String,System.Object},System.Guid)">
      <summary>Creates a workflow instance by using the specified parameters. </summary>
      <returns>The created workflow instance.</returns>
      <param name="workflowType">The <see cref="T:System.Type" /> of the workflow to create.</param>
      <param name="namedArgumentValues">A <see cref="T:System.Collections.Generic.Dictionary`2" /> of objects keyed by a string that represents the arguments to the workflow.</param>
      <param name="instanceId">The <see cref="T:System.Guid" /> of the specific <see cref="T:System.Workflow.Runtime.WorkflowInstance" /> to create.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="workflowType" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.CreateWorkflow(System.Xml.XmlReader)">
      <summary>Creates a workflow instance by using the specified <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>The created workflow instance.</returns>
      <param name="workflowDefinitionReader">An <see cref="T:System.Xml.XmlReader" /> that contains the workflow definition.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="workflowDefinitionReader" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> is disposed.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.CreateWorkflow(System.Xml.XmlReader,System.Xml.XmlReader,System.Collections.Generic.Dictionary{System.String,System.Object})">
      <summary>Creates a workflow instance by using the specified <see cref="T:System.Xml.XmlReader" /> objects and the arguments contained in the specified <see cref="T:System.Collections.Generic.Dictionary`2" />. </summary>
      <returns>The created workflow instance.</returns>
      <param name="workflowDefinitionReader">An <see cref="T:System.Xml.XmlReader" /> that contains the workflow definition.</param>
      <param name="rulesReader">An <see cref="T:System.Xml.XmlReader" />.</param>
      <param name="namedArgumentValues">A <see cref="T:System.Collections.Generic.Dictionary`2" /> of objects keyed by a string that represents the arguments to the workflow.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="workflowDefinitionReader" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> is disposed.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.CreateWorkflow(System.Xml.XmlReader,System.Xml.XmlReader,System.Collections.Generic.Dictionary{System.String,System.Object},System.Guid)">
      <summary>Creates a workflow instance by using the specified parameters.</summary>
      <returns>The created workflow instance.</returns>
      <param name="workflowDefinitionReader">An <see cref="T:System.Xml.XmlReader" /> that contains the workflow definition.</param>
      <param name="rulesReader">An <see cref="T:System.Xml.XmlReader" />.</param>
      <param name="namedArgumentValues">A <see cref="T:System.Collections.Generic.Dictionary`2" /> of objects keyed by a string that represents the arguments to the workflow.</param>
      <param name="instanceId">The <see cref="T:System.Guid" /> of the specific <see cref="T:System.Workflow.Runtime.WorkflowInstance" /> to create.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="workflowType" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.Dispose">
      <summary>Releases the resources used by the <see cref="T:System.Workflow.Runtime.WorkflowRuntime" />.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.GetAllServices``1">
      <summary>Retrieves all the services that are added to the workflow run-time engine that implement or derive from the specified generic type.</summary>
      <returns>Services that implement or derive from the specified generic type. </returns>
      <typeparam name="T">The service type.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> is disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.GetAllServices(System.Type)">
      <summary>Retrieves all the services that are added to the workflow run-time engine that implement or derive from the specified <see cref="T:System.Type" />.</summary>
      <returns>Services that implement or derive from the specified <see cref="T:System.Type" />.</returns>
      <param name="serviceType">The <see cref="T:System.Type" /> that services must implement to be returned.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serviceType" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> is disposed.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.GetLoadedWorkflows">
      <summary>Gets a collection that contains all the workflow instances currently loaded in memory.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.WorkflowInstance" /> object for each workflow instance currently loaded in memory.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> is disposed.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.GetService``1">
      <summary>Retrieves a service of the specified generic type from the workflow run-time engine.</summary>
      <returns>A single service of the specified generic type.</returns>
      <typeparam name="T">The service type.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> has already been disposed of.</exception>
      <exception cref="T:System.InvalidOperationException">More than one service of the generic type was found.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.GetService(System.Type)">
      <summary>Retrieves a service of the specified <see cref="T:System.Type" /> from the workflow run-time engine.</summary>
      <returns>The service of the specified <see cref="T:System.Type" />.</returns>
      <param name="serviceType">The <see cref="T:System.Type" /> of the service to retrieve.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serviceType" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> is already disposed of.</exception>
      <exception cref="T:System.InvalidOperationException">More than one service of type <paramref name="serviceType" /> was found.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.GetWorkflow(System.Guid)">
      <summary>Retrieves the workflow instance that has the specified <see cref="T:System.Guid" />.</summary>
      <returns>The <see cref="T:System.Workflow.Runtime.WorkflowInstance" /> that has the specified <see cref="T:System.Guid" />.</returns>
      <param name="instanceId">The <see cref="T:System.Guid" /> of a workflow instance.</param>
      <exception cref="T:System.InvalidOperationException">The workflow runtime is not started.</exception>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowRuntime.IsStarted">
      <summary>Gets a value that indicates whether the workflow run-time engine has been started.</summary>
      <returns>true if the workflow run-time engine has been started; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowRuntime.Name">
      <summary>Gets or sets the name associated with the <see cref="T:System.Workflow.Runtime.WorkflowRuntime" />.</summary>
      <returns>The name associated with this <see cref="T:System.Workflow.Runtime.WorkflowRuntime" />.</returns>
      <exception cref="T:System.ObjectDisposedException">An attempt to set <see cref="P:System.Workflow.Runtime.WorkflowRuntime.Name" /> on a <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> that is disposed occurs.</exception>
      <exception cref="T:System.InvalidOperationException">An attempt to set <see cref="P:System.Workflow.Runtime.WorkflowRuntime.Name" /> while the workflow run-time engine is running occurs.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.RemoveService(System.Object)">
      <summary>Removes the specified service from the workflow run-time engine.</summary>
      <param name="service">An object that represents the service to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="service" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> is already disposed of.</exception>
      <exception cref="T:System.InvalidOperationException">The workflow run-time engine has started (<see cref="P:System.Workflow.Runtime.WorkflowRuntime.IsStarted" /> is true) and <paramref name="service" /> is a core service. - or -<paramref name="service" /> is not registered with the workflow run-time engine.</exception>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowRuntime.ServicesExceptionNotHandled">
      <summary>Occurs when a service that is derived from the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowRuntimeService" /> class calls <see cref="M:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.RaiseServicesExceptionNotHandledEvent(System.Exception,System.Guid)" />.</summary>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowRuntime.Started">
      <summary>Occurs when the workflow run-time engine is started.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.StartRuntime">
      <summary>Starts the workflow run-time engine and the workflow run-time engine services.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">There is more than one service workflow CommitWorkBatch service registered with this <see cref="T:System.Workflow.Runtime.WorkflowRuntime" />.-or-There is more than one scheduler service registered with this <see cref="T:System.Workflow.Runtime.WorkflowRuntime" />.-or- There is more than one persistence service registered with this <see cref="T:System.Workflow.Runtime.WorkflowRuntime" />. </exception>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowRuntime.Stopped">
      <summary>Occurs when the workflow run-time engine is stopped.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.WorkflowRuntime.StopRuntime">
      <summary>Stops the workflow run-time engine and the run-time services.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> is already disposed of.</exception>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowRuntime.WorkflowAborted">
      <summary>Occurs when a workflow instance is aborted.</summary>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowRuntime.WorkflowCompleted">
      <summary>Occurs when a workflow instance has completed. </summary>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowRuntime.WorkflowCreated">
      <summary>Occurs when a workflow instance is created.</summary>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowRuntime.WorkflowIdled">
      <summary>Occurs when a workflow instance enters the idle state.</summary>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowRuntime.WorkflowLoaded">
      <summary>Occurs when the workflow instance is loaded into memory.</summary>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowRuntime.WorkflowPersisted">
      <summary>Occurs when the state of a workflow instance is persisted.</summary>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowRuntime.WorkflowResumed">
      <summary>Occurs when execution of a workflow instance is resumed following a suspension.</summary>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowRuntime.WorkflowStarted">
      <summary>Occurs when a workflow instance has been started.</summary>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowRuntime.WorkflowSuspended">
      <summary>Occurs when a workflow instance is suspended.</summary>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowRuntime.WorkflowTerminated">
      <summary>Occurs when a workflow instance is terminated.</summary>
    </member>
    <member name="E:System.Workflow.Runtime.WorkflowRuntime.WorkflowUnloaded">
      <summary>Occurs when the workflow instance is unloaded from memory.</summary>
    </member>
    <member name="T:System.Workflow.Runtime.WorkflowRuntimeEventArgs">
      <summary>Provides data for the <see cref="E:System.Workflow.Runtime.WorkflowRuntime.Started" /> and <see cref="E:System.Workflow.Runtime.WorkflowRuntime.Stopped" /> events.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowRuntimeEventArgs.IsStarted">
      <summary>Gets a value that indicates whether the workflow runtime engine is running.</summary>
      <returns>true if the workflow runtime engine is running; otherwise, false.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.WorkflowStatus">
      <summary>Specifies the status of a workflow instance.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.WorkflowStatus.Running">
      <summary>The workflow instance is running.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.WorkflowStatus.Completed">
      <summary>The workflow instance has completed.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.WorkflowStatus.Suspended">
      <summary>The workflow instance has been suspended by a <see cref="T:System.Workflow.ComponentModel.SuspendActivity" /> activity, by a call to <see cref="M:System.Workflow.Runtime.WorkflowInstance.Suspend(System.String)" />,or by the workflow runtime engine.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.WorkflowStatus.Terminated">
      <summary>The workflow instance has been terminated by a </summary>
    </member>
    <member name="F:System.Workflow.Runtime.WorkflowStatus.Created">
      <summary>The workflow instance has been created by a call to one of the overloaded <see cref="M:System.Workflow.Runtime.WorkflowRuntime.CreateWorkflow(System.Type)" /> methods.</summary>
    </member>
    <member name="T:System.Workflow.Runtime.WorkflowSuspendedEventArgs">
      <summary>Provides data for the <see cref="E:System.Workflow.Runtime.WorkflowRuntime.WorkflowSuspended" /> event.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowSuspendedEventArgs.Error">
      <summary>Gets the description of why the workflow instance was suspended.</summary>
      <returns>The description of why the workflow instance was suspended.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.WorkflowTerminatedEventArgs">
      <summary>Provides data for the <see cref="E:System.Workflow.Runtime.WorkflowRuntime.WorkflowTerminated" /> event.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.WorkflowTerminatedEventArgs.Exception">
      <summary>Gets the exception that caused the workflow instance to be terminated.</summary>
      <returns>The <see cref="T:System.Exception" /> that caused the workflow instance to be terminated.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Configuration.WorkflowRuntimeSection">
      <summary>Represents a section, within a configuration file, that defines settings for the workflow run-time engine.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Configuration.WorkflowRuntimeSection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Configuration.WorkflowRuntimeSection" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Configuration.WorkflowRuntimeSection.CommonParameters">
      <summary>Gets the collection of common parameters used by services.</summary>
      <returns>The common parameters used by services. The default is a null reference (Nothing in Visual Basic).</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Configuration.WorkflowRuntimeSection.EnablePerformanceCounters">
      <summary>Gets or sets a value that indicates whether performance counters are enabled.</summary>
      <returns>true if performance counters are enabled; otherwise false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Configuration.WorkflowRuntimeSection.Name">
      <summary>Gets or sets the name of the workflow run-time engine.</summary>
      <returns>The name of the workflow run-time engine. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Configuration.WorkflowRuntimeSection.Services">
      <summary>Gets the collection of services that are added to the workflow run-time engine when it is initialized.</summary>
      <returns>The services to be added to the workflow run-time engine.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Configuration.WorkflowRuntimeSection.ValidateOnCreate">
      <summary>Gets or sets a value that indicates whether validation occurs on the creation of the workflow instance.</summary>
      <returns>true if validation occurs on creation; otherwise, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Configuration.WorkflowRuntimeSection.WorkflowDefinitionCacheCapacity">
      <summary>Gets the number of workflow definitions that can be cached by the runtime.</summary>
      <returns>The number of workflows.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElement">
      <summary>Represents a configuration element for a service to be added to the workflow runtime engine.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElement" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElement.OnDeserializeUnrecognizedAttribute(System.String,System.String)">
      <summary>Called when an unknown attribute is encountered while deserializing the <see cref="T:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElement" />. </summary>
      <returns>
        <see cref="M:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElement.OnDeserializeUnrecognizedAttribute(System.String,System.String)" /> always returns true.</returns>
      <param name="name">The name of the unrecognized attribute.</param>
      <param name="value">The value of the unrecognized attribute.</param>
    </member>
    <member name="P:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElement.Parameters">
      <summary>Gets the parameters for this service.</summary>
      <returns>A <see cref="T:System.Collections.Specialized.NameValueCollection" /> that specifies the parameters for the service.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElement.Type">
      <summary>Gets or sets the assembly-qualified type name of the service.</summary>
      <returns>The assembly-qualified type name of the service.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set <see cref="P:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElement.Type" /> to a null reference is made.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElementCollection">
      <summary>Represents the collection of services to be added to the workflow runtime engine.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElementCollection" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElementCollection.Add(System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElement)">
      <summary>Adds a <see cref="T:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElement" /> to this <see cref="T:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElementCollection" />.</summary>
      <param name="serviceSettings">A <see cref="T:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElement" /> that represents a service to be initialized and activated by the workflow runtime engine.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serviceSettings" /> is a null reference (Nothing in Visual Basic)</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElementCollection.CreateNewElement">
      <summary>Creates a new <see cref="T:System.Configuration.ConfigurationElement" />.</summary>
      <returns>An empty <see cref="T:System.Configuration.ConfigurationElement" />.</returns>
    </member>
    <member name="M:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElementCollection.GetElementKey(System.Configuration.ConfigurationElement)">
      <summary>Returns a key for the specified <see cref="T:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElement" />.</summary>
      <returns>The assembly-qualified type name of the service for the <see cref="T:System.Workflow.Runtime.Configuration.WorkflowRuntimeServiceElement" /> specified by <paramref name="settings" />.</returns>
      <param name="settings">A <see cref="T:System.Configuration.ConfigurationElement" /> for which to return a key.</param>
    </member>
    <member name="T:System.Workflow.Runtime.DebugEngine.ActivityHandlerDescriptor">
      <summary>Specifies the code-beside handler information in the <see cref="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebugger.BeforeHandlerInvoked(System.Guid,System.Guid,System.String,System.Workflow.Runtime.DebugEngine.ActivityHandlerDescriptor)" /> and <see cref="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebugger.UpdateHandlerMethodsForActivity(System.Guid,System.Guid,System.String,System.Collections.Generic.List{System.Workflow.Runtime.DebugEngine.ActivityHandlerDescriptor})" /> methods.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Workflow.Runtime.DebugEngine.ActivityHandlerDescriptor.Name">
      <summary>The <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> representing the name of the handler method.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Workflow.Runtime.DebugEngine.ActivityHandlerDescriptor.Token">
      <summary>The <see cref="T:System.Workflow.ComponentModel.DependencyProperty" /> representing the CLR metadata token identifier for the handler method.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.DebugEngine.DebugController">
      <summary>Relays workflow instance creation, execution and termination events to the debugger process. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.DebugController.AttachToConduit(System.Uri)">
      <summary>Establishes the communication channel between the <see cref="T:System.Workflow.Runtime.DebugEngine.DebugController" /> object running in the workflow host application and the debugger process.</summary>
      <param name="url">A <see cref="T:System.Uri" /> of the remoting object running in the debugger process with which the <see cref="T:System.Workflow.Runtime.DebugEngine.DebugController" /> communicates.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.DebugController.InitializeLifetimeService">
      <summary>Establishes a lifetime lease for the <see cref="T:System.Workflow.Runtime.DebugEngine.DebugController" /> object.</summary>
      <returns>An <see cref="T:System.Object" /> that implements <see cref="T:System.Runtime.Remoting.Lifetime.ILease" /> and is used to control the lifetime policy for the <see cref="T:System.Workflow.Runtime.DebugEngine.DebugController" /> object. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.DebugEngine.DebugEngineCallback">
      <summary>References a callback method that is invoked by the workflow expression evaluation component. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.DebugEngine.IExpressionEvaluationFrame">
      <summary>Defines the interface implemented by the workflow expression evaluation component that provides a context frame for expression evaluation.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IExpressionEvaluationFrame.CreateEvaluationFrame(System.Workflow.Runtime.DebugEngine.IInstanceTable,System.Workflow.Runtime.DebugEngine.DebugEngineCallback)">
      <summary>Creates a stack frame for workflow expression evaluation.</summary>
      <param name="instanceTable">An object that implements <see cref="T:System.Workflow.Runtime.DebugEngine.IInstanceTable" />. Not used.</param>
      <param name="callback">The default <see cref="T:System.Workflow.Runtime.DebugEngine.DebugEngineCallback" /> implemented by the workflow runtime.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.DebugEngine.IInstanceTable">
      <summary>Reserved for future use.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IInstanceTable.GetActivity(System.String,System.String)">
      <summary>Reserved for future use.</summary>
      <returns>The <see cref="T:System.Workflow.ComponentModel.Activity" /> specified by <paramref name="activityName" /> in the workflow with an id of <paramref name="instanceId" />. </returns>
      <param name="instanceId">Unique identifier of the workflow containing the activity</param>
      <param name="activityName">Name of the <see cref="T:System.Workflow.ComponentModel.Activity" /></param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.DebugEngine.IWorkflowDebugger">
      <summary>Defines the interface that receives workflow instance creation, execution, and termination status information from a <see cref="T:System.Workflow.Runtime.DebugEngine.DebugController" /> object running inside the workflow host application.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebugger.ActivityStatusChanged(System.Guid,System.Guid,System.Guid,System.String,System.String,System.Workflow.ComponentModel.ActivityExecutionStatus,System.Int32)">
      <summary>Called when the <see cref="P:System.Workflow.ComponentModel.Activity.ExecutionStatus" /> of an activity within the workflow changes.</summary>
      <param name="programId">An internally generated <see cref="T:System.Guid" /> associated with a <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> instance.</param>
      <param name="scheduleTypeId">An internally generated <see cref="T:System.Guid" /> associated with a workflow type. </param>
      <param name="instanceId">A <see cref="T:System.Guid" /> associated with the currently running workflow instance.</param>
      <param name="activityQualifiedName">A <see cref="T:System.String" /> containing the fully qualified name of the current activity.</param>
      <param name="hierarchicalActivityId">A <see cref="T:System.String" /> containing the qualified name of the parent activity.</param>
      <param name="status">The <see cref="P:System.Workflow.ComponentModel.Activity.ExecutionStatus" /> of the current activity.</param>
      <param name="stateReaderId">An <see cref="T:System.Int32" /> containing the activity execution context ID associated with the current activity.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebugger.AssemblyLoaded(System.Guid,System.String,System.Boolean)">
      <summary>Called when an assembly is loaded in the app domain corresponding to the <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> in the workflow host application.</summary>
      <param name="programId">An internally generated <see cref="T:System.Guid" /> associated with a <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> instance.</param>
      <param name="assemblyPath">A <see cref="T:System.String" /> containing the path on disk from where the assembly is loaded</param>
      <param name="fromGlobalAssemblyCache">A <see cref="T:System.Boolean" /> that indicates whether the assembly is loaded from the global assembly cache.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebugger.BeforeActivityStatusChanged(System.Guid,System.Guid,System.Guid,System.String,System.String,System.Workflow.ComponentModel.ActivityExecutionStatus,System.Int32)">
      <summary>Called before the <see cref="P:System.Workflow.ComponentModel.Activity.ExecutionStatus" /> of an activity within the workflow changes.</summary>
      <param name="programId">An internally generated <see cref="T:System.Guid" /> associated with a <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> instance.</param>
      <param name="scheduleTypeId">An internally generated <see cref="T:System.Guid" /> associated with a workflow type. </param>
      <param name="instanceId">A <see cref="T:System.Guid" /> associated with the currently running workflow instance.</param>
      <param name="activityQualifiedName">A <see cref="T:System.String" /> containing the fully qualified name of the current activity.</param>
      <param name="hierarchicalActivityId">A <see cref="T:System.String" /> containing the qualified name of the parent activity.</param>
      <param name="status">The <see cref="P:System.Workflow.ComponentModel.Activity.ExecutionStatus" /> of the current activity.</param>
      <param name="stateReaderId">An <see cref="T:System.Int32" /> containing the activity execution context ID associated with the current activity.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebugger.BeforeHandlerInvoked(System.Guid,System.Guid,System.String,System.Workflow.Runtime.DebugEngine.ActivityHandlerDescriptor)">
      <summary>Called before a handler in the code-beside is about to be invoked.</summary>
      <param name="programId">An internally generated <see cref="T:System.Guid" /> associated with a <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> instance.</param>
      <param name="scheduleTypeId">An internally generated <see cref="T:System.Guid" /> associated with a workflow type. </param>
      <param name="activityQualifiedName">A <see cref="T:System.String" /> containing the fully qualified name of the current activity.</param>
      <param name="handlerMethod">An <see cref="T:System.Workflow.Runtime.DebugEngine.ActivityHandlerDescriptor" /> that contains the information regarding the code-beside handler method that is about to be invoked.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebugger.HandlerInvoked(System.Guid,System.Guid,System.Int32,System.String)">
      <summary>Called after a handler in the code-beside is invoked.</summary>
      <param name="programId">An internally generated <see cref="T:System.Guid" /> associated with a <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> instance.</param>
      <param name="instanceId">A <see cref="T:System.Guid" /> associated with the currently running workflow instance.</param>
      <param name="threadId">An <see cref="T:System.Int32" /> containing the ID of the thread on which the handler was invoked.</param>
      <param name="activityQualifiedName">A <see cref="T:System.String" /> containing the fully qualified name of the current activity.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebugger.InstanceCompleted(System.Guid,System.Guid)">
      <summary>Called when a workflow instance completes.</summary>
      <param name="programId">An internally generated <see cref="T:System.Guid" /> associated with a <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> instance.</param>
      <param name="instanceId">A <see cref="T:System.Guid" /> associated with the currently running workflow instance.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebugger.InstanceCreated(System.Guid,System.Guid,System.Guid)">
      <summary>Called when a workflow instance is created.</summary>
      <param name="programId">An internally generated <see cref="T:System.Guid" /> associated with a <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> instance.</param>
      <param name="instanceId">A <see cref="T:System.Guid" /> associated with the currently running workflow instance.</param>
      <param name="scheduleTypeId">An internally generated <see cref="T:System.Guid" /> associated with a workflow type. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebugger.InstanceDynamicallyUpdated(System.Guid,System.Guid,System.Guid)">
      <summary>Called when the workflow instance is dynamically updated with a workflow change.</summary>
      <param name="programId">An internally generated <see cref="T:System.Guid" /> associated with a <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> instance.</param>
      <param name="instanceId">A <see cref="T:System.Guid" /> associated with the currently running workflow instance.</param>
      <param name="scheduleTypeId">An internally generated <see cref="T:System.Guid" /> associated with a workflow type. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebugger.ScheduleTypeLoaded(System.Guid,System.Guid,System.String,System.String,System.String,System.Boolean,System.String,System.String,System.String)">
      <summary>Called when a new workflow type is loaded by the workflow runtime engine in the workflow host application.</summary>
      <param name="programId">An internally generated <see cref="T:System.Guid" /> associated with a <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> instance.</param>
      <param name="scheduleTypeId">An internally generated <see cref="T:System.Guid" /> associated with a workflow type. </param>
      <param name="assemblyFullName">A <see cref="T:System.String" /> containing the assembly name from which the workflow type was loaded.</param>
      <param name="fileName">A <see cref="T:System.String" /> containing the full path of the markup file corresponding to the workflow, or null if the workflow type was a code-only workflow.</param>
      <param name="md5Digest">A <see cref="T:System.String" /> containing the md5 hash code for the workflow markup definition, or null if the workflow type was a code-only workflow.</param>
      <param name="isDynamic">A <see cref="T:System.Boolean" /> that indicates whether the workflow definition was modified dynamically.</param>
      <param name="scheduleNamespace">A <see cref="T:System.String" /> containing the fully qualified name of the workflow type.</param>
      <param name="scheduleName">A <see cref="T:System.String" /> containing the workflow type name.</param>
      <param name="workflowMarkup">A <see cref="T:System.String" /> containing the XAML serialized format of the workflow type.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebugger.SetInitialActivityStatus(System.Guid,System.Guid,System.Guid,System.String,System.String,System.Workflow.ComponentModel.ActivityExecutionStatus,System.Int32)">
      <summary>Called to set the initial status of the activities in a workflow. </summary>
      <param name="programId">An internally generated <see cref="T:System.Guid" /> associated with a <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> instance.</param>
      <param name="scheduleTypeId">An internally generated <see cref="T:System.Guid" /> associated with a workflow type. </param>
      <param name="instanceId">A <see cref="T:System.Guid" /> associated with the currently running workflow instance.</param>
      <param name="activityQualifiedName">A <see cref="T:System.String" /> containing the fully qualified name of the current activity.</param>
      <param name="hierarchicalActivityId">A <see cref="T:System.String" /> containing the qualified name of the parent activity.</param>
      <param name="status">The <see cref="P:System.Workflow.ComponentModel.Activity.ExecutionStatus" /> of the current activity.</param>
      <param name="stateReaderId">An <see cref="T:System.Int32" /> containing the activity execution context ID associated with the current activity.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebugger.UpdateHandlerMethodsForActivity(System.Guid,System.Guid,System.String,System.Collections.Generic.List{System.Workflow.Runtime.DebugEngine.ActivityHandlerDescriptor})">
      <summary>Called when an activity enters the Executing state.</summary>
      <param name="programId">An internally generated <see cref="T:System.Guid" /> associated with a <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> instance.</param>
      <param name="scheduleTypeId">An internally generated <see cref="T:System.Guid" /> associated with a workflow type. </param>
      <param name="activityQualifiedName">A <see cref="T:System.String" /> containing the fully qualified name of the current activity.</param>
      <param name="handlerMethods">A <see cref="T:System.Collections.Generic.List`1" /> of handlers associated with an activity.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.DebugEngine.IWorkflowDebuggerService">
      <summary>Notifies workflow debugger that code condition evaluation events have occurred.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebuggerService.NotifyHandlerInvoked">
      <summary>Called after a code condition handler is invoked.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.IWorkflowDebuggerService.NotifyHandlerInvoking(System.Delegate)">
      <summary>Called before a code condition handler is invoked.</summary>
      <param name="delegateHandler">The code condition handler that will be invoked.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.DebugEngine.WorkflowDebuggerSteppingAttribute">
      <summary>Specifies the debug stepping behavior for composite activities whose child activities can execute concurrently.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.DebugEngine.WorkflowDebuggerSteppingAttribute.#ctor(System.Workflow.Runtime.DebugEngine.WorkflowDebuggerSteppingOption)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.DebugEngine.WorkflowDebuggerSteppingAttribute" /> class by using a <see cref="T:System.Workflow.Runtime.DebugEngine.WorkflowDebuggerSteppingOption" /> enumeration value.</summary>
      <param name="steppingOption">A <see cref="T:System.Workflow.Runtime.DebugEngine.WorkflowDebuggerSteppingOption" /> enumeration value specifying the stepping behavior for the concurrently executing child activities.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.DebugEngine.WorkflowDebuggerSteppingAttribute.SteppingOption">
      <summary>Gets the stepping behavior for the composite activity.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.DebugEngine.WorkflowDebuggerSteppingOption" /> enumeration value.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.DebugEngine.WorkflowDebuggerSteppingOption">
      <summary>Specifies how the workflow debugger will step through concurrently executing child activities of a composite activity.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Workflow.Runtime.DebugEngine.WorkflowDebuggerSteppingOption.Sequential">
      <summary>The child activities will be debugged sequentially.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.DebugEngine.WorkflowDebuggerSteppingOption.Concurrent">
      <summary>The child activities will be debugged concurrently.</summary>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.DefaultWorkflowCommitWorkBatchService">
      <summary>Represents the default version of <see cref="T:System.Workflow.Runtime.Hosting.WorkflowCommitWorkBatchService" /> created by the workflow runtime engine if no other WorkflowCommitWorkBatch service is added.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowCommitWorkBatchService.#ctor">
      <summary>Initializes a <see cref="T:System.Workflow.Runtime.Hosting.DefaultWorkflowCommitWorkBatchService" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowCommitWorkBatchService.#ctor(System.Collections.Specialized.NameValueCollection)">
      <summary>Initializes a SharedConnectionWorkflowCommitWorkBatchService class using a collection of parameters for initialization.</summary>
      <param name="parameters">
        <see cref="T:System.Collections.Specialized.NameValueCollection" />. The constructor is invoked when the workflow runtime engine loads services from an application configuration file. The valid key is <see cref="P:System.Workflow.Runtime.Hosting.DefaultWorkflowCommitWorkBatchService.EnableRetries" />. This requests the <see cref="T:System.Workflow.Runtime.Hosting.DefaultWorkflowCommitWorkBatchService" /> to retry in case of a failed database connection.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowCommitWorkBatchService.CommitWorkBatch(System.Workflow.Runtime.Hosting.WorkflowCommitWorkBatchService.CommitWorkBatchCallback)">
      <summary>Creates a work batch if one does not exist.</summary>
      <param name="commitWorkBatchCallback">The <see cref="T:System.Workflow.Runtime.Hosting.WorkflowCommitWorkBatchService.CommitWorkBatchCallback" />  delegate to call to commit the work batch.</param>
    </member>
    <member name="P:System.Workflow.Runtime.Hosting.DefaultWorkflowCommitWorkBatchService.EnableRetries">
      <summary>Gets and sets a value specifying whether the <see cref="T:System.Workflow.Runtime.Hosting.DefaultWorkflowCommitWorkBatchService" /> will retry committing a work batch. </summary>
      <returns>true if the service should retry committing the work batch; otherwise, false. The default is false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowCommitWorkBatchService.OnStopped">
      <summary>Represents the method called when the workflow runtime raises the <see cref="E:System.Workflow.Runtime.WorkflowRuntime.Stopped" /> event.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowCommitWorkBatchService.Start">
      <summary>Represents the method called to start the service.</summary>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.DefaultWorkflowLoaderService">
      <summary>Represents the default version of <see cref="T:System.Workflow.Runtime.Hosting.WorkflowLoaderService" /> created by the workflow runtime engine if no other workflow loader service is added.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowLoaderService.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.DefaultWorkflowLoaderService" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowLoaderService.CreateInstance(System.Type)">
      <summary>Creates a root activity definition tree by using the specified workflow <see cref="T:System.Type" />.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Activity" /> object that represents the root activity definition tree. </returns>
      <param name="workflowType">The <see cref="T:System.Type" /> of the workflow to create. </param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowLoaderService.CreateInstance(System.Xml.XmlReader,System.Xml.XmlReader)">
      <summary>Creates a new root activity definition tree by deserializing the .xoml and the .rules files.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Activity" /> object that represents the root activity definition tree. </returns>
      <param name="workflowDefinitionReader">
        <see cref="T:System.Xml.XmlReader" /> that holds the workflow XOML definition (.xoml file).</param>
      <param name="rulesReader">
        <see cref="T:System.Xml.XmlReader" /> that holds the workflow rules (.rules file).</param>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService">
      <summary>Creates and manages the threads that run workflow instances on the workflow runtime engine. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService" /> class.  </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService.#ctor(System.Collections.Specialized.NameValueCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService" /> class.  </summary>
      <param name="parameters">A <see cref="T:System.Collections.Specialized.NameValueCollection" /> that holds initialization information.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="parameters" /> contains a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">One of the key values in the <paramref name="parameters" /> collection contains a null reference (Nothing).--or--One of the key values in the <paramref name="parameters" /> collection does not match the default key for <see cref="T:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService" />.</exception>
      <exception cref="T:System.FormatException">One of the parameters cannot be converted to a double-precision floating point number that matches <see cref="P:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService.MaxSimultaneousWorkflows" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService.MaxSimultaneousWorkflows" /> is less than 1.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService" /> class by using the specified maximum number of workflow instances that the service can run simultaneously. </summary>
      <param name="maxSimultaneousWorkflows">An integer that determines the maximum number of workflow instances that can be stored in the thread pool queue.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maxSimultaneousWorkflows" /> is less than 1.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService.Cancel(System.Guid)">
      <summary>Cancels the specified existing workflow instance work item.</summary>
      <param name="timerId">The <see cref="T:System.Guid" /> associated with the existing scheduled work item to cancel. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="timerId" /> is an empty GUID.</exception>
    </member>
    <member name="P:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService.MaxSimultaneousWorkflows">
      <summary>Gets the maximum number of workflow instances that can be stored in the thread pool queue.</summary>
      <returns>An integer that represents the maximum number of workflow instances that can be stored in the thread pool queue.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService.OnStarted">
      <summary>Notifies the <see cref="T:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService" /> that a workflow instance has started to run.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService.Schedule(System.Threading.WaitCallback,System.Guid)">
      <summary>Adds a workflow instance to the pending work items queue using the specified workflow instance using the specified multicast delegate. </summary>
      <param name="callback">A <see cref="T:System.Threading.WaitCallback" /> multicast delegate that represents the method to run.</param>
      <param name="workflowInstanceId">A <see cref="T:System.Guid" /> that represents the workflow instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService.Schedule(System.Threading.WaitCallback,System.Guid,System.DateTime,System.Guid)">
      <summary>Adds the specified workflow instance to the pending work item queue using the specified multicast delegate, <see cref="T:System.DateTime" /> and GUIDs. </summary>
      <param name="callback">A <see cref="T:System.Threading.WaitCallback" /> multicast delegate that represents the method to run.</param>
      <param name="workflowInstanceId">A <see cref="T:System.Guid" /> that represents the workflow instance.</param>
      <param name="whenUtc">The <see cref="T:System.DateTime" /> that indicates the time to begin running the thread.</param>
      <param name="timerId">A <see cref="T:System.Guid" /> that represents the scheduled thread.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> contains a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="workflowInstanceId" /> or <paramref name="timerId" /> is an empty GUID.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.DefaultWorkflowSchedulerService.Stop">
      <summary>Stops the currently running thread on the workflow instance and any timers that are running. </summary>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService">
      <summary>Provides a threading service that allows the host application creating a workflow instance to donate the <see cref="T:System.Threading.Thread" /> on which the workflow instance is run. Using this threading service, host applications can run a workflow instance on a single <see cref="T:System.Threading.Thread" /> in synchronous mode (although if the workflow contains a delay activity, the work is postponed until after the delay activity is executed on a separate thread spawned by <see cref="System.Threading.Timer" />).This mode blocks the execution of the host application until the workflow instance becomes idle. Subsequently, the workflow instance can only be executed using the <see cref="M:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService.RunWorkflow(System.Guid)" /> method of this service.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService" /> class.  </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService" /> class. </summary>
      <param name="useActiveTimers">Boolean that determines how delay activities are handled. If true, the scheduler service automatically resumes workflows after delay activities expire (by using an in-memory timer). If false, the host must manually resume the workflow after the delay activities expire.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService.#ctor(System.Collections.Specialized.NameValueCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService" /> class. </summary>
      <param name="parameters">A <see cref="T:System.Collections.Specialized.NameValueCollection" /> that contains parameters for <paramref name="useActiveTimers" />. If true, the scheduler service automatically resumes workflows after delay activities expire (by using an in-memory timer). If false, the host must manually resume the workflow after the delay activities expire.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService.Cancel(System.Guid)">
      <summary>Cancels the specified existing workflow instance work item.</summary>
      <param name="timerId">The <see cref="T:System.Guid" /> associated with the existing scheduled work item to cancel.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService.OnStarted">
      <summary>Overloaded from <see cref="M:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.OnStarted" />. This method is called whenever <see cref="M:System.Workflow.Runtime.WorkflowRuntime.StartRuntime" /> is called and is used by the <see cref="T:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService" /> to do work that is needed when the <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> starts.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService.RunWorkflow(System.Guid)">
      <summary>Runs the specified workflow instance.</summary>
      <returns>true if the workflow starts running; otherwise, false.</returns>
      <param name="workflowInstanceId">The <see cref="T:System.Guid" /> for the workflow instance to run.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="workflowInstanceId" /> is an empty GUID.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService.Schedule(System.Threading.WaitCallback,System.Guid)">
      <summary>Adds a workflow instance to the pending work items queue using the specified workflow instance using the specified multicast delegate. </summary>
      <param name="callback">A <see cref="T:System.Threading.WaitCallback" /> multicast delegate that represents the method to run.</param>
      <param name="workflowInstanceId">A <see cref="T:System.Guid" /> that represents the workflow instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> contains a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService.Schedule(System.Threading.WaitCallback,System.Guid,System.DateTime,System.Guid)">
      <summary>Adds the specified workflow instance to the pending work item queue using the specified multicast delegate, <see cref="T:System.DateTime" /> and GUIDs. </summary>
      <param name="callback">A <see cref="T:System.Threading.WaitCallback" /> multicast delegate that represents the method to run.</param>
      <param name="workflowInstanceId">A <see cref="T:System.Guid" /> that represents the workflow instance.</param>
      <param name="whenUtc">The <see cref="T:System.DateTime" /> that indicates the time to begin running the thread.</param>
      <param name="timerId">A <see cref="T:System.Guid" /> that represents the scheduled thread.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> contains a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="workflowInstanceId" /> or <paramref name="timerId" /> is an empty GUID.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService.Stop">
      <summary>Overloaded from <see cref="M:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.Stop" />. The <see cref="T:System.Workflow.Runtime.Hosting.ManualWorkflowSchedulerService" /> uses this method to do work that is needed when the <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> stops.</summary>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.PersistenceException">
      <summary>The exception that is thrown when the persistence service cannot fulfill a request. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.PersistenceException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.PersistenceException" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.PersistenceException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.PersistenceException" /> class by using the specified <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and the specified <see cref="T:System.Runtime.Serialization.StreamingContext" />. </summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that holds contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.PersistenceException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.PersistenceException" /> class by using the specified message.</summary>
      <param name="message">A description of the cause of the exception.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.PersistenceException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.PersistenceException" /> by using the specified message and <see cref="T:System.Exception" />.</summary>
      <param name="message">A description of the cause of the exception.</param>
      <param name="innerException">The exception that caused the <see cref="T:System.Workflow.Runtime.Hosting.PersistenceException" /> to be thrown.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.SharedConnectionWorkflowCommitWorkBatchService">
      <summary>Represents the shared-connection version of the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowCommitWorkBatchService" /> service used by the runtime. In this context, shared-connection means that the service uses the same SQL connection for both the tracking and persistence services. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SharedConnectionWorkflowCommitWorkBatchService.#ctor(System.Collections.Specialized.NameValueCollection)">
      <summary>Initializes a <see cref="T:System.Workflow.Runtime.Hosting.SharedConnectionWorkflowCommitWorkBatchService" /> class.</summary>
      <param name="parameters">
        <see cref="T:System.Collections.Specialized.NameValueCollection" />. The constructor is invoked when the workflow runtime engine loads services from an application configuration file. The valid key is <paramref name="EnableRetries" />. This requests that the <see cref="T:System.Workflow.Runtime.Hosting.SharedConnectionWorkflowCommitWorkBatchService" /> retry in case of a failed database connection.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SharedConnectionWorkflowCommitWorkBatchService.#ctor(System.String)">
      <summary>Initializes a <see cref="T:System.Workflow.Runtime.Hosting.SharedConnectionWorkflowCommitWorkBatchService" /> class using a database connection string.</summary>
      <param name="connectionString">Database connection string.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SharedConnectionWorkflowCommitWorkBatchService.CommitWorkBatch(System.Workflow.Runtime.Hosting.WorkflowCommitWorkBatchService.CommitWorkBatchCallback)">
      <summary>Creates a transaction if one does not exist.</summary>
      <param name="commitWorkBatchCallback">The <see cref="T:System.Workflow.Runtime.Hosting.WorkflowCommitWorkBatchService.CommitWorkBatchCallback" />  delegate to call to commit the work batch.</param>
    </member>
    <member name="P:System.Workflow.Runtime.Hosting.SharedConnectionWorkflowCommitWorkBatchService.EnableRetries">
      <summary>Gets and sets a value specifying whether the <see cref="T:System.Workflow.Runtime.Hosting.SharedConnectionWorkflowCommitWorkBatchService" /> will retry committing a work batch.</summary>
      <returns>true if the service should retry committing the work batch; otherwise, false. The default is false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SharedConnectionWorkflowCommitWorkBatchService.OnStopped">
      <summary>Represents the method called when the workflow runtime raises the <see cref="E:System.Workflow.Runtime.WorkflowRuntime.Stopped" /> event.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SharedConnectionWorkflowCommitWorkBatchService.Start">
      <summary>Represents the method called to start the service.</summary>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.SqlPersistenceWorkflowInstanceDescription">
      <summary>Describes the workflow instances that are stored in the <see cref="T:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Hosting.SqlPersistenceWorkflowInstanceDescription.IsBlocked">
      <summary>Gets a value that shows whether the workflow instance is blocked.</summary>
      <returns>true if the workflow instance is blocked; otherwise, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Hosting.SqlPersistenceWorkflowInstanceDescription.NextTimerExpiration">
      <summary>Gets the time in UTC format at which the next timer will expire.</summary>
      <returns>The time in UTC format at which the next timer will expire.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Hosting.SqlPersistenceWorkflowInstanceDescription.Status">
      <summary>Gets the status of the workflow instance.</summary>
      <returns>The status of the workflow instance.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Hosting.SqlPersistenceWorkflowInstanceDescription.SuspendOrTerminateDescription">
      <summary>Gets the description for the suspension or termination of the <see cref="T:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService" />.</summary>
      <returns>The description for the suspension or termination of the <see cref="T:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Hosting.SqlPersistenceWorkflowInstanceDescription.WorkflowInstanceId">
      <summary>Gets the workflow instance identifier.</summary>
      <returns>The workflow instance identifier.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService">
      <summary>Represents a persistence service that uses a SQL database to store workflow state information.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.#ctor(System.Collections.Specialized.NameValueCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService" /> class by using the specified parameters.</summary>
      <param name="parameters">A <see cref="T:System.Collections.Specialized.NameValueCollection" /> that contains startup parameters.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="parameters" /> contains an invalid database connection string.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="parameters " />is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService" /> class by using the database connection string.</summary>
      <param name="connectionString">A valid database connection string.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="connectionString" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.#ctor(System.String,System.Boolean,System.TimeSpan,System.TimeSpan)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService" /> class using a database connection string and other parameters. </summary>
      <param name="connectionString">A valid database connection string.</param>
      <param name="unloadOnIdle">true to unload the workflow when it is in an idle state.</param>
      <param name="instanceOwnershipDuration">The length of time that locks are maintained on idle workflows.</param>
      <param name="loadingInterval">The frequency at which the persistence service polls the database for workflows with expired timers.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="connectionString" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="P:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.EnableRetries">
      <summary>Gets and sets a value that specifies whether the <see cref="T:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService" /> retries committing a work batch. </summary>
      <returns>true if the service should retry committing the work batch; otherwise, false. The default is false.</returns>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.GetAllWorkflows">
      <summary>Retrieves instance descriptions of all persisted workflows.</summary>
      <returns>A list of all persisted workflows.</returns>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.LoadCompletedContextActivity(System.Guid,System.Workflow.ComponentModel.Activity)">
      <summary>Retrieves the specified completed scope from the database.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the completed scope.</returns>
      <param name="id">The <see cref="T:System.Guid" /> of the scope activity. </param>
      <param name="outerActivity">The <see cref="T:System.Workflow.ComponentModel.Activity" /> that encloses the scope activity.</param>
      <exception cref="T:System.InvalidOperationException">A scope that matches <paramref name="id" /> could not be found in the database.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.LoadExpiredTimerWorkflowIds">
      <summary>Retrieves a list of ids for workflows with expired timers.</summary>
      <returns>A list of ids for workflows with expired timers.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.LoadingInterval">
      <summary>Gets the length of the loading interval.</summary>
      <returns>The frequency at which the persistence service polls the database for workflows with expired timers.</returns>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.LoadWorkflowInstanceState(System.Guid)">
      <summary>Retrieves the specified workflow instance state from the database.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the root activity of the workflow instance.</returns>
      <param name="id">The <see cref="T:System.Guid" /> of the workflow instance state.</param>
      <exception cref="T:System.InvalidOperationException">A workflow instance state that matches <paramref name="id" /> could not be found in the database.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.OnStarted">
      <summary>Starts a new timer and recovers running workflow instances.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.SaveCompletedContextActivity(System.Workflow.ComponentModel.Activity)">
      <summary>Saves the state of the specified completed scope.</summary>
      <param name="completedScopeActivity">An <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the completed scope.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.SaveWorkflowInstanceState(System.Workflow.ComponentModel.Activity,System.Boolean)">
      <summary>Saves the specified workflow instance state.</summary>
      <param name="rootActivity">The root activity of the workflow instance.</param>
      <param name="unlock">true if the workflow instance should not be locked; false if the workflow instance should be locked.</param>
    </member>
    <member name="P:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.ServiceInstanceId">
      <summary>Gets the service instance identifier.</summary>
      <returns>The service instance identifier.</returns>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.Start">
      <summary>Starts the <see cref="T:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService" />.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.Stop">
      <summary>Stops the service and the timer. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.System#Workflow#Runtime#IPendingWork#Commit(System.Transactions.Transaction,System.Collections.ICollection)">
      <summary>Writes an <see cref="T:System.Collections.ICollection" /> of serialized state objects to the database.</summary>
      <param name="transaction">A <see cref="T:System.Transactions.Transaction" />.</param>
      <param name="items">The <see cref="T:System.Collections.ICollection" /> of work items to be written to the database.</param>
      <exception cref="T:System.Workflow.Runtime.Hosting.PersistenceException">An error occurred while trying to write to the database.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.System#Workflow#Runtime#IPendingWork#Complete(System.Boolean,System.Collections.ICollection)">
      <summary>Completes the work batch and releases any resources.</summary>
      <param name="succeeded">true if the commit succeeded; otherwise, false.</param>
      <param name="items">An <see cref="T:System.Collections.ICollection" /> of serialized state objects.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.System#Workflow#Runtime#IPendingWork#MustCommit(System.Collections.ICollection)">
      <summary>Returns a value that indicates whether the collection of serialized state objects should be written to the database.</summary>
      <returns>true indicates that the batch should be committed; <see cref="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.System.Workflow.Runtime.IPendingWork.MustCommit(System.Collections.ICollection)" /> always returns true. </returns>
      <param name="items">An <see cref="T:System.Collections.ICollection" /> of serialized state objects to be written to the database.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.UnloadOnIdle(System.Workflow.ComponentModel.Activity)">
      <summary>Returns a value that indicates whether the workflow is unloaded when it is in an idle state.</summary>
      <returns>true if the workflow is unloaded when it is in an idle state; otherwise, false.</returns>
      <param name="activity">An <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the root activity of the workflow instance.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.SqlWorkflowPersistenceService.UnlockWorkflowInstanceState(System.Workflow.ComponentModel.Activity)">
      <summary>Unlocks access to the specified workflow instance state.</summary>
      <param name="rootActivity">An <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the root activity of the workflow instance.</param>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.WorkflowCommitWorkBatchService">
      <summary>Allows custom logic for the commitment of work batches.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowCommitWorkBatchService.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowCommitWorkBatchService" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowCommitWorkBatchService.CommitWorkBatch(System.Workflow.Runtime.Hosting.WorkflowCommitWorkBatchService.CommitWorkBatchCallback)">
      <summary>Called to commit the work batch.</summary>
      <param name="commitWorkBatchCallback">The <see cref="T:System.Workflow.Runtime.Hosting.WorkflowCommitWorkBatchService.CommitWorkBatchCallback" />  delegate to call to commit the work batch. </param>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.WorkflowCommitWorkBatchService.CommitWorkBatchCallback">
      <summary>Commits a <see cref="T:System.Workflow.Runtime.Hosting.WorkflowCommitWorkBatchService" /> work batch.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.WorkflowLoaderService">
      <summary>The abstract base class from which workflow loader services are derived.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowLoaderService.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowLoaderService" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowLoaderService.CreateInstance(System.Type)">
      <summary>Creates a new workflow instance by using the specified workflow <see cref="T:System.Type" />.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.WorkflowInstance" /> object that represents the workflow instance created. </returns>
      <param name="workflowType">The <see cref="T:System.Type" /> of the workflow to create. </param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowLoaderService.CreateInstance(System.Xml.XmlReader,System.Xml.XmlReader)">
      <summary>Creates a workflow instance by using the specified <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.WorkflowInstance" /> object that represents the workflow instance created.</returns>
      <param name="workflowDefinitionReader">An <see cref="T:System.Xml.XmlReader" /> that contains the workflow definition.</param>
      <param name="rulesReader">An <see cref="T:System.Xml.XmlReader" />.</param>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.WorkflowPersistenceService">
      <summary>The abstract base class from which all persistence services are derived.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowPersistenceService.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowPersistenceService" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowPersistenceService.GetDefaultSerializedForm(System.Workflow.ComponentModel.Activity)">
      <summary>Retrieves the serialized default form of the <see cref="T:System.Workflow.ComponentModel.Activity" />. </summary>
      <returns>The serialized default form of the <see cref="T:System.Workflow.ComponentModel.Activity" />.</returns>
      <param name="activity">The <see cref="T:System.Workflow.ComponentModel.Activity" /> whose serialized form is requested.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowPersistenceService.GetIsBlocked(System.Workflow.ComponentModel.Activity)">
      <summary>Indicates whether the given activity is blocked.</summary>
      <returns>true if the given activity is blocked; otherwise, false. </returns>
      <param name="rootActivity">The root activity of the workflow instance.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowPersistenceService.GetSuspendOrTerminateInfo(System.Workflow.ComponentModel.Activity)">
      <summary>Retrieves the termination or suspend information of the given activity.</summary>
      <returns>A <see cref="T:System.String" /> that contains the termination or suspend information. </returns>
      <param name="rootActivity">The root activity of the workflow instance.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowPersistenceService.GetWorkflowStatus(System.Workflow.ComponentModel.Activity)">
      <summary>Retrieves the status of the workflow.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.WorkflowStatus" /> enumeration value that denotes the status of the workflow.  </returns>
      <param name="rootActivity">The root activity of the workflow instance.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowPersistenceService.LoadCompletedContextActivity(System.Guid,System.Workflow.ComponentModel.Activity)">
      <summary>When implemented in a derived class, loads the specified completed scope back into memory.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the completed scope.</returns>
      <param name="scopeId">The <see cref="T:System.Guid" /> of the completed scope.</param>
      <param name="outerActivity">An <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the activity that encloses the completed scope.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowPersistenceService.LoadWorkflowInstanceState(System.Guid)">
      <summary>When implemented in a derived class, loads the specified state of the workflow instance back into memory.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the root activity of the workflow instance.</returns>
      <param name="instanceId">The <see cref="T:System.Guid" /> of the root activity of the workflow instance.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowPersistenceService.RestoreFromDefaultSerializedForm(System.Byte[],System.Workflow.ComponentModel.Activity)">
      <summary>Restores the <see cref="T:System.Workflow.ComponentModel.Activity" /> from its serialized form.</summary>
      <returns>The restored <see cref="T:System.Workflow.ComponentModel.Activity" />.</returns>
      <param name="activityBytes">The serialized form of <see cref="T:System.Workflow.ComponentModel.Activity" />.</param>
      <param name="outerActivity">The outer <see cref="T:System.Workflow.ComponentModel.Activity" />, containing the <see cref="T:System.Workflow.ComponentModel.Activity" /> to restore.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowPersistenceService.SaveCompletedContextActivity(System.Workflow.ComponentModel.Activity)">
      <summary>When implemented in a derived class, saves the specified completed scope to a data store.</summary>
      <param name="activity">An <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the completed scope.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowPersistenceService.SaveWorkflowInstanceState(System.Workflow.ComponentModel.Activity,System.Boolean)">
      <summary>When implemented in a derived class, saves the workflow instance state to a data store.</summary>
      <param name="rootActivity">The root activity of the workflow instance.</param>
      <param name="unlock">true if the workflow instance should not be locked; false if the workflow instance should be locked.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowPersistenceService.UnloadOnIdle(System.Workflow.ComponentModel.Activity)">
      <summary>Determines whether a workflow should be unloaded when idle. </summary>
      <returns>If true, the workflow runtime engine unloads the specified workflow when it becomes idle. </returns>
      <param name="activity">An <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the completed scope.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowPersistenceService.UnlockWorkflowInstanceState(System.Workflow.ComponentModel.Activity)">
      <summary>When overridden in a derived class, unlocks the workflow instance state.</summary>
      <param name="rootActivity">The root activity of the workflow instance.</param>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.WorkflowRuntimeService">
      <summary>The abstract base class from which the workflow runtime engine core services are derived.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowRuntimeService" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.OnStarted">
      <summary>When overridden in a derived class, represents the method that will be called when the workflow runtime engine raises the <see cref="E:System.Workflow.Runtime.WorkflowRuntime.Started" /> event. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.OnStopped">
      <summary>When overridden in a derived class, represents the method that will be called when the workflow runtime engine raises the <see cref="E:System.Workflow.Runtime.WorkflowRuntime.Stopped" /> event.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.RaiseServicesExceptionNotHandledEvent(System.Exception,System.Guid)">
      <summary>Raises the <see cref="E:System.Workflow.Runtime.WorkflowRuntime.ServicesExceptionNotHandled" /> event.</summary>
      <param name="exception">The exception that could not be handled.</param>
      <param name="instanceId">The <see cref="T:System.Guid" /> of the workflow instance associated with the unhandled exception.</param>
    </member>
    <member name="P:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.Runtime">
      <summary>Gets the <see cref="T:System.Workflow.Runtime.WorkflowRuntime" /> for this service.</summary>
      <returns>The <see cref="T:System.Workflow.Runtime.Hosting.WorkflowRuntimeService" />.</returns>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.Start">
      <summary>When overridden in a derived class, starts the service and changes the <see cref="P:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.State" /> to <see cref="F:System.Workflow.Runtime.Hosting.WorkflowRuntimeServiceState.Starting" />.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.Runtime" /> is a null reference (Nothing in Visual Basic)-or-The service has already been started.</exception>
    </member>
    <member name="P:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.State">
      <summary>Gets the state of the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowRuntimeService" />.</summary>
      <returns>One of the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowRuntimeServiceState" /> values.</returns>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.Stop">
      <summary>When overridden in a derived class, stops the service and changes the <see cref="P:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.State" /> to <see cref="F:System.Workflow.Runtime.Hosting.WorkflowRuntimeServiceState.Stopping" />.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.Runtime" /> is a null reference (Nothing in Visual Basic).-or-The service has not yet been started.</exception>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.WorkflowRuntimeServiceState">
      <summary>Specifies the state of the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowRuntimeService" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Workflow.Runtime.Hosting.WorkflowRuntimeServiceState.Stopped">
      <summary>Indicates that the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowRuntimeService" /> has stopped.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Hosting.WorkflowRuntimeServiceState.Starting">
      <summary>Indicates that the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowRuntimeService" /> is starting. <see cref="M:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.Start" /> changes the state of the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowRuntimeService" /> to <see cref="F:System.Workflow.Runtime.Hosting.WorkflowRuntimeServiceState.Starting" />.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Hosting.WorkflowRuntimeServiceState.Started">
      <summary>Indicates that the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowRuntimeService" /> has started. </summary>
    </member>
    <member name="F:System.Workflow.Runtime.Hosting.WorkflowRuntimeServiceState.Stopping">
      <summary>Indicates that the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowRuntimeService" /> is stopping. <see cref="M:System.Workflow.Runtime.Hosting.WorkflowRuntimeService.Stop" /> changes the state of the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowRuntimeService" /> to <see cref="F:System.Workflow.Runtime.Hosting.WorkflowRuntimeServiceState.Stopping" />.</summary>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.WorkflowSchedulerService">
      <summary>Provides a mechanism to implement your own thread pool to execute the workflow and manage in-memory timer registration and events.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowSchedulerService.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowSchedulerService" /> class.  </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowSchedulerService.Cancel(System.Guid)">
      <summary>When overridden in a derived class, cancels the scheduled work item with the given <paramref name="timerId" />.</summary>
      <param name="timerId">The <see cref="T:System.Guid" /> associated with the existing scheduled thread to cancel.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowSchedulerService.Schedule(System.Threading.WaitCallback,System.Guid)">
      <summary>When overridden in a derived class, this method is called by the runtime to schedule a work item (callback) for a particular instance ID.</summary>
      <param name="callback">A <see cref="T:System.Threading.WaitCallback" /> multicast delegate that represents the method to run. </param>
      <param name="workflowInstanceId">A <see cref="T:System.Guid" /> that represents the workflow instance.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowSchedulerService.Schedule(System.Threading.WaitCallback,System.Guid,System.DateTime,System.Guid)">
      <summary>When overridden in a derived class, this method is called by the runtime to schedule a work item (callback) for a particular workflow instance to be done at the given time (<see cref="T:System.DateTime" />).</summary>
      <param name="callback">A <see cref="T:System.Threading.WaitCallback" /> multicast delegate that represents the method to run.</param>
      <param name="workflowInstanceId">A <see cref="T:System.Guid" /> that represents the workflow instance to add.</param>
      <param name="whenUtc">The <see cref="T:System.DateTime" /> to begin running the workflow item.</param>
      <param name="timerId">A <see cref="T:System.Guid" /> that represents the scheduled timer.</param>
    </member>
    <member name="T:System.Workflow.Runtime.Hosting.WorkflowWebHostingModule">
      <summary>Provides a mechanism for routing the workflow instance ID to and from a <see cref="T:System.Workflow.Activities.WorkflowWebService" /> to a cookie in the Web client. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowWebHostingModule.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowWebHostingModule" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowWebHostingModule.System#Web#IHttpModule#Dispose">
      <summary>Releases the resources used by the <see cref="T:System.Workflow.Runtime.Hosting.WorkflowWebHostingModule" />.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Hosting.WorkflowWebHostingModule.System#Web#IHttpModule#Init(System.Web.HttpApplication)">
      <summary>Initializes the workflow Web hosting module and prepares the module to handle requests. </summary>
      <param name="application">An <see cref="T:System.Web.HttpApplication" /> that provides access to the methods, properties, and events common to all application objects within an ASP.NET application.</param>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.ActivityDataTrackingExtract">
      <summary>Specifies a property or a field to be extracted from an activity and sent to the tracking service together with an associated collection of annotations when a track point is matched.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityDataTrackingExtract.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityDataTrackingExtract" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityDataTrackingExtract.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityDataTrackingExtract" /> class by using the specified activity member name. </summary>
      <param name="member">The dot delineated name of the field or the property that should be extracted and sent to the tracking service.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityDataTrackingExtract.Annotations">
      <summary>Gets the collection of annotations associated with the extracted data.</summary>
      <returns>The <see cref="T:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection" /> associated with the activity property or field to be extracted. The default is an empty collection.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityDataTrackingExtract.Member">
      <summary>Gets or sets the field or the property to be extracted from the associated activity when a track point is matched.</summary>
      <returns>A dot delineated name that specifies a field or a property of the activity. The default is a null reference (Nothing in Visual Basic). </returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.ActivityTrackingCondition">
      <summary>Represents a condition that compares the value of an activity member to a specified value by using a specified comparison operator.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackingCondition.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingCondition" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackingCondition.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingCondition" /> class by specifying an activity member and a value for comparison.</summary>
      <param name="member">The dot delineated name of an activity member.</param>
      <param name="value">The value to be compared</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> is a null reference (Nothing in Visual Basic). </exception>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingCondition.Member">
      <summary>Gets or sets the name of the activity member that is to be compared with <see cref="P:System.Workflow.Runtime.Tracking.ActivityTrackingCondition.Value" />.</summary>
      <returns>The dot delineated name of the activity member</returns>
      <exception cref="T:System.ArgumentNullException">The condition was evaluated and <see cref="P:System.Workflow.Runtime.Tracking.ActivityTrackingCondition.Member" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">The condition was evaluated and <see cref="P:System.Workflow.Runtime.Tracking.ActivityTrackingCondition.Member" /> is incorrectly formed.</exception>
      <exception cref="T:System.MissingMemberException">The condition was evaluated and <see cref="P:System.Workflow.Runtime.Tracking.ActivityTrackingCondition.Member" /> does not refer to a valid activity member.</exception>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingCondition.Operator">
      <summary>Gets or sets the operator that is used in the comparison.</summary>
      <returns>One of the <see cref="T:System.Workflow.Runtime.Tracking.ComparisonOperator" /> values. The default is Equals.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingCondition.Value">
      <summary>The value that is to be compared with <see cref="P:System.Workflow.Runtime.Tracking.ActivityTrackingCondition.Member" />.</summary>
      <returns>The value used in the comparison. </returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocation">
      <summary>Defines an activity-qualified location that corresponds to an activity status event in the potential execution path of a root workflow instance. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackingLocation.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocation" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackingLocation.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocation" /> class by using the specified activity name. </summary>
      <param name="activityTypeName">The unqualified name of the common language runtime (CLR) type of an activity.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="activityTypeName" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackingLocation.#ctor(System.String,System.Boolean,System.Collections.Generic.IEnumerable{System.Workflow.ComponentModel.ActivityExecutionStatus})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocation" /> class that has a reference activity type with the specified unqualified name, that specifies whether to match activities derived from the reference activity type, and that matches the specified activity execution status events.</summary>
      <param name="activityTypeName">The unqualified name of the common language runtime (CLR) type of an activity.</param>
      <param name="matchDerivedTypes">true if activities derived from the reference activity type should be matched; otherwise, false.</param>
      <param name="executionStatusEvents">A collection that contains one or more of the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionStatus" /> values.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="activityTypeName" /> is a null reference (Nothing in Visual Basic)-or-<paramref name="executionStatusEvents" /> is a null reference (Nothing).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackingLocation.#ctor(System.String,System.Collections.Generic.IEnumerable{System.Workflow.ComponentModel.ActivityExecutionStatus})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocation" /> class that has a reference activity type with the specified unqualified name and that matches the specified activity execution status events.</summary>
      <param name="activityTypeName">The unqualified name of the common language runtime (CLR) type of an activity.</param>
      <param name="executionStatusEvents">A collection that contains one or more of the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionStatus" /> values.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="activityTypeName" /> is a null reference (Nothing in Visual Basic)-or-<paramref name="executionStatusEvents" /> is a null reference (Nothing).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackingLocation.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocation" /> class by using the specified reference activity type.</summary>
      <param name="activityType">The <see cref="T:System.Type" /> of an activity.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="activityType" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackingLocation.#ctor(System.Type,System.Boolean,System.Collections.Generic.IEnumerable{System.Workflow.ComponentModel.ActivityExecutionStatus})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocation" /> class that has the specified reference activity type, that specifies whether to match activities derived from the reference activity type, and that matches the specified activity execution status events.</summary>
      <param name="activityType">The <see cref="T:System.Type" /> of an activity.</param>
      <param name="matchDerivedTypes">true if activities derived from the reference activity type should be matched; otherwise, false.</param>
      <param name="executionStatusEvents">A collection that contains one or more of the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionStatus" /> values.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="activityType" /> is a null reference (Nothing in Visual Basic).-or-<paramref name="executionStatusEvents" /> is a null reference (Nothing).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackingLocation.#ctor(System.Type,System.Collections.Generic.IEnumerable{System.Workflow.ComponentModel.ActivityExecutionStatus})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocation" /> class that has the specified reference activity type and matches the specified activity execution status events.</summary>
      <param name="activityType">The <see cref="T:System.Type" /> of an activity.</param>
      <param name="executionStatusEvents">A collection that contains one or more of the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionStatus" /> values.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="activityType" /> is a null reference (Nothing in Visual Basic).-or-<paramref name="executionStatusEvents" /> is a null reference (Nothing).</exception>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingLocation.ActivityType">
      <summary>Gets or sets the common language runtime (CLR) type of the reference activity to be matched.</summary>
      <returns>The <see cref="T:System.Type" /> of the activity.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingLocation.ActivityTypeName">
      <summary>Gets or sets the unqualified name of the reference activity type for the location.</summary>
      <returns>The unqualified name of the reference activity type.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingLocation.Conditions">
      <summary>Gets a collection of conditions that are used to qualify interest in the activity.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.TrackingConditionCollection" /> that contains conditions that are used to qualify interest in the activity. The default is an empty collection.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingLocation.ExecutionStatusEvents">
      <summary>Gets the list of activity status events that can be matched for this location.</summary>
      <returns>A <see cref="T:System.Collections.Generic.IList`1" /> that contains one or more of the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionStatus" /> values. The default is an empty list.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingLocation.MatchDerivedTypes">
      <summary>Gets or sets a value that indicates whether activities derived from the reference activity type should be matched.</summary>
      <returns>true if activities derived from the reference activity type of the location should be matched; otherwise, false. The default is false.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocationCollection">
      <summary>Contains a collection of <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocation" /> objects.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackingLocationCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocationCollection" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackingLocationCollection.#ctor(System.Collections.Generic.IEnumerable{System.Workflow.Runtime.Tracking.ActivityTrackingLocation})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocationCollection" /> class by using a list of activity locations. </summary>
      <param name="locations">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> list of <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocation" /> objects.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="locations" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.ActivityTrackingRecord">
      <summary>Contains the data sent to a tracking service by the runtime tracking infrastructure when an <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackPoint" /> is matched. It is also used in the return list of the <see cref="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.ActivityEvents" /> property.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackingRecord.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingRecord" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackingRecord.#ctor(System.Type,System.String,System.Guid,System.Guid,System.Workflow.ComponentModel.ActivityExecutionStatus,System.DateTime,System.Int32,System.EventArgs)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingRecord" /> class by using the specified parameters.</summary>
      <param name="activityType">The <see cref="T:System.Type" /> of the activity associated with the activity status event.</param>
      <param name="qualifiedName">The <see cref="P:System.Workflow.ComponentModel.Activity.QualifiedName" /> of the activity associated with the activity status event.</param>
      <param name="contextGuid">The <see cref="T:System.Guid" /> that identifies the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> of the activity associated with the activity status event.</param>
      <param name="parentContextGuid">The <see cref="T:System.Guid" /> that identifies the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> of the parent activity of the activity associated with the activity status event.</param>
      <param name="executionStatus">One of the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionStatus" /> values.</param>
      <param name="eventDateTime">A <see cref="T:System.DateTime" /> that indicates the date and time of the activity status event associated with the tracking record.</param>
      <param name="eventOrder">The relative order in which the activity status event associated with this tracking record occurred in the workflow instance.</param>
      <param name="eventArgs">A null reference (Nothing in Visual Basic). This field is not set for an <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingRecord" />.</param>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingRecord.ActivityType">
      <summary>Gets or sets the common language runtime (CLR) type of the activity associated with this <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingRecord" />.</summary>
      <returns>The <see cref="T:System.Type" /> of the activity for which this <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingRecord" /> was created.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingRecord.Annotations">
      <summary>Gets the collection of annotations associated with the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackPoint" /> that was matched.</summary>
      <returns>An <see cref="T:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection" /> that contains the annotations that are associated with the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackPoint" /> that corresponds to this tracking record.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingRecord.Body">
      <summary>Gets a list that contains any data extracted from the workflow for the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackPoint" /> that was matched.</summary>
      <returns>A list of <see cref="T:System.Workflow.Runtime.Tracking.TrackingDataItem" /> objects each of which contains a single piece of extracted data and its associated annotations.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingRecord.ContextGuid">
      <summary>Context of the activity.</summary>
      <returns>
        <see cref="T:System.Guid" /> that identifies the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> of the activity associated with the activity status event.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingRecord.EventArgs">
      <summary>Always null for an <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingRecord" />.</summary>
      <returns>A null reference (Nothing) for an <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingRecord" />.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingRecord.EventDateTime">
      <summary>Gets or sets the date and time that the activity status event occurred.</summary>
      <returns>A <see cref="T:System.DateTime" /> value.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingRecord.EventOrder">
      <summary>Gets or sets a value that indicates the order in the workflow instance of the activity status event that matched the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackPoint" />.</summary>
      <returns>A value that indicates the order of the activity status event in the workflow instance.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingRecord.ExecutionStatus">
      <summary>Gets or sets the execution status of the activity associated with this <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingRecord" />. </summary>
      <returns>One of the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionStatus" /> values.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingRecord.ParentContextGuid">
      <summary>Context of the parent activity.</summary>
      <returns>
        <see cref="T:System.Guid" /> that identifies the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> of the activity associated with the activity status event.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackingRecord.QualifiedName">
      <summary>Gets or sets the identifier of the activity associated with this <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingRecord" />.</summary>
      <returns>The <see cref="P:System.Workflow.ComponentModel.Activity.QualifiedName" /> of the <see cref="T:System.Workflow.ComponentModel.Activity" /> for which this tracking record was created.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.ActivityTrackPoint">
      <summary>Defines a point, associated with an activity execution status change, to be tracked that is in the potential execution path of a workflow instance. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackPoint.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackPoint" /> class.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackPoint.Annotations">
      <summary>Gets the collection of annotations associated with the track point.</summary>
      <returns>An <see cref="T:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection" />. The default is an empty collection.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackPoint.ExcludedLocations">
      <summary>Gets the collection of locations that should be excluded from the track point by the runtime tracking infrastructure.</summary>
      <returns>An <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocationCollection" /> that specifies locations to be excluded from the track point. The default is an empty collection.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackPoint.Extracts">
      <summary>Gets a collection that specifies data to be extracted from the workflow instance and sent to the tracking service.</summary>
      <returns>An <see cref="T:System.Workflow.Runtime.Tracking.ExtractCollection" /> that specifies the data to be extracted and sent to the tracking service. The default is an empty collection. </returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ActivityTrackPoint.MatchingLocations">
      <summary>Gets the collection of locations that should be included in matching for the track point by the runtime tracking infrastructure.</summary>
      <returns>An <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocationCollection" /> that specifies the locations to be matched for the track point. The default is an empty collection. </returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.ActivityTrackPointCollection">
      <summary>Contains a collection of <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackPoint" /> objects. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackPointCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackPointCollection" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ActivityTrackPointCollection.#ctor(System.Collections.Generic.IEnumerable{System.Workflow.Runtime.Tracking.ActivityTrackPoint})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackPointCollection" /> class by using a list of activity track points.</summary>
      <param name="points">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> list of <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackPoint" /> objects.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="points" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.ComparisonOperator">
      <summary>Specifies the operation to perform on the operands of a comparison. </summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.ComparisonOperator.Equals">
      <summary>Test for operand equality.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.ComparisonOperator.NotEquals">
      <summary>Test for operand inequality.</summary>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.ExtractCollection">
      <summary>Contains a collection of <see cref="T:System.Workflow.Runtime.Tracking.TrackingExtract" /> objects. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ExtractCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ExtractCollection" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ExtractCollection.#ctor(System.Collections.Generic.IEnumerable{System.Workflow.Runtime.Tracking.TrackingExtract})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ExtractCollection" /> class by using a list of <see cref="T:System.Workflow.Runtime.Tracking.TrackingExtract" /> objects. </summary>
      <param name="extracts">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> list of <see cref="T:System.Workflow.Runtime.Tracking.TrackingExtract" /> objects.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="extracts" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.IProfileNotification">
      <summary>Provides a notification mechanism for a tracking service to inform the runtime tracking infrastructure about changes to the <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> for a particular workflow <see cref="T:System.Type" />. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Workflow.Runtime.Tracking.IProfileNotification.ProfileRemoved">
      <summary>Occurs when a <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> for a specific workflow Type is removed.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Workflow.Runtime.Tracking.IProfileNotification.ProfileUpdated">
      <summary>Occurs when a <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> for a specific workflow <see cref="T:System.Type" /> is updated.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.PreviousTrackingServiceAttribute">
      <summary>Indicates the type of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingService" /> that was used prior to the current version.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.PreviousTrackingServiceAttribute.#ctor(System.String)">
      <summary>Creates a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.PreviousTrackingServiceAttribute" /> class. </summary>
      <param name="assemblyQualifiedName">A <see cref="T:System.String" /> that contains a fully qualified assembly name that identifies the previous version of the tracking service.</param>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.PreviousTrackingServiceAttribute.AssemblyQualifiedName">
      <summary>Gets the fully qualified assembly name of the previous version tracking service that this version replaces.</summary>
      <returns>A <see cref="T:System.String" /> containing the fully qualified assembly name.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.ProfileRemovedEventArgs">
      <summary>Provides data for the <see cref="E:System.Workflow.Runtime.Tracking.IProfileNotification.ProfileRemoved" /> event. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ProfileRemovedEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ProfileRemovedEventArgs" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ProfileRemovedEventArgs.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ProfileRemovedEventArgs" /> class that applies to the specified workflow <see cref="T:System.Type" />.</summary>
      <param name="workflowType">The workflow <see cref="T:System.Type" /> for which the <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> should be removed. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ProfileRemovedEventArgs.WorkflowType">
      <summary>Gets or sets the workflow <see cref="T:System.Type" /> for which the <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> should be removed.</summary>
      <returns>The <see cref="T:System.Type" /> of a workflow.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.ProfileUpdatedEventArgs">
      <summary>Provides the data for the <see cref="E:System.Workflow.Runtime.Tracking.IProfileNotification.ProfileUpdated" /> event. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ProfileUpdatedEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ProfileUpdatedEventArgs" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.ProfileUpdatedEventArgs.#ctor(System.Type,System.Workflow.Runtime.Tracking.TrackingProfile)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.ProfileUpdatedEventArgs" /> class that specifies a new <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> for the specified workflow <see cref="T:System.Type" />.</summary>
      <param name="workflowType">The <see cref="T:System.Type" /> of workflow for which the <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> has changed. </param>
      <param name="profile">The new <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ProfileUpdatedEventArgs.TrackingProfile">
      <summary>Gets or sets the new <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> for the workflow Type.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.ProfileUpdatedEventArgs.WorkflowType">
      <summary>Gets or sets the <see cref="T:System.Type" /> of the workflow whose <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> should be updated.</summary>
      <returns>The <see cref="T:System.Type" /> of a workflow.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.SqlTrackingQuery">
      <summary>Contains methods and properties that are used to manage queries to the tracking data that is contained in the SQL database used by a <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingService" />.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.SqlTrackingQuery.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingQuery" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.SqlTrackingQuery.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingQuery" /> class.</summary>
      <param name="connectionString">The connection string of the SQL database to query.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="connectionString" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingQuery.ConnectionString">
      <summary>The connection string of the SQL tracking database to query.</summary>
      <returns>A SQL database connection string.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set <see cref="P:System.Workflow.Runtime.Tracking.SqlTrackingQuery.ConnectionString" /> to a null reference (Nothing in Visual Basic) was made.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.SqlTrackingQuery.GetWorkflows(System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions)">
      <summary>Returns tracking data for a set of workflow instances that correspond to a set of query parameters specified by a <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions" />.</summary>
      <returns>A list of <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance" /> objects that provide access to tracking data for the set of workflow instances with tracking data that matches the query parameters specified by <paramref name="options" />. </returns>
      <param name="options">A <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions" /> that specifies query parameters.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="options" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Workflow.Runtime.Tracking.SqlTrackingQuery.ConnectionString" /> is a null reference (Nothing).</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.SqlTrackingQuery.TryGetWorkflow(System.Guid,System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance@)">
      <summary>Tries to get query data for a specified workflow instance.</summary>
      <returns>true if tracking data is available for the requested workflow instance; otherwise, false. </returns>
      <param name="workflowInstanceId">The <see cref="T:System.Guid" /> of the workflow instance for which the tracking data is requested.</param>
      <param name="workflowInstance">When this method returns true, contains a <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance" /> that provides access to the tracking data associated with the workflow instance. This parameter is passed uninitiailized.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Workflow.Runtime.Tracking.SqlTrackingQuery.ConnectionString" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions">
      <summary>Contains properties that are used to constrain the set of <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance" /> objects returned by a call to <see cref="M:System.Workflow.Runtime.Tracking.SqlTrackingQuery.GetWorkflows(System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions)" />. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions" /> class.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions.Clear">
      <summary>Resets the properties of the <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions" /> to their default values.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions.StatusMaxDateTime">
      <summary>Gets or sets a <see cref="T:System.DateTime" /> that specifies the upper limit of the time period that, together with <see cref="P:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions.WorkflowStatus" />, is used to constrain the set of <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance" /> objects returned by a call to <see cref="M:System.Workflow.Runtime.Tracking.SqlTrackingQuery.GetWorkflows(System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions)" />.  </summary>
      <returns>A <see cref="T:System.DateTime" /> that specifies the upper limit of the time period used for matching workflow instances with a status specified by <see cref="P:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions.WorkflowStatus" />. The default is <see cref="F:System.DateTime.MinValue" />.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions.StatusMinDateTime">
      <summary>Gets or sets a <see cref="T:System.DateTime" /> that, together with <see cref="P:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions.WorkflowStatus" />, is used to constrain the set of <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance" /> objects returned by a call to <see cref="M:System.Workflow.Runtime.Tracking.SqlTrackingQuery.GetWorkflows(System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions)" />.   </summary>
      <returns>A <see cref="T:System.DateTime" /> that specifies the lower limit of the time period used for matching workflow instances with a status specified by <see cref="P:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions.WorkflowStatus" />. The default is <see cref="F:System.DateTime.MinValue" />.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions.TrackingDataItems">
      <summary>Gets or sets a list of data extract values which are used to constrain the set of <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance" /> objects returned by a call to <see cref="M:System.Workflow.Runtime.Tracking.SqlTrackingQuery.GetWorkflows(System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions)" />. </summary>
      <returns>A <see cref="T:System.Collections.Generic.List`1" /> of <see cref="T:System.Workflow.Runtime.Tracking.TrackingDataItemValue" /> objects that contains specified values to be matched for data extracted from the workflow instance or a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions.WorkflowStatus">
      <summary>Gets or sets the <see cref="T:System.Workflow.Runtime.WorkflowStatus" /> value that is used to constrain the set of <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance" /> objects returned by a call to <see cref="M:System.Workflow.Runtime.Tracking.SqlTrackingQuery.GetWorkflows(System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions)" />. </summary>
      <returns>One of the <see cref="T:System.Workflow.Runtime.WorkflowStatus" /> values or null (Nothing in Visual Basic). The default is null (Nothing).</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions.WorkflowType">
      <summary>Gets or sets the workflow instance <see cref="T:System.Type" /> that is used to constrain the set of <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance" /> objects returned by a call to <see cref="M:System.Workflow.Runtime.Tracking.SqlTrackingQuery.GetWorkflows(System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions)" />. </summary>
      <returns>A <see cref="T:System.Type" />. The default is a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.SqlTrackingService">
      <summary>Represents a tracking service that uses a SQL database to store tracking information. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.SqlTrackingService.#ctor(System.Collections.Specialized.NameValueCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingService" /> class by using the specified <see cref="T:System.Collections.Specialized.NameValueCollection" />.</summary>
      <param name="parameters">A <see cref="T:System.Collections.Specialized.NameValueCollection" /> that specifies properties of <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingService" /> and their initial values.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="parameters" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.FormatException">The string value associated with a Boolean property key in <paramref name="parameters" /> is not represented by either <see cref="F:System.Boolean.TrueString" /> or <see cref="F:System.Boolean.FalseString" />.</exception>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.SqlTrackingService.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingService" /> class by using the specified database connection string.</summary>
      <param name="connectionString">A valid database connection string.</param>
      <exception cref="T:System.ArgumentException">The database connection string is not valid.</exception>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingService.ConnectionString">
      <summary>Gets the connection string for the tracking database.</summary>
      <returns>The connection string for the tracking database.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingService.EnableRetries">
      <summary>Gets and sets a value specifying whether the <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingService" /> will retry when performing a database operation.</summary>
      <returns>true if the <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingService" /> should retry the operation; otherwise, false.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingService.IsTransactional">
      <summary>Gets or sets a value that indicates whether <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingService" /> participates in workflow transactions. </summary>
      <returns>true if <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingService" /> participates in the workflow transaction; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingService.PartitionOnCompletion">
      <summary>Gets or sets a value that specifies whether tracking data for a workflow instance should be moved to the currently active partition in the database when the workflow instance is completed.</summary>
      <returns>true if tracking data should be moved to the currently active partition on workflow completion; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingService.ProfileChangeCheckInterval">
      <summary>Gets or sets a value that specifies the interval at which the database should be checked for changes to one or more of its tracking profiles.</summary>
      <returns>The interval length in milliseconds. The default is 60000.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Workflow.Runtime.Tracking.SqlTrackingService.ProfileRemoved">
      <summary>Occurs when the <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingService" /> detects that a <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> has been deleted. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Workflow.Runtime.Tracking.SqlTrackingService.ProfileUpdated">
      <summary>Occurs when the <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingService" /> detects that a <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> has been changed. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingService.UseDefaultProfile">
      <summary>Gets or sets a value specifying whether a default <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> should be used for workflow types that do not have a tracking profile.</summary>
      <returns>true if a default tracking profile should be used; otherwise, false. The default is true.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance">
      <summary>Provides access to tracking data maintained in a SQL database by the <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingService" /> for a workflow instance.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.ActivityEvents">
      <summary>Gets the list of activity tracking records that have been sent for this workflow instance to a <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingService" /> database by the run-time tracking infrastructure.</summary>
      <returns>The list of <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingRecord" /> objects sent for this workflow instance to the tracking database by the run-time tracking infrastructure.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.AutoRefresh">
      <summary>Gets or sets a value that specifies whether property data for this <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance" /> should be automatically updated when it is accessed.</summary>
      <returns>true if property data should be automatically refreshed when it is accessed; otherwise, false.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.Initialized">
      <summary>Gets or sets a <see cref="T:System.DateTime" /> that indicates the time at which the first <see cref="T:System.Workflow.Runtime.Tracking.TrackingChannel" /> for this workflow instance was requested by the workflow run-time engine.</summary>
      <returns>A <see cref="T:System.DateTime" /> that indicates the time at which the first <see cref="T:System.Workflow.Runtime.Tracking.TrackingChannel" /> for this workflow instance was requested by the workflow run-time engine.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.InvokedWorkflows">
      <summary>Gets a list of <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance" /> objects that correspond to workflows that have been invoked by this workflow.</summary>
      <returns>A list of <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance" /> objects that correspond to workflows that have been invoked by this workflow.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.InvokingWorkflowInstanceId">
      <summary>Gets or sets the <see cref="T:System.Guid" /> of the workflow instance that invoked this workflow instance.</summary>
      <returns>The <see cref="T:System.Guid" /> of the workflow instance that invoked this workflow instance or an empty <see cref="T:System.Guid" /> if this workflow instance was not invoked by another workflow instance.</returns>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.Refresh">
      <summary>Updates the property data for this <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance" />.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.Status">
      <summary>Gets the status of the workflow instance.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.WorkflowStatus" /> that represents the current status of the workflow instance.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.UserEvents">
      <summary>Gets the list of user tracking records that have been sent for this workflow instance to a <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingService" /> database by the run-time tracking infrastructure.</summary>
      <returns>The list of <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingRecord" /> objects sent for this workflow instance to the tracking database by the run-time tracking infrastructure.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.WorkflowDefinition">
      <summary>Gets an <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the current workflow definition for the workflow instance.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the current workflow definition for the workflow instance.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.WorkflowDefinitionUpdated">
      <summary>Gets a value that indicates whether the workflow has been updated since the last time <see cref="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.WorkflowDefinition" /> was loaded.</summary>
      <returns>true if the workflow has been updated; otherwise false.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.WorkflowEvents">
      <summary>Gets the list of workflow tracking records that have been sent for this workflow instance to a <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingService" /> database by the run-time tracking infrastructure.</summary>
      <returns>The list of <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackingRecord" /> objects sent for this workflow instance to the tracking database by the run-time tracking infrastructure.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.WorkflowInstanceId">
      <summary>The <see cref="T:System.Guid" /> of the workflow instance for which this <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance" /> applies.</summary>
      <returns>The <see cref="T:System.Guid" /> of the workflow instance for which this <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance" /> applies.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.WorkflowInstanceInternalId">
      <summary>Gets or sets a number that can be used to find related records in separate views for this workflow instance.</summary>
      <returns>A number that can be used to find related records in separate views for this workflow instance.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.WorkflowType">
      <summary>Gets and sets the <see cref="T:System.Type" /> of the workflow instance.</summary>
      <returns>The <see cref="T:System.Type" /> of the workflow instance.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection">
      <summary>Contains a collection of annotations.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection" /> class</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection.#ctor(System.Collections.Generic.IEnumerable{System.String})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection" /> class by using a list of string annotations. </summary>
      <param name="annotations">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> list of string annotations.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="annotations" /> is a null reference (Nothing in Visual Basic)</exception>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingChannel">
      <summary>The abstract base class that represents a tracking channel.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingChannel.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingChannel" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingChannel.InstanceCompletedOrTerminated">
      <summary>When implemented in a derived class, notifies a receiver of data on the tracking channel that the workflow instance associated with the tracking channel has either completed or terminated.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingChannel.Send(System.Workflow.Runtime.Tracking.TrackingRecord)">
      <summary>When implemented in a derived class, sends a <see cref="T:System.Workflow.Runtime.Tracking.TrackingRecord" /> on the <see cref="T:System.Workflow.Runtime.Tracking.TrackingChannel" />.</summary>
      <param name="record">The <see cref="T:System.Workflow.Runtime.Tracking.TrackingRecord" /> to send. </param>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingCondition">
      <summary>An abstract base class representing a comparison that can be used to constrain an <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingLocation" /> or a <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocation" />.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingCondition.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingCondition" /> class.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingCondition.Member">
      <summary>When overridden in a derived class, gets or sets the name of the member whose value will be compared. </summary>
      <returns>The name of the member to be compared. </returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingCondition.Operator">
      <summary>When overridden in a derived class, gets or sets the operator to use in the comparison.</summary>
      <returns>One of the <see cref="T:System.Workflow.Runtime.Tracking.ComparisonOperator" /> values.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingCondition.Value">
      <summary>When overridden in a derived class, gets or sets the value to compare.</summary>
      <returns>The value to compare.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingConditionCollection">
      <summary>Contains a collection of <see cref="T:System.Workflow.Runtime.Tracking.TrackingCondition" /> objects.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingConditionCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingConditionCollection" /> class</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingConditionCollection.#ctor(System.Collections.Generic.IEnumerable{System.Workflow.Runtime.Tracking.TrackingCondition})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingConditionCollection" /> by using a list of <see cref="T:System.Workflow.Runtime.Tracking.TrackingCondition" /> objects. </summary>
      <param name="conditions">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> list of <see cref="T:System.Workflow.Runtime.Tracking.TrackingCondition" /> objects.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conditions" /> is a null reference (Nothing in Visual Basic)</exception>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingDataItem">
      <summary>Represents a single item of data extracted from a workflow and all its associated annotations.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingDataItem.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingDataItem" /> class.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingDataItem.Annotations">
      <summary>Gets and sets the list of annotations associated with the extracted data.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection" /> that contains the annotations associated with the extracted data.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingDataItem.Data">
      <summary>Gets or sets the extracted data associated with the <see cref="T:System.Workflow.Runtime.Tracking.TrackingDataItem" />.</summary>
      <returns>An <see cref="T:System.Object" /> that represents the extracted data.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingDataItem.FieldName">
      <summary>Gets or sets the name of the property or field associated with the extracted data.</summary>
      <returns>The dot delineated name of the property or field that was extracted.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingDataItemValue">
      <summary>Contains filter criteria for data extracted from a workflow instance in a <see cref="T:System.Workflow.Runtime.Tracking.SqlTrackingQueryOptions" />. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingDataItemValue.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingDataItemValue" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingDataItemValue.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingDataItemValue" /> class by using the specified qualified name of an activity, data field name, and string representation of a data value. </summary>
      <param name="qualifiedName">The qualified name of the activity from which the data was extracted.</param>
      <param name="fieldName">The name of the member from which the data was extracted.</param>
      <param name="dataValue">A string representation of the value of the data.</param>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingDataItemValue.DataValue">
      <summary>Gets or sets a string representation of the value of the extracted data. </summary>
      <returns>A string representation of the value of the extracted data. The default is a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingDataItemValue.FieldName">
      <summary>Gets or sets the name of the activity member from which the data was extracted.</summary>
      <returns>The name of the activity member from which the data was extracted. The default is a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingDataItemValue.QualifiedName">
      <summary>Gets or sets the qualified name of the activity from which the data was extracted.</summary>
      <returns>The qualified name of the activity from which the data was extracted. The default is a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingExtract">
      <summary>The abstract base class representing a field or a property to be extracted from a workflow instance and its associated annotations. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingExtract.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingExtract" /> class.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingExtract.Annotations">
      <summary>When implemented in a derived class, gets the annotations associated with the extracted data.</summary>
      <returns>The <see cref="T:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection" /> associated with the extracted data. </returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingExtract.Member">
      <summary>When implemented in a derived class, gets or sets the name of the field or property to be extracted. </summary>
      <returns>The dot delineated name of a field or a property. </returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingParameters">
      <summary>Contains information about the workflow instance associated with a <see cref="T:System.Workflow.Runtime.Tracking.TrackingChannel" />. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingParameters.#ctor(System.Guid,System.Type,System.Workflow.ComponentModel.Activity,System.Collections.Generic.IList{System.String},System.Guid,System.Guid,System.Guid,System.Guid)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingParameters" /> class. </summary>
      <param name="instanceId">The <see cref="T:System.Guid" /> of the workflow instance associated with the tracking channel.</param>
      <param name="workflowType">The <see cref="T:System.Type" /> of the workflow instance associated with the tracking channel.</param>
      <param name="rootActivity">The root <see cref="T:System.Workflow.ComponentModel.Activity" /> of the workflow instance associated with the tracking channel.</param>
      <param name="callPath">A list of strings, each of which represents the <see cref="P:System.Workflow.ComponentModel.Activity.QualifiedName" /> of an activity in the call chain of the workflow instance associated with the tracking channel. Currently, only the <see cref="P:System.Workflow.ComponentModel.Activity.QualifiedName" /> of the activity that invoked the workflow instance is included in the list.</param>
      <param name="callerInstanceId">The <see cref="T:System.Guid" /> of the workflow that has called the workflow instance associated with the tracking channel.</param>
      <param name="contextGuid">A number that identifies the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> of the root activity of the workflow instance associated with the tracking channel.</param>
      <param name="callerContextGuid">A number that identifies the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> of the activity that invoked the workflow instance associated with the tracking channel.</param>
      <param name="callerParentContextGuid">A number that identifies the parent <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> of the activity that invoked the workflow instance associated with the tracking channel.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingParameters.CallerContextGuid">
      <summary>Gets the context ID of the caller activity.</summary>
      <returns>A <see cref="T:System.Guid" /> that contains the context ID of the caller <see cref="T:System.Workflow.ComponentModel.Activity" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingParameters.CallerInstanceId">
      <summary>Gets the <see cref="T:System.Guid" /> of the workflow instance that called the workflow instance associated with the tracking channel.</summary>
      <returns>The <see cref="T:System.Guid" /> of the workflow instance that called the workflow instance associated with the tracking channel.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingParameters.CallerParentContextGuid">
      <summary>Gets the context ID of the caller's parent activity.</summary>
      <returns>A <see cref="T:System.Guid" /> that contains the context ID of the caller's parent <see cref="T:System.Workflow.ComponentModel.Activity" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingParameters.CallPath">
      <summary>Gets a list of strings, each of which represents the <see cref="P:System.Workflow.ComponentModel.Activity.QualifiedName" /> of an activity in the call chain of the workflow instance associated with the tracking channel. </summary>
      <returns>A list of strings, each of which represents the <see cref="P:System.Workflow.ComponentModel.Activity.QualifiedName" /> of an activity in the call chain of the workflow instance associated with the tracking channel.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingParameters.ContextGuid">
      <summary>Gets the context ID of the associated activity.</summary>
      <returns>A <see cref="T:System.Guid" /> that contains the context ID of the <see cref="T:System.Workflow.ComponentModel.Activity" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingParameters.InstanceId">
      <summary>Gets the <see cref="T:System.Guid" /> of the workflow instance associated with the tracking channel.</summary>
      <returns>The <see cref="T:System.Guid" /> of the workflow instance associated with the tracking channel.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingParameters.RootActivity">
      <summary>Gets the root activity of the workflow instance associated with the tracking channel.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the root activity of the workflow instance associated with the tracking channel.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingParameters.WorkflowType">
      <summary>Gets the <see cref="T:System.Type" /> of the workflow instance associated with the tracking channel.</summary>
      <returns>The <see cref="T:System.Type" /> of the workflow instance associated with the tracking channel.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingProfile">
      <summary>Defines points of interest in the potential execution path of a root workflow instance about which a tracking service should be notified.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingProfile.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" />.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingProfile.ActivityTrackPoints">
      <summary>Gets the collection of activity track points used by the runtime tracking infrastructure to filter activity status events.</summary>
      <returns>An <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackPointCollection" /> that specifies the points in a workflow instance for which the runtime tracking infrastructure should send an <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingRecord" /> to the tracking service.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingProfile.UserTrackPoints">
      <summary>Gets the collection of user track points used by the runtime tracking infrastructure to filter user events.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.UserTrackPointCollection" /> that specifies the points in a workflow instance for which the runtime tracking infrastructure should send a <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingRecord" /> to the tracking service.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingProfile.Version">
      <summary>Gets or sets the version of the tracking profile.</summary>
      <returns>The <see cref="T:System.Version" /> of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" />.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingProfile.WorkflowTrackPoints">
      <summary>Gets the collection of workflow track points used by the runtime tracking infrastructure to filter workflow status events.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackPointCollection" /> that specifies the points in a workflow instance for which the runtime tracking infrastructure should send a <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackingRecord" /> to the tracking service.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingProfileCache">
      <summary>Provides a method that a host application can use to clear the tracking profile cache.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingProfileCache.Clear">
      <summary>Clears the tracking profile cache maintained by the runtime tracking infrastructure of all tracking profiles.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingProfileDeserializationException">
      <summary>The exception that is thrown when an XML document cannot be deserialized into a <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> by a <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfileSerializer" />.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingProfileDeserializationException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfileDeserializationException" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingProfileDeserializationException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfileDeserializationException" /> class with a specified error message.</summary>
      <param name="message">The message that describes the error.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingProfileDeserializationException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfileDeserializationException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the error.</param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not a null reference (Nothing in Visual Basic), the current exception is raised in a catch block that handles the inner exception.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingProfileDeserializationException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with the collection of <see cref="T:System.Xml.Schema.ValidationEventArgs" /> associated with this exception, and additional exception information.</summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is a null reference (Nothing in Visual Basic)</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingProfileDeserializationException.ValidationEventArgs">
      <summary>Gets a list that contains validation warnings and errors associated with this exception.</summary>
      <returns>A <see cref="T:System.Collections.IList" /> of <see cref="T:System.Xml.Schema.ValidationEventArgs" /> objects that contains validation warnings and errors associated with this exception. The default is an empty list.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingProfileSerializer">
      <summary>Provides methods to serialize and deserialize tracking profiles into and from XML documents by using the tracking profile XML schema definition (XSD).</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingProfileSerializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfileSerializer" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingProfileSerializer.Deserialize(System.IO.TextReader)">
      <summary>Deserializes the XML document that is contained in the text reader by using the tracking profile XML Schema definition (XSD).</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> that contains the deserialized tracking profile.</returns>
      <param name="reader">A <see cref="T:System.IO.TextReader" /> that contains an XML document. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.Workflow.Runtime.Tracking.TrackingProfileDeserializationException">
        <paramref name="reader" /> does not contain a document that conforms to the tracking profile XSD.</exception>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingProfileSerializer.Schema">
      <summary>Gets the tracking profile XML schema definition (XSD).</summary>
      <returns>The tracking profile XSD.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingProfileSerializer.Serialize(System.IO.TextWriter,System.Workflow.Runtime.Tracking.TrackingProfile)">
      <summary>Serializes the tracking profile into an XML document by using the tracking profile XML Schema definition (XSD).</summary>
      <param name="writer">A valid <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" />.</param>
      <param name="profile">When this method returns, contains a <see cref="T:System.IO.TextWriter" /> that holds the XML document. The parameter is passed uninitialized.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="profile" /> is a null reference (Nothing in Visual Basic).- or -<paramref name="writer" /> is a null reference(Nothing).- or -</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="profile" /> is not a valid tracking profile.</exception>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingRecord">
      <summary>The abstract base class from which <see cref="T:System.Workflow.Runtime.Tracking.ActivityTrackingRecord" />, <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingRecord" />, and <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackingRecord" /> are derived.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingRecord.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingRecord" /> class.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingRecord.Annotations">
      <summary>When overridden in a derived class, gets the collection of annotations associated with the track point.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection" /> that contains the annotations associated with the track point to which this <see cref="T:System.Workflow.Runtime.Tracking.TrackingRecord" /> corresponds.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingRecord.EventArgs">
      <summary>When overridden in a derived class, gets or sets the event data, if any, that is associated with the tracking event that caused the tracking record to be sent.</summary>
      <returns>An <see cref="T:System.EventArgs" /> that represents the event data, if any, that is associated with the tracking event that caused the tracking record to be sent.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingRecord.EventDateTime">
      <summary>When overridden in a derived class, gets or sets the time and date of the tracking event associated with the tracking record.</summary>
      <returns>A <see cref="T:System.DateTime" /> that indicates the date and time that the tracking event occurred.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingRecord.EventOrder">
      <summary>When overridden in a derived class, gets or sets a value that indicates the order of the tracking event associated with the tracking record relative to the other tracking events emitted by the workflow instance. </summary>
      <returns>A value that indicates the order of the tracking event relative to the other tracking events emitted by the workflow instance.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingService">
      <summary>The abstract base class that provides the basic interface between a tracking service and the run-time tracking infrastructure.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingService.#ctor">
      <summary>When implemented in a derived class, initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingService" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingService.GetProfile(System.Guid)">
      <summary>Must be overridden in the derived class, and when implemented, returns the tracking profile for the specified workflow instance.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" />.</returns>
      <param name="workflowInstanceId">The <see cref="T:System.Guid" /> of the workflow instance.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingService.GetProfile(System.Type,System.Version)">
      <summary>Must be overridden in the derived class, and when implemented, returns the tracking profile, qualified by version, for the specified workflow <see cref="T:System.Type" />. </summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" />.</returns>
      <param name="workflowType">The <see cref="T:System.Type" /> of the workflow.</param>
      <param name="profileVersionId">The <see cref="T:System.Version" /> of the tracking profile. </param>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingService.GetTrackingChannel(System.Workflow.Runtime.Tracking.TrackingParameters)">
      <summary>Must be overridden in the derived class, and when implemented, returns the channel that the run-time tracking infrastructure uses to send tracking records to the tracking service.</summary>
      <returns>The <see cref="T:System.Workflow.Runtime.Tracking.TrackingChannel" /> that is used to send tracking records to the tracking service.</returns>
      <param name="parameters">The <see cref="T:System.Workflow.Runtime.Tracking.TrackingParameters" /> associated with the workflow instance.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingService.TryGetProfile(System.Type,System.Workflow.Runtime.Tracking.TrackingProfile@)">
      <summary>Must be overridden in the derived class, and when implemented, retrieves the tracking profile for the specified workflow type if one is available.</summary>
      <returns>true if a <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> for the specified workflow <see cref="T:System.Type" /> is available; otherwise, false. If true, the <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> is returned in <paramref name="profile" />.</returns>
      <param name="workflowType">The <see cref="T:System.Type" /> of the workflow for which to get the tracking profile.</param>
      <param name="profile">When this method returns, contains the <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> to load. This parameter is passed un-initialized.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.TrackingService.TryReloadProfile(System.Type,System.Guid,System.Workflow.Runtime.Tracking.TrackingProfile@)">
      <summary>Must be overridden in the derived class, and when implemented, retrieves a new tracking profile for the specified workflow instance if the tracking profile has changed since it was last loaded.</summary>
      <returns>true if a new <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> should be loaded; otherwise, false. If true, the <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> is returned in <paramref name="profile" />.</returns>
      <param name="workflowType">The <see cref="T:System.Type" /> of the workflow instance.</param>
      <param name="workflowInstanceId">The <see cref="T:System.Guid" /> of the workflow instance.</param>
      <param name="profile">When this method returns, contains the <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" /> to load. This parameter is passed un-initialized.</param>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingWorkflowChangedEventArgs">
      <summary>Contains data associated with a workflow change that occurs during the execution of a workflow instance.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingWorkflowChangedEventArgs.Changes">
      <summary>Gets the changes that occurred to the workflow instance.</summary>
      <returns>A list of <see cref="T:System.Workflow.ComponentModel.WorkflowChangeAction" /> objects that specify the changes that occurred to the workflow instance.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingWorkflowChangedEventArgs.Definition">
      <summary>Gets the workflow definition.</summary>
      <returns>An <see cref="T:System.Workflow.ComponentModel.Activity" /> that represents the root activity of the workflow instance.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent">
      <summary>Specifies a type of workflow status event.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent.Created">
      <summary>The workflow instance has been created.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent.Completed">
      <summary>The workflow instance has completed.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent.Idle">
      <summary>The workflow instance is idle.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent.Suspended">
      <summary>The workflow instance has been suspended.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent.Resumed">
      <summary>A previously suspended workflow instance has resumed running.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent.Persisted">
      <summary>The workflow instance has been persisted.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent.Unloaded">
      <summary>The workflow instance has been unloaded from memory.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent.Loaded">
      <summary>The workflow instance has been loaded into memory.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent.Exception">
      <summary>An unhandled exception has occurred.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent.Terminated">
      <summary>The workflow instance has been terminated.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent.Aborted">
      <summary>The workflow instance has aborted.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent.Changed">
      <summary>A workflow change has occurred on the workflow instance.</summary>
    </member>
    <member name="F:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent.Started">
      <summary>The workflow instance has been started.</summary>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingWorkflowExceptionEventArgs">
      <summary>Contains data associated with an exception that occurs during the execution of a workflow instance.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingWorkflowExceptionEventArgs.ContextGuid">
      <summary>Gets the context ID of the associated activity.</summary>
      <returns>A <see cref="T:System.Guid" /> that contains the context ID of the <see cref="T:System.Workflow.ComponentModel.Activity" />.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingWorkflowExceptionEventArgs.CurrentActivityPath">
      <summary>Gets the <see cref="P:System.Workflow.ComponentModel.Activity.QualifiedName" /> of the activity currently throwing the exception.</summary>
      <returns>The <see cref="P:System.Workflow.ComponentModel.Activity.QualifiedName" /> of the activity currently throwing the exception.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingWorkflowExceptionEventArgs.Exception">
      <summary>Gets the <see cref="T:System.Exception" /> that is being thrown by the workflow instance.</summary>
      <returns>An <see cref="T:System.Exception" />.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingWorkflowExceptionEventArgs.OriginalActivityPath">
      <summary>Gets the <see cref="P:System.Workflow.ComponentModel.Activity.QualifiedName" /> of the activity that originally threw the exception.</summary>
      <returns>The <see cref="P:System.Workflow.ComponentModel.Activity.QualifiedName" /> of the activity that originally threw the exception.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingWorkflowExceptionEventArgs.ParentContextGuid">
      <summary>Gets the context ID of the parent activity.</summary>
      <returns>A <see cref="T:System.Guid" /> that contains the context ID of the parent <see cref="T:System.Workflow.ComponentModel.Activity" />.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingWorkflowSuspendedEventArgs">
      <summary>Contains information about the reason that a workflow instance was suspended.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingWorkflowSuspendedEventArgs.Error">
      <summary>Contains information about the reason that a workflow instance was suspended. This can come from the <see cref="M:System.Workflow.Runtime.WorkflowInstance.Suspend(System.String)" /> (string error) or the <see cref="P:System.Workflow.ComponentModel.SuspendActivity.Error" /> property that you set on the <see cref="T:System.Workflow.ComponentModel.SuspendActivity" /> activity.</summary>
      <returns>String value that contains the reason that a workflow instance was suspended.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.TrackingWorkflowTerminatedEventArgs">
      <summary>Contains data associated with the termination of a workflow instance.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.TrackingWorkflowTerminatedEventArgs.Exception">
      <summary>Gets the exception that caused the workflow instance to be terminated.</summary>
      <returns>The <see cref="T:System.Exception" /> that caused the workflow instance to be terminated.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.UserTrackingLocation">
      <summary>Defines an activity-qualified location that corresponds to a user event in the potential execution path of a root workflow instance. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.UserTrackingLocation.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocation" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.UserTrackingLocation.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocation" /> class by using the unqualified name of the common language runtime (CLR) type of the user data.</summary>
      <param name="argumentTypeName">The unqualified name of the CLR type of the user data to be matched.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.UserTrackingLocation.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocation" /> class by using the unqualified name of the common language runtime (CLR) type of the user data and the unqualified name of the CLR type of the activity from which the user data must be emitted.</summary>
      <param name="argumentTypeName">The unqualified name of the CLR type of the user data to be matched.</param>
      <param name="activityTypeName">The unqualified name of the CLR type of the activity from which the user data must be emitted.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.UserTrackingLocation.#ctor(System.String,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocation" /> class by using the unqualified name of the common language runtime (CLR) type of the user data and the CLR type of the activity from which the user data must be emitted.</summary>
      <param name="argumentTypeName">The unqualified name of the CLR type of the user data to be matched.</param>
      <param name="activityType">The <see cref="T:System.Type" /> of the activity from which the user data must be emitted.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.UserTrackingLocation.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocation" /> class using the common language runtime (CLR) type of the user data.</summary>
      <param name="argumentType">The <see cref="T:System.Type" /> of the user data to be matched.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.UserTrackingLocation.#ctor(System.Type,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocation" /> class by using the common language runtime (CLR) type of the user data and unqualified name of the CLR type of the activity from which the user data must be emitted.</summary>
      <param name="argumentType">The <see cref="T:System.Type" /> of the user data to be matched.</param>
      <param name="activityTypeName">The unqualified name of the CLR type of the activity from which the user data must be emitted.</param>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.UserTrackingLocation.#ctor(System.Type,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocation" /> class by using the common language runtime (CLR) type of the user data and the CLR type of the activity from which the user data must be emitted.</summary>
      <param name="argumentType">The <see cref="T:System.Type" /> of the user data to be matched.</param>
      <param name="activityType">The <see cref="T:System.Type" /> of the activity from which the user data must be emitted.</param>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.ActivityType">
      <summary>Gets or sets the common language runtime (CLR) type of the activity from which the user data must be emitted to be tracked.</summary>
      <returns>The <see cref="T:System.Type" /> of the activity from which the data must be emitted.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.ActivityTypeName">
      <summary>Gets or sets the unqualified name of the common language runtime (CLR) type of the activity from which the user data must be emitted to be tracked.</summary>
      <returns>The unqualified name of the <see cref="T:System.Type" /> of the activity from which the data must be emitted.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.ArgumentType">
      <summary>Gets or sets the common language runtime (CLR) type of the user data to be tracked.</summary>
      <returns>The <see cref="T:System.Type" /> of the user data to be tracked.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.ArgumentTypeName">
      <summary>Gets or sets the unqualified name of the common language runtime (CLR) type of the user data to be tracked.</summary>
      <returns>The unqualified name of the <see cref="T:System.Type" /> of the user data.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.Conditions">
      <summary>Gets a collection of conditions that are used to qualify the activity from which the user data must be emitted to be tracked.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.TrackingConditionCollection" /> that is used to qualify the activity from which the user data must be emitted.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.KeyName">
      <summary>Get or sets the name with which the user data must be associated for the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocation" /> to be matched. </summary>
      <returns>A string key with which the user data must be associated for the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocation" /> to be matched or a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.MatchDerivedActivityTypes">
      <summary>Gets or sets a value that indicates whether the user data should be tracked when it is emitted from activities derived from the activity type specified by <see cref="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.ActivityType" /> or <see cref="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.ActivityTypeName" />.</summary>
      <returns>true if the user data should be tracked when it is emitted from activities derived from the type specified by <see cref="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.ActivityType" /> or <see cref="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.ActivityTypeName" />; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.MatchDerivedArgumentTypes">
      <summary>The User tracking location is a way to filter user tracked data. The user can filter by the type of the user tracked data by specifying the <see cref="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.ArgumentType" /> or <see cref="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.ArgumentTypeName" />. Setting the MatchDerrivedArgumentTypesBoolean value to true specifies that the user tracked data can be of type ArgumentType/ArgumentTypeName or a type derived from the ArgumentType/ArgumentTypeName.</summary>
      <returns>true if user data derived from the type of the user data specified by <see cref="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.ArgumentType" /> or <see cref="P:System.Workflow.Runtime.Tracking.UserTrackingLocation.ArgumentTypeName" /> should be tracked; otherwise, false. The default is false.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.UserTrackingLocationCollection">
      <summary>Contains a collection of <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocation" /> objects.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.UserTrackingLocationCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocationCollection" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.UserTrackingLocationCollection.#ctor(System.Collections.Generic.IEnumerable{System.Workflow.Runtime.Tracking.UserTrackingLocation})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocationCollection" /> class by using a list of user locations.</summary>
      <param name="locations">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> list of <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocation" /> objects. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="locations" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.UserTrackingRecord">
      <summary>Contains the data sent to a tracking service by the runtime tracking infrastructure when a <see cref="T:System.Workflow.Runtime.Tracking.UserTrackPoint" /> is matched.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.UserTrackingRecord.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingRecord" /> class</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.UserTrackingRecord.#ctor(System.Type,System.String,System.Guid,System.Guid,System.DateTime,System.Int32,System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingRecord" /> class by using the specified parameters.</summary>
      <param name="activityType">The <see cref="T:System.Type" /> of the activity associated with the user event.</param>
      <param name="qualifiedName">The <see cref="P:System.Workflow.ComponentModel.Activity.QualifiedName" /> of the activity associated with the user event.</param>
      <param name="contextGuid">A number that identifies the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> of the activity associated with the user event.</param>
      <param name="parentContextGuid">A number that identifies the <see cref="T:System.Workflow.ComponentModel.ActivityExecutionContext" /> of the parent activity of the activity associated with the user event.</param>
      <param name="eventDateTime">A <see cref="T:System.DateTime" /> that indicates the date and time of the user event associated with the tracking record.</param>
      <param name="eventOrder">The relative order in which the user event associated with this tracking record occurred in the workflow instance.</param>
      <param name="userDataKey">A key associated with the user data for this tracking record, or a null reference (Nothing in Visual Basic).</param>
      <param name="userData">The user data associated with the user event.</param>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingRecord.ActivityType">
      <summary>Gets or sets the common language runtime (CLR) type of the activity that emitted the user data for which this <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingRecord" /> was created.</summary>
      <returns>The <see cref="T:System.Type" /> of the activity that emitted the user data.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingRecord.Annotations">
      <summary>Gets the collection of annotations associated with the user event.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection" /> that contains the annotations that are associated with the user event by the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackPoint" /> that was matched in the <see cref="T:System.Workflow.Runtime.Tracking.TrackingProfile" />.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingRecord.Body">
      <summary>Gets a list containing any additional data extracted from the workflow for the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackPoint" /> that was matched.</summary>
      <returns>A list of <see cref="T:System.Workflow.Runtime.Tracking.TrackingDataItem" /> objects each of which contains a single piece of extracted data and its associated annotations.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingRecord.ContextGuid">
      <summary>Context of the activity.</summary>
      <returns>A number that identifies the context of the activity.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingRecord.EventArgs">
      <summary>Always a null reference (Nothing in Visual Basic) for a <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingRecord" />.</summary>
      <returns>A null reference (Nothing) for a <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingRecord" />.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingRecord.EventDateTime">
      <summary>Gets or sets the date and time that the user event occurred.</summary>
      <returns>A <see cref="T:System.DateTime" /> value.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingRecord.EventOrder">
      <summary>Gets or sets a value that indicates the order of the user event that matched the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackPoint" /> in the workflow instance.</summary>
      <returns>A value that indicates the order of the user event in the workflow instance.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingRecord.ParentContextGuid">
      <summary>Context of the parent activity. </summary>
      <returns>A number that identifies the context of the parent activity.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingRecord.QualifiedName">
      <summary>Gets or sets the qualified name of the activity associated with this <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingRecord" />.</summary>
      <returns>The <see cref="P:System.Workflow.ComponentModel.Activity.QualifiedName" /> of the <see cref="T:System.Workflow.ComponentModel.Activity" /> from which the user data for this user event was emitted.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingRecord.UserData">
      <summary>Gets and sets the user data for this user event.</summary>
      <returns>The user data for which the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingRecord" /> was created.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackingRecord.UserDataKey">
      <summary>Gets or sets a key associated with the user data for this <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingRecord" />.</summary>
      <returns>A string key associated with the user data for this tracking record.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.UserTrackPoint">
      <summary>Defines a point, associated with a user event, to be tracked in the potential execution path of a root workflow instance. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.UserTrackPoint.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackPoint" /> class.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackPoint.Annotations">
      <summary>Gets the collection of annotations associated with the track point.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection" />. The default is an empty collection.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackPoint.ExcludedLocations">
      <summary>Gets the collection of locations that should be excluded from the track point by the runtime tracking infrastructure.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocationCollection" /> that specifies locations to be excluded from the track point. The default is an empty collection.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackPoint.Extracts">
      <summary>Gets a collection that specifies data to be extracted from the workflow instance and sent to the tracking service.</summary>
      <returns>An <see cref="T:System.Workflow.Runtime.Tracking.ExtractCollection" /> that specifies data to be extracted and sent to the tracking service. The default is an empty collection.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.UserTrackPoint.MatchingLocations">
      <summary>Gets the collection of locations that should be included in matching for the track point by the runtime tracking infrastructure.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.UserTrackingLocationCollection" /> that specifies the locations to be matched for the track point. The default is an empty collection.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.UserTrackPointCollection">
      <summary>Contains a collection of <see cref="T:System.Workflow.Runtime.Tracking.UserTrackPoint" /> objects.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.UserTrackPointCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackPointCollection" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.UserTrackPointCollection.#ctor(System.Collections.Generic.IEnumerable{System.Workflow.Runtime.Tracking.UserTrackPoint})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.UserTrackPointCollection" /> class by using a list of user track points. </summary>
      <param name="points">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> list of <see cref="T:System.Workflow.Runtime.Tracking.UserTrackPoint" /> objects.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="points" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.WorkflowDataTrackingExtract">
      <summary>Specifies a property or a field to be extracted from the root activity of the workflow and sent to the tracking service together with and an associated collection of annotations when a track point is matched.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.WorkflowDataTrackingExtract.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.WorkflowDataTrackingExtract" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.WorkflowDataTrackingExtract.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.WorkflowDataTrackingExtract" /> class by using the specified name of a member of the root activity.</summary>
      <param name="member">The dot delineated name of the field or property of the root activity that should be extracted and sent to the tracking service.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> is a null reference (Nothing in Visual Basic). </exception>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.WorkflowDataTrackingExtract.Annotations">
      <summary>Gets the collection of annotations associated with the extracted data.</summary>
      <returns>The <see cref="T:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection" /> associated with the activity property or field to be extracted. The default is an empty collection.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.WorkflowDataTrackingExtract.Member">
      <summary>Gets or sets the field or the property to be extracted from the root activity when a track point is matched.</summary>
      <returns>A dot delineated name that specifies a field or a property of the root activity. The default is a null reference (Nothing in Visual Basic). </returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.WorkflowTrackingLocation">
      <summary>Defines an interest in specific workflow status events that occur in a root workflow instance; used for matching by a <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackPoint" /> in a tracking profile. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.WorkflowTrackingLocation.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackingLocation" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.WorkflowTrackingLocation.#ctor(System.Collections.Generic.IList{System.Workflow.Runtime.Tracking.TrackingWorkflowEvent})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackingLocation" /> class that matches the specified tracking workflow events.</summary>
      <param name="events">IList of tracking workflow events to be matched.</param>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.WorkflowTrackingLocation.Events">
      <summary>Gets the list of workflow status events that will be matched for this location.</summary>
      <returns>A list of <see cref="T:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent" /> values that specifies the workflow status events for which the location will be matched. The default is an empty list.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.WorkflowTrackingRecord">
      <summary>Contains the data sent to the tracking service by the runtime tracking infrastructure when it matches a <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackPoint" />. It is also used in the return list of the <see cref="P:System.Workflow.Runtime.Tracking.SqlTrackingWorkflowInstance.WorkflowEvents" /> property.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.WorkflowTrackingRecord.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackingRecord" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.WorkflowTrackingRecord.#ctor(System.Workflow.Runtime.Tracking.TrackingWorkflowEvent,System.DateTime,System.Int32,System.EventArgs)">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackingRecord" /> class by using the specified <see cref="T:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent" /> value, <see cref="T:System.DateTime" />, number, and <see cref="T:System.EventArgs" />.</summary>
      <param name="trackingWorkflowEvent">One of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent" /> values.</param>
      <param name="eventDateTime">A <see cref="T:System.DateTime" /> that indicates the date and time of the workflow status event associated with the tracking record.</param>
      <param name="eventOrder">The relative order in which the workflow status event associated with this tracking record occurred in the workflow instance.</param>
      <param name="eventArgs">Either a <see cref="T:System.Workflow.Runtime.Tracking.TrackingWorkflowSuspendedEventArgs" />, a <see cref="T:System.Workflow.Runtime.Tracking.TrackingWorkflowTerminatedEventArgs" />, a <see cref="T:System.Workflow.Runtime.Tracking.TrackingWorkflowExceptionEventArgs" />, or a null reference (Nothing in Visual Basic).</param>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.WorkflowTrackingRecord.Annotations">
      <summary>Gets the collection of annotations associated with the <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackPoint" /> that was matched.</summary>
      <returns>An <see cref="T:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection" /> that contains the annotations that are associated with the <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackPoint" />.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.WorkflowTrackingRecord.EventArgs">
      <summary>Gets or sets an <see cref="T:System.EventArgs" /> that contains additional data associated with certain types of workflow status events.</summary>
      <returns>Either a <see cref="T:System.Workflow.Runtime.Tracking.TrackingWorkflowSuspendedEventArgs" />, a <see cref="T:System.Workflow.Runtime.Tracking.TrackingWorkflowTerminatedEventArgs" />, a <see cref="T:System.Workflow.Runtime.Tracking.TrackingWorkflowExceptionEventArgs" />, or a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.WorkflowTrackingRecord.EventDateTime">
      <summary>Gets or sets the date and time that the workflow tracking event occurred.</summary>
      <returns>A <see cref="T:System.DateTime" /> that indicates the date and time that the workflow status event occurred.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.WorkflowTrackingRecord.EventOrder">
      <summary>Gets or sets a value that indicates the order in the workflow instance of the workflow status event that matched the <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackPoint" />.</summary>
      <returns>A value that indicates the order of the workflow status event in the workflow instance. </returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.WorkflowTrackingRecord.TrackingWorkflowEvent">
      <summary>Gets or sets the type of workflow status event associated with the tracking record.</summary>
      <returns>One of the <see cref="T:System.Workflow.Runtime.Tracking.TrackingWorkflowEvent" /> values.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.WorkflowTrackPoint">
      <summary>Defines a point associated with a set of workflow status events that are tracked in the potential execution path of a root workflow instance. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.WorkflowTrackPoint.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackPoint" /> class.</summary>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.WorkflowTrackPoint.Annotations">
      <summary>Gets the collection of annotations associated with the track point.</summary>
      <returns>An <see cref="T:System.Workflow.Runtime.Tracking.TrackingAnnotationCollection" /> that contains the annotations associated with the track point. The default is an empty collection; it specifies that no annotations are to be associated with the track point.</returns>
    </member>
    <member name="P:System.Workflow.Runtime.Tracking.WorkflowTrackPoint.MatchingLocation">
      <summary>Gets or sets the <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackingLocation" /> that specifies the workflow status events that should be matched by the runtime tracking infrastructure for the track point.</summary>
      <returns>A <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackingLocation" />.</returns>
    </member>
    <member name="T:System.Workflow.Runtime.Tracking.WorkflowTrackPointCollection">
      <summary>Contains a collection of <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackPoint" /> objects.</summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.WorkflowTrackPointCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackPointCollection" /> class. </summary>
    </member>
    <member name="M:System.Workflow.Runtime.Tracking.WorkflowTrackPointCollection.#ctor(System.Collections.Generic.IEnumerable{System.Workflow.Runtime.Tracking.WorkflowTrackPoint})">
      <summary>Initializes a new instance of the <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackPointCollection" /> class by using a list of workflow track points. </summary>
      <param name="points">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> list of <see cref="T:System.Workflow.Runtime.Tracking.WorkflowTrackPoint" /> objects.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="points" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
  </members>
</doc>