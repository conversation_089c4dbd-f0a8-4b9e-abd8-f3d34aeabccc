using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class ListButton : <PERSON><PERSON>
    {
        public delegate void TextChangeHandler(object sender, EventArgs e);

        private readonly ContextMenuHelp _comboxbtn = new ContextMenuHelp();

        private ToolStripMenuItem[] _listItems;

        private bool _mBMouseHover;

        public string[] listItemEx;

        public ListButton()
        {
            FlatStyle = FlatStyle.Flat;
            SetStyle(ControlStyles.Selectable, false);
            SetStyle(ControlStyles.UserPaint, true);
            SetStyle(ControlStyles.ResizeRedraw, true);
            SetStyle(ControlStyles.AllPaintingInWmPaint, true);
            SetStyle(ControlStyles.OptimizedDoubleBuffer, true);
            SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            Click += dSkinButton1_Click;
        }

        public bool Islist
        {
            set => Setlist();
        }

        public bool IsBorder { get; set; } = true;


        public string[] ListItems
        {
            get => listItemEx;
            set => listItemEx = value;
        }

        public new event TextChangeHandler TextChanged;

        public void Change()
        {
            if (TextChanged != null) TextChanged(null, EventArgs.Empty);
        }

        private void dSkinButton1_Click(object sender, EventArgs e)
        {
            if (_comboxbtn != null) _comboxbtn.ShowEx(this, false, IsBorder);
        }

        public void Setlist()
        {
            if (listItemEx != null)
            {
                _comboxbtn.Renderer = new ComboxbtnRenderer(true);
                _listItems = new ToolStripMenuItem[listItemEx.Length];
                for (var i = 0; i < _listItems.Length; i++)
                    _listItems[i] = new ToolStripMenuItem
                    {
                        AutoSize = false,
                        Size = new Size(Size.Width, Size.Height - 5.DpiValue()),
                        Text = listItemEx[i]
                    };
                ToolStripItem[] array = _listItems;
                Setlistbox(array);
            }
        }

        public void Setlistbox(ToolStripItem[] listItems)
        {
            _comboxbtn.Items.AddRange(listItems);
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            _mBMouseHover = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            _mBMouseHover = false;
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            base.OnPaintBackground(e);
            var graphics = e.Graphics;
            var stringFormat = new StringFormat
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Center
            };
            using (var pen = new Pen(Color.Silver))
            {
                using (var brush = new SolidBrush(BackColor))
                {
                    using (var brush3 = new SolidBrush(Color.FromArgb(50, Color.Silver)))
                    {
                        using (var brush2 = new SolidBrush(ForeColor))
                        {
                            graphics.FillRectangle(brush, ClientRectangle);
                            graphics.DrawString(Text, Font, brush2, ClientRectangle, stringFormat);
                            if (_mBMouseHover) graphics.FillRectangle(brush3, ClientRectangle);
                            if (IsBorder) graphics.DrawRectangle(pen, ClientRectangle.SizeOffset(-1, -1));
                        }
                    }
                }
            }
        }
    }
}