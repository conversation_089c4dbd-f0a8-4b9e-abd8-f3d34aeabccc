﻿#region License Information (GPL v3)

/*
    OCRTools - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2020 OCRTools Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)

using MetroFramework;
using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace OCRTools
{
    public enum ThumbnailTitleLocation
    {
        Top,
        Bottom
    }

    public partial class TaskThumbnailView : UserControl, IMetroControl
    {
        public delegate void TaskViewMouseEventHandler(object sender, MouseEventArgs e);

        private Size _thumbnailSize = new Size(200, 150);

        private ThumbnailTitleLocation _titleLocation;

        private bool _titleVisible = true;

        public TaskThumbnailView()
        {
            Panels = new List<TaskThumbnailPanel>();
            SelectedPanels = new List<TaskThumbnailPanel>();

            InitializeComponent();
            UpdateTheme();
        }

        public List<TaskThumbnailPanel> Panels { get; }
        public List<TaskThumbnailPanel> SelectedPanels { get; }

        public TaskThumbnailPanel SelectedPanel
        {
            get
            {
                if (SelectedPanels.Count > 0) return SelectedPanels[SelectedPanels.Count - 1];

                return null;
            }
        }

        public bool TitleVisible
        {
            get => _titleVisible;
            set
            {
                if (_titleVisible != value)
                {
                    _titleVisible = value;

                    foreach (var panel in Panels) panel.TitleVisible = _titleVisible;
                }
            }
        }

        public ThumbnailTitleLocation TitleLocation
        {
            get => _titleLocation;
            set
            {
                if (_titleLocation != value)
                {
                    _titleLocation = value;

                    foreach (var panel in Panels) panel.TitleLocation = _titleLocation;
                }
            }
        }

        public Size ThumbnailSize
        {
            get => _thumbnailSize;
            set
            {
                if (_thumbnailSize != value)
                {
                    _thumbnailSize = value;

                    foreach (var panel in Panels) panel.ThumbnailSize = _thumbnailSize;

                    UpdateAllThumbnails(true);
                }
            }
        }

        public event TaskViewMouseEventHandler ContextMenuRequested;

        public event EventHandler SelectedPanelChanged;

        protected override Point ScrollToControl(Control activeControl)
        {
            return AutoScrollPosition;
        }

        public void UpdateTheme()
        {
            // 设置主题背景色
            if (!UseCustomBackColor)
                BackColor = MetroPaint.BackColor.Button.Normal(Theme);
            else
                BackColor = SystemColors.Window;

            foreach (var panel in Panels) panel.UpdateTheme();
        }

        private TaskThumbnailPanel CreatePanel(HistoryTask task)
        {
            var panel = new TaskThumbnailPanel(task)
            {
                ThumbnailSize = ThumbnailSize,
                TitleVisible = TitleVisible,
                TitleLocation = TitleLocation
            };
            panel.MouseEnter += Panel_MouseEnter;
            panel.MouseDown += (sender, e) => Panel_MouseDown(sender, e, panel);
            panel.MouseUp += Panel_MouseUp;
            return panel;
        }

        public TaskThumbnailPanel AddPanel(HistoryTask task)
        {
            var panel = CreatePanel(task);
            Panels.Add(panel);
            flpMain.Controls.Add(panel);
            flpMain.Controls.SetChildIndex(panel, 0);
            return panel;
        }

        public void RemovePanel(HistoryTask task)
        {
            var panel = FindPanel(task);

            if (panel != null)
            {
                Panels.Remove(panel);
                SelectedPanels.Remove(panel);
                flpMain.Controls.Remove(panel);
                panel.Dispose();
            }
        }

        public void RemoveAllPanel()
        {
            Panels.ForEach(p => { p.Dispose(); });
            Panels.Clear();
            SelectedPanels.Clear();
            flpMain.Controls.Clear();
        }

        public TaskThumbnailPanel FindPanel(HistoryTask task)
        {
            return Panels.FirstOrDefault(x => x.Task == task);
        }

        public void UpdateAllThumbnails(bool forceUpdate = false)
        {
            foreach (var panel in Panels)
                if (forceUpdate || !panel.ThumbnailExists)
                    panel.UpdateThumbnail();
        }

        public void UnselectAllPanels(TaskThumbnailPanel ignorePanel = null)
        {
            SelectedPanels.Clear();

            foreach (var panel in Panels)
                if (panel != ignorePanel)
                    panel.Selected = false;

            OnSelectedPanelChanged();
        }

        protected void OnContextMenuRequested(object sender, MouseEventArgs e)
        {
            ContextMenuRequested?.Invoke(sender, e);
        }

        protected void OnSelectedPanelChanged()
        {
            SelectedPanelChanged?.Invoke(this, EventArgs.Empty);
        }

        private void Panel_MouseEnter(object sender, EventArgs e)
        {
            // Workaround to handle mouse wheel scrolling in Windows 7
            if (NativeMethods.GetForegroundWindow() == ParentForm.Handle && !flpMain.Focused) flpMain.Focus();
        }

        private void Panel_MouseDown(object sender, MouseEventArgs e)
        {
            Panel_MouseDown(sender, e, null);
        }

        private void Panel_MouseDown(object sender, MouseEventArgs e, TaskThumbnailPanel panel)
        {
            if (panel == null)
            {
                UnselectAllPanels();
            }
            else
            {
                if (ModifierKeys == Keys.Control)
                {
                    if (panel.Selected)
                    {
                        panel.Selected = false;
                        SelectedPanels.Remove(panel);
                    }
                    else
                    {
                        panel.Selected = true;
                        SelectedPanels.Add(panel);
                    }
                }
                else if (ModifierKeys == Keys.Shift)
                {
                    if (SelectedPanels.Count > 0)
                    {
                        var firstPanel = SelectedPanels[0];

                        UnselectAllPanels();

                        foreach (var p in Panels.Range(firstPanel, panel))
                        {
                            p.Selected = true;
                            SelectedPanels.Add(p);
                        }
                    }
                    else
                    {
                        panel.Selected = true;
                        SelectedPanels.Add(panel);
                    }
                }
                else
                {
                    if (!panel.Selected || e.Button == MouseButtons.Left)
                    {
                        UnselectAllPanels(panel);
                        panel.Selected = true;
                        SelectedPanels.Add(panel);
                    }
                }
            }

            OnSelectedPanelChanged();
        }

        private void Panel_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right) OnContextMenuRequested(sender, e);
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            OnKeyDown(new KeyEventArgs(keyData));

            return base.ProcessCmdKey(ref msg, keyData);
        }

        #region 主题

        private MetroColorStyle _metroStyle;

        private MetroThemeStyle _metroTheme;

        [Category("Metro Appearance")]
        [DefaultValue(MetroColorStyle.Blue)]
        public MetroColorStyle Style
        {
            get
            {
                if (DesignMode || _metroStyle != 0) return _metroStyle;
                if (StyleManager != null) return StyleManager.Style;
                if (StyleManager == null) return MetroColorStyle.Blue;
                return _metroStyle;
            }
            set => _metroStyle = value;
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (DesignMode || _metroTheme != 0) return _metroTheme;
                if (StyleManager != null) return StyleManager.Theme;
                if (StyleManager == null) return MetroThemeStyle.Light;
                return _metroTheme;
            }
            set => _metroTheme = value;
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public MetroStyleManager StyleManager { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor { get; set; }

        #endregion
    }
}