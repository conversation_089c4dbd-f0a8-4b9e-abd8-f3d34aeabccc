﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Build</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Build.Construction.ProjectChooseElement">
      <summary>Represents the Choose Element (MSBuild) in an MSBuild project. </summary>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectChooseElement.Condition">
      <summary>Gets a nonexistent condition, which is implicitly true.</summary>
      <returns>Returns a null.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectChooseElement.OtherwiseElement">
      <summary>Gets any Otherwise Element (MSBuild) child. </summary>
      <returns>Returns any Otherwise child. Returns null if no Otherwise child exists.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectChooseElement.WhenElements">
      <summary>Gets all the When Element (MSBuild) children. </summary>
      <returns>Returns all the When children. There is always at least one When child.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectElement">
      <summary>Abstract base class for MSBuild construction object model elements. All project elements use this as the base class.</summary>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectElement.AllParents">
      <summary>All parent elements of this element, going up to the ProjectRootElement.</summary>
      <returns>Returns an enumerator over all parent elements, none if the project element is a ProjectRootElement or has not been attached to a parent yet.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectElement.Condition">
      <summary>Gets or sets the Condition attribute of this project element.</summary>
      <returns>Returns the Condition attribute value. Returns an empty string if the attribute is not present. </returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectElement.ContainingProject">
      <summary>Gets the project root container that contains this project. </summary>
      <returns>Returns the project root container that contains this project. </returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectElement.Label">
      <summary>Gets or sets the Label value. </summary>
      <returns>Returns the label. Returns an empty string if no label is present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectElement.NextSibling">
      <summary>Gets the next sibling of this project element. </summary>
      <returns>Returns the next sibling of this project element. Returns null if no next sibling exists.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectElement.Parent">
      <summary>Gets the project element container that contains this project. </summary>
      <returns>Returns the project element container that contains this project. </returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectElement.PreviousSibling">
      <summary>Gets the previous sibling of this project element. </summary>
      <returns>Returns the previous sibling of this project element. Returns null if no previous sibling exists.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectElementContainer">
      <summary>Provides an abstract container class for project elements. </summary>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectElementContainer.AllChildren">
      <summary>Gets a recursive depth-first enumerator over all child elements.</summary>
      <returns>Returns a recursive depth-first enumerator over all child elements.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectElementContainer.AppendChild(Microsoft.Build.Construction.ProjectElement)">
      <summary>Appends the <paramref name="child" /> element as the last child of this project container. </summary>
      <param name="child">The project element to be appended.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectElementContainer.Children">
      <summary>Gets all child elements.</summary>
      <returns>Returns all child elements.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectElementContainer.ChildrenReversed">
      <summary>Gets all child elements, starting from the last child.</summary>
      <returns>Returns all child elements, starting from the last child.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectElementContainer.Count">
      <summary>Gets the number of child elements.</summary>
      <returns>Returns the number of child elements.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectElementContainer.FirstChild">
      <summary>Gets the first child element. </summary>
      <returns>Returns the first child element. Returns null if no child element exists.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectElementContainer.InsertAfterChild(Microsoft.Build.Construction.ProjectElement,Microsoft.Build.Construction.ProjectElement)">
      <summary>Inserts the <paramref name="child" /> element after the <paramref name="reference" /> element. </summary>
      <param name="child">The project element to be inserted.</param>
      <param name="reference">The project element to be inserted after.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectElementContainer.InsertBeforeChild(Microsoft.Build.Construction.ProjectElement,Microsoft.Build.Construction.ProjectElement)">
      <summary>Inserts the <paramref name="child" /> element before the <paramref name="reference" /> element.</summary>
      <param name="child">The project element to be inserted.</param>
      <param name="reference">The project element to be inserted before.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectElementContainer.LastChild">
      <summary>Gets the last child element.</summary>
      <returns>Returns the last child element. Returns null if no child element exists.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectElementContainer.PrependChild(Microsoft.Build.Construction.ProjectElement)">
      <summary>Prepends the <paramref name="child" /> element as the first child of this project container. </summary>
      <param name="child">The project element to be prepended.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectElementContainer.RemoveAllChildren">
      <summary>Removes all the children, if any, from this project container.</summary>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectElementContainer.RemoveChild(Microsoft.Build.Construction.ProjectElement)">
      <summary>Removes a <paramref name="child" /> element from this project container.</summary>
      <param name="child">The project element to be removed.</param>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectExtensionsElement">
      <summary>Represents the ProjectExtensions Element (MSBuild) in an MSBuild project. Project extensions can contain arbitrary XML content. </summary>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectExtensionsElement.Condition">
      <summary>Gets a nonexistent condition, which is implicitly true.</summary>
      <returns>Returns a null.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectExtensionsElement.Content">
      <summary>Gets or sets the arbitrary XML content of this project extension.</summary>
      <returns>Returns the arbitrary XML content of this project extension.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectExtensionsElement.Item(System.String)">
      <summary>Gets or sets the content of the first sub-element with the given <paramref name="name" /> parameter.</summary>
      <returns>Returns the contents of the element.</returns>
      <param name="name">The name of the sub-element.</param>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectImportElement">
      <summary>Represents an Import Element (MSBuild) in an MSBuild project.</summary>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectImportElement.Project">
      <summary>Gets or sets the Project attribute.</summary>
      <returns>Returns the value of the Project attribute. </returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectImportGroupElement">
      <summary>Represents the ImportGroup element in an MSBuild project.</summary>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectImportGroupElement.AddImport(System.String)">
      <summary>Adds a new import after the last import in this import group.</summary>
      <returns>Returns the new import.</returns>
      <param name="project">The project to import.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectImportGroupElement.Imports">
      <summary>Gets all properties in this project import.</summary>
      <returns>Returns all properties in this project import.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectItemDefinitionElement">
      <summary>Represents an ItemDefinition element in an MSBuild project.</summary>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectItemDefinitionElement.AddMetadata(System.String,System.String)">
      <summary>Adds metadata to this item definition. Appends the metadata to any existing metadata.</summary>
      <returns>Returns the modified metadata.</returns>
      <param name="name">Name of the metadata.</param>
      <param name="unevaluatedValue">Metadata to be added.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectItemDefinitionElement.ItemType">
      <summary>Gets the item definition element name. </summary>
      <returns>Returns the item definition element name. </returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectItemDefinitionElement.Metadata">
      <summary>Gets all child metadata definitions.</summary>
      <returns>Returns all child metadata definitions.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectItemDefinitionGroupElement">
      <summary>Represents an ItemDefinitionGroup Element (MSBuild) in an MSBuild project.</summary>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectItemDefinitionGroupElement.AddItemDefinition(System.String)">
      <summary>Adds a new item definition after the last child in this item definition group.</summary>
      <returns>Returns the new item definition.</returns>
      <param name="itemType">Element name of item to be added.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectItemDefinitionGroupElement.ItemDefinitions">
      <summary>Gets all child item definitions.</summary>
      <returns>Returns all child item definitions.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectItemElement">
      <summary>Represents an Item Element (MSBuild) in an MSBuild project.</summary>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectItemElement.AddMetadata(System.String,System.String)">
      <summary>Adds metadata to this item. Appends the metadata to any existing metadata.</summary>
      <returns>Returns the modified metadata.</returns>
      <param name="name">Name of metadata.</param>
      <param name="unevaluatedValue">Metadata to be added.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectItemElement.Exclude">
      <summary>Gets or sets the Exclude attribute value. </summary>
      <returns>Returns the Exclude attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectItemElement.HasMetadata">
      <summary>Determines if this item has any child metadata elements.</summary>
      <returns>Returns true if this item has child metadata elements; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectItemElement.Include">
      <summary>Gets or sets the Include attribute value. </summary>
      <returns>Returns the Include attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectItemElement.ItemType">
      <summary>Gets the item element name.</summary>
      <returns>Returns the item element name. </returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectItemElement.Metadata">
      <summary>Gets all child metadata.</summary>
      <returns>Returns all child metadata.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectItemElement.Remove">
      <summary>Gets or sets the Remove attribute value. </summary>
      <returns>Returns the Remove attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectItemGroupElement">
      <summary>Represents an ItemGroup Element (MSBuild) in an MSBuild project.</summary>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectItemGroupElement.AddItem(System.String,System.String)">
      <summary>Adds a new item to the item group. Items are ordered first by item element name, and then by the Include attribute.</summary>
      <returns>Returns the new item element.</returns>
      <param name="itemType">Item element name of the item to be added.</param>
      <param name="include">Include attribute of the item to be added.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectItemGroupElement.AddItem(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>Adds a new item with metadata to the item group. Items are ordered first by item element name, and then by the Include attribute.</summary>
      <returns>Returns the new item element.</returns>
      <param name="itemType">Item element name of the item to be added.</param>
      <param name="include">Include attribute of the item to be added.</param>
      <param name="metadata">Metadata to be added. </param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectItemGroupElement.Items">
      <summary>Gets all child items. </summary>
      <returns>Returns all child items.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectMetadataElement">
      <summary>Represents a Metadata element in an MSBuild project.</summary>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectMetadataElement.Name">
      <summary>Gets the metadata name. </summary>
      <returns>Returns the metadata name. </returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectMetadataElement.Value">
      <summary>Gets or sets the unevaluated metadata value.</summary>
      <returns>Returns the unevaluated metadata value. Returns an empty string if the value is uninitialized.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectOnErrorElement">
      <summary>Represents an OnError Element (MSBuild) in an MSBuild project.</summary>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectOnErrorElement.ExecuteTargetsAttribute">
      <summary>Gets or sets the value of the ExecuteTargets attribute.</summary>
      <returns>Returns the value of the ExecuteTargets attribute.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectOtherwiseElement">
      <summary>Represents an Otherwise Element (MSBuild) in an MSBuild project.</summary>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectOtherwiseElement.ChooseElements">
      <summary>Gets all child Choose elements.</summary>
      <returns>Returns all child Choose elements.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectOtherwiseElement.Condition">
      <summary>Gets a nonexistent condition, which is implicitly true.</summary>
      <returns>Returns a null.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectOtherwiseElement.ItemGroups">
      <summary>Gets all child item groups.</summary>
      <returns>Returns all child item groups.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectOtherwiseElement.PropertyGroups">
      <summary>Gets all child property groups.</summary>
      <returns>Returns all child property groups.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectOutputElement">
      <summary>Represents an Output Element (MSBuild) in an MSBuild project.</summary>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectOutputElement.IsOutputItem">
      <summary>Determines whether this output element represents an output item, as opposed to an output property.</summary>
      <returns>Returns true if this output element represents an output item; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectOutputElement.IsOutputProperty">
      <summary>Determines whether this output element represents an output property, as opposed to an output item.</summary>
      <returns>Returns true if this output element represents an output property; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectOutputElement.ItemType">
      <summary>Gets or sets the ItemType attribute value. </summary>
      <returns>Gets or sets the ItemType attribute value  Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectOutputElement.PropertyName">
      <summary>Gets or sets the PropertyName attribute value. </summary>
      <returns>Gets or sets the PropertyName attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectOutputElement.TaskParameter">
      <summary>Gets or sets the TaskParameter attributevalue. </summary>
      <returns>Gets or sets the TaskParameter attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectPropertyElement">
      <summary>Represents a Property element in an MSBuild project.</summary>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectPropertyElement.Name">
      <summary>Gets the property name. </summary>
      <returns>Returns the property name.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectPropertyElement.Value">
      <summary>Gets or sets the unevaluated property value. </summary>
      <returns>Returns the unevaluated property value. Returns an empty string if the value is uninitialized.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectPropertyGroupElement">
      <summary>Represents a PropertyGroup element in an MSBuild project.</summary>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectPropertyGroupElement.AddProperty(System.String,System.String)">
      <summary>Appends a new property to the property group. </summary>
      <returns>Returns the new property. </returns>
      <param name="name">The property name.</param>
      <param name="unevaluatedValue">The property value.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectPropertyGroupElement.Properties">
      <summary>Gets all child properties.</summary>
      <returns>Returns all child properties.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectPropertyGroupElement.PropertiesReversed">
      <summary>Gets all child properties, starting with the last child.</summary>
      <returns>Returns all child properties, starting with the last child.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectPropertyGroupElement.SetProperty(System.String,System.String)">
      <summary>Updates the value of the given property in the property group. </summary>
      <returns>Returns the updated property. </returns>
      <param name="name">The name of the property to be updated.</param>
      <param name="unevaluatedValue">The new property value.</param>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectRootElement">
      <summary>Represents an MSBuild project, a targets file, or any other file that conforms to MSBuild project file schema. This class and its related classes allow a complete MSBuild project or targets file to be read and written. </summary>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.AddImport(System.String)">
      <summary>Adds a project import to this project.</summary>
      <returns>Returns the added project import.</returns>
      <param name="project">The project to be imported.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.AddImportGroup">
      <summary>Creates an import group at the end of this project.</summary>
      <returns>Returns the import group created at the end of this project.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.AddItem(System.String,System.String)">
      <summary>Adds an item to this project.</summary>
      <returns>Returns the added item.</returns>
      <param name="itemType">The item type of the item to be added.</param>
      <param name="include">The Include value of the item to be added.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.AddItem(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>Adds an item with metadata to this project.</summary>
      <returns>Returns the added item.</returns>
      <param name="itemType">The item type of the item to be added.</param>
      <param name="include">The Include value of the item to be added.</param>
      <param name="metadata">The metadata to be added.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.AddItemDefinition(System.String)">
      <summary>Adds an item definition to this project.</summary>
      <returns>Returns the added item definition.</returns>
      <param name="itemType">The item type of the item definition to be added.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.AddItemDefinitionGroup">
      <summary>Adds an item definition group to this project.</summary>
      <returns>Returns the added item definition group.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.AddItemGroup">
      <summary>Creates and adds an item group to this project.</summary>
      <returns>Returns the added item group.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.AddProperty(System.String,System.String)">
      <summary>Updates or adds a property to this project.</summary>
      <returns>Returns the updated or added property.</returns>
      <param name="name">The name of the property to be updated or added.</param>
      <param name="value">The value of the property to be updated or added.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.AddPropertyGroup">
      <summary>Adds a new property group to this project.</summary>
      <returns>Returns the added property group.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.AddTarget(System.String)">
      <summary>Adds a target to the project.</summary>
      <returns>Returns the added target.</returns>
      <param name="name">The name of the target to be added.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.AddUsingTask(System.String,System.String,System.String)">
      <summary>Adds a UsingTask Element (MSBuild) to the project.</summary>
      <returns>Returns the added UsingTask element.</returns>
      <param name="name">The task name.</param>
      <param name="assemblyFile">The file path to the assembly.</param>
      <param name="assemblyName">The name of the assembly to load.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.ChooseElements">
      <summary>Gets all child Choose Element (MSBuild) in this project.</summary>
      <returns>Returns all child Choose elements.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.Condition">
      <summary>Gets a nonexistent condition, which is implicitly true.</summary>
      <returns>Returns a null.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.Create">
      <summary>Creates and initializes an in-memory, empty ProjectRootElement instance and adds it to the global project collection. </summary>
      <returns>Returns the new project root.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.Create(Microsoft.Build.Evaluation.ProjectCollection)">
      <summary>Creates and initializes an in-memory, empty ProjectRootElement instance and adds it to the specified project collection.</summary>
      <returns>Returns the new project root.</returns>
      <param name="projectCollection">The project collection to be added to.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.Create(System.String)">
      <summary>Creates and initializes an in-memory, empty ProjectRootElement instance and adds it to the global project collection. The new project root is initialized from data found at the specified file path.</summary>
      <returns>Returns the new project root.</returns>
      <param name="path">The file path to the data used for initialization.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.Create(System.String,Microsoft.Build.Evaluation.ProjectCollection)">
      <summary>Creates and initializes an in-memory, empty ProjectRootElement instance and adds it to the specified project collection. The new project root is initialized from data found at the specified file path.</summary>
      <returns>Returns the new project root.</returns>
      <param name="path">The file path to the data used for initialization.</param>
      <param name="projectCollection">The project collection to be added to.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.Create(System.Xml.XmlReader)">
      <summary>Creates and initializes an in-memory, empty ProjectRootElement instance and adds it to the global project collection. The new project root is initialized from data read from the specified XmlReader. </summary>
      <returns>Returns the new project root.</returns>
      <param name="xmlReader">The XML reader used for initialization.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.Create(System.Xml.XmlReader,Microsoft.Build.Evaluation.ProjectCollection)">
      <summary>Creates and initializes an in-memory, empty ProjectRootElement instance and adds it to the specified project collection. The new project root is initialized from data read from the specified XmlReader. </summary>
      <returns>Returns the new project root.</returns>
      <param name="xmlReader">The XML reader used for initialization.</param>
      <param name="projectCollection">The project collection to be added to.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateChooseElement">
      <summary>Creates a Choose Element (MSBuild). </summary>
      <returns>A <see cref="ChoseElement" /> class.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateImportElement(System.String)">
      <summary>Creates an Import Element (MSBuild). </summary>
      <returns>Returns the created Import element.</returns>
      <param name="project">The project to be imported.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateImportGroupElement">
      <summary>Creates an import group. </summary>
      <returns>Returns the import group.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateItemDefinitionElement(System.String)">
      <summary>Creates an item definition. </summary>
      <returns>Returns the item definition.</returns>
      <param name="itemType">The item type of the item definition.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateItemDefinitionGroupElement">
      <summary>Creates an item definition group. </summary>
      <returns>Returns the item definition group.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateItemElement(System.String)">
      <summary>Creates an item. </summary>
      <returns>Returns the item.</returns>
      <param name="itemType">The item type of the item.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateItemElement(System.String,System.String)">
      <summary>Creates an item with the specifed Include value.</summary>
      <returns>Returns the item.</returns>
      <param name="itemType">The item type of the item.</param>
      <param name="include">The Include value of the item.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateItemGroupElement">
      <summary>Creates an item group. </summary>
      <returns>Returns the item group.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateMetadataElement(System.String)">
      <summary>Creates a metadata node with the specified name. </summary>
      <returns>Returns the metadata node.</returns>
      <param name="name">The name of the metadata.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateMetadataElement(System.String,System.String)">
      <summary>Creates a metadata node with the specified name and value. </summary>
      <returns>Returns the metadata element.</returns>
      <param name="name">The name of the metadata.</param>
      <param name="unevaluatedValue">The value of the metadata.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateOnErrorElement(System.String)">
      <summary>Creates an OnError Element (MSBuild). </summary>
      <returns>Returns the OnError element.</returns>
      <param name="executeTargets">The targets to execute if a task fails.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateOtherwiseElement">
      <summary>Creates an Otherwise Element (MSBuild). Caller must add it to the location of choice in the project.</summary>
      <returns>Returns the Otherwise element.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateOutputElement(System.String,System.String,System.String)">
      <summary>Creates an Output Element (MSBuild).</summary>
      <returns>Returns the Output element.</returns>
      <param name="taskParameter">The name of the task's output parameter.</param>
      <param name="itemType">The item that receives the task's output parameter value.</param>
      <param name="propertyName">The property that receives the task's output parameter value.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateProjectExtensionsElement">
      <summary>Creates a ProjectExtensions Element (MSBuild). </summary>
      <returns>Returns the ProjectExtensions element.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreatePropertyElement(System.String)">
      <summary>Creates a property. </summary>
      <returns>Returns the property.</returns>
      <param name="name">The name of the property.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreatePropertyGroupElement">
      <summary>Creates a property group. </summary>
      <returns>Returns the property group.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateTargetElement(System.String)">
      <summary>Creates a target. </summary>
      <returns>Returns the target.</returns>
      <param name="name">The name of the target.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateTaskElement(System.String)">
      <summary>Creates a task. </summary>
      <returns>Returns the task.</returns>
      <param name="name">The name of the task.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateUsingTaskBodyElement(System.String,System.String)">
      <summary>Creates a task to be referenced by a UsingTask Element (MSBuild). </summary>
      <returns>Returns the task.</returns>
      <param name="evaluate">The string to evaluate.</param>
      <param name="body">The body to add.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateUsingTaskElement(System.String,System.String,System.String)">
      <summary>Adds a UsingTask Element (MSBuild) to the project.</summary>
      <returns>Returns the UsingTask element.</returns>
      <param name="taskName">The task name.</param>
      <param name="assemblyFile">The file path to the assembly.</param>
      <param name="assemblyName">The name of the assembly to load.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateUsingTaskParameterElement(System.String,System.String,System.String,System.String)">
      <summary>Creates a parameter for use in a UsingTask Element (MSBuild) parameter group.</summary>
      <returns>Returns the parameter.</returns>
      <param name="name">The name of the UsingTask element.</param>
      <param name="output">Stores outputs from the task in the project file.</param>
      <param name="required">A user-defined task parameter that contains the parameter value as its value.</param>
      <param name="parameterType">The type of the parameter.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateUsingTaskParameterGroupElement">
      <summary>Creates a parameter group for a UsingTask Element (MSBuild). </summary>
      <returns>Returns the parameter group.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.CreateWhenElement(System.String)">
      <summary>Creates a When Element (MSBuild) with a specified Condition attribute. </summary>
      <returns>Returns the When element.</returns>
      <param name="condition">The value of the Condition attribute.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.DefaultTargets">
      <summary>Gets or sets the value of the DefaultTargets attribute. </summary>
      <returns>Returns the value of the DefaultTargets attribute. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.DirectoryPath">
      <summary>Gets the directory path to the project file. </summary>
      <returns>Returns the directory path, which is never null. If the project is not loaded from disk, returns the current-directory at the time the project was loaded.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.Encoding">
      <summary>Specifies the character encoding that the project file is to be saved in.</summary>
      <returns>Returns the character encoding that the project file is to be saved in.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.FullPath">
      <summary>Gets the full path to the project file. </summary>
      <returns>Returns the full path to the project file. If the project is not loaded from disk, returns null.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.HasUnsavedChanges">
      <summary>Determines whether the project has been modified since it was last loaded or saved.</summary>
      <returns>Returns true if the project has been modified; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.ImportGroups">
      <summary>Gets all the child import groups in this project.</summary>
      <returns>Returns all the child import groups in this project.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.ImportGroupsReversed">
      <summary>Gets all the child import groups in this project, starting with the last group.</summary>
      <returns>Returns all the child import groups in this project, starting with the last group.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.Imports">
      <summary>Gets all the child import elements in this project.</summary>
      <returns>Gets all the child import elements in this project.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.InitialTargets">
      <summary>Gets or sets the value of the InitialTargets attribute. </summary>
      <returns>Returns the InitialTargets attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.ItemDefinitionGroups">
      <summary>Gets all the child item definition groups in this project.</summary>
      <returns>Returns all the child item definition groups in this project.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.ItemDefinitionGroupsReversed">
      <summary>Gets all the child item definition groups in this project, starting with the last group.</summary>
      <returns>Returns all the child item definition groups in this project, starting with the last group.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.ItemDefinitions">
      <summary>Gets all child item definitions in all item definition groups anywhere in this project.</summary>
      <returns>Gets all child item definitions in all item definition groups anywhere in this project.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.ItemGroups">
      <summary>Gets all the child item groups in this project. </summary>
      <returns>Returns all the child item groups in this project. </returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.ItemGroupsReversed">
      <summary>Gets all the child item groups present in this project, starting with the last group. </summary>
      <returns>Returns all the child item groups present in this project, starting with the last group. </returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.Items">
      <summary>Gets all child items in this project.</summary>
      <returns>Returns all child items in this project, even if they are contained by Choose elements. </returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.LastWriteTimeWhenRead">
      <summary>Gets the last-write-time of the project file.</summary>
      <returns>Gets the last-write-time of the project file.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.Open(System.String)">
      <summary>Initializes a project root in the global project collection by loading data from the specified file path. </summary>
      <returns>Returns the initialized project root.</returns>
      <param name="path">The file path to the data.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.Open(System.String,Microsoft.Build.Evaluation.ProjectCollection)">
      <summary>Initializes a project root in the specified project collection by loading data from the specified file path. </summary>
      <returns>Returns the initialized project root.</returns>
      <param name="path">The file path to the data.</param>
      <param name="projectCollection">The project collection containing the project to be initialized.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.Properties">
      <summary>Gets all the child properties in this project.</summary>
      <returns>Returns all child properties in this project, even if they are contained by Choose elements. </returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.PropertyGroups">
      <summary>Gets all the child property groups in this project. </summary>
      <returns>Returns all the child property groups in this project. </returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.PropertyGroupsReversed">
      <summary>Gets all the child property groups present in this project, starting with the last group. </summary>
      <returns>Returns all the child property groups present in this project, starting with the last group. </returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.RawXml">
      <summary>Gets the XML content that represents this project. </summary>
      <returns>Returns the XML content that represents this project as a string.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.Save">
      <summary>Saves the project, if modified, to the file system. </summary>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.Save(System.IO.TextWriter)">
      <summary>Saves the project to the specified text writer, whether modified or not. </summary>
      <param name="writer">The text writer to write the project to.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.Save(System.String)">
      <summary>Saves the project, if modified or if the file path to storage has changed.</summary>
      <param name="path">The file path to the project in storage.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.Save(System.String,System.Text.Encoding)">
      <summary>Saves the project, if modified or if the file path to storage has changed. Uses the specified character encoding..</summary>
      <param name="path">The file path to the project in storage.</param>
      <param name="encoding">The character encoding used to save the project.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.Save(System.Text.Encoding)">
      <summary>Saves the project, if modified, using the specified character encoding.</summary>
      <param name="saveEncoding">The character encoding used to save the project.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.Targets">
      <summary>Gets all the child targets in this project.</summary>
      <returns>Returns all the child targets in this project.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.TimeLastChanged">
      <summary>Gets the time that this project was last modified. </summary>
      <returns>Returns the time that this project was last modified. Returns null if the project hasn't been modified since being created or loaded.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.ToolsVersion">
      <summary>Gets or sets the value of the ToolsVersion attribute. </summary>
      <returns>Returns the ToolsVersion attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.TryOpen(System.String)">
      <summary>Gets the project root in the global project collection that was loaded into memory from the specified file path. or null if it is not currently in memory. </summary>
      <returns>Returns the project root. Returns null if no project was loaded into memory from the specified file path.</returns>
      <param name="path">The path of the ProjectRootElement, cannot be null.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectRootElement.TryOpen(System.String,Microsoft.Build.Evaluation.ProjectCollection)">
      <summary>Gets the project root in the specified project collection that was loaded into memory from the specified file path. or null if it is not currently in memory. </summary>
      <returns>Returns the project root. Returns null if no project was loaded into memory from the specified file path.</returns>
      <param name="path">The path of the ProjectRootElement, cannot be null.</param>
      <param name="projectCollection">The project collection to search for the project root.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.UsingTasks">
      <summary>Gets all child UsingTask Element (MSBuild) in this project.</summary>
      <returns>Returns all child UsingTask elements in this project.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectRootElement.Version">
      <summary>Gets the version number of this object. </summary>
      <returns>Returns the version number of this object. </returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectTargetElement">
      <summary>Represents a Target Element (MSBuild) in an MSBuild project.</summary>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectTargetElement.AddItemGroup">
      <summary>Adds an item group after the last child.</summary>
      <returns>Returns the added item group.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectTargetElement.AddPropertyGroup">
      <summary>Adds a property group after the last child.</summary>
      <returns>Returns the added property group.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectTargetElement.AddTask(System.String)">
      <summary>Adds a task to this target after any existing task.</summary>
      <returns>Returns the added task.</returns>
      <param name="taskName">The name of the task to add.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTargetElement.AfterTargets">
      <summary>Gets or sets the AfterTargets attribute value. </summary>
      <returns>Returns the AfterTargets attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTargetElement.BeforeTargets">
      <summary>Gets or sets the BeforeTargets attribute value. </summary>
      <returns>Returns the BeforeTargets attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTargetElement.DependsOnTargets">
      <summary>Gets or sets the DependsOnTargets attribute value.  Returns empty string if it is not present. Removes the attribute if the value to set is empty.</summary>
      <returns>Returns the DependsOnTargets attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTargetElement.Inputs">
      <summary>Gets or sets the Inputs attribute value.  Returns empty string if it is not present. Removes the attribute if the value to set is empty.</summary>
      <returns>Returns the Inputs attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTargetElement.ItemGroups">
      <summary>Gets all child item groups</summary>
      <returns>Returns all child item groups.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTargetElement.KeepDuplicateOutputs">
      <summary>Gets or sets the TrimDuplicateOutputs attribute value. </summary>
      <returns>Returns the TrimDuplicateOutputs attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTargetElement.Name">
      <summary>Gets and sets the name of the this target.</summary>
      <returns>Returns the name of the this target.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTargetElement.OnErrors">
      <summary>Get all child OnError Element (MSBuild).</summary>
      <returns>Returns all child OnError elements.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTargetElement.Outputs">
      <summary>Gets or sets the Outputs attribute value.  Returns empty string if it is not present. Removes the attribute if the value to set is empty.</summary>
      <returns>Returns the Outputs attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTargetElement.PropertyGroups">
      <summary>Gets all child property groups.</summary>
      <returns>Returns all child property groups.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTargetElement.Returns">
      <summary>Gets or sets the Returns attribute value. </summary>
      <returns>Returns the returns attribute value; null if the attribute is not present</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTargetElement.Tasks">
      <summary>Gets all child tasks.</summary>
      <returns>Returns all child tasks.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectTaskElement">
      <summary>Represents a Task Element (MSBuild) in an MSBuild project.</summary>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectTaskElement.AddOutputItem(System.String,System.String)">
      <summary>Adds an Output item after the last child.</summary>
      <returns>Returns the added Output item.</returns>
      <param name="taskParameter">The name of the task which outputs to the item.</param>
      <param name="itemType">The item type of the new item whose value is set to the output of the task.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectTaskElement.AddOutputItem(System.String,System.String,System.String)">
      <summary>Adds a conditioned Output item to this task after the last child.</summary>
      <returns>Returns the added conditioned Output item.</returns>
      <param name="taskParameter">The name of the parameter.</param>
      <param name="itemType">The item type of the item.</param>
      <param name="condition">The condition of the parameter.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectTaskElement.AddOutputProperty(System.String,System.String)">
      <summary>Adds an Output property to this task after the last child.</summary>
      <returns>Returns the added Output property.</returns>
      <param name="taskParameter">The name of the parameter.</param>
      <param name="propertyName">The name of the property.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectTaskElement.AddOutputProperty(System.String,System.String,System.String)">
      <summary>Adds a conditioned Output property to this task after the last child.</summary>
      <returns>Returns the added conditioned Output property.</returns>
      <param name="taskParameter">The name of the parameter.</param>
      <param name="propertyName">The name of the property.</param>
      <param name="condition">The condition of the property.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTaskElement.ContinueOnError">
      <summary>Gets or sets the ContinueOnError attribute value. </summary>
      <returns>Returns the ContinueOnError attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectTaskElement.GetParameter(System.String)">
      <summary>Gets the value of the parameter with the specified name.</summary>
      <returns>Returns the value of the parameter with the specified name. If no parameter by that name exists in the task, returns an empty string.</returns>
      <param name="name">The name of the parameter.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTaskElement.Name">
      <summary>Gets the name of this task.</summary>
      <returns>Returns the name of this task.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTaskElement.Outputs">
      <summary>Gets all Output Element (MSBuild) children.</summary>
      <returns>Returns all Output element children.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectTaskElement.Parameters">
      <summary>Gets all unevaluated parameters of this task. </summary>
      <returns>Returns all unevaluated parameters of this task. </returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectTaskElement.RemoveAllParameters">
      <summary>Removes all parameters from the task. Does not remove any ContinueOnError and/or Condition attributes.</summary>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectTaskElement.RemoveParameter(System.String)">
      <summary>Removes any parameter on this task with the specified name. If no parameter by that name exists in the task, does nothing.</summary>
      <param name="name">The name of the parameter to remove.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectTaskElement.SetParameter(System.String,System.String)">
      <summary>Updates or adds a parameter on this task</summary>
      <param name="name">The name of the parameter to update or add.</param>
      <param name="unevaluatedValue">The value of the parameter.</param>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectUsingTaskBodyElement">
      <summary>Represents the body of an inline task.</summary>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectUsingTaskBodyElement.Condition">
      <summary>Gets a nonexistent condition, which is implicitly true.</summary>
      <returns>Returns a null.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectUsingTaskBodyElement.Evaluate">
      <summary>Gets the value of the Evaluate attribute.</summary>
      <returns>Returns the value of the Evaluate attribute, which is either "true" or "false". Returns "true" if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectUsingTaskBodyElement.TaskBody">
      <summary>Gets or sets the unevaluated value of the contents of the inline task. </summary>
      <returns>Returns the unevaluated inner XML content of the inline task. Returns an empty string if no inline task body is present.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectUsingTaskElement">
      <summary>Represents a UsingTask Element (MSBuild) in an MSBuild project. The UsingTask element is used both for inline tasks and precompiled tasks.</summary>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectUsingTaskElement.AddParameterGroup">
      <summary>Adds a new ParameterGroup element to this inline task. </summary>
      <returns>Returns the new parameter group.</returns>
    </member>
    <member name="M:Microsoft.Build.Construction.ProjectUsingTaskElement.AddUsingTaskBody(System.String,System.String)">
      <summary>Adds a new TaskBody element to this inline task.</summary>
      <returns>Returns the new task body.</returns>
      <param name="evaluate">A flag which, if true, expands the item and property values in the task body. This flag is true by default.</param>
      <param name="taskBody">The body of the task as a string.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectUsingTaskElement.AssemblyFile">
      <summary>Gets the value of the AssemblyFile attribute, which selects the name of the assembly to load.</summary>
      <returns>Returns the value of the AssemblyFile attribute. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectUsingTaskElement.AssemblyName">
      <summary>Gets and sets the value of the AssemblyName attribute. </summary>
      <returns>Returns the value of the AssemblyName attribute. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectUsingTaskElement.ParameterGroup">
      <summary>Gets any ParameterGroup element for this inline task.</summary>
      <returns>Returns the parameter group. Returns null if no parameter group exists.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectUsingTaskElement.TaskBody">
      <summary>Gets the inner XML content of this inline task.</summary>
      <returns>Returns the inner XML content of the inline task. Returns null if no body exists.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectUsingTaskElement.TaskFactory">
      <summary>Gets and sets the value of the TaskFactory attribute of this inline task.</summary>
      <returns>Returns the value of the TaskFactory attribute. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectUsingTaskElement.TaskName">
      <summary>Gets and sets the value of the TaskName attribute.</summary>
      <returns>Returns the value of the TaskName attribute. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectUsingTaskParameterElement">
      <summary>Represents a parameter of an inline task.</summary>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectUsingTaskParameterElement.Condition">
      <summary>Gets a nonexistent condition, which is implicitly true.</summary>
      <returns>Returns a null.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectUsingTaskParameterElement.Name">
      <summary>Gets and sets the name of the parameter of this inline task.</summary>
      <returns>Returns the name of the parameter.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectUsingTaskParameterElement.Output">
      <summary>Gets or sets the optional Output attribute of this inline task.</summary>
      <returns>Returns the value of the Output attribute. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectUsingTaskParameterElement.ParameterType">
      <summary>Gets or sets the Type attribute of this inline task.</summary>
      <returns>Returns the value of the Type attribute. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectUsingTaskParameterElement.Required">
      <summary>Gets or sets the Required attribute</summary>
      <returns>Returns the value of the Required attribute. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.ProjectWhenElement">
      <summary>Represents a When Element (MSBuild) in an MSBuild project.</summary>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectWhenElement.ChooseElements">
      <summary>Gets all child Choose Element (MSBuild).</summary>
      <returns>Returns all child Choose elements.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectWhenElement.ItemGroups">
      <summary>Gets all child item groups.</summary>
      <returns>Returns all child item groups.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.ProjectWhenElement.PropertyGroups">
      <summary>Gets all child property groups.</summary>
      <returns>Returns all child property groups.</returns>
    </member>
    <member name="T:Microsoft.Build.Construction.UsingTaskParameterGroupElement">
      <summary>Represents the ParameterGroup of an inline task.</summary>
    </member>
    <member name="M:Microsoft.Build.Construction.UsingTaskParameterGroupElement.AddParameter(System.String)">
      <summary>Adds a parameter to this parameter group.</summary>
      <returns>The new parameter.</returns>
      <param name="name">The name of the parameter to be added.</param>
    </member>
    <member name="M:Microsoft.Build.Construction.UsingTaskParameterGroupElement.AddParameter(System.String,System.String,System.String,System.String)">
      <summary>Adds a parameter to this parameter group, using the given name, type, and attributes.</summary>
      <returns>Returns the new parameter.</returns>
      <param name="name">The name of the parameter to be added.</param>
      <param name="output">The value of the Output attribute.</param>
      <param name="required">The value of the Required attribute.</param>
      <param name="parameterType">The type of the parameter.</param>
    </member>
    <member name="P:Microsoft.Build.Construction.UsingTaskParameterGroupElement.Condition">
      <summary>Gets a nonexistent condition, which is implicitly true.</summary>
      <returns>Returns a null.</returns>
    </member>
    <member name="P:Microsoft.Build.Construction.UsingTaskParameterGroupElement.Parameters">
      <summary>Gets all parameters of this parameter group.</summary>
      <returns>Returns all parameters.</returns>
    </member>
    <member name="T:Microsoft.Build.Debugging.DebuggerManager">
      <summary>Manager for supporting debugging a state machine.</summary>
    </member>
    <member name="T:Microsoft.Build.Debugging.DebuggerManager.IslandThread">
      <summary>Executes the islands on a dedicated worker thread. The worker thread's physical callstack then maps to the interpreter's virtual callstack.</summary>
    </member>
    <member name="M:Microsoft.Build.Debugging.DebuggerManager.IslandThread.IslandWorker(Microsoft.Build.Debugging.DebuggerManager.IslandThread)">
      <summary>Private entry point called from islands. Must be public so that the islands can invoke it. Called on debugger worker thread.</summary>
      <param name="controller">The thread calling the entry point.</param>
    </member>
    <member name="M:Microsoft.Build.Debugging.DebuggerManager.IslandThread.System#IDisposable#Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.Build.Debugging.DebuggerManager.IslandThread" /> class.</summary>
    </member>
    <member name="T:Microsoft.Build.Evaluation.Project">
      <summary>Represents a project with design time semantics. This class can load project files, edit items and properties and build projects.</summary>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor">
      <summary>Constructs an empty project using the global project collection global properties and default tools version.</summary>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor(Microsoft.Build.Construction.ProjectRootElement)">
      <summary>Constructs an empty project and evaluates it using the given project root and the global project collection global properties and default tools version.</summary>
      <param name="xml">ProjectRootElement to use for evaluation.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor(Microsoft.Build.Construction.ProjectRootElement,System.Collections.Generic.IDictionary{System.String,System.String},System.String)">
      <summary>Constructs an empty project and evaluates it using the given project root and with the given global properties and given tools version.</summary>
      <param name="xml">The project root to use for evaluation.</param>
      <param name="globalProperties">Global properties to evaluate with. May be null in which case the containing project collection's global properties will be used.</param>
      <param name="toolsVersion">Tools version to evaluate with. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor(Microsoft.Build.Construction.ProjectRootElement,System.Collections.Generic.IDictionary{System.String,System.String},System.String,Microsoft.Build.Evaluation.ProjectCollection)">
      <summary>Constructs a project and evaluates it using the given project root and with the given global properties and given tools version.</summary>
      <param name="xml">The project root to use for evaluation.</param>
      <param name="globalProperties">Global properties to evaluate with. May be null in which case the containing project collection's global properties will be used.</param>
      <param name="toolsVersion">Tools version to evaluate with. May be null.</param>
      <param name="projectCollection">The project collection the project is added to.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor(Microsoft.Build.Construction.ProjectRootElement,System.Collections.Generic.IDictionary{System.String,System.String},System.String,Microsoft.Build.Evaluation.ProjectCollection,Microsoft.Build.Evaluation.ProjectLoadSettings)">
      <summary>Constructs a project and evaluates it using the given project root and with the given global properties, given tools version, and given load settings.</summary>
      <param name="xml">The project root to use for evaluation.</param>
      <param name="globalProperties">Global properties to evaluate with. May be null in which case the containing project collection's global properties will be used.</param>
      <param name="toolsVersion">Tools version to evaluate with. May be null</param>
      <param name="projectCollection">The project collection the project is added to. May not be null.</param>
      <param name="loadSettings">The load settings to use for evaluation.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor(Microsoft.Build.Evaluation.ProjectCollection)">
      <summary>Constructs an empty project using the given project collection global properties and default tools version.</summary>
      <param name="projectCollection">The project collection that the new project is added to.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor(System.Collections.Generic.IDictionary{System.String,System.String},System.String,Microsoft.Build.Evaluation.ProjectCollection)">
      <summary>Constructs an empty project, to be evaluated with the given project collection and with the given global properties and given tools version. </summary>
      <param name="globalProperties">Global properties to evaluate with. May be null in which case the containing project collection's global properties will be used.</param>
      <param name="toolsVersion">The tools version used to evaluate the project. May be null.</param>
      <param name="projectCollection">The project collection the new project will be added to.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor(System.String)">
      <summary>Constructs a project and evaluates it from the given source project file and with the global project collection global properties and default tools version. </summary>
      <param name="projectFile">The source project file to be evaluated.</param>
      <exception cref="T:Microsoft.Build.Exceptions.InvalidProjectFileException">If the evaluation fails.</exception>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.String},System.String)">
      <summary>Constructs a project and evaluates it from the given source project file and with the given global properties and given tools version. </summary>
      <param name="projectFile">The path to the source project file to be evaluated.</param>
      <param name="globalProperties">Global properties to evaluate with. May be null in which case the containing project collection's global properties will be used.</param>
      <param name="toolsVersion">Tools version to evaluate with. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.String},System.String,Microsoft.Build.Evaluation.ProjectCollection)">
      <summary>Constructs a project and evaluates it from the given source project file and with the given global properties and given tools version. </summary>
      <param name="projectFile">The path to the source project file to be evaluated.</param>
      <param name="globalProperties">The global properties. May be null.</param>
      <param name="toolsVersion">The tools version. May be null.</param>
      <param name="projectCollection">The project collection the project is added to. May not be null.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.String},System.String,Microsoft.Build.Evaluation.ProjectCollection,Microsoft.Build.Evaluation.ProjectLoadSettings)">
      <summary>Constructs a project and evaluates it from the given source project file and with the given global properties, given tools version, and given load settings. </summary>
      <param name="projectFile">The path to the source project file to be evaluated.</param>
      <param name="globalProperties">Global properties to evaluate with. May be null in which case the containing project collection's global properties will be used.</param>
      <param name="toolsVersion">The tools version. May be null.</param>
      <param name="projectCollection">The project collection the project is added to. May not be null.</param>
      <param name="loadSettings">The load settings to use for evaluation.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor(System.Xml.XmlReader)">
      <summary>Constructs a project and evaluates the source code from the given XML reader. The source code is evaluated with the global project collection global properties and default tools version. </summary>
      <param name="xmlReader">Xml reader to read project source code from.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor(System.Xml.XmlReader,System.Collections.Generic.IDictionary{System.String,System.String},System.String)">
      <summary>Constructs a project and evaluates the source code from the given XML reader. The source code is evaluated with the given global properties and given tools version. </summary>
      <param name="xmlReader">Xml reader to read project source code from.</param>
      <param name="globalProperties">Global properties to evaluate with. May be null in which case the containing project collection's global properties will be used.</param>
      <param name="toolsVersion">Tools version to evaluate with. May be null</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor(System.Xml.XmlReader,System.Collections.Generic.IDictionary{System.String,System.String},System.String,Microsoft.Build.Evaluation.ProjectCollection)">
      <summary>Constructs a project and evaluates the source code from the given XML reader. The source code is evaluated with the given global properties and given tools version. </summary>
      <param name="xmlReader">Xml reader to read project source code from.</param>
      <param name="globalProperties">Global properties to evaluate with. May be null in which case the containing project collection's global properties will be used.</param>
      <param name="toolsVersion">Tools version to evaluate with. May be null</param>
      <param name="projectCollection">The project collection the project is added to. May not be null.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.#ctor(System.Xml.XmlReader,System.Collections.Generic.IDictionary{System.String,System.String},System.String,Microsoft.Build.Evaluation.ProjectCollection,Microsoft.Build.Evaluation.ProjectLoadSettings)">
      <summary>Constructs a project and evaluates the source code from the given XML reader. The source code is evaluated with the given global properties and given tools version. </summary>
      <param name="xmlReader">Xml reader to read project from</param>
      <param name="globalProperties">Global properties to evaluate with. May be null in which case the containing project collection's global properties will be used.</param>
      <param name="toolsVersion">Tools version to evaluate with. May be null</param>
      <param name="projectCollection">The project collection the project is added to. May not be null.</param>
      <param name="loadSettings">The load settings to use for evaluation.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.AddItem(System.String,System.String)">
      <summary>Adds an item with no metadata to the project. </summary>
      <returns>Returns the added item.</returns>
      <param name="itemType">The item type of the added item.</param>
      <param name="unevaluatedInclude">Include attribute of the item to be added.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.AddItem(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>Adds an item with the given metadata to the project. </summary>
      <returns>Returns the added item.</returns>
      <param name="itemType">The item type of the added item.</param>
      <param name="unevaluatedInclude">Include attribute of the item to be added.</param>
      <param name="metadata">The metadata of the added item.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.AddItemFast(System.String,System.String)">
      <summary>Adds an item with no metadata to the project.  Makes no effort to see if an existing wildcard would already match the new item, unless it is the first item in an item group.  Makes no effort to locate the new item near similar items.</summary>
      <returns>Returns a list of the project items added.</returns>
      <param name="itemType">The item type to be added.</param>
      <param name="unevaluatedInclude">The unevaluated Include attribute to be added to the item.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.AddItemFast(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>Adds an item with metadata to the project. Metadata may be null, indicating no metadata. Makes no effort to see if an existing wildcard would already match the new item, unless it is the first item in an item group. Makes no effort to locate the new item near similar items.</summary>
      <returns>Returns a list of the project items added.</returns>
      <param name="itemType">The item type to be added.</param>
      <param name="unevaluatedInclude">The unevaluated Include attribute to be added to the item.</param>
      <param name="metadata">The metadata to be added.</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.AllEvaluatedItemDefinitionMetadata">
      <summary>Gets all item definition metadata encountered during evaluation. </summary>
      <returns>Returns all item definition metadata encountered during evaluation. This does not include any elements whose conditions did not evaluate to true, nor any item definition metadata added since the last evaluation.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.AllEvaluatedItems">
      <summary>Gets an enumerator over all items encountered during evaluation. These are read during the third evaluation pass.</summary>
      <returns>Returns an enumerator over all items encountered during evaluation. These are read during the third evaluation pass.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.AllEvaluatedProperties">
      <summary>Gets all properties encountered during evaluation. </summary>
      <returns>Returns all properties encountered during evaluation. This does not include any properties whose conditions did not evaluate to true, nor any properties added since the last evaluation.</returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Build">
      <summary>Builds this project, using the default targets. </summary>
      <returns>Returns true on success; false otherwise. </returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Build(Microsoft.Build.Framework.ILogger)">
      <summary>Builds this project, using the default targets and the given logger.</summary>
      <returns>Returns true on success; false otherwise. </returns>
      <param name="logger">The logger to be used during the build.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Build(System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger})">
      <summary>Builds this project, using the default targets and the given loggers.</summary>
      <returns>Returns true on success; false otherwise. </returns>
      <param name="loggers">An enumerator over all loggers to be used during the build.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Build(System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger},System.Collections.Generic.IEnumerable{Microsoft.Build.Logging.ForwardingLoggerRecord})">
      <summary>Builds this project, using the default targets and the given loggers and remote loggers.</summary>
      <returns>Returns true on success; false otherwise. </returns>
      <param name="loggers">The loggers to be used during the build.</param>
      <param name="remoteLoggers">The remote loggers to be used during the build.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Build(System.String)">
      <summary>Builds this project, building the given target.</summary>
      <returns>Returns true on success; false otherwise. </returns>
      <param name="target">The target to be built.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Build(System.String,System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger})">
      <summary>Builds this project, building the given target and using the given loggers.</summary>
      <returns>Returns true on success; false otherwise. </returns>
      <param name="target">The target to be built.</param>
      <param name="loggers">The loggers to be used during the build.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Build(System.String,System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger},System.Collections.Generic.IEnumerable{Microsoft.Build.Logging.ForwardingLoggerRecord})">
      <summary>Builds this project, building the given target and using the given loggers and remote loggers.</summary>
      <returns>Returns true on success; false otherwise. </returns>
      <param name="target">The target to be built.</param>
      <param name="loggers">The loggers to be used during the build.</param>
      <param name="remoteLoggers">The remote loggers to be used during the build.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Build(System.String[])">
      <summary>Builds this project, building the given targets.</summary>
      <returns>Returns true on success; false otherwise. </returns>
      <param name="targets">An array of targets to be built.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Build(System.String[],System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger})">
      <summary>Builds this project, building the given targets and using the given loggers.</summary>
      <returns>Returns true on success; false otherwise. </returns>
      <param name="targets">The targets to be built.</param>
      <param name="loggers">The loggers to be used during the build.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Build(System.String[],System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger},System.Collections.Generic.IEnumerable{Microsoft.Build.Logging.ForwardingLoggerRecord})">
      <summary>Builds this project, building the given targets and using the given loggers and remote loggers.</summary>
      <returns>Returns true on success; false otherwise. </returns>
      <param name="targets">The targets to be built.</param>
      <param name="loggers">The loggers to be used during the build.</param>
      <param name="remoteLoggers">The remote loggers to be used during the build.</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.ConditionedProperties">
      <summary>Gets a collection of possible property values for properties used to evaluate conditions found on properties, property groups, imports, and whens.</summary>
      <returns>Returns a collection of possible property values for properties used to evaluate conditions found on properties, property groups, imports, and whens. The collection is in canonical form. The name of the entry is the name of the property to be evaluated, and the value is a list of possible property values.</returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.CreateProjectInstance">
      <summary>Creates a project instance based on this project, but completely disconnected from it. </summary>
      <returns>Returns the project instance.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.DirectoryPath">
      <summary>Gets the root directory for this project. </summary>
      <returns>Returns the root directory for this project. The root directory is never null; in-memory projects use the current directory at the time of project load.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.DisableMarkDirty">
      <summary>Gets or sets a flag the determines whether <see cref="M:Microsoft.Build.Evaluation.Project.MarkDirty" /> is temporarily disabled. This allows, for example, a global property to be set without the project getting marked dirty for reevaluation as a consequence.</summary>
      <returns>Returns a flag the determines whether MarkDirty is temporarily disabled.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.EvaluationCounter">
      <summary>Gets a number that is incremented every time that project is re-evaluated.</summary>
      <returns>Returns the incremented value. </returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.ExpandString(System.String)">
      <summary>Evaluates the given string by expanding items and properties. The string is evaluated as if it were found at the very end of the project file. </summary>
      <returns>Returns the evaluated string.</returns>
      <param name="unexpandedValue">The string to be evaluated.</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.FullPath">
      <summary>Gets or sets the full path to the project source file. </summary>
      <returns>Returns the full path to the project source file. Returns an empty string if the project was not loaded from disk. </returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.GetEvaluatedItemIncludeEscaped(Microsoft.Build.Evaluation.ProjectItem)">
      <summary>Gets evaluated, escaped value of the Include attribute of the provided items.</summary>
      <returns>Returns the evaluated, escaped value of the Include attribute of the provided items.</returns>
      <param name="item">The item to be evaluated and escaped.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.GetEvaluatedItemIncludeEscaped(Microsoft.Build.Evaluation.ProjectItemDefinition)">
      <summary>Gets the evaluated, escaped value of the Include attribute of the provided item definition.</summary>
      <returns>Returns the value of the Include attribute.</returns>
      <param name="item">The item definition to be evaluated and escaped.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.GetItems(System.String)">
      <summary>Gets all the items in the project of the given item type. If there are none, returns an empty list. Use AddItem or RemoveItem to modify items in this project.</summary>
      <returns>Returns all the items in the project of the given item type. Returns an empty list if there are no items with the given item name. </returns>
      <param name="itemType">The item type to be retrieved.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.GetItemsByEvaluatedInclude(System.String)">
      <summary>Gets all items that have the given evaluated Include attribute. </summary>
      <returns>Returns all items that have the given evaluated Include attribute.</returns>
      <param name="evaluatedInclude">The evaluated Include attribute.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.GetItemsIgnoringCondition(System.String)">
      <summary>Gets all the items in the project of the given type, whether or not their Condition attribute evaluates to true. This is a read-only list: use AddItem or RemoveItem to modify items in this project.</summary>
      <returns>Returns all the items in the project of the given type, whether or not their Condition attribute evaluates to true. </returns>
      <param name="itemType">The item type to be retrieved.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.GetLogicalProject">
      <summary>Gets an enumerator over all the elements in the "logical project". The logical project is defined as the unevaluated project obtained from the single MSBuild file that is the result of inlining the text of all imports of the original MSBuild project manifest file.</summary>
      <returns>Returns an enumerator over all the elements in the "logical project". </returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.GetMetadataValueEscaped(Microsoft.Build.Evaluation.ProjectItem,System.String)">
      <summary>Gets the escaped value of the metadatum with the provided name on the provided item.</summary>
      <returns>Gets the escaped value of the metadatum with the provided name on the provided item.</returns>
      <param name="item">The item whose metadata is to be escaped.</param>
      <param name="name">The name of the metadata to be escaped.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.GetMetadataValueEscaped(Microsoft.Build.Evaluation.ProjectItemDefinition,System.String)">
      <summary>Gets the escaped value of the metadatum with the provided name on the provided item definition.</summary>
      <returns>Returns the escaped value of the metadatum with the provided name on the provided item definition.</returns>
      <param name="item">The item definition whose metadata is to be escaped.</param>
      <param name="name">The name of the metadata to be escaped.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.GetMetadataValueEscaped(Microsoft.Build.Evaluation.ProjectMetadata)">
      <summary>Gets the escaped value of the provided metadatum.</summary>
      <returns>Gets the escaped value of the provided metadatum.</returns>
      <param name="metadatum">The metadatum to be escaped.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.GetProperty(System.String)">
      <summary>Gets any property in the project that has the specified name. </summary>
      <returns>Returns any property in the project that has the specified name. Returns null if no property of that name exists.</returns>
      <param name="name">The name of the property to retrieve.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.GetPropertyValue(System.String)">
      <summary>Gets the value of the given property in this project.</summary>
      <returns>Returns the value of the given property in this project. Return an empty string if no property of that name exists. Escape sequences are converted before returning the string.</returns>
      <param name="name">The name of the property whose value is retrieved.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.GetPropertyValueEscaped(Microsoft.Build.Evaluation.ProjectProperty)">
      <summary>Get the escaped value of the provided property.</summary>
      <returns>Returns the escaped value of the provided property.</returns>
      <param name="property">The property to be escaped.</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.GlobalProperties">
      <summary>Gets a dictionary of the global properties used for the evaluation of this project.</summary>
      <returns>Returns a dictionary of the global properties used for the evaluation of this project.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.Imports">
      <summary>Gets a list of all the files that contributed to the evaluation of this project.</summary>
      <returns>Returns a list of all the files that contributed to the evaluation of this project.The name of each item is the Import element that caused the file to be imported. The value of each item is the project root of the imported project. Import elements whose Condition attribute evaluated to false are not included. </returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.ImportsIncludingDuplicates">
      <summary>Gets a list of duplicate imports if an import was imported multiple times. However, only the first import was used in evaluation.</summary>
      <returns>Returns a list of duplicate imports.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.IsBuildEnabled">
      <summary>Gets or sets a property that selects whether the targets and tasks of this project can be built.</summary>
      <returns>Returns true if the targets and tasks of this project can be built; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.IsDirty">
      <summary>Gets a property that indicates whether this project has been modified so that it must be reevaluated. </summary>
      <returns>Returns a property that indicates whether this project has been modified so that it must be reevaluated. </returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.ItemDefinitions">
      <summary>Gets a dictionary of item definitions in this project, keyed by item type.</summary>
      <returns>Returns a dictionary of item definitions in this project, keyed by item type.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.Items">
      <summary>Gets an enumerator over all items in this project, ordered within groups of item types.</summary>
      <returns>Returns an enumerator over all items in this project, ordered within groups of item types.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.ItemsIgnoringCondition">
      <summary>Gets an enumerator over all items in this project, ordered within groups of item types. </summary>
      <returns>Returns an enumerator over all items in this project., ordered within groups of item types, including items whose conditions evaluate to false, or are contained within item groups whose condition evaluates to false. </returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.ItemTypes">
      <summary>Gets an enumerator over all item types in this project. </summary>
      <returns>Returns an enumerator over all item types in this project. This is an ordered collection.</returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.MarkDirty">
      <summary>Marks this project as modified. </summary>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.ProjectCollection">
      <summary>Gets the project collection which contains this project. </summary>
      <returns>Returns the project collection which contains this project, which is never null.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.Properties">
      <summary>Gets an enumerator over all properties in this project. This is an unordered collection.</summary>
      <returns>Returns an enumerator over all properties in this project. Since properties are evaluated as they appear, this is an unordered collection.</returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.ReevaluateIfNecessary">
      <summary>Reevaluates the project to incorporate any changes. </summary>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.RemoveGlobalProperty(System.String)">
      <summary>Removes a global property and marks the project as modified.</summary>
      <returns>Returns true if the value of the global property was set. </returns>
      <param name="name">The name of the global property to be removed.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.RemoveItem(Microsoft.Build.Evaluation.ProjectItem)">
      <summary>Removes an item from the project. The item to be removed must be present in the project, and must not originate from an imported file. </summary>
      <returns>Returns true if the item is present in this evaluated project; otherwise false. Normally this method returns true, since if the requested item is not present, the method throws an exception. If the item is present only in the ItemsIgnoringCondition collection, however, the method returns false.</returns>
      <param name="item">The item to be removed.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.RemoveItems(System.Collections.Generic.IEnumerable{Microsoft.Build.Evaluation.ProjectItem})">
      <summary>Removes all the given items from the project, unless they originate from an imported project. </summary>
      <param name="items">The items to be removed.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.RemoveProperty(Microsoft.Build.Evaluation.ProjectProperty)">
      <summary>Removes an property from the project. The property to be removed must be present in the project, and must not originate from an imported file. </summary>
      <returns>Returns true if the property is present in this evaluated project; otherwise false. </returns>
      <param name="property">The property to remove.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Save">
      <summary>Saves the project to the file system, if modified, using the default character encoding.</summary>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Save(System.IO.TextWriter)">
      <summary>Saves the project to the provided text writer, whether or not the project has been modified. Uses the character encoding of the text writer and marks the project as unmodified.</summary>
      <param name="writer">The text writer to save the project to.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Save(System.String)">
      <summary>Saves the project to the file system, if modified or if the path to the project source code changes, using the default character encoding.</summary>
      <param name="path">The path to the project source code.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Save(System.String,System.Text.Encoding)">
      <summary>Saves the project to the file system, if modified or if the path to the project source code changes, using the given character encoding.</summary>
      <param name="path">The path to the project source code.</param>
      <param name="encoding">The character encoding used to save the project.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.Save(System.Text.Encoding)">
      <summary>Saves the project to the file system, if modified, using the given character encoding.</summary>
      <param name="encoding">The character encoding used to save the project.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.SaveLogicalProject(System.IO.TextWriter)">
      <summary>Saves a "logical" or "preprocessed" project file, that includes all the imported files as if they formed a single file.</summary>
      <param name="writer">The writer that saves the project file.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.SetGlobalProperty(System.String,System.String)">
      <summary>Sets a global property after the project has been evaluated. If the value changes, the project is marked to require reevaluation. </summary>
      <param name="name">The name of the global property to set.</param>
      <param name="escapedValue">The new value of the global property.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Project.SetProperty(System.String,System.String)">
      <summary>Sets or adds a property with the given name and value to the project. Overwrites the value of a property with the same name if it did not originate in an imported file. </summary>
      <returns>Returns the property.</returns>
      <param name="name">The name of the property to set.</param>
      <param name="unevaluatedValue">The new unevaluated value of the property.</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.SkipEvaluation">
      <summary>Gets or sets a flag the determines whether ReevaluateIfNecessary is temporarily disabled. This is useful when the host expects to make a number of reads and writes to the project, and wants to temporarily sacrifice correctness for performance.</summary>
      <returns>Returns a flag the determines whether ReevaluateIfNecessary is temporarily disabled.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.Targets">
      <summary>Gets a dictionary of all targets in this project, keyed by target name. </summary>
      <returns>Returns a dictionary of all targets in this project, keyed by target name. Overridden targets are not included in this collection. This dictionary is read-only.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.ToolsVersion">
      <summary>Gets the tools version that this project was evaluated with, if any.</summary>
      <returns>Gets the tools version that this project was evaluated with, if any. </returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Project.Xml">
      <summary>Gets the root project associated with this project. Can never be null</summary>
      <returns>Returns the root project associated with this project, which can never be null.</returns>
    </member>
    <member name="T:Microsoft.Build.Evaluation.ProjectCollection">
      <summary>Encapsulates a set of related projects, their toolsets, a default set of global properties, and the loggers that should be used to build them. A global version of this class acts as the default project collection. </summary>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.#ctor">
      <summary>Creates a project collection with no global properties or loggers. The project collection toolset is initialized from the configuration file and registry.</summary>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.#ctor(Microsoft.Build.Evaluation.ToolsetDefinitionLocations)">
      <summary>Creates a project collection with no global properties or loggers. The project collection toolset is initialized from toolsets in the given locations.</summary>
      <param name="toolsetLocations">The locations from which to load toolsets.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.#ctor(System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>Instantiates a project collection with specified global properties, no loggers, and that reads toolset information from the configuration file and registry.</summary>
      <param name="globalProperties">The default global properties to use. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.#ctor(System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger},Microsoft.Build.Evaluation.ToolsetDefinitionLocations)">
      <summary>Instantiates a project collection with specified global properties and loggers and using the specified toolset locations.</summary>
      <param name="globalProperties">The default global properties to use. May be null.</param>
      <param name="loggers">The loggers to register. May be null.</param>
      <param name="toolsetDefinitionLocations">The locations from which to load toolsets.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.#ctor(System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger},System.Collections.Generic.IEnumerable{Microsoft.Build.Logging.ForwardingLoggerRecord},Microsoft.Build.Evaluation.ToolsetDefinitionLocations,System.Int32,System.Boolean)">
      <summary>Creates a project collection with specified global properties, loggers, node count, and onlyLogCriticalEvents value. The project collection toolset is initialized from toolsets in the given locations. </summary>
      <param name="globalProperties">The default global properties to use. May be null.</param>
      <param name="loggers">The loggers to register. May be null and specified to any build instead.</param>
      <param name="remoteLoggers">Any remote loggers to register. May be null and specified to any build instead.</param>
      <param name="toolsetDefinitionLocations">The locations from which to load toolsets.</param>
      <param name="maxNodeCount">The maximum number of nodes to use for building.</param>
      <param name="onlyLogCriticalEvents">If set to true, only critical events will be logged.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.AddToolset(Microsoft.Build.Evaluation.Toolset)">
      <summary>Adds a new toolset to the project collection. Replaces any existing toolset with the same tools version.</summary>
      <param name="toolset">The toolset to be added.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.ContainsToolset(System.String)">
      <summary>Determines whether a toolset is defined for the given tools version.</summary>
      <returns>Returns true if there is a toolset defined for the given tools version; false otherwise.</returns>
      <param name="toolsVersion">The tools version to search for.</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectCollection.Count">
      <summary>Gets the number of projects currently loaded into this collection.</summary>
      <returns>Returns the number of projects currently loaded into this collection.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectCollection.DefaultToolsVersion">
      <summary>Gets the default tools version of this project collection. </summary>
      <returns>Returns the default tools version of this project collection, which is always defined. </returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectCollection.DisableMarkDirty">
      <summary>Gets or sets a flag that determines whether <see cref="M:Microsoft.Build.Evaluation.Project.MarkDirty" /> is temporarily disabled on projects in this collection. This allows, for example, a global properties to be set without projects getting marked dirty for reevaluation as a consequence.</summary>
      <returns>Returns a flag that determines whether MarkDirty is temporarily disabled.</returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.Dispose">
      <summary>Releases both managed and unmanaged resources.  Called when a host no longer needs the project collection. </summary>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.Dispose(System.Boolean)">
      <summary>Releases both managed and unmanaged resources.  Called when a host no longer needs the project collection.. Shuts down any logging services that the project collection owns and releases the logger thread.</summary>
      <param name="disposing">If true, releases both managed and unmanaged resources; otherwise releases only unmanaged resources..</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.Escape(System.String)">
      <summary>Converts special characters in a string to MSBuild escape format. </summary>
      <returns>Returns the converted string.</returns>
      <param name="unescapedString">The string to be converted.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.GetGlobalProperty(System.String)">
      <summary>Get any global property on the project collection that has the specified name.</summary>
      <returns>Returns any global property on the project collection that has the specified name; otherwise returns null.</returns>
      <param name="name">The name of the property to be retrieved.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.GetLoadedProjects(System.String)">
      <summary>Gets all projects whose project path matches the given path. </summary>
      <returns>Returns all projects whose project path matches the given path. </returns>
      <param name="fullPath">The path to a project file to be matched.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.GetToolset(System.String)">
      <summary>Get the toolset with the specified tools version. </summary>
      <returns>Returns the toolset with the specified tools version. Returns null if no toolset matching this tools version exists.</returns>
      <param name="toolsVersion">The tools version to match.</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectCollection.GlobalProjectCollection">
      <summary>Gets the global project collection object. </summary>
      <returns>Returns the global project collection object. This is a singleton project collection with no global properties or loggers. Toolset information is obtained from the configuration file and registry.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectCollection.GlobalProperties">
      <summary>Gets the read-only default global properties for all projects in this collection. </summary>
      <returns>Returns the read-only default global properties for all projects in this collection. </returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectCollection.HostServices">
      <summary>Gets or sets an object that provides host services to tasks during builds of projects contained in the project collection. </summary>
      <returns>Returns the host services object.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectCollection.IsBuildEnabled">
      <summary>Gets or sets a property that selects by default whether the targets and tasks of projects in the project collection can be built.</summary>
      <returns>Gets or sets a property that selects by default whether the targets and tasks of projects in this project collection can be built.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectCollection.LoadedProjects">
      <summary>Gets all the projects currently loaded into this collection.</summary>
      <returns>Returns all the projects currently loaded into this collection. </returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.LoadProject(System.String)">
      <summary>Evaluates a project from the source code in the given project file. The source code is evaluated with the global properties and tools version of this project collection. </summary>
      <returns>If an existing project in the collection has already been evaluated from the given project file, the existing project is returned. Otherwise, a new project is created, evaluated, added to the project collection, and returned.</returns>
      <param name="fileName">The project file to evaluate.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.LoadProject(System.String,System.Collections.Generic.IDictionary{System.String,System.String},System.String)">
      <summary>Evaluates a project from the source code read from the given project file. The source code is evaluated with the given global properties and tools version. </summary>
      <returns>If an existing project in the collection has already been evaluated from the given project file, and with the given global properties and tools version, then the existing project is returned. Otherwise, a new project is created, evaluated, added to the project collection, and returned.</returns>
      <param name="fileName">The project file to be evaluated.</param>
      <param name="globalProperties">The global properties to use. May be null, in which case the containing project collection's global properties will be used.</param>
      <param name="toolsVersion">The tools version. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.LoadProject(System.String,System.String)">
      <summary>Evaluates a project from the source code read from the given project file. The source code is evaluated with the global properties of this project collection and the given tools version. </summary>
      <returns>If an existing project in the collection has already been evaluated from the given project file, and with the given tools version, then the existing project is returned. Otherwise, a new project is created, evaluated, added to the project collection, and returned.</returns>
      <param name="fileName">The project file to evaluate.</param>
      <param name="toolsVersion">The tools version to use. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.LoadProject(System.Xml.XmlReader)">
      <summary>Evaluates a project from the source code read from the given XML reader. The source code is evaluated with the global properties and tools version of this project collection. </summary>
      <returns>Returns the new project.</returns>
      <param name="xmlReader">Xml reader to read project source code from.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.LoadProject(System.Xml.XmlReader,System.Collections.Generic.IDictionary{System.String,System.String},System.String)">
      <summary>Evaluates a project from the source code read from the given XML reader. The source code is evaluated with the given global properties and tools version. </summary>
      <returns>Returns the new project.</returns>
      <param name="xmlReader">Xml reader to read project from</param>
      <param name="globalProperties">The global properties to use. May be null in which case the containing project collection's global properties will be used.</param>
      <param name="toolsVersion">The tools version. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.LoadProject(System.Xml.XmlReader,System.String)">
      <summary>Evaluates a project from the source code read from the given XML reader. The source code is evaluated with the global properties of this project collection and the given tools version. </summary>
      <returns>Returns the new project.</returns>
      <param name="xmlReader">Xml reader to read project source code from.</param>
      <param name="toolsVersion">The tools version to use. May be null.</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectCollection.Loggers">
      <summary>Gets all loggers that projects in this collection can use for their builds. </summary>
      <returns>Returns all loggers that projects in this collection can use for their builds. </returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectCollection.OnlyLogCriticalEvents">
      <summary>Gets or sets a switch that determines whether only critical events such as warnings and errors are logged.</summary>
      <returns>If true, only critical events such as warnings and errors are logged; false otherwise.</returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.RegisterForwardingLoggers(System.Collections.Generic.IEnumerable{Microsoft.Build.Logging.ForwardingLoggerRecord})">
      <summary>Adds the given remote loggers to the collection of remote loggers used for builds of projects in this collection. </summary>
      <param name="remoteLoggers">The remote loggers to add. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.RegisterLogger(Microsoft.Build.Framework.ILogger)">
      <summary>Adds the given logger to the collection of loggers used for builds of projects in this collection. </summary>
      <param name="logger">The logger to add.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.RegisterLoggers(System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger})">
      <summary>Adds the given loggers to the collection of loggers used for builds of projects in this collection. </summary>
      <param name="loggers">The loggers to be added. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.RemoveAllToolsets">
      <summary>Removes all toolsets from the project collection.</summary>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.RemoveGlobalProperty(System.String)">
      <summary>Removes a global property from the set of default global properties.</summary>
      <returns>Returns true if the global property is present before removal; false otherwise.</returns>
      <param name="name">The name of the default global property to remove.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.RemoveToolset(System.String)">
      <summary>Removes a toolset from the project collection.</summary>
      <returns>Returns true if the toolset is present before removal; false otherwise.</returns>
      <param name="toolsVersion">The toolset to be removed.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.SetGlobalProperty(System.String,System.String)">
      <summary>Sets the value of a property in the default set of global properties.</summary>
      <param name="name">The name of the default global property to be added or set.</param>
      <param name="value">The new value of the default global property.</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectCollection.SkipEvaluation">
      <summary>Gets or sets a flag the determines whether ReevaluateIfNecessary is temporarily disabled on projects in this collection. This is useful when the host expects to make a number of reads and writes to projects, and wants to temporarily sacrifice correctness for performance.</summary>
      <returns>Returns a flag the determines whether ReevaluateIfNecessary is temporarily disabled.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectCollection.ToolsetLocations">
      <summary>Gets the locations used to find the toolsets.</summary>
      <returns>Returns the locations used to find the toolsets.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectCollection.Toolsets">
      <summary>Gets the toolsets available to this project collection.</summary>
      <returns>Returns the toolsets available to this project collection.</returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.TryUnloadProject(Microsoft.Build.Construction.ProjectRootElement)">
      <summary>Attempts to remove a project from the collection.</summary>
      <returns>Returns true if the project was unloaded.</returns>
      <param name="projectRootElement">The root element of the project to unload.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.Unescape(System.String)">
      <summary>Converts escaped characters in a string to MSBuild characters with special meaning. </summary>
      <returns>Returns the converted string.</returns>
      <param name="escapedString">The string to be converted.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.UnloadAllProjects">
      <summary>Removes all projects in this project collection. </summary>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.UnloadProject(Microsoft.Build.Construction.ProjectRootElement)">
      <summary>Removes a project root element from the project root cache.</summary>
      <param name="projectRootElement">The project root element to unload.</param>
      <exception cref="T:System.InvalidOperationException">Thrown if the project root element to unload is still in use by a loaded project or its imports.</exception>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.UnloadProject(Microsoft.Build.Evaluation.Project)">
      <summary>Removes the given project from the project collection.</summary>
      <param name="project">The project to remove.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectCollection.UnregisterAllLoggers">
      <summary>Removes all loggers from the collection of loggers used by project builds in this collection.</summary>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectCollection.Version">
      <summary>Gets the file version of the assembly file that contains the MSBuild engine.</summary>
      <returns>Returns the file version of the assembly file that contains the MSBuild engine.</returns>
    </member>
    <member name="T:Microsoft.Build.Evaluation.ProjectItem">
      <summary>Represents an evaluated design-time project item.</summary>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectItem.DirectMetadata">
      <summary>Gets an enumerator over all item metadata. </summary>
      <returns>Returns an enumerator over all item metadata, which can never be null. </returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectItem.DirectMetadataCount">
      <summary>Gets the count of metadata for this item. </summary>
      <returns>Returns the count of metadata for this item. </returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectItem.EvaluatedInclude">
      <summary>Gets the evaluated value of the Include attribute.</summary>
      <returns>Returns the evaluated value of the Include attribute.</returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectItem.GetMetadata(System.String)">
      <summary>Gets the evaluated value of the given metadata for this item. </summary>
      <returns>Gets the evaluated value of the given metadata for this item. Returns null if no metadata exists with the given name.</returns>
      <param name="name">The name of the metadata whose value is retrieved.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectItem.GetMetadataValue(System.String)">
      <summary>Get the evaluated value of the given metadata for this item, including metadata originating from an item definition.</summary>
      <returns>Returns the evaluated value of the given metadata for this item, including metadata originating from an item definition. Returns an empty string if no metadata exists with the given name.</returns>
      <param name="name">The name of the metadata whose value is retrieved.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectItem.HasMetadata(System.String)">
      <summary>Determines whether metadata with the given name is defined on this item.</summary>
      <returns>Returns true if metadata with the given name is defined on this item; false otherwise. </returns>
      <param name="name">The name of the metadata to be searched for.</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectItem.IsImported">
      <summary>Determines if this item originates from an imported file.</summary>
      <returns>Return true if this item originates from an imported file; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectItem.ItemType">
      <summary>Gets or sets the type of this item.</summary>
      <returns>Returns the item type.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectItem.Metadata">
      <summary>Gets all the metadata for this item, including metadata originating from item definitions. </summary>
      <returns>Returns all the metadata for this item, including metadata originating from item definitions.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectItem.MetadataCount">
      <summary>Gets the count of metadata for this item, including any metadata originating from item definitions, and any built-in metadata.</summary>
      <returns>Returns the count of metadata for this item, including any metadata originating from item definitions, and any built-in metadata.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectItem.Project">
      <summary>Gets the project that contains this project item.</summary>
      <returns>Returns the project that contains this project item.</returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectItem.RemoveMetadata(System.String)">
      <summary>Removes any metadata with the given name. </summary>
      <returns>Returns true if metadata with the given name exists before removal; false otherwise. </returns>
      <param name="name">The name of the metadata to remove.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectItem.Rename(System.String)">
      <summary>Renames the item to the given name. </summary>
      <param name="name">The new name of the item.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectItem.SetMetadataValue(System.String,System.String)">
      <summary>Adds metadata with the given name and unevaluated value to the item. </summary>
      <returns>Returns the new metadata. If metadata with the given name already exists, returns this metadata.</returns>
      <param name="name">The name of the metadata to add.</param>
      <param name="unevaluatedValue">The unevaluated value of the metadata to add.</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectItem.UnevaluatedInclude">
      <summary>Gets or sets the unevaluated value of the Include attribute.</summary>
      <returns>Returns the unevaluated value of the Include attribute.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectItem.Xml">
      <summary>Gets the item element that is associated with this item. </summary>
      <returns>Returns the item element that is associated with this item, which can never be null. </returns>
    </member>
    <member name="T:Microsoft.Build.Evaluation.ProjectItemDefinition">
      <summary>Represents an evaluated item definition for a particular item-type.</summary>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectItemDefinition.GetMetadata(System.String)">
      <summary>Get any metadata in the item definition with the given name.</summary>
      <returns>Get any metadata in the item definition with the given name. Returns null if no metadata exists with that name.</returns>
      <param name="name">The name of the metadata to retrieve.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectItemDefinition.GetMetadataValue(System.String)">
      <summary>Get the value of any metadata in the item that has the specified name.</summary>
      <returns>Returns the value of any metadata in the item that has the specified name; otherwise returns null.</returns>
      <param name="name">The name of the item whose metadata value is to be retrieved.</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectItemDefinition.ItemType">
      <summary>Gets the item type of this item definition.</summary>
      <returns>Returns the item type of this item definition.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectItemDefinition.Metadata">
      <summary>Gets an enumerator over all metadata for the item definition. </summary>
      <returns>Returns an enumerator over all metadata for the item definition. </returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectItemDefinition.MetadataCount">
      <summary>Gets the count of the metadata on the item definition.</summary>
      <returns>Returns the count of the metadata on the item definition.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectItemDefinition.Project">
      <summary>Gets the project that contains this item definition. </summary>
      <returns>Returns the project that contains this item definition, which is never null.</returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectItemDefinition.SetMetadataValue(System.String,System.String)">
      <summary>Sets the value of the given metadata in the item definition.</summary>
      <returns>Returns the metadata with the given name.</returns>
      <param name="name">The name of the metadata to set.</param>
      <param name="unevaluatedValue">The new unevaluated value of the metadata.</param>
    </member>
    <member name="T:Microsoft.Build.Evaluation.ProjectLoadSettings">
      <summary>Flags for controlling the project load.</summary>
    </member>
    <member name="F:Microsoft.Build.Evaluation.ProjectLoadSettings.Default">
      <summary>Normal load. This is the default.</summary>
    </member>
    <member name="F:Microsoft.Build.Evaluation.ProjectLoadSettings.IgnoreMissingImports">
      <summary>Ignore nonexistent targets files when evaluating the project.</summary>
    </member>
    <member name="F:Microsoft.Build.Evaluation.ProjectLoadSettings.RecordDuplicateButNotCircularImports">
      <summary>Indicates that imports including duplicate, but not circular, imports on the ImportsIncludingDuplicates property should be recorded</summary>
    </member>
    <member name="F:Microsoft.Build.Evaluation.ProjectLoadSettings.RejectCircularImports">
      <summary>Indicates that an exception will be thrown if any circular imports are detected</summary>
    </member>
    <member name="T:Microsoft.Build.Evaluation.ProjectMetadata">
      <summary>Represents evaluated design-time metadata. </summary>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectMetadata.EvaluatedValue">
      <summary>Gets the evaluated metadata value. </summary>
      <returns>Returns the evaluated metadata value, which is never null.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectMetadata.IsImported">
      <summary>Determines if the metadata originated from an imported file.</summary>
      <returns>Returns true if the metadata originated from an imported file; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectMetadata.ItemType">
      <summary>Gets the item type of the containing item definition or item.</summary>
      <returns>Returns the item type of the containing item definition or item.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectMetadata.Name">
      <summary>Gets the name of the metadata.</summary>
      <returns>Returns the name of the metadata.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectMetadata.Predecessor">
      <summary>Gets the last metadata (from an item definition or item) that was overridden by this metadata during evaluation. </summary>
      <returns>Returns the last metadata (from an item definition or item) whose value was overridden by this metadata during evaluation. Returns null if the metadata has not been overridden.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectMetadata.Project">
      <summary>Gets the project that contains this metadata.</summary>
      <returns>Returns the project that contains this metadata, which is never null.</returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectMetadata.System#IEquatable{T}#Equals(Microsoft.Build.Evaluation.ProjectMetadata)">
      <summary>Compares this project metadata with the given project metadata for equality.</summary>
      <returns>Returns true if the project metadata are equal; false otherwise.</returns>
      <param name="other">The project metadata to be compared to.</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectMetadata.UnevaluatedValue">
      <summary>Gets or sets the unevaluated metadata value. </summary>
      <returns>Returns the unevaluated metadata value.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectMetadata.Xml">
      <summary>Gets the associated project metadata element.</summary>
      <returns>Returns the associated project metadata element, which can never be null.</returns>
    </member>
    <member name="T:Microsoft.Build.Evaluation.ProjectProperty">
      <summary>Represents an evaluated design-time property.</summary>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectProperty.EvaluatedValue">
      <summary>Gets the evaluated property value. </summary>
      <returns>Returns the evaluated property value, which is never null. </returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectProperty.IsEnvironmentProperty">
      <summary>Determines whether the property originated from the environment variables.</summary>
      <returns>True if the property originated from an environment variable; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectProperty.IsGlobalProperty">
      <summary>Determines whether the property is a global property.</summary>
      <returns>True if the property is a global property; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectProperty.IsImported">
      <summary>Determines whether the property originates from an imported file.</summary>
      <returns>Returns true if the property originates from an imported file and not from an environment variable, a global property, or a reserved property. Returns false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectProperty.IsReservedProperty">
      <summary>Determines whether the property is a reserved property, for example 'MSBuildProjectFile'.</summary>
      <returns>Returns true if the property is a reserved property; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectProperty.Name">
      <summary>Gets the name of the property. </summary>
      <returns>Returns the name of the property. </returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectProperty.Predecessor">
      <summary>Gets the last property that was overridden by this property during evaluation. </summary>
      <returns>Returns the last property whose value was overridden by this property during evaluation. Returns null if the property has not been overridden.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectProperty.Project">
      <summary>Gets the project that contains this property.</summary>
      <returns>Returns the project that contains this property, which is never null.</returns>
    </member>
    <member name="M:Microsoft.Build.Evaluation.ProjectProperty.System#IEquatable{T}#Equals(Microsoft.Build.Evaluation.ProjectProperty)">
      <summary>Compares this project property with the given project property for equality.</summary>
      <returns>Returns true if the project properties are equal; false otherwise.</returns>
      <param name="other">The project property to be compared to</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectProperty.UnevaluatedValue">
      <summary>Gets or sets the unevaluated property value. </summary>
      <returns>Returns the unevaluated property value. </returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ProjectProperty.Xml">
      <summary>Gets the associated property element. Backing XML property. Null only if this is a global, environment, or built-in property.</summary>
      <returns>Returns the associated property element. Return null if this is a global or reserved property, or originates from an environment variable.</returns>
    </member>
    <member name="T:Microsoft.Build.Evaluation.ResolvedImport">
      <summary>Encapsulates an import relationship in an evaluated project between a ProjectImportElement and the ProjectRootElement of the imported project.</summary>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ResolvedImport.ImportedProject">
      <summary>Gets one of the imported projects.</summary>
      <returns>Returns a <see cref="T:Microsoft.Build.Construction.ProjectRootElement" /> representing the imported project.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.ResolvedImport.ImportingElement">
      <summary>Gets the element doing the import.</summary>
      <returns>Returns the <see cref="T:Microsoft.Build.Construction.ProjectImportElement" /> representing the element doing the import.</returns>
    </member>
    <member name="T:Microsoft.Build.Evaluation.Toolset">
      <summary>Represent an aggregation of a tools version (eg. "2.0"), tools path, and optional set of associated properties. </summary>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Toolset.#ctor(System.String,System.String,Microsoft.Build.Evaluation.ProjectCollection,System.String)">
      <summary>Creates a toolset with the given tools version and tools path. Global, reserved, and environment properties are taken from the given project collection.</summary>
      <param name="toolsVersion">Name of the toolset</param>
      <param name="toolsPath">Path to this toolset's tasks and targets</param>
      <param name="projectCollection">The project collection from which to obtain the properties.</param>
      <param name="msbuildOverrideTasksPath">The path to search for msbuild overridetasks files.</param>
    </member>
    <member name="M:Microsoft.Build.Evaluation.Toolset.#ctor(System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String},Microsoft.Build.Evaluation.ProjectCollection,System.String)">
      <summary>Creates a toolset with the given tools version, tools path, and build properties. Global, reserved, and environment properties are taken from the given project collection.</summary>
      <param name="toolsVersion">Name of the toolset</param>
      <param name="toolsPath">Path to this toolset's tasks and targets</param>
      <param name="buildProperties">Properties that should be associated with the Toolset. May be null, in which case an empty property group will be used.</param>
      <param name="projectCollection">The project collection from which to obtain the properties.</param>
      <param name="msbuildOverrideTasksPath">The path to search for msbuild overridetasks files.</param>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Toolset.Properties">
      <summary>Gets the optional properties associated with the toolset.</summary>
      <returns>Returns the optional properties associated with the toolset.</returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Toolset.ToolsPath">
      <summary>Gets a path to the tasks and targets of this toolset. </summary>
      <returns>Returns a path to the tasks and targets of this toolset. </returns>
    </member>
    <member name="P:Microsoft.Build.Evaluation.Toolset.ToolsVersion">
      <summary>Gets the name of this toolset.</summary>
      <returns>Returns the name of this toolset.</returns>
    </member>
    <member name="T:Microsoft.Build.Evaluation.ToolsetDefinitionLocations">
      <summary>Flags for controlling the toolset initialization. </summary>
    </member>
    <member name="F:Microsoft.Build.Evaluation.ToolsetDefinitionLocations.None">
      <summary>Do not read toolset information from any external location.</summary>
    </member>
    <member name="F:Microsoft.Build.Evaluation.ToolsetDefinitionLocations.ConfigurationFile">
      <summary>Read toolset information from the exe configuration file.</summary>
    </member>
    <member name="F:Microsoft.Build.Evaluation.ToolsetDefinitionLocations.Registry">
      <summary>Read toolset information from the registry (HKLM\Software\Microsoft\MSBuild\ToolsVersions).</summary>
    </member>
    <member name="T:Microsoft.Build.Exceptions.BuildAbortedException">
      <summary>An exception representing the case where the build was aborted by request, as opposed to being unceremoniously shut down due to another kind of error exception. </summary>
    </member>
    <member name="M:Microsoft.Build.Exceptions.BuildAbortedException.#ctor">
      <summary>Creates a BuildAborted exception.</summary>
    </member>
    <member name="M:Microsoft.Build.Exceptions.BuildAbortedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Protected constructor used for (de)serialization. </summary>
      <param name="info">The error information.</param>
      <param name="context">The error context.</param>
    </member>
    <member name="M:Microsoft.Build.Exceptions.BuildAbortedException.#ctor(System.String)">
      <summary>Creates a BuildAborted exception.</summary>
      <param name="message">The error message.</param>
    </member>
    <member name="M:Microsoft.Build.Exceptions.BuildAbortedException.#ctor(System.String,System.Exception)">
      <summary>Constructs a BuildAbortedException with an additional message attached and an inner exception.</summary>
      <param name="message">The error message.</param>
      <param name="innerException">The inner exception.</param>
    </member>
    <member name="P:Microsoft.Build.Exceptions.BuildAbortedException.ErrorCode">
      <summary>Gets the error code (if any) associated with the exception message.</summary>
      <returns>Returns the error code as a string; returns a null string if no error code exists.</returns>
    </member>
    <member name="M:Microsoft.Build.Exceptions.BuildAbortedException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>ISerializable method which must be overridden because Exception implements this interface.</summary>
      <param name="info">The error information.</param>
      <param name="context">The error context.</param>
    </member>
    <member name="T:Microsoft.Build.Exceptions.InternalLoggerException">
      <summary>This exception is used to wrap an unhandled exception from a logger. This exception aborts the build, and it can only be thrown by the MSBuild engine.</summary>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InternalLoggerException.#ctor">
      <summary>Default constructor.</summary>
      <exception cref="T:System.InvalidOperationException">Operation is invalid.</exception>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InternalLoggerException.#ctor(System.String)">
      <summary>Creates an instance of this exception using the specified error message.</summary>
      <param name="message">The error message.</param>
      <exception cref="T:System.InvalidOperationException">Operation is invalid.</exception>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InternalLoggerException.#ctor(System.String,System.Exception)">
      <summary>Creates an instance of this exception using the specified error message and inner exception.</summary>
      <param name="message">The error message.</param>
      <param name="innerException">The inner exception.</param>
      <exception cref="T:System.InvalidOperationException">Operation is invalid.</exception>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InternalLoggerException.BuildEventArgs">
      <summary>Gets the details of the build event (if any) that was being logged.</summary>
      <returns>The build event args, or null.</returns>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InternalLoggerException.ErrorCode">
      <summary>Gets the error code associated with this exception's message (not the inner exception).</summary>
      <returns>The error code string.</returns>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InternalLoggerException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>ISerializable method which we must override since Exception implements this interface If we ever add new members to this class, we'll need to update this.</summary>
      <param name="info">The error information.</param>
      <param name="context">The error context.</param>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InternalLoggerException.HelpKeyword">
      <summary>Gets the F1-help keyword associated with this error, for the host IDE.</summary>
      <returns>The keyword string.</returns>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InternalLoggerException.InitializationException">
      <summary>True if the exception occured during logger initialization</summary>
    </member>
    <member name="T:Microsoft.Build.Exceptions.InvalidProjectFileException">
      <summary>This exception is thrown whenever there is a problem with the user's XML project file. The problem might be semantic or syntactical. The latter would be of a type typically caught by XSD validation (if it was performed by the project writer). </summary>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InvalidProjectFileException.#ctor">
      <summary>Default constructor.</summary>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InvalidProjectFileException.#ctor(System.String)">
      <summary>Creates an instance of this exception using the specified error message.</summary>
      <param name="message">The error message.</param>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InvalidProjectFileException.#ctor(System.String,System.Exception)">
      <summary>Creates an instance of this exception using the specified error message and inner exception.</summary>
      <param name="message">The error message.</param>
      <param name="innerException">The inner exception.</param>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InvalidProjectFileException.#ctor(System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,System.String)">
      <summary>Creates an instance of this exception using rich error information.</summary>
      <param name="projectFile">The invalid project file (can be empty string).</param>
      <param name="lineNumber">The invalid line number in the project (set to zero if not available).</param>
      <param name="columnNumber">The invalid column number in the project (set to zero if not available).</param>
      <param name="endLineNumber">The end of a range of invalid lines in the project (set to zero if not available).</param>
      <param name="endColumnNumber">The end of a range of invalid columns in the project (set to zero if not available).</param>
      <param name="message">Error message for exception.</param>
      <param name="errorSubcategory">Error sub-category that describes the error (can be null).</param>
      <param name="errorCode">The error code (can be null).</param>
      <param name="helpKeyword">The F1-help keyword for the host IDE (can be null).</param>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InvalidProjectFileException.BaseMessage">
      <summary>Gets the exception message not including the project file.</summary>
      <returns>The error message string only.</returns>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InvalidProjectFileException.ColumnNumber">
      <summary>Gets the invalid column number (if any) in the project.</summary>
      <returns>The invalid column number, or zero.</returns>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InvalidProjectFileException.EndColumnNumber">
      <summary>Gets the last column number (if any) of a range of invalid columns in the project.</summary>
      <returns>The last invalid column number, or zero.</returns>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InvalidProjectFileException.EndLineNumber">
      <summary>Gets the last line number (if any) of a range of invalid lines in the project.</summary>
      <returns>The last invalid line number, or zero.</returns>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InvalidProjectFileException.ErrorCode">
      <summary>Gets the error code (if any) associated with the exception message.</summary>
      <returns>Error code string, or null.</returns>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InvalidProjectFileException.ErrorSubcategory">
      <summary>Gets the error sub-category (if any) that describes the type of this error.</summary>
      <returns>The sub-category string, or null.</returns>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InvalidProjectFileException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>ISerializable method which we must override since Exception implements this interface If we ever add new members to this class, we'll need to update this.</summary>
      <param name="info">The error information.</param>
      <param name="context">The error context.</param>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InvalidProjectFileException.HasBeenLogged">
      <summary>Gets a flag that determines whether the exception has already been logged. Allows the exception to be logged at the most appropriate location, but continue to be propagated.</summary>
      <returns>Returns a flag that determines whether the exception has already been logged.</returns>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InvalidProjectFileException.HelpKeyword">
      <summary>Gets the F1-help keyword (if any) associated with this error, for the host IDE.</summary>
      <returns>The keyword string, or null.</returns>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InvalidProjectFileException.LineNumber">
      <summary>Gets the invalid line number (if any) in the project.</summary>
      <returns>The invalid line number, or zero.</returns>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InvalidProjectFileException.Message">
      <summary>Gets the exception message including the affected project file (if any).</summary>
      <returns>The complete message string.</returns>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InvalidProjectFileException.ProjectFile">
      <summary>Gets the file (if any) associated with this exception. This may be an imported file.</summary>
      <returns>Project filename/path string, or null.</returns>
    </member>
    <member name="T:Microsoft.Build.Exceptions.InvalidToolsetDefinitionException">
      <summary>Exception subclass that ToolsetReaders should throw.</summary>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InvalidToolsetDefinitionException.#ctor">
      <summary>Basic constructor.</summary>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InvalidToolsetDefinitionException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Basic constructor.</summary>
      <param name="info">The error information.</param>
      <param name="context">The error context.</param>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InvalidToolsetDefinitionException.#ctor(System.String)">
      <summary>Basic constructor.</summary>
      <param name="message">The error message.</param>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InvalidToolsetDefinitionException.#ctor(System.String,System.Exception)">
      <summary>Basic constructor.</summary>
      <param name="message">The error message.</param>
      <param name="innerException">The inner exception.</param>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InvalidToolsetDefinitionException.#ctor(System.String,System.String)">
      <summary>Constructor that takes an MSBuild error code</summary>
      <param name="message">The error message.</param>
      <param name="errorCode">The error code.</param>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InvalidToolsetDefinitionException.#ctor(System.String,System.String,System.Exception)">
      <summary>Constructor that takes an MSBuild error code</summary>
      <param name="message">The error message.</param>
      <param name="errorCode">The error code.</param>
      <param name="innerException">The inner exception.</param>
    </member>
    <member name="P:Microsoft.Build.Exceptions.InvalidToolsetDefinitionException.ErrorCode">
      <summary>The MSBuild error code corresponding with this exception, or null if none was specified.</summary>
    </member>
    <member name="M:Microsoft.Build.Exceptions.InvalidToolsetDefinitionException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>ISerializable method which we must override since Exception implements this interface If we ever add new members to this class, we'll need to update this.</summary>
      <param name="info">The error information.</param>
      <param name="context">The error context.</param>
    </member>
    <member name="T:Microsoft.Build.Execution.BuildManager">
      <summary>This class is the public entry point for executing builds.</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildManager.#ctor">
      <summary>Gets a new build manager without specifying a name.</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildManager.#ctor(System.String)">
      <summary>Private constructor.</summary>
      <param name="hostName">The host for the build manager.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildManager.BeginBuild(Microsoft.Build.Execution.BuildParameters)">
      <summary>Prepares the BuildManager to receive build requests.</summary>
      <param name="parameters">The build parameters. May be null.</param>
      <exception cref="T:System.InvalidOperationException">Thrown if a build is already in progress.</exception>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildManager.Build(Microsoft.Build.Execution.BuildParameters,Microsoft.Build.Execution.BuildRequestData)">
      <summary>Submits a lone build request and blocks until results are available.</summary>
      <returns>Returns the build result.</returns>
      <param name="parameters">Build parameters.</param>
      <param name="requestData">Build request data.</param>
      <exception cref="T:System.InvalidOperationException">Thrown if a build is already in progress.</exception>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildManager.BuildRequest(Microsoft.Build.Execution.BuildRequestData)">
      <summary>Submits a build request and blocks until the results are available.</summary>
      <returns>Returns the build result.</returns>
      <param name="requestData">Build request.</param>
      <exception cref="T:System.InvalidOperationException">Thrown if StartBuild has not been called or if EndBuild has been called.</exception>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildManager.CancelAllSubmissions">
      <summary>Cancels all outstanding submissions asynchronously.</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildManager.DefaultBuildManager">
      <summary>Gets the singleton instance of the Build Manager.</summary>
      <returns>Returns the singleton instance of the Build Manager.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildManager.EndBuild">
      <summary>Signals that no more build requests are expected (or allowed) and that the BuildManager may clean up.</summary>
      <exception cref="T:System.InvalidOperationException">Thrown if there is no build in progress.</exception>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildManager.GetProjectInstanceForBuild(Microsoft.Build.Evaluation.Project)">
      <summary>Gets a matching ProjectInstance from the BuildManager cache of previously built projects.</summary>
      <returns>Returns a matching ProjectInstance from the BuildManager cache of previously built projects. If none exist, a new project instance will be created from the specified project.</returns>
      <param name="project">The project for which an instance should be retrieved.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildManager.PendBuildRequest(Microsoft.Build.Execution.BuildRequestData)">
      <summary>Submits a build request to the current build but does not start it immediately. Allows the user to perform asynchronous execution or access the submission ID prior to executing the request.</summary>
      <returns>Returns the submission that will be built.</returns>
      <param name="requestData">Build request data.</param>
      <exception cref="T:System.InvalidOperationException">Thrown if StartBuild has not been called or if EndBuild has been called.</exception>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildManager.ResetCaches">
      <summary>Clears out all of the build manager cached information.</summary>
    </member>
    <member name="T:Microsoft.Build.Execution.BuildParameters">
      <summary>This class represents all of the settings which must be specified to start a build.</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildParameters.#ctor">
      <summary>Creates build parameters.</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildParameters.#ctor(Microsoft.Build.Evaluation.ProjectCollection)">
      <summary>Creates build parameters and initializes them from a project collection.</summary>
      <param name="projectCollection">The ProjectCollection from which the BuildParameters should populate itself.</param>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.BuildThreadPriority">
      <summary>Gets or sets the desired thread priority for building.</summary>
      <returns>Returns the thread priority for building</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildParameters.Clone">
      <summary>Creates a clone of this build parameters object. </summary>
      <returns>Returns a clone of this build parameters object. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.Culture">
      <summary>Gets or sets the name of the culture to use during the build.</summary>
      <returns>Returns the culture.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.DefaultToolsVersion">
      <summary>Gets or sets the default tools version for the build.</summary>
      <returns>Returns the tools version.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.DetailedSummary">
      <summary>Gets or sets a switch that specifies whether the build should emit a detailed summary at the end of the log.</summary>
      <returns>Returns the value of a switch that specifies whether the build should emit a detailed summary at the end of the log</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.EnableNodeReuse">
      <summary>Gets or sets a flag determining whether out-of-process nodes should persist after the build and wait for further builds.</summary>
      <returns>Returns the flag.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.EnvironmentProperties">
      <summary>Gets an immutable collection of environment properties.</summary>
      <returns>Returns an immutable collection of environment properties.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.ForwardingLoggers">
      <summary>Gets or sets the collection of forwarding logger descriptions.</summary>
      <returns>Returns the collection of forwarding logger descriptions.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildParameters.GetToolset(System.String)">
      <summary>Gets a given toolset.</summary>
      <returns>Returns the toolset.</returns>
      <param name="toolsVersion">The toolset to retrieve.</param>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.GlobalProperties">
      <summary>Gets or sets an immutable collection of global properties.</summary>
      <returns>Returns an immutable collection of global properties.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.HostServices">
      <summary>Gets or sets a service that allows the host to provide additional control over the build process.</summary>
      <returns>Returns the host service.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.LegacyThreadingSemantics">
      <summary>Gets or sets a switch that enables or disables legacy threading semantics.</summary>
      <returns>Returns a switch that enables or disables legacy threading semantics; true if enabled, false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.Loggers">
      <summary>Gets or sets the collection of loggers to use during the build.</summary>
      <returns>Returns the collection of loggers to use during the build.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.MaxNodeCount">
      <summary>Gets or sets the maximum number of nodes this build may use.</summary>
      <returns>Returns the maximum number of nodes this build may use.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.MemoryUseLimit">
      <summary>Gets or sets the amount of memory the build should limit itself to using, in megabytes.</summary>
      <returns>Returns the amount of memory the build should limit itself to using, in megabytes.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.NodeExeLocation">
      <summary>Gets or sets the location of the build node executable.</summary>
      <returns>Returns the location of the build node executable.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.OnlyLogCriticalEvents">
      <summary>Gets or sets a flag that determines if non-critical logging events should be discarded.</summary>
      <returns>Returns a flag that determines if non-critical logging events should be discarded.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.ResetCaches">
      <summary>Gets or sets a switch that specifies whether the build should reset the configuration and results caches.</summary>
      <returns>Returns a switch that specifies whether the build should reset the configuration and results caches; true if the caches should be reset, false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.SaveOperatingEnvironment">
      <summary>Gets or sets a switch specifying if the operating environment, such as the current directory and environment, should be saved and restored between project builds and task invocations.</summary>
      <returns>Returns a switch indicating whether the operating environment should be saved and restored between project builds and task invocations; true if the environment should be saved, false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.ToolsetDefinitionLocations">
      <summary>Gets or sets locations to search for toolsets.</summary>
      <returns>Returns locations to search for toolsets.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.Toolsets">
      <summary>Gets all of the toolsets.</summary>
      <returns>Returns all of the toolsets.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.UICulture">
      <summary>Gets or sets the name of the UI culture to use during the build.</summary>
      <returns>Returns the name of the UI culture to use during the build.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildParameters.UseSynchronousLogging">
      <summary>Gets or sets a switch that specifies whether synchronous logging is used when there is only a single process.</summary>
      <returns>Returns a switch that specifies whether synchronous logging is used when there is only a single process; if true, use synchronous logging, otherwise use asynchronous logging.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.BuildRequestData">
      <summary>Encapsulates all of the data needed to submit a build request.</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildRequestData.#ctor(Microsoft.Build.Execution.ProjectInstance,System.String[])">
      <summary>Constructs build request data for build requests based on project instances.</summary>
      <param name="projectInstance">The instance to build.</param>
      <param name="targetsToBuild">The targets to build.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildRequestData.#ctor(Microsoft.Build.Execution.ProjectInstance,System.String[],Microsoft.Build.Execution.HostServices)">
      <summary>Constructs build request data for build requests based on project instances.</summary>
      <param name="projectInstance">The instance to build.</param>
      <param name="targetsToBuild">The targets to build.</param>
      <param name="hostServices">The host services to use, if any.  May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildRequestData.#ctor(Microsoft.Build.Execution.ProjectInstance,System.String[],Microsoft.Build.Execution.HostServices,Microsoft.Build.Execution.BuildRequestDataFlags)">
      <summary>Constructs a BuildRequestData for build requests based on project instances.</summary>
      <param name="projectInstance">The instance to build.</param>
      <param name="targetsToBuild">The targets to build.</param>
      <param name="hostServices">The host services to use, if any.  May be null.</param>
      <param name="flags">The flags to use for the build.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildRequestData.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.String},System.String,System.String[],Microsoft.Build.Execution.HostServices)">
      <summary>Constructs a BuildRequestData for build requests with a given project path.</summary>
      <param name="projectFullPath">The full path to the project file.</param>
      <param name="globalProperties">The global properties to use during evaluation of the project.</param>
      <param name="toolsVersion">The tools version to use for the build.  May be null.</param>
      <param name="targetsToBuild">The targets to build.</param>
      <param name="hostServices">The host services to use, if any.  May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildRequestData.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.String},System.String,System.String[],Microsoft.Build.Execution.HostServices,Microsoft.Build.Execution.BuildRequestDataFlags)">
      <summary>Constructs a BuildRequestData for build requests with a given project path.</summary>
      <param name="projectFullPath">The full path to the project file.</param>
      <param name="globalProperties">The global properties to use during evaluation of the project.</param>
      <param name="toolsVersion">The tools version to use for the build.  May be null.</param>
      <param name="targetsToBuild">The targets to build.</param>
      <param name="hostServices">The host services to use, if any.  May be null.</param>
      <param name="flags">The flags to use for the build.</param>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildRequestData.ExplicitlySpecifiedToolsVersion">
      <summary>Gets or sets the tools version to use for the build.</summary>
      <returns>Returns the tools version to use for the build.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildRequestData.Flags">
      <summary>Gets or sets additional flags for this build request.</summary>
      <returns>Returns the build request flags.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildRequestData.GlobalProperties">
      <summary>Gets the global properties to use for this build request.</summary>
      <returns>Returns the global properties to use for this build request.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildRequestData.HostServices">
      <summary>Gets the host service for this build.</summary>
      <returns>Returns the host service for this build.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildRequestData.ProjectFullPath">
      <summary>Gets of sets the path to the project file for this build.</summary>
      <returns>Returns the path to the project file for this build.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildRequestData.ProjectInstance">
      <summary>Gets or sets project instance for this build.</summary>
      <returns>The project instance, which may be null.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildRequestData.TargetNames">
      <summary>Gets the names of the targets to build.</summary>
      <returns>Returns the names of the targets to build.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.BuildRequestDataFlags">
      <summary>Flags providing additional control over the build request. </summary>
    </member>
    <member name="F:Microsoft.Build.Execution.BuildRequestDataFlags.None">
      <summary>No flags.</summary>
    </member>
    <member name="F:Microsoft.Build.Execution.BuildRequestDataFlags.ReplaceExistingProjectInstance">
      <summary>When this flag is present, the existing ProjectInstance in the build will be replaced by this one.</summary>
    </member>
    <member name="T:Microsoft.Build.Execution.BuildResult">
      <summary>Represents the current result set for all of the targets that have produced results for a particular configuration.</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildResult.#ctor">
      <summary>Creates the build result set.</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildResult.AddResultsForTarget(System.String,Microsoft.Build.Execution.TargetResult)">
      <summary>Adds the results for the specified target to this build result set.</summary>
      <param name="target">The target whose results are to be added.</param>
      <param name="result">The results for the target.</param>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildResult.CircularDependency">
      <summary>Gets a flag indicating whether a circular dependency was detected.</summary>
      <returns>Returns true if a circular dependency was detected; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildResult.ConfigurationId">
      <summary>Gets the configuration ID for this build result set.</summary>
      <returns>Returns the configuration ID for this build result set.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildResult.Exception">
      <summary>Gets the exception generated for this build result set. </summary>
      <returns>Returns the exception generated for this build result set. Returns false if no exception occurred.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildResult.GlobalRequestId">
      <summary>Gets the build request id for this build result set.</summary>
      <returns>Returns the build request id for this build result set.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildResult.HasResultsForTarget(System.String)">
      <summary>Determines if there are any results for the given target.</summary>
      <returns>Returns true if results exist; false otherwise.</returns>
      <param name="target">The target whose results are retrieved.</param>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildResult.Item(System.String)">
      <summary>Gets an indexer which can be used to get the build result for the given target.</summary>
      <returns>The build result for the indexed target.</returns>
      <param name="target">The indexed target.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildResult.MergeResults(Microsoft.Build.Execution.BuildResult)">
      <summary>Merges the given results with this build result set.</summary>
      <param name="results">The results to merge.</param>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildResult.NodeRequestId">
      <summary>Gets the build request ID of the originating node.</summary>
      <returns>Returns the build request ID of the originating node.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildResult.OverallResult">
      <summary>Gets the overall result for this build.</summary>
      <returns>Returns the overall result for this build.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildResult.ParentGlobalRequestId">
      <summary>Gets the global build request ID which issued the request leading to this build result set.</summary>
      <returns>Returns the global build request ID which issued the request leading to this build result set.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildResult.ResultsByTarget">
      <summary>Gets an enumerator over all target results in this build result set.</summary>
      <returns>Returns an enumerator over all target results in this build result set.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildResult.SubmissionId">
      <summary>Gets the build submission which this build result set is associated with.</summary>
      <returns>Returns the build submission which this build result set is associated with.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.BuildResultCode">
      <summary>Enumerated data indicating the build result of a target or a  build request.</summary>
    </member>
    <member name="F:Microsoft.Build.Execution.BuildResultCode.Success">
      <summary>The target or request was a complete success.</summary>
    </member>
    <member name="F:Microsoft.Build.Execution.BuildResultCode.Failure">
      <summary>The target or request failed in some way.</summary>
    </member>
    <member name="T:Microsoft.Build.Execution.BuildSubmission">
      <summary>Represents a build request that has been submitted to the build manager for processing. The methods of this class may be used to execute synchronous or asynchronous build requests and to provide access to the results upon completion.</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildSubmission.AsyncContext">
      <summary>Gets or sets the asynchronous context provided to <see cref="M:Microsoft.Build.Execution.BuildSubmission.ExecuteAsync(Microsoft.Build.Execution.BuildSubmissionCompleteCallback,System.Object)" />, if any.</summary>
      <returns>Returns the asynchronous context provided to <see cref="M:Microsoft.Build.Execution.BuildSubmission.ExecuteAsync(Microsoft.Build.Execution.BuildSubmissionCompleteCallback,System.Object)" />. Returns null if no context is provided.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildSubmission.BuildManager">
      <summary>Gets or sets the build manager this build submission is associated with.</summary>
      <returns>Returns the build manager this build submission is associated with.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildSubmission.BuildResult">
      <summary>Gets or sets the result of the build. </summary>
      <returns>Returns the result of the build. </returns>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildSubmission.Execute">
      <summary>Starts a build request synchronously and waits until results are available.</summary>
      <returns>Returns the build request.</returns>
      <exception cref="T:System.InvalidOperationException">The request has already been started or is already complete.</exception>
    </member>
    <member name="M:Microsoft.Build.Execution.BuildSubmission.ExecuteAsync(Microsoft.Build.Execution.BuildSubmissionCompleteCallback,System.Object)">
      <summary>Starts a build request asynchronously and within the given context. Immediately returns control to the caller.</summary>
      <param name="callback">The callback method to call when the build submission is complete.</param>
      <param name="context">The context in which the build submission occurs.</param>
      <exception cref="T:System.InvalidOperationException">The request has already been started or is already complete.</exception>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildSubmission.IsCompleted">
      <summary>Determines whether this build submission is complete.</summary>
      <returns>Returns true if this build submission is complete; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildSubmission.SubmissionId">
      <summary>Gets or sets an ID uniquely identifying this build submission.</summary>
      <returns>Returns an ID uniquely identifying this build submission. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.BuildSubmission.WaitHandle">
      <summary>Gets a <see cref="T:System.Threading.WaitHandle" /> object that will be signalled when the build is complete. </summary>
      <returns>Returns null until <see cref="M:Microsoft.Build.Execution.BuildSubmission.Execute" /> or <see cref="M:Microsoft.Build.Execution.BuildSubmission.ExecuteAsync(Microsoft.Build.Execution.BuildSubmissionCompleteCallback,System.Object)" /> returns, then returns the WaitHandle object.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.BuildSubmissionCompleteCallback">
      <summary>A callback function that receives notification when a build completes.</summary>
      <param name="submission">The submission that initiates the build.</param>
    </member>
    <member name="T:Microsoft.Build.Execution.HostServices">
      <summary>Implementation of a host service that mediates access from the build to the host.</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.HostServices.#ctor">
      <summary>Creates a host service.</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.HostServices.GetHostObject(System.String,System.String,System.String)">
      <summary>Gets the host service for the given task where the task appears within a target and project with the given names. </summary>
      <returns>Returns the host service for the given task where the task appears within a target and project with the given names. If no host service exists, returns null.</returns>
      <param name="projectFile">The name of the project file.</param>
      <param name="targetName">The name of the target.</param>
      <param name="taskName">The task name associated with the host service.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.HostServices.GetNodeAffinity(System.String)">
      <summary>Gets the node affinity for the given project file.</summary>
      <returns>Returns the node affinity for the given project file.</returns>
      <param name="projectFile">The project file associated with the node.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.HostServices.OnRenameProject(System.String,System.String)">
      <summary>Updates the host services table when a project is named or renamed. </summary>
      <param name="oldFullPath">The current path to the project file.</param>
      <param name="newFullPath">The new path to the project file.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.HostServices.RegisterHostObject(System.String,System.String,System.String,Microsoft.Build.Framework.ITaskHost)">
      <summary>Register a host service for the given task/target pair. </summary>
      <param name="projectFile">The project file containing the task/target pair.</param>
      <param name="targetName">The name of the target.</param>
      <param name="taskName">The name of the task.</param>
      <param name="hostObject">The host service.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.HostServices.SetNodeAffinity(System.String,Microsoft.Build.Execution.NodeAffinity)">
      <summary>Sets the node affinity for the given project file.</summary>
      <param name="projectFile">The project file. If set to String.Empty, all projects will use the given node affinity. If set to null, all node affinities will be cleared.</param>
      <param name="nodeAffinity">The node affinity.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.HostServices.UnregisterProject(System.String)">
      <summary>Unregister the project's host objects, if any and remove any node affinities associated with it.</summary>
      <param name="projectFullPath">The path to the project to unregister.</param>
    </member>
    <member name="T:Microsoft.Build.Execution.ITargetResult">
      <summary>An interface representing the results for a specific target</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ITargetResult.Exception">
      <summary>Gets the exception generated when the target was built.</summary>
      <returns>Returns the exception generated when the target was built. Return null if no exception occurred.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ITargetResult.Items">
      <summary>Gets the set of build items output by the target. </summary>
      <returns>Returns the set of build items output by the target. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ITargetResult.ResultCode">
      <summary>Gets the result code returned when the target was built.</summary>
      <returns>Returns the result code returned when the target was built.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.NodeAffinity">
      <summary>An enumerated data type that determines where projects are built.</summary>
    </member>
    <member name="F:Microsoft.Build.Execution.NodeAffinity.InProc">
      <summary>The project may only be scheduled on the in-proc node. This happens automatically if there is a host object or if a ProjectInstance was specified. A host may wish to specify it if they know a task depends explicitly on shared static data or other host-provided objects.</summary>
    </member>
    <member name="F:Microsoft.Build.Execution.NodeAffinity.OutOfProc">
      <summary>The project may only be scheduled on an out-of-proc node. A host may wish to specify this if it is known the project being built could contaminate the host environment (or the host contaminates the environment while a build is proceeding.)</summary>
    </member>
    <member name="F:Microsoft.Build.Execution.NodeAffinity.Any">
      <summary>The project may be scheduled anywhere.</summary>
    </member>
    <member name="T:Microsoft.Build.Execution.NodeEngineShutdownReason">
      <summary>Reasons for a node to shutdown.</summary>
    </member>
    <member name="F:Microsoft.Build.Execution.NodeEngineShutdownReason.BuildComplete">
      <summary>The BuildManager sent a command instructing the node to terminate.</summary>
    </member>
    <member name="F:Microsoft.Build.Execution.NodeEngineShutdownReason.BuildCompleteReuse">
      <summary>The BuildManager sent a command instructing the node to terminate, but to restart for reuse.</summary>
    </member>
    <member name="F:Microsoft.Build.Execution.NodeEngineShutdownReason.ConnectionFailed">
      <summary>The communication link failed.</summary>
    </member>
    <member name="F:Microsoft.Build.Execution.NodeEngineShutdownReason.Error">
      <summary>The NodeEngine caught an exception which requires the Node to shut down.</summary>
    </member>
    <member name="T:Microsoft.Build.Execution.OutOfProcNode">
      <summary>This class represents an implementation of an out-of-proc build node.  This class is deprecated and has no alternative.</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.OutOfProcNode.#ctor">
      <summary>Constructor.</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.OutOfProcNode.Run(System.Exception@)">
      <summary>Starts up the node and processes messages until the node is requested to shut down.</summary>
      <returns>The reason for shutting down.</returns>
      <param name="shutdownException">The exception which caused shutdown, if any.</param>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectInstance">
      <summary>Represents a project instance. </summary>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.#ctor(Microsoft.Build.Construction.ProjectRootElement)">
      <summary>Creates a new project instance and initializes it from the given project root, using the default project collection. </summary>
      <param name="xml">The project root element.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.#ctor(Microsoft.Build.Construction.ProjectRootElement,System.Collections.Generic.IDictionary{System.String,System.String},System.String,Microsoft.Build.Evaluation.ProjectCollection)">
      <summary>Creates a new project instance and initializes it from the given project root, using the given global properties, tools version, and project collection. </summary>
      <param name="xml">The project root element</param>
      <param name="globalProperties">The global properties to use. May be null.</param>
      <param name="toolsVersion">The tools version. May be null.</param>
      <param name="projectCollection">Project collection</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.#ctor(System.String)">
      <summary>Creates a new project instance and initializes it from the given project file, using the default project collection. </summary>
      <param name="projectFile">The name of the project file.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.String},System.String)">
      <summary>Creates a new project instance and initializes it from the given project file, using the given global properties, tools version, and the default project collection. </summary>
      <param name="projectFile">The name of the project file.</param>
      <param name="globalProperties">The global properties to use.</param>
      <param name="toolsVersion">The tools version.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.String},System.String,Microsoft.Build.Evaluation.ProjectCollection)">
      <summary>Creates a new project instance and initializes it from the given project file, using the given global properties, tools version, and project collection. </summary>
      <param name="projectFile">The name of the project file.</param>
      <param name="globalProperties">The global properties to use. May be null.</param>
      <param name="toolsVersion">The tools version. May be null.</param>
      <param name="projectCollection">Project collection</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.AddItem(System.String,System.String)">
      <summary>Adds an item with no metadata to the project.</summary>
      <returns>Returns the new item.</returns>
      <param name="itemType">The item type of the item to be added.</param>
      <param name="evaluatedInclude">The evaluated Include attribute of the item.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.AddItem(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>Adds an item with the given metadata to the project.</summary>
      <returns>Return the new item.</returns>
      <param name="itemType">The item type of the item to be added.</param>
      <param name="evaluatedInclude">The evaluated Include attribute of the item.</param>
      <param name="metadata">The metadata of the item.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.Build">
      <summary>Builds the default targets of the project with loggers of the project collection. </summary>
      <returns>Returns true if the build succeeds; false otherwise.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.Build(System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger})">
      <summary>Builds the default targets of the project with the given loggers. </summary>
      <returns>Returns true if the build succeeds; false otherwise.</returns>
      <param name="loggers">The loggers to use for the build. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.Build(System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger},System.Collections.Generic.IEnumerable{Microsoft.Build.Logging.ForwardingLoggerRecord})">
      <summary>Builds the default targets of the project with the given loggers and remote loggers. </summary>
      <returns>Returns true if the build succeeds; false otherwise.</returns>
      <param name="loggers">The loggers to use for the build. May be null.</param>
      <param name="remoteLoggers">The remote loggers to use for the build. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.Build(System.String,System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger})">
      <summary>Builds the given target of the project with the given loggers. </summary>
      <returns>Returns true if the build succeeds; false otherwise.</returns>
      <param name="target">The target to build. May be null.</param>
      <param name="loggers">The loggers to use for the build. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.Build(System.String,System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger},System.Collections.Generic.IEnumerable{Microsoft.Build.Logging.ForwardingLoggerRecord})">
      <summary>Builds the given target of the project with the given loggers and remote loggers. </summary>
      <returns>Returns true if the build succeeds; false otherwise.</returns>
      <param name="target">The target to build. May be null.</param>
      <param name="loggers">The loggers to use for the build. May be null.</param>
      <param name="remoteLoggers">The remote loggers to use for the build. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.Build(System.String[],System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger})">
      <summary>Builds the given targets of the project with the given loggers. </summary>
      <returns>Returns true if the build succeeds; false otherwise.</returns>
      <param name="targets">The targets to build. May be null.</param>
      <param name="loggers">The loggers to use for the build. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.Build(System.String[],System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger},System.Collections.Generic.IDictionary{System.String,Microsoft.Build.Execution.TargetResult}@)">
      <summary>Build a list of targets with specified loggers. Targets may be null. Loggers may be null.</summary>
      <returns>Returns true on success, false on failure.</returns>
      <param name="targets">List of targets to build. May be null.</param>
      <param name="loggers">Enumerated list of loggers to use for building targets.</param>
      <param name="targetOutputs">Outputs of target builds.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.Build(System.String[],System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger},System.Collections.Generic.IEnumerable{Microsoft.Build.Logging.ForwardingLoggerRecord})">
      <summary>Builds the given targets of the project with the given loggers and remote loggers. </summary>
      <returns>Returns true if the build succeeds; false otherwise.</returns>
      <param name="targets">The targets to build. May be null.</param>
      <param name="loggers">The loggers to use for the build. May be null.</param>
      <param name="remoteLoggers">The remote loggers to use for the build. May be null.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.Build(System.String[],System.Collections.Generic.IEnumerable{Microsoft.Build.Framework.ILogger},System.Collections.Generic.IEnumerable{Microsoft.Build.Logging.ForwardingLoggerRecord},System.Collections.Generic.IDictionary{System.String,Microsoft.Build.Execution.TargetResult}@)">
      <summary>Builds the given targets with the given target outputs and with the given loggers and remote loggers. </summary>
      <returns>Returns true if the build succeeds; false otherwise.</returns>
      <param name="targets">The targets to build. May be null.</param>
      <param name="loggers">The loggers to use for the build. May be null.</param>
      <param name="remoteLoggers">The remote loggers to use for the build. May be null.</param>
      <param name="targetOutputs">The target outputs.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.DeepCopy">
      <summary>Creates an independent, deep clone of this project and everything in it. Useful for compiling a single file or for keeping build results between builds.</summary>
      <returns>Creates an independent, deep clone of this project.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectInstance.DefaultTargets">
      <summary>Gets or sets the list of default targets for this project. </summary>
      <returns>Returns the list of default targets for this project. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectInstance.Directory">
      <summary>Gets the project root directory. Used for evaluation of relative paths and setting the current directory during build. Is never null: projects not loaded from disk use the current directory from the time the build started.</summary>
      <returns>Returns the project root directory, which is never null. Projects not loaded from disk return the current directory at the time the build was started.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.EvaluateCondition(System.String)">
      <summary>Evaluates the provided string as a condition by expanding items and properties, using the current items and properties available, then doing a logical evaluation.</summary>
      <returns>Returns true if the string evaluates to true; false otherwise.</returns>
      <param name="condition">The string to be expanded.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.ExpandString(System.String)">
      <summary>Evaluates the provided string by expanding items and properties, using the current items and properties available.</summary>
      <returns>Returns the expanded string value.</returns>
      <param name="unexpandedValue">The string to be expanded.</param>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectInstance.FullPath">
      <summary>Gets the full path to the project file. Used for logging. </summary>
      <returns>Returns the full path to the project file. Returns null if the project was never named.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.GetEvaluatedItemIncludeEscaped(Microsoft.Build.Execution.ProjectItemDefinitionInstance)">
      <summary>Gets the evaluated, escaped value of the Include attribute of the provided item definition.</summary>
      <returns>Returns the evaluated, escaped value of the Include attribute of the provided item definition.</returns>
      <param name="item">The item definition to be evaluated and escaped.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.GetEvaluatedItemIncludeEscaped(Microsoft.Build.Execution.ProjectItemInstance)">
      <summary>Gets the evaluated, escaped value of the Include attribute of the provided item.</summary>
      <returns>Returns the evaluated, escaped value of the Include attribute of the provided item.</returns>
      <param name="item">The item to be evaluated and escaped.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.GetItems(System.String)">
      <summary>Gets all the items in this project of the specified type. This is a read-only list.</summary>
      <returns>Returns all the items in this project of the specified type. Returns an empty list if there are none.</returns>
      <param name="itemType">The item type of the items to be retrieved.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.GetMetadataValueEscaped(Microsoft.Build.Execution.ProjectItemDefinitionInstance,System.String)">
      <summary>Gets the escaped value of the metadatum with the provided name of the provided item definition.</summary>
      <returns>Returns the escaped value of the metadatum with the provided name of the provided item definition.</returns>
      <param name="item">The item definition to provide the metadatum value.</param>
      <param name="name">The name of the metadatum.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.GetMetadataValueEscaped(Microsoft.Build.Execution.ProjectItemInstance,System.String)">
      <summary>Gets the escaped value of the metadatum with the provided name on the provided item.</summary>
      <returns>Returns the escaped value of the metadatum with the provided name on the provided item.</returns>
      <param name="item">The item to provide the metadatum value.</param>
      <param name="name">The name of the metadatum.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.GetMetadataValueEscaped(Microsoft.Build.Execution.ProjectMetadataInstance)">
      <summary>Gets the escaped value of the provided metadatum.</summary>
      <returns>Returns the escaped value of the provided metadatum.</returns>
      <param name="metadatum">The metadatum to be escaped.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.GetProperty(System.String)">
      <summary>Get the property with the given name.</summary>
      <returns>Returns the property with the given name. Returns null if no property exists with that name.</returns>
      <param name="name">The name of the property to retrieve.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.GetPropertyValue(System.String)">
      <summary>Get the value of the property with the given name.</summary>
      <returns>Returns the value of the property with the given name. Returns an empty string if no property exists with that name. The value returned has no escaped character sequences.</returns>
      <param name="name">The name of the property whose value is to be retrieved.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.GetPropertyValueEscaped(Microsoft.Build.Execution.ProjectPropertyInstance)">
      <summary>Get the escaped value of the provided property.</summary>
      <returns>Returns the escaped value of the provided property.</returns>
      <param name="property">The property to be escaped.</param>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectInstance.GlobalProperties">
      <summary>Gets the dictionary of global properties this project was evaluated with, if any.</summary>
      <returns>Returns the dictionary of global properties this project was evaluated with.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectInstance.InitialTargets">
      <summary>Gets the list of initial targets for the project and all its imports, depth-first. These targets are built before any other targets.</summary>
      <returns>Returns the list of initial targets for the project and all its imports, depth-first. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectInstance.ItemDefinitions">
      <summary>Gets a read-only dictionary of the item definitions in the project, keyed by item type.</summary>
      <returns>Returns a read-only dictionary of the item definitions in the project, keyed by item type.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectInstance.Items">
      <summary>Gets all items in this project.</summary>
      <returns>Returns all items in this project.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectInstance.ItemTypes">
      <summary>Gets all item types in this project.</summary>
      <returns>Returns all item types in this project.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectInstance.Properties">
      <summary>Gets all properties in this project.</summary>
      <returns>Returns all properties in this project.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.RemoveItem(Microsoft.Build.Execution.ProjectItemInstance)">
      <summary>Removes an item from the project, if present. Returns true if it was present, false otherwise.</summary>
      <returns>Returns true if the item was present before removal; false otherwise.</returns>
      <param name="item">The item to be removed.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.RemoveProperty(System.String)">
      <summary>Removes a property with the given name. Returns true if the property had a value (possibly empty string), otherwise false.</summary>
      <returns>Returns true if the property had a value (possibly an empty string) before removal; false otherwise.</returns>
      <param name="name">The name of the property to be removed.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.SetProperty(System.String,System.String)">
      <summary>Add a property with the specified name and value to the project. Overwrites any property with the same name already in the property collection.</summary>
      <returns>Returns the new property.</returns>
      <param name="name">The name of the property to add.</param>
      <param name="evaluatedValue">The evaluated value of the property to add.</param>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectInstance.Targets">
      <summary>Gets an enumerator over all targets in this project. This collection is read-only.</summary>
      <returns>Returns an enumerator over all targets in this project.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectInstance.ToolsVersion">
      <summary>Gets the tools version this project was evaluated with, if any.</summary>
      <returns>Returns the tools version this project was evaluated with, if any.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectInstance.ToProjectRootElement">
      <summary>Creates a ProjectRootElement from the contents of this ProjectInstance.</summary>
      <returns>Returns a ProjectRootElement which represents this instance.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectItemDefinitionInstance">
      <summary>Represents an evaluated item definition for a particular item type, divested of all references to project source code.</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemDefinitionInstance.GetMetadata(System.String)">
      <summary>Get any metadata in this item definition with the given name.</summary>
      <returns>Returns any metadata in this item definition with the given name. Returns null if no metadata exists with this name.</returns>
      <param name="name">The name of the metadata to retrieve.</param>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemDefinitionInstance.ItemType">
      <summary>Gets the item type of this item definition.</summary>
      <returns>Returns the item type of this item definition.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemDefinitionInstance.Metadata">
      <summary>Gets all the metadata for this item definition. This is a read-only collection.</summary>
      <returns>Returns all the metadata for this item definition. If there is no metadata, returns empty collection.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemDefinitionInstance.MetadataCount">
      <summary>Gets the number of pieces of metadata on this item definition.</summary>
      <returns>Returns the number of pieces of metadata on this item definition.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemDefinitionInstance.MetadataNames">
      <summary>Gets an enumerator over the metadata names for this item definition. This is a read-only collection.</summary>
      <returns>Returns an enumerator over the metadata names for this item definition.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectItemGroupTaskInstance">
      <summary>Wraps an unevaluated itemgroup under a target. Immutable.</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemGroupTaskInstance.Condition">
      <summary>Gets or sets the Condition attribute of this project element.</summary>
      <returns>Returns the Condition attribute value. Returns an empty string if the attribute is not present. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemGroupTaskInstance.Items">
      <summary>Gets all child items of this item definition.</summary>
      <returns>Returns all child items of this item definition.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectItemGroupTaskItemInstance">
      <summary>Wraps an unevaluated item under an itemgroup in a target. Immutable.</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemGroupTaskItemInstance.Condition">
      <summary>Gets the unevaluated Condition attribute value.</summary>
      <returns>Returns the unevaluated Condition attribute value.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemGroupTaskItemInstance.Exclude">
      <summary>Gets the unevaluated Exclude attribute value.</summary>
      <returns>Returns the unevaluated Exclude attribute value.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemGroupTaskItemInstance.Include">
      <summary>Gets the unevaluated Include attribute value.</summary>
      <returns>Returns the unevaluated Include attribute value.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemGroupTaskItemInstance.ItemType">
      <summary>Get the item type, for example "Compile".</summary>
      <returns>Returns the item type, for example "Compile".</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemGroupTaskItemInstance.Metadata">
      <summary>Gets an ordered collection of unevaluated metadata on the item. </summary>
      <returns>Returns an ordered collection of unevaluated metadata on the item. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemGroupTaskItemInstance.Remove">
      <summary>Gets the unevaluated Remove attribute value.</summary>
      <returns>Returns the unevaluated Remove attribute value.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectItemGroupTaskMetadataInstance">
      <summary>Wraps an unevaluated metadata under an item in an item group in a target. Immutable.</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemGroupTaskMetadataInstance.Condition">
      <summary>Gets the unevaluated Condition attribute value.</summary>
      <returns>Returns the unevaluated Condition attribute value.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemGroupTaskMetadataInstance.Name">
      <summary>Gets the name of this metadata.</summary>
      <returns>Returns the name of this metadata.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemGroupTaskMetadataInstance.Value">
      <summary>Gets the unevaluated value of this metadata.</summary>
      <returns>Returns the unevaluated value of this metadata.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectItemInstance">
      <summary>Wraps an evaluated item for build purposes.</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemInstance.DirectMetadataCount">
      <summary>Gets the number of pieces of metadata on this item.</summary>
      <returns>Returns the number of pieces of metadata on this item.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemInstance.EvaluatedInclude">
      <summary>Gets the evaluated Include attribute value. </summary>
      <returns>Returns the evaluated Include attribute value, which may be empty string.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemInstance.GetMetadata(System.String)">
      <summary>Get the metadata with the given name, including metadata inherited from item definitions. </summary>
      <returns>Returns the metadata with the given name. Returns null if no metadata exists with that name. </returns>
      <param name="name">The name of the metadata.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemInstance.GetMetadataValue(System.String)">
      <summary>Gets the value of the metadata with the given name, including metadata inherited from item definitions. </summary>
      <returns>Gets the value of the metadata with the given name, including metadata inherited from item definitions. Returns an empty string if no metadata exists with that name.</returns>
      <param name="name">The name of the metadata.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemInstance.HasMetadata(System.String)">
      <summary>Determines whether the metadata with the given name is defined on this item (even if its value is empty string). </summary>
      <returns>Returns true if the metadata with the given name is defined on this item (even if its value is empty string); false otherwise.</returns>
      <param name="name">The name of the metadata.</param>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemInstance.ItemType">
      <summary>Gets the item type, for example "Compile".</summary>
      <returns>Returns the item type, for example "Compile".</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemInstance.Metadata">
      <summary>Gets an enumerator over the evaluated metadata of the item. This is a read-only collection. </summary>
      <returns>Returns an enumerator over the evaluated metadata of the item. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemInstance.MetadataCount">
      <summary>Gets the number of metadata entries, including metadata inherited from item definitions. </summary>
      <returns>Returns the number of metadata entries, including metadata inherited from item definitions</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemInstance.MetadataNames">
      <summary>Gets all metadata names of this item, including metadata inherited from item definitions. </summary>
      <returns>Returns all metadata names of this item, including metadata inherited from item definitions. </returns>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemInstance.Microsoft#Build#Framework#ITaskItem#CloneCustomMetadata">
      <summary>ITaskItem implementation. Gets the collection of custom metadata.</summary>
      <returns>The collection of custom metadata.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemInstance.Microsoft#Build#Framework#ITaskItem#CopyMetadataTo(Microsoft.Build.Framework.ITaskItem)">
      <summary>ITaskItem implementation. Copies the custom metadata entries to another item.</summary>
      <param name="destinationItem">The item to copy the metadata entries to.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemInstance.Microsoft#Build#Framework#ITaskItem#GetMetadata(System.String)">
      <summary>ITaskItem implementation. Gets the value of the specified metadata entry.</summary>
      <returns>The value of the <paramref name="attributeName" /> metadata.</returns>
      <param name="metadataName">The name of the metadata entry.</param>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemInstance.Microsoft#Build#Framework#ITaskItem#ItemSpec">
      <summary>ITaskItem implementation. Gets or sets the item specification.</summary>
      <returns>The item specification.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemInstance.Microsoft#Build#Framework#ITaskItem#MetadataNames">
      <summary>ITaskItem implementation. Gets the names of the metadata entries associated with the item.</summary>
      <returns>The names of the metadata entries associated with the item.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemInstance.Microsoft#Build#Framework#ITaskItem#SetMetadata(System.String,System.String)">
      <summary>ITaskItem implementation. Adds or changes a custom metadata entry to the item.</summary>
      <param name="metadataName">Metadata name to be added or changed.</param>
      <param name="metadataValue">Metadata value to be added or changed.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemInstance.Microsoft#Build#Framework#ITaskItem2#CloneCustomMetadataEscaped">
      <summary>ITaskItem2 implementation. Returns a clone of the metadata on this object.  Values returned are in their original escaped form.</summary>
      <returns>Returns the cloned metadata, with values' escaping preserved.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemInstance.Microsoft#Build#Framework#ITaskItem2#EvaluatedIncludeEscaped">
      <summary>ITaskItem2 implementation. Evaluated include value, escaped as necessary. May be empty string. Gets or sets the item include value e.g. for disk-based items this would be the file path.</summary>
      <returns>Returns the item include value.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemInstance.Microsoft#Build#Framework#ITaskItem2#GetMetadataValueEscaped(System.String)">
      <summary>ITaskItem2 implementation. Allows the values of metadata on the item to be queried.</summary>
      <returns>Returns the escaped metadata value.</returns>
      <param name="name">The metadata to be queried.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemInstance.Microsoft#Build#Framework#ITaskItem2#SetMetadataValueLiteral(System.String,System.String)">
      <summary>ITaskItem2 implementation. Allows a piece of custom metadata to be set on the item. Assumes that the value passed in is unescaped, and escapes the value as necessary in order to maintain its value.</summary>
      <param name="metadataName">The metadata name.</param>
      <param name="metadataValue">The metadata value.</param>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectItemInstance.Project">
      <summary>Gets the owning project.</summary>
      <returns>Returns the owning project.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemInstance.RemoveMetadata(System.String)">
      <summary>Removes metadata with the given name. </summary>
      <param name="metadataName">The metadata name.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemInstance.SetMetadata(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>Adds metadata with the given names and values. Overwrites any metadata with the same name already in the collection.</summary>
      <param name="metadataDictionary">The metadata to add.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemInstance.SetMetadata(System.String,System.String)">
      <summary>Add metadata with the given name and value. Overwrites any metadata with the same name already in the collection.</summary>
      <returns>Returns the new metadata.</returns>
      <param name="name">The name of the metadata to add.</param>
      <param name="evaluatedValue">The evaluated value of the metadata to add.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectItemInstance.ToString">
      <summary>Gets a String that represents the current item.</summary>
      <returns>Returns a String that represents the current item.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectMetadataInstance">
      <summary>Wraps an evaluated metadata for build purposes.</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectMetadataInstance.DeepClone">
      <summary>Copies the metadata to create a deep clone.</summary>
      <returns>Returns the new metadata.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectMetadataInstance.EvaluatedValue">
      <summary>Gets or sets the evaluated value of the metadata. </summary>
      <returns>Returns the evaluated value of the metadata, which is never null.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectMetadataInstance.Name">
      <summary>Gets the name of the metadata.</summary>
      <returns>Returns the name of the metadata.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectMetadataInstance.System#IEquatable{T}#Equals(Microsoft.Build.Execution.ProjectMetadataInstance)">
      <summary>Compares this project metadata with the given project metadata for equality.</summary>
      <returns>Returns true if the project metadata are equal; false otherwise.</returns>
      <param name="other">The project metadata to be compared to.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectMetadataInstance.ToString">
      <summary>Gets a string representation of this metadata.</summary>
      <returns>Returns a string representation of this metadata.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectOnErrorInstance">
      <summary>Wraps an OnError Element (MSBuild).</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectOnErrorInstance.Condition">
      <summary>Gets the unevaluated Condition attribute value of this OnError element.</summary>
      <returns>Returns the unevaluated Condition attribute value. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectOnErrorInstance.ExecuteTargets">
      <summary>Gets the unevaluated ExecuteTargets attribute value of this OnError element. </summary>
      <returns>Returns the unevaluated ExecuteTargets attribute value, which may be an empty string.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectPropertyGroupTaskInstance">
      <summary>Wraps an unevaluated propertygroup under a target. Immutable.</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectPropertyGroupTaskInstance.Condition">
      <summary>Gets or sets the Condition attribute of this property group.</summary>
      <returns>Returns the Condition attribute value. Returns an empty string if the attribute is not present. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectPropertyGroupTaskInstance.Properties">
      <summary>Gets all child properties in this property group.</summary>
      <returns>Returns all child properties in this property group.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectPropertyGroupTaskPropertyInstance">
      <summary>Wraps an unevaluated property of a property group in a target. Immutable.</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectPropertyGroupTaskPropertyInstance.Condition">
      <summary>Gets or sets the unevaluated Condition attribute of this property.</summary>
      <returns>Returns the Condition attribute value. Returns an empty string if the attribute is not present. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectPropertyGroupTaskPropertyInstance.Name">
      <summary>Gets the name of this property.</summary>
      <returns>Returns the name of this property.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectPropertyGroupTaskPropertyInstance.Value">
      <summary>Gets the unevaluated value of this property.</summary>
      <returns>Returns the unevaluated value of this property.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectPropertyInstance">
      <summary>Wraps an evaluated property for build purposes. Instances of these properties are added and removed via methods on the ProjectInstance object.</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectPropertyInstance.EvaluatedValue">
      <summary>Gets or sets the evaluated value of this property. </summary>
      <returns>Returns the evaluated value of this property.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectPropertyInstance.Name">
      <summary>Gets the name of this property.</summary>
      <returns>Returns the name of this property.</returns>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectPropertyInstance.System#IEquatable{T}#Equals(Microsoft.Build.Execution.ProjectPropertyInstance)">
      <summary>Compares this project property with the given project property for equality.</summary>
      <returns>Returns true if the project property are equal; false otherwise.</returns>
      <param name="other">The project property to be compared to.</param>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectPropertyInstance.ToString">
      <summary>Gets a string representation for this property.</summary>
      <returns>Returns a string representation for this property.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectTargetInstance">
      <summary>Wraps a Target Element (MSBuild).</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTargetInstance.Children">
      <summary>Gets a list of the children of this target. The build iterates through this to get each task to execute. </summary>
      <returns>Returns a list of the children of this target.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTargetInstance.Condition">
      <summary>Gets the unevaluated Condition attribute of this target element.</summary>
      <returns>Returns the Condition attribute value. Returns an empty string if the attribute is not present. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTargetInstance.DependsOnTargets">
      <summary>Gets an unevaluated semicolon-delimited list of targets that this target depends on. </summary>
      <returns>Returns an unevaluated semicolon-delimited list of targets that this target depends on. Returns an empty string if this target depends on no other targets.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTargetInstance.FullPath">
      <summary>Gets the full path to the file from which this target originated. </summary>
      <returns>Returns the full path to the file from which this target originated. Returns an empty string if this target originated in a project that was not loaded and has never been given a path.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTargetInstance.Inputs">
      <summary>Gets the unevaluated inputs on this target element. </summary>
      <returns>Returns the unevaluated inputs on this target element. Returns an empty string if this target has no inputs.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTargetInstance.KeepDuplicateOutputs">
      <summary>Gets an unevaluated condition which is used to delete duplicate outputs from this target.</summary>
      <returns>Returns an unevaluated condition which is used to delete duplicate outputs from this target. Returns an empty string if there is no condition.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTargetInstance.Name">
      <summary>Gets the name of the target.</summary>
      <returns>Returns the name of the target.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTargetInstance.OnErrorChildren">
      <summary>Gets a list of the children of this target that refer to OnError targets. </summary>
      <returns>Gets a list of the children of this target that refer to OnError targets. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTargetInstance.Outputs">
      <summary>Gets the unevaluated outputs on this target element May be empty string.</summary>
      <returns>Returns the unevaluated outputs on this target element Returns an empty string if there are no outputs.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTargetInstance.Returns">
      <summary>Gets the unevaluated return values on the target element as a string.</summary>
      <returns>Returns the unevaluated return values on the target element as a string. Returns an empty string or null, if no return value is specified.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTargetInstance.Tasks">
      <summary>Gets all the tasks that are immediate children of this target.</summary>
      <returns>Returns all the tasks that are immediate children of this target.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectTargetInstanceChild">
      <summary>Abstracts the <see cref="T:Microsoft.Build.Execution.ProjectTaskInstance" />, <see cref="T:Microsoft.Build.Execution.ProjectPropertyGroupTaskInstance" /> and <see cref="T:Microsoft.Build.Execution.ProjectItemGroupTaskInstance" /> classes. This allows these types to be used in a single collection of target children</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectTargetInstanceChild.#ctor">
      <summary>Abstract classes cannot be instantiated.</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTargetInstanceChild.Condition">
      <summary>Gets or sets the Condition attribute of this target.</summary>
      <returns>Returns the Condition attribute value. Returns an empty string if the attribute is not present. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTargetInstanceChild.FullPath">
      <summary>Gets the full path to the file from which this task originated. </summary>
      <returns>Returns the full path to the file from which this task originated. Returns an empty string if the task originated in a project that was not loaded and has never been given a path.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectTaskInstance">
      <summary>Wraps a Task Element (MSBuild).</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTaskInstance.Condition">
      <summary>Gets the unevaluated Condition attribute of this task.</summary>
      <returns>Returns the Condition attribute value. Returns an empty string if the attribute is not present. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTaskInstance.ContinueOnError">
      <summary>Gets the unevaluated ContinueOnError attribute of the task. </summary>
      <returns>Returns the unevaluated ContinueOnError attribute of the task. Returns an empty string if the attribute is not present.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTaskInstance.Name">
      <summary>Gets the name of the task, possibly qualified, as it appears in the project.</summary>
      <returns>Returns the name of the task, possibly qualified, as it appears in the project.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTaskInstance.Outputs">
      <summary>Gets an ordered set of the output property and item objects of this task. </summary>
      <returns>Returns an ordered set of the output property and item objects of the task. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTaskInstance.Parameters">
      <summary>Gets an unordered set of the task parameter names and unevaluated values of this task. </summary>
      <returns>Returns an unordered set of the task parameter names and unevaluated values of this task. </returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectTaskInstanceChild">
      <summary>Abstracts the base class for the TaskOutputItem and TaskOutputProperty classes. This allows them to be used in a single collection.</summary>
    </member>
    <member name="M:Microsoft.Build.Execution.ProjectTaskInstanceChild.#ctor">
      <summary>Abstract classes cannot be instantiated.</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTaskInstanceChild.Condition">
      <summary>Gets the Condition attribute of this task.</summary>
      <returns>Returns the Condition attribute value. Returns an empty string if the attribute is not present. </returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectTaskOutputItemInstance">
      <summary>Wraps an output item element of a Task Element (MSBuild).</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTaskOutputItemInstance.Condition">
      <summary>Gets the Condition attribute of this project element.</summary>
      <returns>Returns the Condition attribute value. Returns an empty string if the attribute is not present. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTaskOutputItemInstance.ItemType">
      <summary>Gets the item type that the outputs go into.</summary>
      <returns>Returns the item type that the outputs go into.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTaskOutputItemInstance.TaskParameter">
      <summary>Gets the property of this class to retrieve the outputs from.</summary>
      <returns>Returns the property of this class to retrieve the outputs from.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.ProjectTaskOutputPropertyInstance">
      <summary>Represents an output property element of a Task Element (MSBuild).</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTaskOutputPropertyInstance.Condition">
      <summary>Gets the Condition attribute of the output element.</summary>
      <returns>Returns the Condition attribute of the output element.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTaskOutputPropertyInstance.PropertyName">
      <summary>Gets the name of the property that receives the output.</summary>
      <returns>Returns the name of the property that receives the output.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.ProjectTaskOutputPropertyInstance.TaskParameter">
      <summary>Gets the property of the task class to retrieve the output from.</summary>
      <returns>Returns the property of the task class to retrieve the output from.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.TargetResult">
      <summary>Represents both the result code for building a single target and the overall build result.</summary>
    </member>
    <member name="P:Microsoft.Build.Execution.TargetResult.Exception">
      <summary>Gets the exception which aborted this target, if any.</summary>
      <returns>Returns the exception which aborted this target, if any.</returns>
    </member>
    <member name="P:Microsoft.Build.Execution.TargetResult.Items">
      <summary>Gets the items produced by this target. </summary>
      <returns>Returns the items produced by this target. </returns>
    </member>
    <member name="P:Microsoft.Build.Execution.TargetResult.ResultCode">
      <summary>Gets the result code for building this target.</summary>
      <returns>Returns the result code for building this target.</returns>
    </member>
    <member name="T:Microsoft.Build.Execution.TargetResultCode">
      <summary>The result code for a given target.</summary>
    </member>
    <member name="F:Microsoft.Build.Execution.TargetResultCode.Skipped">
      <summary>The target was skipped because its condition was not met.</summary>
    </member>
    <member name="F:Microsoft.Build.Execution.TargetResultCode.Success">
      <summary>The target successfully built.</summary>
    </member>
    <member name="F:Microsoft.Build.Execution.TargetResultCode.Failure">
      <summary>The target failed to build.</summary>
    </member>
    <member name="T:Microsoft.Build.Logging.ColorResetter">
      <summary>Type of delegate used to reset console color.</summary>
    </member>
    <member name="T:Microsoft.Build.Logging.ColorSetter">
      <summary>Type of delegate used to set console color.</summary>
      <param name="color">Text color.</param>
    </member>
    <member name="T:Microsoft.Build.Logging.ConfigurableForwardingLogger">
      <summary>Represents a logger that forwards events to a central logger (e.g ConsoleLogger) residing on the parent node.</summary>
    </member>
    <member name="M:Microsoft.Build.Logging.ConfigurableForwardingLogger.#ctor">
      <summary>Constructs a logger.</summary>
    </member>
    <member name="P:Microsoft.Build.Logging.ConfigurableForwardingLogger.BuildEventRedirector">
      <summary>Gets or sets a property that determines how a node logger to forwards messages to the central logger.</summary>
      <returns>Returns a property that determines how a node logger to forwards messages to the central logger.</returns>
    </member>
    <member name="M:Microsoft.Build.Logging.ConfigurableForwardingLogger.ForwardToCentralLogger(Microsoft.Build.Framework.BuildEventArgs)">
      <summary>Forward this event to the central logger.</summary>
      <param name="e">The event to be forwarded.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConfigurableForwardingLogger.Initialize(Microsoft.Build.Framework.IEventSource)">
      <summary>Signs up the console logger to log the given build events.</summary>
      <param name="eventSource">The event source for build events.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConfigurableForwardingLogger.Initialize(Microsoft.Build.Framework.IEventSource,System.Int32)">
      <summary>Signs up the console logger for all build events.</summary>
      <param name="eventSource">The event source for build events.</param>
      <param name="nodeCount">The node count.  Not used.</param>
    </member>
    <member name="P:Microsoft.Build.Logging.ConfigurableForwardingLogger.NodeId">
      <summary>Gets or sets the ID of the node.</summary>
      <returns>Returns the ID of the node.</returns>
    </member>
    <member name="P:Microsoft.Build.Logging.ConfigurableForwardingLogger.Parameters">
      <summary>Gets or sets a switch that determines whether to suppress the output of the errors and warnings summary at the end of a build.</summary>
      <returns>Returns a switch that determines whether to suppress the output of the errors and warnings summary at the end of a build.</returns>
    </member>
    <member name="M:Microsoft.Build.Logging.ConfigurableForwardingLogger.Shutdown">
      <summary>Called when the build engine is finished with this logger.</summary>
    </member>
    <member name="P:Microsoft.Build.Logging.ConfigurableForwardingLogger.Verbosity">
      <summary>Gets or sets the level of detail to show in the event log.</summary>
      <returns>Returns the level of detail to show in the event log.</returns>
    </member>
    <member name="T:Microsoft.Build.Logging.ConsoleLogger">
      <summary>Represents the default logger that outputs event data to the console (stdout).</summary>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.#ctor">
      <summary>Creates a default logger.</summary>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.#ctor(Microsoft.Build.Framework.LoggerVerbosity)">
      <summary>Creates a logger instance with the given verbosity that logs to the default console.</summary>
      <param name="verbosity">The verbosity level.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.#ctor(Microsoft.Build.Framework.LoggerVerbosity,Microsoft.Build.Logging.WriteHandler,Microsoft.Build.Logging.ColorSetter,Microsoft.Build.Logging.ColorResetter)">
      <summary>Initializes the logger with alternate output handlers.</summary>
      <param name="verbosity">The verbosity level.</param>
      <param name="write">The write handler.</param>
      <param name="colorSet">Sets the text color.</param>
      <param name="colorReset">Resets the text color.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.ApplyParameter(System.String,System.String)">
      <summary>Applies a parameter to the logger. </summary>
      <param name="parameterName">The name of the parameter.</param>
      <param name="parameterValue">The value of the parameter.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.BuildFinishedHandler(System.Object,Microsoft.Build.Framework.BuildFinishedEventArgs)">
      <summary>Called when the build is finished.</summary>
      <param name="sender">The sender (should be null).</param>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.BuildStartedHandler(System.Object,Microsoft.Build.Framework.BuildStartedEventArgs)">
      <summary>Called when the build is started.</summary>
      <param name="sender">The sender (should be null).</param>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.CustomEventHandler(System.Object,Microsoft.Build.Framework.CustomBuildEventArgs)">
      <summary>Called to log a custom event.</summary>
      <param name="sender">The sender of the event.</param>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.ErrorHandler(System.Object,Microsoft.Build.Framework.BuildErrorEventArgs)">
      <summary>Called to log an error event.</summary>
      <param name="sender">The sender of the event.</param>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.Initialize(Microsoft.Build.Framework.IEventSource)">
      <summary>Signs up the console logger for the given build event.</summary>
      <param name="eventSource">The available events.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.Initialize(Microsoft.Build.Framework.IEventSource,System.Int32)">
      <summary>Initializes the logger with the given event source and node count.</summary>
      <param name="eventSource">The event source.</param>
      <param name="nodeCount">The node count.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.MessageHandler(System.Object,Microsoft.Build.Framework.BuildMessageEventArgs)">
      <summary>Called to log a message event.</summary>
      <param name="sender">The sender of the event.</param>
      <param name="e">The event arguments.</param>
    </member>
    <member name="P:Microsoft.Build.Logging.ConsoleLogger.Parameters">
      <summary>Gets or sets a semicolon-delimited list of parameter key-value pairs.</summary>
      <returns>Returns a semicolon-delimited list of parameter key-value pairs.</returns>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.ProjectFinishedHandler(System.Object,Microsoft.Build.Framework.ProjectFinishedEventArgs)">
      <summary>Called to log a project finished event.</summary>
      <param name="sender">The sender (should be null).</param>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.ProjectStartedHandler(System.Object,Microsoft.Build.Framework.ProjectStartedEventArgs)">
      <summary>Called to log a project started event.</summary>
      <param name="sender">The sender (should be null).</param>
      <param name="e">The event arguments.</param>
    </member>
    <member name="P:Microsoft.Build.Logging.ConsoleLogger.ShowSummary">
      <summary>Gets or sets a switch that determines whether to suppress the display of errors and the warnings summary.</summary>
      <returns>Returns a switch that determines whether to suppress the display of errors and the warnings summary.</returns>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.Shutdown">
      <summary>Called when the build is finished.</summary>
    </member>
    <member name="P:Microsoft.Build.Logging.ConsoleLogger.SkipProjectStartedText">
      <summary>Gets or sets a switch that determines whether to suppress the display of project headers. </summary>
      <returns>Returns a switch that determines whether to suppress the display of project headers. </returns>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.TargetFinishedHandler(System.Object,Microsoft.Build.Framework.TargetFinishedEventArgs)">
      <summary>Called to log a target finished event.</summary>
      <param name="sender">The sender (should be null).</param>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.TargetStartedHandler(System.Object,Microsoft.Build.Framework.TargetStartedEventArgs)">
      <summary>Called to log a target started event.</summary>
      <param name="sender">The sender (should be null).</param>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.TaskFinishedHandler(System.Object,Microsoft.Build.Framework.TaskFinishedEventArgs)">
      <summary>Called to log a task finished event.</summary>
      <param name="sender">The sender (should be null).</param>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.TaskStartedHandler(System.Object,Microsoft.Build.Framework.TaskStartedEventArgs)">
      <summary>Called to log a task started event.</summary>
      <param name="sender">The sender (should be null).</param>
      <param name="e">The event arguments.</param>
    </member>
    <member name="P:Microsoft.Build.Logging.ConsoleLogger.Verbosity">
      <summary>Gets or sets the level of detail to show in the event log.</summary>
      <returns>Returns the level of detail to show in the event log.</returns>
    </member>
    <member name="M:Microsoft.Build.Logging.ConsoleLogger.WarningHandler(System.Object,Microsoft.Build.Framework.BuildWarningEventArgs)">
      <summary>Called to log a warning event.</summary>
      <param name="sender">The sender of the event.</param>
      <param name="e">The event arguments.</param>
    </member>
    <member name="P:Microsoft.Build.Logging.ConsoleLogger.WriteHandler">
      <summary>Provides access to the write handler delegate so that it can be redirected if necessary (e.g. to a file).</summary>
      <returns>Returns the write handler delegate.</returns>
    </member>
    <member name="T:Microsoft.Build.Logging.DistributedFileLogger">
      <summary>This class creates a text file which will contain the build log for a node.</summary>
    </member>
    <member name="M:Microsoft.Build.Logging.DistributedFileLogger.#ctor">
      <summary>Creates a file logger.</summary>
    </member>
    <member name="P:Microsoft.Build.Logging.DistributedFileLogger.BuildEventRedirector">
      <summary>Gets or sets a property that determines how a node logger to forwards messages to the central logger.</summary>
      <returns>Returns a property that determines how a node logger to forwards messages to the central logger.</returns>
    </member>
    <member name="M:Microsoft.Build.Logging.DistributedFileLogger.Initialize(Microsoft.Build.Framework.IEventSource)">
      <summary>Initializes the logger with the given event source.</summary>
      <param name="eventSource">The event source.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.DistributedFileLogger.Initialize(Microsoft.Build.Framework.IEventSource,System.Int32)">
      <summary>Initializes the logger with the given event source and node count.</summary>
      <param name="eventSource">The event source.</param>
      <param name="nodeCount">The node count.</param>
    </member>
    <member name="P:Microsoft.Build.Logging.DistributedFileLogger.NodeId">
      <summary>Gets or sets the ID of the node.</summary>
      <returns>Returns the ID of the node.</returns>
    </member>
    <member name="P:Microsoft.Build.Logging.DistributedFileLogger.Parameters">
      <summary>Gets or sets a switch that determines whether to suppress the output of the errors and warnings summary at the end of a build.</summary>
      <returns>Returns a switch that determines whether to suppress the output of the errors and warnings summary at the end of a build.</returns>
    </member>
    <member name="M:Microsoft.Build.Logging.DistributedFileLogger.Shutdown">
      <summary>Called when the build engine is finished with this logger.</summary>
    </member>
    <member name="P:Microsoft.Build.Logging.DistributedFileLogger.Verbosity">
      <summary>Gets or sets the level of detail to show in the event log.</summary>
      <returns>Returns the level of detail to show in the event log.</returns>
    </member>
    <member name="T:Microsoft.Build.Logging.FileLogger">
      <summary>A specialization of the ConsoleLogger that logs to a file instead of the console. </summary>
    </member>
    <member name="M:Microsoft.Build.Logging.FileLogger.#ctor">
      <summary>Creates a file logger.</summary>
    </member>
    <member name="M:Microsoft.Build.Logging.FileLogger.Initialize(Microsoft.Build.Framework.IEventSource)">
      <summary>Signs up the console file logger for the given build events. </summary>
      <param name="eventSource">The available events.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.FileLogger.Initialize(Microsoft.Build.Framework.IEventSource,System.Int32)">
      <summary>Initializes a file logger in a multiprocessor environment.</summary>
      <param name="eventSource">The event source.</param>
      <param name="nodeCount">The node count.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.FileLogger.Shutdown">
      <summary>Called when the build is finished.</summary>
    </member>
    <member name="T:Microsoft.Build.Logging.ForwardingLoggerRecord">
      <summary>Represents a central/forwarding logger pair used in multiprocessor logging.</summary>
    </member>
    <member name="M:Microsoft.Build.Logging.ForwardingLoggerRecord.#ctor(Microsoft.Build.Framework.ILogger,Microsoft.Build.Logging.LoggerDescription)">
      <summary>Creates a forwarding logger.</summary>
      <param name="centralLogger">The central logger.</param>
      <param name="forwardingLoggerDescription">The description for the forwarding logger.</param>
    </member>
    <member name="P:Microsoft.Build.Logging.ForwardingLoggerRecord.CentralLogger">
      <summary>Gets the central logger.</summary>
      <returns>Returns the central logger.</returns>
    </member>
    <member name="P:Microsoft.Build.Logging.ForwardingLoggerRecord.ForwardingLoggerDescription">
      <summary>Gets the forwarding logger description.</summary>
      <returns>Returns the forwarding logger description.</returns>
    </member>
    <member name="T:Microsoft.Build.Logging.LoggerDescription">
      <summary>Contains information about a logger as a collection of values that can be used to instantiate the logger and can be serialized to be passed between different processes.</summary>
    </member>
    <member name="M:Microsoft.Build.Logging.LoggerDescription.#ctor(System.String,System.String,System.String,System.String,Microsoft.Build.Framework.LoggerVerbosity)">
      <summary>Creates a logger description from the given data.</summary>
      <param name="loggerClassName">The class name of the logger.</param>
      <param name="loggerAssemblyName">The assembly name that implements the logger.</param>
      <param name="loggerAssemblyFile">The assembly file that implements the logger.</param>
      <param name="loggerSwitchParameters">Switch parameters for the logger.</param>
      <param name="verbosity">The verbosity level.</param>
    </member>
    <member name="M:Microsoft.Build.Logging.LoggerDescription.CreateLogger">
      <summary>Creates an ILogger instance from the data in this description. </summary>
      <returns>Returns an ILogger instance from the data in this description. </returns>
    </member>
    <member name="P:Microsoft.Build.Logging.LoggerDescription.LoggerSwitchParameters">
      <summary>Gets the string of logger parameters.</summary>
      <returns>Returns the string of logger parameters.  Returns null if there are no parameters.</returns>
    </member>
    <member name="P:Microsoft.Build.Logging.LoggerDescription.Verbosity">
      <summary>Returns the verbosity level for this logger. </summary>
    </member>
    <member name="T:Microsoft.Build.Logging.WriteHandler">
      <summary>Delegate used for writing a string to some location like the console window or the IDE build window.</summary>
      <param name="message">The message to write.</param>
    </member>
  </members>
</doc>