﻿using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using MetroFramework.Native;
using OCRTools;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.Security;
using System.Windows.Forms;
using System.Linq;
using OCRTools.Properties;

namespace MetroFramework.Forms
{
    public class MetroForm : Form, IMetroForm
    {
        private bool _displayHeader = true;

        private MetroColorStyle _metroStyle = MetroColorStyle.蓝色;

        private MetroThemeStyle _metroTheme = MetroThemeStyle.Light;

        private Form _shadowForm;
        private MetroFormShadowType _shadowType = MetroFormShadowType.Flat;

        private Dictionary<WindowButtons, List<Control>> _windowButtonList = new Dictionary<WindowButtons, List<Control>>();

        public MetroForm()
        {
            SetStyle(
                ControlStyles.UserPaint |
                ControlStyles.AllPaintingInWmPaint |
                ControlStyles.ResizeRedraw |
                ControlStyles.DoubleBuffer |
                ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.ContainerControl |
                ControlStyles.SupportsTransparentBackColor, true);

            FormBorderStyle = FormBorderStyle.None;
            Name = "MetroForm";
            StartPosition = FormStartPosition.CenterScreen;
            TransparencyKey = Color.Lavender;
        }

        public virtual void OnThemeChange()
        {

        }

        [Category("Metro Appearance")]
        [Browsable(true)]
        public MetroFormTextAlign TextAlign { get; set; }

        [Browsable(false)] public override Color BackColor => MetroPaint.BackColor.Form(Theme);

        private const int borderWidth = 5;

        [Category("Metro Appearance")] public bool Movable { get; set; } = true;

        public event EventHandler TopMostChangedEvent;

        public new bool TopMost
        {
            get => base.TopMost;
            set
            {
                base.TopMost = value;
                TopMostChangedEvent?.Invoke(null, null);
            }
        }

        public new Padding Padding
        {
            get => base.Padding;
            set
            {
                value.Top = Math.Max(value.Top, DisplayHeader ? 60 : 20);
                base.Padding = value;
            }
        }

        protected override Padding DefaultPadding => new Padding(20, DisplayHeader ? 60 : 20, 20, 20);

        [Category("Metro Appearance")]
        [DefaultValue(true)]
        public bool DisplayHeader
        {
            get => _displayHeader;
            set
            {
                if (value != _displayHeader)
                {
                    var padding = base.Padding;
                    padding.Top += value ? 40 : -40;
                    base.Padding = padding;
                }

                _displayHeader = value;
            }
        }

        [Category("Metro Appearance")] public bool Resizable { get; set; } = true;

        [DefaultValue(MetroFormShadowType.Flat)]
        [Category("Metro Appearance")]
        public MetroFormShadowType ShadowType
        {
            get
            {
                if (!IsMdiChild) return _shadowType;
                return MetroFormShadowType.None;
            }
            set => _shadowType = value;
        }

        [Browsable(false)]
        public new FormBorderStyle FormBorderStyle
        {
            get => base.FormBorderStyle;
            set => base.FormBorderStyle = value;
        }

        protected override CreateParams CreateParams
        {
            get
            {
                var createParams = base.CreateParams;
                createParams.Style |= 131072;
                if (ShadowType == MetroFormShadowType.SystemShadow) createParams.ClassStyle |= 131072;
                return createParams;
            }
        }

        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (StyleManager != null) return StyleManager.Style;
                return _metroStyle;
            }
            set => _metroStyle = value;
        }

        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (StyleManager != null) return StyleManager.Theme;
                return _metroTheme;
            }
            set
            {
                _metroTheme = value;
            }
        }

        [Browsable(false)]
        public MetroStyleManager StyleManager { get; set; } = CommonTheme.StyleManager;

        protected override void Dispose(bool disposing)
        {
            if (disposing) RemoveShadow();
            base.Dispose(disposing);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            e.Graphics.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
            e.Graphics.InterpolationMode = InterpolationMode.High;
            e.Graphics.CompositingQuality = CompositingQuality.HighQuality;

            var backColor = MetroPaint.BackColor.Form(Theme);
            e.Graphics.Clear(backColor);

            using (var brush = MetroPaint.GetStyleBrush(Style))
            {
                var topRect = new Rectangle(0, 0, Width, borderWidth);
                e.Graphics.FillRectangle(brush, topRect);
            }

            //Draw Border
            var borderColor = MetroPaint.BorderColor.Form(Theme);
            using (var pen = new Pen(borderColor))
            {
                e.Graphics.DrawLines(pen, new[]
                {
                        new Point(0, borderWidth),
                        new Point(0, Height - 1),
                        new Point(Width - 1, Height - 1),
                        new Point(Width - 1, borderWidth)
                });
            }

            e.Graphics.SetHighQuality();
            if (_displayHeader && !string.IsNullOrEmpty(Text))
            {
                var foreColor = MetroPaint.ForeColor.Title(Theme);
                var bounds = new Rectangle(20, 20, ClientRectangle.Width - 40, 40);
                TextRenderer.DrawText(e.Graphics, Text, MetroFonts.Title, bounds, foreColor, TextFormatFlags.EndEllipsis | GetTextFormatFlags());
            }

            if (Resizable && (SizeGripStyle == SizeGripStyle.Auto || SizeGripStyle == SizeGripStyle.Show))
                using (var brush2 = new SolidBrush(MetroPaint.ForeColor.Button.Disabled(Theme)))
                {
                    var size = new Size(2, 2);
                    e.Graphics.FillRectangles(brush2, new[]
                    {
                        new Rectangle(new Point(ClientRectangle.Width - 6, ClientRectangle.Height - 6), size),
                        new Rectangle(new Point(ClientRectangle.Width - 10, ClientRectangle.Height - 10), size),
                        new Rectangle(new Point(ClientRectangle.Width - 10, ClientRectangle.Height - 6), size),
                        new Rectangle(new Point(ClientRectangle.Width - 6, ClientRectangle.Height - 10), size),
                        new Rectangle(new Point(ClientRectangle.Width - 14, ClientRectangle.Height - 6), size),
                        new Rectangle(new Point(ClientRectangle.Width - 6, ClientRectangle.Height - 14), size)
                    });
                }
        }

        private TextFormatFlags GetTextFormatFlags()
        {
            switch (TextAlign)
            {
                case MetroFormTextAlign.Left: return TextFormatFlags.Left;
                case MetroFormTextAlign.Center: return TextFormatFlags.HorizontalCenter;
                case MetroFormTextAlign.Right: return TextFormatFlags.Right;
            }

            return TextFormatFlags.Default;
        }

        protected override void OnClosed(EventArgs e)
        {
            if (Owner != null) Owner = null;
            RemoveShadow();
            base.OnClosed(e);
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            if (DesignMode) return;
            switch (StartPosition)
            {
                case FormStartPosition.CenterParent:
                    CenterToParent();
                    break;
                case FormStartPosition.CenterScreen:
                    if (IsMdiChild)
                        CenterToParent();
                    else
                        CenterToScreen();
                    break;
            }

            RemoveCloseButton();
            if (ControlBox)
            {
                AddWindowButton(WindowButtons.Close);
                if (MaximizeBox) AddWindowButton(WindowButtons.Maximize);
                if (MinimizeBox) AddWindowButton(WindowButtons.Minimize);
            }

            UpdateWindowButtonPosition();
            CreateShadow();
        }

        private float currentScale = 1.0f;

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        static extern int GetDpiForWindow(IntPtr hWnd);

        protected void OnDisplayChange()
        {
            if (this is FrmMain && CommonSetting.跟随系统DPI缩放)
            {
                var nowScale = GetDpiForWindow(FrmMain.FrmTool.Handle) / 96f;
                if (!Equals(currentScale, nowScale) && (Visible || Width > 450))
                {
                    InitForm(nowScale * 1f / currentScale);

                    var padding = base.Padding;
                    padding.Top = 20 + (int)(40d * nowScale);
                    base.Padding = padding;

                    currentScale = nowScale;
                    UpdateWindowButtonPosition();
                }
            }
        }

        private void InitForm(float scal)
        {
            ////Location = new Point((int)(Location.X * scal), (int)(Location.Y * scal));
            Size = new Size((int)(Size.Width * scal), (int)(Size.Height * scal));
            ////Font = new Font(Font.Name, Font.Size * scal, Font.Style, Font.Unit, Font.GdiCharSet, Font.GdiVerticalFont);
            foreach (var item in _windowButtonList.OrderBy(p => p.Key.GetHashCode()))
            {
                foreach (var ctrl in item.Value)
                {
                    ctrl.Font = new Font(ctrl.Font.Name, ctrl.Font.Size * scal, ctrl.Font.Style, ctrl.Font.Unit, ctrl.Font.GdiCharSet, ctrl.Font.GdiVerticalFont);
                    if (ctrl is Button btn && btn.AutoSize)
                    {
                        ctrl.Size = new Size(30, 30);
                        continue;
                    }
                    ctrl.Size = new Size((int)(ctrl.Size.Width * scal), (int)(ctrl.Size.Height * scal));
                }
            }
            //SetControlSize(this, scal);
        }

        protected override void OnShown(EventArgs e)
        {
            OnThemeChange();
            base.OnShown(e);
            this.AutoSizeMutilScreen();
        }

        protected override void OnActivated(EventArgs e)
        {
            base.OnActivated(e);
            if (_shadowType == MetroFormShadowType.AeroShadow && IsAeroThemeEnabled() && IsDropShadowSupported())
            {
                var attrValue = 2;
                DwmApi.DwmSetWindowAttribute(Handle, 2, ref attrValue, 4);
                var mArgins = new DwmApi.Margins
                {
                    cyBottomHeight = 1,
                    cxLeftWidth = 0,
                    cxRightWidth = 0,
                    cyTopHeight = 0
                };

                DwmApi.DwmExtendFrameIntoClientArea(Handle, ref mArgins);
            }
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }

        protected override void OnVisibleChanged(EventArgs e)
        {
            base.OnVisibleChanged(e);
            OnDisplayChange();
        }

        protected override void OnResizeEnd(EventArgs e)
        {
            base.OnResizeEnd(e);
            UpdateWindowButtonPosition();
        }

        protected override void WndProc(ref Message m)
        {
            if (DesignMode)
            {
                base.WndProc(ref m);
                return;
            }

            switch (m.Msg)
            {
                case (int)Messages.WM_SYSCOMMAND:
                    int sc = m.WParam.ToInt32() & 0xFFF0;
                    switch (sc)
                    {
                        case (int)Messages.SC_MOVE:
                            if (!Movable) return;
                            break;

                        case (int)Messages.SC_MAXIMIZE:
                            break;
                        case (int)Messages.SC_RESTORE:
                            break;
                    }
                    break;

                case (int)Messages.WM_NCLBUTTONDBLCLK:
                case (int)Messages.WM_LBUTTONDBLCLK:
                    if (!MaximizeBox) return;
                    break;
                case (int)Messages.WM_NCHITTEST:
                    WinApi.HitTest ht = HitTestNca(m.HWnd, m.WParam, m.LParam);
                    if (ht != WinApi.HitTest.HTCLIENT)
                    {
                        m.Result = (IntPtr)ht;
                        return;
                    }
                    break;
                case (int)Messages.WM_DWMCOMPOSITIONCHANGED:
                    break;
            }

            base.WndProc(ref m);
            switch (m.Msg)
            {
                case (int)Messages.WM_GETMINMAXINFO:
                    OnGetMinMaxInfo(m.HWnd, m.LParam);
                    break;
                case (int)Messages.WM_SIZE:
                    {
                        if (_shadowForm != null)
                        {
                            _shadowForm.Visible = _shadowForm != null && Visible && Opacity > 0;
                        }
                        _windowButtonList.TryGetValue(WindowButtons.Maximize, out var value);
                        if (value == null || value.Count <= 0) break;
                        value.FirstOrDefault().BackgroundImage = null;
                        break;
                    }
            }
        }

        [SecuritySafeCritical]
        private unsafe void OnGetMinMaxInfo(IntPtr hwnd, IntPtr lParam)
        {
            var ptr = (WinApi.MINMAXINFO*)(void*)lParam;
            var screen = Screen.FromHandle(hwnd);
            ptr->ptMaxSize.x = screen.WorkingArea.Width;
            ptr->ptMaxSize.y = screen.WorkingArea.Height;
            ptr->ptMaxPosition.x = Math.Abs(screen.WorkingArea.Left - screen.Bounds.Left);
            ptr->ptMaxPosition.y = Math.Abs(screen.WorkingArea.Top - screen.Bounds.Top);
        }

        private WinApi.HitTest HitTestNca(IntPtr hwnd, IntPtr wparam, IntPtr lparam)
        {
            Point vPoint = new Point((short)lparam, (short)((int)lparam >> 16));
            int vPadding = Math.Max(Padding.Right, Padding.Bottom);

            if (Resizable)
            {
                if (RectangleToScreen(new Rectangle(ClientRectangle.Width - vPadding, ClientRectangle.Height - vPadding, vPadding, vPadding)).Contains(vPoint))
                    return WinApi.HitTest.HTBOTTOMRIGHT;
            }

            if (RectangleToScreen(new Rectangle(borderWidth, borderWidth, ClientRectangle.Width - 2 * borderWidth, 50)).Contains(vPoint))
                return WinApi.HitTest.HTCAPTION;

            return WinApi.HitTest.HTCLIENT;
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            base.OnMouseDown(e);
            if (e.Button == MouseButtons.Left && Movable && WindowState != FormWindowState.Maximized &&
                Width - borderWidth > e.Location.X && e.Location.X > borderWidth && e.Location.Y > borderWidth) MoveControl();
        }

        [SecuritySafeCritical]
        private void MoveControl()
        {
            WinApi.ReleaseCapture();
            WinApi.SendMessage(Handle, (int)Messages.WM_NCLBUTTONDOWN, (int)WinApi.HitTest.HTCAPTION, 0);
        }

        [SecuritySafeCritical]
        private bool IsAeroThemeEnabled()
        {
            if (Environment.OSVersion.Version.Major <= 5) return false;
            DwmApi.DwmIsCompositionEnabled(out var pfEnabled);
            return pfEnabled;
        }

        private bool IsDropShadowSupported()
        {
            return Environment.OSVersion.Version.Major > 5 && SystemInformation.IsDropShadowEnabled;
        }

        internal Control AddWindowButton(WindowButtons button, EventHandler click = null)
        {
            var metroFormButton = new MetroFormButton() { BackgroundImageLayout = ImageLayout.Stretch };
            metroFormButton.Style = Style;
            metroFormButton.Theme = Theme;
            metroFormButton.Tag = button;
            metroFormButton.Size = button == WindowButtons.Top ? new Size(30, 25) : new Size(25, 25);
            metroFormButton.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            metroFormButton.TabStop = false;
            metroFormButton.Click += click ?? WindowButton_Click;
            Controls.Add(metroFormButton);
            _windowButtonList.Add(button, new List<Control>() { metroFormButton });
            return metroFormButton;
        }

        internal void AddCustomControl(WindowButtons button, Control ctrl)
        {
            if (!_windowButtonList.ContainsKey(button))
            {
                _windowButtonList.Add(button, new List<Control>());
            }
            var lstControl = _windowButtonList[button];
            Controls.Add(ctrl);
            lstControl.Add(ctrl);
            _windowButtonList[button] = lstControl;
        }

        private void WindowButton_Click(object sender, EventArgs e)
        {
            var metroFormButton = sender as MetroFormButton;
            if (metroFormButton == null) return;
            switch ((WindowButtons)metroFormButton.Tag)
            {
                case WindowButtons.Close:
                    Close();
                    break;
                case WindowButtons.Minimize:
                    WindowState = FormWindowState.Minimized;
                    break;
                case WindowButtons.Maximize:
                    metroFormButton.BackgroundImage = null;
                    WindowState = WindowState == FormWindowState.Normal ? FormWindowState.Maximized : FormWindowState.Normal;
                    break;
            }
        }

        public Rectangle LeftButtonRectangle { get; set; }

        internal void UpdateWindowButtonImage()
        {
            foreach (var item in _windowButtonList)
            {
                if (item.Key.Equals(WindowButtons.Custom))
                {
                    continue;
                }
                item.Value.ForEach(p => p.BackgroundImage = null);
            }
        }

        internal void UpdateWindowButtonPosition()
        {
            var firstButtonLocation = new Point(ClientRectangle.Width - borderWidth, borderWidth);
            var lastDrawedButtonPosition = firstButtonLocation.X;
            foreach (var item in _windowButtonList.OrderBy(p => p.Key.GetHashCode()))
            {
                foreach (var ctrl in item.Value)
                {
                    ctrl.Location = new Point(lastDrawedButtonPosition - ctrl.Width, borderWidth);
                    if (item.Key == WindowButtons.Menu || item.Key == WindowButtons.Dark)
                    {
                        ctrl.Top += 3;
                    }
                    else if (item.Key == WindowButtons.Top)
                    {
                        ctrl.Top += 4;
                    }
                    lastDrawedButtonPosition -= ctrl.Width;
                    LeftButtonRectangle = ctrl.Bounds;
                }
            }

            Refresh();
        }

        private void CreateShadow()
        {
            switch (ShadowType)
            {
                case MetroFormShadowType.None:
                    break;
                case MetroFormShadowType.Flat:
                    _shadowForm = new MetroFlatDropShadow(this);
                    break;
                case MetroFormShadowType.DropShadow:
                    _shadowForm = new MetroRealisticDropShadow(this);
                    break;
            }
        }

        private void RemoveShadow()
        {
            if (_shadowForm == null || _shadowForm.IsDisposed) return;

            _shadowForm.Visible = false;
            Owner = _shadowForm.Owner;
            _shadowForm.Owner = null;
            _shadowForm.Dispose();
            _shadowForm = null;
        }

        [SecuritySafeCritical]
        public void RemoveCloseButton()
        {
            var systemMenu = WinApi.GetSystemMenu(Handle, false);
            if (systemMenu == IntPtr.Zero) return;

            int menuItemCount = WinApi.GetMenuItemCount(systemMenu);
            if (menuItemCount <= 0) return;
            WinApi.RemoveMenu(systemMenu, (uint)(menuItemCount - 1), 5120u);
            WinApi.RemoveMenu(systemMenu, (uint)(menuItemCount - 2), 5120u);
            WinApi.DrawMenuBar(Handle);
        }

        internal enum WindowButtons
        {
            Close = 0,
            Maximize = 1,
            Minimize = 2,
            Menu = 3,
            Top = 4,
            Dark = 5,
            Custom = 6,
        }

        private class MetroFormButton : Button, IMetroControl
        {
            private bool _isHovered;

            private bool _isPressed;
            private MetroColorStyle _metroStyle;

            private MetroThemeStyle _metroTheme;

            public MetroFormButton()
            {
                SetStyle(
                    ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint |
                    ControlStyles.OptimizedDoubleBuffer, true);
            }

            [Category("Metro Appearance")]
            [DefaultValue(MetroColorStyle.蓝色)]
            public MetroColorStyle Style
            {
                get
                {
                    if (DesignMode || _metroStyle != 0) return _metroStyle;
                    if (StyleManager != null) return StyleManager.Style;
                    if (StyleManager == null) return MetroColorStyle.蓝色;
                    return _metroStyle;
                }
                set => _metroStyle = value;
            }

            [Category("Metro Appearance")]
            [DefaultValue(MetroThemeStyle.Light)]
            public MetroThemeStyle Theme
            {
                get
                {
                    if (DesignMode || _metroTheme != 0) return _metroTheme;
                    if (StyleManager != null) return StyleManager.Theme;
                    if (StyleManager == null) return MetroThemeStyle.Light;
                    return _metroTheme;
                }
                set
                {
                    _metroTheme = value;
                }
            }

            [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
            [Browsable(false)]
            public MetroStyleManager StyleManager { get; set; }

            protected override void OnPaint(PaintEventArgs e)
            {
                var theme = Theme;
                var style = Style;
                Color backColor;
                if (Parent != null)
                {
                    if (!(Parent is IMetroForm form))
                    {
                        backColor = !(Parent is IMetroControl) ? Parent.BackColor : MetroPaint.GetStyleColor(Style);
                    }
                    else
                    {
                        theme = form.Theme;
                        style = form.Style;
                        backColor = MetroPaint.BackColor.Form(theme);
                    }
                }
                else
                {
                    backColor = MetroPaint.BackColor.Form(theme);
                }

                if (_isHovered && !_isPressed && Enabled)
                {
                    backColor = MetroPaint.BackColor.Button.Normal(theme);
                }
                else if (_isHovered && _isPressed && Enabled)
                {
                    backColor = MetroPaint.GetStyleColor(style);
                }
                else if (!Enabled)
                {
                    backColor = MetroPaint.BackColor.Button.Disabled(theme);
                }

                e.Graphics.Clear(backColor);
                e.Graphics.InterpolationMode = InterpolationMode.HighQualityBilinear;
                e.Graphics.CompositingQuality = CompositingQuality.HighQuality;
                e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
                e.Graphics.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
                if (BackgroundImage == null && Tag != null && Tag is WindowButtons button)
                {
                    BackgroundImage = GetWindowsButtonImage(button);
                }
                if (BackgroundImage != null)
                {
                    //base.OnPaintBackground(e);
                    e.Graphics.DrawImage(BackgroundImage, new PointF((Width - BackgroundImage.Width) / 2, (Height - BackgroundImage.Height) / 2));
                }
            }

            internal Bitmap GetWindowsButtonImage(WindowButtons button)
            {
                Bitmap image = null;
                switch (button)
                {
                    case WindowButtons.Close:
                        image = CommonSetting.夜间模式 ? Resources.ic_close_dark : Resources.ic_close;
                        break;
                    case WindowButtons.Minimize:
                        image = CommonSetting.夜间模式 ? Resources.ic_smallest_dark : Resources.ic_smallest;
                        break;
                    case WindowButtons.Maximize:
                        image = CommonSetting.夜间模式 ? Resources.ic_largest_dark : Resources.ic_largest;
                        break;
                    case WindowButtons.Menu:
                        image = CommonSetting.夜间模式 ? Resources.menu_dark : Resources.menu;
                        break;
                    case WindowButtons.Top:
                        var topMost = FindForm().TopMost;
                        image = CommonSetting.夜间模式 ? (topMost ? Resources.top_dark : Resources.untop_dark) : (topMost ? Resources.top : Resources.untop);
                        break;
                    case WindowButtons.Dark:
                        image = CommonSetting.夜间模式 ? Resources.sun : Resources.sun_dark;
                        break;
                }
                return image;
            }

            protected override void OnMouseEnter(EventArgs e)
            {
                _isHovered = true;
                Invalidate();
                base.OnMouseEnter(e);
            }

            protected override void OnMouseDown(MouseEventArgs e)
            {
                if (e.Button == MouseButtons.Left)
                {
                    _isPressed = true;
                    Invalidate();
                }
                base.OnMouseDown(e);
            }

            protected override void OnMouseUp(MouseEventArgs e)
            {
                _isPressed = false;
                Invalidate();
                base.OnMouseUp(e);
            }

            protected override void OnMouseLeave(EventArgs e)
            {
                _isHovered = false;
                Invalidate();
                base.OnMouseLeave(e);
            }
        }

        protected const int WS_EX_TRANSPARENT = 0x20;
        protected const int WS_EX_LAYERED = 0x80000;
        protected const int WS_EX_NOACTIVATE = 0x8000000;

        protected abstract class MetroShadowBase : Form
        {
            private readonly int _shadowSize;

            private readonly int _wsExStyle;

            //private bool _isBringingToFront;

            private long _lastResizedOn;

            protected MetroShadowBase(MetroForm targetForm, int shadowSize, int wsExStyle)
            {
                TargetForm = targetForm;
                _shadowSize = shadowSize;
                _wsExStyle = wsExStyle;
                TargetForm.Activated += OnTargetFormActivated;
                //TargetForm.Activated += TargetForm_LostFocus;
                //TargetForm.LostFocus += TargetForm_LostFocus;
                TargetForm.ResizeBegin += OnTargetFormResizeBegin;
                TargetForm.ResizeEnd += OnTargetFormResizeEnd;
                TargetForm.VisibleChanged += OnTargetFormVisibleChanged;
                TargetForm.SizeChanged += OnTargetFormSizeChanged;
                TargetForm.Move += OnTargetFormMove;
                TargetForm.Resize += OnTargetFormResize;
                TargetForm.Shown += TargetForm_Shown;
                TargetForm.TopMostChangedEvent += OnTargetFormTopMostChanged;

                if (TargetForm.Owner != null) Owner = TargetForm.Owner;
                TargetForm.Owner = this;
                MaximizeBox = false;
                MinimizeBox = false;
                ShowInTaskbar = false;
                ShowIcon = false;
                FormBorderStyle = FormBorderStyle.None;
                Bounds = GetShadowBounds();
            }

            protected MetroForm TargetForm { get; }

            protected override CreateParams CreateParams
            {
                get
                {
                    //const int WS_EX_NOACTIVATE = 0x08000000;
                    //const int WS_CHILD = 0x40000000;
                    //CreateParams cp = base.CreateParams;
                    //cp.Style |= WS_CHILD;
                    //cp.ExStyle |= WS_EX_NOACTIVATE;
                    //return cp;
                    var createParams = base.CreateParams;
                    //createParams.Style |= 0x40000000;
                    createParams.ExStyle |= _wsExStyle;
                    //createParams.ExStyle |= 0x08000000;
                    return createParams;
                }
            }

            private bool IsResizing => _lastResizedOn > 0;

            //private void TargetForm_LostFocus(object sender, EventArgs e)
            //{
            //    if (this.TopMost != TargetForm.TopMost)
            //    {
            //        this.TopMost = TargetForm.TopMost;
            //    }
            //}

            private Rectangle GetShadowBounds()
            {
                var bounds = TargetForm.Bounds;
                bounds.Inflate(_shadowSize, _shadowSize);
                return bounds;
            }

            protected abstract void PaintShadow();

            protected abstract void ClearShadow();

            private bool _isTargetFormShown;

            private void OnTargetFormTopMostChanged(object sender, EventArgs e)
            {
                TopMost = TargetForm.TopMost;
            }

            private void TargetForm_Shown(object sender, EventArgs e)
            {
                OnTargetFormTopMostChanged(sender, e);
                _isTargetFormShown = true;
                PaintShadowIfVisible();
                if (Visible)
                    TargetForm?.ForceActivate();
            }

            //protected override void OnDeactivate(EventArgs e)
            //{
            //    base.OnDeactivate(e);
            //    _isBringingToFront = true;
            //}

            private void OnTargetFormActivated(object sender, EventArgs e)
            {
                if (Visible) Update();
                //if (_isBringingToFront)
                //{
                //    Visible = true;
                //    _isBringingToFront = false;
                //}
                //else
                //{
                //    BringToFront();
                //}
            }

            private void OnTargetFormVisibleChanged(object sender, EventArgs e)
            {
                Visible = IsCanShow;
                Update();
            }

            private void OnTargetFormMove(object sender, EventArgs e)
            {
                Bounds = GetShadowBounds();
            }

            private void OnTargetFormResizeBegin(object sender, EventArgs e)
            {
                _lastResizedOn = ServerTime.DateTime.Ticks;
            }

            private void OnTargetFormResizeEnd(object sender, EventArgs e)
            {
                _lastResizedOn = 0L;
                PaintShadowIfVisible();
            }

            private void OnTargetFormResize(object sender, EventArgs e)
            {
                ClearShadow();
            }

            private void OnTargetFormSizeChanged(object sender, EventArgs e)
            {
                Bounds = GetShadowBounds();
                if (!IsResizing) PaintShadowIfVisible();
            }

            private void PaintShadowIfVisible()
            {
                Visible = IsCanShow;
                if (Visible) PaintShadow();
            }

            public bool IsCanShow => _isTargetFormShown && TargetForm != null && TargetForm.Visible && TargetForm.WindowState == FormWindowState.Normal && TargetForm.Opacity > 0;
        }

        protected class MetroFlatDropShadow : MetroShadowBase
        {
            private Point _offset = new Point(-6, -6);

            public MetroFlatDropShadow(MetroForm targetForm)
                : base(targetForm, 6, WS_EX_LAYERED | WS_EX_TRANSPARENT | WS_EX_NOACTIVATE)
            {
            }

            protected override void OnLoad(EventArgs e)
            {
                base.OnLoad(e);
                OnPaint(null);
            }

            protected override void OnPaint(PaintEventArgs e)
            {
                Visible = IsCanShow;
                if (Visible)
                    PaintShadow();
            }

            protected override void PaintShadow()
            {
                using (var bitmap = DrawBlurBorder())
                {
                    SetBitmap(bitmap, byte.MaxValue);
                }
            }

            protected override void ClearShadow()
            {
                var bitmap = new Bitmap(Width, Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.Clear(Color.Transparent);
                graphics.Flush();
                graphics.Dispose();
                SetBitmap(bitmap, byte.MaxValue);
                bitmap.Dispose();
            }

            [SecuritySafeCritical]
            private void SetBitmap(Bitmap bitmap, byte opacity)
            {
                if (bitmap.PixelFormat != PixelFormat.Format32bppArgb)
                    throw new ApplicationException("The bitmap must be 32ppp with alpha-channel.");
                var dC = WinApi.GetDC(IntPtr.Zero);
                var intPtr = WinApi.CreateCompatibleDC(dC);
                var intPtr2 = IntPtr.Zero;
                var hObject = IntPtr.Zero;
                try
                {
                    intPtr2 = bitmap.GetHbitmap(Color.FromArgb(0));
                    hObject = WinApi.SelectObject(intPtr, intPtr2);
                    var psize = new WinApi.SIZE(bitmap.Width, bitmap.Height);
                    var pprSrc = new WinApi.POINT(0, 0);
                    var pptDst = new WinApi.POINT(Left, Top);
                    var pblend = default(WinApi.BLENDFUNCTION);
                    pblend.BlendOp = 0;
                    pblend.BlendFlags = 0;
                    pblend.SourceConstantAlpha = opacity;
                    pblend.AlphaFormat = 1;
                    WinApi.UpdateLayeredWindow(Handle, dC, ref pptDst, ref psize, intPtr, ref pprSrc, 0, ref pblend, 2);
                }
                finally
                {
                    WinApi.ReleaseDC(IntPtr.Zero, dC);
                    if (intPtr2 != IntPtr.Zero)
                    {
                        WinApi.SelectObject(intPtr, hObject);
                        WinApi.DeleteObject(intPtr2);
                    }

                    WinApi.DeleteDC(intPtr);
                }
            }

            private Bitmap DrawBlurBorder()
            {
                return (Bitmap)DrawOutsetShadow(Color.Black,
                    new Rectangle(0, 0, ClientRectangle.Width, ClientRectangle.Height));
            }

            private Image DrawOutsetShadow(Color color, Rectangle shadowCanvasArea)
            {
                var rect = shadowCanvasArea;
                var rect2 = new Rectangle(shadowCanvasArea.X + (-_offset.X - 1), shadowCanvasArea.Y + (-_offset.Y - 1),
                    shadowCanvasArea.Width - (-_offset.X * 2 - 1), shadowCanvasArea.Height - (-_offset.Y * 2 - 1));
                var bitmap = new Bitmap(rect.Width, rect.Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                using (Brush brush = new SolidBrush(Color.FromArgb(30, Color.Black)))
                {
                    graphics.FillRectangle(brush, rect);
                }

                using (Brush brush2 = new SolidBrush(Color.FromArgb(60, Color.Black)))
                {
                    graphics.FillRectangle(brush2, rect2);
                }

                graphics.Flush();
                graphics.Dispose();
                return bitmap;
            }
        }

        protected class MetroRealisticDropShadow : MetroShadowBase
        {
            public MetroRealisticDropShadow(MetroForm targetForm)
                : base(targetForm, 15, WS_EX_LAYERED | WS_EX_TRANSPARENT | WS_EX_NOACTIVATE)
            {
            }

            protected override void OnLoad(EventArgs e)
            {
                base.OnLoad(e);
                OnPaint(null);
            }

            protected override void OnPaint(PaintEventArgs e)
            {
                Visible = IsCanShow;
                if (Visible)
                    PaintShadow();
            }

            protected override void PaintShadow()
            {
                using (var bitmap = DrawBlurBorder())
                {
                    SetBitmap(bitmap, byte.MaxValue);
                }
            }

            protected override void ClearShadow()
            {
                var bitmap = new Bitmap(Width, Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.Clear(Color.Transparent);
                graphics.Flush();
                graphics.Dispose();
                SetBitmap(bitmap, byte.MaxValue);
                bitmap.Dispose();
            }

            [SecuritySafeCritical]
            private void SetBitmap(Bitmap bitmap, byte opacity)
            {
                if (bitmap.PixelFormat != PixelFormat.Format32bppArgb)
                    throw new ApplicationException("The bitmap must be 32ppp with alpha-channel.");
                var dC = WinApi.GetDC(IntPtr.Zero);
                var intPtr = WinApi.CreateCompatibleDC(dC);
                var intPtr2 = IntPtr.Zero;
                var hObject = IntPtr.Zero;
                try
                {
                    intPtr2 = bitmap.GetHbitmap(Color.FromArgb(0));
                    hObject = WinApi.SelectObject(intPtr, intPtr2);
                    var psize = new WinApi.SIZE(bitmap.Width, bitmap.Height);
                    var pprSrc = new WinApi.POINT(0, 0);
                    var pptDst = new WinApi.POINT(Left, Top);
                    var bLendfunction = default(WinApi.BLENDFUNCTION);
                    bLendfunction.BlendOp = 0;
                    bLendfunction.BlendFlags = 0;
                    bLendfunction.SourceConstantAlpha = opacity;
                    bLendfunction.AlphaFormat = 1;
                    var pblend = bLendfunction;
                    WinApi.UpdateLayeredWindow(Handle, dC, ref pptDst, ref psize, intPtr, ref pprSrc, 0, ref pblend, 2);
                }
                finally
                {
                    WinApi.ReleaseDC(IntPtr.Zero, dC);
                    if (intPtr2 != IntPtr.Zero)
                    {
                        WinApi.SelectObject(intPtr, hObject);
                        WinApi.DeleteObject(intPtr2);
                    }

                    WinApi.DeleteDC(intPtr);
                }
            }

            private Bitmap DrawBlurBorder()
            {
                return (Bitmap)DrawOutsetShadow(0, 0, 40, 1, Color.Black,
                    new Rectangle(1, 1, ClientRectangle.Width, ClientRectangle.Height));
            }

            private Image DrawOutsetShadow(int hShadow, int vShadow, int blur, int spread, Color color,
                Rectangle shadowCanvasArea)
            {
                var rectangle = shadowCanvasArea;
                var rectangle2 = shadowCanvasArea;
                rectangle2.Offset(hShadow, vShadow);
                rectangle2.Inflate(-blur, -blur);
                rectangle.Inflate(spread, spread);
                rectangle.Offset(hShadow, vShadow);
                var rectangle3 = rectangle;
                var bitmap = new Bitmap(rectangle3.Width, rectangle3.Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                var cornerRadius = 0;
                do
                {
                    var num = (rectangle.Height - rectangle2.Height) / (double)(blur * 2 + spread * 2);
                    var fillColor = Color.FromArgb((int)(200.0 * (num * num)), color);
                    var bounds = rectangle2;
                    bounds.Offset(-rectangle3.Left, -rectangle3.Top);
                    DrawRoundedRectangle(graphics, bounds, cornerRadius, Pens.Transparent, fillColor);
                    rectangle2.Inflate(1, 1);
                    cornerRadius = (int)(blur * (1.0 - num * num));
                } while (rectangle.Contains(rectangle2));

                graphics.Flush();
                graphics.Dispose();
                return bitmap;
            }

            private void DrawRoundedRectangle(Graphics g, Rectangle bounds, int cornerRadius, Pen drawPen,
                Color fillColor)
            {
                var num = Convert.ToInt32(Math.Ceiling(drawPen.Width));
                bounds = Rectangle.Inflate(bounds, -num, -num);
                var graphicsPath = new GraphicsPath();
                if (cornerRadius > 0)
                {
                    graphicsPath.AddArc(bounds.X, bounds.Y, cornerRadius, cornerRadius, 180f, 90f);
                    graphicsPath.AddArc(bounds.X + bounds.Width - cornerRadius, bounds.Y, cornerRadius, cornerRadius,
                        270f, 90f);
                    graphicsPath.AddArc(bounds.X + bounds.Width - cornerRadius, bounds.Y + bounds.Height - cornerRadius,
                        cornerRadius, cornerRadius, 0f, 90f);
                    graphicsPath.AddArc(bounds.X, bounds.Y + bounds.Height - cornerRadius, cornerRadius, cornerRadius,
                        90f, 90f);
                }
                else
                {
                    graphicsPath.AddRectangle(bounds);
                }

                graphicsPath.CloseAllFigures();
                if (cornerRadius > 5)
                    using (var brush = new SolidBrush(fillColor))
                    {
                        g.FillPath(brush, graphicsPath);
                    }

                if (!Equals(drawPen, Pens.Transparent))
                    using (var pen = new Pen(drawPen.Color))
                    {
                        pen.EndCap = pen.StartCap = LineCap.Round;
                        g.DrawPath(pen, graphicsPath);
                    }
            }
        }
    }

    public enum MetroFormTextAlign
    {
        Left,
        Center,
        Right
    }
    public enum Messages : uint
    {
        WM_SIZE = 0x5,
        WM_GETMINMAXINFO = 0x24,
        WM_NCHITTEST = 0x84,
        WM_NCLBUTTONDOWN = 0xa1,
        WM_NCLBUTTONDBLCLK = 0xa3,
        WM_SYSCOMMAND = 0x112,
        WM_LBUTTONDBLCLK = 0x203,
        WM_DWMCOMPOSITIONCHANGED = 0x031E,

        SC_MOVE = 0xF010,
        SC_MAXIMIZE = 0xF030,
        SC_RESTORE = 0xF120
    }
}