﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class TransParentLabel : Label
    {
        private bool isClickToView;

        private bool isMouseMove;

        public TransParentLabel()
        {
            SetStyle(ControlStyles.SupportsTransparentBackColor |
                     ControlStyles.OptimizedDoubleBuffer |
                     ControlStyles.AllPaintingInWmPaint |
                     ControlStyles.ResizeRedraw |
                     ControlStyles.UserPaint, true);
            BackColor = Color.Transparent;
            BorderStyle = BorderStyle.None;
            AutoSize = false;
            Margin = Padding.Empty;
            Padding = Padding.Empty;
            MouseClick += TransParentTextBox_MouseClick;
            //MouseMove += TransParentLabel_MouseMove;
            //MouseEnter += TransParentLabel_MouseEnter;
            //MouseLeave += TransParentLabel_MouseLeave;
        }

        private Font ShowTxtFont { get; set; }

        //private void TransParentLabel_MouseEnter(object sender, EventArgs e)
        //{
        //    IsMouseMove = true;
        //    Console.WriteLine("MouseEnter-2");
        //}

        //private void TransParentLabel_MouseLeave(object sender, EventArgs e)
        //{
        //    IsMouseMove = false;
        //}

        //private void TransParentLabel_MouseMove(object sender, MouseEventArgs e)
        //{
        //    IsMouseMove = true;
        //    Console.WriteLine("当前活动控件：" + FindForm().Controls[0]?.Focused);
        //}

        public Size OriSize { get; set; }

        public Point OrgLocation { get; set; }

        public bool IsShowText { get; set; } //= true;

        public bool IsClickToView
        {
            get => isClickToView;
            set
            {
                isClickToView = value;
                Invalidate();
            }
        }

        public bool IsMouseMove
        {
            get => isMouseMove;
            set
            {
                isMouseMove = value;
                Invalidate();
            }
        }

        public Color ContentBackColor { get; set; }

        protected override void OnPaint(PaintEventArgs e)
        {
            if (IsMouseMove)
            {
                using (var contentBrush = new SolidBrush(Color.FromArgb(50, Color.Red)))
                {
                    e.Graphics.FillRectangle(contentBrush, 0, 0, Width, Height);
                }
            }
            else if (IsClickToView)
            {
                using (var contentBrush = new SolidBrush(Color.FromArgb(90, Color.Red)))
                {
                    e.Graphics.FillRectangle(contentBrush, 0, 0, Width, Height);
                }
            }
            else if (IsShowText)
            {
                if (ShowTxtFont == null)
                    ShowTxtFont = CommonMethod.FindFont(e.Graphics, Text, CommonMethod.BaseFont, Size);

                using (var backgroundBrush = new SolidBrush(ContentBackColor))
                {
                    e.Graphics.FillRectangle(backgroundBrush, 0, 0, Width, Height);
                }

                using (var textBrush = new SolidBrush(Color.Black))
                {
                    e.Graphics.DrawString(Text, ShowTxtFont ?? Font, textBrush, new RectangleF(0, 0, Width, Height));
                }
            }
            else
            {
                using (var backgroundBrush = new SolidBrush(Color.Transparent))
                {
                    e.Graphics.FillRectangle(backgroundBrush, 0, 0, Width, Height);
                }
            }

            using (var penBorder = new Pen(Color.Red, 1))
            {
                var rectBorder = new Rectangle(e.ClipRectangle.X, e.ClipRectangle.Y, e.ClipRectangle.Width - 1,
                    e.ClipRectangle.Height - 1);
                e.Graphics.DrawRectangle(penBorder, rectBorder);
            }
        }

        private void TransParentTextBox_MouseClick(object sender, MouseEventArgs e)
        {
            if (!string.IsNullOrEmpty(Text))
            {
                var mouseToForm = PointToClient(MousePosition);
                CommonMethod.ShowTxtToolTipContextMenu(this,
                    new Point(e.X > mouseToForm.X ? 0 : e.X - mouseToForm.X, Math.Min(Height, e.Y + 20)));
                //Console.WriteLine("MouseClick触发");
            }
        }
    }
}