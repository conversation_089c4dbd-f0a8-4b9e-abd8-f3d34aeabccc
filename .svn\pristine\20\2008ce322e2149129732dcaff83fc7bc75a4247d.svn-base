﻿using MetroFramework.Forms;
using OCRTools.Common;
using ShareX.ScreenCaptureLib;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class ScrollingCaptureForm : MetroForm
    {
        private readonly List<Bitmap> _images = new List<Bitmap>();

        private int _currentScrollCount;
        private ScrollingCaptureScrollMethod _currentScrollMethod;
        private bool _isBusy, _firstCapture, _detectingScrollMethod;
        private Rectangle _selectedRectangle;

        private WindowInfo _selectedWindow;
        ScrollingCaptureOptions Options = new ScrollingCaptureOptions();

        public ScrollingCaptureForm()
        {
            AutoScaleMode = AutoScaleMode.Dpi;
            Font = CommonString.GetSysNormalFont(13F);
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            WindowState = FormWindowState.Minimized;
        }

        public Bitmap Result { get; private set; }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                components?.Dispose();

                Clean();
            }

            base.Dispose(disposing);
        }

        public void BindWindow(WindowInfo window)
        {
            if (window != null)
            {
                _selectedWindow = window;
                _selectedRectangle = window.Rectangle;
            }
        }

        private void StartCapture()
        {
            _firstCapture = true;

            if (CommonSetting.ScrollMethod == ScrollingCaptureScrollMethod.自动尝试所有方法直到某方法生效.ToString())
                _currentScrollMethod = ScrollingCaptureScrollMethod.发送滚动消息至窗口或控件;
            else
                _currentScrollMethod = CommonSetting.ConvertToEnum(CommonSetting.ScrollMethod, _currentScrollMethod);

            _detectingScrollMethod = true;
            Clean();
            if (_selectedWindow.GetIsMinimized()) _selectedWindow.Restore();
            if (!_selectedWindow.Activate())
            {
                Console.WriteLine("激活窗口失败");
            }
            captureTimer.Interval = (int)CommonSetting.StartDelay;
            captureTimer.Start();
        }

        private void StopCapture()
        {
            captureTimer.Stop();
            this.WindowState = FormWindowState.Maximized;
            this.ForceActivate();
            tcScrollingCapture.SelectedTab = tpOutput;
            StartingProcess();
            RemoveDuplicates();
            txtImagesCount.Text = _images.Count.ToString();
            btnGuessEdges.Enabled = btnGuessCombineAdjustments.Enabled = _images.Count > 1;
            btnResetCombine.Enabled = _images.Count > 0;
            nudIgnoreLast.Maximum = _images.Count > 1 ? _images.Count - 1 : 0;
            ResetOffsets();

            GuessEdges();
            GuessCombineAdjustments();

            CombineAndPreviewImages();

            EndingProcess();
        }

        private void Clean()
        {
            _currentScrollCount = 0;

            CleanPictureBox();

            if (_images != null)
            {
                foreach (var bmp in _images) bmp?.Dispose();

                _images.Clear();
            }

            Result?.Dispose();
        }

        private void CleanPictureBox(Image img = null)
        {
            var temp = pbOutput.Image;

            pbOutput.Image = img;

            temp?.Dispose();
        }

        private void RemoveDuplicates()
        {
            if (_images.Count <= 1) return;
            for (var i = _images.Count - 1; i > 0; i--)
            {
                var result = ImageProcessHelper.IsImagesEqual(_images[i], _images[i - 1]);

                if (result)
                {
                    var bmp = _images[i];
                    _images.Remove(bmp);
                    bmp.Dispose();
                }
            }
        }

        private void captureTimer_Tick(object sender, EventArgs e)
        {
            if (_firstCapture)
            {
                _firstCapture = false;
                captureTimer.Interval = (int)CommonSetting.ScrollDelay;

                if (CommonSetting.ScrollTopMethodBeforeCapture == ScrollingCaptureScrollTopMethod.自动尝试所有方法直到某方法生效.ToString() ||
                    CommonSetting.ScrollTopMethodBeforeCapture == ScrollingCaptureScrollTopMethod.模拟按下Home按键.ToString())
                    InputHelpers.SendKeyPress(VirtualKeyCode.Home);

                if (CommonSetting.ScrollTopMethodBeforeCapture == ScrollingCaptureScrollTopMethod.自动尝试所有方法直到某方法生效.ToString() ||
                    CommonSetting.ScrollTopMethodBeforeCapture == ScrollingCaptureScrollTopMethod.发送滚动消息至顶部.ToString())
                    NativeMethods.SendMessage(_selectedWindow.Handle, 0x0115, (int)ScrollBarCommands.SB_TOP, 0);

                if (CommonSetting.ScrollTopMethodBeforeCapture != ScrollingCaptureScrollTopMethod.不自动滚动至顶部.ToString()) return;
            }

            var bmp = Screenshot.CaptureRectangle(_selectedRectangle, _selectedRectangle);

            if (bmp != null) _images.Add(bmp);

            if (CommonSetting.ScrollMethod == ScrollingCaptureScrollMethod.自动尝试所有方法直到某方法生效.ToString() && _detectingScrollMethod &&
                _images.Count > 1 && IsLastTwoImagesSame())
            {
                if (_currentScrollMethod == ScrollingCaptureScrollMethod.发送滚动消息至窗口或控件)
                    _currentScrollMethod = ScrollingCaptureScrollMethod.模拟按下Page_Down按钮;
                else if (_currentScrollMethod == ScrollingCaptureScrollMethod.模拟按下Page_Down按钮)
                    _currentScrollMethod = ScrollingCaptureScrollMethod.模拟鼠标滚轮滚动;
                else if (_currentScrollMethod == ScrollingCaptureScrollMethod.模拟鼠标滚轮滚动)
                    _currentScrollMethod = ScrollingCaptureScrollMethod.模拟按下Down按键;
                else
                    StopCapture();
            }
            else if (IsScrollReachedBottom(_selectedWindow.Handle))
            {
                StopCapture();
            }
            else if (_images.Count > 1)
            {
                _detectingScrollMethod = false;
            }

            switch (_currentScrollMethod)
            {
                case ScrollingCaptureScrollMethod.自动尝试所有方法直到某方法生效:
                case ScrollingCaptureScrollMethod.发送滚动消息至窗口或控件: //WindowsMessages.VSCROLL
                    NativeMethods.SendMessage(_selectedWindow.Handle, 0x0115, (int)ScrollBarCommands.SB_PAGEDOWN, 0);
                    break;
                case ScrollingCaptureScrollMethod.模拟按下Page_Down按钮:
                    InputHelpers.SendKeyPress(VirtualKeyCode.Next);
                    break;
                case ScrollingCaptureScrollMethod.模拟按下Down按键:
                    InputHelpers.SendKeyPress(VirtualKeyCode.Down);
                    break;
                case ScrollingCaptureScrollMethod.模拟鼠标滚轮滚动:
                    InputHelpers.SendMouseWheel(-120);
                    break;
            }

            _currentScrollCount++;
        }

        private bool IsScrollReachedBottom(IntPtr handle)
        {
            var scrollInfo = new SCROLLINFO();
            scrollInfo.cbSize = (uint)Marshal.SizeOf(scrollInfo);
            scrollInfo.fMask =
                (uint)(ScrollInfoMask.SIF_RANGE | ScrollInfoMask.SIF_PAGE | ScrollInfoMask.SIF_TRACKPOS);

            if (NativeMethods.GetScrollInfo(handle, (int)SBOrientation.SB_VERT, ref scrollInfo))
                return scrollInfo.nMax == scrollInfo.nTrackPos + scrollInfo.nPage - 1;

            return IsLastTwoImagesSame();
        }

        private bool IsLastTwoImagesSame()
        {
            var result = false;

            if (_images.Count > 1)
            {
                result = ImageProcessHelper.IsImagesEqual(_images[_images.Count - 1], _images[_images.Count - 2]);

                if (result)
                {
                    var last = _images[_images.Count - 1];
                    _images.Remove(last);
                    last.Dispose();
                }
            }

            return result;
        }

        private void nudTrimLeft_ValueChanged(object sender, EventArgs e)
        {
            Options.TrimLeftEdge = (int)nudTrimLeft.Value;
            CombineAndPreviewImagesFromControl();
        }

        private void nudTrimTop_ValueChanged(object sender, EventArgs e)
        {
            Options.TrimTopEdge = (int)nudTrimTop.Value;
            CombineAndPreviewImagesFromControl();
        }

        private void nudTrimRight_ValueChanged(object sender, EventArgs e)
        {
            Options.TrimRightEdge = (int)nudTrimRight.Value;
            CombineAndPreviewImagesFromControl();
        }

        private void nudTrimBottom_ValueChanged(object sender, EventArgs e)
        {
            Options.TrimBottomEdge = (int)nudTrimBottom.Value;
            CombineAndPreviewImagesFromControl();
        }

        private void nudCombineVertical_ValueChanged(object sender, EventArgs e)
        {
            Options.CombineAdjustmentVertical = (int)nudCombineVertical.Value;
            CombineAndPreviewImagesFromControl();
        }

        private void nudCombineLastVertical_ValueChanged(object sender, EventArgs e)
        {
            Options.CombineAdjustmentLastVertical = (int)nudCombineLastVertical.Value;
            CombineAndPreviewImagesFromControl();
        }

        private void nudIgnoreLast_ValueChanged(object sender, EventArgs e)
        {
            Options.IgnoreLast = (int)nudIgnoreLast.Value;
            txtImagesCount.Text = (_images.Count - Options.IgnoreLast).ToString();
            CombineAndPreviewImagesFromControl();
        }

        private void btnGuessEdges_Click(object sender, EventArgs e)
        {
            StartingProcess();
            GuessEdges();
            CombineAndPreviewImages();
            EndingProcess();
        }

        private void btnGuessCombineAdjustments_Click(object sender, EventArgs e)
        {
            StartingProcess();
            GuessCombineAdjustments();
            CombineAndPreviewImages();
            EndingProcess();
        }

        private void StartingProcess()
        {
            _isBusy = true;
            CleanPictureBox();
            lblProcessing.Visible = true;
            Application.DoEvents();
        }

        private void EndingProcess()
        {
            lblProcessing.Visible = false;
            _isBusy = false;
        }

        private void btnResetCombine_Click(object sender, EventArgs e)
        {
            StartingProcess();
            ResetOffsets();
            CombineAndPreviewImages();
            EndingProcess();
        }

        private void ResetOffsets()
        {
            nudTrimLeft.Value = nudTrimTop.Value = nudTrimRight.Value = nudTrimBottom.Value =
                nudCombineVertical.Value = nudCombineLastVertical.Value = nudIgnoreLast.Value = 0;
            Options.TrimLeftEdge = Options.TrimTopEdge = Options.TrimRightEdge = Options.TrimBottomEdge =
                Options.CombineAdjustmentVertical = Options.CombineAdjustmentLastVertical = Options.IgnoreLast = 0;
        }

        private void CombineAndPreviewImagesFromControl()
        {
            if (!_isBusy)
            {
                Result = CombineImages();
                CleanPictureBox(Result);
            }
        }

        private void CombineAndPreviewImages()
        {
            Result = CombineImages();
            pbOutput.Image = Result;
        }

        private Bitmap CombineImages()
        {
            if (_images == null || _images.Count == 0) return null;

            if (_images.Count == 1) return (Bitmap)_images[0].Clone();

            var output = new List<Bitmap>();

            for (var i = 0; i < _images.Count - Options.IgnoreLast; i++)
            {
                Bitmap newImage;
                var image = _images[i];

                if (Options.TrimLeftEdge > 0 || Options.TrimTopEdge > 0 || Options.TrimRightEdge > 0 ||
                    Options.TrimBottomEdge > 0 ||
                    Options.CombineAdjustmentVertical > 0 || Options.CombineAdjustmentLastVertical > 0)
                {
                    var rect = new Rectangle(Options.TrimLeftEdge, Options.TrimTopEdge,
                        image.Width - Options.TrimLeftEdge - Options.TrimRightEdge,
                        image.Height - Options.TrimTopEdge - Options.TrimBottomEdge);

                    if (i == _images.Count - 1)
                    {
                        rect.Y += Options.CombineAdjustmentLastVertical;
                        rect.Height -= Options.CombineAdjustmentLastVertical;
                    }
                    else if (i > 0)
                    {
                        rect.Y += Options.CombineAdjustmentVertical;
                        rect.Height -= Options.CombineAdjustmentVertical;
                    }

                    newImage = ImageProcessHelper.CropBitmap(image, rect);

                    if (newImage == null) continue;
                }
                else
                {
                    newImage = (Bitmap)image.Clone();
                }

                output.Add(newImage);
            }

            var bmpResult = ImageProcessHelper.CombineImages(output);

            foreach (var image in output) image?.Dispose();

            output.Clear();

            return bmpResult;
        }

        private void GuessEdges()
        {
            if (_images.Count < 2) return;

            nudTrimLeft.Value = nudTrimTop.Value = nudTrimRight.Value = nudTrimBottom.Value = 0;

            var result = new Padding();

            for (var i = 0; i < _images.Count - 1; i++)
            {
                var edges = GuessEdges(_images[i], _images[i + 1]);

                if (i == 0)
                {
                    result = edges;
                }
                else
                {
                    result.Left = Math.Min(result.Left, edges.Left);
                    result.Top = Math.Min(result.Top, edges.Top);
                    result.Right = Math.Min(result.Right, edges.Right);
                    result.Bottom = Math.Min(result.Bottom, edges.Bottom);
                }
            }

            nudTrimLeft.SetValue(result.Left);
            nudTrimTop.SetValue(result.Top);
            nudTrimRight.SetValue(result.Right);
            nudTrimBottom.SetValue(result.Bottom);
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            if (Result != null)
                new Bitmap(Result).SaveFile(this);
        }

        private void ScrollingCaptureForm_Load(object sender, EventArgs e)
        {
            StartCapture();
        }

        private void lnkSet_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            using (var frmSetting = new FormSetting { Icon = Icon })
            {
                frmSetting.TopMost = true;
                frmSetting.OpenTab = "截屏";
                frmSetting.SharkGroup = "捕捉前";

                frmSetting.ShowDialog(this);
            }
        }

        private Padding GuessEdges(Bitmap img1, Bitmap img2)
        {
            var result = new Padding();
            var rect = new Rectangle(0, 0, img1.Width, img1.Height);

            using (var bmp1 = new UnsafeBitmap(img1, true, ImageLockMode.ReadOnly))
            using (var bmp2 = new UnsafeBitmap(img2, true, ImageLockMode.ReadOnly))
            {
                var valueFound = false;

                // Left edge
                for (var x = rect.X; !valueFound && x < rect.Width; x++)
                    for (var y = rect.Y; y < rect.Height; y++)
                        if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        {
                            valueFound = true;
                            result.Left = x;
                            rect.X = x;
                            break;
                        }

                valueFound = false;

                // Top edge
                for (var y = rect.Y; !valueFound && y < rect.Height; y++)
                    for (var x = rect.X; x < rect.Width; x++)
                        if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        {
                            valueFound = true;
                            result.Top = y;
                            rect.Y = y;
                            break;
                        }

                valueFound = false;

                // Right edge
                for (var x = rect.Width - 1; !valueFound && x >= rect.X; x--)
                    for (var y = rect.Y; y < rect.Height; y++)
                        if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        {
                            valueFound = true;
                            result.Right = rect.Width - x - 1;
                            rect.Width = x + 1;
                            break;
                        }

                valueFound = false;

                // Bottom edge
                for (var y = rect.Height - 1; !valueFound && y >= rect.X; y--)
                    for (var x = rect.X; x < rect.Width; x++)
                        if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        {
                            valueFound = true;
                            result.Bottom = rect.Height - y - 1;
                            rect.Height = y + 1;
                            break;
                        }
            }

            return result;
        }

        private void GuessCombineAdjustments()
        {
            if (_images.Count > 1)
            {
                var vertical = 0;

                for (var i = 0; i < _images.Count - 2; i++)
                {
                    var temp = CalculateVerticalOffset(_images[i], _images[i + 1]);
                    vertical = Math.Max(vertical, temp);
                }

                nudCombineVertical.SetValue(vertical);
                nudCombineLastVertical.SetValue(CalculateVerticalOffset(_images[_images.Count - 2], _images[_images.Count - 1]));
            }
        }

        private int CalculateVerticalOffset(Bitmap img1, Bitmap img2, int ignoreRightOffset = 50)
        {
            var lastMatchCount = 0;
            var lastMatchOffset = 0;

            var rect = new Rectangle(Options.TrimLeftEdge, Options.TrimTopEdge,
                img1.Width - Options.TrimLeftEdge - Options.TrimRightEdge -
                (img1.Width > ignoreRightOffset ? ignoreRightOffset : 0),
                img1.Height - Options.TrimTopEdge - Options.TrimBottomEdge);

            using (var bmp1 = new UnsafeBitmap(img1, true, ImageLockMode.ReadOnly))
            using (var bmp2 = new UnsafeBitmap(img2, true, ImageLockMode.ReadOnly))
            {
                for (var y = rect.Y; y < rect.Bottom; y++)
                {
                    var isLineMatches = true;

                    for (var x = rect.X; x < rect.Right; x++)
                        if (bmp2.GetPixel(x, y) != bmp1.GetPixel(x, rect.Bottom - 1))
                        {
                            isLineMatches = false;
                            break;
                        }

                    if (isLineMatches)
                    {
                        var lineMatchesCount = 1;
                        var y3 = 2;

                        for (var y2 = y - 1; y2 >= rect.Y; y2--)
                        {
                            var isLineMatches2 = true;

                            for (var x2 = rect.X; x2 < rect.Right; x2++)
                                if (bmp2.GetPixel(x2, y2) != bmp1.GetPixel(x2, rect.Bottom - y3))
                                {
                                    isLineMatches2 = false;
                                    break;
                                }

                            if (isLineMatches2)
                            {
                                lineMatchesCount++;
                                y3++;
                            }
                            else
                            {
                                break;
                            }
                        }

                        if (lineMatchesCount > lastMatchCount)
                        {
                            lastMatchCount = lineMatchesCount;
                            lastMatchOffset = y - rect.Y + 1;
                        }
                    }
                }
            }

            return lastMatchOffset;
        }
    }
}