﻿using System.Drawing;

namespace OCRTools.ScreenCaptureLib
{
    public class SmartEraserDrawingShape : BaseDrawingShape
    {
        public override ShapeType ShapeType { get; } = ShapeType.橡皮擦;

        private Color eraserColor;
        private Color eraserDimmedColor;

        public override void OnConfigLoad()
        {
        }

        public override void OnConfigSave()
        {
        }

        public override void OnCreating()
        {
            base.OnCreating();

            eraserColor = Manager.GetCurrentColor();

            if (eraserColor.IsEmpty)
            {
                eraserColor = Color.White;
            }

            if (Manager.Form.DimmedCanvas != null)
            {
                eraserDimmedColor = Manager.GetCurrentColor(Manager.Form.DimmedCanvas);
            }
        }

        public override void OnDraw(Graphics g)
        {
            Color color;

            if (!Manager.IsRenderingOutput && !eraserDimmedColor.IsEmpty)
            {
                color = eraserDimmedColor;
            }
            else
            {
                color = eraserColor;
            }

            using (Brush brush = new SolidBrush(color))
            {
                g.<PERSON>ll<PERSON><PERSON>tangle(brush, Rectangle);
            }
        }
    }
}