﻿//************************************************************************
//      https://github.com/yuzhengyang
//      author:     yuzhengyang
//      date:       2017.4.27 - 2017.8.25
//      desc:       工具描述
//      Copyright (c) yuzhengyang. All rights reserved.
//************************************************************************

using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using OCRTools.MyShadowForm;

namespace OCRTools.Shadow
{
    public partial class ShadowForm : Form
    {
        private bool _mBMouseOnClose;
        private bool _mBMouseOnMax;
        private bool _mBMouseOnMin;

        private Rectangle _mRectClose;
        private Rectangle _mRectMax;
        private Rectangle _mRectMin;
        private Rectangle _mRectTitle;
        private ShadowFormSkin _skin;

        public Color ShadowColor = StaticValue.ShadowActiveColor;

        public ShadowForm()
        {
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false;
            SetStyle(
                ControlStyles.UserPaint |
                ControlStyles.AllPaintingInWmPaint |
                ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.ResizeRedraw |
                ControlStyles.SupportsTransparentBackColor |
                ControlStyles.DoubleBuffer, true);
            BackColor = Color.FromArgb(0, 240, 240, 240);
            Padding = new Padding(2, 35, 2, 2);
            base.FormBorderStyle = FormBorderStyle.None;
            FormClosed += ShadowForm_FormClosed;
            FormClosing += ShadowForm_FormClosing;
        }

        public bool IsSharkWindow { get; set; } = true;
        public bool IsForceActive { get; set; } = true;
        public int ShadowWidth { get; set; } = 10;

        public bool IsUseCustomerShadowColor { get; set; }

        public new bool MaximizeBox
        {
            get => base.MaximizeBox;
            set
            {
                if (value == base.MaximizeBox) return;
                base.MaximizeBox = value;
                CheckTitleBarSize();
            }
        }

        public new bool MinimizeBox
        {
            get => base.MinimizeBox;
            set
            {
                if (value == base.MinimizeBox) return;
                base.MinimizeBox = value;
                CheckTitleBarSize();
            }
        }

        public new FormBorderStyle FormBorderStyle
        {
            get => FormBorderStyle.None;
            set { }
        }

        public override string Text
        {
            get => base.Text;
            set
            {
                if (value == base.Text) return;
                base.Text = value;
                Invalidate(_mRectTitle);
            }
        }

        public bool IsShowTitle { get; set; } = true;

        public bool Sizeable { get; set; } = true;

        private void ShadowForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            Visibility(false, false);
        }

        private void ShadowForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            if (_skin != null && !_skin.IsDisposed)
            {
                _skin.Dispose();
                _skin = null;
            }
        }

        private void ShadowFormSkin_LostFocus(object sender, EventArgs e)
        {
            DrawShadow(StaticValue.ShadowNormalColor);
        }

        private void ShadowFormSkin_GotFocus(object sender, EventArgs e)
        {
            if (IsUseCustomerShadowColor)
            {
                ShadowColor = CommonSetting.贴图窗口阴影色;
                ShadowWidth = (int) Math.Max(CommonSetting.贴图窗口阴影宽度, 1);
            }

            DrawShadow(ShadowColor);
        }

        private void IrregularForm_Load(object sender, EventArgs e)
        {
            if (!DesignMode)
            {
                _skin = new ShadowFormSkin(this)
                {
                    BackColor = Color.Red
                }; //创建皮肤层
                _skin.Show(); //显示皮肤层

                ShadowShark();
                GotFocus += ShadowFormSkin_GotFocus;
                LostFocus += ShadowFormSkin_LostFocus;
            }
        }

        public void ShadowShark()
        {
            //ForceActivate();
            Task.Factory.StartNew(() =>
            {
                if (IsSharkWindow)
                    for (var i = 0; i < 7; i++)
                    {
                        if (_skin == null || _skin.IsDisposed || !_skin.Visible) return;
                        var color = Color.FromArgb(0, new Random(Guid.NewGuid().GetHashCode()).Next(0, 256),
                            new Random(Guid.NewGuid().GetHashCode()).Next(0, 256),
                            new Random(Guid.NewGuid().GetHashCode()).Next(0, 256));
                        _skin.DrawShadow(color);
                        Thread.Sleep(100);
                    }

                if (IsForceActive) this.ForceActivate();
                ShadowFormSkin_GotFocus(null, null);
            });
        }

        public void DrawShadow(Color color)
        {
            if (_skin != null)
            {
                _skin.Location = new Point(Left - ShadowWidth, Top - ShadowWidth);
                _skin.DrawShadow(color);
            }
        }

        private void ShadowForm_LocationChanged(object sender, EventArgs e)
        {
            DrawShadow(ShadowColor);
        }

        /// <summary>
        ///     窗体显示状态
        /// </summary>
        /// <param name="value"></param>
        public void Visibility(bool value, bool isOnlyShadow = true)
        {
            if (value)
            {
                if (!isOnlyShadow) Show();
                _skin.Show();
            }
            else
            {
                if (!isOnlyShadow) Hide();
                _skin.Hide();
            }
        }

        protected override void OnSizeChanged(EventArgs e)
        {
            DrawShadow(ShadowColor);
            CheckTitleBarSize();
            base.OnSizeChanged(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            if (_mRectTitle.Contains(e.Location))
            {
                FormStyleApi.ReleaseCapture();
                FormStyleApi.SendMessage(Handle, Win32.WM_SYSCOMMAND, Win32.SC_MOVE + (int) Win32.HTCAPTION, 0);
            }

            base.OnMouseDown(e);
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            if (IsShowTitle)
            {
                CheckRectToReDraw(_mRectClose, e.Location, ref _mBMouseOnClose);
                if (MaximizeBox) CheckRectToReDraw(_mRectMax, e.Location, ref _mBMouseOnMax);
                if (MinimizeBox) CheckRectToReDraw(_mRectMin, e.Location, ref _mBMouseOnMin);
            }

            if (Cursor == Cursors.Default && e.Button == MouseButtons.Left && !_mRectTitle.Contains(e.Location))
            {
                FormStyleApi.ReleaseCapture();
                FormStyleApi.SendMessage(Handle, FormStyleApi.WM_NCLBUTTONDOWN, FormStyleApi.HTCAPTION, 0);
            }

            base.OnMouseMove(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            _mBMouseOnClose = _mBMouseOnMax = _mBMouseOnMin = false;
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnClick(EventArgs e)
        {
            if (_mBMouseOnClose) Close();
            if (_mBMouseOnMax)
                WindowState = WindowState != FormWindowState.Maximized
                    ? FormWindowState.Maximized
                    : FormWindowState.Normal;
            if (_mBMouseOnMin) WindowState = FormWindowState.Minimized;
            base.OnClick(e);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;
            if (IsShowTitle) OnDrawTitle(g);
            g.DrawRectangle(Pens.Gray, 0, 0, Width - 1, Height - 1);
            base.OnPaint(e);
        }

        protected virtual void OnDrawTitle(Graphics g)
        {
            var sf = new StringFormat
            {
                LineAlignment = StringAlignment.Center
            };
            g.FillRectangle(Brushes.White, 0, 0, Width, 35);
            g.DrawIcon(Icon, new Rectangle(6, 6, 23, 23));
            g.DrawString(Text, Font, Brushes.Black, _mRectTitle, sf);
            if (_mRectClose != Rectangle.Empty)
            {
                g.DrawLine(Pens.Gray, _mRectClose.Right - 1, 0, _mRectClose.Right - 1, 34);
                if (_mBMouseOnClose) g.FillRectangle(Brushes.Red, _mRectClose);
                g.DrawLine(Pens.Gray, _mRectClose.X + 14, 14, _mRectClose.X + 21, 21);
                g.DrawLine(Pens.Gray, _mRectClose.X + 14, 21, _mRectClose.X + 21, 14);
            }

            if (_mRectMax != Rectangle.Empty)
            {
                g.DrawLine(Pens.Gray, _mRectMax.Right - 1, 0, _mRectMax.Right - 1, 34);
                if (_mBMouseOnMax) g.FillRectangle(Brushes.LightGray, _mRectMax);
                g.DrawRectangle(Pens.Gray, _mRectMax.X + 14, 14, 7, 7);
            }

            if (_mRectMin != Rectangle.Empty)
            {
                g.DrawLine(Pens.Gray, _mRectMin.Right - 1, 0, _mRectMin.Right - 1, 34);
                if (_mBMouseOnMin) g.FillRectangle(Brushes.LightGray, _mRectMin);
                g.DrawLine(Pens.Gray, _mRectMin.X + 14, 21, _mRectMin.X + 21, 21);
            }
        }

        protected override void WndProc(ref Message m)
        {
            switch ((uint) m.Msg)
            {
                case Win32.WM_NCHITTEST:
                    m.Result = OnHitTest(m);
                    return;
                case Win32.WM_GETMINMAXINFO: //在无边窗体时确定窗体最大、最小、最大化尺寸
                    var rectArea = Screen.GetWorkingArea(MousePosition);
                    var rectBounds = Screen.GetBounds(MousePosition);
                    var stMinMaxInfo = (Win32.MINMAXINFO) m.GetLParam(typeof(Win32.MINMAXINFO));
                    stMinMaxInfo.ptMinTrackSize.X = 1;
                    stMinMaxInfo.ptMinTrackSize.Y = 1;
                    //窗体最大化坐标及宽高
                    stMinMaxInfo.ptMaxPosition.X = rectArea.X - rectBounds.X;
                    stMinMaxInfo.ptMaxPosition.Y = rectArea.Y - rectBounds.Y;
                    stMinMaxInfo.ptMaxSize.X = rectArea.Width;
                    stMinMaxInfo.ptMaxSize.Y = rectArea.Height;
                    Marshal.StructureToPtr(stMinMaxInfo, m.LParam, true);
                    return;
            }

            base.WndProc(ref m);
        }

        protected virtual IntPtr OnHitTest(Message msg)
        {
            var pt = new Point((int) msg.LParam);
            pt.Offset(-Left, -Top);
            if (Sizeable)
            {
                if (pt.X < 5 && pt.Y < 5)
                    return (IntPtr) Win32.HTTOPLEFT;
                if (pt.X > Width - 5 && pt.Y < 5)
                    return (IntPtr) Win32.HTTOPRIGHT;
                if (pt.X < 5 && pt.Y > Height - 5)
                    return (IntPtr) Win32.HTBOTTOMLEFT;
                if (pt.X > Width - 5 && pt.Y > Height - 5)
                    return (IntPtr) Win32.HTBOTTOMRIGHT;

                if (pt.X < 5) return (IntPtr) Win32.HTLEFT;
                if (pt.Y < 5) return (IntPtr) Win32.HTTOP;
                if (pt.X > Width - 5) return (IntPtr) Win32.HTRIGHT;
                if (pt.Y > Height - 5) return (IntPtr) Win32.HTBOTTOM;
            }

            if (IsShowTitle && _mRectTitle.Contains(pt)) return (IntPtr) Win32.HTCAPTION;
            return (IntPtr) Win32.HTCLIENT;
        }

        private void CheckTitleBarSize()
        {
            if (IsShowTitle)
            {
                _mRectClose = new Rectangle(Width - 35, 0, 35, 35);
                if (MaximizeBox)
                    _mRectMax = new Rectangle(Width - 70, 0, 35, 35);
                else
                    _mRectMax = Rectangle.Empty;
                if (MinimizeBox)
                    _mRectMin = new Rectangle(Width - 70 - _mRectMax.Width, 0, 35, 35);
                else
                    _mRectMin = Rectangle.Empty;
                _mRectTitle = new Rectangle(35, 0, Width - 70 - _mRectMax.Width - _mRectMin.Width, 35);
            }
            else
            {
                _mRectClose = Rectangle.Empty;
                _mRectMax = Rectangle.Empty;
                _mRectMin = Rectangle.Empty;
                _mRectTitle = Rectangle.Empty;
            }
        }

        private void CheckRectToReDraw(Rectangle rect, Point pt, ref bool bFlag)
        {
            if (rect.Contains(pt))
            {
                if (!bFlag)
                {
                    bFlag = true;
                    Invalidate(rect);
                }
            }
            else
            {
                if (bFlag)
                {
                    bFlag = false;
                    Invalidate(rect);
                }
            }
        }
    }
}