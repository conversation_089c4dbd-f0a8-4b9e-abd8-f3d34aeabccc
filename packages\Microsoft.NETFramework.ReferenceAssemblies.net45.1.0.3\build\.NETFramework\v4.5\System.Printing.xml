﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Printing</name>
  </assembly>
  <members>
    <member name="T:System.Printing.EnumeratedPrintQueueTypes">
      <summary>Specifies attributes of print queues.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.EnableDevQuery">
      <summary>A print queue that holds its print jobs when the document and printer configurations do not match.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.PushedMachineConnection">
      <summary>A print queue that was installed by using the Push Printer Connections computer policy. See Remarks.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.PushedUserConnection">
      <summary>A print queue that was installed by using the Push Printer Connections user policy. See Remarks.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.Queued">
      <summary>A print queue that allows multiple print jobs in the queue.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.DirectPrinting">
      <summary>A print queue that sends a print job directly to printing instead of spooling the job first.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.PublishedInDirectoryServices">
      <summary>A print queue that is visible in the directory of printers.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.WorkOffline">
      <summary>A print queue that can work offline.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.RawOnly">
      <summary>A print queue that spools only raw data.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.EnableBidi">
      <summary>A print queue for a printer that has bidirectional communication enabled.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.KeepPrintedJobs">
      <summary>A print queue that keeps jobs in the queue after printing them.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.Fax">
      <summary>A print queue that services a fax machine.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.TerminalServer">
      <summary>A print queue that is installed by the redirection feature in Terminal Services.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.Connections">
      <summary>A print queue that is connected to the specified print server.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.Shared">
      <summary>A print queue that is shared.</summary>
    </member>
    <member name="F:System.Printing.EnumeratedPrintQueueTypes.Local">
      <summary>A print queue that is installed as a local print queue on the specified print server.</summary>
    </member>
    <member name="T:System.Printing.LocalPrintServer">
      <summary>Represents the local print server (the computer on which your application is running) and enables management of its print queues.</summary>
    </member>
    <member name="M:System.Printing.LocalPrintServer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.LocalPrintServer" /> class. </summary>
    </member>
    <member name="M:System.Printing.LocalPrintServer.#ctor(System.Printing.LocalPrintServerIndexedProperty[])">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.LocalPrintServer" /> class that has the specified <see cref="T:System.Printing.LocalPrintServerIndexedProperty" /> array. </summary>
      <param name="propertiesFilter">An array of properties that the constructor initializes.</param>
    </member>
    <member name="M:System.Printing.LocalPrintServer.#ctor(System.Printing.LocalPrintServerIndexedProperty[],System.Printing.PrintSystemDesiredAccess)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.LocalPrintServer" /> class that has the specified <see cref="T:System.Printing.LocalPrintServerIndexedProperty" /> array and the specified <see cref="T:System.Printing.PrintSystemDesiredAccess" />. </summary>
      <param name="propertiesFilter">An array of properties that the constructor initializes.</param>
      <param name="desiredAccess">A value specifying the type of access to the print server that your program needs.</param>
      <exception cref="T:System.Printing.PrintServerException">
        <paramref name="desiredAccess" /> is a value that can be applied only to a <see cref="T:System.Printing.PrintQueue" /> object, not a <see cref="T:System.Printing.LocalPrintServer" /> object. For example, <see cref="F:System.Printing.PrintSystemDesiredAccess.UsePrinter" />.</exception>
    </member>
    <member name="M:System.Printing.LocalPrintServer.#ctor(System.Printing.PrintSystemDesiredAccess)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.LocalPrintServer" /> class that has the specified <see cref="T:System.Printing.PrintSystemDesiredAccess" />. </summary>
      <param name="desiredAccess">A value specifying the type of access to the print server that your application needs.</param>
      <exception cref="T:System.Printing.PrintServerException">
        <paramref name="desiredAccess" /> is a value that can be applied only to a <see cref="T:System.Printing.PrintQueue" /> object, not a <see cref="T:System.Printing.LocalPrintServer" /> object. For example, <see cref="F:System.Printing.PrintSystemDesiredAccess.UsePrinter" />.</exception>
    </member>
    <member name="M:System.Printing.LocalPrintServer.#ctor(System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.LocalPrintServer" /> class that has the specified properties.</summary>
      <param name="propertiesFilter">An array of the property names that the constructor initializes.</param>
    </member>
    <member name="M:System.Printing.LocalPrintServer.#ctor(System.String[],System.Printing.PrintSystemDesiredAccess)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.LocalPrintServer" /> class that has the specified properties and <see cref="T:System.Printing.PrintSystemDesiredAccess" />. </summary>
      <param name="propertiesFilter">An array of the property names that the constructor initializes.</param>
      <param name="desiredAccess">A value specifying the type of access to the print server that your application needs.</param>
      <exception cref="T:System.Printing.PrintServerException">
        <paramref name="desiredAccess" /> is a value that can be applied only to a <see cref="T:System.Printing.PrintQueue" /> object, not a <see cref="T:System.Printing.LocalPrintServer" /> object. For example, <see cref="F:System.Printing.PrintSystemDesiredAccess.UsePrinter" />.</exception>
    </member>
    <member name="M:System.Printing.LocalPrintServer.Commit">
      <summary>Writes any changes that your application made to the properties of the <see cref="T:System.Printing.LocalPrintServer" /> to the actual print server that the object represents. </summary>
      <exception cref="T:System.Printing.PrintServerException">Some properties are not committed.</exception>
      <exception cref="T:System.Printing.PrintCommitAttributesException">Some of the properties could not be committed. - or -The <see cref="T:System.Printing.LocalPrintServer" /> object was not created with sufficient rights. See Remarks.</exception>
    </member>
    <member name="M:System.Printing.LocalPrintServer.ConnectToPrintQueue(System.Printing.PrintQueue)">
      <summary>Connects the local print server to the specified <see cref="T:System.Printing.PrintQueue" />. </summary>
      <returns>true if the connection is made; otherwise false.</returns>
      <param name="printer">The print queue to connect.</param>
      <exception cref="T:System.Printing.PrintServerException">A print queue that matches the <see cref="P:System.Printing.PrintQueue.FullName" /> property of the <paramref name="printer" /> is not found.</exception>
    </member>
    <member name="M:System.Printing.LocalPrintServer.ConnectToPrintQueue(System.String)">
      <summary>Connects to the print queue that is specified by using the <see cref="T:System.String" />. </summary>
      <returns>true if the connection is made; otherwise false.</returns>
      <param name="printQueuePath">The full path of the print queue that is being connected.</param>
      <exception cref="T:System.Printing.PrintServerException">A print queue with the specified path is not found.</exception>
    </member>
    <member name="P:System.Printing.LocalPrintServer.DefaultPrintQueue">
      <summary>Gets or sets the default print queue. </summary>
      <returns>The <see cref="T:System.Printing.PrintQueue" /> that is designated as the default queue for the local computer.</returns>
    </member>
    <member name="M:System.Printing.LocalPrintServer.DisconnectFromPrintQueue(System.Printing.PrintQueue)">
      <summary>Disconnects the local print server from the specified <see cref="T:System.Printing.PrintQueue" />. </summary>
      <returns>true if the disconnection is successful; otherwise false.</returns>
      <param name="printer">The print queue that is being disconnected.</param>
      <exception cref="T:System.Printing.PrintServerException">A print queue matching the <see cref="P:System.Printing.PrintQueue.FullName" /> property of the <paramref name="printer" /> is not found.</exception>
    </member>
    <member name="M:System.Printing.LocalPrintServer.DisconnectFromPrintQueue(System.String)">
      <summary>Disconnects from the print queue that is specified in the <see cref="T:System.String" />. </summary>
      <returns>true if the disconnection is successful; otherwise false.</returns>
      <param name="printQueuePath">The full path to the print queue that is disconnected.</param>
      <exception cref="T:System.Printing.PrintServerException">A print queue with the specified path is not found.</exception>
    </member>
    <member name="M:System.Printing.LocalPrintServer.GetDefaultPrintQueue">
      <summary>Returns a reference to the default print queue of the <see cref="T:System.Printing.LocalPrintServer" />. </summary>
      <returns>The default <see cref="T:System.Printing.PrintQueue" />.</returns>
    </member>
    <member name="M:System.Printing.LocalPrintServer.Refresh">
      <summary>Updates the properties of the <see cref="T:System.Printing.LocalPrintServer" /> object so that their values match the values of the print server that the object represents. </summary>
      <exception cref="T:System.Printing.PrintServerException">Some properties did not update.</exception>
    </member>
    <member name="T:System.Printing.LocalPrintServerIndexedProperty">
      <summary>Specifies the properties of a <see cref="T:System.Printing.LocalPrintServer" /> object that are initialized when it is constructed.</summary>
    </member>
    <member name="F:System.Printing.LocalPrintServerIndexedProperty.DefaultPrintQueue">
      <summary>The property that specifies the default print queue for the local print server.</summary>
    </member>
    <member name="F:System.Printing.LocalPrintServerIndexedProperty.MinorVersion">
      <summary>The property that specifies the minor version of the operating system.</summary>
    </member>
    <member name="F:System.Printing.LocalPrintServerIndexedProperty.MajorVersion">
      <summary>The property that specifies the major version of the operating system.</summary>
    </member>
    <member name="F:System.Printing.LocalPrintServerIndexedProperty.RestartJobOnPoolEnabled">
      <summary>The property that specifies whether users can restart jobs when printer pooling is being used.</summary>
    </member>
    <member name="F:System.Printing.LocalPrintServerIndexedProperty.RestartJobOnPoolTimeout">
      <summary>The property that specifies the wait time before jobs can be restarted when printer pooling is being used.</summary>
    </member>
    <member name="F:System.Printing.LocalPrintServerIndexedProperty.EventLog">
      <summary>The property that specifies the kind of event logging that is provided by the local print server.</summary>
    </member>
    <member name="F:System.Printing.LocalPrintServerIndexedProperty.NetPopup">
      <summary>The property that specifies whether the client computer or the print server receives notifications that a job is finished.</summary>
    </member>
    <member name="F:System.Printing.LocalPrintServerIndexedProperty.BeepEnabled">
      <summary>The property that specifies whether a printer error causes the local print server to beep.</summary>
    </member>
    <member name="F:System.Printing.LocalPrintServerIndexedProperty.DefaultSchedulerPriority">
      <summary>The property that specifies the default priority for the scheduler.</summary>
    </member>
    <member name="F:System.Printing.LocalPrintServerIndexedProperty.SchedulerPriority">
      <summary>The property that specifies the priority of the scheduler.</summary>
    </member>
    <member name="F:System.Printing.LocalPrintServerIndexedProperty.DefaultPortThreadPriority">
      <summary>The property that specifies the default priority for the thread that manages port I/0.</summary>
    </member>
    <member name="F:System.Printing.LocalPrintServerIndexedProperty.PortThreadPriority">
      <summary>The property that specifies the priority of the thread that manages port I/O.</summary>
    </member>
    <member name="F:System.Printing.LocalPrintServerIndexedProperty.DefaultSpoolDirectory">
      <summary>The property that specifies the path to the folder where spool jobs are located as temporary files.</summary>
    </member>
    <member name="T:System.Printing.PrintDocumentImageableArea">
      <summary>Specifies the size of the paper (or other media), the size of the imageable area, and the location of the imageable area.</summary>
    </member>
    <member name="P:System.Printing.PrintDocumentImageableArea.ExtentHeight">
      <summary>Gets the height of the imageable area. </summary>
      <returns>A <see cref="T:System.Double" /> that represents the distance from the origin.</returns>
    </member>
    <member name="P:System.Printing.PrintDocumentImageableArea.ExtentWidth">
      <summary>Gets the width of the imageable area. </summary>
      <returns>A <see cref="T:System.Double" /> that represents the distance from the origin.</returns>
    </member>
    <member name="P:System.Printing.PrintDocumentImageableArea.MediaSizeHeight">
      <summary>Gets the height of the paper or media.</summary>
      <returns>A <see cref="T:System.Double" /> that represents the distance from the upper-left corner of the page to the lower-left corner.</returns>
    </member>
    <member name="P:System.Printing.PrintDocumentImageableArea.MediaSizeWidth">
      <summary>Gets the width of the paper or media.</summary>
      <returns>A <see cref="T:System.Double" /> that represents the distance from the upper-left corner of the page to the upper-right corner.</returns>
    </member>
    <member name="P:System.Printing.PrintDocumentImageableArea.OriginHeight">
      <summary>Gets the distance from the upper-left corner of the imageable area (also called the 'origin' of the imageable area) to the nearest point on the top edge of the page.</summary>
      <returns>A <see cref="T:System.Double" /> that represents the distance (in pixels - 1/96 of an inch) from the upper-left corner of the imageable area (also called the 'origin' of the imageable area) to the nearest point on the top edge of the page.</returns>
    </member>
    <member name="P:System.Printing.PrintDocumentImageableArea.OriginWidth">
      <summary>Gets the origin width, which is the distance from the left edge of the page to the upper-left corner of the imageable area (also called the "origin" of the imageable area).</summary>
      <returns>A <see cref="T:System.Double" /> that represents the origin width (in pixels - 1/96 of an inch), which is the distance from the left edge of the page to the upper-left corner of the imageable area (also called the "origin" of the imageable area).</returns>
    </member>
    <member name="T:System.Printing.PrintDriver">
      <summary>Represents a print driver.</summary>
    </member>
    <member name="M:System.Printing.PrintDriver.Commit">
      <summary>Do not use.</summary>
    </member>
    <member name="M:System.Printing.PrintDriver.Refresh">
      <summary>Do not use.</summary>
    </member>
    <member name="T:System.Printing.PrintFilter">
      <summary>Defines disposal behavior that is common to both the <see cref="T:System.Printing.PrintDriver" /> and <see cref="T:System.Printing.PrintProcessor" /> classes. <see cref="T:System.Printing.PrintFilter" /> supports the Windows Presentation Foundation (WPF) infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Printing.PrintFilter.InternalDispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are used by the class that is derived from <see cref="T:System.Printing.PrintFilter" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="T:System.Printing.PrintJobInfoCollection">
      <summary>Represents one or more <see cref="T:System.Printing.PrintSystemJobInfo" /> objects. </summary>
    </member>
    <member name="M:System.Printing.PrintJobInfoCollection.#ctor(System.Printing.PrintQueue,System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintJobInfoCollection" /> class that contains the <see cref="T:System.Printing.PrintSystemJobInfo" /> objects for every job that is in the specified <see cref="T:System.Printing.PrintQueue" /> and that initializes those objects only in the properties that are listed in the specified property filter. </summary>
      <param name="printQueue">The print queue whose print jobs will populate the collection. </param>
      <param name="propertyFilter">A list of a subset of the properties of a <see cref="T:System.Printing.PrintSystemJobInfo" /> object. </param>
    </member>
    <member name="M:System.Printing.PrintJobInfoCollection.Add(System.Printing.PrintSystemJobInfo)">
      <summary>Adds a member to the <see cref="T:System.Printing.PrintJobInfoCollection" />. </summary>
      <param name="printObject">The object that is added.</param>
    </member>
    <member name="M:System.Printing.PrintJobInfoCollection.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are being used by the <see cref="T:System.Printing.PrintJobInfoCollection" /> and optionally releases the managed resources. </summary>
    </member>
    <member name="M:System.Printing.PrintJobInfoCollection.GetEnumerator">
      <summary>Gets an object that implements the generic <see cref="T:System.Collections.IEnumerator" /> interface that is closed with <see cref="T:System.Printing.PrintSystemJobInfo" />.</summary>
      <returns>An object that implements the generic <see cref="T:System.Collections.IEnumerator" /> interface and that can iterate through the <see cref="T:System.Printing.PrintSystemJobInfo" /> objects that the <see cref="T:System.Printing.PrintJobInfoCollection" /> contains.</returns>
    </member>
    <member name="M:System.Printing.PrintJobInfoCollection.GetNonGenericEnumerator">
      <summary>Gets an object that implements the non-generic <see cref="T:System.Collections.IEnumerator" /> interface. </summary>
      <returns>An object that implements the non-generic <see cref="T:System.Collections.IEnumerator" /> interface and that can iterate through the <see cref="T:System.Printing.PrintSystemJobInfo" /> objects that the <see cref="T:System.Printing.PrintJobInfoCollection" /> contains.</returns>
    </member>
    <member name="T:System.Printing.PrintJobPriority">
      <summary>Specifies a non-numerical priority for a print job relative to other print jobs in the print queue. </summary>
    </member>
    <member name="F:System.Printing.PrintJobPriority.Maximum">
      <summary>A job that has the highest priority.</summary>
    </member>
    <member name="F:System.Printing.PrintJobPriority.Default">
      <summary>A job that has the <see cref="P:System.Printing.PrintQueue.DefaultPriority" /> for the <see cref="T:System.Printing.PrintQueueStream" />.</summary>
    </member>
    <member name="F:System.Printing.PrintJobPriority.Minimum">
      <summary>A job that has the lowest priority.</summary>
    </member>
    <member name="F:System.Printing.PrintJobPriority.None">
      <summary>A job that has no non-numerical priority.</summary>
    </member>
    <member name="T:System.Printing.PrintJobSettings">
      <summary>Describes a print job. </summary>
    </member>
    <member name="P:System.Printing.PrintJobSettings.CurrentPrintTicket">
      <summary>Gets or sets a <see cref="T:System.Printing.PrintTicket" /> object that holds all the detailed settings for the print job.</summary>
      <returns>A <see cref="T:System.Printing.PrintTicket" /> object that holds all the details about the print job, such as the number of copies to print, and whether stapling or duplex printing is used. </returns>
    </member>
    <member name="P:System.Printing.PrintJobSettings.Description">
      <summary>Gets or sets a description of a print job. </summary>
      <returns>A <see cref="T:System.String" /> that describes the print job, for example, "Quarterly Report." </returns>
    </member>
    <member name="T:System.Printing.PrintJobStatus">
      <summary>Specifies the current status of a print job in a print queue.</summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.Retained">
      <summary>The print job is retained in the print queue after printing.</summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.Completed">
      <summary>The print job is complete, including any post-printing processing. </summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.Restarted">
      <summary>The print job was blocked but has restarted.</summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.UserIntervention">
      <summary>The printer requires user action to fix an error condition.  </summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.Blocked">
      <summary>An error condition, possibly on a print job that precedes this one in the queue, blocked the print job.</summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.Deleted">
      <summary>The print job was deleted from the queue, typically after printing.</summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.Printed">
      <summary>The print job printed. </summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.PaperOut">
      <summary>The printer is out of the required paper size.</summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.Offline">
      <summary>The printer is offline.</summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.Spooling">
      <summary>The print job is spooling.</summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.Deleting">
      <summary>The print job is in the process of being deleted.</summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.Error">
      <summary>The print job is in an error state.</summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.Paused">
      <summary>The print job is paused.</summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.None">
      <summary>The print job has no specified state.</summary>
    </member>
    <member name="F:System.Printing.PrintJobStatus.Printing">
      <summary>The print job is now printing.</summary>
    </member>
    <member name="T:System.Printing.PrintJobType">
      <summary>Specifies whether the print job uses XML Paper Specification (XPS). </summary>
    </member>
    <member name="F:System.Printing.PrintJobType.Legacy">
      <summary>A non-XPS print job.</summary>
    </member>
    <member name="F:System.Printing.PrintJobType.None">
      <summary>Not specified whether the print job is XPS.</summary>
    </member>
    <member name="F:System.Printing.PrintJobType.Xps">
      <summary>An XPS print job. </summary>
    </member>
    <member name="T:System.Printing.PrintPort">
      <summary>Represents a printer port on a print server. Each print queue has a print port assigned to it.</summary>
    </member>
    <member name="M:System.Printing.PrintPort.Commit">
      <summary>Do not use. </summary>
    </member>
    <member name="M:System.Printing.PrintPort.Refresh">
      <summary>Do not use. </summary>
    </member>
    <member name="T:System.Printing.PrintProcessor">
      <summary>Represents a print processor on a print server.</summary>
    </member>
    <member name="M:System.Printing.PrintProcessor.Commit">
      <summary>Do not use. </summary>
    </member>
    <member name="M:System.Printing.PrintProcessor.Refresh">
      <summary>Do not use. </summary>
    </member>
    <member name="T:System.Printing.PrintQueue">
      <summary>Manages printers and print jobs. </summary>
    </member>
    <member name="M:System.Printing.PrintQueue.#ctor(System.Printing.PrintServer,System.String)">
      <summary>Initializes a new instance of <see cref="T:System.Printing.PrintQueue" /> class using the specified <see cref="T:System.Printing.PrintServer" /> and queue name. </summary>
      <param name="printServer">The print server to host the print queue.</param>
      <param name="printQueueName">The name of the print queue.</param>
    </member>
    <member name="M:System.Printing.PrintQueue.#ctor(System.Printing.PrintServer,System.String,System.Int32)">
      <summary>Initializes a new instance of <see cref="T:System.Printing.PrintQueue" /> class using the specified <see cref="T:System.Printing.PrintServer" />, queue name, and print schema version. </summary>
      <param name="printServer">The print server that hosts the print queue.</param>
      <param name="printQueueName">The name of the print queue.</param>
      <param name="printSchemaVersion">The version of the Print Schema to use.</param>
    </member>
    <member name="M:System.Printing.PrintQueue.#ctor(System.Printing.PrintServer,System.String,System.Int32,System.Printing.PrintSystemDesiredAccess)">
      <summary>Initializes a new instance of <see cref="T:System.Printing.PrintQueue" /> class using the specified <see cref="T:System.Printing.PrintServer" />, queue name, print schema version, and desired access. </summary>
      <param name="printServer">The print server that hosts the print queue.</param>
      <param name="printQueueName">The name of the print queue.</param>
      <param name="printSchemaVersion">The version of the Print Schema to use.</param>
      <param name="desiredAccess">One of the <see cref="T:System.Printing.PrintSystemDesiredAccess" />   values that specifies the type of access to the print queue that your program needs.</param>
      <exception cref="T:System.Printing.PrintQueueException">
        <paramref name="desiredAccess" /> is a value that can be applied only to a <see cref="T:System.Printing.PrintServer" /> object, not a <see cref="T:System.Printing.PrintQueue" /> object. For example, <see cref="F:System.Printing.PrintSystemDesiredAccess.AdministrateServer" />.</exception>
    </member>
    <member name="M:System.Printing.PrintQueue.#ctor(System.Printing.PrintServer,System.String,System.Printing.PrintQueueIndexedProperty[])">
      <summary>Initializes a new instance of <see cref="T:System.Printing.PrintQueue" /> class using the specified <see cref="T:System.Printing.PrintServer" />, queue name, and array of <see cref="T:System.Printing.PrintQueueIndexedProperty" /> values to initialize. </summary>
      <param name="printServer">The print server that hosts the print queue.</param>
      <param name="printQueueName">The name of the print queue.</param>
      <param name="propertyFilter">An array of <see cref="T:System.Printing.PrintQueueIndexedProperty" /> values that specifies the property values to initialize.</param>
    </member>
    <member name="M:System.Printing.PrintQueue.#ctor(System.Printing.PrintServer,System.String,System.Printing.PrintQueueIndexedProperty[],System.Printing.PrintSystemDesiredAccess)">
      <summary>Initializes a new instance of <see cref="T:System.Printing.PrintQueue" /> class with the specified <see cref="T:System.Printing.PrintServer" />, queue name, array of <see cref="T:System.Printing.PrintQueueIndexedProperty" /> values to initialize, and desired access. </summary>
      <param name="printServer">The print server that hosts the print queue.</param>
      <param name="printQueueName">The name of the print queue.</param>
      <param name="propertyFilter">An array of <see cref="T:System.Printing.PrintQueueIndexedProperty" /> values that specifies the properties to initialize.</param>
      <param name="desiredAccess">One of the <see cref="T:System.Printing.PrintSystemDesiredAccess" /> values that specifies the type of access to the print queue that your program needs.</param>
      <exception cref="T:System.Printing.PrintQueueException">
        <paramref name="desiredAccess" /> is a value that can be applied only to a <see cref="T:System.Printing.PrintServer" /> object, not a <see cref="T:System.Printing.PrintQueue" /> object. For example, <see cref="F:System.Printing.PrintSystemDesiredAccess.AdministrateServer" />.</exception>
    </member>
    <member name="M:System.Printing.PrintQueue.#ctor(System.Printing.PrintServer,System.String,System.Printing.PrintSystemDesiredAccess)">
      <summary>Initializes a new instance of <see cref="T:System.Printing.PrintQueue" /> class using the specified <see cref="T:System.Printing.PrintServer" />, queue name, and desired access. </summary>
      <param name="printServer">The print server that hosts the print queue.</param>
      <param name="printQueueName">The name of the print queue.</param>
      <param name="desiredAccess">One of the <see cref="T:System.Printing.PrintSystemDesiredAccess" />   values that specifies the type of access to the print queue that your program needs.</param>
      <exception cref="T:System.Printing.PrintQueueException">
        <paramref name="desiredAccess" /> is a value that can be applied only to a <see cref="T:System.Printing.PrintServer" /> object, not a <see cref="T:System.Printing.PrintQueue" /> object. For example, <see cref="F:System.Printing.PrintSystemDesiredAccess.AdministrateServer" />.</exception>
    </member>
    <member name="M:System.Printing.PrintQueue.#ctor(System.Printing.PrintServer,System.String,System.String[])">
      <summary>Initializes a new instance of <see cref="T:System.Printing.PrintQueue" /> class using the specified <see cref="T:System.Printing.PrintServer" />, queue name, and array of property names to initialize. </summary>
      <param name="printServer">The print server that hosts the print queue.</param>
      <param name="printQueueName">The name of the print queue.</param>
      <param name="propertyFilter">An array of the names of properties to initialize.</param>
    </member>
    <member name="M:System.Printing.PrintQueue.#ctor(System.Printing.PrintServer,System.String,System.String[],System.Printing.PrintSystemDesiredAccess)">
      <summary>Initializes a new instance of <see cref="T:System.Printing.PrintQueue" /> class using the specified <see cref="T:System.Printing.PrintServer" />, queue name, property filter, and desired access. </summary>
      <param name="printServer">The print server that hosts the print queue.</param>
      <param name="printQueueName">The name of the print queue.</param>
      <param name="propertyFilter">An array of the names of properties to initialize.</param>
      <param name="desiredAccess">One of the <see cref="T:System.Printing.PrintSystemDesiredAccess" />   values that specifies the type of access to the print queue that your program needs.</param>
      <exception cref="T:System.Printing.PrintQueueException">
        <paramref name="desiredAccess" /> is a value that can be applied only to a <see cref="T:System.Printing.PrintServer" /> object, not a <see cref="T:System.Printing.PrintQueue" /> object. For example, <see cref="F:System.Printing.PrintSystemDesiredAccess.AdministrateServer" />.</exception>
    </member>
    <member name="M:System.Printing.PrintQueue.AddJob">
      <summary>Inserts a new (generically named) print job, whose content is a <see cref="T:System.Byte" /> array, into the queue. </summary>
      <returns>A <see cref="T:System.Printing.PrintSystemJobInfo" /> that represents the print job and its status.</returns>
    </member>
    <member name="M:System.Printing.PrintQueue.AddJob(System.String)">
      <summary>Inserts a new print job, whose content is a <see cref="T:System.Byte" /> array, into the queue. </summary>
      <returns>A <see cref="T:System.Printing.PrintSystemJobInfo" /> that represents the print job and its status.</returns>
      <param name="jobName">The name of the print job.</param>
    </member>
    <member name="M:System.Printing.PrintQueue.AddJob(System.String,System.Printing.PrintTicket)">
      <summary>Inserts a new print job for an XML Paper Specification (XPS) Document into the queue, gives it the specified name and settings.</summary>
      <returns>A <see cref="T:System.Printing.PrintSystemJobInfo" /> that represents the print job and its status. </returns>
      <param name="jobName">The path and name of the document that is being printed.</param>
      <param name="printTicket">The settings of the print job.</param>
    </member>
    <member name="M:System.Printing.PrintQueue.AddJob(System.String,System.String,System.Boolean)">
      <summary>Inserts a new print job for an XML Paper Specification (XPS) Document into the queue, gives it the specified name, and specifies whether or not it should be validated.</summary>
      <returns>A <see cref="T:System.Printing.PrintSystemJobInfo" /> that represents the print job and its status.</returns>
      <param name="jobName">The name of the print job.</param>
      <param name="documentPath">The path and name of the document that is being printed.</param>
      <param name="fastCopy">true to spool quickly without page-by-page progress feedback and without validating that the file is valid XPS; otherwise, false. </param>
    </member>
    <member name="P:System.Printing.PrintQueue.AveragePagesPerMinute">
      <summary>Gets the speed of the printer measured in pages per minute. </summary>
      <returns>The average pages printed per minute of the printer.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.ClientPrintSchemaVersion">
      <summary>Gets the version of the Print Schema. </summary>
      <returns>The version of the Print Schema in use.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.Comment">
      <summary>Gets or sets a comment about the printer. </summary>
      <returns>A comment about the printer.</returns>
    </member>
    <member name="M:System.Printing.PrintQueue.Commit">
      <summary>Writes the current properties of the <see cref="T:System.Printing.PrintQueue" /> object to the actual print queue on the print server. </summary>
      <exception cref="T:System.Printing.PrintSystemException">Some of the properties could not be committed. </exception>
      <exception cref="T:System.Printing.PrintCommitAttributesException">Some of the properties could not be committed. - or -The <see cref="T:System.Printing.PrintQueue" /> object was not created with sufficient rights.</exception>
    </member>
    <member name="M:System.Printing.PrintQueue.CreateXpsDocumentWriter(System.Double@,System.Double@)">
      <summary>Creates an <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> object with the specified dimensions. </summary>
      <returns>An <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> that writes to an XPS stream. This can be null. </returns>
      <param name="width">The width of the XPS document.</param>
      <param name="height">The height of the XPS document.</param>
    </member>
    <member name="M:System.Printing.PrintQueue.CreateXpsDocumentWriter(System.Printing.PrintDocumentImageableArea@)">
      <summary>Creates an <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> object, opens a Windows common print dialog and returns a ref (ByRef in Visual Basic) parameter that represents information about the imageable area and the dimensions of the media.</summary>
      <returns>An <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> that writes XPS data to a stream. This can be null. (The parameter <paramref name="documentImageableArea" /> is a ref [ByRef in Visual Basic] parameter that is initialized by the method, so it represents a second returned item.) </returns>
      <param name="documentImageableArea">A reference to an object that contains the dimensions of the area of the page on which the device can print. Since its data type has no public constructor, this parameter is passed uninitialized.</param>
    </member>
    <member name="M:System.Printing.PrintQueue.CreateXpsDocumentWriter(System.Printing.PrintDocumentImageableArea@,System.Windows.Controls.PageRangeSelection@,System.Windows.Controls.PageRange@)">
      <summary>Creates an <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> object, opens a Windows common print dialog, provides the dialog with a page range and a description of the print job, and returns a ref (ByRef in Visual Basic) parameter that represents information about the imageable area and the dimensions of the media.</summary>
      <returns>An <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> that writes XPS data to a stream. This can be null. (The parameters <paramref name="documentImageableArea" />, <paramref name="pageRangeSelection" />, and <paramref name="pageRange" /> are all ref [ByRef in Visual Basic] parameters that are initialized by the user and returned when the dialog is closed, so each represents an additional returned item.) </returns>
      <param name="documentImageableArea">A reference to an object that contains the dimensions of the area of the page on which the device can print. Since its data type has no public constructor, this parameter is passed uninitialized.</param>
      <param name="pageRangeSelection">A value that specifies whether to print all pages or only a range that is specified by the user.</param>
      <param name="pageRange">The range of pages that is printed.</param>
    </member>
    <member name="M:System.Printing.PrintQueue.CreateXpsDocumentWriter(System.Printing.PrintQueue)">
      <summary>Creates an <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> object and associates it with the specified print queue. </summary>
      <returns>An <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> that writes to an XPS stream. </returns>
      <param name="printQueue">A print queue to print the XPS document.</param>
    </member>
    <member name="M:System.Printing.PrintQueue.CreateXpsDocumentWriter(System.String,System.Printing.PrintDocumentImageableArea@)">
      <summary>Creates an <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> object, opens a Windows common print dialog (and provides it a job description) and returns a ref (ByRef in Visual Basic) parameter that represents information about the imageable area and the dimensions of the media.</summary>
      <returns>An <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> that writes XPS data to a stream. This can be null. (The parameter <paramref name="documentImageableArea" /> is a ref [ByRef in Visual Basic] parameter that is initialized by the method, so it represents a second returned item.) </returns>
      <param name="jobDescription">A name for the print job. It appears in the Windows printing user interface. </param>
      <param name="documentImageableArea">A reference to an object that contains the dimensions of the area of the page on which the device can print. Since its data type has no public constructor, this parameter is passed uninitialized.</param>
    </member>
    <member name="M:System.Printing.PrintQueue.CreateXpsDocumentWriter(System.String,System.Printing.PrintDocumentImageableArea@,System.Windows.Controls.PageRangeSelection@,System.Windows.Controls.PageRange@)">
      <summary>Creates an <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> object, opens a Windows common print dialog, provides the dialog with a page range, and returns a ref (ByRef in Visual Basic) parameter that represents information about the imageable area and the dimensions of the media.</summary>
      <returns>An <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> that writes XPS data to a stream. This can be null. (The parameters <paramref name="documentImageableArea" />, <paramref name="pageRangeSelection" />, and <paramref name="pageRange" /> are all ref [ByRef in Visual Basic] parameters that are initialized by the user and returned when the dialog is closed, so each represents an additional returned item.)</returns>
      <param name="jobDescription">A name for the print job. It appears in the Windows printing user interface. </param>
      <param name="documentImageableArea">A reference to an object that contains the dimensions of the area of the page on which the device can print. Since its data type has no public constructor, this parameter is passed uninitialized.</param>
      <param name="pageRangeSelection">A value that specifies whether to print all pages or only a range that is specified by the user.</param>
      <param name="pageRange">The range of pages that is printed.</param>
    </member>
    <member name="P:System.Printing.PrintQueue.CurrentJobSettings">
      <summary>Gets an object that contains the configuration settings for the current print job. </summary>
      <returns>A <see cref="T:System.Printing.PrintJobSettings" /> value that holds the settings of the currently printing job. These settings include a description of the job and a reference to the job's <see cref="T:System.Printing.PrintTicket" />. </returns>
    </member>
    <member name="P:System.Printing.PrintQueue.DefaultPrintTicket">
      <summary>Gets or sets the default printer options associated with this <see cref="T:System.Printing.PrintQueue" />.</summary>
      <returns>The default <see cref="T:System.Printing.PrintTicket" /> for the print queue; or null if an error has occurred in the print queue.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.DefaultPriority">
      <summary>Gets or sets the default priority that is given to each new print job added to the queue. </summary>
      <returns>The default priority for print jobs added to the queue. Possible values range from 1 through 99. The default is 1. </returns>
    </member>
    <member name="P:System.Printing.PrintQueue.Description">
      <summary>Gets a description of the print queue. </summary>
      <returns>A description of the print queue.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.FullName">
      <summary>Gets the complete name of the queue. </summary>
      <returns>The complete name of the print queue.</returns>
    </member>
    <member name="M:System.Printing.PrintQueue.GetJob(System.Int32)">
      <summary>Gets the print job with the specified ID number. </summary>
      <returns>A <see cref="T:System.Printing.PrintSystemJobInfo" /> that specifies the properties of the job and its status.</returns>
      <param name="jobId">The number of the job in the queue.</param>
    </member>
    <member name="M:System.Printing.PrintQueue.GetPrintCapabilities">
      <summary>Gets a <see cref="T:System.Printing.PrintCapabilities" /> object that identifies the capabilities of the printer. </summary>
      <returns>A <see cref="T:System.Printing.PrintCapabilities" /> object that specifies what the printer can and cannot do, such as two-sided coping or automatic stapling. </returns>
      <exception cref="T:System.Printing.PrintQueueException">The <see cref="T:System.Printing.PrintCapabilities" /> object could not be retrieved.</exception>
    </member>
    <member name="M:System.Printing.PrintQueue.GetPrintCapabilities(System.Printing.PrintTicket)">
      <summary>Gets a <see cref="T:System.Printing.PrintCapabilities" /> object that identifies the capabilities of the printer. </summary>
      <returns>A <see cref="T:System.Printing.PrintCapabilities" /> object that specifies what the printer can and cannot do, such as two-sided coping or automatic stapling. </returns>
      <param name="printTicket">A print ticket that provides the basis on which the print capabilities are reported. </param>
      <exception cref="T:System.Printing.PrintQueueException">The <see cref="T:System.Printing.PrintCapabilities" /> object could not be retrieved.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="printTicket" /> is not well-formed.</exception>
    </member>
    <member name="M:System.Printing.PrintQueue.GetPrintCapabilitiesAsXml">
      <summary>Gets a <see cref="T:System.IO.MemoryStream" /> object that specifies the printer's capabilities as an XML stream that complies with the Print Schema. </summary>
      <returns>A <see cref="T:System.IO.MemoryStream" /> specifying the printer's capabilities by using the XML schema "PrintCapabilities," a part of the Print Schema system.</returns>
      <exception cref="T:System.Printing.PrintQueueException">The print capabilities could not be retrieved.</exception>
    </member>
    <member name="M:System.Printing.PrintQueue.GetPrintCapabilitiesAsXml(System.Printing.PrintTicket)">
      <summary>Gets a <see cref="T:System.IO.MemoryStream" /> object that specifies the printer's capabilities in an XML format that complies with the Print Schema. </summary>
      <returns>A <see cref="T:System.IO.MemoryStream" /> specifying the printer's capabilities by using the XML schema "PrintCapabilities," a part of the Print Schema system.</returns>
      <param name="printTicket">A print ticket that provides the basis on which the print capabilities are reported. </param>
      <exception cref="T:System.Printing.PrintQueueException">The print capabilities could not be retrieved.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="printTicket" /> is not well-formed.</exception>
    </member>
    <member name="M:System.Printing.PrintQueue.GetPrintJobInfoCollection">
      <summary>Creates a collection that contains a <see cref="T:System.Printing.PrintSystemJobInfo" /> object for each job in the queue. </summary>
      <returns>Returns a <see cref="T:System.Printing.PrintJobInfoCollection" /> of <see cref="T:System.Printing.PrintSystemJobInfo" /> objects. There is one for each job in the queue.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.HasPaperProblem">
      <summary>Gets a value that indicates if the printer is having an unspecified paper problem. </summary>
      <returns>true if there is an unspecified paper problem; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.HasToner">
      <summary>Gets a value that indicates if the printer has toner. </summary>
      <returns>true if the current printer has toner; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.HostingPrintServer">
      <summary>Gets or sets (protected) the print server that controls the print queue. </summary>
      <returns>The name and other properties of the <see cref="T:System.Printing.PrintServer" /> that is hosting the print queue.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.InPartialTrust">
      <summary>Gets or sets a value that indicates whether the queue is operating in a partially trusted mode, a higher level of trust. </summary>
      <returns>true if the queue is operating in a partially trusted mode; otherwise, false.</returns>
    </member>
    <member name="M:System.Printing.PrintQueue.InternalDispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Printing.PrintQueue" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="P:System.Printing.PrintQueue.IsBidiEnabled">
      <summary>Gets a value that indicates whether bidirectional communication with the printer is enabled. </summary>
      <returns>true if bidirectional communication with the printer is enabled; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsBusy">
      <summary>Gets a value that indicates whether the printing device is busy. </summary>
      <returns>true if the device is busy; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsDevQueryEnabled">
      <summary>Gets a value that indicates whether the queue holds documents when document and printer configurations do not match. </summary>
      <returns>true if the queue holds mismatched configurations; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsDirect">
      <summary>Gets a value that indicates whether the queue prints directly to the printer or spools documents first and then prints them. </summary>
      <returns>true if the queue prints directly to the printer; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsDoorOpened">
      <summary>Gets a value that indicates whether a door is open on the printer. </summary>
      <returns>true if a door is open; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsHidden">
      <summary>Gets a value that indicates whether the print queue is hidden in your application's user interface. </summary>
      <returns>true if the print queue is hidden in the Windows user interface; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsInError">
      <summary>Gets a value that indicates whether the printer or device is in an error condition. </summary>
      <returns>true if the device is in an error condition; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsInitializing">
      <summary>Gets a value that indicates whether the printer is initializing itself. </summary>
      <returns>true if the printer is initializing; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsIOActive">
      <summary>Gets a value that indicates whether the printer is receiving or sending data or signals. </summary>
      <returns>true if the printer is receiving or sending; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsManualFeedRequired">
      <summary>Gets a value that indicates whether the printer needs to be manually fed paper for the current print job. </summary>
      <returns>true if the printer needs to be manually fed paper; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsNotAvailable">
      <summary>Gets a value that indicates whether the printer is available. </summary>
      <returns>true if the printer is available; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsOffline">
      <summary>Gets a value that indicates whether the printer is offline. </summary>
      <returns>true if the printer is offline; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsOutOfMemory">
      <summary>Gets a value that indicates whether the printer is out of memory. </summary>
      <returns>true if the printer is out of memory; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsOutOfPaper">
      <summary>Gets a value that indicates whether the printer needs to be reloaded with paper of the size required for the current job. </summary>
      <returns>true if the printer needs to be reloaded; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsOutputBinFull">
      <summary>Gets a value that indicates whether the output area of the printer is in danger of overflowing. </summary>
      <returns>true if the output area of the printer is full; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsPaperJammed">
      <summary>Gets a value that indicates whether the current sheet of paper is stuck in the printer. </summary>
      <returns>true if the paper is stuck; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsPaused">
      <summary>Gets a value that indicates whether the print queue has been paused. </summary>
      <returns>true if the print queue has been paused; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsPendingDeletion">
      <summary>Gets a value that indicates whether the printer is in the process of deleting a print job. </summary>
      <returns>true if the printer is deleting a job; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsPowerSaveOn">
      <summary>Gets a value that indicates whether the printer is in power save mode. </summary>
      <returns>true if the printer is in power save mode; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsPrinting">
      <summary>Gets a value that indicates whether a job is printing. </summary>
      <returns>true if a job is printing; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsProcessing">
      <summary>Gets a value that indicates whether the printer is processing a print job. </summary>
      <returns>true if the printer is processing a print job; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsPublished">
      <summary>Gets a value that indicates whether the printer is visible to other network users. </summary>
      <returns>true if the printer is visible to other network users; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsQueued">
      <summary>Gets a value that indicates whether the printer can support a queue with more than one print job in it at a time. </summary>
      <returns>true if the printer can support the queuing of multiple print jobs; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsRawOnlyEnabled">
      <summary>Gets a value that indicates whether the print queue can use EMF (Enhanced Meta File) that enables faster data flow from a printing application to the Windows spooler. </summary>
      <returns>true if the printer cannot use EMF printing; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsServerUnknown">
      <summary>Gets a value that indicates whether the printer is in an error state. </summary>
      <returns>true if in the printer is in an error state; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsShared">
      <summary>Gets a value that indicates whether the printer is available for use by other computers on the network. </summary>
      <returns>true if the printer is shared; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsTonerLow">
      <summary>Gets a value that indicates whether the printer is running short of toner. </summary>
      <returns>true if the printer is running short of toner; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsWaiting">
      <summary>Gets a value that indicates whether the queue is waiting for a job to be added. </summary>
      <returns>true if the queue is waiting for a job; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsWarmingUp">
      <summary>Gets a value that indicates whether the printer is warming up. </summary>
      <returns>true if the printer is warming up; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.IsXpsDevice">
      <summary>Gets a value that indicates whether the printer's driver is built on the Printing Overview so it uses XML Paper Specification (XPS) as its page description language. </summary>
      <returns>true if the printer uses the Printing Overview; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.KeepPrintedJobs">
      <summary>Gets a value that indicates whether the queue is saving the printer language file instead of deleting it following printing. </summary>
      <returns>true if the queue is saving the printer language file; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.Location">
      <summary>Gets or sets the printer's physical location. </summary>
      <returns>The printer's physical location.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.MaxPrintSchemaVersion">
      <summary>Gets the most recent possible version number of the Print Schema that the queue can use. </summary>
      <returns>The most recent version number of the Print Schema that the queue can use.</returns>
    </member>
    <member name="M:System.Printing.PrintQueue.MergeAndValidatePrintTicket(System.Printing.PrintTicket,System.Printing.PrintTicket)">
      <summary>Merges two <see cref="T:System.Printing.PrintTicket" />s and guarantees that the resulting <see cref="T:System.Printing.PrintTicket" /> is valid and does not ask for any printing functionality that the printer does not support. </summary>
      <returns>A <see cref="T:System.Printing.ValidationResult" /> that includes the merged <see cref="T:System.Printing.PrintTicket" /> and an indication of whether any of its settings had to be changed to guarantee viability.</returns>
      <param name="basePrintTicket">The first print ticket. </param>
      <param name="deltaPrintTicket">The second print ticket. This can be null. </param>
      <exception cref="T:System.ArgumentException">At least one of the input print tickets is not valid. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="basePrintTicket" /> is null.</exception>
      <exception cref="T:System.Printing.PrintQueueException">The validation, merger, and viability checking operation failed.</exception>
    </member>
    <member name="M:System.Printing.PrintQueue.MergeAndValidatePrintTicket(System.Printing.PrintTicket,System.Printing.PrintTicket,System.Printing.PrintTicketScope)">
      <summary>Merges two <see cref="T:System.Printing.PrintTicket" />s and guarantees that the resulting <see cref="T:System.Printing.PrintTicket" /> is valid, does not ask for any printing functionality that the printer does not support, and is limited to the specified scope. </summary>
      <returns>A <see cref="T:System.Printing.ValidationResult" /> that includes the merged <see cref="T:System.Printing.PrintTicket" /> and an indication of whether any of its settings had to be changed to guarantee viability.</returns>
      <param name="basePrintTicket">The first print ticket. </param>
      <param name="deltaPrintTicket">The second print ticket. This can be null. </param>
      <param name="scope">A value indicating whether the scope of <paramref name="deltaPrintTicket" />, and the scope of the print ticket returned in the <see cref="T:System.Printing.ValidationResult" />, is a page, a document, or the whole job. </param>
      <exception cref="T:System.ArgumentException">At least one of the input print tickets is not valid. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="basePrintTicket" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="scope" /> parameter does not have a valid <see cref="T:System.Printing.PrintTicketScope" /> value.</exception>
      <exception cref="T:System.Printing.PrintQueueException">The validation, merger, and viability checking operation failed.</exception>
    </member>
    <member name="P:System.Printing.PrintQueue.Name">
      <summary>Gets or sets the print queue's name. </summary>
      <returns>The name of the print queue.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.NeedUserIntervention">
      <summary>Gets a value that indicates whether the printer needs the attention of a human being. </summary>
      <returns>true if the printer needs human attention; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.NumberOfJobs">
      <summary>Gets the total number of jobs lined up in the print queue. </summary>
      <returns>The number of jobs in the queue.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.PagePunt">
      <summary>Gets a value that indicates whether the printer is unable to print the current page. </summary>
      <returns>true if the printer is unable to print the current page; otherwise, false.</returns>
    </member>
    <member name="M:System.Printing.PrintQueue.Pause">
      <summary>Pauses the print queue. It remains paused until <see cref="M:System.Printing.PrintQueue.Resume" /> is executed. </summary>
      <exception cref="T:System.Printing.PrintSystemException">The printer cannot be paused. </exception>
    </member>
    <member name="P:System.Printing.PrintQueue.PrintingIsCancelled">
      <summary>Gets or sets a value that indicates whether the current print job is being cancelled. </summary>
      <returns>true if the print job is being cancelled; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.Priority">
      <summary>Gets or sets the priority of the print queue relative to other print queues that are hosted by the same print server and that use the same physical printer. </summary>
      <returns>The priority for the print queue. Possible values are from 1 through 99. The default is 1.</returns>
    </member>
    <member name="M:System.Printing.PrintQueue.Purge">
      <summary>Removes all the jobs in the print queue. </summary>
      <exception cref="T:System.Printing.PrintSystemException">Some print jobs could not be removed from the queue. </exception>
    </member>
    <member name="P:System.Printing.PrintQueue.QueueAttributes">
      <summary>Gets the properties of the print queue. </summary>
      <returns>A bitwise combination of the <see cref="T:System.Printing.PrintQueueAttributes" /> enumeration values. </returns>
    </member>
    <member name="P:System.Printing.PrintQueue.QueueDriver">
      <summary>Gets or sets the printer driver for the queue. </summary>
      <returns>The <see cref="T:System.Printing.PrintDriver" /> that the queue uses.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.QueuePort">
      <summary>Gets or sets the port that the queue uses. </summary>
      <returns>The <see cref="T:System.Printing.PrintPort" /> that is assigned to the print queue.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.QueuePrintProcessor">
      <summary>Gets or sets the print processor that the queue uses. </summary>
      <returns>The <see cref="T:System.Printing.PrintProcessor" /> that the queue uses, such as WinPrint or ModiPrint. </returns>
    </member>
    <member name="P:System.Printing.PrintQueue.QueueStatus">
      <summary>Gets a value that represents the status of the printer. These include "warming up," "initializing," "printing," and others. </summary>
      <returns>The current <see cref="T:System.Printing.PrintQueueStatus" /> value.</returns>
    </member>
    <member name="M:System.Printing.PrintQueue.Refresh">
      <summary>Updates the properties of the <see cref="T:System.Printing.PrintQueue" /> object with values from the printer and the print queue utility that runs on the computer. </summary>
      <exception cref="T:System.Printing.PrintSystemException">Some of the properties could not be refreshed. </exception>
    </member>
    <member name="M:System.Printing.PrintQueue.Resume">
      <summary>Restarts a print queue that was paused. </summary>
      <exception cref="T:System.Printing.PrintSystemException">The printer cannot resume. </exception>
    </member>
    <member name="P:System.Printing.PrintQueue.ScheduleCompletedJobsFirst">
      <summary>Gets a value that indicates whether the printer prints jobs that have completed the spooling process before jobs that have not fully spooled even if the latter entered the queue first or have a higher priority. </summary>
      <returns>true if the printer prints jobs that have completed the spooling process before jobs that have not fully spooled; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.SeparatorFile">
      <summary>Gets or sets the path and file name of a file that is inserted at the beginning of each print job. </summary>
      <returns>The path and file name of the separator file.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.ShareName">
      <summary>Gets or sets a name for the printer that is seen by users on the network when it is shared. </summary>
      <returns>The public name of a shared printer.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.StartTimeOfDay">
      <summary>Gets or sets the earliest time of day, expressed as the number of minutes after midnight Coordinated Universal Time (UTC) (also called Greenwich Mean Time [GMT]), that the printer will print a job. </summary>
      <returns>The time of day that the printer first becomes available, expressed as the number of minutes after midnight (UTC). The maximum value is 1439. When a printer is first installed by using the Microsoft Windows Add Printer Wizard, the printer defaults to being available all the time, and this property returns 0 in all time zones.</returns>
    </member>
    <member name="P:System.Printing.PrintQueue.UntilTimeOfDay">
      <summary>Gets or sets the latest time, expressed as the number of minutes after midnight Coordinated Universal Time (UTC) (also called Greenwich Mean Time [GMT]), that the printer will print a job. </summary>
      <returns>The time of day that the printer is no longer available, expressed as the number of minutes after midnight (UTC). The maximum value is 1439. When a printer is first installed by using the Microsoft Windows Add Printer Wizard, the printer defaults to being available all the time, and this property returns 0 in all time zones. </returns>
    </member>
    <member name="P:System.Printing.PrintQueue.UserPrintTicket">
      <summary>Gets or sets the current user's default <see cref="T:System.Printing.PrintTicket" /> object which contains detailed information about the print job. </summary>
      <returns>The <see cref="T:System.Printing.PrintTicket" /> for the current user, or null if a user <see cref="T:System.Printing.PrintTicket" /> has not been specified.</returns>
    </member>
    <member name="T:System.Printing.PrintQueueAttributes">
      <summary>Specifies the attributes of a print queue and its printer.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueAttributes.Published">
      <summary>The print queue is visible to other network users.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueAttributes.RawOnly">
      <summary>The print queue cannot use enhanced metafile (EMF) printing.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueAttributes.EnableBidi">
      <summary>The printer's bidirectional communication is enabled.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueAttributes.ScheduleCompletedJobsFirst">
      <summary>The queue prints a fully spooled job before it prints higher priority jobs that are still spooling.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueAttributes.KeepPrintedJobs">
      <summary>The printer language file is not deleted after the file prints.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueAttributes.EnableDevQuery">
      <summary>The queue holds its jobs when the document and printer configurations do not match.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueAttributes.Hidden">
      <summary>The print queue is not visible in the application UI.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueAttributes.Shared">
      <summary>The print queue is shared.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueAttributes.Queued">
      <summary>The print queue can hold more than one print job at a time.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueAttributes.None">
      <summary>No print queue attribute is specified.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueAttributes.Direct">
      <summary>The print queue sends print jobs immediately to the printer instead of spooling jobs first.</summary>
    </member>
    <member name="T:System.Printing.PrintQueueCollection">
      <summary>Represents a collection of <see cref="T:System.Printing.PrintQueue" /> objects. </summary>
    </member>
    <member name="M:System.Printing.PrintQueueCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintQueueCollection" /> class. </summary>
    </member>
    <member name="M:System.Printing.PrintQueueCollection.#ctor(System.Printing.PrintServer,System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintQueueCollection" /> class for the specified <see cref="T:System.Printing.PrintServer" />. </summary>
      <param name="printServer">The print server that hosts the collection.</param>
      <param name="propertyFilter">The properties of the collection members that are initialized. </param>
    </member>
    <member name="M:System.Printing.PrintQueueCollection.#ctor(System.Printing.PrintServer,System.String[],System.Printing.EnumeratedPrintQueueTypes[])">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintQueueCollection" /> class for the specified <see cref="T:System.Printing.PrintServer" />, containing only the print server's queues of the specified <see cref="T:System.Printing.EnumeratedPrintQueueTypes" />. </summary>
      <param name="printServer">The print server that hosts the collection.</param>
      <param name="propertyFilter">The properties of the collection members that are initialized. </param>
      <param name="enumerationFlag">An array that specifies the types of print queues that are included in the collection. </param>
    </member>
    <member name="M:System.Printing.PrintQueueCollection.Add(System.Printing.PrintQueue)">
      <summary>Adds a <see cref="T:System.Printing.PrintQueue" /> to the collection.</summary>
      <param name="printObject">The print queue that is added.</param>
    </member>
    <member name="M:System.Printing.PrintQueueCollection.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are being used by the <see cref="T:System.Printing.PrintQueueCollection" />, and optionally releases the managed resources that are being used. </summary>
    </member>
    <member name="M:System.Printing.PrintQueueCollection.GetEnumerator">
      <summary>Returns an object that implements the generic <see cref="T:System.Collections.IEnumerator" /> interface that has been closed with <see cref="T:System.Printing.PrintQueue" />. </summary>
      <returns>An object that implements the generic <see cref="T:System.Collections.IEnumerator" /> and that can iterate through the <see cref="T:System.Printing.PrintQueue" /> objects that the <see cref="T:System.Printing.PrintQueueCollection" /> contains.</returns>
    </member>
    <member name="M:System.Printing.PrintQueueCollection.GetNonGenericEnumerator">
      <summary>Gets an object that implements the non-generic <see cref="T:System.Collections.IEnumerator" /> interface. </summary>
      <returns>An object that implements the non-generic <see cref="T:System.Collections.IEnumerator" /> and that can iterate through the <see cref="T:System.Printing.PrintQueue" /> objects that the <see cref="T:System.Printing.PrintQueueCollection" /> contains.</returns>
    </member>
    <member name="P:System.Printing.PrintQueueCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Printing.PrintQueueCollection" />.</summary>
      <returns>A <see cref="T:System.Object" /> that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="T:System.Printing.PrintQueueIndexedProperty">
      <summary>Specifies the properties that are initialized when a <see cref="T:System.Printing.PrintQueue" /> object is constructed.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.DefaultPrintTicket">
      <summary>The default print ticket object.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.UserPrintTicket">
      <summary>The print ticket for the user.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.SeparatorFile">
      <summary>The path to the separator file.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.QueueStatus">
      <summary>The current status of the queue.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.HostingPrintServer">
      <summary>The host print server.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.QueuePrintProcessor">
      <summary>The print processor for the print queue.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.QueuePort">
      <summary>The printer port used by the print queue.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.QueueDriver">
      <summary>The printer driver for the queue.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.QueueAttributes">
      <summary>The attributes of the print queue.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.NumberOfJobs">
      <summary>The number of jobs in the print queue.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.AveragePagesPerMinute">
      <summary>The speed of the print queue.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.UntilTimeOfDay">
      <summary>The time of day that the queue stops printing jobs.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.StartTimeOfDay">
      <summary>The time of day that the queue begins printing its jobs.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.DefaultPriority">
      <summary>The default priority. </summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.Comment">
      <summary>A comment specific to the print queue.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.ShareName">
      <summary>The share name of the queue.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.Name">
      <summary>The name of the print queue.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.Description">
      <summary>The description of the queue.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.Location">
      <summary>The location of the physical printer.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueIndexedProperty.Priority">
      <summary>The priority of the print queue relative to other print queues serving the same printer.</summary>
    </member>
    <member name="T:System.Printing.PrintQueueStatus">
      <summary>Specifies the status of a print queue or its printer. </summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.PowerSave">
      <summary>The printer is in power save mode.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.ServerUnknown">
      <summary>The printer is in an error state.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.DoorOpen">
      <summary>A door on the printer is open.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.OutOfMemory">
      <summary>The printer has no available memory.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.UserIntervention">
      <summary>The printer requires user action to correct an error condition.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.PagePunt">
      <summary>The printer is unable to print the current page.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.NoToner">
      <summary>The printer is out of toner.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.TonerLow">
      <summary>Only a small amount of toner remains in the printer.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.WarmingUp">
      <summary>The printer is warming up.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.Initializing">
      <summary>The printer is initializing.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.Processing">
      <summary>The device is doing some kind of work, which need not be printing if the device is a combination printer, fax machine, and scanner.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.Waiting">
      <summary>The printer is waiting for a print job.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.NotAvailable">
      <summary>Status information is unavailable.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.OutputBinFull">
      <summary>The printer's output bin is full.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.Busy">
      <summary>The printer is busy.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.IOActive">
      <summary>The printer is exchanging data with the print server.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.Offline">
      <summary>The printer is offline.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.PaperProblem">
      <summary>The paper in the printer is causing an unspecified error condition.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.ManualFeed">
      <summary>The printer is waiting for a user to place print media in the manual feed bin.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.PaperOut">
      <summary>The printer does not have, or is out of, the type of paper needed for the current print job.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.PaperJam">
      <summary>The paper in the printer is jammed.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.PendingDeletion">
      <summary>The print queue is deleting a print job.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.Error">
      <summary>The printer cannot print due to an error condition.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.Paused">
      <summary>The print queue is paused.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.None">
      <summary>Status is not specified.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStatus.Printing">
      <summary>The device is printing. </summary>
    </member>
    <member name="T:System.Printing.PrintQueueStream">
      <summary>A stream that represents a spooled print job in a print queue.</summary>
    </member>
    <member name="M:System.Printing.PrintQueueStream.#ctor(System.Printing.PrintQueue,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintQueueStream" /> class for the specified print job that is hosted in the specified <see cref="T:System.Printing.PrintQueue" />. </summary>
      <param name="printQueue">The <see cref="T:System.Printing.PrintQueue" /> that hosts the print job that provides the content of the stream.</param>
      <param name="printJobName">The name of the print job that provides the content of the stream.</param>
    </member>
    <member name="M:System.Printing.PrintQueueStream.#ctor(System.Printing.PrintQueue,System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintQueueStream" /> class for the specified print job that is hosted in the specified <see cref="T:System.Printing.PrintQueue" />, with an indication of whether data in the <see cref="T:System.Printing.PrintQueueStream" /> should be committed when the stream is closed. </summary>
      <param name="printQueue">The <see cref="T:System.Printing.PrintQueue" /> that hosts the print job that provides the content of the stream.</param>
      <param name="printJobName">The name of the print job that provides the content of the stream.</param>
      <param name="commitDataOnClose">true to commit data in the <see cref="T:System.Printing.PrintQueueStream" /> when the <see cref="M:System.Printing.PrintQueueStream.Close" /> method is called; otherwise, false.</param>
    </member>
    <member name="M:System.Printing.PrintQueueStream.#ctor(System.Printing.PrintQueue,System.String,System.Boolean,System.Printing.PrintTicket)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintQueueStream" /> class for the specified print job that is hosted in the specified <see cref="T:System.Printing.PrintQueue" />, with the specified settings and an indication of whether data in the <see cref="T:System.Printing.PrintQueueStream" /> should be committed when the stream is closed.</summary>
      <param name="printQueue">The <see cref="T:System.Printing.PrintQueue" /> that hosts the print job that provides the content of the stream.</param>
      <param name="printJobName">The name of the print job that provides the content of the stream.</param>
      <param name="commitDataOnClose">true to commit data in the <see cref="T:System.Printing.PrintQueueStream" /> when the <see cref="M:System.Printing.PrintQueueStream.Close" /> method is called; otherwise, false.</param>
      <param name="printTicket">The settings of the print job.</param>
    </member>
    <member name="M:System.Printing.PrintQueueStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous write operation. </summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous write, which might still be pending.</returns>
      <param name="buffer">The buffer from which to write data.</param>
      <param name="offset">The byte offset in the buffer from which to begin writing.</param>
      <param name="count">The maximum number of bytes to write.</param>
      <param name="callback">An asynchronous callback, which is called when the writing operation is complete.</param>
      <param name="state">A user-provided object that distinguishes this asynchronous writing request from other requests.</param>
    </member>
    <member name="P:System.Printing.PrintQueueStream.CanRead">
      <summary>Gets a value that indicates whether the stream supports reading.</summary>
      <returns>true if reading is supported; otherwise false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueueStream.CanSeek">
      <summary>Gets a value that indicates whether the stream supports seeking, which is moving the read/write position to a new position in the stream.</summary>
      <returns>true if seeking is supported; otherwise false.</returns>
    </member>
    <member name="P:System.Printing.PrintQueueStream.CanWrite">
      <summary>Gets a value that indicates whether the stream supports writing.</summary>
      <returns>true if writing is supported; otherwise false.</returns>
    </member>
    <member name="M:System.Printing.PrintQueueStream.Close">
      <summary>Closes the stream and releases any resources, such as sockets and file handles, that are associated with it.</summary>
    </member>
    <member name="M:System.Printing.PrintQueueStream.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are used by the <see cref="T:System.Printing.PrintQueueStream" /> and optionally releases the managed resources.</summary>
    </member>
    <member name="M:System.Printing.PrintQueueStream.EndWrite(System.IAsyncResult)">
      <summary>Ends an asynchronous write operation.</summary>
      <param name="asyncResult">A reference to the pending asynchronous I/O request.</param>
    </member>
    <member name="M:System.Printing.PrintQueueStream.Finalize">
      <summary>Enables a <see cref="T:System.Printing.PrintQueueStream" /> to attempt to free resources and perform other cleanup operations before the <see cref="T:System.Printing.PrintQueueStream" /> is reclaimed by garbage collection.</summary>
    </member>
    <member name="M:System.Printing.PrintQueueStream.Flush">
      <summary>Clears all the buffers for this stream and writes any buffered data to the underlying device.</summary>
    </member>
    <member name="M:System.Printing.PrintQueueStream.HandlePackagingProgressEvent(System.Object,System.Windows.Xps.Packaging.PackagingProgressEventArgs)">
      <summary>Enables the <see cref="T:System.Printing.PrintQueueStream" /> to respond to the packaging progress by handling the <see cref="E:System.Windows.Xps.Serialization.XpsPackagingPolicy.PackagingProgressEvent" />. </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">The event data.</param>
    </member>
    <member name="P:System.Printing.PrintQueueStream.JobIdentifier">
      <summary>Gets the ID number of the print job.</summary>
      <returns>An <see cref="T:System.Int32" /> that represents an ID number.</returns>
    </member>
    <member name="P:System.Printing.PrintQueueStream.Length">
      <summary>Gets the length of the stream in bytes.</summary>
      <returns>An <see cref="T:System.Int64" /> that represents the length of the stream in bytes.</returns>
    </member>
    <member name="P:System.Printing.PrintQueueStream.Position">
      <summary>Gets or sets the current read/write position in the stream.</summary>
      <returns>An <see cref="T:System.Int64" /> that represents the current position in the stream.</returns>
    </member>
    <member name="M:System.Printing.PrintQueueStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Reads a sequence of bytes from the stream and advances the read/write position in the stream by the number of bytes that were read.</summary>
      <returns>An <see cref="T:System.Int32" /> that holds the total number of bytes that are read into the buffer. </returns>
      <param name="buffer">An array of bytes. </param>
      <param name="offset">The zero-based byte offset in the buffer where you want to begin storing the data that is read from the stream.</param>
      <param name="count">The maximum number of bytes to be read from the stream.</param>
    </member>
    <member name="M:System.Printing.PrintQueueStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Sets the read/write position within the stream.</summary>
      <returns>An <see cref="T:System.Int64" /> that represents the new read/write position.</returns>
      <param name="offset">A byte offset that is relative to the origin parameter.</param>
      <param name="origin">A value of type <see cref="T:System.IO.SeekOrigin" /> that indicates the reference point that is used to obtain the new position.</param>
    </member>
    <member name="M:System.Printing.PrintQueueStream.SetLength(System.Int64)">
      <summary>Sets the length of the stream.</summary>
      <param name="value">The needed length, in bytes, of the current stream.</param>
    </member>
    <member name="M:System.Printing.PrintQueueStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Writes a sequence of bytes to the stream and advances the read/write position in the stream by the number of bytes that are written.</summary>
      <param name="buffer">An array of bytes from which to copy to the stream.</param>
      <param name="offset">The zero-based byte offset in the <paramref name="buffer" /> where you want to begin copying bytes to the stream.</param>
      <param name="count">The number of bytes to write to the stream.</param>
    </member>
    <member name="T:System.Printing.PrintQueueStringProperty">
      <summary>Represents one, and only one, of three possible properties of a print queue: <see cref="P:System.Printing.PrintQueue.Location" />, <see cref="P:System.Printing.PrintQueue.Comment" />, or <see cref="P:System.Printing.PrintQueue.ShareName" />. </summary>
    </member>
    <member name="M:System.Printing.PrintQueueStringProperty.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintQueueStringProperty" /> class. </summary>
    </member>
    <member name="P:System.Printing.PrintQueueStringProperty.Name">
      <summary>Gets or sets the value of the print queue property that is represented. </summary>
      <returns>A <see cref="T:System.String" /> with the value of the property. </returns>
    </member>
    <member name="P:System.Printing.PrintQueueStringProperty.Type">
      <summary>Gets or sets a value that identifies which of the three possible properties of a print queue is being represented. </summary>
      <returns>A <see cref="T:System.Printing.PrintQueueStringPropertyType" /> that specifies the kind of print queue property that is being represented. </returns>
    </member>
    <member name="T:System.Printing.PrintQueueStringPropertyType">
      <summary>Specifies the intended meaning of a <see cref="T:System.Printing.PrintQueueStringProperty" />.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStringPropertyType.ShareName">
      <summary>The share name of the print queue.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStringPropertyType.Comment">
      <summary>A comment about the print queue.</summary>
    </member>
    <member name="F:System.Printing.PrintQueueStringPropertyType.Location">
      <summary>The location of the physical printer.</summary>
    </member>
    <member name="T:System.Printing.PrintServer">
      <summary>Manages the print queues on a print server, which is usually a computer, but can be a dedicated hardware print server appliance. </summary>
    </member>
    <member name="M:System.Printing.PrintServer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintServer" /> class. </summary>
    </member>
    <member name="M:System.Printing.PrintServer.#ctor(System.Printing.PrintSystemDesiredAccess)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintServer" /> class that represents the local print server and assigns it the specified <see cref="T:System.Printing.PrintSystemDesiredAccess" />. </summary>
      <param name="desiredAccess">A value that specifies the type of print server access that your program needs.</param>
      <exception cref="T:System.Printing.PrintServerException">
        <paramref name="desiredAccess" /> is a value that can be applied only to a <see cref="T:System.Printing.PrintQueue" /> object, not a <see cref="T:System.Printing.LocalPrintServer" /> object. For example, <see cref="F:System.Printing.PrintSystemDesiredAccess.UsePrinter" />.</exception>
    </member>
    <member name="M:System.Printing.PrintServer.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintServer" /> class that has the specified path. </summary>
      <param name="path">The name and complete path of the print server.</param>
    </member>
    <member name="M:System.Printing.PrintServer.#ctor(System.String,System.Printing.PrintServerIndexedProperty[])">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintServer" /> class by using the specified <see cref="T:System.Printing.PrintServerIndexedProperty" /> array to determine which properties will be initialized. </summary>
      <param name="path">The complete path and name of the print server</param>
      <param name="propertiesFilter">The properties that the constructor initializes.</param>
    </member>
    <member name="M:System.Printing.PrintServer.#ctor(System.String,System.Printing.PrintServerIndexedProperty[],System.Printing.PrintSystemDesiredAccess)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintServer" /> class and provides the specified path, the <see cref="T:System.Printing.PrintServerIndexedProperty" /> array, and the needed access. </summary>
      <param name="path">The complete path and name of the print server</param>
      <param name="propertiesFilter">The properties that the constructor initializes.</param>
      <param name="desiredAccess">A value that specifies the type of print server access that your program needs.</param>
      <exception cref="T:System.Printing.PrintServerException">
        <paramref name="desiredAccess" /> is a value that can be applied only to a <see cref="T:System.Printing.PrintQueue" /> object, not a <see cref="T:System.Printing.LocalPrintServer" /> object. For example, <see cref="F:System.Printing.PrintSystemDesiredAccess.UsePrinter" />.</exception>
    </member>
    <member name="M:System.Printing.PrintServer.#ctor(System.String,System.Printing.PrintSystemDesiredAccess)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintServer" /> class that has the specified path and the needed access. </summary>
      <param name="path">The name and complete path of the print server.</param>
      <param name="desiredAccess">A value that specifies the type of print server access that your program needs.</param>
      <exception cref="T:System.Printing.PrintServerException">
        <paramref name="desiredAccess" /> is a value that can be applied only to a <see cref="T:System.Printing.PrintQueue" /> object, not a <see cref="T:System.Printing.LocalPrintServer" /> object. For example, <see cref="F:System.Printing.PrintSystemDesiredAccess.UsePrinter" />.</exception>
    </member>
    <member name="M:System.Printing.PrintServer.#ctor(System.String,System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintServer" /> class that has the specified path and properties filter. </summary>
      <param name="path">The name and complete path of the print server.</param>
      <param name="propertiesFilter">An array of the names of properties that the constructor initializes.</param>
    </member>
    <member name="M:System.Printing.PrintServer.#ctor(System.String,System.String[],System.Printing.PrintSystemDesiredAccess)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintServer" /> class that has the specified path, properties filter, and the needed access. </summary>
      <param name="path">The name and complete path of the print server.</param>
      <param name="propertiesFilter">An array of the names of properties that the constructor initializes.</param>
      <param name="desiredAccess">A value that specifies the type of print server access that your program needs.</param>
      <exception cref="T:System.Printing.PrintServerException">
        <paramref name="desiredAccess" /> is a value that can be applied only to a <see cref="T:System.Printing.PrintQueue" /> object, not a <see cref="T:System.Printing.LocalPrintServer" /> object. For example, <see cref="F:System.Printing.PrintSystemDesiredAccess.UsePrinter" />.</exception>
    </member>
    <member name="P:System.Printing.PrintServer.BeepEnabled">
      <summary>Gets or sets a value that indicates whether the print server beeps in response to an error condition in the printer. </summary>
      <returns>
        <see cref="T:System.Boolean" />true if the print server beeps in response to an error; otherwise, false.</returns>
    </member>
    <member name="M:System.Printing.PrintServer.Commit">
      <summary>Commits any changes that your program made to the properties of the print server object by writing them to the print server that the object represents. </summary>
      <exception cref="T:System.Printing.PrintSystemException">Some of the properties are not committed. </exception>
      <exception cref="T:System.Printing.PrintCommitAttributesException">Some of the properties could not be committed. - or -The <see cref="T:System.Printing.PrintServer" /> object was not created with sufficient rights. See Remarks.</exception>
    </member>
    <member name="P:System.Printing.PrintServer.DefaultPortThreadPriority">
      <summary>Do not use.</summary>
      <returns>The thread priority.</returns>
    </member>
    <member name="P:System.Printing.PrintServer.DefaultSchedulerPriority">
      <summary>Do not use.</summary>
      <returns>The default scheduler thread priority.</returns>
    </member>
    <member name="P:System.Printing.PrintServer.DefaultSpoolDirectory">
      <summary>Gets or sets the path where the print server's spool files are located. </summary>
      <returns>A <see cref="T:System.String" /> that identifies the complete path of the folder for the spool files.</returns>
    </member>
    <member name="M:System.Printing.PrintServer.DeletePrintQueue(System.Printing.PrintQueue)">
      <summary>Removes the specified <see cref="T:System.Printing.PrintQueue" /> from the print server. </summary>
      <returns>true if the queue is successfully deleted; otherwise, false.</returns>
      <param name="printQueue">The queue that is deleted.</param>
    </member>
    <member name="M:System.Printing.PrintServer.DeletePrintQueue(System.String)">
      <summary>Removes the print queue with the specified name from the print server. </summary>
      <returns>true if the queue is successfully deleted; otherwise, false.</returns>
      <param name="printQueueName">The name of the queue that is deleted.</param>
    </member>
    <member name="P:System.Printing.PrintServer.EventLog">
      <summary>Gets or sets the type of events that the print server logs. </summary>
      <returns>A value of <see cref="T:System.Printing.PrintServerEventLoggingTypes" /> that identifies the type of event logging that is provided by the print server. The default is <see cref="F:System.Printing.PrintServerEventLoggingTypes.LogPrintingErrorEvents" />.</returns>
    </member>
    <member name="M:System.Printing.PrintServer.GetPrintQueue(System.String)">
      <summary>Obtains a reference to the named print queue from the print server. </summary>
      <returns>A <see cref="T:System.Printing.PrintQueue" />.</returns>
      <param name="printQueueName">The name of the print queue.</param>
    </member>
    <member name="M:System.Printing.PrintServer.GetPrintQueue(System.String,System.String[])">
      <summary>Gets a specified print queue from the print server. </summary>
      <returns>A <see cref="T:System.Printing.PrintQueue" />.</returns>
      <param name="printQueueName">The name of the print queue.</param>
      <param name="propertiesFilter">The names of the properties that are initialized in the returned queue.</param>
    </member>
    <member name="M:System.Printing.PrintServer.GetPrintQueues">
      <summary>Gets the collection of print queues that the print server hosts. </summary>
      <returns>The <see cref="T:System.Printing.PrintQueueCollection" /> of print queues on the print server.</returns>
    </member>
    <member name="M:System.Printing.PrintServer.GetPrintQueues(System.Printing.EnumeratedPrintQueueTypes[])">
      <summary>Gets the collection of print queues of the specified types that are named in <see cref="T:System.Printing.EnumeratedPrintQueueTypes" /> and hosted by the print server. </summary>
      <returns>The <see cref="T:System.Printing.PrintQueueCollection" /> of print queues, of the specified types, on the print server.</returns>
      <param name="enumerationFlag">An array of values that represent the types of print queues that are in the collection.</param>
    </member>
    <member name="M:System.Printing.PrintServer.GetPrintQueues(System.Printing.PrintQueueIndexedProperty[])">
      <summary>Gets a collection of print queues that are hosted by the print server and initialized only in the properties that are specified in the <see cref="T:System.Printing.PrintQueueIndexedProperty" /> array. </summary>
      <returns>A <see cref="T:System.Printing.PrintQueueCollection" /> whose members are initialized only in the properties specified by <paramref name="propertiesFilter" />.</returns>
      <param name="propertiesFilter">The properties that the constructor initializes.</param>
    </member>
    <member name="M:System.Printing.PrintServer.GetPrintQueues(System.Printing.PrintQueueIndexedProperty[],System.Printing.EnumeratedPrintQueueTypes[])">
      <summary>Gets a collection of print queues of the specified types. These print queues are only initialized in the properties that are specified in the <see cref="T:System.Printing.PrintQueueIndexedProperty" /> array. </summary>
      <returns>The <see cref="T:System.Printing.PrintQueueCollection" /> of the print server.</returns>
      <param name="propertiesFilter">The properties that the constructor initializes.</param>
      <param name="enumerationFlag">An array of values that represent the types of print queues in the collection.</param>
    </member>
    <member name="M:System.Printing.PrintServer.GetPrintQueues(System.String[])">
      <summary>Gets a collection of print queues that are hosted by the print server and that are initialized only in the specified properties. </summary>
      <returns>The <see cref="T:System.Printing.PrintQueueCollection" /> of print queues on the print server; each print queue is initialized only in the properties that are specified in <paramref name="propertiesFilter" />.</returns>
      <param name="propertiesFilter">The names of the queue properties that are initialized.</param>
    </member>
    <member name="M:System.Printing.PrintServer.GetPrintQueues(System.String[],System.Printing.EnumeratedPrintQueueTypes[])">
      <summary>Gets the collection of print queues, which are of the specified <see cref="T:System.Printing.EnumeratedPrintQueueTypes" /> and are initialized only in the specified properties.  </summary>
      <returns>A <see cref="T:System.Printing.PrintQueueCollection" /> of print queues of the specified types; each print queue has only the specified properties initialized. </returns>
      <param name="propertiesFilter">The names of the queue properties that are initialized.</param>
      <param name="enumerationFlag">An array of values that represent the types of print queues that are returned in the collection.</param>
    </member>
    <member name="M:System.Printing.PrintServer.InstallPrintQueue(System.String,System.String,System.String[],System.String,System.Printing.IndexedProperties.PrintPropertyDictionary)">
      <summary>Installs a print queue, and associated printer driver, on the print server. </summary>
      <returns>The new <see cref="T:System.Printing.PrintQueue" />.</returns>
      <param name="printQueueName">The name of the new queue.</param>
      <param name="driverName">The path and name of the printer driver.</param>
      <param name="portNames">The IDs of the ports that the new queue uses.</param>
      <param name="printProcessorName">The name of the print processor.</param>
      <param name="initialParameters">The parameters that are initialized.</param>
    </member>
    <member name="M:System.Printing.PrintServer.InstallPrintQueue(System.String,System.String,System.String[],System.String,System.Printing.PrintQueueAttributes)">
      <summary>Installs a print queue, and associated printer driver, on the print server. </summary>
      <returns>The newly created <see cref="T:System.Printing.PrintQueue" />.</returns>
      <param name="printQueueName">The name of the new queue.</param>
      <param name="driverName">The path and name of the printer driver.</param>
      <param name="portNames">The IDs of the ports that the new queue uses.</param>
      <param name="printProcessorName">The name of the print processor.</param>
      <param name="printQueueAttributes">The attributes, as flags, of the new queue.</param>
    </member>
    <member name="M:System.Printing.PrintServer.InstallPrintQueue(System.String,System.String,System.String[],System.String,System.Printing.PrintQueueAttributes,System.Printing.PrintQueueStringProperty,System.Int32,System.Int32)">
      <summary>Installs a prioritized print queue, and associated printer driver, on the print server. </summary>
      <returns>The newly created <see cref="T:System.Printing.PrintQueue" />.</returns>
      <param name="printQueueName">The name of the new queue.</param>
      <param name="driverName">The path and name of the printer driver.</param>
      <param name="portNames">The IDs of the ports that the new queue uses.</param>
      <param name="printProcessorName">The name of the print processor.</param>
      <param name="printQueueAttributes">The attributes, as flags, of the new queue.</param>
      <param name="printQueueProperty">The comment, location, or share name of the new queue.</param>
      <param name="printQueuePriority">A value from 1 through 99 that specifies the priority of this print queue relative to other queues that are hosted by the print server.</param>
      <param name="printQueueDefaultPriority">A value from 1 to 99 that specifies the default priority of print jobs that are sent to the queue.</param>
    </member>
    <member name="M:System.Printing.PrintServer.InstallPrintQueue(System.String,System.String,System.String[],System.String,System.Printing.PrintQueueAttributes,System.String,System.String,System.String,System.String,System.Int32,System.Int32)">
      <summary>Installs a shared, prioritized print queue, and associated printer driver, on the print server. </summary>
      <returns>The newly created <see cref="T:System.Printing.PrintQueue" />.</returns>
      <param name="printQueueName">The name of the new queue.</param>
      <param name="driverName">The path and name of the printer driver.</param>
      <param name="portNames">The IDs of the ports that the new queue uses.</param>
      <param name="printProcessorName">The name of the print processor.</param>
      <param name="printQueueAttributes">The attributes, as flags, of the new queue.</param>
      <param name="printQueueShareName">The share name of the new queue.</param>
      <param name="printQueueComment">A comment about the queue that is visible to users in the Microsoft Windows UI.</param>
      <param name="printQueueLocation">The location of the new queue.</param>
      <param name="printQueueSeparatorFile">The path of a file that is inserted at the beginning of each print job.</param>
      <param name="printQueuePriority">A value from 1 through 99 that specifies the priority of the queue relative to other queues that are hosted by the print server.</param>
      <param name="printQueueDefaultPriority">A value from 1 through 99 that specifies the default priority of new print jobs that are sent to the queue.</param>
    </member>
    <member name="M:System.Printing.PrintServer.InternalDispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are used by the <see cref="T:System.Printing.PrintServer" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="P:System.Printing.PrintServer.IsDelayInitialized">
      <summary>Gets or sets a value that indicates whether initialization of the <see cref="T:System.Printing.PrintServer" /> properties has been postponed. </summary>
      <returns>true if initialization is postponed; otherwise, false. </returns>
    </member>
    <member name="P:System.Printing.PrintServer.MajorVersion">
      <summary>Gets the major version of the operating system. </summary>
      <returns>An <see cref="T:System.Int32" /> that identifies the major version of the operating system.</returns>
    </member>
    <member name="P:System.Printing.PrintServer.MinorVersion">
      <summary>Gets the minor version within the major version of the operating system. </summary>
      <returns>An <see cref="T:System.Int32" /> that identifies the minor version of the operating system.</returns>
    </member>
    <member name="P:System.Printing.PrintServer.Name">
      <summary>Gets the name of the print server. </summary>
      <returns>The name of the print server.</returns>
      <exception cref="T:System.Printing.PrintSystemException">The property is not initialized.</exception>
    </member>
    <member name="P:System.Printing.PrintServer.NetPopup">
      <summary>Gets or sets a value that indicates whether notifications that a print job has finished are sent to either the print server or the client computer. </summary>
      <returns>true if notifications are sent to the client computer; false if notifications are sent to the print server.</returns>
    </member>
    <member name="P:System.Printing.PrintServer.PortThreadPriority">
      <summary>Gets or sets the thread priority for the process that manages I/O through the printer ports. </summary>
      <returns>A <see cref="T:System.Threading.ThreadPriority" /> enumeration value that identifies the thread priority for the process that manages the printer ports. The default is <see cref="F:System.Threading.ThreadPriority.Normal" />.</returns>
    </member>
    <member name="M:System.Printing.PrintServer.Refresh">
      <summary>Updates the properties of the <see cref="T:System.Printing.PrintServer" /> object so that each property value matches the corresponding attribute value of the print server that the object represents. </summary>
      <exception cref="T:System.Printing.PrintSystemException">Some of the properties cannot be refreshed. </exception>
    </member>
    <member name="P:System.Printing.PrintServer.RestartJobOnPoolEnabled">
      <summary>Gets or sets a value that indicates whether users can restart jobs after an error occurs if printer pooling is enabled.</summary>
      <returns>true if jobs can be restarted when printer pooling is enabled; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintServer.RestartJobOnPoolTimeout">
      <summary>Gets or sets a value that indicates the wait time before a job can be restarted, if an error occurs when printer pooling is also enabled.</summary>
      <returns>The wait time, in minutes, before a job restarts.  </returns>
    </member>
    <member name="P:System.Printing.PrintServer.SchedulerPriority">
      <summary>Gets or sets the thread priority for the process that routes print jobs from applications to print queues. </summary>
      <returns>A <see cref="T:System.Threading.ThreadPriority" /> enumeration value that identifies the thread priority for the print server scheduling process. The default is <see cref="F:System.Threading.ThreadPriority.Normal" />.</returns>
    </member>
    <member name="P:System.Printing.PrintServer.SubSystemVersion">
      <summary>Gets the version of the print spooler system. </summary>
      <returns>A <see cref="T:System.Byte" /> that identifies the version of the print spooler system.</returns>
    </member>
    <member name="T:System.Printing.PrintServerEventLoggingTypes">
      <summary>Specifies the types of events that can be logged by a <see cref="T:System.Printing.PrintServer" />.</summary>
    </member>
    <member name="F:System.Printing.PrintServerEventLoggingTypes.LogAllPrintingEvents">
      <summary>All printing events.</summary>
    </member>
    <member name="F:System.Printing.PrintServerEventLoggingTypes.LogPrintingInformationEvents">
      <summary>Information events for printing.</summary>
    </member>
    <member name="F:System.Printing.PrintServerEventLoggingTypes.LogPrintingWarningEvents">
      <summary>Warning events.</summary>
    </member>
    <member name="F:System.Printing.PrintServerEventLoggingTypes.LogPrintingErrorEvents">
      <summary>Error events for printing.</summary>
    </member>
    <member name="F:System.Printing.PrintServerEventLoggingTypes.LogPrintingSuccessEvents">
      <summary>Successful printing events.</summary>
    </member>
    <member name="F:System.Printing.PrintServerEventLoggingTypes.None">
      <summary>No events.</summary>
    </member>
    <member name="T:System.Printing.PrintServerIndexedProperty">
      <summary>Specifies the properties of a <see cref="T:System.Printing.PrintServer" /> object that are initialized when it is constructed.</summary>
    </member>
    <member name="F:System.Printing.PrintServerIndexedProperty.MinorVersion">
      <summary>The property that specifies the minor version of the operating system.</summary>
    </member>
    <member name="F:System.Printing.PrintServerIndexedProperty.MajorVersion">
      <summary>The property that specifies the major version of the operating system.</summary>
    </member>
    <member name="F:System.Printing.PrintServerIndexedProperty.RestartJobOnPoolEnabled">
      <summary>The property that specifies whether users can restart print jobs when printer pooling is being used.</summary>
    </member>
    <member name="F:System.Printing.PrintServerIndexedProperty.RestartJobOnPoolTimeout">
      <summary>The property that specifies how long to wait before restarting a print job when printer pooling is being used.</summary>
    </member>
    <member name="F:System.Printing.PrintServerIndexedProperty.EventLog">
      <summary>The property that specifies the kind of event logging that is provided by the print server.</summary>
    </member>
    <member name="F:System.Printing.PrintServerIndexedProperty.NetPopup">
      <summary>The property that specifies whether the client computer or the print server receives notifications that a job is finished.</summary>
    </member>
    <member name="F:System.Printing.PrintServerIndexedProperty.BeepEnabled">
      <summary>The property that specifies whether a printer error causes the print server to beep.</summary>
    </member>
    <member name="F:System.Printing.PrintServerIndexedProperty.DefaultSchedulerPriority">
      <summary>The property that specifies the default thread priority for the scheduling of print jobs.</summary>
    </member>
    <member name="F:System.Printing.PrintServerIndexedProperty.SchedulerPriority">
      <summary>The property that specifies the priority of the scheduler.</summary>
    </member>
    <member name="F:System.Printing.PrintServerIndexedProperty.DefaultPortThreadPriority">
      <summary>The property that specifies the default priority for the thread that manages port I/0.</summary>
    </member>
    <member name="F:System.Printing.PrintServerIndexedProperty.PortThreadPriority">
      <summary>The property that specifies the priority of the thread that manages port I/O.</summary>
    </member>
    <member name="F:System.Printing.PrintServerIndexedProperty.DefaultSpoolDirectory">
      <summary>The property that specifies the path to the folder where spool jobs are located as temporary files.</summary>
    </member>
    <member name="T:System.Printing.PrintSystemDesiredAccess">
      <summary>Specifies the different access rights (or levels of access) for printing objects.</summary>
    </member>
    <member name="F:System.Printing.PrintSystemDesiredAccess.AdministratePrinter">
      <summary>The right to perform all administrative tasks for the print queue, including the right to pause and resume any print job; and the right to delete all jobs from the queue. This access level also includes all rights under <see cref="F:System.Printing.PrintSystemDesiredAccess.UsePrinter" />.</summary>
    </member>
    <member name="F:System.Printing.PrintSystemDesiredAccess.UsePrinter">
      <summary>The right to add print jobs to the queue and to delete and enumerate one's own jobs.</summary>
    </member>
    <member name="F:System.Printing.PrintSystemDesiredAccess.EnumerateServer">
      <summary>The right to list the queues on the print server.</summary>
    </member>
    <member name="F:System.Printing.PrintSystemDesiredAccess.AdministrateServer">
      <summary>The right to perform all administrative tasks for the print server. This access level does not include <see cref="F:System.Printing.PrintSystemDesiredAccess.AdministratePrinter" /> rights for the print queues hosted by the server.</summary>
    </member>
    <member name="F:System.Printing.PrintSystemDesiredAccess.None">
      <summary>No access.</summary>
    </member>
    <member name="T:System.Printing.PrintSystemJobInfo">
      <summary>Defines a print job in detail.</summary>
    </member>
    <member name="M:System.Printing.PrintSystemJobInfo.Cancel">
      <summary>Cancels the print job. </summary>
    </member>
    <member name="M:System.Printing.PrintSystemJobInfo.Commit">
      <summary>Writes any changes to the properties of the <see cref="T:System.Printing.PrintSystemJobInfo" /> object to the actual print job that the object represents.</summary>
    </member>
    <member name="M:System.Printing.PrintSystemJobInfo.Get(System.Printing.PrintQueue,System.Int32)">
      <summary>Gets the <see cref="T:System.Printing.PrintSystemJobInfo" /> for the specified job in the specified <see cref="T:System.Printing.PrintQueue" />.</summary>
      <returns>The <see cref="T:System.Printing.PrintSystemJobInfo" /> that corresponds to the <paramref name="jobIdentifier" />.</returns>
      <param name="printQueue">The print queue hosting the print job.</param>
      <param name="jobIdentifier">A numerical ID for the print job.</param>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.HostingPrintQueue">
      <summary>Gets the print queue that is hosting the print job.</summary>
      <returns>A <see cref="T:System.Printing.PrintQueue" /> value that represents the print queue that owns the print job.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.HostingPrintServer">
      <summary>Gets the print server that is hosting the print queue for the print job.</summary>
      <returns>A <see cref="T:System.Printing.PrintServer" /> value that represents the hosting print server (usually a computer) for the <see cref="T:System.Printing.PrintQueue" /> that owns the print job. </returns>
    </member>
    <member name="M:System.Printing.PrintSystemJobInfo.InternalDispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are used by the <see cref="T:System.Printing.PrintSystemJobInfo" /> and optionally, releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.IsBlocked">
      <summary>Gets a value that indicates whether the print job is blocked and therefore, not printing. </summary>
      <returns>true if the print job is blocked; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.IsCompleted">
      <summary>Gets a value that indicates whether the print job is finished.</summary>
      <returns>true if the print job is finished; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.IsDeleted">
      <summary>Gets a value that indicates whether the print job, which is represented by the <see cref="T:System.Printing.PrintSystemJobInfo" /> object, was deleted from the print queue. </summary>
      <returns>true if the print job was deleted; otherwise false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.IsDeleting">
      <summary>Gets a value that indicates whether the print job is being deleted from the print queue. </summary>
      <returns>true if the print job is being deleted; otherwise false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.IsInError">
      <summary>Gets a value that indicates whether an error condition is associated with the print job.</summary>
      <returns>true if an error condition is associated with the print job; otherwise false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.IsOffline">
      <summary>Gets a value that indicates whether the printer is offline. </summary>
      <returns>true if the printer is offline; otherwise false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.IsPaperOut">
      <summary>Gets a value that indicates whether the printer has run out of the paper size and type that the print job requires. </summary>
      <returns>true if the printer has run out of the required paper; otherwise, false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.IsPaused">
      <summary>Gets a value that indicates whether the print job is paused.</summary>
      <returns>true if the print job is paused; otherwise false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.IsPrinted">
      <summary>Gets a value that indicates whether the print job printed.</summary>
      <returns>true if the print job has printed; otherwise false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.IsPrinting">
      <summary>Gets a value that indicates whether the print job is being printed.</summary>
      <returns>true if the printer is printing; otherwise false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.IsRestarted">
      <summary>Gets a value that indicates whether the print job has been restarted. </summary>
      <returns>true if the printer is printing; otherwise false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.IsRetained">
      <summary>Gets a value that indicates whether the print job was saved in the queue after it printed. </summary>
      <returns>true if the print job was saved; otherwise false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.IsSpooling">
      <summary>Gets a value that indicates whether the print job is being spooled.</summary>
      <returns>true if the print job is being spooled; otherwise false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.IsUserInterventionRequired">
      <summary>Gets a value that indicates whether the printer needs user intervention.</summary>
      <returns>true if user intervention is needed; otherwise false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.JobIdentifier">
      <summary>Gets the identification number for the print job.</summary>
      <returns>An <see cref="T:System.Int32" /> that identifies the print job.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.JobName">
      <summary>Gets or sets a name for the print job.</summary>
      <returns>A <see cref="T:System.String" /> name for the print job.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.JobSize">
      <summary>Get the size, in bytes, of the print job.</summary>
      <returns>An <see cref="T:System.Int32" /> that states the size, in bytes, of the print job.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.JobStatus">
      <summary>Gets the current status of the print job. </summary>
      <returns>A <see cref="T:System.Printing.PrintJobStatus" /> value.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.JobStream">
      <summary>Gets a reference to the <see cref="T:System.IO.Stream" /> of the print job.</summary>
      <returns>A <see cref="T:System.IO.Stream" /> that contains the print job.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.NumberOfPages">
      <summary>Gets the number of pages in the print job.</summary>
      <returns>An <see cref="T:System.Int32" /> that states the number of pages in the print job.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.NumberOfPagesPrinted">
      <summary>Gets the number of pages that have already printed.</summary>
      <returns>An <see cref="T:System.Int32" /> that states the number of pages that have already printed.</returns>
    </member>
    <member name="M:System.Printing.PrintSystemJobInfo.Pause">
      <summary>Halts printing of the job until <see cref="M:System.Printing.PrintSystemJobInfo.Resume" /> runs. </summary>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.PositionInPrintQueue">
      <summary>Gets the print job's place in the print queue. </summary>
      <returns>An <see cref="T:System.Int32" /> that states the print job's place in the queue.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.Priority">
      <summary>Gets a non-numerical expression that represents the priority of the print job relative to other jobs in the print queue.</summary>
      <returns>A <see cref="T:System.Printing.PrintJobPriority" /> that represents the priority of the print job as <see cref="F:System.Printing.PrintJobPriority.Maximum" />, <see cref="F:System.Printing.PrintJobPriority.Minimum" />, <see cref="F:System.Printing.PrintJobPriority.Default" />, or <see cref="F:System.Printing.PrintJobPriority.None" />.</returns>
    </member>
    <member name="M:System.Printing.PrintSystemJobInfo.Refresh">
      <summary>Updates the properties of the <see cref="T:System.Printing.PrintSystemJobInfo" /> object so that their values match the values of the actual print job that the object represents.</summary>
    </member>
    <member name="M:System.Printing.PrintSystemJobInfo.Restart">
      <summary>Restarts a print job from the beginning. </summary>
    </member>
    <member name="M:System.Printing.PrintSystemJobInfo.Resume">
      <summary>Resumes the printing of a paused print job.</summary>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.StartTimeOfDay">
      <summary>Gets the earliest time of day, expressed as the number of minutes after midnight Coordinated Universal Time (UTC) (also called Greenwich Mean Time [GMT]), that the print job can begin printing.</summary>
      <returns>An <see cref="T:System.Int32" /> specifying the earliest possible start time for the print job, expressed as the number of minutes after midnight (UTC). The maximum value is 1439.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.Submitter">
      <summary>Gets the name of the user who submitted the print job.</summary>
      <returns>A <see cref="T:System.String" /> that identifies the user who submitted the print job.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.TimeJobSubmitted">
      <summary>Gets the date and time that the print job is submitted.</summary>
      <returns>A <see cref="T:System.DateTime" /> object containing the date and time that the print job is submitted.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.TimeSinceStartedPrinting">
      <summary>Gets the time, in milliseconds, since the print job started printing.</summary>
      <returns>An <see cref="T:System.Int32" /> that represents the time, in milliseconds, since the print job started.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemJobInfo.UntilTimeOfDay">
      <summary>Gets the last time of day, expressed as the number of minutes after midnight Coordinated Universal Time (UTC) (also called Greenwich Mean Time [GMT]), that the print job can begin printing.</summary>
      <returns>An <see cref="T:System.Int32" /> that specifies the last time that the job can print, expressed as the number of minutes after midnight (UTC). The maximum value is 1439.</returns>
    </member>
    <member name="T:System.Printing.PrintSystemObject">
      <summary>Defines basic properties and methods that are common to the objects of the printing system. Classes that derive from this class represent such objects as print queues, print servers, and print jobs.</summary>
    </member>
    <member name="M:System.Printing.PrintSystemObject.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintSystemObject" /> class. </summary>
    </member>
    <member name="M:System.Printing.PrintSystemObject.#ctor(System.Printing.PrintSystemObjectLoadMode)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintSystemObject" /> class by using the specified <see cref="T:System.Printing.PrintSystemObjectLoadMode" />.</summary>
      <param name="mode">A value that specifies whether the properties of the object should be initialized when the object is loaded.</param>
    </member>
    <member name="M:System.Printing.PrintSystemObject.BaseAttributeNames">
      <summary>Gets the names of the attributes of the derived class. </summary>
      <returns>An array of <see cref="T:System.String" /> values.</returns>
    </member>
    <member name="M:System.Printing.PrintSystemObject.Commit">
      <summary>When overridden in a derived class, writes any changes that your program has made to the object's properties to the actual software or hardware component that the object represents. </summary>
    </member>
    <member name="M:System.Printing.PrintSystemObject.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Printing.PrintSystemObject" />. </summary>
    </member>
    <member name="M:System.Printing.PrintSystemObject.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Printing.PrintSystemObject" /> and optionally releases the managed resources. </summary>
    </member>
    <member name="M:System.Printing.PrintSystemObject.Finalize">
      <summary>Releases the resources that are being used by the <see cref="T:System.Printing.PrintSystemObject" />. </summary>
    </member>
    <member name="M:System.Printing.PrintSystemObject.Initialize">
      <summary>Initializes the properties of the <see cref="T:System.Printing.PrintSystemObject" />. </summary>
    </member>
    <member name="M:System.Printing.PrintSystemObject.InternalDispose(System.Boolean)">
      <summary>When overridden in a derived class, releases the unmanaged resources that are being used by the <see cref="T:System.Printing.PrintSystemObject" />, and optionally releases the managed resources that are being used. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="P:System.Printing.PrintSystemObject.IsDisposed">
      <summary>Gets or sets a value that indicates whether the object has been disposed. </summary>
      <returns>true if the object has been disposed; otherwise false.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemObject.Name">
      <summary>Gets the name of the object. </summary>
      <returns>A <see cref="T:System.String" /> that represents the name of the object.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemObject.Parent">
      <summary>Gets the parent of the object. </summary>
      <returns>Another <see cref="T:System.Printing.PrintSystemObject" />.</returns>
    </member>
    <member name="P:System.Printing.PrintSystemObject.PropertiesCollection">
      <summary>Gets a collection of attribute and value pairs. </summary>
      <returns>A <see cref="T:System.Printing.IndexedProperties.PrintPropertyDictionary" /> that contains attribute and value pairs.</returns>
    </member>
    <member name="M:System.Printing.PrintSystemObject.Refresh">
      <summary>When overridden in a derived class, updates the properties of an object of the derived class so that its values match the values of the actual software or hardware component that the object represents. </summary>
    </member>
    <member name="T:System.Printing.PrintSystemObjectLoadMode">
      <summary>Specifies whether the properties of an object are initialized when the object loads.</summary>
    </member>
    <member name="F:System.Printing.PrintSystemObjectLoadMode.None">
      <summary>Not specified whether the properties are initialized. </summary>
    </member>
    <member name="F:System.Printing.PrintSystemObjectLoadMode.LoadInitialized">
      <summary>The properties are initialized during loading.</summary>
    </member>
    <member name="F:System.Printing.PrintSystemObjectLoadMode.LoadUninitialized">
      <summary>The properties are not initialized during loading.</summary>
    </member>
    <member name="T:System.Printing.PrintSystemObjectPropertiesChangedEventArgs">
      <summary>Provides data for a PropertiesChanged event, which you must create.</summary>
    </member>
    <member name="M:System.Printing.PrintSystemObjectPropertiesChangedEventArgs.#ctor(System.Collections.Specialized.StringCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintSystemObjectPropertiesChangedEventArgs" /> class.</summary>
      <param name="events">A collection of strings, each of which identifies a changed property.</param>
    </member>
    <member name="M:System.Printing.PrintSystemObjectPropertiesChangedEventArgs.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Printing.PrintSystemObjectPropertiesChangedEventArgs" /> object.</summary>
    </member>
    <member name="M:System.Printing.PrintSystemObjectPropertiesChangedEventArgs.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are used by the <see cref="T:System.Printing.PrintSystemObjectPropertiesChangedEventArgs" /> object and optionally releases the managed resources. </summary>
    </member>
    <member name="P:System.Printing.PrintSystemObjectPropertiesChangedEventArgs.PropertiesNames">
      <summary>Gets a collection of the names of the changed properties.</summary>
      <returns>A <see cref="T:System.Collections.Specialized.StringCollection" /> of property names.</returns>
    </member>
    <member name="T:System.Printing.PrintSystemObjectPropertyChangedEventArgs">
      <summary>Provides data for a PropertyChanged event, which you must create.</summary>
    </member>
    <member name="M:System.Printing.PrintSystemObjectPropertyChangedEventArgs.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintSystemObjectPropertyChangedEventArgs" /> class.</summary>
      <param name="eventName">The name of the property that changed.</param>
    </member>
    <member name="M:System.Printing.PrintSystemObjectPropertyChangedEventArgs.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Printing.PrintSystemObjectPropertyChangedEventArgs" />.</summary>
    </member>
    <member name="M:System.Printing.PrintSystemObjectPropertyChangedEventArgs.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are used by the <see cref="T:System.Printing.PrintSystemObjectPropertyChangedEventArgs" /> and optionally releases the managed resources. </summary>
    </member>
    <member name="P:System.Printing.PrintSystemObjectPropertyChangedEventArgs.PropertyName">
      <summary>Gets the name of the property that changed.</summary>
      <returns>A <see cref="T:System.String" /> that holds the property name.</returns>
    </member>
    <member name="T:System.Printing.PrintSystemObjects">
      <summary>Represents a collection of print system objects. </summary>
    </member>
    <member name="M:System.Printing.PrintSystemObjects.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintSystemObjects" /> class.</summary>
    </member>
    <member name="M:System.Printing.PrintSystemObjects.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Printing.PrintSystemObjects" />.</summary>
    </member>
    <member name="M:System.Printing.PrintSystemObjects.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are used by the <see cref="T:System.Printing.PrintSystemObjects" /> and optionally releases the managed resources. </summary>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintBooleanProperty">
      <summary>Represents a <see cref="T:System.Boolean" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintBooleanProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintBooleanProperty" /> class for the specified attribute.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Boolean" /> attribute that the <see cref="T:System.Printing.IndexedProperties.PrintBooleanProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintBooleanProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintBooleanProperty" /> class for the specified property that is using the specified value.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Boolean" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintBooleanProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintBooleanProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintBooleanProperty.op_Implicit(System.Printing.IndexedProperties.PrintBooleanProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Boolean">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintBooleanProperty" /> to a <see cref="T:System.Boolean" />.</summary>
      <returns>A <see cref="T:System.Boolean" />.</returns>
      <param name="attribRef">The <see cref="T:System.Printing.IndexedProperties.PrintBooleanProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintBooleanProperty.op_Implicit(System.Printing.IndexedProperties.PrintBooleanProperty)~System.Boolean">
      <summary>Provides implicit conversion to a <see cref="T:System.Boolean" /> from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintBooleanProperty" />.</summary>
      <returns>A <see cref="T:System.Boolean" />.</returns>
      <param name="attribRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintBooleanProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintBooleanProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintBooleanProperty" /> represents.</summary>
      <returns>A boxed <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintByteArrayProperty">
      <summary>Represents a property of a printing system hardware or software component whose value is an array of <see cref="T:System.Byte" /> values. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintByteArrayProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintByteArrayProperty" /> class for the specified attribute.</summary>
      <param name="attributeName">The name of the property, which is an array of type <see cref="T:System.Byte" />, that the <see cref="T:System.Printing.IndexedProperties.PrintByteArrayProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintByteArrayProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintByteArrayProperty" /> class that has the specified value for the specified attribute.</summary>
      <param name="attributeName">The name of the property, which is an array of type <see cref="T:System.Byte" />, that the <see cref="T:System.Printing.IndexedProperties.PrintByteArrayProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintByteArrayProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintByteArrayProperty.op_Implicit(System.Printing.IndexedProperties.PrintByteArrayProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Byte[]">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintByteArrayProperty" /> to an array of <see cref="T:System.Byte" /> values. </summary>
      <returns>An array of <see cref="T:System.Byte" /> values.</returns>
      <param name="attribRef">The <see cref="T:System.Printing.IndexedProperties.PrintByteArrayProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintByteArrayProperty.op_Implicit(System.Printing.IndexedProperties.PrintByteArrayProperty)~System.Byte[]">
      <summary>Provides implicit conversion to an array of <see cref="T:System.Byte" /> values from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintByteArrayProperty" />.</summary>
      <returns>An array of <see cref="T:System.Byte" /> values.</returns>
      <param name="attribRef">A pointer to a <see cref="T:System.Printing.IndexedProperties.PrintByteArrayProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintByteArrayProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintByteArrayProperty" /> represents.</summary>
      <returns>A boxed array of <see cref="T:System.Byte" /> values.</returns>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintDateTimeProperty">
      <summary>Represents a <see cref="T:System.DateTime" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintDateTimeProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintDateTimeProperty" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.DateTime" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintDateTimeProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintDateTimeProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintDateTimeProperty" /> class that has the specified value for the specified attribute.</summary>
      <param name="attributeName">The name of the <see cref="T:System.DateTime" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintDateTimeProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintDateTimeProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintDateTimeProperty.op_Implicit(System.Printing.IndexedProperties.PrintDateTimeProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.ValueType!System.DateTime!System.Runtime.CompilerServices.IsBoxed">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintDateTimeProperty" /> to a <see cref="T:System.DateTime" />. </summary>
      <returns>A <see cref="T:System.DateTime" />. </returns>
      <param name="attribRef">The object that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintDateTimeProperty.op_Implicit(System.Printing.IndexedProperties.PrintDateTimeProperty)~System.ValueType!System.DateTime!System.Runtime.CompilerServices.IsBoxed">
      <summary>Provides implicit conversion from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintDateTimeProperty" /> to a <see cref="T:System.DateTime" />. </summary>
      <returns>A <see cref="T:System.DateTime" />. </returns>
      <param name="attribRef">The object that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintDateTimeProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintDateTimeProperty" /> represents.</summary>
      <returns>A boxed <see cref="T:System.DateTime" />.</returns>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintDriverProperty">
      <summary>Represents a <see cref="T:System.Printing.PrintDriver" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintDriverProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintDriverProperty" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintDriver" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintDriverProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintDriverProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintDriverProperty" /> class that has the specified value for the specified attribute. </summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintDriver" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintDriverProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintDriverProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintDriverProperty.op_Implicit(System.Printing.IndexedProperties.PrintDriverProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Printing.PrintDriver">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintDriverProperty" /> to a <see cref="T:System.Printing.PrintDriver" />. </summary>
      <returns>A <see cref="T:System.Printing.PrintDriver" />.</returns>
      <param name="attribRef">The <see cref="T:System.Printing.IndexedProperties.PrintDriverProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintDriverProperty.op_Implicit(System.Printing.IndexedProperties.PrintDriverProperty)~System.Printing.PrintDriver">
      <summary>Provides implicit conversion to a <see cref="T:System.Printing.PrintDriver" /> from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintDriverProperty" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintDriver" />.</returns>
      <param name="attribRef">The pointer to a <see cref="T:System.Printing.IndexedProperties.PrintDriverProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintDriverProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintDriverProperty" /> represents. </summary>
      <returns>An <see cref="T:System.Object" /> that can be cast to a <see cref="T:System.Printing.PrintDriver" />. </returns>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintInt32Property">
      <summary>Represents an <see cref="T:System.Int32" /> property (and the value of the property) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintInt32Property.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintInt32Property" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Int32" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintInt32Property" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintInt32Property.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintInt32Property" /> class for the specified attribute and gives it the specified value.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Int32" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintInt32Property" /> represents.</param>
      <param name="attributeValue">The value of <see cref="T:System.Object" /> the property that the <see cref="T:System.Printing.IndexedProperties.PrintInt32Property" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintInt32Property.op_Implicit(System.Printing.IndexedProperties.PrintInt32Property|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Int32">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintInt32Property" /> to an <see cref="T:System.Int32" />. </summary>
      <returns>An <see cref="T:System.Int32" />.</returns>
      <param name="attribRef">The <see cref="T:System.Printing.IndexedProperties.PrintInt32Property" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintInt32Property.op_Implicit(System.Printing.IndexedProperties.PrintInt32Property)~System.Int32">
      <summary>Provides implicit conversion to an <see cref="T:System.Int32" /> from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintInt32Property" />.</summary>
      <returns>An <see cref="T:System.Int32" />.</returns>
      <param name="attribRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintInt32Property" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintInt32Property.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintInt32Property" /> represents.</summary>
      <returns>A boxed <see cref="T:System.Int32" />.</returns>
      <exception cref="T:System.InvalidOperationException">The property cannot be set to the value that the calling code provides.</exception>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintJobPriorityProperty">
      <summary>Represents a <see cref="T:System.Printing.PrintJobPriority" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintJobPriorityProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintJobPriorityProperty" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintJobPriority" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintJobPriorityProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintJobPriorityProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintJobPriorityProperty" /> class that has the specified value for the specified attribute. </summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintJobPriority" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintJobPriorityProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintJobPriorityProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintJobPriorityProperty.op_Implicit(System.Printing.IndexedProperties.PrintJobPriorityProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Printing.PrintJobPriority">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintJobPriorityProperty" /> to a <see cref="T:System.Printing.PrintJobPriority" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintJobPriority" /> value.</returns>
      <param name="attribRef">The <see cref="T:System.Printing.IndexedProperties.PrintJobPriorityProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintJobPriorityProperty.op_Implicit(System.Printing.IndexedProperties.PrintJobPriorityProperty)~System.Printing.PrintJobPriority">
      <summary>Provides implicit conversion to a <see cref="T:System.Printing.PrintJobPriority" /> from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintJobPriorityProperty" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintJobPriority" /> value.</returns>
      <param name="attribRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintBooleanProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintJobPriorityProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintJobPriorityProperty" /> represents.</summary>
      <returns>A boxed <see cref="T:System.Printing.PrintJobPriority" /> value.</returns>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintJobStatusProperty">
      <summary>Represents a <see cref="T:System.Printing.PrintJobStatus" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintJobStatusProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintJobStatusProperty" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintJobStatus" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintJobStatusProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintJobStatusProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintJobStatusProperty" /> class that has the specified value for the specified property. </summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintJobStatus" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintJobStatusProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintJobStatusProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintJobStatusProperty.op_Implicit(System.Printing.IndexedProperties.PrintJobStatusProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Printing.PrintJobStatus">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintJobStatusProperty" /> to a <see cref="T:System.Printing.PrintJobStatus" /> value.</summary>
      <returns>A <see cref="T:System.Printing.PrintJobStatus" />.</returns>
      <param name="attribRef">The <see cref="T:System.Printing.IndexedProperties.PrintJobStatusProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintJobStatusProperty.op_Implicit(System.Printing.IndexedProperties.PrintJobStatusProperty)~System.Printing.PrintJobStatus">
      <summary>Provides implicit conversion to a <see cref="T:System.Printing.PrintJobStatus" /> value from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintJobStatusProperty" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintJobStatus" />.</returns>
      <param name="attribRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintJobStatusProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintJobStatusProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintJobStatusProperty" /> represents.</summary>
      <returns>A boxed <see cref="T:System.Printing.PrintJobStatus" /> value.</returns>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintPortProperty">
      <summary>Represents a <see cref="T:System.Printing.PrintPort" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintPortProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintPortProperty" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintPort" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintPortProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintPortProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintPortProperty" /> class that has the specified value for the specified property. </summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintPort" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintPortProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintPortProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintPortProperty.op_Implicit(System.Printing.IndexedProperties.PrintPortProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Printing.PrintPort">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintPortProperty" /> to a <see cref="T:System.Printing.PrintPort" /> value.</summary>
      <returns>A <see cref="T:System.Printing.PrintPort" />.</returns>
      <param name="attribRef">The <see cref="T:System.Printing.IndexedProperties.PrintPortProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintPortProperty.op_Implicit(System.Printing.IndexedProperties.PrintPortProperty)~System.Printing.PrintPort">
      <summary>Provides implicit conversion to a <see cref="T:System.Printing.PrintPort" /> value from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintPortProperty" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintPort" />.</returns>
      <param name="attribRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintPortProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintPortProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintPortProperty" /> represents.</summary>
      <returns>An <see cref="T:System.Object" /> that can be cast to a <see cref="T:System.Printing.IndexedProperties.PrintPortProperty" />.</returns>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintProcessorProperty">
      <summary>Represents a <see cref="T:System.Printing.PrintProcessor" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintProcessorProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintProcessorProperty" /> class for the specified property.</summary>
      <param name="attributeName">The <see cref="T:System.Printing.IndexedProperties.PrintProcessorProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintProcessorProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintProcessorProperty" /> class that has the specified value for the specified property. </summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintProcessor" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintProcessorProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintProcessorProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintProcessorProperty.op_Implicit(System.Printing.IndexedProperties.PrintProcessorProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Printing.PrintProcessor">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintProcessorProperty" /> to a <see cref="T:System.Printing.PrintProcessor" /> value.</summary>
      <returns>A <see cref="T:System.Printing.PrintProcessor" />.</returns>
      <param name="attribRef">The <see cref="T:System.Printing.IndexedProperties.PrintProcessorProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintProcessorProperty.op_Implicit(System.Printing.IndexedProperties.PrintProcessorProperty)~System.Printing.PrintProcessor">
      <summary>Provides implicit conversion to a <see cref="T:System.Printing.PrintProcessor" /> value from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintProcessorProperty" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintProcessor" />.</returns>
      <param name="attribRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintProcessorProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintProcessorProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintProcessorProperty" /> represents.</summary>
      <returns>An <see cref="T:System.Object" /> that can be cast to a <see cref="T:System.Printing.PrintProcessor" />.</returns>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintProperty">
      <summary>Represents a property (and the value of the property) of a printing system hardware or software component.</summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintProperty" /> class. </summary>
      <param name="attributeName">The name of the property that this object represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintProperty.Dispose">
      <summary>Releases all resources that are being used by the <see cref="T:System.Printing.IndexedProperties.PrintProperty" />. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintProperty.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are being used by the <see cref="T:System.Printing.IndexedProperties.PrintProperty" /> and optionally releases the managed resource. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintProperty.Finalize">
      <summary>Enables a <see cref="T:System.Printing.IndexedProperties.PrintProperty" /> to attempt to free resources and to perform other cleanup operations before the <see cref="T:System.Printing.IndexedProperties.PrintProperty" /> is reclaimed by garbage collection.</summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintProperty.InternalDispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are being used by the <see cref="T:System.Printing.IndexedProperties.PrintProperty" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both the managed resources and the unmanaged resources; false to release only the unmanaged resources. </param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintProperty.IsDisposed">
      <summary>Gets or sets a value that indicates whether the object has been disposed.</summary>
      <returns>true if the object has been disposed; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintProperty.IsInitialized">
      <summary>Gets or sets a value that indicates whether the object has been initialized.</summary>
      <returns>true if the object has been initialized; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintProperty.Name">
      <summary>When overridden in a derived class, gets the name of the property that the object represents.</summary>
      <returns>A <see cref="T:System.String" /> that represents the name of the property.</returns>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintProperty.OnDeserialization(System.Object)">
      <summary>When overridden in a derived class, implements the <see cref="T:System.Runtime.Serialization.ISerializable" /> interface and raises the deserialization event when the deserialization is complete.</summary>
      <param name="sender">The source of the event. </param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintProperty.Value">
      <summary>When overridden in a derived class, gets or sets the value of the property that the object represents. </summary>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintPropertyDictionary">
      <summary>Represents a collection of properties and values that are associated with an object in the <see cref="N:System.Printing" /> namespace.</summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintPropertyDictionary.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintPropertyDictionary" /> class. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintPropertyDictionary.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintPropertyDictionary" /> class and provides it with the specified <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />. </summary>
      <param name="info">The data that is required to serialize or deserialize an object.</param>
      <param name="context">The context of the serialized stream, including the source and the destination.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintPropertyDictionary.Add(System.Printing.IndexedProperties.PrintProperty)">
      <summary>Adds the specified object (of a class that is derived from <see cref="T:System.Printing.IndexedProperties.PrintProperty" />) into the dictionary.</summary>
      <param name="attributeValue">An object (of a class that is derived from <see cref="T:System.Printing.IndexedProperties.PrintProperty" />) that represents a property of a printing system hardware or software component.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintPropertyDictionary.Dispose">
      <summary>Releases all the resources that are being used by the <see cref="T:System.Printing.IndexedProperties.PrintPropertyDictionary" />. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintPropertyDictionary.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are being used by the <see cref="T:System.Printing.IndexedProperties.PrintPropertyDictionary" /> and optionally releases the managed resources. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintPropertyDictionary.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with the data that is needed to serialize the <see cref="T:System.Printing.IndexedProperties.PrintPropertyDictionary" />.</summary>
      <param name="info">Stores all the data that is used to serialize the object.</param>
      <param name="context">Describes the context of the serialized stream, including the source and the destination.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintPropertyDictionary.GetProperty(System.String)">
      <summary>Gets the object (of a class that is derived from <see cref="T:System.Printing.IndexedProperties.PrintProperty" />) that represents the specified property.</summary>
      <returns>An object of a class that is derived from the <see cref="T:System.Printing.IndexedProperties.PrintProperty" />.</returns>
      <param name="attribName">The name of the property that is represented by the <see cref="T:System.Printing.IndexedProperties.PrintProperty" />.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintPropertyDictionary.OnDeserialization(System.Object)">
      <summary>Implements the <see cref="T:System.Runtime.Serialization.ISerializable" /> interface and raises the deserialization event when the deserialization is complete.</summary>
      <param name="sender">The source of the event. </param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintPropertyDictionary.SetProperty(System.String,System.Printing.IndexedProperties.PrintProperty)">
      <summary>Sets the value of the specified attribute to an object of a class that is derived from <see cref="T:System.Printing.IndexedProperties.PrintProperty" />.</summary>
      <param name="attribName">The name of the attribute.</param>
      <param name="attribValue">An object of a type that is derived from <see cref="T:System.Printing.IndexedProperties.PrintProperty" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="attribName" /> is already in the dictionary and it already has the value <paramref name="attribValue" />.</exception>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintQueueAttributeProperty">
      <summary>Represents a <see cref="T:System.Printing.PrintQueueAttributes" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintQueueAttributeProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintQueueAttributeProperty" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintQueueAttributes" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintQueueAttributeProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintQueueAttributeProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintQueueAttributeProperty" /> class that has the specified value for the specified property. </summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintQueueAttributes" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintQueueAttributeProperty" /> represents.</param>
      <param name="attributeValue">The value of <see cref="T:System.Object" /> the property that the <see cref="T:System.Printing.IndexedProperties.PrintQueueAttributeProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintQueueAttributeProperty.op_Implicit(System.Printing.IndexedProperties.PrintQueueAttributeProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Printing.PrintQueueAttributes">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintQueueAttributeProperty" /> to a <see cref="T:System.Printing.PrintQueueAttributes" /> value.</summary>
      <returns>A <see cref="T:System.Printing.PrintQueueAttributes" /> value.</returns>
      <param name="attributeRef">The <see cref="T:System.Printing.IndexedProperties.PrintQueueAttributeProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintQueueAttributeProperty.op_Implicit(System.Printing.IndexedProperties.PrintQueueAttributeProperty)~System.Printing.PrintQueueAttributes">
      <summary>Provides implicit conversion to a <see cref="T:System.Printing.PrintQueueAttributes" /> value from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintQueueAttributeProperty" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintQueueAttributes" /> value.</returns>
      <param name="attributeRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintQueueAttributeProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintQueueAttributeProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintQueueAttributeProperty" /> represents.</summary>
      <returns>A boxed <see cref="T:System.Printing.PrintQueueAttributes" /> value.</returns>
      <exception cref="T:System.InvalidOperationException">The property cannot be set by using the value that is provided by the calling code.</exception>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintQueueProperty">
      <summary>Represents a <see cref="T:System.Printing.PrintQueue" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintQueueProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintQueueProperty" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintQueue" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintQueueProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintQueueProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintQueueProperty" /> class that has the specified value for the specified property. </summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintQueue" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintQueueProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintQueueProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintQueueProperty.op_Implicit(System.Printing.IndexedProperties.PrintQueueProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Printing.PrintQueue">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintQueueProperty" /> to a <see cref="T:System.Printing.PrintQueue" /> value.</summary>
      <returns>A <see cref="T:System.Printing.PrintQueue" />.</returns>
      <param name="attribRef">The <see cref="T:System.Printing.IndexedProperties.PrintQueueProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintQueueProperty.op_Implicit(System.Printing.IndexedProperties.PrintQueueProperty)~System.Printing.PrintQueue">
      <summary>Provides implicit conversion to a <see cref="T:System.Printing.PrintQueue" /> value from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintQueueProperty" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintQueue" />.</returns>
      <param name="attribRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintQueueProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintQueueProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintQueueProperty" /> represents.</summary>
      <returns>An <see cref="T:System.Object" /> that can be cast to a <see cref="T:System.Printing.PrintQueue" />.</returns>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintQueueStatusProperty">
      <summary>Represents a <see cref="T:System.Printing.PrintQueueStatus" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintQueueStatusProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintQueueStatusProperty" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintQueueStatus" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintQueueStatusProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintQueueStatusProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintQueueStatusProperty" /> class that has the specified value for the specified property. </summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintQueueStatus" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintQueueStatusProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintQueueStatusProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintQueueStatusProperty.op_Implicit(System.Printing.IndexedProperties.PrintQueueStatusProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Printing.PrintQueueStatus">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintQueueStatusProperty" /> to a <see cref="T:System.Printing.PrintQueueStatus" /> value.</summary>
      <returns>A <see cref="T:System.Printing.PrintQueueStatus" /> value.</returns>
      <param name="attributeRef">The <see cref="T:System.Printing.IndexedProperties.PrintQueueStatusProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintQueueStatusProperty.op_Implicit(System.Printing.IndexedProperties.PrintQueueStatusProperty)~System.Printing.PrintQueueStatus">
      <summary>Provides implicit conversion to a <see cref="T:System.Printing.PrintQueueStatus" /> value from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintQueueStatusProperty" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintQueueStatus" /> value.</returns>
      <param name="attributeRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintQueueStatusProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintQueueStatusProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintQueueStatusProperty" /> represents.</summary>
      <returns>A boxed <see cref="T:System.Printing.PrintQueueStatus" />. </returns>
      <exception cref="T:System.InvalidOperationException">The property cannot be set by using the value that is provided by the calling code.</exception>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintServerLoggingProperty">
      <summary>Represents a <see cref="T:System.Printing.PrintServerEventLoggingTypes" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintServerLoggingProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintServerLoggingProperty" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintServerEventLoggingTypes" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintServerLoggingProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintServerLoggingProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintServerLoggingProperty" /> class that has the specified value for the specified property. </summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintServerEventLoggingTypes" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintServerLoggingProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintServerLoggingProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintServerLoggingProperty.op_Implicit(System.Printing.IndexedProperties.PrintServerLoggingProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Printing.PrintServerEventLoggingTypes">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintServerLoggingProperty" /> (or from a pointer to such a property) to a <see cref="T:System.Printing.PrintServerEventLoggingTypes" /> value.</summary>
      <returns>A <see cref="T:System.Printing.PrintServerEventLoggingTypes" /> value.</returns>
      <param name="attribRef">The <see cref="T:System.Printing.IndexedProperties.PrintServerLoggingProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintServerLoggingProperty.op_Implicit(System.Printing.IndexedProperties.PrintServerLoggingProperty)~System.Printing.PrintServerEventLoggingTypes">
      <summary>Provides implicit conversion to a <see cref="T:System.Printing.PrintServerEventLoggingTypes" /> value from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintServerLoggingProperty" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintServerEventLoggingTypes" /> value.</returns>
      <param name="attribRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintServerLoggingProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintServerLoggingProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintServerLoggingProperty" /> represents.</summary>
      <returns>A boxed <see cref="T:System.Printing.PrintServerEventLoggingTypes" /> value.</returns>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintServerProperty">
      <summary>Represents a <see cref="T:System.Printing.PrintServer" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintServerProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintServerProperty" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintServer" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintServerProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintServerProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintServerProperty" /> class that has the specified value for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintServer" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintServerProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintServerProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintServerProperty.op_Implicit(System.Printing.IndexedProperties.PrintServerProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Printing.PrintServer">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintServerProperty" /> to a <see cref="T:System.Printing.PrintServer" /> value.</summary>
      <returns>A <see cref="T:System.Printing.PrintServer" />.</returns>
      <param name="attribRef">The <see cref="T:System.Printing.IndexedProperties.PrintServerProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintServerProperty.op_Implicit(System.Printing.IndexedProperties.PrintServerProperty)~System.Printing.PrintServer">
      <summary>Provides implicit conversion to a <see cref="T:System.Printing.PrintServer" /> value from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintServerProperty" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintServer" />.</returns>
      <param name="attribRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintServerProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintServerProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintServerProperty" /> represents.</summary>
      <returns>A <see cref="T:System.Object" /> that can be cast as a <see cref="T:System.Printing.PrintServer" />.</returns>
      <exception cref="T:System.InvalidOperationException">The property is not internally initialized.</exception>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintStreamProperty">
      <summary>Represents a <see cref="T:System.IO.Stream" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintStreamProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintStreamProperty" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.IO.Stream" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintStreamProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintStreamProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintStreamProperty" /> class that has the specified value for the specified property. </summary>
      <param name="attributeName">The name of the <see cref="T:System.IO.Stream" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintStreamProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintStreamProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintStreamProperty.op_Implicit(System.Printing.IndexedProperties.PrintStreamProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.IO.Stream">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintStreamProperty" /> to a <see cref="T:System.IO.Stream" /> value.</summary>
      <returns>A <see cref="T:System.IO.Stream" />.</returns>
      <param name="attributeRef">The <see cref="T:System.Printing.IndexedProperties.PrintStreamProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintStreamProperty.op_Implicit(System.Printing.IndexedProperties.PrintStreamProperty)~System.IO.Stream">
      <summary>Provides implicit conversion to a <see cref="T:System.IO.Stream" /> value from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintStreamProperty" />. </summary>
      <returns>A <see cref="T:System.IO.Stream" />.</returns>
      <param name="attributeRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintStreamProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintStreamProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintStreamProperty" /> represents.</summary>
      <returns>An <see cref="T:System.Object" /> that can be cast as a <see cref="T:System.IO.Stream" />.</returns>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintStringProperty">
      <summary>Represents a <see cref="T:System.String" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintStringProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintStringProperty" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.String" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintStringProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintStringProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintStringProperty" /> class that has the specified value for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.String" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintStringProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintStringProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintStringProperty.op_Implicit(System.Printing.IndexedProperties.PrintStringProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.String">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintStringProperty" /> to a <see cref="T:System.String" /> value.</summary>
      <returns>A <see cref="T:System.String" />.</returns>
      <param name="attributeRef">The <see cref="T:System.Printing.IndexedProperties.PrintStringProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintStringProperty.op_Implicit(System.Printing.IndexedProperties.PrintStringProperty)~System.String">
      <summary>Provides implicit conversion to a <see cref="T:System.String" /> value from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintStringProperty" />.</summary>
      <returns>A <see cref="T:System.String" />.</returns>
      <param name="attributeRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintStringProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintStringProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintStringProperty" /> represents.</summary>
      <returns>A boxed <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintSystemTypeProperty">
      <summary>Represents a <see cref="T:System.Type" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintSystemTypeProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintSystemTypeProperty" /> class that has the specified property name.</summary>
      <param name="attributeName">The name of the property that the <see cref="T:System.Printing.IndexedProperties.PrintSystemTypeProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintSystemTypeProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintSystemTypeProperty" /> class that has the specified property name and value.</summary>
      <param name="attributeName">The name of the property that the <see cref="T:System.Printing.IndexedProperties.PrintSystemTypeProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintSystemTypeProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintSystemTypeProperty.op_Implicit(System.Printing.IndexedProperties.PrintSystemTypeProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Type">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintSystemTypeProperty" /> to a <see cref="T:System.Type" /> value.</summary>
      <returns>A <see cref="T:System.Type" />.</returns>
      <param name="attribRef">The <see cref="T:System.Printing.IndexedProperties.PrintSystemTypeProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintSystemTypeProperty.op_Implicit(System.Printing.IndexedProperties.PrintSystemTypeProperty)~System.Type">
      <summary>Provides implicit conversion to a <see cref="T:System.Type" /> value from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintSystemTypeProperty" />.</summary>
      <returns>A <see cref="T:System.Type" />.</returns>
      <param name="attribRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintSystemTypeProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintSystemTypeProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintSystemTypeProperty" /> represents.</summary>
      <returns>An <see cref="T:System.Object" />. </returns>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintThreadPriorityProperty">
      <summary>Represents a <see cref="T:System.Threading.ThreadPriority" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintThreadPriorityProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintThreadPriorityProperty" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Threading.ThreadPriority" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintThreadPriorityProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintThreadPriorityProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintThreadPriorityProperty" /> class that has the specified value for the specified property. </summary>
      <param name="attributeName">The name of the <see cref="T:System.Threading.ThreadPriority" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintThreadPriorityProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintThreadPriorityProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintThreadPriorityProperty.op_Implicit(System.Printing.IndexedProperties.PrintThreadPriorityProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Threading.ThreadPriority">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintThreadPriorityProperty" /> to a <see cref="T:System.Threading.ThreadPriority" /> value.</summary>
      <returns>A <see cref="T:System.Threading.ThreadPriority" /> value.</returns>
      <param name="attribRef">The <see cref="T:System.Printing.IndexedProperties.PrintThreadPriorityProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintThreadPriorityProperty.op_Implicit(System.Printing.IndexedProperties.PrintThreadPriorityProperty)~System.Threading.ThreadPriority">
      <summary>Provides implicit conversion to a <see cref="T:System.Threading.ThreadPriority" /> value from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintThreadPriorityProperty" />.</summary>
      <returns>A <see cref="T:System.Threading.ThreadPriority" /> value.</returns>
      <param name="attribRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintThreadPriorityProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintThreadPriorityProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintThreadPriorityProperty" /> represents.</summary>
      <returns>A boxed <see cref="T:System.Threading.ThreadPriority" /> value.</returns>
    </member>
    <member name="T:System.Printing.IndexedProperties.PrintTicketProperty">
      <summary>Represents a <see cref="T:System.Printing.PrintTicket" /> property (and its value) of a printing system hardware or software component. </summary>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintTicketProperty.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintTicketProperty" /> class for the specified property.</summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintTicket" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintTicketProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintTicketProperty.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.IndexedProperties.PrintTicketProperty" /> class that has the specified value for the specified property. </summary>
      <param name="attributeName">The name of the <see cref="T:System.Printing.PrintTicket" /> property that the <see cref="T:System.Printing.IndexedProperties.PrintTicketProperty" /> represents.</param>
      <param name="attributeValue">The value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintTicketProperty" /> represents.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintTicketProperty.op_Implicit(System.Printing.IndexedProperties.PrintTicketProperty|System.Runtime.CompilerServices.IsImplicitlyDereferenced)~System.Printing.PrintTicket">
      <summary>Provides implicit conversion from a <see cref="T:System.Printing.IndexedProperties.PrintTicketProperty" /> to a <see cref="T:System.Printing.PrintTicket" /> value.</summary>
      <returns>A <see cref="T:System.Printing.PrintTicket" />.</returns>
      <param name="attribRef">The <see cref="T:System.Printing.IndexedProperties.PrintTicketProperty" />  that is converted.</param>
    </member>
    <member name="M:System.Printing.IndexedProperties.PrintTicketProperty.op_Implicit(System.Printing.IndexedProperties.PrintTicketProperty)~System.Printing.PrintTicket">
      <summary>Provides implicit conversion to a <see cref="T:System.Printing.PrintTicket" /> value from a pointer to a <see cref="T:System.Printing.IndexedProperties.PrintTicketProperty" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintTicket" />.</returns>
      <param name="attribRef">A pointer to the <see cref="T:System.Printing.IndexedProperties.PrintTicketProperty" />  that is converted.</param>
    </member>
    <member name="P:System.Printing.IndexedProperties.PrintTicketProperty.Value">
      <summary>Gets or sets the value of the property that the <see cref="T:System.Printing.IndexedProperties.PrintTicketProperty" /> represents.</summary>
      <returns>An <see cref="T:System.Object" /> that can be cast to a <see cref="T:System.Printing.PrintTicket" />.</returns>
    </member>
    <member name="T:System.Windows.Xps.VisualsToXpsDocument">
      <summary>Provides methods for writing <see cref="T:System.Windows.Media.Visual" /> objects to XML Paper Specification (XPS) documents or to a print queue in batch mode. </summary>
    </member>
    <member name="M:System.Windows.Xps.VisualsToXpsDocument.BeginBatchWrite">
      <summary>Indicates that write operations can begin.</summary>
    </member>
    <member name="M:System.Windows.Xps.VisualsToXpsDocument.Cancel">
      <summary>Cancels a synchronous writing operation.</summary>
      <exception cref="T:System.Windows.Xps.XpsWriterException">The state of the <see cref="T:System.Windows.Xps.VisualsToXpsDocument" /> is not compatible with a <see cref="M:System.Windows.Xps.VisualsToXpsDocument.Cancel" /> operation.</exception>
    </member>
    <member name="M:System.Windows.Xps.VisualsToXpsDocument.CancelAsync">
      <summary>Cancels an asynchronous writing operation.</summary>
      <exception cref="T:System.Windows.Xps.XpsWriterException">The state of the <see cref="T:System.Windows.Xps.VisualsToXpsDocument" /> is not compatible with a <see cref="M:System.Windows.Xps.VisualsToXpsDocument.CancelAsync" /> operation.</exception>
    </member>
    <member name="M:System.Windows.Xps.VisualsToXpsDocument.EndBatchWrite">
      <summary>Indicates that write operations must end.</summary>
    </member>
    <member name="M:System.Windows.Xps.VisualsToXpsDocument.Write(System.Windows.Media.Visual)">
      <summary>Writes a <see cref="T:System.Windows.Media.Visual" /> synchronously to an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or a <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="visual">The <see cref="T:System.Windows.Media.Visual" /> that is written.</param>
    </member>
    <member name="M:System.Windows.Xps.VisualsToXpsDocument.Write(System.Windows.Media.Visual,System.Printing.PrintTicket)">
      <summary>Writes a <see cref="T:System.Windows.Media.Visual" /> synchronously to an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or a <see cref="T:System.Printing.PrintQueue" /> and includes a <see cref="T:System.Printing.PrintTicket" />.</summary>
      <param name="visual">The <see cref="T:System.Windows.Media.Visual" /> that is written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the document.</param>
    </member>
    <member name="M:System.Windows.Xps.VisualsToXpsDocument.WriteAsync(System.Windows.Media.Visual)">
      <summary>Writes a <see cref="T:System.Windows.Media.Visual" /> asynchronously to an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or a <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="visual">The <see cref="T:System.Windows.Media.Visual" /> that is written.</param>
    </member>
    <member name="M:System.Windows.Xps.VisualsToXpsDocument.WriteAsync(System.Windows.Media.Visual,System.Object)">
      <summary>Writes a <see cref="T:System.Windows.Media.Visual" /> asynchronously to an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or a <see cref="T:System.Printing.PrintQueue" /> and includes additional information that the caller wants to pass to an event handler. </summary>
      <param name="visual">The <see cref="T:System.Windows.Media.Visual" /> that is written.</param>
      <param name="userSuppliedState">An object that contains data that the caller wants to pass to the <see cref="E:System.Windows.Xps.XpsDocumentWriter.WritingCompleted" /> event handler. </param>
    </member>
    <member name="M:System.Windows.Xps.VisualsToXpsDocument.WriteAsync(System.Windows.Media.Visual,System.Printing.PrintTicket)">
      <summary>Writes a <see cref="T:System.Windows.Media.Visual" /> asynchronously to an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or a <see cref="T:System.Printing.PrintQueue" /> and includes a <see cref="T:System.Printing.PrintTicket" />.</summary>
      <param name="visual">The <see cref="T:System.Windows.Media.Visual" /> that is written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the document.</param>
    </member>
    <member name="M:System.Windows.Xps.VisualsToXpsDocument.WriteAsync(System.Windows.Media.Visual,System.Printing.PrintTicket,System.Object)">
      <summary>Writes a <see cref="T:System.Windows.Media.Visual" /> asynchronously to an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or a <see cref="T:System.Printing.PrintQueue" />; also includes a <see cref="T:System.Printing.PrintTicket" /> and any additional information that the caller wants to pass to an event handler.</summary>
      <param name="visual">The <see cref="T:System.Windows.Media.Visual" /> that is written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the document.</param>
      <param name="userSuppliedState">An object that contains the data that the caller wants to pass to the <see cref="E:System.Windows.Xps.XpsDocumentWriter.WritingCompleted" /> event handler. </param>
    </member>
    <member name="T:System.Windows.Xps.XpsDocumentNotificationLevel">
      <summary>Indicates whether a write operation to an XML Paper Specification (XPS) document or a print queue sends back page-by-page and document-by-document progress notifications.</summary>
    </member>
    <member name="F:System.Windows.Xps.XpsDocumentNotificationLevel.None">
      <summary>The notification status is not indicated.</summary>
    </member>
    <member name="F:System.Windows.Xps.XpsDocumentNotificationLevel.ReceiveNotificationDisabled">
      <summary>Progress notifications are disabled.</summary>
    </member>
    <member name="F:System.Windows.Xps.XpsDocumentNotificationLevel.ReceiveNotificationEnabled">
      <summary>Progress notifications are enabled.</summary>
    </member>
    <member name="T:System.Windows.Xps.XpsDocumentWriter">
      <summary>Provides methods to write to an XPS document or print queue.  </summary>
    </member>
    <member name="E:System.Windows.Xps.XpsDocumentWriter._WritingCancelled">
      <summary>Occurs when a <see cref="Overload:System.Windows.Xps.XpsDocumentWriter.Write" /> or <see cref="Overload:System.Windows.Xps.XpsDocumentWriter.WriteAsync" /> operation is canceled.</summary>
    </member>
    <member name="E:System.Windows.Xps.XpsDocumentWriter._WritingCompleted">
      <summary>Occurs when a write operation finishes.</summary>
    </member>
    <member name="E:System.Windows.Xps.XpsDocumentWriter._WritingProgressChanged">
      <summary>Occurs when the <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> updates its progress.</summary>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.CancelAsync">
      <summary>Cancels the current <see cref="Overload:System.Windows.Xps.XpsDocumentWriter.WriteAsync" /> operation.</summary>
      <exception cref="T:System.Windows.Xps.XpsWriterException">No asynchronous write operation is in progress.</exception>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.CreateVisualsCollator">
      <summary>Returns a <see cref="T:System.Windows.Xps.VisualsToXpsDocument" /> that can write <see cref="T:System.Windows.Media.Visual" /> objects to a document or print queue.</summary>
      <returns>The new <see cref="T:System.Windows.Xps.VisualsToXpsDocument" />. </returns>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.CreateVisualsCollator(System.Printing.PrintTicket,System.Printing.PrintTicket)">
      <summary>Returns a <see cref="T:System.Windows.Xps.VisualsToXpsDocument" /> that can write <see cref="T:System.Windows.Media.Visual" /> objects with <see cref="T:System.Printing.PrintTicket" /> settings to an XPS document or print queue.</summary>
      <returns>The new <see cref="T:System.Windows.Xps.VisualsToXpsDocument" /> that writes <see cref="T:System.Windows.Media.Visual" /> elements with <see cref="T:System.Printing.PrintTicket" /> settings to the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</returns>
      <param name="documentSequencePrintTicket">A <see cref="T:System.Printing.PrintTicket" /> that specifies the default printing preferences for the document sequence.</param>
      <param name="documentPrintTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for each document.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.Write(System.String)">
      <summary>Writes synchronously a specified XPS document to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="documentPath">The path of the source document.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.Write(System.String,System.Windows.Xps.XpsDocumentNotificationLevel)">
      <summary>Writes synchronously a specified XPS document to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="documentPath">The path of the source document.</param>
      <param name="notificationLevel">An indication of whether notification is enabled.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.Write(System.Windows.Documents.DocumentPaginator)">
      <summary>Writes synchronously paginated content from a specified <see cref="T:System.Windows.Documents.DocumentPaginator" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="documentPaginator">An object that contains a pointer to unpaginated source material and also contains methods for paginating the material.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.Write(System.Windows.Documents.DocumentPaginator,System.Printing.PrintTicket)">
      <summary>Writes synchronously paginated content from a specified <see cref="T:System.Windows.Documents.DocumentPaginator" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="documentPaginator">An object that contains a pointer to unpaginated source material and also contains methods for paginating the material.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for material.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.Write(System.Windows.Documents.FixedDocument)">
      <summary>Writes synchronously a specified <see cref="T:System.Windows.Documents.FixedDocument" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedDocument">A document that is written to the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.Write(System.Windows.Documents.FixedDocument,System.Printing.PrintTicket)">
      <summary>Writes synchronously a <see cref="T:System.Windows.Documents.FixedDocument" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedDocument">The document that is written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the document.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.Write(System.Windows.Documents.FixedDocumentSequence)">
      <summary>Writes synchronously a specified <see cref="T:System.Windows.Documents.FixedDocumentSequence" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedDocumentSequence">A set of documents that is written to the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.Write(System.Windows.Documents.FixedDocumentSequence,System.Printing.PrintTicket)">
      <summary>Writes synchronously a specified <see cref="T:System.Windows.Documents.FixedDocumentSequence" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedDocumentSequence">The set of documents that are written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the set of documents.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.Write(System.Windows.Documents.FixedPage)">
      <summary>Writes synchronously a specified <see cref="T:System.Windows.Documents.FixedPage" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedPage">A page that is written to the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.Write(System.Windows.Documents.FixedPage,System.Printing.PrintTicket)">
      <summary>Writes synchronously a specified <see cref="T:System.Windows.Documents.FixedPage" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedPage">The page that is written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the page.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.Write(System.Windows.Media.Visual)">
      <summary>Writes synchronously a specified <see cref="T:System.Windows.Media.Visual" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="visual">The <see cref="T:System.Windows.Media.Visual" /> that is written.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.Write(System.Windows.Media.Visual,System.Printing.PrintTicket)">
      <summary>Writes synchronously a specified <see cref="T:System.Windows.Media.Visual" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="visual">The <see cref="T:System.Windows.Media.Visual" /> that is written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the document.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.String)">
      <summary>Writes asynchronously a specified XPS document to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="documentPath">The path of the source document.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.String,System.Windows.Xps.XpsDocumentNotificationLevel)">
      <summary>Writes asynchronously a specified XPS document with notification option to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="documentPath">The path of the source document.</param>
      <param name="notificationLevel">An indication of whether notification is enabled.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.DocumentPaginator)">
      <summary>Writes asynchronously paginated content from a specified <see cref="T:System.Windows.Documents.DocumentPaginator" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="documentPaginator">An object that contains a pointer to unpaginated source material and also contains methods for paginating the material.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.DocumentPaginator,System.Object)">
      <summary>Writes asynchronously paginated content from a specified <see cref="T:System.Windows.Documents.DocumentPaginator" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="documentPaginator">An object that contains a pointer to unpaginated source material; also contains methods for paginating the material.</param>
      <param name="userSuppliedState">A user-specified object to identify and associate with the asynchronous operation.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.DocumentPaginator,System.Printing.PrintTicket)">
      <summary>Writes asynchronously paginated content from a specified <see cref="T:System.Windows.Documents.DocumentPaginator" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="documentPaginator">An object that contains a pointer to unpaginated source material and also contains methods for paginating the material.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the material.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.DocumentPaginator,System.Printing.PrintTicket,System.Object)">
      <summary>Writes asynchronously paginated content from a specified <see cref="T:System.Windows.Documents.DocumentPaginator" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="documentPaginator">An object that contains a pointer to unpaginated source material; also contains methods for paginating the material.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the material.</param>
      <param name="userSuppliedState">A user-specified object to identify and associate with the asynchronous operation.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.FixedDocument)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Documents.FixedDocument" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedDocument">The document that is written.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.FixedDocument,System.Object)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Documents.FixedDocument" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedDocument">The document that is written.</param>
      <param name="userSuppliedState">A user-specified object to identify and associate with the asynchronous operation.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.FixedDocument,System.Printing.PrintTicket)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Documents.FixedDocument" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedDocument">The document that is written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the document.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.FixedDocument,System.Printing.PrintTicket,System.Object)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Documents.FixedDocument" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedDocument">The document that is written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the document.</param>
      <param name="userSuppliedState">A user-specified object to identify and associate with the asynchronous operation.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.FixedDocumentSequence)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Documents.FixedDocumentSequence" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedDocumentSequence">The set of documents that is written.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.FixedDocumentSequence,System.Object)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Documents.FixedDocumentSequence" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedDocumentSequence">The set of documents that are written.</param>
      <param name="userSuppliedState">A user-specified object to identify and associate with the asynchronous operation.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.FixedDocumentSequence,System.Printing.PrintTicket)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Documents.FixedDocumentSequence" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedDocumentSequence">The set of documents that are written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the set of documents.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.FixedDocumentSequence,System.Printing.PrintTicket,System.Object)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Documents.FixedDocumentSequence" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedDocumentSequence">The set of documents to be written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the set of documents.</param>
      <param name="userSuppliedState">A user-specified object to identify and associate with the asynchronous operation.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.FixedPage)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Documents.FixedPage" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedPage">The page that is written.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.FixedPage,System.Object)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Documents.FixedPage" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedPage">The page that is written.</param>
      <param name="userSuppliedState">A user-specified object to identify and associate with the asynchronous operation.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.FixedPage,System.Printing.PrintTicket)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Documents.FixedPage" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedPage">The page that is written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the page.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Documents.FixedPage,System.Printing.PrintTicket,System.Object)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Documents.FixedPage" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="fixedPage">The page that is written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the page.</param>
      <param name="userSuppliedState">A user-specified object to identify and associate with the asynchronous operation.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Media.Visual)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Media.Visual" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="visual">The <see cref="T:System.Windows.Media.Visual" /> that is written.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Media.Visual,System.Object)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Media.Visual" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="visual">The <see cref="T:System.Windows.Media.Visual" /> that is written.</param>
      <param name="userSuppliedState">A user-specified object to identify and associate with the asynchronous operation.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Media.Visual,System.Printing.PrintTicket)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Media.Visual" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="visual">The <see cref="T:System.Windows.Media.Visual" /> that is written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the document.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsDocumentWriter.WriteAsync(System.Windows.Media.Visual,System.Printing.PrintTicket,System.Object)">
      <summary>Writes asynchronously a specified <see cref="T:System.Windows.Media.Visual" /> together with a <see cref="T:System.Printing.PrintTicket" /> to the target <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> or <see cref="T:System.Printing.PrintQueue" />.</summary>
      <param name="visual">The <see cref="T:System.Windows.Media.Visual" /> that is written.</param>
      <param name="printTicket">A <see cref="T:System.Printing.PrintTicket" /> that represents the default printing preferences for the document.</param>
      <param name="userSuppliedState">A user-specified object to identify and associate with the asynchronous operation.</param>
    </member>
    <member name="E:System.Windows.Xps.XpsDocumentWriter.WritingCancelled">
      <summary>Occurs when a <see cref="Overload:System.Windows.Xps.XpsDocumentWriter.Write" /> or <see cref="Overload:System.Windows.Xps.XpsDocumentWriter.WriteAsync" /> operation is canceled.</summary>
    </member>
    <member name="E:System.Windows.Xps.XpsDocumentWriter.WritingCompleted">
      <summary>Occurs when a write operation finishes.</summary>
    </member>
    <member name="E:System.Windows.Xps.XpsDocumentWriter.WritingPrintTicketRequired">
      <summary>Occurs just before a <see cref="Overload:System.Windows.Xps.XpsDocumentWriter.Write" /> or <see cref="Overload:System.Windows.Xps.XpsDocumentWriter.WriteAsync" /> method adds a <see cref="T:System.Printing.PrintTicket" /> to a document or print queue.</summary>
    </member>
    <member name="E:System.Windows.Xps.XpsDocumentWriter.WritingProgressChanged">
      <summary>Occurs when the <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> updates its progress.</summary>
    </member>
    <member name="T:System.Windows.Xps.XpsWriterException">
      <summary>The exception that is thrown when a method of either an <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> or a <see cref="T:System.Windows.Xps.VisualsToXpsDocument" /> object is called that is incompatible with the current state of the object. </summary>
    </member>
    <member name="M:System.Windows.Xps.XpsWriterException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsWriterException" /> class. </summary>
    </member>
    <member name="M:System.Windows.Xps.XpsWriterException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsWriterException" /> class that provides specific <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />. This constructor is protected.</summary>
      <param name="info">The data that is required to serialize or deserialize an object.</param>
      <param name="context">The context, which includes source and destination, of the serialized stream.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsWriterException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsWriterException" /> class that provides a specific error condition.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsWriterException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsWriterException" /> class that provides a specific error condition and includes the cause of the exception. </summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="innerException">The underlying error that caused the <see cref="T:System.Windows.Xps.XpsWriterException" />.</param>
    </member>
  </members>
</doc>