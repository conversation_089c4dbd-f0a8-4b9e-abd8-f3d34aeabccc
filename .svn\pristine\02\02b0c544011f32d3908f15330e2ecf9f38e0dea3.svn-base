using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    public static class PrimaryScreen
    {
        public static float ScreenScalingFactor { get; set; }

        [DllImport("user32.dll")]
        public static extern bool EnumDisplaySettings(string lpszDeviceName, int iModeNum, ref DEVMODE lpDevMode);

        [StructLayout(LayoutKind.Sequential)]
        public struct DEVMODE
        {
            private const int CCHDEVICENAME = 0x20;
            private const int CCHFORMNAME = 0x20;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 0x20)]
            public string dmDeviceName;
            public short dmSpecVersion;
            public short dmDriverVersion;
            public short dmSize;
            public short dmDriverExtra;
            public int dmFields;
            public int dmPositionX;
            public int dmPositionY;
            public ScreenOrientation dmDisplayOrientation;
            public int dmDisplayFixedOutput;
            public short dmColor;
            public short dmDuplex;
            public short dmYResolution;
            public short dmTTOption;
            public short dmCollate;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 0x20)]
            public string dmFormName;
            public short dmLogPixels;
            public int dmBitsPerPel;
            public int dmPelsWidth;
            public int dmPelsHeight;
            public int dmDisplayFlags;
            public int dmDisplayFrequency;
            public int dmICMMethod;
            public int dmICMIntent;
            public int dmMediaType;
            public int dmDitherType;
            public int dmReserved1;
            public int dmReserved2;
            public int dmPanningWidth;
            public int dmPanningHeight;
        }

        public class ScreenInfo
        {
            public int Index { get; set; }

            public float Scal { get; set; }

            public Rectangle RealRectangle { get; set; }

            public Rectangle DisplayRectangle { get; set; }

            public Screen Screen { get; set; }
        }

        public static List<ScreenInfo> NowScreen = new List<ScreenInfo>();

        public static ScreenInfo InitScreens(Control ctrl = null)
        {
            NowScreen = new List<ScreenInfo>();
            int index = 1;
            foreach (var screen in Screen.AllScreens)
            {
                var dm = new DEVMODE { dmSize = (short)Marshal.SizeOf(typeof(DEVMODE)) };
                EnumDisplaySettings(screen.DeviceName, -1, ref dm);
                ScreenInfo item = new ScreenInfo()
                {
                    Screen = screen,
                    Index = index,
                    RealRectangle = new Rectangle(screen.Bounds.Location, new Size(dm.dmPelsWidth, dm.dmPelsHeight)),
                    DisplayRectangle = screen.Bounds
                };
                item.Scal = Math.Min(
                    item.RealRectangle.Width * 1.0f / item.DisplayRectangle.Width,
                    item.RealRectangle.Height * 1.0f / item.DisplayRectangle.Height);
                NowScreen.Add(item);
                index++;
            }
            ScreenScalingFactor = scaling();
            ScreenInfo nowScreen = null;
            if (ctrl != null)
            {
                var sec = Screen.FromControl(ctrl);
                nowScreen = NowScreen.FirstOrDefault(p => Equals(sec, p.Screen));
            }
            return nowScreen;
        }

        public static Rectangle GetDisplayRectangle(Rectangle rectangle)
        {
            if (NowScreen?.Count > 1 && NowScreen.Exists(p => !Equals(p.Scal, 1)))
            {
                var first = NowScreen.FirstOrDefault(p => p.Index == 1);
                var sec = NowScreen.FirstOrDefault(p => p.Index == 2);
                if (sec.RealRectangle.Contains(rectangle))
                {
                    var step = sec.Scal * 1.0d / first.Scal;
                    rectangle = new Rectangle((int)(rectangle.X * step), (int)(rectangle.Y * step)
                        , (int)(rectangle.Width * step), (int)(rectangle.Height * step));
                }
            }
            return rectangle;
        }

        public static Rectangle GetAllRectangle(ref Rectangle workAreaBounds)
        {
            InitScreens();
            //var firstScreen = NowScreen.FirstOrDefault(p => p.Index == 1);
            //var width = firstScreen.DisplayRectangle.Width + NowScreen.Where(p => p.Index > 1).Sum(p => p.RealRectangle.Width);
            //var height = firstScreen.DisplayRectangle.Height;
            //workAreaBounds = new Rectangle(Point.Empty, new Size(width, height));
            var result = new Rectangle(Point.Empty, new Size(NowScreen.Sum(p => p.RealRectangle.Width), NowScreen.Max(p => p.RealRectangle.Height)));

            workAreaBounds = new Rectangle(Point.Empty, new Size((int)(result.Width / ScreenScalingFactor), (int)(result.Height / ScreenScalingFactor)));
            return result;
        }

        public enum DeviceCap
        {
            /// <summary>
            /// Horizontal width in pixels
            /// </summary>
            HORZRES = 8,
            /// <summary>
            /// Vertical height in pixels
            /// </summary>
            VERTRES = 10,
            /// <summary>
            /// Vertical height of entire desktop in pixels
            /// </summary>
            DESKTOPVERTRES = 117,
            /// <summary>
            /// Horizontal width of entire desktop in pixels
            /// </summary>
            DESKTOPHORZRES = 118,
        }

        public static float scaling()
        {
            using (var g = Graphics.FromHwnd(IntPtr.Zero))
            {
                IntPtr desktop = g.GetHdc();
                int WorkAreaScreenHeight = GetDeviceCaps(desktop, (int)DeviceCap.VERTRES);
                int PhysicalScreenHeight = GetDeviceCaps(desktop, (int)DeviceCap.DESKTOPVERTRES);

                int WorkAreaScreenWidth = GetDeviceCaps(desktop, (int)DeviceCap.HORZRES);
                int PhysicalScreenWidth = GetDeviceCaps(desktop, (int)DeviceCap.DESKTOPHORZRES);
                g.ReleaseHdc(desktop);
                return (float)PhysicalScreenHeight / WorkAreaScreenHeight;
            }
        }

        //public static float GetScreenScalingFactor()
        //{
        //    using (Graphics g = Graphics.FromHwnd(IntPtr.Zero))
        //    {
        //        IntPtr desktop = g.GetHdc();
        //        int LogicalScreenHeight = GetDeviceCaps(desktop, 10);//(int)DeviceCap.VERTRES);
        //        int PhysicalScreenHeight = GetDeviceCaps(desktop, 117);// (int)DeviceCap.DESKTOPVERTRES);
        //        int logpixelsy = GetDeviceCaps(desktop, 90);// (int)DeviceCap.LOGPIXELSY);
        //        float screenScalingFactor = (float)PhysicalScreenHeight / LogicalScreenHeight;
        //        float dpiScalingFactor = logpixelsy / 96f;
        //        ScreenScalingFactor = Math.Max(screenScalingFactor, dpiScalingFactor);
        //        g.ReleaseHdc(desktop);
        //    }
        //    return ScreenScalingFactor;
        //}

        [DllImport("gdi32.dll")]
        private static extern int GetDeviceCaps(IntPtr hdc, int nIndex);

        public static int DpiValue(this int num)
        {
            return num;
            //return Convert.ToInt32(Convert.ToDouble(num) * DpiZoom);
        }
    }
}