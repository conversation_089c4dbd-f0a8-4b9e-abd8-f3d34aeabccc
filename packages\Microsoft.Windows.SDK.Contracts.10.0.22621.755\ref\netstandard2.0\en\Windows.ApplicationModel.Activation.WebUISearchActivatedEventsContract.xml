﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract</name>
  </assembly>
  <members>
    <member name="T:Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract">
      <summary>
      </summary>
    </member>
    <member name="T:Windows.UI.WebUI.WebUISearchActivatedEventArgs">
      <summary>Provides information about the activated event that fires when the user searches the app from the Search charm and the app isn't the main app on screen.</summary>
    </member>
    <member name="P:Windows.UI.WebUI.WebUISearchActivatedEventArgs.ActivatedOperation">
      <summary>Gets the app activated operation.</summary>
      <returns>The activation operation.</returns>
    </member>
    <member name="P:Windows.UI.WebUI.WebUISearchActivatedEventArgs.CurrentlyShownApplicationViewId">
      <summary>Gets the identifier for the currently shown app view.</summary>
      <returns>The identifier for the currently shown app view.</returns>
    </member>
    <member name="P:Windows.UI.WebUI.WebUISearchActivatedEventArgs.Kind">
      <summary>Gets the activation type.</summary>
      <returns>The activationKind.search enumeration value.</returns>
    </member>
    <member name="P:Windows.UI.WebUI.WebUISearchActivatedEventArgs.Language">
      <summary>Gets the Internet Engineering Task Force (IETF) language tag (BCP 47 standard) that identifies the language currently associated with the user's text input device.</summary>
      <returns>The Internet Engineering Task Force (IETF) BCP 47 standard language tag.</returns>
    </member>
    <member name="P:Windows.UI.WebUI.WebUISearchActivatedEventArgs.LinguisticDetails">
      <summary>Gets a SearchPaneQueryLinguisticDetails object that provides info about query text that the user enters through an Input Method Editor (IME).</summary>
      <returns>The object that provides info about query text.</returns>
    </member>
    <member name="P:Windows.UI.WebUI.WebUISearchActivatedEventArgs.PreviousExecutionState">
      <summary>Gets the execution state of the app before it was activated.</summary>
      <returns>One of the enumeration values.</returns>
    </member>
    <member name="P:Windows.UI.WebUI.WebUISearchActivatedEventArgs.QueryText">
      <summary>Gets the text that the user wants the app to search for. The user entered this text into the search box of the search pane.</summary>
      <returns>The text to search for.</returns>
    </member>
    <member name="P:Windows.UI.WebUI.WebUISearchActivatedEventArgs.SplashScreen">
      <summary>Gets the splash screen object that provides information about the transition from the splash screen to the activated app.</summary>
      <returns>The object that provides splash screen information.</returns>
    </member>
  </members>
</doc>