﻿using OCRTools;
using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Net;
using System.Reflection;
using System.Threading.Tasks;
using System.Web;

namespace ImageLib
{
    /// <summary>
    /// https://cloud.tencent.com/act/pro/ciExhibition?tab=contentReview&sub=pictureReview
    /// </summary>
    public class TencentUpload
    {
        public static bool Enable { get; set; } = true;

        private static List<string> LstBucket = new List<string>() { "https://ci-h5-bj-1258125638.cos.ap-beijing.myqcloud.com/", "https://ci-h5-demo-1258125638.cos.ap-chengdu.myqcloud.com/" };

        private static QQCloudRoot GetToken1(string fileName, string method)
        {
            var html = WebClientExt.GetHtml("https://ci-exhibition.cloud.tencent.com/sts?prefix=" + HttpUtility.UrlEncode(fileName) + "&method=" + method + "&action=");
            if (!string.IsNullOrEmpty(html) && html.Length > 2)
            {
                return html.DeserializeJson<QQCloudRoot>();
            }

            return null;
        }

        public static string GetResult(byte[] content, bool isZip = false)
        {
            var result = string.Empty;
            var fileName = "users/source." + Guid.NewGuid().ToString().ToLower().Replace("-", "") + ".png";
            var token = GetToken1(fileName, "PUT");

            if (token == null || string.IsNullOrEmpty(token.Authorization))
                return result;
            try
            {
                var url = LstBucket.GetRndItem() + fileName;
                var html = WebClientExt.GetHtml(url, Convert.ToBase64String(content), 30, new NameValueCollection
                {
                    {"Authorization",token.Authorization},
                    {"X-Cos-Security-Token",token.XCosSecurityToken},
                    { "Referer","https://cloud.tencent.com/act/pro/ciExhibition?tab=contentReview&sub=pictureReview"}
                });
                using (var client = new WebClient())
                {
                    client.OpenRead(url);
                    result = url;
                }
            }
            catch { }
            if (!string.IsNullOrEmpty(result) && isZip)
            {
                token = GetToken1(fileName, "GET");
                if (token != null && !string.IsNullOrEmpty(token.Authorization))
                    result = string.Format("{0}?{1}&x-cos-security-token={2}&imageSlim", result, token.Authorization, token.XCosSecurityToken);
            }
            return result;
        }

        public static byte[] GetZipResult(byte[] content, ref string strUrl)
        {
            strUrl = GetResult(content, true);
            return CommonMethod.GetUrlBytes(strUrl);
        }

        [Obfuscation]
        public class QQCloudRoot
        {
            [Obfuscation]
            public string Authorization { get; set; }

            [Obfuscation]
            public string XCosSecurityToken { get; set; }
        }
    }
}
