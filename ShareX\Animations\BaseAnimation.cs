﻿using System;
using System.Diagnostics;

namespace OCRTools.ScreenCaptureLib
{
    public class BaseAnimation
    {
        public virtual bool IsActive { get; protected set; }

        protected Stopwatch Timer { get; private set; } = new Stopwatch();
        protected TimeSpan TotalElapsed { get; private set; }
        protected TimeSpan Elapsed { get; private set; }

        protected TimeSpan previousElapsed;

        public virtual void Start()
        {
            IsActive = true;
            Timer.Restart();
        }

        public virtual void Stop()
        {
            Timer.Stop();
            IsActive = false;
        }

        public virtual bool Update()
        {
            if (IsActive)
            {
                TotalElapsed = Timer.Elapsed;
                Elapsed = TotalElapsed - previousElapsed;
                previousElapsed = TotalElapsed;
            }

            return IsActive;
        }
    }
}