﻿using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace OCRTools.HelpersLib
{
    [ToolStripItemDesignerAvailability(ToolStripItemDesignerAvailability.MenuStrip | ToolStripItemDesignerAvailability.ContextMenuStrip)]
    public class ToolStripLabeledComboBox : ToolStripControlHost
    {
        public LabeledComboBox Content => Control as LabeledComboBox;

        public ToolStripLabeledComboBox(string text) : base(new LabeledComboBox())
        {
            Content.Text = text;
        }
    }
}