﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class TransParentLabel : Label
    {
        private bool _isClickToView;

        private bool _isMouseMove;

        public TransParentLabel()
        {
            SetStyle(ControlStyles.SupportsTransparentBackColor |
                     ControlStyles.OptimizedDoubleBuffer |
                     ControlStyles.AllPaintingInWmPaint |
                     ControlStyles.ResizeRedraw |
                     ControlStyles.UserPaint, true);
            BackColor = Color.Transparent;
            BorderStyle = BorderStyle.None;
            AutoSize = false;
            Margin = Padding.Empty;
            Padding = Padding.Empty;
            MouseClick += TransParentTextBox_MouseClick;
        }

        private Font normalFont;
        public Font NormalFont
        {
            get
            {
                if (normalFont == null && !Equals(Text, string.Empty))
                {
                    normalFont = CommonMethod.ScaleLabel(Text, CommonMethod.BaseFont, OriSize);
                }
                return normalFont ?? Font;
            }
            set
            {
                normalFont = value;
            }
        }

        public Size OriSize { get; set; }

        public Point OrgLocation { get; set; }

        public bool IsShowText { get; set; } //= true;

        public bool IsClickToView
        {
            get => _isClickToView;
            set
            {
                _isClickToView = value;
                Invalidate();
            }
        }

        public bool IsMouseMove
        {
            get => _isMouseMove;
            set
            {
                _isMouseMove = value;
                Invalidate();
            }
        }

        public Color ContentBackColor { get; set; }

        public Point ViewTipLocation;

        protected override void OnPaint(PaintEventArgs e)
        {
            if (IsMouseMove || IsClickToView)
            {
                using (var contentBrush = new SolidBrush(Color.FromArgb(IsClickToView ? 90 : 50, Color.Red)))
                {
                    e.Graphics.FillRectangle(contentBrush, 0, 0, Width, Height);
                }
                ViewTipLocation = new Point(e.ClipRectangle.Location.X, e.ClipRectangle.Location.Y + Height);
            }
            else if (IsShowText)
            {
                using (var backgroundBrush = new SolidBrush(Color.FromArgb(210, ContentBackColor)))
                {
                    e.Graphics.FillRectangle(backgroundBrush, 0, 0, Width, Height);
                }

                using (var textBrush = new SolidBrush(Color.Black))
                {
                    var font = new Font(NormalFont.FontFamily, (float)(NormalFont.Size * (Width * 1.0d / OriSize.Width)), NormalFont.Style, NormalFont.Unit);
                    e.Graphics.DrawString(Text, font, textBrush, new RectangleF(0, 0, Width, Height));
                }
            }
            else
            {
                using (var backgroundBrush = new SolidBrush(Color.Transparent))
                {
                    e.Graphics.FillRectangle(backgroundBrush, 0, 0, Width, Height);
                }
            }

            using (var penBorder = new Pen(Color.Red, 1))
            {
                var rectBorder = new Rectangle(e.ClipRectangle.X, e.ClipRectangle.Y, e.ClipRectangle.Width - 1,
                    e.ClipRectangle.Height - 1);
                e.Graphics.DrawRectangle(penBorder, rectBorder);
            }
        }

        private void TransParentTextBox_MouseClick(object sender, MouseEventArgs e)
        {
            if (!string.IsNullOrEmpty(Text))
            {
                CommonMethod.ShowTxtToolTipContextMenu(this, ViewTipLocation);
            }
        }
    }
}