﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.WindowsRuntime</name>
  </assembly>
  <members>
    <member name="T:System.WindowsRuntimeSystemExtensions">
      <summary>Предоставляет методы расширения для преобразования между задачами и асинхронными действиями и операциями Среда выполнения Windows. </summary>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsAsyncAction(System.Threading.Tasks.Task)">
      <summary>Возвращает асинхронное действие Среда выполнения Windows, представляющее запущенную задачу. </summary>
      <returns>Экземпляр Windows.Foundation.IAsyncAction, представляющий начавшуюся задачу. </returns>
      <param name="source">Запущенная задача. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> является неначатой задачей. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsAsyncOperation``1(System.Threading.Tasks.Task{``0})">
      <summary>Возвращает асинхронную операцию Среда выполнения Windows, представляющую запущенную задачу, которая возвращает результат. </summary>
      <returns>Экземпляр Windows.Foundation.IAsyncOperation&lt;TResult&gt;, представляющий начавшуюся задачу. </returns>
      <param name="source">Запущенная задача. </param>
      <typeparam name="TResult">Тип, возвращающий результат. </typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> является неначатой задачей. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask(Windows.Foundation.IAsyncAction)">
      <summary>Возвращает задачу, представляющая асинхронное действие Среда выполнения Windows. </summary>
      <returns>Задача, представляющая асинхронное действие. </returns>
      <param name="source">Асинхронное действие. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask(Windows.Foundation.IAsyncAction,System.Threading.CancellationToken)">
      <summary>Возвращает задачу, представляющая асинхронную действие Среда выполнения Windows, которое может быть отменено. </summary>
      <returns>Задача, представляющая асинхронное действие. </returns>
      <param name="source">Асинхронное действие. </param>
      <param name="cancellationToken">Токен, который может быть использован для запроса отмены асинхронной операции. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0})">
      <summary>Возвращает задачу, представляющая асинхронное действие Среда выполнения Windows. </summary>
      <returns>Задача, представляющая асинхронное действие. </returns>
      <param name="source">Асинхронное действие. </param>
      <typeparam name="TProgress">Тип объекта, предоставляющего данные, указывающие ход выполнения. </typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0},System.IProgress{``0})">
      <summary>Возвращает задачу, представляющую асинхронное действие Среда выполнения Windows, которое сообщает о ходе процесса. </summary>
      <returns>Задача, представляющая асинхронное действие. </returns>
      <param name="source">Асинхронное действие. </param>
      <param name="progress">Объект, получающий уведомления о ходе выполнения. </param>
      <typeparam name="TProgress">Тип объекта, предоставляющего данные, указывающие ход выполнения. </typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0},System.Threading.CancellationToken)">
      <summary>Возвращает задачу, представляющая асинхронную действие Среда выполнения Windows, которое может быть отменено. </summary>
      <returns>Задача, представляющая асинхронное действие. </returns>
      <param name="source">Асинхронное действие. </param>
      <param name="cancellationToken">Токен, который может быть использован для запроса отмены асинхронной операции. </param>
      <typeparam name="TProgress">Тип объекта, предоставляющего данные, указывающие ход выполнения. </typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0},System.Threading.CancellationToken,System.IProgress{``0})">
      <summary>Возвращает задачу, представляющая асинхронную действие Среда выполнения Windows, которое сообщает о ходе процесса и может быть отменено.</summary>
      <returns>Задача, представляющая асинхронное действие. </returns>
      <param name="source">Асинхронное действие. </param>
      <param name="cancellationToken">Токен, который может быть использован для запроса отмены асинхронной операции. </param>
      <param name="progress">Объект, получающий уведомления о ходе выполнения. </param>
      <typeparam name="TProgress">Тип объекта, предоставляющего данные, указывающие ход выполнения. </typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncOperation{``0})">
      <summary>Возвращает задачу, представляющая асинхронную операцию Среда выполнения Windows, возвращающую результат. </summary>
      <returns>Задача, представляющая асинхронную операцию. </returns>
      <param name="source">Асинхронная операция. </param>
      <typeparam name="TResult">Тип объекта, возвращающего результат асинхронной операции. </typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncOperation{``0},System.Threading.CancellationToken)">
      <summary>Возвращает задачу, которая представляет асинхронную операцию Среда выполнения Windows, которая возвращает результат и может быть отменена. </summary>
      <returns>Задача, представляющая асинхронную операцию. </returns>
      <param name="source">Асинхронная операция. </param>
      <param name="cancellationToken">Токен, который может быть использован для запроса отмены асинхронной операции. </param>
      <typeparam name="TResult">Тип объекта, возвращающего результат асинхронной операции. </typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1})">
      <summary>Возвращает задачу, представляющая асинхронную операцию Среда выполнения Windows, возвращающую результат. </summary>
      <returns>Задача, представляющая асинхронную операцию. </returns>
      <param name="source">Асинхронная операция. </param>
      <typeparam name="TResult">Тип объекта, возвращающего результат асинхронной операции. </typeparam>
      <typeparam name="TProgress">Тип объекта, предоставляющего данные, указывающие ход выполнения. </typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1},System.IProgress{``1})">
      <summary>Возвращает задачу, которая представляет асинхронную операцию Среда выполнения Windows, которая возвращает результат и отчет о ходе операции. </summary>
      <returns>Задача, представляющая асинхронную операцию. </returns>
      <param name="source">Асинхронная операция. </param>
      <param name="progress">Объект, получающий уведомления о ходе выполнения. </param>
      <typeparam name="TResult">Тип объекта, возвращающего результат асинхронной операции. </typeparam>
      <typeparam name="TProgress">Тип объекта, предоставляющего данные, указывающие ход выполнения. </typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1},System.Threading.CancellationToken)">
      <summary>Возвращает задачу, которая представляет асинхронную операцию Среда выполнения Windows, которая возвращает результат и может быть отменена. </summary>
      <returns>Задача, представляющая асинхронную операцию. </returns>
      <param name="source">Асинхронная операция. </param>
      <param name="cancellationToken">Токен, который может быть использован для запроса отмены асинхронной операции. </param>
      <typeparam name="TResult">Тип объекта, возвращающего результат асинхронной операции. </typeparam>
      <typeparam name="TProgress">Тип объекта, предоставляющего данные, указывающие ход выполнения. </typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1},System.Threading.CancellationToken,System.IProgress{``1})">
      <summary>Возвращает задачу, которая представляет асинхронную операцию Среда выполнения Windows, которое возвращает результат выполнения, отчеты и может быть отменена. </summary>
      <returns>Задача, представляющая асинхронную операцию. </returns>
      <param name="source">Асинхронная операция. </param>
      <param name="cancellationToken">Токен, который может быть использован для запроса отмены асинхронной операции. </param>
      <param name="progress">Объект, получающий уведомления о ходе выполнения. </param>
      <typeparam name="TResult">Тип объекта, возвращающего результат асинхронной операции. </typeparam>
      <typeparam name="TProgress">Тип объекта, предоставляющего данные, указывающие ход выполнения. </typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter(Windows.Foundation.IAsyncAction)">
      <summary>Возвращает объект, ожидающий асинхронное действие. </summary>
      <returns>Объект, ожидающий заданное асинхронное действие. </returns>
      <param name="source">Ожидаемое асинхронное действие. </param>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter``1(Windows.Foundation.IAsyncActionWithProgress{``0})">
      <summary>Возвращает объект, который ожидает завершения асинхронного действия, которое предоставляет отчет о ходе процесса. </summary>
      <returns>Объект, ожидающий заданное асинхронное действие. </returns>
      <param name="source">Ожидаемое асинхронное действие. </param>
      <typeparam name="TProgress">Тип объекта, предоставляющего данные, указывающие ход выполнения. </typeparam>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter``1(Windows.Foundation.IAsyncOperation{``0})">
      <summary>Возвращает объект, ожидающий асинхронную операцию, которая возвращает результат.</summary>
      <returns>Объект, ожидающий указанную асинхронную операцию. </returns>
      <param name="source">Асинхронная операция для ожидания. </param>
      <typeparam name="TResult">Тип объекта, возвращающего результат асинхронной операции. </typeparam>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1})">
      <summary>Возвращает объект, который ожидает завершения асинхронной операции, которая предоставляет отчет о ходе процесса и возвращает результат. </summary>
      <returns>Объект, ожидающий указанную асинхронную операцию. </returns>
      <param name="source">Асинхронная операция для ожидания. </param>
      <typeparam name="TResult">Тип объекта, возвращающего результат асинхронной операции.</typeparam>
      <typeparam name="TProgress">Тип объекта, предоставляющего данные, указывающие ход выполнения. </typeparam>
    </member>
    <member name="T:System.IO.WindowsRuntimeStorageExtensions">
      <summary>Содержит методы расширения для интерфейсов IStorageFile и IStorageFolder в Среда выполнения Windows при разработке приложений магазина Windows.</summary>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForReadAsync(Windows.Storage.IStorageFile)">
      <summary>Извлекает поток для чтения из указанного файла.</summary>
      <returns>Задача, представляющая асинхронную операцию чтения.</returns>
      <param name="windowsRuntimeFile">Объект Среда выполнения Windows IStorageFile, из которого требуется произвести чтение.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="windowsRuntimeFile" /> имеет значение null.</exception>
      <exception cref="T:System.IO.IOException">Невозможно открыть файл или извлечь его как поток.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForReadAsync(Windows.Storage.IStorageFolder,System.String)">
      <summary>Извлекает поток для чтения из файла в указанной родительской папке.</summary>
      <returns>Задача, представляющая асинхронную операцию чтения.</returns>
      <param name="rootDirectory">Объект Среда выполнения Windows IStorageFolder, содержащий файл, из которого требуется произвести чтение.</param>
      <param name="relativePath">Путь, относительно корневой папки, к файлу, из которого выполняется чтение.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="rootDirectory" /> или <paramref name="relativePath" /> — null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="relativePath" /> является пустой строкой или содержит только пробелы.</exception>
      <exception cref="T:System.IO.IOException">Невозможно открыть файл или извлечь его как поток.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForWriteAsync(Windows.Storage.IStorageFile)">
      <summary>Извлекает поток для записи в указанный файл.</summary>
      <returns>Задача, представляющая асинхронную операцию записи.</returns>
      <param name="windowsRuntimeFile">Объект Среда выполнения Windows IStorageFile, в который требуется произвести запись.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="windowsRuntimeFile" /> имеет значение null.</exception>
      <exception cref="T:System.IO.IOException">Невозможно открыть файл или извлечь его как поток.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForWriteAsync(Windows.Storage.IStorageFolder,System.String,Windows.Storage.CreationCollisionOption)">
      <summary>Извлекает поток для записи в файл в указанной родительской папке.</summary>
      <returns>Задача, представляющая асинхронную операцию записи.</returns>
      <param name="rootDirectory">Объект Среда выполнения Windows IStorageFolder, содержащий файл, в который требуется произвести запись.</param>
      <param name="relativePath">Путь, относительно корневой папки, к файлу, в который производится запись.</param>
      <param name="creationCollisionOption">Значение перечисления Среда выполнения Windows CreationCollisionOption, которое определяет поведение, используемое, если имя создаваемого файла совпадает с именем существующего файла.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="rootDirectory" /> или <paramref name="relativePath" /> — null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="relativePath" /> является пустой строкой или содержит только пробелы.</exception>
      <exception cref="T:System.IO.IOException">Невозможно открыть файл или извлечь его как поток.</exception>
    </member>
    <member name="T:System.IO.WindowsRuntimeStreamExtensions">
      <summary>Содержит методы расширения для преобразования между потоками в Среда выполнения Windows и управляемыми потоками в Приложения .NET для Магазина Windows.</summary>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsInputStream(System.IO.Stream)">
      <summary>Преобразует управляемый поток в Приложения .NET для Магазина Windows во входной поток в Среда выполнения Windows.</summary>
      <returns>Объект Среда выполнения WindowsIInputStream, представляющий преобразованное поток.</returns>
      <param name="stream">Поток для преобразования.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="stream" /> имеет значение null.</exception>
      <exception cref="T:System.NotSupportedException">Поток не поддерживает чтение.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsOutputStream(System.IO.Stream)">
      <summary>Преобразует управляемый поток в Приложения .NET для Магазина Windows в выходной поток в Среда выполнения Windows.</summary>
      <returns>Объект Среда выполнения WindowsIOutputStream, представляющий преобразованное поток.</returns>
      <param name="stream">Поток для преобразования.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="stream" /> имеет значение null.</exception>
      <exception cref="T:System.NotSupportedException">Поток не поддерживает запись.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsRandomAccessStream(System.IO.Stream)">
      <summary>Преобразует заданный поток в поток случайного доступа.</summary>
      <returns>RandomAccessStreamСреда выполнения Windows, который представляет преобразованный поток.</returns>
      <param name="stream">Поток для преобразования.</param>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStream(Windows.Storage.Streams.IRandomAccessStream)">
      <summary>Преобразует случайный поток доступа в Среда выполнения Windows в управляемый поток в Приложения .NET для Магазина Windows.</summary>
      <returns>Преобразованный поток.</returns>
      <param name="windowsRuntimeStream">Объект Среда выполнения WindowsIRandomAccessStream для преобразования.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="windowsRuntimeStream" /> имеет значение null.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStream(Windows.Storage.Streams.IRandomAccessStream,System.Int32)">
      <summary>Преобразует поток произвольного доступа в Среда выполнения Windows в управляемый поток в Приложения .NET для Магазина Windows, используя указанный размер буфера.</summary>
      <returns>Преобразованный поток.</returns>
      <param name="windowsRuntimeStream">Объект Среда выполнения WindowsIRandomAccessStream для преобразования.</param>
      <param name="bufferSize">Размер (в байтах) буфера.Это значение не может быть отрицательным, но оно может быть равно нулю для отключения буферизацию.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="windowsRuntimeStream" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="bufferSize" /> отрицательно.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForRead(Windows.Storage.Streams.IInputStream)">
      <summary>Преобразует входной поток в Среда выполнения Windows в управляемый поток в Приложения .NET для Магазина Windows.</summary>
      <returns>Преобразованный поток.</returns>
      <param name="windowsRuntimeStream">Объект Среда выполнения WindowsIInputStream для преобразования.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="windowsRuntimeStream" /> имеет значение null.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForRead(Windows.Storage.Streams.IInputStream,System.Int32)">
      <summary>Преобразует поток входных данных в Среда выполнения Windows в управляемый поток в Приложения .NET для Магазина Windows, используя указанный размер буфера.</summary>
      <returns>Преобразованный поток.</returns>
      <param name="windowsRuntimeStream">Объект Среда выполнения WindowsIInputStream для преобразования.</param>
      <param name="bufferSize">Размер (в байтах) буфера.Это значение не может быть отрицательным, но оно может быть равно нулю для отключения буферизацию.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="windowsRuntimeStream" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="bufferSize" /> отрицательно.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForWrite(Windows.Storage.Streams.IOutputStream)">
      <summary>Преобразует выходной поток в Среда выполнения Windows в управляемый поток в Приложения .NET для Магазина Windows.</summary>
      <returns>Преобразованный поток.</returns>
      <param name="windowsRuntimeStream">Объект Среда выполнения WindowsIOutputStream для преобразования.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="windowsRuntimeStream" /> имеет значение null.</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForWrite(Windows.Storage.Streams.IOutputStream,System.Int32)">
      <summary>Преобразует поток выходных данных в Среда выполнения Windows в управляемый поток в Приложения .NET для Магазина Windows, используя указанный размер буфера.</summary>
      <returns>Преобразованный поток.</returns>
      <param name="windowsRuntimeStream">Объект Среда выполнения WindowsIOutputStream для преобразования.</param>
      <param name="bufferSize">Размер (в байтах) буфера.Это значение не может быть отрицательным, но оно может быть равно нулю для отключения буферизацию.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="windowsRuntimeStream" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="bufferSize" /> отрицательно.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo">
      <summary>Предоставляет фабричные методы построения представлений управляемых задач, совместимые с асинхронными действиями и операциями Среда выполнения Windows. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run``1(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{``0}})">
      <summary>Создает и запускает асинхронную операцию Среда выполнения Windows с помощью функции, создающей запущенную задачу, возвращающую результат.Задача может поддерживать отмену.</summary>
      <returns>Запущенный экземпляр Windows.Foundation.IAsyncOperation&lt;TResult&gt;, который представляет задачу, созданную параметром <paramref name="taskProvider" />. </returns>
      <param name="taskProvider">Делегат, представляющий функцию, которая создает и запускает задачу.Запущенная задача представляется возвращенной асинхронной операцией Среда выполнения Windows.Функции передается токен отмены, который задача может отслеживать для уведомления о запросах на отмену; этот токен можно не использовать, если задача не поддерживает отмену выполнения.</param>
      <typeparam name="TResult">Тип, возвращающий результат. </typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="taskProvider" /> имеет значение null. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" /> возвращает незапущенную задачу. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task})">
      <summary>Создает и запускает асинхронное действие Среда выполнения Windows с помощью функции, создающей запущенную задачу.Задача может поддерживать отмену.</summary>
      <returns>Запущенный экземпляр Windows.Foundation.IAsyncAction, который представляет задачу, созданную параметром <paramref name="taskProvider" />. </returns>
      <param name="taskProvider">Делегат, представляющий функцию, которая создает и запускает задачу.Запущенная задача представляется возвращенным асинхронным действием Среда выполнения Windows.Функции передается токен отмены, который задача может отслеживать для уведомления о запросах на отмену; этот токен можно не использовать, если задача не поддерживает отмену выполнения.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="taskProvider" /> имеет значение null. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" /> возвращает незапущенную задачу. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run``2(System.Func{System.Threading.CancellationToken,System.IProgress{``1},System.Threading.Tasks.Task{``0}})">
      <summary>Создает и запускает асинхронную операцию Среда выполнения Windows, которое включает обновления хода выполнения, с помощью функции, создающей запущенную задачу, возвращающую результаты.Задача может поддерживать отчеты отмены и хода выполнения.</summary>
      <returns>Запущенный экземпляр Windows.Foundation.IAsyncOperationWithProgress&lt;TResult,TProgress&gt;, который представляет задачу, созданную параметром <paramref name="taskProvider" />. </returns>
      <param name="taskProvider">Делегат, представляющий функцию, которая создает и запускает задачу.Запущенная задача представляется возвращенным асинхронным действием Среда выполнения Windows.Функции передается токен отмены, который задача может отслеживать для уведомления о запросах на отмену, и интерфейс для отчетности о ходе выполнения; любой из этих аргументов (или оба аргумента) можно не использовать, если задача не поддерживает отчетность о ходе выполнения и отмену выполнения.</param>
      <typeparam name="TResult">Тип, возвращающий результат. </typeparam>
      <typeparam name="TProgress">Тип, используемый для уведомлений о ходе выполнения. </typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="taskProvider" /> имеет значение null. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" /> возвращает незапущенную задачу. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run``1(System.Func{System.Threading.CancellationToken,System.IProgress{``0},System.Threading.Tasks.Task})">
      <summary>Создает и запускает асинхронное действие Среда выполнения Windows, которое включает обновления хода выполнения, с помощью функции, создающей запущенную задачу.Задача может поддерживать отчеты отмены и хода выполнения.</summary>
      <returns>Запущенный экземпляр Windows.Foundation.IAsyncActionWithProgress&lt;TProgress&gt;, который представляет задачу, созданную параметром <paramref name="taskProvider" />. </returns>
      <param name="taskProvider">Делегат, представляющий функцию, которая создает и запускает задачу.Запущенная задача представляется возвращенным асинхронным действием Среда выполнения Windows.Функции передается токен отмены, который задача может отслеживать для уведомления о запросах на отмену, и интерфейс для отчетности о ходе выполнения; любой из этих аргументов (или оба аргумента) можно не использовать, если задача не поддерживает отчетность о ходе выполнения и отмену выполнения.</param>
      <typeparam name="TProgress">Тип, используемый для уведомлений о ходе выполнения. </typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="taskProvider" /> имеет значение null. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" /> возвращает незапущенную задачу. </exception>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBuffer">
      <summary>Предоставляет реализацию интерфейса Среда выполнения WindowsIBuffer (Windows.Storage.Streams.IBuffer), и любые дополнительные необходимые интерфейсы. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBuffer.Create(System.Byte[],System.Int32,System.Int32,System.Int32)">
      <summary>Возвращает интерфейс Windows.Storage.Streams.IBuffer, содержащий указанный диапазон байтов, копируемых из массива байтов.Если указанная емкость превышает количество скопированных байтов, остальная часть буфера заполняются нулями.</summary>
      <returns>Интерфейс Windows.Storage.Streams.IBuffer, содержащий указанный диапазон байтов.Если <paramref name="capacity" /> больше <paramref name="length" />, остальная часть буфера заполняется нулями.</returns>
      <param name="data">Массив байтов, из которого необходимо скопировать данные. </param>
      <param name="offset">Смещение в буфере <paramref name="data" />, с которого начинается копирование. </param>
      <param name="length">Число байтов, предназначенных для копирования. </param>
      <param name="capacity">Максимальное количество байтов, которое может содержать буфер; если это значение больше значения параметра <paramref name="length" />, то остальные байты в буфере инициализируются значением 0 (ноль).</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="capacity" />, <paramref name="offset" /> или <paramref name="length" /> меньше 0 (нуля). </exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="data" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentException">Начиная с <paramref name="offset" />, <paramref name="data" /> не содержит элементов <paramref name="length" />. – или –Начиная с <paramref name="offset" />, <paramref name="data" /> не содержит элементов <paramref name="capacity" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBuffer.Create(System.Int32)">
      <summary>Возвращает пустой интерфейс Windows.Storage.Streams.IBuffer, имеющий указанную максимальную емкость. </summary>
      <returns>Интерфейс Windows.Storage.Streams.IBuffer, который имеет указанную емкость и свойство Length, равное 0 (ноль). </returns>
      <param name="capacity">Максимальное количество байтов, которое может храниться в буфере. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра<paramref name="capacity" /> меньше 0 (нуля). </exception>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions">
      <summary>Предоставляет методы расширения для работы в буферах Среда выполнения Windows (интерфейс Windows.Storage.Streams.IBuffer). </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsBuffer(System.Byte[])">
      <summary>Возвращает интерфейс Windows.Storage.Streams.IBuffer, который представляет указанный массив байтов. </summary>
      <returns>Интерфейс Windows.Storage.Streams.IBuffer, который представляет указанный массив байтов. </returns>
      <param name="source">Массив, который необходимо представить. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsBuffer(System.Byte[],System.Int32,System.Int32)">
      <summary>Возвращает интерфейс Windows.Storage.Streams.IBuffer, представляющий диапазон байтов из указанного массива байтов. </summary>
      <returns>Интерфейс IBuffer, представляющий указанный диапазон байтов в <paramref name="source" />.</returns>
      <param name="source">Массив, содержащий диапазон байтов, представленный элементом IBuffer. </param>
      <param name="offset">Смещение в объекте <paramref name="source" />, с которого начинается диапазон. </param>
      <param name="length">Длина диапазона, представленного объектом IBuffer. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="offset" /> или <paramref name="length" /> меньше 0 (нуля). </exception>
      <exception cref="T:System.ArgumentException">Массив недостаточно большой для использования в качестве резервного хранилища для IBuffer; то есть число байтов в <paramref name="source" />, начиная с <paramref name="offset" />, меньше чем <paramref name="length" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsBuffer(System.Byte[],System.Int32,System.Int32,System.Int32)">
      <summary>Возвращает интерфейс Windows.Storage.Streams.IBuffer, представляющий диапазон байтов из указанного массива байтов.При необходимости задает свойство LengthIBuffer равным значению, которое меньше емкости.</summary>
      <returns>Интерфейс IBuffer, который представляет заданный диапазон байтов в <paramref name="source" /> и в котором имеется заданное значение свойства Length. </returns>
      <param name="source">Массив, содержащий диапазон байтов, представленный элементом IBuffer. </param>
      <param name="offset">Смещение в объекте <paramref name="source" />, с которого начинается диапазон. </param>
      <param name="length">Значение свойства Length объекта IBuffer. </param>
      <param name="capacity">Размер диапазона, представленного объектом IBuffer.Для свойства Capacity объекта IBuffer задано это значение.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="offset" />, <paramref name="length" /> или <paramref name="capacity" /> меньше 0 (нуля). </exception>
      <exception cref="T:System.ArgumentException">Значение <paramref name="length" /> больше значения <paramref name="capacity" />. – или –Массив недостаточно большой для использования в качестве резервного хранилища для IBuffer; то есть число байтов в <paramref name="source" />, начиная с <paramref name="offset" />, меньше чем <paramref name="length" /> или <paramref name="capacity" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsStream(Windows.Storage.Streams.IBuffer)">
      <summary>Возвращает Поток, который представляет ту же память, которую представляет заданный интерфейс Windows.Storage.Streams.IBuffer. </summary>
      <returns>Поток, который представляет ту же память, которую представляет заданный интерфейс Windows.Storage.Streams.IBuffer. </returns>
      <param name="source">IBuffer, представляемый в виде потока. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(System.Byte[],System.Int32,Windows.Storage.Streams.IBuffer,System.UInt32,System.Int32)">
      <summary>Копирует байты из массива источника в буфер назначения (Windows.Storage.Streams.IBuffer), указывая начальный индекс в массиве источника, начальный индекс в буфере назначения, и число байтов для копирования.Метод не обновляет свойство Length буфера назначения.</summary>
      <param name="source">Массив, из которого необходимо скопировать данные. </param>
      <param name="sourceIndex">Индекс в <paramref name="source" />, из которого следует начать копирование данных. </param>
      <param name="destination">Буфер, в который копируются данные. </param>
      <param name="destinationIndex">Индекс в <paramref name="destination" />, в который следует начать копирование данных. </param>
      <param name="count">Число байтов, предназначенных для копирования. </param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="destination" /> — null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="count" />, <paramref name="sourceIndex" /> или <paramref name="destinationIndex" /> меньше 0 (нуля). </exception>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="sourceIndex" /> больше или равно длине массива <paramref name="source" />. – или –Число байтов в <paramref name="source" />, начиная с <paramref name="sourceIndex" />, меньше, чем <paramref name="count" />. – или –Копирование <paramref name="count" /> байтов, начиная на <paramref name="destinationIndex" />, превысило бы емкость <paramref name="destination" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(System.Byte[],Windows.Storage.Streams.IBuffer)">
      <summary>Копирует все байты из массива источника в буфер назначения (Windows.Storage.Streams.IBuffer), начиная с позиции 0 (ноль) в обеих.Метод не обновляет длину буфера назначения.</summary>
      <param name="source">Массив, из которого необходимо скопировать данные. </param>
      <param name="destination">Буфер, в который копируются данные. </param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="destination" /> — null. </exception>
      <exception cref="T:System.ArgumentException">Размер <paramref name="source" /> превышает емкость <paramref name="destination" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,System.Byte[])">
      <summary>Копирует все байты из буфера источника (Windows.Storage.Streams.IBuffer) в массив назначения, начиная с позиции 0 (ноль) в обеих. </summary>
      <param name="source">Буфер, из которого копируются данные. </param>
      <param name="destination">Массив, в который выполняется копирование данных. </param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="destination" /> — null. </exception>
      <exception cref="T:System.ArgumentException">Размер <paramref name="source" /> превышает размер <paramref name="destination" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,System.UInt32,System.Byte[],System.Int32,System.Int32)">
      <summary>Копирует байты из буфера источника (Windows.Storage.Streams.IBuffer) в массив назначения, указывая начальный индекс в буфере источника, начальный индекс в массиве назначения, и число байтов для копирования. </summary>
      <param name="source">Буфер, из которого копируются данные. </param>
      <param name="sourceIndex">Индекс в <paramref name="source" />, из которого следует начать копирование данных. </param>
      <param name="destination">Массив, в который выполняется копирование данных. </param>
      <param name="destinationIndex">Индекс в <paramref name="destination" />, в который следует начать копирование данных. </param>
      <param name="count">Число байтов, предназначенных для копирования. </param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="destination" /> — null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="count" />, <paramref name="sourceIndex" /> или <paramref name="destinationIndex" /> меньше 0 (нуля). </exception>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="sourceIndex" /> больше или равно емкости <paramref name="source" />. – или –Значение параметра <paramref name="destinationIndex" /> больше или равно длине массива <paramref name="destination" />. – или –Число байтов в <paramref name="source" />, начиная с <paramref name="sourceIndex" />, меньше, чем <paramref name="count" />. – или –Копирование <paramref name="count" /> байтов, начиная на <paramref name="destinationIndex" />, превысило бы размер <paramref name="destination" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,System.UInt32,Windows.Storage.Streams.IBuffer,System.UInt32,System.UInt32)">
      <summary>Копирует байты из буфера источника (Windows.Storage.Streams.IBuffer) в буфер назначения, указывая начальный индекс в источнике, начальный индекс в назначении, и число байтов для копирования.</summary>
      <param name="source">Буфер, из которого копируются данные. </param>
      <param name="sourceIndex">Индекс в <paramref name="source" />, из которого следует начать копирование данных. </param>
      <param name="destination">Буфер, в который копируются данные. </param>
      <param name="destinationIndex">Индекс в <paramref name="destination" />, в который следует начать копирование данных. </param>
      <param name="count">Число байтов, предназначенных для копирования. </param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="destination" /> — null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="count" />, <paramref name="sourceIndex" /> или <paramref name="destinationIndex" /> меньше 0 (нуля). </exception>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="sourceIndex" /> больше или равно емкости <paramref name="source" />. – или –Значение параметра <paramref name="destinationIndex" /> больше или равно емкости <paramref name="destination" />. – или –Число байтов в <paramref name="source" />, начиная с <paramref name="sourceIndex" />, меньше, чем <paramref name="count" />. – или –Копирование <paramref name="count" /> байтов, начиная на <paramref name="destinationIndex" />, превысило бы емкость <paramref name="destination" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,Windows.Storage.Streams.IBuffer)">
      <summary>Копирует все байты из буфера источника (Windows.Storage.Streams.IBuffer) в буфер назначения, начиная с позиции 0 (ноль) в обеих. </summary>
      <param name="source">Исходный буфер. </param>
      <param name="destination">Конечный буфер. </param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="destination" /> — null. </exception>
      <exception cref="T:System.ArgumentException">Размер <paramref name="source" /> превышает емкость <paramref name="destination" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.GetByte(Windows.Storage.Streams.IBuffer,System.UInt32)">
      <summary>Возвращает байт с указанным смещением в указанной позиции в заданном интерфейсе Windows.Storage.Streams.IBuffer.</summary>
      <returns>Байт с указанным смещением. </returns>
      <param name="source">Буфер для получения байта. </param>
      <param name="byteOffset">Смещение байта. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра<paramref name="byteOffset" /> меньше 0 (нуля). </exception>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="byteOffset" /> больше или равно емкости <paramref name="source" />. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.GetWindowsRuntimeBuffer(System.IO.MemoryStream)">
      <summary>Возвращает интерфейс Windows.Storage.Streams.IBuffer, который представляет ту же память, как указанного потока памяти. </summary>
      <returns>Интерфейс Windows.Storage.Streams.IBuffer, поддерживаемый той же памятью, что поддерживает указанный поток памяти.</returns>
      <param name="underlyingStream">Поток, который предоставляет резервную память для объекта IBuffer. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.GetWindowsRuntimeBuffer(System.IO.MemoryStream,System.Int32,System.Int32)">
      <summary>Возвращает интерфейс Windows.Storage.Streams.IBuffer, представляющий область в памяти, которую представляет указанный поток памяти. </summary>
      <returns>Интерфейс Windows.Storage.Streams.IBuffer, поддерживаемый областью в пределах памяти, поддерживающей указанный поток памяти. </returns>
      <param name="underlyingStream">Поток, совместно использующий память с объектом IBuffer. </param>
      <param name="positionInStream">Положение общей области памяти в <paramref name="underlyingStream" />. </param>
      <param name="length">Максимальный размер общей области памяти.Если число байтов в <paramref name="underlyingStream" />, начиная с <paramref name="positionInStream" />, меньше чем <paramref name="length" />, возвращаемый IBuffer представляет только доступные байты.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="underlyingStream" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="positionInStream" /> или <paramref name="length" /> меньше 0 (нуля). </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="positionInStream" /> за пределами <paramref name="source" />. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="underlyingStream" /> не может предоставить свой базовый буфер памяти. </exception>
      <exception cref="T:System.ObjectDisposedException">Объект <paramref name="underlyingStream" /> закрыт. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.IsSameData(Windows.Storage.Streams.IBuffer,Windows.Storage.Streams.IBuffer)">
      <summary>Возвращает значение, указывающее, представляют ли два буфера (объекты Windows.Storage.Streams.IBuffer) одну и ту же базовую область памяти. </summary>
      <returns>Значение true, если области памяти, представленные 2 буферами, имеют одну и ту же начальную точку; в противном случае — значение false. </returns>
      <param name="buffer">Первый буфер. </param>
      <param name="otherBuffer">Второй буфер. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.ToArray(Windows.Storage.Streams.IBuffer)">
      <summary>Возвращает новый массив, созданный из содержимого указанного буфера (Windows.Storage.Streams.IBuffer).Размер массива является значением свойства Length объекта IBuffer.</summary>
      <returns>Массив байтов, который содержит байты в указанном объекте IBuffer, начиная со смещения 0 (ноль), в том числе количество байтов, которое равно значению свойства Length объекта IBuffer. </returns>
      <param name="source">Буфер, содержимое которого заполняет новый массив. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.ToArray(Windows.Storage.Streams.IBuffer,System.UInt32,System.Int32)">
      <summary>Возвращает новый массив, созданный из содержимого указанного буфера (Windows.Storage.Streams.IBuffer), начиная с указанного смещения  и включая указанное число байтов. </summary>
      <returns>Массив байтов, содержащий указанный диапазон байтов. </returns>
      <param name="source">Буфер, содержимое которого заполняет новый массив. </param>
      <param name="sourceIndex">Индекс в <paramref name="source" />, из которого следует начать копирование данных. </param>
      <param name="count">Число байтов, предназначенных для копирования. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="count" /> или <paramref name="sourceIndex" /> меньше 0 (нуля). </exception>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="sourceIndex" /> больше или равно емкости <paramref name="source" />. – или –Число байтов в <paramref name="source" />, начиная с <paramref name="sourceIndex" />, меньше, чем <paramref name="count" />. </exception>
    </member>
    <member name="T:Windows.Foundation.Point">
      <summary>Представляет пару координат X и Y в двухмерном пространстве.Может также представлять логическую току для определенных использований свойствами.</summary>
    </member>
    <member name="M:Windows.Foundation.Point.#ctor(System.Double,System.Double)">
      <summary>Инициализирует структуру <see cref="T:Windows.Foundation.Point" />, содержащую заданные значения. </summary>
      <param name="x">Значение по оси X структуры <see cref="T:Windows.Foundation.Point" />. </param>
      <param name="y">Значение по оси Y структуры <see cref="T:Windows.Foundation.Point" />. </param>
    </member>
    <member name="M:Windows.Foundation.Point.Equals(System.Object)">
      <summary>Определяет, является ли указанный объект <see cref="T:Windows.Foundation.Point" /> и содержит ли он те же значения, что данный объект <see cref="T:Windows.Foundation.Point" />. </summary>
      <returns>Значение true, если <paramref name="obj" /> является <see cref="T:Windows.Foundation.Point" /> и содержит те же значения <see cref="P:Windows.Foundation.Point.X" /> и <see cref="P:Windows.Foundation.Point.Y" />, что и данный <see cref="T:Windows.Foundation.Point" />; в противном случае — значение false.</returns>
      <param name="o">Сравниваемый объект.</param>
    </member>
    <member name="M:Windows.Foundation.Point.Equals(Windows.Foundation.Point)">
      <summary>Сравнивает две структуры <see cref="T:Windows.Foundation.Point" /> на равенство.</summary>
      <returns>Значение true, если обе структуры <see cref="T:Windows.Foundation.Point" /> содержат одинаковые значения <see cref="P:Windows.Foundation.Point.X" /> и <see cref="P:Windows.Foundation.Point.Y" />; в противном случае — значение false.</returns>
      <param name="value">Точка для сравнения с данным экземпляром.</param>
    </member>
    <member name="M:Windows.Foundation.Point.GetHashCode">
      <summary>Возвращает хэш-код для этого <see cref="T:Windows.Foundation.Point" />.</summary>
      <returns>Хэш-код для данной структуры <see cref="T:Windows.Foundation.Point" />.</returns>
    </member>
    <member name="M:Windows.Foundation.Point.op_Equality(Windows.Foundation.Point,Windows.Foundation.Point)">
      <summary>Сравнивает две структуры <see cref="T:Windows.Foundation.Point" /> на равенство.</summary>
      <returns>Значение true, если значения <see cref="P:Windows.Foundation.Point.X" /> и <see cref="P:Windows.Foundation.Point.Y" /> точек <paramref name="point1" /> и <paramref name="point2" /> равны; в противном случае — значение false.</returns>
      <param name="point1">Первая сравниваемая структура <see cref="T:Windows.Foundation.Point" />.</param>
      <param name="point2">Вторая сравниваемая структура <see cref="T:Windows.Foundation.Point" />.</param>
    </member>
    <member name="M:Windows.Foundation.Point.op_Inequality(Windows.Foundation.Point,Windows.Foundation.Point)">
      <summary>Сравнивает две структуры <see cref="T:Windows.Foundation.Point" /> на предмет их неравенства.</summary>
      <returns>Значение true, если точки <paramref name="point1" /> и <paramref name="point2" /> имеют разные значения <see cref="P:Windows.Foundation.Point.X" /> или <see cref="P:Windows.Foundation.Point.Y" />; значение false, если точки <paramref name="point1" /> и <paramref name="point2" /> имеют одинаковые значения <see cref="P:Windows.Foundation.Point.X" /> и <see cref="P:Windows.Foundation.Point.Y" />.</returns>
      <param name="point1">Первая точка для сравнения.</param>
      <param name="point2">Вторая точка для сравнения.</param>
    </member>
    <member name="M:Windows.Foundation.Point.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>Описание этого члена см. в разделе <see cref="M:System.IFormattable.ToString(System.String,System.IFormatProvider)" />.</summary>
      <returns>Строка, содержащая значение текущего экземпляра в заданном формате.</returns>
      <param name="format">Строка, задающая используемый формат. – или – Значение null для использования формата по умолчанию, определенного для типа реализации IFormattable. </param>
      <param name="provider">Объект IFormatProvider, используемый для форматирования значения. – или – Значение null для получения сведений о форматировании чисел на основе текущего значения параметра языкового стандарта операционной системы. </param>
    </member>
    <member name="M:Windows.Foundation.Point.ToString">
      <summary>Создает представление <see cref="T:Windows.Foundation.Point" /> данного объекта <see cref="T:System.String" />. </summary>
      <returns>Строка <see cref="T:System.String" />, содержащая значения <see cref="P:Windows.Foundation.Point.X" /> и <see cref="P:Windows.Foundation.Point.Y" /> данной структуры <see cref="T:Windows.Foundation.Point" />.</returns>
    </member>
    <member name="M:Windows.Foundation.Point.ToString(System.IFormatProvider)">
      <summary>Создает представление <see cref="T:Windows.Foundation.Point" /> данного объекта <see cref="T:System.String" />. </summary>
      <returns>Строка <see cref="T:System.String" />, содержащая значения <see cref="P:Windows.Foundation.Point.X" /> и <see cref="P:Windows.Foundation.Point.Y" /> данной структуры <see cref="T:Windows.Foundation.Point" />.</returns>
      <param name="provider">Сведения о форматировании, связанные с языком и региональными параметрами.</param>
    </member>
    <member name="P:Windows.Foundation.Point.X">
      <summary>Получает или задает значение координаты <see cref="P:Windows.Foundation.Point.X" /> этой структуры <see cref="T:Windows.Foundation.Point" />. </summary>
      <returns>Значение координаты <see cref="P:Windows.Foundation.Point.X" /> этой структуры <see cref="T:Windows.Foundation.Point" />.Значение по умолчанию — 0.</returns>
    </member>
    <member name="P:Windows.Foundation.Point.Y">
      <summary>Получает или задает значение координаты <see cref="P:Windows.Foundation.Point.Y" /> данной структуры <see cref="T:Windows.Foundation.Point" />. </summary>
      <returns>Значение координаты <see cref="P:Windows.Foundation.Point.Y" /> этой структуры <see cref="T:Windows.Foundation.Point" />.  Значение по умолчанию — 0.</returns>
    </member>
    <member name="T:Windows.Foundation.Rect">
      <summary>Описывает ширину, высоту и точку начала координат прямоугольника. </summary>
    </member>
    <member name="M:Windows.Foundation.Rect.#ctor(System.Double,System.Double,System.Double,System.Double)">
      <summary>Инициализирует новый экземпляр структуры <see cref="T:Windows.Foundation.Rect" />, которая имеет указанные координаты по осям Х и Y, а также ширину и высоту. </summary>
      <param name="x">Координата по оси X верхнего левого угла прямоугольника.</param>
      <param name="y">Координата по оси Y верхнего левого угла прямоугольника.</param>
      <param name="width">Ширина прямоугольника.</param>
      <param name="height">Высота прямоугольника.</param>
      <exception cref="T:System.ArgumentException">Значение ширины или высоты меньше 0.</exception>
    </member>
    <member name="M:Windows.Foundation.Rect.#ctor(Windows.Foundation.Point,Windows.Foundation.Point)">
      <summary>Инициализирует структуру <see cref="T:Windows.Foundation.Rect" />, которая необходимо и достаточно велика, чтобы включать две указанных точки. </summary>
      <param name="point1">Первая точка, которую должен включать новый прямоугольник.</param>
      <param name="point2">Вторая точка, которую должен включать новый прямоугольник.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.#ctor(Windows.Foundation.Point,Windows.Foundation.Size)">
      <summary>Инициализирует структуру на основе начала координат и размера <see cref="T:Windows.Foundation.Rect" />. </summary>
      <param name="location">Начало координат нового объекта <see cref="T:Windows.Foundation.Rect" />.</param>
      <param name="size">Размер нового объекта <see cref="T:Windows.Foundation.Rect" />.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Bottom">
      <summary>Возвращает координату по оси Y нижней стороны прямоугольника. </summary>
      <returns>Координата по оси Y нижней стороны прямоугольника.Если прямоугольник пуст, значение — <see cref="F:System.Double.NegativeInfinity" />.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.Contains(Windows.Foundation.Point)">
      <summary>Указывает, включает ли прямоугольник, описанный <see cref="T:Windows.Foundation.Rect" />, указанную точку.</summary>
      <returns>Значение true, если прямоугольник, описанный <see cref="T:Windows.Foundation.Rect" />, включает указанную точку; в противном случае — значение false.</returns>
      <param name="point">Проверяемая точка.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Empty">
      <summary>Возвращает специальное значение, которое представляет прямоугольник без расположения или области. </summary>
      <returns>Пустой прямоугольник, который имеет значения свойств <see cref="P:Windows.Foundation.Rect.X" /> и <see cref="P:Windows.Foundation.Rect.Y" /> равными <see cref="F:System.Double.PositiveInfinity" /> и значения свойств <see cref="P:Windows.Foundation.Rect.Width" /> и <see cref="P:Windows.Foundation.Rect.Height" /> равными <see cref="F:System.Double.NegativeInfinity" />.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.Equals(System.Object)">
      <summary>Определение равенства указанного объекта текущему объекту <see cref="T:Windows.Foundation.Rect" />.</summary>
      <returns>Значение true, если <paramref name="o" /> является <see cref="T:Windows.Foundation.Rect" /> и имеет такие же значения x,y,width,height, как и текущий прямоугольник <see cref="T:Windows.Foundation.Rect" />; в противном случае — значение false.</returns>
      <param name="o">Объект, который требуется сравнить с текущим прямоугольником.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.Equals(Windows.Foundation.Rect)">
      <summary>Определяет, равен ли заданный объект <see cref="T:Windows.Foundation.Rect" /> текущему объекту <see cref="T:Windows.Foundation.Rect" />. </summary>
      <returns>Значение true, если указанный объект <see cref="T:Windows.Foundation.Rect" /> имеет такие же значения свойств x,y,width,height, как и текущий прямоугольник <see cref="T:Windows.Foundation.Rect" />; в противном случае — значение false.</returns>
      <param name="value">Прямоугольник, который требуется сравнить с текущим прямоугольником.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.GetHashCode">
      <summary>Создает хэш-код для прямоугольника <see cref="T:Windows.Foundation.Rect" />. </summary>
      <returns>Хэш-код для текущей структуры <see cref="T:Windows.Foundation.Rect" />.</returns>
    </member>
    <member name="P:Windows.Foundation.Rect.Height">
      <summary>Получает или задает высоту прямоугольника. </summary>
      <returns>Значение, представляющее высоту прямоугольника.Значение по умолчанию — 0.</returns>
      <exception cref="T:System.ArgumentException">Заданное значение меньше 0.</exception>
    </member>
    <member name="M:Windows.Foundation.Rect.Intersect(Windows.Foundation.Rect)">
      <summary>Находит пересечение прямоугольника, представленного текущим объектом <see cref="T:Windows.Foundation.Rect" />, и прямоугольника, представленного указанным объектом <see cref="T:Windows.Foundation.Rect" />, и сохраняет результаты как текущий объект <see cref="T:Windows.Foundation.Rect" />. </summary>
      <param name="rect">Прямоугольник, который должен пересекаться с текущим прямоугольником.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.IsEmpty">
      <summary>Возвращает значение, которое показывает является ли прямоугольник <see cref="P:Windows.Foundation.Rect.Empty" />.</summary>
      <returns>true, если прямоугольник является <see cref="P:Windows.Foundation.Rect.Empty" />. В противном случае — значение false.</returns>
    </member>
    <member name="P:Windows.Foundation.Rect.Left">
      <summary>Возвращает координату по оси X левой стороны прямоугольника. </summary>
      <returns>Координата по оси X левой стороны прямоугольника.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.op_Equality(Windows.Foundation.Rect,Windows.Foundation.Rect)">
      <summary>Сравнивает две структуры <see cref="T:Windows.Foundation.Rect" /> на равенство.</summary>
      <returns>Значение true, если структуры <see cref="T:Windows.Foundation.Rect" /> имеют одинаковые значения свойств x,y,width,height; в противном случае — значение false.</returns>
      <param name="rect1">Первый прямоугольник для сравнения.</param>
      <param name="rect2">Второй прямоугольник для сравнения.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.op_Inequality(Windows.Foundation.Rect,Windows.Foundation.Rect)">
      <summary>Сравнивает две структуры <see cref="T:Windows.Foundation.Rect" />, чтобы выявить различие.  </summary>
      <returns>Значение true, если структуры <see cref="T:Windows.Foundation.Rect" /> имеют различные значения свойств x,y,width,height; в противном случае — значение false.</returns>
      <param name="rect1">Первый прямоугольник для сравнения.</param>
      <param name="rect2">Второй прямоугольник для сравнения.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Right">
      <summary>Возвращает координату по оси X правой стороны прямоугольника.  </summary>
      <returns>Координата по оси X правой стороны прямоугольника.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>Описание этого члена см. в разделе <see cref="M:System.IFormattable.ToString(System.String,System.IFormatProvider)" />.</summary>
      <returns>Строка, содержащая значение текущего экземпляра в заданном формате.</returns>
      <param name="format">Строка, задающая используемый формат. – или – Значение null для использования формата по умолчанию, определенного для типа реализации IFormattable. </param>
      <param name="provider">Объект IFormatProvider, используемый для форматирования значения. – или – Значение null для получения сведений о форматировании чисел на основе текущего значения параметра языкового стандарта операционной системы. </param>
    </member>
    <member name="P:Windows.Foundation.Rect.Top">
      <summary>Возвращает координату по оси Y верхней стороны прямоугольника. </summary>
      <returns>Координата по оси Y верхней стороны прямоугольника.</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.ToString">
      <summary>Возвращает строковое представление структуры <see cref="T:Windows.Foundation.Rect" />. </summary>
      <returns>Строковое представление текущей структуры <see cref="T:Windows.Foundation.Rect" />.Строка имеет следующий формат: "<see cref="P:Windows.Foundation.Rect.X" />,<see cref="P:Windows.Foundation.Rect.Y" />,<see cref="P:Windows.Foundation.Rect.Width" />,<see cref="P:Windows.Foundation.Rect.Height" />".</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.ToString(System.IFormatProvider)">
      <summary>Возвращает строковое представление прямоугольника с использованием указанного поставщика формата. </summary>
      <returns>Строковое представление текущего прямоугольника, которое определяется указанным поставщиком формата.</returns>
      <param name="provider">Сведения о форматировании, связанные с языком и региональными параметрами.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.Union(Windows.Foundation.Point)">
      <summary>Расширяет прямоугольник, представленный текущим объектом <see cref="T:Windows.Foundation.Rect" /> ровно настолько, чтобы вместить указанную точку. </summary>
      <param name="point">Точка, которую необходимо включить.</param>
    </member>
    <member name="M:Windows.Foundation.Rect.Union(Windows.Foundation.Rect)">
      <summary>Расширяет прямоугольник, представленный текущим объектом <see cref="T:Windows.Foundation.Rect" /> ровно настолько, чтобы вместить указанный прямоугольник. </summary>
      <param name="rect">Прямоугольник, который необходимо включить.</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Width">
      <summary>Получает или задает ширину прямоугольника.  </summary>
      <returns>Значение, представляющее ширину прямоугольника в пикселях.Значение по умолчанию — 0.</returns>
      <exception cref="T:System.ArgumentException">Заданное значение меньше 0.</exception>
    </member>
    <member name="P:Windows.Foundation.Rect.X">
      <summary>Получает или задает координату по оси X левой стороны прямоугольника. </summary>
      <returns>Координата по оси X левой стороны прямоугольника.Это значение интерпретируется как пиксели в пределах пространства координат.</returns>
    </member>
    <member name="P:Windows.Foundation.Rect.Y">
      <summary>Получает или задает координату по оси Y верхней стороны прямоугольника. </summary>
      <returns>Координата по оси Y верхней стороны прямоугольника.Это значение интерпретируется как пиксели в пределах пространства координат.</returns>
    </member>
    <member name="T:Windows.Foundation.Size">
      <summary>Описывает ширину и высоту объекта. </summary>
    </member>
    <member name="M:Windows.Foundation.Size.#ctor(System.Double,System.Double)">
      <summary>Инициализирует новый экземпляр структуры <see cref="T:Windows.Foundation.Size" /> и присваивает ему начальные значения <paramref name="width" /> и <paramref name="height" />.</summary>
      <param name="width">Исходная ширина экземпляра объекта <see cref="T:Windows.Foundation.Size" />.</param>
      <param name="height">Исходная высота экземпляра объекта <see cref="T:Windows.Foundation.Size" />.</param>
      <exception cref="T:System.ArgumentException">Значение <paramref name="width" /> или <paramref name="height" /> меньше 0.</exception>
    </member>
    <member name="P:Windows.Foundation.Size.Empty">
      <summary>Получает значение, которое представляет статический пустой объект <see cref="T:Windows.Foundation.Size" />. </summary>
      <returns>Пустой экземпляр <see cref="T:Windows.Foundation.Size" />.</returns>
    </member>
    <member name="M:Windows.Foundation.Size.Equals(System.Object)">
      <summary>Сравнивает на равенство объект с экземпляром объекта <see cref="T:Windows.Foundation.Size" />. </summary>
      <returns>true, если размеры равны, в противном случае — false.</returns>
      <param name="o">Объект <see cref="T:System.Object" /> для сравнения.</param>
    </member>
    <member name="M:Windows.Foundation.Size.Equals(Windows.Foundation.Size)">
      <summary>Сравнивает значение с экземпляром объекта <see cref="T:Windows.Foundation.Size" /> на наличие равенства. </summary>
      <returns>true, если экземпляры <see cref="T:Windows.Foundation.Size" /> равны, в противном случае — false.</returns>
      <param name="value">Размер для сравнения с текущим экземпляром объекта <see cref="T:Windows.Foundation.Size" />.</param>
    </member>
    <member name="M:Windows.Foundation.Size.GetHashCode">
      <summary>Получает хэш-код для этого экземпляра объекта <see cref="T:Windows.Foundation.Size" />. </summary>
      <returns>Хэш-код данного экземпляра <see cref="T:Windows.Foundation.Size" />.</returns>
    </member>
    <member name="P:Windows.Foundation.Size.Height">
      <summary>Получает или задает высоту данного экземпляра объекта <see cref="T:Windows.Foundation.Size" />. </summary>
      <returns>Свойство <see cref="P:Windows.Foundation.Size.Height" /> данного экземпляра объекта <see cref="T:Windows.Foundation.Size" /> в пикселях.Значение по умолчанию — 0.Значение не может быть отрицательным.</returns>
      <exception cref="T:System.ArgumentException">Заданное значение меньше 0.</exception>
    </member>
    <member name="P:Windows.Foundation.Size.IsEmpty">
      <summary>Получает значение, которое показывает, является ли данный экземпляр объекта <see cref="T:Windows.Foundation.Size" /> свойством <see cref="P:Windows.Foundation.Size.Empty" />. </summary>
      <returns>Значение true, если данный экземпляр размера является свойством <see cref="P:Windows.Foundation.Size.Empty" />; в противном случае — значение false.</returns>
    </member>
    <member name="M:Windows.Foundation.Size.op_Equality(Windows.Foundation.Size,Windows.Foundation.Size)">
      <summary>Сравнивает на равенство два экземпляра <see cref="T:Windows.Foundation.Size" />. </summary>
      <returns>Значение true если два экземпляра <see cref="T:Windows.Foundation.Size" /> равны; в противном случае — значение false.</returns>
      <param name="size1">Первый экземпляр <see cref="T:Windows.Foundation.Size" /> для сравнения.</param>
      <param name="size2">Второй экземпляр <see cref="T:Windows.Foundation.Size" /> для сравнения.</param>
    </member>
    <member name="M:Windows.Foundation.Size.op_Inequality(Windows.Foundation.Size,Windows.Foundation.Size)">
      <summary>Сравнивает два экземпляра <see cref="T:Windows.Foundation.Size" /> на предмет их неравенства. </summary>
      <returns>Значение true если экземпляры <see cref="T:Windows.Foundation.Size" /> не равны; в противном случае — значение false.</returns>
      <param name="size1">Первый экземпляр <see cref="T:Windows.Foundation.Size" /> для сравнения.</param>
      <param name="size2">Второй экземпляр <see cref="T:Windows.Foundation.Size" /> для сравнения.</param>
    </member>
    <member name="M:Windows.Foundation.Size.ToString">
      <summary>Возвращает строковое представление данного объекта <see cref="T:Windows.Foundation.Size" />.</summary>
      <returns>Строковое представление данного объекта <see cref="T:Windows.Foundation.Size" />.</returns>
    </member>
    <member name="P:Windows.Foundation.Size.Width">
      <summary>Получает или задает ширину данного экземпляра объекта <see cref="T:Windows.Foundation.Size" />. </summary>
      <returns>Свойство <see cref="P:Windows.Foundation.Size.Width" /> данного экземпляра объекта <see cref="T:Windows.Foundation.Size" /> в пикселях.Значение по умолчанию — 0.Значение не может быть отрицательным.</returns>
      <exception cref="T:System.ArgumentException">Заданное значение меньше 0.</exception>
    </member>
    <member name="T:Windows.UI.Color">
      <summary>Описывает цвет в терминах каналов альфа, красного, зеленого и синего. </summary>
    </member>
    <member name="P:Windows.UI.Color.A">
      <summary>Получает или задает значение альфа-канала цвета sRGB. </summary>
      <returns>Значение альфа-канала цвета sRGB как значение от 0 до 255.</returns>
    </member>
    <member name="P:Windows.UI.Color.B">
      <summary>Получает или задает значение синего канала цвета sRGB. </summary>
      <returns>Значение синего канала цвета sRGB как значение от 0 до 255.</returns>
    </member>
    <member name="M:Windows.UI.Color.Equals(System.Object)">
      <summary>Проверяет, является ли указанный объект структурой <see cref="T:Windows.UI.Color" />, эквивалентной структуре текущему цвету. </summary>
      <returns>true, если указанный объект является структурой <see cref="T:Windows.UI.Color" />, идентичной текущей структуре <see cref="T:Windows.UI.Color" />; в противном случае — false.</returns>
      <param name="o">Объект для сравнения с текущей структурой <see cref="T:Windows.UI.Color" />.</param>
    </member>
    <member name="M:Windows.UI.Color.Equals(Windows.UI.Color)">
      <summary>Проверяет, идентична ли заданная структура <see cref="T:Windows.UI.Color" /> текущему цвету.</summary>
      <returns>true, если заданная структура <see cref="T:Windows.UI.Color" /> идентична текущему экземпляру <see cref="T:Windows.UI.Color" />; в противном случае — false.</returns>
      <param name="color">Структура <see cref="T:Windows.UI.Color" /> для сравнения с текущей структурой <see cref="T:Windows.UI.Color" />.</param>
    </member>
    <member name="M:Windows.UI.Color.FromArgb(System.Byte,System.Byte,System.Byte,System.Byte)">
      <summary>Создает новую структуру <see cref="T:Windows.UI.Color" /> с использованием заданного альфа-канал а и значений цветовых каналов sRGB. </summary>
      <returns>Структура <see cref="T:Windows.UI.Color" /> с заданными значениями.</returns>
      <param name="a">Значение альфа-канала, <see cref="P:Windows.UI.Color.A" />, для нового цвета.Значение должно находиться в диапазоне от 0 до 255.</param>
      <param name="r">Красный канал, <see cref="P:Windows.UI.Color.R" />, для нового цвета.Значение должно находиться в диапазоне от 0 до 255.</param>
      <param name="g">Зеленый канал, <see cref="P:Windows.UI.Color.G" />, для нового цвета.Значение должно находиться в диапазоне от 0 до 255.</param>
      <param name="b">Синий канал, <see cref="P:Windows.UI.Color.B" />, для нового цвета.Значение должно находиться в диапазоне от 0 до 255.</param>
    </member>
    <member name="P:Windows.UI.Color.G">
      <summary>Получает или задает значение зеленого канала цвета sRGB. </summary>
      <returns>Значение зеленого канала цвета sRGB как значение от 0 до 255.</returns>
    </member>
    <member name="M:Windows.UI.Color.GetHashCode">
      <summary>Получает хэш-код текущей структуры <see cref="T:Windows.UI.Color" />. </summary>
      <returns>Хэш-код для текущей структуры <see cref="T:Windows.UI.Color" />.</returns>
    </member>
    <member name="M:Windows.UI.Color.op_Equality(Windows.UI.Color,Windows.UI.Color)">
      <summary>Проверяет идентичность двух структур <see cref="T:Windows.UI.Color" />. </summary>
      <returns>Значение true, если параметры <paramref name="color1" /> и <paramref name="color2" /> полностью идентичны; в противном случае — значение false.</returns>
      <param name="color1">Первая сравниваемая структура <see cref="T:Windows.UI.Color" />.</param>
      <param name="color2">Вторая сравниваемая структура <see cref="T:Windows.UI.Color" />.</param>
    </member>
    <member name="M:Windows.UI.Color.op_Inequality(Windows.UI.Color,Windows.UI.Color)">
      <summary>Проверяет, являются ли две структуры <see cref="T:Windows.UI.Color" /> не идентичными. </summary>
      <returns>Значение true, если значения параметров <paramref name="color1" /> и <paramref name="color2" /> не равны; в противном случае — значение false.</returns>
      <param name="color1">Первая сравниваемая структура <see cref="T:Windows.UI.Color" />.</param>
      <param name="color2">Вторая сравниваемая структура <see cref="T:Windows.UI.Color" />.</param>
    </member>
    <member name="P:Windows.UI.Color.R">
      <summary>Получает или задает значение красного канала цвета sRGB. </summary>
      <returns>Значение красного канала цвета sRGB как значение от 0 до 255.</returns>
    </member>
    <member name="M:Windows.UI.Color.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>Описание этого члена см. в разделе <see cref="M:System.IFormattable.ToString(System.String,System.IFormatProvider)" />.</summary>
      <returns>Строка, содержащая значение текущего экземпляра в заданном формате.</returns>
      <param name="format">Строка, задающая используемый формат. – или – Значение null для использования формата по умолчанию, определенного для типа реализации IFormattable. </param>
      <param name="provider">Объект IFormatProvider, используемый для форматирования значения. – или – Значение null для получения сведений о форматировании чисел на основе текущего значения параметра языкового стандарта операционной системы. </param>
    </member>
    <member name="M:Windows.UI.Color.ToString">
      <summary>Создает строковое представление цвета с использованием каналов ARGB в шестнадцатеричной нотации. </summary>
      <returns>Строковое представление данного цвета.</returns>
    </member>
    <member name="M:Windows.UI.Color.ToString(System.IFormatProvider)">
      <summary>Создает строковое представление цвета с использованием каналов ARGB и заданного поставщика формата. </summary>
      <returns>Строковое представление данного цвета.</returns>
      <param name="provider">Сведения о форматировании, связанные с языком и региональными параметрами.</param>
    </member>
  </members>
</doc>