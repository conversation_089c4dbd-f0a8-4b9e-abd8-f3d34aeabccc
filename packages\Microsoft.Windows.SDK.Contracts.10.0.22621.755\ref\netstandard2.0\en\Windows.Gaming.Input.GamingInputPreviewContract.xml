﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Windows.Gaming.Input.GamingInputPreviewContract</name>
  </assembly>
  <members>
    <member name="T:Windows.Gaming.Input.GamingInputPreviewContract">
      <summary>
      </summary>
    </member>
    <member name="T:Windows.Gaming.Input.Preview.GameControllerProviderInfo">
      <summary>This API is not available to all apps. Unless your developer account is specially provisioned by Microsoft, calls to these APIs will fail at runtime.</summary>
    </member>
    <member name="M:Windows.Gaming.Input.Preview.GameControllerProviderInfo.GetParentProviderId(Windows.Gaming.Input.Custom.IGameControllerProvider)">
      <summary>This API is not available to all apps. Unless your developer account is specially provisioned by Microsoft, calls to these APIs will fail at runtime.</summary>
      <param name="provider">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:Windows.Gaming.Input.Preview.GameControllerProviderInfo.GetProviderId(Windows.Gaming.Input.Custom.IGameControllerProvider)">
      <summary>This API is not available to all apps. Unless your developer account is specially provisioned by Microsoft, calls to these APIs will fail at runtime.</summary>
      <param name="provider">
      </param>
      <returns>
      </returns>
    </member>
  </members>
</doc>