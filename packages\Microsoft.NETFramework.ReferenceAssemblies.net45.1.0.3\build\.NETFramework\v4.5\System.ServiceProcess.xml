﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ServiceProcess</name>
  </assembly>
  <members>
    <member name="T:System.ServiceProcess.PowerBroadcastStatus">
      <summary>Indicates the system's power status.</summary>
    </member>
    <member name="F:System.ServiceProcess.PowerBroadcastStatus.BatteryLow">
      <summary>Battery power is low.</summary>
    </member>
    <member name="F:System.ServiceProcess.PowerBroadcastStatus.OemEvent">
      <summary>An Advanced Power Management (APM) BIOS signaled an APM OEM event.</summary>
    </member>
    <member name="F:System.ServiceProcess.PowerBroadcastStatus.PowerStatusChange">
      <summary>A change in the power status of the computer is detected, such as a switch from battery power to A/C. The system also broadcasts this event when remaining battery power slips below the threshold specified by the user or if the battery power changes by a specified percentage.</summary>
    </member>
    <member name="F:System.ServiceProcess.PowerBroadcastStatus.QuerySuspend">
      <summary>The system has requested permission to suspend the computer. An application that grants permission should carry out preparations for the suspension before returning.</summary>
    </member>
    <member name="F:System.ServiceProcess.PowerBroadcastStatus.QuerySuspendFailed">
      <summary>The system was denied permission to suspend the computer. This status is broadcast if any application or driver denied a previous QuerySuspend status.</summary>
    </member>
    <member name="F:System.ServiceProcess.PowerBroadcastStatus.ResumeAutomatic">
      <summary>The computer has woken up automatically to handle an event.</summary>
    </member>
    <member name="F:System.ServiceProcess.PowerBroadcastStatus.ResumeCritical">
      <summary>The system has resumed operation after a critical suspension caused by a failing battery.</summary>
    </member>
    <member name="F:System.ServiceProcess.PowerBroadcastStatus.ResumeSuspend">
      <summary>The system has resumed operation after being suspended.</summary>
    </member>
    <member name="F:System.ServiceProcess.PowerBroadcastStatus.Suspend">
      <summary>The computer is about to enter a suspended state. This event is typically broadcast when all applications and installable drivers have returned true to a previous QuerySuspend state.</summary>
    </member>
    <member name="T:System.ServiceProcess.ServiceAccount">
      <summary>Specifies a service's security context, which defines its logon type.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceAccount.LocalService">
      <summary>An account that acts as a non-privileged user on the local computer, and presents anonymous credentials to any remote server.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceAccount.NetworkService">
      <summary>An account that provides extensive local privileges, and presents the computer's credentials to any remote server.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceAccount.LocalSystem">
      <summary>An account, used by the service control manager, that has extensive privileges on the local computer and acts as the computer on the network.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceAccount.User">
      <summary>An account defined by a specific user on the network. Specifying User for the <see cref="P:System.ServiceProcess.ServiceProcessInstaller.Account" /> member causes the system to prompt for a valid user name and password when the service is installed, unless you set values for both the <see cref="P:System.ServiceProcess.ServiceProcessInstaller.Username" /> and <see cref="P:System.ServiceProcess.ServiceProcessInstaller.Password" /> properties of your <see cref="T:System.ServiceProcess.ServiceProcessInstaller" /> instance.</summary>
    </member>
    <member name="T:System.ServiceProcess.ServiceBase">
      <summary>Provides a base class for a service that will exist as part of a service application. <see cref="T:System.ServiceProcess.ServiceBase" /> must be derived from when creating a new service class.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceProcess.ServiceBase" /> class.</summary>
    </member>
    <member name="P:System.ServiceProcess.ServiceBase.AutoLog">
      <summary>Indicates whether to report Start, Stop, Pause, and Continue commands in the event log.</summary>
      <returns>true to report information in the event log; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceProcess.ServiceBase.CanHandlePowerEvent">
      <summary>Gets or sets a value indicating whether the service can handle notifications of computer power status changes.</summary>
      <returns>true if the service handles the computer power status changes indicated in the <see cref="T:System.ServiceProcess.PowerBroadcastStatus" /> class, otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">This property is modified after the service was started. </exception>
    </member>
    <member name="P:System.ServiceProcess.ServiceBase.CanHandleSessionChangeEvent">
      <summary>Gets or sets a value that indicates whether the service can handle session change events received from a Terminal Server session.</summary>
      <returns>true if the service can handle Terminal Server session change events; otherwise, false. </returns>
      <exception cref="T:System.InvalidOperationException">This property is modified after the service was started.</exception>
    </member>
    <member name="P:System.ServiceProcess.ServiceBase.CanPauseAndContinue">
      <summary>Gets or sets a value indicating whether the service can be paused and resumed.</summary>
      <returns>true if the service can be paused; otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">The service has already been started. The <see cref="P:System.ServiceProcess.ServiceBase.CanPauseAndContinue" /> property cannot be changed once the service has started. </exception>
    </member>
    <member name="P:System.ServiceProcess.ServiceBase.CanShutdown">
      <summary>Gets or sets a value indicating whether the service should be notified when the system is shutting down.</summary>
      <returns>true if the service should be notified when the system is shutting down; otherwise, false.  The default is false.</returns>
      <exception cref="T:System.InvalidOperationException">The service has already been started. The <see cref="P:System.ServiceProcess.ServiceBase.CanShutdown" /> property cannot be changed once the service has started. </exception>
    </member>
    <member name="P:System.ServiceProcess.ServiceBase.CanStop">
      <summary>Gets or sets a value indicating whether the service can be stopped once it has started.</summary>
      <returns>true if the service can be stopped and the <see cref="M:System.ServiceProcess.ServiceBase.OnStop" /> method called; otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">The service has already been started. The <see cref="P:System.ServiceProcess.ServiceBase.CanStop" /> property cannot be changed once the service has started. </exception>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.Dispose(System.Boolean)">
      <summary>Disposes of the resources (other than memory) used by the <see cref="T:System.ServiceProcess.ServiceBase" />.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="P:System.ServiceProcess.ServiceBase.EventLog">
      <summary>Gets an event log you can use to write notification of service command calls, such as Start and Stop, to the Application event log.</summary>
      <returns>An <see cref="T:System.Diagnostics.EventLog" /> instance whose source is registered to the Application log.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceBase.ExitCode">
      <summary>Gets or sets the exit code for the service.</summary>
      <returns>The exit code for the service.</returns>
    </member>
    <member name="F:System.ServiceProcess.ServiceBase.MaxNameLength">
      <summary>Indicates the maximum size for a service name.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.OnContinue">
      <summary>When implemented in a derived class, <see cref="M:System.ServiceProcess.ServiceBase.OnContinue" /> runs when a Continue command is sent to the service by the Service Control Manager (SCM). Specifies actions to take when a service resumes normal functioning after being paused.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.OnCustomCommand(System.Int32)">
      <summary>When implemented in a derived class, <see cref="M:System.ServiceProcess.ServiceBase.OnCustomCommand(System.Int32)" /> executes when the Service Control Manager (SCM) passes a custom command to the service. Specifies actions to take when a command with the specified parameter value occurs.</summary>
      <param name="command">The command message sent to the service. </param>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.OnPause">
      <summary>When implemented in a derived class, executes when a Pause command is sent to the service by the Service Control Manager (SCM). Specifies actions to take when a service pauses.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.OnPowerEvent(System.ServiceProcess.PowerBroadcastStatus)">
      <summary>When implemented in a derived class, executes when the computer's power status has changed. This applies to laptop computers when they go into suspended mode, which is not the same as a system shutdown.</summary>
      <returns>When implemented in a derived class, the needs of your application determine what value to return. For example, if a QuerySuspend broadcast status is passed, you could cause your application to reject the query by returning false.</returns>
      <param name="powerStatus">A <see cref="T:System.ServiceProcess.PowerBroadcastStatus" /> that indicates a notification from the system about its power status. </param>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.OnSessionChange(System.ServiceProcess.SessionChangeDescription)">
      <summary>Executes when a change event is received from a Terminal Server session. </summary>
      <param name="changeDescription">A <see cref="T:System.ServiceProcess.SessionChangeDescription" /> structure that identifies the change type.</param>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.OnShutdown">
      <summary>When implemented in a derived class, executes when the system is shutting down. Specifies what should occur immediately prior to the system shutting down.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.OnStart(System.String[])">
      <summary>When implemented in a derived class, executes when a Start command is sent to the service by the Service Control Manager (SCM) or when the operating system starts (for a service that starts automatically). Specifies actions to take when the service starts.</summary>
      <param name="args">Data passed by the start command. </param>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.OnStop">
      <summary>When implemented in a derived class, executes when a Stop command is sent to the service by the Service Control Manager (SCM). Specifies actions to take when a service stops running.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.RequestAdditionalTime(System.Int32)">
      <summary>Requests additional time for a pending operation.</summary>
      <param name="milliseconds">The requested time in milliseconds.</param>
      <exception cref="T:System.InvalidOperationException">The service is not in a pending state.</exception>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.Run(System.ServiceProcess.ServiceBase)">
      <summary>Registers the executable for a service with the Service Control Manager (SCM).</summary>
      <param name="service">A <see cref="T:System.ServiceProcess.ServiceBase" /> which indicates a service to start. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="service" /> is null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlAppDomain" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.Run(System.ServiceProcess.ServiceBase[])">
      <summary>Registers the executable for multiple services with the Service Control Manager (SCM).</summary>
      <param name="services">An array of ServiceBase instances, which indicate services to start. </param>
      <exception cref="T:System.ArgumentException">You did not supply a service to start. The array might be null or empty. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlAppDomain" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceBase.ServiceHandle">
      <summary>Gets the service control handle for the service.</summary>
      <returns>An <see cref="T:System.IntPtr" /> structure that contains the service control handle for the service.</returns>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.ServiceMainCallback(System.Int32,System.IntPtr)">
      <summary>Registers the command handler and starts the service.</summary>
      <param name="argCount">The number of arguments in the argument array. </param>
      <param name="argPointer">An <see cref="T:System.IntPtr" /> structure that points to an array of arguments.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceBase.ServiceName">
      <summary>Gets or sets the short name used to identify the service to the system.</summary>
      <returns>The name of the service. </returns>
      <exception cref="T:System.InvalidOperationException">The service has already been started. The <see cref="P:System.ServiceProcess.ServiceBase.ServiceName" /> property cannot be changed once the service has started. </exception>
      <exception cref="T:System.ArgumentException">The specified name is a zero-length string or is longer than <see cref="F:System.ServiceProcess.ServiceBase.MaxNameLength" />, or the specified name contains forward slash or backslash characters.</exception>
    </member>
    <member name="M:System.ServiceProcess.ServiceBase.Stop">
      <summary>Stops the executing service.</summary>
    </member>
    <member name="T:System.ServiceProcess.ServiceController">
      <summary>Represents a Windows service and allows you to connect to a running or stopped service, manipulate it, or get information about it.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.ServiceController" /> class that is not associated with a specific service.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.ServiceController" /> class that is associated with an existing service on the local computer.</summary>
      <param name="name">The name that identifies the service to the system. This can also be the display name for the service.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is invalid. </exception>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.ServiceController" /> class that is associated with an existing service on the specified computer.</summary>
      <param name="name">The name that identifies the service to the system. This can also be the display name for the service.</param>
      <param name="machineName">The computer on which the service resides. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is invalid.-or- <paramref name="machineName" /> is invalid. </exception>
    </member>
    <member name="P:System.ServiceProcess.ServiceController.CanPauseAndContinue">
      <summary>Gets a value indicating whether the service can be paused and resumed.</summary>
      <returns>true if the service can be paused; otherwise, false.</returns>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.InvalidOperationException">The service was not found.</exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceController.CanShutdown">
      <summary>Gets a value indicating whether the service should be notified when the system is shutting down.</summary>
      <returns>true if the service should be notified when the system is shutting down; otherwise, false.</returns>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.InvalidOperationException">The service was not found.</exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceController.CanStop">
      <summary>Gets a value indicating whether the service can be stopped after it has started.</summary>
      <returns>true if the service can be stopped and the <see cref="M:System.ServiceProcess.ServiceBase.OnStop" /> method called; otherwise, false.</returns>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.InvalidOperationException">The service was not found.</exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.Close">
      <summary>Disconnects this <see cref="T:System.ServiceProcess.ServiceController" /> instance from the service and frees all the resources that the instance allocated.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.Continue">
      <summary>Continues a service after it has been paused.</summary>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.InvalidOperationException">The service was not found. </exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceController.DependentServices">
      <summary>Gets the set of services that depends on the service associated with this <see cref="T:System.ServiceProcess.ServiceController" /> instance.</summary>
      <returns>An array of <see cref="T:System.ServiceProcess.ServiceController" /> instances, each of which is associated with a service that depends on this service.</returns>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.InvalidOperationException">The service was not found.</exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceController.DisplayName">
      <summary>Gets or sets a friendly name for the service.</summary>
      <returns>The friendly name of the service, which can be used to identify the service.</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.ServiceProcess.ServiceController.DisplayName" /> is null. </exception>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.InvalidOperationException">The service was not found.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ServiceProcess.ServiceController" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.ExecuteCommand(System.Int32)">
      <summary>Executes a custom command on the service.</summary>
      <param name="command">An application-defined command flag that indicates which custom command to execute. The value must be between 128 and 256, inclusive.</param>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.InvalidOperationException">The service was not found. </exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.GetDevices">
      <summary>Retrieves the device driver services on the local computer.</summary>
      <returns>An array of type <see cref="T:System.ServiceProcess.ServiceController" /> in which each element is associated with a device driver service on the local computer.</returns>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.GetDevices(System.String)">
      <summary>Retrieves the device driver services on the specified computer.</summary>
      <returns>An array of type <see cref="T:System.ServiceProcess.ServiceController" /> in which each element is associated with a device driver service on the specified computer.</returns>
      <param name="machineName">The computer from which to retrieve the device driver services. </param>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="machineName" /> parameter has invalid syntax. </exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.GetServices">
      <summary>Retrieves all the services on the local computer, except for the device driver services.</summary>
      <returns>An array of type <see cref="T:System.ServiceProcess.ServiceController" /> in which each element is associated with a service on the local computer.</returns>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.GetServices(System.String)">
      <summary>Retrieves all the services on the specified computer, except for the device driver services.</summary>
      <returns>An array of type <see cref="T:System.ServiceProcess.ServiceController" /> in which each element is associated with a service on the specified computer.</returns>
      <param name="machineName">The computer from which to retrieve the services. </param>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="machineName" /> parameter has invalid syntax. </exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceController.MachineName">
      <summary>Gets or sets the name of the computer on which this service resides.</summary>
      <returns>The name of the computer that is running the service associated with this <see cref="T:System.ServiceProcess.ServiceController" /> instance. The default is the local computer (".").</returns>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.ServiceProcess.ServiceController.MachineName" /> syntax is invalid. </exception>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.Pause">
      <summary>Suspends a service's operation.</summary>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.InvalidOperationException">The service was not found. </exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.Refresh">
      <summary>Refreshes property values by resetting the properties to their current values.</summary>
    </member>
    <member name="P:System.ServiceProcess.ServiceController.ServiceHandle">
      <summary>Gets the handle for the service.</summary>
      <returns>A <see cref="T:System.Runtime.InteropServices.SafeHandle" /> that contains the handle for the service. </returns>
      <exception cref="T:System.InvalidOperationException">The service was not found.</exception>
    </member>
    <member name="P:System.ServiceProcess.ServiceController.ServiceName">
      <summary>Gets or sets the name that identifies the service that this instance references.</summary>
      <returns>The name that identifies the service that this <see cref="T:System.ServiceProcess.ServiceController" /> instance references. The default is an empty string ("").</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.ServiceProcess.ServiceController.ServiceName" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The syntax of the <see cref="P:System.ServiceProcess.ServiceController.ServiceName" /> property is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">The service was not found.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceController.ServicesDependedOn">
      <summary>The set of services that this service depends on.</summary>
      <returns>An array of <see cref="T:System.ServiceProcess.ServiceController" /> instances, each of which is associated with a service that must be running for this service to run.</returns>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.InvalidOperationException">The service was not found.</exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceController.ServiceType">
      <summary>Gets the type of service that this object references.</summary>
      <returns>One of the <see cref="T:System.ServiceProcess.ServiceType" /> values, used to indicate the network service type.</returns>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.InvalidOperationException">The service was not found.</exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.Start">
      <summary>Starts the service, passing no arguments.</summary>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.InvalidOperationException">The service was not found.</exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.Start(System.String[])">
      <summary>Starts a service, passing the specified arguments.</summary>
      <param name="args">An array of arguments to pass to the service when it starts. </param>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.InvalidOperationException">The service cannot be started. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="args" /> is null.-or-A member of the array is null.</exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceController.Status">
      <summary>Gets the status of the service that is referenced by this instance.</summary>
      <returns>One of the <see cref="T:System.ServiceProcess.ServiceControllerStatus" /> values that indicates whether the service is running, stopped, or paused, or whether a start, stop, pause, or continue command is pending.</returns>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.InvalidOperationException">The service was not found.</exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.Stop">
      <summary>Stops this service and any services that are dependent on this service.</summary>
      <exception cref="T:System.ComponentModel.Win32Exception">An error occurred when accessing a system API. </exception>
      <exception cref="T:System.InvalidOperationException">The service was not found. </exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.WaitForStatus(System.ServiceProcess.ServiceControllerStatus)">
      <summary>Infinitely waits for the service to reach the specified status.</summary>
      <param name="desiredStatus">The status to wait for. </param>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="desiredStatus" /> parameter is not any of the values defined in the <see cref="T:System.ServiceProcess.ServiceControllerStatus" /> enumeration. </exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceController.WaitForStatus(System.ServiceProcess.ServiceControllerStatus,System.TimeSpan)">
      <summary>Waits for the service to reach the specified status or for the specified time-out to expire.</summary>
      <param name="desiredStatus">The status to wait for. </param>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> object specifying the amount of time to wait for the service to reach the specified status. </param>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The <paramref name="desiredStatus" /> parameter is not any of the values defined in the <see cref="T:System.ServiceProcess.ServiceControllerStatus" /> enumeration. </exception>
      <exception cref="T:System.ServiceProcess.TimeoutException">The value specified for the <paramref name="timeout" /> parameter expires. </exception>
      <PermissionSet>
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.ServiceProcess.ServiceControllerPermission">
      <summary>Allows control of code access security permissions for service controllers.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermission.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.ServiceControllerPermission" /> class.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermission.#ctor(System.Security.Permissions.PermissionState)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.ServiceControllerPermission" /> class with the specified access to resources.</summary>
      <param name="state">One of the <see cref="T:System.Security.Permissions.PermissionState" /> values. </param>
      <exception cref="T:System.ArgumentException">The <paramref name="state" /> parameter is not a valid value of <see cref="T:System.Security.Permissions.PermissionState" />. </exception>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermission.#ctor(System.ServiceProcess.ServiceControllerPermissionAccess,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.ServiceControllerPermission" /> class with the specified permission access levels, the name of the computer on which the service resides, and the short name that identifies the service to the system.</summary>
      <param name="permissionAccess">One of the <see cref="T:System.ServiceProcess.ServiceControllerPermissionAccess" /> values. </param>
      <param name="machineName">The name of the computer on which the service resides. </param>
      <param name="serviceName">The short name that identifies the service to the system. </param>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermission.#ctor(System.ServiceProcess.ServiceControllerPermissionEntry[])">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.ServiceControllerPermission" /> class with the specified permission entries.</summary>
      <param name="permissionAccessEntries">An array of <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntry" /> objects. The <see cref="P:System.ServiceProcess.ServiceControllerPermission.PermissionEntries" /> property is set to this value. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="permissionAccessEntries " />is null.</exception>
    </member>
    <member name="P:System.ServiceProcess.ServiceControllerPermission.PermissionEntries">
      <summary>Gets the collection of permission entries for this permissions request.</summary>
      <returns>A <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntryCollection" /> that contains the permission entries for this permissions request.</returns>
    </member>
    <member name="T:System.ServiceProcess.ServiceControllerPermissionAccess">
      <summary>Defines access levels used by <see cref="T:System.ServiceProcess.ServiceController" /> permission classes.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceControllerPermissionAccess.None">
      <summary>The <see cref="T:System.ServiceProcess.ServiceController" /> has no permissions.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceControllerPermissionAccess.Browse">
      <summary>The <see cref="T:System.ServiceProcess.ServiceController" /> can connect to, but not control, existing services.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceControllerPermissionAccess.Control">
      <summary>The <see cref="T:System.ServiceProcess.ServiceController" /> can connect to and control existing services.</summary>
    </member>
    <member name="T:System.ServiceProcess.ServiceControllerPermissionAttribute">
      <summary>Allows declarative service controller permission checks.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionAttribute.#ctor(System.Security.Permissions.SecurityAction)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.ServiceControllerPermissionAttribute" /> class.</summary>
      <param name="action">One of the <see cref="T:System.Security.Permissions.SecurityAction" /> values. </param>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionAttribute.CreatePermission">
      <summary>Creates the permission based on the requested access levels that are set through the <see cref="P:System.ServiceProcess.ServiceControllerPermissionAttribute.PermissionAccess" /> property on the attribute.</summary>
      <returns>An <see cref="T:System.Security.IPermission" /> that represents the created permission.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceControllerPermissionAttribute.MachineName">
      <summary>Gets or sets the name of the computer on which the service resides.</summary>
      <returns>The name of the computer that is running the service associated with the <see cref="T:System.ServiceProcess.ServiceController" />.</returns>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.ServiceProcess.ServiceControllerPermissionAttribute.MachineName" /> syntax is invalid. </exception>
    </member>
    <member name="P:System.ServiceProcess.ServiceControllerPermissionAttribute.PermissionAccess">
      <summary>Gets or sets the access levels used in the permissions request.</summary>
      <returns>A bitwise combination of the <see cref="T:System.ServiceProcess.ServiceControllerPermissionAccess" /> values.</returns>
    </member>
    <member name="P:System.ServiceProcess.ServiceControllerPermissionAttribute.ServiceName">
      <summary>Gets or sets the short name that identifies the service to the system.</summary>
      <returns>The name of the service.</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.ServiceProcess.ServiceControllerPermissionAttribute.ServiceName" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The syntax of the <see cref="P:System.ServiceProcess.ServiceControllerPermissionAttribute.ServiceName" /> property is invalid. </exception>
    </member>
    <member name="T:System.ServiceProcess.ServiceControllerPermissionEntry">
      <summary>Defines the smallest unit of a code access security permission that is set for a <see cref="T:System.ServiceProcess.ServiceController" />.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionEntry.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntry" /> class.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionEntry.#ctor(System.ServiceProcess.ServiceControllerPermissionAccess,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntry" /> class with the specified permission access levels, the name of the computer, and a short name that identifies the service.</summary>
      <param name="permissionAccess">One of the <see cref="T:System.ServiceProcess.ServiceControllerPermissionAccess" /> values. </param>
      <param name="machineName">The name of the computer on which the service resides. </param>
      <param name="serviceName">The short name that identifies the service to the system. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serviceName" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="machineName " />is not valid.-or-<paramref name="serviceName " />is not valid.</exception>
    </member>
    <member name="P:System.ServiceProcess.ServiceControllerPermissionEntry.MachineName">
      <summary>Gets the name of the computer on which the service resides.</summary>
      <returns>The name of the computer that is running the service associated with the <see cref="T:System.ServiceProcess.ServiceController" />.</returns>
    </member>
    <member name="P:System.ServiceProcess.ServiceControllerPermissionEntry.PermissionAccess">
      <summary>Gets the access levels used in the permissions request.</summary>
      <returns>A bitwise combination of the <see cref="T:System.ServiceProcess.ServiceControllerPermissionAccess" /> values.</returns>
    </member>
    <member name="P:System.ServiceProcess.ServiceControllerPermissionEntry.ServiceName">
      <summary>Gets the short name that identifies the service to the system.</summary>
      <returns>The name of the service.</returns>
    </member>
    <member name="T:System.ServiceProcess.ServiceControllerPermissionEntryCollection">
      <summary>Contains a strongly-typed collection of <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntry" /> objects.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionEntryCollection.Add(System.ServiceProcess.ServiceControllerPermissionEntry)">
      <summary>Adds a specified <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntry" /> to this collection.</summary>
      <returns>The zero-based index of the added <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntry" /> object.</returns>
      <param name="value">The <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntry" /> object to add. </param>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionEntryCollection.AddRange(System.ServiceProcess.ServiceControllerPermissionEntry[])">
      <summary>Appends a set of specified permission entries to this collection.</summary>
      <param name="value">An array of type <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntry" /> objects that contains the permission entries to add. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null.</exception>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionEntryCollection.AddRange(System.ServiceProcess.ServiceControllerPermissionEntryCollection)">
      <summary>Appends a set of specified permission entries to this collection.</summary>
      <param name="value">A <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntryCollection" /> that contains the permission entries to add. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null.</exception>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionEntryCollection.Contains(System.ServiceProcess.ServiceControllerPermissionEntry)">
      <summary>Determines whether this collection contains a specified <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntry" /> object.</summary>
      <returns>true if the specified <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntry" /> object belongs to this collection; otherwise, false.</returns>
      <param name="value">The <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntry" /> object to find. </param>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionEntryCollection.CopyTo(System.ServiceProcess.ServiceControllerPermissionEntry[],System.Int32)">
      <summary>Copies the permission entries from this collection to an array, starting at a particular index of the array.</summary>
      <param name="array">An array of type <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntry" /> that receives the copied permission entries. </param>
      <param name="index">The zero-based index at which to begin copying the permission entries. </param>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionEntryCollection.IndexOf(System.ServiceProcess.ServiceControllerPermissionEntry)">
      <summary>Determines the index of a specified permission entry in this collection.</summary>
      <returns>The zero-based index of the specified permission entry, or -1 if the permission entry was not found in the collection.</returns>
      <param name="value">The permission entry to search for. </param>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionEntryCollection.Insert(System.Int32,System.ServiceProcess.ServiceControllerPermissionEntry)">
      <summary>Inserts a permission entry into this collection at a specified index.</summary>
      <param name="index">The zero-based index of the collection at which to insert the permission entry. </param>
      <param name="value">The permission entry to insert into this collection. </param>
    </member>
    <member name="P:System.ServiceProcess.ServiceControllerPermissionEntryCollection.Item(System.Int32)">
      <summary>Gets or sets the object at a specified index.</summary>
      <returns>The <see cref="T:System.ServiceProcess.ServiceControllerPermissionEntry" /> object that exists at the specified index.</returns>
      <param name="index">The zero-based index into the collection. </param>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionEntryCollection.OnClear">
      <summary>Performs additional custom processes after clearing the contents of the collection.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionEntryCollection.OnInsert(System.Int32,System.Object)">
      <summary>Performs additional custom processes before a new permission entry is inserted into the collection.</summary>
      <param name="index">The zero-based index at which to insert <paramref name="value" />. </param>
      <param name="value">The new value of the permission entry at <paramref name="index" />. </param>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionEntryCollection.OnRemove(System.Int32,System.Object)">
      <summary>Performs additional custom processes when removing a new permission entry from the collection.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> can be found. </param>
      <param name="value">The permission entry to remove from <paramref name="index" />. </param>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionEntryCollection.OnSet(System.Int32,System.Object,System.Object)">
      <summary>Performs additional custom processes before setting a value in the collection.</summary>
      <param name="index">The zero-based index at which <paramref name="oldValue" /> can be found. </param>
      <param name="oldValue">The value to replace with <paramref name="newValue" />. </param>
      <param name="newValue">The new value of the permission entry at <paramref name="index" />. </param>
    </member>
    <member name="M:System.ServiceProcess.ServiceControllerPermissionEntryCollection.Remove(System.ServiceProcess.ServiceControllerPermissionEntry)">
      <summary>Removes a specified permission entry from this collection.</summary>
      <param name="value">The permission entry to remove. </param>
    </member>
    <member name="T:System.ServiceProcess.ServiceControllerStatus">
      <summary>Indicates the current state of the service.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceControllerStatus.ContinuePending">
      <summary>The service continue is pending. This corresponds to the Win32 SERVICE_CONTINUE_PENDING constant, which is defined as 0x00000005.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceControllerStatus.Paused">
      <summary>The service is paused. This corresponds to the Win32 SERVICE_PAUSED constant, which is defined as 0x00000007.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceControllerStatus.PausePending">
      <summary>The service pause is pending. This corresponds to the Win32 SERVICE_PAUSE_PENDING constant, which is defined as 0x00000006.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceControllerStatus.Running">
      <summary>The service is running. This corresponds to the Win32 SERVICE_RUNNING constant, which is defined as 0x00000004.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceControllerStatus.StartPending">
      <summary>The service is starting. This corresponds to the Win32 SERVICE_START_PENDING constant, which is defined as 0x00000002.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceControllerStatus.Stopped">
      <summary>The service is not running. This corresponds to the Win32 SERVICE_STOPPED constant, which is defined as 0x00000001.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceControllerStatus.StopPending">
      <summary>The service is stopping. This corresponds to the Win32 SERVICE_STOP_PENDING constant, which is defined as 0x00000003.</summary>
    </member>
    <member name="T:System.ServiceProcess.ServiceInstaller">
      <summary>Installs a class that extends <see cref="T:System.ServiceProcess.ServiceBase" /> to implement a service. This class is called by the install utility when installing a service application.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceInstaller.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.ServiceInstaller" /> class.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceInstaller.CopyFromComponent(System.ComponentModel.IComponent)">
      <summary>Copies properties from an instance of <see cref="T:System.ServiceProcess.ServiceBase" /> to this installer.</summary>
      <param name="component">The <see cref="T:System.ComponentModel.IComponent" /> from which to copy. </param>
      <exception cref="T:System.ArgumentException">The component you are associating with this installer does not inherit from <see cref="T:System.ServiceProcess.ServiceBase" />. </exception>
    </member>
    <member name="P:System.ServiceProcess.ServiceInstaller.DelayedAutoStart">
      <summary>Gets or sets a value that indicates whether the service should be delayed from starting until other automatically started services are running.</summary>
      <returns>true to delay automatic start of the service; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.ServiceProcess.ServiceInstaller.Description">
      <summary>Gets or sets the description for the service.</summary>
      <returns>The description of the service. The default is an empty string ("").</returns>
    </member>
    <member name="P:System.ServiceProcess.ServiceInstaller.DisplayName">
      <summary>Indicates the friendly name that identifies the service to the user.</summary>
      <returns>The name associated with the service, used frequently for interactive tools.</returns>
    </member>
    <member name="M:System.ServiceProcess.ServiceInstaller.Install(System.Collections.IDictionary)">
      <summary>Installs the service by writing service application information to the registry. This method is meant to be used by installation tools, which process the appropriate methods automatically.</summary>
      <param name="stateSaver">An <see cref="T:System.Collections.IDictionary" /> that contains the context information associated with the installation. </param>
      <exception cref="T:System.InvalidOperationException">The installation does not contain a <see cref="T:System.ServiceProcess.ServiceProcessInstaller" /> for the executable.-or- The file name for the assembly is null or an empty string.-or- The service name is invalid.-or- The Service Control Manager could not be opened. </exception>
      <exception cref="T:System.ArgumentException">The display name for the service is more than 255 characters in length.</exception>
      <exception cref="T:System.ComponentModel.Win32Exception">The system could not generate a handle to the service. -or-A service with that name is already installed.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
        <IPermission class="System.Security.Permissions.UIPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceInstaller.IsEquivalentInstaller(System.Configuration.Install.ComponentInstaller)">
      <summary>Indicates whether two installers would install the same service.</summary>
      <returns>true if calling <see cref="M:System.ServiceProcess.ServiceInstaller.Install(System.Collections.IDictionary)" /> on both of these installers would result in installing the same service; otherwise, false.</returns>
      <param name="otherInstaller">A <see cref="T:System.Configuration.Install.ComponentInstaller" /> to which you are comparing the current installer. </param>
    </member>
    <member name="M:System.ServiceProcess.ServiceInstaller.Rollback(System.Collections.IDictionary)">
      <summary>Rolls back service application information written to the registry by the installation procedure. This method is meant to be used by installation tools, which process the appropriate methods automatically.</summary>
      <param name="savedState">An <see cref="T:System.Collections.IDictionary" /> that contains the context information associated with the installation. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceInstaller.ServiceName">
      <summary>Indicates the name used by the system to identify this service. This property must be identical to the <see cref="P:System.ServiceProcess.ServiceBase.ServiceName" /> of the service you want to install.</summary>
      <returns>The name of the service to be installed. This value must be set before the install utility attempts to install the service.</returns>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.ServiceProcess.ServiceInstaller.ServiceName" /> property is invalid. </exception>
    </member>
    <member name="P:System.ServiceProcess.ServiceInstaller.ServicesDependedOn">
      <summary>Indicates the services that must be running for this service to run.</summary>
      <returns>An array of services that must be running before the service associated with this installer can run.</returns>
    </member>
    <member name="P:System.ServiceProcess.ServiceInstaller.StartType">
      <summary>Indicates how and when this service is started.</summary>
      <returns>A <see cref="T:System.ServiceProcess.ServiceStartMode" /> that represents the way the service is started. The default is Manual, which specifies that the service will not automatically start after reboot.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The start mode is not a value of the <see cref="T:System.ServiceProcess.ServiceStartMode" /> enumeration.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceInstaller.Uninstall(System.Collections.IDictionary)">
      <summary>Uninstalls the service by removing information about it from the registry.</summary>
      <param name="savedState">An <see cref="T:System.Collections.IDictionary" /> that contains the context information associated with the installation. </param>
      <exception cref="T:System.ComponentModel.Win32Exception">The Service Control Manager could not be opened.-or- The system could not get a handle to the service. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
        <IPermission class="System.ServiceProcess.ServiceControllerPermission, System.ServiceProcess, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.ServiceProcess.ServiceProcessDescriptionAttribute">
      <summary>Specifies a description for a property or event.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceProcessDescriptionAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.ServiceProcessDescriptionAttribute" /> class, using the specified description.</summary>
      <param name="description">The application-defined description text. </param>
    </member>
    <member name="P:System.ServiceProcess.ServiceProcessDescriptionAttribute.Description">
      <summary>Gets description text associated with the service process.</summary>
      <returns>An application-defined description.</returns>
    </member>
    <member name="T:System.ServiceProcess.ServiceProcessInstaller">
      <summary>Installs an executable containing classes that extend <see cref="T:System.ServiceProcess.ServiceBase" />. This class is called by installation utilities, such as InstallUtil.exe, when installing a service application.</summary>
    </member>
    <member name="M:System.ServiceProcess.ServiceProcessInstaller.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.ServiceProcess.ServiceProcessInstaller" /> class. </summary>
    </member>
    <member name="P:System.ServiceProcess.ServiceProcessInstaller.Account">
      <summary>Gets or sets the type of account under which to run this service application.</summary>
      <returns>A <see cref="T:System.ServiceProcess.ServiceAccount" /> that defines the type of account under which the system runs this service. The default is User.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Security.Permissions.UIPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceProcessInstaller.CopyFromComponent(System.ComponentModel.IComponent)">
      <summary>Implements the base class <see cref="M:System.Configuration.Install.ComponentInstaller.CopyFromComponent(System.ComponentModel.IComponent)" /> method with no <see cref="T:System.ServiceProcess.ServiceProcessInstaller" /> class-specific behavior.</summary>
      <param name="comp">The <see cref="T:System.ComponentModel.IComponent" /> that represents the service process. </param>
    </member>
    <member name="P:System.ServiceProcess.ServiceProcessInstaller.HelpText">
      <summary>Gets help text displayed for service installation options.</summary>
      <returns>Help text that provides a description of the steps for setting the user name and password in order to run the service under a particular account.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceProcessInstaller.Install(System.Collections.IDictionary)">
      <summary>Writes service application information to the registry. This method is meant to be used by installation tools, which call the appropriate methods automatically.</summary>
      <param name="stateSaver">An <see cref="T:System.Collections.IDictionary" /> that contains the context information associated with the installation. </param>
      <exception cref="T:System.ArgumentException">The <paramref name="stateSaver" /> is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
        <IPermission class="System.Security.Permissions.UIPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceProcessInstaller.Password">
      <summary>Gets or sets the password associated with the user account under which the service application runs.</summary>
      <returns>The password associated with the account under which the service should run. The default is an empty string (""). The property is not public, and is never serialized.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Security.Permissions.UIPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.ServiceProcess.ServiceProcessInstaller.Rollback(System.Collections.IDictionary)">
      <summary>Rolls back service application information written to the registry by the installation procedure. This method is meant to be used by installation tools, which process the appropriate methods automatically.</summary>
      <param name="savedState">An <see cref="T:System.Collections.IDictionary" /> that contains the context information associated with the installation. </param>
      <exception cref="T:System.ArgumentException">The <paramref name="savedState" /> is null.-or- The <paramref name="savedState" /> is corrupted or non-existent. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.ServiceProcessInstaller.Username">
      <summary>Gets or sets the user account under which the service application will run.</summary>
      <returns>The account under which the service should run. The default is an empty string ("").</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Security.Permissions.UIPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.ServiceProcess.ServiceStartMode">
      <summary>Indicates the start mode of the service.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceStartMode.Manual">
      <summary>Indicates that the service is started only manually, by a user (using the Service Control Manager) or by an application.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceStartMode.Automatic">
      <summary>Indicates that the service is to be started (or was started) by the operating system, at system start-up. If an automatically started service depends on a manually started service, the manually started service is also started automatically at system startup.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceStartMode.Disabled">
      <summary>Indicates that the service is disabled, so that it cannot be started by a user or application.</summary>
    </member>
    <member name="T:System.ServiceProcess.ServiceType">
      <summary>Represents the type of the service.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceType.Adapter">
      <summary>A service for a hardware device that requires its own driver.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceType.FileSystemDriver">
      <summary>A file system driver, which is also a Kernel device driver.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceType.InteractiveProcess">
      <summary>A service that can communicate with the desktop.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceType.KernelDriver">
      <summary>A Kernel device driver such as a hard disk or other low-level hardware device driver.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceType.RecognizerDriver">
      <summary>A file system driver used during startup to determine the file systems present on the system.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceType.Win32OwnProcess">
      <summary>A Win32 program that can be started by the Service Controller and that obeys the service control protocol. This type of Win32 service runs in a process by itself.</summary>
    </member>
    <member name="F:System.ServiceProcess.ServiceType.Win32ShareProcess">
      <summary>A Win32 service that can share a process with other Win32 services.</summary>
    </member>
    <member name="T:System.ServiceProcess.SessionChangeDescription">
      <summary>Identifies the reason for a Terminal Services session change.</summary>
    </member>
    <member name="M:System.ServiceProcess.SessionChangeDescription.Equals(System.Object)">
      <summary>Determines whether the specified object is equal to the current session change description.</summary>
      <returns>true if <paramref name="obj" /> is equal to the current <see cref="T:System.ServiceProcess.SessionChangeDescription" /> structure; otherwise, false.</returns>
      <param name="obj">The object to compare to the current <see cref="T:System.ServiceProcess.SessionChangeDescription" /> structure.</param>
    </member>
    <member name="M:System.ServiceProcess.SessionChangeDescription.Equals(System.ServiceProcess.SessionChangeDescription)">
      <summary>Determines whether the specified session change description is equal to the current session change description.</summary>
      <returns>true if <paramref name="changeDescription" /> is equal to the current <see cref="T:System.ServiceProcess.SessionChangeDescription" /> structure; otherwise, false.</returns>
      <param name="changeDescription">The <see cref="T:System.ServiceProcess.SessionChangeDescription" /> structure to compare to the current <see cref="T:System.ServiceProcess.SessionChangeDescription" /> structure.</param>
    </member>
    <member name="M:System.ServiceProcess.SessionChangeDescription.GetHashCode">
      <summary>Gets a hash code for the current session change description.</summary>
      <returns>A hash code for the current session change description.</returns>
    </member>
    <member name="M:System.ServiceProcess.SessionChangeDescription.op_Equality(System.ServiceProcess.SessionChangeDescription,System.ServiceProcess.SessionChangeDescription)">
      <summary>Returns a value indicating whether two <see cref="T:System.ServiceProcess.SessionChangeDescription" /> structures are equal.</summary>
      <returns>true if <paramref name="a" /> and <paramref name="b" /> are equal; otherwise, false. </returns>
      <param name="a">A <see cref="T:System.ServiceProcess.SessionChangeDescription" /> structure.</param>
      <param name="b">A <see cref="T:System.ServiceProcess.SessionChangeDescription" /> structure.</param>
    </member>
    <member name="M:System.ServiceProcess.SessionChangeDescription.op_Inequality(System.ServiceProcess.SessionChangeDescription,System.ServiceProcess.SessionChangeDescription)">
      <summary>Returns a value indicating whether two <see cref="T:System.ServiceProcess.SessionChangeDescription" /> structures are not equal.</summary>
      <returns>true if <paramref name="a" /> and <paramref name="b" /> are not equal; otherwise, false.</returns>
      <param name="a">A <see cref="T:System.ServiceProcess.SessionChangeDescription" /> structure.</param>
      <param name="b">A <see cref="T:System.ServiceProcess.SessionChangeDescription" /> structure.</param>
    </member>
    <member name="P:System.ServiceProcess.SessionChangeDescription.Reason">
      <summary>Gets the reason for the session change.</summary>
      <returns>One of the <see cref="T:System.ServiceProcess.SessionChangeReason" /> values. </returns>
    </member>
    <member name="P:System.ServiceProcess.SessionChangeDescription.SessionId">
      <summary>Gets the session ID for the associated session.</summary>
      <returns>The session ID for the associated session.</returns>
    </member>
    <member name="T:System.ServiceProcess.SessionChangeReason">
      <summary>Specifies the reason for a Terminal Services session change notice.</summary>
    </member>
    <member name="F:System.ServiceProcess.SessionChangeReason.ConsoleConnect">
      <summary>A console session has connected.</summary>
    </member>
    <member name="F:System.ServiceProcess.SessionChangeReason.ConsoleDisconnect">
      <summary>A console session has disconnected.</summary>
    </member>
    <member name="F:System.ServiceProcess.SessionChangeReason.RemoteConnect">
      <summary>A remote session has connected.</summary>
    </member>
    <member name="F:System.ServiceProcess.SessionChangeReason.RemoteDisconnect">
      <summary>A remote session has disconnected.</summary>
    </member>
    <member name="F:System.ServiceProcess.SessionChangeReason.SessionLogon">
      <summary>A user has logged on to a session.</summary>
    </member>
    <member name="F:System.ServiceProcess.SessionChangeReason.SessionLogoff">
      <summary>A user has logged off from a session.</summary>
    </member>
    <member name="F:System.ServiceProcess.SessionChangeReason.SessionLock">
      <summary>A session has been locked.</summary>
    </member>
    <member name="F:System.ServiceProcess.SessionChangeReason.SessionUnlock">
      <summary>A session has been unlocked.</summary>
    </member>
    <member name="F:System.ServiceProcess.SessionChangeReason.SessionRemoteControl">
      <summary>The remote control status of a session has changed.</summary>
    </member>
    <member name="T:System.ServiceProcess.TimeoutException">
      <summary>The exception that is thrown when a specified timeout has expired.</summary>
    </member>
    <member name="M:System.ServiceProcess.TimeoutException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.TimeoutException" /> class with no message text.</summary>
    </member>
    <member name="M:System.ServiceProcess.TimeoutException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instace of the <see cref="T:System.ServiceProcess.TimeoutException" /> class with the specified serialization information and context.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="info" /> parameter is null. </exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">The class name is null or <see cref="P:System.Exception.HResult" /> is zero (0). </exception>
    </member>
    <member name="M:System.ServiceProcess.TimeoutException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.TimeoutException" /> class with the specified message text.</summary>
      <param name="message">Text that describes the type or source of the exception.</param>
    </member>
    <member name="M:System.ServiceProcess.TimeoutException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceProcess.TimeoutException" /> class with the specified message text and inner exception.</summary>
      <param name="message">Text that describes the type or source of the exception.</param>
      <param name="innerException">The exception that caused the current exception. </param>
    </member>
    <member name="T:System.ServiceProcess.Design.ServiceInstallerDialog">
      <summary>Provides a dialog box, which prompts for account information of a Windows Service application.</summary>
    </member>
    <member name="M:System.ServiceProcess.Design.ServiceInstallerDialog.#ctor">
      <summary>Initializes a new instance of the service account form.</summary>
    </member>
    <member name="M:System.ServiceProcess.Design.ServiceInstallerDialog.Main">
      <summary>Begins running a standard application message loop and displays the service account form.</summary>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.UIPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.Design.ServiceInstallerDialog.Password">
      <summary>Gets or sets the password for the service account form.</summary>
      <returns>A string representing the password in the service account form. The default is an empty string ("").</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.ServiceProcess.Design.ServiceInstallerDialog.Result">
      <summary>Gets the dialog result for the service account form.</summary>
      <returns>A <see cref="T:System.ServiceProcess.Design.ServiceInstallerDialogResult" /> indicating the user response to the dialog box. The default is OK.</returns>
    </member>
    <member name="P:System.ServiceProcess.Design.ServiceInstallerDialog.Username">
      <summary>Gets or sets the user name for the service account form.</summary>
      <returns>A string representing the user name in the service account form. The default is an empty string ("").</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.ServiceProcess.Design.ServiceInstallerDialogResult">
      <summary>Specifies the return value of a <see cref="T:System.ServiceProcess.Design.ServiceInstallerDialog" /> form.</summary>
    </member>
    <member name="F:System.ServiceProcess.Design.ServiceInstallerDialogResult.OK">
      <summary>The dialog return value is OK. This value typically indicates that the user confirmed the account properties and pressed the OK button to close the dialog.</summary>
    </member>
    <member name="F:System.ServiceProcess.Design.ServiceInstallerDialogResult.UseSystem">
      <summary>Install the service with a system account rather than a user account. This value typically indicates that the dialog was not displayed to the user. For example, the <see cref="P:System.ServiceProcess.ServiceProcessInstaller.Account" /> property is set to something other than User.</summary>
    </member>
    <member name="F:System.ServiceProcess.Design.ServiceInstallerDialogResult.Canceled">
      <summary>The dialog return value is Canceled. This value typically indicates that the user canceled out of the dialog without setting the account fields.</summary>
    </member>
  </members>
</doc>