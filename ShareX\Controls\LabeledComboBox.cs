﻿using System;
using System.Windows.Forms;

namespace OCRTools.HelpersLib
{
    public partial class LabeledComboBox : UserControl
    {
        public new string Text
        {
            get
            {
                return lblText.Text;
            }
            set
            {
                lblText.Text = value;
            }
        }

        public int SelectedIndex
        {
            get
            {
                return cbList.SelectedIndex;
            }
            set
            {
                cbList.SelectedIndex = value;
            }
        }

        public event EventHandler SelectedIndexChanged
        {
            add { cbList.SelectedIndexChanged += value; }
            remove { cbList.SelectedIndexChanged -= value; }
        }

        public LabeledComboBox()
        {
            InitializeComponent();
            Margin = CommonString.PaddingZero;
        }

        public void Add(object item)
        {
            cbList.Items.Add(item);
        }

        public void AddRange(string[] items)
        {
            cbList.Items.AddRange(items);
            cbList.AutoSizeDropDown();
            cbList.Width = Math.Max(cbList.Width, cbList.DropDownWidth);
        }
    }
}