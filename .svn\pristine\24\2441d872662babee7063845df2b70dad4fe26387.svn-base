﻿using MetroFramework.Components;
using MetroFramework.Design;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using MetroFramework.Native;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Security;
using System.Windows.Forms;
using System.Windows.Forms.Design;

/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

namespace MetroFramework.Controls
{
    public enum MetroScrollOrientation
    {
        Horizontal,
        Vertical
    }

    [DefaultEvent("Scroll")]
    [DefaultProperty("Value")]
    [Designer(typeof(MetroScrollBarDesigner), typeof(ParentControlDesigner))]
    public class MetroScrollBar : Control, IMetroControl
    {
        public delegate void ScrollValueChangedDelegate(object sender, int newValue);

        private readonly Timer progressTimer = new Timer();

        private Timer autoHoverTimer;

        private bool bottomBarClicked;

        private Rectangle clickedBarRectangle;

        private int curValue;

        private bool dontUpdateColor;

        private bool inUpdate;

        private bool isFirstScrollEventHorizontal = true;

        private bool isFirstScrollEventVertical = true;

        private bool isHovered;

        private bool isPressed;

        private int largeChange = 10;

        private int maximum = 100;

        private MetroScrollOrientation metroOrientation = MetroScrollOrientation.Vertical;

        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        private int minimum;

        private int mouseWheelBarPartitions = 10;

        private ScrollOrientation scrollOrientation = ScrollOrientation.VerticalScroll;

        private int smallChange = 1;

        private int thumbBottomLimitBottom;

        private int thumbBottomLimitTop;

        private bool thumbClicked;

        private int thumbHeight;

        private int thumbPosition;

        private Rectangle thumbRectangle;

        private int thumbTopLimit;

        private int thumbWidth = 6;

        private bool topBarClicked;

        private int trackPosition;

        public MetroScrollBar()
        {
            SetStyle(
                ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.Selectable |
                ControlStyles.SupportsTransparentBackColor | ControlStyles.OptimizedDoubleBuffer, true);
            Width = 10;
            Height = 200;
            SetupScrollBar();
            progressTimer.Interval = 20;
            progressTimer.Tick += ProgressTimerTick;
        }

        public MetroScrollBar(MetroScrollOrientation orientation)
            : this()
        {
            Orientation = orientation;
        }

        public MetroScrollBar(MetroScrollOrientation orientation, int width)
            : this(orientation)
        {
            Width = width;
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor { get; set; }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public bool UseCustomForeColor { get; set; }

        [Category("Metro Appearance")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [DefaultValue(false)]
        [Browsable(false)]
        public bool UseStyleColors { get; set; }

        [Browsable(false)]
        [DefaultValue(false)]
        [Category("Metro Behaviour")]
        public bool UseSelectable
        {
            get => GetStyle(ControlStyles.Selectable);
            set => SetStyle(ControlStyles.Selectable, value);
        }

        public int MouseWheelBarPartitions
        {
            get => mouseWheelBarPartitions;
            set
            {
                if (value > 0)
                {
                    mouseWheelBarPartitions = value;
                    return;
                }

                throw new ArgumentOutOfRangeException("value", "MouseWheelBarPartitions has to be greather than zero");
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseBarColor { get; set; }

        [Category("Metro Appearance")]
        public int ScrollbarSize
        {
            get
            {
                if (Orientation != MetroScrollOrientation.Vertical) return Height;
                return Width;
            }
            set
            {
                if (Orientation == MetroScrollOrientation.Vertical)
                    Width = value;
                else
                    Height = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool HighlightOnWheel { get; set; }

        public MetroScrollOrientation Orientation
        {
            get => metroOrientation;
            set
            {
                if (value != metroOrientation)
                {
                    metroOrientation = value;
                    if (value == MetroScrollOrientation.Vertical)
                        scrollOrientation = ScrollOrientation.VerticalScroll;
                    else
                        scrollOrientation = ScrollOrientation.HorizontalScroll;
                    Size = new Size(Height, Width);
                    SetupScrollBar();
                }
            }
        }

        public int Minimum
        {
            get => minimum;
            set
            {
                if (minimum != value && value >= 0 && value < maximum)
                {
                    minimum = value;
                    if (curValue < value) curValue = value;
                    if (largeChange > maximum - minimum) largeChange = maximum - minimum;
                    SetupScrollBar();
                    if (curValue < value)
                    {
                        dontUpdateColor = true;
                        Value = value;
                    }
                    else
                    {
                        ChangeThumbPosition(GetThumbPosition());
                        Refresh();
                    }
                }
            }
        }

        public int Maximum
        {
            get => maximum;
            set
            {
                if (value != maximum && value >= 1 && value > minimum)
                {
                    maximum = value;
                    if (largeChange > maximum - minimum) largeChange = maximum - minimum;
                    SetupScrollBar();
                    if (curValue > value)
                    {
                        dontUpdateColor = true;
                        Value = maximum;
                    }
                    else
                    {
                        ChangeThumbPosition(GetThumbPosition());
                        Refresh();
                    }
                }
            }
        }

        [DefaultValue(1)]
        public int SmallChange
        {
            get => smallChange;
            set
            {
                if (value != smallChange && value >= 1 && value < largeChange)
                {
                    smallChange = value;
                    SetupScrollBar();
                }
            }
        }

        [DefaultValue(5)]
        public int LargeChange
        {
            get => largeChange;
            set
            {
                if (value != largeChange && value >= smallChange && value >= 2)
                {
                    if (value > maximum - minimum)
                        largeChange = maximum - minimum;
                    else
                        largeChange = value;
                    SetupScrollBar();
                }
            }
        }

        [Browsable(false)]
        [DefaultValue(0)]
        public int Value
        {
            get => curValue;
            set
            {
                if (curValue == value || value < minimum || value > maximum) return;
                curValue = value;
                ChangeThumbPosition(GetThumbPosition());
                OnScroll(ScrollEventType.ThumbPosition, -1, value, scrollOrientation);
                if (!dontUpdateColor && HighlightOnWheel)
                {
                    if (!isHovered) isHovered = true;
                    if (autoHoverTimer == null)
                    {
                        autoHoverTimer = new Timer
                        {
                            Interval = 1000
                        };
                        autoHoverTimer.Tick += autoHoverTimer_Tick;
                        autoHoverTimer.Start();
                    }
                    else
                    {
                        autoHoverTimer.Stop();
                        autoHoverTimer.Start();
                    }
                }
                else
                {
                    dontUpdateColor = false;
                }

                Refresh();
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroColorStyle.Blue)]
        public MetroColorStyle Style
        {
            get
            {
                if (DesignMode || metroStyle != 0) return metroStyle;
                if (StyleManager != null) return StyleManager.Style;
                if (StyleManager == null) return MetroColorStyle.Blue;
                return metroStyle;
            }
            set => metroStyle = value;
        }

        [DefaultValue(MetroThemeStyle.Light)]
        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (DesignMode || metroTheme != 0) return metroTheme;
                if (StyleManager != null) return StyleManager.Theme;
                if (StyleManager == null) return MetroThemeStyle.Light;
                return metroTheme;
            }
            set => metroTheme = value;
        }

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public MetroStyleManager StyleManager { get; set; }

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        public event ScrollEventHandler Scroll;

        public event ScrollValueChangedDelegate ValueChanged;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintBackground != null) CustomPaintBackground(this, e);
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaint != null) CustomPaint(this, e);
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintForeground != null) CustomPaintForeground(this, e);
        }

        private void OnScroll(ScrollEventType type, int oldValue, int newValue, ScrollOrientation orientation)
        {
            if (oldValue != newValue && ValueChanged != null) ValueChanged(this, curValue);
            if (Scroll == null) return;
            if (orientation == ScrollOrientation.HorizontalScroll)
            {
                if (type != ScrollEventType.EndScroll && isFirstScrollEventHorizontal)
                    type = ScrollEventType.First;
                else if (!isFirstScrollEventHorizontal && type == ScrollEventType.EndScroll)
                    isFirstScrollEventHorizontal = true;
            }
            else if (type != ScrollEventType.EndScroll && isFirstScrollEventVertical)
            {
                type = ScrollEventType.First;
            }
            else if (!isFirstScrollEventHorizontal && type == ScrollEventType.EndScroll)
            {
                isFirstScrollEventVertical = true;
            }

            Scroll(this, new ScrollEventArgs(type, oldValue, newValue, orientation));
        }

        private void autoHoverTimer_Tick(object sender, EventArgs e)
        {
            isHovered = false;
            Invalidate();
            autoHoverTimer.Stop();
        }

        public bool HitTest(Point point)
        {
            return thumbRectangle.Contains(point);
        }

        [SecuritySafeCritical]
        public void BeginUpdate()
        {
            WinApi.SendMessage(Handle, 11, false, 0);
            inUpdate = true;
        }

        [SecuritySafeCritical]
        public void EndUpdate()
        {
            WinApi.SendMessage(Handle, 11, true, 0);
            inUpdate = false;
            SetupScrollBar();
            Refresh();
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                var color = BackColor;
                if (!UseCustomBackColor)
                    color = Parent == null ? MetroPaint.BackColor.Form(Theme) :
                        !(Parent is IMetroControl) ? Parent.BackColor : MetroPaint.BackColor.Form(Theme);
                if (color.A == byte.MaxValue)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint)) OnPaintBackground(e);
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            var backColor = UseCustomBackColor ? BackColor :
                Parent == null ? MetroPaint.BackColor.Form(Theme) :
                !(Parent is IMetroControl) ? Parent.BackColor : MetroPaint.BackColor.Form(Theme);
            Color color;
            Color barColor;
            if (isHovered && !isPressed && Enabled)
            {
                color = MetroPaint.BackColor.ScrollBar.Thumb.Hover(Theme);
                barColor = MetroPaint.BackColor.ScrollBar.Bar.Hover(Theme);
            }
            else if (isHovered && isPressed && Enabled)
            {
                color = MetroPaint.BackColor.ScrollBar.Thumb.Press(Theme);
                barColor = MetroPaint.BackColor.ScrollBar.Bar.Press(Theme);
            }
            else if (!Enabled)
            {
                color = MetroPaint.BackColor.ScrollBar.Thumb.Disabled(Theme);
                barColor = MetroPaint.BackColor.ScrollBar.Bar.Disabled(Theme);
            }
            else
            {
                color = MetroPaint.BackColor.ScrollBar.Thumb.Normal(Theme);
                barColor = MetroPaint.BackColor.ScrollBar.Bar.Normal(Theme);
            }

            DrawScrollBar(e.Graphics, backColor, color, barColor);
            OnCustomPaintForeground(new MetroPaintEventArgs(backColor, color, e.Graphics));
        }

        private void DrawScrollBar(Graphics g, Color backColor, Color thumbColor, Color barColor)
        {
            if (UseBarColor)
                using (var brush = new SolidBrush(barColor))
                {
                    g.FillRectangle(brush, ClientRectangle);
                }

            using (var brush2 = new SolidBrush(backColor))
            {
                var rect = new Rectangle(thumbRectangle.X - 1, thumbRectangle.Y - 1, thumbRectangle.Width + 2,
                    thumbRectangle.Height + 2);
                g.FillRectangle(brush2, rect);
            }

            using (var brush3 = new SolidBrush(thumbColor))
            {
                g.FillRectangle(brush3, thumbRectangle);
            }
        }

        protected override void OnGotFocus(EventArgs e)
        {
            Invalidate();
            base.OnGotFocus(e);
        }

        protected override void OnLostFocus(EventArgs e)
        {
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnLostFocus(e);
        }

        protected override void OnEnter(EventArgs e)
        {
            Invalidate();
            base.OnEnter(e);
        }

        protected override void OnLeave(EventArgs e)
        {
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnLeave(e);
        }

        protected override void OnMouseWheel(MouseEventArgs e)
        {
            base.OnMouseWheel(e);
            var num = e.Delta / 120 * (maximum - minimum) / mouseWheelBarPartitions;
            if (Orientation == MetroScrollOrientation.Vertical)
                Value -= num;
            else
                Value += num;
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                isPressed = true;
                Invalidate();
            }

            base.OnMouseDown(e);
            Focus();
            if (e.Button == MouseButtons.Left)
            {
                var location = e.Location;
                if (thumbRectangle.Contains(location))
                {
                    thumbClicked = true;
                    thumbPosition = metroOrientation == MetroScrollOrientation.Vertical
                        ? location.Y - thumbRectangle.Y
                        : location.X - thumbRectangle.X;
                    Invalidate(thumbRectangle);
                    return;
                }

                trackPosition = metroOrientation == MetroScrollOrientation.Vertical ? location.Y : location.X;
                if (trackPosition < (metroOrientation == MetroScrollOrientation.Vertical
                    ? thumbRectangle.Y
                    : thumbRectangle.X))
                    topBarClicked = true;
                else
                    bottomBarClicked = true;
                ProgressThumb(true);
            }
            else if (e.Button == MouseButtons.Right)
            {
                trackPosition = metroOrientation == MetroScrollOrientation.Vertical ? e.Y : e.X;
            }
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            isPressed = false;
            base.OnMouseUp(e);
            if (e.Button == MouseButtons.Left)
            {
                if (thumbClicked)
                {
                    thumbClicked = false;
                    OnScroll(ScrollEventType.EndScroll, -1, curValue, scrollOrientation);
                }
                else if (topBarClicked)
                {
                    topBarClicked = false;
                    StopTimer();
                }
                else if (bottomBarClicked)
                {
                    bottomBarClicked = false;
                    StopTimer();
                }

                Invalidate();
            }
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            isHovered = false;
            Invalidate();
            base.OnMouseLeave(e);
            ResetScrollStatus();
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            base.OnMouseMove(e);
            if (e.Button == MouseButtons.Left)
            {
                if (!thumbClicked) return;
                var num = curValue;
                var num2 = metroOrientation == MetroScrollOrientation.Vertical ? e.Location.Y : e.Location.X;
                var num3 = metroOrientation == MetroScrollOrientation.Vertical
                    ? num2 / Height / thumbHeight
                    : num2 / Width / thumbWidth;
                if (num2 <= thumbTopLimit + thumbPosition)
                {
                    ChangeThumbPosition(thumbTopLimit);
                    curValue = minimum;
                    Invalidate();
                }
                else if (num2 >= thumbBottomLimitTop + thumbPosition)
                {
                    ChangeThumbPosition(thumbBottomLimitTop);
                    curValue = maximum;
                    Invalidate();
                }
                else
                {
                    ChangeThumbPosition(num2 - thumbPosition);
                    int num4;
                    int num5;
                    if (Orientation == MetroScrollOrientation.Vertical)
                    {
                        num4 = Height - num3;
                        num5 = thumbRectangle.Y;
                    }
                    else
                    {
                        num4 = Width - num3;
                        num5 = thumbRectangle.X;
                    }

                    var num6 = 0f;
                    if (num4 != 0) num6 = num5 / (float) num4;
                    curValue = Convert.ToInt32(num6 * (maximum - minimum) + minimum);
                }

                if (num != curValue)
                {
                    OnScroll(ScrollEventType.ThumbTrack, num, curValue, scrollOrientation);
                    Refresh();
                }
            }
            else if (!ClientRectangle.Contains(e.Location))
            {
                ResetScrollStatus();
            }
            else if (e.Button == MouseButtons.None)
            {
                if (thumbRectangle.Contains(e.Location))
                    Invalidate(thumbRectangle);
                else if (ClientRectangle.Contains(e.Location)) Invalidate();
            }
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            isHovered = true;
            isPressed = true;
            Invalidate();
            base.OnKeyDown(e);
        }

        protected override void OnKeyUp(KeyEventArgs e)
        {
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnKeyUp(e);
        }

        protected override void SetBoundsCore(int x, int y, int width, int height, BoundsSpecified specified)
        {
            base.SetBoundsCore(x, y, width, height, specified);
            if (DesignMode) SetupScrollBar();
        }

        protected override void OnSizeChanged(EventArgs e)
        {
            base.OnSizeChanged(e);
            SetupScrollBar();
        }

        protected override bool ProcessDialogKey(Keys keyData)
        {
            var keys = Keys.Up;
            var keys2 = Keys.Down;
            if (Orientation == MetroScrollOrientation.Horizontal)
            {
                keys = Keys.Left;
                keys2 = Keys.Right;
            }

            if (keyData == keys)
            {
                Value -= smallChange;
                return true;
            }

            if (keyData == keys2)
            {
                Value += smallChange;
                return true;
            }

            switch (keyData)
            {
                case Keys.Prior:
                    Value = GetValue(false, true);
                    return true;
                case Keys.Next:
                    if (curValue + largeChange > maximum)
                        Value = maximum;
                    else
                        Value += largeChange;
                    return true;
                case Keys.Home:
                    Value = minimum;
                    return true;
                case Keys.End:
                    Value = maximum;
                    return true;
                default:
                    return base.ProcessDialogKey(keyData);
            }
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }

        private void SetupScrollBar()
        {
            if (!inUpdate)
            {
                if (Orientation == MetroScrollOrientation.Vertical)
                {
                    thumbWidth = Width > 0 ? Width : 10;
                    thumbHeight = GetThumbSize();
                    clickedBarRectangle = ClientRectangle;
                    clickedBarRectangle.Inflate(-1, -1);
                    thumbRectangle = new Rectangle(ClientRectangle.X, ClientRectangle.Y, thumbWidth, thumbHeight);
                    thumbPosition = thumbRectangle.Height / 2;
                    thumbBottomLimitBottom = ClientRectangle.Bottom;
                    thumbBottomLimitTop = thumbBottomLimitBottom - thumbRectangle.Height;
                    thumbTopLimit = ClientRectangle.Y;
                }
                else
                {
                    thumbHeight = Height > 0 ? Height : 10;
                    thumbWidth = GetThumbSize();
                    clickedBarRectangle = ClientRectangle;
                    clickedBarRectangle.Inflate(-1, -1);
                    thumbRectangle = new Rectangle(ClientRectangle.X, ClientRectangle.Y, thumbWidth, thumbHeight);
                    thumbPosition = thumbRectangle.Width / 2;
                    thumbBottomLimitBottom = ClientRectangle.Right;
                    thumbBottomLimitTop = thumbBottomLimitBottom - thumbRectangle.Width;
                    thumbTopLimit = ClientRectangle.X;
                }

                ChangeThumbPosition(GetThumbPosition());
                Refresh();
            }
        }

        private void ResetScrollStatus()
        {
            bottomBarClicked = topBarClicked = false;
            StopTimer();
            Refresh();
        }

        private void ProgressTimerTick(object sender, EventArgs e)
        {
            ProgressThumb(true);
        }

        private int GetValue(bool smallIncrement, bool up)
        {
            int num;
            if (up)
            {
                num = curValue - (smallIncrement ? smallChange : largeChange);
                if (num < minimum) num = minimum;
            }
            else
            {
                num = curValue + (smallIncrement ? smallChange : largeChange);
                if (num > maximum) num = maximum;
            }

            return num;
        }

        private int GetThumbPosition()
        {
            if (thumbHeight == 0 || thumbWidth == 0) return 0;
            var num = metroOrientation == MetroScrollOrientation.Vertical
                ? thumbPosition / Height / thumbHeight
                : thumbPosition / Width / thumbWidth;
            var num2 = Orientation != MetroScrollOrientation.Vertical ? Width - num : Height - num;
            var num3 = maximum - minimum;
            var num4 = 0f;
            if (num3 != 0) num4 = (curValue - (float) minimum) / num3;
            return Math.Max(thumbTopLimit, Math.Min(thumbBottomLimitTop, Convert.ToInt32(num4 * num2)));
        }

        private int GetThumbSize()
        {
            var num = metroOrientation == MetroScrollOrientation.Vertical ? Height : Width;
            if (maximum == 0 || largeChange == 0) return num;
            var val = largeChange * (float) num / maximum;
            return Convert.ToInt32(Math.Min(num, Math.Max(val, 10f)));
        }

        private void EnableTimer()
        {
            if (!progressTimer.Enabled)
            {
                progressTimer.Interval = 600;
                progressTimer.Start();
            }
            else
            {
                progressTimer.Interval = 10;
            }
        }

        private void StopTimer()
        {
            progressTimer.Stop();
        }

        private void ChangeThumbPosition(int position)
        {
            if (Orientation == MetroScrollOrientation.Vertical)
                thumbRectangle.Y = position;
            else
                thumbRectangle.X = position;
        }

        private void ProgressThumb(bool enableTimer)
        {
            var num = curValue;
            var type = ScrollEventType.First;
            int num2;
            int num3;
            if (Orientation == MetroScrollOrientation.Vertical)
            {
                num2 = thumbRectangle.Y;
                num3 = thumbRectangle.Height;
            }
            else
            {
                num2 = thumbRectangle.X;
                num3 = thumbRectangle.Width;
            }

            if (bottomBarClicked && num2 + num3 < trackPosition)
            {
                type = ScrollEventType.LargeIncrement;
                curValue = GetValue(false, false);
                if (curValue == maximum)
                {
                    ChangeThumbPosition(thumbBottomLimitTop);
                    type = ScrollEventType.Last;
                }
                else
                {
                    ChangeThumbPosition(Math.Min(thumbBottomLimitTop, GetThumbPosition()));
                }
            }
            else if (topBarClicked && num2 > trackPosition)
            {
                type = ScrollEventType.LargeDecrement;
                curValue = GetValue(false, true);
                if (curValue == minimum)
                {
                    ChangeThumbPosition(thumbTopLimit);
                    type = ScrollEventType.First;
                }
                else
                {
                    ChangeThumbPosition(Math.Max(thumbTopLimit, GetThumbPosition()));
                }
            }

            if (num != curValue)
            {
                OnScroll(type, num, curValue, scrollOrientation);
                Invalidate();
                if (enableTimer) EnableTimer();
            }
        }
    }
}