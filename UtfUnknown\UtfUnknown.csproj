﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectGuid>{70FE3D22-D272-4E1E-A5E9-B7C3B64AAE61}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <OutputType>Library</OutputType>
    <AssemblyName>UtfUnknown</AssemblyName>
    <TargetFrameworkIdentifier>.NETFramework</TargetFrameworkIdentifier>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Platform)' == 'AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <OutputPath>bin\Debug\</OutputPath>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <OutputPath>bin\Release\</OutputPath>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <Optimize>true</Optimize>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System.Core">
      <HintPath>C:\WINDOWS\Microsoft.NET\assembly\GAC_MSIL\System.Core\v4.0_4.0.0.0__b77a5c561934e089\System.Core.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Code\Analyzers\Chinese\BIG5DistributionAnalyser.cs" />
    <Compile Include="Code\Analyzers\Chinese\EUCTWDistributionAnalyser.cs" />
    <Compile Include="Code\Analyzers\Chinese\GB18030DistributionAnalyser.cs" />
    <Compile Include="Code\Analyzers\CharDistributionAnalyser.cs" />
    <Compile Include="Code\Models\MultiByte\Chinese\BIG5SMModel.cs" />
    <Compile Include="Code\Models\MultiByte\Chinese\EUCTWSMModel.cs" />
    <Compile Include="Code\Models\MultiByte\Chinese\GB18030_SMModel.cs" />
    <Compile Include="Code\Models\MultiByte\Chinese\HZ_GB_2312_SMModel.cs" />
    <Compile Include="Code\Models\MultiByte\Chinese\Iso_2022_CN_SMModel.cs" />
    <Compile Include="Code\Models\MultiByte\UTF8_SMModel.cs" />
    <Compile Include="Code\Models\StateMachineModel.cs" />
    <Compile Include="Code\Probers\MultiByte\Chinese\Big5Prober.cs" />
    <Compile Include="Code\Probers\MultiByte\Chinese\EUCTWProber.cs" />
    <Compile Include="Code\Probers\MultiByte\Chinese\GB18030Prober.cs" />
    <Compile Include="Code\Probers\MultiByte\UTF8Prober.cs" />
    <Compile Include="Code\Probers\CharsetProber.cs" />
    <Compile Include="Code\Probers\CodingStateMachine.cs" />
    <Compile Include="Code\Probers\EscCharsetProber.cs" />
    <Compile Include="Code\Probers\Latin1Prober.cs" />
    <Compile Include="Code\Probers\MBCSGroupProber.cs" />
    <Compile Include="Code\Probers\ProbingState.cs" />
    <Compile Include="Code\BitPackage.cs" />
    <Compile Include="Code\InputState.cs" />
    <Compile Include="CharsetDetector.cs" />
    <Compile Include="DetectionDetail.cs" />
    <Compile Include="DetectionResult.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>