namespace OCRTools
{
    internal class CommandAdd : Command
    {
        private readonly DrawObject _drawObject;

        public CommandAdd(DrawObject drawObject)
        {
            _drawObject = drawObject.Clone();
        }

        public override void Undo(GraphicsList list)
        {
            list.DeleteLastAddedObject();
        }

        public override void Redo(GraphicsList list)
        {
            list.UnselectAll();
            list.Add(_drawObject);
        }
    }
}