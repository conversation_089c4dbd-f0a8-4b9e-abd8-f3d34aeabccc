<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Sentry</name>
    </assembly>
    <members>
        <member name="T:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute">
            <summary>
            Indicates that compiler support for a particular feature is required for the location where this attribute is applied.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.#ctor(System.String)">
            <summary>
            Initialize a new instance of <see cref="T:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute"/>
            </summary>
        </member>
        <member name="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName">
            <summary>
            The name of the compiler feature.
            </summary>
        </member>
        <member name="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.IsOptional">
            <summary>
            If true, the compiler can choose to allow access to the location where this attribute is applied if it does not understand <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.RefStructs">
            <summary>
            The <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/> used for the ref structs C# feature.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.RequiredMembers">
            <summary>
            The <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/> used for the required members C# feature.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.DisableRuntimeMarshallingAttribute">
             <summary>
             Disables the built-in runtime managed/unmanaged marshalling subsystem for
             P/Invokes, Delegate types, and unmanaged function pointer invocations.
             </summary>
             <remarks>
             The built-in marshalling subsystem has some behaviors that cannot be changed due to
             backward-compatibility requirements. This attribute allows disabling the built-in
             subsystem and instead uses the following rules for P/Invokes, Delegates,
             and unmanaged function pointer invocations:
            
             - All value types that do not contain reference type fields recursively (<c>unmanaged</c> in C#) are blittable
             - Value types that recursively have any fields that have <c>[StructLayout(LayoutKind.Auto)]</c> are disallowed from interop.
             - All reference types are disallowed from usage in interop scenarios.
             - SetLastError support in P/Invokes is disabled.
             - varargs support is disabled.
             - LCIDConversionAttribute support is disabled.
             </remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.RequiredMemberAttribute">
            <summary>
            Specifies that a type has required members or that a member is required.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.OSPlatformAttribute">
            <summary>
            Base type for all platform-specific API attributes.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.ObsoletedOSPlatformAttribute">
            <summary>
            Marks APIs that were obsoleted in a given operating system version.
            </summary>
            <remarks>
            Primarily used by OS bindings to indicate APIs that should not be used anymore.
            </remarks>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.SetsRequiredMembersAttribute">
            <summary>
            Specifies that this constructor sets all required members for the current type, and callers
            do not need to set any required members themselves.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute">
            <summary>
            Specifies the syntax used in a string.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.#ctor(System.String)">
            <summary>
            Initializes the <see cref="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute"/> with the identifier of the syntax used.</summary>
            <param name="syntax">The syntax identifier.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.#ctor(System.String,System.Object[])">
            <summary>
            Initializes the <see cref="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute"/> with the identifier of the syntax used.</summary>
            <param name="syntax">The syntax identifier.</param>
            <param name="arguments">Optional arguments associated with the specific syntax employed.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Syntax">
            <summary>Gets the identifier of the syntax used.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Arguments">
            <summary>Optional arguments associated with the specific syntax employed.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.CompositeFormat">
            <summary>The syntax identifier for strings containing composite formats for string formatting.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.DateOnlyFormat">
            <summary>The syntax identifier for strings containing date format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.DateTimeFormat">
            <summary>The syntax identifier for strings containing date and time format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.EnumFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.Enum"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.GuidFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.Guid"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Json">
            <summary>The syntax identifier for strings containing JavaScript Object Notation (JSON).</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.NumericFormat">
            <summary>The syntax identifier for strings containing numeric format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Regex">
            <summary>The syntax identifier for strings containing regular expressions.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.TimeOnlyFormat">
            <summary>The syntax identifier for strings containing time format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.TimeSpanFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.TimeSpan"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Uri">
            <summary>The syntax identifier for strings containing URIs.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Xml">
            <summary>The syntax identifier for strings containing XML.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute">
            <summary>
            Indicates that the specified method requires the ability to generate new code at runtime,
            for example through <see cref="N:System.Reflection"/>.
            </summary>
            <remarks>
            This allows tools to understand which methods are unsafe to call when compiling ahead of time.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute"/> class
            with the specified message.
            </summary>
            <param name="message">
            A message that contains information about the usage of dynamic code.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.Message">
            <summary>
            Gets a message that contains information about the usage of dynamic code.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.Url">
            <summary>
            Gets or sets an optional URL that contains more information about the method,
            why it requires dynamic code, and what options a consumer has to deal with it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.UnscopedRefAttribute">
            <summary>
            Used to indicate a byref escapes and is not scoped.
            </summary>
            <remarks>
            <para>
            There are several cases where the C# compiler treats a <see langword="ref"/> as implicitly
            <see langword="scoped"/> - where the compiler does not allow the <see langword="ref"/> to escape the method.
            </para>
            <para>
            For example:
            <list type="number">
              <item><see langword="this"/> for <see langword="struct"/> instance methods.</item>
              <item><see langword="ref"/> parameters that refer to <see langword="ref"/> <see langword="struct"/> types.</item>
              <item><see langword="out"/> parameters.</item>
            </list>
            </para>
            <para>
            This attribute is used in those instances where the <see langword="ref"/> should be allowed to escape.
            </para>
            <para>
            Applying this attribute, in any form, has impact on consumers of the applicable API. It is necessary for
            API authors to understand the lifetime implications of applying this attribute and how it may impact their users.
            </para>
            </remarks>
        </member>
        <member name="M:System.Diagnostics.EnhancedStackFrame.GetFileColumnNumber">
            <summary>
                 Gets the column number in the file that contains the code that is executing. 
                 This information is typically extracted from the debugging symbols for the executable.
            </summary>
            <returns>The file column number, or 0 (zero) if the file column number cannot be determined.</returns>
        </member>
        <member name="M:System.Diagnostics.EnhancedStackFrame.GetFileLineNumber">
            <summary>
                Gets the line number in the file that contains the code that is executing. 
                This information is typically extracted from the debugging symbols for the executable.
            </summary>
            <returns>The file line number, or 0 (zero) if the file line number cannot be determined.</returns>
        </member>
        <member name="M:System.Diagnostics.EnhancedStackFrame.GetFileName">
            <summary>
                Gets the file name that contains the code that is executing. 
                This information is typically extracted from the debugging symbols for the executable.
            </summary>
            <returns>The file name, or null if the file name cannot be determined.</returns>
        </member>
        <member name="M:System.Diagnostics.EnhancedStackFrame.GetILOffset">
            <summary>
               Gets the offset from the start of the Microsoft intermediate language (MSIL)
               code for the method that is executing. This offset might be an approximation
               depending on whether or not the just-in-time (JIT) compiler is generating debugging
               code. The generation of this debugging information is controlled by the System.Diagnostics.DebuggableAttribute.
            </summary>
            <returns>The offset from the start of the MSIL code for the method that is executing.</returns>
        </member>
        <member name="M:System.Diagnostics.EnhancedStackFrame.GetMethod">
            <summary>
                Gets the method in which the frame is executing.
            </summary>
            <returns>The method in which the frame is executing.</returns>
        </member>
        <member name="M:System.Diagnostics.EnhancedStackFrame.GetNativeOffset">
            <summary>
                Gets the offset from the start of the native just-in-time (JIT)-compiled code
                for the method that is being executed. The generation of this debugging information
                is controlled by the System.Diagnostics.DebuggableAttribute class.
            </summary>
            <returns>The offset from the start of the JIT-compiled code for the method that is being executed.</returns>
        </member>
        <member name="M:System.Diagnostics.EnhancedStackFrame.ToString">
            <summary>
                Builds a readable representation of the stack trace.
            </summary>
            <returns>A readable representation of the stack trace.</returns>
        </member>
        <member name="P:System.Diagnostics.EnhancedStackTrace.FrameCount">
            <summary>
            Gets the number of frames in the stack trace.
            </summary>
            <returns>The number of frames in the stack trace.</returns>
        </member>
        <member name="M:System.Diagnostics.EnhancedStackTrace.GetFrame(System.Int32)">
            <summary>
            Gets the specified stack frame.
            </summary>
            <param name="index">The index of the stack frame requested.</param>
            <returns>The specified stack frame.</returns>
        </member>
        <member name="M:System.Diagnostics.EnhancedStackTrace.GetFrames">
            <summary>
                Returns a copy of all stack frames in the current stack trace.
            </summary>
            <returns>
                An array of type System.Diagnostics.StackFrame representing the function calls
                in the stack trace.
            </returns>
        </member>
        <member name="M:System.Diagnostics.EnhancedStackTrace.ToString">
            <summary>
            Builds a readable representation of the stack trace.
            </summary>
            <returns>A readable representation of the stack trace.</returns>
        </member>
        <member name="M:System.Diagnostics.EnhancedStackTrace.TryGetFullPath(System.String)">
            <summary>
            Tries to convert a given <paramref name="filePath"/> to a full path.
            Returns original value if the conversion isn't possible or a given path is relative.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ExceptionExtensions.Demystify``1(``0)">
            <summary>
            Demystifies the given <paramref name="exception"/> and tracks the original stack traces for the whole exception tree.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ExceptionExtensions.ToStringDemystified(System.Exception)">
            <summary>
            Gets demystified string representation of the <paramref name="exception"/>.
            </summary>
            <remarks>
            <see cref="M:System.Diagnostics.ExceptionExtensions.Demystify``1(``0)"/> method mutates the exception instance that can cause
            issues if a system relies on the stack trace be in the specific form.
            Unlike <see cref="M:System.Diagnostics.ExceptionExtensions.Demystify``1(``0)"/> this method is pure. It calls <see cref="M:System.Diagnostics.ExceptionExtensions.Demystify``1(``0)"/> first,
            computes a demystified string representation and then restores the original state of the exception back.
            </remarks>
        </member>
        <member name="T:System.Diagnostics.Internal.ReflectionHelper">
            <summary>
            A helper class that contains utilities methods for dealing with reflection.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Internal.ReflectionHelper.IsValueTuple(System.Type)">
            <summary>
            Returns true if the <paramref name="type"/> is a value tuple type.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Internal.ReflectionHelper.IsTupleElementNameAttribute(System.Attribute)">
            <summary>
            Returns true if the given <paramref name="attribute"/> is of type <code>TupleElementNameAttribute</code>.
            </summary>
            <remarks>
            To avoid compile-time dependency hell with System.ValueTuple, this method uses reflection and not checks statically that 
            the given <paramref name="attribute"/> is <code>TupleElementNameAttribute</code>.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.Internal.ReflectionHelper.GetTransformerNames(System.Attribute)">
            <summary>
            Returns 'TransformNames' property value from a given <paramref name="attribute"/>.
            </summary>
            <remarks>
            To avoid compile-time dependency hell with System.ValueTuple, this method uses reflection 
            instead of casting the attribute to a specific type.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.TypeNameHelper.GetTypeDisplayName(System.Type,System.Boolean,System.Boolean)">
            <summary>
            Pretty print a type name.
            </summary>
            <param name="type">The <see cref="T:System.Type"/>.</param>
            <param name="fullName"><c>true</c> to print a fully qualified name.</param>
            <param name="includeGenericParameterNames"><c>true</c> to include generic parameter names.</param>
            <returns>The pretty printed type name.</returns>
        </member>
        <member name="M:System.Diagnostics.TypeNameHelper.GetTypeNameForGenericType(System.Type)">
            <summary>
            Returns a name of given generic type without '`'.
            </summary>
        </member>
        <member name="P:System.Collections.Generic.Enumerable.EnumerableIList`1.Item(System.Int32)">
            <inheritdoc />
        </member>
        <member name="P:System.Collections.Generic.Enumerable.EnumerableIList`1.Count">
            <inheritdoc />
        </member>
        <member name="P:System.Collections.Generic.Enumerable.EnumerableIList`1.IsReadOnly">
            <inheritdoc />
        </member>
        <member name="M:System.Collections.Generic.Enumerable.EnumerableIList`1.Add(`0)">
            <inheritdoc />
        </member>
        <member name="M:System.Collections.Generic.Enumerable.EnumerableIList`1.Clear">
            <inheritdoc />
        </member>
        <member name="M:System.Collections.Generic.Enumerable.EnumerableIList`1.Contains(`0)">
            <inheritdoc />
        </member>
        <member name="M:System.Collections.Generic.Enumerable.EnumerableIList`1.CopyTo(`0[],System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:System.Collections.Generic.Enumerable.EnumerableIList`1.IndexOf(`0)">
            <inheritdoc />
        </member>
        <member name="M:System.Collections.Generic.Enumerable.EnumerableIList`1.Insert(System.Int32,`0)">
            <inheritdoc />
        </member>
        <member name="M:System.Collections.Generic.Enumerable.EnumerableIList`1.Remove(`0)">
            <inheritdoc />
        </member>
        <member name="M:System.Collections.Generic.Enumerable.EnumerableIList`1.RemoveAt(System.Int32)">
            <inheritdoc />
        </member>
        <member name="T:PolyfillExtensions">
            <summary>
            Static and thread safe wrapper around NullabilityInfoContext.
            </summary>
        </member>
        <member name="M:PolyfillExtensions.MaxBy``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
            <summary>
            Returns the maximum value in a generic sequence according to a specified key selector function.
            </summary>
            <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
            <typeparam name="TKey">The type of key to compare elements by.</typeparam>
            <param name="source">A sequence of values to determine the maximum value of.</param>
            <param name="keySelector">A function to extract the key for each element.</param>
            <returns>The value with the maximum key in the sequence.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="source" /> is <see langword="null" />.</exception>
            <exception cref="T:System.ArgumentException">No key extracted from <paramref name="source" /> implements the <see cref="T:System.IComparable" /> or <see cref="T:System.IComparable`1" /> interface.</exception>
            <remarks>
            <para>If <typeparamref name="TKey" /> is a reference type and the source sequence is empty or contains only values that are <see langword="null" />, this method returns <see langword="null" />.</para>
            </remarks>
        </member>
        <member name="M:PolyfillExtensions.MaxBy``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IComparer{``1})">
            <summary>Returns the maximum value in a generic sequence according to a specified key selector function.</summary>
            <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
            <typeparam name="TKey">The type of key to compare elements by.</typeparam>
            <param name="source">A sequence of values to determine the maximum value of.</param>
            <param name="keySelector">A function to extract the key for each element.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> to compare keys.</param>
            <returns>The value with the maximum key in the sequence.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="source" /> is <see langword="null" />.</exception>
            <exception cref="T:System.ArgumentException">No key extracted from <paramref name="source" /> implements the <see cref="T:System.IComparable" /> or <see cref="T:System.IComparable`1" /> interface.</exception>
            <remarks>
            <para>If <typeparamref name="TKey" /> is a reference type and the source sequence is empty or contains only values that are <see langword="null" />, this method returns <see langword="null" />.</para>
            </remarks>
        </member>
        <member name="M:PolyfillExtensions.MinBy``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
            <summary>
            Returns the minimum value in a generic sequence according to a specified key selector function.
            </summary>
            <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
            <typeparam name="TKey">The type of key to compare elements by.</typeparam>
            <param name="source">A sequence of values to determine the minby value of.</param>
            <param name="keySelector">A function to extract the key for each element.</param>
            <returns>The value with the minimum key in the sequence.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="source" /> is <see langword="null" />.</exception>
            <exception cref="T:System.ArgumentException">No key extracted from <paramref name="source" /> implements the <see cref="T:System.IComparable" /> or <see cref="T:System.IComparable`1" /> interface.</exception>
            <remarks>
            <para>If <typeparamref name="TKey" /> is a reference type and the source sequence is empty or contains only values that are <see langword="null" />, this method returns <see langword="null" />.</para>
            </remarks>
        </member>
        <member name="M:PolyfillExtensions.MinBy``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IComparer{``1})">
            <summary>Returns the minimum value in a generic sequence according to a specified key selector function.</summary>
            <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
            <typeparam name="TKey">The type of key to compare elements by.</typeparam>
            <param name="source">A sequence of values to determine the minimum value of.</param>
            <param name="keySelector">A function to extract the key for each element.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> to compare keys.</param>
            <returns>The value with the minimum key in the sequence.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="source" /> is <see langword="null" />.</exception>
            <exception cref="T:System.ArgumentException">No key extracted from <paramref name="source" /> implements the <see cref="T:System.IComparable" /> or <see cref="T:System.IComparable`1" /> interface.</exception>
            <remarks>
            <para>If <typeparamref name="TKey" /> is a reference type and the source sequence is empty or contains only values that are <see langword="null" />, this method returns <see langword="null" />.</para>
            </remarks>
        </member>
        <member name="M:PolyfillExtensions.Nanoseconds(System.TimeSpan)">
            <summary>
            Gets the nanosecond component of the time represented by the current <see cref="T:System.TimeSpan"/> object.
            </summary>
        </member>
        <member name="M:PolyfillExtensions.Nanosecond(System.DateTime)">
            <summary>
            Gets the nanosecond component of the time represented by the current <see cref="T:System.DateTime"/> object.
            </summary>
        </member>
        <member name="M:PolyfillExtensions.Nanosecond(System.DateTimeOffset)">
            <summary>
            Gets the nanosecond component of the time represented by the current <see cref="T:System.DateTimeOffset"/> object.
            </summary>
        </member>
        <member name="M:PolyfillExtensions.Microseconds(System.TimeSpan)">
            <summary>
            Gets the microsecond component of the time represented by the current <see cref="T:System.TimeSpan"/> object.
            </summary>
        </member>
        <member name="M:PolyfillExtensions.Microsecond(System.DateTime)">
            <summary>
            Gets the microsecond component of the time represented by the current <see cref="T:System.DateTime"/> object.
            </summary>
        </member>
        <member name="M:PolyfillExtensions.Microsecond(System.DateTimeOffset)">
            <summary>
            Gets the microsecond component of the time represented by the current <see cref="T:System.DateTimeOffset"/> object.
            </summary>
        </member>
        <member name="M:PolyfillExtensions.AddMicroseconds(System.DateTime,System.Double)">
            <summary>
            Returns a new <see cref="T:System.DateTime"/> object that adds a specified number of microseconds to the value of this instance..
            </summary>
        </member>
        <member name="M:PolyfillExtensions.AddMicroseconds(System.DateTimeOffset,System.Double)">
            <summary>
            Returns a new <see cref="T:System.DateTimeOffset"/> object that adds a specified number of microseconds to the value of this instance..
            </summary>
        </member>
        <member name="M:PolyfillExtensions.ReadToEndAsync(System.IO.TextReader,System.Threading.CancellationToken)">
            <summary>
            Reads all characters from the current position to the end of the stream asynchronously and returns them as one string.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task that represents the asynchronous read operation. The value of the <c>TResult</c> parameter contains
            a string with the characters from the current position to the end of the stream.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">The number of characters is larger than <see cref="F:System.Int32.MaxValue"/>.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream reader has been disposed.</exception>
            <exception cref="T:System.InvalidOperationException">The reader is currently in use by a previous read operation.</exception>
        </member>
        <member name="M:PolyfillExtensions.TryFormat(System.DateTimeOffset,System.Span{System.Char},System.Int32@,System.ReadOnlySpan{System.Char},System.IFormatProvider)">
            <summary>
            Tries to format the value of the current instance into the provided span of characters.
            </summary>
        </member>
        <member name="M:PolyfillExtensions.TryFormat(System.DateTime,System.Span{System.Char},System.Int32@,System.ReadOnlySpan{System.Char},System.IFormatProvider)">
            <summary>
            Tries to format the value of the current instance into the provided span of characters.
            </summary>
        </member>
        <member name="M:PolyfillExtensions.TryFormat(System.DateOnly,System.Span{System.Char},System.Int32@,System.ReadOnlySpan{System.Char},System.IFormatProvider)">
            <summary>
            Tries to format the value of the current instance into the provided span of characters.
            </summary>
        </member>
        <member name="M:PolyfillExtensions.TryFormat(System.TimeOnly,System.Span{System.Char},System.Int32@,System.ReadOnlySpan{System.Char},System.IFormatProvider)">
            <summary>
            Tries to format the value of the current instance into the provided span of characters.
            </summary>
        </member>
        <member name="M:PolyfillExtensions.IsGenericMethodParameter(System.Type)">
            <summary>
            Gets a value that indicates whether the current Type represents a type parameter in the definition of a generic method.
            </summary>
        </member>
        <member name="T:Sentry.AttachmentType">
            <summary>
            Attachment type.
            </summary>
        </member>
        <member name="F:Sentry.AttachmentType.Default">
            <summary>
            Standard attachment without special meaning.
            </summary>
        </member>
        <member name="F:Sentry.AttachmentType.Minidump">
            <summary>
            Minidump file that creates an error event and is symbolicated.
            The file should start with the <c>MDMP</c> magic bytes.
            </summary>
        </member>
        <member name="F:Sentry.AttachmentType.AppleCrashReport">
            <summary>
            Apple crash report file that creates an error event and is symbolicated.
            </summary>
        </member>
        <member name="F:Sentry.AttachmentType.UnrealContext">
            <summary>
            XML file containing UE4 crash meta data.
            During event ingestion, event contexts and extra fields are extracted from this file.
            </summary>
        </member>
        <member name="F:Sentry.AttachmentType.UnrealLogs">
            <summary>
            Plain-text log file obtained from UE4 crashes.
            During event ingestion, the last logs are extracted into event breadcrumbs.
            </summary>
        </member>
        <member name="F:Sentry.AttachmentType.ViewHierarchy">
            <summary>
            A JSON attachment containing the View Hierarchy
            </summary>
        </member>
        <member name="T:Sentry.Attachment">
            <summary>
            Sentry attachment.
            </summary>
        </member>
        <member name="P:Sentry.Attachment.Type">
            <summary>
            Attachment type.
            </summary>
        </member>
        <member name="P:Sentry.Attachment.Content">
            <summary>
            Attachment content.
            </summary>
        </member>
        <member name="P:Sentry.Attachment.FileName">
            <summary>
            Attachment file name.
            </summary>
        </member>
        <member name="P:Sentry.Attachment.ContentType">
            <summary>
            Attachment content type.
            </summary>
        </member>
        <member name="M:Sentry.Attachment.#ctor(Sentry.AttachmentType,Sentry.IAttachmentContent,System.String,System.String)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.Attachment"/>.
            </summary>
        </member>
        <member name="T:Sentry.BaggageHeader">
            <summary>
            Baggage Header for dynamic sampling.
            </summary>
            <seealso href="https://develop.sentry.dev/sdk/performance/dynamic-sampling-context"/>
            <seealso href="https://www.w3.org/TR/baggage"/>
        </member>
        <member name="M:Sentry.BaggageHeader.ToString">
            <summary>
            Creates the baggage header string based on the members of this instance.
            </summary>
            <returns>The baggage header string.</returns>
        </member>
        <member name="M:Sentry.BaggageHeader.TryParse(System.String,System.Boolean)">
            <summary>
            Parses a baggage header string.
            </summary>
            <param name="baggage">The string to parse.</param>
            <param name="onlySentry">
            When <c>false</c>, the resulting object includes all list members present in the baggage header string.
            When <c>true</c>, the resulting object includes only members prefixed with <c>"sentry-"</c>.
            </param>
            <returns>
            An object representing the members baggage header, or <c>null</c> if there are no members parsed.
            </returns>
        </member>
        <member name="T:Sentry.Breadcrumb">
            <summary>
            Series of application events.
            </summary>
        </member>
        <member name="P:Sentry.Breadcrumb.Timestamp">
            <summary>
            A timestamp representing when the breadcrumb occurred.
            </summary>
            <remarks>
            This can be either an ISO datetime string, or a Unix timestamp.
            </remarks>
        </member>
        <member name="P:Sentry.Breadcrumb.Message">
            <summary>
            If a message is provided, it’s rendered as text and the whitespace is preserved.
            Very long text might be abbreviated in the UI.
            </summary>
        </member>
        <member name="P:Sentry.Breadcrumb.Type">
            <summary>
            The type of breadcrumb.
            </summary>
            <remarks>
            The default type is default which indicates no specific handling.
            Other types are currently http for HTTP requests and navigation for navigation events.
            </remarks>
        </member>
        <member name="P:Sentry.Breadcrumb.Data">
            <summary>
            Data associated with this breadcrumb.
            </summary>
            <remarks>
            Contains a sub-object whose contents depend on the breadcrumb type.
            Additional parameters that are unsupported by the type are rendered as a key/value table.
            </remarks>
        </member>
        <member name="P:Sentry.Breadcrumb.Category">
            <summary>
            Dotted strings that indicate what the crumb is or where it comes from.
            </summary>
            <remarks>
            Typically it’s a module name or a descriptive string.
            For instance aspnet.mvc.filter could be used to indicate that it came from an Action Filter.
            </remarks>
        </member>
        <member name="P:Sentry.Breadcrumb.Level">
            <summary>
            The level of the event.
            </summary>
            <remarks>
            Levels are used in the UI to emphasize and de-emphasize the crumb.
            </remarks>
        </member>
        <member name="M:Sentry.Breadcrumb.#ctor(System.String,System.String,System.Collections.Generic.IReadOnlyDictionary{System.String,System.String},System.String,Sentry.BreadcrumbLevel)">
            <summary>
            Initializes a new instance of the <see cref="T:Sentry.Breadcrumb"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="type">The type.</param>
            <param name="data">The data.</param>
            <param name="category">The category.</param>
            <param name="level">The level.</param>
        </member>
        <member name="M:Sentry.Breadcrumb.#ctor(System.Nullable{System.DateTimeOffset},System.String,System.String,System.Collections.Generic.IReadOnlyDictionary{System.String,System.String},System.String,Sentry.BreadcrumbLevel)">
            <summary>
            Initializes a new instance of the <see cref="T:Sentry.Breadcrumb"/> class.
            </summary>
            <param name="timestamp"></param>
            <param name="message">The message.</param>
            <param name="type">The type.</param>
            <param name="data">The data.</param>
            <param name="category">The category.</param>
            <param name="level">The level.</param>
        </member>
        <member name="M:Sentry.Breadcrumb.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Breadcrumb.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.BreadcrumbLevel">
            <summary>
            The level of the Breadcrumb.
            </summary>
        </member>
        <member name="F:Sentry.BreadcrumbLevel.Debug">
            <summary>
            Debug level.
            </summary>
        </member>
        <member name="F:Sentry.BreadcrumbLevel.Info">
            <summary>
            Information level.
            </summary>
            <remarks>
            This is value 0, hence, default.
            </remarks>
        </member>
        <member name="F:Sentry.BreadcrumbLevel.Warning">
            <summary>
            Warning breadcrumb level.
            </summary>
        </member>
        <member name="F:Sentry.BreadcrumbLevel.Error">
            <summary>
            Error breadcrumb level.
            </summary>
        </member>
        <member name="F:Sentry.BreadcrumbLevel.Critical">
            <summary>
            Critical breadcrumb level.
            </summary>
        </member>
        <member name="T:Sentry.ByteAttachmentContent">
            <summary>
            Attachment sourced from a provided byte array.
            </summary>
        </member>
        <member name="M:Sentry.ByteAttachmentContent.#ctor(System.Byte[])">
            <summary>
            Creates a new instance of <see cref="T:Sentry.ByteAttachmentContent"/>.
            </summary>
        </member>
        <member name="M:Sentry.ByteAttachmentContent.GetStream">
            <inheritdoc />
        </member>
        <member name="T:Sentry.Constants">
            <summary>
            Constant values.
            </summary>
        </member>
        <member name="F:Sentry.Constants.DisableSdkDsnValue">
            <summary>
            Empty string disables the SDK.
            </summary>
            <see href="https://develop.sentry.dev/sdk/overview/#usage-for-end-users"/>
        </member>
        <member name="F:Sentry.Constants.DefaultMaxBreadcrumbs">
            <summary>
            Default maximum number of breadcrumbs to hold in memory.
            </summary>
        </member>
        <member name="F:Sentry.Constants.ProtocolVersion">
            <summary>
            Protocol version.
            </summary>
        </member>
        <member name="F:Sentry.Constants.Platform">
            <summary>
            Platform key that defines an events is coming from any .NET implementation.
            </summary>
        </member>
        <member name="T:Sentry.Contexts">
            <summary>
            Represents Sentry's structured Context.
            </summary>
            <seealso href="https://develop.sentry.dev/sdk/event-payloads/contexts/" />
        </member>
        <member name="P:Sentry.Contexts.App">
            <summary>
            Describes the application.
            </summary>
        </member>
        <member name="P:Sentry.Contexts.Browser">
            <summary>
            Describes the browser.
            </summary>
        </member>
        <member name="P:Sentry.Contexts.Device">
            <summary>
            Describes the device.
            </summary>
        </member>
        <member name="P:Sentry.Contexts.OperatingSystem">
            <summary>
            Defines the operating system.
            </summary>
            <remarks>
            In web contexts, this is the operating system of the browser (normally pulled from the User-Agent string).
            </remarks>
        </member>
        <member name="P:Sentry.Contexts.Response">
            <summary>
            Response interface that contains information on any HTTP response related to the event.
            </summary>
        </member>
        <member name="P:Sentry.Contexts.Runtime">
            <summary>
            This describes a runtime in more detail.
            </summary>
        </member>
        <member name="P:Sentry.Contexts.Gpu">
            <summary>
            This describes a GPU of the device.
            </summary>
        </member>
        <member name="P:Sentry.Contexts.Trace">
            <summary>
            This describes trace information.
            </summary>
        </member>
        <member name="M:Sentry.Contexts.#ctor">
            <summary>
            Initializes an instance of <see cref="T:Sentry.Contexts"/>.
            </summary>
        </member>
        <member name="M:Sentry.Contexts.Clone">
            <summary>
            Creates a deep clone of this context.
            </summary>
        </member>
        <member name="M:Sentry.Contexts.CopyTo(Sentry.Contexts)">
            <summary>
            Copies the items of the context while cloning the known types.
            </summary>
        </member>
        <member name="M:Sentry.Contexts.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Contexts.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.CrashType">
            <summary>
            A type of application crash.
            Used exclusively by <see cref="M:Sentry.SentrySdk.CauseCrash(Sentry.CrashType)"/>.
            </summary>
        </member>
        <member name="F:Sentry.CrashType.Managed">
            <summary>
            A managed <see cref="T:System.ApplicationException"/> will be thrown from .NET.
            </summary>
        </member>
        <member name="F:Sentry.CrashType.ManagedBackgroundThread">
            <summary>
            A managed <see cref="T:System.ApplicationException"/> will be thrown from .NET on a background thread.
            </summary>
        </member>
        <member name="F:Sentry.CrashType.Native">
            <summary>
            A native operation that will crash the appliction will be performed by a C library.
            </summary>
        </member>
        <member name="T:Sentry.DebugImage">
            <summary>
            The Sentry Debug Meta Images interface.
            </summary>
            <see href="https://develop.sentry.dev/sdk/event-payloads/debugmeta#debug-images"/>
        </member>
        <member name="P:Sentry.DebugImage.Type">
            <summary>
            Type of the debug image.
            </summary>
        </member>
        <member name="P:Sentry.DebugImage.ImageAddress">
            <summary>
            Memory address, at which the image is mounted in the virtual address space of the process.
            Should be a string in hex representation prefixed with "0x".
            </summary>
        </member>
        <member name="P:Sentry.DebugImage.ImageSize">
            <summary>
            The size of the image in virtual memory.
            If missing, Sentry will assume that the image spans up to the next image, which might lead to invalid stack traces.
            </summary>
        </member>
        <member name="P:Sentry.DebugImage.DebugId">
            <summary>
            Unique debug identifier of the image.
            </summary>
        </member>
        <member name="P:Sentry.DebugImage.DebugChecksum">
            <summary>
            Checksum of the companion debug file.
            </summary>
        </member>
        <member name="P:Sentry.DebugImage.DebugFile">
            <summary>
            Path and name of the debug companion file.
            </summary>
        </member>
        <member name="P:Sentry.DebugImage.CodeId">
            <summary>
            Optional identifier of the code file.
            </summary>
        </member>
        <member name="P:Sentry.DebugImage.CodeFile">
            <summary>
            The absolute path to the dynamic library or executable.
            This helps to locate the file if it is missing on Sentry.
            </summary>
        </member>
        <member name="M:Sentry.DebugImage.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.DebugImage.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.DebugMeta">
            <summary>
            The Sentry Debug Meta interface.
            </summary>
            <see href="https://develop.sentry.dev/sdk/event-payloads/debugmeta"/>
        </member>
        <member name="M:Sentry.DebugMeta.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.DebugMeta.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.DeduplicateMode">
            <summary>
            Possible modes of dropping events that are detected to be duplicates.
            </summary>
        </member>
        <member name="F:Sentry.DeduplicateMode.SameEvent">
            <summary>
            Same event instance. Assumes no object reuse/pooling.
            </summary>
        </member>
        <member name="F:Sentry.DeduplicateMode.SameExceptionInstance">
            <summary>
            An exception that was captured twice.
            </summary>
        </member>
        <member name="F:Sentry.DeduplicateMode.InnerException">
            <summary>
            An exception already captured exists as an inner exception.
            </summary>
        </member>
        <member name="F:Sentry.DeduplicateMode.AggregateException">
            <summary>
            An exception already captured is part of the aggregate exception.
            </summary>
        </member>
        <member name="F:Sentry.DeduplicateMode.All">
            <summary>
            All modes combined.
            </summary>
        </member>
        <member name="T:Sentry.DefaultSentryScopeStateProcessor">
            <summary>
            Defines the logic for applying state onto a scope.
            </summary>
        </member>
        <member name="M:Sentry.DefaultSentryScopeStateProcessor.Apply(Sentry.Scope,System.Object)">
            <summary>
            Applies state onto a scope.
            </summary>
        </member>
        <member name="T:Sentry.Dsn">
            <summary>
            The Data Source Name of a given project in Sentry.
            </summary>
            <remarks>
            <see href="https://develop.sentry.dev/sdk/overview/#parsing-the-dsn"/>
            </remarks>
        </member>
        <member name="P:Sentry.Dsn.Source">
            <summary>
            Source DSN string.
            </summary>
        </member>
        <member name="P:Sentry.Dsn.ProjectId">
            <summary>
            The project ID which the authenticated user is bound to.
            </summary>
        </member>
        <member name="P:Sentry.Dsn.Path">
            <summary>
            An optional path of which Sentry is hosted.
            </summary>
        </member>
        <member name="P:Sentry.Dsn.SecretKey">
            <summary>
            The optional secret key to authenticate the SDK.
            </summary>
        </member>
        <member name="P:Sentry.Dsn.PublicKey">
            <summary>
            The required public key to authenticate the SDK.
            </summary>
        </member>
        <member name="P:Sentry.Dsn.ApiBaseUri">
            <summary>
            Sentry API's base URI.
            </summary>
        </member>
        <member name="T:Sentry.DsnAttribute">
            <summary>
            A way to configure the DSN via attribute defined at the entry-assembly.
            </summary>
        </member>
        <member name="P:Sentry.DsnAttribute.Dsn">
            <summary>
            The string DSN or empty string to turn the SDK off.
            </summary>
        </member>
        <member name="M:Sentry.DsnAttribute.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Sentry.DsnAttribute" />.
            </summary>
        </member>
        <member name="T:Sentry.DynamicSamplingContext">
            <summary>
            Provides the Dynamic Sampling Context for a transaction.
            </summary>
            <seealso href="https://develop.sentry.dev/sdk/performance/dynamic-sampling-context"/>
        </member>
        <member name="F:Sentry.DynamicSamplingContext.Empty">
            <summary>
            Gets an empty <see cref="T:Sentry.DynamicSamplingContext"/> that can be used to "freeze" the DSC on a transaction.
            </summary>
        </member>
        <member name="T:Sentry.Extensibility.BaseRequestPayloadExtractor">
            <summary>
            Base type for payload extraction.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.BaseRequestPayloadExtractor.ExtractPayload(Sentry.Extensibility.IHttpRequest)">
            <summary>
            Extract the payload of the <see cref="T:Sentry.Extensibility.IHttpRequest"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.BaseRequestPayloadExtractor.IsSupported(Sentry.Extensibility.IHttpRequest)">
            <summary>
            Whether this implementation supports the <see cref="T:Sentry.Extensibility.IHttpRequest"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.BaseRequestPayloadExtractor.DoExtractPayLoad(Sentry.Extensibility.IHttpRequest)">
            <summary>
            The extraction that gets called in case <see cref="M:Sentry.Extensibility.BaseRequestPayloadExtractor.IsSupported(Sentry.Extensibility.IHttpRequest)"/> is true.
            </summary>
        </member>
        <member name="T:Sentry.Extensibility.DefaultRequestPayloadExtractor">
            <summary>
            Default request payload extractor that will read the body as a string.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DefaultRequestPayloadExtractor.IsSupported(Sentry.Extensibility.IHttpRequest)">
            <summary>
            Whether the <see cref="T:Sentry.Extensibility.IHttpRequest"/> is supported.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DefaultRequestPayloadExtractor.DoExtractPayLoad(Sentry.Extensibility.IHttpRequest)">
            <summary>
            Extracts the request body of the <see cref="T:Sentry.Extensibility.IHttpRequest"/> as a string.
            </summary>
        </member>
        <member name="T:Sentry.Extensibility.DiagnosticLoggerExtensions">
            <summary>
            The generic overloads avoid boxing in case logging is disabled for that level
            </summary>
            <remarks>
            Calls to this class verify the level before calling the overload with object params.
            </remarks>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.Log(Sentry.SentryOptions,Sentry.SentryLevel,System.String,System.Exception,System.Object[])">
            <summary>
            Log an internal SDK message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogDebug``1(Sentry.Extensibility.IDiagnosticLogger,System.String,``0)">
            <summary>
            Log a debug message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogDebug``1(Sentry.SentryOptions,System.String,``0)">
            <summary>
            Log a debug message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogDebug``2(Sentry.Extensibility.IDiagnosticLogger,System.String,``0,``1)">
            <summary>
            Log a debug message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogDebug``2(Sentry.SentryOptions,System.String,``0,``1)">
            <summary>
            Log a debug message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogDebug(Sentry.Extensibility.IDiagnosticLogger,System.String)">
            <summary>
            Log a debug message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogDebug(Sentry.SentryOptions,System.String)">
            <summary>
            Log a debug message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogInfo(Sentry.Extensibility.IDiagnosticLogger,System.String)">
            <summary>
            Log a info message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogInfo(Sentry.SentryOptions,System.String)">
            <summary>
            Log a info message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogInfo``1(Sentry.Extensibility.IDiagnosticLogger,System.String,``0)">
            <summary>
            Log a info message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogInfo``1(Sentry.SentryOptions,System.String,``0)">
            <summary>
            Log a info message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogInfo``2(Sentry.Extensibility.IDiagnosticLogger,System.String,``0,``1)">
            <summary>
            Log a info message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogInfo``2(Sentry.SentryOptions,System.String,``0,``1)">
            <summary>
            Log a info message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogInfo``3(Sentry.Extensibility.IDiagnosticLogger,System.String,``0,``1,``2)">
            <summary>
            Log a info message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogInfo``3(Sentry.SentryOptions,System.String,``0,``1,``2)">
            <summary>
            Log a info message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogWarning(Sentry.Extensibility.IDiagnosticLogger,System.String,System.Exception)">
            <summary>
            Log a warning message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogWarning(Sentry.SentryOptions,System.String,System.Exception)">
            <summary>
            Log a warning message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogWarning``1(Sentry.Extensibility.IDiagnosticLogger,System.String,``0)">
            <summary>
            Log a warning message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogWarning``1(Sentry.SentryOptions,System.String,``0)">
            <summary>
            Log a warning message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogWarning``2(Sentry.Extensibility.IDiagnosticLogger,System.String,``0,``1)">
            <summary>
            Log a warning message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogWarning``2(Sentry.SentryOptions,System.String,``0,``1)">
            <summary>
            Log a warning message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogError(Sentry.Extensibility.IDiagnosticLogger,System.String,System.Exception)">
            <summary>
            Log a error message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogError(Sentry.SentryOptions,System.String,System.Exception)">
            <summary>
            Log a error message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogError``1(Sentry.Extensibility.IDiagnosticLogger,System.String,System.Exception,``0)">
            <summary>
            Log a error message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogError``1(Sentry.SentryOptions,System.String,System.Exception,``0)">
            <summary>
            Log a error message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogError``2(Sentry.Extensibility.IDiagnosticLogger,System.String,System.Exception,``0,``1)">
            <summary>
            Log a error message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogError``2(Sentry.SentryOptions,System.String,System.Exception,``0,``1)">
            <summary>
            Log a error message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogError``4(Sentry.Extensibility.IDiagnosticLogger,System.String,System.Exception,``0,``1,``2,``3)">
            <summary>
            Log a error message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogError``4(Sentry.SentryOptions,System.String,System.Exception,``0,``1,``2,``3)">
            <summary>
            Log a error message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogError``3(Sentry.Extensibility.IDiagnosticLogger,System.Exception,System.String,``0,``1,``2)">
            <summary>
            Log an error message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogError``3(Sentry.SentryOptions,System.Exception,System.String,``0,``1,``2)">
            <summary>
            Log an error message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogFatal(Sentry.Extensibility.IDiagnosticLogger,System.String,System.Exception)">
            <summary>
            Log a warning message.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DiagnosticLoggerExtensions.LogFatal(Sentry.SentryOptions,System.String,System.Exception)">
            <summary>
            Log a warning message.
            </summary>
        </member>
        <member name="T:Sentry.Extensibility.DisabledHub">
            <summary>
            Disabled Hub.
            </summary>
        </member>
        <member name="F:Sentry.Extensibility.DisabledHub.Instance">
            <summary>
            The singleton instance.
            </summary>
        </member>
        <member name="P:Sentry.Extensibility.DisabledHub.IsEnabled">
            <summary>
            Always disabled.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.ConfigureScope(System.Action{Sentry.Scope})">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.ConfigureScopeAsync(System.Func{Sentry.Scope,System.Threading.Tasks.Task})">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.PushScope">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.PushScope``1(``0)">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.WithScope(System.Action{Sentry.Scope})">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.StartTransaction(Sentry.ITransactionContext,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Returns a dummy transaction.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.BindException(System.Exception,Sentry.ISpan)">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.GetSpan">
            <summary>
            Returns null.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.GetTraceHeader">
            <summary>
            Returns null.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.GetBaggage">
            <summary>
            Returns null.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.ContinueTrace(System.String,System.String,System.String,System.String)">
            <summary>
            Returns sampled out transaction context.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.ContinueTrace(Sentry.SentryTraceHeader,Sentry.BaggageHeader,System.String,System.String)">
            <summary>
            Returns sampled out transaction context.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.StartSession">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.PauseSession">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.ResumeSession">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.EndSession(Sentry.SessionEndStatus)">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.BindClient(Sentry.ISentryClient)">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.CaptureEvent(Sentry.SentryEvent,Sentry.Scope)">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.CaptureEvent(Sentry.SentryEvent,Sentry.Hint,Sentry.Scope)">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.CaptureEvent(Sentry.SentryEvent,System.Action{Sentry.Scope})">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.CaptureTransaction(Sentry.Transaction)">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.CaptureTransaction(Sentry.Transaction,Sentry.Hint)">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.CaptureSession(Sentry.SessionUpdate)">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.FlushAsync(System.TimeSpan)">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.Dispose">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.DisabledHub.CaptureUserFeedback(Sentry.UserFeedback)">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="P:Sentry.Extensibility.DisabledHub.LastEventId">
            <summary>
            No-Op.
            </summary>
        </member>
        <member name="T:Sentry.Extensibility.FormRequestPayloadExtractor">
            <summary>
            Form based request extractor.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.FormRequestPayloadExtractor.IsSupported(Sentry.Extensibility.IHttpRequest)">
            <summary>
            Supports <see cref="T:Sentry.Extensibility.IHttpRequest"/> with content type application/x-www-form-urlencoded.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.FormRequestPayloadExtractor.DoExtractPayLoad(Sentry.Extensibility.IHttpRequest)">
            <summary>
            Extracts the request form data as a dictionary.
            </summary>
        </member>
        <member name="T:Sentry.Extensibility.HubAdapter">
            <summary>
            An implementation of <see cref="T:Sentry.IHub" /> which forwards any call to <see cref="T:Sentry.SentrySdk" />.
            </summary>
            <remarks>
            Allows testing classes which otherwise would need to depend on static <see cref="T:Sentry.SentrySdk" />
            by having them depend on <see cref="T:Sentry.IHub"/> instead, which can be mocked.
            </remarks>
            <inheritdoc cref="T:Sentry.IHub" />
        </member>
        <member name="F:Sentry.Extensibility.HubAdapter.Instance">
            <summary>
            The single instance which forwards all calls to <see cref="T:Sentry.SentrySdk"/>
            </summary>
        </member>
        <member name="P:Sentry.Extensibility.HubAdapter.IsEnabled">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="P:Sentry.Extensibility.HubAdapter.LastEventId">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.ConfigureScope(System.Action{Sentry.Scope})">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.ConfigureScopeAsync(System.Func{Sentry.Scope,System.Threading.Tasks.Task})">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.PushScope">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.PushScope``1(``0)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.WithScope(System.Action{Sentry.Scope})">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.StartTransaction(Sentry.ITransactionContext,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.StartTransaction(Sentry.ITransactionContext,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object},Sentry.DynamicSamplingContext)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.BindException(System.Exception,Sentry.ISpan)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.GetSpan">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.GetTraceHeader">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.GetBaggage">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.ContinueTrace(System.String,System.String,System.String,System.String)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.ContinueTrace(Sentry.SentryTraceHeader,Sentry.BaggageHeader,System.String,System.String)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.StartSession">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.PauseSession">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.ResumeSession">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.EndSession(Sentry.SessionEndStatus)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.BindClient(Sentry.ISentryClient)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.AddBreadcrumb(System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String},Sentry.BreadcrumbLevel)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.AddBreadcrumb(Sentry.Infrastructure.ISystemClock,System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String},Sentry.BreadcrumbLevel)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.Sentry#Internal#IHubEx#CaptureEventInternal(Sentry.SentryEvent,Sentry.Hint,Sentry.Scope)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.CaptureEvent(Sentry.SentryEvent)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.CaptureEvent(Sentry.SentryEvent,Sentry.Scope)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.CaptureEvent(Sentry.SentryEvent,Sentry.Hint,Sentry.Scope)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.CaptureEvent(Sentry.SentryEvent,System.Action{Sentry.Scope})">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.CaptureException(System.Exception)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.CaptureTransaction(Sentry.Transaction)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.CaptureTransaction(Sentry.Transaction,Sentry.Hint)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.CaptureSession(Sentry.SessionUpdate)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.FlushAsync(System.TimeSpan)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.CaptureUserFeedback(Sentry.UserFeedback)">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.WithScope``1(System.Func{Sentry.Scope,``0})">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.WithScopeAsync(System.Func{Sentry.Scope,System.Threading.Tasks.Task})">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.HubAdapter.WithScopeAsync``1(System.Func{Sentry.Scope,System.Threading.Tasks.Task{``0}})">
            <summary>
            Forwards the call to <see cref="T:Sentry.SentrySdk"/>
            </summary>
        </member>
        <member name="T:Sentry.Extensibility.IBackgroundWorker">
            <summary>
            A worker that queues envelopes synchronously and flushes async.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.IBackgroundWorker.EnqueueEnvelope(Sentry.Protocol.Envelopes.Envelope)">
            <summary>
            Attempts to enqueue the envelope with the worker.
            </summary>
            <param name="envelope">The envelope to enqueue.</param>
            <returns>True of queueing was successful. Otherwise, false.</returns>
        </member>
        <member name="M:Sentry.Extensibility.IBackgroundWorker.FlushAsync(System.TimeSpan)">
            <summary>
            Flushes envelopes asynchronously.
            </summary>
            <param name="timeout">How long to wait for flush to finish.</param>
            <returns>A task to await for the flush operation.</returns>
        </member>
        <member name="P:Sentry.Extensibility.IBackgroundWorker.QueuedItems">
            <summary>
            Current count of items queued up.
            </summary>
        </member>
        <member name="T:Sentry.Extensibility.IDiagnosticLogger">
            <summary>
            Abstraction for internal logging.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.IDiagnosticLogger.IsEnabled(Sentry.SentryLevel)">
            <summary>
            Whether the logger is enabled or not to the specified <see cref="T:Sentry.SentryLevel"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.IDiagnosticLogger.Log(Sentry.SentryLevel,System.String,System.Exception,System.Object[])">
            <summary>
            Log an internal SDK message.
            </summary>
            <param name="logLevel">The level.</param>
            <param name="message">The message.</param>
            <param name="exception">An optional Exception.</param>
            <param name="args">Optional arguments for string template.</param>
        </member>
        <member name="T:Sentry.Extensibility.IExceptionFilter">
            <summary>
            A filter to be applied to an exception instance.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.IExceptionFilter.Filter(System.Exception)">
            <summary>
            Whether to filter out or not the exception.
            </summary>
            <param name="ex">The exception about to be captured.</param>
            <returns><c>true</c> if [the event should be filtered out]; otherwise, <c>false</c>.</returns>
        </member>
        <member name="T:Sentry.Extensibility.IHttpRequest">
            <summary>
            An abstraction to an HTTP Request.
            </summary>
        </member>
        <member name="P:Sentry.Extensibility.IHttpRequest.ContentLength">
            <summary>
            The content length.
            </summary>
        </member>
        <member name="P:Sentry.Extensibility.IHttpRequest.ContentType">
            <summary>
            The content type.
            </summary>
        </member>
        <member name="P:Sentry.Extensibility.IHttpRequest.Body">
            <summary>
            The request body.
            </summary>
        </member>
        <member name="P:Sentry.Extensibility.IHttpRequest.Form">
            <summary>
            Represents the parsed form values sent with the HttpRequest.
            </summary>
        </member>
        <member name="T:Sentry.Extensibility.INetworkStatusListener">
            <summary>
            Provides a mechanism to convey network status.
            Used internally by some integrations. Not intended for public usage.
            </summary>
            <remarks>
            This must be public because we use it in Sentry.Maui, which can't use InternalsVisibleTo
            because MAUI assemblies are not strong-named.
            </remarks>
        </member>
        <member name="P:Sentry.Extensibility.INetworkStatusListener.Online">
            <summary>
            Gets a value that indicates whether the network is online.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.INetworkStatusListener.WaitForNetworkOnlineAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously waits for the network to come online.
            </summary>
            <param name="cancellationToken">A token which cancels waiting.</param>
        </member>
        <member name="T:Sentry.Extensibility.IRequestPayloadExtractor">
            <summary>
            A request body extractor.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.IRequestPayloadExtractor.ExtractPayload(Sentry.Extensibility.IHttpRequest)">
            <summary>
            Extracts the payload of the provided <see cref="T:Sentry.Extensibility.IHttpRequest"/>.
            </summary>
            <param name="request">The HTTP Request object.</param>
            <returns>The extracted payload.</returns>
        </member>
        <member name="T:Sentry.Extensibility.ISentryEventExceptionProcessor">
            <summary>
            Process exceptions and augments the event with its data.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.ISentryEventExceptionProcessor.Process(System.Exception,Sentry.SentryEvent)">
            <summary>
            Process the exception and augments the event with its data.
            </summary>
            <param name="exception">The exception to process.</param>
            <param name="sentryEvent">The event to add data to.</param>
        </member>
        <member name="T:Sentry.Extensibility.ISentryEventProcessor">
            <summary>
            Process a SentryEvent during the prepare phase.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.ISentryEventProcessor.Process(Sentry.SentryEvent)">
            <summary>
            Process the <see cref="T:Sentry.SentryEvent"/>
            </summary>
            <param name="event">The event to process</param>
            <return>The processed event or <c>null</c> if the event was dropped.</return>
            <remarks>
            The event returned can be the same instance received or a new one.
            Returning null will stop the processing pipeline.
            Meaning the event should no longer be processed nor send.
            </remarks>
        </member>
        <member name="T:Sentry.Extensibility.ISentryEventProcessorWithHint">
            <summary>
            Process a SentryEvent during the prepare phase.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.ISentryEventProcessorWithHint.Process(Sentry.SentryEvent,Sentry.Hint)">
            <summary>
            Process the <see cref="T:Sentry.SentryEvent"/>
            </summary>
            <param name="event">The event to process</param>
            <param name="hint">A <see cref="T:Sentry.Hint"/> with context that may be useful prior to sending the event</param>
            <return>The processed event or <c>null</c> if the event was dropped.</return>
            <remarks>
            The event returned can be the same instance received or a new one.
            Returning null will stop the processing pipeline so that the event will neither be processed by
            additional event processors or sent to Sentry.
            </remarks>
        </member>
        <member name="T:Sentry.Extensibility.ISentryStackTraceFactory">
            <summary>
            Factory to <see cref="T:Sentry.SentryStackTrace" /> from an <see cref="T:System.Exception" />.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.ISentryStackTraceFactory.Create(System.Exception)">
            <summary>
            Creates a <see cref="T:Sentry.SentryStackTrace" /> from the optional <see cref="T:System.Exception" />.
            </summary>
            <param name="exception">The exception to create the stacktrace from.</param>
            <returns>A Sentry stack trace.</returns>
        </member>
        <member name="T:Sentry.Extensibility.ISentryTransactionProcessor">
            <summary>
            Process a <see cref="T:Sentry.Transaction"/> during the prepare phase.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.ISentryTransactionProcessor.Process(Sentry.Transaction)">
            <summary>
            Process the <see cref="T:Sentry.Transaction"/>
            </summary>
            <param name="transaction">The Transaction to process</param>
            <remarks>
            The transaction returned can be the same instance received or a new one.
            Returning null will stop the processing pipeline.
            Meaning the transaction should no longer be processed nor send.
            </remarks>
        </member>
        <member name="T:Sentry.Extensibility.ISentryTransactionProcessorWithHint">
            <summary>
            Process a <see cref="T:Sentry.Transaction"/> during the prepare phase.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.ISentryTransactionProcessorWithHint.Process(Sentry.Transaction,Sentry.Hint)">
            <summary>
            Process the <see cref="T:Sentry.Transaction"/>
            </summary>
            <param name="transaction">The Transaction to process</param>
            <param name="hint">A <see cref="T:Sentry.Hint"/> with context that may be useful prior to sending the transaction</param>
            <remarks>
            The transaction returned can be the same instance received or a new one.
            Returning null will stop the processing pipeline.
            Meaning the transaction should no longer be processed nor send.
            </remarks>
        </member>
        <member name="T:Sentry.Extensibility.ITransport">
            <summary>
            An abstraction to the transport of the event.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.ITransport.SendEnvelopeAsync(Sentry.Protocol.Envelopes.Envelope,System.Threading.CancellationToken)">
            <summary>
            Sends the <see cref="T:Sentry.Protocol.Envelopes.Envelope" /> to Sentry asynchronously.
            </summary>
            <param name="envelope">The envelope to send to Sentry.</param>
            <param name="cancellationToken">The cancellation token.</param>
        </member>
        <member name="T:Sentry.Extensibility.RequestBodyExtractionDispatcher">
            <summary>
            Dispatches request body extractions if enabled and within limits.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.RequestBodyExtractionDispatcher.#ctor(System.Collections.Generic.IEnumerable{Sentry.Extensibility.IRequestPayloadExtractor},Sentry.SentryOptions,System.Func{Sentry.Extensibility.RequestSize})">
            <summary>
            Creates a new instance of <see cref="T:Sentry.Extensibility.RequestBodyExtractionDispatcher"/>.
            </summary>
            <param name="extractors">Extractors to use.</param>
            <param name="options">Sentry Options.</param>
            <param name="sizeSwitch">The max request size to capture.</param>
        </member>
        <member name="M:Sentry.Extensibility.RequestBodyExtractionDispatcher.ExtractPayload(Sentry.Extensibility.IHttpRequest)">
            <summary>
            Extract the payload using the provided extractors.
            </summary>
            <param name="request">The request.</param>
            <returns>A serializable representation of the payload.</returns>
        </member>
        <member name="T:Sentry.Extensibility.RequestSize">
            <summary>
            The size allowed when extracting a request body in a web application.
            </summary>
        </member>
        <member name="F:Sentry.Extensibility.RequestSize.None">
            <summary>
            No request payload is extracted
            </summary>
            <remarks>This is the default value. Opt-in is required.</remarks>
        </member>
        <member name="F:Sentry.Extensibility.RequestSize.Small">
            <summary>
            A small payload is extracted.
            </summary>
        </member>
        <member name="F:Sentry.Extensibility.RequestSize.Medium">
            <summary>
            A medium payload is extracted.
            </summary>
        </member>
        <member name="F:Sentry.Extensibility.RequestSize.Always">
            <summary>
            The SDK will always capture the request body. Sentry might truncate or reject the event if too large.
            </summary>
        </member>
        <member name="T:Sentry.Extensibility.SentryEventExceptionProcessor`1">
            <summary>
            Process an exception type and augments the event with its data.
            </summary>
            <typeparam name="TException">The type of the exception to process.</typeparam>
            <inheritdoc />
        </member>
        <member name="M:Sentry.Extensibility.SentryEventExceptionProcessor`1.Process(System.Exception,Sentry.SentryEvent)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Extensibility.SentryEventExceptionProcessor`1.ProcessException(`0,Sentry.SentryEvent)">
            <summary>
            Process the exception and event.
            </summary>
            <param name="exception">The exception to process.</param>
            <param name="sentryEvent">The event to process.</param>
        </member>
        <member name="T:Sentry.Extensibility.SentryStackTraceFactory">
            <summary>
            Default factory to <see cref="T:Sentry.SentryStackTrace" /> from an <see cref="T:System.Exception" />.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.SentryStackTraceFactory.#ctor(Sentry.SentryOptions)">
            <summary>
            Creates an instance of <see cref="T:Sentry.Extensibility.SentryStackTraceFactory"/>.
            </summary>
        </member>
        <member name="M:Sentry.Extensibility.SentryStackTraceFactory.Create(System.Exception)">
            <summary>
            Creates a <see cref="T:Sentry.SentryStackTrace" /> from the optional <see cref="T:System.Exception" />.
            </summary>
            <param name="exception">The exception to create the stacktrace from.</param>
            <returns>A Sentry stack trace.</returns>
        </member>
        <member name="T:Sentry.FileAttachmentContent">
            <summary>
            Attachment sourced from the file system.
            </summary>
        </member>
        <member name="M:Sentry.FileAttachmentContent.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Sentry.FileAttachmentContent"/>.
            </summary>
            <param name="filePath">The path to the file to attach.</param>
        </member>
        <member name="M:Sentry.FileAttachmentContent.#ctor(System.String,System.Boolean)">
            <summary>
            Creates a new instance of <see cref="T:Sentry.FileAttachmentContent"/>.
            </summary>
            <param name="filePath">The path to the file to attach.</param>
            <param name="readFileAsynchronously">Whether to use async file I/O to read the file.</param>
        </member>
        <member name="M:Sentry.FileAttachmentContent.GetStream">
            <inheritdoc />
        </member>
        <member name="P:Sentry.GraphQLRequestContent.Query">
            <summary>
            Document containing GraphQL to execute.
            It can be null for automatic persisted queries, in which case a SHA-256 hash of the query would be sent in the
            Extensions. See https://www.apollographql.com/docs/apollo-server/performance/apq/ for details.
            </summary>
        </member>
        <member name="M:Sentry.GraphQLRequestContent.OperationNameOrFallback">
            <summary>
            Returns the OperationName if present or "graphql" otherwise.
            </summary>
        </member>
        <member name="M:Sentry.GraphQLRequestContent.OperationTypeOrFallback">
            <summary>
            Returns the OperationType if present or "graphql.operation" otherwise.
            </summary>
        </member>
        <member name="T:Sentry.Hint">
            <summary>
            A hint that can be provided when capturing a <see cref="T:Sentry.SentryEvent"/> or when adding a <see cref="T:Sentry.Breadcrumb"/>.
            Hints can be used to filter or modify events, transactions, or breadcrumbs before they are sent to Sentry.
            </summary>
        </member>
        <member name="M:Sentry.Hint.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Sentry.Hint"/>.
            </summary>
        </member>
        <member name="M:Sentry.Hint.#ctor(System.String,System.Object)">
            <summary>
            Creates a new hint containing a single item.
            </summary>
            <param name="key">The key of the hint item.</param>
            <param name="value">The value of the hint item.</param>
        </member>
        <member name="P:Sentry.Hint.Attachments">
            <summary>
            Attachments added to the Hint.
            </summary>
            <remarks>
            This collection represents all of the attachments that will be sent to Sentry with the corresponding event.
            You can add or remove attachments from this collection as needed.
            </remarks>
        </member>
        <member name="P:Sentry.Hint.Items">
            <summary>
            A dictionary of arbitrary items provided with the Hint.
            </summary>
            <remarks>
            These are not sent to Sentry, but rather they are available during processing, such as when using
            BeforeSend and others.
            </remarks>
        </member>
        <member name="M:Sentry.Hint.AddAttachmentsFromScope(Sentry.Scope)">
            <summary>
            The Java SDK has some logic so that certain Hint types do not copy attachments from the Scope.
            This provides a location that allows us to do the same in the .NET SDK in the future.
            </summary>
            <param name="scope">The <see cref="T:Sentry.Scope"/> that the attachments should be copied from</param>
        </member>
        <member name="M:Sentry.Hint.AddAttachment(System.String,Sentry.AttachmentType,System.String)">
            <summary>
            Creates a new Hint with one or more attachments.
            </summary>
            <param name="filePath">The path to the file to attach.</param>
            <param name="type">The type of attachment.</param>
            <param name="contentType">The content type of the attachment.</param>
        </member>
        <member name="M:Sentry.Hint.WithAttachments(Sentry.Attachment[])">
            <summary>
            Creates a new Hint with one or more attachments.
            </summary>
            <param name="attachments">The attachment(s) to add.</param>
            <returns>A Hint having the attachment(s).</returns>
        </member>
        <member name="M:Sentry.Hint.WithAttachments(System.Collections.Generic.IEnumerable{Sentry.Attachment})">
            <summary>
            Creates a new Hint with attachments.
            </summary>
            <param name="attachments">The attachments to add.</param>
            <returns>A Hint having the attachments.</returns>
        </member>
        <member name="T:Sentry.HintTypes">
            <summary>
            Constants used to name Hints generated by the Sentry SDK
            </summary>
        </member>
        <member name="F:Sentry.HintTypes.HttpResponseMessage">
            <summary>
            Used for HttpResponseMessage hints
            </summary>
        </member>
        <member name="T:Sentry.Http.HttpTransportBase">
            <summary>
            Provides a base class for Sentry HTTP transports.  Used internally by the Sentry SDK,
            but also allows for higher-level SDKs (such as Unity) to implement their own transport.
            </summary>
        </member>
        <member name="M:Sentry.Http.HttpTransportBase.#ctor(Sentry.SentryOptions,System.Func{System.String,System.String},Sentry.Infrastructure.ISystemClock)">
            <summary>
            Constructor for this class.
            </summary>
            <param name="options">The Sentry options.</param>
            <param name="getEnvironmentVariable">An optional method used to read environment variables.</param>
            <param name="clock">An optional system clock - used for testing.</param>
        </member>
        <member name="M:Sentry.Http.HttpTransportBase.ProcessEnvelope(Sentry.Protocol.Envelopes.Envelope)">
            <summary>
            Processes an envelope before sending.
            Repackages the original envelope discarding items that don't fit the rate limit.
            </summary>
            <param name="envelope">The envelope to process.</param>
            <returns>The processed envelope, ready to be sent.</returns>
        </member>
        <member name="M:Sentry.Http.HttpTransportBase.CreateRequest(Sentry.Protocol.Envelopes.Envelope)">
            <summary>
            Creates an HTTP request message from an envelope.
            </summary>
            <param name="envelope">The envelope.</param>
            <returns>An HTTP request message, with the proper headers and body set.</returns>
            <exception cref="T:System.InvalidOperationException">Throws if the DSN is not set in the options.</exception>
        </member>
        <member name="M:Sentry.Http.HttpTransportBase.HandleResponse(System.Net.Http.HttpResponseMessage,Sentry.Protocol.Envelopes.Envelope)">
            <summary>
            Synchronously handles the response message after it is received, extracting any information from the
            response such as rate limits, or error messages.
            </summary>
            <param name="response">The response message received from Sentry.</param>
            <param name="envelope">The envelope that was being sent.</param>
        </member>
        <member name="M:Sentry.Http.HttpTransportBase.HandleResponseAsync(System.Net.Http.HttpResponseMessage,Sentry.Protocol.Envelopes.Envelope,System.Threading.CancellationToken)">
            <summary>
            Asynchronously handles the response message after it is received, extracting any information from the
            response such as rate limits, or error messages.
            </summary>
            <param name="response">The response message received from Sentry.</param>
            <param name="envelope">The envelope that was being sent.</param>
            <param name="cancellationToken">A cancellation token.</param>
        </member>
        <member name="M:Sentry.Http.HttpTransportBase.ReadStreamFromHttpContent(System.Net.Http.HttpContent)">
            <summary>
            Reads a stream from an HTTP content object.
            </summary>
            <param name="content">The HTTP content object to read from.</param>
            <returns>A stream of the content.</returns>
            <remarks>
            This is a helper method that allows higher-level APIs to serialize content synchronously
            without exposing our custom <see cref="T:Sentry.Internal.Http.EnvelopeHttpContent"/> type.
            </remarks>
        </member>
        <member name="T:Sentry.Http.ISentryHttpClientFactory">
            <summary>
            Sentry <see cref="T:System.Net.Http.HttpClient"/> factory.
            </summary>
        </member>
        <member name="M:Sentry.Http.ISentryHttpClientFactory.Create(Sentry.SentryOptions)">
            <summary>
            Creates an HttpClient using the specified options.
            </summary>
            <param name="options">The options.</param>
            <returns><see cref="T:System.Net.Http.HttpClient"/>.</returns>
        </member>
        <member name="T:Sentry.HttpStatusCodeRange">
            <summary>
            Holds a fully-inclusive range of HTTP status codes.
            e.g. Start = 500, End = 599 represents the range 500-599.
            </summary>
        </member>
        <member name="P:Sentry.HttpStatusCodeRange.Start">
            <summary>
            The inclusive start of the range.
            </summary>
        </member>
        <member name="P:Sentry.HttpStatusCodeRange.End">
            <summary>
            The inclusive end of the range.
            </summary>
        </member>
        <member name="M:Sentry.HttpStatusCodeRange.#ctor(System.Int32)">
            <summary>
            Creates a range that will only match a single value.
            </summary>
            <param name="statusCode">The value in the range.</param>
        </member>
        <member name="M:Sentry.HttpStatusCodeRange.#ctor(System.Int32,System.Int32)">
            <summary>
            Creates a range that will match all values between <paramref name="start"/> and <paramref name="end"/>.
            </summary>
            <param name="start">The inclusive start of the range.</param>
            <param name="end">The inclusive end of the range.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown if <paramref name="start"/> is greater than <paramref name="end"/>.
            </exception>
        </member>
        <member name="M:Sentry.HttpStatusCodeRange.op_Implicit(System.ValueTuple{System.Int32,System.Int32})~Sentry.HttpStatusCodeRange">
            <summary>
            Implicitly converts a tuple of ints to a <see cref="T:Sentry.HttpStatusCodeRange"/>.
            </summary>
            <param name="range">A tuple of ints to convert.</param>
        </member>
        <member name="M:Sentry.HttpStatusCodeRange.op_Implicit(System.Int32)~Sentry.HttpStatusCodeRange">
            <summary>
            Implicitly converts an int to a <see cref="T:Sentry.HttpStatusCodeRange"/>.
            </summary>
            <param name="statusCode">An int to convert.</param>
        </member>
        <member name="M:Sentry.HttpStatusCodeRange.op_Implicit(System.Net.HttpStatusCode)~Sentry.HttpStatusCodeRange">
            <summary>
            Implicitly converts an <see cref="T:System.Net.HttpStatusCode"/> to a <see cref="T:Sentry.HttpStatusCodeRange"/>.
            </summary>
            <param name="statusCode">A status code to convert.</param>
        </member>
        <member name="M:Sentry.HttpStatusCodeRange.op_Implicit(System.ValueTuple{System.Net.HttpStatusCode,System.Net.HttpStatusCode})~Sentry.HttpStatusCodeRange">
            <summary>
            Implicitly converts a tuple of <see cref="T:System.Net.HttpStatusCode"/> to a <see cref="T:Sentry.HttpStatusCodeRange"/>.
            </summary>
            <param name="range">A tuple of status codes to convert.</param>
        </member>
        <member name="M:Sentry.HttpStatusCodeRange.Contains(System.Int32)">
            <summary>
            Checks if a given status code is contained in the range.
            </summary>
            <param name="statusCode">Status code to check.</param>
            <returns>True if the range contains the given status code.</returns>
        </member>
        <member name="M:Sentry.HttpStatusCodeRange.Contains(System.Net.HttpStatusCode)">
            <summary>
            Checks if a given status code is contained in the range.
            </summary>
            <param name="statusCode">Status code to check.</param>
            <returns>True if the range contains the given status code.</returns>
        </member>
        <member name="T:Sentry.HubExtensions">
            <summary>
            Extension methods for <see cref="T:Sentry.IHub"/>.
            </summary>
        </member>
        <member name="M:Sentry.HubExtensions.StartTransaction(Sentry.IHub,Sentry.ITransactionContext)">
            <summary>
            Starts a transaction.
            </summary>
        </member>
        <member name="M:Sentry.HubExtensions.StartTransaction(Sentry.IHub,System.String,System.String)">
            <summary>
            Starts a transaction.
            </summary>
        </member>
        <member name="M:Sentry.HubExtensions.StartTransaction(Sentry.IHub,System.String,System.String,System.String)">
            <summary>
            Starts a transaction.
            </summary>
        </member>
        <member name="M:Sentry.HubExtensions.StartTransaction(Sentry.IHub,System.String,System.String,Sentry.SentryTraceHeader)">
            <summary>
            Starts a transaction from the specified trace header.
            </summary>
        </member>
        <member name="M:Sentry.HubExtensions.AddBreadcrumb(Sentry.IHub,System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String},Sentry.BreadcrumbLevel)">
            <summary>
            Adds a breadcrumb to the current scope.
            </summary>
            <param name="hub">The Hub which holds the scope stack.</param>
            <param name="message">The message.</param>
            <param name="category">Category.</param>
            <param name="type">Breadcrumb type.</param>
            <param name="data">Additional data.</param>
            <param name="level">Breadcrumb level.</param>
        </member>
        <member name="M:Sentry.HubExtensions.AddBreadcrumb(Sentry.IHub,Sentry.Infrastructure.ISystemClock,System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String},Sentry.BreadcrumbLevel)">
            <summary>
            Adds a breadcrumb using a custom <see cref="T:Sentry.Infrastructure.ISystemClock"/> which allows better testability.
            </summary>
            <param name="hub">The Hub which holds the scope stack.</param>
            <param name="clock">The system clock.</param>
            <param name="message">The message.</param>
            <param name="category">Category.</param>
            <param name="type">Breadcrumb type.</param>
            <param name="data">Additional data.</param>
            <param name="level">Breadcrumb level.</param>
            <remarks>
            This method is to be used by integrations to allow testing.
            </remarks>
        </member>
        <member name="M:Sentry.HubExtensions.AddBreadcrumb(Sentry.IHub,Sentry.Breadcrumb,Sentry.Hint)">
            <summary>
            Adds a breadcrumb to the current scope.
            </summary>
            <param name="hub">The Hub which holds the scope stack.</param>
            <param name="breadcrumb">The breadcrumb to add</param>
            <param name="hint">An hint provided with the breadcrumb in the BeforeBreadcrumb callback</param>
        </member>
        <member name="M:Sentry.HubExtensions.PushAndLockScope(Sentry.IHub)">
            <summary>
            Pushes a new scope while locking it which stop new scope creation.
            </summary>
        </member>
        <member name="M:Sentry.HubExtensions.LockScope(Sentry.IHub)">
            <summary>
            Lock the scope so subsequent <see cref="M:Sentry.ISentryScopeManager.PushScope"/> don't create new scopes.
            </summary>
            <remarks>
            This is useful to stop following scope creation by other integrations
            like Loggers which guarantee log messages are not lost.
            </remarks>
        </member>
        <member name="M:Sentry.HubExtensions.UnlockScope(Sentry.IHub)">
            <summary>
            Unlocks the current scope to allow subsequent calls to <see cref="M:Sentry.ISentryScopeManager.PushScope"/> create new scopes.
            </summary>
        </member>
        <member name="M:Sentry.HubExtensions.CaptureException(Sentry.IHub,System.Exception,System.Action{Sentry.Scope})">
            <summary>
            Captures the exception with a configurable scope callback.
            </summary>
            <param name="hub">The Sentry hub.</param>
            <param name="ex">The exception.</param>
            <param name="configureScope">The callback to configure the scope.</param>
            <returns>The Id of the event</returns>
        </member>
        <member name="M:Sentry.HubExtensions.CaptureMessage(Sentry.IHub,System.String,System.Action{Sentry.Scope},Sentry.SentryLevel)">
            <summary>
            Captures a message with a configurable scope callback.
            </summary>
            <param name="hub">The Sentry hub.</param>
            <param name="message">The message to send.</param>
            <param name="configureScope">The callback to configure the scope.</param>
            <param name="level">The message level.</param>
            <returns>The Id of the event</returns>
        </member>
        <member name="T:Sentry.IAttachmentContent">
            <summary>
            Attachment content.
            </summary>
        </member>
        <member name="M:Sentry.IAttachmentContent.GetStream">
            <summary>
            Gets the stream that represents attachment content.
            </summary>
        </member>
        <member name="T:Sentry.IEventLike">
            <summary>
            Models members common between types that represent event-like data.
            </summary>
        </member>
        <member name="P:Sentry.IEventLike.Level">
            <summary>
            Sentry level.
            </summary>
        </member>
        <member name="P:Sentry.IEventLike.Request">
            <summary>
            Gets or sets the HTTP.
            </summary>
            <value>
            The HTTP.
            </value>
        </member>
        <member name="P:Sentry.IEventLike.Contexts">
            <summary>
            Gets the structured Sentry context.
            </summary>
            <value>
            The contexts.
            </value>
        </member>
        <member name="P:Sentry.IEventLike.User">
            <summary>
            Gets the user information.
            </summary>
            <value>
            The user.
            </value>
        </member>
        <member name="P:Sentry.IEventLike.Platform">
            <summary>
            The name of the platform.
            </summary>
            <remarks>
            In most cases, the platform will be "csharp". However, it may differ if the event
            was generated from another embeded SDK.  For example, when targeting net6.0-android,
            events generated by the Sentry Android SDK will have the platform "java".
            </remarks>
        </member>
        <member name="P:Sentry.IEventLike.Release">
            <summary>
            The release version of the application.
            </summary>
        </member>
        <member name="P:Sentry.IEventLike.Environment">
            <summary>
            The environment name, such as 'production' or 'staging'.
            </summary>
            <remarks>Requires Sentry 8.0 or higher.</remarks>
        </member>
        <member name="P:Sentry.IEventLike.TransactionName">
            <summary>
            The name of the transaction in which there was an event.
            </summary>
            <remarks>
            A transaction should only be defined when it can be well defined.
            On a Web framework, for example, a transaction is the route template
            rather than the actual request path. That is so GET /user/10 and /user/20
            (which have route template /user/{id}) are identified as the same transaction.
            </remarks>
        </member>
        <member name="P:Sentry.IEventLike.Sdk">
            <summary>
            SDK information.
            </summary>
            <remarks>New in Sentry version: 8.4</remarks>
        </member>
        <member name="P:Sentry.IEventLike.Fingerprint">
            <summary>
            A list of strings used to dictate the deduplication of this event.
            </summary>
            <seealso href="https://docs.sentry.io/platforms/dotnet/data-management/event-grouping/grouping-enhancements/"/>
            <remarks>
            A value of {{ default }} will be replaced with the built-in behavior, thus allowing you to extend it, or completely replace it.
            New in version Protocol: version '7'
            </remarks>
            <example> { "fingerprint": ["myrpc", "POST", "/foo.bar"] } </example>
            <example> { "fingerprint": ["{{ default }}", "http://example.com/my.url"] } </example>
        </member>
        <member name="T:Sentry.EventLikeExtensions">
            <summary>
            Extensions for <see cref="T:Sentry.IEventLike"/>.
            </summary>
        </member>
        <member name="M:Sentry.EventLikeExtensions.HasUser(Sentry.IEventLike)">
            <summary>
            Whether a <see cref="T:Sentry.User"/> has been set to the object with any of its fields non null.
            </summary>
        </member>
        <member name="M:Sentry.EventLikeExtensions.SetFingerprint(Sentry.IEventLike,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets the fingerprint to the object.
            </summary>
        </member>
        <member name="M:Sentry.EventLikeExtensions.SetFingerprint(Sentry.IEventLike,System.String[])">
            <summary>
            Sets the fingerprint to the object.
            </summary>
        </member>
        <member name="T:Sentry.IHasBreadcrumbs">
            <summary>
            Implemented by objects that contain breadcrumbs.
            </summary>
        </member>
        <member name="P:Sentry.IHasBreadcrumbs.Breadcrumbs">
            <summary>
            A trail of events which happened prior to an issue.
            </summary>
            <seealso href="https://docs.sentry.io/platforms/dotnet/enriching-events/breadcrumbs/"/>
        </member>
        <member name="M:Sentry.IHasBreadcrumbs.AddBreadcrumb(Sentry.Breadcrumb)">
            <summary>
            Adds a breadcrumb.
            </summary>
        </member>
        <member name="T:Sentry.HasBreadcrumbsExtensions">
            <summary>
            Extensions for <see cref="T:Sentry.IHasBreadcrumbs"/>.
            </summary>
        </member>
        <member name="M:Sentry.HasBreadcrumbsExtensions.AddBreadcrumb(Sentry.IHasBreadcrumbs,System.String,System.String,System.String,System.Nullable{System.ValueTuple{System.String,System.String}},Sentry.BreadcrumbLevel)">
            <summary>
            Adds a breadcrumb to the object.
            </summary>
            <param name="hasBreadcrumbs">The object.</param>
            <param name="message">The message.</param>
            <param name="category">The category.</param>
            <param name="type">The type.</param>
            <param name="dataPair">The data key-value pair.</param>
            <param name="level">The level.</param>
        </member>
        <member name="M:Sentry.HasBreadcrumbsExtensions.AddBreadcrumb(Sentry.IHasBreadcrumbs,System.String,System.String,System.String,System.Collections.Generic.IReadOnlyDictionary{System.String,System.String},Sentry.BreadcrumbLevel)">
            <summary>
            Adds a breadcrumb to the object.
            </summary>
            <param name="hasBreadcrumbs">The object.</param>
            <param name="message">The message.</param>
            <param name="category">The category.</param>
            <param name="type">The type.</param>
            <param name="data">The data.</param>
            <param name="level">The level.</param>
        </member>
        <member name="M:Sentry.HasBreadcrumbsExtensions.AddBreadcrumb(Sentry.IHasBreadcrumbs,System.Nullable{System.DateTimeOffset},System.String,System.String,System.String,System.Collections.Generic.IReadOnlyDictionary{System.String,System.String},Sentry.BreadcrumbLevel)">
            <summary>
            Adds a breadcrumb to the object.
            </summary>
            <remarks>
            This overload is used for testing.
            </remarks>
            <param name="hasBreadcrumbs">The object.</param>
            <param name="timestamp">The timestamp</param>
            <param name="message">The message.</param>
            <param name="category">The category.</param>
            <param name="type">The type.</param>
            <param name="data">The data</param>
            <param name="level">The level.</param>
        </member>
        <member name="T:Sentry.IHasExtra">
            <summary>
            Implemented by objects that contain a map of untyped extra data.
            </summary>
        </member>
        <member name="P:Sentry.IHasExtra.Extra">
            <summary>
            An arbitrary mapping of additional metadata to store with the event.
            </summary>
        </member>
        <member name="M:Sentry.IHasExtra.SetExtra(System.String,System.Object)">
            <summary>
            Sets an extra.
            </summary>
        </member>
        <member name="T:Sentry.HasExtraExtensions">
            <summary>
            Extensions for <see cref="T:Sentry.IHasExtra"/>.
            </summary>
        </member>
        <member name="M:Sentry.HasExtraExtensions.SetExtras(Sentry.IHasExtra,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
            Sets the extra key-value pairs to the object.
            </summary>
        </member>
        <member name="T:Sentry.IHasMeasurements">
            <summary>
            Interface for transactions that can keep track of measurements.
            </summary>
            <remarks>
            Ideally, this would just be implemented as part of <see cref="T:Sentry.ITransactionData"/>.
            However, adding a property to a public interface is a breaking change.  We can do that in a future major version.
            </remarks>
        </member>
        <member name="P:Sentry.IHasMeasurements.Measurements">
            <summary>
            The measurements that have been set on the transaction.
            </summary>
        </member>
        <member name="M:Sentry.IHasMeasurements.SetMeasurement(System.String,Sentry.Protocol.Measurement)">
            <summary>
            Sets a measurement on the transaction.
            </summary>
            <param name="name">The name of the measurement.</param>
            <param name="measurement">The measurement.</param>
        </member>
        <member name="T:Sentry.MeasurementExtensions">
            <summary>
            Extensions for <see cref="T:Sentry.IHasMeasurements"/>
            </summary>
        </member>
        <member name="M:Sentry.MeasurementExtensions.SetMeasurement(Sentry.ITransactionData,System.String,System.Int32,Sentry.MeasurementUnit)">
            <summary>
            Sets a measurement on the transaction.
            </summary>
            <param name="transaction">The transaction.</param>
            <param name="name">The name of the measurement.</param>
            <param name="value">The value of the measurement.</param>
            <param name="unit">The optional unit of the measurement.</param>
        </member>
        <member name="M:Sentry.MeasurementExtensions.SetMeasurement(Sentry.ITransactionData,System.String,System.Int64,Sentry.MeasurementUnit)">
            <inheritdoc cref="M:Sentry.MeasurementExtensions.SetMeasurement(Sentry.ITransactionData,System.String,System.Int32,Sentry.MeasurementUnit)" />
        </member>
        <member name="M:Sentry.MeasurementExtensions.SetMeasurement(Sentry.ITransactionData,System.String,System.UInt64,Sentry.MeasurementUnit)">
            <inheritdoc cref="M:Sentry.MeasurementExtensions.SetMeasurement(Sentry.ITransactionData,System.String,System.Int32,Sentry.MeasurementUnit)" />
        </member>
        <member name="M:Sentry.MeasurementExtensions.SetMeasurement(Sentry.ITransactionData,System.String,System.Double,Sentry.MeasurementUnit)">
            <inheritdoc cref="M:Sentry.MeasurementExtensions.SetMeasurement(Sentry.ITransactionData,System.String,System.Int32,Sentry.MeasurementUnit)" />
        </member>
        <member name="T:Sentry.IHasTags">
            <summary>
            Implemented by objects that contain a map of tags.
            </summary>
        </member>
        <member name="P:Sentry.IHasTags.Tags">
            <summary>
            Arbitrary key-value for this event.
            </summary>
        </member>
        <member name="M:Sentry.IHasTags.SetTag(System.String,System.String)">
            <summary>
            Sets a tag.
            </summary>
        </member>
        <member name="M:Sentry.IHasTags.UnsetTag(System.String)">
            <summary>
            Removes a tag.
            </summary>
        </member>
        <member name="T:Sentry.HasTagsExtensions">
            <summary>
            Extensions for <see cref="T:Sentry.IHasTags"/>.
            </summary>
        </member>
        <member name="M:Sentry.HasTagsExtensions.SetTags(Sentry.IHasTags,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Set all items as tags.
            </summary>
        </member>
        <member name="T:Sentry.IHasTransactionNameSource">
            <summary>
            Interface for transactions that implement a transaction name source.
            </summary>
            <remarks>
            Ideally, this would just be implemented as part of <see cref="T:Sentry.ITransaction"/> and <see cref="T:Sentry.ITransactionContext"/>.
            However, adding a property to a public interface is a breaking change.  We can do that in a future major version.
            </remarks>
        </member>
        <member name="P:Sentry.IHasTransactionNameSource.NameSource">
            <summary>
            The source of the transaction name.
            </summary>
        </member>
        <member name="T:Sentry.IHub">
            <summary>
            SDK API contract which combines a client and scope management.
            </summary>
            <remarks>
            The contract of which <see cref="T:Sentry.SentrySdk" /> exposes statically.
            This interface exist to allow better testability of integrations which otherwise
            would require dependency to the static <see cref="T:Sentry.SentrySdk" />.
            </remarks>
            <inheritdoc cref="T:Sentry.ISentryClient" />
            <inheritdoc cref="T:Sentry.ISentryScopeManager" />
        </member>
        <member name="P:Sentry.IHub.LastEventId">
            <summary>
            Last event id recorded in the current scope.
            </summary>
        </member>
        <member name="M:Sentry.IHub.StartTransaction(Sentry.ITransactionContext,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Starts a transaction.
            </summary>
        </member>
        <member name="M:Sentry.IHub.BindException(System.Exception,Sentry.ISpan)">
            <summary>
            Binds specified exception the specified span.
            </summary>
            <remarks>
            This method is used internally and is not meant for public use.
            </remarks>
        </member>
        <member name="M:Sentry.IHub.GetSpan">
            <summary>
            Gets the currently ongoing (not finished) span or <code>null</code> if none available.
            </summary>
        </member>
        <member name="M:Sentry.IHub.GetTraceHeader">
            <summary>
            Gets the Sentry trace header that allows tracing across services
            </summary>
        </member>
        <member name="M:Sentry.IHub.GetBaggage">
            <summary>
            Gets the Sentry baggage header that allows tracing across services
            </summary>
        </member>
        <member name="M:Sentry.IHub.ContinueTrace(System.String,System.String,System.String,System.String)">
            <summary>
            Continues a trace based on HTTP header values provided as strings.
            </summary>
            <remarks>
            If no "sentry-trace" header is provided a random trace ID and span ID is created.
            </remarks>
        </member>
        <member name="M:Sentry.IHub.ContinueTrace(Sentry.SentryTraceHeader,Sentry.BaggageHeader,System.String,System.String)">
            <summary>
            Continues a trace based on HTTP header values.
            </summary>
            <remarks>
            If no "sentry-trace" header is provided a random trace ID and span ID is created.
            </remarks>
        </member>
        <member name="M:Sentry.IHub.StartSession">
            <summary>
            Starts a new session.
            </summary>
        </member>
        <member name="M:Sentry.IHub.PauseSession">
            <summary>
            Pauses an active session.
            </summary>
        </member>
        <member name="M:Sentry.IHub.ResumeSession">
            <summary>
            Resumes an active session.
            If the session has been paused for longer than the duration of time specified in
            <see cref="P:Sentry.SentryOptions.AutoSessionTrackingInterval"/> then the paused session is
            ended and a new one is started instead.
            </summary>
        </member>
        <member name="M:Sentry.IHub.EndSession(Sentry.SessionEndStatus)">
            <summary>
            Ends the currently active session.
            </summary>
        </member>
        <member name="M:Sentry.IHub.CaptureEvent(Sentry.SentryEvent,System.Action{Sentry.Scope})">
            <summary>
            Captures an event with a configurable scope.
            </summary>
            <remarks>
            This allows modifying a scope without affecting other events.
            </remarks>
            <param name="evt">The event to be captured.</param>
            <param name="configureScope">The callback to configure the scope.</param>
            <returns></returns>
        </member>
        <member name="T:Sentry.IJsonSerializable">
            <summary>
            Sentry JsonSerializable.
            </summary>
        </member>
        <member name="M:Sentry.IJsonSerializable.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <summary>
            Writes the object as JSON.
            </summary>
            <remarks>
            Note: this method is meant only for internal use and is exposed due to a language limitation.
            Avoid relying on this method in user code.
            </remarks>
        </member>
        <member name="T:Sentry.Infrastructure.ConsoleDiagnosticLogger">
            <summary>
            Console logger used by the SDK to report its internal logging.
            </summary>
            <remarks>
            The default logger, usually replaced by a higher level logging adapter like Microsoft.Extensions.Logging.
            </remarks>
        </member>
        <member name="M:Sentry.Infrastructure.ConsoleDiagnosticLogger.#ctor(Sentry.SentryLevel)">
            <summary>
            Creates a new instance of <see cref="T:Sentry.Infrastructure.ConsoleDiagnosticLogger"/>.
            </summary>
        </member>
        <member name="M:Sentry.Infrastructure.ConsoleDiagnosticLogger.LogMessage(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Sentry.Infrastructure.DebugDiagnosticLogger">
            <summary>
            Debug logger used by the SDK to report its internal logging.
            </summary>
            <remarks>
            Logger available when compiled in Debug mode. It's useful when debugging apps running under IIS which have no output to Console logger.
            </remarks>
        </member>
        <member name="M:Sentry.Infrastructure.DebugDiagnosticLogger.#ctor(Sentry.SentryLevel)">
            <summary>
            Creates a new instance of <see cref="T:Sentry.Infrastructure.DebugDiagnosticLogger"/>.
            </summary>
        </member>
        <member name="M:Sentry.Infrastructure.DebugDiagnosticLogger.LogMessage(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Sentry.Infrastructure.DiagnosticLogger">
            <summary>
            Base class for diagnostic loggers.
            </summary>
        </member>
        <member name="M:Sentry.Infrastructure.DiagnosticLogger.#ctor(Sentry.SentryLevel)">
            <summary>
            Creates a new instance of <see cref="T:Sentry.Infrastructure.DiagnosticLogger"/>.
            </summary>
        </member>
        <member name="M:Sentry.Infrastructure.DiagnosticLogger.IsEnabled(Sentry.SentryLevel)">
            <summary>
            Whether the logger is enabled to the defined level.
            </summary>
        </member>
        <member name="M:Sentry.Infrastructure.DiagnosticLogger.Log(Sentry.SentryLevel,System.String,System.Exception,System.Object[])">
            <summary>
            Log message with level, exception and parameters.
            </summary>
        </member>
        <member name="M:Sentry.Infrastructure.DiagnosticLogger.LogMessage(System.String)">
            <summary>
            Writes a formatted message to the log.
            </summary>
            <param name="message">The complete message, ready to be logged.</param>
        </member>
        <member name="T:Sentry.Infrastructure.FileDiagnosticLogger">
            <summary>
            File logger used by the SDK to report its internal logging to a file.
            </summary>
            <remarks>
            Primarily used to capture debug information to troubleshoot the SDK itself.
            </remarks>
        </member>
        <member name="M:Sentry.Infrastructure.FileDiagnosticLogger.#ctor(System.String,System.Boolean)">
            <summary>
            Creates a new instance of <see cref="T:Sentry.Infrastructure.FileDiagnosticLogger"/>.
            </summary>
            <param name="path">The path to the file to write logs to.  Will overwrite any existing file.</param>
            <param name="alsoWriteToConsole">If <c>true</c>, will write to the console as well as the file.</param>
        </member>
        <member name="M:Sentry.Infrastructure.FileDiagnosticLogger.#ctor(System.String,Sentry.SentryLevel,System.Boolean)">
            <summary>
            Creates a new instance of <see cref="T:Sentry.Infrastructure.FileDiagnosticLogger"/>.
            </summary>
            <param name="path">The path to the file to write logs to.  Will overwrite any existing file.</param>
            <param name="minimalLevel">The minimal level to log.</param>
            <param name="alsoWriteToConsole">If <c>true</c>, will write to the console as well as the file.</param>
        </member>
        <member name="M:Sentry.Infrastructure.FileDiagnosticLogger.LogMessage(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Sentry.Infrastructure.ISystemClock">
            <summary>
            An abstraction to the system clock.
            </summary>
            <remarks>
            Agree to disagree with closing this: https://github.com/aspnet/Common/issues/151
            </remarks>
        </member>
        <member name="M:Sentry.Infrastructure.ISystemClock.GetUtcNow">
            <summary>
            Gets the current time in UTC.
            </summary>
        </member>
        <member name="T:Sentry.Infrastructure.SystemClock">
            <summary>
            Implementation of <see cref="T:Sentry.Infrastructure.ISystemClock"/> to help testability.
            </summary>
            <seealso cref="T:Sentry.Infrastructure.ISystemClock" />
        </member>
        <member name="M:Sentry.Infrastructure.SystemClock.#ctor">
            <summary>
            Constructs a SystemClock instance.
            </summary>
            <remarks>
            This constructor should have been private originally.  It will be removed in a future major version.
            </remarks>
        </member>
        <member name="F:Sentry.Infrastructure.SystemClock.Clock">
            <summary>
            System clock singleton.
            </summary>
        </member>
        <member name="M:Sentry.Infrastructure.SystemClock.GetUtcNow">
            <summary>
            Gets the current time in UTC.
            </summary>
            <remarks>
            Used for testability, calls: DateTimeOffset.UtcNow
            </remarks>
        </member>
        <member name="T:Sentry.Infrastructure.TraceDiagnosticLogger">
            <summary>
            Trace logger used by the SDK to report its internal logging.
            </summary>
            <remarks>
            Logger available when hooked to an IDE. It's useful when debugging apps running under IIS which have no output to Console logger.
            </remarks>
        </member>
        <member name="M:Sentry.Infrastructure.TraceDiagnosticLogger.#ctor(Sentry.SentryLevel)">
            <summary>
            Creates a new instance of <see cref="T:Sentry.Infrastructure.TraceDiagnosticLogger"/>.
            </summary>
        </member>
        <member name="M:Sentry.Infrastructure.TraceDiagnosticLogger.LogMessage(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Sentry.Instrumenter">
            <summary>
            Describes which approach is used to create spans.
            </summary>
        </member>
        <member name="F:Sentry.Instrumenter.Sentry">
            <summary>
            Spans are instrumented via the Sentry SDK.
            </summary>
        </member>
        <member name="F:Sentry.Instrumenter.OpenTelemetry">
            <summary>
            Spans are instrumented via OpenTelemetry.
            </summary>
        </member>
        <member name="T:Sentry.Integrations.ISdkIntegration">
            <summary>
            An SDK Integration.
            </summary>
        </member>
        <member name="M:Sentry.Integrations.ISdkIntegration.Register(Sentry.IHub,Sentry.SentryOptions)">
            <summary>
            Registers this integration with the hub.
            </summary>
            <remarks>
            This method is invoked when the Hub is created.
            </remarks>
            <param name="hub">The hub.</param>
            <param name="options">The options.</param>
        </member>
        <member name="M:Sentry.Internal.BackgroundWorker.EnqueueEnvelope(Sentry.Protocol.Envelopes.Envelope)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Internal.BackgroundWorker.EnqueueEnvelope(Sentry.Protocol.Envelopes.Envelope,System.Boolean)">
            <summary>
            Attempts to enqueue the envelope with the worker.
            </summary>
            <param name="envelope">The envelope to enqueue.</param>
            <param name="process">
            Whether to process the next item in the queue after enqueuing this item,
            which may or may not be the item being enqueued.  The default is <c>true</c>.
            Pass <c>false</c> for testing, when you want to add items to the queue without unblocking the worker.
            After items are enqueued, use <see cref="M:Sentry.Internal.BackgroundWorker.ProcessQueuedItems(System.Int32)"/> to unblock the worker to process the items.
            Disposing the worker will also unblock the items, which will then be processed until the shutdown timeout
            is reached or the queue is emptied.
            </param>
            <returns>True of queueing was successful. Otherwise, false.</returns>
        </member>
        <member name="M:Sentry.Internal.BackgroundWorker.ProcessQueuedItems(System.Int32)">
            <summary>
            Processes the number of queued items specified.
            Used only in testing, after calling <see cref="M:Sentry.Internal.BackgroundWorker.EnqueueEnvelope(Sentry.Protocol.Envelopes.Envelope,System.Boolean)"/>
            when passing <c>process: false</c>.
            </summary>
            <param name="count">The number of items to process from the queue.</param>
        </member>
        <member name="M:Sentry.Internal.BackgroundWorker.Dispose">
            <summary>
            Stops the background worker and waits for it to empty the queue until 'shutdownTimeout' is reached
            </summary>
            <inheritdoc />
        </member>
        <member name="M:Sentry.Internal.ClientReport.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses <see cref="T:Sentry.Internal.ClientReport"/> from <paramref name="json"/>.
            </summary>
        </member>
        <member name="T:Sentry.Internal.Constants">
            <summary>
            Internal Constant Values.
            </summary>
        </member>
        <member name="F:Sentry.Internal.Constants.DsnEnvironmentVariable">
            <summary>
            Sentry DSN environment variable.
            </summary>
        </member>
        <member name="F:Sentry.Internal.Constants.ReleaseEnvironmentVariable">
            <summary>
            Sentry release environment variable.
            </summary>
        </member>
        <member name="F:Sentry.Internal.Constants.EnvironmentEnvironmentVariable">
            <summary>
            Sentry environment, environment variable.
            </summary>
        </member>
        <member name="F:Sentry.Internal.Constants.ProductionEnvironmentSetting">
            <summary>
            Default Sentry environment setting.
            </summary>
            <remarks>Best Sentry practice is to always try and have a value for this setting.</remarks>
        </member>
        <member name="T:Sentry.Internal.DebugStackTrace">
            <summary>
            Sentry Stacktrace with debug images.
            </summary>
        </member>
        <member name="M:Sentry.Internal.DebugStackTrace.CreateFrames(System.Diagnostics.StackTrace,System.Boolean)">
            <summary>
            Creates an enumerator of <see cref="T:Sentry.SentryStackFrame"/> from a <see cref="T:System.Diagnostics.StackTrace"/>.
            </summary>
        </member>
        <member name="M:Sentry.Internal.DebugStackTrace.CreateFrame(System.Diagnostics.StackFrame)">
            <summary>
            Create a <see cref="T:Sentry.SentryStackFrame"/> from a <see cref="T:System.Diagnostics.StackFrame"/>.
            </summary>
        </member>
        <member name="M:Sentry.Internal.DebugStackTrace.InternalCreateFrame(System.Diagnostics.StackFrame,System.Boolean)">
            <summary>
            Default the implementation of CreateFrame.
            </summary>
        </member>
        <member name="M:Sentry.Internal.DebugStackTrace.DemangleAsyncFunctionName(Sentry.SentryStackFrame)">
            <summary>
            Clean up function and module names produced from `async` state machine calls.
            </summary>
            <para>
            When the Microsoft cs.exe compiler compiles some modern C# features,
            such as async/await calls, it can create synthetic function names that
            do not match the function names in the original source code. Here we
            reverse some of these transformations, so that the function and module
            names that appears in the Sentry UI will match the function and module
            names in the original source-code.
            </para>
        </member>
        <member name="M:Sentry.Internal.DebugStackTrace.DemangleAnonymousFunction(Sentry.SentryStackFrame)">
            <summary>
            Clean up function names for anonymous lambda calls.
            </summary>
        </member>
        <member name="M:Sentry.Internal.DebugStackTrace.DemangleLambdaReturnType(Sentry.SentryStackFrame)">
            <summary>
            Remove return type from module in a Task with a Lambda with a return value.
            This was seen in Unity, see https://github.com/getsentry/sentry-unity/issues/845
            </summary>
        </member>
        <member name="M:Sentry.Internal.Extensions.MiscExtensions.IsNull(System.Object)">
            <summary>
            Determines whether an object is <c>null</c>.
            </summary>
            <remarks>
            This method exists so that we can test for null in situations where a method might be called from
            code that ignores nullability warnings.
            (It prevents us having to have two different resharper ignore comments depending on target framework.)
            </remarks>
        </member>
        <member name="T:Sentry.Internal.Extensions.PEDebugImageData">
            <summary>
            The subset of information about a DebugImage that we can obtain from a PE file.
            This rest of the information is obtained from the Module.
            </summary>
        </member>
        <member name="M:Sentry.Internal.Extensions.StringExtensions.NullIfWhitespace(System.String)">
            <summary>
            Returns <c>null</c> if <paramref name="str"/> is <c>null</c> or contains only whitespace.
            Otherwise, returns <paramref name="str"/>.
            </summary>
        </member>
        <member name="T:Sentry.Internal.HashableGrowableArray`1">
            <summary>
            A GrowableArray that can be used as a key in a Dictionary.
            Note: it must be Seal()-ed before used as a key and can't be changed afterwards.
            </summary>
        </member>
        <member name="M:Sentry.Internal.HashableGrowableArray`1.Seal">
            <summary>
            Seal this array so that it cannot be changed anymore and can be hashed.
            </summary>
        </member>
        <member name="M:Sentry.Internal.HashableGrowableArray`1.Trim(System.Int32)">
            <summary>
            Trims the size of the array so that no more than 'maxWaste' slots are wasted.
            You can call this even on Seal()'ed array because it doesn't affect the content and thus the hash code.
            </summary>
        </member>
        <member name="T:Sentry.Internal.GrowableArray`1">
            <summary>
            A cheap version of List(T). The idea is to make it as cheap as if you did it 'by hand' using an array and
            an int which represents the logical charCount. It is a struct to avoid an extra pointer dereference, so this
            is really meant to be embedded in other structures.
            </summary>
            <remarks>
            Adapted to null-safety from the original version licensed under MIT and located at:
            https://github.com/microsoft/perfview/blob/050c303943e74ff51ce584b2717e578d96684e85/src/FastSerialization/GrowableArray.cs
            </remarks>
        </member>
        <member name="M:Sentry.Internal.GrowableArray`1.#ctor(System.Int32)">
            <summary>
            Create a growable array with the given initial size it will grow as needed.
            </summary>
            <param name="initialSize"></param>
        </member>
        <member name="P:Sentry.Internal.GrowableArray`1.Item(System.Int32)">
            <summary>
            Fetch the element at the given index which must be lower than `Count`.
            </summary>
        </member>
        <member name="P:Sentry.Internal.GrowableArray`1.Count">
            <summary>
            The number of elements in the array
            </summary>
        </member>
        <member name="M:Sentry.Internal.GrowableArray`1.Clear">
            <summary>
            Remove all elements in the array.
            </summary>
        </member>
        <member name="M:Sentry.Internal.GrowableArray`1.Add(`0)">
            <summary>
            Add an item at the end of the array, growing as necessary.
            </summary>
            <param name="item"></param>
        </member>
        <member name="M:Sentry.Internal.GrowableArray`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Add all items 'items' to the end of the array, growing as necessary.
            </summary>
            <param name="items"></param>
        </member>
        <member name="M:Sentry.Internal.GrowableArray`1.Insert(System.Int32,`0)">
            <summary>
            Insert 'item' directly at 'index', shifting all items >= index up.  'index' can be code:Count in
            which case the item is appended to the end.  Larger indexes are not allowed.
            </summary>
        </member>
        <member name="M:Sentry.Internal.GrowableArray`1.RemoveRange(System.Int32,System.Int32)">
            <summary>
            Remove 'count' elements starting at 'index'
            </summary>
        </member>
        <member name="P:Sentry.Internal.GrowableArray`1.Empty">
            <summary>
            Returns true if there are no elements in the array.
            </summary>
        </member>
        <member name="M:Sentry.Internal.GrowableArray`1.Trim(System.Int32)">
            <summary>
            Trims the size of the array so that no more than 'maxWaste' slots are wasted.   Useful when
            you know that the array has stopped growing.
            </summary>
        </member>
        <member name="P:Sentry.Internal.GrowableArray`1.EmptyCapacity">
            <summary>
            Returns true if the Growable array was initialized by the default constructor
            which has no capacity (and thus will cause growth on the first addition).
            This method allows you to lazily set the compacity of your GrowableArray by
            testing if it is of EmtpyCapacity, and if so set it to some useful capacity.
            This avoids unnecessary reallocs to get to a reasonable capacity.
            </summary>
        </member>
        <member name="M:Sentry.Internal.GrowableArray`1.ToString">
            <summary>
            A string representing the array. Only intended for debugging.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Sentry.Internal.GrowableArray`1.Foreach``1(System.Func{`0,``0})">
            <summary>
            Executes 'func' for each element in the GrowableArray and returns a GrowableArray
            for the result.
            </summary>
        </member>
        <member name="M:Sentry.Internal.GrowableArray`1.Search``1(``0,System.Int32,System.Func{``0,`0,System.Int32},System.Int32@)">
            <summary>
            Perform a linear search starting at 'startIndex'.  If found return true and the index in 'index'.
            It is legal that 'startIndex' is greater than the charCount, in which case, the search returns false
            immediately.   This allows a nice loop to find all items matching a pattern.
            </summary>
        </member>
        <member name="P:Sentry.Internal.GrowableArray`1.UnderlyingArray">
            <summary>
            Returns the underlying array.  Should not be used most of the time!
            </summary>
        </member>
        <member name="T:Sentry.Internal.GrowableArray`1.GrowableArrayEnumerator">
            <summary>
            IEnumerator implementation.
            </summary>
        </member>
        <member name="T:Sentry.Internal.Http.DefaultSentryHttpClientFactory">
            <summary>
            Default Sentry HttpClientFactory
            </summary>
            <inheritdoc />
        </member>
        <member name="M:Sentry.Internal.Http.DefaultSentryHttpClientFactory.Create(Sentry.SentryOptions)">
            <summary>
            Creates an <see cref="T:System.Net.Http.HttpClient" /> configure to call Sentry for the specified <see cref="T:Sentry.Dsn" />
            </summary>
            <param name="options">The HTTP options.</param>
        </member>
        <member name="T:Sentry.Internal.Http.GzipBufferedRequestBodyHandler">
            <summary>
            Compresses the body of an HTTP request with GZIP while buffering the result.
            </summary>
            <remarks>
            This handler doesn't use 'Content-Encoding: chunked' as it sets the 'Content-Length' of the request.
            </remarks>
            <inheritdoc />
        </member>
        <member name="M:Sentry.Internal.Http.GzipBufferedRequestBodyHandler.#ctor(System.Net.Http.HttpMessageHandler,System.IO.Compression.CompressionLevel)">
            <summary>
            Creates a new instance of <see cref="T:Sentry.Internal.Http.GzipBufferedRequestBodyHandler" />.
            </summary>
            <param name="innerHandler">The actual handler which handles the request.</param>
            <param name="compressionLevel">The compression level to use.</param>
            <exception cref="T:System.InvalidOperationException">Constructing this type with <see cref="T:System.IO.Compression.CompressionLevel" />
            of value <see cref="F:System.IO.Compression.CompressionLevel.NoCompression" /> is an invalid operation.</exception>
            <inheritdoc />
        </member>
        <member name="M:Sentry.Internal.Http.GzipBufferedRequestBodyHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
            <summary>
            Compresses the request body and sends a request with a buffered stream.
            </summary>
            <param name="request">The HTTP request to compress.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <inheritdoc />
        </member>
        <member name="T:Sentry.Internal.Http.GzipRequestBodyHandler">
            <summary>
            Compresses the body of an HTTP request with GZIP.
            </summary>
            <inheritdoc />
        </member>
        <member name="M:Sentry.Internal.Http.GzipRequestBodyHandler.#ctor(System.Net.Http.HttpMessageHandler,System.IO.Compression.CompressionLevel)">
            <summary>
            Creates a new instance of <see cref="T:Sentry.Internal.Http.GzipRequestBodyHandler" />.
            </summary>
            <param name="innerHandler">The actual handler which handles the request.</param>
            <param name="compressionLevel">The compression level to use.</param>
            <exception cref="T:System.InvalidOperationException">Constructing this type with <see cref="T:System.IO.Compression.CompressionLevel" />
            of value <see cref="F:System.IO.Compression.CompressionLevel.NoCompression" /> is an invalid operation.</exception>
            <inheritdoc />
        </member>
        <member name="M:Sentry.Internal.Http.GzipRequestBodyHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
            <summary>
            Sends the request while compressing its payload.
            </summary>
            <param name="request">The HTTP request to compress.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <inheritdoc />
        </member>
        <member name="M:Sentry.Internal.Http.HttpTransport.SendEnvelopeAsync(Sentry.Protocol.Envelopes.Envelope,System.Threading.CancellationToken)">
            <summary>
            Sends an envelope over this transport.
            </summary>
            <param name="envelope">The envelope to send.</param>
            <param name="cancellationToken">A cancellation token.</param>
            <remarks>
            This method implements the overarching workflow, but all features are implemented in the base class
            such that they can be shared with higher-level SDKs (such as Unity) that may implement their own method
            for performing HTTP transport.
            </remarks>
        </member>
        <member name="T:Sentry.Internal.Http.RetryAfterHandler">
            <summary>
            Retry After Handler which short-circuit requests following an HTTP 429.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc6585#section-4" />
            <seealso href="https://develop.sentry.dev/sdk/overview/#writing-an-sdk"/>
            <inheritdoc />
        </member>
        <member name="M:Sentry.Internal.Http.RetryAfterHandler.#ctor(System.Net.Http.HttpMessageHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sentry.Internal.Http.RetryAfterHandler"/> class.
            </summary>
            <param name="innerHandler">The inner handler which is responsible for processing the HTTP response messages.</param>
        </member>
        <member name="M:Sentry.Internal.Http.RetryAfterHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
            <summary>
            Sends an HTTP request to the inner handler while verifying the Response status code for HTTP 429.
            </summary>
            <param name="request">The HTTP request message to send to the server.</param>
            <param name="cancellationToken">A cancellation token to cancel operation.</param>
            <returns>
            The task object representing the asynchronous operation.
            </returns>
            <inheritdoc />
        </member>
        <member name="M:Sentry.Internal.IClientReportRecorder.RecordDiscardedEvent(Sentry.Internal.DiscardReason,Sentry.Internal.DataCategory)">
            <summary>
            Records one count of a discarded event, with the given <paramref name="reason"/> and <paramref name="category"/>.
            </summary>
            <param name="reason">The reason for the event being discarded.</param>
            <param name="category">The data category of the event being discarded.</param>
        </member>
        <member name="M:Sentry.Internal.IClientReportRecorder.GenerateClientReport">
            <summary>
            Generates a <see cref="T:Sentry.Internal.ClientReport"/> containing counts of discarded events that have been recorded.
            Also resets those counts to zero at the same time the report is generated.
            </summary>
            <returns>
            The <see cref="T:Sentry.Internal.ClientReport"/>, as long as there is something to report.
            Returns <c>null</c> if there were no discarded events recorded since the previous call to this method.
            </returns>
        </member>
        <member name="M:Sentry.Internal.IClientReportRecorder.Load(Sentry.Internal.ClientReport)">
            <summary>
            Loads the current instance with the events from the provided <paramref name="report"/>.
            </summary>
            <remarks>
            Useful when recovering from failures while sending client reports.
            </remarks>
            <param name="report">The client report to load.</param>
        </member>
        <member name="P:Sentry.Internal.IHasDistribution.Distribution">
            <summary>
            The release distribution of the application.
            </summary>
        </member>
        <member name="M:Sentry.Internal.ILSpy.ModuleExtensions.GetNameOrScopeName(System.Reflection.Module)">
            <summary>
            The Module.Name for Modules that are embedded in SingleFileApps will be null
            or &lt;Unknown&gt;, in that case we can use Module.ScopeName instead
            </summary>
            <param name="module">A Module instance</param>
            <returns>module.Name, if this is available. module.ScopeName otherwise</returns>
        </member>
        <member name="T:Sentry.Internal.ITransactionProfilerFactory">
            <summary>
            Factory to create/attach profilers when a transaction starts.
            </summary>
        </member>
        <member name="M:Sentry.Internal.ITransactionProfilerFactory.Start(Sentry.ITransaction,System.Threading.CancellationToken)">
            <summary>
            Called during transaction start to start a new profiler, if applicable.
            </summary>
        </member>
        <member name="T:Sentry.Internal.ITransactionProfiler">
            <summary>
            A profiler collecting ProfileInfo for a given transaction.
            </summary>
        </member>
        <member name="M:Sentry.Internal.ITransactionProfiler.Finish">
            <summary>
            Called when the transaction ends - this should stop profile samples collection.
            </summary>
        </member>
        <member name="M:Sentry.Internal.ITransactionProfiler.CollectAsync(Sentry.Transaction)">
            <summary>
            Process and collect the profile.
            </summary>
        </member>
        <member name="T:Sentry.Internal.JsonConverters.SentryJsonConverter">
            <summary>
            A converter that removes dangerous classes from being serialized,
            and, also formats some classes like Exception and Type.
            </summary>
        </member>
        <member name="T:Sentry.Internal.NoOpSpan">
            <summary>
            Span class to use when we can't return null but a request to create a span couldn't be completed.
            </summary>
        </member>
        <member name="T:Sentry.Internal.NoOpTransaction">
            <summary>
            Transaction class to use when we can't return null but a request to create a transaction couldn't be completed.
            </summary>
        </member>
        <member name="T:Sentry.Internal.ObjectExtensions">
            <summary>
            Copied/Modified from
            https://github.com/mentaldesk/fuse/blob/91af00dc9bc7e1deb2f11ab679c536194f85dd4a/MentalDesk.Fuse/ObjectExtensions.cs
            </summary>
        </member>
        <member name="T:Sentry.Internal.OpenTelemetry.OtelSemanticConventions">
            <summary>
            Constants for semantic attribute names outlined by the OpenTelemetry specifications.
            <see href="https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/trace/semantic_conventions/README.md"/> and
            <see href="https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/metrics/semantic_conventions/README.md"/>.
            </summary>
        </member>
        <member name="T:Sentry.Internal.OpenTelemetry.OtelSpanAttributeConstants">
            <summary>
            Defines well-known span attribute keys.
            </summary>
        </member>
        <member name="T:Sentry.Internal.PiiExtensions">
            <summary>
            Extensions to help redact data that might contain Personally Identifiable Information (PII) before sending it to
            Sentry.
            </summary>
        </member>
        <member name="M:Sentry.Internal.PiiExtensions.RedactUrl(System.String)">
            <summary>
            Searches for URLs in text data and redacts any PII data from these, as required.
            </summary>
            <param name="data">The data to be searched</param>
            <returns>
            The data, if no PII data is present or a copy of the data with PII data redacted otherwise
            </returns>
        </member>
        <member name="P:Sentry.Internal.ProcessInfo.StartupTime">
            <summary>
            When the code was initialized.
            </summary>
        </member>
        <member name="P:Sentry.Internal.ProcessInfo.BootTime">
            <summary>
            When the device was initialized.
            </summary>
        </member>
        <member name="T:Sentry.Internal.SentryStopwatch">
            <summary>
            This is a struct-based alternative to <see cref="T:System.Diagnostics.Stopwatch"/>.
            It avoids unnecessary allocations and includes realtime clock values.
            </summary>
        </member>
        <member name="T:Sentry.Internal.SettingLocator">
            <summary>
            Exposes settings that are read from multiple places, such as environment variables, options, attributes, or defaults.
            </summary>
        </member>
        <member name="T:Sentry.Internal.SparseScalarArray`1">
            <summary>
            Sparse array for scalars (value types). You must provide the uninitialized value that will be used for new unused elements.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="T:Sentry.Internal.ThreadsafeCounterDictionary`1">
            <summary>
            Provides a keyed set of counters that can be incremented, read, and reset atomically.
            </summary>
            <typeparam name="TKey">The type of the key.</typeparam>
        </member>
        <member name="M:Sentry.Internal.ThreadsafeCounterDictionary`1.Add(`0,System.Int32)">
            <summary>
            Atomically adds to a counter based on the key provided, creating the counter if necessary.
            </summary>
            <param name="key">The key of the counter to increment.</param>
            <param name="quantity">The amount to add to the counter.</param>
        </member>
        <member name="M:Sentry.Internal.ThreadsafeCounterDictionary`1.Increment(`0)">
            <summary>
            Atomically increments a counter based on the key provided, creating the counter if necessary.
            </summary>
            <param name="key">The key of the counter to increment.</param>
        </member>
        <member name="M:Sentry.Internal.ThreadsafeCounterDictionary`1.ReadAndReset(`0)">
            <summary>
            Gets a single counter's value while atomically resetting it to zero.
            </summary>
            <param name="key">The key to the counter.</param>
            <returns>The previous value of the counter.</returns>
            <remarks>If no counter with the given key has been set, this returns zero.</remarks>
        </member>
        <member name="M:Sentry.Internal.ThreadsafeCounterDictionary`1.ReadAllAndReset">
            <summary>
            Gets the keys and values of all of the counters while atomically resetting them to zero.
            </summary>
            <returns>A read-only dictionary containing the key and the previous value for each counter.</returns>
        </member>
        <member name="M:Sentry.Internal.ThreadsafeCounterDictionary`1.GetEnumerator">
            <summary>
            Gets an enumerator over the keys and values of the counters.
            </summary>
            <returns>An enumerator.</returns>
        </member>
        <member name="P:Sentry.Internal.ThreadsafeCounterDictionary`1.Count">
            <summary>
            Gets the number of counters currently being tracked.
            </summary>
        </member>
        <member name="M:Sentry.Internal.ThreadsafeCounterDictionary`1.ContainsKey(`0)">
            <summary>
            Tests whether or not a counter with the given key exists.
            </summary>
            <param name="key">The key to check.</param>
            <returns>True if the counter exists, false otherwise.</returns>
        </member>
        <member name="M:Sentry.Internal.ThreadsafeCounterDictionary`1.TryGetValue(`0,System.Int32@)">
            <summary>
            Gets the current value of the counter specified.
            </summary>
            <param name="key">The key of the counter.</param>
            <param name="value">The value of the counter, or zero if the counter does not yet exist.</param>
            <returns>Returns <c>true</c> in all cases.</returns>
        </member>
        <member name="P:Sentry.Internal.ThreadsafeCounterDictionary`1.Item(`0)">
            <summary>
            Gets the current value of the counter specified, returning zero if the counter does not yet exist.
            </summary>
            <param name="key">The key of the counter.</param>
        </member>
        <member name="P:Sentry.Internal.ThreadsafeCounterDictionary`1.Keys">
            <summary>
            Gets all of the current counter keys.
            </summary>
        </member>
        <member name="P:Sentry.Internal.ThreadsafeCounterDictionary`1.Values">
            <summary>
            Gets all of the current counter values.
            </summary>
            <remarks>
            Useless, but required by the IReadOnlyDictionary interface.
            </remarks>
        </member>
        <member name="T:Sentry.Internal.DiagnosticSource.OTelKeys">
            <summary>
            Open Telemetry Keys
            https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/trace/semantic_conventions/database.md
            </summary>
        </member>
        <member name="T:Sentry.Internal.DiagnosticSource.SqlKeys">
            <summary>
            Keys specific to the SqlClient listener
            </summary>
        </member>
        <member name="T:Sentry.Internal.DiagnosticSource.EFKeys">
            <summary>
            Keys specific to the Entity Framework listener
            </summary>
        </member>
        <member name="T:Sentry.Internal.DiagnosticSource.DatabaseProviderSystems">
            <summary>
            Mapping of Database Providers to known Open Telemetry db.system
            https://learn.microsoft.com/en-us/ef/core/providers/?tabs=dotnet-core-cli
            https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/trace/semantic_conventions/database.md#notes-and-well-known-identifiers-for-dbsystem
            </summary>
        </member>
        <member name="M:Sentry.Internal.DiagnosticSource.EFConnectionDiagnosticSourceHelper.AddOrReuseSpan(System.Object)">
            <summary>
            EF Connections are often pooled. If we see the same connection multiple times, we reuse the span so that it
            shows as a single connection in the resulting waterfall chart on Sentry.
            </summary>
            <param name="diagnosticSourceValue"></param>
        </member>
        <member name="M:Sentry.Internal.DiagnosticSource.EFDiagnosticSourceHelper.FilterNewLineValue(System.Object)">
            <summary>
            Get the Query with error message and remove the unneeded values.
            </summary>
            <example>
            Compiling query model:
            EF initialize...\r\nEF Query...
            becomes:
            EF Query...
            </example>
            <param name="value">the query to be parsed value</param>
            <returns>the filtered query</returns>
        </member>
        <member name="M:Sentry.Internal.DiagnosticSource.EFQueryCompilerDiagnosticSourceHelper.GetSpanReference(Sentry.ITransaction,System.Object)">
            <summary>
            We don't have a correlation id for compiled query events. We just return the first unfinished query compile span.
            </summary>
        </member>
        <member name="T:Sentry.Internal.DiagnosticSource.SentryDiagnosticSubscriber">
            <summary>
            Class that subscribes to specific listeners from DiagnosticListener.
            </summary>
        </member>
        <member name="T:Sentry.Internal.DiagnosticSource.SentryEFCoreListener">
            <summary>
            Class that consumes Entity Framework Core events.
            </summary>
        </member>
        <member name="F:Sentry.Internal.DiagnosticSource.SentryEFCoreListener.EFQueryStartCompiling">
            <summary>
            Used for EF Core 2.X and 3.X.
            <seealso href="https://docs.microsoft.com/dotnet/api/microsoft.entityframeworkcore.diagnostics.coreeventid.querymodelcompiling?view=efcore-3.1"/>
            </summary>
        </member>
        <member name="F:Sentry.Internal.DiagnosticSource.SentryEFCoreListener.EFQueryCompiling">
            <summary>
            Used for EF Core 2.X and 3.X.
            <seealso href="https://docs.microsoft.com/dotnet/api/microsoft.entityframeworkcore.diagnostics.coreeventid.querymodelcompiling?view=efcore-3.1"/>
            </summary>
        </member>
        <member name="T:Sentry.IScopeObserver">
            <summary>
            Observer for the sync. of Scopes across SDKs.
            </summary>
        </member>
        <member name="M:Sentry.IScopeObserver.AddBreadcrumb(Sentry.Breadcrumb)">
            <summary>
            Adds a breadcrumb.
            </summary>
        </member>
        <member name="M:Sentry.IScopeObserver.SetExtra(System.String,System.Object)">
            <summary>
            Sets an extra.
            </summary>
        </member>
        <member name="M:Sentry.IScopeObserver.SetTag(System.String,System.String)">
            <summary>
            Sets a tag.
            </summary>
        </member>
        <member name="M:Sentry.IScopeObserver.UnsetTag(System.String)">
            <summary>
            Removes a tag.
            </summary>
        </member>
        <member name="M:Sentry.IScopeObserver.SetUser(Sentry.User)">
            <summary>
            Sets the user information.
            </summary>
        </member>
        <member name="T:Sentry.ISentryClient">
            <summary>
            Sentry Client interface.
            </summary>
        </member>
        <member name="P:Sentry.ISentryClient.IsEnabled">
            <summary>
            Whether the client is enabled or not.
            </summary>
        </member>
        <member name="M:Sentry.ISentryClient.CaptureEvent(Sentry.SentryEvent,Sentry.Scope)">
            <summary>
            Capture the event.
            </summary>
            <param name="evt">The event to be captured.</param>
            <param name="scope">An optional scope to be applied to the event.</param>
            <returns>The Id of the event.</returns>
        </member>
        <member name="M:Sentry.ISentryClient.CaptureEvent(Sentry.SentryEvent,Sentry.Hint,Sentry.Scope)">
            <summary>
            Capture the event
            </summary>
            <param name="evt">The event to be captured.</param>
            <param name="hint">An optional hint providing high level context for the source of the event</param>
            <param name="scope">An optional scope to be applied to the event.</param>
            <returns>The Id of the event.</returns>
        </member>
        <member name="M:Sentry.ISentryClient.CaptureUserFeedback(Sentry.UserFeedback)">
            <summary>
            Captures a user feedback.
            </summary>
            <param name="userFeedback">The user feedback to send to Sentry.</param>
        </member>
        <member name="M:Sentry.ISentryClient.CaptureTransaction(Sentry.Transaction)">
            <summary>
            Captures a transaction.
            </summary>
            <remarks>
            Note: this method is NOT meant to be called from user code!
            Instead, call <see cref="M:Sentry.ISpan.Finish"/> on the transaction.
            </remarks>
            <param name="transaction">The transaction.</param>
        </member>
        <member name="M:Sentry.ISentryClient.CaptureTransaction(Sentry.Transaction,Sentry.Hint)">
            <summary>
            Captures a transaction.
            </summary>
            <remarks>
            Note: this method is NOT meant to be called from user code!
            Instead, call <see cref="M:Sentry.ISpan.Finish"/> on the transaction.
            </remarks>
            <param name="transaction">The transaction.</param>
            <param name="hint">
            A hint providing extra context.
            This will be available in callbacks prior to processing the transaction.
            </param>
        </member>
        <member name="M:Sentry.ISentryClient.CaptureSession(Sentry.SessionUpdate)">
            <summary>
            Captures a session update.
            </summary>
            <remarks>
            Note: this method is NOT meant to be called from user code!
            It will be called automatically by the SDK.
            </remarks>
            <param name="sessionUpdate">The update to send to Sentry.</param>
        </member>
        <member name="M:Sentry.ISentryClient.FlushAsync(System.TimeSpan)">
            <summary>
            Flushes the queue of captured events until the timeout is reached.
            </summary>
            <param name="timeout">The amount of time allowed for flushing.</param>
            <returns>A task to await for the flush operation.</returns>
        </member>
        <member name="T:Sentry.ISentryScopeManager">
            <summary>
            Scope management.
            </summary>
            <remarks>
            An implementation shall create new scopes and allow consumers
            modify the current scope.
            </remarks>
        </member>
        <member name="M:Sentry.ISentryScopeManager.ConfigureScope(System.Action{Sentry.Scope})">
            <summary>
            Configures the current scope.
            </summary>
            <param name="configureScope">The configure scope.</param>
        </member>
        <member name="M:Sentry.ISentryScopeManager.ConfigureScopeAsync(System.Func{Sentry.Scope,System.Threading.Tasks.Task})">
            <summary>
            Asynchronously configure the current scope.
            </summary>
            <param name="configureScope">The configure scope.</param>
            <returns>A task that completes when the callback is done or a completed task if the SDK is disabled.</returns>
        </member>
        <member name="M:Sentry.ISentryScopeManager.BindClient(Sentry.ISentryClient)">
            <summary>
            Binds the client to the current scope.
            </summary>
            <param name="client">The client.</param>
        </member>
        <member name="M:Sentry.ISentryScopeManager.PushScope">
            <summary>
            Pushes a new scope into the stack which is removed upon Dispose.
            </summary>
            <returns>A disposable which removes the scope
            from the environment when invoked.</returns>
        </member>
        <member name="M:Sentry.ISentryScopeManager.PushScope``1(``0)">
            <summary>
            Pushes a new scope into the stack which is removed upon Dispose.
            </summary>
            <param name="state">A state to associate with the scope.</param>
            <returns>A disposable which removes the scope
            from the environment when invoked.</returns>
        </member>
        <member name="M:Sentry.ISentryScopeManager.WithScope(System.Action{Sentry.Scope})">
            <summary>
            Runs the callback within a new scope.
            </summary>
            <remarks>
            Pushes a new scope, runs the callback, then pops the scope. Use this when you have significant work to
            perform within an isolated scope.  If you just need to configure scope for a single event, use the overloads
            of CaptureEvent, CaptureMessage and CaptureException that provide a callback to a configurable scope.
            </remarks>
            <see href="https://docs.sentry.io/platforms/dotnet/enriching-events/scopes/#local-scopes"/>
            <param name="scopeCallback">The callback to run with the one time scope.</param>
        </member>
        <member name="T:Sentry.ISentryScopeStateProcessor">
            <summary>
            Defines the logic for applying state onto a scope.
            </summary>
        </member>
        <member name="M:Sentry.ISentryScopeStateProcessor.Apply(Sentry.Scope,System.Object)">
            <summary>
            Applies state onto a scope.
            </summary>
        </member>
        <member name="T:Sentry.ISentryUserFactory">
            <summary>
            Sentry User Factory
            </summary>
        </member>
        <member name="M:Sentry.ISentryUserFactory.Create">
            <summary>
            Creates a Sentry <see cref="T:Sentry.User"/> representing the current principal.
            </summary>
            <returns>The protocol user</returns>
        </member>
        <member name="T:Sentry.ISession">
            <summary>
            Session metadata.
            </summary>
        </member>
        <member name="P:Sentry.ISession.Id">
            <summary>
            Session auto-generated ID.
            </summary>
        </member>
        <member name="P:Sentry.ISession.DistinctId">
            <summary>
            Session distinct ID.
            </summary>
        </member>
        <member name="P:Sentry.ISession.StartTimestamp">
            <summary>
            Session start timestamp.
            </summary>
        </member>
        <member name="P:Sentry.ISession.Release">
            <summary>
            Release.
            </summary>
        </member>
        <member name="P:Sentry.ISession.Environment">
            <summary>
            Environment.
            </summary>
        </member>
        <member name="P:Sentry.ISession.IpAddress">
            <summary>
            IP address of the user.
            </summary>
        </member>
        <member name="P:Sentry.ISession.UserAgent">
            <summary>
            User agent.
            </summary>
        </member>
        <member name="P:Sentry.ISession.ErrorCount">
            <summary>
            Reported error count.
            </summary>
        </member>
        <member name="T:Sentry.ISpan">
            <summary>
            Span.
            </summary>
        </member>
        <member name="P:Sentry.ISpan.Description">
            <summary>
            Span description.
            </summary>
        </member>
        <member name="P:Sentry.ISpan.Operation">
            <summary>
            Span operation.
            </summary>
        </member>
        <member name="P:Sentry.ISpan.Status">
            <summary>
            Span status.
            </summary>
        </member>
        <member name="M:Sentry.ISpan.StartChild(System.String)">
            <summary>
            Starts a child span.
            </summary>
        </member>
        <member name="M:Sentry.ISpan.Finish">
            <summary>
            Finishes the span.
            </summary>
        </member>
        <member name="M:Sentry.ISpan.Finish(Sentry.SpanStatus)">
            <summary>
            Finishes the span with the specified status.
            </summary>
        </member>
        <member name="M:Sentry.ISpan.Finish(System.Exception,Sentry.SpanStatus)">
            <summary>
            Finishes the span with the specified exception and status.
            </summary>
        </member>
        <member name="M:Sentry.ISpan.Finish(System.Exception)">
            <summary>
            Finishes the span with the specified exception and automatically inferred status.
            </summary>
        </member>
        <member name="T:Sentry.SpanExtensions">
            <summary>
            Extensions for <see cref="T:Sentry.ISpan"/>.
            </summary>
        </member>
        <member name="M:Sentry.SpanExtensions.StartChild(Sentry.ISpan,System.String,System.String)">
            <summary>
            Starts a child span.
            </summary>
        </member>
        <member name="M:Sentry.SpanExtensions.GetTransaction(Sentry.ISpan)">
            <summary>
            Gets the transaction that this span belongs to.
            </summary>
        </member>
        <member name="M:Sentry.SpanExtensions.GetDbParentSpan(Sentry.ISpan)">
            <summary>
            Gets the parent span for database operations. This is the last active non-database span, which might be the
            transaction root, or it might be some other child span of the transaction (such as a web request).
            </summary>
            <remarks>
            Used by EF, EF Core, and SQLClient integrations.
            </remarks>
        </member>
        <member name="T:Sentry.ISpanContext">
            <summary>
            Span metadata.
            </summary>
        </member>
        <member name="T:Sentry.ISpanData">
            <summary>
            Immutable data belonging to a span.
            </summary>
        </member>
        <member name="P:Sentry.ISpanData.StartTimestamp">
            <summary>
            Start timestamp.
            </summary>
        </member>
        <member name="P:Sentry.ISpanData.EndTimestamp">
            <summary>
            End timestamp.
            </summary>
        </member>
        <member name="P:Sentry.ISpanData.IsFinished">
            <summary>
            Whether the span is finished.
            </summary>
        </member>
        <member name="M:Sentry.ISpanData.GetTraceHeader">
            <summary>
            Get Sentry trace header.
            </summary>
        </member>
        <member name="T:Sentry.ITransaction">
            <summary>
            Transaction.
            </summary>
        </member>
        <member name="P:Sentry.ITransaction.Name">
            <summary>
            Transaction name.
            </summary>
        </member>
        <member name="P:Sentry.ITransaction.IsParentSampled">
            <summary>
            Whether the parent transaction of this transaction has been sampled.
            </summary>
        </member>
        <member name="P:Sentry.ITransaction.Spans">
            <summary>
            Flat list of spans within this transaction.
            </summary>
        </member>
        <member name="M:Sentry.ITransaction.GetLastActiveSpan">
            <summary>
            Gets the last active (not finished) span in this transaction.
            </summary>
        </member>
        <member name="T:Sentry.ITransactionContext">
            <summary>
            Transaction metadata.
            </summary>
        </member>
        <member name="P:Sentry.ITransactionContext.Name">
            <summary>
            Transaction name.
            </summary>
        </member>
        <member name="P:Sentry.ITransactionContext.IsParentSampled">
            <summary>
            Whether the parent transaction of this transaction has been sampled.
            </summary>
        </member>
        <member name="T:Sentry.ITransactionData">
            <summary>
            Immutable data belonging to a transaction.
            </summary>
        </member>
        <member name="T:Sentry.MeasurementUnit">
            <summary>
            The unit of measurement of a metric value.
            </summary>
            <seealso href="https://getsentry.github.io/relay/relay_metrics/enum.MetricUnit.html"/>
        </member>
        <member name="F:Sentry.MeasurementUnit.None">
            <summary>
            A special measurement unit that is used for measurements that have no natural unit.
            </summary>
        </member>
        <member name="M:Sentry.MeasurementUnit.Custom(System.String)">
            <summary>
            Creates a custom measurement unit.
            </summary>
            <param name="name">The name of the custom measurement unit. It will be converted to lower case.</param>
            <returns>The custom measurement unit.</returns>
        </member>
        <member name="M:Sentry.MeasurementUnit.ToString">
            <summary>
            Returns the string representation of the measurement unit, as it will be sent to Sentry.
            </summary>
        </member>
        <member name="M:Sentry.MeasurementUnit.Equals(Sentry.MeasurementUnit)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.MeasurementUnit.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.MeasurementUnit.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Sentry.MeasurementUnit.op_Equality(Sentry.MeasurementUnit,Sentry.MeasurementUnit)">
            <summary>
            Returns true if the operands are equal.
            </summary>
        </member>
        <member name="M:Sentry.MeasurementUnit.op_Inequality(Sentry.MeasurementUnit,Sentry.MeasurementUnit)">
            <summary>
            Returns true if the operands are not equal.
            </summary>
        </member>
        <member name="T:Sentry.MeasurementUnit.Duration">
            <summary>
            A time duration unit
            </summary>
            <seealso href="https://getsentry.github.io/relay/relay_metrics/enum.DurationUnit.html"/>
        </member>
        <member name="F:Sentry.MeasurementUnit.Duration.Nanosecond">
            <summary>
            Nanosecond unit (10^-9 seconds)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Duration.Microsecond">
            <summary>
            Microsecond unit (10^-6 seconds)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Duration.Millisecond">
            <summary>
            Millisecond unit (10^-3 seconds)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Duration.Second">
            <summary>
            Second unit
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Duration.Minute">
            <summary>
            Minute unit (60 seconds)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Duration.Hour">
            <summary>
            Hour unit (3,600 seconds)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Duration.Day">
            <summary>
            Day unit (86,400 seconds)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Duration.Week">
            <summary>
            Week unit (604,800 seconds)
            </summary>
        </member>
        <member name="M:Sentry.MeasurementUnit.op_Implicit(Sentry.MeasurementUnit.Duration)~Sentry.MeasurementUnit">
            <summary>
            Implicitly casts a <see cref="T:Sentry.MeasurementUnit.Duration"/> to a <see cref="T:Sentry.MeasurementUnit"/>.
            </summary>
        </member>
        <member name="T:Sentry.MeasurementUnit.Fraction">
            <summary>
            A fraction unit
            </summary>
            <seealso href="https://getsentry.github.io/relay/relay_metrics/enum.FractionUnit.html"/>
        </member>
        <member name="F:Sentry.MeasurementUnit.Fraction.Ratio">
            <summary>
            Floating point fraction of 1.
            A ratio of 1.0 equals 100%.
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Fraction.Percent">
            <summary>
            Ratio expressed as a fraction of 100.
            100% equals a ratio of 1.0.
            </summary>
        </member>
        <member name="M:Sentry.MeasurementUnit.op_Implicit(Sentry.MeasurementUnit.Fraction)~Sentry.MeasurementUnit">
            <summary>
            Implicitly casts a <see cref="T:Sentry.MeasurementUnit.Fraction"/> to a <see cref="T:Sentry.MeasurementUnit"/>.
            </summary>
        </member>
        <member name="T:Sentry.MeasurementUnit.Information">
            <summary>
            An information size unit
            </summary>
            <seealso href="https://getsentry.github.io/relay/relay_metrics/enum.InformationUnit.html"/>
        </member>
        <member name="F:Sentry.MeasurementUnit.Information.Bit">
            <summary>
            Bit unit (1/8 of byte)
            </summary>
            <remarks>
            Some computer systems may have a different number of bits per byte.
            </remarks>
        </member>
        <member name="F:Sentry.MeasurementUnit.Information.Byte">
            <summary>
            Byte unit
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Information.Kilobyte">
            <summary>
            Kilobyte unit (10^3 bytes)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Information.Kibibyte">
            <summary>
            Kibibyte unit (2^10 bytes)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Information.Megabyte">
            <summary>
            Megabyte unit (10^6 bytes)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Information.Mebibyte">
            <summary>
            Mebibyte unit (2^20 bytes)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Information.Gigabyte">
            <summary>
            Gigabyte unit (10^9 bytes)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Information.Gibibyte">
            <summary>
            Gibibyte unit (2^30 bytes)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Information.Terabyte">
            <summary>
            Terabyte unit (10^12 bytes)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Information.Tebibyte">
            <summary>
            Tebibyte unit (2^40 bytes)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Information.Petabyte">
            <summary>
            Petabyte unit (10^15 bytes)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Information.Pebibyte">
            <summary>
            Pebibyte unit (2^50 bytes)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Information.Exabyte">
            <summary>
            Exabyte unit (10^18 bytes)
            </summary>
        </member>
        <member name="F:Sentry.MeasurementUnit.Information.Exbibyte">
            <summary>
            Exbibyte unit (2^60 bytes)
            </summary>
        </member>
        <member name="M:Sentry.MeasurementUnit.op_Implicit(Sentry.MeasurementUnit.Information)~Sentry.MeasurementUnit">
            <summary>
            Implicitly casts a <see cref="T:Sentry.MeasurementUnit.Information"/> to a <see cref="T:Sentry.MeasurementUnit"/>.
            </summary>
        </member>
        <member name="T:Sentry.Package">
            <summary>
            Represents a package used to compose the SDK.
            </summary>
        </member>
        <member name="P:Sentry.Package.Name">
            <summary>
            The name of the package.
            </summary>
            <example>
            nuget:Sentry
            nuget:Sentry.AspNetCore
            </example>
        </member>
        <member name="P:Sentry.Package.Version">
            <summary>
            The version of the package.
            </summary>
            <example>
            1.0.0-rc1
            </example>
        </member>
        <member name="M:Sentry.Package.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of a <see cref="T:Sentry.Package"/>.
            </summary>
            <param name="name">The package name.</param>
            <param name="version">The package version.</param>
        </member>
        <member name="M:Sentry.Package.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Package.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="M:Sentry.Package.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Package.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Sentry.PlatformAbstractions.FrameworkInfo">
            <summary>
            Information about .NET Framework in the running machine
            The purpose of this partial class is to expose the API to all targets
            For netstandard, the call to methods will be a simple no-op.
            </summary>
            <summary>
            No-op version for netstandard targets
            </summary>
        </member>
        <member name="P:Sentry.PlatformAbstractions.FrameworkInfo.NetFxReleaseVersionMap">
            <summary>
            The map between release number and version number
            </summary>
            <see href="https://docs.microsoft.com/en-us/dotnet/framework/migration-guide/how-to-determine-which-versions-are-installed" />
        </member>
        <member name="M:Sentry.PlatformAbstractions.FrameworkInfo.GetLatest(System.Int32)">
            <summary>
            No-op version for netstandard targets
            </summary>
            <param name="clr"></param>
        </member>
        <member name="M:Sentry.PlatformAbstractions.FrameworkInfo.GetInstallations">
            <summary>
            No-op version for netstandard targets
            </summary>
        </member>
        <member name="T:Sentry.PlatformAbstractions.FrameworkInstallation">
            <summary>
            A .NET Framework installation
            </summary>
            <seealso href="https://en.wikipedia.org/wiki/.NET_Framework_version_history"/>
            <seealso href="https://docs.microsoft.com/en-us/dotnet/framework/migration-guide/how-to-determine-which-versions-are-installed"/>
        </member>
        <member name="P:Sentry.PlatformAbstractions.FrameworkInstallation.ShortName">
            <summary>
            Short name
            </summary>
            <example>
            v2.0.50727, v3.5, v4.0
            </example>
        </member>
        <member name="P:Sentry.PlatformAbstractions.FrameworkInstallation.Version">
            <summary>
            Version
            </summary>
            <example>
            2.0.50727.4927, 3.0.30729.4926, 3.5.30729.4926
            </example>
        </member>
        <member name="P:Sentry.PlatformAbstractions.FrameworkInstallation.ServicePack">
            <summary>
            Service pack number, if any
            </summary>
            <remarks>
            Only relevant prior to .NET 4
            </remarks>
        </member>
        <member name="P:Sentry.PlatformAbstractions.FrameworkInstallation.Profile">
            <summary>
            Type of Framework profile
            </summary>
            <remarks>Only relevant for .NET 3.5 and 4.0</remarks>
            <seealso href="https://docs.microsoft.com/en-us/dotnet/framework/deployment/client-profile"/>
        </member>
        <member name="P:Sentry.PlatformAbstractions.FrameworkInstallation.Release">
            <summary>
             A .NET Framework release key
            </summary>
            <remarks>
            Windows registry key:
            HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\Release
            Only applicable when on Windows, with full .NET Framework 4.5 and later.
            </remarks>
            <see href="https://docs.microsoft.com/en-us/dotnet/framework/migration-guide/how-to-determine-which-versions-are-installed"/>
        </member>
        <member name="M:Sentry.PlatformAbstractions.FrameworkInstallation.ToString">
            <inheritdoc />
        </member>
        <member name="T:Sentry.PlatformAbstractions.FrameworkProfile">
            <summary>
            Type of Framework profile
            </summary>
            <remarks>Only relevant for .NET 3.5 and 4.0</remarks>
            <seealso href="https://docs.microsoft.com/en-us/dotnet/framework/deployment/client-profile"/>
        </member>
        <member name="F:Sentry.PlatformAbstractions.FrameworkProfile.Client">
            <summary>
            The .NET Client Profile is a subset of the .NET Framework
            </summary>
        </member>
        <member name="F:Sentry.PlatformAbstractions.FrameworkProfile.Full">
            <summary>
            The full .NET Framework
            </summary>
        </member>
        <member name="T:Sentry.PlatformAbstractions.Runtime">
            <summary>
            Details of the runtime
            </summary>
        </member>
        <member name="P:Sentry.PlatformAbstractions.Runtime.Current">
            <summary>
            Gets the current runtime
            </summary>
            <value>
            The current runtime.
            </value>
        </member>
        <member name="P:Sentry.PlatformAbstractions.Runtime.Name">
            <summary>
            The name of the runtime
            </summary>
            <example>
            .NET Framework, .NET Native, Mono
            </example>
        </member>
        <member name="P:Sentry.PlatformAbstractions.Runtime.Version">
            <summary>
            The version of the runtime
            </summary>
            <example>
            4.7.2633.0
            </example>
        </member>
        <member name="P:Sentry.PlatformAbstractions.Runtime.Raw">
            <summary>
            The raw value parsed to extract Name and Version
            </summary>
            <remarks>
            This property will contain a value when the underlying API
            returned Name and Version as a single string which required parsing.
            </remarks>
        </member>
        <member name="P:Sentry.PlatformAbstractions.Runtime.Identifier">
            <summary>
            The .NET Runtime Identifier of the runtime
            </summary>
            <remarks>
            This property will be populated for .NET 5 and newer, or <c>null</c> otherwise.
            </remarks>
        </member>
        <member name="M:Sentry.PlatformAbstractions.Runtime.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            Creates a new Runtime instance
            </summary>
        </member>
        <member name="M:Sentry.PlatformAbstractions.Runtime.ToString">
            <summary>
            The string representation of the Runtime
            </summary>
        </member>
        <member name="M:Sentry.PlatformAbstractions.Runtime.Equals(Sentry.PlatformAbstractions.Runtime)">
            <summary>
            Compare instances for equality.
            </summary>
            <param name="other">The instance to compare against.</param>
            <returns>True if the instances are equal by reference or its state.</returns>
        </member>
        <member name="M:Sentry.PlatformAbstractions.Runtime.Equals(System.Object)">
            <summary>
            Compare instances for equality.
            </summary>
            <param name="obj">The instance to compare against.</param>
            <returns>True if the instances are equal by reference or its state.</returns>
        </member>
        <member name="M:Sentry.PlatformAbstractions.Runtime.GetHashCode">
            <summary>
            Get the hashcode of this instance.
            </summary>
            <returns>The hashcode of the instance.</returns>
        </member>
        <member name="T:Sentry.PlatformAbstractions.RuntimeExtensions">
            <summary>
            Extension method to the <see cref="T:Sentry.PlatformAbstractions.Runtime"/> class.
            </summary>
        </member>
        <member name="M:Sentry.PlatformAbstractions.RuntimeExtensions.IsNetFx(Sentry.PlatformAbstractions.Runtime)">
            <summary>
            Is the runtime instance .NET Framework.
            </summary>
            <param name="runtime">The runtime instance to check.</param>
            <returns>True if it's .NET Framework, otherwise false.</returns>
        </member>
        <member name="M:Sentry.PlatformAbstractions.RuntimeExtensions.IsNetCore(Sentry.PlatformAbstractions.Runtime)">
            <summary>
            Is the runtime instance .NET Core (or .NET).
            </summary>
            <param name="runtime">The runtime instance to check.</param>
            <returns>True if it's .NET Core (or .NET), otherwise false.</returns>
        </member>
        <member name="M:Sentry.PlatformAbstractions.RuntimeExtensions.IsMono(Sentry.PlatformAbstractions.Runtime)">
            <summary>
            Is the runtime instance Mono.
            </summary>
            <param name="runtime">The runtime instance to check.</param>
            <returns>True if it's Mono, otherwise false.</returns>
        </member>
        <member name="M:Sentry.PlatformAbstractions.RuntimeExtensions.IsBrowserWasm(Sentry.PlatformAbstractions.Runtime)">
            <summary>
            Is the runtime instance Browser Web Assembly.
            </summary>
            <param name="runtime">The runtime instance to check.</param>
            <returns>True if it's Browser WASM, otherwise false.</returns>
        </member>
        <member name="M:Sentry.PlatformAbstractions.RuntimeInfo.GetRuntime">
            <summary>
            Gets the current runtime.
            </summary>
            <returns>A new instance for the current runtime</returns>
        </member>
        <member name="M:Sentry.iOS.Extensions.CocoaExtensions.ToObject(Foundation.NSNumber)">
            <summary>
            Converts an <see cref="T:Foundation.NSNumber"/> to a .NET primitive data type and returns the result box in an <see cref="T:System.Object"/>.
            </summary>
            <param name="n">The <see cref="T:Foundation.NSNumber"/> to convert.</param>
            <returns>An <see cref="T:System.Object"/> that contains the number in its primitive type.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the number's <c>ObjCType</c> was unrecognized.</exception>
            <remarks>
            This method always returns a result that is compatible with its value, but does not always give the expected result.
            Specifically:
            <list type="bullet">
              <item><c>byte</c> returns <c>short</c></item>
              <item><c>ushort</c> return <c>int</c></item>
              <item><c>uint</c> returns <c>long</c></item>
              <item><c>ulong</c> returns <c>long</c> unless it's > <c>long.MaxValue</c></item>
              <item>n/nu types return more primitive types (ex. <c>nfloat</c> => <c>double</c>)</item>
            </list>
            Type encodings are listed here:
            https://developer.apple.com/library/archive/documentation/Cocoa/Conceptual/ObjCRuntimeGuide/Articles/ocrtTypeEncodings.html
            </remarks>
        </member>
        <member name="T:Sentry.SentryOptions">
            <summary>
            Sentry SDK options
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.iOS">
            <summary>
            Exposes additional options for the iOS platform.
            </summary>
        </member>
        <member name="T:Sentry.SentryOptions.IosOptions">
            <summary>
            Provides additional options for the iOS platform.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.AttachScreenshot">
            <summary>
            Automatically attaches a screenshot when capturing an error or exception.
            The default value is <c>false</c> (disabled).
            </summary>
            <remarks>
            See https://docs.sentry.io/platforms/apple/guides/ios/configuration/options/#attach-screenshot
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.AppHangTimeoutInterval">
            <summary>
            The minimum amount of time an app should be unresponsive to be classified as an App Hanging.
            The actual amount may be a little longer.  Avoid using values lower than 100ms, which may cause a lot
            of app hangs events being transmitted.
            The default value is 2 seconds.
            Requires setting <see cref="P:Sentry.SentryOptions.IosOptions.EnableAppHangTracking"/> to <c>true</c>.
            </summary>
            <remarks>
            See https://docs.sentry.io/platforms/apple/configuration/app-hangs/
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.IdleTimeout">
            <summary>
            How long an idle transaction waits for new children after all its child spans finished.
            Only UI event transactions are idle transactions.
            The default value is 3 seconds.
            </summary>
            <remarks>
            See https://docs.sentry.io/platforms/apple/performance/instrumentation/automatic-instrumentation/#user-interaction-instrumentation
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.Distribution">
            <summary>
            The distribution of the application, associated with the release set in <see cref="P:Sentry.SentryOptions.Release"/>.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableAppHangTracking">
            <summary>
            When enabled, the SDK tracks when the application stops responding for a specific amount of
            time defined by the <see cref="P:Sentry.SentryOptions.IosOptions.AppHangTimeoutInterval"/> option.
            The default value is <c>true</c> (enabled).
            </summary>
            <remarks>
            See https://docs.sentry.io/platforms/apple/configuration/app-hangs/
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableAutoBreadcrumbTracking">
            <summary>
            When enabled, the SDK adds breadcrumbs for various system events.
            The default value is <c>true</c> (enabled).
            </summary>
            <remarks>
            See https://docs.sentry.io/platforms/apple/enriching-events/breadcrumbs/#automatic-breadcrumbs
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableAutoPerformanceTracing">
            <summary>
            When enabled, the SDK tracks performance for <see cref="T:UIKit.UIViewController"/> subclasses and HTTP requests
            automatically. It also measures the app start and slow and frozen frames.
            The default value is <c>true</c> (enabled).
            </summary>
            <remarks>
            Performance Monitoring must be enabled for this option to take effect.
            See: https://docs.sentry.io/platforms/apple/performance/
            And: https://docs.sentry.io/platforms/apple/performance/instrumentation/automatic-instrumentation/#opt-out
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableAutoPerformanceTracking">
            <summary>
            When enabled, the SDK tracks performance for <see cref="T:UIKit.UIViewController"/> subclasses and HTTP requests
            automatically. It also measures the app start and slow and frozen frames.
            The default value is <c>true</c> (enabled).
            </summary>
            <remarks>
            Performance Monitoring must be enabled for this option to take effect.
            See: https://docs.sentry.io/platforms/apple/performance/
            And: https://docs.sentry.io/platforms/apple/performance/instrumentation/automatic-instrumentation/#opt-out
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableCoreDataTracing">
            <summary>
            When enabled, the SDK tracks the performance of Core Data operations.
            It requires enabling performance monitoring.
            The default value is <c>true</c> (enabled).
            </summary>
            <remarks>
            Performance Monitoring must be enabled for this option to take effect.
            See https://docs.sentry.io/platforms/apple/performance/instrumentation/automatic-instrumentation/#core-data-instrumentation
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableCoreDataTracking">
            <summary>
            When enabled, the SDK tracks the performance of Core Data operations.
            It requires enabling performance monitoring.
            The default value is <c>true</c> (enabled).
            </summary>
            <remarks>
            Performance Monitoring must be enabled for this option to take effect.
            See https://docs.sentry.io/platforms/apple/performance/instrumentation/automatic-instrumentation/#core-data-instrumentation
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableFileIOTracing">
            <summary>
            When enabled, the SDK tracks performance for file IO reads and writes with <see cref="T:Foundation.NSData"/>
            if auto performance tracking and <see cref="P:Sentry.SentryOptions.IosOptions.EnableSwizzling"/> are enabled.
            The default value is <c>true</c> (enabled).
            </summary>
            <remarks>
            See https://docs.sentry.io/platforms/apple/performance/instrumentation/automatic-instrumentation/#file-io-instrumentation
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableFileIOTracking">
            <summary>
            When enabled, the SDK tracks performance for file IO reads and writes with <see cref="T:Foundation.NSData"/>
            if auto performance tracking and <see cref="P:Sentry.SentryOptions.IosOptions.EnableSwizzling"/> are enabled.
            The default value is <c>true</c> (enabled).
            </summary>
            <remarks>
            See https://docs.sentry.io/platforms/apple/performance/instrumentation/automatic-instrumentation/#file-io-instrumentation
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableNetworkBreadcrumbs">
            <summary>
            When enabled, the SDK adds breadcrumbs for each network request
            if auto performance tracking and <see cref="P:Sentry.SentryOptions.IosOptions.EnableSwizzling"/> are enabled.
            The default value is <c>true</c> (enabled).
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableNetworkTracking">
            <summary>
            When enabled, the SDK adds breadcrumbs for HTTP requests and tracks performance for HTTP requests
            if auto performance tracking and <see cref="P:Sentry.SentryOptions.IosOptions.EnableSwizzling"/> are enabled.
            The default value is <c>true</c> (enabled).
            </summary>
            <remarks>
            https://docs.sentry.io/platforms/apple/performance/instrumentation/automatic-instrumentation/#http-instrumentation
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableWatchdogTerminationTracking">
            <summary>
            Whether to enable watchdog termination tracking or not.
            The default value is <c>true</c> (enabled).
            </summary>
            <remarks>
            https://docs.sentry.io/platforms/apple/configuration/watchdog-terminations/
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableOutOfMemoryTracking">
            <summary>
            Whether to enable out of memory tracking or not.
            The default value is <c>true</c> (enabled).
            </summary>
            <remarks>
            https://docs.sentry.io/platforms/apple/configuration/out-of-memory/
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableSwizzling">
            <summary>
            Whether the SDK should use swizzling or not.
            The default value is <c>true</c> (enabled).
            </summary>
            <remarks>
            When turned off the following features are disabled: breadcrumbs for touch events and
            navigation with <see cref="T:UIKit.UIViewController"/>, automatic instrumentation for <see cref="T:UIKit.UIViewController"/>,
            automatic instrumentation for HTTP requests, automatic instrumentation for file IO with <see cref="T:Foundation.NSData"/>,
            and automatically added sentry-trace header to HTTP requests for distributed tracing.
            See https://docs.sentry.io/platforms/apple/configuration/swizzling/
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableUIViewControllerTracing">
            <summary>
            When enabled, the SDK tracks performance for <see cref="T:UIKit.UIViewController"/> subclasses.
            The default value is <c>true</c> (enabled).
            </summary>
            <remarks>
            See https://docs.sentry.io/platforms/apple/performance/instrumentation/automatic-instrumentation/#uiviewcontroller-instrumentation
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableUIViewControllerTracking">
            <summary>
            When enabled, the SDK tracks performance for <see cref="T:UIKit.UIViewController"/> subclasses.
            The default value is <c>true</c> (enabled).
            </summary>
            <remarks>
            See https://docs.sentry.io/platforms/apple/performance/instrumentation/automatic-instrumentation/#uiviewcontroller-instrumentation
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableUserInteractionTracing">
            <summary>
            When enabled, the SDK creates transactions for UI events like buttons clicks, switch toggles,
            and other UI elements that uses <see cref="M:UIKit.UIControl.SendAction(ObjCRuntime.Selector,Foundation.NSObject,UIKit.UIEvent)"/>.
            The default value is <c>false</c> (disabled).
            </summary>
            <remarks>
            See https://docs.sentry.io/platforms/apple/performance/instrumentation/automatic-instrumentation/#user-interaction-instrumentation
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.StitchAsyncCode">
            <summary>
            This feature is no longer available.  This option does nothing and will be removed in a future release.
            </summary>
            <remarks>
            This was removed from the Cocoa SDK in 8.6.0 with https://github.com/getsentry/sentry-cocoa/pull/2973
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.UrlSessionDelegate">
            <summary>
            When provided, this will be set as delegate on the <see cref="T:Foundation.NSUrlSession"/> used for network
            data-transfer tasks performed by the native Sentry Cocoa SDK.
            </summary>
            <remarks>
            See https://github.com/getsentry/sentry-cocoa/issues/1168
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IosOptions.EnableCocoaSdkTracing">
            <summary>
            Gets or sets a value that indicates if tracing features are enabled on the embedded Cocoa SDK.
            The default value is <c>false</c> (disabled).
            </summary>
        </member>
        <member name="M:Sentry.SentryOptions.IosOptions.AddInAppExclude(System.String)">
            <summary>
            Add prefix to exclude from 'InApp' stacktrace list by the Cocoa SDK.
            Note that this uses iOS module names, not .NET namespaces.
            </summary>
            <param name="prefix">The string used to filter the stacktrace to be excluded from InApp.</param>
            <remarks>
            https://docs.sentry.io/platforms/apple/configuration/options/#in-app-exclude
            </remarks>
        </member>
        <member name="M:Sentry.SentryOptions.IosOptions.AddInAppInclude(System.String)">
            <summary>
            Add prefix to include as in 'InApp' stacktrace by the Cocoa SDK.
            Note that this uses iOS package names, not .NET namespaces.
            </summary>
            <param name="prefix">The string used to filter the stacktrace to be included in InApp.</param>
            <remarks>
            See https://docs.sentry.io/platforms/apple/configuration/options/#in-app-include
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.ScopeStackContainer">
            <summary>
            If set, the <see cref="T:Sentry.Internal.SentryScopeManager"/> will ignore <see cref="P:Sentry.SentryOptions.IsGlobalModeEnabled"/>
            and use the provided container instead.
            </summary>
            <remarks>
            Used by the ASP.NET (classic) integration.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IsGlobalModeEnabled">
            <summary>
            Specifies whether to use global scope management mode.
            Should be <c>true</c> for client applications and <c>false</c> for server applications.
            The default is <c>true</c> for mobile targets.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.ScopeObserver">
            <summary>
            A scope set outside of Sentry SDK. If set, the global parameters from the SDK's scope will be sent to the observed scope.<br/>
            NOTE: EnableScopeSync must be set true for the scope to be synced.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.EnableScopeSync">
            <summary>
            If true, the SDK's scope will be synced with the observed scope.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.Transport">
            <summary>
            This holds a reference to the current transport, when one is active.
            If set manually before initialization, the provided transport will be used instead of the default transport.
            </summary>
            <remarks>
            If <seealso cref="P:Sentry.SentryOptions.CacheDirectoryPath"/> is set, any transport set here will be wrapped in a
            <seealso cref="T:Sentry.Internal.Http.CachingTransport"/> and used as its inner transport.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.ExceptionProcessors">
            <summary>
            A list of exception processors
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.TransactionProcessors">
            <summary>
            A list of transaction processors
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.EventProcessors">
            <summary>
            A list of event processors
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.EventProcessorsProviders">
            <summary>
            A list of providers of <see cref="T:Sentry.Extensibility.ISentryEventProcessor"/>
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.TransactionProcessorsProviders">
            <summary>
            A list of providers of <see cref="T:Sentry.Extensibility.ISentryTransactionProcessor"/>
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.ExceptionProcessorsProviders">
            <summary>
            A list of providers of <see cref="T:Sentry.Extensibility.ISentryEventExceptionProcessor"/>
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.Integrations">
            <summary>
            A list of integrations to be added when the SDK is initialized.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.TagFilters">
            <summary>
            List of substrings or regular expression patterns to filter out tags
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.BackgroundWorker">
            <summary>
            The worker used by the client to pass envelopes.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.SentryScopeStateProcessor">
            <summary>
            Scope state processor.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.InAppExclude">
            <summary>
            A list of namespaces (or prefixes) considered not part of application code
            </summary>
            <remarks>
            Sentry by default filters the stacktrace to display only application code.
            A user can optionally click to see all which will include framework and libraries.
            A <see cref="M:System.String.StartsWith(System.String)"/> is executed
            </remarks>
            <example>
            'System.', 'Microsoft.'
            </example>
        </member>
        <member name="P:Sentry.SentryOptions.InAppInclude">
            <summary>
            A list of namespaces (or prefixes) considered part of application code
            </summary>
            <remarks>
            Sentry by default filters the stacktrace to display only application code.
            A user can optionally click to see all which will include framework and libraries.
            A <see cref="M:System.String.StartsWith(System.String)"/> is executed
            </remarks>
            <example>
            'System.CustomNamespace', 'Microsoft.Azure.App'
            </example>
            <seealso href="https://docs.sentry.io/platforms/dotnet/guides/aspnet/configuration/options/#in-app-include"/>
        </member>
        <member name="P:Sentry.SentryOptions.SendDefaultPii">
            <summary>
            Whether to include default Personal Identifiable information
            </summary>
            <remarks>
            By default PII data like Username and Client IP address are not sent to Sentry.
            When this flag is turned on, default PII data like Cookies, Claims in Web applications
            and user data read from the request are sent.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IsEnvironmentUser">
            <summary>
            Whether to report the <see cref="P:System.Environment.UserName"/> as the User affected in the event.
            </summary>
            <remarks>
            This configuration is only relevant if <see cref="P:Sentry.SentryOptions.SendDefaultPii"/> is set to true.
            In environments like server applications this is set to false in order to not report server account names as user names.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.ServerName">
            <summary>
            Gets or sets the name of the server running the application.
            </summary>
            <remarks>
            When <see cref="P:Sentry.SentryOptions.SendDefaultPii"/> is set to <c>true</c>, <see cref="P:System.Environment.MachineName"/> is
            automatically set as ServerName. This property can serve as an override.
            This is relevant only to server applications.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.AttachStacktrace">
            <summary>
            Whether to send the stack trace of a event captured without an exception.
            As of version 3.22.0, the default is <c>true</c>.
            </summary>
            <remarks>
            Append stack trace of the call to the SDK to capture a message or event without Exception
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.MaxBreadcrumbs">
            <summary>
            Gets or sets the maximum breadcrumbs.
            </summary>
            <remarks>
            When the number of events reach this configuration value,
            older breadcrumbs start dropping to make room for new ones.
            </remarks>
            <value>
            The maximum breadcrumbs per scope.
            </value>
        </member>
        <member name="P:Sentry.SentryOptions.SampleRate">
            <summary>
            The rate to sample error and crash events.
            </summary>
            <remarks>
            Can be anything between 0.01 (1%) and 1.0 (99.9%) or null (default), to disable it.
            </remarks>
            <example>
            0.1 = 10% of events are sent
            </example>
            <see href="https://develop.sentry.dev/sdk/features/#event-sampling"/>
            <exception cref="T:System.InvalidOperationException"></exception>
        </member>
        <member name="P:Sentry.SentryOptions.Release">
            <summary>
            The release information for the application.
            Can be anything, but generally should be either a semantic version string in the format
            <c>package@version</c> or <c>package@version+build</c>, or a commit SHA from a version control system.
            </summary>
            <example>
            MyApp@1.2.3
            MyApp@1.2.3+foo
            721e41770371db95eee98ca2707686226b993eda
            14.1.16.32451
            </example>
            <remarks>
            This value will generally be something along the lines of the git SHA for the given project.
            If not explicitly defined via configuration or environment variable (SENTRY_RELEASE).
            It will attempt to read it from:
            <see cref="T:System.Reflection.AssemblyInformationalVersionAttribute"/>
            </remarks>
            <seealso href="https://docs.sentry.io/platforms/dotnet/configuration/releases/"/>
        </member>
        <member name="P:Sentry.SentryOptions.Distribution">
            <summary>
            The distribution of the application, associated with the release set in <see cref="P:Sentry.SentryOptions.Release"/>.
            </summary>
            <example>
            22
            14G60
            </example>
            <remarks>
            Distributions are used to disambiguate build or deployment variants of the same release of
            an application. For example, it can be the build number of an XCode (iOS) build, or the version
            code of an Android build.
            A distribution can be set under any circumstances, and is passed along to Sentry if provided.
            However, they are generally relevant only for mobile application scenarios.
            </remarks>
            <seealso href="https://develop.sentry.dev/sdk/event-payloads/#optional-attributes"/>
        </member>
        <member name="P:Sentry.SentryOptions.Environment">
            <summary>
            The environment the application is running
            </summary>
            <remarks>
            This value can also be set via environment variable: SENTRY_ENVIRONMENT
            In some cases you don't need to set this manually since integrations, when possible, automatically fill this value.
            For ASP.NET Core which can read from IHostingEnvironment
            </remarks>
            <example>
            Production, Staging
            </example>
            <seealso href="https://docs.sentry.io/platforms/dotnet/configuration/environments/"/>
        </member>
        <member name="P:Sentry.SentryOptions.Dsn">
            <summary>
            The Data Source Name of a given project in Sentry.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.BeforeSend">
            <summary>
            Configures a callback to invoke before sending an event to Sentry
            </summary>
            <see cref="M:Sentry.SentryOptions.SetBeforeBreadcrumb(System.Func{Sentry.Breadcrumb,Sentry.Hint,Sentry.Breadcrumb})"/>
        </member>
        <member name="M:Sentry.SentryOptions.SetBeforeSend(System.Func{Sentry.SentryEvent,Sentry.Hint,Sentry.SentryEvent})">
            <summary>
            Configures a callback function to be invoked before sending an event to Sentry
            </summary>
            <remarks>
            The event returned by this callback will be sent to Sentry. This allows the
            application a chance to inspect and/or modify the event before it's sent. If the
            event should not be sent at all, return null from the callback.
            </remarks>
        </member>
        <member name="M:Sentry.SentryOptions.SetBeforeSend(System.Func{Sentry.SentryEvent,Sentry.SentryEvent})">
            <summary>
            Configures a callback function to be invoked before sending an event to Sentry
            </summary>
            <remarks>
            The event returned by this callback will be sent to Sentry. This allows the
            application a chance to inspect and/or modify the event before it's sent. If the
            event should not be sent at all, return null from the callback.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.BeforeSendTransaction">
            <summary>
            A callback to invoke before sending a transaction to Sentry
            </summary>
            <remarks>
            The return of this transaction will be sent to Sentry. This allows the application
            a chance to inspect and/or modify the transaction before it's sent. If the transaction
            should not be sent at all, return null from the callback.
            </remarks>
        </member>
        <member name="M:Sentry.SentryOptions.SetBeforeSendTransaction(System.Func{Sentry.Transaction,Sentry.Hint,Sentry.Transaction})">
            <summary>
            Configures a callback to invoke before sending a transaction to Sentry
            </summary>
            <param name="beforeSendTransaction">The callback</param>
        </member>
        <member name="M:Sentry.SentryOptions.SetBeforeSendTransaction(System.Func{Sentry.Transaction,Sentry.Transaction})">
            <summary>
            Configures a callback to invoke before sending a transaction to Sentry
            </summary>
            <param name="beforeSendTransaction">The callback</param>
        </member>
        <member name="P:Sentry.SentryOptions.BeforeBreadcrumb">
            <summary>
            Sets a callback function to be invoked when a breadcrumb is about to be stored.
            </summary>
            <see cref="M:Sentry.SentryOptions.SetBeforeBreadcrumb(System.Func{Sentry.Breadcrumb,Sentry.Hint,Sentry.Breadcrumb})"/>
        </member>
        <member name="M:Sentry.SentryOptions.SetBeforeBreadcrumb(System.Func{Sentry.Breadcrumb,Sentry.Hint,Sentry.Breadcrumb})">
            <summary>
            Sets a callback function to be invoked when a breadcrumb is about to be stored.
            </summary>
            <remarks>
            Gives a chance to inspect and modify the breadcrumb. If null is returned, the
            breadcrumb will be discarded. Otherwise the result of the callback will be stored.
            </remarks>
        </member>
        <member name="M:Sentry.SentryOptions.SetBeforeBreadcrumb(System.Func{Sentry.Breadcrumb,Sentry.Breadcrumb})">
            <summary>
            Sets a callback function to be invoked when a breadcrumb is about to be stored.
            </summary>
            <remarks>
            Gives a chance to inspect and modify the breadcrumb. If null is returned, the
            breadcrumb will be discarded. Otherwise the result of the callback will be stored.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.MaxQueueItems">
            <summary>
            The maximum number of events to keep while the worker attempts to send them.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.MaxCacheItems">
            <summary>
            The maximum number of events to keep in cache.
            This option only works if <see cref="P:Sentry.SentryOptions.CacheDirectoryPath"/> is configured as well.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.ShutdownTimeout">
            <summary>
            How long to wait for events to be sent before shutdown
            </summary>
            <remarks>
            In case there are events queued when the SDK is closed, upper bound limit to wait
            for the worker to send the events to Sentry.
            </remarks>
            <example>
            The SDK is closed while the queue has 1 event queued.
            The worker takes 50 milliseconds to send an event to Sentry.
            Even though default settings say 2 seconds, closing the SDK would block for 50ms.
            </example>
        </member>
        <member name="P:Sentry.SentryOptions.FlushTimeout">
            <summary>
            How long to wait for flush operations to finish. Defaults to 2 seconds.
            </summary>
            <remarks>
            When using the <c>Sentry.NLog</c> integration, the default is increased to 15 seconds.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.DecompressionMethods">
            <summary>
            Decompression methods accepted
            </summary>
            <remarks>
            By default accepts all available compression methods supported by the platform
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.RequestBodyCompressionLevel">
            <summary>
            The level of which to compress the <see cref="T:Sentry.SentryEvent"/> before sending to Sentry
            </summary>
            <remarks>
            To disable request body compression, use <see cref="F:System.IO.Compression.CompressionLevel.NoCompression"/>
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.RequestBodyCompressionBuffered">
            <summary>
            Whether the body compression is buffered and the request 'Content-Length' known in advance.
            </summary>
            <remarks>
            Without reading through the Gzip stream to have its final size, it's no possible to use 'Content-Length'
            header value. That means 'Content-Encoding: chunked' has to be used which is sometimes not supported.
            Sentry on-premise without a reverse-proxy, for example, does not support 'chunked' requests.
            </remarks>
            <see href="https://github.com/getsentry/sentry-dotnet/issues/71"/>
        </member>
        <member name="P:Sentry.SentryOptions.SendClientReports">
            <summary>
            Whether to send client reports, which contain statistics about discarded events.
            </summary>
            <see href="https://develop.sentry.dev/sdk/client-reports/"/>
        </member>
        <member name="P:Sentry.SentryOptions.HttpProxy">
            <summary>
            An optional web proxy
            </summary>
        </member>
        <member name="F:Sentry.SentryOptions._createClientHandler">
            <summary>
            private field to hold the <see cref="P:Sentry.SentryOptions.CreateHttpClientHandler"/>, since a typecheck or cast won't work here.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.CreateHttpClientHandler">
            <summary>
            Creates the inner most <see cref="T:System.Net.Http.HttpClientHandler"/>.
            Deprecated in favor of <see cref="P:Sentry.SentryOptions.CreateHttpMessageHandler"/>.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.CreateHttpMessageHandler">
            <summary>
            Creates the inner most <see cref="T:System.Net.Http.HttpMessageHandler"/>.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.ConfigureClient">
            <summary>
            A callback invoked when a <see cref="T:Sentry.SentryClient"/> is created.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.Debug">
            <summary>
            Whether to log diagnostics messages
            </summary>
            <remarks>
            The verbosity can be controlled through <see cref="P:Sentry.SentryOptions.DiagnosticLevel"/>
            and the implementation via <see cref="P:Sentry.SentryOptions.DiagnosticLogger"/>.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.DiagnosticLevel">
            <summary>
            The diagnostics level to be used
            </summary>
            <remarks>
            The <see cref="P:Sentry.SentryOptions.Debug"/> flag has to be switched on for this setting to take effect.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.DiagnosticLogger">
            <summary>
            The implementation of the logger.
            </summary>
            <remarks>
            The <see cref="P:Sentry.SentryOptions.Debug"/> flag has to be switched on for this logger to be used at all.
            When debugging is turned off, this property is made null and any internal logging results in a no-op.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.ReportAssemblies">
            <summary>
            Whether or not to include referenced assemblies in each event sent to sentry. Defaults to <see langword="true"/>.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.ReportAssembliesMode">
            <summary>
            What mode to use for reporting referenced assemblies in each event sent to sentry. Defaults to <see cref="F:Sentry.ReportAssembliesMode.Version"/>.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.DeduplicateMode">
            <summary>
            What modes to use for event automatic deduplication
            </summary>
            <remarks>
            By default will not drop an event solely for including an inner exception that was already captured.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.CacheDirectoryPath">
            <summary>
            Path to the root directory used for storing events locally for resilience.
            If set to <i>null</i>, caching will not be used.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.CaptureFailedRequests">
            <summary>
            <para>The SDK will only capture HTTP Client errors if it is enabled.</para>
            <para><see cref="P:Sentry.SentryOptions.FailedRequestStatusCodes"/> can be used to configure which requests will be treated as failed.</para>
            <para>Also <see cref="P:Sentry.SentryOptions.FailedRequestTargets"/> can be used to filter to match only certain request URLs.</para>
            <para>Defaults to false due to PII reasons.</para>
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.FailedRequestStatusCodes">
            <summary>
            <para>The SDK will only capture HTTP Client errors if the HTTP Response status code is within these defined ranges.</para>
            <para>Defaults to 500-599 (Server error responses only).</para>
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.FailedRequestTargets">
            <summary>
            <para>The SDK will only capture HTTP Client errors if the HTTP Request URL is a match for any of the failedRequestsTargets.</para>
            <para>Targets may be URLs or Regular expressions.</para>
            <para>Matches "*." by default.</para>
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.FileSystem">
            <summary>
            Sets the filesystem instance to use. Defaults to the actual <see cref="T:Sentry.Internal.FileSystem"/>.
            Used for testing.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.InitCacheFlushTimeout">
            <summary>
            If set to a positive value, Sentry will attempt to flush existing local event cache when initializing.
            Set to <see cref="F:System.TimeSpan.Zero"/> to disable this feature.
            This option only works if <see cref="P:Sentry.SentryOptions.CacheDirectoryPath"/> is configured as well.
            </summary>
            <remarks>
            The trade off here is: Ensure a crash that happens during app start is sent to Sentry
            even though that might slow down the app start. If set to false, the app might crash
            too quickly, before Sentry can capture the cached error in the background.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.DefaultTags">
            <summary>
            Defaults tags to add to all events. (These are indexed by Sentry).
            </summary>
            <remarks>
            If the key already exists in the event, it will not be overwritten by a default tag.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.IsPerformanceMonitoringEnabled">
            <summary>
            Indicates whether the performance feature is enabled, via any combination of
            <see cref="P:Sentry.SentryOptions.EnableTracing"/>, <see cref="P:Sentry.SentryOptions.TracesSampleRate"/>, or <see cref="P:Sentry.SentryOptions.TracesSampler"/>.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.EnableTracing">
            <summary>
            Simplified option for enabling or disabling tracing.
            <list type="table">
              <listheader>
                <term>Value</term>
                <description>Effect</description>
              </listheader>
              <item>
                <term><c>true</c></term>
                <description>
                  Tracing is enabled. <see cref="P:Sentry.SentryOptions.TracesSampleRate"/> or <see cref="P:Sentry.SentryOptions.TracesSampler"/> will be used if set,
                  or 100% sample rate will be used otherwise.
                </description>
              </item>
              <item>
                <term><c>false</c></term>
                <description>
                  Tracing is disabled, regardless of <see cref="P:Sentry.SentryOptions.TracesSampleRate"/> or <see cref="P:Sentry.SentryOptions.TracesSampler"/>.
                </description>
              </item>
              <item>
                <term><c>null</c></term>
                <description>
                  <b>The default setting.</b>
                  Tracing is enabled only if <see cref="P:Sentry.SentryOptions.TracesSampleRate"/> or <see cref="P:Sentry.SentryOptions.TracesSampler"/> are set.
                </description>
              </item>
            </list>
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.TracesSampleRate">
            <summary>
            Indicates the percentage of the tracing data that is collected.
            <list type="table">
              <listheader>
                <term>Value</term>
                <description>Effect</description>
              </listheader>
              <item>
                <term><c>&gt;= 0.0 and &lt;=1.0</c></term>
                <description>
                  A custom sample rate is used unless <see cref="P:Sentry.SentryOptions.EnableTracing"/> is <c>false</c>,
                  or unless overriden by a <see cref="P:Sentry.SentryOptions.TracesSampler"/> function.
                  Values outside of this range are invalid.
                </description>
              </item>
              <item>
                <term><c>null</c></term>
                <description>
                  <b>The default setting.</b>
                  The tracing sample rate is determined by the <see cref="P:Sentry.SentryOptions.EnableTracing"/> property,
                  unless overriden by a <see cref="P:Sentry.SentryOptions.TracesSampler"/> function.
                </description>
              </item>
            </list>
            </summary>
            <remarks>
            Random sampling rate is only applied to transactions that don't already
            have a sampling decision set by other means, such as through <see cref="P:Sentry.SentryOptions.TracesSampler"/>,
            by inheriting it from an incoming trace header, or by copying it from <see cref="T:Sentry.TransactionContext"/>.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.TracesSampler">
            <summary>
            Custom delegate that returns sample rate dynamically for a specific transaction context.
            </summary>
            <remarks>
            Returning <c>null</c> signals that the sampler did not reach a sampling decision.
            In such case, if the transaction already has a sampling decision (for example, if it's
            started from a trace header) that decision is retained.
            Otherwise sampling decision is determined by applying the static sampling rate
            set in <see cref="P:Sentry.SentryOptions.TracesSampleRate"/>.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.TracePropagationTargets">
            <summary>
            A customizable list of <see cref="T:Sentry.TracePropagationTarget"/> objects, each containing either a
            substring or regular expression pattern that can be used to control which outgoing HTTP requests
            will have the <c>sentry-trace</c> and <c>baggage</c> headers propagated, for purposes of distributed tracing.
            The default value contains a single value of <c>.*</c>, which matches everything.
            To disable propagation completely, clear this collection or set it to an empty collection.
            </summary>
            <seealso href="https://develop.sentry.dev/sdk/performance/#tracepropagationtargets"/>
            <remarks>
            Adding an item to the default list will clear the <c>.*</c> value automatically.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.StackTraceMode">
            <summary>
            ATTENTION: This option will change how issues are grouped in Sentry!
            </summary>
            <remarks>
            Sentry groups events by stack traces. If you change this mode and you have thousands of groups,
            you'll get thousands of new groups. So use this setting with care.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.MaxAttachmentSize">
            <summary>
            Maximum allowed file size of attachments, in bytes.
            Attachments above this size will be discarded.
            </summary>
            <remarks>
            Regardless of this setting, attachments are also limited to 20mb (compressed) on Relay.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.DetectStartupTime">
            <summary>
            The mode that the SDK should use when attempting to detect the app's and device's startup time.
            </summary>
            <remarks>
            Note that the highest precision value relies on <see cref="M:System.Diagnostics.Process.GetCurrentProcess"/>
            which might not be available. For example on Unity's IL2CPP.
            Additionally, "Best" mode is not available on mobile platforms.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.AutoSessionTrackingInterval">
            <summary>
            Determines the duration of time a session can stay paused before it's considered ended.
            </summary>
            <remarks>
            Note: This interval is only taken into account when integrations support Pause and Resume.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.AutoSessionTracking">
            <summary>
            Whether the SDK should start a session automatically when it's initialized and
            end the session when it's closed.
            On mobile application platforms, this is enabled by default.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.UseAsyncFileIO">
            <summary>
            Whether the SDK should attempt to use asynchronous file I/O.
            For example, when reading a file to use as an attachment.
            </summary>
            <remarks>
            This option should rarely be disabled, but is necessary in some environments such as Unity WebGL.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.CrashedLastRun">
            <summary>
            Delegate which is used to check whether the application crashed during last run.
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.Instrumenter">
            <summary>
            <para>
                Gets the <see cref="P:Sentry.SentryOptions.Instrumenter"/> used to create spans.
            </para>
            <para>
                Defaults to <see cref="F:Sentry.Instrumenter.Sentry"/>
            </para>
            </summary>
        </member>
        <member name="P:Sentry.SentryOptions.KeepAggregateException">
            <summary>
            This property is no longer used.  It will be removed in a future version.
            </summary>
            <remarks>
            All exceptions are now sent to Sentry, including <see cref="T:System.AggregateException"/>s.
            The issue grouping rules in Sentry have been updated to accomodate "exception groups",
            such as <see cref="T:System.AggregateException"/> in .NET.
            </remarks>
        </member>
        <member name="M:Sentry.SentryOptions.AddJsonConverter(System.Text.Json.Serialization.JsonConverter)">
            <summary>
            Adds a <see cref="T:System.Text.Json.Serialization.JsonConverter"/> to be used when serializing or deserializing
            objects to JSON with this SDK.  For example, when custom context data might use
            a data type that requires custom serialization logic.
            </summary>
            <param name="converter">The <see cref="T:System.Text.Json.Serialization.JsonConverter"/> to add.</param>
            <remarks>
            This currently modifies a static list, so will affect any instance of the Sentry SDK.
            If that becomes problematic, we will have to refactor all serialization code to be
            able to accept an instance of <see cref="T:Sentry.SentryOptions"/>.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.JsonPreserveReferences">
            <summary>
            When <c>true</c>, if an object being serialized to JSON contains references to other objects, and the
            serialized object graph exceed the maximum allowable depth, the object will instead be serialized using
            <see cref="P:System.Text.Json.Serialization.ReferenceHandler.Preserve"/> (from System.Text.Json) - which adds <c>$id</c> and <c>$ref</c>
            metadata to the JSON.  When <c>false</c>, an object graph exceeding the maximum depth will be truncated.
            The default value is <c>true</c>.
            </summary>
            <remarks>
            This option applies only to complex objects being added to Sentry events as contexts or extras, which do not
            implement <see cref="T:Sentry.IJsonSerializable"/>.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.NetworkStatusListener">
            <summary>
            Provides a mechanism to convey network status to the caching transport, so that it does not attempt
            to send cached events to Sentry when the network is offline. Used internally by some integrations.
            Not intended for public usage.
            </summary>
            <remarks>
            This must be public because we use it in Sentry.Maui, which can't use InternalsVisibleTo
            because MAUI assemblies are not strong-named.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.AssemblyReader">
            <summary>
            Allows integrations to provide a custom assembly reader.
            </summary>
            <remarks>
            This is for Sentry use only, and can change without a major version bump.
            </remarks>
        </member>
        <member name="P:Sentry.SentryOptions.InitNativeSdks">
            <summary>
            Controls whether the native SDKs (Android, Cocoa, etc.) will be initialized (when applicable).
            Should be set <c>false</c> (disabled) only when testing, and then only if the test initializes the managed SDK.
            Defaults to <c>true</c> (enabled).
            </summary>
        </member>
        <member name="M:Sentry.SentryOptions.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Sentry.SentryOptions"/>
            </summary>
        </member>
        <member name="T:Sentry.SentrySdk">
            <summary>
            Sentry SDK entrypoint.
            </summary>
            <remarks>
            This is a facade to the SDK instance.
            It allows safe static access to a client and scope management.
            When the SDK is uninitialized, calls to this class result in no-op so no callbacks are invoked.
            </remarks>
        </member>
        <member name="P:Sentry.SentrySdk.LastEventId">
            <summary>
            Last event id recorded in the current scope.
            </summary>
        </member>
        <member name="M:Sentry.SentrySdk.Init">
            <summary>
            Initializes the SDK while attempting to locate the DSN.
            </summary>
            <remarks>
            If the DSN is not found, the SDK will not change state.
            </remarks>
            <returns>An object that can be disposed to disable the Sentry SDK, if desired.</returns>
            <remarks>
            Disposing the result will flush previously-captured events and disable the SDK.
            In most cases there's no need to dispose the result.  There are only a few exceptions where it makes
            sense to dispose:
            <list type="bullet">
            <item>You have additional work to perform that you don't want Sentry to monitor.</item>
            <item>You have used <see cref="M:Sentry.SentryOptionsExtensions.DisableAppDomainProcessExitFlush(Sentry.SentryOptions)"/>.</item>
            <item>You are integrating Sentry into an environment that has custom application lifetime events.</item>
            </list>
            </remarks>
        </member>
        <member name="M:Sentry.SentrySdk.Init(System.String)">
            <summary>
            Initializes the SDK with the specified DSN.
            </summary>
            <remarks>
            An empty string is interpreted as a disabled SDK.
            </remarks>
            <seealso href="https://develop.sentry.dev/sdk/overview/#usage-for-end-users"/>
            <param name="dsn">The dsn.</param>
            <returns>An object that can be disposed to disable the Sentry SDK, if desired.</returns>
            <remarks>
            Disposing the result will flush previously-captured events and disable the SDK.
            In most cases there's no need to dispose the result.  There are only a few exceptions where it makes
            sense to dispose:
            <list type="bullet">
            <item>You have additional work to perform that you don't want Sentry to monitor.</item>
            <item>You have used <see cref="M:Sentry.SentryOptionsExtensions.DisableAppDomainProcessExitFlush(Sentry.SentryOptions)"/>.</item>
            <item>You are integrating Sentry into an environment that has custom application lifetime events.</item>
            </list>
            </remarks>
        </member>
        <member name="M:Sentry.SentrySdk.Init(System.Action{Sentry.SentryOptions})">
            <summary>
            Initializes the SDK with an optional configuration options callback.
            </summary>
            <param name="configureOptions">The configuration options callback.</param>
            <returns>An object that can be disposed to disable the Sentry SDK, if desired.</returns>
            <remarks>
            Disposing the result will flush previously-captured events and disable the SDK.
            In most cases there's no need to dispose the result.  There are only a few exceptions where it makes
            sense to dispose:
            <list type="bullet">
            <item>You have additional work to perform that you don't want Sentry to monitor.</item>
            <item>You have used <see cref="M:Sentry.SentryOptionsExtensions.DisableAppDomainProcessExitFlush(Sentry.SentryOptions)"/>.</item>
            <item>You are integrating Sentry into an environment that has custom application lifetime events.</item>
            </list>
            </remarks>
        </member>
        <member name="M:Sentry.SentrySdk.Init(Sentry.SentryOptions)">
            <summary>
            Initializes the SDK with the specified options instance.
            </summary>
            <param name="options">The options instance</param>
            <remarks>
            Used by integrations which have their own delegates.
            </remarks>
            <returns>An object that can be disposed to disable the Sentry SDK, if desired.</returns>
            <remarks>
            Disposing the result will flush previously-captured events and disable the SDK.
            In most cases there's no need to dispose the result.  There are only a few exceptions where it makes
            sense to dispose:
            <list type="bullet">
            <item>You have additional work to perform that you don't want Sentry to monitor.</item>
            <item>You have used <see cref="M:Sentry.SentryOptionsExtensions.DisableAppDomainProcessExitFlush(Sentry.SentryOptions)"/>.</item>
            <item>You are integrating Sentry into an environment that has custom application lifetime events.</item>
            </list>
            </remarks>
        </member>
        <member name="M:Sentry.SentrySdk.Flush">
            <summary>
            Flushes the queue of captured events until the timeout set in <see cref="P:Sentry.SentryOptions.FlushTimeout"/>
            is reached.
            </summary>
            <remarks>
            Blocks synchronously. Prefer <see cref="M:Sentry.SentrySdk.FlushAsync"/> in async code.
            </remarks>
        </member>
        <member name="M:Sentry.SentrySdk.Flush(System.TimeSpan)">
            <summary>
            Flushes the queue of captured events until the timeout is reached.
            </summary>
            <param name="timeout">The amount of time allowed for flushing.</param>
            <remarks>
            Blocks synchronously. Prefer <see cref="M:Sentry.SentrySdk.FlushAsync(System.TimeSpan)"/> in async code.
            </remarks>
        </member>
        <member name="M:Sentry.SentrySdk.FlushAsync">
            <summary>
            Flushes the queue of captured events until the timeout set in <see cref="P:Sentry.SentryOptions.FlushTimeout"/>
            is reached.
            </summary>
            <returns>A task to await for the flush operation.</returns>
        </member>
        <member name="M:Sentry.SentrySdk.FlushAsync(System.TimeSpan)">
            <summary>
            Flushes the queue of captured events until the timeout is reached.
            </summary>
            <param name="timeout">The amount of time allowed for flushing.</param>
            <returns>A task to await for the flush operation.</returns>
        </member>
        <member name="M:Sentry.SentrySdk.Close">
            <summary>
            Close the SDK.
            </summary>
            <remarks>
            Flushes the events and disables the SDK.
            This method is mostly used for testing the library since
            Init returns a IDisposable that can be used to shutdown the SDK.
            </remarks>
        </member>
        <member name="P:Sentry.SentrySdk.IsEnabled">
            <summary>
            Whether the SDK is enabled or not.
            </summary>
        </member>
        <member name="M:Sentry.SentrySdk.PushScope``1(``0)">
            <summary>
            Creates a new scope that will terminate when disposed.
            </summary>
            <remarks>
            Pushes a new scope while inheriting the current scope's data.
            </remarks>
            <param name="state">A state object to be added to the scope.</param>
            <returns>A disposable that when disposed, ends the created scope.</returns>
        </member>
        <member name="M:Sentry.SentrySdk.PushScope">
            <summary>
            Creates a new scope that will terminate when disposed.
            </summary>
            <returns>A disposable that when disposed, ends the created scope.</returns>
        </member>
        <member name="M:Sentry.SentrySdk.BindClient(Sentry.ISentryClient)">
            <summary>
            Binds the client to the current scope.
            </summary>
            <param name="client">The client.</param>
        </member>
        <member name="M:Sentry.SentrySdk.AddBreadcrumb(System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String},Sentry.BreadcrumbLevel)">
            <summary>
            Adds a breadcrumb to the current Scope.
            </summary>
            <param name="message">
            If a message is provided it’s rendered as text and the whitespace is preserved.
            Very long text might be abbreviated in the UI.</param>
            <param name="category">
            Categories are dotted strings that indicate what the crumb is or where it comes from.
            Typically it’s a module name or a descriptive string.
            For instance ui.click could be used to indicate that a click happened in the UI or flask could be used to indicate that the event originated in the Flask framework.
            </param>
            <param name="type">
            The type of breadcrumb.
            The default type is default which indicates no specific handling.
            Other types are currently http for HTTP requests and navigation for navigation events.
            <seealso href="https://develop.sentry.dev/sdk/event-payloads/breadcrumbs/#breadcrumb-types"/>
            </param>
            <param name="data">
            Data associated with this breadcrumb.
            Contains a sub-object whose contents depend on the breadcrumb type.
            Additional parameters that are unsupported by the type are rendered as a key/value table.
            </param>
            <param name="level">Breadcrumb level.</param>
            <seealso href="https://develop.sentry.dev/sdk/event-payloads/breadcrumbs/"/>
        </member>
        <member name="M:Sentry.SentrySdk.AddBreadcrumb(Sentry.Infrastructure.ISystemClock,System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String},Sentry.BreadcrumbLevel)">
            <summary>
            Adds a breadcrumb to the current scope.
            </summary>
            <remarks>
            This overload is intended to be used by integrations only.
            The objective is to allow better testability by allowing control of the timestamp set to the breadcrumb.
            </remarks>
            <param name="clock">An optional <see cref="T:Sentry.Infrastructure.ISystemClock"/>.</param>
            <param name="message">The message.</param>
            <param name="category">The category.</param>
            <param name="type">The type.</param>
            <param name="data">The data.</param>
            <param name="level">The level.</param>
        </member>
        <member name="M:Sentry.SentrySdk.AddBreadcrumb(Sentry.Breadcrumb,Sentry.Hint)">
            <summary>
            Adds a breadcrumb to the current Scope.
            </summary>
            <param name="breadcrumb">The breadcrumb to be added</param>
            <param name="hint">A hint providing additional context that can be used in the BeforeBreadcrumb callback</param>
            <see cref="M:Sentry.SentrySdk.AddBreadcrumb(System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String},Sentry.BreadcrumbLevel)"/>
        </member>
        <member name="M:Sentry.SentrySdk.WithScope(System.Action{Sentry.Scope})">
            <summary>
            Runs the callback within a new scope.
            </summary>
            <remarks>
            Pushes a new scope, runs the callback, then pops the scope. Use this when you have significant work to
            perform within an isolated scope.  If you just need to configure scope for a single event, use the overloads
            of CaptureEvent, CaptureMessage and CaptureException that provide a callback to a configurable scope.
            </remarks>
            <see href="https://docs.sentry.io/platforms/dotnet/enriching-events/scopes/#local-scopes"/>
            <param name="scopeCallback">The callback to run with the one time scope.</param>
        </member>
        <member name="M:Sentry.SentrySdk.WithScope``1(System.Func{Sentry.Scope,``0})">
            <summary>
            Runs the callback within a new scope.
            </summary>
            <remarks>
            Pushes a new scope, runs the callback, then pops the scope. Use this when you have significant work to
            perform within an isolated scope.  If you just need to configure scope for a single event, use the overloads
            of CaptureEvent, CaptureMessage and CaptureException that provide a callback to a configurable scope.
            </remarks>
            <see href="https://docs.sentry.io/platforms/dotnet/enriching-events/scopes/#local-scopes"/>
            <param name="scopeCallback">The callback to run with the one time scope.</param>
            <returns>The result from the callback.</returns>
        </member>
        <member name="M:Sentry.SentrySdk.WithScopeAsync(System.Func{Sentry.Scope,System.Threading.Tasks.Task})">
            <summary>
            Runs the asynchronous callback within a new scope.
            </summary>
            <remarks>
            Asynchronous version of <see cref="M:Sentry.ISentryScopeManager.WithScope(System.Action{Sentry.Scope})"/>.
            Pushes a new scope, runs the callback, then pops the scope. Use this when you have significant work to
            perform within an isolated scope.  If you just need to configure scope for a single event, use the overloads
            of CaptureEvent, CaptureMessage and CaptureException that provide a callback to a configurable scope.
            </remarks>
            <see href="https://docs.sentry.io/platforms/dotnet/enriching-events/scopes/#local-scopes"/>
            <param name="scopeCallback">The callback to run with the one time scope.</param>
            <returns>An async task to await the callback.</returns>
        </member>
        <member name="M:Sentry.SentrySdk.WithScopeAsync``1(System.Func{Sentry.Scope,System.Threading.Tasks.Task{``0}})">
            <summary>
            Runs the asynchronous callback within a new scope.
            </summary>
            <remarks>
            Asynchronous version of <see cref="M:Sentry.ISentryScopeManager.WithScope(System.Action{Sentry.Scope})"/>.
            Pushes a new scope, runs the callback, then pops the scope. Use this when you have significant work to
            perform within an isolated scope.  If you just need to configure scope for a single event, use the overloads
            of CaptureEvent, CaptureMessage and CaptureException that provide a callback to a configurable scope.
            </remarks>
            <see href="https://docs.sentry.io/platforms/dotnet/enriching-events/scopes/#local-scopes"/>
            <param name="scopeCallback">The callback to run with the one time scope.</param>
            <returns>An async task to await the result of the callback.</returns>
        </member>
        <member name="M:Sentry.SentrySdk.ConfigureScope(System.Action{Sentry.Scope})">
            <summary>
            Configures the scope through the callback.
            </summary>
            <param name="configureScope">The configure scope callback.</param>
        </member>
        <member name="M:Sentry.SentrySdk.ConfigureScopeAsync(System.Func{Sentry.Scope,System.Threading.Tasks.Task})">
            <summary>
            Configures the scope asynchronously.
            </summary>
            <param name="configureScope">The configure scope callback.</param>
            <returns>The Id of the event.</returns>
        </member>
        <member name="M:Sentry.SentrySdk.CaptureEvent(Sentry.SentryEvent)">
            <summary>
            Captures the event.
            </summary>
            <param name="evt">The event.</param>
            <returns>The Id of the event.</returns>
        </member>
        <member name="M:Sentry.SentrySdk.CaptureEvent(Sentry.SentryEvent,Sentry.Scope)">
            <summary>
            Captures the event using the specified scope.
            </summary>
            <param name="evt">The event.</param>
            <param name="scope">The scope.</param>
            <returns>The Id of the event.</returns>
        </member>
        <member name="M:Sentry.SentrySdk.CaptureEvent(Sentry.SentryEvent,Sentry.Hint,Sentry.Scope)">
            <summary>
            Captures the event, passing a hint, using the specified scope.
            </summary>
            <param name="evt">The event.</param>
            <param name="hint">a hint for the event.</param>
            <param name="scope">The scope.</param>
            <returns>The Id of the event.</returns>
        </member>
        <member name="M:Sentry.SentrySdk.CaptureEvent(Sentry.SentryEvent,System.Action{Sentry.Scope})">
            <summary>
            Captures an event with a configurable scope.
            </summary>
            <remarks>
            This allows modifying a scope without affecting other events.
            </remarks>
            <param name="evt">The event.</param>
            <param name="configureScope">The callback to configure the scope.</param>
            <returns>The Id of the event.</returns>
        </member>
        <member name="M:Sentry.SentrySdk.CaptureException(System.Exception)">
            <summary>
            Captures the exception.
            </summary>
            <param name="exception">The exception.</param>
            <returns>The Id of the even.t</returns>
        </member>
        <member name="M:Sentry.SentrySdk.CaptureException(System.Exception,System.Action{Sentry.Scope})">
            <summary>
            Captures the exception with a configurable scope.
            </summary>
            <remarks>
            This allows modifying a scope without affecting other events.
            </remarks>
            <param name="exception">The exception.</param>
            <param name="configureScope">The callback to configure the scope.</param>
            <returns>The Id of the even.t</returns>
        </member>
        <member name="M:Sentry.SentrySdk.CaptureMessage(System.String,Sentry.SentryLevel)">
            <summary>
            Captures the message.
            </summary>
            <param name="message">The message to send.</param>
            <param name="level">The message level.</param>
            <returns>The Id of the event.</returns>
        </member>
        <member name="M:Sentry.SentrySdk.CaptureMessage(System.String,System.Action{Sentry.Scope},Sentry.SentryLevel)">
            <summary>
            Captures the message with a configurable scope.
            </summary>
            <remarks>
            This allows modifying a scope without affecting other events.
            </remarks>
            <param name="message">The message to send.</param>
            <param name="configureScope">The callback to configure the scope.</param>
            <param name="level">The message level.</param>
            <returns>The Id of the event.</returns>
        </member>
        <member name="M:Sentry.SentrySdk.CaptureUserFeedback(Sentry.UserFeedback)">
            <summary>
            Captures a user feedback.
            </summary>
            <param name="userFeedback">The user feedback to send to Sentry.</param>
        </member>
        <member name="M:Sentry.SentrySdk.CaptureUserFeedback(Sentry.SentryId,System.String,System.String,System.String)">
            <summary>
            Captures a user feedback.
            </summary>
            <param name="eventId">The event Id.</param>
            <param name="email">The user email.</param>
            <param name="comments">The user comments.</param>
            <param name="name">The optional username.</param>
        </member>
        <member name="M:Sentry.SentrySdk.CaptureTransaction(Sentry.Transaction)">
            <summary>
            Captures a transaction.
            </summary>
            <remarks>
            Note: this method is NOT meant to be called from user code!
            Instead, call <see cref="M:Sentry.ISpan.Finish"/> on the transaction.
            </remarks>
        </member>
        <member name="M:Sentry.SentrySdk.CaptureTransaction(Sentry.Transaction,Sentry.Hint)">
            <summary>
            Captures a transaction.
            </summary>
            <remarks>
            Note: this method is NOT meant to be called from user code!
            Instead, call <see cref="M:Sentry.ISpan.Finish"/> on the transaction.
            </remarks>
        </member>
        <member name="M:Sentry.SentrySdk.CaptureSession(Sentry.SessionUpdate)">
            <summary>
            Captures a session update.
            </summary>
        </member>
        <member name="M:Sentry.SentrySdk.StartTransaction(Sentry.ITransactionContext,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Starts a transaction.
            </summary>
        </member>
        <member name="M:Sentry.SentrySdk.StartTransaction(Sentry.ITransactionContext,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object},Sentry.DynamicSamplingContext)">
            <summary>
            Starts a transaction.
            </summary>
        </member>
        <member name="M:Sentry.SentrySdk.StartTransaction(Sentry.ITransactionContext)">
            <summary>
            Starts a transaction.
            </summary>
        </member>
        <member name="M:Sentry.SentrySdk.StartTransaction(System.String,System.String)">
            <summary>
            Starts a transaction.
            </summary>
        </member>
        <member name="M:Sentry.SentrySdk.StartTransaction(System.String,System.String,System.String)">
            <summary>
            Starts a transaction.
            </summary>
        </member>
        <member name="M:Sentry.SentrySdk.StartTransaction(System.String,System.String,Sentry.SentryTraceHeader)">
            <summary>
            Starts a transaction.
            </summary>
        </member>
        <member name="M:Sentry.SentrySdk.BindException(System.Exception,Sentry.ISpan)">
            <summary>
            Binds specified exception the specified span.
            </summary>
            <remarks>
            This method is used internally and is not meant for public use.
            </remarks>
        </member>
        <member name="M:Sentry.SentrySdk.GetSpan">
            <summary>
            Gets the last active span.
            </summary>
        </member>
        <member name="M:Sentry.SentrySdk.GetTraceHeader">
            <summary>
            Gets the Sentry trace header of the parent that allows tracing across services
            </summary>
        </member>
        <member name="M:Sentry.SentrySdk.GetBaggage">
            <summary>
            Gets the Sentry "baggage" header that allows tracing across services
            </summary>
        </member>
        <member name="M:Sentry.SentrySdk.ContinueTrace(System.String,System.String,System.String,System.String)">
            <summary>
            Continues a trace based on HTTP header values provided as strings.
            </summary>
            <remarks>
            If no "sentry-trace" header is provided a random trace ID and span ID is created.
            </remarks>
        </member>
        <member name="M:Sentry.SentrySdk.ContinueTrace(Sentry.SentryTraceHeader,Sentry.BaggageHeader,System.String,System.String)">
            <summary>
            Continues a trace based on HTTP header values.
            </summary>
            <remarks>
            If no "sentry-trace" header is provided a random trace ID and span ID is created.
            </remarks>
        </member>
        <member name="M:Sentry.SentrySdk.StartSession">
            <inheritdoc cref="M:Sentry.IHub.StartSession"/>
        </member>
        <member name="M:Sentry.SentrySdk.EndSession(Sentry.SessionEndStatus)">
            <inheritdoc cref="M:Sentry.IHub.EndSession(Sentry.SessionEndStatus)"/>
        </member>
        <member name="M:Sentry.SentrySdk.PauseSession">
            <inheritdoc cref="M:Sentry.IHub.PauseSession"/>
        </member>
        <member name="M:Sentry.SentrySdk.ResumeSession">
            <inheritdoc cref="M:Sentry.IHub.ResumeSession"/>
        </member>
        <member name="M:Sentry.SentrySdk.CauseCrash(Sentry.CrashType)">
            <summary>
            Deliberately crashes an application, which is useful for testing and demonstration purposes.
            </summary>
            <remarks>
            The method is marked obsolete only to discourage accidental misuse.
            We do not intend to remove it.
            </remarks>
        </member>
        <member name="T:Sentry.Protocol.App">
            <summary>
            Describes the application.
            </summary>
            <remarks>
            As opposed to the runtime, this is the actual application that
            was running and carries meta data about the current session.
            </remarks>
            <seealso href="https://develop.sentry.dev/sdk/event-payloads/contexts/"/>
        </member>
        <member name="F:Sentry.Protocol.App.Type">
            <summary>
            Tells Sentry which type of context this is.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.App.Identifier">
            <summary>
            Version-independent application identifier, often a dotted bundle ID.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.App.StartTime">
            <summary>
            Formatted UTC timestamp when the application was started by the user.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.App.Hash">
            <summary>
            Application specific device identifier.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.App.BuildType">
            <summary>
            String identifying the kind of build, e.g. testflight.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.App.Name">
            <summary>
            Human readable application name, as it appears on the platform.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.App.Version">
            <summary>
            Human readable application version, as it appears on the platform.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.App.Build">
            <summary>
            Internal build identifier, as it appears on the platform.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.App.Clone">
            <summary>
            Clones this instance.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.App.UpdateFrom(Sentry.Protocol.App)">
            <summary>
            Updates this instance with data from the properties in the <paramref name="source"/>,
            unless there is already a value in the existing property.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.App.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.App.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.Protocol.Browser">
            <summary>
            Carries information about the browser or user agent for web-related errors.
            This can either be the browser this event occurred in, or the user agent of a
            web request that triggered the event.
            </summary>
            <seealso href="https://develop.sentry.dev/sdk/event-payloads/contexts/"/>
        </member>
        <member name="F:Sentry.Protocol.Browser.Type">
            <summary>
            Tells Sentry which type of context this is.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Browser.Name">
            <summary>
            Display name of the browser application.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Browser.Version">
            <summary>
            Version string of the browser.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Browser.Clone">
            <summary>
            Clones this instance
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Browser.UpdateFrom(Sentry.Protocol.Browser)">
            <summary>
            Updates this instance with data from the properties in the <paramref name="source"/>,
            unless there is already a value in the existing property.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Browser.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Browser.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.Protocol.Device">
            <summary>
            Describes the device that caused the event. This is most appropriate for mobile applications.
            </summary>
            <seealso href="https://develop.sentry.dev/sdk/event-payloads/contexts/"/>
        </member>
        <member name="F:Sentry.Protocol.Device.Type">
            <summary>
            Tells Sentry which type of context this is.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.Timezone">
            <summary>
            The timezone of the device.
            </summary>
            <example>
            Europe/Vienna
            </example>
        </member>
        <member name="P:Sentry.Protocol.Device.Name">
            <summary>
            The name of the device. This is typically a hostname.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.Manufacturer">
            <summary>
            The manufacturer of the device.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.Brand">
            <summary>
            The brand of the device.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.Family">
            <summary>
            The family of the device.
            </summary>
            <remarks>
            This is normally the common part of model names across generations.
            </remarks>
            <example>
            iPhone, Samsung Galaxy
            </example>
        </member>
        <member name="P:Sentry.Protocol.Device.Model">
            <summary>
            The model name.
            </summary>
            <example>
            Samsung Galaxy S3
            </example>
        </member>
        <member name="P:Sentry.Protocol.Device.ModelId">
            <summary>
            An internal hardware revision to identify the device exactly.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.Architecture">
            <summary>
            The CPU architecture.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.BatteryLevel">
            <summary>
            If the device has a battery an integer defining the battery level (in the range 0-100).
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.IsCharging">
            <summary>
            True if the device is charging.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.IsOnline">
            <summary>
            True if the device has a internet connection.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.Orientation">
            <summary>
            This can be a string portrait or landscape to define the orientation of a device.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.Simulator">
            <summary>
            A boolean defining whether this device is a simulator or an actual device.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.MemorySize">
            <summary>
            Total system memory available in bytes.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.FreeMemory">
            <summary>
            Free system memory in bytes.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.UsableMemory">
            <summary>
            Memory usable for the app in bytes.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.LowMemory">
            <summary>
            True, if the device memory is low.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.StorageSize">
            <summary>
            Total device storage in bytes.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.FreeStorage">
            <summary>
            Free device storage in bytes.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.ExternalStorageSize">
            <summary>
            Total size of an attached external storage in bytes (e.g.: android SDK card).
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.ExternalFreeStorage">
            <summary>
            Free size of an attached external storage in bytes (e.g.: android SDK card).
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.ScreenResolution">
            <summary>
            The resolution of the screen.
            </summary>
            <example>
            800x600
            </example>
        </member>
        <member name="P:Sentry.Protocol.Device.ScreenDensity">
            <summary>
            The logical density of the display.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.ScreenDpi">
            <summary>
            The screen density as dots-per-inch.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.BootTime">
            <summary>
            A formatted UTC timestamp when the system was booted.
            </summary>
            <example>
            2018-02-08T12:52:12Z
            </example>
        </member>
        <member name="P:Sentry.Protocol.Device.ProcessorCount">
            <summary>
            Number of "logical processors".
            </summary>
            <example>
            8
            </example>
        </member>
        <member name="P:Sentry.Protocol.Device.CpuDescription">
            <summary>
            CPU description.
            </summary>
            <example>
            Intel(R) Core(TM)2 Quad CPU Q6600 @ 2.40GHz
            </example>
        </member>
        <member name="P:Sentry.Protocol.Device.ProcessorFrequency">
            <summary>
            Processor frequency in MHz. Note that the actual CPU frequency might vary depending on current load and power
            conditions, especially on low-powered devices like phones and laptops. On some platforms it's not possible
            to query the CPU frequency. Currently such platforms are iOS and WebGL.
            </summary>
            <example>
            2500
            </example>
        </member>
        <member name="P:Sentry.Protocol.Device.DeviceType">
            <summary>
            Kind of device the application is running on.
            </summary>
            <example>
            Unknown, Handheld, Console, Desktop
            </example>
        </member>
        <member name="P:Sentry.Protocol.Device.BatteryStatus">
            <summary>
            Status of the device's battery.
            </summary>
            <example>
            Unknown, Charging, Discharging, NotCharging, Full
            </example>
        </member>
        <member name="P:Sentry.Protocol.Device.DeviceUniqueIdentifier">
            <summary>
            Unique device identifier. Depends on the running platform.
            </summary>
            <example>
            iOS: UIDevice.identifierForVendor (UUID)
            Android: The generated Installation ID
            Windows Store Apps: AdvertisingManager::AdvertisingId (possible fallback to HardwareIdentification::GetPackageSpecificToken().Id)
            Windows Standalone: hash from the concatenation of strings taken from Computer System Hardware Classes
            </example>
            TODO: Investigate - Do ALL platforms now return a generated installation ID?
                  See https://github.com/getsentry/sentry-java/pull/1455
        </member>
        <member name="P:Sentry.Protocol.Device.SupportsVibration">
            <summary>
            Is vibration available on the device?
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.SupportsAccelerometer">
            <summary>
            Is accelerometer available on the device?
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.SupportsGyroscope">
            <summary>
            Is gyroscope available on the device?
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.SupportsAudio">
            <summary>
            Is audio available on the device?
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Device.SupportsLocationService">
            <summary>
            Is the device capable of reporting its location?
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Device.Clone">
            <summary>
            Clones this instance.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Device.UpdateFrom(Sentry.Protocol.Device)">
            <summary>
            Updates this instance with data from the properties in the <paramref name="source"/>,
            unless there is already a value in the existing property.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Device.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Device.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.Protocol.DeviceOrientation">
            <summary>
            Defines the orientation of a device.
            </summary>
        </member>
        <member name="F:Sentry.Protocol.DeviceOrientation.Portrait">
            <summary>
            Portrait.
            </summary>
        </member>
        <member name="F:Sentry.Protocol.DeviceOrientation.Landscape">
            <summary>
            Landscape.
            </summary>
        </member>
        <member name="T:Sentry.Protocol.Envelopes.AsyncJsonSerializable">
            <summary>
            Represents a task producing an object serializable to JSON format.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Envelopes.AsyncJsonSerializable.Source">
            <summary>
            Source object.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.AsyncJsonSerializable.CreateFrom``1(System.Threading.Tasks.Task{``0})">
            <summary>
            Initializes an instance of <see cref="T:Sentry.Protocol.Envelopes.AsyncJsonSerializable"/>.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.AsyncJsonSerializable.SerializeAsync(System.IO.Stream,Sentry.Extensibility.IDiagnosticLogger,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Envelopes.AsyncJsonSerializable.Serialize(System.IO.Stream,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="T:Sentry.Protocol.Envelopes.Envelope">
            <summary>
            Envelope.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Envelopes.Envelope.Header">
            <summary>
            Header associated with the envelope.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Envelopes.Envelope.Items">
            <summary>
            Envelope items.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.Envelope.#ctor(System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object},System.Collections.Generic.IReadOnlyList{Sentry.Protocol.Envelopes.EnvelopeItem})">
            <summary>
            Initializes an instance of <see cref="T:Sentry.Protocol.Envelopes.Envelope"/>.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.Envelope.TryGetEventId">
            <summary>
            Attempts to extract the value of "event_id" header if it's present.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.Envelope.TryGetEventId(Sentry.Extensibility.IDiagnosticLogger)">
            <summary>
            Attempts to extract the value of "event_id" header if it's present.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.Envelope.SerializeAsync(System.IO.Stream,Sentry.Extensibility.IDiagnosticLogger,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Envelopes.Envelope.Serialize(System.IO.Stream,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Envelopes.Envelope.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Envelopes.Envelope.FromEvent(Sentry.SentryEvent,Sentry.Extensibility.IDiagnosticLogger,System.Collections.Generic.IReadOnlyCollection{Sentry.Attachment},Sentry.SessionUpdate)">
            <summary>
            Creates an envelope that contains a single event.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.Envelope.FromUserFeedback(Sentry.UserFeedback)">
            <summary>
            Creates an envelope that contains a single user feedback.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.Envelope.FromTransaction(Sentry.Transaction)">
            <summary>
            Creates an envelope that contains a single transaction.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.Envelope.FromSession(Sentry.SessionUpdate)">
            <summary>
            Creates an envelope that contains a session update.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.Envelope.FromClientReport(Sentry.Internal.ClientReport)">
            <summary>
            Creates an envelope that contains a client report.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.Envelope.DeserializeAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Deserializes envelope from stream.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.Envelope.WithItem(Sentry.Protocol.Envelopes.EnvelopeItem)">
            <summary>
            Creates a new <see cref="T:Sentry.Protocol.Envelopes.Envelope"/> starting from the current one and appends the <paramref name="item"/> given.
            </summary>
            <param name="item">The <see cref="T:Sentry.Protocol.Envelopes.EnvelopeItem"/> to append.</param>
            <returns>A new <see cref="T:Sentry.Protocol.Envelopes.Envelope"/> with the same headers and items, including the new <paramref name="item"/>.</returns>
        </member>
        <member name="T:Sentry.Protocol.Envelopes.EnvelopeItem">
            <summary>
            Envelope item.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Envelopes.EnvelopeItem.Header">
            <summary>
            Header associated with this envelope item.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Envelopes.EnvelopeItem.Payload">
            <summary>
            Item payload.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.#ctor(System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object},Sentry.Protocol.Envelopes.ISerializable)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.Protocol.Envelopes.EnvelopeItem"/>.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.TryGetType">
            <summary>
            Tries to get item type.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.TryGetLength">
            <summary>
            Tries to get payload length.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.TryGetFileName">
            <summary>
            Returns the file name or null if no name exists.
            </summary>
            <returns>The file name or null.</returns>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.SerializeAsync(System.IO.Stream,Sentry.Extensibility.IDiagnosticLogger,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.Serialize(System.IO.Stream,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.FromEvent(Sentry.SentryEvent)">
            <summary>
            Creates an <see cref="T:Sentry.Protocol.Envelopes.EnvelopeItem"/> from <paramref name="event"/>.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.FromUserFeedback(Sentry.UserFeedback)">
            <summary>
            Creates an <see cref="T:Sentry.Protocol.Envelopes.EnvelopeItem"/> from <paramref name="sentryUserFeedback"/>.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.FromTransaction(Sentry.Transaction)">
            <summary>
            Creates an <see cref="T:Sentry.Protocol.Envelopes.EnvelopeItem"/> from <paramref name="transaction"/>.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.FromProfileInfo(System.Threading.Tasks.Task{Sentry.Protocol.ProfileInfo})">
            <summary>
            Creates an <see cref="T:Sentry.Protocol.Envelopes.EnvelopeItem"/> from <paramref name="source"/>.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.FromSession(Sentry.SessionUpdate)">
            <summary>
            Creates an <see cref="T:Sentry.Protocol.Envelopes.EnvelopeItem"/> from <paramref name="sessionUpdate"/>.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.FromAttachment(Sentry.Attachment)">
            <summary>
            Creates an <see cref="T:Sentry.Protocol.Envelopes.EnvelopeItem"/> from <paramref name="attachment"/>.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.FromClientReport(Sentry.Internal.ClientReport)">
            <summary>
            Creates an <see cref="T:Sentry.Protocol.Envelopes.EnvelopeItem"/> from <paramref name="report"/>.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.EnvelopeItem.DeserializeAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Deserializes envelope item from stream.
            </summary>
        </member>
        <member name="T:Sentry.Protocol.Envelopes.ISerializable">
            <summary>
            Represents a serializable entity.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.ISerializable.SerializeAsync(System.IO.Stream,Sentry.Extensibility.IDiagnosticLogger,System.Threading.CancellationToken)">
            <summary>
            Serializes the object to a stream asynchronously.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.ISerializable.Serialize(System.IO.Stream,Sentry.Extensibility.IDiagnosticLogger)">
            <summary>
            Serializes the object to a stream synchronously.
            </summary>
        </member>
        <member name="T:Sentry.Protocol.Envelopes.JsonSerializable">
            <summary>
            Represents an object serializable in JSON format.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Envelopes.JsonSerializable.Source">
            <summary>
            Source object.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.JsonSerializable.#ctor(Sentry.IJsonSerializable)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.Protocol.Envelopes.JsonSerializable"/>.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.JsonSerializable.SerializeAsync(System.IO.Stream,Sentry.Extensibility.IDiagnosticLogger,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Envelopes.JsonSerializable.Serialize(System.IO.Stream,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="T:Sentry.Protocol.Envelopes.StreamSerializable">
            <summary>
            Represents an object which is already serialized as a stream.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Envelopes.StreamSerializable.Source">
            <summary>
            Source stream.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.StreamSerializable.#ctor(System.IO.Stream)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.Protocol.Envelopes.StreamSerializable"/>.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Envelopes.StreamSerializable.SerializeAsync(System.IO.Stream,Sentry.Extensibility.IDiagnosticLogger,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Envelopes.StreamSerializable.Serialize(System.IO.Stream,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Envelopes.StreamSerializable.Dispose">
            <inheritdoc />
        </member>
        <member name="T:Sentry.Protocol.Gpu">
            <summary>
            Graphics device unit.
            </summary>
            <seealso href="https://develop.sentry.dev/sdk/event-payloads/contexts/#gpu-context"/>
        </member>
        <member name="F:Sentry.Protocol.Gpu.Type">
            <summary>
            Tells Sentry which type of context this is.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Gpu.Name">
            <summary>
            The name of the graphics device.
            </summary>
            <example>
            iPod touch: Apple A8 GPU
            Samsung S7: Mali-T880
            </example>
        </member>
        <member name="P:Sentry.Protocol.Gpu.Id">
            <summary>
            The PCI Id of the graphics device.
            </summary>
            <remarks>
            Combined with <see cref="P:Sentry.Protocol.Gpu.VendorId"/> uniquely identifies the GPU.
            </remarks>
        </member>
        <member name="P:Sentry.Protocol.Gpu.VendorId">
            <summary>
            The PCI vendor Id of the graphics device.
            </summary>
            <remarks>
            Combined with <see cref="P:Sentry.Protocol.Gpu.Id"/> uniquely identifies the GPU.
            </remarks>
            <seealso href="https://docs.microsoft.com/en-us/windows-hardware/drivers/install/identifiers-for-pci-devices"/>
            <seealso href="http://pci-ids.ucw.cz/read/PC/"/>
        </member>
        <member name="P:Sentry.Protocol.Gpu.VendorName">
            <summary>
            The vendor name reported by the graphic device.
            </summary>
            <example>
            Apple, ARM, WebKit
            </example>
        </member>
        <member name="P:Sentry.Protocol.Gpu.MemorySize">
            <summary>
            Total GPU memory available in mega-bytes.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Gpu.ApiType">
            <summary>
            Device type.
            </summary>
            <remarks>The low level API used.</remarks>
            <example>Metal, Direct3D11, OpenGLES3, PlayStation4, XboxOne</example>
        </member>
        <member name="P:Sentry.Protocol.Gpu.MultiThreadedRendering">
            <summary>
            Whether the GPU is multi-threaded rendering or not.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Gpu.Version">
            <summary>
            The Version of the API of the graphics device.
            </summary>
            <example>
            iPod touch: Metal
            Android: OpenGL ES 3.2 v1.r22p0-01rel0.f294e54ceb2cb2d81039204fa4b0402e
            WebGL Windows: OpenGL ES 3.0 (WebGL 2.0 (OpenGL ES 3.0 Chromium))
            OpenGL 2.0, Direct3D 9.0c
            </example>
        </member>
        <member name="P:Sentry.Protocol.Gpu.NpotSupport">
            <summary>
            The Non-Power-Of-Two support level.
            </summary>
            <example>
            Full
            </example>
        </member>
        <member name="P:Sentry.Protocol.Gpu.MaxTextureSize">
            <summary>
            Largest size of a texture that is supported by the graphics hardware.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Gpu.GraphicsShaderLevel">
            <summary>
            Approximate "shader capability" level of the graphics device.
            </summary>
            <example>
            Shader Model 2.0, OpenGL ES 3.0, Metal / OpenGL ES 3.1, 27 (unknown)
            </example>
        </member>
        <member name="P:Sentry.Protocol.Gpu.SupportsDrawCallInstancing">
            <summary>
            Is GPU draw call instancing supported?
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Gpu.SupportsRayTracing">
            <summary>
            Is ray tracing available on the device?
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Gpu.SupportsComputeShaders">
            <summary>
            Are compute shaders available on the device?
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Gpu.SupportsGeometryShaders">
            <summary>
            Are geometry shaders available on the device?
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Gpu.Clone">
            <summary>
            Clones this instance.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Gpu.UpdateFrom(Sentry.Protocol.Gpu)">
            <summary>
            Updates this instance with data from the properties in the <paramref name="source"/>,
            unless there is already a value in the existing property.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Gpu.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Gpu.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.Protocol.ITraceContext">
            <summary>
            Trace metadata stored in 'contexts.trace' on an event or transaction.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.ITraceContext.SpanId">
            <summary>
            Span ID.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.ITraceContext.ParentSpanId">
            <summary>
            Parent ID.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.ITraceContext.TraceId">
            <summary>
            Trace ID.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.ITraceContext.Operation">
            <summary>
            Operation.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.ITraceContext.Description">
            <summary>
            Description.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.ITraceContext.Status">
            <summary>
            Status.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.ITraceContext.IsSampled">
            <summary>
            Whether the span or transaction is sampled in (i.e. eligible for sending to Sentry).
            </summary>
        </member>
        <member name="T:Sentry.Protocol.Measurement">
            <summary>
            A measurement, containing a numeric value and a unit.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Measurement.Value">
            <summary>
            The numeric value of the measurement.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Measurement.Unit">
            <summary>
            The unit of measurement.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Measurement.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Measurement.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.Protocol.Mechanism">
            <summary>
            Sentry Exception Mechanism.
            </summary>
            <remarks>
            The exception mechanism is an optional field residing in the Exception Interface.
            It carries additional information about the way the exception was created on the target system.
            This includes general exception values obtained from operating system or runtime APIs, as well as mechanism-specific values.
            </remarks>
            <see href="https://develop.sentry.dev/sdk/event-payloads/exception/#exception-mechanism"/>
        </member>
        <member name="F:Sentry.Protocol.Mechanism.HandledKey">
            <summary>
            Key found inside of <c>Exception.Data</c> to inform if the exception was handled.
            </summary>
        </member>
        <member name="F:Sentry.Protocol.Mechanism.MechanismKey">
            <summary>
            Key found inside of <c>Exception.Data</c> to inform which mechanism captured the exception.
            </summary>
        </member>
        <member name="F:Sentry.Protocol.Mechanism.DescriptionKey">
            <summary>
            Key found inside of <c>Exception.Data</c> to provide a description of the mechanism.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Mechanism.Type">
            <summary>
            Required unique identifier of this mechanism determining rendering and processing of the mechanism data.
            Defaults to <c>"generic"</c>.
            </summary>
            <remarks>
            If <c>null</c>, empty, or whitespace are set, reverts to the default string <c>"generic"</c>.
            Nullability is for backwards compatibility, and may be removed in a future major version.
            </remarks>
        </member>
        <member name="P:Sentry.Protocol.Mechanism.Description">
            <summary>
            Optional human readable description of the error mechanism and a possible hint on how to solve this error.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Mechanism.Source">
            <summary>
            An optional value to explain the source of the exception.
            </summary>
            <remarks>
            For chained exceptions, this should be the property name where the exception was retrieved from its parent
            exception.  In .NET, either &quot;<see cref="P:System.Exception.InnerException"/>&quot; or <c>&quot;InnerExceptions[i]&quot;</c>
            (where <c>i</c> is replaced with the numeric index within <see cref="P:System.AggregateException.InnerExceptions"/>).
            </remarks>
        </member>
        <member name="P:Sentry.Protocol.Mechanism.HelpLink">
            <summary>
            Optional fully qualified URL to an online help resource, possible interpolated with error parameters.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Mechanism.Handled">
            <summary>
            Optional flag indicating whether the exception has been handled by the user (e.g. via try..catch).
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Mechanism.Synthetic">
            <summary>
            Optional flag indicating whether the exception is synthetic.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Mechanism.IsExceptionGroup">
            <summary>
            Whether the exception represents an exception group.
            In .NET, an <see cref="T:System.AggregateException"/>.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Mechanism.ExceptionId">
            <summary>
            A numeric identifier assigned to the exception by the SDK.
            </summary>
            <remarks>
            The SDK should assign a different ID to each exception in an event, starting with the root exception as 0,
            and incrementing thereafter. This ID can be used with <see cref="P:Sentry.Protocol.Mechanism.ParentId"/> to reconstruct the logical
            structure of an exception group.  When <c>null</c>, Sentry will assume that all exceptions in an event are
            in a single chain.
            </remarks>
        </member>
        <member name="P:Sentry.Protocol.Mechanism.ParentId">
            <summary>
            The parent exception's identifier, or <c>null</c> for the root exception.
            </summary>
            <remarks>
            This ID can be used with <see cref="P:Sentry.Protocol.Mechanism.ExceptionId"/> to reconstruct the logical structure of an exception group.
            </remarks>
        </member>
        <member name="P:Sentry.Protocol.Mechanism.Meta">
            <summary>
            Optional information from the operating system or runtime on the exception mechanism.
            </summary>
            <remarks>
            The mechanism meta data usually carries error codes reported by the runtime or operating system,
            along with a platform dependent interpretation of these codes.
            SDKs can safely omit code names and descriptions for well known error codes, as it will be filled out by Sentry.
            For proprietary or vendor-specific error codes, adding these values will give additional information to the user.
            </remarks>
            <see href="https://develop.sentry.dev/sdk/event-payloads/exception/#meta-information"/>
        </member>
        <member name="P:Sentry.Protocol.Mechanism.Data">
            <summary>
            Arbitrary extra data that might help the user understand the error thrown by this mechanism.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Mechanism.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Mechanism.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.Protocol.OperatingSystem">
            <summary>
            Represents Sentry's context for OS.
            </summary>
            <remarks>
            Defines the operating system that caused the event. In web contexts, this is the operating system of the browser (normally pulled from the User-Agent string).
            </remarks>
            <seealso href="https://develop.sentry.dev/sdk/event-payloads/contexts/#os-context"/>
        </member>
        <member name="F:Sentry.Protocol.OperatingSystem.Type">
            <summary>
            Tells Sentry which type of context this is.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.OperatingSystem.Name">
            <summary>
            The name of the operating system.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.OperatingSystem.Version">
            <summary>
            The version of the operating system.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.OperatingSystem.RawDescription">
            <summary>
            An optional raw description that Sentry can use in an attempt to normalize OS info.
            </summary>
            <remarks>
            When the system doesn't expose a clear API for <see cref="P:Sentry.Protocol.OperatingSystem.Name"/> and <see cref="P:Sentry.Protocol.OperatingSystem.Version"/>
            this field can be used to provide a raw system info (e.g: uname)
            </remarks>
        </member>
        <member name="P:Sentry.Protocol.OperatingSystem.Build">
            <summary>
            The internal build revision of the operating system.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.OperatingSystem.KernelVersion">
            <summary>
            If known, this can be an independent kernel version string. Typically
            this is something like the entire output of the 'uname' tool.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.OperatingSystem.Rooted">
            <summary>
            An optional boolean that defines if the OS has been jailbroken or rooted.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.OperatingSystem.Clone">
            <summary>
            Clones this instance
            </summary>
        </member>
        <member name="M:Sentry.Protocol.OperatingSystem.UpdateFrom(Sentry.Protocol.OperatingSystem)">
            <summary>
            Updates this instance with data from the properties in the <paramref name="source"/>,
            unless there is already a value in the existing property.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.OperatingSystem.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.OperatingSystem.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.Protocol.ProfileInfo">
            <summary>
            Profiling context information.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.ProfileInfo.EventId">
            <summary>
            Profile's event ID.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.ProfileInfo.Contexts">
            <inheritdoc />
        </member>
        <member name="T:Sentry.Protocol.Response">
             <summary>
             Sentry Response context interface.
             </summary>
             <example>
            {
              "contexts": {
                "response": {
                  "cookies": "PHPSESSID=298zf09hf012fh2; csrftoken=u32t4o3tb3gg43; _gat=1;",
                  "headers": {
                    "content-type": "text/html"
                    /// ...
                  },
                  "status_code": 500,
                  "body_size": 1000, // in bytes
                }
              }
            }
             </example>
             <see href="https://develop.sentry.dev/sdk/event-payloads/types/#responsecontext"/>
        </member>
        <member name="F:Sentry.Protocol.Response.Type">
            <summary>
            Tells Sentry which type of context this is.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Response.BodySize">
            <summary>
            Gets or sets the HTTP response body size.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Response.Cookies">
            <summary>
            Gets or sets (optional) cookie values
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Response.Data">
            <summary>
            Submitted data in whatever format makes most sense.
            </summary>
            <remarks>
            This data should not be provided by default as it can get quite large.
            </remarks>
            <value>The request payload.</value>
        </member>
        <member name="P:Sentry.Protocol.Response.Headers">
            <summary>
            Gets or sets the headers.
            </summary>
            <remarks>
            If a header appears multiple times it needs to be merged according to the HTTP standard for header merging.
            </remarks>
        </member>
        <member name="P:Sentry.Protocol.Response.StatusCode">
            <summary>
            Gets or sets the HTTP Status response code
            </summary>
            <value>The HTTP method.</value>
        </member>
        <member name="M:Sentry.Protocol.Response.Clone">
            <summary>
            Clones this instance.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Response.UpdateFrom(Sentry.Protocol.Response)">
            <summary>
            Updates this instance with data from the properties in the <paramref name="source"/>,
            unless there is already a value in the existing property.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Response.UpdateFrom(System.Object)">
            <summary>
            Updates this instance with data from the properties in the <paramref name="source"/>,
            unless there is already a value in the existing property.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Response.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Response.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.Protocol.Runtime">
            <summary>
            This describes a runtime in more detail.
            </summary>
            <remarks>
            Typically this context is used multiple times if multiple runtimes are involved (for instance if you have a JavaScript application running on top of JVM)
            </remarks>
            <seealso href="https://develop.sentry.dev/sdk/event-payloads/contexts/"/>
        </member>
        <member name="F:Sentry.Protocol.Runtime.Type">
            <summary>
            Tells Sentry which type of context this is.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Runtime.Name">
            <summary>
            The name of the runtime.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Runtime.Version">
            <summary>
            The version identifier of the runtime.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Runtime.RawDescription">
            <summary>
             An optional raw description that Sentry can use in an attempt to normalize Runtime info.
            </summary>
            <remarks>
            When the system doesn't expose a clear API for <see cref="P:Sentry.Protocol.Runtime.Name"/> and <see cref="P:Sentry.Protocol.Runtime.Version"/>
            this field can be used to provide a raw system info (e.g: .NET Framework 4.7.1).
            </remarks>
        </member>
        <member name="P:Sentry.Protocol.Runtime.Identifier">
            <summary>
            An optional .NET Runtime Identifier string.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Runtime.Build">
            <summary>
            An optional build number.
            </summary>
            <see href="https://docs.microsoft.com/en-us/dotnet/framework/migration-guide/how-to-determine-which-versions-are-installed"/>
        </member>
        <member name="M:Sentry.Protocol.Runtime.Clone">
            <summary>
            Clones this instance
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Runtime.UpdateFrom(Sentry.Protocol.Runtime)">
            <summary>
            Updates this instance with data from the properties in the <paramref name="source"/>,
            unless there is already a value in the existing property.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Runtime.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Runtime.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.Protocol.SampleProfile">
            <summary>
            Sentry sampling profiler output profile
            </summary>
        </member>
        <member name="F:Sentry.Protocol.SampleProfile.Sample.Timestamp">
            <summary>
            Timestamp in nanoseconds relative to the profile start.
            </summary>
        </member>
        <member name="T:Sentry.Protocol.SentryException">
            <summary>
            Sentry Exception interface.
            </summary>
            <see href="https://develop.sentry.dev/sdk/event-payloads/exception"/>
        </member>
        <member name="P:Sentry.Protocol.SentryException.Type">
            <summary>
            Exception Type.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.SentryException.Value">
            <summary>
            The exception value.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.SentryException.Module">
            <summary>
            The optional module, or package which the exception type lives in.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.SentryException.ThreadId">
            <summary>
            An optional value which refers to a thread in the threads interface.
            </summary>
            <seealso href="https://develop.sentry.dev/sdk/event-payloads/threads/"/>
            <seealso cref="T:Sentry.SentryThread"/>
        </member>
        <member name="P:Sentry.Protocol.SentryException.Stacktrace">
            <summary>
            Stack trace.
            </summary>
            <see href="https://develop.sentry.dev/sdk/event-payloads/stacktrace/"/>
        </member>
        <member name="P:Sentry.Protocol.SentryException.Mechanism">
            <summary>
            An optional mechanism that created this exception.
            </summary>
            <see href="https://develop.sentry.dev/sdk/event-payloads/exception/#exception-mechanism"/>
        </member>
        <member name="P:Sentry.Protocol.SentryException.Data">
            <summary>
            Arbitrary extra data that is related to this error.
            </summary>
            <remarks>
            This property is obsolete and should no longer be used.
            Anything added here will be ignored and not sent to Sentry.
            </remarks>
        </member>
        <member name="M:Sentry.Protocol.SentryException.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.SentryException.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.Protocol.Trace">
            <summary>
            Trace context data.
            </summary>
        </member>
        <member name="F:Sentry.Protocol.Trace.Type">
            <summary>
            Tells Sentry which type of context this is.
            </summary>
        </member>
        <member name="P:Sentry.Protocol.Trace.SpanId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Protocol.Trace.ParentSpanId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Protocol.Trace.TraceId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Protocol.Trace.Operation">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Protocol.Trace.Description">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Protocol.Trace.Status">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Protocol.Trace.IsSampled">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Trace.Clone">
            <summary>
            Clones this instance.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Trace.UpdateFrom(Sentry.Protocol.Trace)">
            <summary>
            Updates this instance with data from the properties in the <paramref name="source"/>,
            unless there is already a value in the existing property.
            </summary>
        </member>
        <member name="M:Sentry.Protocol.Trace.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Protocol.Trace.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses trace context from JSON.
            </summary>
        </member>
        <member name="T:Sentry.Reflection.AssemblyExtensions">
            <summary>
            Extension methods to <see cref="T:System.Reflection.Assembly"/>.
            </summary>
        </member>
        <member name="M:Sentry.Reflection.AssemblyExtensions.GetNameAndVersion(System.Reflection.Assembly)">
            <summary>
            Get the assemblies Name and Version.
            </summary>
            <remarks>
            Attempts to read the version from <see cref="T:System.Reflection.AssemblyInformationalVersionAttribute"/>.
            If not available, falls back to <see cref="P:System.Reflection.AssemblyName.Version"/>.
            </remarks>
            <param name="asm">The assembly to get the name and version from.</param>
            <returns>The SdkVersion.</returns>
        </member>
        <member name="T:Sentry.ReportAssembliesMode">
            <summary>
            Possible modes for reporting the assemblies.
            </summary>
        </member>
        <member name="F:Sentry.ReportAssembliesMode.None">
            <summary>
            Don't report any assemblies.
            </summary>
        </member>
        <member name="F:Sentry.ReportAssembliesMode.Version">
            <summary>
            Report assemblies and use the assembly version to determine the version.
            </summary>
        </member>
        <member name="F:Sentry.ReportAssembliesMode.InformationalVersion">
            <summary>
            Report assemblies and prefer the informational assembly version to determine the version. If
            the informational assembly version is not available, fall back to the assembly version.
            </summary>
        </member>
        <member name="T:Sentry.Request">
            <summary>
            Sentry HTTP interface.
            </summary>
            <example>
            "request": {
                "url": "http://absolute.uri/foo",
                "method": "POST",
                "api_target": "apiType",
                "data": {
                    "foo": "bar"
                },
                "query_string": "hello=world",
                "cookies": "foo=bar",
                "headers": {
                    "Content-Type": "text/html"
                },
                "env": {
                    "REMOTE_ADDR": "192.168.0.1"
                }
            }
            </example>
            <see href="https://develop.sentry.dev/sdk/event-payloads/request/"/>
        </member>
        <member name="P:Sentry.Request.Url">
            <summary>
            Gets or sets the full request URL, if available.
            </summary>
            <value>The request URL.</value>
        </member>
        <member name="P:Sentry.Request.Method">
            <summary>
            Gets or sets the method of the request.
            </summary>
            <value>The HTTP method.</value>
        </member>
        <member name="P:Sentry.Request.ApiTarget">
            <summary>
            Gets or sets the API target for the request (e.g. "graphql")
            </summary>
            <value>The API Target.</value>
        </member>
        <member name="P:Sentry.Request.Data">
            <summary>
            Submitted data in whatever format makes most sense.
            </summary>
            <remarks>
            This data should not be provided by default as it can get quite large.
            </remarks>
            <value>The request payload.</value>
        </member>
        <member name="P:Sentry.Request.QueryString">
            <summary>
            Gets or sets the unparsed query string.
            </summary>
            <value>The query string.</value>
        </member>
        <member name="P:Sentry.Request.Cookies">
            <summary>
            Gets or sets the cookies.
            </summary>
            <value>The cookies.</value>
        </member>
        <member name="P:Sentry.Request.Headers">
            <summary>
            Gets or sets the headers.
            </summary>
            <remarks>
            If a header appears multiple times it needs to be merged according to the HTTP standard for header merging.
            </remarks>
            <value>The headers.</value>
        </member>
        <member name="P:Sentry.Request.Env">
            <summary>
            Gets or sets the optional environment data.
            </summary>
            <remarks>
            This is where information such as IIS/CGI keys go that are not HTTP headers.
            </remarks>
            <value>The env.</value>
        </member>
        <member name="P:Sentry.Request.Other">
            <summary>
            Gets or sets some optional other data.
            </summary>
            <value>The other.</value>
        </member>
        <member name="M:Sentry.Request.Clone">
            <summary>
            Clones this instance.
            </summary>
            <remarks>
            This is a shallow copy.
            References like <see cref="P:Sentry.Request.Data"/> could hold a mutable, non-thread-safe object.
            </remarks>
        </member>
        <member name="M:Sentry.Request.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Request.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.Scope">
            <summary>
            Scope data to be sent with the event.
            </summary>
            <remarks>
            Scope data is sent together with any event captured
            during the lifetime of the scope.
            </remarks>
        </member>
        <member name="P:Sentry.Scope.HasEvaluated">
            <summary>
            Whether the <see cref="E:Sentry.Scope.OnEvaluating"/> event has already fired.
            </summary>
        </member>
        <member name="P:Sentry.Scope.ExceptionProcessors">
            <summary>
            A list of exception processors.
            </summary>
        </member>
        <member name="P:Sentry.Scope.EventProcessors">
            <summary>
            A list of event processors.
            </summary>
        </member>
        <member name="P:Sentry.Scope.TransactionProcessors">
            <summary>
            A list of event processors.
            </summary>
        </member>
        <member name="E:Sentry.Scope.OnEvaluating">
            <summary>
            An event that fires when the scope evaluates.
            </summary>
            <remarks>
            This allows registering an event handler that is invoked in case
            an event is about to be sent to Sentry. If an event is never sent,
            this event is never fired and the resources spared.
            It also allows registration at an early stage of the processing
            but execution at a later time, when more data is available.
            </remarks>
            <see cref="M:Sentry.Scope.Evaluate"/>
        </member>
        <member name="P:Sentry.Scope.Level">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Scope.Request">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Scope.Contexts">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Scope.User">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Scope.Platform">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Scope.Release">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Scope.Distribution">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Scope.Environment">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Scope.TransactionName">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Scope.Transaction">
            <summary>
            Transaction.
            </summary>
        </member>
        <member name="P:Sentry.Scope.Sdk">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Scope.Fingerprint">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Scope.Breadcrumbs">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Scope.Extra">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Scope.Tags">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Scope.Attachments">
            <summary>
            Attachments.
            </summary>
        </member>
        <member name="M:Sentry.Scope.#ctor(Sentry.SentryOptions)">
            <summary>
            Creates a scope with the specified options.
            </summary>
        </member>
        <member name="M:Sentry.Scope.AddBreadcrumb(Sentry.Breadcrumb)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Scope.AddBreadcrumb(Sentry.Breadcrumb,Sentry.Hint)">
            <summary>
            Adds a breadcrumb with a hint.
            </summary>
            <param name="breadcrumb">The breadcrumb</param>
            <param name="hint">A hint for use in the BeforeBreadcrumb callback</param>
        </member>
        <member name="M:Sentry.Scope.SetExtra(System.String,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Scope.SetTag(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Scope.UnsetTag(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Scope.AddAttachment(Sentry.Attachment)">
            <summary>
            Adds an attachment.
            </summary>
        </member>
        <member name="M:Sentry.Scope.Clear">
            <summary>
            Resets all the properties and collections within the scope to their default values.
            </summary>
        </member>
        <member name="M:Sentry.Scope.ClearAttachments">
            <summary>
            Clear all Attachments.
            </summary>
        </member>
        <member name="M:Sentry.Scope.ClearBreadcrumbs">
            <summary>
            Removes all Breadcrumbs from the scope.
            </summary>
        </member>
        <member name="M:Sentry.Scope.Apply(Sentry.IEventLike)">
            <summary>
            Applies the data from this scope to another event-like object.
            </summary>
            <param name="other">The scope to copy data to.</param>
            <remarks>
            Applies the data of 'from' into 'to'.
            If data in 'from' is null, 'to' is unmodified.
            Conflicting keys are not overriden.
            This is a shallow copy.
            </remarks>
        </member>
        <member name="M:Sentry.Scope.Apply(Sentry.Scope)">
            <summary>
            Applies data from one scope to another.
            </summary>
        </member>
        <member name="M:Sentry.Scope.Apply(System.Object)">
            <summary>
            Applies the state object into the scope.
            </summary>
            <param name="state">The state object to apply.</param>
        </member>
        <member name="M:Sentry.Scope.Clone">
            <summary>
            Clones the current <see cref="T:Sentry.Scope"/>.
            </summary>
        </member>
        <member name="M:Sentry.Scope.GetSpan">
            <summary>
            Obsolete.  Use the <see cref="P:Sentry.Scope.Span"/> property instead.
            </summary>
        </member>
        <member name="P:Sentry.Scope.Span">
            <summary>
            Gets or sets the active span, or <c>null</c> if none available.
            </summary>
            <remarks>
            If a span has been set on this property, it will become the active span until it is finished.
            Otherwise, the active span is the latest unfinished span on the transaction, presuming a transaction
            was set on the scope via the <see cref="P:Sentry.Scope.Transaction"/> property.
            </remarks>
        </member>
        <member name="T:Sentry.ScopeExtensions">
            <summary>
            Scope extensions.
            </summary>
        </member>
        <member name="M:Sentry.ScopeExtensions.GetAllEventProcessors(Sentry.Scope)">
            <summary>
            Invokes all event processor providers available.
            </summary>
            <param name="scope">The Scope which holds the processor providers.</param>
        </member>
        <member name="M:Sentry.ScopeExtensions.GetAllTransactionProcessors(Sentry.Scope)">
            <summary>
            Invokes all transaction processor providers available.
            </summary>
            <param name="scope">The Scope which holds the processor providers.</param>
        </member>
        <member name="M:Sentry.ScopeExtensions.GetAllExceptionProcessors(Sentry.Scope)">
            <summary>
            Invokes all exception processor providers available.
            </summary>
            <param name="scope">The Scope which holds the processor providers.</param>
        </member>
        <member name="M:Sentry.ScopeExtensions.AddExceptionProcessor(Sentry.Scope,Sentry.Extensibility.ISentryEventExceptionProcessor)">
            <summary>
            Add an exception processor.
            </summary>
            <param name="scope">The Scope to hold the processor.</param>
            <param name="processor">The exception processor.</param>
        </member>
        <member name="M:Sentry.ScopeExtensions.AddExceptionProcessors(Sentry.Scope,System.Collections.Generic.IEnumerable{Sentry.Extensibility.ISentryEventExceptionProcessor})">
            <summary>
            Add the exception processors.
            </summary>
            <param name="scope">The Scope to hold the processor.</param>
            <param name="processors">The exception processors.</param>
        </member>
        <member name="M:Sentry.ScopeExtensions.AddEventProcessor(Sentry.Scope,Sentry.Extensibility.ISentryEventProcessor)">
            <summary>
            Adds an event processor which is invoked when creating a <see cref="T:Sentry.SentryEvent"/>.
            </summary>
            <param name="scope">The Scope to hold the processor.</param>
            <param name="processor">The event processor.</param>
        </member>
        <member name="M:Sentry.ScopeExtensions.AddEventProcessor(Sentry.Scope,System.Func{Sentry.SentryEvent,Sentry.SentryEvent})">
            <summary>
            Adds an event processor which is invoked when creating a <see cref="T:Sentry.SentryEvent"/>.
            </summary>
            <param name="scope">The Scope to hold the processor.</param>
            <param name="processor">The event processor.</param>
        </member>
        <member name="M:Sentry.ScopeExtensions.AddEventProcessors(Sentry.Scope,System.Collections.Generic.IEnumerable{Sentry.Extensibility.ISentryEventProcessor})">
            <summary>
            Adds event processors which are invoked when creating a <see cref="T:Sentry.SentryEvent"/>.
            </summary>
            <param name="scope">The Scope to hold the processor.</param>
            <param name="processors">The event processors.</param>
        </member>
        <member name="M:Sentry.ScopeExtensions.AddTransactionProcessor(Sentry.Scope,Sentry.Extensibility.ISentryTransactionProcessor)">
            <summary>
            Adds an transaction processor which is invoked when creating a <see cref="T:Sentry.Transaction"/>.
            </summary>
            <param name="scope">The Scope to hold the processor.</param>
            <param name="processor">The transaction processor.</param>
        </member>
        <member name="M:Sentry.ScopeExtensions.AddTransactionProcessor(Sentry.Scope,System.Func{Sentry.Transaction,Sentry.Transaction})">
            <summary>
            Adds an transaction processor which is invoked when creating a <see cref="T:Sentry.Transaction"/>.
            </summary>
            <param name="scope">The Scope to hold the processor.</param>
            <param name="processor">The transaction processor.</param>
        </member>
        <member name="M:Sentry.ScopeExtensions.AddTransactionProcessors(Sentry.Scope,System.Collections.Generic.IEnumerable{Sentry.Extensibility.ISentryTransactionProcessor})">
            <summary>
            Adds transaction processors which are invoked when creating a <see cref="T:Sentry.Transaction"/>.
            </summary>
            <param name="scope">The Scope to hold the processor.</param>
            <param name="processors">The transaction processors.</param>
        </member>
        <member name="M:Sentry.ScopeExtensions.AddAttachment(Sentry.Scope,System.IO.Stream,System.String,Sentry.AttachmentType,System.String)">
            <summary>
            Adds an attachment.
            </summary>
            <remarks>
            Note: the stream must be seekable.
            </remarks>
        </member>
        <member name="M:Sentry.ScopeExtensions.AddAttachment(Sentry.Scope,System.Byte[],System.String,Sentry.AttachmentType,System.String)">
            <summary>
            Adds an attachment.
            </summary>
        </member>
        <member name="M:Sentry.ScopeExtensions.AddAttachment(Sentry.Scope,System.String,Sentry.AttachmentType,System.String)">
            <summary>
            Adds an attachment.
            </summary>
        </member>
        <member name="M:Sentry.ScopeExtensions.LastCreatedSpan(Sentry.Scope)">
            <summary>
            Gets the last opened span.
            </summary>
            <param name="scope">The scope.</param>
            <returns>The last span not finished or null.</returns>
        </member>
        <member name="T:Sentry.SdkVersion">
            <summary>
            Information about the SDK to be sent with the SentryEvent.
            </summary>
            <remarks>Requires Sentry version 8.4 or higher.</remarks>
        </member>
        <member name="P:Sentry.SdkVersion.Packages">
            <summary>
            SDK packages.
            </summary>
            <remarks>This property is not required.</remarks>
        </member>
        <member name="P:Sentry.SdkVersion.Name">
            <summary>
            SDK name.
            </summary>
        </member>
        <member name="P:Sentry.SdkVersion.Version">
            <summary>
            SDK Version.
            </summary>
        </member>
        <member name="M:Sentry.SdkVersion.AddPackage(System.String,System.String)">
            <summary>
            Add a package used to compose the SDK.
            </summary>
            <param name="name">The package name.</param>
            <param name="version">The package version.</param>
        </member>
        <member name="M:Sentry.SdkVersion.AddIntegration(System.String)">
            <summary>
            Add an integration used in the SDK.
            </summary>
            <param name="integration">The integrations name.</param>
        </member>
        <member name="M:Sentry.SdkVersion.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SdkVersion.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.SentryClient">
            <summary>
            Sentry client used to send events to Sentry.
            </summary>
            <remarks>
            This client captures events by queueing those to its
            internal background worker which sends events to Sentry.
            </remarks>
            <inheritdoc cref="T:Sentry.ISentryClient" />
            <inheritdoc cref="T:System.IDisposable" />
        </member>
        <member name="P:Sentry.SentryClient.IsEnabled">
            <summary>
            Whether the client is enabled.
            </summary>
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryClient.#ctor(Sentry.SentryOptions)">
            <summary>
            Creates a new instance of <see cref="T:Sentry.SentryClient"/>.
            </summary>
            <param name="options">The configuration for this client.</param>
        </member>
        <member name="M:Sentry.SentryClient.CaptureEvent(Sentry.SentryEvent,Sentry.Scope)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryClient.CaptureEvent(Sentry.SentryEvent,Sentry.Hint,Sentry.Scope)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryClient.CaptureUserFeedback(Sentry.UserFeedback)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryClient.CaptureTransaction(Sentry.Transaction)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryClient.CaptureTransaction(Sentry.Transaction,Sentry.Hint)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryClient.CaptureSession(Sentry.SessionUpdate)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryClient.FlushAsync(System.TimeSpan)">
            <summary>
            Flushes events asynchronously.
            </summary>
            <param name="timeout">How long to wait for flush to finish.</param>
            <returns>A task to await for the flush operation.</returns>
        </member>
        <member name="M:Sentry.SentryClient.CaptureEnvelope(Sentry.Protocol.Envelopes.Envelope)">
            <summary>
            Capture an envelope and queue it.
            </summary>
            <param name="envelope">The envelope.</param>
            <returns>true if the enveloped was queued, false otherwise.</returns>
        </member>
        <member name="M:Sentry.SentryClient.Dispose">
            <summary>
            Disposes this client
            </summary>
            <inheritdoc />
        </member>
        <member name="T:Sentry.SentryClientExtensions">
            <summary>
            Extension methods for <see cref="T:Sentry.ISentryClient"/>
            </summary>
        </member>
        <member name="M:Sentry.SentryClientExtensions.CaptureException(Sentry.ISentryClient,System.Exception)">
            <summary>
            Captures the exception.
            </summary>
            <param name="client">The Sentry client.</param>
            <param name="ex">The exception.</param>
            <returns>The Id of the event</returns>
        </member>
        <member name="M:Sentry.SentryClientExtensions.CaptureMessage(Sentry.ISentryClient,System.String,Sentry.SentryLevel)">
            <summary>
            Captures a message.
            </summary>
            <param name="client">The Sentry client.</param>
            <param name="message">The message to send.</param>
            <param name="level">The message level.</param>
            <returns>The Id of the event</returns>
        </member>
        <member name="M:Sentry.SentryClientExtensions.CaptureUserFeedback(Sentry.ISentryClient,Sentry.SentryId,System.String,System.String,System.String)">
            <summary>
            Captures a user feedback.
            </summary>
            <param name="client"></param>
            <param name="eventId">The event Id.</param>
            <param name="email">The user email.</param>
            <param name="comments">The user comments.</param>
            <param name="name">The optional username.</param>
        </member>
        <member name="M:Sentry.SentryClientExtensions.Flush(Sentry.ISentryClient)">
            <summary>
            Flushes the queue of captured events until the timeout set in <see cref="P:Sentry.SentryOptions.FlushTimeout"/>
            is reached.
            </summary>
            <param name="client">The Sentry client.</param>
            <remarks>
            Blocks synchronously. Prefer <see cref="M:Sentry.SentryClientExtensions.FlushAsync(Sentry.ISentryClient)"/> in async code.
            </remarks>
        </member>
        <member name="M:Sentry.SentryClientExtensions.Flush(Sentry.ISentryClient,System.TimeSpan)">
            <summary>
            Flushes the queue of captured events until the timeout is reached.
            </summary>
            <param name="client">The Sentry client.</param>
            <param name="timeout">The amount of time allowed for flushing.</param>
            <remarks>
            Blocks synchronously. Prefer <see cref="M:Sentry.ISentryClient.FlushAsync(System.TimeSpan)"/> in async code.
            </remarks>
        </member>
        <member name="M:Sentry.SentryClientExtensions.FlushAsync(Sentry.ISentryClient)">
            <summary>
            Flushes the queue of captured events until the timeout set in <see cref="P:Sentry.SentryOptions.FlushTimeout"/>
            is reached.
            </summary>
            <param name="client">The Sentry client.</param>
            <returns>A task to await for the flush operation.</returns>
        </member>
        <member name="T:Sentry.SentryEvent">
            <summary>
            An event to be sent to Sentry.
            </summary>
            <seealso href="https://develop.sentry.dev/sdk/event-payloads/" />
        </member>
        <member name="P:Sentry.SentryEvent.Exception">
            <summary>
            The <see cref="T:System.Exception"/> used to create this event.
            </summary>
            <remarks>
            The information from this exception is used by the Sentry SDK
            to add the relevant data to the event prior to sending to Sentry.
            </remarks>
        </member>
        <member name="P:Sentry.SentryEvent.EventId">
            <summary>
            The unique identifier of this event.
            </summary>
            <remarks>
            Hexadecimal string representing a uuid4 value.
            The length is exactly 32 characters (no dashes!).
            </remarks>
        </member>
        <member name="P:Sentry.SentryEvent.Timestamp">
            <summary>
            Indicates when the event was created.
            </summary>
            <example>2018-04-03T17:41:36</example>
        </member>
        <member name="P:Sentry.SentryEvent.Message">
            <summary>
            Gets the structured message that describes this event.
            </summary>
            <remarks>
            This helps Sentry group events together as the grouping happens
            on the template message instead of the result string message.
            </remarks>
            <example>
            SentryMessage will have a template like: 'user {0} logged in'
            Or structured logging template: '{user} has logged in'
            </example>
        </member>
        <member name="P:Sentry.SentryEvent.Logger">
            <summary>
            Name of the logger (or source) of the event.
            </summary>
        </member>
        <member name="P:Sentry.SentryEvent.Platform">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SentryEvent.ServerName">
            <summary>
            Identifies the computer from which the event was recorded.
            </summary>
        </member>
        <member name="P:Sentry.SentryEvent.Release">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SentryEvent.Distribution">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SentryEvent.SentryExceptions">
            <summary>
            The Sentry Exception interface.
            </summary>
        </member>
        <member name="P:Sentry.SentryEvent.SentryThreads">
            <summary>
            The Sentry Thread interface.
            </summary>
            <see href="https://develop.sentry.dev/sdk/event-payloads/threads/"/>
        </member>
        <member name="P:Sentry.SentryEvent.DebugImages">
            <summary>
            The Sentry Debug Meta Images interface.
            </summary>
            <see href="https://develop.sentry.dev/sdk/event-payloads/debugmeta#debug-images"/>
        </member>
        <member name="P:Sentry.SentryEvent.Modules">
            <summary>
            A list of relevant modules and their versions.
            </summary>
        </member>
        <member name="P:Sentry.SentryEvent.Level">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SentryEvent.TransactionName">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SentryEvent.Request">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SentryEvent.Contexts">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SentryEvent.User">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SentryEvent.Environment">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SentryEvent.Sdk">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SentryEvent.Fingerprint">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SentryEvent.Breadcrumbs">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SentryEvent.Extra">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SentryEvent.Tags">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryEvent.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Sentry.SentryEvent" />.
            </summary>
        </member>
        <member name="M:Sentry.SentryEvent.#ctor(System.Exception)">
            <summary>
            Creates a Sentry event with optional Exception details and default values like Id and Timestamp.
            </summary>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:Sentry.SentryEvent.AddBreadcrumb(Sentry.Breadcrumb)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryEvent.SetExtra(System.String,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryEvent.SetTag(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryEvent.UnsetTag(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryEvent.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryEvent.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.SentryGraphQLHttpMessageHandler">
            <summary>
            Special HTTP message handler that can be used to propagate Sentry headers and other contextual information.
            </summary>
        </member>
        <member name="M:Sentry.SentryGraphQLHttpMessageHandler.#ctor(System.Net.Http.HttpMessageHandler,Sentry.IHub)">
            <summary>
            Constructs an instance of <see cref="T:Sentry.SentryGraphQLHttpMessageHandler"/>.
            </summary>
            <param name="innerHandler">An inner message handler to delegate calls to.</param>
            <param name="hub">The Sentry hub.</param>
        </member>
        <member name="M:Sentry.SentryGraphQLHttpMessageHandler.ProcessRequest(System.Net.Http.HttpRequestMessage,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryGraphQLHttpMessageHandler.HandleResponse(System.Net.Http.HttpResponseMessage,Sentry.ISpan,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="T:Sentry.SentryHttpMessageHandler">
            <summary>
            Special HTTP message handler that can be used to propagate Sentry headers and other contextual information.
            </summary>
        </member>
        <member name="M:Sentry.SentryHttpMessageHandler.#ctor">
            <summary>
            Constructs an instance of <see cref="T:Sentry.SentryHttpMessageHandler"/>.
            </summary>
        </member>
        <member name="M:Sentry.SentryHttpMessageHandler.#ctor(System.Net.Http.HttpMessageHandler)">
            <summary>
            Constructs an instance of <see cref="T:Sentry.SentryHttpMessageHandler"/>.
            </summary>
            <param name="innerHandler">An inner message handler to delegate calls to.</param>
        </member>
        <member name="M:Sentry.SentryHttpMessageHandler.#ctor(Sentry.IHub)">
            <summary>
            Constructs an instance of <see cref="T:Sentry.SentryHttpMessageHandler"/>.
            </summary>
            <param name="hub">The Sentry hub.</param>
        </member>
        <member name="M:Sentry.SentryHttpMessageHandler.#ctor(System.Net.Http.HttpMessageHandler,Sentry.IHub)">
            <summary>
            Constructs an instance of <see cref="T:Sentry.SentryHttpMessageHandler"/>.
            </summary>
            <param name="innerHandler">An inner message handler to delegate calls to.</param>
            <param name="hub">The Sentry hub.</param>
        </member>
        <member name="M:Sentry.SentryHttpMessageHandler.ProcessRequest(System.Net.Http.HttpRequestMessage,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryHttpMessageHandler.HandleResponse(System.Net.Http.HttpResponseMessage,Sentry.ISpan,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="T:Sentry.SentryId">
            <summary>
            The identifier of an event in Sentry.
            </summary>
        </member>
        <member name="F:Sentry.SentryId.Empty">
            <summary>
            An empty sentry id.
            </summary>
        </member>
        <member name="M:Sentry.SentryId.#ctor(System.Guid)">
            <summary>
            Creates a new instance of a Sentry Id.
            </summary>
        </member>
        <member name="M:Sentry.SentryId.ToString">
            <summary>
            Sentry Id in the format Sentry recognizes.
            </summary>
            <remarks>
            Default <see cref="M:Sentry.SentryId.ToString"/> of <see cref="T:System.Guid"/> includes
            dashes which sentry doesn't expect when searching events.
            </remarks>
            <returns>String representation of the event id.</returns>
        </member>
        <member name="M:Sentry.SentryId.Equals(Sentry.SentryId)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryId.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryId.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryId.Create">
            <summary>
            Generates a new Sentry ID.
            </summary>
        </member>
        <member name="M:Sentry.SentryId.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryId.Parse(System.String)">
            <summary>
            Parses from string.
            </summary>
        </member>
        <member name="M:Sentry.SentryId.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="M:Sentry.SentryId.op_Equality(Sentry.SentryId,Sentry.SentryId)">
            <summary>
            Equality operator.
            </summary>
        </member>
        <member name="M:Sentry.SentryId.op_Inequality(Sentry.SentryId,Sentry.SentryId)">
            <summary>
            Equality operator.
            </summary>
        </member>
        <member name="M:Sentry.SentryId.op_Implicit(Sentry.SentryId)~System.Guid">
            <summary>
            The <see cref="T:System.Guid"/> from the <see cref="T:Sentry.SentryId"/>.
            </summary>
        </member>
        <member name="M:Sentry.SentryId.op_Implicit(System.Guid)~Sentry.SentryId">
            <summary>
            A <see cref="T:Sentry.SentryId"/> from a <see cref="T:System.Guid"/>.
            </summary>
        </member>
        <member name="T:Sentry.SentryLevel">
            <summary>
            The level of the event sent to Sentry.
            </summary>
        </member>
        <member name="F:Sentry.SentryLevel.Debug">
            <summary>
            Debug.
            </summary>
        </member>
        <member name="F:Sentry.SentryLevel.Info">
            <summary>
            Informational.
            </summary>
        </member>
        <member name="F:Sentry.SentryLevel.Warning">
            <summary>
            Warning.
            </summary>
        </member>
        <member name="F:Sentry.SentryLevel.Error">
            <summary>
            Error.
            </summary>
        </member>
        <member name="F:Sentry.SentryLevel.Fatal">
            <summary>
            Fatal.
            </summary>
        </member>
        <member name="T:Sentry.SentryMessage">
            <summary>
            Sentry Message interface.
            </summary>
            <remarks>
            This interface enables support to structured logging.
            </remarks>
            <example>
            "sentry.interfaces.Message": {
              "message": "Message for event: {eventId}",
              "params": [10]
            }
            </example>
            <seealso href="https://develop.sentry.dev/sdk/event-payloads/message/"/>
        </member>
        <member name="P:Sentry.SentryMessage.Message">
            <summary>
            The raw message string (un-interpolated).
            </summary>
            <remarks>
            Must be no more than 1000 characters in length.
            </remarks>
        </member>
        <member name="P:Sentry.SentryMessage.Params">
            <summary>
            The optional list of formatting parameters.
            </summary>
        </member>
        <member name="P:Sentry.SentryMessage.Formatted">
            <summary>
            The formatted message.
            </summary>
        </member>
        <member name="M:Sentry.SentryMessage.op_Implicit(System.String)~Sentry.SentryMessage">
            <summary>
            Coerces <see cref="T:System.String"/> into <see cref="T:Sentry.SentryMessage"/>.
            </summary>
        </member>
        <member name="M:Sentry.SentryMessage.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryMessage.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.SentryMessageHandler">
            <summary>
            Special HTTP message handler that can be used to propagate Sentry headers and other contextual information.
            </summary>
        </member>
        <member name="M:Sentry.SentryMessageHandler.#ctor">
            <summary>
            Constructs an instance of <see cref="T:Sentry.SentryMessageHandler"/>.
            </summary>
        </member>
        <member name="M:Sentry.SentryMessageHandler.#ctor(System.Net.Http.HttpMessageHandler)">
            <summary>
            Constructs an instance of <see cref="T:Sentry.SentryMessageHandler"/>.
            </summary>
            <param name="innerHandler">An inner message handler to delegate calls to.</param>
        </member>
        <member name="M:Sentry.SentryMessageHandler.#ctor(Sentry.IHub)">
            <summary>
            Constructs an instance of <see cref="T:Sentry.SentryMessageHandler"/>.
            </summary>
            <param name="hub">The Sentry hub.</param>
        </member>
        <member name="M:Sentry.SentryMessageHandler.#ctor(System.Net.Http.HttpMessageHandler,Sentry.IHub)">
            <summary>
            Constructs an instance of <see cref="T:Sentry.SentryMessageHandler"/>.
            </summary>
            <param name="innerHandler">An inner message handler to delegate calls to.</param>
            <param name="hub">The Sentry hub.</param>
        </member>
        <member name="M:Sentry.SentryMessageHandler.ProcessRequest(System.Net.Http.HttpRequestMessage,System.String,System.String)">
            <summary>
            Starts a span for a request
            </summary>
            <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage"/></param>
            <param name="method">The request method (e.g. "GET")</param>
            <param name="url">The request URL</param>
            <returns>An <see cref="T:Sentry.ISpan"/></returns>
        </member>
        <member name="M:Sentry.SentryMessageHandler.HandleResponse(System.Net.Http.HttpResponseMessage,Sentry.ISpan,System.String,System.String)">
            <summary>
            Provides an opportunity for further processing of the span once a response is received.
            </summary>
            <param name="response">The <see cref="T:System.Net.Http.HttpResponseMessage"/></param>
            <param name="span">The <see cref="T:Sentry.ISpan"/> created in <see cref="M:Sentry.SentryMessageHandler.ProcessRequest(System.Net.Http.HttpRequestMessage,System.String,System.String)"/></param>
            <param name="method">The request method (e.g. "GET")</param>
            <param name="url">The request URL</param>
        </member>
        <member name="M:Sentry.SentryMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryMessageHandler.Send(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="T:Sentry.SentryOptionsExtensions">
            <summary>
            SentryOptions extensions.
            </summary>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.DisableDuplicateEventDetection(Sentry.SentryOptions)">
            <summary>
            Disables the strategy to detect duplicate events.
            </summary>
            <remarks>
            In case a second event is being sent out from the same exception, that event will be discarded.
            It is possible the second event had in fact more data. In which case it'd be ideal to avoid the first
            event going out in the first place.
            </remarks>
            <param name="options">The SentryOptions to remove the processor from.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.DisableAppDomainUnhandledExceptionCapture(Sentry.SentryOptions)">
            <summary>
            Disables the capture of errors through <see cref="E:System.AppDomain.UnhandledException"/>.
            </summary>
            <param name="options">The SentryOptions to remove the integration from.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.DisableDiagnosticSourceIntegration(Sentry.SentryOptions)">
            <summary>
            Disables the integrations with Diagnostic source.
            </summary>
            <param name="options">The SentryOptions to remove the integration from.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.DisableTaskUnobservedTaskExceptionCapture(Sentry.SentryOptions)">
            <summary>
            Disables the capture of errors through <see cref="E:System.Threading.Tasks.TaskScheduler.UnobservedTaskException"/>.
            </summary>
            <param name="options">The SentryOptions to remove the integration from.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.DisableUnobservedTaskExceptionCapture(Sentry.SentryOptions)">
            <summary>
            Disables the capture of errors through <see cref="E:System.Threading.Tasks.TaskScheduler.UnobservedTaskException"/>.
            </summary>
            <param name="options">The SentryOptions to remove the integration from.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.DisableAppDomainProcessExitFlush(Sentry.SentryOptions)">
            <summary>
            By default, any queued events (i.e: captures errors) are flushed on <see cref="E:System.AppDomain.ProcessExit"/>.
            This method disables that behaviour.
            </summary>
            <param name="options">The SentryOptions to remove the integration from.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.DisableWinUiUnhandledExceptionIntegration(Sentry.SentryOptions)">
            <summary>
            Disables WinUI exception handler
            </summary>
            <param name="options">The SentryOptions to remove the integration from.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.AddIntegration(Sentry.SentryOptions,Sentry.Integrations.ISdkIntegration)">
            <summary>
            Add an integration
            </summary>
            <param name="options">The SentryOptions to hold the processor.</param>
            <param name="integration">The integration.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.RemoveIntegration``1(Sentry.SentryOptions)">
            <summary>
            Removes all integrations of type <typeparamref name="TIntegration"/>.
            </summary>
            <typeparam name="TIntegration">The type of the integration(s) to remove.</typeparam>
            <param name="options">The SentryOptions to remove the integration(s) from.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.AddExceptionFilter(Sentry.SentryOptions,Sentry.Extensibility.IExceptionFilter)">
            <summary>
            Add an exception filter.
            </summary>
            <param name="options">The SentryOptions to hold the processor.</param>
            <param name="exceptionFilter">The exception filter to add.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.RemoveExceptionFilter``1(Sentry.SentryOptions)">
            <summary>
            Removes all filters of type <typeparamref name="TFilter"/>
            </summary>
            <typeparam name="TFilter">The type of filter(s) to remove.</typeparam>
            <param name="options">The SentryOptions to remove the filter(s) from.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.AddExceptionFilterForType``1(Sentry.SentryOptions)">
            <summary>
            Ignore exception of type <typeparamref name="TException"/> or derived.
            </summary>
            <typeparam name="TException">The type of the exception to ignore.</typeparam>
            <param name="options">The SentryOptions to store the exceptions type ignore.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.AddInAppExclude(Sentry.SentryOptions,System.String)">
            <summary>
            Add prefix to exclude from 'InApp' stacktrace list.
            </summary>
            <param name="options">The SentryOptions which holds the stacktrace list.</param>
            <param name="prefix">The string used to filter the stacktrace to be excluded from InApp.</param>
            <remarks>
            Sentry by default filters the stacktrace to display only application code.
            A user can optionally click to see all which will include framework and libraries.
            A <see cref="M:System.String.StartsWith(System.String)"/> is executed
            </remarks>
            <example>
            'System.', 'Microsoft.'
            </example>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.AddInAppInclude(Sentry.SentryOptions,System.String)">
            <summary>
            Add prefix to include as in 'InApp' stacktrace.
            </summary>
            <param name="options">The SentryOptions which holds the stacktrace list.</param>
            <param name="prefix">The string used to filter the stacktrace to be included in InApp.</param>
            <remarks>
            Sentry by default filters the stacktrace to display only application code.
            A user can optionally click to see all which will include framework and libraries.
            A <see cref="M:System.String.StartsWith(System.String)"/> is executed
            </remarks>
            <example>
            'System.CustomNamespace', 'Microsoft.Azure.App'
            </example>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.AddExceptionProcessor(Sentry.SentryOptions,Sentry.Extensibility.ISentryEventExceptionProcessor)">
            <summary>
            Add an exception processor.
            </summary>
            <param name="options">The SentryOptions to hold the processor.</param>
            <param name="processor">The exception processor.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.AddExceptionProcessors(Sentry.SentryOptions,System.Collections.Generic.IEnumerable{Sentry.Extensibility.ISentryEventExceptionProcessor})">
            <summary>
            Add the exception processors.
            </summary>
            <param name="options">The SentryOptions to hold the processor.</param>
            <param name="processors">The exception processors.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.AddEventProcessor(Sentry.SentryOptions,Sentry.Extensibility.ISentryEventProcessor)">
            <summary>
            Adds an event processor which is invoked when creating a <see cref="T:Sentry.SentryEvent"/>.
            </summary>
            <param name="options">The SentryOptions to hold the processor.</param>
            <param name="processor">The event processor.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.AddEventProcessors(Sentry.SentryOptions,System.Collections.Generic.IEnumerable{Sentry.Extensibility.ISentryEventProcessor})">
            <summary>
            Adds event processors which are invoked when creating a <see cref="T:Sentry.SentryEvent"/>.
            </summary>
            <param name="options">The SentryOptions to hold the processor.</param>
            <param name="processors">The event processors.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.RemoveEventProcessor``1(Sentry.SentryOptions)">
            <summary>
            Removes all event processors of type <typeparamref name="TProcessor"/>
            </summary>
            <typeparam name="TProcessor">The type of processor(s) to remove.</typeparam>
            <param name="options">The SentryOptions to remove the processor(s) from.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.AddEventProcessorProvider(Sentry.SentryOptions,System.Func{System.Collections.Generic.IEnumerable{Sentry.Extensibility.ISentryEventProcessor}})">
            <summary>
            Adds an event processor provider which is invoked when creating a <see cref="T:Sentry.SentryEvent"/>.
            </summary>
            <param name="options">The SentryOptions to hold the processor provider.</param>
            <param name="processorProvider">The event processor provider.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.AddTransactionProcessor(Sentry.SentryOptions,Sentry.Extensibility.ISentryTransactionProcessor)">
            <summary>
            Adds an transaction processor which is invoked when creating a <see cref="T:Sentry.Transaction"/>.
            </summary>
            <param name="options">The SentryOptions to hold the processor.</param>
            <param name="processor">The transaction processor.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.AddTransactionProcessors(Sentry.SentryOptions,System.Collections.Generic.IEnumerable{Sentry.Extensibility.ISentryTransactionProcessor})">
            <summary>
            Adds transaction processors which are invoked when creating a <see cref="T:Sentry.Transaction"/>.
            </summary>
            <param name="options">The SentryOptions to hold the processor.</param>
            <param name="processors">The transaction processors.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.RemoveTransactionProcessor``1(Sentry.SentryOptions)">
            <summary>
            Removes all transaction processors of type <typeparamref name="TProcessor"/>
            </summary>
            <typeparam name="TProcessor">The type of processor(s) to remove.</typeparam>
            <param name="options">The SentryOptions to remove the processor(s) from.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.AddTransactionProcessorProvider(Sentry.SentryOptions,System.Func{System.Collections.Generic.IEnumerable{Sentry.Extensibility.ISentryTransactionProcessor}})">
            <summary>
            Adds an transaction processor provider which is invoked when creating a <see cref="T:Sentry.Transaction"/>.
            </summary>
            <param name="options">The SentryOptions to hold the processor provider.</param>
            <param name="processorProvider">The transaction processor provider.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.AddExceptionProcessorProvider(Sentry.SentryOptions,System.Func{System.Collections.Generic.IEnumerable{Sentry.Extensibility.ISentryEventExceptionProcessor}})">
            <summary>
            Add the exception processor provider.
            </summary>
            <param name="options">The SentryOptions to hold the processor provider.</param>
            <param name="processorProvider">The exception processor provider.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.GetAllEventProcessors(Sentry.SentryOptions)">
            <summary>
            Invokes all event processor providers available.
            </summary>
            <param name="options">The SentryOptions which holds the processor providers.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.GetAllTransactionProcessors(Sentry.SentryOptions)">
            <summary>
            Invokes all transaction processor providers available.
            </summary>
            <param name="options">The SentryOptions which holds the processor providers.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.GetAllExceptionProcessors(Sentry.SentryOptions)">
            <summary>
            Invokes all exception processor providers available.
            </summary>
            <param name="options">The SentryOptions which holds the processor providers.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.UseStackTraceFactory(Sentry.SentryOptions,Sentry.Extensibility.ISentryStackTraceFactory)">
            <summary>
            Use custom <see cref="T:Sentry.Extensibility.ISentryStackTraceFactory" />.
            </summary>
            <param name="options">The SentryOptions to hold the processor provider.</param>
            <param name="sentryStackTraceFactory">The stack trace factory.</param>
        </member>
        <member name="M:Sentry.SentryOptionsExtensions.ApplyDefaultTags(Sentry.SentryOptions,Sentry.IHasTags)">
            <summary>
            Applies the default tags to an event without resetting existing tags.
            </summary>
            <param name="options">The options to read the default tags from.</param>
            <param name="hasTags">The event to apply the tags to.</param>
        </member>
        <member name="T:Sentry.SentryScopeManagerExtensions">
            <summary>
            Extension methods for <see cref="T:Sentry.ISentryScopeManager"/>.
            </summary>
        </member>
        <member name="M:Sentry.SentryScopeManagerExtensions.WithScope``1(Sentry.ISentryScopeManager,System.Func{Sentry.Scope,``0})">
            <summary>
            Runs the callback within a new scope.
            </summary>
            <remarks>
            Pushes a new scope, runs the callback, then pops the scope. Use this when you have significant work to
            perform within an isolated scope.  If you just need to configure scope for a single event, use the overloads
            of CaptureEvent, CaptureMessage and CaptureException that provide a callback to a configurable scope.
            </remarks>
            <see href="https://docs.sentry.io/platforms/dotnet/enriching-events/scopes/#local-scopes"/>
            <param name="scopeManager">The scope manager (usually the hub).</param>
            <param name="scopeCallback">The callback to run with the one time scope.</param>
            <returns>The result from the callback.</returns>
        </member>
        <member name="M:Sentry.SentryScopeManagerExtensions.WithScopeAsync(Sentry.ISentryScopeManager,System.Func{Sentry.Scope,System.Threading.Tasks.Task})">
            <summary>
            Runs the asynchronous callback within a new scope.
            </summary>
            <remarks>
            Asynchronous version of <see cref="M:Sentry.ISentryScopeManager.WithScope(System.Action{Sentry.Scope})"/>.
            Pushes a new scope, runs the callback, then pops the scope. Use this when you have significant work to
            perform within an isolated scope.  If you just need to configure scope for a single event, use the overloads
            of CaptureEvent, CaptureMessage and CaptureException that provide a callback to a configurable scope.
            </remarks>
            <see href="https://docs.sentry.io/platforms/dotnet/enriching-events/scopes/#local-scopes"/>
            <param name="scopeManager">The scope manager (usually the hub).</param>
            <param name="scopeCallback">The callback to run with the one time scope.</param>
            <returns>An async task to await the callback.</returns>
        </member>
        <member name="M:Sentry.SentryScopeManagerExtensions.WithScopeAsync``1(Sentry.ISentryScopeManager,System.Func{Sentry.Scope,System.Threading.Tasks.Task{``0}})">
            <summary>
            Runs the asynchronous callback within a new scope.
            </summary>
            <remarks>
            Asynchronous version of <see cref="M:Sentry.ISentryScopeManager.WithScope(System.Action{Sentry.Scope})"/>.
            Pushes a new scope, runs the callback, then pops the scope. Use this when you have significant work to
            perform within an isolated scope.  If you just need to configure scope for a single event, use the overloads
            of CaptureEvent, CaptureMessage and CaptureException that provide a callback to a configurable scope.
            </remarks>
            <see href="https://docs.sentry.io/platforms/dotnet/enriching-events/scopes/#local-scopes"/>
            <param name="scopeManager">The scope manager (usually the hub).</param>
            <param name="scopeCallback">The callback to run with the one time scope.</param>
            <returns>An async task to await the result of the callback.</returns>
        </member>
        <member name="T:Sentry.SentryStackFrame">
            <summary>
            A frame of a stacktrace.
            </summary>
            <see href="https://develop.sentry.dev/sdk/event-payloads/stacktrace/"/>
        </member>
        <member name="P:Sentry.SentryStackFrame.FileName">
            <summary>
            The relative file path to the call.
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.Function">
            <summary>
            The name of the function being called.
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.Module">
            <summary>
            Platform-specific module path.
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.LineNumber">
            <summary>
            The line number of the call.
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.ColumnNumber">
            <summary>
            The column number of the call.
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.AbsolutePath">
            <summary>
            The absolute path to filename.
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.ContextLine">
            <summary>
            Source code in filename at line number.
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.PreContext">
            <summary>
            A list of source code lines before context_line (in order) – usually [lineno - 5:lineno].
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.PostContext">
            <summary>
            A list of source code lines after context_line (in order) – usually [lineno + 1:lineno + 5].
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.InApp">
            <summary>
            Signifies whether this frame is related to the execution of the relevant code in this stacktrace.
            </summary>
            <example>
            For example, the frames that might power the framework’s web server of your app are probably not relevant,
            however calls to the framework’s library once you start handling code likely are.
            </example>
        </member>
        <member name="P:Sentry.SentryStackFrame.Vars">
            <summary>
            A mapping of variables which were available within this frame (usually context-locals).
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.FramesOmitted">
            <summary>
            Which frames were omitted, if any.
            </summary>
            <remarks>
            If the list of frames is large, you can explicitly tell the system that you’ve omitted a range of frames.
            The frames_omitted must be a single tuple two values: start and end.
            </remarks>
            <example>
            If you only removed the 8th frame, the value would be (8, 9), meaning it started at the 8th frame,
            and went until the 9th (the number of frames omitted is end-start).
            The values should be based on a one-index.
            </example>
        </member>
        <member name="P:Sentry.SentryStackFrame.Package">
            <summary>
            The assembly where the code resides.
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.Platform">
            <summary>
            This can override the platform for a single frame. Otherwise the platform of the event is assumed.
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.ImageAddress">
            <summary>
            Optionally an address of the debug image to reference.
            If this is set and a known image is defined by debug_meta then symbolication can take place.
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.SymbolAddress">
            <summary>
            An optional address that points to a symbol.
            We actually use the instruction address for symbolication but this can be used to calculate an instruction offset automatically.
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.InstructionAddress">
            <summary>
            An optional instruction address for symbolication.<br/>
            This should be a string with a hexadecimal number that includes a <b>0x</b> prefix.<br/>
            If this is set and a known image is defined in the <see href="https://develop.sentry.dev/sdk/event-payloads/debugmeta/">Debug Meta Interface</see>, then symbolication can take place.<br/>
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.InstructionOffset">
            <summary>
            The instruction offset.
            </summary>
            <remarks>
            The official docs refer to it as 'The difference between instruction address and symbol address in bytes.'
            In .NET this means the IL Offset within the assembly.
            </remarks>
        </member>
        <member name="P:Sentry.SentryStackFrame.AddressMode">
            <summary>
            Optionally changes the addressing mode. The default value is the same as
            `"abs"` which means absolute referencing. This can also be set to
            `"rel:DEBUG_ID"` or `"rel:IMAGE_INDEX"` to make addresses relative to an
            object referenced by debug id or index.
            </summary>
        </member>
        <member name="P:Sentry.SentryStackFrame.FunctionId">
            <summary>
            The optional Function Id.<br/>
            This is derived from the `MetadataToken`, and should be the record id
            of a `MethodDef`.<br/>
            This should be a string with a hexadecimal number that includes a <b>0x</b> prefix.<br/>
            </summary>
        </member>
        <member name="M:Sentry.SentryStackFrame.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryStackFrame.ConfigureAppFrame(Sentry.SentryOptions)">
            <summary>
            Configures <see cref="P:Sentry.SentryStackFrame.InApp"/> based on the <see cref="P:Sentry.SentryOptions.InAppInclude"/> and <see cref="P:Sentry.SentryOptions.InAppExclude"/> or <paramref name="options"/>.
            </summary>
            <param name="options">The Sentry options.</param>
            <remarks><see cref="P:Sentry.SentryStackFrame.InApp"/> will remain with the same value if previously set.</remarks>
        </member>
        <member name="M:Sentry.SentryStackFrame.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.InstructionAddressAdjustment">
            <summary>
            Instruction Address Adjustments
            </summary>
        </member>
        <member name="F:Sentry.InstructionAddressAdjustment.Auto">
            <summary>
            Symbolicator will use the `"all_but_first"` strategy **unless** the event has a crashing `signal`
            attribute and the Stack Trace has a `registers` map, and the instruction pointer register (`rip` / `pc`)
            does not match the first frame. In that case, `"all"` frames will be adjusted.
            </summary>
        </member>
        <member name="F:Sentry.InstructionAddressAdjustment.All">
            <summary>
            All frames of the stack trace will be adjusted, subtracting one instruction with (or `1`) from the
            incoming `instruction_addr` before symbolication.
            </summary>
        </member>
        <member name="F:Sentry.InstructionAddressAdjustment.AllButFirst">
            <summary>
            All frames but the first (in callee to caller / child to parent direction) should be adjusted.
            </summary>
        </member>
        <member name="F:Sentry.InstructionAddressAdjustment.None">
            <summary>
            No adjustment will be applied whatsoever.
            </summary>
        </member>
        <member name="T:Sentry.SentryStackTrace">
            <summary>
            Sentry Stacktrace interface.
            </summary>
            <remarks>
            A stacktrace contains a list of frames, each with various bits (most optional) describing the context of that frame.
            Frames should be sorted from oldest to newest.
            </remarks>
            <see href="https://develop.sentry.dev/sdk/event-payloads/stacktrace/"/>
        </member>
        <member name="P:Sentry.SentryStackTrace.Frames">
            <summary>
            The list of frames in the stack.
            </summary>
            <remarks>
            The list of frames should be ordered by the oldest call first.
            </remarks>
        </member>
        <member name="P:Sentry.SentryStackTrace.AddressAdjustment">
            <summary>
            The optional instruction address adjustment.
            </summary>
            <remarks>
            Tells the symbolicator if and what adjustment for is needed.
            </remarks>
        </member>
        <member name="M:Sentry.SentryStackTrace.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryStackTrace.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.SentryThread">
            <summary>
            A thread running at the time of an event.
            </summary>
            <see href="https://develop.sentry.dev/sdk/event-payloads/threads/"/>
        </member>
        <member name="P:Sentry.SentryThread.Id">
            <summary>
            The Id of the thread.
            </summary>
        </member>
        <member name="P:Sentry.SentryThread.Name">
            <summary>
            The name of the thread.
            </summary>
        </member>
        <member name="P:Sentry.SentryThread.Crashed">
            <summary>
            Whether the crash happened on this thread.
            </summary>
        </member>
        <member name="P:Sentry.SentryThread.Current">
            <summary>
            An optional flag to indicate that the thread was in the foreground.
            </summary>
        </member>
        <member name="P:Sentry.SentryThread.Stacktrace">
            <summary>
            Stack trace.
            </summary>
            <see href="https://develop.sentry.dev/sdk/event-payloads/stacktrace/"/>
        </member>
        <member name="M:Sentry.SentryThread.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryThread.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.SentryTraceHeader">
            <summary>
            Sentry trace header.
            </summary>
        </member>
        <member name="P:Sentry.SentryTraceHeader.TraceId">
            <summary>
            Trace ID.
            </summary>
        </member>
        <member name="P:Sentry.SentryTraceHeader.SpanId">
            <summary>
            Span ID.
            </summary>
        </member>
        <member name="P:Sentry.SentryTraceHeader.IsSampled">
            <summary>
            Whether the trace is sampled.
            </summary>
        </member>
        <member name="M:Sentry.SentryTraceHeader.#ctor(Sentry.SentryId,Sentry.SpanId,System.Nullable{System.Boolean})">
            <summary>
            Initializes an instance of <see cref="T:Sentry.SentryTraceHeader"/>.
            </summary>
        </member>
        <member name="M:Sentry.SentryTraceHeader.ToString">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SentryTraceHeader.Parse(System.String)">
            <summary>
            Parses <see cref="T:Sentry.SentryTraceHeader"/> from string.
            </summary>
        </member>
        <member name="T:Sentry.SentryValues`1">
            <summary>
            Helps serialization of Sentry protocol types which include a values property.
            </summary>
        </member>
        <member name="P:Sentry.SentryValues`1.Values">
            <summary>
            The values.
            </summary>
        </member>
        <member name="M:Sentry.SentryValues`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Creates an instance from the specified <see cref="T:System.Collections.Generic.IEnumerable`1"/>.
            </summary>
        </member>
        <member name="M:Sentry.SentryValues`1.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="T:Sentry.Session">
            <summary>
            Sentry session.
            </summary>
        </member>
        <member name="P:Sentry.Session.Id">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Session.DistinctId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Session.StartTimestamp">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Session.Release">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Session.Environment">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Session.IpAddress">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Session.UserAgent">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Session.ErrorCount">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Session.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Sentry.Session"/>.
            </summary>
        </member>
        <member name="M:Sentry.Session.ReportError">
            <summary>
            Reports an error on the session.
            </summary>
        </member>
        <member name="T:Sentry.SessionEndStatus">
            <summary>
            Terminal state of a session.
            </summary>
        </member>
        <member name="F:Sentry.SessionEndStatus.Exited">
            <summary>
            Session ended normally.
            </summary>
        </member>
        <member name="F:Sentry.SessionEndStatus.Crashed">
            <summary>
            Session ended with an unhandled exception.
            </summary>
        </member>
        <member name="F:Sentry.SessionEndStatus.Abnormal">
            <summary>
            Session ended abnormally (e.g. device lost power).
            </summary>
        </member>
        <member name="T:Sentry.SessionUpdate">
            <summary>
            Session update.
            </summary>
        </member>
        <member name="P:Sentry.SessionUpdate.Id">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SessionUpdate.DistinctId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SessionUpdate.StartTimestamp">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SessionUpdate.Release">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SessionUpdate.Environment">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SessionUpdate.IpAddress">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SessionUpdate.UserAgent">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SessionUpdate.ErrorCount">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SessionUpdate.IsInitial">
            <summary>
            Whether this is the initial update.
            </summary>
        </member>
        <member name="P:Sentry.SessionUpdate.Timestamp">
            <summary>
            Timestamp.
            </summary>
        </member>
        <member name="P:Sentry.SessionUpdate.SequenceNumber">
            <summary>
            Sequence number.
            </summary>
        </member>
        <member name="P:Sentry.SessionUpdate.Duration">
            <summary>
            Duration of time since the start of the session.
            </summary>
        </member>
        <member name="P:Sentry.SessionUpdate.EndStatus">
            <summary>
            Status with which the session was ended.
            </summary>
        </member>
        <member name="M:Sentry.SessionUpdate.#ctor(Sentry.SentryId,System.String,System.DateTimeOffset,System.String,System.String,System.String,System.String,System.Int32,System.Boolean,System.DateTimeOffset,System.Int32,System.Nullable{Sentry.SessionEndStatus})">
            <summary>
            Initializes a new instance of <see cref="T:Sentry.SessionUpdate"/>.
            </summary>
        </member>
        <member name="M:Sentry.SessionUpdate.#ctor(Sentry.ISession,System.Boolean,System.DateTimeOffset,System.Int32,System.Nullable{Sentry.SessionEndStatus})">
            <summary>
            Initializes a new instance of <see cref="T:Sentry.SessionUpdate"/>.
            </summary>
        </member>
        <member name="M:Sentry.SessionUpdate.#ctor(Sentry.SessionUpdate,System.Boolean,System.Nullable{Sentry.SessionEndStatus})">
            <summary>
            Initializes a new instance of <see cref="T:Sentry.SessionUpdate"/>.
            </summary>
        </member>
        <member name="M:Sentry.SessionUpdate.#ctor(Sentry.SessionUpdate,System.Boolean)">
            <summary>
            Initializes a new instance of <see cref="T:Sentry.SessionUpdate"/>.
            </summary>
        </member>
        <member name="M:Sentry.SessionUpdate.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SessionUpdate.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses <see cref="T:Sentry.SessionUpdate"/> from JSON.
            </summary>
        </member>
        <member name="T:Sentry.Span">
            <summary>
            Transaction span.
            </summary>
        </member>
        <member name="P:Sentry.Span.SpanId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Span.ParentSpanId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Span.TraceId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Span.StartTimestamp">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Span.EndTimestamp">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Span.IsFinished">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Span.Operation">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Span.Description">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Span.Status">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Span.IsSampled">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Span.Tags">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Span.SetTag(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Span.UnsetTag(System.String)">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Span.Extra">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Span.SetExtra(System.String,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Span.#ctor(System.Nullable{Sentry.SpanId},System.String)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.Span"/>.
            </summary>
        </member>
        <member name="M:Sentry.Span.#ctor(Sentry.ISpan)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.Span"/>.
            </summary>
        </member>
        <member name="M:Sentry.Span.GetTraceHeader">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Span.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Span.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses a span from JSON.
            </summary>
        </member>
        <member name="T:Sentry.SpanContext">
            <summary>
            Span metadata used for sampling.
            </summary>
        </member>
        <member name="P:Sentry.SpanContext.SpanId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanContext.ParentSpanId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanContext.TraceId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanContext.Operation">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanContext.Description">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanContext.Status">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanContext.IsSampled">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanContext.Instrumenter">
            <summary>
            Identifies which instrumentation is being used.
            </summary>
        </member>
        <member name="M:Sentry.SpanContext.#ctor(Sentry.SpanId,System.Nullable{Sentry.SpanId},Sentry.SentryId,System.String,System.String,System.Nullable{Sentry.SpanStatus},System.Nullable{System.Boolean})">
            <summary>
            Initializes an instance of <see cref="T:Sentry.SpanContext"/>.
            </summary>
        </member>
        <member name="T:Sentry.SpanId">
            <summary>
            Sentry span ID.
            </summary>
        </member>
        <member name="F:Sentry.SpanId.Empty">
            <summary>
            An empty Sentry span ID.
            </summary>
        </member>
        <member name="M:Sentry.SpanId.#ctor(System.String)">
            <summary>
            Creates a new instance of a Sentry span Id.
            </summary>
        </member>
        <member name="M:Sentry.SpanId.#ctor(System.Int64)">
            <summary>
            Creates a new instance of a Sentry span Id.
            </summary>
            <param name="value"></param>
        </member>
        <member name="M:Sentry.SpanId.Equals(Sentry.SpanId)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SpanId.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SpanId.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SpanId.ToString">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SpanId.Create">
            <summary>
            Generates a new Sentry ID.
            </summary>
        </member>
        <member name="M:Sentry.SpanId.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SpanId.Parse(System.String)">
            <summary>
            Parses from string.
            </summary>
        </member>
        <member name="M:Sentry.SpanId.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="M:Sentry.SpanId.op_Equality(Sentry.SpanId,Sentry.SpanId)">
            <summary>
            Equality operator.
            </summary>
        </member>
        <member name="M:Sentry.SpanId.op_Inequality(Sentry.SpanId,Sentry.SpanId)">
            <summary>
            Equality operator.
            </summary>
        </member>
        <member name="M:Sentry.SpanId.op_Implicit(Sentry.SpanId)~System.String">
            <summary>
            The <see cref="T:System.Guid"/> from the <see cref="T:Sentry.SentryId"/>.
            </summary>
        </member>
        <member name="T:Sentry.SpanStatus">
            <summary>
            Span status.
            </summary>
        </member>
        <member name="F:Sentry.SpanStatus.Ok">
            <summary>The operation completed successfully.</summary>
        </member>
        <member name="F:Sentry.SpanStatus.DeadlineExceeded">
            <summary>Deadline expired before operation could complete.</summary>
        </member>
        <member name="F:Sentry.SpanStatus.Unauthenticated">
            <summary>401 Unauthorized (actually does mean unauthenticated according to RFC 7235).</summary>
        </member>
        <member name="F:Sentry.SpanStatus.PermissionDenied">
            <summary>403 Forbidden</summary>
        </member>
        <member name="F:Sentry.SpanStatus.NotFound">
            <summary>404 Not Found. Some requested entity (file or directory) was not found.</summary>
        </member>
        <member name="F:Sentry.SpanStatus.ResourceExhausted">
            <summary>429 Too Many Requests</summary>
        </member>
        <member name="F:Sentry.SpanStatus.InvalidArgument">
            <summary>Client specified an invalid argument. 4xx.</summary>
        </member>
        <member name="F:Sentry.SpanStatus.Unimplemented">
            <summary>501 Not Implemented</summary>
        </member>
        <member name="F:Sentry.SpanStatus.Unavailable">
            <summary>503 Service Unavailable</summary>
        </member>
        <member name="F:Sentry.SpanStatus.InternalError">
            <summary>Other/generic 5xx.</summary>
        </member>
        <member name="F:Sentry.SpanStatus.UnknownError">
            <summary>Unknown. Any non-standard HTTP status code.</summary>
        </member>
        <member name="F:Sentry.SpanStatus.Cancelled">
            <summary>The operation was cancelled (typically by the user).</summary>
        </member>
        <member name="F:Sentry.SpanStatus.AlreadyExists">
            <summary>Already exists (409).</summary>
        </member>
        <member name="F:Sentry.SpanStatus.FailedPrecondition">
            <summary>Operation was rejected because the system is not in a state required for the operation.</summary>
        </member>
        <member name="F:Sentry.SpanStatus.Aborted">
            <summary>The operation was aborted, typically due to a concurrency issue.</summary>
        </member>
        <member name="F:Sentry.SpanStatus.OutOfRange">
            <summary>Operation was attempted past the valid range.</summary>
        </member>
        <member name="F:Sentry.SpanStatus.DataLoss">
            <summary>Unrecoverable data loss or corruption</summary>
        </member>
        <member name="T:Sentry.SpanTracer">
            <summary>
            Transaction span tracer.
            </summary>
        </member>
        <member name="P:Sentry.SpanTracer.SpanId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanTracer.ParentSpanId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanTracer.TraceId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanTracer.StartTimestamp">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanTracer.EndTimestamp">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanTracer.IsFinished">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanTracer.Operation">
            <inheritdoc cref="P:Sentry.ISpan.Operation" />
        </member>
        <member name="P:Sentry.SpanTracer.Description">
            <inheritdoc cref="P:Sentry.ISpan.Description" />
        </member>
        <member name="P:Sentry.SpanTracer.Status">
            <inheritdoc cref="P:Sentry.ISpan.Status" />
        </member>
        <member name="P:Sentry.SpanTracer.IsSentryRequest">
            <summary>
            Used by the Sentry.OpenTelemetry.SentrySpanProcessor to mark a span as a Sentry request. Ideally we wouldn't
            create these spans but since we can't avoid doing that, once we detect that it's a Sentry request we mark it
            as such so that we can filter it when the transaction finishes and the TransactionTracer gets converted into
            a Transaction.
            </summary>
        </member>
        <member name="P:Sentry.SpanTracer.IsSampled">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanTracer.Tags">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SpanTracer.SetTag(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SpanTracer.UnsetTag(System.String)">
            <inheritdoc />
        </member>
        <member name="P:Sentry.SpanTracer.Extra">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SpanTracer.SetExtra(System.String,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SpanTracer.#ctor(Sentry.IHub,Sentry.TransactionTracer,System.Nullable{Sentry.SpanId},Sentry.SentryId,System.String)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.SpanTracer"/>.
            </summary>
        </member>
        <member name="M:Sentry.SpanTracer.StartChild(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SpanTracer.Unfinish">
            <summary>
            Used to mark a span as unfinished when it was previously marked as finished. This allows us to reuse spans for
            DB Connections that get reused by the underlying connection pool
            </summary>
        </member>
        <member name="M:Sentry.SpanTracer.Finish">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SpanTracer.Finish(Sentry.SpanStatus)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SpanTracer.Finish(System.Exception,Sentry.SpanStatus)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SpanTracer.Finish(System.Exception)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SpanTracer.GetTraceHeader">
            <inheritdoc />
        </member>
        <member name="T:Sentry.StackTraceMode">
            <summary>
            The mode which the SDK builds the stack trace.
            </summary>
            <remarks>
            Changing this WILL affect issue grouping in Sentry since the format of the frames will change.
            </remarks>
        </member>
        <member name="F:Sentry.StackTraceMode.Original">
            <summary>
            The default .NET stack trace format.
            </summary>
            <remarks>
            This was the default before Sentry .NET 3.0.0.
            </remarks>
        </member>
        <member name="F:Sentry.StackTraceMode.Enhanced">
            <summary>
            Includes return type, arguments ref modifiers and more.
            </summary>
            <remarks>
            This mode uses <see href="https://github.com/getsentry/Ben.Demystifier">Ben Adams' Demystifier library</see>.
            </remarks>
        </member>
        <member name="T:Sentry.StartupTimeDetectionMode">
            <summary>
            The mode of which to attempt to detect the process startup time.
            </summary>
        </member>
        <member name="F:Sentry.StartupTimeDetectionMode.None">
            <summary>
            Disabled.
            </summary>
        </member>
        <member name="F:Sentry.StartupTimeDetectionMode.Fast">
            <summary>
            Best effort approach that can be off by a few seconds or minutes.
            </summary>
            <remarks>
            In this mode, the App startup time is assumed to be the point of which the SDK was initialized.
            </remarks>
        </member>
        <member name="F:Sentry.StartupTimeDetectionMode.Best">
            <summary>
            Attempts to detect the startup time with the most precision.
            </summary>
            <remarks>
            This can require starting work on the thread pool due to P/Invoke calls.
            </remarks>
        </member>
        <member name="T:Sentry.StreamAttachmentContent">
            <summary>
            Attachment sourced from stream.
            </summary>
        </member>
        <member name="M:Sentry.StreamAttachmentContent.#ctor(System.IO.Stream)">
            <summary>
            Creates a new instance of <see cref="T:Sentry.StreamAttachmentContent"/>.
            </summary>
        </member>
        <member name="M:Sentry.StreamAttachmentContent.GetStream">
            <inheritdoc />
        </member>
        <member name="T:Sentry.SubstringOrRegexPattern">
            <summary>
            Provides a pattern that can be used to match against other strings as either a substring or regular expression.
            </summary>
        </member>
        <member name="M:Sentry.SubstringOrRegexPattern.#ctor(System.String,System.StringComparison)">
            <summary>
            Constructs a <see cref="T:Sentry.SubstringOrRegexPattern"/> instance.
            </summary>
            <param name="substringOrRegexPattern">The substring or regular expression pattern to match on.</param>
            <param name="comparison">The string comparison type to use when matching.</param>
        </member>
        <member name="M:Sentry.SubstringOrRegexPattern.#ctor(System.Text.RegularExpressions.Regex)">
            <summary>
            Constructs a <see cref="T:Sentry.SubstringOrRegexPattern"/> instance.
            </summary>
            <param name="regex"></param>
            <remarks>
            Use this constructor when you need to control the regular expression matching options.
            We recommend setting at least <see cref="F:System.Text.RegularExpressions.RegexOptions.Compiled"/> for performance, and
            <see cref="F:System.Text.RegularExpressions.RegexOptions.CultureInvariant"/> (unless you have culture-specific matching needs).
            The <see cref="M:Sentry.SubstringOrRegexPattern.#ctor(System.String,System.StringComparison)"/> constructor sets these by default.
            </remarks>
        </member>
        <member name="M:Sentry.SubstringOrRegexPattern.op_Implicit(System.String)~Sentry.SubstringOrRegexPattern">
            <summary>
            Implicitly converts a <see cref="T:System.String"/> to a <see cref="T:Sentry.SubstringOrRegexPattern"/>.
            </summary>
            <param name="substringOrRegexPattern"></param>
        </member>
        <member name="M:Sentry.SubstringOrRegexPattern.ToString">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SubstringOrRegexPattern.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SubstringOrRegexPattern.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Sentry.SubstringOrRegexPatternExtensions.SetWithConfigBinding``1(System.Collections.Generic.IList{``0})">
            <summary>
            During configuration binding, .NET 6 and lower used to just call Add on the existing item.
            .NET 7 changed this to call the setter with an array that already starts with the old value.
            We have to handle both cases.
            </summary>
            <typeparam name="T">The List Type</typeparam>
            <param name="value">The set of values to be assigned</param>
            <returns>A IList of type T that will be consistent even if it has been set via Config</returns>
        </member>
        <member name="T:Sentry.TracePropagationTarget">
            <summary>
            Provides a pattern that can be used to identify which destinations will have <c>sentry-trace</c> and
            <c>baggage</c> headers propagated to, for purposes of distributed tracing.
            The pattern can either be a substring or a regular expression.
            </summary>
            <seealso href="https://develop.sentry.dev/sdk/performance/#tracepropagationtargets"/>
        </member>
        <member name="M:Sentry.TracePropagationTarget.#ctor(System.String,System.StringComparison)">
            <summary>
            Constructs a <see cref="T:Sentry.TracePropagationTarget"/> instance that will match when the provided
            <paramref name="substringOrRegexPattern"/> is either found as a substring within the outgoing request URL,
            or matches as a regular expression pattern against the outgoing request URL.
            </summary>
            <param name="substringOrRegexPattern">The substring or regular expression pattern to match on.</param>
            <param name="comparison">The string comparison type to use when matching.</param>
        </member>
        <member name="M:Sentry.TracePropagationTarget.#ctor(System.Text.RegularExpressions.Regex)">
            <summary>
            Constructs a <see cref="T:Sentry.TracePropagationTarget"/> instance that will match when the provided
            <paramref name="regex"/> object matches the outgoing request URL.
            </summary>
            <param name="regex"></param>
            <remarks>
            Use this constructor when you need to control the regular expression matching options.
            We recommend setting at least <see cref="F:System.Text.RegularExpressions.RegexOptions.Compiled"/> for performance, and
            <see cref="F:System.Text.RegularExpressions.RegexOptions.CultureInvariant"/> (unless you have culture-specific matching needs).
            The <see cref="M:Sentry.TracePropagationTarget.#ctor(System.String,System.StringComparison)"/> constructor sets these by default.
            </remarks>
        </member>
        <member name="T:Sentry.TracePropagationTargetTypeConverter">
            <summary>
            Allows the TracePropagationTargets option to be set from config, such as appSettings.json
            </summary>
        </member>
        <member name="T:Sentry.Transaction">
            <summary>
            Sentry performance transaction.
            </summary>
        </member>
        <member name="P:Sentry.Transaction.EventId">
            <summary>
            Transaction's event ID.
            </summary>
        </member>
        <member name="P:Sentry.Transaction.SpanId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.ParentSpanId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.TraceId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Name">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.NameSource">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.IsParentSampled">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Platform">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Release">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Distribution">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.StartTimestamp">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.EndTimestamp">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Operation">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Description">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Status">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.IsSampled">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.SampleRate">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Level">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Request">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Contexts">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.User">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Environment">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Sdk">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Fingerprint">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Breadcrumbs">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Extra">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Tags">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.Spans">
            <summary>
            Flat list of spans within this transaction.
            </summary>
        </member>
        <member name="P:Sentry.Transaction.Measurements">
            <inheritdoc />
        </member>
        <member name="P:Sentry.Transaction.IsFinished">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Transaction.#ctor(System.String,System.String)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.Transaction"/>.
            </summary>
        </member>
        <member name="M:Sentry.Transaction.#ctor(System.String,System.String,Sentry.TransactionNameSource)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.Transaction"/>.
            </summary>
        </member>
        <member name="M:Sentry.Transaction.#ctor(Sentry.ITransaction)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.Transaction"/>.
            </summary>
        </member>
        <member name="M:Sentry.Transaction.AddBreadcrumb(Sentry.Breadcrumb)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Transaction.SetExtra(System.String,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Transaction.SetTag(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Transaction.UnsetTag(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Transaction.SetMeasurement(System.String,Sentry.Protocol.Measurement)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Transaction.GetTraceHeader">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Transaction.Redact">
            <summary>
            Redacts PII from the transaction
            </summary>
        </member>
        <member name="M:Sentry.Transaction.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.Transaction.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses transaction from JSON.
            </summary>
        </member>
        <member name="T:Sentry.TransactionContext">
            <summary>
            Transaction metadata used for sampling.
            </summary>
        </member>
        <member name="P:Sentry.TransactionContext.Name">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionContext.NameSource">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionContext.IsParentSampled">
            <summary>
            Whether the parent transaction of this transaction has been sampled.
            </summary>
        </member>
        <member name="M:Sentry.TransactionContext.#ctor(Sentry.SpanId,System.Nullable{Sentry.SpanId},Sentry.SentryId,System.String,System.String,System.String,System.Nullable{Sentry.SpanStatus},System.Nullable{System.Boolean},System.Nullable{System.Boolean},Sentry.TransactionNameSource)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.TransactionContext"/>.
            </summary>
        </member>
        <member name="M:Sentry.TransactionContext.#ctor(Sentry.SpanId,System.Nullable{Sentry.SpanId},Sentry.SentryId,System.String,System.String,System.String,System.Nullable{Sentry.SpanStatus},System.Nullable{System.Boolean},System.Nullable{System.Boolean})">
            <summary>
            Initializes an instance of <see cref="T:Sentry.TransactionContext"/>.
            </summary>
        </member>
        <member name="M:Sentry.TransactionContext.#ctor(System.Nullable{Sentry.SpanId},Sentry.SentryId,System.String,System.String,System.Nullable{System.Boolean})">
            <summary>
            Initializes an instance of <see cref="T:Sentry.TransactionContext"/>.
            </summary>
        </member>
        <member name="M:Sentry.TransactionContext.#ctor(System.String,System.String,System.Nullable{System.Boolean})">
            <summary>
            Initializes an instance of <see cref="T:Sentry.TransactionContext"/>.
            </summary>
        </member>
        <member name="M:Sentry.TransactionContext.#ctor(System.String,System.String,Sentry.SentryTraceHeader)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.TransactionContext"/>.
            </summary>
        </member>
        <member name="M:Sentry.TransactionContext.#ctor(System.String,System.String,Sentry.SentryTraceHeader,Sentry.TransactionNameSource)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.TransactionContext"/>.
            </summary>
        </member>
        <member name="M:Sentry.TransactionContext.#ctor(System.String,System.String)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.TransactionContext"/>.
            </summary>
        </member>
        <member name="M:Sentry.TransactionContext.#ctor(System.String,System.String,Sentry.TransactionNameSource)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.TransactionContext"/>.
            </summary>
        </member>
        <member name="T:Sentry.TransactionNameSource">
            <summary>
            Transaction source.
            This information is required by dynamic sampling. Contains information about how the name
            of the transaction was determined. This will be used by the server to decide whether or not
            to scrub identifiers from the transaction name, or replace the entire name with a placeholder.
            The source should only be set by integrations and not by developers directly
            https://develop.sentry.dev/sdk/event-payloads/transaction/#transaction-annotations
            </summary>
        </member>
        <member name="F:Sentry.TransactionNameSource.Custom">
            <summary>
            User-defined name.
            </summary>
            <example>
              <list type="bullet">
                <item>my_transaction</item>
              </list>
            </example>
        </member>
        <member name="F:Sentry.TransactionNameSource.Url">
            <summary>
            Raw URL, potentially containing identifiers.
            </summary>
            <example>
              <list type="bullet">
                <item>/auth/login/john123/</item>
                <item>GET /auth/login/john123/</item>
              </list>
            </example>
        </member>
        <member name="F:Sentry.TransactionNameSource.Route">
            <summary>
            Parametrized URL / route
            </summary>
            <example>
              <list type="bullet">
                <item>/auth/login/:userId/</item>
                <item>GET /auth/login/{user}/</item>
              </list>
            </example>
        </member>
        <member name="F:Sentry.TransactionNameSource.View">
            <summary>
            Name of the view handling the request.
            </summary>
            <example>
              <list type="bullet">
                <item>UserListView</item>
              </list>
            </example>
        </member>
        <member name="F:Sentry.TransactionNameSource.Component">
            <summary>
            Named after a software component, such as a function or class name.
            </summary>
            <example>
              <list type="bullet">
                <item>AuthLogin.login</item>
                <item>LoginActivity.login_button</item>
              </list>
            </example>
        </member>
        <member name="F:Sentry.TransactionNameSource.Task">
            <summary>
            Name of a background task
            </summary>
            <example>
              <list type="bullet">
                <item>sentry.tasks.do_something</item>
              </list>
            </example>
        </member>
        <member name="M:Sentry.TransactionNameSourceExtensions.IsHighQuality(Sentry.TransactionNameSource)">
            <summary>
            Determines if the <paramref name="transactionNameSource"/> is considered "high quality"
            for purposes of dynamic sampling.
            </summary>
            <remarks>
            Currently, only <see cref="F:Sentry.TransactionNameSource.Url"/> is considered low quality,
            and the others are high quality, but this may change in the future.
            </remarks>
            <seealso href="https://develop.sentry.dev/sdk/performance/dynamic-sampling-context/#note-on-low-quality-transaction-names"/>
        </member>
        <member name="T:Sentry.TransactionSamplingContext">
            <summary>
            Context information passed into a <see cref="P:Sentry.SentryOptions.TracesSampler"/> function,
            which can be used to determine whether a transaction should be sampled.
            </summary>
        </member>
        <member name="P:Sentry.TransactionSamplingContext.TransactionContext">
            <summary>
            Transaction context.
            </summary>
        </member>
        <member name="P:Sentry.TransactionSamplingContext.CustomSamplingContext">
            <summary>
            Custom data used for sampling.
            </summary>
        </member>
        <member name="M:Sentry.TransactionSamplingContext.#ctor(Sentry.ITransactionContext,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Initializes an instance of <see cref="T:Sentry.TransactionSamplingContext"/>.
            </summary>
        </member>
        <member name="T:Sentry.TransactionTracer">
            <summary>
            Transaction tracer.
            </summary>
        </member>
        <member name="P:Sentry.TransactionTracer.SpanId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.ParentSpanId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.TraceId">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.Name">
            <inheritdoc cref="P:Sentry.ITransaction.Name" />
        </member>
        <member name="P:Sentry.TransactionTracer.NameSource">
            <inheritdoc cref="P:Sentry.IHasTransactionNameSource.NameSource" />
        </member>
        <member name="P:Sentry.TransactionTracer.IsParentSampled">
            <inheritdoc cref="P:Sentry.ITransaction.IsParentSampled" />
        </member>
        <member name="P:Sentry.TransactionTracer.Platform">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.Release">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.Distribution">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.StartTimestamp">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.EndTimestamp">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.Operation">
            <inheritdoc cref="P:Sentry.ISpan.Operation" />
        </member>
        <member name="P:Sentry.TransactionTracer.Description">
            <inheritdoc cref="P:Sentry.ISpan.Description" />
        </member>
        <member name="P:Sentry.TransactionTracer.Status">
            <inheritdoc cref="P:Sentry.ISpan.Status" />
        </member>
        <member name="P:Sentry.TransactionTracer.IsSampled">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.SampleRate">
            <summary>
            The sample rate used for this transaction.
            </summary>
        </member>
        <member name="P:Sentry.TransactionTracer.Level">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.Request">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.Contexts">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.User">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.Environment">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.Sdk">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.Fingerprint">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.Breadcrumbs">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.Extra">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.Tags">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.Spans">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.Measurements">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.IsFinished">
            <inheritdoc />
        </member>
        <member name="P:Sentry.TransactionTracer.IsSentryRequest">
            <summary>
            Used by the Sentry.OpenTelemetry.SentrySpanProcessor to mark a transaction as a Sentry request. Ideally we wouldn't
            create this transaction but since we can't avoid doing that, once we detect that it's a Sentry request we mark it
            as such so that we can prevent finishing the transaction tracer when idle timeout elapses and the TransactionTracer gets converted into
            a Transaction.
            </summary>
        </member>
        <member name="M:Sentry.TransactionTracer.#ctor(Sentry.IHub,System.String,System.String)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.Transaction"/>.
            </summary>
        </member>
        <member name="M:Sentry.TransactionTracer.#ctor(Sentry.IHub,System.String,System.String,Sentry.TransactionNameSource)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.Transaction"/>.
            </summary>
        </member>
        <member name="M:Sentry.TransactionTracer.#ctor(Sentry.IHub,Sentry.ITransactionContext)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.TransactionTracer"/>.
            </summary>
        </member>
        <member name="M:Sentry.TransactionTracer.#ctor(Sentry.IHub,Sentry.ITransactionContext,System.Nullable{System.TimeSpan})">
            <summary>
            Initializes an instance of <see cref="T:Sentry.TransactionTracer"/>.
            </summary>
        </member>
        <member name="M:Sentry.TransactionTracer.AddBreadcrumb(Sentry.Breadcrumb)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.TransactionTracer.SetExtra(System.String,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.TransactionTracer.SetTag(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.TransactionTracer.UnsetTag(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.TransactionTracer.SetMeasurement(System.String,Sentry.Protocol.Measurement)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.TransactionTracer.StartChild(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.TransactionTracer.GetLastActiveSpan">
            <inheritdoc />
        </member>
        <member name="M:Sentry.TransactionTracer.Finish">
            <inheritdoc />
        </member>
        <member name="M:Sentry.TransactionTracer.Finish(Sentry.SpanStatus)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.TransactionTracer.Finish(System.Exception,Sentry.SpanStatus)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.TransactionTracer.Finish(System.Exception)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.TransactionTracer.GetTraceHeader">
            <inheritdoc />
        </member>
        <member name="T:Sentry.User">
            <summary>
            An interface which describes the authenticated User for a request.
            </summary>
            <see href="https://develop.sentry.dev/sdk/event-payloads/user/"/>
        </member>
        <member name="P:Sentry.User.Id">
            <summary>
            The unique ID of the user.
            </summary>
        </member>
        <member name="P:Sentry.User.Username">
            <summary>
            The username of the user.
            </summary>
        </member>
        <member name="P:Sentry.User.Email">
            <summary>
            The email address of the user.
            </summary>
        </member>
        <member name="P:Sentry.User.IpAddress">
            <summary>
            The IP address of the user.
            </summary>
        </member>
        <member name="P:Sentry.User.Segment">
            <summary>
            The segment the user belongs to.
            </summary>
        </member>
        <member name="P:Sentry.User.Other">
            <summary>
            Additional information about the user.
            </summary>
        </member>
        <member name="M:Sentry.User.Clone">
            <summary>
            Clones the current <see cref="T:Sentry.User"/> instance.
            </summary>
            <returns>The cloned user.</returns>
        </member>
        <member name="M:Sentry.User.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.User.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.UserFeedback">
            <summary>
            Sentry User Feedback.
            </summary>
        </member>
        <member name="P:Sentry.UserFeedback.EventId">
            <summary>
            The eventId of the event to which the user feedback is associated.
            </summary>
        </member>
        <member name="P:Sentry.UserFeedback.Name">
            <summary>
            The name of the user.
            </summary>
        </member>
        <member name="P:Sentry.UserFeedback.Email">
            <summary>
            The name of the user.
            </summary>
        </member>
        <member name="P:Sentry.UserFeedback.Comments">
            <summary>
            Comments of the user about what happened.
            </summary>
        </member>
        <member name="M:Sentry.UserFeedback.#ctor(Sentry.SentryId,System.String,System.String,System.String)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.UserFeedback"/>.
            </summary>
        </member>
        <member name="M:Sentry.UserFeedback.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.UserFeedback.FromJson(System.Text.Json.JsonElement)">
            <summary>
            Parses from JSON.
            </summary>
        </member>
        <member name="T:Sentry.ViewHierarchy">
            <summary>
            Sentry View Hierarchy.
            </summary>
        </member>
        <member name="P:Sentry.ViewHierarchy.RenderingSystem">
            <summary>
            The rendering system this view hierarchy is capturing.
            </summary>
        </member>
        <member name="P:Sentry.ViewHierarchy.Windows">
            <summary>
            The elements or windows within the view hierarchy.
            </summary>
        </member>
        <member name="M:Sentry.ViewHierarchy.#ctor(System.String)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.ViewHierarchy"/>
            </summary>
            <param name="renderingSystem">The rendering system</param>
        </member>
        <member name="M:Sentry.ViewHierarchy.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="T:Sentry.ViewHierarchyAttachment">
            <summary>
            Sentry View Hierarchy attachment.
            </summary>
        </member>
        <member name="M:Sentry.ViewHierarchyAttachment.#ctor(Sentry.IAttachmentContent)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.ViewHierarchyAttachment"/>.
            </summary>
            /// <param name="content">The view hierarchy attachment</param>
        </member>
        <member name="T:Sentry.ViewHierarchyNode">
            <summary>
            Sentry View Hierarchy Node
            </summary>
        </member>
        <member name="P:Sentry.ViewHierarchyNode.Type">
            <summary>
            The type of the element represented by this node.
            </summary>
        </member>
        <member name="P:Sentry.ViewHierarchyNode.Children">
            <summary>
            The child nodes
            </summary>
        </member>
        <member name="M:Sentry.ViewHierarchyNode.#ctor(System.String)">
            <summary>
            Initializes an instance of <see cref="T:Sentry.ViewHierarchyNode"/>
            </summary>
            <param name="type">The type of node</param>
        </member>
        <member name="M:Sentry.ViewHierarchyNode.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <inheritdoc />
        </member>
        <member name="M:Sentry.ViewHierarchyNode.WriteAdditionalProperties(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)">
            <summary>
            Gets automatically called and writes additional properties during <see cref="M:Sentry.ViewHierarchyNode.WriteTo(System.Text.Json.Utf8JsonWriter,Sentry.Extensibility.IDiagnosticLogger)"/>
            </summary>
        </member>
        <member name="T:SentryExceptionExtensions">
            <summary>
            Extends Exception with formatted data that can be used by Sentry SDK.
            </summary>
        </member>
        <member name="M:SentryExceptionExtensions.AddSentryTag(System.Exception,System.String,System.String)">
            <summary>
            Set a tag that will be added to the event when the exception is captured.
            </summary>
            <param name="ex">The exception.</param>
            <param name="name">The name of the tag.</param>
            <param name="value">The value of the key.</param>
        </member>
        <member name="M:SentryExceptionExtensions.AddSentryContext(System.Exception,System.String,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Set context data that will be added to the event when the exception is captured.
            </summary>
            <param name="ex">The exception.</param>
            <param name="name">The context name.</param>
            <param name="data">The context data.</param>
        </member>
        <member name="M:SentryExceptionExtensions.SetSentryMechanism(System.Exception,System.String,System.String,System.Nullable{System.Boolean})">
            <summary>
            Set mechanism information that will be included with the exception when it is captured.
            </summary>
            <param name="ex">The exception.</param>
            <param name="type">A required short string that identifies the mechanism.</param>
            <param name="description">An optional human-readable description of the mechanism.</param>
            <param name="handled">An optional flag indicating whether the exception was handled by the mechanism.</param>
        </member>
    </members>
</doc>
