﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools.HelpersLib
{
    public partial class LabeledNumericUpDown : UserControl
    {
        public new string Text
        {
            get
            {
                return lblText.Text;
            }
            set
            {
                lblText.Text = value;
            }
        }

        public string Text2
        {
            get
            {
                return lblText2.Text;
            }
            set
            {
                lblText2.Text = value;
            }
        }

        public decimal Value
        {
            get
            {
                return nudValue.Value;
            }
            set
            {
                nudValue.SetValue(value);
            }
        }

        public decimal Maximum
        {
            get
            {
                return nudValue.Maximum;
            }
            set
            {
                nudValue.Maximum = value;
            }
        }

        public decimal Minimum
        {
            get
            {
                return nudValue.Minimum;
            }
            set
            {
                nudValue.Minimum = value;
            }
        }

        public decimal Increment
        {
            get
            {
                return nudValue.Increment;
            }
            set
            {
                nudValue.Increment = value;
            }
        }

        public EventHandler ValueChanged;

        public LabeledNumericUpDown()
        {
            InitializeComponent();
            nudValue.BackColor = SystemColors.Control;
            nudValue.ValueChanged += OnValueChanged;
        }

        private void OnValueChanged(object sender, EventArgs e)
        {
            ValueChanged?.Invoke(sender, e);
        }
    }
}