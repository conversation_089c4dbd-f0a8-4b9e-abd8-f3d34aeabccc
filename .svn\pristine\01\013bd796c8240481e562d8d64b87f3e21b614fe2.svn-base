﻿using OCRTools.Colors;
using OCRTools.Language;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

//using Bluegrams.Application;
//using Bluegrams.Application.WinForms;

namespace OCRTools
{
    [DesignerCategory("Form")]
    public partial class RulerForm : RulerBaseForm
    {
        private readonly MouseTracker _mouseTracker;
        private readonly RulerPainter _painter;

        public RulerForm()
        {
            Settings = new Settings();
            CustomMarkers = new RulerMarkerCollection();
            //manager.Manage(nameof(Settings), nameof(TopMost), nameof(CustomMarkers));
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false;
            SetStyle(
                ControlStyles.UserPaint |
                ControlStyles.AllPaintingInWmPaint |
                ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.ResizeRedraw |
                ControlStyles.SupportsTransparentBackColor |
                ControlStyles.DoubleBuffer, true);

            ShowInTaskbar = true;
            StartPosition = FormStartPosition.CenterScreen;

            ResizeMode = RulerFormResizeMode.Horizontal;
            _mouseTracker = new MouseTracker(this);
            _mouseTracker.Tick += mouseTracker_Tick;
            Width = 850;

            _painter = new RulerPainter(this);
            SetStyle(ControlStyles.ResizeRedraw, true);
            TopMost = CommonSetting.标尺置顶;
            MouseWheel += RulerForm_MouseWheel;
        }

        public Settings Settings { get; set; }
        public RulerMarkerCollection CustomMarkers { get; set; }

        private void RulerForm_Load(object sender, EventArgs e)
        {
            Settings.SelectedTheme = CommonSetting.夜间模式 ? ThemeOption.黑色 : ThemeOption.白色;
            Opacity = BoxUtil.GetInt32FromObject(CommonSetting.标尺透明度, 100) * 1.0d / 100;
            // Set some items of the context menu
            foreach (Enum item in Enum.GetValues(typeof(MeasuringUnit)))
            {
                var meun = new ToolStripMenuItem(item.ToString())
                {
                    Tag = item
                };
                if (Equals(item.ToString(), CommonSetting.标尺计量单位)) meun.Checked = true;
                meun.Click += UnitItemClick;
                comUnits.DropDownItems.Add(meun);
            }

            foreach (Enum item in Enum.GetValues(typeof(ThemeOption)))
            {
                var meun = new ToolStripMenuItem(item.ToString())
                {
                    Tag = item
                };
                if (Equals(item, Settings.SelectedTheme)) meun.Checked = true;
                meun.Click += ThemeItemClick;
                comTheme.DropDownItems.Add(meun);
            }

            // Reset the currently selected theme to avoid inconsistencies
            // caused by manual edits in the settings file.
            switch (Opacity * 100)
            {
                case 100:
                    conHigh.Checked = true;
                    break;
                case 80:
                    conDefault.Checked = true;
                    break;
                case 60:
                    conLow.Checked = true;
                    break;
                case 40:
                    conVeryLow.Checked = true;
                    break;
            }

            // Set the minimum size
            RestrictSize = Settings.SlimMode ? RulerPainter.RulerWidthSlim : RulerPainter.RulerWidthWide;
            // apply other loaded settings
            ApplySettings();
            // 开始箭头 tracking mouse
            _mouseTracker.Start();
        }

        private void UnitItemClick(object sender, EventArgs e)
        {
            foreach (ToolStripMenuItem t in comUnits.DropDownItems) t.Checked = Equals(sender, t);
            var item = (MeasuringUnit)(sender as ToolStripMenuItem)?.Tag;
            Settings.MeasuringUnit = item;
            Invalidate();
        }

        private void ThemeItemClick(object sender, EventArgs e)
        {
            foreach (ToolStripMenuItem t in comTheme.DropDownItems) t.Checked = Equals(sender, t);
            var item = (ThemeOption)(sender as ToolStripMenuItem)?.Tag;
            Settings.SelectedTheme = item;
            Invalidate();
        }

        // a helper method to be called after settings values have changed
        private void ApplySettings()
        {
            // Set the marker limit
            CustomMarkers.Limit = Settings.MultiMarking ? int.MaxValue : 1;
        }

        private void RulerForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            _mouseTracker.Stop();
        }

        #region Input Events

        //Message result codes of WndProc that trigger resizing:
        // HTLEFT = 10 -> in left resize area 
        // HTRIGHT = 11 -> in right resize area
        // HTTOP = 12 -> in upper resize area
        // HTBOTTOM = 15 -> in lower resize area
        private const int HTLEFT = 10;
        private const int HTRIGHT = 11;
        private const int HTTOP = 12;
        private const int HTBOTTOM = 15;

        private const int GRIP_OFFSET = 5;

        // Use Windows messages to handle resizing of the ruler at the edges
        // and moving of the cursor marker.
        protected override void WndProc(ref Message m)
        {
            if (m.Msg == 0x84) //WM_NCHITTEST (sent for all mouse events)
            {
                // Get mouse position and convert to app coordinates
                var pos = Cursor.Position;
                pos = PointToClient(pos);
                // Check if inside grip area (5 pixels next to border)
                if (ResizeMode.HasFlag(RulerFormResizeMode.Horizontal))
                {
                    if (pos.X <= GRIP_OFFSET)
                    {
                        m.Result = (IntPtr)HTLEFT;
                        return;
                    }

                    if (pos.X >= ClientSize.Width - GRIP_OFFSET)
                    {
                        m.Result = (IntPtr)HTRIGHT;
                        return;
                    }
                }

                if (ResizeMode.HasFlag(RulerFormResizeMode.Vertical))
                {
                    if (pos.Y <= GRIP_OFFSET)
                    {
                        m.Result = (IntPtr)HTTOP;
                        return;
                    }

                    if (pos.Y >= ClientSize.Height - GRIP_OFFSET)
                    {
                        m.Result = (IntPtr)HTBOTTOM;
                        return;
                    }
                }
            }

            // Pass return message down to base class
            base.WndProc(ref m);
        }

        private void mouseTracker_Tick(object sender, EventArgs e)
        {
            if (Settings.FollowMousePointer)
            {
                int offsetX, offsetY;
                if (Settings.FollowMousePointerCenter)
                {
                    offsetX = Size.Width / 2;
                    offsetY = ResizeMode == RulerFormResizeMode.Vertical ? Size.Height / 2 : RestrictSize / 2;
                }
                else
                {
                    offsetX = 10;
                    offsetY = 10;
                }

                Location = new Point(Cursor.Position.X - offsetX, Cursor.Position.Y - offsetY);
            }
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Space:
                    ToggleRulerMode();
                    break;
                case Keys.J:
                    conSlimMode.PerformClick();
                    break;
                case Keys.Escape:
                    conExit.PerformClick();
                    break;
                case Keys.Z:
                    conMeasure.PerformClick();
                    break;
                case Keys.S:
                    conTopmost.PerformClick();
                    break;
                case Keys.V:
                    ToggleVertical();
                    break;
                case Keys.M:
                    conMarkCenter.PerformClick();
                    break;
                case Keys.T:
                    if (e.Control)
                    {
                        if (Settings.SelectedTheme == ThemeOption.白色)
                            Settings.SelectedTheme = ThemeOption.黑色;
                        else
                            Settings.SelectedTheme = ThemeOption.白色;
                    }
                    else
                    {
                        conMarkThirds.PerformClick();
                    }

                    break;
                case Keys.G:
                    conMarkGolden.PerformClick();
                    break;
                case Keys.P:
                    conMarkMouse.PerformClick();
                    break;
                case Keys.Delete:
                    conClearCustomMarker.PerformClick();
                    break;
                case Keys.C:
                    if (e.Control)
                    {
                        // copy size
                        Clipboard.SetText($"{Width}, {Height}");
                    }
                    else
                    {
                        // clear first custom marker
                        if (CustomMarkers.Markers.Count > 0)
                        {
                            CustomMarkers.RemoveFirstMarker();
                            Invalidate();
                        }
                    }

                    break;
                case Keys.L:
                    CustomMarkers.AddMarker((Point)Size, RestrictSize);
                    Invalidate();
                    break;
                case Keys.W:
                    conFollowMousePointer.PerformClick();
                    break;
                case Keys.Oemplus:
                    if (e.Control) ChangeOpacity(true);
                    break;
                case Keys.OemMinus:
                    if (e.Control) ChangeOpacity(false);
                    break;
                default:
                    if (e.Control) ResizeKeyDown(e);
                    else if (e.Alt) DockKeyDown(e);
                    else MoveKeyDown(e);
                    break;
            }

            base.OnKeyDown(e);
        }

        /// <summary>
        ///     Handles moving key events.
        /// </summary>
        private void MoveKeyDown(KeyEventArgs e)
        {
            var step = e.Shift ? Settings.MediumStep : Settings.SmallStep;
            switch (e.KeyCode)
            {
                case Keys.Left:
                    Left -= step;
                    break;
                case Keys.Right:
                    Left += step;
                    break;
                case Keys.Up:
                    Top -= step;
                    break;
                case Keys.Down:
                    Top += step;
                    break;
            }
        }

        /// <summary>
        ///     Handles resizing key events.
        /// </summary>
        private void ResizeKeyDown(KeyEventArgs e)
        {
            var step = e.Shift ? Settings.MediumStep : Settings.SmallStep;
            switch (e.KeyCode)
            {
                case Keys.Left:
                    Width -= step;
                    break;
                case Keys.Right:
                    Width += step;
                    break;
                case Keys.Up:
                    Height -= step;
                    break;
                case Keys.Down:
                    Height += step;
                    break;
            }
        }

        /// <summary>
        ///     Handles key events for docking to borders.
        private void DockKeyDown(KeyEventArgs e)
        {
            var screen = Screen.FromControl(this);
            switch (e.KeyCode)
            {
                case Keys.Left:
                    Left = screen.WorkingArea.Left;
                    break;
                case Keys.Right:
                    Left = screen.WorkingArea.Right - Width;
                    break;
                case Keys.Up:
                    Top = screen.WorkingArea.Top;
                    break;
                case Keys.Down:
                    Top = screen.WorkingArea.Bottom - Height;
                    break;
            }
        }

        private void RulerForm_MouseWheel(object sender, MouseEventArgs e)
        {
            // Resize according to mouse scroll direction.
            var amount = Math.Sign(e.Delta);
            if (ModifierKeys.HasFlag(Keys.Shift))
                amount *= Settings.LargeStep;
            else amount *= Settings.SmallStep;
            // Add to width or height according to current mode and mouse location
            if (ResizeMode == RulerFormResizeMode.Horizontal)
            {
                Width += amount;
            }
            else if (ResizeMode == RulerFormResizeMode.Vertical)
            {
                Height += amount;
            }
            else
            {
                if (e.Y > RestrictSize)
                    Height += amount;
                else Width += amount;
            }
        }

        private void RulerForm_MouseClick(object sender, MouseEventArgs e)
        {
            var marker = CustomMarkers.GetMarker(e.Location, RestrictSize);
            if (marker != RulerMarker.Default)
            {
                CustomMarkers.RemoveMarker(marker);
                Invalidate();
            }
        }

        private void RulerForm_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            // Add a marker at the cursor position.
            CustomMarkers.AddMarker(e.Location, RestrictSize, ResizeMode == RulerFormResizeMode.Vertical);
        }

        #endregion

        #region Draw Components

        protected override void OnPaint(PaintEventArgs e)
        {
            using (var buffer = BufferedGraphicsManager.Current.Allocate(e.Graphics, e.ClipRectangle))
            {
                buffer.Graphics.FillRectangle(new SolidBrush(TransparencyKey), e.ClipRectangle);
                // paint the ruler into buffer
                _painter.Update(buffer.Graphics, Settings, ResizeMode);
                _painter.PaintRuler();
                _painter.PaintMarkers(CustomMarkers, _mouseTracker.Position);
                // paint buffer onto screen
                buffer.Render();
                buffer.Dispose();
            }
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            // draw transparent background
            var offset = RestrictSize;
            e.Graphics.FillRectangle(
                new SolidBrush(TransparencyKey),
                new Rectangle(
                    offset, offset,
                    Width - offset, Height - offset
                )
            );
        }

        #endregion

        #region Ruler Mode

        private void ToggleRulerMode()
        {
            ResizeMode = (RulerFormResizeMode)(((int)ResizeMode + 1) % 3 + 1);
        }

        private void ToggleVertical()
        {
            if (ResizeMode == RulerFormResizeMode.Vertical)
            {
                var length = Height;
                ResizeMode = RulerFormResizeMode.Horizontal;
                Width = length;
            }
            else
            {
                var length = Width;
                ResizeMode = RulerFormResizeMode.Vertical;
                Height = length;
            }
        }

        #endregion

        #region Context Menu

        // Load current context menu state
        private void contxtMenu_Opening(object sender, CancelEventArgs e)
        {
            conSlimMode.Checked = Settings.SlimMode;
            conTopmost.Checked = TopMost;
            conFollowMousePointer.Checked = Settings.FollowMousePointer;
            SetAppearanceCheckboxes();
            // Show ruler if it was minimized
            if (WindowState == FormWindowState.Minimized)
            {
                WindowState = FormWindowState.Normal;
                Activate();
            }
        }

        private void SetAppearanceCheckboxes()
        {
            conMarkCenter.Checked = Settings.ShowCenterLine;
            conMarkThirds.Checked = Settings.ShowThirdLines;
            conMarkGolden.Checked = Settings.ShowGoldenLine;
            conMarkMouse.Checked = Settings.ShowMouseLine;
            conOffsetLength.Checked = Settings.ShowOffsetLengthLabels;
        }

        private void conRulerMode_DropDownOpening(object sender, EventArgs e)
        {
            conModeHorizontal.Checked = ResizeMode == RulerFormResizeMode.Horizontal;
            conModeVertical.Checked = ResizeMode == RulerFormResizeMode.Vertical;
            conModeTwoDimensional.Checked = ResizeMode == RulerFormResizeMode.TwoDimensional;
        }

        private void conMeasure_Click(object sender, EventArgs e)
        {
            var overlay = new RulerOverlayForm
            {
                TopMost = TopMost
            };
            if (overlay.ShowDialog() == DialogResult.OK)
            {
                ResizeMode = RulerFormResizeMode.TwoDimensional;
                Location = overlay.WindowSelection.Location;
                Height = overlay.WindowSelection.Height;
                Width = overlay.WindowSelection.Width;
                CheckOutOfBounds();
                Settings.ShowOffsetLengthLabels = true;
            }
        }

        private void conFollowMousePointer_Click(object sender, EventArgs e)
        {
            Settings.FollowMousePointer = !Settings.FollowMousePointer;
        }

        private void conMarkMouse_Click(object sender, EventArgs e)
        {
            Settings.ShowMouseLine = !Settings.ShowMouseLine;
            Invalidate();
        }

        private void conMarkCenter_Click(object sender, EventArgs e)
        {
            Settings.ShowCenterLine = !Settings.ShowCenterLine;
            Invalidate();
        }

        private void conMarkThirds_Click(object sender, EventArgs e)
        {
            Settings.ShowThirdLines = !Settings.ShowThirdLines;
            Invalidate();
        }

        private void conMarkGolden_Click(object sender, EventArgs e)
        {
            Settings.ShowGoldenLine = !Settings.ShowGoldenLine;
            Invalidate();
        }

        private void conOffsetLength_Click(object sender, EventArgs e)
        {
            Settings.ShowOffsetLengthLabels = !Settings.ShowOffsetLengthLabels;
            Invalidate();
        }

        private void conClearCustomMarker_Click(object sender, EventArgs e)
        {
            CustomMarkers.Clear();
            Invalidate();
        }

        private void conTopmost_Click(object sender, EventArgs e)
        {
            TopMost = !TopMost;
        }

        private void ChangeRulerMode(object sender, EventArgs e)
        {
            foreach (ToolStripMenuItem it in conRulerMode.DropDownItems)
                it.Checked = false;
            ((ToolStripMenuItem)sender).Checked = true;
            ResizeMode = (RulerFormResizeMode)Enum.Parse(typeof(RulerFormResizeMode),
                (string)((ToolStripMenuItem)sender).Tag);
        }

        private void ChangeOpacity(bool increase)
        {
            var current = 0;
            foreach (ToolStripMenuItem item in conOpacity.DropDownItems)
            {
                if (item.Checked)
                {
                    item.Checked = false;
                    break;
                }

                current++;
            }

            // drop down items are in order from highest to lowest, therefore reverse
            var newIndex = increase ? current - 1 : current + 1;
            newIndex = Math.Max(0, Math.Min(newIndex, conOpacity.DropDownItems.Count - 1));
            ChangeOpacity(conOpacity.DropDownItems[newIndex], EventArgs.Empty);
        }

        private void ChangeOpacity(object sender, EventArgs e)
        {
            foreach (ToolStripMenuItem it in conOpacity.DropDownItems)
                it.Checked = false;
            ((ToolStripMenuItem)sender).Checked = true;
            var opacity = int.Parse((string)((ToolStripMenuItem)sender).Tag);
            Opacity = (double)opacity / 100;
        }

        private void conSlimMode_Click(object sender, EventArgs e)
        {
            Settings.SlimMode = !Settings.SlimMode;
            RestrictSize = Settings.SlimMode ? RulerPainter.RulerWidthSlim : RulerPainter.RulerWidthWide;
        }

        private void conLength_Click(object sender, EventArgs e)
        {
            var sizeForm = new SetSizeForm(Size, Settings)
            {
                TopMost = TopMost
            };
            if (sizeForm.ShowDialog(this) == DialogResult.OK)
            {
                var w = (int)Math.Round(sizeForm.RulerWidth);
                var h = (int)Math.Round(sizeForm.RulerHeight);
                // if both width and height are set, switch into 2d mode
                if (w > RestrictSize && h > RestrictSize)
                    ResizeMode = RulerFormResizeMode.TwoDimensional;
                Width = w;
                Height = h;
            }
        }

        private void SetToolTip()
        {
            // Tool tip
            if (Settings.ShowToolTip)
                rulerToolTip.SetToolTip(this,
                    string.Format("尺寸".CurrentText()
                    + ": {0} * {1} px\n"
                    + "鼠标".CurrentText()
                    + ": {2}", Width, Height, $"{Left}, {Top}"));
            else rulerToolTip.SetToolTip(this, string.Empty);
        }

        private void RulerForm_Move(object sender, EventArgs e)
        {
            SetToolTip();
        }

        private void RulerForm_SizeChanged(object sender, EventArgs e)
        {
            SetToolTip();
        }

        private void conCalibrate_Click(object sender, EventArgs e)
        {
            var scalingForm = new CalibrationForm(Settings) { Icon = Icon };
            if (scalingForm.ShowDialog(this) == DialogResult.OK)
            {
                Settings.MonitorDpi = scalingForm.MonitorDpi;
                Settings.MonitorScaling = scalingForm.MonitorScaling;
            }
        }

        private void conExit_Click(object sender, EventArgs e)
        {
            Close();
        }

        #endregion
    }
}