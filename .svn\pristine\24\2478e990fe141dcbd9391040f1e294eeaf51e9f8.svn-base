// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class DropTargetPattern : BasePattern
    {
        public static readonly AutomationProperty DropTargetEffectProperty =
            DropTargetPatternIdentifiers.DropTargetEffectProperty;

        public static readonly AutomationProperty DropTargetEffectsProperty =
            DropTargetPatternIdentifiers.DropTargetEffectsProperty;

        public static readonly AutomationEvent DropTargetEnterEvent = DropTargetPatternIdentifiers.DragEnterEvent;
        public static readonly AutomationEvent DropTargetLeaveEvent = DropTargetPatternIdentifiers.DragLeaveEvent;
        public static readonly AutomationEvent DroppedEvent = DropTargetPatternIdentifiers.DroppedEvent;
        public static readonly AutomationPattern Pattern = DropTargetPatternIdentifiers.Pattern;

        private IUIAutomationDropTargetPattern _pattern;


        private DropTargetPattern(AutomationElement el, IUIAutomationDropTargetPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new DropTargetPattern(el, (IUIAutomationDropTargetPattern) pattern, cached);
        }
    }
}