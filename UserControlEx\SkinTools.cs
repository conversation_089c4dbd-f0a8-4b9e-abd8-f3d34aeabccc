﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace OCRTools.UserControlEx
{
    public class SkinTools
    {

        /// <summary>
        /// 样式绘制圆角
        /// </summary>
        /// <param name="control">控件</param>
        /// <param name="bounds">范围</param>
        /// <param name="radius">圆角大小</param>
        /// <param name="roundStyle">圆角样式</param>
        public static void CreateRegion(
             Control control,
             Rectangle bounds,
             int radius,
             RoundStyle roundStyle)
        {
            using (GraphicsPath path =
                GraphicsPathHelper.CreatePath(
                bounds, radius, roundStyle, true))
            {
                Region region = new Region(path);
                path.Widen(Pens.White);
                region.Union(path);
                control.Region = region;
            }
        }

        /// <summary>
        /// 判断颜色偏向于暗色或亮色(true为偏向于暗色，false位偏向于亮色。)
        /// </summary>
        /// <param name="c">要判断的颜色</param>
        /// <returns>true为偏向于暗色，false位偏向于亮色。</returns>
        public static bool ColorSlantsDarkOrBright(Color c)
        {
            HSL hsl = ColorToHSL(c);
            if (hsl.Luminance < 0.15d)
            {
                return true;
            }
            else if (hsl.Luminance < 0.35d)
            {
                return true;
            }
            else if (hsl.Luminance < 0.85d)
            {
                return false;
            }
            else
            {
                return false;
            }
        }

        public static HSL ColorToHSL(Color color)
        {
            int hue;
            double saturation;

            double r = (color.R / 255.0d);
            double g = (color.G / 255.0d);
            double b = (color.B / 255.0d);

            double min = Math.Min(Math.Min(r, g), b);
            double max = Math.Max(Math.Max(r, g), b);
            double delta = max - min;

            // get luminance value
            var luminance = (max + min) / 2;

            if (delta == 0)
            {
                // gray color
                hue = 0;
                saturation = 0.0;
            }
            else
            {
                // get saturation value
                saturation = (luminance < 0.5) ?
                    (delta / (max + min)) : (delta / (2 - max - min));

                // get hue value
                double del_r = (((max - r) / 6) + (delta / 2)) / delta;
                double del_g = (((max - g) / 6) + (delta / 2)) / delta;
                double del_b = (((max - b) / 6) + (delta / 2)) / delta;
                double dHue;

                if (r == max)
                {
                    dHue = del_b - del_g;
                }
                else if (g == max)
                {
                    dHue = (1.0 / 3) + del_r - del_b;
                }
                else
                {
                    dHue = (2.0 / 3) + del_g - del_r;
                }

                // correct hue if needed
                if (dHue < 0)
                {
                    dHue += 1;
                }

                if (dHue > 1)
                {
                    dHue -= 1;
                }

                hue = (int)(dHue * 360);
            }

            return new HSL(hue, saturation, luminance);
        }

        #region 获取窗体不透明区域
        /// <summary> 
        /// 创建支持位图区域的控件（目前有button和form）
        /// </summary> 
        /// <param name="control">控件</param> 
        /// <param name="bitmap">位图</param>
        /// <param name="Alpha">小于此透明值的去除</param> 
        public static void CreateControlRegion(Control control, Bitmap bitmap, int Alpha)
        {
            //判断是否存在控件和位图
            if (control == null || bitmap == null)
                return;

            control.Width = bitmap.Width;
            control.Height = bitmap.Height;
            //当控件是form时
            if (control is Form form)
            {
                //强制转换为FORM
                //当FORM的边界FormBorderStyle不为NONE时，应将FORM的大小设置成比位图大小稍大一点
                form.Width = form.Width;
                form.Height = form.Height;
                //没有边界
                form.FormBorderStyle = FormBorderStyle.None;
                //将位图设置成窗体背景图片
                form.BackgroundImage = bitmap;
                //计算位图中不透明部分的边界
                Bitmap back = new Bitmap(bitmap.Width, bitmap.Height);
                Graphics g = Graphics.FromImage(back);
                foreach (Control c in form.Controls)
                {
                    g.FillRectangle(new SolidBrush(form.BackColor), new Rectangle(c.Location, c.Size));
                }
                g.DrawImage(bitmap, 0, 0);
                GraphicsPath graphicsPath = CalculateControlGraphicsPath(back, Alpha);
                //应用新的区域
                form.Region = new Region(graphicsPath);
                GC.Collect();
            }
            //当控件是button时
            else if (control is SkinButton skinButton)
            {
                //强制转换为 button
                //计算位图中不透明部分的边界
                GraphicsPath graphicsPath = CalculateControlGraphicsPath(bitmap, Alpha);
                //应用新的区域
                skinButton.Region = new Region(graphicsPath);
            }
        }

        //计算位图中不透明部分的边界
        public static GraphicsPath CalculateControlGraphicsPath(Bitmap bitmap, int Alpha)
        {
            //创建快速位图遍历
            FastBitmap bmp = new FastBitmap(bitmap);
            bmp.Lock();
            //创建 GraphicsPath
            GraphicsPath graphicsPath = new GraphicsPath();
            //第一个找到点的X
            // 偏历所有行（Y方向）
            for (int row = 0; row < bitmap.Height; row++)
            {
                //重设
                //偏历所有列（X方向）
                for (int col = 0; col < bitmap.Width; col++)
                {
                    //如果是不需要透明处理的点则标记，然后继续偏历
                    if (bmp.GetPixel(col, row).A >= Alpha)
                    {
                        //记录当前
                        var colOpaquePixel = col;
                        //建立新变量来记录当前点
                        int colNext = col;
                        ///从找到的不透明点开始，继续寻找不透明点,一直到找到或则达到图片宽度 
                        for (colNext = colOpaquePixel; colNext < bitmap.Width; colNext++)
                            if (bmp.GetPixel(colNext, row).A < Alpha)
                                break;
                        //将不透明点加到graphics path
                        graphicsPath.AddRectangle(new Rectangle(colOpaquePixel, row, colNext - colOpaquePixel, 1));
                        col = colNext;
                    }
                }
            }
            bmp.Unlock();
            return graphicsPath;
        }
        #endregion
    }

    public class HSL
    {
        private int _hue;
        private double _saturation;
        private double _luminance;

        public int Hue
        {
            get { return _hue; }
            set
            {
                if (value < 0)
                {
                    _hue = 0;
                }
                else if (value <= 360)
                {
                    _hue = value;
                }
                else
                {
                    _hue = value % 360;
                }
            }
        }

        public double Saturation
        {
            get { return _saturation; }
            set
            {
                if (value < 0)
                {
                    _saturation = 0;
                }
                else
                {
                    _saturation = Math.Min(value, 1D);
                }
            }
        }

        public double Luminance
        {
            get { return _luminance; }
            set
            {
                if (value < 0)
                {
                    _luminance = 0;
                }
                else
                {
                    _luminance = Math.Min(value, 1D);
                }
            }
        }

        public HSL() { }

        public HSL(int hue, double saturation, double luminance)
        {
            Hue = hue;
            Saturation = saturation;
            Luminance = luminance;
        }

        public override string ToString()
        {
            return string.Format("HSL [H={0}, S={1}, L={2}]", _hue, _saturation, _luminance);
        }
    }
}
