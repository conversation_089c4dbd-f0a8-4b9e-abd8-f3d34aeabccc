﻿using System.Collections.Generic;

namespace OCRTools.ScreenCaptureLib
{
    public class RegionCaptureOptions
    {
        public const int DefaultMinimumSize = 5;
        public const int MagnifierPixelCountMinimum = 3;
        public const int MagnifierPixelCountMaximum = 35;
        public const int MagnifierPixelSizeMinimum = 1;
        public const int MagnifierPixelSizeMaximum = 31;
        public const int SnapDistance = 30;
        public const int MoveSpeedMinimum = 1;
        public const int MoveSpeedMaximum = 10;
        public int MinimumSize = DefaultMinimumSize;

        public bool QuickCrop = true;
        public bool DetectWindows = true;
        public bool DetectControls = true;
        public bool UseDimming = true;
        public string CustomInfoText = string.Empty;
        public string CustomInfoTitle = string.Empty;
        public List<SnapSize> SnapSizes = new List<SnapSize>()
        {
            new SnapSize(426, 240), // 240p
            new SnapSize(640, 360), // 360p
            new SnapSize(854, 480), // 480p
            new SnapSize(1280, 720), // 720p
            new SnapSize(1920, 1080) // 1080p
        };
        public bool ShowInfo = true;
        public bool ShowMagnifier = true;
        public bool UseSquareMagnifier = true;
        public int MagnifierPixelCount = 13; // Must be odd number like 11, 13, 15 etc.
        public int MagnifierPixelSize = 10;
        //放大镜中间的聚焦线
        public bool DrawMagnifierCenterLines = true;
        public bool ShowCrosshair = false;
        public bool EnableAnimations = true;
        public int InputDelay = 500;

        public bool SwitchToDrawingToolAfterSelection = false;
        public bool SwitchToSelectionToolAfterDrawing = false;

        public AnnotationOptions AnnotationOptions { get; set; }

        public ShapeType LastRegionTool = ShapeType.矩形区域;
        public ShapeType LastAnnotationTool = ShapeType.矩形;
        public ShapeType LastEditorTool = ShapeType.选择并移动;
        public bool IsSmallControlModel { get; set; }

        public const int BorderSizeMinimum = 1;
        public const int BorderSizeMaximum = 10;
    }
}