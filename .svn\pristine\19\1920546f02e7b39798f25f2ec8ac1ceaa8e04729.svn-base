using System;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace OCRTools
{
    internal static class TxtHelper
    {
        public static void Write(string path, string content)
        {
            try
            {
                using (FileStream stream = new FileStream(path, FileMode.Create, FileAccess.Write))
                {
                    using (StreamWriter streamWriter = new StreamWriter(stream, Encoding.UTF8))
                    {
                        streamWriter.Write(content);
                    }
                }
            }
            catch (Exception)
            {
                MessageBox.Show("文件写入出现异常！", "提醒");
            }
        }

        public static void WriteEncode(string path, string content)
        {
            if (!Directory.Exists(Path.GetDirectoryName(path)))
            {
                Directory.CreateDirectory(path);
            }
            if (!File.Exists(path))
            {
                using (File.Create(path))
                {
                }
            }
            try
            {
                using (FileStream stream = new FileStream(path, FileMode.OpenOrCreate, FileAccess.Write, FileShare.ReadWrite))
                {
                    using (StreamWriter streamWriter = new StreamWriter(stream))
                    {
                        streamWriter.Write(content);
                    }
                }
            }
            catch (Exception)
            {
                MessageBox.Show("文件写入出现异常！", "提醒");
            }
        }

        public static string ReadAllText(string path)
        {
            if (!File.Exists(path))
            {
                return "";
            }
            try
            {
                string result = "";
                using (FileStream fileStream = new FileStream(path, FileMode.OpenOrCreate, FileAccess.Read, FileShare.ReadWrite))
                {
                    using (StreamReader streamReader = new StreamReader(fileStream, Encoding.UTF8))
                    {
                        result = streamReader.ReadToEnd();
                        streamReader.Close();
                        fileStream.Close();
                    }
                }
                return result;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public static string Readlastline(string path)
        {
            string[] array = File.ReadAllLines(path);
            return array[array.Length - 1];
        }

        public static void Writelastline(string path, string ReadTxt)
        {
            StreamWriter streamWriter = new StreamWriter(path, append: true);
            streamWriter.WriteLine(ReadTxt);
            streamWriter.Close();
        }

        public static string ReadFirstLine(string path)
        {
            if (!File.Exists(path))
            {
                return "";
            }
            try
            {
                FileStream stream = new FileStream(path, FileMode.OpenOrCreate, FileAccess.Read, FileShare.ReadWrite);
                StreamReader streamReader = new StreamReader(stream, Encoding.Default);
                return streamReader.ReadLine();
            }
            catch (Exception)
            {
                throw;
            }
        }
    }
}
