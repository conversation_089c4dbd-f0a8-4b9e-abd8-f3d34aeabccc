namespace UtfUnknown.Core.Models.MultiByte.Chinese
{
    public class EuctwsmModel : StateMachineModel
    {
        private static readonly int[] EuctwCls =
        {
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 0, 0),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 0, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 6, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 3, 4, 4, 4, 4, 4, 4),
            BitPackage.Pack4Bits(5, 5, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 3, 1, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 0)
        };

        private static readonly int[] EuctwSt =
        {
            BitPackage.Pack4Bits(1, 1, 0, 3, 3, 3, 4, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 1, 0, 1),
            BitPackage.Pack4Bits(0, 0, 0, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(5, 1, 1, 1, 0, 1, 0, 0),
            BitPackage.Pack4Bits(0, 1, 0, 0, 0, 0, 0, 0)
        };

        private static readonly int[] EuctwCharLenTable =
        {
            0,
            0,
            1,
            2,
            2,
            2,
            3
        };

        public EuctwsmModel()
            : base(
                new BitPackage(BitPackage.indexShift4Bits, BitPackage.shiftMask4Bits, BitPackage.bitShift4Bits,
                    BitPackage.unitMask4Bits, EuctwCls), 7,
                new BitPackage(BitPackage.indexShift4Bits, BitPackage.shiftMask4Bits, BitPackage.bitShift4Bits,
                    BitPackage.unitMask4Bits, EuctwSt), EuctwCharLenTable, "euc-tw")
        {
        }
    }
}