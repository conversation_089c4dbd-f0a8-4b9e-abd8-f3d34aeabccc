﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class SetSizeForm : BaseForm
    {
        private readonly Settings _settings;
        private UnitConverter _converter;

        public SetSizeForm(Size rulerSize, Settings settings)
        {
            InitializeComponent();
            RulerWidth = rulerSize.Width;
            RulerHeight = rulerSize.Height;
            _settings = settings;
            SetConverter(settings.MeasuringUnit);
        }

        public float RulerWidth { get; private set; }
        public float RulerHeight { get; private set; }

        private void SetSizeForm_Load(object sender, EventArgs e)
        {
            foreach (Enum item in Enum.GetValues(typeof(MeasuringUnit))) comUnits.Items.Add(item.ToString());
            comUnits.SelectedIndex = (int)_settings.MeasuringUnit;
            UpdateText();
        }

        private void SetConverter(MeasuringUnit unit)
        {
            var screenSize = Screen.FromControl(this).Bounds.Size;
            _converter = new UnitConverter(unit, screenSize, _settings.MonitorDpi);
        }

        private void comUnits_SelectedIndexChanged(object sender, EventArgs e)
        {
            var unit = (MeasuringUnit)comUnits.SelectedIndex;
            SetConverter(unit);
            UpdateText();
        }

        /// <summary>
        ///     Updates the ruler length and unit symbol to the currently chosen unit.
        /// </summary>
        private void UpdateText()
        {
            numWidth.Value = (decimal)_converter.ConvertFromPixel(RulerWidth, false);
            numHeight.Value = (decimal)_converter.ConvertFromPixel(RulerHeight, true);
            lblUnit1.Text = _converter.UnitString;
            lblUnit2.Text = _converter.UnitString;
        }

        private void numWidth_ValueChanged(object sender, EventArgs e)
        {
            RulerWidth = _converter.ConvertToPixel((float)numWidth.Value, false);
        }

        private void numHeight_ValueChanged(object sender, EventArgs e)
        {
            RulerHeight = _converter.ConvertToPixel((float)numHeight.Value, true);
        }

        private void butSubmit_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
            Close();
        }
    }
}