using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Management;
using System.Net;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Windows.Forms;
using Microsoft.Win32;
using OCRTools.Common;
using OCRTools.Language;
using OCRTools.ScreenCaptureLib;

namespace OCRTools
{
    public class CommonMethod
    {
        private static readonly DefaultToastSetting ToastSetting = new DefaultToastSetting();

        private static ToolTip _txtTooTip;

        private static ToolStripDropDown _txtTipDropDown;

        private static FormTool FrmMsg;

        static CommonMethod()
        {
            InitRecentTask();
        }

        private static readonly string[] Suffix = { "Byte", "KB", "MB", "GB", "TB", "PB", "EB" };

        public static string FormatBytes(string fileName)
        {
            var size = new FileInfo(fileName).Length;
            return FormatBytes(size);
        }

        public static string FormatBytes(long bytes)
        {
            var i = 0;
            double dblSByte = bytes;
            if (bytes > 1024)
                for (i = 0; (bytes / 1024) > 0; i++, bytes /= 1024)
                    dblSByte = bytes / 1024.0;
            return string.Format("{0:0.##}{1}", dblSByte, Suffix[i]);
        }

        public static bool IsMatchUserLevel(string userLevel)
        {
            var result = true;
            if (!string.IsNullOrEmpty(userLevel))
            {
                result = userLevel.Contains("|" + Program.NowUser?.UserType + "|");
            }
            return result;
        }

        public static byte[] GetUrlBytes(string url, string cookie = null)
        {
            byte[] result = null;
            try
            {
                if (!string.IsNullOrEmpty(url))
                {
                    using (var client = new WebClient())
                    {
                        client.Proxy = WebRequest.DefaultWebProxy;
                        if (!string.IsNullOrEmpty(cookie))
                            client.Headers.Add(HttpRequestHeader.Cookie, cookie);
                        result = client.DownloadData(url);
                    }
                }
            }
            catch { }
            return result;
        }

        public const string StrKeFuQ = "365833440";
        public const string StrKeFuQun = "100029010";

        public static void OpenKeFuQ()
        {
            try
            {
                OpenUrl("http://wpa.qq.com/msgrd?v=3&uin=" + StrKeFuQ + "&site=qq&menu=yes");
                ClipboardService.SetText(StrKeFuQ);
            }
            catch { }
            ShowHelpMsg("正在打开客服QQ:" + StrKeFuQ + ".已复制到粘贴板,如打开失败,请手动添加好友！", 5000);
        }

        public static void OpenQun()
        {
            try
            {
                OpenUrl("https://qm.qq.com/cgi-bin/qm/qr?k=2DGDSxmtkAxbGL8Z9hVrx5YbXqgHfD8K&jump_from=webapi");
                ClipboardService.SetText(StrKeFuQun);
            }
            catch { }
            ShowHelpMsg("正在打开QQ群:" + StrKeFuQun + ".已复制到粘贴板,如打开失败,请手动加群！", 5000);
        }

        public static bool IsContainsChinese(string strTxt)
        {
            return Regex.IsMatch(strTxt, "[\u4e00-\u9fa5]");
        }

        //public static bool IsContainEnglish(string str)
        //{
        //    return Regex.IsMatch(str, @"[a-zA-Z]");
        //}

        public static bool IsAutoStart
        {
            get
            {
                try
                {
                    using (var currentUser = Registry.CurrentUser)
                    {
                        using (var registryKey =
                            currentUser.CreateSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"))
                        {
                            return registryKey != null && registryKey.GetValue(CommonString.ProductName) != null;
                        }
                    }
                }
                catch (Exception oe)
                {
                    Log.WriteError("IsAutoStart", oe);
                }

                return false;
            }
        }

        private static void InitRecentTask()
        {
            if (Program.RecentTasks == null) Program.RecentTasks = new List<HistoryTask>();
            try
            {
                var files = Directory.GetFiles(string.IsNullOrEmpty(CommonSetting.截图文件保存路径)
                    ? CommonString.DefaultImagePath
                    : CommonSetting.截图文件保存路径);
                files = files.Reverse().Take((int)CommonSetting.最大历史记录数量).ToArray();
                foreach (var file in files)
                {
                    AddRecentTask(new HistoryTask
                    {
                        Status = TaskStatus.History,
                        Info = new HistoryItem
                        {
                            DataType = EDataType.Image,
                            FilePath = file,
                            CreateTime = File.GetCreationTime(file)
                        }
                    });
                }
                Program.RecentTasks.Reverse();
            }
            catch (Exception oe)
            {
                Log.WriteError("InitRecentTask", oe);
            }
        }

        public static void ClearAllTask()
        {
            Program.RecentTasks.Clear();
        }

        public static void AddRecentTask(HistoryTask task)
        {
            if (task != null)
            {
                if (Program.RecentTasks.Count >= CommonSetting.最大历史记录数量 && Program.RecentTasks.Count > 0) Program.RecentTasks.RemoveAt(0);
                if (!string.IsNullOrEmpty(task.Info.FilePath))
                    task.Info.FilePath = task.Info.FilePath.Replace("\\\\", "\\");
                Program.RecentTasks.Add(task);
            }
        }

        public static void RemoveRecentTask(HistoryTask task)
        {
            if (task != null) Program.RecentTasks.Remove(task);
        }

        public static DateTime GetAssemblyDate(string fileName)
        {
            var result = DateTime.MinValue;
            try
            {
                if (File.Exists(fileName))
                {
                    var versionInfo = FileVersionInfo.GetVersionInfo(fileName);
                    if (!string.IsNullOrEmpty(versionInfo.Comments) && versionInfo.Comments.Contains("("))
                        result = SubString(versionInfo.Comments, "(").Replace("(", "").Replace(")", "").ToDateTime();
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return result;
        }

        public static DateTime GetLastWriteDate(string fileName)
        {
            var result = DateTime.MinValue;
            try
            {
                if (File.Exists(fileName))
                {
                    result = new FileInfo(fileName).LastWriteTime;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return result;
        }

        static string GetCacheFile(string fileName)
        {
            try
            {
                fileName = CommonString.DefaultCachePath + fileName + ".dat";
                if (!Directory.Exists(Path.GetDirectoryName(fileName)))
                    Directory.CreateDirectory(Path.GetDirectoryName(fileName));
            }
            catch { }
            return fileName;
        }

        public static T GetCache<T>(string cacheKey)
        {
            cacheKey = GetCacheFile(cacheKey);
            T result = default;
            try
            {
                if (File.Exists(cacheKey))
                    result = CommonString.JavaScriptSerializer.Deserialize<T>(Encoding.UTF8.GetString(Convert.FromBase64String(File.ReadAllText(cacheKey, Encoding.UTF8))));
            }
            catch { }
            try
            {
                if (result == null)
                    result = (T)Activator.CreateInstance(typeof(T));
            }
            catch { }
            return result;
        }

        public static void ClearCache(string cacheKey)
        {
            cacheKey = GetCacheFile(cacheKey);
            try
            {
                if (File.Exists(cacheKey))
                    File.Delete(cacheKey);
            }
            catch { }
        }

        public static void SaveCache<T>(T obj, string cacheKey)
        {
            cacheKey = GetCacheFile(cacheKey);
            try
            {
                File.WriteAllText(cacheKey, Convert.ToBase64String(Encoding.UTF8.GetBytes(CommonString.JavaScriptSerializer.Serialize(obj))));
            }
            catch { }
        }

        public static void ShowColorInfo(Color color, string from)
        {
            var data = ClipboardService.GetColorHtmlImage(color);
            ClipboardService.SetText(data.OriginalText);
            FrmMain.FrmTool.ViewTextImage(data);
            CommonMethod.ShowNotificationTip(data.OriginalText + "\n\n" + "已复制到粘贴板！".CurrentText(), from + " - " + CommonString.FullName.CurrentText(),
                5 * 1000);
        }

        //ShowNotificationTip(string.Format(Resources.TaskHelpers_OpenQuickScreenColorPicker_Copied_to_clipboard___0_, text),
        //                "OCRTools - " + Resources.ScreenColorPicker);
        public static void ShowNotificationTip(string text, string title = null, int duration = -1
            , string font = null, float fontSize = 15, string foreColor = null, string backColor = null, string url = null, string data = null)
        {
            ShowNotificationTip(text, title, null, duration, font, fontSize, foreColor, backColor, url, data);
        }

        public static void ShowCaptureNotificationTip(string fileName, int duration = -1)
        {
            ShowNotificationTip(null, "截图".CurrentText() + "-" + CommonString.FullName.CurrentText(), fileName, duration);
        }

        public static void ShowNotificationTip(string text, string title, string fileName = null, int duration = -1
            , string font = null, float fontSize = 15, string foreColor = null, string backColor = null, string url = null, string data = null)
        {
            if (string.IsNullOrEmpty(title)) title = CommonString.FullName.CurrentText();
            if (duration <= 0) duration = (int)(ToastSetting.ToastWindowDuration * 1000);

            var toastConfig = new NotificationFormConfig
            {
                Duration = duration,
                FadeDuration = (int)(ToastSetting.ToastWindowFadeDuration * 1000),
                Placement = ToastSetting.ToastWindowPlacement,
                Size = ToastSetting.ToastWindowSize,
                Title = title,
                Text = text,
                FilePath = fileName,
                Url = url,
                Data = data
            };
            if (!string.IsNullOrEmpty(font))
            {
                //特殊处理，发通知以Point为单位，默认13
                toastConfig.TextFont = CommonString.GetFont(font, fontSize, FontStyle.Regular, GraphicsUnit.Point, false);
            }
            if (!string.IsNullOrEmpty(foreColor))
            {
                toastConfig.TextColor = Color.FromName(foreColor);
            }
            if (!string.IsNullOrEmpty(backColor))
            {
                toastConfig.BackgroundColor = Color.FromName(backColor);
            }
            if (toastConfig.LeftClickAction == ToastClickAction.None)
            {
                if (!string.IsNullOrEmpty(toastConfig.Url))
                    toastConfig.LeftClickAction = ToastClickAction.OpenUrl;
                else if (!string.IsNullOrEmpty(toastConfig.FilePath) || toastConfig.Image != null)
                    toastConfig.LeftClickAction = ToastClickAction.ViewImage;
                else if (!string.IsNullOrEmpty(toastConfig.Data))
                    toastConfig.LeftClickAction = ToastClickAction.OpenForm;
                //toastConfig.LeftClickAction = ToastClickAction.OpenFolder;
            }
            CommonMethod.DetermineCall(FrmMain.FrmTool, () =>
            {
                NotificationForm.Show(toastConfig);
            });
        }

        public static void AutoStart(bool isAdd)
        {
            try
            {
                //if (Equals(isAdd, CommonSetting.开机启动)) return;
                using (var currentUser = Registry.CurrentUser)
                {
                    using (var registryKey =
                        currentUser.CreateSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"))
                    {
                        if (registryKey != null)
                        {
                            if (isAdd)
                            {
                                if (registryKey.GetValue(CommonString.ProductName) == null)
                                    registryKey.SetValue(CommonString.ProductName,
                                        Application.ExecutablePath.Replace("/", "\\"));
                            }
                            else
                            {
                                if (registryKey.GetValue(CommonString.ProductName) != null)
                                    registryKey.DeleteValue(CommonString.ProductName, false);
                            }

                            registryKey.Close();
                        }
                    }

                    currentUser.Close();
                }
            }
            catch (Exception)
            {
                ShowHelpMsg("设置开机启动失败，如果被拦截，请点击允许！或者右键->以管理员方式打开重试！".CurrentText());
            }
        }

        public static bool OpenFolder(string filePath)
        {
            if (!string.IsNullOrEmpty(filePath) && Directory.Exists(filePath))
                try
                {
                    using (var process = new Process())
                    {
                        var psi = new ProcessStartInfo
                        {
                            FileName = filePath
                        };

                        process.StartInfo = psi;
                        process.Start();
                    }

                    return true;
                }
                catch
                {
                }

            return false;
        }

        public static bool OpenFile(string filePath, string param = null, bool isShowWindow = true)
        {
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
            {
                using (var process = new Process())
                {
                    try
                    {
                        var psi = new ProcessStartInfo
                        {
                            FileName = filePath
                        };
                        if (!string.IsNullOrEmpty(param))
                        {
                            psi.Arguments = param;
                        }
                        if (!isShowWindow)
                        {
                            psi.CreateNoWindow = true;
                            psi.UseShellExecute = false;
                        }

                        process.StartInfo = psi;
                        process.Start();
                    }
                    catch { }
                    return true;
                }
            }

            return false;
        }

        public static bool OpenFolderWithFile(string filePath)
        {
            filePath = filePath.Replace("\\\\", "\\");
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                try
                {
                    NativeMethods.OpenFolderAndSelectFile(filePath);
                    return true;
                }
                catch
                {
                }

            return false;
        }

        public static void DetermineCall(Control ctrl, MethodInvoker method)
        {
            try
            {
                if (ctrl.InvokeRequired)
                    ctrl.Invoke(method);
                else
                    method();
            }
            catch { }
        }

        /// <summary>
        /// 异步执行UI操作 - 立即释放当前线程，在后台准备后切换到UI线程执行
        /// 适用于需要立即响应但包含耗时操作的场景
        /// </summary>
        /// <param name="ctrl">UI控件</param>
        /// <param name="method">要执行的UI操作</param>
        public static void DetermineCallAsync(Control ctrl, MethodInvoker method)
        {
            if (ctrl == null || ctrl.IsDisposed)
                return;

            Task.Run(() =>
            {
                try
                {
                    if (ctrl.IsDisposed)
                        return;

                    if (ctrl.InvokeRequired)
                    {
                        ctrl.BeginInvoke(new MethodInvoker(() =>
                        {
                            try
                            {
                                if (!ctrl.IsDisposed)
                                    method();
                            }
                            catch (Exception ex)
                            {
                                Log.WriteError("DetermineCallAsync_UI", ex);
                            }
                        }));
                    }
                    else
                    {
                        method();
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError("DetermineCallAsync", ex);
                }
            });
        }

        /// <summary>
        /// 延迟异步执行UI操作 - 在指定延迟后异步执行
        /// </summary>
        /// <param name="ctrl">UI控件</param>
        /// <param name="method">要执行的UI操作</param>
        /// <param name="delayMilliseconds">延迟毫秒数</param>
        public static void DetermineCallDelayed(Control ctrl, MethodInvoker method, int delayMilliseconds = 100)
        {
            if (ctrl == null || ctrl.IsDisposed)
                return;

            Task.Delay(delayMilliseconds).ContinueWith(_ =>
            {
                DetermineCallAsync(ctrl, method);
            }, TaskScheduler.Default);
        }

        public static void OpenUrl(string url)
        {
            if (!string.IsNullOrEmpty(url))
                Task.Factory.StartNew(() =>
                {
                    try
                    {
                        using (var process = new Process())
                        {
                            var psi = new ProcessStartInfo
                            {
                                FileName = "cmd.exe",
                                UseShellExecute = false, //不使用shell启动
                                RedirectStandardInput = true, //喊cmd接受标准输入
                                RedirectStandardOutput = false, //不想听cmd讲话所以不要他输出
                                RedirectStandardError = true, //重定向标准错误输出
                                CreateNoWindow = true //不显示窗口
                            };

                            process.StartInfo = psi;
                            process.Start();

                            //向cmd窗口发送输入信息 后面的&exit告诉cmd运行好之后就退出
                            process.StandardInput.WriteLine("start " + url.Replace("&", "^&") + " &exit");
                            process.StandardInput.AutoFlush = true;
                            process.WaitForExit();//等待程序执行完退出进程
                            process.Close();
                        }
                    }
                    catch
                    {
                    }
                });
        }

        public static void OpenForm(string cName)
        {
            if (string.IsNullOrEmpty(cName)) return;
            try
            {
                (Assembly.GetExecutingAssembly().CreateInstance(cName) as Form)?.ShowDialog();
            }
            catch
            {
            }
        }

        /// <summary>
        ///     引用user32.dll动态链接库（windows api），
        ///     使用库中定义 API：SetCursorPos
        /// </summary>
        [DllImport("user32.dll")]
        public static extern int SetCursorPos(int x, int y);
        public static void HideTxtToolTip(Control parentCtrl)
        {
            _txtTooTip?.Hide(parentCtrl);
        }

        //private static PopupForm popupForm;
        public static void ShowTxtToolTip(Control parentCtrl, string text, Point location)
        {
            if (_txtTooTip == null)
            {
                _txtTooTip = new ToolTip { ToolTipIcon = ToolTipIcon.Info, OwnerDraw = true };
                var tipStringFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center,
                    //FormatFlags = StringFormatFlags.NoWrap,
                    HotkeyPrefix = HotkeyPrefix.None
                };
                _txtTooTip.Popup += (sender, e) =>
                {
                    Size textSize = TextRenderer.MeasureText(_txtTooTip.GetToolTip(e.AssociatedControl), CommonString.GetSysBoldFont(14));
                    e.ToolTipSize = new Size((int)(textSize.Width * 1.1)
                        , (int)(textSize.Height * 2.5));
                };
                _txtTooTip.Draw += (sender, e) =>
                {
                    e.Graphics.InterpolationMode = InterpolationMode.HighQualityBilinear;
                    e.Graphics.CompositingQuality = CompositingQuality.HighQuality;
                    e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
                    e.Graphics.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;

                    using (var contentBrush = new SolidBrush(CommonSetting.Get默认背景颜色()))
                    {
                        e.Graphics.FillRectangle(contentBrush, e.Bounds);
                    }

                    e.Graphics.DrawString(e.ToolTipText, CommonString.GetSysBoldFont(14), new SolidBrush(Color.FromArgb(255, CommonSetting.Get默认文字颜色())), //SystemBrushes.ControlText,
                        new RectangleF(e.Bounds.X, e.Bounds.Y + 2, e.Bounds.Width, e.Bounds.Height - 4),
                        tipStringFormat);

                    e.DrawBorder();
                    //using (var penBorder = new Pen(Color.Gray, 1))
                    //{
                    //    Rectangle rectBorder = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width - 1, e.Bounds.Height - 1);
                    //    e.Graphics.DrawRectangle(penBorder, e.Bounds);
                    //}
                };
            }

            _txtTooTip.Show(text, parentCtrl, location);
        }

        //static List<float> ScaleStep = new List<float>() { 10, 1, 0.1f };
        public static Font ScaleLabelByHeight(string strText, Font baseFont, Size baseSize, float maxSize = 0)
        {
            var font = string.IsNullOrWhiteSpace(strText) ? baseFont : ScaleLabelBySize(strText, baseFont, baseSize, maxSize);
            return font;
        }

        static Font ScaleLabelBySize(string strText, Font baseFont, Size baseSize, float maxSize = 0)
        {
            // 初始尝试
            var initialSize = GetFontSize(strText, baseFont, baseSize);
            bool isTooLarge = initialSize.Height > baseSize.Height;

            // 设置搜索范围
            float minSize = 1.0f;
            float maxFontSize = maxSize > 0 ? maxSize : 72.0f;

            // 使用递归二分法快速查找
            Font resultFont = FindOptimalFontSize(strText, baseFont, baseSize,
                                                 isTooLarge ? minSize : baseFont.Size,  // 下界
                                                 isTooLarge ? baseFont.Size : maxFontSize,  // 上界
                                                 isTooLarge  // 搜索方向
                                                 );

            // 应用最大字体限制
            if (maxSize > 0 && resultFont.Size > maxSize)
            {
                resultFont = CommonString.GetFont(baseFont.FontFamily.Name, maxSize, baseFont.Style, baseFont.Unit, false);
            }

            return resultFont;
        }

        // 递归查找最佳字体大小
        private static Font FindOptimalFontSize(string text, Font baseFont, Size containerSize,
                                               float minSize, float maxSize, bool wasTooLarge, int depth = 1)
        {
            // 防止过深递归和确保精度
            if (depth > 10 || maxSize - minSize < 0.2f)
            {
                return CommonString.GetFont(baseFont.FontFamily.Name, minSize, baseFont.Style, baseFont.Unit, false);
            }

            // 采用自适应步长 - 开始大步尝试，后续逐渐精细化
            float jumpFactor = wasTooLarge ? 0.5f : 2.0f; // 初始大幅调整
            float newSize;

            if (depth <= 3) // 前几次使用指数调整
            {
                newSize = wasTooLarge ?
                    minSize + (maxSize - minSize) * 0.3f : // 缩小时偏向较小值
                    minSize + (maxSize - minSize) * 0.7f;  // 放大时偏向较大值
            }
            else // 后续使用更精确的黄金分割
            {
                // 黄金分割比 ~0.618
                newSize = minSize + (maxSize - minSize) * 0.618f;
            }

            // 测试新尺寸
            Font testFont = CommonString.GetFont(baseFont.FontFamily.Name, newSize, baseFont.Style, baseFont.Unit, false);
            Size textSize = GetFontSize(text, testFont, containerSize);

            bool isCurrentTooLarge = textSize.Height > containerSize.Height;

            if (isCurrentTooLarge)
            {
                depth++;
                // 字体太大，缩小搜索上界
                return FindOptimalFontSize(text, baseFont, containerSize,
                                           minSize, newSize, true, depth);
            }
            else if (textSize.Height < containerSize.Height * 0.9f)
            {
                depth++;
                // 字体太小，提高搜索下界，但保留当前有效的字体大小
                // 这里我们保留找到的有效字体，但继续搜索可能存在的更大有效字体
                Font betterFont = FindOptimalFontSize(text, baseFont, containerSize,
                                                    newSize, maxSize, false, depth);

                // 比较找到的字体与当前字体，选择更大的有效字体
                return betterFont.Size > testFont.Size ? betterFont : testFont;
            }
            else
            {
                // 找到合适大小，无需继续搜索
                return testFont;
            }
        }

        private static Size GetFontSize(string text, Font font, Size size)
        {
            return TextRenderer.MeasureText(text, font, size, TextFormatFlags.WordBreak);
        }

        public static void HideTxtToolTipContextMenu()
        {
            _txtTipDropDown?.Hide();
        }

        public static void ShowTxtToolTipContextMenu(Control ctrl, string text, Point location)
        {
            TextBox txtBox;
            if (_txtTipDropDown == null)
            {
                _txtTipDropDown = new ToolStripDropDown
                {
                    Padding = Padding.Empty,
                    Margin = Padding.Empty,
                    TabStop = false
                };
                txtBox = new TextBox
                {
                    BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle,
                    AutoSize = false,
                    Multiline = true,
                    Margin = CommonString.PaddingZero,
                    Font = CommonSetting.Get默认文本字体(),
                    //Location = ctrl.Location,
                    TabStop = false
                };
                ToolStripItem lblInfo = new ToolStripControlHost(txtBox)
                {
                    //DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                    AutoSize = false,
                    Padding = CommonString.PaddingZero,
                    Margin = CommonString.PaddingZero,
                    DisplayStyle = ToolStripItemDisplayStyle.Text
                    //Image = Properties.Resources.复制,
                };
                _txtTipDropDown.Items.Add(lblInfo);
            }
            else
            {
                txtBox = (_txtTipDropDown.Items[0] as ToolStripControlHost)?.Control as TextBox;
            }

            if (txtBox != null)
            {
                txtBox.Text = text;
                var preferredSize = txtBox.PreferredSize;
                if (preferredSize.Width < 100) preferredSize.Width = (int)(preferredSize.Width * 1.5d);
                //preferredSize.Width = Math.Max(preferredSize.Width, ctrl.Width);
                txtBox.Size = preferredSize;
            }

            _txtTipDropDown.Show(ctrl, location);

            if (txtBox != null)
            {
                txtBox.Focus();
                txtBox.SelectAll();
            }
        }

        public static void SetStyle(Control control, ControlStyles styles,
            bool newValue)
        {
            typeof(Control).GetMethod("SetStyle",
                BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.InvokeMethod)?.Invoke(control, new object[] { styles, newValue });
        }

        //双缓冲
        public static void EnableDoubleBuffering(Control ctrl)
        {
            try
            {
                ctrl.GetType().GetProperty("DoubleBuffered", BindingFlags.Instance | BindingFlags.NonPublic)?.SetValue(ctrl, true, null);

                foreach (Control subCtrl in ctrl.Controls) EnableDoubleBuffering(subCtrl);
            }
            catch
            {
            }
        }

        public static string GetFileExt(string fileName, string fileExt = null)
        {
            try
            {
                if (string.IsNullOrEmpty(fileExt))
                {
                    if (fileName.StartsWith("data:txt"))
                        fileExt = CommonString.StrDefaultTxtType;
                    else if (fileName.StartsWith("data:") || fileName.StartsWith("http"))
                        fileExt = CommonString.StrDefaultImgType;
                }

                if (string.IsNullOrEmpty(fileExt))
                {
                    fileExt = Path.GetExtension(fileName).TrimStart('.').ToLower();
                    if (!string.IsNullOrEmpty(fileExt) && fileExt.Contains("?"))
                        fileExt = CommonString.SubString(fileExt, "", "?");
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe);
            }

            return fileExt?.ToLower();
        }

        public static void ShowHelpMsg(string msg, int totalMilsec = 5000, string strFrom = null)
        {
            if (FrmMsg == null || FrmMsg.IsDisposed)
            {
                CommonMethod.DetermineCall(FrmMain.FrmTool, () =>
                {
                    FrmMsg = new FormTool();
                    FrmMsg.SetWindowLong();
                });
            }
            Task.Factory.StartNew(() =>
            {
                try
                {
                    if (string.IsNullOrEmpty(msg))
                    {
                        msg = string.Format("欢迎使用{0}！", CommonString.FullName.CurrentText());
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(strFrom)) msg = string.Format("{0} {1}", CommonString.FullName.CurrentText(), msg);
                    }

                    if (FrmMsg == null) return;
                    FrmMsg.DelayMilSec = totalMilsec;
                    FrmMsg.NowID = Guid.NewGuid().ToString();
                    FrmMsg.DrawStr(msg, FrmMsg.NowID);
                    FrmMsg.TopMost = true;
                    FrmMsg.BringToFront();
                }
                catch
                {
                }
            });
        }

        #region 初始化数据

        public static void Exit()
        {
            try
            {
                Log.TrackEvent("Exit", false);
                Log.FlushInsight();
            }
            catch { }

            // 清理Web组件资源
            try
            {
                OCRTools.WebBroswerEx.ApplicationWebManager.Cleanup();
            }
            catch { }

            try
            {
                CommonString.IsExit = true;
                Application.ExitThread();
                Application.Exit();
                Process.GetCurrentProcess().Kill();
            }
            catch { }

            try
            {
                Environment.Exit(0);
            }
            catch
            {
            }
        }

        #endregion

        #region 站点可用性相关

        public static string GetSubStrByUrl(string strUrl)
        {
            var strTmp = "";
            if (!string.IsNullOrEmpty(strUrl))
                try
                {
                    if (strUrl.Contains("&"))
                    {
                        strTmp = SubString(strUrl, "", "&");
                        var strEncrpt = SubString(strUrl, "&");
                        strEncrpt = CommonEncryptHelper.DesEncrypt(strEncrpt, CommonString.StrCommonEncryptKey);
                        //strURL = CommonEncryptHelper.DESDecrypt(strTmp, CommonString.StrCommonEncryptKey);
                        strTmp += "&con=" + HttpUtility.UrlEncode(strEncrpt);
                    }
                    else
                    {
                        strTmp = strUrl;
                    }
                }
                catch
                {
                    strTmp = strUrl;
                    //Log.WriteError("GetSubStrByURL出错", oe);
                }

            return strTmp;
        }

        public static int ExecTimeOutSecond = 35;

        public static string Identity = string.Empty;

        public static string UserDomainName;

        public static string GetMachineId()
        {
            var result = string.Empty;
            try
            {
                var mos = new ManagementObjectSearcher("SELECT UUID FROM Win32_ComputerSystemProduct");
                foreach (ManagementBaseObject mo in mos.Get())
                {
                    result = mo["UUID"] as string;
                }
            }
            catch
            {
            }

            return result;
        }

        private static string GetMachineGuid()
        {
            var result = string.Empty;
            try
            {
                var baseKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, Environment.Is64BitOperatingSystem ? RegistryView.Registry64 : RegistryView.Registry32);
                using (var subKey = baseKey.OpenSubKey(@"SOFTWARE\Microsoft\Cryptography", RegistryKeyPermissionCheck.ReadSubTree))
                {
                    if (subKey != null)
                    {
                        var value = subKey.GetValue("MachineGuid", "default");
                        if (value != null && value.ToString() != "default")
                        {
                            result = value.ToString();
                        }
                        subKey.Close();
                    }
                    baseKey.Close();
                    baseKey.Dispose();
                }
            }
            catch (Exception)
            {
            }

            return result;
        }

        public static string PreProcessUrl(string url)
        {
            try
            {
                var selfHost = CommonString.IsSelfHost(new Uri(url, false).Host);
                if (!Equals(selfHost, SiteType.Default))
                {
                    var headers = GetRequestHeader();
                    if (headers != null)
                    {
                        var queryString = HttpUtility.ParseQueryString(url);
                        StringBuilder sbAppendUrl = new StringBuilder();
                        foreach (string name in headers.Keys)
                        {
                            try
                            {
                                if (!queryString.AllKeys.Contains(name))
                                    sbAppendUrl.Append(string.Format("&{0}={1}", name, headers[name]));
                            }
                            catch { }
                        }
                        if (sbAppendUrl.Length > 0)
                        {
                            url += (url.Contains("?") ? "&" : "?") + sbAppendUrl.ToString().TrimStart('&');
                        }
                    }
                }
            }
            catch { }
            return url;
        }

        public static NameValueCollection GetRequestHeader()
        {
            if (string.IsNullOrEmpty(UserDomainName))
            {
                try
                {
                    UserDomainName = Environment.UserDomainName + "-" + Environment.UserName;
                }
                catch
                {
                    try
                    {
                        UserDomainName = Environment.MachineName + "-" + Environment.UserName;
                    }
                    catch { }
                }
            }
            if (string.IsNullOrEmpty(Identity))
            {
                lock (Identity)
                {
                    if (string.IsNullOrEmpty(Identity))
                    {
                        var strTmpId = GetMachineGuid() + ":" + GetMachineId();
                        if (string.IsNullOrEmpty(strTmpId))
                            try
                            {
                                var info = new FileInfo(Application.ExecutablePath);
                                strTmpId = new List<DateTime> { info.CreationTime, info.LastWriteTime }.Min().ToDateStr("yyyy-MM-dd HH:mm:ss fff");
                            }
                            catch
                            {
                                try
                                {
                                    strTmpId = File.GetCreationTime(Application.StartupPath).ToDateStr("yyyy-MM-dd HH:mm:ss fff");
                                }
                                catch { }
                            }
                        try
                        {
                            strTmpId = ToMd5(UserDomainName + strTmpId);
                        }
                        catch
                        {
                            try
                            {
                                strTmpId = GetMd5Hash(UserDomainName + strTmpId);
                            }
                            catch { }
                        }
                        Identity = strTmpId;
                    }
                }
            }
            var header = new NameValueCollection
            {
                {"mac", HttpUtility.UrlEncode(UserDomainName)},
                {"uid",Identity},
                {"ver", CommonString.DtNowDate.ToDateStr("yyyy-MM-dd HH:mm:ss")},
                {"tick",ServerTime.DateTime.Ticks.ToString()},
                {"lang",CommonSetting.语言}
            };
            if (Program.IsLogined())
            {
                header.Add("app", Program.NowUser?.Account);
                header.Add("reg", Program.NowUser?.DtReg.ToDateStr("yyyy-MM-dd HH:mm:ss"));
                header.Add("token", Program.NowUser?.Token);
            }
            return header;
        }

        static string ToMd5(string s, int len = 32)
        {
            using (var md5Hasher = new MD5CryptoServiceProvider())
            {
                var data = md5Hasher.ComputeHash(Encoding.UTF8.GetBytes(s));
                var sb = new StringBuilder();
                foreach (var t in data)
                {
                    sb.Append(t.ToString("x2"));
                }
                var result = sb.ToString();

                return len == 32 ? result : result.Substring(8, 16);
            }
        }

        static string GetMd5Hash(string input, int len = 32)
        {
            using (MD5 md5 = MD5.Create())
            {
                var data = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
                var sb = new StringBuilder();
                foreach (byte t in data)
                {
                    sb.Append(t.ToString("x2")); // 将每个字节转换为16进制字符串
                }
                var result = sb.ToString();

                return len == 32 ? result : result.Substring(8, 16);
            }
        }

        public static string GetServerHtml(string url, string strNowSite
            , bool isDecodeUrl = true, bool isPost = false, string strPost = "")
        {
            var html = "";
            try
            {
                var headers = GetRequestHeader();
                if (isDecodeUrl)
                {
                    if (isPost)
                        html = WebClientExt.GetHtml(strNowSite + GetSubStrByUrl(url), "", "", strPost, "", ExecTimeOutSecond, headers);
                    else
                        html = WebClientExt.GetHtml(strNowSite + GetSubStrByUrl(url), "", "", "", "", ExecTimeOutSecond, headers);
                }
                else
                {
                    if (isPost)
                        html = WebClientExt.GetHtml(strNowSite + url, "", "", strPost, "", ExecTimeOutSecond, headers);
                    else
                        html = WebClientExt.GetHtml(strNowSite + url, "", "", "", "", ExecTimeOutSecond, headers);
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("GetServerHtml:" + html, oe);
                //Log.WriteError("GetServerHtml出错", oe);
            }

            return html;
        }

        #endregion

        #region 共享方法

        public static string ExecCmd(string str)
        {
            //process用于调用外部程序
            using (var p = new Process())
            {
                //调用cmd.exe
                p.StartInfo.FileName = "cmd.exe";
                //不显示程序窗口
                p.StartInfo.CreateNoWindow = true;
                //是否指定操作系统外壳进程启动程序
                p.StartInfo.UseShellExecute = false;
                //可能接受来自调用程序的输入信息
                //重定向标准输入
                p.StartInfo.RedirectStandardInput = true;
                //重定向标准输出
                p.StartInfo.RedirectStandardOutput = true;
                //重定向错误输出
                p.StartInfo.RedirectStandardError = true;
                //启动程序
                p.Start();
                //输入命令
                p.StandardInput.WriteLine(str);
                //一定要关闭。
                p.StandardInput.WriteLine("exit");
                return p.StandardOutput.ReadToEnd();
            }
        }



        public static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return strSource.SubStringHorspool(strSpilt, strEnd).Trim();
        }

        #endregion

        public static DateTime ConvertToDateTime(string timestamp)
        {
            DateTime time = DateTime.MinValue;
            DateTime startTime = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
            if (timestamp.Length == 10)//精确到秒
            {
                time = startTime.AddSeconds(double.Parse(timestamp));
            }
            else if (timestamp.Length == 13)//精确到毫秒
            {
                time = startTime.AddMilliseconds(double.Parse(timestamp));
            }
            return time;
        }

        public static string NumberToLetters(int num)
        {
            string result = "";
            while (--num >= 0)
            {
                result = (char)('A' + (num % 26)) + result;
                num /= 26;
            }
            return result;
        }

        private static string GetNextRomanNumeralStep(ref int num, int step, string numeral)
        {
            string result = "";
            if (num >= step)
            {
                result = numeral.Repeat(num / step);
                num %= step;
            }
            return result;
        }

        public static string NumberToRomanNumeral(int num)
        {
            string result = "";
            result += GetNextRomanNumeralStep(ref num, 1000, "M");
            result += GetNextRomanNumeralStep(ref num, 900, "CM");
            result += GetNextRomanNumeralStep(ref num, 500, "D");
            result += GetNextRomanNumeralStep(ref num, 400, "CD");
            result += GetNextRomanNumeralStep(ref num, 100, "C");
            result += GetNextRomanNumeralStep(ref num, 90, "XC");
            result += GetNextRomanNumeralStep(ref num, 50, "L");
            result += GetNextRomanNumeralStep(ref num, 40, "XL");
            result += GetNextRomanNumeralStep(ref num, 10, "X");
            result += GetNextRomanNumeralStep(ref num, 9, "IX");
            result += GetNextRomanNumeralStep(ref num, 5, "V");
            result += GetNextRomanNumeralStep(ref num, 4, "IV");
            result += GetNextRomanNumeralStep(ref num, 1, "I");
            return result;
        }

        public static Size MeasureText(string text, Font font)
        {
            using (Graphics g = Graphics.FromHwnd(IntPtr.Zero))
            {
                return g.MeasureString(text, font).ToSize();
            }
        }

        public static Icon GetApplicationIcon()
        {
            return FrmMain.FrmTool?.Icon;
        }

        public static void LoadHtml(Control ctrl, Point location, Size size, string url, Dictionary<string, string> dicCheck = null)
        {
            ctrl.LoadHtml(location, size, url, dicCheck);
        }

        public static RegionCaptureOptions RegionCaptureOptions;

        // 缓存优化
        private static bool _baseOptionsInitialized = false;
        private static RegionCaptureOptions _baseOptions;

        public static RegionCaptureOptions GetRegionCaptureOptions(bool isQuick, string customTitle)
        {
            // 初始化基础配置（只执行一次）
            if (!_baseOptionsInitialized)
            {
                _baseOptions = new RegionCaptureOptions
                {
                    AnnotationOptions = new AnnotationOptions()
                };
                InitLastSetting(_baseOptions.AnnotationOptions);
                _baseOptionsInitialized = true;
            }

            RegionCaptureOptions = new RegionCaptureOptions
            {
                QuickCrop = isQuick,
                CustomInfoTitle = customTitle,
                AnnotationOptions = _baseOptions.AnnotationOptions
            };
            if (customTitle != null && customTitle.Contains("识别"))
            {
                RegionCaptureOptions.LastRegionTool = ShapeType.矩形区域;
            }

            return RegionCaptureOptions;
        }

        public class CaptureImageContent
        {
            public Bitmap Image { get; set; }

            public Rectangle Rectangle { get; set; }

            public Color Color { get; set; }
            public WindowInfo Window { get; set; }
            public ConcurrentBag<WindowInfo> Windows { get; internal set; }
        }

        public static CaptureImageContent EditImage(Image image)
        {
            var img = new CaptureImageContent() { Image = image == null ? null : new Bitmap(image) };
            return EditImage(img, RegionCaptureMode.Editor);
        }

        public static CaptureImageContent EditImage(CaptureImageContent img, RegionCaptureMode mode)
        {
            var result = CatchImage("图片编辑", mode, img);
            if (result?.Image != null)
            {
                SaveImage(result.Image, false, false);
            }
            return result;
        }

        public static bool IsImageEditModel(string operate)
        {
            return Equals(operate, CaptureActions.截图贴图.ToString()) || Equals(operate, CaptureActions.截图编辑.ToString());
        }

        public static CaptureImageContent CatchImage(string operate, RegionCaptureMode mode = RegionCaptureMode.Annotation
            , CaptureImageContent img = null)
        {

            CaptureImageContent result = null;
            if (mode != RegionCaptureMode.Editor)
                StaticValue.IsCatchScreen = true;
            using (var form = new RegionCaptureForm(
                mode, GetRegionCaptureOptions(true, operate)
                , img == null || img.Image == null ? null : new Bitmap(img.Image)
                ))
            {
                form.Icon = FrmMain.FrmTool.Icon;
                try
                {
                    if (img != null && img.Windows != null && Equals(operate, "图片编辑"))
                    {
                        form.ShapeManager.Windows = img.Windows;
                        form.ShapeManager.LastAllScreenRectangle = img.Rectangle;
                    }

                    form.ShowDialog();
                    form.IsExitCapture = true;

                    if (form.Result != RegionResult.Close)
                    {
                        result = new CaptureImageContent()
                        {
                            Image = form.GetResultImage(),
                            Color = form.ShapeManager?.GetCurrentColor() ?? Color.Empty,
                            Rectangle = form.RectangleResult,
                            Window = form.SelectedWindow
                        };
                        StaticValue.LastRectangle = result.Rectangle;
                        if (IsImageEditModel(operate))
                        {
                            result.Windows = form.ShapeManager?.Windows;
                        }

                    }
                }
                catch (Exception oe)
                {
                    Log.WriteError("CatchImage", oe);
                }
                finally
                {
                    if (mode != RegionCaptureMode.Editor)
                    {
                        StaticValue.IsCatchScreen = false;
                        try
                        {
                            SaveLastSetting(RegionCaptureOptions.AnnotationOptions);
                        }
                        catch (Exception oe)
                        {
                            Log.WriteError("CatchImage!Editor", oe);
                        }
                    }
                }
            }
            return result;
        }

        public static string SaveImage(Image image, bool isCapture, bool isShowToast)
        {
            if (image == null) return string.Empty;


            // 异步执行剪贴板和文件保存操作
            Task.Run(async () =>
            {
                // 异步剪贴板操作
                if (CommonSetting.复制到粘贴板)
                {
                    await ClipboardService.ClipSetImageAsync(image, false);
                }
            });

            if (CommonSetting.自动保存)
            {
                var fileName = image.GenerateImageFilePath(!isCapture);
                try
                {
                    image.SaveFileWithOutConfirm(fileName, false);

                    // Toast通知在保存完成后显示
                    if (isShowToast && isCapture && CommonSetting.显示Toast通知)
                    {
                        ShowCaptureNotificationTip(fileName);
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError("SaveImage异步保存", ex);
                }

                return fileName;
            }

            return string.Empty;
        }

        static void InitLastSetting(object objTarget, string parent = "")
        {
            var type = objTarget.GetType();
            var properties = type.GetProperties();
            if (properties.Length <= 0) return;

            foreach (var item in properties)
            {
                if (!item.CanWrite)
                {
                    continue;
                }
                var itemType = type.GetProperty(item.Name);
                var objKey = (string.IsNullOrEmpty(parent) ? "" : (parent + "-")) + item.Name;
                if (item.PropertyType != typeof(string) && item.PropertyType.IsClass)
                {
                    var objValue = itemType?.GetValue(objTarget, null);
                    InitLastSetting(objValue, objKey);
                    continue;
                }
                string strValue = CommonSetting.GetValue<string>(type.Name, objKey);
                if (strValue == null) continue;
                try
                {
                    var value = TypeDescriptor.GetConverter(item.PropertyType).ConvertFromInvariantString(strValue);
                    itemType?.SetValue(objTarget, value, null);
                }
                catch { }
            }
        }

        static void SaveLastSetting(object objTarget, string parent = "")
        {
            var type = objTarget.GetType();
            var properties = type.GetProperties();
            if (properties.Length <= 0) return;
            foreach (var item in properties)
            {
                if (!item.CanWrite)
                {
                    continue;
                }
                var objKey = (string.IsNullOrEmpty(parent) ? "" : (parent + "-")) + item.Name;
                object value = type.GetProperty(item.Name)?.GetValue(objTarget, null);
                if (item.PropertyType != typeof(string) && item.PropertyType.IsClass)
                {
                    SaveLastSetting(value, objKey);
                    continue;
                }

                if (value != null)
                {
                    var valueStr = TypeDescriptor.GetConverter(item.PropertyType).ConvertToInvariantString(value);
                    CommonSetting.SetValue(type.Name, objKey, valueStr);
                }
            }
        }

        public static Point GetPosition(ContentAlignment placement, int offset, Size backgroundSize, Size objectSize)
        {
            return GetPosition(placement, new Point(offset, offset), backgroundSize, objectSize);
        }

        public static Point GetPosition(ContentAlignment placement, Point offset, Size backgroundSize, Size objectSize)
        {
            var midX = (int)Math.Round(backgroundSize.Width / 2f - objectSize.Width / 2f);
            var midY = (int)Math.Round(backgroundSize.Height / 2f - objectSize.Height / 2f);
            var right = backgroundSize.Width - objectSize.Width;
            var bottom = backgroundSize.Height - objectSize.Height;

            switch (placement)
            {
                default:
                    return new Point(offset.X, offset.Y);
                case ContentAlignment.TopCenter:
                    return new Point(midX, offset.Y);
                case ContentAlignment.TopRight:
                    return new Point(right - offset.X, offset.Y);
                case ContentAlignment.MiddleLeft:
                    return new Point(offset.X, midY);
                case ContentAlignment.MiddleCenter:
                    return new Point(midX, midY);
                case ContentAlignment.MiddleRight:
                    return new Point(right - offset.X, midY);
                case ContentAlignment.BottomLeft:
                    return new Point(offset.X, bottom - offset.Y);
                case ContentAlignment.BottomCenter:
                    return new Point(midX, bottom - offset.Y);
                case ContentAlignment.BottomRight:
                    return new Point(right - offset.X, bottom - offset.Y);
            }
        }
    }
}