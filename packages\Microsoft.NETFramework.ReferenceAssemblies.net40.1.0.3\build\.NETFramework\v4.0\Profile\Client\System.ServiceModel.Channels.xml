﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ServiceModel.Channels</name>
  </assembly>
  <members>
    <member name="T:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement">
      <summary>The binding element that specifies the message encoding as a stream of bytes and has the option to specify the character encoding.</summary>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.#ctor(System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement" /> class with the specified quota.</summary>
      <param name="quota">The constraints on the complexity of XML messages that can be processed by endpoints configured with this binding element.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.BuildChannelFactory``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Builds the channel factory stack on the client that creates a specified type of channel for a specified context.</summary>
      <returns>A channel factory of type <paramref name="TChannel" /> for the specified context.</returns>
      <param name="context">The binding context for the channel.</param>
      <typeparam name="TChannel">The type of channel the channel factory produces.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.BuildChannelListener``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Builds the channel listener on the service that accepts a specified type of channel for a specified context.</summary>
      <returns>A channel listener of type <paramref name="TChannel" /> for the specified context.</returns>
      <param name="context">The binding context for the listener.</param>
      <typeparam name="TChannel">The type of channel the channel listener accepts.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.CanBuildChannelFactory``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Returns a value that indicates whether the current binding can build a factory for a specified type of channel and context.</summary>
      <returns>true if the specified channel factory stack can be built on the service; otherwise, false.</returns>
      <param name="context">The binding context for the channel.</param>
      <typeparam name="TChannel">The type of channel the channel factory produces.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.CanBuildChannelListener``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Returns a value that indicates whether the current binding can build a listener for a specified type of channel and context.</summary>
      <returns>true if the specified channel listener stack can be built on the service; otherwise, false.</returns>
      <param name="context">The binding context for the listener.</param>
      <typeparam name="TChannel">The type of channel the channel listener accepts.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.Clone">
      <summary>Creates a new <see cref="T:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement" /> object initialized from the current one.</summary>
      <returns>A binding element with property values equal to those of the current element.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.CreateMessageEncoderFactory">
      <summary>Creates a factory for byte stream message encoders that employ the SOAP and WS-Addressing versions and the character encoding specified by the current encoding binding element.</summary>
      <returns>The message encoder factory that this binding element creates.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.MessageVersion">
      <summary>Gets or sets the SOAP message and WS-Addressing versions that are used or expected.</summary>
      <returns>The message version that is used or expected.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.ReaderQuotas">
      <summary>Gets or sets constraints on the complexity of XML messages that can be processed by endpoints configured with this binding element.</summary>
      <returns>The complexity constraints on SOAP messages exchanged. The default values for these constraints are provided in the following remarks section.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.ShouldSerializeMessageVersion">
      <summary>Returns a value that indicates whether the <see cref="P:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.MessageVersion" /> property has changed from its default value and should be serialized.</summary>
      <returns>false.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.ShouldSerializeReaderQuotas">
      <summary>Returns a value that indicates whether the <see cref="P:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.ReaderQuotas" /> property has changed from its default value and should be serialized.</summary>
      <returns>true if the <see cref="P:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.ReaderQuotas" /> property value should be serialized; otherwise, false.</returns>
    </member>
    <member name="T:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement">
      <summary>Enables construction of a configuration element that defines the settings for a byte stream message encoding element.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement.ApplyConfiguration(System.ServiceModel.Channels.BindingElement)">
      <summary>Applies the content of a specified binding element to the current <see cref="T:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement" /> object.</summary>
      <param name="bindingElement">The binding element whose settings are to be applied to this configuration element.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement.BindingElementType">
      <summary>Gets the type of the <see cref="T:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement" />.</summary>
      <returns>A configuration element type.</returns>
    </member>
    <member name="M:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement.CopyFrom(System.ServiceModel.Configuration.ServiceModelExtensionElement)">
      <summary>Copies the content from the specified configuration section to the current <see cref="T:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement" /> object.</summary>
      <param name="from">The content being copied.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement.ReaderQuotas">
      <summary>Gets a <see cref="T:System.ServiceModel.Configuration.XmlDictionaryReaderQuotasElement" /> configuration element that defines the constraints on the complexity of SOAP messages, which can be processed by endpoints configured with this encoding.</summary>
      <returns>A configuration element.</returns>
    </member>
  </members>
</doc>