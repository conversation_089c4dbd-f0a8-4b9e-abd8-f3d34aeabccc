﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>PresentationFramework.Aero</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Windows.Themes.ButtonChrome">
      <summary>Creates the theme-specific look for Microsoft .NET Framework <see cref="T:System.Windows.Controls.Button" /> elements.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.ButtonChrome.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Windows.Themes.ButtonChrome" /> class.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ButtonChrome.Background">
      <summary>Gets or sets the brush used to fill the background of the <see cref="T:System.Windows.Controls.Button" />.  </summary>
      <returns>The brush used to fill the background of the <see cref="T:System.Windows.Controls.Button" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ButtonChrome.BackgroundProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.Background" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.Background" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.ButtonChrome.BorderBrush">
      <summary>Gets or sets the brush used to draw the outer border of the <see cref="T:System.Windows.Controls.Button" />.  </summary>
      <returns>The brush used to draw the outer border of the <see cref="T:System.Windows.Controls.Button" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ButtonChrome.BorderBrushProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.BorderBrush" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.BorderBrush" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.ButtonChrome.RenderDefaulted">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Windows.Controls.Button" /> has the appearance of the default button on the form.  </summary>
      <returns>true if the <see cref="T:System.Windows.Controls.Button" /> has the appearance of the default button; otherwise false.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ButtonChrome.RenderDefaultedProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.RenderDefaulted" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.RenderDefaulted" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.ButtonChrome.RenderMouseOver">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Windows.Controls.Button" /> appears as if the mouse is over it.  </summary>
      <returns>true if the <see cref="T:System.Windows.Controls.Button" /> appears as if the mouse is over it; otherwise false.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ButtonChrome.RenderMouseOverProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.RenderMouseOver" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.RenderMouseOver" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.ButtonChrome.RenderPressed">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Windows.Controls.Button" /> appears pressed.  </summary>
      <returns>true if the <see cref="T:System.Windows.Controls.Button" /> appears pressed; otherwise false.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ButtonChrome.RenderPressedProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.RenderPressed" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.RenderPressed" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.ButtonChrome.RoundCorners">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Windows.Controls.Button" /> has round corners.  </summary>
      <returns>true if the <see cref="T:System.Windows.Controls.Button" /> has round corners; otherwise false.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ButtonChrome.RoundCornersProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.RoundCorners" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.RoundCorners" /> dependency property.</returns>
    </member>
    <member name="T:Microsoft.Windows.Themes.DataGridHeaderBorder">
      <summary>Creates the theme specific-look for headers in .NET Framework version 4 <see cref="T:System.Windows.Controls.DataGrid" />.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.DataGridHeaderBorder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Windows.Themes.DataGridHeaderBorder" /> class. </summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsClickable">
      <summary>Gets or sets a value that indicates whether the header is clickable.</summary>
      <returns>true if the header clickable; otherwise, false. The registered default is true. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.IsClickableProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsClickable" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsClickable" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsHovered">
      <summary>Gets or sets a value that indicates whether the header appears as if the mouse pointer is moved over it.</summary>
      <returns>true if the header appears as if the mouse pointer is moved over it; otherwise, false. The registered default is false. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.IsHoveredProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsHovered" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsHovered" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsPressed">
      <summary>Gets or sets a value that indicates whether the header appears pressed.</summary>
      <returns>true if the header appears pressed; otherwise, false. The registered default is false. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.IsPressedProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsPressed" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsPressed" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsSelected">
      <summary>Gets or sets a value that indicates whether the header appears selected.</summary>
      <returns>true if the header appears selected; otherwise, false. The registered default is false. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.IsSelectedProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsSelected" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsSelected" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.Orientation">
      <summary>Gets or sets whether the header renders in the vertical direction, as a column header, or horizontal direction, as a row header.</summary>
      <returns>One of the enumeration values that indicates which direction the header renders. The registered default is <see cref="F:System.Windows.Controls.Orientation.Vertical" />. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.OrientationProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.Orientation" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.Orientation" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorBrush">
      <summary>Gets or sets the brush that draws the separation between headers.</summary>
      <returns>The brush that draws the separation between headers. The registered default is null. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorBrushProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorBrush" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorBrush" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorVisibility">
      <summary>Gets or sets the value that indicates whether the separation between headers is visible.</summary>
      <returns>One of the enumeration values that indicates whether the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorBrush" /> is visible. The registered default is <see cref="F:System.Windows.Visibility.Visible" />. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorVisibilityProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorVisibility" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorVisibility" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SortDirection">
      <summary>Gets or sets the header sort direction.</summary>
      <returns>One of the enumeration values that indicates which direction the column is sorted. The registered default is null. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.SortDirectionProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SortDirection" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SortDirection" /> dependency property.</returns>
    </member>
    <member name="T:Microsoft.Windows.Themes.ListBoxChrome">
      <summary>Creates the theme-specific look for Microsoft .NET Framework <see cref="T:System.Windows.Controls.ListBox" /> elements.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.ListBoxChrome.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Windows.Themes.ListBoxChrome" /> class.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ListBoxChrome.Background">
      <summary>Gets or sets the brush used to fill the background of the <see cref="T:System.Windows.Controls.ListBox" />.  </summary>
      <returns>The brush used to fill the background of the <see cref="T:System.Windows.Controls.ListBox" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ListBoxChrome.BackgroundProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.Background" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.Background" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.ListBoxChrome.BorderBrush">
      <summary>Gets or sets the brush used to draw the outer border of the <see cref="T:System.Windows.Controls.ListBox" />.  </summary>
      <returns>The brush used to draw the outer border of the <see cref="T:System.Windows.Controls.ListBox" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ListBoxChrome.BorderBrushProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.BorderBrush" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.BorderBrush" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.ListBoxChrome.BorderThickness">
      <summary>Gets or sets the thickness of the border of the <see cref="T:System.Windows.Controls.ListBox" />.  </summary>
      <returns>The thickness of the border.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ListBoxChrome.BorderThicknessProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.BorderThickness" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.BorderThickness" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.ListBoxChrome.RenderFocused">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Windows.Controls.ListBox" /> appears as if it has keyboard focus.  </summary>
      <returns>true if the <see cref="T:System.Windows.Controls.ListBox" /> appears as if it has keyboard focus; otherwise false.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ListBoxChrome.RenderFocusedProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.RenderFocused" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.RenderFocused" /> dependency property.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.ListBoxChrome.RenderMouseOver">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Windows.Controls.ListBox" /> appears as if the mouse is over it.  </summary>
      <returns>true if the <see cref="T:System.Windows.Controls.ListBox" /> appears as if the mouse is over it; otherwise false.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ListBoxChrome.RenderMouseOverProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.RenderMouseOver" /> dependency property.</summary>
      <returns>The identifier for the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.RenderMouseOver" /> dependency property.</returns>
    </member>
    <member name="T:Microsoft.Windows.Themes.PlatformCulture">
      <summary>Provides culture-specific information used by the Microsoft .NET Framework system themes.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.PlatformCulture.FlowDirection">
      <summary>Gets a value that specifies whether the primary text advance direction shall be left-to-right, right-to-left, or top-to-bottom.</summary>
      <returns>One of the enumeration values.</returns>
    </member>
    <member name="T:Microsoft.Windows.Themes.ProgressBarHighlightConverter">
      <summary>Creates a <see cref="T:System.Windows.Media.DrawingBrush" /> used to draw the highlighting for the <see cref="T:System.Windows.Controls.ProgressBar" />.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.ProgressBarHighlightConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Windows.Themes.ProgressBarHighlightConverter" /> class.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.ProgressBarHighlightConverter.Convert(System.Object[],System.Type,System.Object,System.Globalization.CultureInfo)">
      <summary>Creates the <see cref="T:System.Windows.Media.DrawingBrush" />.</summary>
      <returns>The <see cref="T:System.Windows.Media.DrawingBrush" />. </returns>
      <param name="values">The <see cref="T:System.Windows.Media.Brush" /> used for the <see cref="P:System.Windows.Controls.Control.Foreground" /> of the <see cref="T:System.Windows.Controls.ProgressBar" />, the <see cref="T:System.Double" /> defining the <see cref="P:System.Windows.FrameworkElement.Width" />, and the <see cref="T:System.Double" /> defining the <see cref="P:System.Windows.FrameworkElement.Height" />. </param>
      <param name="targetType">This parameter is not used.</param>
      <param name="parameter">This parameter is not used.</param>
      <param name="culture">This parameter is not used.</param>
    </member>
    <member name="M:Microsoft.Windows.Themes.ProgressBarHighlightConverter.ConvertBack(System.Object,System.Type[],System.Object,System.Globalization.CultureInfo)">
      <summary>Not implemented.</summary>
      <returns>null.</returns>
      <param name="value">This parameter is not used.</param>
      <param name="targetTypes">This parameter is not used.</param>
      <param name="parameter">This parameter is not used.</param>
      <param name="culture">This parameter is not used.</param>
    </member>
  </members>
</doc>