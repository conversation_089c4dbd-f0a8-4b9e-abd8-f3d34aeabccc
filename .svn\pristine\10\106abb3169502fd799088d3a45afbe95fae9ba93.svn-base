using OCRTools.Common;
using System;
using System.Drawing;
using System.Globalization;

namespace OCRTools
{
    public static class AutoSelectApi
    {
        public static bool IsWindowCloaked(IntPtr handle)
        {
            int pvAttribute;
            return NativeMethods.IsDWMEnabled() &&
                   NativeMethods.DwmGetWindowAttribute(handle, 14, out pvAttribute, 4) == 0 && pvAttribute != 0;
        }

        public static Rectangle ScreenToClient(Rectangle r)
        {
            return new Rectangle(ScreenToClient(r.Location), r.Size);
        }

        public static Point ScreenToClient(Point p)
        {
            var systemMetrics = NativeMethods.GetSystemMetrics(76);
            var systemMetrics2 = NativeMethods.GetSystemMetrics(77);
            return new Point(p.X - systemMetrics, p.Y - systemMetrics2);
        }

        public static Point ScreenToClient2(Point p)
        {
            var systemMetrics = NativeMethods.GetSystemMetrics(76);
            var systemMetrics2 = NativeMethods.GetSystemMetrics(77);
            return new Point(systemMetrics - p.X, systemMetrics2 - p.Y);
        }

        public struct RECT
        {
            public int Left;

            public int Top;

            public int Right;

            public int Bottom;

            public int X
            {
                get => Left;
                set
                {
                    Right -= Left - value;
                    Left = value;
                }
            }

            public int Y
            {
                get => Top;
                set
                {
                    Bottom -= Top - value;
                    Top = value;
                }
            }

            public int Width
            {
                get => Right - Left;
                set => Right = value + Left;
            }

            public int Height
            {
                get => Bottom - Top;
                set => Bottom = value + Top;
            }

            public Point Location
            {
                get => new Point(Left, Top);
                set
                {
                    X = value.X;
                    Y = value.Y;
                }
            }

            public Size Size
            {
                get => new Size(Width, Height);
                set
                {
                    Width = value.Width;
                    Height = value.Height;
                }
            }

            public RECT(int left, int top, int right, int bottom)
            {
                Left = left;
                Top = top;
                Right = right;
                Bottom = bottom;
            }

            public RECT(Rectangle r)
            {
                this = new RECT(r.Left, r.Top, r.Right, r.Bottom);
            }

            public static implicit operator Rectangle(RECT r)
            {
                return new Rectangle(r.Left, r.Top, r.Width, r.Height);
            }

            public static implicit operator RECT(Rectangle r)
            {
                return new RECT(r);
            }

            public static bool operator ==(RECT r1, RECT r2)
            {
                return r1.Equals(r2);
            }

            public static bool operator !=(RECT r1, RECT r2)
            {
                return !r1.Equals(r2);
            }

            public bool Equals(RECT r)
            {
                return r.Left == Left && r.Top == Top && r.Right == Right && r.Bottom == Bottom;
            }

            public override bool Equals(object obj)
            {
                if (obj is RECT) return Equals((RECT) obj);
                return obj is Rectangle && Equals(new RECT((Rectangle) obj));
            }

            public override int GetHashCode()
            {
                return GetHashCode();
            }

            public override string ToString()
            {
                return string.Format(CultureInfo.CurrentCulture, "{{Left={0},Top={1},Right={2},Bottom={3}}}", Left, Top,
                    Right, Bottom);
            }
        }
    }
}