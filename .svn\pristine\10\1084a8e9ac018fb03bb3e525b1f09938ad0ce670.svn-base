﻿using MetroFramework.Components;
using MetroFramework.Controls;
using System;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace OCRTools
{
    [ToolStripItemDesignerAvailability(ToolStripItemDesignerAvailability.StatusStrip)]
    public class ToolStripCheckBoxControl : ToolStripControlHost
    {
        private readonly MetroCheckBox _checkBox = new MetroCheckBox();

        public ToolStripCheckBoxControl() : base(new MetroCheckBox())
        {
            _checkBox = Control as MetroCheckBox;
        }

        public bool Checked
        {
            get => _checkBox.Checked;
            set => _checkBox.Checked = value;
        }

        public event EventHandler CheckedChanged
        {
            add => _checkBox.CheckedChanged += value;
            remove => _checkBox.CheckedChanged -= value;
        }

        public void SetStyleManager(MetroStyleManager styleManager)
        {
            _checkBox.StyleManager = styleManager;
        }
    }
}