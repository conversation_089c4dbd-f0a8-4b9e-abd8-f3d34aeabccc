﻿using OCRTools.Common;
using ShareX.ScreenCaptureLib;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools.ScrollingCapture
{
    internal class ScrollingCaptureManager : IDisposable
    {
        public ScrollingCaptureOptions Options { get; private set; }
        public Bitmap Result { get; private set; }

        private int bestMatchCount, bestMatchIndex;
        private WindowInfo selectedWindow;
        private Rectangle selectedRectangle;

        public ScrollingCaptureManager(ScrollingCaptureOptions options, WindowInfo window, Rectangle rectangle)
        {
            Options = options;
            selectedWindow = window;
            selectedRectangle = rectangle;
        }

        public void Dispose()
        {
            Result?.Dispose();
            Result = null;
        }

        public void StartCapture()
        {
            //var files = new System.Collections.Generic.List<string>(Directory.GetFiles(Application.StartupPath, "*.png")).OrderBy(p => p).ToList();

            //Result = new Bitmap(Image.FromFile(files[0]));
            //for (int i = 1; i < files.Count; i++)
            //{
            //    Result = CombineImages(Result, new Bitmap(Image.FromFile(files[i])));
            //}
            //Result.SaveFileWithOutConfirm("Result_" + DateTime.Now.ToString("HHmmssfff") + ".png");

            //return;
            if (selectedWindow != null && !selectedRectangle.IsEmpty)
            {
                bestMatchCount = 0;
                bestMatchIndex = 0;

                try
                {
                    selectedWindow.Activate();

                    Thread.Sleep(Options.StartDelay);

                    if (CommonSetting.ScrollTopMethodBeforeCapture != ScrollingCaptureScrollTopMethod.不自动滚动至顶部.ToString())
                    {
                        InputHelpers.SendKeyPress(VirtualKeyCode.Home);

                        SendKeys.SendWait("{HOME}");

                        NativeMethods.SendMessage(selectedWindow.IsParentHandleCreated ? selectedWindow.ParentHandle : selectedWindow.Handle, 0x0115, (int)ScrollBarCommands.SB_TOP, 0);

                        Thread.Sleep(Options.ScrollDelay);
                    }

                    var _autoChangeScroll = CommonSetting.ScrollMethod == ScrollingCaptureScrollMethod.自动尝试所有方法直到某方法生效.ToString();
                    ScrollingCaptureScrollMethod _currentScrollMethod = ScrollingCaptureScrollMethod.发送滚动消息至窗口或控件;
                    if (!_autoChangeScroll)
                        _currentScrollMethod = CommonSetting.ConvertToEnum(CommonSetting.ScrollMethod, _currentScrollMethod);

                    var _currentScrollCount = 0;
                    Bitmap lastImage = null;

                    while (true)
                    {
                        Thread.Sleep(Options.ScrollDelay);

                        var bmp = Screenshot.CaptureRectangle(selectedRectangle, selectedRectangle);

                        //bmp.SaveFileWithOutConfirm(_currentScrollCount + "Begin_" + DateTime.Now.ToString("HHmmssfff") + ".png");

                        if (CompareLastTwoImages(lastImage, bmp))
                        {
                            //自动尝试所有方法直到某方法生效
                            if (_autoChangeScroll && _currentScrollMethod != ScrollingCaptureScrollMethod.模拟按下Down按键)
                            {
                                _currentScrollMethod = (ScrollingCaptureScrollMethod)(_currentScrollMethod.GetHashCode() + 1);
                            }
                            else
                            {
                                break;
                            }
                        }

                        switch (_currentScrollMethod)
                        {
                            case ScrollingCaptureScrollMethod.发送滚动消息至窗口或控件:
                                NativeMethods.SendMessage(selectedWindow.IsParentHandleCreated ? selectedWindow.ParentHandle : selectedWindow.Handle, 0x0115, (int)ScrollBarCommands.SB_PAGEDOWN, 0);
                                break;
                            case ScrollingCaptureScrollMethod.模拟按下Page_Down按钮:
                                if (!InputHelpers.SendKeyPress(VirtualKeyCode.Next))//, Options.ScrollAmount
                                {
                                    SendKeys.SendWait("{PGDN}");
                                }
                                break;
                            case ScrollingCaptureScrollMethod.模拟按下Down按键:
                                if (!InputHelpers.SendKeyPress(VirtualKeyCode.Down, Options.ScrollAmount))
                                {
                                    SendKeys.SendWait("{DOWN}");
                                }
                                break;
                            case ScrollingCaptureScrollMethod.模拟鼠标滚轮滚动:
                                InputHelpers.SendMouseWheel(-120 * Options.ScrollAmount);
                                break;
                        }
                        _currentScrollCount++;

                        Result = CombineImages(Result, bmp);
                        //Result.SaveFileWithOutConfirm(_currentScrollCount + "Result_" + DateTime.Now.ToString("HHmmssfff") + ".png");

                        lastImage = bmp;
                    }
                }
                finally
                {
                }
            }
        }

        private bool CompareLastTwoImages(Bitmap lastImage, Bitmap currentImg)
        {
            if (lastImage != null)
            {
                return ImageProcessHelper.IsImagesEqual(lastImage, currentImg);
            }

            return false;
        }

        private Bitmap CombineImages(Bitmap result, Bitmap currentImage, bool isGuess = true)
        {
            if (result == null)
            {
                return (Bitmap)currentImage.Clone();
            }

            int matchCount = 0;
            int matchIndex = 0;
            int matchLimit = currentImage.Height / 2;
            int ignoreSideOffset = Math.Max(50, currentImage.Width / 20);

            if (currentImage.Width < ignoreSideOffset * 3)
            {
                ignoreSideOffset = 0;
            }

            Rectangle rect = new Rectangle(ignoreSideOffset, result.Height - currentImage.Height, currentImage.Width - ignoreSideOffset * 2, currentImage.Height);

            BitmapData bdResult = result.LockBits(new Rectangle(0, 0, result.Width, result.Height), ImageLockMode.ReadOnly, PixelFormat.Format32bppArgb);
            BitmapData bdCurrentImage = currentImage.LockBits(new Rectangle(0, 0, currentImage.Width, currentImage.Height), ImageLockMode.ReadOnly, PixelFormat.Format32bppArgb);
            int stride = bdResult.Stride;
            int pixelSize = stride / result.Width;
            IntPtr resultScan0 = bdResult.Scan0 + pixelSize * ignoreSideOffset;
            IntPtr currentImageScan0 = bdCurrentImage.Scan0 + pixelSize * ignoreSideOffset;
            int rectBottom = rect.Bottom - 1;
            int compareLength = pixelSize * rect.Width;

            for (int currentImageY = currentImage.Height - 1; currentImageY >= 0 && matchCount < matchLimit; currentImageY--)
            {
                int currentMatchCount = 0;

                for (int y = 0; currentImageY - y >= 0 && currentMatchCount < matchLimit; y++)
                {
                    if (NativeMethods.memcmp(resultScan0 + ((rectBottom - y) * stride), currentImageScan0 + ((currentImageY - y) * stride), compareLength) == 0)
                    {
                        currentMatchCount++;
                    }
                    else
                    {
                        break;
                    }
                }

                if (currentMatchCount > matchCount)
                {
                    matchCount = currentMatchCount;
                    matchIndex = currentImageY;
                }
            }

            result.UnlockBits(bdResult);
            currentImage.UnlockBits(bdCurrentImage);

            if (matchCount == 0 && bestMatchCount > 0)
            {
                matchCount = bestMatchCount;
                matchIndex = bestMatchIndex;
            }

            if (matchCount > 0)
            {
                int matchHeight = currentImage.Height - matchIndex - 1;

                if (matchHeight > 0)
                {
                    if (matchCount > bestMatchCount)
                    {
                        bestMatchCount = matchCount;
                        bestMatchIndex = matchIndex;
                    }

                    Bitmap newResult = new Bitmap(result.Width, result.Height + matchHeight);

                    using (Graphics g = Graphics.FromImage(newResult))
                    {
                        g.DrawImage(result, new Rectangle(0, 0, result.Width, result.Height),
                            new Rectangle(0, 0, result.Width, result.Height), GraphicsUnit.Pixel);
                        g.DrawImage(currentImage, new Rectangle(0, result.Height, currentImage.Width, matchHeight),
                            new Rectangle(0, matchIndex + 1, currentImage.Width, matchHeight), GraphicsUnit.Pixel);
                    }

                    result.Dispose();
                    result = newResult;
                }
            }
            else
            {
                if (isGuess)
                {
                    Rectangle notSameRect = GuessEdges(result, currentImage);

                    if (!Equals(notSameRect, Rectangle.Empty))
                    {
                        var cropImgRect = new Rectangle(notSameRect.X, notSameRect.Y, notSameRect.Width - notSameRect.X, notSameRect.Height - notSameRect.Y);

                        var newBmp1 = ImageProcessHelper.CropBitmap(Result, cropImgRect);
                        //newBmp1.SaveFileWithOutConfirm("111_" + DateTime.Now.ToString("HHmmssfff") + ".png");

                        var newBmp2 = ImageProcessHelper.CropBitmap(currentImage, cropImgRect);
                        //newBmp2.SaveFileWithOutConfirm("112_" + DateTime.Now.ToString("HHmmssfff") + ".png");

                        result = CombineImages(newBmp1, newBmp2, false);
                        //result.SaveFileWithOutConfirm("113_" + DateTime.Now.ToString("HHmmssfff") + ".png");

                        selectedRectangle = new Rectangle(notSameRect.X + selectedRectangle.X, notSameRect.Y + selectedRectangle.Y
                            , Math.Min(selectedRectangle.Width - notSameRect.X, Result.Width)
                            , Math.Min(selectedRectangle.Height - notSameRect.Y, Result.Height));
                    }
                }
            }

            return result;
        }

        private Rectangle GuessEdges(Bitmap img1, Bitmap img2)
        {
            var rect = new Rectangle(0, 0, img1.Width, img1.Height);

            using (var bmp1 = new UnsafeBitmap(img1, true, ImageLockMode.ReadOnly))
            using (var bmp2 = new UnsafeBitmap(img2, true, ImageLockMode.ReadOnly))
            {
                var valueFound = false;

                // Left edge
                for (var x = rect.X; !valueFound && x < rect.Width; x++)
                    for (var y = rect.Y; y < rect.Height; y++)
                        if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        {
                            valueFound = true;
                            rect.X = x;
                            break;
                        }

                valueFound = false;

                // Top edge
                for (var y = rect.Y; !valueFound && y < rect.Height; y++)
                    for (var x = rect.X; x < rect.Width; x++)
                        if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        {
                            valueFound = true;
                            rect.Y = y;
                            break;
                        }

                valueFound = false;

                // Right edge
                for (var x = rect.Width - 1; !valueFound && x >= rect.X; x--)
                    for (var y = rect.Y; y < rect.Height; y++)
                        if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        {
                            valueFound = true;
                            rect.Width = x + 1;
                            break;
                        }

                valueFound = false;

                // Bottom edge
                for (var y = rect.Height - 1; !valueFound && y >= rect.X; y--)
                    for (var x = rect.X; x < rect.Width; x++)
                        if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        {
                            valueFound = true;
                            rect.Height = y + 1;
                            break;
                        }
            }

            return rect;
        }

    }
}
