﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>XamlBuildTask</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Build.Tasks.Xaml.CompilationPass2Task">
      <summary>An MS Build task that verifies that all the types referenced in the original input XAML files are resolved.</summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.CompilationPass2Task.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Xaml.CompilationPass2Task" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CompilationPass2Task.ApplicationMarkup">
      <summary>A list of XAML files to process, populated by MS Build.</summary>
      <returns>A list of the XAML files to process.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CompilationPass2Task.AssemblyName">
      <summary>Gets or sets the name of the assembly to be generated.</summary>
      <returns>The name of the assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CompilationPass2Task.BuildTaskPath">
      <summary>Gets or sets the path to the assembly for this build task. </summary>
      <returns>The assembly path.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.CompilationPass2Task.Execute">
      <summary>Called by MS Build to execute the task.</summary>
      <returns>true if the task executes successfully; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CompilationPass2Task.LocalAssemblyReference">
      <summary>Gets or sets the path to the generated temporary assembly.</summary>
      <returns>The path to the generated temporary assembly.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CompilationPass2Task.References">
      <summary>Gets or sets a list of assemblies to reference during the compilation process. </summary>
      <returns>A list of the assemblies to reference during the compilation process.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.CompilationPass2Task.RootNamespace">
      <summary>Gets or sets the root namespace for the project.</summary>
      <returns>The root namespace.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Xaml.GenerateTemporaryAssemblyTask">
      <summary>An MS Build task that generates a temporary assembly by compiling the source files generated by the <see cref="T:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask" />. </summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.GenerateTemporaryAssemblyTask.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Xaml.GenerateTemporaryAssemblyTask" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.GenerateTemporaryAssemblyTask.ApplicationMarkupTypeName">
      <summary>Gets or sets the build action for the  application markup files which are excluded from the project used to generate the temporary assembly.</summary>
      <returns>The application markup type name.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.GenerateTemporaryAssemblyTask.AssemblyName">
      <summary>Gets or sets the generated assembly’s name.</summary>
      <returns>The assembly name.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.GenerateTemporaryAssemblyTask.CompileTargetName">
      <summary>Gets or sets the compilation target name to build in the generated project file used to build the generated temporary assembly.</summary>
      <returns>The compilation target name.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.GenerateTemporaryAssemblyTask.CurrentProject">
      <summary>Gets or sets the current project file name.</summary>
      <returns>The current project.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.GenerateTemporaryAssemblyTask.Execute">
      <summary>Called by MS Build to execute the task.</summary>
      <returns>true if the code is generated successfully; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.GenerateTemporaryAssemblyTask.OutputPath">
      <summary>Gets or sets the directory to place the generated files in.</summary>
      <returns>The directory that stores the generated files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.GenerateTemporaryAssemblyTask.ReferencePaths">
      <summary>Gets or sets the paths to any referenced assemblies specified in the project.</summary>
      <returns>A list of <see cref="T:Microsoft.Build.Framework.ITaskItem" /> that represents the paths to referenced assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.GenerateTemporaryAssemblyTask.SourceCodeFiles">
      <summary>Gets or sets a list of source code files to compile into a temporary assembly.</summary>
      <returns>A list of <see cref="T:Microsoft.Build.Framework.ITaskItem" /> that represents the source code files.</returns>
    </member>
    <member name="T:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask">
      <summary>This task accesses XAML files that define types (with x:Class) and generates the corresponding source code that can be compiled into an assembly. </summary>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask" /> class.</summary>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.ApplicationMarkup">
      <summary>A list of XAML files to process, populated by MS Build.</summary>
      <returns>A list of <see cref="T:Microsoft.Build.Framework.ITaskItem" /> that represents the XAML files to process.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.AssemblyName">
      <summary>Gets or sets the name of the assembly being compiled.</summary>
      <returns>The name of the assembly being compiled.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.BuildTaskPath">
      <summary>Gets or sets the path of the assembly that contains this build task. </summary>
      <returns>The build task path.</returns>
    </member>
    <member name="M:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.Execute">
      <summary>Called by MS Build to execute the code generation task.</summary>
      <returns>true if the code is generated successfully; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.GeneratedCodeFiles">
      <summary>Gets or sets a list of generated code files, one for each input file.</summary>
      <returns>A list of <see cref="T:Microsoft.Build.Framework.ITaskItem" /> that represents the source XAML files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.GeneratedResources">
      <summary>Gets or sets a list of XAML files that contain the markup for instantiating the newly generated types, one for each input file.</summary>
      <returns>A list of <see cref="T:Microsoft.Build.Framework.ITaskItem" /> that represents the generated XAML files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.GeneratedSourceExtension">
      <summary>Gets or sets the file extension to add to the generated source files.</summary>
      <returns>The file extension.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.IsInProcessXamlMarkupCompile">
      <summary>Gets or sets whether the compilation of XAML markup is in process.</summary>
      <returns>True if the compilation of XAML markup is in process; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.KnownReferencePaths">
      <summary>Gets or sets the known reference paths.</summary>
      <returns>The known reference paths. </returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.Language">
      <summary>Gets or sets the language to generate source code in.</summary>
      <returns>The language to generate source code in.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.OutputPath">
      <summary>Gets or sets the directory to place the generated files.</summary>
      <returns>The directory that stores the generated files.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.References">
      <summary>Gets or sets a list of assemblies to reference.</summary>
      <returns>A list of <see cref="T:Microsoft.Build.Framework.ITaskItem" /> that represents referenced assemblies.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.RequiresCompilationPass2">
      <summary>Gets or sets a value that indicates whether this project requires executing the <see cref="T:Microsoft.Build.Tasks.Xaml.CompilationPass2Task" /> task.</summary>
      <returns>true if there are unresolved types; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.RootNamespace">
      <summary>Gets or sets the root namespace for the project.</summary>
      <returns>The root namespace.</returns>
    </member>
    <member name="P:Microsoft.Build.Tasks.Xaml.PartialClassGenerationTask.SourceCodeFiles">
      <summary>Gets or sets a list of source code files in the project.</summary>
      <returns>A list of <see cref="T:Microsoft.Build.Framework.ITaskItem" /> that represents the source code files in the project.</returns>
    </member>
  </members>
</doc>