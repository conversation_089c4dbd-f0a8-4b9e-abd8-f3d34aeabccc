﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>OcrMain</RootNamespace>
    <AssemblyName>OcrMain</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System.Xml" />
    <Reference Include="Windows.Foundation.FoundationContract">
      <HintPath>C:\Program Files (x86)\Windows Kits\10\References\10.0.19041.0\Windows.Foundation.FoundationContract\*******\Windows.Foundation.FoundationContract.winmd</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Windows.Foundation.UniversalApiContract">
      <HintPath>C:\Program Files (x86)\Windows Kits\10\References\10.0.19041.0\Windows.Foundation.UniversalApiContract\10.0.0.0\Windows.Foundation.UniversalApiContract.winmd</HintPath>
      <Private>False</Private>
    </Reference>
    <PackageReference Include="clipper_library" Version="6.2.1" />
    <PackageReference Include="Emgu.CV" Version="4.6.0.5131" />
    <PackageReference Include="Microsoft.ML.OnnxRuntime.Managed" Version="1.13.1" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="UwpDesktop" Version="10.0.14393.3" />
  </ItemGroup>
  <ItemGroup>
    <Reference Update="System.Runtime.WindowsRuntime, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Update="System.Runtime.WindowsRuntime.UI.Xaml, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Angle.cs" />
    <Compile Include="AngleNet.cs" />
    <Compile Include="CommonStyle.cs" />
    <Compile Include="CrnnNet.cs" />
    <Compile Include="DbNet.cs" />
    <Compile Include="HttpProcessor.cs" />
    <Compile Include="HttpServer.cs" />
    <Compile Include="IniHelper.cs" />
    <Compile Include="LocalOcrHelper.cs" />
    <Compile Include="MyHttpServer.cs" />
    <Compile Include="Ocr.cs" />
    <Compile Include="OcrResult.cs" />
    <Compile Include="OcrUtils.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ScaleParam.cs" />
    <Compile Include="StringExtension.cs" />
    <Compile Include="TextBlock.cs" />
    <Compile Include="TextBox.cs" />
    <Compile Include="TextCellInfo.cs" />
    <Compile Include="TextLine.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>