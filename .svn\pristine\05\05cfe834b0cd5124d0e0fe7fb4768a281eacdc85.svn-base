using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace OCRTools
{
    /// <summary>
    /// 支持文字区域交互的图片查看器基类
    /// 提取 PanelPictureView 和 TextSelectableImageViewer 的共同功能
    /// </summary>
    public abstract class TextInteractiveImageViewer : ImageBox
    {
        #region 共同数据结构
        
        /// <summary>
        /// 文字区域数据
        /// </summary>
        protected List<TextCellInfo> textCells = new List<TextCellInfo>();
        
        /// <summary>
        /// 是否已经绑定过图片文字
        /// </summary>
        public bool IsBindImageMode { get; set; }
        
        /// <summary>
        /// 是否显示提示
        /// </summary>
        public bool IsShowTip { get; set; }
        
        #endregion

        #region 共同事件定义
        
        /// <summary>
        /// 文本块选择状态变化事件
        /// </summary>
        public event EventHandler<TextCellEventArgs> TextCellStateChanged;

        /// <summary>
        /// 事件参数类
        /// </summary>
        public class TextCellEventArgs : EventArgs
        {
            public TextCellInfo Cell { get; private set; }
            public TextCellSelectionType SelectionType { get; private set; }

            public TextCellEventArgs(TextCellInfo cell, TextCellSelectionType type)
            {
                Cell = cell;
                SelectionType = type;
            }
        }

        /// <summary>
        /// 选择类型枚举
        /// </summary>
        public enum TextCellSelectionType
        {
            None,       // 无选择
            Hover,      // 悬停
            Click       // 点击
        }
        
        #endregion

        #region 共同工具方法
        
        /// <summary>
        /// 获取指定控件坐标点对应的文字区域
        /// </summary>
        /// <param name="controlPoint">控件坐标点</param>
        /// <returns>对应的文字区域，如果没有则返回null</returns>
        protected TextCellInfo GetCellAtControlPoint(Point controlPoint)
        {
            // 转换为图片坐标
            var imagePoint = GetImagePointFromControl(controlPoint);
            
            // 查找包含该点的文字区域
            return textCells.Where(item => 
                item?.location != null && 
                item.location.Rectangle.Contains(imagePoint)
            ).FirstOrDefault();
        }
        
        /// <summary>
        /// 坐标转换：控件坐标 → 图片坐标
        /// </summary>
        /// <param name="controlPoint">控件坐标</param>
        /// <returns>对应的图片坐标</returns>
        protected Point GetImagePointFromControl(Point controlPoint)
        {
            var scrollOffset = AutoScrollPosition;
            return new Point(
                (int)((controlPoint.X - scrollOffset.X) / ZoomFactor),
                (int)((controlPoint.Y - scrollOffset.Y) / ZoomFactor)
            );
        }
        
        /// <summary>
        /// 坐标转换：图片坐标 → 控件坐标
        /// </summary>
        /// <param name="imagePoint">图片坐标</param>
        /// <returns>对应的控件坐标</returns>
        protected Point GetControlPointFromImage(Point imagePoint)
        {
            var scrollOffset = AutoScrollPosition;
            return new Point(
                (int)(imagePoint.X * ZoomFactor + scrollOffset.X),
                (int)(imagePoint.Y * ZoomFactor + scrollOffset.Y)
            );
        }
        
        /// <summary>
        /// 获取文字区域在控件中的显示矩形
        /// </summary>
        /// <param name="cell">文字区域</param>
        /// <returns>在控件中的显示矩形</returns>
        protected Rectangle GetCellDisplayRect(TextCellInfo cell)
        {
            if (cell?.location == null) return Rectangle.Empty;
            
            var cellRect = cell.location.Rectangle.Zoom(ZoomFactor);
            cellRect.Location = cellRect.Location.Add(AutoScrollPosition);
            return cellRect;
        }
        
        /// <summary>
        /// 复制文字到剪贴板
        /// </summary>
        /// <param name="text">要复制的文字</param>
        protected void CopyTextToClipboard(string text)
        {
            if (string.IsNullOrEmpty(text)) return;
            
            try
            {
                ClipboardService.SetText(text);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"复制到剪贴板失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 触发文字选择状态变化事件
        /// </summary>
        /// <param name="cell">选中的文字区域</param>
        /// <param name="type">选择类型</param>
        protected virtual void OnTextCellStateChanged(TextCellInfo cell, TextCellSelectionType type)
        {
            TextCellStateChanged?.Invoke(this, new TextCellEventArgs(cell, type));
        }
        
        #endregion

        #region 抽象方法 - 子类必须实现
        
        /// <summary>
        /// 绑定图片和文字区域数据
        /// </summary>
        /// <param name="image">图片</param>
        /// <param name="regions">文字区域列表</param>
        public abstract void BindImageAndTextRegions(Image image, List<TextCellInfo> regions);
        
        /// <summary>
        /// 处理鼠标按下事件
        /// </summary>
        /// <param name="e">鼠标事件参数</param>
        protected abstract void HandleMouseDown(MouseEventArgs e);
        
        /// <summary>
        /// 处理鼠标移动事件
        /// </summary>
        /// <param name="e">鼠标事件参数</param>
        protected abstract void HandleMouseMove(MouseEventArgs e);
        
        /// <summary>
        /// 处理鼠标释放事件
        /// </summary>
        /// <param name="e">鼠标事件参数</param>
        protected abstract void HandleMouseUp(MouseEventArgs e);
        
        /// <summary>
        /// 处理自定义绘制
        /// </summary>
        /// <param name="e">绘制事件参数</param>
        protected abstract void HandleCustomPaint(PaintEventArgs e);
        
        #endregion

        #region 事件分发到子类
        
        protected override void OnMouseDown(MouseEventArgs e)
        {
            HandleMouseDown(e);
            base.OnMouseDown(e);
        }
        
        protected override void OnMouseMove(MouseEventArgs e)
        {
            HandleMouseMove(e);
            base.OnMouseMove(e);
        }
        
        protected override void OnMouseUp(MouseEventArgs e)
        {
            HandleMouseUp(e);
            base.OnMouseUp(e);
        }
        
        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            HandleCustomPaint(e);
        }
        
        #endregion
    }
}
