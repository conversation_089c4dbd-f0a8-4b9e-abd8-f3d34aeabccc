﻿using OCRTools.Units;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class SetSizeForm : Form
    {
        private readonly Settings settings;
        private UnitConverter converter;

        public SetSizeForm(Size rulerSize, Settings settings)
        {
            InitializeComponent();
            RulerWidth = rulerSize.Width;
            RulerHeight = rulerSize.Height;
            this.settings = settings;
            setConverter(settings.MeasuringUnit);
        }

        public float RulerWidth { get; private set; }
        public float RulerHeight { get; private set; }

        private void SetSizeForm_Load(object sender, EventArgs e)
        {
            foreach (Enum item in Enum.GetValues(typeof(MeasuringUnit))) comUnits.Items.Add(item.ToString());
            comUnits.SelectedIndex = (int) settings.MeasuringUnit;
            updateText();
        }

        private void setConverter(MeasuringUnit unit)
        {
            var screenSize = Screen.FromControl(this).Bounds.Size;
            converter = new UnitConverter(unit, screenSize, settings.MonitorDpi);
        }

        private void comUnits_SelectedIndexChanged(object sender, EventArgs e)
        {
            var unit = (MeasuringUnit) comUnits.SelectedIndex;
            setConverter(unit);
            updateText();
        }

        /// <summary>
        ///     Updates the ruler length and unit symbol to the currently chosen unit.
        /// </summary>
        private void updateText()
        {
            numWidth.Value = (decimal) converter.ConvertFromPixel(RulerWidth, false);
            numHeight.Value = (decimal) converter.ConvertFromPixel(RulerHeight, true);
            lblUnit1.Text = converter.UnitString;
            lblUnit2.Text = converter.UnitString;
        }

        private void numWidth_ValueChanged(object sender, EventArgs e)
        {
            RulerWidth = converter.ConvertToPixel((float) numWidth.Value, false);
        }

        private void numHeight_ValueChanged(object sender, EventArgs e)
        {
            RulerHeight = converter.ConvertToPixel((float) numHeight.Value, true);
        }

        private void butSubmit_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
            Close();
        }
    }
}