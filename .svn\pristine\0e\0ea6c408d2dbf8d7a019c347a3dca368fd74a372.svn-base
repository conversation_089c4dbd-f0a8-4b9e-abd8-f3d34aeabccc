﻿using System;
using System.Net;
using System.Threading;

namespace OcrMain
{
    class Program
    {
        #region 配置文件相关

        public static string ConfigPath { get; set; } = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + "\\OcrService\\";

        public static string ConfigName { get; set; } = "config.ini";

        public static string IniFileName => string.Format("{0}\\{1}", ConfigPath, ConfigName);

        #endregion

        static void Main(string[] args)
        {
            using (new Mutex(true, "OcrServer", out var flag))
            {
                if (flag)
                {
                    var port = IniHelper.GetValue("配置", "端口", "8080");
                    int.TryParse(port, out int nPort);
                    if (nPort < 80 || nPort > 9999)
                    {
                        nPort = 8080;
                    }

                    var thread = IniHelper.GetValue("配置", "线程", "5");
                    int.TryParse(thread, out int nThread);
                    if (nThread < 1 || nThread > 100)
                    {
                        nThread = 5;
                    }

                    InitThread();

                    HttpServer httpServer = new MyHttpServer(nPort);
                    MyHttpServer.NMaxThread = nThread;
                    httpServer.Listen();
                }
            }
        }

        static void InitThread()
        {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls | SecurityProtocolType.Ssl3;
                ServicePointManager.ServerCertificateValidationCallback = (obj, certificate, chain, sslPolicyErrors) => true;
                ServicePointManager.DefaultConnectionLimit = int.MaxValue;
                ServicePointManager.MaxServicePoints = int.MaxValue;
                //ServicePointManager.MaxServicePoints = int.MaxValue;
                ServicePointManager.MaxServicePointIdleTime = int.MaxValue;
                ServicePointManager.UseNagleAlgorithm = false;
                //ServicePointManager.CheckCertificateRevocationList = false;
                ServicePointManager.Expect100Continue = false;
                //ServicePointManager.SetTcpKeepAlive(true, 1000, 200);
                GlobalProxySelection.Select = GlobalProxySelection.GetEmptyWebProxy();
                WebRequest.DefaultWebProxy = null;

                ThreadPool.GetAvailableThreads(out var workerThreads, out var completionPortThreads);
                Console.WriteLine("默认配置：工作线程：{0}，完成端口：{1}", workerThreads, completionPortThreads);
                ThreadPool.SetMinThreads(200, 100);
                if (Environment.ProcessorCount * 200 > workerThreads)
                {
                    if (ThreadPool.SetMaxThreads(Environment.ProcessorCount * 200, Environment.ProcessorCount * 100))
                    {
                        Console.WriteLine();
                    }
                    ThreadPool.GetAvailableThreads(out workerThreads, out completionPortThreads);
                    Console.WriteLine("修改配置：工作线程：{0}，完成端口：{1}", workerThreads, completionPortThreads);
                }
                else
                {
                    Console.WriteLine("默认配置大于要修改的配置，无需修改");
                }
            }
            catch
            {
                // ignored
            }
        }

    }
}
