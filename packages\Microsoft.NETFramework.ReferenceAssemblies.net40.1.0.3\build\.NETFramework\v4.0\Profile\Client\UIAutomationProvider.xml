﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>UIAutomationProvider</name>
  </assembly>
  <members>
    <member name="T:System.Windows.Automation.Provider.AutomationInteropProvider">
      <summary>Provides support for communication between UI Automation providers and UI Automation client applications.</summary>
    </member>
    <member name="F:System.Windows.Automation.Provider.AutomationInteropProvider.AppendRuntimeId">
      <summary>Contains a value that, when returned as the first element of the array passed by the UI Automation provider to <see cref="M:System.Windows.Automation.Provider.IRawElementProviderFragment.GetRuntimeId" />, indicates that the ID is partial and should be appended to the ID provided by the base provider.</summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.AutomationInteropProvider.ClientsAreListening">
      <summary>Gets a value that specifies whether any UI Automation client application is subscribed to UI Automation events.</summary>
      <returns>true if a client is subscribed to events; otherwise false.</returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.AutomationInteropProvider.HostProviderFromHandle(System.IntPtr)">
      <summary>Returns a UI Automation provider that represents the specified window. </summary>
      <returns>The raw element provider for the specified window.</returns>
      <param name="hwnd">The handle of the window.</param>
    </member>
    <member name="F:System.Windows.Automation.Provider.AutomationInteropProvider.InvalidateLimit">
      <summary>Specifies the maximum number of events to send before batching. </summary>
    </member>
    <member name="F:System.Windows.Automation.Provider.AutomationInteropProvider.ItemsInvalidateLimit"></member>
    <member name="M:System.Windows.Automation.Provider.AutomationInteropProvider.RaiseAutomationEvent(System.Windows.Automation.AutomationEvent,System.Windows.Automation.Provider.IRawElementProviderSimple,System.Windows.Automation.AutomationEventArgs)">
      <summary>Raises a pattern event or a custom event.</summary>
      <param name="eventId">The event identifier.</param>
      <param name="provider">The element associated with the event.</param>
      <param name="e">Information about the event.</param>
    </member>
    <member name="M:System.Windows.Automation.Provider.AutomationInteropProvider.RaiseAutomationPropertyChangedEvent(System.Windows.Automation.Provider.IRawElementProviderSimple,System.Windows.Automation.AutomationPropertyChangedEventArgs)">
      <summary>Raises an event when a UI Automation property has changed. </summary>
      <param name="element">The element associated with the event.</param>
      <param name="e">Information about the event.</param>
    </member>
    <member name="M:System.Windows.Automation.Provider.AutomationInteropProvider.RaiseStructureChangedEvent(System.Windows.Automation.Provider.IRawElementProviderSimple,System.Windows.Automation.StructureChangedEventArgs)">
      <summary>Raises an event when the UI Automation tree has changed.</summary>
      <param name="provider">The element associated with the event.</param>
      <param name="e">Information about the event.</param>
    </member>
    <member name="M:System.Windows.Automation.Provider.AutomationInteropProvider.ReturnRawElementProvider(System.IntPtr,System.IntPtr,System.IntPtr,System.Windows.Automation.Provider.IRawElementProviderSimple)">
      <summary>Returns the UI automation provider for an element in response to a WM_GETOBJECT message. </summary>
      <returns>A pointer to data that should be assigned to <see cref="P:System.Windows.Forms.Message.Result" /> before passing the message to the base window procedure.</returns>
      <param name="hwnd">Handle of the element's window.</param>
      <param name="wParam">The wParam member of the WM_GETOBJECT message.</param>
      <param name="lParam">The lParam member of the WM_GETOBJECT message.</param>
      <param name="el">The UI Automation provider for the element.</param>
    </member>
    <member name="F:System.Windows.Automation.Provider.AutomationInteropProvider.RootObjectId">
      <summary>Contains a value used in the lParam member of a WM_GETOBJECT message, indicating that the server should return a reference to the root raw element provider. </summary>
    </member>
    <member name="T:System.Windows.Automation.Provider.IDockProvider">
      <summary>Exposes methods and properties to support UI Automation client access to controls that expose their dock properties within a docking container. </summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.IDockProvider.DockPosition">
      <summary>Gets the current <see cref="T:System.Windows.Automation.DockPosition" /> of the control within a docking container.</summary>
      <returns>The <see cref="T:System.Windows.Automation.DockPosition" /> of the control, relative to the boundaries of the docking container and other elements within the container.</returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.IDockProvider.SetDockPosition(System.Windows.Automation.DockPosition)">
      <summary>Docks the control within a docking container.</summary>
      <param name="dockPosition">The dock position, relative to the boundaries of the docking container and other elements within the container.</param>
    </member>
    <member name="T:System.Windows.Automation.Provider.IExpandCollapseProvider">
      <summary>Exposes methods and properties to support UI Automation client access to controls that visually expand to display content and collapse to hide content.</summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.IExpandCollapseProvider.Collapse">
      <summary>Hides all nodes, controls, or content that are descendants of the control.</summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.IExpandCollapseProvider.Expand">
      <summary>Displays all child nodes, controls, or content of the control.</summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.IExpandCollapseProvider.ExpandCollapseState">
      <summary>Gets the state, expanded or collapsed, of the control.</summary>
      <returns>The state, expanded or collapsed, of the control.</returns>
    </member>
    <member name="T:System.Windows.Automation.Provider.IGridItemProvider">
      <summary>Exposes methods and properties to support UI Automation client access to individual child controls of containers that implement <see cref="T:System.Windows.Automation.Provider.IGridProvider" />. </summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.IGridItemProvider.Column">
      <summary>Gets the ordinal number of the column that contains the cell or item.</summary>
      <returns>A zero-based ordinal number that identifies the column containing the cell or item.</returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IGridItemProvider.ColumnSpan">
      <summary>Gets the number of columns spanned by a cell or item.</summary>
      <returns>The number of columns spanned. </returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IGridItemProvider.ContainingGrid">
      <summary>Gets a UI Automation provider that implements <see cref="T:System.Windows.Automation.Provider.IGridProvider" /> and represents the container of the cell or item.</summary>
      <returns>A UI Automation provider that implements the <see cref="T:System.Windows.Automation.GridPattern" /> and represents the cell or item container. </returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IGridItemProvider.Row">
      <summary>Gets the ordinal number of the row that contains the cell or item.</summary>
      <returns>A zero-based ordinal number that identifies the row containing the cell or item. </returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IGridItemProvider.RowSpan">
      <summary>Gets the number of rows spanned by a cell or item.</summary>
      <returns>The number of rows spanned. </returns>
    </member>
    <member name="T:System.Windows.Automation.Provider.IGridProvider">
      <summary>Exposes methods and properties to support UI Automation client access to controls that act as containers for a collection of child elements. The children of this element must implement <see cref="T:System.Windows.Automation.Provider.IGridItemProvider" /> and be organized in a two-dimensional logical coordinate system that can be traversed (that is, a UI Automation client can move to adjacent controls) by using the keyboard.</summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.IGridProvider.ColumnCount">
      <summary>Gets the total number of columns in a grid.</summary>
      <returns>The total number of columns in a grid.</returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.IGridProvider.GetItem(System.Int32,System.Int32)">
      <summary>Retrieves the UI Automation provider for the specified cell.</summary>
      <returns>The UI Automation provider for the specified cell.</returns>
      <param name="row">The ordinal number of the row of interest.</param>
      <param name="column">The ordinal number of the column of interest.</param>
    </member>
    <member name="P:System.Windows.Automation.Provider.IGridProvider.RowCount">
      <summary>Gets the total number of rows in a grid.</summary>
      <returns>The total number of rows in a grid.</returns>
    </member>
    <member name="T:System.Windows.Automation.Provider.IInvokeProvider">
      <summary>Exposes methods and properties to support UI Automation client access to controls that initiate or perform a single, unambiguous action and do not maintain state when activated. </summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.IInvokeProvider.Invoke">
      <summary>Sends a request to activate a control and initiate its single, unambiguous action.</summary>
      <exception cref="T:System.Windows.Automation.ElementNotEnabledException">If the control is not enabled.</exception>
    </member>
    <member name="T:System.Windows.Automation.Provider.IItemContainerProvider">
      <summary>Exposes a method to enable applications to find an element in a container, such as a virtualized list.</summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.IItemContainerProvider.FindItemByProperty(System.Windows.Automation.Provider.IRawElementProviderSimple,System.Int32,System.Object)">
      <summary>Retrieves an element by the specified property value.</summary>
      <returns>The first item that matches the search criterion; otherwise, null if no items match.</returns>
      <param name="startAfter">The item in the container after which to begin the search.</param>
      <param name="propertyId">The property that contains the value to retrieve.</param>
      <param name="value">The value to retrieve.</param>
    </member>
    <member name="T:System.Windows.Automation.Provider.IMultipleViewProvider">
      <summary>Exposes methods and properties to support UI Automation client access to controls that provide, and are able to switch between, multiple representations of the same set of information or child controls.</summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.IMultipleViewProvider.CurrentView">
      <summary>Gets the current control-specific view.</summary>
      <returns>The value for the current view of the UI Automation element. </returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.IMultipleViewProvider.GetSupportedViews">
      <summary>Retrieves a collection of control-specific view identifiers.</summary>
      <returns>A collection of values that identifies the views available for a UI Automation element. </returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.IMultipleViewProvider.GetViewName(System.Int32)">
      <summary>Retrieves the name of a control-specific view.</summary>
      <returns>A localized name for the view.</returns>
      <param name="viewId">The view identifier.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="viewId" /> is not a member of the supported views collection.</exception>
    </member>
    <member name="M:System.Windows.Automation.Provider.IMultipleViewProvider.SetCurrentView(System.Int32)">
      <summary>Sets the current control-specific view. </summary>
      <param name="viewId">A view identifier.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="viewId" /> is not a member of the supported views collection.</exception>
    </member>
    <member name="T:System.Windows.Automation.Provider.IRangeValueProvider">
      <summary>Exposes methods and properties to support UI Automation client access to controls that can be set to a value within a range.</summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.IRangeValueProvider.IsReadOnly">
      <summary>Gets a value that specifies whether the value of a control is read-only. </summary>
      <returns>true if the value is read-only; false if it can be modified. </returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IRangeValueProvider.LargeChange">
      <summary>Gets the value that is added to or subtracted from the <see cref="P:System.Windows.Automation.Provider.IRangeValueProvider.Value" /> property when a large change is made, such as with the PAGE DOWN key.</summary>
      <returns>The large-change value supported by the control or null (Nothing in Microsoft Visual Basic .NET) if the control does not support <see cref="P:System.Windows.Automation.Provider.IRangeValueProvider.LargeChange" />. </returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IRangeValueProvider.Maximum">
      <summary>Gets the maximum range value supported by the control.</summary>
      <returns>The maximum value supported by the control or null (Nothing in Microsoft Visual Basic .NET) if the control does not support <see cref="P:System.Windows.Automation.Provider.IRangeValueProvider.Maximum" />. </returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IRangeValueProvider.Minimum">
      <summary>Gets the minimum range value supported by the control.</summary>
      <returns>The minimum value supported by the control or null (Nothing in Microsoft Visual Basic .NET) if the control does not support <see cref="P:System.Windows.Automation.Provider.IRangeValueProvider.Minimum" />. </returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.IRangeValueProvider.SetValue(System.Double)">
      <summary>Sets the value of the control.</summary>
      <param name="value">The value to set.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">When <paramref name="value" /> is less than the minimum or greater than the maximum value of the control.</exception>
    </member>
    <member name="P:System.Windows.Automation.Provider.IRangeValueProvider.SmallChange">
      <summary>Gets the value that is added to or subtracted from the <see cref="P:System.Windows.Automation.Provider.IRangeValueProvider.Value" /> property when a small change is made, such as with an arrow key.</summary>
      <returns>The small-change value or null (Nothing in Microsoft Visual Basic .NET) if the control does not support <see cref="P:System.Windows.Automation.Provider.IRangeValueProvider.SmallChange" />. </returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IRangeValueProvider.Value">
      <summary>Gets the value of the control.</summary>
      <returns>The value of the control or null (Nothing in Microsoft Visual Basic .NET) if the control does not support <see cref="P:System.Windows.Automation.Provider.IRangeValueProvider.Value" />.</returns>
    </member>
    <member name="T:System.Windows.Automation.Provider.IRawElementProviderAdviseEvents">
      <summary>Exposes methods that are called to notify the root element of a fragment when a UI Automation client application begins or ends listening for events. </summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.IRawElementProviderAdviseEvents.AdviseEventAdded(System.Int32,System.Int32[])">
      <summary>Notifies the UI Automation provider when a UI Automation client begins listening for a specific event, including a property-changed event. </summary>
      <param name="eventId">The identifier of the event being added.</param>
      <param name="properties">The identifiers of the properties being added, or null if the event listener being added is not listening for property events.</param>
    </member>
    <member name="M:System.Windows.Automation.Provider.IRawElementProviderAdviseEvents.AdviseEventRemoved(System.Int32,System.Int32[])">
      <summary>Notifies the UI Automation provider when a UI Automation client stops listening for a specific event, including a property-changed event. </summary>
      <param name="eventId">The identifier of the event being removed</param>
      <param name="properties">The identifiers of the properties being removed, or null if the event listener being removed is not listening for property events.</param>
    </member>
    <member name="T:System.Windows.Automation.Provider.IRawElementProviderFragment">
      <summary>Exposes methods and properties on user interface (UI) elements that are part of a structure more than one level deep, such as a list box or a list item. Implemented by UI Automation providers.</summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.IRawElementProviderFragment.BoundingRectangle">
      <summary>Gets the bounding rectangle of this element.</summary>
      <returns>The bounding rectangle, in screen coordinates.</returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IRawElementProviderFragment.FragmentRoot">
      <summary>Retrieves the root node of the fragment.</summary>
      <returns>The root node. </returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.IRawElementProviderFragment.GetEmbeddedFragmentRoots">
      <summary>Retrieves an array of fragment roots that are embedded in the UI Automation element tree rooted at the current element.</summary>
      <returns>An array of root fragments, or null.</returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.IRawElementProviderFragment.GetRuntimeId">
      <summary>Retrieves the runtime identifier of an element. </summary>
      <returns>The unique run-time identifier of the element.</returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.IRawElementProviderFragment.Navigate(System.Windows.Automation.Provider.NavigateDirection)">
      <summary>Retrieves the UI Automation element in a specified direction within the tree.</summary>
      <returns>The element in the specified direction, or null if there is no element in that direction</returns>
      <param name="direction">The direction in which to navigate.</param>
    </member>
    <member name="M:System.Windows.Automation.Provider.IRawElementProviderFragment.SetFocus">
      <summary>Sets the focus to this element. </summary>
    </member>
    <member name="T:System.Windows.Automation.Provider.IRawElementProviderFragmentRoot">
      <summary>Exposes methods and properties on the root element in a fragment. </summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.IRawElementProviderFragmentRoot.ElementProviderFromPoint(System.Double,System.Double)">
      <summary>Retrieves the element in this fragment that is at the specified point. </summary>
      <returns>The provider for the child element at the specified point, if one exists, or the root provider if the point is on this element but not on any child element. Otherwise returns null.</returns>
      <param name="x">The X coordinate,.</param>
      <param name="y">The Y coordinate.</param>
    </member>
    <member name="M:System.Windows.Automation.Provider.IRawElementProviderFragmentRoot.GetFocus">
      <summary>Retrieves the element in this fragment that has the input focus.</summary>
      <returns>The provider for the element in this fragment that has the input focus, if any; otherwise, a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="T:System.Windows.Automation.Provider.IRawElementProviderHwndOverride">
      <summary>Exposes a method that enables repositioning of window-based elements within the  UI Automation tree of the fragment. </summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.IRawElementProviderHwndOverride.GetOverrideProviderForHwnd(System.IntPtr)">
      <summary>Retrieves a UI automation provider for the specified element. </summary>
      <returns>The provider for the specified element, or a null reference (Nothing in Microsoft Visual Basic .NET) if the element's default provider is not being overridden.</returns>
      <param name="hwnd">The window handle of the element.</param>
    </member>
    <member name="T:System.Windows.Automation.Provider.IRawElementProviderSimple">
      <summary>Provides methods and properties that expose basic information about a UI element.</summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.IRawElementProviderSimple.GetPatternProvider(System.Int32)">
      <summary>Retrieves an object that provides support for a control pattern on a UI Automation element.</summary>
      <returns>Object that implements the pattern interface, or null if the pattern is not supported.</returns>
      <param name="patternId">Identifier of the pattern.</param>
    </member>
    <member name="M:System.Windows.Automation.Provider.IRawElementProviderSimple.GetPropertyValue(System.Int32)">
      <summary>Retrieves the value of a property supported by the UI Automation provider.</summary>
      <returns>The property value, or a null if the property is not supported by this provider, or <see cref="F:System.Windows.Automation.AutomationElementIdentifiers.NotSupported" /> if it is not supported at all.</returns>
      <param name="propertyId">The property identifier.</param>
    </member>
    <member name="P:System.Windows.Automation.Provider.IRawElementProviderSimple.HostRawElementProvider">
      <summary>Gets a base provider for this element.</summary>
      <returns>The base provider, or null.</returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IRawElementProviderSimple.ProviderOptions">
      <summary>Gets a value that specifies characteristics of the UI Automation provider; for example, whether it is a client-side or server-side provider. </summary>
      <returns>Either <see cref="F:System.Windows.Automation.Provider.ProviderOptions.ClientSideProvider" /> or <see cref="F:System.Windows.Automation.Provider.ProviderOptions.ServerSideProvider" />.</returns>
    </member>
    <member name="T:System.Windows.Automation.Provider.IScrollItemProvider">
      <summary>Exposes methods and properties to support UI Automation client access to individual child controls of containers that implement <see cref="T:System.Windows.Automation.Provider.IScrollProvider" />.</summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.IScrollItemProvider.ScrollIntoView">
      <summary>Scrolls the content area of a container object in order to display the control within the visible region (viewport) of the container.</summary>
      <exception cref="T:System.InvalidOperationException">The item cannot be scrolled into view.</exception>
    </member>
    <member name="T:System.Windows.Automation.Provider.IScrollProvider">
      <summary>Exposes methods and properties to support UI Automation client access to a control that acts as a scrollable container for a collection of child objects. The children of this control must implement <see cref="T:System.Windows.Automation.Provider.IScrollItemProvider" />.</summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.IScrollProvider.HorizontallyScrollable">
      <summary>Gets a value that indicates whether the control can scroll horizontally.</summary>
      <returns>true if the control can scroll horizontally; otherwise false.</returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IScrollProvider.HorizontalScrollPercent">
      <summary>Gets the current horizontal scroll position.</summary>
      <returns>The horizontal scroll position as a percentage of the total content area within the control.</returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IScrollProvider.HorizontalViewSize">
      <summary>Gets the current horizontal view size.</summary>
      <returns>The horizontal size of the viewable region as a percentage of the total content area within the control. </returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.IScrollProvider.Scroll(System.Windows.Automation.ScrollAmount,System.Windows.Automation.ScrollAmount)">
      <summary>Scrolls the visible region of the content area horizontally and vertically.</summary>
      <param name="horizontalAmount">The horizontal increment specific to the control. <see cref="F:System.Windows.Automation.ScrollPatternIdentifiers.NoScroll" /> should be passed in if the control cannot be scrolled in this direction.</param>
      <param name="verticalAmount">The vertical increment specific to the control. <see cref="F:System.Windows.Automation.ScrollPatternIdentifiers.NoScroll" /> should be passed in if the control cannot be scrolled in this direction.</param>
      <exception cref="T:System.ArgumentException">If a control supports <see cref="F:System.Windows.Automation.ScrollAmount.SmallIncrement" /> values exclusively for horizontal or vertical scrolling but a <see cref="F:System.Windows.Automation.ScrollAmount.LargeIncrement" /> value is passed in.</exception>
      <exception cref="T:System.InvalidOperationException">An attempt is made to scroll in an unsupported direction.</exception>
    </member>
    <member name="M:System.Windows.Automation.Provider.IScrollProvider.SetScrollPercent(System.Double,System.Double)">
      <summary>Sets the horizontal and vertical scroll position as a percentage of the total content area within the control.</summary>
      <param name="horizontalPercent">The horizontal position as a percentage of the content area's total range. <see cref="F:System.Windows.Automation.ScrollPatternIdentifiers.NoScroll" /> should be passed in if the control cannot be scrolled in this direction.</param>
      <param name="verticalPercent">The vertical position as a percentage of the content area's total range. <see cref="F:System.Windows.Automation.ScrollPatternIdentifiers.NoScroll" /> should be passed in if the control cannot be scrolled in this direction.</param>
      <exception cref="T:System.ArgumentException">A value that cannot be converted to a double is passed in.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">A value greater than 100 or less than 0 is passed in (except -1, which is equivalent to <see cref="F:System.Windows.Automation.ScrollPatternIdentifiers.NoScroll" />). The <see cref="P:System.Windows.Automation.Provider.IScrollProvider.HorizontalScrollPercent" /> and <see cref="P:System.Windows.Automation.Provider.IScrollProvider.VerticalScrollPercent" /> values are normalized to either 100 percent or 0 percent. </exception>
      <exception cref="T:System.InvalidOperationException">An attempt is made to scroll in an unsupported direction.</exception>
    </member>
    <member name="P:System.Windows.Automation.Provider.IScrollProvider.VerticallyScrollable">
      <summary>Gets a value that indicates whether the control can scroll vertically.</summary>
      <returns>true if the control can scroll vertically; otherwise false. </returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IScrollProvider.VerticalScrollPercent">
      <summary>Gets the current vertical scroll position.</summary>
      <returns>The vertical scroll position as a percentage of the total content area within the control. </returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IScrollProvider.VerticalViewSize">
      <summary>Gets the vertical view size.</summary>
      <returns>The vertical size of the viewable region as a percentage of the total content area within the control. </returns>
    </member>
    <member name="T:System.Windows.Automation.Provider.ISelectionItemProvider">
      <summary>Exposes methods and properties to support UI Automation client access to individual, selectable child controls of containers that implement <see cref="T:System.Windows.Automation.Provider.ISelectionProvider" />.</summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.ISelectionItemProvider.AddToSelection">
      <summary>Adds the current element to the collection of selected items.</summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.ISelectionItemProvider.IsSelected">
      <summary>Gets a value that indicates whether an item is selected. </summary>
      <returns>true if the element is selected; otherwise false.</returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.ISelectionItemProvider.RemoveFromSelection">
      <summary>Removes the current element from the collection of selected items.</summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.ISelectionItemProvider.Select">
      <summary>Deselects any selected items and then selects the current element.</summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.ISelectionItemProvider.SelectionContainer">
      <summary>Gets the UI Automation provider that implements <see cref="T:System.Windows.Automation.Provider.ISelectionProvider" /> and acts as the container for the calling object.</summary>
      <returns>The provider that supports <see cref="T:System.Windows.Automation.Provider.ISelectionProvider" />. </returns>
    </member>
    <member name="T:System.Windows.Automation.Provider.ISelectionProvider">
      <summary>Exposes methods and properties to support UI Automation client access to controls that act as containers for a collection of individual, selectable child items. The children of this control must implement <see cref="T:System.Windows.Automation.Provider.ISelectionItemProvider" />.</summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.ISelectionProvider.CanSelectMultiple">
      <summary>Gets a value that specifies whether the UI Automation provider allows more than one child element to be selected concurrently.</summary>
      <returns>true if multiple selection is allowed; otherwise false.</returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.ISelectionProvider.GetSelection">
      <summary>Retrieves a UI Automation provider for each child element that is selected.</summary>
      <returns>A collection of UI Automation providers. </returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.ISelectionProvider.IsSelectionRequired">
      <summary>Gets a value that specifies whether the UI Automation provider requires at least one child element to be selected.</summary>
      <returns>true if selection is required; otherwise false.</returns>
    </member>
    <member name="T:System.Windows.Automation.Provider.ISynchronizedInputProvider">
      <summary>Exposes methods to facilitate UI Automation client access to controls that support synchronized input.</summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.ISynchronizedInputProvider.Cancel">
      <summary>Stops listening for input.</summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.ISynchronizedInputProvider.StartListening(System.Windows.Automation.SynchronizedInputType)">
      <summary>Designates that the provider start listening for input of the specified type.</summary>
      <param name="inputType">The type of input to listen for.</param>
    </member>
    <member name="T:System.Windows.Automation.Provider.ITableItemProvider">
      <summary>Exposes methods and properties to support UI Automation client access to child controls of containers that implement <see cref="T:System.Windows.Automation.Provider.ITableProvider" />. </summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITableItemProvider.GetColumnHeaderItems">
      <summary>Retrieves a collection of UI Automation providers representing all the column headers associated with a table item or cell.</summary>
      <returns>A collection of UI Automation providers. </returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITableItemProvider.GetRowHeaderItems">
      <summary>Retrieves a collection of UI Automation providers representing all the row headers associated with a table item or cell.</summary>
      <returns>A collection of UI Automation providers. </returns>
    </member>
    <member name="T:System.Windows.Automation.Provider.ITableProvider">
      <summary>Exposes methods and properties to support UI Automation client access to controls that act as containers for a collection of child elements. The children of this element must implement <see cref="T:System.Windows.Automation.Provider.ITableItemProvider" /> and be organized in a two-dimensional logical coordinate system that can be traversed (that is, a UI Automation client can move to adjacent controls) by using the keyboard.</summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITableProvider.GetColumnHeaders">
      <summary>Gets a collection of UI Automation providers that represents all the column headers in a table.</summary>
      <returns>A collection of UI Automation providers. </returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITableProvider.GetRowHeaders">
      <summary>Retrieves a collection of UI Automation providers that represents all row headers in the table.</summary>
      <returns>A collection of UI Automation providers.</returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.ITableProvider.RowOrColumnMajor">
      <summary>Retrieves the primary direction of traversal for the table.</summary>
      <returns>The primary direction of traversal. </returns>
    </member>
    <member name="T:System.Windows.Automation.Provider.ITextProvider">
      <summary>Exposes methods and properties to support UI Automation client access to controls that contain text.</summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.ITextProvider.DocumentRange">
      <summary>Gets a text range that encloses the main text of a document. </summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextProvider.GetSelection">
      <summary>Retrieves a collection of disjoint text ranges associated with the current text selection or selections. </summary>
      <returns>A collection of disjoint text ranges.</returns>
      <exception cref="T:System.InvalidOperationException">If the UI Automation provider does not support text selection.</exception>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextProvider.GetVisibleRanges">
      <summary>Retrieves an array of disjoint text ranges from a text container where each text range begins with the first partially visible line through to the end of the last partially visible line. </summary>
      <returns>The collection of visible text ranges within the container or an empty array. A null reference (Nothing in Microsoft Visual Basic .NET) is never returned.</returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextProvider.RangeFromChild(System.Windows.Automation.Provider.IRawElementProviderSimple)">
      <summary>Retrieves a text range enclosing a child element such as an image, hyperlink, or other embedded object. </summary>
      <returns>A range that spans the child element.</returns>
      <param name="childElement">The enclosed object.</param>
      <exception cref="T:System.ArgumentException">If the child element is a null reference (Nothing in Microsoft Visual Basic .NET).</exception>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextProvider.RangeFromPoint(System.Windows.Point)">
      <summary>Returns the degenerate (empty) text range nearest to the specified screen coordinates. </summary>
      <returns>A degenerate range nearest the specified location. A null reference (Nothing in Microsoft Visual Basic .NET) is never returned.</returns>
      <param name="screenLocation">The location in screen coordinates. </param>
      <exception cref="T:System.ArgumentException">If a given point is outside the UI Automation element associated with the text pattern.</exception>
    </member>
    <member name="P:System.Windows.Automation.Provider.ITextProvider.SupportedTextSelection">
      <summary>Gets a value that specifies whether a text provider supports selection and, if so, the type of selection supported.</summary>
      <returns>One of None, Single, or Multiple from <see cref="T:System.Windows.Automation.SupportedTextSelection" />.</returns>
    </member>
    <member name="T:System.Windows.Automation.Provider.ITextRangeProvider">
      <summary>Exposes methods and properties to support UI Automation client access to a span of continuous text in a text container that implements <see cref="T:System.Windows.Automation.Provider.ITextProvider" />.</summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.AddToSelection">
      <summary>Adds to the collection of highlighted text in a text container that supports multiple, disjoint selections.</summary>
      <exception cref="T:System.InvalidOperationException">If text provider does not support multiple, disjoint selections (that is, <see cref="P:System.Windows.Automation.Provider.ITextProvider.SupportedTextSelection" /> must have a value of Multiple).</exception>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.Clone">
      <summary>Returns a new <see cref="T:System.Windows.Automation.Provider.ITextRangeProvider" /> identical to the original <see cref="T:System.Windows.Automation.Provider.ITextRangeProvider" /> and inheriting all properties of the original. </summary>
      <returns>The new text range. A null reference (Nothing in Microsoft Visual Basic .NET) is never returned.</returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.Compare(System.Windows.Automation.Provider.ITextRangeProvider)">
      <summary>Returns a value that indicates whether the span (the <see cref="F:System.Windows.Automation.Text.TextPatternRangeEndpoint.Start" /> endpoint to the <see cref="F:System.Windows.Automation.Text.TextPatternRangeEndpoint.End" /> endpoint) of a text range is the same as another text range. </summary>
      <returns>true if the span of both text ranges is identical; otherwise false. </returns>
      <param name="range">A text range to compare</param>
      <exception cref="T:System.ArgumentException">If the range being compared does not come from the same text provider.</exception>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.CompareEndpoints(System.Windows.Automation.Text.TextPatternRangeEndpoint,System.Windows.Automation.Provider.ITextRangeProvider,System.Windows.Automation.Text.TextPatternRangeEndpoint)">
      <summary>Returns a value that specifies whether two text ranges have identical endpoints.  </summary>
      <returns>Returns a negative value if the caller's endpoint occurs earlier in the text than the target endpoint. Returns zero if the caller's endpoint is at the same location as the target endpoint. Returns a positive value if the caller's endpoint occurs later in the text than the target endpoint. </returns>
      <param name="endpoint">The <see cref="F:System.Windows.Automation.Text.TextPatternRangeEndpoint.Start" /> or <see cref="F:System.Windows.Automation.Text.TextPatternRangeEndpoint.End" /> endpoint of the caller.</param>
      <param name="targetRange">The target range for comparison.</param>
      <param name="targetEndpoint">The <see cref="F:System.Windows.Automation.Text.TextPatternRangeEndpoint.Start" /> or <see cref="F:System.Windows.Automation.Text.TextPatternRangeEndpoint.End" /> endpoint of the target.</param>
      <exception cref="T:System.ArgumentException">If <paramref name="targetRange" /> is from a different text provider.</exception>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.ExpandToEnclosingUnit(System.Windows.Automation.Text.TextUnit)">
      <summary>Expands the text range to the specified text unit.</summary>
      <param name="unit">The textual unit.</param>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.FindAttribute(System.Int32,System.Object,System.Boolean)">
      <summary>Returns a text range subset that has the specified attribute value. </summary>
      <returns>A text range having a matching attribute and attribute value; otherwise null (Nothing in Microsoft Visual Basic .NET). </returns>
      <param name="attribute">The attribute to search for.</param>
      <param name="value">The attribute value to search for. This value must match the type specified for the attribute. </param>
      <param name="backward">true if the last occurring text range should be returned instead of the first; otherwise false. </param>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.FindText(System.String,System.Boolean,System.Boolean)">
      <summary>Returns a text range subset that contains the specified text.</summary>
      <returns>A text range matching the specified text; otherwise null (Nothing in Microsoft Visual Basic .NET).</returns>
      <param name="text">The text string to search for.</param>
      <param name="backward">true if the last occurring text range should be returned instead of the first; otherwise false.</param>
      <param name="ignoreCase">true if case should be ignored; otherwise false.</param>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.GetAttributeValue(System.Int32)">
      <summary>Retrieves the value of the specified attribute across the text range. </summary>
      <returns>Retrieves an object representing the value of the specified attribute. For example, GetAttributeValue(TextPattern.FontNameAttribute) would return a string that represents the font name of the text range while GetAttributeValue(TextPattern.IsItalicAttribute) would return a value of type <see cref="T:System.Boolean" />.Returns <see cref="F:System.Windows.Automation.TextPattern.MixedAttributeValue" /> if the value of the specified attribute varies over the text range.Returns <see cref="F:System.Windows.Automation.AutomationElement.NotSupported" /> if the specified attribute is not supported by the provider or the control. </returns>
      <param name="attribute">The text attribute.</param>
      <exception cref="T:System.ArgumentException">If the specified attribute is not valid.</exception>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.GetBoundingRectangles">
      <summary>Retrieves a collection of bounding rectangles for each fully or partially visible line of text in a text range.</summary>
      <returns>An array of bounding rectangles for each full or partial line of text in a text range.An empty array for a degenerate range.An empty array for a text range that has screen coordinates placing it completely off-screen, scrolled out of view, or obscured by an overlapping window.</returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.GetChildren">
      <summary>Retrieves a collection of all embedded objects that fall within the text range. </summary>
      <returns>A collection of child objects that fall within the range. Children that overlap with the text range but are not entirely enclosed by it will also be included in the collection.Returns an empty collection if there are no child objects.</returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.GetEnclosingElement">
      <summary>Returns the innermost control that encloses the text range. </summary>
      <returns>The enclosing control, typically the text provider that supplies the text range. However, if the text provider supports child elements such as tables or hyperlinks, then the enclosing element could be a descendant of the text provider. </returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.GetText(System.Int32)">
      <summary>Retrieves the plain text of the range. </summary>
      <returns>The plain text of the text range, possibly truncated at the specified <paramref name="maxLength" />. </returns>
      <param name="maxLength">The maximum length of the string to return. Use -1 if no limit is required.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">If <paramref name="maxLength" /> is less than -1.</exception>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.Move(System.Windows.Automation.Text.TextUnit,System.Int32)">
      <summary>Moves the text range the specified number of text units.</summary>
      <returns>The number of units actually moved. This can be less than the number requested if either of the new text range endpoints is greater than or less than the <see cref="P:System.Windows.Automation.Provider.ITextProvider.DocumentRange" /> endpoints. </returns>
      <param name="unit">The text unit boundary.</param>
      <param name="count">The number of text units to move.A positive value moves the text range forward, a negative value moves the text range backward, and 0 has no effect.</param>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.MoveEndpointByRange(System.Windows.Automation.Text.TextPatternRangeEndpoint,System.Windows.Automation.Provider.ITextRangeProvider,System.Windows.Automation.Text.TextPatternRangeEndpoint)">
      <summary>Moves one endpoint of a text range to the specified endpoint of a second text range. </summary>
      <param name="endpoint">The endpoint to move.</param>
      <param name="targetRange">Another range from the same text provider.</param>
      <param name="targetEndpoint">An endpoint on the other range.</param>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.MoveEndpointByUnit(System.Windows.Automation.Text.TextPatternRangeEndpoint,System.Windows.Automation.Text.TextUnit,System.Int32)">
      <summary>Moves one endpoint of the text range the specified number of text units within the document range. </summary>
      <returns>The number of units actually moved, which can be less than the number requested if moving the endpoint runs into the beginning or end of the document.</returns>
      <param name="endpoint">The endpoint to move.</param>
      <param name="unit">The textual unit for moving.</param>
      <param name="count">The number of units to move. A positive value moves the endpoint forward. A negative value moves backward. A value of 0 has no effect.</param>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.RemoveFromSelection">
      <summary>Removes a highlighted section of text, corresponding to the caller's <see cref="F:System.Windows.Automation.Text.TextPatternRangeEndpoint.Start" /> and <see cref="F:System.Windows.Automation.Text.TextPatternRangeEndpoint.End" /> endpoints, from the collection of highlighted text in a text container that supports multiple, disjoint selections.</summary>
      <exception cref="T:System.InvalidOperationException">If text provider does not support multiple, disjoint selections (for example, <see cref="P:System.Windows.Automation.TextPattern.SupportedTextSelection" /> must have a value of Multiple).</exception>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.ScrollIntoView(System.Boolean)">
      <summary>Causes the text control to scroll vertically until the text range is visible in the viewport. </summary>
      <param name="alignToTop">true if the text control should be scrolled so the text range is flush with the top of the viewport; false if it should be flush with the bottom of the viewport. </param>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITextRangeProvider.Select">
      <summary>Highlights text in the text control corresponding to the text range <see cref="F:System.Windows.Automation.Text.TextPatternRangeEndpoint.Start" /> and <see cref="F:System.Windows.Automation.Text.TextPatternRangeEndpoint.End" /> endpoints. </summary>
      <exception cref="T:System.InvalidOperationException">Occurs when text selection is not supported by the text control.</exception>
    </member>
    <member name="T:System.Windows.Automation.Provider.IToggleProvider">
      <summary>Exposes methods and properties to support UI Automation client access to controls that can cycle through a set of states and maintain a state once set.</summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.IToggleProvider.Toggle">
      <summary>Cycles through the toggle states of a control.</summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.IToggleProvider.ToggleState">
      <summary>Gets the toggle state of the control.</summary>
      <returns>The <see cref="T:System.Windows.Automation.ToggleState" /> of the control. </returns>
    </member>
    <member name="T:System.Windows.Automation.Provider.ITransformProvider">
      <summary>Exposes methods and properties to support UI Automation client access to controls that can be moved, resized, or rotated within a two-dimensional space.</summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.ITransformProvider.CanMove">
      <summary>Gets a value that specifies whether the control can be moved.</summary>
      <returns>true if the element can be moved; otherwise false. </returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.ITransformProvider.CanResize">
      <summary>Gets a value that specifies whether the UI Automation element can be resized.</summary>
      <returns>true if the element can be resized; otherwise false. </returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.ITransformProvider.CanRotate">
      <summary>Gets a value that specifies whether the control can be rotated.</summary>
      <returns>true if the element can be rotated; otherwise false. </returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITransformProvider.Move(System.Double,System.Double)">
      <summary>Moves the control.</summary>
      <param name="x">Absolute screen coordinates of the left side of the control.</param>
      <param name="y">Absolute screen coordinates of the top of the control.</param>
      <exception cref="T:System.InvalidOperationException">If the <see cref="P:System.Windows.Automation.Provider.ITransformProvider.CanMove" /> property is false.</exception>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITransformProvider.Resize(System.Double,System.Double)">
      <summary>Resizes the control. </summary>
      <param name="width">The new width of the window, in pixels.</param>
      <param name="height">The new height of the window, in pixels.</param>
      <exception cref="T:System.InvalidOperationException">If the <see cref="P:System.Windows.Automation.Provider.ITransformProvider.CanResize" /> property is false.</exception>
    </member>
    <member name="M:System.Windows.Automation.Provider.ITransformProvider.Rotate(System.Double)">
      <summary>Rotates the control.</summary>
      <param name="degrees">The number of degrees to rotate the control. A positive number rotates clockwise; a negative number rotates counterclockwise.</param>
      <exception cref="T:System.InvalidOperationException">If the <see cref="P:System.Windows.Automation.Provider.ITransformProvider.CanRotate" /> property is false.</exception>
    </member>
    <member name="T:System.Windows.Automation.Provider.IValueProvider">
      <summary>Exposes methods and properties to support UI Automation client access to controls having an intrinsic value that does not span a range and that can be represented as a string. </summary>
    </member>
    <member name="P:System.Windows.Automation.Provider.IValueProvider.IsReadOnly">
      <summary>Gets a value that specifies whether the value of a control is read-only. </summary>
      <returns>true if the value is read-only; false if it can be modified. </returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.IValueProvider.SetValue(System.String)">
      <summary>Sets the value of a control.</summary>
      <exception cref="T:System.InvalidOperationException">If locale-specific information is passed to a control in an incorrect format such as an incorrectly formatted date. </exception>
      <exception cref="T:System.ArgumentException">If a new value cannot be converted from a string to a format the control recognizes.</exception>
      <exception cref="T:System.Windows.Automation.ElementNotEnabledException">When an attempt is made to manipulate a control that is not enabled.</exception>
    </member>
    <member name="P:System.Windows.Automation.Provider.IValueProvider.Value">
      <summary>Gets the value of the control.</summary>
      <returns>The value of the control as a string. </returns>
    </member>
    <member name="T:System.Windows.Automation.Provider.IVirtualizedItemProvider">
      <summary>Exposes a method to support the virtualized item control pattern.</summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.IVirtualizedItemProvider.Realize">
      <summary>Makes the virtual item fully accessible as a UI Automation element.</summary>
    </member>
    <member name="T:System.Windows.Automation.Provider.IWindowProvider">
      <summary>Exposes methods and properties to support UI Automation client access to controls that provide fundamental window-based functionality within a traditional graphical user interface (GUI). </summary>
    </member>
    <member name="M:System.Windows.Automation.Provider.IWindowProvider.Close">
      <summary>Attempts to close the window.</summary>
      <exception cref="T:System.InvalidOperationException">When the control is unable to perform the requested action.</exception>
      <exception cref="T:System.Windows.Automation.ElementNotAvailableException">When the target element is no longer available (for example, the window has closed).</exception>
    </member>
    <member name="P:System.Windows.Automation.Provider.IWindowProvider.InteractionState">
      <summary>Gets the interaction state of the window. </summary>
      <returns>The <see cref="T:System.Windows.Automation.WindowInteractionState" /> of the control. </returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IWindowProvider.IsModal">
      <summary>Gets a value that specifies whether the window is modal.</summary>
      <returns>true if the window is modal; otherwise false.</returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IWindowProvider.IsTopmost">
      <summary>Gets a value that specifies whether the window is the topmost element in the z-order.</summary>
      <returns>true if the window is topmost; otherwise false.</returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IWindowProvider.Maximizable">
      <summary>Gets a value that specifies whether the window can be maximized.</summary>
      <returns>true if the window can be maximized; otherwise false.</returns>
    </member>
    <member name="P:System.Windows.Automation.Provider.IWindowProvider.Minimizable">
      <summary>Gets a value that specifies whether the window can be minimized. </summary>
      <returns>true if the window can be minimized; otherwise false.</returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.IWindowProvider.SetVisualState(System.Windows.Automation.WindowVisualState)">
      <summary>Changes the visual state of the window. For example, minimizes or maximizes it.</summary>
      <param name="state">The requested visual state of the window.</param>
      <exception cref="T:System.InvalidOperationException">When the control does not support the requested behavior.</exception>
    </member>
    <member name="P:System.Windows.Automation.Provider.IWindowProvider.VisualState">
      <summary>Gets the visual state of the window. </summary>
      <returns>The visual state of the window. </returns>
    </member>
    <member name="M:System.Windows.Automation.Provider.IWindowProvider.WaitForInputIdle(System.Int32)">
      <summary>Causes the calling code to block for the specified time or until the associated process enters an idle state, whichever completes first. </summary>
      <returns>true if the window has entered the idle state; false if the timeout occurred.</returns>
      <param name="milliseconds">The amount of time, in milliseconds, to wait for the associated process to become idle. The maximum is <see cref="F:System.Int32.MaxValue" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">When the parameter passed in is not a valid number.</exception>
    </member>
    <member name="T:System.Windows.Automation.Provider.NavigateDirection">
      <summary>Contains values used to specify the direction of navigation within the UI Automation tree.</summary>
    </member>
    <member name="F:System.Windows.Automation.Provider.NavigateDirection.Parent">
      <summary>The navigation direction is toward the parent.</summary>
    </member>
    <member name="F:System.Windows.Automation.Provider.NavigateDirection.NextSibling">
      <summary>The navigation direction is to the next sibling. </summary>
    </member>
    <member name="F:System.Windows.Automation.Provider.NavigateDirection.PreviousSibling">
      <summary>The navigation direction is to the previous sibling. </summary>
    </member>
    <member name="F:System.Windows.Automation.Provider.NavigateDirection.FirstChild">
      <summary>The navigation direction is to the first child. </summary>
    </member>
    <member name="F:System.Windows.Automation.Provider.NavigateDirection.LastChild">
      <summary>The navigation direction is to the last child. </summary>
    </member>
    <member name="T:System.Windows.Automation.Provider.ProviderOptions">
      <summary>Contains values that specify the type of UI Automation provider. </summary>
    </member>
    <member name="F:System.Windows.Automation.Provider.ProviderOptions.ClientSideProvider">
      <summary>The UI Automation provider is a client-side provider. </summary>
    </member>
    <member name="F:System.Windows.Automation.Provider.ProviderOptions.ServerSideProvider">
      <summary>The UI Automation provider is a server-side provider. </summary>
    </member>
    <member name="F:System.Windows.Automation.Provider.ProviderOptions.NonClientAreaProvider">
      <summary>The UI Automation provider is a non-client-area provider. </summary>
    </member>
    <member name="F:System.Windows.Automation.Provider.ProviderOptions.OverrideProvider">
      <summary>The UI Automation provider overrides another provider.</summary>
    </member>
    <member name="F:System.Windows.Automation.Provider.ProviderOptions.ProviderOwnsSetFocus">
      <summary>The UI Automation provider handles its own focus, and does not want UI Automation to set focus to the nearest window on its behalf when <see cref="M:System.Windows.Automation.AutomationElement.SetFocus" /> is called. This option is typically used by providers for windows that appear to take focus without actually receiving Win32 focus, such as menus and drop-down menus.</summary>
    </member>
    <member name="F:System.Windows.Automation.Provider.ProviderOptions.UseComThreading">
      <summary />
    </member>
  </members>
</doc>