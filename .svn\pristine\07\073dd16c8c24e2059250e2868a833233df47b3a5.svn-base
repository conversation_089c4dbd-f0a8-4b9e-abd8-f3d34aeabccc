﻿using OCRTools.Common;
using OCRTools.Language;
using System.Diagnostics;
using System.IO;
using System.Linq;

namespace OCRTools
{
    class LocalOcrService
    {
        public static void CloseService()
        {
            // KillProcess
            try
            {
                var process = Process.GetProcessesByName(Path.GetFileNameWithoutExtension(CommonString.DefaultLocalRecExePath)).FirstOrDefault();
                if (process != null)
                    KillProcessHelper.KillProcess(process);
            }
            catch { }
            MemoryManager.ClearMemory();
        }

        public static void OpenOcrService(int port, int thread, bool isShowMsg = true)
        {
            if (!File.Exists(CommonString.DefaultLocalRecExePath) || Program.NowUser?.IsSupportLocalOcr != true)
            {
                return;
            }

            if (port < 1000 || port > 9999)
            {
                port = 8083;
            }
            if (thread < 1 || thread > 100)
            {
                thread = 5;
            }

            if (isShowMsg)
                CommonMethod.ShowHelpMsg("正在启动本地识别服务进程…".CurrentText());

            CloseService();

            // StartProcess
            //CommonMethod.ExecCmdWithNoResult("start /min cmd /c \"" + CommonString.DefaultLocalRecExePath + "\"");
            CommonMethod.OpenFile(CommonString.DefaultLocalRecExePath, port + " " + thread, false);
            if (isShowMsg)
                CommonMethod.ShowHelpMsg("启动本地识别服务完成！".CurrentText());
        }
    }
}
