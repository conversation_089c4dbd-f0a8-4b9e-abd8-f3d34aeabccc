﻿using OCRTools.Language;
using System;
using System.Data;
using System.IO;
using System.Net;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Xml;

namespace OCRTools
{
    public class CommonUpdate
    {
        private static Thread _updateMainThread;

        private static bool _isOnUpdate;
        public static bool isAlertUpdate;

        public static bool IsAutoCheckUpdate { get; set; } = true;

        public static void InitUpdate()
        {
            if (_updateMainThread != null)
                try
                {
                    _updateMainThread.Abort();
                    _updateMainThread = null;
                }
                catch
                {
                }

            try
            {
                InitUpdateService();
            }
            catch
            {
            }
        }

        private static void InitUpdateService()
        {
            var timerInfo = new TimerInfo
            {
                TimerType = "LoopHours",
                DateValue = (int)Math.Max(CommonSetting.自动更新间隔, 1),
                IsExecFirst = CommonSetting.启动时检查更新,
                Param = CommonString.UpdateFileUrl,
                DtNow = CommonString.DtNowDate,
                TaskName = "UpdateTask"
            };
            _updateMainThread = TimerTaskService.CreateTimerTaskService(timerInfo, UpdateMethod);
            _updateMainThread.Start();
        }

        public static bool CheckInstallLocalDectExe()
        {
            var result = File.Exists(CommonString.DefaultLocalDectExePath);
            if (!result)
            {
                var item = new ObjectTypeItem
                {
                    Name = "本地文档矫正服务",
                    AppPath = CommonString.DefaultLocalDectExePath,
                    Date = DateTime.MinValue,
                    Desc = "Deskew本地文档矫正服务，用于修复倾斜的扫描文档。\n原理：使用 Hough 变换来检测图像中的“文本行”及倾斜方向，\n再加上助手的智能化图像处理算法，来实现倾斜文档的平铺拉伸。",
                    NeedInstall = true,
                    UpdateUrl = CommonString.DectServiceUpdateFileUrl
                };
                CommonUpdate.Install(item.UpdateUrl, item.Date, item.Desc, item.Name, item.AppPath, false, false, true, false, null, true);
                result = File.Exists(CommonString.DefaultLocalDectExePath);
            }
            return result;
        }

        public static string GetServerFile(string fullFileName, string url)
        {
            var result = File.Exists(fullFileName);
            if (!result)
            {
                try
                {
                    var path = Path.GetDirectoryName(fullFileName);
                    if (path != null && !Directory.Exists(path))
                    {
                        Directory.CreateDirectory(path);
                    }
                }
                catch (Exception oe)
                {
                    Log.WriteError("GetServerFile", oe);
                }
                using (var client = new WebClient())
                {
                    client.Proxy = null;
                    client.DownloadFile(url, fullFileName);
                }
                result = File.Exists(fullFileName);
            }
            return result ? fullFileName : string.Empty;
        }

        public static void InstallLocalOcrExe(bool isUserUpdate = false)
        {
            if (!isUserUpdate && !File.Exists(CommonString.DefaultLocalRecExePath))
            {
                return;
            }
            var item = new ObjectTypeItem
            {
                Name = "本地识别".CurrentText(),
                AppPath = CommonString.DefaultLocalRecPath,
                UpdateUrl = CommonString.OcrServiceUpdateFileUrl,
                Date = CommonMethod.GetAssemblyDate(CommonString.DefaultLocalRecExePath)
            };
            var result = CommonUpdate.Install(item.UpdateUrl, item.Date, item.Desc, item.Name, item.AppPath, false, true, true, false, null, true);
            if (isUserUpdate && !result)
            {
                CommonMethod.ShowHelpMsg(item.Name + "已经是最新版本！".CurrentText());
            }
        }

        public static void InstallLocalOcrItem(ObjectTypeItem item)
        {
            CheckIfLocalOcrModuleInstall(ref item);

            var result = CommonUpdate.Install(item.UpdateUrl, item.Date, item.Desc, item.Name, item.AppPath, false, true, true, false, null, true);
            if (!result)
            {
                CommonMethod.ShowHelpMsg(item.Name + "已经是最新版本！".CurrentText());
            }
        }

        public static bool CheckIfLocalOcrModuleInstall(ref ObjectTypeItem item)
        {
            var modelPath = CommonString.DefaultLocalRecModelsPath + item.Name;
            if (Directory.Exists(modelPath))
            {
                var versionTxt = modelPath + "\\version.txt";
                try
                {
                    DateTime dtLast = DateTime.MinValue;
                    if (File.Exists(versionTxt))
                    {
                        DateTime.TryParse(File.ReadAllText(versionTxt), out dtLast);
                    }
                    item.Date = dtLast;
                    return true;
                }
                catch (Exception oe)
                {
                    Log.WriteError("CheckIfLocalOcrModuleInstall", oe);
                }
            }

            return false;
        }

        public static bool Install(string url, DateTime dtNowDate, string desc, string appName, string appPath, bool isUserUpdateMode
            , bool isNeedUnZip, bool isAutoStart, bool isNeedClearFolder, FormUpdate updateForm, bool isDialog = false)
        {
            var result = false;
            var isBeta = false;
            UpdateEntity updateEntity = null;
            result = IsHasNew(url, dtNowDate, desc, ref updateEntity);
            //检查更新Beta版本
            if (!result && CommonSetting.体验Beta测试版)
            {
                var newUrl = CommonString.GetBetaUrl(url);
                UpdateEntity updateBetaEntity = null;
                result = IsHasNew(newUrl, dtNowDate, desc, ref updateBetaEntity);
                if (updateBetaEntity != null)
                {
                    if (result || Equals(updateBetaEntity.dtNowDate, dtNowDate))
                    {
                        isBeta = true;
                        updateEntity = updateBetaEntity;
                    }
                }
            }
            if (result && isUserUpdateMode)
                CommonMethod.ShowHelpMsg("发现新版本,更新前请关闭杀毒软件，或先添加信任，避免被误杀！".CurrentText());
            if (result || isUserUpdateMode)
            {
                if (updateForm == null || updateForm.IsDisposed)
                {
                    updateForm = new FormUpdate
                    {
                        Icon = FrmMain.FrmTool.Icon,
                        UpdateInfo = updateEntity,
                        TopMost = true,
                        StartPosition = FormStartPosition.CenterScreen,
                        AppName = appName,
                        AppPath = appPath,
                        IsUpdateMode = isUserUpdateMode,
                        IsNeedUnZip = isNeedUnZip,
                        IsCanUserUpdate = isUserUpdateMode,
                        IsAutoStart = isAutoStart,
                        IsNeedClearFolder = isNeedClearFolder,
                        IsHasUpdate = result,
                        IsBeta = isBeta
                    };
                }
                CommonMethod.DetermineCall(FrmMain.FrmTool, delegate
                {
                    if (isDialog)
                    {
                        updateForm.ShowDialog();
                    }
                    else
                    {
                        updateForm.Show();
                    }
                });
            }

            return result;
        }

        public static void UpdateMain()
        {
            CommonUpdate.isAlertUpdate = true;
            CommonUpdate.UpdateMethod(true, CommonString.DtNowDate, CommonString.UpdateFileUrl);
        }

        private static bool UpdateMethod(bool isUserUpdate, DateTime dtDate, string url)
        {
            bool result = false;
            try
            {
                if (_isOnUpdate)
                {
                    if (isAlertUpdate) CommonMethod.ShowHelpMsg("正在检查更新".CurrentText() + "，" + CommonString.StrRetry.CurrentText());
                    return result;
                }

                var isCheckUpdate = isUserUpdate || IsAutoCheckUpdate;
                if (isCheckUpdate)
                {
                    _isOnUpdate = true;
                    result = Install(url, dtDate, CommonString.FullName.CurrentText(), CommonString.FullName, Application.ExecutablePath, isUserUpdate, false, false, false, FrmMain.FormUpdate);
                    if (!result)
                    {
                        if (isAlertUpdate)
                        {
                            isAlertUpdate = false;
                            CommonMethod.ShowHelpMsg(string.Format("版本".CurrentText() + ":V{0}，" + "已经是最新版本！".CurrentText(), CommonString.StrNowVersion.Replace(".0", "")));
                        }
                        InstallLocalOcrExe();
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("CommonUpdate.UpdateMethod", oe);
            }
            finally
            {
                _isOnUpdate = false;
            }
            return result;
        }

        public static bool IsHasNew(string url, DateTime dtNowDate, string desc, ref UpdateEntity updateNew)
        {
            var result = false;
            try
            {
                if (!string.IsNullOrEmpty(url))
                {
                    updateNew = LoadVersionInfo(url, desc);
                    if (updateNew != null)
                    {
                        result = dtNowDate < updateNew?.dtNowDate;
                        if (result && !updateNew.IsNowForce)
                        {
                            if (dtNowDate < updateNew.dtMinDate)
                            {
                                updateNew.IsNowForce = true;
                            }
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("CommonUpdate.IsHasNew", oe);
            }

            return result;
        }

        /// <summary>
        /// 将Xml内容字符串转换成DataSet对象
        /// </summary>
        /// <param name="xmlStr"></param>
        /// <returns></returns>
        private static DataSet CXmlToDataSet(string xmlStr)
        {
            if (string.IsNullOrEmpty(xmlStr)) return null;
            using (var ds = new DataSet())
            {
                using (var strStream = new StringReader(xmlStr))
                {
                    using (var xmlrdr = new XmlTextReader(strStream))
                    {
                        ds.ReadXml(xmlrdr);
                    }
                }
                return ds;
            }
        }

        private static UpdateEntity LoadVersionInfo(string url, string desc)
        {
            UpdateEntity updateNew = null;

            if (url.Contains(".zip") || url.Contains(".exe") || url.Contains(".lang"))
            {
                try
                {
                    DateTime newDate = DateTime.MinValue;
                    using (var client = new WebClient())
                    {
                        client.Proxy = null;
                        client.OpenRead(url);
                        if (!string.IsNullOrEmpty(client.ResponseHeaders["Last-Modified"]))
                            newDate = client.ResponseHeaders["Last-Modified"].ToDateTime();
                    }

                    updateNew = new UpdateEntity
                    {
                        strNowVersion = "*******",
                        dtNowDate = newDate.Date,
                        strContext = desc,
                        strURL = url,
                        strFullURL = url,
                        IsNowForce = false
                    };
                }
                catch (Exception oe)
                {
                    Log.WriteError("CommonUpdate.LoadVersionInfo-1", oe);
                }
            }
            else
            {
                updateNew = CommonCDNRequest<UpdateEntity>.InitFromCDN(url, ParseText, 5);
            }

            return updateNew;
        }

        private static UpdateEntity ParseText(string html)
        {
            UpdateEntity update = null;
            if (!string.IsNullOrEmpty(html))
            {
                try
                {
                    if (!html.StartsWith("<"))
                        html = Encoding.UTF8.GetString(Convert.FromBase64String(html));
                    var dsTemp = CXmlToDataSet(html);
                    if (dsTemp != null)
                    {
                        var row = dsTemp.Tables.Count > 0 && dsTemp.Tables[0].Rows.Count > 0 ? dsTemp.Tables[0].Rows[0] : null;
                        if (row != null)
                            update = new UpdateEntity
                            {
                                strNowVersion = row["strNowVersion"].ToString(),
                                dtNowDate = row["dtNowDate"].ToString().ToDateTime(),
                                strMinVersion = row["strMinVersion"]?.ToString(),
                                dtMinDate = row["dtMinDate"]?.ToString().ToDateTime() ?? DateTime.MinValue,
                                strContext = row["strContext"].ToString().Replace("|", Environment.NewLine),
                                strURL = row["strURL"].ToString(),
                                strFullURL = row["strFullURL"].ToString(),
                                IsNowForce = BoxUtil.GetBooleanFromObject(row["IsNowForce"].ToString(), false)
                            };
                        dsTemp.Dispose();
                    }
                }
                catch { }
            }
            return update;
        }
    }

    [Serializable]
    [Obfuscation]
    public class UpdateEntity
    {
        [Obfuscation]
        public string strNowVersion { get; set; }
        [Obfuscation]
        public DateTime dtNowDate { get; set; }
        [Obfuscation]
        public string strMinVersion { get; set; }
        [Obfuscation]
        public DateTime dtMinDate { get; set; }
        [Obfuscation]
        public string strContext { get; set; }
        [Obfuscation]
        public string strURL { get; set; }
        [Obfuscation]
        public string strFullURL { get; set; }
        [Obfuscation]
        public bool IsNowForce { get; set; }
    }
}