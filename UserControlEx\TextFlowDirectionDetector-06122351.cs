using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using OCRTools.Common;

namespace OCRTools.UserControlEx
{
    /// <summary>
    /// 文本流方向检测器，负责分析文本块的排列方向。
    /// </summary>
    public class TextFlowDirectionDetector
    {
        private readonly List<TextCellInfo> cells;

        // 自适应分组阈值
        private double groupingThresholdVertical = 10;
        private double groupingThresholdHorizontal = 10;

        // 极端竖排比例阈值
        private double extremeVerticalRatioThreshold = 7.0;

        public TextFlowDirectionDetector(List<TextCellInfo> cells)
        {
            this.cells = cells;
        }

        /// <summary>
        /// 文本方向检测结果
        /// </summary>
        public class TextDirectionResult
        {
            /// <summary>水平方向（左右方向）</summary>
            public TextFlowDirection HorizontalDirection { get; set; } = TextFlowDirection.LeftToRight;

            /// <summary>垂直方向（上下方向）</summary>
            public TextFlowDirection VerticalDirection { get; set; } = TextFlowDirection.TopToBottom;

            /// <summary>水平方向置信度（0-100）</summary>
            public int HorizontalConfidence { get; set; } = 0;

            /// <summary>垂直方向置信度（0-100）</summary>
            public int VerticalConfidence { get; set; } = 0;

            /// <summary>是否为竖排布局（true表示竖排，false表示横排）</summary>
            public bool IsVerticalLayout { get; set; } = false;

            /// <summary>布局方式置信度（0-100）</summary>
            public int LayoutConfidence { get; set; } = 0;

            /// <summary>
            /// 获取主要文本方向（基于布局类型）
            /// </summary>
            /// <returns>主要阅读方向</returns>
            public TextFlowDirection GetPrimaryDirection()
            {
                // 如果水平方向是混合的，返回垂直方向
                if (HorizontalDirection == TextFlowDirection.Mixed)
                    return VerticalDirection;

                // 如果垂直方向是混合的，返回水平方向
                if (VerticalDirection == TextFlowDirection.Mixed)
                    return HorizontalDirection;

                // 根据布局类型判断主要方向
                return IsVerticalLayout ? VerticalDirection : HorizontalDirection;
            }

            /// <summary>
            /// 获取结果的字符串表示
            /// </summary>
            public override string ToString()
            {
                string layoutType = IsVerticalLayout ? "竖排" : "横排";
                return $"布局类型: {layoutType}(置信度:{LayoutConfidence}), 水平方向: {HorizontalDirection}(置信度:{HorizontalConfidence}), 垂直方向: {VerticalDirection}(置信度:{VerticalConfidence})";
            }
        }

        /// <summary>
        /// 检测文本方向（兼容旧版本）
        /// </summary>
        /// <returns>主要文本方向</returns>
        public TextFlowDirection Detect()
        {
            var result = DetectDirections();
            return result.GetPrimaryDirection();
        }

        /// <summary>
        /// 检测文本的水平和垂直方向
        /// </summary>
        /// <returns>包含水平和垂直方向的结果对象</returns>
        public TextDirectionResult DetectDirections()
        {
            var result = new TextDirectionResult();

            if (cells == null || cells.Count < 2) return result; // 默认方向

            var validCells = cells.Where(c => c?.location != null).ToList();
            if (validCells.Count < 2) return result;
            
            // 计算自适应分组阈值
            CalculateAdaptiveThresholds(validCells);
            
            // 提取统一的文本布局特征
            var features = ExtractTextFeatures(validCells);
            
            // 分析文本布局特征
            
            // 收集方向证据
            var directionEvidence = CollectDirectionEvidenceFromFeatures(features);
            
            // 分析方向证据，确定水平和垂直方向
            AnalyzeDirectionEvidence(directionEvidence, result, validCells);
            
            // 分析列的分布和方向特征
            bool isLikelyRightToLeft = false;
            if (features.VerticalColumnCount >= 2)
            {
                // 计算首尾列的中心位置
                var firstColumn = features.VerticalColumns.First();
                var lastColumn = features.VerticalColumns.Last();
                
                double firstColumnCenter = firstColumn.Average(c => c.location.left + c.location.width / 2);
                double lastColumnCenter = lastColumn.Average(c => c.location.left + c.location.width / 2);
                
                // 检测是否从右到左排列 - 第一列位于页面右侧，最后一列位于页面左侧
                isLikelyRightToLeft = firstColumnCenter > features.PageCenter_X && lastColumnCenter < features.PageCenter_X;
                
                // 基于内容密度进行分析
                bool densitySupportsRightToLeft = features.ContentDensityRightHalf > features.ContentDensityLeftHalf * 1.2;
                
                // 分析右侧列比例
                int columnsInRightHalf = 0;
                foreach (var column in features.VerticalColumns)
                {
                    double centerX = column.Average(c => c.location.left + c.location.width / 2);
                    if (centerX > features.PageCenter_X)
                    {
                        columnsInRightHalf++;
                    }
                }
                
                double rightColumnsRatio = columnsInRightHalf / (double)features.VerticalColumnCount;
                
                // 如果右半页列比例大于50%，考虑从右到左读取
                if (rightColumnsRatio > 0.55 && (densitySupportsRightToLeft || !isLikelyRightToLeft))
                {
                    isLikelyRightToLeft = true;
                }
            }
            
            // 如果列分布分析发现从右到左的倾向，增加相应证据
            if (isLikelyRightToLeft)
            {
                directionEvidence.RightToLeftCount += Math.Max(2, features.VerticalColumnCount);
            }
            
            // 确定文档布局类型
            DetermineLayoutType(result, directionEvidence);

            // 确保最低置信度，避免不确定结果
            EnsureMinimumConfidence(result);

            // 对于复杂文档（有更多文本块），进行额外的置信度优化
            if (validCells.Count >= 10)
            {
                ApplyAdvancedConfidenceOptimization(result, directionEvidence, validCells);
            }

            // 进行最终的一致性检查 - 确保方向和布局保持一致
            AdjustConfidenceBasedOnConsistency(result, directionEvidence);
            
            // 最终水平方向判断调整 - 竖排多列情况特殊处理
            if (result.IsVerticalLayout)
            {
                // 竖排文本特殊处理
                OptimizeVerticalLayoutDirections(result, features, directionEvidence);
            }

            return result;
        }
        
        /// <summary>
        /// 优化竖排布局的方向判断
        /// </summary>
        /// <param name="result">方向结果</param>
        /// <param name="features">文本布局特征</param>
        /// <param name="evidence">方向证据</param>
        private void OptimizeVerticalLayoutDirections(TextDirectionResult result, TextLayoutFeatures features, DirectionEvidence evidence)
        {
            // 针对竖排文本的特殊优化
            if (features.VerticalTextCount <= features.HorizontalTextCount) return;
            
            // 1. 多列竖排文本的水平方向判断
            if (features.VerticalColumnCount >= 2)
            {
                // 计算右到左和左到右的证据比例
                double totalHorizontalEvidence = evidence.LeftToRightCount + evidence.RightToLeftCount;
                if (totalHorizontalEvidence > 0)
                {
                    double rtlRatio = evidence.RightToLeftCount / totalHorizontalEvidence;
                    
                    // 如果右到左证据占比较高，调整方向和置信度
                    if (rtlRatio > 0.4) // 阈值降低，因为竖排文本的水平方向判断较难
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        
                        // 置信度基于证据比例
                        int confidenceBoost = (int)(Math.Min(25, features.VerticalColumnCount * 5) * 
                                                   Math.Min(1.0, rtlRatio + 0.2));
                        
                        result.HorizontalConfidence = Math.Min(95, 
                            Math.Max(result.HorizontalConfidence, result.HorizontalConfidence + confidenceBoost));
                    }
                }
                
                // 2. 基于内容分布的判断
                if (features.ContentDensityRightHalf > 0 && features.ContentDensityLeftHalf > 0)
                {
                    double densityRatio = features.ContentDensityRightHalf / features.ContentDensityLeftHalf;
                    
                    // 右半页密度明显高于左半页，增强从右到左的可能性
                    if (densityRatio > 1.2)
                    {
                        // 如果已经是RightToLeft，增强置信度
                        if (result.HorizontalDirection == TextFlowDirection.RightToLeft)
                        {
                            result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 10);
                        }
                        // 如果是LeftToRight但置信度不高，考虑切换
                        else if (result.HorizontalConfidence < 70)
                        {
                            result.HorizontalDirection = TextFlowDirection.RightToLeft;
                            result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 70);
                        }
                    }
                    // 左半页密度明显高于右半页，增强从左到右的可能性
                    else if (densityRatio < 0.8)
                    {
                        // 如果已经是LeftToRight，增强置信度
                        if (result.HorizontalDirection == TextFlowDirection.LeftToRight)
                        {
                            result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 10);
                        }
                        // 如果是RightToLeft但置信度不高，考虑切换
                        else if (result.HorizontalConfidence < 70)
                        {
                            result.HorizontalDirection = TextFlowDirection.LeftToRight;
                            result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 70);
                        }
                    }
                }
            }
            
            // 3. 单列竖排文本的特殊处理
            else if (features.VerticalTextCount >= 3)
            {
                // 检查是否为中日韩文字
                bool isCJKText = IsCJKText(this.cells);
                
                // 对于中日韩文字的单列竖排，增强从右到左的可能性
                if (isCJKText && result.HorizontalConfidence < 80)
                {
                    result.HorizontalDirection = TextFlowDirection.RightToLeft;
                    result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 75);
                }
            }
            
            // 4. 确保垂直方向置信度较高
            if (result.VerticalDirection == TextFlowDirection.TopToBottom)
            {
                result.VerticalConfidence = Math.Max(result.VerticalConfidence, 85);
            }
        }
        
        /// <summary>
        /// 检查文本是否主要为中日韩文字
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <returns>是否为中日韩文字</returns>
        private bool IsCJKText(List<TextCellInfo> cells)
        {
            int cjkCharCount = 0;
            int totalCharCount = 0;
            
            foreach (var cell in cells)
            {
                if (string.IsNullOrEmpty(cell.words)) continue;
                
                foreach (char c in cell.words)
                {
                    totalCharCount++;
                    // 检查是否为中日韩文字（粗略判断）
                    if (c >= 0x4E00 && c <= 0x9FFF) // 基本汉字范围
                    {
                        cjkCharCount++;
                    }
                }
            }
            
            // 如果70%以上是中日韩文字，认为是中日韩文本
            return totalCharCount > 0 && (double)cjkCharCount / totalCharCount > 0.7;
        }
        
        /// <summary>
        /// 基于统一特征收集方向证据
        /// </summary>
        /// <param name="features">文本布局特征</param>
        /// <returns>方向证据</returns>
        private DirectionEvidence CollectDirectionEvidenceFromFeatures(TextLayoutFeatures features)
        {
            var evidence = new DirectionEvidence();
            
            // 1. 从形状特征收集证据
            evidence.HorizontalTextCount = features.HorizontalTextCount;
            evidence.VerticalTextCount = features.VerticalTextCount;
            
            // 2. 从行列结构特征收集证据
            evidence.HorizontalAlignedRows = features.HorizontalRowCount;
            evidence.VerticalAlignedColumns = features.VerticalColumnCount;
            
            // 3. 从对齐特征收集证据
            evidence.LeftAlignedCount = features.LeftAlignedCount;
            evidence.RightAlignedCount = features.RightAlignedCount;
            evidence.TopAlignedCount = features.TopAlignedCount;
            evidence.BottomAlignedCount = features.BottomAlignedCount;
            
            // 4. 从边缘变异特征收集证据
            evidence.LeftEdgeVariance = features.LeftEdgeVariance;
            evidence.RightEdgeVariance = features.RightEdgeVariance;
            evidence.TopEdgeVariance = features.TopEdgeVariance;
            evidence.BottomEdgeVariance = features.BottomEdgeVariance;
            
            // 5. 从段落特征收集证据
            evidence.ParagraphCount = features.ParagraphCount;
            
            // 6. 从方向证据直接收集
            evidence.LeftToRightCount += features.LeftToRightEvidence;
            evidence.RightToLeftCount += features.RightToLeftEvidence;
            evidence.TopToBottomCount += features.TopToBottomEvidence;
            evidence.BottomToTopCount += features.BottomToTopEvidence;
            
            // 计算基准单位 - 基于文本块总数的动态基准
            // 文本块越多，每个证据的权重越小，避免大文档中证据过度累积
            int baseUnitWeight = Math.Max(1, Math.Min(5, features.ValidTextBlocks / 10));
            
            // 7. 从边缘变异分析方向 - 使用动态阈值
            // 计算边缘整齐度强度 (变异比率越极端，强度越高)
            double leftRightVarianceStrength = 0;
            double topBottomVarianceStrength = 0;
            
            // 左右边缘变异比率阈值 - 动态计算
            double lrLowThreshold = Math.Max(0.7, 0.8 - features.ValidTextBlocks * 0.005); // 文本块越多，阈值越低
            double lrHighThreshold = Math.Min(1.3, 1.25 + features.ValidTextBlocks * 0.005); // 文本块越多，阈值越高
            
            // 左边界比右边界更整齐（变异小）表示从左到右阅读
            if (features.LeftRightEdgeVarianceRatio < lrLowThreshold)
            {
                leftRightVarianceStrength = Math.Min(1.0, (lrLowThreshold - features.LeftRightEdgeVarianceRatio) / 0.3);
                evidence.LeftToRightCount += (int)(baseUnitWeight * 3 * leftRightVarianceStrength);
            }
            // 右边界比左边界更整齐表示从右到左阅读
            else if (features.LeftRightEdgeVarianceRatio > lrHighThreshold)
            {
                leftRightVarianceStrength = Math.Min(1.0, (features.LeftRightEdgeVarianceRatio - lrHighThreshold) / 0.75);
                evidence.RightToLeftCount += (int)(baseUnitWeight * 3 * leftRightVarianceStrength);
            }
            
            // 上下边缘变异比率阈值 - 动态计算
            double tbLowThreshold = Math.Max(0.7, 0.8 - features.ValidTextBlocks * 0.005);
            double tbHighThreshold = Math.Min(1.3, 1.25 + features.ValidTextBlocks * 0.005);
            
            // 上边界比下边界更整齐表示从上到下阅读
            if (features.TopBottomEdgeVarianceRatio < tbLowThreshold)
            {
                topBottomVarianceStrength = Math.Min(1.0, (tbLowThreshold - features.TopBottomEdgeVarianceRatio) / 0.3);
                evidence.TopToBottomCount += (int)(baseUnitWeight * 3 * topBottomVarianceStrength);
            }
            // 下边界比上边界更整齐表示从下到上阅读
            else if (features.TopBottomEdgeVarianceRatio > tbHighThreshold)
            {
                topBottomVarianceStrength = Math.Min(1.0, (features.TopBottomEdgeVarianceRatio - tbHighThreshold) / 0.75);
                evidence.BottomToTopCount += (int)(baseUnitWeight * 3 * topBottomVarianceStrength);
            }
            
            // 8. 竖排文本的特殊处理 - 根据竖排文本比例增强方向证据
            if (features.VerticalTextCount > features.HorizontalTextCount * 1.5)
            {
                // 竖排文本比例明显高于横排文本
                double verticalRatio = Math.Min(1.0, 
                    (double)features.VerticalTextCount / Math.Max(1, features.HorizontalTextCount) / 2.0);
                
                // 强度限制在0.2-1.0之间
                verticalRatio = Math.Max(0.2, verticalRatio);
                
                // 增加从上到下的证据权重
                evidence.TopToBottomCount += (int)(baseUnitWeight * 5 * verticalRatio);
                
                // 对于竖排文本，如果有明确的右到左证据，增强这一方向
                if (features.RightToLeftEvidence > features.LeftToRightEvidence)
                {
                    double rtlStrength = Math.Min(1.0, 
                        (double)features.RightToLeftEvidence / Math.Max(1, features.LeftToRightEvidence));
                    
                    evidence.RightToLeftCount += (int)(baseUnitWeight * 4 * rtlStrength * verticalRatio);
                }
            }
            
            return evidence;
        }

        /// <summary>
        /// 对复杂文档应用高级置信度优化
        /// </summary>
        private void ApplyAdvancedConfidenceOptimization(TextDirectionResult result, DirectionEvidence evidence, List<TextCellInfo> cells)
        {
            // 1. 检查布局和主方向的一致性
            // 检查是否存在强烈的一致性证据
            bool hasStrongConsistency = false;

            // 横排文本与从左到右方向一致性
            if (!result.IsVerticalLayout && result.HorizontalDirection == TextFlowDirection.LeftToRight &&
                evidence.LeftToRightCount > evidence.RightToLeftCount * 2 &&
                evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
            {
                hasStrongConsistency = true;
            }

            // 竖排文本与从上到下方向一致性
            else if (result.IsVerticalLayout && result.VerticalDirection == TextFlowDirection.TopToBottom &&
                    evidence.TopToBottomCount > evidence.BottomToTopCount * 2 &&
                    evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
            {
                hasStrongConsistency = true;
            }

            // 如果有强烈一致性，适当提高置信度
            if (hasStrongConsistency)
            {
                // 同时提高布局和方向置信度
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 5);

                if (!result.IsVerticalLayout)
                {
                    result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 5);
                }
                else
                {
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + 5);
                }
            }

            // 2. 一致性特征分析
            // 计算各种证据的互相支持程度
            int consistencyScore = 0;

            // 文本形状与对齐特征一致性
            if ((evidence.HorizontalTextCount > evidence.VerticalTextCount &&
                 evidence.LeftAlignedCount > evidence.TopAlignedCount) ||
                (evidence.VerticalTextCount > evidence.HorizontalTextCount &&
                 evidence.TopAlignedCount > evidence.LeftAlignedCount))
            {
                consistencyScore += 2;
            }

            // 边界变异与方向一致性
            if ((evidence.LeftEdgeVariance < evidence.RightEdgeVariance &&
                 result.HorizontalDirection == TextFlowDirection.LeftToRight) ||
                (evidence.RightEdgeVariance < evidence.LeftEdgeVariance &&
                 result.HorizontalDirection == TextFlowDirection.RightToLeft) ||
                (evidence.TopEdgeVariance < evidence.BottomEdgeVariance &&
                 result.VerticalDirection == TextFlowDirection.TopToBottom) ||
                (evidence.BottomEdgeVariance < evidence.TopEdgeVariance &&
                 result.VerticalDirection == TextFlowDirection.BottomToTop))
            {
                consistencyScore += 2;
            }

            // 段落布局与布局类型一致性
            if ((evidence.ParagraphCount >= 3 && !result.IsVerticalLayout) ||
                (evidence.IsSequentialParagraphs && evidence.LeftAlignmentRatio > 0.7 && !result.IsVerticalLayout))
            {
                consistencyScore += 2;
            }

            // 应用一致性评分
            if (consistencyScore >= 4)
            {
                // 高一致性得分提高整体置信度
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyScore / 2);
                result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + consistencyScore / 2);
                result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + consistencyScore / 2);
            }
        }

        /// <summary>
        /// 确保结果具有合理的置信度，避免不确定的输出
        /// </summary>
        private void EnsureMinimumConfidence(TextDirectionResult result)
        {
            // 基于相对性的置信度调整

            // 1. 默认方向置信度阈值 - 使用相对阈值而非绝对值
            int baseThreshold = 50; // 最基础的置信度阈值

            // 2. 根据文本复杂度动态调整阈值
            // 如果两个方向的置信度差异很小，说明文本布局复杂或模糊，应该降低阈值
            if (Math.Abs(result.HorizontalConfidence - result.VerticalConfidence) < 15)
            {
                baseThreshold = 45; // 降低阈值
            }

            // 3. 水平方向的置信度调整
            if (result.HorizontalConfidence < baseThreshold)
            {
                // 根据垂直方向的置信度动态决定水平方向的默认置信度
                // 如果垂直方向的置信度很高，我们对水平方向的默认值更有信心
                int defaultHorizontalConfidence =
                    result.VerticalConfidence > 75 ? 65 : 60;

                result.HorizontalDirection = TextFlowDirection.LeftToRight; // 最常见的默认方向
                result.HorizontalConfidence = defaultHorizontalConfidence;
            }

            // 4. 垂直方向的置信度调整
            if (result.VerticalConfidence < baseThreshold)
            {
                // 根据水平方向的置信度动态决定垂直方向的默认置信度
                int defaultVerticalConfidence =
                    result.HorizontalConfidence > 75 ? 70 : 65;

                result.VerticalDirection = TextFlowDirection.TopToBottom; // 最常见的默认方向
                result.VerticalConfidence = defaultVerticalConfidence;
            }

            // 5. 布局类型置信度调整 - 根据主方向置信度动态决定
            if (result.LayoutConfidence < baseThreshold)
            {
                // 根据当前布局类型选择参考置信度
                int referenceConfidence = result.IsVerticalLayout ?
                    result.VerticalConfidence : result.HorizontalConfidence;

                // 如果参考置信度高，保持当前布局类型但提高置信度
                if (referenceConfidence > 70)
                {
                    result.LayoutConfidence = Math.Max(referenceConfidence - 10, 60);
                }
                // 否则采用更保守的默认值：横排布局（更常见）
                else
                {
                    result.IsVerticalLayout = false;
                    result.LayoutConfidence = 60;
                }
            }
        }

        /// <summary>
        /// 确定文档的布局类型（横排或竖排）
        /// </summary>
        /// <remarks>
        /// 这个方法是布局类型判断的核心算法，使用多维度特征的累加得分系统来判断文档是横排还是竖排布局。
        /// 该方法考虑多种布局证据，包括文本形状、行列结构、对齐特征等，适用于各种语言的文档。
        /// </remarks>
        /// <param name="result">输出的文本方向结果，将设置布局类型</param>
        /// <param name="evidence">收集的方向证据</param>
        private void DetermineLayoutType(TextDirectionResult result, DirectionEvidence evidence)
        {
            // 使用得分累加系统确定布局类型
            // 通过多维度特征分析，使系统适用于各种不同的文档布局
            int horizontalLayoutScore = 0; // 横排布局得分
            int verticalLayoutScore = 0;   // 竖排布局得分

            // 开始详细的布局分析记录
            System.Diagnostics.Debug.WriteLine("\n============ 布局类型分析开始 ============");

            // ====================== 布局类型特征分析 ======================

            // 1. 文本块形状特征 - 最直观和通用的特征
            // 对任何语言都适用，无需特定语言知识
            // 1.1 高宽比特征 - 根据文本块的形状判断其可能的排版方向

            // 计算总的形状特征得分
            int totalShapeScore = 0;

            // 记录形状得分情况
            System.Diagnostics.Debug.WriteLine("\n--- 1. 形状特征得分 ---");
            System.Diagnostics.Debug.WriteLine($"竖向文本数: {evidence.VerticalTextCount}, 横向文本数: {evidence.HorizontalTextCount}");

            // 检查是否存在极端竖排文本特征
            bool hasExtremeVerticalFeature = false;
            bool hasExtremeHorizontalFeature = false;

            // 检测极端比例文本块占比
            double totalTextCount = evidence.VerticalTextCount + evidence.HorizontalTextCount;
            double verticalRatio = totalTextCount > 0 ? evidence.VerticalTextCount / totalTextCount : 0;
            double horizontalRatio = totalTextCount > 0 ? evidence.HorizontalTextCount / totalTextCount : 0;

            System.Diagnostics.Debug.WriteLine($"竖向文本占比: {verticalRatio:P2}, 横向文本占比: {horizontalRatio:P2}");
            
            // 检查是否有足够的文本来判断
            if (totalTextCount < 3)
            {
                System.Diagnostics.Debug.WriteLine("文本数量不足，难以可靠判断形状特征");
                // 使用其他证据继续分析
            }
            else
            {
                // 1. 分析显著优势情况 - 一种形状完全占优
                if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
                {
                    // 竖向文本明显占优 - 强烈暗示竖排布局
                    int score = 8;  // 强力证据

                    // 增强对极端竖排情况的检测
                    if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 2.5)
                    {
                        // 竖向文本极度占优 - 之前是3倍，现在降低到2.5倍
                        score += 6; // 增加得分
                        hasExtremeVerticalFeature = true;
                        System.Diagnostics.Debug.WriteLine($"竖向文本远远多于横向(2.5倍以上) => 竖排得分 +{score}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"竖向文本远多于横向(1.5倍以上) => 竖排得分 +{score}");
                    }

                    verticalLayoutScore += score;
                    totalShapeScore += score;
                }
                else if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.25)
                {
                    // 竖向文本明显较多 - 从1.4倍降低到1.25倍
                    int score = 6;  // 中等证据
                    verticalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"竖向文本明显多于横向(1.25倍以上) => 竖排得分 +{score}");
                }
                else if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.1)
                {
                    // 竖向文本略多 - 从1.2倍降低到1.1倍
                    int score = 4;  // 中弱证据
                    verticalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"竖向文本略多于横向(1.1倍以上) => 竖排得分 +{score}");
                }
                else if (evidence.VerticalTextCount > evidence.HorizontalTextCount)
                {
                    // 竖向文本稍多 - 弱竖排证据，但仍有意义
                    int score = 3;  // 弱证据，增加得分
                    verticalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"竖向文本稍多于横向 => 竖排得分 +{score}");
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
                {
                    // 横向文本明显占优 - 强烈暗示横排布局
                    int score = 8;  // 强力证据

                    // 增强对极端横排情况的检测
                    if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 3)
                    {
                        // 横向文本极度占优
                        score += 4;
                        hasExtremeHorizontalFeature = true;
                        System.Diagnostics.Debug.WriteLine($"横向文本远远多于竖向(3倍以上) => 横排得分 +{score}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"横向文本远多于竖向(1.5倍以上) => 横排得分 +{score}");
                    }

                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.4)
                {
                    // 横向文本明显较多 - 中等强度的横排证据
                    int score = 6;  // 中等证据
                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"横向文本明显多于竖向(1.4倍以上) => 横排得分 +{score}");
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.2)
                {
                    // 横向文本略多 - 中弱强度的横排证据
                    int score = 4;  // 中弱证据
                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"横向文本略多于竖向(1.2倍以上) => 横排得分 +{score}");
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount)
                {
                    // 横向文本稍多 - 弱横排证据，但仍有意义
                    int score = 2;  // 弱证据
                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"横向文本稍多于竖向 => 横排得分 +{score}");
                }
                // 2. 当文本数量接近相等时，引入额外判断因素
                else if (Math.Abs(evidence.VerticalTextCount - evidence.HorizontalTextCount) <= 2)
                {
                    // 当形状特征没有明显优势时，考虑其他证据
                    System.Diagnostics.Debug.WriteLine($"横向与竖向文本数量接近，形状特征不明显");

                    // 检查列与行的数量，通常能更好地反映整体布局
                    if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows)
                    {
                        int score = 4; // 增加得分
                        verticalLayoutScore += score;
                        totalShapeScore += score;
                        System.Diagnostics.Debug.WriteLine($"垂直列数({evidence.VerticalAlignedColumns})大于水平行数({evidence.HorizontalAlignedRows}) => 竖排得分 +{score}");
                    }
                    else if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns)
                    {
                        int score = 3;
                        horizontalLayoutScore += score;
                        totalShapeScore += score;
                        System.Diagnostics.Debug.WriteLine($"水平行数({evidence.HorizontalAlignedRows})大于垂直列数({evidence.VerticalAlignedColumns}) => 横排得分 +{score}");
                    }
                }
            }

            // 检查极端形状特征的占比影响
            if (verticalRatio > 0.7 && evidence.VerticalTextCount >= 3)
            {
                // 如果竖向文本占比超过70%，且至少有3个，这是很强的竖排证据
                // 原来是80%，现在降低到70%
                int bonusScore = 8; // 增加得分
                verticalLayoutScore += bonusScore;
                totalShapeScore += bonusScore;
                System.Diagnostics.Debug.WriteLine($"竖向文本占比超过70%，强烈的竖排证据 => 竖排得分 +{bonusScore}");
            }
            else if (horizontalRatio > 0.8 && evidence.HorizontalTextCount >= 3)
            {
                // 如果横向文本占比超过80%，且至少有3个，这是很强的横排证据
                int bonusScore = 6;
                horizontalLayoutScore += bonusScore;
                totalShapeScore += bonusScore;
                System.Diagnostics.Debug.WriteLine($"横向文本占比超过80%，强烈的横排证据 => 横排得分 +{bonusScore}");
            }

            // 形状特征总结
            if (totalShapeScore > 0)
            {
                double verticalRatioScore = verticalLayoutScore / (double)totalShapeScore;
                System.Diagnostics.Debug.WriteLine($"形状特征总结: 总分={totalShapeScore}, 竖排得分比例={verticalRatioScore:P2}");

                if (verticalRatioScore > 0.6)
                {
                    System.Diagnostics.Debug.WriteLine("形状特征总体倾向: 竖排");
                }
                else if (verticalRatioScore < 0.4)
                {
                    System.Diagnostics.Debug.WriteLine("形状特征总体倾向: 横排");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("形状特征总体倾向: 不确定");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("形状特征分析: 无明显倾向");
            }

            // 2. 方向置信度比较 - 通过已确定的方向置信度差异判断布局
            // 已确定的阅读方向置信度差异可以作为布局判断的辅助证据
            int confidenceDiff = Math.Abs(result.HorizontalConfidence - result.VerticalConfidence);
            if (confidenceDiff >= 20) // 方向置信度差异非常明显
            {
                // 方向置信度差异很大，证据强
                if (result.HorizontalConfidence > result.VerticalConfidence)
                {
                    // 水平方向置信度明显高 - 横排证据
                    // 例：阅读方向分析中水平方向特征非常强
                    horizontalLayoutScore += 6;
                }
                else
                {
                    // 垂直方向置信度明显高 - 竖排证据
                    // 例：阅读方向分析中垂直方向特征非常强
                    verticalLayoutScore += 6;
                }
            }
            else if (confidenceDiff >= 10) // 方向置信度有一定差异
            {
                // 方向置信度差异中等
                if (result.HorizontalConfidence > result.VerticalConfidence)
                {
                    // 水平方向置信度稍高 - 弱横排证据
                    horizontalLayoutScore += 3;
                }
                else
                {
                    // 垂直方向置信度稍高 - 弱竖排证据
                    verticalLayoutScore += 3;
                }
            }

            // 3. 行列结构比较 - 文档的整体排列结构
            // 3.1 行数与列数比较 - 分析文本的整体排列模式
            if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows * 1.8)
            {
                // 列数远多于行数 - 强竖排证据，降低阈值从2.0到1.8
                // 例：传统竖排中文，文本块按列排列
                verticalLayoutScore += 8;  // 增加得分
                System.Diagnostics.Debug.WriteLine($"列数({evidence.VerticalAlignedColumns})远多于行数({evidence.HorizontalAlignedRows})，强竖排证据 => 竖排得分 +8");
            }
            else if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows * 1.3)
            {
                // 列数明显多于行数 - 中等竖排证据，降低阈值从1.5到1.3
                verticalLayoutScore += 6;  // 增加得分
                System.Diagnostics.Debug.WriteLine($"列数明显多于行数(1.3倍) => 竖排得分 +6");
            }
            else if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows)
            {
                // 列数略多于行数 - 弱竖排证据
                verticalLayoutScore += 3;  // 增加得分
                System.Diagnostics.Debug.WriteLine($"列数略多于行数 => 竖排得分 +3");
            }
            else if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns * 2.0)
            {
                // 行数远多于列数 - 强横排证据
                // 例：普通书籍文档，文本块按行排列
                horizontalLayoutScore += 7;  // 行数远大于列数，强力横排证据
                System.Diagnostics.Debug.WriteLine($"行数远多于列数(2倍) => 横排得分 +7");
            }
            else if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns * 1.5)
            {
                // 行数明显多于列数 - 中等横排证据
                horizontalLayoutScore += 5;  // 行数明显多于列数
                System.Diagnostics.Debug.WriteLine($"行数明显多于列数(1.5倍) => 横排得分 +5");
            }
            else if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns)
            {
                // 行数略多于列数 - 弱横排证据
                horizontalLayoutScore += 2;  // 行数略多于列数
                System.Diagnostics.Debug.WriteLine($"行数略多于列数 => 横排得分 +2");
            }

            // 检查列数和行数的绝对值
            if (evidence.VerticalAlignedColumns >= 3)
            {
                // 至少有3列，这是很强的竖排证据
                int columnBonus = Math.Min(10, evidence.VerticalAlignedColumns * 2);
                verticalLayoutScore += columnBonus;
                System.Diagnostics.Debug.WriteLine($"至少有3列排列，强竖排证据 => 竖排得分 +{columnBonus}");
            }
            else if (evidence.VerticalAlignedColumns == 2)
            {
                // 有2列，中等竖排证据
                verticalLayoutScore += 5;
                System.Diagnostics.Debug.WriteLine($"有2列排列，中等竖排证据 => 竖排得分 +5");
            }

            // 4. 对齐特征 - 分析文本块的对齐模式
            // 4.1 左/右对齐 vs 上/下对齐的比较
            if (evidence.LeftAlignedCount > evidence.TopAlignedCount * 2.0)
            {
                // 左对齐远多于顶部对齐 - 强横排证据
                // 例：典型的左对齐段落排版
                horizontalLayoutScore += 5;  // 左对齐远多于顶对齐，横排证据
                System.Diagnostics.Debug.WriteLine($"左对齐({evidence.LeftAlignedCount})远多于顶对齐({evidence.TopAlignedCount})，横排证据 => 横排得分 +5");
            }
            else if (evidence.LeftAlignedCount > evidence.TopAlignedCount * 1.5)
            {
                // 左对齐明显多于顶部对齐 - 中等横排证据
                horizontalLayoutScore += 3;
                System.Diagnostics.Debug.WriteLine($"左对齐明显多于顶对齐(1.5倍) => 横排得分 +3");
            }
            else if (evidence.TopAlignedCount > evidence.LeftAlignedCount * 2.0)
            {
                // 顶部对齐远多于左对齐 - 强竖排证据
                // 例：竖排文本的顶部对齐排版
                verticalLayoutScore += 5;  // 顶对齐远多于左对齐，竖排证据
                System.Diagnostics.Debug.WriteLine($"顶对齐({evidence.TopAlignedCount})远多于左对齐({evidence.LeftAlignedCount})，竖排证据 => 竖排得分 +5");
            }
            else if (evidence.TopAlignedCount > evidence.LeftAlignedCount * 1.5)
            {
                // 顶部对齐明显多于左对齐 - 中等竖排证据
                verticalLayoutScore += 3;
                System.Diagnostics.Debug.WriteLine($"顶对齐明显多于左对齐(1.5倍) => 竖排得分 +3");
            }

            // 5. 边界变异特征 - 文本边界的整齐程度
            // 阅读起始边界通常更加整齐（变异小）
            if (evidence.LeftEdgeVariance < evidence.TopEdgeVariance * 0.5)
            {
                // 左边界比顶部边界更整齐 - 横排证据
                // 例：左对齐的横排文本，左边界一致而顶部边界不一致
                horizontalLayoutScore += 3;  // 左边界比顶边界更整齐，横排证据
                System.Diagnostics.Debug.WriteLine($"左边界比顶边界更整齐，横排证据 => 横排得分 +3");
            }
            else if (evidence.TopEdgeVariance < evidence.LeftEdgeVariance * 0.5)
            {
                // 顶部边界比左边界更整齐 - 竖排证据
                // 例：顶部对齐的竖排文本，顶部边界一致而左边界不一致
                verticalLayoutScore += 3;  // 顶边界比左边界更整齐，竖排证据
                System.Diagnostics.Debug.WriteLine($"顶边界比左边界更整齐，竖排证据 => 竖排得分 +3");
            }

            // 9. 段落特征 - 段落结构分析
            if (evidence.ParagraphCount >= 3)
            {
                // 多段落文本通常是横排的
                // 例：常见的文章结构，多个段落按横排布局
                horizontalLayoutScore += 4;
                System.Diagnostics.Debug.WriteLine($"检测到多段落结构({evidence.ParagraphCount}个)，横排证据 => 横排得分 +4");
            }

            // 增强对极端竖排布局的识别
            if (hasExtremeVerticalFeature && evidence.VerticalAlignedColumns >= 2)
            {
                // 如果有极端竖排特征且至少有2列，增加竖排得分
                int extraScore = 10;  // 提高权重，使其更有影响力
                verticalLayoutScore += extraScore;
                System.Diagnostics.Debug.WriteLine($"检测到极端竖排特征且有多列排列 => 额外竖排得分 +{extraScore}");
            }
            
            // 增强对极端横排布局的识别
            if (hasExtremeHorizontalFeature && evidence.HorizontalAlignedRows >= 2)
            {
                // 如果有极端横排特征且至少有2行，增加横排得分
                int extraScore = 8;  // 提高权重，使其更有影响力
                horizontalLayoutScore += extraScore;
                System.Diagnostics.Debug.WriteLine($"检测到极端横排特征且有多行排列 => 额外横排得分 +{extraScore}");
            }

            // ====================== 布局类型判断 ======================

            // 综合评分确定布局类型 - 分数高的布局类型胜出
            bool isVerticalLayout = verticalLayoutScore > horizontalLayoutScore;

            // 计算置信度 - 基于得分差异比例
            int totalScore = Math.Max(1, horizontalLayoutScore + verticalLayoutScore);
            int winningScore = isVerticalLayout ? verticalLayoutScore : horizontalLayoutScore;
            int opposingScore = isVerticalLayout ? horizontalLayoutScore : verticalLayoutScore;

            // 计算置信度 - 分数差异越大，置信度越高
            int layoutConfidence = CalculateLayoutConfidence(winningScore, opposingScore, totalScore);

            // 应用布局决策
            result.IsVerticalLayout = isVerticalLayout;
            result.LayoutConfidence = layoutConfidence;

            System.Diagnostics.Debug.WriteLine($"\n最终布局得分 - 竖排: {verticalLayoutScore}, 横排: {horizontalLayoutScore}");
            System.Diagnostics.Debug.WriteLine($"判定结果: {(isVerticalLayout ? "竖排" : "横排")}, 置信度: {layoutConfidence}");

            // 特殊布局处理 - 处理特殊布局场景
            ApplySpecialLayoutTypeRules(evidence, result);

            // 增强置信度 - 基于特征一致性进一步调整置信度
            AdjustConfidenceBasedOnConsistency(result, evidence);
            
            System.Diagnostics.Debug.WriteLine($"最终布局结果(特殊规则后): {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
        }

        /// <summary>
        /// 根据布局类型和方向的一致性调整置信度
        /// </summary>
        /// <param name="result">方向检测结果</param>
        /// <param name="evidence">方向证据</param>
        private void AdjustConfidenceBasedOnConsistency(TextDirectionResult result, DirectionEvidence evidence)
        {
            System.Diagnostics.Debug.WriteLine("\n=== 一致性调整开始 ===");
            System.Diagnostics.Debug.WriteLine($"调整前 - 布局: {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
            System.Diagnostics.Debug.WriteLine($"水平方向: {result.HorizontalDirection}, 置信度: {result.HorizontalConfidence}");
            System.Diagnostics.Debug.WriteLine($"垂直方向: {result.VerticalDirection}, 置信度: {result.VerticalConfidence}");
            
            // 检查布局类型和方向是否一致
            bool isConsistent = false;

            // 增强一致性检查，考虑更多情况
            if (!result.IsVerticalLayout)
            {
                // 横排布局一致性检查
                if (result.HorizontalDirection == TextFlowDirection.LeftToRight ||
                    result.HorizontalDirection == TextFlowDirection.RightToLeft)
                {
                    isConsistent = true;

                    // 计算一致性得分 - 基于证据强度
                    int consistencyScore = 0;

                    // 形状特征支持
                    if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
                        consistencyScore += 2;

                    // 行列结构支持
                    if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns)
                        consistencyScore += 2;

                    // 边界变异支持
                    if ((result.HorizontalDirection == TextFlowDirection.LeftToRight &&
                         evidence.LeftEdgeVariance < evidence.RightEdgeVariance) ||
                        (result.HorizontalDirection == TextFlowDirection.RightToLeft &&
                         evidence.RightEdgeVariance < evidence.LeftEdgeVariance))
                    {
                        consistencyScore += 1;
                    }

                    // 应用一致性增强
                    int consistencyBonus = Math.Min(15, consistencyScore * 3);
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyBonus);
                    result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + consistencyBonus / 2);

                    System.Diagnostics.Debug.WriteLine($"横排布局与水平方向一致性检查: 得分={consistencyScore}, 增加置信度 +{consistencyBonus}");
                }
            }
            else // 竖排布局
            {
                // 竖排布局一致性检查 - 与横排类似但针对垂直方向
                if (result.VerticalDirection == TextFlowDirection.TopToBottom ||
                    result.VerticalDirection == TextFlowDirection.BottomToTop)
                {
                    isConsistent = true;

                    // 计算一致性得分
                    int consistencyScore = 0;

                    // 形状特征支持 - 降低阈值从1.5到1.2
                    if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.2)
                        consistencyScore += 3; // 增加得分

                    // 增强对极端竖排情况的检测
                    int extremeVerticalCount = 0;
                    int highVerticalCount = 0;
                    
                    foreach (var cell in this.cells)
                    {
                        if (cell?.location != null)
                        {
                            double ratio = cell.location.height / (double)cell.location.width;
                            if (ratio > 8.0) // 超极端竖排
                            {
                                extremeVerticalCount++;
                            }
                            else if (ratio > 5.0) // 高竖排
                            {
                                highVerticalCount++;
                            }
                        }
                    }
                    
                    System.Diagnostics.Debug.WriteLine($"极端竖排文本块: {extremeVerticalCount}, 高竖排文本块: {highVerticalCount}");
                    
                    // 如果存在多个极端竖排文本块，增加一致性得分
                    if (extremeVerticalCount >= 2) // 降低阈值从3到2
                    {
                        // 增加更高的得分
                        consistencyScore += 5;
                        System.Diagnostics.Debug.WriteLine($"检测到多个极端竖排文本块({extremeVerticalCount}个)，增加一致性得分 +5");
                    }
                    else if (extremeVerticalCount >= 1 && highVerticalCount >= 1) // 添加新条件
                    {
                        // 即使只有1个极端竖排，只要还有高竖排文本，也增加得分
                        consistencyScore += 3;
                        System.Diagnostics.Debug.WriteLine($"检测到1个极端竖排和{highVerticalCount}个高竖排文本块，增加一致性得分 +3");
                    }
                    else if (highVerticalCount >= 3) // 添加仅高竖排文本的判断
                    {
                        consistencyScore += 2;
                        System.Diagnostics.Debug.WriteLine($"检测到多个高竖排文本块({highVerticalCount}个)，增加一致性得分 +2");
                    }

                    // 行列结构支持 - 降低列数要求
                    if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows)
                        consistencyScore += 2;
                    else if (evidence.VerticalAlignedColumns >= 2) // 添加绝对列数判断
                        consistencyScore += 3;

                    // 边界变异支持
                    if ((result.VerticalDirection == TextFlowDirection.TopToBottom &&
                         evidence.TopEdgeVariance < evidence.BottomEdgeVariance) ||
                        (result.VerticalDirection == TextFlowDirection.BottomToTop &&
                         evidence.BottomEdgeVariance < evidence.TopEdgeVariance))
                    {
                        consistencyScore += 1;
                    }

                    // 分析行内文本宽度一致性 - 为中文竖排诗文特殊优化
                    if (extremeVerticalCount + highVerticalCount >= 2)
                    {
                        var verticalBlocks = this.cells
                            .Where(c => c?.location != null && c.location.height / (double)c.location.width > 5.0)
                            .ToList();
                            
                        if (verticalBlocks.Count >= 2)
                        {
                            // 计算宽度的标准差
                            var widths = verticalBlocks.Select(c => c.location.width).ToList();
                            double avgWidth = widths.Average();
                            double widthStdDev = CalculateStandardDeviation(widths);
                            double widthCV = widthStdDev / avgWidth; // 变异系数
                            
                            System.Diagnostics.Debug.WriteLine($"竖排文本块宽度分析 - 平均值: {avgWidth:F2}, 标准差: {widthStdDev:F2}, 变异系数: {widthCV:F2}");
                            
                            // 如果宽度非常一致（表明是规整排版），增加一致性得分
                            if (widthCV < 0.15)
                            {
                                consistencyScore += 4;
                                System.Diagnostics.Debug.WriteLine($"竖排文本块宽度非常一致，很可能是规整排版，增加一致性得分 +4");
                            }
                            else if (widthCV < 0.25)
                            {
                                consistencyScore += 2;
                                System.Diagnostics.Debug.WriteLine($"竖排文本块宽度较一致，增加一致性得分 +2");
                            }
                        }
                    }

                    // 应用一致性增强 - 增加竖排布局的加权得分
                    int consistencyBonus = Math.Min(20, consistencyScore * 3); // 增加最大值和乘数
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyBonus);
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + consistencyBonus / 2);

                    System.Diagnostics.Debug.WriteLine($"竖排布局与垂直方向一致性检查: 得分={consistencyScore}, 增加置信度 +{consistencyBonus}");
                }
            }

            // 处理不一致情况
            if (!isConsistent)
            {
                // 检查不一致程度
                int primaryConfidence = result.IsVerticalLayout ?
                    result.VerticalConfidence : result.HorizontalConfidence;

                // 如果主方向置信度高但布局置信度低，调整布局以匹配主方向
                if (primaryConfidence > result.LayoutConfidence + 25)
                {
                    // 重新评估布局方向
                    if (result.HorizontalConfidence > result.VerticalConfidence + 20)
                    {
                        // 水平方向明显更可信
                        result.IsVerticalLayout = false;
                        result.LayoutConfidence = (result.LayoutConfidence + result.HorizontalConfidence) / 2;
                        System.Diagnostics.Debug.WriteLine($"方向不一致修正: 水平方向置信度明显更高，调整为横排布局");
                    }
                    else if (result.VerticalConfidence > result.HorizontalConfidence + 20)
                    {
                        // 垂直方向明显更可信
                        result.IsVerticalLayout = true;
                        result.LayoutConfidence = (result.LayoutConfidence + result.VerticalConfidence) / 2;
                        System.Diagnostics.Debug.WriteLine($"方向不一致修正: 垂直方向置信度明显更高，调整为竖排布局");
                    }
                }
            }

            // 特殊情况处理：竖排布局中的垂直方向检查 - 基于统计特征
            if (result.IsVerticalLayout)
            {
                // 1. 基于方向证据评估垂直方向
                if (evidence.TopToBottomCount > evidence.BottomToTopCount * 1.5)
                {
                    // 有明显的从上到下的证据
                    if (result.VerticalDirection != TextFlowDirection.TopToBottom)
                    {
                        // 修正垂直方向
                        result.VerticalDirection = TextFlowDirection.TopToBottom;
                        result.VerticalConfidence = Math.Max(70, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"竖排布局中检测到强烈的从上到下证据，修正垂直方向为TopToBottom");
                    }
                }
                else if (evidence.BottomToTopCount > evidence.TopToBottomCount * 1.5)
                {
                    // 有明显的从下到上的证据
                    if (result.VerticalDirection != TextFlowDirection.BottomToTop)
                    {
                        // 修正垂直方向
                        result.VerticalDirection = TextFlowDirection.BottomToTop;
                        result.VerticalConfidence = Math.Max(70, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"竖排布局中检测到强烈的从下到上证据，修正垂直方向为BottomToTop");
                    }
                }
                else
                {
                    // 2. 如果方向证据不明确，使用内容分布特征进行判断
                    TextLayoutFeatures features = null;
                    
                    // 判断features是否在局部作用域内可用
                    if (features != null)
                    {
                        if (features.ContentDensityTopHalf > features.ContentDensityBottomHalf * 1.2)
                        {
                            // 上半页内容密度更高，倾向于从上到下
                            result.VerticalDirection = TextFlowDirection.TopToBottom;
                            result.VerticalConfidence = Math.Max(65, result.VerticalConfidence);
                            System.Diagnostics.Debug.WriteLine($"基于上半页内容密度较高，设置垂直方向为TopToBottom");
                        }
                        else if (features.ContentDensityBottomHalf > features.ContentDensityTopHalf * 1.2)
                        {
                            // 下半页内容密度更高，倾向于从下到上
                            result.VerticalDirection = TextFlowDirection.BottomToTop;
                            result.VerticalConfidence = Math.Max(65, result.VerticalConfidence);
                            System.Diagnostics.Debug.WriteLine($"基于下半页内容密度较高，设置垂直方向为BottomToTop");
                        }
                        else if (evidence.VerticalAlignedColumns >= 2)
                        {
                            // 内容分布也不明确，但有多列，默认使用从上到下（通用性更好）
                            result.VerticalDirection = TextFlowDirection.TopToBottom;
                            result.VerticalConfidence = Math.Max(60, result.VerticalConfidence);
                            System.Diagnostics.Debug.WriteLine($"方向证据和内容分布均不明确，但有多列，默认使用TopToBottom");
                        }
                    }
                    else if (evidence.VerticalAlignedColumns >= 2)
                    {
                        // features不可用，但有多列，默认使用从上到下
                        result.VerticalDirection = TextFlowDirection.TopToBottom;
                        result.VerticalConfidence = Math.Max(60, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"垂直方向证据不明确，但检测到多列，默认使用TopToBottom");
                    }
                }
                
                // 3. 基于形状特征增强布局置信度
                if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
                {
                    // 竖排文本明显占优，增强竖排布局置信度
                    int verticalBonus = Math.Min(15, (evidence.VerticalTextCount - evidence.HorizontalTextCount) / 2);
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + verticalBonus);
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + verticalBonus / 2);
                    
                    System.Diagnostics.Debug.WriteLine($"竖排文本明显占优({evidence.VerticalTextCount}:{evidence.HorizontalTextCount})，增强竖排布局置信度 +{verticalBonus}");
                }
            }
            
            System.Diagnostics.Debug.WriteLine($"调整后 - 布局: {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
            System.Diagnostics.Debug.WriteLine($"水平方向: {result.HorizontalDirection}, 置信度: {result.HorizontalConfidence}");
            System.Diagnostics.Debug.WriteLine($"垂直方向: {result.VerticalDirection}, 置信度: {result.VerticalConfidence}");
            System.Diagnostics.Debug.WriteLine("=== 一致性调整结束 ===\n");
        }

        /// <summary>
        /// 计算布局置信度
        /// 基于得分差异比例和总分动态计算置信度，适用于任何得分分布
        /// </summary>
        /// <param name="winningScore">获胜方向的得分</param>
        /// <param name="opposingScore">相反方向的得分</param>
        /// <param name="totalScore">总得分</param>
        /// <returns>计算得出的置信度（55-95）</returns>
        private int CalculateLayoutConfidence(int winningScore, int opposingScore, int totalScore)
        {
            // 基础置信度基于分数差异比例
            // 使用相对差异比例而非绝对差异，使结果更通用
            double scoreDiff = winningScore - opposingScore;
            double diffRatio = scoreDiff / Math.Max(1, totalScore);

            // 映射到置信度范围：55-95
            // 下限保证最低可信度，上限避免过于武断
            int baseConfidence = 55 + (int)(diffRatio * 40);

            // 高总分提升置信度 - 更多证据支持时提升置信度
            // 使用平方根函数使增长合理
            int scoreBoost = (int)Math.Min(10, Math.Sqrt(totalScore));

            return Math.Min(95, baseConfidence + scoreBoost);
        }

        /// <summary>
        /// 应用特殊布局类型规则
        /// 处理各种特殊布局场景，增强系统的通用性
        /// </summary>
        /// <param name="evidence">收集的方向证据</param>
        /// <param name="result">文本方向结果，可能被调整</param>
        private void ApplySpecialLayoutTypeRules(DirectionEvidence evidence, TextDirectionResult result)
        {
            // 特殊布局规则用于处理一些极端或特殊情况
            // 这些规则基于具体的布局特征，适用范围广

            System.Diagnostics.Debug.WriteLine("\n--- 应用特殊布局规则 ---");

            // 1. 多列竖排文本布局
            // 垂直排列的文本列是非常明显的竖排特征
            if (evidence.VerticalAlignedColumns >= 2 &&
                evidence.VerticalTextCount > evidence.HorizontalTextCount &&
                evidence.HorizontalAlignedRows <= 1)
            {
                result.IsVerticalLayout = true;
                int oldConfidence = result.LayoutConfidence;
                // 提高置信度，但不超过95
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 10);

                System.Diagnostics.Debug.WriteLine($"特殊规则1: 检测到多列竖排布局 => 置信度从{oldConfidence}提升到{result.LayoutConfidence}");
            }

            // 2. 多行横排文本布局
            // 水平排列的文本行是非常明显的横排特征
            if (evidence.HorizontalAlignedRows >= 2 &&
                evidence.HorizontalTextCount > evidence.VerticalTextCount &&
                evidence.VerticalAlignedColumns <= 1)
            {
                result.IsVerticalLayout = false;
                int oldConfidence = result.LayoutConfidence;
                // 提高置信度，但不超过95
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 10);

                System.Diagnostics.Debug.WriteLine($"特殊规则2: 检测到多行横排布局 => 置信度从{oldConfidence}提升到{result.LayoutConfidence}");
            }

            // 5. 文本内容极少的情况
            if (evidence.HorizontalTextCount + evidence.VerticalTextCount < 5 &&
                result.LayoutConfidence > 60)
            {
                int oldConfidence = result.LayoutConfidence;
                // 文本内容很少时，降低我们的判断置信度
                result.LayoutConfidence = Math.Min(result.LayoutConfidence, 70);

                if (oldConfidence != result.LayoutConfidence)
                {
                    System.Diagnostics.Debug.WriteLine($"特殊规则5: 文本内容极少 => 降低置信度从{oldConfidence}到{result.LayoutConfidence}");
                }
            }
        }

        /// <summary>
        /// 统一的文本布局特征数据结构
        /// </summary>
        private class TextLayoutFeatures
        {
            // 文本块基本统计
            public int TotalTextBlocks { get; set; } = 0;
            public int ValidTextBlocks { get; set; } = 0;
            
            // 文本形状分布
            public List<double> HeightWidthRatios { get; set; } = new List<double>();
            public List<double> WidthHeightRatios { get; set; } = new List<double>();
            
            // 形状统计特征
            public double MedianHeightWidthRatio { get; set; } = 1.0;
            public double MedianWidthHeightRatio { get; set; } = 1.0;
            public double HeightWidthRatioVariance { get; set; } = 0;
            public double WidthHeightRatioVariance { get; set; } = 0;
            
            // 形状分布百分位数（用于动态阈值）
            public double VerticalRatio_P90 { get; set; } = 7.0; // 90百分位高宽比，替代固定阈值
            public double VerticalRatio_P75 { get; set; } = 5.0; // 75百分位高宽比
            public double VerticalRatio_P50 { get; set; } = 1.5; // 中位数高宽比
            
            public double HorizontalRatio_P90 { get; set; } = 7.0; // 90百分位宽高比
            public double HorizontalRatio_P75 { get; set; } = 5.0; // 75百分位宽高比
            public double HorizontalRatio_P50 { get; set; } = 1.5; // 中位数宽高比
            
            // 文本形状计数（基于动态阈值）
            public int VerticalTextCount { get; set; } = 0;   // 高>宽的文本块
            public int HorizontalTextCount { get; set; } = 0; // 宽>高的文本块
            public int SquareTextCount { get; set; } = 0;     // 近似正方形的文本块
            
            // 文本块大小统计
            public double MedianWidth { get; set; } = 0;
            public double MedianHeight { get; set; } = 0;
            public double WidthVariance { get; set; } = 0;
            public double HeightVariance { get; set; } = 0;
            
            // 边缘位置统计
            public List<double> LeftEdges { get; set; } = new List<double>();
            public List<double> RightEdges { get; set; } = new List<double>();
            public List<double> TopEdges { get; set; } = new List<double>();
            public List<double> BottomEdges { get; set; } = new List<double>();
            
            // 边缘变异特征
            public double LeftEdgeVariance { get; set; } = 0;
            public double RightEdgeVariance { get; set; } = 0;
            public double TopEdgeVariance { get; set; } = 0;
            public double BottomEdgeVariance { get; set; } = 0;
            
            // 边缘变异比例（用于方向判断）
            public double LeftRightEdgeVarianceRatio { get; set; } = 1.0;
            public double TopBottomEdgeVarianceRatio { get; set; } = 1.0;
            
            // 行列结构特征
            public int HorizontalRowCount { get; set; } = 0;
            public int VerticalColumnCount { get; set; } = 0;
            public List<List<TextCellInfo>> HorizontalRows { get; set; } = new List<List<TextCellInfo>>();
            public List<List<TextCellInfo>> VerticalColumns { get; set; } = new List<List<TextCellInfo>>();
            
            // 行列间距统计
            public List<double> RowGaps { get; set; } = new List<double>();
            public List<double> ColumnGaps { get; set; } = new List<double>();
            public double MedianRowGap { get; set; } = 0;
            public double MedianColumnGap { get; set; } = 0;
            public double RowGapVariance { get; set; } = 0;
            public double ColumnGapVariance { get; set; } = 0;
            
            // 行列规律性（变异系数，越小越规律）
            public double RowGapRegularity { get; set; } = 1.0;
            public double ColumnGapRegularity { get; set; } = 1.0;
            
            // 对齐特征
            public int LeftAlignedCount { get; set; } = 0;
            public int RightAlignedCount { get; set; } = 0;
            public int TopAlignedCount { get; set; } = 0;
            public int BottomAlignedCount { get; set; } = 0;
            
            // 段落特征
            public int ParagraphCount { get; set; } = 1;
            public double ParagraphGapThreshold { get; set; } = 0;
            public List<double> ParagraphGaps { get; set; } = new List<double>();
            
            // 内容分布特征
            public double ContentDensityLeftHalf { get; set; } = 0; // 左半页内容密度
            public double ContentDensityRightHalf { get; set; } = 0; // 右半页内容密度
            public double ContentDensityTopHalf { get; set; } = 0;   // 上半页内容密度
            public double ContentDensityBottomHalf { get; set; } = 0; // 下半页内容密度
            
            // 页面范围
            public double PageLeft { get; set; } = 0;
            public double PageRight { get; set; } = 0;
            public double PageTop { get; set; } = 0;
            public double PageBottom { get; set; } = 0;
            public double PageWidth { get; set; } = 0;
            public double PageHeight { get; set; } = 0;
            public double PageCenter_X { get; set; } = 0;
            public double PageCenter_Y { get; set; } = 0;
            
            // 特征关系
            public double RowColumnRatio { get; set; } = 1.0;
            public double VerticalHorizontalRatio { get; set; } = 1.0;
            public double LeftAlignmentRatio { get; set; } = 0.5;
            public double TopAlignmentRatio { get; set; } = 0.5;
            
            // 方向证据 - 新增字段
            public int LeftToRightEvidence { get; set; } = 0;
            public int RightToLeftEvidence { get; set; } = 0;
            public int TopToBottomEvidence { get; set; } = 0;
            public int BottomToTopEvidence { get; set; } = 0;
        }

        /// <summary>
        /// 方向证据数据结构
        /// </summary>
        private class DirectionEvidence
        {
            // 通用布局特征
            public int LeftToRightCount { get; set; } = 0;
            public int RightToLeftCount { get; set; } = 0;
            public int TopToBottomCount { get; set; } = 0;
            public int BottomToTopCount { get; set; } = 0;

            // 文本特征
            public int HorizontalTextCount { get; set; } = 0;  // 宽>高的文本块数量
            public int VerticalTextCount { get; set; } = 0;    // 高>宽的文本块数量

            // 段落特征
            public int ParagraphCount { get; set; } = 0;       // 识别出的段落数量

            // 布局统计
            public int HorizontalAlignedRows { get; set; } = 0;  // 水平排列的行数
            public int VerticalAlignedColumns { get; set; } = 0; // 垂直排列的列数

            // 对齐特征
            public int LeftAlignedCount { get; set; } = 0;     // 左对齐数量
            public int RightAlignedCount { get; set; } = 0;    // 右对齐数量
            public int TopAlignedCount { get; set; } = 0;      // 顶部对齐数量 
            public int BottomAlignedCount { get; set; } = 0;   // 底部对齐数量

            // 对齐差异特征
            public double LeftEdgeVariance { get; set; } = 0;  // 左边界变异度
            public double RightEdgeVariance { get; set; } = 0; // 右边界变异度
            public double TopEdgeVariance { get; set; } = 0;   // 顶部边界变异度
            public double BottomEdgeVariance { get; set; } = 0;// 底部边界变异度

            // 文本布局特征
            public bool IsSequentialParagraphs { get; set; } = false;  // 是否为连续段落布局
            public double LeftAlignmentRatio { get; set; } = 0;        // 左对齐比例
        }

        /// <summary>
        /// 将相似的值分组
        /// </summary>
        private List<List<double>> GroupSimilarValues(List<double> values, double threshold)
        {
            var groups = new List<List<double>>();

            foreach (var value in values)
            {
                bool addedToExisting = false;

                foreach (var group in groups)
                {
                    if (Math.Abs(group[0] - value) <= threshold)
                    {
                        group.Add(value);
                        addedToExisting = true;
                        break;
                    }
                }

                if (!addedToExisting)
                {
                    groups.Add(new List<double> { value });
                }
            }

            return groups;
        }

        /// <summary>
        /// 综合分析方向证据，确定最终方向
        /// 使用得分累加系统来判断方向，适用于各种语言和文档类型
        /// </summary>
        /// <param name="evidence">收集的方向证据</param>
        /// <param name="result">输出的方向结果</param>
        /// <param name="cells">文本单元格列表</param>
        private void AnalyzeDirectionEvidence(DirectionEvidence evidence, TextDirectionResult result, List<TextCellInfo> cells)
        {
            // 使用得分累加系统，而非直接设置置信度
            // 这种方法更加通用，可以处理各种语言和排版方式

            // ====================== 水平方向分析 ======================
            // 水平方向得分系统，分别计算从左到右和从右到左的证据得分
            int leftToRightScore = 0;
            int rightToLeftScore = 0;
            
            // 计算基于文本块数量的基准权重 - 动态基准
            int baseWeight = Math.Max(1, Math.Min(5, cells.Count / 10));
            
            // 1.1 基础方向证据 - 基础权重
            leftToRightScore += evidence.LeftToRightCount;
            rightToLeftScore += evidence.RightToLeftCount;

            // 1.2 对齐特征分析 - 使用相对比例
            if (evidence.LeftAlignedCount > 0 || evidence.RightAlignedCount > 0)
            {
                double totalAligned = evidence.LeftAlignedCount + evidence.RightAlignedCount;
                if (totalAligned > 0)
                {
                    double leftAlignRatio = evidence.LeftAlignedCount / totalAligned;
                    double rightAlignRatio = evidence.RightAlignedCount / totalAligned;
                    
                    // 对齐比例差异阈值 - 动态计算
                    double alignDiffThreshold = Math.Max(0.15, 0.2 - cells.Count * 0.002);
                    
                    // 对齐比例差异超过阈值才有明显意义
                    if (leftAlignRatio > rightAlignRatio + alignDiffThreshold)
                    {
                        double strengthFactor = Math.Min(1.0, (leftAlignRatio - rightAlignRatio) / 0.5);
                        int weightedScore = (int)(Math.Min(evidence.LeftAlignedCount, cells.Count / 2) * strengthFactor);
                        leftToRightScore += weightedScore;
                    }
                    else if (rightAlignRatio > leftAlignRatio + alignDiffThreshold)
                    {
                        double strengthFactor = Math.Min(1.0, (rightAlignRatio - leftAlignRatio) / 0.5);
                        int weightedScore = (int)(Math.Min(evidence.RightAlignedCount, cells.Count / 2) * strengthFactor);
                        rightToLeftScore += weightedScore;
                    }
                }
            }

            // 1.3 边界变异特征分析 - 使用动态阈值
            if (evidence.LeftEdgeVariance > 0 && evidence.RightEdgeVariance > 0)
            {
                double edgeVarianceRatio = evidence.LeftEdgeVariance / evidence.RightEdgeVariance;
                
                // 边缘变异比率阈值 - 动态计算
                double lrLowThreshold = Math.Max(0.7, 0.7 - cells.Count * 0.001);
                double lrHighThreshold = Math.Min(1.4, 1.4 + cells.Count * 0.001);
                
                // 使用比率而非固定倍数判断
                if (edgeVarianceRatio < lrLowThreshold) // 左边界比右边界更整齐
                {
                    double strengthFactor = Math.Min(1.0, (lrLowThreshold - edgeVarianceRatio) / 0.4);
                    leftToRightScore += (int)(baseWeight * 3 * strengthFactor);
                }
                else if (edgeVarianceRatio > lrHighThreshold) // 右边界比左边界更整齐
                {
                    double strengthFactor = Math.Min(1.0, (edgeVarianceRatio - lrHighThreshold) / 0.6);
                    rightToLeftScore += (int)(baseWeight * 3 * strengthFactor);
                }
            }

            // 1.4 形状特征分析 - 使用相对权重
            // 横向文本块比例 - 考虑文本块总数
            if (evidence.HorizontalTextCount > 0 || evidence.VerticalTextCount > 0)
            {
                double totalTextCount = evidence.HorizontalTextCount + evidence.VerticalTextCount;
                if (totalTextCount > 0)
                {
                    double horizontalRatio = evidence.HorizontalTextCount / totalTextCount;
                    
                    // 横向文本占比越高，越可能从左到右
                    if (horizontalRatio > 0.6 && evidence.HorizontalTextCount >= 3)
                    {
                        double strengthFactor = Math.Min(1.0, (horizontalRatio - 0.6) / 0.4);
                        int weightedScore = (int)(baseWeight * 5 * strengthFactor);
                        leftToRightScore += weightedScore;
                    }
                }
            }

            // 1.5 行列结构分析 - 使用相对权重
            // 行数与列数比较
            if (evidence.HorizontalAlignedRows > 0 || evidence.VerticalAlignedColumns > 0)
            {
                double totalRowColCount = evidence.HorizontalAlignedRows + evidence.VerticalAlignedColumns;
                if (totalRowColCount > 0)
                {
                    double rowRatio = evidence.HorizontalAlignedRows / totalRowColCount;
                    
                    // 行占比越高，越可能从左到右
                    if (rowRatio > 0.6 && evidence.HorizontalAlignedRows >= 2)
                    {
                        double strengthFactor = Math.Min(1.0, (rowRatio - 0.6) / 0.4);
                        int weightedScore = (int)(baseWeight * 4 * strengthFactor);
                        leftToRightScore += weightedScore;
                    }
                }
            }

            // 1.6 段落特征分析
            if (evidence.ParagraphCount >= 2)
            {
                // 多段落文本通常是从左到右阅读的
                int paragraphBonus = Math.Min(10, evidence.ParagraphCount * 2);
                leftToRightScore += paragraphBonus;
            }

            // 1.7 内容密度分析
            // 左右内容密度比较，基于统计特征而非语言假设
            if (evidence.LeftEdgeVariance > 0 && evidence.RightEdgeVariance > 0)
            {
                // 左边界变异小于右边界变异，通常表示从左到右阅读
                if (evidence.LeftEdgeVariance < evidence.RightEdgeVariance * 0.8)
                {
                    leftToRightScore += baseWeight * 2;
                }
                // 右边界变异小于左边界变异，通常表示从右到左阅读
                else if (evidence.RightEdgeVariance < evidence.LeftEdgeVariance * 0.8)
                {
                    rightToLeftScore += baseWeight * 2;
                }
            }

            // ====================== 垂直方向分析 ======================
            // 垂直方向得分系统，分别计算从上到下和从下到上的证据得分
            int topToBottomScore = 0;
            int bottomToTopScore = 0;

            // 2.1 基础方向证据 - 基础权重
            topToBottomScore += evidence.TopToBottomCount;
            bottomToTopScore += evidence.BottomToTopCount;

            // 2.2 对齐特征分析 - 使用相对比例
            if (evidence.TopAlignedCount > 0 || evidence.BottomAlignedCount > 0)
            {
                double totalAligned = evidence.TopAlignedCount + evidence.BottomAlignedCount;
                if (totalAligned > 0)
                {
                    double topAlignRatio = evidence.TopAlignedCount / totalAligned;
                    double bottomAlignRatio = evidence.BottomAlignedCount / totalAligned;
                    
                    // 对齐比例差异阈值 - 动态计算
                    double alignDiffThreshold = Math.Max(0.15, 0.2 - cells.Count * 0.002);
                    
                    // 对齐比例差异超过阈值才有明显意义
                    if (topAlignRatio > bottomAlignRatio + alignDiffThreshold)
                    {
                        double strengthFactor = Math.Min(1.0, (topAlignRatio - bottomAlignRatio) / 0.5);
                        int weightedScore = (int)(Math.Min(evidence.TopAlignedCount, cells.Count / 2) * strengthFactor);
                        topToBottomScore += weightedScore;
                    }
                    else if (bottomAlignRatio > topAlignRatio + alignDiffThreshold)
                    {
                        double strengthFactor = Math.Min(1.0, (bottomAlignRatio - topAlignRatio) / 0.5);
                        int weightedScore = (int)(Math.Min(evidence.BottomAlignedCount, cells.Count / 2) * strengthFactor);
                        bottomToTopScore += weightedScore;
                    }
                }
            }

            // 2.3 边界变异特征分析 - 使用动态阈值
            if (evidence.TopEdgeVariance > 0 && evidence.BottomEdgeVariance > 0)
            {
                double edgeVarianceRatio = evidence.TopEdgeVariance / evidence.BottomEdgeVariance;
                
                // 边缘变异比率阈值 - 动态计算
                double tbLowThreshold = Math.Max(0.7, 0.7 - cells.Count * 0.001);
                double tbHighThreshold = Math.Min(1.4, 1.4 + cells.Count * 0.001);
                
                // 使用比率而非固定倍数判断
                if (edgeVarianceRatio < tbLowThreshold) // 上边界比下边界更整齐
                {
                    double strengthFactor = Math.Min(1.0, (tbLowThreshold - edgeVarianceRatio) / 0.4);
                    topToBottomScore += (int)(baseWeight * 3 * strengthFactor);
                }
                else if (edgeVarianceRatio > tbHighThreshold) // 下边界比上边界更整齐
                {
                    double strengthFactor = Math.Min(1.0, (edgeVarianceRatio - tbHighThreshold) / 0.6);
                    bottomToTopScore += (int)(baseWeight * 3 * strengthFactor);
                }
            }

            // 2.4 形状特征分析 - 使用相对权重
            // 竖向文本块比例 - 考虑文本块总数
            if (evidence.HorizontalTextCount > 0 || evidence.VerticalTextCount > 0)
            {
                double totalTextCount = evidence.HorizontalTextCount + evidence.VerticalTextCount;
                if (totalTextCount > 0)
                {
                    double verticalRatio = evidence.VerticalTextCount / totalTextCount;
                    
                    // 竖向文本占比越高，越可能从上到下
                    if (verticalRatio > 0.6 && evidence.VerticalTextCount >= 3)
                    {
                        double strengthFactor = Math.Min(1.0, (verticalRatio - 0.6) / 0.4);
                        int weightedScore = (int)(baseWeight * 5 * strengthFactor);
                        topToBottomScore += weightedScore;
                    }
                }
            }

            // 2.5 行列结构分析 - 使用相对权重
            // 行数与列数比较
            if (evidence.HorizontalAlignedRows > 0 || evidence.VerticalAlignedColumns > 0)
            {
                double totalRowColCount = evidence.HorizontalAlignedRows + evidence.VerticalAlignedColumns;
                if (totalRowColCount > 0)
                {
                    double colRatio = evidence.VerticalAlignedColumns / totalRowColCount;
                    
                    // 列占比越高，越可能从上到下
                    if (colRatio > 0.6 && evidence.VerticalAlignedColumns >= 2)
                    {
                        double strengthFactor = Math.Min(1.0, (colRatio - 0.6) / 0.4);
                        int weightedScore = (int)(baseWeight * 4 * strengthFactor);
                        topToBottomScore += weightedScore;
                    }
                }
            }

            // 2.6 内容密度分析
            // 上下内容密度比较，基于统计特征而非语言假设
            if (evidence.TopEdgeVariance > 0 && evidence.BottomEdgeVariance > 0)
            {
                // 上边界变异小于下边界变异，通常表示从上到下阅读
                if (evidence.TopEdgeVariance < evidence.BottomEdgeVariance * 0.8)
                {
                    topToBottomScore += baseWeight * 2;
                }
                // 下边界变异小于上边界变异，通常表示从下到上阅读
                else if (evidence.BottomEdgeVariance < evidence.TopEdgeVariance * 0.8)
                {
                    bottomToTopScore += baseWeight * 2;
                }
            }

            // ====================== 方向决策 ======================
            // 水平方向决策
            TextFlowDirection horizontalDirection = TextFlowDirection.LeftToRight; // 默认从左到右
            int horizontalConfidence = 0;

            // 如果有足够的证据，确定水平方向
            if (leftToRightScore > 0 || rightToLeftScore > 0)
            {
                // 计算总分和差异
                int totalHorizontalScore = leftToRightScore + rightToLeftScore;
                int horizontalDiff = Math.Abs(leftToRightScore - rightToLeftScore);
                
                // 计算置信度 - 基于分数差异和总分
                horizontalConfidence = CalculateConfidenceFromScore(
                    Math.Max(leftToRightScore, rightToLeftScore),
                    Math.Min(leftToRightScore, rightToLeftScore),
                    totalHorizontalScore);
                
                // 确定方向 - 得分高的方向胜出
                if (rightToLeftScore > leftToRightScore)
                {
                    horizontalDirection = TextFlowDirection.RightToLeft;
                }
            }
            else
            {
                // 没有足够的证据，使用默认方向和低置信度
                horizontalConfidence = 60;
            }

            // 垂直方向决策
            TextFlowDirection verticalDirection = TextFlowDirection.TopToBottom; // 默认从上到下
            int verticalConfidence = 0;

            // 如果有足够的证据，确定垂直方向
            if (topToBottomScore > 0 || bottomToTopScore > 0)
            {
                // 计算总分和差异
                int totalVerticalScore = topToBottomScore + bottomToTopScore;
                int verticalDiff = Math.Abs(topToBottomScore - bottomToTopScore);
                
                // 计算置信度 - 基于分数差异和总分
                verticalConfidence = CalculateConfidenceFromScore(
                    Math.Max(topToBottomScore, bottomToTopScore),
                    Math.Min(topToBottomScore, bottomToTopScore),
                    totalVerticalScore);
                
                // 确定方向 - 得分高的方向胜出
                if (bottomToTopScore > topToBottomScore)
                {
                    verticalDirection = TextFlowDirection.BottomToTop;
                }
            }
            else
            {
                // 没有足够的证据，使用默认方向和低置信度
                verticalConfidence = 60;
            }

            // 设置结果
            result.HorizontalDirection = horizontalDirection;
            result.HorizontalConfidence = horizontalConfidence;
            result.VerticalDirection = verticalDirection;
            result.VerticalConfidence = verticalConfidence;
        }

        /// <summary>
        /// 计算置信度分数
        /// </summary>
        /// <param name="winningScore">获胜方向的得分</param>
        /// <param name="opposingScore">对立方向的得分</param>
        /// <param name="totalScore">总得分</param>
        /// <returns>计算出的置信度（55-95）</returns>
        private int CalculateConfidenceFromScore(int winningScore, int opposingScore, int totalScore)
        {
            // 基础置信度基于分数差异比例
            // 使用相对差异比例而非绝对差异，使结果更通用
            double scoreDiff = winningScore - opposingScore;
            double diffRatio = scoreDiff / Math.Max(1, totalScore);

            // 映射到置信度范围：55-95
            // 下限保证最低可信度，上限避免过于武断
            int baseConfidence = 55 + (int)(diffRatio * 40);

            // 高总分提升置信度 - 更多证据支持时提升置信度
            // 使用平方根函数使增长合理
            int scoreBoost = (int)Math.Min(10, Math.Sqrt(totalScore));

            return Math.Min(95, baseConfidence + scoreBoost);
        }

        /// <summary>
        /// 计算自适应分组阈值，基于文本块高宽比分布
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        private void CalculateAdaptiveThresholds(List<TextCellInfo> cells)
        {
            // 初始化默认阈值
            double defaultThresholdVertical = 25.0;
            double defaultThresholdHorizontal = 10.0;
            
            if (cells.Count < 2)
            {
                groupingThresholdVertical = defaultThresholdVertical;
                groupingThresholdHorizontal = defaultThresholdHorizontal;
                return;
            }

            // 计算比例分布
            var ratios = cells
                .Where(c => c?.location != null)
                .Select(c => Math.Max(
                    c.location.height / (double)c.location.width,
                    c.location.width / (double)c.location.height))
                .OrderBy(r => r)
                .ToList();

            if (ratios.Count == 0)
            {
                groupingThresholdVertical = defaultThresholdVertical;
                groupingThresholdHorizontal = defaultThresholdHorizontal;
                return;
            }

            // 检查是否存在极端比例（高于7:1）
            bool hasExtremeRatio = ratios.Any(r => r > 7.0);
            // 检查是否存在超极端比例（高于10:1）
            bool hasSuperExtremeRatio = ratios.Any(r => r > 10.0);

            // 分析比例分布，计算高比例文本块的百分比
            int extremeRatioCount = ratios.Count(r => r > 5.0);
            double extremeRatioPercentage = (double)extremeRatioCount / ratios.Count;

            // 计算自适应阈值
            double adaptiveVerticalThreshold;
            double adaptiveHorizontalThreshold;
            
            // 根据极端比例的存在情况，自适应调整阈值
            if (hasSuperExtremeRatio)
            {
                System.Diagnostics.Debug.WriteLine("检测到超极端竖排比例(>10:1)，使用特殊阈值");
                adaptiveVerticalThreshold = defaultThresholdVertical * 2.0; // 更大的阈值
                adaptiveHorizontalThreshold = defaultThresholdHorizontal * 1.8; // 也调整横向阈值
            }
            else if (hasExtremeRatio)
            {
                System.Diagnostics.Debug.WriteLine("检测到极端比例(>7:1)，调整阈值");
                adaptiveVerticalThreshold = defaultThresholdVertical * 1.5; // 调整阈值
                adaptiveHorizontalThreshold = defaultThresholdHorizontal * 1.3;
            }
            else if (extremeRatioPercentage > 0.3) // 如果30%以上的文本块都有较高比例
            {
                adaptiveVerticalThreshold = defaultThresholdVertical * 1.2; 
                adaptiveHorizontalThreshold = defaultThresholdHorizontal * 1.2;
            }
            else
            {
                // 使用标准阈值
                adaptiveVerticalThreshold = defaultThresholdVertical;
                adaptiveHorizontalThreshold = defaultThresholdHorizontal;
            }

            // 安全检查 - 确保阈值在合理范围内
            groupingThresholdVertical = Math.Max(15.0, Math.Min(adaptiveVerticalThreshold, 100.0));
            groupingThresholdHorizontal = Math.Max(5.0, Math.Min(adaptiveHorizontalThreshold, 50.0));
            
            // 计算极端竖排比例的自适应阈值
            // 这个阈值用于判断哪些文本块是极端竖排的
            if (ratios.Count >= 5)
            {
                // 使用百分位数来判断极端比例
                // 找出前20%的比例值作为极端比例的起点
                int percentileIndex = Math.Max(0, (int)(ratios.Count * 0.8) - 1);
                double percentileValue = ratios[percentileIndex];
                
                // 取5和百分位数的较大值，确保阈值不会太低
                extremeVerticalRatioThreshold = Math.Max(5.0, percentileValue);
                
                System.Diagnostics.Debug.WriteLine($"自适应极端竖排比例阈值: {extremeVerticalRatioThreshold:F1}");
            }
            else
            {
                // 默认值
                extremeVerticalRatioThreshold = 7.0;
            }

            System.Diagnostics.Debug.WriteLine($"自适应阈值 - 垂直: {groupingThresholdVertical:F1}, 水平: {groupingThresholdHorizontal:F1}, 存在极端比例: {hasExtremeRatio}, 超极端比例: {hasSuperExtremeRatio}");
        }

        /// <summary>
        /// 计算标准差
        /// </summary>
        private double CalculateStandardDeviation(IEnumerable<double> values)
        {
            double avg = values.Average();
            double sumOfSquaresOfDifferences = values.Select(val => (val - avg) * (val - avg)).Sum();
            double variance = sumOfSquaresOfDifferences / values.Count();
            return Math.Sqrt(variance);
        }
        
        /// <summary>
        /// 提取统一的文本布局特征
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <returns>文本布局特征对象</returns>
        private TextLayoutFeatures ExtractTextFeatures(List<TextCellInfo> cells)
        {
            var features = new TextLayoutFeatures();
            
            // 过滤有效的文本块
            var validCells = cells.Where(c => 
                c.location.width >= 5 && 
                c.location.height >= 5 && 
                !string.IsNullOrWhiteSpace(c.words)).ToList();
            
            features.ValidTextBlocks = validCells.Count;
            
            if (validCells.Count < 2) return features;
            
            // 1. 提取形状特征
            ExtractShapeFeatures(validCells, features);
            
            // 2. 提取边缘特征
            ExtractEdgeFeatures(validCells, features);
            
            // 3. 提取行列结构特征
            ExtractRowColumnFeatures(validCells, features);
            
            // 4. 提取对齐特征
            ExtractAlignmentFeatures(validCells, features);
            
            // 5. 提取段落特征
            ExtractParagraphFeatures(validCells, features);
            
            // 6. 提取内容分布特征
            ExtractContentDistributionFeatures(validCells, features);
            
            // 7. 提取文本方向特征
            ExtractTextDirectionFeatures(validCells, features);
            
            // 8. 计算特征比率和关系
            CalculateFeatureRelationships(features);
            
            return features;
        }
        
        /// <summary>
        /// 提取文本方向特征 - 特别关注竖排文本的水平方向判断
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <param name="features">文本布局特征</param>
        private void ExtractTextDirectionFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            if (cells.Count < 3) return;
            
            // 检测是否为竖排文本
            bool isLikelyVerticalText = features.VerticalTextCount > features.HorizontalTextCount * 1.5;
            
            if (!isLikelyVerticalText) return;
            
            // 对于竖排文本，分析文本的排列方向
            
            // 1. 检查多列情况下的阅读顺序
            if (features.VerticalColumnCount >= 2)
            {
                // 按X坐标排序列
                var sortedColumns = features.VerticalColumns.OrderBy(col => 
                    col.Average(c => c.location.left)).ToList();
                
                // 检查首列和末列的文本密度
                if (sortedColumns.Count >= 2)
                {
                    var firstColumn = sortedColumns.First();
                    var lastColumn = sortedColumns.Last();
                    
                    double firstColumnDensity = firstColumn.Sum(c => c.location.width * c.location.height);
                    double lastColumnDensity = lastColumn.Sum(c => c.location.width * c.location.height);
                    
                    // 首列密度明显高于末列，可能是从左到右阅读
                    if (firstColumnDensity > lastColumnDensity * 1.3)
                    {
                        features.LeftToRightEvidence += 3;
                    }
                    // 末列密度明显高于首列，可能是从右到左阅读
                    else if (lastColumnDensity > firstColumnDensity * 1.3)
                    {
                        features.RightToLeftEvidence += 3;
                    }
                }
                
                // 2. 检查列间距的规律性
                var columnCenters = features.VerticalColumns.Select(col => 
                    col.Average(c => c.location.left + c.location.width / 2)).OrderBy(x => x).ToList();
                
                if (columnCenters.Count >= 3)
                {
                    // 计算列间距
                    List<double> columnGaps = new List<double>();
                    for (int i = 1; i < columnCenters.Count; i++)
                    {
                        columnGaps.Add(columnCenters[i] - columnCenters[i-1]);
                    }
                    
                    // 检查列间距是否从左到右递增（可能表示从右到左阅读）
                    bool increasingGaps = true;
                    for (int i = 1; i < columnGaps.Count; i++)
                    {
                        if (columnGaps[i] < columnGaps[i-1] * 0.8)
                        {
                            increasingGaps = false;
                            break;
                        }
                    }
                    
                    // 检查列间距是否从左到右递减（可能表示从左到右阅读）
                    bool decreasingGaps = true;
                    for (int i = 1; i < columnGaps.Count; i++)
                    {
                        if (columnGaps[i] > columnGaps[i-1] * 0.8)
                        {
                            decreasingGaps = false;
                            break;
                        }
                    }
                    
                    if (increasingGaps)
                    {
                        features.RightToLeftEvidence += 2;
                    }
                    else if (decreasingGaps)
                    {
                        features.LeftToRightEvidence += 2;
                    }
                }
            }
            
            // 3. 分析单个竖排文本块的内部特征
            var verticalTextBlocks = cells.Where(c => c.location.height > c.location.width * 2).ToList();
            if (verticalTextBlocks.Count >= 3)
            {
                // 检查字符分布 - 对于中日韩文字，可以通过分析Unicode范围判断
                int cjkCharCount = 0;
                int totalCharCount = 0;
                
                foreach (var cell in verticalTextBlocks)
                {
                    if (string.IsNullOrEmpty(cell.words)) continue;
                    
                    foreach (char c in cell.words)
                    {
                        totalCharCount++;
                        // 检查是否为中日韩文字（粗略判断）
                        if (c >= 0x4E00 && c <= 0x9FFF) // 基本汉字范围
                        {
                            cjkCharCount++;
                        }
                    }
                }
                
                // 如果大部分是中日韩文字，增加从右到左的证据
                // 但这只是一个统计特征，不是绝对规则
                if (totalCharCount > 0 && (double)cjkCharCount / totalCharCount > 0.7)
                {
                    // 仅增加轻微证据，避免过度依赖语言特征
                    features.RightToLeftEvidence += 1;
                }
            }
            
            // 4. 分析页面整体布局
            // 计算页面中心
            double pageWidth = cells.Max(c => c.location.left + c.location.width);
            double pageCenter = pageWidth / 2;
            
            // 计算文本块在页面左右两侧的分布
            int leftSideBlocks = 0;
            int rightSideBlocks = 0;
            
            foreach (var cell in verticalTextBlocks)
            {
                double cellCenter = cell.location.left + cell.location.width / 2;
                if (cellCenter < pageCenter)
                {
                    leftSideBlocks++;
                }
                else
                {
                    rightSideBlocks++;
                }
            }
            
            // 如果右侧文本块明显多于左侧，可能是从右到左阅读
            if (rightSideBlocks > leftSideBlocks * 1.5 && rightSideBlocks >= 3)
            {
                features.RightToLeftEvidence += 2;
            }
            // 如果左侧文本块明显多于右侧，可能是从左到右阅读
            else if (leftSideBlocks > rightSideBlocks * 1.5 && leftSideBlocks >= 3)
            {
                features.LeftToRightEvidence += 2;
            }
        }
        
        /// <summary>
        /// 计算特征之间的关系
        /// </summary>
        /// <param name="features">文本布局特征</param>
        private void CalculateFeatureRelationships(TextLayoutFeatures features)
        {
            // 计算边缘变异比率
            if (features.RightEdgeVariance > 0)
            {
                features.LeftRightEdgeVarianceRatio = features.LeftEdgeVariance / features.RightEdgeVariance;
            }
            
            if (features.BottomEdgeVariance > 0)
            {
                features.TopBottomEdgeVarianceRatio = features.TopEdgeVariance / features.BottomEdgeVariance;
            }
            
            // 计算行列比例
            if (features.VerticalColumnCount > 0)
            {
                features.RowColumnRatio = (double)features.HorizontalRowCount / features.VerticalColumnCount;
            }
            
            // 计算形状比例
            if (features.HorizontalTextCount > 0)
            {
                features.VerticalHorizontalRatio = (double)features.VerticalTextCount / features.HorizontalTextCount;
            }
            
            // 计算对齐比例
            double totalHorizontalAligned = features.LeftAlignedCount + features.RightAlignedCount;
            if (totalHorizontalAligned > 0)
            {
                features.LeftAlignmentRatio = features.LeftAlignedCount / totalHorizontalAligned;
            }
            
            double totalVerticalAligned = features.TopAlignedCount + features.BottomAlignedCount;
            if (totalVerticalAligned > 0)
            {
                features.TopAlignmentRatio = features.TopAlignedCount / totalVerticalAligned;
            }
        }

        /// <summary>
        /// 提取文本形状特征
        /// </summary>
        private void ExtractShapeFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 计算所有文本块的高宽比和宽高比
            foreach (var cell in cells)
            {
                double hwRatio = cell.location.height / (double)cell.location.width;
                double whRatio = cell.location.width / (double)cell.location.height;
                
                features.HeightWidthRatios.Add(hwRatio);
                features.WidthHeightRatios.Add(whRatio);
            }
            
            // 按高宽比排序，用于计算百分位数
            var sortedHWRatios = features.HeightWidthRatios.OrderBy(r => r).ToList();
            var sortedWHRatios = features.WidthHeightRatios.OrderBy(r => r).ToList();
            
            // 计算统计特征
            if (sortedHWRatios.Count > 0)
            {
                // 中位数
                int midIndex = sortedHWRatios.Count / 2;
                features.MedianHeightWidthRatio = sortedHWRatios[midIndex];
                features.MedianWidthHeightRatio = sortedWHRatios[midIndex];
                
                // 方差
                features.HeightWidthRatioVariance = CalculateVariance(sortedHWRatios);
                features.WidthHeightRatioVariance = CalculateVariance(sortedWHRatios);
                
                // 百分位数
                if (sortedHWRatios.Count >= 4)
                {
                    // 90百分位
                    int p90Index = (int)(sortedHWRatios.Count * 0.9);
                    features.VerticalRatio_P90 = sortedHWRatios[p90Index];
                    features.HorizontalRatio_P90 = sortedWHRatios[p90Index];
                    
                    // 75百分位
                    int p75Index = (int)(sortedHWRatios.Count * 0.75);
                    features.VerticalRatio_P75 = sortedHWRatios[p75Index];
                    features.HorizontalRatio_P75 = sortedWHRatios[p75Index];
                    
                    // 50百分位（中位数）
                    int p50Index = (int)(sortedHWRatios.Count * 0.5);
                    features.VerticalRatio_P50 = sortedHWRatios[p50Index];
                    features.HorizontalRatio_P50 = sortedWHRatios[p50Index];
                }
                else
                {
                    // 如果样本太少，使用合理的默认值
                    features.VerticalRatio_P90 = Math.Max(7.0, features.MedianHeightWidthRatio * 2);
                    features.VerticalRatio_P75 = Math.Max(5.0, features.MedianHeightWidthRatio * 1.5);
                    features.VerticalRatio_P50 = Math.Max(1.5, features.MedianHeightWidthRatio);
                    
                    features.HorizontalRatio_P90 = Math.Max(7.0, features.MedianWidthHeightRatio * 2);
                    features.HorizontalRatio_P75 = Math.Max(5.0, features.MedianWidthHeightRatio * 1.5);
                    features.HorizontalRatio_P50 = Math.Max(1.5, features.MedianWidthHeightRatio);
                }
            }
            
            // 基于动态阈值计算文本形状计数
            double verticalThreshold = Math.Max(1.2, features.VerticalRatio_P50 * 0.7); // 动态调整竖排阈值
            double horizontalThreshold = Math.Max(1.2, features.HorizontalRatio_P50 * 0.7); // 动态调整横排阈值
            
            foreach (var cell in cells)
            {
                double hwRatio = cell.location.height / (double)cell.location.width;
                double whRatio = cell.location.width / (double)cell.location.height;
                
                if (hwRatio >= verticalThreshold)
                {
                    features.VerticalTextCount++;
                }
                else if (whRatio >= horizontalThreshold)
                {
                    features.HorizontalTextCount++;
                }
                else
                {
                    features.SquareTextCount++;
                }
            }
            
            // 计算文本块大小统计
            var widths = cells.Select(c => (double)c.location.width).OrderBy(w => w).ToList();
            var heights = cells.Select(c => (double)c.location.height).OrderBy(h => h).ToList();
            
            if (widths.Count > 0 && heights.Count > 0)
            {
                features.MedianWidth = widths[widths.Count / 2];
                features.MedianHeight = heights[heights.Count / 2];
                features.WidthVariance = CalculateVariance(widths);
                features.HeightVariance = CalculateVariance(heights);
            }
        }
        
        /// <summary>
        /// 提取边缘特征
        /// </summary>
        private void ExtractEdgeFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 提取边缘坐标
            foreach (var cell in cells)
            {
                features.LeftEdges.Add(cell.location.left);
                features.RightEdges.Add(cell.location.left + cell.location.width);
                features.TopEdges.Add(cell.location.top);
                features.BottomEdges.Add(cell.location.top + cell.location.height);
            }
            
            // 计算边缘变异
            if (features.LeftEdges.Count > 1)
            {
                features.LeftEdgeVariance = CalculateVariance(features.LeftEdges);
                features.RightEdgeVariance = CalculateVariance(features.RightEdges);
                features.TopEdgeVariance = CalculateVariance(features.TopEdges);
                features.BottomEdgeVariance = CalculateVariance(features.BottomEdges);
                
                // 计算边缘变异比例（避免除零）
                features.LeftRightEdgeVarianceRatio = features.RightEdgeVariance > 0 ? 
                    features.LeftEdgeVariance / features.RightEdgeVariance : 1.0;
                    
                features.TopBottomEdgeVarianceRatio = features.BottomEdgeVariance > 0 ? 
                    features.TopEdgeVariance / features.BottomEdgeVariance : 1.0;
            }
        }
        
        /// <summary>
        /// 提取行列结构特征
        /// </summary>
        private void ExtractRowColumnFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 动态计算分组阈值
            double verticalGroupThreshold = this.groupingThresholdVertical;
            double horizontalGroupThreshold = this.groupingThresholdHorizontal;
            
            // 按水平位置分组形成列
            var columnGroups = cells
                .GroupBy(c => Math.Round(c.location.left / horizontalGroupThreshold) * horizontalGroupThreshold)
                .OrderBy(g => g.Key)
                .ToList();
                
            features.VerticalColumnCount = columnGroups.Count;
            
            // 存储列数据
            foreach (var column in columnGroups)
            {
                features.VerticalColumns.Add(column.ToList());
            }
            
            // 计算列间距
            if (columnGroups.Count >= 2)
            {
                for (int i = 1; i < columnGroups.Count; i++)
                {
                    double currentColCenter = columnGroups[i].Average(c => c.location.left + c.location.width / 2);
                    double prevColCenter = columnGroups[i-1].Average(c => c.location.left + c.location.width / 2);
                    double gap = currentColCenter - prevColCenter;
                    
                    if (gap > 0)
                    {
                        features.ColumnGaps.Add(gap);
                    }
                }
                
                if (features.ColumnGaps.Count > 0)
                {
                    features.MedianColumnGap = features.ColumnGaps.OrderBy(g => g).ElementAt(features.ColumnGaps.Count / 2);
                    features.ColumnGapVariance = CalculateVariance(features.ColumnGaps);
                    
                    // 计算规律性（变异系数：标准差/平均值）
                    double columnGapMean = features.ColumnGaps.Average();
                    if (columnGapMean > 0)
                    {
                        features.ColumnGapRegularity = Math.Sqrt(features.ColumnGapVariance) / columnGapMean;
                    }
                }
            }
            
            // 按垂直位置分组形成行
            var rowGroups = cells
                .GroupBy(c => Math.Round(c.location.top / verticalGroupThreshold) * verticalGroupThreshold)
                .OrderBy(g => g.Key)
                .ToList();
                
            features.HorizontalRowCount = rowGroups.Count;
            
            // 存储行数据
            foreach (var row in rowGroups)
            {
                features.HorizontalRows.Add(row.ToList());
            }
            
            // 计算行间距
            if (rowGroups.Count >= 2)
            {
                for (int i = 1; i < rowGroups.Count; i++)
                {
                    double currentRowCenter = rowGroups[i].Average(c => c.location.top + c.location.height / 2);
                    double prevRowCenter = rowGroups[i-1].Average(c => c.location.top + c.location.height / 2);
                    double gap = currentRowCenter - prevRowCenter;
                    
                    if (gap > 0)
                    {
                        features.RowGaps.Add(gap);
                    }
                }
                
                if (features.RowGaps.Count > 0)
                {
                    features.MedianRowGap = features.RowGaps.OrderBy(g => g).ElementAt(features.RowGaps.Count / 2);
                    features.RowGapVariance = CalculateVariance(features.RowGaps);
                    
                    // 计算规律性（变异系数）
                    double rowGapMean = features.RowGaps.Average();
                    if (rowGapMean > 0)
                    {
                        features.RowGapRegularity = Math.Sqrt(features.RowGapVariance) / rowGapMean;
                    }
                }
            }
        }
        
        /// <summary>
        /// 提取对齐特征
        /// </summary>
        private void ExtractAlignmentFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 动态确定对齐阈值
            double alignmentThreshold = Math.Min(10.0, features.MedianWidth * 0.1); // 默认10像素或宽度的10%
            
            // 统计左对齐
            var leftGroups = GroupSimilarValues(features.LeftEdges, alignmentThreshold);
            features.LeftAlignedCount = leftGroups.Count > 0 ? leftGroups.Max(g => g.Count) : 0;
            
            // 统计右对齐
            var rightGroups = GroupSimilarValues(features.RightEdges, alignmentThreshold);
            features.RightAlignedCount = rightGroups.Count > 0 ? rightGroups.Max(g => g.Count) : 0;
            
            // 统计顶部对齐
            var topGroups = GroupSimilarValues(features.TopEdges, alignmentThreshold);
            features.TopAlignedCount = topGroups.Count > 0 ? topGroups.Max(g => g.Count) : 0;
            
            // 统计底部对齐
            var bottomGroups = GroupSimilarValues(features.BottomEdges, alignmentThreshold);
            features.BottomAlignedCount = bottomGroups.Count > 0 ? bottomGroups.Max(g => g.Count) : 0;
        }
        
        /// <summary>
        /// 提取段落特征
        /// </summary>
        private void ExtractParagraphFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            if (cells.Count < 3)
            {
                features.ParagraphCount = 1;
                return;
            }
            
            // 按垂直位置排序
            var sortedCells = cells.OrderBy(c => c.location.top).ToList();
            
            // 计算段落间距阈值 - 动态计算
            features.ParagraphGapThreshold = features.MedianHeight * 1.5; // 默认为中位数高度的1.5倍
            
            // 统计段落数量
            int paragraphCount = 1;
            
            for (int i = 1; i < sortedCells.Count; i++)
            {
                double gap = sortedCells[i].location.top - (sortedCells[i-1].location.top + sortedCells[i-1].location.height);
                
                if (gap > features.ParagraphGapThreshold)
                {
                    paragraphCount++;
                    features.ParagraphGaps.Add(gap);
                }
            }
            
            features.ParagraphCount = paragraphCount;
        }
        
        /// <summary>
        /// 提取内容分布特征
        /// </summary>
        private void ExtractContentDistributionFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 计算内容面积和位置
            double totalArea = 0;
            double leftHalfArea = 0;
            double rightHalfArea = 0;
            double topHalfArea = 0;
            double bottomHalfArea = 0;
            
            foreach (var cell in cells)
            {
                double area = cell.location.width * cell.location.height;
                totalArea += area;
                
                // 计算中心点
                double centerX = cell.location.left + cell.location.width / 2;
                double centerY = cell.location.top + cell.location.height / 2;
                
                // 按位置分配面积
                if (centerX < features.PageCenter_X)
                {
                    leftHalfArea += area;
                }
                else
                {
                    rightHalfArea += area;
                }
                
                if (centerY < features.PageCenter_Y)
                {
                    topHalfArea += area;
                }
                else
                {
                    bottomHalfArea += area;
                }
            }
            
            // 计算内容密度比例
            if (totalArea > 0)
            {
                features.ContentDensityLeftHalf = leftHalfArea / totalArea;
                features.ContentDensityRightHalf = rightHalfArea / totalArea;
                features.ContentDensityTopHalf = topHalfArea / totalArea;
                features.ContentDensityBottomHalf = bottomHalfArea / totalArea;
            }
        }

        /// <summary>
        /// 计算方差
        /// </summary>
        private double CalculateVariance(List<double> values)
        {
            if (values.Count <= 1) return 0;

            double avg = values.Average();
            double sumOfSquaredDifferences = values.Sum(x => (x - avg) * (x - avg));
            return sumOfSquaredDifferences / values.Count;
        }
    }

    /// <summary>
    /// 文本流方向枚举
    /// </summary>
    public enum TextFlowDirection
    {
        /// <summary>从左到右（常见于拉丁语系、中文横排等）</summary>
        LeftToRight,
        /// <summary>从右到左（常见于阿拉伯语、希伯来语等）</summary>
        RightToLeft,
        /// <summary>从上到下（常见于中文竖排等）</summary>
        TopToBottom,
        /// <summary>从下到上（常见于车道提示语等）</summary>
        BottomToTop,
        /// <summary>混合方向（多种方向混合）</summary>
        Mixed
    }
}