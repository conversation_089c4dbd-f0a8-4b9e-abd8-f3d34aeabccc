﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace OCRTools
{
    public abstract class ImageEffect
    {
        [DefaultValue(true), Browsable(false)]
        public bool Enabled { get; set; }

        protected ImageEffect()
        {
            Enabled = true;
        }

        public abstract Bitmap Apply(Bitmap bmp);
    }
    public class ImageEffectPreset
    {
        public string Name { get; set; } = "";

        public List<ImageEffect> Effects { get; set; } = new List<ImageEffect>();

        public Bitmap ApplyEffects(Bitmap bmp)
        {
            Bitmap result = (Bitmap)bmp.Clone();
            result.SetResolution(96f, 96f);

            if (Effects != null && Effects.Count > 0)
            {
                foreach (ImageEffect effect in Effects.Where(x => x.Enabled))
                {
                    result = effect.Apply(result);

                    if (result == null)
                    {
                        break;
                    }
                }
            }

            return result;
        }

        public override string ToString()
        {
            if (!string.IsNullOrEmpty(Name))
            {
                return Name;
            }

            return "Name";
        }

        public static ImageEffectPreset GetDefaultPreset()
        {
            ImageEffectPreset preset = new ImageEffectPreset();

            Canvas canvas = new Canvas();
            canvas.Margin = new Padding(0, 0, 0, 30);
            preset.Effects.Add(canvas);

            DrawText text = new DrawText();
            text.Offset = new Point(0, 0);
            text.UseGradient = true;
            preset.Effects.Add(text);

            return preset;
        }
    }
}
