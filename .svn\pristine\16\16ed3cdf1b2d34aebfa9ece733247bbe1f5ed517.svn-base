﻿using ShareX.ScreenCaptureLib;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FormTool : Form
    {
        private static readonly int frameWidth = 250; //背景宽
        private static readonly int frameHeight = 100; //背景高
        private readonly Font _baseFont = new Font("微软雅黑", 32, GraphicsUnit.Pixel);

        private int _frameX = Screen.PrimaryScreen.WorkingArea.Size.Width - frameWidth;
        private int _frameY = frameHeight;

        private Bitmap _frmBitmap;

        //private bool _haveHandle;

        //记录鼠标按键是否按下
        private bool _isMouseDown;
        private Point _oldPoint = new Point(0, 0);

        public FormTool()
        {
            InitializeComponent();
        }

        public void SetWindowLong()
        {
            try
            {
                CommonMethod.DetermineCall(this, delegate
                {
                    var baseStyle = 524416 | WS_EX_NOACTIVATE | WS_EX_TOPMOST;
                    SetWindowLong(Handle, GWL_EXSTYLE, new IntPtr(baseStyle));
                });
            }
            catch (Exception e)
            {
            }
        }

        public bool IsImage { get; set; }

        public int DelayMilSec { get; set; }

        private void FormHome_Load(object sender, EventArgs e)
        {
            if (IsImage)
            {
                SetLocation();
                RefreshImage();
                MouseEnter += FormTool_MouseEnter;
                MouseLeave += FormTool_MouseLeave;
            }
            else
            {
                MouseDoubleClick += FormTool_MouseDoubleClick;
            }
        }

        private void FormTool_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            Hide();
        }

        public string NowID { get; set; }

        public void DrawStr(string tipMsg, string id)
        {
            SizeF sf = TextRenderer.MeasureText(CreateGraphics(), tipMsg, _baseFont, new Size(10000000, 1000000),
                CommonString.BaseTextFormatFlags);
            var 宽度 = (int)(100 + sf.Width);
            ClientSize = new Size(宽度, 50);
            Location = new Point((Screen.PrimaryScreen.Bounds.Width - Width) / 2,
                (Screen.PrimaryScreen.WorkingArea.Height - Height) / 2 / 3 * 5);
            using (var bmp = new Bitmap(宽度, 50))
            {
                using (var g = Graphics.FromImage(bmp))
                {
                    g.InterpolationMode = InterpolationMode.Bilinear;
                    g.SmoothingMode = SmoothingMode.HighQuality;
                    g.TextRenderingHint = TextRenderingHint.AntiAlias;
                    g.Clear(Color.Transparent);
                    g.FillRectangle(new SolidBrush(Color.FromArgb(1, 255, 255, 255)), ClientRectangle);
                    var stringFormat = new StringFormat
                    {
                        Alignment = StringAlignment.Center
                    };
                    var r = new Rectangle(0, 3, 宽度, 50);
                    g.FillRectangle(new SolidBrush(Color.FromArgb(120, Color.Black)), 1, 1, 宽度 - 2, 48);
                    g.DrawRectangle(new Pen(Color.FromArgb(224, 224, 224)), 2, 2, 宽度 - 2 - 2, 46);
                    g.DrawString(tipMsg, _baseFont, new SolidBrush(Color.FromArgb(255, Color.White)), r, stringFormat);
                    SetBits(bmp);
                    g.Dispose();
                }

                bmp.Dispose();
            }

            Show();
            Delay(DelayMilSec, id);
            if (IsMe(id))
                Hide();
        }

        private bool IsMe(string id)
        {
            return Equals(id, NowID);
        }

        private void Delay(int ms, string id)
        {
            var tickCount = ServerTime.DateTime.Ticks;
            while (IsMe(id) && new TimeSpan(ServerTime.DateTime.Ticks - tickCount).TotalMilliseconds < ms)
            {
                Thread.SpinWait(5);
                Application.DoEvents();
            }
        }

        public void SetNotMove()
        {
            _isMouseDown = false;
        }

        private void FormHome_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                _oldPoint = e.Location;
                _isMouseDown = true;
            }
            else if (e.Button == MouseButtons.Right)
            {
                this.ForceActivate();
                ContextMenuStrip?.Show(this, e.Location);
            }
        }

        private void FormHome_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isMouseDown) Location = new Point(Left + e.X - _oldPoint.X, Top + e.Y - _oldPoint.Y);
        }

        private void FormHome_MouseUp(object sender, MouseEventArgs e)
        {
            if (_isMouseDown) CommonSetting.SetValue("工具栏位置", string.Format("{0},{1}", Left, Top));
            _isMouseDown = false;
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            e.Cancel = true;
            Hide();
            //base.OnClosing(e);
            //_haveHandle = false;
        }

        public void VisibleChange()
        {
            Visible = !Visible;
        }

        public void RefreshImage(Bitmap image = null, bool isFocus = false)
        {
            if (image == null)
                image = CommonSetting.Get图标效果(CommonSetting.GetStrSize(CommonSetting.工具栏图标尺寸), CommonSetting.工具栏图片,
                    CommonSetting.圆形图标, CommonSetting.阴影效果, CommonSetting.工具栏阴影宽度, isFocus);

            if (Equals(_frmBitmap, image)) return;

            SetBits(image);
            if (_frmBitmap == null)
            {
                Location = new Point(_frameX, _frameY);
            }
            else
            {
                // 计算偏移位置
                if (!_isMouseDown && !Equals(image.Size, _frmBitmap.Size))
                    Location = new Point(Left + (_frmBitmap.Width - image.Width) / 2,
                        Top + (_frmBitmap.Height - image.Height) / 2);
            }

            _frmBitmap = image;
            Invalidate(true);
        }

        private void SetLocation()
        {
            var objValue = CommonSetting.GetValue<string>(null, "工具栏位置");
            if (string.IsNullOrEmpty(objValue) || !objValue.Contains(",")) return;
            var locations = objValue.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries);
            if (locations.Length != 2) return;
            var left = BoxUtil.GetInt32FromObject(locations[0]);
            if (left > 0 && left <= Screen.PrimaryScreen.WorkingArea.Size.Width - 10) _frameX = left;
            var top = BoxUtil.GetInt32FromObject(locations[1]);
            if (top > 0 && top <= Screen.PrimaryScreen.WorkingArea.Size.Height - 10) _frameY = top;
        }

        public void SetBits(Bitmap bitmap)
        {
            //if (!_haveHandle) return;

            if (!Image.IsCanonicalPixelFormat(bitmap.PixelFormat) || !Image.IsAlphaPixelFormat(bitmap.PixelFormat))
                throw new ApplicationException("The picture must be 32bit picture with alpha channel.");

            var oldBits = IntPtr.Zero;
            var screenDc = Win32.GetDC(IntPtr.Zero);
            var hBitmap = IntPtr.Zero;
            var memDc = Win32.CreateCompatibleDC(screenDc);

            try
            {
                var topLoc = new Win32.Point(Left, Top);
                var bitMapSize = new Win32.Size(bitmap.Width, bitmap.Height);
                var blendFunc = new Win32.BLENDFUNCTION();
                var srcLoc = new Win32.Point(0, 0);

                hBitmap = bitmap.GetHbitmap(Color.FromArgb(0));
                oldBits = Win32.SelectObject(memDc, hBitmap);

                blendFunc.BlendOp = Win32.AC_SRC_OVER;
                blendFunc.SourceConstantAlpha = 255;
                blendFunc.AlphaFormat = Win32.AC_SRC_ALPHA;
                blendFunc.BlendFlags = 0;

                Win32.UpdateLayeredWindow(Handle, screenDc, ref topLoc, ref bitMapSize, memDc, ref srcLoc, 0,
                    ref blendFunc, Win32.ULW_ALPHA);
            }
            finally
            {
                if (hBitmap != IntPtr.Zero)
                {
                    Win32.SelectObject(memDc, oldBits);
                    Win32.DeleteObject(hBitmap);
                }

                Win32.ReleaseDC(IntPtr.Zero, screenDc);
                Win32.DeleteDC(memDc);
            }
        }

        public void SetDragDrop()
        {
            this.ControlUseDrop();
        }

        private void FormTool_MouseLeave(object sender, EventArgs e)
        {
            RefreshImage();
        }

        private void FormTool_MouseEnter(object sender, EventArgs e)
        {
            RefreshImage(null, true);
        }

        #region Native Methods

        private const int WS_EX_TOPMOST = 0x00000008;
        private const int WS_EX_NOACTIVATE = 0x08000000;
        private const int GWL_EXSTYLE = -20;

        private static IntPtr SetWindowLong(IntPtr hWnd, int nIndex, IntPtr dwNewLong)
        {
            return Environment.Is64BitProcess
                ? SetWindowLong64(hWnd, nIndex, dwNewLong)
                : SetWindowLong32(hWnd, nIndex, dwNewLong);
        }

        [DllImport("user32.dll", EntryPoint = "SetWindowLong")]
        private static extern IntPtr SetWindowLong32(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

        [DllImport("user32.dll", EntryPoint = "SetWindowLongPtr")]
        private static extern IntPtr SetWindowLong64(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

        #endregion
    }
}