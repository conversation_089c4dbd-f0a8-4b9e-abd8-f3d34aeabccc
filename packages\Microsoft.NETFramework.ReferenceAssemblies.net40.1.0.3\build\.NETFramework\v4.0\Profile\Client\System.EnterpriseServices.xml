﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.EnterpriseServices</name>
  </assembly>
  <members>
    <member name="T:System.EnterpriseServices.AccessChecksLevelOption">
      <summary>Specifies the level of access checking for an application, either at the process level only or at all levels, including component, interface, and method levels.</summary>
    </member>
    <member name="F:System.EnterpriseServices.AccessChecksLevelOption.Application">
      <summary>Enables access checks only at the process level. No access checks are made at the component, interface, or method level.</summary>
    </member>
    <member name="F:System.EnterpriseServices.AccessChecksLevelOption.ApplicationComponent">
      <summary>Enables access checks at every level on calls into the application.</summary>
    </member>
    <member name="T:System.EnterpriseServices.ActivationOption">
      <summary>Specifies the manner in which serviced components are activated in the application.</summary>
    </member>
    <member name="F:System.EnterpriseServices.ActivationOption.Library">
      <summary>Specifies that serviced components in the marked application are activated in the creator's process.</summary>
    </member>
    <member name="F:System.EnterpriseServices.ActivationOption.Server">
      <summary>Specifies that serviced components in the marked application are activated in a system-provided process.</summary>
    </member>
    <member name="T:System.EnterpriseServices.Activity">
      <summary>Creates an activity to do synchronous or asynchronous batch work that can use COM+ services without needing to create a COM+ component. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Activity.#ctor(System.EnterpriseServices.ServiceConfig)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Activity" /> class.</summary>
      <param name="cfg">A <see cref="T:System.EnterpriseServices.ServiceConfig" /> that contains the configuration information for the services to be used. </param>
      <exception cref="T:System.PlatformNotSupportedException">
        <see cref="T:System.EnterpriseServices.Activity" /> is not supported on the current platform. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Activity.AsynchronousCall(System.EnterpriseServices.IServiceCall)">
      <summary>Runs the specified user-defined batch work asynchronously.</summary>
      <param name="serviceCall">A <see cref="T:System.EnterpriseServices.IServiceCall" /> object that is used to implement the batch work. </param>
    </member>
    <member name="M:System.EnterpriseServices.Activity.BindToCurrentThread">
      <summary>Binds the user-defined work to the current thread.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Activity.SynchronousCall(System.EnterpriseServices.IServiceCall)">
      <summary>Runs the specified user-defined batch work synchronously.</summary>
      <param name="serviceCall">A <see cref="T:System.EnterpriseServices.IServiceCall" /> object that is used to implement the batch work. </param>
    </member>
    <member name="M:System.EnterpriseServices.Activity.UnbindFromThread">
      <summary>Unbinds the batch work that is submitted by the <see cref="M:System.EnterpriseServices.Activity.SynchronousCall(System.EnterpriseServices.IServiceCall)" /> or <see cref="M:System.EnterpriseServices.Activity.AsynchronousCall(System.EnterpriseServices.IServiceCall)" /> methods from the thread on which the batch work is running.</summary>
    </member>
    <member name="T:System.EnterpriseServices.ApplicationAccessControlAttribute">
      <summary>Specifies access controls to an assembly containing <see cref="T:System.EnterpriseServices.ServicedComponent" /> classes.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ApplicationAccessControlAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ApplicationAccessControlAttribute" /> class, enabling the COM+ security configuration.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ApplicationAccessControlAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ApplicationAccessControlAttribute" /> class and sets the <see cref="P:System.EnterpriseServices.ApplicationAccessControlAttribute.Value" /> property indicating whether to enable COM security configuration.</summary>
      <param name="val">true to allow configuration of security; otherwise, false. </param>
    </member>
    <member name="P:System.EnterpriseServices.ApplicationAccessControlAttribute.AccessChecksLevel">
      <summary>Gets or sets the access checking level to process level or to component level.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.AccessChecksLevelOption" /> values.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ApplicationAccessControlAttribute.Authentication">
      <summary>Gets or sets the remote procedure call (RPC) authentication level.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.AuthenticationOption" /> values.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ApplicationAccessControlAttribute.ImpersonationLevel">
      <summary>Gets or sets the impersonation level that is allowed for calling targets of this application.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.ImpersonationLevelOption" /> values.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ApplicationAccessControlAttribute.Value">
      <summary>Gets or sets a value indicating whether to enable COM+ security configuration.</summary>
      <returns>true if COM+ security configuration is enabled; otherwise, false.</returns>
    </member>
    <member name="T:System.EnterpriseServices.ApplicationActivationAttribute">
      <summary>Specifies whether components in the assembly run in the creator's process or in a system process.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ApplicationActivationAttribute.#ctor(System.EnterpriseServices.ActivationOption)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ApplicationActivationAttribute" /> class, setting the specified <see cref="T:System.EnterpriseServices.ActivationOption" /> value.</summary>
      <param name="opt">One of the <see cref="T:System.EnterpriseServices.ActivationOption" /> values. </param>
    </member>
    <member name="P:System.EnterpriseServices.ApplicationActivationAttribute.SoapMailbox">
      <summary>This property is not supported in the current version.</summary>
      <returns>This property is not supported in the current version.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ApplicationActivationAttribute.SoapVRoot">
      <summary>Gets or sets a value representing a virtual root on the Web for the COM+ application.</summary>
      <returns>The virtual root on the Web for the COM+ application.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ApplicationActivationAttribute.Value">
      <summary>Gets the specified <see cref="T:System.EnterpriseServices.ActivationOption" /> value.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.ActivationOption" /> values, either Library or Server.</returns>
    </member>
    <member name="T:System.EnterpriseServices.ApplicationIDAttribute">
      <summary>Specifies the application ID (as a GUID) for this assembly. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ApplicationIDAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ApplicationIDAttribute" /> class specifying the GUID representing the application ID for the COM+ application.</summary>
      <param name="guid">The GUID associated with the COM+ application. </param>
    </member>
    <member name="P:System.EnterpriseServices.ApplicationIDAttribute.Value">
      <summary>Gets the GUID of the COM+ application.</summary>
      <returns>The GUID representing the COM+ application.</returns>
    </member>
    <member name="T:System.EnterpriseServices.ApplicationNameAttribute">
      <summary>Specifies the name of the COM+ application to be used for the install of the components in the assembly. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ApplicationNameAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ApplicationNameAttribute" /> class, specifying the name of the COM+ application to be used for the install of the components.</summary>
      <param name="name">The name of the COM+ application. </param>
    </member>
    <member name="P:System.EnterpriseServices.ApplicationNameAttribute.Value">
      <summary>Gets a value indicating the name of the COM+ application that contains the components in the assembly.</summary>
      <returns>The name of the COM+ application.</returns>
    </member>
    <member name="T:System.EnterpriseServices.ApplicationQueuingAttribute">
      <summary>Enables queuing support for the marked assembly and enables the application to read method calls from Message Queuing queues. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ApplicationQueuingAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ApplicationQueuingAttribute" /> class, enabling queuing support for the assembly and initializing <see cref="P:System.EnterpriseServices.ApplicationQueuingAttribute.Enabled" />, <see cref="P:System.EnterpriseServices.ApplicationQueuingAttribute.QueueListenerEnabled" />, and <see cref="P:System.EnterpriseServices.ApplicationQueuingAttribute.MaxListenerThreads" />.</summary>
    </member>
    <member name="P:System.EnterpriseServices.ApplicationQueuingAttribute.Enabled">
      <summary>Gets or sets a value indicating whether queuing support is enabled.</summary>
      <returns>true if queuing support is enabled; otherwise, false. The default value set by the constructor is true.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ApplicationQueuingAttribute.MaxListenerThreads">
      <summary>Gets or sets the number of threads used to extract messages from the queue and activate the corresponding component.</summary>
      <returns>The maximum number of threads to use for processing messages arriving in the queue. The default is zero.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ApplicationQueuingAttribute.QueueListenerEnabled">
      <summary>Gets or sets a value indicating whether the application will accept queued component calls from clients.</summary>
      <returns>true if the application accepts queued component calls; otherwise, false. The default is false.</returns>
    </member>
    <member name="T:System.EnterpriseServices.AuthenticationOption">
      <summary>Specifies the remote procedure call (RPC) authentication mechanism. Applicable only when the <see cref="T:System.EnterpriseServices.ActivationOption" /> is set to Server.</summary>
    </member>
    <member name="F:System.EnterpriseServices.AuthenticationOption.Default">
      <summary>Uses the default authentication level for the specified authentication service. In COM+, this setting is provided by the DefaultAuthenticationLevel property in the LocalComputer collection.</summary>
    </member>
    <member name="F:System.EnterpriseServices.AuthenticationOption.None">
      <summary>Authentication does not occur.</summary>
    </member>
    <member name="F:System.EnterpriseServices.AuthenticationOption.Connect">
      <summary>Authenticates credentials only when the connection is made.</summary>
    </member>
    <member name="F:System.EnterpriseServices.AuthenticationOption.Call">
      <summary>Authenticates credentials at the beginning of every call.</summary>
    </member>
    <member name="F:System.EnterpriseServices.AuthenticationOption.Packet">
      <summary>Authenticates credentials and verifies that all call data is received.</summary>
    </member>
    <member name="F:System.EnterpriseServices.AuthenticationOption.Integrity">
      <summary>Authenticates credentials and verifies that no call data has been modified in transit.</summary>
    </member>
    <member name="F:System.EnterpriseServices.AuthenticationOption.Privacy">
      <summary>Authenticates credentials and encrypts the packet, including the data and the sender's identity and signature.</summary>
    </member>
    <member name="T:System.EnterpriseServices.AutoCompleteAttribute">
      <summary>Marks the attributed method as an AutoComplete object. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.AutoCompleteAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.AutoCompleteAttribute" /> class, specifying that the application should automatically call <see cref="M:System.EnterpriseServices.ContextUtil.SetComplete" /> if the transaction completes successfully.</summary>
    </member>
    <member name="M:System.EnterpriseServices.AutoCompleteAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.AutoCompleteAttribute" /> class, specifying whether COM+ AutoComplete is enabled.</summary>
      <param name="val">true to enable AutoComplete in the COM+ object; otherwise, false. </param>
    </member>
    <member name="P:System.EnterpriseServices.AutoCompleteAttribute.Value">
      <summary>Gets a value indicating the setting of the AutoComplete option in COM+.</summary>
      <returns>true if AutoComplete is enabled in COM+; otherwise, false. The default is true.</returns>
    </member>
    <member name="T:System.EnterpriseServices.BindingOption">
      <summary>Indicates whether all work submitted by <see cref="T:System.EnterpriseServices.Activity" /> should be bound to only one single-threaded apartment (STA). This enumeration has no impact on the multithreaded apartment (MTA).</summary>
    </member>
    <member name="F:System.EnterpriseServices.BindingOption.NoBinding">
      <summary>The work submitted by the activity is not bound to a single STA.</summary>
    </member>
    <member name="F:System.EnterpriseServices.BindingOption.BindingToPoolThread">
      <summary>The work submitted by the activity is bound to a single STA.</summary>
    </member>
    <member name="T:System.EnterpriseServices.BOID">
      <summary>Represents the unit of work associated with a transaction. This structure is used in <see cref="T:System.EnterpriseServices.XACTTRANSINFO" />.</summary>
    </member>
    <member name="F:System.EnterpriseServices.BOID.rgb">
      <summary>Represents an array that contains the data.</summary>
    </member>
    <member name="T:System.EnterpriseServices.BYOT">
      <summary>Wraps the COM+ ByotServerEx class and the COM+ DTC interfaces ICreateWithTransactionEx and ICreateWithTipTransactionEx. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.BYOT.CreateWithTipTransaction(System.String,System.Type)">
      <summary>Creates an object that is enlisted within a manual transaction using the Transaction Internet Protocol (TIP).</summary>
      <returns>The requested transaction.</returns>
      <param name="url">A TIP URL that specifies a transaction. </param>
      <param name="t">The type. </param>
    </member>
    <member name="M:System.EnterpriseServices.BYOT.CreateWithTransaction(System.Object,System.Type)">
      <summary>Creates an object that is enlisted within a manual transaction.</summary>
      <returns>The requested transaction.</returns>
      <param name="transaction">The <see cref="T:System.EnterpriseServices.ITransaction" /> or <see cref="T:System.Transactions.Transaction" /> object that specifies a transaction. </param>
      <param name="t">The specified type. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.ComponentAccessControlAttribute">
      <summary>Enables security checking on calls to a component. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ComponentAccessControlAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ComponentAccessControlAttribute" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ComponentAccessControlAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ComponentAccessControlAttribute" /> class and sets the <see cref="P:System.EnterpriseServices.ComponentAccessControlAttribute.Value" /> property to indicate whether to enable COM+ security configuration.</summary>
      <param name="val">true to enable security checking on calls to a component; otherwise, false. </param>
    </member>
    <member name="P:System.EnterpriseServices.ComponentAccessControlAttribute.Value">
      <summary>Gets a value indicating whether to enable security checking on calls to a component.</summary>
      <returns>true if security checking is to be enabled; otherwise, false.</returns>
    </member>
    <member name="T:System.EnterpriseServices.COMTIIntrinsicsAttribute">
      <summary>Enables you to pass context properties from the COM Transaction Integrator (COMTI) into the COM+ context.</summary>
    </member>
    <member name="M:System.EnterpriseServices.COMTIIntrinsicsAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.COMTIIntrinsicsAttribute" /> class, setting the <see cref="P:System.EnterpriseServices.COMTIIntrinsicsAttribute.Value" /> property to true.</summary>
    </member>
    <member name="M:System.EnterpriseServices.COMTIIntrinsicsAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.COMTIIntrinsicsAttribute" /> class, enabling the setting of the <see cref="P:System.EnterpriseServices.COMTIIntrinsicsAttribute.Value" /> property.</summary>
      <param name="val">true if the COMTI context properties are passed into the COM+ context; otherwise, false. </param>
    </member>
    <member name="P:System.EnterpriseServices.COMTIIntrinsicsAttribute.Value">
      <summary>Gets a value indicating whether the COM Transaction Integrator (COMTI) context properties are passed into the COM+ context.</summary>
      <returns>true if the COMTI context properties are passed into the COM+ context; otherwise, false. The default is true.</returns>
    </member>
    <member name="T:System.EnterpriseServices.ConstructionEnabledAttribute">
      <summary>Enables COM+ object construction support. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ConstructionEnabledAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ConstructionEnabledAttribute" /> class and initializes the default settings for <see cref="P:System.EnterpriseServices.ConstructionEnabledAttribute.Enabled" /> and <see cref="P:System.EnterpriseServices.ConstructionEnabledAttribute.Default" />.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ConstructionEnabledAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ConstructionEnabledAttribute" /> class, setting <see cref="P:System.EnterpriseServices.ConstructionEnabledAttribute.Enabled" /> to the specified value.</summary>
      <param name="val">true to enable COM+ object construction support; otherwise, false. </param>
    </member>
    <member name="P:System.EnterpriseServices.ConstructionEnabledAttribute.Default">
      <summary>Gets or sets a default value for the constructor string.</summary>
      <returns>The value to be used for the default constructor string. The default is an empty string ("").</returns>
    </member>
    <member name="P:System.EnterpriseServices.ConstructionEnabledAttribute.Enabled">
      <summary>Gets or sets a value indicating whether COM+ object construction support is enabled.</summary>
      <returns>true if COM+ object construction support is enabled; otherwise, false. The default is true.</returns>
    </member>
    <member name="T:System.EnterpriseServices.ContextUtil">
      <summary>Obtains information about the COM+ object context. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.EnterpriseServices.ContextUtil.ActivityId">
      <summary>Gets a GUID representing the activity containing the component.</summary>
      <returns>The GUID for an activity if the current context is part of an activity; otherwise, GUID_NULL.</returns>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The platform is not Windows 2000 or later. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.EnterpriseServices.ContextUtil.ApplicationId">
      <summary>Gets a GUID for the current application.</summary>
      <returns>The GUID for the current application.</returns>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The platform is not Windows XP or later. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.EnterpriseServices.ContextUtil.ApplicationInstanceId">
      <summary>Gets a GUID for the current application instance.</summary>
      <returns>The GUID for the current application instance.</returns>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The platform is not Windows XP or later. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.EnterpriseServices.ContextUtil.ContextId">
      <summary>Gets a GUID for the current context.</summary>
      <returns>The GUID for the current context.</returns>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The platform is not Windows 2000 or later. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.EnterpriseServices.ContextUtil.DeactivateOnReturn">
      <summary>Gets or sets the done bit in the COM+ context.</summary>
      <returns>true if the object is to be deactivated when the method returns; otherwise, false. The default is false.</returns>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The platform is not Windows 2000 or later. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.ContextUtil.DisableCommit">
      <summary>Sets both the consistent bit and the done bit to false in the COM+ context.</summary>
      <exception cref="T:System.Runtime.InteropServices.COMException">No COM+ context is available.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.ContextUtil.EnableCommit">
      <summary>Sets the consistent bit to true and the done bit to false in the COM+ context.</summary>
      <exception cref="T:System.Runtime.InteropServices.COMException">No COM+ context is available. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.ContextUtil.GetNamedProperty(System.String)">
      <summary>Returns a named property from the COM+ context.</summary>
      <returns>The named property for the context.</returns>
      <param name="name">The name of the requested property. </param>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The platform is not Windows 2000 or later. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.ContextUtil.IsCallerInRole(System.String)">
      <summary>Determines whether the caller is in the specified role.</summary>
      <returns>true if the caller is in the specified role; otherwise, false.</returns>
      <param name="role">The name of the role to check. </param>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.ContextUtil.IsDefaultContext">
      <summary>Determines whether the serviced component is activated in the default context. Serviced components that do not have COM+ catalog information are activated in the default context.</summary>
      <returns>true if the serviced component is activated in the default context; otherwise, false.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ContextUtil.IsInTransaction">
      <summary>Gets a value that indicates whether the current context is transactional.</summary>
      <returns>true if the current context is transactional; otherwise, false.</returns>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
    </member>
    <member name="P:System.EnterpriseServices.ContextUtil.IsSecurityEnabled">
      <summary>Gets a value that indicates whether role-based security is active in the current context.</summary>
      <returns>true if the current context has security enabled; otherwise, false.</returns>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.EnterpriseServices.ContextUtil.MyTransactionVote">
      <summary>Gets or sets the consistent bit in the COM+ context.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.TransactionVote" /> values, either Commit or Abort.</returns>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The platform is not Windows 2000 or later.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.EnterpriseServices.ContextUtil.PartitionId">
      <summary>Gets a GUID for the current partition.</summary>
      <returns>The GUID for the current partition.</returns>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The platform is not Windows XP or later. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.ContextUtil.SetAbort">
      <summary>Sets the consistent bit to false and the done bit to true in the COM+ context.</summary>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.ContextUtil.SetComplete">
      <summary>Sets the consistent bit and the done bit to true in the COM+ context.</summary>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.ContextUtil.SetNamedProperty(System.String,System.Object)">
      <summary>Sets the named property for the COM+ context.</summary>
      <param name="name">The name of the property to set. </param>
      <param name="value">Object that represents the property value to set.</param>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The platform is not Windows 2000 or later. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.EnterpriseServices.ContextUtil.SystemTransaction">
      <summary>Gets the current transaction context.</summary>
      <returns>A <see cref="T:System.Transactions.Transaction" /> that represents the current transaction context.</returns>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The platform is not Windows 2000 or later. </exception>
    </member>
    <member name="P:System.EnterpriseServices.ContextUtil.Transaction">
      <summary>Gets an object describing the current COM+ DTC transaction.</summary>
      <returns>An object that represents the current transaction.</returns>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The platform is not Windows 2000 or later. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.EnterpriseServices.ContextUtil.TransactionId">
      <summary>Gets the GUID of the current COM+ DTC transaction.</summary>
      <returns>A GUID representing the current COM+ DTC transaction, if one exists.</returns>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no COM+ context available. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The platform is not Windows 2000 or later. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.DescriptionAttribute">
      <summary>Sets the description on an assembly (application), component, method, or interface. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.DescriptionAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.DescriptionAttribute" /> class.</summary>
      <param name="desc">The description of the assembly (application), component, method, or interface. </param>
    </member>
    <member name="T:System.EnterpriseServices.EventClassAttribute">
      <summary>Marks the attributed class as an event class. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.EventClassAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.EventClassAttribute" /> class.</summary>
    </member>
    <member name="P:System.EnterpriseServices.EventClassAttribute.AllowInprocSubscribers">
      <summary>Gets or sets a value that indicates whether subscribers can be activated in the publisher's process.</summary>
      <returns>true if subscribers can be activated in the publisher's process; otherwise, false.</returns>
    </member>
    <member name="P:System.EnterpriseServices.EventClassAttribute.FireInParallel">
      <summary>Gets or sets a value that indicates whether events are to be delivered to subscribers in parallel.</summary>
      <returns>true if events are to be delivered to subscribers in parallel; otherwise, false.</returns>
    </member>
    <member name="P:System.EnterpriseServices.EventClassAttribute.PublisherFilter">
      <summary>Gets or sets a publisher filter for an event method.</summary>
      <returns>The publisher filter.</returns>
    </member>
    <member name="T:System.EnterpriseServices.EventTrackingEnabledAttribute">
      <summary>Enables event tracking for a component. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.EventTrackingEnabledAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.EventTrackingEnabledAttribute" /> class, enabling event tracking.</summary>
    </member>
    <member name="M:System.EnterpriseServices.EventTrackingEnabledAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.EventTrackingEnabledAttribute" /> class, optionally disabling event tracking.</summary>
      <param name="val">true to enable event tracking; otherwise, false. </param>
    </member>
    <member name="P:System.EnterpriseServices.EventTrackingEnabledAttribute.Value">
      <summary>Gets the value of the <see cref="P:System.EnterpriseServices.EventTrackingEnabledAttribute.Value" /> property, which indicates whether tracking is enabled.</summary>
      <returns>true if tracking is enabled; otherwise, false. The default is true.</returns>
    </member>
    <member name="T:System.EnterpriseServices.ExceptionClassAttribute">
      <summary>Sets the queuing exception class for the queued class. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ExceptionClassAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ExceptionClassAttribute" /> class.</summary>
      <param name="name">The name of the exception class for the player to activate and play back before the message is routed to the dead letter queue. </param>
    </member>
    <member name="P:System.EnterpriseServices.ExceptionClassAttribute.Value">
      <summary>Gets the name of the exception class for the player to activate and play back before the message is routed to the dead letter queue.</summary>
      <returns>The name of the exception class for the player to activate and play back before the message is routed to the dead letter queue.</returns>
    </member>
    <member name="T:System.EnterpriseServices.IAsyncErrorNotify">
      <summary>Implements error trapping on the asynchronous batch work that is submitted by the <see cref="T:System.EnterpriseServices.Activity" /> object.</summary>
    </member>
    <member name="M:System.EnterpriseServices.IAsyncErrorNotify.OnError(System.Int32)">
      <summary>Handles errors for asynchronous batch work.</summary>
      <param name="hresult">The HRESULT of the error that occurred while the batch work was running asynchronously. </param>
    </member>
    <member name="T:System.EnterpriseServices.IISIntrinsicsAttribute">
      <summary>Enables access to ASP intrinsic values from <see cref="M:System.EnterpriseServices.ContextUtil.GetNamedProperty(System.String)" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.IISIntrinsicsAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.IISIntrinsicsAttribute" /> class, enabling access to the ASP intrinsic values.</summary>
    </member>
    <member name="M:System.EnterpriseServices.IISIntrinsicsAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.IISIntrinsicsAttribute" /> class, optionally disabling access to the ASP intrinsic values.</summary>
      <param name="val">true to enable access to the ASP intrinsic values; otherwise, false. </param>
    </member>
    <member name="P:System.EnterpriseServices.IISIntrinsicsAttribute.Value">
      <summary>Gets a value that indicates whether access to the ASP intrinsic values is enabled.</summary>
      <returns>true if access is enabled; otherwise, false. The default is true.</returns>
    </member>
    <member name="T:System.EnterpriseServices.ImpersonationLevelOption">
      <summary>Specifies the level of impersonation allowed when calling targets of a server application.</summary>
    </member>
    <member name="F:System.EnterpriseServices.ImpersonationLevelOption.Default">
      <summary>Uses the default impersonation level for the specified authentication service. In COM+, this setting is provided by the DefaultImpersonationLevel property in the LocalComputer collection.</summary>
    </member>
    <member name="F:System.EnterpriseServices.ImpersonationLevelOption.Anonymous">
      <summary>The client is anonymous to the server. The server process can impersonate the client, but the impersonation token does not contain any information about the client.</summary>
    </member>
    <member name="F:System.EnterpriseServices.ImpersonationLevelOption.Identify">
      <summary>The system default level. The server can obtain the client's identity, and the server can impersonate the client to do ACL checks.</summary>
    </member>
    <member name="F:System.EnterpriseServices.ImpersonationLevelOption.Impersonate">
      <summary>The server can impersonate the client's security context while acting on behalf of the client. The server can access local resources as the client.</summary>
    </member>
    <member name="F:System.EnterpriseServices.ImpersonationLevelOption.Delegate">
      <summary>The most powerful impersonation level. When this level is selected, the server (whether local or remote) can impersonate the client's security context while acting on behalf of the client </summary>
    </member>
    <member name="T:System.EnterpriseServices.InheritanceOption">
      <summary>Indicates whether to create a new context based on the current context or on the information in <see cref="T:System.EnterpriseServices.ServiceConfig" />.</summary>
    </member>
    <member name="F:System.EnterpriseServices.InheritanceOption.Inherit">
      <summary>The new context is created from the existing context. <see cref="F:System.EnterpriseServices.InheritanceOption.Inherit" /> is the default value for <see cref="P:System.EnterpriseServices.ServiceConfig.Inheritance" />.</summary>
    </member>
    <member name="F:System.EnterpriseServices.InheritanceOption.Ignore">
      <summary>The new context is created from the default context.</summary>
    </member>
    <member name="T:System.EnterpriseServices.InstallationFlags">
      <summary>Flags used with the <see cref="T:System.EnterpriseServices.RegistrationHelper" /> class.</summary>
    </member>
    <member name="F:System.EnterpriseServices.InstallationFlags.Default">
      <summary>Do the default installation, which configures, installs, and registers, and assumes that the application already exists.</summary>
    </member>
    <member name="F:System.EnterpriseServices.InstallationFlags.ExpectExistingTypeLib">
      <summary>Do not export the type library; one can be found either by the generated or supplied type library name.</summary>
    </member>
    <member name="F:System.EnterpriseServices.InstallationFlags.CreateTargetApplication">
      <summary>Creates the target application. An error occurs if the target already exists.</summary>
    </member>
    <member name="F:System.EnterpriseServices.InstallationFlags.FindOrCreateTargetApplication">
      <summary>Creates the application if it does not exist; otherwise use the existing application.</summary>
    </member>
    <member name="F:System.EnterpriseServices.InstallationFlags.ReconfigureExistingApplication">
      <summary>If using an existing application, ensures that the properties on this application match those in the assembly.</summary>
    </member>
    <member name="F:System.EnterpriseServices.InstallationFlags.ConfigureComponentsOnly">
      <summary>Configures components only, do not configure methods or interfaces.</summary>
    </member>
    <member name="F:System.EnterpriseServices.InstallationFlags.ReportWarningsToConsole">
      <summary>When alert text is encountered, writes it to the Console.</summary>
    </member>
    <member name="F:System.EnterpriseServices.InstallationFlags.Register">
      <summary>Should not be used.</summary>
    </member>
    <member name="F:System.EnterpriseServices.InstallationFlags.Install">
      <summary>Should not be used.</summary>
    </member>
    <member name="F:System.EnterpriseServices.InstallationFlags.Configure">
      <summary>Should not be used.</summary>
    </member>
    <member name="T:System.EnterpriseServices.InterfaceQueuingAttribute">
      <summary>Enables queuing support for the marked interface. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.InterfaceQueuingAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.InterfaceQueuingAttribute" /> class setting the <see cref="P:System.EnterpriseServices.InterfaceQueuingAttribute.Enabled" /> and <see cref="P:System.EnterpriseServices.InterfaceQueuingAttribute.Interface" /> properties to their default values.</summary>
    </member>
    <member name="M:System.EnterpriseServices.InterfaceQueuingAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.InterfaceQueuingAttribute" /> class, optionally disabling queuing support.</summary>
      <param name="enabled">true to enable queuing support; otherwise, false. </param>
    </member>
    <member name="P:System.EnterpriseServices.InterfaceQueuingAttribute.Enabled">
      <summary>Gets or sets a value indicating whether queuing support is enabled.</summary>
      <returns>true if queuing support is enabled; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.EnterpriseServices.InterfaceQueuingAttribute.Interface">
      <summary>Gets or sets the name of the interface on which queuing is enabled.</summary>
      <returns>The name of the interface on which queuing is enabled.</returns>
    </member>
    <member name="T:System.EnterpriseServices.IPlaybackControl">
      <summary>Functions in Queued Components in the abnormal handling of server-side playback errors and client-side failures of the Message Queuing delivery mechanism.</summary>
    </member>
    <member name="M:System.EnterpriseServices.IPlaybackControl.FinalClientRetry">
      <summary>Informs the client-side exception-handling component that all Message Queuing attempts to deliver the message to the server were rejected, and the message ended up on the client-side Xact Dead Letter queue.</summary>
    </member>
    <member name="M:System.EnterpriseServices.IPlaybackControl.FinalServerRetry">
      <summary>Informs the server-side exception class implementation that all attempts to play back the deferred activation to the server have failed, and the message is about to be moved to its final resting queue.</summary>
    </member>
    <member name="T:System.EnterpriseServices.IProcessInitControl">
      <summary>Supports setting the time-out for the <see cref="M:System.EnterpriseServices.IProcessInitializer.Startup(System.Object)" /> method.</summary>
    </member>
    <member name="M:System.EnterpriseServices.IProcessInitControl.ResetInitializerTimeout(System.Int32)">
      <summary>Sets the number of seconds remaining before the <see cref="M:System.EnterpriseServices.IProcessInitializer.Startup(System.Object)" /> method times out.</summary>
      <param name="dwSecondsRemaining">The number of seconds that remain before the time taken to execute the start up method times out. </param>
    </member>
    <member name="T:System.EnterpriseServices.IProcessInitializer">
      <summary>Supports methods that can be called when a COM component starts up or shuts down.</summary>
    </member>
    <member name="M:System.EnterpriseServices.IProcessInitializer.Shutdown">
      <summary>Performs shutdown actions. Called when Dllhost.exe is shut down.</summary>
    </member>
    <member name="M:System.EnterpriseServices.IProcessInitializer.Startup(System.Object)">
      <summary>Performs initialization at startup. Called when Dllhost.exe is started.</summary>
      <param name="punkProcessControl">In Microsoft Windows XP, a pointer to the IUnknown interface of the COM component starting up. In Microsoft Windows 2000, this argument is always null. </param>
    </member>
    <member name="T:System.EnterpriseServices.IRegistrationHelper">
      <summary>Installs and configures assemblies in the COM+ catalog.</summary>
    </member>
    <member name="M:System.EnterpriseServices.IRegistrationHelper.InstallAssembly(System.String,System.String@,System.String@,System.EnterpriseServices.InstallationFlags)">
      <summary>Installs the assembly into the COM+ catalog.</summary>
      <param name="assembly">The assembly name as a file or the strong name of an assembly in the global assembly cache (GAC). </param>
      <param name="application">The application parameter can be null. If it is, the name of the application is automatically generated based on the name of the assembly or the ApplicationName attribute. If the application contains an ApplicationID attribute, the attribute takes precedence. </param>
      <param name="tlb">The name of the output type library (TLB) file, or a string containing null if the registration helper is expected to generate the name. On call completion, the actual name used is placed in the parameter. </param>
      <param name="installFlags">The installation options specified in the enumeration. </param>
    </member>
    <member name="M:System.EnterpriseServices.IRegistrationHelper.UninstallAssembly(System.String,System.String)">
      <summary>Uninstalls the assembly from the COM+ catalog.</summary>
      <param name="assembly">The assembly name as a file or the strong name of an assembly in the global assembly cache (GAC). </param>
      <param name="application">The name of the COM+ application. </param>
    </member>
    <member name="T:System.EnterpriseServices.IRemoteDispatch">
      <summary>Implemented by the <see cref="T:System.EnterpriseServices.ServicedComponent" /> class to determine if the <see cref="T:System.EnterpriseServices.AutoCompleteAttribute" /> class attribute is set to true or false for a remote method invocation.</summary>
    </member>
    <member name="M:System.EnterpriseServices.IRemoteDispatch.RemoteDispatchAutoDone(System.String)">
      <summary>Ensures that, in the COM+ context, the <see cref="T:System.EnterpriseServices.ServicedComponent" /> class object's done bit is set to true after a remote method invocation.</summary>
      <returns>A string converted from a response object that implements the <see cref="T:System.Runtime.Remoting.Messaging.IMethodReturnMessage" /> interface.</returns>
      <param name="s">A string to be converted into a request object that implements the <see cref="T:System.Runtime.Remoting.Messaging.IMessage" /> interface.</param>
    </member>
    <member name="M:System.EnterpriseServices.IRemoteDispatch.RemoteDispatchNotAutoDone(System.String)">
      <summary>Does not ensure that, in the COM+ context, the <see cref="T:System.EnterpriseServices.ServicedComponent" /> class object's done bit is set to true after a remote method invocation.</summary>
      <returns>A string converted from a response object implementing the <see cref="T:System.Runtime.Remoting.Messaging.IMethodReturnMessage" /> interface.</returns>
      <param name="s">A string to be converted into a request object implementing the <see cref="T:System.Runtime.Remoting.Messaging.IMessage" /> interface.</param>
    </member>
    <member name="T:System.EnterpriseServices.IServiceCall">
      <summary>Implements the batch work that is submitted through the activity created by <see cref="T:System.EnterpriseServices.Activity" />.</summary>
    </member>
    <member name="M:System.EnterpriseServices.IServiceCall.OnCall">
      <summary>Starts the execution of the batch work implemented in this method.</summary>
    </member>
    <member name="T:System.EnterpriseServices.IServicedComponentInfo">
      <summary>Implemented by the <see cref="T:System.EnterpriseServices.ServicedComponent" /> class to obtain information about the component via the <see cref="M:System.EnterpriseServices.IServicedComponentInfo.GetComponentInfo(System.Int32@,System.String[]@)" /> method.</summary>
    </member>
    <member name="M:System.EnterpriseServices.IServicedComponentInfo.GetComponentInfo(System.Int32@,System.String[]@)">
      <summary>Obtains certain information about the <see cref="T:System.EnterpriseServices.ServicedComponent" /> class instance.</summary>
      <param name="infoMask">A bitmask where 0x00000001 is a key for the serviced component's process ID, 0x00000002 is a key for the application domain ID, and 0x00000004 is a key for the serviced component's remote URI.</param>
      <param name="infoArray">A string array that may contain any or all of the following, in order: the serviced component's process ID, the application domain ID, and the serviced component's remote URI.</param>
    </member>
    <member name="T:System.EnterpriseServices.ITransaction">
      <summary>Corresponds to the Distributed Transaction Coordinator (DTC) ITransaction interface and is supported by objects obtained through <see cref="P:System.EnterpriseServices.ContextUtil.Transaction" />.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ITransaction.Abort(System.EnterpriseServices.BOID@,System.Int32,System.Int32)">
      <summary>Aborts the transaction.</summary>
      <param name="pboidReason">An optional <see cref="T:System.EnterpriseServices.BOID" /> that indicates why the transaction is being aborted. This parameter can be null, indicating that no reason for the abort is provided. </param>
      <param name="fRetaining">Must be false. </param>
      <param name="fAsync">When <paramref name="fAsync" /> is true, an asynchronous abort is performed and the caller must use ITransactionOutcomeEvents to learn the outcome of the transaction. </param>
    </member>
    <member name="M:System.EnterpriseServices.ITransaction.Commit(System.Int32,System.Int32,System.Int32)">
      <summary>Commits the transaction.</summary>
      <param name="fRetaining">Must be false. </param>
      <param name="grfTC">A value taken from the OLE DB enumeration XACTTC. </param>
      <param name="grfRM">Must be zero. </param>
    </member>
    <member name="M:System.EnterpriseServices.ITransaction.GetTransactionInfo(System.EnterpriseServices.XACTTRANSINFO@)">
      <summary>Returns information about a transaction object.</summary>
      <param name="pinfo">Pointer to the caller-allocated <see cref="T:System.EnterpriseServices.XACTTRANSINFO" /> structure that will receive information about the transaction. Must not be null. </param>
    </member>
    <member name="T:System.EnterpriseServices.JustInTimeActivationAttribute">
      <summary>Turns just-in-time (JIT) activation on or off. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.JustInTimeActivationAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.JustInTimeActivationAttribute" /> class. The default constructor enables just-in-time (JIT) activation.</summary>
    </member>
    <member name="M:System.EnterpriseServices.JustInTimeActivationAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.JustInTimeActivationAttribute" /> class, optionally allowing the disabling of just-in-time (JIT) activation by passing false as the parameter.</summary>
      <param name="val">true to enable JIT activation; otherwise, false. </param>
    </member>
    <member name="P:System.EnterpriseServices.JustInTimeActivationAttribute.Value">
      <summary>Gets the value of the <see cref="T:System.EnterpriseServices.JustInTimeActivationAttribute" /> setting.</summary>
      <returns>true if JIT activation is enabled; otherwise, false. The default is true.</returns>
    </member>
    <member name="T:System.EnterpriseServices.LoadBalancingSupportedAttribute">
      <summary>Determines whether the component participates in load balancing, if the component load balancing service is installed and enabled on the server.</summary>
    </member>
    <member name="M:System.EnterpriseServices.LoadBalancingSupportedAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.LoadBalancingSupportedAttribute" /> class, specifying load balancing support.</summary>
    </member>
    <member name="M:System.EnterpriseServices.LoadBalancingSupportedAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.LoadBalancingSupportedAttribute" /> class, optionally disabling load balancing support.</summary>
      <param name="val">true to enable load balancing support; otherwise, false. </param>
    </member>
    <member name="P:System.EnterpriseServices.LoadBalancingSupportedAttribute.Value">
      <summary>Gets a value that indicates whether load balancing support is enabled.</summary>
      <returns>true if load balancing support is enabled; otherwise, false. The default is true.</returns>
    </member>
    <member name="T:System.EnterpriseServices.MustRunInClientContextAttribute">
      <summary>Forces the attributed object to be created in the context of the creator, if possible. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.MustRunInClientContextAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.MustRunInClientContextAttribute" /> class, requiring creation of the object in the context of the creator.</summary>
    </member>
    <member name="M:System.EnterpriseServices.MustRunInClientContextAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.MustRunInClientContextAttribute" /> class, optionally not creating the object in the context of the creator.</summary>
      <param name="val">true to create the object in the context of the creator; otherwise, false. </param>
    </member>
    <member name="P:System.EnterpriseServices.MustRunInClientContextAttribute.Value">
      <summary>Gets a value that indicates whether the attributed object is to be created in the context of the creator.</summary>
      <returns>true if the object is to be created in the context of the creator; otherwise, false. The default is true.</returns>
    </member>
    <member name="T:System.EnterpriseServices.ObjectPoolingAttribute">
      <summary>Enables and configures object pooling for a component. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ObjectPoolingAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ObjectPoolingAttribute" /> class and sets the <see cref="P:System.EnterpriseServices.ObjectPoolingAttribute.Enabled" />, <see cref="P:System.EnterpriseServices.ObjectPoolingAttribute.MaxPoolSize" />, <see cref="P:System.EnterpriseServices.ObjectPoolingAttribute.MinPoolSize" />, and <see cref="P:System.EnterpriseServices.ObjectPoolingAttribute.CreationTimeout" /> properties to their default values.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ObjectPoolingAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ObjectPoolingAttribute" /> class and sets the <see cref="P:System.EnterpriseServices.ObjectPoolingAttribute.Enabled" /> property.</summary>
      <param name="enable">true to enable object pooling; otherwise, false. </param>
    </member>
    <member name="M:System.EnterpriseServices.ObjectPoolingAttribute.#ctor(System.Boolean,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ObjectPoolingAttribute" /> class and sets the <see cref="P:System.EnterpriseServices.ObjectPoolingAttribute.Enabled" />, <see cref="P:System.EnterpriseServices.ObjectPoolingAttribute.MaxPoolSize" />, and <see cref="P:System.EnterpriseServices.ObjectPoolingAttribute.MinPoolSize" /> properties.</summary>
      <param name="enable">true to enable object pooling; otherwise, false. </param>
      <param name="minPoolSize">The minimum pool size.</param>
      <param name="maxPoolSize">The maximum pool size.</param>
    </member>
    <member name="M:System.EnterpriseServices.ObjectPoolingAttribute.#ctor(System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ObjectPoolingAttribute" /> class and sets the <see cref="P:System.EnterpriseServices.ObjectPoolingAttribute.MaxPoolSize" /> and <see cref="P:System.EnterpriseServices.ObjectPoolingAttribute.MinPoolSize" /> properties.</summary>
      <param name="minPoolSize">The minimum pool size. </param>
      <param name="maxPoolSize">The maximum pool size. </param>
    </member>
    <member name="M:System.EnterpriseServices.ObjectPoolingAttribute.AfterSaveChanges(System.Collections.Hashtable)">
      <summary>Called internally by the .NET Framework infrastructure while installing and configuring assemblies in the COM+ catalog.</summary>
      <returns>true if the method has made changes.</returns>
      <param name="info">A hash table that contains internal objects referenced by internal keys.</param>
    </member>
    <member name="M:System.EnterpriseServices.ObjectPoolingAttribute.Apply(System.Collections.Hashtable)">
      <summary>Called internally by the .NET Framework infrastructure while applying the <see cref="T:System.EnterpriseServices.ObjectPoolingAttribute" /> class attribute to a serviced component.</summary>
      <returns>true if the method has made changes.</returns>
      <param name="info">A hash table that contains an internal object to which object pooling properties are applied, referenced by an internal key.</param>
    </member>
    <member name="P:System.EnterpriseServices.ObjectPoolingAttribute.CreationTimeout">
      <summary>Gets or sets the length of time to wait for an object to become available in the pool before throwing an exception. This value is in milliseconds.</summary>
      <returns>The time-out value in milliseconds.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ObjectPoolingAttribute.Enabled">
      <summary>Gets or sets a value that indicates whether object pooling is enabled.</summary>
      <returns>true if object pooling is enabled; otherwise, false. The default is true.</returns>
    </member>
    <member name="M:System.EnterpriseServices.ObjectPoolingAttribute.IsValidTarget(System.String)">
      <summary>Called internally by the .NET Framework infrastructure while installing and configuring assemblies in the COM+ catalog.</summary>
      <returns>true if the attribute is applied to a serviced component class.</returns>
      <param name="s">A string generated by the .NET Framework infrastructure that is checked for a special value that indicates a serviced component.</param>
    </member>
    <member name="P:System.EnterpriseServices.ObjectPoolingAttribute.MaxPoolSize">
      <summary>Gets or sets the value for the maximum size of the pool.</summary>
      <returns>The maximum number of objects in the pool.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ObjectPoolingAttribute.MinPoolSize">
      <summary>Gets or sets the value for the minimum size of the pool.</summary>
      <returns>The minimum number of objects in the pool.</returns>
    </member>
    <member name="T:System.EnterpriseServices.PartitionOption">
      <summary>Indicates the context in which to run the COM+ partition.</summary>
    </member>
    <member name="F:System.EnterpriseServices.PartitionOption.Ignore">
      <summary>The enclosed context runs in the Global Partition. <see cref="F:System.EnterpriseServices.PartitionOption.Ignore" /> is the default setting for <see cref="P:System.EnterpriseServices.ServiceConfig.PartitionOption" /> when <see cref="P:System.EnterpriseServices.ServiceConfig.Inheritance" /> is set to <see cref="F:System.EnterpriseServices.InheritanceOption.Ignore" />.</summary>
    </member>
    <member name="F:System.EnterpriseServices.PartitionOption.Inherit">
      <summary>The enclosed context runs in the current containing COM+ partition. This is the default setting for <see cref="P:System.EnterpriseServices.ServiceConfig.PartitionOption" /> when <see cref="P:System.EnterpriseServices.ServiceConfig.Inheritance" /> is set to <see cref="F:System.EnterpriseServices.InheritanceOption.Inherit" />.</summary>
    </member>
    <member name="F:System.EnterpriseServices.PartitionOption.New">
      <summary>The enclosed context runs in a COM+ partition that is different from the current containing partition.</summary>
    </member>
    <member name="T:System.EnterpriseServices.PrivateComponentAttribute">
      <summary>Identifies a component as a private component that is only seen and activated by components in the same application. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.PrivateComponentAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.PrivateComponentAttribute" /> class.</summary>
    </member>
    <member name="T:System.EnterpriseServices.PropertyLockMode">
      <summary>Specifies the mode for accessing shared properties in the shared property group manager.</summary>
    </member>
    <member name="F:System.EnterpriseServices.PropertyLockMode.SetGet">
      <summary>Locks a property during a get or set, assuring that every get or set operation on a shared property is atomic.</summary>
    </member>
    <member name="F:System.EnterpriseServices.PropertyLockMode.Method">
      <summary>Locks all the properties in the shared property group for exclusive use by the caller, as long as the caller's current method is executing.</summary>
    </member>
    <member name="T:System.EnterpriseServices.PropertyReleaseMode">
      <summary>Specifies the release mode for the properties in the new shared property group.</summary>
    </member>
    <member name="F:System.EnterpriseServices.PropertyReleaseMode.Standard">
      <summary>When all clients have released their references on the property group, the property group is automatically destroyed. This is the default COM mode.</summary>
    </member>
    <member name="F:System.EnterpriseServices.PropertyReleaseMode.Process">
      <summary>The property group is not destroyed until the process in which it was created has terminated.</summary>
    </member>
    <member name="T:System.EnterpriseServices.RegistrationConfig">
      <summary>Provides configuration information for installing assemblies into the COM+ catalog.</summary>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationConfig.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.RegistrationConfig" /> class.</summary>
    </member>
    <member name="P:System.EnterpriseServices.RegistrationConfig.Application">
      <summary>Gets or sets the name of the COM+ application in which the assembly is to be installed.</summary>
      <returns>The name of the COM+ application in which the assembly is to be installed.</returns>
    </member>
    <member name="P:System.EnterpriseServices.RegistrationConfig.ApplicationRootDirectory">
      <summary>Gets or sets the name of the root directory of the application.</summary>
      <returns>The name of the root directory of the application.</returns>
    </member>
    <member name="P:System.EnterpriseServices.RegistrationConfig.AssemblyFile">
      <summary>Gets or sets the file name of the assembly to install.</summary>
      <returns>The file name of the assembly to install.</returns>
    </member>
    <member name="P:System.EnterpriseServices.RegistrationConfig.InstallationFlags">
      <summary>Gets or sets a flag that indicates how to install the assembly.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.InstallationFlags" /> values.</returns>
    </member>
    <member name="P:System.EnterpriseServices.RegistrationConfig.Partition">
      <summary>Gets or sets the name of the COM+ partition.</summary>
      <returns>The name of the COM+ partition.</returns>
    </member>
    <member name="P:System.EnterpriseServices.RegistrationConfig.TypeLibrary">
      <summary>Gets or sets the name of the output Type Library Exporter (Tlbexp.exe) file.</summary>
      <returns>The name of the output Type Library Exporter (Tlbexp.exe) file.</returns>
    </member>
    <member name="T:System.EnterpriseServices.RegistrationErrorInfo">
      <summary>Retrieves extended error information about methods related to multiple COM+ objects. This also includes methods that install, import, and export COM+ applications and components. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.EnterpriseServices.RegistrationErrorInfo.ErrorCode">
      <summary>Gets the error code for the object or file.</summary>
      <returns>The error code for the object or file.</returns>
    </member>
    <member name="P:System.EnterpriseServices.RegistrationErrorInfo.ErrorString">
      <summary>Gets the description of the <see cref="P:System.EnterpriseServices.RegistrationErrorInfo.ErrorCode" />.</summary>
      <returns>The description of the <see cref="P:System.EnterpriseServices.RegistrationErrorInfo.ErrorCode" />.</returns>
    </member>
    <member name="P:System.EnterpriseServices.RegistrationErrorInfo.MajorRef">
      <summary>Gets the key value for the object that caused the error, if applicable.</summary>
      <returns>The key value for the object that caused the error, if applicable.</returns>
    </member>
    <member name="P:System.EnterpriseServices.RegistrationErrorInfo.MinorRef">
      <summary>Gets a precise specification of the item that caused the error, such as a property name.</summary>
      <returns>A precise specification of the item, such as a property name, that caused the error. If multiple errors occurred, or this does not apply, <see cref="P:System.EnterpriseServices.RegistrationErrorInfo.MinorRef" /> returns the string "&lt;Invalid&gt;".</returns>
    </member>
    <member name="P:System.EnterpriseServices.RegistrationErrorInfo.Name">
      <summary>Gets the name of the object or file that caused the error.</summary>
      <returns>The name of the object or file that caused the error.</returns>
    </member>
    <member name="T:System.EnterpriseServices.RegistrationException">
      <summary>The exception that is thrown when a registration error is detected.</summary>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.RegistrationException" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.RegistrationException" /> class with a specified error message.</summary>
      <param name="msg">The message displayed to the client when the exception is thrown. </param>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.RegistrationException" /> class with a specified error message and nested exception.</summary>
      <param name="msg">The message displayed to the client when the exception is thrown. </param>
      <param name="inner">The nested exception.</param>
    </member>
    <member name="P:System.EnterpriseServices.RegistrationException.ErrorInfo">
      <summary>Gets an array of <see cref="T:System.EnterpriseServices.RegistrationErrorInfo" /> objects that describe registration errors.</summary>
      <returns>The array of <see cref="T:System.EnterpriseServices.RegistrationErrorInfo" /> objects.</returns>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with the error information in <see cref="T:System.EnterpriseServices.RegistrationErrorInfo" />.</summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object that contains serialized object data. </param>
      <param name="ctx">The contextual information about the source or destination. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="info" /> parameter is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.RegistrationHelper">
      <summary>Installs and configures assemblies in the COM+ catalog. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelper.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.RegistrationHelper" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelper.InstallAssembly(System.String,System.String@,System.String,System.String@,System.EnterpriseServices.InstallationFlags)">
      <summary>Installs the named assembly in a COM+ application.</summary>
      <param name="assembly">The file name of the assembly to install. </param>
      <param name="application">The name of the COM+ application to install into. This parameter can be null. If the parameter is null and the assembly contains a <see cref="T:System.EnterpriseServices.ApplicationNameAttribute" />, then the attribute is used. Otherwise, the name of the application is generated based on the name of the assembly, then is returned.</param>
      <param name="partition">The name of the partition. This parameter can be null. </param>
      <param name="tlb">The name of the output Type Library Exporter (Tlbexp.exe) file, or a string that contains null if the registration helper is expected to generate the name. The actual name used is placed in the parameter on call completion. </param>
      <param name="installFlags">A bitwise combination of the <see cref="T:System.EnterpriseServices.InstallationFlags" /> values. </param>
      <exception cref="T:System.EnterpriseServices.RegistrationException">The input assembly does not have a strong name. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelper.InstallAssembly(System.String,System.String@,System.String@,System.EnterpriseServices.InstallationFlags)">
      <summary>Installs the named assembly in a COM+ application.</summary>
      <param name="assembly">The file name of the assembly to install. </param>
      <param name="application">The name of the COM+ application to install into. This parameter can be null. If the parameter is null and the assembly contains a <see cref="T:System.EnterpriseServices.ApplicationNameAttribute" />, then the attribute is used. Otherwise, the name of the application is generated based on the name of the assembly, then is returned.</param>
      <param name="tlb">The name of the output Type Library Exporter (Tlbexp.exe) file, or a string that contains null if the registration helper is expected to generate the name. The actual name used is placed in the parameter on call completion. </param>
      <param name="installFlags">A bitwise combination of the <see cref="T:System.EnterpriseServices.InstallationFlags" /> values. </param>
      <exception cref="T:System.EnterpriseServices.RegistrationException">The input assembly does not have a strong name. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelper.InstallAssemblyFromConfig(System.EnterpriseServices.RegistrationConfig@)">
      <summary>Installs the named assembly in a COM+ application.</summary>
      <param name="regConfig">A <see cref="T:System.EnterpriseServices.RegistrationConfig" /> identifying the assembly to install. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelper.UninstallAssembly(System.String,System.String)">
      <summary>Uninstalls the assembly from the given application.</summary>
      <param name="assembly">The file name of the assembly to uninstall. </param>
      <param name="application">If this name is not null, it is the name of the application that contains the components in the assembly. </param>
      <exception cref="T:System.EnterpriseServices.RegistrationException">The input assembly does not have a strong name. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelper.UninstallAssembly(System.String,System.String,System.String)">
      <summary>Uninstalls the assembly from the given application.</summary>
      <param name="assembly">The file name of the assembly to uninstall. </param>
      <param name="application">If this name is not null, it is the name of the application that contains the components in the assembly. </param>
      <param name="partition">The name of the partition. This parameter can be null. </param>
      <exception cref="T:System.EnterpriseServices.RegistrationException">The input assembly does not have a strong name. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelper.UninstallAssemblyFromConfig(System.EnterpriseServices.RegistrationConfig@)">
      <summary>Uninstalls the assembly from the given application.</summary>
      <param name="regConfig">A <see cref="T:System.EnterpriseServices.RegistrationConfig" /> identifying the assembly to uninstall. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.RegistrationHelperTx">
      <summary>Used by the .NET Framework infrastructure to install and configure assemblies in the COM+ catalog while maintaining a newly established transaction.</summary>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelperTx.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.RegistrationHelperTx" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelperTx.InstallAssembly(System.String,System.String@,System.String,System.String@,System.EnterpriseServices.InstallationFlags,System.Object)">
      <summary>Installs the named assembly in the COM+ catalog using transactional semantics.</summary>
      <param name="assembly">The file name of the assembly to install.</param>
      <param name="application">Either the name of the COM+ application to install into or a string that contains null.</param>
      <param name="partition">Either the name of the partition or null.</param>
      <param name="tlb">Either the name of the output Type Library Exporter (Tlbexp.exe) file or null.</param>
      <param name="installFlags">A bitwise combination of the installation flags values.</param>
      <param name="sync">A synchronization object generated by the infrastructure that can wait until the specified assembly has been configured in the COM+ catalog.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelperTx.InstallAssembly(System.String,System.String@,System.String@,System.EnterpriseServices.InstallationFlags,System.Object)">
      <summary>Installs the named assembly in the COM+ catalog using transactional semantics.</summary>
      <param name="assembly">The file name of the assembly to install.</param>
      <param name="application">Either the name of the COM+ application to install into or null.</param>
      <param name="tlb">Either the name of the output Type Library Exporter (Tlbexp.exe) file or null.</param>
      <param name="installFlags">A bitwise combination of the installation flags values.</param>
      <param name="sync">A synchronization object generated by the infrastructure that can wait until the specified assembly has been configured in the COM+ catalog.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelperTx.InstallAssemblyFromConfig(System.EnterpriseServices.RegistrationConfig@,System.Object)">
      <summary>Installs a specified assembly in the COM+ catalog using transactional semantics.</summary>
      <param name="regConfig">Configuration information for installing an assembly into the COM+ catalog.</param>
      <param name="sync">A synchronization object generated by the infrastructure that waits until the specified assembly has been configured in the COM+ catalog.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelperTx.IsInTransaction">
      <summary>Gets a value indicating whether the current context for the <see cref="T:System.EnterpriseServices.RegistrationHelperTx" /> class instance is transactional.</summary>
      <returns>true if the current context for the <see cref="T:System.EnterpriseServices.RegistrationHelperTx" /> class instance is transactional; otherwise, false.</returns>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelperTx.UninstallAssembly(System.String,System.String,System.Object)">
      <summary>Uninstalls an assembly from a COM+ application using transactional semantics.</summary>
      <param name="assembly">The file name of the assembly to uninstall.</param>
      <param name="application">Either the name of the COM+ application that contains the components in the assembly or null.</param>
      <param name="sync">A synchronization object generated by the infrastructure that can wait until the specified assembly has been uninstalled.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelperTx.UninstallAssembly(System.String,System.String,System.String,System.Object)">
      <summary>Uninstalls an assembly from a COM+ application using transactional semantics.</summary>
      <param name="assembly">The file name of the assembly to uninstall.</param>
      <param name="application">Either the name of the COM+ application that contains the components in the assembly or null.</param>
      <param name="partition">Either the name of the partition or null.</param>
      <param name="sync">A synchronization object generated by the infrastructure that can wait until the specified assembly has been uninstalled.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.RegistrationHelperTx.UninstallAssemblyFromConfig(System.EnterpriseServices.RegistrationConfig@,System.Object)">
      <summary>Uninstalls a specified assembly from a COM+ application using transactional semantics.</summary>
      <param name="regConfig">Configuration information that specifies an assembly to uninstall from an application.</param>
      <param name="sync">A synchronization object generated by the infrastructure that waits until the specified assembly has been uninstalled.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.ResourcePool">
      <summary>Stores objects in the current transaction. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ResourcePool.#ctor(System.EnterpriseServices.ResourcePool.TransactionEndDelegate)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ResourcePool" /> class.</summary>
      <param name="cb">A <see cref="T:System.EnterpriseServices.ResourcePool.TransactionEndDelegate" />, that is called when a transaction is finished. All items currently stored in the transaction are handed back to the user through the delegate. </param>
    </member>
    <member name="M:System.EnterpriseServices.ResourcePool.GetResource">
      <summary>Gets a resource from the current transaction.</summary>
      <returns>The resource object.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.ResourcePool.PutResource(System.Object)">
      <summary>Adds a resource to the current transaction.</summary>
      <returns>true if the resource object was added to the pool; otherwise, false.</returns>
      <param name="resource">The resource to add. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.ResourcePool.TransactionEndDelegate">
      <summary>Represents the method that handles the ending of a transaction.</summary>
      <param name="resource">The object that is passed back to the delegate. </param>
    </member>
    <member name="T:System.EnterpriseServices.SecureMethodAttribute">
      <summary>Ensures that the infrastructure calls through an interface for a method or for each method in a class when using the security service. Classes need to use interfaces to use security services. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.SecureMethodAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.SecureMethodAttribute" /> class.</summary>
    </member>
    <member name="T:System.EnterpriseServices.SecurityCallContext">
      <summary>Describes the chain of callers leading up to the current method call.</summary>
    </member>
    <member name="P:System.EnterpriseServices.SecurityCallContext.Callers">
      <summary>Gets a <see cref="T:System.EnterpriseServices.SecurityCallers" /> object that describes the caller.</summary>
      <returns>The <see cref="T:System.EnterpriseServices.SecurityCallers" /> object that describes the caller.</returns>
      <exception cref="T:System.Runtime.InteropServices.COMException">There is no security context. </exception>
    </member>
    <member name="P:System.EnterpriseServices.SecurityCallContext.CurrentCall">
      <summary>Gets a <see cref="T:System.EnterpriseServices.SecurityCallContext" /> object that describes the security call context.</summary>
      <returns>The <see cref="T:System.EnterpriseServices.SecurityCallContext" /> object that describes the security call context.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.EnterpriseServices.SecurityCallContext.DirectCaller">
      <summary>Gets a <see cref="T:System.EnterpriseServices.SecurityIdentity" /> object that describes the direct caller of this method.</summary>
      <returns>A <see cref="T:System.EnterpriseServices.SecurityIdentity" /> value.</returns>
    </member>
    <member name="M:System.EnterpriseServices.SecurityCallContext.IsCallerInRole(System.String)">
      <summary>Verifies that the direct caller is a member of the specified role.</summary>
      <returns>true if the direct caller is a member of the specified role; otherwise, false.</returns>
      <param name="role">The specified role. </param>
    </member>
    <member name="P:System.EnterpriseServices.SecurityCallContext.IsSecurityEnabled">
      <summary>Determines whether security checks are enabled in the current context.</summary>
      <returns>true if security checks are enabled in the current context; otherwise, false.</returns>
    </member>
    <member name="M:System.EnterpriseServices.SecurityCallContext.IsUserInRole(System.String,System.String)">
      <summary>Verifies that the specified user is in the specified role.</summary>
      <returns>true if the specified user is a member of the specified role; otherwise, false.</returns>
      <param name="user">The specified user. </param>
      <param name="role">The specified role. </param>
    </member>
    <member name="P:System.EnterpriseServices.SecurityCallContext.MinAuthenticationLevel">
      <summary>Gets the MinAuthenticationLevel value from the ISecurityCallContext collection in COM+.</summary>
      <returns>The MinAuthenticationLevel value from the ISecurityCallContext collection in COM+.</returns>
    </member>
    <member name="P:System.EnterpriseServices.SecurityCallContext.NumCallers">
      <summary>Gets the NumCallers value from the ISecurityCallContext collection in COM+.</summary>
      <returns>The NumCallers value from the ISecurityCallContext collection in COM+.</returns>
    </member>
    <member name="P:System.EnterpriseServices.SecurityCallContext.OriginalCaller">
      <summary>Gets a <see cref="T:System.EnterpriseServices.SecurityIdentity" /> that describes the original caller.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.SecurityIdentity" /> values.</returns>
    </member>
    <member name="T:System.EnterpriseServices.SecurityCallers">
      <summary>Provides an ordered collection of identities in the current call chain.</summary>
    </member>
    <member name="P:System.EnterpriseServices.SecurityCallers.Count">
      <summary>Gets the number of callers in the chain.</summary>
      <returns>The number of callers in the chain.</returns>
    </member>
    <member name="M:System.EnterpriseServices.SecurityCallers.GetEnumerator">
      <summary>Retrieves the enumeration interface for the object.</summary>
      <returns>The enumerator interface for the ISecurityCallersColl collection.</returns>
    </member>
    <member name="P:System.EnterpriseServices.SecurityCallers.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:System.EnterpriseServices.SecurityIdentity" /> item.</summary>
      <returns>A <see cref="T:System.EnterpriseServices.SecurityIdentity" /> object.</returns>
      <param name="idx">The item to access using an index number. </param>
    </member>
    <member name="T:System.EnterpriseServices.SecurityIdentity">
      <summary>Contains information that regards an identity in a COM+ call chain.</summary>
    </member>
    <member name="P:System.EnterpriseServices.SecurityIdentity.AccountName">
      <summary>Gets the name of the user described by this identity.</summary>
      <returns>The name of the user described by this identity.</returns>
    </member>
    <member name="P:System.EnterpriseServices.SecurityIdentity.AuthenticationLevel">
      <summary>Gets the authentication level of the user described by this identity.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.AuthenticationOption" /> values.</returns>
    </member>
    <member name="P:System.EnterpriseServices.SecurityIdentity.AuthenticationService">
      <summary>Gets the authentication service described by this identity.</summary>
      <returns>The authentication service described by this identity.</returns>
    </member>
    <member name="P:System.EnterpriseServices.SecurityIdentity.ImpersonationLevel">
      <summary>Gets the impersonation level of the user described by this identity.</summary>
      <returns>A <see cref="T:System.EnterpriseServices.ImpersonationLevelOption" /> value.</returns>
    </member>
    <member name="T:System.EnterpriseServices.SecurityRoleAttribute">
      <summary>Configures a role for an application or component. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.SecurityRoleAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.SecurityRoleAttribute" /> class and sets the <see cref="P:System.EnterpriseServices.SecurityRoleAttribute.Role" /> property.</summary>
      <param name="role">A security role for the application, component, interface, or method. </param>
    </member>
    <member name="M:System.EnterpriseServices.SecurityRoleAttribute.#ctor(System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.SecurityRoleAttribute" /> class and sets the <see cref="P:System.EnterpriseServices.SecurityRoleAttribute.Role" /> and <see cref="P:System.EnterpriseServices.SecurityRoleAttribute.SetEveryoneAccess" /> properties.</summary>
      <param name="role">A security role for the application, component, interface, or method. </param>
      <param name="everyone">true to require that the newly created role have the Everyone user group added as a user; otherwise, false. </param>
    </member>
    <member name="P:System.EnterpriseServices.SecurityRoleAttribute.Description">
      <summary>Gets or sets the role description.</summary>
      <returns>The description for the role.</returns>
    </member>
    <member name="P:System.EnterpriseServices.SecurityRoleAttribute.Role">
      <summary>Gets or sets the security role.</summary>
      <returns>The security role applied to an application, component, interface, or method.</returns>
    </member>
    <member name="P:System.EnterpriseServices.SecurityRoleAttribute.SetEveryoneAccess">
      <summary>Sets a value indicating whether to add the Everyone user group as a user.</summary>
      <returns>true to require that a newly created role have the Everyone user group added as a user (roles that already exist on the application are not modified); otherwise, false to suppress adding the Everyone user group as a user.</returns>
    </member>
    <member name="T:System.EnterpriseServices.ServiceConfig">
      <summary>Specifies and configures the services that are to be active in the domain which is entered when calling <see cref="M:System.EnterpriseServices.ServiceDomain.Enter(System.EnterpriseServices.ServiceConfig)" /> or creating an <see cref="T:System.EnterpriseServices.Activity" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ServiceConfig.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ServiceConfig" /> class, setting the properties to configure the desired services.</summary>
      <exception cref="T:System.PlatformNotSupportedException">
        <see cref="T:System.EnterpriseServices.ServiceConfig" /> is not supported on the current platform. </exception>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.Binding">
      <summary>Gets or sets the binding option, which indicates whether all work submitted by the activity is to be bound to only one single-threaded apartment (STA).</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.BindingOption" /> values. The default is <see cref="F:System.EnterpriseServices.BindingOption.NoBinding" />.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.BringYourOwnSystemTransaction">
      <summary>Gets or sets a <see cref="T:System.Transactions.Transaction" /> that represents an existing transaction that supplies the settings used to run the transaction identified by <see cref="T:System.EnterpriseServices.ServiceConfig" />.</summary>
      <returns>A <see cref="T:System.Transactions.Transaction" />. The default is null.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.BringYourOwnTransaction">
      <summary>Gets or sets a <see cref="T:System.EnterpriseServices.ITransaction" /> that represents an existing transaction that supplies the settings used to run the transaction identified by <see cref="T:System.EnterpriseServices.ServiceConfig" />.</summary>
      <returns>An <see cref="T:System.EnterpriseServices.ITransaction" />. The default is null.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.COMTIIntrinsicsEnabled">
      <summary>Gets or sets a value that indicates whether COM Transaction Integrator (COMTI) intrinsics are enabled.</summary>
      <returns>true if COMTI intrinsics are enabled; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.IISIntrinsicsEnabled">
      <summary>Gets or sets a value that indicates whether Internet Information Services (IIS) intrinsics are enabled.</summary>
      <returns>true if IIS intrinsics are enabled; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.Inheritance">
      <summary>Gets or sets a value that indicates whether to construct a new context based on the current context or to create a new context based solely on the information in <see cref="T:System.EnterpriseServices.ServiceConfig" />.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.InheritanceOption" /> values. The default is <see cref="F:System.EnterpriseServices.InheritanceOption.Inherit" />.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.IsolationLevel">
      <summary>Gets or sets the isolation level of the transaction.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.TransactionIsolationLevel" /> values. The default is <see cref="F:System.EnterpriseServices.TransactionIsolationLevel.Any" />.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.PartitionId">
      <summary>Gets or sets the GUID for the COM+ partition that is to be used.</summary>
      <returns>The GUID for the partition to be used. The default is a zero GUID.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.PartitionOption">
      <summary>Gets or sets a value that indicates how partitions are used for the enclosed work.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.PartitionOption" /> values. The default is <see cref="F:System.EnterpriseServices.PartitionOption.Ignore" />.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.SxsDirectory">
      <summary>Gets or sets the directory for the side-by-side assembly for the enclosed work.</summary>
      <returns>The name of the directory to be used for the side-by-side assembly. The default value is null.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.SxsName">
      <summary>Gets or sets the file name of the side-by-side assembly for the enclosed work.</summary>
      <returns>The file name of the side-by-side assembly. The default value is null.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.SxsOption">
      <summary>Gets or sets a value that indicates how to configure the side-by-side assembly.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.SxsOption" /> values. The default is <see cref="F:System.EnterpriseServices.SxsOption.Ignore" />.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.Synchronization">
      <summary>Gets or sets a value in that indicates the type of automatic synchronization requested by the component.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.SynchronizationOption" /> values. The default is <see cref="F:System.EnterpriseServices.SynchronizationOption.Disabled" />.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.ThreadPool">
      <summary>Gets or sets a value that indicates the thread pool which runs the work submitted by the activity.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.ThreadPoolOption" /> values. The default is <see cref="F:System.EnterpriseServices.ThreadPoolOption.None" />.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.TipUrl">
      <summary>Gets or sets the Transaction Internet Protocol (TIP) URL that allows the enclosed code to run in an existing transaction.</summary>
      <returns>A TIP URL. The default value is null.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.TrackingAppName">
      <summary>Gets or sets a text string that corresponds to the application ID under which tracker information is reported.</summary>
      <returns>The application ID under which tracker information is reported. The default value is null.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.TrackingComponentName">
      <summary>Gets or sets a text string that corresponds to the context name under which tracker information is reported.</summary>
      <returns>The context name under which tracker information is reported. The default value is null.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.TrackingEnabled">
      <summary>Gets or sets a value that indicates whether tracking is enabled.</summary>
      <returns>true if tracking is enabled; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.Transaction">
      <summary>Gets or sets a value that indicates how transactions are used in the enclosed work.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.TransactionOption" /> values. The default is <see cref="F:System.EnterpriseServices.TransactionOption.Disabled" />.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.TransactionDescription">
      <summary>Gets or sets the name that is used when transaction statistics are displayed.</summary>
      <returns>The name used when transaction statistics are displayed. The default value is null.</returns>
    </member>
    <member name="P:System.EnterpriseServices.ServiceConfig.TransactionTimeout">
      <summary>Gets or sets the transaction time-out for a new transaction.</summary>
      <returns>The transaction time-out, in seconds.</returns>
    </member>
    <member name="T:System.EnterpriseServices.ServicedComponent">
      <summary>Represents the base class of all classes using COM+ services.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ServicedComponent.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ServicedComponent" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ServicedComponent.Activate">
      <summary>Called by the infrastructure when the object is created or allocated from a pool. Override this method to add custom initialization code to objects.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ServicedComponent.CanBePooled">
      <summary>This method is called by the infrastructure before the object is put back into the pool. Override this method to vote on whether the object is put back into the pool.</summary>
      <returns>true if the serviced component can be pooled; otherwise, false.</returns>
    </member>
    <member name="M:System.EnterpriseServices.ServicedComponent.Construct(System.String)">
      <summary>Called by the infrastructure just after the constructor is called, passing in the constructor string. Override this method to make use of the construction string value.</summary>
      <param name="s">The construction string. </param>
    </member>
    <member name="M:System.EnterpriseServices.ServicedComponent.Deactivate">
      <summary>Called by the infrastructure when the object is about to be deactivated. Override this method to add custom finalization code to objects when just-in-time (JIT) compiled code or object pooling is used.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ServicedComponent.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.EnterpriseServices.ServicedComponent" />.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ServicedComponent.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.EnterpriseServices.ServicedComponent" /> and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; otherwise, false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.EnterpriseServices.ServicedComponent.DisposeObject(System.EnterpriseServices.ServicedComponent)">
      <summary>Finalizes the object and removes the associated COM+ reference.</summary>
      <param name="sc">The object to dispose. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlThread" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.ServicedComponent.System#EnterpriseServices#IRemoteDispatch#RemoteDispatchAutoDone(System.String)">
      <summary>Ensures that, in the COM+ context, the <see cref="T:System.EnterpriseServices.ServicedComponent" /> class object's done bit is set to true after a remote method invocation</summary>
      <returns>A string converted from a response object that implements the <see cref="T:System.Runtime.Remoting.Messaging.IMethodReturnMessage" /> interface.</returns>
      <param name="s">A string to be converted into a request object that implements the <see cref="T:System.Runtime.Remoting.Messaging.IMessage" /> interface.</param>
    </member>
    <member name="M:System.EnterpriseServices.ServicedComponent.System#EnterpriseServices#IRemoteDispatch#RemoteDispatchNotAutoDone(System.String)">
      <summary>Does not ensure that, in the COM+ context, the <see cref="T:System.EnterpriseServices.ServicedComponent" /> class object's done bit is set to true after a remote method invocation.</summary>
      <returns>A string converted from a response object implementing the <see cref="T:System.Runtime.Remoting.Messaging.IMethodReturnMessage" /> interface.</returns>
      <param name="s">A string to be converted into a request object implementing the <see cref="T:System.Runtime.Remoting.Messaging.IMessage" /> interface.</param>
    </member>
    <member name="M:System.EnterpriseServices.ServicedComponent.System#EnterpriseServices#IServicedComponentInfo#GetComponentInfo(System.Int32@,System.String[]@)">
      <summary>Obtains information about the <see cref="T:System.EnterpriseServices.ServicedComponent" /> class instance.</summary>
      <param name="infoMask">A bitmask where 0x00000001 is a key for the serviced component's process ID, 0x00000002 is a key for the application domain ID, and 0x00000004 is a key for the serviced component's remote URI.</param>
      <param name="infoArray">A string array that may contain any or all of the following, in order: the serviced component's process ID, the application domain ID, and the serviced component's remote URI.</param>
    </member>
    <member name="T:System.EnterpriseServices.ServicedComponentException">
      <summary>The exception that is thrown when an error is detected in a serviced component.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ServicedComponentException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ServicedComponentException" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ServicedComponentException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ServicedComponentException" /> class with a specified error message.</summary>
      <param name="message">The message displayed to the client when the exception is thrown. </param>
    </member>
    <member name="M:System.EnterpriseServices.ServicedComponentException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.ServicedComponentException" /> class.</summary>
      <param name="message">The message displayed to the client when the exception is thrown. </param>
      <param name="innerException">The <see cref="P:System.Exception.InnerException" />, if any, that threw the current exception. </param>
    </member>
    <member name="T:System.EnterpriseServices.ServiceDomain">
      <summary>Allows a code segment identified by <see cref="M:System.EnterpriseServices.ServiceDomain.Enter(System.EnterpriseServices.ServiceConfig)" /> and <see cref="M:System.EnterpriseServices.ServiceDomain.Leave" /> to run in its own context and behave as if it were a method that is called on an object created within the context. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.ServiceDomain.Enter(System.EnterpriseServices.ServiceConfig)">
      <summary>Creates the context specified by the <see cref="T:System.EnterpriseServices.ServiceConfig" /> object and pushes it onto the context stack to become the current context.</summary>
      <param name="cfg">A <see cref="T:System.EnterpriseServices.ServiceConfig" /> that contains the configuration information for the services to be used within the enclosed code. </param>
      <exception cref="T:System.PlatformNotSupportedException">
        <see cref="T:System.EnterpriseServices.ServiceConfig" /> is not supported on the current platform. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.ServiceDomain.Leave">
      <summary>Triggers the server and then the client side policies as if a method call were returning. The current context is then popped from the context stack, and the context that was running when <see cref="M:System.EnterpriseServices.ServiceDomain.Enter(System.EnterpriseServices.ServiceConfig)" /> was called becomes the current context.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.TransactionStatus" /> values.</returns>
      <exception cref="T:System.PlatformNotSupportedException">
        <see cref="T:System.EnterpriseServices.ServiceConfig" /> is not supported on the current platform. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.SharedProperty">
      <summary>Accesses a shared property. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.EnterpriseServices.SharedProperty.Value">
      <summary>Gets or sets the value of the shared property.</summary>
      <returns>The value of the shared property.</returns>
    </member>
    <member name="T:System.EnterpriseServices.SharedPropertyGroup">
      <summary>Represents a collection of shared properties. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.SharedPropertyGroup.CreateProperty(System.String,System.Boolean@)">
      <summary>Creates a property with the given name.</summary>
      <returns>The requested <see cref="T:System.EnterpriseServices.SharedProperty" />.</returns>
      <param name="name">The name of the new property. </param>
      <param name="fExists">Determines whether the property exists. Set to true on return if the property exists. </param>
    </member>
    <member name="M:System.EnterpriseServices.SharedPropertyGroup.CreatePropertyByPosition(System.Int32,System.Boolean@)">
      <summary>Creates a property at the given position.</summary>
      <returns>The requested <see cref="T:System.EnterpriseServices.SharedProperty" />.</returns>
      <param name="position">The index of the new property </param>
      <param name="fExists">Determines whether the property exists. Set to true on return if the property exists. </param>
    </member>
    <member name="M:System.EnterpriseServices.SharedPropertyGroup.Property(System.String)">
      <summary>Returns the property with the given name.</summary>
      <returns>The requested <see cref="T:System.EnterpriseServices.SharedProperty" />.</returns>
      <param name="name">The name of requested property. </param>
    </member>
    <member name="M:System.EnterpriseServices.SharedPropertyGroup.PropertyByPosition(System.Int32)">
      <summary>Returns the property at the given position.</summary>
      <returns>The requested <see cref="T:System.EnterpriseServices.SharedProperty" />.</returns>
      <param name="position">The index of the property. </param>
    </member>
    <member name="T:System.EnterpriseServices.SharedPropertyGroupManager">
      <summary>Controls access to shared property groups. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.SharedPropertyGroupManager.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.SharedPropertyGroupManager" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.SharedPropertyGroupManager.CreatePropertyGroup(System.String,System.EnterpriseServices.PropertyLockMode@,System.EnterpriseServices.PropertyReleaseMode@,System.Boolean@)">
      <summary>Finds or creates a property group with the given information.</summary>
      <returns>The requested <see cref="T:System.EnterpriseServices.SharedPropertyGroup" />.</returns>
      <param name="name">The name of requested property. </param>
      <param name="dwIsoMode">One of the <see cref="T:System.EnterpriseServices.PropertyLockMode" /> values. See the Remarks section for more information. </param>
      <param name="dwRelMode">One of the <see cref="T:System.EnterpriseServices.PropertyReleaseMode" /> values. See the Remarks section for more information. </param>
      <param name="fExist">When this method returns, contains true if the property already existed; false if the call created the property. </param>
    </member>
    <member name="M:System.EnterpriseServices.SharedPropertyGroupManager.GetEnumerator">
      <summary>Retrieves the enumeration interface for the collection.</summary>
      <returns>The enumerator interface for the collection.</returns>
    </member>
    <member name="M:System.EnterpriseServices.SharedPropertyGroupManager.Group(System.String)">
      <summary>Finds the property group with the given name.</summary>
      <returns>The requested <see cref="T:System.EnterpriseServices.SharedPropertyGroup" />.</returns>
      <param name="name">The name of requested property. </param>
    </member>
    <member name="T:System.EnterpriseServices.SxsOption">
      <summary>Indicates how side-by-side assemblies are configured for <see cref="T:System.EnterpriseServices.ServiceConfig" />.</summary>
    </member>
    <member name="F:System.EnterpriseServices.SxsOption.Ignore">
      <summary>Side-by-side assemblies are not used within the enclosed context. <see cref="F:System.EnterpriseServices.SxsOption.Ignore" /> is the default setting for <see cref="P:System.EnterpriseServices.ServiceConfig.SxsOption" /> when <see cref="P:System.EnterpriseServices.ServiceConfig.Inheritance" /> is set to <see cref="F:System.EnterpriseServices.InheritanceOption.Ignore" />.</summary>
    </member>
    <member name="F:System.EnterpriseServices.SxsOption.Inherit">
      <summary>The current side-by-side assembly of the enclosed context is used. <see cref="F:System.EnterpriseServices.SxsOption.Inherit" /> is the default setting for <see cref="P:System.EnterpriseServices.ServiceConfig.SxsOption" /> when <see cref="P:System.EnterpriseServices.ServiceConfig.Inheritance" /> is set to <see cref="F:System.EnterpriseServices.InheritanceOption.Inherit" />.</summary>
    </member>
    <member name="F:System.EnterpriseServices.SxsOption.New">
      <summary>A new side-by-side assembly is created for the enclosed context.</summary>
    </member>
    <member name="T:System.EnterpriseServices.SynchronizationAttribute">
      <summary>Sets the synchronization value of the component. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.SynchronizationAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.SynchronizationAttribute" /> class with the default <see cref="T:System.EnterpriseServices.SynchronizationOption" />.</summary>
    </member>
    <member name="M:System.EnterpriseServices.SynchronizationAttribute.#ctor(System.EnterpriseServices.SynchronizationOption)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.SynchronizationAttribute" /> class with the specified <see cref="T:System.EnterpriseServices.SynchronizationOption" />.</summary>
      <param name="val">One of the <see cref="T:System.EnterpriseServices.SynchronizationOption" /> values. </param>
    </member>
    <member name="P:System.EnterpriseServices.SynchronizationAttribute.Value">
      <summary>Gets the current setting of the <see cref="P:System.EnterpriseServices.SynchronizationAttribute.Value" /> property.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.SynchronizationOption" /> values. The default is Required.</returns>
    </member>
    <member name="T:System.EnterpriseServices.SynchronizationOption">
      <summary>Specifies the type of automatic synchronization requested by the component.</summary>
    </member>
    <member name="F:System.EnterpriseServices.SynchronizationOption.Disabled">
      <summary>COM+ ignores the synchronization requirements of the component when determining context for the object.</summary>
    </member>
    <member name="F:System.EnterpriseServices.SynchronizationOption.NotSupported">
      <summary>An object with this value never participates in synchronization, regardless of the status of its caller. This setting is only available for components that are non-transactional and do not use just-in-time (JIT) activation.</summary>
    </member>
    <member name="F:System.EnterpriseServices.SynchronizationOption.Supported">
      <summary>An object with this value participates in synchronization, if it exists.</summary>
    </member>
    <member name="F:System.EnterpriseServices.SynchronizationOption.Required">
      <summary>Ensures that all objects created from the component are synchronized.</summary>
    </member>
    <member name="F:System.EnterpriseServices.SynchronizationOption.RequiresNew">
      <summary>An object with this value must participate in a new synchronization where COM+ manages contexts and apartments on behalf of all components involved in the call.</summary>
    </member>
    <member name="T:System.EnterpriseServices.ThreadPoolOption">
      <summary>Indicates the thread pool in which the work, submitted by <see cref="T:System.EnterpriseServices.Activity" />, runs.</summary>
    </member>
    <member name="F:System.EnterpriseServices.ThreadPoolOption.None">
      <summary>No thread pool is used. If this value is used to configure a <see cref="T:System.EnterpriseServices.ServiceConfig" /> that is passed to an <see cref="T:System.EnterpriseServices.Activity" />, an exception is thrown.</summary>
    </member>
    <member name="F:System.EnterpriseServices.ThreadPoolOption.Inherit">
      <summary>The same type of thread pool apartment as the caller's thread apartment is used.</summary>
    </member>
    <member name="F:System.EnterpriseServices.ThreadPoolOption.STA">
      <summary>A single-threaded apartment (STA) is used.</summary>
    </member>
    <member name="F:System.EnterpriseServices.ThreadPoolOption.MTA">
      <summary>A multithreaded apartment (MTA) is used.</summary>
    </member>
    <member name="T:System.EnterpriseServices.TransactionAttribute">
      <summary>Specifies the type of transaction that is available to the attributed object. Permissible values are members of the <see cref="T:System.EnterpriseServices.TransactionOption" /> enumeration.</summary>
    </member>
    <member name="M:System.EnterpriseServices.TransactionAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.TransactionAttribute" /> class, setting the component's requested transaction type to <see cref="F:System.EnterpriseServices.TransactionOption.Required" />.</summary>
    </member>
    <member name="M:System.EnterpriseServices.TransactionAttribute.#ctor(System.EnterpriseServices.TransactionOption)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.TransactionAttribute" /> class, specifying the transaction type.</summary>
      <param name="val">The specified transaction type, a <see cref="T:System.EnterpriseServices.TransactionOption" /> value. </param>
    </member>
    <member name="P:System.EnterpriseServices.TransactionAttribute.Isolation">
      <summary>Gets or sets the transaction isolation level.</summary>
      <returns>One of the <see cref="T:System.EnterpriseServices.TransactionIsolationLevel" /> values.</returns>
    </member>
    <member name="P:System.EnterpriseServices.TransactionAttribute.Timeout">
      <summary>Gets or sets the time-out for this transaction.</summary>
      <returns>The transaction time-out in seconds.</returns>
    </member>
    <member name="P:System.EnterpriseServices.TransactionAttribute.Value">
      <summary>Gets the <see cref="T:System.EnterpriseServices.TransactionOption" /> value for the transaction, optionally disabling the transaction service.</summary>
      <returns>The specified transaction type, a <see cref="T:System.EnterpriseServices.TransactionOption" /> value.</returns>
    </member>
    <member name="T:System.EnterpriseServices.TransactionIsolationLevel">
      <summary>Specifies the value of the <see cref="T:System.EnterpriseServices.TransactionAttribute" />.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionIsolationLevel.Any">
      <summary>The isolation level for the component is obtained from the calling component's isolation level. If this is the root component, the isolation level used is <see cref="F:System.EnterpriseServices.TransactionIsolationLevel.Serializable" />.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionIsolationLevel.ReadUncommitted">
      <summary>Shared locks are issued and no exclusive locks are honored.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionIsolationLevel.ReadCommitted">
      <summary>Shared locks are held while the data is being read to avoid reading modified data, but the data can be changed before the end of the transaction, resulting in non-repeatable reads or phantom data.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionIsolationLevel.RepeatableRead">
      <summary>Locks are placed on all data that is used in a query, preventing other users from updating the data. Prevents non-repeatable reads, but phantom rows are still possible.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionIsolationLevel.Serializable">
      <summary>Prevents updating or inserting until the transaction is complete.</summary>
    </member>
    <member name="T:System.EnterpriseServices.TransactionOption">
      <summary>Specifies the automatic transaction type requested by the component.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionOption.Disabled">
      <summary>Ignores any transaction in the current context.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionOption.NotSupported">
      <summary>Creates the component in a context with no governing transaction.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionOption.Supported">
      <summary>Shares a transaction, if one exists.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionOption.Required">
      <summary>Shares a transaction, if one exists, and creates a new transaction if necessary.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionOption.RequiresNew">
      <summary>Creates the component with a new transaction, regardless of the state of the current context.</summary>
    </member>
    <member name="T:System.EnterpriseServices.TransactionStatus">
      <summary>Indicates the transaction status.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionStatus.Commited">
      <summary>The transaction has committed.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionStatus.LocallyOk">
      <summary>The transaction has neither committed nor aborted.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionStatus.NoTransaction">
      <summary>No transactions are being used through <see cref="M:System.EnterpriseServices.ServiceDomain.Enter(System.EnterpriseServices.ServiceConfig)" />.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionStatus.Aborting">
      <summary>The transaction is in the process of aborting.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionStatus.Aborted">
      <summary>The transaction is aborted.</summary>
    </member>
    <member name="T:System.EnterpriseServices.TransactionVote">
      <summary>Specifies the values allowed for transaction outcome voting.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionVote.Commit">
      <summary>Commits the current transaction.</summary>
    </member>
    <member name="F:System.EnterpriseServices.TransactionVote.Abort">
      <summary>Aborts the current transaction.</summary>
    </member>
    <member name="T:System.EnterpriseServices.XACTTRANSINFO">
      <summary>Represents a structure used in the <see cref="T:System.EnterpriseServices.ITransaction" /> interface.</summary>
    </member>
    <member name="F:System.EnterpriseServices.XACTTRANSINFO.grfRMSupported">
      <summary>Specifies zero. This field is reserved.</summary>
    </member>
    <member name="F:System.EnterpriseServices.XACTTRANSINFO.grfRMSupportedRetaining">
      <summary>Specifies zero. This field is reserved.</summary>
    </member>
    <member name="F:System.EnterpriseServices.XACTTRANSINFO.grfTCSupported">
      <summary>Represents a bitmask that indicates which grfTC flags this transaction implementation supports.</summary>
    </member>
    <member name="F:System.EnterpriseServices.XACTTRANSINFO.grfTCSupportedRetaining">
      <summary>Specifies zero. This field is reserved.</summary>
    </member>
    <member name="F:System.EnterpriseServices.XACTTRANSINFO.isoFlags">
      <summary>Specifies zero. This field is reserved.</summary>
    </member>
    <member name="F:System.EnterpriseServices.XACTTRANSINFO.isoLevel">
      <summary>Represents the isolation level associated with this transaction object. ISOLATIONLEVEL_UNSPECIFIED indicates that no isolation level was specified.</summary>
    </member>
    <member name="F:System.EnterpriseServices.XACTTRANSINFO.uow">
      <summary>Represents the unit of work associated with this transaction.</summary>
    </member>
    <member name="T:System.EnterpriseServices.CompensatingResourceManager.ApplicationCrmEnabledAttribute">
      <summary>Enables Compensating Resource Manger (CRM) on the tagged application.</summary>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.ApplicationCrmEnabledAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.CompensatingResourceManager.ApplicationCrmEnabledAttribute" /> class, setting the <see cref="P:System.EnterpriseServices.CompensatingResourceManager.ApplicationCrmEnabledAttribute.Value" /> property to true.</summary>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.ApplicationCrmEnabledAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.CompensatingResourceManager.ApplicationCrmEnabledAttribute" /> class, optionally setting the <see cref="P:System.EnterpriseServices.CompensatingResourceManager.ApplicationCrmEnabledAttribute.Value" /> property to false.</summary>
      <param name="val">true to enable Compensating Resource Manager (CRM); otherwise, false. </param>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.ApplicationCrmEnabledAttribute.Value">
      <summary>Enables or disables Compensating Resource Manager (CRM) on the tagged application.</summary>
      <returns>true if CRM is enabled; otherwise, false. The default is true.</returns>
    </member>
    <member name="T:System.EnterpriseServices.CompensatingResourceManager.Clerk">
      <summary>Writes records of transactional actions to a log.</summary>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Clerk.#ctor(System.String,System.String,System.EnterpriseServices.CompensatingResourceManager.CompensatorOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.CompensatingResourceManager.Clerk" /> class.</summary>
      <param name="compensator">The name of the compensator. </param>
      <param name="description">The description of the compensator. </param>
      <param name="flags">A bitwise combination of the <see cref="T:System.EnterpriseServices.CompensatingResourceManager.CompensatorOptions" /> values. </param>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Clerk.#ctor(System.Type,System.String,System.EnterpriseServices.CompensatingResourceManager.CompensatorOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.CompensatingResourceManager.Clerk" /> class.</summary>
      <param name="compensator">A type that represents the compensator. </param>
      <param name="description">The description of the compensator. </param>
      <param name="flags">A bitwise combination of the <see cref="T:System.EnterpriseServices.CompensatingResourceManager.CompensatorOptions" /> values. </param>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Clerk.ForceLog">
      <summary>Forces all log records to disk.</summary>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Clerk.ForceTransactionToAbort">
      <summary>Performs an immediate abort call on the transaction.</summary>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Clerk.ForgetLogRecord">
      <summary>Does not deliver the last log record that was written by this instance of this interface.</summary>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.Clerk.LogRecordCount">
      <summary>Gets the number of log records.</summary>
      <returns>The number of log records.</returns>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.Clerk.TransactionUOW">
      <summary>Gets a value representing the transaction unit of work (UOW).</summary>
      <returns>A GUID representing the UOW.</returns>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Clerk.WriteLogRecord(System.Object)">
      <summary>Writes unstructured log records to the log.</summary>
      <param name="record">The log record to write to the log. </param>
    </member>
    <member name="T:System.EnterpriseServices.CompensatingResourceManager.ClerkInfo">
      <summary>Contains information describing an active Compensating Resource Manager (CRM) Clerk object.</summary>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.ClerkInfo.ActivityId">
      <summary>Gets the activity ID of the current Compensating Resource Manager (CRM) Worker.</summary>
      <returns>Gets the activity ID of the current Compensating Resource Manager (CRM) Worker.</returns>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.ClerkInfo.Clerk">
      <summary>Gets <see cref="F:System.Runtime.InteropServices.UnmanagedType.IUnknown" /> for the current Clerk.</summary>
      <returns>
        <see cref="F:System.Runtime.InteropServices.UnmanagedType.IUnknown" /> for the current Clerk.</returns>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.ClerkInfo.Compensator">
      <summary>Gets the ProgId of the Compensating Resource Manager (CRM) Compensator for the current CRM Clerk.</summary>
      <returns>The ProgId of the CRM Compensator for the current CRM Clerk.</returns>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.ClerkInfo.Description">
      <summary>Gets the description of the Compensating Resource Manager (CRM) Compensator for the current CRM Clerk. The description string is the string that was provided by the ICrmLogControl::RegisterCompensator method.</summary>
      <returns>The description of the CRM Compensator for the current CRM Clerk.</returns>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.ClerkInfo.InstanceId">
      <summary>Gets the instance class ID (CLSID) of the current Compensating Resource Manager (CRM) Clerk.</summary>
      <returns>The instance CLSID of the current CRM Clerk.</returns>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.ClerkInfo.TransactionUOW">
      <summary>Gets the unit of work (UOW) of the transaction for the current Compensating Resource Manager (CRM) Clerk.</summary>
      <returns>The UOW of the transaction for the current CRM Clerk.</returns>
    </member>
    <member name="T:System.EnterpriseServices.CompensatingResourceManager.ClerkMonitor">
      <summary>Contains a snapshot of all Clerks active in the process.</summary>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.ClerkMonitor.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.CompensatingResourceManager.ClerkMonitor" /> class.</summary>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.ClerkMonitor.Count">
      <summary>Gets the count of the Clerk monitors in the Compensating Resource Manager (CRM) monitor collection.</summary>
      <returns>The number of Clerk monitors in the CRM monitor collection.</returns>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.ClerkMonitor.GetEnumerator">
      <summary>Returns the enumeration of the clerks in the Compensating Resource Manager (CRM) monitor collection.</summary>
      <returns>An enumerator describing the clerks in the collection.</returns>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.ClerkMonitor.Item(System.Int32)">
      <summary>Gets the <see cref="T:System.EnterpriseServices.CompensatingResourceManager.ClerkInfo" /> object for this <see cref="T:System.EnterpriseServices.CompensatingResourceManager.ClerkMonitor" />.</summary>
      <returns>The <see cref="T:System.EnterpriseServices.CompensatingResourceManager.ClerkInfo" /> object for this <see cref="T:System.EnterpriseServices.CompensatingResourceManager.ClerkMonitor" />.</returns>
      <param name="index">The integer index that identifies the <see cref="T:System.EnterpriseServices.CompensatingResourceManager.ClerkMonitor" />. </param>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.ClerkMonitor.Item(System.String)">
      <summary>Gets the <see cref="T:System.EnterpriseServices.CompensatingResourceManager.ClerkInfo" /> object for this <see cref="T:System.EnterpriseServices.CompensatingResourceManager.ClerkMonitor" />.</summary>
      <returns>The <see cref="T:System.EnterpriseServices.CompensatingResourceManager.ClerkInfo" /> object for this <see cref="T:System.EnterpriseServices.CompensatingResourceManager.ClerkMonitor" />.</returns>
      <param name="index">The numeric index that identifies the <see cref="T:System.EnterpriseServices.CompensatingResourceManager.ClerkMonitor" />. </param>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.ClerkMonitor.Populate">
      <summary>Gets the Clerks collection object, which is a snapshot of the current state of the Clerks.</summary>
    </member>
    <member name="T:System.EnterpriseServices.CompensatingResourceManager.Compensator">
      <summary>Represents the base class for all Compensating Resource Manager (CRM) Compensators.</summary>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Compensator.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.CompensatingResourceManager.Compensator" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Compensator.AbortRecord(System.EnterpriseServices.CompensatingResourceManager.LogRecord)">
      <summary>Delivers a log record to the Compensating Resource Manager (CRM) Compensator during the abort phase.</summary>
      <returns>true if the delivered record should be forgotten; otherwise, false.</returns>
      <param name="rec">The log record to be delivered. </param>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Compensator.BeginAbort(System.Boolean)">
      <summary>Notifies the Compensating Resource Manager (CRM) Compensator of the abort phase of the transaction completion, and the upcoming delivery of records.</summary>
      <param name="fRecovery">true to begin abort phase; otherwise, false. </param>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Compensator.BeginCommit(System.Boolean)">
      <summary>Notifies the Compensating Resource Manager (CRM) Compensator of the commit phase of the transaction completion and the upcoming delivery of records.</summary>
      <param name="fRecovery">true to begin commit phase; otherwise, false. </param>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Compensator.BeginPrepare">
      <summary>Notifies the Compensating Resource Manager (CRM) Compensator of the prepare phase of the transaction completion and the upcoming delivery of records.</summary>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.Compensator.Clerk">
      <summary>Gets a value representing the Compensating Resource Manager (CRM) <see cref="T:System.EnterpriseServices.CompensatingResourceManager.Clerk" /> object.</summary>
      <returns>The <see cref="T:System.EnterpriseServices.CompensatingResourceManager.Clerk" /> object.</returns>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Compensator.CommitRecord(System.EnterpriseServices.CompensatingResourceManager.LogRecord)">
      <summary>Delivers a log record in forward order during the commit phase.</summary>
      <returns>true if the delivered record should be forgotten; otherwise, false.</returns>
      <param name="rec">The log record to forward. </param>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Compensator.EndAbort">
      <summary>Notifies the Compensating Resource Manager (CRM) Compensator that it has received all the log records available during the abort phase.</summary>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Compensator.EndCommit">
      <summary>Notifies the Compensating Resource Manager (CRM) Compensator that it has delivered all the log records available during the commit phase.</summary>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Compensator.EndPrepare">
      <summary>Notifies the Compensating Resource Manager (CRM) Compensator that it has had all the log records available during the prepare phase.</summary>
      <returns>true if successful; otherwise, false.</returns>
    </member>
    <member name="M:System.EnterpriseServices.CompensatingResourceManager.Compensator.PrepareRecord(System.EnterpriseServices.CompensatingResourceManager.LogRecord)">
      <summary>Delivers a log record in forward order during the prepare phase.</summary>
      <returns>true if the delivered record should be forgotten; otherwise, false.</returns>
      <param name="rec">The log record to forward. </param>
    </member>
    <member name="T:System.EnterpriseServices.CompensatingResourceManager.CompensatorOptions">
      <summary>Specifies flags that control which phases of transaction completion should be received by the Compensating Resource Manager (CRM) Compensator, and whether recovery should fail if questionable transactions remain after recovery has been attempted.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.CompensatorOptions.PreparePhase">
      <summary>Represents the prepare phase.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.CompensatorOptions.CommitPhase">
      <summary>Represents the commit phase.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.CompensatorOptions.AbortPhase">
      <summary>Represents the abort phase.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.CompensatorOptions.AllPhases">
      <summary>Represents all phases.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.CompensatorOptions.FailIfInDoubtsRemain">
      <summary>Fails if in-doubt transactions remain after recovery has been attempted.</summary>
    </member>
    <member name="T:System.EnterpriseServices.CompensatingResourceManager.LogRecord">
      <summary>Represents an unstructured log record delivered as a COM+ CrmLogRecordRead structure. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.LogRecord.Flags">
      <summary>Gets a value that indicates when the log record was written.</summary>
      <returns>A bitwise combination of the <see cref="T:System.EnterpriseServices.CompensatingResourceManager.LogRecordFlags" /> values which provides information about when this record was written.</returns>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.LogRecord.Record">
      <summary>Gets the log record user data.</summary>
      <returns>A single BLOB that contains the user data.</returns>
    </member>
    <member name="P:System.EnterpriseServices.CompensatingResourceManager.LogRecord.Sequence">
      <summary>The sequence number of the log record.</summary>
      <returns>An integer value that specifies the sequence number of the log record.</returns>
    </member>
    <member name="T:System.EnterpriseServices.CompensatingResourceManager.LogRecordFlags">
      <summary>Describes the origin of a Compensating Resource Manager (CRM) log record.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.LogRecordFlags.ForgetTarget">
      <summary>Indicates the delivered record should be forgotten.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.LogRecordFlags.WrittenDuringPrepare">
      <summary>Log record was written during prepare.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.LogRecordFlags.WrittenDuringCommit">
      <summary>Log record was written during commit.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.LogRecordFlags.WrittenDuringAbort">
      <summary>Log record was written during abort.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.LogRecordFlags.WrittenDurringRecovery">
      <summary>Log record was written during recovery.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.LogRecordFlags.WrittenDuringReplay">
      <summary>Log record was written during replay.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.LogRecordFlags.ReplayInProgress">
      <summary>Log record was written when replay was in progress.</summary>
    </member>
    <member name="T:System.EnterpriseServices.CompensatingResourceManager.TransactionState">
      <summary>Specifies the state of the current Compensating Resource Manager (CRM) transaction.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.TransactionState.Active">
      <summary>The transaction is active.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.TransactionState.Committed">
      <summary>The transaction is commited.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.TransactionState.Aborted">
      <summary>The transaction is aborted.</summary>
    </member>
    <member name="F:System.EnterpriseServices.CompensatingResourceManager.TransactionState.Indoubt">
      <summary>The transaction is in-doubt.</summary>
    </member>
    <member name="T:System.EnterpriseServices.Internal.AppDomainHelper">
      <summary>Switches into the given application domain, which the object should be bound to, and does a callback on the given function.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.AppDomainHelper.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Internal.AppDomainHelper" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.AppDomainHelper.Finalize">
      <summary>Releases unmanaged resources and performs other cleanup operations before the <see cref="T:System.ComponentModel.Component" /> is reclaimed by garbage collection.</summary>
    </member>
    <member name="T:System.EnterpriseServices.Internal.AssemblyLocator">
      <summary>Locates an assembly and returns information about its modules.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.AssemblyLocator.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Internal.AssemblyLocator" /> class.</summary>
    </member>
    <member name="T:System.EnterpriseServices.Internal.ClientRemotingConfig">
      <summary>Defines a static <see cref="M:System.EnterpriseServices.Internal.ClientRemotingConfig.Write(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)" /> method that creates a client remoting configuration file for a client type library.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ClientRemotingConfig.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Internal.ClientRemotingConfig" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ClientRemotingConfig.Write(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>Creates a client remoting configuration file for a client type library in a SOAP-enabled COM+ application.</summary>
      <returns>true if the client remoting configuration file was successfully created; otherwise false.</returns>
      <param name="DestinationDirectory">The folder in which to create the configuration file.</param>
      <param name="VRoot">The name of the virtual root.</param>
      <param name="BaseUrl">The base URL that contains the virtual root.</param>
      <param name="AssemblyName">The display name of the assembly that contains common language runtime (CLR) metadata corresponding to the type library.</param>
      <param name="TypeName">The fully qualified name of the assembly that contains CLR metadata corresponding to the type library.</param>
      <param name="ProgId">The programmatic identifier of the class.</param>
      <param name="Mode">The activation mode.</param>
      <param name="Transport">Not used. Specify null for this parameter.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="RemotingConfiguration" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.Internal.ClrObjectFactory">
      <summary>Activates SOAP-enabled COM+ application proxies from a client.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ClrObjectFactory.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Internal.ClrObjectFactory" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ClrObjectFactory.CreateFromAssembly(System.String,System.String,System.String)">
      <summary>Activates a remote assembly through .NET remoting, using the assembly's configuration file.</summary>
      <returns>An instance of the <see cref="T:System.Object" /> that represents the type, with culture, arguments, and binding and activation attributes set to null, or null if the <paramref name="TypeName" /> parameter is not found.</returns>
      <param name="AssemblyName">The name of the assembly to activate. </param>
      <param name="TypeName">The name of the type to activate. </param>
      <param name="Mode">Not used. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.Runtime.InteropServices.COMException">The class is not registered. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence, RemotingConfiguration" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ClrObjectFactory.CreateFromMailbox(System.String,System.String)">
      <summary>Activates a remote assembly through .NET remoting, using the remote assembly's mailbox. Currently not implemented; throws a <see cref="T:System.Runtime.InteropServices.COMException" /> if called.</summary>
      <returns>This method throws an exception if called.</returns>
      <param name="Mailbox">A mailbox on the Web service. </param>
      <param name="Mode">Not used. </param>
      <exception cref="T:System.Runtime.InteropServices.COMException">Simple Mail Transfer Protocol (SMTP) is not implemented. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ClrObjectFactory.CreateFromVroot(System.String,System.String)">
      <summary>Activates a remote assembly through .NET remoting, using the virtual root URL of the remote assembly.</summary>
      <returns>An instance of the <see cref="T:System.Object" /> representing the type, with culture, arguments, and binding and activation attributes set to null, or null if the assembly identified by the <paramref name="VrootUrl" /> parameter is not found.</returns>
      <param name="VrootUrl">The virtual root URL of the object to be activated. </param>
      <param name="Mode">Not used. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.Runtime.InteropServices.COMException">The thread token could not be opened. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ClrObjectFactory.CreateFromWsdl(System.String,System.String)">
      <summary>Activates a remote assembly through .NET remoting, using the Web Services Description Language (WSDL) of the XML Web service.</summary>
      <returns>An instance of the <see cref="T:System.Object" /> representing the type, with culture, arguments, and binding and activation attributes set to null, or null if the assembly identified by the <paramref name="WsdlUrl" /> parameter is not found.</returns>
      <param name="WsdlUrl">The WSDL URL of the Web service. </param>
      <param name="Mode">Not used. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.Runtime.InteropServices.COMException">The thread token could not be opened. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.Internal.ComManagedImportUtil">
      <summary>Identifies and installs components in the COM+ catalog.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ComManagedImportUtil.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Internal.ComManagedImportUtil" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ComManagedImportUtil.GetComponentInfo(System.String,System.String@,System.String@)">
      <summary>Gets the component information from the assembly.</summary>
      <param name="assemblyPath">The path to the assembly. </param>
      <param name="numComponents">When this method returns, this parameter contains the number of components in the assembly. </param>
      <param name="componentInfo">When this method returns, this parameter contains the information about the components. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="assemblyPath" /> is an empty string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.-or- The system could not retrieve the absolute path. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permissions. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="assemblyPath" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="assemblyPath" /> contains a colon (":"). </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ComManagedImportUtil.InstallAssembly(System.String,System.String,System.String)">
      <summary>Installs an assembly into a COM+ application.</summary>
      <param name="asmpath">The path for the assembly. </param>
      <param name="parname">The COM+ partition name. </param>
      <param name="appname">The COM+ application name. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.EnterpriseServices.RegistrationException">The input assembly does not have a strong name. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.Internal.ComSoapPublishError">
      <summary>Error handler for publishing SOAP-enabled services in COM+ applications.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ComSoapPublishError.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Internal.ComSoapPublishError" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ComSoapPublishError.Report(System.String)">
      <summary>Writes to an event log an error encountered while publishing SOAP-enabled COM interfaces in COM+ applications.</summary>
      <param name="s">An error message to be written to the event log.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.Internal.GenerateMetadata">
      <summary>Generates common language runtime (CLR) metadata for a COM+ component.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.GenerateMetadata.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Internal.GenerateMetadata" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.GenerateMetadata.Generate(System.String,System.String)">
      <summary>Generates, or locates, an assembly that contains common language runtime (CLR) metadata for a COM+ component represented by the specified type library.</summary>
      <returns>The generated assembly name; otherwise, an empty string if the inputs are invalid.</returns>
      <param name="strSrcTypeLib">The name of the type library for which to generate an assembly.</param>
      <param name="outPath">The folder in which to generate an assembly or to locate an already existing assembly.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.GenerateMetadata.GenerateMetaData(System.String,System.String,System.Byte[],System.Reflection.StrongNameKeyPair)">
      <summary>Generates, or locates, an assembly that contains common language runtime (CLR) metadata for a COM+ component represented by the specified type library, signs the assembly with a strong-named key pair, and installs it in the global assembly cache.</summary>
      <returns>The generated assembly name; otherwise, an empty string if the inputs are invalid.</returns>
      <param name="strSrcTypeLib">The name of the type library for which to generate an assembly.</param>
      <param name="outPath">The folder in which to generate an assembly or to locate an already existing assembly.</param>
      <param name="PublicKey">A public key used to import type library information into an assembly.</param>
      <param name="KeyPair">A strong-named key pair used to sign the generated assembly.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.GenerateMetadata.GenerateSigned(System.String,System.String,System.Boolean,System.String@)">
      <summary>Generates, or locates, an assembly that contains common language runtime (CLR) metadata for a COM+ component represented by the specified type library, signs the assembly with a strong-named key pair, and installs it in the global assembly cache.</summary>
      <returns>The generated assembly name; otherwise, an empty string if the inputs are invalid.</returns>
      <param name="strSrcTypeLib">The name of the type library for which to generate an assembly.</param>
      <param name="outPath">The folder in which to generate an assembly or to locate an already existing assembly.</param>
      <param name="InstallGac">Ignored. </param>
      <param name="Error">A string to which an error message can be written.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.GenerateMetadata.SearchPath(System.String,System.String,System.String,System.Int32,System.String,System.Int32[])">
      <summary>Searches for a specified file in a specified path.</summary>
      <returns>If the search succeeds, the return value is the length of the string copied to <paramref name="buffer" />. If the search fails, the return value is 0.</returns>
      <param name="path">The path to be searched for the file.</param>
      <param name="fileName">The name of the file for which to search.</param>
      <param name="extension">An extension to be added to the file name when searching for the file.</param>
      <param name="numBufferChars">The size of the buffer that receives the valid path and file name.</param>
      <param name="buffer">The buffer that receives the path and file name of the file found.</param>
      <param name="filePart">The variable that receives the address of the last component of the valid path and file name.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.Internal.IClrObjectFactory">
      <summary>Activates SOAP-enabled COM+ application proxies from a client.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IClrObjectFactory.CreateFromAssembly(System.String,System.String,System.String)">
      <summary>Activates a remote assembly through .NET remoting, using the assembly's configuration file.</summary>
      <returns>An instance of the <see cref="T:System.Object" /> representing the type, with culture, arguments, and binding and activation attributes set to null, or null if the <paramref name="type" /> parameter is not found.</returns>
      <param name="assembly">The name of the assembly to activate. </param>
      <param name="type">The name of the type to activate. </param>
      <param name="mode">Not used. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.Runtime.InteropServices.COMException">The class is not registered. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IClrObjectFactory.CreateFromMailbox(System.String,System.String)">
      <summary>Activates a remote assembly through .NET remoting, using the remote assembly's mailbox. Currently not implemented; throws a <see cref="T:System.Runtime.InteropServices.COMException" /> if called.</summary>
      <returns>This method throws an exception if called.</returns>
      <param name="Mailbox">A mailbox on the Web service. </param>
      <param name="Mode">Not used. </param>
      <exception cref="T:System.Runtime.InteropServices.COMException">Simple Mail Transfer Protocol (SMTP) is not implemented. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IClrObjectFactory.CreateFromVroot(System.String,System.String)">
      <summary>Activates a remote assembly through .NET remoting, using the virtual root URL of the remote assembly.</summary>
      <returns>An instance of the <see cref="T:System.Object" /> representing the type, with culture, arguments, and binding and activation attributes set to null, or null if the assembly identified by the <paramref name="VrootUrl" /> parameter is not found.</returns>
      <param name="VrootUrl">The virtual root URL of the remote object. </param>
      <param name="Mode">Not used. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.Runtime.InteropServices.COMException">The thread token could not be opened. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IClrObjectFactory.CreateFromWsdl(System.String,System.String)">
      <summary>Activates a remote assembly through .NET remoting, using the Web Services Description Language (WSDL) of the XML Web service.</summary>
      <returns>An instance of the <see cref="T:System.Object" /> representing the type, with culture, arguments, and binding and activation attributes set to null, or null if the assembly identified by the <paramref name="WsdlUrl" /> parameter is not found.</returns>
      <param name="WsdlUrl">The WSDL URL of the Web service. </param>
      <param name="Mode">Not used. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.Runtime.InteropServices.COMException">The thread token could not be opened. </exception>
    </member>
    <member name="T:System.EnterpriseServices.Internal.IComManagedImportUtil">
      <summary>Identifies and installs components in the COM+ catalog.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComManagedImportUtil.GetComponentInfo(System.String,System.String@,System.String@)">
      <summary>Gets the component information from the assembly.</summary>
      <param name="assemblyPath">The path to the assembly. </param>
      <param name="numComponents">When this method returns, this parameter contains the number of components in the assembly. </param>
      <param name="componentInfo">When this method returns, this parameter contains the information about the components. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="assemblyPath" /> is an empty string (""), contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.-or- The system could not retrieve the absolute path. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permissions. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="assemblyPath" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="assemblyPath" /> contains a colon (":"). </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComManagedImportUtil.InstallAssembly(System.String,System.String,System.String)">
      <summary>Installs an assembly into a COM+ application.</summary>
      <param name="filename">The path for the assembly. </param>
      <param name="parname">The COM+ partition name. </param>
      <param name="appname">The COM+ application name. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.EnterpriseServices.RegistrationException">The input assembly does not have a strong name. </exception>
    </member>
    <member name="T:System.EnterpriseServices.Internal.IComSoapIISVRoot">
      <summary>Interface definition for creating and deleting Internet Information Services (IIS) 6.0 virtual roots.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapIISVRoot.Create(System.String,System.String,System.String,System.String@)">
      <summary>Creates an Internet Information Services (IIS) virtual root.</summary>
      <param name="RootWeb">The root Web server.</param>
      <param name="PhysicalDirectory">The physical path of the virtual root, which corresponds to <paramref name="PhysicalPath" /> from the <see cref="M:System.EnterpriseServices.Internal.IComSoapPublisher.CreateVirtualRoot(System.String,System.String,System.String@,System.String@,System.String@,System.String@)" /> method.</param>
      <param name="VirtualDirectory">The name of the virtual root, which corresponds to <paramref name="VirtualRoot" /> from the <see cref="M:System.EnterpriseServices.Internal.IComSoapPublisher.CreateVirtualRoot(System.String,System.String,System.String@,System.String@,System.String@,System.String@)" /> method.</param>
      <param name="Error">A string to which an error message can be written.</param>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapIISVRoot.Delete(System.String,System.String,System.String,System.String@)">
      <summary>Deletes an Internet Information Services (IIS) virtual root.</summary>
      <param name="RootWeb">The root Web server.</param>
      <param name="PhysicalDirectory">The physical path of the virtual root.</param>
      <param name="VirtualDirectory">The name of the virtual root.</param>
      <param name="Error">A string to which an error message can be written.</param>
    </member>
    <member name="T:System.EnterpriseServices.Internal.IComSoapMetadata">
      <summary>Specifies methods for generating common language runtime (CLR) metadata for a COM+ component.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapMetadata.Generate(System.String,System.String)">
      <summary>Generates an assembly that contains common language runtime (CLR) metadata for a COM+ component represented by the specified type library.</summary>
      <returns>The generated assembly name.</returns>
      <param name="SrcTypeLibFileName">The name of the type library for which to generate an assembly.</param>
      <param name="OutPath">The folder in which to generate an assembly.</param>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapMetadata.GenerateSigned(System.String,System.String,System.Boolean,System.String@)">
      <summary>Generates an assembly that contains common language runtime (CLR) metadata for a COM+ component represented by the specified type library, signs the assembly with a strong-named key pair, and installs it in the global assembly cache.</summary>
      <returns>The generated assembly name.</returns>
      <param name="SrcTypeLibFileName">The name of the type library for which to generate an assembly.</param>
      <param name="OutPath">The folder in which to generate an assembly.</param>
      <param name="InstallGac">A flag that indicates whether to install the assembly in the global assembly cache.</param>
      <param name="Error">A string to which an error message can be written.</param>
    </member>
    <member name="T:System.EnterpriseServices.Internal.IComSoapPublisher">
      <summary>Publishes COM interfaces for SOAP-enabled COM+ applications.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapPublisher.CreateMailBox(System.String,System.String,System.String@,System.String@,System.String@,System.String@)">
      <summary>Creates a SOAP-enabled COM+ application mailbox at a specified URL. Not fully implemented.</summary>
      <param name="RootMailServer">The URL for the root mail server. </param>
      <param name="MailBox">The mailbox to create. </param>
      <param name="SmtpName">When this method returns, this parameter contains the name of the Simple Mail Transfer Protocol (SMTP) server containing the mailbox. </param>
      <param name="Domain">When this method returns, this parameter contains the domain of the SMTP server. </param>
      <param name="PhysicalPath">When this method returns, this parameter contains the file system path for the mailbox. </param>
      <param name="Error">When this method returns, this parameter contains an error message if a problem was encountered. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapPublisher.CreateVirtualRoot(System.String,System.String,System.String@,System.String@,System.String@,System.String@)">
      <summary>Creates a SOAP-enabled COM+ application virtual root.</summary>
      <param name="Operation">The operation to perform. </param>
      <param name="FullUrl">The complete URL address for the virtual root. </param>
      <param name="BaseUrl">When this method returns, this parameter contains the base URL address. </param>
      <param name="VirtualRoot">When this method returns, this parameter contains the name of the virtual root. </param>
      <param name="PhysicalPath">When this method returns, this parameter contains the file path for the virtual root. </param>
      <param name="Error">When this method returns, this parameter contains an error message if a problem was encountered. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code.-or- The caller does not have permission to access Domain Name System (DNS) information. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="FullUrl" /> is null. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error is encountered when resolving the local host name. </exception>
      <exception cref="T:System.UriFormatException">
        <paramref name="FullUrl" /> is empty.-or- The scheme specified in <paramref name="FullUrl" /> is invalid.-or- <paramref name="FullUrl" /> contains more than two consecutive slashes.-or- The password specified in <paramref name="FullUrl" /> is invalid.-or- The host name specified in <paramref name="FullUrl" /> is invalid.-or- The file name specified in <paramref name="FullUrl" /> is invalid. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapPublisher.DeleteMailBox(System.String,System.String,System.String@)">
      <summary>Deletes a SOAP-enabled COM+ application mailbox at a specified URL. Not fully implemented.</summary>
      <param name="RootMailServer">The URL for the root mail server. </param>
      <param name="MailBox">The mailbox to delete. </param>
      <param name="Error">When this method returns, this parameter contains an error message if a problem was encountered. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapPublisher.DeleteVirtualRoot(System.String,System.String,System.String@)">
      <summary>Deletes a SOAP-enabled COM+ application virtual root. Not fully implemented.</summary>
      <param name="RootWebServer">The root Web server. </param>
      <param name="FullUrl">The complete URL address for the virtual root. </param>
      <param name="Error">When this method returns, this parameter contains an error message if a problem was encountered. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapPublisher.GacInstall(System.String)">
      <summary>Installs an assembly in the global assembly cache.</summary>
      <param name="AssemblyPath">The file system path for the assembly. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapPublisher.GacRemove(System.String)">
      <summary>Removes an assembly from the global assembly cache.</summary>
      <param name="AssemblyPath">The file system path for the assembly. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="AssemblyPath" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="AssemblyPath" /> is empty. </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="AssemblyPath" /> is not found. </exception>
      <exception cref="T:System.IO.FileLoadException">An assembly or module was loaded twice with two different evidences. </exception>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="AssemblyPath" /> is not a valid assembly. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapPublisher.GetAssemblyNameForCache(System.String,System.String@)">
      <summary>Returns the full path for a strong-named signed generated assembly in the SoapCache directory.</summary>
      <param name="TypeLibPath">The path for the file that contains the typelib. </param>
      <param name="CachePath">When this method returns, this parameter contains the full path of the proxy assembly in the SoapCache directory. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="TypeLibPath" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">The file name is empty, contains only white spaces, or contains invalid characters. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to <paramref name="TypeLibPath" /> is denied. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="TypeLibPath" /> contains a colon (:) in the middle of the string. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapPublisher.GetTypeNameFromProgId(System.String,System.String)">
      <summary>Reflects over an assembly and returns the type name that matches the ProgID.</summary>
      <returns>The type name that matches the ProgID.</returns>
      <param name="AssemblyPath">The file system path for the assembly. </param>
      <param name="ProgId">The programmatic identifier of the class. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapPublisher.ProcessClientTlb(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String@,System.String@,System.String@)">
      <summary>Processes a client type library, creating a configuration file on the client.</summary>
      <param name="ProgId">The programmatic identifier of the class. </param>
      <param name="SrcTlbPath">The path for the file that contains the typelib. </param>
      <param name="PhysicalPath">The Web application directory. </param>
      <param name="VRoot">The name of the virtual root. </param>
      <param name="BaseUrl">The base URL that contains the virtual root. </param>
      <param name="Mode">The activation mode. </param>
      <param name="Transport">Not used. Specify null for this parameter.</param>
      <param name="AssemblyName">When this method returns, this parameter contains the display name of the assembly. </param>
      <param name="TypeName">When this method returns, this parameter contains the fully-qualified type name of the assembly. </param>
      <param name="Error">When this method returns, this parameter contains an error message if a problem was encountered. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapPublisher.ProcessServerTlb(System.String,System.String,System.String,System.String,System.String@,System.String@,System.String@)">
      <summary>Processes a server type library, either adding or deleting component entries to the Web.config and Default.disco files. Generates a proxy if necessary.</summary>
      <param name="ProgId">The programmatic identifier of the class. </param>
      <param name="SrcTlbPath">The path for the file that contains the type library. </param>
      <param name="PhysicalPath">The Web application directory. </param>
      <param name="Operation">The operation to perform. </param>
      <param name="AssemblyName">When this method returns, this parameter contains the display name of the assembly. </param>
      <param name="TypeName">When this method returns, this parameter contains the fully-qualified type name of the assembly. </param>
      <param name="Error">When this method returns, this parameter contains an error message if a problem was encountered. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.EnterpriseServices.ServicedComponentException">The <paramref name="SrcTlbPath" /> parameter referenced scrobj.dll; therefore, SOAP publication of script components is not supported. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapPublisher.RegisterAssembly(System.String)">
      <summary>Registers an assembly for COM interop.</summary>
      <param name="AssemblyPath">The file system path for the assembly. </param>
      <exception cref="T:System.EnterpriseServices.RegistrationException">The input assembly does not have a strong name. </exception>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code.-or- A codebase that does not start with "file://" was specified without the required <see cref="T:System.Net.WebPermission" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="AssemblyPath" /> is null. </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="AssemblyPath" /> is not found, or a file name extension is not specified. </exception>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="AssemblyPath" /> is not a valid assembly. </exception>
      <exception cref="T:System.IO.FileLoadException">An assembly or module was loaded twice with two different evidences, or the assembly name is longer than MAX_PATH characters. </exception>
      <exception cref="T:System.InvalidOperationException">A method marked with <see cref="T:System.Runtime.InteropServices.ComUnregisterFunctionAttribute" /> is not static.-or- There is more than one method marked with <see cref="T:System.Runtime.InteropServices.ComUnregisterFunctionAttribute" /> at a given level of the hierarchy.-or- The signature of the method marked with <see cref="T:System.Runtime.InteropServices.ComUnregisterFunctionAttribute" /> is not valid. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IComSoapPublisher.UnRegisterAssembly(System.String)">
      <summary>Unregisters a COM interop assembly.</summary>
      <param name="AssemblyPath">The file system path for the assembly. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code.-or- A codebase that does not start with "file://" was specified without the required <see cref="T:System.Net.WebPermission" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="AssemblyPath" /> is null. </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="AssemblyPath" /> is not found, or a file name extension is not specified. </exception>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="AssemblyPath" /> is not a valid assembly. </exception>
      <exception cref="T:System.IO.FileLoadException">An assembly or module was loaded twice with two different evidences, or the assembly name is longer than MAX_PATH characters. </exception>
      <exception cref="T:System.InvalidOperationException">A method marked with <see cref="T:System.Runtime.InteropServices.ComUnregisterFunctionAttribute" /> is not static.-or- There is more than one method marked with <see cref="T:System.Runtime.InteropServices.ComUnregisterFunctionAttribute" /> at a given level of the hierarchy.-or- The signature of the method marked with <see cref="T:System.Runtime.InteropServices.ComUnregisterFunctionAttribute" /> is not valid. </exception>
    </member>
    <member name="T:System.EnterpriseServices.Internal.IISVirtualRoot">
      <summary>Creates and deletes Internet Information Services (IIS) 6.0 virtual roots.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IISVirtualRoot.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Internal.IISVirtualRoot" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IISVirtualRoot.Create(System.String,System.String,System.String,System.String@)">
      <summary>Creates an Internet Information Services (IIS) virtual root.</summary>
      <param name="RootWeb">A string with the value "IIS://localhost/W3SVC/1/ROOT" representing the root Web server.</param>
      <param name="inPhysicalDirectory">The physical path of the virtual root, which corresponds to <paramref name="PhysicalPath" /> from the <see cref="M:System.EnterpriseServices.Internal.Publish.CreateVirtualRoot(System.String,System.String,System.String@,System.String@,System.String@,System.String@)" /> method.</param>
      <param name="VirtualDirectory">The name of the virtual root, which corresponds to <paramref name="VirtualRoot" /> from <see cref="M:System.EnterpriseServices.Internal.Publish.CreateVirtualRoot(System.String,System.String,System.String@,System.String@,System.String@,System.String@)" />.</param>
      <param name="Error">A string to which an error message can be written.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
        <IPermission class="System.DirectoryServices.DirectoryServicesPermission, System.DirectoryServices, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IISVirtualRoot.Delete(System.String,System.String,System.String,System.String@)">
      <summary>Deletes an Internet Information Services (IIS) virtual root.</summary>
      <param name="RootWeb">The root Web server, as specified by <paramref name="RootWebServer" /> from the <see cref="M:System.EnterpriseServices.Internal.IComSoapPublisher.DeleteVirtualRoot(System.String,System.String,System.String@)" /> method.</param>
      <param name="PhysicalDirectory">The physical path of the virtual root.</param>
      <param name="VirtualDirectory">The name of the virtual root.</param>
      <param name="Error">A string to which an error message can be written.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
        <IPermission class="System.DirectoryServices.DirectoryServicesPermission, System.DirectoryServices, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.Internal.IServerWebConfig">
      <summary>Creates a Web.config file for a SOAP-enabled COM+ application and adds component entries to the file for COM interfaces being published in the application.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IServerWebConfig.AddElement(System.String,System.String,System.String,System.String,System.String,System.String@)">
      <summary>Adds XML elements to a Web.config file for a COM interface being published in a SOAP-enabled COM+ application.</summary>
      <param name="FilePath">The path for the existing Web.config file.</param>
      <param name="AssemblyName">The name of the assembly that contains the type being added.</param>
      <param name="TypeName">The name of the type being added.</param>
      <param name="ProgId">The programmatic identifier for the type being added.</param>
      <param name="Mode">A string constant that corresponds to the name of a member from the <see cref="T:System.Runtime.Remoting.WellKnownObjectMode" /> enumeration, which indicates how a well-known object is activated.</param>
      <param name="Error">A string to which an error message can be written.</param>
    </member>
    <member name="M:System.EnterpriseServices.Internal.IServerWebConfig.Create(System.String,System.String,System.String@)">
      <summary>Creates a Web.config file for a SOAP-enabled COM+ application so that the file is ready to have XML elements added for COM interfaces being published.</summary>
      <param name="FilePath">The folder in which to create the configuration file.</param>
      <param name="FileRootName">The string value to which a config extension can be added (for example, Web for Web.config).</param>
      <param name="Error">A string to which an error message can be written.</param>
    </member>
    <member name="T:System.EnterpriseServices.Internal.ISoapClientImport">
      <summary>Imports authenticated, encrypted SOAP client proxies.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ISoapClientImport.ProcessClientTlbEx(System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>Creates a .NET remoting client configuration file that includes security and authentication options.</summary>
      <param name="progId">The programmatic identifier of the class. If an empty string (""), this method returns without doing anything. </param>
      <param name="virtualRoot">The name of the virtual root. </param>
      <param name="baseUrl">The base URL that contains the virtual root. </param>
      <param name="authentication">The type of ASP.NET authentication to use. </param>
      <param name="assemblyName">The name of the assembly. </param>
      <param name="typeName">The name of the type. </param>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
    </member>
    <member name="T:System.EnterpriseServices.Internal.ISoapServerTlb">
      <summary>Processes authenticated, encrypted SOAP components on servers.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ISoapServerTlb.AddServerTlb(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String@,System.String@)">
      <summary>Adds the entries for a server type library to the Web.config and Default.disco files, depending on security options, and generates a proxy if necessary.</summary>
      <param name="progId">The programmatic identifier of the class. </param>
      <param name="classId">The class identifier (CLSID) for the type library. </param>
      <param name="interfaceId">The IID for the type library. </param>
      <param name="srcTlbPath">The path for the file containing the type library. </param>
      <param name="rootWebServer">The root Web server. </param>
      <param name="baseUrl">The base URL that contains the virtual root. </param>
      <param name="virtualRoot">The name of the virtual root. </param>
      <param name="clientActivated">true if client activated; otherwise, false. </param>
      <param name="wellKnown">true if well-known; otherwise, false. </param>
      <param name="discoFile">true if a discovery file; otherwise, false. </param>
      <param name="operation">The operation to perform. Specify either "delete" or an empty string. </param>
      <param name="assemblyName">When this method returns, contains the name of the assembly. </param>
      <param name="typeName">When this method returns, contains the type of the assembly. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The SOAP utility is not available. </exception>
      <exception cref="T:System.EnterpriseServices.ServicedComponentException">The call to get the system directory failed. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ISoapServerTlb.DeleteServerTlb(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>Removes entries for a server type library from the Web.config and Default.disco files, depending on security options.</summary>
      <param name="progId">The programmatic identifier of the class. </param>
      <param name="classId">The class identifier (CLSID) for the type library. </param>
      <param name="interfaceId">The IID for the type library. </param>
      <param name="srcTlbPath">The path for the file containing the type library. </param>
      <param name="rootWebServer">The root Web server. </param>
      <param name="baseUrl">The base URL that contains the virtual root. </param>
      <param name="virtualRoot">The name of the virtual root. </param>
      <param name="operation">Not used. Specify null for this parameter.</param>
      <param name="assemblyName">The name of the assembly. </param>
      <param name="typeName">The type of the assembly. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The SOAP utility is not available. </exception>
    </member>
    <member name="T:System.EnterpriseServices.Internal.ISoapServerVRoot">
      <summary>Publishes authenticated, encrypted SOAP virtual roots on servers.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ISoapServerVRoot.CreateVirtualRootEx(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String@,System.String@,System.String@)">
      <summary>Creates a SOAP virtual root with security options.</summary>
      <param name="rootWebServer">The root Web server. </param>
      <param name="inBaseUrl">The base URL that contains the virtual root. </param>
      <param name="inVirtualRoot">The name of the virtual root. </param>
      <param name="homePage">true if the EnableDefaultDoc property is to be set; otherwise, false.</param>
      <param name="discoFile">true if a default discovery file is to be created; false if there is to be no discovery file. If false and a Default.disco file exists, the file is deleted. </param>
      <param name="secureSockets">true if SSL encryption is required; otherwise, false.</param>
      <param name="authentication">Specify "anonymous" if no authentication is to be used (anonymous user). Otherwise, specify an empty string.</param>
      <param name="operation">Not used. Specify null for this parameter.</param>
      <param name="baseUrl">When this method returns, this parameter contains the base URL. </param>
      <param name="virtualRoot">When this method returns, this parameter contains the name of the virtual root. </param>
      <param name="physicalPath">When this method returns, this parameter contains the disk address of the Virtual Root directory. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The SOAP utility is not available. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ISoapServerVRoot.DeleteVirtualRootEx(System.String,System.String,System.String)">
      <summary>Deletes a virtual root. Not fully implemented.</summary>
      <param name="rootWebServer">The root Web server. </param>
      <param name="baseUrl">The base URL that contains the virtual root. </param>
      <param name="virtualRoot">The name of the virtual root. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The SOAP utility is not available. </exception>
      <exception cref="T:System.EnterpriseServices.ServicedComponentException">The call to identify the system directory failed. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ISoapServerVRoot.GetVirtualRootStatus(System.String,System.String,System.String,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@)">
      <summary>Returns the security status of an existing SOAP virtual root.</summary>
      <param name="rootWebServer">The root Web server. </param>
      <param name="inBaseUrl">The base URL that contains the virtual root. </param>
      <param name="inVirtualRoot">The name of the virtual root. </param>
      <param name="exists">When this method returns, this parameter contains a true if the virtual directory exists; otherwise, false.</param>
      <param name="secureSockets">When this method returns, this parameter contains a true if SSL encryption is required; otherwise, false.</param>
      <param name="windowsAuth">When this method returns, this parameter contains true if Windows authentication is set, otherwise, false.</param>
      <param name="anonymous">When this method returns, this parameter contains true if no authentication is set (anonymous user); otherwise, false.</param>
      <param name="homePage">When this method returns, this parameter contains a true if the Virtual Root directory's EnableDefaultDoc property is set; otherwise, false.</param>
      <param name="discoFile">When this method returns, this parameter contains a true if a Default.disco file exists; otherwise, false.</param>
      <param name="physicalPath">When this method returns, this parameter contains the disk address of the Virtual Root directory. </param>
      <param name="baseUrl">When this method returns, this parameter contains the base URL. </param>
      <param name="virtualRoot">When this method returns, this parameter contains the name of the virtual root. </param>
    </member>
    <member name="T:System.EnterpriseServices.Internal.ISoapUtility">
      <summary>Provides utilities to support the exporting of COM+ SOAP-enabled application proxies by the server and the importing of the proxies by the client.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ISoapUtility.GetServerBinPath(System.String,System.String,System.String,System.String@)">
      <summary>Returns the path for the SOAP virtual root bin directory.</summary>
      <param name="rootWebServer">The root Web server. </param>
      <param name="inBaseUrl">The base URL address. </param>
      <param name="inVirtualRoot">The name of the virtual root. </param>
      <param name="binPath">When this method returns, this parameter contains the file path for the SOAP virtual root bin directory. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The SOAP utility is not available. </exception>
      <exception cref="T:System.EnterpriseServices.ServicedComponentException">The call to get the system directory failed. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ISoapUtility.GetServerPhysicalPath(System.String,System.String,System.String,System.String@)">
      <summary>Returns the path for the SOAP virtual root.</summary>
      <param name="rootWebServer">The root Web server. </param>
      <param name="inBaseUrl">The base URL address. </param>
      <param name="inVirtualRoot">The name of the virtual root. </param>
      <param name="physicalPath">When this method returns, this parameter contains the file path for the SOAP virtual root. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The SOAP utility is not available. </exception>
      <exception cref="T:System.EnterpriseServices.ServicedComponentException">The call to get the system directory failed. </exception>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ISoapUtility.Present">
      <summary>Determines whether authenticated, encrypted SOAP interfaces are present.</summary>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The SOAP utility is not available. </exception>
    </member>
    <member name="T:System.EnterpriseServices.Internal.Publish">
      <summary>Publishes COM interfaces for SOAP-enabled COM+ applications.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Internal.Publish" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.CreateMailBox(System.String,System.String,System.String@,System.String@,System.String@,System.String@)">
      <summary>Creates a SOAP-enabled COM+ application mailbox at a specified URL. Not fully implemented.</summary>
      <param name="RootMailServer">The URL for the root mail server. </param>
      <param name="MailBox">The mailbox to create. </param>
      <param name="SmtpName">When this method returns, this parameter contains the name of the Simple Mail Transfer Protocol (SMTP) server containing the mailbox. </param>
      <param name="Domain">When this method returns, this parameter contains the domain of the SMTP server. </param>
      <param name="PhysicalPath">When this method returns, this parameter contains the file system path for the mailbox. </param>
      <param name="Error">When this method returns, this parameter contains an error message if a problem was encountered. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.CreateVirtualRoot(System.String,System.String,System.String@,System.String@,System.String@,System.String@)">
      <summary>Creates a SOAP-enabled COM+ application virtual root.</summary>
      <param name="Operation">The operation to perform. </param>
      <param name="FullUrl">The complete URL address for the virtual root. </param>
      <param name="BaseUrl">When this method returns, this parameter contains the base URL address. </param>
      <param name="VirtualRoot">When this method returns, this parameter contains the name of the virtual root. </param>
      <param name="PhysicalPath">When this method returns, this parameter contains the file path for the virtual root. </param>
      <param name="Error">When this method returns, this parameter contains an error message if a problem was encountered. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code.-or- The caller does not have permission to access DNS information. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="FullUrl" /> is null. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error is encountered when resolving the local host name. </exception>
      <exception cref="T:System.UriFormatException">
        <paramref name="FullUrl" /> is empty.-or- The scheme specified in <paramref name="FullUrl" /> is invalid.-or- <paramref name="FullUrl" /> contains more than two consecutive slashes.-or- The password specified in <paramref name="FullUrl" /> is invalid.-or- The host name specified in <paramref name="FullUrl" /> is invalid.-or- The file name specified in <paramref name="FullUrl" /> is invalid. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence, RemotingConfiguration" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.DirectoryServices.DirectoryServicesPermission, System.DirectoryServices, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.DeleteMailBox(System.String,System.String,System.String@)">
      <summary>Deletes a SOAP-enabled COM+ application mailbox at a specified URL. Not fully implemented.</summary>
      <param name="RootMailServer">The URL for the root mail server. </param>
      <param name="MailBox">The mailbox to delete. </param>
      <param name="Error">When this method returns, this parameter contains an error message if a problem was encountered. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.DeleteVirtualRoot(System.String,System.String,System.String@)">
      <summary>Deletes a SOAP-enabled COM+ application virtual root. Not fully implemented.</summary>
      <param name="RootWebServer">The root Web server. </param>
      <param name="FullUrl">The complete URL address for the virtual root. </param>
      <param name="Error">When this method returns, this parameter contains an error message if a problem was encountered. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.GacInstall(System.String)">
      <summary>Installs an assembly in the global assembly cache.</summary>
      <param name="AssemblyPath">The file system path for the assembly. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.GacRemove(System.String)">
      <summary>Removes an assembly from the global assembly cache.</summary>
      <param name="AssemblyPath">The file system path for the assembly. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code.-or- The caller does not have path discovery permission. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="AssemblyPath" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="AssemblyPath" /> is empty. </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="AssemblyPath" /> is not found. </exception>
      <exception cref="T:System.IO.FileLoadException">An assembly or module was loaded twice with two different evidences. </exception>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="AssemblyPath" /> is not a valid assembly. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence, ControlAppDomain" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.GetAssemblyNameForCache(System.String,System.String@)">
      <summary>Returns the full path for a strong-named signed generated assembly in the SoapCache directory.</summary>
      <param name="TypeLibPath">The path for the file that contains the typelib. </param>
      <param name="CachePath">When this method returns, this parameter contains the name of the SoapCache directory. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="TypeLibPath" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">The file name is empty, contains only white spaces, or contains invalid characters. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to <paramref name="TypeLibPath" /> is denied. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="TypeLibPath" /> contains a colon (:) in the middle of the string. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.GetClientPhysicalPath(System.Boolean)">
      <summary>Returns the path for the directory for storing client configuration files.</summary>
      <returns>The path for the directory to contain the configuration files.</returns>
      <param name="CreateDir">Set to true to create the directory, or false to return the path but not create the directory. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.GetTypeNameFromProgId(System.String,System.String)">
      <summary>Reflects over an assembly and returns the type name that matches the ProgID.</summary>
      <returns>The type name that matches the ProgID.</returns>
      <param name="AssemblyPath">The file system path for the assembly. </param>
      <param name="ProgId">The programmatic identifier of the class. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence, ControlAppDomain" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.ParseUrl(System.String,System.String@,System.String@)">
      <summary>Parses a URL and returns the base URL and virtual root portions.</summary>
      <param name="FullUrl">The complete URL address for the virtual root. </param>
      <param name="BaseUrl">When this method returns, this parameter contains the base URL address. </param>
      <param name="VirtualRoot">When this method returns, this parameter contains the name of the virtual root. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="FullUrl" /> is null. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error is encountered when resolving the local host name. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to access DNS information. </exception>
      <exception cref="T:System.UriFormatException">
        <paramref name="FullUrl" /> is empty.-or- The scheme specified in <paramref name="FullUrl" /> is invalid.-or- <paramref name="FullUrl" /> contains too many slashes.-or- The password specified in <paramref name="FullUrl" /> is invalid.-or- The host name specified in <paramref name="FullUrl" /> is invalid.-or- The file name specified in <paramref name="FullUrl" /> is invalid. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.ProcessClientTlb(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String@,System.String@,System.String@)">
      <summary>Processes a client type library, creating a configuration file on the client.</summary>
      <param name="ProgId">The programmatic identifier of the class. </param>
      <param name="SrcTlbPath">The path for the file that contains the typelib. </param>
      <param name="PhysicalPath">The Web application directory. </param>
      <param name="VRoot">The name of the virtual root. </param>
      <param name="BaseUrl">The base URL that contains the virtual root. </param>
      <param name="Mode">The activation mode. </param>
      <param name="Transport">Not used. Specify null for this parameter.</param>
      <param name="AssemblyName">When this method returns, this parameter contains the display name of the assembly. </param>
      <param name="TypeName">When this method returns, this parameter contains the fully-qualified type name of the assembly. </param>
      <param name="Error">When this method returns, this parameter contains an error message if a problem was encountered. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence, ControlAppDomain, RemotingConfiguration" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.ProcessServerTlb(System.String,System.String,System.String,System.String,System.String@,System.String@,System.String@)">
      <summary>Processes a server type library, either adding or deleting component entries to the Web.config and Default.disco files. Generates a proxy if necessary.</summary>
      <param name="ProgId">The programmatic identifier of the class. </param>
      <param name="SrcTlbPath">The path for the file that contains the type library. </param>
      <param name="PhysicalPath">The Web application directory. </param>
      <param name="Operation">The operation to perform. </param>
      <param name="strAssemblyName">When this method returns, this parameter contains the display name of the assembly. </param>
      <param name="TypeName">When this method returns, this parameter contains the fully-qualified type name of the assembly. </param>
      <param name="Error">When this method returns, this parameter contains an error message if a problem was encountered. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.EnterpriseServices.ServicedComponentException">The <paramref name="SrcTlbPath" /> parameter referenced scrobj.dll; therefore, SOAP publication of script components is not supported. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence, ControlAppDomain, RemotingConfiguration" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.RegisterAssembly(System.String)">
      <summary>Registers an assembly for COM interop.</summary>
      <param name="AssemblyPath">The file system path for the assembly. </param>
      <exception cref="T:System.EnterpriseServices.RegistrationException">The input assembly does not have a strong name. </exception>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code.-or- A codebase that does not start with "file://" was specified without the required <see cref="T:System.Net.WebPermission" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="AssemblyPath" /> is null. </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="AssemblyPath" /> is not found, or a filename extension is not specified. </exception>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="AssemblyPath" /> is not a valid assembly. </exception>
      <exception cref="T:System.IO.FileLoadException">An assembly or module was loaded twice with two different evidences, or the assembly name is longer than MAX_PATH characters. </exception>
      <exception cref="T:System.InvalidOperationException">A method marked with <see cref="T:System.Runtime.InteropServices.ComUnregisterFunctionAttribute" /> is not static.-or- There is more than one method marked with <see cref="T:System.Runtime.InteropServices.ComUnregisterFunctionAttribute" /> at a given level of the hierarchy.-or- The signature of the method marked with <see cref="T:System.Runtime.InteropServices.ComUnregisterFunctionAttribute" /> is not valid. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.Publish.UnRegisterAssembly(System.String)">
      <summary>Unregisters a COM interop assembly.</summary>
      <param name="AssemblyPath">The file system path for the assembly. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code.-or- A codebase that does not start with "file://" was specified without the required <see cref="T:System.Net.WebPermission" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="AssemblyPath" /> is null. </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="AssemblyPath" /> is not found, or a file name extension is not specified. </exception>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="AssemblyPath" /> is not a valid assembly. </exception>
      <exception cref="T:System.IO.FileLoadException">An assembly or module was loaded twice with two different evidences, or the assembly name is longer than MAX_PATH characters. </exception>
      <exception cref="T:System.InvalidOperationException">A method marked with <see cref="T:System.Runtime.InteropServices.ComUnregisterFunctionAttribute" /> is not static.-or- There is more than one method marked with <see cref="T:System.Runtime.InteropServices.ComUnregisterFunctionAttribute" /> at a given level of the hierarchy.-or- The signature of the method marked with <see cref="T:System.Runtime.InteropServices.ComUnregisterFunctionAttribute" /> is not valid. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.Internal.ServerWebConfig">
      <summary>Creates a Web.config file for a SOAP-enabled COM+ application. Can also add component entries to the file for COM interfaces being published in the application.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ServerWebConfig.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Internal.ServerWebConfig" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ServerWebConfig.AddElement(System.String,System.String,System.String,System.String,System.String,System.String@)">
      <summary>Adds XML elements to a Web.config file for a COM interface being published in a SOAP-enabled COM+ application.</summary>
      <param name="FilePath">The path of the existing Web.config file.</param>
      <param name="AssemblyName">The name of the assembly that contains the type being added.</param>
      <param name="TypeName">The name of the type being added.</param>
      <param name="ProgId">The programmatic identifier for the type being added.</param>
      <param name="WkoMode">A string constant that corresponds to the name of a member from the <see cref="T:System.Runtime.Remoting.WellKnownObjectMode" /> enumeration, which indicates how a well-known object is activated.</param>
      <param name="Error">A string to which an error message can be written.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence, RemotingConfiguration" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.ServerWebConfig.Create(System.String,System.String,System.String@)">
      <summary>Creates a Web.config file for a SOAP-enabled COM+ application so that the file is ready to have XML elements added for COM interfaces being published.</summary>
      <param name="FilePath">The folder in which the configuration file should be created.</param>
      <param name="FilePrefix">The string value "Web", to which a config extension is added.</param>
      <param name="Error">A string to which an error message can be written.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, RemotingConfiguration" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.Internal.SoapClientImport">
      <summary>Imports authenticated, encrypted SOAP client proxies. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.SoapClientImport.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Internal.SoapClientImport" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.SoapClientImport.ProcessClientTlbEx(System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>Creates a .NET remoting client configuration file that includes security and authentication options.</summary>
      <param name="progId">The programmatic identifier of the class. If an empty string (""), this method returns without doing anything. </param>
      <param name="virtualRoot">The name of the virtual root. </param>
      <param name="baseUrl">The base URL that contains the virtual root. </param>
      <param name="authentication">The type of ASP.NET authentication to use. </param>
      <param name="assemblyName">The name of the assembly. </param>
      <param name="typeName">The name of the type. </param>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.Internal.SoapServerTlb">
      <summary>Processes authenticated, encrypted SOAP components on servers. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.SoapServerTlb.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Internal.SoapServerTlb" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.SoapServerTlb.AddServerTlb(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String@,System.String@)">
      <summary>Adds the entries for a server type library to the Web.config and Default.disco files, depending on security options, and generates a proxy if necessary.</summary>
      <param name="progId">The programmatic identifier of the class. </param>
      <param name="classId">The class identifier (CLSID) for the type library. </param>
      <param name="interfaceId">The IID for the type library. </param>
      <param name="srcTlbPath">The path for the file containing the type library. </param>
      <param name="rootWebServer">The root Web server. </param>
      <param name="inBaseUrl">The base URL that contains the virtual root. </param>
      <param name="inVirtualRoot">The name of the virtual root. </param>
      <param name="clientActivated">true if client activated; otherwise, false. </param>
      <param name="wellKnown">true if well-known; otherwise, false. </param>
      <param name="discoFile">true if a discovery file; otherwise, false.</param>
      <param name="operation">The operation to perform. Specify either "delete" or an empty string. </param>
      <param name="strAssemblyName">When this method returns, contains the name of the assembly. </param>
      <param name="typeName">When this method returns, contains the type of the assembly. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The SOAP utility is not available. </exception>
      <exception cref="T:System.EnterpriseServices.ServicedComponentException">The call to get the system directory failed. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence, ControlAppDomain, RemotingConfiguration" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.SoapServerTlb.DeleteServerTlb(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>Removes entries for a server type library from the Web.config and Default.disco files, depending on security options.</summary>
      <param name="progId">The programmatic identifier of the class. </param>
      <param name="classId">The class identifier (CLSID) for the type library. </param>
      <param name="interfaceId">The IID for the type library. </param>
      <param name="srcTlbPath">The path for the file containing the type library. </param>
      <param name="rootWebServer">The root Web server. </param>
      <param name="baseUrl">The base URL that contains the virtual root. </param>
      <param name="virtualRoot">The name of the virtual root. </param>
      <param name="operation">Not used. Specify null for this parameter.</param>
      <param name="assemblyName">The name of the assembly. </param>
      <param name="typeName">The type of the assembly. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The SOAP utility is not available. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence, ControlAppDomain, RemotingConfiguration" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.Internal.SoapServerVRoot">
      <summary>Publishes authenticated, encrypted SOAP virtual roots on servers. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.SoapServerVRoot.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Internal.SoapServerVRoot" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.SoapServerVRoot.CreateVirtualRootEx(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String@,System.String@,System.String@)">
      <summary>Creates a SOAP virtual root with security options.</summary>
      <param name="rootWebServer">The root Web server. The default is "IIS://localhost/W3SVC/1/ROOT". </param>
      <param name="inBaseUrl">The base URL that contains the virtual root. </param>
      <param name="inVirtualRoot">The name of the virtual root. </param>
      <param name="homePage">The URL of the home page. </param>
      <param name="discoFile">true if a default discovery file is to be created; false if there is to be no discovery file. If false and a Default.disco file exists, the file is deleted. </param>
      <param name="secureSockets">true if SSL encryption is required; otherwise, false. </param>
      <param name="authentication">Specify "anonymous" if no authentication is to be used (anonymous user). Otherwise, specify an empty string.</param>
      <param name="operation">Not used. Specify null for this parameter.</param>
      <param name="baseUrl">When this method returns, this parameter contains the base URL. </param>
      <param name="virtualRoot">When this method returns, this parameter contains the name of the virtual root. </param>
      <param name="physicalPath">When this method returns, this parameter contains the disk address of the Virtual Root directory. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The SOAP utility is not available. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence, RemotingConfiguration" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.EventLogPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.DirectoryServices.DirectoryServicesPermission, System.DirectoryServices, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.SoapServerVRoot.DeleteVirtualRootEx(System.String,System.String,System.String)">
      <summary>Deletes a virtual root. Not fully implemented.</summary>
      <param name="rootWebServer">The root Web server. The default is "IIS://localhost/W3SVC/1/ROOT".</param>
      <param name="inBaseUrl">The base URL that contains the virtual root. </param>
      <param name="inVirtualRoot">The name of the virtual root. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The SOAP utility is not available. </exception>
      <exception cref="T:System.EnterpriseServices.ServicedComponentException">The call to get the system directory failed. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.SoapServerVRoot.GetVirtualRootStatus(System.String,System.String,System.String,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@)">
      <summary>Returns the security status of an existing SOAP virtual root.</summary>
      <param name="RootWebServer">The root Web server. The default is "IIS://localhost/W3SVC/1/ROOT". </param>
      <param name="inBaseUrl">The base URL that contains the virtual root. </param>
      <param name="inVirtualRoot">The name of the virtual root. </param>
      <param name="Exists">When this method returns, this parameter contains a true if the virtual directory exists; otherwise, false. </param>
      <param name="SSL">When this method returns, this parameter contains a true if SSL encryption is required; otherwise, false. </param>
      <param name="WindowsAuth">When this method returns, this parameter contains true if Windows authentication is set, otherwise, false. </param>
      <param name="Anonymous">When this method returns, this parameter contains true if no authentication is set (anonymous user); otherwise, false.</param>
      <param name="HomePage">When this method returns, this parameter contains a true if the Virtual Root's EnableDefaultDoc property is set; otherwise, false.</param>
      <param name="DiscoFile">When this method returns, this parameter contains a true if a Default.disco file exists; otherwise, false.</param>
      <param name="PhysicalPath">When this method returns, this parameter contains the disk address of the virtual root directory. </param>
      <param name="BaseUrl">When this method returns, this parameter contains the base URL. </param>
      <param name="VirtualRoot">When this method returns, this parameter contains the name of the virtual root. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.DirectoryServices.DirectoryServicesPermission, System.DirectoryServices, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.EnterpriseServices.Internal.SoapUtility">
      <summary>Provides utilities to support the exporting of COM+ SOAP-enabled application proxies by the server and the importing of the proxies by the client. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.SoapUtility.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.EnterpriseServices.Internal.SoapUtility" /> class.</summary>
    </member>
    <member name="M:System.EnterpriseServices.Internal.SoapUtility.GetServerBinPath(System.String,System.String,System.String,System.String@)">
      <summary>Returns the path for the SOAP bin directory.</summary>
      <param name="rootWebServer">The root Web server. </param>
      <param name="inBaseUrl">The base URL address. </param>
      <param name="inVirtualRoot">The name of the virtual root. </param>
      <param name="binPath">When this method returns, this parameter contains the file path for the SOAP virtual root bin directory. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The SOAP utility is not available. </exception>
      <exception cref="T:System.EnterpriseServices.ServicedComponentException">The call to get the system directory failed. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.SoapUtility.GetServerPhysicalPath(System.String,System.String,System.String,System.String@)">
      <summary>Returns the path for the SOAP virtual root.</summary>
      <param name="rootWebServer">The root Web server. </param>
      <param name="inBaseUrl">The base URL address. </param>
      <param name="inVirtualRoot">The name of the virtual root. </param>
      <param name="physicalPath">When this method returns, this parameter contains the file path for the SOAP virtual root. </param>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The SOAP utility is not available. </exception>
      <exception cref="T:System.EnterpriseServices.ServicedComponentException">The call to get the system directory failed. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.EnterpriseServices.Internal.SoapUtility.Present">
      <summary>Determines whether authenticated, encrypted SOAP interfaces are present.</summary>
      <exception cref="T:System.Security.SecurityException">A caller in the call chain does not have permission to access unmanaged code. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The SOAP utility is not available. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
  </members>
</doc>