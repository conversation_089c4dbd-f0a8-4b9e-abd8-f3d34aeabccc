// Copyright (C) Microsoft Corporation. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import "objidl.idl";
import "oaidl.idl";
import "EventToken.idl";

[uuid(26d34152-879f-4065-bea2-3daa2cfadfb8), version(1.0)]
library WebView2 {

// Interface forward declarations

interface ICoreWebView2AcceleratorKeyPressedEventArgs;
interface ICoreWebView2AcceleratorKeyPressedEventHandler;
interface ICoreWebView2AddScriptToExecuteOnDocumentCreatedCompletedHandler;
interface ICoreWebView2CallDevToolsProtocolMethodCompletedHandler;
interface ICoreWebView2CapturePreviewCompletedHandler;
interface ICoreWebView2;
interface ICoreWebView2_2;
interface ICoreWebView2_3;
interface ICoreWebView2_4;
interface ICoreWebView2_5;
interface ICoreWebView2_6;
interface ICoreWebView2_7;
interface ICoreWebView2_8;
interface ICoreWebView2_9;
interface ICoreWebView2_10;
interface ICoreWebView2_11;
interface ICoreWebView2_12;
interface ICoreWebView2_13;
interface ICoreWebView2BasicAuthenticationRequestedEventArgs;
interface ICoreWebView2BasicAuthenticationRequestedEventHandler;
interface ICoreWebView2BasicAuthenticationResponse;
interface ICoreWebView2BrowserProcessExitedEventArgs;
interface ICoreWebView2BrowserProcessExitedEventHandler;
interface ICoreWebView2BytesReceivedChangedEventHandler;
interface ICoreWebView2CompositionController;
interface ICoreWebView2CompositionController2;
interface ICoreWebView2Controller;
interface ICoreWebView2Controller2;
interface ICoreWebView2Controller3;
interface ICoreWebView2Controller4;
interface ICoreWebView2ControllerOptions;
interface ICoreWebView2ContentLoadingEventArgs;
interface ICoreWebView2ContentLoadingEventHandler;
interface ICoreWebView2ContextMenuRequestedEventArgs;
interface ICoreWebView2ContextMenuRequestedEventHandler;
interface ICoreWebView2Cookie;
interface ICoreWebView2CookieList;
interface ICoreWebView2CookieManager;
interface ICoreWebView2Certificate;
interface ICoreWebView2ClientCertificate;
interface ICoreWebView2StringCollection;
interface ICoreWebView2ClearBrowsingDataCompletedHandler;
interface ICoreWebView2ClientCertificateCollection;
interface ICoreWebView2ClientCertificateRequestedEventArgs;
interface ICoreWebView2ClientCertificateRequestedEventHandler;
interface ICoreWebView2ContextMenuItem;
interface ICoreWebView2ContextMenuItemCollection;
interface ICoreWebView2ContextMenuRequestedEventArgs;
interface ICoreWebView2ContextMenuRequestedEventHandler;
interface ICoreWebView2ContextMenuTarget;
interface ICoreWebView2ClearServerCertificateErrorActionsCompletedHandler;
interface ICoreWebView2CreateCoreWebView2CompositionControllerCompletedHandler;
interface ICoreWebView2CreateCoreWebView2ControllerCompletedHandler;
interface ICoreWebView2CreateCoreWebView2EnvironmentCompletedHandler;
interface ICoreWebView2ContainsFullScreenElementChangedEventHandler;
interface ICoreWebView2CursorChangedEventHandler;
interface ICoreWebView2CustomItemSelectedEventHandler;
interface ICoreWebView2DocumentTitleChangedEventHandler;
interface ICoreWebView2DOMContentLoadedEventArgs;
interface ICoreWebView2DOMContentLoadedEventHandler;
interface ICoreWebView2Deferral;
interface ICoreWebView2DevToolsProtocolEventReceivedEventArgs;
interface ICoreWebView2DevToolsProtocolEventReceivedEventArgs2;
interface ICoreWebView2DevToolsProtocolEventReceivedEventHandler;
interface ICoreWebView2DevToolsProtocolEventReceiver;
interface ICoreWebView2DownloadOperation;
interface ICoreWebView2DownloadStartingEventArgs;
interface ICoreWebView2DownloadStartingEventHandler;
interface ICoreWebView2Environment;
interface ICoreWebView2Environment2;
interface ICoreWebView2Environment3;
interface ICoreWebView2Environment4;
interface ICoreWebView2Environment5;
interface ICoreWebView2Environment6;
interface ICoreWebView2Environment7;
interface ICoreWebView2Environment8;
interface ICoreWebView2Environment9;
interface ICoreWebView2Environment10;
interface ICoreWebView2EnvironmentOptions;
interface ICoreWebView2EnvironmentOptions2;
interface ICoreWebView2EstimatedEndTimeChangedEventHandler;
interface ICoreWebView2ExecuteScriptCompletedHandler;
interface ICoreWebView2Frame;
interface ICoreWebView2Frame2;
interface ICoreWebView2Frame3;
interface ICoreWebView2FrameContentLoadingEventHandler;
interface ICoreWebView2FrameCreatedEventArgs;
interface ICoreWebView2FrameCreatedEventHandler;
interface ICoreWebView2FrameDestroyedEventHandler;
interface ICoreWebView2FrameDOMContentLoadedEventHandler;
interface ICoreWebView2FrameNameChangedEventHandler;
interface ICoreWebView2FrameNavigationCompletedEventHandler;
interface ICoreWebView2FrameNavigationStartingEventHandler;
interface ICoreWebView2FramePermissionRequestedEventHandler;
interface ICoreWebView2FrameWebMessageReceivedEventHandler;
interface ICoreWebView2FrameInfo;
interface ICoreWebView2FrameInfoCollection;
interface ICoreWebView2FrameInfoCollectionIterator;
interface ICoreWebView2FocusChangedEventHandler;
interface ICoreWebView2GetCookiesCompletedHandler;
interface ICoreWebView2HistoryChangedEventHandler;
interface ICoreWebView2HttpHeadersCollectionIterator;
interface ICoreWebView2HttpRequestHeaders;
interface ICoreWebView2HttpResponseHeaders;
interface ICoreWebView2IsDefaultDownloadDialogOpenChangedEventHandler;
interface ICoreWebView2MoveFocusRequestedEventArgs;
interface ICoreWebView2MoveFocusRequestedEventHandler;
interface ICoreWebView2NavigationCompletedEventArgs;
interface ICoreWebView2NavigationCompletedEventArgs2;
interface ICoreWebView2NavigationCompletedEventHandler;
interface ICoreWebView2NavigationStartingEventArgs;
interface ICoreWebView2NavigationStartingEventArgs2;
interface ICoreWebView2NavigationStartingEventHandler;
interface ICoreWebView2NewBrowserVersionAvailableEventHandler;
interface ICoreWebView2NewWindowRequestedEventArgs;
interface ICoreWebView2NewWindowRequestedEventArgs2;
interface ICoreWebView2NewWindowRequestedEventHandler;
interface ICoreWebView2PermissionRequestedEventArgs;
interface ICoreWebView2PermissionRequestedEventArgs2;
interface ICoreWebView2PermissionRequestedEventHandler;
interface ICoreWebView2PointerInfo;
interface ICoreWebView2PrintSettings;
interface ICoreWebView2PrintToPdfCompletedHandler;
interface ICoreWebView2ProcessFailedEventArgs;
interface ICoreWebView2ProcessFailedEventArgs2;
interface ICoreWebView2ProcessFailedEventHandler;
interface ICoreWebView2Profile;
interface ICoreWebView2Profile2;
interface ICoreWebView2RasterizationScaleChangedEventHandler;
interface ICoreWebView2ServerCertificateErrorDetectedEventArgs;
interface ICoreWebView2ServerCertificateErrorDetectedEventHandler;
interface ICoreWebView2ScriptDialogOpeningEventArgs;
interface ICoreWebView2ScriptDialogOpeningEventHandler;
interface ICoreWebView2Settings;
interface ICoreWebView2Settings2;
interface ICoreWebView2Settings3;
interface ICoreWebView2Settings4;
interface ICoreWebView2Settings5;
interface ICoreWebView2Settings6;
interface ICoreWebView2Settings7;
interface ICoreWebView2SourceChangedEventArgs;
interface ICoreWebView2SourceChangedEventHandler;
interface ICoreWebView2StateChangedEventHandler;
interface ICoreWebView2StatusBarTextChangedEventHandler;
interface ICoreWebView2TrySuspendCompletedHandler;
interface ICoreWebView2WebMessageReceivedEventArgs;
interface ICoreWebView2WebMessageReceivedEventHandler;
interface ICoreWebView2WebResourceRequest;
interface ICoreWebView2WebResourceRequestedEventArgs;
interface ICoreWebView2WebResourceRequestedEventHandler;
interface ICoreWebView2WebResourceResponse;
interface ICoreWebView2WebResourceResponseReceivedEventHandler;
interface ICoreWebView2WebResourceResponseReceivedEventArgs;
interface ICoreWebView2WebResourceResponseView;
interface ICoreWebView2WebResourceResponseViewGetContentCompletedHandler;
interface ICoreWebView2WindowCloseRequestedEventHandler;
interface ICoreWebView2WindowFeatures;
interface ICoreWebView2ZoomFactorChangedEventHandler;
interface ICoreWebView2IsMutedChangedEventHandler;
interface ICoreWebView2IsDocumentPlayingAudioChangedEventHandler;
interface ICoreWebView2ProcessInfo;
interface ICoreWebView2ProcessInfoCollection;
interface ICoreWebView2ProcessInfosChangedEventHandler;
interface ICoreWebView2FaviconChangedEventHandler;
interface ICoreWebView2GetFaviconCompletedHandler;

// Enums and structs

/// Specifies the image format for the `ICoreWebView2::CapturePreview` method.

[v1_enum]
typedef enum COREWEBVIEW2_CAPTURE_PREVIEW_IMAGE_FORMAT {

  /// Indicates that the PNG image format is used.

  COREWEBVIEW2_CAPTURE_PREVIEW_IMAGE_FORMAT_PNG,

  /// Indicates the JPEG image format is used.

  COREWEBVIEW2_CAPTURE_PREVIEW_IMAGE_FORMAT_JPEG,
} COREWEBVIEW2_CAPTURE_PREVIEW_IMAGE_FORMAT;

/// Kind of cookie SameSite status used in the ICoreWebView2Cookie interface.
/// These fields match those as specified in https://developer.mozilla.org/docs/Web/HTTP/Cookies#.
/// Learn more about SameSite cookies here: https://tools.ietf.org/html/draft-west-first-party-cookies-07
[v1_enum]
typedef enum COREWEBVIEW2_COOKIE_SAME_SITE_KIND {
  /// None SameSite type. No restrictions on cross-site requests.
  COREWEBVIEW2_COOKIE_SAME_SITE_KIND_NONE,
  /// Lax SameSite type. The cookie will be sent with "same-site" requests, and with "cross-site" top level navigation.
  COREWEBVIEW2_COOKIE_SAME_SITE_KIND_LAX,
  /// Strict SameSite type. The cookie will only be sent along with "same-site" requests.
  COREWEBVIEW2_COOKIE_SAME_SITE_KIND_STRICT,
} COREWEBVIEW2_COOKIE_SAME_SITE_KIND;

/// Kind of cross origin resource access allowed for host resources during download.
/// Note that other normal access checks like same origin DOM access check and [Content
/// Security Policy](https://developer.mozilla.org/docs/Web/HTTP/CSP) still apply.
/// The following table illustrates the host resource cross origin access according to
/// access context and `COREWEBVIEW2_HOST_RESOURCE_ACCESS_KIND`.
///
/// Cross Origin Access Context | DENY | ALLOW | DENY_CORS
/// --- | --- | --- | ---
/// From DOM like src of img, script or iframe element| Deny | Allow | Allow
/// From Script like Fetch or XMLHttpRequest| Deny | Allow | Deny
[v1_enum]
typedef enum COREWEBVIEW2_HOST_RESOURCE_ACCESS_KIND {
  /// All cross origin resource access is denied, including normal sub resource access
  /// as src of a script or image element.
  COREWEBVIEW2_HOST_RESOURCE_ACCESS_KIND_DENY,

  /// All cross origin resource access is allowed, including accesses that are
  /// subject to Cross-Origin Resource Sharing(CORS) check. The behavior is similar to
  /// a web site sends back http header Access-Control-Allow-Origin: *.
  COREWEBVIEW2_HOST_RESOURCE_ACCESS_KIND_ALLOW,

  /// Cross origin resource access is allowed for normal sub resource access like
  /// as src of a script or image element, while any access that subjects to CORS check
  /// will be denied.
  /// See [Cross-Origin Resource Sharing](https://developer.mozilla.org/docs/Web/HTTP/CORS)
  /// for more information.
  COREWEBVIEW2_HOST_RESOURCE_ACCESS_KIND_DENY_CORS,
} COREWEBVIEW2_HOST_RESOURCE_ACCESS_KIND;

/// Specifies the JavaScript dialog type used in the
/// `ICoreWebView2ScriptDialogOpeningEventHandler` interface.

[v1_enum]
typedef enum COREWEBVIEW2_SCRIPT_DIALOG_KIND {

  /// Indicates that the dialog uses the `window.alert` JavaScript function.

  COREWEBVIEW2_SCRIPT_DIALOG_KIND_ALERT,

  /// Indicates that the dialog uses the `window.confirm` JavaScript function.

  COREWEBVIEW2_SCRIPT_DIALOG_KIND_CONFIRM,

  /// Indicates that the dialog uses the `window.prompt` JavaScript function.

  COREWEBVIEW2_SCRIPT_DIALOG_KIND_PROMPT,

  /// Indicates that the dialog uses the `beforeunload` JavaScript event.

  COREWEBVIEW2_SCRIPT_DIALOG_KIND_BEFOREUNLOAD,
} COREWEBVIEW2_SCRIPT_DIALOG_KIND;

/// Specifies the process failure type used in the
/// `ICoreWebView2ProcessFailedEventHandler` interface. The values in this enum
/// make reference to the process kinds in the Chromium architecture. For more
/// information about what these processes are and what they do, see
/// [Browser Architecture - Inside look at modern web browser](https://developers.google.com/web/updates/2018/09/inside-browser-part1).

[v1_enum]
typedef enum COREWEBVIEW2_PROCESS_FAILED_KIND {

  /// Indicates that the browser process ended unexpectedly.  The WebView
  /// automatically moves to the Closed state.  The app has to recreate a new
  /// WebView to recover from this failure.

  COREWEBVIEW2_PROCESS_FAILED_KIND_BROWSER_PROCESS_EXITED,

  /// Indicates that the main frame's render process ended unexpectedly.  A new
  /// render process is created automatically and navigated to an error page.
  /// You can use the `Reload` method to try to reload the page that failed.

  COREWEBVIEW2_PROCESS_FAILED_KIND_RENDER_PROCESS_EXITED,

  /// Indicates that the main frame's render process is unresponsive.

  // Note that this does not seem to work right now.
  // Does not fire for simple long running script case, the only related test
  // SitePerProcessBrowserTest::NoCommitTimeoutForInvisibleWebContents is
  // disabled.

  COREWEBVIEW2_PROCESS_FAILED_KIND_RENDER_PROCESS_UNRESPONSIVE,

  /// Indicates that a frame-only render process ended unexpectedly. The process
  /// exit does not affect the top-level document, only a subset of the
  /// subframes within it. The content in these frames is replaced with an error
  /// page in the frame.

  COREWEBVIEW2_PROCESS_FAILED_KIND_FRAME_RENDER_PROCESS_EXITED,

  /// Indicates that a utility process ended unexpectedly.

  COREWEBVIEW2_PROCESS_FAILED_KIND_UTILITY_PROCESS_EXITED,

  /// Indicates that a sandbox helper process ended unexpectedly.

  COREWEBVIEW2_PROCESS_FAILED_KIND_SANDBOX_HELPER_PROCESS_EXITED,

  /// Indicates that the GPU process ended unexpectedly.

  COREWEBVIEW2_PROCESS_FAILED_KIND_GPU_PROCESS_EXITED,

  /// Indicates that a PPAPI plugin process ended unexpectedly.

  COREWEBVIEW2_PROCESS_FAILED_KIND_PPAPI_PLUGIN_PROCESS_EXITED,

  /// Indicates that a PPAPI plugin broker process ended unexpectedly.

  COREWEBVIEW2_PROCESS_FAILED_KIND_PPAPI_BROKER_PROCESS_EXITED,

  /// Indicates that a process of unspecified kind ended unexpectedly.

  COREWEBVIEW2_PROCESS_FAILED_KIND_UNKNOWN_PROCESS_EXITED,
} COREWEBVIEW2_PROCESS_FAILED_KIND;

/// Specifies the process failure reason used in the
/// `ICoreWebView2ProcessFailedEventHandler` interface.

[v1_enum]
typedef enum COREWEBVIEW2_PROCESS_FAILED_REASON {

  /// An unexpected process failure occurred.
  COREWEBVIEW2_PROCESS_FAILED_REASON_UNEXPECTED,

  /// The process became unresponsive.
  /// This only applies to the main frame's render process.

  COREWEBVIEW2_PROCESS_FAILED_REASON_UNRESPONSIVE,

  /// The process was terminated. For example, from Task Manager.

  COREWEBVIEW2_PROCESS_FAILED_REASON_TERMINATED,

  /// The process crashed.

  COREWEBVIEW2_PROCESS_FAILED_REASON_CRASHED,

  /// The process failed to launch.

  COREWEBVIEW2_PROCESS_FAILED_REASON_LAUNCH_FAILED,

  /// The process died due to running out of memory.

  COREWEBVIEW2_PROCESS_FAILED_REASON_OUT_OF_MEMORY,
} COREWEBVIEW2_PROCESS_FAILED_REASON;

/// Indicates the type of a permission request.

[v1_enum]
typedef enum COREWEBVIEW2_PERMISSION_KIND {

  /// Indicates an unknown permission.

  COREWEBVIEW2_PERMISSION_KIND_UNKNOWN_PERMISSION,

  /// Indicates permission to capture audio.

  COREWEBVIEW2_PERMISSION_KIND_MICROPHONE,

  /// Indicates permission to capture video.

  COREWEBVIEW2_PERMISSION_KIND_CAMERA,

  /// Indicates permission to access geolocation.

  COREWEBVIEW2_PERMISSION_KIND_GEOLOCATION,

  /// Indicates permission to send web notifications.  This permission request
  /// is currently auto-rejected and no event is run for it.

  COREWEBVIEW2_PERMISSION_KIND_NOTIFICATIONS,

  /// Indicates permission to access generic sensor.  Generic Sensor covering
  /// ambient-light-sensor, accelerometer, gyroscope, and magnetometer.

  COREWEBVIEW2_PERMISSION_KIND_OTHER_SENSORS,

  /// Indicates permission to read the system clipboard without a user gesture.

  COREWEBVIEW2_PERMISSION_KIND_CLIPBOARD_READ,
} COREWEBVIEW2_PERMISSION_KIND;

/// Specifies the response to a permission request.

[v1_enum]
typedef enum COREWEBVIEW2_PERMISSION_STATE {

  /// Specifies that the default browser behavior is used, which normally
  /// prompt users for decision.

  COREWEBVIEW2_PERMISSION_STATE_DEFAULT,

  /// Specifies that the permission request is granted.

  COREWEBVIEW2_PERMISSION_STATE_ALLOW,

  /// Specifies that the permission request is denied.

  COREWEBVIEW2_PERMISSION_STATE_DENY,
} COREWEBVIEW2_PERMISSION_STATE;

/// Indicates the error status values for web navigations.

[v1_enum]
typedef enum COREWEBVIEW2_WEB_ERROR_STATUS {

  /// Indicates that an unknown error occurred.

  COREWEBVIEW2_WEB_ERROR_STATUS_UNKNOWN,

  /// Indicates that the SSL certificate common name does not match the web
  /// address.

  COREWEBVIEW2_WEB_ERROR_STATUS_CERTIFICATE_COMMON_NAME_IS_INCORRECT,

  /// Indicates that the SSL certificate has expired.

  COREWEBVIEW2_WEB_ERROR_STATUS_CERTIFICATE_EXPIRED,

  /// Indicates that the SSL client certificate contains errors.

  COREWEBVIEW2_WEB_ERROR_STATUS_CLIENT_CERTIFICATE_CONTAINS_ERRORS,

  /// Indicates that the SSL certificate has been revoked.

  COREWEBVIEW2_WEB_ERROR_STATUS_CERTIFICATE_REVOKED,

  /// Indicates that the SSL certificate is not valid.  The certificate may not
  /// match the public key pins for the host name, the certificate is signed
  /// by an untrusted authority or using a weak sign algorithm, the certificate
  /// claimed DNS names violate name constraints, the certificate contains a
  /// weak key, the validity period of the certificate is too long, lack of
  /// revocation information or revocation mechanism, non-unique host name,
  /// lack of certificate transparency information, or the certificate is
  /// chained to a
  /// [legacy Symantec root](https://security.googleblog.com/2018/03/distrust-of-symantec-pki-immediate.html).

  COREWEBVIEW2_WEB_ERROR_STATUS_CERTIFICATE_IS_INVALID,

  /// Indicates that the host is unreachable.

  COREWEBVIEW2_WEB_ERROR_STATUS_SERVER_UNREACHABLE,

  /// Indicates that the connection has timed out.

  COREWEBVIEW2_WEB_ERROR_STATUS_TIMEOUT,

  /// Indicates that the server returned an invalid or unrecognized response.

  COREWEBVIEW2_WEB_ERROR_STATUS_ERROR_HTTP_INVALID_SERVER_RESPONSE,

  /// Indicates that the connection was stopped.

  COREWEBVIEW2_WEB_ERROR_STATUS_CONNECTION_ABORTED,

  /// Indicates that the connection was reset.

  COREWEBVIEW2_WEB_ERROR_STATUS_CONNECTION_RESET,

  /// Indicates that the Internet connection has been lost.

  COREWEBVIEW2_WEB_ERROR_STATUS_DISCONNECTED,

  /// Indicates that a connection to the destination was not established.

  COREWEBVIEW2_WEB_ERROR_STATUS_CANNOT_CONNECT,

  /// Indicates that the provided host name was not able to be resolved.

  COREWEBVIEW2_WEB_ERROR_STATUS_HOST_NAME_NOT_RESOLVED,

  /// Indicates that the operation was canceled.

  COREWEBVIEW2_WEB_ERROR_STATUS_OPERATION_CANCELED,

  /// Indicates that the request redirect failed.

  COREWEBVIEW2_WEB_ERROR_STATUS_REDIRECT_FAILED,

  /// Indicates that an unexpected error occurred.

  COREWEBVIEW2_WEB_ERROR_STATUS_UNEXPECTED_ERROR,

  /// Indicates that user is prompted with a login, waiting on user action.
  /// Initial navigation to a login site will always return this even if app provides
  /// credential using BasicAuthenticationRequested.
  /// HTTP response status code in this case is 401.
  /// See status code reference here: https://developer.mozilla.org/docs/Web/HTTP/Status.

  COREWEBVIEW2_WEB_ERROR_STATUS_VALID_AUTHENTICATION_CREDENTIALS_REQUIRED,

  /// Indicates that user lacks proper authentication credentials for a proxy server.
  /// HTTP response status code in this case is 407.
  /// See status code reference here: https://developer.mozilla.org/docs/Web/HTTP/Status.

  COREWEBVIEW2_WEB_ERROR_STATUS_VALID_PROXY_AUTHENTICATION_REQUIRED,
} COREWEBVIEW2_WEB_ERROR_STATUS;

/// Specifies the web resource request contexts.

[v1_enum]
typedef enum COREWEBVIEW2_WEB_RESOURCE_CONTEXT {

  /// Specifies all resources.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_ALL,

  /// Specifies a document resource.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_DOCUMENT,

  /// Specifies a CSS resource.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_STYLESHEET,

  /// Specifies an image resource.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_IMAGE,

  /// Specifies another media resource such as a video.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_MEDIA,

  /// Specifies a font resource.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_FONT,

  /// Specifies a script resource.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_SCRIPT,

  /// Specifies an XML HTTP request.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_XML_HTTP_REQUEST,

  /// Specifies a Fetch API communication.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_FETCH,

  /// Specifies a TextTrack resource.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_TEXT_TRACK,

  /// Specifies an EventSource API communication.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_EVENT_SOURCE,

  /// Specifies a WebSocket API communication.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_WEBSOCKET,

  /// Specifies a Web App Manifest.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_MANIFEST,

  /// Specifies a Signed HTTP Exchange.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_SIGNED_EXCHANGE,

  /// Specifies a Ping request.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_PING,

  /// Specifies a CSP Violation Report.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_CSP_VIOLATION_REPORT,

  /// Specifies an other resource.

  COREWEBVIEW2_WEB_RESOURCE_CONTEXT_OTHER
} COREWEBVIEW2_WEB_RESOURCE_CONTEXT;

/// Specifies the reason for moving focus.

[v1_enum]
typedef enum COREWEBVIEW2_MOVE_FOCUS_REASON {

  /// Specifies that the code is setting focus into WebView.

  COREWEBVIEW2_MOVE_FOCUS_REASON_PROGRAMMATIC,

  /// Specifies that the focus is moving due to Tab traversal forward.

  COREWEBVIEW2_MOVE_FOCUS_REASON_NEXT,

  /// Specifies that the focus is moving due to Tab traversal backward.

  COREWEBVIEW2_MOVE_FOCUS_REASON_PREVIOUS,
} COREWEBVIEW2_MOVE_FOCUS_REASON;

/// Specifies the key event type that triggered an `AcceleratorKeyPressed`
/// event.

[v1_enum]
typedef enum COREWEBVIEW2_KEY_EVENT_KIND {

  /// Specifies that the key event type corresponds to window message
  /// `WM_KEYDOWN`.

  COREWEBVIEW2_KEY_EVENT_KIND_KEY_DOWN,

  /// Specifies that the key event type corresponds to window message
  /// `WM_KEYUP`.

  COREWEBVIEW2_KEY_EVENT_KIND_KEY_UP,

  /// Specifies that the key event type corresponds to window message
  /// `WM_SYSKEYDOWN`.

  COREWEBVIEW2_KEY_EVENT_KIND_SYSTEM_KEY_DOWN,

  /// Specifies that the key event type corresponds to window message
  /// `WM_SYSKEYUP`.

  COREWEBVIEW2_KEY_EVENT_KIND_SYSTEM_KEY_UP,
} COREWEBVIEW2_KEY_EVENT_KIND;

/// Specifies the browser process exit type used in the
/// `ICoreWebView2BrowserProcessExitedEventArgs` interface.

[v1_enum]
typedef enum COREWEBVIEW2_BROWSER_PROCESS_EXIT_KIND {

  /// Indicates that the browser process ended normally.

  COREWEBVIEW2_BROWSER_PROCESS_EXIT_KIND_NORMAL,

  /// Indicates that the browser process ended unexpectedly.
  /// A `ProcessFailed` event will also be sent to listening WebViews from the
  /// `ICoreWebView2Environment` associated to the failed process.

  COREWEBVIEW2_BROWSER_PROCESS_EXIT_KIND_FAILED
} COREWEBVIEW2_BROWSER_PROCESS_EXIT_KIND;

/// Contains the information packed into the `LPARAM` sent to a Win32 key
/// event.  For more information about `WM_KEYDOWN`, navigate to
/// [WM_KEYDOWN message](/windows/win32/inputdev/wm-keydown).

typedef struct COREWEBVIEW2_PHYSICAL_KEY_STATUS {

  /// Specifies the repeat count for the current message.

  UINT32 RepeatCount;

  /// Specifies the scan code.

  UINT32 ScanCode;

  /// Indicates that the key is an extended key.

  BOOL IsExtendedKey;

  /// Indicates that a menu key is held down (context code).

  BOOL IsMenuKeyDown;

  /// Indicates that the key was held down.

  BOOL WasKeyDown;

  /// Indicates that the key was released.

  BOOL IsKeyReleased;
} COREWEBVIEW2_PHYSICAL_KEY_STATUS;

/// A value representing RGBA color (Red, Green, Blue, Alpha) for WebView2.
/// Each component takes a value from 0 to 255, with 0 being no intensity
/// and 255 being the highest intensity.

typedef struct COREWEBVIEW2_COLOR {

  /// Specifies the intensity of the Alpha ie. opacity value. 0 is transparent,
  /// 255 is opaque.

  BYTE A;

  /// Specifies the intensity of the Red color.

  BYTE R;

  /// Specifies the intensity of the Green color.

  BYTE G;

  /// Specifies the intensity of the Blue color.

  BYTE B;
} COREWEBVIEW2_COLOR;

/// Mouse event type used by SendMouseInput to convey the type of mouse event
/// being sent to WebView. The values of this enum align with the matching
/// WM_* window messages.

[v1_enum]
typedef enum COREWEBVIEW2_MOUSE_EVENT_KIND {

  /// Mouse horizontal wheel scroll event, WM_MOUSEHWHEEL.

  COREWEBVIEW2_MOUSE_EVENT_KIND_HORIZONTAL_WHEEL = 0x020E,

  /// Left button double click mouse event, WM_LBUTTONDBLCLK.

  COREWEBVIEW2_MOUSE_EVENT_KIND_LEFT_BUTTON_DOUBLE_CLICK = 0x0203,

  /// Left button down mouse event, WM_LBUTTONDOWN.

  COREWEBVIEW2_MOUSE_EVENT_KIND_LEFT_BUTTON_DOWN = 0x0201,

  /// Left button up mouse event, WM_LBUTTONUP.

  COREWEBVIEW2_MOUSE_EVENT_KIND_LEFT_BUTTON_UP = 0x0202,

  /// Mouse leave event, WM_MOUSELEAVE.

  COREWEBVIEW2_MOUSE_EVENT_KIND_LEAVE = 0x02A3,

  /// Middle button double click mouse event, WM_MBUTTONDBLCLK.

  COREWEBVIEW2_MOUSE_EVENT_KIND_MIDDLE_BUTTON_DOUBLE_CLICK = 0x0209,

  /// Middle button down mouse event, WM_MBUTTONDOWN.

  COREWEBVIEW2_MOUSE_EVENT_KIND_MIDDLE_BUTTON_DOWN = 0x0207,

  /// Middle button up mouse event, WM_MBUTTONUP.

  COREWEBVIEW2_MOUSE_EVENT_KIND_MIDDLE_BUTTON_UP = 0x0208,

  /// Mouse move event, WM_MOUSEMOVE.

  COREWEBVIEW2_MOUSE_EVENT_KIND_MOVE = 0x0200,

  /// Right button double click mouse event, WM_RBUTTONDBLCLK.

  COREWEBVIEW2_MOUSE_EVENT_KIND_RIGHT_BUTTON_DOUBLE_CLICK = 0x0206,

  /// Right button down mouse event, WM_RBUTTONDOWN.

  COREWEBVIEW2_MOUSE_EVENT_KIND_RIGHT_BUTTON_DOWN = 0x0204,

  /// Right button up mouse event, WM_RBUTTONUP.

  COREWEBVIEW2_MOUSE_EVENT_KIND_RIGHT_BUTTON_UP = 0x0205,

  /// Mouse wheel scroll event, WM_MOUSEWHEEL.

  COREWEBVIEW2_MOUSE_EVENT_KIND_WHEEL = 0x020A,

  /// First or second X button double click mouse event, WM_XBUTTONDBLCLK.

  COREWEBVIEW2_MOUSE_EVENT_KIND_X_BUTTON_DOUBLE_CLICK = 0x020D,

  /// First or second X button down mouse event, WM_XBUTTONDOWN.

  COREWEBVIEW2_MOUSE_EVENT_KIND_X_BUTTON_DOWN = 0x020B,

  /// First or second X button up mouse event, WM_XBUTTONUP.

  COREWEBVIEW2_MOUSE_EVENT_KIND_X_BUTTON_UP = 0x020C,
} COREWEBVIEW2_MOUSE_EVENT_KIND;

/// Mouse event virtual keys associated with a COREWEBVIEW2_MOUSE_EVENT_KIND for
/// SendMouseInput. These values can be combined into a bit flag if more than
/// one virtual key is pressed for the event. The values of this enum align
/// with the matching MK_* mouse keys.

[v1_enum]
typedef enum COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS {

  /// No additional keys pressed.

  COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_NONE     = 0x0,

  /// Left mouse button is down, MK_LBUTTON.

  COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_LEFT_BUTTON  = 0x0001,

  /// Right mouse button is down, MK_RBUTTON.

  COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_RIGHT_BUTTON  = 0x0002,

  /// SHIFT key is down, MK_SHIFT.

  COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_SHIFT    = 0x0004,

  /// CTRL key is down, MK_CONTROL.

  COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_CONTROL  = 0x0008,

  /// Middle mouse button is down, MK_MBUTTON.

  COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_MIDDLE_BUTTON  = 0x0010,

  /// First X button is down, MK_XBUTTON1

  COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_X_BUTTON1 = 0x0020,

  /// Second X button is down, MK_XBUTTON2

  COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_X_BUTTON2 = 0x0040,
} COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS);")

/// Pointer event type used by SendPointerInput to convey the type of pointer
/// event being sent to WebView. The values of this enum align with the
/// matching WM_POINTER* window messages.

[v1_enum]
typedef enum COREWEBVIEW2_POINTER_EVENT_KIND {

  /// Corresponds to WM_POINTERACTIVATE.

  COREWEBVIEW2_POINTER_EVENT_KIND_ACTIVATE = 0x024B,

  /// Corresponds to WM_POINTERDOWN.

  COREWEBVIEW2_POINTER_EVENT_KIND_DOWN = 0x0246,

  /// Corresponds to WM_POINTERENTER.

  COREWEBVIEW2_POINTER_EVENT_KIND_ENTER = 0x0249,

  /// Corresponds to WM_POINTERLEAVE.

  COREWEBVIEW2_POINTER_EVENT_KIND_LEAVE = 0x024A,

  /// Corresponds to WM_POINTERUP.

  COREWEBVIEW2_POINTER_EVENT_KIND_UP = 0x0247,

  /// Corresponds to WM_POINTERUPDATE.

  COREWEBVIEW2_POINTER_EVENT_KIND_UPDATE = 0x0245,
} COREWEBVIEW2_POINTER_EVENT_KIND;

/// Mode for how the Bounds property is interpreted in relation to the RasterizationScale property.
[v1_enum]
typedef enum COREWEBVIEW2_BOUNDS_MODE {

  /// Bounds property represents raw pixels. Physical size of Webview is not impacted by RasterizationScale.

  COREWEBVIEW2_BOUNDS_MODE_USE_RAW_PIXELS,

  /// Bounds property represents logicl pixels and the RasterizationScale property is used to get the physical size of the WebView.

  COREWEBVIEW2_BOUNDS_MODE_USE_RASTERIZATION_SCALE,
} COREWEBVIEW2_BOUNDS_MODE;

/// Specifies the client certificate kind.
[v1_enum] typedef enum COREWEBVIEW2_CLIENT_CERTIFICATE_KIND {
  /// Specifies smart card certificate.
  COREWEBVIEW2_CLIENT_CERTIFICATE_KIND_SMART_CARD,
  /// Specifies PIN certificate.
  COREWEBVIEW2_CLIENT_CERTIFICATE_KIND_PIN,
  /// Specifies other certificate.
  COREWEBVIEW2_CLIENT_CERTIFICATE_KIND_OTHER,
} COREWEBVIEW2_CLIENT_CERTIFICATE_KIND;

/// State of the download operation.
[v1_enum]
typedef enum COREWEBVIEW2_DOWNLOAD_STATE {
  /// The download is in progress.
  COREWEBVIEW2_DOWNLOAD_STATE_IN_PROGRESS,
  /// The connection with the file host was broken. The `InterruptReason` property
  /// can be accessed from `ICoreWebView2DownloadOperation`. See
  /// `COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON` for descriptions of kinds of
  /// interrupt reasons. Host can check whether an interrupted download can be
  /// resumed with the `CanResume` property on the `ICoreWebView2DownloadOperation`.
  /// Once resumed, a download is in the `COREWEBVIEW2_DOWNLOAD_STATE_IN_PROGRESS` state.
  COREWEBVIEW2_DOWNLOAD_STATE_INTERRUPTED,
  /// The download completed successfully.
  COREWEBVIEW2_DOWNLOAD_STATE_COMPLETED,
} COREWEBVIEW2_DOWNLOAD_STATE;

/// Reason why a download was interrupted.
[v1_enum]
typedef enum COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON {
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_NONE,

  /// Generic file error.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_FILE_FAILED,
  /// Access denied due to security restrictions.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_FILE_ACCESS_DENIED,
  /// Disk full. User should free some space or choose a different location to
  /// store the file.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_FILE_NO_SPACE,
  /// Result file path with file name is too long.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_FILE_NAME_TOO_LONG,
  /// File is too large for file system.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_FILE_TOO_LARGE,
  /// Microsoft Defender Smartscreen detected a virus in the file.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_FILE_MALICIOUS,
  /// File was in use, too many files opened, or out of memory.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_FILE_TRANSIENT_ERROR,
  /// File blocked by local policy.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_FILE_BLOCKED_BY_POLICY,
  /// Security check failed unexpectedly. Microsoft Defender SmartScreen could
  /// not scan this file.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_FILE_SECURITY_CHECK_FAILED,
  /// Seeking past the end of a file in opening a file, as part of resuming an
  /// interrupted download. The file did not exist or was not as large as
  /// expected. Partially downloaded file was truncated or deleted, and download
  /// will be restarted automatically.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_FILE_TOO_SHORT,
  /// Partial file did not match the expected hash and was deleted. Download
  /// will be restarted automatically.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_FILE_HASH_MISMATCH,

  /// Generic network error. User can retry the download manually.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_NETWORK_FAILED,
  /// Network operation timed out.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_NETWORK_TIMEOUT,
  /// Network connection lost. User can retry the download manually.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_NETWORK_DISCONNECTED,
  /// Server has gone down. User can retry the download manually.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_NETWORK_SERVER_DOWN,
  /// Network request invalid because original or redirected URI is invalid, has
  /// an unsupported scheme, or is disallowed by network policy.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_NETWORK_INVALID_REQUEST,

  /// Generic server error. User can retry the download manually.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_SERVER_FAILED,
  /// Server does not support range requests.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_SERVER_NO_RANGE,
  /// Server does not have the requested data.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_SERVER_BAD_CONTENT,
  /// Server did not authorize access to resource.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_SERVER_UNAUTHORIZED,
  /// Server certificate problem.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_SERVER_CERTIFICATE_PROBLEM,
  /// Server access forbidden.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_SERVER_FORBIDDEN,
  /// Unexpected server response. Responding server may not be intended server.
  /// User can retry the download manually.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_SERVER_UNEXPECTED_RESPONSE,
  /// Server sent fewer bytes than the Content-Length header. Content-length
  /// header may be invalid or connection may have closed. Download is treated
  /// as complete unless there are
  /// [strong validators](https://tools.ietf.org/html/rfc7232#section-2) present
  /// to interrupt the download.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_SERVER_CONTENT_LENGTH_MISMATCH,
  /// Unexpected cross-origin redirect.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_SERVER_CROSS_ORIGIN_REDIRECT,

  /// User canceled the download.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_USER_CANCELED,
  /// User shut down the WebView. Resuming downloads that were interrupted
  /// during shutdown is not yet supported.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_USER_SHUTDOWN,
  /// User paused the download.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_USER_PAUSED,

  /// WebView crashed.
  COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_DOWNLOAD_PROCESS_CRASHED,
} COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON;

/// The orientation for printing, used by the `Orientation` property on
/// `ICoreWebView2PrintSettings`. Currently only printing to PDF is supported.
[v1_enum]
typedef enum COREWEBVIEW2_PRINT_ORIENTATION {
  /// Print the page(s) in portrait orientation.
  COREWEBVIEW2_PRINT_ORIENTATION_PORTRAIT,

  /// Print the page(s) in landscape orientation.
  COREWEBVIEW2_PRINT_ORIENTATION_LANDSCAPE,
} COREWEBVIEW2_PRINT_ORIENTATION;

/// The default download dialog can be aligned to any of the WebView corners
/// by setting the `DefaultDownloadDialogCornerAlignment` property. The default
/// position is top-right corner.
[v1_enum]
typedef enum COREWEBVIEW2_DEFAULT_DOWNLOAD_DIALOG_CORNER_ALIGNMENT {

  /// Top-left corner of the WebView.
  COREWEBVIEW2_DEFAULT_DOWNLOAD_DIALOG_CORNER_ALIGNMENT_TOP_LEFT,

  /// Top-right corner of the WebView.
  COREWEBVIEW2_DEFAULT_DOWNLOAD_DIALOG_CORNER_ALIGNMENT_TOP_RIGHT,

  /// Bottom-left corner of the WebView.
  COREWEBVIEW2_DEFAULT_DOWNLOAD_DIALOG_CORNER_ALIGNMENT_BOTTOM_LEFT,

  /// Bottom-right corner of the WebView.
  COREWEBVIEW2_DEFAULT_DOWNLOAD_DIALOG_CORNER_ALIGNMENT_BOTTOM_RIGHT,

} COREWEBVIEW2_DEFAULT_DOWNLOAD_DIALOG_CORNER_ALIGNMENT;

/// Indicates the process type used in the ICoreWebView2ProcessInfo interface.
[v1_enum]
typedef enum COREWEBVIEW2_PROCESS_KIND {
  /// Indicates the browser process kind.
  COREWEBVIEW2_PROCESS_KIND_BROWSER,

  /// Indicates the render process kind.
  COREWEBVIEW2_PROCESS_KIND_RENDERER,

  /// Indicates the utility process kind.
  COREWEBVIEW2_PROCESS_KIND_UTILITY,

  /// Indicates the sandbox helper process kind.
  COREWEBVIEW2_PROCESS_KIND_SANDBOX_HELPER,

  /// Indicates the GPU process kind.
  COREWEBVIEW2_PROCESS_KIND_GPU,

  /// Indicates the PPAPI plugin process kind.
  COREWEBVIEW2_PROCESS_KIND_PPAPI_PLUGIN,

  /// Indicates the PPAPI plugin broker process kind.
  COREWEBVIEW2_PROCESS_KIND_PPAPI_BROKER,
} COREWEBVIEW2_PROCESS_KIND;

// PDF toolbar item. This enum must be in sync with ToolBarItem in pdf-store-data-types.ts
/// Specifies the PDF toolbar item types used for the `ICoreWebView2Settings::put_HiddenPdfToolbarItems` method.
[v1_enum]
typedef enum COREWEBVIEW2_PDF_TOOLBAR_ITEMS {

  /// No item
  COREWEBVIEW2_PDF_TOOLBAR_ITEMS_NONE  = 0x0,

  /// The save button
  COREWEBVIEW2_PDF_TOOLBAR_ITEMS_SAVE  = 0x0001,

  /// The print button
  COREWEBVIEW2_PDF_TOOLBAR_ITEMS_PRINT  = 0x0002,

  /// The save as button
  COREWEBVIEW2_PDF_TOOLBAR_ITEMS_SAVE_AS  = 0x0004,

    /// The zoom in button
  COREWEBVIEW2_PDF_TOOLBAR_ITEMS_ZOOM_IN  = 0x0008,

  /// The zoom out button
  COREWEBVIEW2_PDF_TOOLBAR_ITEMS_ZOOM_OUT  = 0x0010,

  /// The rotate button
  COREWEBVIEW2_PDF_TOOLBAR_ITEMS_ROTATE  = 0x0020,

  /// The fit page button
  COREWEBVIEW2_PDF_TOOLBAR_ITEMS_FIT_PAGE  = 0x0040,

  /// The page layout button
  COREWEBVIEW2_PDF_TOOLBAR_ITEMS_PAGE_LAYOUT  = 0x0080,

  /// The bookmarks button
  COREWEBVIEW2_PDF_TOOLBAR_ITEMS_BOOKMARKS  = 0x0100,

  /// The page select button
  COREWEBVIEW2_PDF_TOOLBAR_ITEMS_PAGE_SELECTOR  = 0x0200,

  /// The search button
  COREWEBVIEW2_PDF_TOOLBAR_ITEMS_SEARCH  = 0x0400,

} COREWEBVIEW2_PDF_TOOLBAR_ITEMS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(COREWEBVIEW2_PDF_TOOLBAR_ITEMS);")

/// Indicates the kind of context for which the context menu was created
/// for the `ICoreWebView2ContextMenuTarget::get_Kind` method.
/// This enum will always represent the active element that caused the context menu request.
/// If there is a selection with multiple images, audio and text, for example, the element that
/// the end user right clicks on within this selection will be the option represented by this enum.
[v1_enum]
typedef enum COREWEBVIEW2_CONTEXT_MENU_TARGET_KIND {
    /// Indicates that the context menu was created for the page without any additional content.
    COREWEBVIEW2_CONTEXT_MENU_TARGET_KIND_PAGE,

    /// Indicates that the context menu was created for an image element.
    COREWEBVIEW2_CONTEXT_MENU_TARGET_KIND_IMAGE,

    /// Indicates that the context menu was created for selected text.
    COREWEBVIEW2_CONTEXT_MENU_TARGET_KIND_SELECTED_TEXT,

    /// Indicates that the context menu was created for an audio element.
    COREWEBVIEW2_CONTEXT_MENU_TARGET_KIND_AUDIO,

    /// Indicates that the context menu was created for a video element.
    COREWEBVIEW2_CONTEXT_MENU_TARGET_KIND_VIDEO,
} COREWEBVIEW2_CONTEXT_MENU_TARGET_KIND;

/// Specifies the menu item kind
/// for the `ICoreWebView2ContextMenuItem::get_Kind` method
[v1_enum]
typedef enum COREWEBVIEW2_CONTEXT_MENU_ITEM_KIND {
    /// Specifies a command menu item kind.
    COREWEBVIEW2_CONTEXT_MENU_ITEM_KIND_COMMAND,

    /// Specifies a check box menu item kind. `ContextMenuItem` objects of this kind
    /// will need the `IsChecked` property to determine current state of the check box.
    COREWEBVIEW2_CONTEXT_MENU_ITEM_KIND_CHECK_BOX,

    /// Specifies a radio button menu item kind. `ContextMenuItem` objects of this kind
    /// will need the `IsChecked` property to determine current state of the radio button.
    COREWEBVIEW2_CONTEXT_MENU_ITEM_KIND_RADIO,

    /// Specifies a separator menu item kind. `ContextMenuItem` objects of this kind
    /// are used to signal a visual separator with no functionality.
    COREWEBVIEW2_CONTEXT_MENU_ITEM_KIND_SEPARATOR,

    /// Specifies a submenu menu item kind. `ContextMenuItem` objects of this kind will contain
    /// a `ContextMenuItemCollection` of its children `ContextMenuItem` objects.
    COREWEBVIEW2_CONTEXT_MENU_ITEM_KIND_SUBMENU,
} COREWEBVIEW2_CONTEXT_MENU_ITEM_KIND;

/// An enum to represent the options for WebView2 color scheme: auto, light, or dark.
[v1_enum]
typedef enum COREWEBVIEW2_PREFERRED_COLOR_SCHEME {
    /// Auto color scheme.
    COREWEBVIEW2_PREFERRED_COLOR_SCHEME_AUTO,

    /// Light color scheme.
    COREWEBVIEW2_PREFERRED_COLOR_SCHEME_LIGHT,

    /// Dark color scheme.
    COREWEBVIEW2_PREFERRED_COLOR_SCHEME_DARK
} COREWEBVIEW2_PREFERRED_COLOR_SCHEME;

/// Specifies the datatype for the
/// `ICoreWebView2Profile2::ClearBrowsingData` method.
[v1_enum]
typedef enum COREWEBVIEW2_BROWSING_DATA_KINDS {

    /// Specifies file systems data.
    COREWEBVIEW2_BROWSING_DATA_KINDS_FILE_SYSTEMS = 1 << 0,

    /// Specifies data stored by the IndexedDB DOM feature.
    COREWEBVIEW2_BROWSING_DATA_KINDS_INDEXED_DB = 1 << 1,

    /// Specifies data stored by the localStorage DOM API.
    COREWEBVIEW2_BROWSING_DATA_KINDS_LOCAL_STORAGE = 1 << 2,

    /// Specifies data stored by the Web SQL database DOM API.
    COREWEBVIEW2_BROWSING_DATA_KINDS_WEB_SQL = 1 << 3,

    /// Specifies data stored by the CacheStorage DOM API.
    COREWEBVIEW2_BROWSING_DATA_KINDS_CACHE_STORAGE = 1 << 4,

    /// Specifies DOM storage data, now and future. This browsing data kind is
    /// inclusive of COREWEBVIEW2_BROWSING_DATA_KINDS_FILE_SYSTEMS,
    /// COREWEBVIEW2_BROWSING_DATA_KINDS_INDEXED_DB,
    /// COREWEBVIEW2_BROWSING_DATA_KINDS_LOCAL_STORAGE,
    /// COREWEBVIEW2_BROWSING_DATA_KINDS_WEB_SQL,
    /// COREWEBVIEW2_BROWSING_DATA_KINDS_CACHE_STORAGE.
    /// New DOM storage data types may be added to this data kind in the future.
    COREWEBVIEW2_BROWSING_DATA_KINDS_ALL_DOM_STORAGE = 1 << 5,

    /// Specifies HTTP cookies data.
    COREWEBVIEW2_BROWSING_DATA_KINDS_COOKIES = 1 << 6,

    /// Specifies all site data, now and future. This browsing data kind
    /// is inclusive of COREWEBVIEW2_BROWSING_DATA_KINDS_ALL_DOM_STORAGE and
    /// COREWEBVIEW2_BROWSING_DATA_KINDS_COOKIES. New site data types
    /// may be added to this data kind in the future.
    COREWEBVIEW2_BROWSING_DATA_KINDS_ALL_SITE = 1 << 7,

    /// Specifies disk cache.
    COREWEBVIEW2_BROWSING_DATA_KINDS_DISK_CACHE = 1 << 8,

    /// Specifies download history data.
    COREWEBVIEW2_BROWSING_DATA_KINDS_DOWNLOAD_HISTORY = 1 << 9,

    /// Specifies general autofill form data.
    /// This excludes password information and includes information like:
    /// names, street and email addresses, phone numbers, and arbitrary input.
    /// This also includes payment data.
    COREWEBVIEW2_BROWSING_DATA_KINDS_GENERAL_AUTOFILL = 1 << 10,

    /// Specifies password autosave data.
    COREWEBVIEW2_BROWSING_DATA_KINDS_PASSWORD_AUTOSAVE = 1 << 11,

    /// Specifies browsing history data.
    COREWEBVIEW2_BROWSING_DATA_KINDS_BROWSING_HISTORY = 1 << 12,

    /// Specifies settings data.
    COREWEBVIEW2_BROWSING_DATA_KINDS_SETTINGS = 1 << 13,

    /// Specifies profile data that should be wiped to make it look like a new profile.
    /// This does not delete account-scoped data like passwords but will remove access
    /// to account-scoped data by signing the user out.
    /// Specifies all profile data, now and future. New profile data types may be added
    /// to this data kind in the future.
    /// This browsing data kind is inclusive of COREWEBVIEW2_BROWSING_DATA_KINDS_ALL_SITE,
    /// COREWEBVIEW2_BROWSING_DATA_KINDS_DISK_CACHE,
    /// COREWEBVIEW2_BROWSING_DATA_KINDS_DOWNLOAD_HISTORY,
    /// COREWEBVIEW2_BROWSING_DATA_KINDS_GENERAL_AUTOFILL,
    /// COREWEBVIEW2_BROWSING_DATA_KINDS_PASSWORD_AUTOSAVE,
    /// COREWEBVIEW2_BROWSING_DATA_KINDS_BROWSING_HISTORY, and
    /// COREWEBVIEW2_BROWSING_DATA_KINDS_SETTINGS.
    COREWEBVIEW2_BROWSING_DATA_KINDS_ALL_PROFILE =  1 << 14,
} COREWEBVIEW2_BROWSING_DATA_KINDS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(COREWEBVIEW2_BROWSING_DATA_KINDS);")

/// Specifies the action type when server certificate error is detected to be
/// used in the `ICoreWebView2ServerCertificateErrorDetectedEventArgs`
/// interface.
[v1_enum] typedef enum COREWEBVIEW2_SERVER_CERTIFICATE_ERROR_ACTION {
  /// Indicates to ignore the warning and continue the request with the TLS
  /// certificate. This decision is cached for the RequestUri's host and the
  /// server certificate in the session.
  COREWEBVIEW2_SERVER_CERTIFICATE_ERROR_ACTION_ALWAYS_ALLOW,

  /// Indicates to reject the certificate and cancel the request.
  COREWEBVIEW2_SERVER_CERTIFICATE_ERROR_ACTION_CANCEL,

  /// Indicates to display the default TLS interstitial error page to user for
  /// page navigations.
  /// For others TLS certificate is rejected and the request is cancelled.
  COREWEBVIEW2_SERVER_CERTIFICATE_ERROR_ACTION_DEFAULT
} COREWEBVIEW2_SERVER_CERTIFICATE_ERROR_ACTION;

/// Specifies the image format to use for favicon.
[v1_enum]
typedef enum COREWEBVIEW2_FAVICON_IMAGE_FORMAT {
    /// Indicates that the PNG image format is used.
    COREWEBVIEW2_FAVICON_IMAGE_FORMAT_PNG,

    /// Indicates the JPEG image format is used.
    COREWEBVIEW2_FAVICON_IMAGE_FORMAT_JPEG,
} COREWEBVIEW2_FAVICON_IMAGE_FORMAT;

// End of enums and structs

/// WebView2 enables you to host web content using the latest Microsoft Edge
/// browser and web technology.

[uuid(76eceacb-0462-4d94-ac83-423a6793775e), object, pointer_default(unique)]
interface ICoreWebView2 : IUnknown {

  /// The `ICoreWebView2Settings` object contains various modifiable settings
  /// for the running WebView.

  [propget] HRESULT Settings([out, retval] ICoreWebView2Settings** settings);

  /// The URI of the current top level document.  This value potentially
  /// changes as a part of the `SourceChanged` event that runs for some cases
  /// such as navigating to a different site or fragment navigations.  It
  /// remains the same for other types of navigations such as page refreshes
  /// or `history.pushState` with the same URL as the current page.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  ///
  /// \snippet ControlComponent.cpp SourceChanged
  [propget] HRESULT Source([out, retval] LPWSTR* uri);

  /// Cause a navigation of the top-level document to run to the specified URI.
  /// For more information, navigate to [Navigation
  /// events](/microsoft-edge/webview2/concepts/navigation-events).
  ///
  /// \> [!NOTE]\n\> This operation starts a navigation and the corresponding
  /// `NavigationStarting` event triggers sometime after `Navigate` runs.
  ///
  /// \snippet ControlComponent.cpp Navigate
  HRESULT Navigate([in] LPCWSTR uri);

  /// Initiates a navigation to htmlContent as source HTML of a new document.
  /// The `htmlContent` parameter may not be larger than 2 MB (2 * 1024 * 1024 bytes) in total size.
  /// The origin of the new page is `about:blank`.
  ///
  /// ```cpp
  ///    SetVirtualHostNameToFolderMapping(
  ///        L"appassets.example", L"assets",
  ///        COREWEBVIEW2_HOST_RESOURCE_ACCESS_KIND_DENY);
  ///
  ///    WCHAR c_navString[] = LR"
  ///    <head><link rel='stylesheet' href ='http://appassets.example/wv2.css'/></head>
  ///    <body>
  ///      <img src='http://appassets.example/wv2.png' />
  ///      <p><a href='http://appassets.example/winrt_test.txt'>Click me</a></p>
  ///    </body>";
  ///    m_webView->NavigateToString(c_navString);
  /// ```
  /// \snippet SettingsComponent.cpp NavigateToString
  HRESULT NavigateToString([in] LPCWSTR htmlContent);

  /// Add an event handler for the `NavigationStarting` event.
  /// `NavigationStarting` runs when the WebView main frame is requesting
  /// permission to navigate to a different URI.  Redirects trigger this
  /// operation as well, and the navigation id is the same as the original
  /// one.
  ///
  /// Navigations will be blocked until all `NavigationStarting` event handlers
  /// return.
  ///
  /// \snippet SettingsComponent.cpp NavigationStarting
  HRESULT add_NavigationStarting(
      [in] ICoreWebView2NavigationStartingEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_NavigationStarting`.
  HRESULT remove_NavigationStarting(
      [in] EventRegistrationToken token);

  /// Add an event handler for the `ContentLoading` event.  `ContentLoading`
  /// triggers before any content is loaded, including scripts added with
  /// `AddScriptToExecuteOnDocumentCreated`.  `ContentLoading` does not trigger
  /// if a same page navigation occurs (such as through `fragment`
  /// navigations or `history.pushState` navigations).  This operation
  /// follows the `NavigationStarting` and `SourceChanged` events and precedes
  /// the `HistoryChanged` and `NavigationCompleted` events.
  HRESULT add_ContentLoading(
      [in] ICoreWebView2ContentLoadingEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_ContentLoading`.
  HRESULT remove_ContentLoading(
      [in] EventRegistrationToken token);

  /// Add an event handler for the `SourceChanged` event.  `SourceChanged`
  /// triggers when the `Source` property changes.  `SourceChanged` runs when
  /// navigating to a different site or fragment navigations.  It does not
  /// trigger for other types of navigations such as page refreshes or
  /// `history.pushState` with the same URL as the current page.
  /// `SourceChanged` runs before `ContentLoading` for navigation to a new
  /// document.
  ///
  /// \snippet ControlComponent.cpp SourceChanged
  HRESULT add_SourceChanged(
      [in] ICoreWebView2SourceChangedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_SourceChanged`.
  HRESULT remove_SourceChanged(
      [in] EventRegistrationToken token);

  /// Add an event handler for the `HistoryChanged` event.  `HistoryChanged` is
  /// raised for changes to joint session history, which consists of top-level
  /// and manual frame navigations.  Use `HistoryChanged` to verify that the
  /// `CanGoBack` or `CanGoForward` value has changed.  `HistoryChanged` also
  /// runs for using `GoBack` or `GoForward`.  `HistoryChanged` runs after
  /// `SourceChanged` and `ContentLoading`.  `CanGoBack` is false for
  /// navigations initiated through ICoreWebView2Frame APIs if there has not yet
  /// been a user gesture.
  ///
  /// \snippet ControlComponent.cpp HistoryChanged
  HRESULT add_HistoryChanged(
      [in] ICoreWebView2HistoryChangedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_HistoryChanged`.
  HRESULT remove_HistoryChanged(
      [in] EventRegistrationToken token);

  /// Add an event handler for the `NavigationCompleted` event.
  /// `NavigationCompleted` runs when the WebView has completely loaded
  /// (concurrently when `body.onload` runs) or loading stopped with error.
  ///
  /// \snippet ControlComponent.cpp NavigationCompleted
  HRESULT add_NavigationCompleted(
      [in] ICoreWebView2NavigationCompletedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_NavigationCompleted`.
  HRESULT remove_NavigationCompleted(
      [in] EventRegistrationToken token);

  /// Add an event handler for the `FrameNavigationStarting` event.
  /// `FrameNavigationStarting` triggers when a child frame in the WebView
  /// requests permission to navigate to a different URI.  Redirects trigger
  /// this operation as well, and the navigation id is the same as the original
  /// one.
  ///
  /// Navigations will be blocked until all `FrameNavigationStarting` event
  /// handlers return.
  ///
  /// \snippet SettingsComponent.cpp FrameNavigationStarting
  HRESULT add_FrameNavigationStarting(
      [in] ICoreWebView2NavigationStartingEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with
  /// `add_FrameNavigationStarting`.
  HRESULT remove_FrameNavigationStarting(
      [in] EventRegistrationToken token);

  /// Add an event handler for the `FrameNavigationCompleted` event.
  /// `FrameNavigationCompleted` triggers when a child frame has completely
  /// loaded (concurrently when `body.onload` has triggered) or loading stopped with error.
  ///
  /// \snippet ControlComponent.cpp FrameNavigationCompleted
  HRESULT add_FrameNavigationCompleted(
      [in] ICoreWebView2NavigationCompletedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with
  /// `add_FrameNavigationCompleted`.
  HRESULT remove_FrameNavigationCompleted(
      [in] EventRegistrationToken token);

  /// Add an event handler for the `ScriptDialogOpening` event.
  /// `ScriptDialogOpening` runs when a JavaScript dialog (`alert`, `confirm`,
  /// `prompt`, or `beforeunload`) displays for the webview.  This event only
  /// triggers if the `ICoreWebView2Settings::AreDefaultScriptDialogsEnabled`
  /// property is set to `FALSE`.  The `ScriptDialogOpening` event suppresses
  /// dialogs or replaces default dialogs with custom dialogs.
  ///
  /// If a deferral is not taken on the event args, the subsequent scripts are
  /// blocked until the event handler returns.  If a deferral is taken, the
  /// scripts are blocked until the deferral is completed.
  ///
  /// \snippet SettingsComponent.cpp ScriptDialogOpening
  HRESULT add_ScriptDialogOpening(
      [in] ICoreWebView2ScriptDialogOpeningEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_ScriptDialogOpening`.
  HRESULT remove_ScriptDialogOpening(
      [in] EventRegistrationToken token);

  /// Add an event handler for the `PermissionRequested` event.
  /// `PermissionRequested` runs when content in a WebView requests permission
  /// to access some privileged resources.
  ///
  /// If a deferral is not taken on the event args, the subsequent scripts are
  /// blocked until the event handler returns.  If a deferral is taken, the
  /// scripts are blocked until the deferral is completed.
  ///
  /// \snippet SettingsComponent.cpp PermissionRequested0
  /// \snippet SettingsComponent.cpp PermissionRequested1
  HRESULT add_PermissionRequested(
      [in] ICoreWebView2PermissionRequestedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_PermissionRequested`.
  HRESULT remove_PermissionRequested(
      [in] EventRegistrationToken token);

  /// Add an event handler for the `ProcessFailed` event.  `ProcessFailed` runs
  /// when a WebView process ends unexpectedly or becomes unresponsive.
  ///
  /// \snippet ProcessComponent.cpp ProcessFailed
  HRESULT add_ProcessFailed(
      [in] ICoreWebView2ProcessFailedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with add_ProcessFailed.
  HRESULT remove_ProcessFailed(
      [in] EventRegistrationToken token);

  /// Add the provided JavaScript to a list of scripts that should be run after
  /// the global object has been created, but before the HTML document has
  /// been parsed and before any other script included by the HTML document is
  /// run.  This method injects a script that runs on all top-level document
  /// and child frame page navigations.  This method runs asynchronously, and
  /// you must wait for the completion handler to finish before the injected
  /// script is ready to run.  When this method completes, the `Invoke` method
  /// of the handler is run with the `id` of the injected script.  `id` is a
  /// string.  To remove the injected script, use
  /// `RemoveScriptToExecuteOnDocumentCreated`.
  ///
  /// If the method is run in add_NewWindowRequested handler it should be called
  /// after the new window is set. For more details see `ICoreWebView2NewWindowRequestedEventArgs::put_NewWindow`.
  ///
  /// \> [!NOTE]\n\> If an HTML document is running in a sandbox of some kind using
  /// [sandbox](https://developer.mozilla.org/docs/Web/HTML/Element/iframe#attr-sandbox)
  /// properties or the
  /// [Content-Security-Policy](https://developer.mozilla.org/docs/Web/HTTP/Headers/Content-Security-Policy)
  /// HTTP header affects the script that runs.  For example, if the
  /// `allow-modals` keyword is not set then requests to run the `alert`
  /// function are ignored.
  ///
  /// \snippet ScriptComponent.cpp AddScriptToExecuteOnDocumentCreated
  HRESULT AddScriptToExecuteOnDocumentCreated(
      [in] LPCWSTR javaScript,
      [in] ICoreWebView2AddScriptToExecuteOnDocumentCreatedCompletedHandler* handler);

  /// Remove the corresponding JavaScript added using
  /// `AddScriptToExecuteOnDocumentCreated` with the specified script ID.
  HRESULT RemoveScriptToExecuteOnDocumentCreated([in] LPCWSTR id);

  /// Run JavaScript code from the javascript parameter in the current
  /// top-level document rendered in the WebView.  The result of evaluating
  /// the provided JavaScript is used in this parameter.  The result value is
  /// a JSON encoded string.  If the result is undefined, contains a reference
  /// cycle, or otherwise is not able to be encoded into JSON, then the result
  /// is considered to be null, which is encoded in JSON as the string "null".
  ///
  /// \> [!NOTE]\n\> A function that has no explicit return value returns undefined. If the
  /// script that was run throws an unhandled exception, then the result is
  /// also "null".  This method is applied asynchronously. If the method is
  /// run after the `NavigationStarting` event during a navigation, the script
  /// runs in the new document when loading it, around the time
  /// `ContentLoading` is run.  This operation executes the script even if
  /// `ICoreWebView2Settings::IsScriptEnabled` is set to `FALSE`.
  ///
  /// \snippet ScriptComponent.cpp ExecuteScript
  HRESULT ExecuteScript(
      [in] LPCWSTR javaScript,
      [in] ICoreWebView2ExecuteScriptCompletedHandler* handler);

  /// Capture an image of what WebView is displaying.  Specify the format of
  /// the image with the `imageFormat` parameter.  The resulting image binary
  /// data is written to the provided `imageStream` parameter.  When
  /// `CapturePreview` finishes writing to the stream, the `Invoke` method on
  /// the provided `handler` parameter is run.  This method fails if called
  /// before the first ContentLoading event.  For example if this is called in
  /// the NavigationStarting event for the first navigation it will fail.
  /// For subsequent navigations, the method may not fail, but will not capture
  /// an image of a given webpage until the ContentLoading event has been fired
  /// for it.  Any call to this method prior to that will result in a capture of
  /// the page being navigated away from.
  ///
  /// \snippet FileComponent.cpp CapturePreview
  HRESULT CapturePreview(
      [in] COREWEBVIEW2_CAPTURE_PREVIEW_IMAGE_FORMAT imageFormat,
      [in] IStream* imageStream,
      [in] ICoreWebView2CapturePreviewCompletedHandler* handler);

  /// Reload the current page.  This is similar to navigating to the URI of
  /// current top level document including all navigation events firing and
  /// respecting any entries in the HTTP cache.  But, the back or forward
  /// history are not modified.
  HRESULT Reload();

  /// Post the specified webMessage to the top level document in this WebView.
  /// The main page receives the message by subscribing to the `message` event of the
  /// `window.chrome.webview` of the page document.
  ///
  /// ```cpp
  /// window.chrome.webview.addEventListener('message', handler)
  /// window.chrome.webview.removeEventListener('message', handler)
  /// ```
  ///
  /// The event args is an instance of `MessageEvent`.  The
  /// `ICoreWebView2Settings::IsWebMessageEnabled` setting must be `TRUE` or
  /// this method fails with `E_INVALIDARG`.  The `data` property of the event
  /// arg is the `webMessage` string parameter parsed as a JSON string into a
  /// JavaScript object.  The `source` property of the event arg is a reference
  ///  to the `window.chrome.webview` object.  For information about sending
  /// messages from the HTML document in the WebView to the host, navigate to
  /// [add_WebMessageReceived](/microsoft-edge/webview2/reference/win32/icorewebview2#add_webmessagereceived).
  /// The message is delivered asynchronously.  If a navigation occurs before
  /// the message is posted to the page, the message is discarded.
  ///
  /// \snippet ScenarioWebMessage.cpp WebMessageReceived
  HRESULT PostWebMessageAsJson([in] LPCWSTR webMessageAsJson);

  /// Posts a message that is a simple string rather than a JSON string
  /// representation of a JavaScript object.  This behaves in exactly the same
  /// manner as `PostWebMessageAsJson`, but the `data` property of the event
  /// arg of the `window.chrome.webview` message is a string with the same
  /// value as `webMessageAsString`.  Use this instead of
  /// `PostWebMessageAsJson` if you want to communicate using simple strings
  /// rather than JSON objects.
  HRESULT PostWebMessageAsString([in] LPCWSTR webMessageAsString);

  /// Add an event handler for the `WebMessageReceived` event.
  /// `WebMessageReceived` runs when the
  /// `ICoreWebView2Settings::IsWebMessageEnabled` setting is set and the
  /// top-level document of the WebView runs
  /// `window.chrome.webview.postMessage`.  The `postMessage` function is
  /// `void postMessage(object)` where object is any object supported by JSON
  /// conversion.
  ///
  /// \snippet assets\ScenarioWebMessage.html chromeWebView
  ///
  /// When the page calls `postMessage`, the object parameter is converted to a
  /// JSON string and is posted asynchronously to the host process. This will
  /// result in the handler's `Invoke` method being called with the JSON string
  /// as a parameter.
  ///
  /// \snippet ScenarioWebMessage.cpp WebMessageReceived
  ///
  /// If the same page calls `postMessage` multiple times, the corresponding
  /// `WebMessageReceived` events are guaranteed to be fired in the same order.
  /// However, if multiple frames call `postMessage`, there is no guaranteed
  /// order.  In addition, `WebMessageReceived` events caused by calls to
  /// `postMessage` are not guaranteed to be sequenced with events caused by DOM
  /// APIs.  For example, if the page runs
  ///
  /// ```javascript
  /// chrome.webview.postMessage("message");
  /// window.open();
  /// ```
  ///
  /// then the `NewWindowRequested` event might be fired before the
  /// `WebMessageReceived` event.  If you need the `WebMessageReceived` event
  /// to happen before anything else, then in the `WebMessageReceived` handler
  /// you can post a message back to the page and have the page wait until it
  /// receives that message before continuing.
  HRESULT add_WebMessageReceived(
      [in] ICoreWebView2WebMessageReceivedEventHandler* handler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_WebMessageReceived`.
  HRESULT remove_WebMessageReceived(
      [in] EventRegistrationToken token);

  /// Runs an asynchronous `DevToolsProtocol` method.  For more information
  /// about available methods, navigate to
  /// [DevTools Protocol Viewer](https://chromedevtools.github.io/devtools-protocol/tot)
  /// .  The `methodName` parameter is the full name of the method in the
  /// `{domain}.{method}` format.  The `parametersAsJson` parameter is a JSON
  /// formatted string containing the parameters for the corresponding method.
  /// The `Invoke` method of the `handler` is run when the method
  /// asynchronously completes.  `Invoke` is run with the return object of the
  /// method as a JSON string.  This function returns E_INVALIDARG if the `methodName` is
  /// unknown or the `parametersAsJson` has an error.  In the case of such an error, the
  /// `returnObjectAsJson` parameter of the handler will include information
  /// about the error.
  ///
  /// \snippet ScriptComponent.cpp CallDevToolsProtocolMethod
  HRESULT CallDevToolsProtocolMethod(
      [in] LPCWSTR methodName,
      [in] LPCWSTR parametersAsJson,
      [in] ICoreWebView2CallDevToolsProtocolMethodCompletedHandler* handler);

  /// The process ID of the browser process that hosts the WebView.
  [propget] HRESULT BrowserProcessId([out, retval] UINT32* value);

  /// `TRUE` if the WebView is able to navigate to a previous page in the
  /// navigation history.  If `CanGoBack` changes value, the `HistoryChanged`
  /// event runs.
  [propget] HRESULT CanGoBack([out, retval] BOOL* canGoBack);

  /// `TRUE` if the WebView is able to navigate to a next page in the
  /// navigation history.  If `CanGoForward` changes value, the
  /// `HistoryChanged` event runs.
  [propget] HRESULT CanGoForward([out, retval] BOOL* canGoForward);

  /// Navigates the WebView to the previous page in the navigation history.
  HRESULT GoBack();

  /// Navigates the WebView to the next page in the navigation history.
  HRESULT GoForward();

  /// Get a DevTools Protocol event receiver that allows you to subscribe to a
  /// DevTools Protocol event.  The `eventName` parameter is the full name of
  /// the event in the format `{domain}.{event}`.  For more information about
  /// DevTools Protocol events description and event args, navigate to
  /// [DevTools Protocol Viewer](https://chromedevtools.github.io/devtools-protocol/tot).
  ///
  /// \snippet ScriptComponent.cpp DevToolsProtocolEventReceived
  HRESULT GetDevToolsProtocolEventReceiver(
      [in] LPCWSTR eventName,
      [out, retval] ICoreWebView2DevToolsProtocolEventReceiver** receiver);

  /// Stop all navigations and pending resource fetches.  Does not stop scripts.
  HRESULT Stop();

  /// Add an event handler for the `NewWindowRequested` event.
  /// `NewWindowRequested` runs when content inside the WebView requests to
  /// open a new window, such as through `window.open`.  The app passes a
  /// target WebView that is considered the opened window.
  ///
  /// If a deferral is not taken on the event args, scripts that resulted in
  /// the new window that are requested are blocked until the event handler
  /// returns.  If a deferral is taken, then scripts are blocked until the
  /// deferral is completed or new window is set.
  ///
  /// For more details and considerations on the target WebView to be supplied
  /// at the opened window, see `ICoreWebView2NewWindowRequestedEventArgs::put_NewWindow`.
  ///
  /// \snippet AppWindow.cpp NewWindowRequested
  HRESULT add_NewWindowRequested(
      [in] ICoreWebView2NewWindowRequestedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_NewWindowRequested`.
  HRESULT remove_NewWindowRequested(
      [in] EventRegistrationToken token);

  /// Add an event handler for the `DocumentTitleChanged` event.
  /// `DocumentTitleChanged` runs when the `DocumentTitle` property of the
  /// WebView changes and may run before or after the `NavigationCompleted`
  /// event.
  ///
  /// \snippet FileComponent.cpp DocumentTitleChanged
  HRESULT add_DocumentTitleChanged(
      [in] ICoreWebView2DocumentTitleChangedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_DocumentTitleChanged`.
  HRESULT remove_DocumentTitleChanged(
      [in] EventRegistrationToken token);

  /// The title for the current top-level document.  If the document has no
  /// explicit title or is otherwise empty, a default that may or may not match
  ///  the URI of the document is used.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT DocumentTitle([out, retval] LPWSTR* title);

  /// Add the provided host object to script running in the WebView with the
  /// specified name.  Host objects are exposed as host object proxies using
  /// `window.chrome.webview.hostObjects.{name}`.  Host object proxies are
  /// promises and resolves to an object representing the host object.  The
  /// promise is rejected if the app has not added an object with the name.
  /// When JavaScript code access a property or method of the object, a promise
  ///  is return, which resolves to the value returned from the host for the
  /// property or method, or rejected in case of error, for example, no
  /// property or method on the object or parameters are not valid.
  ///
  /// \> [!NOTE]\n\> While simple types, `IDispatch` and array are supported, and
  /// `IUnknown` objects that also implement `IDispatch` are treated as `IDispatch`,
  /// generic `IUnknown`, `VT_DECIMAL`, or `VT_RECORD` variant is not supported.
  /// Remote JavaScript objects like callback functions are represented as an
  /// `VT_DISPATCH` `VARIANT` with the object implementing `IDispatch`.  The
  /// JavaScript callback method may be invoked using `DISPID_VALUE` for the
  /// `DISPID`.  Nested arrays are supported up to a depth of 3.  Arrays of by
  /// reference types are not supported. `VT_EMPTY` and `VT_NULL` are mapped
  /// into JavaScript as `null`.  In JavaScript, `null` and undefined are
  /// mapped to `VT_EMPTY`.
  ///
  /// Additionally, all host objects are exposed as
  /// `window.chrome.webview.hostObjects.sync.{name}`.  Here the host objects
  /// are exposed as synchronous host object proxies. These are not promises
  /// and function runtimes or property access synchronously block running
  /// script waiting to communicate cross process for the host code to run.
  /// Accordingly the result may have reliability issues and it is recommended
  /// that you use the promise-based asynchronous
  /// `window.chrome.webview.hostObjects.{name}` API.
  ///
  /// Synchronous host object proxies and asynchronous host object proxies may
  /// both use a proxy to the same host object.  Remote changes made by one
  /// proxy propagates to any other proxy of that same host object whether
  /// the other proxies and synchronous or asynchronous.
  ///
  /// While JavaScript is blocked on a synchronous run to native code, that
  /// native code is unable to run back to JavaScript.  Attempts to do so fail
  ///  with `HRESULT_FROM_WIN32(ERROR_POSSIBLE_DEADLOCK)`.
  ///
  /// Host object proxies are JavaScript Proxy objects that intercept all
  /// property get, property set, and method invocations. Properties or methods
  ///  that are a part of the Function or Object prototype are run locally.
  /// Additionally any property or method in the
  /// `chrome.webview.hostObjects.options.forceLocalProperties`
  /// array are also run locally.  This defaults to including optional methods
  /// that have meaning in JavaScript like `toJSON` and `Symbol.toPrimitive`.
  /// Add more to the array as required.
  ///
  /// The `chrome.webview.hostObjects.cleanupSome` method performs a best
  /// effort garbage collection on host object proxies.
  ///
  /// The `chrome.webview.hostObjects.options` object provides the ability to
  /// change some functionality of host objects.
  ///
  /// Options property | Details
  /// ---|---
  /// `forceLocalProperties` | This is an array of host object property names that will be run locally, instead of being called on the native host object. This defaults to `then`, `toJSON`, `Symbol.toString`, and `Symbol.toPrimitive`. You can add other properties to specify that they should be run locally on the javascript host object proxy.
  /// `log` | This is a callback that will be called with debug information. For example, you can set this to `console.log.bind(console)` to have it print debug information to the console to help when troubleshooting host object usage. By default this is null.
  /// `shouldSerializeDates` | By default this is false, and javascript Date objects will be sent to host objects as a string using `JSON.stringify`. You can set this property to true to have Date objects properly serialize as a `VT_DATE` when sending to the native host object, and have `VT_DATE` properties and return values create a javascript Date object.
  /// `defaultSyncProxy` | When calling a method on a synchronous proxy, the result should also be a synchronous proxy. But in some cases, the sync/async context is lost (for example, when providing to native code a reference to a function, and then calling that function in native code). In these cases, the proxy will be asynchronous, unless this property is set.
  /// `forceAsyncMethodMatches ` | This is an array of regular expressions. When calling a method on a synchronous proxy, the method call will be performed asynchronously if the method name matches a string or regular expression in this array. Setting this value to `Async` will make any method that ends with Async be an asynchronous method call. If an async method doesn't match here and isn't forced to be asynchronous, the method will be invoked synchronously, blocking execution of the calling JavaScript and then returning the resolution of the promise, rather than returning a promise.
  /// `ignoreMemberNotFoundError` | By default, an exception is thrown when attempting to get the value of a proxy property that doesn't exist on the corresponding native class. Setting this property to `true` switches the behavior to match Chakra WinRT projection (and general JavaScript) behavior of returning `undefined` with no error.
  ///
  /// Host object proxies additionally have the following methods which run
  /// locally.
  ///
  /// Method name | Details
  /// ---|---
  ///`applyHostFunction`, `getHostProperty`, `setHostProperty` | Perform a method invocation, property get, or property set on the host object. Use the methods to explicitly force a method or property to run remotely if a conflicting local method or property exists.  For instance, `proxy.toString()` runs the local `toString` method on the proxy object. But proxy.applyHostFunction('toString') runs `toString` on the host proxied object instead.
  ///`getLocalProperty`, `setLocalProperty` | Perform property get, or property set locally.  Use the methods to force getting or setting a property on the host object proxy rather than on the host object it represents. For instance, `proxy.unknownProperty` gets the property named `unknownProperty` from the host proxied object.  But proxy.getLocalProperty('unknownProperty') gets the value of the property `unknownProperty` on the proxy object.
  ///`sync` | Asynchronous host object proxies expose a sync method which returns a promise for a synchronous host object proxy for the same host object.  For example, `chrome.webview.hostObjects.sample.methodCall()` returns an asynchronous host object proxy.  Use the `sync` method to obtain a synchronous host object proxy instead: `const syncProxy = await chrome.webview.hostObjects.sample.methodCall().sync()`.
  ///`async` | Synchronous host object proxies expose an async method which blocks and returns an asynchronous host object proxy for the same host object.  For example, `chrome.webview.hostObjects.sync.sample.methodCall()` returns a synchronous host object proxy.  Running the `async` method on this blocks and then returns an asynchronous host object proxy for the same host object: `const asyncProxy = chrome.webview.hostObjects.sync.sample.methodCall().async()`.
  ///`then` | Asynchronous host object proxies have a `then` method.  Allows proxies to be awaitable.  `then` returns a promise that resolves with a representation of the host object.  If the proxy represents a JavaScript literal, a copy of that is returned locally.  If the proxy represents a function, a non-awaitable proxy is returned.  If the proxy represents a JavaScript object with a mix of literal properties and function properties, the a copy of the object is returned with some properties as host object proxies.
  ///
  /// All other property and method invocations (other than the above Remote
  /// object proxy methods, `forceLocalProperties` list, and properties on
  /// Function and Object prototypes) are run remotely.  Asynchronous host
  /// object proxies return a promise representing asynchronous completion of
  /// remotely invoking the method, or getting the property.  The promise
  /// resolves after the remote operations complete and the promises resolve to
  ///  the resulting value of the operation.  Synchronous host object proxies
  /// work similarly, but block running JavaScript and wait for the remote
  /// operation to complete.
  ///
  /// Setting a property on an asynchronous host object proxy works slightly
  /// differently.  The set returns immediately and the return value is the
  /// value that is set.  This is a requirement of the JavaScript Proxy object.
  /// If you need to asynchronously wait for the property set to complete, use
  /// the `setHostProperty` method which returns a promise as described above.
  /// Synchronous object property set property synchronously blocks until the
  /// property is set.
  ///
  /// For example, suppose you have a COM object with the following interface.
  ///
  /// \snippet HostObjectSample.idl AddHostObjectInterface
  ///
  /// Add an instance of this interface into your JavaScript with
  /// `AddHostObjectToScript`.  In this case, name it `sample`.
  ///
  /// \snippet ScenarioAddHostObject.cpp AddHostObjectToScript
  ///
  /// In the HTML document, use the COM object using
  /// `chrome.webview.hostObjects.sample`.
  ///
  /// \snippet assets\ScenarioAddHostObject.html HostObjectUsage
  ///
  /// Exposing host objects to script has security risk.  For more information
  /// about best practices, navigate to
  /// [Best practices for developing secure WebView2 applications](/microsoft-edge/webview2/concepts/security).
  HRESULT AddHostObjectToScript([in] LPCWSTR name, [in] VARIANT* object);

  /// Remove the host object specified by the name so that it is no longer
  /// accessible from JavaScript code in the WebView.  While new access
  /// attempts are denied, if the object is already obtained by JavaScript code
  /// in the WebView, the JavaScript code continues to have access to that
  /// object.   Run this method for a name that is already removed or never
  /// added fails.
  HRESULT RemoveHostObjectFromScript([in] LPCWSTR name);

  /// Opens the DevTools window for the current document in the WebView. Does
  /// nothing if run when the DevTools window is already open.
  HRESULT OpenDevToolsWindow();

  /// Add an event handler for the `ContainsFullScreenElementChanged` event.
  /// `ContainsFullScreenElementChanged` triggers when the
  /// `ContainsFullScreenElement` property changes.  An HTML element inside the
  /// WebView may enter fullscreen to the size of the WebView or leave
  /// fullscreen.  This event is useful when, for example, a video element
  /// requests to go fullscreen.  The listener of
  /// `ContainsFullScreenElementChanged` may resize the WebView in response.
  ///
  /// \snippet AppWindow.cpp ContainsFullScreenElementChanged
  HRESULT add_ContainsFullScreenElementChanged(
      [in] ICoreWebView2ContainsFullScreenElementChangedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with
  /// `add_ContainsFullScreenElementChanged`.
  HRESULT remove_ContainsFullScreenElementChanged(
      [in] EventRegistrationToken token);

  /// Indicates if the WebView contains a fullscreen HTML element.
  [propget] HRESULT ContainsFullScreenElement(
      [out, retval] BOOL* containsFullScreenElement);

  /// Add an event handler for the `WebResourceRequested` event.
  /// `WebResourceRequested` runs when the WebView is performing a URL request
  /// to a matching URL and resource context filter that was added with
  /// `AddWebResourceRequestedFilter`.  At least one filter must be added for
  /// the event to run.
  ///
  /// The web resource requested may be blocked until the event handler returns
  /// if a deferral is not taken on the event args.  If a deferral is taken,
  /// then the web resource requested is blocked until the deferral is
  /// completed.
  ///
  /// If this event is subscribed in the add_NewWindowRequested handler it should be called
  /// after the new window is set. For more details see `ICoreWebView2NewWindowRequestedEventArgs::put_NewWindow`.
  ///
  /// Currently this only supports file, http, and https URI schemes.
  ///
  /// \snippet SettingsComponent.cpp WebResourceRequested0
  /// \snippet SettingsComponent.cpp WebResourceRequested1
  HRESULT add_WebResourceRequested(
    [in] ICoreWebView2WebResourceRequestedEventHandler* eventHandler,
    [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_WebResourceRequested`.
  HRESULT remove_WebResourceRequested(
      [in] EventRegistrationToken token);

  /// Adds a URI and resource context filter for the `WebResourceRequested`
  /// event.  A web resource request with a resource context that matches this
  /// filter's resource context and a URI that matches this filter's URI
  /// wildcard string will be raised via the `WebResourceRequested` event.
  ///
  /// The `uri` parameter value is a wildcard string matched against the URI
  /// of the web resource request. This is a glob style
  /// wildcard string in which a `*` matches zero or more characters and a `?`
  /// matches exactly one character.
  /// These wildcard characters can be escaped using a backslash just before
  /// the wildcard character in order to represent the literal `*` or `?`.
  ///
  /// The matching occurs over the URI as a whole string and not limiting
  /// wildcard matches to particular parts of the URI.
  /// The wildcard filter is compared to the URI after the URI has been
  /// normalized, any URI fragment has been removed, and non-ASCII hostnames
  /// have been converted to punycode.
  ///
  /// Specifying a `nullptr` for the uri is equivalent to an empty string which
  /// matches no URIs.
  ///
  /// For more information about resource context filters, navigate to
  /// [COREWEBVIEW2_WEB_RESOURCE_CONTEXT](/microsoft-edge/webview2/reference/win32/icorewebview2#corewebview2_web_resource_context).
  ///
  /// | URI Filter String | Request URI | Match | Notes |
  /// | ---- | ---- | ---- | ---- |
  /// | `*` | `https://contoso.com/a/b/c` | Yes | A single * will match all URIs |
  /// | `*://contoso.com/*` | `https://contoso.com/a/b/c` | Yes | Matches everything in contoso.com across all schemes |
  /// | `*://contoso.com/*` | `https://example.com/?https://contoso.com/` | Yes | But also matches a URI with just the same text anywhere in the URI |
  /// | `example` | `https://contoso.com/example` | No | The filter does not perform partial matches |
  /// | `*example` | `https://contoso.com/example` | Yes | The filter matches across URI parts  |
  /// | `*example` | `https://contoso.com/path/?example` | Yes | The filter matches across URI parts |
  /// | `*example` | `https://contoso.com/path/?query#example` | No | The filter is matched against the URI with no fragment |
  /// | `*example` | `https://example` | No | The URI is normalized before filter matching so the actual URI used for comparison is `https://example/` |
  /// | `*example/` | `https://example` | Yes | Just like above, but this time the filter ends with a / just like the normalized URI |
  /// | `https://xn--qei.example/` | `https://&#x2764;.example/` | Yes | Non-ASCII hostnames are normalized to punycode before wildcard comparison |
  /// | `https://&#x2764;.example/` | `https://xn--qei.example/` | No | Non-ASCII hostnames are normalized to punycode before wildcard comparison |
  HRESULT AddWebResourceRequestedFilter(
    [in] LPCWSTR const uri,
    [in] COREWEBVIEW2_WEB_RESOURCE_CONTEXT const resourceContext);

  /// Removes a matching WebResource filter that was previously added for the
  /// `WebResourceRequested` event.  If the same filter was added multiple
  /// times, then it must be removed as many times as it was added for the
  /// removal to be effective.  Returns `E_INVALIDARG` for a filter that was
  /// never added.
  HRESULT RemoveWebResourceRequestedFilter(
    [in] LPCWSTR const uri,
    [in] COREWEBVIEW2_WEB_RESOURCE_CONTEXT const resourceContext);

  /// Add an event handler for the `WindowCloseRequested` event.
  /// `WindowCloseRequested` triggers when content inside the WebView
  /// requested to close the window, such as after `window.close` is run.  The
  /// app should close the WebView and related app window if that makes sense
  /// to the app.
  ///
  /// \snippet AppWindow.cpp WindowCloseRequested
  HRESULT add_WindowCloseRequested(
      [in] ICoreWebView2WindowCloseRequestedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_WindowCloseRequested`.
  HRESULT remove_WindowCloseRequested(
      [in] EventRegistrationToken token);
}

/// A continuation of the ICoreWebView2 interface.
[uuid(9E8F0CF8-E670-4B5E-B2BC-73E061E3184C), object, pointer_default(unique)]
interface ICoreWebView2_2 : ICoreWebView2 {
  /// Add an event handler for the WebResourceResponseReceived event.
  /// WebResourceResponseReceived is raised when the WebView receives the
  /// response for a request for a web resource (any URI resolution performed by
  /// the WebView; such as HTTP/HTTPS, file and data requests from redirects,
  /// navigations, declarations in HTML, implicit favicon lookups, and fetch API
  /// usage in the document). The host app can use this event to view the actual
  /// request and response for a web resource. There is no guarantee about the
  /// order in which the WebView processes the response and the host app's
  /// handler runs. The app's handler will not block the WebView from processing
  /// the response.
  /// \snippet ScenarioAuthentication.cpp WebResourceResponseReceived
  HRESULT add_WebResourceResponseReceived(
    [in] ICoreWebView2WebResourceResponseReceivedEventHandler* eventHandler,
    [out] EventRegistrationToken* token);
  /// Remove an event handler previously added with
  /// add_WebResourceResponseReceived.
  HRESULT remove_WebResourceResponseReceived(
    [in] EventRegistrationToken token);

  /// Navigates using a constructed WebResourceRequest object. This lets you
  /// provide post data or additional request headers during navigation.
  /// The headers in the WebResourceRequest override headers
  /// added by WebView2 runtime except for Cookie headers.
  /// Method can only be either "GET" or "POST". Provided post data will only
  /// be sent only if the method is "POST" and the uri scheme is HTTP(S).
  /// \snippet ScenarioNavigateWithWebResourceRequest.cpp NavigateWithWebResourceRequest
  HRESULT NavigateWithWebResourceRequest([in] ICoreWebView2WebResourceRequest* request);

  /// Add an event handler for the DOMContentLoaded event.
  /// DOMContentLoaded is raised when the initial html document has been parsed.
  /// This aligns with the document's DOMContentLoaded event in html.
  ///
  /// \snippet ScenarioDOMContentLoaded.cpp DOMContentLoaded
  HRESULT add_DOMContentLoaded(
      [in] ICoreWebView2DOMContentLoadedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with add_DOMContentLoaded.
  HRESULT remove_DOMContentLoaded(
      [in] EventRegistrationToken token);

  /// Gets the cookie manager object associated with this ICoreWebView2.
  /// See ICoreWebView2CookieManager.
  ///
  /// \snippet ScenarioCookieManagement.cpp CookieManager
  [propget] HRESULT CookieManager([out, retval] ICoreWebView2CookieManager** cookieManager);

  /// Exposes the CoreWebView2Environment used to create this CoreWebView2.
  [propget] HRESULT Environment([out, retval] ICoreWebView2Environment** environment);
}

/// A continuation of the ICoreWebView2_2 interface.
[uuid(A0D6DF20-3B92-416D-AA0C-437A9C727857), object, pointer_default(unique)]
interface ICoreWebView2_3 : ICoreWebView2_2 {
  /// An app may call the `TrySuspend` API to have the WebView2 consume less memory.
  /// This is useful when a Win32 app becomes invisible, or when a Universal Windows
  /// Platform app is being suspended, during the suspended event handler before completing
  /// the suspended event.
  /// The CoreWebView2Controller's IsVisible property must be false when the API is called.
  /// Otherwise, the API fails with `HRESULT_FROM_WIN32(ERROR_INVALID_STATE)`.
  /// Suspending is similar to putting a tab to sleep in the Edge browser. Suspending pauses
  /// WebView script timers and animations, minimizes CPU usage for the associated
  /// browser renderer process and allows the operating system to reuse the memory that was
  /// used by the renderer process for other processes.
  /// Note that Suspend is best effort and considered completed successfully once the request
  /// is sent to browser renderer process. If there is a running script, the script will continue
  /// to run and the renderer process will be suspended after that script is done.
  /// See [Sleeping Tabs FAQ](https://techcommunity.microsoft.com/t5/articles/sleeping-tabs-faq/m-p/1705434)
  /// for conditions that might prevent WebView from being suspended. In those situations,
  /// the completed handler will be invoked with isSuccessful as false and errorCode as S_OK.
  /// The WebView will be automatically resumed when it becomes visible. Therefore, the
  /// app normally does not have to call `Resume` explicitly.
  /// The app can call `Resume` and then `TrySuspend` periodically for an invisible WebView so that
  /// the invisible WebView can sync up with latest data and the page ready to show fresh content
  /// when it becomes visible.
  /// All WebView APIs can still be accessed when a WebView is suspended. Some APIs like Navigate
  /// will auto resume the WebView. To avoid unexpected auto resume, check `IsSuspended` property
  /// before calling APIs that might change WebView state.
  ///
  /// \snippet ViewComponent.cpp ToggleIsVisibleOnMinimize
  ///
  /// \snippet ViewComponent.cpp Suspend
  ///
  HRESULT TrySuspend([in] ICoreWebView2TrySuspendCompletedHandler* handler);

  /// Resumes the WebView so that it resumes activities on the web page.
  /// This API can be called while the WebView2 controller is invisible.
  /// The app can interact with the WebView immediately after `Resume`.
  /// WebView will be automatically resumed when it becomes visible.
  ///
  /// \snippet ViewComponent.cpp ToggleIsVisibleOnMinimize
  ///
  /// \snippet ViewComponent.cpp Resume
  ///
  HRESULT Resume();

  /// Whether WebView is suspended.
  /// `TRUE` when WebView is suspended, from the time when TrySuspend has completed
  ///  successfully until WebView is resumed.
  [propget] HRESULT IsSuspended([out, retval] BOOL* isSuspended);

  /// Sets a mapping between a virtual host name and a folder path to make available to web sites
  /// via that host name.
  ///
  /// After setting the mapping, documents loaded in the WebView can use HTTP or HTTPS URLs at
  /// the specified host name specified by hostName to access files in the local folder specified
  /// by folderPath.
  ///
  /// This mapping applies to both top-level document and iframe navigations as well as subresource
  /// references from a document. This also applies to web workers including dedicated/shared worker
  /// and service worker, for loading either worker scripts or subresources
  /// (importScripts(), fetch(), XHR, etc.) issued from within a worker.
  /// For virtual host mapping to work with service worker, please keep the virtual host name
  /// mappings consistent among all WebViews sharing the same browser instance. As service worker
  /// works independently of WebViews, we merge mappings from all WebViews when resolving virtual
  /// host name, inconsistent mappings between WebViews would lead unexpected behavior.
  ///
  /// Due to a current implementation limitation, media files accessed using virtual host name can be
  /// very slow to load.
  /// As the resource loaders for the current page might have already been created and running,
  /// changes to the mapping might not be applied to the current page and a reload of the page is
  /// needed to apply the new mapping.
  ///
  /// Both absolute and relative paths are supported for folderPath. Relative paths are interpreted
  /// as relative to the folder where the exe of the app is in.
  ///
  /// Note that the folderPath length must not exceed the Windows MAX_PATH limit.
  ///
  /// accessKind specifies the level of access to resources under the virtual host from other sites.
  ///
  /// For example, after calling
  /// ```cpp
  ///    SetVirtualHostNameToFolderMapping(
  ///        L"appassets.example", L"assets",
  ///        COREWEBVIEW2_HOST_RESOURCE_ACCESS_KIND_DENY);
  /// ```
  /// navigating to `https://appassets.example/my-local-file.html` will
  /// show the content from my-local-file.html in the assets subfolder located on disk under
  /// the same path as the app's executable file.
  ///
  /// DOM elements that want to reference local files will have their host reference virtual host in the source.
  /// If there are multiple folders being used, define one unique virtual host per folder.
  /// For example, you can embed a local image like this
  /// ```cpp
  ///    WCHAR c_navString[] = L"<img src=\"http://appassets.example/wv2.png\"/>";
  ///    m_webView->NavigateToString(c_navString);
  /// ```
  /// The example above shows the image wv2.png by resolving the folder mapping above.
  ///
  /// You should typically choose virtual host names that are never used by real sites.
  /// If you own a domain such as example.com, another option is to use a subdomain reserved for
  /// the app (like my-app.example.com).
  ///
  /// [RFC 6761](https://tools.ietf.org/html/rfc6761) has reserved several special-use domain
  /// names that are guaranteed to not be used by real sites (for example, .example, .test, and
  /// .invalid.)
  ///
  /// Note that using `.local` as the top-level domain name will work but can cause a delay
  /// during navigations. You should avoid using `.local` if you can.
  ///
  /// Apps should use distinct domain names when mapping folder from different sources that
  /// should be isolated from each other. For instance, the app might use app-file.example for
  /// files that ship as part of the app, and book1.example might be used for files containing
  /// books from a less trusted source that were previously downloaded and saved to the disk by
  /// the app.
  ///
  /// The host name used in the APIs is canonicalized using Chromium's host name parsing logic
  /// before being used internally. For more information see [HTML5 2.6 URLs](https://dev.w3.org/html5/spec-LC/urls.html).
  ///
  /// All host names that are canonicalized to the same string are considered identical.
  /// For example, `EXAMPLE.COM` and `example.com` are treated as the same host name.
  /// An international host name and its Punycode-encoded host name are considered the same host
  /// name. There is no DNS resolution for host name and the trailing '.' is not normalized as
  /// part of canonicalization.
  ///
  /// Therefore `example.com` and `example.com.` are treated as different host names. Similarly,
  /// `virtual-host-name` and `virtual-host-name.example.com` are treated as different host names
  /// even if the machine has a DNS suffix of `example.com`.
  ///
  /// Specify the minimal cross-origin access necessary to run the app. If there is not a need to
  /// access local resources from other origins, use COREWEBVIEW2_HOST_RESOURCE_ACCESS_KIND_DENY.
  ///
  /// \snippet AppWindow.cpp AddVirtualHostNameToFolderMapping
  ///
  /// \snippet AppWindow.cpp LocalUrlUsage
  HRESULT SetVirtualHostNameToFolderMapping(
      [in] LPCWSTR hostName,
      [in] LPCWSTR folderPath,
      [in] COREWEBVIEW2_HOST_RESOURCE_ACCESS_KIND accessKind);

  /// Clears a host name mapping for local folder that was added by `SetVirtualHostNameToFolderMapping`.
  HRESULT ClearVirtualHostNameToFolderMapping(
      [in] LPCWSTR hostName);
}

/// A continuation of the ICoreWebView2_3 interface to support FrameCreated and
/// DownloadStarting events.
[uuid(20d02d59-6df2-42dc-bd06-f98a694b1302), object, pointer_default(unique)]
interface ICoreWebView2_4 : ICoreWebView2_3 {
  /// Raised when a new iframe is created. 
  /// Handle this event to get access to ICoreWebView2Frame objects.
  /// Use ICoreWebView2Frame.add_Destroyed to listen for when this iframe goes
  /// away.
  HRESULT add_FrameCreated(
      [in] ICoreWebView2FrameCreatedEventHandler * eventHandler,
      [out] EventRegistrationToken * token);

  /// Remove an event handler previously added with add_FrameCreated.
  HRESULT remove_FrameCreated([in] EventRegistrationToken token);

  /// Add an event handler for the `DownloadStarting` event. This event is
  /// raised when a download has begun, blocking the default download dialog,
  /// but not blocking the progress of the download.
  ///
  /// The host can choose to cancel a download, change the result file path,
  /// and hide the default download dialog.
  /// If the host chooses to cancel the download, the download is not saved, no
  /// dialog is shown, and the state is changed to
  /// COREWEBVIEW2_DOWNLOAD_STATE_INTERRUPTED with interrupt reason
  /// COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_USER_CANCELED. Otherwise, the
  /// download is saved to the default path after the event completes,
  /// and default download dialog is shown if the host did not choose to hide it.
  /// The host can change the visibility of the download dialog using the
  /// `Handled` property. If the event is not handled, downloads complete
  /// normally with the default dialog shown.
  ///
  /// \snippet ScenarioCustomDownloadExperience.cpp DownloadStarting
  HRESULT add_DownloadStarting(
    [in] ICoreWebView2DownloadStartingEventHandler* eventHandler,
    [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_DownloadStarting`.
  HRESULT remove_DownloadStarting(
      [in] EventRegistrationToken token);
}

/// A continuation of the ICoreWebView2_4 interface to support ClientCertificateRequested
/// event.
[uuid(bedb11b8-d63c-11eb-b8bc-0242ac130003), object, pointer_default(unique)]
interface ICoreWebView2_5 : ICoreWebView2_4 {
  /// Add an event handler for the ClientCertificateRequested event.
  /// The ClientCertificateRequested event is raised when the WebView2
  /// is making a request to an HTTP server that needs a client certificate
  /// for HTTP authentication.
  /// Read more about HTTP client certificates at
  /// [RFC 8446 The Transport Layer Security (TLS) Protocol Version 1.3](https://tools.ietf.org/html/rfc8446).
  ///
  /// With this event you have several options for responding to client certificate requests:
  ///
  /// Scenario                                                   | Handled | Cancel | SelectedCertificate
  /// ---------------------------------------------------------- | ------- | ------ | -------------------
  /// Respond to server with a certificate                       | True    | False  | MutuallyTrustedCertificate value
  /// Respond to server without certificate                      | True    | False  | null
  /// Display default client certificate selection dialog prompt | False   | False  | n/a
  /// Cancel the request                                         | n/a     | True   | n/a
  ///
  /// If you don't handle the event, WebView2 will
  /// show the default client certificate selection dialog prompt to user.
  ///
  /// \snippet SettingsComponent.cpp ClientCertificateRequested1
  /// \snippet ScenarioClientCertificateRequested.cpp ClientCertificateRequested2
  HRESULT add_ClientCertificateRequested(
      [in] ICoreWebView2ClientCertificateRequestedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with add_ClientCertificateRequested.
  HRESULT remove_ClientCertificateRequested([in] EventRegistrationToken token);
}

/// This interface is an extension of `ICoreWebView2_5` that manages opening
/// the browser task manager window.
[uuid(499aadac-d92c-4589-8a75-111bfc167795), object, pointer_default(unique)]
interface ICoreWebView2_6 : ICoreWebView2_5 {
  /// Opens the Browser Task Manager view as a new window in the foreground.
  /// If the Browser Task Manager is already open, this will bring it into
  /// the foreground. WebView2 currently blocks the Shift+Esc shortcut for
  /// opening the task manager. An end user can open the browser task manager
  /// manually via the `Browser task manager` entry of the DevTools window's
  /// title bar's context menu.
  HRESULT OpenTaskManagerWindow();
}

/// This interface is an extension of `ICoreWebView2_6` that supports printing
/// to PDF.
[uuid(79c24d83-09a3-45ae-9418-487f32a58740), object, pointer_default(unique)]
interface ICoreWebView2_7 : ICoreWebView2_6 {
  /// Print the current page to PDF asynchronously with the provided settings.
  /// See `ICoreWebView2PrintSettings` for description of settings. Passing
  /// nullptr for `printSettings` results in default print settings used.
  ///
  /// Use `resultFilePath` to specify the path to the PDF file. The host should
  /// provide an absolute path, including file name. If the path
  /// points to an existing file, the file will be overwritten. If the path is
  /// not valid, the method fails with `E_INVALIDARG`.
  ///
  /// The async `PrintToPdf` operation completes when the data has been written
  /// to the PDF file. At this time the
  /// `ICoreWebView2PrintToPdfCompletedHandler` is invoked. If the
  /// application exits before printing is complete, the file is not saved.
  /// Only one `PrintToPdf` operation can be in progress at a time. If
  /// `PrintToPdf` is called while a print to PDF job is in progress, the
  /// completed handler is immediately invoked with `isSuccessful` set to FALSE.
  ///
  /// \snippet FileComponent.cpp PrintToPdf
  HRESULT PrintToPdf(
    [in] LPCWSTR resultFilePath,
    [in] ICoreWebView2PrintSettings* printSettings,
    [in] ICoreWebView2PrintToPdfCompletedHandler* handler);
}

/// This interface is an extension of `ICoreWebView2_7` that supports media features.
[uuid(********-6E1E-43AB-B7B8-7B2C9E62E094), object, pointer_default(unique)]
interface ICoreWebView2_8 : ICoreWebView2_7 {
  /// Adds an event handler for the `IsMutedChanged` event.
  /// `IsMutedChanged` is raised when the IsMuted property changes value.
  ///
  /// \snippet AudioComponent.cpp IsMutedChanged
  HRESULT add_IsMutedChanged(
      [in] ICoreWebView2IsMutedChangedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_IsMutedChanged`.
  HRESULT remove_IsMutedChanged(
      [in] EventRegistrationToken token);

  /// Indicates whether all audio output from this CoreWebView2 is muted or not.
  ///
  /// \snippet AudioComponent.cpp ToggleIsMuted
  [propget] HRESULT IsMuted([out, retval] BOOL* value);

  /// Sets the `IsMuted` property.
  ///
  /// \snippet AudioComponent.cpp ToggleIsMuted
  [propput] HRESULT IsMuted([in] BOOL value);

  /// Adds an event handler for the `IsDocumentPlayingAudioChanged` event.
  /// `IsDocumentPlayingAudioChanged` is raised when the IsDocumentPlayingAudio property changes value.
  ///
  /// \snippet AudioComponent.cpp IsDocumentPlayingAudioChanged
  HRESULT add_IsDocumentPlayingAudioChanged(
      [in] ICoreWebView2IsDocumentPlayingAudioChangedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_IsDocumentPlayingAudioChanged`.
  HRESULT remove_IsDocumentPlayingAudioChanged(
      [in] EventRegistrationToken token);

  /// Indicates whether any audio output from this CoreWebView2 is playing.
  /// This property will be true if audio is playing even if IsMuted is true.
  ///
  /// \snippet AudioComponent.cpp IsDocumentPlayingAudio
  [propget] HRESULT IsDocumentPlayingAudio([out, retval] BOOL* value);
}

/// This interface is an extension of `ICoreWebView2_8` that default download
/// dialog positioning and anchoring.
[uuid(4d7b2eab-9fdc-468d-b998-a9260b5ed651), object, pointer_default(unique)]
interface ICoreWebView2_9 : ICoreWebView2_8 {
  /// Raised when the `IsDefaultDownloadDialogOpen` property changes. This event
  /// comes after the `DownloadStarting` event. Setting the `Handled` property
  /// on the `DownloadStartingEventArgs` disables the default download dialog
  /// and ensures that this event is never raised.
  HRESULT add_IsDefaultDownloadDialogOpenChanged(
      [in] ICoreWebView2IsDefaultDownloadDialogOpenChangedEventHandler* handler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with
  /// `add_IsDefaultDownloadDialogOpenChanged`.
  HRESULT remove_IsDefaultDownloadDialogOpenChanged(
      [in] EventRegistrationToken token);

  /// `TRUE` if the default download dialog is currently open. The value of this
  /// property changes only when the default download dialog is explicitly
  /// opened or closed. Hiding the WebView implicitly hides the dialog, but does
  /// not change the value of this property.
  [propget] HRESULT IsDefaultDownloadDialogOpen([out, retval] BOOL* value);

  /// Open the default download dialog. If the dialog is opened before there
  /// are recent downloads, the dialog shows all past downloads for the
  /// current profile. Otherwise, the dialog shows only the recent downloads
  /// with a "See more" button for past downloads. Calling this method raises
  /// the `IsDefaultDownloadDialogOpenChanged` event if the dialog was closed.
  /// No effect if the dialog is already open.
  ///
  /// \snippet ViewComponent.cpp ToggleDefaultDownloadDialog
  HRESULT OpenDefaultDownloadDialog();

  /// Close the default download dialog. Calling this method raises the
  /// `IsDefaultDownloadDialogOpenChanged` event if the dialog was open. No
  /// effect if the dialog is already closed.
  HRESULT CloseDefaultDownloadDialog();

  /// Get the default download dialog corner alignment.
  [propget] HRESULT DefaultDownloadDialogCornerAlignment(
      [out, retval] COREWEBVIEW2_DEFAULT_DOWNLOAD_DIALOG_CORNER_ALIGNMENT* value);

  /// Set the default download dialog corner alignment. The dialog can be
  /// aligned to any of the WebView corners (see
  /// COREWEBVIEW2_DEFAULT_DOWNLOAD_DIALOG_CORNER_ALIGNMENT). When the WebView
  /// or dialog changes size, the dialog keeps its position relative to the
  /// corner. The dialog may become partially or completely outside of the
  /// WebView bounds if the WebView is small enough. Set the margin relative to
  /// the corner with the `DefaultDownloadDialogMargin` property.
  ///
  /// \snippet ViewComponent.cpp SetDefaultDownloadDialogPosition
  [propput] HRESULT DefaultDownloadDialogCornerAlignment(
      [in] COREWEBVIEW2_DEFAULT_DOWNLOAD_DIALOG_CORNER_ALIGNMENT value);

  /// Get the default download dialog margin.
  [propget] HRESULT DefaultDownloadDialogMargin([out, retval] POINT* value);

  /// Set the default download dialog margin relative to the WebView corner
  /// specified by `DefaultDownloadDialogCornerAlignment`. The margin is a
  /// point that describes the vertical and horizontal distances between the
  /// chosen WebView corner and the default download dialog corner nearest to
  /// it. Positive values move the dialog towards the center of the WebView from
  /// the chosen WebView corner, and negative values move the dialog away from
  /// it. Use (0, 0) to align the dialog to the WebView corner with no margin.
  [propput] HRESULT DefaultDownloadDialogMargin([in] POINT value);
}

/// This interface is an extension of `ICoreWebView2_9` that supports
/// BasicAuthenticationRequested event.
[uuid(b1690564-6f5a-4983-8e48-31d1143fecdb), object, pointer_default(unique)]
interface ICoreWebView2_10 : ICoreWebView2_9 {
  /// Add an event handler for the BasicAuthenticationRequested event.
  /// BasicAuthenticationRequested event is raised when WebView encounters a
  /// Basic HTTP Authentication request as described in
  /// https://developer.mozilla.org/docs/Web/HTTP/Authentication, an NTLM
  /// authentication or a Proxy Authentication request.
  ///
  /// The host can provide a response with credentials for the authentication or
  /// cancel the request. If the host doesn't set the Cancel property to true or
  /// set either UserName or Password properties on the Response property, then
  /// WebView2 will show the default authentication challenge dialog prompt to
  /// the user.
  ///
  /// \snippet ScenarioAuthentication.cpp BasicAuthenticationRequested
  HRESULT add_BasicAuthenticationRequested(
      [in] ICoreWebView2BasicAuthenticationRequestedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with add_BasicAuthenticationRequested.
  HRESULT remove_BasicAuthenticationRequested(
      [in] EventRegistrationToken token);
}

/// This interface is an extension of `ICoreWebView2_10` that supports sessionId
/// for CDP method calls and ContextMenuRequested event.
[uuid(0be78e56-c193-4051-b943-23b460c08bdb), object, pointer_default(unique)]
interface ICoreWebView2_11 : ICoreWebView2_10 {
  /// Runs an asynchronous `DevToolsProtocol` method for a specific session of
  /// an attached target.
  /// There could be multiple `DevToolsProtocol` targets in a WebView.
  /// Besides the top level page, iframes from different origin and web workers
  /// are also separate targets. Attaching to these targets allows interaction with them.
  /// When the DevToolsProtocol is attached to a target, the connection is identified
  /// by a sessionId.
  /// To use this API, you must set the `flatten` parameter to true when calling
  /// `Target.attachToTarget` or `Target.setAutoAttach` `DevToolsProtocol` method.
  /// Using `Target.setAutoAttach` is recommended as that would allow you to attach
  /// to dedicated worker targets, which are not discoverable via other APIs like
  /// `Target.getTargets`.
  /// For more information about targets and sessions, navigate to
  /// [DevTools Protocol Viewer](https://chromedevtools.github.io/devtools-protocol/tot/Target).
  /// For more information about available methods, navigate to
  /// [DevTools Protocol Viewer](https://chromedevtools.github.io/devtools-protocol/tot)
  /// The `sessionId` parameter is the sessionId for an attached target.
  /// nullptr or empty string is treated as the session for the default target for the top page.
  /// The `methodName` parameter is the full name of the method in the
  /// `{domain}.{method}` format.  The `parametersAsJson` parameter is a JSON
  /// formatted string containing the parameters for the corresponding method.
  /// The `Invoke` method of the `handler` is run when the method
  /// asynchronously completes.  `Invoke` is run with the return object of the
  /// method as a JSON string.  This function returns E_INVALIDARG if the `methodName` is
  /// unknown or the `parametersAsJson` has an error.  In the case of such an error, the
  /// `returnObjectAsJson` parameter of the handler will include information
  /// about the error.
  ///
  /// \snippet ScriptComponent.cpp DevToolsProtocolMethodMultiSession
  ///
  /// \snippet ScriptComponent.cpp CallDevToolsProtocolMethodForSession
  HRESULT CallDevToolsProtocolMethodForSession(
      [in] LPCWSTR sessionId,
      [in] LPCWSTR methodName,
      [in] LPCWSTR parametersAsJson,
      [in] ICoreWebView2CallDevToolsProtocolMethodCompletedHandler* handler);

  /// Add an event handler for the `ContextMenuRequested` event.
  /// `ContextMenuRequested` event is raised when a context menu is requested by the user
  /// and the content inside WebView hasn't disabled context menus.
  /// The host has the option to create their own context menu with the information provided in
  /// the event or can add items to or remove items from WebView context menu.
  /// If the host doesn't handle the event, WebView will display the default context menu.
  ///
  /// \snippet SettingsComponent.cpp EnableCustomMenu
  HRESULT add_ContextMenuRequested(
      [in] ICoreWebView2ContextMenuRequestedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_ContextMenuRequested`.
  HRESULT remove_ContextMenuRequested(
      [in] EventRegistrationToken token);
}

/// This interface is an extension of `ICoreWebView2_11` that supports
/// StatusBarTextChanged event.
[uuid(35D69927-BCFA-4566-9349-6B3E0D154CAC), object, pointer_default(unique)]
interface ICoreWebView2_12 : ICoreWebView2_11 {
  /// Add an event handler for the `StatusBarTextChanged` event.
  /// `StatusBarTextChanged` fires when the WebView is showing a status message,
  /// a URL, or an empty string (an indication to hide the status bar).
  /// \snippet SettingsComponent.cpp StatusBarTextChanged
  HRESULT add_StatusBarTextChanged(
      [in] ICoreWebView2StatusBarTextChangedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_StatusBarTextChanged`.
  HRESULT remove_StatusBarTextChanged([in] EventRegistrationToken token);

  /// The status message text.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT StatusBarText([out, retval] LPWSTR* value);
}

/// This interface is an extension of `ICoreWebView2_12` that supports Profile
/// API.
[uuid(F75F09A8-667E-4983-88D6-C8773F315E84), object, pointer_default(unique)]
interface ICoreWebView2_13 : ICoreWebView2_12 {
  /// The associated `ICoreWebView2Profile` object. If this CoreWebView2 was created with a
  /// CoreWebView2ControllerOptions, the CoreWebView2Profile will match those specified options.
  /// Otherwise if this CoreWebView2 was created without a CoreWebView2ControllerOptions, then
  /// this will be the default CoreWebView2Profile for the corresponding CoreWebView2Environment.
  ///
  /// \snippet AppWindow.cpp CoreWebView2Profile
  [propget] HRESULT Profile([out, retval] ICoreWebView2Profile** value);
}

/// This interface is an extension of `ICoreWebView2_13` that adds
/// ServerCertificate support.
[uuid(6DAA4F10-4A90-4753-8898-77C5DF534165), object, pointer_default(unique)]
interface ICoreWebView2_14 : ICoreWebView2_13 {
  /// Add an event handler for the ServerCertificateErrorDetected event.
  /// The ServerCertificateErrorDetected event is raised when the WebView2
  /// cannot verify server's digital certificate while loading a web page.
  ///
  /// This event will raise for all web resources and follows the `WebResourceRequested` event.
  ///
  /// If you don't handle the event, WebView2 will show the default TLS interstitial error page to the user
  /// for navigations, and for non-navigations the web request is cancelled.
  ///
  /// WebView2 caches the response when action is `COREWEBVIEW2_SERVER_CERTIFICATE_ERROR_ACTION_ALWAYS_ALLOW`
  /// for the RequestUri's host and the server certificate in the session and the `ServerCertificateErrorDetected`
  /// event won't be raised again.
  ///
  /// To raise the event again you must clear the cache using `ClearServerCertificateErrorActions`.
  ///
  /// \snippet SettingsComponent.cpp ServerCertificateErrorDetected1
  HRESULT add_ServerCertificateErrorDetected(
      [in] ICoreWebView2ServerCertificateErrorDetectedEventHandler*
          eventHandler,
      [out] EventRegistrationToken* token);
  /// Remove an event handler previously added with add_ServerCertificateErrorDetected.
  HRESULT remove_ServerCertificateErrorDetected([in] EventRegistrationToken token);

  /// Clears all cached decisions to proceed with TLS certificate errors from the
  /// ServerCertificateErrorDetected event for all WebView2's sharing the same session.
  HRESULT ClearServerCertificateErrorActions(
      [in] ICoreWebView2ClearServerCertificateErrorActionsCompletedHandler*
      handler);
}

/// Receives `StatusBarTextChanged` events.
[uuid(A5E3B0D0-10DF-4156-BFAD-3B43867ACAC6), object, pointer_default(unique)]
interface ICoreWebView2StatusBarTextChangedEventHandler : IUnknown {
  /// Called to provide the implementer with the event args for the
  /// corresponding event. No event args exist and the `args`
  /// parameter is set to `null`.
  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] IUnknown* args);
}

/// This interface is an extension of `ICoreWebView2_14` that supports status Favicons.
[uuid(517B2D1D-7DAE-4A66-A4F4-10352FFB9518), object, pointer_default(unique)]
interface ICoreWebView2_15 : ICoreWebView2_14 {
  /// Add an event handler for the `FaviconChanged` event.
  /// The `FaviconChanged` event is raised when the
  /// [favicon](https://developer.mozilla.org/docs/Glossary/Favicon)
  /// had a different URL then the previous URL.
  /// The FaviconChanged event will be raised for first navigating to a new
  /// document, whether or not a document declares a Favicon in HTML if the
  /// favicon is different from the previous fav icon. The event will
  /// be raised again if a favicon is declared in its HTML or has script
  /// to set its favicon. The favicon information can then be retrieved with
  /// `GetFavicon` and `FaviconUri`.
  HRESULT add_FaviconChanged(
      [in] ICoreWebView2FaviconChangedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove the event handler for `FaviconChanged` event.
  HRESULT remove_FaviconChanged(
      [in] EventRegistrationToken token);

  /// Get the current Uri of the favicon as a string.
  /// If the value is null, then the return value is `E_POINTER`, otherwise it is `S_OK`.
  /// If a page has no favicon then the value is an empty string.
  [propget] HRESULT FaviconUri([out, retval] LPWSTR* value);

  /// Async function for getting the actual image data of the favicon.
  /// The image is copied to the `imageStream` object in `ICoreWebView2GetFaviconCompletedHandler`.
  /// If there is no image then no data would be copied into the imageStream.
  /// The `format` is the file format to return the image stream.
  /// `completedHandler` is executed at the end of the operation.
  ///
  /// \snippet SettingsComponent.cpp FaviconChanged
  HRESULT GetFavicon(
        [in] COREWEBVIEW2_FAVICON_IMAGE_FORMAT format,
        [in] ICoreWebView2GetFaviconCompletedHandler* completedHandler);
}

/// The caller implements this interface to handle the BasicAuthenticationRequested event.
[uuid(58b4d6c2-18d4-497e-b39b-9a96533fa278), object, pointer_default(unique)]
interface ICoreWebView2BasicAuthenticationRequestedEventHandler : IUnknown {
  /// Called to provide the implementer with the event args for the
  /// corresponding event.  
  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] ICoreWebView2BasicAuthenticationRequestedEventArgs* args);
}

/// Implements the interface to receive `IsDefaultDownloadDialogOpenChanged`
/// events.
[uuid(3117da26-ae13-438d-bd46-edbeb2c4ce81), object, pointer_default(unique)]
interface ICoreWebView2IsDefaultDownloadDialogOpenChangedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event. No event args exist
  /// and the `args` parameter is set to `null`.
  HRESULT Invoke([in] ICoreWebView2* sender,
                 [in] IUnknown* args);
}

/// Receives the result of the `PrintToPdf` method. If the print to PDF
/// operation succeeds, `isSuccessful` is true. Otherwise, if the operation
/// failed, `isSuccessful` is set to false. An invalid path returns
/// `E_INVALIDARG`.
[uuid(ccf1ef04-fd8e-4d5f-b2de-0983e41b8c36), object, pointer_default(unique)]
interface ICoreWebView2PrintToPdfCompletedHandler : IUnknown {

  /// Provides the result of the corresponding asynchronous method.
  HRESULT Invoke([in] HRESULT errorCode, BOOL isSuccessful);
}

/// Settings used by the `PrintToPdf` method. Other programmatic printing is not
/// currently supported.
[uuid(377f3721-c74e-48ca-8db1-df68e51d60e2), object, pointer_default(unique)]
interface ICoreWebView2PrintSettings : IUnknown {

  /// The orientation can be portrait or landscape. The default orientation is
  /// portrait. See `COREWEBVIEW2_PRINT_ORIENTATION`.
  [propget] HRESULT Orientation(
    [out, retval] COREWEBVIEW2_PRINT_ORIENTATION* orientation);

  /// Sets the `Orientation` property.
  [propput] HRESULT Orientation(
      [in] COREWEBVIEW2_PRINT_ORIENTATION orientation);

  /// The scale factor is a value between 0.1 and 2.0. The default is 1.0.
  [propget] HRESULT ScaleFactor([out, retval] double* scaleFactor);

  /// Sets the `ScaleFactor` property. Returns `E_INVALIDARG` if an invalid
  /// value is provided, and the current value is not changed.
  [propput] HRESULT ScaleFactor([in] double scaleFactor);

  /// The page width in inches. The default width is 8.5 inches.
  [propget] HRESULT PageWidth([out, retval] double* pageWidth);

  /// Sets the `PageWidth` property. Returns `E_INVALIDARG` if the page width is
  /// less than or equal to zero, and the current value is not changed.
  [propput] HRESULT PageWidth([in] double pageWidth);

  /// The page height in inches. The default height is 11 inches.
  [propget] HRESULT PageHeight([out, retval] double* pageHeight);

  /// Sets the `PageHeight` property. Returns `E_INVALIDARG` if the page height
  /// is less than or equal to zero, and the current value is not changed.
  [propput] HRESULT PageHeight([in] double pageHeight);

  /// The top margin in inches. The default is 1 cm, or ~0.4 inches.
  [propget] HRESULT MarginTop([out, retval] double* marginTop);

  /// Sets the `MarginTop` property. A margin cannot be less than zero.
  /// Returns `E_INVALIDARG` if an invalid value is provided, and the current
  /// value is not changed.
  [propput] HRESULT MarginTop([in] double marginTop);

  /// The bottom margin in inches. The default is 1 cm, or ~0.4 inches.
  [propget] HRESULT MarginBottom([out, retval] double* marginBottom);

  /// Sets the `MarginBottom` property. A margin cannot be less than zero.
  /// Returns `E_INVALIDARG` if an invalid value is provided, and the current
  /// value is not changed.
  [propput] HRESULT MarginBottom([in] double marginBottom);

  /// The left margin in inches. The default is 1 cm, or ~0.4 inches.
  [propget] HRESULT MarginLeft([out, retval] double* marginLeft);

  /// Sets the `MarginLeft` property. A margin cannot be less than zero.
  /// Returns `E_INVALIDARG` if an invalid value is provided, and the current
  /// value is not changed.
  [propput] HRESULT MarginLeft([in] double marginLeft);

  /// The right margin in inches. The default is 1 cm, or ~0.4 inches.
  [propget] HRESULT MarginRight([out, retval] double* marginRight);

  /// Set the `MarginRight` property.A margin cannot be less than zero.
  /// Returns `E_INVALIDARG` if an invalid value is provided, and the current
  /// value is not changed.
  [propput] HRESULT MarginRight([in] double marginRight);

  /// `TRUE` if background colors and images should be printed. The default value
  /// is `FALSE`.
  [propget] HRESULT ShouldPrintBackgrounds(
      [out, retval] BOOL* shouldPrintBackgrounds);

  /// Set the `ShouldPrintBackgrounds` property.
  [propput] HRESULT ShouldPrintBackgrounds([in] BOOL shouldPrintBackgrounds);

  /// `TRUE` if only the current end user's selection of HTML in the document
  /// should be printed. The default value is `FALSE`.
  [propget] HRESULT ShouldPrintSelectionOnly(
      [out, retval] BOOL* shouldPrintSelectionOnly);

  /// Set the `ShouldPrintSelectionOnly` property.
  [propput] HRESULT ShouldPrintSelectionOnly(
      [in] BOOL shouldPrintSelectionOnly);

  /// `TRUE` if header and footer should be printed. The default value is `FALSE`.
  /// The header consists of the date and time of printing, and the title of the
  /// page. The footer consists of the URI and page number. The height of the
  /// header and footer is 0.5 cm, or ~0.2 inches.
  [propget] HRESULT ShouldPrintHeaderAndFooter(
      [out, retval] BOOL* shouldPrintHeaderAndFooter);

  /// Set the `ShouldPrintHeaderAndFooter` property.
  [propput] HRESULT ShouldPrintHeaderAndFooter(
      [in] BOOL shouldPrintHeaderAndFooter);

  /// The title in the header if `ShouldPrintHeaderAndFooter` is `TRUE`. The
  /// default value is the title of the current document.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT HeaderTitle([out, retval] LPWSTR* headerTitle);

  /// Set the `HeaderTitle` property. If an empty string or null value is
  /// provided, no title is shown in the header.
  [propput] HRESULT HeaderTitle([in] LPCWSTR headerTitle);

  /// The URI in the footer if `ShouldPrintHeaderAndFooter` is `TRUE`. The
  /// default value is the current URI.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT FooterUri([out, retval] LPWSTR* footerUri);

  /// Set the `FooterUri` property. If an empty string or null value is
  /// provided, no URI is shown in the footer.
  [propput] HRESULT FooterUri([in] LPCWSTR footerUri);
}

/// The caller implements this interface to receive the TrySuspend result.
[uuid(00F206A7-9D17-4605-91F6-4E8E4DE192E3), object, pointer_default(unique)]
interface ICoreWebView2TrySuspendCompletedHandler : IUnknown {

  /// Provides the result of the TrySuspend operation.
  /// See [Sleeping Tabs FAQ](https://techcommunity.microsoft.com/t5/articles/sleeping-tabs-faq/m-p/1705434)
  /// for conditions that might prevent WebView from being suspended. In those situations,
  /// isSuccessful will be false and errorCode is S_OK.
  HRESULT Invoke([in] HRESULT errorCode, [in] BOOL isSuccessful);

}

/// The owner of the `CoreWebView2` object that provides support for resizing,
/// showing and hiding, focusing, and other functionality related to
/// windowing and composition.  The `CoreWebView2Controller` owns the
/// `CoreWebView2`, and if all references to the `CoreWebView2Controller` go
/// away, the WebView is closed.
[uuid(4d00c0d1-9434-4eb6-8078-8697a560334f), object, pointer_default(unique)]
interface ICoreWebView2Controller : IUnknown {

  /// The `IsVisible` property determines whether to show or hide the WebView2.
  ///   If `IsVisible` is set to `FALSE`, the WebView2 is transparent and is
  /// not rendered.   However, this does not affect the window containing the
  /// WebView2 (the `HWND` parameter that was passed to
  /// `CreateCoreWebView2Controller`).  If you want that window to disappear
  /// too, run `ShowWindow` on it directly in addition to modifying the
  /// `IsVisible` property.  WebView2 as a child window does not get window
  /// messages when the top window is minimized or restored.  For performance
  /// reasons, developers should set the `IsVisible` property of the WebView to
  /// `FALSE` when the app window is minimized and back to `TRUE` when the app
  /// window is restored. The app window does this by handling
  /// `SIZE_MINIMIZED and SIZE_RESTORED` command upon receiving `WM_SIZE`
  /// message.
  ///
  /// There are CPU and memory benefits when the page is hidden. For instance,
  /// Chromium has code that throttles activities on the page like animations
  /// and some tasks are run less frequently. Similarly, WebView2 will
  /// purge some caches to reduce memory usage.
  ///
  /// \snippet ViewComponent.cpp ToggleIsVisible
  [propget] HRESULT IsVisible([out, retval] BOOL* isVisible);

  /// Sets the `IsVisible` property.
  ///
  /// \snippet ViewComponent.cpp ToggleIsVisibleOnMinimize
  [propput] HRESULT IsVisible([in] BOOL isVisible);

  /// The WebView bounds. Bounds are relative to the parent `HWND`.  The app
  /// has two ways to position a WebView.
  ///
  /// *   Create a child `HWND` that is the WebView parent `HWND`.  Position
  ///     the window where the WebView should be.  Use `(0, 0)` for the
  ///     top-left corner (the offset) of the `Bounds` of the WebView.
  /// *   Use the top-most window of the app as the WebView parent HWND.  For
  ///     example, to position WebView correctly in the app, set the top-left
  ///     corner of the Bound of the WebView.
  ///
  /// The values of `Bounds` are limited by the coordinate space of the host.

  [propget] HRESULT Bounds([out, retval] RECT* bounds);

  /// Sets the `Bounds` property.
  ///
  /// \snippet ViewComponent.cpp ResizeWebView

  [propput] HRESULT Bounds([in] RECT bounds);

  /// The zoom factor for the WebView.
  ///
  /// \> [!NOTE]\n\> Changing zoom factor may cause `window.innerWidth`,
  /// `window.innerHeight`, both, and page layout to change.  A zoom factor
  /// that is applied by the host by running `ZoomFactor` becomes the new
  /// default zoom for the WebView.  The zoom factor applies across navigations
  /// and is the zoom factor WebView is returned to when the user chooses
  /// Ctrl+0.  When the zoom factor is changed by the user (resulting in
  /// the app receiving `ZoomFactorChanged`), that zoom applies only for the
  /// current page.  Any user applied zoom is only for the current page and is
  /// reset on a navigation.  Specifying a `zoomFactor` less than or equal to
  /// `0` is not allowed.  WebView also has an internal supported zoom factor
  /// range.  When a specified zoom factor is out of that range, it is
  /// normalized to be within the range, and a `ZoomFactorChanged` event is
  /// triggered for the real applied zoom factor.  When the range normalization
  /// happens, the `ZoomFactor` property reports the zoom factor specified
  /// during the previous modification of the `ZoomFactor` property until the
  /// `ZoomFactorChanged` event is received after WebView applies the
  /// normalized zoom factor.

  [propget] HRESULT ZoomFactor([out, retval] double* zoomFactor);

  /// Sets the `ZoomFactor` property.

  [propput] HRESULT ZoomFactor([in] double zoomFactor);

  /// Adds an event handler for the `ZoomFactorChanged` event.
  /// `ZoomFactorChanged` runs when the `ZoomFactor` property of the WebView
  /// changes.  The event may run because the `ZoomFactor` property was
  /// modified, or due to the user manually modifying the zoom.  When it is
  /// modified using the `ZoomFactor` property, the internal zoom factor is
  /// updated immediately and no `ZoomFactorChanged` event is triggered.
  /// WebView associates the last used zoom factor for each site.  It is
  /// possible for the zoom factor to change when navigating to a different
  /// page.  When the zoom factor changes due to a navigation change, the
  /// `ZoomFactorChanged` event runs right after the `ContentLoading` event.
  ///
  /// \snippet ViewComponent.cpp ZoomFactorChanged

  HRESULT add_ZoomFactorChanged(
      [in] ICoreWebView2ZoomFactorChangedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_ZoomFactorChanged`.

  HRESULT remove_ZoomFactorChanged(
      [in] EventRegistrationToken token);

  /// Updates `Bounds` and `ZoomFactor` properties at the same time.  This
  /// operation is atomic from the perspective of the host.  After returning
  /// from this function, the `Bounds` and `ZoomFactor` properties are both
  /// updated if the function is successful, or neither is updated if the
  /// function fails.  If `Bounds` and `ZoomFactor` are both updated by the
  /// same scale (for example, `Bounds` and `ZoomFactor` are both doubled),
  /// then the page does not display a change in `window.innerWidth` or
  /// `window.innerHeight` and the WebView renders the content at the new size
  /// and zoom without intermediate renderings.  This function also updates
  /// just one of `ZoomFactor` or `Bounds` by passing in the new value for one
  /// and the current value for the other.
  ///
  /// \snippet ViewComponent.cpp SetBoundsAndZoomFactor

  HRESULT SetBoundsAndZoomFactor([in] RECT bounds, [in] double zoomFactor);

  /// Moves focus into WebView.  WebView gets focus and focus is set to
  /// correspondent element in the page hosted in the WebView.  For
  /// Programmatic reason, focus is set to previously focused element or the
  /// default element if no previously focused element exists.  For `Next`
  /// reason, focus is set to the first element.  For `Previous` reason, focus
  /// is set to the last element.  WebView changes focus through user
  /// interaction including selecting into a WebView or Tab into it.  For
  /// tabbing, the app runs MoveFocus with Next or Previous to align with Tab
  /// and Shift+Tab respectively when it decides the WebView is the next
  /// element that may exist in a tab.  Or, the app runs `IsDialogMessage`
  /// as part of the associated message loop to allow the platform to auto
  /// handle tabbing.  The platform rotates through all windows with
  /// `WS_TABSTOP`.  When the WebView gets focus from `IsDialogMessage`, it is
  /// internally put the focus on the first or last element for tab and
  /// Shift+Tab respectively.
  ///
  /// \snippet App.cpp MoveFocus0
  ///
  /// \snippet ControlComponent.cpp MoveFocus1
  ///
  /// \snippet ControlComponent.cpp MoveFocus2

  HRESULT MoveFocus([in] COREWEBVIEW2_MOVE_FOCUS_REASON reason);

  /// Adds an event handler for the `MoveFocusRequested` event.
  /// `MoveFocusRequested` runs when user tries to tab out of the WebView.  The
  /// focus of the WebView has not changed when this event is run.
  ///
  /// \snippet ControlComponent.cpp MoveFocusRequested

  HRESULT add_MoveFocusRequested(
      [in] ICoreWebView2MoveFocusRequestedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Removes an event handler previously added with `add_MoveFocusRequested`.

  HRESULT remove_MoveFocusRequested(
      [in] EventRegistrationToken token);

  /// Adds an event handler for the `GotFocus` event.  `GotFocus` runs when
  /// WebView has focus.

  HRESULT add_GotFocus(
      [in] ICoreWebView2FocusChangedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Removes an event handler previously added with `add_GotFocus`.

  HRESULT remove_GotFocus(
      [in] EventRegistrationToken token);

  /// Adds an event handler for the `LostFocus` event.  `LostFocus` runs when
  /// WebView loses focus.  In the case where `MoveFocusRequested` event is
  /// run, the focus is still on WebView when `MoveFocusRequested` event runs.
  /// `LostFocus` only runs afterwards when code of the app or default action
  /// of `MoveFocusRequested` event set focus away from WebView.

  HRESULT add_LostFocus(
      [in] ICoreWebView2FocusChangedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Removes an event handler previously added with `add_LostFocus`.

  HRESULT remove_LostFocus(
      [in] EventRegistrationToken token);

  /// Adds an event handler for the `AcceleratorKeyPressed` event.
  /// `AcceleratorKeyPressed` runs when an accelerator key or key combo is
  /// pressed or released while the WebView is focused.  A key is considered an
  ///  accelerator if either of the following conditions are true.
  ///
  /// *   Ctrl or Alt is currently being held.
  /// *   The pressed key does not map to a character.
  ///
  /// A few specific keys are never considered accelerators, such as Shift.
  /// The `Escape` key is always considered an accelerator.
  ///
  /// Auto-repeated key events caused by holding the key down also triggers
  /// this event.  Filter out the auto-repeated key events by verifying the
  /// `KeyEventLParam` or `PhysicalKeyStatus` event args.
  ///
  /// In windowed mode, the event handler is run synchronously.  Until you
  /// run `Handled()` on the event args or the event handler returns, the
  /// browser process is blocked and outgoing cross-process COM requests fail
  /// with `RPC_E_CANTCALLOUT_ININPUTSYNCCALL`.  All `CoreWebView2` API methods
  /// work, however.
  ///
  /// In windowless mode, the event handler is run asynchronously.  Further
  /// input do not reach the browser until the event handler returns or
  /// `Handled()` is run, but the browser process is not blocked, and outgoing
  /// COM requests work normally.
  ///
  /// It is recommended to run `Handled(TRUE)` as early as are able to know
  /// that you want to handle the accelerator key.
  ///
  /// \snippet ControlComponent.cpp AcceleratorKeyPressed

  HRESULT add_AcceleratorKeyPressed(
    [in] ICoreWebView2AcceleratorKeyPressedEventHandler* eventHandler,
    [out] EventRegistrationToken* token);

  /// Removes an event handler previously added with
  /// `add_AcceleratorKeyPressed`.

  HRESULT remove_AcceleratorKeyPressed(
    [in] EventRegistrationToken token);

  /// The parent window provided by the app that this WebView is using to
  /// render content.  This API initially returns the window passed into
  /// `CreateCoreWebView2Controller`.

  [propget] HRESULT ParentWindow([out, retval] HWND* parentWindow);

  /// Sets the parent window for the WebView.  This causes the WebView to
  /// re-parent the main WebView window to the newly provided window.

  [propput] HRESULT ParentWindow([in] HWND parentWindow);

  /// This is a notification separate from `Bounds` that tells WebView that the
  ///  main WebView parent (or any ancestor) `HWND` moved.  This is needed
  /// for accessibility and certain dialogs in WebView to work correctly.
  ///
  /// \snippet ViewComponent.cpp NotifyParentWindowPositionChanged

  HRESULT NotifyParentWindowPositionChanged();

  /// Closes the WebView and cleans up the underlying browser instance.
  /// Cleaning up the browser instance releases the resources powering the
  /// WebView.  The browser instance is shut down if no other WebViews are
  /// using it.
  ///
  /// After running `Close`, most methods will fail and event handlers stop
  /// running.  Specifically, the WebView releases the associated references to
  /// any associated event handlers when `Close` is run.
  ///
  /// `Close` is implicitly run when the `CoreWebView2Controller` loses the
  /// final reference and is destructed.  But it is best practice to
  /// explicitly run `Close` to avoid any accidental cycle of references
  /// between the WebView and the app code.  Specifically, if you capture a
  /// reference to the WebView in an event handler you create a reference cycle
  /// between the WebView and the event handler.  Run `Close` to break the
  /// cycle by releasing all event handlers.  But to avoid the situation, it is
  /// best to both explicitly run `Close` on the WebView and to not capture a
  /// reference to the WebView to ensure the WebView is cleaned up correctly.
  /// `Close` is synchronous and won't trigger the `beforeunload` event.
  ///
  /// \snippet AppWindow.cpp Close
  HRESULT Close();

  /// Gets the `CoreWebView2` associated with this `CoreWebView2Controller`.

  [propget] HRESULT CoreWebView2([out, retval] ICoreWebView2** coreWebView2);
}

/// A continuation of the ICoreWebView2Controller interface.
[uuid(c979903e-d4ca-4228-92eb-47ee3fa96eab), object, pointer_default(unique)]
interface ICoreWebView2Controller2 : ICoreWebView2Controller {
  /// The `DefaultBackgroundColor` property is the color WebView renders
  /// underneath all web content. This means WebView renders this color when
  /// there is no web content loaded such as before the initial navigation or
  /// between navigations. This also means web pages with undefined css
  /// background properties or background properties containing transparent
  /// pixels will render their contents over this color. Web pages with defined
  /// and opaque background properties that span the page will obscure the
  /// `DefaultBackgroundColor` and display normally. The default value for this
  /// property is white to resemble the native browser experience.
  ///
  /// The Color is specified by the COREWEBVIEW2_COLOR that represents an RGBA
  /// value. The `A` represents an Alpha value, meaning
  /// `DefaultBackgroundColor` can be transparent. In the case of a transparent
  /// `DefaultBackgroundColor` WebView will render hosting app content as the
  /// background. This Alpha value is not supported on Windows 7. Any `A` value
  /// other than 255 will result in E_INVALIDARG on Windows 7.
  /// It is supported on all other WebView compatible platforms.
  ///
  /// Semi-transparent colors are not currently supported by this API and
  /// setting `DefaultBackgroundColor` to a semi-transparent color will fail
  /// with E_INVALIDARG. The only supported alpha values are 0 and 255, all
  /// other values will result in E_INVALIDARG.
  /// `DefaultBackgroundColor` can only be an opaque color or transparent.
  ///
  /// \snippet ViewComponent.cpp DefaultBackgroundColor
  [propget] HRESULT DefaultBackgroundColor(
    [out, retval] COREWEBVIEW2_COLOR* backgroundColor);

  /// Sets the `DefaultBackgroundColor` property.
  [propput] HRESULT DefaultBackgroundColor(
    [in] COREWEBVIEW2_COLOR backgroundColor);
}

/// A continuation of the ICoreWebView2Controller2 interface.
[uuid(f9614724-5d2b-41dc-aef7-73d62b51543b), object, pointer_default(unique)]
interface ICoreWebView2Controller3 : ICoreWebView2Controller2 {
  /// The rasterization scale for the WebView. The rasterization scale is the
  /// combination of the monitor DPI scale and text scaling set by the user.
  /// This value should be updated when the DPI scale of the app's top level
  /// window changes (i.e. monitor DPI scale changes or window changes monitor)
  /// or when the text scale factor of the system changes.
  ///
  /// \snippet AppWindow.cpp DPIChanged
  ///
  /// \snippet AppWindow.cpp TextScaleChanged1
  ///
  /// \snippet AppWindow.cpp TextScaleChanged2
  ///
  /// Rasterization scale applies to the WebView content, as well as
  /// popups, context menus, scroll bars, and so on. Normal app scaling
  /// scenarios should use the ZoomFactor property or SetBoundsAndZoomFactor
  /// API which only scale the rendered HTML content and not popups, context
  /// menus, scroll bars, and so on.
  ///
  /// \snippet ViewComponent.cpp RasterizationScale
  [propget] HRESULT RasterizationScale([out, retval] double* scale);
  /// Set the rasterization scale property.
  [propput] HRESULT RasterizationScale([in] double scale);

  /// ShouldDetectMonitorScaleChanges property determines whether the WebView
  /// attempts to track monitor DPI scale changes. When true, the WebView will
  /// track monitor DPI scale changes, update the RasterizationScale property,
  /// and raises RasterizationScaleChanged event. When false, the WebView will
  /// not track monitor DPI scale changes, and the app must update the
  /// RasterizationScale property itself. RasterizationScaleChanged event will
  /// never raise when ShouldDetectMonitorScaleChanges is false. Apps that want
  /// to set their own rasterization scale should set this property to false to
  /// avoid the WebView2 updating the RasterizationScale property to match the
  /// monitor DPI scale.
  [propget] HRESULT ShouldDetectMonitorScaleChanges([out, retval] BOOL* value);
  /// Set the ShouldDetectMonitorScaleChanges property.
  [propput] HRESULT ShouldDetectMonitorScaleChanges([in] BOOL value);

  /// Add an event handler for the RasterizationScaleChanged event.
  /// The event is raised when the WebView detects that the monitor DPI scale
  /// has changed, ShouldDetectMonitorScaleChanges is true, and the WebView has
  /// changed the RasterizationScale property.
  ///
  /// \snippet ViewComponent.cpp RasterizationScaleChanged
  HRESULT add_RasterizationScaleChanged(
    [in] ICoreWebView2RasterizationScaleChangedEventHandler* eventHandler,
    [out] EventRegistrationToken* token);
  /// Remove an event handler previously added with
  /// add_RasterizationScaleChanged.
  HRESULT remove_RasterizationScaleChanged(
    [in] EventRegistrationToken token);

  /// BoundsMode affects how setting the Bounds and RasterizationScale
  /// properties work. Bounds mode can either be in COREWEBVIEW2_BOUNDS_MODE_USE_RAW_PIXELS
  /// mode or COREWEBVIEW2_BOUNDS_MODE_USE_RASTERIZATION_SCALE mode.
  ///
  /// When the mode is in COREWEBVIEW2_BOUNDS_MODE_USE_RAW_PIXELS, setting the bounds
  /// property will set the size of the WebView in raw screen pixels. Changing
  /// the rasterization scale in this mode won't change the raw pixel size of
  /// the WebView and will only change the rasterization scale.
  ///
  /// When the mode is in COREWEBVIEW2_BOUNDS_MODE_USE_RASTERIZATION_SCALE, setting the
  /// bounds property will change the logical size of the WebView which can be
  /// described by the following equation:
  /// ```text
  /// Logical size * rasterization scale = Raw Pixel size
  /// ```
  /// In this case, changing the rasterization scale will keep the logical size
  /// the same and change the raw pixel size.
  ///
  /// \snippet ViewComponent.cpp BoundsMode
  [propget] HRESULT BoundsMode(
    [out, retval] COREWEBVIEW2_BOUNDS_MODE* boundsMode);
  /// Set the BoundsMode property.
  [propput] HRESULT BoundsMode([in] COREWEBVIEW2_BOUNDS_MODE boundsMode);
}

/// This is the ICoreWebView2Controller4 interface.
/// The ICoreWebView2Controller4 provides interface to enable/disable external drop.
[uuid(97d418d5-a426-4e49-a151-e1a10f327d9e), object, pointer_default(unique)]
interface ICoreWebView2Controller4 : ICoreWebView2Controller3 {
  /// Gets the `AllowExternalDrop` property which is used to configure the
  /// capability that dragging objects from outside the bounds of webview2 and
  /// dropping into webview2 is allowed or disallowed. The default value is
  /// TRUE.
  ///
  /// \snippet SettingsComponent.cpp ToggleAllowExternalDrop
  [propget] HRESULT AllowExternalDrop([ out, retval ] BOOL * value);
  /// Sets the `AllowExternalDrop` property which is used to configure the
  /// capability that dragging objects from outside the bounds of webview2 and
  /// dropping into webview2 is allowed or disallowed.
  ///
  /// \snippet SettingsComponent.cpp ToggleAllowExternalDrop
  [propput] HRESULT AllowExternalDrop([in] BOOL value);
}

/// This interface is an extension of the ICoreWebView2Controller interface to
/// support visual hosting. An object implementing the
/// ICoreWebView2CompositionController interface will also implement
/// ICoreWebView2Controller. Callers are expected to use
/// ICoreWebView2Controller for resizing, visibility, focus, and so on, and
/// then use ICoreWebView2CompositionController to connect to a composition
/// tree and provide input meant for the WebView.
[uuid(3df9b733-b9ae-4a15-86b4-eb9ee9826469), object, pointer_default(unique)]
interface ICoreWebView2CompositionController : IUnknown {
  /// The RootVisualTarget is a visual in the hosting app's visual tree. This
  /// visual is where the WebView will connect its visual tree. The app uses
  /// this visual to position the WebView within the app. The app still needs
  /// to use the Bounds property to size the WebView. The RootVisualTarget
  /// property can be an IDCompositionVisual or a
  /// Windows::UI::Composition::ContainerVisual. WebView will connect its visual
  /// tree to the provided visual before returning from the property setter. The
  /// app needs to commit on its device setting the RootVisualTarget property.
  /// The RootVisualTarget property supports being set to nullptr to disconnect
  /// the WebView from the app's visual tree.
  /// \snippet ViewComponent.cpp SetRootVisualTarget
  /// \snippet ViewComponent.cpp BuildDCompTree
  [propget] HRESULT RootVisualTarget([out, retval] IUnknown** target);
  /// Set the RootVisualTarget property.
  [propput] HRESULT RootVisualTarget([in] IUnknown* target);

  /// If eventKind is COREWEBVIEW2_MOUSE_EVENT_KIND_HORIZONTAL_WHEEL or
  /// COREWEBVIEW2_MOUSE_EVENT_KIND_WHEEL, then mouseData specifies the amount of
  /// wheel movement. A positive value indicates that the wheel was rotated
  /// forward, away from the user; a negative value indicates that the wheel was
  /// rotated backward, toward the user. One wheel click is defined as
  /// WHEEL_DELTA, which is 120.
  /// If eventKind is COREWEBVIEW2_MOUSE_EVENT_KIND_X_BUTTON_DOUBLE_CLICK
  /// COREWEBVIEW2_MOUSE_EVENT_KIND_X_BUTTON_DOWN, or
  /// COREWEBVIEW2_MOUSE_EVENT_KIND_X_BUTTON_UP, then mouseData specifies which X
  /// buttons were pressed or released. This value should be 1 if the first X
  /// button is pressed/released and 2 if the second X button is
  /// pressed/released.
  /// If eventKind is COREWEBVIEW2_MOUSE_EVENT_KIND_LEAVE, then virtualKeys,
  /// mouseData, and point should all be zero.
  /// If eventKind is any other value, then mouseData should be zero.
  /// Point is expected to be in the client coordinate space of the WebView.
  /// To track mouse events that start in the WebView and can potentially move
  /// outside of the WebView and host application, calling SetCapture and
  /// ReleaseCapture is recommended.
  /// To dismiss hover popups, it is also recommended to send
  /// COREWEBVIEW2_MOUSE_EVENT_KIND_LEAVE messages.
  /// \snippet ViewComponent.cpp SendMouseInput
  HRESULT SendMouseInput(
    [in] COREWEBVIEW2_MOUSE_EVENT_KIND eventKind,
    [in] COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS virtualKeys,
    [in] UINT32 mouseData,
    [in] POINT point);

  /// SendPointerInput accepts touch or pen pointer input of types defined in
  /// COREWEBVIEW2_POINTER_EVENT_KIND. Any pointer input from the system must be
  /// converted into an ICoreWebView2PointerInfo first.
  HRESULT SendPointerInput(
    [in] COREWEBVIEW2_POINTER_EVENT_KIND eventKind,
    [in] ICoreWebView2PointerInfo* pointerInfo);

  /// The current cursor that WebView thinks it should be. The cursor should be
  /// set in WM_SETCURSOR through \::SetCursor or set on the corresponding
  /// parent/ancestor HWND of the WebView through \::SetClassLongPtr. The HCURSOR
  /// can be freed so CopyCursor/DestroyCursor is recommended to keep your own
  /// copy if you are doing more than immediately setting the cursor.
  [propget] HRESULT Cursor([out, retval] HCURSOR* cursor);

  /// The current system cursor ID reported by the underlying rendering engine
  /// for WebView. For example, most of the time, when the cursor is over text,
  /// this will return the int value for IDC_IBEAM. The systemCursorId is only
  /// valid if the rendering engine reports a default Windows cursor resource
  /// value. Navigate to
  /// [LoadCursorW](/windows/win32/api/winuser/nf-winuser-loadcursorw) for more
  /// details. Otherwise, if custom CSS cursors are being used, this will return
  /// 0. To actually use systemCursorId in LoadCursor or LoadImage,
  /// MAKEINTRESOURCE must be called on it first.
  ///
  /// \snippet ViewComponent.cpp SystemCursorId
  [propget] HRESULT SystemCursorId([out, retval] UINT32* systemCursorId);

  /// Add an event handler for the CursorChanged event.
  /// The event is raised when WebView thinks the cursor should be changed. For
  /// example, when the mouse cursor is currently the default cursor but is then
  /// moved over text, it may try to change to the IBeam cursor.
  ///
  /// It is expected for the developer to send
  /// COREWEBVIEW2_MOUSE_EVENT_KIND_LEAVE messages (in addition to
  /// COREWEBVIEW2_MOUSE_EVENT_KIND_MOVE messages) through the SendMouseInput
  /// API. This is to ensure that the mouse is actually within the WebView that
  /// sends out CursorChanged events.
  ///
  /// \snippet ViewComponent.cpp CursorChanged
  HRESULT add_CursorChanged(
      [in] ICoreWebView2CursorChangedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);
  /// Remove an event handler previously added with add_CursorChanged.
  HRESULT remove_CursorChanged(
      [in] EventRegistrationToken token);
}

/// A continuation of the ICoreWebView2CompositionController interface.
[uuid(0b6a3d24-49cb-4806-ba20-b5e0734a7b26), object, pointer_default(unique)]
interface ICoreWebView2CompositionController2 : ICoreWebView2CompositionController {
  /// Returns the Automation Provider for the WebView. This object implements
  /// IRawElementProviderSimple.
  [propget] HRESULT AutomationProvider([out, retval] IUnknown** provider);
}

/// This interface is used to complete deferrals on event args that support
/// getting deferrals using the `GetDeferral` method.

[uuid(c10e7f7b-b585-46f0-a623-8befbf3e4ee0), object, pointer_default(unique)]
interface ICoreWebView2Deferral : IUnknown {

  /// Completes the associated deferred event.  Complete should only be run
  /// once for each deferral taken.

  HRESULT Complete();
}

/// Defines properties that enable, disable, or modify WebView features.
/// Changes to `IsGeneralAutofillEnabled` and `IsPasswordAutosaveEnabled`
/// apply immediately, while other setting changes made after `NavigationStarting`
/// event do not apply until the next top-level navigation.

[uuid(e562e4f0-d7fa-43ac-8d71-c05150499f00), object, pointer_default(unique)]
interface ICoreWebView2Settings : IUnknown {

  /// Controls if running JavaScript is enabled in all future navigations in
  /// the WebView.  This only affects scripts in the document.  Scripts
  /// injected with `ExecuteScript` runs even if script is disabled.
  /// The default value is `TRUE`.
  ///
  /// \snippet SettingsComponent.cpp IsScriptEnabled
  [propget] HRESULT IsScriptEnabled(
      [out, retval] BOOL* isScriptEnabled);

  /// Sets the `IsScriptEnabled` property.
  [propput] HRESULT IsScriptEnabled([in] BOOL isScriptEnabled);

  /// The `IsWebMessageEnabled` property is used when loading a new HTML
  /// document.  If set to `TRUE`, communication from the host to the top-level
  ///  HTML document of the WebView is allowed using `PostWebMessageAsJson`,
  /// `PostWebMessageAsString`, and message event of `window.chrome.webview`.
  /// For more information, navigate to PostWebMessageAsJson.  Communication
  /// from the top-level HTML document of the WebView to the host is allowed
  /// using the postMessage function of `window.chrome.webview` and
  /// `add_WebMessageReceived` method.  For more information, navigate to
  /// [add_WebMessageReceived](/microsoft-edge/webview2/reference/win32/icorewebview2#add_webmessagereceived).
  /// If set to false, then communication is disallowed.  `PostWebMessageAsJson`
  /// and `PostWebMessageAsString` fails with `E_ACCESSDENIED` and
  /// `window.chrome.webview.postMessage` fails by throwing an instance of an
  /// `Error` object. The default value is `TRUE`.
  ///
  /// \snippet ScenarioWebMessage.cpp IsWebMessageEnabled
  [propget] HRESULT IsWebMessageEnabled(
      [out, retval] BOOL* isWebMessageEnabled);

  /// Sets the `IsWebMessageEnabled` property.
  [propput] HRESULT IsWebMessageEnabled([in] BOOL isWebMessageEnabled);

  /// `AreDefaultScriptDialogsEnabled` is used when loading a new HTML
  /// document.  If set to `FALSE`, WebView2 does not render the default JavaScript
  /// dialog box (Specifically those displayed by the JavaScript alert,
  /// confirm, prompt functions and `beforeunload` event).  Instead, if an
  /// event handler is set using `add_ScriptDialogOpening`, WebView sends an
  /// event that contains all of the information for the dialog and allow the
  /// host app to show a custom UI.
  /// The default value is `TRUE`.
  [propget] HRESULT AreDefaultScriptDialogsEnabled(
      [out, retval] BOOL* areDefaultScriptDialogsEnabled);

  /// Sets the `AreDefaultScriptDialogsEnabled` property.
  [propput] HRESULT AreDefaultScriptDialogsEnabled(
      [in] BOOL areDefaultScriptDialogsEnabled);

  /// `IsStatusBarEnabled` controls whether the status bar is displayed.  The
  /// status bar is usually displayed in the lower left of the WebView and
  /// shows things such as the URI of a link when the user hovers over it and
  /// other information.
  /// The default value is `TRUE`.
  /// The status bar UI can be altered by web content and should not be considered secure.
  [propget] HRESULT IsStatusBarEnabled([out, retval] BOOL* isStatusBarEnabled);

  /// Sets the `IsStatusBarEnabled` property.
  [propput] HRESULT IsStatusBarEnabled([in] BOOL isStatusBarEnabled);

  /// `AreDevToolsEnabled` controls whether the user is able to use the context
  /// menu or keyboard shortcuts to open the DevTools window.
  /// The default value is `TRUE`.
  [propget] HRESULT AreDevToolsEnabled([out, retval] BOOL* areDevToolsEnabled);

  /// Sets the `AreDevToolsEnabled` property.
  [propput] HRESULT AreDevToolsEnabled([in] BOOL areDevToolsEnabled);

  /// The `AreDefaultContextMenusEnabled` property is used to prevent default
  /// context menus from being shown to user in WebView.
  /// The default value is `TRUE`.
  ///
  /// \snippet SettingsComponent.cpp DisableContextMenu
  [propget] HRESULT AreDefaultContextMenusEnabled([out, retval] BOOL* enabled);

  /// Sets the `AreDefaultContextMenusEnabled` property.
  [propput] HRESULT AreDefaultContextMenusEnabled([in] BOOL enabled);

  /// The `AreHostObjectsAllowed` property is used to control whether host
  /// objects are accessible from the page in WebView.
  /// The default value is `TRUE`.
  ///
  /// \snippet SettingsComponent.cpp HostObjectsAccess
  [propget] HRESULT AreHostObjectsAllowed([out, retval] BOOL* allowed);

  /// Sets the `AreHostObjectsAllowed` property.

  [propput] HRESULT AreHostObjectsAllowed([in] BOOL allowed);

  /// The `IsZoomControlEnabled` property is used to prevent the user from
  /// impacting the zoom of the WebView.  When disabled, the user is not able
  /// to zoom using Ctrl++, Ctrl+-, or Ctrl+mouse wheel, but the zoom
  /// is set using `ZoomFactor` API.  The default value is `TRUE`.
  ///
  /// \snippet SettingsComponent.cpp DisableZoomControl

  [propget] HRESULT IsZoomControlEnabled([out, retval] BOOL* enabled);

  /// Sets the `IsZoomControlEnabled` property.

  [propput] HRESULT IsZoomControlEnabled([in] BOOL enabled);

  /// The `IsBuiltInErrorPageEnabled` property is used to disable built in
  /// error page for navigation failure and render process failure.  When
  /// disabled, a blank page is displayed when the related error happens.
  /// The default value is `TRUE`.
  ///
  /// \snippet SettingsComponent.cpp BuiltInErrorPageEnabled
  [propget] HRESULT IsBuiltInErrorPageEnabled([out, retval] BOOL* enabled);

  /// Sets the `IsBuiltInErrorPageEnabled` property.
  [propput] HRESULT IsBuiltInErrorPageEnabled([in] BOOL enabled);
}

/// A continuation of the ICoreWebView2Settings interface that manages the user agent.

[uuid(ee9a0f68-f46c-4e32-ac23-ef8cac224d2a), object, pointer_default(unique)]
interface ICoreWebView2Settings2 : ICoreWebView2Settings {
  /// Returns the User Agent. The default value is the default User Agent of the
  /// Microsoft Edge browser.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  ///
  /// \snippet SettingsComponent.cpp UserAgent
  [propget] HRESULT UserAgent([out, retval] LPWSTR* userAgent);
  /// Sets the `UserAgent` property. This property may be overridden if
  /// the User-Agent header is set in a request. If the parameter is empty
  /// the User Agent will not be updated and the current User Agent will remain.
  /// Returns `HRESULT_FROM_WIN32(ERROR_INVALID_STATE)` if the owning WebView is
  /// closed.
  [propput] HRESULT UserAgent([in] LPCWSTR userAgent);
}

/// A continuation of the ICoreWebView2Settings interface that manages whether
/// browser accelerator keys are enabled.
[uuid(fdb5ab74-af33-4854-84f0-0a631deb5eba), object, pointer_default(unique)]
interface ICoreWebView2Settings3 : ICoreWebView2Settings2 {
  /// When this setting is set to FALSE, it disables all accelerator keys that
  /// access features specific to a web browser, including but not limited to:
  ///  - Ctrl-F and F3 for Find on Page
  ///  - Ctrl-P for Print
  ///  - Ctrl-R and F5 for Reload
  ///  - Ctrl-Plus and Ctrl-Minus for zooming
  ///  - Ctrl-Shift-C and F12 for DevTools
  ///  - Special keys for browser functions, such as Back, Forward, and Search
  ///
  /// It does not disable accelerator keys related to movement and text editing,
  /// such as:
  ///  - Home, End, Page Up, and Page Down
  ///  - Ctrl-X, Ctrl-C, Ctrl-V
  ///  - Ctrl-A for Select All
  ///  - Ctrl-Z for Undo
  ///
  /// Those accelerator keys will always be enabled unless they are handled in
  /// the `AcceleratorKeyPressed` event.
  ///
  /// This setting has no effect on the `AcceleratorKeyPressed` event.  The event
  /// will be fired for all accelerator keys, whether they are enabled or not.
  ///
  /// The default value for `AreBrowserAcceleratorKeysEnabled` is TRUE.
  ///
  /// \snippet SettingsComponent.cpp AreBrowserAcceleratorKeysEnabled
  [propget] HRESULT AreBrowserAcceleratorKeysEnabled(
      [out, retval] BOOL* areBrowserAcceleratorKeysEnabled);

  /// Sets the `AreBrowserAcceleratorKeysEnabled` property.
  [propput] HRESULT AreBrowserAcceleratorKeysEnabled(
      [in] BOOL areBrowserAcceleratorKeysEnabled);
}

/// A continuation of the ICoreWebView2Settings interface to manage autofill.
[uuid(cb56846c-4168-4d53-b04f-03b6d6796ff2), object, pointer_default(unique)]
interface ICoreWebView2Settings4 : ICoreWebView2Settings3 {
  /// IsPasswordAutosaveEnabled controls whether autosave for password
  /// information is enabled. The IsPasswordAutosaveEnabled property behaves
  /// independently of the IsGeneralAutofillEnabled property. When IsPasswordAutosaveEnabled is
  /// false, no new password data is saved and no Save/Update Password prompts are displayed.
  /// However, if there was password data already saved before disabling this setting,
  /// then that password information is auto-populated, suggestions are shown and clicking on
  /// one will populate the fields.
  /// When IsPasswordAutosaveEnabled is true, password information is auto-populated,
  /// suggestions are shown and clicking on one will populate the fields, new data
  /// is saved, and a Save/Update Password prompt is displayed.
  /// It will apply immediately after setting.
  /// The default value is `FALSE`.
  /// This property is linked with `CoreWebView2Profile.IsPasswordAutosaveEnabled`, so
  /// changing one will change the other. And all WebView2s that created with the same
  /// `CoreWebView2Profile` will share this property, so for the WebView2s with the same
  /// profile, their `CoreWebView2Settings.IsPasswordAutosaveEnabled` and
  /// `CoreWebView2Profile.IsPasswordAutosaveEnabled` are always in sync.
  ///
  /// \snippet SettingsComponent.cpp PasswordAutosaveEnabled
  [propget] HRESULT IsPasswordAutosaveEnabled([out, retval] BOOL* value);

  /// Set the IsPasswordAutosaveEnabled property.
  [propput] HRESULT IsPasswordAutosaveEnabled([in] BOOL value);

  /// IsGeneralAutofillEnabled controls whether autofill for information
  /// like names, street and email addresses, phone numbers, and arbitrary input
  /// is enabled. This excludes password and credit card information. When
  /// IsGeneralAutofillEnabled is false, no suggestions appear, and no new information
  /// is saved. When IsGeneralAutofillEnabled is true, information is saved, suggestions
  /// appear and clicking on one will populate the form fields.
  /// It will apply immediately after setting.
  /// The default value is `TRUE`.
  /// This property is linked with `CoreWebView2Profile.IsGeneralAutofillEnabled`, so
  /// changing one will change the other. And all WebView2s that created with the same
  /// `CoreWebView2Profile` will share this property, so for the WebView2s with the same
  /// profile, their `CoreWebView2Settings.IsGeneralAutofillEnabled` and
  /// `CoreWebView2Profile.IsGeneralAutofillEnabled` are always in sync.
  ///
  /// \snippet SettingsComponent.cpp GeneralAutofillEnabled
  [propget] HRESULT IsGeneralAutofillEnabled([out, retval] BOOL* value);
  
  /// Set the IsGeneralAutofillEnabled property.
  [propput] HRESULT IsGeneralAutofillEnabled([in] BOOL value);
}

/// A continuation of the ICoreWebView2Settings interface to manage pinch zoom.
[uuid(183e7052-1d03-43a0-ab99-98e043b66b39), object, pointer_default(unique)]
interface ICoreWebView2Settings5 : ICoreWebView2Settings4 {
  /// Pinch-zoom, referred to as "Page Scale" zoom, is performed as a post-rendering step,
  /// it changes the page scale factor property and scales the surface the web page is
  /// rendered onto when user performs a pinch zooming action. It does not change the layout
  /// but rather changes the viewport and clips the web content, the content outside of the
  /// viewport isn't visible onscreen and users can't reach this content using mouse.
  ///
  /// The `IsPinchZoomEnabled` property enables or disables the ability of
  /// the end user to use a pinching motion on touch input enabled devices
  /// to scale the web content in the WebView2. It defaults to `TRUE`.
  /// When set to `FALSE`, the end user cannot pinch zoom after the next navigation.
  /// Disabling/Enabling `IsPinchZoomEnabled` only affects the end user's ability to use
  /// pinch motions and does not change the page scale factor.
  /// This API only affects the Page Scale zoom and has no effect on the
  /// existing browser zoom properties (`IsZoomControlEnabled` and `ZoomFactor`)
  /// or other end user mechanisms for zooming.
  ///
  /// \snippet SettingsComponent.cpp TogglePinchZoomEnabled
  [propget] HRESULT IsPinchZoomEnabled([out, retval] BOOL* enabled);
  /// Set the `IsPinchZoomEnabled` property
  [propput] HRESULT IsPinchZoomEnabled([in] BOOL enabled);
}

/// A continuation of the ICoreWebView2Settings interface to manage swipe navigation.
[uuid(11cb3acd-9bc8-43b8-83bf-f40753714f87), object, pointer_default(unique)]
interface ICoreWebView2Settings6 : ICoreWebView2Settings5 {
  /// The `IsSwipeNavigationEnabled` property enables or disables the ability of the
  /// end user to use swiping gesture on touch input enabled devices to
  /// navigate in WebView2. It defaults to `TRUE`.
  ///
  /// When this property is `TRUE`, then all configured navigation gestures are enabled:
  /// 1. Swiping left and right to navigate forward and backward is always configured.
  /// 2. Swiping down to refresh is off by default and not exposed via our API currently,
  /// it requires the "--pull-to-refresh" option to be included in the additional browser
  /// arguments to be configured. (See put_AdditionalBrowserArguments.)
  ///
  /// When set to `FALSE`, the end user cannot swipe to navigate or pull to refresh.
  /// This API only affects the overscrolling navigation functionality and has no
  /// effect on the scrolling interaction used to explore the web content shown
  /// in WebView2.
  ///
  /// Disabling/Enabling IsSwipeNavigationEnabled takes effect after the
  /// next navigation.
  ///
  /// \snippet SettingsComponent.cpp ToggleSwipeNavigationEnabled
  [propget] HRESULT IsSwipeNavigationEnabled([out, retval] BOOL* enabled);
  /// Set the `IsSwipeNavigationEnabled` property
  [propput] HRESULT IsSwipeNavigationEnabled([in] BOOL enabled);
}

/// A continuation of the ICoreWebView2Settings interface to hide Pdf toolbar items.
[uuid(488dc902-35ef-42d2-bc7d-94b65c4bc49c), object, pointer_default(unique)]
interface ICoreWebView2Settings7 : ICoreWebView2Settings6 {
  /// `HiddenPdfToolbarItems` is used to customize the PDF toolbar items. By default, it is COREWEBVIEW2_PDF_TOOLBAR_ITEMS_NONE and so it displays all of the items.
  /// Changes to this property apply to all CoreWebView2s in the same environment and using the same profile.
  /// Changes to this setting apply only after the next navigation.
  /// \snippet SettingsComponent.cpp ToggleHidePdfToolbarItems
  [propget] HRESULT HiddenPdfToolbarItems([out, retval] COREWEBVIEW2_PDF_TOOLBAR_ITEMS* hidden_pdf_toolbar_items);

  /// Set the `HiddenPdfToolbarItems` property.
  [propput] HRESULT HiddenPdfToolbarItems([in] COREWEBVIEW2_PDF_TOOLBAR_ITEMS hidden_pdf_toolbar_items);
}

/// Event args for the `ProcessFailed` event.

[uuid(8155a9a4-1474-4a86-8cae-151b0fa6b8ca), object, pointer_default(unique)]
interface ICoreWebView2ProcessFailedEventArgs : IUnknown {

  /// The kind of process failure that has occurred. `processFailedKind` is
  /// `COREWEBVIEW2_PROCESS_FAILED_KIND_RENDER_PROCESS_EXITED` if the
  /// failed process is the main frame's renderer, even if there were subframes
  /// rendered by such process; all frames are gone when this happens.
  [propget] HRESULT ProcessFailedKind(
      [out, retval] COREWEBVIEW2_PROCESS_FAILED_KIND* processFailedKind);
}

/// Receives `ProcessFailed` events.
[uuid(79e0aea4-990b-42d9-aa1d-0fcc2e5bc7f1), object, pointer_default(unique)]
interface ICoreWebView2ProcessFailedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.

  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] ICoreWebView2ProcessFailedEventArgs* args);
}

/// Implements the interface to receive `ZoomFactorChanged` events.  Use the
/// `ICoreWebView2Controller.ZoomFactor` property to get the modified zoom
/// factor.

[uuid(b52d71d6-c4df-4543-a90c-64a3e60f38cb), object, pointer_default(unique)]
interface ICoreWebView2ZoomFactorChangedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.  No event args exist
  /// and the `args` parameter is set to `null`.

  HRESULT Invoke([in] ICoreWebView2Controller* sender, [in] IUnknown* args);
}

/// Iterator for a collection of HTTP headers.  For more information, navigate
/// to ICoreWebView2HttpRequestHeaders and ICoreWebView2HttpResponseHeaders.
///
/// \snippet ScenarioWebViewEventMonitor.cpp HttpRequestHeaderIterator
[uuid(0702fc30-f43b-47bb-ab52-a42cb552ad9f), object, pointer_default(unique)]
interface ICoreWebView2HttpHeadersCollectionIterator : IUnknown {

  /// Get the name and value of the current HTTP header of the iterator.  If
  /// the previous `MoveNext` operation set the `hasNext` parameter to `FALSE`,
  /// this method fails.
  ///
  /// The caller must free the returned strings with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  HRESULT GetCurrentHeader([out] LPWSTR* name, [out] LPWSTR* value);

  /// `TRUE` when the iterator has not run out of headers.  If the collection
  /// over which the iterator is iterating is empty or if the iterator has gone
  ///  past the end of the collection then this is `FALSE`.

  [propget] HRESULT HasCurrentHeader([out, retval] BOOL* hasCurrent);

  /// Move the iterator to the next HTTP header in the collection.
  ///
  /// \> [!NOTE]\n \> If no more HTTP headers exist, the `hasNext` parameter is set to
  /// `FALSE`.  After this occurs the `GetCurrentHeader` method fails.

  HRESULT MoveNext([out, retval] BOOL* hasNext);
}

/// HTTP request headers.  Used to inspect the HTTP request on
/// `WebResourceRequested` event and `NavigationStarting` event.
///
/// \> [!NOTE]\n\> It is possible to modify the HTTP request from a `WebResourceRequested`
/// event, but not from a `NavigationStarting` event.
[uuid(e86cac0e-5523-465c-b536-8fb9fc8c8c60), object, pointer_default(unique)]
interface ICoreWebView2HttpRequestHeaders : IUnknown {

  /// Gets the header value matching the name.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  HRESULT GetHeader([in] LPCWSTR name, [out, retval] LPWSTR* value);

  /// Gets the header value matching the name using an iterator.

  HRESULT GetHeaders([in] LPCWSTR name, [out, retval] ICoreWebView2HttpHeadersCollectionIterator** iterator);

  /// Verifies that the headers contain an entry that matches the header name.

  HRESULT Contains([in] LPCWSTR name, [out, retval] BOOL* contains);

  /// Adds or updates header that matches the name.

  HRESULT SetHeader([in] LPCWSTR name, [in] LPCWSTR value);

  /// Removes header that matches the name.

  HRESULT RemoveHeader([in] LPCWSTR name);

  /// Gets an iterator over the collection of request headers.

  HRESULT GetIterator(
      [out, retval] ICoreWebView2HttpHeadersCollectionIterator** iterator);
}

/// HTTP response headers.  Used to construct a `WebResourceResponse` for the
/// `WebResourceRequested` event.
[uuid(03c5ff5a-9b45-4a88-881c-89a9f328619c), object, pointer_default(unique)]
interface ICoreWebView2HttpResponseHeaders : IUnknown {

  /// Appends header line with name and value.

  HRESULT AppendHeader([in] LPCWSTR name, [in] LPCWSTR value);

  /// Verifies that the headers contain entries that match the header name.

  HRESULT Contains([in] LPCWSTR name, [out, retval] BOOL* contains);

  /// Gets the first header value in the collection matching the name.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  HRESULT GetHeader([in] LPCWSTR name, [out, retval] LPWSTR* value);

  /// Gets the header values matching the name.

  HRESULT GetHeaders([in] LPCWSTR name, [out, retval] ICoreWebView2HttpHeadersCollectionIterator** iterator);

  /// Gets an iterator over the collection of entire response headers.

  HRESULT GetIterator(
  [out, retval] ICoreWebView2HttpHeadersCollectionIterator** iterator);
}

/// An HTTP request used with the `WebResourceRequested` event.
[uuid(97055cd4-512c-4264-8b5f-e3f446cea6a5), object, pointer_default(unique)]
interface ICoreWebView2WebResourceRequest : IUnknown {

  /// The request URI.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT Uri([out, retval] LPWSTR* uri);

  /// Sets the `Uri` property.

  [propput] HRESULT Uri([in] LPCWSTR uri);

  /// The HTTP request method.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT Method([out, retval] LPWSTR* method);

  /// Sets the `Method` property.

  [propput] HRESULT Method([in] LPCWSTR method);

  /// The HTTP request message body as stream.  POST data should be here.  If a
  /// stream is set, which overrides the message body, the stream must have
  /// all the content data available by the time the `WebResourceRequested`
  /// event deferral of this response is completed.  Stream should be agile or
  /// be created from a background STA to prevent performance impact to the UI
  /// thread.  `Null` means no content data.  `IStream` semantics apply
  /// (return `S_OK` to `Read` runs until all data is exhausted).

  [propget] HRESULT Content([out, retval] IStream** content);

  /// Sets the `Content` property.

  [propput] HRESULT Content([in] IStream* content);

  /// The mutable HTTP request headers

  [propget] HRESULT Headers([out, retval] ICoreWebView2HttpRequestHeaders** headers);
}

/// An HTTP response used with the `WebResourceRequested` event.
[uuid(aafcc94f-fa27-48fd-97df-830ef75aaec9), object, pointer_default(unique)]
interface ICoreWebView2WebResourceResponse : IUnknown {

  /// HTTP response content as stream.  Stream must have all the content data
  /// available by the time the `WebResourceRequested` event deferral of this
  /// response is completed.  Stream should be agile or be created from a
  /// background thread to prevent performance impact to the UI thread.  `Null`
  ///  means no content data.  `IStream` semantics apply (return `S_OK` to
  /// `Read` runs until all data is exhausted).

  [propget] HRESULT Content([out, retval] IStream** content);

  /// Sets the `Content` property.

  [propput] HRESULT Content([in] IStream* content);

  /// Overridden HTTP response headers.

  [propget] HRESULT Headers([out, retval] ICoreWebView2HttpResponseHeaders** headers);

  /// The HTTP response status code.

  [propget] HRESULT StatusCode([out, retval] int* statusCode);

  /// Sets the `StatusCode` property.

  [propput] HRESULT StatusCode([in] int statusCode);

  /// The HTTP response reason phrase.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT ReasonPhrase([out, retval] LPWSTR* reasonPhrase);

  /// Sets the `ReasonPhrase` property.

  [propput] HRESULT ReasonPhrase([in] LPCWSTR reasonPhrase);
}

/// Event args for the `NavigationStarting` event.
[uuid(5b495469-e119-438a-9b18-7604f25f2e49), object, pointer_default(unique)]
interface ICoreWebView2NavigationStartingEventArgs : IUnknown {

  /// The uri of the requested navigation.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT Uri([out, retval] LPWSTR* uri);

  /// `TRUE` when the navigation was initiated through a user gesture as
  /// opposed to programmatic navigation by page script. Navigations initiated
  /// via WebView2 APIs are considered as user initiated.

  [propget] HRESULT IsUserInitiated([out, retval] BOOL* isUserInitiated);

  /// `TRUE` when the navigation is redirected.

  [propget] HRESULT IsRedirected([out, retval] BOOL* isRedirected);

  /// The HTTP request headers for the navigation.
  ///
  /// \> [!NOTE]\n\> You are not able to modify the HTTP request headers in a
  /// `NavigationStarting` event.

  [propget] HRESULT RequestHeaders([out, retval] ICoreWebView2HttpRequestHeaders** requestHeaders);

  /// The host may set this flag to cancel the navigation.  If set, the
  /// navigation is not longer present and the content of the current page is
  /// intact.  For performance reasons, `GET` HTTP requests may happen, while
  /// the host is responding.  You may set cookies and use part of a request
  /// for the navigation.  Cancellation for navigation to `about:blank` or
  /// frame navigation to `srcdoc` is not supported.  Such attempts are
  /// ignored.  A cancelled navigation will fire a `NavigationCompleted` event
  /// with a `WebErrorStatus` of
  /// `COREWEBVIEW2_WEB_ERROR_STATUS_OPERATION_CANCELED`.

  [propget] HRESULT Cancel([out, retval] BOOL* cancel);

  /// Sets the `Cancel` property.

  [propput] HRESULT Cancel([in] BOOL cancel);

  /// The ID of the navigation.

  [propget] HRESULT NavigationId([out, retval] UINT64* navigationId);
}

/// The AdditionalAllowedFrameAncestors API that enable developers to provide additional allowed frame ancestors.
[uuid(9086BE93-91AA-472D-A7E0-579F2BA006AD), object, pointer_default(unique)]
interface ICoreWebView2NavigationStartingEventArgs2 : ICoreWebView2NavigationStartingEventArgs {

  /// Get additional allowed frame ancestors set by the host app.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT AdditionalAllowedFrameAncestors([out, retval] LPWSTR* value);

  /// The app may set this property to allow a frame to be embedded by additional ancestors besides what is allowed by
  /// http header [X-Frame-Options](https://developer.mozilla.org/docs/Web/HTTP/Headers/X-Frame-Options)
  /// and [Content-Security-Policy frame-ancestors directive](https://developer.mozilla.org/docs/Web/HTTP/Headers/Content-Security-Policy/frame-ancestors).
  /// If set, a frame ancestor is allowed if it is allowed by the additional allowed frame
  /// ancestors or original http header from the site.
  /// Whether an ancestor is allowed by the additional allowed frame ancestors is done the same way as if the site provided
  /// it as the source list of the Content-Security-Policy frame-ancestors directive.
  /// For example, if `https://example.com` and `https://www.example.com` are the origins of the top
  /// page and intermediate iframes that embed a nested site-embedding iframe, and you fully trust
  /// those origins, you should set this property to `https://example.com https://www.example.com`.
  /// This property gives the app the ability to use iframe to embed sites that otherwise
  /// could not be embedded in an iframe in trusted app pages.
  /// This could potentially subject the embedded sites to [Clickjacking](https://en.wikipedia.org/wiki/Clickjacking)
  /// attack from the code running in the embedding web page. Therefore, you should only
  /// set this property with origins of fully trusted embedding page and any intermediate iframes.
  /// Whenever possible, you should use the list of specific origins of the top and intermediate
  /// frames instead of wildcard characters for this property.
  /// This API is to provide limited support for app scenarios that used to be supported by
  /// `<webview>` element in other solutions like JavaScript UWP apps and Electron.
  /// You should limit the usage of this property to trusted pages, and specific navigation
  /// target url, by checking the `Source` of the WebView2, and `Uri` of the event args.
  ///
  /// This property is ignored for top level document navigation.
  ///
  /// \snippet ScriptComponent.cpp AdditionalAllowedFrameAncestors_1
  ///
  /// \snippet ScriptComponent.cpp AdditionalAllowedFrameAncestors_2
  [propput] HRESULT AdditionalAllowedFrameAncestors([in] LPCWSTR value);

}

/// Receives `NavigationStarting` events.
[uuid(9adbe429-f36d-432b-9ddc-f8881fbd76e3), object, pointer_default(unique)]
interface ICoreWebView2NavigationStartingEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.

  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] ICoreWebView2NavigationStartingEventArgs* args);
}

/// Event args for the `ContentLoading` event.
[uuid(0c8a1275-9b6b-4901-87ad-70df25bafa6e), object, pointer_default(unique)]
interface ICoreWebView2ContentLoadingEventArgs : IUnknown {

  /// `TRUE` if the loaded content is an error page.

  [propget] HRESULT IsErrorPage([out, retval] BOOL* isErrorPage);

  /// The ID of the navigation.

  [propget] HRESULT NavigationId([out, retval] UINT64* navigationId);
}

/// Receives `ContentLoading` events.
[uuid(364471e7-f2be-4910-bdba-d72077d51c4b), object, pointer_default(unique)]
interface ICoreWebView2ContentLoadingEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.

  HRESULT Invoke([in] ICoreWebView2* sender, [in] ICoreWebView2ContentLoadingEventArgs* args);
}

/// Event args for the `SourceChanged` event.
[uuid(31e0e545-1dba-4266-8914-f63848a1f7d7), object, pointer_default(unique)]
interface ICoreWebView2SourceChangedEventArgs : IUnknown {

  /// `TRUE` if the page being navigated to is a new document.

  [propget] HRESULT IsNewDocument([out, retval] BOOL* isNewDocument);
}

/// Receives `SourceChanged` events.
[uuid(3c067f9f-5388-4772-8b48-79f7ef1ab37c), object, pointer_default(unique)]
interface ICoreWebView2SourceChangedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.

  HRESULT Invoke([in] ICoreWebView2* sender, [in] ICoreWebView2SourceChangedEventArgs* args);
}

/// Receives `HistoryChanged` events.
[uuid(c79a420c-efd9-4058-9295-3e8b4bcab645), object, pointer_default(unique)]
interface ICoreWebView2HistoryChangedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.  No event args exist
  /// and the `args` parameter is set to `null`.

  HRESULT Invoke([in] ICoreWebView2* sender, [in] IUnknown* args);
}

/// Event args for the `ScriptDialogOpening` event.
[uuid(7390bb70-abe0-4843-9529-f143b31b03d6), object, pointer_default(unique)]
interface ICoreWebView2ScriptDialogOpeningEventArgs : IUnknown {

  /// The URI of the page that requested the dialog box.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT Uri([out, retval] LPWSTR* uri);

  /// The kind of JavaScript dialog box.  `alert`, `confirm`, `prompt`, or
  /// `beforeunload`.

  [propget] HRESULT Kind([out, retval] COREWEBVIEW2_SCRIPT_DIALOG_KIND* kind);

  /// The message of the dialog box.  From JavaScript this is the first
  /// parameter passed to `alert`, `confirm`, and `prompt` and is empty for
  /// `beforeunload`.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT Message([out, retval] LPWSTR* message);

  /// The host may run this to respond with **OK** to `confirm`, `prompt`, and
  /// `beforeunload` dialogs.  Do not run this method to indicate cancel.
  /// From JavaScript, this means that the `confirm` and `beforeunload` function
  /// returns `TRUE` if `Accept` is run.  And for the prompt function it returns
  /// the value of `ResultText` if `Accept` is run and otherwise returns
  /// `FALSE`.

  HRESULT Accept();

  /// The second parameter passed to the JavaScript prompt dialog.
  /// The result of the prompt JavaScript function uses this value as the
  /// default value.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT DefaultText([out, retval] LPWSTR* defaultText);

  /// The return value from the JavaScript prompt function if `Accept` is run.
  ///  This value is ignored for dialog kinds other than prompt.  If `Accept`
  /// is not run, this value is ignored and `FALSE` is returned from prompt.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT ResultText([out, retval] LPWSTR* resultText);

  /// Sets the `ResultText` property.

  [propput] HRESULT ResultText([in] LPCWSTR resultText);

  /// Returns an `ICoreWebView2Deferral` object.  Use this operation to
  /// complete the event at a later time.

  HRESULT GetDeferral([out, retval] ICoreWebView2Deferral** deferral);
}

/// Receives `ScriptDialogOpening` events.
[uuid(ef381bf9-afa8-4e37-91c4-8ac48524bdfb), object, pointer_default(unique)]
interface ICoreWebView2ScriptDialogOpeningEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.

  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] ICoreWebView2ScriptDialogOpeningEventArgs* args);
}

/// Event args for the `NavigationCompleted` event.
[uuid(30d68b7d-20d9-4752-a9ca-ec8448fbb5c1), object, pointer_default(unique)]
interface ICoreWebView2NavigationCompletedEventArgs : IUnknown {

  /// `TRUE` when the navigation is successful.  `FALSE` for a navigation that
  /// ended up in an error page (failures due to no network, DNS lookup
  /// failure, HTTP server responds with 4xx), but may also be `FALSE` for
  /// additional scenarios such as `window.stop()` run on navigated page.

  [propget] HRESULT IsSuccess([out, retval] BOOL* isSuccess);

  /// The error code if the navigation failed.

  [propget] HRESULT WebErrorStatus([out, retval] COREWEBVIEW2_WEB_ERROR_STATUS*
      webErrorStatus);

  /// The ID of the navigation.

  [propget] HRESULT NavigationId([out, retval] UINT64* navigationId);
}

/// This is an interface for the StatusCode property of
/// ICoreWebView2NavigationCompletedEventArgs
[uuid(FDF8B738-EE1E-4DB2-A329-8D7D7B74D792), object, pointer_default(unique)]
interface ICoreWebView2NavigationCompletedEventArgs2 : ICoreWebView2NavigationCompletedEventArgs {
  /// The HTTP status code of the navigation if it involved an HTTP request.
  /// For instance, this will usually be 200 if the request was successful, 404
  /// if a page was not found, etc.  See
  /// https://developer.mozilla.org/docs/Web/HTTP/Status for a list of
  /// common status codes.
  ///
  /// The `HttpStatusCode` property will be 0 in the following cases:
  /// * The navigation did not involve an HTTP request.  For instance, if it was
  ///   a navigation to a file:// URL, or if it was a same-document navigation.
  /// * The navigation failed before a response was received.  For instance, if
  ///   the hostname was not found, or if there was a network error.
  ///
  /// In those cases, you can get more information from the `IsSuccess` and
  /// `WebErrorStatus` properties.
  ///
  /// If the navigation receives a successful HTTP response, but the navigated
  /// page calls `window.stop()` before it finishes loading, then
  /// `HttpStatusCode` may contain a success code like 200, but `IsSuccess` will
  /// be FALSE and `WebErrorStatus` will be
  /// `COREWEBVIEW2_WEB_ERROR_STATUS_CONNECTION_ABORTED`.
  ///
  /// Since WebView2 handles HTTP continuations and redirects automatically, it
  /// is unlikely for `HttpStatusCode` to ever be in the 1xx or 3xx ranges.
  [propget] HRESULT HttpStatusCode([out, retval] int* http_status_code);
}

/// Receives `NavigationCompleted` events.
[uuid(d33a35bf-1c49-4f98-93ab-006e0533fe1c), object, pointer_default(unique)]
interface ICoreWebView2NavigationCompletedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.

  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] ICoreWebView2NavigationCompletedEventArgs* args);
}

/// Event args for the `PermissionRequested` event.
[uuid(973ae2ef-ff18-4894-8fb2-3c758f046810), object, pointer_default(unique)]
interface ICoreWebView2PermissionRequestedEventArgs : IUnknown {

  /// The origin of the web content that requests the permission.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT Uri([out, retval] LPWSTR* uri);

  /// The type of the permission that is requested.

  [propget] HRESULT PermissionKind([out, retval] COREWEBVIEW2_PERMISSION_KIND* permissionKind);

  /// `TRUE` when the permission request was initiated through a user gesture.
  ///
  /// \> [!NOTE]\n\> Being initiated through a user gesture does not mean that user intended
  /// to access the associated resource.

  [propget] HRESULT IsUserInitiated([out, retval] BOOL* isUserInitiated);

  /// The status of a permission request, (for example is the request is granted).
  /// The default value is `COREWEBVIEW2_PERMISSION_STATE_DEFAULT`.

  [propget] HRESULT State([out, retval] COREWEBVIEW2_PERMISSION_STATE* state);

  /// Sets the `State` property.

  [propput] HRESULT State([in] COREWEBVIEW2_PERMISSION_STATE state);

  /// Gets an `ICoreWebView2Deferral` object.  Use the deferral object to make
  /// the permission decision at a later time.

  HRESULT GetDeferral([out, retval] ICoreWebView2Deferral** deferral);
}

/// Receives `PermissionRequested` events.
[uuid(15e1c6a3-c72a-4df3-91d7-d097fbec6bfd), object, pointer_default(unique)]
interface ICoreWebView2PermissionRequestedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.

  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] ICoreWebView2PermissionRequestedEventArgs* args);
}

/// Receives the result of the `AddScriptToExecuteOnDocumentCreated` method.
[uuid(b99369f3-9b11-47b5-bc6f-8e7895fcea17), object, pointer_default(unique)]
interface ICoreWebView2AddScriptToExecuteOnDocumentCreatedCompletedHandler : IUnknown {

  /// Provide the completion status and result of the corresponding
  /// asynchronous method.

  HRESULT Invoke([in] HRESULT errorCode, [in] LPCWSTR id);
}

/// Receives the result of the `ExecuteScript` method.
[uuid(49511172-cc67-4bca-9923-137112f4c4cc), object, pointer_default(unique)]
interface ICoreWebView2ExecuteScriptCompletedHandler : IUnknown {

  /// Provide the implementer with the completion status and result of the
  /// corresponding asynchronous method.

  HRESULT Invoke([in] HRESULT errorCode, [in] LPCWSTR resultObjectAsJson);
}

/// Event args for the `WebResourceRequested` event.
[uuid(453e667f-12c7-49d4-be6d-ddbe7956f57a), object, pointer_default(unique)]
interface ICoreWebView2WebResourceRequestedEventArgs : IUnknown {

  /// The Web resource request.  The request object may be missing some headers
  /// that are added by network stack at a later time.

  [propget] HRESULT Request([out, retval] ICoreWebView2WebResourceRequest** request);

  /// A placeholder for the web resource response object.  If this object is
  /// set, the web resource request is completed with the specified response.

  [propget] HRESULT Response([out, retval] ICoreWebView2WebResourceResponse** response);

  /// Sets the `Response` property.  Create an empty web resource response
  /// object with `CreateWebResourceResponse` and then modify it to construct
  /// the response.

  [propput] HRESULT Response([in] ICoreWebView2WebResourceResponse* response);

  /// Obtain an `ICoreWebView2Deferral` object and put the event into a
  /// deferred state.  Use the `ICoreWebView2Deferral` object to complete the
  /// request at a later time.

  HRESULT GetDeferral([out, retval] ICoreWebView2Deferral** deferral);

  /// The web resource request context.

  [propget] HRESULT ResourceContext([out, retval] COREWEBVIEW2_WEB_RESOURCE_CONTEXT* context);
}

/// Runs when a URL request (through network, file, and so on) is made in
/// the webview for a Web resource matching resource context filter and URL
/// specified in `AddWebResourceRequestedFilter`.  The host views and modifies
/// the request or provide a response in a similar pattern to HTTP, in which
/// case the request immediately completed.  This may not contain any request
/// headers that are added by the network stack, such as an `Authorization`
/// header.
[uuid(ab00b74c-15f1-4646-80e8-e76341d25d71), object, pointer_default(unique)]
interface ICoreWebView2WebResourceRequestedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.

  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] ICoreWebView2WebResourceRequestedEventArgs* args);
}

/// Receives the result of the `CapturePreview` method.  The result is written
/// to the stream provided in the `CapturePreview` method.

[uuid(697e05e9-3d8f-45fa-96f4-8ffe1ededaf5), object, pointer_default(unique)]
interface ICoreWebView2CapturePreviewCompletedHandler : IUnknown {

  /// Provides the completion status of the corresponding asynchronous method.

  HRESULT Invoke([in] HRESULT errorCode);
}

/// Receives `GotFocus` and `LostFocus` events.

[uuid(05ea24bd-**************-4b82b498135d), object, pointer_default(unique)]
interface ICoreWebView2FocusChangedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.  No event args exist
  /// and the `args` parameter is set to `null`.

  HRESULT Invoke(
      [in] ICoreWebView2Controller* sender,
      [in] IUnknown* args);
}

/// Event args for the `MoveFocusRequested` event.

[uuid(2d6aa13b-3839-4a15-92fc-d88b3c0d9c9d), object, pointer_default(unique)]
interface ICoreWebView2MoveFocusRequestedEventArgs : IUnknown {

  /// The reason for WebView to run the `MoveFocusRequested` event.

  [propget] HRESULT Reason([out, retval] COREWEBVIEW2_MOVE_FOCUS_REASON* reason);

  /// Indicates whether the event has been handled by the app.  If the app has
  /// moved the focus to another desired location, it should set the `Handled`
  /// property to `TRUE`.  When the `Handled` property is `FALSE` after the
  /// event handler returns, default action is taken.  The default action is to
  /// try to find the next tab stop child window in the app and try to move
  /// focus to that window.  If no other window exists to move focus, focus is
  /// cycled within the web content of the WebView.

  [propget] HRESULT Handled([out, retval] BOOL* value);

  /// Sets the `Handled` property.

  [propput] HRESULT Handled([in] BOOL value);
}

/// Receives `MoveFocusRequested` events.

[uuid(69035451-6dc7-4cb8-9bce-b2bd70ad289f), object, pointer_default(unique)]
interface ICoreWebView2MoveFocusRequestedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.

  HRESULT Invoke(
      [in] ICoreWebView2Controller* sender,
      [in] ICoreWebView2MoveFocusRequestedEventArgs* args);
}

/// Event args for the `WebMessageReceived` event.
[uuid(0f99a40c-e962-4207-9e92-e3d542eff849), object, pointer_default(unique)]
interface ICoreWebView2WebMessageReceivedEventArgs : IUnknown {

  /// The URI of the document that sent this web message.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT Source([out, retval] LPWSTR* source);

  /// The message posted from the WebView content to the host converted to a
  /// JSON string.  Run this operation to communicate using JavaScript objects.
  ///
  /// For example, the following `postMessage` runs result in the following
  /// `WebMessageAsJson` values.
  ///
  /// ```json
  /// postMessage({'a': 'b'})      L"{\"a\": \"b\"}"
  /// postMessage(1.2)             L"1.2"
  /// postMessage('example')       L"\"example\""
  /// ```
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT WebMessageAsJson([out, retval] LPWSTR* webMessageAsJson);

  /// If the message posted from the WebView content to the host is a string
  /// type, this method returns the value of that string.  If the message
  /// posted is some other kind of JavaScript type this method fails with the
  /// following error.
  ///
  /// ```text
  /// E_INVALIDARG
  /// ```
  ///
  /// Run this operation to communicate using simple strings.
  ///
  /// For example, the following `postMessage` runs result in the following
  /// `WebMessageAsString` values.
  ///
  /// ```json
  /// postMessage({'a': 'b'})      E_INVALIDARG
  /// postMessage(1.2)             E_INVALIDARG
  /// postMessage('example')       L"example"
  /// ```
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  HRESULT TryGetWebMessageAsString([out, retval] LPWSTR* webMessageAsString);
}

/// Receives `WebMessageReceived` events.
[uuid(57213f19-00e6-49fa-8e07-898ea01ecbd2), object, pointer_default(unique)]
interface ICoreWebView2WebMessageReceivedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.

  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] ICoreWebView2WebMessageReceivedEventArgs* args);
}

/// Event args for the `DevToolsProtocolEventReceived` event.
[uuid(653c2959-bb3a-4377-8632-b58ada4e66c4), object, pointer_default(unique)]
interface ICoreWebView2DevToolsProtocolEventReceivedEventArgs : IUnknown {

  /// The parameter object of the corresponding `DevToolsProtocol` event
  /// represented as a JSON string.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT ParameterObjectAsJson([out, retval] LPWSTR*
                                    parameterObjectAsJson);
}

/// This is a continuation of the `ICoreWebView2DevToolsProtocolEventReceivedEventArgs`
/// interface that provides the session ID of the target where the event originates from.
[uuid(2DC4959D-1494-4393-95BA-BEA4CB9EBD1B), object, pointer_default(unique)]
interface ICoreWebView2DevToolsProtocolEventReceivedEventArgs2 : ICoreWebView2DevToolsProtocolEventReceivedEventArgs {

  /// The sessionId of the target where the event originates from.
  /// Empty string is returned as sessionId if the event comes from the default
  /// session for the top page.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  ///
  /// \snippet ScriptComponent.cpp DevToolsProtocolEventReceivedSessionId
  [propget] HRESULT SessionId([out, retval] LPWSTR* sessionId);
}

/// Receives `DevToolsProtocolEventReceived` events from the WebView.
[uuid(e2fda4be-5456-406c-a261-3d452138362c), object, pointer_default(unique)]
interface ICoreWebView2DevToolsProtocolEventReceivedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.

  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] ICoreWebView2DevToolsProtocolEventReceivedEventArgs* args);
}

/// Receives `CallDevToolsProtocolMethod` completion results.
[uuid(5c4889f0-5ef6-4c5a-952c-d8f1b92d0574), object, pointer_default(unique)]
interface ICoreWebView2CallDevToolsProtocolMethodCompletedHandler : IUnknown {

  /// Provides the completion status and result of the corresponding
  /// asynchronous method.

  HRESULT Invoke([in] HRESULT errorCode, [in] LPCWSTR returnObjectAsJson);
}

/// Receives the `CoreWebView2Controller` created using `CreateCoreWebView2Controller`.

[uuid(6c4819f3-c9b7-4260-8127-c9f5bde7f68c), object, pointer_default(unique)]
interface ICoreWebView2CreateCoreWebView2ControllerCompletedHandler : IUnknown {

  /// Provides the completion status and result of the corresponding
  /// asynchronous method.

  HRESULT Invoke(HRESULT errorCode, ICoreWebView2Controller* createdController);
}

/// The caller implements this interface to receive the CoreWebView2Controller
/// created via CreateCoreWebView2CompositionController.
[uuid(02fab84b-1428-4fb7-ad45-1b2e64736184), object, pointer_default(unique)]
interface ICoreWebView2CreateCoreWebView2CompositionControllerCompletedHandler : IUnknown {
  /// Called to provide the implementer with the completion status and result
  /// of the corresponding asynchronous method call.
  HRESULT Invoke(
      HRESULT errorCode,
      ICoreWebView2CompositionController* webView);
}

/// Event args for the `NewWindowRequested` event.  The event is run when
/// content inside webview requested to a open a new window (through
/// `window.open()` and so on).
[uuid(34acb11c-fc37-4418-9132-f9c21d1eafb9), object, pointer_default(unique)]
interface ICoreWebView2NewWindowRequestedEventArgs : IUnknown {

  /// The target uri of the new window requested.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT Uri([out, retval] LPWSTR* uri);

  /// Sets a CoreWebView2 as a result of the NewWindowRequested event. If the
  /// `NewWindow` is set, the top-level window returns as the opened `WindowProxy`.
  /// The NewWindow property should be set to a CoreWebView2 that has not been
  /// navigated previously. Don't use methods that cause navigation or interact
  /// with the DOM on this CoreWebView2. Setting event handlers, changing
  /// Settings properties, or other methods are fine to call. Changes to
  /// settings should be made before `put_NewWindow` is called to ensure that
  /// those settings take effect for the newly setup WebView. Once the
  /// NewWindow is set the underlying web contents of this CoreWebView2 will be
  /// replaced and navigated as appropriate for the new window.
  /// After setting new window it cannot be changed and error will be return otherwise.
  ///
  /// The methods which should affect the new web contents like
  /// AddScriptToExecuteOnDocumentCreated and add_WebResourceRequested
  /// have to be called after setting NewWindow.
  ///
  /// The new WebView must have the same profile as the opener WebView.

  [propput] HRESULT NewWindow([in] ICoreWebView2* newWindow);

  /// Gets the new window.

  [propget] HRESULT NewWindow([out, retval] ICoreWebView2** newWindow);

  /// Sets whether the `NewWindowRequested` event is handled by host.  If this
  /// is `FALSE` and no `NewWindow` is set, the WebView opens a popup window
  /// and it returns as opened `WindowProxy`.  If set to `TRUE` and no
  /// `NewWindow` is set for `window.open`, the opened `WindowProxy` is for an
  /// testing window object and no window loads.
  /// The default value is `FALSE`.

  [propput] HRESULT Handled([in] BOOL handled);

  /// Gets whether the `NewWindowRequested` event is handled by host.

  [propget] HRESULT Handled([out, retval] BOOL* handled);

  /// `TRUE` when the new window request was initiated through a user gesture.
  /// Examples of user initiated requests are:
  ///
  /// - Selecting an anchor tag with target
  /// - Programmatic window open from a script that directly run as a result of
  /// user interaction such as via onclick handlers.
  ///
  /// Non-user initiated requests are programmatic window opens from a script
  /// that are not directly triggered by user interaction, such as those that
  /// run while loading a new page or via timers.
  /// The Microsoft Edge popup blocker is disabled for WebView so the app is
  /// able to use this flag to block non-user initiated popups.

  [propget] HRESULT IsUserInitiated([out, retval] BOOL* isUserInitiated);

  /// Obtain an `ICoreWebView2Deferral` object and put the event into a
  /// deferred state.  Use the `ICoreWebView2Deferral` object to complete the
  /// window open request at a later time.  While this event is deferred the
  /// opener window returns a `WindowProxy` to an un-navigated window, which
  /// navigates when the deferral is complete.

  HRESULT GetDeferral([out, retval] ICoreWebView2Deferral** deferral);

  /// Window features specified by the `window.open`.  The features should be
  /// considered for positioning and sizing of new webview windows.

  [propget] HRESULT WindowFeatures([out, retval] ICoreWebView2WindowFeatures** value);
}

/// This is a continuation of the `ICoreWebView2NewWindowRequestedEventArgs` interface.
[uuid(bbc7baed-74c6-4c92-b63a-7f5aeae03de3), object, pointer_default(unique)]
interface ICoreWebView2NewWindowRequestedEventArgs2 : ICoreWebView2NewWindowRequestedEventArgs {
  /// Gets the name of the new window. This window can be created via `window.open(url, windowName)`,
  /// where the windowName parameter corresponds to `Name` property.
  /// If no windowName is passed to `window.open`, then the `Name` property
  /// will be set to an empty string. Additionally, if window is opened through other means,
  /// such as `<a target="windowName">...</a>` or `<iframe name="windowName">...</iframe>`,
  /// then the `Name` property will be set accordingly. In the case of target=_blank,
  /// the `Name` property will be an empty string.
  /// Opening a window via ctrl+clicking a link would result in the `Name` property
  /// being set to an empty string.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Name([out, retval] LPWSTR* value);
}

/// The window features for a WebView popup window.  The fields match the
/// `windowFeatures` passed to `window.open` as specified in
/// [Window features](https://developer.mozilla.org/docs/Web/API/Window/open#Window_features)
/// on MDN.
///
/// There is no requirement for you to respect the values.  If your app does
/// not have corresponding UI features (for example, no toolbar) or if all
/// instance of WebView are opened in tabs and do not have distinct size or
/// positions, then your app does not respect the values.  You may want to
/// respect values, but perhaps only some apply to the UI of you app.
/// Accordingly, you may respect all, some, or none of the properties as
/// appropriate for your app.  For all numeric properties, if the value that is
/// passed to `window.open` is outside the range of an unsigned 32bit int, the
/// resulting value is the absolute value of the maximum for unsigned 32bit
/// integer.  If you are not able to parse the value an integer, it is
/// considered `0`.  If the value is a floating point value, it is rounded down
/// to an integer.
///
/// In runtime versions 98 or later, the values of `ShouldDisplayMenuBar`,
/// `ShouldDisplayStatus`, `ShouldDisplayToolbar`, and `ShouldDisplayScrollBars`
/// will not directly depend on the equivalent fields in the `windowFeatures`
/// string.  Instead, they will all be false if the window is expected to be a
/// popup, and true if it is not.
[uuid(5eaf559f-b46e-4397-8860-e422f287ff1e), object, pointer_default(unique)]
interface ICoreWebView2WindowFeatures : IUnknown {

  /// Specifies left and top values.

  [propget] HRESULT HasPosition([out, retval] BOOL* value);

  /// Specifies height and width values.

  [propget] HRESULT HasSize([out, retval] BOOL* value);

  /// Specifies the left position of the window.   If `HasPosition` is set to
  /// `FALSE`, this field is ignored.

  [propget] HRESULT Left([out, retval] UINT32* value);

  /// Specifies the top position of the window.   If `HasPosition` is set to
  /// `FALSE`, this field is ignored.

  [propget] HRESULT Top([out, retval] UINT32* value);

  /// Specifies the height of the window.  Minimum value is `100`.  If
  /// `HasSize` is set to `FALSE`, this field is ignored.

  [propget] HRESULT Height([out, retval] UINT32* value);

  /// Specifies the width of the window.  Minimum value is `100`.  If `HasSize`
  /// is set to `FALSE`, this field is ignored.

  [propget] HRESULT Width([out, retval] UINT32* value);

  /// Indicates that the menu bar is displayed.

  [propget] HRESULT ShouldDisplayMenuBar([out, retval] BOOL* value);

  /// Indicates that the status bar is displayed.

  [propget] HRESULT ShouldDisplayStatus([out, retval] BOOL* value);

  /// Indicates that the browser toolbar is displayed.

  [propget] HRESULT ShouldDisplayToolbar([out, retval] BOOL* value);

  /// Indicates that the scroll bars are displayed.

  [propget] HRESULT ShouldDisplayScrollBars([out, retval] BOOL* value);
}

/// Receives `NewWindowRequested` events.
[uuid(d4c185fe-c81c-4989-97af-2d3fa7ab5651), object, pointer_default(unique)]
interface ICoreWebView2NewWindowRequestedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.

  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] ICoreWebView2NewWindowRequestedEventArgs* args);
}

/// Receives `DocumentTitleChanged` events.  Use the `DocumentTitle` property
/// to get the modified title.

[uuid(f5f2b923-953e-4042-9f95-f3a118e1afd4), object, pointer_default(unique)]
interface ICoreWebView2DocumentTitleChangedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.  No event args exist
  /// and the `args` parameter is set to `null`.

  HRESULT Invoke([in] ICoreWebView2* sender, [in] IUnknown* args);
}

/// Event args for the `AcceleratorKeyPressed` event.

[uuid(9f760f8a-fb79-42be-9990-7b56900fa9c7), object, pointer_default(unique)]
interface ICoreWebView2AcceleratorKeyPressedEventArgs : IUnknown {

  /// The key event type that caused the event to run.

  [propget] HRESULT KeyEventKind([out, retval] COREWEBVIEW2_KEY_EVENT_KIND* keyEventKind);

  /// The Win32 virtual key code of the key that was pressed or released.  It
  /// is one of the Win32 virtual key constants such as `VK_RETURN` or an
  /// (uppercase) ASCII value such as `A`.  Verify whether Ctrl or Alt
  /// are pressed by running `GetKeyState(VK_CONTROL)` or
  /// `GetKeyState(VK_MENU)`.

  [propget] HRESULT VirtualKey([out, retval] UINT* virtualKey);

  /// The `LPARAM` value that accompanied the window message.  For more
  /// information, navigate to [WM_KEYDOWN](/windows/win32/inputdev/wm-keydown)
  /// and [WM_KEYUP](/windows/win32/inputdev/wm-keyup).

  [propget] HRESULT KeyEventLParam([out, retval] INT* lParam);

  /// A structure representing the information passed in the `LPARAM` of the
  /// window message.

  [propget] HRESULT PhysicalKeyStatus(
      [out, retval] COREWEBVIEW2_PHYSICAL_KEY_STATUS* physicalKeyStatus);

  /// During `AcceleratorKeyPressedEvent` handler invocation the WebView is
  /// blocked waiting for the decision of if the accelerator is handled by the
  /// host (or not).  If the `Handled` property is set to `TRUE` then this
  /// prevents the WebView from performing the default action for this
  /// accelerator key.  Otherwise the WebView performs the default action for
  /// the accelerator key.

  [propget] HRESULT Handled([out, retval] BOOL* handled);

  /// Sets the `Handled` property.

  [propput] HRESULT Handled([in] BOOL handled);
}

/// Receives `AcceleratorKeyPressed` events.

[uuid(b29c7e28-fa79-41a8-8e44-65811c76dcb2), object, pointer_default(unique)]
interface ICoreWebView2AcceleratorKeyPressedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.

  HRESULT Invoke(
      [in] ICoreWebView2Controller* sender,
      [in] ICoreWebView2AcceleratorKeyPressedEventArgs* args);
}

/// Receives `NewBrowserVersionAvailable` events.
[uuid(f9a2976e-d34e-44fc-adee-81b6b57ca914), object, pointer_default(unique)]
interface ICoreWebView2NewBrowserVersionAvailableEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.

  HRESULT Invoke([in] ICoreWebView2Environment* sender,
                 [in] IUnknown* args);
}

/// Receives `BrowserProcessExited` events.
[uuid(fa504257-a216-4911-a860-fe8825712861), object, pointer_default(unique)]
interface ICoreWebView2BrowserProcessExitedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.
  HRESULT Invoke(
      [in] ICoreWebView2Environment* sender,
      [in] ICoreWebView2BrowserProcessExitedEventArgs* args);
}

/// Receives `ContainsFullScreenElementChanged` events.

[uuid(e45d98b1-afef-45be-8baf-6c7728867f73), object, pointer_default(unique)]
interface ICoreWebView2ContainsFullScreenElementChangedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.  No event args exist
  /// and the `args` parameter is set to `null`.

  HRESULT Invoke([in] ICoreWebView2* sender, [in] IUnknown* args);
}

/// Receives `WindowCloseRequested` events.
[uuid(5c19e9e0-092f-486b-affa-ca8231913039), object, pointer_default(unique)]
interface ICoreWebView2WindowCloseRequestedEventHandler : IUnknown {

  /// Provides the event args for the corresponding event.  No event args exist
  /// and the `args` parameter is set to `null`.

  HRESULT Invoke([in] ICoreWebView2* sender, [in] IUnknown* args);
}

/// Receives `WebResourceResponseReceived` events.
[uuid(7DE9898A-24F5-40C3-A2DE-D4F458E69828), object, pointer_default(unique)]
interface ICoreWebView2WebResourceResponseReceivedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.
  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] ICoreWebView2WebResourceResponseReceivedEventArgs* args);
}

/// Event args for the `BrowserProcessExited` event.
[uuid(1f00663f-af8c-4782-9cdd-dd01c52e34cb), object, pointer_default(unique)]
interface ICoreWebView2BrowserProcessExitedEventArgs : IUnknown {
  /// The kind of browser process exit that has occurred.
  [propget] HRESULT BrowserProcessExitKind(
      [out, retval] COREWEBVIEW2_BROWSER_PROCESS_EXIT_KIND* browserProcessExitKind);

  /// The process ID of the browser process that has exited.
  [propget] HRESULT BrowserProcessId([out, retval] UINT32* value);
}

/// Event args for the WebResourceResponseReceived event.
[uuid(D1DB483D-6796-4B8B-80FC-13712BB716F4), object, pointer_default(unique)]
interface ICoreWebView2WebResourceResponseReceivedEventArgs : IUnknown {
  /// The request object for the web resource, as committed. This includes
  /// headers added by the network stack that were not be included during the
  /// associated WebResourceRequested event, such as Authentication headers.
  /// Modifications to this object have no effect on how the request is
  /// processed as it has already been sent.
  [propget] HRESULT Request([out, retval] ICoreWebView2WebResourceRequest** request);
  /// View of the response object received for the web resource.
  [propget] HRESULT Response([out, retval] ICoreWebView2WebResourceResponseView** response);
}

/// View of the HTTP representation for a web resource response. The properties
/// of this object are not mutable. This response view is used with the
/// WebResourceResponseReceived event.
[uuid(*************-4162-8F7D-F1B3F084928D), object, pointer_default(unique)]
interface ICoreWebView2WebResourceResponseView : IUnknown {
  /// The HTTP response headers as received.
  [propget] HRESULT Headers(
      [out, retval] ICoreWebView2HttpResponseHeaders** headers);
  /// The HTTP response status code.
  [propget] HRESULT StatusCode([out, retval] int* statusCode);
  /// The HTTP response reason phrase.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT ReasonPhrase([out, retval] LPWSTR* reasonPhrase);

  /// Get the response content asynchronously. The handler will receive the
  /// response content stream.
  ///
  /// This method returns null if content size is more than 123MB or for navigations that become downloads
  /// or if response is downloadable content type (e.g., application/octet-stream).
  /// See `add_DownloadStarting` event to handle the response.
  ///
  /// If this method is being called again before a first call has completed,
  /// the handler will be invoked at the same time the handlers from prior calls
  /// are invoked.
  /// If this method is being called after a first call has completed, the
  /// handler will be invoked immediately.
  /// \snippet ScenarioWebViewEventMonitor.cpp GetContent
  HRESULT GetContent(
      [in] ICoreWebView2WebResourceResponseViewGetContentCompletedHandler* handler);
}

/// Receives the result of the
/// `ICoreWebView2WebResourceResponseView::GetContent` method.
[uuid(875738E1-9FA2-40E3-8B74-2E8972DD6FE7), object, pointer_default(unique)]
interface ICoreWebView2WebResourceResponseViewGetContentCompletedHandler : IUnknown {
  /// Provides the completion status and result of the corresponding
  /// asynchronous method call. A failure `errorCode` will be passed if the
  /// content failed to load. `E_ABORT` means the response loading was blocked
  /// (e.g., by CORS policy); `ERROR_CANCELLED` means the response loading was
  /// cancelled. `ERROR_NO_DATA` means the response has no content data,
  /// `content` is `null` in this case. Note content (if any) is ignored for
  /// redirects, 204 No Content, 205 Reset Content, and HEAD-request responses.
  HRESULT Invoke([in] HRESULT errorCode, [in] IStream* content);
}

/// Event args for the DOMContentLoaded event.
[uuid(16B1E21A-C503-44F2-84C9-70ABA5031283), object, pointer_default(unique)]
interface ICoreWebView2DOMContentLoadedEventArgs : IUnknown {
  /// The ID of the navigation which corresponds to other navigation ID properties on other navigation events.
  [propget] HRESULT NavigationId([out, retval] UINT64* navigationId);
}

/// Receives `DOMContentLoaded` events.
[uuid(4BAC7E9C-199E-49ED-87ED-249303ACF019), object, pointer_default(unique)]
interface ICoreWebView2DOMContentLoadedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.
  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] ICoreWebView2DOMContentLoadedEventArgs* args);
}

/// Provides a set of properties that are used to manage an
/// ICoreWebView2Cookie.
///
/// \snippet ScenarioCookieManagement.cpp CookieObject
[uuid(AD26D6BE-1486-43E6-BF87-A2034006CA21), object, pointer_default(unique)]
interface ICoreWebView2Cookie : IUnknown {
  /// Cookie name.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Name([out, retval] LPWSTR* name);

  /// Cookie value.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Value([out, retval] LPWSTR* value);
  /// Set the cookie value property.
  [propput] HRESULT Value([in] LPCWSTR value);

  /// The domain for which the cookie is valid.
  /// The default is the host that this cookie has been received from.
  /// Note that, for instance, ".bing.com", "bing.com", and "www.bing.com" are
  /// considered different domains.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Domain([out, retval] LPWSTR* domain);

  /// The path for which the cookie is valid. The default is "/", which means
  /// this cookie will be sent to all pages on the Domain.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Path([out, retval] LPWSTR* path);

  /// The expiration date and time for the cookie as the number of seconds since the UNIX epoch.
  /// The default is -1.0, which means cookies are session cookies by default.
  [propget] HRESULT Expires([out, retval] double* expires);
  /// Set the Expires property. Cookies are session cookies and will not be
  /// persistent if Expires is set to -1.0. NaN, infinity, and any negative
  /// value set other than -1.0 is disallowed.
  [propput] HRESULT Expires([in] double expires);

  /// Whether this cookie is http-only.
  /// True if a page script or other active content cannot access this
  /// cookie. The default is false.
  [propget] HRESULT IsHttpOnly([out, retval] BOOL* isHttpOnly);
  /// Set the IsHttpOnly property.
  [propput] HRESULT IsHttpOnly([in] BOOL isHttpOnly);

  /// SameSite status of the cookie which represents the enforcement mode of the cookie.
  /// The default is COREWEBVIEW2_COOKIE_SAME_SITE_KIND_LAX.
  [propget] HRESULT SameSite([out, retval] COREWEBVIEW2_COOKIE_SAME_SITE_KIND* sameSite);
  /// Set the SameSite property.
  [propput] HRESULT SameSite([in] COREWEBVIEW2_COOKIE_SAME_SITE_KIND sameSite);

  /// The security level of this cookie. True if the client is only to return
  /// the cookie in subsequent requests if those requests use HTTPS.
  /// The default is false.
  /// Note that cookie that requests COREWEBVIEW2_COOKIE_SAME_SITE_KIND_NONE but
  /// is not marked Secure will be rejected.
  [propget] HRESULT IsSecure([out, retval] BOOL* isSecure);
  /// Set the IsSecure property.
  [propput] HRESULT IsSecure([in] BOOL isSecure);

  /// Whether this is a session cookie. The default is false.
  [propget] HRESULT IsSession([out, retval] BOOL* isSession);
}

/// Creates, adds or updates, gets, or or view the cookies. The changes would
/// apply to the context of the user profile. That is, other WebViews under the
/// same user profile could be affected.
[uuid(177CD9E7-B6F5-451A-94A0-5D7A3A4C4141), object, pointer_default(unique)]
interface ICoreWebView2CookieManager : IUnknown {
  /// Create a cookie object with a specified name, value, domain, and path.
  /// One can set other optional properties after cookie creation.
  /// This only creates a cookie object and it is not added to the cookie
  /// manager until you call AddOrUpdateCookie.
  /// Leading or trailing whitespace(s), empty string, and special characters
  /// are not allowed for name.
  /// See ICoreWebView2Cookie for more details.
  HRESULT CreateCookie(
    [in] LPCWSTR name,
    [in] LPCWSTR value,
    [in] LPCWSTR domain,
    [in] LPCWSTR path,
    [out, retval] ICoreWebView2Cookie** cookie);

  /// Creates a cookie whose params matches those of the specified cookie.
  HRESULT CopyCookie(
    [in] ICoreWebView2Cookie* cookieParam,
    [out, retval] ICoreWebView2Cookie** cookie);

  /// Gets a list of cookies matching the specific URI.
  /// If uri is empty string or null, all cookies under the same profile are
  /// returned.
  /// You can modify the cookie objects by calling
  /// ICoreWebView2CookieManager::AddOrUpdateCookie, and the changes
  /// will be applied to the webview.
  /// \snippet ScenarioCookieManagement.cpp GetCookies
  HRESULT GetCookies(
    [in] LPCWSTR uri,
    [in] ICoreWebView2GetCookiesCompletedHandler* handler);

  /// Adds or updates a cookie with the given cookie data; may overwrite
  /// cookies with matching name, domain, and path if they exist.
  /// This method will fail if the domain of the given cookie is not specified.
  /// \snippet ScenarioCookieManagement.cpp AddOrUpdateCookie
  HRESULT AddOrUpdateCookie([in] ICoreWebView2Cookie* cookie);

  /// Deletes a cookie whose name and domain/path pair
  /// match those of the specified cookie.
  HRESULT DeleteCookie([in] ICoreWebView2Cookie* cookie);

  /// Deletes cookies with matching name and uri.
  /// Cookie name is required.
  /// All cookies with the given name where domain
  /// and path match provided URI are deleted.
  HRESULT DeleteCookies([in] LPCWSTR name, [in] LPCWSTR uri);

  /// Deletes cookies with matching name and domain/path pair.
  /// Cookie name is required.
  /// If domain is specified, deletes only cookies with the exact domain.
  /// If path is specified, deletes only cookies with the exact path.
  HRESULT DeleteCookiesWithDomainAndPath([in] LPCWSTR name, [in] LPCWSTR domain, [in] LPCWSTR path);

  /// Deletes all cookies under the same profile.
  /// This could affect other WebViews under the same user profile.
  HRESULT DeleteAllCookies();
}

/// A list of cookie objects. See `ICoreWebView2Cookie`.
/// \snippet ScenarioCookieManagement.cpp GetCookies
[uuid(F7F6F714-5D2A-43C6-9503-346ECE02D186), object, pointer_default(unique)]
interface ICoreWebView2CookieList : IUnknown {
  /// The number of cookies contained in the ICoreWebView2CookieList.
  [propget] HRESULT Count([out, retval] UINT* count);

  /// Gets the cookie object at the given index.
  HRESULT GetValueAtIndex([in] UINT index, [out, retval] ICoreWebView2Cookie** cookie);
}

/// Receives the result of the `GetCookies` method.  The result is written to
/// the cookie list provided in the `GetCookies` method call.
[uuid(5A4F5069-5C15-47C3-8646-F4DE1C116670), object, pointer_default(unique)]
interface ICoreWebView2GetCookiesCompletedHandler : IUnknown {
  /// Provides the completion status of the corresponding asynchronous method
  /// call.
  HRESULT Invoke(HRESULT result, ICoreWebView2CookieList* cookieList);
}

/// Provides access to the certificate metadata
[uuid(e7188076-bcc3-11eb-8529-0242ac130003), object, pointer_default(unique)]
interface ICoreWebView2ClientCertificate : IUnknown {
  /// Subject of the certificate.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Subject([out, retval] LPWSTR* value);
  /// Name of the certificate authority that issued the certificate.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Issuer([out, retval] LPWSTR* value);
  /// The valid start date and time for the certificate as the number of seconds since
  /// the UNIX epoch.
  [propget] HRESULT ValidFrom([out, retval] double* value);
  /// The valid expiration date and time for the certificate as the number of seconds since
  /// the UNIX epoch.
  [propget] HRESULT ValidTo([out, retval] double* value);
  /// DER encoded serial number of the certificate.
  /// Read more about DER at [RFC 7468 DER]
  /// (https://tools.ietf.org/html/rfc7468#appendix-B).
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT DerEncodedSerialNumber([out, retval] LPWSTR* value);
  /// Display name for a certificate.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT DisplayName([out, retval] LPWSTR* value);
  /// PEM encoded data for the certificate.
  /// Returns Base64 encoding of DER encoded certificate.
  /// Read more about PEM at [RFC 1421 Privacy Enhanced Mail]
  /// (https://tools.ietf.org/html/rfc1421).
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  HRESULT ToPemEncoding([out, retval] LPWSTR* pemEncodedData);
  /// Collection of PEM encoded client certificate issuer chain.
  /// In this collection first element is the current certificate followed by
  /// intermediate1, intermediate2...intermediateN-1. Root certificate is the
  /// last element in collection.
  [propget] HRESULT PemEncodedIssuerCertificateChain([out, retval]
      ICoreWebView2StringCollection** value);
  /// Kind of a certificate (eg., smart card, pin, other).
  [propget] HRESULT Kind([out, retval]
      COREWEBVIEW2_CLIENT_CERTIFICATE_KIND* value);
}

/// A collection of client certificate object.
[uuid(ef5674d2-bcc3-11eb-8529-0242ac130003), object, pointer_default(unique)]
interface ICoreWebView2ClientCertificateCollection : IUnknown {
  /// The number of client certificates contained in the
  /// ICoreWebView2ClientCertificateCollection.
  [propget] HRESULT Count([out, retval] UINT* value);
  /// Gets the certificate object at the given index.
  HRESULT GetValueAtIndex([in] UINT index,
      [out, retval] ICoreWebView2ClientCertificate** certificate);
}

/// A collection of strings.
[uuid(f41f3f8a-bcc3-11eb-8529-0242ac130003), object, pointer_default(unique)]
interface ICoreWebView2StringCollection : IUnknown {
  /// The number of strings contained in ICoreWebView2StringCollection.
  [propget] HRESULT Count([out, retval] UINT* value);

  /// Gets the value at a given index.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  HRESULT GetValueAtIndex([in] UINT index, [out, retval] LPWSTR* value);
}

/// An event handler for the `ClientCertificateRequested` event.
[uuid(d7175ba2-bcc3-11eb-8529-0242ac130003), object, pointer_default(unique)]
interface ICoreWebView2ClientCertificateRequestedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.
  HRESULT Invoke([in] ICoreWebView2* sender,
      [in] ICoreWebView2ClientCertificateRequestedEventArgs* args);
}

/// Event args for the `ClientCertificateRequested` event.
[uuid(bc59db28-bcc3-11eb-8529-0242ac130003), object, pointer_default(unique)]
interface ICoreWebView2ClientCertificateRequestedEventArgs : IUnknown {
  /// Host name of the server that requested client certificate authentication.
  /// Normalization rules applied to the hostname are:
  /// * Convert to lowercase characters for ascii characters.
  /// * Punycode is used for representing non ascii characters.
  /// * Strip square brackets for IPV6 address.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Host([out, retval] LPWSTR* value);

  /// Port of the server that requested client certificate authentication.
  [propget] HRESULT Port([out, retval] int* value);

  /// Returns true if the server that issued this request is an http proxy.
  /// Returns false if the server is the origin server.
  [propget] HRESULT IsProxy([out, retval] BOOL* value);

  /// Returns the `ICoreWebView2StringCollection`.
  /// The collection contains distinguished names of certificate authorities
  /// allowed by the server.
  [propget] HRESULT AllowedCertificateAuthorities([out, retval]
      ICoreWebView2StringCollection** value);

  /// Returns the `ICoreWebView2ClientCertificateCollection` when client
  /// certificate authentication is requested. The collection contains mutually
  /// trusted CA certificates.
  [propget] HRESULT MutuallyTrustedCertificates([out, retval]
      ICoreWebView2ClientCertificateCollection** value);

  /// Returns the selected certificate.
  [propget] HRESULT SelectedCertificate([out, retval]
      ICoreWebView2ClientCertificate** value);

  /// Sets the certificate to respond to the server.
  [propput] HRESULT SelectedCertificate(
      [in] ICoreWebView2ClientCertificate* value);

  /// You may set this flag to cancel the certificate selection. If canceled,
  /// the request is aborted regardless of the `Handled` property. By default the
  /// value is `FALSE`.
  [propget] HRESULT Cancel([out, retval] BOOL* value);

  /// Sets the `Cancel` property.
  [propput] HRESULT Cancel([in] BOOL value);

  /// You may set this flag to `TRUE` to respond to the server with or
  /// without a certificate. If this flag is `TRUE` with a `SelectedCertificate`
  /// it responds to the server with the selected certificate otherwise respond to the
  /// server without a certificate. By default the value of `Handled` and `Cancel` are `FALSE`
  /// and display default client certificate selection dialog prompt to allow the user to
  /// choose a certificate. The `SelectedCertificate` is ignored unless `Handled` is set `TRUE`.
  [propget] HRESULT Handled([out, retval] BOOL* value);

  /// Sets the `Handled` property.
  [propput] HRESULT Handled([in] BOOL value);

  /// Returns an `ICoreWebView2Deferral` object. Use this operation to
  /// complete the event at a later time.
  HRESULT GetDeferral([out, retval] ICoreWebView2Deferral** deferral);
}

/// This mostly represents a combined win32
/// POINTER_INFO/POINTER_TOUCH_INFO/POINTER_PEN_INFO object. It takes fields
/// from all three and excludes some win32 specific data types like HWND and
/// HANDLE. Note, sourceDevice is taken out but we expect the PointerDeviceRect
/// and DisplayRect to cover the existing use cases of sourceDevice.
/// Another big difference is that any of the point or rect locations are
/// expected to be in WebView physical coordinates. That is, coordinates
/// relative to the WebView and no DPI scaling applied.
//
// The PointerId, PointerFlags, ButtonChangeKind, PenFlags, PenMask, TouchFlags,
// and TouchMask are all #defined flags or enums in the
// POINTER_INFO/POINTER_TOUCH_INFO/POINTER_PEN_INFO structure. We define those
// properties here as UINT32 or INT32 and expect the developer to know how to
// populate those values based on the Windows definitions.
[uuid(e6995887-d10d-4f5d-9359-4ce46e4f96b9), object, pointer_default(unique)]
interface ICoreWebView2PointerInfo : IUnknown {
  /// Get the PointerKind of the pointer event. This corresponds to the
  /// pointerKind property of the POINTER_INFO struct. The values are defined by
  /// the POINTER_INPUT_KIND enum in the Windows SDK (winuser.h). Supports
  /// PT_PEN and PT_TOUCH.
  [propget] HRESULT PointerKind([out, retval] DWORD* pointerKind);
  /// Set the PointerKind of the pointer event. This corresponds to the
  /// pointerKind property of the POINTER_INFO struct. The values are defined by
  /// the POINTER_INPUT_KIND enum in the Windows SDK (winuser.h). Supports
  /// PT_PEN and PT_TOUCH.
  [propput] HRESULT PointerKind([in] DWORD pointerKind);

  /// Get the PointerId of the pointer event. This corresponds to the pointerId
  /// property of the POINTER_INFO struct as defined in the Windows SDK
  /// (winuser.h).
  [propget] HRESULT PointerId([out, retval] UINT32* pointerId);
  /// Set the PointerId of the pointer event. This corresponds to the pointerId
  /// property of the POINTER_INFO struct as defined in the Windows SDK
  /// (winuser.h).
  [propput] HRESULT PointerId([in] UINT32 pointerId);

  /// Get the FrameID of the pointer event. This corresponds to the frameId
  /// property of the POINTER_INFO struct as defined in the Windows SDK
  /// (winuser.h).
  [propget] HRESULT FrameId([out, retval] UINT32* frameId);
  /// Set the FrameID of the pointer event. This corresponds to the frameId
  /// property of the POINTER_INFO struct as defined in the Windows SDK
  /// (winuser.h).
  [propput] HRESULT FrameId([in] UINT32 frameId);

  /// Get the PointerFlags of the pointer event. This corresponds to the
  /// pointerFlags property of the POINTER_INFO struct. The values are defined
  /// by the POINTER_FLAGS constants in the Windows SDK (winuser.h).
  [propget] HRESULT PointerFlags([out, retval] UINT32* pointerFlags);
  /// Set the PointerFlags of the pointer event. This corresponds to the
  /// pointerFlags property of the POINTER_INFO struct. The values are defined
  /// by the POINTER_FLAGS constants in the Windows SDK (winuser.h).
  [propput] HRESULT PointerFlags([in] UINT32 pointerFlags);

  /// Get the PointerDeviceRect of the sourceDevice property of the
  /// POINTER_INFO struct as defined in the Windows SDK (winuser.h).
  [propget] HRESULT PointerDeviceRect([out, retval] RECT* pointerDeviceRect);
  /// Set the PointerDeviceRect of the sourceDevice property of the
  /// POINTER_INFO struct as defined in the Windows SDK (winuser.h).
  [propput] HRESULT PointerDeviceRect([in] RECT pointerDeviceRect);

  /// Get the DisplayRect of the sourceDevice property of the POINTER_INFO
  /// struct as defined in the Windows SDK (winuser.h).
  [propget] HRESULT DisplayRect([out, retval] RECT* displayRect);
  /// Set the DisplayRect of the sourceDevice property of the POINTER_INFO
  /// struct as defined in the Windows SDK (winuser.h).
  [propput] HRESULT DisplayRect([in] RECT displayRect);

  /// Get the PixelLocation of the pointer event. This corresponds to the
  /// ptPixelLocation property of the POINTER_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propget] HRESULT PixelLocation([out, retval] POINT* pixelLocation);
  /// Set the PixelLocation of the pointer event. This corresponds to the
  /// ptPixelLocation property of the POINTER_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propput] HRESULT PixelLocation([in] POINT pixelLocation);

  /// Get the HimetricLocation of the pointer event. This corresponds to the
  /// ptHimetricLocation property of the POINTER_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propget] HRESULT HimetricLocation([out, retval] POINT* himetricLocation);
  /// Set the HimetricLocation of the pointer event. This corresponds to the
  /// ptHimetricLocation property of the POINTER_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propput] HRESULT HimetricLocation([in] POINT himetricLocation);

  /// Get the PixelLocationRaw of the pointer event. This corresponds to the
  /// ptPixelLocationRaw property of the POINTER_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propget] HRESULT PixelLocationRaw([out, retval] POINT* pixelLocationRaw);
  /// Set the PixelLocationRaw of the pointer event. This corresponds to the
  /// ptPixelLocationRaw property of the POINTER_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propput] HRESULT PixelLocationRaw([in] POINT pixelLocationRaw);

  /// Get the HimetricLocationRaw of the pointer event. This corresponds to the
  /// ptHimetricLocationRaw property of the POINTER_INFO struct as defined in
  /// the Windows SDK (winuser.h).
  [propget] HRESULT HimetricLocationRaw([out, retval] POINT* himetricLocationRaw);
  /// Set the HimetricLocationRaw of the pointer event. This corresponds to the
  /// ptHimetricLocationRaw property of the POINTER_INFO struct as defined in
  /// the Windows SDK (winuser.h).
  [propput] HRESULT HimetricLocationRaw([in] POINT himetricLocationRaw);

  /// Get the Time of the pointer event. This corresponds to the dwTime property
  /// of the POINTER_INFO struct as defined in the Windows SDK (winuser.h).
  [propget] HRESULT Time([out, retval] DWORD* time);
  /// Set the Time of the pointer event. This corresponds to the dwTime property
  /// of the POINTER_INFO struct as defined in the Windows SDK (winuser.h).
  [propput] HRESULT Time([in] DWORD time);

  /// Get the HistoryCount of the pointer event. This corresponds to the
  /// historyCount property of the POINTER_INFO struct as defined in the Windows
  /// SDK (winuser.h).
  [propget] HRESULT HistoryCount([out, retval] UINT32* historyCount);
  /// Set the HistoryCount of the pointer event. This corresponds to the
  /// historyCount property of the POINTER_INFO struct as defined in the Windows
  /// SDK (winuser.h).
  [propput] HRESULT HistoryCount([in] UINT32 historyCount);

  /// Get the InputData of the pointer event. This corresponds to the InputData
  /// property of the POINTER_INFO struct as defined in the Windows SDK
  /// (winuser.h).
  [propget] HRESULT InputData([out, retval] INT32* inputData);
  /// Set the InputData of the pointer event. This corresponds to the InputData
  /// property of the POINTER_INFO struct as defined in the Windows SDK
  /// (winuser.h).
  [propput] HRESULT InputData([in] INT32 inputData);

  /// Get the KeyStates of the pointer event. This corresponds to the
  /// dwKeyStates property of the POINTER_INFO struct as defined in the Windows
  /// SDK (winuser.h).
  [propget] HRESULT KeyStates([out, retval] DWORD* keyStates);
  /// Set the KeyStates of the pointer event. This corresponds to the
  /// dwKeyStates property of the POINTER_INFO struct as defined in the Windows
  /// SDK (winuser.h).
  [propput] HRESULT KeyStates([in] DWORD keyStates);

  /// Get the PerformanceCount of the pointer event. This corresponds to the
  /// PerformanceCount property of the POINTER_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propget] HRESULT PerformanceCount([out, retval] UINT64* performanceCount);
  /// Set the PerformanceCount of the pointer event. This corresponds to the
  /// PerformanceCount property of the POINTER_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propput] HRESULT PerformanceCount([in] UINT64 performanceCount);

  /// Get the ButtonChangeKind of the pointer event. This corresponds to the
  /// ButtonChangeKind property of the POINTER_INFO struct. The values are
  /// defined by the POINTER_BUTTON_CHANGE_KIND enum in the Windows SDK
  /// (winuser.h).
  [propget] HRESULT ButtonChangeKind([out, retval] INT32* buttonChangeKind);
  /// Set the ButtonChangeKind of the pointer event. This corresponds to the
  /// ButtonChangeKind property of the POINTER_INFO struct. The values are
  /// defined by the POINTER_BUTTON_CHANGE_KIND enum in the Windows SDK
  /// (winuser.h).
  [propput] HRESULT ButtonChangeKind([in] INT32 buttonChangeKind);

  // Pen specific attributes

  /// Get the PenFlags of the pointer event. This corresponds to the penFlags
  /// property of the POINTER_PEN_INFO struct. The values are defined by the
  /// PEN_FLAGS constants in the Windows SDK (winuser.h).
  [propget] HRESULT PenFlags([out, retval] UINT32* penFLags);
  /// Set the PenFlags of the pointer event. This corresponds to the penFlags
  /// property of the POINTER_PEN_INFO struct. The values are defined by the
  /// PEN_FLAGS constants in the Windows SDK (winuser.h).
  [propput] HRESULT PenFlags([in] UINT32 penFLags);

  /// Get the PenMask of the pointer event. This corresponds to the penMask
  /// property of the POINTER_PEN_INFO struct. The values are defined by the
  /// PEN_MASK constants in the Windows SDK (winuser.h).
  [propget] HRESULT PenMask([out, retval] UINT32* penMask);
  /// Set the PenMask of the pointer event. This corresponds to the penMask
  /// property of the POINTER_PEN_INFO struct. The values are defined by the
  /// PEN_MASK constants in the Windows SDK (winuser.h).
  [propput] HRESULT PenMask([in] UINT32 penMask);

  /// Get the PenPressure of the pointer event. This corresponds to the pressure
  /// property of the POINTER_PEN_INFO struct as defined in the Windows SDK
  /// (winuser.h).
  [propget] HRESULT PenPressure([out, retval] UINT32* penPressure);
  /// Set the PenPressure of the pointer event. This corresponds to the pressure
  /// property of the POINTER_PEN_INFO struct as defined in the Windows SDK
  /// (winuser.h).
  [propput] HRESULT PenPressure([in] UINT32 penPressure);

  /// Get the PenRotation of the pointer event. This corresponds to the rotation
  /// property of the POINTER_PEN_INFO struct as defined in the Windows SDK
  /// (winuser.h).
  [propget] HRESULT PenRotation([out, retval] UINT32* penRotation);
  /// Set the PenRotation of the pointer event. This corresponds to the rotation
  /// property of the POINTER_PEN_INFO struct as defined in the Windows SDK
  /// (winuser.h).
  [propput] HRESULT PenRotation([in] UINT32 penRotation);

  /// Get the PenTiltX of the pointer event. This corresponds to the tiltX
  /// property of the POINTER_PEN_INFO struct as defined in the Windows SDK
  /// (winuser.h).
  [propget] HRESULT PenTiltX([out, retval] INT32* penTiltX);
  /// Set the PenTiltX of the pointer event. This corresponds to the tiltX
  /// property of the POINTER_PEN_INFO struct as defined in the Windows SDK
  /// (winuser.h).
  [propput] HRESULT PenTiltX([in] INT32 penTiltX);

  /// Get the PenTiltY of the pointer event. This corresponds to the tiltY
  /// property of the POINTER_PEN_INFO struct as defined in the Windows SDK
  /// (winuser.h).
  [propget] HRESULT PenTiltY([out, retval] INT32* penTiltY);
  /// Set the PenTiltY of the pointer event. This corresponds to the tiltY
  /// property of the POINTER_PEN_INFO struct as defined in the Windows SDK
  /// (winuser.h).
  [propput] HRESULT PenTiltY([in] INT32 penTiltY);

  // Touch specific attributes

  /// Get the TouchFlags of the pointer event. This corresponds to the
  /// touchFlags property of the POINTER_TOUCH_INFO struct. The values are
  /// defined by the TOUCH_FLAGS constants in the Windows SDK (winuser.h).
  [propget] HRESULT TouchFlags([out, retval] UINT32* touchFlags);
  /// Set the TouchFlags of the pointer event. This corresponds to the
  /// touchFlags property of the POINTER_TOUCH_INFO struct. The values are
  /// defined by the TOUCH_FLAGS constants in the Windows SDK (winuser.h).
  [propput] HRESULT TouchFlags([in] UINT32 touchFlags);

  /// Get the TouchMask of the pointer event. This corresponds to the
  /// touchMask property of the POINTER_TOUCH_INFO struct. The values are
  /// defined by the TOUCH_MASK constants in the Windows SDK (winuser.h).
  [propget] HRESULT TouchMask([out, retval] UINT32* touchMask);
  /// Set the TouchMask of the pointer event. This corresponds to the
  /// touchMask property of the POINTER_TOUCH_INFO struct. The values are
  /// defined by the TOUCH_MASK constants in the Windows SDK (winuser.h).
  [propput] HRESULT TouchMask([in] UINT32 touchMask);

  /// Get the TouchContact of the pointer event. This corresponds to the
  /// rcContact property of the POINTER_TOUCH_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propget] HRESULT TouchContact([out, retval] RECT* touchContact);
  /// Set the TouchContact of the pointer event. This corresponds to the
  /// rcContact property of the POINTER_TOUCH_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propput] HRESULT TouchContact([in] RECT touchContact);

  /// Get the TouchContactRaw of the pointer event. This corresponds to the
  /// rcContactRaw property of the POINTER_TOUCH_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propget] HRESULT TouchContactRaw([out, retval] RECT* touchContactRaw);
  /// Set the TouchContactRaw of the pointer event. This corresponds to the
  /// rcContactRaw property of the POINTER_TOUCH_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propput] HRESULT TouchContactRaw([in] RECT touchContactRaw);

  /// Get the TouchOrientation of the pointer event. This corresponds to the
  /// orientation property of the POINTER_TOUCH_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propget] HRESULT TouchOrientation([out, retval] UINT32* touchOrientation);
  /// Set the TouchOrientation of the pointer event. This corresponds to the
  /// orientation property of the POINTER_TOUCH_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propput] HRESULT TouchOrientation([in] UINT32 touchOrientation);

  /// Get the TouchPressure of the pointer event. This corresponds to the
  /// pressure property of the POINTER_TOUCH_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propget] HRESULT TouchPressure([out, retval] UINT32* touchPressure);
  /// Set the TouchPressure of the pointer event. This corresponds to the
  /// pressure property of the POINTER_TOUCH_INFO struct as defined in the
  /// Windows SDK (winuser.h).
  [propput] HRESULT TouchPressure([in] UINT32 touchPressure);
}

/// The caller implements this interface to receive CursorChanged events. Use
/// the Cursor property to get the new cursor.
[uuid(9da43ccc-26e1-4dad-b56c-d8961c94c571), object, pointer_default(unique)]
interface ICoreWebView2CursorChangedEventHandler : IUnknown {
  /// Called to provide the implementer with the event args for the
  /// corresponding event. There are no event args and the args
  /// parameter will be null.
  HRESULT Invoke([in] ICoreWebView2CompositionController* sender, [in] IUnknown* args);
}

/// Receives `RasterizationScaleChanged` events.  Use the `RasterizationScale`
/// property to get the modified scale.

[uuid(9c98c8b1-ac53-427e-a345-3049b5524bbe), object, pointer_default(unique)]
interface ICoreWebView2RasterizationScaleChangedEventHandler : IUnknown {
  /// Called to provide the implementer with the event args for the
  /// corresponding event. There are no event args and the args
  /// parameter will be null.
  HRESULT Invoke(
    [in] ICoreWebView2Controller* sender,
    [in] IUnknown* args);
}

/// Represents the WebView2 Environment.  WebViews created from an environment
/// run on the browser process specified with environment parameters and
/// objects created from an environment should be used in the same
/// environment.  Using it in different environments are not guaranteed to be
///  compatible and may fail.

[uuid(b96d755e-0319-4e92-a296-23436f46a1fc), object, pointer_default(unique)]
interface ICoreWebView2Environment : IUnknown {

  /// Asynchronously create a new WebView.
  ///
  /// `parentWindow` is the `HWND` in which the WebView should be displayed and
  /// from which receive input.  The WebView adds a child window to the
  /// provided window before this function returns.  Z-order and other things
  /// impacted by sibling window order are affected accordingly.  If you want to
  /// move the WebView to a different parent after it has been created, you must
  /// call put_ParentWindow to update tooltip positions, accessibility trees,
  /// and such.
  ///
  /// HWND_MESSAGE is a valid parameter for `parentWindow` for an invisible
  /// WebView for Windows 8 and above. In this case the window will never
  /// become visible. You are not able to reparent the window after you have
  /// created the WebView.  This is not supported in Windows 7 or below.
  /// Passing this parameter in Windows 7 or below will return
  /// ERROR_INVALID_WINDOW_HANDLE in the controller callback.
  ///
  /// It is recommended that the app set Application User Model ID for the
  /// process or the app window.  If none is set, during WebView creation a
  /// generated Application User Model ID is set to root window of
  /// `parentWindow`.
  ///
  /// \snippet AppWindow.cpp CreateCoreWebView2Controller
  ///
  /// It is recommended that the app handles restart manager messages, to
  /// gracefully restart it in the case when the app is using the WebView2
  /// Runtime from a certain installation and that installation is being
  /// uninstalled.  For example, if a user installs a version of the WebView2
  /// Runtime and opts to use another version of the WebView2 Runtime for
  /// testing the app, and then uninstalls the 1st version of the WebView2
  /// Runtime without closing the app, the app restarts to allow
  /// un-installation to succeed.
  ///
  /// \snippet AppWindow.cpp RestartManager
  ///
  /// The app should retry `CreateCoreWebView2Controller` upon failure, unless the
  /// error code is `HRESULT_FROM_WIN32(ERROR_INVALID_STATE)`.
  /// When the app retries `CreateCoreWebView2Controller` upon failure, it is
  /// recommended that the app restarts from creating a new WebView2
  /// Environment.  If a WebView2 Runtime update happens, the version
  /// associated with a WebView2 Environment may have been removed and causing
  /// the object to no longer work.  Creating a new WebView2 Environment works
  /// since it uses the latest version.
  ///
  /// WebView creation fails with `HRESULT_FROM_WIN32(ERROR_INVALID_STATE)` if a
  /// running instance using the same user data folder exists, and the Environment
  /// objects have different `EnvironmentOptions`.  For example, if a WebView was
  /// created with one language, an attempt to create a WebView with a different
  /// language using the same user data folder will fail.
  ///
  /// The creation will fail with `E_ABORT` if `parentWindow` is destroyed
  /// before the creation is finished.  If this is caused by a call to
  /// `DestroyWindow`, the creation completed handler will be invoked before
  /// `DestroyWindow` returns, so you can use this to cancel creation and clean
  /// up resources synchronously when quitting a thread.

  HRESULT CreateCoreWebView2Controller(
    HWND parentWindow,
    ICoreWebView2CreateCoreWebView2ControllerCompletedHandler* handler);

  /// Create a new web resource response object.  The `headers` parameter is
  /// the raw response header string delimited by newline.  It is also possible
  /// to create this object with null headers string and then use the
  /// `ICoreWebView2HttpResponseHeaders` to construct the headers line by line.
  /// For more information about other parameters, navigate to
  /// [ICoreWebView2WebResourceResponse](/microsoft-edge/webview2/reference/win32/icorewebview2webresourceresponse).
  ///
  /// \snippet SettingsComponent.cpp WebResourceRequested0
  /// \snippet SettingsComponent.cpp WebResourceRequested1
  HRESULT CreateWebResourceResponse(
    [in] IStream* content,
    [in] int statusCode,
    [in] LPCWSTR reasonPhrase,
    [in] LPCWSTR headers,
    [out, retval] ICoreWebView2WebResourceResponse** response);

  /// The browser version info of the current `ICoreWebView2Environment`,
  /// including channel name if it is not the WebView2 Runtime.  It matches the
  /// format of the `GetAvailableCoreWebView2BrowserVersionString` API.
  /// Channel names are `beta`, `dev`, and `canary`.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  ///
  /// \snippet AppWindow.cpp GetBrowserVersionString
  [propget] HRESULT BrowserVersionString([out, retval] LPWSTR* versionInfo);

  /// Add an event handler for the `NewBrowserVersionAvailable` event.
  /// `NewBrowserVersionAvailable` runs when a newer version of the WebView2
  /// Runtime is installed and available using WebView2.  To use the newer
  /// version of the browser you must create a new environment and WebView.
  /// The event only runs for new version from the same WebView2 Runtime from
  /// which the code is running. When not running with installed WebView2
  /// Runtime, no event is run.
  ///
  /// Because a user data folder is only able to be used by one browser
  /// process at a time, if you want to use the same user data folder in the
  /// WebView using the new version of the browser, you must close the
  /// environment and instance of WebView that are using the older version of
  /// the browser first.  Or simply prompt the user to restart the app.
  ///
  /// \snippet AppWindow.cpp NewBrowserVersionAvailable
  HRESULT add_NewBrowserVersionAvailable(
      [in] ICoreWebView2NewBrowserVersionAvailableEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_NewBrowserVersionAvailable`.
  HRESULT remove_NewBrowserVersionAvailable(
      [in] EventRegistrationToken token);
}

/// A continuation of the ICoreWebView2Environment interface.
[uuid(41F3632B-5EF4-404F-AD82-2D606C5A9A21), object, pointer_default(unique)]
interface ICoreWebView2Environment2 : ICoreWebView2Environment {
  /// Create a new web resource request object.
  /// URI parameter must be absolute URI.
  /// The headers string is the raw request header string delimited by CRLF
  /// (optional in last header).
  /// It's also possible to create this object with null headers string
  /// and then use the ICoreWebView2HttpRequestHeaders to construct the headers
  /// line by line.
  /// For information on other parameters see ICoreWebView2WebResourceRequest.
  ///
  /// \snippet ScenarioNavigateWithWebResourceRequest.cpp NavigateWithWebResourceRequest
  HRESULT CreateWebResourceRequest([in] LPCWSTR uri,
                                   [in] LPCWSTR method,
                                   [in] IStream* postData,
                                   [in] LPCWSTR headers,
                                   [out, retval] ICoreWebView2WebResourceRequest** request);
}

/// A continuation of the ICoreWebView2Environment2 interface.
[uuid(80a22ae3-be7c-4ce2-afe1-5a50056cdeeb), object, pointer_default(unique)]
interface ICoreWebView2Environment3 : ICoreWebView2Environment2 {
  /// Asynchronously create a new WebView for use with visual hosting.
  ///
  /// parentWindow is the HWND in which the app will connect the visual tree of
  /// the WebView. This will be the HWND that the app will receive pointer/
  /// mouse input meant for the WebView (and will need to use SendMouseInput/
  /// SendPointerInput to forward). If the app moves the WebView visual tree to
  /// underneath a different window, then it needs to call put_ParentWindow to
  /// update the new parent HWND of the visual tree.
  ///
  /// HWND_MESSAGE is not a valid parameter for `parentWindow` for visual hosting.
  /// The underlying implementation of supporting HWND_MESSAGE would break
  /// accessibility for visual hosting. This is supported in windowed
  /// WebViews - see CreateCoreWebView2Controller.
  ///
  /// Use put_RootVisualTarget on the created CoreWebView2CompositionController to
  /// provide a visual to host the browser's visual tree.
  ///
  /// It is recommended that the application set Application User Model ID for
  /// the process or the application window. If none is set, during WebView
  /// creation a generated Application User Model ID is set to root window of
  /// parentWindow.
  /// \snippet AppWindow.cpp CreateCoreWebView2Controller
  ///
  /// It is recommended that the application handles restart manager messages
  /// so that it can be restarted gracefully in the case when the app is using
  /// Edge for WebView from a certain installation and that installation is
  /// being uninstalled. For example, if a user installs Edge from Dev channel
  /// and opts to use Edge from that channel for testing the app, and then
  /// uninstalls Edge from that channel without closing the app, the app will
  /// be restarted to allow uninstallation of the dev channel to succeed.
  /// \snippet AppWindow.cpp RestartManager
  ///
  /// The app should retry `CreateCoreWebView2CompositionController` upon failure,
  /// unless the error code is `HRESULT_FROM_WIN32(ERROR_INVALID_STATE)`.
  /// When the app retries `CreateCoreWebView2CompositionController`
  /// upon failure, it is recommended that the app restarts from creating a new
  /// WebView2 Environment.  If a WebView2 Runtime update happens, the version
  /// associated with a WebView2 Environment may have been removed and causing
  /// the object to no longer work.  Creating a new WebView2 Environment works
  /// since it uses the latest version.
  ///
  /// WebView creation fails with `HRESULT_FROM_WIN32(ERROR_INVALID_STATE)` if a
  /// running instance using the same user data folder exists, and the Environment
  /// objects have different `EnvironmentOptions`.  For example, if a WebView was
  /// created with one language, an attempt to create a WebView with a different
  /// language using the same user data folder will fail.
  ///
  /// The creation will fail with `E_ABORT` if `parentWindow` is destroyed
  /// before the creation is finished.  If this is caused by a call to
  /// `DestroyWindow`, the creation completed handler will be invoked before
  /// `DestroyWindow` returns, so you can use this to cancel creation and clean
  /// up resources synchronously when quitting a thread.
  ///
  /// CreateCoreWebView2CompositionController is supported in the following versions of Windows:
  ///
  /// - Windows 11
  /// - Windows 10
  /// - Windows Server 2019
  /// - Windows Server 2016
  ///
  HRESULT CreateCoreWebView2CompositionController(
      HWND parentWindow,
      ICoreWebView2CreateCoreWebView2CompositionControllerCompletedHandler* handler);

  /// Create an empty ICoreWebView2PointerInfo. The returned
  /// ICoreWebView2PointerInfo needs to be populated with all of the relevant
  /// info before calling SendPointerInput.
  HRESULT CreateCoreWebView2PointerInfo(
    [out, retval] ICoreWebView2PointerInfo** pointerInfo);
}

/// A continuation of the ICoreWebView2Environment3 interface.
[uuid(20944379-6dcf-41d6-a0a0-abc0fc50de0d), object, pointer_default(unique)]
interface ICoreWebView2Environment4 : ICoreWebView2Environment3 {
  /// Returns the Automation Provider for the WebView that matches the provided
  /// window. Host apps are expected to implement
  /// IRawElementProviderHwndOverride. When GetOverrideProviderForHwnd is
  /// called, the app can pass the HWND to GetAutomationProviderForWindow to
  /// find the matching WebView Automation Provider.
  HRESULT GetAutomationProviderForWindow([in] HWND hwnd,
                                         [out, retval] IUnknown** provider);
}

/// A continuation of the `ICoreWebView2Environment4` interface that supports
/// the `BrowserProcessExited` event.
[uuid(319e423d-e0d7-4b8d-9254-ae9475de9b17), object, pointer_default(unique)]
interface ICoreWebView2Environment5 : ICoreWebView2Environment4 {
  /// Add an event handler for the `BrowserProcessExited` event.
  /// The `BrowserProcessExited` event is raised when the collection of WebView2
  /// Runtime processes for the browser process of this environment terminate
  /// due to browser process failure or normal shutdown (for example, when all
  /// associated WebViews are closed), after all resources have been released
  /// (including the user data folder). To learn about what these processes are,
  /// go to [Process model](/microsoft-edge/webview2/concepts/process-model).
  ///
  /// A handler added with this method is called until removed with
  /// `remove_BrowserProcessExited`, even if a new browser process is bound to
  /// this environment after earlier `BrowserProcessExited` events are raised.
  ///
  /// Multiple app processes can share a browser process by creating their webviews
  /// from a `ICoreWebView2Environment` with the same user data folder. When the entire
  /// collection of WebView2Runtime processes for the browser process exit, all
  /// associated `ICoreWebView2Environment` objects receive the `BrowserProcessExited`
  /// event. Multiple processes sharing the same browser process need to coordinate
  /// their use of the shared user data folder to avoid race conditions and
  /// unnecessary waits. For example, one process should not clear the user data
  /// folder at the same time that another process recovers from a crash by recreating
  /// its WebView controls; one process should not block waiting for the event if
  /// other app processes are using the same browser process (the browser process will
  /// not exit until those other processes have closed their webviews too).
  ///
  /// Note this is an event from the `ICoreWebView2Environment3` interface, not
  /// the `ICoreWebView2` one. The difference between `BrowserProcessExited` and
  /// `ICoreWebView2`'s `ProcessFailed` is that `BrowserProcessExited` is
  /// raised for any **browser process** exit (expected or unexpected, after all
  /// associated processes have exited too), while `ProcessFailed` is raised for
  /// **unexpected** process exits of any kind (browser, render, GPU, and all
  /// other types), or for main frame **render process** unresponsiveness. To
  /// learn more about the WebView2 Process Model, go to
  /// [Process model](/microsoft-edge/webview2/concepts/process-model).
  ///
  /// In the case the browser process crashes, both `BrowserProcessExited` and
  /// `ProcessFailed` events are raised, but the order is not guaranteed. These
  /// events are intended for different scenarios. It is up to the app to
  /// coordinate the handlers so they do not try to perform reliability recovery
  /// while also trying to move to a new WebView2 Runtime version or remove the
  /// user data folder.
  ///
  /// \snippet AppWindow.cpp Close
  HRESULT add_BrowserProcessExited(
      [in] ICoreWebView2BrowserProcessExitedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_BrowserProcessExited`.
  HRESULT remove_BrowserProcessExited([in] EventRegistrationToken token);
}

/// This interface is an extension of the ICoreWebView2Environment that supports
/// creating print settings for printing to PDF.
[uuid(e59ee362-acbd-4857-9a8e-d3644d9459a9), object, pointer_default(unique)]
interface ICoreWebView2Environment6 : ICoreWebView2Environment5 {
    /// Creates the `ICoreWebView2lPrintSettings` used by the `PrintToPdf`
    /// method.
    HRESULT CreatePrintSettings(
        [out, retval] ICoreWebView2PrintSettings** printSettings);
}

/// This interface is an extension of the ICoreWebView2Environment. An object
/// implementing the ICoreWebView2Environment7 interface will also
/// implement ICoreWebView2Environment.
[uuid(43C22296-3BBD-43A4-9C00-5C0DF6DD29A2), object, pointer_default(unique)]
interface ICoreWebView2Environment7 : ICoreWebView2Environment6 {
  /// Returns the user data folder that all CoreWebView2's created from this
  /// environment are using.
  /// This could be either the value passed in by the developer when creating
  /// the environment object or the calculated one for default handling.  It
  /// will always be an absolute path.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  ///
  /// \snippet AppWindow.cpp GetUserDataFolder
  [propget] HRESULT UserDataFolder([ out, retval ] LPWSTR * value);
}

/// A continuation of the `ICoreWebView2Environment7` interface that supports
/// the `ProcessInfosChanged` event.
[uuid(D6EB91DD-C3D2-45E5-BD29-6DC2BC4DE9CF), object, pointer_default(unique)]
interface ICoreWebView2Environment8 : ICoreWebView2Environment7 {
  /// Adds an event handler for the `ProcessInfosChanged` event.
  ///
  /// \snippet ProcessComponent.cpp ProcessInfosChanged
  /// \snippet ProcessComponent.cpp ProcessInfosChanged1
  HRESULT add_ProcessInfosChanged(
      [in] ICoreWebView2ProcessInfosChangedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_ProcessInfosChanged`.
  HRESULT remove_ProcessInfosChanged(
      [in] EventRegistrationToken token);

  /// Returns the `ICoreWebView2ProcessInfoCollection`
  /// Provide a list of all process using same user data folder except for crashpad process.
  HRESULT GetProcessInfos([out, retval]ICoreWebView2ProcessInfoCollection** value);
}

/// Provides a set of properties for a process in the `ICoreWebView2Environment`.
[uuid(84FA7612-3F3D-4FBF-889D-FAD000492D72), object, pointer_default(unique)]
interface ICoreWebView2ProcessInfo : IUnknown {

  /// The process id of the process.
  [propget] HRESULT ProcessId([out, retval] INT32* value);

  /// The kind of the process.
  [propget] HRESULT Kind([out, retval] COREWEBVIEW2_PROCESS_KIND* kind);
}

/// A continuation of the ICoreWebView2Environment interface for
/// creating CoreWebView2 ContextMenuItem objects.
[uuid(f06f41bf-4b5a-49d8-b9f6-fa16cd29f274), object, pointer_default(unique)]
interface ICoreWebView2Environment9 : ICoreWebView2Environment8 {
  /// Create a custom `ContextMenuItem` object to insert into the WebView context menu.
  /// CoreWebView2 will rewind the icon stream before decoding.
  /// There is a limit of 1000 active custom context menu items at a given time.
  /// Attempting to create more before deleting existing ones will fail with
  /// ERROR_NOT_ENOUGH_QUOTA.
  /// It is recommended to reuse ContextMenuItems across ContextMenuRequested events
  /// for performance.
  /// The returned ContextMenuItem object's `IsEnabled` property will default to `TRUE`
  /// and `IsChecked` property will default to `FALSE`. A `CommandId` will be assigned
  /// to the ContextMenuItem object that's unique across active custom context menu items,
  /// but command ID values of deleted ContextMenuItems can be reassigned.
  HRESULT CreateContextMenuItem(
      [in] LPCWSTR label,
      [in] IStream* iconStream,
      [in] COREWEBVIEW2_CONTEXT_MENU_ITEM_KIND kind,
      [out, retval] ICoreWebView2ContextMenuItem** item);
}

/// This interface is used to create `ICoreWebView2ControllerOptions` object, which
/// can be passed as a parameter in `CreateCoreWebView2ControllerWithOptions` and
/// `CreateCoreWebView2CompositionControllerWithOptions` function for multiple profiles support.
/// The profile will be created on disk or opened when calling `CreateCoreWebView2ControllerWithOptions` or
/// `CreateCoreWebView2CompositionControllerWithOptions` no matter InPrivate mode is enabled or not, and
/// it will be released in memory when the corresponding controller is closed but still remain on disk.
/// If you create a WebView2Controller with {ProfileName="name", InPrivate=false} and then later create another
/// one with {ProfileName="name", InPrivate=true}, these two controllers using the same profile would be allowed to
/// run at the same time.
/// As WebView2 is built on top of Edge browser, it follows Edge's behavior pattern. To create an InPrivate WebView,
/// we gets an off-the-record profile (an InPrivate profile) from a regular profile, then create the WebView with the
/// off-the-record profile.
///
/// \snippet AppWindow.cpp CreateControllerWithOptions
[uuid(ee0eb9df-6f12-46ce-b53f-3f47b9c928e0), object, pointer_default(unique)]
interface ICoreWebView2Environment10 : ICoreWebView2Environment9 {
  /// Create a new ICoreWebView2ControllerOptions to be passed as a parameter of
  /// CreateCoreWebView2ControllerWithOptions and CreateCoreWebView2CompositionControllerWithOptions.
  /// The 'options' is settable and in it the default value for profile name is the empty string,
  /// and the default value for IsInPrivateModeEnabled is false.
  /// Also the profile name can be reused.
  HRESULT CreateCoreWebView2ControllerOptions(
      [out, retval] ICoreWebView2ControllerOptions** options);

  /// Create a new WebView with options.
  HRESULT CreateCoreWebView2ControllerWithOptions(
      [in] HWND parentWindow,
      [in] ICoreWebView2ControllerOptions* options,
      [in] ICoreWebView2CreateCoreWebView2ControllerCompletedHandler* handler);

  /// Create a new WebView in visual hosting mode with options.
  HRESULT CreateCoreWebView2CompositionControllerWithOptions(
      [in] HWND parentWindow,
      [in] ICoreWebView2ControllerOptions* options,
      [in] ICoreWebView2CreateCoreWebView2CompositionControllerCompletedHandler* handler);
}

/// A list containing process id and corresponding process type.
[uuid(402B99CD-A0CC-4FA5-B7A5-51D86A1D2339), object, pointer_default(unique)]
interface ICoreWebView2ProcessInfoCollection : IUnknown {
  /// The number of process contained in the ICoreWebView2ProcessInfoCollection.
  [propget] HRESULT Count([out, retval] UINT* count);

  /// Gets the `ICoreWebView2ProcessInfo` located in the `ICoreWebView2ProcessInfoCollection`
  /// at the given index.
  HRESULT GetValueAtIndex([in] UINT32 index,
                          [out, retval] ICoreWebView2ProcessInfo** processInfo);
}

/// An event handler for the `ProcessInfosChanged` event.
[uuid(F4AF0C39-44B9-40E9-8B11-0484CFB9E0A1), object, pointer_default(unique)]
interface ICoreWebView2ProcessInfosChangedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.  No event args exist
  /// and the `args` parameter is set to `null`.
  HRESULT Invoke([in] ICoreWebView2Environment* sender, [in] IUnknown* args);
}

/// Options used to create WebView2 Environment.  A default implementation is
/// provided in `WebView2EnvironmentOptions.h`.
///
/// \snippet AppWindow.cpp CreateCoreWebView2EnvironmentWithOptions

[uuid(2fde08a8-1e9a-4766-8c05-95a9ceb9d1c5), object, pointer_default(unique)]
interface ICoreWebView2EnvironmentOptions : IUnknown {

  /// Changes the behavior of the WebView.  The arguments are passed to the
  /// browser process as part of the command.  For more information about
  /// using command-line switches with Chromium browser processes, navigate to
  /// [Run Chromium with Flags](https://www.chromium.org/developers/how-tos/run-chromium-with-flags).
  /// The value appended to a switch is appended to the browser process, for
  /// example, in `--edge-webview-switches=xxx` the value is `xxx`.  If you
  /// specify a switch that is important to WebView functionality, it is
  /// ignored, for example, `--user-data-dir`.  Specific features are disabled
  /// internally and blocked from being enabled.  If a switch is specified
  /// multiple times, only the last instance is used.
  ///
  /// \> [!NOTE]\n\> A merge of the different values of the same switch is not attempted,
  /// except for disabled and enabled features. The features specified by
  /// `--enable-features` and `--disable-features` are merged with simple
  /// logic.\n\> *   The features is the union of the specified features
  /// and built-in features.  If a feature is disabled, it is removed from the
  /// enabled features list.
  ///
  /// If you specify command-line switches and use the
  /// `additionalBrowserArguments` parameter, the `--edge-webview-switches`
  /// value takes precedence and is processed last.  If a switch fails to
  /// parse, the switch is ignored.  The default state for the operation is
  /// to run the browser process with no extra flags.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT AdditionalBrowserArguments([out, retval] LPWSTR* value);

  /// Sets the `AdditionalBrowserArguments` property.
  ///
  /// Please note that calling this API twice will replace the previous value
  /// rather than appending to it. If there are multiple switches, there
  /// should be a space in between them. The one exception is if multiple
  /// features are being enabled/disabled for a single switch, in which
  /// case the features should be comma-seperated.
  /// Ex. "--disable-features=feature1,feature2 --some-other-switch --do-something"
  [propput] HRESULT AdditionalBrowserArguments([in] LPCWSTR value);

  /// The default display language for WebView.  It applies to browser UI such as
  /// context menu and dialogs.  It also applies to the `accept-languages` HTTP
  ///  header that WebView sends to websites.  It is in the format of
  /// `language[-country]` where `language` is the 2-letter code from [ISO
  /// 639](https://www.iso.org/iso-639-language-codes.html) and `country` is the
  /// 2-letter code from [ISO 3166](https://www.iso.org/standard/72482.html).
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Language([out, retval] LPWSTR* value);

  /// Sets the `Language` property.
  [propput] HRESULT Language([in] LPCWSTR value);

  /// Specifies the version of the WebView2 Runtime binaries required to be
  /// compatible with your app.  This defaults to the WebView2 Runtime version
  /// that corresponds with the version of the SDK the app is using.  The
  /// format of this value is the same as the format of the
  /// `BrowserVersionString` property and other `BrowserVersion` values.  Only
  /// the version part of the `BrowserVersion` value is respected.  The channel
  ///  suffix, if it exists, is ignored.  The version of the WebView2 Runtime
  /// binaries actually used may be different from the specified
  /// `TargetCompatibleBrowserVersion`.  The binaries are only guaranteed to be
  /// compatible.  Verify the actual version on the `BrowserVersionString`
  /// property on the `ICoreWebView2Environment`.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT TargetCompatibleBrowserVersion([out, retval] LPWSTR* value);

  /// Sets the `TargetCompatibleBrowserVersion` property.
  [propput] HRESULT TargetCompatibleBrowserVersion([in] LPCWSTR value);

  /// The `AllowSingleSignOnUsingOSPrimaryAccount` property is used to enable
  /// single sign on with Azure Active Directory (AAD) and personal Microsoft
  /// Account (MSA) resources inside WebView. All AAD accounts, connected to
  /// Windows and shared for all apps, are supported. For MSA, SSO is only enabled
  /// for the account associated for Windows account login, if any.
  /// Default is disabled. Universal Windows Platform apps must also declare
  /// `enterpriseCloudSSO`
  /// [Restricted capabilities](/windows/uwp/packaging/app-capability-declarations\#restricted-capabilities)
  /// for the single sign on (SSO) to work.
  [propget] HRESULT AllowSingleSignOnUsingOSPrimaryAccount([out, retval] BOOL* allow);

  /// Sets the `AllowSingleSignOnUsingOSPrimaryAccount` property.
  [propput] HRESULT AllowSingleSignOnUsingOSPrimaryAccount([in] BOOL allow);
}

/// Additional options used to create WebView2 Environment.  A default implementation is
/// provided in `WebView2EnvironmentOptions.h`.
///
/// \snippet AppWindow.cpp CreateCoreWebView2EnvironmentWithOptions

// Note: ICoreWebView2EnvironmentOptions* interfaces derive from IUnknown to make moving
// the API from experimental to public smoothier. These interfaces are mostly internal to
// WebView's own code. Normal apps just use the objects we provided and never interact
// with the interfaces. Advanced apps might implement their own options object. In that
// case, it is also easier for them to implement the interface if it is derived from IUnknown.
[uuid(FF85C98A-1BA7-4A6B-90C8-2B752C89E9E2), object, pointer_default(unique)]
interface ICoreWebView2EnvironmentOptions2 : IUnknown {

  /// Whether other processes can create WebView2 from WebView2Environment created with the
  /// same user data folder and therefore sharing the same WebView browser process instance.
  /// Default is FALSE.
  [propget] HRESULT ExclusiveUserDataFolderAccess([out, retval] BOOL* value);

  /// Sets the `ExclusiveUserDataFolderAccess` property.
  /// The `ExclusiveUserDataFolderAccess` property specifies that the WebView environment
  /// obtains exclusive access to the user data folder.
  /// If the user data folder is already being used by another WebView environment with a
  /// different value for `ExclusiveUserDataFolderAccess` property, the creation of a WebView2Controller
  /// using the environment object will fail with `HRESULT_FROM_WIN32(ERROR_INVALID_STATE)`.
  /// When set as TRUE, no other WebView can be created from other processes using WebView2Environment
  /// objects with the same UserDataFolder. This prevents other processes from creating WebViews
  /// which share the same browser process instance, since sharing is performed among
  /// WebViews that have the same UserDataFolder. When another process tries to create a
  /// WebView2Controller from an WebView2Environment object created with the same user data folder,
  /// it will fail with `HRESULT_FROM_WIN32(ERROR_INVALID_STATE)`.
  [propput] HRESULT ExclusiveUserDataFolderAccess([in] BOOL value);
}

/// Receives the `WebView2Environment` created using
/// `CreateCoreWebView2Environment`.
[uuid(4e8a3389-c9d8-4bd2-b6b5-124fee6cc14d), object, pointer_default(unique)]
interface ICoreWebView2CreateCoreWebView2EnvironmentCompletedHandler : IUnknown {

  /// Provides the completion status and result of the corresponding
  /// asynchronous method.

  HRESULT Invoke(HRESULT errorCode, ICoreWebView2Environment* createdEnvironment);
}

/// A Receiver is created for a particular DevTools Protocol event and allows
/// you to subscribe and unsubscribe from that event.  Obtained from the
/// WebView object using `GetDevToolsProtocolEventReceiver`.
[uuid(b32ca51a-8371-45e9-9317-af021d080367), object, pointer_default(unique)]
interface ICoreWebView2DevToolsProtocolEventReceiver : IUnknown {

  /// Subscribe to a `DevToolsProtocol` event.  The `Invoke` method of the
  /// `handler` runs whenever the corresponding `DevToolsProtocol` event runs.
  /// `Invoke` runs with an event args object containing the parameter object
  /// of the DevTools Protocol event as a JSON string.
  ///
  /// \snippet ScriptComponent.cpp DevToolsProtocolEventReceived

  HRESULT add_DevToolsProtocolEventReceived(
      [in] ICoreWebView2DevToolsProtocolEventReceivedEventHandler* handler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with
  /// `add_DevToolsProtocolEventReceived`.

  HRESULT remove_DevToolsProtocolEventReceived(
      [in] EventRegistrationToken token);

}

/// ICoreWebView2Frame provides direct access to the iframes information.
/// You can get an ICoreWebView2Frame by handling the ICoreWebView2_4::add_FrameCreated event.
[uuid(f1131a5e-9ba9-11eb-a8b3-0242ac130003), object, pointer_default(unique)]
interface ICoreWebView2Frame : IUnknown {
  /// The name of the iframe from the iframe html tag declaring it.
  /// You can access this property even if the iframe is destroyed.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Name([ out, retval ] LPWSTR * name);
  /// Raised when the iframe changes its window.name property.
  HRESULT add_NameChanged(
      [in] ICoreWebView2FrameNameChangedEventHandler * eventHandler,
      [out] EventRegistrationToken * token);
  /// Remove an event handler previously added with add_NameChanged.
  HRESULT remove_NameChanged([in] EventRegistrationToken token);

  /// Add the provided host object to script running in the iframe with the
  /// specified name for the list of the specified origins. The host object
  /// will be accessible for this iframe only if the iframe's origin during
  /// access matches one of the origins which are passed. The provided origins
  /// will be normalized before comparing to the origin of the document.
  /// So the scheme name is made lower case, the host will be punycode decoded
  /// as appropriate, default port values will be removed, and so on.
  /// This means the origin's host may be punycode encoded or not and will match
  /// regardless. If list contains malformed origin the call will fail.
  /// The method can be called multiple times in a row without calling
  /// RemoveHostObjectFromScript for the same object name. It will replace
  /// the previous object with the new object and new list of origins.
  /// List of origins will be treated as following:
  /// 1. empty list - call will succeed and object will be added for the iframe
  /// but it will not be exposed to any origin;
  /// 2. list with origins - during access to host object from iframe the
  /// origin will be checked that it belongs to this list;
  /// 3. list with "*" element - host object will be available for iframe for
  /// all origins. We suggest not to use this feature without understanding
  /// security implications of giving access to host object from from iframes
  /// with unknown origins.
  /// Calling this method fails if it is called after the iframe is destroyed.
  /// \snippet ScenarioAddHostObject.cpp AddHostObjectToScriptWithOrigins
  /// For more information about host objects navigate to
  /// ICoreWebView2::AddHostObjectToScript.
  HRESULT AddHostObjectToScriptWithOrigins(
      [in] LPCWSTR name,
      [in] VARIANT * object,
      [in] UINT32 originsCount,
      [ in, size_is(originsCount) ] LPCWSTR * origins);
  /// Remove the host object specified by the name so that it is no longer
  /// accessible from JavaScript code in the iframe. While new access
  /// attempts are denied, if the object is already obtained by JavaScript code
  /// in the iframe, the JavaScript code continues to have access to that
  /// object. Calling this method for a name that is already removed or was
  /// never added fails. If the iframe is destroyed this method will return fail
  /// also.
  HRESULT RemoveHostObjectFromScript([in] LPCWSTR name);

  /// The Destroyed event is raised when the iframe corresponding
  /// to this CoreWebView2Frame object is removed or the document
  /// containing that iframe is destroyed.
  HRESULT add_Destroyed(
      [in] ICoreWebView2FrameDestroyedEventHandler * eventHandler,
      [out] EventRegistrationToken * token);
  /// Remove an event handler previously added with add_Destroyed.
  HRESULT remove_Destroyed([in] EventRegistrationToken token);
  /// Check whether a frame is destroyed. Returns true during
  /// the Destroyed event.
  HRESULT IsDestroyed([ out, retval ] BOOL * destroyed);
}

/// A continuation of the ICoreWebView2Frame interface with navigation events,
/// executing script and posting web messages.
[uuid(7a6a5834-d185-4dbf-b63f-4a9bc43107d4), object, pointer_default(unique)]
interface ICoreWebView2Frame2 : ICoreWebView2Frame {
  /// Add an event handler for the `NavigationStarting` event.
  /// A frame navigation will raise a `NavigationStarting` event and
  /// a `CoreWebView2.FrameNavigationStarting` event. All of the
  /// `FrameNavigationStarting` event handlers for the current frame will be
  /// run before the `NavigationStarting` event handlers. All of the event handlers
  /// share a common `NavigationStartingEventArgs` object. Whichever event handler is
  /// last to change the `NavigationStartingEventArgs.Cancel` property will
  /// decide if the frame navigation will be cancelled. Redirects raise this
  /// event as well, and the navigation id is the same as the original one.
  ///
  /// Navigations will be blocked until all `NavigationStarting` and
  /// `CoreWebView2.FrameNavigationStarting` event handlers return.
  HRESULT add_NavigationStarting(
      [in] ICoreWebView2FrameNavigationStartingEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_NavigationStarting`.
  HRESULT remove_NavigationStarting(
      [in] EventRegistrationToken token);

  /// Add an event handler for the `ContentLoading` event.  `ContentLoading`
  /// triggers before any content is loaded, including scripts added with
  /// `AddScriptToExecuteOnDocumentCreated`.  `ContentLoading` does not trigger
  /// if a same page navigation occurs (such as through `fragment`
  /// navigations or `history.pushState` navigations).  This operation
  /// follows the `NavigationStarting` and precedes `NavigationCompleted` events.
  HRESULT add_ContentLoading(
      [in] ICoreWebView2FrameContentLoadingEventHandler * eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_ContentLoading`.
  HRESULT remove_ContentLoading(
      [in] EventRegistrationToken token);

  /// Add an event handler for the `NavigationCompleted` event.
  /// `NavigationCompleted` runs when the CoreWebView2Frame has completely
  /// loaded (concurrently when `body.onload` runs) or loading stopped with error.
  HRESULT add_NavigationCompleted(
      [in] ICoreWebView2FrameNavigationCompletedEventHandler *
          eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_NavigationCompleted`.
  HRESULT remove_NavigationCompleted(
      [in] EventRegistrationToken token);

  /// Add an event handler for the DOMContentLoaded event.
  /// DOMContentLoaded is raised when the iframe html document has been parsed.
  /// This aligns with the document's DOMContentLoaded event in html.
  HRESULT add_DOMContentLoaded(
      [in] ICoreWebView2FrameDOMContentLoadedEventHandler * eventHandler,
      [out] EventRegistrationToken* token);
  /// Remove an event handler previously added with add_DOMContentLoaded.
  HRESULT remove_DOMContentLoaded(
      [in] EventRegistrationToken token);

  /// Run JavaScript code from the javascript parameter in the current frame.
  /// The result of evaluating the provided JavaScript is passed to the completion handler.
  /// The result value is a JSON encoded string. If the result is undefined,
  /// contains a reference cycle, or otherwise is not able to be encoded into
  /// JSON, then the result is considered to be null, which is encoded
  /// in JSON as the string "null".
  ///
  /// \> [!NOTE]\n\> A function that has no explicit return value returns undefined. If the
  /// script that was run throws an unhandled exception, then the result is
  /// also "null". This method is applied asynchronously. If the method is
  /// run before `ContentLoading`, the script will not be executed
  /// and the string "null" will be returned.
  /// This operation executes the script even if `ICoreWebView2Settings::IsScriptEnabled` is
  /// set to `FALSE`.
  ///
  /// \snippet ScenarioDOMContentLoaded.cpp ExecuteScriptFrame
  HRESULT ExecuteScript(
      [in] LPCWSTR javaScript,
      [in] ICoreWebView2ExecuteScriptCompletedHandler* handler);

  /// Posts the specified webMessage to the frame.
  /// The frame receives the message by subscribing to the `message` event of
  /// the `window.chrome.webview` of the frame document.
  ///
  /// ```cpp
  /// window.chrome.webview.addEventListener('message', handler)
  /// window.chrome.webview.removeEventListener('message', handler)
  /// ```
  ///
  /// The event args is an instances of `MessageEvent`. The
  /// `ICoreWebView2Settings::IsWebMessageEnabled` setting must be `TRUE` or
  /// this method fails with `E_INVALIDARG`. The `data` property of the event
  /// args is the `webMessage` string parameter parsed as a JSON string into a
  /// JavaScript object. The `source` property of the event args is a reference
  /// to the `window.chrome.webview` object.  For information about sending
  /// messages from the HTML document in the WebView to the host, navigate to
  /// [add_WebMessageReceived](/microsoft-edge/webview2/reference/win32/icorewebview2#add_webmessagereceived).
  /// The message is delivered asynchronously. If a navigation occurs before the
  /// message is posted to the page, the message is discarded.
  HRESULT PostWebMessageAsJson([in] LPCWSTR webMessageAsJson);

  /// Posts a message that is a simple string rather than a JSON string
  /// representation of a JavaScript object. This behaves in exactly the same
  /// manner as `PostWebMessageAsJson`, but the `data` property of the event
  /// args of the `window.chrome.webview` message is a string with the same
  /// value as `webMessageAsString`. Use this instead of
  /// `PostWebMessageAsJson` if you want to communicate using simple strings
  /// rather than JSON objects.
  HRESULT PostWebMessageAsString([in] LPCWSTR webMessageAsString);

  /// Add an event handler for the `WebMessageReceived` event.
  /// `WebMessageReceived` runs when the
  /// `ICoreWebView2Settings::IsWebMessageEnabled` setting is set and the
  /// frame document runs `window.chrome.webview.postMessage`.
  /// The `postMessage` function is `void postMessage(object)`
  /// where object is any object supported by JSON conversion.
  ///
  /// \snippet assets\ScenarioWebMessage.html chromeWebView
  ///
  /// When the frame calls `postMessage`, the object parameter is converted to a
  /// JSON string and is posted asynchronously to the host process. This will
  /// result in the handlers `Invoke` method being called with the JSON string
  /// as its parameter.
  ///
  /// \snippet ScenarioWebMessage.cpp WebMessageReceivedIFrame
  HRESULT add_WebMessageReceived(
      [in] ICoreWebView2FrameWebMessageReceivedEventHandler *
          handler,
      [out] EventRegistrationToken * token);

  /// Remove an event handler previously added with `add_WebMessageReceived`.
  HRESULT remove_WebMessageReceived([in] EventRegistrationToken token);
}

/// Receives `FrameCreated` event.
[uuid(38059770-9baa-11eb-a8b3-0242ac130003), object, pointer_default(unique)]
interface ICoreWebView2FrameCreatedEventHandler : IUnknown {
  /// Provides the result for the iframe created event.
  HRESULT Invoke([in] ICoreWebView2 * sender,
                 [in] ICoreWebView2FrameCreatedEventArgs * args);
}

/// Receives `FrameNameChanged` event.
[uuid(435c7dc8-9baa-11eb-a8b3-0242ac130003), object, pointer_default(unique)]
interface ICoreWebView2FrameNameChangedEventHandler : IUnknown {
  /// Provides the result for the iframe name changed event.
  /// No event args exist and the `args` parameter is set to `null`.
  HRESULT Invoke([in] ICoreWebView2Frame * sender, [in] IUnknown * args);
}

/// Receives `NavigationStarting` events for iframe.
[uuid(e79908bf-2d5d-4968-83db-263fea2c1da3), object, pointer_default(unique)]
interface ICoreWebView2FrameNavigationStartingEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.
  HRESULT Invoke(
      [in] ICoreWebView2Frame* sender,
      [in] ICoreWebView2NavigationStartingEventArgs* args);
}

/// Receives `ContentLoading` events for iframe.
[uuid(0d6156f2-d332-49a7-9e03-7d8f2feeee54), object, pointer_default(unique)]
interface ICoreWebView2FrameContentLoadingEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.
  HRESULT Invoke(
      [in] ICoreWebView2Frame* sender,
      [in] ICoreWebView2ContentLoadingEventArgs* args);
}

/// Receives `NavigationCompleted` events for iframe.
[uuid(609302ad-0e36-4f9a-a210-6a45272842a9), object, pointer_default(unique)]
interface ICoreWebView2FrameNavigationCompletedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.
  HRESULT Invoke(
      [in] ICoreWebView2Frame* sender,
      [in] ICoreWebView2NavigationCompletedEventArgs* args);
}

/// Receives `DOMContentLoaded` events for iframe.
[uuid(38d9520d-340f-4d1e-a775-43fce9753683), object, pointer_default(unique)]
interface ICoreWebView2FrameDOMContentLoadedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.
  HRESULT Invoke(
      [in] ICoreWebView2Frame* sender,
      [in] ICoreWebView2DOMContentLoadedEventArgs* args);
}

/// Receives `WebMessageReceived` events for iframe.
[uuid(e371e005-6d1d-4517-934b-a8f1629c62a5), object, pointer_default(unique)]
interface ICoreWebView2FrameWebMessageReceivedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.
  HRESULT Invoke(
      [in] ICoreWebView2Frame* sender,
      [in] ICoreWebView2WebMessageReceivedEventArgs* args);
}

/// Event args for the `FrameCreated` events.
[uuid(4d6e7b5e-9baa-11eb-a8b3-0242ac130003), object, pointer_default(unique)]
interface ICoreWebView2FrameCreatedEventArgs : IUnknown {
  /// The frame which was created.
  [propget] HRESULT Frame([ out, retval ] ICoreWebView2Frame **frame);
}

/// Receives `FrameDestroyed` event.
[uuid(59dd7b4c-9baa-11eb-a8b3-0242ac130003), object, pointer_default(unique)]
interface ICoreWebView2FrameDestroyedEventHandler : IUnknown {
  /// Provides the result for the iframe destroyed event.
  /// No event args exist and the `args` parameter is set to `null`.
  HRESULT Invoke([in] ICoreWebView2Frame * sender, [in] IUnknown * args);
}

/// Add an event handler for the `DownloadStarting` event.
[uuid(efedc989-c396-41ca-83f7-07f845a55724), object, pointer_default(unique)]
interface ICoreWebView2DownloadStartingEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.
  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] ICoreWebView2DownloadStartingEventArgs* args);
}

/// Event args for the `DownloadStarting` event.
[uuid(e99bbe21-43e9-4544-a732-282764eafa60), object, pointer_default(unique)]
interface ICoreWebView2DownloadStartingEventArgs : IUnknown {
  /// Returns the `ICoreWebView2DownloadOperation` for the download that
  /// has started.
  [propget] HRESULT DownloadOperation(
      [out, retval] ICoreWebView2DownloadOperation** downloadOperation);

  /// The host may set this flag to cancel the download. If canceled, the
  /// download save dialog is not displayed regardless of the
  /// `Handled` property.
  [propget] HRESULT Cancel([out, retval] BOOL* cancel);

  /// Sets the `Cancel` property.
  [propput] HRESULT Cancel([in] BOOL cancel);

  /// The path to the file. If setting the path, the host should ensure that it
  /// is an absolute path, including the file name, and that the path does not
  /// point to an existing file. If the path points to an existing file, the
  /// file will be overwritten. If the directory does not exist, it is created.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT ResultFilePath([out, retval] LPWSTR* resultFilePath);

  /// Sets the `ResultFilePath` property.
  [propput] HRESULT ResultFilePath([in] LPCWSTR resultFilePath);

  /// The host may set this flag to `TRUE` to hide the default download dialog
  /// for this download. The download will progress as normal if it is not
  /// canceled, there will just be no default UI shown. By default the value is
  /// `FALSE` and the default download dialog is shown.
  [propget] HRESULT Handled([out, retval] BOOL* handled);

  /// Sets the `Handled` property.
  [propput] HRESULT Handled([in] BOOL handled);

  /// Returns an `ICoreWebView2Deferral` object.  Use this operation to
  /// complete the event at a later time.
  HRESULT GetDeferral([out, retval] ICoreWebView2Deferral** deferral);
}

/// Implements the interface to receive `BytesReceivedChanged` event.  Use the
/// `ICoreWebView2DownloadOperation.BytesReceived` property to get the received
/// bytes count.
[uuid(828e8ab6-d94c-4264-9cef-5217170d6251), object, pointer_default(unique)]
interface ICoreWebView2BytesReceivedChangedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event. No event args exist
  /// and the `args` parameter is set to `null`.
  HRESULT Invoke(
      [in] ICoreWebView2DownloadOperation* sender, [in] IUnknown* args);
}

/// Implements the interface to receive `EstimatedEndTimeChanged` event. Use the
/// `ICoreWebView2DownloadOperation.EstimatedEndTime` property to get the new
/// estimated end time.
[uuid(28f0d425-93fe-4e63-9f8d-2aeec6d3ba1e), object, pointer_default(unique)]
interface ICoreWebView2EstimatedEndTimeChangedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event. No event args exist
  /// and the `args` parameter is set to `null`.
  HRESULT Invoke(
      [in] ICoreWebView2DownloadOperation* sender, [in] IUnknown* args);
}

/// Implements the interface to receive `StateChanged` event. Use the
/// `ICoreWebView2DownloadOperation.State` property to get the current state,
/// which can be in progress, interrupted, or completed. Use the
/// `ICoreWebView2DownloadOperation.InterruptReason` property to get the
/// interrupt reason if the download is interrupted.
[uuid(81336594-7ede-4ba9-bf71-acf0a95b58dd), object, pointer_default(unique)]
interface ICoreWebView2StateChangedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event. No event args exist
  /// and the `args` parameter is set to `null`.
  HRESULT Invoke(
      [in] ICoreWebView2DownloadOperation* sender, [in] IUnknown* args);
}

/// Represents a download operation. Gives access to the download's metadata
/// and supports a user canceling, pausing, or resuming the download.
[uuid(3d6b6cf2-afe1-44c7-a995-c65117714336), object, pointer_default(unique)]
interface ICoreWebView2DownloadOperation : IUnknown {
  /// Add an event handler for the `BytesReceivedChanged` event.
  ///
  /// \snippet ScenarioCustomDownloadExperience.cpp BytesReceivedChanged
  HRESULT add_BytesReceivedChanged(
    [in] ICoreWebView2BytesReceivedChangedEventHandler* eventHandler,
    [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_BytesReceivedChanged`.
  HRESULT remove_BytesReceivedChanged(
      [in] EventRegistrationToken token);

  /// Add an event handler for the `EstimatedEndTimeChanged` event.
  HRESULT add_EstimatedEndTimeChanged(
    [in] ICoreWebView2EstimatedEndTimeChangedEventHandler* eventHandler,
    [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_EstimatedEndTimeChanged`.
  HRESULT remove_EstimatedEndTimeChanged(
      [in] EventRegistrationToken token);

  /// Add an event handler for the `StateChanged` event.
  ///
  /// \snippet ScenarioCustomDownloadExperience.cpp StateChanged
  HRESULT add_StateChanged(
    [in] ICoreWebView2StateChangedEventHandler* eventHandler,
    [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_StateChanged`.
  HRESULT remove_StateChanged(
      [in] EventRegistrationToken token);

  /// The URI of the download.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Uri([out, retval] LPWSTR* uri);

  /// The Content-Disposition header value from the download's HTTP response.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT ContentDisposition([out, retval] LPWSTR* contentDisposition);

  /// MIME type of the downloaded content.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT MimeType([out, retval] LPWSTR* mimeType);

  /// The expected size of the download in total number of bytes based on the
  /// HTTP Content-Length header. Returns -1 if the size is unknown.
  [propget] HRESULT TotalBytesToReceive([out, retval] INT64* totalBytesToReceive);

  /// The number of bytes that have been written to the download file.
  [propget] HRESULT BytesReceived([out, retval] INT64* bytesReceived);

  /// The estimated end time in [ISO 8601 Date and Time Format](https://www.iso.org/iso-8601-date-and-time-format.html).
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT EstimatedEndTime([out, retval] LPWSTR* estimatedEndTime);

  /// The absolute path to the download file, including file name. Host can change
  /// this from `ICoreWebView2DownloadStartingEventArgs`.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT ResultFilePath([out, retval] LPWSTR* resultFilePath);

  /// The state of the download. A download can be in progress, interrupted, or
  /// completed. See `COREWEBVIEW2_DOWNLOAD_STATE` for descriptions of states.
  [propget] HRESULT State([out, retval] COREWEBVIEW2_DOWNLOAD_STATE* downloadState);

  /// The reason why connection with file host was broken.
  [propget] HRESULT InterruptReason(
      [out, retval] COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON* interruptReason);

  /// Cancels the download. If canceled, the default download dialog shows
  /// that the download was canceled. Host should set the `Cancel` property from
  /// `ICoreWebView2SDownloadStartingEventArgs` if the download should be
  /// canceled without displaying the default download dialog.
  HRESULT Cancel();

  /// Pauses the download. If paused, the default download dialog shows that the
  /// download is paused. No effect if download is already paused. Pausing a
  /// download changes the state to `COREWEBVIEW2_DOWNLOAD_STATE_INTERRUPTED`
  /// with `InterruptReason` set to `COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_USER_PAUSED`.
  HRESULT Pause();

  /// Resumes a paused download. May also resume a download that was interrupted
  /// for another reason, if `CanResume` returns true. Resuming a download changes
  /// the state from `COREWEBVIEW2_DOWNLOAD_STATE_INTERRUPTED` to
  /// `COREWEBVIEW2_DOWNLOAD_STATE_IN_PROGRESS`.
  HRESULT Resume();

  /// Returns true if an interrupted download can be resumed. Downloads with
  /// the following interrupt reasons may automatically resume without you
  /// calling any methods:
  /// `COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_SERVER_NO_RANGE`,
  /// `COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_FILE_HASH_MISMATCH`,
  /// `COREWEBVIEW2_DOWNLOAD_INTERRUPT_REASON_FILE_TOO_SHORT`.
  /// In these cases download progress may be restarted with `BytesReceived`
  /// reset to 0.
  [propget] HRESULT CanResume([out, retval] BOOL* canResume);
}

/// A continuation of the ICoreWebView2ProcessFailedEventArgs interface.
[uuid(4dab9422-46fa-4c3e-a5d2-41d2071d3680), object, pointer_default(unique)]
interface ICoreWebView2ProcessFailedEventArgs2 : ICoreWebView2ProcessFailedEventArgs {

  /// The reason for the process failure. The reason is always
  /// `COREWEBVIEW2_PROCESS_FAILED_REASON_UNEXPECTED` when `ProcessFailedKind`
  /// is `COREWEBVIEW2_PROCESS_FAILED_KIND_BROWSER_PROCESS_EXITED`, and
  /// `COREWEBVIEW2_PROCESS_FAILED_REASON_UNRESPONSIVE` when `ProcessFailedKind`
  /// is `COREWEBVIEW2_PROCESS_FAILED_KIND_RENDER_PROCESS_UNRESPONSIVE`.
  /// For other process failure kinds, the reason may be any of the reason
  /// values.

  [propget] HRESULT Reason(
      [out, retval] COREWEBVIEW2_PROCESS_FAILED_REASON* reason);

  /// The exit code of the failing process, for telemetry purposes. The exit
  /// code is always `STILL_ACTIVE` (`259`) when `ProcessFailedKind` is
  /// `COREWEBVIEW2_PROCESS_FAILED_KIND_RENDER_PROCESS_UNRESPONSIVE`.

  [propget] HRESULT ExitCode(
      [out, retval] int* exitCode);

  /// Description of the process assigned by the WebView2 Runtime. This is a
  /// technical English term appropriate for logging or development purposes,
  /// and not localized for the end user. It applies to utility processes (for
  /// example, "Audio Service", "Video Capture") and plugin processes (for
  /// example, "Flash"). The returned `processDescription` is empty if the
  /// WebView2 Runtime did not assign a description to the process.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT ProcessDescription(
      [out, retval] LPWSTR* processDescription);

  /// The collection of `FrameInfo`s for frames in the `ICoreWebView2` that were
  /// being rendered by the failed process. The content in these frames is
  /// replaced with an error page.
  /// This is only available when `ProcessFailedKind` is
  /// `COREWEBVIEW2_PROCESS_FAILED_KIND_FRAME_RENDER_PROCESS_EXITED`;
  /// `frames` is `null` for all other process failure kinds, including the case
  /// in which the failed process was the renderer for the main frame and
  /// subframes within it, for which the failure kind is
  /// `COREWEBVIEW2_PROCESS_FAILED_KIND_RENDER_PROCESS_EXITED`.

  [propget] HRESULT FrameInfosForFailedProcess(
      [out, retval] ICoreWebView2FrameInfoCollection** frames);
}

/// Collection of `FrameInfo`s (name and source). Used to list the affected
/// frames' info when a frame-only render process failure occurs in the
/// `ICoreWebView2`.
[uuid(8f834154-d38e-4d90-affb-6800a7272839), object, pointer_default(unique)]
interface ICoreWebView2FrameInfoCollection : IUnknown {

  /// Gets an iterator over the collection of `FrameInfo`s.

  HRESULT GetIterator(
      [out, retval] ICoreWebView2FrameInfoCollectionIterator** iterator);
}

/// Iterator for a collection of `FrameInfo`s. For more info, see
/// `ICoreWebView2ProcessFailedEventArgs2` and
/// `ICoreWebView2FrameInfoCollection`.
[uuid(1bf89e2d-1b2b-4629-b28f-05099b41bb03), object, pointer_default(unique)]
interface ICoreWebView2FrameInfoCollectionIterator : IUnknown {

  /// `TRUE` when the iterator has not run out of `FrameInfo`s.  If the
  /// collection over which the iterator is iterating is empty or if the
  /// iterator has gone past the end of the collection, then this is `FALSE`.

  [propget] HRESULT HasCurrent([out, retval] BOOL* hasCurrent);

  /// Get the current `ICoreWebView2FrameInfo` of the iterator.
  /// Returns `HRESULT_FROM_WIN32(ERROR_INVALID_INDEX)` if HasCurrent is
  /// `FALSE`.

  HRESULT GetCurrent([out, retval] ICoreWebView2FrameInfo** frameInfo);

  /// Move the iterator to the next `FrameInfo` in the collection.

  HRESULT MoveNext([out, retval] BOOL* hasNext);
}

/// Provides a set of properties for a frame in the `ICoreWebView2`.
[uuid(da86b8a1-bdf3-4f11-9955-528cefa59727), object, pointer_default(unique)]
interface ICoreWebView2FrameInfo : IUnknown {

  /// The name attribute of the frame, as in `<iframe name="frame-name" ...>`.
  /// The returned string is empty when the frame has no name attribute.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT Name([out, retval] LPWSTR* name);

  /// The URI of the document in the frame.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

  [propget] HRESULT Source([out, retval] LPWSTR* source);
}

/// Represents a Basic HTTP authentication response that contains a user name
/// and a password as according to RFC7617 (https://tools.ietf.org/html/rfc7617)
[uuid(07023f7d-2d77-4d67-9040-6e7d428c6a40), object, pointer_default(unique)]
interface ICoreWebView2BasicAuthenticationResponse : IUnknown {
  /// User name provided for authentication.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT UserName([out, retval] LPWSTR* userName);
  /// Set user name property
  [propput] HRESULT UserName([in] LPCWSTR userName);

  /// Password provided for authentication.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Password([out, retval] LPWSTR* password);
  /// Set password property
  [propput] HRESULT Password([in] LPCWSTR password);
}

/// Event args for the BasicAuthenticationRequested event. Will contain the
/// request that led to the HTTP authorization challenge, the challenge
/// and allows the host to provide authentication response or cancel the request.
[uuid(ef05516f-d897-4f9e-b672-d8e2307a3fb0), object, pointer_default(unique)]
interface ICoreWebView2BasicAuthenticationRequestedEventArgs : IUnknown {
  /// The URI that led to the authentication challenge
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Uri([out, retval] LPWSTR* value);

  /// The authentication challenge string
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Challenge([out, retval] LPWSTR* challenge);

  /// Response to the authentication request with credentials. This object will be populated by the app
  /// if the host would like to provide authentication credentials.
  [propget] HRESULT Response([out, retval] ICoreWebView2BasicAuthenticationResponse** response);

  /// Cancel the authentication request. False by default.
  /// If set to true, Response will be ignored.
  [propget] HRESULT Cancel([out, retval] BOOL* cancel);
  /// Set the Cancel property.
  [propput] HRESULT Cancel([in] BOOL cancel);

  /// Returns an `ICoreWebView2Deferral` object. Use this deferral to
  /// defer the decision to show the Basic Authentication dialog.
  HRESULT GetDeferral([out, retval] ICoreWebView2Deferral** deferral);
}


/// Implements the interface to receive `IsDocumentPlayingAudioChanged` events.  Use the
/// IsDocumentPlayingAudio property to get the audio playing state.
[uuid(5DEF109A-2F4B-49FA-B7F6-11C39E513328), object, pointer_default(unique)]
interface ICoreWebView2IsDocumentPlayingAudioChangedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.  No event args exist
  /// and the `args` parameter is set to `null`.
  HRESULT Invoke([in] ICoreWebView2* sender, [in] IUnknown* args);
}

/// Implements the interface to receive `IsMutedChanged` events.  Use the
/// IsMuted property to get the mute state.
[uuid(57D90347-CD0E-4952-A4A2-7483A2756F08), object, pointer_default(unique)]
interface ICoreWebView2IsMutedChangedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.  No event args exist
  /// and the `args` parameter is set to `null`.
  HRESULT Invoke([in] ICoreWebView2* sender, [in] IUnknown* args);
}

/// This is an extension of the ICoreWebView2Frame interface that supports PermissionRequested
[uuid(b50d82cc-cc28-481d-9614-cb048895e6a0), object, pointer_default(unique)]
interface ICoreWebView2Frame3 : ICoreWebView2Frame2 {
  /// Add an event handler for the `PermissionRequested` event.
  /// `PermissionRequested` is raised when content in an iframe any of its
  /// descendant iframes requests permission to privileged resources.
  ///
  /// This relates to the `PermissionRequested` event on the `CoreWebView2`.
  /// Both these events will be raised in the case of an iframe requesting
  /// permission. The `CoreWebView2Frame`'s event handlers will be invoked
  /// before the event handlers on the `CoreWebView2`. If the `Handled` property
  /// of the `PermissionRequestedEventArgs` is set to TRUE within the
  /// `CoreWebView2Frame` event handler, then the event will not be
  /// raised on the `CoreWebView2`, and it's event handlers will not be invoked.
  ///
  /// In the case of nested iframes, the 'PermissionRequested' event will
  /// be raised from the top level iframe.
  ///
  /// If a deferral is not taken on the event args, the subsequent scripts are
  /// blocked until the event handler returns.  If a deferral is taken, the
  /// scripts are blocked until the deferral is completed.
  ///
  /// \snippet ScenarioIFrameDevicePermission.cpp PermissionRequested0
  /// \snippet ScenarioIFrameDevicePermission.cpp PermissionRequested1
  HRESULT add_PermissionRequested(
      [in] ICoreWebView2FramePermissionRequestedEventHandler* handler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_PermissionRequested`
  HRESULT remove_PermissionRequested(
      [in] EventRegistrationToken token);
}

/// Receives `PermissionRequested` events for iframes.
[uuid(845d0edd-8bd8-429b-9915-4821789f23e9), object, pointer_default(unique)]
interface ICoreWebView2FramePermissionRequestedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.
  HRESULT Invoke(
      [in] ICoreWebView2Frame* sender,
      [in] ICoreWebView2PermissionRequestedEventArgs2* args);
}

/// This is a continuation of the `ICoreWebView2PermissionRequestedEventArgs` interface.
[uuid(74d7127f-9de6-4200-8734-42d6fb4ff741), object, pointer_default(unique)]
interface ICoreWebView2PermissionRequestedEventArgs2: ICoreWebView2PermissionRequestedEventArgs {
  /// By default, both the `PermissionRequested` event handlers on the
  /// `CoreWebView2Frame' and the `CoreWebView2` will be invoked, with the
  /// `CoreWebView2Frame' event handlers invoked first. The host may
  /// set this flag to `TRUE` within the `CoreWebView2Frame' event handlers
  /// to prevent the remaining `CoreWebView2` event handlers from being invoked.
  ///
  /// If a deferral is taken on the event args, then you must synchronously
  /// set `Handled` to TRUE prior to taking your deferral to prevent the
  /// `CoreWebView2`s event handlers from being invoked.
  [propget] HRESULT Handled([out, retval] BOOL* handled);

  /// Sets the `Handled` property.
  [propput] HRESULT Handled([in] BOOL handled);
}

/// Represents a context menu item of a context menu displayed by WebView.
[uuid(7aed49e3-a93f-497a-811c-749c6b6b6c65), object, pointer_default(unique)]
interface ICoreWebView2ContextMenuItem : IUnknown {
  /// Gets the unlocalized name for the `ContextMenuItem`. Use this to
  /// distinguish between context menu item types. This will be the English
  /// label of the menu item in lower camel case. For example, the "Save as"
  /// menu item will be "saveAs". Extension menu items will be "extension",
  /// custom menu items will be "custom" and spellcheck items will be
  /// "spellCheck".
  /// Some example context menu item names are:
  /// - "saveAs"
  /// - "copyImage"
  /// - "openLinkInNewWindow"
  /// - "cut"
  /// - "copy"
  /// - "paste"
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Name([out, retval] LPWSTR* value);

  /// Gets the localized label for the `ContextMenuItem`. Will contain an
  /// ampersand for characters to be used as keyboard accelerator.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Label([out, retval] LPWSTR* value);

  /// Gets the Command ID for the `ContextMenuItem`. Use this to report the
  /// `SelectedCommandId` in `ContextMenuRequested` event.
  [propget] HRESULT CommandId([out, retval] INT32* value);

  /// Gets the localized keyboard shortcut for this ContextMenuItem. It will be
  /// the empty string if there is no keyboard shortcut. This is text intended
  /// to be displayed to the end user to show the keyboard shortcut. For example
  /// this property is Ctrl+Shift+I for the "Inspect" `ContextMenuItem`.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT ShortcutKeyDescription([out, retval] LPWSTR* value);

  /// Gets the Icon for the `ContextMenuItem` in PNG, Bitmap or SVG formats in the form of an IStream.
  /// Stream will be rewound to the start of the image data.
  [propget] HRESULT Icon([out, retval] IStream** value);

  /// Gets the `ContextMenuItem` kind.
  [propget] HRESULT Kind([out, retval] COREWEBVIEW2_CONTEXT_MENU_ITEM_KIND* value);

  /// Sets the enabled property of the `ContextMenuItem`. Must only be used in the case of a
  /// custom context menu item. The default value for this is `TRUE`.
  [propput] HRESULT IsEnabled([in] BOOL value);

  /// Gets the enabled property of the `ContextMenuItem`.
  [propget] HRESULT IsEnabled([out, retval] BOOL* value);

  /// Sets the checked property of the `ContextMenuItem`. Must only be used for custom context
  /// menu items that are of kind Check box or Radio.
  [propput] HRESULT IsChecked([in] BOOL value);

  /// Gets the checked property of the `ContextMenuItem`, used if the kind is Check box or Radio.
  [propget] HRESULT IsChecked([out, retval] BOOL* value);

  /// Gets the list of children menu items through a `ContextMenuItemCollection`
  /// if the kind is Submenu. If the kind is not submenu, will return null.
  [propget] HRESULT Children([out, retval] ICoreWebView2ContextMenuItemCollection** value);

  /// Add an event handler for the `CustomItemSelected` event.
  /// `CustomItemSelected` event is raised when the user selects this `ContextMenuItem`.
  /// Will only be raised for end developer created context menu items
  HRESULT add_CustomItemSelected(
      [in] ICoreWebView2CustomItemSelectedEventHandler* eventHandler,
      [out] EventRegistrationToken* token);

  /// Remove an event handler previously added with `add_CustomItemSelected`.
  HRESULT remove_CustomItemSelected(
      [in] EventRegistrationToken token);
}

/// Represents a collection of `ContextMenuItem` objects. Used to get, remove and add
/// `ContextMenuItem` objects at the specified index.
[uuid(f562a2f5-c415-45cf-b909-d4b7c1e276d3), object, pointer_default(unique)]
interface ICoreWebView2ContextMenuItemCollection : IUnknown {
  /// Gets the number of `ContextMenuItem` objects contained in the `ContextMenuItemCollection`.
  [propget] HRESULT Count([out, retval] UINT32* value);

  /// Gets the `ContextMenuItem` at the specified index.
  HRESULT GetValueAtIndex([in] UINT32 index,
      [out, retval] ICoreWebView2ContextMenuItem** value);

  /// Removes the `ContextMenuItem` at the specified index.
  HRESULT RemoveValueAtIndex([in] UINT32 index);

  /// Inserts the `ContextMenuItem` at the specified index.
  HRESULT InsertValueAtIndex(
      [in] UINT32 index,
        [in] ICoreWebView2ContextMenuItem* value);
}

/// Receives `ContextMenuRequested` events.
[uuid(04d3fe1d-ab87-42fb-a898-da241d35b63c), object, pointer_default(unique)]
interface ICoreWebView2ContextMenuRequestedEventHandler : IUnknown {
  /// Called to provide the event args when a context menu is requested on a
  /// WebView element.
  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] ICoreWebView2ContextMenuRequestedEventArgs* args);
}

/// Raised to notify the host that the end user selected a custom
/// `ContextMenuItem`. `CustomItemSelected` event is raised on the specific
/// `ContextMenuItem` that the end user selected.
[uuid(49e1d0bc-fe9e-4481-b7c2-32324aa21998), object, pointer_default(unique)]
interface ICoreWebView2CustomItemSelectedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event. No event args exist
  /// and the `args` parameter is set to `null`.
  HRESULT Invoke(
      [in] ICoreWebView2ContextMenuItem* sender, [in] IUnknown* args);
}

/// Represents the information regarding the context menu target.
/// Includes the context selected and the appropriate data used for the actions of a context menu.
[uuid(b8611d99-eed6-4f3f-902c-a198502ad472), object, pointer_default(unique)]
interface ICoreWebView2ContextMenuTarget : IUnknown {
  /// Gets the kind of context that the user selected.
  [propget] HRESULT Kind([out, retval] COREWEBVIEW2_CONTEXT_MENU_TARGET_KIND* value);

  /// Returns TRUE if the context menu is requested on an editable component.
  [propget] HRESULT IsEditable([out, retval] BOOL* value);

  /// Returns TRUE if the context menu was requested on the main frame and
  /// FALSE if invoked on another frame.
  [propget] HRESULT IsRequestedForMainFrame([out, retval] BOOL* value);

  /// Gets the uri of the page.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT PageUri([out, retval] LPWSTR* value);

  /// Gets the uri of the frame. Will match the PageUri if `IsRequestedForMainFrame` is TRUE.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT FrameUri([out, retval] LPWSTR* value);

  /// Returns TRUE if the context menu is requested on HTML containing an anchor tag.
  [propget] HRESULT HasLinkUri([out, retval] BOOL* value);

  /// Gets the uri of the link (if `HasLinkUri` is TRUE, null otherwise).
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT LinkUri([out, retval] LPWSTR* value);

  /// Returns TRUE if the context menu is requested on text element that contains an anchor tag.
  [propget] HRESULT HasLinkText([out, retval] BOOL* value);

  /// Gets the text of the link (if `HasLinkText` is TRUE, null otherwise).
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT LinkText([out, retval] LPWSTR * value);

  /// Returns TRUE if the context menu is requested on HTML containing a source uri.
  [propget] HRESULT HasSourceUri([out, retval] BOOL* value);

  /// Gets the active source uri of element (if `HasSourceUri` is TRUE, null otherwise).
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT SourceUri([out, retval] LPWSTR* value);

  /// Returns TRUE if the context menu is requested on a selection.
  [propget] HRESULT HasSelection([out, retval] BOOL* value);

  /// Gets the selected text (if `HasSelection` is TRUE, null otherwise).
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT SelectionText([out, retval] LPWSTR* value);
}

/// Event args for the `ContextMenuRequested` event. Will contain the selection information
/// and a collection of all of the default context menu items that the WebView
/// would show. Allows the app to draw its own context menu or add/remove
/// from the default context menu.
[uuid(a1d309ee-c03f-11eb-8529-0242ac130003), object, pointer_default(unique)]
interface ICoreWebView2ContextMenuRequestedEventArgs : IUnknown {
  /// Gets the collection of `ContextMenuItem` objects.
  /// See `ICoreWebView2ContextMenuItemCollection` for more details.
  [propget] HRESULT MenuItems([out, retval] ICoreWebView2ContextMenuItemCollection** value);

  /// Gets the target information associated with the requested context menu.
  /// See `ICoreWebView2ContextMenuTarget` for more details.
  [propget] HRESULT ContextMenuTarget([out, retval] ICoreWebView2ContextMenuTarget** value);

  /// Gets the coordinates where the context menu request occurred in relation to the upper
  /// left corner of the WebView bounds.
  [propget] HRESULT Location([out, retval] POINT* value);

  /// Sets the selected context menu item's command ID. When this is set,
  /// WebView will execute the selected command. This
  /// value should always be obtained via the selected `ContextMenuItem`'s `CommandId` property.
  /// The default value is -1 which means that no selection occurred. The app can
  /// also report the selected command ID for a custom context menu item, which
  /// will cause the `CustomItemSelected` event to be fired for the custom item, however
  /// while command IDs for each custom context menu item is unique
  /// during a ContextMenuRequested event, CoreWebView2 may reassign command ID
  /// values of deleted custom ContextMenuItems to new objects and the command
  /// ID assigned to the same custom item can be different between each app runtime.
  [propput] HRESULT SelectedCommandId([in] INT32 value);

  /// Gets the selected CommandId.
  [propget] HRESULT SelectedCommandId([out, retval] INT32* value);

  /// Sets whether the `ContextMenuRequested` event is handled by host after
  /// the event handler completes or if there is a deferral then after the deferral is completed.
  /// If `Handled` is set to TRUE then WebView will not display a context menu and will instead
  /// use the `SelectedCommandId` property to indicate which, if any, context menu item command to invoke.
  /// If after the event handler or deferral completes `Handled` is set to FALSE then WebView
  /// will display a context menu based on the contents of the `MenuItems` property.
  /// The default value is FALSE.
  [propput] HRESULT Handled([in] BOOL value);

  /// Gets whether the `ContextMenuRequested` event is handled by host.
  [propget] HRESULT Handled([out, retval] BOOL* value);

  /// Returns an `ICoreWebView2Deferral` object. Use this operation to
  /// complete the event when the custom context menu is closed.
  HRESULT GetDeferral([out, retval] ICoreWebView2Deferral** deferral);
}

/// This interface is used to manage profile options that created by 'CreateCoreWebView2ControllerOptions'.
///
/// \snippet AppWindow.cpp CreateControllerWithOptions
[uuid(12aae616-8ccb-44ec-bcb3-eb1831881635), object, pointer_default(unique)]
interface ICoreWebView2ControllerOptions : IUnknown {
  /// `ProfileName` property is to specify a profile name, which is only allowed to contain
  /// the following ASCII characters. It has a maximum length of 64 characters excluding the null-terminator.
  /// It is ASCII case insensitive.
  ///
  /// * alphabet characters: a-z and A-Z
  /// * digit characters: 0-9
  /// * and '#', '@', '$', '(', ')', '+', '-', '_', '~', '.', ' ' (space).
  ///
  /// Note: the text must not end with a period '.' or ' ' (space). And, although upper-case letters are
  /// allowed, they're treated just as lower-case counterparts because the profile name will be mapped to
  /// the real profile directory path on disk and Windows file system handles path names in a case-insensitive way.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT ProfileName([out, retval] LPWSTR* value);
  /// Sets the `ProfileName` property.
  [propput] HRESULT ProfileName([in] LPCWSTR value);

  /// `IsInPrivateModeEnabled` property is to enable/disable InPrivate mode.
  [propget] HRESULT IsInPrivateModeEnabled([out, retval] BOOL* value);
  /// Sets the `IsInPrivateModeEnabled` property.
  [propput] HRESULT IsInPrivateModeEnabled([in] BOOL value);
}

/// Provides a set of properties to configure a Profile object.
///
/// \snippet AppWindow.cpp OnCreateCoreWebView2ControllerCompleted
[uuid(79110ad3-cd5d-4373-8bc3-c60658f17a5f), object, pointer_default(unique)]
interface ICoreWebView2Profile : IUnknown {
  /// Name of the profile.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT ProfileName([out, retval] LPWSTR* value);

  /// InPrivate mode is enabled or not.
  [propget] HRESULT IsInPrivateModeEnabled([out, retval] BOOL* value);

  /// Full path of the profile directory.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT ProfilePath([out, retval] LPWSTR* value);

  /// Gets the `DefaultDownloadFolderPath` property. The default value is the
  /// system default download folder path for the user.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT DefaultDownloadFolderPath([out, retval] LPWSTR* value);

  /// Sets the `DefaultDownloadFolderPath` property. The default download folder
  /// path is persisted in the user data folder across sessions. The value
  /// should be an absolute path to a folder that the user and application can
  /// write to. Returns `E_INVALIDARG` if the value is invalid, and the default
  /// download folder path is not changed. Otherwise the path is changed
  /// immediately. If the directory does not yet exist, it is created at the
  /// time of the next download. If the host application does not have
  /// permission to create the directory, then the user is prompted to provide a
  /// new path through the Save As dialog. The user can override the default
  /// download folder path for a given download by choosing a different path in
  /// the Save As dialog.
  [propput] HRESULT DefaultDownloadFolderPath([in] LPCWSTR value);

  /// The PreferredColorScheme property sets the overall color scheme of the
  /// WebView2s associated with this profile. This sets the color scheme for
  /// WebView2 UI like dialogs, prompts, and context menus by setting the
  /// media feature `prefers-color-scheme` for websites to respond to.
  ///
  /// The default value for this is COREWEBVIEW2_PREFERRED_COLOR_AUTO,
  /// which will follow whatever theme the OS is currently set to.
  ///
  /// \snippet ViewComponent.cpp SetPreferredColorScheme
  /// Returns the value of the `PreferredColorScheme` property.
  [propget] HRESULT PreferredColorScheme(
    [out, retval] COREWEBVIEW2_PREFERRED_COLOR_SCHEME* value);

  /// Sets the `PreferredColorScheme` property.
  [propput] HRESULT PreferredColorScheme(
    [in] COREWEBVIEW2_PREFERRED_COLOR_SCHEME value);
}

/// Provides access to the certificate metadata.
[uuid(C5FB2FCE-1CAC-4AEE-9C79-5ED0362EAAE0), object, pointer_default(unique)]
interface ICoreWebView2Certificate : IUnknown {
  /// Subject of the certificate.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`. See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Subject([out, retval] LPWSTR* value);
  /// Name of the certificate authority that issued the certificate.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`. See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT Issuer([out, retval] LPWSTR* value);
  /// The valid start date and time for the certificate as the number of seconds since
  /// the UNIX epoch.
  [propget] HRESULT ValidFrom([out, retval] double* value);
  /// The valid expiration date and time for the certificate as the number of seconds since
  /// the UNIX epoch.
  [propget] HRESULT ValidTo([out, retval] double* value);
  /// DER encoded serial number of the certificate.
  /// Read more about DER at [RFC 7468 DER]
  /// (https://tools.ietf.org/html/rfc7468#appendix-B).
  ///
  /// The caller must free the returned string with `CoTaskMemFree`. See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT DerEncodedSerialNumber([out, retval] LPWSTR* value);
  /// Display name for a certificate.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`. See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings)
  [propget] HRESULT DisplayName([out, retval] LPWSTR* value);
  /// PEM encoded data for the certificate.
  /// Returns Base64 encoding of DER encoded certificate.
  /// Read more about PEM at [RFC 1421 Privacy Enhanced Mail]
  /// (https://tools.ietf.org/html/rfc1421).
  ///
  /// The caller must free the returned string with `CoTaskMemFree`. See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings)
  HRESULT ToPemEncoding([out, retval] LPWSTR* pemEncodedData);
  /// Collection of PEM encoded certificate issuer chain.
  /// In this collection first element is the current certificate followed by
  /// intermediate1, intermediate2...intermediateN-1. Root certificate is the
  /// last element in collection.
  [propget] HRESULT PemEncodedIssuerCertificateChain([out, retval]
      ICoreWebView2StringCollection** value);
}

/// An event handler for the `ServerCertificateErrorDetected` event.
[uuid(969B3A26-D85E-4795-8199-FEF57344DA22), object, pointer_default(unique)]
interface ICoreWebView2ServerCertificateErrorDetectedEventHandler : IUnknown {
  /// Provides the event args for the corresponding event.
  HRESULT Invoke([in] ICoreWebView2* sender,
      [in] ICoreWebView2ServerCertificateErrorDetectedEventArgs*
                     args);
}

/// Event args for the `ServerCertificateErrorDetected` event.
[uuid(012193ED-7C13-48FF-969D-A84C1F432A14), object, pointer_default(unique)]
interface ICoreWebView2ServerCertificateErrorDetectedEventArgs : IUnknown {
  /// The TLS error code for the invalid certificate.
  [propget] HRESULT ErrorStatus([out, retval] COREWEBVIEW2_WEB_ERROR_STATUS* value);

  /// URI associated with the request for the invalid certificate.
  ///
  /// The caller must free the returned string with `CoTaskMemFree`.  See
  /// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).
  [propget] HRESULT RequestUri([out, retval] LPWSTR* value);

  /// Returns the server certificate.
  [propget] HRESULT ServerCertificate([out, retval] ICoreWebView2Certificate** value);

  /// The action of the server certificate error detection.
  /// The default value is `COREWEBVIEW2_SERVER_CERTIFICATE_ERROR_ACTION_DEFAULT`.
  [propget] HRESULT Action([out, retval] COREWEBVIEW2_SERVER_CERTIFICATE_ERROR_ACTION* value);

  /// Sets the `Action` property.
  [propput] HRESULT Action([in] COREWEBVIEW2_SERVER_CERTIFICATE_ERROR_ACTION value);

  /// Returns an `ICoreWebView2Deferral` object. Use this operation to
  /// complete the event at a later time.
  HRESULT GetDeferral([out, retval] ICoreWebView2Deferral** deferral);
}

/// Receives the result of the `ClearServerCertificateErrorActions` method.
[uuid(3B40AAC6-ACFE-4FFD-8211-F607B96E2D5B), object, pointer_default(unique)]
interface ICoreWebView2ClearServerCertificateErrorActionsCompletedHandler : IUnknown {
  /// Provides the result of the corresponding asynchronous method.
  HRESULT Invoke([in] HRESULT errorCode);
}

/// Profile2 interface.
///
[uuid(fa740d4b-5eae-4344-a8ad-74be31925397), object, pointer_default(unique)]
interface ICoreWebView2Profile2: ICoreWebView2Profile {

  /// Clear browsing data based on a data type. This method takes two parameters,
  /// the first being a mask of one or more `COREWEBVIEW2_BROWSING_DATA_KINDS`. OR
  /// operation(s) can be applied to multiple `COREWEBVIEW2_BROWSING_DATA_KINDS` to
  /// create a mask representing those data types. The browsing data kinds that are
  /// supported are listed below. These data kinds follow a hierarchical structure in
  /// which nested bullet points are included in their parent bullet point's data kind.
  /// Ex: All DOM storage is encompassed in all site data which is encompassed in
  /// all profile data.
  /// * All Profile
  ///   * All Site Data
  ///     * All DOM Storage: File Systems, Indexed DB, Local Storage, Web SQL, Cache
  ///         Storage
  ///     * Cookies
  ///   * Disk Cache
  ///   * Download History
  ///   * General Autofill
  ///   * Password Autosave
  ///   * Browsing History
  ///   * Settings
  /// The completed handler will be invoked when the browsing data has been cleared and
  /// will indicate if the specified data was properly cleared. In the case in which
  /// the operation is interrupted and the corresponding data is not fully cleared
  /// the handler will return `E_ABORT` and otherwise will return `S_OK`.
  /// Because this is an asynchronous operation, code that is dependent on the cleared
  /// data must be placed in the callback of this operation.
  /// If the WebView object is closed before the clear browsing data operation
  /// has completed, the handler will be released, but not invoked. In this case
  /// the clear browsing data operation may or may not be completed.
  /// ClearBrowsingData clears the `dataKinds` regardless of timestamp.

  HRESULT ClearBrowsingData(
      [in] COREWEBVIEW2_BROWSING_DATA_KINDS dataKinds,
      [in] ICoreWebView2ClearBrowsingDataCompletedHandler* handler);

  /// ClearBrowsingDataInTimeRange behaves like ClearBrowsingData except that it
  /// takes in two additional parameters for the start and end time for which it
  /// should clear the data between.  The `startTime` and `endTime`
  /// parameters correspond to the number of seconds since the UNIX epoch.
  /// `startTime` is inclusive while `endTime` is exclusive, therefore the data will
  /// be cleared between [startTime, endTime).

  HRESULT ClearBrowsingDataInTimeRange(
      [in] COREWEBVIEW2_BROWSING_DATA_KINDS dataKinds,
      [in] double startTime,
      [in] double endTime,
      [in] ICoreWebView2ClearBrowsingDataCompletedHandler* handler);

  /// ClearBrowsingDataAll behaves like ClearBrowsingData except that it
  /// clears the entirety of the data associated with the profile it is called on.
  /// It clears the data regardless of timestamp.
  ///
  /// \snippet AppWindow.cpp ClearBrowsingData

  HRESULT ClearBrowsingDataAll(
    [in] ICoreWebView2ClearBrowsingDataCompletedHandler* handler);
}

/// The caller implements this interface to receive the ClearBrowsingData result.
[uuid(e9710a06-1d1d-49b2-8234-226f35846ae5), object, pointer_default(unique)]
interface ICoreWebView2ClearBrowsingDataCompletedHandler : IUnknown {

  /// Provide the completion status of the corresponding asynchronous method.
  HRESULT Invoke([in] HRESULT errorCode);
}

/// This interface is a handler for when the `Favicon` is changed.
/// The sender is the ICoreWebView2 object the top-level document of
/// which has changed favicon and the eventArgs is nullptr. Use the
/// FaviconUri property and GetFavicon method to obtain the favicon
/// data. The second argument is always null.
/// For more information see `add_FaviconChanged`.
[uuid(2913DA94-833D-4DE0-8DCA-900FC524A1A4), object, pointer_default(unique)]
interface ICoreWebView2FaviconChangedEventHandler : IUnknown {
  /// Called to notify the favicon changed. The event args are always null.
  HRESULT Invoke(
      [in] ICoreWebView2* sender,
      [in] IUnknown* args);
}

/// This interface is a handler for the completion of the population of
/// `imageStream`.
/// `errorCode` returns S_OK if the API succeeded.
/// The image is returned in the `faviconStream` object. If there is no image
/// then no data would be copied into the imageStream.
/// For more details, see the `GetFavicon` API.
[uuid(********-7DA8-49D7-8C05-FA125E4AEE8D), object, pointer_default(unique)]
interface ICoreWebView2GetFaviconCompletedHandler : IUnknown {
  /// Called to notify the favicon has been retrieved.
  HRESULT Invoke(
      [in] HRESULT errorCode,
      [in] IStream* faviconStream);
}

/// DLL export to create a WebView2 environment with a custom version of
/// WebView2 Runtime, user data folder, and with or without additional options.
///
/// When WebView2 experimental APIs are used, make sure to provide a valid `environmentOptions`
/// so that WebView2 runtime knows which version of the SDK that the app is using. Otherwise,
/// WebView2 runtime assumes that the version of the SDK being used is the latest
/// version known to it, which might not be the version of the SDK being used.
/// This wrong SDK version assumption could result in some experimental APIs not being available.
///
/// The WebView2 environment and all other WebView2 objects are single threaded
///  and have dependencies on Windows components that require COM to be
/// initialized for a single-threaded apartment.  The app is expected to run
/// `CoInitializeEx` before running `CreateCoreWebView2EnvironmentWithOptions`.
///
/// ```text
/// CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED);
/// ```
///
/// If `CoInitializeEx` did not run or previously ran with
/// `COINIT_MULTITHREADED`, `CreateCoreWebView2EnvironmentWithOptions` fails
/// with one of the following errors.
///
/// ```text
/// CO_E_NOTINITIALIZED -  if CoInitializeEx was not called
/// RPC_E_CHANGED_MODE  -  if CoInitializeEx was previously called with
///                        COINIT_MULTITHREADED
/// ```
///
///
/// Use `browserExecutableFolder` to specify whether WebView2 controls use a
/// fixed or installed version of the WebView2 Runtime that exists on a user
/// machine.  To use a fixed version of the WebView2 Runtime, pass the
/// folder path that contains the fixed version of the WebView2 Runtime to
/// `browserExecutableFolder`. BrowserExecutableFolder supports both relative
///  (to the application's executable) and absolute files paths.
/// To create WebView2 controls that use the
/// installed version of the WebView2 Runtime that exists on user machines,
/// pass a `null` or empty string to `browserExecutableFolder`.  In this
/// scenario, the API tries to find a compatible version of the WebView2
/// Runtime that is installed on the user machine (first at the machine level,
///  and then per user) using the selected channel preference.  The path of
/// fixed version of the WebView2 Runtime should not contain
/// `\Edge\Application\`. When such a path is used, the API fails
/// with `HRESULT_FROM_WIN32(ERROR_NOT_SUPPORTED)`.
///
/// The default channel search order is the WebView2 Runtime, Beta, Dev, and
/// Canary.  When an override `WEBVIEW2_RELEASE_CHANNEL_PREFERENCE` environment
///  variable or applicable `releaseChannelPreference` registry value is set to
///  `1`, the channel search order is reversed.
///
/// You may specify the `userDataFolder` to change the default user data
/// folder location for WebView2.  The path is either an absolute file path
/// or a relative file path that is interpreted as relative to the compiled
/// code for the current process.  For UWP apps, the default user data
/// folder is the app data folder for the package.  For non-UWP apps, the
/// default user data (`{Executable File Name}.WebView2`) folder is
/// created in the same directory next to the compiled code for the app.
/// WebView2 creation fails if the compiled code is running in a directory in
/// which the process does not have permission to create a new directory.  The
/// app is responsible to clean up the associated user data folder when it
/// is done.
///
/// \> [!NOTE]\n\> As a browser process may be shared among WebViews, WebView creation fails
/// with `HRESULT_FROM_WIN32(ERROR_INVALID_STATE)` if the specified options
/// does not match the options of the WebViews that are currently running in
/// the shared browser process.
///
/// `environmentCreatedHandler` is the handler result to the async operation
/// that contains the `WebView2Environment` that was created.
///
/// The `browserExecutableFolder`, `userDataFolder` and
/// `additionalBrowserArguments` of the `environmentOptions` may be overridden
/// by values either specified in environment variables or in the registry.
///
/// When creating a `WebView2Environment` the following environment variables
/// are verified.
///
/// ```text
/// WEBVIEW2_BROWSER_EXECUTABLE_FOLDER
/// WEBVIEW2_USER_DATA_FOLDER
/// WEBVIEW2_ADDITIONAL_BROWSER_ARGUMENTS
/// WEBVIEW2_RELEASE_CHANNEL_PREFERENCE
/// ```
///
/// If you find an override environment variable, use the
/// `browserExecutableFolder` and `userDataFolder` values as replacements for
/// the corresponding values in `CreateCoreWebView2EnvironmentWithOptions`
/// parameters.  If `additionalBrowserArguments` is specified in environment
/// variable or in the registry, it is appended to the corresponding values in
/// `CreateCoreWebView2EnvironmentWithOptions` parameters.
///
/// While not strictly overrides, additional environment variables may be set.
///
/// ```text
/// WEBVIEW2_WAIT_FOR_SCRIPT_DEBUGGER
/// ```
///
/// When found with a non-empty value, this indicates that the WebView is being
///  launched under a script debugger.  In this case, the WebView issues a
/// `Page.waitForDebugger` CDP command that runs the script inside the WebView
/// to pause on launch, until a debugger issues a corresponding
/// `Runtime.runIfWaitingForDebugger` CDP command to resume the runtime.
///
/// \> [!NOTE]\n\> The following environment variable does not have a registry key
/// equivalent: `WEBVIEW2_WAIT_FOR_SCRIPT_DEBUGGER`.
///
/// When found with a non-empty value, it indicates that the WebView is being
/// launched under a script debugger that also supports host apps that use
/// multiple WebViews.  The value is used as the identifier for a named pipe
/// that is opened and written to when a new WebView is created by the host
/// app.  The payload should match the payload of the `remote-debugging-port`
/// JSON target and an external debugger may use it to attach to a specific
/// WebView instance.  The format of the pipe created by the debugger should be
/// `\\.\pipe\WebView2\Debugger\{app_name}\{pipe_name}`, where the following
/// are true.
///
/// *   `{app_name}` is the host app exe file name, for example,
///     `WebView2Example.exe`
/// *   `{pipe_name}` is the value set for `WEBVIEW2_PIPE_FOR_SCRIPT_DEBUGGER`
///
/// To enable debugging of the targets identified by the JSON, you must set the
///  `WEBVIEW2_ADDITIONAL_BROWSER_ARGUMENTS` environment variable to send
/// `--remote-debugging-port={port_num}`, where the following is true.
///
/// *   `{port_num}` is the port on which the CDP server binds.
///
/// \> [!WARNING]\n\> If you set both `WEBVIEW2_PIPE_FOR_SCRIPT_DEBUGGER` and
/// `WEBVIEW2_ADDITIONAL_BROWSER_ARGUMENTS` environment variables, the
/// WebViews hosted in your app and associated contents may exposed to 3rd
/// party apps such as debuggers.
///
/// \> [!NOTE]\n\> The following environment variable does not have a registry key
/// equivalent: `WEBVIEW2_PIPE_FOR_SCRIPT_DEBUGGER`.
///
/// If none of those environment variables exist, then the registry is examined
/// next.  The following registry values are verified.
///
/// ```text
/// [{Root}]\Software\Policies\Microsoft\Edge\WebView2\BrowserExecutableFolder
/// "{AppId}"=""
///
/// [{Root}]\Software\Policies\Microsoft\Edge\WebView2\ReleaseChannelPreference
/// "{AppId}"=""
///
/// [{Root}]\Software\Policies\Microsoft\Edge\WebView2\AdditionalBrowserArguments
/// "{AppId}"=""
///
/// [{Root}]\Software\Policies\Microsoft\Edge\WebView2\UserDataFolder
/// "{AppId}"=""
/// ```
///
/// Use a group policy under **Administrative Templates** >
/// **Microsoft Edge WebView2** to configure `browserExecutableFolder` and
/// `releaseChannelPreference`.
///
/// In the unlikely scenario where some instances of WebView are open during a
/// browser update, the deletion of the previous WebView2 Runtime may be
/// blocked.  To avoid running out of disk space, a new WebView creation fails
/// with `HRESULT_FROM_WIN32(ERROR_DISK_FULL)` if it detects that too many
/// previous WebView2 Runtime versions exist.
///
/// The default maximum number of WebView2 Runtime versions allowed is `20`.
/// To override the maximum number of the previous WebView2 Runtime versions
/// allowed, set the value of the following environment variable.
///
/// ```text
/// WEBVIEW2_MAX_INSTANCES
/// ```
///
/// If the Webview depends upon an installed WebView2 Runtime version and it is
/// uninstalled, any subsequent creation fails with
/// `HRESULT_FROM_WIN32(ERROR_PRODUCT_UNINSTALLED)`.
///
/// First verify with Root as `HKLM` and then `HKCU`.  `AppId` is first set to
/// the Application User Model ID of the process, then if no corresponding
/// registry key, the `AppId` is set to the compiled code name of the process,
/// or if that is not a registry key then `*`.  If an override registry key is
/// found, use the `browserExecutableFolder` and `userDataFolder` registry
/// values as replacements and append `additionalBrowserArguments` registry
/// values for the corresponding values in
/// `CreateCoreWebView2EnvironmentWithOptions` parameters.
///
/// The following summarizes the possible error values that can be returned from
/// `CreateCoreWebView2EnvironmentWithOptions` and a description of why these
/// errors occur.
///
/// Error value                                     | Description
/// ----------------------------------------------- | --------------------------
/// `CO_E_NOTINITIALIZED`                           | CoInitializeEx was not called.
/// `RPC_E_CHANGED_MODE`                            | CoInitializeEx was previously called with COINIT_MULTITHREADED.
/// `HRESULT_FROM_WIN32(ERROR_NOT_SUPPORTED)`       | *\\Edge\\Application* path used in browserExecutableFolder.
/// `HRESULT_FROM_WIN32(ERROR_INVALID_STATE)`       | Specified options do not match the options of the WebViews that are currently running in the shared browser process.
/// `HRESULT_FROM_WIN32(ERROR_DISK_FULL)`           | In the unlikely scenario where some instances of WebView are open during a browser update, the deletion of the previous WebView2 Runtime may be blocked. To avoid running out of disk space, a new WebView creation fails with `HRESULT_FROM_WIN32(ERROR_DISK_FULL)` if it detects that too many previous WebView2 Runtime versions exist.
/// `HRESULT_FROM_WIN32(ERROR_PRODUCT_UNINSTALLED)` | If the Webview depends upon an installed WebView2 Runtime version and it is uninstalled.
/// `HRESULT_FROM_WIN32(ERROR_FILE_NOT_FOUND)`      | Could not find Edge installation.
/// `HRESULT_FROM_WIN32(ERROR_FILE_EXISTS)`         | User data folder cannot be created because a file with the same name already exists.
/// `E_ACCESSDENIED`                                | Unable to create user data folder, Access Denied.
/// `E_FAIL`                                        | Edge runtime unable to start.

cpp_quote("STDAPI CreateCoreWebView2EnvironmentWithOptions(PCWSTR browserExecutableFolder, PCWSTR userDataFolder, ICoreWebView2EnvironmentOptions* environmentOptions, ICoreWebView2CreateCoreWebView2EnvironmentCompletedHandler* environmentCreatedHandler);")

/// Creates an evergreen WebView2 Environment using the installed WebView2
/// Runtime version.  This is equivalent to running
/// `CreateCoreWebView2EnvironmentWithOptions` with `nullptr` for
/// `browserExecutableFolder`, `userDataFolder`, `additionalBrowserArguments`.
/// For more information, navigate to
/// `CreateCoreWebView2EnvironmentWithOptions`.

cpp_quote("STDAPI CreateCoreWebView2Environment(ICoreWebView2CreateCoreWebView2EnvironmentCompletedHandler* environmentCreatedHandler);")

/// Get the browser version info including channel name if it is not the
/// WebView2 Runtime.  Channel names are Beta, Dev, and Canary.
/// If an override exists for the `browserExecutableFolder` or the channel
/// preference, the override is used.  If an override is not specified, then
/// the parameter value passed to
/// `GetAvailableCoreWebView2BrowserVersionString` is used.
///
/// The caller must free the returned string with `CoTaskMemFree`.  See
/// [API Conventions](/microsoft-edge/webview2/concepts/win32-api-conventions#strings).

cpp_quote("STDAPI GetAvailableCoreWebView2BrowserVersionString(PCWSTR browserExecutableFolder, LPWSTR* versionInfo);")

/// This method is for anyone want to compare version correctly to determine
/// which version is newer, older or same.  Use it to determine whether
/// to use webview2 or certain feature based upon version.  Sets the value of
/// result to `-1`, `0` or `1` if `version1` is less than, equal or greater
/// than `version2` respectively.  Returns `E_INVALIDARG` if it fails to parse
/// any of the version strings or any input parameter is `null`.  Directly use
/// the `versionInfo` obtained from
/// `GetAvailableCoreWebView2BrowserVersionString` with input, channel
/// information is ignored.

cpp_quote("STDAPI CompareBrowserVersions(PCWSTR version1, PCWSTR version2, int* result);")

}
