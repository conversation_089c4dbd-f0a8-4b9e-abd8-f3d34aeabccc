﻿using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FormViewImageWX : BaseForm
    {
        private TextSelectableImageViewer imageViewer;
        private List<TextCellInfo> textRegions;
        private Label statusLabel;

        public FormViewImageWX()
        {
            Icon = FrmMain.FrmTool.Icon;
            Text = $"{"图像预览".CurrentText()}-{CommonString.FullName.CurrentText()}";
            ShowIcon = true;
            ShowInTaskbar = true;

            InitializeControls();

            // 设置窗口默认大小和居中显示
            Size = new Size(1200, 800);
            StartPosition = FormStartPosition.CenterScreen;
            MinimumSize = new Size(800, 600);
        }

        private void InitializeControls()
        {
            // 创建状态栏
            statusLabel = new Label
            {
                Text = "点击或拖拽选择文字",
                Dock = DockStyle.Bottom,
                Height = 25,
                TextAlign = ContentAlignment.MiddleLeft,
                BackColor = SystemColors.Control,
                BorderStyle = BorderStyle.FixedSingle
            };
            Controls.Add(statusLabel);

            // 创建分割容器
            var splitContainer = new SplitContainer
            {
                Dock = DockStyle.Fill,
                Orientation = Orientation.Vertical,
                SplitterDistance = 700, // 左侧图片区域更宽
                FixedPanel = FixedPanel.None,
                SplitterWidth = 4
            };

            // 创建自定义图片查看器
            imageViewer = new TextSelectableImageViewer
            {
                Dock = DockStyle.Fill
            };

            // 订阅文字选择事件
            imageViewer.TextSelectionChanged += OnTextSelectionChanged;

            splitContainer.Panel1.Controls.Add(imageViewer);

            // 创建文字显示区域（使用RichTextBox支持精确文字高亮）
            var textRichTextBox = new RichTextBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 11),
                BackColor = Color.FromArgb(45, 45, 48),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.None,
                ReadOnly = true,
                WordWrap = true,
                ScrollBars = RichTextBoxScrollBars.Vertical
            };

            splitContainer.Panel2.Controls.Add(textRichTextBox);
            imageViewer.TextRichTextBox = textRichTextBox;

            Controls.Add(splitContainer);
        }

        private void OnTextSelectionChanged(object sender, TextSelectionEventArgs e)
        {
            if (!string.IsNullOrEmpty(e.SelectedText))
            {
                var displayText = e.SelectedText.Length > 50 ? e.SelectedText.Substring(0, 50) + "..." : e.SelectedText;
                statusLabel.Text = $"已选择 {e.SelectedCharacterCount} 个字符: {displayText}";

                // 复制到剪贴板
                try
                {
                    ClipboardService.SetText(e.SelectedText);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"复制到剪贴板失败: {ex.Message}");
                }
            }
            else
            {
                statusLabel.Text = "点击或拖拽选择文字";
            }
        }

        internal void Bind(Image image, List<TextCellInfo> regions)
        {
            textRegions = regions ?? new List<TextCellInfo>();
            Text = $"{"图像预览".CurrentText()} {"尺寸".CurrentText()}:{image?.Width}×{image?.Height} - {CommonString.FullName.CurrentText()}";

            // 绑定图片和文字区域到查看器
            imageViewer.BindImageAndTextRegions(image, textRegions);

            // 根据图片大小动态调整窗口大小
            AdjustWindowSizeForImage(image);
        }

        /// <summary>
        /// 根据图片大小动态调整窗口大小
        /// </summary>
        private void AdjustWindowSizeForImage(Image image)
        {
            if (image == null) return;

            // 计算合适的窗口大小
            var screenSize = Screen.PrimaryScreen.WorkingArea.Size;
            var maxWidth = (int)(screenSize.Width * 0.85); // 最大宽度为屏幕的85%
            var maxHeight = (int)(screenSize.Height * 0.80); // 最大高度为屏幕的80%

            // 基础UI元素的空间
            var uiElementsHeight = 80; // 状态栏等UI元素的高度
            var rightPanelMinWidth = 300; // 右侧文字区域的最小宽度
            var splitterWidth = 8; // 分隔栏宽度
            var padding = 30; // 内边距

            // 计算图片的合适显示尺寸
            var imageDisplayWidth = image.Width;
            var imageDisplayHeight = image.Height;

            // 如果图片太大，按比例缩小
            var maxImageWidth = maxWidth - rightPanelMinWidth - splitterWidth - padding;
            var maxImageHeight = maxHeight - uiElementsHeight - padding;

            if (imageDisplayWidth > maxImageWidth || imageDisplayHeight > maxImageHeight)
            {
                var scaleX = (double)maxImageWidth / imageDisplayWidth;
                var scaleY = (double)maxImageHeight / imageDisplayHeight;
                var scale = Math.Min(scaleX, scaleY);

                imageDisplayWidth = (int)(imageDisplayWidth * scale);
                imageDisplayHeight = (int)(imageDisplayHeight * scale);
            }

            // 根据图片实际显示尺寸计算窗口大小
            var leftPanelWidth = Math.Max(imageDisplayWidth + 40, 400); // 图片区域加边距，最小400
            var rightPanelWidth = rightPanelMinWidth;

            // 如果图片很小，适当减少右侧面板宽度，让整体更紧凑
            if (imageDisplayWidth < 500)
            {
                rightPanelWidth = Math.Max(250, rightPanelMinWidth - 50);
            }

            var targetWidth = leftPanelWidth + rightPanelWidth + splitterWidth + padding;
            var targetHeight = Math.Max(imageDisplayHeight + uiElementsHeight + padding, 600);

            // 限制在屏幕范围内
            targetWidth = Math.Min(targetWidth, maxWidth);
            targetHeight = Math.Min(targetHeight, maxHeight);

            // 确保窗口不会太小
            targetWidth = Math.Max(targetWidth, 800);
            targetHeight = Math.Max(targetHeight, 600);

            // 设置窗口大小
            Size = new Size(targetWidth, targetHeight);

            // 重新居中显示
            CenterToScreen();

            // 调整分隔栏位置，确保图片能合适显示
            var splitContainer = Controls.OfType<SplitContainer>().FirstOrDefault();
            if (splitContainer != null)
            {
                // 重新计算实际的左侧面板宽度
                var actualLeftWidth = targetWidth - rightPanelWidth - splitterWidth - 20;
                splitContainer.SplitterDistance = Math.Max(actualLeftWidth, 350);
            }
        }

        /// <summary>
        /// 从OCR内容创建并显示文字选择窗体
        /// </summary>
        /// <param name="ocrContent">OCR识别结果</param>
        /// <param name="image">原始图片</param>
        public static void ShowWithOcrContent(OcrContent ocrContent, Image image)
        {
            if (ocrContent?.result == null || image == null) return;

            // 从verticalText中获取TextCellInfo列表
            var textCells = new List<TextCellInfo>();
            if (!string.IsNullOrEmpty(ocrContent.result.verticalText))
            {
                try
                {
                    textCells = ocrContent.result.verticalText.DeserializeJson<List<TextCellInfo>>();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"解析verticalText失败: {ex.Message}");
                }
            }

            // 创建并显示窗体
            var form = new FormViewImageWX();
            form.Bind(image, textCells);
            form.Show();
        }
    }
}
