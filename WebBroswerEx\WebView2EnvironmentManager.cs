using System;
using System.Reflection;
using System.Threading.Tasks;

namespace OCRTools.WebBroswerEx
{
    /// <summary>
    /// WebView2环境实例管理器 - 负责全局环境的创建和管理
    /// 从 ApplicationWebManager 中分离出来，专门管理环境实例
    /// </summary>
    internal static class WebView2EnvironmentManager
    {
        private static object _webView2Environment = null;
        private static bool _isCreating = false;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// 获取或创建WebView2环境实例
        /// 只负责环境创建，不做可用性检查（由调用方保证）
        /// </summary>
        public static async Task<object> GetOrCreateEnvironmentAsync()
        {
            // 如果已经有环境实例，直接返回
            if (_webView2Environment != null) return _webView2Environment;

            lock (_lockObject)
            {
                if (_isCreating) return null; // 避免重复创建
                _isCreating = true;
            }

            try
            {
                var createAsyncMethod = WebView2DynamicManager.CoreEnvironmentType.GetMethod("CreateAsync", BindingFlags.Public | BindingFlags.Static);
                if (createAsyncMethod == null) return null;

                var task = createAsyncMethod.Invoke(null, new object[createAsyncMethod.GetParameters().Length]);
                if (task is Task taskObj)
                {
                    await taskObj;
                    
                    dynamic dynamicTask = task;
                    _webView2Environment = dynamicTask.Result;
                }

                return _webView2Environment;
            }
            catch (Exception ex)
            {
                return null;
            }
            finally
            {
                _isCreating = false;
            }
        }

        /// <summary>
        /// 清理环境实例
        /// </summary>
        public static void DisposeEnvironment()
        {
            try
            {
                _webView2Environment = null;
            }
            catch
            {
                // 静默处理清理异常
            }
        }
    }
}
