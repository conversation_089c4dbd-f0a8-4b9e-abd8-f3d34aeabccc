﻿using OCRTools.Common;
using System;
using System.Collections.Specialized;
using System.IO;
using System.Net;

namespace ImageLib
{
    /// <summary>
    /// </summary>
    public class TouTiaoUpload
    {
        public static bool Enable { get; set; } = true;

        public static string GetResult(byte[] content)
        {
            var result = GetFromAiWen(content);
            if (!string.IsNullOrEmpty(result))
            {
                result += "?1.png";
            }
            return result;
        }

        private const string strFileNameSpilt = "\"url\":\"";

        private static string GetFromAiWen(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://www.toutiao.com/mp/agw/article_material/photo/upload_picture/?without_check=1&type=ueditor&pgc_watermark=1&action=uploadimage&encode=utf-8";
                var file = new UploadFileInfo()
                {
                    Name = "upfile",
                    Filename = "1.jpg",
                    ContentType = "image/jpeg",
                    Stream = new MemoryStream(content)
                };
                var vaules = new NameValueCollection() {
                    { "type", "image/jpeg" }
                };
                var html = string.Empty;
                try
                {
                    html = UploadFileRequest.Post(url, new[] { file }, vaules);
                }
                catch (BadApiException exception)
                {
                    switch (exception.Code)
                    {
                        case HttpStatusCode.MethodNotAllowed: //405
                        case HttpStatusCode.Unauthorized://401
                        case HttpStatusCode.NotFound: //404
                            Enable = false;
                            break;
                    }
                }
                catch { }
                if (html.Contains(strFileNameSpilt))
                {
                    result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
                }
            }
            catch (Exception oe)
            {

            }
            return result;
        }
    }
}
