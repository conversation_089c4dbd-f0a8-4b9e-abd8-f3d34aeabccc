﻿using System;
using System.IO;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace OCRTools
{
    public static class Log
    {
        static Type telemetryType;
        static object telemetryClient;
        static Type eventTelemetryType;
        static Type exceptionTelemetryType;

        public static void InitInsight()
        {
            Task.Factory.StartNew(() =>
            {
                var assembly = CommonString.LoadDllByName("未能加载文件或程序集“Microsoft.ApplicationInsights,");
                if (assembly != null)
                {
                    Type configType = assembly.GetType("Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration");
                    object config = configType.InvokeMember("CreateDefault", BindingFlags.InvokeMethod | BindingFlags.Static | BindingFlags.Public, null, null, null);
                    configType.GetMethod("set_InstrumentationKey", new Type[] { typeof(string) })?.Invoke(config, new object[] { "85f0eb3d-ee17-48b2-912a-195caf0207e9" });
                    telemetryType = assembly.GetType("Microsoft.ApplicationInsights.TelemetryClient");
                    telemetryClient = Activator.CreateInstance(telemetryType, config);
                    eventTelemetryType = assembly.GetType("Microsoft.ApplicationInsights.DataContracts.EventTelemetry");
                    exceptionTelemetryType = assembly.GetType("Microsoft.ApplicationInsights.DataContracts.ExceptionTelemetry");
                    TrackEvent("程序初始化成功");
                }
            });
        }

        static bool IsInsightInit()
        {
            return telemetryClient != null && telemetryType != null;
        }

        public static void TrackEvent(string eventMsg)
        {
            if (!IsInsightInit())
            {
                return;
            }
            try
            {
                var eventTelemetryInstance = Activator.CreateInstance(eventTelemetryType);
                eventTelemetryType.GetProperty("Name").SetValue(eventTelemetryInstance, eventMsg);
                telemetryType?.GetMethod("TrackEvent", new Type[] { eventTelemetryType })?.Invoke(telemetryClient, new object[] { eventTelemetryInstance });
            }
            catch { }
        }

        static bool TrackException(Exception oe, string eventMsg = "")
        {
            var result = false;
            if (!IsInsightInit())
            {
                return result;
            }
            try
            {
                var eventTelemetryInstance = Activator.CreateInstance(exceptionTelemetryType);
                exceptionTelemetryType.GetProperty("Exception").SetValue(eventTelemetryInstance, oe);
                exceptionTelemetryType.GetProperty("Message").SetValue(eventTelemetryInstance, string.IsNullOrEmpty(eventMsg) ? oe.Message : eventMsg);
                telemetryType?.GetMethod("TrackException", new Type[] { exceptionTelemetryType })?.Invoke(telemetryClient, new object[] { eventTelemetryInstance });
                result = true;
            }
            catch { }
            return result;
        }

        public static void FlushInsight()
        {
            if (!IsInsightInit())
            {
                return;
            }
            try
            {
                telemetryType?.GetMethod("Flush").Invoke(telemetryClient, null);
            }
            catch { }

        }

        private static readonly object Obj = new object();

        /// <summary>
        ///     操作日志
        /// </summary>
        public static void WriteLog(string content)
        {
            WriteLogs(content, "操作日志");
        }

        /// <summary>
        ///     错误日志
        /// </summary>
        public static void WriteError(string strMsg, Exception oe)
        {
            var traceNet = false;
            try
            {
                traceNet = TrackException(oe, strMsg);
            }
            catch { }
            if (!traceNet)
            {
                var strTmp = "";
                if (!string.IsNullOrEmpty(strMsg)) strTmp = string.Format("\n{0}", strMsg);
                try
                {
                    if (oe != null)
                        strTmp += string.Format("\nMessage:{0}\nStackTrace:{1}\nTargetSite:{2}", oe.Message, oe.StackTrace,
                            oe.TargetSite);
                }
                catch { }
                WriteLogs(strTmp, "错误日志");
            }
        }

        private static void WriteLogs(string content, string type)
        {
            if (string.IsNullOrEmpty(content))
                return;
            try
            {
                lock (Obj)
                {
                    var path = AppDomain.CurrentDomain.BaseDirectory;
                    if (!string.IsNullOrEmpty(path))
                    {
                        path = AppDomain.CurrentDomain.BaseDirectory + "Logs";
                        if (!Directory.Exists(path)) Directory.CreateDirectory(path);
                        path = path + "\\" + ServerTime.DateTime.ToString("yyMM");
                        if (!Directory.Exists(path)) Directory.CreateDirectory(path);
                        path = path + "\\" + ServerTime.DateTime.ToString("dd") + ".txt";
                        if (!File.Exists(path))
                        {
                            var fs = File.Create(path);
                            fs.Close();
                        }

                        if (File.Exists(path))
                        {
                            var sw = new StreamWriter(path, true, Encoding.Default);
                            sw.WriteLine(ServerTime.DateTime);
                            sw.WriteLine("日志类型：" + type);
                            sw.WriteLine("详情：" + content);
                            sw.WriteLine("----------------------------------------");
                            sw.Close();
                        }
                    }
                }
            }
            catch
            {
            }
        }
    }
}