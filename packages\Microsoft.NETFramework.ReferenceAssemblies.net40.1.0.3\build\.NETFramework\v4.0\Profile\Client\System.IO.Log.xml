﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.Log</name>
  </assembly>
  <members>
    <member name="T:System.IO.Log.FileRecordSequence">
      <summary>Implements an <see cref="T:System.IO.Log.IRecordSequence" /> on top of a file. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.FileRecordSequence" /> class with a specified file. </summary>
      <param name="path">A relative or absolute path for the file that this <see cref="T:System.IO.Log.FileRecordSequence" /> instance will encapsulate. </param>
      <exception cref="T:System.ArgumentException">The file specified by <paramref name="path" /> is not valid.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <paramref name="path" /> cannot be found.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.#ctor(System.String,System.IO.FileAccess)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.FileRecordSequence" /> class with a specified file and an access mode.</summary>
      <param name="path">A relative or absolute path for the file that this <see cref="T:System.IO.Log.FileRecordSequence" /> instance will encapsulate.</param>
      <param name="access">A valid <see cref="T:System.IO.FileAccess" /> value that controls the kind of access users have to a log file.</param>
      <exception cref="T:System.ArgumentException">The file specified by <paramref name="path" /> is not valid.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <paramref name="path" /> cannot be found.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.#ctor(System.String,System.IO.FileAccess,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.FileRecordSequence" /> class with a specified file, an access mode, and a file size.</summary>
      <param name="path">A relative or absolute path for the file that this <see cref="T:System.IO.Log.FileRecordSequence" /> instance will encapsulate.</param>
      <param name="access">A valid <see cref="T:System.IO.FileAccess" /> value that controls the kind of access users have to a log file.</param>
      <param name="size">The size of the log file to be opened.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the parameters are out of the valid range.</exception>
      <exception cref="T:System.ArgumentException">The file specified by <paramref name="path" /> is not valid.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <paramref name="path" /> cannot be found.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.AdvanceBaseSequenceNumber(System.IO.Log.SequenceNumber)">
      <summary>Moves the base sequence number of the log forward. This method cannot be inherited.</summary>
      <param name="newBaseSequenceNumber">The new base sequence number of the record sequence.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="newBaseSequenceNumber" /> is invalid.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="newBaseSequenceNumber" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while modifying the sequence. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.Append(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions)">
      <summary>Writes a log record to the <see cref="T:System.IO.Log.FileRecordSequence" />. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the arguments are out of range.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.Append(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection)">
      <summary>Writes a log record to the <see cref="T:System.IO.Log.FileRecordSequence" />, using space previously reserved in the sequence. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written. </param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this record. </param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the arguments are out of range.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reservations" /> was not created by this record sequence..</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.Append(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions)">
      <summary>Writes a log record to the <see cref="T:System.IO.Log.FileRecordSequence" />. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written. </param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the arguments are out of range.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.Append(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection)">
      <summary>Writes a log record to the <see cref="T:System.IO.Log.FileRecordSequence" />, using space previously reserved in the sequence. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written. </param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this record. </param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the arguments are out of range.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reservations" /> was not created by this record sequence..</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
    </member>
    <member name="P:System.IO.Log.FileRecordSequence.BaseSequenceNumber">
      <summary>Gets the sequence number of the first valid record in the current <see cref="T:System.IO.Log.FileRecordSequence" />. </summary>
      <returns>The lowest sequence number that corresponds to a valid record in the <see cref="T:System.IO.Log.FileRecordSequence" />. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.BeginAppend(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous append operation. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous append, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests. </param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the arguments are out of range.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.BeginAppend(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous append operation using space previously reserved in the sequence. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous append, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this record. </param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests. </param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the arguments are out of range.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reservations" /> was not created by this record sequence.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.BeginAppend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous append operation. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous append, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete.</param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests.</param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the arguments are out of range.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.BeginAppend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous append operation using space previously reserved in the sequence. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous append, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this record. </param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests. </param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the arguments are out of range.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reservations" /> was not created by this record sequence.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.BeginFlush(System.IO.Log.SequenceNumber,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous flush operation, using space previously reserved in the sequence. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous flush operation, which could still be pending. </returns>
      <param name="sequenceNumber">The sequence number of the latest record that must be written. If <see cref="T:System.IO.Log.SequenceNumber" /> is invalid, then all records must be written. </param>
      <param name="callback">An optional asynchronous callback, to be called when the flush is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous flush request from other requests. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid.<paramref name="sequenceNumber" /> is not valid for this sequence. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="sequenceNumber" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while flushing the data. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.BeginReserveAndAppend(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.Int64[],System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous reserve and append operation. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents this asynchronous operation, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservationCollection">The reservation collection to make reservations in. </param>
      <param name="reservations">The reservations to make, in bytes. </param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests. </param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the arguments are out of range.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reservations" /> was not created by this record sequence.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.BeginReserveAndAppend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.Int64[],System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous reserve and append operation. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents this asynchronous operation, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservationCollection">The reservation collection to make reservations in. </param>
      <param name="reservations">The reservations to make, in bytes. </param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests. </param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the arguments are out of range.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reservations" /> was not created by this record sequence.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.BeginWriteRestartArea(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.ReservationCollection,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous restart area write operation, using space previously reserved in the sequence. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous restart area write operation, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="newBaseSeqNum">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number. </param>
      <param name="reservation">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this restart area. </param>
      <param name="callback">An optional asynchronous callback, to be called when the restart area write is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous restart area write request from other requests. </param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="newBaseSeqNum" /> is not between the base and last sequence numbers of this sequence.-or-A new or existing archive tail or base of the active log is invalid.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reservations" /> was not created by this record sequence.-or-<paramref name="newBaseSeqNum" /> is not valid for this sequence.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.BeginWriteRestartArea(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.ReservationCollection,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous restart area write operation, using space previously reserved in the sequence. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous restart area write operation, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="newBaseSeqNum">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number. </param>
      <param name="reservation">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this restart area. </param>
      <param name="callback">An optional asynchronous callback, to be called when the restart area write is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous restart area write request from other requests. </param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="newBaseSeqNum" /> is not between the base and last sequence numbers of this sequence.-or-A new or existing archive tail or base of the active log is invalid.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reservations" /> was not created by this record sequence.-or-<paramref name="newBaseSeqNum" /> is not valid for this sequence.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.CreateReservationCollection">
      <summary>Creates a new <see cref="T:System.IO.Log.ReservationCollection" />. This method cannot be inherited.</summary>
      <returns>The newly created <see cref="T:System.IO.Log.ReservationCollection" />. </returns>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.Dispose">
      <summary>Immediately releases the unmanaged resources used by an object.</summary>
      <exception cref="T:System.IO.IOException">An I/O error occurred while flushing the data. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.EndAppend(System.IAsyncResult)">
      <summary>Ends an asynchronous append operation. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="result">A reference to the outstanding asynchronous I/O request.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="result" /> is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">End has already been called for this asynchronous operation.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.EndFlush(System.IAsyncResult)">
      <summary>Ends an asynchronous flush operation. This method cannot be inherited.</summary>
      <returns>The sequence number of the last record written. </returns>
      <param name="result">A reference to the outstanding asynchronous I/O request.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="result" /> is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">End has already been called for this asynchronous operation.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.EndReserveAndAppend(System.IAsyncResult)">
      <summary>Ends an asynchronous reserve and append operation. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="result">A reference to the outstanding asynchronous I/O request.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="result" /> is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">End has already been called for this asynchronous operation.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.EndWriteRestartArea(System.IAsyncResult)">
      <summary>Ends an asynchronous restart area write operation. This method cannot be inherited.</summary>
      <returns>The sequence number of the written log record. </returns>
      <param name="result">A reference to the outstanding asynchronous I/O request.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="result" /> is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">End has already been called for this asynchronous operation.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.Flush">
      <summary>Ensures that all appended records have been written. This method cannot be inherited.</summary>
      <returns>The sequence number of the last record written. </returns>
      <exception cref="T:System.IO.IOException">An I/O error occurred while flushing the data. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.Flush(System.IO.Log.SequenceNumber)">
      <summary>Ensures that all appended records have been written. This method cannot be inherited.</summary>
      <returns>The sequence number of the last record written. </returns>
      <param name="sequenceNumber">The sequence number of the latest record that must be written. If this <see cref="T:System.IO.Log.SequenceNumber" /> is invalid, then all records must be written. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="sequenceNumber" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sequenceNumber" /> is not valid for this sequence.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while flushing the data. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
    </member>
    <member name="P:System.IO.Log.FileRecordSequence.LastSequenceNumber">
      <summary>Gets the sequence number which is greater than the last record appended.</summary>
      <returns>A sequence number which is greater than the last record appended. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="P:System.IO.Log.FileRecordSequence.MaximumRecordLength">
      <summary>Gets the size of the largest record that can be appended to or read from this sequence, in bytes.</summary>
      <returns>The size of the largest record that can be appended to or read from this sequence, in bytes. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.ReadLogRecords(System.IO.Log.SequenceNumber,System.IO.Log.LogRecordEnumeratorType)">
      <summary>Returns an enumerable collection of records in the sequence. This method cannot be inherited.</summary>
      <returns>An enumerable collection of records in the sequence. </returns>
      <param name="start">The sequence number of the first record where the reading starts.</param>
      <param name="logRecordEnum">A valid <see cref="T:System.IO.Log.LogRecordEnumeratorType" /> value that specifies the manner (that is, forward or backward) in which records should be read from a <see cref="T:System.IO.Log.LogRecordSequence" />. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the arguments are out of range.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with write-only access.</exception>
      <exception cref="T:System.IO.IOException">The record sequence is corrupted.-or-The record was written with an incompatible version of the record sequence.</exception>
      <exception cref="T:System.InvalidOperationException">The enumeration has ended.-or-The enumeration has not been started. A call to <see cref="M:System.Collections.IEnumerator.MoveNext" /> must be made.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.ReadRestartAreas">
      <summary>Returns an enumerable collection of the restart areas in the sequence. This method cannot be inherited.</summary>
      <returns>An enumerable collection of the restart areas in the sequence. </returns>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with write-only access.</exception>
      <exception cref="T:System.IO.IOException">The record sequence is corrupted.-or-The record was written with an incompatible version of the record sequence.</exception>
      <exception cref="T:System.InvalidOperationException">The enumeration has ended.-or-The enumeration has not been started. A call to <see cref="M:System.Collections.IEnumerator.MoveNext" /> must be made.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.ReserveAndAppend(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.Int64[])">
      <summary>Automatically makes a single reservation and appends a record to the sequence. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written. </param>
      <param name="reservationCollection">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the collection to make reservations in.</param>
      <param name="reservations">The reservations to make, in bytes.</param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the arguments are out of range.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reservations" /> was not created by this record sequence..</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.ReserveAndAppend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.Int64[])">
      <summary>Automatically makes a single reservation and appends a record to the sequence. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservationCollection">The reservation collection to make reservations in. </param>
      <param name="reservations">The reservations to make, in bytes. </param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the arguments are out of range.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reservations" /> was not created by this record sequence..</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Log.FileRecordSequence.ReservedBytes">
      <summary>Gets the total number of bytes that have been reserved. </summary>
      <returns>The total size of all reservations made in this record sequence.</returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Log.FileRecordSequence.RestartSequenceNumber">
      <summary>Gets the sequence number of the most recently written restart area. </summary>
      <returns>The sequence number of the most recently written restart area. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="P:System.IO.Log.FileRecordSequence.RetryAppend">
      <summary>Gets or sets a value indicating whether or not appends are automatically retried if the log is full. </summary>
      <returns>true if appends are automatically retried if the log is full; otherwise, false. The default is true. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="E:System.IO.Log.FileRecordSequence.TailPinned">
      <summary>Occurs when the record sequence determines that the tail must be moved forward. This method cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.WriteRestartArea(System.ArraySegment{System.Byte})">
      <summary>Writes a restart area to the <see cref="T:System.IO.Log.FileRecordSequence" />. This method cannot be inherited.</summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.WriteRestartArea(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber)">
      <summary>Writes a restart area to the <see cref="T:System.IO.Log.FileRecordSequence" />. This method cannot be inherited.</summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="newBaseSeqNum">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number.</param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="newBaseSeqNum" /> is not between the base and last sequence numbers of this sequence.-or-A new or existing archive tail or base of the active log is invalid.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="newBaseSeqNum" /> is not valid for this sequence.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.WriteRestartArea(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.ReservationCollection)">
      <summary>Writes a restart area to the <see cref="T:System.IO.Log.FileRecordSequence" />. This method cannot be inherited.</summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="newBaseSeqNum">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number.</param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this restart area.</param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="newBaseSeqNum" /> is not between the base and last sequence numbers of this sequence.-or-A new or existing archive tail or base of the active log is invalid.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reservations" /> was not created by this record sequence.-or-<paramref name="newBaseSeqNum" /> is not valid for this sequence.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.WriteRestartArea(System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
      <summary>Writes a restart area to the <see cref="T:System.IO.Log.FileRecordSequence" />. This method cannot be inherited.</summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.WriteRestartArea(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber)">
      <summary>Writes a restart area to the <see cref="T:System.IO.Log.FileRecordSequence" />. This method cannot be inherited.</summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="newBaseSeqNum">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number.</param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="newBaseSeqNum" /> is not between the base and last sequence numbers of this sequence.-or-A new or existing archive tail or base of the active log is invalid.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="newBaseSeqNum" /> is not valid for this sequence.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
    </member>
    <member name="M:System.IO.Log.FileRecordSequence.WriteRestartArea(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.ReservationCollection)">
      <summary>Writes a restart area to the <see cref="T:System.IO.Log.FileRecordSequence" />. This method cannot be inherited.</summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="newBaseSeqNum">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number.</param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this restart area.</param>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="newBaseSeqNum" /> is not between the base and last sequence numbers of this sequence.-or-A new or existing archive tail or base of the active log is invalid.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reservations" /> was not created by this record sequence.-or-<paramref name="newBaseSeqNum" /> is not valid for this sequence.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.IO.IOException">The request cannot be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
    </member>
    <member name="T:System.IO.Log.FileRegion">
      <summary>Represents a region of a file to be archived. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.IO.Log.FileRegion.FileLength">
      <summary>Gets the length of the file in bytes.</summary>
      <returns>The length of the file in bytes. </returns>
    </member>
    <member name="M:System.IO.Log.FileRegion.GetStream">
      <summary>Returns a stream that can be used to read the data to be archived.</summary>
      <returns>A stream that contains the data to be archived. </returns>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.NotSupportedException">The operation is not supported. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="P:System.IO.Log.FileRegion.Offset">
      <summary>Gets the offset into the file where the data begins.</summary>
      <returns>The offset into the file where the data begins.</returns>
    </member>
    <member name="P:System.IO.Log.FileRegion.Path">
      <summary>Gets the fully qualified location of the file containing this region.</summary>
      <returns>The fully qualified location of the file containing this region. </returns>
    </member>
    <member name="T:System.IO.Log.IRecordSequence">
      <summary>Provides a generic interface to a sequence of records. </summary>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.AdvanceBaseSequenceNumber(System.IO.Log.SequenceNumber)">
      <summary>Moves the base sequence number of the log forward. </summary>
      <param name="newBaseSequenceNumber">The new base sequence number of the record sequence.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="newBaseSequenceNumber" /> is invalid.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="newBaseSequenceNumber" /> is not active in the log.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while modifying the sequence. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.Append(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions)">
      <summary>When overridden in a derived class, writes a log record to the <see cref="T:System.IO.Log.IRecordSequence" />. </summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while appending the record. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.Append(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection)">
      <summary>When overridden in a derived class, appends a log record to the <see cref="T:System.IO.Log.IRecordSequence" />, using space previously reserved in the sequence. </summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written. </param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this record. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while appending the record. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.Append(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions)">
      <summary>When overridden in a derived class, appends a log record to the <see cref="T:System.IO.Log.IRecordSequence" />. </summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while appending the record. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.Append(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection)">
      <summary>When overridden in a derived class, appends a log record to the <see cref="T:System.IO.Log.IRecordSequence" />, using space previously reserved in the sequence. </summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written. </param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this record. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while appending the record. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="P:System.IO.Log.IRecordSequence.BaseSequenceNumber">
      <summary>When overridden in a derived class, gets the sequence number of the first valid record in the current <see cref="T:System.IO.Log.IRecordSequence" />. </summary>
      <returns>The lowest sequence number that corresponds to a valid record in the <see cref="T:System.IO.Log.IRecordSequence" />. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.BeginAppend(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.AsyncCallback,System.Object)">
      <summary>When overridden in a derived class, begins an asynchronous append operation. </summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous append, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while appending the record. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.BeginAppend(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.AsyncCallback,System.Object)">
      <summary>When overridden in a derived class, begins an asynchronous append operation using space previously reserved in the sequence.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous append, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this record. </param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while appending the record. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.BeginAppend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.AsyncCallback,System.Object)">
      <summary>When overridden in a derived class, begins an asynchronous append operation, using space previously reserved in the sequence. </summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous append, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete.</param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests.</param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while appending the record. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.BeginAppend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.AsyncCallback,System.Object)">
      <summary>When overridden in a derived class, begins an asynchronous append operation, using space previously reserved in the sequence. </summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous append, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousUndoRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this record. </param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while appending the record. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.BeginFlush(System.IO.Log.SequenceNumber,System.AsyncCallback,System.Object)">
      <summary>When overridden in a derived class, begins an asynchronous flush operation, using space previously reserved in the sequence. </summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous flush operation, which could still be pending. </returns>
      <param name="sequenceNumber">The sequence number of the latest record that must be written. If this <see cref="T:System.IO.Log.SequenceNumber" /> is invalid, then all records must be written. </param>
      <param name="callback">An optional asynchronous callback, to be called when the flush is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous flush request from other requests. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while flushing the data. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.BeginReserveAndAppend(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.Int64[],System.AsyncCallback,System.Object)">
      <summary>When overridden in a derived class, begins an asynchronous reserve and append operation. </summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents this asynchronous operation, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservationCollection">The reservation collection to make reservations in. </param>
      <param name="reservations">The reservations to make, in bytes. </param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while appending the record. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record, or to make the reservation. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.BeginReserveAndAppend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.Int64[],System.AsyncCallback,System.Object)">
      <summary>When overridden in a derived class, begins an asynchronous reserve and append operation. </summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents this asynchronous operation, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservationCollection">The reservation collection to make reservations in. </param>
      <param name="reservations">The reservations to make, in bytes. </param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while appending the record. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record, or to make the reservation. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.BeginWriteRestartArea(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.ReservationCollection,System.AsyncCallback,System.Object)">
      <summary>When overridden in a derived class, begins an asynchronous restart area write operation, using space previously reserved in the sequence. </summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous restart area write operation, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="newBaseSequenceNumber">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number. </param>
      <param name="reservation">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this restart area. </param>
      <param name="callback">An optional asynchronous callback, to be called when the restart area write is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous restart area write request from other requests. </param>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.BeginWriteRestartArea(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.ReservationCollection,System.AsyncCallback,System.Object)">
      <summary>When overridden in a derived class, begins an asynchronous restart area write operation, using space previously reserved in the sequence. </summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous restart area write operation, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="newBaseSequenceNumber">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number. </param>
      <param name="reservation">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this restart area. </param>
      <param name="callback">An optional asynchronous callback, to be called when the restart area write is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous restart area write request from other requests. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while writing the restart area. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.CreateReservationCollection">
      <summary>When overridden in a derived class, creates a new <see cref="T:System.IO.Log.ReservationCollection" />. </summary>
      <returns>The newly created <see cref="T:System.IO.Log.ReservationCollection" />. </returns>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.EndAppend(System.IAsyncResult)">
      <summary>When overridden in a derived class, ends an asynchronous append operation. </summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="result">A reference to the outstanding asynchronous I/O request.</param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.EndFlush(System.IAsyncResult)">
      <summary>When overridden in a derived class, ends an asynchronous flush operation. </summary>
      <returns>The sequence number of the last record written. </returns>
      <param name="result">A reference to the outstanding asynchronous I/O request.</param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.EndReserveAndAppend(System.IAsyncResult)">
      <summary>When overridden in a derived class, ends an asynchronous reserve and append operation. </summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="result">A reference to the outstanding asynchronous I/O request.</param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.EndWriteRestartArea(System.IAsyncResult)">
      <summary>When overridden in a derived class, ends an asynchronous restart area write operation. </summary>
      <returns>The sequence number of the written log record. </returns>
      <param name="result">A reference to the outstanding asynchronous I/O request.</param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.Flush">
      <summary>When overridden in a derived class, ensures that all appended records have been written. </summary>
      <returns>The sequence number of the last record written. </returns>
      <exception cref="T:System.IO.IOException">An I/O error occurred while flushing the data. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.Flush(System.IO.Log.SequenceNumber)">
      <summary>When overridden in a derived class, ensures that all appended records up to and including the record with the specified sequence number have been durably written. </summary>
      <returns>The sequence number of the last record written. </returns>
      <param name="sequenceNumber">The sequence number of the latest record that must be written. If this <see cref="T:System.IO.Log.SequenceNumber" /> is invalid, then all records must be written. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while flushing the data. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="P:System.IO.Log.IRecordSequence.LastSequenceNumber">
      <summary>When overridden in a derived class, gets the sequence number which is greater than the last record appended. </summary>
      <returns>A sequence number which is greater than the last record appended. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="P:System.IO.Log.IRecordSequence.MaximumRecordLength">
      <summary>When overridden in a derived class, gets the size of the largest record that can be appended to or read from this sequence, in bytes. </summary>
      <returns>The size of the largest record that can be appended to or read from this sequence, in bytes. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.ReadLogRecords(System.IO.Log.SequenceNumber,System.IO.Log.LogRecordEnumeratorType)">
      <summary>When overridden in a derived class, returns an enumerable collection of records in the sequence. </summary>
      <returns>An enumerable collection of records in the sequence. </returns>
      <param name="start">The sequence number of the first record where the reading starts.</param>
      <param name="logRecordEnum">A valid <see cref="T:System.IO.Log.LogRecordEnumeratorType" /> value that specifies the manner (that is, forward or backward) in which records should be read from a <see cref="T:System.IO.Log.LogRecordSequence" />. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while reading the record. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.ReadRestartAreas">
      <summary>When overridden in a derived class, returns an enumerable collection of the restart areas in the sequence. </summary>
      <returns>An enumerable collection of the restart areas in the sequence. </returns>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while reading the record. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.ReserveAndAppend(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.Int64[])">
      <summary>When overridden in a derived class, automatically makes a single reservation and appends a record to the sequence. </summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written. </param>
      <param name="reservationCollection">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the collection to make reservations in.</param>
      <param name="reservations">The reservations to make, in bytes.</param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while appending the record. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record, or to make the reservation. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.ReserveAndAppend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.Int64[])">
      <summary>When overridden in a derived class, automatically makes a single reservation and appends a record to the sequence. </summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservationCollection">The reservation collection to make reservations in. </param>
      <param name="reservations">The reservations to make, in bytes. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while appending the record. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record, or to make the reservation. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Log.IRecordSequence.ReservedBytes">
      <summary>When overridden in a derived class, gets the total number of bytes that have been reserved. </summary>
      <returns>The total size of all reservations made in this record sequence.</returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Log.IRecordSequence.RestartSequenceNumber">
      <summary>When overridden in a derived class, gets the sequence number of the most recently written restart area. </summary>
      <returns>The sequence number of the most recently written restart area. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="P:System.IO.Log.IRecordSequence.RetryAppend">
      <summary>Gets or sets a value indicating whether appends are automatically retried if the log is full. </summary>
      <returns>true if appends are automatically retried if the log is full; otherwise, false. The default is true. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="E:System.IO.Log.IRecordSequence.TailPinned">
      <summary>Occurs when the record sequence determines that the tail must be moved forward. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.WriteRestartArea(System.ArraySegment{System.Byte})">
      <summary>When overridden in a derived class, writes a restart area to the <see cref="T:System.IO.Log.IRecordSequence" />. </summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while writing the restart area. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new restart area. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.WriteRestartArea(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber)">
      <summary>When overridden in a derived class, writes a restart area to the <see cref="T:System.IO.Log.IRecordSequence" /> and updates the base sequence number</summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="newBaseSequenceNumber">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number.</param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while writing the restart area. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new restart area. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.WriteRestartArea(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.ReservationCollection)">
      <summary>When overridden in a derived class, writes a restart area to the <see cref="T:System.IO.Log.IRecordSequence" /> using a reservation, and updates the base sequence number</summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="newBaseSequenceNumber">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number.</param>
      <param name="reservation">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this restart area.</param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while writing the restart area. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new restart area. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.WriteRestartArea(System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
      <summary>When overridden in a derived class, writes a restart area to the <see cref="T:System.IO.Log.IRecordSequence" />. </summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while writing the restart area. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new restart area. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.WriteRestartArea(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber)">
      <summary>When overridden in a derived class, writes a restart area to the <see cref="T:System.IO.Log.IRecordSequence" /> and updates the base sequence number. </summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="newBaseSequenceNumber">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while writing the restart area. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new restart area. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.IRecordSequence.WriteRestartArea(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.ReservationCollection)">
      <summary>When overridden in a derived class, writes a restart area to the <see cref="T:System.IO.Log.IRecordSequence" /> using a reservation, and updates the base sequence number. </summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="newBaseSequenceNumber">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number. </param>
      <param name="reservation">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this restart area. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while writing the restart area. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new restart area. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="T:System.IO.Log.LogArchiveSnapshot">
      <summary>Represents a snapshot of the <see cref="T:System.IO.Log.LogStore" /> instance that can be used to generate an archive. </summary>
    </member>
    <member name="P:System.IO.Log.LogArchiveSnapshot.ArchiveRegions">
      <summary>Gets an enumerable collection of <see cref="T:System.IO.Log.FileRegion" /> instances containing the actual archival data.</summary>
      <returns>An enumerable collection of <see cref="T:System.IO.Log.FileRegion" /> instances containing the actual archival data.</returns>
    </member>
    <member name="P:System.IO.Log.LogArchiveSnapshot.ArchiveTail">
      <summary>Gets the sequence number of the <see cref="T:System.IO.Log.LogStore" /> archive tail at the time the snapshot was taken. </summary>
      <returns>The sequence number of the <see cref="T:System.IO.Log.LogStore" /> archive tail at the time the snapshot was taken. </returns>
    </member>
    <member name="P:System.IO.Log.LogArchiveSnapshot.BaseSequenceNumber">
      <summary>Gets the base sequence number of the <see cref="T:System.IO.Log.LogStore" /> at the time the snapshot was taken. </summary>
      <returns>The base sequence number of the <see cref="T:System.IO.Log.LogStore" /> at the time the snapshot was taken. </returns>
    </member>
    <member name="P:System.IO.Log.LogArchiveSnapshot.LastSequenceNumber">
      <summary>Gets the last sequence number of the <see cref="T:System.IO.Log.LogStore" /> at the time the snapshot was taken. </summary>
      <returns>The last sequence number of the <see cref="T:System.IO.Log.LogStore" /> at the time the snapshot was taken. </returns>
    </member>
    <member name="T:System.IO.Log.LogExtent">
      <summary>Represents a disk extent that contains log data. </summary>
    </member>
    <member name="P:System.IO.Log.LogExtent.Path">
      <summary>Gets the fully qualified path of this <see cref="T:System.IO.Log.LogExtent" /> instance. </summary>
      <returns>The fully qualified path of this <see cref="T:System.IO.Log.LogExtent" /> instance. </returns>
    </member>
    <member name="P:System.IO.Log.LogExtent.Size">
      <summary>Gets the size of this <see cref="T:System.IO.Log.LogExtent" /> instance, in bytes. </summary>
      <returns>The size of this <see cref="T:System.IO.Log.LogExtent" /> instance, in bytes. </returns>
    </member>
    <member name="P:System.IO.Log.LogExtent.State">
      <summary>Gets the state of this <see cref="T:System.IO.Log.LogExtent" /> instance.  </summary>
      <returns>The state of this <see cref="T:System.IO.Log.LogExtent" /> instance.</returns>
    </member>
    <member name="T:System.IO.Log.LogExtentCollection">
      <summary>Represents the collection of <see cref="T:System.IO.Log.LogExtent" /> objects associated with a <see cref="T:System.IO.Log.LogStore" />.</summary>
    </member>
    <member name="M:System.IO.Log.LogExtentCollection.Add(System.String)">
      <summary>Adds a <see cref="T:System.IO.Log.LogExtent" /> instance to the collection. </summary>
      <param name="path">The path to the log extent to be added.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path " />is null.</exception>
      <exception cref="T:System.ArgumentException">The log store file name specified by <paramref name="path " />is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">A size must be specified for the first extent.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The file specified by <paramref name="path" /> cannot be created because the file or directory already exists.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.-or-The storage disk or medium is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.LogExtentCollection.Add(System.String,System.Int64)">
      <summary>Creates a new <see cref="T:System.IO.Log.LogExtent" /> with the specified size and adds it to the collection. </summary>
      <param name="path">The path to the log extent. </param>
      <param name="size">The size of the log extent. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path " />or <paramref name="size" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="archiveTail" /> is not within a valid range.</exception>
      <exception cref="T:System.ArgumentException">The log store file name specified by <paramref name="path " />is not valid.<paramref name="size" /> is smaller than the size of the other extents in the store.</exception>
      <exception cref="T:System.InvalidOperationException">A size must be specified for the first extent.-or-A duplicate extent name cannot be used.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The file specified by <paramref name="path" /> cannot be created because the file or directory already exists.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.-or-The storage disk or medium is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="P:System.IO.Log.LogExtentCollection.Count">
      <summary>Gets the number of log extents in the collection. </summary>
      <returns>The number of log extents in the collection. </returns>
      <exception cref="T:System.ObjectDisposedException">The log store has been disposed of. </exception>
    </member>
    <member name="P:System.IO.Log.LogExtentCollection.FreeCount">
      <summary>Gets the number of free <see cref="T:System.IO.Log.LogExtent" /> instances in the collection, that is, the number of <see cref="T:System.IO.Log.LogExtent" /> instances that do not contain any data. </summary>
      <returns>The number of free <see cref="T:System.IO.Log.LogExtent" /> instances in the collection. </returns>
      <exception cref="T:System.ObjectDisposedException">The log store has been disposed of. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.LogExtentCollection.GetEnumerator">
      <summary>Gets an enumerator for the <see cref="T:System.IO.Log.LogExtent" /> instances in this collection. This method cannot be inherited.</summary>
      <returns>An enumerator for the <see cref="T:System.IO.Log.LogExtent" /> instances in this collection. </returns>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.InvalidOperationException">The operation is invalid for one of the following reasons:The enumeration has ended.The collection has been modified.The enumeration has not been started. A call to <see cref="M:System.Collections.IEnumerator.MoveNext" /> must be made.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.LogExtentCollection.Remove(System.IO.Log.LogExtent,System.Boolean)">
      <summary>Removes the specified <see cref="T:System.IO.Log.LogExtent" /> instance from the collection. </summary>
      <param name="extent">The <see cref="T:System.IO.Log.LogExtent" /> instance to remove. </param>
      <param name="force">true if the extent should be removed immediately; otherwise, false. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="extent" /> or <paramref name="force" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="extent" /> cannot be found in the collection.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="extent" /> cannot be removed. It is either still part of the active region, or access is denied.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-<paramref name="extent" /> cannot be deleted.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.LogExtentCollection.Remove(System.String,System.Boolean)">
      <summary>Removes the <see cref="T:System.IO.Log.LogExtent" /> instance with the specified path from the collection. </summary>
      <param name="path">The path of the <see cref="T:System.IO.Log.LogExtent" /> instance to remove. </param>
      <param name="force">true if the extent should be removed immediately; otherwise, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="force" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The extent specified by <paramref name="path" /> cannot be found in the collection.</exception>
      <exception cref="T:System.InvalidOperationException">The extent specified by <paramref name="path" /> cannot be removed. It is either still part of the active region, or access is denied.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The extent specified by <paramref name="path" /> cannot be deleted.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.LogExtentCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gets an enumerator for the <see cref="T:System.IO.Log.LogExtent" /> instances in this collection. This method cannot be inherited.</summary>
      <returns>An enumerator for the <see cref="T:System.IO.Log.LogExtent" /> instances in this collection. </returns>
    </member>
    <member name="T:System.IO.Log.LogExtentState">
      <summary>Describes the current state of a <see cref="T:System.IO.Log.LogExtent" /> instance. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.IO.Log.LogExtentState.Unknown">
      <summary>The status of the <see cref="T:System.IO.Log.LogExtent" /> instance is unknown.</summary>
    </member>
    <member name="F:System.IO.Log.LogExtentState.Initializing">
      <summary>The <see cref="T:System.IO.Log.LogExtent" /> instance has not yet been allocated to the <see cref="T:System.IO.Log.LogStore" />.</summary>
    </member>
    <member name="F:System.IO.Log.LogExtentState.Inactive">
      <summary>The <see cref="T:System.IO.Log.LogExtent" /> instance has been allocated to the <see cref="T:System.IO.Log.LogStore" /> but does not contain any active data.</summary>
    </member>
    <member name="F:System.IO.Log.LogExtentState.Active">
      <summary>The <see cref="T:System.IO.Log.LogExtent" /> instance is being used as storage for the <see cref="T:System.IO.Log.LogStore" />.</summary>
    </member>
    <member name="F:System.IO.Log.LogExtentState.ActivePendingDelete">
      <summary>The <see cref="T:System.IO.Log.LogExtent" /> instance is marked for deletion but still contains some active data.</summary>
    </member>
    <member name="F:System.IO.Log.LogExtentState.PendingArchive">
      <summary>The <see cref="T:System.IO.Log.LogExtent" /> instance does not contain any active data, but contains data that has not yet been archived.</summary>
    </member>
    <member name="F:System.IO.Log.LogExtentState.PendingArchiveAndDelete">
      <summary>The <see cref="T:System.IO.Log.LogExtent" /> instance is marked for deletion but still contains data that has not been archived. </summary>
    </member>
    <member name="T:System.IO.Log.LogPolicy">
      <summary>Represents the policy associated with a <see cref="T:System.IO.Log.LogStore" />. </summary>
    </member>
    <member name="P:System.IO.Log.LogPolicy.AutoGrow">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.IO.Log.LogStore" /> can grow automatically.</summary>
      <returns>true if the <see cref="T:System.IO.Log.LogStore" /> can grow its size automatically; otherwise, false.</returns>
    </member>
    <member name="P:System.IO.Log.LogPolicy.AutoShrinkPercentage">
      <summary>Gets or sets the percentage of free space the <see cref="T:System.IO.Log.LogStore" /> can shrink. </summary>
      <returns>The percentage of free space the <see cref="T:System.IO.Log.LogStore" /> can shrink. </returns>
    </member>
    <member name="M:System.IO.Log.LogPolicy.Commit">
      <summary>Sets this policy as the current policy for the <see cref="T:System.IO.Log.LogStore" />. </summary>
      <exception cref="T:System.InvalidOperationException">The set of policies installed on the log is invalid.-or-A policy of the log prevented this operation from completing.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.NotSupportedException">Setting this policy is not supported on the current platform.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.PathTooLongException">The new extent prefix is invalid or too long. A valid prefix must be provided.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="P:System.IO.Log.LogPolicy.GrowthRate">
      <summary>Gets or sets the rate of automatic growth of the <see cref="T:System.IO.Log.LogStore" />. </summary>
      <returns>The rate of automatic growth of the <see cref="T:System.IO.Log.LogStore" />. </returns>
    </member>
    <member name="P:System.IO.Log.LogPolicy.MaximumExtentCount">
      <summary>Gets or sets the maximum number of <see cref="T:System.IO.Log.LogExtent" /> instances the <see cref="T:System.IO.Log.LogStore" /> can contain. </summary>
      <returns>The maximum number of <see cref="T:System.IO.Log.LogExtent" /> instances the <see cref="T:System.IO.Log.LogStore" /> can contain. </returns>
    </member>
    <member name="P:System.IO.Log.LogPolicy.MinimumExtentCount">
      <summary>Gets or sets the minimum number of <see cref="T:System.IO.Log.LogExtent" /> instances the <see cref="T:System.IO.Log.LogStore" /> can contain. </summary>
      <returns>An integer that specifies the minimum number of <see cref="T:System.IO.Log.LogExtent" /> instances the <see cref="T:System.IO.Log.LogStore" /> can contain. Since the CLFS requires that the minimum extent count to be 2 extents, this value should be at least 2.</returns>
    </member>
    <member name="P:System.IO.Log.LogPolicy.NewExtentPrefix">
      <summary>Gets or sets the prefix string for automatically created extents. </summary>
      <returns>The prefix string for automatically created extents. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Log.LogPolicy.NextExtentSuffix">
      <summary>Gets or sets the suffix number for new extents. </summary>
      <returns>The suffix number for new extents. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Log.LogPolicy.PinnedTailThreshold">
      <summary>Gets or sets the amount of space that the <see cref="E:System.IO.Log.LogRecordSequence.TailPinned" /> event requires for advancing the base of the log. </summary>
      <returns>The amount of space that the <see cref="E:System.IO.Log.LogRecordSequence.TailPinned" /> event requires for advancing the base of the log. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.LogPolicy.Refresh">
      <summary>Reads the current policy for the <see cref="T:System.IO.Log.LogStore" />, discarding any changes that may have been made. </summary>
      <exception cref="T:System.InvalidOperationException">The set of policies installed on the log is invalid.-or-A policy of the log prevented this operation from completing.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.NotSupportedException">Setting this policy is not supported on the current platform.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="T:System.IO.Log.LogRecord">
      <summary>Represents a log record that has been appended to a sequence. </summary>
    </member>
    <member name="M:System.IO.Log.LogRecord.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.LogRecord" /> class. </summary>
    </member>
    <member name="P:System.IO.Log.LogRecord.Data">
      <summary>When overridden in a derived class, gets a stream that contains the record data. </summary>
      <returns>A stream that contains the record data. </returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Log.LogRecord" /> has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.LogRecord.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.IO.Log.LogRecord" />. </summary>
    </member>
    <member name="P:System.IO.Log.LogRecord.Previous">
      <summary>When overridden in a derived class, gets the sequence number of the previous record. </summary>
      <returns>The sequence number of the previous record. </returns>
    </member>
    <member name="P:System.IO.Log.LogRecord.SequenceNumber">
      <summary>When overridden in a derived class, gets the sequence number of this record. </summary>
      <returns>The sequence number of this record. </returns>
    </member>
    <member name="P:System.IO.Log.LogRecord.User">
      <summary>When overridden in a derived class, gets the sequence number of the next record.</summary>
      <returns>The sequence number of the next record in the user-specified chain. </returns>
    </member>
    <member name="T:System.IO.Log.LogRecordEnumeratorType">
      <summary>Specifies the manner in which records should be read from a <see cref="T:System.IO.Log.LogRecordSequence" />. </summary>
    </member>
    <member name="F:System.IO.Log.LogRecordEnumeratorType.User">
      <summary>Records should be read in the order specified by the User sequence number of the log record.</summary>
    </member>
    <member name="F:System.IO.Log.LogRecordEnumeratorType.Previous">
      <summary>Records should be read in the order specified by the Previous sequence number of the log record.</summary>
    </member>
    <member name="F:System.IO.Log.LogRecordEnumeratorType.Next">
      <summary>Records should be read in the order in which they were appended to the sequence.</summary>
    </member>
    <member name="T:System.IO.Log.LogRecordSequence">
      <summary>Represents a record sequence stored in a <see cref="T:System.IO.Log.LogStore" />. </summary>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.#ctor(System.IO.Log.LogStore)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.LogRecordSequence" /> class with the specified log store.</summary>
      <param name="logStore">The <see cref="T:System.IO.Log.LogStore" /> that this record sequence should use. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="logStore" /> is invalid. </exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.#ctor(System.IO.Log.LogStore,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.LogRecordSequence" /> class with the specified log store, buffer size for each record, and buffer number.</summary>
      <param name="logStore">The <see cref="T:System.IO.Log.LogStore" /> that this record sequence should use. </param>
      <param name="bufferSize">The desired buffer size in bytes. The buffer size determines the maximum size of the record that can be appended or read. </param>
      <param name="bufferCount">The desired number of buffers. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="logStore" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize " />is negative or zero.-or-<paramref name="bufferCount" /> is negative or zero. </exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.#ctor(System.String,System.IO.FileMode)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.LogRecordSequence" /> class with a specified path to the log store and the access mode. </summary>
      <param name="path">A relative or absolute path for the base file of the log store to open. </param>
      <param name="mode">One of the <see cref="T:System.IO.FileMode" /> values that determines how to open or create the store.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string ("").-or-<paramref name="path" /> contains only white space.-or-<paramref name="path" /> contains one or more invalid characters. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contains an invalid value. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when opening the log store. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system. </exception>
      <exception cref="T:System.NotSupportedException">This operation is not supported. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.PlatformNotSupportedException">
        <see cref="T:System.IO.Log.LogRecordSequence" /> cannot be used because the required Common Log File System (CLFS) component is not installed. Install the CLFS component if it is available for your platform, or use the <see cref="T:System.IO.Log.FileRecordSequence" /> class. </exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.LogRecordSequence" /> class with a specified path to the log store and the access and share modes.</summary>
      <param name="path">A relative or absolute path for the base file of the log store to open. </param>
      <param name="mode">One of the <see cref="T:System.IO.FileMode" /> values that determines how to open or create the store.</param>
      <param name="access">One of the <see cref="T:System.IO.FileAccess" /> values that determines how the file can be accessed by the <see cref="T:System.IO.Log.LogStore" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string ("").-or-<paramref name="path" /> contains only white space.-or-<paramref name="path" /> contains one or more invalid characters. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contains an invalid value. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when opening the log store. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system. </exception>
      <exception cref="T:System.NotSupportedException">This operation is not supported. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.PlatformNotSupportedException">
        <see cref="T:System.IO.Log.LogRecordSequence" /> cannot be used because the required Common Log File System (CLFS) component is not installed. Install the CLFS component if it is available for your platform, or use the <see cref="T:System.IO.Log.FileRecordSequence" /> class. </exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.LogRecordSequence" /> class with a specified path to the log store and the access mode. </summary>
      <param name="path">A relative or absolute path for the base file of the log store to open. </param>
      <param name="mode">One of the <see cref="T:System.IO.FileMode" /> values that determines how to open or create the store.</param>
      <param name="access">One of the <see cref="T:System.IO.FileAccess" /> values that determines how the file can be accessed by the <see cref="T:System.IO.Log.LogStore" />.</param>
      <param name="share">One of the <see cref="T:System.IO.FileShare" /> values that determines how the log store will be shared among processes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string ("").-or-<paramref name="path" /> contains only white space.-or-<paramref name="path" /> contains one or more invalid characters. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contains an invalid value. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when opening the log store. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system. </exception>
      <exception cref="T:System.NotSupportedException">This operation is not supported. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.PlatformNotSupportedException">
        <see cref="T:System.IO.Log.LogRecordSequence" /> cannot be used because the required Common Log File System (CLFS) component is not installed. Install the CLFS component if it is available for your platform, or use the <see cref="T:System.IO.Log.FileRecordSequence" /> class. </exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.LogRecordSequence" /> class with a specified path to the log store, file permission, access and share modes, and the buffer size and count for records. </summary>
      <param name="path">A relative or absolute path for the base file of the log store to open. </param>
      <param name="mode">One of the <see cref="T:System.IO.FileMode" /> values that determines how to open or create the store.</param>
      <param name="access">One of the <see cref="T:System.IO.FileAccess" /> values that determines how the file can be accessed by the <see cref="T:System.IO.Log.LogStore" />.</param>
      <param name="share">One of the <see cref="T:System.IO.FileShare" /> values that determines how the log store will be shared among processes. </param>
      <param name="bufferSize">The desired buffer size in bytes. The buffer size determines the maximum size of the record that can be appended or read. </param>
      <param name="bufferCount">The desired number of buffers. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string ("").-or-<paramref name="path" /> contains only white space.-or-<paramref name="path" /> contains one or more invalid characters. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contains an invalid value. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when opening the log store. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system. </exception>
      <exception cref="T:System.NotSupportedException">This operation is not supported. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.PlatformNotSupportedException">
        <see cref="T:System.IO.Log.LogRecordSequence" /> cannot be used because the required Common Log File System (CLFS) component is not installed. Install the CLFS component if it is available for your platform, or use the <see cref="T:System.IO.Log.FileRecordSequence" /> class. </exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.Int32,System.Security.AccessControl.FileSecurity)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.LogRecordSequence" /> class.  </summary>
      <param name="path">A relative or absolute path for the base file of the log store to open. </param>
      <param name="mode">A valid <see cref="T:System.IO.FileMode" /> value that determines how to open or create the store. </param>
      <param name="access">A valid <see cref="T:System.IO.FileAccess" /> value that determines how the log store can be accessed. </param>
      <param name="share">A valid <see cref="T:System.IO.FileShare" /> value that determines how the log store will be shared among processes. </param>
      <param name="bufferSize">The desired buffer size in bytes. The buffer size determines the maximum size of the record that can be appended or read. </param>
      <param name="bufferCount">The desired number of buffers. </param>
      <param name="fileSecurity">A valid <see cref="T:System.Security.AccessControl.FileSecurity" /> value that specifies the security to set on the newly created store if the store must be created.</param>
      <exception cref="T:System.ArgumentException">The file specified by <paramref name="path" /> is not valid.-or-The specified log store file name is not valid. -or-<paramref name="mode" /> has a value of <see cref="F:System.IO.FileMode.CreateNew" />, and cannot be used without write access.-or-<paramref name="mode" /> has a value of <see cref="F:System.IO.FileMode.OpenOrCreate" />, and cannot be used without write access.</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">One or more of the arguments are out of range.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <paramref name="path" /> cannot be found.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The file specified by <paramref name="path" /> cannot be accessed because it is in use by another process.-or-The file specified by <paramref name="path" /> cannot be created because the file or directory already exists.-or-The log handle could not be bound to the thread pool.-or-The specified log file format or version is invalid.</exception>
      <exception cref="T:System.NotSupportedException">This operation is not supported. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.PlatformNotSupportedException">
        <see cref="T:System.IO.Log.LogRecordSequence" /> cannot be used because the required Common Log File System (CLFS) component is not installed. Install the CLFS component if it is available for your platform, or use the <see cref="T:System.IO.Log.FileRecordSequence" /> class. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.AdvanceBaseSequenceNumber(System.IO.Log.SequenceNumber)">
      <summary>Moves the base sequence number of the log forward. This method cannot be inherited.</summary>
      <param name="newBaseSequenceNumber">Specifies the new base <see cref="T:System.IO.Log.SequenceNumber" /> for the log. This must lie in the range between the current base sequence number and the last sequence number of the log inclusively.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="newBaseSequenceNumber" /> is not valid for this sequence.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">A new or existing archive tail or base of the active log is invalid.-or-<paramref name="newBaseSequenceNumber" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.InvalidOperationException">The specified log does not have any extents. One or more extents must be created before a record sequence may be used.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.Append(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions)">
      <summary>Writes a log record to the <see cref="T:System.IO.Log.LogRecordSequence" />. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not valid for this sequence.-or-<paramref name="data" /> cannot be appended because it is larger than the maximum record size.-or-<paramref name="reservations" /> was not created by this record sequence.</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.Append(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection)">
      <summary>Appends a log record to the <see cref="T:System.IO.Log.IRecordSequence" />, using space previously reserved in the sequence. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written. </param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this record. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not valid for this sequence.-or-<paramref name="data" /> cannot be appended because it is larger than the maximum record size.-or-<paramref name="reservations" /> was not created by this record sequence.</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.Append(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions)">
      <summary>Appends a log record to the <see cref="T:System.IO.Log.IRecordSequence" />. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="userRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not valid for this sequence.-or-<paramref name="data" /> cannot be appended because it is larger than the maximum record size.-or-<paramref name="reservations" /> was not created by this record sequence.</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.Append(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection)">
      <summary>Appends a log record to the <see cref="T:System.IO.Log.IRecordSequence" />, using space previously reserved in the sequence. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="userRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written. </param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this record. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not valid for this sequence.-or-<paramref name="data" /> cannot be appended because it is larger than the maximum record size.-or-<paramref name="reservations" /> was not created by this record sequence.</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
    </member>
    <member name="P:System.IO.Log.LogRecordSequence.BaseSequenceNumber">
      <summary>Gets the sequence number of the first valid record in the current <see cref="T:System.IO.Log.LogRecordSequence" />. </summary>
      <returns>The lowest sequence number that corresponds to a valid record in the <see cref="T:System.IO.Log.LogRecordSequence" />. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.BeginAppend(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous append operation. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous append, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete.</param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="nextUndoRecord" /> or <paramref name="previousRecord" /> is not valid for this sequence.-or-<paramref name="data" /> cannot be appended because it is larger than the maximum record size.-or-<paramref name="reservations" /> was not created by this record sequence.</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="nextUndoRecord" /> or <paramref name="previousRecord" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.BeginAppend(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous append operation. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous append, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this record. </param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not valid for this sequence.-or-<paramref name="data" /> cannot be appended because it is larger than the maximum record size.-or-<paramref name="reservations" /> was not created by this record sequence.</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.BeginAppend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous append operation. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous append, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="userRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete.</param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not valid for this sequence.-or-<paramref name="data" /> cannot be appended because it is larger than the maximum record size.-or-<paramref name="reservations" /> was not created by this record sequence.</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.BeginAppend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous append operation. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous append, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <param name="userRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this record. </param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not valid for this sequence.-or-<paramref name="data" /> cannot be appended because it is larger than the maximum record size.-or-<paramref name="reservations" /> was not created by this record sequence.</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.BeginFlush(System.IO.Log.SequenceNumber,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous flush operation, using space previously reserved in the sequence. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous flush operation, which could still be pending. </returns>
      <param name="sequenceNumber">The sequence number of the latest record that must be written. If this <see cref="T:System.IO.Log.SequenceNumber" /> is invalid, then all records must be written. </param>
      <param name="callback">An optional asynchronous callback, to be called when the flush is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous flush request from other requests. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sequenceNumber" /> is not valid for this sequence.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="sequenceNumber" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.InvalidOperationException">The specified log does not have any extents. One or more extents must be created before a record sequence can be used.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.BeginReserveAndAppend(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.Int64[],System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous reserve and append operation. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents this asynchronous operation, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservationCollection">The reservation collection to make reservations in. </param>
      <param name="reservations">The reservations to make, in bytes. </param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not valid for this sequence.-or-<paramref name="data" /> cannot be appended because it is larger than the maximum record size.-or-<paramref name="reservations" /> was not created by this record sequence.</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.BeginReserveAndAppend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.Int64[],System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous reserve and append operation. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents this asynchronous operation, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="userRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservationCollection">The reservation collection to make reservations in. </param>
      <param name="reservations">The reservations to make, in bytes. </param>
      <param name="callback">An optional asynchronous callback, to be called when the append is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous append request from other requests. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while appending the record. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record, or to make the reservation. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.BeginWriteRestartArea(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.ReservationCollection,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous restart area write operation, using space previously reserved in the sequence. This method cannot be inherited.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous restart area write operation, which could still be pending. </returns>
      <param name="data">Byte array segments that will be concatenated and appended as the record. </param>
      <param name="newBaseSeqNum">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number. </param>
      <param name="reservation">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this restart area. </param>
      <param name="callback">An optional asynchronous callback, to be called when the restart area write is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous restart area write request from other requests. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="newBaseSeqNum" /> is not valid for this sequence.-or-The specified log enumeration start sequence number is invalid.-or-<paramref name="data" /> cannot be appended because it is larger than the maximum record size.-or-<paramref name="reservation" /> was not created by this record sequence.</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the parameters is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">A new or existing archive tail or base of the active log is invalid.-or-<paramref name="newBaseSeqNum" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.BeginWriteRestartArea(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.ReservationCollection,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous restart area write operation, using space previously reserved in the sequence. This method cannot be inherited. </summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous restart area write operation, which could still be pending. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="newBaseSeqNum">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number. </param>
      <param name="reservationCollection">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this restart area. </param>
      <param name="callback">An optional asynchronous callback, to be called when the restart area write is complete. </param>
      <param name="state">A user-provided object that distinguishes this particular asynchronous restart area write request from other requests. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="newBaseSeqNum" /> is not valid for this sequence.-or-The specified log enumeration start sequence number is invalid.-or-<paramref name="data" /> cannot be appended because it is larger than the maximum record size.-or-<paramref name="reservationCollection" /> was not created by this record sequence.</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the parameters is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">A new or existing archive tail or base of the active log is invalid.-or-<paramref name="newBaseSeqNum" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.CreateReservationCollection">
      <summary>Creates a new <see cref="T:System.IO.Log.ReservationCollection" />. This method cannot be inherited. </summary>
      <returns>The newly created <see cref="T:System.IO.Log.ReservationCollection" />. </returns>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.Dispose">
      <summary>Releases the resources used by the component. </summary>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.EndAppend(System.IAsyncResult)">
      <summary>Ends an asynchronous append operation. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="result">A reference to the outstanding asynchronous I/O request.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="result" /> is invalid.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.InvalidOperationException">End has already been called for this asynchronous operation.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.EndFlush(System.IAsyncResult)">
      <summary>Ends an asynchronous flush operation. This method cannot be inherited.</summary>
      <returns>The sequence number of the last record written. </returns>
      <param name="result">A reference to the outstanding asynchronous I/O request.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="result" /> is invalid.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.InvalidOperationException">End has already been called for this asynchronous operation.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.EndReserveAndAppend(System.IAsyncResult)">
      <summary>Ends an asynchronous reserve and append operation. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="result">A reference to the outstanding asynchronous I/O request.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="result" /> is invalid.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.InvalidOperationException">End has already been called for this asynchronous operation.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.EndWriteRestartArea(System.IAsyncResult)">
      <summary>Ends an asynchronous restart area write operation. This method cannot be inherited.</summary>
      <returns>The sequence number of the written log record. </returns>
      <param name="result">A reference to the outstanding asynchronous I/O request.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="result" /> is not valid.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">A new or existing archive tail or base of the active log is invalid.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.InvalidOperationException">End has already been called for this asynchronous operation.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.Flush">
      <summary>Ensures that all appended records have been written. This method cannot be inherited.</summary>
      <returns>The sequence number of the last record written. </returns>
      <exception cref="T:System.IO.IOException">An I/O error occurred while flushing the data. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.NotSupportedException">This operation is not supported. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new restart area. </exception>
      <exception cref="T:System.InvalidOperationException">The specified log does not have any extents. One or more extents must be created before a record sequence can be used.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.Flush(System.IO.Log.SequenceNumber)">
      <summary>Ensures that all appended records up to and including the record with the specified sequence number have been durably written. This method cannot be inherited.</summary>
      <returns>The sequence number of the last record written. </returns>
      <param name="sequenceNumber">The sequence number of the latest record that must be written. If this <see cref="T:System.IO.Log.SequenceNumber" /> is invalid, then all records must be written. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sequenceNumber" /> is not valid for this sequence.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="sequenceNumber" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.</exception>
      <exception cref="T:System.InvalidOperationException">The specified log does not have any extents. One or more extents must be created before a record sequence can be used.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
    </member>
    <member name="P:System.IO.Log.LogRecordSequence.LastSequenceNumber">
      <summary>Gets the sequence number which is greater than the last record appended</summary>
      <returns>A sequence number which is greater than the last record appended. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="P:System.IO.Log.LogRecordSequence.LogStore">
      <summary>Gets the <see cref="T:System.IO.Log.LogStore" /> that contains the data for this record sequence. This method cannot be inherited. </summary>
      <returns>The <see cref="T:System.IO.Log.LogStore" /> that contains the data for this record sequence. </returns>
    </member>
    <member name="P:System.IO.Log.LogRecordSequence.MaximumRecordLength">
      <summary>Gets the maximum size of a record that can be added to this record sequence.</summary>
      <returns>The maximum size of a record that can be added to this record sequence.</returns>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.ReadLogRecords(System.IO.Log.SequenceNumber,System.IO.Log.LogRecordEnumeratorType)">
      <summary>Returns an enumerable collection of records in the sequence. This method cannot be inherited.</summary>
      <returns>An enumerable collection of records in the sequence. </returns>
      <param name="start">The sequence number of the first record where the reading starts.</param>
      <param name="logRecordEnum">A valid <see cref="T:System.IO.Log.LogRecordEnumeratorType" /> value that specifies the manner (that is, forward or backward) in which records should be read from a <see cref="T:System.IO.Log.LogRecordSequence" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="start" /> is not valid for this sequence.-or-<paramref name="logRecordEnum" /> is invalid.-or-The specified element was not found in the collection.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="start" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.-orThe buffer size used to write the log record is larger than the buffer size being used to read it.-or-The record sequence is corrupted.-or-The specified log file format or version is invalid.-or-The record was written with an incompatible version of the record sequence.</exception>
      <exception cref="T:System.InvalidOperationException">The operation is invalid because the enumeration has not been started. A call to <see cref="M:System.Collections.IEnumerator.MoveNext" /> must be made.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.ReadRestartAreas">
      <summary>Returns an enumerable collection of the restart areas in the sequence. This method cannot be inherited.</summary>
      <returns>An enumerable collection of the restart areas in the sequence. </returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="start" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.-orThe buffer size used to write the log record is larger than the buffer size being used to read it.-or-The record sequence is corrupted.-or-The specified log file format or version is invalid.-or-The record was written with an incompatible version of the record sequence.</exception>
      <exception cref="T:System.InvalidOperationException">The operation is invalid because the enumeration has not been started. A call to <see cref="M:System.Collections.IEnumerator.MoveNext" /> must be made.-orThe enumeration has ended.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.ReserveAndAppend(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.Int64[])">
      <summary>Automatically makes a single reservation and appends a record to the sequence. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="nextUndoRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written. </param>
      <param name="reservationCollection">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the collection to make reservations in.</param>
      <param name="reservations">The reservations to make, in bytes.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="nextUndoRecord" /> or <paramref name="previousRecord" /> is not valid for this sequence.-or-<paramref name="data" /> cannot be appended because it is larger than the maximum record size.-or-<paramref name="reservations" /> was not created by this record sequence.</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="nextUndoRecord" /> or <paramref name="previousRecord" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.ReserveAndAppend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber,System.IO.Log.RecordAppendOptions,System.IO.Log.ReservationCollection,System.Int64[])">
      <summary>Automatically makes a single reservation and appends a record to the sequence. This method cannot be inherited.</summary>
      <returns>The sequence number of the appended log record. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="userRecord">The sequence number of the next record in the user-specified order. </param>
      <param name="previousRecord">The sequence number of the next record in Previous order. </param>
      <param name="recordAppendOptions">A valid value of <see cref="T:System.IO.Log.RecordAppendOptions" /> that specifies how the data should be written.</param>
      <param name="reservationCollection">The reservation collection to make reservations in. </param>
      <param name="reservations">The reservations to make, in bytes. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while appending the record. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new record, or to make the reservation. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="userRecord" /> or <paramref name="previousRecord" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.NotSupportedException">The operation cannot be performed because the record sequence was opened with read-only access.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.IO.Log.ReservationNotFoundException">No reservation large enough to fit <paramref name="data" /> can be found in <paramref name="reservations" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Log.LogRecordSequence.ReservedBytes">
      <summary>Gets the total number of bytes that have been reserved. </summary>
      <returns>The total size of all reservations made in this record sequence.</returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Log.LogRecordSequence.RestartSequenceNumber">
      <summary>Gets the sequence number of the restart area closest to the end of the log. </summary>
      <returns>The sequence number of the restart area closest to the end of the log. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="P:System.IO.Log.LogRecordSequence.RetryAppend">
      <summary>Gets or sets a value indicating whether or not appends are automatically retried if the log is full. </summary>
      <returns>true if appends are automatically retried if the log is full; otherwise, false. The default is true. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.SetLastRecord(System.IO.Log.SequenceNumber)">
      <summary>Sets the last record in the <see cref="T:System.IO.Log.LogRecordSequence" />. </summary>
      <param name="sequenceNumber">The new last sequence number in the <see cref="T:System.IO.Log.LogRecordSequence" />. This should refer to a current valid record   currently in the log.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sequenceNumber" /> is not valid for this sequence.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">A new or existing archive tail or base of the active log is invalid.-or-<paramref name="sequenceNumber" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The end of the log has been reached.-or-The specified log file format or version is invalid.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.IO.Log.LogRecordSequence.TailPinned">
      <summary>Signals the need to move the tail of the sequence.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.WriteRestartArea(System.ArraySegment{System.Byte})">
      <summary>Writes a restart area to the <see cref="T:System.IO.Log.LogRecordSequence" />. This method cannot be inherited.</summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">An array segment that will be concatenated and appended as the record.</param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while writing the restart area. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new restart area. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.WriteRestartArea(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber)">
      <summary>Writes a restart area to the <see cref="T:System.IO.Log.LogRecordSequence" /> and updates the base sequence number. This method cannot be inherited.</summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="newBaseSeqNum">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while writing the restart area. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new restart area. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.WriteRestartArea(System.ArraySegment{System.Byte},System.IO.Log.SequenceNumber,System.IO.Log.ReservationCollection)">
      <summary>Writes a restart area to the <see cref="T:System.IO.Log.LogRecordSequence" /> using a reservation, and updates the base sequence number. This method cannot be inherited.</summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="newBaseSeqNum">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number. </param>
      <param name="reservations">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this restart area. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while writing the restart area. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new restart area. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.WriteRestartArea(System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
      <summary>Writes a restart area to the <see cref="T:System.IO.Log.LogRecordSequence" />. This method cannot be inherited.</summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record.</param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while writing the restart area. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new restart area. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.ArgumentNullException">One or more of the arguments are null.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.WriteRestartArea(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber)">
      <summary>Writes a restart area to the <see cref="T:System.IO.Log.LogRecordSequence" /> and updates the base sequence number. This method cannot be inherited.</summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="newBaseSeqNum">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number. </param>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while writing the restart area. </exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence could not make enough free space to contain the new restart area. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.LogRecordSequence.WriteRestartArea(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.IO.Log.SequenceNumber,System.IO.Log.ReservationCollection)">
      <summary>Writes a restart area to the <see cref="T:System.IO.Log.LogRecordSequence" /> using a reservation, and updates the base sequence number. This method cannot be inherited.</summary>
      <returns>The sequence number of the written restart area. </returns>
      <param name="data">A list of byte array segments that will be concatenated and appended as the record. </param>
      <param name="newBaseSeqNum">The new base sequence number. The specified sequence number must be greater than or equal to the current base sequence number. </param>
      <param name="reservationCollection">A <see cref="T:System.IO.Log.ReservationCollection" /> that contains the reservation that should be used for this restart area. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="newBaseSeqNum" /> is not valid for this sequence.-or-The specified log enumeration start sequence number is invalid.-or-<paramref name="data" /> cannot be appended because it is larger than the maximum record size.-or-<paramref name="reservationCollection" /> was not created by this record sequence.</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the parameters is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">A new or existing archive tail or base of the active log is invalid.-or-<paramref name="newBaseSeqNum" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.IO.IOException">The request could not be performed because of an unexpected I/O exception.-or-The request could not be performed because of an I/O device error.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log sequence is denied by the operating system.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
    </member>
    <member name="T:System.IO.Log.LogStore">
      <summary>Represents a log-structured storage. </summary>
    </member>
    <member name="M:System.IO.Log.LogStore.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.LogStore" /> class for the specified handle. </summary>
      <param name="handle">A file handle for the log file that the current <see cref="T:System.IO.Log.LogStore" /> object encapsulates. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="handle" /> is null.  </exception>
      <exception cref="T:System.IO.IOException">The log handle could not be bound to the thread pool.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of a program.</exception>
    </member>
    <member name="M:System.IO.Log.LogStore.#ctor(System.String,System.IO.FileMode)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.LogStore" /> class with the specified path and mode. </summary>
      <param name="path">A relative or absolute path for the base file of the log store to open. </param>
      <param name="mode">One of the <see cref="T:System.IO.FileMode" /> values that determines how to open or create the store.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string ("").-or-<paramref name="path" /> contains only white space.-or-<paramref name="path" /> contains one or more invalid characters. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contains an invalid value. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when opening the log store. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system. </exception>
      <exception cref="T:System.PlatformNotSupportedException">
        <see cref="T:System.IO.Log.LogStore" /> cannot be used because the required Common Log File System (CLFS) component is not installed. Install the CLFS component if it is available for your platform or use the <see cref="T:System.IO.Log.FileRecordSequence" /> class instead.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of a program.</exception>
    </member>
    <member name="M:System.IO.Log.LogStore.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.LogStore" /> class with the specified path, mode, and access.</summary>
      <param name="path">A relative or absolute path for the base file of the log store to open. </param>
      <param name="mode">One of the <see cref="T:System.IO.FileMode" /> values that determines how to open or create the store.</param>
      <param name="access">One of the <see cref="T:System.IO.FileAccess" /> values that determines how the file can be accessed by the <see cref="T:System.IO.Log.LogStore" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string ("").-or-<paramref name="path" /> contains only white space.-or-<paramref name="path" /> contains one or more invalid characters.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contains an invalid value. -or<paramref name="access" /> contains an invalid value. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when opening the log store. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
      <exception cref="T:System.PlatformNotSupportedException">
        <see cref="T:System.IO.Log.LogStore" /> cannot be used because the required Common Log File System (CLFS) component is not installed. Install the CLFS component if it is available for your platform or use the <see cref="T:System.IO.Log.FileRecordSequence" /> class instead.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of a program.</exception>
    </member>
    <member name="M:System.IO.Log.LogStore.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.LogStore" /> class.  </summary>
      <param name="path">A relative or absolute path for the base file of the log store to open. </param>
      <param name="mode">One of the <see cref="T:System.IO.FileMode" /> values that determines how to open or create the store.</param>
      <param name="access">One of the <see cref="T:System.IO.FileAccess" /> values that determines how the file can be accessed by the <see cref="T:System.IO.Log.LogStore" />.</param>
      <param name="share">One of the <see cref="T:System.IO.FileShare" /> values that determines how the log store will be shared among processes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string ("").-or-<paramref name="path" /> contains only white space.-or-<paramref name="path" /> contains one or more invalid characters.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contains an invalid value. -or<paramref name="access" /> contains an invalid value. -or-<paramref name="share" /> contains an invalid value. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when opening the log store. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
      <exception cref="T:System.PlatformNotSupportedException">
        <see cref="T:System.IO.Log.LogStore" /> cannot be used because the required Common Log File System (CLFS) component is not installed. Install the CLFS component if it is available for your platform or use the <see cref="T:System.IO.Log.FileRecordSequence" /> class instead.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of a program.</exception>
    </member>
    <member name="M:System.IO.Log.LogStore.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Security.AccessControl.FileSecurity)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.LogStore" /> class.  </summary>
      <param name="path">A relative or absolute path for the base file of the log store to open. </param>
      <param name="mode">One of the <see cref="T:System.IO.FileMode" /> values that determines how to open or create the store.</param>
      <param name="access">One of the <see cref="T:System.IO.FileAccess" /> values that determines how the file can be accessed by the <see cref="T:System.IO.Log.LogStore" />.</param>
      <param name="share">One of the <see cref="T:System.IO.FileShare" /> values that determines how the log store will be shared among processes. </param>
      <param name="fileSecurity">One of the <see cref="T:System.Security.AccessControl.FileSecurity" /> value that specifies the security to set on the newly created store if the store must be created.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The log store file name specified by <paramref name="path" /> is not valid.-or-<paramref name="Mode" /> is CreateNew, which cannot be used without write access.-or-<paramref name="Mode" /> is OpenOrCreate, which cannot be used without write access.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contains an invalid value. -or<paramref name="access" /> contains an invalid value. -or-<paramref name="share" /> contains an invalid value. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when opening the log store. The file specified by <paramref name="path" /> cannot be accessed because it is in use by another process.-or-The file specified by <paramref name="path" /> cannot be created because the file or directory already exists.-or-The log handle could not be bound to the thread pool.-or-The specified log file format or version is invalid.</exception>
      <exception cref="T:System.PlatformNotSupportedException">
        <see cref="T:System.IO.Log.LogStore" /> cannot be used because the required Common Log File System (CLFS) component is not installed. Install the CLFS component if it is available for your platform or use the <see cref="T:System.IO.Log.FileRecordSequence" /> class instead.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of a program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="P:System.IO.Log.LogStore.Archivable">
      <summary>Gets a value indicating whether this <see cref="T:System.IO.Log.LogStore" /> instance can be archived. </summary>
      <returns>true if this <see cref="T:System.IO.Log.LogStore" /> instance can be archived; otherwise, false. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="P:System.IO.Log.LogStore.BaseSequenceNumber">
      <summary>Gets the lowest sequence number that corresponds to a valid record in this <see cref="T:System.IO.Log.LogStore" /> instance.</summary>
      <returns>The lowest sequence number that corresponds to a valid record in this <see cref="T:System.IO.Log.LogStore" /> instance.</returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="M:System.IO.Log.LogStore.CreateLogArchiveSnapshot">
      <summary>Takes a snapshot of the log store state for making a backup. </summary>
      <returns>A <see cref="T:System.IO.Log.LogArchiveSnapshot" /> object that contains the state necessary to make an archive. </returns>
      <exception cref="T:System.NotSupportedException">The log store is not archivable. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when creating the archive snapshot. </exception>
      <exception cref="T:System.ArgumentException">An argument is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An invalid operation has been executed.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of a program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.LogStore.CreateLogArchiveSnapshot(System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber)">
      <summary>Takes a snapshot of the log store state between the specified sequence numbers for making a backup. </summary>
      <returns>A <see cref="T:System.IO.Log.LogArchiveSnapshot" /> object that contains the state necessary to make an archive. </returns>
      <param name="first">The starting sequence number in the range to archive. </param>
      <param name="last">The ending sequence number in the range to archive. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="first" /> or <paramref name="last" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="first" /> is larger than <paramref name="last" />.</exception>
      <exception cref="T:System.InvalidOperationException">An invalid operation has been executed.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when creating the archive snapshot. </exception>
      <exception cref="T:System.NotSupportedException">The log store is not archivable. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of a program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.LogStore.Delete(System.String)">
      <summary>Removes the log store.</summary>
      <param name="path">A relative or absolute path for the base file of the log store to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string ("").-or-<paramref name="path" /> contains only white space.-or-<paramref name="path" /> contains one or more invalid characters. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when opening the log store. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system. </exception>
    </member>
    <member name="M:System.IO.Log.LogStore.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.IO.Log.LogStore" />. </summary>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
    </member>
    <member name="P:System.IO.Log.LogStore.Extents">
      <summary>Gets the collection of log extents that contain the data for this log store. </summary>
      <returns>A <see cref="T:System.IO.Log.LogExtentCollection" /> instance containing the collection of log extents that encapsulates data for this log store. </returns>
    </member>
    <member name="P:System.IO.Log.LogStore.FreeBytes">
      <summary>Gets the number of bytes available in the log store. </summary>
      <returns>The number of bytes available in the log store. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Log.LogStore.Handle">
      <summary>Gets the operating system file handle for the log file that the current <see cref="T:System.IO.Log.LogStore" /> instance encapsulates. </summary>
      <returns>The operating system file handle for the log file that the current <see cref="T:System.IO.Log.LogStore" /> instance encapsulates. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Log.LogStore.LastSequenceNumber">
      <summary>When overridden in a derived class, gets the sequence number of the next record to be appended to the log store. </summary>
      <returns>The sequence number of the next record to be appended to the log store. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Log.LogStore.Length">
      <summary>Gets the size of the log store, in bytes. </summary>
      <returns>The size of the log store, in bytes. </returns>
      <exception cref="T:System.ObjectDisposedException">The property was accessed after the sequence has been disposed of. </exception>
    </member>
    <member name="P:System.IO.Log.LogStore.Policy">
      <summary>Gets the policy associated with this log store. </summary>
      <returns>A <see cref="T:System.IO.Log.LogPolicy" /> instance representing the policy associated with this log store. </returns>
    </member>
    <member name="M:System.IO.Log.LogStore.SetArchiveTail(System.IO.Log.SequenceNumber)">
      <summary>Sets the sequence number of the archive tail. </summary>
      <param name="archiveTail">The sequence number of the archive tail. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="archiveTail" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveTail" /> is not valid for this sequence.</exception>
      <exception cref="T:System.InvalidOperationException">An invalid operation has been executed.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when creating the archive snapshot. </exception>
      <exception cref="T:System.NotSupportedException">The log store is not archivable. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of a program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Log.LogStore.StreamCount">
      <summary>Gets the number of log streams in this log store.</summary>
      <returns>The number of log streams in this log store.</returns>
    </member>
    <member name="T:System.IO.Log.PolicyUnit">
      <summary>Represents a size measurement in a log store policy. </summary>
    </member>
    <member name="M:System.IO.Log.PolicyUnit.#ctor(System.Int64,System.IO.Log.PolicyUnitType)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.PolicyUnit" /> structure with the specified value and type. </summary>
      <param name="value">The size of the <see cref="T:System.IO.Log.PolicyUnit" />. </param>
      <param name="type">One of the <see cref="T:System.IO.Log.PolicyUnitType" /> values. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> or <paramref name="type" /> is not a valid value.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
    </member>
    <member name="M:System.IO.Log.PolicyUnit.Equals(System.Object)">
      <summary>Indicates whether this instance and a specified object are equal. </summary>
      <returns>true if this instance and a specified object are equal; otherwise, false. </returns>
      <param name="obj">The specified object to be checked.</param>
    </member>
    <member name="M:System.IO.Log.PolicyUnit.Extents(System.Int64)">
      <summary>Creates a <see cref="T:System.IO.Log.PolicyUnit" /> of type <see cref="F:System.IO.Log.PolicyUnitType.Extents" /> from the specified number. </summary>
      <returns>A <see cref="T:System.IO.Log.PolicyUnit" /> of type <see cref="F:System.IO.Log.PolicyUnitType.Extents" /> from the specified number. </returns>
      <param name="value">A number that represents the number of containers. </param>
    </member>
    <member name="M:System.IO.Log.PolicyUnit.GetHashCode">
      <summary>Provides a unique hash code for this object.</summary>
      <returns>An integer with the hash code value.</returns>
    </member>
    <member name="M:System.IO.Log.PolicyUnit.op_Equality(System.IO.Log.PolicyUnit,System.IO.Log.PolicyUnit)">
      <summary>Compares two <see cref="T:System.IO.Log.PolicyUnit" /> instances to determine whether they are equal. </summary>
      <returns>true if both <see cref="T:System.IO.Log.PolicyUnit" /> instances are equal; otherwise, false. </returns>
      <param name="left">The <see cref="T:System.IO.Log.PolicyUnit" /> on the left side of the operator. </param>
      <param name="right">The <see cref="T:System.IO.Log.PolicyUnit" /> on the right side of the operator. </param>
    </member>
    <member name="M:System.IO.Log.PolicyUnit.op_Inequality(System.IO.Log.PolicyUnit,System.IO.Log.PolicyUnit)">
      <summary>Compares two <see cref="T:System.IO.Log.PolicyUnit" /> instances to determine whether they are not equal. </summary>
      <returns>true if both <see cref="T:System.IO.Log.PolicyUnit" /> instances are not equal; otherwise, false. </returns>
      <param name="left">The <see cref="T:System.IO.Log.PolicyUnit" /> on the left side of the operator. </param>
      <param name="right">The <see cref="T:System.IO.Log.PolicyUnit" /> on the right side of the operator. </param>
    </member>
    <member name="M:System.IO.Log.PolicyUnit.Percentage(System.Int64)">
      <summary>Creates a <see cref="T:System.IO.Log.PolicyUnit" /> of type <see cref="F:System.IO.Log.PolicyUnitType.Percentage" /> from the specified number. </summary>
      <returns>A <see cref="T:System.IO.Log.PolicyUnit" /> of type <see cref="F:System.IO.Log.PolicyUnitType.Percentage" /> from the specified number. </returns>
      <param name="value">A number that represents the percentage. </param>
    </member>
    <member name="M:System.IO.Log.PolicyUnit.ToString">
      <summary>Returns a string representation of this object.</summary>
      <returns>A string representing this object.</returns>
    </member>
    <member name="P:System.IO.Log.PolicyUnit.Type">
      <summary>Gets the unit type of the <see cref="T:System.IO.Log.PolicyUnit" />. </summary>
      <returns>One of the <see cref="T:System.IO.Log.PolicyUnitType" /> values. The default is <see cref="F:System.IO.Log.PolicyUnitType.Extents" />. </returns>
    </member>
    <member name="P:System.IO.Log.PolicyUnit.Value">
      <summary>Gets the value of the <see cref="T:System.IO.Log.PolicyUnit" />. </summary>
      <returns>The value of the <see cref="T:System.IO.Log.PolicyUnit" />. </returns>
    </member>
    <member name="T:System.IO.Log.PolicyUnitType">
      <summary>Specifies the unit of measurement of a <see cref="T:System.IO.Log.PolicyUnit" /> instance. </summary>
    </member>
    <member name="F:System.IO.Log.PolicyUnitType.Extents">
      <summary>The measurement is in containers.</summary>
    </member>
    <member name="F:System.IO.Log.PolicyUnitType.Percentage">
      <summary>The measurement is a percentage of the size of the <see cref="T:System.IO.Log.LogStore" />.</summary>
    </member>
    <member name="T:System.IO.Log.RecordAppendOptions">
      <summary>Specifies how records are appended. </summary>
    </member>
    <member name="F:System.IO.Log.RecordAppendOptions.None">
      <summary>Data is written lazily.</summary>
    </member>
    <member name="F:System.IO.Log.RecordAppendOptions.ForceAppend">
      <summary>The record sequence should begin the process of flushing internal buffers after this record is appended. This does not indicate that the record should be durably written before the Append operation completes. To get that behavior, specify the <see cref="F:System.IO.Log.RecordAppendOptions.ForceFlush" /> flag. </summary>
    </member>
    <member name="F:System.IO.Log.RecordAppendOptions.ForceFlush">
      <summary>The record sequence should flush any internal buffers after this record is appended. When the Append operation completes, the specified record has been durably written. </summary>
    </member>
    <member name="T:System.IO.Log.ReservationCollection">
      <summary>Manages the collection of space reservations made in a record sequence. </summary>
    </member>
    <member name="M:System.IO.Log.ReservationCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.ReservationCollection" /> class.  </summary>
    </member>
    <member name="M:System.IO.Log.ReservationCollection.Add(System.Int64)">
      <summary>Allocates a reservation and adds it to the collection. This method cannot be inherited.</summary>
      <param name="size">The size of the requested reservation, in bytes. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="archiveTail" /> is not between the base and last sequence numbers of this sequence.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="size" /> is invalid or exceeds maximum reservation size.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when creating the archive snapshot. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
    </member>
    <member name="M:System.IO.Log.ReservationCollection.Clear">
      <summary>Releases all of the reservations in the collection. This method cannot be inherited.</summary>
      <exception cref="T:System.ArgumentException">
        <paramref name="size" /> is invalid or exceeds maximum reservation size.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when creating the archive snapshot. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.ReservationCollection.Contains(System.Int64)">
      <summary>Determines whether a reservation of the specified size has been made. This method cannot be inherited.</summary>
      <returns>true if at least one reservation of the specified size has been made; otherwise, false. </returns>
      <param name="size">The size to locate in the collection. </param>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.ReservationCollection.CopyTo(System.Int64[],System.Int32)">
      <summary>Copies the entire collection to a one-dimensional array, starting at the specified index of the target array. This method cannot be inherited. </summary>
      <param name="array">The array that is the destination of the copy. </param>
      <param name="arrayIndex">The zero-based index in the array at which copying begins. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> or <paramref name="arrayIndex" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero, or greater than or equal to the length of <paramref name="array" />. </exception>
      <exception cref="T:System.ArgumentException">There is not enough space in the specified array to copy the data.-or-<paramref name="array" /> is not a single dimensional array, which is the only type supported for the requested action.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="P:System.IO.Log.ReservationCollection.Count">
      <summary>Gets the number of reservations in the collection. </summary>
      <returns>The number of reservations in the collection. </returns>
    </member>
    <member name="M:System.IO.Log.ReservationCollection.Finalize">
      <summary>Allows the <see cref="T:System.IO.Log.ReservationCollection" /> to free resources before it is destroyed by the garbage collector.</summary>
    </member>
    <member name="M:System.IO.Log.ReservationCollection.FreeReservation(System.Int64)">
      <summary>When overridden in a derived class, releases a reservation of the specified size. </summary>
      <param name="size">The size of the reservation to release. </param>
    </member>
    <member name="M:System.IO.Log.ReservationCollection.GetBestMatchingReservation(System.Int64)">
      <summary>When overridden in a derived class, gets the best matching reservation for a given size. </summary>
      <returns>A reservation from the collection that is greater than or equal to the specified size, or -1 if no such reservation exists. </returns>
      <param name="size">The size of the reservation that must be fit. </param>
    </member>
    <member name="M:System.IO.Log.ReservationCollection.GetEnumerator">
      <summary>Returns an enumerator that can iterate through the <see cref="T:System.IO.Log.ReservationCollection" />. This method cannot be inherited.</summary>
      <returns>An enumerator that can iterate through the <see cref="T:System.IO.Log.ReservationCollection" />.</returns>
      <exception cref="T:System.InvalidOperationException">The operation is invalid for one of the following reasons:The enumeration has ended.The collection has been modified.The enumeration has not been started. A call to <see cref="M:System.Collections.IEnumerator.MoveNext" /> must be made.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="P:System.IO.Log.ReservationCollection.IsReadOnly">
      <summary>Gets a value that indicates whether the collection is read-only.</summary>
      <returns>This property always returns false. </returns>
    </member>
    <member name="M:System.IO.Log.ReservationCollection.MakeReservation(System.Int64)">
      <summary>When overridden in a derived class, allocates a reservation of the specified size. </summary>
      <param name="size">The size of the reservation to allocate. </param>
    </member>
    <member name="M:System.IO.Log.ReservationCollection.Remove(System.Int64)">
      <summary>Releases a reservation of the specified size, if one has been made. This method cannot be inherited.</summary>
      <returns>true if a reservation of the specified size was found and removed; otherwise, false. </returns>
      <param name="item">The size of the reservation to release. </param>
      <exception cref="T:System.ArgumentException">No reservation large enough to fit the provided record can be found in the specified reservation collection.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs when creating the archive snapshot. </exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.IO.Log.SequenceFullException">The record sequence is full.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="M:System.IO.Log.ReservationCollection.ReservationFreed(System.Int64)">
      <summary>When overridden in a derived class, indicates that a reservation has been released. </summary>
      <param name="size">The size of the reservation that has been released. </param>
    </member>
    <member name="M:System.IO.Log.ReservationCollection.ReservationMade(System.Int64)">
      <summary>When overridden in a derived class, indicates that a reservation has been made. </summary>
      <param name="size">The size of the reservation that has been made. </param>
    </member>
    <member name="M:System.IO.Log.ReservationCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that can iterate through the <see cref="T:System.IO.Log.ReservationCollection" />. This method cannot be inherited.</summary>
      <returns>An enumerator that can iterate through the <see cref="T:System.IO.Log.ReservationCollection" />.</returns>
      <exception cref="T:System.InvalidOperationException">The operation is invalid for one of the following reasons:The enumeration has ended.The collection has been modified.The enumeration has not been started. A call to <see cref="M:System.Collections.IEnumerator.MoveNext" /> must be made.</exception>
      <exception cref="T:System.ObjectDisposedException">The method was called after the sequence has been disposed of. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to continue the execution of the program.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access for the specified log store is denied by the operating system.</exception>
    </member>
    <member name="T:System.IO.Log.ReservationNotFoundException">
      <summary>Represents the exception that is thrown when a specific space reservation in a <see cref="T:System.IO.Log.LogRecordSequence" /> is not found.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.ReservationNotFoundException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.ReservationNotFoundException" /> class. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.ReservationNotFoundException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.ReservationNotFoundException" /> class with serialized data.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
    </member>
    <member name="M:System.IO.Log.ReservationNotFoundException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.ReservationNotFoundException" /> class with a specified error message.</summary>
      <param name="message">The message that describes the error.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.ReservationNotFoundException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.ReservationNotFoundException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the error.</param>
      <param name="inner">The exception that is the cause of the current exception.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.Log.SequenceFullException">
      <summary>Represents the exception that is thrown when a sequence of log records is full.</summary>
    </member>
    <member name="M:System.IO.Log.SequenceFullException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.SequenceFullException" /> class.  </summary>
    </member>
    <member name="M:System.IO.Log.SequenceFullException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.SequenceFullException" /> class with serialized data.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
    </member>
    <member name="M:System.IO.Log.SequenceFullException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.SequenceFullException" /> class with a specified error message.</summary>
      <param name="message">The message that describes the error.</param>
    </member>
    <member name="M:System.IO.Log.SequenceFullException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.SequenceFullException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the error.</param>
      <param name="inner">The exception that is the cause of the current exception.</param>
    </member>
    <member name="T:System.IO.Log.SequenceNumber">
      <summary>Represents a sequence number assigned to a log record in a record sequence. </summary>
    </member>
    <member name="M:System.IO.Log.SequenceNumber.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.SequenceNumber" /> class.  </summary>
      <param name="sequenceNumber">Bytes that make up the sequence number.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sequenceNumber" /> is of the wrong length, or does not represent a valid sequence number.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.SequenceNumber.CompareTo(System.IO.Log.SequenceNumber)">
      <summary>Compares this sequence number with a specified object.</summary>
      <returns>A 32-bit signed integer indicating the lexical relationship between the two comparands.ValueConditionLess than zeroThis instance is less than <paramref name="other" />.ZeroThis instance is equal to <paramref name="other" />.Greater than zeroThis instance is greater than <paramref name="other" />. </returns>
      <param name="other">An object that evaluates to the current sequence number.</param>
    </member>
    <member name="M:System.IO.Log.SequenceNumber.Equals(System.IO.Log.SequenceNumber)">
      <summary>Indicates whether this instance and a specified sequence number are equal.</summary>
      <returns>true if <paramref name="other" /> and this instance represent the same value; otherwise, false.</returns>
      <param name="other">Another <see cref="T:System.IO.Log.SequenceNumber" /> to compare to.</param>
    </member>
    <member name="M:System.IO.Log.SequenceNumber.Equals(System.Object)">
      <summary>Indicates whether this instance and a specified object are equal.</summary>
      <returns>true if <paramref name="other" /> and this instance are the same type and represent the same value; otherwise, false.</returns>
      <param name="other">Another object to compare to.</param>
    </member>
    <member name="M:System.IO.Log.SequenceNumber.GetBytes">
      <summary>Encodes all or part of the current sequence number into a byte array.</summary>
      <returns>A byte array containing all or part of the current sequence number.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.SequenceNumber.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Log.SequenceNumber.Invalid">
      <summary>Gets a sequence number that serves as a default value for representing an invalid sequence number.</summary>
      <returns>A <see cref="T:System.IO.Log.SequenceNumber" /> initialized to UInt64.<see cref="F:System.UInt64.MaxValue" /> that serves as a default value for representing an invalid sequence number.</returns>
    </member>
    <member name="M:System.IO.Log.SequenceNumber.op_Equality(System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber)">
      <summary>Compares two <see cref="T:System.IO.Log.SequenceNumber" /> instances to determine whether they are equal. </summary>
      <returns>true if both <see cref="T:System.IO.Log.SequenceNumber" /> instances are equal; otherwise, false. </returns>
      <param name="c1">The <see cref="T:System.IO.Log.SequenceNumber" /> on the left side of the operator. </param>
      <param name="c2">The <see cref="T:System.IO.Log.SequenceNumber" /> on the right side of the operator. </param>
    </member>
    <member name="M:System.IO.Log.SequenceNumber.op_GreaterThan(System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber)">
      <summary>Performs a logical comparison of two <see cref="T:System.IO.Log.SequenceNumber" /> instances to determine whether the first is greater than the second.</summary>
      <returns>true if the first instance is greater than the second instance, otherwise, false. </returns>
      <param name="c1">The <see cref="T:System.IO.Log.SequenceNumber" /> on the left side of the operator. </param>
      <param name="c2">The <see cref="T:System.IO.Log.SequenceNumber" /> on the right side of the operator. </param>
    </member>
    <member name="M:System.IO.Log.SequenceNumber.op_GreaterThanOrEqual(System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber)">
      <summary>Performs a logical comparison of two <see cref="T:System.IO.Log.SequenceNumber" /> instances to determine whether the first is greater than or equal to the second.</summary>
      <returns>true if the first instance is greater than or equal to the second instance, otherwise, false. </returns>
      <param name="c1">The <see cref="T:System.IO.Log.SequenceNumber" /> on the left side of the operator. </param>
      <param name="c2">The <see cref="T:System.IO.Log.SequenceNumber" /> on the right side of the operator. </param>
    </member>
    <member name="M:System.IO.Log.SequenceNumber.op_Inequality(System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber)">
      <summary>Compares two <see cref="T:System.IO.Log.SequenceNumber" /> instances to determine whether they are not equal. </summary>
      <returns>true if both <see cref="T:System.IO.Log.SequenceNumber" /> instances are not equal, otherwise, false. </returns>
      <param name="c1">The <see cref="T:System.IO.Log.SequenceNumber" /> on the left side of the operator. </param>
      <param name="c2">The <see cref="T:System.IO.Log.SequenceNumber" /> on the right side of the operator. </param>
    </member>
    <member name="M:System.IO.Log.SequenceNumber.op_LessThan(System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber)">
      <summary>Performs a logical comparison of the two <see cref="T:System.IO.Log.SequenceNumber" /> parameters to determine whether the first is less than the second.</summary>
      <returns>true if the first instance is less than the second instance, otherwise, false. </returns>
      <param name="c1">The <see cref="T:System.IO.Log.SequenceNumber" /> on the left side of the operator. </param>
      <param name="c2">The <see cref="T:System.IO.Log.SequenceNumber" /> on the right side of the operator. </param>
    </member>
    <member name="M:System.IO.Log.SequenceNumber.op_LessThanOrEqual(System.IO.Log.SequenceNumber,System.IO.Log.SequenceNumber)">
      <summary>Performs a logical comparison of the two <see cref="T:System.IO.Log.SequenceNumber" /> parameters to determine whether the first is less than or equal to the second.</summary>
      <returns>true if the first instance is less than or equal to the second instance, otherwise, false. </returns>
      <param name="c1">The <see cref="T:System.IO.Log.SequenceNumber" /> on the left side of the operator. </param>
      <param name="c2">The <see cref="T:System.IO.Log.SequenceNumber" /> on the right side of the operator. </param>
    </member>
    <member name="T:System.IO.Log.TailPinnedEventArgs">
      <summary>Provides data for the <see cref="E:System.IO.Log.IRecordSequence.TailPinned" /> event. This class cannot be inherited.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Log.TailPinnedEventArgs.#ctor(System.IO.Log.SequenceNumber)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Log.TailPinnedEventArgs" /> class.  </summary>
      <param name="sequenceNumber">The target sequence number. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Log.TailPinnedEventArgs.TargetSequenceNumber">
      <summary>Gets the sequence number that the tail should be moved past. </summary>
      <returns>The sequence number that the tail should be moved past.</returns>
      <filterpriority>2</filterpriority>
    </member>
  </members>
</doc>