using System;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools.WebBroswerEx
{
    /// <summary>
    /// 智能Web控件 - 自动选择最佳的网页加载器
    /// 专注于核心的网页加载功能，保持简单
    /// </summary>
    public class SmartWebControl : UserControl
    {
        private IWebLoader _loader;
        private LoadingIndicator _loadingIndicator;
        private bool _isInitialized = false;
        private Timer _loadingTimer;
        private TaskCompletionSource<bool> _initializationTcs;

        // 事件定义 - 保持向后兼容
        public event EventHandler<string> LoadingStarted;
        public event EventHandler<LoadCompletedEventArgs> LoadingCompleted;
        public event EventHandler NavigationCompleted;
        public event EventHandler<WebBrowserNavigateErrorEventArgs> NavigationError;

        // 简化的属性
        public string DocumentTitle => _loader?.DocumentTitle ?? string.Empty;
        public string CurrentUrl => _loader?.CurrentUrl ?? string.Empty;
        public string LoaderName => _loader?.LoaderName ?? "未初始化";
        public bool IsWebView2 => _loader is WebView2Loader;
        public bool IsInitialized => _isInitialized;

        public SmartWebControl()
        {
            Dock = DockStyle.Fill;

            SetupWindowStyles();

            // 初始化加载指示器
            _loadingIndicator = new LoadingIndicator();
            _loadingIndicator.SetParent(this);

            InitializeLoader();
        }

        /// <summary>
        /// 设置窗口样式优化，解决WebView2控件闪烁问题
        /// </summary>
        private void SetupWindowStyles()
        {
            try
            {
                // 启用双缓冲
                CommonMethod.EnableDoubleBuffering(this);

                // 设置控件样式：启用AllPaintingInWmPaint和UserPaint，禁用Opaque
                CommonMethod.SetStyle(this, ControlStyles.AllPaintingInWmPaint, true);
                CommonMethod.SetStyle(this, ControlStyles.UserPaint, true);
                CommonMethod.SetStyle(this, ControlStyles.DoubleBuffer, true);
                CommonMethod.SetStyle(this, ControlStyles.ResizeRedraw, true);
                CommonMethod.SetStyle(this, ControlStyles.Opaque, false);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SmartWebControl窗口样式设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化加载器
        /// </summary>
        private void InitializeLoader()
        {
            // 创建初始化任务完成源
            _initializationTcs = new TaskCompletionSource<bool>();

            if (ApplicationWebManager.IsWebView2Available)
            {
                _loader = new WebView2Loader();
            }
            else
            {
                _loader = new WebBrowserLoader();
            }

            _ = CompleteInitializationAsync();
        }

        /// <summary>
        /// 完成异步初始化（只处理UI相关的异步操作）
        /// 包含自动降级逻辑：WebView2失败时自动降级到WebBrowser
        /// </summary>
        private async Task<bool> CompleteInitializationAsync()
        {
            try
            {
                var initResult = await _loader.InitializeAsync();

                if (initResult && _loader.WebControl != null && await ValidateLoaderFunctionality())
                {
                    BindLoaderEvents();
                    AddWebControlToUI();
                    _isInitialized = true;
                    _initializationTcs?.TrySetResult(true);
                    return true;
                }
            }
            catch (Exception ex)
            {
            }

            // 初始化失败，尝试降级
            if (_loader is WebView2Loader)
            {
                ApplicationWebManager.MarkWebView2RuntimeFailed();
                _loader?.Dispose();
                _loader = new WebBrowserLoader();
                return await CompleteInitializationAsync();
            }

            // 如果所有尝试都失败，通知等待者
            _initializationTcs?.TrySetResult(false);
            return false;
        }

        /// <summary>
        /// 验证加载器功能是否正常
        /// </summary>
        private async Task<bool> ValidateLoaderFunctionality()
        {
            try
            {
                if (_loader?.WebControl == null)
                {
                    return false;
                }

                // 对于WebView2，验证CoreWebView2
                if (_loader is WebView2Loader webView2Loader)
                {
                    var coreWebView2 = webView2Loader.GetType()
                        .GetField("_coreWebView2", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                        ?.GetValue(webView2Loader);

                    if (coreWebView2 == null)
                    {
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        /// <summary>
        /// 绑定加载器事件
        /// </summary>
        private void BindLoaderEvents()
        {
            _loader.LoadingStarted += OnLoadingStarted;
            _loader.LoadingCompleted += OnLoadingCompleted;
            _loader.TitleChanged += OnTitleChanged;
        }

        /// <summary>
        /// 将Web控件添加到UI
        /// </summary>
        private void AddWebControlToUI()
        {
            if (InvokeRequired)
            {
                BeginInvoke(new Action(() => Controls.Add(_loader.WebControl)));
            }
            else
            {
                Controls.Add(_loader.WebControl);
            }
        }

        /// <summary>
        /// 加载开始事件处理
        /// </summary>
        private void OnLoadingStarted(object sender, string url)
        {
            _loadingIndicator?.Show();
            LoadingStarted?.Invoke(this, url);

            // 启动3秒超时保护
            StartLoadingTimeout();
        }

        /// <summary>
        /// 加载完成事件处理
        /// </summary>
        private void OnLoadingCompleted(object sender, LoadCompletedEventArgs e)
        {
            StopLoadingTimeout();
            _loadingIndicator?.Hide();
            LoadingCompleted?.Invoke(this, e);

            // 触发兼容事件
            if (e.IsSuccess) NavigationCompleted?.Invoke(this, EventArgs.Empty);
            else NavigationError?.Invoke(this, new WebBrowserNavigateErrorEventArgs(e.Url, -1));
        }

        /// <summary>
        /// 标题变化事件处理
        /// </summary>
        private void OnTitleChanged(object sender, string title)
        {
            UpdateParentFormTitle(title);
        }

        /// <summary>
        /// 启动loading超时保护（3秒）
        /// </summary>
        private void StartLoadingTimeout()
        {
            StopLoadingTimeout();
            _loadingTimer = new Timer
            {
                Interval = 3000
            };
            _loadingTimer.Tick += OnLoadingTimeout;
            _loadingTimer.Start();
        }

        /// <summary>
        /// 停止loading超时计时器
        /// </summary>
        private void StopLoadingTimeout()
        {
            if (_loadingTimer != null)
            {
                _loadingTimer.Stop();
                _loadingTimer.Dispose();
                _loadingTimer = null;
            }
        }

        /// <summary>
        /// Loading超时处理
        /// </summary>
        private void OnLoadingTimeout(object sender, EventArgs e)
        {
            StopLoadingTimeout();
            _loadingIndicator?.Hide();
        }

        /// <summary>
        /// 更新父窗体标题（提取为独立方法，便于控制）
        /// </summary>
        private void UpdateParentFormTitle(string title)
        {
            if (string.IsNullOrEmpty(title)) return;
            try
            {
                var form = FindForm();
                if (form?.IsDisposed == false && form.Visible && form is FrmViewUrl)
                {
                    form.Text = title;
                    form.Invalidate(new System.Drawing.Region(new System.Drawing.RectangleF(0, 0, Width, 60)));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新窗体标题失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载网页 - 核心功能
        /// </summary>
        public async Task<bool> LoadAsync(string url, string postData = null)
        {
            if (string.IsNullOrWhiteSpace(url))
            {
                throw new ArgumentException("URL不能为空", nameof(url));
            }

            // 立即显示加载指示器，避免空白等待
            _loadingIndicator?.Show();

            try
            {
                // 等待初始化完成
                if (_initializationTcs != null)
                {
                    var initResult = await _initializationTcs.Task;
                    if (!initResult)
                    {
                        return false;
                    }
                }

                // 检查加载器是否可用
                if (_loader == null || !_isInitialized)
                {
                    return false;
                }

                return await _loader.LoadAsync(url, postData);
            }
            catch
            {
                // 如果加载失败，停止超时计时器并隐藏加载指示器
                StopLoadingTimeout();
                _loadingIndicator?.Hide();
                return false;
            }
        }

        /// <summary>
        /// 执行JavaScript脚本
        /// </summary>
        /// <param name="script">要执行的JavaScript代码</param>
        /// <returns>脚本执行结果，如果不支持则返回null</returns>
        public async Task<string> ExecuteScriptAsync(string script)
        {
            if (string.IsNullOrWhiteSpace(script) || _loader == null)
            {
                return null;
            }

            try
            {
                // 如果还在初始化中，等待一小段时间
                if (!_isInitialized)
                {
                    return null;
                }

                // 统一处理UI线程调用
                if (this.InvokeRequired)
                {
                    var tcs = new TaskCompletionSource<string>();
                    this.BeginInvoke(new Action(async () =>
                    {
                        try
                        {
                            var result = await _loader.ExecuteScriptAsync(script);
                            tcs.SetResult(result);
                        }
                        catch (Exception ex)
                        {
                            tcs.SetException(ex);
                        }
                    }));
                    return await tcs.Task;
                }
                else
                {
                    return await _loader.ExecuteScriptAsync(script);
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                try
                {
                    // 清理超时计时器
                    StopLoadingTimeout();

                    _loadingIndicator?.Dispose();
                    _loadingIndicator = null;

                    _loader?.Dispose();
                    _loader = null;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"SmartWebControl释放资源失败: {ex.Message}");
                }
            }
            base.Dispose(disposing);
        }

        /// <summary>
        /// 导航错误事件处理器委托
        /// </summary>
        public delegate void WebBrowserNavigateErrorEventHandler(object sender, WebBrowserNavigateErrorEventArgs e);

        /// <summary>
        /// 导航错误事件参数
        /// </summary>
        public class WebBrowserNavigateErrorEventArgs : EventArgs
        {
            public WebBrowserNavigateErrorEventArgs(string url, int statusCode)
            {
                Url = url;
                StatusCode = statusCode;
            }

            public string Url { get; set; }
            public int StatusCode { get; set; }
        }
    }
}