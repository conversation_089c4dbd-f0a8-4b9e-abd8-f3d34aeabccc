using System.Collections.Generic;
using System.Text;
using Emgu.CV;

namespace OcrLib
{
	public sealed class OcrResult
	{
		public List<TextBlock> TextBlocks { get; set; }

		public float DbNetTime { get; set; }

		public Mat BoxImg { get; set; }

		public float DetectTime { get; set; }

		public string StrRes { get; set; }

		public override string ToString()
		{
			StringBuilder sb = new StringBuilder();
			sb.AppendLine("OcrResult");
			TextBlocks.ForEach(delegate(TextBlock x)
			{
				sb.Append(x);
			});
			sb.AppendLine($"├─DbNetTime({DbNetTime}ms)");
			sb.AppendLine($"├─DetectTime({DetectTime}ms)");
			sb.AppendLine("└─StrRes(" + StrRes + ")");
			return sb.ToString();
		}
	}
}
