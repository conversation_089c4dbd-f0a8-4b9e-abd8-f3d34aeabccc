﻿using OCRTools.MyShadowForm;
using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace OCRTools.Shadow
{
    internal partial class ShadowFormSkin : Form
    {
        //private Point _MouseLocation;
        //internal Point MouseLocation { get { return _MouseLocation; } }

        private readonly ShadowForm _main;

        public ShadowFormSkin(ShadowForm main)
        {
            InitializeComponent();
            SetStyles(); //减少闪烁
            _main = main; //获取控件层对象
            Text = _main.Text; //设置标题栏文本
            Icon = _main.Icon; //设置图标
            TopMost = _main.TopMost;
            FormBorderStyle = FormBorderStyle.None; //设置无边框的窗口样式
            ShowInTaskbar = true; //显示阴影层到任务栏（win10可显示缩略图）
            Size = _main.Size; //统一大小
            _main.Owner = this; //设置控件层的拥有皮肤层
            //SetBits();//绘制半透明不规则皮肤
            this.AutoSizeMutilScreen();
        }

        #region 减少闪烁

        private void SetStyles()
        {
            SetStyle(
                ControlStyles.UserPaint |
                ControlStyles.AllPaintingInWmPaint |
                ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.ResizeRedraw |
                ControlStyles.DoubleBuffer, true);
            //强制分配样式重新应用到控件上
            UpdateStyles();
            AutoScaleMode = AutoScaleMode.None;
        }

        #endregion

        #region 绘图

        protected override CreateParams CreateParams
        {
            get
            {
                var cParms = base.CreateParams;
                cParms.ExStyle |= 0x00080000; // WS_EX_LAYERED
                return cParms;
            }
        }

        internal void DrawShadow(Color shadowColor)
        {
            Width = _main.Width + _main.ShadowWidth * 2;
            Height = _main.Height + _main.ShadowWidth * 2;

            Bitmap bitmap = null;
            Graphics g = null;
            try
            {
                bitmap = new Bitmap(Width, Height);
                g = Graphics.FromImage(bitmap);
                g.SmoothingMode = SmoothingMode.AntiAlias;

                //Color c = Color.FromArgb(0, 64, 133, 249);
                using (var p = new Pen(shadowColor, 1))
                {
                    for (var i = 0; i < _main.ShadowWidth; i++)
                    {
                        var tmp = 255 / _main.ShadowWidth * i;
                        p.Color = Color.FromArgb(Math.Min(tmp, 255), shadowColor);
                        //g.DrawRectangle(p, new Rectangle(i, i, Width - (2 * i) - 1, Height - (2 * i) - 1));
                        DrawRoundRectangle(g, p, new Rectangle(i, i, Width - 2 * i - 1, Height - 2 * i - 1),
                            _main.ShadowWidth - i);
                    }
                }

                SetBits(bitmap);
            }
            catch
            {
            }
            finally
            {
                g?.Dispose();
                bitmap?.Dispose();
            }
        }

        public void SetBits(Bitmap bitmap)
        {
            if (!Image.IsCanonicalPixelFormat(bitmap.PixelFormat) || !Image.IsAlphaPixelFormat(bitmap.PixelFormat))
                throw new ApplicationException("图片必须是32位带Alhpa通道的图片。");
            var oldBits = IntPtr.Zero;
            var screenDc = FormStyleApi.GetDC(IntPtr.Zero);
            var hBitmap = IntPtr.Zero;
            var memDc = FormStyleApi.CreateCompatibleDC(screenDc);

            try
            {
                var topLoc = new FormStyleApi.Point(Left, Top);
                var bitMapSize = new FormStyleApi.Size(Width, Height);
                var blendFunc = new FormStyleApi.BLENDFUNCTION();
                var srcLoc = new FormStyleApi.Point(0, 0);

                hBitmap = bitmap.GetHbitmap(Color.FromArgb(0));
                oldBits = FormStyleApi.SelectObject(memDc, hBitmap);

                blendFunc.BlendOp = FormStyleApi.AcSrcOver;
                blendFunc.SourceConstantAlpha = byte.Parse(((int)(_main.Opacity * 255)).ToString());
                blendFunc.AlphaFormat = FormStyleApi.AcSrcAlpha;
                blendFunc.BlendFlags = 0;

                FormStyleApi.UpdateLayeredWindow(Handle, screenDc, ref topLoc, ref bitMapSize, memDc, ref srcLoc, 0,
                    ref blendFunc, FormStyleApi.UlwAlpha);
            }
            finally
            {
                if (hBitmap != IntPtr.Zero)
                {
                    FormStyleApi.SelectObject(memDc, oldBits);
                    FormStyleApi.DeleteObject(hBitmap);
                }

                FormStyleApi.ReleaseDC(IntPtr.Zero, screenDc);
                FormStyleApi.DeleteDC(memDc);
            }
        }

        #endregion

        #region MyRegion

        public static void DrawRoundRectangle(Graphics g, Pen pen, Rectangle rect, int cornerRadius)
        {
            using (var path = CreateRoundedRectanglePath(rect, cornerRadius))
            {
                g.DrawPath(pen, path);
            }
        }

        public static GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int cornerRadius)
        {
            var roundedRect = new GraphicsPath();
            roundedRect.AddArc(rect.X, rect.Y, cornerRadius * 2, cornerRadius * 2, 180, 90);
            roundedRect.AddLine(rect.X + cornerRadius, rect.Y, rect.Right - cornerRadius * 2, rect.Y);
            roundedRect.AddArc(rect.X + rect.Width - cornerRadius * 2, rect.Y, cornerRadius * 2, cornerRadius * 2, 270,
                90);
            roundedRect.AddLine(rect.Right, rect.Y + cornerRadius * 2, rect.Right,
                rect.Y + rect.Height - cornerRadius * 2);
            roundedRect.AddArc(rect.X + rect.Width - cornerRadius * 2, rect.Y + rect.Height - cornerRadius * 2,
                cornerRadius * 2, cornerRadius * 2, 0, 90);
            roundedRect.AddLine(rect.Right - cornerRadius * 2, rect.Bottom, rect.X + cornerRadius * 2, rect.Bottom);
            roundedRect.AddArc(rect.X, rect.Bottom - cornerRadius * 2, cornerRadius * 2, cornerRadius * 2, 90, 90);
            roundedRect.AddLine(rect.X, rect.Bottom - cornerRadius * 2, rect.X, rect.Y + cornerRadius * 2);
            roundedRect.CloseFigure();
            return roundedRect;
        }

        #endregion
    }
}