﻿using OCRTools.Common;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FormViewImage : Form
    {

        private UcContent ucContent;

        public FormViewImage()
        {
            Icon = FrmMain.FrmTool.Icon;
            Text = $"图片预览-{CommonString.FullName}";
            ShowIcon = true;
            ShowInTaskbar = true;
            ucContent = new UcContent
            {
                Dock = DockStyle.Fill,
                Image = null,
                IsShowOldContent = false,
                IsShowToolBox = true,
                IsShowTxt = false,
                Margin = CommonString.PaddingZero,
                Padding = CommonString.PaddingZero,
                Location = new Point(0, 0),
                Name = "ucContent",
                TabIndex = 0,
                TabStop = false
            };
            Controls.Add(ucContent);
            InitializeComponent();
            Shown += FormViewImage_Shown;
            FormClosing += FormViewImage_FormClosing;
        }

        private void FormViewImage_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                ucContent.SetImageZoomSmall(true);
                ucContent.Image?.Dispose();
                ucContent.Image = null;
                ucContent.Dispose();
                Dispose();
            }
            catch { }
            MemoryManager.ClearMemory();
        }

        private void FormViewImage_Shown(object sender, EventArgs e)
        {
            ucContent.SetImageZoomBig(true);
            MemoryManager.ClearMemory();
            SizeChanged += FormViewImage_SizeChanged;
            this.ForceActivate();
        }

        private void FormViewImage_SizeChanged(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Normal)
            {
                SizeChanged -= FormViewImage_SizeChanged;
                SetImageMode(ucContent.Image);
            }
            this.ForceActivate();
        }

        private void SetImageMode(Image image, bool isInit = false)
        {
            var width = image.Width * (isInit ? 100 : ucContent.Zoom) / 100 + (Width - ClientRectangle.Width);
            var height = image.Height * (isInit ? 100 : ucContent.Zoom) / 100 + (Height - ClientRectangle.Height);
            var minWidth = Screen.PrimaryScreen.WorkingArea.Width * 0.282;
            var minHeight = Screen.PrimaryScreen.WorkingArea.Height * 0.530;
            if (width < minWidth && height < minHeight)
            {
                width = (int)Math.Max(width, minWidth);
                height = (int)Math.Max(height, minHeight);
            }

            if (width > Screen.PrimaryScreen.WorkingArea.Width)
            {
                height += SystemInformation.HorizontalScrollBarArrowWidth;
            }
            if (height > Screen.PrimaryScreen.WorkingArea.Height)
            {
                width += SystemInformation.VerticalScrollBarWidth;
            }
            width = Math.Min(width, Screen.PrimaryScreen.WorkingArea.Width);
            height = Math.Min(height, Screen.PrimaryScreen.WorkingArea.Height);
            if (!isInit)
            {
                var size = new Size(width, height);
                for (int i = 0; i < 10; i++)
                {
                    if (!Equals(Size, size))
                    {
                        Size = size;
                        Application.DoEvents();
                    }
                    else
                    {
                        break;
                    }
                }
            }
            Location = new Point((Screen.PrimaryScreen.WorkingArea.Width - width) / 2, (Screen.PrimaryScreen.WorkingArea.Height - height) / 2);
        }

        internal void Bind(Image image, string fileName = null)
        {
            if (image == null && !string.IsNullOrEmpty(fileName))
            {
                image = Image.FromFile(fileName);
            }
            Text = $"图片预览 尺寸:{image.Width}×{image.Height} - {CommonString.FullName}";
            ucContent.BindImageOnly(image, null, true);
            ucContent.NowDisplayMode = DisplayModel.图文模式;
            ucContent.RefreshStyle();
            ucContent.BindContentByOcr(FrmMain.EmptyContent);
            ucContent.SetCanClose();
            Location = new Point((int)(Screen.PrimaryScreen.WorkingArea.Width * 0.359), (int)(Screen.PrimaryScreen.WorkingArea.Height * 0.235));
            SetImageMode(ucContent.Image, true);
        }

        private void FrmPicCompare_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape) Close();
        }

        private void FormViewImage_Load(object sender, EventArgs e)
        {
            FormBorderStyle = FormBorderStyle.None;
            ucContent.SetImageMode(true);
            ucContent.ShowImageTool();
        }
    }
}
