﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel.Composition</name>
  </assembly>
  <members>
    <member name="T:System.Lazy`2">
      <summary>Provides a lazy indirect reference to an object and its associated metadata for use by the Managed Extensibility Framework.</summary>
      <typeparam name="T">The type of the object referenced.</typeparam>
      <typeparam name="TMetadata">The type of the metadata.</typeparam>
    </member>
    <member name="M:System.Lazy`2.#ctor(System.Func{`0},`1)">
      <summary>Initializes a new instance of the <see cref="T:System.Lazy`2" /> class with the specified metadata that uses the specified function to get the referenced object.</summary>
      <param name="valueFactory">A function that returns the referenced object.</param>
      <param name="metadata">The metadata associated with the referenced object.</param>
    </member>
    <member name="M:System.Lazy`2.#ctor(System.Func{`0},`1,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Lazy`2" /> class with the specified metadata and thread safety value that uses the specified function to get the referenced object.</summary>
      <param name="valueFactory">A function that returns the referenced object.</param>
      <param name="metadata">The metadata associated with the referenced object.</param>
      <param name="isThreadSafe">Indicates whether the <see cref="T:System.Lazy`2" /> object that is created will be thread-safe.</param>
    </member>
    <member name="M:System.Lazy`2.#ctor(System.Func{`0},`1,System.Threading.LazyThreadSafetyMode)">
      <summary>Initializes a new instance of the <see cref="T:System.Lazy`2" /> class with the specified metadata and thread synchronization mode that uses the specified function to get the referenced object.</summary>
      <param name="valueFactory">A function that returns the referenced object</param>
      <param name="metadata">The metadata associated with the referenced object.</param>
      <param name="mode">The thread synchronization mode</param>
    </member>
    <member name="M:System.Lazy`2.#ctor(`1)">
      <summary>Initializes a new instance of the <see cref="T:System.Lazy`2" /> class with the specified metadata.</summary>
      <param name="metadata">The metadata associated with the referenced object.</param>
    </member>
    <member name="M:System.Lazy`2.#ctor(`1,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Lazy`2" /> class with the specified metadata and thread safety value.</summary>
      <param name="metadata">The metadata associated with the referenced object.</param>
      <param name="isThreadSafe">Indicates whether the <see cref="T:System.Lazy`2" /> object that is created will be thread-safe.</param>
    </member>
    <member name="M:System.Lazy`2.#ctor(`1,System.Threading.LazyThreadSafetyMode)">
      <summary>Initializes a new instance of the <see cref="T:System.Lazy`2" /> class with the specified metadata and thread synchronization mode.</summary>
      <param name="metadata">The metadata associated with the referenced object.</param>
      <param name="mode">The thread synchronization mode.</param>
    </member>
    <member name="P:System.Lazy`2.Metadata">
      <summary>Gets the metadata associated with the referenced object.</summary>
      <returns>The metadata associated with the referenced object.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.AttributedModelServices">
      <summary>Contains helper methods for using the MEF attributed programming model with composition.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.AddExportedValue``1(System.ComponentModel.Composition.Hosting.CompositionBatch,System.String,``0)">
      <summary>Creates a part from the specified value and adds it to the specified batch with the specified contract name.</summary>
      <returns>The new part.</returns>
      <param name="batch">The batch to add to.</param>
      <param name="contractName">The contract name of the export.</param>
      <param name="exportedValue">The value to add.</param>
      <typeparam name="T">The type of the new part.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.AddExportedValue``1(System.ComponentModel.Composition.Hosting.CompositionBatch,``0)">
      <summary>Creates a part from the specified value and adds it to the specified batch.</summary>
      <returns>The new part.</returns>
      <param name="batch">The batch to add to.</param>
      <param name="exportedValue">The value to add.</param>
      <typeparam name="T">The type of the new part.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.AddPart(System.ComponentModel.Composition.Hosting.CompositionBatch,System.Object)">
      <summary>Creates a composable part from the specified attributed object, and adds it to the specified composition batch.</summary>
      <returns>The new part.</returns>
      <param name="batch">The batch to add to.</param>
      <param name="attributedPart">The object to add.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.ComposeExportedValue``1(System.ComponentModel.Composition.Hosting.CompositionContainer,System.String,``0)">
      <summary>Creates a part from the specified object under the specified contract name and composes it in the specified composition container.</summary>
      <param name="container">The composition container to perform composition in.</param>
      <param name="contractName">The contract name to export the part under.</param>
      <param name="exportedValue">The value to compose.</param>
      <typeparam name="T">The type of the new part.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.ComposeExportedValue``1(System.ComponentModel.Composition.Hosting.CompositionContainer,``0)">
      <summary>Creates a part from the specified value and composes it in the specified composition container.</summary>
      <param name="container">The composition container to perform composition in.</param>
      <param name="exportedValue">The value to compose.</param>
      <typeparam name="T">The type of the new part.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.ComposeParts(System.ComponentModel.Composition.Hosting.CompositionContainer,System.Object[])">
      <summary>Creates composable parts from an array of attributed objects and composes them in the specified composition container.</summary>
      <param name="container">The composition container to perform composition in.</param>
      <param name="attributedParts">An array of attributed objects to compose.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.CreatePart(System.ComponentModel.Composition.Primitives.ComposablePartDefinition,System.Object)">
      <summary>Creates a composable part from the specified attributed object, using the specified part definition.</summary>
      <returns>The created part.</returns>
      <param name="partDefinition">The definition of the new part.</param>
      <param name="attributedPart">The attributed object.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.CreatePart(System.Object)">
      <summary>Creates a composable part from the specified attributed object.</summary>
      <returns>The created part.</returns>
      <param name="attributedPart">The attributed object.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.CreatePart(System.Object,System.Reflection.ReflectionContext)">
      <summary>Creates a composable part from the specified attributed object, using the specified reflection context.</summary>
      <returns>The created part.</returns>
      <param name="attributedPart">The attributed object.</param>
      <param name="reflectionContext">The reflection context for the part.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reflectionContext" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.CreatePartDefinition(System.Type,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Creates a part definition with the specified type and origin.</summary>
      <returns>The new part definition.</returns>
      <param name="type">The type of the definition.</param>
      <param name="origin">The origin of the definition.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.CreatePartDefinition(System.Type,System.ComponentModel.Composition.Primitives.ICompositionElement,System.Boolean)">
      <summary>Creates a part definition with the specified type and origin.</summary>
      <returns>The new part definition.</returns>
      <param name="type">The type of the definition.</param>
      <param name="origin">The origin of the definition.</param>
      <param name="ensureIsDiscoverable">A value indicating whether or not the new definition should be discoverable.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.Exports``1(System.ComponentModel.Composition.Primitives.ComposablePartDefinition)">
      <summary>Returns a value that indicates whether the specified part contains an export that matches the specified contract type.</summary>
      <returns>true if <paramref name="part" /> contains an export definition of type <paramref name="T" />; otherwise, false.</returns>
      <param name="part">The part to search.</param>
      <typeparam name="T">The contract type.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.Exports(System.ComponentModel.Composition.Primitives.ComposablePartDefinition,System.Type)">
      <summary>Returns a value that indicates whether the specified part contains an export that matches the specified contract type.</summary>
      <returns>true if <paramref name="part" /> contains an export definition that matches <paramref name="contractType" />; otherwise, false.</returns>
      <param name="part">The part to search.</param>
      <param name="contractType">The contract type.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.GetContractName(System.Type)">
      <summary>Gets a canonical contract name for the specified type.</summary>
      <returns>A contract name created from the specified type.</returns>
      <param name="type">The type to use.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.GetMetadataView``1(System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>Gets a metadata view object from a dictionary of loose metadata.</summary>
      <returns>A metadata view containing the specified metadata.</returns>
      <param name="metadata">A collection of loose metadata.</param>
      <typeparam name="TMetadataView">The type of the metadata view object to get.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.GetTypeIdentity(System.Reflection.MethodInfo)">
      <summary>Gets the unique identifier for the specified method.</summary>
      <returns>The unique identifier for the method.</returns>
      <param name="method">The method to examine.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.GetTypeIdentity(System.Type)">
      <summary>Gets the unique identifier for the specified type.</summary>
      <returns>The unique identifier for the type.</returns>
      <param name="type">The type to examine.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.Imports``1(System.ComponentModel.Composition.Primitives.ComposablePartDefinition)">
      <summary>Returns a value that indicates whether the specified part contains an import that matches the specified contract type.</summary>
      <returns>true if <paramref name="part" /> contains an import definition of type <paramref name="T" />; otherwise, false.</returns>
      <param name="part">The part to search.</param>
      <typeparam name="T">The contract type.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.Imports``1(System.ComponentModel.Composition.Primitives.ComposablePartDefinition,System.ComponentModel.Composition.Primitives.ImportCardinality)">
      <summary>Returns a value that indicates whether the specified part contains an import that matches the specified contract type and import cardinality.</summary>
      <returns>true if <paramref name="part" /> contains an import definition of type <paramref name="T" /> that has the specified import cardinality; otherwise, false.</returns>
      <param name="part">The part to search.</param>
      <param name="importCardinality">The import cardinality.</param>
      <typeparam name="T">The contract type.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.Imports(System.ComponentModel.Composition.Primitives.ComposablePartDefinition,System.Type)">
      <summary>Returns a value that indicates whether the specified part contains an import that matches the specified contract type.</summary>
      <returns>true if <paramref name="part" /> contains an import definition that matches <paramref name="contractType" />; otherwise, false.</returns>
      <param name="part">The part to search.</param>
      <param name="contractType">The contract type.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.Imports(System.ComponentModel.Composition.Primitives.ComposablePartDefinition,System.Type,System.ComponentModel.Composition.Primitives.ImportCardinality)">
      <summary>Returns a value that indicates whether the specified part contains an import that matches the specified contract type and import cardinality.</summary>
      <returns>true if <paramref name="part" /> contains an import definition that matches <paramref name="contractType" /> and <paramref name="importCardinality" />; otherwise, false.</returns>
      <param name="part">The part to search.</param>
      <param name="contractType">The contract type.</param>
      <param name="importCardinality">The import cardinality.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.SatisfyImportsOnce(System.ComponentModel.Composition.ICompositionService,System.Object)">
      <summary>Composes the specified part by using the specified composition service, with recomposition disabled.</summary>
      <returns>The composed part.</returns>
      <param name="compositionService">The composition service to use.</param>
      <param name="attributedPart">The part to compose.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.AttributedModelServices.SatisfyImportsOnce(System.ComponentModel.Composition.ICompositionService,System.Object,System.Reflection.ReflectionContext)">
      <summary>Composes the specified part by using the specified composition service, with recomposition disabled and using the specified reflection context.</summary>
      <returns>The composed part.</returns>
      <param name="compositionService">The composition service to use.</param>
      <param name="attributedPart">The part to compose.</param>
      <param name="reflectionContext">The reflection context for the part.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reflectionContext" /> is null.</exception>
    </member>
    <member name="T:System.ComponentModel.Composition.CatalogReflectionContextAttribute">
      <summary>When applied to a <see cref="T:System.Reflection.Assembly" /> object, enables an <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> object to discover custom <see cref="T:System.Reflection.ReflectionContext" /> objects.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.CatalogReflectionContextAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> class with the specified <see cref="T:System.Reflection.ReflectionContext" /> type.</summary>
      <param name="reflectionContextType">The type of the reflection context.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.CatalogReflectionContextAttribute.CreateReflectionContext">
      <summary>Creates an instance of the custom <see cref="T:System.Reflection.ReflectionContext" /> object.</summary>
      <returns>An instance of the custom reflection context.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.ChangeRejectedException">
      <summary>An exception that indicates whether a part has been rejected during composition.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ChangeRejectedException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ChangeRejectedException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ChangeRejectedException.#ctor(System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.CompositionError})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ChangeRejectedException" /> class with a list of composition errors. </summary>
      <param name="errors">A collection of errors that occurred during composition.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ChangeRejectedException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ChangeRejectedException" /> class with a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ChangeRejectedException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ChangeRejectedException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture. </param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception. </param>
    </member>
    <member name="P:System.ComponentModel.Composition.ChangeRejectedException.Message">
      <summary>Gets or sets the message associated with the component rejection.</summary>
      <returns>The message associated with the component rejection.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.CompositionContractMismatchException">
      <summary>The exception that is thrown when the underlying exported value or metadata of a <see cref="T:System.Lazy`1" /> or <see cref="T:System.Lazy`2" /> object cannot be cast to T or TMetadataView, respectively.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.CompositionContractMismatchException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.CompositionContractMismatchException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.CompositionContractMismatchException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.CompositionContractMismatchException" /> class with serialized data.</summary>
      <param name="info">The object that holds the serialized object data. </param>
      <param name="context">The contextual information about the source or destination. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.CompositionContractMismatchException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.CompositionContractMismatchException" /> class with a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.CompositionContractMismatchException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.CompositionContractMismatchException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture. </param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception. </param>
    </member>
    <member name="T:System.ComponentModel.Composition.CompositionError">
      <summary>Represents an error that occurred during composition.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.CompositionError.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.CompositionError" /> class with the specified error message.</summary>
      <param name="message">A message that describes the <see cref="T:System.ComponentModel.Composition.CompositionError" /> or null to set the <see cref="P:System.ComponentModel.Composition.CompositionError.Description" /> property to an empty string ("").</param>
    </member>
    <member name="M:System.ComponentModel.Composition.CompositionError.#ctor(System.String,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.CompositionError" /> class with the specified error message and the composition element that is the cause of the composition error.</summary>
      <param name="message">A message that describes the <see cref="T:System.ComponentModel.Composition.CompositionError" /> or null to set the <see cref="P:System.ComponentModel.Composition.CompositionError.Description" /> property to an empty string ("").</param>
      <param name="element">The composition element that is the cause of the <see cref="T:System.ComponentModel.Composition.CompositionError" /> or null to set the <see cref="P:System.ComponentModel.Composition.CompositionError.Element" /> property to null.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.CompositionError.#ctor(System.String,System.ComponentModel.Composition.Primitives.ICompositionElement,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.CompositionError" /> class with the specified error message, and the composition element and exception that are the cause of the composition error.</summary>
      <param name="message">A message that describes the <see cref="T:System.ComponentModel.Composition.CompositionError" /> or null to set the <see cref="P:System.ComponentModel.Composition.CompositionError.Description" /> property to an empty string ("").</param>
      <param name="element">The composition element that is the cause of the <see cref="T:System.ComponentModel.Composition.CompositionError" /> or null to set the <see cref="P:System.ComponentModel.Composition.CompositionError.Element" /> property to null.</param>
      <param name="exception">The <see cref="P:System.ComponentModel.Composition.CompositionError.Exception" /> that is the underlying cause of the <see cref="T:System.ComponentModel.Composition.CompositionError" /> or null to set the <see cref="P:System.ComponentModel.Composition.CompositionError.Exception" /> property to null.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.CompositionError.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.CompositionError" /> class with the specified error message and the exception that is the cause of the composition error.</summary>
      <param name="message">A message that describes the <see cref="T:System.ComponentModel.Composition.CompositionError" /> or null to set the <see cref="P:System.ComponentModel.Composition.CompositionError.Description" /> property to an empty string ("").</param>
      <param name="exception">The <see cref="P:System.ComponentModel.Composition.CompositionError.Exception" /> that is the underlying cause of the <see cref="T:System.ComponentModel.Composition.CompositionError" /> or null to set the <see cref="P:System.ComponentModel.Composition.CompositionError.Exception" /> property to null.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.CompositionError.Description">
      <summary>Gets a description of the composition error.</summary>
      <returns>A message that describes the <see cref="T:System.ComponentModel.Composition.CompositionError" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.CompositionError.Element">
      <summary>Gets the composition element that is the cause of the error.</summary>
      <returns>The composition element that is the cause of the <see cref="T:System.ComponentModel.Composition.CompositionError" />. The default is null.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.CompositionError.Exception">
      <summary>Gets the exception that is the underlying cause of the composition error.</summary>
      <returns>The exception that is the underlying cause of the <see cref="T:System.ComponentModel.Composition.CompositionError" />. The default is null.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.CompositionError.ToString">
      <summary>Returns a string representation of the composition error.</summary>
      <returns>A string that contains the <see cref="P:System.ComponentModel.Composition.CompositionError.Description" /> property.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.CompositionException">
      <summary>Represents the exception that is thrown when one or more errors occur during composition in a <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.CompositionException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.CompositionException" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.CompositionException.#ctor(System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.CompositionError})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.CompositionException" /> class with the specified collection of composition errors.</summary>
      <param name="errors">A collection of <see cref="T:System.ComponentModel.Composition.CompositionError" /> objects that represent problems during composition.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.CompositionException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.CompositionException" /> class with the specified error message.</summary>
      <param name="message">A message that describes the <see cref="T:System.ComponentModel.Composition.CompositionException" /> or null to set the <see cref="P:System.Exception.Message" /> property to its default value.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.CompositionException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.CompositionException" /> class with the specified error message and the exception that is the cause of this exception.</summary>
      <param name="message">A message that describes the <see cref="T:System.ComponentModel.Composition.CompositionException" /> or null to set the <see cref="P:System.Exception.Message" /> property to its default value.</param>
      <param name="innerException">The exception that is the underlying cause of the <see cref="T:System.ComponentModel.Composition.CompositionException" /> or null to set the <see cref="P:System.Exception.InnerException" /> property to null.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.CompositionException.Errors">
      <summary>Gets or sets a collection of <see cref="T:System.ComponentModel.Composition.CompositionError" /> objects that describe the errors associated with the <see cref="T:System.ComponentModel.Composition.CompositionException" />.</summary>
      <returns>A collection of <see cref="T:System.ComponentModel.Composition.CompositionError" /> objects that describe the errors associated with the <see cref="T:System.ComponentModel.Composition.CompositionException" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.CompositionException.Message">
      <summary>Gets a message that describes the exception.</summary>
      <returns> A message that describes the <see cref="T:System.ComponentModel.Composition.CompositionException" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.CompositionException.RootCauses">
      <summary>Gets a collection that contains the initial sources of this exception.</summary>
      <returns>A collection that contains the initial sources of this exception.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.CreationPolicy">
      <summary>Specifies when and how a part will be instantiated.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.CreationPolicy.Any">
      <summary>Specifies that the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> will use the most appropriate <see cref="T:System.ComponentModel.Composition.CreationPolicy" /> for the part given the current context. This is the default <see cref="T:System.ComponentModel.Composition.CreationPolicy" />. Be default, <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> will use <see cref="F:System.ComponentModel.Composition.CreationPolicy.Shared" />, unless the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> or importer requests <see cref="F:System.ComponentModel.Composition.CreationPolicy.NonShared" />.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.CreationPolicy.Shared">
      <summary>Specifies that a single shared instance of the associated <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> will be created by the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> and shared by all requestors.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.CreationPolicy.NonShared">
      <summary>Specifies that a new non-shared instance of the associated <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> will be created by the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> for every requestor.</summary>
    </member>
    <member name="T:System.ComponentModel.Composition.ExportAttribute">
      <summary>Specifies that a type, property, field, or method provides a particular export.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ExportAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ExportAttribute" /> class, exporting the type or member marked with this attribute under the default contract name.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ExportAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ExportAttribute" /> class, exporting the type or member marked with this attribute under the specified contract name.</summary>
      <param name="contractName">The contract name that is used to export the type or member marked with this attribute, or null or an empty string ("") to use the default contract name.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ExportAttribute.#ctor(System.String,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ExportAttribute" /> class, exporting the specified type under the specified contract name.</summary>
      <param name="contractName">The contract name that is used to export the type or member marked with this attribute, or null or an empty string ("") to use the default contract name.</param>
      <param name="contractType">The type to export.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ExportAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ExportAttribute" /> class, exporting the type or member marked with this attribute under a contract name derived from the specified type.</summary>
      <param name="contractType">A type from which to derive the contract name that is used to export the type or member marked with this attribute, or null to use the default contract name.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.ExportAttribute.ContractName">
      <summary>Gets the contract name that is used to export the type or member marked with this attribute.</summary>
      <returns>The contract name that is used to export the type or member marked with this attribute. The default value is an empty string ("").</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.ExportAttribute.ContractType">
      <summary>Gets the contract type that is exported by the member that this attribute is attached to.</summary>
      <returns>The type of export that is be provided. The default value is null, which means that the type will be obtained by looking at the type on the member that this export is attached to. </returns>
    </member>
    <member name="T:System.ComponentModel.Composition.ExportFactory`1">
      <summary>A factory that creates new instances of a part that provides the specified export.</summary>
      <typeparam name="T">The type of the export.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.ExportFactory`1.#ctor(System.Func{System.Tuple{`0,System.Action}})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ExportFactory`1" /> class.</summary>
      <param name="exportLifetimeContextCreator">A function that returns the exported value and an <see cref="T:System.Action" /> that releases it.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ExportFactory`1.CreateExport">
      <summary>Creates an instance of the factory's export type.</summary>
      <returns>A valid instance of the factory's exported type.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.ExportFactory`2">
      <summary>A factory that creates new instances of a part that provides the specified export, with attached metadata.</summary>
      <typeparam name="T">The type of the created part.</typeparam>
      <typeparam name="TMetadata">The type of the created part's metadata.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.ExportFactory`2.#ctor(System.Func{System.Tuple{`0,System.Action}},`1)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ExportFactory`2" /> class. </summary>
      <param name="exportLifetimeContextCreator">A function that returns the exported value and an <see cref="T:System.Action" /> that releases it.</param>
      <param name="metadata">The metadata to attach to the created parts.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.ExportFactory`2.Metadata">
      <summary>Gets the metadata to be attached to the created parts.</summary>
      <returns>A metadata object that will be attached to the created parts.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.ExportLifetimeContext`1">
      <summary>Holds an exported value created by an <see cref="T:System.ComponentModel.Composition.ExportFactory`1" /> object and a reference to a method to release that object.</summary>
      <typeparam name="T">The type of the exported value.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.ExportLifetimeContext`1.#ctor(`0,System.Action)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ExportLifetimeContext`1" /> class. </summary>
      <param name="value">The exported value.</param>
      <param name="disposeAction">A reference to a method to release the object.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ExportLifetimeContext`1.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.ComponentModel.Composition.ExportLifetimeContext`1" /> class, including its associated export.</summary>
    </member>
    <member name="P:System.ComponentModel.Composition.ExportLifetimeContext`1.Value">
      <summary>Gets the exported value of a <see cref="T:System.ComponentModel.Composition.ExportFactory`1" /> object.</summary>
      <returns>The exported value.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.ExportMetadataAttribute">
      <summary>Specifies metadata for a type, property, field, or method marked with the <see cref="T:System.ComponentModel.Composition.ExportAttribute" />.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ExportMetadataAttribute.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ExportMetadataAttribute" /> with the specified name and metadata value.</summary>
      <param name="name">A string that contains the name of the metadata value, or null to set the <see cref="P:System.ComponentModel.Composition.ExportMetadataAttribute.Name" /> property to an empty string ("").</param>
      <param name="value">An object that contains the metadata value. This can be null.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.ExportMetadataAttribute.IsMultiple">
      <summary>Gets or sets a value that indicates whether this item is marked with this attribute more than once.</summary>
      <returns>true if the item is marked more than once; otherwise, false.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.ExportMetadataAttribute.Name">
      <summary>Gets the name of the metadata value.</summary>
      <returns> A string that contains the name of the metadata value.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.ExportMetadataAttribute.Value">
      <summary>Gets the metadata value.</summary>
      <returns> An object that contains the metadata value.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.ICompositionService">
      <summary>Provides methods to satisfy imports on an existing part instance.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ICompositionService.SatisfyImportsOnce(System.ComponentModel.Composition.Primitives.ComposablePart)">
      <summary>Composes the specified part, with recomposition and validation disabled.</summary>
      <param name="part">The part to compose.</param>
    </member>
    <member name="T:System.ComponentModel.Composition.ImportAttribute">
      <summary>Specifies that a property, field, or parameter value should be provided by the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />.object</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ImportAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ImportAttribute" /> class, importing the export with the default contract name.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ImportAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ImportAttribute" /> class, importing the export with the specified contract name.</summary>
      <param name="contractName">The contract name of the export to import, or null or an empty string ("") to use the default contract name.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ImportAttribute.#ctor(System.String,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ImportAttribute" /> class, importing the export with the specified contract name and type.</summary>
      <param name="contractName">The contract name of the export to import, or null or an empty string ("") to use the default contract name.</param>
      <param name="contractType">The type of the export to import.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ImportAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ImportAttribute" /> class, importing the export with the contract name derived from the specified type.</summary>
      <param name="contractType">The type to derive the contract name of the export from, or null to use the default contract name.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.ImportAttribute.AllowDefault">
      <summary>Gets or sets a value that indicates whether the property, field, or parameter will be set to its type's default value when an export with the contract name is not present in the container.</summary>
      <returns>true if the property, field, or parameter will be set to its type's default value when there is no export with the <see cref="P:System.ComponentModel.Composition.ImportAttribute.ContractName" /> in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.ImportAttribute.AllowRecomposition">
      <summary>Gets or sets a value that indicates whether the property or field will be recomposed when exports with a matching contract have changed in the container.</summary>
      <returns>true if the property or field allows recomposition when exports with a matching <see cref="P:System.ComponentModel.Composition.ImportAttribute.ContractName" /> are added or removed from the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.ImportAttribute.ContractName">
      <summary>Gets the contract name of the export to import.</summary>
      <returns>The contract name of the export to import. The default is an empty string ("").</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.ImportAttribute.ContractType">
      <summary>Gets the type of the export to import.</summary>
      <returns>The type of the export to import.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.ImportAttribute.RequiredCreationPolicy">
      <summary>Gets or sets a value that indicates that the importer requires a specific <see cref="T:System.ComponentModel.Composition.CreationPolicy" /> for the exports used to satisfy this import. </summary>
      <returns>One of the following values:<see cref="F:System.ComponentModel.Composition.CreationPolicy.Any" />, if the importer does not require a specific <see cref="T:System.ComponentModel.Composition.CreationPolicy" />. This is the default.<see cref="F:System.ComponentModel.Composition.CreationPolicy.Shared" /> to require that all used exports be shared by all parts in the container.<see cref="F:System.ComponentModel.Composition.CreationPolicy.NonShared" /> to require that all used exports be non-shared in a container. In this case, each part receives their own instance.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.ImportAttribute.Source">
      <summary>Gets or sets a value that specifies the scopes from which this import may be satisfied.</summary>
      <returns>A value that specifies the scopes from which this import may be satisfied.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.ImportCardinalityMismatchException">
      <summary>The exception that is thrown when the cardinality of an import is not compatible with the cardinality of the matching exports.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ImportCardinalityMismatchException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ImportCardinalityMismatchException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException" /> class with serialized data.</summary>
      <param name="info">An object that holds the serialized object data about the <see cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException" />. </param>
      <param name="context">An object that contains contextual information about the source or destination. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is null.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">
        <paramref name="info" /> is missing a required value.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="info" /> contains a value that cannot be cast to the correct type.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.ImportCardinalityMismatchException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException" /> class with a specified message that describes the error.</summary>
      <param name="message">A message that describes the <see cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException" />, or null to set the <see cref="P:System.Exception.Message" /> property to its default value. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.ImportCardinalityMismatchException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture. </param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception. </param>
    </member>
    <member name="T:System.ComponentModel.Composition.ImportingConstructorAttribute">
      <summary>Specifies which constructor should be used when creating a part.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ImportingConstructorAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ImportingConstructorAttribute" /> class.</summary>
    </member>
    <member name="T:System.ComponentModel.Composition.ImportManyAttribute">
      <summary>Specifies that a property, field, or parameter should be populated with all matching exports by the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ImportManyAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ImportManyAttribute" /> class, importing the set of exports with the default contract name.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ImportManyAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ImportManyAttribute" /> class, importing the set of exports with the specified contract name.</summary>
      <param name="contractName">The contract name of the exports to import, or null or an empty string ("") to use the default contract name.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ImportManyAttribute.#ctor(System.String,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ImportManyAttribute" /> class, importing the set of exports with the specified contract name and contract type.</summary>
      <param name="contractName">The contract name of the exports to import, or null or an empty string ("") to use the default contract name.</param>
      <param name="contractType">The type of the export to import.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ImportManyAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ImportManyAttribute" /> class, importing the set of exports with the contract name derived from the specified type.</summary>
      <param name="contractType">The type to derive the contract name of the exports to import, or null to use the default contract name.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.ImportManyAttribute.AllowRecomposition">
      <summary>Gets or sets a value indicating whether the decorated property or field will be recomposed when exports that provide the matching contract change.</summary>
      <returns>true if the property or field allows for recomposition when exports that provide the same <see cref="P:System.ComponentModel.Composition.ImportManyAttribute.ContractName" /> are added or removed from the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />; otherwise, false.The default value is false.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.ImportManyAttribute.ContractName">
      <summary>Gets the contract name of the exports to import.</summary>
      <returns>The contract name of the exports to import. The default value is an empty string ("").</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.ImportManyAttribute.ContractType">
      <summary>Gets the contract type of the export to import.</summary>
      <returns>The type of the export that this import is expecting. The default value is null, which means that the type will be obtained by looking at the type on the member that this import is attached to. If the type is <see cref="T:System.Object" />, the import will match any exported type.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.ImportManyAttribute.RequiredCreationPolicy">
      <summary>Gets or sets a value that indicates that the importer requires a specific <see cref="T:System.ComponentModel.Composition.CreationPolicy" /> for the exports used to satisfy this import. </summary>
      <returns>One of the following values:<see cref="F:System.ComponentModel.Composition.CreationPolicy.Any" />, if the importer does not require a specific <see cref="T:System.ComponentModel.Composition.CreationPolicy" />. This is the default.<see cref="F:System.ComponentModel.Composition.CreationPolicy.Shared" /> to require that all used exports be shared by all parts in the container.<see cref="F:System.ComponentModel.Composition.CreationPolicy.NonShared" /> to require that all used exports be non-shared in a container. In this case, each part receives their own instance.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.ImportManyAttribute.Source">
      <summary>Gets or sets a value that specifies the scopes from which this import may be satisfied.</summary>
      <returns>A value that specifies the scopes from which this import may be satisfied.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.ImportSource">
      <summary>Specifies values that indicate how the MEF composition engine searches for imports.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.ImportSource.Any">
      <summary>Imports may be satisfied from the current scope or any ancestor scope.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.ImportSource.Local">
      <summary>Imports may be satisfied only from the current scope.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.ImportSource.NonLocal">
      <summary>Imports may be satisfied only from an ancestor scope.</summary>
    </member>
    <member name="T:System.ComponentModel.Composition.InheritedExportAttribute">
      <summary>Specifies that a type provides a particular export, and that subclasses of that type will also provide that export.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.InheritedExportAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.InheritedExportAttribute" /> class. </summary>
    </member>
    <member name="M:System.ComponentModel.Composition.InheritedExportAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.InheritedExportAttribute" /> class with the specified contract name.</summary>
      <param name="contractName">The name of the contract.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.InheritedExportAttribute.#ctor(System.String,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.InheritedExportAttribute" /> class with the specified contract name and type.</summary>
      <param name="contractName">The name of the contract.</param>
      <param name="contractType">The type of the contract.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.InheritedExportAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.InheritedExportAttribute" /> class with the specified contract type.</summary>
      <param name="contractType">The type of the contract.</param>
    </member>
    <member name="T:System.ComponentModel.Composition.IPartImportsSatisfiedNotification">
      <summary>Notifies a part when its imports have been satisfied.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.IPartImportsSatisfiedNotification.OnImportsSatisfied">
      <summary>Called when a part's imports have been satisfied and it is safe to use.</summary>
    </member>
    <member name="T:System.ComponentModel.Composition.MetadataAttributeAttribute">
      <summary>Specifies that a custom attribute’s properties provide metadata for exports applied to the same type, property, field, or method.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.MetadataAttributeAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.MetadataAttributeAttribute" /> class.</summary>
    </member>
    <member name="T:System.ComponentModel.Composition.MetadataViewImplementationAttribute">
      <summary>Specifies the type used to implement a metadata view.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.MetadataViewImplementationAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.MetadataViewImplementationAttribute" /> class.</summary>
      <param name="implementationType">The type of the metadata view.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.MetadataViewImplementationAttribute.ImplementationType">
      <summary>Gets the type of the metadata view.</summary>
      <returns>The type of the metadata view.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.PartCreationPolicyAttribute">
      <summary>Specifies the <see cref="P:System.ComponentModel.Composition.PartCreationPolicyAttribute.CreationPolicy" /> for a part.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.PartCreationPolicyAttribute.#ctor(System.ComponentModel.Composition.CreationPolicy)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.PartCreationPolicyAttribute" /> class with the specified creation policy.</summary>
      <param name="creationPolicy">The creation policy to use.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.PartCreationPolicyAttribute.CreationPolicy">
      <summary>Gets or sets a value that indicates the creation policy of the attributed part.</summary>
      <returns>One of the <see cref="P:System.ComponentModel.Composition.PartCreationPolicyAttribute.CreationPolicy" /> values that indicates the creation policy of the attributed part. The default is <see cref="F:System.ComponentModel.Composition.CreationPolicy.Any" />.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.PartMetadataAttribute">
      <summary>Specifies metadata for a part.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.PartMetadataAttribute.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.PartMetadataAttribute" /> class with the specified name and metadata value.</summary>
      <param name="name">A string that contains the name of the metadata value or null to use an empty string ("").</param>
      <param name="value">An object that contains the metadata value. This can be null.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.PartMetadataAttribute.Name">
      <summary>Gets the name of the metadata value.</summary>
      <returns> A string that contains the name of the metadata value.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.PartMetadataAttribute.Value">
      <summary>Gets the metadata value.</summary>
      <returns> An object that contains the metadata value.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.PartNotDiscoverableAttribute">
      <summary>Specifies that this type’s exports won’t be included in a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" />.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.PartNotDiscoverableAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.PartNotDiscoverableAttribute" /> class.</summary>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.AggregateCatalog">
      <summary>A catalog that combines the elements of <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> objects. </summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AggregateCatalog.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AggregateCatalog.#ctor(System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Primitives.ComposablePartCatalog})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" /> class with the specified catalogs.</summary>
      <param name="catalogs">A collection of <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" /> or null to create an empty <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="catalogs" /> contains an element that is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AggregateCatalog.#ctor(System.ComponentModel.Composition.Primitives.ComposablePartCatalog[])">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" /> class with the specified catalogs.</summary>
      <param name="catalogs">A array of <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="catalogs" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="catalogs" /> contains an element that is null.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.AggregateCatalog.Catalogs">
      <summary>Gets the underlying catalogs of the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" /> object.</summary>
      <returns>A collection of <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> objects that underlie the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" /> object.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" /> object has been disposed of.</exception>
    </member>
    <member name="E:System.ComponentModel.Composition.Hosting.AggregateCatalog.Changed">
      <summary>Occurs when the contents of the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" /> object have changed.</summary>
    </member>
    <member name="E:System.ComponentModel.Composition.Hosting.AggregateCatalog.Changing">
      <summary>Occurs when the contents of the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" /> object are changing.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AggregateCatalog.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AggregateCatalog.GetEnumerator">
      <summary>Returns an enumerator that iterates through the catalog.</summary>
      <returns>An enumerator that can be used to iterate through the catalog.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AggregateCatalog.GetExports(System.ComponentModel.Composition.Primitives.ImportDefinition)">
      <summary>Gets the export definitions that match the constraint expressed by the specified definition.</summary>
      <returns>A collection of <see cref="T:System.Tuple`2" /> containing the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> objects and their associated <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects for objects that match the constraint specified by <paramref name="definition" />.</returns>
      <param name="definition">The conditions of the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> objects to be returned.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" /> object has been disposed of.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="definition" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AggregateCatalog.OnChanged(System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Composition.Hosting.AggregateCatalog.Changed" /> event.</summary>
      <param name="e">A <see cref="T:System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs" /> object that contains the event data. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AggregateCatalog.OnChanging(System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Composition.Hosting.AggregateCatalog.Changing" /> event.</summary>
      <param name="e">A <see cref="T:System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs" /> object that contains the event data. </param>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.AggregateExportProvider">
      <summary>Retrieves exports provided by a collection of <see cref="T:System.ComponentModel.Composition.Hosting.ExportProvider" /> objects.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AggregateExportProvider.#ctor(System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Hosting.ExportProvider})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateExportProvider" /> class.</summary>
      <param name="providers">The prioritized list of export providers. The providers are consulted in the order in which they are supplied.</param>
      <exception cref="T:System.ArgumentException">One or more elements of <paramref name="providers" /> are null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AggregateExportProvider.#ctor(System.ComponentModel.Composition.Hosting.ExportProvider[])">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateExportProvider" /> class.</summary>
      <param name="providers">The prioritized list of export providers.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AggregateExportProvider.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateExportProvider" /> class. </summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AggregateExportProvider.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateExportProvider" /> class and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AggregateExportProvider.GetExportsCore(System.ComponentModel.Composition.Primitives.ImportDefinition,System.ComponentModel.Composition.Hosting.AtomicComposition)">
      <summary>Gets all the exports that match the conditions of the specified import.</summary>
      <returns>A collection that contains all the exports that match the specified condition.</returns>
      <param name="definition">The conditions of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects to be returned.</param>
      <param name="atomicComposition">The transactional container for the composition.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.AggregateExportProvider.Providers">
      <summary>Gets a collection that contains the providers that the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateExportProvider" /> object aggregates.</summary>
      <returns>A collection of the <see cref="T:System.ComponentModel.Composition.Hosting.ExportProvider" /> objects that the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateExportProvider" /> aggregates.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.AggregateExportProvider" /> object has been disposed of.</exception>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.ApplicationCatalog">
      <summary>Discovers attributed parts in the dynamic link library (DLL) and EXE files in an application's directory and path.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ApplicationCatalog.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ApplicationCatalog" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ApplicationCatalog.#ctor(System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ApplicationCatalog" /> class by using the specified source for parts.</summary>
      <param name="definitionOrigin">The element used by diagnostics to identify the source for parts.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ApplicationCatalog.#ctor(System.Reflection.ReflectionContext)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ApplicationCatalog" /> class by using the specified reflection context.</summary>
      <param name="reflectionContext">The reflection context.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ApplicationCatalog.#ctor(System.Reflection.ReflectionContext,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ApplicationCatalog" /> class by using the specified reflection context and source for parts.</summary>
      <param name="reflectionContext">The reflection context.</param>
      <param name="definitionOrigin">The element used by diagnostics to identify the source for parts.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ApplicationCatalog.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ApplicationCatalog.GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ApplicationCatalog.GetExports(System.ComponentModel.Composition.Primitives.ImportDefinition)">
      <summary>Gets the export definitions that match the constraint expressed by the specified import definition.</summary>
      <returns>A collection of objects that contain the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> objects and their associated <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects that match the specified constraint.</returns>
      <param name="definition">The conditions of the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> objects to be returned.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> object has been disposed of.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="definition" /> is null.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.ApplicationCatalog.System#ComponentModel#Composition#Primitives#ICompositionElement#DisplayName">
      <summary>Gets the display name of the application catalog.</summary>
      <returns>A string that contains a human-readable display name of the <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> object.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.ApplicationCatalog.System#ComponentModel#Composition#Primitives#ICompositionElement#Origin">
      <summary>Gets the composition element from which the application catalog originated.</summary>
      <returns>Always null.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ApplicationCatalog.ToString">
      <summary>Retrieves a string representation of the application catalog.</summary>
      <returns>A string representation of the catalog.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog">
      <summary>Discovers attributed parts in a managed code assembly.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AssemblyCatalog.#ctor(System.Reflection.Assembly)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> class with the specified assembly.</summary>
      <param name="assembly">The assembly that contains the attributed <see cref="T:System.Type" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> object.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="assembly" /> is null.-or-<paramref name="assembly" /> was loaded in the reflection-only context.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AssemblyCatalog.#ctor(System.Reflection.Assembly,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> class with the specified assembly.</summary>
      <param name="assembly">The assembly that contains the attributed <see cref="T:System.Type" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> object.</param>
      <param name="definitionOrigin">The element used by diagnostics to identify the sources of parts.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="assembly" /> or <paramref name="definitionOrigin" /> is null.-or-<paramref name="assembly" /> was loaded in the reflection-only context.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AssemblyCatalog.#ctor(System.Reflection.Assembly,System.Reflection.ReflectionContext)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> class with the specified assembly and reflection context.</summary>
      <param name="assembly">The assembly that contains the attributed <see cref="T:System.Type" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> object.</param>
      <param name="reflectionContext">The context used by the catalog to interpret types.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="assembly" /> or <paramref name="reflectionContext" /> is null.-or-<paramref name="assembly" /> was loaded in the reflection-only context.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AssemblyCatalog.#ctor(System.Reflection.Assembly,System.Reflection.ReflectionContext,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> class with the specified assembly and reflection context.</summary>
      <param name="assembly">The assembly that contains the attributed <see cref="T:System.Type" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> object.</param>
      <param name="reflectionContext">The context used by the catalog to interpret types.</param>
      <param name="definitionOrigin">The element used by diagnostics to identify the sources of parts.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="assembly" />, <paramref name="definitionOrigin" />, or <paramref name="reflectionContext" /> is null.-or-<paramref name="assembly" /> was loaded in the reflection-only context.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AssemblyCatalog.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> class with the specified code base.</summary>
      <param name="codeBase">A string that specifies the code base of the assembly (that is, the path to the assembly file) that contains the attributed <see cref="T:System.Type" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> object.</param>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="codeBase" /> is not a valid assembly. -or-Version 2.0 or earlier of the common language runtime is currently loaded and <paramref name="codeBase" /> was compiled with a later version.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have path discovery permission. </exception>
      <exception cref="T:System.IO.FileLoadException">
        <paramref name="codeBase" /> could not be loaded.-or-<paramref name="codeBase" /> specified a directory.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="codeBase" /> is null.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="codeBase" /> is not found.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="codeBase" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. </exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AssemblyCatalog.#ctor(System.String,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> class with the specified code base.</summary>
      <param name="codeBase">A string that specifies the code base of the assembly (that is, the path to the assembly file) that contains the attributed <see cref="T:System.Type" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> object.</param>
      <param name="definitionOrigin">The element used by diagnostics to identify the sources of parts.</param>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="codeBase" /> is not a valid assembly. -or-Version 2.0 or later of the common language runtime is currently loaded and <paramref name="codeBase" /> was compiled with a later version.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have path discovery permission. </exception>
      <exception cref="T:System.IO.FileLoadException">
        <paramref name="codeBase" /> could not be loaded.-or-<paramref name="codeBase" /> specified a directory.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="codebase" /> or <paramref name="definitionOrigin" /> is null.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="codeBase" /> is not found.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="codeBase" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. </exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AssemblyCatalog.#ctor(System.String,System.Reflection.ReflectionContext)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> class with the specified code base and reflection context.</summary>
      <param name="codeBase">A string that specifies the code base of the assembly (that is, the path to the assembly file) that contains the attributed <see cref="T:System.Type" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> object.</param>
      <param name="reflectionContext">The context used by the catalog to interpret types.</param>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="codeBase" /> is not a valid assembly. -or-Version 2.0 or later of the common language runtime is currently loaded and <paramref name="codeBase" /> was compiled with a later version.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have path discovery permission. </exception>
      <exception cref="T:System.IO.FileLoadException">
        <paramref name="codeBase" /> could not be loaded.-or-<paramref name="codeBase" /> specified a directory.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="codebase" /> or <paramref name="reflectionContext" /> is null.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="codeBase" /> is not found.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="codeBase" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. </exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AssemblyCatalog.#ctor(System.String,System.Reflection.ReflectionContext,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> class with the specified code base and reflection context.</summary>
      <param name="codeBase">A string that specifies the code base of the assembly (that is, the path to the assembly file) that contains the attributed <see cref="T:System.Type" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> object.</param>
      <param name="reflectionContext">The context used by the catalog to interpret types.</param>
      <param name="definitionOrigin">The element used by diagnostics to identify the sources of parts.</param>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="codeBase" /> is not a valid assembly. -or-Version 2.0 or later of the common language runtime is currently loaded and <paramref name="codeBase" /> was compiled with a later version.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have path discovery permission. </exception>
      <exception cref="T:System.IO.FileLoadException">
        <paramref name="codeBase" /> could not be loaded.-or-<paramref name="codeBase" /> specified a directory.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="codebase" />, <paramref name="definitionOrigin" /> or <paramref name="reflectionContext" /> is null.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="codeBase" /> is not found.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="codeBase" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. </exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.AssemblyCatalog.Assembly">
      <summary>Gets the assembly whose attributed types are contained in the assembly catalog.</summary>
      <returns>The assembly whose attributed <see cref="T:System.Type" /> objects are contained in the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AssemblyCatalog.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AssemblyCatalog.GetEnumerator">
      <summary>Returns an enumerator that iterates through the catalog.</summary>
      <returns>An enumerator that can be used to iterate through the catalog.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AssemblyCatalog.GetExports(System.ComponentModel.Composition.Primitives.ImportDefinition)">
      <summary>Gets a collection of exports that match the conditions specified by the import definition.</summary>
      <returns>A collection of exports that match the conditions specified by <paramref name="definition" />.</returns>
      <param name="definition">Conditions that specify which exports to match.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.AssemblyCatalog.System#ComponentModel#Composition#Primitives#ICompositionElement#DisplayName">
      <summary>Gets the display name of the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> object.</summary>
      <returns>A string that represents the type and assembly of this <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> object.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.AssemblyCatalog.System#ComponentModel#Composition#Primitives#ICompositionElement#Origin">
      <summary>Gets the composition element that this element originated from.</summary>
      <returns>Always null.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AssemblyCatalog.ToString">
      <summary>Gets a string representation of the assembly catalog.</summary>
      <returns>A representation of the assembly catalog.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.AtomicComposition">
      <summary>Represents a single composition operation for transactional composition.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AtomicComposition.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AtomicComposition" /> class. </summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AtomicComposition.#ctor(System.ComponentModel.Composition.Hosting.AtomicComposition)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AtomicComposition" /> class with the specified parent <see cref="T:System.ComponentModel.Composition.Hosting.AtomicComposition" />.</summary>
      <param name="outerAtomicComposition">The parent of this composition operation.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AtomicComposition.AddCompleteAction(System.Action)">
      <summary>Adds an action to be executed when the overall composition operation completes successfully.</summary>
      <param name="completeAction">The action to be executed.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AtomicComposition.AddRevertAction(System.Action)">
      <summary>Adds an action to be executed if the overall composition operation fails.</summary>
      <param name="revertAction">The action to be executed.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AtomicComposition.Complete">
      <summary>Marks this composition operation as complete.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AtomicComposition.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.ComponentModel.Composition.Hosting.AtomicComposition" /> class, and mark this composition operation as failed.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AtomicComposition.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Composition.Hosting.AtomicComposition" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AtomicComposition.SetValue(System.Object,System.Object)">
      <summary>Saves a key-value pair in the transaction to track tentative state.</summary>
      <param name="key">The key to save.</param>
      <param name="value">The value to save.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AtomicComposition.TryGetValue``1(System.Object,System.Boolean,``0@)">
      <summary>Gets a value saved by the <see cref="M:System.ComponentModel.Composition.Hosting.AtomicComposition.SetValue(System.Object,System.Object)" /> method, with the option of not searching parent transactions.</summary>
      <returns>true if the value was successfully retrieved; otherwise, false.</returns>
      <param name="key">The key to retrieve from.</param>
      <param name="localAtomicCompositionOnly">true to exclude parent transactions; otherwise, false.</param>
      <param name="value">The retrieved value.</param>
      <typeparam name="T">The type of the value to be retrieved.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.AtomicComposition.TryGetValue``1(System.Object,``0@)">
      <summary>Gets a value saved by the <see cref="M:System.ComponentModel.Composition.Hosting.AtomicComposition.SetValue(System.Object,System.Object)" /> method.</summary>
      <returns>true if the value was successfully retrieved; otherwise, false.</returns>
      <param name="key">The key to retrieve from.</param>
      <param name="value">The retrieved value.</param>
      <typeparam name="T">The type of the value to be retrieved.</typeparam>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.CatalogExportProvider">
      <summary>Retrieves exports from a catalog.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CatalogExportProvider.#ctor(System.ComponentModel.Composition.Primitives.ComposablePartCatalog)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CatalogExportProvider" /> class with the specified catalog.</summary>
      <param name="catalog">The catalog that the <see cref="T:System.ComponentModel.Composition.Hosting.CatalogExportProvider" /> uses to produce <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="catalog" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CatalogExportProvider.#ctor(System.ComponentModel.Composition.Primitives.ComposablePartCatalog,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CatalogExportProvider" /> class with the specified catalog and optional thread-safe mode. </summary>
      <param name="catalog">The catalog that the <see cref="T:System.ComponentModel.Composition.Hosting.CatalogExportProvider" /> uses to produce <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects.</param>
      <param name="isThreadSafe">true if this object must be thread-safe; otherwise, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="catalog" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CatalogExportProvider.#ctor(System.ComponentModel.Composition.Primitives.ComposablePartCatalog,System.ComponentModel.Composition.Hosting.CompositionOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CatalogExportProvider" /> class with the specified catalog and composition options.</summary>
      <param name="catalog">The catalog that the <see cref="T:System.ComponentModel.Composition.Hosting.CatalogExportProvider" /> uses to produce <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects.</param>
      <param name="compositionOptions">Options that determine the behavior of this provider.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="catalog" /> is null.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.CatalogExportProvider.Catalog">
      <summary>Gets the catalog that is used to provide exports.</summary>
      <returns>The catalog that the <see cref="T:System.ComponentModel.Composition.Hosting.CatalogExportProvider" /> uses to produce <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> has been disposed of.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CatalogExportProvider.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CatalogExportProvider" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CatalogExportProvider.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Composition.Hosting.CatalogExportProvider" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CatalogExportProvider.GetExportsCore(System.ComponentModel.Composition.Primitives.ImportDefinition,System.ComponentModel.Composition.Hosting.AtomicComposition)">
      <summary>Returns all exports that match the conditions of the specified import.</summary>
      <returns>A collection that contains all the exports that match the specified condition.</returns>
      <param name="definition">The conditions of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects to be returned.</param>
      <param name="atomicComposition">The composition transaction to use, or null to disable transactional composition.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.CatalogExportProvider.SourceProvider">
      <summary>Gets or sets the export provider that provides access to additional exports.</summary>
      <returns>The export provider that provides the <see cref="T:System.ComponentModel.Composition.Hosting.CatalogExportProvider" /> access to additional <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects. The default is null.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CatalogExportProvider" /> has been disposed of.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">This property has already been set.-or-The methods on the <see cref="T:System.ComponentModel.Composition.Hosting.CatalogExportProvider" /> object have already been accessed.</exception>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.CatalogExtensions">
      <summary>Provides extension methods for constructing composition services.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CatalogExtensions.CreateCompositionService(System.ComponentModel.Composition.Primitives.ComposablePartCatalog)">
      <summary>Creates a new composition service by using the specified catalog as a source for exports.</summary>
      <returns>A new composition service.</returns>
      <param name="composablePartCatalog">The catalog that will provide exports.</param>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs">
      <summary>Provides data for the <see cref="E:System.ComponentModel.Composition.Hosting.INotifyComposablePartCatalogChanged.Changed" /> event.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs.#ctor(System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Primitives.ComposablePartDefinition},System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Primitives.ComposablePartDefinition},System.ComponentModel.Composition.Hosting.AtomicComposition)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs" /> class with the specified changes.</summary>
      <param name="addedDefinitions">The part definitions that were added to the catalog.</param>
      <param name="removedDefinitions">The part definitions that were removed from the catalog.</param>
      <param name="atomicComposition">The composition transaction to use, or null to disable transactional composition.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs.AddedDefinitions">
      <summary>Gets a collection of definitions added to the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> in this change.</summary>
      <returns>A collection of definitions added to the catalog.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs.AtomicComposition">
      <summary>Gets the composition transaction for this change.</summary>
      <returns>The composition transaction for this change.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs.RemovedDefinitions">
      <summary>Gets a collection of definitions removed from the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> in this change.</summary>
      <returns>A collection of definitions removed from the catalog in this change.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider">
      <summary>Retrieves exports from a part.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider" /> class, optionally in thread-safe mode. </summary>
      <param name="isThreadSafe">true if the <see cref="T:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider" /> object must be thread-safe; otherwise, false.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider.#ctor(System.ComponentModel.Composition.Hosting.CompositionOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider" /> class with the specified composition options.</summary>
      <param name="compositionOptions">Options that specify the behavior of this provider.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider.Compose(System.ComponentModel.Composition.Hosting.CompositionBatch)">
      <summary>Executes composition on the specified batch.</summary>
      <param name="batch">The batch to execute composition on.</param>
      <exception cref="T:System.InvalidOperationException">The container is already in the process of composing.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider.GetExportsCore(System.ComponentModel.Composition.Primitives.ImportDefinition,System.ComponentModel.Composition.Hosting.AtomicComposition)">
      <summary>Gets a collection of all exports in this provider that match the conditions of the specified import.</summary>
      <returns>A collection of all exports in this provider that match the specified conditions.</returns>
      <param name="definition">The <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> that defines the conditions of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> to get.</param>
      <param name="atomicComposition">The composition transaction to use, or null to disable transactional composition.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider.SourceProvider">
      <summary>Gets or sets the export provider that provides access to additional <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects.</summary>
      <returns>A provider that provides the <see cref="T:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider" /> access to <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects. The default is null.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider" /> has been disposed of.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">This property has already been set.-or-The methods on the <see cref="T:System.ComponentModel.Composition.Hosting.ComposablePartExportProvider" /> have already been accessed.</exception>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.CompositionBatch">
      <summary>Represents a set of <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> objects which will be added or removed from the container in a single transactional composition.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionBatch.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionBatch" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionBatch.#ctor(System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Primitives.ComposablePart},System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Primitives.ComposablePart})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionBatch" /> class with the specified parts for addition and removal.</summary>
      <param name="partsToAdd">A collection of <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> objects to add.</param>
      <param name="partsToRemove">A collection of <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> objects to remove.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="partsToAdd" /> is null.-or-<paramref name="partsToRemove" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionBatch.AddExport(System.ComponentModel.Composition.Primitives.Export)">
      <summary>Adds the specified export to the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionBatch" /> object.</summary>
      <returns>The part added.</returns>
      <param name="export">The export to add to the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionBatch" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="export" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionBatch.AddPart(System.ComponentModel.Composition.Primitives.ComposablePart)">
      <summary>Adds the specified part to the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionBatch" /> object.</summary>
      <param name="part">The part to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="part" /> is null.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.CompositionBatch.PartsToAdd">
      <summary>Gets the collection of <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> objects to be added.</summary>
      <returns>A collection of parts to be added.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.CompositionBatch.PartsToRemove">
      <summary>Gets the collection of <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> objects to be removed.</summary>
      <returns>A collection of parts to be removed.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionBatch.RemovePart(System.ComponentModel.Composition.Primitives.ComposablePart)">
      <summary>Puts the specified part on the list of parts to remove.</summary>
      <param name="part">The part to be removed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="part" /> is null.</exception>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.CompositionConstants">
      <summary>Contains static metadata keys used by the composition system.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.Hosting.CompositionConstants.ExportTypeIdentityMetadataName">
      <summary>Specifies the metadata key created by the composition system to mark a part with a unique identifier.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.Hosting.CompositionConstants.GenericContractMetadataName">
      <summary>Specifies the metadata key created by the composition system to mark a generic contract.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.Hosting.CompositionConstants.GenericParametersMetadataName">
      <summary>Specifies the metadata key created by the composition system to mark generic parameters.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.Hosting.CompositionConstants.ImportSourceMetadataName">
      <summary>Specifies the metadata key created by the composition system to mark an import source.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.Hosting.CompositionConstants.IsGenericPartMetadataName">
      <summary>Specifies the metadata key created by the composition system to mark an IsGenericPart method.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.Hosting.CompositionConstants.PartCreationPolicyMetadataName">
      <summary>Specifies the metadata key created by the composition system to mark a part with a creation policy.</summary>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.CompositionContainer">
      <summary>Manages the composition of parts.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.#ctor(System.ComponentModel.Composition.Hosting.CompositionOptions,System.ComponentModel.Composition.Hosting.ExportProvider[])">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> class with the specified export providers and options.</summary>
      <param name="compositionOptions">An object that specifies the behavior of this container.</param>
      <param name="providers">An array of <see cref="T:System.ComponentModel.Composition.Hosting.ExportProvider" /> objects that provide the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> access to <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects, or null to set <see cref="P:System.ComponentModel.Composition.Hosting.CompositionContainer.Providers" /> to an empty <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="providers" /> contains an element that is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.#ctor(System.ComponentModel.Composition.Hosting.ExportProvider[])">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> class with the specified export providers.</summary>
      <param name="providers">An array of <see cref="T:System.ComponentModel.Composition.Hosting.ExportProvider" /> objects that provide the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> access to <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects, or null to set <see cref="P:System.ComponentModel.Composition.Hosting.CompositionContainer.Providers" /> to an empty <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="providers" /> contains an element that is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.#ctor(System.ComponentModel.Composition.Primitives.ComposablePartCatalog,System.Boolean,System.ComponentModel.Composition.Hosting.ExportProvider[])">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> class with the specified catalog, thread-safe mode, and export providers. </summary>
      <param name="catalog">A catalog that provides <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects to the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />.</param>
      <param name="isThreadSafe">true if this <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object must be thread-safe; otherwise, false.</param>
      <param name="providers">An array of <see cref="T:System.ComponentModel.Composition.Hosting.ExportProvider" /> objects that provide the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> access to <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects, or null to set the <see cref="P:System.ComponentModel.Composition.Hosting.CompositionContainer.Providers" /> property to an empty <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />.</param>
      <exception cref="T:System.ArgumentException">One or more elements of <paramref name="providers" /> are null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.#ctor(System.ComponentModel.Composition.Primitives.ComposablePartCatalog,System.ComponentModel.Composition.Hosting.CompositionOptions,System.ComponentModel.Composition.Hosting.ExportProvider[])">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> class with the specified catalog, options, and export providers.</summary>
      <param name="catalog">A catalog that provides <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects to the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />.</param>
      <param name="compositionOptions">An object that specifies options that affect the behavior of the container.</param>
      <param name="providers">An array of <see cref="T:System.ComponentModel.Composition.Hosting.ExportProvider" /> objects that provide the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> access to <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects, or null to set <see cref="P:System.ComponentModel.Composition.Hosting.CompositionContainer.Providers" /> to an empty <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="providers" /> contains an element that is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.#ctor(System.ComponentModel.Composition.Primitives.ComposablePartCatalog,System.ComponentModel.Composition.Hosting.ExportProvider[])">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> class with the specified catalog and export providers.</summary>
      <param name="catalog">A catalog that provides <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects to the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />.</param>
      <param name="providers">An array of <see cref="T:System.ComponentModel.Composition.Hosting.ExportProvider" /> objects that provide the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> access to <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects, or null to set <see cref="P:System.ComponentModel.Composition.Hosting.CompositionContainer.Providers" /> to an empty <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="providers" /> contains an element that is null.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.CompositionContainer.Catalog">
      <summary>Gets the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> that provides the container access to <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects.</summary>
      <returns>The catalog that provides the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> access to exports produced from <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> objects. The default is null.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.Compose(System.ComponentModel.Composition.Hosting.CompositionBatch)">
      <summary>Adds or removes the parts in the specified <see cref="T:System.ComponentModel.Composition.Hosting.CompositionBatch" /> from the container and executes composition.</summary>
      <param name="batch">Changes to the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> to include during the composition.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.GetExportsCore(System.ComponentModel.Composition.Primitives.ImportDefinition,System.ComponentModel.Composition.Hosting.AtomicComposition)">
      <summary>Returns a collection of all exports that match the conditions in the specified <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> object.</summary>
      <returns>A collection of all the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects in this <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object that match the conditions specified by <paramref name="definition" />.</returns>
      <param name="definition">The object that defines the conditions of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects to get.</param>
      <param name="atomicComposition">The composition transaction to use, or null to disable transactional composition.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.CompositionContainer.Providers">
      <summary>Gets the export providers that provide the container access to additional <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> objects.</summary>
      <returns>A collection of <see cref="T:System.ComponentModel.Composition.Hosting.ExportProvider" /> objects that provide the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> access to additional <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects. The default is an empty <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> has been disposed of.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.ReleaseExport(System.ComponentModel.Composition.Primitives.Export)">
      <summary>Releases the specified <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> object from the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />. </summary>
      <param name="export">The <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> that needs to be released.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="export" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.ReleaseExport``1(System.Lazy{``0})">
      <summary>Removes the specified export from composition and releases its resources if possible.</summary>
      <param name="export">An indirect reference to the export to remove.</param>
      <typeparam name="T">The type of the export.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.ReleaseExports(System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Primitives.Export})">
      <summary>Releases a set of <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects from the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />. </summary>
      <param name="exports">A collection of <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects to be released.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exports" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="exports" /> contains an element that is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.ReleaseExports``1(System.Collections.Generic.IEnumerable{System.Lazy{``0}})">
      <summary>Removes a collection of exports from composition and releases their resources if possible.</summary>
      <param name="exports">A collection of indirect references to the exports to be removed.</param>
      <typeparam name="T">The type of the exports.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.ReleaseExports``2(System.Collections.Generic.IEnumerable{System.Lazy{``0,``1}})">
      <summary>Removes a collection of exports from composition and releases their resources if possible.</summary>
      <param name="exports">A collection of indirect references to the exports to be removed and their metadata.</param>
      <typeparam name="T">The type of the exports.</typeparam>
      <typeparam name="TMetadataView">The type of the exports' metadata view.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionContainer.SatisfyImportsOnce(System.ComponentModel.Composition.Primitives.ComposablePart)">
      <summary>Satisfies the imports of the specified <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object without registering it for recomposition.</summary>
      <param name="part">The part to satisfy the imports of.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="part" /> is null.</exception>
      <exception cref="T:System.ComponentModel.Composition.CompositionException">An error occurred during composition. <see cref="P:System.ComponentModel.Composition.CompositionException.Errors" /> will contain a collection of the errors that occurred.</exception>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.CompositionOptions">
      <summary>Defines options for export providers.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.Hosting.CompositionOptions.Default">
      <summary>No options are defined.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.Hosting.CompositionOptions.DisableSilentRejection">
      <summary>Silent rejection is disabled, so all rejections will result in errors.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.Hosting.CompositionOptions.IsThreadSafe">
      <summary>This provider should be thread-safe.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.Hosting.CompositionOptions.ExportCompositionService">
      <summary>This provider is an export composition service.</summary>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition">
      <summary>Represents a node in a tree of scoped catalogs, reflecting an underlying catalog and its child scopes.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.#ctor(System.ComponentModel.Composition.Primitives.ComposablePartCatalog,System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Hosting.CompositionScopeDefinition})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition" /> class with the specified underlying catalog and children.</summary>
      <param name="catalog">The underlying catalog for this catalog.</param>
      <param name="children">A collection of the child scopes of this catalog.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.#ctor(System.ComponentModel.Composition.Primitives.ComposablePartCatalog,System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Hosting.CompositionScopeDefinition},System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Primitives.ExportDefinition})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition" /> class with the specified underlying catalog, children, and public surface.</summary>
      <param name="catalog">The underlying catalog for this catalog.</param>
      <param name="children">A collection of the child scopes of this catalog.</param>
      <param name="publicSurface">The public surface for this catalog.</param>
    </member>
    <member name="E:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.Changed">
      <summary>Occurs when the underlying catalog has changed, if that catalog supports notifications.</summary>
    </member>
    <member name="E:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.Changing">
      <summary>Occurs when the underlying catalog is changing, if that catalog supports notifications.</summary>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.Children">
      <summary>Gets the child scopes of this catalog.</summary>
      <returns>A collection of the child scopes of this catalog.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.Dispose(System.Boolean)">
      <summary>Called by the Dispose() and Finalize() methods to release the managed and unmanaged resources used by the current instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition" /> class.</summary>
      <param name="disposing">true to release managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.GetEnumerator">
      <summary>Returns an enumerator that iterates through the catalog.</summary>
      <returns>An enumerator that can be used to iterate through the catalog.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.GetExports(System.ComponentModel.Composition.Primitives.ImportDefinition)">
      <summary>Gets a collection of exports that match the conditions specified by the import definition.</summary>
      <returns>A collection of exports that match the specified conditions.</returns>
      <param name="definition">Conditions that specify which exports to match.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.OnChanged(System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.Changed" /> event.</summary>
      <param name="e">Contains data for the <see cref="E:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.Changed" /> event.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.OnChanging(System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.Changing" /> event.</summary>
      <param name="e">Contains data for the <see cref="E:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.Changing" /> event.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.CompositionScopeDefinition.PublicSurface">
      <summary>Gets a collection of parts visible to the parent scope of this catalog.</summary>
      <returns>A collection of parts visible to the parent scope of this catalog.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.CompositionService">
      <summary>Provides methods to satisfy imports on an existing part instance.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionService.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.CompositionService.SatisfyImportsOnce(System.ComponentModel.Composition.Primitives.ComposablePart)">
      <summary>Composes the specified part, with recomposition and validation disabled.</summary>
      <param name="part">The part to compose.</param>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog">
      <summary>Discovers attributed parts in the assemblies in a specified directory.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> class by using <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects based on all the DLL files in the specified directory path.</summary>
      <param name="path">The path to the directory to scan for assemblies to add to the catalog.The path must be absolute or relative to <see cref="P:System.AppDomain.BaseDirectory" />.</param>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified <paramref name="path" /> is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more implementation-specific invalid characters.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based computers, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.#ctor(System.String,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> class by using <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects based on all the DLL files in the specified directory path with the specified source for parts.</summary>
      <param name="path">The path to the directory to scan for assemblies to add to the catalog.The path must be absolute or relative to <see cref="P:System.AppDomain.BaseDirectory" />.</param>
      <param name="definitionOrigin">The element used by diagnostics to identify the source for parts.</param>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified <paramref name="path" /> is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more implementation-specific invalid characters.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based computers, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.#ctor(System.String,System.Reflection.ReflectionContext)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> class by using <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects based on all the DLL files in the specified directory path, in the specified reflection context.</summary>
      <param name="path">The path to the directory to scan for assemblies to add to the catalog.The path must be absolute or relative to <see cref="P:System.AppDomain.BaseDirectory" />.</param>
      <param name="reflectionContext">The context used to create parts.</param>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified <paramref name="path" /> is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more implementation-specific invalid characters.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based computers, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.#ctor(System.String,System.Reflection.ReflectionContext,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> class by  using <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects based on all the DLL files in the specified directory path, in the specified reflection context.</summary>
      <param name="path">The path to the directory to scan for assemblies to add to the catalog.The path must be absolute or relative to <see cref="P:System.AppDomain.BaseDirectory" />.</param>
      <param name="reflectionContext">The context used to create parts.</param>
      <param name="definitionOrigin">The element used by diagnostics to identify the source for parts.</param>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified <paramref name="path" /> is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more implementation-specific invalid characters.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based computers, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> class by using <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects that match a specified search pattern in the specified directory path.</summary>
      <param name="path">The path to the directory to scan for assemblies to add to the catalog.The path must be absolute or relative to <see cref="P:System.AppDomain.BaseDirectory" />.</param>
      <param name="searchPattern">The search string. The format of the string should be the same as specified for the <see cref="M:System.IO.Directory.GetFiles(System.String,System.String)" /> method.</param>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified <paramref name="path" /> is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="searchPattern" /> is null.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more implementation-specific invalid characters.-or-<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based computers, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.#ctor(System.String,System.String,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> class by using <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects based on the specified search pattern in the specified directory path with the specified source for parts.</summary>
      <param name="path">The path to the directory to scan for assemblies to add to the catalog.The path must be absolute or relative to <see cref="P:System.AppDomain.BaseDirectory" />.</param>
      <param name="searchPattern">The search string. The format of the string should be the same as specified for the <see cref="M:System.IO.Directory.GetFiles(System.String,System.String)" /> method.</param>
      <param name="definitionOrigin">The element used by diagnostics to identify the source for parts.</param>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified <paramref name="path" /> is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="searchPattern" /> is null.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more implementation-specific invalid characters. -or-<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based computers, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.#ctor(System.String,System.String,System.Reflection.ReflectionContext)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> class by using <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects based on the specified search pattern in the specified directory path, using the specified reflection context.</summary>
      <param name="path">The path to the directory to scan for assemblies to add to the catalog.The path must be absolute or relative to <see cref="P:System.AppDomain.BaseDirectory" />.</param>
      <param name="searchPattern">The search string. The format of the string should be the same as specified for the <see cref="M:System.IO.Directory.GetFiles(System.String,System.String)" /> method.</param>
      <param name="reflectionContext">The context used to create parts.</param>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified <paramref name="path" /> is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="searchPattern" /> is null.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more implementation-specific invalid characters.-or-<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based computers, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.#ctor(System.String,System.String,System.Reflection.ReflectionContext,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> class by using <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects based on the specified search pattern in the specified directory path, using the specified reflection context.</summary>
      <param name="path">The path to the directory to scan for assemblies to add to the catalog.The path must be absolute or relative to <see cref="P:System.AppDomain.BaseDirectory" />.</param>
      <param name="searchPattern">The search string. The format of the string should be the same as specified for the <see cref="M:System.IO.Directory.GetFiles(System.String,System.String)" /> method.</param>
      <param name="reflectionContext">The context used to create parts.</param>
      <param name="definitionOrigin">The element used by diagnostics to identify the source for parts.</param>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified <paramref name="path" /> is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="searchPattern" /> is null.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more implementation-specific invalid characters.-or-<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based computers, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
    </member>
    <member name="E:System.ComponentModel.Composition.Hosting.DirectoryCatalog.Changed">
      <summary>Occurs when the contents of the catalog has changed.</summary>
    </member>
    <member name="E:System.ComponentModel.Composition.Hosting.DirectoryCatalog.Changing">
      <summary>Occurs when the catalog is changing.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.DirectoryCatalog.FullPath">
      <summary>Gets the translated absolute path observed by the <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> object.</summary>
      <returns>The translated absolute path observed by the catalog.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.GetEnumerator">
      <summary>Returns an enumerator that iterates through the catalog.</summary>
      <returns>An enumerator that can be used to iterate through the catalog.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.GetExports(System.ComponentModel.Composition.Primitives.ImportDefinition)">
      <summary>Gets the export definitions that match the constraint expressed by the specified import definition.</summary>
      <returns>A collection of objects that contain the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> objects and their associated <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects that match the constraint specified by <paramref name="definition" />.</returns>
      <param name="definition">The conditions of the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> objects to be returned.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> object has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="definition" /> is null.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.DirectoryCatalog.LoadedFiles">
      <summary>Gets the collection of files currently loaded in the catalog.</summary>
      <returns>A collection of files currently loaded in the catalog.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.OnChanged(System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Composition.Hosting.DirectoryCatalog.Changed" /> event.</summary>
      <param name="e">An object  that contains the event data. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.OnChanging(System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Composition.Hosting.DirectoryCatalog.Changing" /> event.</summary>
      <param name="e">An object  that contains the event data.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.DirectoryCatalog.Path">
      <summary>Gets the path observed by the <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> object.</summary>
      <returns>The path observed by the catalog.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.Refresh">
      <summary>Refreshes the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects with the latest files in the directory that match the search pattern. </summary>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.DirectoryCatalog.SearchPattern">
      <summary>Gets the search pattern that is passed into the constructor of the <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> object.</summary>
      <returns>The search pattern the catalog uses to find files. The default is *.dll, which returns all DLL files.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.DirectoryCatalog.System#ComponentModel#Composition#Primitives#ICompositionElement#DisplayName">
      <summary>Gets the display name of the directory catalog.</summary>
      <returns>A string that contains a human-readable display name of the directory catalog.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.DirectoryCatalog.System#ComponentModel#Composition#Primitives#ICompositionElement#Origin">
      <summary>Gets the composition element from which the directory catalog originated.</summary>
      <returns>Always null.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.DirectoryCatalog.ToString">
      <summary>Gets a string representation of the directory catalog.</summary>
      <returns>A string representation of the catalog.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.ExportProvider">
      <summary>Retrieves exports which match a specified <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> object.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ExportProvider" /> class.</summary>
    </member>
    <member name="E:System.ComponentModel.Composition.Hosting.ExportProvider.ExportsChanged">
      <summary>Occurs when the exports in the <see cref="T:System.ComponentModel.Composition.Hosting.ExportProvider" /> change.</summary>
    </member>
    <member name="E:System.ComponentModel.Composition.Hosting.ExportProvider.ExportsChanging">
      <summary>Occurs when the provided exports are changing.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExport``1">
      <summary>Returns the export with the contract name derived from the specified type parameter. If there is not exactly one matching export, an exception is thrown.</summary>
      <returns>The export with the contract name derived from the specified type parameter.</returns>
      <typeparam name="T">The type parameter of the <see cref="T:System.Lazy`1" /> object to return. The contract name is also derived from this type parameter.</typeparam>
      <exception cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException">There are zero <see cref="T:System.Lazy`1" /> objects with the contract name derived from <paramref name="T" /> in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object.-or-There is more than one <see cref="T:System.Lazy`1" /> object with the contract name derived from <paramref name="T" /> in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExport``2">
      <summary>Returns the export with the contract name derived from the specified type parameter. If there is not exactly one matching export, an exception is thrown.</summary>
      <returns>System.Lazy`2</returns>
      <typeparam name="T">The type parameter of the <see cref="T:System.Lazy`2" /> object to return. The contract name is also derived from this type parameter.</typeparam>
      <typeparam name="TMetadataView">The type of the metadata view of the <see cref="T:System.Lazy`2" /> object to return.</typeparam>
      <exception cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException">There are zero <see cref="T:System.Lazy`2" /> objects with the contract name derived from <paramref name="T" /> in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object.-or-There is more than one <see cref="T:System.Lazy`2" /> object with the contract name derived from <paramref name="T" /> in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="TMetadataView" /> is not a valid metadata view type.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExport``1(System.String)">
      <summary>Returns the export with the specified contract name. If there is not exactly one matching export, an exception is thrown.</summary>
      <returns>The export with the specified contract name.</returns>
      <param name="contractName">The contract name of the <see cref="T:System.Lazy`1" /> object to return, or null or an empty string ("") to use the default contract name.</param>
      <typeparam name="T">The type parameter of the <see cref="T:System.Lazy`1" /> object to return.</typeparam>
      <exception cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException">There are zero <see cref="T:System.Lazy`1" /> objects with the contract name derived from <paramref name="T" /> in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object.-or-There is more than one <see cref="T:System.Lazy`1" /> object with the contract name derived from <paramref name="T" /> in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExport``2(System.String)">
      <summary>Returns the export with the specified contract name. If there is not exactly one matching export, an exception is thrown.</summary>
      <returns>The export with the specified contract name.</returns>
      <param name="contractName">The contract name of the <see cref="T:System.Lazy`2" /> object to return, or null or an empty string ("") to use the default contract name.</param>
      <typeparam name="T">The type parameter of the <see cref="T:System.Lazy`2" /> object to return.</typeparam>
      <typeparam name="TMetadataView">The type of the metadata view of the <see cref="T:System.Lazy`2" /> object to return.</typeparam>
      <exception cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException">There are zero <see cref="T:System.Lazy`2" /> objects with the contract name derived from <paramref name="T" /> in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object.-or-There is more than one <see cref="T:System.Lazy`2" /> object with the contract name derived from <paramref name="T" /> in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="TMetadataView" /> is not a valid metadata view type.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExportedValue``1">
      <summary>Returns the exported object with the contract name derived from the specified type parameter. If there is not exactly one matching exported object, an exception is thrown.</summary>
      <returns>The exported object with the contract name derived from the specified type parameter.</returns>
      <typeparam name="T">The type of the exported object to return. The contract name is also derived from this type parameter.</typeparam>
      <exception cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException">There are zero exported objects with the contract name derived from <paramref name="T" /> in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />.-or-There is more than one exported object with the contract name derived from <paramref name="T" /> in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
      <exception cref="T:System.ComponentModel.Composition.CompositionContractMismatchException">The underlying exported object cannot be cast to <paramref name="T" />.</exception>
      <exception cref="T:System.ComponentModel.Composition.CompositionException">An error occurred during composition. <see cref="P:System.ComponentModel.Composition.CompositionException.Errors" /> will contain a collection of errors that occurred.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExportedValue``1(System.String)">
      <summary>Returns the exported object with the specified contract name. If there is not exactly one matching exported object, an exception is thrown.</summary>
      <returns>The exported object with the specified contract name.</returns>
      <param name="contractName">The contract name of the exported object to return, or null or an empty string ("") to use the default contract name.</param>
      <typeparam name="T">The type of the exported object to return.</typeparam>
      <exception cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException">There are zero exported objects with the contract name derived from <paramref name="T" /> in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />.-or-There is more than one exported object with the contract name derived from <paramref name="T" /> in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
      <exception cref="T:System.ComponentModel.Composition.CompositionContractMismatchException">The underlying exported object cannot be cast to <paramref name="T" />.</exception>
      <exception cref="T:System.ComponentModel.Composition.CompositionException">An error occurred during composition. <see cref="P:System.ComponentModel.Composition.CompositionException.Errors" /> will contain a collection of errors that occurred.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExportedValueOrDefault``1">
      <summary>Gets the exported object with the contract name derived from the specified type parameter or the default value for the specified type, or throws an exception if there is more than one matching exported object.</summary>
      <returns>The exported object with the contract name derived from <paramref name="T" />, if found; otherwise, the default value for <paramref name="T" />.</returns>
      <typeparam name="T">The type of the exported object to return. The contract name is also derived from this type parameter.</typeparam>
      <exception cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException">There is more than one exported object with the contract name derived from <paramref name="T" /> in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
      <exception cref="T:System.ComponentModel.Composition.CompositionContractMismatchException">The underlying exported object cannot be cast to <paramref name="T" />.</exception>
      <exception cref="T:System.ComponentModel.Composition.CompositionException">An error occurred during composition. <see cref="P:System.ComponentModel.Composition.CompositionException.Errors" /> will contain a collection of errors that occurred.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExportedValueOrDefault``1(System.String)">
      <summary>Gets the exported object with the specified contract name or the default value for the specified type, or throws an exception if there is more than one matching exported object.</summary>
      <returns>The exported object with the specified contract name, if found; otherwise, the default value for <paramref name="T" />.</returns>
      <param name="contractName">The contract name of the exported object to return, or null or an empty string ("") to use the default contract name.</param>
      <typeparam name="T">The type of the exported object to return.</typeparam>
      <exception cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException">There is more than one exported object with the specified contract name in the <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
      <exception cref="T:System.ComponentModel.Composition.CompositionContractMismatchException">The underlying exported object cannot be cast to <paramref name="T" />.</exception>
      <exception cref="T:System.ComponentModel.Composition.CompositionException">An error occurred during composition. <see cref="P:System.ComponentModel.Composition.CompositionException.Errors" /> will contain a collection of errors that occurred.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExportedValues``1">
      <summary>Gets all the exported objects with the contract name derived from the specified type parameter.</summary>
      <returns>The exported objects with the contract name derived from the specified type parameter, if found; otherwise, an empty <see cref="T:System.Collections.ObjectModel.Collection`1" /> object.</returns>
      <typeparam name="T">The type of the exported object to return. The contract name is also derived from this type parameter.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
      <exception cref="T:System.ComponentModel.Composition.CompositionContractMismatchException">One or more of the underlying exported objects cannot be cast to <paramref name="T" />.</exception>
      <exception cref="T:System.ComponentModel.Composition.CompositionException">An error occurred during composition. <see cref="P:System.ComponentModel.Composition.CompositionException.Errors" /> will contain a collection of errors that occurred.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExportedValues``1(System.String)">
      <summary>Gets all the exported objects with the specified contract name.</summary>
      <returns>The exported objects with the specified contract name, if found; otherwise, an empty <see cref="T:System.Collections.ObjectModel.Collection`1" /> object.</returns>
      <param name="contractName">The contract name of the exported objects to return; or null or an empty string ("") to use the default contract name.</param>
      <typeparam name="T">The type of the exported object to return.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
      <exception cref="T:System.ComponentModel.Composition.CompositionContractMismatchException">One or more of the underlying exported values cannot be cast to <paramref name="T" />.</exception>
      <exception cref="T:System.ComponentModel.Composition.CompositionException">An error occurred during composition. <see cref="P:System.ComponentModel.Composition.CompositionException.Errors" /> will contain a collection of errors that occurred.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExports``1">
      <summary>Gets all the exports with the contract name derived from the specified type parameter.</summary>
      <returns>The <see cref="T:System.Lazy`1" /> objects with the contract name derived from <paramref name="T" />, if found; otherwise, an empty <see cref="T:System.Collections.Generic.IEnumerable`1" /> object.</returns>
      <typeparam name="T">The type parameter of the <see cref="T:System.Lazy`1" /> objects to return. The contract name is also derived from this type parameter.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExports``2">
      <summary>Gets all the exports with the contract name derived from the specified type parameter.</summary>
      <returns>The <see cref="T:System.Lazy`2" /> objects with the contract name derived from <paramref name="T" />, if found; otherwise, an empty <see cref="T:System.Collections.Generic.IEnumerable`1" /> object.</returns>
      <typeparam name="T">The type parameter of the <see cref="T:System.Lazy`2" /> objects to return. The contract name is also derived from this type parameter.</typeparam>
      <typeparam name="TMetadataView">The type of the metadata view of the <see cref="T:System.Lazy`2" /> objects to return.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="TMetadataView" /> is not a valid metadata view type.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExports(System.ComponentModel.Composition.Primitives.ImportDefinition)">
      <summary>Gets all exports that match the conditions of the specified import definition.</summary>
      <returns>A collection of all the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects matching the condition specified by <paramref name="definition" />.</returns>
      <param name="definition">The object that defines the conditions of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects to get.</param>
      <exception cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException">
        <see cref="P:System.ComponentModel.Composition.Primitives.ImportDefinition.Cardinality" /> is <see cref="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ExactlyOne" /> and there are zero <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects that match the conditions of the specified <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" />.-or-<see cref="P:System.ComponentModel.Composition.Primitives.ImportDefinition.Cardinality" /> is <see cref="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ZeroOrOne" /> or <see cref="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ExactlyOne" /> and there is more than one <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> object that matches the conditions of the specified <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="definition" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExports(System.ComponentModel.Composition.Primitives.ImportDefinition,System.ComponentModel.Composition.Hosting.AtomicComposition)">
      <summary>Gets all exports that match the conditions of the specified import definition and composition.</summary>
      <returns>A collection of all the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects matching the condition specified by <paramref name="definition" /> and <paramref name="atomicComposition" />.</returns>
      <param name="definition">The object that defines the conditions of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects to get.</param>
      <param name="atomicComposition">The transactional container for the composition.</param>
      <exception cref="T:System.ComponentModel.Composition.ImportCardinalityMismatchException">
        <see cref="P:System.ComponentModel.Composition.Primitives.ImportDefinition.Cardinality" /> is <see cref="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ExactlyOne" /> and there are zero <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects that match the conditions of the specified <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" />.-or-<see cref="P:System.ComponentModel.Composition.Primitives.ImportDefinition.Cardinality" /> is <see cref="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ZeroOrOne" /> or <see cref="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ExactlyOne" /> and there is more than one <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> object that matches the conditions of the specified <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="definition" /> is null.-or-<paramref name="atomicComposition" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExports``1(System.String)">
      <summary>Gets all the exports with the specified contract name.</summary>
      <returns>The <see cref="T:System.Lazy`1" /> objects with the specified contract name, if found; otherwise, an empty <see cref="T:System.Collections.Generic.IEnumerable`1" /> object.</returns>
      <param name="contractName">The contract name of the <see cref="T:System.Lazy`1" /> objects to return, or null or an empty string ("") to use the default contract name.</param>
      <typeparam name="T">The type parameter of the <see cref="T:System.Lazy`1" /> objects to return.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExports``2(System.String)">
      <summary>Gets all the exports with the specified contract name.</summary>
      <returns>The <see cref="T:System.Lazy`2" /> objects with the specified contract name if found; otherwise, an empty <see cref="T:System.Collections.Generic.IEnumerable`1" /> object.</returns>
      <param name="contractName">The contract name of the <see cref="T:System.Lazy`2" /> objects to return, or null or an empty string ("") to use the default contract name.</param>
      <typeparam name="T">The type parameter of the <see cref="T:System.Lazy'2" /> objects to return. The contract name is also derived from this type parameter.</typeparam>
      <typeparam name="TMetadataView">The type of the metadata view of the <see cref="T:System.Lazy`2" /> objects to return.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="TMetadataView" /> is not a valid metadata view type.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExports(System.Type,System.Type,System.String)">
      <summary>Gets all the exports with the specified contract name.</summary>
      <returns>A collection of all the <see cref="T:System.Lazy`2" /> objects for the contract matching <paramref name="contractName" />.</returns>
      <param name="type">The type parameter of the <see cref="T:System.Lazy`2" /> objects to return.</param>
      <param name="metadataViewType">The type of the metadata view of the <see cref="T:System.Lazy`2" /> objects to return.</param>
      <param name="contractName">The contract name of the <see cref="T:System.Lazy`2" /> object to return, or null or an empty string ("") to use the default contract name.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.CompositionContainer" /> object has been disposed of.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="metadataViewType" /> is not a valid metadata view type.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.GetExportsCore(System.ComponentModel.Composition.Primitives.ImportDefinition,System.ComponentModel.Composition.Hosting.AtomicComposition)">
      <summary>Gets all the exports that match the constraint defined by the specified definition.</summary>
      <returns>A collection that contains all the exports that match the specified condition.</returns>
      <param name="definition">The object that defines the conditions of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects to return.</param>
      <param name="atomicComposition">The transactional container for the composition.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.OnExportsChanged(System.ComponentModel.Composition.Hosting.ExportsChangeEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Composition.Hosting.ExportProvider.ExportsChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.ComponentModel.Composition.Hosting.ExportsChangeEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.OnExportsChanging(System.ComponentModel.Composition.Hosting.ExportsChangeEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Composition.Hosting.ExportProvider.ExportsChanging" /> event.</summary>
      <param name="e">An <see cref="T:System.ComponentModel.Composition.Hosting.ExportsChangeEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportProvider.TryGetExports(System.ComponentModel.Composition.Primitives.ImportDefinition,System.ComponentModel.Composition.Hosting.AtomicComposition,System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Primitives.Export}@)">
      <summary>Gets all the exports that match the conditions of the specified import.</summary>
      <returns>trueif <see cref="P:System.ComponentModel.Composition.Primitives.ImportDefinition.Cardinality" /> is <see cref="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ZeroOrOne" /> or <see cref="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ZeroOrMore" /> and there are zero <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects that match the conditions of the specified <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" />; true if <see cref="P:System.ComponentModel.Composition.Primitives.ImportDefinition.Cardinality" /> is <see cref="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ZeroOrOne" /> or <see cref="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ExactlyOne" /> and there is exactly one <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> that matches the conditions of the specified <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" />; otherwise, false.</returns>
      <param name="definition">The object that defines the conditions of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects to get.</param>
      <param name="atomicComposition">The transactional container for the composition.</param>
      <param name="exports">When this method returns, contains a collection of <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects that match the conditions defined by <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" />, if found; otherwise, an empty <see cref="T:System.Collections.Generic.IEnumerable`1" /> object. This parameter is passed uninitialized. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="definition" /> is null.</exception>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.ExportsChangeEventArgs">
      <summary>Provides data for the <see cref="E:System.ComponentModel.Composition.Hosting.ExportProvider.ExportsChanging" /> and <see cref="E:System.ComponentModel.Composition.Hosting.ExportProvider.ExportsChanged" /> event. </summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ExportsChangeEventArgs.#ctor(System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Primitives.ExportDefinition},System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Primitives.ExportDefinition},System.ComponentModel.Composition.Hosting.AtomicComposition)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ExportsChangeEventArgs" /> class. </summary>
      <param name="addedExports">The events that were added.</param>
      <param name="removedExports">The events that were removed.</param>
      <param name="atomicComposition">The composition transaction that contains the change.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.ExportsChangeEventArgs.AddedExports">
      <summary>Gets the exports that were added in this change.</summary>
      <returns>A collection of the exports that were added.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.ExportsChangeEventArgs.AtomicComposition">
      <summary>Gets the composition transaction of the change, if any.</summary>
      <returns>A reference to the composition transaction associated with the change, or null if no transaction is being used.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.ExportsChangeEventArgs.ChangedContractNames">
      <summary>Gets the contract names that were altered in the change.</summary>
      <returns>A collection of the altered contract names.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.ExportsChangeEventArgs.RemovedExports">
      <summary>Gets the exports that were removed in the change.</summary>
      <returns>A collection of the removed exports.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.FilteredCatalog">
      <summary>Represents a catalog after a filter function is applied to it.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.FilteredCatalog.#ctor(System.ComponentModel.Composition.Primitives.ComposablePartCatalog,System.Func{System.ComponentModel.Composition.Primitives.ComposablePartDefinition,System.Boolean})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.FilteredCatalog" /> class with the specified underlying catalog and filter.</summary>
      <param name="catalog">The underlying catalog.</param>
      <param name="filter">The function to filter parts.</param>
    </member>
    <member name="E:System.ComponentModel.Composition.Hosting.FilteredCatalog.Changed">
      <summary>Occurs when the underlying catalog has changed.</summary>
    </member>
    <member name="E:System.ComponentModel.Composition.Hosting.FilteredCatalog.Changing">
      <summary>Occurs when the underlying catalog is changing.</summary>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.FilteredCatalog.Complement">
      <summary>Gets a catalog that contains parts that are present in the underlying catalog but that were filtered out by the filter function.</summary>
      <returns>A catalog that contains the complement of this catalog.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.FilteredCatalog.Dispose(System.Boolean)">
      <summary>Called by the Dispose() and Finalize() methods to release the managed and unmanaged resources used by the current instance of the <see cref="T:System.ComponentModel.Composition.Hosting.FilteredCatalog" /> class.</summary>
      <param name="disposing">true to release managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.FilteredCatalog.GetEnumerator">
      <summary>Returns an enumerator that iterates through the catalog.</summary>
      <returns>An enumerator that can be used to iterate through the catalog.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.FilteredCatalog.GetExports(System.ComponentModel.Composition.Primitives.ImportDefinition)">
      <summary>Gets the exported parts from this catalog that match the specified import.</summary>
      <returns>A collection of matching parts.</returns>
      <param name="definition">The import to match.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.FilteredCatalog.IncludeDependencies">
      <summary>Gets a new <see cref="T:System.ComponentModel.Composition.Hosting.FilteredCatalog" /> object that contains all the parts from this catalog and all their dependencies.</summary>
      <returns>The new catalog.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.FilteredCatalog.IncludeDependencies(System.Func{System.ComponentModel.Composition.Primitives.ImportDefinition,System.Boolean})">
      <summary>Gets a new <see cref="T:System.ComponentModel.Composition.Hosting.FilteredCatalog" /> object that contains all the parts from this catalog and all dependencies that can be reached through imports that match the specified filter.</summary>
      <returns>The new catalog.</returns>
      <param name="importFilter">The filter for imports.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.FilteredCatalog.IncludeDependents">
      <summary>Gets a new <see cref="T:System.ComponentModel.Composition.Hosting.FilteredCatalog" /> object that contains all the parts from this catalog and all their dependents.</summary>
      <returns>The new catalog.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.FilteredCatalog.IncludeDependents(System.Func{System.ComponentModel.Composition.Primitives.ImportDefinition,System.Boolean})">
      <summary>Gets a new <see cref="T:System.ComponentModel.Composition.Hosting.FilteredCatalog" /> object that contains all the parts from this catalog and all dependents that can be reached through imports that match the specified filter.</summary>
      <returns>The new catalog.</returns>
      <param name="importFilter">The filter for imports.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.FilteredCatalog.OnChanged(System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Composition.Hosting.FilteredCatalog.Changed" /> event.</summary>
      <param name="e">Provides data for the event.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.FilteredCatalog.OnChanging(System.ComponentModel.Composition.Hosting.ComposablePartCatalogChangeEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Composition.Hosting.FilteredCatalog.Changing" /> event.</summary>
      <param name="e">Provides data for the event.</param>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.ImportEngine">
      <summary>Performs composition for containers.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ImportEngine.#ctor(System.ComponentModel.Composition.Hosting.ExportProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ImportEngine" /> class. </summary>
      <param name="sourceProvider">The <see cref="T:System.ComponentModel.Composition.Hosting.ExportProvider" /> that provides the <see cref="T:System.ComponentModel.Composition.Hosting.ImportEngine" /> access to <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ImportEngine.#ctor(System.ComponentModel.Composition.Hosting.ExportProvider,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ImportEngine" /> class, optionally in thread-safe mode.</summary>
      <param name="sourceProvider">The <see cref="T:System.ComponentModel.Composition.Hosting.ExportProvider" /> that provides the <see cref="T:System.ComponentModel.Composition.Hosting.ImportEngine" /> access to <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects.</param>
      <param name="isThreadSafe">true if thread safety is required; otherwise, false.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ImportEngine.#ctor(System.ComponentModel.Composition.Hosting.ExportProvider,System.ComponentModel.Composition.Hosting.CompositionOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ImportEngine" /> class with the specified options.</summary>
      <param name="sourceProvider">The <see cref="T:System.ComponentModel.Composition.Hosting.ExportProvider" /> that provides the <see cref="T:System.ComponentModel.Composition.Hosting.ImportEngine" /> access to <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects.</param>
      <param name="compositionOptions">An object that specifies options that affect the behavior of the engine.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ImportEngine.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.ComponentModel.Composition.Hosting.ImportEngine" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ImportEngine.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Composition.Hosting.ImportEngine" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ImportEngine.PreviewImports(System.ComponentModel.Composition.Primitives.ComposablePart,System.ComponentModel.Composition.Hosting.AtomicComposition)">
      <summary>Previews all the required imports for the specified part to make sure that they can be satisfied, without actually setting them.</summary>
      <param name="part">The part to preview the imports of.</param>
      <param name="atomicComposition">The composition transaction to use, or null for no composition transaction.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ImportEngine.ReleaseImports(System.ComponentModel.Composition.Primitives.ComposablePart,System.ComponentModel.Composition.Hosting.AtomicComposition)">
      <summary>Releases all the exports used to satisfy the imports of the specified part.</summary>
      <param name="part">The part to release the imports of.</param>
      <param name="atomicComposition">The composition transaction to use, or null for no composition transaction.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ImportEngine.SatisfyImports(System.ComponentModel.Composition.Primitives.ComposablePart)">
      <summary>Satisfies the imports of the specified part.</summary>
      <param name="part">The part to satisfy the imports of.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ImportEngine.SatisfyImportsOnce(System.ComponentModel.Composition.Primitives.ComposablePart)">
      <summary>Satisfies the imports of the specified part without registering them for recomposition.</summary>
      <param name="part">The part to satisfy the imports of.</param>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.INotifyComposablePartCatalogChanged">
      <summary>Provides notifications when a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> changes.</summary>
    </member>
    <member name="E:System.ComponentModel.Composition.Hosting.INotifyComposablePartCatalogChanged.Changed">
      <summary>Occurs when a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> has changed.</summary>
    </member>
    <member name="E:System.ComponentModel.Composition.Hosting.INotifyComposablePartCatalogChanged.Changing">
      <summary>Occurs when a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> is changing.</summary>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.ScopingExtensions">
      <summary>Defines static convenience methods for scoping.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ScopingExtensions.ContainsPartMetadata``1(System.ComponentModel.Composition.Primitives.ComposablePartDefinition,System.String,``0)">
      <summary>Gets a value that indicates whether the specified part contains metadata that has the specified key and value.</summary>
      <returns>true if <paramref name="part" /> contains metadata that has the specified key, value type, and value; otherwise, false.</returns>
      <param name="part">The part to search.</param>
      <param name="key">The metadata key.</param>
      <param name="value">The metadata value.</param>
      <typeparam name="T">The type of the metadata value.</typeparam>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ScopingExtensions.ContainsPartMetadataWithKey(System.ComponentModel.Composition.Primitives.ComposablePartDefinition,System.String)">
      <summary>Gets a value that indicates whether the specified part contains metadata that has the specified key.</summary>
      <returns>true if <paramref name="part" /> contains metadata that has the specified key; otherwise, false.</returns>
      <param name="part">The part to search.</param>
      <param name="key">The metadata key.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ScopingExtensions.Exports(System.ComponentModel.Composition.Primitives.ComposablePartDefinition,System.String)">
      <summary>Gets a value that indicates whether the specified part exports the specified contract.</summary>
      <returns>true if <paramref name="part" /> exports the specified contract; otherwise, false.</returns>
      <param name="part">The part to search.</param>
      <param name="contractName">The name of the contract.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ScopingExtensions.Filter(System.ComponentModel.Composition.Primitives.ComposablePartCatalog,System.Func{System.ComponentModel.Composition.Primitives.ComposablePartDefinition,System.Boolean})">
      <summary>Filters the specified catalog with the specified filter function.</summary>
      <returns>A new catalog filtered by using the specified filter.</returns>
      <param name="catalog">The catalog to filter.</param>
      <param name="filter">The filter function.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ScopingExtensions.Imports(System.ComponentModel.Composition.Primitives.ComposablePartDefinition,System.String)">
      <summary>Determines whether the specified part imports the specified contract.</summary>
      <returns>true if <paramref name="part" /> imports the specified contract; otherwise, false.</returns>
      <param name="part">The part to search.</param>
      <param name="contractName">The name of the contract.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.ScopingExtensions.Imports(System.ComponentModel.Composition.Primitives.ComposablePartDefinition,System.String,System.ComponentModel.Composition.Primitives.ImportCardinality)">
      <summary>Determines whether the specified part imports the specified contract with the specified cardinality.</summary>
      <returns>true if <paramref name="part" /> imports a contract that has the specified name and cardinality; otherwise, false.</returns>
      <param name="part">The part to search.</param>
      <param name="contractName">The name of the contract.</param>
      <param name="importCardinality">The cardinality of the contract.</param>
    </member>
    <member name="T:System.ComponentModel.Composition.Hosting.TypeCatalog">
      <summary>Discovers attributed parts from a collection of types.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.TypeCatalog.#ctor(System.Collections.Generic.IEnumerable{System.Type})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.TypeCatalog" /> class with the specified types.</summary>
      <param name="types">A collection of attributed <see cref="T:System.Type" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.TypeCatalog" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="types" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="types" /> contains an element that is null.-or-<paramref name="types" /> contains an element that was loaded in the reflection-only context.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.TypeCatalog.#ctor(System.Collections.Generic.IEnumerable{System.Type},System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.TypeCatalog" /> class with the specified types and source for parts.</summary>
      <param name="types">A collection of attributed <see cref="T:System.Type" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.TypeCatalog" /> object.</param>
      <param name="definitionOrigin">An element used by diagnostics to identify the source for parts.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="types" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="types" /> contains an element that is null.-or-<paramref name="types" /> contains an element that was loaded in the reflection-only context.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.TypeCatalog.#ctor(System.Collections.Generic.IEnumerable{System.Type},System.Reflection.ReflectionContext)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.TypeCatalog" /> class with the specified types in the specified reflection context.</summary>
      <param name="types">A collection of attributed <see cref="T:System.Type" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.TypeCatalog" /> object.</param>
      <param name="reflectionContext">The context used to interpret the types.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="types" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="types" /> contains an element that is null.-or-<paramref name="types" /> contains an element that was loaded in the reflection-only context.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.TypeCatalog.#ctor(System.Collections.Generic.IEnumerable{System.Type},System.Reflection.ReflectionContext,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.TypeCatalog" /> class with the specified types in the specified reflection context and source for parts.</summary>
      <param name="types">A collection of attributed <see cref="T:System.Type" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.TypeCatalog" /> object.</param>
      <param name="reflectionContext">The context used to interpret the types.</param>
      <param name="definitionOrigin">An element used by diagnostics to identify the source for parts.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="types" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="types" /> contains an element that is null.-or-<paramref name="types" /> contains an element that was loaded in the reflection-only context.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.TypeCatalog.#ctor(System.Type[])">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Hosting.TypeCatalog" /> class with the specified types.</summary>
      <param name="types">An array of attributed <see cref="T:System.Type" /> objects to add to the <see cref="T:System.ComponentModel.Composition.Hosting.TypeCatalog" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="types" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="types" /> contains an element that is null.-or-<paramref name="types" /> contains an element that was loaded in the reflection-only context.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.TypeCatalog.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Composition.Hosting.TypeCatalog" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.TypeCatalog.GetEnumerator">
      <summary>Returns an enumerator that iterates through the catalog.</summary>
      <returns>An enumerator that can be used to iterate through the catalog.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.TypeCatalog.System#ComponentModel#Composition#Primitives#ICompositionElement#DisplayName">
      <summary>Gets the display name of the type catalog.</summary>
      <returns>A string containing a human-readable display name of the <see cref="T:System.ComponentModel.Composition.Hosting.TypeCatalog" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.TypeCatalog.System#ComponentModel#Composition#Primitives#ICompositionElement#Origin">
      <summary>Gets the composition element from which the type catalog originated.</summary>
      <returns>Always null. </returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.TypeCatalog.ToString">
      <summary>Returns a string representation of the type catalog.</summary>
      <returns>A string representation of the type catalog.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.AggregateCatalog.Parts">
      <summary>Gets the part definitions that are contained in the catalog.</summary>
      <returns>The <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects that are contained in the <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" /> object.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.AggregateCatalog" /> has been disposed of.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.AssemblyCatalog.Parts">
      <summary>Gets the part definitions that are contained in the assembly catalog.</summary>
      <returns>The <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects contained in the <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.AssemblyCatalog" /> object has been disposed of.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.DirectoryCatalog.Parts">
      <summary>Gets the part definitions that are contained in the directory catalog.</summary>
      <returns>The objects that are contained in the directory catalog.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.DirectoryCatalog" /> object has been disposed.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Hosting.TypeCatalog.GetExports(System.ComponentModel.Composition.Primitives.ImportDefinition)">
      <summary>Gets a collection of exports that match the conditions specified by the import definition.</summary>
      <returns>A collection of exports that match the conditions specified by <paramref name="definition" />.</returns>
      <param name="definition">Conditions that specify which exports to match.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.Hosting.TypeCatalog.Parts">
      <summary>Gets the part definitions that are contained in the catalog.</summary>
      <returns>The <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects that are contained in the <see cref="T:System.ComponentModel.Composition.Hosting.TypeCatalog" /> object.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Hosting.TypeCatalog" /> object has been disposed of.</exception>
    </member>
    <member name="T:System.ComponentModel.Composition.Primitives.ComposablePart">
      <summary>Defines the abstract base class for composable parts, which import objects and produce exported objects.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePart.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePart.Activate">
      <summary>Called when all the imports of the part have been set, and exports can be retrieved.</summary>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ComposablePart.ExportDefinitions">
      <summary>Gets a collection of the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> objects that describe the exported objects provided by the part.</summary>
      <returns>A collection of <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> objects that describe the exported objects provided by the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object has been disposed of.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePart.GetExportedValue(System.ComponentModel.Composition.Primitives.ExportDefinition)">
      <summary>Gets the exported object described by the specified <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> object.</summary>
      <returns>The exported object described by <paramref name="definition" />.</returns>
      <param name="definition">One of the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> objects from the <see cref="P:System.ComponentModel.Composition.Primitives.ComposablePart.ExportDefinitions" /> property that describes the exported object to return.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object has been disposed of.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="definition" /> is null.</exception>
      <exception cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException">An error occurred getting the exported object described by the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="definition" /> did not originate from the <see cref="P:System.ComponentModel.Composition.Primitives.ComposablePart.ExportDefinitions" /> property on the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" />.</exception>
      <exception cref="T:System.InvalidOperationException">One or more prerequisite imports, indicated by <see cref="P:System.ComponentModel.Composition.Primitives.ImportDefinition.IsPrerequisite" />, have not been set.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ComposablePart.ImportDefinitions">
      <summary>Gets a collection of the <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> objects that describe the imported objects required by the part.</summary>
      <returns>A collection of <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> objects that describe the imported objects required by the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object has been disposed of.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ComposablePart.Metadata">
      <summary>Gets the metadata of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object.</summary>
      <returns>The metadata of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object. The default is an empty, read-only <see cref="T:System.Collections.Generic.IDictionary`2" /> object. </returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object has been disposed of.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePart.SetImport(System.ComponentModel.Composition.Primitives.ImportDefinition,System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Primitives.Export})">
      <summary>Sets the import described by the specified <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> object to be satisfied by the specified exports.</summary>
      <param name="definition">One of the objects from the <see cref="P:System.ComponentModel.Composition.Primitives.ComposablePart.ImportDefinitions" /> property that specifies the import to be set.</param>
      <param name="exports">A collection of <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects of which to set the import described by <paramref name="definition" />.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object has been disposed of.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="definition" /> is null. -or- <paramref name="exports" /> is null.</exception>
      <exception cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException">An error occurred setting the import described by the <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> object.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="definition" /> did not originate from the <see cref="P:System.ComponentModel.Composition.Primitives.ComposablePart.ImportDefinitions" /> property on the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" />. -or- <paramref name="exports" /> contains an element that is null. -or- <paramref name="exports" /> is empty and <see cref="P:System.ComponentModel.Composition.Primitives.ImportDefinition.Cardinality" /> is <see cref="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ExactlyOne" />. -or- <paramref name="exports" /> contains more than one element and <see cref="P:System.ComponentModel.Composition.Primitives.ImportDefinition.Cardinality" /> is <see cref="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ZeroOrOne" /> or <see cref="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ExactlyOne" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.ComponentModel.Composition.Primitives.ComposablePart.SetImport(System.ComponentModel.Composition.Primitives.ImportDefinition,System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Primitives.Export})" /> has been previously called and <see cref="P:System.ComponentModel.Composition.Primitives.ImportDefinition.IsRecomposable" /> is false.</exception>
    </member>
    <member name="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog">
      <summary>Represents the abstract base class for composable part catalogs, which collect and return <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartCatalog.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartCatalog.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" />.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartCatalog.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> and optionally releases the managed resources. </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartCatalog.GetEnumerator">
      <summary>Returns an enumerator that iterates through the catalog.</summary>
      <returns>An enumerator that can be used to iterate through the catalog.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartCatalog.GetExports(System.ComponentModel.Composition.Primitives.ImportDefinition)">
      <summary>Gets a list of export definitions that match the constraint defined by the specified <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> object.</summary>
      <returns>A collection of <see cref="T:System.Tuple`2" /> containing the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> objects and their associated <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> objects for objects that match the constraint specified by <paramref name="definition" />.</returns>
      <param name="definition">The conditions of the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> objects to be returned.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> object has been disposed of.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="definition" /> is null.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ComposablePartCatalog.Parts">
      <summary>Gets the part definitions that are contained in the catalog.</summary>
      <returns>The <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> contained in the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartCatalog" /> object has been disposed of.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartCatalog.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the catalog.</summary>
      <returns>An enumerator that can be used to iterate through the catalog.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition">
      <summary>Defines an abstract base class for composable part definitions, which describe and enable the creation of <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> objects.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartDefinition.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartDefinition.CreatePart">
      <summary>Creates a new instance of a part that the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> describes.</summary>
      <returns>The created part.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ComposablePartDefinition.ExportDefinitions">
      <summary>Gets a collection of <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> objects that describe the objects exported by the part defined by this <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> object.</summary>
      <returns>A collection of <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> objects that describe the exported objects provided by <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> objects created by the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ComposablePartDefinition.ImportDefinitions">
      <summary>Gets a collection of <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> objects that describe the imports required by the part defined by this <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> object.</summary>
      <returns>A collection of <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> objects that describe the imports required by <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> objects created by the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ComposablePartDefinition.Metadata">
      <summary>Gets a collection of the metadata for this <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" /> object.</summary>
      <returns>A collection that contains the metadata for the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartDefinition" />. The default is an empty, read-only <see cref="T:System.Collections.Generic.IDictionary`2" /> object.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.Primitives.ComposablePartException">
      <summary>The exception that is thrown when an error occurs when calling methods on a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" /> class with the specified serialization data.</summary>
      <param name="info">An object that holds the serialized object data for the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" />.</param>
      <param name="context">An object that contains contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is null.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">
        <paramref name="info" /> is missing a required value.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="info" /> contains a value that cannot be cast to the correct type.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" /> class with the specified error message.</summary>
      <param name="message">A message that describes the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" />, or null to set the <see cref="P:System.Exception.Message" /> property to its default value.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartException.#ctor(System.String,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" /> class with the specified error message and the composition element that is the cause of the exception.</summary>
      <param name="message">A message that describes the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" />, or null to set the <see cref="P:System.Exception.Message" /> property to its default value.</param>
      <param name="element">The composition element that is the cause of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" />, or null to set the <see cref="P:System.ComponentModel.Composition.Primitives.ComposablePartException.Element" /> property to null.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartException.#ctor(System.String,System.ComponentModel.Composition.Primitives.ICompositionElement,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" /> class with the specified error message, and the composition element and exception that are the cause of this exception.</summary>
      <param name="message">A message that describes the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" />, or null to set the <see cref="P:System.Exception.Message" /> property to its default value.</param>
      <param name="element">The composition element that is the cause of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" />, or null to set the <see cref="P:System.ComponentModel.Composition.Primitives.ComposablePartException.Element" /> property to null.</param>
      <param name="innerException">The exception that is the underlying cause of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" />, or null to set the <see cref="P:System.Exception.InnerException" /> property to null.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" /> class with the specified error message and the exception that is the cause of this exception.</summary>
      <param name="message">A message that describes the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" />, or null to set the <see cref="P:System.Exception.Message" /> property to its default value.</param>
      <param name="innerException">The exception that is the underlying cause of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" />, or null to set the <see cref="P:System.Exception.InnerException" /> property to null.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ComposablePartException.Element">
      <summary>Gets the composition element that is the cause of the exception.</summary>
      <returns>The compositional element that is the cause of the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" />. The default is null.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ComposablePartException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Gets the serialization data for the exception.</summary>
      <param name="info">After calling the method, contains serialized object data about the <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePartException" />.</param>
      <param name="context">After calling the method, contains contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is null.</exception>
    </member>
    <member name="T:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition">
      <summary>Represents an import that is required by a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object and that can specify both a contract name and metadata.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition.#ctor(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Type}},System.ComponentModel.Composition.Primitives.ImportCardinality,System.Boolean,System.Boolean,System.ComponentModel.Composition.CreationPolicy)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition" /> class with the specified contract name, required type identity, required metadata, cardinality, and creation policy, and indicates whether the import definition is recomposable or a prerequisite.</summary>
      <param name="contractName">The contract name of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> object required by the import definition.</param>
      <param name="requiredTypeIdentity">The type identity of the export type expected. Use the <see cref="M:System.ComponentModel.Composition.AttributedModelServices.GetTypeIdentity(System.Type)" /> method to generate a type identity for a given type. If no specific type is required, use null.</param>
      <param name="requiredMetadata">A collection of key/value pairs that contain the metadata names and types required by the import definition; or null to set the <see cref="P:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition.RequiredMetadata" /> property to an empty <see cref="T:System.Collections.Generic.IEnumerable`1" /> collection.</param>
      <param name="cardinality">One of the enumeration values that indicates the cardinality of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects required by the import definition.</param>
      <param name="isRecomposable">true to specify that the import definition can be satisfied multiple times throughout the lifetime of a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" />; otherwise, false.</param>
      <param name="isPrerequisite">true to specify that the import definition is required to be satisfied before a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> can start producing exported objects; otherwise, false.</param>
      <param name="requiredCreationPolicy">A value that indicates that the importer requires a specific creation policy for the exports used to satisfy this import. If no specific creation policy is needed, the default is <see cref="F:System.ComponentModel.Composition.CreationPolicy.Any" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="contractName" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="contractName" /> is an empty string ("").-or-<paramref name="requiredMetadata" /> contains an element that is null.-or-<paramref name="cardinality" /> is not one of the <see cref="T:System.ComponentModel.Composition.Primitives.ImportCardinality" /> values.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition.#ctor(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Type}},System.ComponentModel.Composition.Primitives.ImportCardinality,System.Boolean,System.Boolean,System.ComponentModel.Composition.CreationPolicy,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition" /> class with the specified contract name, required type identity, required and optional metadata, cardinality, and creation policy, and indicates whether the import definition is recomposable or a prerequisite.</summary>
      <param name="contractName">The contract name of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> object required by the import definition.</param>
      <param name="requiredTypeIdentity">The type identity of the export type expected. Use the <see cref="M:System.ComponentModel.Composition.AttributedModelServices.GetTypeIdentity(System.Type)" /> method to generate a type identity for a given type. If no specific type is required, use null.</param>
      <param name="requiredMetadata">A collection of key/value pairs that contain the metadata names and types required by the import definition; or null to set the <see cref="P:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition.RequiredMetadata" /> property to an empty <see cref="T:System.Collections.Generic.IEnumerable`1" /> collection.</param>
      <param name="cardinality">One of the enumeration values that indicates the cardinality of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects required by the import definition.</param>
      <param name="isRecomposable">true to specify that the import definition can be satisfied multiple times throughout the lifetime of a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" />; otherwise, false.</param>
      <param name="isPrerequisite">true to specify that the import definition is required to be satisfied before a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> can start producing exported objects; otherwise, false.</param>
      <param name="requiredCreationPolicy">A value that indicates that the importer requires a specific creation policy for the exports used to satisfy this import. If no specific creation policy is needed, the default is <see cref="F:System.ComponentModel.Composition.CreationPolicy.Any" />.</param>
      <param name="metadata">The metadata associated with this import.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="contractName" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="contractName" /> is an empty string ("").-or-<paramref name="requiredMetadata" /> contains an element that is null.-or-<paramref name="cardinality" /> is not one of the <see cref="T:System.ComponentModel.Composition.Primitives.ImportCardinality" /> values.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition.Constraint">
      <summary>Gets an expression that defines conditions that must be matched to satisfy the import described by this import definition.</summary>
      <returns>An expression that contains a <see cref="T:System.Func`2" /> object that defines the conditions that must be matched for the <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> to be satisfied by an <see cref="T:System.ComponentModel.Composition.Primitives.Export" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition.IsConstraintSatisfiedBy(System.ComponentModel.Composition.Primitives.ExportDefinition)">
      <summary>Returns a value indicating whether the constraint represented by this object is satisfied by the export represented by the given export definition.</summary>
      <returns>true if the constraint is satisfied; otherwise, false.</returns>
      <param name="exportDefinition">The export definition to test.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition.RequiredCreationPolicy">
      <summary>Gets or sets a value that indicates that the importer requires a specific <see cref="T:System.ComponentModel.Composition.CreationPolicy" /> for the exports used to satisfy this import. </summary>
      <returns>One of the following values:<see cref="F:System.ComponentModel.Composition.CreationPolicy.Any" />, if the importer does not require a specific <see cref="T:System.ComponentModel.Composition.CreationPolicy" />.<see cref="F:System.ComponentModel.Composition.CreationPolicy.Shared" /> to require that all exports used should be shared by all importers in the container.<see cref="F:System.ComponentModel.Composition.CreationPolicy.NonShared" /> to require that all exports used should be non-shared in the container. In this case, each importer receives a separate instance.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition.RequiredMetadata">
      <summary>Gets the metadata names of the export required by the import definition.</summary>
      <returns>A collection of <see cref="T:System.String" /> objects that contain the metadata names of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects required by the <see cref="T:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition" />. The default is an empty <see cref="T:System.Collections.Generic.IEnumerable`1" /> collection.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition.RequiredTypeIdentity">
      <summary>Gets the expected type of the export that matches this <see cref="T:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition" />.</summary>
      <returns>A string that is generated by calling the <see cref="M:System.ComponentModel.Composition.AttributedModelServices.GetTypeIdentity(System.Type)" /> method on the type that this import expects. If the value is null, this import does not expect a particular type.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ContractBasedImportDefinition.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.Primitives.Export">
      <summary>Represents an export, which is a type that consists of a delay-created exported object and the metadata that describes that object.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.Export.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.Export.#ctor(System.ComponentModel.Composition.Primitives.ExportDefinition,System.Func{System.Object})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> class with the specified export definition and exported object getter.</summary>
      <param name="definition">An object that describes the contract that the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> object satisfies.</param>
      <param name="exportedValueGetter">A method that is called to create the exported object of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" />. This delays the creation of the object until the <see cref="P:System.ComponentModel.Composition.Primitives.Export.Value" /> property is called. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="definition" /> is null.-or-<paramref name="exportedObjectGetter" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.Export.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Func{System.Object})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> class with the specified contract name, metadata, and exported value getter.</summary>
      <param name="contractName">The contract name of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> object.</param>
      <param name="metadata">The metadata of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> object or null to set the <see cref="P:System.ComponentModel.Composition.Primitives.Export.Metadata" /> property to an empty, read-only <see cref="T:System.Collections.Generic.IDictionary`2" /> object.</param>
      <param name="exportedValueGetter">A method that is called to create the exported object of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" />. This delays the creation of the object until the <see cref="P:System.ComponentModel.Composition.Primitives.Export.Value" /> method is called.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="contractName" /> is null.-or-<paramref name="exportedObjectGetter" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="contractName" /> is an empty string ("").</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.Export.#ctor(System.String,System.Func{System.Object})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> class with the specified contract name and exported value getter.</summary>
      <param name="contractName">The contract name of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> object.</param>
      <param name="exportedValueGetter">A method that is called to create the exported object of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" />. This delays the creation of the object until the <see cref="P:System.ComponentModel.Composition.Primitives.Export.Value" /> method is called.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="contractName" /> is null.-or-<paramref name="exportedObjectGetter" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="contractName" /> is an empty string ("").</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.Export.Definition">
      <summary>Gets the definition that describes the contract that the export satisfies.</summary>
      <returns>A definition that describes the contract that the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> object satisfies.</returns>
      <exception cref="T:System.NotImplementedException">This property was not overridden by a derived class.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.Export.GetExportedValueCore">
      <summary>Returns the exported object the export provides.</summary>
      <returns>The exported object the export provides.</returns>
      <exception cref="T:System.NotImplementedException">The <see cref="M:System.ComponentModel.Composition.Primitives.Export.GetExportedValueCore" /> method was not overridden by a derived class.</exception>
      <exception cref="T:System.ComponentModel.Composition.CompositionException">An error occurred during composition. <see cref="P:System.ComponentModel.Composition.CompositionException.Errors" /> will contain a collection of errors that occurred.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.Export.Metadata">
      <summary>Gets the metadata for the export.</summary>
      <returns>The metadata of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" />.</returns>
      <exception cref="T:System.NotImplementedException">The <see cref="P:System.ComponentModel.Composition.Primitives.Export.Definition" /> property was not overridden by a derived class.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.Export.Value">
      <summary>Provides the object this export represents.</summary>
      <returns>The object this export represents.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.Primitives.ExportDefinition">
      <summary>Describes the contract that a particular <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> object satisfies.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ExportDefinition.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ExportDefinition.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> class with the specified contract name and metadata.</summary>
      <param name="contractName">The contract name of the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> object.</param>
      <param name="metadata">The metadata of the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> or null to set the <see cref="P:System.ComponentModel.Composition.Primitives.ExportDefinition.Metadata" /> property to an empty, read-only <see cref="T:System.Collections.Generic.IDictionary`2" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="contractName" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="contractName" /> is an empty string ("").</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ExportDefinition.ContractName">
      <summary>Gets the contract name.</summary>
      <returns>The contract name of the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" /> object.</returns>
      <exception cref="T:System.NotImplementedException">The property was not overridden by a derived class.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ExportDefinition.Metadata">
      <summary>Gets the contract metadata.</summary>
      <returns>The metadata of the <see cref="T:System.ComponentModel.Composition.Primitives.ExportDefinition" />. The default is an empty, read-only <see cref="T:System.Collections.Generic.IDictionary`2" /> object.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ExportDefinition.ToString">
      <summary>Returns a string representation of the export definition.</summary>
      <returns>A string representation of the export definition.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.Primitives.ExportedDelegate">
      <summary>Represents a function exported by a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" />.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ExportedDelegate.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ExportedDelegate" /> class. </summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ExportedDelegate.#ctor(System.Object,System.Reflection.MethodInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ExportedDelegate" /> class for the specified part and method. </summary>
      <param name="instance">The part exporting the method.</param>
      <param name="method">The method to be exported.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ExportedDelegate.CreateDelegate(System.Type)">
      <summary>Gets a delegate of the specified type.</summary>
      <returns>A delegate of the specified type, or null if no such delegate can be created.</returns>
      <param name="delegateType">The type of the delegate to return.</param>
    </member>
    <member name="T:System.ComponentModel.Composition.Primitives.ICompositionElement">
      <summary>Represents an element that participates in composition.</summary>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ICompositionElement.DisplayName">
      <summary>Gets the display name of the composition element.</summary>
      <returns>The human-readable display name of the <see cref="T:System.ComponentModel.Composition.Primitives.ICompositionElement" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ICompositionElement.Origin">
      <summary>Gets the composition element from which the current composition element originated.</summary>
      <returns>The composition element from which the current <see cref="T:System.ComponentModel.Composition.Primitives.ICompositionElement" /> originated, or null if the <see cref="T:System.ComponentModel.Composition.Primitives.ICompositionElement" /> is the root composition element.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.Primitives.ImportCardinality">
      <summary>Indicates the cardinality of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects required by an <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" />.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ZeroOrOne">
      <summary>Zero or one <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects are required by the <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" />.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ExactlyOne">
      <summary>Exactly one <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> object is required by the <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" />.</summary>
    </member>
    <member name="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ZeroOrMore">
      <summary>Zero or more <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects are required by the <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" />.</summary>
    </member>
    <member name="T:System.ComponentModel.Composition.Primitives.ImportDefinition">
      <summary>Represents an import that is required by a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ImportDefinition.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ImportDefinition.#ctor(System.Linq.Expressions.Expression{System.Func{System.ComponentModel.Composition.Primitives.ExportDefinition,System.Boolean}},System.String,System.ComponentModel.Composition.Primitives.ImportCardinality,System.Boolean,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> class with the specified constraint, contract name, and cardinality, and indicates whether the import definition is recomposable or a prerequisite.</summary>
      <param name="constraint">An expression that contains a <see cref="T:System.Func`2" /> object that defines the conditions an <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> must match to satisfy the import definition.</param>
      <param name="contractName">The contract name.</param>
      <param name="cardinality">One of the enumeration values that indicates the cardinality of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects required by the import definition.</param>
      <param name="isRecomposable">true to specify that the import definition can be satisfied multiple times throughout the lifetime of a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object; otherwise, false.</param>
      <param name="isPrerequisite">true to specify that the import definition must be satisfied before a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> can start producing exported objects; otherwise, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="constraint" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="cardinality" /> is not one of the values of <see cref="T:System.ComponentModel.Composition.Primitives.ImportCardinality" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ImportDefinition.#ctor(System.Linq.Expressions.Expression{System.Func{System.ComponentModel.Composition.Primitives.ExportDefinition,System.Boolean}},System.String,System.ComponentModel.Composition.Primitives.ImportCardinality,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> class with the specified constraint, contract name, cardinality, and metadata, and indicates whether the import definition is recomposable or a prerequisite.</summary>
      <param name="constraint">An expression that contains a <see cref="T:System.Func`2" /> object that defines the conditions an <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> must match to satisfy the import definition.</param>
      <param name="contractName">The contract name.</param>
      <param name="cardinality">One of the enumeration values that indicates the cardinality of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects required by the import definition.</param>
      <param name="isRecomposable">true to specify that the import definition can be satisfied multiple times throughout the lifetime of a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object; otherwise, false.</param>
      <param name="isPrerequisite">true to specify that the import definition must be satisfied before a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> can start producing exported objects; otherwise, false.</param>
      <param name="metadata">The metadata associated with the import.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ImportDefinition.Cardinality">
      <summary>Gets the cardinality of the exports required by the import definition.</summary>
      <returns>One of the enumeration values that indicates the cardinality of the <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> objects required by the <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" />. The default is <see cref="F:System.ComponentModel.Composition.Primitives.ImportCardinality.ExactlyOne" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ImportDefinition.Constraint">
      <summary>Gets an expression that defines conditions that the import must satisfy to match the import definition.</summary>
      <returns>An expression that contains a <see cref="T:System.Func`2" /> object that defines the conditions an <see cref="T:System.ComponentModel.Composition.Primitives.Export" /> must satisfy to match the <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" />.</returns>
      <exception cref="T:System.NotImplementedException">The property was not overridden by a derived class.</exception>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ImportDefinition.ContractName">
      <summary>Gets the name of the contract.</summary>
      <returns>The contract name.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ImportDefinition.IsConstraintSatisfiedBy(System.ComponentModel.Composition.Primitives.ExportDefinition)">
      <summary>Gets a value that indicates whether the export represented by the specified definition satisfies the constraints of this import definition.</summary>
      <returns>true if the constraints are satisfied; otherwise, false.</returns>
      <param name="exportDefinition">The export definition to test.</param>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ImportDefinition.IsPrerequisite">
      <summary>Gets a value that indicates whether the import definition must be satisfied before a part can start producing exported objects.</summary>
      <returns>true if the <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> must be satisfied before a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object can start producing exported objects; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ImportDefinition.IsRecomposable">
      <summary>Gets a value that indicates whether the import definition can be satisfied multiple times.</summary>
      <returns>true if the <see cref="T:System.ComponentModel.Composition.Primitives.ImportDefinition" /> can be satisfied multiple times throughout the lifetime of a <see cref="T:System.ComponentModel.Composition.Primitives.ComposablePart" /> object; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.ComponentModel.Composition.Primitives.ImportDefinition.Metadata">
      <summary>Gets the metadata associated with this import.</summary>
      <returns>A collection that contains the metadata associated with this import.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Primitives.ImportDefinition.ToString">
      <summary>Returns a string representation of the import definition.</summary>
      <returns>A string representation of the import definition.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo">
      <summary>Represents a <see cref="T:System.Reflection.MemberInfo" /> object that does not load assemblies or create objects until requested.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo.#ctor(System.Reflection.MemberInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo" /> class, representing the specified member.</summary>
      <param name="member">The member to represent.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo.#ctor(System.Reflection.MemberTypes,System.Func{System.Reflection.MemberInfo[]})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo" /> class for a member of the specified type with the specified accessors.</summary>
      <param name="memberType">The type of the represented member.</param>
      <param name="accessorsCreator">A function whose return value is a collection of the accessors for the represented member.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo.#ctor(System.Reflection.MemberTypes,System.Reflection.MemberInfo[])">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo" /> class for a member of the specified type with the specified accessors.</summary>
      <param name="memberType">The type of the represented member.</param>
      <param name="accessors">An array of the accessors for the represented member.</param>
      <exception cref="T:System.ArgumentException">One or more of the objects in <paramref name="accessors" /> are not valid accessors for this member.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo.Equals(System.Object)">
      <param name="obj">The other object.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo.GetAccessors">
      <summary>Gets an array of the accessors for the represented member.</summary>
      <returns>An array of the accessors for the represented member.</returns>
      <exception cref="T:System.ArgumentException">One or more of the accessors in this object are invalid.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo.GetHashCode"></member>
    <member name="P:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo.MemberType">
      <summary>Gets the type of the represented member.</summary>
      <returns>The type of the represented member.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo.op_Equality(System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo,System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo)">
      <summary>Determines whether the two specified <see cref="T:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo" /> objects are equal.</summary>
      <returns>true if the objects are equal; otherwise, false.</returns>
      <param name="left">The first object to test.</param>
      <param name="right">The second object to test.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo.op_Inequality(System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo,System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo)">
      <summary>Determines whether the two specified <see cref="T:System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo" /> objects are not equal.</summary>
      <returns>true if the objects are equal; otherwise, false.</returns>
      <param name="left">The first object to test.</param>
      <param name="right">The second object to test.</param>
    </member>
    <member name="T:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices">
      <summary>Provides extension methods to create and retrieve reflection-based parts.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.CreateExportDefinition(System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo,System.String,System.Lazy{System.Collections.Generic.IDictionary{System.String,System.Object}},System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Creates an export definition from the specified member, with the specified contract name, metadata, and origin.</summary>
      <returns>An export definition created from the specified parameters.</returns>
      <param name="exportingMember">The member to export.</param>
      <param name="contractName">The contract name to use for the export.</param>
      <param name="metadata">The metadata for the export.</param>
      <param name="origin">The object that the export originates from.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.CreateImportDefinition(System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo,System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Type}},System.ComponentModel.Composition.Primitives.ImportCardinality,System.Boolean,System.Boolean,System.ComponentModel.Composition.CreationPolicy,System.Collections.Generic.IDictionary{System.String,System.Object},System.Boolean,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Creates an import definition for the specified member by using the specified contract name, type identity, import and contract metadata, cardinality, recomposition policy, and creation policy.</summary>
      <returns>An import definition created from the specified parameters.</returns>
      <param name="importingMember">The member to import into.</param>
      <param name="contractName">The contract name to use for the import.</param>
      <param name="requiredTypeIdentity">The required type identity for the import.</param>
      <param name="requiredMetadata">The required metadata for the import.</param>
      <param name="cardinality">The cardinality of the import.</param>
      <param name="isRecomposable">true to indicate that the import is recomposable; otherwise, false.</param>
      <param name="isPreRequisite">true to indicate that the import is a prerequisite; otherwise, false.</param>
      <param name="requiredCreationPolicy">One of the enumeration values that specifies the import's creation policy.</param>
      <param name="metadata">The contract metadata.</param>
      <param name="isExportFactory">true to indicate that the import represents an <see cref="T:System.ComponentModel.Composition.ExportFactory`1" />; otherwise, false.</param>
      <param name="origin">The object to import into.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.CreateImportDefinition(System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo,System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Type}},System.ComponentModel.Composition.Primitives.ImportCardinality,System.Boolean,System.ComponentModel.Composition.CreationPolicy,System.Collections.Generic.IDictionary{System.String,System.Object},System.Boolean,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Creates an import definition for the specified member by using the specified contract name, type identity, import and contract metadata, cardinality, recomposition policy, and creation policy.</summary>
      <returns>An import definition created from the specified parameters.</returns>
      <param name="importingMember">The member to import into.</param>
      <param name="contractName">The contract name to use for the import.</param>
      <param name="requiredTypeIdentity">The required type identity for the import.</param>
      <param name="requiredMetadata">The required metadata for the import.</param>
      <param name="cardinality">The cardinality of the import.</param>
      <param name="isRecomposable">true to indicate that the import is recomposable; otherwise, false.</param>
      <param name="requiredCreationPolicy">One of the enumeration values that specifies the import's creation policy.</param>
      <param name="metadata">The contract metadata.</param>
      <param name="isExportFactory">true to indicate that the import represents an <see cref="T:System.ComponentModel.Composition.ExportFactory`1" />; otherwise, false.</param>
      <param name="origin">The object to import into.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.CreateImportDefinition(System.ComponentModel.Composition.ReflectionModel.LazyMemberInfo,System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Type}},System.ComponentModel.Composition.Primitives.ImportCardinality,System.Boolean,System.ComponentModel.Composition.CreationPolicy,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Creates an import definition for the specified member by using the specified contract name, type identity, import metadata, cardinality, recomposition policy, and creation policy.</summary>
      <returns>An import definition created from the specified parameters.</returns>
      <param name="importingMember">The member to import into.</param>
      <param name="contractName">The contract name to use for the import.</param>
      <param name="requiredTypeIdentity">The required type identity for the import.</param>
      <param name="requiredMetadata">The required metadata for the import.</param>
      <param name="cardinality">The cardinality of the import.</param>
      <param name="isRecomposable">true to indicate that the import is recomposable; otherwise, false.</param>
      <param name="requiredCreationPolicy">One of the enumeration values that specifies the import's creation policy.</param>
      <param name="origin">The object to import into.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.CreateImportDefinition(System.Lazy{System.Reflection.ParameterInfo},System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Type}},System.ComponentModel.Composition.Primitives.ImportCardinality,System.ComponentModel.Composition.CreationPolicy,System.Collections.Generic.IDictionary{System.String,System.Object},System.Boolean,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Creates an import definition for the specified parameter by using the specified contract name, type identity, import and contract metadata, cardinality, and creation policy.</summary>
      <returns>An import definition created from the specified parameters.</returns>
      <param name="parameter">The parameter to import.</param>
      <param name="contractName">The contract name to use for the import.</param>
      <param name="requiredTypeIdentity">The required type identity for the import.</param>
      <param name="requiredMetadata">The required metadata for the import.</param>
      <param name="cardinality">The cardinality of the import.</param>
      <param name="requiredCreationPolicy">One of the enumeration values that specifies the import's creation policy.</param>
      <param name="metadata">The contract metadata</param>
      <param name="isExportFactory">true to indicate that the import represents an <see cref="T:System.ComponentModel.Composition.ExportFactory`1" />; otherwise, false.</param>
      <param name="origin">The object to import into.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.CreateImportDefinition(System.Lazy{System.Reflection.ParameterInfo},System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Type}},System.ComponentModel.Composition.Primitives.ImportCardinality,System.ComponentModel.Composition.CreationPolicy,System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Creates an import definition for the specified parameter by using the specified contract name, type identity, import metadata, cardinality, and creation policy.</summary>
      <returns>An import definition created from the specified parameters.</returns>
      <param name="parameter">The parameter to import.</param>
      <param name="contractName">The contract name to use for the import.</param>
      <param name="requiredTypeIdentity">The required type identity for the import.</param>
      <param name="requiredMetadata">The required metadata for the import.</param>
      <param name="cardinality">The cardinality of the import.</param>
      <param name="requiredCreationPolicy">One of the enumeration values that specifies the import's creation policy.</param>
      <param name="origin">The object to import into.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.CreatePartDefinition(System.Lazy{System.Type},System.Boolean,System.Lazy{System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Primitives.ImportDefinition}},System.Lazy{System.Collections.Generic.IEnumerable{System.ComponentModel.Composition.Primitives.ExportDefinition}},System.Lazy{System.Collections.Generic.IDictionary{System.String,System.Object}},System.ComponentModel.Composition.Primitives.ICompositionElement)">
      <summary>Creates a part definition with the specified part type, imports, exports, metadata, and origin.</summary>
      <returns>A part definition created from the specified parameters.</returns>
      <param name="partType">The type of the part.</param>
      <param name="isDisposalRequired">true if the part requires disposal; otherwise, false.</param>
      <param name="imports">A collection of the part's imports.</param>
      <param name="exports">A collection of the part's exports.</param>
      <param name="metadata">The part's metadata.</param>
      <param name="origin">The part's origin.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.GetExportFactoryProductImportDefinition(System.ComponentModel.Composition.Primitives.ImportDefinition)">
      <summary>Returns a representation of an import definition as an export factory product.</summary>
      <returns>The representation of the import definition.</returns>
      <param name="importDefinition">The import definition to represent.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.GetExportingMember(System.ComponentModel.Composition.Primitives.ExportDefinition)">
      <summary>Gets the exporting member from a specified export definition.</summary>
      <returns>The member specified in the export definition.</returns>
      <param name="exportDefinition">The export definition to examine.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="exportDefinition" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.GetImportingMember(System.ComponentModel.Composition.Primitives.ImportDefinition)">
      <summary>Gets the importing member from a specified import definition.</summary>
      <returns>The member specified in the import definition.</returns>
      <param name="importDefinition">The import definition to examine.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="importDefinition" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.GetImportingParameter(System.ComponentModel.Composition.Primitives.ImportDefinition)">
      <summary>Gets the importing parameter from a specified import definition.</summary>
      <returns>The parameter specified in the import definition.</returns>
      <param name="importDefinition">The import definition to examine.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="importDefinition" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.GetPartType(System.ComponentModel.Composition.Primitives.ComposablePartDefinition)">
      <summary>Gets the type of a part from a specified part definition.</summary>
      <returns>The type of the defined part.</returns>
      <param name="partDefinition">The part definition to examine.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="partDefinition" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.IsDisposalRequired(System.ComponentModel.Composition.Primitives.ComposablePartDefinition)">
      <summary>Determines whether the specified part requires disposal.</summary>
      <returns>true if the part requires disposal; otherwise, false.</returns>
      <param name="partDefinition">The part to examine.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="partDefinition" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.IsExportFactoryImportDefinition(System.ComponentModel.Composition.Primitives.ImportDefinition)">
      <summary>Indicates whether a specified import definition represents an export factory (<see cref="T:System.ComponentModel.Composition.ExportFactory`1" /> or <see cref="T:System.ComponentModel.Composition.ExportFactory`2" /> object).</summary>
      <returns>true if the specified import definition represents an export factory; otherwise, false.</returns>
      <param name="importDefinition">The import definition to check.</param>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.IsImportingParameter(System.ComponentModel.Composition.Primitives.ImportDefinition)">
      <summary>Determines whether an import definition represents a member or a parameter.</summary>
      <returns>true if the import definition represents a parameter; otherwise, false.</returns>
      <param name="importDefinition">The import definition to examine.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="importDefinition" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.ReflectionModel.ReflectionModelServices.TryMakeGenericPartDefinition(System.ComponentModel.Composition.Primitives.ComposablePartDefinition,System.Collections.Generic.IEnumerable{System.Type},System.ComponentModel.Composition.Primitives.ComposablePartDefinition@)">
      <summary>Indicates whether a generic part definition can be specialized with the provided parameters.</summary>
      <returns>true if the specialization succeeds; otherwise, false.</returns>
      <param name="partDefinition">The part definition.</param>
      <param name="genericParameters">A collection of types to specify the generic parameters.</param>
      <param name="specialization">When this method returns, contains the specialized part definition. This parameter is treated as uninitialized.</param>
    </member>
  </members>
</doc>