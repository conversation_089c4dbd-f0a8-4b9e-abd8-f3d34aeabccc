﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IdentityModel.Services</name>
  </assembly>
  <members>
    <member name="T:System.IdentityModel.Services.ApplicationType">
      <summary>Specifies the kind of application.</summary>
    </member>
    <member name="F:System.IdentityModel.Services.ApplicationType.AspNetWebApplication">
      <summary>An ASP.NET Web application.</summary>
    </member>
    <member name="F:System.IdentityModel.Services.ApplicationType.WcfServiceApplication">
      <summary>A Windows Communication Foundation (WCF) application.</summary>
    </member>
    <member name="T:System.IdentityModel.Services.AsynchronousOperationException">
      <summary>The exception that is thrown when an error occurs during an asynchronous operation.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.AsynchronousOperationException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.AsynchronousOperationException" /> class.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.AsynchronousOperationException.#ctor(System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.AsynchronousOperationException" /> class with a system-supplied error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="innerException">The <see cref="T:System.Exception" /> that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception.</param>
    </member>
    <member name="M:System.IdentityModel.Services.AsynchronousOperationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.AsynchronousOperationException" /> class with serialized data.</summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object that holds the serialized object data.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> object that contains the contextual information about the source or destination.</param>
    </member>
    <member name="M:System.IdentityModel.Services.AsynchronousOperationException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.AsynchronousOperationException" /> class with a specified error message.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
    </member>
    <member name="M:System.IdentityModel.Services.AsynchronousOperationException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.AsynchronousOperationException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <param name="innerException">The <see cref="T:System.Exception" /> that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception.</param>
    </member>
    <member name="T:System.IdentityModel.Services.AttributeRequestMessage">
      <summary>Represents a WS-Federation Attribute Request message. This message is created when the wa parameter in the received message is “wattr1.0”.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.AttributeRequestMessage.#ctor(System.Uri)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.AttributeRequestMessage" /> class with the specified base URL.</summary>
      <param name="baseUrl">The base URL to which this message applies.</param>
    </member>
    <member name="P:System.IdentityModel.Services.AttributeRequestMessage.Attribute">
      <summary>Gets or sets the wattr parameter of the message.</summary>
      <returns>A string that contains the value of the wattr parameter.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.AttributeRequestMessage.AttributePtr">
      <summary>Gets or sets the wattrptr parameter of the message.</summary>
      <returns>A string that contains the value of the wattrptr parameter.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid URI occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.AttributeRequestMessage.Reply">
      <summary>Gets or sets the wreply parameter of the message.</summary>
      <returns>A string that contains the value of the wreply parameter. This is the URL to which the reply should be sent.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid URI occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.AttributeRequestMessage.Result">
      <summary>Gets or sets the wresult parameter of the message.</summary>
      <returns>A string that contains the value of the wresult parameter.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.AttributeRequestMessage.ResultPtr">
      <summary>Gets or sets the wresultptr parameter of the message.</summary>
      <returns>A string that contains the value of the wresultptr parameter.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid URI occurs.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.AttributeRequestMessage.Validate">
      <summary>No validation is performed by the framework. Users of this class should validate externally.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.AttributeRequestMessage.Write(System.IO.TextWriter)">
      <summary>Writes the message in query string form to the specified text writer.</summary>
      <param name="writer">The writer to which to write the message.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is null.</exception>
    </member>
    <member name="T:System.IdentityModel.Services.AuthorizationFailedEventArgs">
      <summary>Provides data for the <see cref="E:System.IdentityModel.Services.WSFederationAuthenticationModule.AuthorizationFailed" /> event.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.AuthorizationFailedEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.AuthorizationFailedEventArgs" /> class.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.AuthorizationFailedEventArgs.RedirectToIdentityProvider">
      <summary>Gets or sets a value that indicates whether the runtime (<see cref="T:System.IdentityModel.Services.WSFederationAuthenticationModule" />) should redirect to an identity provider.</summary>
      <returns>true to redirect; otherwise, false.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.ChunkedCookieHandler">
      <summary>Represents a cookie handler that writes cookie data so that cookies never exceed a set size.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.ChunkedCookieHandler.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.ChunkedCookieHandler" /> class that uses the default chunk size.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.ChunkedCookieHandler.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.ChunkedCookieHandler" /> class that uses a specified chunk size.</summary>
      <param name="chunkSize">The chunk size that will be used to write cookies.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="chunkSize" />' is less than the minimum chunk size of 1000 (<see cref="F:System.IdentityModel.Services.ChunkedCookieHandler.MinimumChunkSize" />).</exception>
    </member>
    <member name="P:System.IdentityModel.Services.ChunkedCookieHandler.ChunkSize">
      <summary>Gets the chunk size used by the current instance.</summary>
      <returns>The chunk size, in bytes.</returns>
    </member>
    <member name="F:System.IdentityModel.Services.ChunkedCookieHandler.DefaultChunkSize">
      <summary>The default chunk size of 2000 characters.</summary>
    </member>
    <member name="F:System.IdentityModel.Services.ChunkedCookieHandler.MinimumChunkSize">
      <summary>The minimum chunk size of 1000 characters.</summary>
    </member>
    <member name="T:System.IdentityModel.Services.ChunkedCookieHandlerElement">
      <summary>Represents the &lt;chunkedCookieHandler&gt; configuration element.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.ChunkedCookieHandlerElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.ChunkedCookieHandlerElement" /> class.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.ChunkedCookieHandlerElement.ChunkSize">
      <summary>Gets or sets the chunk size for the handler.</summary>
      <returns>The chunk size, in bytes. The default is <see cref="F:System.IdentityModel.Services.ChunkedCookieHandler.DefaultChunkSize" />. Do not set a value below the minimum chunk size defined by <see cref="F:System.IdentityModel.Services.ChunkedCookieHandler.MinimumChunkSize" />.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.ClaimsAuthorizationModule">
      <summary>Represents an HTTP module that performs claims-based authorization.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsAuthorizationModule.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.ClaimsAuthorizationModule" /> class.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsAuthorizationModule.Authorize">
      <summary>Returns a value that indicates whether the requestor (principal) is authorized for the current request.</summary>
      <returns>true if the principal is authorized; otherwise, false. The default is true if no claims authentication manager is associated with this module.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:System.Security.Claims.ClaimsPrincipal" /> associated with the request is not authorized.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.ClaimsAuthorizationModule.ClaimsAuthorizationManager">
      <summary>Gets or sets the current claims authorization manager instance that is used by this module.</summary>
      <returns>The claims authorization manager instance or null if no claims authorization manager is associated with this module.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set the property to null occurs.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsAuthorizationModule.Dispose">
      <summary>Disposes of the resources (other than memory) used by the module.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsAuthorizationModule.InitializeModule(System.Web.HttpApplication)">
      <summary>Initializes the module and prepares it to handle events from its ASP.NET application object.</summary>
      <param name="context">The application object that contains this module.</param>
      <exception cref="">
        <paramref name="context" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsAuthorizationModule.InitializePropertiesFromConfiguration">
      <summary>Initializes the module properties based on definitions in the configuration file.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsAuthorizationModule.OnAuthorizeRequest(System.Object,System.EventArgs)">
      <summary>Handles the HTTP pipeline <see cref="E:System.Web.HttpApplication.AuthorizeRequest" /> event</summary>
      <param name="sender">The source of the event.</param>
      <param name="args">The data for the event.</param>
    </member>
    <member name="T:System.IdentityModel.Services.ClaimsPrincipalPermission">
      <summary>Represents a permission that uses a <see cref="T:System.Security.Claims.ClaimsAuthorizationManager" /> implementation to determine if access to a resource should be granted to the active principal. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsPrincipalPermission.#ctor(System.String,System.String)">
      <summary>Creates a new instance of the <see cref="T:System.IdentityModel.Services.ClaimsPrincipalPermission" /> class.</summary>
      <param name="resource">The resource on which the principal should be authorized.</param>
      <param name="action">The action for which the principal should be authorized.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="resource" /> is null or an empty string.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsPrincipalPermission.CheckAccess(System.String,System.String)">
      <summary>Checks if the current principal is authorized to perform the specified action on the specified resource.</summary>
      <param name="resource">The resource on which the principal should be authorized.</param>
      <param name="action">The action for which the principal should be authorized.</param>
      <exception cref="T:System.InvalidOperationException">The current principal is not assignable from <see cref="T:System.Security.Claims.ClaimsPrincipal" />.-or-There is no <see cref="T:System.Security.Claims.ClaimsAuthorizationManager" /> configured.</exception>
      <exception cref="T:System.Security.SecurityException">The authorization check failed.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsPrincipalPermission.Copy">
      <summary>Returns a copy of the current <see cref="T:System.IdentityModel.Services.ClaimsPrincipalPermission" /> instance.</summary>
      <returns>A <see cref="T:System.IdentityModel.Services.ClaimsPrincipalPermission" /> copied from the current instance.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsPrincipalPermission.Demand">
      <summary>Checks if the current principal is authorized for the resource-action pairs associated with the current instance.</summary>
      <exception cref="T:System.InvalidOperationException">The current principal is not assignable from <see cref="T:System.Security.Claims.ClaimsPrincipal" />.-or-There is no <see cref="T:System.Security.Claims.ClaimsAuthorizationManager" /> configured.</exception>
      <exception cref="T:System.Security.SecurityException">The authorization check failed.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsPrincipalPermission.FromXml(System.Security.SecurityElement)">
      <summary>Reconstructs the current permission and its state from the specified XML encoding.</summary>
      <param name="element">The XML encoding to use to reconstruct the permission.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The encoding contains unrecognized elements or attributes or improperly formed XML.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsPrincipalPermission.Intersect(System.Security.IPermission)">
      <summary>Returns a permission that is the intersection of the current permission and the specified permission.</summary>
      <returns>A new <see cref="T:System.IdentityModel.Services.ClaimsPrincipalPermission" /> that represents the intersection of the current permission and the specified permission; or null if <paramref name="target" /> is <paramref name="null" /> or is not an instance of <see cref="T:System.IdentityModel.Services.ClaimsPrincipalPermission" />.</returns>
      <param name="target">The permission to intersect with the current permission. It must be an instance of <see cref="T:System.IdentityModel.Services.ClaimsPrincipalPermission" />.</param>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsPrincipalPermission.IsSubsetOf(System.Security.IPermission)">
      <summary>Returns a value that indicates whether current permission is a subset of the specified permission.</summary>
      <returns>true if current permission is a subset of the specified permission; otherwise, false.</returns>
      <param name="target">The permission to be tested for the subset relationship. It must be an instance of <see cref="T:System.IdentityModel.Services.ClaimsPrincipalPermission" />.</param>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsPrincipalPermission.IsUnrestricted">
      <summary>Returns a value that indicates whether the permission is unrestricted.</summary>
      <returns>true if the permission is unrestricted; otherwise, false. Always returns true indicating that permission is unrestricted.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsPrincipalPermission.ToXml">
      <summary>Returns the XML encoded form of the current permission and its state.</summary>
      <returns>The XML encoded form of the current permission and its state.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsPrincipalPermission.Union(System.Security.IPermission)">
      <summary>Returns a new permission that is the union of the current permission and the specified permission. <see cref="T:System.IdentityModel.Services.ClaimsPrincipalPermission" /> object that has all of the resource-action pairs that are present in the current instance and the target instance.</summary>
      <returns>A new <see cref="T:System.IdentityModel.Services.ClaimsPrincipalPermission" /> that represents the union of the current permission and the specified permission; or null if <paramref name="target" /> is <paramref name="null" /> or is not an instance of <see cref="T:System.IdentityModel.Services.ClaimsPrincipalPermission" />.</returns>
      <param name="target">The permission to combine with current permission. It must be of type <see cref="T:System.IdentityModel.Services.ClaimsPrincipalPermission" />.</param>
    </member>
    <member name="T:System.IdentityModel.Services.ClaimsPrincipalPermissionAttribute">
      <summary>Represents a security attribute used to declaratively assign access checks based on the logic provided by the <see cref="T:System.Security.Claims.ClaimsAuthorizationManager" /> instance in the current application context. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsPrincipalPermissionAttribute.#ctor(System.Security.Permissions.SecurityAction)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.ClaimsPrincipalPermissionAttribute" /> class.</summary>
      <param name="action">One of the values that specifies the security actions that can be performed when using declarative security.</param>
    </member>
    <member name="M:System.IdentityModel.Services.ClaimsPrincipalPermissionAttribute.CreatePermission">
      <summary>Creates new instance of the <see cref="T:System.IdentityModel.Services.ClaimsPrincipalPermission" /> class that is based on the current instance.</summary>
      <returns>A <see cref="T:System.IdentityModel.Services.ClaimsPrincipalPermission" /> with the operation and resource associated with this instance.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.ClaimsPrincipalPermissionAttribute.Operation">
      <summary>Gets or sets the operation for which the current principal should be authorized on the specified resource.</summary>
      <returns>The action for which the current principal should be authorized.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.ClaimsPrincipalPermissionAttribute.Resource">
      <summary>Gets or sets the resource on which the principal should be authorized to perform the specified action (operation).</summary>
      <returns>The resource on which the current principal should be authorized.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.CookieHandler">
      <summary>Provides an abstract base class for reading, writing, and deleting session cookies on an HTTP client.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.#ctor">
      <summary>Called from constructors in derived classes to initialize the <see cref="T:System.IdentityModel.Services.CookieHandler" /> class.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.Delete">
      <summary>Deletes the cookie associated with the current request that has the default name, domain, and path.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.Delete(System.String)">
      <summary>Deletes the cookie associated with the current request that has the specified name and the default domain and path. </summary>
      <param name="name">The name of the cookie.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or empty.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.Delete(System.String,System.String,System.String,System.Web.HttpContext)">
      <summary>Deletes the cookie associated with the specified request that has the specified name, path, and domain. </summary>
      <param name="name">The name of the cookie.</param>
      <param name="path">The path for the cookie.</param>
      <param name="domain">The domain for the cookie</param>
      <param name="context">The <see cref="T:System.Web.HttpContext" /> for the request</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or empty.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.Delete(System.String,System.Web.HttpContext)">
      <summary>Deletes the cookie associated with the specified request that has the specified name and the default domain and path.</summary>
      <param name="name">The name of the cookie.</param>
      <param name="context">The <see cref="T:System.Web.HttpContext" /> for the request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or empty.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.Delete(System.Web.HttpContext)">
      <summary>Deletes the cookie associated with the current request that has the default name, domain, and path.</summary>
      <param name="context">The <see cref="T:System.Web.HttpContext" /> for the request.</param>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.DeleteCore(System.String,System.String,System.String,System.Web.HttpContext)">
      <summary>When overridden in a derived class, deletes the cookie associated with the specified request that has the specified name, domain, and path.</summary>
      <param name="name">The name of the cookie</param>
      <param name="path">The path for the cookie</param>
      <param name="domain">The domain for the cookie</param>
      <param name="context">The <see cref="T:System.Web.HttpContext" /> for the request.</param>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandler.Domain">
      <summary>Gets or sets the domain used for cookies.</summary>
      <returns>The domain used for cookies. The default is null.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandler.HideFromClientScript">
      <summary>Gets or sets a value that indicates whether the cookie should be hidden from client script.</summary>
      <returns>true if the cookie should be hidden from client script; otherwise, false. The default is true.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.MatchCookiePath(System.Uri,System.Uri)">
      <summary>If the target domain is within the cookie domain and the target path is within the cookie path, match the casing of the cookie path portion.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
      <param name="baseUri">The base URL of the request.</param>
      <param name="targetUri">The URL to match</param>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandler.Name">
      <summary>Gets or sets the base name for cookies written by the handler.</summary>
      <returns>The base name to use for cookies. The default is “FedAuth”.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a null or empty value occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandler.Path">
      <summary>Gets or sets the virtual path for cookies written by the handler.</summary>
      <returns>The virtual path. Defaults to the root of the ASP.NET application.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a null or empty value occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandler.PersistentSessionLifetime">
      <summary>The lifetime of persistent sessions. If zero, transient sessions are always used.</summary>
      <returns>The session lifetime. The default is null.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">A value that is less than zero is specified.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.Read">
      <summary>Reads the cookie associated with the current request that has the default name.</summary>
      <returns>The cookie value or null if the cookie was not found.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.Read(System.String)">
      <summary>Reads the cookie associated with the current request that has the specified name. </summary>
      <returns>The cookie value or null if the cookie was not found.</returns>
      <param name="name">The name of the cookie to read.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is null or empty.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.Read(System.String,System.Web.HttpContext)">
      <summary>Reads the cookie associated with the specified request that has the specified name and the default domain and path. </summary>
      <returns>The cookie value or null if the cookie was not found.</returns>
      <param name="name">The name of the cookie to read.</param>
      <param name="context">The <see cref="T:System.Web.HttpContext" /> for the request.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is null or empty.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.Read(System.Web.HttpContext)">
      <summary>Reads the cookie associated with the current request that has the default name, domain, and path.</summary>
      <returns>The cookie value or null if the cookie was not found.</returns>
      <param name="context">The <see cref="T:System.Web.HttpContext" /> for the request.</param>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.ReadCore(System.String,System.Web.HttpContext)">
      <summary>When overridden in a derived class, reads the cookie that has the specified name and that is associated with the specified request.</summary>
      <returns>The cookie value or null if the cookie was not found</returns>
      <param name="name">The name of the cookie</param>
      <param name="context">The <see cref="T:System.Web.HttpContext" /> for the request.</param>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandler.RequireSsl">
      <summary>Gets or sets a value that specifies whether the cookie should be used only with SSL.</summary>
      <returns>true if the cookie should only be used over an SSL connection; otherwise, false. The default is true.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.Write(System.Byte[],System.Boolean,System.DateTime)">
      <summary>Writes a cookie associated with the current request that has the specified value, persistence, and expiration time.</summary>
      <param name="value">The cookie value.</param>
      <param name="isPersistent">true if the cookie is persistent; false if the cookie is session-only, that is, only valid until the browser on the client is closed.</param>
      <param name="tokenExpirationTime">The expiration time for the underlying token.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null or empty.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.Write(System.Byte[],System.String,System.DateTime)">
      <summary>Writes a cookie associated with the current request that has the specified name, value, and expiration time.</summary>
      <param name="value">The cookie value.</param>
      <param name="name">The name of the cookie.</param>
      <param name="expirationTime">The expiration time for the cookie, or <see cref="F:System.DateTime.MinValue" /> for a session (session-only) cookie.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null or empty.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is null or empty.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.Write(System.Byte[],System.String,System.DateTime,System.Web.HttpContext)">
      <summary>Writes a cookie associated with the specified request that has the specified name, value, and expiration time.</summary>
      <param name="value">The cookie value.</param>
      <param name="name">The name of the cookie.</param>
      <param name="expirationTime">The expiration time for the cookie, or <see cref="F:System.DateTime.MinValue" /> for a session (session-only) cookie.</param>
      <param name="context">The <see cref="T:System.Web.HttpContext" /> for the request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null or empty.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is null or empty.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.Write(System.Byte[],System.String,System.String,System.String,System.DateTime,System.Boolean,System.Boolean,System.Web.HttpContext)">
      <summary>Writes a cookie associated with the specified request that has the specified name, value, domain, path, expiration time, and visibility.</summary>
      <param name="value">The cookie value.</param>
      <param name="name">The name of the cookie.</param>
      <param name="path">The path for the cookie</param>
      <param name="domain">The domain for the cookie</param>
      <param name="expirationTime">The expiration time for the cookie, or <see cref="F:System.DateTime.MinValue" /> for a session (session-only) cookie.</param>
      <param name="requiresSsl">true if the cookie should only be used over an SSL connection; otherwise, false.</param>
      <param name="hideFromClientScript">true if the cookie should be hidden from client script; otherwise, false.</param>
      <param name="context">The <see cref="T:System.Web.HttpContext" /> for the request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null or empty.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is null or empty.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandler.WriteCore(System.Byte[],System.String,System.String,System.String,System.DateTime,System.Boolean,System.Boolean,System.Web.HttpContext)">
      <summary>When overridden in a derived class, writes a cookie associated with the specified request that has the specified name, value, domain, path, expiration time, persistence and visibility.</summary>
      <param name="value">The cookie value.</param>
      <param name="name">The name of the cookie.</param>
      <param name="path">The path for the cookie</param>
      <param name="domain">The domain for the cookie</param>
      <param name="expirationTime">The expiration time for the cookie, or <see cref="F:System.DateTime.MinValue" /> for a session (session-only) cookie.</param>
      <param name="secure">true if the cookie should only be used over an SSL connection; otherwise, false.</param>
      <param name="httpOnly">true if the cookie should be hidden from client script; otherwise, false.</param>
      <param name="context">The <see cref="T:System.Web.HttpContext" /> for the request.</param>
    </member>
    <member name="T:System.IdentityModel.Services.CookieHandlerElement">
      <summary>Represents the &lt;cookieHandler&gt; configuration element.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandlerElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.CookieHandlerElement" /> class.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandlerElement.ChunkedCookieHandler">
      <summary>Gets or sets a chunked cookie handler.</summary>
      <returns>A <see cref="T:System.IdentityModel.Services.ChunkedCookieHandlerElement" /> that provides additional configuration for the chunked cookie handler.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandlerElement.CustomCookieHandler">
      <summary>Gets or sets a custom cookie handler type. This property must be set when a custom cookie handler is being configured.</summary>
      <returns>A <see cref="T:System.IdentityModel.Configuration.CustomTypeElement" /> that defines the custom cookie handler type.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandlerElement.Domain">
      <summary>Gets or sets the domain value for cookies written by the handler.</summary>
      <returns>The domain value. The default is an empty string.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.CookieHandlerElement.GetConfiguredCookieHandler">
      <summary>Gets a new cookie handler based on the configuration properties.</summary>
      <returns>The cookie handler. This will be an instance of <see cref="T:System.IdentityModel.Services.CookieHandler" /> or <see cref="T:System.IdentityModel.Services.ChunkedCookieHandler" /> depending on the kind of handler that is configured by the properties of the current instance.</returns>
      <exception cref="T:System.Configuration.ConfigurationException">A cookie handler cannot be created from the properties of the current instance.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandlerElement.HideFromScript">
      <summary>Gets or sets a value that specifies whether the httpOnly property of the cookies written by this handler should be set.</summary>
      <returns>true if the httpOnly property should be set; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandlerElement.Mode">
      <summary>Gets or sets the cookie handler mode.</summary>
      <returns>A valid <see cref="T:System.IdentityModel.Services.CookieHandlerMode" /> value that indicates the kind of cookie handler that is being configured. The default value is <see cref="F:System.IdentityModel.Services.CookieHandlerMode.Default" />, which indicates a chunked cookie handler.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandlerElement.Name">
      <summary>Gets or sets the base name for any cookies written by the handler.</summary>
      <returns>The base name for the cookies. The default is “FedAuth”.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandlerElement.Path">
      <summary>Gets or sets the path value for cookies written by the handler.</summary>
      <returns>A string that contains the path.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandlerElement.PersistentSessionLifetime">
      <summary>Gets or sets the lifetime of cookies issued by the handler.</summary>
      <returns>A <see cref="T:System.TimeSpan" /> that represents the cookie lifetime. The default is 0 days (“0.0:0:0”). The value must be a value between 0 (“0.0:0:0”) and 365 (“365.0:0:0”) days.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.CookieHandlerElement.RequireSsl">
      <summary>Gets or sets a value that specifies whether the cookie should be used only with SSL.</summary>
      <returns>true if the cookie should only be used over an SSL connection; otherwise, false. The default is true.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.CookieHandlerMode">
      <summary>Specifies the cookie handler modes that are supported.Defines the possible values of the <see cref="P:System.IdentityModel.Services.CookieHandlerElement.Mode" /> property. This property defines the kind of cookie handler that a <see cref="T:System.IdentityModel.Services.CookieHandlerElement" /> object configures. For more information, see the <see cref="T:System.IdentityModel.Services.CookieHandlerElement" /> class.</summary>
    </member>
    <member name="F:System.IdentityModel.Services.CookieHandlerMode.Default">
      <summary>Specifies that the default type of cookie handler is used. For the <see cref="T:System.IdentityModel.Services.CookieHandlerElement" /> class the default is a chunked cookie handler.</summary>
    </member>
    <member name="F:System.IdentityModel.Services.CookieHandlerMode.Chunked">
      <summary>Specifies a chunked cookie handler; a cookie handler that is an instance of or that derives from the <see cref="T:System.IdentityModel.Services.ChunkedCookieHandler" /> class.</summary>
    </member>
    <member name="F:System.IdentityModel.Services.CookieHandlerMode.Custom">
      <summary>Specifies a custom cookie handler; a custom cookie handler that derives from the <see cref="T:System.IdentityModel.Services.CookieHandler" /> class.</summary>
    </member>
    <member name="T:System.IdentityModel.Services.ErrorEventArgs">
      <summary>Provides data for the <see cref="E:System.IdentityModel.Services.SessionAuthenticationModule.SignOutError" /> event.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.ErrorEventArgs.#ctor(System.Boolean,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.ErrorEventArgs" /> class with the specified cancel state and exception.</summary>
      <param name="cancel">The initial cancel state. Sets the <see cref="P:System.ComponentModel.CancelEventArgs.Cancel" /> property.</param>
      <param name="exception">The exception that occurred. Sets the <see cref="P:System.IdentityModel.Services.ErrorEventArgs.Exception" /> property.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.ErrorEventArgs.#ctor(System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.ErrorEventArgs" /> class with the specified exception.</summary>
      <param name="exception">The exception that occurred. Sets the <see cref="P:System.IdentityModel.Services.ErrorEventArgs.Exception" /> property.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> is null.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.ErrorEventArgs.Exception">
      <summary>Gets the exception that occurred.</summary>
      <returns>The exception that occurred.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.FederatedAuthentication">
      <summary>Provides access to state pertinent to all HTTP modules relevant to federated authentication in the web application.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.FederatedAuthentication.ClaimsAuthorizationModule">
      <summary>Gets the claims authorization module used by the web application.</summary>
      <returns>The claims authorization module.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.FederatedAuthentication.FederationConfiguration">
      <summary>Gets the singleton <see cref="T:System.IdentityModel.Services.Configuration.FederationConfiguration" /> instance used by the HTTP modules in this web application.</summary>
      <returns>The federation configuration instance.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.IdentityModel.Services.Configuration.FederationConfigurationCreatedEventArgs.FederationConfiguration" /> property is set to null by an event handler for the <see cref="E:System.IdentityModel.Services.FederatedAuthentication.FederationConfigurationCreated" /> event.</exception>
    </member>
    <member name="E:System.IdentityModel.Services.FederatedAuthentication.FederationConfigurationCreated">
      <summary>Occurs when the <see cref="P:System.IdentityModel.Services.FederatedAuthentication.FederationConfiguration" /> property is accessed for the first time by one of the HTTP modules in the web application.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedAuthentication.GetHttpModule``1">
      <summary>Gets the HTTP module of the specified type that is being used by the web application.</summary>
      <returns>The HTTP module of the specified type or null if a module of the specified type cannot be found.</returns>
      <typeparam name="T">The type of the module.</typeparam>
    </member>
    <member name="P:System.IdentityModel.Services.FederatedAuthentication.SessionAuthenticationModule">
      <summary>Gets the session authentication module (SAM) used by the web application.</summary>
      <returns>The session authentication module.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.FederatedAuthentication.WSFederationAuthenticationModule">
      <summary>Gets the WS-Federation Authentication Module (WSFAM) used by the web application.</summary>
      <returns>The WS-Federation Authentication Module.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.FederatedAuthenticationSessionEndingException">
      <summary>Indicates that the sign-in session should being terminated, and the current request is unauthenticated.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedAuthenticationSessionEndingException.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.IdentityModel.Services.FederatedAuthenticationSessionEndingException" /></summary>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedAuthenticationSessionEndingException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.FederatedAuthenticationSessionEndingException" /> class with serialized data.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedAuthenticationSessionEndingException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.FederatedAuthenticationSessionEndingException" /> class with a specified error message.</summary>
      <param name="message">The message that describes the error.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedAuthenticationSessionEndingException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.FederatedAuthenticationSessionEndingException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the error.</param>
      <param name="inner">The exception that is the cause of the current exception, or null if no inner exception is specified.</param>
    </member>
    <member name="T:System.IdentityModel.Services.FederatedPassiveSecurityTokenServiceOperations">
      <summary>A utility class that encapsulates the operations typically performed by a Federated Passive Security Token Service.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedPassiveSecurityTokenServiceOperations.ProcessRequest(System.Web.HttpRequest,System.Security.Claims.ClaimsPrincipal,System.IdentityModel.SecurityTokenService,System.Web.HttpResponse)">
      <summary>Processes an incoming WS-Federation Passive Protocol request.</summary>
      <param name="request">The incoming HTTP request.</param>
      <param name="principal">The principal associated with this request.</param>
      <param name="sts">The security token service (STS) to use to issue tokens.</param>
      <param name="response">The outgoing HTTP response.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedPassiveSecurityTokenServiceOperations.ProcessRequest(System.Web.HttpRequest,System.Security.Claims.ClaimsPrincipal,System.IdentityModel.SecurityTokenService,System.Web.HttpResponse,System.IdentityModel.Services.WSFederationSerializer)">
      <summary>Processes an incoming WS-Federation Passive Protocol request by using the specified WS-Federation serializer.</summary>
      <param name="request">The incoming HTTP request.</param>
      <param name="principal">The principal associated with this request.</param>
      <param name="sts">The security token service (STS) to use to issue tokens.</param>
      <param name="response">The outgoing HTTP response.</param>
      <param name="federationSerializer">The serializer to use for handling WS-Federation Passive Protocol messages.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedPassiveSecurityTokenServiceOperations.ProcessSignInRequest(System.IdentityModel.Services.SignInRequestMessage,System.Security.Claims.ClaimsPrincipal,System.IdentityModel.SecurityTokenService)">
      <summary>Processes an incoming WS-Federation Passive Protocol SignIn request.</summary>
      <returns>The sign-in response message that results from handling the request.</returns>
      <param name="requestMessage">The incoming sign-in request message.</param>
      <param name="principal">The principal associated with the request.</param>
      <param name="sts">The security token service (STS) to use to issue tokens.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedPassiveSecurityTokenServiceOperations.ProcessSignInRequest(System.IdentityModel.Services.SignInRequestMessage,System.Security.Claims.ClaimsPrincipal,System.IdentityModel.SecurityTokenService,System.IdentityModel.Services.WSFederationSerializer)">
      <summary>Processes an incoming WS-Federation Passive Protocol SignIn request by using the specified WS-Federation serializer.</summary>
      <returns>The sign-in response message that results from handling the request.</returns>
      <param name="requestMessage">The incoming sign-in request message.</param>
      <param name="principal">The principal associated with the request.</param>
      <param name="sts">The security token service (STS) to use to issue tokens.</param>
      <param name="federationSerializer">The serializer to use for handling WS-Federation Passive Protocol messages.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedPassiveSecurityTokenServiceOperations.ProcessSignInResponse(System.IdentityModel.Services.SignInResponseMessage,System.Web.HttpResponse)">
      <summary>Processes a sign-in response message.</summary>
      <param name="signInResponseMessage">The sign-in response message to be processed.</param>
      <param name="httpResponse">The outgoing HTTP response.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedPassiveSecurityTokenServiceOperations.ProcessSignOutRequest(System.IdentityModel.Services.FederationMessage,System.Security.Claims.ClaimsPrincipal,System.String,System.Web.HttpResponse)">
      <summary>Processes an incoming WS-Federation Passive Protocol SignOut request.</summary>
      <param name="requestMessage">The incoming WS-Federation message representing a sign-out request. By default, this must be an instance of <see cref="T:System.IdentityModel.Services.SignOutRequestMessage" /> or <see cref="T:System.IdentityModel.Services.SignOutCleanupRequestMessage" />.</param>
      <param name="principal">The principal associated with the request.</param>
      <param name="reply">The reply URL to be redirected to after sign-out.</param>
      <param name="httpResponse">The outgoing HTTP response.</param>
    </member>
    <member name="T:System.IdentityModel.Services.FederatedSessionExpiredException">
      <summary>The exception that is thrown when a session has expired.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedSessionExpiredException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.FederatedSessionExpiredException" />class.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedSessionExpiredException.#ctor(System.DateTime,System.DateTime)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.FederatedSessionExpiredException" /> class that has the specified instant that the session was validated and the instant that it expired.</summary>
      <param name="tested">The time that the session was validated.</param>
      <param name="expired">The time that the session expired.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedSessionExpiredException.#ctor(System.DateTime,System.DateTime,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.FederatedSessionExpiredException" /> class with the specified instant that the session was validated, the instant that the session expired, and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="tested">The time at which the session was validated.</param>
      <param name="expired">The time at which the session expired.</param>
      <param name="inner">The exception that is the cause of the current exception, or null if no inner exception is specified.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedSessionExpiredException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.FederatedSessionExpiredException" />class with serialized data.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedSessionExpiredException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.FederatedSessionExpiredException" /> class with a specified error message.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedSessionExpiredException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.FederatedSessionExpiredException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <param name="inner">The <see cref="T:System.Exception" /> that is the cause of the current exception. If the <paramref name="inner" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception.</param>
    </member>
    <member name="P:System.IdentityModel.Services.FederatedSessionExpiredException.Expired">
      <summary>Gets the time that the session expired.</summary>
      <returns>The time that the session expired.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.FederatedSessionExpiredException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>When overridden in a derived class, sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with information about the exception.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
    </member>
    <member name="P:System.IdentityModel.Services.FederatedSessionExpiredException.Tested">
      <summary>Gets the time that the session was validated.</summary>
      <returns>The time that the session was validated.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.FederationException">
      <summary>Base class for exceptions raised in WS-Federation support.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.FederationException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.FederationException" /> class.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.FederationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.FederationException" /> class with serialized data.</summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object that holds the serialized object data.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> object that contains the contextual information about the source or destination.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederationException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.FederationException" /> class with a specified error message.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederationException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.FederationException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <param name="inner">The <see cref="T:System.Exception" /> that is the cause of the current exception. If the <paramref name="inner" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception.</param>
    </member>
    <member name="T:System.IdentityModel.Services.FederationManagement">
      <summary>Defines static methods for creating Federation Metadata documents and for updating application configuration based on Federation Metadata documents.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.FederationManagement.CreateApplicationFederationMetadata(System.Uri,System.Security.Cryptography.X509Certificates.X509Certificate2,System.Collections.ObjectModel.Collection{System.IdentityModel.Metadata.DisplayClaim},System.IdentityModel.Services.ApplicationType,System.Collections.ObjectModel.Collection{System.Uri})">
      <summary>Creates an XML document that contains Federation Metadata.</summary>
      <returns>An XML reader that contains the metadata document that was created by using the parameters.</returns>
      <param name="applicationUri">The application URI.</param>
      <param name="certificate">The X.509 certificate to use to sign the metadata.</param>
      <param name="claimsRequired">The claims required by the application.</param>
      <param name="applicationType">One of the <see cref="T:System.IdentityModel.Services.ApplicationType" /> values that specifies the type of application that the metadata describes. This is used to determine the endpoints to create.</param>
      <param name="audienceUris">The collection of acceptable URIs that can be used for this application.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederationManagement.UpdateIdentityProviderTrustInfo(System.Xml.XmlReader,System.Xml.XmlReader,System.Boolean)">
      <summary>Updates the specified application configuration, if needed, based on the specified Federation Metadata document and a value that specifies whether the claims offered list should be updated.</summary>
      <returns>An XML reader that contains the updated configuration or null if the configuration could not be updated.</returns>
      <param name="metadataReader">An XML reader that contains the metadata document.</param>
      <param name="inputConfiguration">An XML reader that contains the configuration.</param>
      <param name="claimsOfferedUpdate">true if the claims offered list should be updated; otherwise, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="metadataReader" /> is null.-or-<paramref name="inputConfiguration" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.FederationManagement.UpdateIdentityProviderTrustInfo(System.Xml.XmlReader,System.Xml.XmlReader,System.Boolean,System.IdentityModel.Metadata.MetadataSerializer)">
      <summary>Updates the specified application configuration, if needed, based on the specified Federation Metadata document and a value that specifies whether the claims offered list should be updated. The serializer with which to read the security token service (STS) metadata is also specified.</summary>
      <returns>An XML reader that contains the updated configuration or null if the configuration could not be updated.</returns>
      <param name="metadataReader">An XML reader that contains the metadata document.</param>
      <param name="inputConfiguration">An XML reader that contains the configuration.</param>
      <param name="claimsOfferedUpdate">true if the claims offered list should be updated; otherwise, false.</param>
      <param name="metadataSerializer">The serializer with which to read the STS metadata.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="metadataReader" /> is null.-or-<paramref name="inputConfiguration" /> is null.-or-<paramref name="metadataSerializer" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.FederationManagement.UpdateIdentityProviderTrustInfo(System.Xml.XmlReader,System.Xml.XmlReader,System.Xml.XmlNodeReader@,System.Xml.XmlNodeReader@)">
      <summary>Updates the specified application configuration, if needed, based on the specified Federation Metadata document. Returns the configuration elements for the issuer name registry, as well as the configuration elements for the updated list of claims types offered, if it has been updated. </summary>
      <param name="metadataReader">An XML reader that contains the metadata document.</param>
      <param name="inputConfiguration">An XML reader that contains the configuration.</param>
      <param name="newIssuerNameRegistry">When this method returns, contains an XML node reader that contains the elements that specify the issuer name registry. null if there are is no issuer name registry specified. This parameter is treated as uninitialized.</param>
      <param name="claimTypesOffered">When this method returns, if the list of claim types offered has been changed, contains an XML node reader that contains elements that specify new list. If the list has not been changed, returns null. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="metadataReader" /> is null.-or-<paramref name="inputConfiguration" /><paramref name="metadataReader" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.FederationManagement.UpdateIdentityProviderTrustInfo(System.Xml.XmlReader,System.Xml.XmlReader,System.Xml.XmlNodeReader@,System.Xml.XmlNodeReader@,System.IdentityModel.Metadata.MetadataSerializer)">
      <summary>Updates the specified application configuration, if needed, based on the specified Federation Metadata document. Returns configuration elements for the issuer name registry, as well as the configuration elements for the updated list of claims types offered, if it has been updated. The serializer with which to read the security token service (STS) metadata is also specified.</summary>
      <param name="metadataReader">An XML reader that contains the metadata document.</param>
      <param name="inputConfiguration">An XML reader that contains the configuration.</param>
      <param name="newIssuerNameRegistry">When this method returns, contains an XML node reader that contains the elements that specify the issuer name registry. null if there are is no issuer name registry specified. This parameter is treated as uninitialized.</param>
      <param name="claimTypesOffered">When this method returns, if the list of claim types offered has been changed, contains an XML node reader that contains elements that specify new list. If the list has not been changed, returns null. This parameter is treated as uninitialized.</param>
      <param name="metadataSerializer">The serializer with which to read the STS metadata.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="metadataReader" /> is null.-or-<paramref name="inputConfiguration" /> is null.-or-<paramref name="metadataSerializer" /> is null.</exception>
    </member>
    <member name="T:System.IdentityModel.Services.FederationMessage">
      <summary>Defines the base class from which all federation message classes derive.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.FederationMessage.#ctor(System.Uri)">
      <summary>Called from constructors in derived classes to initialize the <see cref="T:System.IdentityModel.Services.FederationMessage" /> class.</summary>
      <param name="baseUrl">The base URL to which the federation message applies. Initializes the <see cref="P:System.IdentityModel.Services.FederationMessage.BaseUri" /> property.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="baseUri" /> is null.</exception>
      <exception cref="T:System.IdentityModel.Services.WSFederationMessageException">
        <paramref name="baseUri" /> is not a valid, absolute URI.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.FederationMessage.BaseUri">
      <summary>Gets or sets the base URL to which the message applies.</summary>
      <returns>A <see cref="T:System.Uri" /> that contains the base URL.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set a value that is null occurs.</exception>
      <exception cref="T:System.IdentityModel.Services.WSFederationMessageException">An attempt to set a value that is not a valid URI occurs.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.FederationMessage.GetBaseUrl(System.Uri)">
      <summary>Helper method that extracts the base URL from the specified URI.</summary>
      <returns>The base URL that was extracted.</returns>
      <param name="uri">The URI from which to extract the base URL.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederationMessage.GetParameter(System.String)">
      <summary>Returns the specified parameter value from the parameters dictionary.</summary>
      <returns>The value of the parameter or null if the parameter does not exist.</returns>
      <param name="parameter">The parameter for which to search.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="parameter" /> is null or an empty string.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.FederationMessage.Parameters">
      <summary>Gets the message parameters as a dictionary.</summary>
      <returns>A dictionary that contains the message parameters.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.FederationMessage.ParseQueryString(System.Uri)">
      <summary>Helper method that parses the query string in the specified URI into a <see cref="T:System.Collections.Specialized.NameValueCollection" />.</summary>
      <returns>A <see cref="T:System.Collections.Specialized.NameValueCollection" /> that contains the parameters in the query string.</returns>
      <param name="data">The URI to parse.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.FederationMessage.RemoveParameter(System.String)">
      <summary>Removes a parameter from the parameters dictionary.</summary>
      <param name="parameter">The name of the parameter to remove.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="parameter" /> is null or an empty string.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.FederationMessage.SetParameter(System.String,System.String)">
      <summary>Sets the value of a parameter in the parameters dictionary.</summary>
      <param name="parameter">The name of the parameter to set.</param>
      <param name="value">The value to be assigned to the parameter.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="parameter" /> is null or an empty string. -or-<paramref name="value" /> is null or an empty string.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.FederationMessage.SetUriParameter(System.String,System.String)">
      <summary>Sets the value of a parameter in the parameters dictionary. The value must be an absolute URI.</summary>
      <param name="parameter">The parameter name.</param>
      <param name="value">The parameter value.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="parameter" /> is null.-or-<paramref name="value" /> is null or not an absolute URI.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.FederationMessage.Validate">
      <summary>Validates the message.</summary>
      <exception cref="T:System.IdentityModel.Services.WSFederationMessageException">The value of the <see cref="P:System.IdentityModel.Services.FederationMessage.BaseUri" /> property is null or is not an absolute URI.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.FederationMessage.Write(System.IO.TextWriter)">
      <summary>When overridden in a derived class, writes the message to the output stream.</summary>
      <param name="writer">The text writer to which the message is written out.</param>
    </member>
    <member name="M:System.IdentityModel.Services.FederationMessage.WriteFormPost">
      <summary>Serializes the message as a form post and returns the resulting Form together with its Javascript as a string.</summary>
      <returns>A string representation of the message as a Form together with its associated Javascript.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.FederationMessage.WriteQueryString">
      <summary>Returns a string representation of the message in query-string format.</summary>
      <returns>The message in query-string format.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.HttpModuleBase">
      <summary>The base class from which HTTP modules that are configurable with the &lt;system.identityModel.services&gt; element (<see cref="T:System.IdentityModel.Services.Configuration.SystemIdentityModelServicesSection" />) derive.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.HttpModuleBase.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.HttpModuleBase" /> class.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.HttpModuleBase.Dispose">
      <summary>Releases the resources (except memory) used by the current instance of the <see cref="T:System.IdentityModel.Services.HttpModuleBase" /> class.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.HttpModuleBase.FederationConfiguration">
      <summary>Gets or sets the <see cref="T:System.IdentityModel.Services.Configuration.FederationConfiguration" /> object that is in effect for the current module.</summary>
      <returns>The <see cref="T:System.IdentityModel.Services.Configuration.FederationConfiguration" /> object that is in effect for the current module.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set the property to null occurs.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.HttpModuleBase.Init(System.Web.HttpApplication)">
      <summary>Initializes the HTTP module.</summary>
      <param name="context">The application object which contains this module.</param>
    </member>
    <member name="M:System.IdentityModel.Services.HttpModuleBase.InitializeModule(System.Web.HttpApplication)">
      <summary>When overridden in a derived class, initializes the current module and prepares it to handle events from its ASP.NET application object.</summary>
      <param name="context">The application object which contains this module.</param>
    </member>
    <member name="M:System.IdentityModel.Services.HttpModuleBase.InitializePropertiesFromConfiguration">
      <summary>When overridden in a derived class, initializes module properties based on values in the <see cref="T:System.IdentityModel.Services.FederationConfiguration" /> object that is in effect for it.</summary>
    </member>
    <member name="T:System.IdentityModel.Services.MachineKeyTransform">
      <summary>Protects session tokens with the cryptographic material specified in the &lt;machineKey&gt; section of the web.config or machine.config configuration file.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.MachineKeyTransform.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.MachineKeyTransform" /> class.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.MachineKeyTransform.Decode(System.Byte[])">
      <summary>Reverses the transform.</summary>
      <returns>The decoded byte array.</returns>
      <param name="encoded">The encoded form of the cookie.</param>
    </member>
    <member name="M:System.IdentityModel.Services.MachineKeyTransform.Encode(System.Byte[])">
      <summary>Applies the transform.</summary>
      <returns>The encoded cookie.</returns>
      <param name="value">The byte array to be encoded.</param>
    </member>
    <member name="T:System.IdentityModel.Services.PseudonymRequestMessage">
      <summary>Represents a WS-Federation Pseudonym Request message. This message is created when the received message wa parameter is “wpseudo1.0”.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.PseudonymRequestMessage.#ctor(System.Uri)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.PseudonymRequestMessage" /> class with the specified base URL.</summary>
      <param name="baseUrl">The base URL to which this message applies.</param>
    </member>
    <member name="P:System.IdentityModel.Services.PseudonymRequestMessage.Pseudonym">
      <summary>Gets or sets the wpseudo parameter of the message.</summary>
      <returns>A string that contains the value of the wpseudo parameter.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.PseudonymRequestMessage.PseudonymPtr">
      <summary>Gets or sets the wpseudoptr parameter of the message.</summary>
      <returns>A string that contains the value of the wpseudoptr parameter.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid URI occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.PseudonymRequestMessage.Reply">
      <summary>Gets or sets the Reply parameter of the message.</summary>
      <returns>A string that contains the value of the wreply parameter. This is the URL to which the reply should be sent.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid URI occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.PseudonymRequestMessage.Result">
      <summary>Gets or sets the wresult parameter of the message.</summary>
      <returns>A string that contains the value of the wresult parameter.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.PseudonymRequestMessage.ResultPtr">
      <summary>Gets or sets the wresultptr parameter of the message.</summary>
      <returns>A string that contains the value of the wresultptr parameter. This is a URI.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid URI occurs.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.PseudonymRequestMessage.Validate">
      <summary>No validation is performed by the framework. Users of this class should validate externally.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.PseudonymRequestMessage.Write(System.IO.TextWriter)">
      <summary>Writes this message in a query string form to the specified text writer.</summary>
      <param name="writer">The text writer to which to write the message.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is null.</exception>
    </member>
    <member name="T:System.IdentityModel.Services.RedirectingToIdentityProviderEventArgs">
      <summary>Provides data for the <see cref="E:System.IdentityModel.Services.WSFederationAuthenticationModule.RedirectingToIdentityProvider" /> event.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.RedirectingToIdentityProviderEventArgs.#ctor(System.IdentityModel.Services.SignInRequestMessage)">
      <summary>Initializes an instance of the <see cref="T:System.IdentityModel.Services.RedirectingToIdentityProviderEventArgs" /> class by using the specified WS-Federation Passive Sign-in message.</summary>
      <param name="signInRequestMessage">The WS-Federation Passive Sign-in message that will be used to redirect the user to the Identity Provider.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="signInRequestMessage" /> is null.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.RedirectingToIdentityProviderEventArgs.SignInRequestMessage">
      <summary>Gets or sets the WS-Federation Passive Sign-in message that will be used to redirect the user to the identity provider.</summary>
      <returns>The WS-Federation Sign-in message that will be used to redirect the user to the identity provider.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set the property to null occurs.</exception>
    </member>
    <member name="T:System.IdentityModel.Services.SecurityTokenReceivedEventArgs">
      <summary>Provides data for the <see cref="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SecurityTokenReceived" /> event.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SecurityTokenReceivedEventArgs.#ctor(System.IdentityModel.Tokens.SecurityToken)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SecurityTokenReceivedEventArgs" /> class by using the specified security token.</summary>
      <param name="securityToken">The issued security token.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="securityToken" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SecurityTokenReceivedEventArgs.#ctor(System.IdentityModel.Tokens.SecurityToken,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SecurityTokenReceivedEventArgs" /> class by using the specified security token and sign-in context.</summary>
      <param name="securityToken">The issued security token.</param>
      <param name="signInContext">The sign-in context specified in the control.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="securityToken" /> is null.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SecurityTokenReceivedEventArgs.SecurityToken">
      <summary>Gets or sets the issued security token.</summary>
      <returns>The issued security token.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.SecurityTokenReceivedEventArgs.SignInContext">
      <summary>Gets the sign-in context specified in the control.</summary>
      <returns>The sign-in context.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.SecurityTokenValidatedEventArgs">
      <summary>Provides data for the <see cref="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SecurityTokenValidated" /> event.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SecurityTokenValidatedEventArgs.#ctor(System.Security.Claims.ClaimsPrincipal)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SecurityTokenValidatedEventArgs" /> class.</summary>
      <param name="claimsPrincipal">The claims principal resulting from validation of the received <see cref="T:System.IdentityModel.Tokens.SecurityToken" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="claimsPrincipal" /> is null.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SecurityTokenValidatedEventArgs.ClaimsPrincipal">
      <summary>Gets or sets the <see cref="T:System.Security.Claims.ClaimsPrincipal" /> that results from token validation.</summary>
      <returns>The claims principal that results from token validation.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.ServiceCertificateElement">
      <summary>Represents the &lt;serviceCertificate&gt; element in a configuration file.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.ServiceCertificateElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.ServiceCertificateElement" /> class.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.ServiceCertificateElement.CertificateReference">
      <summary>Gets or sets the child &lt;certificateReference&gt; element for this &lt;serviceCertificate&gt; element.</summary>
      <returns>The &lt;certificateReference&gt; element.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.SessionAuthenticationModule">
      <summary>Implements an ASP.NET module that processes session cookies in WS-Federation scenarios.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SessionAuthenticationModule" /> class.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.AuthenticateSessionSecurityToken(System.IdentityModel.Tokens.SessionSecurityToken,System.Boolean)">
      <summary>Authenticates the incoming request by validating the incoming session token. Upon successful validation, it updates the current HTTP context and thread principal with the specified  <see cref="T:System.IdentityModel.Tokens.SessionSecurityToken" />.</summary>
      <param name="sessionToken">The session security token to use to authenticate the incoming HTTP request.</param>
      <param name="writeCookie">true to write the session cookie; otherwise false. </param>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.ContainsSessionTokenCookie(System.Web.HttpCookieCollection)">
      <summary>Determines whether a session cookie is in the specified cookie collection.</summary>
      <returns>true if a session cookie is found; otherwise, false.</returns>
      <param name="httpCookieCollection">The collection of cookies in which to search.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="httpCookieCollection" /> is null</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SessionAuthenticationModule.ContextSessionSecurityToken">
      <summary>Gets the active <see cref="T:System.IdentityModel.Tokens.SessionSecurityToken" /> for the current <see cref="T:System.Web.HttpContext" />.</summary>
      <returns>The active session security token.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.SessionAuthenticationModule.CookieHandler">
      <summary>Gets the cookie handler that is used to read, write, and delete session cookies.</summary>
      <returns>The cookie handler.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.CreateSessionSecurityToken(System.Security.Claims.ClaimsPrincipal,System.String,System.DateTime,System.DateTime,System.Boolean)">
      <summary>Creates a <see cref="T:System.IdentityModel.Tokens.SessionSecurityToken" /> from the specified parameters by using the configured session token handler.</summary>
      <returns>The session token.</returns>
      <param name="principal">The principal to be captured in the token.</param>
      <param name="context">An application-defined context string.</param>
      <param name="validFrom">The first instant in which this token is valid.</param>
      <param name="validTo">The last instant in which this token is valid.</param>
      <param name="isPersistent">true if the value should be persisted by the user agent; otherwise, false.</param>
      <exception cref="T:System.InvalidOperationException">There is not a valid session token handler configured. (There is no <see cref="T:System.IdentityModel.Tokens.SessionSecurityTokenHandler" /> configured in the <see cref="P:System.IdentityModel.Configuration.IdentityConfiguration.SecurityTokenHandlers" /> property.)</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.DeleteSessionTokenCookie">
      <summary>Deletes the session cookie and removes it from the cache.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.InitializeModule(System.Web.HttpApplication)">
      <summary>Initializes the module and prepares it to handle events from the module's ASP.NET application object.</summary>
      <param name="context">The HTTP application object that contains this module.</param>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.InitializePropertiesFromConfiguration">
      <summary>Initializes the module properties based on definitions in the configuration file.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.SessionAuthenticationModule.IsReferenceMode">
      <summary>Gets or sets a value that specifies whether the session information (claim values, etc.) should be stored in the session cookie or whether the session content should be stored on the server side, using the cookie to store just a reference.</summary>
      <returns>true if issued cookies are in reference mode; otherwise, false. The default is false, which specifies that issued cookies are not in reference mode.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.OnAuthenticateRequest(System.Object,System.EventArgs)">
      <summary>Handles the <see cref="E:System.Web.HttpApplication.AuthenticateRequest" /> event from the ASP.NET pipeline. </summary>
      <param name="sender">The source for the event. This will be an <see cref="T:System.Web.HttpApplication" /> object.</param>
      <param name="eventArgs">The data for the event.</param>
      <exception cref="T:System.InvalidOperationException">There is not a valid session token handler configured. (There is no <see cref="T:System.IdentityModel.Tokens.SessionSecurityTokenHandler" /> configured in the <see cref="P:System.IdentityModel.Configuration.IdentityConfiguration.SecurityTokenHandlers" /> property.)</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.OnPostAuthenticateRequest(System.Object,System.EventArgs)">
      <summary>Handles the <see cref="E:System.Web.HttpApplication.PostAuthenticateRequest" /> event from the ASP.NET pipeline. </summary>
      <param name="sender">The source for the event. This will be an <see cref="T:System.Web.HttpApplication" /> object.</param>
      <param name="e">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.OnSessionSecurityTokenCreated(System.IdentityModel.Services.SessionSecurityTokenCreatedEventArgs)">
      <summary>Raises the <see cref="E:System.IdentityModel.Services.SessionAuthenticationModule.SessionSecurityTokenCreated" /> event.</summary>
      <param name="args">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.OnSessionSecurityTokenReceived(System.IdentityModel.Services.SessionSecurityTokenReceivedEventArgs)">
      <summary>Raises the <see cref="E:System.IdentityModel.Services.SessionAuthenticationModule.SessionSecurityTokenReceived" /> event.</summary>
      <param name="args">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.OnSignedOut(System.EventArgs)">
      <summary>Raises the <see cref="E:System.IdentityModel.Services.SessionAuthenticationModule.SignedOut" /> event.</summary>
      <param name="e">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.OnSigningOut(System.IdentityModel.Services.SigningOutEventArgs)">
      <summary>Raises the <see cref="E:System.IdentityModel.Services.SessionAuthenticationModule.SigningOut" /> event.</summary>
      <param name="e">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.OnSignOutError(System.IdentityModel.Services.ErrorEventArgs)">
      <summary>Raises the <see cref="E:System.IdentityModel.Services.SessionAuthenticationModule.SignOutError" /> event.</summary>
      <param name="e">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.ReadSessionTokenFromCookie(System.Byte[])">
      <summary>Reads a <see cref="T:System.IdentityModel.Tokens.SessionSecurityToken" /> from the specified session cookie.</summary>
      <returns>The session token that was read from the cookie.</returns>
      <param name="sessionCookie">The cookie, in raw form, that contains the session token.</param>
      <exception cref="T:System.InvalidOperationException">There is not a valid session token handler configured. (There is no <see cref="T:System.IdentityModel.Tokens.SessionSecurityTokenHandler" /> configured in the <see cref="P:System.IdentityModel.Configuration.IdentityConfiguration.SecurityTokenHandlers" /> property.)</exception>
    </member>
    <member name="E:System.IdentityModel.Services.SessionAuthenticationModule.SessionSecurityTokenCreated">
      <summary>Occurs when a session security token has been created.</summary>
    </member>
    <member name="E:System.IdentityModel.Services.SessionAuthenticationModule.SessionSecurityTokenReceived">
      <summary>Occurs when a session security token has been read from a cookie.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.SetPrincipalFromSessionToken(System.IdentityModel.Tokens.SessionSecurityToken)">
      <summary>Sets the principal on the <see cref="T:System.Web.HttpContext" /> and <see cref="T:System.Threading.Thread" /> to the principal that is contained in the specified session token.</summary>
      <param name="sessionSecurityToken">The session token from which to set the principal.</param>
    </member>
    <member name="E:System.IdentityModel.Services.SessionAuthenticationModule.SignedOut">
      <summary>Occurs after the user is signed out.</summary>
    </member>
    <member name="E:System.IdentityModel.Services.SessionAuthenticationModule.SigningOut">
      <summary>Occurs before deleting the sign-in session.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.SignOut">
      <summary>Signs the current user out and raises the associated events.</summary>
    </member>
    <member name="E:System.IdentityModel.Services.SessionAuthenticationModule.SignOutError">
      <summary>Occurs when there is an error during sign-out.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.TryReadSessionTokenFromCookie(System.IdentityModel.Tokens.SessionSecurityToken@)">
      <summary>Attempts to read a <see cref="T:System.IdentityModel.Tokens.SessionSecurityToken" /> from a session cookie and returns a value that indicates whether the session cookie was successfully read.</summary>
      <returns>true if the session cookie was successfully read from the request; otherwise, false.</returns>
      <param name="sessionToken">When this method returns, contains the session security token that was read from the session cookie.</param>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.ValidateSessionToken(System.IdentityModel.Tokens.SessionSecurityToken)">
      <summary>Validates the specified <see cref="T:System.IdentityModel.Tokens.SessionSecurityToken" /> and returns its identities.</summary>
      <returns>The collection of identities that are contained in the token.</returns>
      <param name="sessionSecurityToken">The token to validate.</param>
      <exception cref="T:System.IdentityModel.Services.FederatedSessionExpiredException">The token has expired.</exception>
      <exception cref="T:System.IdentityModel.Services.FederationException">The token start time is not yet valid.</exception>
      <exception cref="T:System.InvalidOperationException">There is not a valid session token handler configured. (There is no <see cref="T:System.IdentityModel.Tokens.SessionSecurityTokenHandler" /> configured in the <see cref="P:System.IdentityModel.Configuration.IdentityConfiguration.SecurityTokenHandlers" /> property.)</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SessionAuthenticationModule.WriteSessionTokenToCookie(System.IdentityModel.Tokens.SessionSecurityToken)">
      <summary>Writes the specified <see cref="T:System.IdentityModel.Tokens.SessionSecurityToken" /> to a session cookie.</summary>
      <param name="sessionToken">The session security token to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sessionToken" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">There is not a valid session token handler configured. (There is no <see cref="T:System.IdentityModel.Tokens.SessionSecurityTokenHandler" /> configured in the <see cref="P:System.IdentityModel.Configuration.IdentityConfiguration.SecurityTokenHandlers" /> property.)</exception>
    </member>
    <member name="T:System.IdentityModel.Services.SessionSecurityTokenCreatedEventArgs">
      <summary>Provides data for the <see cref="E:System.IdentityModel.Services.SessionAuthenticationModule.SessionSecurityTokenCreated" /> event and for the <see cref="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SessionSecurityTokenCreated" /> event.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SessionSecurityTokenCreatedEventArgs.#ctor(System.IdentityModel.Tokens.SessionSecurityToken)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SessionSecurityTokenCreatedEventArgs" /> class by using the specified session security token.</summary>
      <param name="sessionToken">The session security token that was created.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sessionToken" /> is null.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SessionSecurityTokenCreatedEventArgs.SessionToken">
      <summary>Gets or sets the session security token that was created.</summary>
      <returns>The session token that was created.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.SessionSecurityTokenCreatedEventArgs.WriteSessionCookie">
      <summary>Gets or sets a value that indicates whether a cookie should be written in the response.</summary>
      <returns>true to write a cookie in the response; otherwise, false. The default is false, the cookie is not written in the response.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.SessionSecurityTokenReceivedEventArgs">
      <summary>Provides data for the <see cref="E:System.IdentityModel.Services.SessionAuthenticationModule.SessionSecurityTokenReceived" /> event.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SessionSecurityTokenReceivedEventArgs.#ctor(System.IdentityModel.Tokens.SessionSecurityToken)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SecurityTokenReceivedEventArgs" /> class that has the specified session token.</summary>
      <param name="sessionToken">The session security token that was received.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sessionToken" /> is null.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SessionSecurityTokenReceivedEventArgs.ReissueCookie">
      <summary>Gets or sets a value that specifies whether to reissue the session cookie with the response.</summary>
      <returns>true to reissue the session cookie with the response; otherwise, false. The default is false, the session cookie is not reissued with the response.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.SessionSecurityTokenReceivedEventArgs.SessionToken">
      <summary>Gets or sets the session security token that was received.</summary>
      <returns>The session token that was received.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.SessionSecurityTokenResolver">
      <summary>Resolves session security tokens from the current cache.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SessionSecurityTokenResolver.#ctor(System.IdentityModel.Tokens.SessionSecurityTokenCache,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SessionSecurityTokenResolver" /> class.</summary>
      <param name="tokenCache">The token cache.</param>
      <param name="endpointId">The identifier of the endpoint.</param>
    </member>
    <member name="M:System.IdentityModel.Services.SessionSecurityTokenResolver.TryResolveSecurityKeyCore(System.IdentityModel.Tokens.SecurityKeyIdentifierClause,System.IdentityModel.Tokens.SecurityKey@)">
      <summary>Attempts to resolve the security key core, given a specified key identifier clause.</summary>
      <returns>true if the key core was successfully resolved; otherwise, false.</returns>
      <param name="keyIdentifierClause">The security key identifier clause.</param>
      <param name="key">When this method returns, contains the resolved key core. This parameter is passed uninitialized.</param>
    </member>
    <member name="M:System.IdentityModel.Services.SessionSecurityTokenResolver.TryResolveTokenCore(System.IdentityModel.Tokens.SecurityKeyIdentifier,System.IdentityModel.Tokens.SecurityToken@)">
      <summary>Attempts to resolve the token from the specified key identifier.</summary>
      <returns>true if the key token was successfully resolved; otherwise, false.</returns>
      <param name="keyIdentifier">The key identifier.</param>
      <param name="token">When this method returns, contains the resolved key core. This parameter is passed uninitialized.</param>
    </member>
    <member name="M:System.IdentityModel.Services.SessionSecurityTokenResolver.TryResolveTokenCore(System.IdentityModel.Tokens.SecurityKeyIdentifierClause,System.IdentityModel.Tokens.SecurityToken@)">
      <summary>Attempts to resolve the token from the specified key identifier clause.</summary>
      <returns>true if the key token was successfully resolved; otherwise, false.</returns>
      <param name="keyIdentifierClause">The key identifier clause.</param>
      <param name="token">When this method returns, contains the resolved key core. This parameter is passed uninitialized.</param>
    </member>
    <member name="T:System.IdentityModel.Services.SigningOutEventArgs">
      <summary>Provides data for the <see cref="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SigningOut" /> event.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SigningOutEventArgs.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SigningOutEventArgs" /> class by using a value that specifies whether sign-out was requested by the identity provider (IP).</summary>
      <param name="isIPInitiated">true if sign-out was requested by the IP; otherwise, false.</param>
    </member>
    <member name="P:System.IdentityModel.Services.SigningOutEventArgs.IPInitiated">
      <summary>Gets a <see cref="T:System.IdentityModel.Services.SigningOutEventArgs" /> that indicates sign-out was initiated by the identity provider (IP). </summary>
      <returns>A <see cref="T:System.IdentityModel.Services.SigningOutEventArgs" /> that has its <see cref="P:System.IdentityModel.Services.SigningOutEventArgs.IsIPInitiated" /> property set true.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.SigningOutEventArgs.IsIPInitiated">
      <summary>Gets a value that indicates whether sign-out was initiated by the identity provider (IP) via a WS-Federation sign-out clean-up message (“wsignoutcleanup1.0”).</summary>
      <returns>true if sign-out was initiated by the IP; otherwise, false.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.SigningOutEventArgs.RPInitiated">
      <summary>Gets a <see cref="T:System.IdentityModel.Services.SigningOutEventArgs" /> that indicates sign-out was initiated by the relying party (RP). </summary>
      <returns>A <see cref="T:System.IdentityModel.Services.SigningOutEventArgs" /> that has its <see cref="P:System.IdentityModel.Services.SigningOutEventArgs.IsIPInitiated" /> property set false.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.SignInRequestMessage">
      <summary>Represents a WS-Federation Sign-In Request message.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SignInRequestMessage.#ctor(System.Uri,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SignInRequestMessage" /> class with the specified base URL and wtrealm parameter.</summary>
      <param name="baseUrl">The base URL to which the sign-in message applies.</param>
      <param name="realm">The value of the wtrealm message parameter. Sets the <see cref="P:System.IdentityModel.Services.SignInRequestMessage.Realm" /> property.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="realm" /> is null or an empty string.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SignInRequestMessage.#ctor(System.Uri,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SignInRequestMessage" /> class using the specified base URI, wtrealm parameter, and wreply parameter. Supports non-standard message creation for backward compatibility.</summary>
      <param name="baseUrl">The Base URL to which the sign-in message applies.</param>
      <param name="realm">The value of the wtrealm message parameter. If not null or empty, sets the <see cref="P:System.IdentityModel.Services.SignInRequestMessage.Realm" /> property. </param>
      <param name="reply">The URI to which to reply. (The value of the wreply message parameter.) If not null or empty, sets the <see cref="P:System.IdentityModel.Services.SignInRequestMessage.Reply" /> property.</param>
      <exception cref="T:System.ArgumentNullException">Both <paramref name="realm" /> and <paramref name="reply" /> are null or an empty string.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SignInRequestMessage.AuthenticationType">
      <summary>Gets or sets the wauth parameter of the message.</summary>
      <returns>The authentication type. This is specified as a URI.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid, absolute URI occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SignInRequestMessage.CurrentTime">
      <summary>Gets or sets the wct parameter of the message.
</summary>
      <returns>The value of the wct parameter specified as a datetime string in UTC.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid datetime string occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SignInRequestMessage.Federation">
      <summary>Gets or sets the wfed parameter of the message.</summary>
      <returns>The value of the wfed parameter. This is specified as a URI.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.SignInRequestMessage.Freshness">
      <summary>Gets or sets the wfresh parameter of the message.
</summary>
      <returns>The value of the wfresh parameter. This should be an integer represented as a string. It specifies the maximum age in minutes that the authentication is valid. Zero indicates that the user should be prompted before the token is issued.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a string representation of an integer.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SignInRequestMessage.HomeRealm">
      <summary>Gets or sets the whr parameter of the message.</summary>
      <returns>The value of the whr parameter. This is specified as a URI.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.SignInRequestMessage.Policy">
      <summary>Gets or sets the wp parameter of the message.</summary>
      <returns>The value of the wp parameter. This is specified as a URI.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid, absolute URI occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SignInRequestMessage.Realm">
      <summary>Gets or sets the wtrealm parameter of the message.</summary>
      <returns>The value of the wtrealm parameter. This is specified as a URI.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid, absolute URI occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SignInRequestMessage.Reply">
      <summary>Gets or sets the wreply parameter of the message.</summary>
      <returns>The value of the wreply parameter. This is specified as a URI.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid, absolute URI occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SignInRequestMessage.Request">
      <summary>Gets or sets the wreq parameter of the message.
</summary>
      <returns>The value of the wreq parameter.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.SignInRequestMessage.RequestPtr">
      <summary>Gets or sets the wreqptr parameter of the message.</summary>
      <returns>The value of the wreqptr parameter.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid, absolute URI occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SignInRequestMessage.RequestUrl">
      <summary>Gets a string representation of the URL that corresponds to this message.</summary>
      <returns>A URL serialized from the current instance.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.SignInRequestMessage.Resource">
      <summary>Gets or sets the wres parameter of the message.</summary>
      <returns>The value of the wres parameter.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid, absolute URI occurs.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SignInRequestMessage.Validate">
      <summary>Validates the current instance.</summary>
      <exception cref="T:System.InvalidOperationException">The wa parameter (the <see cref="P:System.IdentityModel.Services.WSFederationMessage.Action" /> property) is not set to “wsignin1.0”. </exception>
      <exception cref="T:System.IdentityModel.Services.WSFederationMessageException">Neither the wtrealm parameter nor the wreply parameter is present. (The <see cref="P:System.IdentityModel.Services.SignInRequestMessage.Realm" /> property and the <see cref="P:System.IdentityModel.Services.SignInRequestMessage.Reply" /> property are null or empty.)-or-Both the wreq parameter and the wreqptr parameter are present. (The <see cref="P:System.IdentityModel.Services.SignInRequestMessage.Request" /> property and the <see cref="P:System.IdentityModel.Services.SignInRequestMessage.RequestPtr" /> property are both set.)</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SignInRequestMessage.Write(System.IO.TextWriter)">
      <summary>Writes this message in query string form to the specified text writer.</summary>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> to which to write the message.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is null.</exception>
    </member>
    <member name="T:System.IdentityModel.Services.SignInResponseMessage">
      <summary>Represents a WS-Federation Sign-In Response message.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SignInResponseMessage.#ctor(System.Uri,System.IdentityModel.Protocols.WSTrust.RequestSecurityTokenResponse,System.IdentityModel.Services.WSFederationSerializer,System.IdentityModel.Protocols.WSTrust.WSTrustSerializationContext)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SignInResponseMessage" /> class by using the specified base URL, response message object, federation serializer, and serialization context.</summary>
      <param name="baseUrl">The base URL to which the sign-in response message applies.</param>
      <param name="response">The <see cref="T:System.IdentityModel.Protocols.WSTrust.RequestSecurityTokenResponse" /> to be returned.</param>
      <param name="federationSerializer">The <see cref="T:System.IdentityModel.Services.WSFederationSerializer" /> to use to serialize the response.</param>
      <param name="context">The <see cref="T:System.IdentityModel.Protocols.WSTrust.WSTrustSerializationContext" /> that contains the context for the serialization.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="baseUrl" /> is null. -or-<paramref name="Response" /> is null. -or-<paramref name="federationSerializer" /> is null. -or-<paramref name="context" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SignInResponseMessage.#ctor(System.Uri,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SignInResponseMessage" /> class with the specified base URL and wresult parameter.</summary>
      <param name="baseUrl">The base URL to which the Sign-In Response message applies.</param>
      <param name="result">The wresult parameter in the message.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="result" /> is null or empty.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SignInResponseMessage.#ctor(System.Uri,System.Uri)">
      <summary>Initializes an instance of the <see cref="T:System.IdentityModel.Services.SignInResponseMessage" /> class using the specified base URL and wresultptr parameter.</summary>
      <param name="baseUrl">The base URL to which the Sign-In Response message applies.</param>
      <param name="resultPtr">The wresultptr parameter in the message.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="resultPtr" /> is null.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SignInResponseMessage.Result">
      <summary>Gets or sets the wresult parameter of the message.</summary>
      <returns>The value of the wresult parameter.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.SignInResponseMessage.ResultPtr">
      <summary>Gets or sets the wresultptr parameter of the message.</summary>
      <returns>The value of the wresultptr parameter.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid, absolute URI occurs. Can be null or empty.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SignInResponseMessage.Validate">
      <summary>Validates the current instance. </summary>
      <exception cref="T:System.InvalidOperationException">The action parameter (wa) is not “wsignin1.0”.</exception>
      <exception cref="T:System.IdentityModel.Services.WSFederationMessageException">Neither the wresult parameter nor the wresultptr parameter is specified-or-Both the wresult parameter and the wresultptr parameter are specified.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SignInResponseMessage.Write(System.IO.TextWriter)">
      <summary>Writes this message in a form post format to the specified text writer.</summary>
      <param name="writer">The text writer to which to write the message.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is null.</exception>
    </member>
    <member name="T:System.IdentityModel.Services.SignOutCleanupRequestMessage">
      <summary>Represents a WS-Federation Sign-Out Cleanup message. The message is created when the received message has the action parameter (wa) set to “wsignoutcleanup1.0”.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SignOutCleanupRequestMessage.#ctor(System.Uri)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SignOutCleanupRequestMessage" /> class with the specified base URL.</summary>
      <param name="baseUrl">The base URL to which this message applies.</param>
    </member>
    <member name="M:System.IdentityModel.Services.SignOutCleanupRequestMessage.#ctor(System.Uri,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SignOutCleanupRequestMessage" /> class with the specified base URL and wreply parameter.</summary>
      <param name="baseUrl">The base URL to which this message applies.</param>
      <param name="reply">The value of the wreply parameter. The URL to which the reply should be sent.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="reply" /> is either empty or null.-or-<paramref name="reply" /> is not a valid URI.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SignOutCleanupRequestMessage.Reply">
      <summary>Gets or sets the wreply parameter of the message.</summary>
      <returns>The value of the wreply parameter. This is the URL to which the reply should be sent.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid, absolute URI occurs.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SignOutCleanupRequestMessage.Validate">
      <summary>Validates the current instance.</summary>
      <exception cref="T:System.InvalidOperationException">The wa parameter (the <see cref="P:System.IdentityModel.Services.WSFederationMessage.Action" /> property) is not set to “wsignoutcleanup1.0”.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SignOutCleanupRequestMessage.Write(System.IO.TextWriter)">
      <summary>Writes the message in query string form to the specified text writer.</summary>
      <param name="writer">The writer to which to write the message.</param>
      <exception cref="T:System.ArgumentNullExcetion">
        <paramref name="writer" /> is null.</exception>
    </member>
    <member name="T:System.IdentityModel.Services.SignOutRequestMessage">
      <summary>Represents a WS-Federation sign-out message. This message is created when the received message has the action parameter (wa) set to “wsignout1.0”.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.SignOutRequestMessage.#ctor(System.Uri)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SignOutRequestMessage" /> class with the specified base URL.</summary>
      <param name="baseUrl">The base URL to which this message applies. Sets the  <see cref="" /> property.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="baseUrl" /> is null.</exception>
      <exception cref="T:System.IdentityModel.Services.WSFederationMessageException">
        <paramref name="baseUrl" /> is not a valid, absolute URI.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SignOutRequestMessage.#ctor(System.Uri,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.SignOutRequestMessage" /> class with the specified base URL and wreply parameter.</summary>
      <param name="baseUrl">The base URL to which this message applies.</param>
      <param name="reply">The value of the wreply parameter. The URL to which the reply should be sent.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="reply" /> is either empty or null.-or-<paramref name="reply" /> is not a valid, absolute URI.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="baseUrl" /> is null.</exception>
      <exception cref="T:System.IdentityModel.Services.WSFederationMessageException">
        <paramref name="baseUrl" /> is not a valid, absolute URI.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.SignOutRequestMessage.Reply">
      <summary>Gets or sets the wreply parameter of the message.</summary>
      <returns>The value of the wreply parameter. This is the URL to which the browser should be redirected.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is not a valid, absolute URI occurs.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SignOutRequestMessage.Validate">
      <summary>Validates the current instance.</summary>
      <exception cref="T:System.InvalidOperationException">The wa parameter (the <see cref="P:System.IdentityModel.Services.WSFederationMessage.Action" /> property) is not set to “wsignout1.0”.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.SignOutRequestMessage.Write(System.IO.TextWriter)">
      <summary>Writes the message in query string form to the specified text writer.</summary>
      <param name="writer">The writer to which to write the message.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is null.</exception>
    </member>
    <member name="T:System.IdentityModel.Services.WSFederationAuthenticationModule">
      <summary>The <see cref="T:System.IdentityModel.Services.WSFederationAuthenticationModule" /> is an HTTP module which is used to secure an ASP.NET application by enforcing  federated authentication settings on incoming requests. The <see cref="T:System.IdentityModel.Services.WSFederationAuthenticationModule" /> is the main module that WIF offers out of the box for handling claims-based identity access in ASP.NET  applications. The <see cref="T:System.IdentityModel.Services.WSFederationAuthenticationModule" /> raises several events, which allows ASP.NET developers to change the default behavior and control the details of how authentication and claims processing take place. The <see cref="T:System.IdentityModel.Services.WSFederationAuthenticationModule" /> functionality is divided into task-specific methods.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.#ctor">
      <summary>Called by constructors in derived classes to initialize the <see cref="T:System.IdentityModel.Services.WSFederationAuthenticationModule" /> class.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.AuthenticationType">
      <summary>Gets or sets the value of the wauth parameter to use in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>A URI that specifies the authentication type.</returns>
    </member>
    <member name="E:System.IdentityModel.Services.WSFederationAuthenticationModule.AuthorizationFailed">
      <summary>Occurs when the module is determining whether it should redirect the user to the configured issuer to authenticate.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.CanReadSignInResponse(System.Web.HttpRequestBase)">
      <summary>Returns a value that indicates whether the specified HTTP request is a WS-Federation sign-in response message. If the message is a WS-Federation sign-out clean-up message (“wsignoutcleanup1.0”), this method processes the request.</summary>
      <returns>true if the specified HTTP request contains a form POST in which the wa parameter is set to “wsignout1.0” and the wresult parameter is not empty; otherwise false.</returns>
      <param name="request">The incoming HTTP request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.CanReadSignInResponse(System.Web.HttpRequestBase,System.Boolean)">
      <summary>Returns a value that indicates whether the specified HTTP request is a WS-Federation sign-in response message. If the message is a WS-Federation sign-out clean-up message (“wsignoutcleanup1.0”), this method processes the request.</summary>
      <returns>true if the specified HTTP request contains a FORM post in which the wa parameter is set to “wsignout1.0” and the wresult parameter is not empty; otherwise false.</returns>
      <param name="request">The incoming HTTP request.</param>
      <param name="onPage">true if the call originates while processing a page request. false if the caller is an HTTP module. This parameter determines how to terminate processing or send redirects if the incoming message is a WS-Federation sign-out cleanup message (“wsignoutcleanup1.0”).</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.CreateSignInRequest(System.String,System.String,System.Boolean)">
      <summary>Creates a WS-Federation sign in request message by using the WS-Federation parameters configured on the module.</summary>
      <returns>The WS-Federation sign-in request message.</returns>
      <param name="uniqueId">The WSFAM saves this value in the wctx parameter in the WS-Federation sign in request; however, the module does not use it when processing sign-in requests or sign-in responses. You can set it to any value. It does not have to be unique.</param>
      <param name="returnUrl">The URL to which the module should return upon authentication.</param>
      <param name="rememberMeSet">The WSFAM saves this value in the wctx parameter in the WS-Federation sign in request; however, the module does not use it when processing sign-in requests or sign-in responses. You can set it either true or false.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Issuer" /> property is null or an empty string.-or-The <see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Realm" /> property is null or an empty string.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.FederatedSignOut(System.Uri,System.Uri)">
      <summary>Signs out at the specified security token service (STS) by using the WS-Federation protocol.</summary>
      <param name="signOutUrl">The URL of the STS to receive the WS-Federation sign-out request message. Cannot be null.</param>
      <param name="replyUrl">The URL to be sent as the wreply value in the sign-out request message.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="signOutUrl" /> is not null and is not an absolute URI.-or-<paramref name="replyUrl" /> is not null and is not an absolute URI.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="signOutUrl" /> is null.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Freshness">
      <summary>Gets or sets the value of the wfresh parameter to use in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>The desired maximum age of authentication requests, in minutes.CautionIn the next release of .NET Framework 4.5, the <see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Freshness" /> property will be of type <see cref="T:System.String" /> and its default value will be null.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.GetFederationPassiveSignOutUrl(System.String,System.String,System.String)">
      <summary>Returns a URL that represents a WS-Federation sign-out request addressed to the specified issuer and that contains the specified wreply parameter and the specified additional parameters.</summary>
      <returns>A URL that contains a WS-Federation passive sign-out request that is built by using the specified parameters.</returns>
      <param name="issuer">The issuer address. The address of the security token service (STS) to which to direct the request.</param>
      <param name="signOutReply">A URL that specifies the address to return to after sign-out. This sets the wreply parameter in the sign-out request. Can be null or empty if no wreply parameter should be included in the generated sign-out request. This should be an absolute URI.</param>
      <param name="signOutQueryString">Additional query string parameters to be included in the sign-out request. Can be null or empty if no additional to be parameters included in the generated sign-out request. This should be a relative URI.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="issuer" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.GetReferencedResult(System.String)">
      <summary>Gets the issuance result (typically the issued token) from the specified URL. Resolves the URL specified in the wresultptr parameter in a sign-in response message.</summary>
      <returns>The issuance result that was referenced by the URL.</returns>
      <param name="resultPtr">The URL that specifies the address of the issuance result.</param>
      <exception cref="T:System.NotSupportedException">This method is not supported in the base class. You must override it in a derived class.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.GetReturnUrlFromResponse(System.Web.HttpRequestBase)">
      <summary>Extracts the URL of the page that was originally requested from the sign-in response.</summary>
      <returns>The URL of the page that was originally requested by the client. This is the URL (at the relying party) to which the client should be redirected following successful sign-in.</returns>
      <param name="request">The HTTP request that contains a form POST, which contains the WS-Federation sign-in response message.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.GetSecurityToken(System.IdentityModel.Services.SignInResponseMessage)">
      <summary>Reads a security token from the specified WS Federation sign-in response message.</summary>
      <returns>The security token that was read from the message.</returns>
      <param name="message">The sign-in response message from which to read the token.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="message" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.GetSecurityToken(System.Web.HttpRequestBase)">
      <summary>Reads a security token from the specified HTTP request.</summary>
      <returns>The security token that was read.</returns>
      <param name="request">The HTTP request from which to read the token.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.GetSessionTokenContext">
      <summary>Gets a string that should be persisted with the session cookie in the <see cref="P:System.IdentityModel.Tokens.SessionSecurityToken.Context" /> property.</summary>
      <returns>The string to persist in the session cookie.</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Issuer" /> property is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.GetSignInResponseMessage(System.Web.HttpRequestBase)">
      <summary>Reads a <see cref="T:System.IdentityModel.Services.SignInResponseMessage" /> object from the form POST represented by the specified HTTP request.</summary>
      <returns>The sign-in response message that was created.</returns>
      <param name="request">The HTTP request from which to read the sign-in response message. The request should represent a form POST.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">A sign-in response message could not be read from the specified request.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.GetSignOutRedirectUrl(System.IdentityModel.Services.SignOutCleanupRequestMessage)">
      <summary>Determines the URL to which to redirect when processing a WS-Federation sign-out clean-up request (wsignoutcleanup1.0) that contains a wreply parameter.</summary>
      <returns>The URL to redirect to.</returns>
      <param name="signOutMessage">The sign-out clean-up request.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="signOutMessage" /> has a <see cref="P:System.IdentityModel.Services.SignOutCleanupRequestMessage.Reply" /> property that is null or empty. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="signOutMessage" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.GetXmlTokenFromMessage(System.IdentityModel.Services.SignInResponseMessage)">
      <summary>Extracts the issued token from the specified WS-Federation sign-in response message.</summary>
      <returns>A string that contains the XML that represents the issued token. This is a &lt;wst:RequestSecurityTokenResponse&gt; element.</returns>
      <param name="message">The sign-in response message to extract the token from. The sign-in response contains the token issued by the STS.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="message" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="message" /> does not contain a token. (It does not contain a valid wresult or wresultptr parameter.)</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.GetXmlTokenFromMessage(System.IdentityModel.Services.SignInResponseMessage,System.IdentityModel.Services.WSFederationSerializer)">
      <summary>Extracts the issued token from the specified WS-Federation sign-in response message by using the specified WS-Federation serializer.</summary>
      <returns>A string that contains the XML that represents the issued token. This is a &lt;wst:RequestSecurityTokenResponse&gt; element.</returns>
      <param name="message">The sign-in response message to extract the token from. The sign-in response contains the token issued by the STS.</param>
      <param name="federationSerializer">The WS-Federation serializer to use to de-serialize the sign-in response message.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="message" /> is null.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.HomeRealm">
      <summary>Gets or sets the value of the whr parameter to use in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>The address of the identity provider.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.InitializeModule(System.Web.HttpApplication)">
      <summary>Initializes the module and prepares it to handle events from the module's ASP.NET application object.</summary>
      <param name="context">The HTTP application object that contains this module.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="context" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.InitializePropertiesFromConfiguration">
      <summary>Initializes the module properties based on the configuration specified by the <see cref="P:System.IdentityModel.Services.HttpModuleBase.FederationConfiguration" /> property of the module.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.IsSignInResponse(System.Web.HttpRequestBase)">
      <summary>Gets a value that indicates whether the specified request is a WS-Federation sign-in response message.</summary>
      <returns>true if the request is a WS-Federation sign-in response message; otherwise false.</returns>
      <param name="request">The incoming HTTP request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is null.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Issuer">
      <summary>Gets or sets a URI that identifies the intended issuer of the security token.</summary>
      <returns>The URI that identifies the security token service (STS).</returns>
      <exception cref="T:System.ArgumentException">An attempt to set the property to null or an empty string occurs.-or-An attempt to set the property to a value that is not a valid, absolute URI occurs.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.OnAuthenticateRequest(System.Object,System.EventArgs)">
      <summary>Handles the <see cref="E:System.Web.HttpApplication.AuthenticateRequest" /> event from the ASP.NET pipeline.</summary>
      <param name="sender">The source for the event. This will be an <see cref="T:System.Web.HttpApplication" /> object.</param>
      <param name="args">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.OnAuthorizationFailed(System.IdentityModel.Services.AuthorizationFailedEventArgs)">
      <summary>Raises the <see cref="E:System.IdentityModel.Services.WSFederationAuthenticationModule.AuthorizationFailed" /> event.</summary>
      <param name="e">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.OnEndRequest(System.Object,System.EventArgs)">
      <summary>Handles the <see cref="E:System.Web.HttpApplication.EndRequest" /> event from the ASP.NET pipeline.</summary>
      <param name="sender">The source for the event. This will be an <see cref="T:System.Web.HttpApplication" /> object.</param>
      <param name="args">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.OnPostAuthenticateRequest(System.Object,System.EventArgs)">
      <summary>Handles the <see cref="E:System.Web.HttpApplication.PostAuthenticateRequest" /> event from the ASP.NET pipeline.</summary>
      <param name="sender">The source for the event. This will be an <see cref="T:System.Web.HttpApplication" /> object.</param>
      <param name="e">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.OnRedirectingToIdentityProvider(System.IdentityModel.Services.RedirectingToIdentityProviderEventArgs)">
      <summary>Raises the <see cref="E:System.IdentityModel.Services.WSFederationAuthenticationModule.RedirectingToIdentityProvider" /> event.</summary>
      <param name="e">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.OnSessionSecurityTokenCreated(System.IdentityModel.Services.SessionSecurityTokenCreatedEventArgs)">
      <summary>Raises the <see cref="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SessionSecurityTokenCreated" /> event.</summary>
      <param name="args">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.OnSignedIn(System.EventArgs)">
      <summary>Raises the <see cref="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SignedIn" /> event.</summary>
      <param name="args">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.OnSignedOut(System.EventArgs)">
      <summary>Raises the <see cref="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SignedOut" /> event.</summary>
      <param name="args">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.OnSignInError(System.IdentityModel.Services.ErrorEventArgs)">
      <summary>Raises the <see cref="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SignInError" /> event.</summary>
      <param name="args">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.OnSigningOut(System.IdentityModel.Services.SigningOutEventArgs)">
      <summary>Raises the <see cref="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SigningOut" /> event.</summary>
      <param name="args">The data for the event.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.OnSignOutError(System.IdentityModel.Services.ErrorEventArgs)">
      <summary>Raises the <see cref="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SignOutError" /> event.</summary>
      <param name="args">The data for the event.</param>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.PassiveRedirectEnabled">
      <summary>Gets or sets a value that specifies whether the module is enabled to initiate WS-Federation protocol redirects.</summary>
      <returns>true to enable redirects; otherwise, false.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.PersistentCookiesOnPassiveRedirects">
      <summary>Gets or sets a value that specifies whether a persistent session cookie is issued on successful authentication.</summary>
      <returns>true to issue a persistent session cookie; otherwise, false.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Policy">
      <summary>Gets or sets the value of the wp parameter to be used in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>A URL that identifies the policy to use.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Realm">
      <summary>Gets or sets the value of the wtrealm parameter to be used for WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>A URI that identifies the relying party (RP) to the security token service (STS).</returns>
      <exception cref="T:System.ArgumentException">An attempt to set the property to null or an empty string occurs.-or-An attempt to set the property to a value that is not a valid, absolute URI occurs.</exception>
    </member>
    <member name="E:System.IdentityModel.Services.WSFederationAuthenticationModule.RedirectingToIdentityProvider">
      <summary>Occurs when the module is going to redirect the user to the identity provider.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.RedirectToIdentityProvider(System.String,System.String,System.Boolean)">
      <summary>Redirects the user to the security token service (STS) specified by the <see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Issuer" /> property to obtain a security token using the WS-Federation protocol.</summary>
      <param name="uniqueId">The WSFAM saves this value in the wctx parameter in the WS-Federation sign in request; however, the module does not use it when processing sign-in requests or sign-in responses. You can set it to any value. It does not have to be unique. For more information, see the <see cref="M:System.IdentityModel.Services.WSFederationAuthenticationModule.CreateSignInRequest(System.String,System.String,System.Boolean)" /> method.</param>
      <param name="returnUrl">The URL to which the module should return upon authentication.</param>
      <param name="persist">The WSFAM saves this value in the wctx parameter in the WS-Federation sign in request; however, the module does not use it when processing sign-in requests or sign-in responses. You can set it either true or false.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Issuer" /> is null or an empty string.-or-<see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Realm" /> is null or an empty string.-or-The <see cref="P:System.Web.HttpContext.Current" /> property is null or empty. -or-The <see cref="P:System.Web.HttpContext.Response" /> property of the context returned by <see cref="P:System.Web.HttpContext.Current" /> is null or empty.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Reply">
      <summary>Gets or sets the value of the wreply parameter to use in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>A URL that identifies the address at which the relying party (RP) application would like to receive replies from the Security Token Service (STS).</returns>
      <exception cref="T:System.ArgumentException">An attempt to set the property to a value that is not a valid, absolute URI occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Request">
      <summary>Gets or sets the value of the wreq parameter to use in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>The token issuance request expressed as a &lt;wst:RequestSecurityToken&gt; element.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.RequestPtr">
      <summary>Gets or sets the value of the wreqptr parameter to use in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>A URL that specifies the location of the token issuance request expressed as a &lt;wst:RequestSecurityToken&gt; element</returns>
      <exception cref="T:System.ArgumentException">An attempt to set the property to a value that is not a valid, absolute URI occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.RequireHttps">
      <summary>Gets or sets a value that specifies whether communication with the security token service (STS) must use HTTPS protocol.</summary>
      <returns>true if communication with the STS must be secured using HTTPS; otherwise, false.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Resource">
      <summary>Get or sets the value of the wres parameter to use in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>A URI that identifies the resource being accessed, the relying party (RP), to the to the security token service (STS).</returns>
    </member>
    <member name="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SecurityTokenReceived">
      <summary>Occurs when a security token has been received from a security token service (STS).</summary>
    </member>
    <member name="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SecurityTokenValidated">
      <summary>Occurs after a security token that was received from the security token service (STS) has been validated but before the session security token is created.</summary>
    </member>
    <member name="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SessionSecurityTokenCreated">
      <summary>Occurs when a session security token has been created from the security token received from a security token service (STS).</summary>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.SetPrincipalAndWriteSessionToken(System.IdentityModel.Tokens.SessionSecurityToken,System.Boolean)">
      <summary>Sets the thread principal and optionally writes the session cookie.</summary>
      <param name="sessionToken">The session security token that was created from the WS-Federation sign-in response message from the STS. </param>
      <param name="isSession">true to write a cookie that represents the session included with the response; otherwise, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sessionToken" /> is null.</exception>
    </member>
    <member name="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SignedIn">
      <summary>Occurs after the user is signed in.</summary>
    </member>
    <member name="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SignedOut">
      <summary>Occurs just after deleting the session during sign-out.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.SignIn(System.String)">
      <summary>Performs sign-in to a security token service (STS) through the WS-Federation protocol.</summary>
      <param name="ControlId">The WSFAM saves this value in the wctx parameter in the WS-Federation sign in request; however, the module does not use it when processing sign-in requests or sign-in responses. You can set it to any value. It does not have to be unique. For more information, see the <see cref="M:System.IdentityModel.Services.WSFederationAuthenticationModule.CreateSignInRequest(System.String,System.String,System.Boolean)" /> method.</param>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.SignInContext">
      <summary>Gest or sets an application specific context value to be included in the wctx parameter in WS-Federation sign-in requests.</summary>
      <returns>The application specific context value to be included in the wctx parameter for sign-in requests.</returns>
    </member>
    <member name="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SignInError">
      <summary>Raised when an error during sign-in occurs.</summary>
    </member>
    <member name="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SigningOut">
      <summary>Occurs before deleting the session during sign-out.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.SignInQueryString">
      <summary>Gets or sets a query string that contains any additional parameters to be sent in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>A URL fragment that contains the additional message parameters in the following format: param1=value1&amp;param2=value2&amp;paramN=valueN. The default is an empty string.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set the property to null occurs.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.SignOut">
      <summary>Signs out of the current session and requests a redirect back to the URL specified in the current HTTP request.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.SignOut(System.Boolean)">
      <summary>Signs out of the current session and raises the appropriate events.</summary>
      <param name="isIPRequest">true if the request was initiated by the IP-STS via a WS-Federation sign-out cleanup request message (“wsignoutcleanup1.0”); otherwise, false.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.SignOut(System.String)">
      <summary>Signs out of the current session and requests a redirect back to the specified URL.</summary>
      <param name="redirectUrl">The URL to which the browser should be redirected after the session is deleted.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="redirectUrl" /> is not a valid URI.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.SignOut(System.String,System.Boolean)">
      <summary>Signs out of the current session and requests a redirect back to the specified URL.</summary>
      <param name="redirectUrl">The URL to which the browser should be redirected after sign-out.</param>
      <param name="initiateSignoutCleanup">Always set false. Setting this parameter to true is not supported.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="redirectUrl" /> is not a valid URI.</exception>
      <exception cref="T:System.NotImplementedException">The <see cref="T:System.IdentityModel.Services.WSFederationAuthenticationModule" /> class throws this exception if <paramref name="initiateSignoutCleanup" /> is true. Do not set this parameter to true.</exception>
    </member>
    <member name="E:System.IdentityModel.Services.WSFederationAuthenticationModule.SignOutError">
      <summary>Raised when an error occurs during sign-out.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.SignOutQueryString">
      <summary>Gets or sets a query string that contains any additional parameters to be sent in WS-Federation sign-out requests (“wsignout1.0”).</summary>
      <returns>A URL fragment that contains the additional message parameters in the following format: param1=value1&amp;param2=value2&amp;paramN=valueN. The default is an empty string.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set the property to null occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.SignOutReply">
      <summary>Gets or sets the value of the wreply parameter to use during WS-Federation sign-out requests (“wsignout1.0”). </summary>
      <returns>The URL to which the client should be redirected by the security token service (STS) following sign-out through the WS-Federation protocol.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationAuthenticationModule.VerifyProperties">
      <summary>Verifies that the <see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Issuer" /> and <see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Realm" /> properties are non-empty, and, that, if the <see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.RequireHttps" /> property is true, that the URIs specified for <see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Issuer" /> and <see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Realm" /> are HTTPS-compliant.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Issuer" /> is null or an empty string.-or-<see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Realm" /> is null or an empty string.-or-<see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.RequireHttps" /> is true and <see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Issuer" /> not HTTPS-compliant.-or-<see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.RequireHttps" /> is true and <see cref="P:System.IdentityModel.Services.WSFederationAuthenticationModule.Realm" /> not HTTPS-compliant.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationAuthenticationModule.XmlDictionaryReaderQuotas">
      <summary>Gets or sets the <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> object to use when deserializing WS-Federation sign-in response messages to get the token issued by the security token service (STS).</summary>
      <returns>The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> object to use when deserializing WS-Federation sign-in response messages to get the token issued by the security token service (STS).</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set the property to null occurs.</exception>
    </member>
    <member name="T:System.IdentityModel.Services.WSFederationMessage">
      <summary>Represents a WS-Federation message and defines the base class from which more specialized WS-Federation message classes derive.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationMessage.#ctor(System.Uri,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.WSFederationMessage" /> class from the base URL to which the message applies and the action to be performed (the wa message parameter).</summary>
      <param name="baseUrl">The base URL to which the WS-Federation message applies. This is the URL without any query parameters. Sets the <see cref="P:System.IdentityModel.Services.FederationMessage.BaseUri" /> property.</param>
      <param name="action">The wa parameter of the message. Specifies the action to be performed; for example “wsignin1.0” for a WS-Federation sign-in request. Sets the <see cref="P:System.IdentityModel.Services.WSFederationMessage.Action" /> property.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="baseUri" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="action" /> is null or an empty string.</exception>
      <exception cref="T:System.IdentityModel.Services.WSFederationMessageException">
        <paramref name="baseUri" /> is not a valid, absolute URI.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationMessage.Action">
      <summary>Gets or sets the wa parameter of the message.</summary>
      <returns>The value of the wa parameter.</returns>
      <exception cref="T:System.ArgumentException">An attempt to set a value that is null or empty occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationMessage.Context">
      <summary>Gets or sets the wctx parameter of the message.</summary>
      <returns>The value of the wctx parameter.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationMessage.CreateFromFormPost(System.Web.HttpRequestBase)">
      <summary>Creates a WS-Federation message from the form post received in the specified request.</summary>
      <returns>The message that was created or null if a message cannot be created.</returns>
      <param name="request">The request that contains the form post.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationMessage.CreateFromNameValueCollection(System.Uri,System.Collections.Specialized.NameValueCollection)">
      <summary>Creates a WS-Federation message from a <see cref="T:System.Collections.Specialized.NameValueCollection" /> of parameters.</summary>
      <returns>The message that was created or null if a message cannot be created.</returns>
      <param name="baseUrl">The base URL to which the message is intended.</param>
      <param name="collection">The <see cref="T:System.Collections.Specialized.NameValueCollection" /> that contains the parameters for the message.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="baseUrl" /> is null. -or-<paramref name="collection" /> is null.</exception>
      <exception cref="T:System.IdentityModel.Services.WSFederationMessageException">The wa parameter in the parameter collection is not recognized.</exception>
      <exception cref="T:System.ArgumentException">A sign-in response message has both the wresult and wresultptr parameter in the parameter collection. (A valid sign-in response message has the wa parameter equal to “wsignin1.0” and either the wresult or the wresultptr parameter, but not both.)</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationMessage.CreateFromUri(System.Uri)">
      <summary>Creates a WS-Federation message from the specified URI. The parameters are assumed to be specified in the query string.</summary>
      <returns>The message that was created.</returns>
      <param name="requestUri">The URI from which to create the message. Message parameters are specified in the query string. The wa parameter must be present.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> is null.</exception>
      <exception cref="T:System.IdentityModel.Services.WSFederationMessageException">A message cannot be created from the specified URI.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.WSFederationMessage.Encoding">
      <summary>Gets or sets the wencoding parameter of the message.</summary>
      <returns>The value of the wencoding parameter. </returns>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationMessage.TryCreateFromUri(System.Uri,System.IdentityModel.Services.WSFederationMessage@)">
      <summary>Attempts to create a WS-Federation message from the specified URI. The parameters are assumed to be specified as a query string.</summary>
      <returns>true if a message was successfully created; otherwise, false.</returns>
      <param name="requestUri">The URI from which to create the message. Message parameters are specified in the query string. The wa parameter must be present.</param>
      <param name="fedMsg">When this method returns, contains the message that was created or null if a message could not be created. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> is null.</exception>
    </member>
    <member name="T:System.IdentityModel.Services.WSFederationMessageException">
      <summary>The exception that is thrown when an error occurs while serializing or deserializing a WS-Federation message.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationMessageException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.WSFederationMessageException" /> class.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationMessageException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.WSFederationMessageException" /> class with serialized data.</summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object that holds the serialized object data.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> object that contains the contextual information about the source or destination.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationMessageException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.WSFederationMessageException" /> class with a specified error message.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationMessageException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.WSFederationMessageException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <param name="inner">The <see cref="T:System.Exception" /> that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception.</param>
    </member>
    <member name="T:System.IdentityModel.Services.WSFederationSerializer">
      <summary>Provides methods to convert a WS-Federation message to <see cref="T:System.IdentityModel.Protocols.WSTrust.RequestSecurityToken" /> and <see cref="T:System.IdentityModel.Protocols.WSTrust.RequestSecurityTokenResponse" /> objects, which are WS-Trust protocol specific.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationSerializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.WSFederationSerializer" /> class.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationSerializer.#ctor(System.IdentityModel.Protocols.WSTrust.WSTrustRequestSerializer,System.IdentityModel.Protocols.WSTrust.WSTrustResponseSerializer)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.WSFederationSerializer" /> class with the specified request and response serializers.</summary>
      <param name="requestSerializer">The WS-Trust Serializer to use to read the request (RST).</param>
      <param name="responseSerializer">The WS-Trust Serializer to use to write the response (RSTR).</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestSerializer" /> is null.-or-<paramref name="responseSerializer" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationSerializer.#ctor(System.Xml.XmlDictionaryReader)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.WSFederationSerializer" /> class based on the namespace of the response XML.</summary>
      <param name="reader">An <see cref="T:System.Xml.XmlDictionaryReader" /> opened on the response XML.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationSerializer.CanReadRequest(System.String)">
      <summary>Checks whether the specified string is a wst:RequestSecurityToken message with a namespace that is recognized by the wrapped <see cref="T:System.IdentityModel.Protocols.WSTrust.WSTrustRequestSerializer" />.</summary>
      <returns>true if the message is a valid wst:RequestSecurityToken message with a namespace that matches the WS-Trust protocol that this <see cref="T:System.IdentityModel.Services.WSFederationSerializer" /> supports; otherwise, false.</returns>
      <param name="trustMessage">The WS-Trust message to check.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationSerializer.CanReadResponse(System.String)">
      <summary>Checks whether the specified string is a wst:RequestSecurityTokenResponse message with a namespace that is recognized by the wrapped <see cref="T:System.IdentityModel.Protocols.WSTrust.WSTrustResponseSerializer" />.</summary>
      <returns>true if the message is a valid wst:RequestSecurityTokenResponse message with a namespace that matches the WS-Trust protocol that this <see cref="T:System.IdentityModel.Services.WSFederationSerializer" /> supports; otherwise false.</returns>
      <param name="trustMessage">The WS-Trust message to check.</param>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationSerializer.CreateRequest(System.IdentityModel.Services.FederationMessage,System.IdentityModel.Protocols.WSTrust.WSTrustSerializationContext)">
      <summary>Converts a WS-Federation Sign-In Request message to a <see cref="T:System.IdentityModel.SecurityTokenService.RequestSecurityToken" /> object that the security token service (the SecurityTokenService class) can consume.</summary>
      <returns>A <see cref="T:System.IdentityModel.SecurityTokenService.RequestSecurityToken" /> object that represents the converted Sign-In Request message.</returns>
      <param name="message">The message to convert. This should be an instance of <see cref="T:System.IdentityModel.Services.SignInRequestMessage" />.</param>
      <param name="context">The current serialization context.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="message" /> is null.-or-<paramref name="context" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The message is not of type <see cref="T:System.IdentityModel.Services.SignInRequestMessage" />.-or-The <see cref="T:System.IdentityModel.Services.SignInRequestMessage" /> object is missing required information to construct a valid <see cref="T:System.IdentityModel.SecurityTokenService.RequestSecurityToken" /> object.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationSerializer.CreateResponse(System.IdentityModel.Services.FederationMessage,System.IdentityModel.Protocols.WSTrust.WSTrustSerializationContext)">
      <summary>Converts a WS-Federation Sign-In Response message object to a <see cref="T:System.IdentityModel.SecurityTokenService.RequestSecurityTokenResponse" /> object.</summary>
      <returns>A <see cref="T:System.IdentityModel.SecurityTokenService.RequestSecurityTokenResponse" /> object that represents the converted Sign-In Response message.</returns>
      <param name="message">The message to convert. This should be an instance of <see cref="T:System.IdentityModel.Services.SignInResponseMessage" />.</param>
      <param name="context">The current serialization context.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="message" /> is null.-or-<paramref name="context" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The message is not of type <see cref="T:System.IdentityModel.Services.SignInResponseMessage" />. -or-The <see cref="T:System.IdentityModel.Services.SignInResponseMessage" /> object is missing required information to construct a valid <see cref="T:System.IdentityModel.SecurityTokenService.RequestSecurityTokenResponse" /> object.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationSerializer.GetReferencedRequest(System.String)">
      <summary>Gets the wreq string from a referenced URL.</summary>
      <returns>The request data from the URL.</returns>
      <param name="wreqptr">The URL of the request data.</param>
      <exception cref="T:System.NotSupportedException">Thrown by the default implementation.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationSerializer.GetReferencedResult(System.String)">
      <summary>Gets the wresult string from a referenced URL.</summary>
      <returns>The request data from the URL.</returns>
      <param name="wresultptr">The URL of the result data.</param>
      <exception cref="T:System.NotSupportedException">Thrown by the default implementation.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationSerializer.GetRequestAsString(System.IdentityModel.Protocols.WSTrust.RequestSecurityToken,System.IdentityModel.Protocols.WSTrust.WSTrustSerializationContext)">
      <summary>Serializes the specified <see cref="T:System.IdentityModel.Protocols.WSTrust.RequestSecurityToken" /> object into a string.</summary>
      <returns>A serialized string representation of the <see cref="T:System.IdentityModel.Protocols.WSTrust.RequestSecurityToken" /> object.</returns>
      <param name="request">The <see cref="T:System.IdentityModel.Protocols.WSTrust.RequestSecurityToken" /> object to serialize.</param>
      <param name="context">The current serialization context.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> is null. -or-<paramref name="context" /> is null.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.WSFederationSerializer.GetResponseAsString(System.IdentityModel.Protocols.WSTrust.RequestSecurityTokenResponse,System.IdentityModel.Protocols.WSTrust.WSTrustSerializationContext)">
      <summary>Serializes the specified <see cref="T:System.IdentityModel.Protocols.WSTrust.RequestSecurityTokenResponse" /> object into a string.</summary>
      <returns>A serialized string representation of the <see cref="T:System.IdentityModel.Protocols.WSTrust.RequestSecurityTokenResponse" /> object.</returns>
      <param name="response">The <see cref="T:System.IdentityModel.Protocols.WSTrust.RequestSecurityTokenResponse" /> object to serialize.</param>
      <param name="context">The current serialization context.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="response" /> is null.-or-<paramref name="context" /> is null.</exception>
    </member>
    <member name="T:System.IdentityModel.Services.Configuration.FederationConfiguration">
      <summary>Exposes properties that contain federation settings that control the behavior of the WS-Federation Authentication Module (<see cref="T:System.IdentityModel.Services.WSFederationAuthenticationModule" />) and the Session Authentication Module (<see cref="T:System.IdentityModel.Services.SessionAuthenticationModule" />) in web applications.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.Configuration.FederationConfiguration.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.Configuration.FederationConfiguration" /> class by loading settings from the system.identityModel.services section of the configuration file.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.Configuration.FederationConfiguration.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.Configuration.FederationConfiguration" /> class by optionally loading settings from the system.identityModel.services section of the configuration file.</summary>
      <param name="loadConfig">true to initialize with settings loaded from the configuration file; false to initialize to default values.</param>
      <exception cref="T:System.InvalidOperationException">The system.identityModel configuration section is not defined in the configuration file.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.Configuration.FederationConfiguration.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.Configuration.FederationConfiguration" /> class from the &lt;federationConfiguration&gt; element with the specified name.</summary>
      <param name="federationConfigurationName">The name of the &lt;federationConfiguration&gt; element from which to load the configuration.</param>
      <exception cref="T:System.InvalidOperationException">There is no system.identityModel.services section defined in the configuration file.-or-There is no system.identityModel section defined in the configuration file.-or-There is no &lt;federationConfiguration&gt; element with the specified name in the system.identityModel.services section. </exception>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfiguration.CookieHandler">
      <summary>Gets or sets the cookie handler to be used by the Session Authentication Module (<see cref="T:System.IdentityModel.Services.SessionAuthenticationModule" />).</summary>
      <returns>The cookie handler to be used by the Session Authentication Module (SAM).</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set the property to null occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfiguration.CustomElement">
      <summary>Gets or sets the custom XML element, if any, present in this configuration.</summary>
      <returns>The custom XML element.</returns>
    </member>
    <member name="F:System.IdentityModel.Services.Configuration.FederationConfiguration.DefaultFederationConfigurationName">
      <summary>Defines the name of the default &lt;federationConfiguration&gt; element from which settings should be loaded. This is an empty string as the default element is unnamed.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfiguration.IdentityConfiguration">
      <summary>Gets or sets the <see cref="T:System.IdentityModel.Configuration.IdentityConfiguration" /> object associated with this instance.</summary>
      <returns>The identity configuration object associated with this instance.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set the property to null occurs.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.Configuration.FederationConfiguration.Initialize">
      <summary>Initializes the current instance.</summary>
      <exception cref="T:System.InvalidOperationException">The current instance has already been initialized (The <see cref="P:System.IdentityModel.Services.Configuration.FederationConfiguration.IsInitialized" /> property is true.)</exception>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfiguration.IsInitialized">
      <summary>Gets or sets a value that indicates whether the <see cref="M:System.IdentityModel.Services.Configuration.FederationConfiguration.Initialize" /> method has been called.</summary>
      <returns>true if the configuration object has been configured; otherwise false.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.Configuration.FederationConfiguration.LoadConfiguration(System.IdentityModel.Services.Configuration.FederationConfigurationElement)">
      <summary>Loads the properties for the current instance from the configuration file. This method is called by constructors that initialize the newly created <see cref="T:System.IdentityModel.Services.Configuration.FederationConfiguration" /> from configuration settings.</summary>
      <param name="element">The &lt;federationConfiguration&gt; element to load settings from. Can be null to load default values.</param>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfiguration.Name">
      <summary>Gets the name associated with this instance.</summary>
      <returns>The name associated with this federation configuration instance.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfiguration.ServiceCertificate">
      <summary>Gets or sets the X.509 certificate used to sign WS-Federation protocol messages.</summary>
      <returns>The X.509 certificate used to sign WS-Federation protocol messages.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfiguration.WsFederationConfiguration">
      <summary>Gets or sets the configuration settings for the WS-Federation Authentication Module (<see cref="T:System.IdentityModel.Services.WSFederationAuthenticationModule" />).</summary>
      <returns>The configuration settings for the WS-Federation Authentication Module.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set the property to null occurs.</exception>
    </member>
    <member name="T:System.IdentityModel.Services.Configuration.FederationConfigurationCreatedEventArgs">
      <summary>Provides data for the <see cref="E:System.IdentityModel.Services.FederatedAuthentication.FederationConfigurationCreated" /> event.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.Configuration.FederationConfigurationCreatedEventArgs.#ctor(System.IdentityModel.Services.Configuration.FederationConfiguration)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.Configuration.FederationConfigurationCreatedEventArgs" /> class with the specified federation configuration object.</summary>
      <param name="config">The federation configuration object. A <see cref="T:System.IdentityModel.Services.Configuration.FederationConfiguration" /> object contains the configurable properties for the WS-Federation Authentication Module (<see cref="T:System.IdentityModel.Services.WSFederationAuthenticationModule" />) and the Session Authentication Module (<see cref="T:System.IdentityModel.Services.SessionAuthenticationModule" />) in a web application.</param>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfigurationCreatedEventArgs.FederationConfiguration">
      <summary>Gets or sets the federation configuration object that was initialized from the configuration file.</summary>
      <returns>The federation configuration object that was initialized from configuration.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.Configuration.FederationConfigurationElement">
      <summary>Represents the &lt;federationConfiguration&gt; element in a configuration file. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.Configuration.FederationConfigurationElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.Configuration.FederationConfigurationElement" /> class.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfigurationElement.CookieHandler">
      <summary>Gets or sets the child &lt;cookieHandler&gt; element.</summary>
      <returns>The child &lt;cookieHandler&gt; element.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfigurationElement.CustomElement">
      <summary>Gets or sets a custom child configuration element, if one exists.</summary>
      <returns>The custom configuration element.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfigurationElement.IdentityConfigurationName">
      <summary>Gets or sets the identityConfigurationName attribute.</summary>
      <returns>The name of the &lt;identityConfiguration&gt; section to associate with this &lt;federationConfiguration&gt; element.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfigurationElement.IsConfigured">
      <summary>Gets a value that indicates whether this element has been configured with non-default values.</summary>
      <returns>true if the element has been configured with non-default values; otherwise, false.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfigurationElement.Name">
      <summary>Gets or sets the name attribute.</summary>
      <returns>The name of this federation configuration element.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfigurationElement.ServiceCertificate">
      <summary>Gets or sets the child &lt;serviceCertificate&gt; element.</summary>
      <returns>The child &lt;serviceCertificate&gt; element.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.FederationConfigurationElement.WsFederation">
      <summary>Gets or sets the child &lt;wsFederation&gt; element.</summary>
      <returns>The child &lt;wsFederation&gt; element.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.Configuration.FederationConfigurationElementCollection">
      <summary>Contains a collection of all of the &lt;federationConfiguration&gt; elements that are specified in the configuration file. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.Configuration.FederationConfigurationElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.Configuration.FederationConfigurationElementCollection" /> class.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.Configuration.FederationConfigurationElementCollection.GetElement(System.String)">
      <summary>Retrieves the &lt;federationConfiguration&gt; element that has the specified name.</summary>
      <returns>The &lt;federationConfiguration&gt; element that has the specified name.</returns>
      <param name="name">The name of the &lt;federationConfiguration&gt; element to retrieve.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">No element with the specified name was found.</exception>
    </member>
    <member name="T:System.IdentityModel.Services.Configuration.SystemIdentityModelServicesSection">
      <summary>Represents the &lt;system.identityModel.services&gt; section in a configuration file. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.Configuration.SystemIdentityModelServicesSection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.Configuration.SystemIdentityModelServicesSection" /> class.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.SystemIdentityModelServicesSection.Current">
      <summary>Gets a reference to the &lt;system.identityModel.services&gt; section from the configuration file.</summary>
      <returns>The &lt;system.identityModel.services&gt; section from the configuration file. null if the configuration file does not contain a &lt;system.identityModel.services&gt; section.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.SystemIdentityModelServicesSection.DefaultFederationConfigurationElement">
      <summary>Gets the unnamed &lt;federationConfiguration&gt; element from the configuration file. </summary>
      <returns>The unnamed &lt;federationConfiguration&gt; element from the configuration file. null if the configuration file does not contain a &lt;system.identityModel.services&gt; section.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.SystemIdentityModelServicesSection.FederationConfigurationElements">
      <summary>Gets the collection of &lt;federationConfiguration&gt; elements configured in this &lt;system.identityModel.services&gt; section.</summary>
      <returns>A collection that contains all of the &lt;federationConfiguration&gt; elements configured in this &lt;system.identityModel.services&gt; section.</returns>
    </member>
    <member name="F:System.IdentityModel.Services.Configuration.SystemIdentityModelServicesSection.SectionName">
      <summary>A constant that defines the name of the configuration section; “system.identityModel.services”.</summary>
    </member>
    <member name="T:System.IdentityModel.Services.Configuration.WsFederationConfiguration">
      <summary>Contains all of the configuration settings needed by the WS-Federation Authentication Module (<see cref="T:System.IdentityModel.Services.WSFederationAuthenticationModule" />). </summary>
    </member>
    <member name="M:System.IdentityModel.Services.Configuration.WsFederationConfiguration.#ctor(System.IdentityModel.Services.Configuration.WSFederationElement)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.Configuration.WsFederationConfiguration" /> class from the specified &lt;wsFederation&gt; element.</summary>
      <param name="federationElement">The &lt;wsFederation&gt; element from which to initialize the new instance.</param>
    </member>
    <member name="M:System.IdentityModel.Services.Configuration.WsFederationConfiguration.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.Configuration.WsFederationConfiguration" /> class by using the specified issuer and realm.</summary>
      <param name="issuer">A string that contains the URI of the issuer to be used by the WS-Federation Authentication Module (WSFAM).</param>
      <param name="realm">A string that contains the URI of the requesting realm to be used by the WSFAM.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="issuer" /> is null.-or-<paramref name="realm" /> is null.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.AuthenticationType">
      <summary>Gets or sets the value of the wauth parameter to use in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>A URI that specifies the authentication type. The default is an empty string.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.CustomAttributes">
      <summary>Gets a dictionary that contains any extra attributes specified in the &lt;wsFederation&gt; element in the configuration file.</summary>
      <returns>The dictionary that contains the extra attributes.</returns>
    </member>
    <member name="F:System.IdentityModel.Services.Configuration.WsFederationConfiguration.DefaultFreshness">
      <summary>A constant that contains the default value for the <see cref="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.Freshness" /> property; zero.</summary>
    </member>
    <member name="F:System.IdentityModel.Services.Configuration.WsFederationConfiguration.DefaultMaxArrayLength">
      <summary>A constant that sets the default <see cref="P:System.Xml.XmlDictionaryReaderQuotas.MaxArrayLength" /> property of the XML dictionary reader quotas object referenced by the <see cref="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.XmlDictionaryReaderQuotas" /> property.</summary>
    </member>
    <member name="F:System.IdentityModel.Services.Configuration.WsFederationConfiguration.DefaultMaxStringContentLength">
      <summary>A constant that sets the default <see cref="P:System.Xml.XmlDictionaryReaderQuotas.MaxStringContentLength" /> property of the XML dictionary reader quotas object referenced by the <see cref="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.XmlDictionaryReaderQuotas" /> property.</summary>
    </member>
    <member name="F:System.IdentityModel.Services.Configuration.WsFederationConfiguration.DefaultPassiveRedirectEnabled">
      <summary>A constant that contains the default value for the <see cref="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.PassiveRedirectEnabled" /> property; true, passive redirects are enabled.</summary>
    </member>
    <member name="F:System.IdentityModel.Services.Configuration.WsFederationConfiguration.DefaultPersistentCookiesOnPassiveRedirects">
      <summary>A constant that contains the default value for the <see cref="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.PersistentCookiesOnPassiveRedirects" /> property; false, cookies are not enabled.</summary>
    </member>
    <member name="F:System.IdentityModel.Services.Configuration.WsFederationConfiguration.DefaultRequireHttps">
      <summary>A constant that contains the default value for the <see cref="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.RequireHttps" /> property; true, HTTPS is required on redirects.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.Freshness">
      <summary>Gets or sets the value of the wfresh parameter to use in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>The desired maximum age of authentication requests, in minutes. The default is <see cref="F:System.IdentityModel.Services.Configuration.WsFederationConfiguration.DefaultFreshness" />.CautionIn the next release of .NET Framework 4.5, the <see cref="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.Freshness" /> property will be of type <see cref="T:System.String" /> and its default value will be null.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.HomeRealm">
      <summary>Gets or sets the value of the whr parameter to use in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>The address of the home realm identity provider. The default is an empty string.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.Issuer">
      <summary>Gets or sets a URI that identifies the intended issuer of the security token.</summary>
      <returns>The URI that identifies the security token service (STS). Cannot be null.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set the property to null occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.PassiveRedirectEnabled">
      <summary>Gets or sets a value that specifies whether the module is enabled to initiate WS-Federation protocol redirects.</summary>
      <returns>true to enable redirects; otherwise, false. The default is <see cref="F:System.IdentityModel.Services.Configuration.WsFederationConfiguration.DefaultPassiveRedirectEnabled" />, redirects are enabled.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.PersistentCookiesOnPassiveRedirects">
      <summary>Gets or sets a value that specifies whether a persistent session cookie is issued on successful authentication.</summary>
      <returns>true to issue a persistent session cookie; otherwise, false. The default is <see cref="F:System.IdentityModel.Services.Configuration.WsFederationConfiguration.DefaultPersistentCookiesOnPassiveRedirects" />, cookies are not enabled.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.Policy">
      <summary>Gets or sets the value of the wp parameter to be used in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>A URL that identifies the policy to use. The default is an empty string.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.Realm">
      <summary>Gets or sets the value of the wtrealm parameter to be used for WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>A URI that identifies the relying party (RP) to the security token service (STS). Cannot be null.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set the property to null occurs.</exception>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.Reply">
      <summary>Gets or sets the value of the wreply parameter to use in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>A URL that identifies the address at which the relying party (RP) application would like to receive replies from the Security Token Service (STS). The default is an empty string.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.Request">
      <summary>Gets or sets the value of the wreq parameter to use in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>The token issuance request expressed as a &lt;wst:RequestSecurityToken&gt; element. The default is an empty string.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.RequestPtr">
      <summary>Gets or sets the value of the wreqptr parameter to use in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>A URL that specifies the location of the token issuance request expressed as a &lt;wst:RequestSecurityToken&gt; element</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.RequireHttps">
      <summary>Gets or sets a value that specifies whether communication with the security token service (STS) must use HTTPS protocol.</summary>
      <returns>true if communication with the STS must be secured using HTTPS; otherwise, false. The default is <see cref="F:System.IdentityModel.Services.Configuration.WsFederationConfiguration.DefaultRequireHttps" />, HTTPS is required.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.Resource">
      <summary>Get or sets the value of the wres parameter to use in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>A URI that identifies the resource being accessed, the relying party (RP), to the to the security token service (STS). The default is an empty string.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.SignInQueryString">
      <summary>Gets or sets a query string that contains any additional parameters to be sent in WS-Federation sign-in requests (“wsignin1.0”).</summary>
      <returns>A URL fragment that contains the additional message parameters in the following format: “?param1=value1&amp;param2=value2&amp;paramN=valueN”. The default is an empty string.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.SignOutQueryString">
      <summary>Gets or sets a query string that contains any additional parameters to be sent in WS-Federation sign-out requests (“wsignout1.0”) during passive sign-out.</summary>
      <returns>A URL fragment that contains the additional message parameters in the following format: “?param1=value1&amp;param2=value2&amp;paramN=valueN”. The default is an empty string.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.SignOutReply">
      <summary>Gets or sets the value of the wreply parameter to use during WS-Federation sign-out requests (“wsignout1.0”). </summary>
      <returns>The URL to which the client should be redirected by the security token service (STS) during passive sign-out through the WS-Federation protocol. The default is an empty string.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WsFederationConfiguration.XmlDictionaryReaderQuotas">
      <summary>Gets or sets the configurable quota values for XML dictionary readers used by this instance.</summary>
      <returns>The object that contains the configurable quota values. The default object has the <see cref="P:System.Xml.XmlDictionaryReaderQuotas.MaxArrayLength" /> property set to <see cref="F:System.IdentityModel.Services.Configuration.WsFederationConfiguration.DefaultMaxArrayLength" /> and the <see cref="P:System.Xml.XmlDictionaryReaderQuotas.MaxStringContentLength" /> property set to <see cref="F:System.IdentityModel.Services.Configuration.WsFederationConfiguration.DefaultMaxStringContentLength" />.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt to set the property to null occurs.</exception>
    </member>
    <member name="T:System.IdentityModel.Services.Configuration.WSFederationElement">
      <summary>Represents the &lt;wsFederation&gt; element in a configuration file. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.Configuration.WSFederationElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.Configuration.WSFederationElement" /> class.</summary>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.AuthenticationType">
      <summary>Gets or sets the authenticationType attribute.</summary>
      <returns>A string that contains a URI that represents the WS-Federation sign-in request wauth type. The default is an empty string. Optional.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.CustomAttributes">
      <summary>Gets or sets the dictionary that contains any custom attributes.</summary>
      <returns>The dictionary that contains the custom attributes, keyed by attribute name.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.Freshness">
      <summary>Gets or sets the freshness attribute.</summary>
      <returns>The required freshness. Sets the WS-Federation sign-in request wfresh parameter. Optional.CautionIn the next release of .NET Framework 4.5, the <see cref="P:System.IdentityModel.Services.Configuration.WSFederationElement.Freshness" /> property will be of type <see cref="T:System.String" /> and its default value will be null.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.HomeRealm">
      <summary>Gets or sets the homeRealm attribute.</summary>
      <returns>A string that contains the URI of the home realm of the identity provider (IP). The default is an empty string. Sets the WS-Federation sign-in request whr parameter. Optional.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.IsConfigured">
      <summary>Gets a value that indicates whether this element has been configured with non-default values.</summary>
      <returns>true if the element has been configured with non-default values; otherwise, false.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.Issuer">
      <summary>Gets or sets the issuer attribute.</summary>
      <returns>A string that contains the URI of the token issuer. The default is an empty string. Required.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.PassiveRedirectEnabled">
      <summary>Gets or sets the passiveRedirectEnabled attribute.</summary>
      <returns>true to enable the WS-Federation Authentication Module (WSFAM) to automatically redirect unauthorized requests to an STS; otherwise, false. The default is true, unauthorized requests are automatically redirected. Optional.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.PersistentCookiesOnPassiveRedirects">
      <summary>Gets or sets the persistentCookiesOnPassiveRedirects attribute.</summary>
      <returns>true to issue persistent cookies when the WS-Federation Authentication Module (WSFAM) is enabled to initiate WS-Federation passive protocol redirects; otherwise, false. The default is false, cookies are not issued. Optional.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.Policy">
      <summary>Gets or sets the policy attribute.</summary>
      <returns>A string that contains the URI of the relevant policy. The default is an empty string. Sets the WS-Federation sign-in request wp parameter. Optional.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.Realm">
      <summary>Gets or sets the realm attribute.</summary>
      <returns>A string that contains the URI of requesting realm. The default is an empty string. Sets the WS-Federation sign-in request wtrealm parameter. Required.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.Reply">
      <summary>Gets or sets the reply attribute.</summary>
      <returns>A string that contains the URI of the address to reply to. The default is an empty string. Sets the wreply parameter on a WS-Federation sign-in request. Optional.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.Request">
      <summary>Gets or sets the request attribute.</summary>
      <returns>A string that contains the token issuance request. The default is an empty string. Sets the WS-Federation sign-in request wreq parameter. Optional.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.RequestPtr">
      <summary>Gets or sets the requestPtr attribute.</summary>
      <returns>A URL that specifies the location of the token issuance request. The default is an empty string. Sets the WS-Federation sign-in request wreqptr parameter. Optional.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.RequireHttps">
      <summary>Gets or sets the requireHttps attribute.</summary>
      <returns>true to specify that the WS-Federation Authentication Module (WSFAM) to only redirect to a secure URL for the STS; otherwise, false. The default is true, the WSFAM only redirects to a secure URL for the STS. Optional.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.Resource">
      <summary>Gets or sets the resource attribute.</summary>
      <returns>A string that contains the URI of the WS-Federation resource value. The default is an empty string. Sets the request WS-Federation sign-in request wres parameter. Optional.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.SignInQueryString">
      <summary>Gets or sets the signInQueryString attribute.</summary>
      <returns>Any application defined parameters for the WS-Federation sign-in request URL. The default is an empty string. Provides an extensibility point to include application defined query parameters in the sign-in request URL. Optional.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.SignOutQueryString">
      <summary>Gets or sets the signOutQueryString attribute.</summary>
      <returns>Any application defined parameters for the WS-Federation sign-out request URL. The default is an empty string. Provides an extensibility point to include application defined query parameters in the sign-out request URL. Optional.</returns>
    </member>
    <member name="P:System.IdentityModel.Services.Configuration.WSFederationElement.SignOutReply">
      <summary>Gets or sets the signOutReply attribute.</summary>
      <returns>A string that contains the URL to return to following sign out. Sets the wreply parameter on a WS-Federation sign-out request. Optional.</returns>
    </member>
    <member name="T:System.IdentityModel.Services.Tokens.MachineKeySessionSecurityTokenHandler">
      <summary>Handles machine key session tokens.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.Tokens.MachineKeySessionSecurityTokenHandler.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.Tokens.MachineKeySessionSecurityTokenHandler" /> class.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.Tokens.MachineKeySessionSecurityTokenHandler.#ctor(System.TimeSpan)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.Tokens.MachineKeySessionSecurityTokenHandler" /> class that has the specified default token lifetime.</summary>
      <param name="tokenLifetime">The default lifetime for a token.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="tokenLifetime" /> is shorter than or equal to <see cref="F:System.TimeSpan.Zero" /></exception>
    </member>
    <member name="T:System.IdentityModel.Services.Tokens.MembershipUserNameSecurityTokenHandler">
      <summary>A <see cref="T:System.IdentityModel.Tokens.UserNameSecurityTokenHandler" /> that validates a <see cref="T:System.IdentityModel.Tokens.UserNameSecurityToken" /> by using a configured <see cref="T:System.Web.Security.MembershipProvider" />.</summary>
    </member>
    <member name="M:System.IdentityModel.Services.Tokens.MembershipUserNameSecurityTokenHandler.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.Tokens.MembershipUserNameSecurityTokenHandler" /> class that uses the default membership provider that is configured for the application.</summary>
      <exception cref="T:System.InvalidOperationException">No default membership provider is configured for the application.</exception>
    </member>
    <member name="M:System.IdentityModel.Services.Tokens.MembershipUserNameSecurityTokenHandler.#ctor(System.Web.Security.MembershipProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.IdentityModel.Services.Tokens.MembershipUserNameSecurityTokenHandler" /> class that uses the specified membership provider.</summary>
      <param name="provider">The membership provider to use to validate tokens.</param>
    </member>
    <member name="P:System.IdentityModel.Services.Tokens.MembershipUserNameSecurityTokenHandler.CanValidateToken">
      <summary>Gets a value that indicates whether the current instance can validate security tokens of type <see cref="T:System.IdentityModel.Tokens.UserNameSecurityToken" />. Always true.</summary>
      <returns>true if the token handler can validate user name security tokens; otherwise, false. Always true.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.Tokens.MembershipUserNameSecurityTokenHandler.LoadCustomConfiguration(System.Xml.XmlNodeList)">
      <summary>Loads custom configuration from XML.</summary>
      <param name="customConfigElements">The custom XML elements.</param>
    </member>
    <member name="P:System.IdentityModel.Services.Tokens.MembershipUserNameSecurityTokenHandler.MembershipProvider">
      <summary>Gets the <see cref="T:System.Web.Security.MembershipProvider" /> that is configured for the current instance.</summary>
      <returns>The configured membership provider.</returns>
    </member>
    <member name="M:System.IdentityModel.Services.Tokens.MembershipUserNameSecurityTokenHandler.ValidateToken(System.IdentityModel.Tokens.SecurityToken)">
      <summary>Validates the specified security token by using the configured <see cref="T:System.Web.Security.MembershipProvider" />.</summary>
      <returns>The identities that are contained in the token.</returns>
      <param name="token">The security token to be validated.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="token" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="token" /> is not an instance of <see cref="T:System.IdentityModel.Tokens.UserNameSecurityToken" />.</exception>
      <exception cref="T:System.IdentityModel.Tokens.SecurityTokenValidationException">
        <paramref name="token" /> failed validation with the configured <see cref="T:System.Web.Security.MembershipProvider" />.</exception>
    </member>
  </members>
</doc>