<?xml version="1.0"?>
<doc>
    <assembly>
        <name>wv2winrt</name>
    </assembly>
    <members>
        <member name="M:wv2winrt.Program.Main(System.String[],System.String,System.String,System.Boolean,System.String[],System.String[],System.Boolean,System.Boolean,System.Boolean,System.String,System.Boolean)">
            <summary>
            Given WinMD files, produce C++ code that implements COM IDispatch wrappers around the types in those WinMD files.
            </summary>
            <param name="winmdPaths">Path(s) to winmd files containing types around which to generate source code.</param>
            <param name="outputPath">Path to a folder into which to place generated source code. Or `--` to write all generated code to standard out.</param>
            <param name="outputNamespace">Namespace where generated source code will be placed.</param>
            <param name="useFullNamespace">If set, wv2winrt will use full namespace names for dispatch adapter source files. Use this option when the output namespace is different to the consuming project's root namespace.</param>
            <param name="useJavascriptCase">If set, wv2winrt will use javascript casing for projection properties, methods, and events.</param>
            <param name="explicitIncludesOnly">If set, wv2winrt won't automatically follow references from other included types.</param>
            <param name="pch">Optional precompiled header filename of the project consuming the generated source code.</param>
            <param name="idl">Optional flag to generate only the idl file. Run with this option before MIDL if the consuming project defines its own WinRT types.</param>
            <param name="include">List of namespaces or runtimeclass names to be included in the generated output.</param>
            <param name="exclude">List of namespaces or runtimeclass names to be excluded from the generated output.</param>
            <param name="verbose">If set, generate verbose output including when a namespace or runtimeclass is excluded.</param>
        </member>
    </members>
</doc>
