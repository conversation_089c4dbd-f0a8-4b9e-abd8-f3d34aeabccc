﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Net;
using System.Reflection;
using System.Text;
using System.Web;

namespace OCRTools.Common
{
    public class WebClientExt
    {
        private const string NetWorkError = "网络异常，稍后重试！";
        private static readonly List<CnnWebClient> LstCache = new List<CnnWebClient>();

        public static CnnWebClient GetOneClient()
        {
            try
            {
                lock (LstCache)
                {
                    try
                    {
                        LstCache.FindAll(p => !p.IsBusy && !p.IsUsed && p.IsExipred).ForEach(p =>
                        {
                            p.Dispose();
                            p = null;
                        });
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                    var myClient = LstCache.Find(p => !p.IsBusy && !p.IsUsed);
                    if (myClient == null)
                    {
                        myClient = new CnnWebClient();
                        LstCache.Add(myClient);
                        myClient.Disposed += (s, args) =>
                        {
                            lock (LstCache)
                            {
                                LstCache.Remove(myClient);
                            }
                        };
                    }

                    myClient.IsUsed = true;
                    myClient.DtLast = ServerTime.DateTime.Ticks;

                    return myClient;
                }
            }
            catch (Exception)
            {
            }

            return GetOneClient();
        }

        private static string GetNewCookie(string strCookie, string strNewCookie)
        {
            var strTmpCookie = strCookie ?? "";
            if (!string.IsNullOrEmpty(strNewCookie))
            {
                var lstTmp = new List<string>();
                lstTmp.AddRange(strNewCookie.Split(new[] { ",", ";" }, StringSplitOptions.RemoveEmptyEntries));
                foreach (var item in lstTmp)
                    if (!item.Trim().ToLower().StartsWith("path=")
                        && !item.Trim().ToLower().StartsWith("expires=")
                        && !item.Trim().ToLower().StartsWith("httponly=")
                        && !item.Trim().ToLower().StartsWith("domain=.")
                        && item.IndexOf("=") > 0)
                    {
                        var strItem = SubStringHorspool(item.Trim(), "", "=") + "=";
                        if (!strTmpCookie.Contains(strItem))
                            strTmpCookie += string.Format(";{0};", item.Trim());
                        else
                            strTmpCookie = strTmpCookie.Replace(
                                strItem + SubStringHorspool(strTmpCookie, strItem, ";"), item);
                    }
            }

            return strTmpCookie.Replace(" ", "").Replace(";;", ";").TrimStart(';');
        }

        public static string SubStringHorspool(string str, string strStart, string strEnd = "")
        {
            int index;
            if (!string.IsNullOrEmpty(strStart))
            {
                index = str.IndexOf(strStart);
                str = index >= 0 ? str.Substring(index + strStart.Length) : string.Empty;
            }

            if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strEnd))
            {
                index = str.IndexOf(strEnd);
                str = index >= 0 ? str.Substring(0, index) : string.Empty;
            }

            return str;
        }


        #region Web

        public static string GetHtml(string url, double timeOut)
        {
            return GetHtml(url, "", "", "", 1, timeOut > 0 ? (int)timeOut : 2);
        }

        public static string GetHtml(string url, string cookie, string ipAddress, string post = "", int retryCount = 1,
            int timeOut = 2)
        {
            return GetHtml(url, ref cookie, ipAddress, post, retryCount, timeOut);
        }

        public static string GetHtml(string url, string strPost = "", int timeOut = 2,
            NameValueCollection collect = null)
        {
            return GetHtml(url, "", "", strPost, "", timeOut, collect);
        }

        public static string GetHtml(string url, ref string cookie, string ipAddress, string post = "",
            int retryCount = 1, int timeOut = 2)
        {
            var strTmp = string.Empty;
            retryCount = retryCount <= 0 ? 1 : retryCount;
            for (var i = 0; i < retryCount; i++)
            {
                strTmp = GetHtml(url, ref cookie, ipAddress, post, "", timeOut);
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }

            if (strTmp != null && strTmp.Equals(" "))
                strTmp = "";
            return strTmp;
        }

        public static string GetHtml(string url, string cookieStr, string ipAddress = "", string strPost = "",
            string referer = "", int timeOut = 2, NameValueCollection collect = null)
        {
            return GetHtml(url, ref cookieStr, ipAddress, strPost, referer, timeOut, collect);
        }

        public static string GetHtml(string url, ref string cookieStr, string ipAddress = "",
            string strPost = "", string referer = "", int timeOut = 2, NameValueCollection collect = null)
        {
            if (!CommonString.IsOnLine && !url.StartsWith("http://127.0.0.1"))
            {
                return NetWorkError;
            }
            var result = "";
            var myClient = GetOneClient();
            try
            {
                myClient.Headers.Clear();
                //if (isMobile)
                //    myClient.Credentials = CredentialCache.DefaultCredentials;
                myClient.Timeout = timeOut;
                myClient.StrIpAddress = ipAddress;

                if (url.Contains("xfyun.cn"))
                {
                    myClient.Headers.Add("Content-Type: application/json");
                }
                else if (url.Contains("api.textin.com") || url.Contains("tinypng.com") || url.Contains("api.ccint.com"))
                {
                    myClient.Headers.Add("Content-Type: image/png");
                }
                else
                {
                    myClient.Headers.Add("Content-Type: application/x-www-form-urlencoded; charset=UTF-8");
                }

                try
                {
                    myClient.Headers.Add("Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
                    myClient.Headers.Add("Accept-Language: zh-CN,zh;q=0.8");
                    myClient.Headers.Add("Cache-Control: no-cache");
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }

                if (!string.IsNullOrEmpty(referer))
                    myClient.Headers.Add("Referer: " + referer);
                if (!string.IsNullOrEmpty(cookieStr)) myClient.Headers.Add("Cookie: " + cookieStr);
                myClient.Encoding = Encoding.UTF8;
                if (collect != null && collect.Count > 0)
                    foreach (string key in collect)
                        try
                        {
                            myClient.Headers.Add(string.Format("{0}: {1}", key.Trim(),
                                collect[key] == null ? "" : collect[key].Trim()));
                        }
                        catch (Exception oe)
                        {
                            if (oe.Message.Contains("无效") || oe.Message.Contains("invalid"))
                            {
                                try
                                {
                                    myClient.Headers.Add(string.Format("{0}: {1}", key.Trim(),
                                        collect[key] == null ? "" : HttpUtility.UrlEncode(collect[key].Trim())));
                                }
                                catch (Exception e)
                                {
                                    Console.WriteLine(e);
                                }
                            }
                            else
                                Console.WriteLine(oe.Message);
                        }

                var uri = new Uri(url, false);
                if ("image/png".Equals(myClient.Headers.Get("Content-Type")))
                {
                    result = Encoding.UTF8.GetString(myClient.UploadData(uri, "POST", Convert.FromBase64String(strPost)));
                }
                else
                {
                    if (string.IsNullOrEmpty(strPost))
                        result = myClient.DownloadString(uri);
                    else
                        result = myClient.UploadString(uri, strPost);
                }
                if (myClient.ResponseHeaders != null && !url.Contains("tinypng.com"))
                {
                    var loc = myClient.ResponseHeaders["Location"];
                    if (!string.IsNullOrEmpty(loc) && !url.Equals(loc))
                        return GetHtml(loc, ref cookieStr, ipAddress, strPost, referer, timeOut);
                    if (!string.IsNullOrEmpty(myClient.ResponseHeaders["Date"]))
                        ServerTime.SetHttpDate(DateTime.Parse(myClient.ResponseHeaders["Date"]));
                }

                if (string.IsNullOrEmpty(result)) result = " ";
            }
            catch (OutOfMemoryException)
            {
                GC.Collect();
            }
            catch (ObjectDisposedException)
            {
                return GetHtml(url, ref cookieStr, ipAddress, strPost, referer, timeOut, collect);
            }
            catch (Exception oe)
            {
                //log4net.LogManager.GetLogger("Order").Error(oe);
                if (oe is WebException exception)
                {
                    var response = exception.Response as HttpWebResponse;
                    try
                    {
                        if (response != null)
                        {
                            switch (response.StatusCode)
                            {
                                case HttpStatusCode.NotFound: //404
                                    DnsHelper.ReportError(myClient.StrHost, myClient.StrIpAddress);
                                    result = " ";
                                    break;
                            }
                        }
                        else if (!string.IsNullOrEmpty(exception.Message))
                        {
                            if (exception.Message.Contains("(404)"))
                            {
                                DnsHelper.ReportError(myClient.StrHost, myClient.StrIpAddress);
                                result = " ";
                            }
                        }
                    }
                    catch { }
                    finally
                    {
                        try
                        {
                            response?.Close();
                        }
                        catch { }
                    }
                }
                else if (!string.IsNullOrEmpty(oe.Message))
                {
                    if (oe.Message.Contains("(404)"))
                    {
                        DnsHelper.ReportError(myClient.StrHost, myClient.StrIpAddress);
                        result = " ";
                    }
                }
            }
            finally
            {
                if (myClient.ResponseHeaders != null && !string.IsNullOrEmpty(myClient.ResponseHeaders["Set-Cookie"]))
                    cookieStr = GetNewCookie(cookieStr, myClient.ResponseHeaders["Set-Cookie"].Trim());
                //CookieStr = myClient.ResponseHeaders["Set-Cookie"].Replace("Path=/otn", " ").Replace("Path=/", "").Replace("path=/", "").Replace(",", "").Trim();
                try
                {
                    if (myClient.IsBusy)
                        myClient.CancelAsync();
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

                myClient.IsUsed = false;
            }

            return result;
        }

        #endregion
    }

    public class CnnWebClient : WebClient
    {
        private int _timeOut = 3;

        public bool IsUsed { get; set; }

        public string StrIpAddress { get; set; } = "";

        public string StrHost { get; set; } = "";

        /// <summary>
        ///     过期时间
        /// </summary>
        public int Timeout
        {
            get
            {
                if (_timeOut <= 0)
                    _timeOut = 3;
                return _timeOut;
            }
            set
            {
                if (value <= 0)
                    _timeOut = 3;
                _timeOut = value;
            }
        }

        public long DtLast { get; set; }

        public bool IsExipred { get { return new TimeSpan(ServerTime.DateTime.Ticks - DtLast).TotalSeconds > 30; } }

        ~CnnWebClient()
        {
            Dispose(false);
        }

        public new void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        public override string ToString()
        {
            return string.Format("{0}-{1}", StrHost, StrIpAddress);
        }

        //public bool RemoteCertificateValidationCallback(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
        //{
        //    return true;
        //}

        /// <summary>
        ///     重写GetWebRequest,添加WebRequest对象超时时间
        /// </summary>
        /// <param name="address"></param>
        /// <returns></returns>
        protected override WebRequest GetWebRequest(Uri address)
        {
            HttpWebRequest nowRequest;
            //if (DateTime.Now.Second % 2 == 0)
            //{
            //    //System.Threading.Thread.Sleep(1);
            //    System.GC.Collect();
            //}

            StrHost = address.Host;

            var selfHost = CommonString.IsSelfHost(StrHost);
            if (selfHost != 0)
            {
                address = CommonString.SetAddress(address, selfHost);
                StrIpAddress = address.Host;
            }
            try
            {
                nowRequest = (HttpWebRequest)base.GetWebRequest(address);
            }
            catch (Exception oe)
            {
                nowRequest = (HttpWebRequest)WebRequest.Create(address);
                Console.WriteLine(oe.Message);
            }

            if (selfHost != 0)
            {
                CommonString.SetUserAgent(nowRequest);
                nowRequest.Host = StrHost;
            }
            else
            {
                nowRequest.UserAgent = CommonString.StrDefaultAgent;
            }

            nowRequest.ProtocolVersion = HttpVersion.Version11;
            //是否使用 Nagle 不使用 提高效率 
            nowRequest.ServicePoint.UseNagleAlgorithm = false;
            //最大连接数 
            nowRequest.ServicePoint.ConnectionLimit = 1000;
            //数据是否缓冲 false 提高效率
            nowRequest.AllowWriteStreamBuffering = false;
            nowRequest.ServicePoint.Expect100Continue = false;
            nowRequest.Headers.Add("Accept-Encoding: gzip, deflate");
            nowRequest.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
            nowRequest.AllowAutoRedirect = false;
            nowRequest.KeepAlive = true;
            ////request.KeepAlive = true;
            //if (address.AbsoluteUri.Contains("/query"))
            //{
            //    NowRequest.Headers.Add("If-无-Match", ServerTime.DateTime.Ticks.ToString());
            //    NowRequest.CachePolicy = new HttpRequestCachePolicy(HttpRequestCacheLevel.NoCacheNoStore);
            //    //SetHeaderValue(request.Headers, "If-Modified-Since", "0");
            //    strIPAddress = string.Format("{0}.{1}.{2}.{3}", new Random().Next(10, 120), new Random().Next(120, 250), new Random().Next(130, 250), new Random().Next(1, 250));
            //    SetHeaderValue(NowRequest.Headers, "X-Forwarded-For", strIPAddress);
            //    SetHeaderValue(NowRequest.Headers, "Proxy-马赛克-IP", strIPAddress);
            //    SetHeaderValue(NowRequest.Headers, "WL-Proxy-马赛克-IP", strIPAddress);
            //}
            ////CommonMethod.SetHeaderValue(request.Headers, "If-Modified-Since", "Wed, 31 Dec 1969 16:00:00 GMT");
            //StrHost = address.Host;
            //if (address.Host.Equals("12306.ie.sogou.com"))
            //{
            //    NowRequest.Headers.Add("Origin: se-extension://ext-1966358554");
            //    NowRequest.UserAgent = "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.122 Safari/537.36 SE 2.X MetaSr 1.0";
            //}
            //else
            try
            {
                nowRequest.Timeout = 1000 * Timeout;
                //NowRequest.ReadWriteTimeout = 1000;// *Timeout;
            }
            catch
            {
            }

            nowRequest.Proxy = null;
            ////if (request.Proxy != null)
            ////    request.Proxy = GlobalProxySelection.GetEmptyWebProxy();
            ////if (NowRequest.Proxy != null)
            //if (!address.Host.Equals("12306.ie.sogou.com"))
            //{
            //    NowRequest.Proxy = null;
            //}
            //else
            //{
            //    // 在发起HTTP请求前将proxy赋值给HttpWebRequest的Proxy 属性
            //    NowRequest.Proxy = WebClientSyncExt.DefaultProxy;
            //}
            return nowRequest;
        }

        private void SetHeaderValue(WebHeaderCollection header, string name, string value)
        {
            var property = typeof(WebHeaderCollection).GetProperty("InnerCollection",
                BindingFlags.Instance | BindingFlags.NonPublic);
            if (property != null)
                if (property.GetValue(header, null) is NameValueCollection collection)
                    collection[name] = value;
        }
    }
}