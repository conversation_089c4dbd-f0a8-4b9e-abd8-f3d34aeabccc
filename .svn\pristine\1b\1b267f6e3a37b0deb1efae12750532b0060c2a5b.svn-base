using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolCatchPointer : Tool
    {
        private Point lastPoint = new Point(0, 0);

        private DrawObject resizedObject;

        private int resizedObjectHandle;

        private SelectionMode selectMode = SelectionMode.None;

        private Point startPoint = new Point(0, 0);

        public void MouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            var @catch = drawArea.GetCatch();
            if (@catch != null)
            {
                drawArea.GraphicsList.UnselectAll();
                @catch.Selected = true;
                selectMode = SelectionMode.None;
                drawArea.HideTool();
                var point = new Point(e.X, e.Y);
                var num = @catch.HitTest(point);
                if (num > 0)
                {
                    selectMode = SelectionMode.Size;
                    resizedObject = @catch;
                    resizedObjectHandle = num;
                }

                if (selectMode == SelectionMode.None)
                {
                    selectMode = SelectionMode.Move;
                    drawArea.Cursor = CursorEx.Move;
                }

                lastPoint.X = e.X;
                lastPoint.Y = e.Y;
                startPoint.X = e.X;
                startPoint.Y = e.Y;
                drawArea.Capture = true;
                drawArea.Refresh();
            }
        }

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            MouseDown(drawArea, e);
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            var point = new Point(e.X, e.Y);
            if (e.Button == MouseButtons.None)
            {
                Cursor cursor = null;
                var num = resizedObject.HitTest(point);
                if (num > 0) cursor = resizedObject.GetHandleCursor(num);
                if (cursor == null) cursor = CursorEx.Cross;
                drawArea.Cursor = cursor;
            }
            else if (e.Button == MouseButtons.Left)
            {
                var num2 = e.X - lastPoint.X;
                var num3 = e.Y - lastPoint.Y;
                lastPoint.X = e.X;
                lastPoint.Y = e.Y;
                if (selectMode == SelectionMode.Size && resizedObject != null)
                {
                    var drawObject = resizedObject;
                    using (new AutomaticCanvasRefresher(drawArea, drawObject.GetBoundingBox))
                    {
                        resizedObject.MoveHandleTo(e.Location, resizedObjectHandle);
                    }
                }

                if (selectMode == SelectionMode.Move)
                {
                    var @object = drawArea.GraphicsList.SelectedObjects.ToList();
                    var rectangle = drawArea.Bounds.SizeOffset(-2);
                    using (new AutomaticCanvasRefresher(drawArea, @object.GetGroupBoundingBox))
                    {
                        var rectangle2 = resizedObject.Rectangle.SizeOffset(-1);
                        var num4 = rectangle.X - rectangle2.X;
                        var num5 = rectangle.Y - rectangle2.Y;
                        var num6 = rectangle.X + rectangle.Width - rectangle2.X - rectangle2.Width;
                        var num7 = rectangle.Y + rectangle.Height - rectangle2.Y - rectangle2.Height;
                        if (num4 > num2) num2 = num4;
                        if (num6 < num2) num2 = num6;
                        if (num5 > num3) num3 = num5;
                        if (num7 < num3) num3 = num7;
                    }

                    resizedObject.Move(num2, num3);
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            drawArea.ShowTool(resizedObject);
            drawArea.Capture = false;
            drawArea.GraphicsList.UnselectAll();
            drawArea.Refresh();
        }

        private enum SelectionMode
        {
            None,
            Move,
            Size
        }
    }
}