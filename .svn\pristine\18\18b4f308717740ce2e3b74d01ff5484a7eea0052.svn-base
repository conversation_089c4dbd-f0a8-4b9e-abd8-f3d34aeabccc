﻿using MetroFramework.Forms;
using OCRTools.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmBatchOCR : MetroForm
    {
        private const string STR_PROCESS_FINISHED = "处理成功";
        private const string STR_PROCESS_FAILED = "处理失败";
        private const string STR_PROCESSING = "处理中…";
        private const string STR_PROCESS_NOSTART = "待处理";

        internal static BlockingCollection<OcrContent> OcrResultPool = new BlockingCollection<OcrContent>();

        private readonly List<BatchOcrItem> _ocrItems = new List<BatchOcrItem>();

        private BindingList<BatchOcrItem> _bindingItems;

        public FrmBatchOCR()
        {
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false;
            ShadowType = CommonString.CommonShadowType;
            CommonMethod.EnableDoubleBuffering(this);
            dgContent.AutoGenerateColumns = false;
        }

        private void FrmBatch_Load(object sender, EventArgs e)
        {
            InitLeftCount();
            txtSaveToPath.Text =
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), CommonString.FullName);
            InitOcrType();
            InitSpiltModel();
            chkShowOldContent.Checked = CommonSetting.显示原文;
            OcrBatchResultProcessThread();
        }

        private void InitOcrType()
        {
            try
            {
                var lstItem = new List<string>();
                foreach (OcrType type in Enum.GetValues(typeof(OcrType)))
                {
                    if (CheckIsForbidOperate(type)) continue;
                    lstItem.Add(type.ToString());
                    cmbOcrTypes.Items.Add(type.ToString());
                }
                识别类型.DataSource = lstItem;
            }
            catch (Exception oe)
            {
                Console.WriteLine("加载OCR类型失败！" + oe.Message);
            }

            if (cmbOcrTypes.SelectedIndex < 0) cmbOcrTypes.SelectedIndex = 0;
        }

        private void InitSpiltModel()
        {
            try
            {
                foreach (SpiltMode type in Enum.GetValues(typeof(SpiltMode)))
                {
                    cmbSpiltModel.Items.Add(type.ToString());
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine("加载OCR类型失败！" + oe.Message);
            }

            cmbSpiltModel.Text = CommonSetting.分段模式;
            if (cmbSpiltModel.SelectedIndex < 0) cmbSpiltModel.SelectedIndex = 0;
        }

        private bool CheckIsForbidOperate(OcrType ocrType)
        {
            var isForbid = Equals(ocrType, OcrType.竖排) && Program.NowUser?.IsSupportVertical != true
                           || Equals(ocrType, OcrType.表格) && Program.NowUser?.IsSupportTable != true
                           || Equals(ocrType, OcrType.公式) && Program.NowUser?.IsSupportMath != true
                           || Equals(ocrType, OcrType.翻译) && Program.NowUser?.IsSupportTranslate != true;

            return isForbid;
        }

        private void btnClearFiles_Click(object sender, EventArgs e)
        {
            _ocrItems.Clear();
            ReBindGridView();
        }

        private void btnRemoveSelected_Click(object sender, EventArgs e)
        {
            if (dgContent.SelectedRows.Count <= 0)
            {
                return;
            }
            foreach (DataGridViewRow row in dgContent.SelectedRows)
            {
                if (row.DataBoundItem is BatchOcrItem item)
                {
                    _ocrItems.Remove(item);
                }
            }
            ReBindGridView();
        }

        private void btnClearSuccess_Click(object sender, EventArgs e)
        {
            _ocrItems.RemoveAll(p => Equals(p.State, STR_PROCESS_FINISHED));
            ReBindGridView();
        }

        private void btnAddFiles_Click(object sender, EventArgs e)
        {
            var openFile = new OpenFileDialog
            {
                Title = "请选择文件",
                Filter = "图片或文档|*.png;*.jpg;*.jpeg;*.bmp;*.pdf;*.doc;*.docx;*.txt;",
                RestoreDirectory = true,
                Multiselect = true
            };
            if (openFile.ShowDialog(this) == DialogResult.OK && openFile.FileNames.Length > 0) AddFileToList(openFile.FileNames);

        }

        private void AddFileToList(string[] files)
        {
            foreach (var item in files)
            {
                if (!CommonString.LstCanProcessFilesExt.Any(p => item.EndsWith(p)))
                {
                    continue;
                }

                if (_ocrItems.Exists(p => Equals(p.FullName, item))) continue;
                var ocrItem = new BatchOcrItem
                {
                    Id = Guid.NewGuid().ToString(),
                    FullName = item,
                    FileName = Path.GetFileName(item),
                    FileType = OcrFileType.自动,
                    OcrType = (OcrType)Enum.Parse(typeof(OcrType), cmbOcrTypes.Text, true),
                    State = STR_PROCESS_NOSTART
                };
                _ocrItems.Add(ocrItem);
            }
            ReBindGridView();
        }

        private void btnAddFolder_Click(object sender, EventArgs e)
        {
            var openFile = new FolderBrowserDialog()
            {
                Description = "请选择要处理的文件所在目录",
            };
            if (openFile.ShowDialog(this) == DialogResult.OK && !string.IsNullOrEmpty(openFile.SelectedPath))
                AddFileToList(Directory.GetFiles(openFile.SelectedPath));
        }

        private void ReBindGridView()
        {
            _bindingItems = new BindingList<BatchOcrItem>(_ocrItems);
            _bindingItems.ListChanged += (sender, e) =>
            {
                if (e.ListChangedType == ListChangedType.ItemDeleted
                    || e.ListChangedType == ListChangedType.ItemAdded)
                    _bindingItems.ResetBindings();
            };
            dgContent.FastLoadDataGrid(_bindingItems);
            dgContent.Refresh();
            Text = string.Format("批量识别{0}", _ocrItems.Count <= 0 ? "" : string.Format("-(共{0}个)", _ocrItems.Count));
            Invalidate();
        }

        private bool isFormExit;

        private int tryCount;

        private void btnProcess_Click(object sender, EventArgs e)
        {
            if (tryCount > 0)
            {
                btnProcess.Text = string.Format("正在重试({0})", tryCount);
            }
            else
            {
                btnProcess.Text = "正在处理…";
            }
            btnProcess.Enabled = false;
            Task.Factory.StartNew(() =>
            {
                try
                {
                    var execPerTime = (Program.NowUser?.PerTimeSpan ?? 30000) / (Program.NowUser?.PerTimeSpanExecCount ?? 1) + 1000;
                    foreach (var batchOcrItem in _ocrItems.Where(p => Equals(p.State, STR_PROCESS_FAILED)))
                    {
                        batchOcrItem.State = STR_PROCESS_NOSTART;
                        batchOcrItem.DtStart = null;
                        batchOcrItem.DtEnd = null;
                    }
                    var random = new Random();
                    while (!isFormExit)
                    {
                        BatchOcrItem item;
                        try
                        {
                            item = _ocrItems.FirstOrDefault(p => Equals(p.State, STR_PROCESS_NOSTART));
                        }
                        catch
                        {
                            continue;
                        }
                        if (item == null)
                        {
                            break;
                        }

                        item.DtStart = ServerTime.DateTime;
                        item.State = STR_PROCESSING;


                        try
                        {
                            if (dgContent.ColumnCount > 3)
                                dgContent.InvalidateColumn(3);
                            var index = _ocrItems.IndexOf(item);
                            if (index > -1)
                                dgContent.CurrentCell = dgContent.Rows[index].Cells[0];
                        }
                        catch { }

                        try
                        {
                            FrmMain.DragDropEventDelegate.Invoke(new List<string> { item.FullName }, null, item.OcrType, ProcessBy.批量识别, item.Id);
                        }
                        catch { }

                        try
                        {
                            if (_ocrItems.Any(p => Equals(p.State, STR_PROCESS_NOSTART)))
                                Thread.Sleep(random.Next((int)(execPerTime * 1.1), (int)(execPerTime * 1.5)));
                        }
                        catch { }

                        SetFaildItem();

                        try
                        {
                            //并行任务数量
                            while (!CommonString.IsExit && !isFormExit && _ocrItems.Count(p => Equals(p.State, STR_PROCESSING)) > nMaxThread.Value - 1)
                            {
                                Thread.Sleep(500);
                                SetFaildItem();
                            }
                        }
                        catch { }
                    }
                }
                catch (Exception exception)
                {
                    Console.WriteLine("btnProcess_Click Error:" + exception.Message);
                }

                try
                {
                    //大循环结束，等待剩余项处理完成
                    while (!CommonString.IsExit && !isFormExit && _ocrItems.Exists(p => Equals(p.State, STR_PROCESSING)))
                    {
                        Thread.Sleep(500);
                        SetFaildItem();
                    }
                }
                catch { }

                RefreshState();

                try
                {
                    //如果存在处理失败的，重新开始处理
                    if (!CommonString.IsExit && !isFormExit && tryCount < nFailedCount.Value + 1 && _ocrItems.Exists(p => Equals(p.State, STR_PROCESS_FAILED)))
                    {
                        tryCount++;
                        btnProcess_Click(sender, e);
                        return;
                    }
                }
                catch { }

                btnProcess.Text = "开始识别(&S)";
                btnProcess.Enabled = true;
                tryCount = 0;
            });
        }

        private void RefreshState()
        {
            if (_ocrItems.Count > 0)
            {
                Text = string.Format("批量识别 共{0}个({1})"
                    , _ocrItems.Count
                    , (_ocrItems.Exists(p => Equals(p.State, STR_PROCESS_FINISHED))
                          ? string.Format("成功:{0}", _ocrItems.Count(p => Equals(p.State, STR_PROCESS_FINISHED)))
                          : "")
                      + (_ocrItems.Exists(p => Equals(p.State, STR_PROCESS_FAILED))
                          ? string.Format(" 失败:{0}", _ocrItems.Count(p => Equals(p.State, STR_PROCESS_FAILED)))
                          : "")
                      + (_ocrItems.Exists(p => Equals(p.State, STR_PROCESSING))
                          ? string.Format(" 处理中:{0}", _ocrItems.Count(p => Equals(p.State, STR_PROCESSING)))
                          : "")
                      + (_ocrItems.Exists(p => Equals(p.State, STR_PROCESS_NOSTART))
                          ? string.Format(" 未开始:{0}", _ocrItems.Count(p => Equals(p.State, STR_PROCESS_NOSTART)))
                          : "")
                );
                Invalidate();
            }
        }

        private void SetFaildItem()
        {
            try
            {
                foreach (var failedItem in _ocrItems.Where(p => Equals(p.State, STR_PROCESSING) && p.DtStart.HasValue && p.DtStart < ServerTime.DateTime.AddSeconds(-(int)nTimeOutSecond.Value)))
                {
                    failedItem.State = STR_PROCESS_FAILED;
                    failedItem.DtEnd = ServerTime.DateTime;
                }
            }
            catch { }
        }

        private void OcrBatchResultProcessThread()
        {
            new Thread(t =>
                {
                    try
                    {
                        foreach (var content in OcrResultPool.GetConsumingEnumerable())
                            try
                            {
                                if (!string.IsNullOrEmpty(content.Identity))
                                {
                                    var strState = content.result?.HasResult == true && !Equals(content.processName, CommonString.StrReminder)
                                        ? STR_PROCESS_FINISHED
                                        : STR_PROCESS_FAILED;
                                    _ocrItems.FindAll(p => Equals(p.Id, content.Identity)).ForEach(ocrItem =>
                                    {
                                        try
                                        {
                                            if (!Equals(content.processName, CommonString.StrReminder))
                                            {
                                                var isSave = !chkOnlyOne.Checked || (chkOnlyOne.Checked && !ocrItem.HasResult);
                                                ocrItem.HasResult = true;
                                                var strPath = chkSameFolder.Checked
                                                    ? Path.GetDirectoryName(ocrItem.FullName)
                                                    : txtSaveToPath.Text;
                                                if (isSave && !string.IsNullOrEmpty(strPath))
                                                {
                                                    CommonResult.SaveFile(content, strPath, Path.GetFileName(ocrItem.FullName)
                                                        , (SpiltMode)Enum.Parse(typeof(SpiltMode), cmbSpiltModel.Text)
                                                        , chkShowOldContent.Checked);
                                                }
                                            }
                                        }
                                        catch (Exception e)
                                        {
                                            Console.WriteLine(e.Message);
                                        }
                                        if (!Equals(ocrItem.State, STR_PROCESS_FINISHED))
                                            ocrItem.State = strState;
                                        ocrItem.DtEnd = ServerTime.DateTime;
                                    });

                                    try
                                    {
                                        if (dgContent.ColumnCount > 3)
                                            dgContent.InvalidateColumn(3);
                                    }
                                    catch { }
                                }
                                else
                                {
                                    Console.WriteLine("");
                                }
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
        }

        private void btnResultFolder_Click(object sender, EventArgs e)
        {
            Process.Start(txtSaveToPath.Text);
        }

        private void btnSelectedPath_Click(object sender, EventArgs e)
        {
            var openFile = new FolderBrowserDialog()
            {
                Description = "请选择要将识别结果文件放在哪个目录",
            };
            if (openFile.ShowDialog(this) == DialogResult.OK && !string.IsNullOrEmpty(openFile.SelectedPath))
            {
                txtSaveToPath.Text = openFile.SelectedPath;
            }
        }

        private void FrmBatch_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_ocrItems != null && _ocrItems.Exists(p => Equals(p.State, STR_PROCESSING)))
            {
                if (MessageBox.Show(this, "当前有任务正在处理，是否继续退出？", CommonString.StrReminder, MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    isFormExit = true;
                }
                else
                {
                    e.Cancel = true;
                }
            }
        }

        private void dgContent_DragDrop(object sender, DragEventArgs e)
        {
            if (!(e.Data.GetData(DataFormats.FileDrop) is string[] path) || path.Length <= 0) return;
            var lstFiles = path.Where(Path.HasExtension).ToList();
            var lstDirs = path.Where(p => !Path.HasExtension(p)).ToList();
            if (lstDirs.Count > 0)
            {
                lstDirs.ForEach(p =>
                {
                    lstFiles.AddRange(Directory.GetFiles(p, "*", SearchOption.AllDirectories));
                });
            }
            AddFileToList(lstFiles.ToArray());
        }

        private void dgContent_DragEnter(object sender, DragEventArgs e)
        {
            e.Effect = e.Data.GetDataPresent(DataFormats.FileDrop) ? DragDropEffects.Copy : DragDropEffects.None;
        }

        private void lnkLeft_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            CommonUser.UpdateLimitInfo();
            InitLeftCount();
        }

        private void InitLeftCount()
        {
            lnkLeft.Text = CommonUser.GetTodayLimitInfo(true);
            CommonMethod.SetStyle(lnkLeft, ControlStyles.Selectable, false);
        }
    }


    [Obfuscation]
    internal class BatchOcrItem
    {
        private string _state;

        [Obfuscation]
        public string Id { get; set; }

        [Obfuscation]
        public string FileName { get; set; }

        [Obfuscation]
        public string FullName { get; set; }

        [Obfuscation]
        public OcrType OcrType { get; set; }

        [Obfuscation]
        public OcrFileType FileType { get; set; }

        [Obfuscation]
        public DateTime? DtStart { get; set; }

        [Obfuscation]
        public DateTime? DtEnd { get; set; }

        [Obfuscation]
        public bool HasResult { get; set; }

        [Obfuscation]
        public string State
        {
            get => _state;
            set
            {
                _state = value;
                NotifyPropertyChanged("State");
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        private void NotifyPropertyChanged(string propertyName)
        {
            if (PropertyChanged != null)
                try
                {
                    PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
                }
                catch
                {
                    // ignored
                }
        }
    }
}