﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ImageLib
{
    public class ImageHelper
    {
        private const string StrUrlStart = "http";

        public static string GetResult(byte[] content, string fileExt)
        {
            var tasks = new List<Task<string>>()
            {
                Task.Run(() => ALiYunUpload.GetResult(content)),
                Task.Run(() =>  SouGouImageUpload.GetResult(content)),
                Task.Run(() => _360ImageUpload.GetResult(content)),
                Task.Run(() => TinyPngUpload.GetResultUrl(content)),
                Task.Run(() => WebResizerUpload.GetResult(content)),
                Task.Run(() => Net126Upload.GetResult(content))
            };
            var result = RunTasks(tasks);
            return result;
        }

        static string RunTasks(List<Task<string>> tasks)
        {
            string result = string.Empty;
            ManualResetEvent resetEvent = new ManualResetEvent(false);

            new Thread(() =>
            {
                CancellationTokenSource cts = new CancellationTokenSource();
                try
                {
                    Parallel.ForEach(tasks, new ParallelOptions { MaxDegreeOfParallelism = 2, CancellationToken = cts.Token }, (task, state) =>
                    {
                        if (cts.IsCancellationRequested)
                        {
                            return;
                        }
                        var taskResult = task.Result;
                        if (!string.IsNullOrEmpty(taskResult) && taskResult.ToLower().StartsWith(StrUrlStart)
                        && !resetEvent.WaitOne(0))
                        {
                            result = taskResult;
                            resetEvent.Set();
                            if (!cts.IsCancellationRequested)
                            {
                                cts.Cancel();
                            }
                        }
                        state.Stop();
                    });
                }
                catch { }
                try
                {
                    if (!resetEvent.WaitOne(0))
                    {
                        resetEvent.Set();
                    }
                }
                catch { }
            })
            { Priority = ThreadPriority.Highest }.Start();
            resetEvent.WaitOne(10000);
            return result;
        }
    }
}
