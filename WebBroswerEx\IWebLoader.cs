using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools.WebBroswerEx
{
    /// <summary>
    /// 简单的网页加载接口 - 只关注核心加载功能
    /// </summary>
    public interface IWebLoader : IDisposable
    {
        /// <summary>
        /// 加载器名称
        /// </summary>
        string LoaderName { get; }

        /// <summary>
        /// 底层控件
        /// </summary>
        Control WebControl { get; }

        /// <summary>
        /// 当前URL
        /// </summary>
        string CurrentUrl { get; }

        /// <summary>
        /// 文档标题
        /// </summary>
        string DocumentTitle { get; }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// 加载开始事件
        /// </summary>
        event EventHandler<string> LoadingStarted;

        /// <summary>
        /// 加载完成事件
        /// </summary>
        event EventHandler<LoadCompletedEventArgs> LoadingCompleted;

        /// <summary>
        /// 标题变化事件
        /// </summary>
        event EventHandler<string> TitleChanged;

        /// <summary>
        /// 初始化加载器
        /// </summary>
        Task<bool> InitializeAsync();

        /// <summary>
        /// 加载网页
        /// </summary>
        Task<bool> LoadAsync(string url, string postData = null);

        /// <summary>
        /// 执行JavaScript脚本
        /// </summary>
        /// <param name="script">要执行的JavaScript代码</param>
        /// <returns>脚本执行结果，如果不支持则返回null</returns>
        Task<string> ExecuteScriptAsync(string script);
    }

    /// <summary>
    /// 加载完成事件参数
    /// </summary>
    public class LoadCompletedEventArgs : EventArgs
    {
        public string Url { get; set; }
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; }

        public LoadCompletedEventArgs(string url, bool isSuccess, string errorMessage = null)
        {
            Url = url;
            IsSuccess = isSuccess;
            ErrorMessage = errorMessage;
        }
    }
}
