﻿using ImageLib;
using OCRTools.Common;
using OCRTools.Common.Entity;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Reflection;
using System.Web;

namespace OCRTools
{
    public class OcrHelper
    {
        internal static void QuantizeOcrProcessEntity(OcrProcessEntity processEntity)
        {
            if (!CommonSetting.图片自动压缩 || processEntity.IsQuantiz || string.IsNullOrEmpty(processEntity.FileExt)
                                                              || processEntity.Byts == null || !CommonString.LstCanProcessImageFilesExt.Contains(processEntity.FileExt))
            {
                return;
            }

            try
            {
                var image = ImageCompress.CompressImage(processEntity.Byts, CompressType.助手压缩);
                if (image != null && image.Length > 0)
                {
                    processEntity.Byts = image;
                    processEntity.IsQuantiz = true;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine("QuantizeOcrProcessEntity Error:" + e.Message);
            }
        }

        internal static string LocalOcrState()
        {
            var result = string.Empty;
            try
            {
                result = WebClientExt.GetHtml("http://127.0.0.1:" + CommonSetting.本地识别端口.ToString("F0") + "/Code.do?type=state&t=" + ServerTime.DateTime.Ticks, 10);
            }
            catch (Exception)
            {
                //Log.WriteError("CheckDecodeWeb出错", oe);
            }
            return result;
        }

        internal static OcrContent GetLocalOcrResult(OcrProcessEntity processEntity)
        {
            OcrContent content = null;

            try
            {
                var strPost = "img=" + HttpUtility.UrlEncode(Convert.ToBase64String(processEntity.Byts));
                var url = "http://127.0.0.1:" + CommonSetting.本地识别端口.ToString("F0") + "/Code.do?type=" + processEntity.LocalOcrType + "&id=" + processEntity.Identity;

                try
                {
                    var result = WebClientExt.GetHtml(url, "", "", strPost, "", 30);
                    if (!string.IsNullOrEmpty(result) && result.Length > 2)
                        content = CommonString.JavaScriptSerializer.Deserialize<OcrContent>(result);
                }
                catch (Exception)
                {
                    //Log.WriteError("CheckDecodeWeb出错", oe);
                }
            }
            catch (Exception)
            {
            }

            return content;
        }

        private const string Str_Ocr_Result = "code.ashx?op=codeFile&type={0}&left={1}&top={2}&group={3}&pid={4}&ext={5}&from={6}&to={7}&half={8}&space={9}&symbol={10}&duplicate={11}";
        internal static OcrContent GetResult(OcrProcessEntity processEntity)
        {
            OcrContent content = null;
            try
            {
                var url = string.Format(
                    Str_Ocr_Result
                    , processEntity.OcrType.GetHashCode()
                    , processEntity.IsFromLeftToRight ? "1" : "0"
                    , processEntity.IsFromTopToDown ? "1" : "0"
                    , processEntity.GroupType
                    , processEntity.ProcessId
                    , processEntity.FileExt
                    , processEntity.From.GetHashCode()
                    , processEntity.To.GetHashCode()
                    , CommonSetting.自动全半角转换 ? "1" : "0"
                    , CommonSetting.自动空格 ? "1" : "0"
                    , CommonSetting.自动中英文标点 ? "1" : "0"
                    , CommonSetting.去除重复标点 ? "1" : "0");

                if (CommonSetting.上传到图床 && string.IsNullOrEmpty(processEntity.ImgUrl)
                                           && CommonString.LstCanProcessImageFilesExt.Contains(processEntity.FileExt)
                                           && processEntity.Byts != null && processEntity.Byts.Length > 0)
                {
                    processEntity.ImgUrl = ImageHelper.GetResult(processEntity.Byts, processEntity.FileExt);
                    if (!string.IsNullOrEmpty(processEntity.ImgUrl))
                    {
                        processEntity.Byts = null;
                    }
                }

                try
                {
                    NameValueCollection values = null;
                    if (processEntity.IsImgUrl)
                        values = new NameValueCollection
                        {
                            {"url", processEntity.ImgUrl}
                        };
                    var result = UploadFileRequest.PostFile(url, processEntity.IsImgUrl ? null : processEntity.Byts,
                        processEntity.FileExt, values, CommonMethod.GetRequestHeader());
                    if (!string.IsNullOrEmpty(result) && result.Length > 2)
                        content = CommonString.JavaScriptSerializer.Deserialize<OcrContent>(result);
                }
                catch (Exception)
                {
                    //Log.WriteError("CheckDecodeWeb出错", oe);
                }
            }
            catch (Exception)
            {
            }

            return content;
        }

        internal static string UploadImage(byte[] bytes)
        {
            string result = null;
            try
            {
                result = UploadFileRequest.PostFile("code.ashx?op=imgUpload", bytes, CommonString.StrDefaultImgType, null, CommonMethod.GetRequestHeader());
                if (!string.IsNullOrEmpty(result) && !result.StartsWith("http"))
                    result = null;
            }
            catch (Exception)
            {
                //Log.WriteError("CheckDecodeWeb出错", oe);
            }

            return result;
        }

        internal static string GetVoiceResultUrl(string strContent, string speaker, string voiceSpeed)
        {
            var url = string.Format(CommonString.AutoCodeUrl + "voice/view.html?text={0}&speed={1}&speaker={2}"
                , HttpUtility.UrlEncode(strContent)
                , voiceSpeed
                , speaker);
            return url;
        }

        internal static string GetFileResultUrl(OcrContent content)
        {
            var url = string.Format(CommonString.AutoCodeUrl + "code.ashx?op=htmlfile&param={0}"
                , HttpUtility.UrlEncode(CommonString.JavaScriptSerializer.Serialize(content)));
            return url;
        }

        internal static string GetMathFileResultUrl(OcrContent content)
        {
            var url = string.Format(CommonString.AutoCodeUrl + "math/view.html?m={0}"
                , HttpUtility.UrlEncode(content?.result?.autoText?.Replace("\n", "\\\\"))?.Replace("+", "%20"));
            return url;
        }

        internal static List<OcrContent> GetResultById(string id)
        {
            List<OcrContent> content = null;
            try
            {
                var url = "code.ashx?op=idcode";
                try
                {
                    var result = CommonMethod.GetServerHtml(url, CommonString.AutoCodeUrl, false, true, "id=" + id);
                    if (!string.IsNullOrEmpty(result) && result.Length > 2)
                        content = CommonString.JavaScriptSerializer.Deserialize<List<OcrContent>>(result);
                }
                catch (Exception)
                {
                    //Log.WriteError("CheckDecodeWeb出错", oe);
                }
            }
            catch (Exception)
            {
            }

            return content;
        }

        internal static bool SendRegMsg(string mobileNo, ref string strMsg)
        {
            var result = false;
            try
            {
                var url = string.Format("code.aspx?op=validatemsg&mobile={0}", mobileNo);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                if (!url?.ToLower().Contains("true") == true)
                    strMsg = url;
                else
                    result = true;
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static void SendReportInfo(string content, List<UploadFileInfo> lstFiles, ref string strMsg)
        {
            try
            {
                var url = CommonString.AutoCodeUrl + "code.ashx?op=report";
                var values = new NameValueCollection { { "content", content } };
                var result = UploadFileRequest.Post(url, lstFiles.ToArray(), values, CommonMethod.GetRequestHeader());
                if (!result?.ToLower().Contains("true") == true) strMsg = result;
            }
            catch (Exception)
            {
            }
        }

        internal static bool SendRegInfo(string account, string pwd, string nickName, string code, ref string strMsg)
        {
            var result = false;
            try
            {
                var url = string.Format("code.aspx?op=reg&account={0}&pwd={1}&code={2}&nick={3}", account, pwd, code,
                    nickName);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                if (!url?.ToLower().Contains("true") == true)
                    strMsg = url;
                else
                    result = true;
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static bool SendResetPwdMail(string email, ref string strMsg)
        {
            var result = false;
            try
            {
                var url = string.Format("mail.aspx?op=forgetpwd&email={0}", email);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl);
                if (!url?.ToLower().Contains("true") == true)
                    strMsg = url;
                else
                    result = true;
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static bool SendResetPwdMsg(string mobileNo, ref string strMsg)
        {
            var result = false;
            try
            {
                var url = string.Format("code.aspx?op=forgetpwd&mobile={0}", mobileNo);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                if (!url?.ToLower().Contains("true") == true)
                    strMsg = url;
                else
                    result = true;
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static bool SendResetPwdInfo(string account, string pwd, string code, ref string strMsg)
        {
            var result = false;
            try
            {
                var url = string.Format("code.aspx?op=resetpwd&account={0}&pwd={1}&code={2}", account, pwd, code);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                if (!url?.ToLower().Contains("true") == true)
                    strMsg = url;
                else
                    result = true;
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static bool EditNickName(string account, string strNickName, ref string strMsg)
        {
            var result = false;
            try
            {
                var url = string.Format("code.aspx?op=resetnickname&account={0}&nick={1}", account, strNickName);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                if (!url?.ToLower().Contains("true") == true)
                    strMsg = url;
                else
                    result = true;
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static bool DoLogin(string account, string pwd, ref string strMsg)
        {
            var result = false;
            try
            {
                var url = string.Format("code.aspx?op=login&account={0}&pwd={1}", account, pwd);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                if (!url?.ToLower().Contains("true") == true)
                {
                    //"True|" + user.StrAppCode + "|" + user.StrType + "|" + user.StrNickName + "|" + user.DtReg.ToString("yyyy-MM-dd HH:mm:ss") + "|" + user.DtExpire.ToString("yyyy-MM-dd HH:mm:ss") + "|" + user.StrRemark
                    strMsg = url;
                }
                else
                {
                    if (!string.IsNullOrEmpty(url))
                    {
                        url = url.Substring(url.IndexOf("|") + 1);
                        Program.NowUser = CommonString.JavaScriptSerializer.Deserialize<UserEntity>(url);
                    }

                    result = true;
                }
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static bool IsLogouted()
        {
            var result = false;
            try
            {
                var url = CommonMethod.GetServerHtml("code.aspx?op=heart", CommonString.HostAccountUrl, false);
                if (!string.IsNullOrEmpty(url) && url.ToLower().Contains("false")) result = true;
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static UserCodeCount GetCodeCount()
        {
            UserCodeCount result = null;
            try
            {
                var html = CommonMethod.GetServerHtml("code.aspx?op=count", CommonString.HostAccountUrl, false);
                if (!string.IsNullOrEmpty(html))
                    result = CommonString.JavaScriptSerializer.Deserialize<UserCodeCount>(html);
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static List<UserType> GetCanRegUserTypes()
        {
            List<UserType> content = null;
            try
            {
                var url = "code.aspx?op=regtype";
                try
                {
                    var result = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                    if (!string.IsNullOrEmpty(result))
                        content = CommonString.JavaScriptSerializer.Deserialize<List<UserType>>(result);
                }
                catch (Exception)
                {
                    //Log.WriteError("CheckDecodeWeb出错", oe);
                }
            }
            catch (Exception)
            {
            }

            return content;
        }

        internal static void InitOcrGroup()
        {
            var dicTmp = GetServerOcrGroupTypes();
            if (dicTmp.Count > 0)
            {
                LstServerOcrGroup = dicTmp;
            }
            dicTmp = GetLocalOcrGroupTypes();
            if (dicTmp.Count > 0)
            {
                LstLocalOcrType = dicTmp;
            }
        }

        internal static List<ObjectTypeItem> GetLocalOcrGroupTypes()
        {
            var content = new List<ObjectTypeItem>();
            try
            {
                var result =
                    CommonMethod.GetServerHtml("code.ashx?op=localGroup", CommonString.AutoCodeUrl, false, true);
                if (!string.IsNullOrEmpty(result))
                    content = CommonString.JavaScriptSerializer.Deserialize<List<ObjectTypeItem>>(result);
            }
            catch (Exception)
            {
                //Log.WriteError("CheckDecodeWeb出错", oe);
            }
            return content;
        }

        internal static List<ObjectTypeItem> GetServerOcrGroupTypes()
        {
            var content = new List<ObjectTypeItem>();
            try
            {
                var result =
                    CommonMethod.GetServerHtml("code.ashx?op=serverGroup", CommonString.AutoCodeUrl, false, true);
                if (!string.IsNullOrEmpty(result))
                    content = CommonString.JavaScriptSerializer.Deserialize<List<ObjectTypeItem>>(result);
            }
            catch (Exception)
            {
                //Log.WriteError("CheckDecodeWeb出错", oe);
            }
            return content;
        }

        internal static List<ObjectTypeItem> LstLocalOcrType = new List<ObjectTypeItem>();

        internal static List<ObjectTypeItem> LstServerOcrGroup = new List<ObjectTypeItem>
        {
            new ObjectTypeItem{Code =0,Name ="不限"},
            new ObjectTypeItem{Code =1,Name ="百度"},
            new ObjectTypeItem{Code =2,Name ="腾讯"},
            new ObjectTypeItem{Code =4,Name ="有道"},
            new ObjectTypeItem{Code =5,Name ="搜狗"},
            new ObjectTypeItem{Code =6,Name ="讯飞"},
            new ObjectTypeItem{Code =7,Name ="迅捷"},
            new ObjectTypeItem{Code =8,Name ="VIVO"},
            new ObjectTypeItem{Code =10,Name ="学而思"},
            new ObjectTypeItem{Code =11,Name ="汉王"},
        };

        public static int GetGroupByName(string name)
        {
            return LstServerOcrGroup.FirstOrDefault(p => Equals(p.Name, name))?.Code ?? 0;
        }
    }

    [Obfuscation]
    internal class ObjectTypeItem
    {
        [Obfuscation]
        public string Name { get; set; }
        [Obfuscation]
        public int Code { get; set; }
        [Obfuscation]
        public string Desc { get; set; }
        [Obfuscation]
        public string Remark { get; set; }
        [Obfuscation]
        public string DescUrl { get; set; }
        [Obfuscation]
        public string UpdateUrl { get; set; }
        [Obfuscation]
        public string AppPath { get; set; }
        [Obfuscation]
        public DateTime Date { get; set; }
    }

    public enum OcrType
    {
        文本 = 0,
        竖排 = 1,
        表格 = 2,
        公式 = 3,
        翻译 = 4
    }

    public enum OcrModel
    {
        本地加网络 = 0,
        仅网络识别 = 1,
        仅本地识别 = 2,
    }
}