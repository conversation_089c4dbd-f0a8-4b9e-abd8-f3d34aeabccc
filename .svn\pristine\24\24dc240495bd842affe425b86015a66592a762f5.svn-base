﻿using System;
using System.Collections.Generic;

namespace OCRTools
{
    public class CommonConfig
    {
        private static List<UserMsgEntity> _serverConfig = new List<UserMsgEntity>();

        static CommonConfig()
        {
            InitServerConfig();
        }

        public static List<UserMsgEntity> GetServerConfig()
        {
            if (_serverConfig == null || _serverConfig.Count <= 0)
            {
                InitServerConfig();
            }
            return _serverConfig;
        }

        static void InitServerConfig()
        {
            try
            {
                var url = "code.ashx?op=tipconfig";
                try
                {
                    var result = CommonMethod.GetServerHtml(url, CommonString.HostCode?.FullUrl, false, true);
                    if (!string.IsNullOrEmpty(result) && result.Length > 2)
                        _serverConfig = CommonString.JavaScriptSerializer.Deserialize<List<UserMsgEntity>>(result.Replace("\\\\", "\\"));
                }
                catch (Exception)
                {
                    //Log.WriteError("CheckDecodeWeb出错", oe);
                }
            }
            catch { }
        }
    }
}