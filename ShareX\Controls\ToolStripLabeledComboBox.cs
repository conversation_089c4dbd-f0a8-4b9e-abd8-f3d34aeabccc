﻿using OCRTools.ShareX.Controls;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace OCRTools.HelpersLib
{
    [ToolStripItemDesignerAvailability(ToolStripItemDesignerAvailability.MenuStrip | ToolStripItemDesignerAvailability.ContextMenuStrip)]
    public class ToolStripLabeledComboBox : ToolStripControlHost
    {
        public LineShapeComboBox Content => Control as LineShapeComboBox;

        public ToolStripLabeledComboBox(string text, int imageType) : base(new LineShapeComboBox())
        {
            Content.Width = 103;
            Content.Text = text;
            Content.ImageType = imageType;
        }
    }
}