﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools.Common
{
    public interface IClipboardNative
    {
        event EventHandler<ClipboardChangedEventArgs> ClipboardChanged;

        void Copy(string text);

        void Paste();

        void StartListening(bool isExpSelf = true);

        void StopListening();
    }

    public class ClipboardNative : IClipboardNative
    {
        private static class PasteWorkaround
        {
            private struct INPUT
            {
                internal uint type;

                internal InputUnion data;
            }

            [StructLayout(LayoutKind.Explicit)]
            private struct InputUnion
            {
                [FieldOffset(0)]
                internal MOUSEINPUT mi;

                [FieldOffset(0)]
                internal KEYBDINPUT ki;

                [FieldOffset(0)]
                internal HARDWAREINPUT hi;
            }

            private struct KEYBDINPUT
            {
                internal ushort wVk;

                internal ushort wScan;

                internal KEYEVENTF dwFlags;

                internal int time;

                internal UIntPtr dwExtraInfo;
            }

            private struct MOUSEINPUT
            {
                internal int x;

                internal int y;

                internal uint mouseData;

                internal uint flags;

                internal uint time;

                internal IntPtr extraInfo;
            }

            private struct HARDWAREINPUT
            {
                internal uint msg;

                internal ushort paramL;

                internal ushort paramH;
            }

            [Flags]
            private enum KEYEVENTF : uint
            {
                KEYDOWN = 0x0,
                EXTENDEDKEY = 0x1,
                KEYUP = 0x2,
                SCANCODE = 0x8,
                UNICODE = 0x4
            }

            private const uint keyboardInput = 1u;

            [DllImport("user32.dll")]
            private static extern uint SendInput(uint nInputs, [In][MarshalAs(UnmanagedType.LPArray)] INPUT[] pInputs, int cbSize);

            private static INPUT Input(Key key, bool keyUp)
            {
                KEYEVENTF kEYEVENTF = keyUp ? KEYEVENTF.KEYUP : KEYEVENTF.KEYDOWN;
                if (key == Key.CONTROL)
                {
                    kEYEVENTF |= KEYEVENTF.EXTENDEDKEY;
                }
                INPUT result = default(INPUT);
                result.type = 1u;
                result.data = new InputUnion
                {
                    ki = new KEYBDINPUT
                    {
                        wVk = (ushort)key,
                        dwFlags = kEYEVENTF
                    }
                };
                return result;
            }

            public static void SendPaste()
            {
                INPUT[] array = new INPUT[4]
                {
                Input(Key.CONTROL, keyUp: false),
                Input(Key.V, keyUp: false),
                Input(Key.V, keyUp: true),
                Input(Key.CONTROL, keyUp: true)
                };
                uint num = SendInput((uint)array.Length, array, Marshal.SizeOf(array[0].GetType()));
                if (num != 0 && num != 4)
                {
                    int lastWin32Error = Marshal.GetLastWin32Error();
                    throw new Win32Exception(lastWin32Error);
                }
            }
        }

        private const int wmClipboardupdate = 797;

        private readonly RetryClipboardWriter retryClipboardWriter = new RetryClipboardWriter();

        private readonly IActiveWindowListener activeWindowListener;

        private readonly int currentProcessId;

        private IntPtr handle = IntPtr.Zero;

        public event EventHandler<ClipboardChangedEventArgs> ClipboardChanged;

        public ClipboardNative(IActiveWindowListener activeWindowListener)
        {
            this.activeWindowListener = (activeWindowListener ?? throw new ArgumentNullException("activeWindowListener"));
            using (var process = Process.GetCurrentProcess())
            {
                currentProcessId = process.Id;
            }
        }

        public void Copy(string text)
        {
            retryClipboardWriter.Copy(text);
        }

        private ClipboardText GetClipboardText(int sourceProcessId, int sequenceNumber)
        {
            try
            {
                var text = Clipboard.GetText();
                if (text != null)
                {
                    return new ClipboardText(sourceProcessId, activeWindowListener.ActiveWindowHandle, sequenceNumber, text, null);
                }
            }
            catch (Exception)
            {
            }
            return ClipboardText.Empty;
        }

        private IntPtr OnClipboardChanged(IntPtr hwnd, int msg, IntPtr wParam, IntPtr lParam, ref bool handled)
        {
            if (msg == 797)
            {
                ClipboardMethods.GetWindowThreadProcessId(activeWindowListener.ActiveWindowHandle, out uint processId);
                if (!IsExpSelf || (IsExpSelf && processId != currentProcessId))
                {
                    int clipboardSequenceNumber = ClipboardMethods.GetClipboardSequenceNumber();
                    ClipboardText clipboardText = GetClipboardText((int)processId, clipboardSequenceNumber);
                    RaiseClipboardChanged(clipboardText);
                }
            }
            return IntPtr.Zero;
        }

        public void Paste()
        {
            PasteWorkaround.SendPaste();
        }

        private void RaiseClipboardChanged(ClipboardText clipboardText)
        {
            this.ClipboardChanged?.Invoke(this, new ClipboardChangedEventArgs(clipboardText));
        }

        private bool IsExpSelf = true;

        public void StartListening(bool isExpSelf = true)
        {
            IsExpSelf = isExpSelf;
            handle = StaticValue.handles[0];
            ClipboardMethods.AddClipboardFormatListener(handle);
        }

        public void StopListening()
        {
            if (handle != IntPtr.Zero)
            {
                ClipboardMethods.RemoveClipboardFormatListener(handle);
            }
        }
    }

    public class ClipboardChangedEventArgs : EventArgs
    {
        public ClipboardText ClipboardText
        {
            get;
        }

        public ClipboardChangedEventArgs(ClipboardText clipboardText)
        {
            ClipboardText = clipboardText;
        }
    }
    public enum Key
    {
        LBUTTON = 1,
        RBUTTON = 2,
        CANCEL = 3,
        MBUTTON = 4,
        XBUTTON1 = 5,
        XBUTTON2 = 6,
        BACK = 8,
        TAB = 9,
        CLEAR = 12,
        RETURN = 13,
        SHIFT = 0x10,
        CONTROL = 17,
        MENU = 18,
        PAUSE = 19,
        CAPITAL = 20,
        KANA = 21,
        HANGUEL = 21,
        HANGUL = 21,
        JUNJA = 23,
        FINAL = 24,
        HANJA = 25,
        KANJI = 25,
        ESCAPE = 27,
        CONVERT = 28,
        NONCONVERT = 29,
        ACCEPT = 30,
        MODECHANGE = 0x1F,
        SPACE = 0x20,
        PRIOR = 33,
        NEXT = 34,
        END = 35,
        HOME = 36,
        LEFT = 37,
        UP = 38,
        RIGHT = 39,
        DOWN = 40,
        SELECT = 41,
        PRINT = 42,
        EXECUTE = 43,
        SNAPSHOT = 44,
        INSERT = 45,
        DELETE = 46,
        HELP = 47,
        KEY_0 = 48,
        KEY_1 = 49,
        KEY_2 = 50,
        KEY_3 = 51,
        KEY_4 = 52,
        KEY_5 = 53,
        KEY_6 = 54,
        KEY_7 = 55,
        KEY_8 = 56,
        KEY_9 = 57,
        A = 65,
        B = 66,
        C = 67,
        D = 68,
        E = 69,
        F = 70,
        G = 71,
        H = 72,
        I = 73,
        J = 74,
        K = 75,
        L = 76,
        M = 77,
        N = 78,
        O = 79,
        P = 80,
        Q = 81,
        R = 82,
        S = 83,
        T = 84,
        U = 85,
        V = 86,
        W = 87,
        X = 88,
        Y = 89,
        Z = 90,
        LWIN = 91,
        RWIN = 92,
        APPS = 93,
        SLEEP = 95,
        NUMPAD0 = 96,
        NUMPAD1 = 97,
        NUMPAD2 = 98,
        NUMPAD3 = 99,
        NUMPAD4 = 100,
        NUMPAD5 = 101,
        NUMPAD6 = 102,
        NUMPAD7 = 103,
        NUMPAD8 = 104,
        NUMPAD9 = 105,
        MULTIPLY = 106,
        ADD = 107,
        SEPARATOR = 108,
        SUBTRACT = 109,
        DECIMAL = 110,
        DIVIDE = 111,
        F1 = 112,
        F2 = 113,
        F3 = 114,
        F4 = 115,
        F5 = 116,
        F6 = 117,
        F7 = 118,
        F8 = 119,
        F9 = 120,
        F10 = 121,
        F11 = 122,
        F12 = 123,
        F13 = 124,
        F14 = 125,
        F15 = 126,
        F16 = 0x7F,
        F17 = 0x80,
        F18 = 129,
        F19 = 130,
        F20 = 131,
        F21 = 132,
        F22 = 133,
        F23 = 134,
        F24 = 135,
        NUMLOCK = 144,
        SCROLL = 145,
        LSHIFT = 160,
        RSHIFT = 161,
        LCONTROL = 162,
        RCONTROL = 163,
        LMENU = 164,
        RMENU = 165,
        BROWSER_BACK = 166,
        BROWSER_FORWARD = 167,
        BROWSER_REFRESH = 168,
        BROWSER_STOP = 169,
        BROWSER_SEARCH = 170,
        BROWSER_FAVORITES = 171,
        BROWSER_HOME = 172,
        VOLUME_MUTE = 173,
        VOLUME_DOWN = 174,
        VOLUME_UP = 175,
        MEDIA_NEXT_TRACK = 176,
        MEDIA_PREV_TRACK = 177,
        MEDIA_STOP = 178,
        MEDIA_PLAY_PAUSE = 179,
        LAUNCH_MAIL = 180,
        LAUNCH_MEDIA_SELECT = 181,
        LAUNCH_APP1 = 182,
        LAUNCH_APP2 = 183,
        OEM_1 = 186,
        OEM_PLUS = 187,
        OEM_COMMA = 188,
        OEM_MINUS = 189,
        OEM_PERIOD = 190,
        OEM_2 = 191,
        OEM_3 = 192,
        OEM_4 = 219,
        OEM_5 = 220,
        OEM_6 = 221,
        OEM_7 = 222,
        OEM_8 = 223,
        OEM_102 = 226,
        PROCESSKEY = 229,
        PACKET = 231,
        ATTN = 246,
        CRSEL = 247,
        EXSEL = 248,
        EREOF = 249,
        PLAY = 250,
        ZOOM = 251,
        NONAME = 252,
        PA1 = 253,
        OEM_CLEAR = 254
    }
}
