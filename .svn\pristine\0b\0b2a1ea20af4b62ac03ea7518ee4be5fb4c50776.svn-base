﻿/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

using MetroFramework.Controls;
using MetroFramework.Native;
using System;
using System.Collections;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace MetroFramework.Design
{
    internal class MetroTabControlDesigner : ParentControlDesigner
    {
        private readonly DesignerVerbCollection designerVerbs = new DesignerVerbCollection();

        private IDesignerHost designerHost;

        private ISelectionService selectionService;

        public MetroTabControlDesigner()
        {
            var designerVerb = new DesignerVerb("Add Tab", OnAddPage);
            var designerVerb2 = new DesignerVerb("Remove Tab", OnRemovePage);
            designerVerbs.AddRange(new DesignerVerb[2]
            {
                designerVerb,
                designerVerb2
            });
        }

        public override SelectionRules SelectionRules
        {
            get
            {
                if (Control.Dock != DockStyle.Fill) return base.SelectionRules;
                return SelectionRules.Visible;
            }
        }

        public override DesignerVerbCollection Verbs
        {
            get
            {
                if (designerVerbs.Count == 2)
                {
                    var metroTabControl = (MetroTabControl) Control;
                    designerVerbs[1].Enabled = metroTabControl.TabCount != 0;
                }

                return designerVerbs;
            }
        }

        public IDesignerHost DesignerHost =>
            designerHost ?? (designerHost = (IDesignerHost) GetService(typeof(IDesignerHost)));

        public ISelectionService SelectionService => selectionService ??
                                                     (selectionService =
                                                         (ISelectionService) GetService(typeof(ISelectionService)));

        private void OnAddPage(object sender, EventArgs e)
        {
            var metroTabControl = (MetroTabControl) Control;
            var controls = metroTabControl.Controls;
            RaiseComponentChanging(TypeDescriptor.GetProperties(metroTabControl)["TabPages"]);
            var metroTabPage = (MetroTabPage) DesignerHost.CreateComponent(typeof(MetroTabPage));
            metroTabPage.Text = metroTabPage.Name;
            metroTabControl.TabPages.Add(metroTabPage);
            RaiseComponentChanged(TypeDescriptor.GetProperties(metroTabControl)["TabPages"], controls,
                metroTabControl.TabPages);
            metroTabControl.SelectedTab = metroTabPage;
            SetVerbs();
        }

        private void OnRemovePage(object sender, EventArgs e)
        {
            var metroTabControl = (MetroTabControl) Control;
            var controls = metroTabControl.Controls;
            if (metroTabControl.SelectedIndex >= 0)
            {
                RaiseComponentChanging(TypeDescriptor.GetProperties(metroTabControl)["TabPages"]);
                DesignerHost.DestroyComponent(metroTabControl.TabPages[metroTabControl.SelectedIndex]);
                RaiseComponentChanged(TypeDescriptor.GetProperties(metroTabControl)["TabPages"], controls,
                    metroTabControl.TabPages);
                SelectionService.SetSelectedComponents(new IComponent[1]
                {
                    metroTabControl
                }, SelectionTypes.Auto);
                SetVerbs();
            }
        }

        private void SetVerbs()
        {
            var metroTabControl = (MetroTabControl) Control;
            if (metroTabControl.TabPages.Count == 0)
                Verbs[1].Enabled = false;
            else
                Verbs[1].Enabled = true;
        }

        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);
            var msg = m.Msg;
            if (msg == 132 && m.Result.ToInt32() == -1) m.Result = (IntPtr) 1L;
        }

        protected override bool GetHitTest(Point point)
        {
            if (SelectionService.PrimarySelection == Control)
            {
                var tCHITTESTINFO = default(WinApi.TCHITTESTINFO);
                tCHITTESTINFO.pt = Control.PointToClient(point);
                tCHITTESTINFO.flags = 0u;
                var tCHITTESTINFO2 = tCHITTESTINFO;
                var message = default(Message);
                message.HWnd = Control.Handle;
                message.Msg = 4883;
                var m = message;
                var intPtr = Marshal.AllocHGlobal(Marshal.SizeOf(tCHITTESTINFO2));
                Marshal.StructureToPtr(tCHITTESTINFO2, intPtr, false);
                m.LParam = intPtr;
                base.WndProc(ref m);
                Marshal.FreeHGlobal(intPtr);
                if (m.Result.ToInt32() != -1) return tCHITTESTINFO2.flags != 1;
            }

            return false;
        }

        protected override void PreFilterProperties(IDictionary properties)
        {
            properties.Remove("ImeMode");
            properties.Remove("Padding");
            properties.Remove("FlatAppearance");
            properties.Remove("FlatStyle");
            properties.Remove("AutoEllipsis");
            properties.Remove("UseCompatibleTextRendering");
            properties.Remove("Image");
            properties.Remove("ImageAlign");
            properties.Remove("ImageIndex");
            properties.Remove("ImageKey");
            properties.Remove("ImageList");
            properties.Remove("TextImageRelation");
            properties.Remove("BackgroundImage");
            properties.Remove("BackgroundImageLayout");
            properties.Remove("UseVisualStyleBackColor");
            properties.Remove("Font");
            properties.Remove("RightToLeft");
            base.PreFilterProperties(properties);
        }
    }

    #region MetroTabPageCollectionEditor

    internal class MetroTabPageCollectionEditor : CollectionEditor
    {
        public MetroTabPageCollectionEditor(Type type)
            : base(type)
        {
        }

        protected override CollectionForm CreateCollectionForm()
        {
            var collectionForm = base.CreateCollectionForm();
            collectionForm.Text = "MetroTabPage Collection Editor";
            return collectionForm;
        }

        protected override Type CreateCollectionItemType()
        {
            return typeof(MetroTabPage);
        }

        protected override Type[] CreateNewItemTypes()
        {
            return new Type[1]
            {
                typeof(MetroTabPage)
            };
        }
    }

    #endregion
}