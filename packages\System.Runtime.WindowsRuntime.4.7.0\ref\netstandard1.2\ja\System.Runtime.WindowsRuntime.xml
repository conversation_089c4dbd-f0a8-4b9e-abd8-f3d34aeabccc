﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.WindowsRuntime</name>
  </assembly>
  <members>
    <member name="T:System.WindowsRuntimeSystemExtensions">
      <summary>タスクと Windows ランタイム の非同期操作および操作の間の変換のための拡張メソッドを提供します。</summary>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsAsyncAction(System.Threading.Tasks.Task)">
      <summary>開始されたタスクを表す Windows ランタイム 非同期アクションを返します。</summary>
      <returns>開始されたタスクを表す Windows.Foundation.IAsyncAction インスタンス。</returns>
      <param name="source">開始されたタスク。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> は未開始のタスクです。</exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsAsyncOperation``1(System.Threading.Tasks.Task{``0})">
      <summary>結果を返す開始されたタスクを表す Windows ランタイム 非同期操作を返します。</summary>
      <returns>開始されたタスクを表す Windows.Foundation.IAsyncOperation&lt;TResult&gt; インスタンス。</returns>
      <param name="source">開始されたタスク。</param>
      <typeparam name="TResult">結果を返す型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> は未開始のタスクです。</exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask(Windows.Foundation.IAsyncAction)">
      <summary>Windows ランタイム 非同期アクションを表すタスクを返します。</summary>
      <returns>非同期アクションを表すタスク。</returns>
      <param name="source">非同期アクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask(Windows.Foundation.IAsyncAction,System.Threading.CancellationToken)">
      <summary>キャンセル可能な Windows ランタイム 非同期アクションを表すタスクを返します。</summary>
      <returns>非同期アクションを表すタスク。</returns>
      <param name="source">非同期アクション。</param>
      <param name="cancellationToken">非同期アクションのキャンセルを要求するために使用できるトークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0})">
      <summary>Windows ランタイム 非同期アクションを表すタスクを返します。</summary>
      <returns>非同期アクションを表すタスク。</returns>
      <param name="source">非同期アクション。</param>
      <typeparam name="TProgress">進行状況を示すデータを提供するオブジェクトの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0},System.IProgress{``0})">
      <summary>進行状況を報告する Windows ランタイム 非同期アクションを表すタスクを返します。</summary>
      <returns>非同期アクションを表すタスク。</returns>
      <param name="source">非同期アクション。</param>
      <param name="progress">進行状況の更新を受け取るオブジェクト。</param>
      <typeparam name="TProgress">進行状況を示すデータを提供するオブジェクトの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0},System.Threading.CancellationToken)">
      <summary>キャンセル可能な Windows ランタイム 非同期アクションを表すタスクを返します。</summary>
      <returns>非同期アクションを表すタスク。</returns>
      <param name="source">非同期アクション。</param>
      <param name="cancellationToken">非同期アクションのキャンセルを要求するために使用できるトークン。</param>
      <typeparam name="TProgress">進行状況を示すデータを提供するオブジェクトの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncActionWithProgress{``0},System.Threading.CancellationToken,System.IProgress{``0})">
      <summary>進行状況を報告し、かつキャンセル可能な Windows ランタイム 非同期アクションを表すタスクを返します。</summary>
      <returns>非同期アクションを表すタスク。</returns>
      <param name="source">非同期アクション。</param>
      <param name="cancellationToken">非同期アクションのキャンセルを要求するために使用できるトークン。</param>
      <param name="progress">進行状況の更新を受け取るオブジェクト。</param>
      <typeparam name="TProgress">進行状況を示すデータを提供するオブジェクトの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncOperation{``0})">
      <summary>結果を返す Windows ランタイム 非同期操作を表すタスクを返します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="source">非同期操作。</param>
      <typeparam name="TResult">非同期操作の結果を返すオブジェクトの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``1(Windows.Foundation.IAsyncOperation{``0},System.Threading.CancellationToken)">
      <summary>結果を返しかつキャンセル可能な Windows ランタイム 非同期操作を表すタスクを返します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="source">非同期操作。</param>
      <param name="cancellationToken">非同期操作のキャンセルを要求するために使用できるトークン。</param>
      <typeparam name="TResult">非同期操作の結果を返すオブジェクトの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1})">
      <summary>結果を返す Windows ランタイム 非同期操作を表すタスクを返します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="source">非同期操作。</param>
      <typeparam name="TResult">非同期操作の結果を返すオブジェクトの型。</typeparam>
      <typeparam name="TProgress">進行状況を示すデータを提供するオブジェクトの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1},System.IProgress{``1})">
      <summary>結果を返し、かつ進行状況を報告する Windows ランタイム 非同期操作を表すタスクを返します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="source">非同期操作。</param>
      <param name="progress">進行状況の更新を受け取るオブジェクト。</param>
      <typeparam name="TResult">非同期操作の結果を返すオブジェクトの型。</typeparam>
      <typeparam name="TProgress">進行状況を示すデータを提供するオブジェクトの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1},System.Threading.CancellationToken)">
      <summary>結果を返しかつキャンセル可能な Windows ランタイム 非同期操作を表すタスクを返します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="source">非同期操作。</param>
      <param name="cancellationToken">非同期操作のキャンセルを要求するために使用できるトークン。</param>
      <typeparam name="TResult">非同期操作の結果を返すオブジェクトの型。</typeparam>
      <typeparam name="TProgress">進行状況を示すデータを提供するオブジェクトの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.AsTask``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1},System.Threading.CancellationToken,System.IProgress{``1})">
      <summary>結果を返し、進行状況を報告し、かつキャンセル可能な Windows ランタイム 非同期操作を表すタスクを返します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="source">非同期操作。</param>
      <param name="cancellationToken">非同期操作のキャンセルを要求するために使用できるトークン。</param>
      <param name="progress">進行状況の更新を受け取るオブジェクト。</param>
      <typeparam name="TResult">非同期操作の結果を返すオブジェクトの型。</typeparam>
      <typeparam name="TProgress">進行状況を示すデータを提供するオブジェクトの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter(Windows.Foundation.IAsyncAction)">
      <summary>非同期アクションを待つオブジェクトを返します。</summary>
      <returns>指定された非同期操作のために待機しているオブジェクト。</returns>
      <param name="source">待機する非同期アクション。</param>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter``1(Windows.Foundation.IAsyncActionWithProgress{``0})">
      <summary>進行状況を報告する非同期アクションを待つオブジェクトを返します。</summary>
      <returns>指定された非同期操作のために待機しているオブジェクト。</returns>
      <param name="source">待機する非同期アクション。</param>
      <typeparam name="TProgress">進行状況を示すデータを提供するオブジェクトの型。</typeparam>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter``1(Windows.Foundation.IAsyncOperation{``0})">
      <summary>結果を返す非同期操作を待つオブジェクトを返します。</summary>
      <returns>指定された非同期操作のために待機しているオブジェクト。</returns>
      <param name="source">待機する非同期操作。</param>
      <typeparam name="TResult">非同期操作の結果を返すオブジェクトの型。</typeparam>
    </member>
    <member name="M:System.WindowsRuntimeSystemExtensions.GetAwaiter``2(Windows.Foundation.IAsyncOperationWithProgress{``0,``1})">
      <summary>進行状況を報告し、結果を返す非同期操作を待つオブジェクトを返します。</summary>
      <returns>指定された非同期操作のために待機しているオブジェクト。</returns>
      <param name="source">待機する非同期操作。</param>
      <typeparam name="TResult">非同期操作の結果を返すオブジェクトの型。</typeparam>
      <typeparam name="TProgress">進行状況を示すデータを提供するオブジェクトの型。</typeparam>
    </member>
    <member name="T:System.IO.WindowsRuntimeStorageExtensions">
      <summary>Windows ストア アプリケーション開発時の Windows ランタイム 内の IStorageFile インターフェイスおよび IStorageFolder インターフェイス用の拡張メソッドを格納します。</summary>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForReadAsync(Windows.Storage.IStorageFile)">
      <summary>指定したファイルから読み取るストリームを取得します。</summary>
      <returns>非同期の読み取り操作を表すタスク。</returns>
      <param name="windowsRuntimeFile">読み取り元の Windows ランタイム IStorageFile オブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeFile" /> は null なので、</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くことができなかったか、ストリームとして取得できませんでした。</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForReadAsync(Windows.Storage.IStorageFolder,System.String)">
      <summary>指定された親フォルダーのファイルから読み取るストリームを取得します。</summary>
      <returns>非同期の読み取り操作を表すタスク。</returns>
      <param name="rootDirectory">読み取り元のファイルを含む Windows ランタイム IStorageFolder オブジェクト。</param>
      <param name="relativePath">ルート フォルダーを基準とした、読み取りファイルのパス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rootDirectory" /> または <paramref name="relativePath" /> が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="relativePath" /> が空であるか、空白文字だけで構成されています。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くことができなかったか、ストリームとして取得できませんでした。</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForWriteAsync(Windows.Storage.IStorageFile)">
      <summary>指定したファイルへ書き込むストリームを取得します。</summary>
      <returns>非同期の書き込み操作を表すタスク。</returns>
      <param name="windowsRuntimeFile">書き込み先の Windows ランタイム IStorageFile オブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeFile" /> は null なので、</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くことができなかったか、ストリームとして取得できませんでした。</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStorageExtensions.OpenStreamForWriteAsync(Windows.Storage.IStorageFolder,System.String,Windows.Storage.CreationCollisionOption)">
      <summary>指定された親フォルダーのファイルへ書き込むストリームを取得します。</summary>
      <returns>非同期の書き込み操作を表すタスク。</returns>
      <param name="rootDirectory">書き込み先のファイルを含む Windows ランタイム IStorageFolder オブジェクト。</param>
      <param name="relativePath">ルート フォルダーを基準とした、書き込み先のファイルのパス。</param>
      <param name="creationCollisionOption">作成するファイルの名前が既存のファイルの名前と同じになるときに使用する動作を指定する Windows ランタイム CreationCollisionOption 列挙値。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rootDirectory" /> または <paramref name="relativePath" /> が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="relativePath" /> が空であるか、空白文字だけで構成されています。</exception>
      <exception cref="T:System.IO.IOException">ファイルを開くことができなかったか、ストリームとして取得できませんでした。</exception>
    </member>
    <member name="T:System.IO.WindowsRuntimeStreamExtensions">
      <summary>Windows ランタイム のストリームと Windows ストア アプリ用 .NET のマネージ ストリームの間の変換のための拡張メソッドを格納します。</summary>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsInputStream(System.IO.Stream)">
      <summary>Windows ストア アプリ用 .NET のマネージ ストリームを Windows ランタイム の入力ストリームに変換します。</summary>
      <returns>変換されたストリームを表す Windows ランタイム IInputStream オブジェクト。</returns>
      <param name="stream">変換するストリーム。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> は null なので、</exception>
      <exception cref="T:System.NotSupportedException">ストリームは読み取りをサポートしません。</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsOutputStream(System.IO.Stream)">
      <summary>Windows ストア アプリ用 .NET のマネージ ストリームを Windows ランタイム の出力ストリームに変換します。</summary>
      <returns>変換されたストリームを表す Windows ランタイム IOutputStream オブジェクト。</returns>
      <param name="stream">変換するストリーム。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> は null なので、</exception>
      <exception cref="T:System.NotSupportedException">ストリームは書き込みをサポートしません。</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsRandomAccessStream(System.IO.Stream)">
      <summary>特定のストリームをランダム アクセス ストリームに変換します。</summary>
      <returns>変換済みのストリームを表す Windows ランタイム RandomAccessStream。</returns>
      <param name="stream">変換するストリーム。</param>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStream(Windows.Storage.Streams.IRandomAccessStream)">
      <summary>Windows ランタイム のランダム アクセス ストリームを Windows ストア アプリ用 .NET のマネージ ストリームに変換します。</summary>
      <returns>変換されたストリーム。</returns>
      <param name="windowsRuntimeStream">変換する Windows ランタイム IRandomAccessStream オブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" /> は null なので、</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStream(Windows.Storage.Streams.IRandomAccessStream,System.Int32)">
      <summary>指定されたバッファー サイズを使用して、Windows ランタイム のランダム アクセス ストリームを Windows ストア アプリ用 .NET のマネージ ストリームに変換します。</summary>
      <returns>変換されたストリーム。</returns>
      <param name="windowsRuntimeStream">変換する Windows ランタイム IRandomAccessStream オブジェクト。</param>
      <param name="bufferSize">バッファーのサイズ (バイト数)。この値を負にすることはできませんが、0 (ゼロ) に設定してバッファリングを無効にすることはできます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> が負の値です。</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForRead(Windows.Storage.Streams.IInputStream)">
      <summary>Windows ランタイム の入力ストリームを Windows ストア アプリ用 .NET のマネージ ストリームに変換します。</summary>
      <returns>変換されたストリーム。</returns>
      <param name="windowsRuntimeStream">変換する Windows ランタイム IInputStream オブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" /> は null なので、</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForRead(Windows.Storage.Streams.IInputStream,System.Int32)">
      <summary>指定されたバッファー サイズを使用して、Windows ランタイム の入力ストリームを Windows ストア アプリ用 .NET のマネージ ストリームに変換します。</summary>
      <returns>変換されたストリーム。</returns>
      <param name="windowsRuntimeStream">変換する Windows ランタイム IInputStream オブジェクト。</param>
      <param name="bufferSize">バッファーのサイズ (バイト数)。この値を負にすることはできませんが、0 (ゼロ) に設定してバッファリングを無効にすることはできます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> が負の値です。</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForWrite(Windows.Storage.Streams.IOutputStream)">
      <summary>Windows ランタイム の出力ストリームを Windows ストア アプリ用 .NET のマネージ ストリームに変換します。</summary>
      <returns>変換されたストリーム。</returns>
      <param name="windowsRuntimeStream">変換する Windows ランタイム IOutputStream オブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" /> は null なので、</exception>
    </member>
    <member name="M:System.IO.WindowsRuntimeStreamExtensions.AsStreamForWrite(Windows.Storage.Streams.IOutputStream,System.Int32)">
      <summary>指定されたバッファー サイズを使用して、Windows ランタイム の出力ストリームを Windows ストア アプリ用 .NET のマネージ ストリームに変換します。</summary>
      <returns>変換されたストリーム。</returns>
      <param name="windowsRuntimeStream">変換する Windows ランタイム IOutputStream オブジェクト。</param>
      <param name="bufferSize">バッファーのサイズ (バイト数)。この値を負にすることはできませんが、0 (ゼロ) に設定してバッファリングを無効にすることはできます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="windowsRuntimeStream" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> が負の値です。</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo">
      <summary>Windows ランタイム の非同期操作および操作と互換性のある、マネージ タスクの表現を構築するためのファクトリ メソッドを提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run``1(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{``0}})">
      <summary>結果を返す開始されたタスクを生成する関数を使用して、Windows ランタイム の非同期操作を作成して開始します。タスクはキャンセルをサポートできます。</summary>
      <returns>
        <paramref name="taskProvider" /> によって生成されるタスクを表す起動済みの Windows.Foundation.IAsyncOperation&lt;TResult&gt; インスタンス。</returns>
      <param name="taskProvider">タスクを作成および開始する関数を表すデリゲート。開始されたタスクは、返された Windows ランタイム 非同期操作によって表されます。関数には、タスクで監視して、キャンセル要求が通知されるようにするためのキャンセル トークンが渡されます。タスクがキャンセルをサポートしていない場合は、このトークンを無視できます。</param>
      <typeparam name="TResult">結果を返す型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="taskProvider" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" /> は未開始のタスクを返します。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task})">
      <summary>開始されたタスクを生成する関数を使用して、Windows ランタイム の非同期アクションを作成して開始します。タスクはキャンセルをサポートできます。</summary>
      <returns>
        <paramref name="taskProvider" /> によって生成されるタスクを表す起動済みの Windows.Foundation.IAsyncAction インスタンス。</returns>
      <param name="taskProvider">タスクを作成および開始する関数を表すデリゲート。開始されたタスクは、返された Windows ランタイム 非同期アクションによって表されます。関数には、タスクで監視して、キャンセル要求が通知されるようにするためのキャンセル トークンが渡されます。タスクがキャンセルをサポートしていない場合は、このトークンを無視できます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="taskProvider" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" /> は未開始のタスクを返します。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run``2(System.Func{System.Threading.CancellationToken,System.IProgress{``1},System.Threading.Tasks.Task{``0}})">
      <summary>結果を返す開始されたタスクを生成する関数を使用して、進行状況の更新を含む Windows ランタイム の非同期操作を作成して開始します。タスクはキャンセルと進行状況のリポートをサポートできます。</summary>
      <returns>
        <paramref name="taskProvider" /> によって生成されるタスクを表す起動済みの Windows.Foundation.IAsyncOperationWithProgress&lt;TResult,TProgress&gt; インスタンス。</returns>
      <param name="taskProvider">タスクを作成および開始する関数を表すデリゲート。開始されたタスクは、返された Windows ランタイム 非同期アクションによって表されます。関数には、タスクで監視して、キャンセル要求が通知されるようにするためのキャンセル トークンと、進行状況をレポートするためのインターフェイスが渡されます。タスクが進行状況のレポートまたはキャンセルをサポートしていない場合は、これらの引数のいずれかまたは両方を無視できます。</param>
      <typeparam name="TResult">結果を返す型。</typeparam>
      <typeparam name="TProgress">進行状況の通知で使用される種類。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="taskProvider" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" /> は未開始のタスクを返します。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.AsyncInfo.Run``1(System.Func{System.Threading.CancellationToken,System.IProgress{``0},System.Threading.Tasks.Task})">
      <summary>開始されたタスクを生成する関数を使用して、進行状況の更新を含む Windows ランタイム の非同期アクションを作成して開始します。タスクはキャンセルと進行状況のリポートをサポートできます。</summary>
      <returns>
        <paramref name="taskProvider" /> によって生成されるタスクを表す起動済みの Windows.Foundation.IAsyncActionWithProgress&lt;TProgress&gt; インスタンス。</returns>
      <param name="taskProvider">タスクを作成および開始する関数を表すデリゲート。開始されたタスクは、返された Windows ランタイム 非同期アクションによって表されます。関数には、タスクで監視して、キャンセル要求が通知されるようにするためのキャンセル トークンと、進行状況をレポートするためのインターフェイスが渡されます。タスクが進行状況のレポートまたはキャンセルをサポートしていない場合は、これらの引数のいずれかまたは両方を無視できます。</param>
      <typeparam name="TProgress">進行状況の通知で使用される種類。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="taskProvider" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="taskProvider" /> は未開始のタスクを返します。</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBuffer">
      <summary>Windows ランタイム IBuffer インターフェイス (Windows.Storage.Streams.IBuffer) の実装、および全ての追加要求インターフェイスを提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBuffer.Create(System.Byte[],System.Int32,System.Int32,System.Int32)">
      <summary>バイト配列からコピーした、指定した範囲のバイトを含む Windows.Storage.Streams.IBuffer インターフェイスを返します。指定された容量が、コピーされたバイト数より大きい場合、バッファーの残りはゼロで埋められます。</summary>
      <returns>指定されたバイトの範囲を含む Windows.Storage.Streams.IBuffer インターフェイス。<paramref name="capacity" /> が <paramref name="length" /> より大きい場合、バッファーの残りは 0 で埋められます。</returns>
      <param name="data">コピー元となるバイト配列。</param>
      <param name="offset">コピーを開始位置を示す <paramref name="data" /> 内のオフセット。</param>
      <param name="length">コピーするバイト数。</param>
      <param name="capacity">バッファーが保持できるバイトの最大数。これが <paramref name="length" /> より大きい場合、バッファーの残りのバイトは 0 (ゼロ) に初期化されます。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" />、<paramref name="offset" />、または <paramref name="length" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> は null なので、</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> を起点とすると、<paramref name="data" /> に <paramref name="length" /> 要素は格納されません。または<paramref name="offset" /> を起点とすると、<paramref name="data" /> に <paramref name="capacity" /> 要素は格納されません。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBuffer.Create(System.Int32)">
      <summary>指定した最大の容量の空の Windows.Storage.Streams.IBuffer インターフェイスを返します。</summary>
      <returns>指定した容量および 0 (ゼロ) に等しい Length プロパティを持つ Windows.Storage.Streams.IBuffer インターフェイス。</returns>
      <param name="capacity">バッファーに保持できる最大バイト数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> が 0 未満です。</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions">
      <summary>Windows ランタイム バッファー (Windows.Storage.Streams.IBuffer インターフェイス) 上の動作のための拡張メソッドを提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsBuffer(System.Byte[])">
      <summary>指定したバイト配列を表す Windows.Storage.Streams.IBuffer インターフェイスを返します。</summary>
      <returns>指定されたバイト配列を表す Windows.Storage.Streams.IBuffer インターフェイス。</returns>
      <param name="source">表現する配列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsBuffer(System.Byte[],System.Int32,System.Int32)">
      <summary>指定されたバイト配列のバイトの範囲を表す Windows.Storage.Streams.IBuffer インターフェイスを返します。</summary>
      <returns>
        <paramref name="source" /> 内の指定されたバイト数を表す IBuffer インターフェイス。</returns>
      <param name="source">IBuffer によって表されるバイトの範囲を含む配列。</param>
      <param name="offset">範囲の開始 <paramref name="source" /> のオフセット。</param>
      <param name="length">IBuffer によって表される範囲の長さ。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> または <paramref name="length" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">配列は IBuffer のバッキング ストアとして実行するには十分ではありません。つまり、<paramref name="offset" /> で始まる、<paramref name="source" /> のバイト数が <paramref name="length" /> 未満です。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsBuffer(System.Byte[],System.Int32,System.Int32,System.Int32)">
      <summary>指定されたバイト配列のバイトの範囲を表す Windows.Storage.Streams.IBuffer インターフェイスを返します。必要に応じて、IBuffer の Length のプロパティを容量未満の値に設定します。</summary>
      <returns>
        <paramref name="source" /> 内の指定されたバイト範囲を表し、Length プロパティ値が指定された IBuffer インターフェイス。</returns>
      <param name="source">IBuffer によって表されるバイトの範囲を含む配列。</param>
      <param name="offset">範囲の開始 <paramref name="source" /> のオフセット。</param>
      <param name="length">IBuffer の Length プロパティの値。</param>
      <param name="capacity">IBuffer によって表される範囲のサイズ。IBuffer の Capacity プロパティはこの値に設定されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" />、<paramref name="length" />、または <paramref name="capacity" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="length" /> が <paramref name="capacity" /> より大きくなっています。または配列は IBufferのバッキング ストアとして実行するには十分ではありません。つまり、<paramref name="offset" /> で始まる、<paramref name="source" /> のバイト数が <paramref name="length" /> または <paramref name="capacity" /> 未満です。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.AsStream(Windows.Storage.Streams.IBuffer)">
      <summary>指定した Windows.Storage.Streams.IBuffer インターフェイスが表現する同じメモリを表すストリームを返します。</summary>
      <returns>指定した Windows.Storage.Streams.IBuffer インターフェイスが表現する同じメモリを表すストリーム。</returns>
      <param name="source">ストリームとして表す IBuffer。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(System.Byte[],System.Int32,Windows.Storage.Streams.IBuffer,System.UInt32,System.Int32)">
      <summary>コピー元の配列内の開始インデックス、コピー先のバッファー内の開始インデックス、およびコピーするバイト数を指定して、コピー元の配列からコピー先のバッファー (Windows.Storage.Streams.IBuffer) にバイトをコピーします。メソッドは、変換先バッファーの Length プロパティを更新しません。</summary>
      <param name="source">データのコピー元となる配列。</param>
      <param name="sourceIndex">データのコピーの開始位置を示す <paramref name="source" /> のインデックス。</param>
      <param name="destination">データのコピー先となるバッファー。</param>
      <param name="destinationIndex">データのコピーの開始位置を示す <paramref name="destination" /> のインデックス。</param>
      <param name="count">コピーするバイト数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="destination" /> が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" />、<paramref name="sourceIndex" />、または <paramref name="destinationIndex" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceIndex" /> が <paramref name="source" /> の長さ以上です。または<paramref name="sourceIndex" /> を開始位置とする <paramref name="source" /> 内のバイト数が <paramref name="count" /> 未満です。または<paramref name="destinationIndex" /> から始まる <paramref name="count" /> バイトのコピーを実行すると、<paramref name="destination" /> の容量を超えます。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(System.Byte[],Windows.Storage.Streams.IBuffer)">
      <summary>コピー元の配列のすべてのバイトを、コピー先のバッファー (Windows.Storage.Streams.IBuffer) にコピーします。コピー操作は、両方のオフセット 0 (ゼロ) から開始されます。メソッドは、変換先バッファーの長さを更新しません。</summary>
      <param name="source">データのコピー元となる配列。</param>
      <param name="destination">データのコピー先となるバッファー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="destination" /> が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="source" /> のサイズが <paramref name="destination" /> の容量を超えています。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,System.Byte[])">
      <summary>コピー元のバッファー (Windows.Storage.Streams.IBuffer) のすべてのバイトを、コピー先の配列にコピーします。コピー操作は、両方のオフセット 0 (ゼロ) から開始されます。</summary>
      <param name="source">データのコピー元となるバッファー。</param>
      <param name="destination">データのコピー先の配列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="destination" /> が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="source" /> のサイズが <paramref name="destination" /> のサイズを超えています。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,System.UInt32,System.Byte[],System.Int32,System.Int32)">
      <summary>コピー元のバッファー内の開始インデックス、コピー先の配列内の開始インデックス、およびコピーするバイト数を指定して、コピー元のバッファー (Windows.Storage.Streams.IBuffer) からコピー先の配列にバイトをコピーします。</summary>
      <param name="source">データのコピー元となるバッファー。</param>
      <param name="sourceIndex">データのコピーの開始位置を示す <paramref name="source" /> のインデックス。</param>
      <param name="destination">データのコピー先の配列。</param>
      <param name="destinationIndex">データのコピーの開始位置を示す <paramref name="destination" /> のインデックス。</param>
      <param name="count">コピーするバイト数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="destination" /> が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" />、<paramref name="sourceIndex" />、または <paramref name="destinationIndex" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceIndex" /> が <paramref name="source" /> の容量と同じか超えています。または<paramref name="destinationIndex" /> が <paramref name="destination" /> の長さ以上です。または<paramref name="sourceIndex" /> を開始位置とする <paramref name="source" /> 内のバイト数が <paramref name="count" /> 未満です。または<paramref name="destinationIndex" /> から始まる <paramref name="count" /> バイトのコピーを実行すると、<paramref name="destination" /> のサイズを超えます。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,System.UInt32,Windows.Storage.Streams.IBuffer,System.UInt32,System.UInt32)">
      <summary>コピー元の開始インデックス、コピー先の開始インデックス、およびコピーするバイト数を指定して、コピー元のバッファー (Windows.Storage.Streams.IBuffer) からコピー先のバッファーにバイトをコピーします。</summary>
      <param name="source">データのコピー元となるバッファー。</param>
      <param name="sourceIndex">データのコピーの開始位置を示す <paramref name="source" /> のインデックス。</param>
      <param name="destination">データのコピー先となるバッファー。</param>
      <param name="destinationIndex">データのコピーの開始位置を示す <paramref name="destination" /> のインデックス。</param>
      <param name="count">コピーするバイト数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="destination" /> が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" />、<paramref name="sourceIndex" />、または <paramref name="destinationIndex" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceIndex" /> が <paramref name="source" /> の容量と同じか超えています。または<paramref name="destinationIndex" /> が <paramref name="destination" /> の容量と同じか超えています。または<paramref name="sourceIndex" /> を開始位置とする <paramref name="source" /> 内のバイト数が <paramref name="count" /> 未満です。または<paramref name="destinationIndex" /> から始まる <paramref name="count" /> バイトのコピーを実行すると、<paramref name="destination" /> の容量を超えます。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.CopyTo(Windows.Storage.Streams.IBuffer,Windows.Storage.Streams.IBuffer)">
      <summary>コピー元のバッファー (Windows.Storage.Streams.IBuffer) のすべてのバイトを、コピー先のバッファーにコピーします。コピー操作は、両方のオフセット 0 (ゼロ) から開始されます。</summary>
      <param name="source">コピー元のバッファー。</param>
      <param name="destination">コピー先のバッファー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="destination" /> が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="source" /> のサイズが <paramref name="destination" /> の容量を超えています。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.GetByte(Windows.Storage.Streams.IBuffer,System.UInt32)">
      <summary>指定した Windows.Storage.Streams.IBuffer インターフェイス内の指定されたオフセット位置のバイトを返します。</summary>
      <returns>指定したオフセットにあるバイト。</returns>
      <param name="source">バイトの派生元であるバッファー。</param>
      <param name="byteOffset">バイトのオフセット。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteOffset" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="byteOffset" /> が <paramref name="source" /> の容量と同じか超えています。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.GetWindowsRuntimeBuffer(System.IO.MemoryStream)">
      <summary>指定したメモリ ストリームと同じメモリを表す Windows.Storage.Streams.IBuffer インターフェイスを返します。</summary>
      <returns>指定したメモリ ストリームをサポートする同じメモリによってサポートされる Windows.Storage.Streams.IBuffer インターフェイス。</returns>
      <param name="underlyingStream">IBuffer のパッキング メモリを提供するストリーム。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.GetWindowsRuntimeBuffer(System.IO.MemoryStream,System.Int32,System.Int32)">
      <summary>指定したメモリ ストリームが示すメモリ内の領域を表す Windows.Storage.Streams.IBuffer インターフェイスを返します。</summary>
      <returns>指定したメモリ ストリームをサポートするメモリ内の領域によってサポートされる Windows.Storage.Streams.IBuffer インターフェイス。</returns>
      <param name="underlyingStream">IBuffer とメモリを共有するストリーム。</param>
      <param name="positionInStream">
        <paramref name="underlyingStream" /> 内の共有メモリ領域の位置。</param>
      <param name="length">共有メモリ領域の最大サイズ。<paramref name="positionInStream" /> で開始される <paramref name="underlyingStream" /> のバイト数が <paramref name="length" /> よりも小さい場合、返される IBuffer は使用できるバイトだけを表します。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="underlyingStream" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="positionInStream" /> または <paramref name="length" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="positionInStream" /> は <paramref name="source" /> の末尾を超えています。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="underlyingStream" /> は、基になるメモリ バッファーを公開することはできません。</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="underlyingStream" /> が閉じられています。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.IsSameData(Windows.Storage.Streams.IBuffer,Windows.Storage.Streams.IBuffer)">
      <summary>2 つのバッファー (Windows.Storage.Streams.IBuffer オブジェクト) が同じ基になるメモリ領域を表すかどうかを示す値を返します。</summary>
      <returns>2 個のバッファーによって表されるメモリ領域の開始点が同じである場合は true。それ以外の場合は false。</returns>
      <param name="buffer">最初のバッファー。</param>
      <param name="otherBuffer">2 番目のバッファー。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.ToArray(Windows.Storage.Streams.IBuffer)">
      <summary>指定したバッファー (Windows.Storage.Streams.IBuffer) の内容から作成された新しい配列を返します。配列のサイズは IBuffer の Length プロパティの値です。</summary>
      <returns>オフセット 0 (ゼロ) で始まり、IBuffer の Length プロパティの値と等しいバイト数を含む、指定された IBuffer のバイトを含むバイト配列。</returns>
      <param name="source">コンテンツが新しい配列を追加するバッファー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeBufferExtensions.ToArray(Windows.Storage.Streams.IBuffer,System.UInt32,System.Int32)">
      <summary>指定されたバッファー (Windows.Storage.Streams.IBuffer) のコンテンツから作成された、指定されたオフセットで始まり、指定したバイト数を含む新しい配列を返します。</summary>
      <returns>指定されたバイトの範囲を含むバイト配列。</returns>
      <param name="source">コンテンツが新しい配列を追加するバッファー。</param>
      <param name="sourceIndex">データのコピーの開始位置を示す <paramref name="source" /> のインデックス。</param>
      <param name="count">コピーするバイト数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> または <paramref name="sourceIndex" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceIndex" /> が <paramref name="source" /> の容量と同じか超えています。または<paramref name="sourceIndex" /> を開始位置とする <paramref name="source" /> 内のバイト数が <paramref name="count" /> 未満です。</exception>
    </member>
    <member name="T:Windows.Foundation.Point">
      <summary>2 次元空間での x 座標と y 座標のペアを表します。特定のプロパティの使用について、論理点を表すこともできます。</summary>
    </member>
    <member name="M:Windows.Foundation.Point.#ctor(System.Double,System.Double)">
      <summary>指定した値を含む <see cref="T:Windows.Foundation.Point" /> 構造体を初期化します。</summary>
      <param name="x">
        <see cref="T:Windows.Foundation.Point" /> 構造体の x 座標の値。</param>
      <param name="y">
        <see cref="T:Windows.Foundation.Point" /> 構造体の y 座標の値。</param>
    </member>
    <member name="M:Windows.Foundation.Point.Equals(System.Object)">
      <summary>指定したオブジェクトが <see cref="T:Windows.Foundation.Point" /> であり、この <see cref="T:Windows.Foundation.Point" /> と同じ値が含まれているかどうかを判断します。</summary>
      <returns>
        <paramref name="obj" /> が <see cref="T:Windows.Foundation.Point" /> であり、この <see cref="T:Windows.Foundation.Point" /> と同じ <see cref="P:Windows.Foundation.Point.X" /> 値と <see cref="P:Windows.Foundation.Point.Y" /> 値を含んでいる場合は true。それ以外の場合は false。</returns>
      <param name="o">比較対象のオブジェクト。</param>
    </member>
    <member name="M:Windows.Foundation.Point.Equals(Windows.Foundation.Point)">
      <summary>2 つの <see cref="T:Windows.Foundation.Point" /> 構造体を比較し、等しいかどうかを確認します。</summary>
      <returns>両方の <see cref="T:Windows.Foundation.Point" /> 構造体が同じ <see cref="P:Windows.Foundation.Point.X" /> 値と <see cref="P:Windows.Foundation.Point.Y" /> 値を含む場合は true。それ以外の場合は false。</returns>
      <param name="value">このインスタンスと比較するポイント。</param>
    </member>
    <member name="M:Windows.Foundation.Point.GetHashCode">
      <summary>この <see cref="T:Windows.Foundation.Point" /> のハッシュ コードを返します。</summary>
      <returns>この <see cref="T:Windows.Foundation.Point" /> 構造体のハッシュ コード。</returns>
    </member>
    <member name="M:Windows.Foundation.Point.op_Equality(Windows.Foundation.Point,Windows.Foundation.Point)">
      <summary>2 つの <see cref="T:Windows.Foundation.Point" /> 構造体を比較し、等しいかどうかを確認します。</summary>
      <returns>
        <paramref name="point1" /> および <paramref name="point2" /> の <see cref="P:Windows.Foundation.Point.X" /> と <see cref="P:Windows.Foundation.Point.Y" /> の値が等しい場合は true。それ以外の場合は false。</returns>
      <param name="point1">最初に比較する <see cref="T:Windows.Foundation.Point" /> 構造体。</param>
      <param name="point2">2 番目に比較する <see cref="T:Windows.Foundation.Point" /> 構造体。</param>
    </member>
    <member name="M:Windows.Foundation.Point.op_Inequality(Windows.Foundation.Point,Windows.Foundation.Point)">
      <summary>2 つの <see cref="T:Windows.Foundation.Point" /> 構造体を比較し、等しくないかどうかを確認します</summary>
      <returns>
        <paramref name="point1" /> および <paramref name="point2" /> の <see cref="P:Windows.Foundation.Point.X" /> または <see cref="P:Windows.Foundation.Point.Y" /> の値が異なる場合は true。<paramref name="point1" /> および <paramref name="point2" /> の <see cref="P:Windows.Foundation.Point.X" /> および <see cref="P:Windows.Foundation.Point.Y" /> の値が同じ場合は false。</returns>
      <param name="point1">最初に比較するポイント。</param>
      <param name="point2">2 番目に比較するポイント。</param>
    </member>
    <member name="M:Windows.Foundation.Point.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>このメンバーの説明については、<see cref="M:System.IFormattable.ToString(System.String,System.IFormatProvider)" /> を参照してください。</summary>
      <returns>指定した書式で現在のインスタンスの値が格納されている文字列。</returns>
      <param name="format">使用する書式を指定する文字列。またはIFormattable 実装の型に対して定義されている既定の書式を使用する場合は、null。</param>
      <param name="provider">値の書式付けに使用する IFormatProvider。またはオペレーティング システムの現在のロケール設定から数値の書式情報を取得する場合は null。</param>
    </member>
    <member name="M:Windows.Foundation.Point.ToString">
      <summary>この <see cref="T:Windows.Foundation.Point" /> の <see cref="T:System.String" /> 表現を作成します。</summary>
      <returns>この <see cref="T:Windows.Foundation.Point" /> 構造体の <see cref="P:Windows.Foundation.Point.X" /> 値および <see cref="P:Windows.Foundation.Point.Y" /> 値を格納する <see cref="T:System.String" />。</returns>
    </member>
    <member name="M:Windows.Foundation.Point.ToString(System.IFormatProvider)">
      <summary>この <see cref="T:Windows.Foundation.Point" /> の <see cref="T:System.String" /> 表現を作成します。</summary>
      <returns>この <see cref="T:Windows.Foundation.Point" /> 構造体の <see cref="P:Windows.Foundation.Point.X" /> 値および <see cref="P:Windows.Foundation.Point.Y" /> 値を格納する <see cref="T:System.String" />。</returns>
      <param name="provider">カルチャ固有の書式設定情報。</param>
    </member>
    <member name="P:Windows.Foundation.Point.X">
      <summary>この <see cref="T:Windows.Foundation.Point" /> 構造体の <see cref="P:Windows.Foundation.Point.X" /> 座標値を取得または設定します。</summary>
      <returns>この <see cref="T:Windows.Foundation.Point" /> 構造体の <see cref="P:Windows.Foundation.Point.X" /> 座標値。既定値は 0 です。</returns>
    </member>
    <member name="P:Windows.Foundation.Point.Y">
      <summary>この <see cref="T:Windows.Foundation.Point" /> の <see cref="P:Windows.Foundation.Point.Y" /> 座標値を取得または設定します。</summary>
      <returns>この <see cref="T:Windows.Foundation.Point" /> 構造体の <see cref="P:Windows.Foundation.Point.Y" /> 座標値。既定値は 0 です。</returns>
    </member>
    <member name="T:Windows.Foundation.Rect">
      <summary>四角形の幅、高さ、および原点を示します。</summary>
    </member>
    <member name="M:Windows.Foundation.Rect.#ctor(System.Double,System.Double,System.Double,System.Double)">
      <summary>指定した x 座標、y 座標、幅、および高さを持つ <see cref="T:Windows.Foundation.Rect" /> 構造体を初期化します。</summary>
      <param name="x">四角形の左上隅の x 座標。</param>
      <param name="y">四角形の左上隅の y 座標。</param>
      <param name="width">四角形の幅。</param>
      <param name="height">四角形の高さ。</param>
      <exception cref="T:System.ArgumentException">width または height が 0 未満です。</exception>
    </member>
    <member name="M:Windows.Foundation.Rect.#ctor(Windows.Foundation.Point,Windows.Foundation.Point)">
      <summary>指定した 2 つの点をちょうど格納できる大きさを持つ、<see cref="T:Windows.Foundation.Rect" /> 構造体を初期化します。</summary>
      <param name="point1">新しい四角形に格納する必要がある最初の点。</param>
      <param name="point2">新しい四角形に格納する必要がある 2 番目の点。</param>
    </member>
    <member name="M:Windows.Foundation.Rect.#ctor(Windows.Foundation.Point,Windows.Foundation.Size)">
      <summary>原点とサイズに基づいて <see cref="T:Windows.Foundation.Rect" /> 構造体を初期化します。</summary>
      <param name="location">新しい <see cref="T:Windows.Foundation.Rect" /> の原点。</param>
      <param name="size">新しい <see cref="T:Windows.Foundation.Rect" /> のサイズ。</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Bottom">
      <summary>四角形の底辺の y 軸の値を取得します。</summary>
      <returns>四角形の底辺の y 軸の値。四角形が空の場合、値は <see cref="F:System.Double.NegativeInfinity" /> になります。</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.Contains(Windows.Foundation.Point)">
      <summary>指定した点が、<see cref="T:Windows.Foundation.Rect" /> で表される四角形に含まれているかどうかを示します。</summary>
      <returns>指定した点が、<see cref="T:Windows.Foundation.Rect" /> で表される四角形に含まれている場合は true。それ以外の場合は false。</returns>
      <param name="point">確認対象の点。</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Empty">
      <summary>位置や領域を持たない四角形を表す特殊な値を取得します。</summary>
      <returns>
        <see cref="F:System.Double.PositiveInfinity" /> の <see cref="P:Windows.Foundation.Rect.X" /> および <see cref="P:Windows.Foundation.Rect.Y" /> 値を持ち、<see cref="F:System.Double.NegativeInfinity" /> の <see cref="P:Windows.Foundation.Rect.Width" /> および <see cref="P:Windows.Foundation.Rect.Height" /> 値を持つ空の四角形。</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.Equals(System.Object)">
      <summary>指定したオブジェクトが、現在の <see cref="T:Windows.Foundation.Rect" /> と等しいかどうかを示します。</summary>
      <returns>
        <paramref name="o" /> が <see cref="T:Windows.Foundation.Rect" /> で、現在の <see cref="T:Windows.Foundation.Rect" /> と同じ x、y、width、height の各値を持つ場合は true。それ以外の場合は false。</returns>
      <param name="o">現在の四角形と比較するオブジェクト。</param>
    </member>
    <member name="M:Windows.Foundation.Rect.Equals(Windows.Foundation.Rect)">
      <summary>指定した <see cref="T:Windows.Foundation.Rect" /> が、現在の <see cref="T:Windows.Foundation.Rect" /> と等しいかどうかを示します。</summary>
      <returns>指定した <see cref="T:Windows.Foundation.Rect" /> が、現在の <see cref="T:Windows.Foundation.Rect" /> と同じ x、y、width、height の各プロパティ値を持つ場合は true。それ以外の場合は false。</returns>
      <param name="value">現在の四角形と比較する対象の四角形。</param>
    </member>
    <member name="M:Windows.Foundation.Rect.GetHashCode">
      <summary>
        <see cref="T:Windows.Foundation.Rect" /> のハッシュ コードを作成します。</summary>
      <returns>現在の <see cref="T:Windows.Foundation.Rect" /> 構造体のハッシュ コード。</returns>
    </member>
    <member name="P:Windows.Foundation.Rect.Height">
      <summary>四角形の高さを取得または設定します。</summary>
      <returns>四角形の高さを表す値。既定値は 0 です。</returns>
      <exception cref="T:System.ArgumentException">0 未満の値が指定されています。</exception>
    </member>
    <member name="M:Windows.Foundation.Rect.Intersect(Windows.Foundation.Rect)">
      <summary>現在の <see cref="T:Windows.Foundation.Rect" /> で表される四角形と指定した <see cref="T:Windows.Foundation.Rect" /> で表される四角形の交差部分を求め、結果を現在の <see cref="T:Windows.Foundation.Rect" /> として保存します。</summary>
      <param name="rect">現在の四角形と交差する四角形。</param>
    </member>
    <member name="P:Windows.Foundation.Rect.IsEmpty">
      <summary>四角形が <see cref="P:Windows.Foundation.Rect.Empty" /> の四角形かどうかを示す値を取得します。</summary>
      <returns>四角形が <see cref="P:Windows.Foundation.Rect.Empty" /> の四角形の場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:Windows.Foundation.Rect.Left">
      <summary>四角形の左辺の x 軸の値を取得します。</summary>
      <returns>四角形の左辺の x 軸の値。</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.op_Equality(Windows.Foundation.Rect,Windows.Foundation.Rect)">
      <summary>2 つの <see cref="T:Windows.Foundation.Rect" /> 構造体を比較し、等しいかどうかを確認します。</summary>
      <returns>
        <see cref="T:Windows.Foundation.Rect" /> 構造体が、同じ x、y、width、height の各プロパティ値を持つ場合は true。それ以外の場合は false。</returns>
      <param name="rect1">最初に比較する四角形。</param>
      <param name="rect2">2 番目に比較する四角形。</param>
    </member>
    <member name="M:Windows.Foundation.Rect.op_Inequality(Windows.Foundation.Rect,Windows.Foundation.Rect)">
      <summary>2 つの <see cref="T:Windows.Foundation.Rect" /> 構造体を比較し、等しくないかどうかを確認します。</summary>
      <returns>
        <see cref="T:Windows.Foundation.Rect" /> 構造体の、x、y、width、height の各プロパティ値が異なる場合は true。それ以外の場合は false。</returns>
      <param name="rect1">最初に比較する四角形。</param>
      <param name="rect2">2 番目に比較する四角形。</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Right">
      <summary>四角形の右辺の x 軸の値を取得します。</summary>
      <returns>四角形の右辺の x 軸の値。</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>このメンバーの説明については、<see cref="M:System.IFormattable.ToString(System.String,System.IFormatProvider)" /> を参照してください。</summary>
      <returns>指定した書式で現在のインスタンスの値が格納されている文字列。</returns>
      <param name="format">使用する書式を指定する文字列。またはIFormattable 実装の型に対して定義されている既定の書式を使用する場合は、null。</param>
      <param name="provider">値の書式付けに使用する IFormatProvider。またはオペレーティング システムの現在のロケール設定から数値の書式情報を取得する場合は null。</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Top">
      <summary>四角形の上辺の y 軸の位置を取得します。</summary>
      <returns>四角形の上辺の y 軸の位置。</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.ToString">
      <summary>
        <see cref="T:Windows.Foundation.Rect" /> 構造体の文字列表現を返します。</summary>
      <returns>現在の <see cref="T:Windows.Foundation.Rect" /> 構造体の文字列表現。文字列は、"<see cref="P:Windows.Foundation.Rect.X" />,<see cref="P:Windows.Foundation.Rect.Y" />,<see cref="P:Windows.Foundation.Rect.Width" />,<see cref="P:Windows.Foundation.Rect.Height" />" の形式になります。</returns>
    </member>
    <member name="M:Windows.Foundation.Rect.ToString(System.IFormatProvider)">
      <summary>指定した書式プロバイダーを使用して、四角形の文字列形式を返します。</summary>
      <returns>指定した書式プロバイダーにより決定される現在の四角形の文字列形式を返します。</returns>
      <param name="provider">カルチャ固有の書式設定情報。</param>
    </member>
    <member name="M:Windows.Foundation.Rect.Union(Windows.Foundation.Point)">
      <summary>指定した点をちょうど格納できる大きさになるように、現在の <see cref="T:Windows.Foundation.Rect" /> で表される四角形を拡大します。</summary>
      <param name="point">格納対象の点。</param>
    </member>
    <member name="M:Windows.Foundation.Rect.Union(Windows.Foundation.Rect)">
      <summary>指定した四角形をちょうど格納できる大きさになるように、現在の<see cref="T:Windows.Foundation.Rect" /> で表される四角形を拡大します。</summary>
      <param name="rect">格納する四角形。</param>
    </member>
    <member name="P:Windows.Foundation.Rect.Width">
      <summary>四角形の幅を取得または設定します。</summary>
      <returns>四角形の幅を表す値 (ピクセル単位)。既定値は 0 です。</returns>
      <exception cref="T:System.ArgumentException">0 未満の値が指定されています。</exception>
    </member>
    <member name="P:Windows.Foundation.Rect.X">
      <summary>四角形の左辺の x 軸の値を取得または設定します。</summary>
      <returns>四角形の左辺の x 軸の値。この値は、座標空間内のピクセルと解釈されます。</returns>
    </member>
    <member name="P:Windows.Foundation.Rect.Y">
      <summary>四角形の上辺の y 軸の値を取得または設定します。</summary>
      <returns>四角形の上辺の y 軸の値。この値は、座標空間内のピクセルと解釈されます。</returns>
    </member>
    <member name="T:Windows.Foundation.Size">
      <summary>オブジェクトの幅と高さを記述します。</summary>
    </member>
    <member name="M:Windows.Foundation.Size.#ctor(System.Double,System.Double)">
      <summary>
        <see cref="T:Windows.Foundation.Size" /> 構造体の新しいインスタンスを初期化し、<paramref name="width" /> および <paramref name="height" /> の初期値を割り当てます。</summary>
      <param name="width">
        <see cref="T:Windows.Foundation.Size" /> のインスタンスの初期幅。</param>
      <param name="height">
        <see cref="T:Windows.Foundation.Size" /> のインスタンスの初期高さ。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="width" /> または <paramref name="height" /> が 0 未満です。</exception>
    </member>
    <member name="P:Windows.Foundation.Size.Empty">
      <summary>静的で空の <see cref="T:Windows.Foundation.Size" /> を表す値を取得します。</summary>
      <returns>
        <see cref="T:Windows.Foundation.Size" /> の空のインスタンス。</returns>
    </member>
    <member name="M:Windows.Foundation.Size.Equals(System.Object)">
      <summary>オブジェクトが <see cref="T:Windows.Foundation.Size" /> のインスタンスと等価であるかどうかを比較します。</summary>
      <returns>サイズが等しい場合は true。それ以外の場合は false。</returns>
      <param name="o">比較対象の <see cref="T:System.Object" />。</param>
    </member>
    <member name="M:Windows.Foundation.Size.Equals(Windows.Foundation.Size)">
      <summary>値が <see cref="T:Windows.Foundation.Size" /> のインスタンスと等しいかどうかを比較します。</summary>
      <returns>
        <see cref="T:Windows.Foundation.Size" /> のインスタンスが等しい場合は true。それ以外の場合は false。</returns>
      <param name="value">
        <see cref="T:Windows.Foundation.Size" /> の現在のインスタンスと比較するサイズ。</param>
    </member>
    <member name="M:Windows.Foundation.Size.GetHashCode">
      <summary>
        <see cref="T:Windows.Foundation.Size" /> のこのインスタンスのハッシュ コードを取得します。</summary>
      <returns>
        <see cref="T:Windows.Foundation.Size" /> のインスタンスのハッシュ コード。</returns>
    </member>
    <member name="P:Windows.Foundation.Size.Height">
      <summary>
        <see cref="T:Windows.Foundation.Size" /> のこのインスタンスの高さを取得または設定します。</summary>
      <returns>
        <see cref="T:Windows.Foundation.Size" /> のこのインスタンスの <see cref="P:Windows.Foundation.Size.Height" /> (ピクセル単位)。既定値は 0 です。この値を負にすることはできません。</returns>
      <exception cref="T:System.ArgumentException">0 未満の値が指定されています。</exception>
    </member>
    <member name="P:Windows.Foundation.Size.IsEmpty">
      <summary>
        <see cref="T:Windows.Foundation.Size" /> のこのインスタンスが <see cref="P:Windows.Foundation.Size.Empty" /> かどうかを示す値を取得します。</summary>
      <returns>Size のこのインスタンスが <see cref="P:Windows.Foundation.Size.Empty" /> の場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:Windows.Foundation.Size.op_Equality(Windows.Foundation.Size,Windows.Foundation.Size)">
      <summary>
        <see cref="T:Windows.Foundation.Size" /> の 2 つのインスタンスを比較し、等しいかどうかを確認します。</summary>
      <returns>
        <see cref="T:Windows.Foundation.Size" /> の 2 つのインスタンスが等しい場合は true。それ以外の場合は false。</returns>
      <param name="size1">比較対象の <see cref="T:Windows.Foundation.Size" /> の第 1 インスタンス。</param>
      <param name="size2">比較対象の <see cref="T:Windows.Foundation.Size" /> の第 2 インスタンス。</param>
    </member>
    <member name="M:Windows.Foundation.Size.op_Inequality(Windows.Foundation.Size,Windows.Foundation.Size)">
      <summary>
        <see cref="T:Windows.Foundation.Size" /> の 2 つのインスタンスを比較し、等しくないかどうかを判断します。</summary>
      <returns>
        <see cref="T:Windows.Foundation.Size" /> のインスタンスが等しくない場合は true。それ以外の場合は false。</returns>
      <param name="size1">比較対象の <see cref="T:Windows.Foundation.Size" /> の第 1 インスタンス。</param>
      <param name="size2">比較対象の <see cref="T:Windows.Foundation.Size" /> の第 2 インスタンス。</param>
    </member>
    <member name="M:Windows.Foundation.Size.ToString">
      <summary>この <see cref="T:Windows.Foundation.Size" /> の文字列表現を返します。</summary>
      <returns>この <see cref="T:Windows.Foundation.Size" /> の文字列表現。</returns>
    </member>
    <member name="P:Windows.Foundation.Size.Width">
      <summary>
        <see cref="T:Windows.Foundation.Size" /> のこのインスタンスの幅を取得または設定します。</summary>
      <returns>
        <see cref="T:Windows.Foundation.Size" /> のこのインスタンスの <see cref="P:Windows.Foundation.Size.Width" /> (ピクセル単位)。既定値は 0 です。この値を負にすることはできません。</returns>
      <exception cref="T:System.ArgumentException">0 未満の値が指定されています。</exception>
    </member>
    <member name="T:Windows.UI.Color">
      <summary>アルファ、赤、緑、および青の各チャネルによって色を表します。</summary>
    </member>
    <member name="P:Windows.UI.Color.A">
      <summary>色の sRGB アルファ チャネルの値を取得または設定します。</summary>
      <returns>色の sRGB アルファ チャネル値 (0 ～ 255)。</returns>
    </member>
    <member name="P:Windows.UI.Color.B">
      <summary>色の sRGB 青チャネルの値を取得または設定します。</summary>
      <returns>sRGB 青チャネル値 (0 ～ 255)。</returns>
    </member>
    <member name="M:Windows.UI.Color.Equals(System.Object)">
      <summary>指定されたオブジェクトが <see cref="T:Windows.UI.Color" /> 構造体かどうか、また、現在の色と等価かどうかをテストします。</summary>
      <returns>指定したオブジェクトが <see cref="T:Windows.UI.Color" /> 構造体であり、現在の <see cref="T:Windows.UI.Color" /> 構造体と等しい場合は true。それ以外の場合は false。</returns>
      <param name="o">現在の <see cref="T:Windows.UI.Color" /> 構造体と比較するオブジェクト。</param>
    </member>
    <member name="M:Windows.UI.Color.Equals(Windows.UI.Color)">
      <summary>指定した <see cref="T:Windows.UI.Color" /> 構造体が、現在の色と等しいかどうかをテストします。</summary>
      <returns>指定した <see cref="T:Windows.UI.Color" /> 構造体が現在の <see cref="T:Windows.UI.Color" /> 構造体と等しい場合は true。それ以外の場合は false。</returns>
      <param name="color">現在の <see cref="T:Windows.UI.Color" /> 構造体と比較する <see cref="T:Windows.UI.Color" /> 構造体。</param>
    </member>
    <member name="M:Windows.UI.Color.FromArgb(System.Byte,System.Byte,System.Byte,System.Byte)">
      <summary>指定した sRGB アルファ チャネル値およびカラー チャネル値を使用して新しい <see cref="T:Windows.UI.Color" /> 構造体を作成します。</summary>
      <returns>指定した値を格納する <see cref="T:Windows.UI.Color" /> 構造体。</returns>
      <param name="a">新しい色のアルファ チャネル <see cref="P:Windows.UI.Color.A" />。値は 0 ～ 255 の範囲である必要があります。</param>
      <param name="r">新しい色の赤チャネル <see cref="P:Windows.UI.Color.R" />。値は 0 ～ 255 の範囲である必要があります。</param>
      <param name="g">新しい色の緑チャネル <see cref="P:Windows.UI.Color.G" />。値は 0 ～ 255 の範囲である必要があります。</param>
      <param name="b">新しい色の青チャネル <see cref="P:Windows.UI.Color.B" />。値は 0 ～ 255 の範囲である必要があります。</param>
    </member>
    <member name="P:Windows.UI.Color.G">
      <summary>色の sRGB 緑チャネルの値を取得または設定します。</summary>
      <returns>sRGB 緑チャネル値 (0 ～ 255)。</returns>
    </member>
    <member name="M:Windows.UI.Color.GetHashCode">
      <summary>現在の <see cref="T:Windows.UI.Color" /> 構造体のハッシュ コードを取得します。</summary>
      <returns>現在の <see cref="T:Windows.UI.Color" /> 構造体のハッシュ コード。</returns>
    </member>
    <member name="M:Windows.UI.Color.op_Equality(Windows.UI.Color,Windows.UI.Color)">
      <summary>2 つの <see cref="T:Windows.UI.Color" /> 構造体が同じかどうかをテストします。</summary>
      <returns>
        <paramref name="color1" /> と <paramref name="color2" /> が完全に同一である場合は true。それ以外の場合は false。</returns>
      <param name="color1">最初に比較する <see cref="T:Windows.UI.Color" /> 構造体。</param>
      <param name="color2">2 番目に比較する <see cref="T:Windows.UI.Color" /> 構造体。</param>
    </member>
    <member name="M:Windows.UI.Color.op_Inequality(Windows.UI.Color,Windows.UI.Color)">
      <summary>2 つの <see cref="T:Windows.UI.Color" /> 構造体が異なるかどうかをテストします。</summary>
      <returns>
        <paramref name="color1" /> および <paramref name="color2" /> が等しくない場合は true。それ以外の場合は false。</returns>
      <param name="color1">最初に比較する <see cref="T:Windows.UI.Color" /> 構造体。</param>
      <param name="color2">2 番目に比較する <see cref="T:Windows.UI.Color" /> 構造体。</param>
    </member>
    <member name="P:Windows.UI.Color.R">
      <summary>色の sRGB 赤チャネルの値を取得または設定します。</summary>
      <returns>sRGB 赤チャネル値 (0 ～ 255)。</returns>
    </member>
    <member name="M:Windows.UI.Color.System#IFormattable#ToString(System.String,System.IFormatProvider)">
      <summary>このメンバーの説明については、<see cref="M:System.IFormattable.ToString(System.String,System.IFormatProvider)" /> を参照してください。</summary>
      <returns>指定した書式で現在のインスタンスの値が格納されている文字列。</returns>
      <param name="format">使用する書式を指定する文字列。またはIFormattable 実装の型に対して定義されている既定の書式を使用する場合は、null。</param>
      <param name="provider">値の書式付けに使用する IFormatProvider。またはオペレーティング システムの現在のロケール設定から数値の書式情報を取得する場合は null。</param>
    </member>
    <member name="M:Windows.UI.Color.ToString">
      <summary>ARGB チャネルを使用して、16 進表記で色の文字列表現を作成します。</summary>
      <returns>色の文字列表現。</returns>
    </member>
    <member name="M:Windows.UI.Color.ToString(System.IFormatProvider)">
      <summary>ARGB チャネルおよび指定した書式プロバイダーを使用して、色の文字列表現を作成します。</summary>
      <returns>色の文字列表現。</returns>
      <param name="provider">カルチャ固有の書式設定情報。</param>
    </member>
  </members>
</doc>