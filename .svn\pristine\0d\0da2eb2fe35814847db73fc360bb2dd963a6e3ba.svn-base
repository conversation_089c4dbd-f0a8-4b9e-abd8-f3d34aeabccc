// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;

namespace System.Windows.Automation
{
    public class BasePattern
    {
        internal bool cached;

        internal AutomationElement el;


        internal BasePattern(AutomationElement el, bool cached)
        {
            Debug.Assert(el != null);
            this.el = el;
            this.cached = cached;
        }
    }
}