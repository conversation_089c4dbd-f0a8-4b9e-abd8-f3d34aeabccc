﻿using System;
using System.Threading;

namespace OCRTools
{
    public class ServerTime
    {
        public static long OffSet;
        private static long lastOffSet;

        public static bool HasGetNTPDate;

        static ServerTime()
        {
            new Thread(p => { ProcessTimeOffSet(); })
                {Priority = ThreadPriority.Highest, IsBackground = true}.Start();
        }

        public static DateTime DateTime
        {
            get
            {
                if (OffSet != 0)
                    return DateTime.Now.AddTicks(OffSet);
                return DateTime.Now;
            }
        }

        private static void ProcessTimeOffSet()
        {
            while (!CommonString.isExit)
            {
                long off;
                if (CommonString.IsOnLine)
                    off = SNtpClient.Instance.GetNetworkTimeOffset();
                else
                    off = -9999;
                if (off != -9999)
                {
                    //Console.WriteLine(string.Format("OffSet:{0},off:{1}", OffSet, off));
                    if (lastOffSet != 0)
                    {
                        OffSet = (lastOffSet + OffSet + off) / 3;
                        lastOffSet = off;
                    }
                    else
                    {
                        OffSet = off;
                        lastOffSet = off;
                    }

                    if (!HasGetNTPDate) HasGetNTPDate = true;
                    Thread.Sleep(10 * 1000);
                }
                else
                {
                    Thread.Sleep(1 * 1000);
                }
            }
        }

        public static void SetHttpDate(DateTime dtHttp)
        {
            if (HasGetNTPDate || dtHttp.Year < 1900)
                return;
            try
            {
                OffSet = dtHttp.Ticks - DateTime.Now.Ticks;
            }
            catch
            {
            }
        }
    }
}