using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools.WebBroswerEx
{
    /// <summary>
    /// 应用程序Web管理器 - 在程序启动时统一初始化所有Web相关组件
    /// 建议在Main方法或Application.Run之前调用
    /// </summary>
    public static class ApplicationWebManager
    {
        private static bool _webView2Available = false;
        private static bool _isWebView2Checked = false;

        // 运行时状态管理
        private static bool _webView2RuntimeFailed = false; // 标记WebView2在运行时是否失败过
        private static readonly object _runtimeStateLock = new object();

        /// <summary>
        /// WebView2是否可用 - 延迟初始化
        /// </summary>
        public static bool IsWebView2Available
        {
            get
            {
                // 检查运行时失败状态
                lock (_runtimeStateLock)
                {
                    if (_webView2RuntimeFailed) return false;
                }

                // 延迟初始化
                if (!_isWebView2Checked)
                {
                    lock (_runtimeStateLock)
                    {
                        if (!_isWebView2Checked)
                        {
                            _webView2Available = InitializeWebView2InSTAThread();
                            _isWebView2Checked = true;
                        }
                    }
                }
                return _webView2Available;
            }
        }

        /// <summary>
        /// 在STA线程中初始化WebView2
        /// </summary>
        private static bool InitializeWebView2InSTAThread()
        {
            bool environmentCreated = false;
            var staThread = new System.Threading.Thread(() =>
            {
                try
                {
                    // 1. 使用WebView2DynamicManager的现有逻辑
                   var  dynamicManagerResult = WebView2DynamicManager.TryInitializeWebView2();
                    if (!dynamicManagerResult)
                    {
                        return;
                    }

                    // 2. 使用WebView2EnvironmentManager的现有逻辑创建环境
                    try
                    {
                        var environment = WebView2EnvironmentManager.GetOrCreateEnvironmentAsync().GetAwaiter().GetResult();
                        environmentCreated = (environment != null);
                    }
                    catch (Exception envEx)
                    {
                        environmentCreated = false;
                    }
                }
                catch (Exception ex)
                {
                }
            })
            {
                IsBackground = true
            };

            staThread.SetApartmentState(System.Threading.ApartmentState.STA);
            staThread.Start();
            staThread.Join();
            return environmentCreated;
        }

        /// <summary>
        /// 标记WebView2在运行时失败，后续创建SmartWebControl时将直接使用WebBrowser
        /// </summary>
        internal static void MarkWebView2RuntimeFailed()
        {
            lock (_runtimeStateLock)
            {
                if (!_webView2RuntimeFailed)
                {
                    _webView2RuntimeFailed = true;
                    Console.WriteLine("WebView2已被标记为运行时不可用，后续将直接使用WebBrowser");
                }
            }
        }

        /// <summary>
        /// 创建WebView2控件实例（内部方法）
        /// 调用方已确保WebView2可用，直接创建控件
        /// </summary>
        internal static async Task<Control> CreateWebView2ControlAsync()
        {
            try
            {
                var control = WebView2DynamicManager.CreateWebView2Control();
                if (control != null)
                {
                    control.Dock = DockStyle.Fill;
                }
                return control;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 初始化CoreWebView2（内部方法）
        /// 调用方已确保WebView2可用，直接初始化CoreWebView2
        /// </summary>
        internal static async Task<object> InitializeCoreWebView2Async(Control webView2Control)
        {
            if (webView2Control == null) return null;

            try
            {
                var webView2Type = webView2Control.GetType();
                var ensureMethod = webView2Type.GetMethod("EnsureCoreWebView2Async");
                if (ensureMethod != null)
                {
                    var task = ensureMethod.Invoke(webView2Control, new object[ensureMethod.GetParameters().Length]);
                    if (task is Task taskObj)
                    {
                        try { await taskObj; } catch { return null; }
                        return webView2Type.GetProperty("CoreWebView2")?.GetValue(webView2Control);
                    }
                }
                return null;
            }
            catch { return null; }
        }

        /// <summary>
        /// 程序退出时清理资源
        /// </summary>
        public static void Cleanup()
        {
            try
            {
                // 清理WebView2环境
                WebView2EnvironmentManager.DisposeEnvironment();
                _webView2Available = false;
            }
            catch
            {
            }
        }
    }
}
