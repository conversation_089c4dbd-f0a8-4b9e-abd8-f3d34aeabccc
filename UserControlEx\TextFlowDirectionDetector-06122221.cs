using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using OCRTools.Common;

namespace OCRTools.UserControlEx
{
    /// <summary>
    /// 文本流方向检测器，负责分析文本块的排列方向。
    /// </summary>
    public class TextFlowDirectionDetector
    {
        private readonly List<TextCellInfo> cells;

        // 自适应分组阈值
        private double groupingThresholdVertical = 10;
        private double groupingThresholdHorizontal = 10;

        // 极端竖排比例阈值
        private double extremeVerticalRatioThreshold = 7.0;

        public TextFlowDirectionDetector(List<TextCellInfo> cells)
        {
            this.cells = cells;
        }

        /// <summary>
        /// 文本方向检测结果
        /// </summary>
        public class TextDirectionResult
        {
            /// <summary>水平方向（左右方向）</summary>
            public TextFlowDirection HorizontalDirection { get; set; } = TextFlowDirection.LeftToRight;

            /// <summary>垂直方向（上下方向）</summary>
            public TextFlowDirection VerticalDirection { get; set; } = TextFlowDirection.TopToBottom;

            /// <summary>水平方向置信度（0-100）</summary>
            public int HorizontalConfidence { get; set; } = 0;

            /// <summary>垂直方向置信度（0-100）</summary>
            public int VerticalConfidence { get; set; } = 0;

            /// <summary>是否为竖排布局（true表示竖排，false表示横排）</summary>
            public bool IsVerticalLayout { get; set; } = false;

            /// <summary>布局方式置信度（0-100）</summary>
            public int LayoutConfidence { get; set; } = 0;

            /// <summary>
            /// 获取主要文本方向（基于布局类型）
            /// </summary>
            /// <returns>主要阅读方向</returns>
            public TextFlowDirection GetPrimaryDirection()
            {
                // 如果水平方向是混合的，返回垂直方向
                if (HorizontalDirection == TextFlowDirection.Mixed)
                    return VerticalDirection;

                // 如果垂直方向是混合的，返回水平方向
                if (VerticalDirection == TextFlowDirection.Mixed)
                    return HorizontalDirection;

                // 根据布局类型判断主要方向
                return IsVerticalLayout ? VerticalDirection : HorizontalDirection;
            }

            /// <summary>
            /// 获取结果的字符串表示
            /// </summary>
            public override string ToString()
            {
                string layoutType = IsVerticalLayout ? "竖排" : "横排";
                return $"布局类型: {layoutType}(置信度:{LayoutConfidence}), 水平方向: {HorizontalDirection}(置信度:{HorizontalConfidence}), 垂直方向: {VerticalDirection}(置信度:{VerticalConfidence})";
            }
        }

        /// <summary>
        /// 检测文本方向（兼容旧版本）
        /// </summary>
        /// <returns>主要文本方向</returns>
        public TextFlowDirection Detect()
        {
            var result = DetectDirections();
            return result.GetPrimaryDirection();
        }

        /// <summary>
        /// 检测文本的水平和垂直方向
        /// </summary>
        /// <returns>包含水平和垂直方向的结果对象</returns>
        public TextDirectionResult DetectDirections()
        {
            var result = new TextDirectionResult();

            if (cells == null || cells.Count < 2) return result; // 默认方向

            var validCells = cells.Where(c => c?.location != null).ToList();
            if (validCells.Count < 2) return result;
            
            // 计算自适应分组阈值
            CalculateAdaptiveThresholds(validCells);
            
            // DEBUG - 打印cells的特征统计
            System.Diagnostics.Debug.WriteLine("\n************ 文本块特征诊断 ************");
            System.Diagnostics.Debug.WriteLine($"文本块总数: {validCells.Count}");
            System.Diagnostics.Debug.WriteLine("------------------------------------");
            
            // 统计高宽比分布
            int extremeVerticalCount = 0;  // 高宽比>自适应阈值的文本块
            int highVerticalCount = 0;     // 高宽比>5的文本块
            int normalVerticalCount = 0;   // 高宽比>1.2的文本块
            
            int extremeHorizontalCount = 0;  // 宽高比>7的文本块
            int highHorizontalCount = 0;     // 宽高比>5的文本块
            int normalHorizontalCount = 0;   // 宽高比>1.2的文本块
            
            double maxHeightWidthRatio = 0;  // 记录最大高宽比
            TextCellInfo maxRatioCell = null; // 记录最大比例的单元格
            
            System.Diagnostics.Debug.WriteLine("\n高宽比分布:");
            foreach (var cell in validCells)
            {
                double hwRatio = cell.location.height / (double)cell.location.width;
                double whRatio = cell.location.width / (double)cell.location.height;
                
                // 记录最大高宽比
                if (hwRatio > maxHeightWidthRatio)
                {
                    maxHeightWidthRatio = hwRatio;
                    maxRatioCell = cell;
                }
                
                string cellInfo = $"文本: '{(cell.words?.Length > 0 ? cell.words?.Substring(0, Math.Min(10, cell.words?.Length ?? 0)) : "无文本")}', " +
                                 $"位置: ({cell.location.left},{cell.location.top}), " +
                                 $"大小: {cell.location.width}x{cell.location.height}, " +
                                 $"高宽比: {hwRatio:F2}";
                
                if (hwRatio > extremeVerticalRatioThreshold)
                {
                    extremeVerticalCount++;
                    System.Diagnostics.Debug.WriteLine($"超高竖排: {cellInfo}");
                }
                else if (hwRatio > 5.0)
                {
                    highVerticalCount++;
                    System.Diagnostics.Debug.WriteLine($"明显竖排: {cellInfo}");
                }
                else if (hwRatio > 1.2)
                {
                    normalVerticalCount++;
                    System.Diagnostics.Debug.WriteLine($"一般竖排: {cellInfo}");
                }
                else if (whRatio > 7.0)
                {
                    extremeHorizontalCount++;
                    System.Diagnostics.Debug.WriteLine($"极端横排: {cellInfo}");
                }
                else if (whRatio > 5.0)
                {
                    highHorizontalCount++;
                    System.Diagnostics.Debug.WriteLine($"明显横排: {cellInfo}");
                }
                else if (whRatio > 1.2)
                {
                    normalHorizontalCount++;
                    System.Diagnostics.Debug.WriteLine($"一般横排: {cellInfo}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"近似方形: {cellInfo}");
                }
            }
            
            System.Diagnostics.Debug.WriteLine("\n文本块形状统计总结:");
            System.Diagnostics.Debug.WriteLine($"超高竖排(>{extremeVerticalRatioThreshold:F1}): {extremeVerticalCount}, 明显竖排(>5): {highVerticalCount}, 一般竖排(>1.2): {normalVerticalCount}");
            System.Diagnostics.Debug.WriteLine($"极端横排(>7): {extremeHorizontalCount}, 明显横排(>5): {highHorizontalCount}, 一般横排(>1.2): {normalHorizontalCount}");
            
            if (maxRatioCell != null)
            {
                System.Diagnostics.Debug.WriteLine($"\n最大高宽比: {maxHeightWidthRatio:F2}, 文本: '{(maxRatioCell.words?.Length > 0 ? maxRatioCell.words : "无文本")}'");
            }
            
            // 统计列分布
            var verticalBlocks = validCells.Where(c => c.location.height / (double)c.location.width > 1.2).ToList();
            var columns = verticalBlocks
                .GroupBy(c => Math.Round(c.location.left / 20) * 20) // 使用更小的分组阈值
                .OrderBy(g => g.Key)
                .ToList();
                
            System.Diagnostics.Debug.WriteLine("\n列分布统计:");
            System.Diagnostics.Debug.WriteLine($"竖直文本块数: {verticalBlocks.Count}, 列数: {columns.Count}");
            
            foreach (var column in columns)
            {
                System.Diagnostics.Debug.WriteLine($"列位置: {column.Key}, 文本块数: {column.Count()}");
                foreach (var cell in column.OrderBy(c => c.location.top))
                {
                    System.Diagnostics.Debug.WriteLine($"  - '{(cell.words?.Length > 0 ? cell.words?.Substring(0, Math.Min(10, cell.words?.Length ?? 0)) : "无文本")}', " +
                                                     $"位置: ({cell.location.left},{cell.location.top}), 大小: {cell.location.width}x{cell.location.height}, " +
                                                     $"高宽比: {cell.location.height/(double)cell.location.width:F2}");
                }
            }
            
            // 分析列的顺序和规律性
            bool isLikelyRightToLeft = false;
            if (columns.Count >= 2)
            {
                double pageLeft = validCells.Min(c => c.location.left);
                double pageRight = validCells.Max(c => c.location.left + c.location.width);
                double pageWidth = pageRight - pageLeft;
                double pageCenter = pageLeft + pageWidth / 2.0;
                
                var firstColumn = columns.First();
                var lastColumn = columns.Last();
                
                double firstColumnCenter = firstColumn.Average(c => c.location.left + c.location.width / 2);
                double lastColumnCenter = lastColumn.Average(c => c.location.left + c.location.width / 2);
                
                System.Diagnostics.Debug.WriteLine("\n列排序分析:");
                System.Diagnostics.Debug.WriteLine($"页面范围: 左={pageLeft:F1}, 右={pageRight:F1}, 中心={pageCenter:F1}");
                System.Diagnostics.Debug.WriteLine($"第一列中心: {firstColumnCenter:F1}, 最后一列中心: {lastColumnCenter:F1}");
                
                // 通用列顺序检测
                isLikelyRightToLeft = firstColumnCenter > pageCenter && lastColumnCenter < pageCenter;
                
                // 其他情况的右到左启发式检测 
                if (!isLikelyRightToLeft)
                {
                    // 检查列是否从右到左排列 - 计算列从右到左的比例
                    double columnsFromRight = 0;
                    double totalColumns = columns.Count;
                    List<double> columnCenters = columns.Select(g => g.Average(c => c.location.left + c.location.width / 2)).ToList();
                    
                    // 检查每一列是否在页面右半部分
                    for (int i = 0; i < columnCenters.Count; i++)
                    {
                        if (columnCenters[i] > pageCenter)
                        {
                            columnsFromRight += 1;
                        }
                    }
                    
                    // 是否有超过50%的列在页面右半部分
                    double rightColumnsRatio = columnsFromRight / totalColumns;
                    
                    // 如果列主要在右侧，优先判定为从右到左
                    if (rightColumnsRatio > 0.5)
                    {
                        isLikelyRightToLeft = true;
                        System.Diagnostics.Debug.WriteLine($"基于列右侧比例({rightColumnsRatio:P2})判断可能为从右到左");
                    }
                }
                
                System.Diagnostics.Debug.WriteLine($"列顺序分析结果: {(isLikelyRightToLeft ? "可能是从右到左" : "可能是从左到右")}");
                
                // 分析列内排列规律性
                System.Diagnostics.Debug.WriteLine("\n列内垂直排列规律性分析:");
                foreach (var column in columns)
                {
                    if (column.Count() < 2) continue;
                    
                    var colBlocks = column.OrderBy(c => c.location.top).ToList();
                    List<double> verticalGaps = new List<double>();
                    
                    for (int i = 0; i < colBlocks.Count - 1; i++)
                    {
                        double gap = colBlocks[i + 1].location.top - (colBlocks[i].location.top + colBlocks[i].location.height);
                        verticalGaps.Add(gap);
                    }
                    
                    double avgGap = verticalGaps.Average();
                    double gapStdDev = verticalGaps.Count > 1 ? CalculateStandardDeviation(verticalGaps) : 0;
                    double gapCV = avgGap != 0 ? gapStdDev / Math.Abs(avgGap) : 0;
                    
                    System.Diagnostics.Debug.WriteLine($"列 {column.Key} 的垂直间距: 平均={avgGap:F2}, 标准差={gapStdDev:F2}, 变异系数={gapCV:F2}");
                    System.Diagnostics.Debug.WriteLine($"规律性评估: {(gapCV < 0.5 ? "高" : (gapCV < 0.8 ? "中" : "低"))}");
                }
                
                // 分析列间距规律性
                if (columns.Count > 2)
                {
                    List<double> columnGaps = new List<double>();
                    List<double> columnCenters = new List<double>();
                    
                    foreach (var column in columns)
                    {
                        double centerX = column.Average(c => c.location.left + c.location.width / 2);
                        columnCenters.Add(centerX);
                    }
                    
                    for (int i = 0; i < columnCenters.Count - 1; i++)
                    {
                        columnGaps.Add(Math.Abs(columnCenters[i+1] - columnCenters[i]));
                    }
                    
                    double avgColumnGap = columnGaps.Average();
                    double columnGapStdDev = columnGaps.Count > 1 ? CalculateStandardDeviation(columnGaps) : 0;
                    double columnGapCV = avgColumnGap > 0 ? columnGapStdDev / avgColumnGap : 0;
                    
                    System.Diagnostics.Debug.WriteLine("\n列间距规律性分析:");
                    System.Diagnostics.Debug.WriteLine($"列间距: 平均={avgColumnGap:F2}, 标准差={columnGapStdDev:F2}, 变异系数={columnGapCV:F2}");
                    System.Diagnostics.Debug.WriteLine($"规律性评估: {(columnGapCV < 0.3 ? "高" : (columnGapCV < 0.5 ? "中" : "低"))}");
                }
            }
            
            // 分析竖排文本的宽度一致性 - 通用版面特征
            var extremeVerticalBlocks = validCells.Where(c => c.location.height / (double)c.location.width > 5.0).ToList();
            if (extremeVerticalBlocks.Count >= 2)
            {
                var widths = extremeVerticalBlocks.Select(c => c.location.width).ToList();
                double avgWidth = widths.Average();
                double widthStdDev = CalculateStandardDeviation(widths);
                double widthCV = widthStdDev / avgWidth;
                
                System.Diagnostics.Debug.WriteLine("\n竖排文本宽度一致性分析:");
                System.Diagnostics.Debug.WriteLine($"宽度: 平均={avgWidth:F2}, 标准差={widthStdDev:F2}, 变异系数={widthCV:F2}");
                System.Diagnostics.Debug.WriteLine($"宽度一致性评估: {(widthCV < 0.15 ? "极高" : (widthCV < 0.25 ? "高" : (widthCV < 0.4 ? "中" : "低")))}");
                
                if (widthCV < 0.25)
                {
                    System.Diagnostics.Debug.WriteLine("宽度高度一致 -> 很可能是规整竖排文本");
                }
            }
            
            System.Diagnostics.Debug.WriteLine("************ 诊断信息结束 ************\n");
            
            // 预先检查是否存在极端竖排布局特征，但不直接决定结果
            // 而是收集特征证据，用于后续权重计算
            bool hasExtremeRatio = false;
            bool isRightToLeft = isLikelyRightToLeft; // 使用分析得到的方向
            
            // 使用增强的极端检测来收集证据
            var extremeEvidence = AnalyzeExtremeLayoutFeatures(validCells, out hasExtremeRatio, out bool detectedRightToLeft);
            
            // 如果AnalyzeExtremeLayoutFeatures检测到RightToLeft，优先使用其结果
            if (detectedRightToLeft)
            {
                isRightToLeft = true;
            }
            
            // 收集方向证据 - 标准分析流程
            var directionEvidence = CollectDirectionEvidence(validCells);
            
            // 将极端布局特征证据合并到标准证据中
            MergeExtremeEvidence(directionEvidence, extremeEvidence, hasExtremeRatio);
            
            // 如果检测到从右到左，增加右到左方向证据
            if (isRightToLeft)
            {
                directionEvidence.RightToLeftCount += 10;
                System.Diagnostics.Debug.WriteLine("检测到从右到左特征，增加RightToLeft证据权重 +10");
            }
            
            // 分析方向证据，确定水平和垂直方向
            AnalyzeDirectionEvidence(directionEvidence, result, validCells);
            
            // 确定文档布局类型
            DetermineLayoutType(result, directionEvidence);

            // 确保最低置信度，避免不确定结果
            EnsureMinimumConfidence(result);

            // 对于复杂文档（有更多文本块），进行额外的置信度优化
            if (validCells.Count >= 10)
            {
                ApplyAdvancedConfidenceOptimization(result, directionEvidence, validCells);
            }

            // 进行最终的一致性检查 - 确保方向和布局保持一致
            AdjustConfidenceBasedOnConsistency(result, directionEvidence);
            
            // 如果检测到了强烈的从右到左特征，调整水平方向
            if (hasExtremeRatio && isRightToLeft && result.IsVerticalLayout)
            {
                result.HorizontalDirection = TextFlowDirection.RightToLeft;
                result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 85);
                System.Diagnostics.Debug.WriteLine("检测到极端竖排特征且从右到左的列排列，调整水平方向为RightToLeft");
            }

            return result;
        }
        
        /// <summary>
        /// 分析极端布局特征，收集证据但不直接决定结果
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <param name="hasExtremeRatio">输出参数：是否存在极端高宽比</param>
        /// <param name="isRightToLeft">输出参数：是否从右到左的列顺序</param>
        /// <returns>极端布局特征证据</returns>
        private DirectionEvidence AnalyzeExtremeLayoutFeatures(List<TextCellInfo> cells, out bool hasExtremeRatio, out bool isRightToLeft)
        {
            hasExtremeRatio = false;
            isRightToLeft = false;
            
            // 初始化极端布局证据
            var extremeEvidence = new DirectionEvidence();
            
            if (cells.Count < 4) return extremeEvidence;
            
            System.Diagnostics.Debug.WriteLine("\n=== 极端布局特征分析 ===");
            
            // 1. 过滤有效文本块（排除太小的元素）
            var validCells = cells.Where(c => 
                c.location.width >= 10 && c.location.height >= 20).ToList();
                
            if (validCells.Count < 4) return extremeEvidence;
            
            // 2. 分析文本块形状
            int extremeVerticalCount = 0;  // 高宽比>7的文本块
            int highVerticalCount = 0;     // 高宽比>5的文本块
            int totalVerticalCount = 0;    // 所有竖排文本(高宽比>1.5)
            
            // 最极端的高宽比
            double maxHeightWidthRatio = 0;
            
            foreach (var cell in validCells)
            {
                double ratio = cell.location.height / (double)cell.location.width;
                // 记录最大高宽比
                maxHeightWidthRatio = Math.Max(maxHeightWidthRatio, ratio);
                
                if (ratio > 7.0) 
                {
                    extremeVerticalCount++;
                    totalVerticalCount++;
                }
                else if (ratio > 5.0) 
                {
                    highVerticalCount++;
                    totalVerticalCount++;
                }
                else if (ratio > 1.5)
                {
                    totalVerticalCount++;
                }
            }
            
            // 3. 计算关键比例
            double extremeRatio = extremeVerticalCount / (double)Math.Max(1, validCells.Count);
            double highVerticalRatio = (extremeVerticalCount + highVerticalCount) / (double)Math.Max(1, validCells.Count);
            
            System.Diagnostics.Debug.WriteLine($"极端竖排文本块: {extremeVerticalCount}, 明显竖排文本块: {highVerticalCount}");
            System.Diagnostics.Debug.WriteLine($"极端竖排比例: {extremeRatio:F2}, 高竖排比例: {highVerticalRatio:F2}");
            System.Diagnostics.Debug.WriteLine($"最大高宽比: {maxHeightWidthRatio:F2}");
            
            // 检查是否存在强烈的竖排特征 - 调整阈值使其更敏感
            // 原始阈值: extremeRatio >= 0.5 && extremeVerticalCount >= 3
            // 更敏感的阈值，针对中文竖排文本的特点
            bool hasHighRatioBlocks = maxHeightWidthRatio > 8.0; // 是否有超高比例文本块
            
            hasExtremeRatio = (extremeRatio >= 0.4 && extremeVerticalCount >= 3) || 
                             (highVerticalRatio >= 0.6 && (extremeVerticalCount + highVerticalCount) >= 4) ||
                             (extremeVerticalCount >= 4) || // 4个以上极端竖排文本
                             (hasHighRatioBlocks && extremeVerticalCount >= 2); // 有超高比例且至少2个极端竖排文本
                             
            System.Diagnostics.Debug.WriteLine($"是否存在极端高宽比特征: {hasExtremeRatio}");
            
            // 4. 收集方向证据 - 为布局判断提供额外信息
            if (hasExtremeRatio)
            {
                // 增加竖排文本计数 - 提高权重增加，使其在计算中更有影响力
                extremeEvidence.VerticalTextCount += extremeVerticalCount * 3 + highVerticalCount * 2;
                System.Diagnostics.Debug.WriteLine($"极端竖排特征权重: +{extremeVerticalCount * 3 + highVerticalCount * 2}");
                
                // 分析竖排文本的列排列
                var verticalBlocks = validCells.Where(c => 
                    c.location.height / (double)c.location.width > 5.0).ToList();
                    
                System.Diagnostics.Debug.WriteLine($"分析竖排文本列排列，有效竖排文本块: {verticalBlocks.Count}");
                
                if (verticalBlocks.Count >= 2) // 降低阈值，让两个极端竖排文本也能检测到
                {
                    // 按X坐标分组形成列 - 使用更小的阈值以提高对列的检测精度
                    var columns = verticalBlocks
                        .GroupBy(c => Math.Round(c.location.left / 20) * 20) // 降低阈值以更好地区分相近的列
                        .OrderBy(g => g.Key)
                        .ToList();
                        
                    System.Diagnostics.Debug.WriteLine($"检测到列数: {columns.Count}");
                        
                    if (columns.Count >= 2)
                    {
                        // 增加竖排列计数 - 提高列的权重
                        int columnWeight = Math.Min(10, columns.Count * 3); // 提高权重
                        extremeEvidence.VerticalAlignedColumns += columnWeight;
                        System.Diagnostics.Debug.WriteLine($"竖排列权重: +{columnWeight}");
                        
                        // 分析列排列方向
                        // 计算页面空间范围
                        double pageLeft = validCells.Min(c => c.location.left);
                        double pageRight = validCells.Max(c => c.location.left + c.location.width);
                        double pageWidth = pageRight - pageLeft;
                        double pageCenter = pageLeft + pageWidth / 2.0;
                        
                        // 计算列的中心点
                        var columnCenters = columns.Select(g => {
                            double colLeft = g.Average(c => c.location.left);
                            double colWidth = g.Average(c => c.location.width);
                            return colLeft + colWidth / 2.0;
                        }).ToList();
                        
                        System.Diagnostics.Debug.WriteLine($"页面中心: {pageCenter}, 列中心点: {string.Join(", ", columnCenters)}");
                        
                        // 如果第一列在页面右侧，最后一列在页面左侧，可能是从右到左的排列
                        if (columns.Count >= 2 && 
                            columnCenters.First() > pageCenter && 
                            columnCenters.Last() < pageCenter)
                        {
                            isRightToLeft = true;
                            extremeEvidence.RightToLeftCount += 8; // 提高从右到左的权重
                            System.Diagnostics.Debug.WriteLine("检测到可能的从右到左列排列，增加RightToLeft权重 +8");
                        }
                        
                        // 检查列内排列 - 竖直排列是否规律
                        int regularColumnCount = 0;
                        foreach (var column in columns)
                        {
                            if (column.Count() < 2) continue;
                            
                            // 分析列内文本块的垂直分布
                            var colBlocks = column.OrderBy(c => c.location.top).ToList();
                            
                            // 计算相邻文本块之间的垂直间距
                            List<double> verticalGaps = new List<double>();
                            for (int i = 0; i < colBlocks.Count - 1; i++)
                            {
                                double gap = colBlocks[i + 1].location.top - 
                                            (colBlocks[i].location.top + colBlocks[i].location.height);
                                if (gap >= -10) // 允许更多重叠
                                {
                                    verticalGaps.Add(gap);
                                }
                            }
                            
                            // 计算间距的一致性
                            if (verticalGaps.Count >= 1) // 只要有一个间隔也计算
                            {
                                double avgGap = verticalGaps.Average();
                                double gapStdDev = verticalGaps.Count > 1 ? 
                                    CalculateStandardDeviation(verticalGaps) : 0;
                                
                                // 如果间距相对一致，增加竖直方向证据
                                if (verticalGaps.Count <= 1 || gapStdDev / Math.Max(1.0, avgGap) < 0.6) // 放宽一致性要求
                                {
                                    regularColumnCount++;
                                    extremeEvidence.TopToBottomCount += 3; // 提高从上到下的权重
                                    System.Diagnostics.Debug.WriteLine($"列 {column.Key} 的竖直排列规律性好，增加TopToBottom权重 +3");
                                }
                            }
                        }
                        
                        // 如果有规律排列的列，增强竖排证据
                        if (regularColumnCount >= 1) // 降低阈值
                        {
                            extremeEvidence.VerticalAlignedColumns += regularColumnCount * 2;
                            System.Diagnostics.Debug.WriteLine($"规律列数: {regularColumnCount}，增加竖排列权重 +{regularColumnCount * 2}");
                        }
                    }
                }
            }
            
            // 针对中文诗文的特殊优化：检查多列高宽比相似的文本块
            if (extremeVerticalCount >= 2)
            {
                // 获取所有极端竖排文本块
                var extremeBlocks = validCells.Where(c => c.location.height / (double)c.location.width > 7.0).ToList();
                
                // 计算高宽比的标准差
                double avgRatio = extremeBlocks.Average(c => c.location.height / (double)c.location.width);
                double ratioStdDev = CalculateStandardDeviation(extremeBlocks.Select(c => c.location.height / (double)c.location.width));
                double ratioCV = ratioStdDev / avgRatio; // 变异系数
                
                System.Diagnostics.Debug.WriteLine($"极端竖排文本块高宽比分析 - 平均值: {avgRatio:F2}, 标准差: {ratioStdDev:F2}, 变异系数: {ratioCV:F2}");
                
                // 如果高宽比非常一致（表明是同类型的文本，如诗句），增强竖排证据
                if (ratioCV < 0.2)
                {
                    extremeEvidence.VerticalTextCount += extremeVerticalCount * 2;
                    System.Diagnostics.Debug.WriteLine($"极端竖排文本块高宽比非常一致，很可能是规整排版，增加竖排权重 +{extremeVerticalCount * 2}");
                    
                    // 如果还没有设置hasExtremeRatio，现在设置
                    if (!hasExtremeRatio && extremeVerticalCount >= 2)
                    {
                        hasExtremeRatio = true;
                        System.Diagnostics.Debug.WriteLine("基于一致的高宽比特征，设置hasExtremeRatio为true");
                    }
                }
            }
            
            System.Diagnostics.Debug.WriteLine($"最终极端布局特征分析结果: hasExtremeRatio={hasExtremeRatio}, isRightToLeft={isRightToLeft}");
            System.Diagnostics.Debug.WriteLine($"竖排文本计数: {extremeEvidence.VerticalTextCount}, 竖排列计数: {extremeEvidence.VerticalAlignedColumns}");
            System.Diagnostics.Debug.WriteLine($"上到下计数: {extremeEvidence.TopToBottomCount}, 右到左计数: {extremeEvidence.RightToLeftCount}");
            System.Diagnostics.Debug.WriteLine("=== 极端布局特征分析结束 ===\n");
            
            return extremeEvidence;
        }
        
        /// <summary>
        /// 将极端布局特征证据合并到标准证据中
        /// </summary>
        /// <param name="standardEvidence">标准方向证据</param>
        /// <param name="extremeEvidence">极端布局特征证据</param>
        /// <param name="hasExtremeRatio">是否存在极端高宽比</param>
        private void MergeExtremeEvidence(DirectionEvidence standardEvidence, DirectionEvidence extremeEvidence, bool hasExtremeRatio)
        {
            System.Diagnostics.Debug.WriteLine("\n=== 合并极端布局特征证据 ===");
            System.Diagnostics.Debug.WriteLine($"合并前标准证据 - 竖排文本: {standardEvidence.VerticalTextCount}, 竖排列: {standardEvidence.VerticalAlignedColumns}");
            System.Diagnostics.Debug.WriteLine($"极端特征证据 - 竖排文本: {extremeEvidence.VerticalTextCount}, 竖排列: {extremeEvidence.VerticalAlignedColumns}");
            
            // 合并各项证据计数
            standardEvidence.VerticalTextCount += extremeEvidence.VerticalTextCount;
            standardEvidence.VerticalAlignedColumns += extremeEvidence.VerticalAlignedColumns;
            standardEvidence.TopToBottomCount += extremeEvidence.TopToBottomCount;
            standardEvidence.RightToLeftCount += extremeEvidence.RightToLeftCount;
            
            // 如果存在极端高宽比，为关键证据项增加额外权重
            if (hasExtremeRatio)
            {
                // 增加更多的额外权重 - 从6增加到10
                int extraWeight = 10;
                standardEvidence.VerticalTextCount += extraWeight;
                
                // 同时增强竖排列证据
                standardEvidence.VerticalAlignedColumns += 5;
                
                // 优先增强从上到下的方向证据
                standardEvidence.TopToBottomCount += 8;
                
                System.Diagnostics.Debug.WriteLine($"检测到极端高宽比，增加额外权重: 竖排文本 +{extraWeight}, 竖排列 +5, 从上到下 +8");
            }
            
            System.Diagnostics.Debug.WriteLine($"合并后标准证据 - 竖排文本: {standardEvidence.VerticalTextCount}, 竖排列: {standardEvidence.VerticalAlignedColumns}");
            System.Diagnostics.Debug.WriteLine($"上到下计数: {standardEvidence.TopToBottomCount}, 右到左计数: {standardEvidence.RightToLeftCount}");
            System.Diagnostics.Debug.WriteLine("=== 合并证据完成 ===\n");
        }

        /// <summary>
        /// 对复杂文档应用高级置信度优化
        /// </summary>
        private void ApplyAdvancedConfidenceOptimization(TextDirectionResult result, DirectionEvidence evidence, List<TextCellInfo> cells)
        {
            // 1. 检查布局和主方向的一致性
            // 检查是否存在强烈的一致性证据
            bool hasStrongConsistency = false;

            // 横排文本与从左到右方向一致性
            if (!result.IsVerticalLayout && result.HorizontalDirection == TextFlowDirection.LeftToRight &&
                evidence.LeftToRightCount > evidence.RightToLeftCount * 2 &&
                evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
            {
                hasStrongConsistency = true;
            }

            // 竖排文本与从上到下方向一致性
            else if (result.IsVerticalLayout && result.VerticalDirection == TextFlowDirection.TopToBottom &&
                    evidence.TopToBottomCount > evidence.BottomToTopCount * 2 &&
                    evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
            {
                hasStrongConsistency = true;
            }

            // 如果有强烈一致性，适当提高置信度
            if (hasStrongConsistency)
            {
                // 同时提高布局和方向置信度
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 5);

                if (!result.IsVerticalLayout)
                {
                    result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 5);
                }
                else
                {
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + 5);
                }
            }

            // 2. 一致性特征分析
            // 计算各种证据的互相支持程度
            int consistencyScore = 0;

            // 文本形状与对齐特征一致性
            if ((evidence.HorizontalTextCount > evidence.VerticalTextCount &&
                 evidence.LeftAlignedCount > evidence.TopAlignedCount) ||
                (evidence.VerticalTextCount > evidence.HorizontalTextCount &&
                 evidence.TopAlignedCount > evidence.LeftAlignedCount))
            {
                consistencyScore += 2;
            }

            // 边界变异与方向一致性
            if ((evidence.LeftEdgeVariance < evidence.RightEdgeVariance &&
                 result.HorizontalDirection == TextFlowDirection.LeftToRight) ||
                (evidence.RightEdgeVariance < evidence.LeftEdgeVariance &&
                 result.HorizontalDirection == TextFlowDirection.RightToLeft) ||
                (evidence.TopEdgeVariance < evidence.BottomEdgeVariance &&
                 result.VerticalDirection == TextFlowDirection.TopToBottom) ||
                (evidence.BottomEdgeVariance < evidence.TopEdgeVariance &&
                 result.VerticalDirection == TextFlowDirection.BottomToTop))
            {
                consistencyScore += 2;
            }

            // 段落布局与布局类型一致性
            if ((evidence.ParagraphCount >= 3 && !result.IsVerticalLayout) ||
                (evidence.IsSequentialParagraphs && evidence.LeftAlignmentRatio > 0.7 && !result.IsVerticalLayout))
            {
                consistencyScore += 2;
            }

            // 应用一致性评分
            if (consistencyScore >= 4)
            {
                // 高一致性得分提高整体置信度
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyScore / 2);
                result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + consistencyScore / 2);
                result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + consistencyScore / 2);
            }
        }

        /// <summary>
        /// 确保结果具有合理的置信度，避免不确定的输出
        /// </summary>
        private void EnsureMinimumConfidence(TextDirectionResult result)
        {
            // 基于相对性的置信度调整

            // 1. 默认方向置信度阈值 - 使用相对阈值而非绝对值
            int baseThreshold = 50; // 最基础的置信度阈值

            // 2. 根据文本复杂度动态调整阈值
            // 如果两个方向的置信度差异很小，说明文本布局复杂或模糊，应该降低阈值
            if (Math.Abs(result.HorizontalConfidence - result.VerticalConfidence) < 15)
            {
                baseThreshold = 45; // 降低阈值
            }

            // 3. 水平方向的置信度调整
            if (result.HorizontalConfidence < baseThreshold)
            {
                // 根据垂直方向的置信度动态决定水平方向的默认置信度
                // 如果垂直方向的置信度很高，我们对水平方向的默认值更有信心
                int defaultHorizontalConfidence =
                    result.VerticalConfidence > 75 ? 65 : 60;

                result.HorizontalDirection = TextFlowDirection.LeftToRight; // 最常见的默认方向
                result.HorizontalConfidence = defaultHorizontalConfidence;
            }

            // 4. 垂直方向的置信度调整
            if (result.VerticalConfidence < baseThreshold)
            {
                // 根据水平方向的置信度动态决定垂直方向的默认置信度
                int defaultVerticalConfidence =
                    result.HorizontalConfidence > 75 ? 70 : 65;

                result.VerticalDirection = TextFlowDirection.TopToBottom; // 最常见的默认方向
                result.VerticalConfidence = defaultVerticalConfidence;
            }

            // 5. 布局类型置信度调整 - 根据主方向置信度动态决定
            if (result.LayoutConfidence < baseThreshold)
            {
                // 根据当前布局类型选择参考置信度
                int referenceConfidence = result.IsVerticalLayout ?
                    result.VerticalConfidence : result.HorizontalConfidence;

                // 如果参考置信度高，保持当前布局类型但提高置信度
                if (referenceConfidence > 70)
                {
                    result.LayoutConfidence = Math.Max(referenceConfidence - 10, 60);
                }
                // 否则采用更保守的默认值：横排布局（更常见）
                else
                {
                    result.IsVerticalLayout = false;
                    result.LayoutConfidence = 60;
                }
            }
        }

        /// <summary>
        /// 确定文档的布局类型（横排或竖排）
        /// </summary>
        /// <remarks>
        /// 这个方法是布局类型判断的核心算法，使用多维度特征的累加得分系统来判断文档是横排还是竖排布局。
        /// 该方法考虑多种布局证据，包括文本形状、行列结构、对齐特征等，适用于各种语言的文档。
        /// </remarks>
        /// <param name="result">输出的文本方向结果，将设置布局类型</param>
        /// <param name="evidence">收集的方向证据</param>
        private void DetermineLayoutType(TextDirectionResult result, DirectionEvidence evidence)
        {
            // 使用得分累加系统确定布局类型
            // 通过多维度特征分析，使系统适用于各种不同的文档布局
            int horizontalLayoutScore = 0; // 横排布局得分
            int verticalLayoutScore = 0;   // 竖排布局得分

            // 开始详细的布局分析记录
            System.Diagnostics.Debug.WriteLine("\n============ 布局类型分析开始 ============");

            // ====================== 布局类型特征分析 ======================

            // 1. 文本块形状特征 - 最直观和通用的特征
            // 对任何语言都适用，无需特定语言知识
            // 1.1 高宽比特征 - 根据文本块的形状判断其可能的排版方向

            // 计算总的形状特征得分
            int totalShapeScore = 0;

            // 记录形状得分情况
            System.Diagnostics.Debug.WriteLine("\n--- 1. 形状特征得分 ---");
            System.Diagnostics.Debug.WriteLine($"竖向文本数: {evidence.VerticalTextCount}, 横向文本数: {evidence.HorizontalTextCount}");

            // 检查是否存在极端竖排文本特征
            bool hasExtremeVerticalFeature = false;
            bool hasExtremeHorizontalFeature = false;

            // 检测极端比例文本块占比
            double totalTextCount = evidence.VerticalTextCount + evidence.HorizontalTextCount;
            double verticalRatio = totalTextCount > 0 ? evidence.VerticalTextCount / totalTextCount : 0;
            double horizontalRatio = totalTextCount > 0 ? evidence.HorizontalTextCount / totalTextCount : 0;

            System.Diagnostics.Debug.WriteLine($"竖向文本占比: {verticalRatio:P2}, 横向文本占比: {horizontalRatio:P2}");
            
            // 检查是否有足够的文本来判断
            if (totalTextCount < 3)
            {
                System.Diagnostics.Debug.WriteLine("文本数量不足，难以可靠判断形状特征");
                // 使用其他证据继续分析
            }
            else
            {
                // 1. 分析显著优势情况 - 一种形状完全占优
                if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
                {
                    // 竖向文本明显占优 - 强烈暗示竖排布局
                    int score = 8;  // 强力证据

                    // 增强对极端竖排情况的检测
                    if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 2.5)
                    {
                        // 竖向文本极度占优 - 之前是3倍，现在降低到2.5倍
                        score += 6; // 增加得分
                        hasExtremeVerticalFeature = true;
                        System.Diagnostics.Debug.WriteLine($"竖向文本远远多于横向(2.5倍以上) => 竖排得分 +{score}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"竖向文本远多于横向(1.5倍以上) => 竖排得分 +{score}");
                    }

                    verticalLayoutScore += score;
                    totalShapeScore += score;
                }
                else if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.25)
                {
                    // 竖向文本明显较多 - 从1.4倍降低到1.25倍
                    int score = 6;  // 中等证据
                    verticalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"竖向文本明显多于横向(1.25倍以上) => 竖排得分 +{score}");
                }
                else if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.1)
                {
                    // 竖向文本略多 - 从1.2倍降低到1.1倍
                    int score = 4;  // 中弱证据
                    verticalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"竖向文本略多于横向(1.1倍以上) => 竖排得分 +{score}");
                }
                else if (evidence.VerticalTextCount > evidence.HorizontalTextCount)
                {
                    // 竖向文本稍多 - 弱竖排证据，但仍有意义
                    int score = 3;  // 弱证据，增加得分
                    verticalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"竖向文本稍多于横向 => 竖排得分 +{score}");
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
                {
                    // 横向文本明显占优 - 强烈暗示横排布局
                    int score = 8;  // 强力证据

                    // 增强对极端横排情况的检测
                    if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 3)
                    {
                        // 横向文本极度占优
                        score += 4;
                        hasExtremeHorizontalFeature = true;
                        System.Diagnostics.Debug.WriteLine($"横向文本远远多于竖向(3倍以上) => 横排得分 +{score}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"横向文本远多于竖向(1.5倍以上) => 横排得分 +{score}");
                    }

                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.4)
                {
                    // 横向文本明显较多 - 中等强度的横排证据
                    int score = 6;  // 中等证据
                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"横向文本明显多于竖向(1.4倍以上) => 横排得分 +{score}");
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.2)
                {
                    // 横向文本略多 - 中弱强度的横排证据
                    int score = 4;  // 中弱证据
                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"横向文本略多于竖向(1.2倍以上) => 横排得分 +{score}");
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount)
                {
                    // 横向文本稍多 - 弱横排证据，但仍有意义
                    int score = 2;  // 弱证据
                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"横向文本稍多于竖向 => 横排得分 +{score}");
                }
                // 2. 当文本数量接近相等时，引入额外判断因素
                else if (Math.Abs(evidence.VerticalTextCount - evidence.HorizontalTextCount) <= 2)
                {
                    // 当形状特征没有明显优势时，考虑其他证据
                    System.Diagnostics.Debug.WriteLine($"横向与竖向文本数量接近，形状特征不明显");

                    // 检查列与行的数量，通常能更好地反映整体布局
                    if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows)
                    {
                        int score = 4; // 增加得分
                        verticalLayoutScore += score;
                        totalShapeScore += score;
                        System.Diagnostics.Debug.WriteLine($"垂直列数({evidence.VerticalAlignedColumns})大于水平行数({evidence.HorizontalAlignedRows}) => 竖排得分 +{score}");
                    }
                    else if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns)
                    {
                        int score = 3;
                        horizontalLayoutScore += score;
                        totalShapeScore += score;
                        System.Diagnostics.Debug.WriteLine($"水平行数({evidence.HorizontalAlignedRows})大于垂直列数({evidence.VerticalAlignedColumns}) => 横排得分 +{score}");
                    }
                }
            }

            // 检查极端形状特征的占比影响
            if (verticalRatio > 0.7 && evidence.VerticalTextCount >= 3)
            {
                // 如果竖向文本占比超过70%，且至少有3个，这是很强的竖排证据
                // 原来是80%，现在降低到70%
                int bonusScore = 8; // 增加得分
                verticalLayoutScore += bonusScore;
                totalShapeScore += bonusScore;
                System.Diagnostics.Debug.WriteLine($"竖向文本占比超过70%，强烈的竖排证据 => 竖排得分 +{bonusScore}");
            }
            else if (horizontalRatio > 0.8 && evidence.HorizontalTextCount >= 3)
            {
                // 如果横向文本占比超过80%，且至少有3个，这是很强的横排证据
                int bonusScore = 6;
                horizontalLayoutScore += bonusScore;
                totalShapeScore += bonusScore;
                System.Diagnostics.Debug.WriteLine($"横向文本占比超过80%，强烈的横排证据 => 横排得分 +{bonusScore}");
            }

            // 形状特征总结
            if (totalShapeScore > 0)
            {
                double verticalRatioScore = verticalLayoutScore / (double)totalShapeScore;
                System.Diagnostics.Debug.WriteLine($"形状特征总结: 总分={totalShapeScore}, 竖排得分比例={verticalRatioScore:P2}");

                if (verticalRatioScore > 0.6)
                {
                    System.Diagnostics.Debug.WriteLine("形状特征总体倾向: 竖排");
                }
                else if (verticalRatioScore < 0.4)
                {
                    System.Diagnostics.Debug.WriteLine("形状特征总体倾向: 横排");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("形状特征总体倾向: 不确定");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("形状特征分析: 无明显倾向");
            }

            // 2. 方向置信度比较 - 通过已确定的方向置信度差异判断布局
            // 已确定的阅读方向置信度差异可以作为布局判断的辅助证据
            int confidenceDiff = Math.Abs(result.HorizontalConfidence - result.VerticalConfidence);
            if (confidenceDiff >= 20) // 方向置信度差异非常明显
            {
                // 方向置信度差异很大，证据强
                if (result.HorizontalConfidence > result.VerticalConfidence)
                {
                    // 水平方向置信度明显高 - 横排证据
                    // 例：阅读方向分析中水平方向特征非常强
                    horizontalLayoutScore += 6;
                }
                else
                {
                    // 垂直方向置信度明显高 - 竖排证据
                    // 例：阅读方向分析中垂直方向特征非常强
                    verticalLayoutScore += 6;
                }
            }
            else if (confidenceDiff >= 10) // 方向置信度有一定差异
            {
                // 方向置信度差异中等
                if (result.HorizontalConfidence > result.VerticalConfidence)
                {
                    // 水平方向置信度稍高 - 弱横排证据
                    horizontalLayoutScore += 3;
                }
                else
                {
                    // 垂直方向置信度稍高 - 弱竖排证据
                    verticalLayoutScore += 3;
                }
            }

            // 3. 行列结构比较 - 文档的整体排列结构
            // 3.1 行数与列数比较 - 分析文本的整体排列模式
            if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows * 1.8)
            {
                // 列数远多于行数 - 强竖排证据，降低阈值从2.0到1.8
                // 例：传统竖排中文，文本块按列排列
                verticalLayoutScore += 8;  // 增加得分
                System.Diagnostics.Debug.WriteLine($"列数({evidence.VerticalAlignedColumns})远多于行数({evidence.HorizontalAlignedRows})，强竖排证据 => 竖排得分 +8");
            }
            else if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows * 1.3)
            {
                // 列数明显多于行数 - 中等竖排证据，降低阈值从1.5到1.3
                verticalLayoutScore += 6;  // 增加得分
                System.Diagnostics.Debug.WriteLine($"列数明显多于行数(1.3倍) => 竖排得分 +6");
            }
            else if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows)
            {
                // 列数略多于行数 - 弱竖排证据
                verticalLayoutScore += 3;  // 增加得分
                System.Diagnostics.Debug.WriteLine($"列数略多于行数 => 竖排得分 +3");
            }
            else if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns * 2.0)
            {
                // 行数远多于列数 - 强横排证据
                // 例：普通书籍文档，文本块按行排列
                horizontalLayoutScore += 7;  // 行数远大于列数，强力横排证据
                System.Diagnostics.Debug.WriteLine($"行数远多于列数(2倍) => 横排得分 +7");
            }
            else if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns * 1.5)
            {
                // 行数明显多于列数 - 中等横排证据
                horizontalLayoutScore += 5;  // 行数明显多于列数
                System.Diagnostics.Debug.WriteLine($"行数明显多于列数(1.5倍) => 横排得分 +5");
            }
            else if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns)
            {
                // 行数略多于列数 - 弱横排证据
                horizontalLayoutScore += 2;  // 行数略多于列数
                System.Diagnostics.Debug.WriteLine($"行数略多于列数 => 横排得分 +2");
            }

            // 检查列数和行数的绝对值
            if (evidence.VerticalAlignedColumns >= 3)
            {
                // 至少有3列，这是很强的竖排证据
                int columnBonus = Math.Min(10, evidence.VerticalAlignedColumns * 2);
                verticalLayoutScore += columnBonus;
                System.Diagnostics.Debug.WriteLine($"至少有3列排列，强竖排证据 => 竖排得分 +{columnBonus}");
            }
            else if (evidence.VerticalAlignedColumns == 2)
            {
                // 有2列，中等竖排证据
                verticalLayoutScore += 5;
                System.Diagnostics.Debug.WriteLine($"有2列排列，中等竖排证据 => 竖排得分 +5");
            }

            // 4. 对齐特征 - 分析文本块的对齐模式
            // 4.1 左/右对齐 vs 上/下对齐的比较
            if (evidence.LeftAlignedCount > evidence.TopAlignedCount * 2.0)
            {
                // 左对齐远多于顶部对齐 - 强横排证据
                // 例：典型的左对齐段落排版
                horizontalLayoutScore += 5;  // 左对齐远多于顶对齐，横排证据
                System.Diagnostics.Debug.WriteLine($"左对齐({evidence.LeftAlignedCount})远多于顶对齐({evidence.TopAlignedCount})，横排证据 => 横排得分 +5");
            }
            else if (evidence.LeftAlignedCount > evidence.TopAlignedCount * 1.5)
            {
                // 左对齐明显多于顶部对齐 - 中等横排证据
                horizontalLayoutScore += 3;
                System.Diagnostics.Debug.WriteLine($"左对齐明显多于顶对齐(1.5倍) => 横排得分 +3");
            }
            else if (evidence.TopAlignedCount > evidence.LeftAlignedCount * 2.0)
            {
                // 顶部对齐远多于左对齐 - 强竖排证据
                // 例：竖排文本的顶部对齐排版
                verticalLayoutScore += 5;  // 顶对齐远多于左对齐，竖排证据
                System.Diagnostics.Debug.WriteLine($"顶对齐({evidence.TopAlignedCount})远多于左对齐({evidence.LeftAlignedCount})，竖排证据 => 竖排得分 +5");
            }
            else if (evidence.TopAlignedCount > evidence.LeftAlignedCount * 1.5)
            {
                // 顶部对齐明显多于左对齐 - 中等竖排证据
                verticalLayoutScore += 3;
                System.Diagnostics.Debug.WriteLine($"顶对齐明显多于左对齐(1.5倍) => 竖排得分 +3");
            }

            // 5. 边界变异特征 - 文本边界的整齐程度
            // 阅读起始边界通常更加整齐（变异小）
            if (evidence.LeftEdgeVariance < evidence.TopEdgeVariance * 0.5)
            {
                // 左边界比顶部边界更整齐 - 横排证据
                // 例：左对齐的横排文本，左边界一致而顶部边界不一致
                horizontalLayoutScore += 3;  // 左边界比顶边界更整齐，横排证据
                System.Diagnostics.Debug.WriteLine($"左边界比顶边界更整齐，横排证据 => 横排得分 +3");
            }
            else if (evidence.TopEdgeVariance < evidence.LeftEdgeVariance * 0.5)
            {
                // 顶部边界比左边界更整齐 - 竖排证据
                // 例：顶部对齐的竖排文本，顶部边界一致而左边界不一致
                verticalLayoutScore += 3;  // 顶边界比左边界更整齐，竖排证据
                System.Diagnostics.Debug.WriteLine($"顶边界比左边界更整齐，竖排证据 => 竖排得分 +3");
            }

            // 9. 段落特征 - 段落结构分析
            if (evidence.ParagraphCount >= 3)
            {
                // 多段落文本通常是横排的
                // 例：常见的文章结构，多个段落按横排布局
                horizontalLayoutScore += 4;
                System.Diagnostics.Debug.WriteLine($"检测到多段落结构({evidence.ParagraphCount}个)，横排证据 => 横排得分 +4");
            }

            // 增强对极端竖排布局的识别
            if (hasExtremeVerticalFeature && evidence.VerticalAlignedColumns >= 2)
            {
                // 如果有极端竖排特征且至少有2列，增加竖排得分
                int extraScore = 10;  // 提高权重，使其更有影响力
                verticalLayoutScore += extraScore;
                System.Diagnostics.Debug.WriteLine($"检测到极端竖排特征且有多列排列 => 额外竖排得分 +{extraScore}");
            }
            
            // 增强对极端横排布局的识别
            if (hasExtremeHorizontalFeature && evidence.HorizontalAlignedRows >= 2)
            {
                // 如果有极端横排特征且至少有2行，增加横排得分
                int extraScore = 8;  // 提高权重，使其更有影响力
                horizontalLayoutScore += extraScore;
                System.Diagnostics.Debug.WriteLine($"检测到极端横排特征且有多行排列 => 额外横排得分 +{extraScore}");
            }

            // ====================== 布局类型判断 ======================

            // 综合评分确定布局类型 - 分数高的布局类型胜出
            bool isVerticalLayout = verticalLayoutScore > horizontalLayoutScore;

            // 计算置信度 - 基于得分差异比例
            int totalScore = Math.Max(1, horizontalLayoutScore + verticalLayoutScore);
            int winningScore = isVerticalLayout ? verticalLayoutScore : horizontalLayoutScore;
            int opposingScore = isVerticalLayout ? horizontalLayoutScore : verticalLayoutScore;

            // 计算置信度 - 分数差异越大，置信度越高
            int layoutConfidence = CalculateLayoutConfidence(winningScore, opposingScore, totalScore);

            // 应用布局决策
            result.IsVerticalLayout = isVerticalLayout;
            result.LayoutConfidence = layoutConfidence;

            System.Diagnostics.Debug.WriteLine($"\n最终布局得分 - 竖排: {verticalLayoutScore}, 横排: {horizontalLayoutScore}");
            System.Diagnostics.Debug.WriteLine($"判定结果: {(isVerticalLayout ? "竖排" : "横排")}, 置信度: {layoutConfidence}");

            // 特殊布局处理 - 处理特殊布局场景
            ApplySpecialLayoutTypeRules(evidence, result);

            // 增强置信度 - 基于特征一致性进一步调整置信度
            AdjustConfidenceBasedOnConsistency(result, evidence);
            
            System.Diagnostics.Debug.WriteLine($"最终布局结果(特殊规则后): {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
        }

        /// <summary>
        /// 根据布局类型和方向的一致性调整置信度
        /// </summary>
        /// <param name="result">方向检测结果</param>
        /// <param name="evidence">方向证据</param>
        private void AdjustConfidenceBasedOnConsistency(TextDirectionResult result, DirectionEvidence evidence)
        {
            System.Diagnostics.Debug.WriteLine("\n=== 一致性调整开始 ===");
            System.Diagnostics.Debug.WriteLine($"调整前 - 布局: {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
            System.Diagnostics.Debug.WriteLine($"水平方向: {result.HorizontalDirection}, 置信度: {result.HorizontalConfidence}");
            System.Diagnostics.Debug.WriteLine($"垂直方向: {result.VerticalDirection}, 置信度: {result.VerticalConfidence}");
            
            // 检查布局类型和方向是否一致
            bool isConsistent = false;

            // 增强一致性检查，考虑更多情况
            if (!result.IsVerticalLayout)
            {
                // 横排布局一致性检查
                if (result.HorizontalDirection == TextFlowDirection.LeftToRight ||
                    result.HorizontalDirection == TextFlowDirection.RightToLeft)
                {
                    isConsistent = true;

                    // 计算一致性得分 - 基于证据强度
                    int consistencyScore = 0;

                    // 形状特征支持
                    if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
                        consistencyScore += 2;

                    // 行列结构支持
                    if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns)
                        consistencyScore += 2;

                    // 边界变异支持
                    if ((result.HorizontalDirection == TextFlowDirection.LeftToRight &&
                         evidence.LeftEdgeVariance < evidence.RightEdgeVariance) ||
                        (result.HorizontalDirection == TextFlowDirection.RightToLeft &&
                         evidence.RightEdgeVariance < evidence.LeftEdgeVariance))
                    {
                        consistencyScore += 1;
                    }

                    // 应用一致性增强
                    int consistencyBonus = Math.Min(15, consistencyScore * 3);
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyBonus);
                    result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + consistencyBonus / 2);

                    System.Diagnostics.Debug.WriteLine($"横排布局与水平方向一致性检查: 得分={consistencyScore}, 增加置信度 +{consistencyBonus}");
                }
            }
            else // 竖排布局
            {
                // 竖排布局一致性检查 - 与横排类似但针对垂直方向
                if (result.VerticalDirection == TextFlowDirection.TopToBottom ||
                    result.VerticalDirection == TextFlowDirection.BottomToTop)
                {
                    isConsistent = true;

                    // 计算一致性得分
                    int consistencyScore = 0;

                    // 形状特征支持 - 降低阈值从1.5到1.2
                    if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.2)
                        consistencyScore += 3; // 增加得分

                    // 增强对极端竖排情况的检测
                    int extremeVerticalCount = 0;
                    int highVerticalCount = 0;
                    
                    foreach (var cell in this.cells)
                    {
                        if (cell?.location != null)
                        {
                            double ratio = cell.location.height / (double)cell.location.width;
                            if (ratio > 8.0) // 超极端竖排
                            {
                                extremeVerticalCount++;
                            }
                            else if (ratio > 5.0) // 高竖排
                            {
                                highVerticalCount++;
                            }
                        }
                    }
                    
                    System.Diagnostics.Debug.WriteLine($"极端竖排文本块: {extremeVerticalCount}, 高竖排文本块: {highVerticalCount}");
                    
                    // 如果存在多个极端竖排文本块，增加一致性得分
                    if (extremeVerticalCount >= 2) // 降低阈值从3到2
                    {
                        // 增加更高的得分
                        consistencyScore += 5;
                        System.Diagnostics.Debug.WriteLine($"检测到多个极端竖排文本块({extremeVerticalCount}个)，增加一致性得分 +5");
                    }
                    else if (extremeVerticalCount >= 1 && highVerticalCount >= 1) // 添加新条件
                    {
                        // 即使只有1个极端竖排，只要还有高竖排文本，也增加得分
                        consistencyScore += 3;
                        System.Diagnostics.Debug.WriteLine($"检测到1个极端竖排和{highVerticalCount}个高竖排文本块，增加一致性得分 +3");
                    }
                    else if (highVerticalCount >= 3) // 添加仅高竖排文本的判断
                    {
                        consistencyScore += 2;
                        System.Diagnostics.Debug.WriteLine($"检测到多个高竖排文本块({highVerticalCount}个)，增加一致性得分 +2");
                    }

                    // 行列结构支持 - 降低列数要求
                    if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows)
                        consistencyScore += 2;
                    else if (evidence.VerticalAlignedColumns >= 2) // 添加绝对列数判断
                        consistencyScore += 3;

                    // 边界变异支持
                    if ((result.VerticalDirection == TextFlowDirection.TopToBottom &&
                         evidence.TopEdgeVariance < evidence.BottomEdgeVariance) ||
                        (result.VerticalDirection == TextFlowDirection.BottomToTop &&
                         evidence.BottomEdgeVariance < evidence.TopEdgeVariance))
                    {
                        consistencyScore += 1;
                    }

                    // 分析行内文本宽度一致性 - 为中文竖排诗文特殊优化
                    if (extremeVerticalCount + highVerticalCount >= 2)
                    {
                        var verticalBlocks = this.cells
                            .Where(c => c?.location != null && c.location.height / (double)c.location.width > 5.0)
                            .ToList();
                            
                        if (verticalBlocks.Count >= 2)
                        {
                            // 计算宽度的标准差
                            var widths = verticalBlocks.Select(c => c.location.width).ToList();
                            double avgWidth = widths.Average();
                            double widthStdDev = CalculateStandardDeviation(widths);
                            double widthCV = widthStdDev / avgWidth; // 变异系数
                            
                            System.Diagnostics.Debug.WriteLine($"竖排文本块宽度分析 - 平均值: {avgWidth:F2}, 标准差: {widthStdDev:F2}, 变异系数: {widthCV:F2}");
                            
                            // 如果宽度非常一致（表明是规整排版），增加一致性得分
                            if (widthCV < 0.15)
                            {
                                consistencyScore += 4;
                                System.Diagnostics.Debug.WriteLine($"竖排文本块宽度非常一致，很可能是规整排版，增加一致性得分 +4");
                            }
                            else if (widthCV < 0.25)
                            {
                                consistencyScore += 2;
                                System.Diagnostics.Debug.WriteLine($"竖排文本块宽度较一致，增加一致性得分 +2");
                            }
                        }
                    }

                    // 应用一致性增强 - 增加竖排布局的加权得分
                    int consistencyBonus = Math.Min(20, consistencyScore * 3); // 增加最大值和乘数
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyBonus);
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + consistencyBonus / 2);

                    System.Diagnostics.Debug.WriteLine($"竖排布局与垂直方向一致性检查: 得分={consistencyScore}, 增加置信度 +{consistencyBonus}");
                }
            }

            // 处理不一致情况
            if (!isConsistent)
            {
                // 检查不一致程度
                int primaryConfidence = result.IsVerticalLayout ?
                    result.VerticalConfidence : result.HorizontalConfidence;

                // 如果主方向置信度高但布局置信度低，调整布局以匹配主方向
                if (primaryConfidence > result.LayoutConfidence + 25)
                {
                    // 重新评估布局方向
                    if (result.HorizontalConfidence > result.VerticalConfidence + 20)
                    {
                        // 水平方向明显更可信
                        result.IsVerticalLayout = false;
                        result.LayoutConfidence = (result.LayoutConfidence + result.HorizontalConfidence) / 2;
                        System.Diagnostics.Debug.WriteLine($"方向不一致修正: 水平方向置信度明显更高，调整为横排布局");
                    }
                    else if (result.VerticalConfidence > result.HorizontalConfidence + 20)
                    {
                        // 垂直方向明显更可信
                        result.IsVerticalLayout = true;
                        result.LayoutConfidence = (result.LayoutConfidence + result.VerticalConfidence) / 2;
                        System.Diagnostics.Debug.WriteLine($"方向不一致修正: 垂直方向置信度明显更高，调整为竖排布局");
                    }
                }
            }

            // 特殊情况处理：竖排布局中的垂直方向检查
            if (result.IsVerticalLayout)
            {
                // 如果是竖排布局，检查垂直方向是否合理

                // 1. 检查从上到下与从下到上的证据强度
                if (evidence.TopToBottomCount > evidence.BottomToTopCount * 1.5)
                {
                    // 有明显的从上到下的证据
                    if (result.VerticalDirection != TextFlowDirection.TopToBottom)
                    {
                        // 修正垂直方向
                        result.VerticalDirection = TextFlowDirection.TopToBottom;
                        result.VerticalConfidence = Math.Max(70, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"竖排布局中检测到强烈的从上到下证据，修正垂直方向为TopToBottom");
                    }
                }
                else if (evidence.BottomToTopCount > evidence.TopToBottomCount * 1.5)
                {
                    // 有明显的从下到上的证据
                    if (result.VerticalDirection != TextFlowDirection.BottomToTop)
                    {
                        // 修正垂直方向
                        result.VerticalDirection = TextFlowDirection.BottomToTop;
                        result.VerticalConfidence = Math.Max(70, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"竖排布局中检测到强烈的从下到上证据，修正垂直方向为BottomToTop");
                    }
                }
                else
                {
                    // 如果证据不明确，但有多列竖排文本，默认使用从上到下（更常见）
                    if (evidence.VerticalAlignedColumns >= 2 && evidence.VerticalTextCount > evidence.HorizontalTextCount)
                    {
                        result.VerticalDirection = TextFlowDirection.TopToBottom;
                        result.VerticalConfidence = Math.Max(65, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"竖排布局中垂直方向证据不明确，但检测到多列竖排文本，默认使用TopToBottom");
                    }
                }
                
                // 检查是否存在极端高宽比的竖排文本
                bool hasExtremeVertical = false;
                int extremeVerticalTextCount = 0;
                
                foreach (var cell in this.cells)
                {
                    if (cell?.location != null)
                    {
                        double ratio = cell.location.height / (double)cell.location.width;
                        if (ratio > 8.0) // 超极端竖排
                        {
                            hasExtremeVertical = true;
                            extremeVerticalTextCount++;
                        }
                    }
                }
                
                // 如果存在极端竖排文本，且占比较高，进一步增强竖排布局置信度
                if (hasExtremeVertical && extremeVerticalTextCount >= 2)
                {
                    int extremeBonus = Math.Min(15, extremeVerticalTextCount * 3);
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + extremeBonus);
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + extremeBonus / 2);
                    
                    System.Diagnostics.Debug.WriteLine($"检测到极端竖排文本({extremeVerticalTextCount}个)，增强竖排布局置信度 +{extremeBonus}");
                }
            }
            
            System.Diagnostics.Debug.WriteLine($"调整后 - 布局: {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
            System.Diagnostics.Debug.WriteLine($"水平方向: {result.HorizontalDirection}, 置信度: {result.HorizontalConfidence}");
            System.Diagnostics.Debug.WriteLine($"垂直方向: {result.VerticalDirection}, 置信度: {result.VerticalConfidence}");
            System.Diagnostics.Debug.WriteLine("=== 一致性调整结束 ===\n");
        }

        /// <summary>
        /// 计算布局置信度
        /// 基于得分差异比例和总分动态计算置信度，适用于任何得分分布
        /// </summary>
        /// <param name="winningScore">获胜方向的得分</param>
        /// <param name="opposingScore">相反方向的得分</param>
        /// <param name="totalScore">总得分</param>
        /// <returns>计算得出的置信度（55-95）</returns>
        private int CalculateLayoutConfidence(int winningScore, int opposingScore, int totalScore)
        {
            // 基础置信度基于分数差异比例
            // 使用相对差异比例而非绝对差异，使结果更通用
            double scoreDiff = winningScore - opposingScore;
            double diffRatio = scoreDiff / Math.Max(1, totalScore);

            // 映射到置信度范围：55-95
            // 下限保证最低可信度，上限避免过于武断
            int baseConfidence = 55 + (int)(diffRatio * 40);

            // 高总分提升置信度 - 更多证据支持时提升置信度
            // 使用平方根函数使增长合理
            int scoreBoost = (int)Math.Min(10, Math.Sqrt(totalScore));

            return Math.Min(95, baseConfidence + scoreBoost);
        }

        /// <summary>
        /// 应用特殊布局类型规则
        /// 处理各种特殊布局场景，增强系统的通用性
        /// </summary>
        /// <param name="evidence">收集的方向证据</param>
        /// <param name="result">文本方向结果，可能被调整</param>
        private void ApplySpecialLayoutTypeRules(DirectionEvidence evidence, TextDirectionResult result)
        {
            // 特殊布局规则用于处理一些极端或特殊情况
            // 这些规则基于具体的布局特征，适用范围广

            System.Diagnostics.Debug.WriteLine("\n--- 应用特殊布局规则 ---");

            // 1. 多列竖排文本布局
            // 垂直排列的文本列是非常明显的竖排特征
            if (evidence.VerticalAlignedColumns >= 2 &&
                evidence.VerticalTextCount > evidence.HorizontalTextCount &&
                evidence.HorizontalAlignedRows <= 1)
            {
                result.IsVerticalLayout = true;
                int oldConfidence = result.LayoutConfidence;
                // 提高置信度，但不超过95
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 10);

                System.Diagnostics.Debug.WriteLine($"特殊规则1: 检测到多列竖排布局 => 置信度从{oldConfidence}提升到{result.LayoutConfidence}");
            }

            // 2. 多行横排文本布局
            // 水平排列的文本行是非常明显的横排特征
            if (evidence.HorizontalAlignedRows >= 2 &&
                evidence.HorizontalTextCount > evidence.VerticalTextCount &&
                evidence.VerticalAlignedColumns <= 1)
            {
                result.IsVerticalLayout = false;
                int oldConfidence = result.LayoutConfidence;
                // 提高置信度，但不超过95
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 10);

                System.Diagnostics.Debug.WriteLine($"特殊规则2: 检测到多行横排布局 => 置信度从{oldConfidence}提升到{result.LayoutConfidence}");
            }

            // 5. 文本内容极少的情况
            if (evidence.HorizontalTextCount + evidence.VerticalTextCount < 5 &&
                result.LayoutConfidence > 60)
            {
                int oldConfidence = result.LayoutConfidence;
                // 文本内容很少时，降低我们的判断置信度
                result.LayoutConfidence = Math.Min(result.LayoutConfidence, 70);

                if (oldConfidence != result.LayoutConfidence)
                {
                    System.Diagnostics.Debug.WriteLine($"特殊规则5: 文本内容极少 => 降低置信度从{oldConfidence}到{result.LayoutConfidence}");
                }
            }
        }

        /// <summary>
        /// 方向证据数据结构
        /// </summary>
        private class DirectionEvidence
        {
            // 通用布局特征
            public int LeftToRightCount { get; set; } = 0;
            public int RightToLeftCount { get; set; } = 0;
            public int TopToBottomCount { get; set; } = 0;
            public int BottomToTopCount { get; set; } = 0;

            // 文本特征
            public int HorizontalTextCount { get; set; } = 0;  // 宽>高的文本块数量
            public int VerticalTextCount { get; set; } = 0;    // 高>宽的文本块数量

            // 段落特征
            public int ParagraphCount { get; set; } = 0;       // 识别出的段落数量

            // 布局统计
            public int HorizontalAlignedRows { get; set; } = 0;  // 水平排列的行数
            public int VerticalAlignedColumns { get; set; } = 0; // 垂直排列的列数

            // 对齐特征
            public int LeftAlignedCount { get; set; } = 0;     // 左对齐数量
            public int RightAlignedCount { get; set; } = 0;    // 右对齐数量
            public int TopAlignedCount { get; set; } = 0;      // 顶部对齐数量 
            public int BottomAlignedCount { get; set; } = 0;   // 底部对齐数量

            // 对齐差异特征
            public double LeftEdgeVariance { get; set; } = 0;  // 左边界变异度
            public double RightEdgeVariance { get; set; } = 0; // 右边界变异度
            public double TopEdgeVariance { get; set; } = 0;   // 顶部边界变异度
            public double BottomEdgeVariance { get; set; } = 0;// 底部边界变异度

            // 文本布局特征
            public bool IsSequentialParagraphs { get; set; } = false;  // 是否为连续段落布局
            public double LeftAlignmentRatio { get; set; } = 0;        // 左对齐比例
        }

        /// <summary>
        /// 收集各种方向的证据
        /// </summary>
        /// <summary>
        /// 收集各种方向证据，用于后续分析
        /// </summary>
        /// <param name="validCells">有效的文本单元格列表</param>
        /// <returns>包含各种方向证据的对象</returns>
        private DirectionEvidence CollectDirectionEvidence(List<TextCellInfo> validCells)
        {
            var evidence = new DirectionEvidence();

            // 创建一个详细的分析报告
            System.Diagnostics.Debug.WriteLine("============ 方向证据收集开始 ============");
            System.Diagnostics.Debug.WriteLine($"分析 {validCells.Count} 个文本块");

            // 打印所有文本块的基本信息
            PrintCellsBasicInfo(validCells);

            // 1. 分析文本块的形状特征
            System.Diagnostics.Debug.WriteLine("\n--- 1. 形状特征分析 ---");
            AnalyzeTextShapeFeatures(validCells, evidence);

            // 2. 分析水平和垂直排列
            System.Diagnostics.Debug.WriteLine("\n--- 2. 水平排列分析 ---");
            AnalyzeHorizontalAlignment(validCells, evidence);

            System.Diagnostics.Debug.WriteLine("\n--- 3. 垂直排列分析 ---");
            AnalyzeVerticalAlignment(validCells, evidence);

            // 3. 分析整体布局方向
            System.Diagnostics.Debug.WriteLine("\n--- 4. 整体布局方向分析 ---");
            AnalyzeOverallLayout(validCells, evidence);

            // 4. 分析文本对齐特征
            System.Diagnostics.Debug.WriteLine("\n--- 5. 文本对齐特征分析 ---");
            AnalyzeAlignmentFeatures(validCells, evidence);

            // 5. 分析段落特征
            System.Diagnostics.Debug.WriteLine("\n--- 6. 段落特征分析 ---");
            AnalyzeParagraphFeatures(validCells, evidence);

            // 6. 分析边界差异特征
            System.Diagnostics.Debug.WriteLine("\n--- 7. 边界差异特征分析 ---");
            AnalyzeEdgeVarianceFeatures(validCells, evidence);

            // 8. 打印收集到的所有证据摘要
            PrintEvidenceSummary(evidence);

            System.Diagnostics.Debug.WriteLine("============ 方向证据收集结束 ============\n");

            return evidence;
        }

        /// <summary>
        /// 打印所有文本块的基本信息
        /// </summary>
        private void PrintCellsBasicInfo(List<TextCellInfo> cells)
        {
            System.Diagnostics.Debug.WriteLine("\n文本块基本信息:");
            for (int i = 0; i < cells.Count; i++)
            {
                var cell = cells[i];
                double ratio = cell.location.height / (double)cell.location.width;
                string cellType = ratio > 2.0 ? "竖排" : (ratio < 0.5 ? "横排" : "方形");

                System.Diagnostics.Debug.WriteLine($"[{i}] 文本: '{cell.words}', " +
                    $"位置: ({cell.location.left},{cell.location.top}), " +
                    $"大小: {cell.location.width}x{cell.location.height}, " +
                    $"高宽比: {ratio:F2}, 类型: {cellType}");
            }
        }

        /// <summary>
        /// 打印收集到的所有证据摘要
        /// </summary>
        private void PrintEvidenceSummary(DirectionEvidence evidence)
        {
            System.Diagnostics.Debug.WriteLine("\n证据摘要:");
            System.Diagnostics.Debug.WriteLine($"形状特征 - 横向文本: {evidence.HorizontalTextCount}, 竖向文本: {evidence.VerticalTextCount}");
            System.Diagnostics.Debug.WriteLine($"方向计数 - 左到右: {evidence.LeftToRightCount}, 右到左: {evidence.RightToLeftCount}, 上到下: {evidence.TopToBottomCount}, 下到上: {evidence.BottomToTopCount}");
            System.Diagnostics.Debug.WriteLine($"对齐特征 - 左对齐: {evidence.LeftAlignedCount}, 右对齐: {evidence.RightAlignedCount}, 顶对齐: {evidence.TopAlignedCount}, 底对齐: {evidence.BottomAlignedCount}");
            System.Diagnostics.Debug.WriteLine($"边界变异 - 左: {evidence.LeftEdgeVariance:F3}, 右: {evidence.RightEdgeVariance:F3}, 上: {evidence.TopEdgeVariance:F3}, 下: {evidence.BottomEdgeVariance:F3}");
            System.Diagnostics.Debug.WriteLine($"行列特征 - 水平行: {evidence.HorizontalAlignedRows}, 垂直列: {evidence.VerticalAlignedColumns}");
            System.Diagnostics.Debug.WriteLine($"段落特征 - 段落数: {evidence.ParagraphCount}, 左对齐比例: {evidence.LeftAlignmentRatio:F2}, 连续段落: {evidence.IsSequentialParagraphs}");
        }

        /// <summary>
        /// 分析段落特征
        /// </summary>
        private void AnalyzeParagraphFeatures(List<TextCellInfo> cells, DirectionEvidence evidence)
        {
            if (cells.Count < 3) return;

            // 按垂直位置分组识别可能的段落
            var sortedCells = cells.OrderBy(c => c.location.top).ToList();

            int paragraphCount = 1; // 至少有一个段落
            double avgHeight = cells.Average(c => c.location.height);
            double paragraphGapThreshold = avgHeight * 1.5; // 段落间距阈值

            // 通过检测垂直间距大于阈值的地方识别段落
            for (int i = 1; i < sortedCells.Count; i++)
            {
                double gap = sortedCells[i].location.top - (sortedCells[i - 1].location.top + sortedCells[i - 1].location.height);
                if (gap > paragraphGapThreshold)
                {
                    paragraphCount++;
                }
            }

            evidence.ParagraphCount = paragraphCount;
        }

        /// <summary>
        /// 分析边界差异特征
        /// </summary>
        private void AnalyzeEdgeVarianceFeatures(List<TextCellInfo> cells, DirectionEvidence evidence)
        {
            if (cells.Count < 3) return;

            // 计算各边界的值
            var leftEdges = cells.Select(c => c.location.left).ToList();
            var rightEdges = cells.Select(c => c.location.left + c.location.width).ToList();
            var topEdges = cells.Select(c => c.location.top).ToList();
            var bottomEdges = cells.Select(c => c.location.top + c.location.height).ToList();

            // 计算标准差
            double leftStdDev = CalculateStandardDeviation(leftEdges);
            double rightStdDev = CalculateStandardDeviation(rightEdges);
            double topStdDev = CalculateStandardDeviation(topEdges);
            double bottomStdDev = CalculateStandardDeviation(bottomEdges);

            // 计算平均值
            double avgLeft = leftEdges.Average();
            double avgRight = rightEdges.Average();
            double avgTop = topEdges.Average();
            double avgBottom = bottomEdges.Average();

            // 计算归一化的变异系数（标准差/平均值）
            evidence.LeftEdgeVariance = leftStdDev / avgLeft;
            evidence.RightEdgeVariance = rightStdDev / avgRight;
            evidence.TopEdgeVariance = topStdDev / avgTop;
            evidence.BottomEdgeVariance = bottomStdDev / avgBottom;

            // 检测对齐模式（一边对齐，另一边不齐）
            // 左对齐右不齐模式（常见于从左到右的文本）
            if (leftStdDev * 3 < rightStdDev && leftStdDev < 15)
            {
                evidence.LeftToRightCount += 5;
            }

            // 右对齐左不齐模式（常见于从右到左的文本）
            if (rightStdDev * 3 < leftStdDev && rightStdDev < 15)
            {
                evidence.RightToLeftCount += 5;
            }

            // 上对齐下不齐模式（常见于从上到下的文本）
            if (topStdDev * 3 < bottomStdDev && topStdDev < 15)
            {
                evidence.TopToBottomCount += 5;
            }

            // 下对齐上不齐模式（常见于从下到上的文本）
            if (bottomStdDev * 3 < topStdDev && bottomStdDev < 15)
            {
                evidence.BottomToTopCount += 5;
            }
        }

        /// <summary>
        /// 分析文本对齐特征
        /// </summary>
        private void AnalyzeAlignmentFeatures(List<TextCellInfo> cells, DirectionEvidence evidence)
        {
            if (cells.Count < 3) return;

            // 计算左对齐特征
            var leftValues = cells.Select(c => c.location.left).ToList();
            var leftGroups = GroupSimilarValues(leftValues, 10);
            evidence.LeftAlignedCount = leftGroups.Count > 0 ? leftGroups.Max(g => g.Count) : 0;

            // 计算右对齐特征
            var rightValues = cells.Select(c => c.location.left + c.location.width).ToList();
            var rightGroups = GroupSimilarValues(rightValues, 10);
            evidence.RightAlignedCount = rightGroups.Count > 0 ? rightGroups.Max(g => g.Count) : 0;

            // 计算顶部对齐特征
            var topValues = cells.Select(c => c.location.top).ToList();
            var topGroups = GroupSimilarValues(topValues, 10);
            evidence.TopAlignedCount = topGroups.Count > 0 ? topGroups.Max(g => g.Count) : 0;

            // 计算底部对齐特征
            var bottomValues = cells.Select(c => c.location.top + c.location.height).ToList();
            var bottomGroups = GroupSimilarValues(bottomValues, 10);
            evidence.BottomAlignedCount = bottomGroups.Count > 0 ? bottomGroups.Max(g => g.Count) : 0;

            // 基于对齐特征的方向判断
            int alignmentThreshold = Math.Max(3, cells.Count / 3); // 至少3个或1/3的文本块对齐

            // 左对齐支持从左到右
            if (evidence.LeftAlignedCount >= alignmentThreshold)
            {
                evidence.LeftToRightCount += 2;
            }

            // 右对齐支持从右到左
            if (evidence.RightAlignedCount >= alignmentThreshold)
            {
                evidence.RightToLeftCount += 2;
            }

            // 顶部对齐支持从上到下
            if (evidence.TopAlignedCount >= alignmentThreshold)
            {
                evidence.TopToBottomCount += 2;
            }

            // 底部对齐支持从下到上
            if (evidence.BottomAlignedCount >= alignmentThreshold)
            {
                evidence.BottomToTopCount += 2;
            }
        }

        /// <summary>
        /// 将相似的值分组
        /// </summary>
        private List<List<double>> GroupSimilarValues(List<double> values, double threshold)
        {
            var groups = new List<List<double>>();

            foreach (var value in values)
            {
                bool addedToExisting = false;

                foreach (var group in groups)
                {
                    if (Math.Abs(group[0] - value) <= threshold)
                    {
                        group.Add(value);
                        addedToExisting = true;
                        break;
                    }
                }

                if (!addedToExisting)
                {
                    groups.Add(new List<double> { value });
                }
            }

            return groups;
        }

        /// <summary>
        /// 分析文本块的形状特征
        /// </summary>
        /// <summary>
        /// 分析文本块的形状特征，计算横向和竖向文本的数量
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <param name="evidence">方向证据对象，用于存储分析结果</param>
        private void AnalyzeTextShapeFeatures(List<TextCellInfo> cells, DirectionEvidence evidence)
        {
            // 计算文本块形状分布
            foreach (var cell in cells)
            {
                // 宽高比: 判断文本块是横向还是纵向
                double ratio = cell.location.width / cell.location.height;

                // 计算文本块特征
                bool isTooSmall = cell.location.width < 20 || cell.location.height < 20;
                bool isVerySmall = cell.location.width < 10 || cell.location.height < 10;
                double textArea = cell.location.width * cell.location.height;
                int textLength = (cell.words != null) ? cell.words.Length : 0;

                // 忽略极小的文本块（通常是标点符号、噪点或单个字符）
                if (isVerySmall)
                {
                    System.Diagnostics.Debug.WriteLine($"文本: '{cell.words}', 比例: {ratio:F2}, 大小: {textArea:F0} - 忽略(极小)");
                    continue;
                }

                // 权重计算更加精细化
                double weightFactor = 1.0;

                // 1. 文本长度权重 - 长文本更有代表性
                if (textLength >= 5) weightFactor += 0.8;  // 较长文本
                else if (textLength >= 3) weightFactor += 0.4;  // 中等长度文本

                // 2. 面积权重 - 大文本块更有代表性
                if (textArea > 15000) weightFactor += 0.8;  // 非常大的文本块
                else if (textArea > 8000) weightFactor += 0.5;  // 较大的文本块
                else if (textArea > 3000) weightFactor += 0.2;  // 中等大小的文本块

                // 3. 形状极端性权重 - 形状越极端，证据越强
                if (ratio > 3.0 || ratio < 0.33) weightFactor += 0.4;  // 形状非常极端

                // 调试输出
                System.Diagnostics.Debug.WriteLine($"文本: '{cell.words}', 比例: {ratio:F2}, 大小: {textArea:F0}, 长度: {textLength}, 权重: {weightFactor:F1}");

                // 使用更精确的分界线，允许更多矩形被正确分类
                if (ratio > 1.1) // 横向文本判断（宽>高）
                {
                    // 添加渐进式权重增加 - 针对极端横向文本
                    if (ratio > 5.0) weightFactor += 1.0;      // 极端横向 (5:1以上)
                    else if (ratio > 3.0) weightFactor += 0.7; // 明显横向 (3:1以上)
                    else if (ratio > 2.0) weightFactor += 0.4; // 中等横向 (2:1以上)

                    // 添加到横向文本计数，基于权重
                    int weightedCount = (int)Math.Round(weightFactor);
                    evidence.HorizontalTextCount += weightedCount;

                    System.Diagnostics.Debug.WriteLine($"  -> 判定为横向文本，权重计数: +{weightedCount}");
                }
                else if (ratio < 0.9) // 竖向文本判断（高>宽）
                {
                    // 渐进式权重增加 - 针对极端竖向文本
                    if (ratio < 0.2) weightFactor += 1.0;      // 极端竖向 (1:5以上)
                    else if (ratio < 0.33) weightFactor += 0.7; // 明显竖向 (1:3以上)
                    else if (ratio < 0.5) weightFactor += 0.4;  // 中等竖向 (1:2以上)

                    // 添加到竖向文本计数，基于权重
                    int weightedCount = (int)Math.Round(weightFactor);
                    evidence.VerticalTextCount += weightedCount;

                    System.Diagnostics.Debug.WriteLine($"  -> 判定为竖向文本，权重计数: +{weightedCount}");
                }
                // 接近正方形的文本块（0.9 <= ratio <= 1.1）
                else
                {
                    // 将接近正方形的文本块视为中性，但仍然可以提供微弱的证据
                    if (!isTooSmall && textLength >= 2)
                    {
                        // 根据形状略微倾向的方向添加少量计数
                        if (ratio >= 1.0)
                        {
                            evidence.HorizontalTextCount += 1; // 轻微横向倾向
                            System.Diagnostics.Debug.WriteLine($"  -> 接近正方形但略偏横向，轻微计数: +1");
                        }
                        else
                        {
                            evidence.VerticalTextCount += 1; // 轻微纵向倾向
                            System.Diagnostics.Debug.WriteLine($"  -> 接近正方形但略偏竖向，轻微计数: +1");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"  -> 判定为方形文本，不计入形状统计");
                    }
                }
            }

            // 输出最终形状统计
            System.Diagnostics.Debug.WriteLine($"形状分析结果 - 横向文本: {evidence.HorizontalTextCount}, 竖向文本: {evidence.VerticalTextCount}");
        }

        /// <summary>
        /// 分析水平排列
        /// </summary>
        private void AnalyzeHorizontalAlignment(List<TextCellInfo> cells, DirectionEvidence evidence)
        {
            // 过滤掉太小的元素，避免干扰分析
            var validCells = cells
                .Where(c => c.location.width >= 10 && c.location.height >= 20)
                .ToList();
                
            if (validCells.Count < 2) return;
            
            // 按垂直位置分组
            var cellsByTop = validCells
                .GroupBy(c => Math.Round(c.location.top / groupingThresholdVertical) * groupingThresholdVertical)
                .OrderBy(g => g.Key)
                .ToList();

            double totalHorizontalGap = 0;
            int gapCount = 0;
            
            // 检查行排列的规律性
            if (cellsByTop.Count >= 2)
            {
                // 计算行间距
                List<double> rowGaps = new List<double>();
                for (int i = 1; i < cellsByTop.Count; i++)
                {
                    double currentCenter = cellsByTop[i].Key + cellsByTop[i].Average(c => c.location.height) / 2;
                    double prevCenter = cellsByTop[i - 1].Key + cellsByTop[i - 1].Average(c => c.location.height) / 2;
                    rowGaps.Add(Math.Abs(currentCenter - prevCenter));
                }
                
                // 检查行间距是否均匀
                if (rowGaps.Count >= 2)
                {
                    double avgGap = rowGaps.Average();
                    double gapStdDev = CalculateStandardDeviation(rowGaps);
                    double gapCoeffVar = avgGap > 0 ? gapStdDev / avgGap : 0;
                    
                    // 如果行间距均匀，增强横排特征
                    if (gapCoeffVar < 0.3)
                    {
                        int extraWeight = Math.Min(6, cellsByTop.Count * 2);
                        evidence.HorizontalAlignedRows += extraWeight;
                        System.Diagnostics.Debug.WriteLine($"检测到规律的行间距，行间变异系数: {gapCoeffVar:F2}, 增加水平行权重 +{extraWeight}");
                    }
                    else
                    {
                        evidence.HorizontalAlignedRows += 2;
                    }
                }
            }

            // 检查横排行特征
            bool hasLongHorizontalRows = false;

            foreach (var group in cellsByTop)
            {
                if (group.Count() > 1)
                {
                    evidence.HorizontalAlignedRows++;

                    var rowCells = group.OrderBy(c => c.location.left).ToList();
                    int leftToRightCount = 0;
                    int rightToLeftCount = 0;
                    
                    // 计算行内文本长度 - 长文本内容行更有代表性
                    int totalContentLength = rowCells.Sum(c => c.words?.Length ?? 0);
                    double rowWidthSpan = rowCells.Last().location.left + rowCells.Last().location.width - rowCells.First().location.left;
                    
                    // 检测长行
                    if (rowCells.Count >= 3 || rowWidthSpan > 150)
                    {
                        hasLongHorizontalRows = true;
                    }

                    // 分析行内元素的排列方向
                    for (int i = 1; i < rowCells.Count; i++)
                    {
                        double gap = rowCells[i].location.left - (rowCells[i - 1].location.left + rowCells[i - 1].location.width);

                        // 考虑间隙
                        totalHorizontalGap += Math.Max(0, gap);
                        gapCount++;

                        // 判断方向
                        if (gap >= -rowCells[i - 1].location.width * 0.2) // 允许少量重叠
                        {
                            leftToRightCount++;
                        }
                        else // 重叠超过20%才视为从右到左
                        {
                            rightToLeftCount++;
                        }
                    }

                    // 给总体方向计数增加行内统计的结果，基于内容长度加权
                    int contentWeight = Math.Min(3, totalContentLength / 10);
                    if (leftToRightCount > rightToLeftCount)
                    {
                        evidence.LeftToRightCount += leftToRightCount + contentWeight;
                    }
                    else if (rightToLeftCount > leftToRightCount)
                    {
                        evidence.RightToLeftCount += rightToLeftCount + contentWeight;
                    }
                    
                    // 检查行内文本块的宽高比
                    double avgWidthHeightRatio = rowCells.Average(c => c.location.width / (double)c.location.height);
                    
                    // 如果行内文本块平均宽高比很大（横排文本），增加水平方向的证据权重
                    if (avgWidthHeightRatio > 3.0)
                    {
                        // 渐进式增加权重 - 比例越大，权重越高
                        int extraWeight = Math.Min(8, (int)Math.Ceiling(avgWidthHeightRatio / 1.5));
                        evidence.LeftToRightCount += extraWeight;
                        System.Diagnostics.Debug.WriteLine($"行内文本块平均宽高比: {avgWidthHeightRatio:F2}, 增加从左到右权重 +{extraWeight}");
                    }
                }
            }

            // 横排文本的特殊处理：如果有多行横排文本，这是很强的从左到右的证据
            if (evidence.HorizontalAlignedRows >= 3 || hasLongHorizontalRows)
            {
                // 计算横排文本块的数量
                int horizontalTextCount = validCells.Count(c => c.location.width > c.location.height * 2);

                // 如果大部分文本块是横排的，且有多行，增加从左到右的权重
                if (horizontalTextCount > validCells.Count * 0.5)
                {
                    int bonus = hasLongHorizontalRows ? 
                        evidence.HorizontalAlignedRows * 3 : 
                        evidence.HorizontalAlignedRows * 2;
                        
                    evidence.LeftToRightCount += bonus;
                    System.Diagnostics.Debug.WriteLine($"检测到多行横排文本({evidence.HorizontalAlignedRows}行), 增加从左到右权重 +{bonus}");
                }
            }
            
            // 检查极端横排文本特征
            int extremeHorizontalTextCount = validCells.Count(c => c.location.width / (double)c.location.height > 8.0);
            if (extremeHorizontalTextCount >= 3 && extremeHorizontalTextCount > validCells.Count * 0.4)
            {
                // 检测到多个极端横排文本块，强有力的横排证据
                evidence.HorizontalTextCount += extremeHorizontalTextCount * 2;
                evidence.LeftToRightCount += extremeHorizontalTextCount * 2;
                System.Diagnostics.Debug.WriteLine($"检测到多个极端横排文本块({extremeHorizontalTextCount}个), 增强横排证据");
            }
        }

        /// <summary>
        /// 分析垂直排列
        /// </summary>
        private void AnalyzeVerticalAlignment(List<TextCellInfo> cells, DirectionEvidence evidence)
        {
            // 过滤掉太小的元素，避免干扰分析
            var validCells = cells
                .Where(c => c.location.width >= 10 && c.location.height >= 20)
                .ToList();
                
            if (validCells.Count < 2) return;
            
            // 按水平位置分组
            var cellsByLeft = validCells
                .GroupBy(c => Math.Round(c.location.left / groupingThresholdHorizontal) * groupingThresholdHorizontal)
                .OrderBy(g => g.Key)
                .ToList();

            double totalVerticalGap = 0;
            int gapCount = 0;

            // 检测列的排列规律
            if (cellsByLeft.Count >= 2)
            {
                // 计算列间距
                List<double> columnGaps = new List<double>();
                for (int i = 1; i < cellsByLeft.Count; i++)
                {
                    double currentCenter = cellsByLeft[i].Key + cellsByLeft[i].Average(c => c.location.width) / 2;
                    double prevCenter = cellsByLeft[i - 1].Key + cellsByLeft[i - 1].Average(c => c.location.width) / 2;
                    columnGaps.Add(Math.Abs(currentCenter - prevCenter));
                }

                // 检查列间距是否相对均匀（表示有规律的排列）
                if (columnGaps.Count >= 2)
                {
                    double avgGap = columnGaps.Average();
                    double gapStdDev = CalculateStandardDeviation(columnGaps);
                    double gapCoeffVar = avgGap > 0 ? gapStdDev / avgGap : 0; // 变异系数

                    // 如果列间距相对均匀（变异系数小），这是强有力的多列证据
                    if (gapCoeffVar < 0.3)
                    {
                        // 增加垂直列的权重 - 根据列数和规律性增加权重
                        int extraWeight = Math.Min(6, cellsByLeft.Count * 2);
                        evidence.VerticalAlignedColumns += extraWeight;
                        System.Diagnostics.Debug.WriteLine($"检测到规律的列间距，列间变异系数: {gapCoeffVar:F2}, 增加垂直列权重 +{extraWeight}");
                    }
                    else
                    {
                        // 列间距不太均匀，但仍有一定规律性
                        evidence.VerticalAlignedColumns += 2;
                    }
                }
                
                // 检查从右到左的列排序模式（典型中文竖排特征）
                if (cellsByLeft.Count >= 3)
                {
                    // 计算最左侧列和最右侧列的内容长度
                    double leftColContentLength = cellsByLeft.First().Sum(c => c.words?.Length ?? 0);
                    double rightColContentLength = cellsByLeft.Last().Sum(c => c.words?.Length ?? 0);
                    
                    // 如果右侧列内容更长（典型中文竖排从右到左开始），增加从右到左的证据
                    if (rightColContentLength > leftColContentLength * 1.2)
                    {
                        evidence.RightToLeftCount += 5;
                        System.Diagnostics.Debug.WriteLine($"检测到可能的从右到左列排序模式，右侧列内容更长");
                    }
                }
            }

            // 检查竖排列特征
            bool hasLongVerticalColumns = false;
            
            foreach (var group in cellsByLeft)
            {
                if (group.Count() > 1)
                {
                    evidence.VerticalAlignedColumns++;

                    var colCells = group.OrderBy(c => c.location.top).ToList();
                    int topToBottomCount = 0;
                    int bottomToTopCount = 0;
                    
                    // 计算列内文本长度 - 长文本内容列更有代表性
                    int totalContentLength = colCells.Sum(c => c.words?.Length ?? 0);
                    double colHeightSpan = colCells.Last().location.top + colCells.Last().location.height - colCells.First().location.top;
                    
                    // 检测长列
                    if (colCells.Count >= 3 || colHeightSpan > 150)
                    {
                        hasLongVerticalColumns = true;
                    }

                    // 分析列内元素的排列方向
                    for (int i = 1; i < colCells.Count; i++)
                    {
                        double gap = colCells[i].location.top - (colCells[i - 1].location.top + colCells[i - 1].location.height);

                        // 考虑间隙
                        totalVerticalGap += Math.Max(0, gap);
                        gapCount++;

                        // 判断方向
                        if (gap >= -colCells[i - 1].location.height * 0.2) // 允许少量重叠
                        {
                            topToBottomCount++;
                        }
                        else // 重叠超过20%才视为从下到上
                        {
                            bottomToTopCount++;
                        }
                    }

                    // 给总体方向计数增加列内统计的结果 - 基于内容长度加权
                    int contentWeight = Math.Min(3, totalContentLength / 10);
                    if (topToBottomCount > bottomToTopCount)
                    {
                        evidence.TopToBottomCount += topToBottomCount + contentWeight;
                    }
                    else if (bottomToTopCount > topToBottomCount)
                    {
                        evidence.BottomToTopCount += bottomToTopCount + contentWeight;
                    }

                    // 检查列内文本块的高宽比
                    double avgHeightWidthRatio = colCells.Average(c => c.location.height / (double)c.location.width);

                    // 如果列内文本块平均高宽比很大（竖排文本），增加垂直方向的证据权重
                    if (avgHeightWidthRatio > 3.0)
                    {
                        // 渐进式增加权重 - 比例越大，权重越高
                        int extraWeight = Math.Min(8, (int)Math.Ceiling(avgHeightWidthRatio / 1.5));
                        evidence.TopToBottomCount += extraWeight;
                        System.Diagnostics.Debug.WriteLine($"列内文本块平均高宽比: {avgHeightWidthRatio:F2}, 增加从上到下权重 +{extraWeight}");
                    }
                }
            }

            // 竖排文本的特殊处理：如果有多列竖排文本，这是很强的从上到下的证据
            if (evidence.VerticalAlignedColumns >= 3 || hasLongVerticalColumns)
            {
                // 计算竖排文本块的数量
                int verticalTextCount = validCells.Count(c => c.location.height > c.location.width * 2);

                // 如果大部分文本块是竖排的，且有多列，增加从上到下的权重
                if (verticalTextCount > validCells.Count * 0.5)
                {
                    int bonus = hasLongVerticalColumns ? 
                        evidence.VerticalAlignedColumns * 3 : 
                        evidence.VerticalAlignedColumns * 2;
                        
                    evidence.TopToBottomCount += bonus;
                    System.Diagnostics.Debug.WriteLine($"检测到多列竖排文本({evidence.VerticalAlignedColumns}列), 增加从上到下权重 +{bonus}");
                }
            }
            
            // 检查极端竖排文本特征
            int extremeVerticalTextCount = validCells.Count(c => c.location.height / (double)c.location.width > 8.0);
            if (extremeVerticalTextCount >= 3 && extremeVerticalTextCount > validCells.Count * 0.4)
            {
                // 检测到多个极端竖排文本块，强有力的竖排证据
                evidence.VerticalTextCount += extremeVerticalTextCount * 2;
                evidence.TopToBottomCount += extremeVerticalTextCount * 2;
                System.Diagnostics.Debug.WriteLine($"检测到多个极端竖排文本块({extremeVerticalTextCount}个), 增强竖排证据");
            }
        }

        /// <summary>
        /// 分析整体布局方向
        /// </summary>
        private void AnalyzeOverallLayout(List<TextCellInfo> cells, DirectionEvidence evidence)
        {
            // 如果文本块数量少于3个，不进行整体布局分析
            if (cells.Count < 3) return;

            // 简化的布局分析，只考虑最基本的排列方向
            var sortedByTop = cells.OrderBy(c => c.location.top).ToList();
            var sortedByLeft = cells.OrderBy(c => c.location.left).ToList();

            // 检查是否主要是从上到下排列（通用布局特征）
            int topToBottomOrderedCount = 0;
            for (int i = 1; i < sortedByTop.Count; i++)
            {
                // 如果后一个元素的top大于前一个元素的bottom，认为是从上到下
                if (sortedByTop[i].location.top > sortedByTop[i - 1].location.top + sortedByTop[i - 1].location.height * 0.5)
                {
                    topToBottomOrderedCount++;
                }
            }

            // 检查是否主要是从左到右排列（通用布局特征）
            int leftToRightOrderedCount = 0;
            for (int i = 1; i < sortedByLeft.Count; i++)
            {
                // 如果后一个元素的left大于前一个元素的right，认为是从左到右
                if (sortedByLeft[i].location.left > sortedByLeft[i - 1].location.left + sortedByLeft[i - 1].location.width * 0.5)
                {
                    leftToRightOrderedCount++;
                }
            }

            // 增加计数，但不设置过高的权重
            if (topToBottomOrderedCount >= (sortedByTop.Count - 1) * 0.5)
            {
                evidence.TopToBottomCount += 2;
            }

            if (leftToRightOrderedCount >= (sortedByLeft.Count - 1) * 0.5)
            {
                evidence.LeftToRightCount += 2;
            }
        }

        /// <summary>
        /// 综合分析方向证据，确定最终方向
        /// 使用得分累加系统来判断方向，适用于各种语言和文档类型
        /// </summary>
        /// <param name="evidence">收集的方向证据</param>
        /// <param name="result">输出的方向结果</param>
        /// <param name="cells">文本单元格列表</param>
        private void AnalyzeDirectionEvidence(DirectionEvidence evidence, TextDirectionResult result, List<TextCellInfo> cells)
        {
            // 使用得分累加系统，而非直接设置置信度
            // 这种方法更加通用，可以处理各种语言和排版方式

            // ====================== 水平方向分析 ======================
            // 水平方向得分系统，分别计算从左到右和从右到左的证据得分
            int leftToRightScore = 0;
            int rightToLeftScore = 0;
            
            // 检查是否有极端竖排比例
            bool hasExtremeVerticalRatio = false;
            int extremeVerticalCount = 0;
            
            // 检查文本是否包含极端竖排布局
            foreach (var cell in cells)
            {
                if (cell?.location != null)
                {
                    double ratio = cell.location.height / (double)cell.location.width;
                    if (ratio > extremeVerticalRatioThreshold)
                    {
                        hasExtremeVerticalRatio = true;
                        extremeVerticalCount++;
                    }
                }
            }
            
            // 如果存在多个极端竖排文本块，增加从右到左的证据权重
            if (hasExtremeVerticalRatio && extremeVerticalCount >= 3 && evidence.VerticalTextCount > evidence.HorizontalTextCount)
            {
                rightToLeftScore += 6;
                System.Diagnostics.Debug.WriteLine("检测到多个极端竖排文本块，增加RightToLeft得分 +6");
            }

            // 1.1 基础方向证据 - 乘以2增加权重，因为这是最直接的方向指标
            leftToRightScore += evidence.LeftToRightCount * 2;
            rightToLeftScore += evidence.RightToLeftCount * 2;

            // 1.2 对齐特征 - 文本块的对齐模式是很强的方向证据
            if (evidence.LeftAlignedCount > evidence.RightAlignedCount * 1.5)
            {
                // 左对齐文本块明显多于右对齐，支持从左到右方向
                leftToRightScore += evidence.LeftAlignedCount;
            }
            else if (evidence.RightAlignedCount > evidence.LeftAlignedCount * 1.5)
            {
                // 右对齐文本块明显多于左对齐，支持从右到左方向
                rightToLeftScore += evidence.RightAlignedCount;
            }

            // 1.3 边界变异特征 - 分析文本边界的整齐度
            // 阅读起始边通常更整齐，因为是对齐的参考点
            if (evidence.LeftEdgeVariance * 2 < evidence.RightEdgeVariance)
            {
                // 左边界比右边界更整齐（变异度小），支持从左到右
                leftToRightScore += 3;
            }
            else if (evidence.RightEdgeVariance * 2 < evidence.LeftEdgeVariance)
            {
                // 右边界比左边界更整齐，支持从右到左
                rightToLeftScore += 3;
            }

            // 1.4 段落特征 - 段落组织方式是强有力的方向证据
            if (evidence.ParagraphCount >= 3 && evidence.LeftAlignmentRatio > 0.7)
            {
                // 多个左对齐段落，几乎可以确定是从左到右的文本
                // 例：多段落的英文文章，中文文章
                leftToRightScore += evidence.ParagraphCount;
            }

            // 1.5 行排列特征 - 多行排列的模式
            if (evidence.HorizontalAlignedRows > 2)
            {
                // 存在多行水平排列的文本，通常表示从左到右阅读
                // 例：普通文章的多行文本
                leftToRightScore += evidence.HorizontalAlignedRows;
            }
            
            // 1.6 竖排布局与水平方向的关系
            // 竖排文本通常从右到左排列列
            if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5 &&
                evidence.VerticalAlignedColumns >= 2)
            {
                // 明显竖排布局，且有多列，增加从右到左的倾向
                rightToLeftScore += 5;
                System.Diagnostics.Debug.WriteLine("基于明显竖排布局，增加RightToLeft得分 +5");
            }

            // 确定水平方向 - 基于得分比例选择最可能的方向
            int totalHorizontalScore = leftToRightScore + rightToLeftScore;
            if (totalHorizontalScore > 0)
            {
                // 计算从左到右得分占总分的比例
                double ltrRatio = (double)leftToRightScore / totalHorizontalScore;
                
                System.Diagnostics.Debug.WriteLine($"水平方向得分分析: LeftToRight={leftToRightScore}, RightToLeft={rightToLeftScore}, 比例={ltrRatio:P2}");

                // 竖排布局时调整水平方向阈值，更倾向于从右到左
                double ltrThreshold = 0.55; // 普通情况的阈值
                double rtlThreshold = 0.45; // 普通情况的阈值
                
                // 如果是竖排布局且有极端高宽比，调整阈值使更容易判断为从右到左
                if (evidence.VerticalTextCount > evidence.HorizontalTextCount && hasExtremeVerticalRatio)
                {
                    ltrThreshold = 0.65; // 需要更强的从左到右证据
                    rtlThreshold = 0.35; // 更容易判断为从右到左
                    System.Diagnostics.Debug.WriteLine("竖排极端布局调整水平方向阈值: LeftToRight需要更强的证据(>65%)");
                }

                if (ltrRatio >= ltrThreshold) // 足够的证据支持从左到右
                {
                    result.HorizontalDirection = TextFlowDirection.LeftToRight;
                    // 计算置信度 - 基于得分差异和总分
                    result.HorizontalConfidence = CalculateConfidenceFromScore(
                        leftToRightScore, rightToLeftScore, totalHorizontalScore);
                }
                else if (ltrRatio <= rtlThreshold) // 足够的证据支持从右到左
                {
                    result.HorizontalDirection = TextFlowDirection.RightToLeft;
                    // 计算置信度 - 基于得分差异和总分
                    result.HorizontalConfidence = CalculateConfidenceFromScore(
                        rightToLeftScore, leftToRightScore, totalHorizontalScore);
                }
                else // 阈值之间，方向不明确
                {
                    // 竖排布局时，当证据不明确，默认选择从右到左（因为竖排通常从右到左）
                    if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.2)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.HorizontalConfidence = 65; // 中等置信度
                        System.Diagnostics.Debug.WriteLine("证据不足但竖排特征明显，默认使用RightToLeft方向");
                    }
                    else 
                    {
                        // 否则默认使用从左到右（最常见）
                        result.HorizontalDirection = TextFlowDirection.LeftToRight;
                        result.HorizontalConfidence = 60; // 较低置信度表示不确定性
                    }
                }
            }
            else // 没有任何方向证据
            {
                // 没有方向证据，使用默认方向
                result.HorizontalDirection = TextFlowDirection.LeftToRight; // 最通用的默认值
                result.HorizontalConfidence = 50; // 中等置信度
            }

            // ====================== 垂直方向分析 ======================
            int topToBottomScore = 0;
            int bottomToTopScore = 0;

            // 2.1 基础方向证据 - 与水平方向分析类似
            topToBottomScore += evidence.TopToBottomCount * 2;
            bottomToTopScore += evidence.BottomToTopCount * 2;

            // 2.2 对齐特征
            if (evidence.TopAlignedCount > evidence.BottomAlignedCount * 1.5)
            {
                // 顶部对齐的文本块明显多于底部对齐
                // 例：普通文档的段落通常顶部对齐
                topToBottomScore += evidence.TopAlignedCount;
            }
            else if (evidence.BottomAlignedCount > evidence.TopAlignedCount * 1.5)
            {
                // 底部对齐的文本块明显多于顶部对齐
                // 例：某些特殊设计的文档，如从页面底部开始的设计
                bottomToTopScore += evidence.BottomAlignedCount;
            }

            // 2.3 边界变异特征
            if (evidence.TopEdgeVariance * 2 < evidence.BottomEdgeVariance)
            {
                // 顶部边界更整齐，支持从上到下
                // 例：常规文档段落顶部通常对齐，底部参差不齐
                topToBottomScore += 3;
            }
            else if (evidence.BottomEdgeVariance * 2 < evidence.TopEdgeVariance)
            {
                // 底部边界更整齐，支持从下到上
                // 例：某些从页面底部开始的排版设计
                bottomToTopScore += 3;
            }

            // 2.4 段落特征
            if (evidence.ParagraphCount >= 2)
            {
                // 多段落文本几乎总是从上到下阅读的
                // 例：几乎所有语言的多段落文档
                topToBottomScore += evidence.ParagraphCount * 2;
            }

            // 2.5 列排列特征
            if (evidence.VerticalAlignedColumns > 2)
            {
                // 多列垂直排列的文本，暗示从上到下阅读
                // 例：报纸多栏布局，词典等参考书
                topToBottomScore += evidence.VerticalAlignedColumns;
            }
            
            // 2.6 极端竖排文本几乎总是从上到下的
            if (hasExtremeVerticalRatio && evidence.VerticalTextCount > evidence.HorizontalTextCount)
            {
                topToBottomScore += 8;
                System.Diagnostics.Debug.WriteLine("检测到极端竖排布局，增加TopToBottom得分 +8");
            }

            // 确定垂直方向 - 逻辑与水平方向分析类似
            int totalVerticalScore = topToBottomScore + bottomToTopScore;
            if (totalVerticalScore > 0)
            {
                double ttbRatio = (double)topToBottomScore / totalVerticalScore;
                
                System.Diagnostics.Debug.WriteLine($"垂直方向得分分析: TopToBottom={topToBottomScore}, BottomToTop={bottomToTopScore}, 比例={ttbRatio:P2}");

                if (ttbRatio >= 0.55)
                {
                    result.VerticalDirection = TextFlowDirection.TopToBottom;
                    result.VerticalConfidence = CalculateConfidenceFromScore(
                        topToBottomScore, bottomToTopScore, totalVerticalScore);
                }
                else if (ttbRatio <= 0.45)
                {
                    result.VerticalDirection = TextFlowDirection.BottomToTop;
                    result.VerticalConfidence = CalculateConfidenceFromScore(
                        bottomToTopScore, topToBottomScore, totalVerticalScore);
                }
                else
                {
                    // 分数接近，方向不明确
                    result.VerticalDirection = TextFlowDirection.TopToBottom; // 默认最常见的
                    result.VerticalConfidence = 50;
                }
            }
            else
            {
                // 没有方向证据，使用默认方向
                result.VerticalDirection = TextFlowDirection.TopToBottom;
                result.VerticalConfidence = 50;
            }

            // 3. 方向特征增强 - 基于特征一致性进一步调整置信度
            ApplyDirectionConfidenceEnhancement(result, evidence);
        }

        /// <summary>
        /// 增强方向置信度 - 基于特征的一致性
        /// </summary>
        private void ApplyDirectionConfidenceEnhancement(TextDirectionResult result, DirectionEvidence evidence)
        {
            // 一致性检测 - 如果所有证据都强烈指向同一方向，提高置信度
            if (evidence.LeftToRightCount > evidence.RightToLeftCount * 3 &&
                evidence.HorizontalTextCount > evidence.VerticalTextCount * 2)
            {
                // 明显的从左到右布局
                result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 5);
            }
            else if (evidence.RightToLeftCount > evidence.LeftToRightCount * 3 &&
                     evidence.HorizontalTextCount > evidence.VerticalTextCount * 2)
            {
                // 明显的从右到左布局
                result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 5);
            }

            if (evidence.TopToBottomCount > evidence.BottomToTopCount * 3 &&
                evidence.VerticalTextCount > evidence.HorizontalTextCount * 2)
            {
                // 明显的从上到下布局
                result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + 5);
            }
            else if (evidence.BottomToTopCount > evidence.TopToBottomCount * 3 &&
                     evidence.VerticalTextCount > evidence.HorizontalTextCount * 2)
            {
                // 明显的从下到上布局
                result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + 5);
            }
        }

        /// <summary>
        /// 基于分数计算置信度
        /// 动态计算基于比例和总分的置信度，适用于任何得分分布
        /// </summary>
        /// <param name="winningScore">获胜方向的得分</param>
        /// <param name="opposingScore">相反方向的得分</param>
        /// <param name="totalScore">总得分</param>
        /// <returns>计算得出的置信度（50-95）</returns>
        private int CalculateConfidenceFromScore(int winningScore, int opposingScore, int totalScore)
        {
            // 基础置信度基于分数差异比例
            // 使用相对差异比例而非绝对差异，使结果更通用
            double scoreDiffRatio = (double)(winningScore - opposingScore) / Math.Max(1, totalScore);

            // 映射到置信度范围：50-95
            int baseConfidence = 50 + (int)(scoreDiffRatio * 45);

            // 高分数总量提升置信度
            int scoreBoost = (int)Math.Min(10, Math.Sqrt(totalScore));

            return Math.Min(95, baseConfidence + scoreBoost);
        }

        /// <summary>
        /// 计算自适应分组阈值，基于文本块高宽比分布
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        private void CalculateAdaptiveThresholds(List<TextCellInfo> cells)
        {
            // 初始化默认阈值
            double defaultThresholdVertical = 25.0;
            double defaultThresholdHorizontal = 10.0;
            
            if (cells.Count < 2)
            {
                groupingThresholdVertical = defaultThresholdVertical;
                groupingThresholdHorizontal = defaultThresholdHorizontal;
                return;
            }

            // 计算比例分布
            var ratios = cells
                .Where(c => c?.location != null)
                .Select(c => Math.Max(
                    c.location.height / (double)c.location.width,
                    c.location.width / (double)c.location.height))
                .OrderBy(r => r)
                .ToList();

            if (ratios.Count == 0)
            {
                groupingThresholdVertical = defaultThresholdVertical;
                groupingThresholdHorizontal = defaultThresholdHorizontal;
                return;
            }

            // 检查是否存在极端比例（高于7:1）
            bool hasExtremeRatio = ratios.Any(r => r > 7.0);
            // 检查是否存在超极端比例（高于10:1）
            bool hasSuperExtremeRatio = ratios.Any(r => r > 10.0);

            // 分析比例分布，计算高比例文本块的百分比
            int extremeRatioCount = ratios.Count(r => r > 5.0);
            double extremeRatioPercentage = (double)extremeRatioCount / ratios.Count;

            // 计算自适应阈值
            double adaptiveVerticalThreshold;
            double adaptiveHorizontalThreshold;
            
            // 根据极端比例的存在情况，自适应调整阈值
            if (hasSuperExtremeRatio)
            {
                System.Diagnostics.Debug.WriteLine("检测到超极端竖排比例(>10:1)，使用特殊阈值");
                adaptiveVerticalThreshold = defaultThresholdVertical * 2.0; // 更大的阈值
                adaptiveHorizontalThreshold = defaultThresholdHorizontal * 1.8; // 也调整横向阈值
            }
            else if (hasExtremeRatio)
            {
                System.Diagnostics.Debug.WriteLine("检测到极端比例(>7:1)，调整阈值");
                adaptiveVerticalThreshold = defaultThresholdVertical * 1.5; // 调整阈值
                adaptiveHorizontalThreshold = defaultThresholdHorizontal * 1.3;
            }
            else if (extremeRatioPercentage > 0.3) // 如果30%以上的文本块都有较高比例
            {
                adaptiveVerticalThreshold = defaultThresholdVertical * 1.2; 
                adaptiveHorizontalThreshold = defaultThresholdHorizontal * 1.2;
            }
            else
            {
                // 使用标准阈值
                adaptiveVerticalThreshold = defaultThresholdVertical;
                adaptiveHorizontalThreshold = defaultThresholdHorizontal;
            }

            // 安全检查 - 确保阈值在合理范围内
            groupingThresholdVertical = Math.Max(15.0, Math.Min(adaptiveVerticalThreshold, 100.0));
            groupingThresholdHorizontal = Math.Max(5.0, Math.Min(adaptiveHorizontalThreshold, 50.0));
            
            // 计算极端竖排比例的自适应阈值
            // 这个阈值用于判断哪些文本块是极端竖排的
            if (ratios.Count >= 5)
            {
                // 使用百分位数来判断极端比例
                // 找出前20%的比例值作为极端比例的起点
                int percentileIndex = Math.Max(0, (int)(ratios.Count * 0.8) - 1);
                double percentileValue = ratios[percentileIndex];
                
                // 取5和百分位数的较大值，确保阈值不会太低
                extremeVerticalRatioThreshold = Math.Max(5.0, percentileValue);
                
                System.Diagnostics.Debug.WriteLine($"自适应极端竖排比例阈值: {extremeVerticalRatioThreshold:F1}");
            }
            else
            {
                // 默认值
                extremeVerticalRatioThreshold = 7.0;
            }

            System.Diagnostics.Debug.WriteLine($"自适应阈值 - 垂直: {groupingThresholdVertical:F1}, 水平: {groupingThresholdHorizontal:F1}, 存在极端比例: {hasExtremeRatio}, 超极端比例: {hasSuperExtremeRatio}");
        }

        /// <summary>
        /// 计算标准差
        /// </summary>
        private double CalculateStandardDeviation(IEnumerable<double> values)
        {
            double avg = values.Average();
            double sumOfSquaresOfDifferences = values.Select(val => (val - avg) * (val - avg)).Sum();
            double variance = sumOfSquaresOfDifferences / values.Count();
            return Math.Sqrt(variance);
        }



        /// <summary>
        /// 计算方差
        /// </summary>
        private double CalculateVariance(List<double> values)
        {
            if (values.Count <= 1) return 0;

            double avg = values.Average();
            double sumOfSquaredDifferences = values.Sum(x => (x - avg) * (x - avg));
            return sumOfSquaredDifferences / values.Count;
        }


    }

    /// <summary>
    /// 文本流方向枚举
    /// </summary>
    public enum TextFlowDirection
    {
        /// <summary>从左到右（常见于拉丁语系、中文横排等）</summary>
        LeftToRight,
        /// <summary>从右到左（常见于阿拉伯语、希伯来语等）</summary>
        RightToLeft,
        /// <summary>从上到下（常见于中文竖排等）</summary>
        TopToBottom,
        /// <summary>从下到上（常见于车道提示语等）</summary>
        BottomToTop,
        /// <summary>混合方向（多种方向混合）</summary>
        Mixed
    }
}