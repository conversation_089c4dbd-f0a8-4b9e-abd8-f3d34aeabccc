﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmGoBuy : MetroForm
    {
        private List<UserType> _lstUserTypes = new List<UserType>();

        private ChargeViewToUser _nowSelectedChargeType;

        private UserType _nowSelectedType;

        public UserTypeInfo NextUserType;

        public FrmGoBuy()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            wbPay.ScriptErrorsSuppressed = true;
            wbDesc.ScriptErrorsSuppressed = true;
            wbPay.DocumentCompleted += browser_DocumentCompleted;
            CommonMethod.SetStyle(linkLabel1, ControlStyles.Selectable, false);
        }

        private void browser_DocumentCompleted(object sender,
            WebBrowserDocumentCompletedEventArgs e)
        {
            var browser = (WebBrowser)sender;
            if (browser != null && browser.Document != null && browser.Document.Window != null)
            {
                browser.Document.Window.Error += (s, g) =>
                    {
                        g.Handled = true;
                        if (_isTry) return;
                        _isTry = true;
                        browser.DocumentText = "<p style=\"text-align:center;\"><image src=\"http://ocr.oldfish.cn/pay.png\" height=\"100%\" /></p>";
                    };
            }
        }

        private bool _isTry;

        private void btnContactQQ_Click(object sender, EventArgs e)
        {
            CommonMethod.OpenUrl("http://wpa.qq.com/msgrd?v=3&uin=*********&site=qq&menu=yes");
            CommonMethod.ShowHelpMsg("正在打开客服QQ:*********,如果打开失败，请尝试手动添加好友！", 5000);
        }

        private void btnOpenVip_Click(object sender, EventArgs e)
        {
            if (Program.NowUser == null || string.IsNullOrEmpty(Program.NowUser.Account))
            {
                CommonMethod.ShowHelpMsg("当前用户未登录，请登录后重试！");
                return;
            }
            if (!CommonString.IsOnLine)
            {
                CommonMethod.ShowHelpMsg("当前网络异常，请等待网络恢复后重试！");
                return;
            }

            if (_nowSelectedType == null)
            {
                CommonMethod.ShowHelpMsg("请选择要升级的账户类型后重试！");
                return;
            }

            //if (NowSelectedType.Type == Program.NowUser?.UserType)
            //{
            //    CommonMethod.ShowHelpMsg(string.Format("当前已经是{0}，祝您使用愉快！", NowSelectedType.Type.ToString()));
            //    return;
            //}
            //http://t.cn/AimZvjI8
            Top -= (760 - Height) / 2;
            Height = 760;
            pnlPay.Dock = DockStyle.Fill;
            pnlPay.Visible = true;
            pnlPay.BringToFront();

            var url = CommonString.HostAccountUrl + "code.aspx?op=pay&remark=" +
                      HttpUtility.UrlEncode(_nowSelectedChargeType?.Name + (_nowSelectedType.Name ?? _nowSelectedType.Type.ToString()))
                      + "&account=" + Program.NowUser.Account;

            var html = WebClientExt.GetHtml(url);
            if (html.Trim().StartsWith("http"))
            {
                url = html.Trim();
            }
            else
            {
                url = CommonString.HostAccountUrl + "?t=" + ServerTime.DateTime.Ticks + "&desc=" +
                         HttpUtility.UrlEncode(string.Format("{0}{1},共{2}元", _nowSelectedChargeType?.Name,
                             _nowSelectedType.Type, _nowSelectedChargeType?.Price.ToString("F0") ?? ""));
            }
            wbPay.Navigate(url);
            CommonMethod.ShowHelpMsg("支付完成后，请发送截图及要开通的版本给客服，祝您使用愉快！", 10000);
            Task.Factory.StartNew(() =>
            {
                var strMsg = "";
                OcrHelper.SendReportInfo(string.Format("用户{0}正在开通{1}{2}，付款{3}元，请及时处理！", Program.NowUser?.Account, _nowSelectedChargeType?.Name,
                    _nowSelectedType.Type, _nowSelectedChargeType?.Price.ToString("F0") ?? ""), new List<UploadFileInfo>(), ref strMsg);
            });
        }

        private void FrmGoBuy_Load(object sender, EventArgs e)
        {
            // LoadInfo
            _lstUserTypes = OcrHelper.GetCanRegUserTypes();
            if (_lstUserTypes != null)
                //RadioButton rdoCheck = null;
                _lstUserTypes.ForEach(p =>
                {
                    var radioButton = new RadioButton
                    {
                        Text = p.Name ?? p.Type.ToString(),
                        Tag = p,
                        AutoSize = true,
                        Font = new Font(btnOpenVip.Font.FontFamily, 10),
                        TabStop = false
                    };
                    CommonMethod.SetStyle(radioButton, ControlStyles.Selectable, false);
                    radioButton.CheckedChanged += RadioButton_CheckedChanged;
                    if (Equals(p.Type, NextUserType?.Code)) radioButton.Checked = true;
                    //if (Program.NowUser?.UserType == p.Type)
                    //{
                    //    radioButton.Enabled = false;
                    //}
                    pnlUserType.Controls.Add(radioButton);
                });
            //if (rdoCheck == null && pnlUserType.Controls.Count > 0)
            //{
            //    rdoCheck = (pnlUserType.Controls[0] as RadioButton);
            //}
            //if (rdoCheck != null)
            //    rdoCheck.Checked = true;
            CommonMsg.ShowToWindow(this, new Point(136, 23));
            var url = CommonString.HostAccountUrl + "/desc.aspx?t=" + ServerTime.DateTime.Ticks;
            wbDesc.Navigate(url);
        }

        private void RadioButton_CheckedChanged(object sender, EventArgs e)
        {
            if (sender == null) return;
            _nowSelectedType = (sender as RadioButton)?.Tag as UserType;
            pnlPayType.Controls.Clear();
            _nowSelectedChargeType = null;
            _nowSelectedType?.UserChargeType?.ForEach(p =>
            {
                var radio = new RadioButton
                {
                    Text = p.Name,
                    AutoSize = true,
                    Tag = p,
                    Font = new Font(btnOpenVip.Font.FontFamily, 9),
                    TabStop = false
                };
                CommonMethod.SetStyle(radio, ControlStyles.Selectable, false);
                radio.CheckedChanged += rdoByYear_CheckedChanged;
                if (!string.IsNullOrEmpty(p.Tag))
                {
                    radio.TextImageRelation = TextImageRelation.TextBeforeImage;
                    try
                    {
                        radio.Image = Resources.ResourceManager.GetObject(p.Tag) as Bitmap;
                    }
                    catch
                    {
                    }
                }

                pnlPayType.Controls.Add(radio);
                if (p.IsDefault) radio.Checked = true;
            });
        }

        private void rdoByYear_CheckedChanged(object sender, EventArgs e)
        {
            _nowSelectedChargeType = (sender as RadioButton)?.Tag as ChargeViewToUser;
            btnOpenVip.Text = _nowSelectedChargeType?.Desc ?? "-";
        }
    }

    [Obfuscation]
    public class UserType
    {
        [Obfuscation] public int Type { get; set; }

        [Obfuscation] public string TypeName { get; set; }

        [Obfuscation] public string Name { get; set; }

        /// <summary>
        ///     是否显示其他处理结果
        /// </summary>
        [Obfuscation]
        public bool IsSetOtherResult { get; set; }

        /// <summary>
        ///     是否支持文本识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportTxt { get; set; } = true;

        ///// <summary>
        ///// 是否支持PDF识别
        ///// </summary>
        //public bool IsSupportPDF { get; set; }

        /// <summary>
        ///     是否支持图片翻译
        /// </summary>
        [Obfuscation]
        public bool IsSupportTranslate { get; set; }

        /// <summary>
        ///     是否支持图片文件识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportImageFile { get; set; }

        /// <summary>
        ///     是否支持文档翻译
        /// </summary>
        [Obfuscation]
        public bool IsSupportDocFile { get; set; }

        /// <summary>
        ///     能否批量处理文件
        /// </summary>
        [Obfuscation]
        public bool IsSupportBatch { get; set; }

        /// <summary>
        ///     是否支持竖排识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportVertical { get; set; }

        /// <summary>
        ///     是否支持数学公式识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportMath { get; set; }

        /// <summary>
        ///     是否支持表格识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportTable { get; set; }

        [Obfuscation] public List<ChargeViewToUser> UserChargeType { get; set; }

        [Obfuscation] public int MaxLoginCount { get; set; }

        public string ToDesc()
        {
            var sb = new StringBuilder();
            //sb.AppendFormat("【{0}】", Type.ToString());
            //sb.AppendLine("价格：");
            //sb.AppendFormat("{0}元/月，", PerPriceMonth.ToString("F0"));
            //sb.AppendFormat("{0}元/季，", (PerPriceMonth * 3 * QuarDiscount).ToString("F0"));
            //sb.AppendFormat("{0}元/年，", (PerPriceMonth * 12 * YearDiscount).ToString("F0"));
            sb.AppendLine("功能对比："); //√×
            if (IsSupportTxt) sb.AppendLine("截图：√");
            if (IsSupportImageFile) sb.AppendLine("图片：√");
            if (IsSupportVertical)
                sb.AppendLine("竖排：√");
            else
                sb.AppendLine("竖排：×");
            if (IsSupportMath)
                sb.AppendLine("公式：√");
            else
                sb.AppendLine("公式：×");
            if (IsSupportTranslate)
                sb.AppendLine("翻译：√");
            else
                sb.AppendLine("翻译：×");
            if (IsSupportTable)
                sb.AppendLine("表格：√");
            else
                sb.AppendLine("表格：×");
            if (IsSetOtherResult)
                sb.AppendLine("多结果：√");
            else
                sb.AppendLine("多结果：×");
            if (MaxLoginCount > 1)
                sb.AppendLine("多账号：√");
            else
                sb.AppendLine("多账号：×");
            //if (IsSupportPDF)
            //{
            //    sb.AppendLine("PDF：√");
            //}
            //else
            //{
            //    sb.AppendLine("PDF：×");
            //}
            if (IsSupportBatch)
                sb.AppendLine("批量：√");
            else
                sb.AppendLine("批量：×");
            if (IsSupportDocFile)
                sb.AppendLine("文档：√");
            else
                sb.AppendLine("文档：×");
            return sb.ToString();
        }
    }

    [Obfuscation]
    public class ChargeViewToUser
    {
        [Obfuscation] public string Name { get; set; }

        [Obfuscation] public string Desc { get; set; }

        [Obfuscation] public double Price { get; set; }

        [Obfuscation] public bool IsDefault { get; set; }

        [Obfuscation] public string Tag { get; set; }
    }

    [Obfuscation]
    public class UserTypeInfo
    {
        public string Name { get; set; }

        public int Code { get; set; }
    }
}