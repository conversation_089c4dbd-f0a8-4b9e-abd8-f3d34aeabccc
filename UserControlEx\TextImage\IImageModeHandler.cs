using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    /// <summary>
    /// 图像模式处理器接口
    /// 定义不同交互模式的统一处理规范
    /// </summary>
    public interface IImageModeHandler
    {
        /// <summary>
        /// 初始化处理器
        /// </summary>
        /// <param name="viewer">关联的查看器</param>
        void Initialize(MultiModeImageViewer viewer);

        /// <summary>
        /// 激活处理器（绑定事件）
        /// </summary>
        void Activate();

        /// <summary>
        /// 停用处理器（解绑事件）
        /// </summary>
        void Deactivate();
        
        /// <summary>
        /// 绑定数据
        /// </summary>
        /// <param name="image">图片</param>
        /// <param name="textCells">文字区域列表</param>
        void BindData(Image image, List<TextCellInfo> textCells);
        
        /// <summary>
        /// 处理鼠标按下事件
        /// </summary>
        /// <param name="e">鼠标事件参数</param>
        void HandleMouseDown(MouseEventArgs e);
        
        /// <summary>
        /// 处理鼠标移动事件
        /// </summary>
        /// <param name="e">鼠标事件参数</param>
        void HandleMouseMove(MouseEventArgs e);
        
        /// <summary>
        /// 处理鼠标释放事件
        /// </summary>
        /// <param name="e">鼠标事件参数</param>
        void HandleMouseUp(MouseEventArgs e);
        
        /// <summary>
        /// 处理鼠标离开事件
        /// </summary>
        /// <param name="e">事件参数</param>
        void HandleMouseLeave(EventArgs e);
        
        /// <summary>
        /// 处理绘制事件
        /// </summary>
        /// <param name="e">绘制事件参数</param>
        void HandlePaint(PaintEventArgs e);
        
        /// <summary>
        /// 处理缩放变化事件
        /// </summary>
        /// <param name="e">事件参数</param>
        void HandleZoomChanged(EventArgs e);
        
        /// <summary>
        /// 处理滚动事件
        /// </summary>
        /// <param name="e">滚动事件参数</param>
        void HandleScroll(ScrollEventArgs e);

        /// <summary>
        /// 处理鼠标滚轮事件
        /// </summary>
        /// <param name="e">鼠标事件参数</param>
        void HandleMouseWheel(MouseEventArgs e);
        
        /// <summary>
        /// 清理状态
        /// </summary>
        void ClearState();
        
        /// <summary>
        /// 释放资源
        /// </summary>
        void Dispose();
    }
}
