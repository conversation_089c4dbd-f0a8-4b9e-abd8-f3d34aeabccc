﻿using System.Drawing;
using System.Drawing.Drawing2D;

namespace OCRTools.ScreenCaptureLib
{
    public class RectangleRegionShape : BaseRegionShape
    {
        public override ShapeType ShapeType { get; } = ShapeType.矩形区域;

        public int CornerRadius { get; set; }

        public override void OnConfigLoad()
        {
            CornerRadius = AnnotationOptions.RegionCornerRadius;
        }

        public override void OnConfigSave()
        {
            AnnotationOptions.RegionCornerRadius = CornerRadius;
        }

        public override void OnShapePathRequested(GraphicsPath gp, Rectangle rect)
        {
            gp.AddRoundedRectangle(rect, CornerRadius);
        }
    }
}