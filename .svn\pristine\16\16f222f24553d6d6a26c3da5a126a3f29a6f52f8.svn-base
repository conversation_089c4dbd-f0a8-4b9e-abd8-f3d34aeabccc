// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class SpreadsheetPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = SpreadsheetPatternIdentifiers.Pattern;

        private SpreadsheetPattern(AutomationElement el, IUIAutomationSpreadsheetPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new SpreadsheetPattern(el, (IUIAutomationSpreadsheetPattern) pattern, cached);
        }
    }

    public class SpreadsheetItemPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = SpreadsheetItemPatternIdentifiers.Pattern;

        private SpreadsheetItemPattern(AutomationElement el, IUIAutomationSpreadsheetItemPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new SpreadsheetItemPattern(el, (IUIAutomationSpreadsheetItemPattern) pattern, cached);
        }
    }
}