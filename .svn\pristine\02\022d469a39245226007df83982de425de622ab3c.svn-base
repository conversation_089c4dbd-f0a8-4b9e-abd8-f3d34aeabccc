﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectGuid>{18E9B1BE-B80D-4830-8170-C8EF08AAC267}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x64</Platform>
    <OutputType>WinExe</OutputType>
    <AssemblyName>OCR Recognition Assistant</AssemblyName>
    <TargetFrameworkIdentifier>.NETFramework</TargetFrameworkIdentifier>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Platform)' == 'x64' ">
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <OutputPath>bin\Debug\</OutputPath>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <OutputPath>bin\Release\</OutputPath>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>
  <PropertyGroup>
    <RootNamespace>OCRTools</RootNamespace>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>7.1</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <OutputPath>bin\Debug\</OutputPath>
    <Prefer32Bit>false</Prefer32Bit>
    <Optimize>false</Optimize>
    <DefineConstants>DEBUG</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <OutputPath>bin\Release\</OutputPath>
    <Prefer32Bit>false</Prefer32Bit>
    <Optimize>true</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>7.1</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>ico.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>OCRTools.Program</StartupObject>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>
  <PropertyGroup>
    <DelaySign>false</DelaySign>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>false</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>CD0BDF2CB9186F0B0DE4379F02024E6AFF6B6262</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>OCRTools_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup />
  <PropertyGroup>
    <AutoGenerateBindingRedirects>false</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Interop.UIAutomationClient, Version=12.0.21213.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>RefDll\Interop.UIAutomationClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.mshtml, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="O2S.Components.PDFRender4NET">
      <HintPath>RefDll\O2S.Components.PDFRender4NET.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Design" />
    <Reference Include="System.Management" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BaseForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Colors\CMYK.cs" />
    <Compile Include="Colors\ColorBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Colors\ColorEventHandler.cs" />
    <Compile Include="Colors\ColorHelper.cs" />
    <Compile Include="Colors\ColorPicker.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Colors\ColorPickerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Colors\ColorPickerForm.designer.cs">
      <DependentUpon>ColorPickerForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Colors\ColorSlider.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Colors\ColorUserControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Colors\HSB.cs" />
    <Compile Include="Colors\MyColor.cs" />
    <Compile Include="Colors\RGBA.cs" />
    <Compile Include="Common\CommonCDNRequest.cs" />
    <Compile Include="Common\CommonConfig.cs" />
    <Compile Include="Common\CommonGuide.cs" />
    <Compile Include="Common\CommonGuideForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\CommonPlug.cs" />
    <Compile Include="Common\CommonAutomation.cs" />
    <Compile Include="Common\CommonTask.cs" />
    <Compile Include="Common\CommonTheme.cs" />
    <Compile Include="Common\CommonThemeManager.cs" />
    <Compile Include="Common\CommonToImage.cs" />
    <Compile Include="Common\CommonTranslate.cs" />
    <Compile Include="Common\CommonUser.cs" />
    <Compile Include="Common\CommonEnumAction.cs" />
    <Compile Include="Common\FileDropHandler.cs" />
    <Compile Include="Common\IDropTargetHelper.cs" />
    <Compile Include="Common\ImageCompress.cs" />
    <Compile Include="Common\ImageResourceManager.cs" />
    <Compile Include="Common\IpHelper.cs" />
    <Compile Include="Common\LoadingTypeHelper.cs" />
    <Compile Include="Common\SunTimes.cs" />
    <Compile Include="Forms\FrmBatchDect.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmBatchDect.Designer.cs">
      <DependentUpon>FrmBatchDect.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmBatchCompress.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmBatchCompress.Designer.cs">
      <DependentUpon>FrmBatchCompress.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmReport.Designer.cs">
      <DependentUpon>FrmReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmViewUrl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmViewUrl.Designer.cs">
      <DependentUpon>FrmViewUrl.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormViewImage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormViewImage.Designer.cs">
      <DependentUpon>FormViewImage.cs</DependentUpon>
    </Compile>
    <Compile Include="GZip\GZip.cs" />
    <Compile Include="GZip\GZipFileInfo.cs" />
    <Compile Include="GZip\GZipResult.cs" />
    <Compile Include="Common\SearchEngine.cs" />
    <Compile Include="HotKeyType.cs" />
    <Compile Include="ImageBox\ImageBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ImageBox\ImageBoxGridDisplayMode.cs" />
    <Compile Include="ImageBox\ImageBoxGridScale.cs" />
    <Compile Include="ImageBox\ImageBoxPanDirection.cs" />
    <Compile Include="ImageBox\ImageBoxPanMode.cs" />
    <Compile Include="ImageBox\ImageBoxPanStyle.cs" />
    <Compile Include="ImageBox\ImageBoxSizeMode.cs" />
    <Compile Include="ImageBox\ImageBoxZoomActions.cs" />
    <Compile Include="ImageBox\ScrollControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ImageBox\VirtualScrollableControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ImgUpload\BaiDuUpload.cs" />
    <Compile Include="ImgUpload\BaseImageUpload.cs" />
    <Compile Include="ImgUpload\TencentNewUpload.cs" />
    <Compile Include="ImgUpload\TencentUpload.cs" />
    <Compile Include="ImgUpload\WebResizerUpload.cs" />
    <Compile Include="ImgUpload\ImageHelper.cs" />
    <Compile Include="ImgUpload\Net126Upload.cs" />
    <Compile Include="ImgUpload\SouGouImageUpload.cs" />
    <Compile Include="ImgUpload\TinyPngUpload.cs" />
    <Compile Include="ImgUpload\ALiYunUpload.cs" />
    <Compile Include="ImgUpload\_360ImageUpload.cs" />
    <Compile Include="Language\LanguageHelper.cs" />
    <Compile Include="Language\SupportedLanguage.cs" />
    <Compile Include="Common\LocalOcrService.cs" />
    <Compile Include="NewForms\CommonSetting.cs" />
    <Compile Include="NewForms\FormPDFProcess.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NewForms\FormPDFProcess.Designer.cs">
      <DependentUpon>FormPDFProcess.cs</DependentUpon>
    </Compile>
    <Compile Include="NewForms\FormAreaCapture.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NewForms\FormAreaCapture.Designer.cs">
      <DependentUpon>FormAreaCapture.cs</DependentUpon>
    </Compile>
    <Compile Include="NewForms\FormSetting.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NewForms\FormSetting.Designer.cs">
      <DependentUpon>FormSetting.cs</DependentUpon>
    </Compile>
    <Compile Include="NQuant\Box.cs" />
    <Compile Include="NQuant\ColorData.cs" />
    <Compile Include="NQuant\CubeCut.cs" />
    <Compile Include="NQuant\Lookup.cs" />
    <Compile Include="NQuant\LookupData.cs" />
    <Compile Include="NQuant\Pixel.cs" />
    <Compile Include="NQuant\QuantizedPalette.cs" />
    <Compile Include="NQuant\WuQuantizer.cs" />
    <Compile Include="Common\ObjectCopy.cs" />
    <Compile Include="OCRTools\CenterForm.cs" />
    <Compile Include="OCRTools\ColorBgra.cs" />
    <Compile Include="OCRTools\ComboxbtnRenderer.cs" />
    <Compile Include="OCRTools\CursorEx.cs" />
    <Compile Include="OCRTools\ExtensionMethods.cs" />
    <Compile Include="OCRTools\ImageHelp.cs" />
    <Compile Include="OCRTools\MathHelpers.cs" />
    <Compile Include="OCRTools\RenderHelper.cs" />
    <Compile Include="OCRTools\SelectRectangleList.cs" />
    <Compile Include="OCRTools\UnsafeBitmap.cs" />
    <Compile Include="OCRTools\Vector.cs" />
    <Compile Include="OCRTools\Win32.cs" />
    <Compile Include="OtherExt\MetroFramework\AnimationManager.cs" />
    <Compile Include="OtherExt\MetroFramework\Forms\ImageAnimatorHelper.cs" />
    <Compile Include="OtherExt\UtfUnknown\CharsetDetector.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Analyzers\CharDistributionAnalyser.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Analyzers\Chinese\BIG5DistributionAnalyser.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Analyzers\Chinese\EUCTWDistributionAnalyser.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Analyzers\Chinese\GB18030DistributionAnalyser.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\BitPackage.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\InputState.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\MultiByte\Chinese\BIG5SMModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\MultiByte\Chinese\EUCTWSMModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\MultiByte\Chinese\GB18030_SMModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\MultiByte\Chinese\HZ_GB_2312_SMModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\MultiByte\Chinese\Iso_2022_CN_SMModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\MultiByte\UTF8_SMModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\StateMachineModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\CharsetProber.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\CodingStateMachine.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\EscCharsetProber.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\Latin1Prober.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\MBCSGroupProber.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\MultiByte\Chinese\Big5Prober.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\MultiByte\Chinese\EUCTWProber.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\MultiByte\Chinese\GB18030Prober.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\MultiByte\UTF8Prober.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\ProbingState.cs" />
    <Compile Include="OtherExt\UtfUnknown\DetectionDetail.cs" />
    <Compile Include="OtherExt\UtfUnknown\DetectionResult.cs" />
    <Compile Include="OtherExt\UIAComWrapper\AnnotationPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\Automation.cs" />
    <Compile Include="OtherExt\UIAComWrapper\AutomationElement.cs" />
    <Compile Include="OtherExt\UIAComWrapper\AutomationElementCollection.cs" />
    <Compile Include="OtherExt\UIAComWrapper\AutomationTypes.cs" />
    <Compile Include="OtherExt\UIAComWrapper\BasePattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\CacheRequest.cs" />
    <Compile Include="OtherExt\UIAComWrapper\Conditions.cs" />
    <Compile Include="OtherExt\UIAComWrapper\DockPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\DragPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\DropTargetPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\ExpandCollapsePattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\GridPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\InternalSchema.cs" />
    <Compile Include="OtherExt\UIAComWrapper\InternalTypes.cs" />
    <Compile Include="OtherExt\UIAComWrapper\InvokePattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\LegacyIAccessiblePattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\MultipleViewPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\ObjectModelPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\Point.cs" />
    <Compile Include="OtherExt\UIAComWrapper\ProviderInterfaces.cs" />
    <Compile Include="OtherExt\UIAComWrapper\Rect.cs" />
    <Compile Include="OtherExt\UIAComWrapper\ScrollPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\SelectionPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\Size.cs" />
    <Compile Include="OtherExt\UIAComWrapper\SpreadsheetPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\StylesPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\SynchronizedInput.cs" />
    <Compile Include="OtherExt\UIAComWrapper\TablePattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\TextChildPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\TextPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\TextRange.cs" />
    <Compile Include="OtherExt\UIAComWrapper\TextTypes.cs" />
    <Compile Include="OtherExt\UIAComWrapper\TogglePattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\TransformPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\Utility.cs" />
    <Compile Include="OtherExt\UIAComWrapper\ValuePattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\VirtualizedPatterns.cs" />
    <Compile Include="OtherExt\UIAComWrapper\WindowPattern.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="RecentControl\BlackStyleLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Common\CommonMsg.cs" />
    <Compile Include="Common\CommonResult.cs" />
    <Compile Include="Common\AutomationExtensions.cs" />
    <Compile Include="Common\CommonUpdate.cs" />
    <Compile Include="Common\Hook\HookManager.Callbacks.cs" />
    <Compile Include="Common\Hook\HookManager.cs" />
    <Compile Include="Common\Hook\HookManager.Structures.cs" />
    <Compile Include="Common\Hook\HookManager.Windows.cs" />
    <Compile Include="Common\Hook\MouseEventExtArgs.cs" />
    <Compile Include="Common\Input\HotKeyEntity.cs" />
    <Compile Include="Common\Input\InputHelpers.cs" />
    <Compile Include="Common\Input\InputManager.cs" />
    <Compile Include="Common\UserEntity.cs" />
    <Compile Include="Common\FlashWindowHelper.cs" />
    <Compile Include="Common\MemoryManager.cs" />
    <Compile Include="Common\NativeMethods.cs" />
    <Compile Include="Forms\FormOCR.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormOCR.designer.cs">
      <DependentUpon>FormOCR.cs</DependentUpon>
    </Compile>
    <Compile Include="RecentControl\FormRecent.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RecentControl\FormRecent.designer.cs">
      <DependentUpon>FormRecent.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormUpdate.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormUpdate.designer.cs">
      <DependentUpon>FormUpdate.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmBatchOCR.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmBatchOCR.Designer.cs">
      <DependentUpon>FrmBatchOCR.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmPicCompare.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmPicCompare.Designer.cs">
      <DependentUpon>FrmPicCompare.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\OcrProcessEntity.cs" />
    <Compile Include="Common\ExcelHelper.cs" />
    <Compile Include="Common\ImageProcessHelper.cs" />
    <Compile Include="Common\Log.cs" />
    <Compile Include="Common\ControlExtension.cs" />
    <Compile Include="Common\TableContentInfo.cs" />
    <Compile Include="Expection\ExceptionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Expection\UHEHandler.cs" />
    <Compile Include="Forms\FormTool.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormTool.designer.cs">
      <DependentUpon>FormTool.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmSearch.Designer.cs">
      <DependentUpon>FrmSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmForgetPwd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmForgetPwd.Designer.cs">
      <DependentUpon>FrmForgetPwd.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmGoBuy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmGoBuy.Designer.cs">
      <DependentUpon>FrmGoBuy.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmUserInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmUserInfo.Designer.cs">
      <DependentUpon>FrmUserInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmReg.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmReg.Designer.cs">
      <DependentUpon>FrmReg.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmLogin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmLogin.Designer.cs">
      <DependentUpon>FrmLogin.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NewForms\NotificationForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Ruler\Colors\CommonThemes.cs" />
    <Compile Include="Ruler\Colors\Theme.cs" />
    <Compile Include="Ruler\Forms\CalibrationForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Ruler\Forms\CalibrationForm.Designer.cs">
      <DependentUpon>CalibrationForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Ruler\Forms\RulerBaseForm.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Ruler\Forms\RulerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Ruler\Forms\RulerForm.Designer.cs">
      <DependentUpon>RulerForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Ruler\Forms\RulerFormResizeMode.cs" />
    <Compile Include="Ruler\Forms\RulerOverlayForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Ruler\Forms\SetSizeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Ruler\Forms\SetSizeForm.Designer.cs">
      <DependentUpon>SetSizeForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Ruler\MouseTracker.cs" />
    <Compile Include="Ruler\RulerMarker.cs" />
    <Compile Include="Ruler\RulerMarkerCollection.cs" />
    <Compile Include="Ruler\RulerPainter.cs" />
    <Compile Include="Ruler\Settings.cs" />
    <Compile Include="Ruler\Units\MeasuringUnit.cs" />
    <Compile Include="Ruler\Units\UnitConverters.cs" />
    <Compile Include="ScrollingCapture\ScrollingCapture.cs" />
    <Compile Include="ScrollingCapture\ScrollingCaptureManager.cs" />
    <Compile Include="ShadowForm\frmPasteImage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShadowForm\frmPasteImage.Designer.cs">
      <DependentUpon>frmPasteImage.cs</DependentUpon>
    </Compile>
    <Compile Include="ShadowForm\FormStyleAPI.cs" />
    <Compile Include="ShadowForm\ShadowForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShadowForm\ShadowForm.designer.cs">
      <DependentUpon>ShadowForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ShadowForm\ShadowFormSkin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShadowForm\ShadowFormSkin.designer.cs">
      <DependentUpon>ShadowFormSkin.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\OCRPoolProcess.cs" />
    <Compile Include="Common\ClipboardService.cs" />
    <Compile Include="Common\SpiltMode.cs" />
    <Compile Include="Common\UploadFileRequest.cs" />
    <Compile Include="FrmMain.designer.cs">
      <DependentUpon>FrmMain.cs</DependentUpon>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Components\MetroStyleManager.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Components\MetroToolTip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Controls\MetroButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Controls\MetroCheckBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Controls\MetroContextMenu.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Controls\MetroScrollBar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Controls\MetroTabControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Controls\MetroTabPage.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Design\MetroButtonDesigner.cs" />
    <Compile Include="OtherExt\MetroFramework\Design\MetroCheckBoxDesigner.cs" />
    <Compile Include="OtherExt\MetroFramework\Design\MetroScrollBarDesigner.cs" />
    <Compile Include="OtherExt\MetroFramework\Design\MetroStyleManagerDesigner.cs" />
    <Compile Include="OtherExt\MetroFramework\Design\MetroTabControlDesigner.cs" />
    <Compile Include="OtherExt\MetroFramework\Design\MetroTabPageDesigner.cs" />
    <Compile Include="OtherExt\MetroFramework\Drawing\MetroImage.cs" />
    <Compile Include="OtherExt\MetroFramework\Drawing\MetroPaint.cs" />
    <Compile Include="OtherExt\MetroFramework\Forms\MetroForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Forms\MetroFormShadowType.cs" />
    <Compile Include="OtherExt\MetroFramework\Interfaces\IMetroComponent.cs" />
    <Compile Include="OtherExt\MetroFramework\Interfaces\IMetroControl.cs" />
    <Compile Include="OtherExt\MetroFramework\Interfaces\IMetroForm.cs" />
    <Compile Include="OtherExt\MetroFramework\MetroBrushes.cs" />
    <Compile Include="OtherExt\MetroFramework\MetroColors.cs" />
    <Compile Include="OtherExt\MetroFramework\MetroColorStyle.cs" />
    <Compile Include="OtherExt\MetroFramework\MetroFonts.cs" />
    <Compile Include="OtherExt\MetroFramework\MetroPens.cs" />
    <Compile Include="OtherExt\MetroFramework\MetroThemeStyle.cs" />
    <Compile Include="OtherExt\MetroFramework\Native\DwmApi.cs" />
    <Compile Include="OtherExt\MetroFramework\Native\WinApi.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="OCRTools\StaticValue.cs" />
    <Compile Include="OCRTools\WindowInfo.cs" />
    <Compile Include="Common\HotKeyHelper.cs" />
    <Compile Include="Common\KillProcessHelper.cs" />
    <Compile Include="Common\BoxUtil.cs" />
    <Compile Include="Common\CommonEncryptHelper.cs" />
    <Compile Include="Common\CommonMethod.cs" />
    <Compile Include="Common\CommonString.cs" />
    <Compile Include="Common\DnsHelper.cs" />
    <Compile Include="Common\IniHelper.cs" />
    <Compile Include="Common\OcrContent.cs" />
    <Compile Include="Common\ServerTime.cs" />
    <Compile Include="Common\SNtpClient.cs" />
    <Compile Include="Common\StringExtension.cs" />
    <Compile Include="Common\WebClientExt.cs" />
    <Compile Include="Common\OcrHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Common\TimerTaskService.cs" />
    <Compile Include="RecentControl\TaskRoundedCornerPanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RecentControl\ThumbnailSizeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RecentControl\ThumbnailSizeForm.designer.cs">
      <DependentUpon>ThumbnailSizeForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ShareX\Animations\BaseAnimation.cs" />
    <Compile Include="ShareX\Animations\OpacityAnimation.cs" />
    <Compile Include="ShareX\Animations\RectangleAnimation.cs" />
    <Compile Include="ShareX\Animations\TextAnimation.cs" />
    <Compile Include="ShareX\Controls\LabeledNumericUpDown.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ShareX\Controls\LabeledNumericUpDown.Designer.cs">
      <DependentUpon>LabeledNumericUpDown.cs</DependentUpon>
    </Compile>
    <Compile Include="ShareX\Controls\LineShapeComboBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ShareX\Controls\LineShapeComboBox.Designer.cs">
      <DependentUpon>LineShapeComboBox.cs</DependentUpon>
    </Compile>
    <Compile Include="ShareX\Controls\ToolStripLabeledComboBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ShareX\Controls\ToolStripRadioButtonMenuItem.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ShareX\Enums.cs" />
    <Compile Include="ShareX\Forms\RegionCaptureForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShareX\Forms\TextDrawingInputBox.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShareX\Forms\TextDrawingInputBox.Designer.cs">
      <DependentUpon>TextDrawingInputBox.cs</DependentUpon>
    </Compile>
    <Compile Include="ShareX\GraphicsQualityManager.cs" />
    <Compile Include="ShareX\RegionCaptureOptions.cs" />
    <Compile Include="ShareX\RegionHelpers\ImageEditorControl.cs" />
    <Compile Include="ShareX\RegionHelpers\InputManager.cs" />
    <Compile Include="ShareX\RegionHelpers\MouseState.cs" />
    <Compile Include="ShareX\RegionHelpers\ResizeNode.cs" />
    <Compile Include="ShareX\RegionHelpers\SnapSize.cs" />
    <Compile Include="ShareX\Screenshot.cs" />
    <Compile Include="ShareX\Shapes\AnnotationOptions.cs" />
    <Compile Include="ShareX\Shapes\BaseShape.cs" />
    <Compile Include="ShareX\Shapes\Drawing\ArrowDrawingShape.cs" />
    <Compile Include="ShareX\Shapes\Drawing\BaseDrawingShape.cs" />
    <Compile Include="ShareX\Shapes\Drawing\EllipseDrawingShape.cs" />
    <Compile Include="ShareX\Shapes\Drawing\FreehandDrawingShape.cs" />
    <Compile Include="ShareX\Shapes\Drawing\LineDrawingShape.cs" />
    <Compile Include="ShareX\Shapes\Drawing\MagnifyDrawingShape.cs" />
    <Compile Include="ShareX\Shapes\Drawing\RectangleDrawingShape.cs" />
    <Compile Include="ShareX\Shapes\Drawing\SmartEraserDrawingShape.cs" />
    <Compile Include="ShareX\Shapes\Drawing\SpeechBalloonDrawingShape.cs" />
    <Compile Include="ShareX\Shapes\Drawing\StepDrawingShape.cs" />
    <Compile Include="ShareX\Shapes\Drawing\TextDrawingShape.cs" />
    <Compile Include="ShareX\Shapes\Drawing\TextOutlineDrawingShape.cs" />
    <Compile Include="ShareX\Shapes\Effect\BaseEffectShape.cs" />
    <Compile Include="ShareX\Shapes\Effect\HighlightEffectShape.cs" />
    <Compile Include="ShareX\Shapes\Effect\PixelateEffectShape.cs" />
    <Compile Include="ShareX\Shapes\Region\BaseRegionShape.cs" />
    <Compile Include="ShareX\Shapes\Region\EllipseRegionShape.cs" />
    <Compile Include="ShareX\Shapes\Region\FreehandRegionShape.cs" />
    <Compile Include="ShareX\Shapes\Region\RectangleRegionShape.cs" />
    <Compile Include="ShareX\Shapes\ShapeManager.cs" />
    <Compile Include="ShareX\Shapes\ShapeManagerMenu.cs" />
    <Compile Include="ShareX\Shapes\TextDrawingOptions.cs" />
    <Compile Include="ShareX\Shapes\Tool\BaseTool.cs" />
    <Compile Include="Common\ShortcutHelpers.cs" />
    <Compile Include="UserControlEx\FastBitmap.cs" />
    <Compile Include="UserControlEx\GraphicsPathHelper.cs" />
    <Compile Include="UserControlEx\MyPictureBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UserControlEx\MyPictureBox.designer.cs">
      <DependentUpon>MyPictureBox.cs</DependentUpon>
    </Compile>
    <Compile Include="UserControlEx\RichTextBoxEx.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RecentControl\TaskThumbnailPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="RecentControl\TaskThumbnailPanel.designer.cs">
      <DependentUpon>TaskThumbnailPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="RecentControl\TaskThumbnailView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="RecentControl\TaskThumbnailView.designer.cs">
      <DependentUpon>TaskThumbnailView.cs</DependentUpon>
    </Compile>
    <Compile Include="UserControlEx\ScrollingText.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UserControlEx\SkinButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UserControlEx\SkinTools.cs" />
    <Compile Include="UserControlEx\RoundStyle.cs" />
    <Compile Include="UserControlEx\TextImage\BaseImageModeHandler.cs" />
    <Compile Include="UserControlEx\TextImage\MultiModeImageViewer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UserControlEx\TextImage\IImageModeHandler.cs" />
    <Compile Include="UserControlEx\TextImage\DualModeImageViewer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UserControlEx\TextImage\ImageTextModeHandler.cs" />
    <Compile Include="UserControlEx\TextImage\DocumentModeHandler.cs" />
    <Compile Include="UserControlEx\TextPositionMapper.cs" />
    <Compile Include="UserControlEx\ToolStripEx.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UserControlEx\ToolStripLabeledNumericUpDown.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UserControlEx\ToolStripCheckBoxControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UserControlEx\DataGridViewEx.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UserControlEx\ucContent.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UserControlEx\ucContent.Designer.cs">
      <DependentUpon>ucContent.cs</DependentUpon>
    </Compile>
    <Compile Include="UserControlEx\ucLoading.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UserControlEx\ucLoading.Designer.cs">
      <DependentUpon>ucLoading.cs</DependentUpon>
    </Compile>
    <Compile Include="WebBroswerEx\ApplicationWebManager.cs" />
    <Compile Include="WebBroswerEx\IWebLoader.cs" />
    <Compile Include="WebBroswerEx\LoadingIndicator.cs" />
    <Compile Include="WebBroswerEx\SecurityManagerCOM.cs" />
    <Compile Include="WebBroswerEx\SecurityManagerHelper.cs" />
    <Compile Include="WebBroswerEx\SmartWebControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="WebBroswerEx\WebBrowserLoader.cs" />
    <Compile Include="WebBroswerEx\WebView2DynamicManager.cs" />
    <Compile Include="WebBroswerEx\WebView2EnvironmentManager.cs" />
    <Compile Include="WebBroswerEx\WebView2Loader.cs" />
    <Compile Include="UserControlEx\YButton.cs">
      <SubType>Component</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Colors\ColorPickerForm.resx">
      <DependentUpon>ColorPickerForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Expection\ExceptionForm.resx">
      <DependentUpon>ExceptionForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormOCR.resx">
      <DependentUpon>FormOCR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormTool.resx">
      <DependentUpon>FormTool.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmBatchDect.resx">
      <DependentUpon>FrmBatchDect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmBatchCompress.resx">
      <DependentUpon>FrmBatchCompress.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmReport.resx">
      <DependentUpon>FrmReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmViewUrl.resx">
      <DependentUpon>FrmViewUrl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormViewImage.resx">
      <DependentUpon>FormViewImage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NewForms\FormPDFProcess.resx">
      <DependentUpon>FormPDFProcess.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NewForms\FormAreaCapture.resx">
      <DependentUpon>FormAreaCapture.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NewForms\FormSetting.resx">
      <DependentUpon>FormSetting.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RecentControl\FormRecent.resx">
      <DependentUpon>FormRecent.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormUpdate.resx">
      <DependentUpon>FormUpdate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmBatchOCR.resx">
      <DependentUpon>FrmBatchOCR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmPicCompare.resx">
      <DependentUpon>FrmPicCompare.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmSearch.resx">
      <DependentUpon>FrmSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmForgetPwd.resx">
      <DependentUpon>FrmForgetPwd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmGoBuy.resx">
      <DependentUpon>FrmGoBuy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmUserInfo.resx">
      <DependentUpon>FrmUserInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmReg.resx">
      <DependentUpon>FrmReg.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmLogin.resx">
      <DependentUpon>FrmLogin.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmMain.resx">
      <DependentUpon>FrmMain.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Ruler\Forms\CalibrationForm.resx">
      <DependentUpon>CalibrationForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Ruler\Forms\RulerForm.resx">
      <DependentUpon>RulerForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Ruler\Forms\SetSizeForm.resx">
      <DependentUpon>SetSizeForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ShadowForm\frmPasteImage.resx">
      <DependentUpon>frmPasteImage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ShadowForm\ShadowForm.resx">
      <DependentUpon>ShadowForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ShadowForm\ShadowFormSkin.resx">
      <DependentUpon>ShadowFormSkin.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="RecentControl\ThumbnailSizeForm.resx">
      <DependentUpon>ThumbnailSizeForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RecentControl\TaskThumbnailPanel.resx">
      <DependentUpon>TaskThumbnailPanel.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="RecentControl\TaskThumbnailView.resx">
      <DependentUpon>TaskThumbnailView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ShareX\Forms\TextDrawingInputBox.resx">
      <DependentUpon>TextDrawingInputBox.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="UserControlEx\ucContent.resx">
      <DependentUpon>ucContent.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UserControlEx\ucLoading.resx">
      <DependentUpon>ucLoading.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="app.manifest">
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="ico.ico" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>