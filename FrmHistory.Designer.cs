﻿namespace OCRTools
{
    partial class FrmHistory
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.cmsTaskInfo = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsmiOpen = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiOpenURL = new System.Windows.Forms.ToolStripMenuItem();
            this.tssOpen1 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmiOpenFile = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiOpenFolder = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiCopy = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiCopyURL = new System.Windows.Forms.ToolStripMenuItem();
            this.tssCopy1 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmiCopyFile = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiCopyImage = new System.Windows.Forms.ToolStripMenuItem();
            this.tssCopy3 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmiCopyFilePath = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiCopyFileName = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiCopyFileNameWithExtension = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiCopyFolder = new System.Windows.Forms.ToolStripMenuItem();
            this.tssCopy6 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmiDeleteSelectedItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiDeleteSelectedFile = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiClearList = new System.Windows.Forms.ToolStripMenuItem();
            this.tssUploadInfo1 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmiThumbnailTitle = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiThumbnailTitleShow = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiThumbnailTitleHide = new System.Windows.Forms.ToolStripMenuItem();
            this.tssThumbnailTitle = new System.Windows.Forms.ToolStripSeparator();
            this.tsmiThumbnailTitleTop = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiThumbnailTitleBottom = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiThumbnailSize = new System.Windows.Forms.ToolStripMenuItem();
            this.ucTaskThumbnailView = new OCRTools.TaskThumbnailView();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.cmsTaskInfo.SuspendLayout();
            this.SuspendLayout();
            // 
            // cmsTaskInfo
            // 
            this.cmsTaskInfo.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiOpen,
            this.tsmiCopy,
            this.toolStripSeparator1,
            this.tsmiDeleteSelectedItem,
            this.tsmiDeleteSelectedFile,
            this.tsmiClearList,
            this.tssUploadInfo1,
            this.tsmiThumbnailTitle,
            this.tsmiThumbnailSize});
            this.cmsTaskInfo.Name = "cmsHistory";
            this.cmsTaskInfo.Size = new System.Drawing.Size(219, 192);
            this.cmsTaskInfo.Opening += new System.ComponentModel.CancelEventHandler(this.cmsTaskInfo_Opening);
            // 
            // tsmiOpen
            // 
            this.tsmiOpen.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiOpenFile,
            this.tsmiOpenFolder,
            this.tssOpen1,
            this.tsmiOpenURL});
            this.tsmiOpen.Name = "tsmiOpen";
            this.tsmiOpen.Size = new System.Drawing.Size(218, 22);
            this.tsmiOpen.Text = "打开";
            // 
            // tsmiOpenURL
            // 
            this.tsmiOpenURL.Name = "tsmiOpenURL";
            this.tsmiOpenURL.ShortcutKeyDisplayString = "Enter";
            this.tsmiOpenURL.Size = new System.Drawing.Size(184, 22);
            this.tsmiOpenURL.Text = "URL";
            this.tsmiOpenURL.Click += new System.EventHandler(this.tsmiOpenURL_Click);
            // 
            // tssOpen1
            // 
            this.tssOpen1.Name = "tssOpen1";
            this.tssOpen1.Size = new System.Drawing.Size(182, 6);
            // 
            // tsmiOpenFile
            // 
            this.tsmiOpenFile.Name = "tsmiOpenFile";
            this.tsmiOpenFile.ShortcutKeyDisplayString = "Ctrl+Enter";
            this.tsmiOpenFile.Size = new System.Drawing.Size(184, 22);
            this.tsmiOpenFile.Text = "文件";
            this.tsmiOpenFile.Click += new System.EventHandler(this.tsmiOpenFile_Click);
            // 
            // tsmiOpenFolder
            // 
            this.tsmiOpenFolder.Name = "tsmiOpenFolder";
            this.tsmiOpenFolder.ShortcutKeyDisplayString = "Shift+Enter";
            this.tsmiOpenFolder.Size = new System.Drawing.Size(184, 22);
            this.tsmiOpenFolder.Text = "文件夹";
            this.tsmiOpenFolder.Click += new System.EventHandler(this.tsmiOpenFolder_Click);
            // 
            // tsmiCopy
            // 
            this.tsmiCopy.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiCopyFile,
            this.tsmiCopyImage,
            this.tssCopy1,
            this.tsmiCopyURL,
            this.tssCopy3,
            this.tsmiCopyFilePath,
            this.tsmiCopyFileName,
            this.tsmiCopyFileNameWithExtension,
            this.tsmiCopyFolder,
            this.tssCopy6});
            this.tsmiCopy.Name = "tsmiCopy";
            this.tsmiCopy.Size = new System.Drawing.Size(218, 22);
            this.tsmiCopy.Text = "复制";
            // 
            // tsmiCopyURL
            // 
            this.tsmiCopyURL.Name = "tsmiCopyURL";
            this.tsmiCopyURL.ShortcutKeyDisplayString = "Ctrl+C";
            this.tsmiCopyURL.Size = new System.Drawing.Size(203, 22);
            this.tsmiCopyURL.Text = "URL";
            this.tsmiCopyURL.Click += new System.EventHandler(this.tsmiCopyURL_Click);
            // 
            // tssCopy1
            // 
            this.tssCopy1.Name = "tssCopy1";
            this.tssCopy1.Size = new System.Drawing.Size(214, 6);
            // 
            // tsmiCopyFile
            // 
            this.tsmiCopyFile.Name = "tsmiCopyFile";
            this.tsmiCopyFile.ShortcutKeyDisplayString = "Shift+C";
            this.tsmiCopyFile.Size = new System.Drawing.Size(203, 22);
            this.tsmiCopyFile.Text = "文件";
            this.tsmiCopyFile.Click += new System.EventHandler(this.tsmiCopyFile_Click);
            // 
            // tsmiCopyImage
            // 
            this.tsmiCopyImage.Name = "tsmiCopyImage";
            this.tsmiCopyImage.ShortcutKeyDisplayString = "Alt+C";
            this.tsmiCopyImage.Size = new System.Drawing.Size(203, 22);
            this.tsmiCopyImage.Text = "图像";
            this.tsmiCopyImage.Click += new System.EventHandler(this.tsmiCopyImage_Click);
            // 
            // tssCopy3
            // 
            this.tssCopy3.Name = "tssCopy3";
            this.tssCopy3.Size = new System.Drawing.Size(214, 6);
            // 
            // tsmiCopyFilePath
            // 
            this.tsmiCopyFilePath.Name = "tsmiCopyFilePath";
            this.tsmiCopyFilePath.ShortcutKeyDisplayString = "Ctrl+Shift+C";
            this.tsmiCopyFilePath.Size = new System.Drawing.Size(203, 22);
            this.tsmiCopyFilePath.Text = "文件路径";
            this.tsmiCopyFilePath.Click += new System.EventHandler(this.tsmiCopyFilePath_Click);
            // 
            // tsmiCopyFileName
            // 
            this.tsmiCopyFileName.Name = "tsmiCopyFileName";
            this.tsmiCopyFileName.Size = new System.Drawing.Size(203, 22);
            this.tsmiCopyFileName.Text = "文件名称";
            this.tsmiCopyFileName.Click += new System.EventHandler(this.tsmiCopyFileName_Click);
            // 
            // tsmiCopyFileNameWithExtension
            // 
            this.tsmiCopyFileNameWithExtension.Name = "tsmiCopyFileNameWithExtension";
            this.tsmiCopyFileNameWithExtension.Size = new System.Drawing.Size(203, 22);
            this.tsmiCopyFileNameWithExtension.Text = "文件名+拓展名";
            this.tsmiCopyFileNameWithExtension.Click += new System.EventHandler(this.tsmiCopyFileNameWithExtension_Click);
            // 
            // tsmiCopyFolder
            // 
            this.tsmiCopyFolder.Name = "tsmiCopyFolder";
            this.tsmiCopyFolder.Size = new System.Drawing.Size(203, 22);
            this.tsmiCopyFolder.Text = "文件夹";
            this.tsmiCopyFolder.Click += new System.EventHandler(this.tsmiCopyFolder_Click);
            // 
            // tssCopy6
            // 
            this.tssCopy6.Name = "tssCopy6";
            this.tssCopy6.Size = new System.Drawing.Size(214, 6);
            this.tssCopy6.Visible = false;
            // 
            // tsmiDeleteSelectedItem
            // 
            this.tsmiDeleteSelectedItem.Name = "tsmiDeleteSelectedItem";
            this.tsmiDeleteSelectedItem.ShortcutKeyDisplayString = "Del";
            this.tsmiDeleteSelectedItem.Size = new System.Drawing.Size(218, 22);
            this.tsmiDeleteSelectedItem.Text = "从列表中删除";
            this.tsmiDeleteSelectedItem.Click += new System.EventHandler(this.tsmiDeleteSelectedItem_Click);
            // 
            // tsmiDeleteSelectedFile
            // 
            this.tsmiDeleteSelectedFile.Name = "tsmiDeleteSelectedFile";
            this.tsmiDeleteSelectedFile.ShortcutKeyDisplayString = "Shift+Del";
            this.tsmiDeleteSelectedFile.Size = new System.Drawing.Size(218, 22);
            this.tsmiDeleteSelectedFile.Text = "删除本地文件...";
            this.tsmiDeleteSelectedFile.Click += new System.EventHandler(this.tsmiDeleteSelectedFile_Click);
            // 
            // tsmiClearList
            // 
            this.tsmiClearList.Name = "tsmiClearList";
            this.tsmiClearList.Size = new System.Drawing.Size(218, 22);
            this.tsmiClearList.Text = "清空列表";
            this.tsmiClearList.Click += new System.EventHandler(this.tsmiClearList_Click);
            // 
            // tssUploadInfo1
            // 
            this.tssUploadInfo1.Name = "tssUploadInfo1";
            this.tssUploadInfo1.Size = new System.Drawing.Size(215, 6);
            // 
            // tsmiThumbnailTitle
            // 
            this.tsmiThumbnailTitle.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiThumbnailTitleShow,
            this.tsmiThumbnailTitleHide,
            this.tssThumbnailTitle,
            this.tsmiThumbnailTitleTop,
            this.tsmiThumbnailTitleBottom});
            this.tsmiThumbnailTitle.Name = "tsmiThumbnailTitle";
            this.tsmiThumbnailTitle.Size = new System.Drawing.Size(218, 22);
            this.tsmiThumbnailTitle.Text = "缩略图标题";
            // 
            // tsmiThumbnailTitleShow
            // 
            this.tsmiThumbnailTitleShow.Checked = true;
            this.tsmiThumbnailTitleShow.CheckState = System.Windows.Forms.CheckState.Checked;
            this.tsmiThumbnailTitleShow.Name = "tsmiThumbnailTitleShow";
            this.tsmiThumbnailTitleShow.Size = new System.Drawing.Size(180, 22);
            this.tsmiThumbnailTitleShow.Tag = "ThumbnailTitleVisible";
            this.tsmiThumbnailTitleShow.Text = "显示";
            this.tsmiThumbnailTitleShow.Click += new System.EventHandler(this.tsmiThumbnailTitleShow_Click);
            // 
            // tsmiThumbnailTitleHide
            // 
            this.tsmiThumbnailTitleHide.Name = "tsmiThumbnailTitleHide";
            this.tsmiThumbnailTitleHide.Size = new System.Drawing.Size(180, 22);
            this.tsmiThumbnailTitleHide.Tag = "ThumbnailTitleVisible";
            this.tsmiThumbnailTitleHide.Text = "隐藏";
            this.tsmiThumbnailTitleHide.Click += new System.EventHandler(this.tsmiThumbnailTitleHide_Click);
            // 
            // tssThumbnailTitle
            // 
            this.tssThumbnailTitle.Name = "tssThumbnailTitle";
            this.tssThumbnailTitle.Size = new System.Drawing.Size(177, 6);
            // 
            // tsmiThumbnailTitleTop
            // 
            this.tsmiThumbnailTitleTop.Checked = true;
            this.tsmiThumbnailTitleTop.CheckState = System.Windows.Forms.CheckState.Checked;
            this.tsmiThumbnailTitleTop.Name = "tsmiThumbnailTitleTop";
            this.tsmiThumbnailTitleTop.Size = new System.Drawing.Size(180, 22);
            this.tsmiThumbnailTitleTop.Tag = "ThumbnailTitleLocation";
            this.tsmiThumbnailTitleTop.Text = "顶部";
            this.tsmiThumbnailTitleTop.Click += new System.EventHandler(this.tsmiThumbnailTitleTop_Click);
            // 
            // tsmiThumbnailTitleBottom
            // 
            this.tsmiThumbnailTitleBottom.Name = "tsmiThumbnailTitleBottom";
            this.tsmiThumbnailTitleBottom.Size = new System.Drawing.Size(180, 22);
            this.tsmiThumbnailTitleBottom.Tag = "ThumbnailTitleLocation";
            this.tsmiThumbnailTitleBottom.Text = "底部";
            this.tsmiThumbnailTitleBottom.Click += new System.EventHandler(this.tsmiThumbnailTitleBottom_Click);
            // 
            // tsmiThumbnailSize
            // 
            this.tsmiThumbnailSize.Name = "tsmiThumbnailSize";
            this.tsmiThumbnailSize.Size = new System.Drawing.Size(218, 22);
            this.tsmiThumbnailSize.Text = "缩略图大小...";
            this.tsmiThumbnailSize.Click += new System.EventHandler(this.tsmiThumbnailSize_Click);
            // 
            // ucTaskThumbnailView
            // 
            this.ucTaskThumbnailView.AutoScroll = true;
            this.ucTaskThumbnailView.BackColor = System.Drawing.SystemColors.Window;
            this.ucTaskThumbnailView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ucTaskThumbnailView.Location = new System.Drawing.Point(0, 0);
            this.ucTaskThumbnailView.Name = "ucTaskThumbnailView";
            this.ucTaskThumbnailView.Size = new System.Drawing.Size(800, 450);
            this.ucTaskThumbnailView.Style = MetroFramework.MetroColorStyle.Black;
            this.ucTaskThumbnailView.TabIndex = 0;
            this.ucTaskThumbnailView.ThumbnailSize = new System.Drawing.Size(200, 150);
            this.ucTaskThumbnailView.TitleLocation = OCRTools.ThumbnailTitleLocation.Top;
            this.ucTaskThumbnailView.TitleVisible = true;
            this.ucTaskThumbnailView.ContextMenuRequested += new OCRTools.TaskThumbnailView.TaskViewMouseEventHandler(this.ucTaskThumbnailView_ContextMenuRequested);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(215, 6);
            // 
            // FrmHistory
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.ucTaskThumbnailView);
            this.Name = "FrmHistory";
            this.Text = "FrmHistory";
            this.cmsTaskInfo.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private TaskThumbnailView ucTaskThumbnailView;
        private System.Windows.Forms.ContextMenuStrip cmsTaskInfo;
        private System.Windows.Forms.ToolStripMenuItem tsmiOpen;
        private System.Windows.Forms.ToolStripMenuItem tsmiOpenURL;
        private System.Windows.Forms.ToolStripSeparator tssOpen1;
        private System.Windows.Forms.ToolStripMenuItem tsmiOpenFile;
        private System.Windows.Forms.ToolStripMenuItem tsmiOpenFolder;
        private System.Windows.Forms.ToolStripMenuItem tsmiCopy;
        private System.Windows.Forms.ToolStripMenuItem tsmiCopyURL;
        private System.Windows.Forms.ToolStripSeparator tssCopy1;
        private System.Windows.Forms.ToolStripMenuItem tsmiCopyFile;
        private System.Windows.Forms.ToolStripMenuItem tsmiCopyImage;
        private System.Windows.Forms.ToolStripSeparator tssCopy3;
        private System.Windows.Forms.ToolStripMenuItem tsmiCopyFilePath;
        private System.Windows.Forms.ToolStripMenuItem tsmiCopyFileName;
        private System.Windows.Forms.ToolStripMenuItem tsmiCopyFileNameWithExtension;
        private System.Windows.Forms.ToolStripMenuItem tsmiCopyFolder;
        private System.Windows.Forms.ToolStripSeparator tssCopy6;
        private System.Windows.Forms.ToolStripMenuItem tsmiDeleteSelectedItem;
        private System.Windows.Forms.ToolStripMenuItem tsmiDeleteSelectedFile;
        private System.Windows.Forms.ToolStripMenuItem tsmiClearList;
        private System.Windows.Forms.ToolStripSeparator tssUploadInfo1;
        private System.Windows.Forms.ToolStripMenuItem tsmiThumbnailTitle;
        private System.Windows.Forms.ToolStripMenuItem tsmiThumbnailTitleShow;
        private System.Windows.Forms.ToolStripMenuItem tsmiThumbnailTitleHide;
        private System.Windows.Forms.ToolStripSeparator tssThumbnailTitle;
        private System.Windows.Forms.ToolStripMenuItem tsmiThumbnailTitleTop;
        private System.Windows.Forms.ToolStripMenuItem tsmiThumbnailTitleBottom;
        private System.Windows.Forms.ToolStripMenuItem tsmiThumbnailSize;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
    }
}