﻿using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows;
using System.Windows.Forms;

namespace OCRTools.ScreenCaptureLib
{
    public class Screenshot
    {
        public static bool CaptureClientArea { get; set; } = false;
        public static bool AutoHideTaskbar { get; set; } = false;

        public static string GetWorkAreaInfo()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine(string.Format("共{0}个显示器".CurrentText(), Screen.AllScreens.Length));
            sb.AppendLine("==============================");
            foreach (var p in Screen.AllScreens)
            {
                sb.AppendLine(string.Format("屏幕{0} 主显示器:{1}".CurrentText(), p.<PERSON>, p.Primary));
                sb.AppendLine(string.Format("起点:{0},{1} 分辨率:{2}*{3}".CurrentText()
                    , p.Bounds.X, p.Bounds.Y
                    , p.Bo<PERSON>.<PERSON>th, p.<PERSON>.Height));
                sb.AppendLine();
            }
            sb.AppendLine("==============================");
            sb.AppendLine("确认以上信息是否正确。".CurrentText());
            sb.AppendLine("您还可以补充信息，以帮助我们快速定位问题！".CurrentText());
            sb.AppendLine("如：图片黑屏/拉伸不正常".CurrentText());
            sb.AppendLine("双屏分辨率及缩放设置".CurrentText());
            return sb.ToString();
        }

        //public static Rectangle GetWorkAreaRectangle()
        //{
        //    //var screenBounds = NativeMethods.GetScreenBounds();
        //    //var realBounds = PrimaryScreen.GetAllRectangle();
        //    //PrimaryScreen.ScreenScalingFactor = realBounds.Width * 1.0f / screenBounds.Width;
        //    Rectangle rectDisplay = new Rectangle();
        //    return PrimaryScreen.GetAllRectangle(ref rectDisplay);
        //}

        public static Bitmap CaptureRectangle(Rectangle srcRect, Rectangle destRect)
        {
            return CaptureRectangleNative(srcRect, destRect);
        }

        public static Bitmap CaptureFullscreen(ref Rectangle srcRect, bool isNeedScal = false)
        {
            srcRect = SystemInformation.VirtualScreen;
            //Rectangle destRect = srcRect;
            //if (srcRect.IsEmpty)
            //    srcRect = PrimaryScreen.GetAllRectangle(ref destRect);
            ////destRect = NativeMethods.GetScreenBounds0Based();
            ////if (isNeedScal)
            ////{
            ////    destRect = GetDestRectangle(srcRect);
            ////    //destRect = NativeMethods.GetScreenBounds();
            ////}
            //var bitMap = CaptureRectangle(srcRect, destRect);
            //srcRect = destRect;
            var bitMap = CaptureRectangle(srcRect, srcRect);
            return bitMap;
        }

        [DllImport("user32.dll")]
        private static extern IntPtr GetAncestor(IntPtr hWnd, uint gaFlags);

        [DllImport("user32.dll")]
        public static extern bool PrintWindow(
         IntPtr hwnd,               // Window to copy,Handle to the window that will be copied. 
         IntPtr hdcBlt,             // HDC to print into,Handle to the device context. 
         UInt32 nFlags              // Optional flags,Specifies the drawing options. It can be one of the following values. 
         );

        const uint GA_ROOT = 2;
        public static IntPtr GetParentHandle(IntPtr windownHandle, ref Rectangle rect)
        {
            if (windownHandle.ToInt32() > 0)
            {
                windownHandle = GetAncestor(windownHandle, GA_ROOT);
                rect = windownHandle.GetRectangle(!CaptureClientArea);
            }
            return windownHandle;
        }

        public static Bitmap CaptureWindow(IntPtr handle, ref Rectangle rect)
        {
            if (handle.ToInt32() > 0)
            {
                rect = handle.GetRectangle(!CaptureClientArea);

                var isTaskbarHide = false;
                try
                {
                    if (AutoHideTaskbar) isTaskbarHide = NativeMethods.SetTaskbarVisibilityIfIntersect(false, rect);

                    return CaptureRectangle(rect, rect);
                }
                finally
                {
                    if (isTaskbarHide) NativeMethods.SetTaskbarVisibility(true);
                }
            }

            return null;
        }

        // 这个标志让API返回窗口的完整内容
        private const int PW_RENDERFULLCONTENT = 0x00000002;
        public static Bitmap CaptureHandle(IntPtr windownHandle, ref Rectangle rect)
        {
            if (windownHandle.ToInt32() > 0)
            {
                rect = windownHandle.GetRectangle(!CaptureClientArea);
                if (!rect.IsEmpty)
                {
                    // 创建一个与窗口大小匹配的位图
                    var bmp = new Bitmap(rect.Width, rect.Height, PixelFormat.Format32bppArgb);
                    // 创建一个GDI+图形对象来从位图中获取HDC
                    using (Graphics gfxBmp = Graphics.FromImage(bmp))
                    {
                        IntPtr hdcBitmap = gfxBmp.GetHdc();
                        try
                        {
                            // 使用PrintWindow来捕获窗口内容
                            if (PrintWindow(windownHandle, hdcBitmap, PW_RENDERFULLCONTENT))
                            {
                                return bmp;
                            }
                        }
                        finally
                        {
                            gfxBmp.ReleaseHdc(hdcBitmap);
                        }
                    }
                }
            }
            return null;
        }


        public static Bitmap CaptureActiveWindow(ref Rectangle rect)
        {
            var handle = NativeMethods.GetForegroundWindow();

            return CaptureWindow(handle, ref rect);
        }

        public static Bitmap CaptureActiveMonitor(ref Rectangle rect)
        {
            rect = NativeMethods.GetActiveScreenBounds();

            return CaptureRectangle(rect, rect);
        }

        private static Bitmap CaptureRectangleNative(Rectangle srcRect, Rectangle destRect)
        {
            if (srcRect.Size.IsEmpty) return null;

            var handle = NativeMethods.GetDesktopWindow();

            IntPtr hdcSrc = NativeMethods.GetWindowDC(handle);
            IntPtr hdcDest = NativeMethods.CreateCompatibleDC(hdcSrc);
            IntPtr hBitmap = NativeMethods.CreateCompatibleBitmap(hdcSrc, srcRect.Width, srcRect.Height);
            IntPtr hOld = NativeMethods.SelectObject(hdcDest, hBitmap);
            if (srcRect.Size.Equals(destRect.Size))
            {
                NativeMethods.BitBlt(hdcDest, 0, 0, srcRect.Width, srcRect.Height, hdcSrc, srcRect.X, srcRect.Y, CopyPixelOperation.SourceCopy | CopyPixelOperation.CaptureBlt);
            }
            else
            {
                NativeMethods.SetStretchBltMode(hdcDest, 0x04);
                NativeMethods.StretchBlt(hdcDest, destRect.X, destRect.Y, destRect.Width, destRect.Height
                    , hdcSrc, srcRect.X, srcRect.Y, srcRect.Width, srcRect.Height,
                    CopyPixelOperation.SourceCopy | CopyPixelOperation.CaptureBlt);
            }

            NativeMethods.SelectObject(hdcDest, hOld);
            NativeMethods.DeleteDC(hdcDest);
            NativeMethods.ReleaseDC(handle, hdcSrc);
            var bmp = Image.FromHbitmap(hBitmap);
            NativeMethods.DeleteObject(hBitmap);
            //if (!srcRect.Size.Equals(destRect.Size))
            //{
            //    bmp = ResizeImage(bmp, destRect.Width, destRect.Height);
            //}
            return bmp;
        }
    }
}