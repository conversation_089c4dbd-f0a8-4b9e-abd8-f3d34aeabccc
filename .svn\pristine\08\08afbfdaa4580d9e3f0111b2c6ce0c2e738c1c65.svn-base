﻿using OCRTools.Common;
using OCRTools.ImgUpload;
using System;
using System.Collections.Specialized;
using System.IO;

namespace ImageLib
{
    internal class SouGouImageUpload : BaseImageUpload
    {
        public SouGouImageUpload()
        {
            MaxSize = 1024 * 1024 * 1;
            Name = "SouGou";
        }

        public override string GetResult(byte[] content, bool isZip = false)
        {
            var result = GetFromSouGou(content);
            if (string.IsNullOrEmpty(result))
                result = GetFromSouGou2(content);
            if (!string.IsNullOrEmpty(result))
            {
                result += "?1.png";
            }
            return result;
        }

        private string GetFromSouGou2(byte[] content)
        {
            var result = "";
            var url = "https://proxy.jianzhuxuezhang.com/upload2";
            var file = new UploadFileInfo()
            {
                Name = "file",
                Filename = Guid.NewGuid().ToString().ToLower().Replace("-", "") + ".png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            try
            {
                result = UploadFileRequest.Post(url, new[] { file }, null, null);
            }
            catch { }
            return result;
        }

        private string GetFromSouGou(byte[] content)
        {
            var result = "";
            var guid = Guid.NewGuid().ToString().ToLower().Replace("-", "");
            var url = "https://pic.sogou.com/pic/upload_pic.jsp?uuid=" + Guid.NewGuid().ToString().ToLower();
            var file = new UploadFileInfo()
            {
                Name = "pic_path",
                Filename = guid + ".png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            var headers = new NameValueCollection() {
                    { "cookie", "SNUID="+guid.ToUpper()+"; SUV="+guid.ToUpper()+"; wuid="+guid+"; FUV="+guid } ,
                };
            try
            {
                result = UploadFileRequest.Post(url, new[] { file }, null, headers);
            }
            catch { }
            return result;
        }

    }
}
