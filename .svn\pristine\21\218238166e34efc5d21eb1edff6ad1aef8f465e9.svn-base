﻿using System.Windows.Automation;

namespace OCRTools.Common
{
    public static class AutomationExtensions
    {
        public static string GetText(this AutomationElement element, out bool isIn)
        {
            string result = null;
            isIn = false;
            try
            {
                //if (element.TryGetCurrentPattern(ValuePattern.Pattern, out object patternObj))
                //{
                //    isIn = true;
                //    var valuePattern = (ValuePattern)patternObj;
                //    result = valuePattern.Current.Value;
                //}
                //else
                if (element.TryGetCurrentPattern(TextPattern.Pattern, out var patternObj))
                {
                    isIn = true;
                    var textPattern = (TextPattern) patternObj;
                    if (textPattern.SupportedTextSelection != SupportedTextSelection.None)
                        foreach (var range in textPattern.GetSelection())
                        {
                            result = range.GetText(-1)?.Trim();
                            if (!string.IsNullOrEmpty(result)) break;
                        }

                    //var strTmp = textPattern.DocumentRange.GetText(-1).TrimEnd('\r'); // often there is an extra '\r' hanging off the end.
                    //strTmp = element.Current.Name;
                }

                //else if (element.TryGetCurrentPattern(SelectionItemPattern.Pattern, out patternObj))
                //{
                //    isIn = true;
                //    //var pattern = (SelectionItemPattern)patternObj;
                //    result = element.Current.Name;
                //}
                //else
                //{
                //    result = element.Current.Name;
                //}
            }
            catch
            {
                // ignored
            }

            return result;
        }
    }
}