using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools.WebBroswerEx
{
    /// <summary>
    /// WebBrowser加载器 - 基于IE内核的简单网页加载
    /// </summary>
    public class WebBrowserLoader : IWebLoader
    {
        private WebBrowser _webBrowser;
        private bool _isInitialized = false;
        private bool _isDisposed = false;

        public string LoaderName => "WebBrowser (IE)";
        public Control WebControl => _webBrowser;
        public string CurrentUrl => _webBrowser?.Url?.ToString() ?? string.Empty;
        public string DocumentTitle => _webBrowser?.DocumentTitle ?? string.Empty;
        public bool IsInitialized => _isInitialized;

        public event EventHandler<string> LoadingStarted;
        public event EventHandler<LoadCompletedEventArgs> LoadingCompleted;
        public event EventHandler<string> TitleChanged;

        public async Task<bool> InitializeAsync()
        {
            if (_isInitialized) return true;

            try
            {
                _webBrowser = new WebBrowser
                {
                    Dock = DockStyle.Fill,
                    ScriptErrorsSuppressed = true,
                    IsWebBrowserContextMenuEnabled = false
                };

                // 绑定基本事件
                _webBrowser.Navigating += OnNavigating;
                _webBrowser.DocumentCompleted += OnDocumentCompleted;
                _webBrowser.DocumentTitleChanged += OnDocumentTitleChanged;

                _isInitialized = true;
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> LoadAsync(string url, string postData = null)
        {
            if (!_isInitialized || _isDisposed) return false;

            try
            {
                url = PreProcessUrl(url);

                if (!string.IsNullOrEmpty(postData))
                {
                    // 标准WebBrowser的POST支持
                    var postDataBytes = System.Text.Encoding.UTF8.GetBytes(postData);
                    var headers = "Content-Type: application/x-www-form-urlencoded\r\n";
                    _webBrowser.Navigate(url, null, postDataBytes, headers);
                }
                else
                {
                    _webBrowser.Navigate(url);
                }

                return true;
            }
            catch (Exception ex)
            {
                LoadingCompleted?.Invoke(this, new LoadCompletedEventArgs(url, false, ex.Message));
                return false;
            }
        }

        private void OnNavigating(object sender, WebBrowserNavigatingEventArgs e)
        {
            if (e.Url.ToString().Contains("file/view.html"))
            {
                e.Cancel = true;
                CommonMethod.OpenUrl(e.Url.ToString());
                return;
            }
            LoadingStarted?.Invoke(this, e.Url?.ToString() ?? string.Empty);
        }

        private void OnDocumentCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
            var url = e.Url?.ToString() ?? CurrentUrl;
            LoadingCompleted?.Invoke(this, new LoadCompletedEventArgs(url, true));
        }

        private void OnDocumentTitleChanged(object sender, EventArgs e)
        {
            TitleChanged?.Invoke(this, DocumentTitle);
        }

        public async Task<string> ExecuteScriptAsync(string script)
        {
            if (!_isInitialized || _isDisposed || _webBrowser?.Document == null || string.IsNullOrWhiteSpace(script))
            {
                return null;
            }

            try
            {
                if (_webBrowser.ReadyState != WebBrowserReadyState.Complete)
                    return null;

                return _webBrowser.Document.InvokeScript("eval", new object[] { script })?.ToString();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 等待页面加载完成
        /// </summary>
        /// <param name="timeoutMs">超时时间（毫秒）</param>
        /// <returns>是否加载完成</returns>
        public async Task<bool> WaitForDocumentReady(int timeoutMs = 10000)
        {
            if (!_isInitialized || _isDisposed || _webBrowser == null)
            {
                return false;
            }

            var startTime = DateTime.Now;

            while ((DateTime.Now - startTime).TotalMilliseconds < timeoutMs)
            {
                try
                {
                    if (_webBrowser.ReadyState == WebBrowserReadyState.Complete &&
                        _webBrowser.Document != null)
                    {
                        return true;
                    }
                }
                catch
                {
                    // 忽略检查过程中的异常
                }

                await Task.Delay(100);
            }

            return false;
        }

        /// <summary>
        /// 安全的ExecuteScriptAsync，会等待页面加载完成
        /// </summary>
        public async Task<string> SafeExecuteScriptAsync(string script, int waitTimeoutMs = 5000)
        {
            if (string.IsNullOrWhiteSpace(script))
            {
                return null;
            }

            // 等待页面加载完成
            if (!await WaitForDocumentReady(waitTimeoutMs))
            {
                System.Diagnostics.Debug.WriteLine("页面加载超时，无法执行脚本");
                return null;
            }

            // 执行脚本
            return await ExecuteScriptAsync(script);
        }

        private string PreProcessUrl(string url)
        {
            if (string.IsNullOrWhiteSpace(url))
                return "about:blank";

            try
            {
                return CommonMethod.PreProcessUrl(url);
            }
            catch
            {
                return url;
            }
        }

        public void Dispose()
        {
            if (!_isDisposed)
            {
                try
                {
                    _webBrowser?.Dispose();
                    _webBrowser = null;
                }
                catch
                {
                }
                finally
                {
                    _isDisposed = true;
                }
            }
        }
    }
}
