using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolStep : ToolObject
    {
        private DrawStep _drawStep;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.CurrentToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else if (drawArea.GraphicsList.Count < 99 && drawArea.StepNum < 99)
            {
                _drawStep = new DrawStep(e.X, e.Y, drawArea);
                AddNewObject(drawArea, _drawStep);
                var num = 0;
                foreach (var graphics in drawArea.GraphicsList.graphicsList)
                    if (graphics.NoteType == DrawToolType.Step)
                        num++;
                if (num == 1) drawArea.StepNum = 0;
                drawArea.StepNum++;
                var num2 = drawArea.NumberArry[drawArea.StepNum - 1];
                _drawStep.Text = num2.ToString();
            }
        }

        protected new void AddNewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.Add(o);
            drawArea.Capture = true;
            o.Selected = true;
            drawArea.Refresh();
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (_drawStep == null)
                    drawArea.ActiveTool = DrawToolType.Text;
                else
                    _drawStep.IsSelected = true;
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (_drawStep != null)
            {
                drawArea.upDownButtonEx.Text = _drawStep.Text;
                StaticValue.CurrentRectangle = _drawStep.Rectangle;
                if (!_drawStep.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var obj = _drawStep;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    _drawStep.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(_drawStep));
                }
            }
        }
    }
}