﻿using OCRTools;
using OCRTools.Common;
using OCRTools.ImgUpload;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Reflection;

namespace ImageLib
{
    /// <summary>
    /// PnnLAB
    /// </summary>
    internal class ALiYunUpload : BaseImageUpload
    {
        public ALiYunUpload()
        {
            Name = "ALiYun";
            MaxSize = (int)(1024 * 1024 * 20d);
        }

        private const string strFileNameSpilt = "origin_url\":\"";
        private const string strFileNameSpilt2 = "output_file\":\"";

        //https://www.hipdf.cn/image-compressor
        private static AliYunToken GetToken2()
        {
            var html = WebClientExt.GetHtml("https://www.hipdf.cn/middle/file/get-oss-policy?t=" + ServerTime.DateTime.Ticks);
            if (!string.IsNullOrEmpty(html) && html.Length > 2)
            {
                return html.DeserializeJson<AliYunTokenRoot>()?.data;
            }

            return null;
        }

        //https://www.hipdf.cn/image-compressor
        private static AliYunToken GetToken3()
        {
            var html = WebClientExt.GetHtml("https://web-api.hipdf.cn/middle/file/get-oss-policy?t=" + ServerTime.DateTime.Ticks);
            if (!string.IsNullOrEmpty(html) && html.Length > 2)
            {
                return html.DeserializeJson<AliYunTokenRoot>()?.data;
            }

            return null;
        }

        private const int maxTimeOut = 10000;
        private static List<string> lstUploadType = new List<string>() { "ALiYun1", "ALiYun2" };

        private static AliYunToken GetResultByType(TaskParam param)
        {
            AliYunToken token = null;
            switch (param.Param1)
            {
                case "ALiYun1":
                    token = GetToken3();
                    break;
                case "ALiYun2":
                    token = GetToken2();
                    break;
            }
            return token;
        }

        public override string GetResult(byte[] content, bool isZip = false)
        {
            var result = string.Empty;
            var lstParam = lstUploadType.Select(dns => new TaskParam() { Param1 = dns }).ToList();
            var token = CommonTask<AliYunToken>.GetFastestValidResult(lstParam, GetResultByType, 2, maxTimeOut, null);
            if (token == null || string.IsNullOrEmpty(token.accessid))
                return result;
            var file = new UploadFileInfo()
            {
                Name = "file",
                Filename = "1.png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            var filePath = token.dir + ServerTime.DateTime.ToDateStr("M-d") + "/" + Guid.NewGuid().ToString().ToLower().Replace("-", "") + ".png";
            var vaules = new NameValueCollection() {
                    { "ossAccessKeyId", token.accessid } ,
                    { "policy", token.policy } ,
                    { "signature", token.signature } ,
                    { "callback", token.callback } ,
                    { "key",filePath  }
                };
            try
            {
                var html = UploadFileRequest.Post(token.host, new[] { file
    }, vaules);
                if (html?.Contains(strFileNameSpilt) == true)
                {
                    result = CommonMethod.SubString(html, strFileNameSpilt, "\"").Replace("\\/", "/");
                }
                else if (html?.Contains(strFileNameSpilt2) == true)
                {
                    result = CommonMethod.SubString(html, strFileNameSpilt2, "\"").Replace("\\/", "/");
                }
                else if (html.Contains("\"Status\":\"Ok\""))
                {
                    result = filePath;
                }
                if (!string.IsNullOrEmpty(result))
                {
                    if (!result.StartsWith("http"))
                    {
                        result = token.host + "/" + result;
                    }
                }
            }
            catch { }
            return result;
        }

        [Obfuscation]
        public class AliYunTokenRoot
        {
            [Obfuscation]
            public AliYunToken data { get; set; }
        }

        [Obfuscation]
        public class AliYunToken
        {
            [Obfuscation]
            public string accessid { get; set; }

            [Obfuscation]
            public string host { get; set; }

            [Obfuscation]
            public string policy { get; set; }

            [Obfuscation]
            public string signature { get; set; }

            [Obfuscation]
            public string dir { get; set; }

            [Obfuscation]
            public string callback { get; set; }
        }
    }
}
