using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace OCRTools
{
    internal class UpDownButton : ListButton
    {
        public bool IsUp { get; set; }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            var graphics = e.Graphics;
            using (var brush = new SolidBrush(Color.Gray))
            {
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                var array = new Point[3];
                if (IsUp)
                {
                    array[0] = new Point(e.ClipRectangle.Width / 2, 1);
                    array[1] = new Point(e.ClipRectangle.Width / 3, e.ClipRectangle.Height - 3);
                    array[2] = new Point(e.ClipRectangle.Width * 2 / 3, e.ClipRectangle.Height - 3);
                }
                else
                {
                    array[0] = new Point(e.ClipRectangle.Width / 2, e.ClipRectangle.Height - 3);
                    array[1] = new Point(e.ClipRectangle.Width / 3, 2);
                    array[2] = new Point(e.ClipRectangle.Width * 2 / 3, 2);
                }

                graphics.FillPolygon(brush, array);
            }
        }
    }
}