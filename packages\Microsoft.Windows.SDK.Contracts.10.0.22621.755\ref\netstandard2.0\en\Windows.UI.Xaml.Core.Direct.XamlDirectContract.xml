﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Windows.UI.Xaml.Core.Direct.XamlDirectContract</name>
  </assembly>
  <members>
    <member name="T:Windows.UI.Xaml.Core.Direct.IXamlDirectObject">
      <summary>Represents the primary object type that participates in the XamlDirect set of APIs.</summary>
    </member>
    <member name="T:Windows.UI.Xaml.Core.Direct.XamlDirect">
      <summary>Represents the base class for all XamlDirect APIs. All of the XamlDirect APIs are instance methods off of this class.</summary>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.AddEventHandler(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlEventIndex,System.Object)">
      <summary>Adds the specified event handler for a specified event using XamlEventIndex, adding the handler to the handler collection on the current IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the current IXamlDirectObject.</param>
      <param name="eventIndex">An identifier for the event to be handled specified through XamlEventIndex enum.</param>
      <param name="handler">A reference to the specified handler implementation.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.AddEventHandler(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlEventIndex,System.Object,System.Boolean)">
      <summary>Adds the specified event handler for a specified event using XamlEventIndex, adding the handler to the handler collection on the current IXamlDirectObject. Specify *handledEventsToo* as true to have the provided handler be invoked even if the event is handled elsewhere.</summary>
      <param name="xamlDirectObject">A reference to the current IXamlDirectObject.</param>
      <param name="eventIndex">An identifier for the event to be handled specified through XamlEventIndex enum.</param>
      <param name="handler">A reference to the specified handler implementation.</param>
      <param name="handledEventsToo">**true** to register the handler such that it is invoked even when the routed event is marked handled in its event data.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.AddToCollection(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.IXamlDirectObject)">
      <summary>Adds a value to the specified IXamlDirectObject collection.</summary>
      <param name="xamlDirectObject">Refers to the specific IXamlDirectObject collection.</param>
      <param name="value">Refers to the IXamlDirectObject value to add to the collection.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.ClearCollection(Windows.UI.Xaml.Core.Direct.IXamlDirectObject)">
      <summary>Removes all items from the specified IXamlDirectObject collection.</summary>
      <param name="xamlDirectObject">Refers to the specific IXamlDirectObject collection.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.ClearProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Clears the value of the specified property on a given IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the property to be cleared from the list of supported properties in XamlPropertyIndex enum.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.CreateInstance(Windows.UI.Xaml.Core.Direct.XamlTypeIndex)">
      <summary>Creates an IXamlDirectObject instance of the type specified by the XamlTypeIndex parameter.</summary>
      <param name="typeIndex">Refers to the specific XAML type from the set of supported types listed in the XamlTypeIndex enum.</param>
      <returns>Returns the IXamlDirectObject instance.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetBooleanProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a _Boolean_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _Boolean_ property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>_Boolean_ value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetCollectionCount(Windows.UI.Xaml.Core.Direct.IXamlDirectObject)">
      <summary>Returns the count of items in the specified IXamlDirectObject collection.</summary>
      <param name="xamlDirectObject">Refers to the specific IXamlDirectObject collection.</param>
      <returns>Count of items.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetColorProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a Color property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the Color property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>Color value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetCornerRadiusProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a CornerRadius property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the CornerRadius property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>CornerRadius value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetDateTimeProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a _DateTimeOffset_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _DateTimeOffset_ property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>_DateTimeOffset_ value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetDefault">
      <summary>Returns the current XamlDirect instance.</summary>
      <returns>XamlDirect</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetDoubleProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a _Double_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _Double_ property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>_Double_ value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetDurationProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a _Duration_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _Duration_ property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>_Duration_ value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetEnumProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of an _Enum_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _Enum_ property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>_UInt32_ value of the specified property which can be cast to the desired _Enum_ type.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetGridLengthProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a GridLength property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the GridLength property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>GridLength value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetInt32Property(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of an _Int32_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _Int32_ property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>_Int32_ value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetMatrix3DProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a Matrix3D property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the Matrix3D property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>Matrix3D value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetMatrixProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a Matrix property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the Matrix property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>Matrix value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetObject(Windows.UI.Xaml.Core.Direct.IXamlDirectObject)">
      <summary>Gets (and creates if necessary) the instance as its full XAML type of the specified IXamlDirectObject.</summary>
      <param name="xamlDirectObject">Refers to the specific IXamlDirectObject.</param>
      <returns>Returns the instance of the full XAML type (typically, but not always, a DependencyObject ).</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetObjectProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of an _object_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _object_ property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>_object_ value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetPointProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a Point property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the Point property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>Point value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetRectProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a Rect  property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the Rect property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>Rect value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetSizeProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a Size property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the Size property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>Size value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetStringProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a _string_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _string_ property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>_string_ value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetThicknessProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a Thickness property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the Thickness property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>Thickness value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetTimeSpanProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of a _TimeSpan_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _TimeSpan_ property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>_TimeSpan_ value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetXamlDirectObject(System.Object)">
      <summary>Gets the instance as an IXamlDirectObject for a given XAML type.</summary>
      <param name="@object">
      </param>
      <returns>Returns the IXamlDirectObject instance.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetXamlDirectObjectFromCollectionAt(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,System.UInt32)">
      <summary>Returns the IXamlDirectObject item at the specified index from the specified IXamlDirectObject collection.</summary>
      <param name="xamlDirectObject">Refers to the specific IXamlDirectObject collection.</param>
      <param name="index">Refers to the index in the collection of the item to get.</param>
      <returns>Returns the IXamlDirectObject item at the specified index of the collection.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.GetXamlDirectObjectProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex)">
      <summary>Gets the value of an IXamlDirectObject property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the IXamlDirectObject property to get the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <returns>IXamlDirectObject value of the specified property.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.InsertIntoCollectionAt(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,System.UInt32,Windows.UI.Xaml.Core.Direct.IXamlDirectObject)">
      <summary>Inserts a value into the specified IXamlDirectObject collection at the specified index.</summary>
      <param name="xamlDirectObject">Refers to the specific IXamlDirectObject collection.</param>
      <param name="index">Refers to the index in the collection where the specified value must be inserted.</param>
      <param name="value">Refers to the IXamlDirectObject value to add to the collection.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.RemoveEventHandler(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlEventIndex,System.Object)">
      <summary>Removes the specified event handler from this IXamlDirectObject. Typically, the handler in question was added by XamlDirect.AddEventHandler.</summary>
      <param name="xamlDirectObject">A reference to the current IXamlDirectObject.</param>
      <param name="eventIndex">An identifier for the event to remove the handle for specified through XamlEventIndex enum.</param>
      <param name="handler">A reference to the specified handler implementation.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.RemoveFromCollection(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.IXamlDirectObject)">
      <summary>Tries to remove a value from the specified IXamlDirectObject collection.</summary>
      <param name="xamlDirectObject">Refers to the specific IXamlDirectObject collection.</param>
      <param name="value">Refers to the IXamlDirectObject value to remove from the collection.</param>
      <returns>**true** if value was present.</returns>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.RemoveFromCollectionAt(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,System.UInt32)">
      <summary>Tries to remove a value from the IXamlDirectObject collection at the specified index.</summary>
      <param name="xamlDirectObject">Refers to the specific IXamlDirectObject collection.</param>
      <param name="index">Refers to the index in the collection where the value must be removed.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetBooleanProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,System.Boolean)">
      <summary>Sets the value of a _Boolean_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _Boolean_ property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">_Boolean_ value to set for the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetColorProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,Windows.UI.Color)">
      <summary>Sets the value of a Color property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the Color property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">Color value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetCornerRadiusProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,Windows.UI.Xaml.CornerRadius)">
      <summary>Sets the value of a CornerRadius property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the CornerRadius property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">CornerRadius value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetDateTimeProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,Windows.Foundation.DateTime)">
      <summary>Sets the value of a _DateTimeOffset_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _DateTimeOffset_ property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">_DateTimeOffset_ value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetDoubleProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,System.Double)">
      <summary>Sets the value of a _Double_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _Double_ property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">_Double_ value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetDurationProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,Windows.UI.Xaml.Duration)">
      <summary>Sets the value of a _Duration_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _Duration_ property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">_Duration_ value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetEnumProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,System.UInt32)">
      <summary>Sets the value of an _Enum_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _Enum_ property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">Cast the desired _Enum_ value to a _UInt32_ to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetGridLengthProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,Windows.UI.Xaml.GridLength)">
      <summary>Sets the value of a GridLength property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the GridLength property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">GridLength value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetInt32Property(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,System.Int32)">
      <summary>Sets the value of an _Int32_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _Int32_ property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">_Int32_ value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetMatrix3DProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,Windows.UI.Xaml.Media.Media3D.Matrix3D)">
      <summary>Sets the value of a Matrix3D property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the Matrix3D property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">Matrix3D value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetMatrixProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,Windows.UI.Xaml.Media.Matrix)">
      <summary>Sets the value of a Matrix property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the Matrix property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">Matrix value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetObjectProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,System.Object)">
      <summary>Sets the value of an _object_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _object_ property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">_object_ value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetPointProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,Windows.Foundation.Point)">
      <summary>Sets the value of a Point property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the Point property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">Point value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetRectProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,Windows.Foundation.Rect)">
      <summary>Sets the value of a Rect property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the Rect property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">Rect value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetSizeProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,Windows.Foundation.Size)">
      <summary>Sets the value of a Size property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the Size property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">Size value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetStringProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,System.String)">
      <summary>Sets the value of a _string_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _string_ property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">_string_ value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetThicknessProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,Windows.UI.Xaml.Thickness)">
      <summary>Sets the value of a Thickness property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the Thickness property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">Thickness value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetTimeSpanProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,Windows.Foundation.TimeSpan)">
      <summary>Sets the value of a _TimeSpan_ property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the _TimeSpan_ property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">_TimeSpan_ value to set to the specified property.</param>
    </member>
    <member name="M:Windows.UI.Xaml.Core.Direct.XamlDirect.SetXamlDirectObjectProperty(Windows.UI.Xaml.Core.Direct.IXamlDirectObject,Windows.UI.Xaml.Core.Direct.XamlPropertyIndex,Windows.UI.Xaml.Core.Direct.IXamlDirectObject)">
      <summary>Sets the value of an IXamlDirectObject property for a specific IXamlDirectObject.</summary>
      <param name="xamlDirectObject">A reference to the specific IXamlDirectObject.</param>
      <param name="propertyIndex">A reference to the IXamlDirectObject property to set the value of from the list of supported properties in XamlPropertyIndex enum.</param>
      <param name="value">IXamlDirectObject value to set to the specified property.</param>
    </member>
    <member name="T:Windows.UI.Xaml.Core.Direct.XamlDirectContract">
      <summary>
      </summary>
    </member>
    <member name="T:Windows.UI.Xaml.Core.Direct.XamlEventIndex">
      <summary>Enum that lists all the supported events in XamlDirect.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.AppBar_Closed">
      <summary>The _Closed_ event for the AppBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.AppBar_Closing">
      <summary>The _Closing_ event for the AppBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.AppBar_Opened">
      <summary>The _Opened_ event for the AppBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.AppBar_Opening">
      <summary>The _Opening_ event for the AppBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.AutoSuggestBox_QuerySubmitted">
      <summary>The _QuerySubmitted_ event for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.AutoSuggestBox_SuggestionChosen">
      <summary>The _SuggestionChosen_ event for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.AutoSuggestBox_TextChanged">
      <summary>The _TextChanged_ event for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ButtonBase_Click">
      <summary>The _Click_ event for the ButtonBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.CalendarDatePicker_CalendarViewDayItemChanging">
      <summary>The _CalendarViewDayItemChanging_ event for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.CalendarDatePicker_Closed">
      <summary>The _Closed_ event for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.CalendarDatePicker_DateChanged">
      <summary>The _DateChanged_ event for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.CalendarDatePicker_Opened">
      <summary>The _Opened_ event for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.CalendarView_CalendarViewDayItemChanging">
      <summary>The _CalendarViewDayItemChanging_ event for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.CalendarView_SelectedDatesChanged">
      <summary>The _SelectedDatesChanged_ event for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.CarouselPanel_HorizontalSnapPointsChanged">
      <summary>The _HorizontalSnapPointsChanged_ event for the CarouselPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.CarouselPanel_VerticalSnapPointsChanged">
      <summary>The _VerticalSnapPointsChanged_ event for the CarouselPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ComboBox_DropDownClosed">
      <summary>The _DropDownClosed_ event for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ComboBox_DropDownOpened">
      <summary>The _DropDownOpened_ event for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.CommandBar_DynamicOverflowItemsChanging">
      <summary>The _DynamicOverflowItemsChanging_ event for the CommandBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ContentDialog_CloseButtonClick">
      <summary>The _CloseButtonClick_ event for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ContentDialog_Closed">
      <summary>The _Closed_ event for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ContentDialog_Closing">
      <summary>The _Closing_ event for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ContentDialog_Opened">
      <summary>The _Opened_ event for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ContentDialog_PrimaryButtonClick">
      <summary>The _PrimaryButtonClick_ event for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ContentDialog_SecondaryButtonClick">
      <summary>The _SecondaryButtonClick_ event for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.Control_FocusDisengaged">
      <summary>The _FocusDisengaged_ event for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.Control_FocusEngaged">
      <summary>The _FocusEngaged_ event for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.DatePicker_DateChanged">
      <summary>The _DateChanged_ event for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.DatePicker_SelectedDateChanged">
      <summary>The _SelectedDateChanged_ event for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.Frame_Navigated">
      <summary>The _Navigated_ event for the Frame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.Frame_Navigating">
      <summary>The _Navigating_ event for the Frame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.Frame_NavigationFailed">
      <summary>The _NavigationFailed_ event for the Frame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.Frame_NavigationStopped">
      <summary>The _NavigationStopped_ event for the Frame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.FrameworkElement_DataContextChanged">
      <summary>The _DataContextChanged_ event for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.FrameworkElement_LayoutUpdated">
      <summary>The _LayoutUpdated_ event for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.FrameworkElement_SizeChanged">
      <summary>The _SizeChanged_ event for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.Hub_SectionHeaderClick">
      <summary>The _SectionHeaderClick_ event for the Hub type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.Hub_SectionsInViewChanged">
      <summary>The _SectionsInViewChanged_ event for the Hub type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ItemsPresenter_HorizontalSnapPointsChanged">
      <summary>The _HorizontalSnapPointsChanged_ event for the ItemsPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ItemsPresenter_VerticalSnapPointsChanged">
      <summary>The _VerticalSnapPointsChanged_ event for the ItemsPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ListViewBase_ChoosingGroupHeaderContainer">
      <summary>The _ChoosingGroupHeaderContainer_ event for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ListViewBase_ChoosingItemContainer">
      <summary>The _ChoosingItemContainer_ event for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ListViewBase_ContainerContentChanging">
      <summary>The _ContainerContentChanging_ event for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ListViewBase_DragItemsCompleted">
      <summary>The _DragItemsCompleted_ event for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ListViewBase_DragItemsStarting">
      <summary>The _DragItemsStarting_ event for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ListViewBase_ItemClick">
      <summary>The _ItemClick_ event for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.MediaTransportControls_ThumbnailRequested">
      <summary>The _ThumbnailRequested_ event for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.MenuFlyoutItem_Click">
      <summary>The _Click_ event for the MenuFlyoutItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.OrientedVirtualizingPanel_HorizontalSnapPointsChanged">
      <summary>The _HorizontalSnapPointsChanged_ event for the OrientedVirtualizingPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.OrientedVirtualizingPanel_VerticalSnapPointsChanged">
      <summary>The _VerticalSnapPointsChanged_ event for the OrientedVirtualizingPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.RangeBase_ValueChanged">
      <summary>The _ValueChanged_ event for the RangeBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.RichEditBox_TextChanging">
      <summary>The _TextChanging_ event for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ScrollBar_Scroll">
      <summary>The _Scroll_ event for the ScrollBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ScrollViewer_AnchorRequested">
      <summary>The _AnchorRequested_ event for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ScrollViewer_DirectManipulationCompleted">
      <summary>The _DirectManipulationCompleted_ event for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ScrollViewer_DirectManipulationStarted">
      <summary>The _DirectManipulationStarted_ event for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ScrollViewer_ViewChanged">
      <summary>The _ViewChanged_ event for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ScrollViewer_ViewChanging">
      <summary>The _ViewChanging_ event for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.SearchBox_PrepareForFocusOnKeyboardInput">
      <summary>The _PrepareForFocusOnKeyboardInput_ event for the SearchBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.SearchBox_QueryChanged">
      <summary>The _QueryChanged_ event for the SearchBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.SearchBox_QuerySubmitted">
      <summary>The _QuerySubmitted_ event for the SearchBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.SearchBox_ResultSuggestionChosen">
      <summary>The _ResultSuggestionChosen_ event for the SearchBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.SearchBox_SuggestionsRequested">
      <summary>The _SuggestionsRequested_ event for the SearchBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.Selector_SelectionChanged">
      <summary>The _SelectionChanged_ event for the Selector type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.SemanticZoom_ViewChangeCompleted">
      <summary>The _ViewChangeCompleted_ event for the SemanticZoom type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.SemanticZoom_ViewChangeStarted">
      <summary>The _ViewChangeStarted_ event for the SemanticZoom type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.SettingsFlyout_BackClick">
      <summary>The _BackClick_ event for the SettingsFlyout type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.StackPanel_HorizontalSnapPointsChanged">
      <summary>The _HorizontalSnapPointsChanged_ event for the StackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.StackPanel_VerticalSnapPointsChanged">
      <summary>The _VerticalSnapPointsChanged_ event for the StackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.Thumb_DragCompleted">
      <summary>The _DragCompleted_ event for the Thumb type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.Thumb_DragDelta">
      <summary>The _DragDelta_ event for the Thumb type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.Thumb_DragStarted">
      <summary>The _DragStarted_ event for the Thumb type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.TimePicker_SelectedTimeChanged">
      <summary>The _SelectedTimeChanged_ event for the TimePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.TimePicker_TimeChanged">
      <summary>The _TimeChanged_ event for the TimePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ToggleButton_Checked">
      <summary>The _Checked_ event for the ToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ToggleButton_Indeterminate">
      <summary>The _Indeterminate_ event for the ToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ToggleButton_Unchecked">
      <summary>The _Unchecked_ event for the ToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ToggleSwitch_Toggled">
      <summary>The _Toggled_ event for the ToggleSwitch type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ToolTip_Closed">
      <summary>The _Closed_ event for the ToolTip type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.ToolTip_Opened">
      <summary>The _Opened_ event for the ToolTip type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_BringIntoViewRequested">
      <summary>The _BringIntoViewRequested_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_CharacterReceived">
      <summary>The _CharacterReceived_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_ContextCanceled">
      <summary>The _ContextCanceled_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_ContextRequested">
      <summary>The _ContextRequested_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_DoubleTapped">
      <summary>The _DoubleTapped_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_DragEnter">
      <summary>The _DragEnter_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_DragLeave">
      <summary>The _DragLeave_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_DragOver">
      <summary>The _DragOver_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_DragStarting">
      <summary>The _DragStarting_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_Drop">
      <summary>The _Drop_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_DropCompleted">
      <summary>The _DropCompleted_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_GettingFocus">
      <summary>The _GettingFocus_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_GotFocus">
      <summary>The _GotFocus_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_Holding">
      <summary>The _Holding_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_KeyDown">
      <summary>The _KeyDown_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_KeyUp">
      <summary>The _KeyUp_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_LosingFocus">
      <summary>The _LosingFocus_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_LostFocus">
      <summary>The _LostFocus_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_ManipulationCompleted">
      <summary>The _ManipulationCompleted_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_ManipulationDelta">
      <summary>The _ManipulationDelta_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_ManipulationInertiaStarting">
      <summary>The _ManipulationInertiaStarting_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_ManipulationStarted">
      <summary>The _ManipulationStarted_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_ManipulationStarting">
      <summary>The _ManipulationStarting_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_NoFocusCandidateFound">
      <summary>The _NoFocusCandidateFound_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_PointerCanceled">
      <summary>The _PointerCanceled_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_PointerCaptureLost">
      <summary>The _PointerCaptureLost_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_PointerEntered">
      <summary>The _PointerEntered_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_PointerExited">
      <summary>The _PointerExited_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_PointerMoved">
      <summary>The _PointerMoved_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_PointerPressed">
      <summary>The _PointerPressed_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_PointerReleased">
      <summary>The _PointerReleased_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_PointerWheelChanged">
      <summary>The _PointerWheelChanged_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_PreviewKeyDown">
      <summary>The _PreviewKeyDown_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_PreviewKeyUp">
      <summary>The _PreviewKeyUp_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_ProcessKeyboardAccelerators">
      <summary>The _ProcessKeyboardAccelerators_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_RightTapped">
      <summary>The _RightTapped_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.UIElement_Tapped">
      <summary>The _Tapped_ event for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.VirtualizingStackPanel_CleanUpVirtualizedItemEvent">
      <summary>The _CleanUpVirtualizedItemEvent_ event for the VirtualizingStackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_ContainsFullScreenElementChanged">
      <summary>The _ContainsFullScreenElementChanged_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_ContentLoading">
      <summary>The _ContentLoading_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_DOMContentLoaded">
      <summary>The _DOMContentLoaded_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_FrameContentLoading">
      <summary>The _FrameContentLoading_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_FrameDOMContentLoaded">
      <summary>The _FrameDOMContentLoaded_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_FrameNavigationCompleted">
      <summary>The _FrameNavigationCompleted_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_FrameNavigationStarting">
      <summary>The _FrameNavigationStarting_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_LoadCompleted">
      <summary>The _LoadCompleted_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_LongRunningScriptDetected">
      <summary>The _LongRunningScriptDetected_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_NavigationCompleted">
      <summary>The _NavigationCompleted_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_NavigationFailed">
      <summary>The _NavigationFailed_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_NavigationStarting">
      <summary>The _NavigationStarting_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_NewWindowRequested">
      <summary>The _NewWindowRequested_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_PermissionRequested">
      <summary>The _PermissionRequested_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_ScriptNotify">
      <summary>The _ScriptNotify_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_SeparateProcessLost">
      <summary>The _SeparateProcessLost_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_UnsafeContentWarningDisplaying">
      <summary>The _UnsafeContentWarningDisplaying_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_UnsupportedUriSchemeIdentified">
      <summary>The _UnsupportedUriSchemeIdentified_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_UnviewableContentIdentified">
      <summary>The _UnviewableContentIdentified_ event for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlEventIndex.WebView_WebResourceRequested">
      <summary>The _WebResourceRequested_ event for the WebView type.</summary>
    </member>
    <member name="T:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex">
      <summary>Enum that lists all the supported properties in XamlDirect.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AdaptiveTrigger_MinWindowHeight">
      <summary>The _MinWindowHeight_ property for the AdaptiveTrigger type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AdaptiveTrigger_MinWindowWidth">
      <summary>The _MinWindowWidth_ property for the AdaptiveTrigger type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBar_ClosedDisplayMode">
      <summary>The _ClosedDisplayMode_ property for the AppBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBar_IsOpen">
      <summary>The _IsOpen_ property for the AppBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBar_IsSticky">
      <summary>The _IsSticky_ property for the AppBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBar_LightDismissOverlayMode">
      <summary>The _LightDismissOverlayMode_ property for the AppBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBar_TemplateSettings">
      <summary>The _TemplateSettings_ property for the AppBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarButton_DynamicOverflowOrder">
      <summary>The _DynamicOverflowOrder_ property for the AppBarButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarButton_Icon">
      <summary>The _Icon_ property for the AppBarButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarButton_IsCompact">
      <summary>The _IsCompact_ property for the AppBarButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarButton_IsInOverflow">
      <summary>The _IsInOverflow_ property for the AppBarButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarButton_KeyboardAcceleratorTextOverride">
      <summary>The _KeyboardAcceleratorTextOverride_ property for the AppBarButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarButton_Label">
      <summary>The _Label_ property for the AppBarButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarButton_LabelPosition">
      <summary>The _LabelPosition_ property for the AppBarButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarButton_TemplateSettings">
      <summary>The _TemplateSettings_ property for the AppBarButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarButtonTemplateSettings_KeyboardAcceleratorTextMinWidth">
      <summary>The _KeyboardAcceleratorTextMinWidth_ property for the AppBarButtonTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarElementContainer_DynamicOverflowOrder">
      <summary>The _DynamicOverflowOrder_ property for the AppBarElementContainer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarElementContainer_IsCompact">
      <summary>The _IsCompact_ property for the AppBarElementContainer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarElementContainer_IsInOverflow">
      <summary>The _IsInOverflow_ property for the AppBarElementContainer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarSeparator_DynamicOverflowOrder">
      <summary>The _DynamicOverflowOrder_ property for the AppBarSeparator type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarSeparator_IsCompact">
      <summary>The _IsCompact_ property for the AppBarSeparator type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarSeparator_IsInOverflow">
      <summary>The _IsInOverflow_ property for the AppBarSeparator type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarTemplateSettings_ClipRect">
      <summary>The _ClipRect_ property for the AppBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarTemplateSettings_CompactRootMargin">
      <summary>The _CompactRootMargin_ property for the AppBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarTemplateSettings_CompactVerticalDelta">
      <summary>The _CompactVerticalDelta_ property for the AppBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarTemplateSettings_HiddenRootMargin">
      <summary>The _HiddenRootMargin_ property for the AppBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarTemplateSettings_HiddenVerticalDelta">
      <summary>The _HiddenVerticalDelta_ property for the AppBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarTemplateSettings_MinimalRootMargin">
      <summary>The _MinimalRootMargin_ property for the AppBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarTemplateSettings_MinimalVerticalDelta">
      <summary>The _MinimalVerticalDelta_ property for the AppBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarTemplateSettings_NegativeCompactVerticalDelta">
      <summary>The _NegativeCompactVerticalDelta_ property for the AppBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarTemplateSettings_NegativeHiddenVerticalDelta">
      <summary>The _NegativeHiddenVerticalDelta_ property for the AppBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarTemplateSettings_NegativeMinimalVerticalDelta">
      <summary>The _NegativeMinimalVerticalDelta_ property for the AppBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarToggleButton_DynamicOverflowOrder">
      <summary>The _DynamicOverflowOrder_ property for the AppBarToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarToggleButton_Icon">
      <summary>The _Icon_ property for the AppBarToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarToggleButton_IsCompact">
      <summary>The _IsCompact_ property for the AppBarToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarToggleButton_IsInOverflow">
      <summary>The _IsInOverflow_ property for the AppBarToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarToggleButton_KeyboardAcceleratorTextOverride">
      <summary>The _KeyboardAcceleratorTextOverride_ property for the AppBarToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarToggleButton_Label">
      <summary>The _Label_ property for the AppBarToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarToggleButton_LabelPosition">
      <summary>The _LabelPosition_ property for the AppBarToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarToggleButton_TemplateSettings">
      <summary>The _TemplateSettings_ property for the AppBarToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AppBarToggleButtonTemplateSettings_KeyboardAcceleratorTextMinWidth">
      <summary>The _KeyboardAcceleratorTextMinWidth_ property for the AppBarToggleButtonTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ArcSegment_IsLargeArc">
      <summary>The _IsLargeArc_ property for the ArcSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ArcSegment_Point">
      <summary>The _Point_ property for the ArcSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ArcSegment_RotationAngle">
      <summary>The _RotationAngle_ property for the ArcSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ArcSegment_Size">
      <summary>The _Size_ property for the ArcSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ArcSegment_SweepDirection">
      <summary>The _SweepDirection_ property for the ArcSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationAnnotation_Element">
      <summary>The _Element_ property for the AutomationAnnotation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationAnnotation_Type">
      <summary>The _Type_ property for the AutomationAnnotation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationPeer_EventsSource">
      <summary>The _EventsSource_ property for the AutomationPeer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationPeerAnnotation_Peer">
      <summary>The _Peer_ property for the AutomationPeerAnnotation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationPeerAnnotation_Type">
      <summary>The _Type_ property for the AutomationPeerAnnotation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_AcceleratorKey">
      <summary>The _AcceleratorKey_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_AccessibilityView">
      <summary>The _AccessibilityView_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_AccessKey">
      <summary>The _AccessKey_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_Annotations">
      <summary>The _Annotations_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_AutomationId">
      <summary>The _AutomationId_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_ControlledPeers">
      <summary>The _ControlledPeers_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_Culture">
      <summary>The _Culture_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_DescribedBy">
      <summary>The _DescribedBy_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_FlowsFrom">
      <summary>The _FlowsFrom_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_FlowsTo">
      <summary>The _FlowsTo_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_FullDescription">
      <summary>The _FullDescription_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_HeadingLevel">
      <summary>The _HeadingLevel_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_HelpText">
      <summary>The _HelpText_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_IsDataValidForForm">
      <summary>The _IsDataValidForForm_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_IsDialog">
      <summary>The _IsDialog_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_IsPeripheral">
      <summary>The _IsPeripheral_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_IsRequiredForForm">
      <summary>The _IsRequiredForForm_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_ItemStatus">
      <summary>The _ItemStatus_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_ItemType">
      <summary>The _ItemType_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_LabeledBy">
      <summary>The _LabeledBy_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_LandmarkType">
      <summary>The _LandmarkType_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_Level">
      <summary>The _Level_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_LiveSetting">
      <summary>The _LiveSetting_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_LocalizedControlType">
      <summary>The _LocalizedControlType_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_LocalizedLandmarkType">
      <summary>The _LocalizedLandmarkType_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_Name">
      <summary>The _Name_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_PositionInSet">
      <summary>The _PositionInSet_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutomationProperties_SizeOfSet">
      <summary>The _SizeOfSet_ property for the AutomationProperties type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBox_AutoMaximizeSuggestionArea">
      <summary>The _AutoMaximizeSuggestionArea_ property for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBox_Description">
      <summary>The _Description_ property for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBox_Header">
      <summary>The _Header_ property for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBox_IsSuggestionListOpen">
      <summary>The _IsSuggestionListOpen_ property for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBox_LightDismissOverlayMode">
      <summary>The _LightDismissOverlayMode_ property for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBox_MaxSuggestionListHeight">
      <summary>The _MaxSuggestionListHeight_ property for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBox_PlaceholderText">
      <summary>The _PlaceholderText_ property for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBox_QueryIcon">
      <summary>The _QueryIcon_ property for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBox_Text">
      <summary>The _Text_ property for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBox_TextBoxStyle">
      <summary>The _TextBoxStyle_ property for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBox_TextMemberPath">
      <summary>The _TextMemberPath_ property for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBox_UpdateTextOnSelect">
      <summary>The _UpdateTextOnSelect_ property for the AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBoxQuerySubmittedEventArgs_ChosenSuggestion">
      <summary>The _ChosenSuggestion_ property for the AutoSuggestBoxQuerySubmittedEventArgs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBoxQuerySubmittedEventArgs_QueryText">
      <summary>The _QueryText_ property for the AutoSuggestBoxQuerySubmittedEventArgs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBoxSuggestionChosenEventArgs_SelectedItem">
      <summary>The _SelectedItem_ property for the AutoSuggestBoxSuggestionChosenEventArgs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.AutoSuggestBoxTextChangedEventArgs_Reason">
      <summary>The _Reason_ property for the AutoSuggestBoxTextChangedEventArgs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BackEase_Amplitude">
      <summary>The _Amplitude_ property for the BackEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BeginStoryboard_Storyboard">
      <summary>The _Storyboard_ property for the BeginStoryboard type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BezierSegment_Point1">
      <summary>The _Point1_ property for the BezierSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BezierSegment_Point2">
      <summary>The _Point2_ property for the BezierSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BezierSegment_Point3">
      <summary>The _Point3_ property for the BezierSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Binding_Converter">
      <summary>The _Converter_ property for the Binding type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Binding_ConverterLanguage">
      <summary>The _ConverterLanguage_ property for the Binding type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Binding_ConverterParameter">
      <summary>The _ConverterParameter_ property for the Binding type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Binding_ElementName">
      <summary>The _ElementName_ property for the Binding type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Binding_FallbackValue">
      <summary>The _FallbackValue_ property for the Binding type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Binding_Mode">
      <summary>The _Mode_ property for the Binding type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Binding_Path">
      <summary>The _Path_ property for the Binding type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Binding_RelativeSource">
      <summary>The _RelativeSource_ property for the Binding type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Binding_Source">
      <summary>The _Source_ property for the Binding type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Binding_TargetNullValue">
      <summary>The _TargetNullValue_ property for the Binding type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Binding_UpdateSourceTrigger">
      <summary>The _UpdateSourceTrigger_ property for the Binding type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BitmapIcon_ShowAsMonochrome">
      <summary>The _ShowAsMonochrome_ property for the BitmapIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BitmapIcon_UriSource">
      <summary>The _UriSource_ property for the BitmapIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BitmapIconSource_ShowAsMonochrome">
      <summary>The _ShowAsMonochrome_ property for the BitmapIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BitmapIconSource_UriSource">
      <summary>The _UriSource_ property for the BitmapIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BitmapImage_AutoPlay">
      <summary>The _AutoPlay_ property for the BitmapImage type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BitmapImage_CreateOptions">
      <summary>The _CreateOptions_ property for the BitmapImage type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BitmapImage_DecodePixelHeight">
      <summary>The _DecodePixelHeight_ property for the BitmapImage type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BitmapImage_DecodePixelType">
      <summary>The _DecodePixelType_ property for the BitmapImage type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BitmapImage_DecodePixelWidth">
      <summary>The _DecodePixelWidth_ property for the BitmapImage type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BitmapImage_IsAnimatedBitmap">
      <summary>The _IsAnimatedBitmap_ property for the BitmapImage type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BitmapImage_IsPlaying">
      <summary>The _IsPlaying_ property for the BitmapImage type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BitmapImage_UriSource">
      <summary>The _UriSource_ property for the BitmapImage type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BitmapSource_PixelHeight">
      <summary>The _PixelHeight_ property for the BitmapSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BitmapSource_PixelWidth">
      <summary>The _PixelWidth_ property for the BitmapSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Block_HorizontalTextAlignment">
      <summary>The _HorizontalTextAlignment_ property for the Block type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Block_LineHeight">
      <summary>The _LineHeight_ property for the Block type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Block_LineStackingStrategy">
      <summary>The _LineStackingStrategy_ property for the Block type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Block_Margin">
      <summary>The _Margin_ property for the Block type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Block_TextAlignment">
      <summary>The _TextAlignment_ property for the Block type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Border_Background">
      <summary>The _Background_ property for the Border type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Border_BackgroundSizing">
      <summary>The _BackgroundSizing_ property for the Border type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Border_BackgroundTransition">
      <summary>The _BackgroundTransition_ property for the Border type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Border_BorderBrush">
      <summary>The _BorderBrush_ property for the Border type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Border_BorderThickness">
      <summary>The _BorderThickness_ property for the Border type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Border_Child">
      <summary>The _Child_ property for the Border type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Border_ChildTransitions">
      <summary>The _ChildTransitions_ property for the Border type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Border_CornerRadius">
      <summary>The _CornerRadius_ property for the Border type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Border_Padding">
      <summary>The _Padding_ property for the Border type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BounceEase_Bounces">
      <summary>The _Bounces_ property for the BounceEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BounceEase_Bounciness">
      <summary>The _Bounciness_ property for the BounceEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Brush_Opacity">
      <summary>The _Opacity_ property for the Brush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Brush_RelativeTransform">
      <summary>The _RelativeTransform_ property for the Brush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Brush_Transform">
      <summary>The _Transform_ property for the Brush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.BrushTransition_Duration">
      <summary>The _Duration_ property for the BrushTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Button_Flyout">
      <summary>The _Flyout_ property for the Button type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ButtonBase_ClickMode">
      <summary>The _ClickMode_ property for the ButtonBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ButtonBase_Command">
      <summary>The _Command_ property for the ButtonBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ButtonBase_CommandParameter">
      <summary>The _CommandParameter_ property for the ButtonBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ButtonBase_IsPointerOver">
      <summary>The _IsPointerOver_ property for the ButtonBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ButtonBase_IsPressed">
      <summary>The _IsPressed_ property for the ButtonBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_CalendarIdentifier">
      <summary>The _CalendarIdentifier_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_CalendarViewStyle">
      <summary>The _CalendarViewStyle_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_Date">
      <summary>The _Date_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_DateFormat">
      <summary>The _DateFormat_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_DayOfWeekFormat">
      <summary>The _DayOfWeekFormat_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_Description">
      <summary>The _Description_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_DisplayMode">
      <summary>The _DisplayMode_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_FirstDayOfWeek">
      <summary>The _FirstDayOfWeek_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_Header">
      <summary>The _Header_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_HeaderTemplate">
      <summary>The _HeaderTemplate_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_IsCalendarOpen">
      <summary>The _IsCalendarOpen_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_IsGroupLabelVisible">
      <summary>The _IsGroupLabelVisible_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_IsOutOfScopeEnabled">
      <summary>The _IsOutOfScopeEnabled_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_IsTodayHighlighted">
      <summary>The _IsTodayHighlighted_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_LightDismissOverlayMode">
      <summary>The _LightDismissOverlayMode_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_MaxDate">
      <summary>The _MaxDate_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_MinDate">
      <summary>The _MinDate_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarDatePicker_PlaceholderText">
      <summary>The _PlaceholderText_ property for the CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_BlackoutForeground">
      <summary>The _BlackoutForeground_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_CalendarIdentifier">
      <summary>The _CalendarIdentifier_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_CalendarItemBackground">
      <summary>The _CalendarItemBackground_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_CalendarItemBorderBrush">
      <summary>The _CalendarItemBorderBrush_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_CalendarItemBorderThickness">
      <summary>The _CalendarItemBorderThickness_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_CalendarItemForeground">
      <summary>The _CalendarItemForeground_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_CalendarViewDayItemStyle">
      <summary>The _CalendarViewDayItemStyle_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_DayItemFontFamily">
      <summary>The _DayItemFontFamily_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_DayItemFontSize">
      <summary>The _DayItemFontSize_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_DayItemFontStyle">
      <summary>The _DayItemFontStyle_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_DayItemFontWeight">
      <summary>The _DayItemFontWeight_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_DayOfWeekFormat">
      <summary>The _DayOfWeekFormat_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_DisplayMode">
      <summary>The _DisplayMode_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_FirstDayOfWeek">
      <summary>The _FirstDayOfWeek_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_FirstOfMonthLabelFontFamily">
      <summary>The _FirstOfMonthLabelFontFamily_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_FirstOfMonthLabelFontSize">
      <summary>The _FirstOfMonthLabelFontSize_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_FirstOfMonthLabelFontStyle">
      <summary>The _FirstOfMonthLabelFontStyle_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_FirstOfMonthLabelFontWeight">
      <summary>The _FirstOfMonthLabelFontWeight_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_FirstOfYearDecadeLabelFontFamily">
      <summary>The _FirstOfYearDecadeLabelFontFamily_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_FirstOfYearDecadeLabelFontSize">
      <summary>The _FirstOfYearDecadeLabelFontSize_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_FirstOfYearDecadeLabelFontStyle">
      <summary>The _FirstOfYearDecadeLabelFontStyle_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_FirstOfYearDecadeLabelFontWeight">
      <summary>The _FirstOfYearDecadeLabelFontWeight_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_FocusBorderBrush">
      <summary>The _FocusBorderBrush_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_HorizontalDayItemAlignment">
      <summary>The _HorizontalDayItemAlignment_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_HorizontalFirstOfMonthLabelAlignment">
      <summary>The _HorizontalFirstOfMonthLabelAlignment_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_HoverBorderBrush">
      <summary>The _HoverBorderBrush_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_IsGroupLabelVisible">
      <summary>The _IsGroupLabelVisible_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_IsOutOfScopeEnabled">
      <summary>The _IsOutOfScopeEnabled_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_IsTodayHighlighted">
      <summary>The _IsTodayHighlighted_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_MaxDate">
      <summary>The _MaxDate_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_MinDate">
      <summary>The _MinDate_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_MonthYearItemFontFamily">
      <summary>The _MonthYearItemFontFamily_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_MonthYearItemFontSize">
      <summary>The _MonthYearItemFontSize_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_MonthYearItemFontStyle">
      <summary>The _MonthYearItemFontStyle_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_MonthYearItemFontWeight">
      <summary>The _MonthYearItemFontWeight_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_NumberOfWeeksInView">
      <summary>The _NumberOfWeeksInView_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_OutOfScopeBackground">
      <summary>The _OutOfScopeBackground_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_OutOfScopeForeground">
      <summary>The _OutOfScopeForeground_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_PressedBorderBrush">
      <summary>The _PressedBorderBrush_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_PressedForeground">
      <summary>The _PressedForeground_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_SelectedBorderBrush">
      <summary>The _SelectedBorderBrush_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_SelectedDates">
      <summary>The _SelectedDates_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_SelectedForeground">
      <summary>The _SelectedForeground_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_SelectedHoverBorderBrush">
      <summary>The _SelectedHoverBorderBrush_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_SelectedPressedBorderBrush">
      <summary>The _SelectedPressedBorderBrush_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_SelectionMode">
      <summary>The _SelectionMode_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_TemplateSettings">
      <summary>The _TemplateSettings_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_TodayFontWeight">
      <summary>The _TodayFontWeight_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_TodayForeground">
      <summary>The _TodayForeground_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_VerticalDayItemAlignment">
      <summary>The _VerticalDayItemAlignment_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarView_VerticalFirstOfMonthLabelAlignment">
      <summary>The _VerticalFirstOfMonthLabelAlignment_ property for the CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewDayItem_Date">
      <summary>The _Date_ property for the CalendarViewDayItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewDayItem_IsBlackout">
      <summary>The _IsBlackout_ property for the CalendarViewDayItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_CenterX">
      <summary>The _CenterX_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_CenterY">
      <summary>The _CenterY_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_ClipRect">
      <summary>The _ClipRect_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_HasMoreContentAfter">
      <summary>The _HasMoreContentAfter_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_HasMoreContentBefore">
      <summary>The _HasMoreContentBefore_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_HasMoreViews">
      <summary>The _HasMoreViews_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_HeaderText">
      <summary>The _HeaderText_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_MinViewWidth">
      <summary>The _MinViewWidth_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_WeekDay1">
      <summary>The _WeekDay1_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_WeekDay2">
      <summary>The _WeekDay2_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_WeekDay3">
      <summary>The _WeekDay3_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_WeekDay4">
      <summary>The _WeekDay4_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_WeekDay5">
      <summary>The _WeekDay5_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_WeekDay6">
      <summary>The _WeekDay6_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CalendarViewTemplateSettings_WeekDay7">
      <summary>The _WeekDay7_ property for the CalendarViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Canvas_Left">
      <summary>The _Left_ property for the Canvas type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Canvas_Top">
      <summary>The _Top_ property for the Canvas type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Canvas_ZIndex">
      <summary>The _ZIndex_ property for the Canvas type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CaptureElement_Source">
      <summary>The _Source_ property for the CaptureElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CaptureElement_Stretch">
      <summary>The _Stretch_ property for the CaptureElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CollectionViewSource_IsSourceGrouped">
      <summary>The _IsSourceGrouped_ property for the CollectionViewSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CollectionViewSource_ItemsPath">
      <summary>The _ItemsPath_ property for the CollectionViewSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CollectionViewSource_Source">
      <summary>The _Source_ property for the CollectionViewSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CollectionViewSource_View">
      <summary>The _View_ property for the CollectionViewSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorAnimation_By">
      <summary>The _By_ property for the ColorAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorAnimation_EasingFunction">
      <summary>The _EasingFunction_ property for the ColorAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorAnimation_EnableDependentAnimation">
      <summary>The _EnableDependentAnimation_ property for the ColorAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorAnimation_From">
      <summary>The _From_ property for the ColorAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorAnimation_To">
      <summary>The _To_ property for the ColorAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorAnimationUsingKeyFrames_EnableDependentAnimation">
      <summary>The _EnableDependentAnimation_ property for the ColorAnimationUsingKeyFrames type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorAnimationUsingKeyFrames_KeyFrames">
      <summary>The _KeyFrames_ property for the ColorAnimationUsingKeyFrames type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorKeyFrame_KeyTime">
      <summary>The _KeyTime_ property for the ColorKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorKeyFrame_Value">
      <summary>The _Value_ property for the ColorKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_Accent">
      <summary>The _Accent_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_AltHigh">
      <summary>The _AltHigh_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_AltLow">
      <summary>The _AltLow_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_AltMedium">
      <summary>The _AltMedium_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_AltMediumHigh">
      <summary>The _AltMediumHigh_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_AltMediumLow">
      <summary>The _AltMediumLow_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_BaseHigh">
      <summary>The _BaseHigh_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_BaseLow">
      <summary>The _BaseLow_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_BaseMedium">
      <summary>The _BaseMedium_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_BaseMediumHigh">
      <summary>The _BaseMediumHigh_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_BaseMediumLow">
      <summary>The _BaseMediumLow_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ChromeAltLow">
      <summary>The _ChromeAltLow_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ChromeBlackHigh">
      <summary>The _ChromeBlackHigh_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ChromeBlackLow">
      <summary>The _ChromeBlackLow_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ChromeBlackMedium">
      <summary>The _ChromeBlackMedium_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ChromeBlackMediumLow">
      <summary>The _ChromeBlackMediumLow_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ChromeDisabledHigh">
      <summary>The _ChromeDisabledHigh_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ChromeDisabledLow">
      <summary>The _ChromeDisabledLow_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ChromeGray">
      <summary>The _ChromeGray_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ChromeHigh">
      <summary>The _ChromeHigh_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ChromeLow">
      <summary>The _ChromeLow_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ChromeMedium">
      <summary>The _ChromeMedium_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ChromeMediumLow">
      <summary>The _ChromeMediumLow_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ChromeWhite">
      <summary>The _ChromeWhite_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ErrorText">
      <summary>The _ErrorText_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ListLow">
      <summary>The _ListLow_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColorPaletteResources_ListMedium">
      <summary>The _ListMedium_ property for the ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColumnDefinition_ActualWidth">
      <summary>The _ActualWidth_ property for the ColumnDefinition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColumnDefinition_MaxWidth">
      <summary>The _MaxWidth_ property for the ColumnDefinition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColumnDefinition_MinWidth">
      <summary>The _MinWidth_ property for the ColumnDefinition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ColumnDefinition_Width">
      <summary>The _Width_ property for the ColumnDefinition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_Description">
      <summary>The _Description_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_Header">
      <summary>The _Header_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_HeaderTemplate">
      <summary>The _HeaderTemplate_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_IsDropDownOpen">
      <summary>The _IsDropDownOpen_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_IsEditable">
      <summary>The _IsEditable_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_IsSelectionBoxHighlighted">
      <summary>The _IsSelectionBoxHighlighted_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_IsTextSearchEnabled">
      <summary>The _IsTextSearchEnabled_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_LightDismissOverlayMode">
      <summary>The _LightDismissOverlayMode_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_MaxDropDownHeight">
      <summary>The _MaxDropDownHeight_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_PlaceholderForeground">
      <summary>The _PlaceholderForeground_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_PlaceholderText">
      <summary>The _PlaceholderText_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_SelectionBoxItem">
      <summary>The _SelectionBoxItem_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_SelectionBoxItemTemplate">
      <summary>The _SelectionBoxItemTemplate_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_SelectionChangedTrigger">
      <summary>The _SelectionChangedTrigger_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_TemplateSettings">
      <summary>The _TemplateSettings_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_Text">
      <summary>The _Text_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBox_TextBoxStyle">
      <summary>The _TextBoxStyle_ property for the ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBoxTemplateSettings_DropDownClosedHeight">
      <summary>The _DropDownClosedHeight_ property for the ComboBoxTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBoxTemplateSettings_DropDownContentMinWidth">
      <summary>The _DropDownContentMinWidth_ property for the ComboBoxTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBoxTemplateSettings_DropDownOffset">
      <summary>The _DropDownOffset_ property for the ComboBoxTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBoxTemplateSettings_DropDownOpenedHeight">
      <summary>The _DropDownOpenedHeight_ property for the ComboBoxTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ComboBoxTemplateSettings_SelectedItemDirection">
      <summary>The _SelectedItemDirection_ property for the ComboBoxTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBar_CommandBarOverflowPresenterStyle">
      <summary>The _CommandBarOverflowPresenterStyle_ property for the CommandBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBar_CommandBarTemplateSettings">
      <summary>The _CommandBarTemplateSettings_ property for the CommandBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBar_DefaultLabelPosition">
      <summary>The _DefaultLabelPosition_ property for the CommandBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBar_IsDynamicOverflowEnabled">
      <summary>The _IsDynamicOverflowEnabled_ property for the CommandBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBar_OverflowButtonVisibility">
      <summary>The _OverflowButtonVisibility_ property for the CommandBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBar_PrimaryCommands">
      <summary>The _PrimaryCommands_ property for the CommandBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBar_SecondaryCommands">
      <summary>The _SecondaryCommands_ property for the CommandBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBarTemplateSettings_ContentHeight">
      <summary>The _ContentHeight_ property for the CommandBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBarTemplateSettings_EffectiveOverflowButtonVisibility">
      <summary>The _EffectiveOverflowButtonVisibility_ property for the CommandBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBarTemplateSettings_NegativeOverflowContentHeight">
      <summary>The _NegativeOverflowContentHeight_ property for the CommandBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBarTemplateSettings_OverflowContentClipRect">
      <summary>The _OverflowContentClipRect_ property for the CommandBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBarTemplateSettings_OverflowContentCompactYTranslation">
      <summary>The _OverflowContentCompactYTranslation_ property for the CommandBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBarTemplateSettings_OverflowContentHeight">
      <summary>The _OverflowContentHeight_ property for the CommandBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBarTemplateSettings_OverflowContentHiddenYTranslation">
      <summary>The _OverflowContentHiddenYTranslation_ property for the CommandBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBarTemplateSettings_OverflowContentHorizontalOffset">
      <summary>The _OverflowContentHorizontalOffset_ property for the CommandBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBarTemplateSettings_OverflowContentMaxHeight">
      <summary>The _OverflowContentMaxHeight_ property for the CommandBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBarTemplateSettings_OverflowContentMaxWidth">
      <summary>The _OverflowContentMaxWidth_ property for the CommandBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBarTemplateSettings_OverflowContentMinimalYTranslation">
      <summary>The _OverflowContentMinimalYTranslation_ property for the CommandBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CommandBarTemplateSettings_OverflowContentMinWidth">
      <summary>The _OverflowContentMinWidth_ property for the CommandBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform_CenterX">
      <summary>The _CenterX_ property for the CompositeTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform_CenterY">
      <summary>The _CenterY_ property for the CompositeTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform_Rotation">
      <summary>The _Rotation_ property for the CompositeTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform_ScaleX">
      <summary>The _ScaleX_ property for the CompositeTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform_ScaleY">
      <summary>The _ScaleY_ property for the CompositeTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform_SkewX">
      <summary>The _SkewX_ property for the CompositeTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform_SkewY">
      <summary>The _SkewY_ property for the CompositeTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform_TranslateX">
      <summary>The _TranslateX_ property for the CompositeTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform_TranslateY">
      <summary>The _TranslateY_ property for the CompositeTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform3D_CenterX">
      <summary>The _CenterX_ property for the CompositeTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform3D_CenterY">
      <summary>The _CenterY_ property for the CompositeTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform3D_CenterZ">
      <summary>The _CenterZ_ property for the CompositeTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform3D_RotationX">
      <summary>The _RotationX_ property for the CompositeTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform3D_RotationY">
      <summary>The _RotationY_ property for the CompositeTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform3D_RotationZ">
      <summary>The _RotationZ_ property for the CompositeTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform3D_ScaleX">
      <summary>The _ScaleX_ property for the CompositeTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform3D_ScaleY">
      <summary>The _ScaleY_ property for the CompositeTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform3D_ScaleZ">
      <summary>The _ScaleZ_ property for the CompositeTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform3D_TranslateX">
      <summary>The _TranslateX_ property for the CompositeTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform3D_TranslateY">
      <summary>The _TranslateY_ property for the CompositeTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.CompositeTransform3D_TranslateZ">
      <summary>The _TranslateZ_ property for the CompositeTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentControl_Content">
      <summary>The _Content_ property for the ContentControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentControl_ContentTemplate">
      <summary>The _ContentTemplate_ property for the ContentControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentControl_ContentTemplateSelector">
      <summary>The _ContentTemplateSelector_ property for the ContentControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentControl_ContentTransitions">
      <summary>The _ContentTransitions_ property for the ContentControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_CloseButtonCommand">
      <summary>The _CloseButtonCommand_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_CloseButtonCommandParameter">
      <summary>The _CloseButtonCommandParameter_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_CloseButtonStyle">
      <summary>The _CloseButtonStyle_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_CloseButtonText">
      <summary>The _CloseButtonText_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_DefaultButton">
      <summary>The _DefaultButton_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_FullSizeDesired">
      <summary>The _FullSizeDesired_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_IsPrimaryButtonEnabled">
      <summary>The _IsPrimaryButtonEnabled_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_IsSecondaryButtonEnabled">
      <summary>The _IsSecondaryButtonEnabled_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_PrimaryButtonCommand">
      <summary>The _PrimaryButtonCommand_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_PrimaryButtonCommandParameter">
      <summary>The _PrimaryButtonCommandParameter_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_PrimaryButtonStyle">
      <summary>The _PrimaryButtonStyle_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_PrimaryButtonText">
      <summary>The _PrimaryButtonText_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_SecondaryButtonCommand">
      <summary>The _SecondaryButtonCommand_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_SecondaryButtonCommandParameter">
      <summary>The _SecondaryButtonCommandParameter_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_SecondaryButtonStyle">
      <summary>The _SecondaryButtonStyle_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_SecondaryButtonText">
      <summary>The _SecondaryButtonText_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_Title">
      <summary>The _Title_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentDialog_TitleTemplate">
      <summary>The _TitleTemplate_ property for the ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentLink_Background">
      <summary>The _Background_ property for the ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentLink_Cursor">
      <summary>The _Cursor_ property for the ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentLink_ElementSoundMode">
      <summary>The _ElementSoundMode_ property for the ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentLink_FocusState">
      <summary>The _FocusState_ property for the ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentLink_IsTabStop">
      <summary>The _IsTabStop_ property for the ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentLink_TabIndex">
      <summary>The _TabIndex_ property for the ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentLink_XYFocusDown">
      <summary>The _XYFocusDown_ property for the ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentLink_XYFocusDownNavigationStrategy">
      <summary>The _XYFocusDownNavigationStrategy_ property for the ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentLink_XYFocusLeft">
      <summary>The _XYFocusLeft_ property for the ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentLink_XYFocusLeftNavigationStrategy">
      <summary>The _XYFocusLeftNavigationStrategy_ property for the ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentLink_XYFocusRight">
      <summary>The _XYFocusRight_ property for the ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentLink_XYFocusRightNavigationStrategy">
      <summary>The _XYFocusRightNavigationStrategy_ property for the ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentLink_XYFocusUp">
      <summary>The _XYFocusUp_ property for the ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentLink_XYFocusUpNavigationStrategy">
      <summary>The _XYFocusUpNavigationStrategy_ property for the ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_Background">
      <summary>The _Background_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_BackgroundSizing">
      <summary>The _BackgroundSizing_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_BackgroundTransition">
      <summary>The _BackgroundTransition_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_BorderBrush">
      <summary>The _BorderBrush_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_BorderThickness">
      <summary>The _BorderThickness_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_CharacterSpacing">
      <summary>The _CharacterSpacing_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_Content">
      <summary>The _Content_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_ContentTemplate">
      <summary>The _ContentTemplate_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_ContentTemplateSelector">
      <summary>The _ContentTemplateSelector_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_ContentTransitions">
      <summary>The _ContentTransitions_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_CornerRadius">
      <summary>The _CornerRadius_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_FontFamily">
      <summary>The _FontFamily_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_FontSize">
      <summary>The _FontSize_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_FontStretch">
      <summary>The _FontStretch_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_FontStyle">
      <summary>The _FontStyle_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_FontWeight">
      <summary>The _FontWeight_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_Foreground">
      <summary>The _Foreground_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_HorizontalContentAlignment">
      <summary>The _HorizontalContentAlignment_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_IsTextScaleFactorEnabled">
      <summary>The _IsTextScaleFactorEnabled_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_LineHeight">
      <summary>The _LineHeight_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_LineStackingStrategy">
      <summary>The _LineStackingStrategy_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_MaxLines">
      <summary>The _MaxLines_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_OpticalMarginAlignment">
      <summary>The _OpticalMarginAlignment_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_Padding">
      <summary>The _Padding_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_TextLineBounds">
      <summary>The _TextLineBounds_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_TextWrapping">
      <summary>The _TextWrapping_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentPresenter_VerticalContentAlignment">
      <summary>The _VerticalContentAlignment_ property for the ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentThemeTransition_HorizontalOffset">
      <summary>The _HorizontalOffset_ property for the ContentThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ContentThemeTransition_VerticalOffset">
      <summary>The _VerticalOffset_ property for the ContentThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_Background">
      <summary>The _Background_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_BackgroundSizing">
      <summary>The _BackgroundSizing_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_BorderBrush">
      <summary>The _BorderBrush_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_BorderThickness">
      <summary>The _BorderThickness_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_CharacterSpacing">
      <summary>The _CharacterSpacing_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_CornerRadius">
      <summary>The _CornerRadius_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_DefaultStyleResourceUri">
      <summary>The _DefaultStyleResourceUri_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_ElementSoundMode">
      <summary>The _ElementSoundMode_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_FocusState">
      <summary>The _FocusState_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_FontFamily">
      <summary>The _FontFamily_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_FontSize">
      <summary>The _FontSize_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_FontStretch">
      <summary>The _FontStretch_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_FontStyle">
      <summary>The _FontStyle_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_FontWeight">
      <summary>The _FontWeight_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_Foreground">
      <summary>The _Foreground_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_HorizontalContentAlignment">
      <summary>The _HorizontalContentAlignment_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_IsEnabled">
      <summary>The _IsEnabled_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_IsFocusEngaged">
      <summary>The _IsFocusEngaged_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_IsFocusEngagementEnabled">
      <summary>The _IsFocusEngagementEnabled_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_IsTabStop">
      <summary>The _IsTabStop_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_IsTemplateFocusTarget">
      <summary>The _IsTemplateFocusTarget_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_IsTemplateKeyTipTarget">
      <summary>The _IsTemplateKeyTipTarget_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_IsTextScaleFactorEnabled">
      <summary>The _IsTextScaleFactorEnabled_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_Padding">
      <summary>The _Padding_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_RequiresPointer">
      <summary>The _RequiresPointer_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_TabIndex">
      <summary>The _TabIndex_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_TabNavigation">
      <summary>The _TabNavigation_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_Template">
      <summary>The _Template_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_UseSystemFocusVisuals">
      <summary>The _UseSystemFocusVisuals_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_VerticalContentAlignment">
      <summary>The _VerticalContentAlignment_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_XYFocusDown">
      <summary>The _XYFocusDown_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_XYFocusLeft">
      <summary>The _XYFocusLeft_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_XYFocusRight">
      <summary>The _XYFocusRight_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Control_XYFocusUp">
      <summary>The _XYFocusUp_ property for the Control type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ControlTemplate_TargetType">
      <summary>The _TargetType_ property for the ControlTemplate type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DataTemplate_ExtensionInstance">
      <summary>The _ExtensionInstance_ property for the DataTemplate type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_CalendarIdentifier">
      <summary>The _CalendarIdentifier_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_Date">
      <summary>The _Date_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_DayFormat">
      <summary>The _DayFormat_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_DayVisible">
      <summary>The _DayVisible_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_Header">
      <summary>The _Header_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_HeaderTemplate">
      <summary>The _HeaderTemplate_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_LightDismissOverlayMode">
      <summary>The _LightDismissOverlayMode_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_MaxYear">
      <summary>The _MaxYear_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_MinYear">
      <summary>The _MinYear_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_MonthFormat">
      <summary>The _MonthFormat_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_MonthVisible">
      <summary>The _MonthVisible_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_Orientation">
      <summary>The _Orientation_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_SelectedDate">
      <summary>The _SelectedDate_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_YearFormat">
      <summary>The _YearFormat_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DatePicker_YearVisible">
      <summary>The _YearVisible_ property for the DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DispatcherTimer_Interval">
      <summary>The _Interval_ property for the DispatcherTimer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DoubleAnimation_By">
      <summary>The _By_ property for the DoubleAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DoubleAnimation_EasingFunction">
      <summary>The _EasingFunction_ property for the DoubleAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DoubleAnimation_EnableDependentAnimation">
      <summary>The _EnableDependentAnimation_ property for the DoubleAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DoubleAnimation_From">
      <summary>The _From_ property for the DoubleAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DoubleAnimation_To">
      <summary>The _To_ property for the DoubleAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DoubleAnimationUsingKeyFrames_EnableDependentAnimation">
      <summary>The _EnableDependentAnimation_ property for the DoubleAnimationUsingKeyFrames type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DoubleAnimationUsingKeyFrames_KeyFrames">
      <summary>The _KeyFrames_ property for the DoubleAnimationUsingKeyFrames type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DoubleKeyFrame_KeyTime">
      <summary>The _KeyTime_ property for the DoubleKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DoubleKeyFrame_Value">
      <summary>The _Value_ property for the DoubleKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DragItemThemeAnimation_TargetName">
      <summary>The _TargetName_ property for the DragItemThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DragOverThemeAnimation_Direction">
      <summary>The _Direction_ property for the DragOverThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DragOverThemeAnimation_TargetName">
      <summary>The _TargetName_ property for the DragOverThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DragOverThemeAnimation_ToOffset">
      <summary>The _ToOffset_ property for the DragOverThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DrillInThemeAnimation_EntranceTarget">
      <summary>The _EntranceTarget_ property for the DrillInThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DrillInThemeAnimation_EntranceTargetName">
      <summary>The _EntranceTargetName_ property for the DrillInThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DrillInThemeAnimation_ExitTarget">
      <summary>The _ExitTarget_ property for the DrillInThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DrillInThemeAnimation_ExitTargetName">
      <summary>The _ExitTargetName_ property for the DrillInThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DrillOutThemeAnimation_EntranceTarget">
      <summary>The _EntranceTarget_ property for the DrillOutThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DrillOutThemeAnimation_EntranceTargetName">
      <summary>The _EntranceTargetName_ property for the DrillOutThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DrillOutThemeAnimation_ExitTarget">
      <summary>The _ExitTarget_ property for the DrillOutThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DrillOutThemeAnimation_ExitTargetName">
      <summary>The _ExitTargetName_ property for the DrillOutThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.DropTargetItemThemeAnimation_TargetName">
      <summary>The _TargetName_ property for the DropTargetItemThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.EasingColorKeyFrame_EasingFunction">
      <summary>The _EasingFunction_ property for the EasingColorKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.EasingDoubleKeyFrame_EasingFunction">
      <summary>The _EasingFunction_ property for the EasingDoubleKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.EasingFunctionBase_EasingMode">
      <summary>The _EasingMode_ property for the EasingFunctionBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.EasingPointKeyFrame_EasingFunction">
      <summary>The _EasingFunction_ property for the EasingPointKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.EdgeUIThemeTransition_Edge">
      <summary>The _Edge_ property for the EdgeUIThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ElasticEase_Oscillations">
      <summary>The _Oscillations_ property for the ElasticEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ElasticEase_Springiness">
      <summary>The _Springiness_ property for the ElasticEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.EllipseGeometry_Center">
      <summary>The _Center_ property for the EllipseGeometry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.EllipseGeometry_RadiusX">
      <summary>The _RadiusX_ property for the EllipseGeometry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.EllipseGeometry_RadiusY">
      <summary>The _RadiusY_ property for the EllipseGeometry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.EntranceThemeTransition_FromHorizontalOffset">
      <summary>The _FromHorizontalOffset_ property for the EntranceThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.EntranceThemeTransition_FromVerticalOffset">
      <summary>The _FromVerticalOffset_ property for the EntranceThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.EntranceThemeTransition_IsStaggeringEnabled">
      <summary>The _IsStaggeringEnabled_ property for the EntranceThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.EventTrigger_Actions">
      <summary>The _Actions_ property for the EventTrigger type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.EventTrigger_RoutedEvent">
      <summary>The _RoutedEvent_ property for the EventTrigger type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ExponentialEase_Exponent">
      <summary>The _Exponent_ property for the ExponentialEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FadeInThemeAnimation_TargetName">
      <summary>The _TargetName_ property for the FadeInThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FadeOutThemeAnimation_TargetName">
      <summary>The _TargetName_ property for the FadeOutThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlipView_UseTouchAnimationsForAllNavigation">
      <summary>The _UseTouchAnimationsForAllNavigation_ property for the FlipView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Flyout_Content">
      <summary>The _Content_ property for the Flyout type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Flyout_FlyoutPresenterStyle">
      <summary>The _FlyoutPresenterStyle_ property for the Flyout type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlyoutBase_AllowFocusOnInteraction">
      <summary>The _AllowFocusOnInteraction_ property for the FlyoutBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlyoutBase_AllowFocusWhenDisabled">
      <summary>The _AllowFocusWhenDisabled_ property for the FlyoutBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlyoutBase_AreOpenCloseAnimationsEnabled">
      <summary>The _AreOpenCloseAnimationsEnabled_ property for the FlyoutBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlyoutBase_AttachedFlyout">
      <summary>The _AttachedFlyout_ property for the FlyoutBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlyoutBase_ElementSoundMode">
      <summary>The _ElementSoundMode_ property for the FlyoutBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlyoutBase_InputDevicePrefersPrimaryCommands">
      <summary>The _InputDevicePrefersPrimaryCommands_ property for the FlyoutBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlyoutBase_IsOpen">
      <summary>The _IsOpen_ property for the FlyoutBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlyoutBase_LightDismissOverlayMode">
      <summary>The _LightDismissOverlayMode_ property for the FlyoutBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlyoutBase_OverlayInputPassThroughElement">
      <summary>The _OverlayInputPassThroughElement_ property for the FlyoutBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlyoutBase_Placement">
      <summary>The _Placement_ property for the FlyoutBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlyoutBase_ShouldConstrainToRootBounds">
      <summary>The _ShouldConstrainToRootBounds_ property for the FlyoutBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlyoutBase_ShowMode">
      <summary>The _ShowMode_ property for the FlyoutBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlyoutBase_Target">
      <summary>The _Target_ property for the FlyoutBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FlyoutPresenter_IsDefaultShadowEnabled">
      <summary>The _IsDefaultShadowEnabled_ property for the FlyoutPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FontIcon_FontFamily">
      <summary>The _FontFamily_ property for the FontIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FontIcon_FontSize">
      <summary>The _FontSize_ property for the FontIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FontIcon_FontStyle">
      <summary>The _FontStyle_ property for the FontIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FontIcon_FontWeight">
      <summary>The _FontWeight_ property for the FontIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FontIcon_Glyph">
      <summary>The _Glyph_ property for the FontIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FontIcon_IsTextScaleFactorEnabled">
      <summary>The _IsTextScaleFactorEnabled_ property for the FontIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FontIcon_MirroredWhenRightToLeft">
      <summary>The _MirroredWhenRightToLeft_ property for the FontIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FontIconSource_FontFamily">
      <summary>The _FontFamily_ property for the FontIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FontIconSource_FontSize">
      <summary>The _FontSize_ property for the FontIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FontIconSource_FontStyle">
      <summary>The _FontStyle_ property for the FontIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FontIconSource_FontWeight">
      <summary>The _FontWeight_ property for the FontIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FontIconSource_Glyph">
      <summary>The _Glyph_ property for the FontIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FontIconSource_IsTextScaleFactorEnabled">
      <summary>The _IsTextScaleFactorEnabled_ property for the FontIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FontIconSource_MirroredWhenRightToLeft">
      <summary>The _MirroredWhenRightToLeft_ property for the FontIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Frame_BackStack">
      <summary>The _BackStack_ property for the Frame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Frame_BackStackDepth">
      <summary>The _BackStackDepth_ property for the Frame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Frame_CacheSize">
      <summary>The _CacheSize_ property for the Frame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Frame_CanGoBack">
      <summary>The _CanGoBack_ property for the Frame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Frame_CanGoForward">
      <summary>The _CanGoForward_ property for the Frame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Frame_CurrentSourcePageType">
      <summary>The _CurrentSourcePageType_ property for the Frame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Frame_ForwardStack">
      <summary>The _ForwardStack_ property for the Frame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Frame_IsNavigationStackEnabled">
      <summary>The _IsNavigationStackEnabled_ property for the Frame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Frame_SourcePageType">
      <summary>The _SourcePageType_ property for the Frame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_ActualHeight">
      <summary>The _ActualHeight_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_ActualTheme">
      <summary>The _ActualTheme_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_ActualWidth">
      <summary>The _ActualWidth_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_AllowFocusOnInteraction">
      <summary>The _AllowFocusOnInteraction_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_AllowFocusWhenDisabled">
      <summary>The _AllowFocusWhenDisabled_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_DataContext">
      <summary>The _DataContext_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_FlowDirection">
      <summary>The _FlowDirection_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_FocusVisualMargin">
      <summary>The _FocusVisualMargin_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_FocusVisualPrimaryBrush">
      <summary>The _FocusVisualPrimaryBrush_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_FocusVisualPrimaryThickness">
      <summary>The _FocusVisualPrimaryThickness_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_FocusVisualSecondaryBrush">
      <summary>The _FocusVisualSecondaryBrush_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_FocusVisualSecondaryThickness">
      <summary>The _FocusVisualSecondaryThickness_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_Height">
      <summary>The _Height_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_HorizontalAlignment">
      <summary>The _HorizontalAlignment_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_Language">
      <summary>The _Language_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_Margin">
      <summary>The _Margin_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_MaxHeight">
      <summary>The _MaxHeight_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_MaxWidth">
      <summary>The _MaxWidth_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_MinHeight">
      <summary>The _MinHeight_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_MinWidth">
      <summary>The _MinWidth_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_Parent">
      <summary>The _Parent_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_RequestedTheme">
      <summary>The _RequestedTheme_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_Resources">
      <summary>The _Resources_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_Style">
      <summary>The _Style_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_Tag">
      <summary>The _Tag_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_Triggers">
      <summary>The _Triggers_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_VerticalAlignment">
      <summary>The _VerticalAlignment_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElement_Width">
      <summary>The _Width_ property for the FrameworkElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.FrameworkElementAutomationPeer_Owner">
      <summary>The _Owner_ property for the FrameworkElementAutomationPeer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Geometry_Bounds">
      <summary>The _Bounds_ property for the Geometry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Geometry_Transform">
      <summary>The _Transform_ property for the Geometry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GeometryGroup_Children">
      <summary>The _Children_ property for the GeometryGroup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GeometryGroup_FillRule">
      <summary>The _FillRule_ property for the GeometryGroup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Glyphs_ColorFontPaletteIndex">
      <summary>The _ColorFontPaletteIndex_ property for the Glyphs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Glyphs_Fill">
      <summary>The _Fill_ property for the Glyphs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Glyphs_FontRenderingEmSize">
      <summary>The _FontRenderingEmSize_ property for the Glyphs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Glyphs_FontUri">
      <summary>The _FontUri_ property for the Glyphs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Glyphs_Indices">
      <summary>The _Indices_ property for the Glyphs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Glyphs_IsColorFontEnabled">
      <summary>The _IsColorFontEnabled_ property for the Glyphs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Glyphs_OriginX">
      <summary>The _OriginX_ property for the Glyphs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Glyphs_OriginY">
      <summary>The _OriginY_ property for the Glyphs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Glyphs_StyleSimulations">
      <summary>The _StyleSimulations_ property for the Glyphs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Glyphs_UnicodeString">
      <summary>The _UnicodeString_ property for the Glyphs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GradientBrush_ColorInterpolationMode">
      <summary>The _ColorInterpolationMode_ property for the GradientBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GradientBrush_GradientStops">
      <summary>The _GradientStops_ property for the GradientBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GradientBrush_MappingMode">
      <summary>The _MappingMode_ property for the GradientBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GradientBrush_SpreadMethod">
      <summary>The _SpreadMethod_ property for the GradientBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GradientStop_Color">
      <summary>The _Color_ property for the GradientStop type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GradientStop_Offset">
      <summary>The _Offset_ property for the GradientStop type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Grid_BackgroundSizing">
      <summary>The _BackgroundSizing_ property for the Grid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Grid_BorderBrush">
      <summary>The _BorderBrush_ property for the Grid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Grid_BorderThickness">
      <summary>The _BorderThickness_ property for the Grid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Grid_Column">
      <summary>The _Column_ property for the Grid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Grid_ColumnDefinitions">
      <summary>The _ColumnDefinitions_ property for the Grid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Grid_ColumnSpacing">
      <summary>The _ColumnSpacing_ property for the Grid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Grid_ColumnSpan">
      <summary>The _ColumnSpan_ property for the Grid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Grid_CornerRadius">
      <summary>The _CornerRadius_ property for the Grid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Grid_Padding">
      <summary>The _Padding_ property for the Grid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Grid_Row">
      <summary>The _Row_ property for the Grid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Grid_RowDefinitions">
      <summary>The _RowDefinitions_ property for the Grid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Grid_RowSpacing">
      <summary>The _RowSpacing_ property for the Grid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Grid_RowSpan">
      <summary>The _RowSpan_ property for the Grid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItem_TemplateSettings">
      <summary>The _TemplateSettings_ property for the GridViewItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_CheckBrush">
      <summary>The _CheckBrush_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_CheckHintBrush">
      <summary>The _CheckHintBrush_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_CheckSelectingBrush">
      <summary>The _CheckSelectingBrush_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_ContentMargin">
      <summary>The _ContentMargin_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_DisabledOpacity">
      <summary>The _DisabledOpacity_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_DragBackground">
      <summary>The _DragBackground_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_DragForeground">
      <summary>The _DragForeground_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_DragOpacity">
      <summary>The _DragOpacity_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_FocusBorderBrush">
      <summary>The _FocusBorderBrush_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_GridViewItemPresenterHorizontalContentAlignment">
      <summary>The _GridViewItemPresenterHorizontalContentAlignment_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_GridViewItemPresenterPadding">
      <summary>The _GridViewItemPresenterPadding_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_GridViewItemPresenterVerticalContentAlignment">
      <summary>The _GridViewItemPresenterVerticalContentAlignment_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_PlaceholderBackground">
      <summary>The _PlaceholderBackground_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_PointerOverBackground">
      <summary>The _PointerOverBackground_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_PointerOverBackgroundMargin">
      <summary>The _PointerOverBackgroundMargin_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_ReorderHintOffset">
      <summary>The _ReorderHintOffset_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_SelectedBackground">
      <summary>The _SelectedBackground_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_SelectedBorderThickness">
      <summary>The _SelectedBorderThickness_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_SelectedForeground">
      <summary>The _SelectedForeground_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_SelectedPointerOverBackground">
      <summary>The _SelectedPointerOverBackground_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_SelectedPointerOverBorderBrush">
      <summary>The _SelectedPointerOverBorderBrush_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemPresenter_SelectionCheckMarkVisualEnabled">
      <summary>The _SelectionCheckMarkVisualEnabled_ property for the GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GridViewItemTemplateSettings_DragItemsCount">
      <summary>The _DragItemsCount_ property for the GridViewItemTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GroupStyle_ContainerStyle">
      <summary>The _ContainerStyle_ property for the GroupStyle type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GroupStyle_ContainerStyleSelector">
      <summary>The _ContainerStyleSelector_ property for the GroupStyle type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GroupStyle_HeaderContainerStyle">
      <summary>The _HeaderContainerStyle_ property for the GroupStyle type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GroupStyle_HeaderTemplate">
      <summary>The _HeaderTemplate_ property for the GroupStyle type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GroupStyle_HeaderTemplateSelector">
      <summary>The _HeaderTemplateSelector_ property for the GroupStyle type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GroupStyle_HidesIfEmpty">
      <summary>The _HidesIfEmpty_ property for the GroupStyle type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.GroupStyle_Panel">
      <summary>The _Panel_ property for the GroupStyle type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.HandwritingView_AreCandidatesEnabled">
      <summary>The _AreCandidatesEnabled_ property for the HandwritingView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.HandwritingView_IsOpen">
      <summary>The _IsOpen_ property for the HandwritingView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.HandwritingView_PlacementAlignment">
      <summary>The _PlacementAlignment_ property for the HandwritingView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.HandwritingView_PlacementTarget">
      <summary>The _PlacementTarget_ property for the HandwritingView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hub_DefaultSectionIndex">
      <summary>The _DefaultSectionIndex_ property for the Hub type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hub_Header">
      <summary>The _Header_ property for the Hub type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hub_HeaderTemplate">
      <summary>The _HeaderTemplate_ property for the Hub type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hub_IsActiveView">
      <summary>The _IsActiveView_ property for the Hub type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hub_IsZoomedInView">
      <summary>The _IsZoomedInView_ property for the Hub type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hub_Orientation">
      <summary>The _Orientation_ property for the Hub type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hub_SectionHeaders">
      <summary>The _SectionHeaders_ property for the Hub type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hub_Sections">
      <summary>The _Sections_ property for the Hub type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hub_SectionsInView">
      <summary>The _SectionsInView_ property for the Hub type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hub_SemanticZoomOwner">
      <summary>The _SemanticZoomOwner_ property for the Hub type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.HubSection_ContentTemplate">
      <summary>The _ContentTemplate_ property for the HubSection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.HubSection_Header">
      <summary>The _Header_ property for the HubSection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.HubSection_HeaderTemplate">
      <summary>The _HeaderTemplate_ property for the HubSection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.HubSection_IsHeaderInteractive">
      <summary>The _IsHeaderInteractive_ property for the HubSection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hyperlink_ElementSoundMode">
      <summary>The _ElementSoundMode_ property for the Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hyperlink_FocusState">
      <summary>The _FocusState_ property for the Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hyperlink_IsTabStop">
      <summary>The _IsTabStop_ property for the Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hyperlink_NavigateUri">
      <summary>The _NavigateUri_ property for the Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hyperlink_TabIndex">
      <summary>The _TabIndex_ property for the Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hyperlink_UnderlineStyle">
      <summary>The _UnderlineStyle_ property for the Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hyperlink_XYFocusDown">
      <summary>The _XYFocusDown_ property for the Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hyperlink_XYFocusDownNavigationStrategy">
      <summary>The _XYFocusDownNavigationStrategy_ property for the Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hyperlink_XYFocusLeft">
      <summary>The _XYFocusLeft_ property for the Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hyperlink_XYFocusLeftNavigationStrategy">
      <summary>The _XYFocusLeftNavigationStrategy_ property for the Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hyperlink_XYFocusRight">
      <summary>The _XYFocusRight_ property for the Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hyperlink_XYFocusRightNavigationStrategy">
      <summary>The _XYFocusRightNavigationStrategy_ property for the Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hyperlink_XYFocusUp">
      <summary>The _XYFocusUp_ property for the Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Hyperlink_XYFocusUpNavigationStrategy">
      <summary>The _XYFocusUpNavigationStrategy_ property for the Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.HyperlinkButton_NavigateUri">
      <summary>The _NavigateUri_ property for the HyperlinkButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.IconElement_Foreground">
      <summary>The _Foreground_ property for the IconElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.IconSource_Foreground">
      <summary>The _Foreground_ property for the IconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.IconSourceElement_IconSource">
      <summary>The _IconSource_ property for the IconSourceElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Image_NineGrid">
      <summary>The _NineGrid_ property for the Image type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Image_PlayToSource">
      <summary>The _PlayToSource_ property for the Image type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Image_Source">
      <summary>The _Source_ property for the Image type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Image_Stretch">
      <summary>The _Stretch_ property for the Image type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ImageBrush_ImageSource">
      <summary>The _ImageSource_ property for the ImageBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.InertiaExpansionBehavior_DesiredDeceleration">
      <summary>The _DesiredDeceleration_ property for the InertiaExpansionBehavior type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.InertiaExpansionBehavior_DesiredExpansion">
      <summary>The _DesiredExpansion_ property for the InertiaExpansionBehavior type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.InertiaRotationBehavior_DesiredDeceleration">
      <summary>The _DesiredDeceleration_ property for the InertiaRotationBehavior type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.InertiaRotationBehavior_DesiredRotation">
      <summary>The _DesiredRotation_ property for the InertiaRotationBehavior type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.InertiaTranslationBehavior_DesiredDeceleration">
      <summary>The _DesiredDeceleration_ property for the InertiaTranslationBehavior type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.InertiaTranslationBehavior_DesiredDisplacement">
      <summary>The _DesiredDisplacement_ property for the InertiaTranslationBehavior type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.InlineUIContainer_Child">
      <summary>The _Child_ property for the InlineUIContainer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.InputScope_Names">
      <summary>The _Names_ property for the InputScope type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.InputScopeName_NameValue">
      <summary>The _NameValue_ property for the InputScopeName type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemAutomationPeer_Item">
      <summary>The _Item_ property for the ItemAutomationPeer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemAutomationPeer_ItemsControlAutomationPeer">
      <summary>The _ItemsControlAutomationPeer_ property for the ItemAutomationPeer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsControl_DisplayMemberPath">
      <summary>The _DisplayMemberPath_ property for the ItemsControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsControl_GroupStyle">
      <summary>The _GroupStyle_ property for the ItemsControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsControl_GroupStyleSelector">
      <summary>The _GroupStyleSelector_ property for the ItemsControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsControl_IsGrouping">
      <summary>The _IsGrouping_ property for the ItemsControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsControl_ItemContainerStyle">
      <summary>The _ItemContainerStyle_ property for the ItemsControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsControl_ItemContainerStyleSelector">
      <summary>The _ItemContainerStyleSelector_ property for the ItemsControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsControl_ItemContainerTransitions">
      <summary>The _ItemContainerTransitions_ property for the ItemsControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsControl_Items">
      <summary>The _Items_ property for the ItemsControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsControl_ItemsPanel">
      <summary>The _ItemsPanel_ property for the ItemsControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsControl_ItemsSource">
      <summary>The _ItemsSource_ property for the ItemsControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsControl_ItemTemplate">
      <summary>The _ItemTemplate_ property for the ItemsControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsControl_ItemTemplateSelector">
      <summary>The _ItemTemplateSelector_ property for the ItemsControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsPresenter_Footer">
      <summary>The _Footer_ property for the ItemsPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsPresenter_FooterTemplate">
      <summary>The _FooterTemplate_ property for the ItemsPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsPresenter_FooterTransitions">
      <summary>The _FooterTransitions_ property for the ItemsPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsPresenter_Header">
      <summary>The _Header_ property for the ItemsPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsPresenter_HeaderTemplate">
      <summary>The _HeaderTemplate_ property for the ItemsPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsPresenter_HeaderTransitions">
      <summary>The _HeaderTransitions_ property for the ItemsPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsPresenter_Padding">
      <summary>The _Padding_ property for the ItemsPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsStackPanel_AreStickyGroupHeadersEnabled">
      <summary>The _AreStickyGroupHeadersEnabled_ property for the ItemsStackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsStackPanel_CacheLength">
      <summary>The _CacheLength_ property for the ItemsStackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsStackPanel_GroupHeaderPlacement">
      <summary>The _GroupHeaderPlacement_ property for the ItemsStackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsStackPanel_GroupPadding">
      <summary>The _GroupPadding_ property for the ItemsStackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsStackPanel_ItemsUpdatingScrollMode">
      <summary>The _ItemsUpdatingScrollMode_ property for the ItemsStackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsStackPanel_Orientation">
      <summary>The _Orientation_ property for the ItemsStackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsWrapGrid_AreStickyGroupHeadersEnabled">
      <summary>The _AreStickyGroupHeadersEnabled_ property for the ItemsWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsWrapGrid_CacheLength">
      <summary>The _CacheLength_ property for the ItemsWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsWrapGrid_GroupHeaderPlacement">
      <summary>The _GroupHeaderPlacement_ property for the ItemsWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsWrapGrid_GroupPadding">
      <summary>The _GroupPadding_ property for the ItemsWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsWrapGrid_ItemHeight">
      <summary>The _ItemHeight_ property for the ItemsWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsWrapGrid_ItemWidth">
      <summary>The _ItemWidth_ property for the ItemsWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsWrapGrid_MaximumRowsOrColumns">
      <summary>The _MaximumRowsOrColumns_ property for the ItemsWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ItemsWrapGrid_Orientation">
      <summary>The _Orientation_ property for the ItemsWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.KeyboardAccelerator_IsEnabled">
      <summary>The _IsEnabled_ property for the KeyboardAccelerator type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.KeyboardAccelerator_Key">
      <summary>The _Key_ property for the KeyboardAccelerator type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.KeyboardAccelerator_Modifiers">
      <summary>The _Modifiers_ property for the KeyboardAccelerator type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.KeyboardAccelerator_ScopeOwner">
      <summary>The _ScopeOwner_ property for the KeyboardAccelerator type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.KeySpline_ControlPoint1">
      <summary>The _ControlPoint1_ property for the KeySpline type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.KeySpline_ControlPoint2">
      <summary>The _ControlPoint2_ property for the KeySpline type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Line_X1">
      <summary>The _X1_ property for the Line type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Line_X2">
      <summary>The _X2_ property for the Line type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Line_Y1">
      <summary>The _Y1_ property for the Line type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Line_Y2">
      <summary>The _Y2_ property for the Line type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.LinearGradientBrush_EndPoint">
      <summary>The _EndPoint_ property for the LinearGradientBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.LinearGradientBrush_StartPoint">
      <summary>The _StartPoint_ property for the LinearGradientBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.LineGeometry_EndPoint">
      <summary>The _EndPoint_ property for the LineGeometry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.LineGeometry_StartPoint">
      <summary>The _StartPoint_ property for the LineGeometry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.LineSegment_Point">
      <summary>The _Point_ property for the LineSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListBox_SelectedItems">
      <summary>The _SelectedItems_ property for the ListBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListBox_SelectionMode">
      <summary>The _SelectionMode_ property for the ListBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListBox_SingleSelectionFollowsFocus">
      <summary>The _SingleSelectionFollowsFocus_ property for the ListBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_CanDragItems">
      <summary>The _CanDragItems_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_CanReorderItems">
      <summary>The _CanReorderItems_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_DataFetchSize">
      <summary>The _DataFetchSize_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_Footer">
      <summary>The _Footer_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_FooterTemplate">
      <summary>The _FooterTemplate_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_FooterTransitions">
      <summary>The _FooterTransitions_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_Header">
      <summary>The _Header_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_HeaderTemplate">
      <summary>The _HeaderTemplate_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_HeaderTransitions">
      <summary>The _HeaderTransitions_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_IncrementalLoadingThreshold">
      <summary>The _IncrementalLoadingThreshold_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_IncrementalLoadingTrigger">
      <summary>The _IncrementalLoadingTrigger_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_IsActiveView">
      <summary>The _IsActiveView_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_IsItemClickEnabled">
      <summary>The _IsItemClickEnabled_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_IsMultiSelectCheckBoxEnabled">
      <summary>The _IsMultiSelectCheckBoxEnabled_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_IsSwipeEnabled">
      <summary>The _IsSwipeEnabled_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_IsZoomedInView">
      <summary>The _IsZoomedInView_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_ReorderMode">
      <summary>The _ReorderMode_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_SelectedItems">
      <summary>The _SelectedItems_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_SelectedRanges">
      <summary>The _SelectedRanges_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_SelectionMode">
      <summary>The _SelectionMode_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_SemanticZoomOwner">
      <summary>The _SemanticZoomOwner_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_ShowsScrollingPlaceholders">
      <summary>The _ShowsScrollingPlaceholders_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewBase_SingleSelectionFollowsFocus">
      <summary>The _SingleSelectionFollowsFocus_ property for the ListViewBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItem_TemplateSettings">
      <summary>The _TemplateSettings_ property for the ListViewItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_CheckBoxBrush">
      <summary>The _CheckBoxBrush_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_CheckBrush">
      <summary>The _CheckBrush_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_CheckHintBrush">
      <summary>The _CheckHintBrush_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_CheckMode">
      <summary>The _CheckMode_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_CheckSelectingBrush">
      <summary>The _CheckSelectingBrush_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_ContentMargin">
      <summary>The _ContentMargin_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_DisabledOpacity">
      <summary>The _DisabledOpacity_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_DragBackground">
      <summary>The _DragBackground_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_DragForeground">
      <summary>The _DragForeground_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_DragOpacity">
      <summary>The _DragOpacity_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_FocusBorderBrush">
      <summary>The _FocusBorderBrush_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_FocusSecondaryBorderBrush">
      <summary>The _FocusSecondaryBorderBrush_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_ListViewItemPresenterHorizontalContentAlignment">
      <summary>The _ListViewItemPresenterHorizontalContentAlignment_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_ListViewItemPresenterPadding">
      <summary>The _ListViewItemPresenterPadding_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_ListViewItemPresenterVerticalContentAlignment">
      <summary>The _ListViewItemPresenterVerticalContentAlignment_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_PlaceholderBackground">
      <summary>The _PlaceholderBackground_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_PointerOverBackground">
      <summary>The _PointerOverBackground_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_PointerOverBackgroundMargin">
      <summary>The _PointerOverBackgroundMargin_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_PointerOverForeground">
      <summary>The _PointerOverForeground_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_PressedBackground">
      <summary>The _PressedBackground_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_ReorderHintOffset">
      <summary>The _ReorderHintOffset_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_RevealBackground">
      <summary>The _RevealBackground_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_RevealBackgroundShowsAboveContent">
      <summary>The _RevealBackgroundShowsAboveContent_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_RevealBorderBrush">
      <summary>The _RevealBorderBrush_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_RevealBorderThickness">
      <summary>The _RevealBorderThickness_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_SelectedBackground">
      <summary>The _SelectedBackground_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_SelectedBorderThickness">
      <summary>The _SelectedBorderThickness_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_SelectedForeground">
      <summary>The _SelectedForeground_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_SelectedPointerOverBackground">
      <summary>The _SelectedPointerOverBackground_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_SelectedPointerOverBorderBrush">
      <summary>The _SelectedPointerOverBorderBrush_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_SelectedPressedBackground">
      <summary>The _SelectedPressedBackground_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemPresenter_SelectionCheckMarkVisualEnabled">
      <summary>The _SelectionCheckMarkVisualEnabled_ property for the ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ListViewItemTemplateSettings_DragItemsCount">
      <summary>The _DragItemsCount_ property for the ListViewItemTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.LoadedImageSurface_DecodedPhysicalSize">
      <summary>The _DecodedPhysicalSize_ property for the LoadedImageSurface type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.LoadedImageSurface_DecodedSize">
      <summary>The _DecodedSize_ property for the LoadedImageSurface type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.LoadedImageSurface_NaturalSize">
      <summary>The _NaturalSize_ property for the LoadedImageSurface type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ManipulationPivot_Center">
      <summary>The _Center_ property for the ManipulationPivot type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ManipulationPivot_Radius">
      <summary>The _Radius_ property for the ManipulationPivot type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Matrix3DProjection_ProjectionMatrix">
      <summary>The _ProjectionMatrix_ property for the Matrix3DProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MatrixTransform_Matrix">
      <summary>The _Matrix_ property for the MatrixTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_ActualStereo3DVideoPackingMode">
      <summary>The _ActualStereo3DVideoPackingMode_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_AreTransportControlsEnabled">
      <summary>The _AreTransportControlsEnabled_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_AspectRatioHeight">
      <summary>The _AspectRatioHeight_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_AspectRatioWidth">
      <summary>The _AspectRatioWidth_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_AudioCategory">
      <summary>The _AudioCategory_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_AudioDeviceType">
      <summary>The _AudioDeviceType_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_AudioStreamCount">
      <summary>The _AudioStreamCount_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_AudioStreamIndex">
      <summary>The _AudioStreamIndex_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_AutoPlay">
      <summary>The _AutoPlay_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_Balance">
      <summary>The _Balance_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_BufferingProgress">
      <summary>The _BufferingProgress_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_CanPause">
      <summary>The _CanPause_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_CanSeek">
      <summary>The _CanSeek_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_CurrentState">
      <summary>The _CurrentState_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_DefaultPlaybackRate">
      <summary>The _DefaultPlaybackRate_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_DownloadProgress">
      <summary>The _DownloadProgress_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_DownloadProgressOffset">
      <summary>The _DownloadProgressOffset_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_IsAudioOnly">
      <summary>The _IsAudioOnly_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_IsFullWindow">
      <summary>The _IsFullWindow_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_IsLooping">
      <summary>The _IsLooping_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_IsMuted">
      <summary>The _IsMuted_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_IsStereo3DVideo">
      <summary>The _IsStereo3DVideo_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_Markers">
      <summary>The _Markers_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_NaturalDuration">
      <summary>The _NaturalDuration_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_NaturalVideoHeight">
      <summary>The _NaturalVideoHeight_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_NaturalVideoWidth">
      <summary>The _NaturalVideoWidth_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_PlaybackRate">
      <summary>The _PlaybackRate_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_PlayToPreferredSourceUri">
      <summary>The _PlayToPreferredSourceUri_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_PlayToSource">
      <summary>The _PlayToSource_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_Position">
      <summary>The _Position_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_PosterSource">
      <summary>The _PosterSource_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_ProtectionManager">
      <summary>The _ProtectionManager_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_RealTimePlayback">
      <summary>The _RealTimePlayback_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_Source">
      <summary>The _Source_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_Stereo3DVideoPackingMode">
      <summary>The _Stereo3DVideoPackingMode_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_Stereo3DVideoRenderMode">
      <summary>The _Stereo3DVideoRenderMode_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_Stretch">
      <summary>The _Stretch_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_TransportControls">
      <summary>The _TransportControls_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaElement_Volume">
      <summary>The _Volume_ property for the MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaPlayerElement_AreTransportControlsEnabled">
      <summary>The _AreTransportControlsEnabled_ property for the MediaPlayerElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaPlayerElement_AutoPlay">
      <summary>The _AutoPlay_ property for the MediaPlayerElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaPlayerElement_IsFullWindow">
      <summary>The _IsFullWindow_ property for the MediaPlayerElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaPlayerElement_MediaPlayer">
      <summary>The _MediaPlayer_ property for the MediaPlayerElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaPlayerElement_PosterSource">
      <summary>The _PosterSource_ property for the MediaPlayerElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaPlayerElement_Source">
      <summary>The _Source_ property for the MediaPlayerElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaPlayerElement_Stretch">
      <summary>The _Stretch_ property for the MediaPlayerElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaPlayerElement_TransportControls">
      <summary>The _TransportControls_ property for the MediaPlayerElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaPlayerPresenter_IsFullWindow">
      <summary>The _IsFullWindow_ property for the MediaPlayerPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaPlayerPresenter_MediaPlayer">
      <summary>The _MediaPlayer_ property for the MediaPlayerPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaPlayerPresenter_Stretch">
      <summary>The _Stretch_ property for the MediaPlayerPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_FastPlayFallbackBehaviour">
      <summary>The _FastPlayFallbackBehaviour_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsCompact">
      <summary>The _IsCompact_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsCompactOverlayButtonVisible">
      <summary>The _IsCompactOverlayButtonVisible_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsCompactOverlayEnabled">
      <summary>The _IsCompactOverlayEnabled_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsFastForwardButtonVisible">
      <summary>The _IsFastForwardButtonVisible_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsFastForwardEnabled">
      <summary>The _IsFastForwardEnabled_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsFastRewindButtonVisible">
      <summary>The _IsFastRewindButtonVisible_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsFastRewindEnabled">
      <summary>The _IsFastRewindEnabled_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsFullWindowButtonVisible">
      <summary>The _IsFullWindowButtonVisible_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsFullWindowEnabled">
      <summary>The _IsFullWindowEnabled_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsNextTrackButtonVisible">
      <summary>The _IsNextTrackButtonVisible_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsPlaybackRateButtonVisible">
      <summary>The _IsPlaybackRateButtonVisible_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsPlaybackRateEnabled">
      <summary>The _IsPlaybackRateEnabled_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsPreviousTrackButtonVisible">
      <summary>The _IsPreviousTrackButtonVisible_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsRepeatButtonVisible">
      <summary>The _IsRepeatButtonVisible_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsRepeatEnabled">
      <summary>The _IsRepeatEnabled_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsSeekBarVisible">
      <summary>The _IsSeekBarVisible_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsSeekEnabled">
      <summary>The _IsSeekEnabled_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsSkipBackwardButtonVisible">
      <summary>The _IsSkipBackwardButtonVisible_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsSkipBackwardEnabled">
      <summary>The _IsSkipBackwardEnabled_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsSkipForwardButtonVisible">
      <summary>The _IsSkipForwardButtonVisible_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsSkipForwardEnabled">
      <summary>The _IsSkipForwardEnabled_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsStopButtonVisible">
      <summary>The _IsStopButtonVisible_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsStopEnabled">
      <summary>The _IsStopEnabled_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsVolumeButtonVisible">
      <summary>The _IsVolumeButtonVisible_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsVolumeEnabled">
      <summary>The _IsVolumeEnabled_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsZoomButtonVisible">
      <summary>The _IsZoomButtonVisible_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_IsZoomEnabled">
      <summary>The _IsZoomEnabled_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControls_ShowAndHideAutomatically">
      <summary>The _ShowAndHideAutomatically_ property for the MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MediaTransportControlsHelper_DropoutOrder">
      <summary>The _DropoutOrder_ property for the MediaTransportControlsHelper type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyout_Items">
      <summary>The _Items_ property for the MenuFlyout type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyout_MenuFlyoutPresenterStyle">
      <summary>The _MenuFlyoutPresenterStyle_ property for the MenuFlyout type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyoutItem_Command">
      <summary>The _Command_ property for the MenuFlyoutItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyoutItem_CommandParameter">
      <summary>The _CommandParameter_ property for the MenuFlyoutItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyoutItem_Icon">
      <summary>The _Icon_ property for the MenuFlyoutItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyoutItem_KeyboardAcceleratorTextOverride">
      <summary>The _KeyboardAcceleratorTextOverride_ property for the MenuFlyoutItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyoutItem_TemplateSettings">
      <summary>The _TemplateSettings_ property for the MenuFlyoutItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyoutItem_Text">
      <summary>The _Text_ property for the MenuFlyoutItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyoutItemTemplateSettings_KeyboardAcceleratorTextMinWidth">
      <summary>The _KeyboardAcceleratorTextMinWidth_ property for the MenuFlyoutItemTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyoutPresenter_IsDefaultShadowEnabled">
      <summary>The _IsDefaultShadowEnabled_ property for the MenuFlyoutPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyoutPresenter_TemplateSettings">
      <summary>The _TemplateSettings_ property for the MenuFlyoutPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyoutPresenterTemplateSettings_FlyoutContentMinWidth">
      <summary>The _FlyoutContentMinWidth_ property for the MenuFlyoutPresenterTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyoutSubItem_Icon">
      <summary>The _Icon_ property for the MenuFlyoutSubItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyoutSubItem_Items">
      <summary>The _Items_ property for the MenuFlyoutSubItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.MenuFlyoutSubItem_Text">
      <summary>The _Text_ property for the MenuFlyoutSubItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ObjectAnimationUsingKeyFrames_EnableDependentAnimation">
      <summary>The _EnableDependentAnimation_ property for the ObjectAnimationUsingKeyFrames type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ObjectAnimationUsingKeyFrames_KeyFrames">
      <summary>The _KeyFrames_ property for the ObjectAnimationUsingKeyFrames type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ObjectKeyFrame_KeyTime">
      <summary>The _KeyTime_ property for the ObjectKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ObjectKeyFrame_Value">
      <summary>The _Value_ property for the ObjectKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Page_BottomAppBar">
      <summary>The _BottomAppBar_ property for the Page type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Page_Frame">
      <summary>The _Frame_ property for the Page type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Page_NavigationCacheMode">
      <summary>The _NavigationCacheMode_ property for the Page type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Page_TopAppBar">
      <summary>The _TopAppBar_ property for the Page type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PageStackEntry_SourcePageType">
      <summary>The _SourcePageType_ property for the PageStackEntry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Panel_Background">
      <summary>The _Background_ property for the Panel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Panel_BackgroundTransition">
      <summary>The _BackgroundTransition_ property for the Panel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Panel_Children">
      <summary>The _Children_ property for the Panel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Panel_ChildrenTransitions">
      <summary>The _ChildrenTransitions_ property for the Panel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Panel_IsItemsHost">
      <summary>The _IsItemsHost_ property for the Panel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PaneThemeTransition_Edge">
      <summary>The _Edge_ property for the PaneThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Paragraph_Inlines">
      <summary>The _Inlines_ property for the Paragraph type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Paragraph_TextIndent">
      <summary>The _TextIndent_ property for the Paragraph type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_CanPasteClipboardContent">
      <summary>The _CanPasteClipboardContent_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_Description">
      <summary>The _Description_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_Header">
      <summary>The _Header_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_HeaderTemplate">
      <summary>The _HeaderTemplate_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_InputScope">
      <summary>The _InputScope_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_IsPasswordRevealButtonEnabled">
      <summary>The _IsPasswordRevealButtonEnabled_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_MaxLength">
      <summary>The _MaxLength_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_Password">
      <summary>The _Password_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_PasswordChar">
      <summary>The _PasswordChar_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_PasswordRevealMode">
      <summary>The _PasswordRevealMode_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_PlaceholderText">
      <summary>The _PlaceholderText_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_PreventKeyboardDisplayOnProgrammaticFocus">
      <summary>The _PreventKeyboardDisplayOnProgrammaticFocus_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_SelectionFlyout">
      <summary>The _SelectionFlyout_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_SelectionHighlightColor">
      <summary>The _SelectionHighlightColor_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PasswordBox_TextReadingOrder">
      <summary>The _TextReadingOrder_ property for the PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Path_Data">
      <summary>The _Data_ property for the Path type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PathFigure_IsClosed">
      <summary>The _IsClosed_ property for the PathFigure type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PathFigure_IsFilled">
      <summary>The _IsFilled_ property for the PathFigure type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PathFigure_Segments">
      <summary>The _Segments_ property for the PathFigure type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PathFigure_StartPoint">
      <summary>The _StartPoint_ property for the PathFigure type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PathGeometry_Figures">
      <summary>The _Figures_ property for the PathGeometry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PathGeometry_FillRule">
      <summary>The _FillRule_ property for the PathGeometry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PathIcon_Data">
      <summary>The _Data_ property for the PathIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PathIconSource_Data">
      <summary>The _Data_ property for the PathIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PerspectiveTransform3D_Depth">
      <summary>The _Depth_ property for the PerspectiveTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PerspectiveTransform3D_OffsetX">
      <summary>The _OffsetX_ property for the PerspectiveTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PerspectiveTransform3D_OffsetY">
      <summary>The _OffsetY_ property for the PerspectiveTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PlaneProjection_CenterOfRotationX">
      <summary>The _CenterOfRotationX_ property for the PlaneProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PlaneProjection_CenterOfRotationY">
      <summary>The _CenterOfRotationY_ property for the PlaneProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PlaneProjection_CenterOfRotationZ">
      <summary>The _CenterOfRotationZ_ property for the PlaneProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PlaneProjection_GlobalOffsetX">
      <summary>The _GlobalOffsetX_ property for the PlaneProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PlaneProjection_GlobalOffsetY">
      <summary>The _GlobalOffsetY_ property for the PlaneProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PlaneProjection_GlobalOffsetZ">
      <summary>The _GlobalOffsetZ_ property for the PlaneProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PlaneProjection_LocalOffsetX">
      <summary>The _LocalOffsetX_ property for the PlaneProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PlaneProjection_LocalOffsetY">
      <summary>The _LocalOffsetY_ property for the PlaneProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PlaneProjection_LocalOffsetZ">
      <summary>The _LocalOffsetZ_ property for the PlaneProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PlaneProjection_ProjectionMatrix">
      <summary>The _ProjectionMatrix_ property for the PlaneProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PlaneProjection_RotationX">
      <summary>The _RotationX_ property for the PlaneProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PlaneProjection_RotationY">
      <summary>The _RotationY_ property for the PlaneProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PlaneProjection_RotationZ">
      <summary>The _RotationZ_ property for the PlaneProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PointAnimation_By">
      <summary>The _By_ property for the PointAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PointAnimation_EasingFunction">
      <summary>The _EasingFunction_ property for the PointAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PointAnimation_EnableDependentAnimation">
      <summary>The _EnableDependentAnimation_ property for the PointAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PointAnimation_From">
      <summary>The _From_ property for the PointAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PointAnimation_To">
      <summary>The _To_ property for the PointAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PointAnimationUsingKeyFrames_EnableDependentAnimation">
      <summary>The _EnableDependentAnimation_ property for the PointAnimationUsingKeyFrames type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PointAnimationUsingKeyFrames_KeyFrames">
      <summary>The _KeyFrames_ property for the PointAnimationUsingKeyFrames type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Pointer_IsInContact">
      <summary>The _IsInContact_ property for the Pointer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Pointer_IsInRange">
      <summary>The _IsInRange_ property for the Pointer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Pointer_PointerDeviceType">
      <summary>The _PointerDeviceType_ property for the Pointer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Pointer_PointerId">
      <summary>The _PointerId_ property for the Pointer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PointerDownThemeAnimation_TargetName">
      <summary>The _TargetName_ property for the PointerDownThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PointerUpThemeAnimation_TargetName">
      <summary>The _TargetName_ property for the PointerUpThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PointKeyFrame_KeyTime">
      <summary>The _KeyTime_ property for the PointKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PointKeyFrame_Value">
      <summary>The _Value_ property for the PointKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PolyBezierSegment_Points">
      <summary>The _Points_ property for the PolyBezierSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Polygon_FillRule">
      <summary>The _FillRule_ property for the Polygon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Polygon_Points">
      <summary>The _Points_ property for the Polygon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Polyline_FillRule">
      <summary>The _FillRule_ property for the Polyline type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Polyline_Points">
      <summary>The _Points_ property for the Polyline type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PolyLineSegment_Points">
      <summary>The _Points_ property for the PolyLineSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PolyQuadraticBezierSegment_Points">
      <summary>The _Points_ property for the PolyQuadraticBezierSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PopInThemeAnimation_FromHorizontalOffset">
      <summary>The _FromHorizontalOffset_ property for the PopInThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PopInThemeAnimation_FromVerticalOffset">
      <summary>The _FromVerticalOffset_ property for the PopInThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PopInThemeAnimation_TargetName">
      <summary>The _TargetName_ property for the PopInThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PopOutThemeAnimation_TargetName">
      <summary>The _TargetName_ property for the PopOutThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Popup_Child">
      <summary>The _Child_ property for the Popup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Popup_ChildTransitions">
      <summary>The _ChildTransitions_ property for the Popup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Popup_HorizontalOffset">
      <summary>The _HorizontalOffset_ property for the Popup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Popup_IsLightDismissEnabled">
      <summary>The _IsLightDismissEnabled_ property for the Popup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Popup_IsOpen">
      <summary>The _IsOpen_ property for the Popup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Popup_LightDismissOverlayMode">
      <summary>The _LightDismissOverlayMode_ property for the Popup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Popup_ShouldConstrainToRootBounds">
      <summary>The _ShouldConstrainToRootBounds_ property for the Popup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Popup_VerticalOffset">
      <summary>The _VerticalOffset_ property for the Popup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PopupThemeTransition_FromHorizontalOffset">
      <summary>The _FromHorizontalOffset_ property for the PopupThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PopupThemeTransition_FromVerticalOffset">
      <summary>The _FromVerticalOffset_ property for the PopupThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PowerEase_Power">
      <summary>The _Power_ property for the PowerEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PrintDocument_DocumentSource">
      <summary>The _DocumentSource_ property for the PrintDocument type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressBar_IsIndeterminate">
      <summary>The _IsIndeterminate_ property for the ProgressBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressBar_ShowError">
      <summary>The _ShowError_ property for the ProgressBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressBar_ShowPaused">
      <summary>The _ShowPaused_ property for the ProgressBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressBar_TemplateSettings">
      <summary>The _TemplateSettings_ property for the ProgressBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressBarTemplateSettings_ContainerAnimationEndPosition">
      <summary>The _ContainerAnimationEndPosition_ property for the ProgressBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressBarTemplateSettings_ContainerAnimationStartPosition">
      <summary>The _ContainerAnimationStartPosition_ property for the ProgressBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressBarTemplateSettings_EllipseAnimationEndPosition">
      <summary>The _EllipseAnimationEndPosition_ property for the ProgressBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressBarTemplateSettings_EllipseAnimationWellPosition">
      <summary>The _EllipseAnimationWellPosition_ property for the ProgressBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressBarTemplateSettings_EllipseDiameter">
      <summary>The _EllipseDiameter_ property for the ProgressBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressBarTemplateSettings_EllipseOffset">
      <summary>The _EllipseOffset_ property for the ProgressBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressBarTemplateSettings_IndicatorLengthDelta">
      <summary>The _IndicatorLengthDelta_ property for the ProgressBarTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressRing_IsActive">
      <summary>The _IsActive_ property for the ProgressRing type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressRing_TemplateSettings">
      <summary>The _TemplateSettings_ property for the ProgressRing type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressRingTemplateSettings_EllipseDiameter">
      <summary>The _EllipseDiameter_ property for the ProgressRingTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressRingTemplateSettings_EllipseOffset">
      <summary>The _EllipseOffset_ property for the ProgressRingTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ProgressRingTemplateSettings_MaxSideLength">
      <summary>The _MaxSideLength_ property for the ProgressRingTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.PropertyPath_Path">
      <summary>The _Path_ property for the PropertyPath type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.QuadraticBezierSegment_Point1">
      <summary>The _Point1_ property for the QuadraticBezierSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.QuadraticBezierSegment_Point2">
      <summary>The _Point2_ property for the QuadraticBezierSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RadioButton_GroupName">
      <summary>The _GroupName_ property for the RadioButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RangeBase_LargeChange">
      <summary>The _LargeChange_ property for the RangeBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RangeBase_Maximum">
      <summary>The _Maximum_ property for the RangeBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RangeBase_Minimum">
      <summary>The _Minimum_ property for the RangeBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RangeBase_SmallChange">
      <summary>The _SmallChange_ property for the RangeBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RangeBase_Value">
      <summary>The _Value_ property for the RangeBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Rectangle_RadiusX">
      <summary>The _RadiusX_ property for the Rectangle type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Rectangle_RadiusY">
      <summary>The _RadiusY_ property for the Rectangle type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RectangleGeometry_Rect">
      <summary>The _Rect_ property for the RectangleGeometry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_Above">
      <summary>The _Above_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_AlignBottomWith">
      <summary>The _AlignBottomWith_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_AlignBottomWithPanel">
      <summary>The _AlignBottomWithPanel_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_AlignHorizontalCenterWith">
      <summary>The _AlignHorizontalCenterWith_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_AlignHorizontalCenterWithPanel">
      <summary>The _AlignHorizontalCenterWithPanel_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_AlignLeftWith">
      <summary>The _AlignLeftWith_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_AlignLeftWithPanel">
      <summary>The _AlignLeftWithPanel_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_AlignRightWith">
      <summary>The _AlignRightWith_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_AlignRightWithPanel">
      <summary>The _AlignRightWithPanel_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_AlignTopWith">
      <summary>The _AlignTopWith_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_AlignTopWithPanel">
      <summary>The _AlignTopWithPanel_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_AlignVerticalCenterWith">
      <summary>The _AlignVerticalCenterWith_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_AlignVerticalCenterWithPanel">
      <summary>The _AlignVerticalCenterWithPanel_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_BackgroundSizing">
      <summary>The _BackgroundSizing_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_Below">
      <summary>The _Below_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_BorderBrush">
      <summary>The _BorderBrush_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_BorderThickness">
      <summary>The _BorderThickness_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_CornerRadius">
      <summary>The _CornerRadius_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_LeftOf">
      <summary>The _LeftOf_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_Padding">
      <summary>The _Padding_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativePanel_RightOf">
      <summary>The _RightOf_ property for the RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RelativeSource_Mode">
      <summary>The _Mode_ property for the RelativeSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RenderTargetBitmap_PixelHeight">
      <summary>The _PixelHeight_ property for the RenderTargetBitmap type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RenderTargetBitmap_PixelWidth">
      <summary>The _PixelWidth_ property for the RenderTargetBitmap type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RepeatButton_Delay">
      <summary>The _Delay_ property for the RepeatButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RepeatButton_Interval">
      <summary>The _Interval_ property for the RepeatButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RepositionThemeAnimation_FromHorizontalOffset">
      <summary>The _FromHorizontalOffset_ property for the RepositionThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RepositionThemeAnimation_FromVerticalOffset">
      <summary>The _FromVerticalOffset_ property for the RepositionThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RepositionThemeAnimation_TargetName">
      <summary>The _TargetName_ property for the RepositionThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RepositionThemeTransition_IsStaggeringEnabled">
      <summary>The _IsStaggeringEnabled_ property for the RepositionThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ResourceDictionary_MergedDictionaries">
      <summary>The _MergedDictionaries_ property for the ResourceDictionary type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ResourceDictionary_Source">
      <summary>The _Source_ property for the ResourceDictionary type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ResourceDictionary_ThemeDictionaries">
      <summary>The _ThemeDictionaries_ property for the ResourceDictionary type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_AcceptsReturn">
      <summary>The _AcceptsReturn_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_CharacterCasing">
      <summary>The _CharacterCasing_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_ClipboardCopyFormat">
      <summary>The _ClipboardCopyFormat_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_ContentLinkBackgroundColor">
      <summary>The _ContentLinkBackgroundColor_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_ContentLinkForegroundColor">
      <summary>The _ContentLinkForegroundColor_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_ContentLinkProviders">
      <summary>The _ContentLinkProviders_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_Description">
      <summary>The _Description_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_DesiredCandidateWindowAlignment">
      <summary>The _DesiredCandidateWindowAlignment_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_DisabledFormattingAccelerators">
      <summary>The _DisabledFormattingAccelerators_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_HandwritingView">
      <summary>The _HandwritingView_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_Header">
      <summary>The _Header_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_HeaderTemplate">
      <summary>The _HeaderTemplate_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_HorizontalTextAlignment">
      <summary>The _HorizontalTextAlignment_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_InputScope">
      <summary>The _InputScope_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_IsColorFontEnabled">
      <summary>The _IsColorFontEnabled_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_IsHandwritingViewEnabled">
      <summary>The _IsHandwritingViewEnabled_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_IsReadOnly">
      <summary>The _IsReadOnly_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_IsSpellCheckEnabled">
      <summary>The _IsSpellCheckEnabled_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_IsTextPredictionEnabled">
      <summary>The _IsTextPredictionEnabled_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_MaxLength">
      <summary>The _MaxLength_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_PlaceholderText">
      <summary>The _PlaceholderText_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_PreventKeyboardDisplayOnProgrammaticFocus">
      <summary>The _PreventKeyboardDisplayOnProgrammaticFocus_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_ProofingMenuFlyout">
      <summary>The _ProofingMenuFlyout_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_SelectionFlyout">
      <summary>The _SelectionFlyout_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_SelectionHighlightColor">
      <summary>The _SelectionHighlightColor_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_SelectionHighlightColorWhenNotFocused">
      <summary>The _SelectionHighlightColorWhenNotFocused_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_TextAlignment">
      <summary>The _TextAlignment_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_TextReadingOrder">
      <summary>The _TextReadingOrder_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichEditBox_TextWrapping">
      <summary>The _TextWrapping_ property for the RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_Blocks">
      <summary>The _Blocks_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_CharacterSpacing">
      <summary>The _CharacterSpacing_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_FontFamily">
      <summary>The _FontFamily_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_FontSize">
      <summary>The _FontSize_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_FontStretch">
      <summary>The _FontStretch_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_FontStyle">
      <summary>The _FontStyle_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_FontWeight">
      <summary>The _FontWeight_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_Foreground">
      <summary>The _Foreground_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_HasOverflowContent">
      <summary>The _HasOverflowContent_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_HorizontalTextAlignment">
      <summary>The _HorizontalTextAlignment_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_IsColorFontEnabled">
      <summary>The _IsColorFontEnabled_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_IsTextScaleFactorEnabled">
      <summary>The _IsTextScaleFactorEnabled_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_IsTextSelectionEnabled">
      <summary>The _IsTextSelectionEnabled_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_IsTextTrimmed">
      <summary>The _IsTextTrimmed_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_LineHeight">
      <summary>The _LineHeight_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_LineStackingStrategy">
      <summary>The _LineStackingStrategy_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_MaxLines">
      <summary>The _MaxLines_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_OpticalMarginAlignment">
      <summary>The _OpticalMarginAlignment_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_OverflowContentTarget">
      <summary>The _OverflowContentTarget_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_Padding">
      <summary>The _Padding_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_SelectedText">
      <summary>The _SelectedText_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_SelectionFlyout">
      <summary>The _SelectionFlyout_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_SelectionHighlightColor">
      <summary>The _SelectionHighlightColor_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_TextAlignment">
      <summary>The _TextAlignment_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_TextDecorations">
      <summary>The _TextDecorations_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_TextHighlighters">
      <summary>The _TextHighlighters_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_TextIndent">
      <summary>The _TextIndent_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_TextLineBounds">
      <summary>The _TextLineBounds_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_TextReadingOrder">
      <summary>The _TextReadingOrder_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_TextTrimming">
      <summary>The _TextTrimming_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlock_TextWrapping">
      <summary>The _TextWrapping_ property for the RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlockOverflow_HasOverflowContent">
      <summary>The _HasOverflowContent_ property for the RichTextBlockOverflow type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlockOverflow_IsTextTrimmed">
      <summary>The _IsTextTrimmed_ property for the RichTextBlockOverflow type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlockOverflow_MaxLines">
      <summary>The _MaxLines_ property for the RichTextBlockOverflow type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlockOverflow_OverflowContentTarget">
      <summary>The _OverflowContentTarget_ property for the RichTextBlockOverflow type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RichTextBlockOverflow_Padding">
      <summary>The _Padding_ property for the RichTextBlockOverflow type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RotateTransform_Angle">
      <summary>The _Angle_ property for the RotateTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RotateTransform_CenterX">
      <summary>The _CenterX_ property for the RotateTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RotateTransform_CenterY">
      <summary>The _CenterY_ property for the RotateTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RowDefinition_ActualHeight">
      <summary>The _ActualHeight_ property for the RowDefinition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RowDefinition_Height">
      <summary>The _Height_ property for the RowDefinition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RowDefinition_MaxHeight">
      <summary>The _MaxHeight_ property for the RowDefinition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.RowDefinition_MinHeight">
      <summary>The _MinHeight_ property for the RowDefinition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Run_FlowDirection">
      <summary>The _FlowDirection_ property for the Run type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Run_Text">
      <summary>The _Text_ property for the Run type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScalarTransition_Duration">
      <summary>The _Duration_ property for the ScalarTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScaleTransform_CenterX">
      <summary>The _CenterX_ property for the ScaleTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScaleTransform_CenterY">
      <summary>The _CenterY_ property for the ScaleTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScaleTransform_ScaleX">
      <summary>The _ScaleX_ property for the ScaleTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScaleTransform_ScaleY">
      <summary>The _ScaleY_ property for the ScaleTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollBar_IndicatorMode">
      <summary>The _IndicatorMode_ property for the ScrollBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollBar_Orientation">
      <summary>The _Orientation_ property for the ScrollBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollBar_ViewportSize">
      <summary>The _ViewportSize_ property for the ScrollBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollContentPresenter_CanContentRenderOutsideBounds">
      <summary>The _CanContentRenderOutsideBounds_ property for the ScrollContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollContentPresenter_SizesContentToTemplatedParent">
      <summary>The _SizesContentToTemplatedParent_ property for the ScrollContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_BringIntoViewOnFocusChange">
      <summary>The _BringIntoViewOnFocusChange_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_CanContentRenderOutsideBounds">
      <summary>The _CanContentRenderOutsideBounds_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_ComputedHorizontalScrollBarVisibility">
      <summary>The _ComputedHorizontalScrollBarVisibility_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_ComputedVerticalScrollBarVisibility">
      <summary>The _ComputedVerticalScrollBarVisibility_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_ExtentHeight">
      <summary>The _ExtentHeight_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_ExtentWidth">
      <summary>The _ExtentWidth_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_HorizontalAnchorRatio">
      <summary>The _HorizontalAnchorRatio_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_HorizontalOffset">
      <summary>The _HorizontalOffset_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_HorizontalScrollBarVisibility">
      <summary>The _HorizontalScrollBarVisibility_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_HorizontalScrollMode">
      <summary>The _HorizontalScrollMode_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_HorizontalSnapPointsAlignment">
      <summary>The _HorizontalSnapPointsAlignment_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_HorizontalSnapPointsType">
      <summary>The _HorizontalSnapPointsType_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_IsDeferredScrollingEnabled">
      <summary>The _IsDeferredScrollingEnabled_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_IsHorizontalRailEnabled">
      <summary>The _IsHorizontalRailEnabled_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_IsHorizontalScrollChainingEnabled">
      <summary>The _IsHorizontalScrollChainingEnabled_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_IsScrollInertiaEnabled">
      <summary>The _IsScrollInertiaEnabled_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_IsVerticalRailEnabled">
      <summary>The _IsVerticalRailEnabled_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_IsVerticalScrollChainingEnabled">
      <summary>The _IsVerticalScrollChainingEnabled_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_IsZoomChainingEnabled">
      <summary>The _IsZoomChainingEnabled_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_IsZoomInertiaEnabled">
      <summary>The _IsZoomInertiaEnabled_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_LeftHeader">
      <summary>The _LeftHeader_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_MaxZoomFactor">
      <summary>The _MaxZoomFactor_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_MinZoomFactor">
      <summary>The _MinZoomFactor_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_ReduceViewportForCoreInputViewOcclusions">
      <summary>The _ReduceViewportForCoreInputViewOcclusions_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_ScrollableHeight">
      <summary>The _ScrollableHeight_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_ScrollableWidth">
      <summary>The _ScrollableWidth_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_TopHeader">
      <summary>The _TopHeader_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_TopLeftHeader">
      <summary>The _TopLeftHeader_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_VerticalAnchorRatio">
      <summary>The _VerticalAnchorRatio_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_VerticalOffset">
      <summary>The _VerticalOffset_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_VerticalScrollBarVisibility">
      <summary>The _VerticalScrollBarVisibility_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_VerticalScrollMode">
      <summary>The _VerticalScrollMode_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_VerticalSnapPointsAlignment">
      <summary>The _VerticalSnapPointsAlignment_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_VerticalSnapPointsType">
      <summary>The _VerticalSnapPointsType_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_ViewportHeight">
      <summary>The _ViewportHeight_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_ViewportWidth">
      <summary>The _ViewportWidth_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_ZoomFactor">
      <summary>The _ZoomFactor_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_ZoomMode">
      <summary>The _ZoomMode_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_ZoomSnapPoints">
      <summary>The _ZoomSnapPoints_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ScrollViewer_ZoomSnapPointsType">
      <summary>The _ZoomSnapPointsType_ property for the ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SearchBox_ChooseSuggestionOnEnter">
      <summary>The _ChooseSuggestionOnEnter_ property for the SearchBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SearchBox_FocusOnKeyboardInput">
      <summary>The _FocusOnKeyboardInput_ property for the SearchBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SearchBox_PlaceholderText">
      <summary>The _PlaceholderText_ property for the SearchBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SearchBox_QueryText">
      <summary>The _QueryText_ property for the SearchBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SearchBox_SearchHistoryContext">
      <summary>The _SearchHistoryContext_ property for the SearchBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SearchBox_SearchHistoryEnabled">
      <summary>The _SearchHistoryEnabled_ property for the SearchBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Selector_IsSynchronizedWithCurrentItem">
      <summary>The _IsSynchronizedWithCurrentItem_ property for the Selector type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Selector_SelectedIndex">
      <summary>The _SelectedIndex_ property for the Selector type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Selector_SelectedItem">
      <summary>The _SelectedItem_ property for the Selector type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Selector_SelectedValue">
      <summary>The _SelectedValue_ property for the Selector type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Selector_SelectedValuePath">
      <summary>The _SelectedValuePath_ property for the Selector type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SelectorItem_IsSelected">
      <summary>The _IsSelected_ property for the SelectorItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SemanticZoom_CanChangeViews">
      <summary>The _CanChangeViews_ property for the SemanticZoom type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SemanticZoom_IsZoomedInViewActive">
      <summary>The _IsZoomedInViewActive_ property for the SemanticZoom type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SemanticZoom_IsZoomOutButtonEnabled">
      <summary>The _IsZoomOutButtonEnabled_ property for the SemanticZoom type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SemanticZoom_ZoomedInView">
      <summary>The _ZoomedInView_ property for the SemanticZoom type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SemanticZoom_ZoomedOutView">
      <summary>The _ZoomedOutView_ property for the SemanticZoom type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Setter_Property">
      <summary>The _Property_ property for the Setter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Setter_Target">
      <summary>The _Target_ property for the Setter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Setter_Value">
      <summary>The _Value_ property for the Setter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SetterBase_IsSealed">
      <summary>The _IsSealed_ property for the SetterBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SetterBaseCollection_IsSealed">
      <summary>The _IsSealed_ property for the SetterBaseCollection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SettingsFlyout_HeaderBackground">
      <summary>The _HeaderBackground_ property for the SettingsFlyout type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SettingsFlyout_HeaderForeground">
      <summary>The _HeaderForeground_ property for the SettingsFlyout type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SettingsFlyout_IconSource">
      <summary>The _IconSource_ property for the SettingsFlyout type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SettingsFlyout_TemplateSettings">
      <summary>The _TemplateSettings_ property for the SettingsFlyout type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SettingsFlyout_Title">
      <summary>The _Title_ property for the SettingsFlyout type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SettingsFlyoutTemplateSettings_BorderBrush">
      <summary>The _BorderBrush_ property for the SettingsFlyoutTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SettingsFlyoutTemplateSettings_BorderThickness">
      <summary>The _BorderThickness_ property for the SettingsFlyoutTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SettingsFlyoutTemplateSettings_ContentTransitions">
      <summary>The _ContentTransitions_ property for the SettingsFlyoutTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SettingsFlyoutTemplateSettings_HeaderBackground">
      <summary>The _HeaderBackground_ property for the SettingsFlyoutTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SettingsFlyoutTemplateSettings_HeaderForeground">
      <summary>The _HeaderForeground_ property for the SettingsFlyoutTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SettingsFlyoutTemplateSettings_IconSource">
      <summary>The _IconSource_ property for the SettingsFlyoutTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Shape_Fill">
      <summary>The _Fill_ property for the Shape type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Shape_GeometryTransform">
      <summary>The _GeometryTransform_ property for the Shape type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Shape_Stretch">
      <summary>The _Stretch_ property for the Shape type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Shape_Stroke">
      <summary>The _Stroke_ property for the Shape type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Shape_StrokeDashArray">
      <summary>The _StrokeDashArray_ property for the Shape type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Shape_StrokeDashCap">
      <summary>The _StrokeDashCap_ property for the Shape type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Shape_StrokeDashOffset">
      <summary>The _StrokeDashOffset_ property for the Shape type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Shape_StrokeEndLineCap">
      <summary>The _StrokeEndLineCap_ property for the Shape type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Shape_StrokeLineJoin">
      <summary>The _StrokeLineJoin_ property for the Shape type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Shape_StrokeMiterLimit">
      <summary>The _StrokeMiterLimit_ property for the Shape type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Shape_StrokeStartLineCap">
      <summary>The _StrokeStartLineCap_ property for the Shape type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Shape_StrokeThickness">
      <summary>The _StrokeThickness_ property for the Shape type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SkewTransform_AngleX">
      <summary>The _AngleX_ property for the SkewTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SkewTransform_AngleY">
      <summary>The _AngleY_ property for the SkewTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SkewTransform_CenterX">
      <summary>The _CenterX_ property for the SkewTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SkewTransform_CenterY">
      <summary>The _CenterY_ property for the SkewTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Slider_Header">
      <summary>The _Header_ property for the Slider type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Slider_HeaderTemplate">
      <summary>The _HeaderTemplate_ property for the Slider type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Slider_IntermediateValue">
      <summary>The _IntermediateValue_ property for the Slider type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Slider_IsDirectionReversed">
      <summary>The _IsDirectionReversed_ property for the Slider type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Slider_IsThumbToolTipEnabled">
      <summary>The _IsThumbToolTipEnabled_ property for the Slider type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Slider_Orientation">
      <summary>The _Orientation_ property for the Slider type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Slider_SnapsTo">
      <summary>The _SnapsTo_ property for the Slider type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Slider_StepFrequency">
      <summary>The _StepFrequency_ property for the Slider type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Slider_ThumbToolTipValueConverter">
      <summary>The _ThumbToolTipValueConverter_ property for the Slider type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Slider_TickFrequency">
      <summary>The _TickFrequency_ property for the Slider type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Slider_TickPlacement">
      <summary>The _TickPlacement_ property for the Slider type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SolidColorBrush_Color">
      <summary>The _Color_ property for the SolidColorBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Span_Inlines">
      <summary>The _Inlines_ property for the Span type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplineColorKeyFrame_KeySpline">
      <summary>The _KeySpline_ property for the SplineColorKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplineDoubleKeyFrame_KeySpline">
      <summary>The _KeySpline_ property for the SplineDoubleKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplinePointKeyFrame_KeySpline">
      <summary>The _KeySpline_ property for the SplinePointKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitCloseThemeAnimation_ClosedLength">
      <summary>The _ClosedLength_ property for the SplitCloseThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitCloseThemeAnimation_ClosedTarget">
      <summary>The _ClosedTarget_ property for the SplitCloseThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitCloseThemeAnimation_ClosedTargetName">
      <summary>The _ClosedTargetName_ property for the SplitCloseThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitCloseThemeAnimation_ContentTarget">
      <summary>The _ContentTarget_ property for the SplitCloseThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitCloseThemeAnimation_ContentTargetName">
      <summary>The _ContentTargetName_ property for the SplitCloseThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitCloseThemeAnimation_ContentTranslationDirection">
      <summary>The _ContentTranslationDirection_ property for the SplitCloseThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitCloseThemeAnimation_ContentTranslationOffset">
      <summary>The _ContentTranslationOffset_ property for the SplitCloseThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitCloseThemeAnimation_OffsetFromCenter">
      <summary>The _OffsetFromCenter_ property for the SplitCloseThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitCloseThemeAnimation_OpenedLength">
      <summary>The _OpenedLength_ property for the SplitCloseThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitCloseThemeAnimation_OpenedTarget">
      <summary>The _OpenedTarget_ property for the SplitCloseThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitCloseThemeAnimation_OpenedTargetName">
      <summary>The _OpenedTargetName_ property for the SplitCloseThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitOpenThemeAnimation_ClosedLength">
      <summary>The _ClosedLength_ property for the SplitOpenThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitOpenThemeAnimation_ClosedTarget">
      <summary>The _ClosedTarget_ property for the SplitOpenThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitOpenThemeAnimation_ClosedTargetName">
      <summary>The _ClosedTargetName_ property for the SplitOpenThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitOpenThemeAnimation_ContentTarget">
      <summary>The _ContentTarget_ property for the SplitOpenThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitOpenThemeAnimation_ContentTargetName">
      <summary>The _ContentTargetName_ property for the SplitOpenThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitOpenThemeAnimation_ContentTranslationDirection">
      <summary>The _ContentTranslationDirection_ property for the SplitOpenThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitOpenThemeAnimation_ContentTranslationOffset">
      <summary>The _ContentTranslationOffset_ property for the SplitOpenThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitOpenThemeAnimation_OffsetFromCenter">
      <summary>The _OffsetFromCenter_ property for the SplitOpenThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitOpenThemeAnimation_OpenedLength">
      <summary>The _OpenedLength_ property for the SplitOpenThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitOpenThemeAnimation_OpenedTarget">
      <summary>The _OpenedTarget_ property for the SplitOpenThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitOpenThemeAnimation_OpenedTargetName">
      <summary>The _OpenedTargetName_ property for the SplitOpenThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitView_CompactPaneLength">
      <summary>The _CompactPaneLength_ property for the SplitView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitView_Content">
      <summary>The _Content_ property for the SplitView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitView_DisplayMode">
      <summary>The _DisplayMode_ property for the SplitView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitView_IsPaneOpen">
      <summary>The _IsPaneOpen_ property for the SplitView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitView_LightDismissOverlayMode">
      <summary>The _LightDismissOverlayMode_ property for the SplitView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitView_OpenPaneLength">
      <summary>The _OpenPaneLength_ property for the SplitView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitView_Pane">
      <summary>The _Pane_ property for the SplitView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitView_PaneBackground">
      <summary>The _PaneBackground_ property for the SplitView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitView_PanePlacement">
      <summary>The _PanePlacement_ property for the SplitView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitView_TemplateSettings">
      <summary>The _TemplateSettings_ property for the SplitView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitViewTemplateSettings_CompactPaneGridLength">
      <summary>The _CompactPaneGridLength_ property for the SplitViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitViewTemplateSettings_NegativeOpenPaneLength">
      <summary>The _NegativeOpenPaneLength_ property for the SplitViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitViewTemplateSettings_NegativeOpenPaneLengthMinusCompactLength">
      <summary>The _NegativeOpenPaneLengthMinusCompactLength_ property for the SplitViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitViewTemplateSettings_OpenPaneGridLength">
      <summary>The _OpenPaneGridLength_ property for the SplitViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitViewTemplateSettings_OpenPaneLength">
      <summary>The _OpenPaneLength_ property for the SplitViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SplitViewTemplateSettings_OpenPaneLengthMinusCompactLength">
      <summary>The _OpenPaneLengthMinusCompactLength_ property for the SplitViewTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.StackPanel_AreScrollSnapPointsRegular">
      <summary>The _AreScrollSnapPointsRegular_ property for the StackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.StackPanel_BackgroundSizing">
      <summary>The _BackgroundSizing_ property for the StackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.StackPanel_BorderBrush">
      <summary>The _BorderBrush_ property for the StackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.StackPanel_BorderThickness">
      <summary>The _BorderThickness_ property for the StackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.StackPanel_CornerRadius">
      <summary>The _CornerRadius_ property for the StackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.StackPanel_Orientation">
      <summary>The _Orientation_ property for the StackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.StackPanel_Padding">
      <summary>The _Padding_ property for the StackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.StackPanel_Spacing">
      <summary>The _Spacing_ property for the StackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.StandardUICommand_Kind">
      <summary>The _Kind_ property for the StandardUICommand type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.StateTrigger_IsActive">
      <summary>The _IsActive_ property for the StateTrigger type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Storyboard_Children">
      <summary>The _Children_ property for the Storyboard type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Storyboard_TargetName">
      <summary>The _TargetName_ property for the Storyboard type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Storyboard_TargetProperty">
      <summary>The _TargetProperty_ property for the Storyboard type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Style_BasedOn">
      <summary>The _BasedOn_ property for the Style type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Style_IsSealed">
      <summary>The _IsSealed_ property for the Style type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Style_Setters">
      <summary>The _Setters_ property for the Style type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Style_TargetType">
      <summary>The _TargetType_ property for the Style type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SvgImageSource_RasterizePixelHeight">
      <summary>The _RasterizePixelHeight_ property for the SvgImageSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SvgImageSource_RasterizePixelWidth">
      <summary>The _RasterizePixelWidth_ property for the SvgImageSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SvgImageSource_UriSource">
      <summary>The _UriSource_ property for the SvgImageSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SwapChainPanel_CompositionScaleX">
      <summary>The _CompositionScaleX_ property for the SwapChainPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SwapChainPanel_CompositionScaleY">
      <summary>The _CompositionScaleY_ property for the SwapChainPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SwipeBackThemeAnimation_FromHorizontalOffset">
      <summary>The _FromHorizontalOffset_ property for the SwipeBackThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SwipeBackThemeAnimation_FromVerticalOffset">
      <summary>The _FromVerticalOffset_ property for the SwipeBackThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SwipeBackThemeAnimation_TargetName">
      <summary>The _TargetName_ property for the SwipeBackThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SwipeHintThemeAnimation_TargetName">
      <summary>The _TargetName_ property for the SwipeHintThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SwipeHintThemeAnimation_ToHorizontalOffset">
      <summary>The _ToHorizontalOffset_ property for the SwipeHintThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SwipeHintThemeAnimation_ToVerticalOffset">
      <summary>The _ToVerticalOffset_ property for the SwipeHintThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SymbolIcon_Symbol">
      <summary>The _Symbol_ property for the SymbolIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.SymbolIconSource_Symbol">
      <summary>The _Symbol_ property for the SymbolIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TargetPropertyPath_Path">
      <summary>The _Path_ property for the TargetPropertyPath type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TargetPropertyPath_Target">
      <summary>The _Target_ property for the TargetPropertyPath type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_CharacterSpacing">
      <summary>The _CharacterSpacing_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_FontFamily">
      <summary>The _FontFamily_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_FontSize">
      <summary>The _FontSize_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_FontStretch">
      <summary>The _FontStretch_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_FontStyle">
      <summary>The _FontStyle_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_FontWeight">
      <summary>The _FontWeight_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_Foreground">
      <summary>The _Foreground_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_HorizontalTextAlignment">
      <summary>The _HorizontalTextAlignment_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_Inlines">
      <summary>The _Inlines_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_IsColorFontEnabled">
      <summary>The _IsColorFontEnabled_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_IsTextScaleFactorEnabled">
      <summary>The _IsTextScaleFactorEnabled_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_IsTextSelectionEnabled">
      <summary>The _IsTextSelectionEnabled_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_IsTextTrimmed">
      <summary>The _IsTextTrimmed_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_LineHeight">
      <summary>The _LineHeight_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_LineStackingStrategy">
      <summary>The _LineStackingStrategy_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_MaxLines">
      <summary>The _MaxLines_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_OpticalMarginAlignment">
      <summary>The _OpticalMarginAlignment_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_Padding">
      <summary>The _Padding_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_SelectedText">
      <summary>The _SelectedText_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_SelectionFlyout">
      <summary>The _SelectionFlyout_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_SelectionHighlightColor">
      <summary>The _SelectionHighlightColor_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_Text">
      <summary>The _Text_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_TextAlignment">
      <summary>The _TextAlignment_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_TextDecorations">
      <summary>The _TextDecorations_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_TextHighlighters">
      <summary>The _TextHighlighters_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_TextLineBounds">
      <summary>The _TextLineBounds_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_TextReadingOrder">
      <summary>The _TextReadingOrder_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_TextTrimming">
      <summary>The _TextTrimming_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBlock_TextWrapping">
      <summary>The _TextWrapping_ property for the TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_AcceptsReturn">
      <summary>The _AcceptsReturn_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_CanPasteClipboardContent">
      <summary>The _CanPasteClipboardContent_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_CanRedo">
      <summary>The _CanRedo_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_CanUndo">
      <summary>The _CanUndo_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_CharacterCasing">
      <summary>The _CharacterCasing_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_Description">
      <summary>The _Description_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_DesiredCandidateWindowAlignment">
      <summary>The _DesiredCandidateWindowAlignment_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_HandwritingView">
      <summary>The _HandwritingView_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_Header">
      <summary>The _Header_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_HeaderTemplate">
      <summary>The _HeaderTemplate_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_HorizontalTextAlignment">
      <summary>The _HorizontalTextAlignment_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_InputScope">
      <summary>The _InputScope_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_IsColorFontEnabled">
      <summary>The _IsColorFontEnabled_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_IsHandwritingViewEnabled">
      <summary>The _IsHandwritingViewEnabled_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_IsReadOnly">
      <summary>The _IsReadOnly_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_IsSpellCheckEnabled">
      <summary>The _IsSpellCheckEnabled_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_IsTextPredictionEnabled">
      <summary>The _IsTextPredictionEnabled_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_MaxLength">
      <summary>The _MaxLength_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_PlaceholderForeground">
      <summary>The _PlaceholderForeground_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_PlaceholderText">
      <summary>The _PlaceholderText_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_PreventKeyboardDisplayOnProgrammaticFocus">
      <summary>The _PreventKeyboardDisplayOnProgrammaticFocus_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_ProofingMenuFlyout">
      <summary>The _ProofingMenuFlyout_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_SelectedText">
      <summary>The _SelectedText_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_SelectionFlyout">
      <summary>The _SelectionFlyout_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_SelectionHighlightColor">
      <summary>The _SelectionHighlightColor_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_SelectionHighlightColorWhenNotFocused">
      <summary>The _SelectionHighlightColorWhenNotFocused_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_SelectionLength">
      <summary>The _SelectionLength_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_SelectionStart">
      <summary>The _SelectionStart_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_Text">
      <summary>The _Text_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_TextAlignment">
      <summary>The _TextAlignment_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_TextReadingOrder">
      <summary>The _TextReadingOrder_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextBox_TextWrapping">
      <summary>The _TextWrapping_ property for the TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_AccessKey">
      <summary>The _AccessKey_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_AccessKeyScopeOwner">
      <summary>The _AccessKeyScopeOwner_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_AllowFocusOnInteraction">
      <summary>The _AllowFocusOnInteraction_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_CharacterSpacing">
      <summary>The _CharacterSpacing_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_ExitDisplayModeOnAccessKeyInvoked">
      <summary>The _ExitDisplayModeOnAccessKeyInvoked_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_FontFamily">
      <summary>The _FontFamily_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_FontSize">
      <summary>The _FontSize_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_FontStretch">
      <summary>The _FontStretch_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_FontStyle">
      <summary>The _FontStyle_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_FontWeight">
      <summary>The _FontWeight_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_Foreground">
      <summary>The _Foreground_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_IsAccessKeyScope">
      <summary>The _IsAccessKeyScope_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_IsTextScaleFactorEnabled">
      <summary>The _IsTextScaleFactorEnabled_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_KeyTipHorizontalOffset">
      <summary>The _KeyTipHorizontalOffset_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_KeyTipPlacementMode">
      <summary>The _KeyTipPlacementMode_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_KeyTipVerticalOffset">
      <summary>The _KeyTipVerticalOffset_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_Language">
      <summary>The _Language_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextElement_TextDecorations">
      <summary>The _TextDecorations_ property for the TextElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextHighlighter_Background">
      <summary>The _Background_ property for the TextHighlighter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextHighlighter_Foreground">
      <summary>The _Foreground_ property for the TextHighlighter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TextHighlighter_Ranges">
      <summary>The _Ranges_ property for the TextHighlighter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ThemeShadow_Receivers">
      <summary>The _Receivers_ property for the ThemeShadow type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Thumb_IsDragging">
      <summary>The _IsDragging_ property for the Thumb type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TickBar_Fill">
      <summary>The _Fill_ property for the TickBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TileBrush_AlignmentX">
      <summary>The _AlignmentX_ property for the TileBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TileBrush_AlignmentY">
      <summary>The _AlignmentY_ property for the TileBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TileBrush_Stretch">
      <summary>The _Stretch_ property for the TileBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Timeline_AutoReverse">
      <summary>The _AutoReverse_ property for the Timeline type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Timeline_BeginTime">
      <summary>The _BeginTime_ property for the Timeline type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Timeline_Duration">
      <summary>The _Duration_ property for the Timeline type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Timeline_FillBehavior">
      <summary>The _FillBehavior_ property for the Timeline type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Timeline_RepeatBehavior">
      <summary>The _RepeatBehavior_ property for the Timeline type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Timeline_SpeedRatio">
      <summary>The _SpeedRatio_ property for the Timeline type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TimelineMarker_Text">
      <summary>The _Text_ property for the TimelineMarker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TimelineMarker_Time">
      <summary>The _Time_ property for the TimelineMarker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TimelineMarker_Type">
      <summary>The _Type_ property for the TimelineMarker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TimePicker_ClockIdentifier">
      <summary>The _ClockIdentifier_ property for the TimePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TimePicker_Header">
      <summary>The _Header_ property for the TimePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TimePicker_HeaderTemplate">
      <summary>The _HeaderTemplate_ property for the TimePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TimePicker_LightDismissOverlayMode">
      <summary>The _LightDismissOverlayMode_ property for the TimePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TimePicker_MinuteIncrement">
      <summary>The _MinuteIncrement_ property for the TimePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TimePicker_SelectedTime">
      <summary>The _SelectedTime_ property for the TimePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TimePicker_Time">
      <summary>The _Time_ property for the TimePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleButton_IsChecked">
      <summary>The _IsChecked_ property for the ToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleButton_IsThreeState">
      <summary>The _IsThreeState_ property for the ToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleMenuFlyoutItem_IsChecked">
      <summary>The _IsChecked_ property for the ToggleMenuFlyoutItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitch_Header">
      <summary>The _Header_ property for the ToggleSwitch type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitch_HeaderTemplate">
      <summary>The _HeaderTemplate_ property for the ToggleSwitch type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitch_IsOn">
      <summary>The _IsOn_ property for the ToggleSwitch type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitch_OffContent">
      <summary>The _OffContent_ property for the ToggleSwitch type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitch_OffContentTemplate">
      <summary>The _OffContentTemplate_ property for the ToggleSwitch type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitch_OnContent">
      <summary>The _OnContent_ property for the ToggleSwitch type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitch_OnContentTemplate">
      <summary>The _OnContentTemplate_ property for the ToggleSwitch type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitch_TemplateSettings">
      <summary>The _TemplateSettings_ property for the ToggleSwitch type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitchTemplateSettings_CurtainCurrentToOffOffset">
      <summary>The _CurtainCurrentToOffOffset_ property for the ToggleSwitchTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitchTemplateSettings_CurtainCurrentToOnOffset">
      <summary>The _CurtainCurrentToOnOffset_ property for the ToggleSwitchTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitchTemplateSettings_CurtainOffToOnOffset">
      <summary>The _CurtainOffToOnOffset_ property for the ToggleSwitchTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitchTemplateSettings_CurtainOnToOffOffset">
      <summary>The _CurtainOnToOffOffset_ property for the ToggleSwitchTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitchTemplateSettings_KnobCurrentToOffOffset">
      <summary>The _KnobCurrentToOffOffset_ property for the ToggleSwitchTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitchTemplateSettings_KnobCurrentToOnOffset">
      <summary>The _KnobCurrentToOnOffset_ property for the ToggleSwitchTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitchTemplateSettings_KnobOffToOnOffset">
      <summary>The _KnobOffToOnOffset_ property for the ToggleSwitchTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToggleSwitchTemplateSettings_KnobOnToOffOffset">
      <summary>The _KnobOnToOffOffset_ property for the ToggleSwitchTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToolTip_HorizontalOffset">
      <summary>The _HorizontalOffset_ property for the ToolTip type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToolTip_IsOpen">
      <summary>The _IsOpen_ property for the ToolTip type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToolTip_Placement">
      <summary>The _Placement_ property for the ToolTip type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToolTip_PlacementRect">
      <summary>The _PlacementRect_ property for the ToolTip type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToolTip_PlacementTarget">
      <summary>The _PlacementTarget_ property for the ToolTip type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToolTip_TemplateSettings">
      <summary>The _TemplateSettings_ property for the ToolTip type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToolTip_VerticalOffset">
      <summary>The _VerticalOffset_ property for the ToolTip type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToolTipService_Placement">
      <summary>The _Placement_ property for the ToolTipService type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToolTipService_PlacementTarget">
      <summary>The _PlacementTarget_ property for the ToolTipService type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToolTipService_ToolTip">
      <summary>The _ToolTip_ property for the ToolTipService type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToolTipTemplateSettings_FromHorizontalOffset">
      <summary>The _FromHorizontalOffset_ property for the ToolTipTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.ToolTipTemplateSettings_FromVerticalOffset">
      <summary>The _FromVerticalOffset_ property for the ToolTipTemplateSettings type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TransformGroup_Children">
      <summary>The _Children_ property for the TransformGroup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TransformGroup_Value">
      <summary>The _Value_ property for the TransformGroup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TranslateTransform_X">
      <summary>The _X_ property for the TranslateTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.TranslateTransform_Y">
      <summary>The _Y_ property for the TranslateTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_AnnotationAlternates">
      <summary>The _AnnotationAlternates_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_Capitals">
      <summary>The _Capitals_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_CapitalSpacing">
      <summary>The _CapitalSpacing_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_CaseSensitiveForms">
      <summary>The _CaseSensitiveForms_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_ContextualAlternates">
      <summary>The _ContextualAlternates_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_ContextualLigatures">
      <summary>The _ContextualLigatures_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_ContextualSwashes">
      <summary>The _ContextualSwashes_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_DiscretionaryLigatures">
      <summary>The _DiscretionaryLigatures_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_EastAsianExpertForms">
      <summary>The _EastAsianExpertForms_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_EastAsianLanguage">
      <summary>The _EastAsianLanguage_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_EastAsianWidths">
      <summary>The _EastAsianWidths_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_Fraction">
      <summary>The _Fraction_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_HistoricalForms">
      <summary>The _HistoricalForms_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_HistoricalLigatures">
      <summary>The _HistoricalLigatures_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_Kerning">
      <summary>The _Kerning_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_MathematicalGreek">
      <summary>The _MathematicalGreek_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_NumeralAlignment">
      <summary>The _NumeralAlignment_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_NumeralStyle">
      <summary>The _NumeralStyle_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_SlashedZero">
      <summary>The _SlashedZero_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StandardLigatures">
      <summary>The _StandardLigatures_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StandardSwashes">
      <summary>The _StandardSwashes_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticAlternates">
      <summary>The _StylisticAlternates_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet1">
      <summary>The _StylisticSet1_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet10">
      <summary>The _StylisticSet10_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet11">
      <summary>The _StylisticSet11_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet12">
      <summary>The _StylisticSet12_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet13">
      <summary>The _StylisticSet13_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet14">
      <summary>The _StylisticSet14_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet15">
      <summary>The _StylisticSet15_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet16">
      <summary>The _StylisticSet16_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet17">
      <summary>The _StylisticSet17_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet18">
      <summary>The _StylisticSet18_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet19">
      <summary>The _StylisticSet19_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet2">
      <summary>The _StylisticSet2_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet20">
      <summary>The _StylisticSet20_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet3">
      <summary>The _StylisticSet3_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet4">
      <summary>The _StylisticSet4_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet5">
      <summary>The _StylisticSet5_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet6">
      <summary>The _StylisticSet6_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet7">
      <summary>The _StylisticSet7_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet8">
      <summary>The _StylisticSet8_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_StylisticSet9">
      <summary>The _StylisticSet9_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Typography_Variants">
      <summary>The _Variants_ property for the Typography type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_AccessKey">
      <summary>The _AccessKey_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_AccessKeyScopeOwner">
      <summary>The _AccessKeyScopeOwner_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_ActualOffset">
      <summary>The _ActualOffset_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_ActualSize">
      <summary>The _ActualSize_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_AllowDrop">
      <summary>The _AllowDrop_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_CacheMode">
      <summary>The _CacheMode_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_CanBeScrollAnchor">
      <summary>The _CanBeScrollAnchor_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_CanDrag">
      <summary>The _CanDrag_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_CenterPoint">
      <summary>The _CenterPoint_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_Clip">
      <summary>The _Clip_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_CompositeMode">
      <summary>The _CompositeMode_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_ContextFlyout">
      <summary>The _ContextFlyout_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_ExitDisplayModeOnAccessKeyInvoked">
      <summary>The _ExitDisplayModeOnAccessKeyInvoked_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_HighContrastAdjustment">
      <summary>The _HighContrastAdjustment_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_IsAccessKeyScope">
      <summary>The _IsAccessKeyScope_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_IsDoubleTapEnabled">
      <summary>The _IsDoubleTapEnabled_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_IsHitTestVisible">
      <summary>The _IsHitTestVisible_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_IsHoldingEnabled">
      <summary>The _IsHoldingEnabled_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_IsRightTapEnabled">
      <summary>The _IsRightTapEnabled_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_IsTapEnabled">
      <summary>The _IsTapEnabled_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_KeyboardAcceleratorPlacementMode">
      <summary>The _KeyboardAcceleratorPlacementMode_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_KeyboardAcceleratorPlacementTarget">
      <summary>The _KeyboardAcceleratorPlacementTarget_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_KeyboardAccelerators">
      <summary>The _KeyboardAccelerators_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_KeyTipHorizontalOffset">
      <summary>The _KeyTipHorizontalOffset_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_KeyTipPlacementMode">
      <summary>The _KeyTipPlacementMode_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_KeyTipTarget">
      <summary>The _KeyTipTarget_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_KeyTipVerticalOffset">
      <summary>The _KeyTipVerticalOffset_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_Lights">
      <summary>The _Lights_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_ManipulationMode">
      <summary>The _ManipulationMode_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_Opacity">
      <summary>The _Opacity_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_OpacityTransition">
      <summary>The _OpacityTransition_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_PointerCaptures">
      <summary>The _PointerCaptures_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_Projection">
      <summary>The _Projection_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_RenderSize">
      <summary>The _RenderSize_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_RenderTransform">
      <summary>The _RenderTransform_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_RenderTransformOrigin">
      <summary>The _RenderTransformOrigin_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_Rotation">
      <summary>The _Rotation_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_RotationAxis">
      <summary>The _RotationAxis_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_RotationTransition">
      <summary>The _RotationTransition_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_Scale">
      <summary>The _Scale_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_ScaleTransition">
      <summary>The _ScaleTransition_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_Shadow">
      <summary>The _Shadow_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_TabFocusNavigation">
      <summary>The _TabFocusNavigation_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_Transform3D">
      <summary>The _Transform3D_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_TransformMatrix">
      <summary>The _TransformMatrix_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_Transitions">
      <summary>The _Transitions_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_Translation">
      <summary>The _Translation_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_TranslationTransition">
      <summary>The _TranslationTransition_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_UseLayoutRounding">
      <summary>The _UseLayoutRounding_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_Visibility">
      <summary>The _Visibility_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_XYFocusDownNavigationStrategy">
      <summary>The _XYFocusDownNavigationStrategy_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_XYFocusKeyboardNavigation">
      <summary>The _XYFocusKeyboardNavigation_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_XYFocusLeftNavigationStrategy">
      <summary>The _XYFocusLeftNavigationStrategy_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_XYFocusRightNavigationStrategy">
      <summary>The _XYFocusRightNavigationStrategy_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UIElement_XYFocusUpNavigationStrategy">
      <summary>The _XYFocusUpNavigationStrategy_ property for the UIElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.UserControl_Content">
      <summary>The _Content_ property for the UserControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VariableSizedWrapGrid_ColumnSpan">
      <summary>The _ColumnSpan_ property for the VariableSizedWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VariableSizedWrapGrid_HorizontalChildrenAlignment">
      <summary>The _HorizontalChildrenAlignment_ property for the VariableSizedWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VariableSizedWrapGrid_ItemHeight">
      <summary>The _ItemHeight_ property for the VariableSizedWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VariableSizedWrapGrid_ItemWidth">
      <summary>The _ItemWidth_ property for the VariableSizedWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VariableSizedWrapGrid_MaximumRowsOrColumns">
      <summary>The _MaximumRowsOrColumns_ property for the VariableSizedWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VariableSizedWrapGrid_Orientation">
      <summary>The _Orientation_ property for the VariableSizedWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VariableSizedWrapGrid_RowSpan">
      <summary>The _RowSpan_ property for the VariableSizedWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VariableSizedWrapGrid_VerticalChildrenAlignment">
      <summary>The _VerticalChildrenAlignment_ property for the VariableSizedWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Vector3Transition_Components">
      <summary>The _Components_ property for the Vector3Transition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Vector3Transition_Duration">
      <summary>The _Duration_ property for the Vector3Transition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Viewbox_Child">
      <summary>The _Child_ property for the Viewbox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Viewbox_Stretch">
      <summary>The _Stretch_ property for the Viewbox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.Viewbox_StretchDirection">
      <summary>The _StretchDirection_ property for the Viewbox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VirtualizingStackPanel_AreScrollSnapPointsRegular">
      <summary>The _AreScrollSnapPointsRegular_ property for the VirtualizingStackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VirtualizingStackPanel_IsVirtualizing">
      <summary>The _IsVirtualizing_ property for the VirtualizingStackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VirtualizingStackPanel_Orientation">
      <summary>The _Orientation_ property for the VirtualizingStackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VirtualizingStackPanel_VirtualizationMode">
      <summary>The _VirtualizationMode_ property for the VirtualizingStackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VisualState_Setters">
      <summary>The _Setters_ property for the VisualState type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VisualState_StateTriggers">
      <summary>The _StateTriggers_ property for the VisualState type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VisualState_Storyboard">
      <summary>The _Storyboard_ property for the VisualState type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VisualStateGroup_States">
      <summary>The _States_ property for the VisualStateGroup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VisualStateGroup_Transitions">
      <summary>The _Transitions_ property for the VisualStateGroup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VisualStateManager_CustomVisualStateManager">
      <summary>The _CustomVisualStateManager_ property for the VisualStateManager type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VisualStateManager_VisualStateGroups">
      <summary>The _VisualStateGroups_ property for the VisualStateManager type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VisualTransition_From">
      <summary>The _From_ property for the VisualTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VisualTransition_GeneratedDuration">
      <summary>The _GeneratedDuration_ property for the VisualTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VisualTransition_GeneratedEasingFunction">
      <summary>The _GeneratedEasingFunction_ property for the VisualTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VisualTransition_Storyboard">
      <summary>The _Storyboard_ property for the VisualTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.VisualTransition_To">
      <summary>The _To_ property for the VisualTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_AllowedScriptNotifyUris">
      <summary>The _AllowedScriptNotifyUris_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_CanGoBack">
      <summary>The _CanGoBack_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_CanGoForward">
      <summary>The _CanGoForward_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_ContainsFullScreenElement">
      <summary>The _ContainsFullScreenElement_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_DataTransferPackage">
      <summary>The _DataTransferPackage_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_DefaultBackgroundColor">
      <summary>The _DefaultBackgroundColor_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_DeferredPermissionRequests">
      <summary>The _DeferredPermissionRequests_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_DocumentTitle">
      <summary>The _DocumentTitle_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_ExecutionMode">
      <summary>The _ExecutionMode_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_Settings">
      <summary>The _Settings_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_Source">
      <summary>The _Source_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_XYFocusDown">
      <summary>The _XYFocusDown_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_XYFocusLeft">
      <summary>The _XYFocusLeft_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_XYFocusRight">
      <summary>The _XYFocusRight_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebView_XYFocusUp">
      <summary>The _XYFocusUp_ property for the WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WebViewBrush_SourceName">
      <summary>The _SourceName_ property for the WebViewBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WrapGrid_HorizontalChildrenAlignment">
      <summary>The _HorizontalChildrenAlignment_ property for the WrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WrapGrid_ItemHeight">
      <summary>The _ItemHeight_ property for the WrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WrapGrid_ItemWidth">
      <summary>The _ItemWidth_ property for the WrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WrapGrid_MaximumRowsOrColumns">
      <summary>The _MaximumRowsOrColumns_ property for the WrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WrapGrid_Orientation">
      <summary>The _Orientation_ property for the WrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.WrapGrid_VerticalChildrenAlignment">
      <summary>The _VerticalChildrenAlignment_ property for the WrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.XamlBindingHelper_DataTemplateComponent">
      <summary>The _DataTemplateComponent_ property for the XamlBindingHelper type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.XamlCompositionBrushBase_FallbackColor">
      <summary>The _FallbackColor_ property for the XamlCompositionBrushBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.XamlUICommand_AccessKey">
      <summary>The _AccessKey_ property for the XamlUICommand type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.XamlUICommand_Command">
      <summary>The _Command_ property for the XamlUICommand type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.XamlUICommand_Description">
      <summary>The _Description_ property for the XamlUICommand type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.XamlUICommand_IconSource">
      <summary>The _IconSource_ property for the XamlUICommand type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.XamlUICommand_KeyboardAccelerators">
      <summary>The _KeyboardAccelerators_ property for the XamlUICommand type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlPropertyIndex.XamlUICommand_Label">
      <summary>The _Label_ property for the XamlUICommand type.</summary>
    </member>
    <member name="T:Windows.UI.Xaml.Core.Direct.XamlTypeIndex">
      <summary>Enum that lists all the supported types in XamlDirect.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.AdaptiveTrigger">
      <summary>The AdaptiveTrigger type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.AddDeleteThemeTransition">
      <summary>The AddDeleteThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.AppBar">
      <summary>The AppBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.AppBarButton">
      <summary>The AppBarButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.AppBarElementContainer">
      <summary>The AppBarElementContainer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.AppBarSeparator">
      <summary>The AppBarSeparator type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.AppBarToggleButton">
      <summary>The AppBarToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ArcSegment">
      <summary>The ArcSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.AutomationAnnotation">
      <summary>The AutomationAnnotation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.AutomationPeerAnnotation">
      <summary>The AutomationPeerAnnotation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.AutoSuggestBox">
      <summary>The AutoSuggestBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.AutoSuggestBoxQuerySubmittedEventArgs">
      <summary>The AutoSuggestBoxQuerySubmittedEventArgs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.AutoSuggestBoxSuggestionChosenEventArgs">
      <summary>The AutoSuggestBoxSuggestionChosenEventArgs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.AutoSuggestBoxTextChangedEventArgs">
      <summary>The AutoSuggestBoxTextChangedEventArgs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.BackEase">
      <summary>The BackEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.BeginStoryboard">
      <summary>The BeginStoryboard type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.BezierSegment">
      <summary>The BezierSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Binding">
      <summary>The Binding type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.BindingBase">
      <summary>The BindingBase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.BitmapCache">
      <summary>The BitmapCache type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.BitmapIcon">
      <summary>The BitmapIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.BitmapIconSource">
      <summary>The BitmapIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.BitmapImage">
      <summary>The BitmapImage type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Bold">
      <summary>The Bold type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Border">
      <summary>The Border type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.BounceEase">
      <summary>The BounceEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Button">
      <summary>The Button type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.CalendarDatePicker">
      <summary>The CalendarDatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.CalendarPanel">
      <summary>The CalendarPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.CalendarView">
      <summary>The CalendarView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.CalendarViewDayItem">
      <summary>The CalendarViewDayItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Canvas">
      <summary>The Canvas type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.CaptureElement">
      <summary>The CaptureElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.CarouselPanel">
      <summary>The CarouselPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.CheckBox">
      <summary>The CheckBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.CircleEase">
      <summary>The CircleEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.CollectionViewSource">
      <summary>The CollectionViewSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ColorAnimation">
      <summary>The ColorAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ColorAnimationUsingKeyFrames">
      <summary>The ColorAnimationUsingKeyFrames type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ColorPaletteResources">
      <summary>The ColorPaletteResources type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ColumnDefinition">
      <summary>The ColumnDefinition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ComboBox">
      <summary>The ComboBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ComboBoxItem">
      <summary>The ComboBoxItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.CommandBar">
      <summary>The CommandBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.CommandBarOverflowPresenter">
      <summary>The CommandBarOverflowPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.CompositeTransform">
      <summary>The CompositeTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.CompositeTransform3D">
      <summary>The CompositeTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ContentControl">
      <summary>The ContentControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ContentDialog">
      <summary>The ContentDialog type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ContentLink">
      <summary>The ContentLink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ContentPresenter">
      <summary>The ContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ContentThemeTransition">
      <summary>The ContentThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ControlTemplate">
      <summary>The ControlTemplate type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.CubicEase">
      <summary>The CubicEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.DataTemplate">
      <summary>The DataTemplate type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.DatePicker">
      <summary>The DatePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.DependencyObjectCollection">
      <summary>The DependencyObjectCollection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.DiscreteColorKeyFrame">
      <summary>The DiscreteColorKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.DiscreteDoubleKeyFrame">
      <summary>The DiscreteDoubleKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.DiscreteObjectKeyFrame">
      <summary>The DiscreteObjectKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.DiscretePointKeyFrame">
      <summary>The DiscretePointKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.DoubleAnimation">
      <summary>The DoubleAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.DoubleAnimationUsingKeyFrames">
      <summary>The DoubleAnimationUsingKeyFrames type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.DragItemThemeAnimation">
      <summary>The DragItemThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.DragOverThemeAnimation">
      <summary>The DragOverThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.DrillInThemeAnimation">
      <summary>The DrillInThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.DrillOutThemeAnimation">
      <summary>The DrillOutThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.DropTargetItemThemeAnimation">
      <summary>The DropTargetItemThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.EasingColorKeyFrame">
      <summary>The EasingColorKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.EasingDoubleKeyFrame">
      <summary>The EasingDoubleKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.EasingPointKeyFrame">
      <summary>The EasingPointKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.EdgeUIThemeTransition">
      <summary>The EdgeUIThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ElasticEase">
      <summary>The ElasticEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Ellipse">
      <summary>The Ellipse type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.EllipseGeometry">
      <summary>The EllipseGeometry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.EntranceThemeTransition">
      <summary>The EntranceThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.EventTrigger">
      <summary>The EventTrigger type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ExponentialEase">
      <summary>The ExponentialEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.FadeInThemeAnimation">
      <summary>The FadeInThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.FadeOutThemeAnimation">
      <summary>The FadeOutThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.FlipView">
      <summary>The FlipView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.FlipViewItem">
      <summary>The FlipViewItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Flyout">
      <summary>The Flyout type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.FlyoutPresenter">
      <summary>The FlyoutPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.FontIcon">
      <summary>The FontIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.FontIconSource">
      <summary>The FontIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Frame">
      <summary>The Frame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.GeometryGroup">
      <summary>The GeometryGroup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Glyphs">
      <summary>The Glyphs type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.GradientStop">
      <summary>The GradientStop type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Grid">
      <summary>The Grid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.GridView">
      <summary>The GridView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.GridViewHeaderItem">
      <summary>The GridViewHeaderItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.GridViewItem">
      <summary>The GridViewItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.GridViewItemPresenter">
      <summary>The GridViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.GroupItem">
      <summary>The GroupItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.HandwritingView">
      <summary>The HandwritingView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Hub">
      <summary>The Hub type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.HubSection">
      <summary>The HubSection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Hyperlink">
      <summary>The Hyperlink type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.HyperlinkButton">
      <summary>The HyperlinkButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.IconSourceElement">
      <summary>The IconSourceElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Image">
      <summary>The Image type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ImageBrush">
      <summary>The ImageBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.InkCanvas">
      <summary>The InkCanvas type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.InlineUIContainer">
      <summary>The InlineUIContainer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.InputScope">
      <summary>The InputScope type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.InputScopeName">
      <summary>The InputScopeName type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Italic">
      <summary>The Italic type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ItemsControl">
      <summary>The ItemsControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ItemsPanelTemplate">
      <summary>The ItemsPanelTemplate type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ItemsPresenter">
      <summary>The ItemsPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ItemsStackPanel">
      <summary>The ItemsStackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ItemsWrapGrid">
      <summary>The ItemsWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.KeyboardAccelerator">
      <summary>The KeyboardAccelerator type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.KeySpline">
      <summary>The KeySpline type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Line">
      <summary>The Line type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.LinearColorKeyFrame">
      <summary>The LinearColorKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.LinearDoubleKeyFrame">
      <summary>The LinearDoubleKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.LinearGradientBrush">
      <summary>The LinearGradientBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.LinearPointKeyFrame">
      <summary>The LinearPointKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.LineBreak">
      <summary>The LineBreak type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.LineGeometry">
      <summary>The LineGeometry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.LineSegment">
      <summary>The LineSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ListBox">
      <summary>The ListBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ListBoxItem">
      <summary>The ListBoxItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ListView">
      <summary>The ListView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ListViewHeaderItem">
      <summary>The ListViewHeaderItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ListViewItem">
      <summary>The ListViewItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ListViewItemPresenter">
      <summary>The ListViewItemPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Matrix3DProjection">
      <summary>The Matrix3DProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.MatrixTransform">
      <summary>The MatrixTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.MediaElement">
      <summary>The MediaElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.MediaPlayerElement">
      <summary>The MediaPlayerElement type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.MediaPlayerPresenter">
      <summary>The MediaPlayerPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.MediaTransportControls">
      <summary>The MediaTransportControls type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.MenuFlyout">
      <summary>The MenuFlyout type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.MenuFlyoutItem">
      <summary>The MenuFlyoutItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.MenuFlyoutPresenter">
      <summary>The MenuFlyoutPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.MenuFlyoutSeparator">
      <summary>The MenuFlyoutSeparator type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.MenuFlyoutSubItem">
      <summary>The MenuFlyoutSubItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ObjectAnimationUsingKeyFrames">
      <summary>The ObjectAnimationUsingKeyFrames type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Page">
      <summary>The Page type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PaneThemeTransition">
      <summary>The PaneThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Paragraph">
      <summary>The Paragraph type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PasswordBox">
      <summary>The PasswordBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Path">
      <summary>The Path type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PathFigure">
      <summary>The PathFigure type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PathGeometry">
      <summary>The PathGeometry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PathIcon">
      <summary>The PathIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PathIconSource">
      <summary>The PathIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PerspectiveTransform3D">
      <summary>The PerspectiveTransform3D type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PlaneProjection">
      <summary>The PlaneProjection type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PointAnimation">
      <summary>The PointAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PointAnimationUsingKeyFrames">
      <summary>The PointAnimationUsingKeyFrames type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PointerDownThemeAnimation">
      <summary>The PointerDownThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PointerUpThemeAnimation">
      <summary>The PointerUpThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PolyBezierSegment">
      <summary>The PolyBezierSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Polygon">
      <summary>The Polygon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Polyline">
      <summary>The Polyline type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PolyLineSegment">
      <summary>The PolyLineSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PolyQuadraticBezierSegment">
      <summary>The PolyQuadraticBezierSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PopInThemeAnimation">
      <summary>The PopInThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PopOutThemeAnimation">
      <summary>The PopOutThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Popup">
      <summary>The Popup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PopupThemeTransition">
      <summary>The PopupThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PowerEase">
      <summary>The PowerEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.PrintDocument">
      <summary>The PrintDocument type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ProgressBar">
      <summary>The ProgressBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ProgressRing">
      <summary>The ProgressRing type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.QuadraticBezierSegment">
      <summary>The QuadraticBezierSegment type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.QuadraticEase">
      <summary>The QuadraticEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.QuarticEase">
      <summary>The QuarticEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.QuinticEase">
      <summary>The QuinticEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.RadioButton">
      <summary>The RadioButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Rectangle">
      <summary>The Rectangle type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.RectangleGeometry">
      <summary>The RectangleGeometry type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.RelativePanel">
      <summary>The RelativePanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.RelativeSource">
      <summary>The RelativeSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.RenderTargetBitmap">
      <summary>The RenderTargetBitmap type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ReorderThemeTransition">
      <summary>The ReorderThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.RepeatButton">
      <summary>The RepeatButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.RepositionThemeAnimation">
      <summary>The RepositionThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.RepositionThemeTransition">
      <summary>The RepositionThemeTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ResourceDictionary">
      <summary>The ResourceDictionary type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.RichEditBox">
      <summary>The RichEditBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.RichTextBlock">
      <summary>The RichTextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.RichTextBlockOverflow">
      <summary>The RichTextBlockOverflow type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.RotateTransform">
      <summary>The RotateTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.RowDefinition">
      <summary>The RowDefinition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Run">
      <summary>The Run type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ScaleTransform">
      <summary>The ScaleTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ScrollBar">
      <summary>The ScrollBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ScrollContentPresenter">
      <summary>The ScrollContentPresenter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ScrollViewer">
      <summary>The ScrollViewer type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SearchBox">
      <summary>The SearchBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SemanticZoom">
      <summary>The SemanticZoom type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Setter">
      <summary>The Setter type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SettingsFlyout">
      <summary>The SettingsFlyout type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SineEase">
      <summary>The SineEase type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SkewTransform">
      <summary>The SkewTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Slider">
      <summary>The Slider type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SoftwareBitmapSource">
      <summary>The SoftwareBitmapSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SolidColorBrush">
      <summary>The SolidColorBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Span">
      <summary>The Span type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SplineColorKeyFrame">
      <summary>The SplineColorKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SplineDoubleKeyFrame">
      <summary>The SplineDoubleKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SplinePointKeyFrame">
      <summary>The SplinePointKeyFrame type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SplitCloseThemeAnimation">
      <summary>The SplitCloseThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SplitOpenThemeAnimation">
      <summary>The SplitOpenThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SplitView">
      <summary>The SplitView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.StackPanel">
      <summary>The StackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.StandardUICommand">
      <summary>The StandardUICommand type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.StateTrigger">
      <summary>The StateTrigger type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Storyboard">
      <summary>The Storyboard type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Style">
      <summary>The Style type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SvgImageSource">
      <summary>The SvgImageSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SwapChainBackgroundPanel">
      <summary>The SwapChainBackgroundPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SwapChainPanel">
      <summary>The SwapChainPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SwipeBackThemeAnimation">
      <summary>The SwipeBackThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SwipeHintThemeAnimation">
      <summary>The SwipeHintThemeAnimation type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SymbolIcon">
      <summary>The SymbolIcon type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.SymbolIconSource">
      <summary>The SymbolIconSource type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.TextBlock">
      <summary>The TextBlock type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.TextBox">
      <summary>The TextBox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ThemeShadow">
      <summary>The ThemeShadow type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Thumb">
      <summary>The Thumb type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.TickBar">
      <summary>The TickBar type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.TimelineMarker">
      <summary>The TimelineMarker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.TimePicker">
      <summary>The TimePicker type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ToggleButton">
      <summary>The ToggleButton type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ToggleMenuFlyoutItem">
      <summary>The ToggleMenuFlyoutItem type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ToggleSwitch">
      <summary>The ToggleSwitch type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.ToolTip">
      <summary>The ToolTip type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.TransformGroup">
      <summary>The TransformGroup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.TranslateTransform">
      <summary>The TranslateTransform type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Underline">
      <summary>The Underline type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.UserControl">
      <summary>The UserControl type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.VariableSizedWrapGrid">
      <summary>The VariableSizedWrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.Viewbox">
      <summary>The Viewbox type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.VirtualizingStackPanel">
      <summary>The VirtualizingStackPanel type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.VisualState">
      <summary>The VisualState type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.VisualStateGroup">
      <summary>The VisualStateGroup type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.VisualStateManager">
      <summary>The VisualStateManager type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.VisualTransition">
      <summary>The VisualTransition type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.WebView">
      <summary>The WebView type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.WebViewBrush">
      <summary>The WebViewBrush type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.WrapGrid">
      <summary>The WrapGrid type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.XamlLight">
      <summary>The XamlLight type.</summary>
    </member>
    <member name="F:Windows.UI.Xaml.Core.Direct.XamlTypeIndex.XamlUICommand">
      <summary>The XamlUICommand type.</summary>
    </member>
  </members>
</doc>