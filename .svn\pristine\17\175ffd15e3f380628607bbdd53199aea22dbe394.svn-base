using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using OCRTools.Common;

namespace OCRTools
{
    /// <summary>
    /// 图像模式处理器抽象基类
    /// 提供通用的基础功能，减少重复代码
    /// </summary>
    public abstract class BaseImageModeHandler : IImageModeHandler
    {
        #region 受保护字段 - 子类可访问

        protected MultiModeImageViewer _viewer;
        protected List<TextCellInfo> _textCells = new List<TextCellInfo>();
        protected bool _isEventsBound = false;

        #endregion

        #region IImageModeHandler 基础实现

        public virtual void Initialize(MultiModeImageViewer viewer)
        {
            _viewer = viewer;
        }

        public abstract void Activate();
        public abstract void Deactivate();

        public virtual void BindData(Image image, List<TextCellInfo> textCells)
        {
            _textCells = textCells ?? new List<TextCellInfo>();

            // 清理当前状态
            ClearCurrentState();

            // 设置图像 - 子类可以重写此方法来实现特殊的图像处理逻辑
            SetImage(image);

            _viewer.BringToFront();
        }

        /// <summary>
        /// 设置图像 - 子类可以重写以实现特殊的图像处理逻辑
        /// </summary>
        protected virtual void SetImage(Image image)
        {
            _viewer.Image = image;
        }

        public abstract void HandleMouseDown(MouseEventArgs e);
        public abstract void HandleMouseMove(MouseEventArgs e);
        public abstract void HandleMouseUp(MouseEventArgs e);
        public abstract void HandleMouseLeave(EventArgs e);
        public abstract void HandlePaint(PaintEventArgs e);

        public virtual void HandleZoomChanged(EventArgs e)
        {
            // 缩放变化时重新绘制 - 通用逻辑
            _viewer?.Invalidate();
        }

        public virtual void HandleScroll(ScrollEventArgs e)
        {
            // 滚动时重新绘制 - 通用逻辑
            _viewer?.Invalidate();
        }

        public virtual void HandleMouseWheel(MouseEventArgs e)
        {
            // 默认不处理滚轮事件，子类可重写
        }

        public virtual void ClearState()
        {
            _textCells?.Clear();
            ClearCurrentState();
        }

        public virtual void Dispose()
        {
            Deactivate();
            _viewer = null;
            _textCells?.Clear();
            _textCells = null;
        }

        public virtual event EventHandler<EventArgs> ModeSpecificEvent;

        #endregion

        #region 受保护的通用方法 - 子类可使用

        /// <summary>
        /// 清理当前状态 - 子类实现具体的清理逻辑
        /// </summary>
        protected abstract void ClearCurrentState();

        /// <summary>
        /// 获取鼠标位置对应的图片坐标
        /// </summary>
        protected Point GetImagePointFromMouse()
        {
            var mouseLoc = _viewer.PointToClient(Cursor.Position);
            return _viewer.GetImagePointFromControl(mouseLoc);
        }

        /// <summary>
        /// 获取鼠标位置对应的图片坐标
        /// </summary>
        protected Point GetImagePointFromMouse(Point mouseLocation)
        {
            return _viewer.GetImagePointFromControl(mouseLocation);
        }

        /// <summary>
        /// 将图片矩形转换为控件矩形
        /// </summary>
        protected Rectangle GetControlRectFromImage(Rectangle imageRect)
        {
            return _viewer.GetControlRectFromImage(imageRect);
        }

        /// <summary>
        /// 查找指定图片坐标位置的文字区域
        /// </summary>
        protected TextCellInfo FindCellAtImagePoint(Point imagePoint)
        {
            return _textCells?.FirstOrDefault(cell =>
                cell?.location?.Rectangle.Contains(imagePoint) == true);
        }

        /// <summary>
        /// 安全地使文字区域失效重绘
        /// </summary>
        protected void InvalidateCell(TextCellInfo cell, int padding = 2)
        {
            if (cell?.location != null && _viewer != null)
            {
                var imageRect = cell.location.Rectangle.SizeOffset(padding);
                var controlRect = GetControlRectFromImage(imageRect);
                _viewer.Invalidate(controlRect);
            }
        }

        /// <summary>
        /// 安全地使矩形区域失效重绘
        /// </summary>
        protected void InvalidateRegion(Rectangle region)
        {
            if (!region.IsEmpty && _viewer != null)
            {
                _viewer.Invalidate(region);
            }
        }

        #endregion

        #region 受保护的事件绑定辅助方法

        /// <summary>
        /// 绑定基础鼠标事件
        /// </summary>
        protected virtual void BindMouseEvents()
        {
            if (_viewer != null && !_isEventsBound)
            {
                _viewer.MouseDown += OnMouseDown;
                _viewer.MouseMove += OnMouseMove;
                _viewer.MouseUp += OnMouseUp;
                _viewer.MouseLeave += OnMouseLeave;
                _viewer.Paint += OnPaint;
                _isEventsBound = true;
            }
        }

        /// <summary>
        /// 解绑基础鼠标事件
        /// </summary>
        protected virtual void UnbindMouseEvents()
        {
            if (_viewer != null && _isEventsBound)
            {
                _viewer.MouseDown -= OnMouseDown;
                _viewer.MouseMove -= OnMouseMove;
                _viewer.MouseUp -= OnMouseUp;
                _viewer.MouseLeave -= OnMouseLeave;
                _viewer.Paint -= OnPaint;
                _isEventsBound = false;
            }
        }

        #endregion

        #region 私有事件处理 - 转发到抽象方法

        private void OnMouseDown(object sender, MouseEventArgs e)
        {
            HandleMouseDown(e);
        }

        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            HandleMouseMove(e);
        }

        private void OnMouseUp(object sender, MouseEventArgs e)
        {
            HandleMouseUp(e);
        }

        private void OnMouseLeave(object sender, EventArgs e)
        {
            HandleMouseLeave(e);
        }

        private void OnPaint(object sender, PaintEventArgs e)
        {
            HandlePaint(e);
        }

        #endregion
    }
}
