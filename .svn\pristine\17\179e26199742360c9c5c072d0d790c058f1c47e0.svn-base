﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace OCRTools
{
    public enum SkinButtonStopStates
    {
        Normal,
        Hover,
        Pressed,
        NoStop
    }
    public enum SkinButtonDrawStyle
    {
        None,
        Img,
        Draw
    }
    public enum SkinButtonRoundStyle
    {
        None = 0,
        TopLeft = 1,
        TopRight = 2,
        BottomLeft = 4,
        BottomRight = 8,
        Left = 5,
        Right = 10,
        Top = 3,
        Bottom = 12,
        All = 0xF
    }
    public enum SkinButtonState
    {
        Normal,
        Hover,
        Pressed,
        Focused
    }
    [ToolboxBitmap(typeof(Button))]
    public class SkinButton : Button
    {
        private class Control5 : Control
        {
            protected override void OnPaintBackground(PaintEventArgs e)
            {
            }

            protected override void OnPaint(PaintEventArgs e)
            {
            }

            public Control5()
            {


            }
        }

        private Timer timer_0;

        private Size size_0;

        private bool bool_0;

        private Color color_0;

        private Color color_1;

        private bool bool_1;

        private bool bool_3;

        private bool bool_4;

        private bool bool_5;

        private Color color_2;

        private SkinButtonStopStates stopStates_0;

        private SkinButtonDrawStyle drawStyle_0;

        private Color color_3;

        private Color color_4;

        private Color color_5;

        private Size size_1;

        private SkinButtonRoundStyle roundStyle_0;

        private int int_0;

        private SkinButtonState controlState_0;

        private bool bool_6;

        private bool bool_7;

        private Rectangle rectangle_0;

        private Image image_0;

        private Image image_1;

        private Image image_2;

        private Button button_0;

        private Bitmap bitmap_0;

        private Rectangle rectangle_1;

        private int? nullable_0;

        private SkinButtonRoundStyle? nullable_1;

        private List<Image> list_0;

        private int int_1;

        private int int_2;

        private IContainer icontainer_0;

        public Timer DhTimer
        {
            get
            {
                if (timer_0 == null)
                {
                    timer_0 = new Timer();
                    timer_0.Tick += timer_0_Tick;
                    timer_0.Interval = 30;
                }
                return timer_0;
            }
            set => timer_0 = value;
        }

        [DefaultValue(typeof(Size), "-1,-1")]
        [Category("Border")]
        [Description("边框放大指定变量。")]
        public Size BorderInflate
        {
            get => size_0;
            set
            {
                if (size_0 != value)
                {
                    size_0 = value;
                    Invalidate();
                }
            }
        }

        [Description("是否绘制边框。)")]
        [Category("Border")]
        [DefaultValue(true)]
        public bool IsDrawBorder
        {
            get => bool_0;
            set
            {
                if (bool_0 != value)
                {
                    bool_0 = value;
                    Invalidate();
                }
            }
        }

        [Description("外边框颜色。)")]
        [Category("Border")]
        [DefaultValue(typeof(Color), "9, 163, 220")]
        public Color BorderColor
        {
            get => color_0;
            set
            {
                if (color_0 != value)
                {
                    color_0 = value;
                    Invalidate();
                }
            }
        }

        [DefaultValue(typeof(Color), "200, 255, 255, 255")]
        [Category("Border")]
        [Description("内边框颜色。)")]
        public Color InnerBorderColor
        {
            get => color_1;
            set
            {
                if (color_1 != value)
                {
                    color_1 = value;
                    Invalidate();
                }
            }
        }

        [Description("是否启用渐变色Glass效果。")]
        [Category("Skin")]
        [DefaultValue(true)]
        public bool IsDrawGlass
        {
            get => bool_1;
            set
            {
                if (bool_1 != value)
                {
                    bool_1 = value;
                    Invalidate();
                }
            }
        }

        [DefaultValue(true)]
        [Category("Skin")]
        [Description("是否画禁用状态下的效果。")]
        public bool IsEnabledDraw
        {
            get => bool_3;
            set
            {
                if (bool_3 != value)
                {
                    bool_3 = value;
                    Invalidate();
                }
            }
        }

        [DefaultValue(typeof(bool), "false")]
        [Category("Skin")]
        [Description("是否继承所在窗体的色调。")]
        public bool InheritColor
        {
            get => bool_4;
            set
            {
                if (bool_4 != value)
                {
                    bool_4 = value;
                    Invalidate();
                }
            }
        }

        [Description("是否开启动画渐变效果(只有DrawType属性设置为Draw才有效)")]
        [DefaultValue(typeof(bool), "true")]
        [Category("Skin")]
        public bool FadeGlow
        {
            get => bool_5;
            set
            {
                if (bool_5 != value)
                {
                    bool_5 = value;
                    Invalidate();
                }
            }
        }

        [Category("Skin")]
        [Description("动画渐变Glow颜色")]
        [DefaultValue(typeof(Color), "White")]
        public virtual Color GlowColor
        {
            get => color_2;
            set
            {
                if (color_2 != value)
                {
                    color_2 = value;
                    method_6();
                    if (base.IsHandleCreated)
                    {
                        Invalidate();
                    }
                }
            }
        }

        [Description("停止当前状态")]
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Category("Skin")]
        [EditorBrowsable(EditorBrowsableState.Never)]
        public SkinButtonStopStates StopState
        {
            get => stopStates_0;
            set
            {
                stopStates_0 = value;
                Invalidate();
            }
        }

        [DefaultValue(typeof(SkinButtonDrawStyle), "2")]
        [Description("按钮的绘画模式")]
        [Category("Skin")]
        public SkinButtonDrawStyle DrawType
        {
            get => drawStyle_0;
            set
            {
                if (drawStyle_0 != value)
                {
                    drawStyle_0 = value;
                    Invalidate();
                }
            }
        }

        [Description("Bottom悬浮时色调(非图片绘制时和Glass模式时生效)")]
        [DefaultValue(typeof(Color), "60,195,245")]
        [Category("Skin")]
        public Color MouseBaseColor
        {
            get => color_3;
            set
            {
                color_3 = value;
                Invalidate();
            }
        }

        [Description("Bottom按下时色调(非图片绘制时和Glass模式时生效)")]
        [DefaultValue(typeof(Color), "9,140,188")]
        [Category("Skin")]
        public Color DownBaseColor
        {
            get => color_4;
            set
            {
                color_4 = value;
                Invalidate();
            }
        }

        [Category("Skin")]
        [DefaultValue(typeof(Color), "9,163,220")]
        [Description("Bottom色调(非图片绘制模式时生效)")]
        public Color BaseColor
        {
            get => color_5;
            set
            {
                color_5 = value;
                Invalidate();
            }
        }

        [DefaultValue(typeof(Size), "18,18")]
        [Category("Skin")]
        [Description("设置或获取图像的大小")]
        public Size ImageSize
        {
            get => size_1;
            set
            {
                if (value != size_1)
                {
                    size_1 = value;
                    Invalidate();
                }
            }
        }

        [Description("设置或获取按钮圆角的样式")]
        [DefaultValue(typeof(SkinButtonRoundStyle), "0")]
        [Category("Skin")]
        public SkinButtonRoundStyle RoundStyle
        {
            get => roundStyle_0;
            set
            {
                if (roundStyle_0 != value)
                {
                    roundStyle_0 = value;
                    Invalidate();
                }
            }
        }

        [DefaultValue(typeof(int), "8")]
        [Category("Skin")]
        [Description("圆角大小")]
        public int Radius
        {
            get => int_0;
            set
            {
                if (int_0 != value)
                {
                    int_0 = ((value < 4) ? 4 : value);
                    Invalidate();
                }
            }
        }

        [Description("控件状态")]
        public SkinButtonState ControlState
        {
            get => controlState_0;
            set
            {
                if (controlState_0 != value)
                {
                    controlState_0 = value;
                    Invalidate();
                }
            }
        }

        [DefaultValue(typeof(bool), "false")]
        [Category("Skin")]
        [Description("是否开启九宫绘图")]
        public bool Palace
        {
            get => bool_6;
            set
            {
                if (bool_6 != value)
                {
                    bool_6 = value;
                    Invalidate();
                }
            }
        }

        [Description("是否开启:根据所绘图限制控件范围")]
        [Category("Skin")]
        [DefaultValue(typeof(bool), "false")]
        public bool Create
        {
            get => bool_7;
            set
            {
                if (bool_7 != value)
                {
                    bool_7 = value;
                    Invalidate();
                }
            }
        }

        [DefaultValue(typeof(Rectangle), "10,10,10,10")]
        [Category("Skin")]
        [Description("九宫绘画区域")]
        public Rectangle BackRectangle
        {
            get => rectangle_0;
            set
            {
                if (rectangle_0 != value)
                {
                    rectangle_0 = value;
                }
                Invalidate();
            }
        }

        [Description("悬浮时背景")]
        [Category("MouseEnter")]
        public Image MouseBack
        {
            get => image_0;
            set
            {
                if (image_0 != value)
                {
                    image_0 = value;
                    Invalidate();
                }
            }
        }

        [Description("点击时背景")]
        [Category("MouseDown")]
        public Image DownBack
        {
            get => image_1;
            set
            {
                if (image_1 != value)
                {
                    image_1 = value;
                    Invalidate();
                }
            }
        }

        [Category("MouseNorml")]
        [Description("初始时背景")]
        public Image NormlBack
        {
            get => image_2;
            set
            {
                if (image_2 != value)
                {
                    image_2 = value;
                    Invalidate();
                }
            }
        }

        public SkinButton()
        {
            size_0 = new Size(-1, -1);
            bool_0 = true;
            color_0 = Color.FromArgb(9, 163, 220);
            color_1 = Color.FromArgb(200, 255, 255, 255);
            bool_1 = true;
            bool_3 = true;
            bool_5 = true;
            color_2 = Color.White;
            stopStates_0 = SkinButtonStopStates.NoStop;
            drawStyle_0 = SkinButtonDrawStyle.Draw;
            color_3 = Color.FromArgb(60, 195, 245);
            color_4 = Color.FromArgb(9, 140, 188);
            color_5 = Color.FromArgb(9, 163, 220);
            size_1 = new Size(18, 18);
            int_0 = 8;
            rectangle_0 = new Rectangle(10, 10, 10, 10);
            rectangle_1 = Rectangle.Empty;
            nullable_0 = null;
            nullable_1 = null;

            Init();
            base.ResizeRedraw = true;
            BackColor = Color.Transparent;
        }

        public void Init()
        {
            SetStyle(ControlStyles.ResizeRedraw, value: true);
            SetStyle(ControlStyles.OptimizedDoubleBuffer, value: true);
            SetStyle(ControlStyles.AllPaintingInWmPaint, value: true);
            SetStyle(ControlStyles.SupportsTransparentBackColor, value: true);
            UpdateStyles();
        }

        protected override void OnParentBackColorChanged(EventArgs e)
        {
            base.OnParentBackColorChanged(e);
            Invalidate();
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            Invalidate();
            base.OnEnabledChanged(e);
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            base.OnMouseEnter(e);
            if (FadeGlow)
            {
                method_10();
            }
            controlState_0 = SkinButtonState.Hover;
            Invalidate();
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            base.OnMouseLeave(e);
            if (FadeGlow)
            {
                method_11();
            }
            controlState_0 = SkinButtonState.Normal;
            Invalidate();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && icontainer_0 != null)
            {
                icontainer_0.Dispose();
            }
            base.Dispose(disposing);
            if (disposing)
            {
                if (DhTimer != null)
                {
                    DhTimer.Dispose();
                    DhTimer = null;
                }
                if (list_0 != null && list_0.Count > 0)
                {
                    list_0.Clear();
                    list_0 = null;
                }
            }
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            base.OnMouseDown(e);
            if ((e.Button & MouseButtons.Left) == MouseButtons.Left)
            {
                controlState_0 = SkinButtonState.Pressed;
                Invalidate();
            }
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            base.OnMouseUp(e);
            if ((e.Button & MouseButtons.Left) == MouseButtons.Left)
            {
                controlState_0 = SkinButtonState.Hover;
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            base.OnPaintBackground(e);
            Graphics graphics = e.Graphics;
            Rectangle clientRectangle = base.ClientRectangle;
            graphics.SmoothingMode = SmoothingMode.AntiAlias;
            graphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;
            Color borderColor = BorderColor;
            Color innerBorderColor = InnerBorderColor;
            Color color = BaseColor;
            if (StopState != SkinButtonStopStates.NoStop)
            {
                controlState_0 = (SkinButtonState)StopState;
            }
            if (InheritColor)
            {
                color = base.Parent.BackColor;
            }
            Bitmap bitmap;
            Color baseColor;
            switch (controlState_0)
            {
                default:
                    bitmap = (Bitmap)NormlBack;
                    baseColor = color;
                    break;
                case SkinButtonState.Hover:
                    bitmap = (Bitmap)MouseBack;
                    baseColor = ((!IsDrawGlass) ? MouseBaseColor : method_3(color, 0, -13, -8, -3));
                    break;
                case SkinButtonState.Pressed:
                    bitmap = (Bitmap)DownBack;
                    baseColor = ((!IsDrawGlass) ? DownBaseColor : method_3(color, 0, -35, -24, -9));
                    break;
            }
            if (!base.Enabled && IsEnabledDraw)
            {
                baseColor = SystemColors.ControlDark;
                borderColor = SystemColors.ControlDark;
                innerBorderColor = SystemColors.ControlDark;
                if (bitmap != null)
                {
                    Bitmap bitmap2 = new Bitmap(bitmap.Width, bitmap.Height);
                    using (Graphics graphics2 = Graphics.FromImage(bitmap2))
                    {
                        ControlPaint.DrawImageDisabled(graphics2, bitmap, 0, 0, BaseColor);
                    }
                    bitmap = bitmap2;
                }
            }
            if (bitmap != null && DrawType == SkinButtonDrawStyle.Img)
            {
                clientRectangle = method_0(clientRectangle, bitmap);
                if (Palace)
                {
                    DrawRect(graphics, bitmap, clientRectangle, Rectangle.FromLTRB(BackRectangle.X, BackRectangle.Y, BackRectangle.Width, BackRectangle.Height), 1, 1);
                }
                else
                {
                    graphics.DrawImage(bitmap, 0, 0, base.Width, base.Height);
                }
            }
            else if (DrawType == SkinButtonDrawStyle.Draw)
            {
                RenderBackgroundInternal(graphics, clientRectangle, baseColor, borderColor, innerBorderColor, RoundStyle, Radius, 0.35f, IsDrawBorder, IsDrawGlass, LinearGradientMode.Vertical);
                if (FadeGlow)
                {
                    method_4(e.Graphics);
                }
            }
            if (button_0 == null)
            {
                button_0 = new Button
                {
                    Parent = new Control5()
                };
                button_0.SuspendLayout();
                button_0.BackColor = Color.Transparent;
                button_0.FlatAppearance.BorderSize = 0;
                button_0.FlatStyle = FlatStyle.Flat;
            }
            else
            {
                button_0.SuspendLayout();
            }
            button_0.AutoEllipsis = base.AutoEllipsis;
            if (base.Enabled)
            {
                Color foreColor = base.Enabled ? ForeColor : SystemColors.ControlDark;
                button_0.ForeColor = foreColor;
            }
            else
            {
                button_0.ForeColor = HuiseColor(ForeColor);
            }
            button_0.Font = Font;
            button_0.RightToLeft = RightToLeft;
            if (button_0.Image != base.Image && button_0.Image != null)
            {
                button_0.Image.Dispose();
            }
            if (base.Image != null)
            {
                Image original = base.Image;
                if (base.ImageList != null && base.ImageIndex != -1)
                {
                    original = base.ImageList.Images[base.ImageIndex];
                }
                Bitmap image = new Bitmap(original, ImageSize);
                if (!base.Enabled)
                {
                    using (Graphics graphics3 = Graphics.FromImage(image))
                    {
                        ControlPaint.DrawImageDisabled(graphics3, image, 0, 0, BaseColor);
                    }
                }
                button_0.Image = image;
            }
            button_0.ImageAlign = base.ImageAlign;
            button_0.ImageIndex = base.ImageIndex;
            button_0.ImageKey = base.ImageKey;
            button_0.ImageList = base.ImageList;
            button_0.Padding = base.Padding;
            button_0.Size = base.Size;
            button_0.Text = Text;
            button_0.TextAlign = TextAlign;
            button_0.TextImageRelation = base.TextImageRelation;
            button_0.UseCompatibleTextRendering = base.UseCompatibleTextRendering;
            button_0.UseMnemonic = base.UseMnemonic;
            button_0.ResumeLayout();
            InvokePaint(button_0, e);
        }

        private void DrawRect(Graphics g, Bitmap img, Rectangle r, Rectangle lr, int index, int Totalindex)
        {
            try
            {
                if (g != null && img != null)
                {
                    int num = (index - 1) * img.Width / Totalindex;
                    int num2 = 0;
                    int left = r.Left;
                    int top = r.Top;
                    if (r.Height > img.Height && r.Width <= img.Width / Totalindex)
                    {
                        Rectangle srcRect = new Rectangle(num, num2, img.Width / Totalindex, lr.Top);
                        Rectangle destRect = new Rectangle(left, top, r.Width, lr.Top);
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                        srcRect = new Rectangle(num, num2 + lr.Top, img.Width / Totalindex, img.Height - lr.Top - lr.Bottom);
                        destRect = new Rectangle(left, top + lr.Top, r.Width, r.Height - lr.Top - lr.Bottom);
                        if (lr.Top + lr.Bottom == 0)
                        {
                            srcRect.Height--;
                        }
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                        srcRect = new Rectangle(num, num2 + img.Height - lr.Bottom, img.Width / Totalindex, lr.Bottom);
                        destRect = new Rectangle(left, top + r.Height - lr.Bottom, r.Width, lr.Bottom);
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                    }
                    else if (r.Height <= img.Height && r.Width > img.Width / Totalindex)
                    {
                        Rectangle srcRect = new Rectangle(num, num2, lr.Left, img.Height);
                        Rectangle destRect = new Rectangle(left, top, lr.Left, r.Height);
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                        srcRect = new Rectangle(num + lr.Left, num2, img.Width / Totalindex - lr.Left - lr.Right, img.Height);
                        destRect = new Rectangle(left + lr.Left, top, r.Width - lr.Left - lr.Right, r.Height);
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                        srcRect = new Rectangle(num + img.Width / Totalindex - lr.Right, num2, lr.Right, img.Height);
                        destRect = new Rectangle(left + r.Width - lr.Right, top, lr.Right, r.Height);
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                    }
                    else if (r.Height <= img.Height && r.Width <= img.Width / Totalindex)
                    {
                        g.DrawImage(srcRect: new Rectangle((index - 1) * img.Width / Totalindex, 0, img.Width / Totalindex, img.Height - 1), image: img, destRect: new Rectangle(left, top, r.Width, r.Height), srcUnit: GraphicsUnit.Pixel);
                    }
                    else if (r.Height > img.Height && r.Width > img.Width / Totalindex)
                    {
                        Rectangle srcRect = new Rectangle(num, num2, lr.Left, lr.Top);
                        Rectangle destRect = new Rectangle(left, top, lr.Left, lr.Top);
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                        srcRect = new Rectangle(num, num2 + img.Height - lr.Bottom, lr.Left, lr.Bottom);
                        destRect = new Rectangle(left, top + r.Height - lr.Bottom, lr.Left, lr.Bottom);
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                        srcRect = new Rectangle(num, num2 + lr.Top, lr.Left, img.Height - lr.Top - lr.Bottom);
                        destRect = new Rectangle(left, top + lr.Top, lr.Left, r.Height - lr.Top - lr.Bottom);
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                        srcRect = new Rectangle(num + lr.Left, num2, img.Width / Totalindex - lr.Left - lr.Right, lr.Top);
                        destRect = new Rectangle(left + lr.Left, top, r.Width - lr.Left - lr.Right, lr.Top);
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                        srcRect = new Rectangle(num + img.Width / Totalindex - lr.Right, num2, lr.Right, lr.Top);
                        destRect = new Rectangle(left + r.Width - lr.Right, top, lr.Right, lr.Top);
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                        srcRect = new Rectangle(num + img.Width / Totalindex - lr.Right, num2 + lr.Top, lr.Right, img.Height - lr.Top - lr.Bottom);
                        destRect = new Rectangle(left + r.Width - lr.Right, top + lr.Top, lr.Right, r.Height - lr.Top - lr.Bottom);
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                        srcRect = new Rectangle(num + img.Width / Totalindex - lr.Right, num2 + img.Height - lr.Bottom, lr.Right, lr.Bottom);
                        destRect = new Rectangle(left + r.Width - lr.Right, top + r.Height - lr.Bottom, lr.Right, lr.Bottom);
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                        srcRect = new Rectangle(num + lr.Left, num2 + img.Height - lr.Bottom, img.Width / Totalindex - lr.Left - lr.Right, lr.Bottom);
                        destRect = new Rectangle(left + lr.Left, top + r.Height - lr.Bottom, r.Width - lr.Left - lr.Right, lr.Bottom);
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                        srcRect = new Rectangle(num + lr.Left, num2 + lr.Top, img.Width / Totalindex - lr.Left - lr.Right, img.Height - lr.Top - lr.Bottom);
                        destRect = new Rectangle(left + lr.Left, top + lr.Top, r.Width - lr.Left - lr.Right, r.Height - lr.Top - lr.Bottom);
                        g.DrawImage(img, destRect, srcRect, GraphicsUnit.Pixel);
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        public Color HuiseColor(Color c)
        {
            return Color.FromArgb(3 * c.R + SystemColors.ControlDark.R >> 2, 3 * c.G + SystemColors.ControlDark.G >> 2, 3 * c.B + SystemColors.ControlDark.B >> 2);
        }

        private Rectangle method_0(Rectangle rectangle_2, Bitmap bitmap_1)
        {
            if (Create)
            {
                if (bitmap_1 != bitmap_0)
                {
                    CreateControlRegion(this, bitmap_1, 1);
                    bitmap_0 = bitmap_1;
                }
            }
            else if (rectangle_2 != rectangle_1 || Radius != nullable_0 || RoundStyle != nullable_1)
            {
                CreateRegion(this, rectangle_2, Radius, RoundStyle);
                rectangle_1 = rectangle_2;
                nullable_0 = Radius;
                nullable_1 = RoundStyle;
            }
            return rectangle_2;
        }

        private void CreateRegion(Control control, Rectangle bounds, int radius, SkinButtonRoundStyle roundStyle)
        {
            using (GraphicsPath graphicsPath = GraphicsPathHelper.CreatePath(bounds, radius, roundStyle, correction: true))
            {
                Region region = new Region(graphicsPath);
                graphicsPath.Widen(Pens.White);
                region.Union(graphicsPath);
                control.Region = region;
            }
        }

        private void CreateControlRegion(Control control, Bitmap bitmap, int Alpha)
        {
            if (control != null && bitmap != null)
            {
                this.Width = bitmap.Width;
                this.Height = bitmap.Height;
                GraphicsPath path2 = CalculateControlGraphicsPath(bitmap, Alpha);
                this.Region = new Region(path2);
            }
        }

        private GraphicsPath CalculateControlGraphicsPath(Bitmap bitmap, int Alpha)
        {
            FastBitmap fastBitmap = new FastBitmap(bitmap);
            fastBitmap.Lock();
            GraphicsPath graphicsPath = new GraphicsPath();
            for (int i = 0; i < bitmap.Height; i++)
            {
                for (int j = 0; j < bitmap.Width; j++)
                {
                    if (fastBitmap.GetPixel(j, i).A >= Alpha)
                    {
                        int num = j;
                        int k;
                        for (k = num; k < bitmap.Width && fastBitmap.GetPixel(k, i).A >= Alpha; k++)
                        {
                        }
                        graphicsPath.AddRectangle(new Rectangle(num, i, k - num, 1));
                        j = k;
                    }
                }
            }
            fastBitmap.Unlock();
            return graphicsPath;
        }

        public void RenderBackgroundInternal(Graphics g, Rectangle rect, Color baseColor, Color borderColor, Color innerBorderColor, SkinButtonRoundStyle style, int roundWidth, float basePosition, bool drawBorder, bool drawGlass, LinearGradientMode mode)
        {
            rect.Width--;
            rect.Height--;
            using (LinearGradientBrush linearGradientBrush = new LinearGradientBrush(rect, Color.Transparent, Color.Transparent, mode))
            {
                Color[] colors = new Color[4]
                {
                    method_3(baseColor, 0, 35, 24, 9),
                    method_3(baseColor, 0, 13, 8, 3),
                    baseColor,
                    method_3(baseColor, 0, 68, 69, 54)
                };
                ColorBlend colorBlend = new ColorBlend
                {
                    Positions = new float[4]
                {
                    0f,
                    basePosition,
                    basePosition + 0.05f,
                    1f
                },
                    Colors = colors
                };
                linearGradientBrush.InterpolationColors = colorBlend;
                if (style != 0)
                {
                    using (GraphicsPath path = GraphicsPathHelper.CreatePath(rect, roundWidth, style, correction: false))
                    {
                        if (drawGlass)
                        {
                            g.FillPath(linearGradientBrush, path);
                            if (baseColor.A > 80)
                            {
                                Rectangle rect2 = rect;
                                if (mode == LinearGradientMode.Vertical)
                                {
                                    rect2.Height = (int)(rect2.Height * basePosition);
                                }
                                else
                                {
                                    rect2.Width = (int)(rect.Width * basePosition);
                                }
                                using (GraphicsPath path2 = GraphicsPathHelper.CreatePath(rect2, roundWidth, SkinButtonRoundStyle.Top, correction: false))
                                {
                                    using (SolidBrush brush = new SolidBrush(Color.FromArgb(80, 255, 255, 255)))
                                    {
                                        g.FillPath(brush, path2);
                                    }
                                }
                            }
                        }
                        else
                        {
                            g.FillPath(new SolidBrush(baseColor), path);
                        }
                    }
                    if (drawGlass)
                    {
                        RectangleF rectangleF_ = rect;
                        if (mode == LinearGradientMode.Vertical)
                        {
                            rectangleF_.Y = rect.Y + rect.Height * basePosition;
                            rectangleF_.Height = (rect.Height - rect.Height * basePosition) * 2f;
                        }
                        else
                        {
                            rectangleF_.X = rect.X + rect.Width * basePosition;
                            rectangleF_.Width = (rect.Width - rect.Width * basePosition) * 2f;
                        }
                        method_1(g, rectangleF_, 170, 0);
                    }
                    if (drawBorder)
                    {
                        if (borderColor != Color.Transparent)
                        {
                            using (GraphicsPath path3 = GraphicsPathHelper.CreatePath(rect, roundWidth, style, correction: false))
                            {
                                using (Pen pen = new Pen(borderColor))
                                {
                                    g.DrawPath(pen, path3);
                                }
                            }
                        }
                        rect.Inflate(BorderInflate);
                        if (innerBorderColor != Color.Transparent)
                        {
                            using (GraphicsPath path4 = GraphicsPathHelper.CreatePath(rect, roundWidth, style, correction: false))
                            {
                                using (Pen pen2 = new Pen(innerBorderColor))
                                {
                                    g.DrawPath(pen2, path4);
                                }
                            }
                        }
                    }
                }
                else
                {
                    if (drawGlass)
                    {
                        g.FillRectangle(linearGradientBrush, rect);
                        if (baseColor.A > 80)
                        {
                            Rectangle rect3 = rect;
                            if (mode == LinearGradientMode.Vertical)
                            {
                                rect3.Height = (int)(rect3.Height * basePosition);
                            }
                            else
                            {
                                rect3.Width = (int)(rect.Width * basePosition);
                            }
                            using (SolidBrush brush2 = new SolidBrush(Color.FromArgb(80, 255, 255, 255)))
                            {
                                g.FillRectangle(brush2, rect3);
                            }
                        }
                    }
                    else
                    {
                        g.FillRectangle(new SolidBrush(baseColor), rect);
                    }
                    if (drawGlass)
                    {
                        RectangleF rectangleF_2 = rect;
                        if (mode == LinearGradientMode.Vertical)
                        {
                            rectangleF_2.Y = rect.Y + rect.Height * basePosition;
                            rectangleF_2.Height = (rect.Height - rect.Height * basePosition) * 2f;
                        }
                        else
                        {
                            rectangleF_2.X = rect.X + rect.Width * basePosition;
                            rectangleF_2.Width = (rect.Width - rect.Width * basePosition) * 2f;
                        }
                        method_1(g, rectangleF_2, 200, 0);
                    }
                    if (drawBorder)
                    {
                        if (borderColor != Color.Transparent)
                        {
                            using (Pen pen3 = new Pen(borderColor))
                            {
                                g.DrawRectangle(pen3, rect);
                            }
                        }
                        rect.Inflate(BorderInflate);
                        if (innerBorderColor != Color.Transparent)
                        {
                            using (Pen pen4 = new Pen(innerBorderColor))
                            {
                                g.DrawRectangle(pen4, rect);
                            }
                        }
                    }
                }
            }
        }

        private void method_1(Graphics graphics_0, RectangleF rectangleF_0, int int_3, int int_4)
        {
            method_2(graphics_0, rectangleF_0, Color.White, int_3, int_4);
        }

        private void method_2(Graphics graphics_0, RectangleF rectangleF_0, Color color_6, int int_3, int int_4)
        {
            using (GraphicsPath graphicsPath = new GraphicsPath())
            {
                graphicsPath.AddEllipse(rectangleF_0);
                using (PathGradientBrush pathGradientBrush = new PathGradientBrush(graphicsPath))
                {
                    pathGradientBrush.CenterColor = Color.FromArgb(int_3, color_6);
                    pathGradientBrush.SurroundColors = new Color[1]
                    {
                        Color.FromArgb(int_4, color_6)
                    };
                    pathGradientBrush.CenterPoint = new PointF(rectangleF_0.X + rectangleF_0.Width / 2f, rectangleF_0.Y + rectangleF_0.Height / 2f);
                    graphics_0.FillPath(pathGradientBrush, graphicsPath);
                }
            }
        }

        private Color method_3(Color color_6, int int_3, int int_4, int int_5, int int_6)
        {
            int a = color_6.A;
            int r = color_6.R;
            int g = color_6.G;
            int b = color_6.B;
            int_3 = ((int_3 + a <= 255) ? Math.Max(int_3 + a, 0) : 255);
            int_4 = ((int_4 + r <= 255) ? Math.Max(int_4 + r, 0) : 255);
            int_5 = ((int_5 + g <= 255) ? Math.Max(int_5 + g, 0) : 255);
            int_6 = ((int_6 + b <= 255) ? Math.Max(int_6 + b, 0) : 255);
            return Color.FromArgb(int_3, int_4, int_5, int_6);
        }

        private void method_4(Graphics graphics_0)
        {
            int index;
            if (!base.Enabled)
            {
                index = 0;
            }
            else if (ControlState == SkinButtonState.Pressed)
            {
                index = 1;
            }
            else if (!method_9() && int_1 == 0)
            {
                index = 2;
            }
            else
            {
                if (!method_5())
                {
                    method_7(bool_8: true);
                }
                index = 3 + int_1;
            }
            if (list_0 == null || list_0.Count == 0)
            {
                method_6();
            }
            graphics_0.DrawImage(list_0[index], Point.Empty);
        }

        public Image CreateBackgroundFrame(bool pressed, bool hovered, bool animating, bool enabled, float glowOpacity)
        {
            Rectangle clientRectangle = base.ClientRectangle;
            if (clientRectangle.Width <= 0)
            {
                clientRectangle.Width = 1;
            }
            if (clientRectangle.Height <= 0)
            {
                clientRectangle.Height = 1;
            }
            Image image = new Bitmap(clientRectangle.Width, clientRectangle.Height);
            using (Graphics graphics = Graphics.FromImage(image))
            {
                graphics.Clear(Color.Transparent);
                smethod_0(graphics, clientRectangle, pressed, hovered, animating, enabled, color_2, glowOpacity);
                return image;
            }
        }

        private static void smethod_0(Graphics graphics_0, Rectangle rectangle_2, bool bool_8, bool bool_9, bool bool_10, bool bool_11, Color color_6, float float_0)
        {
            SmoothingMode smoothingMode = graphics_0.SmoothingMode;
            graphics_0.SmoothingMode = SmoothingMode.AntiAlias;
            Rectangle rectangle = rectangle_2;
            rectangle.Width--;
            rectangle.Height--;
            rectangle.X++;
            rectangle.Y++;
            rectangle.Width -= 2;
            rectangle.Height -= 2;
            Rectangle rectangle2 = rectangle;
            rectangle2.Height >>= 1;
            if ((bool_9 || bool_10) && !bool_8)
            {
                using (GraphicsPath path = smethod_1(rectangle, 2))
                {
                    graphics_0.SetClip(path, CombineMode.Intersect);
                    using (GraphicsPath graphicsPath = smethod_2(rectangle))
                    {
                        using (PathGradientBrush pathGradientBrush = new PathGradientBrush(graphicsPath))
                        {
                            int alpha = (int)(178f * float_0 + 0.5f);
                            RectangleF bounds = graphicsPath.GetBounds();
                            pathGradientBrush.CenterPoint = new PointF((bounds.Left + bounds.Right) / 2f, (bounds.Top + bounds.Bottom) / 2f);
                            pathGradientBrush.CenterColor = Color.FromArgb(alpha, color_6);
                            pathGradientBrush.SurroundColors = new Color[1]
                            {
                                Color.FromArgb(0, color_6)
                            };
                            graphics_0.FillPath(pathGradientBrush, graphicsPath);
                        }
                    }
                    graphics_0.ResetClip();
                }
            }
            graphics_0.SmoothingMode = smoothingMode;
        }

        private static GraphicsPath smethod_1(Rectangle rectangle_2, int int_3)
        {
            GraphicsPath graphicsPath = new GraphicsPath();
            int left = rectangle_2.Left;
            int top = rectangle_2.Top;
            int width = rectangle_2.Width;
            int height = rectangle_2.Height;
            int num = int_3 << 1;
            graphicsPath.AddArc(left, top, num, num, 180f, 90f);
            graphicsPath.AddLine(left + int_3, top, left + width - int_3, top);
            graphicsPath.AddArc(left + width - num, top, num, num, 270f, 90f);
            graphicsPath.AddLine(left + width, top + int_3, left + width, top + height - int_3);
            graphicsPath.AddArc(left + width - num, top + height - num, num, num, 0f, 90f);
            graphicsPath.AddLine(left + width - int_3, top + height, left + int_3, top + height);
            graphicsPath.AddArc(left, top + height - num, num, num, 90f, 90f);
            graphicsPath.AddLine(left, top + height - int_3, left, top + int_3);
            graphicsPath.CloseFigure();
            return graphicsPath;
        }

        private static GraphicsPath smethod_2(Rectangle rectangle_2)
        {
            GraphicsPath graphicsPath = new GraphicsPath();
            RectangleF rect = rectangle_2;
            rect.X -= rect.Width * 0.35f;
            rect.Y -= rect.Height * 0.15f;
            rect.Width *= 1.7f;
            rect.Height *= 2.3f;
            graphicsPath.AddEllipse(rect);
            graphicsPath.CloseFigure();
            return graphicsPath;
        }

        private bool method_5()
        {
            if (list_0 != null)
            {
                return list_0.Count > 3;
            }
            return false;
        }

        private void method_6()
        {
            method_7(bool_8: false);
        }

        private void method_7(bool bool_8)
        {
            method_8();
            if (!base.IsHandleCreated)
            {
                return;
            }
            if (list_0 == null)
            {
                list_0 = new List<Image>();
            }
            list_0.Add(CreateBackgroundFrame(pressed: false, hovered: false, animating: false, enabled: false, glowOpacity: 0f));
            list_0.Add(CreateBackgroundFrame(pressed: true, hovered: true, animating: false, enabled: true, glowOpacity: 0f));
            list_0.Add(CreateBackgroundFrame(pressed: false, hovered: false, animating: false, enabled: true, glowOpacity: 0f));
            if (bool_8)
            {
                for (int i = 0; i < 10; i++)
                {
                    list_0.Add(CreateBackgroundFrame(pressed: false, hovered: true, animating: true, enabled: true, glowOpacity: i / 9f));
                }
            }
        }

        private void method_8()
        {
            if (list_0 != null)
            {
                while (list_0.Count > 0)
                {
                    list_0[list_0.Count - 1].Dispose();
                    list_0.RemoveAt(list_0.Count - 1);
                }
            }
        }

        private bool method_9()
        {
            return int_2 != 0;
        }

        private void method_10()
        {
            int_2 = 1;
            DhTimer.Enabled = true;
        }

        private void method_11()
        {
            int_2 = -1;
            DhTimer.Enabled = true;
        }

        private void timer_0_Tick(object sender, EventArgs e)
        {
            if (DhTimer.Enabled)
            {
                Refresh();
                int_1 += int_2;
                if (int_1 == -1)
                {
                    int_1 = 0;
                    DhTimer.Enabled = false;
                    int_2 = 0;
                }
                else if (int_1 == 10)
                {
                    int_1 = 9;
                    DhTimer.Enabled = false;
                    int_2 = 0;
                }
            }
        }

        private void method_12()
        {
            SuspendLayout();
            ResumeLayout(performLayout: false);
        }
    }
}
