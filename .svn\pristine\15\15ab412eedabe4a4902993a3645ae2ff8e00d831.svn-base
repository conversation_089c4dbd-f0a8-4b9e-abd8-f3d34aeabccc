using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolArrow : ToolObject
    {
        private DrawArrow drawArrow;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.current_ToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                drawArrow = new DrawArrow(e.X, e.Y, e.X + 1, e.Y + 1);
                AddNewObject(drawArea, drawArrow);
            }
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (drawArrow == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                drawArrow.IsSelected = true;
                var obj = drawArrow;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    drawArrow.MoveHandleTo(e.Location, 2, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArrow != null)
            {
                StaticValue.current_Rectangle = drawArrow.Rectangle;
                if (!drawArrow.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var obj = drawArrow;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    drawArrow.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(drawArrow));
                }
            }
        }
    }
}