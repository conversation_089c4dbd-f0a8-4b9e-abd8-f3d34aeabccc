﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="label1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 8.25pt</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 47</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 13</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>宽度:</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="butCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 105</value>
  </data>
  <data name="butCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 24</value>
  </data>
  <data name="butCancel.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="butCancel.Text" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="&gt;&gt;butCancel.Name" xml:space="preserve">
    <value>butCancel</value>
  </data>
  <data name="&gt;&gt;butCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;butCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;butCancel.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="butSubmit.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 105</value>
  </data>
  <data name="butSubmit.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 24</value>
  </data>
  <data name="butSubmit.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="butSubmit.Text" xml:space="preserve">
    <value>确认</value>
  </data>
  <data name="&gt;&gt;butSubmit.Name" xml:space="preserve">
    <value>butSubmit</value>
  </data>
  <data name="&gt;&gt;butSubmit.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;butSubmit.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;butSubmit.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="comUnits.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 12</value>
  </data>
  <data name="comUnits.Size" type="System.Drawing.Size, System.Drawing">
    <value>180, 21</value>
  </data>
  <data name="comUnits.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;comUnits.Name" xml:space="preserve">
    <value>comUnits</value>
  </data>
  <data name="&gt;&gt;comUnits.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comUnits.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comUnits.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="lblUnit1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblUnit1.Location" type="System.Drawing.Point, System.Drawing">
    <value>167, 47</value>
  </data>
  <data name="lblUnit1.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 13</value>
  </data>
  <data name="lblUnit1.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;lblUnit1.Name" xml:space="preserve">
    <value>lblUnit1</value>
  </data>
  <data name="&gt;&gt;lblUnit1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblUnit1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblUnit1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="numWidth.Location" type="System.Drawing.Point, System.Drawing">
    <value>65, 45</value>
  </data>
  <data name="numWidth.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="numWidth.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;numWidth.Name" xml:space="preserve">
    <value>numWidth</value>
  </data>
  <data name="&gt;&gt;numWidth.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numWidth.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;numWidth.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="numHeight.Location" type="System.Drawing.Point, System.Drawing">
    <value>65, 71</value>
  </data>
  <data name="numHeight.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="numHeight.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;numHeight.Name" xml:space="preserve">
    <value>numHeight</value>
  </data>
  <data name="&gt;&gt;numHeight.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numHeight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;numHeight.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lblUnit2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lblUnit2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblUnit2.Location" type="System.Drawing.Point, System.Drawing">
    <value>167, 73</value>
  </data>
  <data name="lblUnit2.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 13</value>
  </data>
  <data name="lblUnit2.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;lblUnit2.Name" xml:space="preserve">
    <value>lblUnit2</value>
  </data>
  <data name="&gt;&gt;lblUnit2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblUnit2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblUnit2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 8.25pt</value>
  </data>
  <data name="label3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 73</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 13</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>高度:</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="$this.Localizable" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>204, 141</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>设置标尺长度</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>SetSizeForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>