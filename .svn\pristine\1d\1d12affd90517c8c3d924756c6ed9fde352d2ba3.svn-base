﻿using ImageLib;
using nQuant;
using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;

namespace OCRTools
{
    public class ImageCompress
    {
        internal static string CompressImageFile(string fileName, string strPath, string type)
        {
            var filePath = Path.Combine(strPath, Path.GetFileNameWithoutExtension(fileName) + "-" + ServerTime.DateTime.Millisecond + Path.GetExtension(fileName));
            try
            {
                var byts = File.ReadAllBytes(fileName);
                var result = !string.IsNullOrEmpty(type) ? CompressImage(byts, type) : GetCompressResult(byts);
                if (result.Result == null || result.Result.Length <= 0) return string.Empty;
                File.WriteAllBytes(filePath, result.Result);
            }
            catch { }
            if (!File.Exists(filePath))
            {
                filePath = string.Empty;
            }
            return filePath;
        }

        private const int maxTimeOut = 30000;

        internal static CompressResult GetCompressResult(byte[] bytes)
        {
            CompressResult result = null;
            try
            {
                var lstParam = LstCompressType.Select(type => new TaskParam() { Param2 = bytes, Param1 = type }).ToList();
                result = CommonTask<CompressResult>.GetFastestValidResult(lstParam, GetValidateResultByType, 3, maxTimeOut, IsValidateResult);
            }
            catch (Exception oe)
            {
                Log.WriteError("GetCompressResult", oe);
            }
            return result;
        }

        private static CompressResult GetValidateResultByType(TaskParam param)
        {
            return CompressImage((byte[])param.Param2, param.Param1);
        }

        private static bool IsValidateResult(CompressResult result)
        {
            return result != null && result.Result != null && result.Result.Length > 0;
        }

        internal static CompressResult CompressImage(byte[] byts, string type)
        {
            CompressResult result = new CompressResult() { Type = type };
            try
            {
                var strUrl = string.Empty;
                if (Equals(SelfCompressTypeKey.CurrentText(), type))
                {
                    using (var stream = new MemoryStream(byts))
                    {
                        using (var tmp = new Bitmap(stream))
                        {
                            using (var resultImg = WuQuantizer.QuantizeImage(tmp))
                            {
                                if (resultImg != null)
                                    result.Result = ImageProcessHelper.ImageToByte(resultImg);
                            }
                        }
                    }
                }
                else
                {
                    result.Result = ImageHelper.GetZipResult(byts, type, ref strUrl);
                    result.Url = strUrl;
                }
            }
            catch (Exception oe)
            {
                //Log.WriteError("CompressImage", oe);
            }
            return result;
        }

        internal class CompressResult
        {
            public string Type { get; set; }

            public byte[] Result { get; set; }

            public string Url { get; set; }
        }

        public const string SelfCompressTypeKey = "图片压缩";
        public static List<string> LstCompressType = new List<string>();
    }
}
