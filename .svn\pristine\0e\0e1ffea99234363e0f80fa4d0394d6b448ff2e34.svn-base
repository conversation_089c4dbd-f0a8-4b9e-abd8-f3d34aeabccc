using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class ProgressbarEX : ProgressBar
    {
        public delegate void ValueChangeHandler(object sender, EventArgs e);

        public new int Value
        {
            get => base.Value;
            set
            {
                base.Value = value;
                Change();
            }
        }

        public event ValueChangeHandler ValueChanged;

        public ProgressbarEX()
        {
            SetStyle(ControlStyles.UserPaint, value: true);
            SetStyle(ControlStyles.AllPaintingInWmPaint, value: true);
            SetStyle(ControlStyles.OptimizedDoubleBuffer, value: true);
        }

        public void Change()
        {
            if (this.ValueChanged != null)
            {
                this.ValueChanged(null, EventArgs.Empty);
            }
        }

        private int GetPercentage()
        {
            int num = base.Maximum - base.Minimum;
            int num2 = Value - base.Minimum;
            if (num2 < 0)
            {
                num2 = 0;
            }
            if (num2 > num)
            {
                num2 = num;
            }
            return (int)Math.Ceiling(num2 / (double)num * 100.0);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            Rectangle clientRectangle = base.ClientRectangle;
            Rectangle rect = new Rectangle(clientRectangle.X, clientRectangle.Y + base.Height / 3, clientRectangle.Width - 1, clientRectangle.Height / 3);
            using (SolidBrush brush2 = new SolidBrush(ForeColor))
            {
                using (Pen pen = new Pen(Color.FromArgb(28, 160, 224)))
                {
                    using (SolidBrush brush = new SolidBrush(BackColor))
                    {
                        e.Graphics.FillRectangle(brush, 0, 0, clientRectangle.Width, clientRectangle.Height);
                        double num = Value / 100.0;
                        e.Graphics.DrawRectangle(pen, rect);
                        rect.Width = Convert.ToInt32(rect.Width * num);
                        rect.Offset(1, 1);
                        rect = rect.SizeOffset(-1, -1);
                        e.Graphics.FillRectangle(brush2, rect);
                    }
                }
            }
        }
    }
}
