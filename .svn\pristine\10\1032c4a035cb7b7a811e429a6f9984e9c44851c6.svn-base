using System.Text;
using UtfUnknown.Core.Analyzers.Chinese;
using UtfUnknown.Core.Models.MultiByte.Chinese;

namespace UtfUnknown.Core.Probers.MultiByte.Chinese
{
    public class Gb18030Prober : CharsetProber
    {
        private readonly Gb18030DistributionAnalyser _analyser;
        private readonly CodingStateMachine _codingSm;

        private readonly byte[] _lastChar;

        public Gb18030Prober()
        {
            _lastChar = new byte[2];
            _codingSm = new CodingStateMachine(new Gb18030SmModel());
            _analyser = new Gb18030DistributionAnalyser();
            Reset();
        }

        public override string GetCharsetName()
        {
            return "gb18030";
        }

        public override ProbingState HandleData(byte[] buf, int offset, int len)
        {
            var num = offset + len;
            for (var i = offset; i < num; i++)
            {
                switch (_codingSm.NextState(buf[i]))
                {
                    case 1:
                        state = ProbingState.NotMe;
                        break;
                    case 2:
                        state = ProbingState.FoundIt;
                        break;
                    case 0:
                    {
                        var currentCharLen = _codingSm.CurrentCharLen;
                        if (i == offset)
                        {
                            _lastChar[1] = buf[offset];
                            _analyser.HandleOneChar(_lastChar, 0, currentCharLen);
                        }
                        else
                        {
                            _analyser.HandleOneChar(buf, i - 1, currentCharLen);
                        }

                        continue;
                    }
                    default:
                        continue;
                }

                break;
            }

            _lastChar[0] = buf[num - 1];
            if (state == ProbingState.Detecting && _analyser.GotEnoughData() && GetConfidence() > 0.95f)
                state = ProbingState.FoundIt;
            return state;
        }

        public override float GetConfidence(StringBuilder status = null)
        {
            return _analyser.GetConfidence();
        }

        public override void Reset()
        {
            _codingSm.Reset();
            state = ProbingState.Detecting;
            _analyser.Reset();
        }
    }
}