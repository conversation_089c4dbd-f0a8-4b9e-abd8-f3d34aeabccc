﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Speech</name>
  </assembly>
  <members>
    <member name="T:System.Speech.AudioFormat.AudioBitsPerSample">
      <summary>Enumerates values that describe the bits-per-sample characteristic of an audio format.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.AudioFormat.AudioBitsPerSample.Eight">
      <summary>The audio format has 8 bits per sample.</summary>
    </member>
    <member name="F:System.Speech.AudioFormat.AudioBitsPerSample.Sixteen">
      <summary>The audio format has 16 bits per sample. </summary>
    </member>
    <member name="T:System.Speech.AudioFormat.AudioChannel">
      <summary>Enumerates values that indicate the number of channels in the audio format.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.AudioFormat.AudioChannel.Mono">
      <summary>The audio format has one channel. </summary>
    </member>
    <member name="F:System.Speech.AudioFormat.AudioChannel.Stereo">
      <summary>The audio format has two channels.</summary>
    </member>
    <member name="T:System.Speech.AudioFormat.EncodingFormat">
      <summary>Enumerates values that describe the encoding format of audio.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.AudioFormat.EncodingFormat.Pcm">
      <summary>The encoding format of the audio is Pulse Code Modulation (PCM).</summary>
    </member>
    <member name="F:System.Speech.AudioFormat.EncodingFormat.ALaw">
      <summary>The encoding format of the audio is ALaw.</summary>
    </member>
    <member name="F:System.Speech.AudioFormat.EncodingFormat.ULaw">
      <summary>The encoding format of the audio is ULaw. </summary>
    </member>
    <member name="T:System.Speech.AudioFormat.SpeechAudioFormatInfo">
      <summary>Represents information about an audio format.</summary>
    </member>
    <member name="M:System.Speech.AudioFormat.SpeechAudioFormatInfo.#ctor(System.Int32,System.Speech.AudioFormat.AudioBitsPerSample,System.Speech.AudioFormat.AudioChannel)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.AudioFormat.SpeechAudioFormatInfo" /> class and specifies the samples per second, bits per sample, and the number of channels.</summary>
      <param name="samplesPerSecond">An int containing the samples per second value.</param>
      <param name="bitsPerSample">An int containing the bits per sample value.</param>
      <param name="channel">A member of the AudioChannel enumeration (indicating <paramref name="Mono" /> or <paramref name="Stereo" />).</param>
    </member>
    <member name="M:System.Speech.AudioFormat.SpeechAudioFormatInfo.#ctor(System.Speech.AudioFormat.EncodingFormat,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.AudioFormat.SpeechAudioFormatInfo" /> class and specifies the encoding format, samples per second, bits per sample, number of channels, average bytes per second, block alignment value, and an array containing format-specific data.</summary>
      <param name="encodingFormat">An EncodingFormat object.</param>
      <param name="samplesPerSecond">An int containing the samples per second value.</param>
      <param name="bitsPerSample">An int containing the bits per sample value.</param>
      <param name="channelCount">An int containing the channel count value.</param>
      <param name="averageBytesPerSecond">An int containing the average bytes per second value.</param>
      <param name="blockAlign">An int containing the BlockAlign value.</param>
      <param name="formatSpecificData">A byte array containing the format-specific data.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Speech.AudioFormat.SpeechAudioFormatInfo.AverageBytesPerSecond">
      <summary>Gets the average bytes per second of the audio.</summary>
      <returns>An int containing the average bytes per second value.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Speech.AudioFormat.SpeechAudioFormatInfo.BitsPerSample">
      <summary>Gets the bits per sample of the audio.</summary>
      <returns>An int containing the bits per sample value.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Speech.AudioFormat.SpeechAudioFormatInfo.BlockAlign">
      <summary>Gets and sets the block alignment in bytes.</summary>
      <returns>An int containing the block alignment value.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Speech.AudioFormat.SpeechAudioFormatInfo.ChannelCount">
      <summary>Gets the channel count of the audio.</summary>
      <returns>An int containing the channel count value.</returns>
    </member>
    <member name="P:System.Speech.AudioFormat.SpeechAudioFormatInfo.EncodingFormat">
      <summary>Gets the encoding format of the audio.</summary>
      <returns>The encoding format of the audio.</returns>
    </member>
    <member name="M:System.Speech.AudioFormat.SpeechAudioFormatInfo.Equals(System.Object)">
      <summary>Returns whether a given object is an instance of <see cref="T:System.Speech.AudioFormat.SpeechAudioFormatInfo" /> and equal to the current instance of <see cref="T:System.Speech.AudioFormat.SpeechAudioFormatInfo" />.</summary>
      <returns>Returns true if the current instance of <see cref="T:System.Speech.AudioFormat.SpeechAudioFormatInfo" /> and that obtained from the <paramref name="obj" /> argument are equal, otherwise returns false.</returns>
      <param name="obj">The object to be compared.</param>
    </member>
    <member name="M:System.Speech.AudioFormat.SpeechAudioFormatInfo.FormatSpecificData">
      <summary>Returns the format-specific data of the audio format.</summary>
      <returns>A byte array containing the format-specific data.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.AudioFormat.SpeechAudioFormatInfo.GetHashCode">
      <summary>Returns the hash code of the audio format.</summary>
      <returns>An int containing the hash code.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.AudioFormat.SpeechAudioFormatInfo.SamplesPerSecond">
      <summary>Gets the samples per second of the audio format.</summary>
      <returns>An int containing the samples per second value.</returns>
    </member>
    <member name="T:System.Speech.Recognition.AudioLevelUpdatedEventArgs">
      <summary>Provides data for the AudioLevelUpdated event of the <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> class.  </summary>
    </member>
    <member name="P:System.Speech.Recognition.AudioLevelUpdatedEventArgs.AudioLevel">
      <summary>Gets the new level of audio input after the <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.AudioLevelUpdated" /> or the <see cref="E:System.Speech.Recognition.SpeechRecognizer.AudioLevelUpdated" /> event is raised.</summary>
      <returns>The new level of audio input.</returns>
    </member>
    <member name="T:System.Speech.Recognition.AudioSignalProblem">
      <summary>Contains a list of possible problems in the audio signal coming in to a speech recognition engine.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Recognition.AudioSignalProblem.None">
      <summary>No problems with audio input.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioSignalProblem.TooNoisy">
      <summary>Audio input has too much background noise.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioSignalProblem.NoSignal">
      <summary>Audio input is not detected.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioSignalProblem.TooLoud">
      <summary>Audio input is too loud.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioSignalProblem.TooSoft">
      <summary>Audio input is too quiet.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioSignalProblem.TooFast">
      <summary>Audio input is too fast.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioSignalProblem.TooSlow">
      <summary>Audio input is too slow.</summary>
    </member>
    <member name="T:System.Speech.Recognition.AudioSignalProblemOccurredEventArgs">
      <summary>Provides data for the AudioSignalProblemOccurred event of a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or a <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.AudioSignalProblemOccurredEventArgs.AudioLevel">
      <summary>Gets the audio level associated with the event.</summary>
      <returns>The level of audio input when the AudioSignalProblemOccurred event was raised.</returns>
    </member>
    <member name="P:System.Speech.Recognition.AudioSignalProblemOccurredEventArgs.AudioPosition">
      <summary>Gets the position in the input device's audio stream that indicates where the problem occurred.</summary>
      <returns>The position in the input device's audio stream when the AudioSignalProblemOccurred event was raised.</returns>
    </member>
    <member name="P:System.Speech.Recognition.AudioSignalProblemOccurredEventArgs.AudioSignalProblem">
      <summary>Gets the audio signal problem.</summary>
      <returns>The audio signal problem that caused the AudioSignalProblemOccurred event to be raised.</returns>
    </member>
    <member name="P:System.Speech.Recognition.AudioSignalProblemOccurredEventArgs.RecognizerAudioPosition">
      <summary>Gets the position in the audio input that the recognizer has received that indicates where the problem occurred.</summary>
      <returns>The position in the audio input that the recognizer has received when the AudioSignalProblemOccurred event was raised.</returns>
    </member>
    <member name="T:System.Speech.Recognition.AudioState">
      <summary>Contains a list of possible states for the audio input to a speech recognition engine.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioState.Stopped">
      <summary>Not processing audio input.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioState.Silence">
      <summary>Receiving silence or non-speech background noise.</summary>
    </member>
    <member name="F:System.Speech.Recognition.AudioState.Speech">
      <summary>Receiving speech input.</summary>
    </member>
    <member name="T:System.Speech.Recognition.AudioStateChangedEventArgs">
      <summary>Provides data for the AudioStateChanged event of the <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> class.</summary>
    </member>
    <member name="P:System.Speech.Recognition.AudioStateChangedEventArgs.AudioState">
      <summary>Gets the new state of audio input to the recognizer.</summary>
      <returns>The state of audio input after a <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.AudioStateChanged" /> or a <see cref="E:System.Speech.Recognition.SpeechRecognizer.AudioStateChanged" /> event is raised.</returns>
    </member>
    <member name="T:System.Speech.Recognition.Choices">
      <summary>Represents a set of alternatives in the constraints of a speech recognition grammar.</summary>
    </member>
    <member name="M:System.Speech.Recognition.Choices.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Choices" /> class that contains an empty set of alternatives.</summary>
    </member>
    <member name="M:System.Speech.Recognition.Choices.#ctor(System.Speech.Recognition.GrammarBuilder[])">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Choices" /> class from an array containing one or more <see cref="T:System.Speech.Recognition.GrammarBuilder" /> objects. </summary>
      <param name="alternateChoices">An array containing the set of alternatives.</param>
    </member>
    <member name="M:System.Speech.Recognition.Choices.#ctor(System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Choices" /> class from an array containing one or more <see cref="T:System.String" /> objects. </summary>
      <param name="phrases">An array containing the set of alternatives.</param>
    </member>
    <member name="M:System.Speech.Recognition.Choices.Add(System.Speech.Recognition.GrammarBuilder[])">
      <summary>Adds an array containing one or more <see cref="T:System.Speech.Recognition.GrammarBuilder" /> objects to the set of alternatives.</summary>
      <param name="alternateChoices">The <see cref="T:System.Speech.Recognition.GrammarBuilder" /> objects to add to this <see cref="T:System.Speech.Recognition.Choices" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.Choices.Add(System.String[])">
      <summary>Adds an array containing one or more <see cref="T:System.String" /> objects to the set of alternatives.</summary>
      <param name="phrases">The strings to add to this <see cref="T:System.Speech.Recognition.Choices" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.Choices.ToGrammarBuilder">
      <summary>Returns a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object from this <see cref="T:System.Speech.Recognition.Choices" /> object.</summary>
      <returns>A <see cref="T:System.Speech.Recognition.GrammarBuilder" /> that matches this <see cref="T:System.Speech.Recognition.Choices" /> object.</returns>
    </member>
    <member name="T:System.Speech.Recognition.DictationGrammar">
      <summary>Represents a speech recognition grammar used for free text dictation.</summary>
    </member>
    <member name="M:System.Speech.Recognition.DictationGrammar.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.DictationGrammar" /> class for the default dictation grammar provided by Windows Desktop Speech Technology.</summary>
    </member>
    <member name="M:System.Speech.Recognition.DictationGrammar.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.DictationGrammar" /> class with a specific dictation grammar.</summary>
      <param name="topic">An XML-compliant Universal Resource Identifier (URI) that specifies the dictation grammar, either <paramref name="grammar:dictation" /> or <paramref name="grammar:dictation#spelling" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.DictationGrammar.SetDictationContext(System.String,System.String)">
      <summary>Adds a context to the dictation grammar.</summary>
      <param name="precedingText">Text that indicates the start of a dictation context.</param>
      <param name="subsequentText">Text that indicates the end of a dictation context.</param>
    </member>
    <member name="T:System.Speech.Recognition.DisplayAttributes">
      <summary>Lists the options that the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> object can use to specify white space for the display of a word or punctuation mark.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Recognition.DisplayAttributes.None">
      <summary>The item does not specify how white space is handled.</summary>
    </member>
    <member name="F:System.Speech.Recognition.DisplayAttributes.ZeroTrailingSpaces">
      <summary>The item has no spaces following it.</summary>
    </member>
    <member name="F:System.Speech.Recognition.DisplayAttributes.OneTrailingSpace">
      <summary>The item has one space following it.</summary>
    </member>
    <member name="F:System.Speech.Recognition.DisplayAttributes.TwoTrailingSpaces">
      <summary>The item has two spaces following it.</summary>
    </member>
    <member name="F:System.Speech.Recognition.DisplayAttributes.ConsumeLeadingSpaces">
      <summary>The item has no spaces preceding it.</summary>
    </member>
    <member name="T:System.Speech.Recognition.EmulateRecognizeCompletedEventArgs">
      <summary>Provides data for the EmulateRecognizeCompleted event of the <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> and <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> classes.</summary>
    </member>
    <member name="P:System.Speech.Recognition.EmulateRecognizeCompletedEventArgs.Result">
      <summary>Gets the results of emulated recognition.</summary>
      <returns>Detailed information about the results of recognition, or null if an error occurred.</returns>
    </member>
    <member name="T:System.Speech.Recognition.Grammar">
      <summary>A runtime object that references a speech recognition grammar, which an application can use to define the constraints for speech recognition.</summary>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Grammar" /> class</summary>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Grammar" /> class from a <see cref="T:System.IO.Stream" />.</summary>
      <param name="stream">A stream that describes a speech recognition grammar in a supported format.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> describes a grammar that does not contain a root rule.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.FormatException">The stream does not contain a valid description of a grammar, or describes a grammar that contains a rule reference that cannot be resolved.</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.IO.Stream,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Grammar" /> class from a <see cref="T:System.IO.Stream" /> and specifies a root rule.</summary>
      <param name="stream">A stream that describes a speech recognition grammar in a supported format.</param>
      <param name="ruleName">The identifier of the rule to use as the entry point of the speech recognition grammar, or null to use the default root rule of the grammar description.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ruleName" /> cannot be resolved or is not public, or <paramref name="ruleName" /> is null and the grammar description does not define a root rule.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.FormatException">The stream does not contain a valid description or describes a grammar that contains a rule reference that cannot be resolved.</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.IO.Stream,System.String,System.Object[])">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Grammar" /> class from a <see cref="T:System.IO.Stream" /> and specifies a root rule. </summary>
      <param name="stream">A <see cref="T:System.IO.Stream" /> connected to an input/output object (including files, VisualStudio Resources, and DLLs) that contains a grammar specification.</param>
      <param name="ruleName">The identifier of the rule to use as the entry point of the speech recognition grammar, or null to use the default root rule of the grammar description.</param>
      <param name="parameters">Parameters to be passed to the initialization handler specified by the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnInit" /> property for the entry point or the root rule of the <see cref="T:System.Speech.Recognition.Grammar" /> to be created. This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> is connected to a grammar that: Does not contain the rule specified in <paramref name="ruleName" />Requires initialization parameters different from those specified in <paramref name="parameters" />Contains a relative rule reference that cannot be resolved by the default base <see cref="T:System.Uri" /> rule for grammars</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.IO.Stream,System.String,System.Uri)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Grammar" /> class from a stream, specifies a root rule, and defines a base Uniform Resource Identifier (URI) to resolve relative rule references.</summary>
      <param name="stream">A stream that describes a speech recognition grammar in a supported format.</param>
      <param name="ruleName">The identifier of the rule to use as the entry point of the speech recognition grammar, or null to use the default root rule of the grammar description.</param>
      <param name="baseUri">The base URI to use to resolve any relative rule reference in the grammar description, or null.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ruleName" /> cannot be resolved or is not public, or <paramref name="ruleName" /> is null and the grammar description does not define a root rule.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.FormatException">The stream does not contain a valid description or describes a grammar that contains a rule reference that cannot be resolved.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.IO.Stream,System.String,System.Uri,System.Object[])">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Grammar" /> class a <see cref="T:System.IO.Stream" />and specifies a root rule and a base URI to resolve relative references. </summary>
      <param name="stream">A <see cref="T:System.IO.Stream" /> connected to an input/output object (including files, VisualStudio Resources, and DLLs) that contains a grammar specification.</param>
      <param name="ruleName">The identifier of the rule to use as the entry point of the speech recognition grammar, or null to use the default root rule of the grammar description.</param>
      <param name="baseUri">The base URI to use to resolve any relative rule reference in the grammar description, or null.</param>
      <param name="parameters">Parameters to be passed to the initialization handler specified by the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnInit" /> property for the entry point or the root rule of the <see cref="T:System.Speech.Recognition.Grammar" /> to be created. This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">Any of the parameters contain an invalid value.The <paramref name="stream" /> is connected to a grammar that does not contain the rule specified by <paramref name="ruleName" />.The contents of the array parameters do not match the arguments of any of the rule's initialization handlers.The grammar contains a relative rule reference that cannot be resolved by the default base <see cref="T:System.Uri" /> rule for grammars or the URI supplied by <paramref name="baseUri" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.Speech.Recognition.GrammarBuilder)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Grammar" /> class from a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</summary>
      <param name="builder">An instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> that contains the constraints for the speech recognition grammar.</param>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsDocument)">
      <summary>Initializes a new instance of a <see cref="T:System.Speech.Recognition.Grammar" /> class from an <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> object.</summary>
      <param name="srgsDocument">The constraints for the speech recognition grammar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="srgsDocument" /> does not contain a root rule.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="srgsDocument" /> is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="srgsDocument" /> contains a rule reference that cannot be resolved.</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsDocument,System.String)">
      <summary>Initializes a new instance of a <see cref="T:System.Speech.Recognition.Grammar" /> class from an <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> object and specifies a root rule.</summary>
      <param name="srgsDocument">The constraints for the speech recognition grammar.</param>
      <param name="ruleName">The identifier of the rule to use as the entry point of the speech recognition grammar, or null to use the default root rule of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ruleName" /> cannot be resolved or is not public, or <paramref name="ruleName" /> is null and <paramref name="srgsDocument" /> does not contain a root rule.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="srgsDocument" /> is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="srgsDocument" /> contains a rule reference that cannot be resolved.</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsDocument,System.String,System.Object[])">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Grammar" /> class from an instance of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" />, and specifies the name of a rule to be the entry point to the grammar. </summary>
      <param name="srgsDocument">An instance of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> that contains the constraints for the speech recognition grammar.</param>
      <param name="ruleName">The identifier of the rule to use as the entry point of the speech recognition grammar, or null to use the default root rule of the grammar description.</param>
      <param name="parameters">Parameters to be passed to the initialization handler specified by the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnInit" /> property for the entry point or the root rule of the <see cref="T:System.Speech.Recognition.Grammar" /> to be created. This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">Any of the parameters contain an invalid value.The <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> specified by <paramref name="srgsDocument" /> does not contain the rule specified by <paramref name="ruleName" />.The contents of the array parameters do not match the arguments of any of the rule's initialization handlers.</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsDocument,System.String,System.Uri)">
      <summary>Initializes a new instance of a <see cref="T:System.Speech.Recognition.Grammar" /> class from an <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> object, specifies a root rule, and defines a base Uniform Resource Identifier (URI) to resolve relative rule references.</summary>
      <param name="srgsDocument">The constraints for the speech recognition grammar.</param>
      <param name="ruleName">The identifier of the rule to use as the entry point of the speech recognition grammar, or null to use the default root rule of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" />.</param>
      <param name="baseUri">The base URI to use to resolve any relative rule reference in the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" />, or null.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ruleName" /> cannot be resolved or is not public, or <paramref name="ruleName" /> is null and <paramref name="srgsDocument" /> does not contain a root rule.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="srgsDocument" /> is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="srgsDocument" /> contains a rule reference that cannot be resolved.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsDocument,System.String,System.Uri,System.Object[])">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Grammar" /> class from an instance of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" />, and specifies the name of a rule to be the entry point to the grammar and a base URI to resolve relative references. </summary>
      <param name="srgsDocument">An instance of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> that contains the constraints for the speech recognition grammar.</param>
      <param name="ruleName">The identifier of the rule to use as the entry point of the speech recognition grammar, or null to use the default root rule of the grammar description.</param>
      <param name="baseUri">The base URI to use to resolve any relative rule reference in the grammar description, or null.</param>
      <param name="parameters">Parameters to be passed to the initialization handler specified by the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnInit" /> property for the entry point or the root rule of the <see cref="T:System.Speech.Recognition.Grammar" /> to be created.This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">Any of the parameters contain an invalid value.The <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> specified by <paramref name="srgsDocument" /> does not contain the rule specified in <paramref name="ruleName" />.The contents of the array parameters do not match the arguments of any of the rule's initialization handlers. The grammar has a relative rule reference that cannot be resolved by the default base <see cref="T:System.Uri" /> rule for grammars or the URI supplied by <paramref name="baseUri" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Grammar" /> class from a file.</summary>
      <param name="path">The path of the file that describes a speech recognition grammar in a supported format.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contains the empty string (""), or the file describes a grammar that does not contain a root rule.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.FormatException">The file does not contain a valid description, or describes a grammar that contains a rule reference that cannot be resolved.</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Grammar" /> class from a file and specifies a root rule.</summary>
      <param name="path">The path of the file that describes a speech recognition grammar in a supported format.</param>
      <param name="ruleName">The identifier of the rule to use as the entry point of the speech recognition grammar, or null to use the default root rule of the grammar description.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ruleName" /> cannot be resolved or is not public, <paramref name="path" /> is the empty string (""), or <paramref name="ruleName" /> is null and the grammar description does not define a root rule.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.FormatException">The file does not contain a valid description or describes a grammar that contains a rule reference that cannot be resolved.</exception>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.#ctor(System.String,System.String,System.Object[])">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.Grammar" /> class from a file that contains a grammar definition, and specifies the name of a rule to be the entry point to the grammar..</summary>
      <param name="path">The path to a file, including DLLs, that contains a grammar specification.</param>
      <param name="ruleName">The identifier of the rule to use as the entry point of the speech recognition grammar, or null to use the default root rule of the grammar description.</param>
      <param name="parameters">Parameters to be passed to the initialization handler specified by the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnInit" /> property for the entry point or the root rule of the <see cref="T:System.Speech.Recognition.Grammar" /> to be created. This parameter may be null.</param>
      <exception cref="T:System.ArgumentException">Any of the parameters contain an invalid value.The file specified by <paramref name="path" /> does not contain a valid grammar or the rule specified in <paramref name="ruleName" />.The contents of the array parameters do not match the arguments of any of the rule's initialization handlers.The grammar has a relative rule reference that cannot be resolved by the default base <see cref="T:System.Uri" /> rule for grammars.</exception>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.Enabled">
      <summary>Gets or sets a value that controls whether a <see cref="T:System.Speech.Recognition.Grammar" /> can be used by a speech recognizer to perform recognition.</summary>
      <returns>The  Enabled property returns true if a speech recognizer can perform recognition using the speech recognition grammar; otherwise the property returns false. The default is true.</returns>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.IsStg">
      <summary>Gets whether a grammar is strongly typed.</summary>
      <returns>The IsStg property returns true if the grammar is strongly-typed; otherwise the property returns false.</returns>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.Loaded">
      <summary>Gets whether a <see cref="T:System.Speech.Recognition.Grammar" /> has been loaded by a speech recognizer.</summary>
      <returns>The Loaded property returns true if the referenced speech recognition grammar is currently loaded in a speech recognizer; otherwise the property returns false. The default is false.</returns>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.LoadLocalizedGrammarFromType(System.Type,System.Object[])">
      <summary>The LoadLocalizedGrammarFromType method returns a localized instance of a <see cref="T:System.Speech.Recognition.Grammar" /> object derived from <see cref="T:System.Type" />.</summary>
      <returns>The LoadLocalizedGrammarFromType method returns a valid object based on <see cref="T:System.Speech.Recognition.Grammar" />, or null if there has been an error.</returns>
      <param name="type">In an assembly, the <see cref="T:System.Type" /> of an object based on <see cref="T:System.Speech.Recognition.Grammar" />.</param>
      <param name="onInitParameters">Parameters to be passed to an initialization method of the localizedobject based on <see cref="T:System.Speech.Recognition.Grammar" />. This parameter may be null.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.Name">
      <summary>Gets or sets the name of a <see cref="T:System.Speech.Recognition.Grammar" /> object.</summary>
      <returns>The Name property returns the name of the <see cref="T:System.Speech.Recognition.Grammar" /> object. The default is null.</returns>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.Priority">
      <summary>Gets or sets the priority value of a <see cref="T:System.Speech.Recognition.Grammar" /> object.</summary>
      <returns>The Priority property returns an integer value that represents the relative priority of a specific <see cref="T:System.Speech.Recognition.Grammar" />. The range is from -128 to 127 inclusive. The default is 0.</returns>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.ResourceName">
      <summary>Gets or sets a value with the name of a binary resource that was used to load the current <see cref="T:System.Speech.Recognition.Grammar" />.</summary>
      <returns>The ResourceName property returns the name of the binary resource from which the strongly-typed grammar, used by <see cref="T:System.Speech.Recognition.Grammar" />, was loaded.</returns>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.RuleName">
      <summary>Gets the name of the root rule or entry point of a <see cref="T:System.Speech.Recognition.Grammar" /> object.</summary>
      <returns>The RuleName property returns the identifier for the root rule of the referenced speech recognition grammar. The default is null.</returns>
    </member>
    <member name="E:System.Speech.Recognition.Grammar.SpeechRecognized">
      <summary>Raised when a speech recognizer performs recognition using the <see cref="T:System.Speech.Recognition.Grammar" /> object.</summary>
    </member>
    <member name="M:System.Speech.Recognition.Grammar.StgInit(System.Object[])">
      <summary>The StgInit method initializes a strongly-typed grammar.</summary>
      <param name="parameters">Parameters to be passed to initialize the strongly-typed grammar.This parameter may be null.</param>
    </member>
    <member name="P:System.Speech.Recognition.Grammar.Weight">
      <summary>Gets or sets the weight value of a <see cref="T:System.Speech.Recognition.Grammar" /> object.</summary>
      <returns>The Weight property returns a floating point value indicating the relative weight that a recognition engine  instance should assign to the grammar when processing speech input. The range is from 0.0 to 1.0 inclusive. The default is 1.0.</returns>
    </member>
    <member name="T:System.Speech.Recognition.GrammarBuilder">
      <summary>Provides a mechanism for programmatically building the constraints for a speech recognition grammar.</summary>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor">
      <summary>Initializes a new, empty instance of the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor(System.Speech.Recognition.Choices)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> class from a set of alternatives.</summary>
      <param name="alternateChoices">The set of alternatives.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor(System.Speech.Recognition.GrammarBuilder,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> class from a repeated element.</summary>
      <param name="builder">The repeated element.</param>
      <param name="minRepeat">The minimum number of times that input matching the element defined by <paramref name="builder" /> must occur to constitute a match.</param>
      <param name="maxRepeat">The maximum number of times that input matching the element defined by <paramref name="builder" /> can occur to constitute a match.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor(System.Speech.Recognition.SemanticResultKey)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> class from a semantic key.</summary>
      <param name="key">The semantic key.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor(System.Speech.Recognition.SemanticResultValue)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> class from a semantic value.</summary>
      <param name="value">The semantic value or name/value pair.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> class from a sequence of words.</summary>
      <param name="phrase">The sequence of words.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor(System.String,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> class from the sequence of words in a <see cref="T:System.String" /> and specifies how many times the <see cref="T:System.String" /> can be repeated.</summary>
      <param name="phrase">The repeated sequence of words.</param>
      <param name="minRepeat">The minimum number of times that input matching the phrase must occur to constitute a match.</param>
      <param name="maxRepeat">The maximum number of times that input matching the phrase can occur to constitute a match.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.#ctor(System.String,System.Speech.Recognition.SubsetMatchingMode)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> class for a subset of a sequence of words.</summary>
      <param name="phrase">The sequence of words.</param>
      <param name="subsetMatchingCriteria">The matching mode the speech recognition grammar uses to recognize the phrase.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Add(System.Speech.Recognition.Choices,System.Speech.Recognition.GrammarBuilder)">
      <summary>Creates a new <see cref="T:System.Speech.Recognition.GrammarBuilder" /> that contains a <see cref="T:System.Speech.Recognition.Choices" /> object followed by a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</summary>
      <returns>A <see cref="T:System.Speech.Recognition.GrammarBuilder" /> for the sequence of the <paramref name="choices" /> element followed by the <paramref name="builder" /> element.</returns>
      <param name="choices">The first grammar element, which represents a set of alternatives.</param>
      <param name="builder">The second grammar element.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Add(System.Speech.Recognition.GrammarBuilder,System.Speech.Recognition.Choices)">
      <summary>Creates a new <see cref="T:System.Speech.Recognition.GrammarBuilder" /> that contains a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object followed by a <see cref="T:System.Speech.Recognition.Choices" /> object.</summary>
      <returns>A <see cref="T:System.Speech.Recognition.GrammarBuilder" /> for the sequence of the <paramref name="builder" /> element followed by the <paramref name="choices" /> element.</returns>
      <param name="builder">The first grammar element.</param>
      <param name="choices">The second grammar element, which represents a set of alternatives.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Add(System.Speech.Recognition.GrammarBuilder,System.Speech.Recognition.GrammarBuilder)">
      <summary>Creates a new <see cref="T:System.Speech.Recognition.GrammarBuilder" /> that contains a sequence of two <see cref="T:System.Speech.Recognition.GrammarBuilder" /> objects.</summary>
      <returns>A <see cref="T:System.Speech.Recognition.GrammarBuilder" /> for the sequence of the <paramref name="builder1" /> element followed by the <paramref name="builder2" /> element.</returns>
      <param name="builder1">The first grammar element.</param>
      <param name="builder2">The second grammar element.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Add(System.Speech.Recognition.GrammarBuilder,System.String)">
      <summary>Creates a new <see cref="T:System.Speech.Recognition.GrammarBuilder" /> that contains a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object followed by a phrase.</summary>
      <returns>A <see cref="T:System.Speech.Recognition.GrammarBuilder" /> for the sequence of the <paramref name="builder" /> element followed by the <paramref name="phrase" /> element.</returns>
      <param name="builder">The first grammar element.</param>
      <param name="phrase">The second grammar element, which represents a sequence of words.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Add(System.String,System.Speech.Recognition.GrammarBuilder)">
      <summary>Creates a new <see cref="T:System.Speech.Recognition.GrammarBuilder" /> that contains a phrase followed by a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</summary>
      <returns>A <see cref="T:System.Speech.Recognition.GrammarBuilder" /> for the sequence of the <paramref name="phrase" /> element followed by the <paramref name="builder" /> element.</returns>
      <param name="phrase">The first grammar element, which represents a sequence of words.</param>
      <param name="builder">The second grammar element.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.Speech.Recognition.Choices)">
      <summary>Appends a set of alternatives to the current sequence of grammar elements.</summary>
      <param name="alternateChoices">The set of alternatives to append.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.Speech.Recognition.GrammarBuilder)">
      <summary>Appends a grammar element to the current sequence of grammar elements.</summary>
      <param name="builder">The grammar element to append.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.Speech.Recognition.GrammarBuilder,System.Int32,System.Int32)">
      <summary>Appends a repeated grammar element to the current sequence of grammar elements.</summary>
      <param name="builder">The repeated grammar element to append.</param>
      <param name="minRepeat">The minimum number of times that input matching the element defined by <paramref name="builder" /> must occur to constitute a match.</param>
      <param name="maxRepeat">The maximum number of times that input matching the element defined by <paramref name="builder" /> can occur to constitute a match.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.Speech.Recognition.SemanticResultKey)">
      <summary>Appends a semantic key to the current sequence of grammar elements.</summary>
      <param name="key">The semantic key to append.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.Speech.Recognition.SemanticResultValue)">
      <summary>Appends a semantic value to the current sequence of grammar elements.</summary>
      <param name="value">The semantic value to append.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.String)">
      <summary>Appends a phrase to the current sequence of grammar elements.</summary>
      <param name="phrase">The sequence of words to append.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.String,System.Int32,System.Int32)">
      <summary>Appends a repeated phrase to the current sequence of grammar elements.</summary>
      <param name="phrase">The repeated sequence of words to append.</param>
      <param name="minRepeat">The minimum number of times that input matching <paramref name="phrase" /> must occur to constitute a match.</param>
      <param name="maxRepeat">The maximum number of times that input matching <paramref name="phrase" /> can occur to constitute a match.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.Append(System.String,System.Speech.Recognition.SubsetMatchingMode)">
      <summary>Appends an element for a subset of a phrase to the current sequence of grammar elements.</summary>
      <param name="phrase">The sequence of words to append.</param>
      <param name="subsetMatchingCriteria">The matching mode the grammar uses to recognize the phrase.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.AppendDictation">
      <summary>Appends the default dictation grammar to the current sequence of grammar elements.</summary>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.AppendDictation(System.String)">
      <summary>Appends the specified dictation grammar to the current sequence of grammar elements.</summary>
      <param name="category">The category of the dictation grammar to append.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.AppendRuleReference(System.String)">
      <summary>Appends a grammar definition file to the current sequence of grammar elements.</summary>
      <param name="path">The path or Universal Resource Identifier (URI) of the file that describes a speech recognition grammar in a supported format.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.AppendRuleReference(System.String,System.String)">
      <summary>Appends the specified rule of a grammar definition file to the current sequence of grammar elements.</summary>
      <param name="path">The file path or Universal Resource Identifier (URI) of the file that describes a speech recognition grammar in a supported format.</param>
      <param name="rule">The identifier of the rule to append, or null to append the default root rule of the grammar file.</param>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.AppendWildcard">
      <summary>Appends a recognition grammar element that matches any input to the current sequence of grammar elements.</summary>
    </member>
    <member name="P:System.Speech.Recognition.GrammarBuilder.Culture">
      <summary>Gets or sets the culture of the speech recognition grammar.</summary>
      <returns>The culture of the <see cref="T:System.Speech.Recognition.GrammarBuilder" />. The default is the executing thread's <see cref="T:System.Threading.Thread.CurrentUICulture" /> property.</returns>
    </member>
    <member name="P:System.Speech.Recognition.GrammarBuilder.DebugShowPhrases">
      <summary>Gets a string that shows the contents and structure of the grammar contained by the <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</summary>
      <returns>The current content and structure of the <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</returns>
      <filterpriority>3</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Addition(System.Speech.Recognition.Choices,System.Speech.Recognition.GrammarBuilder)">
      <summary>Creates a new <see cref="T:System.Speech.Recognition.GrammarBuilder" /> that contains a <see cref="T:System.Speech.Recognition.Choices" /> object followed by a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</summary>
      <returns>Returns a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> for the sequence of the <paramref name="choices" /> parameter followed by the <paramref name="builder" /> parameter.</returns>
      <param name="choices">The first grammar element, which represents a set of alternatives.</param>
      <param name="builder">The second grammar element.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Addition(System.Speech.Recognition.GrammarBuilder,System.Speech.Recognition.Choices)">
      <summary>Creates a new <see cref="T:System.Speech.Recognition.GrammarBuilder" /> that contains a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> followed by a <see cref="T:System.Speech.Recognition.Choices" />.</summary>
      <returns>Returns a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> for the sequence of the <paramref name="builder" /> parameter followed by the <paramref name="choices" /> parameter.</returns>
      <param name="builder">The first grammar element.</param>
      <param name="choices">The second grammar element, which represents a set of alternative elements.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Addition(System.Speech.Recognition.GrammarBuilder,System.Speech.Recognition.GrammarBuilder)">
      <summary>Creates a new <see cref="T:System.Speech.Recognition.GrammarBuilder" /> that contains a sequence of two <see cref="T:System.Speech.Recognition.GrammarBuilder" /> objects.</summary>
      <returns>Returns a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> for the sequence of the <paramref name="builder1" /> parameter followed by the <paramref name="builder2" /> parameter.</returns>
      <param name="builder1">The first grammar element.</param>
      <param name="builder2">The second grammar element.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Addition(System.Speech.Recognition.GrammarBuilder,System.String)">
      <summary>Creates a new <see cref="T:System.Speech.Recognition.GrammarBuilder" /> that contains a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> followed by a phrase.</summary>
      <returns>Returns a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> for the sequence of the <paramref name="builder" /> parameter followed by the <paramref name="phrase" /> parameter.</returns>
      <param name="builder">The first grammar element.</param>
      <param name="phrase">The second grammar element, which represents a sequence of words.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Addition(System.String,System.Speech.Recognition.GrammarBuilder)">
      <summary>Creates a new <see cref="T:System.Speech.Recognition.GrammarBuilder" /> that contains a phrase followed by a <see cref="T:System.Speech.Recognition.GrammarBuilder" />.</summary>
      <returns>Returns a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> for the sequence of the <paramref name="phrase" /> parameter followed by the <paramref name="builder" /> parameter.</returns>
      <param name="phrase">The first grammar element, which represents a sequence of words.</param>
      <param name="builder">The second grammar element.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Implicit(System.Speech.Recognition.Choices)~System.Speech.Recognition.GrammarBuilder">
      <summary>Converts a <see cref="T:System.Speech.Recognition.Choices" /> object to a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</summary>
      <returns>The converted <see cref="T:System.Speech.Recognition.Choices" /> object.</returns>
      <param name="choices">The set of alternatives to convert.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Implicit(System.Speech.Recognition.SemanticResultKey)~System.Speech.Recognition.GrammarBuilder">
      <summary>Converts a <see cref="T:System.Speech.Recognition.SemanticResultKey" /> object to a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</summary>
      <returns>The converted <see cref="T:System.Speech.Recognition.SemanticResultKey" /> object.</returns>
      <param name="semanticKey">The semantic key to convert.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Implicit(System.Speech.Recognition.SemanticResultValue)~System.Speech.Recognition.GrammarBuilder">
      <summary>Converts a <see cref="T:System.Speech.Recognition.SemanticResultValue" /> object to a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</summary>
      <returns>The converted <see cref="T:System.Speech.Recognition.SemanticResultValue" /> object.</returns>
      <param name="semanticValue">The <see cref="T:System.Speech.Recognition.SemanticResultValue" /> object to convert.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.GrammarBuilder.op_Implicit(System.String)~System.Speech.Recognition.GrammarBuilder">
      <summary>Converts a string to a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</summary>
      <returns>The converted string.</returns>
      <param name="phrase">The string to convert.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Speech.Recognition.LoadGrammarCompletedEventArgs">
      <summary>Provides data for the LoadGrammarCompleted event of a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> object.</summary>
    </member>
    <member name="P:System.Speech.Recognition.LoadGrammarCompletedEventArgs.Grammar">
      <summary>The <see cref="T:System.Speech.Recognition.Grammar" /> object that has completed loading.</summary>
      <returns>The <see cref="T:System.Speech.Recognition.Grammar" /> that was loaded by the recognizer.</returns>
    </member>
    <member name="T:System.Speech.Recognition.RecognitionEventArgs">
      <summary>Provides information about speech recognition events.</summary>
    </member>
    <member name="P:System.Speech.Recognition.RecognitionEventArgs.Result">
      <summary>Gets the recognition result data associated with the speech recognition event.</summary>
      <returns>The <see cref="P:System.Speech.Recognition.RecognitionEventArgs.Result" /> property returns the <see cref="T:System.Speech.Recognition.RecognitionResult" /> that contains the information about the recognition.</returns>
    </member>
    <member name="T:System.Speech.Recognition.RecognitionResult">
      <summary>Contains detailed information about input that was recognized by instances of <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> or <see cref="T:System.Speech.Recognition.SpeechRecognizer" />.</summary>
    </member>
    <member name="P:System.Speech.Recognition.RecognitionResult.Alternates">
      <summary>Gets the collection of possible matches for input to the speech recognizer.</summary>
      <returns>A read-only collection of the recognition alternates.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognitionResult.Audio">
      <summary>Gets the audio associated with the recognition result.</summary>
      <returns>The audio associated with the recognition result or null if the recognizer generated the result from a call to the EmulateRecognize or EmulateRecognizeAsync methods of a <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> or <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> instance.</returns>
    </member>
    <member name="M:System.Speech.Recognition.RecognitionResult.GetAudioForWordRange(System.Speech.Recognition.RecognizedWordUnit,System.Speech.Recognition.RecognizedWordUnit)">
      <summary>Gets a section of the audio that is associated with a specific range of words in the recognition result.</summary>
      <returns>The section of audio associated with the word range.</returns>
      <param name="firstWord">The first word in the range.</param>
      <param name="lastWord">The last word in the range.</param>
      <exception cref="T:System.NullReferenceException">The recognizer generated the result from a call to EmulateRecognize or EmulateRecognizeAsync methods of the <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> objects.</exception>
    </member>
    <member name="M:System.Speech.Recognition.RecognitionResult.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> instance with the data needed to serialize the target object.</summary>
      <param name="info">The object to populate with data.</param>
      <param name="context">The destination for the serialization.</param>
    </member>
    <member name="T:System.Speech.Recognition.RecognizeCompletedEventArgs">
      <summary>Provides data for the RecognizeCompleted event raised by a <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> or a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> object.</summary>
    </member>
    <member name="P:System.Speech.Recognition.RecognizeCompletedEventArgs.AudioPosition">
      <summary>Gets the location in the input device's audio stream associated with the <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.RecognizeCompleted" /> event.</summary>
      <returns>The location in the input device's audio stream associated with the <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.RecognizeCompleted" /> event.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizeCompletedEventArgs.BabbleTimeout">
      <summary>Gets a value that indicates whether a babble timeout generated the <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.RecognizeCompleted" /> event.</summary>
      <returns>true if the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> has detected only background noise for longer than was specified by its <see cref="P:System.Speech.Recognition.SpeechRecognitionEngine.BabbleTimeout" /> property; otherwise false.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizeCompletedEventArgs.InitialSilenceTimeout">
      <summary>Gets a value that indicates whether an initial silence timeout generated the <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.RecognizeCompleted" /> event.</summary>
      <returns>true if the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> has detected only silence for a longer time period than was specified by its <see cref="P:System.Speech.Recognition.SpeechRecognitionEngine.InitialSilenceTimeout" /> property; otherwise false.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizeCompletedEventArgs.InputStreamEnded">
      <summary>Gets a value indicating whether the input stream ended.</summary>
      <returns>true if the recognizer no longer has audio input; otherwise, false.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizeCompletedEventArgs.Result">
      <summary>Gets the recognition result.</summary>
      <returns>The recognition result if the recognition operation succeeded; otherwise, null.</returns>
    </member>
    <member name="T:System.Speech.Recognition.RecognizedAudio">
      <summary>Represents audio input that is associated with a <see cref="T:System.Speech.Recognition.RecognitionResult" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedAudio.AudioPosition">
      <summary>Gets the location in the input audio stream for the start of the recognized audio.</summary>
      <returns>The location in the input audio stream for the start of the recognized audio.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedAudio.Duration">
      <summary>Gets the duration of the input audio stream for the recognized audio.</summary>
      <returns>The duration within the input audio stream for the recognized audio.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedAudio.Format">
      <summary>Gets the format of the audio processed by a recognition engine.</summary>
      <returns>The format of the audio processed by the speech recognizer.</returns>
    </member>
    <member name="M:System.Speech.Recognition.RecognizedAudio.GetRange(System.TimeSpan,System.TimeSpan)">
      <summary>Selects and returns a section of the current recognized audio as binary data.</summary>
      <returns>Returns a subsection of the recognized audio, as defined by <paramref name="audioPosition" /> and <paramref name="duration" />.</returns>
      <param name="audioPosition">The starting point of the audio data to be returned.</param>
      <param name="duration">The length of the segment to be returned.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="audioPosition" /> and <paramref name="duration" /> define a segment of audio outside the range of the current segment.</exception>
      <exception cref="T:System.InvalidOperationException">The current recognized audio contains no data.</exception>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedAudio.StartTime">
      <summary>Gets the system time at the start of the recognition operation.</summary>
      <returns>The system time at the start of the recognition operation.</returns>
    </member>
    <member name="M:System.Speech.Recognition.RecognizedAudio.WriteToAudioStream(System.IO.Stream)">
      <summary>Writes the entire audio to a stream as raw data.</summary>
      <param name="outputStream">The stream that will receive the audio data.</param>
    </member>
    <member name="M:System.Speech.Recognition.RecognizedAudio.WriteToWaveStream(System.IO.Stream)">
      <summary>Writes audio to a stream in Wave format.</summary>
      <param name="outputStream">The stream that will receive the audio data.</param>
    </member>
    <member name="T:System.Speech.Recognition.RecognizedPhrase">
      <summary>Contains detailed information, generated by the speech recognizer, about the recognized input.</summary>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.Confidence">
      <summary>Gets a value, assigned by the recognizer, that represents the likelihood that a <see cref="T:System.Speech.Recognition.RecognizedPhrase" /> matches a given input. </summary>
      <returns>A relative measure of the certainty of correct recognition of a phrase. The value is from 0.0 to 1.0, for low to high confidence, respectively.</returns>
    </member>
    <member name="M:System.Speech.Recognition.RecognizedPhrase.ConstructSmlFromSemantics">
      <summary>Returns a semantic markup language (SML) document for the semantic information in the <see cref="T:System.Speech.Recognition.RecognizedPhrase" /> object.</summary>
      <returns>Returns an SML description of the semantics of the <see cref="T:System.Speech.Recognition.RecognizedPhrase" /> as an XPath navigable object.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.Grammar">
      <summary>Gets the <see cref="T:System.Speech.Recognition.Grammar" /> that the speech recognizer used to return the <see cref="T:System.Speech.Recognition.RecognizedPhrase" />.</summary>
      <returns>The grammar object that the speech recognizer used to identify the input.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.HomophoneGroupId">
      <summary>Gets the identifier for the homophone group for the phrase.</summary>
      <returns>The identifier for the homophone group for the phrase.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.Homophones">
      <summary>Gets a collection of the recognition alternates that have the same pronunciation as this recognized phrase.</summary>
      <returns>A read-only collection of the recognition alternates that have the same pronunciation as this recognized phrase.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.ReplacementWordUnits">
      <summary>Gets information about the text that the speech recognizer changed as part of speech-to-text normalization.</summary>
      <returns>A collection of <see cref="T:System.Speech.Recognition.ReplacementText" /> objects that describe sections of text that the speech recognizer replaced when it normalized the recognized input.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.Semantics">
      <summary>Gets the semantic information that is associated with the recognized phrase.</summary>
      <returns>The semantic information associated with the recognized phrase.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.Text">
      <summary>Gets the normalized text generated by a speech recognizer from recognized input.</summary>
      <returns>The normalized text generated by a speech recognizer from recognized input.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedPhrase.Words">
      <summary>Gets the words generated by a speech recognizer from recognized input.</summary>
      <returns>The collection of <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> objects generated by a speech recognizer for recognized input.</returns>
    </member>
    <member name="T:System.Speech.Recognition.RecognizedWordUnit">
      <summary>Provides the atomic unit of recognized speech.</summary>
    </member>
    <member name="M:System.Speech.Recognition.RecognizedWordUnit.#ctor(System.String,System.Single,System.String,System.String,System.Speech.Recognition.DisplayAttributes,System.TimeSpan,System.TimeSpan)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> class.</summary>
      <param name="text">The normalized text for a recognized word.This value can be null, "", or <see cref="P:System.String.Empty" />.</param>
      <param name="confidence">A float value from 0.0 through 1.0 indicating the certainty of word recognition.</param>
      <param name="pronunciation">The phonetic spelling of a recognized word.This value can be null, "", or <see cref="F:System.String.Empty" />.</param>
      <param name="lexicalForm">The unnormalized text for a recognized word.This argument is required and may not be null, "", or <see cref="F:System.String.Empty" />.</param>
      <param name="displayAttributes">Defines the use of white space to display recognized words.</param>
      <param name="audioPosition">The location of the recognized word in the audio input stream.This value can be <see cref="F:System.TimeSpan.Zero" />.</param>
      <param name="audioDuration">The length of the audio input corresponding to the recognized word. This value can be <see cref="F:System.TimeSpan.Zero" />.</param>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedWordUnit.Confidence">
      <summary>Gets a value, assigned by the recognizer, that represents the likelihood that a recognized word matches a given input. </summary>
      <returns>A relative measure of the certainty of correct recognition for a word. The value is from 0.0 to 1.0, for low to high confidence, respectively.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedWordUnit.DisplayAttributes">
      <summary>Gets formatting information used to create the text output from the current <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> instance.</summary>
      <returns>Specifies the use of white space to display of the contents of a <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> object. </returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedWordUnit.LexicalForm">
      <summary>Gets the unnormalized text of a recognized word.</summary>
      <returns>Returns a <see cref="T:System.String" /> containing the text of a recognized word, without any normalization.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedWordUnit.Pronunciation">
      <summary>Gets the phonetic spelling of a recognized word.</summary>
      <returns>A string of characters from a supported phonetic alphabet, such as the International Phonetic Alphabet (IPA) or the Universal Phone Set (UPS).</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizedWordUnit.Text">
      <summary>Gets the normalized text for a recognized word.</summary>
      <returns>A string that contains the normalized text output for a given input word.</returns>
    </member>
    <member name="T:System.Speech.Recognition.RecognizeMode">
      <summary>Enumerates values of the recognition mode.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Recognition.RecognizeMode.Single">
      <summary>Specifies that recognition terminates after completion.</summary>
    </member>
    <member name="F:System.Speech.Recognition.RecognizeMode.Multiple">
      <summary>Specifies that recognition does not terminate after completion.</summary>
    </member>
    <member name="T:System.Speech.Recognition.RecognizerInfo">
      <summary>Represents information about a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerInfo.AdditionalInfo">
      <summary>Gets additional information about a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance.</summary>
      <returns>Returns an instance of <see cref="T:System.Collections.Generic.IDictionary" /> containing information about the configuration of a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> object.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerInfo.Culture">
      <summary>Gets the culture supported by a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance.</summary>
      <returns>Returns information about the culture supported by a given <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerInfo.Description">
      <summary>Gets the description of a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance.</summary>
      <returns>Returns a string that describes the configuration for a specific <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance. </returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerInfo.Id">
      <summary>Gets the identifier of a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance.</summary>
      <returns>Returns the identifier for a specific <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance. </returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerInfo.Name">
      <summary>Gets the friendly name of a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance.</summary>
      <returns>Returns the friendly name for a specific <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance. </returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerInfo.SupportedAudioFormats">
      <summary>Gets the audio formats supported by a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance.</summary>
      <returns>Returns a list of audio formats supported by a specific <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance. </returns>
    </member>
    <member name="T:System.Speech.Recognition.RecognizerState">
      <summary>Enumerates values of the recognizer's state.</summary>
    </member>
    <member name="F:System.Speech.Recognition.RecognizerState.Stopped">
      <summary>The recognition engine is not receiving or analyzing audio input.</summary>
    </member>
    <member name="F:System.Speech.Recognition.RecognizerState.Listening">
      <summary>The recognition engine is available to receive and analyze audio input.</summary>
    </member>
    <member name="T:System.Speech.Recognition.RecognizerUpdateReachedEventArgs">
      <summary>Returns data from a <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.RecognizerUpdateReached" /> or a <see cref="E:System.Speech.Recognition.SpeechRecognizer.RecognizerUpdateReached" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerUpdateReachedEventArgs.AudioPosition">
      <summary>Gets the audio position associated with the event.</summary>
      <returns>Returns the location within the speech buffer of a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> or a <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> when it pauses and raises a RecognizerUpdateReached event.</returns>
    </member>
    <member name="P:System.Speech.Recognition.RecognizerUpdateReachedEventArgs.UserToken">
      <summary>Gets the UserToken passed to the system when an application calls <see cref="Overload:System.Speech.Recognition.SpeechRecognitionEngine.RequestRecognizerUpdate" /> or <see cref="Overload:System.Speech.Recognition.SpeechRecognizer.RequestRecognizerUpdate" />.</summary>
      <returns>Returns an object that contains the UserToken.</returns>
    </member>
    <member name="T:System.Speech.Recognition.ReplacementText">
      <summary>Contains information about a speech normalization procedure that has been performed on recognition results.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.ReplacementText.CountOfWords">
      <summary>Gets the number of recognized words replaced by the speech normalization procedure.</summary>
      <returns>Returns the number of recognized words replaced by the speech normalization procedure.</returns>
    </member>
    <member name="P:System.Speech.Recognition.ReplacementText.DisplayAttributes">
      <summary>Gets information about the leading and trailing spaces for the text replaced by the speech normalization procedure.</summary>
      <returns>Returns a <see cref="T:System.Speech.Recognition.DisplayAttributes" /> object that specifies the use of white space to display text replaced by normalization.</returns>
    </member>
    <member name="P:System.Speech.Recognition.ReplacementText.FirstWordIndex">
      <summary>Gets the location of the first recognized word replaced by the speech normalization procedure.</summary>
      <returns>Returns the location of the first recognized word replaced by the speech normalization procedure.</returns>
    </member>
    <member name="P:System.Speech.Recognition.ReplacementText.Text">
      <summary>Gets the recognized text replaced by the speech normalization procedure.</summary>
      <returns>Returns the recognized text replaced by the speech normalization procedure.</returns>
    </member>
    <member name="T:System.Speech.Recognition.SemanticResultKey">
      <summary>Associates a key string with <see cref="T:System.Speech.Recognition.SemanticResultValue" /> values to define <see cref="T:System.Speech.Recognition.SemanticValue" /> objects.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SemanticResultKey.#ctor(System.String,System.Speech.Recognition.GrammarBuilder[])">
      <summary>Assigns a semantic key to one or more <see cref="T:System.Speech.Recognition.GrammarBuilder" /> objects used to create a speech recognition grammar.</summary>
      <param name="semanticResultKey">The tag to be used as a semantic key to access the <see cref="T:System.Speech.Recognition.SemanticValue" /> instance associated with the <see cref="T:System.Speech.Recognition.GrammarBuilder" /> objects specified by the <paramref name="builders" /> argument. </param>
      <param name="builders">An array of grammar components that will be associated with a <see cref="T:System.Speech.Recognition.SemanticValue" /> object accessible with the tag defined in <paramref name="semanticResultKey" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticResultKey.#ctor(System.String,System.String[])">
      <summary>Assigns a semantic key to one or more <see cref="T:System.String" /> instances used to create a speech recognition grammar.</summary>
      <param name="semanticResultKey">The tag to be used access the <see cref="T:System.Speech.Recognition.SemanticValue" /> instance associated with the <see cref="T:System.String" />objects specified by the <paramref name="phrases" /> argument.</param>
      <param name="phrases">One or more <see cref="T:System.String" /> objects, whose concatenated text will be associated with a <see cref="T:System.Speech.Recognition.SemanticValue" /> object accessible with the tag defined in <paramref name="semanticResultKey" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticResultKey.ToGrammarBuilder">
      <summary>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from the current <see cref="T:System.Speech.Recognition.SemanticResultKey" /> instance.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SemanticResultValue">
      <summary>Represents a semantic value and optionally associates the value with a component of a speech recognition grammar.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SemanticResultValue.#ctor(System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SemanticResultValue" /> class and specifies a semantic value.</summary>
      <param name="value">The value managed by <see cref="T:System.Speech.Recognition.SemanticResultValue" />. Must be of type bool, int, float, or string.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticResultValue.#ctor(System.Speech.Recognition.GrammarBuilder,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SemanticResultValue" /> class and associates a semantic value with a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</summary>
      <param name="builder">A grammar component to be used in recognition.</param>
      <param name="value">The value managed by <see cref="T:System.Speech.Recognition.SemanticResultValue" />. Must be of type bool, int, float, or string.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticResultValue.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SemanticResultValue" /> class and associates a semantic value with a <see cref="T:System.String" /> object.</summary>
      <param name="phrase">A phrase to be used in recognition.</param>
      <param name="value">The value managed by <see cref="T:System.Speech.Recognition.SemanticResultValue" />. Must be of type bool, int, float, or string.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticResultValue.ToGrammarBuilder">
      <summary>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from the current <see cref="T:System.Speech.Recognition.SemanticResultValue" /> instance.</summary>
      <returns>Returns an instance of <see cref="T:System.Speech.Recognition.GrammarBuilder" /> constructed from the current <see cref="T:System.Speech.Recognition.SemanticResultValue" /> instance.</returns>
    </member>
    <member name="T:System.Speech.Recognition.SemanticValue">
      <summary>Represents the semantic organization of a recognized phrase.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.#ctor(System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SemanticValue" /> class and specifies a semantic value.</summary>
      <param name="value">The information to be stored in the <see cref="T:System.Speech.Recognition.SemanticValue" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.#ctor(System.String,System.Object,System.Single)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SemanticValue" /> class and specifies a semantic value, a key name, and a confidence level.</summary>
      <param name="keyName">A key that can be used to reference this <see cref="T:System.Speech.Recognition.SemanticValue" /> instance.</param>
      <param name="value">An object containing information to be stored in the <see cref="T:System.Speech.Recognition.SemanticValue" /> object.</param>
      <param name="confidence">A float containing an estimate of the certainty of semantic analysis.</param>
    </member>
    <member name="P:System.Speech.Recognition.SemanticValue.Confidence">
      <summary>Returns a relative measure of the certainty as to the correctness of the semantic parsing that returned the current instance of <see cref="T:System.Speech.Recognition.SemanticValue" />.</summary>
      <returns>Returns a float that is a relative measure of the certainty of semantic parsing that returned the current instance of <see cref="T:System.Speech.Recognition.SemanticValue" />.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.Contains(System.Collections.Generic.KeyValuePair{System.String,System.Speech.Recognition.SemanticValue})">
      <summary>Indicates whether the current <see cref="T:System.Speech.Recognition.SemanticValue" /> instance collection contains a specific key and a specific instance of <see cref="T:System.Speech.Recognition.SemanticValue" /> expressed as a key/value pair.</summary>
      <returns>Returns a bool which is true if the current <see cref="T:System.Speech.Recognition.SemanticValue" /> contains an instance of KeyValuePair&lt;String, SemanticValue&gt; for a specified value of the key string and the <see cref="T:System.Speech.Recognition.SemanticValue" />. Otherwise, false is returned.</returns>
      <param name="item">An instance of <see cref="T:System.Collections.Generic.KeyValuePair" /> instantiated for a given value of a key string and a <see cref="T:System.Speech.Recognition.SemanticValue" /> instance. </param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.ContainsKey(System.String)">
      <summary>Indicates whether the current <see cref="T:System.Speech.Recognition.SemanticValue" /> instance collection contains a child <see cref="T:System.Speech.Recognition.SemanticValue" /> instance with a given key string.</summary>
      <returns>Returns a bool, true if a child instance <see cref="T:System.Speech.Recognition.SemanticValue" /> tagged with the string <paramref name="key" /> is found, false if not.</returns>
      <param name="key">
        <see cref="T:System.String" /> containing the key string used to identify a child instance of <see cref="T:System.Speech.Recognition.SemanticValue" /> under the current <see cref="T:System.Speech.Recognition.SemanticValue" />.</param>
    </member>
    <member name="P:System.Speech.Recognition.SemanticValue.Count">
      <summary>Returns the number of child <see cref="T:System.Speech.Recognition.SemanticValue" /> objects under the current <see cref="T:System.Speech.Recognition.SemanticValue" /> instance.</summary>
      <returns>The number of child <see cref="T:System.Speech.Recognition.SemanticValue" /> objects under the current <see cref="T:System.Speech.Recognition.SemanticValue" />.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.Equals(System.Object)"></member>
    <member name="M:System.Speech.Recognition.SemanticValue.GetHashCode">
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SemanticValue.Item(System.String)">
      <summary>Returns child <see cref="T:System.Speech.Recognition.SemanticValue" /> instances that  belong to the current <see cref="T:System.Speech.Recognition.SemanticValue" />.</summary>
      <returns>Returns a child of the current <see cref="T:System.Speech.Recognition.SemanticValue" /> that can be indexed as part of a key value pair: KeyValuePair&lt;String,SemanticValue&gt;. </returns>
      <param name="key">A key for a KeyValuePair&lt;String, SemanticValue&gt; contained in the current instance of <see cref="T:System.Speech.Recognition.SemanticValue" />. </param>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Thrown if no child member of the current instance of <see cref="T:System.Speech.Recognition.SemanticValue" /> has the key matching the <paramref name="key" /> parameter.</exception>
      <exception cref="T:System.InvalidOperationException">Thrown if code attempts to change the <see cref="T:System.Speech.Recognition.SemanticValue" /> at a given index.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{System.String,System.Speech.Recognition.SemanticValue})">
      <summary>Adds the specified key and <see cref="T:System.Speech.Recognition.SemanticValue" /> to the collection.</summary>
      <param name="key">A key for a <see cref="T:System.Speech.Recognition.SemanticValue" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Removes all key/value pairs from the collection. </summary>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Speech.Recognition.SemanticValue}[],System.Int32)">
      <summary>Copies a key/value pair to a specific location in a targeted array.</summary>
      <param name="array">The array of key/value pairs that is the target of the operation.</param>
      <param name="index">An integer that specifies the location in the array to which the key/value pair will be copied.</param>
    </member>
    <member name="P:System.Speech.Recognition.SemanticValue.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value that indicates whether the collection is read-only.</summary>
      <returns>Returns a value that indicates whether the collection is read-only.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{System.String,System.Speech.Recognition.SemanticValue})">
      <summary>Removes the specified key and <see cref="T:System.Speech.Recognition.SemanticValue" /> from the collection.</summary>
      <returns>Returns true if the key/value pair was successfully removed from the collection; otherwise the method returns false. This method also returns false if the key/value pair is not found in the collection. </returns>
      <param name="key">A key for a <see cref="T:System.Speech.Recognition.SemanticValue" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#IDictionary{TKey@TValue}#Add(System.String,System.Speech.Recognition.SemanticValue)">
      <summary>Adds the specified key and <see cref="T:System.Speech.Recognition.SemanticValue" /> to the dictionary.</summary>
      <param name="key">A key for a <see cref="T:System.Speech.Recognition.SemanticValue" />.</param>
      <param name="value">The <see cref="T:System.Speech.Recognition.SemanticValue" /> to add.</param>
    </member>
    <member name="P:System.Speech.Recognition.SemanticValue.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Gets a collection that contains the keys from a dictionary of key/value pairs.</summary>
      <returns>Returns a collection that contains the keys from a dictionary of key/value pairs.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#IDictionary{TKey@TValue}#Remove(System.String)">
      <summary>Removes the specified key and <see cref="T:System.Speech.Recognition.SemanticValue" /> from the dictionary.</summary>
      <returns>Returns true if the key/value pair was successfully removed from the dictionary; otherwise the method returns false. This method also returns false if the key/value pair is not found in the dictionary.</returns>
      <param name="key">A key for a <see cref="T:System.Speech.Recognition.SemanticValue" />.</param>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#IDictionary{TKey@TValue}#TryGetValue(System.String,System.Speech.Recognition.SemanticValue@)">
      <summary>Gets the <see cref="T:System.Speech.Recognition.SemanticValue" /> associated with the specified key.</summary>
      <returns>Returns true if the dictionary contains a key/value pair with the specified key; otherwise the method returns false.</returns>
      <param name="key">A key for a <see cref="T:System.Speech.Recognition.SemanticValue" />.</param>
      <param name="value">The <see cref="T:System.Speech.Recognition.SemanticValue" /> to get.</param>
    </member>
    <member name="P:System.Speech.Recognition.SemanticValue.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Gets a collection that contains the values from a dictionary of key/value pairs.</summary>
      <returns>Returns a collection that contains the values from a dictionary of key/value pairs.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>Returns an enumerator that iterates through a collection.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SemanticValue.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>Returns an enumerator that iterates through a collection.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SemanticValue.Value">
      <summary>A read-only property that returns the information contained in the current <see cref="T:System.Speech.Recognition.SemanticValue" />.</summary>
      <returns>Returns an <see cref="T:System.Object" /> instance containing the information stored in the current <see cref="T:System.Speech.Recognition.SemanticValue" /> instance.</returns>
    </member>
    <member name="T:System.Speech.Recognition.SpeechDetectedEventArgs">
      <summary>Returns data from <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechDetected" /> or <see cref="E:System.Speech.Recognition.SpeechRecognizer.SpeechDetected" /> events.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechDetectedEventArgs.AudioPosition">
      <summary>Gets the position in the audio stream where speech was detected.</summary>
      <returns>Returns the location of a detected phrase within a recognition engine’s speech buffer.</returns>
    </member>
    <member name="T:System.Speech.Recognition.SpeechHypothesizedEventArgs">
      <summary>Returns notification from <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechHypothesized" /> or <see cref="E:System.Speech.Recognition.SpeechRecognizer.SpeechHypothesized" /> events.This class supports the .NET Framework infrastructure and is not intended to be used directly from application code.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SpeechRecognitionEngine">
      <summary>Provides the means to access and manage an in-process speech recognition engine.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> class using the default speech recognizer for the system.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.#ctor(System.Globalization.CultureInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> class using the default speech recognizer for a specified locale.</summary>
      <param name="culture">The locale that the speech recognizer must support.</param>
      <exception cref="T:System.ArgumentException">None of the installed speech recognizers support the specified locale, or <paramref name="culture" /> is the invariant culture.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="Culture" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.#ctor(System.Speech.Recognition.RecognizerInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> using the information in a <see cref="T:System.Speech.Recognition.RecognizerInfo" /> object to specify the recognizer to use.</summary>
      <param name="recognizerInfo">The information for the specific speech recognizer.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> class with a string parameter that specifies the name of the recognizer to use.</summary>
      <param name="recognizerId">The token name of the speech recognizer to use.</param>
      <exception cref="T:System.ArgumentException">No speech recognizer with that token name is installed, or <paramref name="recognizerId" /> is the empty string ("").</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="recognizerId" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.AudioFormat">
      <summary>Gets the format of the audio being received by the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</summary>
      <returns>The format of audio at the input to the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance, or null if the input is not configured or set to the null input.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.AudioLevel">
      <summary>Gets the level of the audio being received by the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</summary>
      <returns>The audio level of the input to the speech recognizer, from 0 through 100.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.AudioLevelUpdated">
      <summary>Raised when the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> reports the level of its audio input.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.AudioPosition">
      <summary>Gets the current location in the audio stream being generated by the device that is providing input to the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</summary>
      <returns>The current location in the audio stream being generated by the input device.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.AudioSignalProblemOccurred">
      <summary>Raised when the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> detects a problem in the audio signal.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.AudioState">
      <summary>Gets the state of the audio being received by the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</summary>
      <returns>The state of the audio input to the speech recognizer.</returns>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.AudioStateChanged">
      <summary>Raised when the state changes in the audio being received by the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.BabbleTimeout">
      <summary>Gets or sets the time interval during which a <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> accepts input containing only background noise, before finalizing recognition.</summary>
      <returns>The duration of the time interval. </returns>
      <exception cref="T:System.ArgumentOutOfRangeException">This property is set to less than 0 seconds.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.Dispose">
      <summary>Disposes the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> object.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.Dispose(System.Boolean)">
      <summary>Disposes the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> object and releases resources used during the session.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.EmulateRecognize(System.Speech.Recognition.RecognizedWordUnit[],System.Globalization.CompareOptions)">
      <summary>Emulates input of specific words to the speech recognizer, using text in place of audio for synchronous speech recognition, and specifies how the recognizer handles Unicode comparison between the words and the loaded speech recognition grammars.</summary>
      <returns>The result for the recognition operation, or null if the operation is not successful or the recognizer is not enabled.</returns>
      <param name="wordUnits">An array of word units that contains the input for the recognition operation.</param>
      <param name="compareOptions">A bitwise combination of the enumeration values that describe the type of comparison to use for the emulated recognition operation.</param>
      <exception cref="T:System.InvalidOperationException">The recognizer has no speech recognition grammars loaded.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="wordUnits" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="wordUnits" /> contains one or more null elements.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="compareOptions" /> contains the <see cref="F:System.Globalization.CompareOptions.IgnoreNonSpace" />, <see cref="F:System.Globalization.CompareOptions.IgnoreSymbols" />, or <see cref="F:System.Globalization.CompareOptions.StringSort" /> flag.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.EmulateRecognize(System.String)">
      <summary>Emulates input of a phrase to the speech recognizer, using text in place of audio for synchronous speech recognition.</summary>
      <returns>The result for the recognition operation, or null if the operation is not successful or the recognizer is not enabled.</returns>
      <param name="inputText">The input for the recognition operation.</param>
      <exception cref="T:System.InvalidOperationException">The recognizer has no speech recognition grammars loaded.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputText" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputText" /> is the empty string ("").</exception>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.EmulateRecognize(System.String,System.Globalization.CompareOptions)">
      <summary>Emulates input of a phrase to the speech recognizer, using text in place of audio for synchronous speech recognition, and specifies how the recognizer handles Unicode comparison between the phrase and the loaded speech recognition grammars.</summary>
      <returns>The result for the recognition operation, or null if the operation is not successful or the recognizer is not enabled.</returns>
      <param name="inputText">The input phrase for the recognition operation.</param>
      <param name="compareOptions">A bitwise combination of the enumeration values that describe the type of comparison to use for the emulated recognition operation.</param>
      <exception cref="T:System.InvalidOperationException">The recognizer has no speech recognition grammars loaded.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputText" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputText" /> is the empty string ("").</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="compareOptions" /> contains the <see cref="F:System.Globalization.CompareOptions.IgnoreNonSpace" />, <see cref="F:System.Globalization.CompareOptions.IgnoreSymbols" />, or <see cref="F:System.Globalization.CompareOptions.StringSort" /> flag.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.EmulateRecognizeAsync(System.Speech.Recognition.RecognizedWordUnit[],System.Globalization.CompareOptions)">
      <summary>Emulates input of specific words to the speech recognizer, using an array of <see cref="T:System.Speech.Recognition.RecognizedWordUnit" /> objects in place of audio for asynchronous speech recognition, and specifies how the recognizer handles Unicode comparison between the words and the loaded speech recognition grammars. </summary>
      <param name="wordUnits">An array of word units that contains the input for the recognition operation.</param>
      <param name="compareOptions">A bitwise combination of the enumeration values that describe the type of comparison to use for the emulated recognition operation.</param>
      <exception cref="T:System.InvalidOperationException">The recognizer has no speech recognition grammars loaded, or the recognizer has an asynchronous recognition operation that is not yet complete.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="wordUnits" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="wordUnits" /> contains one or more null elements.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="compareOptions" /> contains the <see cref="F:System.Globalization.CompareOptions.IgnoreNonSpace" />, <see cref="F:System.Globalization.CompareOptions.IgnoreSymbols" />, or <see cref="F:System.Globalization.CompareOptions.StringSort" /> flag.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.EmulateRecognizeAsync(System.String)">
      <summary>Emulates input of a phrase to the speech recognizer, using text in place of audio for asynchronous speech recognition.</summary>
      <param name="inputText">The input for the recognition operation.</param>
      <exception cref="T:System.InvalidOperationException">The recognizer has no speech recognition grammars loaded, or the recognizer has an asynchronous recognition operation that is not yet complete.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputText" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputText" /> is the empty string ("").</exception>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.EmulateRecognizeAsync(System.String,System.Globalization.CompareOptions)">
      <summary>Emulates input of a phrase to the speech recognizer, using text in place of audio for asynchronous speech recognition, and specifies how the recognizer handles Unicode comparison between the phrase and the loaded speech recognition grammars.</summary>
      <param name="inputText">The input phrase for the recognition operation.</param>
      <param name="compareOptions">A bitwise combination of the enumeration values that describe the type of comparison to use for the emulated recognition operation.</param>
      <exception cref="T:System.InvalidOperationException">The recognizer has no speech recognition grammars loaded, or the recognizer has an asynchronous recognition operation that is not yet complete.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputText" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputText" /> is the empty string ("").</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="compareOptions" /> contains the <see cref="F:System.Globalization.CompareOptions.IgnoreNonSpace" />, <see cref="F:System.Globalization.CompareOptions.IgnoreSymbols" />, or <see cref="F:System.Globalization.CompareOptions.StringSort" /> flag.</exception>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.EmulateRecognizeCompleted">
      <summary>Raised when the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> finalizes an asynchronous recognition operation of emulated input.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.EndSilenceTimeout">
      <summary>Gets or sets the interval of silence that the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> will accept at the end of unambiguous input before finalizing a recognition operation.</summary>
      <returns>The duration of the interval of silence. </returns>
      <exception cref="T:System.ArgumentOutOfRangeException">This property is set to less than 0 seconds or greater than 10 seconds.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.EndSilenceTimeoutAmbiguous">
      <summary>Gets or sets the interval of silence that the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> will accept at the end of ambiguous input before finalizing a recognition operation.</summary>
      <returns>The duration of the interval of silence.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">This property is set to less than 0 seconds or greater than 10 seconds.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.Grammars">
      <summary>Gets a collection of the <see cref="T:System.Speech.Recognition.Grammar" /> objects that are loaded in this <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance.</summary>
      <returns>The collection of <see cref="T:System.Speech.Recognition.Grammar" /> objects.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.InitialSilenceTimeout">
      <summary>Gets or sets the time interval during which a <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> accepts input containing only silence before finalizing recognition.</summary>
      <returns>The duration of the interval of silence.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">This property is set to less than 0 seconds.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.InstalledRecognizers">
      <summary>Returns information for all of the installed speech recognizers on the current system.</summary>
      <returns>A read-only collection of the <see cref="T:System.Speech.Recognition.RecognizerInfo" /> objects that describe the installed recognizers.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.LoadGrammar(System.Speech.Recognition.Grammar)">
      <summary>Synchronously loads a <see cref="T:System.Speech.Recognition.Grammar" /> object.</summary>
      <param name="grammar">The grammar object to load.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="Grammar" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="Grammar" /> is not in a valid state.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.LoadGrammarAsync(System.Speech.Recognition.Grammar)">
      <summary>Asynchronously loads a speech recognition grammar.</summary>
      <param name="grammar">The speech recognition grammar to load.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="Grammar" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="Grammar" /> is not in a valid state.</exception>
      <exception cref="T:System.OperationCanceledException">The asynchronous operation was canceled.</exception>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.LoadGrammarCompleted">
      <summary>Raised when the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> finishes the asynchronous loading of a <see cref="T:System.Speech.Recognition.Grammar" /> object.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.MaxAlternates">
      <summary>Gets or sets the maximum number of alternate recognition results that the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> returns for each recognition operation.</summary>
      <returns>The number of alternate results to return.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Speech.Recognition.SpeechRecognitionEngine.MaxAlternates" /> is set to a value less than 0.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.QueryRecognizerSetting(System.String)">
      <summary>Returns the values of settings for the recognizer.</summary>
      <returns>The value of the setting.</returns>
      <param name="settingName">The name of the setting to return.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="settingName" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="settingName" /> is the empty string ("").</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">The recognizer does not have a setting by that name.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.Recognize">
      <summary>Performs a synchronous speech recognition operation.</summary>
      <returns>The recognition result for the input, or null if the operation is not successful or the recognizer is not enabled.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.Recognize(System.TimeSpan)">
      <summary>Performs a synchronous speech recognition operation with a specified initial silence timeout period.</summary>
      <returns>The recognition result for the input, or null if the operation is not successful or the recognizer is not enabled.</returns>
      <param name="initialSilenceTimeout">The interval of time a speech recognizer accepts input containing only silence before finalizing recognition.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.RecognizeAsync">
      <summary>Performs a single, asynchronous speech recognition operation.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.RecognizeAsync(System.Speech.Recognition.RecognizeMode)">
      <summary>Performs one or more asynchronous speech recognition operations.</summary>
      <param name="mode">Indicates whether to perform one or multiple recognition operations.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.RecognizeAsyncCancel">
      <summary>Terminates asynchronous recognition without waiting for the current recognition operation to complete.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.RecognizeAsyncStop">
      <summary>Stops asynchronous recognition after the current recognition operation completes.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.RecognizeCompleted">
      <summary>Raised when the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> finalizes an asynchronous recognition operation.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.RecognizerAudioPosition">
      <summary>Gets the current location of the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> in the audio input that it is processing.</summary>
      <returns>The position of the recognizer in the audio input that it is processing.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognitionEngine.RecognizerInfo">
      <summary>Gets information about the current instance of <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" />.</summary>
      <returns>Information about the current speech recognizer.</returns>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.RecognizerUpdateReached">
      <summary>Raised when a running <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> pauses to accept modifications.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.RequestRecognizerUpdate">
      <summary>Requests that the recognizer pauses to update its state.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.RequestRecognizerUpdate(System.Object)">
      <summary>Requests that the recognizer pauses to update its state and provides a user token for the associated event.</summary>
      <param name="userToken">User-defined information that contains information for the operation.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.RequestRecognizerUpdate(System.Object,System.TimeSpan)">
      <summary>Requests that the recognizer pauses to update its state and provides an offset and a user token for the associated event.</summary>
      <param name="userToken">User-defined information that contains information for the operation.</param>
      <param name="audioPositionAheadToRaiseUpdate">The offset from the current <see cref="P:System.Speech.Recognition.SpeechRecognitionEngine.AudioPosition" /> to delay the request.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.SetInputToAudioStream(System.IO.Stream,System.Speech.AudioFormat.SpeechAudioFormatInfo)">
      <summary>Configures the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> object to receive input from an audio stream.</summary>
      <param name="audioSource">The audio input stream.</param>
      <param name="audioFormat">The format of the audio input.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.SetInputToDefaultAudioDevice">
      <summary>Configures the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> object to receive input from the default audio device.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.SetInputToNull">
      <summary>Disables the input to the speech recognizer.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.SetInputToWaveFile(System.String)">
      <summary>Configures the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> object to receive input from a Waveform audio format (.wav) file.</summary>
      <param name="path">The path of the file to use as input.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.SetInputToWaveStream(System.IO.Stream)">
      <summary>Configures the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> object to receive input from a stream that contains Waveform audio format (.wav) data.</summary>
      <param name="audioSource">The stream containing the audio data.</param>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechDetected">
      <summary>Raised when the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> detects input that it can identify as speech.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechHypothesized">
      <summary>Raised when the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> has recognized a word or words that may be a component of multiple complete phrases in a grammar.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechRecognitionRejected">
      <summary>Raised when the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> receives input that does not match any of its loaded and enabled <see cref="T:System.Speech.Recognition.Grammar" /> objects.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechRecognized">
      <summary>Raised when the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> receives input that matches any of its loaded and enabled <see cref="T:System.Speech.Recognition.Grammar" /> objects.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.UnloadAllGrammars">
      <summary>Unloads all <see cref="T:System.Speech.Recognition.Grammar" /> objects from the recognizer.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.UnloadGrammar(System.Speech.Recognition.Grammar)">
      <summary>Unloads a specified <see cref="T:System.Speech.Recognition.Grammar" /> object from the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> instance.</summary>
      <param name="grammar">The grammar object to unload.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="Grammar" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The grammar is not loaded in this recognizer, or this recognizer is currently loading the grammar asynchronously.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.UpdateRecognizerSetting(System.String,System.Int32)">
      <summary>Updates the specified setting for the <see cref="T:System.Speech.Recognition.SpeechRecognitionEngine" /> with the specified integer value.</summary>
      <param name="settingName">The name of the setting to update.</param>
      <param name="updatedValue">The new value for the setting.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="settingName" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="settingName" /> is the empty string ("").</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">The recognizer does not have a setting by that name.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognitionEngine.UpdateRecognizerSetting(System.String,System.String)">
      <summary>Updates the specified speech recognition engine setting with the specified string value.</summary>
      <param name="settingName">The name of the setting to update.</param>
      <param name="updatedValue">The new value for the setting.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="settingName" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="settingName" /> is the empty string ("").</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">The recognizer does not have a setting by that name.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Speech.Recognition.SpeechRecognitionRejectedEventArgs">
      <summary>Provides information for the <see cref="E:System.Speech.Recognition.SpeechRecognizer.SpeechRecognitionRejected" />  and <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechRecognitionRejected" /> events.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SpeechRecognizedEventArgs">
      <summary>Provides information for the <see cref="E:System.Speech.Recognition.Grammar.SpeechRecognized" />, <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechRecognized" />, and <see cref="E:System.Speech.Recognition.SpeechRecognizer.SpeechRecognized" /> events.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SpeechRecognizer">
      <summary>Provides access to the shared speech recognition service available on the Windows desktop.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> class.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.AudioFormat">
      <summary>Gets the format of the audio being received by the speech recognizer.</summary>
      <returns>The audio input format for the speech recognizer, or null if the input to the recognizer is not configured.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.AudioLevel">
      <summary>Gets the level of the audio being received by the speech recognizer.</summary>
      <returns>The audio level of the input to the speech recognizer, from 0 through 100.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.AudioLevelUpdated">
      <summary>Occurs when the shared recognizer reports the level of its audio input.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.AudioPosition">
      <summary>Gets the current location in the audio stream being generated by the device that is providing input to the speech recognizer.</summary>
      <returns>The current location in the speech recognizer's audio input stream through which it has received input.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.AudioSignalProblemOccurred">
      <summary>Occurs when the recognizer encounters a problem in the audio signal.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.AudioState">
      <summary>Gets the state of the audio being received by the speech recognizer.</summary>
      <returns>The state of the audio input to the speech recognizer.</returns>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.AudioStateChanged">
      <summary>Occurs when the state changes in the audio being received by the recognizer.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.Dispose">
      <summary>Disposes the <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> object.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.Dispose(System.Boolean)">
      <summary>Disposes the <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> object and releases resources used during the session.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.EmulateRecognize(System.Speech.Recognition.RecognizedWordUnit[],System.Globalization.CompareOptions)">
      <summary>Emulates input of specific words to the shared speech recognizer, using text instead of audio for synchronous speech recognition, and specifies how the recognizer handles Unicode comparison between the words and the loaded speech recognition grammars.</summary>
      <returns>The recognition result for the recognition operation, or null, if the operation is not successful or Windows Speech Recognition is in the Sleeping state.</returns>
      <param name="wordUnits">An array of word units that contains the input for the recognition operation.</param>
      <param name="compareOptions">A bitwise combination of the enumeration values that describe the type of comparison to use for the emulated recognition operation.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.EmulateRecognize(System.String)">
      <summary>Emulates input of a phrase to the shared speech recognizer, using text instead of audio for synchronous speech recognition.</summary>
      <returns>The recognition result for the recognition operation, or null, if the operation is not successful or Windows Speech Recognition is in the Sleeping state.</returns>
      <param name="inputText">The input for the recognition operation.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.EmulateRecognize(System.String,System.Globalization.CompareOptions)">
      <summary>Emulates input of a phrase to the shared speech recognizer, using text instead of audio for synchronous speech recognition, and specifies how the recognizer handles Unicode comparison between the phrase and the loaded speech recognition grammars.</summary>
      <returns>The recognition result for the recognition operation, or null, if the operation is not successful or Windows Speech Recognition is in the Sleeping state.</returns>
      <param name="inputText">The input phrase for the recognition operation.</param>
      <param name="compareOptions">A bitwise combination of the enumeration values that describe the type of comparison to use for the emulated recognition operation.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.EmulateRecognizeAsync(System.Speech.Recognition.RecognizedWordUnit[],System.Globalization.CompareOptions)">
      <summary>Emulates input of specific words to the shared speech recognizer, using text instead of audio for asynchronous speech recognition, and specifies how the recognizer handles Unicode comparison between the words and the loaded speech recognition grammars.</summary>
      <param name="wordUnits">An array of word units that contains the input for the recognition operation.</param>
      <param name="compareOptions">A bitwise combination of the enumeration values that describe the type of comparison to use for the emulated recognition operation.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.EmulateRecognizeAsync(System.String)">
      <summary>Emulates input of a phrase to the shared speech recognizer, using text instead of audio for asynchronous speech recognition.</summary>
      <param name="inputText">The input for the recognition operation.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.EmulateRecognizeAsync(System.String,System.Globalization.CompareOptions)">
      <summary>Emulates input of a phrase to the shared speech recognizer, using text instead of audio for asynchronous speech recognition, and specifies how the recognizer handles Unicode comparison between the phrase and the loaded speech recognition grammars.</summary>
      <param name="inputText">The input phrase for the recognition operation.</param>
      <param name="compareOptions">A bitwise combination of the enumeration values that describe the type of comparison to use for the emulated recognition operation.</param>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.EmulateRecognizeCompleted">
      <summary>Occurs when the shared recognizer finalizes an asynchronous recognition operation for emulated input.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.Enabled">
      <summary>Gets or sets a value that indicates whether this <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> object is ready to process speech.</summary>
      <returns>true if this <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> object is performing speech recognition; otherwise, false.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.Grammars">
      <summary>Gets a collection of the <see cref="T:System.Speech.Recognition.Grammar" /> objects that are loaded in this <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> instance.</summary>
      <returns>A collection of the <see cref="T:System.Speech.Recognition.Grammar" /> objects that the application loaded into the current instance of the shared recognizer.</returns>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.LoadGrammar(System.Speech.Recognition.Grammar)">
      <summary>Loads a speech recognition grammar.</summary>
      <param name="grammar">The speech recognition grammar to load.</param>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.LoadGrammarAsync(System.Speech.Recognition.Grammar)">
      <summary>Asynchronously loads a speech recognition grammar.</summary>
      <param name="grammar">The speech recognition grammar to load.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.LoadGrammarCompleted">
      <summary>Occurs when the recognizer finishes the asynchronous loading of a speech recognition grammar.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.MaxAlternates">
      <summary>Gets or sets the maximum number of alternate recognition results that the shared recognizer returns for each recognition operation.</summary>
      <returns>The maximum number of alternate results that the speech recognizer returns for each recognition operation.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.PauseRecognizerOnRecognition">
      <summary>Gets or sets a value that indicates whether the shared recognizer pauses recognition operations while an application is handling a <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechRecognized" /> event.</summary>
      <returns>true if the shared recognizer waits to process input while any application is handling the <see cref="E:System.Speech.Recognition.SpeechRecognitionEngine.SpeechRecognized" /> event; otherwise, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.RecognizerAudioPosition">
      <summary>Gets the current location of the recognizer in the audio input that it is processing.</summary>
      <returns>The position of the recognizer in the audio input that it is processing.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.RecognizerInfo">
      <summary>Gets information about the shared speech recognizer.</summary>
      <returns>Information about the shared speech recognizer.</returns>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.RecognizerUpdateReached">
      <summary>Occurs when the recognizer pauses to synchronize recognition and other operations.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.RequestRecognizerUpdate">
      <summary>Requests that the shared recognizer pause and update its state.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.RequestRecognizerUpdate(System.Object)">
      <summary>Requests that the shared recognizer pause and update its state and provides a user token for the associated event.</summary>
      <param name="userToken">User-defined information that contains information for the operation.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.RequestRecognizerUpdate(System.Object,System.TimeSpan)">
      <summary>Requests that the shared recognizer pause and update its state and provides an offset and a user token for the associated event.</summary>
      <param name="userToken">User-defined information that contains information for the operation.</param>
      <param name="audioPositionAheadToRaiseUpdate">The offset from the current <see cref="P:System.Speech.Recognition.SpeechRecognizer.AudioPosition" /> to delay the request.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.SpeechDetected">
      <summary>Occurs when the recognizer detects input that it can identify as speech.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.SpeechHypothesized">
      <summary>Occurs when the recognizer has recognized a word or words that may be a component of multiple complete phrases in a grammar.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.SpeechRecognitionRejected">
      <summary>Occurs when the recognizer receives input that does not match any of the speech recognition grammars it has loaded.</summary>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.SpeechRecognized">
      <summary>Occurs when the recognizer receives input that matches one of its speech recognition grammars.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SpeechRecognizer.State">
      <summary>Gets the state of a <see cref="T:System.Speech.Recognition.SpeechRecognizer" /> object.</summary>
      <returns>The state of the SpeechRecognizer object.</returns>
    </member>
    <member name="E:System.Speech.Recognition.SpeechRecognizer.StateChanged">
      <summary>Occurs when the running state of the Windows Desktop Speech Technology recognition engine changes.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.UnloadAllGrammars">
      <summary>Unloads all speech recognition grammars from the shared recognizer.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechRecognizer.UnloadGrammar(System.Speech.Recognition.Grammar)">
      <summary>Unloads a specified speech recognition grammar from the shared recognizer.</summary>
      <param name="grammar">The grammar to unload.</param>
    </member>
    <member name="T:System.Speech.Recognition.SpeechUI">
      <summary>Provides text and status information on recognition operations to be displayed in the Speech platform user interface.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SpeechUI.SendTextFeedback(System.Speech.Recognition.RecognitionResult,System.String,System.Boolean)">
      <summary>Sends status and descriptive text to the Speech platform user interface about the status of a recognition operation.</summary>
      <returns>true if the information provided to the method (<paramref name="Feedback" />, and <paramref name="isSuccessfulAction" />) was successfully made available to the Speech platform user interface, and false if the operation failed.</returns>
      <param name="result">A valid <see cref="T:System.Speech.Recognition.RecognitionResult" /> instance.</param>
      <param name="feedback">A <see cref="T:System.String" /> containing a comment about the recognition operation that produced the <see cref="T:System.Speech.Recognition.RecognitionResult" /><paramref name="result" />.</param>
      <param name="isSuccessfulAction">A bool indicating whether the application deemed the recognition operation a success.</param>
    </member>
    <member name="T:System.Speech.Recognition.StateChangedEventArgs">
      <summary>Returns data from the <see cref="E:System.Speech.Recognition.SpeechRecognizer.StateChanged" /> event.</summary>
    </member>
    <member name="P:System.Speech.Recognition.StateChangedEventArgs.RecognizerState">
      <summary>Gets the current state of the shared speech recognition engine in Windows.</summary>
      <returns>A <see cref="T:System.Speech.Recognition.RecognizerState" /> instance that indicates whether the state of a shared speech recognition engine is Listening or Stopped.</returns>
    </member>
    <member name="T:System.Speech.Recognition.SubsetMatchingMode">
      <summary>Enumerates values of subset matching mode.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SubsetMatchingMode.Subsequence">
      <summary>Indicates that subset matching mode is Subsequence.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SubsetMatchingMode.OrderedSubset">
      <summary>Indicates that subset matching mode is OrderedSubset.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SubsetMatchingMode.SubsequenceContentRequired">
      <summary>Indicates that subset matching mode is SubsequenceContentRequired.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SubsetMatchingMode.OrderedSubsetContentRequired">
      <summary>Indicates that subset matching mode is OrderedSubsetContentRequired.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument">
      <summary>Defines a design-time object that is used to build strongly-typed runtime grammars that conform to the Speech Recognition Grammar Specification (SRGS) Version 1.0.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsDocument.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsDocument.#ctor(System.Speech.Recognition.GrammarBuilder)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> class from a <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object.</summary>
      <param name="builder">The <see cref="T:System.Speech.Recognition.GrammarBuilder" /> object used to create the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="builder" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsDocument.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsRule)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> class and specifies an <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> object to be the root rule of the grammar.</summary>
      <param name="grammarRootRule">The root rule in the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="grammarRootRule" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsDocument.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> class specifying the location of the XML document that is used to fill in the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> instance.</summary>
      <param name="path">The location of the SRGS XML file.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsDocument.#ctor(System.Xml.XmlReader)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> class from an instance of <see cref="T:System.Xml.XmlReader" /> that references an XML-format grammar file.</summary>
      <param name="srgsGrammar">The <see cref="T:System.Xml.XmlReader" /> object that was created with the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> XML instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="srgsGrammar" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.AssemblyReferences">
      <summary>Gets the assembly reference information for the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> instance.</summary>
      <returns>The <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.AssemblyReferences" /> property returns a string collection containing a list of the assembly references.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.CodeBehind">
      <summary>Gets the code-behind information for the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> instance.</summary>
      <returns>The <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.CodeBehind" /> property returns a string collection that contains a list of the code-behind documents.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Culture">
      <summary>Gets or sets the culture information for the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> instance.</summary>
      <returns>A <see cref="T:System.Globalization.CultureInfo" /> object that contains the current culture information for <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" />.</returns>
      <exception cref="T:System.ArgumentNullException">The value being assigned to <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Culture" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The value being assigned to <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Culture" /> is <see cref="P:System.Globalization.CultureInfo.InvariantCulture" />.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Debug">
      <summary>Gets or sets whether line numbers should be added to inline scripts.</summary>
      <returns>The <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Debug" /> property returns true if line numbers should be added for debugging purposes; otherwise the property returns false.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.ImportNamespaces">
      <summary>Gets the related namespaces for the current <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> instance.</summary>
      <returns>The <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.ImportNamespaces" /> property returns a string collection that contains a list of the related namespaces in the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> instance.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Language">
      <summary>Gets or sets the programming language used for inline code in the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> class.</summary>
      <returns>The <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Language" /> property returns the programming language to which <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> is currently set.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Mode">
      <summary>Gets or sets the mode for the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> class.</summary>
      <returns>The recognition mode of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" />.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Namespace">
      <summary>Gets or sets the namespace of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> class.</summary>
      <returns>The <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Namespace" /> property returns the namespace for the current <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" />.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.PhoneticAlphabet">
      <summary>Gets or sets the phonetic alphabet of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> class.</summary>
      <returns>Returns the phonetic alphabet that must be used to specify custom pronunciations in the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsToken" /> object. </returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Root">
      <summary>Gets or sets the root rule of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> class.</summary>
      <returns>Returns the rule that is designated as the root rule of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" />.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Rules">
      <summary>Gets the collection of rules that are currently defined for the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> class.</summary>
      <returns>Returns the rules defined for the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> object.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Script">
      <summary>Gets or sets the .NET scripting language for the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> class.</summary>
      <returns>The Script property returns the current .NET scripting language for the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> class.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt is made to set the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Script" /> property to null.</exception>
      <exception cref="T:System.ArgumentException">An attempt is made to set the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.Script" /> property to an empty string.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsDocument.WriteSrgs(System.Xml.XmlWriter)">
      <summary>Writes the contents of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> object to an XML-format grammar file that conforms to the Speech Recognition Grammar Specification (SRGS) Version 1.0.</summary>
      <param name="srgsGrammar">The <see cref="T:System.Xml.XmlWriter" /> that is used to write the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="srgsGrammar" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.XmlBase">
      <summary>Gets or sets the base URI of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> class.</summary>
      <returns>The current base URI of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" />.</returns>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsElement">
      <summary>Defines the base class for classes in the <see cref="N:System.Speech.Recognition.SrgsGrammar" /> namespace that correspond to the elements in an SRGS grammar.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsElement" /> class.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsGrammarCompiler">
      <summary>Compiles <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> and XML-format grammar files into binary grammar files with the .cfg extension and sends the output to a stream.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsGrammarCompiler.Compile(System.Speech.Recognition.SrgsGrammar.SrgsDocument,System.IO.Stream)">
      <summary>Compiles an <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" />object into a binary grammar file with the .cfg extension and sends the output to a stream.</summary>
      <param name="srgsGrammar">The grammar to compile.</param>
      <param name="outputStream">The stream that receives the results of compilation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="srgsGrammar" /> is null.<paramref name="outputStream" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsGrammarCompiler.Compile(System.String,System.IO.Stream)">
      <summary>Compiles an XML-format grammar file into a binary grammar file with the .cfg extension and sends the output to a stream.</summary>
      <param name="inputPath">The path of the file to compile.</param>
      <param name="outputStream">The stream that receives the results of compilation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputPath" /> is null.<paramref name="outputStream" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputPath" /> is an empty string.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsGrammarCompiler.Compile(System.Xml.XmlReader,System.IO.Stream)">
      <summary>Compiles data for an XML-format grammar file referenced by an <see cref="T:System.Xml.XmlReader" /> into a binary grammar file with the .cfg extension and sends the output to a stream.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> that reads the grammar. The grammar can reside in a physical file or in memory.</param>
      <param name="outputStream">The stream that will receive the results of compilation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null.<paramref name="outputStream" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsGrammarCompiler.CompileClassLibrary(System.Speech.Recognition.SrgsGrammar.SrgsDocument,System.String,System.String[],System.String)">
      <summary>Compiles an SRGS document into a DLL.</summary>
      <param name="srgsGrammar">The <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> that contains the grammar to compile.</param>
      <param name="outputPath">The path of the output DLL.</param>
      <param name="referencedAssemblies">A list of the assemblies referenced from the input grammars.</param>
      <param name="keyFile">The name of the file that contains a pair of keys, thereby enabling the output DLL to be signed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="srgsGrammar" /> is null.<paramref name="outputPath" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="outputPath" /> is an empty string.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsGrammarCompiler.CompileClassLibrary(System.String[],System.String,System.String[],System.String)">
      <summary>Compiles multiple SRGS grammars into a DLL.</summary>
      <param name="inputPaths">A list of the grammars to compile.</param>
      <param name="outputPath">The path of the output DLL.</param>
      <param name="referencedAssemblies">A list of the assemblies referenced from the input grammars.</param>
      <param name="keyFile">The name of the file that contains a pair of keys, thereby enabling the output DLL to be signed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputPaths" /> is null.<paramref name="outputPath" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="outputPath" /> is an empty string.Any element of the <paramref name="inputPaths" /> array is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsGrammarCompiler.CompileClassLibrary(System.Xml.XmlReader,System.String,System.String[],System.String)">
      <summary>Compiles an SRGS document into a DLL.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> that reads the grammar.</param>
      <param name="outputPath">The path of the output DLL.</param>
      <param name="referencedAssemblies">A list of the assemblies referenced from the input grammars.</param>
      <param name="keyFile">The name of the file that contains a pair of keys, thereby enabling the output DLL to be signed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null.<paramref name="outputPath" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="outputPath" /> is an empty string.</exception>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsGrammarMode">
      <summary>Indicates the type of input that the grammar, defined by the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" />, will match. </summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsGrammarMode.Voice">
      <summary>The <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> object will match speech input.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsGrammarMode.Dtmf">
      <summary>The <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsDocument" /> object will match DTMF tones similar to those found on a telephone, instead of speech. </summary>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsItem">
      <summary>Represents a grammar element that contains phrases or other entities that a user can speak to produce a successful recognition.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> class and specifies the number of times that its contents must be spoken.</summary>
      <param name="repeatCount">The number of times that the item must be spoken.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="repeatCount" /> is negative or is larger than 255.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.#ctor(System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> class and specifies minimum and maximum repetition counts.</summary>
      <param name="min">The minimum number of times that the text in the item must be repeated.</param>
      <param name="max">The maximum number of times that the text in the item can be repeated.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="min" /> is negative or larger than 255.<paramref name="max" /> is negative or larger than 255.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="min" /> is larger than <paramref name="max" />.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.#ctor(System.Int32,System.Int32,System.Speech.Recognition.SrgsGrammar.SrgsElement[])">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> class, specifies an array of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsElement" /> objects to add to this instance, and sets minimum and maximum repetition counts.</summary>
      <param name="min">The minimum number of times that the contents of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> object must be repeated.</param>
      <param name="max">The maximum number of times that the contents of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> object can be repeated.</param>
      <param name="elements">The array of objects to add to the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="elements" /> is null.</exception>
      <exception cref="T:System.ArgumentException">Any member of the <paramref name="elements" /> array is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.#ctor(System.Int32,System.Int32,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> class, specifies the text associated with the item, and specifies minimum and maximum repetition counts.</summary>
      <param name="min">The minimum number of times that the item must be repeated.</param>
      <param name="max">The maximum number of times that the item can be repeated.</param>
      <param name="text">The text associated with the item.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="min" /> is negative or larger than 255.<paramref name="max" /> is negative or larger than 255.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="min" /> is larger than <paramref name="max" />.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsElement[])">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> class and specifies an array of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsElement" /> objects to add to this instance.</summary>
      <param name="elements">The array of objects to add to the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="elements" /> is null.</exception>
      <exception cref="T:System.ArgumentException">Any member of the <paramref name="elements" /> array is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> class and specifies its textual contents.</summary>
      <param name="text">The text associated with the item.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="text" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="text" /> is an empty string.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.Add(System.Speech.Recognition.SrgsGrammar.SrgsElement)">
      <summary>Adds an object to the collection of objects contained in this <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> instance.</summary>
      <param name="element">The object to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsItem.Elements">
      <summary>Gets the collection of objects contained by the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> instance.</summary>
      <returns>The collection of objects contained by the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> instance.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsItem.MaxRepeat">
      <summary>Gets the maximum number of times that a user can speak the contents of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" />.</summary>
      <returns>The maximum number of times that a user can speak the contents of the item.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsItem.MinRepeat">
      <summary>Gets the minimum number of times that a user must speak the contents of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" />.</summary>
      <returns>The minimum number of times that a user can speak the contents of the item.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsItem.RepeatProbability">
      <summary>Gets or sets the probability that a user will repeat the contents of this <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> instance.</summary>
      <returns>The probability, as a floating point value, that the contents of this item will be repeatedly spoken.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">An attempt is made to set <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsItem.RepeatProbability" /> to a value that is negative or larger than 1.0.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.SetRepeat(System.Int32)">
      <summary>Sets the number of times that the contents of an <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> must be spoken.</summary>
      <param name="count">The number of times that the item must be spoken.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is less than 0 or greater than 255.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsItem.SetRepeat(System.Int32,System.Int32)">
      <summary>Sets the minimum number of times and the maximum number of times that an item can be spoken.</summary>
      <param name="minRepeat">The minimum number of times that the item must be spoken.</param>
      <param name="maxRepeat">The maximum number of times that the item can be spoken.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="minRepeat" /> is less than zero or larger than 255.<paramref name="maxRepeat" /> is less than zero or larger than 255.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="minRepeat" /> is larger than <paramref name="maxRepeat" />.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsItem.Weight">
      <summary>Gets or sets a multiplying factor that adjusts the likelihood that an <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> in a <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsOneOf" /> object will be spoken.</summary>
      <returns>A floating point value that adjusts the likelihood of this item being spoken.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">An attempt is made to set <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsItem.Weight" /> to a negative value.</exception>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag">
      <summary>Represents an element for associating a semantic value with a phrase in a grammar.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.#ctor(System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag" /> class, specifying a value for the instance.</summary>
      <param name="value">The value used to set the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.Value" /> property.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag" /> class, specifying a name and a value for the instance.</summary>
      <param name="name">The string used to set the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.Name" /> property on the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag" /> object.</param>
      <param name="value">The object used to set the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.Value" /> property on the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null.<paramref name="name" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is an empty string.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.Name">
      <summary>Gets or sets the name of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag" /> instance.</summary>
      <returns>A string that contains the name of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt is made to set <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.Name" /> to null.</exception>
      <exception cref="T:System.ArgumentException">An attempt is made to set <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.Name" /> to an empty string.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.Value">
      <summary>Gets or sets the value contained in the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag" /> instance.</summary>
      <returns>The value contained in the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag" /> instance.</returns>
      <exception cref="T:System.Exception.ArgumentNullException">An attempt is made to set <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.Value" /> to null.</exception>
      <exception cref="T:System.ArgumentException">An attempt is made to set <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsNameValueTag.Value" /> to an invalid type. </exception>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsOneOf">
      <summary>Represents a list of alternative words or phrases, any one of which may be used to match speech input.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsOneOf.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsOneOf" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsOneOf.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsItem[])">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsOneOf" /> class from an array of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> objects.</summary>
      <param name="items">The alternative items to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="items" /> is null.Any element in the <paramref name="items" /> array is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsOneOf.#ctor(System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsOneOf" /> class from an array of <see cref="T:System.String" /> objects. </summary>
      <param name="items">The alternative items to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="items" /> is null.Any element in the <paramref name="items" /> array is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsOneOf.Add(System.Speech.Recognition.SrgsGrammar.SrgsItem)">
      <summary>Adds an <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsItem" /> containing a word or a phrase to the list of alternatives.</summary>
      <param name="item">The item to add to the list of alternatives.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="item" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsOneOf.Items">
      <summary>Gets the list of all the alternatives contained in the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsOneOf" /> element.</summary>
      <returns>
Returns the list of alternatives.
</returns>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsPhoneticAlphabet">
      <summary>Enumerates the supported phonetic alphabets.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsPhoneticAlphabet.Sapi">
      <summary>Speech API phoneme set.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsPhoneticAlphabet.Ipa">
      <summary>International Phonetic Alphabet phoneme set.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsPhoneticAlphabet.Ups">
      <summary>Universal Phone Set phoneme set, which is ASCII encoding of phonemes for IPA.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsRule">
      <summary>Represents a grammar rule. </summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRule.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> class and specifies the identifier for the rule.</summary>
      <param name="id">The identifier of the rule. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="id" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="id" /> is empty.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="id" /> is not a proper rule identifier.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRule.#ctor(System.String,System.Speech.Recognition.SrgsGrammar.SrgsElement[])">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> class from an array of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsElement" /> objects.</summary>
      <param name="id">The identifier of the rule. </param>
      <param name="elements">An array of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsElement" /> elements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="id" /> is null.<paramref name="elements" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="id" /> is empty.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="id" /> is not a proper rule identifier.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRule.Add(System.Speech.Recognition.SrgsGrammar.SrgsElement)">
      <summary>Adds an <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsElement" /> to an <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> object.</summary>
      <param name="element">An object that inherits from <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsElement" /> and specifies what can be recognized. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.BaseClass"></member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.Elements">
      <summary>Gets the collection of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsElement" /> objects in the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> instance.</summary>
      <returns>The collection of elements in the rule.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.Id">
      <summary>Gets or sets the identifier for the rule.</summary>
      <returns>The identifier for the rule.</returns>
      <exception cref="T:System.FormatException">An attempt is made to set <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.Id" /> to an invalid value.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnError"></member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnInit"></member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnParse"></member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.OnRecognition"></member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.Scope">
      <summary>Gets or sets whether a rule can be activated for recognition and when the rule can be referenced by other rules.</summary>
      <returns>A value the sets the scope for the rule.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRule.Script"></member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef">
      <summary>Represents the grammar element that specifies a reference to a rule.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsRule)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> class and specifies the rule to reference.</summary>
      <param name="rule">The object to reference.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rule" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsRule,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> class, specifying the rule to reference and a string that contains a semantic key.</summary>
      <param name="rule">The object to reference.</param>
      <param name="semanticKey">The semantic key.</param>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.#ctor(System.Speech.Recognition.SrgsGrammar.SrgsRule,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> class, specifying the rule to reference, the string alias of the semantic dictionary, and initialization parameters.</summary>
      <param name="rule">The object to reference.</param>
      <param name="semanticKey">The semantic key.</param>
      <param name="parameters">The initialization parameters for a <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> object.</param>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.#ctor(System.Uri)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> class and specifies the location of the external grammar file to reference.</summary>
      <param name="uri">The location of a grammar file outside the containing grammar. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.#ctor(System.Uri,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> class, specifying the location of the external grammar file and the identifier of the rule to reference.</summary>
      <param name="uri">The location of a grammar file outside the containing grammar.</param>
      <param name="rule">The identifier of the rule to reference.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> is null.<paramref name="rule" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="rule" /> is empty.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.#ctor(System.Uri,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> class, specifying the location of the external grammar file, the identifier of the rule, and the string alias of the semantic dictionary.</summary>
      <param name="uri">The location of a grammar file outside the containing grammar.</param>
      <param name="rule">The identifier of the rule to reference.</param>
      <param name="semanticKey">An alias string for the semantic dictionary.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> is null.<paramref name="semanticKey" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="semanticKey" /> is empty.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.#ctor(System.Uri,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> class, specifying the location of the external grammar file, the identifier of the rule, the string alias of the semantic dictionary, and initialization parameters.</summary>
      <param name="uri">The location of a grammar file outside the containing grammar.</param>
      <param name="rule">The identifier of the rule to reference.</param>
      <param name="semanticKey">The semantic key.</param>
      <param name="parameters">The initialization parameters for a <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> object.</param>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.Dictation">
      <summary>Defines a rule that can match spoken input as defined by the dictation topic associated with this grammar. </summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.Garbage">
      <summary>Defines a rule that can match any speech up to the next rule match, the next token, or until the end of spoken input.</summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.MnemonicSpelling"></member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.Null">
      <summary>Defines a rule that is automatically matched in the absence of any audio input.</summary>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.Params">
      <summary>Gets the initialization parameters for a <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> element.</summary>
      <returns>The initialization parameters for a <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> element.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.SemanticKey">
      <summary>Gets an alias string for the semantic dictionary.</summary>
      <returns>An alias string for the semantic dictionary.</returns>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.Uri">
      <summary>Gets the URI for the rule that this <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef" /> element references.</summary>
      <returns>The location of the rule to reference.</returns>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsRuleRef.Void">
      <summary>Defines a rule that can never be spoken. Inserting VOID into a sequence automatically makes that sequence unspeakable.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsRulesCollection">
      <summary>Represents a collection of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> objects. </summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRulesCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRulesCollection" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsRulesCollection.Add(System.Speech.Recognition.SrgsGrammar.SrgsRule[])">
      <summary>Adds the contents of an array of <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> objects to the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRulesCollection" /> object.</summary>
      <param name="rules">The array of rule objects to add to the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRulesCollection" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rules" /> is null.</exception>
      <exception cref="T:System.ArgumentException">Any <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> object in the <paramref name="rules" /> array is null.</exception>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsRuleScope">
      <summary>Enumerates values for the scope of a <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsRule" /> object. </summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsRuleScope.Public">
      <summary>The rule can be the target of a rule reference from an external grammar, which can use the rule to perform recognition. A public rule can always be activated for recognition. </summary>
    </member>
    <member name="F:System.Speech.Recognition.SrgsGrammar.SrgsRuleScope.Private">
      <summary>The rule cannot be the target of a rule reference from an external grammar unless it is the root rule of its containing grammar.</summary>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsSemanticInterpretationTag">
      <summary>Represents a tag that contains ECMAScript that is run when the rule is matched.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsSemanticInterpretationTag.#ctor">
      <summary>Creates an instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsSemanticInterpretationTag" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsSemanticInterpretationTag.#ctor(System.String)">
      <summary>Creates an instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsSemanticInterpretationTag" /> class, specifying the script contents of the tag.</summary>
      <param name="script">A string that contains the ECMAScript for the tag.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="script" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsSemanticInterpretationTag.Script">
      <summary>Gets or sets the ECMAScript for the tag.</summary>
      <returns>A string that contains the semantic interpretation script for the tag.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt is made to set Script to null.</exception>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsSubset">
      <summary>Defines methods and properties that can be used to match a given string with a spoken phrase. </summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsSubset.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsSubset" /> class, specifying the portion of the phrase to be matched. </summary>
      <param name="text">The portion of the phrase to be matched.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="text" /> is null.</exception>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsSubset.#ctor(System.String,System.Speech.Recognition.SubsetMatchingMode)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsSubset" /> class, specifying the portion to be matched and the mode in which the text should be matched.</summary>
      <param name="text">The portion of the phrase to be matched.</param>
      <param name="matchingMode">The mode in which <paramref name="text" /> should be matched with the spoken phrase.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="text" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="text" /> is empty.<paramref name="text" /> contains only white space characters (that is, ' ', '\t', '\n', '\r').<paramref name="matchingMode" /> is set to a value in the <see cref="T:System.Speech.Recognition.SubsetMatchingMode" /> enumeration.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsSubset.MatchingMode">
      <summary>Gets or sets the matching mode for the subset.</summary>
      <returns>A member of the <see cref="T:System.Speech.Recognition.SubsetMatchingMode" /> enumeration.</returns>
      <exception cref="T:System.ArgumentException">An attempt is made to set <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsSubset.MatchingMode" /> to a value that is not a member of the <see cref="T:System.Speech.Recognition.SubsetMatchingMode" /> enumeration.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsSubset.Text">
      <summary>Gets or sets as string that contains the portion of a spoken phrase to be matched.</summary>
      <returns>A string that contains the portion of a spoken phrase to be matched.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt is made to set <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsSubset.Text" /> to null or to an empty string. </exception>
      <exception cref="T:System.ArgumentException">An attempt is made to set <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsSubset.Text" /> using a string that contains only white space characters (' ', '\t', '\n', '\r').</exception>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsText">
      <summary>Represents the textual content of grammar elements defined by the World Wide Web Consortium (W3C) Speech Recognition Grammar Specification (SRGS) Version 1.0.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsText.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsText" /> class.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsText.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsText" /> class, specifying the text of the instance.</summary>
      <param name="text">The value used to set the <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsText.Text" /> property on the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsText" /> instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="text" /> is null.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsText.Text">
      <summary>Gets or sets the text contained within the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsText" /> class instance.</summary>
      <returns>The text contained within the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsText" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt is made to set <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsText.Text" /> to null.</exception>
    </member>
    <member name="T:System.Speech.Recognition.SrgsGrammar.SrgsToken">
      <summary>Represents a word or short phrase that can be recognized.</summary>
    </member>
    <member name="M:System.Speech.Recognition.SrgsGrammar.SrgsToken.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsToken" /> class and specifies the text to be recognized.</summary>
      <param name="text">The text of the new <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsToken" /> class instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="text" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="text" /> is empty.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsToken.Display">
      <summary>Gets or sets the display form of the text to be spoken.</summary>
      <returns>A representation of the token as it should be displayed.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt is made to set <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsToken.Display" /> to null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An attempt is made to assign an empty string to <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsToken.Display" />.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsToken.Pronunciation">
      <summary>Gets or sets the string that defines the pronunciation for the token.</summary>
      <returns>Returns a string containing phones from the phonetic alphabet specified in <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsDocument.PhoneticAlphabet" />. </returns>
      <exception cref="T:System.ArgumentNullException">An attempt is made to set <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsToken.Pronunciation" /> to null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An attempt is made to assign an empty string to <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsToken.Pronunciation" />.</exception>
    </member>
    <member name="P:System.Speech.Recognition.SrgsGrammar.SrgsToken.Text">
      <summary>Gets or sets the written form of the word that should be spoken. </summary>
      <returns>The text contained within the <see cref="T:System.Speech.Recognition.SrgsGrammar.SrgsToken" /> class instance.</returns>
      <exception cref="T:System.ArgumentNullException">An attempt is made to set <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsToken.Text" /> to null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An attempt is made to assign an empty string to <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsToken.Text" />.</exception>
      <exception cref="T:System.ArgumentException">An attempt is made to assign a string that contains a quotation mark (") to <see cref="P:System.Speech.Recognition.SrgsGrammar.SrgsToken.Text" />. </exception>
    </member>
    <member name="T:System.Speech.Synthesis.BookmarkReachedEventArgs">
      <summary>Returns data from the <see cref="E:System.Speech.Synthesis.SpeechSynthesizer.BookmarkReached" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.BookmarkReachedEventArgs.AudioPosition">
      <summary>Gets the time offset at which the bookmark was reached. </summary>
      <returns>Returns the location in the audio input stream of a synthesis engine when the event was raised.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.BookmarkReachedEventArgs.Bookmark">
      <summary>Gets the name of the bookmark that was reached.</summary>
      <returns>Returns a value for the name of the bookmark. </returns>
    </member>
    <member name="T:System.Speech.Synthesis.FilePrompt">
      <summary>Represents a prompt created from a file.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.FilePrompt.#ctor(System.String,System.Speech.Synthesis.SynthesisMediaType)">
      <summary>Creates a new instance of the <see cref="T:System.Speech.Synthesis.FilePrompt" /> class, specifying the path to the file and its media type.</summary>
      <param name="path">The path of the file containing the prompt content.</param>
      <param name="media">The media type of the file.</param>
    </member>
    <member name="M:System.Speech.Synthesis.FilePrompt.#ctor(System.Uri,System.Speech.Synthesis.SynthesisMediaType)">
      <summary>Creates a new instance of the <see cref="T:System.Speech.Synthesis.FilePrompt" /> class, specifying the location of the file and its media type.</summary>
      <param name="promptFile">The URI of the file containing the prompt content.</param>
      <param name="media">The media type of the file.</param>
    </member>
    <member name="T:System.Speech.Synthesis.InstalledVoice">
      <summary>Contains information about a speech synthesis voice installed in Windows. </summary>
    </member>
    <member name="P:System.Speech.Synthesis.InstalledVoice.Enabled">
      <summary>Gets or sets whether a voice can be used to generate speech.</summary>
      <returns>Returns a bool that represents the enabled state of the voice.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.InstalledVoice.Equals(System.Object)">
      <summary>Determines if a given object is an instance of <see cref="T:System.Speech.Synthesis.InstalledVoice" /> and equal to the current instance of <see cref="T:System.Speech.Synthesis.InstalledVoice" />.</summary>
      <returns>Returns true if the current instance of <see cref="T:System.Speech.Synthesis.InstalledVoice" /> and that obtained from the <paramref name="obj" /> argument are equal, otherwise returns false.</returns>
      <param name="obj">An object that can be cast to an instance of <see cref="T:System.Speech.Synthesis.InstalledVoice" />.</param>
    </member>
    <member name="M:System.Speech.Synthesis.InstalledVoice.GetHashCode">
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.InstalledVoice.VoiceInfo">
      <summary>Gets information about a voice, such as culture, name, gender, and age.</summary>
      <returns>The information about an installed voice.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.PhonemeReachedEventArgs">
      <summary>Returns data from the <see cref="E:System.Speech.Synthesis.SpeechSynthesizer.PhonemeReached" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.PhonemeReachedEventArgs.AudioPosition">
      <summary>Gets the audio position of the phoneme.</summary>
      <returns>A TimeSpan object indicating the audio position.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.PhonemeReachedEventArgs.Duration">
      <summary>Gets the duration of the phoneme.</summary>
      <returns>A TimeSpan object indicating the duration.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.PhonemeReachedEventArgs.Emphasis">
      <summary>Gets the emphasis of the phoneme.</summary>
      <returns>A SynthesizerEmphasis member indicating the level of emphasis.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.PhonemeReachedEventArgs.NextPhoneme">
      <summary>Gets the phoneme following the phoneme associated with the <see cref="E:System.Speech.Synthesis.SpeechSynthesizer.PhonemeReached" /> event.</summary>
      <returns>A string containing the next phoneme.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.PhonemeReachedEventArgs.Phoneme">
      <summary>The phoneme associated with the <see cref="E:System.Speech.Synthesis.SpeechSynthesizer.PhonemeReached" /> event.</summary>
      <returns>A string containing the phoneme.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.Prompt">
      <summary>Represents information about what can be rendered, either text or an audio file, by the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" />.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.Prompt.#ctor(System.Speech.Synthesis.PromptBuilder)">
      <summary>Creates a new instance of the <see cref="T:System.Speech.Synthesis.Prompt" /> class from a <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <param name="promptBuilder">The content to be spoken.</param>
    </member>
    <member name="M:System.Speech.Synthesis.Prompt.#ctor(System.String)">
      <summary>Creates a new instance of the <see cref="T:System.Speech.Synthesis.Prompt" /> class and specifies the text to be spoken.</summary>
      <param name="textToSpeak">The text to be spoken.</param>
    </member>
    <member name="M:System.Speech.Synthesis.Prompt.#ctor(System.String,System.Speech.Synthesis.SynthesisTextFormat)">
      <summary>Creates a new instance of the <see cref="T:System.Speech.Synthesis.Prompt" /> class and specifies the text to be spoken and whether its format is plain text or markup language.</summary>
      <param name="textToSpeak">The text to be spoken.</param>
      <param name="media">A value that specifies the format of the text.</param>
    </member>
    <member name="P:System.Speech.Synthesis.Prompt.IsCompleted">
      <summary>Gets whether the <see cref="T:System.Speech.Synthesis.Prompt" /> has finished playing.</summary>
      <returns>Returns false if the prompt has completed; otherwise true.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.PromptBreak">
      <summary>Enumerates values for intervals of prosodic separation (breaks) between word boundaries.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.PromptBreak.None">
      <summary>Indicates no break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptBreak.ExtraSmall">
      <summary>Indicates an extra-small break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptBreak.Small">
      <summary>Indicates a small break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptBreak.Medium">
      <summary>Indicates a medium break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptBreak.Large">
      <summary>Indicates a large break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptBreak.ExtraLarge">
      <summary>Indicates an extra-large break.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.PromptBuilder">
      <summary>Creates an empty <see cref="T:System.Speech.Synthesis.Prompt" /> object and provides methods for adding content, selecting voices, controlling voice attributes, and controlling the pronunciation of spoken words.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> class.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.#ctor(System.Globalization.CultureInfo)">
      <summary>Creates a new instance of the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> class and specifies a culture.</summary>
      <param name="culture">Provides information about a specific culture, such as its language, the name of the culture, the writing system, the calendar used, and how to format dates and sort strings.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendAudio(System.String)">
      <summary>Appends the specified audio file to the <see cref="T:System.Speech.Synthesis.PromptBuilder" />.</summary>
      <param name="path">A fully qualified path to the audio file.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendAudio(System.Uri)">
      <summary>Appends the audio file at the specified URI to the <see cref="T:System.Speech.Synthesis.PromptBuilder" />.</summary>
      <param name="audioFile">URI for the audio file.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendAudio(System.Uri,System.String)">
      <summary>Appends the specified audio file and alternate text to the <see cref="T:System.Speech.Synthesis.PromptBuilder" />.</summary>
      <param name="audioFile">URI for the audio file.</param>
      <param name="alternateText">A string containing alternate text representing the audio.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendBookmark(System.String)">
      <summary>Appends a bookmark to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <param name="bookmarkName">A string containing the name of the appended bookmark.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendBreak">
      <summary>Appends a break to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendBreak(System.Speech.Synthesis.PromptBreak)">
      <summary>Appends a break to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object and specifies its strength (duration).</summary>
      <param name="strength">Indicates the duration of the break, with the following increasing values:</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendBreak(System.TimeSpan)">
      <summary>Appends a break of the specified duration to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <param name="duration">The time in ticks, where one tick equals 100 nanoseconds.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendPromptBuilder(System.Speech.Synthesis.PromptBuilder)">
      <summary>Appends a <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object to another <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <param name="promptBuilder">The content to append.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendSsml(System.String)">
      <summary>Appends the SSML file at the specified path to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <param name="path">A fully qualified path to the SSML file to append.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendSsml(System.Uri)">
      <summary>Appends the SSML file at the specified URI to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <param name="ssmlFile">A fully qualified URI to the SSML file to append.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendSsml(System.Xml.XmlReader)">
      <summary>Appends the specified XML file containing SSML to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <param name="ssmlFile">A fully qualified name to the XML file to append.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendSsmlMarkup(System.String)">
      <summary>Appends the specified string containing SSML markup to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <param name="ssmlMarkup">A string containing SSML markup.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendText(System.String)">
      <summary>Specifies text to append to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <param name="textToSpeak">A string containing the text to be spoken.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendText(System.String,System.Speech.Synthesis.PromptEmphasis)">
      <summary>Appends text to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object and specifies the degree of emphasis for the text.</summary>
      <param name="textToSpeak">A string containing the text to be spoken.</param>
      <param name="emphasis">The value for the emphasis or stress to apply to the text.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendText(System.String,System.Speech.Synthesis.PromptRate)">
      <summary>Appends text to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object and specifies the speaking rate for the text.</summary>
      <param name="textToSpeak">A string containing the text to be spoken.</param>
      <param name="rate">The value for the speaking rate to apply to the text.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendText(System.String,System.Speech.Synthesis.PromptVolume)">
      <summary>Appends text to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object and specifies the volume to speak the text.</summary>
      <param name="textToSpeak">A string containing the text to be spoken.</param>
      <param name="volume">The value for the speaking volume (loudness) to apply to the text. </param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendTextWithAlias(System.String,System.String)">
      <summary>Appends text to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object and specifies the alias text to be spoken in place of the appended text.</summary>
      <param name="textToSpeak">A string containing the text representation.</param>
      <param name="substitute">A string containing the text to be spoken.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendTextWithHint(System.String,System.Speech.Synthesis.SayAs)">
      <summary>Appends text to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object and specifies the content type using a member of the <see cref="T:System.Speech.Synthesis.SayAs" /> enumeration.</summary>
      <param name="textToSpeak">A string containing the text to be spoken.</param>
      <param name="sayAs">The content type of the text.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendTextWithHint(System.String,System.String)">
      <summary>Appends text to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object and a <see cref="T:System.String" /> that specifies the content type of the text.</summary>
      <param name="textToSpeak">A string containing the text to be spoken.</param>
      <param name="sayAs">The content type of the text.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.AppendTextWithPronunciation(System.String,System.String)">
      <summary>Appends text to the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object and specifies the pronunciation for the text. </summary>
      <param name="textToSpeak">A string containing the written form of the word using the conventional alphabet for a language. </param>
      <param name="pronunciation">A string containing phones to be spoken from the International Phonetic Alphabet (IPA). </param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.ClearContent">
      <summary>Clears the content from the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.PromptBuilder.Culture">
      <summary>Gets or sets the culture information for the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.EndParagraph">
      <summary>Specifies the end of a paragraph in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.EndSentence">
      <summary>Specifies the end of a sentence in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.EndStyle">
      <summary>Specifies the end of a style in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.EndVoice">
      <summary>Specifies the end of use of a voice in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.PromptBuilder.IsEmpty">
      <summary>Gets whether the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> is empty.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartParagraph">
      <summary>Specifies the start of a paragraph in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartParagraph(System.Globalization.CultureInfo)">
      <summary>Specifies the start of a paragraph in the specified culture in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <param name="culture">Provides information about a specific culture, such as the language, the name of the culture, the writing system, the calendar used, and how to format dates and sort strings.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartSentence">
      <summary>Specifies the start of a sentence in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartSentence(System.Globalization.CultureInfo)">
      <summary>Specifies the start of a sentence in the specified culture in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <param name="culture">Provides information about a specific culture, such as the language, the name of the culture, the writing system, the calendar used, and how to format dates and sort strings.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartStyle(System.Speech.Synthesis.PromptStyle)">
      <summary>Specifies the start of a style in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <param name="style">The style to start.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartVoice(System.Globalization.CultureInfo)">
      <summary>Instructs the synthesizer to change the voice in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object and specifies the culture of the voice to use.</summary>
      <param name="culture">Provides information about a specific culture, such as the language, the name of the culture, the writing system, the calendar used, and how to format dates and sort strings.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartVoice(System.Speech.Synthesis.VoiceGender)">
      <summary>Instructs the synthesizer to change the voice in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object and specifies the gender of the voice to use.</summary>
      <param name="gender">The gender of the voice to use.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartVoice(System.Speech.Synthesis.VoiceGender,System.Speech.Synthesis.VoiceAge)">
      <summary>Instructs the synthesizer to change the voice in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object and specifies the gender and the age of the new voice.</summary>
      <param name="gender">The gender of the new voice to use.</param>
      <param name="age">The age of the voice to use.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartVoice(System.Speech.Synthesis.VoiceGender,System.Speech.Synthesis.VoiceAge,System.Int32)">
      <summary>Instructs the synthesizer to change the voice in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object and specifies its gender, age, and a preferred voice that matches the specified gender and age.</summary>
      <param name="gender">The gender of the voice to use.</param>
      <param name="age">The age of the voice to use.</param>
      <param name="voiceAlternate">An integer that specifies a preferred voice when more than one voice matches the <paramref name="gender" /> and <paramref name="age" /> parameters.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartVoice(System.Speech.Synthesis.VoiceInfo)">
      <summary>Instructs the synthesizer to change the voice in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object and specifies criteria for the new voice.</summary>
      <param name="voice">The criteria for the voice to use.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.StartVoice(System.String)">
      <summary>Instructs the synthesizer to change the voice in the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object and specifies the name of the voice to use.</summary>
      <param name="name">The name of the voice to use.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptBuilder.ToXml">
      <summary>Returns the SSML generated from the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <returns>Returns the SSML generated from the <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object as a single line.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.PromptEmphasis">
      <summary>Enumerates values for levels of emphasis in prompts.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.PromptEmphasis.NotSet">
      <summary>Indicates that no emphasis value is specified.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptEmphasis.Strong">
      <summary>Indicates a strong level of emphasis.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptEmphasis.Moderate">
      <summary>Indicates a moderate level of emphasis.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptEmphasis.None">
      <summary>Indicates no emphasis.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptEmphasis.Reduced">
      <summary>Indicates a reduced level of emphasis.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.PromptEventArgs">
      <summary>Represents the base class for EventArgs classes in the <see cref="N:System.Speech.Synthesis" /> namespace.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.PromptEventArgs.Prompt">
      <summary>Gets the prompt associated with the event.</summary>
      <returns>The Prompt object associated with the event.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.PromptRate">
      <summary>Enumerates values for the speaking rate of prompts.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.PromptRate.NotSet">
      <summary>Indicates no rate is specified.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptRate.ExtraFast">
      <summary>Indicates an extra-fast rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptRate.Fast">
      <summary>Indicates a fast rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptRate.Medium">
      <summary>Indicates a medium rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptRate.Slow">
      <summary>Indicates a slow rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptRate.ExtraSlow">
      <summary>Indicates an extra-slow rate.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.PromptStyle">
      <summary>Defines a style for speaking prompts that consists of settings for emphasis, rate, and volume.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.PromptStyle.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Synthesis.PromptStyle" /> class.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.PromptStyle.#ctor(System.Speech.Synthesis.PromptEmphasis)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Synthesis.PromptStyle" /> class and specifies the setting for the emphasis of the style.</summary>
      <param name="emphasis">The setting for the emphasis of the style.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptStyle.#ctor(System.Speech.Synthesis.PromptRate)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Synthesis.PromptStyle" /> class and specifies the setting for the speaking rate of the style.</summary>
      <param name="rate">The setting for the speaking rate of the style.</param>
    </member>
    <member name="M:System.Speech.Synthesis.PromptStyle.#ctor(System.Speech.Synthesis.PromptVolume)">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Synthesis.PromptStyle" /> class and specifies the setting for the speaking volume of the style.</summary>
      <param name="volume">The setting for the volume (loudness) of the style.</param>
    </member>
    <member name="P:System.Speech.Synthesis.PromptStyle.Emphasis">
      <summary>Gets or sets the setting for the emphasis of the style.</summary>
      <returns>Returns the setting for the emphasis of the style.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.PromptStyle.Rate">
      <summary>Gets or sets the setting for the speaking rate of the style.</summary>
      <returns>Returns the setting for the speaking rate of the style.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.PromptStyle.Volume">
      <summary>Gets or sets the setting for the volume (loudness) of the style.</summary>
      <returns>Returns the setting for the volume (loudness) of the style.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.PromptVolume">
      <summary>Enumerates values for volume levels (loudness) in prompts.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.NotSet">
      <summary>Indicates that the volume level is not set.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.Silent">
      <summary>Indicates a muted volume level.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.ExtraSoft">
      <summary>Indicates an extra soft volume level.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.Soft">
      <summary>Indicates a soft volume level.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.Medium">
      <summary>Indicates a medium volume level.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.Loud">
      <summary>Indicates a loud volume level.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.ExtraLoud">
      <summary>Indicates an extra loud volume level.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.PromptVolume.Default">
      <summary>Indicates the engine-specific default volume level.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.SayAs">
      <summary>Enumerates the content types for the speaking of elements such as times, dates, and currency.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.SpellOut">
      <summary>Spell the word or phrase. For example, say “clock” as “C L O C K”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.NumberOrdinal">
      <summary>Speak a number as an ordinal number. For example, speak "3rd" as "third".</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.NumberCardinal">
      <summary>Speak a number as a cardinal number. For example, speak "3" as "three".</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Date">
      <summary>Speak a number sequence as a date. For example, speak “05/19/2004” or “19.5.2004” as “may nineteenth two thousand four”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.DayMonthYear">
      <summary>Speak a number sequence as a date including the day, month, and year. For example, speak “12/05/2004” as “May twelfth two thousand four”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.MonthDayYear">
      <summary>Speak a number sequence as a date including the day, month, and year. For example, speak “12/05/2004” as “December fifth two thousand four”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.YearMonthDay">
      <summary>Speak a number sequence as a date including the day, month, and year. For example, speak “2004/05/12” as “May twelfth two thousand four”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.YearMonth">
      <summary>Speak a number sequence as a year and month. For example, speak “2004/05” as “May two thousand four”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.MonthYear">
      <summary>Speak a number sequence as a month and year. For example, speak “05/2004” as “May two thousand four”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.MonthDay">
      <summary>Speak a number sequence as a month and day. For example, speak “05/12” as “may twelfth”, and speak “12/5” as “December 5th”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.DayMonth">
      <summary>Speak a number sequence as a day and month. For example, speak “12/05” as “May twelfth”, and speak “05/12” as “December 5th”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Year">
      <summary>Speak a number as a year. For example, speak “1998” as “nineteen ninety-eight”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Month">
      <summary>Speak a word as a month. For example, speak “June” as “June”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Day">
      <summary>Speak a number as the day in a date. For example, speak “3rd” as “third”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Time">
      <summary>Speak a number sequence as a time. For example, speak “9:45” as “nine forty-five”, and speak “9:45am” as “nine forty-five A M”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Time24">
      <summary>Speak a number sequence as a time using the 24-hour clock. For example, speak “18:00” as “eighteen hundred hours”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Time12">
      <summary>Speak a number sequence as a time using the 12-hour clock. For example, speak “03:25” as “three twenty-five”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Telephone">
      <summary>Speak a number sequence as a U.S. telephone number. For example, speak “(*************” as “Area code three zero six five five five one two one two”.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SayAs.Text">
      <summary>Speak the word or phrase as text. For example, speak “timeline” as “timeline”. </summary>
    </member>
    <member name="T:System.Speech.Synthesis.SpeakCompletedEventArgs">
      <summary>Returns notification from the <see cref="E:System.Speech.Synthesis.SpeechSynthesizer.SpeakCompleted" /> event.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.SpeakProgressEventArgs">
      <summary>Returns data from the <see cref="E:System.Speech.Synthesis.SpeechSynthesizer.SpeakProgress" /> event.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.SpeakProgressEventArgs.AudioPosition">
      <summary>Gets the audio position of the event.</summary>
      <returns>Returns the position of the event in the audio output stream.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.SpeakProgressEventArgs.CharacterCount">
      <summary>Gets the number of characters in the word that was spoken just before the event was raised.</summary>
      <returns>Returns the number of characters in the word that was spoken just before the event was raised.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.SpeakProgressEventArgs.CharacterPosition">
      <summary>Gets the number of characters and spaces from the beginning of the prompt to the position before the first letter of the word that was just spoken.</summary>
      <returns>Returns the number of characters and spaces from the beginning of the prompt to the position before the first letter of the word that was just spoken.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.SpeakProgressEventArgs.Text">
      <summary>The text that was just spoken when the event was raised.</summary>
      <returns>Returns the text that was just spoken when the event was raised.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.SpeakStartedEventArgs">
      <summary>Returns notification from the <see cref="E:System.Speech.Synthesis.SpeechSynthesizer.SpeakStarted" /> event.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.SpeechSynthesizer">
      <summary>Provides access to the functionality of an installed a speech synthesis engine.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> class.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.AddLexicon(System.Uri,System.String)">
      <summary>Adds a lexicon to the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object.</summary>
      <param name="uri">The location of the lexicon information.</param>
      <param name="mediaType">The media type of the lexicon. Media type values are not case sensitive.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.BookmarkReached">
      <summary>Raised when the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> encounters a bookmark in a prompt.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.Dispose">
      <summary>Disposes the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object and releases resources used during the session. </summary>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.GetCurrentlySpokenPrompt">
      <summary>Gets the contents of the prompt that the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> is speaking. </summary>
      <returns>Returns the prompt object that is currently being spoken.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.GetInstalledVoices">
      <summary>Returns all of the installed speech synthesis (text-to-speech) voices.</summary>
      <returns>Returns a read-only collection of the voices currently installed on the system.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.GetInstalledVoices(System.Globalization.CultureInfo)">
      <summary>Returns all of the installed speech synthesis  (text-to-speech) voices that support a specific locale.</summary>
      <returns>Returns a read-only collection of the voices currently installed on the system that support the specified locale.</returns>
      <param name="culture">The locale that the voice must support.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.Pause">
      <summary>Pauses the speaking of a prompt by a <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object. </summary>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.PhonemeReached">
      <summary>Raised when a phoneme is reached.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.SpeechSynthesizer.Rate">
      <summary>Gets or sets the speaking rate of the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object.</summary>
      <returns>Returns the speaking rate of the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object, from -10 through 10.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.RemoveLexicon(System.Uri)">
      <summary>Removes a lexicon from the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object.</summary>
      <param name="uri">The location of the lexicon document.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.Resume">
      <summary>Resumes the speaking of a prompt by the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object after it has been paused.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SelectVoice(System.String)">
      <summary>Selects a specific voice by name.</summary>
      <param name="name">The name of the voice to select.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SelectVoiceByHints(System.Speech.Synthesis.VoiceGender)">
      <summary>Selects a voice with a specific gender.</summary>
      <param name="gender">The gender of the voice to select.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SelectVoiceByHints(System.Speech.Synthesis.VoiceGender,System.Speech.Synthesis.VoiceAge)">
      <summary>Selects a voice with a specific gender and age.</summary>
      <param name="gender">The gender of the voice to select.</param>
      <param name="age">The age of the voice to select.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SelectVoiceByHints(System.Speech.Synthesis.VoiceGender,System.Speech.Synthesis.VoiceAge,System.Int32)">
      <summary>Selects a voice with a specific gender and age, based on the position in which the voices are ordered.</summary>
      <param name="gender">The gender of the voice to select.</param>
      <param name="age">The age of the voice to select.</param>
      <param name="voiceAlternate">The position of the voice to select.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SelectVoiceByHints(System.Speech.Synthesis.VoiceGender,System.Speech.Synthesis.VoiceAge,System.Int32,System.Globalization.CultureInfo)">
      <summary>Selects a voice with a specific gender, age, and locale, based on the position in which the voices are ordered.</summary>
      <param name="gender">The gender of the voice to select.</param>
      <param name="age">The age of the voice to select.</param>
      <param name="voiceAlternate">The position of the voice to select.</param>
      <param name="culture">The locale of the voice to select.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SetOutputToAudioStream(System.IO.Stream,System.Speech.AudioFormat.SpeechAudioFormatInfo)">
      <summary>Configures the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object to append output to an audio stream.</summary>
      <param name="audioDestination">The stream to which to append synthesis output.</param>
      <param name="formatInfo">The format to use for the synthesis output.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SetOutputToDefaultAudioDevice">
      <summary>Configures the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object to send output to the default audio device.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SetOutputToNull">
      <summary>Configures the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object to not send output from synthesis operations to a device, file, or stream.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SetOutputToWaveFile(System.String)">
      <summary>Configures the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object to append output to a file that contains Waveform format audio.</summary>
      <param name="path">The path to the file.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SetOutputToWaveFile(System.String,System.Speech.AudioFormat.SpeechAudioFormatInfo)">
      <summary>Configures the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object to append output to a Waveform audio format file in a specified format.</summary>
      <param name="path">The path to the file.</param>
      <param name="formatInfo">The audio format information.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SetOutputToWaveStream(System.IO.Stream)">
      <summary>Configures the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object to append output to a stream that contains Waveform format audio.</summary>
      <param name="audioDestination">The stream to which to append synthesis output.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.Speak(System.Speech.Synthesis.Prompt)">
      <summary>Synchronously speaks the contents of a <see cref="T:System.Speech.Synthesis.Prompt" /> object.</summary>
      <param name="prompt">The content to speak.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.Speak(System.Speech.Synthesis.PromptBuilder)">
      <summary>Synchronously speaks the contents of a <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <param name="promptBuilder">The content to speak.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.Speak(System.String)">
      <summary>Synchronously speaks the contents of a string.</summary>
      <param name="textToSpeak">The text to speak.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SpeakAsync(System.Speech.Synthesis.Prompt)">
      <summary>Asynchronously speaks the contents of a <see cref="T:System.Speech.Synthesis.Prompt" /> object.</summary>
      <param name="prompt">The content to speak.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SpeakAsync(System.Speech.Synthesis.PromptBuilder)">
      <summary>Asynchronously speaks the contents of a <see cref="T:System.Speech.Synthesis.PromptBuilder" /> object.</summary>
      <returns>Returns the object that contains the content to speak.</returns>
      <param name="promptBuilder">The content to speak.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SpeakAsync(System.String)">
      <summary>Asynchronously speaks the contents of a string.</summary>
      <returns>Returns the object that contains the content to speak.</returns>
      <param name="textToSpeak">The text to speak.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SpeakAsyncCancel(System.Speech.Synthesis.Prompt)">
      <summary>Cancels the asynchronous synthesis operation for a queued prompt.</summary>
      <param name="prompt">The content for which to cancel a speak operation.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SpeakAsyncCancelAll">
      <summary>Cancels all queued, asynchronous, speech synthesis operations.</summary>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.SpeakCompleted">
      <summary>Raised when the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> completes the speaking of a prompt.</summary>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.SpeakProgress">
      <summary>Raised after the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> speaks each individual word of a prompt.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SpeakSsml(System.String)">
      <summary>Synchronously speaks a <see cref="T:System.String" /> that contains SSML markup. </summary>
      <param name="textToSpeak">The SSML string to speak.</param>
    </member>
    <member name="M:System.Speech.Synthesis.SpeechSynthesizer.SpeakSsmlAsync(System.String)">
      <summary>Asynchronously speaks a <see cref="T:System.String" /> that contains SSML markup. </summary>
      <param name="textToSpeak">The SMML markup to speak.</param>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.SpeakStarted">
      <summary>Raised when the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> begins the speaking of a prompt.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.SpeechSynthesizer.State">
      <summary>Gets the speaking state of the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object.</summary>
      <returns>Returns the speaking state of the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object.</returns>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.StateChanged">
      <summary>Raised when the state of the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> changes.</summary>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.VisemeReached">
      <summary>Raised when a viseme is reached.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.SpeechSynthesizer.Voice">
      <summary>Gets information about the current voice of the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object.</summary>
      <returns>Returns information about the current voice of the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object.</returns>
    </member>
    <member name="E:System.Speech.Synthesis.SpeechSynthesizer.VoiceChange">
      <summary>Raised when the voice of the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> changes.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.SpeechSynthesizer.Volume">
      <summary>Get or sets the output volume of the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> object.</summary>
      <returns>Returns the volume of the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" />, from 0 through 100.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Speech.Synthesis.StateChangedEventArgs">
      <summary>Returns data from the <see cref="E:System.Speech.Synthesis.SpeechSynthesizer.StateChanged" /> event.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.StateChangedEventArgs.PreviousState">
      <summary>Gets the state of the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> before the <see cref="E:System.Speech.Synthesis.SpeechSynthesizer.StateChanged" /> event.</summary>
      <returns>Returns the state of the synthesizer before the state changed.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.StateChangedEventArgs.State">
      <summary>Gets the state of the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> before the <see cref="E:System.Speech.Synthesis.SpeechSynthesizer.StateChanged" /> event.</summary>
      <returns>The state of the synthesizer after the state changed.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.SynthesisMediaType">
      <summary>Enumerates the types of media files.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesisMediaType.Text">
      <summary>Indicates that the media type is Text.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesisMediaType.Ssml">
      <summary>Indicates that the media type is SSML.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesisMediaType.WaveAudio">
      <summary>Indicates that the media type is WaveAudio.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.SynthesisTextFormat">
      <summary>Enumerates the types of text formats that may be used to construct a <see cref="T:System.Speech.Synthesis.Prompt" /> object.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesisTextFormat.Text">
      <summary>Indicates that the text format is Text.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesisTextFormat.Ssml">
      <summary>Indicates that the text format is SSML.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.SynthesizerEmphasis">
      <summary>Enumerates levels of synthesizer emphasis.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesizerEmphasis.Stressed">
      <summary>Indicates a high level of synthesizer emphasis.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesizerEmphasis.Emphasized">
      <summary>Indicates a low level of synthesizer emphasis.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.SynthesizerState">
      <summary>Enumerates values for the state of the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" />.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesizerState.Ready">
      <summary>Indicates that the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> is ready to generate speech from a prompt.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesizerState.Speaking">
      <summary>Indicates that the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> is speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.SynthesizerState.Paused">
      <summary>Indicates that the <see cref="T:System.Speech.Synthesis.SpeechSynthesizer" /> is paused.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.VisemeReachedEventArgs">
      <summary>Returns data from the <see cref="E:System.Speech.Synthesis.SpeechSynthesizer.VisemeReached" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.VisemeReachedEventArgs.AudioPosition">
      <summary>Gets the position of the viseme in the audio stream.</summary>
      <returns>A <see cref="T:System.TimeSpan" /> object that represents the position of the viseme.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.VisemeReachedEventArgs.Duration">
      <summary>Gets the duration of the viseme.</summary>
      <returns>A <see cref="T:System.TimeSpan" /> object that represents the duration of the viseme.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.VisemeReachedEventArgs.Emphasis">
      <summary>Gets a <see cref="T:System.Speech.Synthesis.SynthesizerEmphasis" /> object that describes the emphasis of the viseme.</summary>
      <returns>A <see cref="T:System.Speech.Synthesis.SynthesizerEmphasis" /> object that represents the emphasis of the viseme.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.VisemeReachedEventArgs.NextViseme">
      <summary>Gets the value of the next viseme.</summary>
      <returns>An <see cref="T:System.Int32" /> object that contains the value of the next viseme.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.VisemeReachedEventArgs.Viseme">
      <summary>Gets the value of the viseme.</summary>
      <returns>An <see cref="T:System.Int32" /> object that contains the value of the viseme.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.VoiceAge">
      <summary>Defines the values for the age of a synthesized voice.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceAge.NotSet">
      <summary>Indicates that no voice age is specified.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceAge.Child">
      <summary>Indicates a child voice (age 10).</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceAge.Teen">
      <summary>Indicates a teenage voice (age 15).</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceAge.Adult">
      <summary>Indicates an adult voice (age 30).</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceAge.Senior">
      <summary>Indicates a senior voice (age 65).</summary>
    </member>
    <member name="T:System.Speech.Synthesis.VoiceChangeEventArgs">
      <summary>Returns data from the <see cref="E:System.Speech.Synthesis.SpeechSynthesizer.VoiceChange" /> event.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceChangeEventArgs.Voice">
      <summary>Gets the <see cref="T:System.Speech.Synthesis.VoiceInfo" /> object of the new voice.</summary>
      <returns>Returns information that describes and identifies the new voice.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.VoiceGender">
      <summary>Defines the values for the gender of a synthesized voice.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceGender.NotSet">
      <summary>Indicates no voice gender specification.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceGender.Male">
      <summary>Indicates a male voice.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceGender.Female">
      <summary>Indicates a female voice.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.VoiceGender.Neutral">
      <summary>Indicates a gender-neutral voice.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.VoiceInfo">
      <summary>Represents an installed speech synthesis engine.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.AdditionalInfo">
      <summary>Gets additional information about the voice.</summary>
      <returns>Returns a collection of name/value pairs that describe and identify the voice.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.Age">
      <summary>Gets the age of the voice.</summary>
      <returns>Returns the age of the voice. </returns>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.Culture">
      <summary>Gets the culture of the voice.</summary>
      <returns>Returns a <see cref="T:System.Globalization.CultureInfo" /> object that provides information about a specific culture, such as the names of the culture, the writing system, the calendar used, and how to format dates and sort strings.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.Description">
      <summary>Gets the description of the voice.</summary>
      <returns>Returns the description of the voice.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.VoiceInfo.Equals(System.Object)">
      <summary>Compares the fields of the voice with the specified <see cref="T:System.Speech.Synthesis.VoiceInfo" /> object to determine whether they contain the same values.</summary>
      <returns>Returns True if the fields of the two <see cref="T:System.Speech.Synthesis.VoiceInfo" /> objects are equal; otherwise returns False. </returns>
      <param name="obj">  The specified <see cref="T:System.Speech.Synthesis.VoiceInfo" /> object.</param>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.Gender">
      <summary>Gets the gender of the voice.</summary>
      <returns>Returns the gender of the voice.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.VoiceInfo.GetHashCode"></member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.Id">
      <summary>Gets the ID of the voice.</summary>
      <returns>Returns the identifier for the voice.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.Name">
      <summary>Gets the name of the voice.</summary>
      <returns>Returns the name of the voice.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.VoiceInfo.SupportedAudioFormats">
      <summary>Gets the collection of audio formats that the voice supports.</summary>
      <returns>Returns a collection of the audio formats that the voice supports.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ContourPoint">
      <summary>Represents the volume for a text fragment with which the <see cref="T:System.Speech.Synthesis.TtsEngine.ContourPoint" /> is associated. </summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ContourPoint.#ctor(System.Single,System.Single,System.Speech.Synthesis.TtsEngine.ContourPointChangeType)"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ContourPoint.Change"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ContourPoint.ChangeType"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ContourPoint.Equals(System.Object)">
      <summary>Determines whether the specified ContourPoint instances are considered equal.</summary>
      <returns>true if the specified Object is equal to the current Object; otherwise, false.</returns>
      <param name="obj">The System.Object to compare with the current Object.</param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ContourPoint.Equals(System.Speech.Synthesis.TtsEngine.ContourPoint)"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ContourPoint.GetHashCode">
      <summary>Returns a hash code for this instance.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ContourPoint.op_Equality(System.Speech.Synthesis.TtsEngine.ContourPoint,System.Speech.Synthesis.TtsEngine.ContourPoint)">
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ContourPoint.op_Inequality(System.Speech.Synthesis.TtsEngine.ContourPoint,System.Speech.Synthesis.TtsEngine.ContourPoint)">
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ContourPoint.Start"></member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ContourPointChangeType">
      <summary>Enumerates values for the types of ContourPoint change.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ContourPointChangeType.Hz">
      <summary>Indicates a change of the pitch value.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ContourPointChangeType.Percentage">
      <summary>Indicates a change of the time value.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.EmphasisBreak">
      <summary>Enumerates values for lengths of EmphasisBreak between spoken words.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisBreak.None">
      <summary>No word break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisBreak.ExtraWeak">
      <summary>Very small word break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisBreak.Weak">
      <summary>Small word break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisBreak.Medium">
      <summary>Moderate word break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisBreak.Strong">
      <summary>Long word break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisBreak.ExtraStrong">
      <summary>Longest word break.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisBreak.Default">
      <summary>Normal word break.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.EmphasisWord">
      <summary>Enumerates the values of EmphasisWord for a specific TextFragment.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisWord.Default">
      <summary>Indicates an engine-specific default level of emphasis.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisWord.Strong">
      <summary>Indicates strong emphasis.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisWord.Moderate">
      <summary>Indicates moderate emphasis.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisWord.None">
      <summary>Indicates no emphasis specified.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EmphasisWord.Reduced">
      <summary>Indicates reduced emphasis.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.EventParameterType">
      <summary>Enumerates the types of data pointers passed to speech synthesis events.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EventParameterType.Undefined">
      <summary>Indicates that the <paramref name="param2" /> argument to the <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is undefined.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EventParameterType.Token">
      <summary>Indicates that the <paramref name="param2" /> argument to the <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is a </summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EventParameterType.Object">
      <summary>Currently not supported.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EventParameterType.Pointer">
      <summary>Currently not supported.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.EventParameterType.String">
      <summary>Indicates that the <paramref name="param2" /> argument to the <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is a System.IntPtr created using System.Runtime.InteropServices.Marshal.StringToCoTaskMemUni referencing a System.String object; <paramref name="param1" /> may take on any value.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.FragmentState">
      <summary>Provides detailed information about a TextFragment.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.FragmentState.#ctor(System.Speech.Synthesis.TtsEngine.TtsEngineAction,System.Int32,System.Int32,System.Int32,System.Speech.Synthesis.TtsEngine.SayAs,System.Speech.Synthesis.TtsEngine.Prosody,System.Char[])">
      <summary>Constructs a new instance of FragmentState.</summary>
      <param name="action">A member of the <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEngineAction" />  enumeration that specifies a speech synthesis action.</param>
      <param name="langId">The id of the language being used. Corresponds to the XML xml:lang attribute.</param>
      <param name="emphasis">The emphasis to be applied to speech output or pauses.</param>
      <param name="duration">The time allotted to speak the text of the TextFragment. </param>
      <param name="sayAs">A member of the <see cref="T:System.Speech.Synthesis.TtsEngine.SayAs" /> class, indicating the type of text of the TextFragment and the level of detail required for accurate rendering of the contained text. Corresponds to the &lt;say-as&gt; XML tag in the SSML specificationThe argument may be null.</param>
      <param name="prosody">A <see cref="T:System.Speech.Synthesis.TtsEngine.Prosody" /> object indicating characteristics of the speech output such as pitch, speaking rate and volume. Corresponds to the &lt;prosody&gt; XML tag in the SSML specification</param>
      <param name="phonemes">An array of char objects providing the phonetic pronunciation for text contained in the <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" />, using the International Phonetic Alphabet (IPA) specification.Corresponds to the &lt;phoneme&gt; XML tag in the SSML specification.This argument may be null.</param>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.FragmentState.Action">
      <summary>Returns the requested speech synthesizer action.</summary>
      <returns>A member of <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEngineAction" /> indicating the speech synthesis action requested by SSML input.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.FragmentState.Duration">
      <summary>Returns the desired time for rendering a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /></summary>
      <returns>Returns an int containing a value in millisecond of the desired time for rendering a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" />.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.FragmentState.Emphasis">
      <summary>Returns instructions on how to emphasize a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" />.</summary>
      <returns>Returns an int value indicating how to emphasize a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" />.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.FragmentState.Equals(System.Object)">
      <summary>Determines if a given object is an instance <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" /> equal to the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" />.</summary>
      <returns>Returns true, if the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> and that obtained from the  provided by the <paramref name="obj" /> argument describe the same <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state. Returns false if the current <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> and the <paramref name="obj" /> argument do not support the same <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state.</returns>
      <param name="obj">An object that can be cast to an instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /></param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.FragmentState.Equals(System.Speech.Synthesis.TtsEngine.FragmentState)">
      <summary>Determines if a given instance of <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" /> is equal to the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" />.</summary>
      <returns>Returns true, if both the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> and that supplied through the <paramref name="other" /> argument describe the same <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state. Returns false if the current <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> and the <paramref name="other" /> argument do not support the same <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state.</returns>
      <param name="other">An instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> that </param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.FragmentState.GetHashCode">
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.FragmentState.LangId">
      <summary>Returns the language supported by the current <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" />.</summary>
      <returns>Returns an int containing an identifier for the language used by the current <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" />.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.FragmentState.op_Equality(System.Speech.Synthesis.TtsEngine.FragmentState,System.Speech.Synthesis.TtsEngine.FragmentState)">
      <summary>Determines if two instances of <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" /> describes the same <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state.</summary>
      <returns>Returns true if both instances of <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" />, <paramref name="state1" /> and <paramref name="state2" />, describe the same <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state, otherwise false is returned.</returns>
      <param name="state1">An instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> whose described state is compared against the instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> provided by the <paramref name="state2" /> argument.</param>
      <param name="state2">An instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> whose described state is compared against the instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> provided by the <paramref name="state1" /> argument.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.FragmentState.op_Inequality(System.Speech.Synthesis.TtsEngine.FragmentState,System.Speech.Synthesis.TtsEngine.FragmentState)">
      <summary>Determines if two instances of <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" /> describes the different <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state.</summary>
      <returns>Returns true if both instances of <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" />, <paramref name="state1" /> and <paramref name="state2" />, do not describe the same <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> state, otherwise false is returned.</returns>
      <param name="state1">An instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> whose described state is compared against the instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> provided by the <paramref name="state2" /> argument.</param>
      <param name="state2">An instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> whose described state is compared against the instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> provided by the <paramref name="state1" /> argument.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.FragmentState.Phoneme">
      <summary>Returns phonetic information for a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /></summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.FragmentState.Prosody">
      <summary>Returns detailed information about the pitch, speaking rate, and volume of speech output.</summary>
      <returns>Returns a valid instance of <see cref="T:System.Speech.Synthesis.TtsEngine.Prosody" /> containing the pitch, speaking rate, and volume settings, and changes to those setting, for speech output.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.FragmentState.SayAs">
      <summary>Returns information about the context for the generation of speech from text..</summary>
      <returns>Returns a value <see cref="T:System.Speech.Synthesis.TtsEngine.SayAs" /> instance if the SSML used by a speech synthesis engine contains detailed information about the context to be used to generate speech, otherwise null.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ITtsEngineSite">
      <summary>Provides methods for writing audio data and events.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.Actions">
      <summary>Determines the action or actions the engine should perform.</summary>
      <returns>An int containing the sum of one or more members of the TtsEngineAction enumeration.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.AddEvents(System.Speech.Synthesis.TtsEngine.SpeechEventInfo[],System.Int32)">
      <summary>Adds one or more events to the EventInterest property.</summary>
      <param name="events">An array of SpeechEventInfo objects.</param>
      <param name="count">The size of the array.</param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.CompleteSkip(System.Int32)">
      <summary>Returns the number of items skipped.</summary>
      <param name="skipped">The number of items skipped.</param>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.EventInterest">
      <summary>Determines the events the engine should raise.</summary>
      <returns>An int containing the sum of one or more members of the TtsEventId enumeration.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.GetSkipInfo">
      <summary>Returns the number and type of items to be skipped.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.LoadResource(System.Uri,System.String)">
      <summary>Loads the resource at the specified URI.</summary>
      <param name="uri">The URI of the resource.</param>
      <param name="mediaType">The media type of the resource.</param>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.Rate">
      <summary>Gets the speaking rate of the engine.</summary>
      <returns>An int containing the speaking rate.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.Volume">
      <summary>Gets the speaking volume of the engine.</summary>
      <returns>An int containing the speaking volume.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.Write(System.IntPtr,System.Int32)">
      <summary>Outputs audio data.</summary>
      <param name="data">The location of the output audio data.</param>
      <param name="count">The number of items in the output audio stream.</param>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.Prosody">
      <summary>Represents a collection of settings for voice properties such as Pitch, Rate and Volume.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.Prosody.#ctor">
      <summary>Constructs a new instance of the Prosody class.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.Prosody.Duration">
      <summary>Gets or sets the duration of the TextFragment in milliseconds.</summary>
      <returns>A value in milliseconds for the desired time to speak the text.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.Prosody.GetContourPoints">
      <summary>Returns an array containing the ContourPoints of the TextFragment.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.Prosody.Pitch">
      <summary>Gets or sets the baseline pitch of the TextFragment.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.Prosody.Range">
      <summary>Gets or sets the pitch range of the TextFragment.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.Prosody.Rate">
      <summary>Gets or sets the speaking rate of the TextFragment.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.Prosody.SetContourPoints(System.Speech.Synthesis.TtsEngine.ContourPoint[])">
      <summary>Sets the ContourPoints of the TextFragment.</summary>
      <param name="points">A byte array of ContourPoint objects.</param>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.Prosody.Volume">
      <summary>Gets or sets the speaking volume (loudness) of the TextFragment.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ProsodyNumber"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ProsodyNumber.#ctor(System.Int32)"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ProsodyNumber.#ctor(System.Single)"></member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyNumber.AbsoluteNumber"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ProsodyNumber.Equals(System.Object)"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ProsodyNumber.Equals(System.Speech.Synthesis.TtsEngine.ProsodyNumber)"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ProsodyNumber.GetHashCode">
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ProsodyNumber.IsNumberPercent"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ProsodyNumber.Number"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ProsodyNumber.op_Equality(System.Speech.Synthesis.TtsEngine.ProsodyNumber,System.Speech.Synthesis.TtsEngine.ProsodyNumber)"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.ProsodyNumber.op_Inequality(System.Speech.Synthesis.TtsEngine.ProsodyNumber,System.Speech.Synthesis.TtsEngine.ProsodyNumber)"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ProsodyNumber.SsmlAttributeId"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.ProsodyNumber.Unit"></member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ProsodyPitch">
      <summary>Enumerates values for the Pitch property of a Prosody object.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyPitch.Default">
      <summary>Indicates a normal pitch range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyPitch.ExtraLow">
      <summary>Indicates an extra-low pitch range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyPitch.Low">
      <summary>Indicates a low pitch range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyPitch.Medium">
      <summary>Indicates a medium pitch range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyPitch.High">
      <summary>Indicates a high pitch range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyPitch.ExtraHigh">
      <summary>Indicates an extra- high pitch range.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ProsodyRange">
      <summary>Enumerates values for the Range property of a Prosody object.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRange.Default">
      <summary>Indicates a normal prosody range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRange.ExtraLow">
      <summary>Indicates an extra low prosody range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRange.Low">
      <summary>Indicates a low prosody range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRange.Medium">
      <summary>Indicates a medium prosody range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRange.High">
      <summary>Indicates a high prosody range.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRange.ExtraHigh">
      <summary>Indicates an extra high prosody range.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ProsodyRate">
      <summary>Enumerates values for the Rate property of a Prosody object.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRate.Default">
      <summary>Indicates the engine-specific default rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRate.ExtraSlow">
      <summary>Indicates an extra-slow rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRate.Slow">
      <summary>Indicates a slow rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRate.Medium">
      <summary>Indicates a medium rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRate.Fast">
      <summary>Indicates a fast rate.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyRate.ExtraFast">
      <summary>Indicates an extra-fast rate.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ProsodyUnit">
      <summary>Enumerates values for the Unit property on the Prosody object.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyUnit.Default">
      <summary>Indicates the engine-specific default value.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyUnit.Hz">
      <summary>Indicates the Unit value is Hz.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyUnit.Semitone">
      <summary>Indicates the Unit value is semitone.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.ProsodyVolume">
      <summary>Enumerates values for the Volume property of a Prosody object.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyVolume.Default">
      <summary>Current default volume value, same as the value returned by the <see cref="P:System.Speech.Synthesis.TtsEngine.ITtsEngineSite.Volume" /> property on the <see cref="T:System.Speech.Synthesis.TtsEngine.ITtsEngineSite" /> site supplied to that engine.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyVolume.Silent">
      <summary>Volume off</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyVolume.ExtraSoft">
      <summary>Approximately 20% of maximum volume.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyVolume.Soft">
      <summary>Approximately 40% of maximum volume.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyVolume.Medium">
      <summary>Approximately 60% of maximum volume.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyVolume.Loud">
      <summary>Approximately 80% of maximum volume.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.ProsodyVolume.ExtraLoud">
      <summary>Maximum volume.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.SayAs"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SayAs.#ctor"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SayAs.Detail"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SayAs.Format"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SayAs.InterpretAs"></member>
    <member name="T:System.Speech.Synthesis.TtsEngine.SkipInfo">
      <summary>Provides information about text stream items to be skipped.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SkipInfo.#ctor">
      <summary>Creates a new instance of the SkipInfo object.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SkipInfo.Count">
      <summary>Gets or sets the number of items to be skipped.</summary>
      <returns>An int containing the number of items.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SkipInfo.Type">
      <summary>Gets or sets the type of object to skip.</summary>
      <returns>An int representing the type of the object.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.SpeakOutputFormat">
      <summary>Enumerates the types of speech output formats.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.SpeakOutputFormat.WaveFormat">
      <summary>Indicates wave (audio) output.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.SpeakOutputFormat.Text">
      <summary>Indicates text output.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo">
      <summary>Used to specify the type of event, and its arguments (if any)  to be generated as part of the rendering of text to speech by a custom synthetic speech engine.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.#ctor(System.Int16,System.Int16,System.Int32,System.IntPtr)">
      <summary>Constucts an appropriate <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" />. </summary>
      <param name="eventId">An instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEventId" /> indicating the sort of Speech platform event the <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> object is to handle.</param>
      <param name="parameterType">An instance of <see cref="T:System.Speech.Synthesis.TtsEngine.EventParameterType" /> indicating how the System.IntPtr reference of <paramref name="param2" /> is to be interpreted, and, by implication, the use of <paramref name="param1" />.</param>
      <param name="param1">An integer value to be passed to the Speech platform when the event requested by the instance of <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> to be constructed is generated.The exact meaning of this integer is implicitly determined by the value of <paramref name="parameterType" />.</param>
      <param name="param2">A System.IntPtr instance referencing an object. to be passed to the Speech platform when the event requested by the instance of <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> to be constructed is generated.The type which must be referenced is explicitly defined by the value <paramref name="parameterType" />. The value System.IntPtr.Zero.</param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.Equals(System.Object)"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.Equals(System.Speech.Synthesis.TtsEngine.SpeechEventInfo)"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.EventId">
      <summary>Gets and set the Speech platform event which an instance of <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is used to request. </summary>
      <returns>Returns a member of <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEventId" /> as a short, indicating the event type the <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> object is to generate.</returns>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.GetHashCode">
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.op_Equality(System.Speech.Synthesis.TtsEngine.SpeechEventInfo,System.Speech.Synthesis.TtsEngine.SpeechEventInfo)"></member>
    <member name="M:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.op_Inequality(System.Speech.Synthesis.TtsEngine.SpeechEventInfo,System.Speech.Synthesis.TtsEngine.SpeechEventInfo)"></member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.Param1">
      <summary>Gets and set the integer value (<paramref name="param1" /> in the constructor) to be passed to the Speech platform to generate an event the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is used to request. </summary>
      <returns>Returns the integer to be passed to Speech platform when the event specified by the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is generated.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.Param2">
      <summary>Gets and set the System.IntPtr instance (<paramref name="param2" /> in the constructor) referencing the object to be passed to the Speech platform to generate an event the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is used to request. </summary>
      <returns>Returns the System.IntPtr referencing the object to be passed to Speech platform when the event specified by the current instance of <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> is generated.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.ParameterType">
      <summary>Returns the data type of the object pointed to by the IntPtr returned by the <see cref="P:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.Param2" /> parameter on the current <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> object.</summary>
      <returns>A short value corresponding to a member of the <see cref="T:System.Speech.Synthesis.TtsEngine.EventParameterType" /> enumeration and indicating the data type of the object pointed to by the IntPtr returned by the <see cref="P:System.Speech.Synthesis.TtsEngine.SpeechEventInfo.Param2" /> parameter and used as the second argument for the constructor of the current <see cref="T:System.Speech.Synthesis.TtsEngine.SpeechEventInfo" /> object.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.TextFragment">
      <summary>Contains text and speech attribute information for consumption by a speech synthsizer engine.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.TextFragment.#ctor">
      <summary>Constructs a new instance of TextFragment.</summary>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.TextFragment.State">
      <summary>Gets or sets speech attribute information for a TextFragment.</summary>
      <returns>A <see cref="T:System.Speech.Synthesis.TtsEngine.FragmentState" /> instance is returned, or used to set speech attribute information for a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" />.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.TextFragment.TextLength">
      <summary>Gets or sets the length of the speech text in the fragment.</summary>
      <returns>An int is returned or can be used to set the length, in character, of the text string associated with this fragment to be spoken.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.TextFragment.TextOffset">
      <summary>Gets or sets the starting location of the text in the fragment.</summary>
      <returns>An int is returned or can be used to set the start location, in character, of the part of text string associated with this fragment to be spoken.</returns>
    </member>
    <member name="P:System.Speech.Synthesis.TtsEngine.TextFragment.TextToSpeak">
      <summary>Sets or gets the speech text of the fragment.</summary>
      <returns>A System.String is returned or can be used to set the speech text to be used by a speech synthesis engine to generate audio output.</returns>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.TtsEngineAction">
      <summary>Specifies the Speech Synthesis Markup Language (SSML) action to be taken in rendering a given TextFragment. </summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.Speak">
      <summary>Requests that the associated <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> should be processed and spoken.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.Silence">
      <summary>Indicates that a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> contains no text to be rendered as speech. </summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.Pronounce">
      <summary>Requests that input <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> text be interpreted as phonemes. </summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.Bookmark">
      <summary>Indicates that <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> is to be used as the contents of a bookmark.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.SpellOut">
      <summary>Indicates that text values provided by a <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> through its <see cref="P:System.Speech.Synthesis.TtsEngine.TextFragment.TextToSpeak" /> property are to be synthesize as individual characters. </summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.StartSentence">
      <summary>Indicates start of sentence.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.StartParagraph">
      <summary>Indicates state of paragraph.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEngineAction.ParseUnknownTag">
      <summary>Indicates that no action has been determined from SSML input.</summary>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.TtsEngineSsml">
      <summary>Abstract base class to be implemented by all text to speech synthesis engines.</summary>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.TtsEngineSsml.#ctor(System.String)">
      <summary>Constructs a new instance of <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEngineSsml" /> based on an appropriate Voice Token registry key.</summary>
      <param name="registryKey">Full name of the registry key for the Voice Token associated with the <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEngineSsml" /> implementation. engine. </param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.TtsEngineSsml.AddLexicon(System.Uri,System.String,System.Speech.Synthesis.TtsEngine.ITtsEngineSite)">
      <summary>Adds a lexicon to the SynthesizerVoice implemented by the current <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEngineSsml" /> instance. </summary>
      <param name="uri">A valid instance of System.Uri indicating the location of the lexicon information.</param>
      <param name="mediaType">A string containing the media type of the lexicon. Media types are case insensitive.</param>
      <param name="site">A reference to an <see cref="T:System.Speech.Synthesis.TtsEngine.ITtsEngineSite" /> interface used to interact with the platform infrastructure.</param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.TtsEngineSsml.GetOutputFormat(System.Speech.Synthesis.TtsEngine.SpeakOutputFormat,System.IntPtr)">
      <summary>Returns the best matching audio output supported by a given synthesize engine response to a request to the synthesizer engine for the support of a particular output format.</summary>
      <returns>Returns a valid IntPtr instance referring to a struct containing detailed information about the output format.</returns>
      <param name="speakOutputFormat">Valid member of the <see cref="T:System.Speech.Synthesis.TtsEngine.SpeakOutputFormat" /> enumeration indicating the type of requested audio output format.</param>
      <param name="targetWaveFormat">A pointer to a struct containing detail setting for the audio format type requested by the <paramref name="speakOutputFormat" /> argument.</param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.TtsEngineSsml.RemoveLexicon(System.Uri,System.Speech.Synthesis.TtsEngine.ITtsEngineSite)">
      <summary>Removes a lexicon currently loaded by the SynthesizerVoice implemented by the current <see cref="T:System.Speech.Synthesis.TtsEngine.TtsEngineSsml" /> instance.</summary>
      <param name="uri">A valid instance of System.Uri indicating the location of the lexicon information.</param>
      <param name="site">A reference to an <see cref="T:System.Speech.Synthesis.TtsEngine.ITtsEngineSite" /> interface passed in by the platform infrastructure to allow access to the infrastructure resources.</param>
    </member>
    <member name="M:System.Speech.Synthesis.TtsEngine.TtsEngineSsml.Speak(System.Speech.Synthesis.TtsEngine.TextFragment[],System.IntPtr,System.Speech.Synthesis.TtsEngine.ITtsEngineSite)">
      <summary>Renders specified <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> array in the specified output format.</summary>
      <param name="fragment">An array of <see cref="T:System.Speech.Synthesis.TtsEngine.TextFragment" /> instances containing the text to be rendered into speech.</param>
      <param name="waveHeader">An IntPtr pointing to a structure containing audio output format.</param>
      <param name="site">A reference to an <see cref="T:System.Speech.Synthesis.TtsEngine.ITtsEngineSite" /> interface passed in by the platform infrastructure to allow access to the infrastructure resources.</param>
    </member>
    <member name="T:System.Speech.Synthesis.TtsEngine.TtsEventId">
      <summary>Enumerates types of speech synthesis events.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.StartInputStream">
      <summary>Identifies events generated when a speech synthesize engine a begins speaking a stream.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.EndInputStream">
      <summary>Identifies events generated when a speech synthesize engine encounters the end of its input stream while speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.VoiceChange">
      <summary>Identifies events generated when a speech synthesize engine encounters a change of Voice while speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.Bookmark">
      <summary>Identifies events generated when a speech synthesize engine encounters a bookmark while speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.WordBoundary">
      <summary>Identifies events generated when a speech synthesize engine completes a word while speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.Phoneme">
      <summary>Identifies events generated when a speech synthesize engine completes a phoneme while speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.SentenceBoundary">
      <summary>Identifies events generated when a speech synthesize engine completes a sentence while speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.Viseme">
      <summary>Identifies events generated when a speech synthesize engine completes a viseme while speaking.</summary>
    </member>
    <member name="F:System.Speech.Synthesis.TtsEngine.TtsEventId.AudioLevel">
      <summary>Identifies events generated when a speech synthesize engine completes an audio level change while speaking.</summary>
    </member>
  </members>
</doc>