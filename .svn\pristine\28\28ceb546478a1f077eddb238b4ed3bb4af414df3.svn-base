﻿using OCRTools.AforgeImage;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.Linq;

namespace OCRTools
{
    class ImageAngleHelper
    {
        public static float GmseSkew(Bitmap image)
        {
            gmseDeskew sk = new gmseDeskew(image);
            float skewangle = (float)sk.GetSkewAngle();

            return skewangle;
        }

        public static float AforgeSkew(Bitmap documentImage)
        {
            // create instance of skew checker
            DocumentSkewChecker skewChecker = new DocumentSkewChecker();
            // get documents skew angle
            double angle = skewChecker.GetSkewAngle(documentImage);

            return (float)angle;
        }

        public static Bitmap RotateImage(Bitmap bmp, float angle)
        {
            Graphics g;
            Bitmap tmp = new Bitmap(bmp.Width, bmp.Height, PixelFormat.Format32bppRgb);

            tmp.SetResolution(bmp.HorizontalResolution, bmp.VerticalResolution);
            g = Graphics.FromImage(tmp);
            try
            {
                g.FillRectangle(Brushes.White, 0, 0, bmp.Width, bmp.Height);
                g.RotateTransform(angle);
                g.DrawImage(bmp, 0, 0);
            }
            finally
            {
                g.Dispose();
            }
            return tmp;
        }

        public static Image GetRotateImageByRedis(Image img, double redis)
        {
            float radian = (float)(redis * 180.0 / Math.PI);
            return GetRotateImage(img, radian);
        }

        public static Image GetRotateImage(Image img, float angle)
        {
            angle = angle % 360;//弧度转换
            if (angle < 0)
            {
                angle += 360;
            }
            double cos = Math.Cos(angle);
            double sin = Math.Sin(angle);
            //原图的宽和高
            int w = img.Width;
            int h = img.Height;
            int W = (int)(Math.Max(Math.Abs(w * cos - h * sin), Math.Abs(w * cos + h * sin)));
            W = Math.Max(w, W);
            int H = (int)(Math.Max(Math.Abs(w * sin - h * cos), Math.Abs(w * sin + h * cos)));
            H = Math.Max(h, H);
            //目标位图
            Image dsImage = new Bitmap(W, H, img.PixelFormat);
            using (Graphics g = Graphics.FromImage(dsImage))
            {
                g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.Bilinear;
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                //计算偏移量
                Point Offset = new Point((W - w) / 2, (H - h) / 2);
                //构造图像显示区域：让图像的中心与窗口的中心点一致
                Rectangle rect = new Rectangle(Offset.X, Offset.Y, w, h);
                Point center = new Point(rect.X + rect.Width / 2, rect.Y + rect.Height / 2);
                g.TranslateTransform(center.X, center.Y);
                g.RotateTransform(angle);
                //恢复图像在水平和垂直方向的平移
                g.TranslateTransform(-center.X, -center.Y);
                g.DrawImage(img, rect);
                //重至绘图的所有变换
                g.ResetTransform();
                g.Save();
            }
            return dsImage;
        }

        private static double ToDegrees(double slope) { return (180.0 / Math.PI) * Math.Atan(slope); }

        public static double GetSkew(Bitmap image)
        {
            BrightnessWrapper wrapper = new BrightnessWrapper(image);

            LinkedList<double> slopes = new LinkedList<double>();

            for (int y = 0; y < wrapper.Height; y++)
            {
                int endY = y;

                long sumOfX = 0;
                long sumOfY = y;
                long sumOfXY = 0;
                long sumOfXX = 0;
                int itemsInSet = 1;
                for (int x = 1; x < wrapper.Width; x++)
                {
                    int aboveY = endY - 1;
                    int belowY = endY + 1;

                    if (aboveY < 0 || belowY >= wrapper.Height)
                    {
                        break;
                    }

                    int center = wrapper.GetBrightness(x, endY);
                    int above = wrapper.GetBrightness(x, aboveY);
                    int below = wrapper.GetBrightness(x, belowY);

                    if (center >= above && center >= below) { /* no change to endY */ }
                    else if (above >= center && above >= below) { endY = aboveY; }
                    else if (below >= center && below >= above) { endY = belowY; }

                    itemsInSet++;
                    sumOfX += x;
                    sumOfY += endY;
                    sumOfXX += (x * x);
                    sumOfXY += (x * endY);
                }

                // least squares slope = (NΣ(XY) - (ΣX)(ΣY)) / (NΣ(X^2) - (ΣX)^2), where N = elements in set
                if (itemsInSet > image.Width / 2) // path covers at least half of the image
                {
                    decimal sumOfX_d = Convert.ToDecimal(sumOfX);
                    decimal sumOfY_d = Convert.ToDecimal(sumOfY);
                    decimal sumOfXY_d = Convert.ToDecimal(sumOfXY);
                    decimal sumOfXX_d = Convert.ToDecimal(sumOfXX);
                    decimal itemsInSet_d = Convert.ToDecimal(itemsInSet);
                    decimal slope =
                        ((itemsInSet_d * sumOfXY) - (sumOfX_d * sumOfY_d))
                        /
                        ((itemsInSet_d * sumOfXX_d) - (sumOfX_d * sumOfX_d));

                    slopes.AddLast(Convert.ToDouble(slope));
                }
            }

            double mean = slopes.Average();
            double sumOfSquares = slopes.Sum(d => Math.Pow(d - mean, 2));
            double stddev = Math.Sqrt(sumOfSquares / (slopes.Count - 1));

            // select items within 1 standard deviation of the mean
            var testSample = slopes.Where(x => Math.Abs(x - mean) <= stddev);

            return ToDegrees(testSample.Average());
        }

    }

    class BrightnessWrapper
    {
        byte[] rgbValues;
        int stride;
        public int Height { get; private set; }
        public int Width { get; private set; }

        public BrightnessWrapper(Bitmap bmp)
        {
            Rectangle rect = new Rectangle(0, 0, bmp.Width, bmp.Height);

            System.Drawing.Imaging.BitmapData bmpData =
                bmp.LockBits(rect,
                    System.Drawing.Imaging.ImageLockMode.ReadOnly,
                    bmp.PixelFormat);

            IntPtr ptr = bmpData.Scan0;

            int bytes = bmpData.Stride * bmp.Height;
            rgbValues = new byte[bytes];

            System.Runtime.InteropServices.Marshal.Copy(ptr,
                           rgbValues, 0, bytes);

            Height = bmp.Height;
            Width = bmp.Width;
            stride = bmpData.Stride;
        }

        public int GetBrightness(int x, int y)
        {
            int position = (y * stride) + (x * 3);
            int b = rgbValues[position];
            int g = rgbValues[position + 1];
            int r = rgbValues[position + 2];
            return (r + r + b + g + g + g) / 6;
        }
    }


    public class gmseDeskew
    {
        // Representation of a line in the image.
        public class HougLine
        {
            // Count of points in the line.
            public int Count;
            // Index in Matrix.
            public int Index;
            // The line is represented as all x,y that solve y*cos(alpha)-x*sin(alpha)=d
            public double Alpha;
            public double d;
        }
        // The Bitmap
        private Bitmap cBmp;
        // The range of angles to search for lines
        private double cAlphaStart = -20;
        private double cAlphaStep = 0.2;
        private int cSteps = 40 * 5;
        // Precalculation of sin and cos.
        private double[] cSinA;
        private double[] cCosA;
        // Range of d
        private double cDMin;
        private double cDStep = 1;
        private int cDCount;
        // Count of points that fit in a line.
        private int[] cHMatrix;

        // Calculate the skew angle of the image cBmp.
        public double GetSkewAngle()
        {
            HougLine[] hl;
            int i;
            double sum = 0;
            int count = 0;

            // Hough Transformation
            Calc();
            // Top 20 of the detected lines in the image.
            hl = GetTop(50);
            // Average angle of the lines
            for (i = 0; i <= 19; i++)
            {
                sum += hl[i].Alpha;
                count += 1;
            }
            return sum / count;
        }

        // Calculate the Count lines in the image with most points.
        private HougLine[] GetTop(int Count)
        {
            HougLine[] hl;
            int i;
            int j;
            HougLine tmp;
            int AlphaIndex;
            int dIndex;

            hl = new HougLine[Count + 1];
            for (i = 0; i <= Count - 1; i++)
                hl[i] = new HougLine();
            for (i = 0; i <= cHMatrix.Length - 1; i++)
            {
                if (cHMatrix[i] > hl[Count - 1].Count)
                {
                    hl[Count - 1].Count = cHMatrix[i];
                    hl[Count - 1].Index = i;
                    j = Count - 1;
                    while (j > 0 && hl[j].Count > hl[j - 1].Count)
                    {
                        tmp = hl[j];
                        hl[j] = hl[j - 1];
                        hl[j - 1] = tmp;
                        j -= 1;
                    }
                }
            }
            for (i = 0; i <= Count - 1; i++)
            {
                dIndex = hl[i].Index / cSteps;
                AlphaIndex = hl[i].Index - dIndex * cSteps;
                hl[i].Alpha = GetAlpha(AlphaIndex);
                hl[i].d = dIndex + cDMin;
            }
            return hl;
        }
        public gmseDeskew(Bitmap bmp)
        {
            cBmp = bmp;
        }
        // Hough Transforamtion:
        private void Calc()
        {
            int x;
            int y;
            int hMin = (int)(cBmp.Height / (double)4);
            int hMax = (int)(cBmp.Height * 3 / (double)4);

            Init();
            for (y = hMin; y <= hMax; y++)
            {
                for (x = 1; x <= cBmp.Width - 2; x++)
                {
                    // Only lower edges are considered.
                    if (IsBlack(x, y))
                    {
                        if (!IsBlack(x, y + 1))
                            Calc(x, y);
                    }
                }
            }
        }
        // Calculate all lines through the point (x,y).
        private void Calc(int x, int y)
        {
            int alpha;
            double d;
            int dIndex;
            int Index;

            for (alpha = 0; alpha <= cSteps - 1; alpha++)
            {
                d = y * cCosA[alpha] - x * cSinA[alpha];
                dIndex = CalcDIndex(d);
                Index = dIndex * cSteps + alpha;
                try
                {
                    cHMatrix[Index] += 1;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine(ex.ToString());
                }
            }
        }
        private int CalcDIndex(double d)
        {
            return Convert.ToInt32(d - cDMin);
        }
        private bool IsBlack(int x, int y)
        {
            Color c;
            double luminance;

            c = cBmp.GetPixel(x, y);
            luminance = (c.R * 0.299) + (c.G * 0.587) + (c.B * 0.114);
            return luminance < 140;
        }
        private void Init()
        {
            int i;
            double angle;

            // Precalculation of sin and cos.
            cSinA = new double[cSteps - 1 + 1];
            cCosA = new double[cSteps - 1 + 1];
            for (i = 0; i <= cSteps - 1; i++)
            {
                angle = GetAlpha(i) * Math.PI / 180.0;
                cSinA[i] = (int)Math.Sin(angle);
                cCosA[i] = Math.Cos(angle);
            }
            // Range of d:
            cDMin = -cBmp.Width;
            cDCount = (int)(2 * (cBmp.Width + cBmp.Height) / cDStep);
            cHMatrix = new int[cDCount * cSteps + 1];
        }

        public double GetAlpha(int Index)
        {
            return cAlphaStart + Index * cAlphaStep;
        }
    }

}
