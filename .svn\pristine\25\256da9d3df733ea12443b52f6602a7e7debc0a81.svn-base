﻿using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace MetroFramework.Controls
{
    [Designer(typeof(Design.MetroTextBoxDesigner), typeof(System.Windows.Forms.Design.ParentControlDesigner))]
    public class MetroTextBox : Control, IMetroControl
    {
        public delegate void ButClick(object sender, EventArgs e);

        private class PromptedTextBox : TextBox
        {
            private const int OCM_COMMAND = 8465;

            private const int WM_PAINT = 15;

            private bool drawPrompt;

            private string promptText = "";

            private Color _waterMarkColor = MetroPaint.ForeColor.Button.Disabled(MetroThemeStyle.Dark);

            private Font _waterMarkFont = MetroFonts.WaterMark(MetroLabelSize.Small, MetroWaterMarkWeight.Italic);

            [DefaultValue("")]
            [Browsable(true)]
            [EditorBrowsable(EditorBrowsableState.Always)]
            public string WaterMark
            {
                get
                {
                    return promptText;
                }
                set
                {
                    promptText = value.Trim();
                    Invalidate();
                }
            }

            public Color WaterMarkColor
            {
                get
                {
                    return _waterMarkColor;
                }
                set
                {
                    _waterMarkColor = value;
                    Invalidate();
                }
            }

            public Font WaterMarkFont
            {
                get
                {
                    return _waterMarkFont;
                }
                set
                {
                    _waterMarkFont = value;
                }
            }

            public PromptedTextBox()
            {
                SetStyle(ControlStyles.DoubleBuffer | ControlStyles.OptimizedDoubleBuffer, value: true);
                drawPrompt = (Text.Trim().Length == 0);
            }

            private void DrawTextPrompt()
            {
                using (Graphics g = CreateGraphics())
                {
                    DrawTextPrompt(g);
                }
            }

            private void DrawTextPrompt(Graphics g)
            {
                TextFormatFlags textFormatFlags = TextFormatFlags.EndEllipsis | TextFormatFlags.NoPadding;
                Rectangle clientRectangle = base.ClientRectangle;
                switch (base.TextAlign)
                {
                    case HorizontalAlignment.Left:
                        clientRectangle.Offset(1, 0);
                        break;
                    case HorizontalAlignment.Right:
                        textFormatFlags |= TextFormatFlags.Right;
                        clientRectangle.Offset(-2, 0);
                        break;
                    case HorizontalAlignment.Center:
                        textFormatFlags |= TextFormatFlags.HorizontalCenter;
                        clientRectangle.Offset(1, 0);
                        break;
                }
                new SolidBrush(WaterMarkColor);
                TextRenderer.DrawText(g, promptText, _waterMarkFont, clientRectangle, _waterMarkColor, BackColor, textFormatFlags);
            }

            protected override void OnPaint(PaintEventArgs e)
            {
                base.OnPaint(e);
                if (drawPrompt)
                {
                    DrawTextPrompt(e.Graphics);
                }
            }

            protected override void OnCreateControl()
            {
                base.OnCreateControl();
            }

            protected override void OnTextAlignChanged(EventArgs e)
            {
                base.OnTextAlignChanged(e);
                Invalidate();
            }

            protected override void OnTextChanged(EventArgs e)
            {
                base.OnTextChanged(e);
                drawPrompt = (Text.Trim().Length == 0);
                Invalidate();
            }

            protected override void WndProc(ref Message m)
            {
                base.WndProc(ref m);
                if ((m.Msg == 15 || m.Msg == 8465) && drawPrompt && !GetStyle(ControlStyles.UserPaint))
                {
                    DrawTextPrompt();
                }
            }

            protected override void OnLostFocus(EventArgs e)
            {
                base.OnLostFocus(e);
            }
        }

        public delegate void LUClear();

        [ToolboxItem(false)]
        public class MetroTextButton : Button, IMetroControl
        {
            private MetroColorStyle metroStyle;

            private MetroThemeStyle metroTheme;

            private MetroStyleManager metroStyleManager;

            private bool useCustomBackColor;

            private bool useCustomForeColor;

            private bool useStyleColors;

            private bool isHovered;

            private bool isPressed;

            private Bitmap _image;

            [DefaultValue(MetroColorStyle.Blue)]
            [Category("Metro Appearance")]
            public MetroColorStyle Style
            {
                get
                {
                    if (base.DesignMode || metroStyle != 0)
                    {
                        return metroStyle;
                    }
                    if (StyleManager != null)
                    {
                        return StyleManager.Style;
                    }
                    if (StyleManager == null)
                    {
                        return MetroColorStyle.Blue;
                    }
                    return metroStyle;
                }
                set
                {
                    metroStyle = value;
                }
            }

            [DefaultValue(MetroThemeStyle.Light)]
            [Category("Metro Appearance")]
            public MetroThemeStyle Theme
            {
                get
                {
                    if (base.DesignMode || metroTheme != 0)
                    {
                        return metroTheme;
                    }
                    if (StyleManager != null)
                    {
                        return StyleManager.Theme;
                    }
                    if (StyleManager == null)
                    {
                        return MetroThemeStyle.Light;
                    }
                    return metroTheme;
                }
                set
                {
                    metroTheme = value;
                }
            }

            [Browsable(false)]
            [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
            public MetroStyleManager StyleManager
            {
                get
                {
                    return metroStyleManager;
                }
                set
                {
                    metroStyleManager = value;
                }
            }

            [Category("Metro Appearance")]
            [DefaultValue(false)]
            public bool UseCustomBackColor
            {
                get
                {
                    return useCustomBackColor;
                }
                set
                {
                    useCustomBackColor = value;
                }
            }

            [DefaultValue(false)]
            [Category("Metro Appearance")]
            public bool UseCustomForeColor
            {
                get
                {
                    return useCustomForeColor;
                }
                set
                {
                    useCustomForeColor = value;
                }
            }

            [DefaultValue(false)]
            [Category("Metro Appearance")]
            public bool UseStyleColors
            {
                get
                {
                    return useStyleColors;
                }
                set
                {
                    useStyleColors = value;
                }
            }

            [Browsable(false)]
            [DefaultValue(false)]
            [Category("Metro Behaviour")]
            public bool UseSelectable
            {
                get
                {
                    return GetStyle(ControlStyles.Selectable);
                }
                set
                {
                    SetStyle(ControlStyles.Selectable, value);
                }
            }

            public new Image Image
            {
                get
                {
                    return base.Image;
                }
                set
                {
                    base.Image = value;
                    if (value != null)
                    {
                        _image = ApplyInvert(new Bitmap(value));
                    }
                }
            }

            protected Size iconSize
            {
                get
                {
                    if (Image != null)
                    {
                        Size size = Image.Size;
                        double num = 14.0 / size.Height;
                        new Point(1, 1);
                        return new Size((int)(size.Width * num), (int)(size.Height * num));
                    }
                    return new Size(-1, -1);
                }
            }

            [Category("Metro Appearance")]
            public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

            [Category("Metro Appearance")]
            public event EventHandler<MetroPaintEventArgs> CustomPaint;

            [Category("Metro Appearance")]
            public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

            protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
            {
                if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintBackground != null)
                {
                    this.CustomPaintBackground(this, e);
                }
            }

            protected virtual void OnCustomPaint(MetroPaintEventArgs e)
            {
                if (GetStyle(ControlStyles.UserPaint) && this.CustomPaint != null)
                {
                    this.CustomPaint(this, e);
                }
            }

            protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
            {
                if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintForeground != null)
                {
                    this.CustomPaintForeground(this, e);
                }
            }

            protected override void OnCreateControl()
            {
                base.OnCreateControl();
                SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, value: true);
            }

            protected override void OnPaint(PaintEventArgs e)
            {
                MetroThemeStyle theme = Theme;
                MetroColorStyle style = Style;
                Color foreColor;
                Color color;
                if (base.Parent != null)
                {
                    if (base.Parent is IMetroForm)
                    {
                        theme = ((IMetroForm)base.Parent).Theme;
                        style = ((IMetroForm)base.Parent).Style;
                        foreColor = MetroPaint.ForeColor.Button.Press(theme);
                        color = MetroPaint.GetStyleColor(style);
                    }
                    else if (base.Parent is IMetroControl)
                    {
                        theme = ((IMetroControl)base.Parent).Theme;
                        style = ((IMetroControl)base.Parent).Style;
                        foreColor = MetroPaint.ForeColor.Button.Press(theme);
                        color = MetroPaint.GetStyleColor(style);
                    }
                    else
                    {
                        foreColor = MetroPaint.ForeColor.Button.Press(theme);
                        color = MetroPaint.GetStyleColor(style);
                    }
                }
                else
                {
                    foreColor = MetroPaint.ForeColor.Button.Press(theme);
                    color = MetroPaint.BackColor.Form(theme);
                }
                if (isHovered && !isPressed && base.Enabled)
                {
                    _ = color.R;
                    _ = color.G;
                    _ = color.B;
                    color = ControlPaint.Light(color, 0.25f);
                }
                else if (isHovered && isPressed && base.Enabled)
                {
                    foreColor = MetroPaint.ForeColor.Button.Press(theme);
                    color = MetroPaint.GetStyleColor(style);
                }
                else if (!base.Enabled)
                {
                    foreColor = MetroPaint.ForeColor.Button.Disabled(theme);
                    color = MetroPaint.BackColor.Button.Disabled(theme);
                }
                else
                {
                    foreColor = MetroPaint.ForeColor.Button.Press(theme);
                }
                e.Graphics.Clear(color);
                Font font = MetroFonts.Button(MetroButtonSize.Small, MetroButtonWeight.Bold);
                TextRenderer.DrawText(e.Graphics, Text, font, base.ClientRectangle, foreColor, color, TextFormatFlags.EndEllipsis | TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                DrawIcon(e.Graphics);
            }

            public Bitmap ApplyInvert(Bitmap bitmapImage)
            {
                for (int i = 0; i < bitmapImage.Height; i++)
                {
                    for (int j = 0; j < bitmapImage.Width; j++)
                    {
                        Color pixel = bitmapImage.GetPixel(j, i);
                        byte a = pixel.A;
                        byte red = (byte)(255 - pixel.R);
                        byte green = (byte)(255 - pixel.G);
                        byte blue = (byte)(255 - pixel.B);
                        bitmapImage.SetPixel(j, i, Color.FromArgb(a, red, green, blue));
                    }
                }
                return bitmapImage;
            }

            private void DrawIcon(Graphics g)
            {
                if (Image != null)
                {
                    Point location = new Point(2, (base.ClientRectangle.Height - iconSize.Height) / 2);
                    int num = 5;
                    switch (base.ImageAlign)
                    {
                        case ContentAlignment.BottomCenter:
                            location = new Point((base.ClientRectangle.Width - iconSize.Width) / 2, base.ClientRectangle.Height - iconSize.Height - num);
                            break;
                        case ContentAlignment.BottomLeft:
                            location = new Point(num, base.ClientRectangle.Height - iconSize.Height - num);
                            break;
                        case ContentAlignment.BottomRight:
                            location = new Point(base.ClientRectangle.Width - iconSize.Width - num, base.ClientRectangle.Height - iconSize.Height - num);
                            break;
                        case ContentAlignment.MiddleCenter:
                            location = new Point((base.ClientRectangle.Width - iconSize.Width) / 2, (base.ClientRectangle.Height - iconSize.Height) / 2);
                            break;
                        case ContentAlignment.MiddleLeft:
                            location = new Point(num, (base.ClientRectangle.Height - iconSize.Height) / 2);
                            break;
                        case ContentAlignment.MiddleRight:
                            location = new Point(base.ClientRectangle.Width - iconSize.Width - num, (base.ClientRectangle.Height - iconSize.Height) / 2);
                            break;
                        case ContentAlignment.TopCenter:
                            location = new Point((base.ClientRectangle.Width - iconSize.Width) / 2, num);
                            break;
                        case ContentAlignment.TopLeft:
                            location = new Point(num, num);
                            break;
                        case ContentAlignment.TopRight:
                            location = new Point(base.ClientRectangle.Width - iconSize.Width - num, num);
                            break;
                    }
                    g.DrawImage((Theme != MetroThemeStyle.Dark) ? (isPressed ? Image : _image) : (isPressed ? _image : Image), new Rectangle(location, iconSize));
                }
            }

            protected override void OnMouseEnter(EventArgs e)
            {
                isHovered = true;
                Invalidate();
                base.OnMouseEnter(e);
            }

            protected override void OnMouseDown(MouseEventArgs e)
            {
                if (e.Button == MouseButtons.Left)
                {
                    isPressed = true;
                    Invalidate();
                }
                base.OnMouseDown(e);
            }

            protected override void OnMouseUp(MouseEventArgs e)
            {
                isPressed = false;
                Invalidate();
                base.OnMouseUp(e);
            }

            protected override void OnMouseLeave(EventArgs e)
            {
                isHovered = false;
                Invalidate();
                base.OnMouseLeave(e);
            }
        }

        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        private MetroStyleManager metroStyleManager;

        private bool useCustomBackColor;

        private bool useCustomForeColor;

        private bool useStyleColors;

        private PromptedTextBox baseTextBox;

        private MetroTextBoxSize metroTextBoxSize;

        private MetroTextBoxWeight metroTextBoxWeight = MetroTextBoxWeight.Regular;

        private Image textBoxIcon;

        private bool textBoxIconRight;

        private bool displayIcon;

        private MetroTextButton _button;

        private bool _showbutton;

        private MetroLink lnkClear;

        private bool _showclear;

        private bool _witherror;

        private bool _cleared;

        private bool _withtext;

        [DefaultValue(MetroColorStyle.Blue)]
        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (base.DesignMode || metroStyle != 0)
                {
                    return metroStyle;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Style;
                }
                if (StyleManager == null)
                {
                    return MetroColorStyle.Blue;
                }
                return metroStyle;
            }
            set
            {
                metroStyle = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (base.DesignMode || metroTheme != 0)
                {
                    return metroTheme;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Theme;
                }
                if (StyleManager == null)
                {
                    return MetroThemeStyle.Light;
                }
                return metroTheme;
            }
            set
            {
                metroTheme = value;
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public MetroStyleManager StyleManager
        {
            get
            {
                return metroStyleManager;
            }
            set
            {
                metroStyleManager = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor
        {
            get
            {
                return useCustomBackColor;
            }
            set
            {
                useCustomBackColor = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomForeColor
        {
            get
            {
                return useCustomForeColor;
            }
            set
            {
                useCustomForeColor = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseStyleColors
        {
            get
            {
                return useStyleColors;
            }
            set
            {
                useStyleColors = value;
            }
        }

        [DefaultValue(false)]
        [Browsable(false)]
        [Category("Metro Behaviour")]
        public bool UseSelectable
        {
            get
            {
                return GetStyle(ControlStyles.Selectable);
            }
            set
            {
                SetStyle(ControlStyles.Selectable, value);
            }
        }

        [DefaultValue(MetroTextBoxSize.Small)]
        [Category("Metro Appearance")]
        public MetroTextBoxSize FontSize
        {
            get
            {
                return metroTextBoxSize;
            }
            set
            {
                metroTextBoxSize = value;
                UpdateBaseTextBox();
            }
        }

        [DefaultValue(MetroTextBoxWeight.Regular)]
        [Category("Metro Appearance")]
        public MetroTextBoxWeight FontWeight
        {
            get
            {
                return metroTextBoxWeight;
            }
            set
            {
                metroTextBoxWeight = value;
                UpdateBaseTextBox();
            }
        }

        [Browsable(true)]
        [EditorBrowsable(EditorBrowsableState.Always)]
        [Obsolete("Use watermark")]
        [Category("Metro Appearance")]
        [DefaultValue("")]
        public string PromptText
        {
            get
            {
                return baseTextBox.WaterMark;
            }
            set
            {
                baseTextBox.WaterMark = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue("")]
        [Browsable(true)]
        [EditorBrowsable(EditorBrowsableState.Always)]
        public string WaterMark
        {
            get
            {
                return baseTextBox.WaterMark;
            }
            set
            {
                baseTextBox.WaterMark = value;
            }
        }

        [Category("Metro Appearance")]
        [EditorBrowsable(EditorBrowsableState.Always)]
        [DefaultValue(null)]
        [Browsable(true)]
        public Image Icon
        {
            get
            {
                return textBoxIcon;
            }
            set
            {
                textBoxIcon = value;
                Refresh();
            }
        }

        [EditorBrowsable(EditorBrowsableState.Always)]
        [DefaultValue(false)]
        [Category("Metro Appearance")]
        [Browsable(true)]
        public bool IconRight
        {
            get
            {
                return textBoxIconRight;
            }
            set
            {
                textBoxIconRight = value;
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        [EditorBrowsable(EditorBrowsableState.Always)]
        [Browsable(true)]
        [DefaultValue(false)]
        public bool DisplayIcon
        {
            get
            {
                return displayIcon;
            }
            set
            {
                displayIcon = value;
                Refresh();
            }
        }

        protected Size iconSize
        {
            get
            {
                if (displayIcon && textBoxIcon != null)
                {
                    int num = (textBoxIcon.Height > base.ClientRectangle.Height) ? base.ClientRectangle.Height : textBoxIcon.Height;
                    Size size = textBoxIcon.Size;
                    double num2 = num / (double)size.Height;
                    new Point(1, 1);
                    return new Size((int)(size.Width * num2), (int)(size.Height * num2));
                }
                return new Size(-1, -1);
            }
        }

        protected int ButtonWidth
        {
            get
            {
                int result = 0;
                if (_button != null)
                {
                    result = (_showbutton ? _button.Width : 0);
                }
                return result;
            }
        }

        [Browsable(true)]
        [DefaultValue(false)]
        [Category("Metro Appearance")]
        [EditorBrowsable(EditorBrowsableState.Always)]
        public bool ShowButton
        {
            get
            {
                return _showbutton;
            }
            set
            {
                _showbutton = value;
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        [Browsable(true)]
        [EditorBrowsable(EditorBrowsableState.Always)]
        public bool ShowClearButton
        {
            get
            {
                return _showclear;
            }
            set
            {
                _showclear = value;
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        [EditorBrowsable(EditorBrowsableState.Always)]
        [DefaultValue(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]
        public MetroTextButton CustomButton
        {
            get
            {
                return _button;
            }
            set
            {
                _button = value;
                Refresh();
            }
        }

        [EditorBrowsable(EditorBrowsableState.Never)]
        [DefaultValue(false)]
        public bool WithError
        {
            get
            {
                return _witherror;
            }
            set
            {
                _witherror = value;
                Invalidate();
            }
        }

        public override ContextMenu ContextMenu
        {
            get
            {
                return baseTextBox.ContextMenu;
            }
            set
            {
                ContextMenu = value;
                baseTextBox.ContextMenu = value;
            }
        }

        public override ContextMenuStrip ContextMenuStrip
        {
            get
            {
                return baseTextBox.ContextMenuStrip;
            }
            set
            {
                ContextMenuStrip = value;
                baseTextBox.ContextMenuStrip = value;
            }
        }

        [DefaultValue(false)]
        public bool Multiline
        {
            get
            {
                return baseTextBox.Multiline;
            }
            set
            {
                baseTextBox.Multiline = value;
            }
        }

        public override string Text
        {
            get
            {
                return baseTextBox.Text;
            }
            set
            {
                baseTextBox.Text = value;
            }
        }

        [Category("Metro Appearance")]
        public Color WaterMarkColor
        {
            get
            {
                return baseTextBox.WaterMarkColor;
            }
            set
            {
                baseTextBox.WaterMarkColor = value;
            }
        }

        [Category("Metro Appearance")]
        public Font WaterMarkFont
        {
            get
            {
                return baseTextBox.WaterMarkFont;
            }
            set
            {
                baseTextBox.WaterMarkFont = value;
            }
        }

        public string[] Lines
        {
            get
            {
                return baseTextBox.Lines;
            }
            set
            {
                baseTextBox.Lines = value;
            }
        }

        [Browsable(false)]
        public string SelectedText
        {
            get
            {
                return baseTextBox.SelectedText;
            }
            set
            {
                baseTextBox.Text = value;
            }
        }

        [DefaultValue(false)]
        public bool ReadOnly
        {
            get
            {
                return baseTextBox.ReadOnly;
            }
            set
            {
                baseTextBox.ReadOnly = value;
            }
        }

        public char PasswordChar
        {
            get
            {
                return baseTextBox.PasswordChar;
            }
            set
            {
                baseTextBox.PasswordChar = value;
            }
        }

        [DefaultValue(false)]
        public bool UseSystemPasswordChar
        {
            get
            {
                return baseTextBox.UseSystemPasswordChar;
            }
            set
            {
                baseTextBox.UseSystemPasswordChar = value;
            }
        }

        [DefaultValue(HorizontalAlignment.Left)]
        public HorizontalAlignment TextAlign
        {
            get
            {
                return baseTextBox.TextAlign;
            }
            set
            {
                baseTextBox.TextAlign = value;
            }
        }

        public int SelectionStart
        {
            get
            {
                return baseTextBox.SelectionStart;
            }
            set
            {
                baseTextBox.SelectionStart = value;
            }
        }

        public int SelectionLength
        {
            get
            {
                return baseTextBox.SelectionLength;
            }
            set
            {
                baseTextBox.SelectionLength = value;
            }
        }

        [DefaultValue(true)]
        public new bool TabStop
        {
            get
            {
                return baseTextBox.TabStop;
            }
            set
            {
                baseTextBox.TabStop = value;
            }
        }

        public int MaxLength
        {
            get
            {
                return baseTextBox.MaxLength;
            }
            set
            {
                baseTextBox.MaxLength = value;
            }
        }

        public ScrollBars ScrollBars
        {
            get
            {
                return baseTextBox.ScrollBars;
            }
            set
            {
                baseTextBox.ScrollBars = value;
            }
        }

        [DefaultValue(AutoCompleteMode.None)]
        public AutoCompleteMode AutoCompleteMode
        {
            get
            {
                return baseTextBox.AutoCompleteMode;
            }
            set
            {
                baseTextBox.AutoCompleteMode = value;
            }
        }

        [DefaultValue(AutoCompleteSource.None)]
        public AutoCompleteSource AutoCompleteSource
        {
            get
            {
                return baseTextBox.AutoCompleteSource;
            }
            set
            {
                baseTextBox.AutoCompleteSource = value;
            }
        }

        public AutoCompleteStringCollection AutoCompleteCustomSource
        {
            get
            {
                return baseTextBox.AutoCompleteCustomSource;
            }
            set
            {
                baseTextBox.AutoCompleteCustomSource = value;
            }
        }

        public bool ShortcutsEnabled
        {
            get
            {
                return baseTextBox.ShortcutsEnabled;
            }
            set
            {
                baseTextBox.ShortcutsEnabled = value;
            }
        }

        [DefaultValue(CharacterCasing.Normal)]
        public CharacterCasing CharacterCasing
        {
            get
            {
                return baseTextBox.CharacterCasing;
            }
            set
            {
                baseTextBox.CharacterCasing = value;
            }
        }

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        public event EventHandler AcceptsTabChanged;

        public event ButClick ButtonClick;

        public event LUClear ClearClicked;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintBackground != null)
            {
                this.CustomPaintBackground(this, e);
            }
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaint != null)
            {
                this.CustomPaint(this, e);
            }
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintForeground != null)
            {
                this.CustomPaintForeground(this, e);
            }
        }

        public MetroTextBox()
        {
            SetStyle(ControlStyles.DoubleBuffer | ControlStyles.OptimizedDoubleBuffer, value: true);
            base.GotFocus += MetroTextBox_GotFocus;
            base.TabStop = false;
            CreateBaseTextBox();
            UpdateBaseTextBox();
            AddEventHandler();
        }

        private void BaseTextBoxAcceptsTabChanged(object sender, EventArgs e)
        {
            if (this.AcceptsTabChanged != null)
            {
                this.AcceptsTabChanged(this, e);
            }
        }

        private void BaseTextBoxSizeChanged(object sender, EventArgs e)
        {
            base.OnSizeChanged(e);
        }

        private void BaseTextBoxCursorChanged(object sender, EventArgs e)
        {
            base.OnCursorChanged(e);
        }

        private void BaseTextBoxContextMenuStripChanged(object sender, EventArgs e)
        {
            base.OnContextMenuStripChanged(e);
        }

        private void BaseTextBoxContextMenuChanged(object sender, EventArgs e)
        {
            base.OnContextMenuChanged(e);
        }

        private void BaseTextBoxClientSizeChanged(object sender, EventArgs e)
        {
            base.OnClientSizeChanged(e);
        }

        private void BaseTextBoxClick(object sender, EventArgs e)
        {
            base.OnClick(e);
        }

        private void BaseTextBoxChangeUiCues(object sender, UICuesEventArgs e)
        {
            base.OnChangeUICues(e);
        }

        private void BaseTextBoxCausesValidationChanged(object sender, EventArgs e)
        {
            base.OnCausesValidationChanged(e);
        }

        private void BaseTextBoxKeyUp(object sender, KeyEventArgs e)
        {
            base.OnKeyUp(e);
        }

        private void BaseTextBoxKeyPress(object sender, KeyPressEventArgs e)
        {
            base.OnKeyPress(e);
        }

        private void BaseTextBoxKeyDown(object sender, KeyEventArgs e)
        {
            base.OnKeyDown(e);
        }

        private void BaseTextBoxTextChanged(object sender, EventArgs e)
        {
            base.OnTextChanged(e);
            if (baseTextBox.Text != "" && !_withtext)
            {
                _withtext = true;
                _cleared = false;
                Invalidate();
            }
            if (baseTextBox.Text == "" && !_cleared)
            {
                _withtext = false;
                _cleared = true;
                Invalidate();
            }
        }

        public void Select(int start, int length)
        {
            baseTextBox.Select(start, length);
        }

        public void SelectAll()
        {
            baseTextBox.SelectAll();
        }

        public void Clear()
        {
            baseTextBox.Clear();
        }

        private void MetroTextBox_GotFocus(object sender, EventArgs e)
        {
            baseTextBox.Focus();
        }

        public void AppendText(string text)
        {
            baseTextBox.AppendText(text);
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                Color color = BackColor;
                baseTextBox.BackColor = color;
                if (!useCustomBackColor)
                {
                    color = MetroPaint.BackColor.Form(Theme);
                    baseTextBox.BackColor = color;
                }
                if (color.A == byte.MaxValue)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint))
                {
                    OnPaintBackground(e);
                }
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            if (useCustomForeColor)
            {
                baseTextBox.ForeColor = ForeColor;
            }
            else
            {
                baseTextBox.ForeColor = MetroPaint.ForeColor.Button.Normal(Theme);
            }
            Color color = MetroPaint.BorderColor.ComboBox.Normal(Theme);
            if (useStyleColors)
            {
                color = MetroPaint.GetStyleColor(Style);
            }
            if (_witherror)
            {
                color = MetroColors.Red;
                if (Style == MetroColorStyle.Red)
                {
                    color = MetroColors.Orange;
                }
            }
            using (Pen pen = new Pen(color))
            {
                e.Graphics.DrawRectangle(pen, new Rectangle(0, 0, base.Width - 2, base.Height - 1));
            }
            DrawIcon(e.Graphics);
        }

        private void DrawIcon(Graphics g)
        {
            if (displayIcon && textBoxIcon != null)
            {
                Point location = new Point(5, 5);
                if (textBoxIconRight)
                {
                    location = new Point(base.ClientRectangle.Width - iconSize.Width - 1, 1);
                }
                g.DrawImage(textBoxIcon, new Rectangle(location, iconSize));
                UpdateBaseTextBox();
            }
            else
            {
                _button.Visible = _showbutton;
                if (_showbutton && _button != null)
                {
                    UpdateBaseTextBox();
                }
            }
            OnCustomPaintForeground(new MetroPaintEventArgs(Color.Empty, baseTextBox.ForeColor, g));
        }

        public override void Refresh()
        {
            base.Refresh();
            UpdateBaseTextBox();
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            UpdateBaseTextBox();
        }

        private void CreateBaseTextBox()
        {
            if (baseTextBox != null)
            {
                return;
            }
            baseTextBox = new PromptedTextBox();
            baseTextBox.BorderStyle = BorderStyle.None;
            baseTextBox.Font = MetroFonts.TextBox(metroTextBoxSize, metroTextBoxWeight);
            baseTextBox.Location = new Point(3, 3);
            baseTextBox.Size = new Size(base.Width - 6, base.Height - 6);
            base.Size = new Size(baseTextBox.Width + 6, baseTextBox.Height + 6);
            baseTextBox.TabStop = true;
            base.Controls.Add(baseTextBox);
            if (_button == null)
            {
                _button = new MetroTextButton();
                _button.Theme = Theme;
                _button.Style = Style;
                _button.Location = new Point(3, 1);
                _button.Size = new Size(base.Height - 4, base.Height - 4);
                _button.TextChanged += _button_TextChanged;
                _button.MouseEnter += _button_MouseEnter;
                _button.MouseLeave += _button_MouseLeave;
                _button.Click += _button_Click;
                if (!base.Controls.Contains(_button))
                {
                    base.Controls.Add(_button);
                }
                if (lnkClear == null)
                {
                    InitializeComponent();
                }
            }
        }

        protected override void OnCreateControl()
        {
            base.OnCreateControl();
        }

        private void _button_Click(object sender, EventArgs e)
        {
            if (this.ButtonClick != null)
            {
                this.ButtonClick(this, e);
            }
        }

        private void _button_MouseLeave(object sender, EventArgs e)
        {
            UseStyleColors = baseTextBox.Focused;
            Invalidate();
        }

        private void _button_MouseEnter(object sender, EventArgs e)
        {
            UseStyleColors = true;
            Invalidate();
        }

        private void _button_TextChanged(object sender, EventArgs e)
        {
            _button.Invalidate();
        }

        private void AddEventHandler()
        {
            baseTextBox.AcceptsTabChanged += BaseTextBoxAcceptsTabChanged;
            baseTextBox.CausesValidationChanged += BaseTextBoxCausesValidationChanged;
            baseTextBox.ChangeUICues += BaseTextBoxChangeUiCues;
            baseTextBox.Click += BaseTextBoxClick;
            baseTextBox.ClientSizeChanged += BaseTextBoxClientSizeChanged;
            baseTextBox.ContextMenuChanged += BaseTextBoxContextMenuChanged;
            baseTextBox.ContextMenuStripChanged += BaseTextBoxContextMenuStripChanged;
            baseTextBox.CursorChanged += BaseTextBoxCursorChanged;
            baseTextBox.KeyDown += BaseTextBoxKeyDown;
            baseTextBox.KeyPress += BaseTextBoxKeyPress;
            baseTextBox.KeyUp += BaseTextBoxKeyUp;
            baseTextBox.SizeChanged += BaseTextBoxSizeChanged;
            baseTextBox.TextChanged += BaseTextBoxTextChanged;
            baseTextBox.GotFocus += baseTextBox_GotFocus;
            baseTextBox.LostFocus += baseTextBox_LostFocus;
        }

        private void baseTextBox_LostFocus(object sender, EventArgs e)
        {
            UseStyleColors = false;
            Invalidate();
            InvokeLostFocus(this, e);
        }

        private void baseTextBox_GotFocus(object sender, EventArgs e)
        {
            _witherror = false;
            UseStyleColors = true;
            Invalidate();
            InvokeGotFocus(this, e);
        }

        private void UpdateBaseTextBox()
        {
            if (_button != null)
            {
                if (base.Height % 2 > 0)
                {
                    _button.Size = new Size(base.Height - 2, base.Height - 2);
                    _button.Location = new Point(base.Width - (_button.Width + 1), 1);
                }
                else
                {
                    _button.Size = new Size(base.Height - 5, base.Height - 5);
                    _button.Location = new Point(base.Width - _button.Width - 3, 2);
                }
                _button.Visible = _showbutton;
            }
            int num = 0;
            if (lnkClear != null)
            {
                lnkClear.Visible = false;
                if (_showclear && Text != "" && !ReadOnly && base.Enabled)
                {
                    num = 16;
                    lnkClear.Location = new Point(base.Width - (ButtonWidth + 17), (base.Height - 14) / 2);
                    lnkClear.Visible = true;
                }
            }
            if (baseTextBox == null)
            {
                return;
            }
            baseTextBox.Font = MetroFonts.TextBox(metroTextBoxSize, metroTextBoxWeight);
            if (displayIcon)
            {
                Point location = new Point(iconSize.Width + 10, 5);
                if (textBoxIconRight)
                {
                    location = new Point(3, 3);
                }
                baseTextBox.Location = location;
                baseTextBox.Size = new Size(base.Width - (20 + ButtonWidth + num) - iconSize.Width, base.Height - 6);
            }
            else
            {
                baseTextBox.Location = new Point(3, 3);
                baseTextBox.Size = new Size(base.Width - (6 + ButtonWidth + num), base.Height - 6);
            }
        }

        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MetroFramework.Controls.MetroTextBox));
            lnkClear = new MetroFramework.Controls.MetroLink();
            SuspendLayout();
            lnkClear.FontSize = MetroFramework.MetroLinkSize.Medium;
            lnkClear.FontWeight = MetroFramework.MetroLinkWeight.Regular;
            lnkClear.Image = (System.Drawing.Image)resources.GetObject("lnkClear.Image");
            lnkClear.ImageSize = 10;
            lnkClear.Location = new System.Drawing.Point(654, 96);
            lnkClear.Name = "lnkClear";
            lnkClear.NoFocusImage = (System.Drawing.Image)resources.GetObject("lnkClear.NoFocusImage");
            lnkClear.Size = new System.Drawing.Size(12, 12);
            lnkClear.TabIndex = 2;
            lnkClear.UseSelectable = true;
            lnkClear.Click += new System.EventHandler(lnkClear_Click);
            ResumeLayout(false);
            base.Controls.Add(lnkClear);
        }

        private void lnkClear_Click(object sender, EventArgs e)
        {
            Focus();
            Clear();
            baseTextBox.Focus();
            if (this.ClearClicked != null)
            {
                this.ClearClicked();
            }
        }
    }
}
