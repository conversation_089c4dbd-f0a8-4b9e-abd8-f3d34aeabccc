using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    internal abstract class ToolObject : Tool
    {
        protected Cursor Cursor { get; set; }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            drawArea.GraphicsList[0].Normalize();
            drawArea.AddCommandToHistory(new CommandAdd(drawArea.GraphicsList[0]));
            drawArea.Refresh();
        }

        protected void AddNewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.Add(o);
            drawArea.Capture = true;
            drawArea.Refresh();
        }

        public DrawObject GetSelected(DrawArea drawArea)
        {
            var count = drawArea.GraphicsList.Count;
            for (var num = count - 1; num >= 0; num--)
            {
                var drawObject = drawArea.GraphicsList[num];
                if (drawObject.Selected) return drawObject;
            }

            return null;
        }

        public void OnCursor(DrawArea drawArea, MouseEventArgs e)
        {
            var point = new Point(e.X, e.Y);
            if (e.Button != 0) return;
            Cursor cursor = null;
            for (var i = 0; i < drawArea.GraphicsList.Count; i++)
            {
                var num = drawArea.GraphicsList[i].HitTest(point);
                if (num > 0)
                {
                    cursor = drawArea.GraphicsList[i].GetHandleCursor(num);
                    break;
                }
            }

            if (cursor == null) cursor = CursorEx.Cross;
            drawArea.Cursor = cursor;
        }

        public Cursor SetCursor(Bitmap cursor, Point hotPoint)
        {
            var x = hotPoint.X;
            var y = hotPoint.Y;
            using (var bitmap = new Bitmap(cursor.Width * 2 - x, cursor.Height * 2 - y))
            {
                var graphics = Graphics.FromImage(bitmap);
                graphics.Clear(Color.FromArgb(0, 0, 0, 0));
                graphics.DrawImage(cursor, cursor.Width - x, cursor.Height - y, cursor.Width, cursor.Height);
                graphics.Dispose();
                return new Cursor(bitmap.GetHicon());
            }
        }
    }
}