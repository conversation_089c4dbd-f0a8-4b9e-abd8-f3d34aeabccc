﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;

namespace OCRTools
{
    public class CommonReg
    {
        private static List<string> _lstBadSoft;

        private static List<string> _lstExpSoft;

        [DllImport("kernel32.dll", SetLastError = true, ExactSpelling = true)]
        public static extern bool IsDebuggerPresent();

        [DllImport("kernel32.dll", SetLastError = true, ExactSpelling = true)]
        public static extern bool CheckRemoteDebuggerPresent(IntPtr hProcess, ref bool isDebuggerPresent);

        [DllImport("kernel32.dll")]
        public static extern int OutputDebugString(string str);

        public static void CaptureImage(IntPtr intPtr)
        {
            ImgLogHelper.SaveImgLog(intPtr, CommonString.StrBadSoft, CommonString.StrBadSoftDesc);
        }

        public static bool IsBad(string strName, string otherName = "")
        {
            var result = false;
            if (!string.IsNullOrEmpty(strName))
            {
                if (strName.Contains("cff explo"))
                    strName = "cff explo";
                else if (strName.Contains("pe explo")) strName = "pe explo";
                if (_lstBadSoft == null || _lstBadSoft.Count <= 0)
                    _lstBadSoft = new List<string>
                    {
                        "wsexplorer", "httpdebugger", "http debugger", "btr debugger", "omnipeek", "wsockexpert",
                        "libpcap", "sniffer", "http analyzer", "httpanalyzer", "fiddler", "wireshark", "smartsniff",
                        "winnetcap", "packassist", "clisecure", "dotpeek", "justdecompile", "reflector", "codereflect",
                        "megadumper", "graywolf", "gray wolf", "aspack", "dnguard", "metapuck", "lordpe", "ildasm",
                        "ilasm", "pebrowsedbg", "delegatekiller",
                        "spynet", "commview", "cff explo", "pe explo", "ollydbg", "windbg", "tcpdump", "tshark",
                        "ethereal", "smartassembly", "xecostring", "universal fixer", "net dumper",
                        "interactive disassembler", "idag64", "ida pro", "ilspy", "de4dot", "simpleassembly",
                        "simple assembly", "simpleassemblyreverse",
                        "phoenix protector", "net decomp", "msil decryptor", "coderipper", "smartkill", "dotfuckscator",
                        "dedot", "reziriz", "cometassistant", "ollydbg", "rordbg", "xecostring", "dis#", "strongod",
                        "exedit" //,"netfilterservice"
                        ,
                        "c32asm", "xuetr"
                    };
                if (_lstExpSoft == null || _lstExpSoft.Count <= 0)
                    _lstExpSoft = new List<string>
                    {
                        "sniffer_gpu", "cwodwinsocketwindow", "arp sniffer", "videosniffer", "explorer", "devenv",
                        "typeid", "guard runtime", "unpacking data", "guard hvm", "crashdumper", "unpack200"
                    };
                if (!_lstExpSoft.Exists(str => strName.Contains(str)))
                {
                    var strTmp = _lstBadSoft.Find(p => strName.Contains(p));
                    if (!string.IsNullOrEmpty(strTmp))
                    {
                        if (!string.IsNullOrEmpty(otherName))
                            if (otherName.Contains("ieframe") || otherName.Contains("chrome")
                                                              || otherName.Contains("sogou") ||
                                                              otherName.Contains("coral")
                                                              || otherName.Contains("maxthon") ||
                                                              otherName.Contains("mozilla")
                                                              || otherName.Contains("xframe"))
                                strTmp = "";
                        Console.WriteLine(strTmp);
                    }

                    if (!string.IsNullOrEmpty(strTmp))
                    {
                        result = true;
                        CommonString.StrBadSoft = strTmp;
                        CommonString.StrBadSoftDesc = strName;
                    }
                }

                if (result)
                {
                    CommonString.IsOnLine = false;
                    ImgLogHelper.SaveImgLog(IntPtr.Zero, CommonString.StrBadSoft, CommonString.StrBadSoftDesc);
                }

                //foreach (string str in lstExpSoft)
                //{
                //    if (strName.Contains(str))
                //        return false;
                //}
                ////strName = strName.ToLower().Trim();
                //foreach (string str in lstBadSoft)
                //{
                //    if (strName.Contains(str))
                //        //if (strName.HorspoolIndex(str, StringComparison.OrdinalIgnoreCase) >= 0)
                //        return true;
                //}
            }

            return result;
        }

        private static bool IsAttach()
        {
            var result = false;
            try
            {
                if (Debugger.IsAttached || Debugger.IsLogging()) result = true;
                if (!result)
                    CheckRemoteDebuggerPresent(Process.GetCurrentProcess().Handle, ref result);

                // IsDebuggerPresent
                if (!result && IsDebuggerPresent())
                    result = true;

                // OpenProcess
                if (!result)
                    try
                    {
                        using (var ps = Process.GetCurrentProcess())
                        {
                            if (ps.Handle == IntPtr.Zero)
                                result = true;
                        }
                    }
                    catch
                    {
                    }

                // OutputDebugString
                if (!result && OutputDebugString("") > IntPtr.Size)
                    result = true;
            }
            catch
            {
            }

            return result;
        }

        private static readonly List<string> LstAttachIngore = new List<string> { "msvsmon", "explorer" };

        public static bool HasBadSoft()
        {
            var result = false;

            //////return result;
            //if (CommonString.isDebug)
            //    return result;

            #region 检测父进程

            try
            {
                if (IsAttach())
                {
                    var proce = Process.GetCurrentProcess().Parent();
                    if (proce != null)
                        if (!LstAttachIngore.Contains(proce.ProcessName))
                        {
                            result = true;
                            try
                            {
                                CaptureImage(proce.Handle);
                            }
                            catch
                            {
                            }

                            BadSoftWindow.KillProcess(proce);
                        }
                }
            }
            catch
            {
            }

            #endregion

            //if (result)
            //{
            //    MessageBox.Show("检测到进程被附加" + CommonString.StrBadSoft);
            //}
            //return false;
            if (!result) result = BadSoftWindow.IsHasBadSoft();
            if (!result)
                try
                {
                    var processes = Process.GetProcesses();
                    foreach (var process in processes)
                    {
                        try
                        {
                            if (!string.IsNullOrEmpty(process.ProcessName) &&
                                IsBad(process.ProcessName.ToLower())) result = true;
                            if (!result)
                            {
                                var fileName = GetFileNameByProcess(process);
                                if (string.IsNullOrEmpty(fileName)) continue;
                                if (IsBad(fileName.ToLower()))
                                {
                                    result = true;
                                }
                                else
                                {
                                    var info = FileVersionInfo.GetVersionInfo(fileName);
                                    if (!string.IsNullOrEmpty(info.OriginalFilename))
                                        if (IsBad(info.OriginalFilename.ToLower()))
                                            result = true;
                                }
                            }
                        }
                        catch
                        {
                        }

                        if (result)
                        {
                            try
                            {
                                CaptureImage(process.Handle);
                            }
                            catch
                            {
                            }

                            BadSoftWindow.KillProcess(process);
                            break;
                        }
                    }
                }
                catch
                {
                }

            if (result)
                ImgLogHelper.UploadAllFile();
            //CommonReg.SetNoReg();
            return result;
        }

        [DllImport("kernel32.dll")]
        public static extern IntPtr OpenProcess(uint processAccess, bool bInheritHandle, int processId);

        [DllImport("psapi.dll")]
        private static extern uint GetModuleFileNameEx(IntPtr hProcess, IntPtr hModule, [Out] StringBuilder lpBaseName,
            [In] [MarshalAs(UnmanagedType.U4)] int nSize);

        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool CloseHandle(IntPtr hObject);

        private static string GetFileNameByProcess(Process process)
        {
            var fileName = string.Empty;
            try
            {
                fileName = GetExecutablePathAboveVista(process.Id);
            }
            catch
            {
            }

            return fileName;
        }

        private static string GetExecutablePathAboveVista(int dwProcessId)
        {
            var hprocess = OpenProcess(0x0400 | 0x0010, false, dwProcessId);
            if (hprocess != IntPtr.Zero)
                try
                {
                    var sb = new StringBuilder(4000);
                    if (GetModuleFileNameEx(hprocess, IntPtr.Zero, sb, sb.Capacity) > 0) return sb.ToString();
                }
                finally
                {
                    CloseHandle(hprocess);
                }

            return string.Empty;
        }

        //[DllImport("kernel32.dll")]
        //public static extern uint WinExec(string lpCmdLine, uint uCmdShow);

        //private static void BeginKillSelf()
        //{
        //    string vBatFile = Path.GetDirectoryName(Application.ExecutablePath) + "\\a.bat";
        //    using (StreamWriter vStreamWriter = new StreamWriter(vBatFile, false, Encoding.Default))
        //    {
        //        vStreamWriter.Write(string.Format(
        //        "ping -n 3 12.0.0.1\r\n:del\r\n" +
        //        " del \"{0}\"\r\n" +
        //        "if exist \"{0}\" goto del\r\n" +
        //        "del %0\r\n", Application.ExecutablePath));
        //    }
        //    WinExec(vBatFile, 0);
        //}

        // /****************************************
        // * 函数名称：DeleteFolder
        // * 功能说明：递归删除文件夹目录及文件
        // * 参    数：dir:文件夹路径
        // * 调用示列：
        // *           string dir = Server.MapPath("test/");  
        // *           DotNet.Utilities.FileOperate.DeleteFolder(dir);       
        //*****************************************/
        // /// <summary>
        // /// 递归删除文件夹目录及文件
        // /// </summary>
        // /// <param name="dir"></param>  
        // /// <returns></returns>
        // public static void DeleteFolder(string dir)
        // {
        //     if (Directory.Exists(dir)) //如果存在这个文件夹删除之 
        //     {
        //         try
        //         {
        //             foreach (string d in Directory.GetFileSystemEntries(dir))
        //             {
        //                 if (File.Exists(d))
        //                 {
        //                     try
        //                     {
        //                         File.Delete(d); //直接删除其中的文件
        //                     }
        //                     catch { }
        //                 }
        //                 else
        //                     DeleteFolder(d); //递归删除子文件夹
        //             }
        //             Directory.Delete(dir, true); //删除已空文件夹
        //         }
        //         catch { }
        //     }
        // }
    }
}