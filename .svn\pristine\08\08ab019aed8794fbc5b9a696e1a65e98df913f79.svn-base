﻿using OCRTools.Language;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace OCRTools
{
    public class CommonUser
    {
        private static List<UserType> LstUserType { get; set; } = new List<UserType>();

        public static List<UserType> GetUserTypes(bool isRefresh)
        {
            if (isRefresh || LstUserType == null || LstUserType.Count <= 0)
            {
                var lstTmp = OcrHelper.GetCanRegUserTypes()?.OrderBy(p => p.Type).ToList();
                if (lstTmp?.Count > 0)
                {
                    LstUserType = lstTmp;
                }
            }

            return LstUserType;
        }

        public static UserTypeInfo GetNextType()
        {
            GetUserTypes(false);
            UserType nNextType = null;
            try
            {
                nNextType = !Program.IsLogined() ? LstUserType.FirstOrDefault() : LstUserType.FirstOrDefault(p => p.Type > Program.NowUser.UserType);
            }
            catch (Exception oe)
            {
                Log.WriteError("GetNextType", oe);
            }
            return new UserTypeInfo
            {
                Name = nNextType == null ? "敬请期待".CurrentText() : "升级到".CurrentText() + " " + nNextType.Name.CurrentText(),
                Code = nNextType?.Type ?? 0
            };
        }

        public static void UpdateLimitInfo()
        {
            var count = OcrHelper.GetCodeCount();
            if (count != null && !string.IsNullOrEmpty(count.Account))
            {
                CommonString.TodayCount = count.TodayCount;
                CommonString.LimitCount = count.LimitCount;
            }
        }

        public static string GetTodayLimitInfo()
        {
            return string.Format("{0}/{1}"
                , CommonString.TodayCount > 0 ? CommonString.TodayCount.ToString() : "-"
                , CommonString.LimitCount > 0 ? CommonString.LimitCount.ToString() : "-"
                );
        }
    }

    [Obfuscation]
    public class UserCodeCount
    {
        [Obfuscation]
        public string Account { get; set; }

        [Obfuscation]
        public long TodayCount { get; set; }

        [Obfuscation]
        public long LimitCount { get; set; }
    }
}
