using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using OCRTools.Common;

namespace OCRTools
{
    public enum CaptureResult
    {
        Close,
        Region,
        Image
    }

    internal class DrawArea : Form
    {
        private readonly RectangleAnimation _regionAnimation;
        private readonly IContainer components = null;

        private readonly UndoManager undoManager;

        private PictureBox _arrowBoth;

        private Pen _borderDotPen;

        private PictureBox _drawArrow;

        private PictureBox _drawCopy;

        private PictureBox _drawGaus;

        private PictureBox _drawHighlight;

        private PictureBox _drawMosaic;
        public DrawToolType activeTool;

        private string currentcolor;

        private PictureBox DrawChanel;

        private PictureBox DrawEllipse;

        private PictureBox DrawLine;

        private PictureBox DrawPaste;

        public PictureBox DrawPolygon;

        public PictureBox DrawRectangle;

        public PictureBox DrawRectangleFill;

        private PictureBox DrawRedo;

        private PictureBox DrawSave;

        private PictureBox DrawStep;

        private PictureBox DrawText;

        private PictureBox DrawUndo;

        private PictureBox Ellipse1;

        private PictureBox Ellipse2;

        private PictureBox Ellipse3;

        private PictureBox Ellipse4;

        private PictureBox EllipseDot;

        private ListButton fontsizeButton;

        private ListButton fontstyleButton;

        public bool IsArrowBoth;

        public bool isAutoDraw = true;

        public bool IsDot;

        public bool IsOutLine;

        public bool isShowZoom = true;

        private PictureBox LineDot;

        public Panel multiBtn;

        private SelectedModel nowSelectedModel;

        public List<int> NumberArry;

        private PictureBox P2_DrawChanel;

        private PictureBox P2_DrawSave;

        private PictureBox Rectangle1;

        private PictureBox Rectangle2;

        private PictureBox Rectangle3;

        private PictureBox Rectangle4;

        private PictureBox Rectangle5;

        private PictureBox Rectangle6;

        private PictureBox Rectangle7;

        private PictureBox RectangleCus;

        private PictureBox RectangleDot;

        public Rectangle RectangleMove;

        private SaveFileDialog saveFileDialog1;

        private PictureBox ShowCross;

        public int StepNum;

        public Status Tempstatus = new Status();

        public AutoSizeTextBox tempTextBox;

        private PictureBox TextDot;

        private Stopwatch timerStart;

        public Tool[] tools;

        public Panel ToolsBtn;

        public Panel toolspanel;

        public UpDownButtonEx upDownButtonEx;

        public DrawArea(bool isInitWindows = true)
        {
            CustomColor.Custom = Color.Transparent;
            StaticValue.IsShowText = false;
            _regionAnimation = new RectangleAnimation
            {
                Duration = TimeSpan.FromMilliseconds(120)
            };
            Tempstatus.LastUsedColor = DrawObject.LastUsedColor;
            Tempstatus.LastUsedPenWidth = DrawObject.LastUsedPenWidth;
            Tempstatus.LastIsDot = DrawObject.LastIsDot;
            Tempstatus.LastIsOutline = DrawObject.LastIsOutline;
            Tempstatus.LastIsArrowBoth = DrawObject.LastIsArrowBoth;
            Tempstatus.LastFontstyle = DrawObject.LastFontstyle;
            Tempstatus.LastFontSize = DrawObject.LastFontSize;
            Tempstatus.ActiveTools = StaticValue.CurrentToolType;
            NumberArry = new List<int>();
            for (var i = 1; i <= 99; i++) NumberArry.Add(i);
            DrawObject.LastUsedColor = CustomColor.red;
            DrawObject.LastUsedPenWidth = 2;
            DrawObject.LastIsDot = false;
            DrawObject.LastIsOutline = false;
            DrawObject.LastIsArrowBoth = false;
            StaticValue.IsCatchScreen = true;
            Console.WriteLine("Init isCatchScreen:" + StaticValue.IsCatchScreen);
            InitializeComponent();
            GraphicsList = new GraphicsList();
            undoManager = new UndoManager(GraphicsList);
            tools = new Tool[15];
            tools[0] = new ToolCatch();
            tools[2] = new ToolMultiCatch();
            tools[1] = new ToolQuickCatch();
            tools[3] = new ToolPointer();
            tools[4] = new ToolRectangle();
            tools[5] = new ToolEllipse();
            tools[6] = new ToolLine();
            tools[7] = new ToolText();
            tools[8] = new ToolArrow();
            tools[9] = new ToolStep();
            tools[10] = new ToolMosaic();
            tools[13] = new ToolHighlight();
            tools[14] = new ToolRectangleFill();
            tools[11] = new ToolGaus();
            tools[12] = new ToolPolygon();
            AutoScaleDimensions = new SizeF(9f, 18f);
            Font = new Font("微软雅黑", 10f);
            AutoScaleMode = AutoScaleMode.Font;
            BackgroundImageLayout = ImageLayout.None;
            ControlBox = false;
            ShowInTaskbar = false;
            FormBorderStyle = FormBorderStyle.None;
            StartPosition = FormStartPosition.Manual;
            if (!IsWindows)
            {
                var rectangle = Rectangle.Empty;
                BackgroundImageEx = new Screenshot().CaptureFullscreen(ref rectangle);
                Size = rectangle.Size;
                Location = rectangle.Location;
                IsSmallControlModel = CommonSetting.自动检测窗口元素;
                Windows = new ConcurrentBag<WindowInfo>();
                if (isInitWindows && CommonSetting.自动检测窗口) InitWindows();
            }

            FormClosing += delegate
            {
                DrawObject.LastUsedColor = Tempstatus.LastUsedColor;
                DrawObject.LastUsedPenWidth = Tempstatus.LastUsedPenWidth;
                DrawObject.LastIsDot = Tempstatus.LastIsDot;
                DrawObject.LastIsOutline = Tempstatus.LastIsOutline;
                DrawObject.LastIsArrowBoth = Tempstatus.LastIsArrowBoth;
                DrawObject.LastFontSize = Tempstatus.LastFontSize;
                DrawObject.LastFontstyle = Tempstatus.LastFontstyle;
            };
            Disposed += delegate { GC.Collect(); };
            fontstyleButton.ListItems = new[]
            {
                "宋体",
                "黑体",
                "楷体",
                "幼圆",
                "微软雅黑"
            };
            fontstyleButton.Islist = true;
            fontsizeButton.ListItems = new[]
            {
                "12pt",
                "14pt",
                "16pt",
                "18pt",
                "20pt",
                "22pt",
                "24pt"
            };
            fontsizeButton.Islist = true;
            fontstyleButton.TextChanged += delegate
            {
                switch (fontstyleButton.Text)
                {
                    case "宋体":
                        DrawObject.LastFontstyle = "宋体";
                        break;
                    case "黑体":
                        DrawObject.LastFontstyle = "黑体";
                        break;
                    case "楷体":
                        DrawObject.LastFontstyle = "楷体";
                        break;
                    case "幼圆":
                        DrawObject.LastFontstyle = "幼圆";
                        break;
                    case "微软雅黑":
                        DrawObject.LastFontstyle = "微软雅黑";
                        break;
                }

                RefreshDraw();
            };
            fontsizeButton.TextChanged += delegate
            {
                switch (fontsizeButton.Text)
                {
                    case "12pt":
                        DrawObject.LastFontSize = 12f;
                        break;
                    case "14pt":
                        DrawObject.LastFontSize = 14f;
                        break;
                    case "16pt":
                        DrawObject.LastFontSize = 16f;
                        break;
                    case "18pt":
                        DrawObject.LastFontSize = 18f;
                        break;
                    case "20pt":
                        DrawObject.LastFontSize = 20f;
                        break;
                    case "22pt":
                        DrawObject.LastFontSize = 22f;
                        break;
                    case "24pt":
                        DrawObject.LastFontSize = 24f;
                        break;
                }

                RefreshDraw();
            };
            upDownButtonEx.TextChanged += delegate
            {
                var selected = GetSelected(this);
                if (selected != null)
                {
                    selected.Text = upDownButtonEx.Text;
                    Refresh();
                }
            };
            upDownButtonEx.TextMouseLeave += delegate { ActiveControl = null; };
        }

        public CaptureResult Result { get; set; }

        protected override CreateParams CreateParams
        {
            get
            {
                var createParams = base.CreateParams;
                createParams.ExStyle |= 33554432;
                return createParams;
            }
        }

        public bool IsWindows { get; set; }

        public bool IsEdit { get; set; }

        public bool IsMulti { get; set; }

        public ConcurrentBag<WindowInfo> Windows { get; set; }

        public DrawToolType ActiveTool
        {
            get => activeTool;
            set => activeTool = value;
        }

        public GraphicsList GraphicsList { get; set; }

        public DrawObject Catch { get; set; }

        public string Status { get; set; }

        public bool CanUndo
        {
            get
            {
                if (undoManager != null) return undoManager.CanUndo;
                return false;
            }
        }

        public bool CanRedo
        {
            get
            {
                if (undoManager != null) return undoManager.CanRedo;
                return false;
            }
        }

        public bool IsShowCross { get; set; }

        public bool IsSmallControlModel { get; set; }

        public bool UnshowZoom { get; set; } = false;

        public bool TempshowZoom { get; set; }

        public Rectangle AutoRect { get; set; }

        public Point CurrentPosition { get; set; }

        public Color CurrentColor
        {
            get
            {
                var point = AutoSelectApi.ScreenToClient(MousePosition);
                if (point.X.IsBetween(0, BackgroundImageEx.Width - 1) &&
                    point.Y.IsBetween(0, BackgroundImageEx.Height - 1))
                    return BackgroundImageEx.GetPixel(point.X, point.Y);
                return Color.Empty;
            }
        }

        public bool IsRgb { get; set; } = true;

        public Color CurrentColorEx
        {
            get
            {
                var bitmap = (Bitmap) BackgroundImageEx.Clone();
                if (GraphicsList != null)
                    using (var g = Graphics.FromImage(bitmap))
                    {
                        GraphicsList.OutDraw(g);
                    }

                var point = AutoSelectApi.ScreenToClient(MousePosition);
                if (point.X.IsBetween(0, bitmap.Width - 1) && point.Y.IsBetween(0, bitmap.Height - 1))
                    return bitmap.GetPixel(point.X, point.Y);
                return Color.Empty;
            }
        }

        public Bitmap BackgroundImageEx { get; set; }

        private void InitWindows()
        {
            var selectRectangleList = new SelectRectangleList
            {
                IgnoreHandle = Handle
            };
            Windows = selectRectangleList.GetWindowInfoListAsync(5000);

            if (CommonSetting.自动检测窗口元素)
                new Thread(() =>
                {
                    selectRectangleList.SetWindowZOrder();
                    Parallel.Invoke(() =>
                    {
                        Parallel.ForEach(Windows.Where(p => p.IsWindow).OrderByDescending(p => p.ZIndex),
                            new ParallelOptions {MaxDegreeOfParallelism = 5}, windowInfo =>
                            {
                                var lstTmp = windowInfo.InitChildHandleByWin32();
                                ProcessWindowList(lstTmp);
                            });
                    }, () =>
                    {
                        Parallel.ForEach(Windows.Where(p => p.IsWindow).OrderByDescending(p => p.ZIndex),
                            new ParallelOptions {MaxDegreeOfParallelism = 5}, windowInfo =>
                            {
                                var lstTmp = windowInfo.InitChildHandle();
                                ProcessWindowList(lstTmp);
                            });
                    }, () =>
                    {
                        Parallel.ForEach(Windows.Where(p => p.IsWindow).OrderByDescending(p => p.ZIndex),
                            new ParallelOptions {MaxDegreeOfParallelism = 5}, windowInfo =>
                            {
                                var lstTmp = windowInfo.InitChildHandle(true);
                                ProcessWindowList(lstTmp);
                            });
                    });
                }).Start();
        }

        private void ProcessWindowList(List<WindowInfo> lstTmp)
        {
            if (lstTmp.Count <= 0) return;
            lstTmp.ForEach(p =>
            {
                if (!Windows.Any(q => q.Rectangle.Equals(p.Rectangle))) Windows.Add(p);
            });
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
        }

        private void RefreshDraw()
        {
            if (GraphicsList.ShowPropertiesDialog(this))
            {
                var selected = GetSelected(this);
                if (selected != null) selected.IsChange = true;
                Refresh();
            }
        }

        public void Prepare()
        {
            if (IsEdit)
            {
                ActiveTool = DrawToolType.Catch;
            }
            else
            {
                Cursor = IsShowCross ? CursorEx.None : CursorEx.Cross;
                ActiveTool = DrawToolType.QuickCatch;
            }

            if (IsMulti)
            {
                ActiveTool = DrawToolType.MultiCatch;
                var color = Color.FromArgb(120, 120, 120);
                P2_DrawChanel.Image = ImageHelp.CreateClose(color);
                new FmTip(P2_DrawChanel);
                P2_DrawSave.Image = ImageHelp.CreateSave(color);
                new FmTip(P2_DrawSave);
            }
        }

        public void AddCommandToHistory(Command command)
        {
            undoManager.AddCommandToHistory(command);
        }

        public void Undo()
        {
            undoManager.Undo();
            GraphicsList.UnselectAll();
            foreach (var graphics in GraphicsList.graphicsList)
                if (graphics.NoteType == DrawToolType.Mosaic || graphics.NoteType == DrawToolType.Gaus ||
                    graphics.NoteType == DrawToolType.Text || graphics.NoteType == DrawToolType.Highlight)
                {
                    graphics.LastRect = new Rectangle(0, 0, 0, 0);
                    graphics.IsCache = true;
                }

            var color = Color.FromArgb(220, 220, 220);
            if (CanUndo)
            {
                color = Color.FromArgb(120, 120, 120);
                DrawUndo.Image = ImageHelp.CreateUndo(color);
            }
            else
            {
                DrawUndo.Image = ImageHelp.CreateUndo(color);
            }

            Refresh();
        }

        public void Redo()
        {
            undoManager.Redo();
            GraphicsList.UnselectAll();
            foreach (var graphics in GraphicsList.graphicsList)
                if (graphics.NoteType == DrawToolType.Mosaic || graphics.NoteType == DrawToolType.Gaus ||
                    graphics.NoteType == DrawToolType.Text || graphics.NoteType == DrawToolType.Highlight)
                {
                    graphics.LastRect = new Rectangle(0, 0, 0, 0);
                    graphics.IsCache = true;
                }

            var color = Color.FromArgb(220, 220, 220);
            if (CanRedo)
            {
                color = Color.FromArgb(120, 120, 120);
                DrawRedo.Image = ImageHelp.CreateRedo(color);
            }
            else
            {
                DrawRedo.Image = ImageHelp.CreateRedo(color);
            }

            Refresh();
        }

        public bool IsAnyModifierPressed(KeyModifiers modifiers)
        {
            var flag = false;
            if ((modifiers & KeyModifiers.Shift) != 0) flag |= (ModifierKeys & Keys.Shift) != 0;
            if ((modifiers & KeyModifiers.Alt) != 0) flag |= (ModifierKeys & Keys.Alt) != 0;
            if ((modifiers & KeyModifiers.Ctrl) != 0) flag |= (ModifierKeys & Keys.Control) != 0;
            return flag;
        }

        private void OnContextMenu(MouseEventArgs e)
        {
            var point = new Point(e.X, e.Y);
            var count = GraphicsList.Count;
            DrawObject drawObject = null;
            for (var i = 0; i < count; i++)
                if (GraphicsList[i].HitTest(point) == 0)
                {
                    drawObject = GraphicsList[i];
                    break;
                }

            if (drawObject != null)
            {
                if (!drawObject.Selected) GraphicsList.UnselectAll();
                drawObject.Selected = true;
            }
            else
            {
                GraphicsList.UnselectAll();
            }

            Refresh();
        }

        public override void Refresh()
        {
            Invalidate(true);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (IsWindows)
                {
                    var toolText = (ToolText) tools[7];
                    toolText.Change();
                }

                if (BackgroundImageEx != null)
                    BitDrawImage.DrawImage(BackgroundImageEx, e.Graphics);
                //GC.Collect();
                Update(this);
                if (!IsWindows)
                {
                    FoundRect();
                    if (isAutoDraw && AutoRect.Width != 0 && !IsMulti)
                    {
                        DrawAreaSelect(e.Graphics);
                        DrawAreaText(e.Graphics);
                    }
                }

                if (IsMulti)
                {
                    e.Graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    StaticValue.GraphicsPath = new GraphicsPath
                    {
                        FillMode = FillMode.Alternate
                    };
                }

                if (IsShowCross)
                {
                    e.Graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    DrawHelp.DrawCrosshair(e.Graphics, this);
                }

                GraphicsList?.Draw(e.Graphics);
                if (!IsMulti) goto IL_023f;
                using (var pen = new Pen(CustomColor.CheckColor, 1f))
                {
                    using (var brush = CreateBackBrush())
                    {
                        e.Graphics.DrawPath(pen, StaticValue.GraphicsPath);
                        StaticValue.GraphicsPath.AddRectangle(BaseVirtualScreen());
                        e.Graphics.FillPath(brush, StaticValue.GraphicsPath);
                    }
                }

                StaticValue.GraphicsPath.Dispose();
                var selected = GetSelected(this);
                if (selected != null)
                {
                    for (var i = 1; i <= selected.HandleCount; i++)
                    {
                        e.Graphics.SmoothingMode = SmoothingMode.HighSpeed;
                        using (Brush brush2 = new SolidBrush(CustomColor.CheckColor))
                        {
                            e.Graphics.FillRectangle(brush2, selected.GetHandleRectangle2(i));
                        }
                    }

                    goto IL_023f;
                }

                goto end_IL_0001;
                IL_023f:
                if (TempshowZoom)
                    DrawCursorGraphics(e.Graphics, true);
                else if (isShowZoom && !IsWindows && !UnshowZoom)
                    DrawCursorGraphics(e.Graphics);
                end_IL_0001: ;
            }
            catch
            {
            }
        }

        public Rectangle GetLine()
        {
            using (var graphicsPath = CreateLinePath())
            {
                var bounds = graphicsPath.GetBounds();
                bounds.Inflate(1, 1);
                return Rectangle.Ceiling(bounds);
            }
        }

        private GraphicsPath CreateLinePath()
        {
            var graphicsPath = new GraphicsPath();
            var pt = new Point(0, CurrentPosition.Y);
            var pt2 = new Point(CurrentPosition.X, 0);
            var pt3 = new Point(Width, CurrentPosition.Y);
            var pt4 = new Point(CurrentPosition.X, Height);
            graphicsPath.AddLine(pt, pt3);
            graphicsPath.AddLine(pt2, pt4);
            return graphicsPath;
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            if (NativeMethods.GetForegroundWindow() != Handle) NativeMethods.SetForegroundWindow(Handle);
            if (TempshowZoom)
            {
                Cursor = CursorEx.Cross;
                return;
            }

            var @catch = GetCatch();
            if (e.Button != MouseButtons.Left && e.Button != 0) return;
            if (!IsShowCross && isAutoDraw) Refresh();
            if (StaticValue.IsShowText)
            {
                Cursor = CursorEx.Cross;
                return;
            }

            tools[(int) activeTool].OnMouseMove(this, e);
            if (IsShowCross)
                using (new AutomaticCanvasRefresher(this, GetLine))
                {
                }

            var count = GraphicsList.Count;
            for (var i = 0; i < count; i++)
                if (GraphicsList[i].PointInObject(e.Location))
                {
                    if (Cursor == CursorEx.Cross && e.Button != MouseButtons.Left) Cursor = CursorEx.Move;
                }
                else
                {
                    Cursor = Cursor;
                }

            var point = new Point(e.X, e.Y);
            if (@catch != null && e.Button == MouseButtons.None)
            {
                var num = @catch.HitCatch(point);
                if (num > 0) Cursor = @catch.GetHandleCursor(num);
            }
        }

        private void DrawArea_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (e.Button != MouseButtons.Left) return;
            var selected = GetSelected(this);
            if (selected?.Rectangle.Contains(e.Location) ?? false)
            {
                if (selected.Rectangle.IsLimt())
                {
                    if (StaticValue.CurrentToolType == DrawToolType.Text)
                        tools[7].MouseDoubleClick(this, e);
                    else if (selected.NoteType == DrawToolType.Catch) tools[0].MouseDoubleClick(this, e);
                }
            }
            else if (!IsWindows)
            {
                Copy();
            }
        }

        private void DrawArea_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                if (IsWindows) ActiveTool = DrawToolType.Text;
                if (TempshowZoom)
                {
                    if (Equals(Status, "取色器"))
                    {
                        DialogResult = e.Button == MouseButtons.Left ? DialogResult.OK : DialogResult.Cancel;
                        Close();
                    }

                    if (e.Button == MouseButtons.Left)
                    {
                        var tmpColor = CurrentColorEx;
                        try
                        {
                            var count = GraphicsList.Count;
                            for (var num = count - 1; num >= 0; num--)
                            {
                                var drawObject = GraphicsList[num];
                                if (drawObject.Selected)
                                {
                                    drawObject.Color = tmpColor;
                                    drawObject.IsCache = true;
                                    drawObject.IsChange = true;
                                    drawObject.IsChangeColor = true;
                                }
                            }
                        }
                        catch
                        {
                        }

                        Refresh();
                        InitImageEditRectangle();
                        CustomColor.Custom = tmpColor;
                        RectangleCus.Image = ImageHelp.EditRectangle(CustomColor.Custom, true);
                        DrawObject.LastUsedColor = CustomColor.Custom;
                    }
                }
                else
                {
                    var objSelected = GetSelected(this);
                    if (objSelected != null && StaticValue.IsShowText)
                    {
                        StaticValue.IsShowText = false;
                        objSelected.Text = tempTextBox.Text;
                        objSelected.Font = tempTextBox.Font;
                        Refresh();
                        Controls.Remove(tempTextBox);
                    }

                    if (e.Button == MouseButtons.Left)
                    {
                        tools[(int) activeTool].OnMouseDown(this, e);
                        if (objSelected != null)
                        {
                            if (objSelected.NoteType == DrawToolType.Step)
                            {
                                StepNum = int.Parse(objSelected.Text);
                                upDownButtonEx.Text = objSelected.Text;
                            }

                            SetImage(objSelected);
                        }
                    }

                    if (objSelected != null)
                    {
                        var array = new[]
                        {
                            CustomColor.red,
                            CustomColor.yellow,
                            CustomColor.blue,
                            CustomColor.Green,
                            CustomColor.yellow,
                            CustomColor.black,
                            CustomColor.gray
                        };
                        DrawObject.LastUsedColor = objSelected.Color;
                        DrawObject.LastUsedPenWidth = objSelected.PenWidth;
                        DrawObject.LastIsDot = objSelected.IsDot;
                        DrawObject.LastIsOutline = objSelected.IsOutline;
                        DrawObject.LastIsArrowBoth = objSelected.IsArrowBoth;
                        DrawObject.LastFontstyle = objSelected.Fontstyle;
                        DrawObject.LastFontSize = objSelected.FontSize;
                        fontstyleButton.Text = DrawObject.LastFontstyle;
                        fontsizeButton.Text = DrawObject.LastFontSize + "pt";
                        Not_Selected(objSelected);
                        switch (objSelected.PenWidth)
                        {
                            case StaticValue.PenWidth1:
                                Ellipse_Selected(Ellipse1);
                                break;
                            case StaticValue.PenWidth2:
                                Ellipse_Selected(Ellipse2);
                                break;
                            case StaticValue.PenWidth3:
                                Ellipse_Selected(Ellipse3);
                                break;
                            case StaticValue.PenWidth4:
                                Ellipse_Selected(Ellipse4);
                                break;
                        }

                        if (objSelected.Color == array[0])
                            Rectangle_Selected(Rectangle1);
                        else if (objSelected.Color == array[1])
                            Rectangle_Selected(Rectangle2);
                        else if (objSelected.Color == array[2])
                            Rectangle_Selected(Rectangle3);
                        else if (objSelected.Color == array[3])
                            Rectangle_Selected(Rectangle4);
                        else if (objSelected.Color == array[4])
                            Rectangle_Selected(Rectangle5);
                        else if (objSelected.Color == array[5])
                            Rectangle_Selected(Rectangle6);
                        else if (objSelected.Color == array[6]) Rectangle_Selected(Rectangle7);
                    }

                    if (!IsWindows) panelSize();
                }
            }
            catch
            {
            }
        }

        public void RefreshCanvas(Rectangle clipRectangle)
        {
            clipRectangle.Inflate(1, 1);
            Invalidate(clipRectangle);
        }

        public DrawObject GetSelected(DrawArea drawArea)
        {
            var count = drawArea.GraphicsList.Count;
            for (var num = count - 1; num >= 0; num--)
            {
                var drawObject = drawArea.GraphicsList[num];
                if (drawObject.Selected) return drawObject;
            }

            return null;
        }

        public DrawObject GetCatch()
        {
            var count = GraphicsList.Count;
            for (var num = count - 1; num >= 0; num--)
            {
                var drawObject = GraphicsList[num];
                if (drawObject.NoteType == DrawToolType.Catch) return drawObject;
            }

            return null;
        }

        private void DrawArea_MouseMove(object sender, MouseEventArgs e)
        {
        }

        public void FoundRect()
        {
            var windowInfo = Windows?.Count > 0 ? FindSelectedWindow(MousePosition) : null;
            nowSelectedModel = windowInfo != null
                ? windowInfo.IsSmallControl ? SelectedModel.控件 : SelectedModel.窗口
                : SelectedModel.全屏;
            if (windowInfo != null && !windowInfo.Rectangle.IsEmpty)
            {
                var b = AutoSelectApi.ScreenToClient(windowInfo.Rectangle);
                AutoRect = Rectangle.Intersect(BaseVirtualScreen(), b);
            }
            else
            {
                AutoRect = BaseVirtualScreen();
            }

            if (!_regionAnimation.ToRectangle.Equals(AutoRect))
            {
                _regionAnimation.FromRectangle = _regionAnimation.CurrentRectangle.IsEmpty
                    ? BaseVirtualScreen()
                    : _regionAnimation.CurrentRectangle;
                _regionAnimation.ToRectangle = AutoRect;
                _regionAnimation.Start();
            }

            StaticValue.catchRectangle = AutoRect.SizeOffset(-1);
        }

        public void DrawAreaSelect(Graphics gs)
        {
            if (!timerStart.IsRunning) timerStart.Start();
            _regionAnimation.Update();
            using (var brush = CreateBackBrush())
            {
                using (var graphicsPath = new GraphicsPath())
                {
                    var rect = _regionAnimation.IsActive && !_regionAnimation.CurrentRectangle.IsEmpty
                        ? _regionAnimation.CurrentRectangle.SizeOffset(-1)
                        : AutoRect;
                    //Console.WriteLine(string.Format("NowRect:{0},TargetRect:{1}\nInfo:{2}\n\n", rect, AutoRect, regionAnimation.IsActive));
                    graphicsPath.AddRectangle(rect);
                    graphicsPath.AddRectangle(BaseVirtualScreen());
                    gs.FillPath(brush, graphicsPath);

                    _borderDotPen.DashOffset = (float) timerStart.Elapsed.TotalSeconds * -15;
                    gs.DrawRectangle(_borderDotPen, rect);
                }
            }
        }

        public Rectangle BaseVirtualScreen()
        {
            return AutoSelectApi.ScreenToClient(SystemInformation.VirtualScreen);
        }

        protected Brush CreateBackBrush()
        {
            return new SolidBrush(Color.FromArgb(80, 40, 40, 40));
        }

        private string GetInfoText_HEXcolor()
        {
            if (CommonSetting.HEX颜色值大写)
                return "#" + CurrentColor.ToHex().ToUpper();
            return "#" + CurrentColor.ToHex().ToLower();
        }

        private string GetInfoText_color()
        {
            return CurrentColor.ToRgb();
        }

        private Bitmap Magnifier(Image img, Point position, int horizontalPixelCount, int verticalPixelCount,
            int pixelSize)
        {
            horizontalPixelCount = (horizontalPixelCount | 1).Between(1, 101);
            verticalPixelCount = (verticalPixelCount | 1).Between(1, 101);
            pixelSize = pixelSize.Between(1, 1000);
            if (horizontalPixelCount * pixelSize > BaseVirtualScreen().Width ||
                verticalPixelCount * pixelSize > BaseVirtualScreen().Height)
            {
                horizontalPixelCount = verticalPixelCount = 15;
                pixelSize = 10;
            }

            var num = horizontalPixelCount * pixelSize;
            var num2 = verticalPixelCount * pixelSize;
            var bitmap = new Bitmap(num - 1, num2 - 1);
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.InterpolationMode = InterpolationMode.NearestNeighbor;
                graphics.PixelOffsetMode = PixelOffsetMode.Half;
                graphics.DrawImage(img, new Rectangle(0, 0, num, num2),
                    new Rectangle(position.X - horizontalPixelCount / 2 - BaseVirtualScreen().X,
                        position.Y - verticalPixelCount / 2 - BaseVirtualScreen().Y, horizontalPixelCount,
                        verticalPixelCount), GraphicsUnit.Pixel);
                graphics.PixelOffsetMode = PixelOffsetMode.None;
                using (var brush = new SolidBrush(Color.FromArgb(100, 26, 173, 25)))
                {
                    graphics.FillRectangle(brush,
                        new Rectangle(0, (num2 - pixelSize) / 2, (num - pixelSize) / 2, pixelSize));
                    graphics.FillRectangle(brush,
                        new Rectangle((num + pixelSize) / 2, (num2 - pixelSize) / 2, (num - pixelSize) / 2, pixelSize));
                    graphics.FillRectangle(brush,
                        new Rectangle((num - pixelSize) / 2, 0, pixelSize, (num2 - pixelSize) / 2));
                    graphics.FillRectangle(brush,
                        new Rectangle((num - pixelSize) / 2, (num2 + pixelSize) / 2, pixelSize,
                            (num2 - pixelSize) / 2));
                }

                graphics.DrawRectangle(Pens.Black, (num - pixelSize) / 2 - 1, (num2 - pixelSize) / 2 - 1, pixelSize,
                    pixelSize);
                if (pixelSize >= 6)
                    graphics.DrawRectangle(Pens.White, (num - pixelSize) / 2, (num2 - pixelSize) / 2, pixelSize - 2,
                        pixelSize - 2);
            }

            return bitmap;
        }

        private void DrawCursorGraphics(Graphics g, bool isGetColor = false)
        {
            g.InterpolationMode = InterpolationMode.HighQualityBilinear;
            g.CompositingQuality = CompositingQuality.HighQuality;
            g.SmoothingMode = SmoothingMode.HighQuality;
            g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;

            var activeScreenBounds0Based = MagnifierHelp.GetActiveScreenBounds0Based();
            var point = CurrentPosition;
            var xSpace = 15.DpiValue();
            var ySpace = 15.DpiValue();
            var horizontalPixelCount = 18;
            var pixelSize = 8.DpiValue();
            Image image = (Bitmap) BackgroundImageEx.Clone();
            if (isGetColor && GraphicsList != null)
                using (var g2 = Graphics.FromImage(image))
                {
                    GraphicsList.OutDraw(g2);
                }

            var colorImage = Magnifier(image, CurrentPosition, horizontalPixelCount, horizontalPixelCount / 4 * 3,
                pixelSize);
            var left = point.X + xSpace;
            var top = point.Y + ySpace;
            if (left + colorImage.Width > activeScreenBounds0Based.Right) left = point.X - xSpace - colorImage.Width;
            if (top + colorImage.Height + 68.DpiValue() > activeScreenBounds0Based.Bottom)
                top = point.Y - ySpace - colorImage.Height - 68.DpiValue();
            using (var pen = new Pen(Color.White))
            {
                using (Brush brush = new SolidBrush(Color.White))
                {
                    var colorImageWidth = colorImage.Width;
                    var colorImageHeight = colorImage.Height;

                    //放大镜部分
                    using (var textureBrush = new TextureBrush(colorImage))
                    {
                        textureBrush.TranslateTransform(left, top);

                        g.FillRectangle(textureBrush, left, top, colorImageWidth, colorImageHeight);
                    }

                    g.DrawRectangleProper(pen, left, top, colorImageWidth, colorImageHeight - 1);

                    int textHeight;
                    using (var font = new Font("黑体", 10.5f))
                    {
                        //color Str
                        currentcolor = IsRgb ? GetInfoText_color() : GetInfoText_HEXcolor();
                        var lstDrawStr = new List<string> {currentcolor};

                        if (isGetColor)
                        {
                            if (Equals(Status, "取色器"))
                            {
                                lstDrawStr.Add("单击/Enter选择颜色");
                                lstDrawStr.Add("鼠标右键/Esc键退出");
                            }
                            else
                            {
                                lstDrawStr.Add("单击选择填充颜色");
                            }
                        }
                        else
                        {
                            lstDrawStr.Add(string.Format("尺寸:{0} x {1}", AutoRect.Width, AutoRect.Height));
                            lstDrawStr.Add("按C键复制颜色值");
                            lstDrawStr.Add("Shift切换RGB/HEX");
                        }

                        textHeight = font.Height * lstDrawStr.Count;
                        var lineSpace = font.Height / lstDrawStr.Count;
                        textHeight += lineSpace * 2;

                        //底部文字阴影
                        using (Brush brush4 = new SolidBrush(Color.FromArgb(230, 0, 0, 0)))
                        {
                            g.FillRectangle(brush4, left - 1, top + (colorImageHeight - 1), colorImageWidth + 2,
                                textHeight);
                        }

                        //all border
                        using (var pen2 = new Pen(Color.Black))
                        {
                            g.DrawRectangleProper(pen2, left - 1, top - 1, colorImageWidth + 2,
                                colorImageHeight + textHeight);
                        }

                        var baseRect = new Rectangle(left, top + colorImageHeight + lineSpace.DpiValue(),
                            colorImage.Width, font.Height);

                        //color block
                        using (Brush brush5 = new SolidBrush(CurrentColor))
                        {
                            var colorRect = new Rectangle(baseRect.X + 20.DpiValue(), baseRect.Y + font.Height / 6,
                                font.Height * 2 / 3, font.Height * 2 / 3);
                            g.FillRectangle(brush5, colorRect);
                            g.DrawRectangle(pen, colorRect);
                        }

                        using (var stringFormat = new StringFormat
                            {LineAlignment = StringAlignment.Center, Alignment = StringAlignment.Center})
                        {
                            for (var i = 0; i < lstDrawStr.Count; i++)
                                g.DrawString(lstDrawStr[i], font, brush,
                                    new RectangleF(baseRect.X, baseRect.Y + (font.Height * i).DpiValue(),
                                        baseRect.Width, baseRect.Height), stringFormat);
                        }
                    }

                    RectangleMove = new Rectangle(left, top, colorImage.Width, colorImage.Height + textHeight);
                    using (new AutomaticCanvasRefresher(this, GetRectangleMove))
                    {
                    }
                }
            }
        }

        public Rectangle GetRectangleMove()
        {
            var rectangleMove = RectangleMove;
            rectangleMove.Inflate(1, 1);
            return Rectangle.Ceiling(rectangleMove);
        }

        protected void AddNewObject()
        {
            var selected = GetSelected(this);
            if (selected == null || selected.NoteType == DrawToolType.Catch || selected.NoteType == DrawToolType.Gaus ||
                selected.NoteType == DrawToolType.Mosaic || selected.NoteType == DrawToolType.Highlight) return;
            var num = 20;
            var rectangle = selected.Rectangle;
            rectangle.Offset(num, num);
            var startPoint = selected.StartPoint;
            startPoint.Offset(num, num);
            var endPoint = selected.EndPoint;
            endPoint.Offset(num, num);
            DrawObject drawObject;
            switch (selected.NoteType)
            {
                case DrawToolType.Rectangle:
                    drawObject = new DrawRectangle(rectangle.X, rectangle.Y, rectangle.Width, rectangle.Height);
                    break;
                case DrawToolType.Ellipse:
                    drawObject = new DrawEllipse(rectangle.X, rectangle.Y, rectangle.Width, rectangle.Height);
                    break;
                case DrawToolType.Text:
                    drawObject = new DrawText(rectangle.X, rectangle.Y, rectangle.Width, rectangle.Height)
                    {
                        Text = selected.Text,
                        Font = selected.Font
                    };
                    break;
                case DrawToolType.Line:
                    drawObject = new DrawLine(startPoint.X, startPoint.Y, endPoint.X, endPoint.Y);
                    break;
                case DrawToolType.Arrow:
                    drawObject = new DrawArrow(startPoint.X, startPoint.Y, endPoint.X, endPoint.Y);
                    break;
                case DrawToolType.Polygon:
                    drawObject = new DrawPolygon(startPoint.X, startPoint.Y, endPoint.X, endPoint.Y);
                    break;
                case DrawToolType.Step:
                    drawObject = new DrawStep(rectangle.X, rectangle.Y, rectangle.Width, rectangle.Height)
                    {
                        Text = selected.Text
                    };
                    break;
                case DrawToolType.MultiCatch:
                    if (GraphicsList.Count >= 99) return;
                    drawObject = new DrawMultiCatch(rectangle.X, rectangle.Y, rectangle.Width, rectangle.Height)
                    {
                        Text = (NumberArry[GraphicsList.Count - 1] + 1).ToString()
                    };
                    break;
                default:
                    drawObject = new DrawRectangle(rectangle.X, rectangle.Y, rectangle.Width, rectangle.Height);
                    break;
            }

            GraphicsList.UnselectAll();
            GraphicsList.Add(drawObject);
            Capture = true;
            drawObject.Selected = true;
            drawObject.Normalize();
            AddCommandToHistory(new CommandAdd(drawObject));
            Refresh();
        }

        public void MoveSelection(Point delta)
        {
            if (GraphicsList.SelectedObjects.Any())
                using (new AutomaticCanvasRefresher(this, GraphicsList.SelectedObjects.GetGroupBoundingBox))
                {
                    foreach (var item in GraphicsList.SelectedObjects) item.Move(delta.X, delta.Y);
                }
        }

        private void MoveSelection(int x, int y)
        {
            MoveSelection(new Point(x, y));
        }

        private void MAddChangeState()
        {
            var commandChangeState = new CommandChangeState(GraphicsList);
            commandChangeState.NewState(GraphicsList);
            AddCommandToHistory(commandChangeState);
        }

        protected override void OnKeyUp(KeyEventArgs e)
        {
            switch (e.KeyData)
            {
                case Keys.Up:
                    MAddChangeState();
                    break;
                case Keys.Down:
                    MAddChangeState();
                    break;
                case Keys.Left:
                    MAddChangeState();
                    break;
                case Keys.Right:
                    MAddChangeState();
                    break;
            }

            if (e.Modifiers.CompareTo(Keys.Shift) == 0 && e.KeyCode == Keys.Up)
                MAddChangeState();
            else if (e.Modifiers.CompareTo(Keys.Shift) == 0 && e.KeyCode == Keys.Down)
                MAddChangeState();
            else if (e.Modifiers.CompareTo(Keys.Shift) == 0 && e.KeyCode == Keys.Left)
                MAddChangeState();
            else if (e.Modifiers.CompareTo(Keys.Shift) == 0 && e.KeyCode == Keys.Right) MAddChangeState();
        }

        public void MoveMouseToPoint(int x, int y)
        {
            NativeMethods.SetCursorPos(x, y);
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            switch (e.KeyData)
            {
                case Keys.Up:
                    if (!IsShowCross) MoveMouseToPoint(MousePosition.X, MousePosition.Y - 1);
                    MoveSelection(0, -1);
                    break;
                case Keys.Down:
                    if (!IsShowCross) MoveMouseToPoint(MousePosition.X, MousePosition.Y + 1);
                    MoveSelection(0, 1);
                    break;
                case Keys.Left:
                    if (!IsShowCross) MoveMouseToPoint(MousePosition.X - 1, MousePosition.Y);
                    MoveSelection(-1, 0);
                    break;
                case Keys.Right:
                    if (!IsShowCross) MoveMouseToPoint(MousePosition.X + 1, MousePosition.Y);
                    MoveSelection(1, 0);
                    break;
                case Keys.Escape:
                    if (!IsWindows) Close();
                    break;
                case Keys.Delete:
                {
                    var command = new CommandDelete(GraphicsList);
                    if (!GraphicsList.DeleteSelection()) break;
                    AddCommandToHistory(command);
                    if (ActiveTool == DrawToolType.MultiCatch)
                    {
                        HideMultiTool();
                        var count = GraphicsList.Count;
                        for (var num = count - 1; num >= 0; num--) GraphicsList[num].Text = (count - num).ToString();
                    }

                    Refresh();
                    MAddChangeState();
                    break;
                }
                case Keys.Return:
                    if (IsMulti)
                        DialogResult = DialogResult.OK;
                    else
                        Copy();
                    break;
                case Keys.Space:
                    Paste();
                    break;
            }

            if (e.Modifiers.CompareTo(Keys.Shift) == 0)
            {
                if (e.KeyCode == Keys.Up)
                    MoveSelection(0, -50);
                else if (e.KeyCode == Keys.Down)
                    MoveSelection(0, 50);
                else if (e.KeyCode == Keys.Left)
                    MoveSelection(-50, 0);
                else if (e.KeyCode == Keys.Right) MoveSelection(50, 0);
            }
            else if (e.Modifiers.CompareTo(Keys.Control) == 0)
            {
                if (e.KeyCode == Keys.D)
                {
                    AddNewObject();
                }
                else if (e.KeyCode == Keys.C)
                {
                    Copy();
                }
                else if (e.KeyCode == Keys.T)
                {
                    Paste();
                }
                else if (e.KeyCode == Keys.Z)
                {
                    Undo();
                }
                else if (e.KeyCode == Keys.Y)
                {
                    Redo();
                }
                else if (e.KeyCode == Keys.S && !IsWindows)
                {
                    Save();
                }
                else if (e.KeyCode == Keys.A)
                {
                    GraphicsList.SelectAll();
                    Refresh();
                }
            }

            if (isShowZoom)
            {
                if ((ModifierKeys & Keys.Shift) == Keys.Shift)
                {
                    IsRgb = !IsRgb;
                    Refresh();
                }

                if ((ModifierKeys & Keys.Alt) == Keys.Alt)
                {
                    IsShowCross = !IsShowCross;
                    Refresh();
                }

                if ((ModifierKeys & Keys.Control) == Keys.Control)
                {
                    IsSmallControlModel = !IsSmallControlModel;
                    Refresh();
                }

                if (e.KeyData == Keys.C && currentcolor != null)
                {
                    if (Equals(Status, "取色器"))
                        DialogResult = DialogResult.OK;
                    else
                        ClipboardService.SetText(currentcolor);
                    Close();
                }
            }
        }

        public void Update(Control control)
        {
            var mousePosition = MousePosition;
            if (control != null)
                CurrentPosition = control.PointToClient(mousePosition);
            else
                CurrentPosition = AutoSelectApi.ScreenToClient(mousePosition);
        }

        public void ShowTool(DrawObject drawObject)
        {
            if (drawObject == null) return;
            var screenRect = BaseVirtualScreen();
            var toolLocation_Padding = 5.DpiValue();
            var toolLocationX = drawObject.RectangleT.X + (drawObject.RectangleT.Width - toolspanel.Width) / 2;
            var toolLocationY = drawObject.RectangleT.Location.Y + drawObject.RectangleT.Height + toolLocation_Padding;
            if (drawObject.RectangleT.Location.Y + drawObject.RectangleT.Height + toolLocation_Padding +
                toolspanel.Height > screenRect.Height)
            {
                if (drawObject.RectangleT.Location.Y > toolspanel.Height)
                    toolLocationY = drawObject.RectangleT.Location.Y - toolspanel.Height;
                else
                    toolLocationY = drawObject.RectangleT.Location.Y + drawObject.RectangleT.Height -
                                    toolspanel.Height;
            }

            if (toolLocationX < screenRect.X) toolLocationX = screenRect.X;
            toolLocationX = Math.Min(screenRect.Width, Math.Max(0, toolLocationX));
            toolLocationY = Math.Min(screenRect.Height, Math.Max(0, toolLocationY));
            toolspanel.Location = new Point(toolLocationX, toolLocationY);
            toolspanel.Visible = true;
            if (toolspanel.Location.Y < drawObject.RectangleT.Y + drawObject.RectangleT.Height)
            {
                ToolsBtn.Location =
                    new Point(toolspanel.Left, toolspanel.Top - toolspanel.Height - toolLocation_Padding);
            }
            else
            {
                ToolsBtn.Location =
                    new Point(toolspanel.Left, toolspanel.Top + toolspanel.Height + toolLocation_Padding);
                if (ToolsBtn.Top + ToolsBtn.Height + toolLocation_Padding > BaseVirtualScreen().Height)
                    ToolsBtn.Location = new Point(toolspanel.Left,
                        toolspanel.Top - toolspanel.Height - toolLocation_Padding);
            }
        }

        public void ShowMultiTool(DrawObject drawObject)
        {
            var num = 5.DpiValue();
            var num2 = drawObject.RectangleT.X + drawObject.RectangleT.Width - multiBtn.Width;
            var y = drawObject.RectangleT.Location.Y + drawObject.RectangleT.Height + num;
            if (drawObject.RectangleT.Location.Y + drawObject.RectangleT.Height + num + multiBtn.Height >
                BaseVirtualScreen().Height)
                y = drawObject.RectangleT.Location.Y + drawObject.RectangleT.Height - multiBtn.Height;
            if (num2 < BaseVirtualScreen().X) num2 = BaseVirtualScreen().X;
            multiBtn.Location = new Point(num2, y);
            multiBtn.Visible = true;
        }

        public void HideMultiTool()
        {
            multiBtn.Visible = false;
        }

        public void HideTool()
        {
            toolspanel.Visible = false;
            ToolsBtn.Visible = false;
        }

        public WindowInfo FindSelectedWindow(Point point)
        {
            var lst = Windows?.Where(p => p.Rectangle.Contains(point));
            if (!IsSmallControlModel) lst = lst?.Where(p => !p.IsSmallControl);
            if (lst?.Count(p => p.IsWindow) > 1)
            {
                var nowHandle = lst.OrderByDescending(p => p.ZIndex).FirstOrDefault(p => p.IsWindow).Handle;
                //Console.WriteLine(string.Format("指定窗体:{0},Info:{1}"
                //    , nowHandle
                //    , string.Join(",", lst.Select(p => p.ZIndex + "|" + p.GetText() + "|" + p.GetClassName() + p.Rectangle))));

                //多个窗口同时被选择了
                //判断当前是在哪个窗口操作
                lst = lst.Where(p =>
                    Equals(p.Handle, nowHandle) || Equals(p.ParentHandle, nowHandle)
                );
                //Console.WriteLine(string.Format("指定窗体:{2},Count:{0},Info:{1}", lst.Count, string.Join(",", lst.Select(p => p.GetText() + "|" + p.GetClassName() + p.Rectangle)), nowHandle));
            }

            return lst?.OrderBy(p => p.Rectangle.Width).FirstOrDefault();
            //return Windows?.FirstOrDefault((WindowInfo x) => x.Rectangle.Contains(point));
        }

        private void DrawArea_MouseUp(object sender, MouseEventArgs e)
        {
            if (TempshowZoom)
            {
                ShowCross.Image = ImageHelp.EditCross();
                TempshowZoom = false;
                Refresh();
                return;
            }

            if (e.Button == MouseButtons.Left)
            {
                tools[(int) activeTool].OnMouseUp(this, e);
                var @catch = GetCatch();
                if (@catch == null) return;
            }

            if (StaticValue.CurrentToolType != 0)
            {
                ActiveTool = StaticValue.CurrentToolType;
            }
            else if (IsEdit)
            {
                var catch2 = GetCatch();
                if (catch2 != null) ShowTool(catch2);
            }

            if (e.Button != MouseButtons.Right) return;
            OnContextMenu(e);
            if (e.Button != MouseButtons.Right) return;
            if (isAutoDraw && AutoRect.Width != 0) Close();
            if (Status.Contains("截图"))
            {
                var catch3 = GetCatch();
                if (catch3 != null && !catch3.Rectangle.Contains(e.Location))
                {
                    HideTool();
                    GraphicsList.DeleteSelectionall();
                    Invalidate();
                    isAutoDraw = true;
                    isShowZoom = false;
                    IsShowCross = false;
                    ActiveTool = DrawToolType.Catch;
                    AutoRect = StaticValue.CurrentRectangle = Rectangle.Empty;
                    StaticValue.CurrentToolType = DrawToolType.Catch;
                }
            }
        }

        private void DrawAreaText(Graphics g)
        {
            if (string.IsNullOrEmpty(Status)) return;
            var text = string.Format("[{0}]-{1}", Status,
                nowSelectedModel == SelectedModel.控件 ? "按Ctrl切换为窗口模式" :
                nowSelectedModel == SelectedModel.窗口 ? "窗口" : "全屏");
            var autoRect = AutoRect;
            var font = new Font("Arial", 10.5f, FontStyle.Bold);
            var num = 5.DpiValue();
            var size = g.MeasureString(text, font).ToSize();
            var isTop = autoRect.Y - num - size.Height < BaseVirtualScreen().Y;
            var point = isTop
                ? new Point(autoRect.X + num, autoRect.Y)
                : new Point(autoRect.X, autoRect.Y - size.Height - num);
            if (point.X + size.Width >= BaseVirtualScreen().Width)
                point.X = BaseVirtualScreen().Width - size.Width;
            var txtRect = new Rectangle(point.X, point.Y, size.Width + num * 2, size.Height + (isTop ? 0 : 1) * num);
            g.FillRectangle(new SolidBrush(Color.FromArgb(180, 0, 0, 0)), txtRect);
            g.DrawTextWithShadow(text, new PointF(point.X + num, point.Y + (isTop ? 0 : 1) * num), font, Brushes.White,
                Brushes.Black);
        }

        private void DrawArea_Load(object sender, EventArgs e)
        {
            HookApi.Hook_Start();
            _borderDotPen = new Pen(CustomColor.CheckColor, (float) Math.Max(1, CommonSetting.截图边框宽度))
                {DashPattern = new float[] {5, 2}};
            timerStart = new Stopwatch();
            var color = Color.FromArgb(120, 120, 120);
            foreach (Control control3 in toolspanel.Controls)
                if (control3 is PictureBox pictureBox)
                {
                    new FmTip(pictureBox);
                    pictureBox.Size = new Size(30.DpiValue(), 35.DpiValue());
                    switch (control3.Name)
                    {
                        case "DrawStep":
                            pictureBox.Image = ImageHelp.CreateStep(color);
                            pictureBox.Visible = true;
                            break;
                        case "DrawCopy":
                            pictureBox.Image = ImageHelp.CreateCopy(color);
                            break;
                        case "DrawPaste":
                            pictureBox.Image = ImageHelp.CreatePaste(color);
                            break;
                        case "DrawChanel":
                            pictureBox.Image = ImageHelp.CreateClose(color);
                            break;
                        case "DrawSave":
                            pictureBox.Image = ImageHelp.CreateSave(color);
                            break;
                        case "DrawRedo":
                            pictureBox.Image = ImageHelp.CreateRedo(color);
                            break;
                        case "DrawUndo":
                            pictureBox.Image = ImageHelp.CreateUndo(color);
                            break;
                        case "DrawGaus":
                            pictureBox.Image = ImageHelp.CreateGaus(color);
                            pictureBox.Visible = true;
                            break;
                        case "DrawHighlight":
                            pictureBox.Image = ImageHelp.CreateHighlight(color);
                            pictureBox.Visible = true;
                            break;
                        case "DrawRectangleFill":
                            pictureBox.Image = ImageHelp.CreateRectangleFill(color);
                            pictureBox.Visible = true;
                            break;
                        case "DrawMosaic":
                            pictureBox.Image = ImageHelp.CreateMosaic(color);
                            pictureBox.Visible = true;
                            break;
                        case "DrawText":
                            pictureBox.Image = ImageHelp.CreateText(color);
                            pictureBox.Visible = true;
                            break;
                        case "DrawLine":
                            pictureBox.Image = ImageHelp.CreateLine(color);
                            pictureBox.Visible = true;
                            break;
                        case "DrawPolygon":
                            pictureBox.Image = ImageHelp.CreatePolygon(color);
                            pictureBox.Visible = true;
                            break;
                        case "DrawArrow":
                            pictureBox.Image = ImageHelp.CreateArrow(color);
                            pictureBox.Visible = true;
                            break;
                        case "DrawEllipse":
                            pictureBox.Image = ImageHelp.CreateEllipse(color);
                            pictureBox.Visible = true;
                            break;
                        case "DrawRectangle":
                            pictureBox.Image = ImageHelp.CreateRectangle(color);
                            pictureBox.Visible = true;
                            break;
                    }
                }

            foreach (Control control4 in ToolsBtn.Controls)
                if (control4 is PictureBox pictureBox2)
                {
                    new FmTip(pictureBox2);
                    pictureBox2.Dock = DockStyle.Right;
                    switch (control4.Name)
                    {
                        case "RectangleDot":
                            pictureBox2.Image = ImageHelp.CreateRectangle(color, true);
                            break;
                        case "EllipseDot":
                            pictureBox2.Image = ImageHelp.CreateEllipse(color, true);
                            break;
                        case "LineDot":
                            pictureBox2.Image = ImageHelp.CreateLine(color, true);
                            break;
                        case "ArrowBoth":
                            pictureBox2.Image = ImageHelp.CreateArrowBoth(false);
                            break;
                        case "TextDot":
                            pictureBox2.Image = ImageHelp.CreateTextDot();
                            break;
                        case "ShowCross":
                            pictureBox2.Image = ImageHelp.EditCross();
                            break;
                        case "Ellipse1":
                            pictureBox2.Image = ImageHelp.EditEllipse(Color.FromArgb(204, 204, 204), 1);
                            break;
                        case "Ellipse2":
                            pictureBox2.Image = ImageHelp.EditEllipse(Color.FromArgb(204, 204, 204), 2);
                            break;
                        case "Ellipse3":
                            pictureBox2.Image = ImageHelp.EditEllipse(Color.FromArgb(204, 204, 204), 3);
                            break;
                        case "Ellipse4":
                            pictureBox2.Image = ImageHelp.EditEllipse(Color.FromArgb(204, 204, 204), 4);
                            break;
                        case "RectangleCus":
                            pictureBox2.Image = ImageHelp.EditRectangleCus(color);
                            break;
                        case "Rectangle1":
                            pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.red);
                            break;
                        case "Rectangle2":
                            pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.yellow);
                            break;
                        case "Rectangle3":
                            pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.blue);
                            break;
                        case "Rectangle4":
                            pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.Green);
                            break;
                        case "Rectangle5":
                            pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.purple);
                            break;
                        case "Rectangle6":
                            pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.black);
                            break;
                        case "Rectangle7":
                            pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.gray);
                            break;
                    }
                }

            panelSize();
        }

        public Bitmap GetRect(Image pic, Rectangle rect)
        {
            var destRect = new Rectangle(0, 0, rect.Width, rect.Height);
            var bitmap = new Bitmap(destRect.Width, destRect.Height);
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.InterpolationMode = InterpolationMode.NearestNeighbor;
                graphics.PixelOffsetMode = PixelOffsetMode.Half;
                graphics.Clear(Color.FromArgb(0, 0, 0, 0));
                graphics.DrawImage(pic, destRect, rect, GraphicsUnit.Pixel);
                graphics.Dispose();
            }

            return bitmap;
        }

        public Bitmap GetResultImage()
        {
            GraphicsList.UnselectAll();
            Image backgroundImageEx = BackgroundImageEx;
            if (GraphicsList != null)
                using (var g = Graphics.FromImage(backgroundImageEx))
                {
                    StaticValue.CanCatch = true;
                    GraphicsList.Draw(g);
                }

            return GetRect(backgroundImageEx, StaticValue.catchRectangle.SizeOffset(1, 1));
        }

        public void Save()
        {
            GetResultImage().SaveFile(this);
            DialogResult = DialogResult.OK;
            Close();
        }

        public void Copy()
        {
            ClipboardService.ClipSetImage(GetResultImage(), false);
            DialogResult = DialogResult.OK;
            Close();
        }

        public new void Closing()
        {
            StaticValue.CurrentToolType = DrawToolType.Catch;
            StaticValue.CurrentRectangle = new Rectangle(0, 0, 0, 0);
            if (Status.Contains("截图"))
            {
                Result = CaptureResult.Image;
                Copy();
            }
            else if (Status.Contains("识别"))
            {
                Result = CaptureResult.Image;
                DialogResult = DialogResult.OK;
                Close();
            }
            else
            {
                Result = CaptureResult.Close;
                Close();
            }
        }

        public void Paste()
        {
            var img = GetResultImage();
            Close();
            this.ViewImageWithLocation(img, new Point(StaticValue.catchRectangle.X, StaticValue.catchRectangle.Y));
        }

        private void SetImage(DrawObject select)
        {
            InitImage();
            var checkColor = CustomColor.CheckColor;
            if (select.NoteType == DrawToolType.Rectangle) DrawRectangle.Image = ImageHelp.CreateRectangle(checkColor);
            if (select.NoteType == DrawToolType.Ellipse) DrawEllipse.Image = ImageHelp.CreateEllipse(checkColor);
            if (select.NoteType == DrawToolType.Line) DrawLine.Image = ImageHelp.CreateLine(checkColor);
            if (select.NoteType == DrawToolType.Text) DrawText.Image = ImageHelp.CreateText(checkColor);
            if (select.NoteType == DrawToolType.Mosaic) _drawMosaic.Image = ImageHelp.CreateMosaic(checkColor);
            if (select.NoteType == DrawToolType.Arrow) _drawArrow.Image = ImageHelp.CreateArrow(checkColor);
            if (select.NoteType == DrawToolType.Highlight) _drawHighlight.Image = ImageHelp.CreateHighlight(checkColor);
            if (select.NoteType == DrawToolType.RectangleFill)
                DrawRectangleFill.Image = ImageHelp.CreateRectangleFill(checkColor);
            if (select.NoteType == DrawToolType.Step) DrawStep.Image = ImageHelp.CreateStep(checkColor);
            if (select.NoteType == DrawToolType.Gaus) _drawGaus.Image = ImageHelp.CreateGaus(checkColor);
        }

        private void InitImage()
        {
            var color = Color.FromArgb(120, 120, 120);
            foreach (Control control in toolspanel.Controls)
                if (control is PictureBox pictureBox)
                {
                    pictureBox.Size = new Size(30.DpiValue(), 35.DpiValue());
                    switch (control.Name)
                    {
                        case "DrawCopy":
                            pictureBox.Image = ImageHelp.CreateCopy(color);
                            break;
                        case "DrawChanel":
                            pictureBox.Image = ImageHelp.CreateClose(color);
                            break;
                        case "DrawSave":
                            pictureBox.Image = ImageHelp.CreateSave(color);
                            break;
                        case "DrawRedo":
                            pictureBox.Image = ImageHelp.CreateRedo(color);
                            break;
                        case "DrawUndo":
                            pictureBox.Image = ImageHelp.CreateUndo(color);
                            break;
                        case "DrawGaus":
                            pictureBox.Image = ImageHelp.CreateGaus(color);
                            break;
                        case "DrawStep":
                            pictureBox.Image = ImageHelp.CreateStep(color);
                            break;
                        case "DrawHighlight":
                            pictureBox.Image = ImageHelp.CreateHighlight(color);
                            break;
                        case "DrawRectangleFill":
                            pictureBox.Image = ImageHelp.CreateRectangleFill(color);
                            break;
                        case "DrawMosaic":
                            pictureBox.Image = ImageHelp.CreateMosaic(color);
                            break;
                        case "DrawText":
                            pictureBox.Image = ImageHelp.CreateText(color);
                            break;
                        case "DrawLine":
                            pictureBox.Image = ImageHelp.CreateLine(color);
                            break;
                        case "DrawArrow":
                            pictureBox.Image = ImageHelp.CreateArrow(color);
                            break;
                        case "DrawEllipse":
                            pictureBox.Image = ImageHelp.CreateEllipse(color);
                            break;
                        case "DrawRectangle":
                            pictureBox.Image = ImageHelp.CreateRectangle(color);
                            break;
                        case "DrawPolygon":
                            pictureBox.Image = ImageHelp.CreatePolygon(color);
                            break;
                    }
                }
        }

        private void Hidedot()
        {
            HidedotALL();
            ShowEllipse();
        }

        private void ShowEllipse()
        {
            Ellipse1.Visible = true;
            Ellipse2.Visible = true;
            Ellipse3.Visible = true;
            Ellipse4.Visible = true;
        }

        private void HidedotALL()
        {
            upDownButtonEx.Visible = false;
            fontstyleButton.Visible = false;
            fontsizeButton.Visible = false;
            LineDot.Visible = false;
            TextDot.Visible = false;
            EllipseDot.Visible = false;
            RectangleDot.Visible = false;
            Ellipse1.Visible = false;
            Ellipse2.Visible = false;
            Ellipse3.Visible = false;
            Ellipse4.Visible = false;
            _arrowBoth.Visible = false;
        }

        public void panelSize()
        {
            var num = 0;
            foreach (Control control in ToolsBtn.Controls)
                if (control.Visible)
                    num += control.Width;
            ToolsBtn.Size = new Size(num, 35.DpiValue());
        }

        private void Draw_MouseDown(object sender, MouseEventArgs e)
        {
            if (GetCatch() is DrawCatch drawCatch) drawCatch.IsCatchMove = false;
            InitImage();
            var control = (Control) sender;
            var checkColor = CustomColor.CheckColor;
            switch (control.Name)
            {
                case "DrawRectangle":
                    ActiveTool = DrawToolType.Rectangle;
                    DrawRectangle.Image = ImageHelp.CreateRectangle(checkColor);
                    ToolsBtn.Visible = true;
                    Hidedot();
                    RectangleDot.Visible = true;
                    panelSize();
                    break;
                case "DrawEllipse":
                    ActiveTool = DrawToolType.Ellipse;
                    DrawEllipse.Image = ImageHelp.CreateEllipse(checkColor);
                    ToolsBtn.Visible = true;
                    Hidedot();
                    EllipseDot.Visible = true;
                    panelSize();
                    break;
                case "DrawPolygon":
                    ActiveTool = DrawToolType.Polygon;
                    DrawPolygon.Image = ImageHelp.CreatePolygon(checkColor);
                    ToolsBtn.Visible = true;
                    Hidedot();
                    panelSize();
                    break;
                case "DrawLine":
                    ActiveTool = DrawToolType.Line;
                    DrawLine.Image = ImageHelp.CreateLine(checkColor);
                    ToolsBtn.Visible = true;
                    Hidedot();
                    LineDot.Visible = true;
                    panelSize();
                    break;
                case "DrawArrow":
                    ActiveTool = DrawToolType.Arrow;
                    _drawArrow.Image = ImageHelp.CreateArrow(checkColor);
                    ToolsBtn.Visible = true;
                    Hidedot();
                    _arrowBoth.Visible = true;
                    panelSize();
                    break;
                case "DrawText":
                    ActiveTool = DrawToolType.Text;
                    DrawText.Image = ImageHelp.CreateText(checkColor);
                    ToolsBtn.Visible = true;
                    Hidedot();
                    TextDot.Visible = true;
                    fontstyleButton.Visible = true;
                    fontsizeButton.Visible = true;
                    Ellipse1.Visible = false;
                    Ellipse2.Visible = false;
                    Ellipse3.Visible = false;
                    Ellipse4.Visible = false;
                    panelSize();
                    break;
                case "DrawMosaic":
                    ActiveTool = DrawToolType.Mosaic;
                    _drawMosaic.Image = ImageHelp.CreateMosaic(checkColor);
                    ToolsBtn.Visible = false;
                    break;
                case "DrawGaus":
                    ActiveTool = DrawToolType.Gaus;
                    _drawGaus.Image = ImageHelp.CreateGaus(checkColor);
                    ToolsBtn.Visible = false;
                    break;
                case "DrawStep":
                    ActiveTool = DrawToolType.Step;
                    DrawStep.Image = ImageHelp.CreateStep(checkColor);
                    HidedotALL();
                    upDownButtonEx.Visible = true;
                    ToolsBtn.Visible = true;
                    panelSize();
                    break;
                case "DrawHighlight":
                    ActiveTool = DrawToolType.Highlight;
                    _drawHighlight.Image = ImageHelp.CreateHighlight(checkColor);
                    ToolsBtn.Visible = true;
                    HidedotALL();
                    panelSize();
                    break;
                case "DrawRectangleFill":
                    ActiveTool = DrawToolType.RectangleFill;
                    DrawRectangleFill.Image = ImageHelp.CreateRectangleFill(checkColor);
                    ToolsBtn.Visible = true;
                    HidedotALL();
                    panelSize();
                    break;
                case "DrawUndo":
                    Undo();
                    ToolsBtn.Visible = false;
                    break;
                case "DrawRedo":
                    Redo();
                    ToolsBtn.Visible = false;
                    break;
                case "DrawSave":
                    ToolsBtn.Visible = false;
                    Save();
                    break;
                case "DrawChanel":
                    ToolsBtn.Visible = false;
                    Close();
                    break;
                case "DrawCopy":
                    ToolsBtn.Visible = false;
                    Copy();
                    break;
                case "DrawPaste":
                    ToolsBtn.Visible = false;
                    Paste();
                    break;
            }

            StaticValue.CurrentToolType = ActiveTool;
        }

        private void Rectangle_Selected(PictureBox sender)
        {
            InitImageEditRectangle();
            sender.Image = ImageHelp.EditRectangle(DrawObject.LastUsedColor, true);
        }

        private void Ellipse_Selected(PictureBox sender)
        {
            var checkColor = CustomColor.CheckColor;
            InitImageEditEllipse();
            sender.Image = ImageHelp.EditEllipse(checkColor, DrawObject.LastUsedPenWidth);
        }

        private void Not_Selected(DrawObject drawObject)
        {
            var unCheckColor = CustomColor.UnCheckColor;
            RectangleDot.Image = ImageHelp.CreateRectangle(unCheckColor, true, DrawObject.LastIsDot);
            IsDot = DrawObject.LastIsDot;
            LineDot.Image = ImageHelp.CreateLine(unCheckColor, true, DrawObject.LastIsDot);
            EllipseDot.Image = ImageHelp.CreateEllipse(unCheckColor, true, DrawObject.LastIsDot);
            TextDot.Image = ImageHelp.CreateTextDot(DrawObject.LastIsOutline);
            IsOutLine = DrawObject.LastIsOutline;
            _arrowBoth.Image = ImageHelp.CreateArrowBoth(DrawObject.LastIsArrowBoth);
            IsArrowBoth = DrawObject.LastIsArrowBoth;
            switch (drawObject.NoteType)
            {
                case DrawToolType.Rectangle:
                    upDownButtonEx.Visible = false;
                    fontstyleButton.Visible = false;
                    fontsizeButton.Visible = false;
                    LineDot.Visible = false;
                    TextDot.Visible = false;
                    EllipseDot.Visible = false;
                    _arrowBoth.Visible = false;
                    Ellipse1.Visible = true;
                    Ellipse2.Visible = true;
                    Ellipse3.Visible = true;
                    Ellipse4.Visible = true;
                    RectangleDot.Visible = true;
                    ToolsBtn.Visible = true;
                    break;
                case DrawToolType.Line:
                    upDownButtonEx.Visible = false;
                    fontstyleButton.Visible = false;
                    fontsizeButton.Visible = false;
                    TextDot.Visible = false;
                    _arrowBoth.Visible = false;
                    RectangleDot.Visible = false;
                    EllipseDot.Visible = false;
                    Ellipse1.Visible = true;
                    Ellipse2.Visible = true;
                    Ellipse3.Visible = true;
                    Ellipse4.Visible = true;
                    LineDot.Visible = true;
                    ToolsBtn.Visible = true;
                    break;
                case DrawToolType.Polygon:
                    upDownButtonEx.Visible = false;
                    fontstyleButton.Visible = false;
                    fontsizeButton.Visible = false;
                    TextDot.Visible = false;
                    _arrowBoth.Visible = false;
                    RectangleDot.Visible = false;
                    EllipseDot.Visible = false;
                    Ellipse1.Visible = true;
                    Ellipse2.Visible = true;
                    Ellipse3.Visible = true;
                    Ellipse4.Visible = true;
                    LineDot.Visible = false;
                    ToolsBtn.Visible = true;
                    break;
                case DrawToolType.Ellipse:
                    upDownButtonEx.Visible = false;
                    fontstyleButton.Visible = false;
                    fontsizeButton.Visible = false;
                    LineDot.Visible = false;
                    TextDot.Visible = false;
                    _arrowBoth.Visible = false;
                    RectangleDot.Visible = false;
                    Ellipse1.Visible = true;
                    Ellipse2.Visible = true;
                    Ellipse3.Visible = true;
                    Ellipse4.Visible = true;
                    EllipseDot.Visible = true;
                    ToolsBtn.Visible = true;
                    break;
                case DrawToolType.Arrow:
                    upDownButtonEx.Visible = false;
                    fontstyleButton.Visible = false;
                    fontsizeButton.Visible = false;
                    LineDot.Visible = false;
                    TextDot.Visible = false;
                    RectangleDot.Visible = false;
                    EllipseDot.Visible = false;
                    Ellipse1.Visible = true;
                    Ellipse2.Visible = true;
                    Ellipse3.Visible = true;
                    Ellipse4.Visible = true;
                    _arrowBoth.Visible = true;
                    ToolsBtn.Visible = true;
                    break;
                case DrawToolType.Highlight:
                    upDownButtonEx.Visible = false;
                    fontstyleButton.Visible = false;
                    fontsizeButton.Visible = false;
                    LineDot.Visible = false;
                    TextDot.Visible = false;
                    _arrowBoth.Visible = false;
                    RectangleDot.Visible = false;
                    EllipseDot.Visible = false;
                    Ellipse1.Visible = false;
                    Ellipse2.Visible = false;
                    Ellipse3.Visible = false;
                    Ellipse4.Visible = false;
                    ToolsBtn.Visible = true;
                    break;
                case DrawToolType.Step:
                    fontstyleButton.Visible = false;
                    fontsizeButton.Visible = false;
                    LineDot.Visible = false;
                    TextDot.Visible = false;
                    _arrowBoth.Visible = false;
                    RectangleDot.Visible = false;
                    EllipseDot.Visible = false;
                    Ellipse1.Visible = false;
                    Ellipse2.Visible = false;
                    Ellipse3.Visible = false;
                    Ellipse4.Visible = false;
                    upDownButtonEx.Visible = true;
                    ToolsBtn.Visible = true;
                    break;
                case DrawToolType.Text:
                    upDownButtonEx.Visible = false;
                    fontstyleButton.Visible = true;
                    fontsizeButton.Visible = true;
                    LineDot.Visible = false;
                    _arrowBoth.Visible = false;
                    RectangleDot.Visible = false;
                    EllipseDot.Visible = false;
                    Ellipse1.Visible = false;
                    Ellipse2.Visible = false;
                    Ellipse3.Visible = false;
                    Ellipse4.Visible = false;
                    TextDot.Visible = true;
                    ToolsBtn.Visible = true;
                    break;
                case DrawToolType.Mosaic:
                    ToolsBtn.Visible = false;
                    break;
                case DrawToolType.Gaus:
                    ToolsBtn.Visible = false;
                    break;
            }
        }

        private void Tools_MouseDown(object sender, MouseEventArgs e)
        {
            var selected = GetSelected(this);
            if (selected != null && selected.NoteType == DrawToolType.Highlight) selected.IsChangeColor = true;
            var checkColor = CustomColor.CheckColor;
            var pictureBox = (PictureBox) sender;
            switch (pictureBox.Name)
            {
                case "Ellipse1":
                    InitImageEditEllipse();
                    pictureBox.Image = ImageHelp.EditEllipse(checkColor, 1);
                    DrawObject.LastUsedPenWidth = StaticValue.PenWidth1;
                    break;
                case "Ellipse2":
                    InitImageEditEllipse();
                    pictureBox.Image = ImageHelp.EditEllipse(checkColor, 2);
                    DrawObject.LastUsedPenWidth = StaticValue.PenWidth2;
                    break;
                case "Ellipse3":
                    InitImageEditEllipse();
                    pictureBox.Image = ImageHelp.EditEllipse(checkColor, 3);
                    DrawObject.LastUsedPenWidth = StaticValue.PenWidth3;
                    break;
                case "Ellipse4":
                    InitImageEditEllipse();
                    pictureBox.Image = ImageHelp.EditEllipse(checkColor, 4);
                    DrawObject.LastUsedPenWidth = StaticValue.PenWidth4;
                    break;
            }

            var pictureBox2 = (PictureBox) sender;
            switch (pictureBox2.Name)
            {
                case "Rectangle1":
                    InitImageEditRectangle();
                    pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.red, true);
                    DrawObject.LastUsedColor = CustomColor.red;
                    break;
                case "Rectangle2":
                    InitImageEditRectangle();
                    pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.yellow, true);
                    DrawObject.LastUsedColor = CustomColor.yellow;
                    break;
                case "Rectangle3":
                    InitImageEditRectangle();
                    pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.blue, true);
                    DrawObject.LastUsedColor = CustomColor.blue;
                    break;
                case "Rectangle4":
                    InitImageEditRectangle();
                    pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.Green, true);
                    DrawObject.LastUsedColor = CustomColor.Green;
                    break;
                case "Rectangle5":
                    InitImageEditRectangle();
                    pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.purple, true);
                    DrawObject.LastUsedColor = CustomColor.purple;
                    break;
                case "Rectangle6":
                    InitImageEditRectangle();
                    pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.black, true);
                    DrawObject.LastUsedColor = CustomColor.black;
                    break;
                case "RectangleCus":
                    InitImageEditRectangle();
                    pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.Custom, true);
                    DrawObject.LastUsedColor = CustomColor.Custom;
                    break;
                case "Rectangle7":
                    InitImageEditRectangle();
                    pictureBox2.Image = ImageHelp.EditRectangle(CustomColor.gray, true);
                    DrawObject.LastUsedColor = CustomColor.gray;
                    break;
            }

            var color = Color.FromArgb(120, 120, 120);
            switch (pictureBox2.Name)
            {
                case "RectangleDot":
                    RectangleDot.Image = ImageHelp.CreateRectangle(color, true, !IsDot);
                    EllipseDot.Image = ImageHelp.CreateEllipse(color, true, !IsDot);
                    LineDot.Image = ImageHelp.CreateLine(color, true, !IsDot);
                    IsDot = !IsDot;
                    DrawObject.LastIsDot = IsDot;
                    break;
                case "EllipseDot":
                    RectangleDot.Image = ImageHelp.CreateRectangle(color, true, !IsDot);
                    EllipseDot.Image = ImageHelp.CreateEllipse(color, true, !IsDot);
                    LineDot.Image = ImageHelp.CreateLine(color, true, !IsDot);
                    IsDot = !IsDot;
                    DrawObject.LastIsDot = IsDot;
                    break;
                case "LineDot":
                    RectangleDot.Image = ImageHelp.CreateRectangle(color, true, !IsDot);
                    EllipseDot.Image = ImageHelp.CreateEllipse(color, true, !IsDot);
                    LineDot.Image = ImageHelp.CreateLine(color, true, !IsDot);
                    IsDot = !IsDot;
                    DrawObject.LastIsDot = IsDot;
                    break;
                case "ArrowBoth":
                    pictureBox2.Image = ImageHelp.CreateArrowBoth(!IsArrowBoth);
                    IsArrowBoth = !IsArrowBoth;
                    DrawObject.LastIsArrowBoth = IsArrowBoth;
                    break;
                case "TextDot":
                    pictureBox2.Image = ImageHelp.CreateTextDot(!IsOutLine);
                    IsOutLine = !IsOutLine;
                    DrawObject.LastIsOutline = IsOutLine;
                    break;
            }

            if (GraphicsList.ShowPropertiesDialog(this))
            {
                var selected2 = GetSelected(this);
                if (selected2 != null) selected.IsChange = true;
                Refresh();
            }
        }

        private void InitImageEditEllipse()
        {
            foreach (Control control in ToolsBtn.Controls)
                if (control is PictureBox pictureBox)
                    switch (pictureBox.Name)
                    {
                        case "Ellipse1":
                            pictureBox.Image = ImageHelp.EditEllipse(Color.FromArgb(204, 204, 204), 1);
                            break;
                        case "Ellipse2":
                            pictureBox.Image = ImageHelp.EditEllipse(Color.FromArgb(204, 204, 204), 2);
                            break;
                        case "Ellipse3":
                            pictureBox.Image = ImageHelp.EditEllipse(Color.FromArgb(204, 204, 204), 3);
                            break;
                        case "Ellipse4":
                            pictureBox.Image = ImageHelp.EditEllipse(Color.FromArgb(204, 204, 204), 4);
                            break;
                    }
        }

        private void InitImageEditRectangle()
        {
            var color = Color.FromArgb(120, 120, 120);
            foreach (Control control in ToolsBtn.Controls)
                if (control is PictureBox pictureBox)
                    switch (pictureBox.Name)
                    {
                        case "Rectangle1":
                            pictureBox.Image = ImageHelp.EditRectangle(CustomColor.red);
                            break;
                        case "Rectangle2":
                            pictureBox.Image = ImageHelp.EditRectangle(CustomColor.yellow);
                            break;
                        case "Rectangle3":
                            pictureBox.Image = ImageHelp.EditRectangle(CustomColor.blue);
                            break;
                        case "RectangleCus":
                            pictureBox.Image = CustomColor.Custom == Color.Transparent
                                ? ImageHelp.EditRectangleCus(color)
                                : ImageHelp.EditRectangle(CustomColor.Custom);
                            break;
                        case "Rectangle4":
                            pictureBox.Image = ImageHelp.EditRectangle(CustomColor.Green);
                            break;
                        case "Rectangle5":
                            pictureBox.Image = ImageHelp.EditRectangle(CustomColor.purple);
                            break;
                        case "Rectangle6":
                            pictureBox.Image = ImageHelp.EditRectangle(CustomColor.black);
                            break;
                        case "Rectangle7":
                            pictureBox.Image = ImageHelp.EditRectangle(CustomColor.gray);
                            break;
                    }
        }

        private void P2_DrawSave_MouseDown(object sender, MouseEventArgs e)
        {
            var pictureBox = (PictureBox) sender;
            var name = pictureBox.Name;
            if (name != "P2_DrawChanel")
            {
                if (name != "P2_DrawOCR")
                {
                    if (name == "P2_DrawSave") SaveMultiFile();
                }
                else
                {
                    DialogResult = DialogResult.OK;
                }
            }
            else
            {
                Close();
            }
        }

        private void SaveMultiFile()
        {
            saveFileDialog1.Filter = "png文件|*.png|jpg文件|*.jpg|bmp文件|*.bmp";
            saveFileDialog1.Title = "保存位置";
            saveFileDialog1.FilterIndex = 0;
            saveFileDialog1.FileName = StaticValue.CatchName + "_" + new Bitmap(1, 1).GetFileName();
            ShowIcon = false;
            this.CenterChild();
            if (saveFileDialog1.ShowDialog(this) != DialogResult.OK) return;
            var extension = Path.GetExtension(saveFileDialog1.FileName);
            if (extension != "")
            {
                ImageFormat format;
                switch (extension)
                {
                    case ".jpg":
                        format = ImageFormat.Jpeg;
                        break;
                    case ".bmp":
                        format = ImageFormat.Bmp;
                        break;
                    case ".png":
                        format = ImageFormat.Png;
                        break;
                    default:
                        format = ImageFormat.Png;
                        break;
                }

                GraphicsList.UnselectAll();
                Image backgroundImageEx = BackgroundImageEx;
                var count = GraphicsList.Count;
                for (var num = count - 1; num >= 0; num--)
                {
                    Image rect = GetRect(backgroundImageEx, GraphicsList[num].Rectangle.SizeOffset(1, 1));
                    var text4 = Path.GetDirectoryName(saveFileDialog1.FileName) + "\\";
                    var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(saveFileDialog1.FileName);
                    var extension2 = Path.GetExtension(saveFileDialog1.FileName);
                    var path = text4 + fileNameWithoutExtension + "_多区域_" + (count - num) + extension2;
                    rect.SafeSave(path, format);
                }

                Close();
            }
        }

        private void DrawArea_Shown(object sender, EventArgs e)
        {
        }

        private void DrawArea_FormClosing(object sender, FormClosingEventArgs e)
        {
            StaticValue.IsCatchScreen = false;
            Console.WriteLine("Close isCatchScreen:" + StaticValue.IsCatchScreen);
            HookApi.Hook_Clear();
        }

        private void ShowCross_MouseDown(object sender, MouseEventArgs e)
        {
            TempshowZoom = true;
            ShowCross.Image = ImageHelp.EditCross(true);
            Refresh();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing) components?.Dispose();
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            toolspanel = new Panel();
            DrawRectangle = new PictureBox();
            DrawEllipse = new PictureBox();
            DrawPolygon = new PictureBox();
            DrawLine = new PictureBox();
            _drawArrow = new PictureBox();
            DrawText = new PictureBox();
            DrawRectangleFill = new PictureBox();
            _drawHighlight = new PictureBox();
            _drawMosaic = new PictureBox();
            _drawGaus = new PictureBox();
            DrawStep = new PictureBox();
            DrawUndo = new PictureBox();
            DrawRedo = new PictureBox();
            DrawChanel = new PictureBox();
            DrawSave = new PictureBox();
            DrawPaste = new PictureBox();
            _drawCopy = new PictureBox();
            saveFileDialog1 = new SaveFileDialog();
            ToolsBtn = new Panel();
            ShowCross = new PictureBox();
            upDownButtonEx = new UpDownButtonEx();
            fontsizeButton = new ListButton();
            fontstyleButton = new ListButton();
            RectangleDot = new PictureBox();
            EllipseDot = new PictureBox();
            LineDot = new PictureBox();
            _arrowBoth = new PictureBox();
            TextDot = new PictureBox();
            Ellipse1 = new PictureBox();
            Ellipse2 = new PictureBox();
            Ellipse3 = new PictureBox();
            Ellipse4 = new PictureBox();
            RectangleCus = new PictureBox();
            Rectangle1 = new PictureBox();
            Rectangle2 = new PictureBox();
            Rectangle3 = new PictureBox();
            Rectangle4 = new PictureBox();
            Rectangle5 = new PictureBox();
            Rectangle6 = new PictureBox();
            Rectangle7 = new PictureBox();
            multiBtn = new Panel();
            P2_DrawChanel = new PictureBox();
            P2_DrawSave = new PictureBox();
            toolspanel.SuspendLayout();
            ((ISupportInitialize) DrawRectangle).BeginInit();
            ((ISupportInitialize) DrawEllipse).BeginInit();
            ((ISupportInitialize) DrawPolygon).BeginInit();
            ((ISupportInitialize) DrawLine).BeginInit();
            ((ISupportInitialize) _drawArrow).BeginInit();
            ((ISupportInitialize) DrawText).BeginInit();
            ((ISupportInitialize) DrawRectangleFill).BeginInit();
            ((ISupportInitialize) _drawHighlight).BeginInit();
            ((ISupportInitialize) _drawMosaic).BeginInit();
            ((ISupportInitialize) _drawGaus).BeginInit();
            ((ISupportInitialize) DrawStep).BeginInit();
            ((ISupportInitialize) DrawUndo).BeginInit();
            ((ISupportInitialize) DrawRedo).BeginInit();
            ((ISupportInitialize) DrawChanel).BeginInit();
            ((ISupportInitialize) DrawSave).BeginInit();
            ((ISupportInitialize) DrawPaste).BeginInit();
            ((ISupportInitialize) _drawCopy).BeginInit();
            ToolsBtn.SuspendLayout();
            ((ISupportInitialize) ShowCross).BeginInit();
            ((ISupportInitialize) upDownButtonEx).BeginInit();
            ((ISupportInitialize) RectangleDot).BeginInit();
            ((ISupportInitialize) EllipseDot).BeginInit();
            ((ISupportInitialize) LineDot).BeginInit();
            ((ISupportInitialize) _arrowBoth).BeginInit();
            ((ISupportInitialize) TextDot).BeginInit();
            ((ISupportInitialize) Ellipse1).BeginInit();
            ((ISupportInitialize) Ellipse2).BeginInit();
            ((ISupportInitialize) Ellipse3).BeginInit();
            ((ISupportInitialize) Ellipse4).BeginInit();
            ((ISupportInitialize) RectangleCus).BeginInit();
            ((ISupportInitialize) Rectangle1).BeginInit();
            ((ISupportInitialize) Rectangle2).BeginInit();
            ((ISupportInitialize) Rectangle3).BeginInit();
            ((ISupportInitialize) Rectangle4).BeginInit();
            ((ISupportInitialize) Rectangle5).BeginInit();
            ((ISupportInitialize) Rectangle6).BeginInit();
            ((ISupportInitialize) Rectangle7).BeginInit();
            multiBtn.SuspendLayout();
            ((ISupportInitialize) P2_DrawChanel).BeginInit();
            ((ISupportInitialize) P2_DrawSave).BeginInit();
            SuspendLayout();
            // 
            // toolspanel
            // 
            toolspanel.AutoSize = true;
            toolspanel.BackColor = Color.White;
            toolspanel.Controls.Add(DrawRectangle);
            toolspanel.Controls.Add(DrawEllipse);
            toolspanel.Controls.Add(DrawPolygon);
            toolspanel.Controls.Add(DrawLine);
            toolspanel.Controls.Add(_drawArrow);
            toolspanel.Controls.Add(DrawText);
            toolspanel.Controls.Add(DrawRectangleFill);
            toolspanel.Controls.Add(_drawHighlight);
            toolspanel.Controls.Add(_drawMosaic);
            toolspanel.Controls.Add(_drawGaus);
            toolspanel.Controls.Add(DrawStep);
            toolspanel.Controls.Add(DrawUndo);
            toolspanel.Controls.Add(DrawRedo);
            toolspanel.Controls.Add(DrawChanel);
            toolspanel.Controls.Add(DrawSave);
            toolspanel.Controls.Add(DrawPaste);
            toolspanel.Controls.Add(_drawCopy);
            toolspanel.Cursor = Cursors.Default;
            toolspanel.Location = new Point(87, 22);
            toolspanel.MaximumSize = new Size(0, 35);
            toolspanel.MinimumSize = new Size(0, 35);
            toolspanel.Name = "toolspanel";
            toolspanel.Size = new Size(573, 35);
            toolspanel.TabIndex = 1;
            toolspanel.Visible = false;
            // 
            // DrawRectangle
            // 
            DrawRectangle.Cursor = Cursors.Default;
            DrawRectangle.Dock = DockStyle.Right;
            DrawRectangle.Location = new Point(33, 0);
            DrawRectangle.Name = "DrawRectangle";
            DrawRectangle.Size = new Size(30, 35);
            DrawRectangle.SizeMode = PictureBoxSizeMode.CenterImage;
            DrawRectangle.TabIndex = 0;
            DrawRectangle.TabStop = false;
            DrawRectangle.Tag = "矩形";
            DrawRectangle.MouseDown += Draw_MouseDown;
            // 
            // DrawEllipse
            // 
            DrawEllipse.Cursor = Cursors.Default;
            DrawEllipse.Dock = DockStyle.Right;
            DrawEllipse.Location = new Point(63, 0);
            DrawEllipse.Name = "DrawEllipse";
            DrawEllipse.Size = new Size(30, 35);
            DrawEllipse.SizeMode = PictureBoxSizeMode.CenterImage;
            DrawEllipse.TabIndex = 1;
            DrawEllipse.TabStop = false;
            DrawEllipse.Tag = "圆形";
            DrawEllipse.MouseDown += Draw_MouseDown;
            // 
            // DrawPolygon
            // 
            DrawPolygon.Cursor = Cursors.Default;
            DrawPolygon.Dock = DockStyle.Right;
            DrawPolygon.Location = new Point(93, 0);
            DrawPolygon.Name = "DrawPolygon";
            DrawPolygon.Size = new Size(30, 35);
            DrawPolygon.SizeMode = PictureBoxSizeMode.CenterImage;
            DrawPolygon.TabIndex = 18;
            DrawPolygon.TabStop = false;
            DrawPolygon.Tag = "画笔";
            DrawPolygon.MouseDown += Draw_MouseDown;
            // 
            // DrawLine
            // 
            DrawLine.Cursor = Cursors.Default;
            DrawLine.Dock = DockStyle.Right;
            DrawLine.Location = new Point(123, 0);
            DrawLine.Name = "DrawLine";
            DrawLine.Size = new Size(30, 35);
            DrawLine.SizeMode = PictureBoxSizeMode.CenterImage;
            DrawLine.TabIndex = 2;
            DrawLine.TabStop = false;
            DrawLine.Tag = "线条";
            DrawLine.MouseDown += Draw_MouseDown;
            // 
            // DrawArrow
            // 
            _drawArrow.Cursor = Cursors.Default;
            _drawArrow.Dock = DockStyle.Right;
            _drawArrow.Location = new Point(153, 0);
            _drawArrow.Name = "_drawArrow";
            _drawArrow.Size = new Size(30, 35);
            _drawArrow.SizeMode = PictureBoxSizeMode.CenterImage;
            _drawArrow.TabIndex = 15;
            _drawArrow.TabStop = false;
            _drawArrow.Tag = "箭头";
            _drawArrow.MouseDown += Draw_MouseDown;
            // 
            // DrawText
            // 
            DrawText.Cursor = Cursors.Default;
            DrawText.Dock = DockStyle.Right;
            DrawText.Location = new Point(183, 0);
            DrawText.Name = "DrawText";
            DrawText.Size = new Size(30, 35);
            DrawText.SizeMode = PictureBoxSizeMode.CenterImage;
            DrawText.TabIndex = 4;
            DrawText.TabStop = false;
            DrawText.Tag = "文字";
            DrawText.MouseDown += Draw_MouseDown;
            // 
            // DrawRectangleFill
            // 
            DrawRectangleFill.Cursor = Cursors.Default;
            DrawRectangleFill.Dock = DockStyle.Right;
            DrawRectangleFill.Location = new Point(213, 0);
            DrawRectangleFill.Name = "DrawRectangleFill";
            DrawRectangleFill.Size = new Size(30, 35);
            DrawRectangleFill.SizeMode = PictureBoxSizeMode.CenterImage;
            DrawRectangleFill.TabIndex = 20;
            DrawRectangleFill.TabStop = false;
            DrawRectangleFill.Tag = "矩形填充";
            DrawRectangleFill.MouseDown += Draw_MouseDown;
            // 
            // DrawHighlight
            // 
            _drawHighlight.Cursor = Cursors.Default;
            _drawHighlight.Dock = DockStyle.Right;
            _drawHighlight.Location = new Point(243, 0);
            _drawHighlight.Name = "_drawHighlight";
            _drawHighlight.Size = new Size(30, 35);
            _drawHighlight.SizeMode = PictureBoxSizeMode.CenterImage;
            _drawHighlight.TabIndex = 14;
            _drawHighlight.TabStop = false;
            _drawHighlight.Tag = "高亮";
            _drawHighlight.MouseDown += Draw_MouseDown;
            // 
            // DrawMosaic
            // 
            _drawMosaic.Cursor = Cursors.Default;
            _drawMosaic.Dock = DockStyle.Right;
            _drawMosaic.Location = new Point(273, 0);
            _drawMosaic.Name = "_drawMosaic";
            _drawMosaic.Size = new Size(30, 35);
            _drawMosaic.SizeMode = PictureBoxSizeMode.CenterImage;
            _drawMosaic.TabIndex = 5;
            _drawMosaic.TabStop = false;
            _drawMosaic.Tag = "马赛克";
            _drawMosaic.MouseDown += Draw_MouseDown;
            // 
            // DrawGaus
            // 
            _drawGaus.Cursor = Cursors.Default;
            _drawGaus.Dock = DockStyle.Right;
            _drawGaus.Location = new Point(303, 0);
            _drawGaus.Name = "_drawGaus";
            _drawGaus.Size = new Size(30, 35);
            _drawGaus.SizeMode = PictureBoxSizeMode.CenterImage;
            _drawGaus.TabIndex = 6;
            _drawGaus.TabStop = false;
            _drawGaus.Tag = "高斯模糊";
            _drawGaus.MouseDown += Draw_MouseDown;
            // 
            // DrawStep
            // 
            DrawStep.Cursor = Cursors.Default;
            DrawStep.Dock = DockStyle.Right;
            DrawStep.Location = new Point(333, 0);
            DrawStep.Name = "DrawStep";
            DrawStep.Size = new Size(30, 35);
            DrawStep.SizeMode = PictureBoxSizeMode.CenterImage;
            DrawStep.TabIndex = 17;
            DrawStep.TabStop = false;
            DrawStep.Tag = "序号";
            DrawStep.MouseDown += Draw_MouseDown;
            // 
            // DrawUndo
            // 
            DrawUndo.Cursor = Cursors.Default;
            DrawUndo.Dock = DockStyle.Right;
            DrawUndo.Location = new Point(363, 0);
            DrawUndo.Name = "DrawUndo";
            DrawUndo.Size = new Size(30, 35);
            DrawUndo.SizeMode = PictureBoxSizeMode.CenterImage;
            DrawUndo.TabIndex = 7;
            DrawUndo.TabStop = false;
            DrawUndo.Tag = "撤销";
            DrawUndo.MouseDown += Draw_MouseDown;
            // 
            // DrawRedo
            // 
            DrawRedo.Cursor = Cursors.Default;
            DrawRedo.Dock = DockStyle.Right;
            DrawRedo.Location = new Point(393, 0);
            DrawRedo.Name = "DrawRedo";
            DrawRedo.Size = new Size(30, 35);
            DrawRedo.SizeMode = PictureBoxSizeMode.CenterImage;
            DrawRedo.TabIndex = 8;
            DrawRedo.TabStop = false;
            DrawRedo.Tag = "重做";
            DrawRedo.MouseDown += Draw_MouseDown;
            // 
            // DrawChanel
            // 
            DrawChanel.Cursor = Cursors.Default;
            DrawChanel.Dock = DockStyle.Right;
            DrawChanel.Location = new Point(423, 0);
            DrawChanel.Name = "DrawChanel";
            DrawChanel.Size = new Size(30, 35);
            DrawChanel.SizeMode = PictureBoxSizeMode.CenterImage;
            DrawChanel.TabIndex = 10;
            DrawChanel.TabStop = false;
            DrawChanel.Tag = "关闭";
            DrawChanel.MouseDown += Draw_MouseDown;
            // 
            // DrawSave
            // 
            DrawSave.Cursor = Cursors.Default;
            DrawSave.Dock = DockStyle.Right;
            DrawSave.Location = new Point(453, 0);
            DrawSave.Name = "DrawSave";
            DrawSave.Size = new Size(30, 35);
            DrawSave.SizeMode = PictureBoxSizeMode.CenterImage;
            DrawSave.TabIndex = 9;
            DrawSave.TabStop = false;
            DrawSave.Tag = "保存";
            DrawSave.MouseDown += Draw_MouseDown;
            // 
            // DrawPaste
            // 
            DrawPaste.Cursor = Cursors.Default;
            DrawPaste.Dock = DockStyle.Right;
            DrawPaste.Location = new Point(483, 0);
            DrawPaste.Name = "DrawPaste";
            DrawPaste.Size = new Size(30, 35);
            DrawPaste.SizeMode = PictureBoxSizeMode.CenterImage;
            DrawPaste.TabIndex = 13;
            DrawPaste.TabStop = false;
            DrawPaste.Tag = "置顶";
            DrawPaste.MouseDown += Draw_MouseDown;
            // 
            // DrawCopy
            // 
            _drawCopy.Cursor = Cursors.Default;
            _drawCopy.Dock = DockStyle.Right;
            _drawCopy.Location = new Point(543, 0);
            _drawCopy.Name = "_drawCopy";
            _drawCopy.Size = new Size(30, 35);
            _drawCopy.SizeMode = PictureBoxSizeMode.CenterImage;
            _drawCopy.TabIndex = 11;
            _drawCopy.TabStop = false;
            _drawCopy.Tag = "复制";
            _drawCopy.MouseDown += Draw_MouseDown;
            // 
            // ToolsBtn
            // 
            ToolsBtn.BackColor = Color.White;
            ToolsBtn.Controls.Add(ShowCross);
            ToolsBtn.Controls.Add(upDownButtonEx);
            ToolsBtn.Controls.Add(fontsizeButton);
            ToolsBtn.Controls.Add(fontstyleButton);
            ToolsBtn.Controls.Add(RectangleDot);
            ToolsBtn.Controls.Add(EllipseDot);
            ToolsBtn.Controls.Add(LineDot);
            ToolsBtn.Controls.Add(_arrowBoth);
            ToolsBtn.Controls.Add(TextDot);
            ToolsBtn.Controls.Add(Ellipse1);
            ToolsBtn.Controls.Add(Ellipse2);
            ToolsBtn.Controls.Add(Ellipse3);
            ToolsBtn.Controls.Add(Ellipse4);
            ToolsBtn.Controls.Add(RectangleCus);
            ToolsBtn.Controls.Add(Rectangle1);
            ToolsBtn.Controls.Add(Rectangle2);
            ToolsBtn.Controls.Add(Rectangle3);
            ToolsBtn.Controls.Add(Rectangle4);
            ToolsBtn.Controls.Add(Rectangle5);
            ToolsBtn.Controls.Add(Rectangle6);
            ToolsBtn.Controls.Add(Rectangle7);
            ToolsBtn.Cursor = Cursors.Default;
            ToolsBtn.Location = new Point(26, 77);
            ToolsBtn.Margin = new Padding(0);
            ToolsBtn.Name = "ToolsBtn";
            ToolsBtn.Size = new Size(654, 35);
            ToolsBtn.TabIndex = 12;
            ToolsBtn.Visible = false;
            // 
            // ShowCross
            // 
            ShowCross.Cursor = Cursors.Default;
            ShowCross.Dock = DockStyle.Right;
            ShowCross.Location = new Point(-27, 0);
            ShowCross.Name = "ShowCross";
            ShowCross.Size = new Size(30, 35);
            ShowCross.SizeMode = PictureBoxSizeMode.CenterImage;
            ShowCross.TabIndex = 18;
            ShowCross.TabStop = false;
            ShowCross.Tag = "取色器";
            ShowCross.MouseDown += ShowCross_MouseDown;
            // 
            // upDownButtonEx
            // 
            upDownButtonEx.BackColor = Color.White;
            upDownButtonEx.Dock = DockStyle.Right;
            upDownButtonEx.Location = new Point(3, 0);
            upDownButtonEx.MaximumSize = new Size(50, 35);
            upDownButtonEx.MinimumSize = new Size(50, 35);
            upDownButtonEx.Name = "upDownButtonEx";
            upDownButtonEx.Size = new Size(50, 35);
            upDownButtonEx.TabIndex = 17;
            upDownButtonEx.TabStop = false;
            // 
            // fontsizeButton
            // 
            fontsizeButton.Dock = DockStyle.Right;
            fontsizeButton.FlatStyle = FlatStyle.Flat;
            fontsizeButton.Font = new Font("微软雅黑", 9F);
            fontsizeButton.IsBorder = false;
            fontsizeButton.ListItems = null;
            fontsizeButton.Location = new Point(53, 0);
            fontsizeButton.Name = "fontsizeButton";
            fontsizeButton.Size = new Size(50, 35);
            fontsizeButton.TabIndex = 16;
            fontsizeButton.Text = "14pt";
            fontsizeButton.UseVisualStyleBackColor = true;
            // 
            // fontstyleButton
            // 
            fontstyleButton.Dock = DockStyle.Right;
            fontstyleButton.FlatStyle = FlatStyle.Flat;
            fontstyleButton.Font = new Font("微软雅黑", 9F);
            fontstyleButton.IsBorder = false;
            fontstyleButton.ListItems = null;
            fontstyleButton.Location = new Point(103, 0);
            fontstyleButton.Name = "fontstyleButton";
            fontstyleButton.Size = new Size(70, 35);
            fontstyleButton.TabIndex = 15;
            fontstyleButton.Text = "微软雅黑";
            fontstyleButton.UseVisualStyleBackColor = true;
            // 
            // RectangleDot
            // 
            RectangleDot.Cursor = Cursors.Default;
            RectangleDot.Dock = DockStyle.Right;
            RectangleDot.Location = new Point(173, 0);
            RectangleDot.Name = "RectangleDot";
            RectangleDot.Size = new Size(30, 35);
            RectangleDot.SizeMode = PictureBoxSizeMode.CenterImage;
            RectangleDot.TabIndex = 10;
            RectangleDot.TabStop = false;
            RectangleDot.Tag = "虚线框";
            RectangleDot.MouseDown += Tools_MouseDown;
            // 
            // EllipseDot
            // 
            EllipseDot.Cursor = Cursors.Default;
            EllipseDot.Dock = DockStyle.Right;
            EllipseDot.Location = new Point(203, 0);
            EllipseDot.Name = "EllipseDot";
            EllipseDot.Size = new Size(30, 35);
            EllipseDot.SizeMode = PictureBoxSizeMode.CenterImage;
            EllipseDot.TabIndex = 12;
            EllipseDot.TabStop = false;
            EllipseDot.Tag = "虚线圆";
            EllipseDot.MouseDown += Tools_MouseDown;
            // 
            // LineDot
            // 
            LineDot.Cursor = Cursors.Default;
            LineDot.Dock = DockStyle.Right;
            LineDot.Location = new Point(233, 0);
            LineDot.Name = "LineDot";
            LineDot.Size = new Size(30, 35);
            LineDot.SizeMode = PictureBoxSizeMode.CenterImage;
            LineDot.TabIndex = 11;
            LineDot.TabStop = false;
            LineDot.Tag = "虚线段";
            LineDot.MouseDown += Tools_MouseDown;
            // 
            // ArrowBoth
            // 
            _arrowBoth.Cursor = Cursors.Default;
            _arrowBoth.Dock = DockStyle.Right;
            _arrowBoth.Location = new Point(263, 0);
            _arrowBoth.Name = "_arrowBoth";
            _arrowBoth.Size = new Size(30, 35);
            _arrowBoth.SizeMode = PictureBoxSizeMode.CenterImage;
            _arrowBoth.TabIndex = 14;
            _arrowBoth.TabStop = false;
            _arrowBoth.Tag = "双向箭头";
            _arrowBoth.MouseDown += Tools_MouseDown;
            // 
            // TextDot
            // 
            TextDot.Cursor = Cursors.Default;
            TextDot.Dock = DockStyle.Right;
            TextDot.Location = new Point(293, 0);
            TextDot.Name = "TextDot";
            TextDot.Size = new Size(30, 35);
            TextDot.SizeMode = PictureBoxSizeMode.CenterImage;
            TextDot.TabIndex = 13;
            TextDot.TabStop = false;
            TextDot.Tag = "描边文字";
            TextDot.MouseDown += Tools_MouseDown;
            // 
            // Ellipse1
            // 
            Ellipse1.Cursor = Cursors.Default;
            Ellipse1.Dock = DockStyle.Right;
            Ellipse1.Location = new Point(323, 0);
            Ellipse1.Name = "Ellipse1";
            Ellipse1.Size = new Size(30, 35);
            Ellipse1.SizeMode = PictureBoxSizeMode.CenterImage;
            Ellipse1.TabIndex = 1;
            Ellipse1.TabStop = false;
            Ellipse1.Tag = "小";
            Ellipse1.MouseDown += Tools_MouseDown;
            // 
            // Ellipse2
            // 
            Ellipse2.Cursor = Cursors.Default;
            Ellipse2.Dock = DockStyle.Right;
            Ellipse2.Location = new Point(353, 0);
            Ellipse2.Name = "Ellipse2";
            Ellipse2.Size = new Size(30, 35);
            Ellipse2.SizeMode = PictureBoxSizeMode.CenterImage;
            Ellipse2.TabIndex = 2;
            Ellipse2.TabStop = false;
            Ellipse2.Tag = "中";
            Ellipse2.MouseDown += Tools_MouseDown;
            // 
            // Ellipse3
            // 
            Ellipse3.Cursor = Cursors.Default;
            Ellipse3.Dock = DockStyle.Right;
            Ellipse3.Location = new Point(383, 0);
            Ellipse3.Name = "Ellipse3";
            Ellipse3.Size = new Size(30, 35);
            Ellipse3.SizeMode = PictureBoxSizeMode.CenterImage;
            Ellipse3.TabIndex = 3;
            Ellipse3.TabStop = false;
            Ellipse3.Tag = "大";
            Ellipse3.MouseDown += Tools_MouseDown;
            // 
            // Ellipse4
            // 
            Ellipse4.Cursor = Cursors.Default;
            Ellipse4.Dock = DockStyle.Right;
            Ellipse4.Location = new Point(413, 0);
            Ellipse4.Name = "Ellipse4";
            Ellipse4.Size = new Size(30, 35);
            Ellipse4.SizeMode = PictureBoxSizeMode.CenterImage;
            Ellipse4.TabIndex = 4;
            Ellipse4.TabStop = false;
            Ellipse4.Tag = "超大";
            Ellipse4.MouseDown += Tools_MouseDown;
            // 
            // RectangleCus
            // 
            RectangleCus.Cursor = Cursors.Default;
            RectangleCus.Dock = DockStyle.Right;
            RectangleCus.Location = new Point(443, 0);
            RectangleCus.Name = "RectangleCus";
            RectangleCus.Size = new Size(30, 35);
            RectangleCus.SizeMode = PictureBoxSizeMode.CenterImage;
            RectangleCus.TabIndex = 19;
            RectangleCus.TabStop = false;
            RectangleCus.Tag = "自定义色块";
            RectangleCus.MouseDown += Tools_MouseDown;
            // 
            // Rectangle1
            // 
            Rectangle1.Cursor = Cursors.Default;
            Rectangle1.Dock = DockStyle.Right;
            Rectangle1.Location = new Point(473, 0);
            Rectangle1.Name = "Rectangle1";
            Rectangle1.Size = new Size(30, 35);
            Rectangle1.SizeMode = PictureBoxSizeMode.CenterImage;
            Rectangle1.TabIndex = 4;
            Rectangle1.TabStop = false;
            Rectangle1.Tag = "红色";
            Rectangle1.MouseDown += Tools_MouseDown;
            // 
            // Rectangle2
            // 
            Rectangle2.Cursor = Cursors.Default;
            Rectangle2.Dock = DockStyle.Right;
            Rectangle2.Location = new Point(503, 0);
            Rectangle2.Name = "Rectangle2";
            Rectangle2.Size = new Size(30, 35);
            Rectangle2.SizeMode = PictureBoxSizeMode.CenterImage;
            Rectangle2.TabIndex = 5;
            Rectangle2.TabStop = false;
            Rectangle2.Tag = "黄色";
            Rectangle2.MouseDown += Tools_MouseDown;
            // 
            // Rectangle3
            // 
            Rectangle3.Cursor = Cursors.Default;
            Rectangle3.Dock = DockStyle.Right;
            Rectangle3.Location = new Point(533, 0);
            Rectangle3.Name = "Rectangle3";
            Rectangle3.Size = new Size(30, 35);
            Rectangle3.SizeMode = PictureBoxSizeMode.CenterImage;
            Rectangle3.TabIndex = 6;
            Rectangle3.TabStop = false;
            Rectangle3.Tag = "蓝色";
            Rectangle3.MouseDown += Tools_MouseDown;
            // 
            // Rectangle4
            // 
            Rectangle4.Cursor = Cursors.Default;
            Rectangle4.Dock = DockStyle.Right;
            Rectangle4.Location = new Point(563, 0);
            Rectangle4.Name = "Rectangle4";
            Rectangle4.Size = new Size(30, 35);
            Rectangle4.SizeMode = PictureBoxSizeMode.CenterImage;
            Rectangle4.TabIndex = 7;
            Rectangle4.TabStop = false;
            Rectangle4.Tag = "绿色";
            Rectangle4.MouseDown += Tools_MouseDown;
            // 
            // Rectangle5
            // 
            Rectangle5.Cursor = Cursors.Default;
            Rectangle5.Dock = DockStyle.Right;
            Rectangle5.Location = new Point(593, 0);
            Rectangle5.Name = "Rectangle5";
            Rectangle5.Size = new Size(30, 35);
            Rectangle5.SizeMode = PictureBoxSizeMode.CenterImage;
            Rectangle5.TabIndex = 8;
            Rectangle5.TabStop = false;
            Rectangle5.Tag = "紫色";
            Rectangle5.MouseDown += Tools_MouseDown;
            // 
            // Rectangle6
            // 
            Rectangle6.Cursor = Cursors.Default;
            Rectangle6.Dock = DockStyle.Right;
            Rectangle6.Location = new Point(623, 0);
            Rectangle6.Name = "Rectangle6";
            Rectangle6.Size = new Size(30, 35);
            Rectangle6.SizeMode = PictureBoxSizeMode.CenterImage;
            Rectangle6.TabIndex = 9;
            Rectangle6.TabStop = false;
            Rectangle6.Tag = "黑色";
            Rectangle6.MouseDown += Tools_MouseDown;
            // 
            // Rectangle7
            // 
            Rectangle7.Cursor = Cursors.Default;
            Rectangle7.Dock = DockStyle.Right;
            Rectangle7.Location = new Point(653, 0);
            Rectangle7.Name = "Rectangle7";
            Rectangle7.Size = new Size(31, 35);
            Rectangle7.SizeMode = PictureBoxSizeMode.CenterImage;
            Rectangle7.TabIndex = 0;
            Rectangle7.TabStop = false;
            Rectangle7.Tag = "银白";
            Rectangle7.MouseDown += Tools_MouseDown;
            // 
            // multiBtn
            // 
            multiBtn.BackColor = Color.White;
            multiBtn.Controls.Add(P2_DrawChanel);
            multiBtn.Controls.Add(P2_DrawSave);
            multiBtn.Cursor = Cursors.Default;
            multiBtn.Location = new Point(26, 132);
            multiBtn.Margin = new Padding(0);
            multiBtn.Name = "multiBtn";
            multiBtn.Size = new Size(91, 35);
            multiBtn.TabIndex = 13;
            multiBtn.Visible = false;
            // 
            // P2_DrawChanel
            // 
            P2_DrawChanel.Cursor = Cursors.Default;
            P2_DrawChanel.Dock = DockStyle.Left;
            P2_DrawChanel.Location = new Point(30, 0);
            P2_DrawChanel.Name = "P2_DrawChanel";
            P2_DrawChanel.Size = new Size(30, 35);
            P2_DrawChanel.SizeMode = PictureBoxSizeMode.CenterImage;
            P2_DrawChanel.TabIndex = 8;
            P2_DrawChanel.TabStop = false;
            P2_DrawChanel.Tag = "关闭";
            P2_DrawChanel.MouseDown += P2_DrawSave_MouseDown;
            // 
            // P2_DrawSave
            // 
            P2_DrawSave.Cursor = Cursors.Default;
            P2_DrawSave.Dock = DockStyle.Left;
            P2_DrawSave.Location = new Point(0, 0);
            P2_DrawSave.Name = "P2_DrawSave";
            P2_DrawSave.Size = new Size(30, 35);
            P2_DrawSave.SizeMode = PictureBoxSizeMode.CenterImage;
            P2_DrawSave.TabIndex = 9;
            P2_DrawSave.TabStop = false;
            P2_DrawSave.Tag = "多区域保存";
            P2_DrawSave.MouseDown += P2_DrawSave_MouseDown;
            // 
            // DrawArea
            // 
            BackgroundImageLayout = ImageLayout.None;
            ClientSize = new Size(753, 300);
            ControlBox = false;
            Controls.Add(multiBtn);
            Controls.Add(ToolsBtn);
            Controls.Add(toolspanel);
            DoubleBuffered = true;
            Font = new Font("微软雅黑", 10F);
            FormBorderStyle = FormBorderStyle.None;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "DrawArea";
            ShowIcon = false;
            ShowInTaskbar = false;
            Text = "截图";
            TopMost = true;
            FormClosing += DrawArea_FormClosing;
            Load += DrawArea_Load;
            Shown += DrawArea_Shown;
            MouseDoubleClick += DrawArea_MouseDoubleClick;
            MouseDown += DrawArea_MouseDown;
            MouseMove += DrawArea_MouseMove;
            MouseUp += DrawArea_MouseUp;
            toolspanel.ResumeLayout(false);
            ((ISupportInitialize) DrawRectangle).EndInit();
            ((ISupportInitialize) DrawEllipse).EndInit();
            ((ISupportInitialize) DrawPolygon).EndInit();
            ((ISupportInitialize) DrawLine).EndInit();
            ((ISupportInitialize) _drawArrow).EndInit();
            ((ISupportInitialize) DrawText).EndInit();
            ((ISupportInitialize) DrawRectangleFill).EndInit();
            ((ISupportInitialize) _drawHighlight).EndInit();
            ((ISupportInitialize) _drawMosaic).EndInit();
            ((ISupportInitialize) _drawGaus).EndInit();
            ((ISupportInitialize) DrawStep).EndInit();
            ((ISupportInitialize) DrawUndo).EndInit();
            ((ISupportInitialize) DrawRedo).EndInit();
            ((ISupportInitialize) DrawChanel).EndInit();
            ((ISupportInitialize) DrawSave).EndInit();
            ((ISupportInitialize) DrawPaste).EndInit();
            ((ISupportInitialize) _drawCopy).EndInit();
            ToolsBtn.ResumeLayout(false);
            ((ISupportInitialize) ShowCross).EndInit();
            ((ISupportInitialize) upDownButtonEx).EndInit();
            ((ISupportInitialize) RectangleDot).EndInit();
            ((ISupportInitialize) EllipseDot).EndInit();
            ((ISupportInitialize) LineDot).EndInit();
            ((ISupportInitialize) _arrowBoth).EndInit();
            ((ISupportInitialize) TextDot).EndInit();
            ((ISupportInitialize) Ellipse1).EndInit();
            ((ISupportInitialize) Ellipse2).EndInit();
            ((ISupportInitialize) Ellipse3).EndInit();
            ((ISupportInitialize) RectangleCus).EndInit();
            ((ISupportInitialize) Rectangle1).EndInit();
            ((ISupportInitialize) Rectangle2).EndInit();
            ((ISupportInitialize) Rectangle3).EndInit();
            ((ISupportInitialize) Rectangle4).EndInit();
            ((ISupportInitialize) Rectangle5).EndInit();
            ((ISupportInitialize) Rectangle6).EndInit();
            ((ISupportInitialize) Rectangle7).EndInit();
            multiBtn.ResumeLayout(false);
            ((ISupportInitialize) P2_DrawChanel).EndInit();
            ((ISupportInitialize) P2_DrawSave).EndInit();
            ((ISupportInitialize) Ellipse4).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }
    }
}